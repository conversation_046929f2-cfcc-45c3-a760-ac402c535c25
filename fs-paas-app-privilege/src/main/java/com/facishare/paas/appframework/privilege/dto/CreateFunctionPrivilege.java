package com.facishare.paas.appframework.privilege.dto;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.privilege.model.FunctionCodeBuilder;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by zhouwr on 2017/11/1
 */
public interface CreateFunctionPrivilege {
    @Data
    @Builder
    class Arg {
        private AuthContext authContext;
        private List<FunctionPojo> functionPojoList = Lists.newArrayList();
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private Object result;
        private boolean success;
    }

    @Data
    class FunctionPojo {
        private String id;
        private String appId;
        private String tenantId;
        private String funcName;
        private String funcCode;
        private String funcOrder;
        private String parentCode;
        private String levelCode;
        private Integer funcType;
        private boolean isEnabled;

        public static FunctionPojo buildFunctionPojo(String tenantId, String apiName, String actionCode) {
            FunctionPojo pojo = new FunctionPojo();
            pojo.setAppId(PrivilegeConstants.APP_ID);
            pojo.setTenantId(tenantId);
            if (apiName.endsWith("__c")) {
                pojo.setFuncType(PrivilegeConstants.USER_DEFINED_FUNCTION_CODE_TYPE);
            } else {
                pojo.setFuncType(PrivilegeConstants.SYSTEM_DEFINED_FUNCTION_CODE_TYPE);
            }
            pojo.setParentCode(PrivilegeConstants.PAAS_PARENT_CODE);
            pojo.setFuncName(ObjectAction.of(actionCode).getActionLabel());
            pojo.setFuncCode(FunctionCodeBuilder.build(apiName, actionCode));
            return pojo;
        }
    }
}
