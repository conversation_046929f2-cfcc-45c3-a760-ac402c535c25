package com.facishare.paas.appframework.metadata.changeorder.convert;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderConfig;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.*;

import static com.facishare.paas.appframework.metadata.FieldDescribeExt.isChangeFieldName;

/**
 * Created by zhaooju on 2023/3/27
 */
public final class ChangeOrderObjectFieldConvert {

    public static ChangeOrderObjectFieldConvert getInstance() {
        return Helper.INSTANCE;
    }

    private static final DescribeLogicService describeLogicService = SpringUtil.getContext().getBean("describeLogicService", DescribeLogicService.class);

    public IFieldDescribe convert(IObjectDescribe describe, IFieldDescribe fieldDescribe) {
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
        String fieldType = getFieldType(fieldDescribeExt);
        // 主从字段不拷贝
        if (IFieldType.MASTER_DETAIL.equals(fieldType)) {
            return null;
        }
        Set<String> fieldTypes = Sets.newHashSet(fieldType);
        fieldTypes.add(fieldDescribeExt.getType());
        // 引用字段
        if (fieldDescribeExt.isQuoteField()) {
            fieldDescribeExt = findAndFillQuoteField(describe, fieldDescribeExt);
        }

        if (IFieldType.SELECT_ONE.equals(fieldType)) {
            fieldDescribeExt = FieldDescribeExt.of(fieldDescribeExt.copy());
        }
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldDescribeExt.toMap().forEach((key, value) -> {
            if (ChangeOrderConfig.supportFieldAttr(key, fieldTypes.toArray(new String[]{}))) {
                fieldMap.put(key, value);
            }
        });
        fieldMap.put(IFieldDescribe.TYPE, fieldType);
        IFieldDescribe field = FieldDescribeExt.of(fieldMap).getFieldDescribe();
        // 业务类型字段不修改apiName
        field.setApiName(getChangeFieldName(field));
        // 查找管理字段需要补充 targetRelatedListName
        Optional.ofNullable(ObjectReferenceWrapper.of(field))
                .ifPresent(objectReferenceWrapper -> {
                    objectReferenceWrapper.setTargetRelatedListName(FieldDescribeExt.generateTargetRelatedListName());
                    objectReferenceWrapper.setTargetRelatedListLabel(objectReferenceWrapper.getTargetRelatedListLabel() + I18NExt.text(I18NKey.CHANGE_OBJECT));
                });
        if (FieldDescribeExt.of(field).isTrueOrFalseField()) {
            if (Objects.isNull(field.getDefaultValue())) {
                field.setDefaultValue(Boolean.TRUE);
            }
        }
        //变更单对象字段的数据也存在专表上(mt_data_change),所以字段需要设置is_extend=true分配槽位
        if (FieldDescribeExt.of(field).isCustomField()) {
            field.setIsExtend(Boolean.TRUE);
        }
        // 处理字段的依赖关系
        fieldDependenceHandler(describe, field);
        return field;
    }

    private String getChangeFieldName(IFieldDescribe field) {
        if (FieldDescribeExt.of(field).isRecordType()) {
            return field.getApiName();
        }
        return FieldDescribeExt.getChangeFieldName(field.getApiName());
    }

    private String getChangeFieldName(String fieldName) {
        if (StringUtils.equals(IFieldType.RECORD_TYPE, fieldName)) {
            return fieldName;
        }
        if (isChangeFieldName(fieldName)) {
            return fieldName;
        }
        return FieldDescribeExt.getChangeFieldName(fieldName);
    }

    private void fieldDependenceHandler(IObjectDescribe describe, IFieldDescribe fieldDescribe) {
        if (Objects.isNull(describe)) {
            return;
        }
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
        // 组件字段的依赖关系
        if (fieldDescribeExt.isGroupField()) {
            Map fields = (Map) fieldDescribeExt.get(Area.FIELDS);
            Map<String, String> newFields = Maps.newHashMap();
            if (CollectionUtils.notEmpty(fields)) {
                fields.forEach((key, value) -> {
                    if (SignIn.SIGN_IN_INFO_LIST_FIELD.equals(key)) {
                        return;
                    }
                    IFieldDescribe field = describe.getFieldDescribe(((String) value));
                    String fieldName = getChangeFieldName(field);
                    newFields.put(key.toString(), fieldName);
                });
            }
            fieldDescribeExt.set(Area.FIELDS, newFields);
        }

        // 级联父字段
        List<String> cascadeParentApiNames = fieldDescribeExt.getCascadeParentApiNames();
        if (CollectionUtils.notEmpty(cascadeParentApiNames)) {
            cascadeParentApiNames.forEach(apiName -> {
                String changeApiName = FieldDescribeExt.getChangeFieldName(apiName);
                fieldDescribeExt.setCascadeParentApiName(changeApiName);
            });
        }
        if (fieldDescribeExt.isSelectOne() && !fieldDescribeExt.isGeneralOptions()) {
            SelectOneExt selectOneExt = SelectOneExt.of(fieldDescribeExt.getFieldDescribe());
            List<ISelectOption> selectOptions = selectOneExt.getSelectOptions();
            if (CollectionUtils.notEmpty(selectOptions)) {
                for (ISelectOption selectOption : selectOptions) {
                    List<Map<String, List<String>>> childOptions = selectOption.getChildOptions();
                    if (CollectionUtils.empty(childOptions)) {
                        continue;
                    }
                    List<Map<String, List<String>>> childOptionList = replaceChildApiNames(childOptions);
                    selectOption.setChildOptions(childOptionList);
                }
            }
        }
    }

    @NotNull
    private List<Map<String, List<String>>> replaceChildApiNames(List<Map<String, List<String>>> childOptions) {
        List<Map<String, List<String>>> childOptionList = Lists.newArrayList();
        for (Map<String, List<String>> childOption : childOptions) {
            Set<String> childFieldApiNameList = childOption.keySet();
            Map<String, List<String>> childOptionMap = Maps.newHashMap();
            childFieldApiNameList.forEach(x -> childOptionMap.put(getChangeFieldName(x), childOption.get(x)));
            childOptionList.add(childOptionMap);
        }
        return childOptionList;
    }

    private FieldDescribeExt findAndFillQuoteField(IObjectDescribe describe, FieldDescribeExt fieldDescribeExt) {
        if (!fieldDescribeExt.isQuoteField() || Objects.isNull(describe)) {
            return fieldDescribeExt;
        }
        QuoteExt quoteExt = QuoteExt.of(fieldDescribeExt.getFieldDescribe());
        Tuple<String, String> quoteField = quoteExt.parseQuoteField();
        String quotedObjectApiName = QuoteExt.getQuotedObjectApiName(describe, quoteField.getKey());
        String apiName = fieldDescribeExt.getApiName();
        String defineType = fieldDescribeExt.getDefineType();
        // 查询引用对象的描述
        IObjectDescribe quotedObjectDescribe = describeLogicService.findObjectWithoutCopy(describe.getTenantId(), quotedObjectApiName);
        return ObjectDescribeExt.of(quotedObjectDescribe)
                .getFieldDescribeSilently(quoteField.getValue())
                .map(field -> {
                    FieldDescribeExt fieldExt = FieldDescribeExt.of(FieldDescribeExt.of(field).copyOnWrite());
                    fieldExt.setApiName(apiName);
                    fieldExt.setDefineType(defineType);
                    // 继续使用原单引用字段的属性
                    fieldDescribeExt.toMap().forEach((key, value) -> {
                        if (ChangeOrderConfig.supportQuoteFieldAttr(key)) {
                            fieldExt.set(key, value);
                        }
                    });
                    // 处理引用单选字段
                    handlerSelectOptions(fieldExt);
                    return fieldExt;
                })
                .orElse(fieldDescribeExt);
    }

    private void handlerSelectOptions(FieldDescribeExt fieldExt) {
        if (!fieldExt.isSelectOne()) {
            return;
        }
        if (fieldExt.isGeneralOptions()) {
            return;
        }
        SelectOneExt selectOneExt = SelectOneExt.of(fieldExt.getFieldDescribe());
        List<ISelectOption> selectOptions = selectOneExt.getSelectOptions();
        if (CollectionUtils.empty(selectOptions)) {
            return;
        }
        // 清空子选项依赖关系
        for (ISelectOption selectOption : selectOptions) {
            if (CollectionUtils.notEmpty(selectOption.getChildOptions())) {
                selectOption.setChildOptions(null);
            }
        }
    }

    private String getFieldType(FieldDescribeExt fieldDescribeExt) {
        String fieldType = fieldDescribeExt.getTypeOrReturnType();
        if (IFieldType.AUTO_NUMBER.equals(fieldType)) {
            return IFieldType.TEXT;
        }
        return fieldType;
    }

    private static class Helper {
        private static final ChangeOrderObjectFieldConvert INSTANCE = new ChangeOrderObjectFieldConvert();
    }

}
