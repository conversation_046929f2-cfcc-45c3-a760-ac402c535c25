package com.facishare.paas.appframework.metadata.button;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.LayoutButtonExt;
import com.facishare.paas.metadata.ui.layout.IButton;
import lombok.Builder;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Builder
public class RemoteLayoutDesignerButtonProvider implements LayoutDesignerButtonProvider {
    private LayoutDesignerButtonProviderProxy proxy;
    private String describeApiName;


    @Override
    public String getApiName() {
        return "REMOTE";
    }

    @Override
    public List<IButton> getButtons(List<IButton> buttons, User user) {
        GetLayoutDesignerButton.Arg arg = GetLayoutDesignerButton.Arg
                .builder()
                .buttonList(CollectionUtils.nullToEmpty(buttons).stream().map(a -> LayoutButtonExt.of(a).toMap()).collect(Collectors.toList()))
                .describeApiName(describeApiName)
                .build();
        Map<String, String> header = RestUtils.buildHeaders(user, describeApiName);
        GetLayoutDesignerButton.RestResult result = proxy.getLayoutDesignerButton(arg, header);
        return CollectionUtils.nullToEmpty(result.getData().getResult()).stream().map(a -> LayoutButtonExt.of(a).getButton()).collect(Collectors.toList());
    }
}
