package com.facishare.paas.appframework.metadata.layout.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class ButtonStyleInfoTest {

  private ButtonStyleInfo buttonStyleInfo;

  @BeforeEach
  void setUp() {
    buttonStyleInfo = new ButtonStyleInfo();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ButtonStyleInfo的无参构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试无参构造函数")
  void testDefaultConstructor() {
    ButtonStyleInfo info = new ButtonStyleInfo();
    assertNotNull(info, "无参构造函数创建的对象不应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ButtonStyleInfo的Map构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = Maps.newHashMap();
    map.put(IButtonStyleInfo.EXPOSED_NUM, 3);
    map.put(IButtonStyleInfo.DISPLAY_MODE, IButtonStyleInfo.ICON);
    
    ButtonStyleInfo info = new ButtonStyleInfo(map);
    assertNotNull(info, "Map构造函数创建的对象不应为空");
    assertEquals(3, info.getExposeNum(), "应正确设置暴露数量");
    assertEquals(IButtonStyleInfo.ICON, info.getDisplayMode(), "应正确设置显示模式");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ExposedNum属性的设置和获取
   */
  @Test
  @DisplayName("正常场景 - 测试ExposedNum属性")
  void testExposedNum() {
    // 测试设置和获取
    buttonStyleInfo.setExposedNum(5);
    assertEquals(5, buttonStyleInfo.getExposeNum(), "应正确设置和获取暴露数量");
    
    // 测试边界值
    buttonStyleInfo.setExposedNum(0);
    assertEquals(0, buttonStyleInfo.getExposeNum(), "应支持设置为0");
    
    buttonStyleInfo.setExposedNum(-1);
    assertEquals(-1, buttonStyleInfo.getExposeNum(), "应支持设置为负数");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试HighlightBtn属性的设置和获取
   */
  @Test
  @DisplayName("正常场景 - 测试HighlightBtn属性")
  void testHighlightBtn() {
    // 测试设置和获取
    List<String> buttonApiNames = Lists.newArrayList("btn1", "btn2", "btn3");
    buttonStyleInfo.setHighlightBtn(buttonApiNames);
    
    List<String> result = buttonStyleInfo.getHighlightBtn();
    assertNotNull(result, "高亮按钮列表不应为空");
    assertEquals(3, result.size(), "高亮按钮列表大小应为3");
    assertTrue(result.contains("btn1"), "应包含btn1");
    assertTrue(result.contains("btn2"), "应包含btn2");
    assertTrue(result.contains("btn3"), "应包含btn3");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试HighlightBtn属性的空列表处理
   */
  @Test
  @DisplayName("边界场景 - 测试HighlightBtn空列表")
  void testHighlightBtnEmptyList() {
    // 测试空列表
    List<String> emptyList = Lists.newArrayList();
    buttonStyleInfo.setHighlightBtn(emptyList);
    
    List<String> result = buttonStyleInfo.getHighlightBtn();
    assertNotNull(result, "结果不应为空");
    assertTrue(result.isEmpty(), "应返回空列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试HighlightBtn属性的null值处理
   */
  @Test
  @DisplayName("边界场景 - 测试HighlightBtn null值")
  void testHighlightBtnNull() {
    // 测试null值
    buttonStyleInfo.setHighlightBtn(null);
    
    List<String> result = buttonStyleInfo.getHighlightBtn();
    assertNotNull(result, "结果不应为空");
    assertTrue(result.isEmpty(), "null值应返回空列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试DisplayMode属性的设置和获取
   */
  @Test
  @DisplayName("正常场景 - 测试DisplayMode属性")
  void testDisplayMode() {
    // 测试ICON模式
    buttonStyleInfo.setDisplayMode(IButtonStyleInfo.ICON);
    assertEquals(IButtonStyleInfo.ICON, buttonStyleInfo.getDisplayMode(), "应正确设置ICON模式");
    
    // 测试WORD模式
    buttonStyleInfo.setDisplayMode(IButtonStyleInfo.WORD);
    assertEquals(IButtonStyleInfo.WORD, buttonStyleInfo.getDisplayMode(), "应正确设置WORD模式");
    
    // 测试自定义模式
    buttonStyleInfo.setDisplayMode("custom_mode");
    assertEquals("custom_mode", buttonStyleInfo.getDisplayMode(), "应正确设置自定义模式");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试DisplayMode属性的null值处理
   */
  @Test
  @DisplayName("边界场景 - 测试DisplayMode null值")
  void testDisplayModeNull() {
    buttonStyleInfo.setDisplayMode(null);
    assertNull(buttonStyleInfo.getDisplayMode(), "null值应正确处理");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试EditButtonLocation属性的设置和获取
   */
  @Test
  @DisplayName("正常场景 - 测试EditButtonLocation属性")
  void testEditButtonLocation() {
    // 测试TOP位置
    buttonStyleInfo.setEditButtonLocation(IButtonStyleInfo.TOP);
    assertEquals(IButtonStyleInfo.TOP, buttonStyleInfo.getEditButtonLocation(), "应正确设置TOP位置");
    
    // 测试RIGHT位置
    buttonStyleInfo.setEditButtonLocation(IButtonStyleInfo.RIGHT);
    assertEquals(IButtonStyleInfo.RIGHT, buttonStyleInfo.getEditButtonLocation(), "应正确设置RIGHT位置");
    
    // 测试自定义位置
    buttonStyleInfo.setEditButtonLocation("bottom");
    assertEquals("bottom", buttonStyleInfo.getEditButtonLocation(), "应正确设置自定义位置");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ButtonAlign属性的设置和获取
   */
  @Test
  @DisplayName("正常场景 - 测试ButtonAlign属性")
  void testButtonAlign() {
    // 测试左对齐
    buttonStyleInfo.setButtonAlign("left");
    assertEquals("left", buttonStyleInfo.getButtonAlign(), "应正确设置左对齐");
    
    // 测试右对齐
    buttonStyleInfo.setButtonAlign("right");
    assertEquals("right", buttonStyleInfo.getButtonAlign(), "应正确设置右对齐");
    
    // 测试居中对齐
    buttonStyleInfo.setButtonAlign("center");
    assertEquals("center", buttonStyleInfo.getButtonAlign(), "应正确设置居中对齐");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试所有属性的综合设置
   */
  @Test
  @DisplayName("正常场景 - 测试所有属性综合设置")
  void testAllProperties() {
    // 设置所有属性
    List<String> highlightBtns = Lists.newArrayList("save", "cancel");
    buttonStyleInfo.setExposedNum(2);
    buttonStyleInfo.setHighlightBtn(highlightBtns);
    buttonStyleInfo.setDisplayMode(IButtonStyleInfo.ICON);
    buttonStyleInfo.setEditButtonLocation(IButtonStyleInfo.TOP);
    buttonStyleInfo.setButtonAlign("center");
    
    // 验证所有属性
    assertEquals(2, buttonStyleInfo.getExposeNum(), "暴露数量应为2");
    assertEquals(2, buttonStyleInfo.getHighlightBtn().size(), "高亮按钮数量应为2");
    assertEquals(IButtonStyleInfo.ICON, buttonStyleInfo.getDisplayMode(), "显示模式应为ICON");
    assertEquals(IButtonStyleInfo.TOP, buttonStyleInfo.getEditButtonLocation(), "编辑按钮位置应为TOP");
    assertEquals("center", buttonStyleInfo.getButtonAlign(), "按钮对齐应为center");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口常量的正确性
   */
  @Test
  @DisplayName("正常场景 - 测试接口常量")
  void testInterfaceConstants() {
    // 验证字段名常量
    assertEquals("exposed_num", IButtonStyleInfo.EXPOSED_NUM);
    assertEquals("highlight_btn", IButtonStyleInfo.HIGHLIGHT_BTN);
    assertEquals("display_mode", IButtonStyleInfo.DISPLAY_MODE);
    assertEquals("edit_button_location", IButtonStyleInfo.EDIT_BUTTON_LOCATION);
    assertEquals("button_align", IButtonStyleInfo.BUTTON_ALIGN);
    
    // 验证值常量
    assertEquals("icon", IButtonStyleInfo.ICON);
    assertEquals("word", IButtonStyleInfo.WORD);
    assertEquals("top", IButtonStyleInfo.TOP);
    assertEquals("right", IButtonStyleInfo.RIGHT);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试JsonCompatible接口实现
   */
  @Test
  @DisplayName("正常场景 - 测试JsonCompatible接口")
  void testJsonCompatible() {
    // 设置一些属性
    buttonStyleInfo.setExposedNum(3);
    buttonStyleInfo.setDisplayMode(IButtonStyleInfo.ICON);
    
    // 验证对象实现了JsonCompatible接口
    assertTrue(buttonStyleInfo instanceof com.facishare.paas.metadata.api.JsonCompatible, 
               "应实现JsonCompatible接口");
  }
}
