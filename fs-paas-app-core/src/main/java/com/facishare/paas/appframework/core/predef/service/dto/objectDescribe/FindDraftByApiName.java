package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 18/1/17.
 */
public interface FindDraftByApiName {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("draft_apiname")
        @SerializedName("draft_apiname")
        String draftApiName;

        @JSONField(name = "M2")
        @JsonProperty("include_layout")
        @SerializedName("include_layout")
        private boolean includeLayout;

        @JSONField(name = "M3")
        @JsonProperty("layout_type")
        @SerializedName("layout_type")
        private String layoutType;

        @JsonProperty("fill_quote_field_option")
        @SerializedName("fill_quote_field_option")
        private boolean fillQuoteFieldOption;

        private String appId;
    }


    @Data
    @Builder
    class Result {
        @JSONField(name = "M4")
        private ObjectDescribeDocument objectDescribeDraft;

        @JSONField(name = "M5")
        private LayoutDocument layout;

        private ObjectDescribeDocument objectDescribeExt;
    }

}
