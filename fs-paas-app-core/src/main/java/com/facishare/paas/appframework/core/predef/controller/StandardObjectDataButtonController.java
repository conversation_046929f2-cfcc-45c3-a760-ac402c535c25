package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.appframework.metadata.ButtonUsePageType;
import com.facishare.paas.appframework.metadata.expansion.DescribeExpansionRender;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

public class StandardObjectDataButtonController extends PreDefineController<StandardObjectDataButtonController.Arg, StandardObjectDataButtonController.Result> {


    private List<IObjectData> objectDataList;
    private List<String> dataList;
    private IObjectDescribe describe;
    private List<IUdefButton> buttonList;

    @Override

    protected void before(Arg arg) {
        init(arg);
        super.before(arg);
    }

    private void init(Arg arg) {
        dataList = arg.getDataList();
        describe = serviceFacade.findObject(controllerContext.getTenantId(), controllerContext.getObjectApiName());
        objectDataList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), dataList, describe.getApiName());
        buttonList = findButton();
    }

    private List<IUdefButton> findButton() {
        return serviceFacade.findCustomButtonByUsePage(controllerContext.getObjectApiName(), ButtonUsePageType.DataList.getId(),
                controllerContext.getUser());

    }

    @Override
    protected Result doService(Arg arg) {
        Map<String, List<ButtonDocument>> result = Maps.newHashMap();
        getButtonForListPage(result);
        return Result.builder().result(result).build();
    }


    private void getButtonForListPage(Map<String, List<ButtonDocument>> result) {
        Map<String, List<IUdefButton>> listButtonMap = serviceFacade.filterListButton(buttonList, describe, objectDataList,
                controllerContext.getUser(), ButtonUsePageType.DataList.getId());
        Set<String> readonlyFields = serviceFacade.getReadonlyFields(controllerContext.getUser(), describe.getApiName());

        DescribeExtra describeExtra = serviceFacade.findDescribeExtraByRenderType(controllerContext.getUser(), describe,
                Collections.emptyList(), DescribeExpansionRender.RenderType.Detail, true);

        listButtonMap.forEach((k, buttonList) -> {
            buttonList.forEach(x ->
                    ButtonExt.of(x).handleButtonParam(controllerContext.getUser(), null, describe, Optional.ofNullable(describeExtra)
                            .map(it -> it.getDescribeExtra(describe.getApiName()))
                            .orElseGet(Maps::newHashMap), readonlyFields)
            );
            result.put(k, ButtonDocument.ofList(buttonList));
        });
    }


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class Result {
        Map<String, List<ButtonDocument>> result;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Arg {
        List<String> dataList;
    }
}
