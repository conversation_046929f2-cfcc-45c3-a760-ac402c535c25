package com.facishare.paas.appframework.privilege.util;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * GenerateByAI
 * 测试内容描述：测试AppIdUtil工具类的方法
 */
class AppIdUtilTest {

    @AfterEach
    void cleanup() {
        // 清理测试后的RequestContextManager
        RequestContextManager.removeContext();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getAppId方法，使用User参数的版本
     */
    @ParameterizedTest
    @CsvSource({
            "custom-app, custom-app",
            "'', CRM",
            ", CRM"
    })
    @DisplayName("参数化测试 - getAppId方法使用User参数")
    void testGetAppId(String contextAppId, String expected) {
        // 准备测试数据
        User user = new User("123", "456");
        
        // 模拟RequestContextManager的上下文
        if (contextAppId != null && !contextAppId.isEmpty()) {
            RequestContext requestContext = RequestContext.builder()
                    .tenantId("123")
                    .user(user)
                    .appId(contextAppId)
                    .requestSource(RequestContext.RequestSource.CEP)
                    .build();
            RequestContextManager.setContext(requestContext);
        }
        
        // 执行被测试方法
        String result = AppIdUtil.getAppId(user);
        
        // 验证结果
        assertEquals(expected, result);
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getAppId方法，使用User和appId两个参数的版本
     */
    @ParameterizedTest
    @CsvSource({
            "custom-app, custom-app",
            "'', CRM",
            ", CRM",
            "' ', CRM"
    })
    @DisplayName("参数化测试 - getAppId方法使用User和appId参数")
    void testGetAppIdWithAppIdParam(String inputAppId, String expected) {
        // 准备测试数据
        User user = new User("123", "456");
        
        // 执行被测试方法
        String result = AppIdUtil.getAppId(user, inputAppId);
        
        // 验证结果
        assertEquals(expected, result);
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getAppIdWithNoDefault方法
     */
    @ParameterizedTest
    @CsvSource(value = {
            "custom-app, custom-app",
            "'', ''",
            "null, null"
    }, nullValues = {"null"})
    @DisplayName("参数化测试 - getAppIdWithNoDefault方法")
    void testGetAppIdWithNoDefault(String contextAppId, String expected) {
        // 准备测试数据
        User user = new User("123", "456");
        
        // 模拟RequestContextManager的上下文
        if (contextAppId != null && !contextAppId.isEmpty()) {
            RequestContext requestContext = RequestContext.builder()
                    .tenantId("123")
                    .user(user)
                    .appId(contextAppId)
                    .requestSource(RequestContext.RequestSource.CEP)
                    .build();
            RequestContextManager.setContext(requestContext);
        } else if ("".equals(contextAppId)) {
            // 设置空字符串的上下文
            RequestContext requestContext = RequestContext.builder()
                    .tenantId("123")
                    .user(user)
                    .appId("")
                    .requestSource(RequestContext.RequestSource.CEP)
                    .build();
            RequestContextManager.setContext(requestContext);
        } else {
            // 清理上下文，确保返回null
            RequestContextManager.removeContext();
        }
        
        // 执行被测试方法
        String result = AppIdUtil.getAppIdWithNoDefault();
        
        // 验证结果
        assertEquals(expected, result);
    }
} 