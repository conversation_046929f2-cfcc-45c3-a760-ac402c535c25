package com.facishare.paas.appframework.privilege;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.privilege.dto.AddRoleModel;
import com.facishare.paas.appframework.privilege.dto.AuthContext;
import com.facishare.paas.appframework.privilege.dto.RestResult;
import com.facishare.paas.appframework.privilege.dto.RoleInfoListByTypesModel;
import com.facishare.paas.appframework.privilege.model.role.Role;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.facishare.paas.appframework.privilege.util.Headers.PAAS_PRIVILEGE_HEADDER;

/**
 * Created by luxin on 2018/5/28.
 */
@Service("privilegeRole")
@Slf4j
public class RoleServiceImpl implements RoleService {
    @Autowired
    private RoleProxy roleProxy;

    @Override
    public void addPredefinedRole(String tenantId, Role role) {
        AuthContext context = buildAuthContext(tenantId, "CRM");
        AddRoleModel.Arg arg = AddRoleModel.Arg.builder()
                .authContext(context)
                .roleCode(role.getRoleCode())
                .roleName(role.getDisplayName())
                .description(role.getDescription())
                .roleType(role.getType().getType())
                .build();

        AddRoleModel.Result result = roleProxy.addRole(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(tenantId));

        if (!result.isSuccess()) {
            log.warn("addFunctionPrivilege error,arg:{},result:{}", arg, result);
            throw new PermissionError(I18N.text(I18NKey.CREATE_ROLE_FAIL_REASON, result.getErrMessage()), result.getErrCode());
        }
    }

    @Override
    public List<RestResult.RoleInfoPojo> getOuterRoleInfoList(String tenantId, String appId) {
        AuthContext context = buildAuthContext(tenantId, appId);
        RoleInfoListByTypesModel.Arg arg = RoleInfoListByTypesModel.Arg.builder()
                .authContext(context)
                .roleTypes(Lists.newArrayList(Role.RoleType.DEFAULT_OUTER_ROLE.getType(), Role.RoleType.USER_DEFINED_OUTER_ROLE.getType()))
                .build();

        RoleInfoListByTypesModel.Result result = roleProxy.roleInfoListByRoleTypes(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(tenantId));

        if (!result.isSuccess()) {
            log.warn("roleInfoListByRoleTypes error,arg:{},result:{}", arg, result);
            throw new PermissionError(I18N.text(I18NKey.GET_OUT_ROLE_FAIL_REASON, result.getErrMessage()));
        }
        return result.getResult().getRoles();
    }

    private AuthContext buildAuthContext(String tenantId, String appId) {
        return AuthContext.builder()
                .appId(appId)
                .tenantId(tenantId)
                .userId("-10000")
                .build();
    }

    @Override
    public List<RestResult.RoleInfoPojo> getCrmRoleInfoList(String tenantId) {
        AuthContext context = buildAuthContext(tenantId, "CRM");
        RoleInfoListByTypesModel.Arg arg = RoleInfoListByTypesModel.Arg.builder()
                .authContext(context)
                .roleTypes(Lists.newArrayList(Role.RoleType.DEFAULT_ROLE.getType(), Role.RoleType.USER_DEFINED_ROLE.getType()))
                .build();

        RoleInfoListByTypesModel.Result result = roleProxy.roleInfoListByRoleTypes(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(tenantId));

        if (!result.isSuccess()) {
            log.warn("roleInfoListByRoleTypes error,arg:{},result:{}", arg, result);
            throw new PermissionError(I18N.text(I18NKey.GET_ROLE_INFO_LIST_FAIL, result.getErrMessage()));
        }
        return result.getResult().getRoles();
    }

    @Override
    public Boolean checkRoleIsExist(String tenantId, String appId, String roleCode) {
        AuthContext context = buildAuthContext(tenantId, appId);
        RoleInfoListByTypesModel.Arg arg = RoleInfoListByTypesModel.Arg.builder()
                .authContext(context)
                .roleTypes(Lists.newArrayList(
                        Role.RoleType.DEFAULT_ROLE.getType(),
                        Role.RoleType.USER_DEFINED_ROLE.getType(),
                        Role.RoleType.DEFAULT_OUTER_ROLE.getType(),
                        Role.RoleType.USER_DEFINED_OUTER_ROLE.getType())
                ).build();
        RoleInfoListByTypesModel.Result result = roleProxy.roleInfoListByRoleTypes(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(tenantId));
        if (!result.isSuccess()) {
            log.warn("roleInfoListByRoleTypes error,arg:{},result:{}", arg, result);
            throw new PermissionError(I18N.text(I18NKey.GET_ROLE_INFO_LIST_FAIL, result.getErrMessage()));
        }

        for (RestResult.RoleInfoPojo role : result.getResult().getRoles()) {
            if (roleCode.equals(role.getRoleCode())) {
                return true;
            }
        }
        return false;
    }


}
