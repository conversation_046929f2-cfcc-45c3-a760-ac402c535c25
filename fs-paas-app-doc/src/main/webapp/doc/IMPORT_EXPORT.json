{"swagger": "2.0", "info": {"title": "AppFrameWork REST API", "description": "PaaS 应用框架导入导出相关REST API", "version": "1.0"}, "host": "************:8234", "schemes": ["http"], "basePath": "/v1/inner/rest/bulkImport", "produces": ["application/json"], "commonParams": {"userInfo": {"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, "eid": {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}}, "paths": {"/findPrimaryFieldList": {"post": {"summary": "create object data", "description": "创建自定义对象数据", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/PrimaryFieldInfoResult"}}}}}, "/findImportInfosByNames": {"post": {"summary": "create object data", "description": "创建自定义对象数据", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CellStructList"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ImportRowInfoResult"}}}}}}, "definitions": {"PrimaryFieldInfoResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/CellStruct"}}}}, "CellStructList": {"type": "array", "items": {"$ref": "#/definitions/CellStruct"}}, "CellStruct": {"type": "object", "properties": {"rowNo": {"type": "integer"}, "describeApiName": {"type": "string"}, "primaryFieldValue": {"type": "string"}}}, "ImportRowInfoResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ImportRowInfo"}}}}, "ImportRowInfo": {"type": "object", "properties": {"rowNo": {"type": "integer"}, "cellInfoList": {"type": "array", "items": {"$ref": "#/definitions/CellStruct"}}}}}}