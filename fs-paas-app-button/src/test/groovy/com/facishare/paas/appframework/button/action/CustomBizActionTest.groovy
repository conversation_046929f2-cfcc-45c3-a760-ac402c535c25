package com.facishare.paas.appframework.button.action

import com.facishare.paas.appframework.button.dto.ButtonExecutor
import com.facishare.paas.appframework.button.dto.CustomBizButtonExecutor
import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.RestUtils
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.IUdefAction
import com.facishare.paas.metadata.api.IUdefButton
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.github.autoconf.ConfigFactory
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

/**
 * CustomBizAction单元测试
 * 用于测试CustomBizAction类的所有功能
 */
@Unroll
class CustomBizActionTest extends Specification {

    CustomBizAction customBizAction
    CustomBizActionProxy customBizActionProxy = Mock(CustomBizActionProxy)

    def setup() {
        customBizAction = new CustomBizAction(customBizActionProxy: customBizActionProxy)
    }

    def setupSpec() {
        // 初始化urlMap
        def urlMap = [
            "test_biz_key": "http://test.example.com/api",
            "valid_key": "http://valid.example.com/api"
        ]
        Whitebox.setInternalState(CustomBizAction, "urlMap", urlMap)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getType方法返回正确的ActionExecutorType
     */
    def "getTypeTest"() {
        when:
        def result = customBizAction.getType()

        then:
        result == ActionExecutorType.CUSTOM_BIZ
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当context为null时，invoke方法返回空结果
     */
    def "invokeErrorContextNull"() {
        given:
        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
            .describeApiName("test_api")
            .objectDataId("123")
            .buttonApiName("test_button")
            .build()

        when:
        def result = customBizAction.invoke(arg, null)

        then:
        result == ButtonExecutor.Result.empty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当action的bizKey为空时，invoke方法返回空结果
     */
    def "invokeErrorBizKeyBlank"() {
        given:
        def user = User.systemUser('74255')
        def describe = Mock(IObjectDescribe)
        def button = Mock(IUdefButton)
        def action = Mock(IUdefAction)
        action.getBizKey() >> bizKey

        def context = ActionExecutorContext.builder()
            .user(user)
            .describe(describe)
            .button(button)
            .action(action)
            .build()

        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
            .describeApiName("test_api")
            .objectDataId("123")
            .buttonApiName("test_button")
            .build()

        when:
        def result = customBizAction.invoke(arg, context)

        then:
        result == ButtonExecutor.Result.empty()

        where:
        bizKey << [null, "", "   "]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当urlMap中不存在对应bizKey时，invoke方法返回空结果
     */
    def "invokeErrorUrlNotFound"() {
        given:
        def user = User.systemUser('74255')
        def describe = Mock(IObjectDescribe)
        def button = Mock(IUdefButton)
        def action = Mock(IUdefAction)
        action.getBizKey() >> "nonexistent_key"

        def context = ActionExecutorContext.builder()
            .user(user)
            .describe(describe)
            .button(button)
            .action(action)
            .build()

        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
            .describeApiName("test_api")
            .objectDataId("123")
            .buttonApiName("test_button")
            .build()

        when:
        def result = customBizAction.invoke(arg, context)

        then:
        result == ButtonExecutor.Result.empty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当buttonApiName为空时，自动设置为context.button的apiName
     */
    def "invokeTestButtonApiNameEmpty"() {
        given:
        def user = User.systemUser('74255')
        def describe = Mock(IObjectDescribe)
        def button = Mock(IUdefButton)
        button.getApiName() >> "context_button_api"
        def action = Mock(IUdefAction)
        action.getBizKey() >> "test_biz_key"
        action.getStage() >> "current"

        def context = ActionExecutorContext.builder()
            .user(user)
            .describe(describe)
            .button(button)
            .action(action)
            .build()

        def objectData = new ObjectData(['_id': '123', 'name': 'test'])
        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
            .describeApiName("test_api")
            .objectDataId("123")
            .objectData(objectData)
            .buttonApiName("")
            .build()

        def mockResponse = new CustomBizActionProxy.ButtonActionResponse()
        mockResponse.errCode = 0
        def mockResult = CustomBizButtonExecutor.Result.builder()
            .hasReturnValue(true)
            .returnValue("success")
            .returnType("string")
            .block(false)
            .build()
        mockResponse.result = mockResult

        when:
        def result = customBizAction.invoke(arg, context)

        then:
        1 * customBizActionProxy.startCustomBizButton(_, "http://test.example.com/api", _) >> { arguments ->
            def buttonArg = arguments[0] as CustomBizButtonExecutor.Arg
            assert buttonArg.buttonApiName == "context_button_api"
            return mockResponse
        }
        result.hasReturnValue == true
        result.returnValue == "success"
        result.returnType == "string"
        result.block == false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试正常执行流程，验证所有参数正确传递
     */
    def "invokeTestNormalFlow"() {
        given:
        def user = User.systemUser('74255')
        def describe = Mock(IObjectDescribe)
        def button = Mock(IUdefButton)
        button.getApiName() >> "test_button"
        def action = Mock(IUdefAction)
        action.getBizKey() >> "valid_key"
        action.getStage() >> "current"

        def context = ActionExecutorContext.builder()
            .user(user)
            .describe(describe)
            .button(button)
            .action(action)
            .build()

        def objectData = new ObjectData(['_id': '123', 'name': 'test'])
        def details = ['detail_obj': [new ObjectData(['detail_id': '456'])]]
        def args = ['param1': 'value1']
        def actionParams = ['action_param': 'action_value']

        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
            .describeApiName("test_api")
            .objectDataId("123")
            .dataIds(['123', '456'])
            .buttonApiName("custom_button")
            .args(args)
            .objectData(objectData)
            .details(details)
            .actionParams(actionParams)
            .build()

        def mockResponse = new CustomBizActionProxy.ButtonActionResponse()
        mockResponse.errCode = 0
        def mockResult = CustomBizButtonExecutor.Result.builder()
            .objectData(['_id': '123', 'updated_field': 'new_value'])
            .details(['updated_detail': [['detail_id': '789']]])
            .targetDescribeApiName("target_api")
            .hasReturnValue(true)
            .returnValue("operation_success")
            .returnType("string")
            .block(false)
            .build()
        mockResponse.result = mockResult

        when:
        def result = customBizAction.invoke(arg, context)

        then:
        1 * customBizActionProxy.startCustomBizButton(_, "http://valid.example.com/api", _) >> { arguments ->
            def buttonArg = arguments[0] as CustomBizButtonExecutor.Arg
            assert buttonArg.describeApiName == "test_api"
            assert buttonArg.objectDataId == "123"
            assert buttonArg.dataIds == ['123', '456']
            assert buttonArg.buttonApiName == "custom_button"
            assert buttonArg.args == args
            assert buttonArg.bizKey == "valid_key"
            assert buttonArg.actionStage == "current"
            assert buttonArg.actionParams == actionParams
            return mockResponse
        }
        
        result.targetDescribeApiName == "target_api"
        result.hasReturnValue == true
        result.returnValue == "operation_success"
        result.returnType == "string"
        result.block == false
        result.objectData != null
        result.details != null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当远程调用返回错误码时，抛出MetaDataBusinessException异常
     */
    def "invokeErrorRemoteCallFailed"() {
        given:
        def user = User.systemUser('74255')
        def describe = Mock(IObjectDescribe)
        def button = Mock(IUdefButton)
        def action = Mock(IUdefAction)
        action.getBizKey() >> "test_biz_key"

        def context = ActionExecutorContext.builder()
            .user(user)
            .describe(describe)
            .button(button)
            .action(action)
            .build()

        def objectData = new ObjectData(['_id': '123'])
        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
            .describeApiName("test_api")
            .objectData(objectData)
            .buttonApiName("test_button")
            .build()

        def mockResponse = new CustomBizActionProxy.ButtonActionResponse()
        mockResponse.errCode = errorCode
        mockResponse.errMessage = errorMessage

        when:
        customBizAction.invoke(arg, context)

        then:
        1 * customBizActionProxy.startCustomBizButton(_, "http://test.example.com/api", _) >> mockResponse
        
        def exception = thrown(MetaDataBusinessException)
        exception.message == errorMessage

        where:
        errorCode | errorMessage
        1         | "业务处理失败"
        500       | "服务器内部错误"
        -1        | "未知错误"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试RestUtils.buildHeaders方法被正确调用
     */
    def "invokeTestHeadersBuilding"() {
        given:
        def user = User.systemUser('74255')
        def describe = Mock(IObjectDescribe)
        def button = Mock(IUdefButton)
        def action = Mock(IUdefAction)
        action.getBizKey() >> "test_biz_key"

        def context = ActionExecutorContext.builder()
            .user(user)
            .describe(describe)
            .button(button)
            .action(action)
            .build()

        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
            .describeApiName("test_api")
            .objectData(new ObjectData(['_id': '123']))
            .buttonApiName("test_button")
            .build()

        def mockResponse = new CustomBizActionProxy.ButtonActionResponse()
        mockResponse.errCode = 0
        mockResponse.result = CustomBizButtonExecutor.Result.builder().build()

        when:
        customBizAction.invoke(arg, context)

        then:
        1 * customBizActionProxy.startCustomBizButton(_, "http://test.example.com/api", _) >> { arguments ->
            def headers = arguments[2] as Map<String, String>
            // 验证headers不为空（具体内容由RestUtils.buildHeaders决定）
            assert headers != null
            return mockResponse
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试CustomBizButtonExecutor.Arg的构建逻辑
     */
    def "invokeTestCustomBizButtonExecutorArgBuilding"() {
        given:
        def user = User.systemUser('74255')
        def describe = Mock(IObjectDescribe)
        def button = Mock(IUdefButton)
        def action = Mock(IUdefAction)
        action.getBizKey() >> "test_biz_key"
        action.getStage() >> "post"

        def context = ActionExecutorContext.builder()
            .user(user)
            .describe(describe)
            .button(button)
            .action(action)
            .build()

        def objectData = new ObjectData(['_id': '123', 'name': 'test_object'])
        def details = ['detail1': [new ObjectData(['detail_id': '456'])]]
        def args = ['arg1': 'value1', 'arg2': 'value2']
        def actionParams = ['action1': 'action_value1']

        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
            .describeApiName("test_describe")
            .objectDataId("123")
            .dataIds(['123', '456', '789'])
            .buttonApiName("test_button_api")
            .args(args)
            .objectData(objectData)
            .details(details)
            .actionParams(actionParams)
            .build()

        def mockResponse = new CustomBizActionProxy.ButtonActionResponse()
        mockResponse.errCode = 0
        mockResponse.result = CustomBizButtonExecutor.Result.builder().build()

        when:
        customBizAction.invoke(arg, context)

        then:
        1 * customBizActionProxy.startCustomBizButton(_, _, _) >> { arguments ->
            def buttonArg = arguments[0] as CustomBizButtonExecutor.Arg
            
            // 验证CustomBizButtonExecutor.Arg的所有字段
            assert buttonArg.describeApiName == "test_describe"
            assert buttonArg.objectDataId == "123"
            assert buttonArg.dataIds == ['123', '456', '789']
            assert buttonArg.buttonApiName == "test_button_api"
            assert buttonArg.args == args
            assert buttonArg.objectData != null
            assert buttonArg.details != null
            assert buttonArg.bizKey == "test_biz_key"
            assert buttonArg.actionStage == "post"
            assert buttonArg.actionParams == actionParams
            
            return mockResponse
        }
    }
} 