package com.facishare.paas.appframework.license.quota

import com.facishare.paas.appframework.license.exception.LicenseException
import com.facishare.paas.appframework.license.quota.checker.NoExistChecker
import com.facishare.paas.appframework.license.quota.checker.QuotaChecker
import spock.lang.Specification

class NoExistCheckerTest extends Specification {

    def "test NoExistChecker check should throw LicenseException"() {
        given:
        def noExistChecker = NoExistChecker.builder().build()
        def quotaInfo = QuotaChecker.QuotaInfo.builder()
                .actualCount(10)
                .build()

        when:
        noExistChecker.check(quotaInfo)

        then:
        thrown(LicenseException)
    }

    def "test NoExistChecker getMaxCount should return 0"() {
        given:
        def noExistChecker = NoExistChecker.builder().build()
        def quotaInfo = QuotaChecker.QuotaInfo.builder()
                .actualCount(10)
                .build()

        when:
        def result = noExistChecker.getMaxCount(quotaInfo)

        then:
        result == 0
    }
} 