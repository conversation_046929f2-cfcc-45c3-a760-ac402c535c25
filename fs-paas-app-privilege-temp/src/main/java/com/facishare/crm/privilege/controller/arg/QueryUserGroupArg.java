package com.facishare.crm.privilege.controller.arg;

public class QueryUserGroupArg {
  private String id;
  private Integer status;
  private String searchKey;
  private Integer pageNumber;
  private Integer pageSize;
  private String orderKey;
  private boolean isAsc = true;

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public String getSearchKey() {
    return searchKey;
  }

  public void setSearchKey(String searchKey) {
    this.searchKey = searchKey;
  }

  public Integer getPageNumber() {
    return pageNumber;
  }

  public void setPageNumber(Integer pageNumber) {
    this.pageNumber = pageNumber;
  }

  public Integer getPageSize() {
    return pageSize;
  }

  public void setPageSize(Integer pageSize) {
    this.pageSize = pageSize;
  }

  public String getOrderKey() {
    return orderKey;
  }

  public void setOrderKey(String orderKey) {
    this.orderKey = orderKey;
  }

  public boolean getAsc() {
    return isAsc;
  }

  public void setAsc(boolean asc) {
    isAsc = asc;
  }
}
