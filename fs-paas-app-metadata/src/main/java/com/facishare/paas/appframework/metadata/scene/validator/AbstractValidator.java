package com.facishare.paas.appframework.metadata.scene.validator;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.facishare.paas.appframework.metadata.dto.scene.SceneDTO;
import com.facishare.paas.appframework.metadata.dto.scene.SceneExt;
import com.facishare.paas.appframework.metadata.layout.component.FieldComponentExt;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.IFormField;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import com.google.common.base.Strings;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2019/05/23
 */

public abstract class AbstractValidator<T> implements Validator<T> {
    @Getter
    private final Type type;

    protected AbstractValidator() {
        ParameterizedType superClass = (ParameterizedType) getClass().getGenericSuperclass();
        type = (superClass).getActualTypeArguments()[0];
    }

    protected abstract void validateSceneType(IScene scene);

    protected abstract void validateDisplayName(IScene scene);

    protected void validateHeadField(IScene scene, IObjectDescribe describe) {
        if (Objects.equals(scene.getHeadFieldType(), SearchTemplateExt.USE_FIELD_LIST)) {// 使用配置的表头字段
            Preconditions.checkArgument(SceneExt.of(scene).validateHeadField(), I18N.text(I18NKey.HEADER_FIELD_NOT_LESS_TWO));
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
            // 校验列设置中的禁用删除字段
            String fieldNames ;
            if (CollectionUtils.notEmpty(scene.getFieldList())) {
                fieldNames = scene.getFieldList().stream()
                        .map(map -> ((String) map.get(IHeadField.FIELD_NAME)))
                        .map(headField -> getDuplicatesField(headField, describeExt.getFieldDescribe(headField)))
                        .filter(Objects::nonNull)
                        //HeadField中标签并不是一个字段
                        .filter(fieldName -> StringUtils.equalsAny(FieldComponentExt.TYPE_TAG))
                        .collect(Collectors.joining(","));
            } else {
                fieldNames = scene.getHeadField().stream()
                        .map(headField -> getDuplicatesField(headField, describeExt.getFieldDescribe(headField)))
                        .filter(Objects::nonNull)
                        //HeadField中标签并不是一个字段
                        .filter(fieldName -> StringUtils.equalsAny(FieldComponentExt.TYPE_TAG))
                        .collect(Collectors.joining(","));
            }
            Preconditions.checkArgument(Strings.isNullOrEmpty(fieldNames), I18N.text(I18NKey.FIELD_IS_DISABLED_DELETED, fieldNames));
        }
    }

    @Override
    public void validate(IScene scene, IObjectDescribe describe) {
        validateHeadField(scene, describe);
        validateMobileFiled(scene, describe);
    }

    /**
     * 通行条件
     * 1.继承移动端摘要字段
     * getMobileFieldType()使用了int基本数据类型前台传入null会被初始化为0
     * 使用默认设置即继承移动端摘要字段设置
     * 2.独立配置 且 字段数量至少1个
     * @param scene 场景
     * @param describe 描述
     */
    // TODO: 记得要设置到国际管理平台
    protected void validateMobileFiled(IScene scene, IObjectDescribe describe) {
        if (Objects.equals(scene.getMobileFieldType(), SearchTemplateExt.USE_MOBILE_FIELD)) { // 独立设置移动端摘要字段显示
            IObjectDescribe oDescExt = ObjectDescribeExt.of(describe);
            TableComponentExt tableComponentExt = TableComponentExt.of(new TableComponent(scene.getMobileField()));
            // 校验是否有字段，且 field_name is not empty，则，校验字段生命周期，统计失效字段
            availableCheck(tableComponentExt, oDescExt);
        }
    }

    private void availableCheck(TableComponentExt tableComponentExt, IObjectDescribe oDescExt) {
        // 开启了新布局不再校验组件中必须配置字段
        if (tableComponentExt.enableNewLayout()) {
            return;
        }
        String loseFields;
        if (CollectionUtils.notEmpty(tableComponentExt.getIncludeFields())) {
            loseFields = tableComponentExt.getIncludeFields().stream()
                    .peek(includeField -> {
                        //sfa只传了api_name，这里会补充上field_name
                        includeField.setName(ObjectUtils.firstNonNull(includeField.getName(), includeField.get("api_name", String.class)));
                        Preconditions.checkArgument(StringUtils.isNotEmpty(includeField.getName()), I18NExt.text(I18NKey.MOBILE_FIELD_NOT_AVAILABLE));
                    })
                    .map(includeField -> getDuplicatesField(includeField.getName(), oDescExt.getFieldDescribe(includeField.getName())))
                    .filter(Objects::nonNull).collect(Collectors.joining(","));
        } else if(CollectionUtils.notEmpty(tableComponentExt.getFieldSections()) && CollectionUtils.notEmpty(tableComponentExt.getFieldSections().get(0).getFields())) {
            List<IFormField> formFields = tableComponentExt.getFieldSections().get(0).getFields();
            loseFields = formFields.stream()
                    .peek(formField -> {
                        //sfa只传了api_name，这里会补充上field_name
                        formField.setFieldName(ObjectUtils.firstNonNull(formField.getFieldName(), formField.get("api_name", String.class)));
                        Preconditions.checkArgument(StringUtils.isNotEmpty(formField.getFieldName()), I18NExt.text(I18NKey.MOBILE_FIELD_NOT_AVAILABLE));
                    })
                    .map(formField -> getDuplicatesField(formField.getFieldName(), oDescExt.getFieldDescribe(formField.getFieldName())))
                    .filter(Objects::nonNull).collect(Collectors.joining(","));
        } else {
            throw new ValidateException(I18NExt.text(I18NKey.MOBILE_FIELD_NOT_LESS_ONE));
        }
        Preconditions.checkArgument(Strings.isNullOrEmpty(loseFields), I18N.text(I18NKey.FIELD_IS_DISABLED_DELETED, loseFields));

    }

    protected void validateOrders(IScene scene, IObjectDescribe describe) {
        List<OrderByExt> orders = scene.getOrders();
        if (CollectionUtils.empty(orders)) {
            return;
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        // 校验orderBy中的禁用删除的字段
        String fieldNames = orders.stream()
                .map(orderByExt -> getDuplicatesField(orderByExt.getFieldName(), describeExt.getFieldDescribe(orderByExt.getFieldName())))
                .filter(Objects::nonNull)
                .collect(Collectors.joining(","));
        Preconditions.checkArgument(Strings.isNullOrEmpty(fieldNames), I18N.text(I18NKey.FIELD_IS_DISABLED_DELETED, fieldNames));
    }

    protected void validateRecordType(IScene scene, IObjectDescribe describe) {
        if (Strings.isNullOrEmpty(scene.getRecordType())) {
            return;
        }
        Optional<IRecordTypeOption> recordTypeOption = ObjectDescribeExt.of(describe).getRecordTypeOption(scene.getRecordType());
        Preconditions.checkArgument(recordTypeOption.isPresent(), I18N.text(I18NKey.RECORD_TYPE_IS_DELETE));
    }

    protected void validateWheres(IScene scene, IObjectDescribe describe) {
        List<SceneDTO.Where> wheres = scene.getWheres();
        if (CollectionUtils.empty(wheres)) {
            return;
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        // 校验禁用删除的字段
        String fieldNames = wheres.stream()
                .flatMap(where -> where.getFilters().stream())
                .filter(filter -> !FilterExt.UNCHECK_FILTER_VALUE_TYPES.contains(filter.getValueType()))
                .map(filter -> getDuplicatesField(getFieldApiName(filter), describeExt.getFieldDescribe(getFieldApiName(filter))))
                .filter(Objects::nonNull)
                .collect(Collectors.joining(","));
        Preconditions.checkArgument(Strings.isNullOrEmpty(fieldNames), I18N.text(I18NKey.FIELD_IS_DISABLED_DELETED, fieldNames));

        // 校验删除选项
        fieldNames = wheres.stream().flatMap(where -> where.getFilters().stream())
                .filter(filter -> !FilterExt.UNCHECK_FILTER_VALUE_TYPES.contains(filter.getValueType()))
                .map(filter -> validateFilterFieldIsShow(describeExt, filter))
                .filter(Objects::nonNull)
                .collect(Collectors.joining(","));
        Preconditions.checkArgument(Strings.isNullOrEmpty(fieldNames), I18N.text(I18NKey.OPTION_FOR_FIELD_REMOVED, fieldNames));

        // 校验不支持筛选的字段
        fieldNames = wheres.stream()
                .flatMap(where -> where.getFilters().stream())
                .filter(filter -> !FilterExt.UNCHECK_FILTER_VALUE_TYPES.contains(filter.getValueType()))
                .map(filter -> validateFilterIsNotIndex(getFieldApiName(filter), describeExt.getFieldDescribe(getFieldApiName(filter))))
                .filter(Objects::nonNull)
                .collect(Collectors.joining(","));
        Preconditions.checkArgument(Strings.isNullOrEmpty(fieldNames), I18N.text(I18NKey.FIELD_UNSUPPORT_FILTER, fieldNames));
    }

    private String getFieldApiName(SceneDTO.Filter filter) {
        return StringUtils.substringBefore(filter.getFieldName(), ".");
    }

    // TODO: 当fieldName null时会有问题
    protected String getDuplicatesField(String fieldName, IFieldDescribe fieldDescribe) {
        if (Objects.isNull(fieldName)) {
            return "null";
        }
        if (Objects.isNull(fieldDescribe)) {
            return fieldName;
        }
        if (!fieldDescribe.isActive()) {
            return fieldDescribe.getLabel();
        }
        return null;
    }

    private String validateFilterFieldIsShow(ObjectDescribeExt describeExt, SceneDTO.Filter filter) {
        IFieldDescribe field = describeExt.getFieldDescribe(getFieldApiName(filter));
        // 字段被禁用或删除
        if (Objects.isNull(field) || !field.isActive()) {
            return null;
        }
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(field);
        // 滤条件中所有选项被删除
        if ((fieldDescribeExt.isSelectMany() || fieldDescribeExt.isSelectOne()) && CollectionUtils.notEmpty(filter.getFieldValues())) {
            SelectOne select = (SelectOne) field;
            if (!filter.getFieldValues().stream().filter(x -> !Strings.isNullOrEmpty(x)).allMatch(fieldName -> select.getOption(fieldName).isPresent())) {
                return field.getLabel();
            }
        }
        return null;
    }

    private String validateFilterIsNotIndex(String fieldApiName, IFieldDescribe fieldDescribe) {
        if (Objects.isNull(fieldApiName)) {
            return null;
        }
        if (fieldDescribe.isIndex()) {
            return null;
        }
        return fieldDescribe.getLabel();
    }
}
