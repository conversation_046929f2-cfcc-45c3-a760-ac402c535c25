package com.facishare.paas.appframework.core.timezone;

import com.facishare.paas.appframework.core.timezone.annotation.ObjectDataField;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * create by z<PERSON><PERSON> on 2021/06/17
 */
public class MapMapObjectDataFieldConvert extends AbstractObjectDataFieldConvert {

    public static MapMapObjectDataFieldConvert getInstance() {
        return Helper.INSTANCE;
    }

    private MapMapObjectDataFieldConvert() {
    }

    @Override
    public ObjectDataField.Type getType() {
        return ObjectDataField.Type.MAP_MAP;
    }

    @Override
    public <T> T convert2SystemZone(T value, Function<String, IObjectDescribe> function) {
        if (Objects.isNull(value)) {
            return null;
        }
        Map<String, Map<String, IObjectData>> calculateDetail = toCalculateDetail(value);

        calculateDetail.forEach((apiName, dataMap) -> {
            IObjectDescribe describe = function.apply(apiName);
            dataMap.values().forEach(it -> ObjectDataExt.of(it).convertDateFieldValueToSystemZone(describe));
        });

        return toCalculateEntity(calculateDetail, value);
    }


    @Override
    public <T> T convert2CustomZone(T value, Function<String, IObjectDescribe> function) {
        if (Objects.isNull(value)) {
            return null;
        }
        Map<String, Map<String, IObjectData>> calculateDetail = toCalculateDetail(value);

        calculateDetail.forEach((apiName, data) -> {
            IObjectDescribe describe = function.apply(apiName);
            data.values().forEach(it -> ObjectDataExt.of(it).convertDateFieldValueToCustomZone(describe));
        });

        return toCalculateEntity(calculateDetail, value);
    }

    private static final class Helper {
        private static final MapMapObjectDataFieldConvert INSTANCE = new MapMapObjectDataFieldConvert();
    }
}
