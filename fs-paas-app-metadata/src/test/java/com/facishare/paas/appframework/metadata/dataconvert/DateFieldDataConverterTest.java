package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试DateFieldDataConverter类的日期字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class DateFieldDataConverterTest {

  @Mock
  private IObjectDataProxyService dataService;

  @Mock
  private SessionContext sessionContext;

  private DateDataConverter converter;
  private IFieldDescribe fieldDescribe;
  private IObjectData objectData;

  @BeforeEach
  void setUp() {
    converter = new DateDataConverter();
    fieldDescribe = createDateFieldDescribe("date_field");
    objectData = createObjectData("date_field", 1640995200000L); // 2022-01-01 00:00:00 UTC
    
    // 设置转换器的属性
    converter.setFieldDescribe(fieldDescribe);
    converter.setObjectData(objectData);
    converter.setDataService(dataService);
    
    // 设置SessionContext的默认行为
    lenient().when(sessionContext.getRegion()).thenReturn("");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法正常转换Long时间戳
   */
  @Test
  @DisplayName("测试convertFieldData - Long时间戳转换")
  void testConvertFieldData_LongTimestampConversion() throws Exception {
    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 应该返回格式化的日期字符串
    assertNotNull(result);
    assertFalse(result.isEmpty());
    // 具体的日期格式取决于DateFieldDescribe的dateFormat设置
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理null值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null值")
  void testConvertFieldData_HandlesNullValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("date_field", null);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理空字符串
   */
  @Test
  @DisplayName("测试convertFieldData - 处理空字符串")
  void testConvertFieldData_HandlesEmptyString() throws Exception {
    // 准备测试数据
    objectData = createObjectData("date_field", "");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理"null"字符串
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null字符串")
  void testConvertFieldData_HandlesNullString() throws Exception {
    // 准备测试数据
    objectData = createObjectData("date_field", "null");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理已经是字符串格式的日期
   */
  @Test
  @DisplayName("测试convertFieldData - 处理字符串格式日期")
  void testConvertFieldData_HandlesStringDate() throws Exception {
    // 准备测试数据
    String dateString = "2022-01-01";
    objectData = createObjectData("date_field", dateString);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 如果输入已经是字符串，应该直接返回
    assertEquals(dateString, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理多区域格式化
   */
  @Test
  @DisplayName("测试convertFieldData - 多区域格式化")
  void testConvertFieldData_MultiRegionFormatting() throws Exception {
    // 准备测试数据
    when(sessionContext.getRegion()).thenReturn("US");
    objectData = createObjectData("date_field", 1640995200000L);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 多区域格式化可能返回null或格式化后的字符串
    // 这里我们只验证方法能正常执行，不抛出异常
    assertTrue(result == null || result.length() >= 0);
  }

  /**
   * 提供不同类型的日期值测试数据
   */
  private static Stream<Arguments> provideDateValues() {
    return Stream.of(
        Arguments.of(1640995200000L, true), // 有效的时间戳
        Arguments.of(0L, true), // Unix纪元时间
        Arguments.of(-1L, false), // 负数时间戳（可能无效）
        Arguments.of("2022-01-01", true), // 字符串格式
        Arguments.of("invalid-date", true), // 无效字符串（会被直接返回）
        Arguments.of(123, true) // 非Long类型的数字
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理不同类型的日期值
   */
  @ParameterizedTest
  @MethodSource("provideDateValues")
  @DisplayName("测试convertFieldData - 不同类型的日期值")
  void testConvertFieldData_DifferentDateValues(Object inputValue, boolean shouldSucceed) throws Exception {
    // 准备测试数据
    objectData = createObjectData("date_field", inputValue);
    converter.setObjectData(objectData);

    // 执行被测试方法
    if (shouldSucceed) {
      String result = converter.convertFieldData(sessionContext);
      // 验证结果 - 应该返回某种字符串结果
      assertNotNull(result);
    } else {
      // 对于可能失败的情况，我们只验证不抛出未处理的异常
      assertDoesNotThrow(() -> {
        converter.convertFieldData(sessionContext);
      });
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理字段不存在的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 字段不存在")
  void testConvertFieldData_FieldNotExists() throws Exception {
    // 准备测试数据 - 创建不包含目标字段的数据
    objectData = createObjectData("other_field", 1640995200000L);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 字段不存在时应该返回空字符串
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理异常情况
   */
  @Test
  @DisplayName("测试convertFieldData - 异常处理")
  void testConvertFieldData_ExceptionHandling() throws Exception {
    // 准备测试数据 - 使用可能导致转换异常的值
    objectData = createObjectData("date_field", Double.NaN);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 异常情况下应该返回空字符串或NaN的字符串表示
    assertNotNull(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试转换器的基本属性设置
   */
  @Test
  @DisplayName("测试转换器属性设置")
  void testConverterProperties() {
    // 验证属性设置
    assertEquals(fieldDescribe, converter.getFieldDescribe());
    assertEquals(objectData, converter.getObjectData());
    assertEquals(dataService, converter.getDataService());
  }

  /**
   * 创建测试用的日期字段描述
   */
  private IFieldDescribe createDateFieldDescribe(String apiName) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试日期字段");
    fieldMap.put("type", IFieldType.DATE);
    fieldMap.put("date_format", "yyyy-MM-dd"); // 设置日期格式
    return FieldDescribeFactory.newInstance(fieldMap);
  }

  /**
   * 创建测试用的对象数据
   */
  private IObjectData createObjectData(String fieldName, Object value) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldName, value);
    return new ObjectData(dataMap);
  }
}
