package com.facishare.paas.appframework.metadata.task.impl;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.OrderByExt;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.MtAsyncTaskMonitor;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.appframework.metadata.task.AsyncTaskService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service("asyncTaskService")
public class AsyncTaskServiceImpl implements AsyncTaskService {

    @Resource
    private IRepository<MtAsyncTaskMonitor> repository;

    @Override
    public void create(User user, MtAsyncTaskMonitor mtAsyncTaskMonitor) {
        if (Objects.isNull(mtAsyncTaskMonitor)) {
            return;
        }
        repository.create(user, mtAsyncTaskMonitor);
    }

    @Override
    public void batchCreate(User user, List<MtAsyncTaskMonitor> mtAsyncTaskMonitors) {
        repository.bulkCreate(user, mtAsyncTaskMonitors);
    }

    @Override
    public void update(User user, MtAsyncTaskMonitor mtAsyncTaskMonitor, List<String> updateFieldList) {
        if (Objects.isNull(mtAsyncTaskMonitor)) {
            return;
        }
        batchUpdate(user, Lists.newArrayList(mtAsyncTaskMonitor), updateFieldList);
    }

    @Override
    public void batchUpdate(User user, List<MtAsyncTaskMonitor> mtAsyncTaskMonitors, List<String> updateFieldList) {
        repository.bulkUpdateByFields(user, mtAsyncTaskMonitors, updateFieldList);
    }

    @Override
    public void updateWithMap(User user, String taskId, Map<String, Object> fieldMap) {
        if (Objects.isNull(taskId) || !CollectionUtils.notEmpty(fieldMap)) {
            return;
        }
        MtAsyncTaskMonitor mtAsyncTaskMonitor = find(user, taskId);
        if (Objects.isNull(mtAsyncTaskMonitor)) {
            return;
        }
        repository.bulkUpdateByFields(user, Lists.newArrayList(mtAsyncTaskMonitor),
                (dateList) -> dateList.forEach(x -> ObjectDataExt.of(x).putAll(fieldMap)),
                Lists.newArrayList(fieldMap.keySet()));
    }

    @Override
    public MtAsyncTaskMonitor find(User user, String taskId) {
        SearchQuery searchQuery = SearchQueryImpl.filters(
                Lists.newArrayList(
                        FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter(),
                        FilterExt.of(Operator.EQ, MtAsyncTaskMonitor.TASK_ID, taskId).getFilter()),
                false);
        Query query = Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, false)))
                .limit(1)
                .offset(0)
                .build();
        QueryResult<MtAsyncTaskMonitor> queryResult = repository.findByQuery(user, query, MtAsyncTaskMonitor.class);
        return CollectionUtils.notEmpty(queryResult.getData()) ? queryResult.getData().get(0) : null;
    }

    @Override
    public List<MtAsyncTaskMonitor> findBySearchQuery(User user, SearchQuery searchQuery) {
        Query query = Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, false)))
                .limit(2000)
                .offset(0)
                .build();
        QueryResult<MtAsyncTaskMonitor> queryResult = repository.findByQuery(user, query, MtAsyncTaskMonitor.class);
        return CollectionUtils.notEmpty(queryResult.getData()) ? queryResult.getData() : Lists.newArrayList();
    }
}
