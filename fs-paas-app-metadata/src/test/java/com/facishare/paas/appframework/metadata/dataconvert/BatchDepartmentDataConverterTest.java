package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试BatchDepartmentDataConverter类的批量部门字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class BatchDepartmentDataConverterTest {

  @Mock
  private OrgService orgService;

  @Mock
  private User user;

  @InjectMocks
  private BatchDepartmentDataConverter converter;

  private IFieldDescribe departmentFieldDescribe;

  @BeforeEach
  void setUp() {
    departmentFieldDescribe = createDepartmentFieldDescribe("department_field", IFieldType.DEPARTMENT);
    
    // 设置mock行为
    lenient().when(user.getTenantId()).thenReturn("tenant123");
    lenient().when(user.getUserId()).thenReturn("currentUser");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法正常获取部门名映射
   */
  @Test
  @DisplayName("测试getIdNameMap - 正常获取部门名映射")
  void testGetIdNameMap_NormalDepartmentNameMapping() {
    // 准备测试数据
    List<String> deptIds = Arrays.asList("dept1", "dept2", "dept3");
    List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = Arrays.asList(
        createDeptInfo("dept1", "技术部"),
        createDeptInfo("dept2", "销售部"),
        createDeptInfo("dept3", "人事部")
    );
    
    when(orgService.getDeptInfoNameByIds("tenant123", "currentUser", deptIds))
        .thenReturn(deptInfos);

    // 执行被测试方法
    Map<String, String> result = converter.getIdNameMap(deptIds, departmentFieldDescribe, user);

    // 验证结果
    assertNotNull(result);
    assertEquals(3, result.size());
    assertEquals("技术部", result.get("dept1"));
    assertEquals("销售部", result.get("dept2"));
    assertEquals("人事部", result.get("dept3"));
    
    verify(orgService).getDeptInfoNameByIds("tenant123", "currentUser", deptIds);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法处理空列表
   */
  @Test
  @DisplayName("测试getIdNameMap - 处理空列表")
  void testGetIdNameMap_EmptyList() {
    // 准备测试数据
    List<String> deptIds = Collections.emptyList();
    List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = Collections.emptyList();
    
    when(orgService.getDeptInfoNameByIds("tenant123", "currentUser", deptIds))
        .thenReturn(deptInfos);

    // 执行被测试方法
    Map<String, String> result = converter.getIdNameMap(deptIds, departmentFieldDescribe, user);

    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());
    
    verify(orgService).getDeptInfoNameByIds("tenant123", "currentUser", deptIds);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法处理部分部门不存在的情况
   */
  @Test
  @DisplayName("测试getIdNameMap - 部分部门不存在")
  void testGetIdNameMap_PartialDepartmentsNotFound() {
    // 准备测试数据
    List<String> deptIds = Arrays.asList("dept1", "dept2", "dept3");
    List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = Arrays.asList(
        createDeptInfo("dept1", "技术部"),
        createDeptInfo("dept3", "人事部")
        // dept2不存在于返回的列表中
    );
    
    when(orgService.getDeptInfoNameByIds("tenant123", "currentUser", deptIds))
        .thenReturn(deptInfos);

    // 执行被测试方法
    Map<String, String> result = converter.getIdNameMap(deptIds, departmentFieldDescribe, user);

    // 验证结果
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals("技术部", result.get("dept1"));
    assertEquals("人事部", result.get("dept3"));
    assertNull(result.get("dept2"));
    
    verify(orgService).getDeptInfoNameByIds("tenant123", "currentUser", deptIds);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法处理null用户
   */
  @Test
  @DisplayName("测试getIdNameMap - null用户")
  void testGetIdNameMap_NullUser() {
    // 准备测试数据
    List<String> deptIds = Arrays.asList("dept1");
    
    // 执行被测试方法 - 传入null用户应该抛出异常
    assertThrows(NullPointerException.class, () -> {
      converter.getIdNameMap(deptIds, departmentFieldDescribe, null);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法处理OrgService返回null的情况
   */
  @Test
  @DisplayName("测试getIdNameMap - OrgService返回null")
  void testGetIdNameMap_OrgServiceReturnsNull() {
    // 准备测试数据
    List<String> deptIds = Arrays.asList("dept1", "dept2");

    when(orgService.getDeptInfoNameByIds("tenant123", "currentUser", deptIds))
        .thenReturn(null);

    // 执行被测试方法 - 当OrgService返回null时，stream操作会抛出NullPointerException
    assertThrows(NullPointerException.class, () -> {
      converter.getIdNameMap(deptIds, departmentFieldDescribe, user);
    });

    verify(orgService).getDeptInfoNameByIds("tenant123", "currentUser", deptIds);
  }

  /**
   * 提供不同大小的部门ID列表测试数据
   */
  private static Stream<Arguments> provideDeptIdLists() {
    return Stream.of(
        Arguments.of(Arrays.asList("dept1"), 1),
        Arguments.of(Arrays.asList("dept1", "dept2"), 2),
        Arguments.of(Arrays.asList("dept1", "dept2", "dept3", "dept4", "dept5"), 5),
        Arguments.of(Collections.emptyList(), 0)
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法处理不同大小的部门ID列表
   */
  @ParameterizedTest
  @MethodSource("provideDeptIdLists")
  @DisplayName("测试getIdNameMap - 不同大小的部门ID列表")
  void testGetIdNameMap_DifferentSizedDeptIdLists(List<String> deptIds, int expectedSize) {
    // 准备测试数据
    List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = deptIds.stream()
        .map(id -> createDeptInfo(id, "部门" + id.substring(4)))
        .collect(java.util.stream.Collectors.toList());
    
    when(orgService.getDeptInfoNameByIds("tenant123", "currentUser", deptIds))
        .thenReturn(deptInfos);

    // 执行被测试方法
    Map<String, String> result = converter.getIdNameMap(deptIds, departmentFieldDescribe, user);

    // 验证结果
    assertNotNull(result);
    assertEquals(expectedSize, result.size());
    
    verify(orgService).getDeptInfoNameByIds("tenant123", "currentUser", deptIds);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getSupportedFieldTypes方法
   */
  @Test
  @DisplayName("测试getSupportedFieldTypes")
  void testGetSupportedFieldTypes() {
    // 执行被测试方法
    List<String> supportedTypes = converter.getSupportedFieldTypes();

    // 验证结果
    assertNotNull(supportedTypes);
    assertEquals(1, supportedTypes.size());
    assertTrue(supportedTypes.contains(IFieldType.DEPARTMENT));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法处理重复部门ID的情况
   */
  @Test
  @DisplayName("测试getIdNameMap - 重复部门ID")
  void testGetIdNameMap_DuplicateDeptIds() {
    // 准备测试数据 - 包含重复的部门ID
    List<String> deptIds = Arrays.asList("dept1", "dept2", "dept1", "dept3");
    List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = Arrays.asList(
        createDeptInfo("dept1", "技术部"),
        createDeptInfo("dept2", "销售部"),
        createDeptInfo("dept1", "技术部"), // 重复的部门信息
        createDeptInfo("dept3", "人事部")
    );

    when(orgService.getDeptInfoNameByIds("tenant123", "currentUser", deptIds))
        .thenReturn(deptInfos);

    // 执行被测试方法 - 重复的key会导致IllegalStateException
    assertThrows(IllegalStateException.class, () -> {
      converter.getIdNameMap(deptIds, departmentFieldDescribe, user);
    });

    verify(orgService).getDeptInfoNameByIds("tenant123", "currentUser", deptIds);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法处理特殊字符部门ID的情况
   */
  @Test
  @DisplayName("测试getIdNameMap - 特殊字符部门ID")
  void testGetIdNameMap_SpecialCharacterDeptIds() {
    // 准备测试数据 - 包含特殊字符的部门ID
    List<String> deptIds = Arrays.asList("dept-1", "dept_2", "dept@3", "dept.4");
    List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = Arrays.asList(
        createDeptInfo("dept-1", "部门1"),
        createDeptInfo("dept_2", "部门2"),
        createDeptInfo("dept@3", "部门3"),
        createDeptInfo("dept.4", "部门4")
    );
    
    when(orgService.getDeptInfoNameByIds("tenant123", "currentUser", deptIds))
        .thenReturn(deptInfos);

    // 执行被测试方法
    Map<String, String> result = converter.getIdNameMap(deptIds, departmentFieldDescribe, user);

    // 验证结果
    assertNotNull(result);
    assertEquals(4, result.size());
    assertEquals("部门1", result.get("dept-1"));
    assertEquals("部门2", result.get("dept_2"));
    assertEquals("部门3", result.get("dept@3"));
    assertEquals("部门4", result.get("dept.4"));
    
    verify(orgService).getDeptInfoNameByIds("tenant123", "currentUser", deptIds);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法处理OrgService抛出异常的情况
   */
  @Test
  @DisplayName("测试getIdNameMap - OrgService抛出异常")
  void testGetIdNameMap_OrgServiceThrowsException() {
    // 准备测试数据
    List<String> deptIds = Arrays.asList("dept1", "dept2");
    
    when(orgService.getDeptInfoNameByIds("tenant123", "currentUser", deptIds))
        .thenThrow(new RuntimeException("Service error"));

    // 执行被测试方法 - 应该抛出异常
    assertThrows(RuntimeException.class, () -> {
      converter.getIdNameMap(deptIds, departmentFieldDescribe, user);
    });
    
    verify(orgService).getDeptInfoNameByIds("tenant123", "currentUser", deptIds);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法处理部门信息中包含null值的情况
   */
  @Test
  @DisplayName("测试getIdNameMap - 部门信息包含null值")
  void testGetIdNameMap_DeptInfoContainsNullValues() {
    // 准备测试数据 - 包含null的部门信息
    List<String> deptIds = Arrays.asList("dept1", "dept2", "dept3");
    List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = Arrays.asList(
        createDeptInfo("dept1", "技术部"),
        createDeptInfo(null, "销售部"), // deptId为null
        createDeptInfo("dept3", null)  // deptName为null
    );

    when(orgService.getDeptInfoNameByIds("tenant123", "currentUser", deptIds))
        .thenReturn(deptInfos);

    // 执行被测试方法 - null key会导致NullPointerException
    assertThrows(NullPointerException.class, () -> {
      converter.getIdNameMap(deptIds, departmentFieldDescribe, user);
    });

    verify(orgService).getDeptInfoNameByIds("tenant123", "currentUser", deptIds);
  }

  /**
   * 创建测试用的部门信息
   */
  private QueryDeptInfoByDeptIds.DeptInfo createDeptInfo(String deptId, String deptName) {
    QueryDeptInfoByDeptIds.DeptInfo deptInfo = new QueryDeptInfoByDeptIds.DeptInfo();
    deptInfo.setDeptId(deptId);
    deptInfo.setDeptName(deptName);
    return deptInfo;
  }

  /**
   * 创建测试用的部门字段描述
   */
  private IFieldDescribe createDepartmentFieldDescribe(String apiName, String fieldType) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试部门字段");
    fieldMap.put("type", fieldType);
    return FieldDescribeFactory.newInstance(fieldMap);
  }
}
