package com.facishare.crm.common.exception

import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：CRMErrorCode枚举类的单元测试
 */
class CRMErrorCodeTest extends Specification {

    def "test basic error codes exist"() {
        expect: "验证基本错误码存在"
        CRMErrorCode.SUCCESS != null
        CRMErrorCode.SYSTEM_ERROR != null
        CRMErrorCode.PARAMETER_IS_WRONG != null
        CRMErrorCode.AUTHENTICATION_ERROR != null
        CRMErrorCode.PAAS_ERROR != null
    }

    @Unroll
    def "test getCode method returns correct value for #errorCode"() {
        expect: "验证getCode方法返回正确的错误码"
        errorCode.getCode() == expectedCode

        where:
        errorCode                           | expectedCode
        CRMErrorCode.SUCCESS               | 0
        CRMErrorCode.SYSTEM_ERROR          | 1
        CRMErrorCode.PARAMETER_IS_WRONG    | 5
        CRMErrorCode.AUTHENTICATION_ERROR  | 10
        CRMErrorCode.PAAS_PRIVILEGE_FAILED | 11
        CRMErrorCode.PAAS_ERROR            | 12
        CRMErrorCode.LOW_VERSION_ERROR     | 13
        CRMErrorCode.WRONG_VERSION_ERROR   | 14
    }

    @Unroll
    def "test getStrCode method returns string representation for #errorCode"() {
        when: "调用getStrCode方法"
        def result = errorCode.getStrCode()

        then: "应该返回字符串形式的错误码"
        result == expectedStrCode

        where:
        errorCode                           | expectedStrCode
        CRMErrorCode.SUCCESS               | "0"
        CRMErrorCode.SYSTEM_ERROR          | "1"
        CRMErrorCode.PARAMETER_IS_WRONG    | "5"
        CRMErrorCode.AUTHENTICATION_ERROR  | "10"
        CRMErrorCode.PAAS_ERROR            | "12"
    }

    def "test getMessage method returns non-null values"() {
        when: "调用getMessage方法"
        def messages = CRMErrorCode.values().collect { it.getMessage() }

        then: "所有消息都不应该为null"
        messages.every { it != null }
    }

    @Unroll
    def "test valueOf with valid code #code"() {
        when: "使用有效的错误码调用valueOf"
        def result = CRMErrorCode.valueOf(code)

        then: "应该返回正确的枚举值"
        result == expectedErrorCode

        where:
        code | expectedErrorCode
        0    | CRMErrorCode.SUCCESS
        1    | CRMErrorCode.SYSTEM_ERROR
        5    | CRMErrorCode.PARAMETER_IS_WRONG
        10   | CRMErrorCode.AUTHENTICATION_ERROR
        11   | CRMErrorCode.PAAS_PRIVILEGE_FAILED
        12   | CRMErrorCode.PAAS_ERROR
    }

    def "test valueOf with invalid code throws exception"() {
        when: "使用无效的错误码调用valueOf"
        CRMErrorCode.valueOf(99999)

        then: "应该抛出IllegalArgumentException"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Invalid code value: 99999")
    }

    def "test CRM common error codes"() {
        expect: "验证CRM通用错误码"
        CRMErrorCode.FS_CRM_COMMON_RUNTIME_UNKOWN.getCode() == 304000001
        CRMErrorCode.FS_CRM_COMMON_RUNTIME_CONFIG_CENTER_FILE_NULL.getCode() == 304000002
        CRMErrorCode.FS_CRM_COMMON_CHECKED_PARAMETER_INVALID.getCode() == 204000001
        CRMErrorCode.FS_CRM_COMMON_CHECKED_AUTHENTICATION_ERROR.getCode() == 204000002
        CRMErrorCode.FS_CRM_COMMON_CHECKED_USERID_NULL.getCode() == 204000003
    }

    def "test OpenAPI error codes"() {
        expect: "验证OpenAPI错误码"
        CRMErrorCode.FS_CRM_OPENAPI_UNSUPPORTED_OBJECT_TYPE.getCode() == 204100001
        CRMErrorCode.FS_CRM_OPENAPI_EXTERNAL_SERVICE_REST_EXCEPTION.getCode() == 204110002
        CRMErrorCode.FS_CRM_OPENAPI_EXTERNAL_SERVICE_REST_NULLRESPONSE_EXCEPTION.getCode() == 204110003
        CRMErrorCode.FS_CRM_OPENAPI_FIELD_NOT_ALLOWED_UPDATED.getCode() == 204110006
        CRMErrorCode.FS_CRM_OPENAPI_FIELD_NOT_ALLOWED_CREATED.getCode() == 204110007
    }

    def "test DefObj error codes"() {
        expect: "验证DefObj错误码"
        CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NORELATION_BETWEEN_OBJECTS.getCode() == 204200001
        CRMErrorCode.FS_CRM_DEFOBJ_MDS_LAYOUT_INIT_ERROR.getCode() == 304200001
        CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_CANNOT_DISASSOCIATE.getCode() == 204200007
        CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NOT_EXIST_DATA.getCode() == 204200008
        CRMErrorCode.FS_CRM_DEFOBJ_EXTERNAL_BEYOND_OBJ_MAX_LIMIT.getCode() == 204200014
    }

    def "test privilege error codes"() {
        expect: "验证权限相关错误码"
        CRMErrorCode.FS_CRM_PRIVILEGE_PAAS_SQL_ERROR.getCode() == 304310000
        CRMErrorCode.FS_CRM_PRIVILEGE_PAAS_REST_ERROR.getCode() == 304310002
        CRMErrorCode.FS_CRM_PRIVILEGE_SYSTEM_UNKNOWN_ERROR.getCode() == 304310003
        CRMErrorCode.PRIVILEGE_SYSTEM_ERROR.getCode() == 304300001
        CRMErrorCode.DATA_PRIVILEGE_NO_DATA_PRIVILEGE.getCode() == 304300013
    }

    def "test license error codes"() {
        expect: "验证License错误码"
        CRMErrorCode.LICENSE_VERSION_ERROR.getCode() == 304500001
        CRMErrorCode.LICENSE_QUOTA_ERROR.getCode() == 304500002
        CRMErrorCode.LICENSE_ERROR.getCode() == 304500003
    }

    def "test group error codes"() {
        expect: "验证用户组错误码"
        CRMErrorCode.GROUP_SYSTEM_ERROR.getCode() == 304400001
        CRMErrorCode.GROUP_PARAMETER_ERROR.getCode() == 304400002
        CRMErrorCode.GROUP_PAAS_ERROR.getCode() == 304400011
    }

    def "test print template error codes"() {
        expect: "验证打印模板错误码"
        CRMErrorCode.FS_CRM_PRINTTEMPLATE_COUNT_EXCEED_MAX.getCode() == 304511002
        CRMErrorCode.FS_CRM_PRINTTEMPLATE_DUPLICATE_NAME.getCode() == 304511003
    }


    def "test enum values method returns all values"() {
        when: "调用values方法"
        def values = CRMErrorCode.values()

        then: "应该返回所有枚举值"
        values.length > 80  // 确保有足够多的错误码
        values.contains(CRMErrorCode.SUCCESS)
        values.contains(CRMErrorCode.SYSTEM_ERROR)
        values.contains(CRMErrorCode.LICENSE_ERROR)
    }

    def "test enum name method returns correct names"() {
        expect: "验证name方法返回正确的枚举名称"
        CRMErrorCode.SUCCESS.name() == "SUCCESS"
        CRMErrorCode.SYSTEM_ERROR.name() == "SYSTEM_ERROR"
        CRMErrorCode.PARAMETER_IS_WRONG.name() == "PARAMETER_IS_WRONG"
        CRMErrorCode.AUTHENTICATION_ERROR.name() == "AUTHENTICATION_ERROR"
    }

    def "test valueOf string method works correctly"() {
        expect: "验证字符串valueOf方法正常工作"
        CRMErrorCode.valueOf("SUCCESS") == CRMErrorCode.SUCCESS
        CRMErrorCode.valueOf("SYSTEM_ERROR") == CRMErrorCode.SYSTEM_ERROR
        CRMErrorCode.valueOf("PARAMETER_IS_WRONG") == CRMErrorCode.PARAMETER_IS_WRONG
    }

    def "test valueOf string with invalid name throws exception"() {
        when: "使用无效的枚举名称"
        CRMErrorCode.valueOf("INVALID_ERROR_CODE")

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test error code ranges"() {
        given: "获取所有错误码"
        def allCodes = CRMErrorCode.values().collect { it.getCode() }

        expect: "验证错误码范围"
        // 基础错误码 (0-99)
        allCodes.any { it >= 0 && it <= 99 }
        // CRM通用错误码 (204000000-304999999)
        allCodes.any { it >= 204000000 && it <= 304999999 }
    }

    def "test toString method returns enum name"() {
        expect: "验证toString方法返回枚举名称"
        CRMErrorCode.SUCCESS.toString() == "SUCCESS"
        CRMErrorCode.SYSTEM_ERROR.toString() == "SYSTEM_ERROR"
        CRMErrorCode.LICENSE_ERROR.toString() == "LICENSE_ERROR"
    }
}
