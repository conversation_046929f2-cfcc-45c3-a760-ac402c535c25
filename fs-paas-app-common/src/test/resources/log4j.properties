# è®¾ç½®æ ¹æ¥å¿çº§å«ä¸ºWARNï¼å¹¶å®ä¹è¾åºç®æ ä¸ºæ§å¶å°
log4j.rootLogger=WARN, console

# æ§å¶å°è¾åºéç½®
log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n

# è®¾ç½®ç¹å®åçæ¥å¿çº§å«
log4j.logger.com.github.trace=ERROR
log4j.logger.com.facishare=INFO
log4j.logger.com.github.autoconf=ERROR
log4j.logger.org.apache.commons.beanutils=ERROR
log4j.logger.org.apache.commons=ERROR
log4j.logger.org.apache=ERROR
log4j.logger.org.springframework=ERROR
log4j.logger.org.mybatis=ERROR
log4j.logger.org.hibernate=ERROR
log4j.logger.com.alibaba=ERROR
log4j.logger.com.github=ERROR
log4j.logger.io.netty=ERROR
log4j.logger.org.jboss=ERROR
log4j.logger.org.apache.zookeeper=ERROR
log4j.logger.org.apache.dubbo=ERROR
log4j.logger.com.netflix=ERROR 