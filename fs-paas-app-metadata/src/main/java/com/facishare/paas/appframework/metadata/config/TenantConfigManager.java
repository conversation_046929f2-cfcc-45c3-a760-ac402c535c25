package com.facishare.paas.appframework.metadata.config;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/3/4 10:25
 */
@Slf4j
@Component
public class TenantConfigManager {
    private static List<WebConfig> ALL_CONFIG = Lists.newArrayList();
    public static Map<String, Map<String, Object>> WEB_CONFIG = Maps.newHashMap();
    private static final Splitter CONFIG_SPLITTER = Splitter.onPattern("[,|;]").omitEmptyStrings().trimResults();
    public static final String DEFAULT_VALUE = "D";
    public static final String VALUE = "V";
    public static final String SHARE = "S";
    public static final String RULE = "Rule";

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-paas-web-config", config -> {
            log.info("reload config fs-paas-web-config,content:{}", config.getString());
            WEB_CONFIG = JacksonUtils.fromJson(config.getString(), Map.class);
            initWebConfig();
        });
    }

//    public Map<String, Object> getWebConfig(String enterpriseAccount) {
//        Map<String, Object> config = Maps.newHashMap();
//        Map<String, Map<String, Object>> webConfig = WEB_CONFIG;
//        if (CollectionUtils.empty(webConfig)) {
//            return config;
//        }
//        webConfig.forEach((event, data) -> {
//            Object share = data.get(SHARE);
//            if (Boolean.TRUE.equals(share)) {
//                Object rule = data.get(RULE);
//                Object value = data.get(VALUE);
//                Object defaultValue = data.get(DEFAULT_VALUE);
//                if (Objects.isNull(rule)) {
//                    config.put(event, defaultValue);
//                    return;
//                }
//                Set<String> enterpriseAccounts = Sets.newHashSet(CONFIG_SPLITTER.split(rule.toString()));
//                if (CollectionUtils.notEmpty(enterpriseAccounts) && enterpriseAccounts.contains(enterpriseAccount)) {
//                    config.put(event, value);
//                } else {
//                    config.put(event, defaultValue);
//                }
//            }
//        });
//        return config;
//    }


    public Map<String, Object> getWebConfig(String enterpriseAccount) {
        Map<String, Object> config = Maps.newHashMap();
        if (CollectionUtils.empty(ALL_CONFIG)) {
            return config;
        }
        ALL_CONFIG.forEach(webConfig -> {
            String event = webConfig.getEvent();
            boolean share = webConfig.isShare();
            Set<String> eaSet = webConfig.getEaSet();
            Object value = webConfig.getValue();
            Object defaultValue = webConfig.getDefaultValue();
            if (share) {
                if (CollectionUtils.empty(eaSet)) {
                    config.put(event, defaultValue);
                    return;
                }
                if (eaSet.contains("*")) {
                    config.put(event, value);
                } else if (eaSet.contains(enterpriseAccount)) {
                    config.put(event, value);
                } else {
                    config.put(event, defaultValue);
                }
            }
        });
        return config;
    }

    private void initWebConfig() {
        if (CollectionUtils.empty(WEB_CONFIG)) {
            return;
        }

        List<WebConfig> list = Lists.newArrayList();
        WEB_CONFIG.forEach((event, data) -> {
            boolean share = Boolean.TRUE.equals(data.get(SHARE));
            Object value = null;
            Object defaultValue = null;
            Set<String> eaSet = Sets.newHashSet();
            if (share) {
                value = data.get(VALUE);
                defaultValue = data.get(DEFAULT_VALUE);
                Object rule = data.get(RULE);
                if (!Objects.isNull(rule)) {
                    eaSet = Sets.newHashSet(CONFIG_SPLITTER.split(rule.toString()));
                }
            }
            list.add(WebConfig.builder()
                    .event(event)
                    .share(share)
                    .eaSet(eaSet)
                    .value(value)
                    .defaultValue(defaultValue)
                    .build());
        });
        ALL_CONFIG = list;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    private static class WebConfig {
        private String event;
        private boolean share;
        private Set<String> eaSet;
        private Object value;
        private Object defaultValue;
    }
}
