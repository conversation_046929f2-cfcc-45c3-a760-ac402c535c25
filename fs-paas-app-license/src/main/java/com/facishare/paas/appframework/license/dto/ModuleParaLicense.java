package com.facishare.paas.appframework.license.dto;

import com.facishare.paas.license.pojo.ModuleParaPojo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by zhouwr on 2017/10/13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ModuleParaLicense {
    private String id;
    private String tenantId;
    private String productId;
    private String moduleId;
    private String moduleCode;
    private String paraKey;
    private String paraValue;
    private String paraType;
    private int usedValue;
    private boolean calculate;
    private String realParaValue;
    private String poolParaValue;

    public static ModuleParaLicense from(ModuleParaPojo moduleInfoPojo) {
        return ModuleParaLicense.builder()
                .id(moduleInfoPojo.getId())
                .tenantId(moduleInfoPojo.getTenantId())
                .productId(moduleInfoPojo.getProductId())
                .moduleId(moduleInfoPojo.getModuleId())
                .moduleCode(moduleInfoPojo.getModuleCode())
                .paraKey(moduleInfoPojo.getParaKey())
                .paraValue(moduleInfoPojo.getParaValue())
                .paraType(moduleInfoPojo.getParaType())
                .usedValue(moduleInfoPojo.getUsedValue())
                .calculate(moduleInfoPojo.isCalculate())
                .realParaValue(moduleInfoPojo.getRealParaValue())
                .poolParaValue(moduleInfoPojo.getPoolParaValue())
                .build();
    }
}
