package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试DepartmentDataConverter类的部门字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class DepartmentDataConverterTest {

  @Mock
  private OrgService orgService;

  @Mock
  private DataConvertContext context;

  @Mock
  private User user;

  @Mock
  private RequestContext requestContext;

  @InjectMocks
  private DepartmentDataConverter converter;

  private IFieldDescribe departmentFieldDescribe;
  private IFieldDescribe departmentManyFieldDescribe;
  private IObjectData objectData;

  @BeforeEach
  void setUp() {
    departmentFieldDescribe = createDepartmentFieldDescribe("department_field", IFieldType.DEPARTMENT);
    departmentManyFieldDescribe = createDepartmentFieldDescribe("department_many_field", IFieldType.DEPARTMENT_MANY);
    objectData = createObjectData("department_field", Arrays.asList("dept1", "dept2"));
    
    // 设置mock行为
    lenient().when(context.getUser()).thenReturn(user);
    lenient().when(user.getTenantId()).thenReturn("tenant123");
    lenient().when(user.getUserId()).thenReturn("currentUser");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法正常转换单个部门
   */
  @Test
  @DisplayName("测试convertFieldData - 单个部门转换")
  void testConvertFieldData_SingleDepartmentConversion() {
    // 准备测试数据
    List<String> deptIds = Arrays.asList("dept1");
    List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = Arrays.asList(
        createDeptInfo("dept1", "技术部", QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE.getCode())
    );
    
    objectData = createObjectData("department_field", deptIds);
    
    try (MockedStatic<RequestContextManager> mockedStatic = mockStatic(RequestContextManager.class)) {
      mockedStatic.when(RequestContextManager::getContext).thenReturn(requestContext);
      when(requestContext.getApiResource()).thenReturn("WebDetail");
      when(orgService.getAllDeptInfoNameByIds("tenant123", "currentUser", deptIds))
          .thenReturn(deptInfos);

      // 执行被测试方法
      String result = converter.convertFieldData(objectData, departmentFieldDescribe, context);

      // 验证结果
      assertEquals("技术部", result);
      verify(orgService).getAllDeptInfoNameByIds("tenant123", "currentUser", deptIds);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法转换多个部门
   */
  @Test
  @DisplayName("测试convertFieldData - 多个部门转换")
  void testConvertFieldData_MultipleDepartmentConversion() {
    // 准备测试数据
    List<String> deptIds = Arrays.asList("dept1", "dept2", "dept3");
    List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = Arrays.asList(
        createDeptInfo("dept1", "技术部", QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE.getCode()),
        createDeptInfo("dept2", "销售部", QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE.getCode()),
        createDeptInfo("dept3", "人事部", QueryDeptInfoByDeptIds.DeptStatusEnum.DISABLE.getCode())
    );
    
    objectData = createObjectData("department_many_field", deptIds);
    
    try (MockedStatic<RequestContextManager> mockedStatic = mockStatic(RequestContextManager.class)) {
      mockedStatic.when(RequestContextManager::getContext).thenReturn(null);
      when(orgService.getDeptInfoNameByIds("tenant123", "currentUser", deptIds))
          .thenReturn(deptInfos);

      // 执行被测试方法
      String result = converter.convertFieldData(objectData, departmentManyFieldDescribe, context);

      // 验证结果 - 包含已停用部门的标记（I18N可能返回key而不是翻译后的文本）
      assertTrue(result.contains("技术部|销售部|人事部"));
      assertTrue(result.contains("已停用") || result.contains("paas.udobj.has_been_discontinu"));
      verify(orgService).getDeptInfoNameByIds("tenant123", "currentUser", deptIds);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理null值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null值")
  void testConvertFieldData_HandlesNullValue() {
    // 准备测试数据
    objectData = createObjectData("department_field", null);

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, departmentFieldDescribe, context);

    // 验证结果
    assertNull(result);
    verify(orgService, never()).getDeptInfoNameByIds(anyString(), anyString(), anyList());
    verify(orgService, never()).getAllDeptInfoNameByIds(anyString(), anyString(), anyList());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理空列表
   */
  @Test
  @DisplayName("测试convertFieldData - 处理空列表")
  void testConvertFieldData_HandlesEmptyList() {
    // 准备测试数据
    objectData = createObjectData("department_field", Collections.emptyList());

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, departmentFieldDescribe, context);

    // 验证结果
    assertNull(result);
    verify(orgService, never()).getDeptInfoNameByIds(anyString(), anyString(), anyList());
    verify(orgService, never()).getAllDeptInfoNameByIds(anyString(), anyString(), anyList());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理非List类型值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理非List类型值")
  void testConvertFieldData_HandlesNonListValue() {
    // 准备测试数据
    objectData = createObjectData("department_field", "not_a_list");

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, departmentFieldDescribe, context);

    // 验证结果
    assertNull(result);
    verify(orgService, never()).getDeptInfoNameByIds(anyString(), anyString(), anyList());
    verify(orgService, never()).getAllDeptInfoNameByIds(anyString(), anyString(), anyList());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理部门信息为空的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 部门信息为空")
  void testConvertFieldData_EmptyDeptInfos() {
    // 准备测试数据
    List<String> deptIds = Arrays.asList("dept1");
    objectData = createObjectData("department_field", deptIds);
    
    try (MockedStatic<RequestContextManager> mockedStatic = mockStatic(RequestContextManager.class)) {
      mockedStatic.when(RequestContextManager::getContext).thenReturn(null);
      when(orgService.getDeptInfoNameByIds("tenant123", "currentUser", deptIds))
          .thenReturn(Collections.emptyList());

      // 执行被测试方法
      String result = converter.convertFieldData(objectData, departmentFieldDescribe, context);

      // 验证结果
      assertNull(result);
    }
  }

  /**
   * 提供不同API资源的测试数据
   */
  private static Stream<Arguments> provideApiResources() {
    return Stream.of(
        Arguments.of("WebDetail", true),
        Arguments.of("List", true),
        Arguments.of("NewDetail", true),
        Arguments.of("RelatedList", true),
        Arguments.of("DetailList", true),
        Arguments.of("TreeRelatedList", true),
        Arguments.of("SearchList", true),
        Arguments.of("WhatList", true),
        Arguments.of("TreeList", true),
        Arguments.of("PartDetail", true),
        Arguments.of("batchCalculate", true),
        Arguments.of("OtherResource", false),
        Arguments.of(null, false)
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法根据API资源选择不同的服务方法
   */
  @ParameterizedTest
  @MethodSource("provideApiResources")
  @DisplayName("测试convertFieldData - 不同API资源")
  void testConvertFieldData_DifferentApiResources(String apiResource, boolean shouldUseGetAll) {
    // 准备测试数据
    List<String> deptIds = Arrays.asList("dept1");
    List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = Arrays.asList(
        createDeptInfo("dept1", "技术部", QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE.getCode())
    );
    
    objectData = createObjectData("department_field", deptIds);
    
    try (MockedStatic<RequestContextManager> mockedStatic = mockStatic(RequestContextManager.class)) {
      mockedStatic.when(RequestContextManager::getContext).thenReturn(requestContext);
      when(requestContext.getApiResource()).thenReturn(apiResource);
      
      if (shouldUseGetAll) {
        when(orgService.getAllDeptInfoNameByIds("tenant123", "currentUser", deptIds))
            .thenReturn(deptInfos);
      } else {
        when(orgService.getDeptInfoNameByIds("tenant123", "currentUser", deptIds))
            .thenReturn(deptInfos);
      }

      // 执行被测试方法
      String result = converter.convertFieldData(objectData, departmentFieldDescribe, context);

      // 验证结果
      assertEquals("技术部", result);
      
      // 验证调用了正确的服务方法
      if (shouldUseGetAll) {
        verify(orgService).getAllDeptInfoNameByIds("tenant123", "currentUser", deptIds);
        verify(orgService, never()).getDeptInfoNameByIds(anyString(), anyString(), anyList());
      } else {
        verify(orgService).getDeptInfoNameByIds("tenant123", "currentUser", deptIds);
        verify(orgService, never()).getAllDeptInfoNameByIds(anyString(), anyString(), anyList());
      }
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getSupportedFieldTypes方法
   */
  @Test
  @DisplayName("测试getSupportedFieldTypes")
  void testGetSupportedFieldTypes() {
    // 执行被测试方法
    List<String> supportedTypes = converter.getSupportedFieldTypes();

    // 验证结果
    assertNotNull(supportedTypes);
    assertEquals(2, supportedTypes.size());
    assertTrue(supportedTypes.contains(IFieldType.DEPARTMENT));
    assertTrue(supportedTypes.contains(IFieldType.DEPARTMENT_MANY));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试处理不支持的字段类型
   */
  @Test
  @DisplayName("测试不支持的字段类型")
  void testUnsupportedFieldType() {
    // 准备测试数据 - 使用不支持的字段类型
    IFieldDescribe unsupportedField = createDepartmentFieldDescribe("test_field", IFieldType.TEXT);
    objectData = createObjectData("test_field", Arrays.asList("dept1"));

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, unsupportedField, context);

    // 验证结果 - 不支持的字段类型应该返回null
    assertNull(result);
  }

  /**
   * 创建测试用的部门信息
   */
  private QueryDeptInfoByDeptIds.DeptInfo createDeptInfo(String deptId, String deptName, Integer status) {
    QueryDeptInfoByDeptIds.DeptInfo deptInfo = new QueryDeptInfoByDeptIds.DeptInfo();
    deptInfo.setDeptId(deptId);
    deptInfo.setDeptName(deptName);
    deptInfo.setStatus(status);
    return deptInfo;
  }

  /**
   * 创建测试用的部门字段描述
   */
  private IFieldDescribe createDepartmentFieldDescribe(String apiName, String fieldType) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试部门字段");
    fieldMap.put("type", fieldType);
    return FieldDescribeFactory.newInstance(fieldMap);
  }

  /**
   * 创建测试用的对象数据
   */
  private IObjectData createObjectData(String fieldName, Object value) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldName, value);
    return new ObjectData(dataMap);
  }
}
