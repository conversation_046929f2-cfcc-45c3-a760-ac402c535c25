package com.facishare.paas.appframework.metadata.dto.ai;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2025/4/27
 */
public interface GenerateSearchQuery {

    @Data
    class Arg {
        private String sessionId;
        private String userInput;
        private String bindingObjectApiName;
        private String currentSearchQuery;
        private List<AIDto.Message> history;
    }

    @Data
    @Builder
    class Result {
        private String content;
        private String searchQuery;
    }
}
