package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.clean.GetCleanInfo;
import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.GetBasicSetting;
import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.SaveDuplicatedSearch;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.DuplicatedSearchService;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 清洗规则服务类
 *
 * <AUTHOR>
 * @date 2020/4/21 4:12 下午
 */
@ServiceModule("clean")
@Service
@Slf4j
public class ObjectCleanService {

    /**
     * 清洗规则和查重同用一套接口
     */
    @Autowired
    private DuplicatedSearchService duplicatedSearchService;

    @Autowired
    private DescribeLogicService describeLogicService;

    /**
     * 保存清洗规则
     * 当前清洗规则与查重规则基本一致
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("save")
    public SaveDuplicatedSearch.Result save(SaveDuplicatedSearch.Arg arg, ServiceContext context) {
        IDuplicatedSearch duplicateSearchInfo = arg.getDuplicateSearch();
        duplicateSearchInfo.setTenantId(context.getTenantId());
        duplicateSearchInfo.setOperatorId(context.getUser().getUserId());
        duplicateSearchInfo.setEffective(false);
        IDuplicatedSearch duplicatedSearch = duplicatedSearchService.createOrUpdateCleanRule(context.getUser(), duplicateSearchInfo, false);
        return SaveDuplicatedSearch.Result.builder().duplicateSearch(duplicatedSearch).build();
    }

    /**
     * 查询清洗规则
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("getCleanRule")
    @Transactional
    public GetCleanInfo.Result GetCleanInfo(GetCleanInfo.Arg arg, ServiceContext context) {

        IObjectDescribe objectDescribe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        List<String> fieldApiNameList = objectDescribe.getFieldDescribes().stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
        Map<IDuplicatedSearch.Type, IDuplicatedSearch> duplicatedSearchMap =
                duplicatedSearchService.findDuplicatedSearchByApiName(context.getUser(), arg.getDescribeApiName(), true);
        IDuplicatedSearch cleanRule = duplicatedSearchMap.get(IDuplicatedSearch.Type.CLEAN);
//        if (Objects.isNull(cleanRule)) {
//            cleanRule = duplicatedSearchService.presetDuplicateRule(arg.getDescribeApiName(), IDuplicatedSearch.Type.CLEAN, context.getUser());
//        }
        GetCleanInfo.Rules.RulesBuilder newRules = GetCleanInfo.Rules.builder();
        String id = cleanRule.getId();
        int version = cleanRule.getVersion();
        CollectionUtils.nullToEmpty(cleanRule.getPendingRules().getShowFields()).removeIf(x -> !fieldApiNameList.contains(x));
        newRules.enable(cleanRule.isEnable())
                .type(IDuplicatedSearch.Type.CLEAN)
                .name(cleanRule.getName())
                .ruleApiName(cleanRule.getRuleApiName())
                .pendingRules(cleanRule.getPendingRules());
        return GetCleanInfo.Result.builder()
                .cleanRule(newRules.build())
                .id(id)
                .describeApiName(arg.getDescribeApiName())
                .version(version)
                .build();
    }


}
