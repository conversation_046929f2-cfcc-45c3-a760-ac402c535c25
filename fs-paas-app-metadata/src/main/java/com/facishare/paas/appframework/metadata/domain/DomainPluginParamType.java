package com.facishare.paas.appframework.metadata.domain;

import java.util.Arrays;
import java.util.Objects;

/**
 * Created by zhouwr on 2021/11/9.
 */
public enum DomainPluginParamType {
    FieldMapping("fieldMapping"), DetailMapping("detailMapping"), Custom("custom");

    DomainPluginParamType(String code) {
        this.code = code;
    }

    String code;

    public static DomainPluginParamType of(String code) {
        return Arrays.stream(values()).filter(x -> Objects.equals(code, x.code)).findFirst().orElse(null);
    }
}
