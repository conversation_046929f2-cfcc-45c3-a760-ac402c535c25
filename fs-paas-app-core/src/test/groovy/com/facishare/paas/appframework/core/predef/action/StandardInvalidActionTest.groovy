package com.facishare.paas.appframework.core.predef.action


import com.facishare.paas.appframework.core.model.ActionContext
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.common.util.BulkOpResult
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import spock.lang.Specification

class StandardInvalidActionTest extends Specification {

    def invalidAction = new StandardInvalidAction(
            serviceFacade: Mock(ServiceFacade),
            actionContext: new ActionContext(RequestContext.builder()
                    .tenantId(tenantId)
                    .user(new User("testTenantId", User.SUPPER_ADMIN_USER_ID))
                    .build(), "testObjectApiName", "testInvalid"),
            objectDataList: [getObjectData()],
            objectDescribe: getObjectDescribe(),
            bulkOpResult: BulkOpResult.builder()
                    .successObjectDataList([])
                    .failObjectDataList([])
                    .failReason("")
                    .build(),
            needTriggerInvalidAfterActionDataList: [],
            objectDescribeMap: [:]
    )

    String tenantId = "71698"

    def setup() {
    }

    def "test initObjectDataList"() {
        expect:
        1 == 1
        /*
        given:
        invalidAction.arg = new StandardInvalidAction.Arg()
        when:
        invalidAction.initObjectDataList()
        then:
        1 * invalidAction.serviceFacade.findObjectData(_, _, _)
        thrown(MetaDataBusinessException)
    }

    def "test before"() {
        expect:
        1 == 1
        /*
        given:
        invalidAction.actionContext.attributes.put(RequestContext.Attributes.SKIP_BASE_VALIDATE, Boolean.TRUE)
        StandardInvalidAction.Arg arg = StandardInvalidAction.Arg.builder().objectDataId("testObjectId").build()
        invalidAction.arg = StandardInvalidAction.Arg.builder().objectDataId("testObjectId").build()
        when:
        invalidAction.before(arg)
        then:
        1 * invalidAction.serviceFacade.findObject(_, _) >> getObjectDescribe()
        1 * invalidAction.serviceFacade.findObjectDataByIds(_, _, _) >> [getObjectData()]
        1 * invalidAction.serviceFacade.findObjectData(_, _, _) >> getObjectData()

        assert invalidAction.objectDescribeMap.size() == 1
        assert invalidAction.objectDataList.size() == 1

        noExceptionThrown()
        */
    }

    def "test validateObjectApprovalFlow"() {
        expect:
        1 == 1
        /*
        given:
        invalidAction.serviceFacade.batchGetApprovalStatusOfObject(_, _) >> ["testObjectId": ApprovalFlowStatus.of("in_progress")]
        when:
        invalidAction.validateObjectApprovalFlow()
        then:
        thrown(ValidateException)
        */
    }

    def "test mergeFailedOpResult"() {
        expect:
        1 == 1
        /*
        when:
        invalidAction.mergeFailedOpResult([getObjectData()], [], "test mergeFailedOpResult")
        then:
        assert invalidAction.bulkOpResult.failReason == "test mergeFailedOpResult"
        assert invalidAction.bulkOpResult.failObjectDataList.size() == 1
        noExceptionThrown()
        */
    }

    def "test arg"() {
        expect:
        1 == 1
        /*
        given:
        StandardInvalidAction.Arg arg = StandardInvalidAction.Arg.builder()
                .objectDataId("testObjectId")
                .build()
        invalidAction.actionContext.attributes.put(RequestContext.Attributes.TRIGGER_FLOW, Boolean.FALSE)
        when:
        invalidAction.doAct(arg)
        then:
        1 * invalidAction.serviceFacade.bulkInvalid(_, _) >> [getObjectData()]
        1 * invalidAction.serviceFacade.findDetailDescribes(_, _) >> [getObjectDescribe()]
        noExceptionThrown()
        */
    }

    ObjectData getObjectData() {
        return new ObjectData([
                "is_active"               : Boolean.TRUE,
                "api_name"                : "testAPIName",
                "_id"                     : "testObjectId",
                "name"                    : "testObjectName",
                "object_describe_api_name": "testDescribeAPIName"
        ])
    }

    ObjectDescribe getObjectDescribe() {
        return new ObjectDescribe(["is_active": Boolean.TRUE, "api_name": "testDescribeAPIName"])
    }

    BulkOpResult getOpResult() {
        BulkOpResult.builder()
                .successObjectDataList([])
                .failObjectDataList([])
                .failReason("")
                .build()
    }


}
