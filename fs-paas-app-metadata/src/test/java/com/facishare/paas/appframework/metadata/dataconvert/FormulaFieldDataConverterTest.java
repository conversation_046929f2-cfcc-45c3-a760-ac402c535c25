package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.appframework.common.util.DateTimeUtils;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.multiRegion.MultiRegionDateTimeFormatUtils;
import com.facishare.paas.timezone.TimeZoneContext;
import com.ibm.icu.util.ULocale;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试FormulaFieldDataConverter类的公式字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class FormulaFieldDataConverterTest {

  @Mock
  private IObjectDataProxyService dataService;

  @Mock
  private SessionContext sessionContext;

  private FormulaFieldDataConverter converter;
  private IFieldDescribe fieldDescribe;
  private IObjectData objectData;

  @BeforeEach
  void setUp() {
    converter = new FormulaFieldDataConverter();
    
    // 设置转换器的属性
    converter.setDataService(dataService);
    
    // 设置SessionContext的默认行为
    lenient().when(sessionContext.getRegion()).thenReturn("");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法转换文本类型公式结果
   */
  @Test
  @DisplayName("测试convertFieldData - 文本类型公式结果")
  void testConvertFieldData_TextFormulaResult() throws Exception {
    // 准备测试数据 - 文本类型返回值
    fieldDescribe = createFormulaFieldDescribe("formula_field", IFieldType.TEXT);
    String formulaResult = "计算结果文本";
    objectData = createObjectData("formula_field", formulaResult);
    
    converter.setFieldDescribe(fieldDescribe);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals(formulaResult, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法转换数字类型公式结果
   */
  @Test
  @DisplayName("测试convertFieldData - 数字类型公式结果")
  void testConvertFieldData_NumberFormulaResult() throws Exception {
    // 准备测试数据 - 数字类型返回值
    fieldDescribe = createFormulaFieldDescribe("formula_field", IFieldType.NUMBER);
    BigDecimal formulaResult = new BigDecimal("123.45");
    objectData = createObjectData("formula_field", formulaResult);
    
    converter.setFieldDescribe(fieldDescribe);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("123.45", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法转换日期类型公式结果（非灰度模式）
   */
  @Test
  @DisplayName("测试convertFieldData - 日期类型公式结果（非灰度模式）")
  void testConvertFieldData_DateFormulaResult() throws Exception {
    // 准备测试数据 - 日期类型返回值
    fieldDescribe = createFormulaFieldDescribe("formula_field", IFieldType.DATE);
    Long dateValue = 1640995200000L; // 2022-01-01
    objectData = createObjectData("formula_field", dateValue);
    
    converter.setFieldDescribe(fieldDescribe);
    converter.setObjectData(objectData);

    try (MockedStatic<DateTimeUtils> mockedDateTimeUtils = mockStatic(DateTimeUtils.class);
         MockedStatic<ObjectDataExt> mockedObjectDataExt = mockStatic(ObjectDataExt.class)) {
      
      mockedDateTimeUtils.when(DateTimeUtils::isGrayTimeZone).thenReturn(false);
      mockedObjectDataExt.when(() -> ObjectDataExt.parseVale(dateValue, IFieldType.DATE))
          .thenReturn("2022-01-01");

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果
      assertEquals("2022-01-01", result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法转换日期时间类型公式结果（灰度模式）
   */
  @Test
  @DisplayName("测试convertFieldData - 日期时间类型公式结果（灰度模式）")
  void testConvertFieldData_DateTimeFormulaResultGrayMode() throws Exception {
    // 准备测试数据 - 日期时间类型返回值
    fieldDescribe = createFormulaFieldDescribe("formula_field", IFieldType.DATE_TIME);
    Long dateTimeValue = 1640995200000L; // 2022-01-01 00:00:00
    objectData = createObjectData("formula_field", dateTimeValue);

    converter.setFieldDescribe(fieldDescribe);
    converter.setObjectData(objectData);

    // 设置SessionContext的时区信息
    when(sessionContext.getTimeZone()).thenReturn(ZoneId.of("Asia/Shanghai"));

    try (MockedStatic<DateTimeUtils> mockedDateTimeUtils = mockStatic(DateTimeUtils.class)) {
      mockedDateTimeUtils.when(DateTimeUtils::isGrayTimeZone).thenReturn(true);

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果 - 灰度模式会调用format方法，这里主要验证不抛异常
      assertNotNull(result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法转换时间类型公式结果（多区域模式）
   */
  @Test
  @DisplayName("测试convertFieldData - 时间类型公式结果（多区域模式）")
  void testConvertFieldData_TimeFormulaResultMultiRegion() throws Exception {
    // 准备测试数据 - 时间类型返回值
    fieldDescribe = createFormulaFieldDescribe("formula_field", IFieldType.TIME);
    Long timeValue = 3600000L; // 01:00:00
    objectData = createObjectData("formula_field", timeValue);
    
    converter.setFieldDescribe(fieldDescribe);
    converter.setObjectData(objectData);

    // 设置多区域模式
    when(sessionContext.getRegion()).thenReturn("zh_CN");

    try (MockedStatic<DateTimeUtils> mockedDateTimeUtils = mockStatic(DateTimeUtils.class);
         MockedStatic<MultiRegionDateTimeFormatUtils> mockedMultiRegion = mockStatic(MultiRegionDateTimeFormatUtils.class)) {
      
      mockedDateTimeUtils.when(DateTimeUtils::isGrayTimeZone).thenReturn(false);
      mockedMultiRegion.when(() -> MultiRegionDateTimeFormatUtils.formatForRegion(
          timeValue, TimeZoneContext.DEFAULT_TIME_ZONE, IFieldType.TIME, new ULocale("zh_CN")))
          .thenReturn("01:00:00");

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果
      assertEquals("01:00:00", result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理null值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null值")
  void testConvertFieldData_HandlesNullValue() throws Exception {
    // 准备测试数据
    fieldDescribe = createFormulaFieldDescribe("formula_field", IFieldType.TEXT);
    objectData = createObjectData("formula_field", null);
    
    converter.setFieldDescribe(fieldDescribe);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * 提供不同类型的公式结果数据测试
   */
  private static Stream<Arguments> provideFormulaResultData() {
    return Stream.of(
        Arguments.of("文本结果", IFieldType.TEXT, "文本结果"),
        Arguments.of(123, IFieldType.NUMBER, "123"),
        Arguments.of(new BigDecimal("456.78"), IFieldType.CURRENCY, "456.78"),
        Arguments.of(true, IFieldType.TRUE_OR_FALSE, "true"),
        Arguments.of(null, IFieldType.TEXT, "")
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理不同类型的公式结果
   */
  @ParameterizedTest
  @MethodSource("provideFormulaResultData")
  @DisplayName("测试convertFieldData - 不同类型的公式结果")
  void testConvertFieldData_DifferentFormulaResults(Object inputValue, String returnType, String expectedResult) throws Exception {
    // 准备测试数据
    fieldDescribe = createFormulaFieldDescribe("formula_field", returnType);
    objectData = createObjectData("formula_field", inputValue);
    
    converter.setFieldDescribe(fieldDescribe);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals(expectedResult, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理字段不存在的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 字段不存在")
  void testConvertFieldData_FieldNotExists() throws Exception {
    // 准备测试数据 - 创建不包含目标字段的数据
    fieldDescribe = createFormulaFieldDescribe("formula_field", IFieldType.TEXT);
    objectData = createObjectData("other_field", "some_value");
    
    converter.setFieldDescribe(fieldDescribe);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 字段不存在时应该返回空字符串
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试转换器的基本属性设置
   */
  @Test
  @DisplayName("测试转换器属性设置")
  void testConverterProperties() {
    // 验证属性设置
    assertEquals(dataService, converter.getDataService());
  }

  /**
   * 创建测试用的公式字段描述
   */
  private IFieldDescribe createFormulaFieldDescribe(String apiName, String returnType) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试公式字段");
    fieldMap.put("type", IFieldType.FORMULA);
    fieldMap.put("return_type", returnType);
    return FieldDescribeFactory.newInstance(fieldMap);
  }

  /**
   * 创建测试用的对象数据
   */
  private IObjectData createObjectData(String fieldName, Object value) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldName, value);
    return new ObjectData(dataMap);
  }
}
