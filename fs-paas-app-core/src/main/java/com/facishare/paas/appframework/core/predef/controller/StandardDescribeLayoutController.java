package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController.Arg;
import com.facishare.paas.appframework.core.predef.listener.prm.PrmDescribeLayoutControllerListener;
import com.facishare.paas.appframework.metadata.dto.AggregatedObjDescribeLayoutResult;
import com.facishare.paas.appframework.metadata.dto.DetailObjectListResult;
import com.facishare.paas.appframework.metadata.dto.RefObjectDescribeListResult;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2017/11/7
 */
public class StandardDescribeLayoutController extends AbstractStandardDescribeLayoutController<Arg> {

    @Override
    public List<Class<? extends ControllerListener<Arg, Result>>> getControllerListenerClassList() {
        List<Class<? extends ControllerListener<Arg, Result>>> controllerListenerClassList = super.getControllerListenerClassList();
        if (AppIdMapping.isPRM(controllerContext.getAppId())) {
            controllerListenerClassList.add(PrmDescribeLayoutControllerListener.class);
        }
        return controllerListenerClassList;
    }

    @Data
    public static class Arg {
        @JSONField(name = "M1")
        String apiname;

        @JSONField(name = "M2")
        private Boolean include_layout = Boolean.TRUE;

        @JSONField(name = "M3")
        private String layout_type;

        @JSONField(name = "M4")
        private String recordType_apiName;

        @Deprecated
        @JSONField(name = "M5")
        private Boolean include_ref_describe = Boolean.FALSE;

        @JSONField(name = "M6")
        private Boolean include_detail_describe = Boolean.FALSE;

        @JSONField(name = "M7")
        private String data_id;

        @JSONField(name = "M8")
        private Boolean include_all_detail_describe = Boolean.FALSE;

        //是否返回describe
        private Boolean include_describe = Boolean.TRUE;

        //布局适用端(web或mobile)
        private String layoutAgentType;

        //端上是否缓存describe
        private Boolean describeCacheable;
        //端上缓存的describe的版本号
        private Map<String, Integer> describeVersionMap;
        //是否计算默认值
        private Boolean calculateExpression;

        @JSONField(name = "relation_field_api_name")
        @JsonProperty("relation_field_api_name")
        private String relationFieldApiName;

        @JSONField(name = "_source")
        @JsonProperty("_source")
        private String source;

        //布局apiName（流程布局使用）
        private String layoutApiName;

        /**
         * change_order 表示为变更单的编辑操作
         */
        @JSONField(name = "action_type")
        @JsonProperty("action_type")
        private String actionType;

        @JSONField(name = "include_active_detail")
        @JsonProperty("include_active_detail")
        private Boolean includeActiveDetail;

        @JSONField(name = "include_plugin")
        @JsonProperty("include_plugin")
        private Boolean includePlugin;


        public boolean isIncludeAllDetailDescribe() {
            return Boolean.TRUE.equals(include_all_detail_describe);
        }

        public boolean isIncludeDetailDescribe() {
            return Boolean.TRUE.equals(include_detail_describe);
        }

        public boolean isIncludeActiveDetail() {
            return Boolean.TRUE.equals(includeActiveDetail);
        }

        public boolean isIncludeDescribe() {
            return !Boolean.FALSE.equals(include_describe);
        }

        public boolean isIncludeLayout() {
            return !Boolean.FALSE.equals(include_layout);
        }

        public boolean describeCacheable() {
            return Boolean.TRUE.equals(describeCacheable);
        }

        public boolean calculateExpression() {
            return !Boolean.FALSE.equals(calculateExpression);
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        @JSONField(name = "M5")
        private LayoutDocument layout;

        @JSONField(name = "M1")
        private ObjectDescribeDocument objectDescribe;

        @JSONField(name = "M3")
        private List<RefObjectDescribeListResult> refObjectDescribeList;

        @JSONField(name = "M6")
        private List<QueryTemplateDocument> templates;

        @JSONField(name = "M7")
        private List<DetailObjectListResult> detailObjectList;

        @JSONField(name = "M8")
        private ObjectDataDocument objectData;

        private ObjectDataDocument maskData;

        @JSONField(name = "M9")
        private List fieldList;

        @Deprecated
        @JSONField(name = "M10")
        private List<DetailObjectListResult> allDetailObjectList;

        @JSONField(name = "M11")
        private List<AggregatedObjDescribeLayoutResult> aggregatedObjDescribeLayoutList;

        @JsonProperty("support_draft")
        private boolean supportDraft;

        @JsonProperty("support_edit_draft")
        private boolean supportEditDraft;

        private boolean supportSaveAndCreate;

        private boolean supportAiUserLicense;

        private ObjectDescribeDocument objectDescribeExt;

        private StandardPluginListController.Result plugin;
    }
}
