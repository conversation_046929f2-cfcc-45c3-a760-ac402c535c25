package com.facishare.paas.appframework.metadata

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.config.ConfigService
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.dto.ImportReferenceMapping
import com.facishare.paas.metadata.api.describe.IFieldType
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import spock.lang.Specification
import spock.lang.Unroll

class ImportServiceImplTest extends Specification {

    ImportServiceImpl importService
    ConfigService configService
    DescribeLogicService describeLogicService

    def setup() {
        importService = new ImportServiceImpl()
        configService = Mock(ConfigService)
        describeLogicService = Mock(DescribeLogicService)
        importService.configService = configService
        importService.describeLogicService = describeLogicService
    }

    @Unroll
    def "test findImportReferenceMapping when switch=#switchEnabled, needLabel=#needLabel"() {
        given: "准备测试数据"
        def user = User.systemUser()
        def describeApiName = "AccountObj"
        def importReferenceMapping = new ImportReferenceMapping(
                referenceFieldMappingSwitch: switchEnabled,
                referenceFieldMapping: referenceFieldMappings
        )

        and: "mock configService"
        configService.findTenantConfig(user, ImportReferenceMapping.buildImportReferenceMappingKey(describeApiName)) >>
                JSON.toJSONString(importReferenceMapping)

        and: "mock describeLogicService"
        if (switchEnabled && needLabel) {
            def mainDescribe = createMainObjectDescribe()
            def contactDescribe = createContactObjectDescribe()

            describeLogicService.findObjectWithoutCopy(user.tenantId, describeApiName) >> mainDescribe
            describeLogicService.findDetailDescribesWithoutCopy(user.tenantId, describeApiName) >> []
            describeLogicService.findObjectsWithoutCopy(user.tenantId, _) >> [
                    (contactDescribe.apiName): contactDescribe
            ]
        }

        when: "调用方法"
        def result = importService.findImportReferenceMapping(user, describeApiName, needLabel)

        then: "验证结果"
        result != null
        result.referenceFieldMappingSwitch == switchEnabled
        result.referenceFieldMapping.size() == expectedSize
        if (switchEnabled && needLabel && expectedSize > 0) {
            with(result.referenceFieldMapping[0]) {
                objectLabel == "客户"
                targetObjectLabel == "联系人"
                objectReferenceFieldLabel == "联系人"
                specifiedUniqueFieldLabel == "姓名"
            }
        }

        where: "测试数据"
        switchEnabled | needLabel | referenceFieldMappings                    | expectedSize
        false         | true      | []                                        | 0
        true          | false     | createReferenceFieldMappings()            | 1
        true          | true      | createReferenceFieldMappings()            | 1
        true          | true      | []                                        | 0
    }

    private IObjectDescribe createMainObjectDescribe() {
        def describe = new ObjectDescribe()
        describe.setApiName("AccountObj")
        describe.setDisplayName("客户")
        describe.setActive(true)

        def lookupField = FieldDescribeFactory.newInstance([
                "api_name": "contactField",
                "type": IFieldType.LOOKUP,
                "label": "联系人",
                "active": true,
                "target_api_name": "ContactObj"
        ])
        describe.setFieldDescribes([lookupField])
        return describe
    }

    private IObjectDescribe createContactObjectDescribe() {
        def describe = new ObjectDescribe()
        describe.setApiName("ContactObj")
        describe.setDisplayName("联系人")
        describe.setActive(true)

        def nameField = FieldDescribeFactory.newInstance([
                "api_name": "name",
                "type": IFieldType.TEXT,
                "label": "姓名",
                "active": true,
                "unique": true
        ])
        describe.setFieldDescribes([nameField])
        return describe
    }

    private List<ImportReferenceMapping.ReferenceFieldMapping> createReferenceFieldMappings() {
        return [
                new ImportReferenceMapping.ReferenceFieldMapping(
                        objectApiName: "AccountObj",
                        objectReferenceFieldApiName: "contactField",
                        targetObjectApiName: "ContactObj",
                        specifiedUniqueFieldApiName: "name",
                        hasConfig: true
                )
        ]
    }
}