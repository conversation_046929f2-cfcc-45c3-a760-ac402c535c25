package com.facishare.paas.appframework.metadata.layout.factory;

import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class BaseLayoutFactoryTest {

  @Mock
  private IComponentFactoryManager componentFactoryManager;

  @Mock
  private IComponentFactory componentFactory;

  private BaseLayoutFactory baseLayoutFactory;

  @BeforeEach
  void setUp() {
    // 创建一个具体的BaseLayoutFactory实现用于测试
    baseLayoutFactory = new BaseLayoutFactory(componentFactoryManager) {
      @Override
      public String supportLayoutType() {
        return "test";
      }

      @Override
      public ILayout generateDefaultLayout(Context context, IObjectDescribe describe) {
        return mock(ILayout.class);
      }
    };
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试BaseLayoutFactory构造函数正常场景
   */
  @Test
  @DisplayName("正常场景 - 构造BaseLayoutFactory对象")
  void testBaseLayoutFactoryConstructor_Success() {
    // 验证结果
    assertNotNull(baseLayoutFactory);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getComponentFactory方法正常场景
   */
  @Test
  @DisplayName("正常场景 - 获取组件工厂")
  void testGetComponentFactory_Success() {
    // 准备测试数据
    String factoryType = "test_factory";
    when(componentFactoryManager.getFactory(factoryType)).thenReturn(componentFactory);

    // 执行被测试方法
    IComponentFactory result = baseLayoutFactory.getComponentFactory(factoryType);

    // 验证结果
    assertNotNull(result);
    assertSame(componentFactory, result);
    verify(componentFactoryManager).getFactory(factoryType);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getComponentFactory方法返回null的场景
   */
  @Test
  @DisplayName("边界场景 - 获取不存在的组件工厂")
  void testGetComponentFactory_NotFound() {
    // 准备测试数据
    String factoryType = "non_existent_factory";
    when(componentFactoryManager.getFactory(factoryType)).thenReturn(null);

    // 执行被测试方法
    IComponentFactory result = baseLayoutFactory.getComponentFactory(factoryType);

    // 验证结果
    assertNull(result);
    verify(componentFactoryManager).getFactory(factoryType);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getComponentFactory方法传入null参数
   */
  @Test
  @DisplayName("边界场景 - 传入null参数")
  void testGetComponentFactory_NullParameter() {
    // 准备测试数据
    when(componentFactoryManager.getFactory(null)).thenReturn(null);

    // 执行被测试方法
    IComponentFactory result = baseLayoutFactory.getComponentFactory(null);

    // 验证结果
    assertNull(result);
    verify(componentFactoryManager).getFactory(null);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试抽象方法的实现
   */
  @Test
  @DisplayName("正常场景 - 测试抽象方法实现")
  void testAbstractMethods_Implementation() {
    // 测试supportLayoutType方法
    String layoutType = baseLayoutFactory.supportLayoutType();
    assertNotNull(layoutType);
    assertEquals("test", layoutType);

    // 测试generateDefaultLayout方法
    ILayoutFactory.Context context = ILayoutFactory.Context.of(null, null);
    ILayout layout = baseLayoutFactory.generateDefaultLayout(context, null);
    assertNotNull(layout);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试基本功能验证
   */
  @Test
  @DisplayName("正常场景 - 验证基本功能")
  void testBaseLayoutFactory_BasicFunctionality() {
    // 验证基本功能
    assertNotNull(baseLayoutFactory);
    assertDoesNotThrow(() -> {
      baseLayoutFactory.toString();
    });
    assertDoesNotThrow(() -> {
      baseLayoutFactory.hashCode();
    });
    assertDoesNotThrow(() -> {
      baseLayoutFactory.equals(baseLayoutFactory);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试多次调用getComponentFactory的一致性
   */
  @Test
  @DisplayName("正常场景 - 测试多次调用的一致性")
  void testGetComponentFactory_MultipleCallsConsistency() {
    // 准备测试数据
    String factoryType = "test_factory";
    when(componentFactoryManager.getFactory(factoryType)).thenReturn(componentFactory);

    // 执行被测试方法
    IComponentFactory result1 = baseLayoutFactory.getComponentFactory(factoryType);
    IComponentFactory result2 = baseLayoutFactory.getComponentFactory(factoryType);

    // 验证结果
    assertNotNull(result1);
    assertNotNull(result2);
    assertSame(result1, result2);
    verify(componentFactoryManager, times(2)).getFactory(factoryType);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试异常处理能力
   */
  @Test
  @DisplayName("异常场景 - 测试异常处理")
  void testBaseLayoutFactory_ExceptionHandling() {
    // 准备测试数据
    String factoryType = "exception_factory";
    when(componentFactoryManager.getFactory(factoryType)).thenThrow(new RuntimeException("Test exception"));

    // 执行被测试方法并验证异常
    assertThrows(RuntimeException.class, () -> {
      baseLayoutFactory.getComponentFactory(factoryType);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试构造函数null参数
   */
  @Test
  @DisplayName("边界场景 - 构造函数null参数")
  void testBaseLayoutFactoryConstructor_NullParameter() {
    // 测试构造函数接受null参数
    assertDoesNotThrow(() -> {
      BaseLayoutFactory factory = new BaseLayoutFactory(null) {
        @Override
        public String supportLayoutType() {
          return "test";
        }

        @Override
        public ILayout generateDefaultLayout(Context context, IObjectDescribe describe) {
          return mock(ILayout.class);
        }
      };
      assertNotNull(factory);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试不同工厂类型的处理
   */
  @Test
  @DisplayName("正常场景 - 测试不同工厂类型")
  void testGetComponentFactory_DifferentTypes() {
    // 准备测试数据
    IComponentFactory factory1 = mock(IComponentFactory.class);
    IComponentFactory factory2 = mock(IComponentFactory.class);
    
    when(componentFactoryManager.getFactory("type1")).thenReturn(factory1);
    when(componentFactoryManager.getFactory("type2")).thenReturn(factory2);

    // 执行被测试方法
    IComponentFactory result1 = baseLayoutFactory.getComponentFactory("type1");
    IComponentFactory result2 = baseLayoutFactory.getComponentFactory("type2");

    // 验证结果
    assertNotNull(result1);
    assertNotNull(result2);
    assertNotSame(result1, result2);
    assertSame(factory1, result1);
    assertSame(factory2, result2);
  }
}
