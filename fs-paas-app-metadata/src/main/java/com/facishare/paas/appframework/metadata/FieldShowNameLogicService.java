package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.FieldRecordShowName;
import com.facishare.paas.appframework.metadata.repository.model.FieldShowName;

import java.util.List;
import java.util.Map;

public interface FieldShowNameLogicService {


    List<FieldShowName> findFieldShowNamesByApiName(User user, String describeApiName);

    /**
     * 多语翻译平台使用的接口
     */
    List<FieldShowName> findFieldShowNamesByApiNameAndRecordApiName(User user, String describeApiName, String recordApiName);

    void upsertFieldShowName(User user, String describeApiName, String recordApiName, List<FieldShowName> fieldShowNames);


    Map<String, Object> findRecordFieldMapping(User user, List<FieldRecordShowName> fieldRecordShowNames);
}
