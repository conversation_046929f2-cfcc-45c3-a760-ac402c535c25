package com.facishare.paas.appframework.log

import org.springframework.context.ApplicationContext
import spock.lang.Specification

/**
 * GenerateByAI
 * Test class for ModifyRecordInitManager
 */
class ModifyRecordInitManagerTest extends Specification {
    ModifyRecordInitManager modifyRecordInitManager
    ApplicationContext applicationContext
    DefaultModifyRecordProvider defaultModifyRecordProvider
    ObjectInfoModelModifyRecordProvider objectInfoModelProvider
    FlowCompletedModifyRecordProvider flowCompletedProvider
    CreateModifyRecordProvider createModifyRecordProvider

    void setup() {
        modifyRecordInitManager = new ModifyRecordInitManager()
        applicationContext = Mock(ApplicationContext)
        defaultModifyRecordProvider = Mock(DefaultModifyRecordProvider)
        objectInfoModelProvider = Mock(ObjectInfoModelModifyRecordProvider)
        flowCompletedProvider = Mock(FlowCompletedModifyRecordProvider)
        createModifyRecordProvider = Mock(CreateModifyRecordProvider)

        modifyRecordInitManager.defaultModifyRecordProvider = defaultModifyRecordProvider
    }

    /**
     * GenerateByAI
     * Test initialization of providers through ApplicationContext
     */
    def "testInitializationTest"() {
        given:
        Map<String, ModifyRecordInitProvider> providerMap = [
            'Default': defaultModifyRecordProvider,
            'ObjectInfoModel': objectInfoModelProvider,
            'approvalFlowInfoList': flowCompletedProvider,
            'Add': createModifyRecordProvider
        ]

        defaultModifyRecordProvider.getApiName() >> 'Default'
        objectInfoModelProvider.getApiName() >> 'ObjectInfoModel'
        flowCompletedProvider.getApiName() >> 'approvalFlowInfoList'
        createModifyRecordProvider.getApiName() >> 'Add'

        applicationContext.getBeansOfType(ModifyRecordInitProvider) >> providerMap

        when:
        modifyRecordInitManager.setApplicationContext(applicationContext)

        then:
        noExceptionThrown()
    }

    /**
     * GenerateByAI
     * Test getting provider for AddEmployee action type
     */
    def "testGetProviderForAddEmployeeTest"() {
        given:
        Map<String, ModifyRecordInitProvider> providerMap = [
            (ActionType.AddEmployee.getId()): createModifyRecordProvider
        ]
        createModifyRecordProvider.getApiName() >> ActionType.AddEmployee.getId()
        applicationContext.getBeansOfType(ModifyRecordInitProvider) >> providerMap
        modifyRecordInitManager.setApplicationContext(applicationContext)

        when:
        def result = modifyRecordInitManager.getProvider(ActionType.AddEmployee.getId())

        then:
        result == createModifyRecordProvider
    }

    /**
     * GenerateByAI
     * Test getting provider for ObjectInfoModel action types
     */
    def "testGetProviderForObjectInfoModelTest"() {
        given:
        Map<String, ModifyRecordInitProvider> providerMap = [
            'ObjectInfoModel': objectInfoModelProvider
        ]
        objectInfoModelProvider.getApiName() >> 'ObjectInfoModel'
        applicationContext.getBeansOfType(ModifyRecordInitProvider) >> providerMap
        modifyRecordInitManager.setApplicationContext(applicationContext)

        when:
        def result = modifyRecordInitManager.getProvider(ActionType.Invalid.getId())

        then:
        result == objectInfoModelProvider
    }

    /**
     * GenerateByAI
     * Test getting provider for approval flow action types
     */
    def "testGetProviderForApprovalFlowTest"() {
        given:
        Map<String, ModifyRecordInitProvider> providerMap = [
            'approvalFlowInfoList': flowCompletedProvider
        ]
        flowCompletedProvider.getApiName() >> 'approvalFlowInfoList'
        applicationContext.getBeansOfType(ModifyRecordInitProvider) >> providerMap
        modifyRecordInitManager.setApplicationContext(applicationContext)

        when:
        def result = modifyRecordInitManager.getProvider(ActionType.SFA_WORK_FLOW_COMPLETE.getId())

        then:
        result == flowCompletedProvider
    }

    /**
     * GenerateByAI
     * Test getting default provider for unknown action type
     */
    def "testGetDefaultProviderTest"() {
        given:
        Map<String, ModifyRecordInitProvider> providerMap = [:]
        applicationContext.getBeansOfType(ModifyRecordInitProvider) >> providerMap
        modifyRecordInitManager.setApplicationContext(applicationContext)

        when:
        def result = modifyRecordInitManager.getProvider("UNKNOWN_ACTION")

        then:
        result == defaultModifyRecordProvider
    }
}
