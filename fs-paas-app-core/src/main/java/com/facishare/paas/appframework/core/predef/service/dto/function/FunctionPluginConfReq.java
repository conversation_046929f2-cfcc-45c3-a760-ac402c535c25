package com.facishare.paas.appframework.core.predef.service.dto.function;

import com.facishare.paas.appframework.metadata.repository.model.MtFunctionPluginConf;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 保存功能插件配置
 */
public class FunctionPluginConfReq {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg {
        private MtFunctionPluginConf config;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FindArg {
        private String refObjApiName;
        private String apiName;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchArg {
        private List<MtFunctionPluginConf> configs;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Result {
        private MtFunctionPluginConf config;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class BatchResult {
        private List<MtFunctionPluginConf> configs;
    }
} 