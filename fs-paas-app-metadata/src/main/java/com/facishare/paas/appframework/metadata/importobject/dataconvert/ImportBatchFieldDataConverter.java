package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Delegate;

import java.util.List;

/**
 * create by z<PERSON><PERSON> on 2019/07/27
 */
public interface ImportBatchFieldDataConverter {

    /**
     * 转换器支持的字段类型
     *
     * @return
     */
    List<String> getSupportedFieldTypes();

    /**
     * 将指定字段的数据转成前端展现的格式
     *
     * @param dataList      数据集合
     * @param fieldDescribe 字段描述
     * @param user          用户信息
     */
    List<BatchConvertResult> convertFieldData(List<BatchConvertData> dataList, IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe, User user);

    List<BatchConvertResult> convertFieldData(List<BatchConvertData> dataList, IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe, DataConverterContext context);

    @Data
    @Builder
    class DataConverterContext {
        @Delegate
        private User user;
        /**
         * 转换部门的时候需要考虑部门的状态
         */
        private QueryDeptInfoByDeptIds.DeptStatusEnum convertDeptWithStatus;
    }
}
