package com.facishare.paas.appframework.core.util;

import com.facishare.paas.appframework.core.exception.NotElementPresentException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Collection;
import java.util.function.Function;
import java.util.function.Supplier;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;

public final class ObjectUtils {


    public static <T> T requireNotEmpty(T obj) {
        if (isEmpty(obj)) {
            throw new NotElementPresentException(I18NExt.text(I18NKey.NOT_ELEMENT_PRESENT_TO_CONTINUE));
        }
        return obj;
    }

    public static <T> T requireNotEmpty(T obj, String message) {
        if (isEmpty(obj)) {
            throw new NotElementPresentException(message);
        }
        return obj;
    }

    public static <T> T requireNotEmpty(T obj, String message, int errorCode) {
        if (isEmpty(obj)) {
            throw new NotElementPresentException(message, errorCode);
        }
        return obj;
    }

    public static <T> T requireNotEmpty(T obj, Supplier<String> messageSupplier) {
        if (isEmpty(obj))
            throw new NotElementPresentException(messageSupplier.get());
        return obj;
    }

    public static <T> T requireNotEmpty(T obj, Supplier<String> messageSupplier, Supplier<Integer> errorCodeSupplier) {
        if (isEmpty(obj))
            throw new NotElementPresentException(messageSupplier.get(), errorCodeSupplier.get());
        return obj;
    }

    public static <T> boolean isNumber(Collection<T> list, Function<T, String> function) {
        if (isEmpty(list)) {
            return true;
        }
        for (T item : list) {
            if (!NumberUtils.isCreatable(function.apply(item))) {
                return false;
            }
        }
        return true;
    }

    // From: https://programming.guide/worlds-most-copied-so-snippet.html
    public static strictfp String humanReadableByteCount(long bytes, boolean si) {
        int unit = si ? 1000 : 1024;
        long absBytes = bytes == Long.MIN_VALUE ? Long.MAX_VALUE : Math.abs(bytes);
        if (absBytes < unit) {
            return bytes + " B";
        }
        int exp = (int) (Math.log(absBytes) / Math.log(unit));
        long th = (long) Math.ceil(Math.pow(unit, exp) * (unit - 0.05));
        if (exp < 6 && absBytes >= th - ((th & 0xFFF) == 0xD00 ? 51 : 0)) {
            exp++;
        }
        String pre = (si ? "kMGTPE" : "KMGTPE").charAt(exp - 1) + (si ? "" : "i");
        if (exp > 4) {
            bytes /= unit;
            exp -= 1;
        }
        return String.format("%.1f %sB", bytes / Math.pow(unit, exp), pre);
    }
}
