package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ContextCacheUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectFieldDescribeDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.DescribeDetailResult;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.metadata.relation.CalculateRelation;
import com.facishare.paas.appframework.metadata.relation.FieldNode;
import com.facishare.paas.appframework.metadata.relation.FieldRelation;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectFieldExtra;
import com.facishare.paas.metadata.impl.LayoutRuleInfo;
import com.facishare.paas.metadata.impl.describe.DateTimeRangeFieldDescribe;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2019/12/13
 */
public class StandardQuickEditLayoutController extends PreDefineController<StandardQuickEditLayoutController.Arg, StandardQuickEditLayoutController.Result> {

    private static final Set<String> NOT_SYNC_PROPS = ImmutableSet.of(IFormField.FIELD_NAME, IFormField.RENDER_TYPE, IFieldDescribe.API_NAME, IFieldDescribe.TYPE);

    protected IObjectDescribe describe;
    protected IObjectData data;
    protected ILayout layout;
    protected List<LayoutRuleInfo> layoutRules;
    protected List<IFieldDescribe> fieldsToEdit;

    private MaskFieldLogicService maskFieldLogicService;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected Result doService(Arg arg) {
        ContextCacheUtil.openContextCache();

        validateByConfig();
        stopWatch.lap("validateByConfig");

        describe = findDescribe();
        stopWatch.lap("findDescribe");
        validateByDescribe();
        stopWatch.lap("validateByDescribe");

        data = findObjectData();
        stopWatch.lap("findObjectData");
        if (!arg.skipDataValidation()) {
            validateByData();
            stopWatch.lap("validateByData");
        }
        validateByDataPrivilege();
        stopWatch.lap("validateByDataPrivilege");

        validateByFieldPermission();
        stopWatch.lap("validateByFieldPermission");

        layout = findLayout();
        stopWatch.lap("findLayout");

        processLayout(layout);
        stopWatch.lap("processLayout");

        processLayoutByDomainPlugin();
        stopWatch.lap("processLayoutByDomainPlugin");

        validateByLayout();
        stopWatch.lap("validateByLayout");

        validateByStageInstance();
        stopWatch.lap("validateByStageInstance");

        validateByUIEvent(Lists.newArrayList(arg.getFieldApiName()));
        stopWatch.lap("validateByUIEvent");
        validateByLayoutRule(Lists.newArrayList(arg.getFieldApiName()));
        stopWatch.lap("validateByLayoutRule");
        validateByCalculateRelation();
        stopWatch.lap("validateByCalculateRelation");

        fieldsToEdit = getFieldsToEdit();
        stopWatch.lap("getFieldsToEdit");
        List<String> fieldInLayout = fieldsToEdit.stream()
                .filter(x -> !FieldDescribeExt.of(x).isHide())
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toList());
        validateByUIEvent(fieldInLayout);
        stopWatch.lap("validateByUIEvent");
        validateByLayoutRule(fieldInLayout);
        stopWatch.lap("validateByLayoutRule");

        processMaskFields();
        //填充字段扩展属性
        addFieldExtAttribute(fieldsToEdit, describe.getApiName());

        return buildResult();
    }

    private void processLayoutByDomainPlugin() {
        DescribeDetailResult describeDetailResult = DescribeDetailResult.builder()
                .layout(LayoutExt.of(layout).toMap())
                .build();
        serviceFacade.getLayoutLogicService().processLayoutByDomainPlugin(controllerContext.getTenantId(), controllerContext.getObjectApiName(),
                describeDetailResult, Lists.newArrayList(data.getRecordType()), PageType.Edit.name());
    }

    private void validateByConfig() {
        if (QuickEditConfig.isFieldCannotEdit(controllerContext.getObjectApiName(), arg.getFieldApiName())) {
            throw new ValidateException(I18N.text(I18NKey.CANNOT_EDIT_FIELD_QUICKLY));
        }
    }

    private IObjectDescribe findDescribe() {
        return serviceFacade.findObject(controllerContext.getTenantId(), controllerContext.getObjectApiName());
    }

    protected void validateByDescribe() {
        ObjectDescribeExt.of(describe).fillParentLookup();
        ObjectDescribeExt.of(describe).fillCascadeDetailLookup();
        ObjectDescribeExt.of(describe).addExtProperty();

        //隐藏了新建、编辑入口的从对象不允许单独编辑
        if (ObjectDescribeExt.of(describe).isSlaveObjectCreateWithMasterAndHiddenDetailButton()) {
            throw new ValidateException(I18N.text(I18NKey.CANNOT_EDIT_OBJECT_QUICKLY));
        }

        IFieldDescribe field = describe.getFieldDescribe(arg.getFieldApiName());
        if (field == null || !field.isActive()) {
            throw new ValidateException(I18N.text(I18NKey.CANNOT_EDIT_FIELD_QUICKLY));
        }
        //自增编号、统计字段、计算字段、引用字段、分组字段、主从字段
        if (QuickEditConfig.isFieldTypeCannotEdit(field.getType())) {
            throw new ValidateException(I18N.text(I18NKey.CANNOT_EDIT_FIELD_QUICKLY));
        }
        //系统字段
        if (IFieldDescribe.DEFINE_TYPE_SYSTEM.equals(field.getDefineType())
                && !IObjectData.NAME.equals(field.getApiName())) {
            throw new ValidateException(I18N.text(I18NKey.CANNOT_EDIT_FIELD_QUICKLY));
        }
        //虚拟字段
        if (BooleanUtils.isTrue(field.isAbstract())) {
            throw new ValidateException(I18N.text(I18NKey.CANNOT_EDIT_FIELD_QUICKLY));
        }
        //除了地区定位和日期范围字段，其他组件字段不允许编辑
        if (isGroupFieldCannotEdit(field.getApiName())) {
            throw new ValidateException(I18N.text(I18NKey.CANNOT_EDIT_FIELD_QUICKLY));
        }
    }

    private boolean isGroupFieldCannotEdit(String fieldApiName) {
        return ObjectDescribeExt.of(describe).stream()
                .filter(x -> FieldDescribeExt.of(x).isGroupField())
                .filter(x -> !FieldDescribeExt.of(x).isAreaField() && !FieldDescribeExt.of(x).isDateTimeRangeField())
                .anyMatch(x -> {
                    Map fields = (Map) x.get("fields");
                    return CollectionUtils.notEmpty(fields) && fields.containsValue(fieldApiName);
                });
    }

    private IObjectData findObjectData() {
        return serviceFacade.findObjectData(controllerContext.getUser(), arg.getDataId(), controllerContext.getObjectApiName());
    }

    protected void validateByData() {
        if (ObjectDataExt.of(data).isLock()) {
            throw new ValidateException(I18N.text(I18NKey.CANNOT_EDIT_LOCKED_DATA));
        }
        if (ObjectDataExt.of(data).isInChange()) {
            String name = StringUtils.trimToEmpty(data.getName());
            throw new ValidateException(I18N.text(I18NKey.ONGOING_APPROVAL_FLOW_AND_NO_CURRENT_OPERATION_POSSIBLE, name));
        }
    }

    private void validateByDataPrivilege() {
        serviceFacade.doDataPrivilegeCheck(controllerContext.getUser(), Lists.newArrayList(data), describe, ObjectAction.UPDATE.getActionCode());
    }

    private void validateByFieldPermission() {
        Set<String> readOnlyFields = serviceFacade.getReadonlyFields(controllerContext.getUser(), controllerContext.getObjectApiName());
        if (readOnlyFields.contains(arg.getFieldApiName())) {
            throw new ValidateException(I18N.text(I18NKey.HAS_NO_FIELD_WRITE_PERMISSION));
        }
    }

    private ILayout findLayout() {
        ILayout layout;
        //传了layoutApiName的直接使用apiName查（流程布局）
        if (!Strings.isNullOrEmpty(arg.getLayoutApiName())) {
            layout = serviceFacade.getLayoutLogicService().findObjectLayoutByApiNameAndType(controllerContext.getUser(), arg.getLayoutApiName(),
                    LayoutTypes.EDIT, describe, data);
        } else {
            layout = serviceFacade.getLayoutLogicService().findObjectLayoutWithType(buildLayoutContext(), data.getRecordType(), describe,
                    LayoutTypes.EDIT, data);
        }
        layoutRules = infraServiceFacade.findValidLayoutRuleByLayout(controllerContext.getUser(), layout.getRefObjectApiName(), layout.getName());
        LayoutExt.of(layout).handleLayoutByLayoutRule(layoutRules, LayoutTypes.EDIT);
        return layout;
    }

    protected LayoutLogicService.LayoutContext buildLayoutContext() {
        return LayoutLogicService.LayoutContext.of(controllerContext.getUser(), controllerContext.getAppId());
    }

    protected void processLayout(ILayout layout) {

    }

    private void validateByLayout() {
        String fieldApiName = getActualFieldName();
        Optional<IFormField> formField = LayoutExt.of(layout).getField(fieldApiName);
        if (!formField.isPresent() || Boolean.TRUE.equals(formField.get().isReadOnly())) {
            throw new ValidateException(I18N.text(I18NKey.HAS_NO_FIELD_WRITE_PERMISSION_IN_LAYOUT));
        }
    }

    private String getActualFieldName() {
        String fieldApiName = arg.getFieldApiName();
        List<DateTimeRangeFieldDescribe> dateTimeRangeFields = ObjectDescribeExt.of(describe).getDateTimeRangeFields();
        if (CollectionUtils.empty(dateTimeRangeFields)) {
            return fieldApiName;
        }
        for (DateTimeRangeFieldDescribe dateTimeRangeFieldDescribe : dateTimeRangeFields) {
            String startTimeFieldApiName = dateTimeRangeFieldDescribe.getStartTimeFieldApiName();
            String endTimeFieldApiName = dateTimeRangeFieldDescribe.getEndTimeFieldApiName();
            if (StringUtils.equals(startTimeFieldApiName, fieldApiName)) {
                break;
            }
            if (StringUtils.equals(endTimeFieldApiName, fieldApiName)) {
                fieldApiName = startTimeFieldApiName;
                break;
            }
        }
        return fieldApiName;
    }

    private void validateByStageInstance() {
        List<String> fieldsUsedByStageInstance = serviceFacade.getSelectOneFieldsUsedByStageInstance(controllerContext.getUser(),
                describe, layout, data);
        if (fieldsUsedByStageInstance.contains(arg.getFieldApiName())) {
            throw new ValidateException(I18N.text(I18NKey.CANNOT_EDIT_FIELD_QUICKLY_BECAUSE_OF_STAGE_INSTANCE));
        }
        LayoutExt.of(layout).setReadOnly(Sets.newHashSet(fieldsUsedByStageInstance), true);
    }

    private void validateByUIEvent(List<String> fieldApiNames) {
        fieldApiNames.forEach(fieldApiName -> {
            if (LayoutExt.of(layout).isFieldInUIEventTriggerList(fieldApiName)) {
                if (Objects.equals(fieldApiName, arg.getFieldApiName())) {
                    throw new ValidateException(I18N.text(I18NKey.CANNOT_EDIT_FIELD_QUICKLY_BECAUSE_OF_UI_EVENT));
                } else {
                    String label = ObjectDescribeExt.of(describe).getFieldLabelByName(fieldApiName);
                    throw new ValidateException(I18N.text(I18NKey.CANNOT_EDIT_FIELD_QUICKLY_BECAUSE_OF_UI_EVENT_REFERENCE, label));
                }
            }
        });
    }

    private void validateByLayoutRule(List<String> fieldApiNames) {
        fieldApiNames.forEach(fieldApiName -> {
            CollectionUtils.nullToEmpty(layoutRules).stream().filter(x -> !LayoutRuleExt.of(x).isPageTypeRule()).forEach(x -> {
                if (LayoutRuleExt.of(x).isFieldInRule(fieldApiName)) {
                    if (Objects.equals(fieldApiName, arg.getFieldApiName())) {
                        throw new ValidateException(I18N.text(I18NKey.CANNOT_EDIT_FIELD_QUICKLY_BECAUSE_OF_LAYOUT_RULE));
                    } else {
                        String label = ObjectDescribeExt.of(describe).getFieldLabelByName(fieldApiName);
                        throw new ValidateException(I18N.text(I18NKey.CANNOT_EDIT_FIELD_QUICKLY_BECAUSE_OF_LAYOUT_RULE_REFERENCE, label));
                    }
                }
            });
        });
    }

    private void validateByCalculateRelation() {
        IObjectDescribe masterDescribe = describe;
        List<IObjectDescribe> detailDescribes = null;
        if (ObjectDescribeExt.of(describe).isSlaveObjectCreateWithMaster()) {
            masterDescribe = serviceFacade.findObject(controllerContext.getTenantId(), ObjectDescribeExt.of(describe).getMasterAPIName()
                    .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR))));
            detailDescribes = Lists.newArrayList(describe);
        } else if (!ObjectDescribeExt.of(describe).isSlaveObject()) {
            detailDescribes = serviceFacade.findDetailDescribesCreateWithMaster(controllerContext.getTenantId(), describe.getApiName());
        }

        FieldRelation fieldRelation = infraServiceFacade.computeCalculateRelation(masterDescribe, detailDescribes);
        Map<String, Set<CalculateRelation.RelateField>> relateFields = FieldDescribeExt.of(describe.getFieldDescribe(arg.getFieldApiName())).getRelateFields();
        if (CollectionUtils.notEmpty(relateFields)) {
            relateFields.forEach((k, v) -> {
                if (k.equals(describe.getApiName())) {
                    return;
                }
                v.forEach(x -> {
                    FieldNode fieldNode = fieldRelation.getGraph().getNode(k, x.getFieldName()).get();
                    if (fieldNode.isDefaultValue()) {
                        throw new ValidateException(I18N.text(I18NKey.CANNOT_EDIT_FIELD_QUICKLY_BECAUSE_OF_CALCULATE_RELATION));
                    }
                });

            });
        }
    }

    protected List<IFieldDescribe> getFieldsToEdit() {
        List<IFieldDescribe> fields = Lists.newArrayList();
        FieldDescribeExt editField = FieldDescribeExt.of(describe.getFieldDescribe(arg.getFieldApiName()));
        //填充描述中开关到字段中
        addDescribeSwitch2Field(editField);
        //级联父字段
        addCascadeParentField(fields);
        //本字段
        fields.add(editField.getFieldDescribe());
        //级联子字段
        addCascadeDetailField(fields);
        //公式关联的字段
        addActiveField(fields, editField.getSelfRelateFields());
        //日期范围字段
        addDateRangeField(fields);
        //同步布局上的只读和必填属性
        syncFieldPropertyFromLayout(fields);

        //按照布局里的字段顺序排序
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ORDER_FIELDS_BY_LAYOUT_IN_QUICK_EDIT_PAGE, controllerContext.getTenantId())) {
            fields = orderFieldsByLayout(fields);
        }

        return fields;
    }

    private List<IFieldDescribe> orderFieldsByLayout(List<IFieldDescribe> fieldsToEdit) {
        if (fieldsToEdit.size() <= 1) {
            return fieldsToEdit;
        }
        List<String> orderedFieldList = LayoutExt.of(layout).getOrderFieldList();
        return fieldsToEdit.stream().sorted(Comparator.comparingInt(x -> {
            int index = orderedFieldList.indexOf(x.getApiName());
            return index == -1 ? Integer.MAX_VALUE : index;
        })).collect(Collectors.toList());
    }

    private void addDateRangeField(List<IFieldDescribe> fields) {
        List<DateTimeRangeFieldDescribe> dateTimeRangeFields = ObjectDescribeExt.of(describe).getDateTimeRangeFields();
        if (CollectionUtils.empty(dateTimeRangeFields)) {
            return;
        }
        for (DateTimeRangeFieldDescribe dateTimeRangeField : dateTimeRangeFields) {
            if (StringUtils.equals(dateTimeRangeField.getStartTimeFieldApiName(), arg.getFieldApiName())) {
                fields.add(dateTimeRangeField.getEndTimeField(describe));
                fields.add(dateTimeRangeField);
            } else if (StringUtils.equals(dateTimeRangeField.getEndTimeFieldApiName(), arg.getFieldApiName())) {
                fields.add(dateTimeRangeField.getStartTimeField(describe));
                fields.add(dateTimeRangeField);
            }
        }
    }

    private void addFieldExtAttribute(List<IFieldDescribe> fieldList, String apiName) {
        Map<String, IObjectFieldExtra> fieldExtraMap = getStringIObjectFieldExtraMap(apiName);
        if (CollectionUtils.empty(fieldExtraMap)) {
            return;
        }
        for (IFieldDescribe field : fieldList) {
            IObjectFieldExtra fieldExtra = fieldExtraMap.get(field.getApiName());
            FieldDescribeExt.of(field).mergeFromObjectFieldExtra(fieldExtra);
        }
    }

    private Map<String, IObjectFieldExtra> getStringIObjectFieldExtraMap(String apiName) {
        DescribeExtra describeExtra = serviceFacade.findDescribeExtra(controllerContext.getUser(), describe);
        if (Objects.isNull(describeExtra)) {
            return Collections.emptyMap();
        }
        return describeExtra.getDescribeExtra(apiName);
    }

    private void addDescribeSwitch2Field(FieldDescribeExt editField) {
        if (editField.isRefObjectField()) {
            String targetApiName = editField.getRefObjTargetApiName();
            IObjectDescribe objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), targetApiName);
            editField.set(IObjectDescribe.IS_OPEN_DISPLAY_NAME, ObjectDescribeExt.of(objectDescribe).isSupportDisplayName());
            if (ObjectDescribeExt.of(objectDescribe).isSupportTreeViewObject()) {
                editField.set(IObjectDescribe.IS_SUPPORT_TREE_VIEW, ObjectDescribeExt.of(objectDescribe).isSupportTreeViewObject());
            }
        }
    }

    private void processMaskFields() {
        if (AppFrameworkConfig.maskFieldEncryptGray(controllerContext.getTenantId(), describe.getApiName())) {
            validateMaskFields();
            return;
        }
        List<IFieldDescribe> maskFields = fieldsToEdit.stream().filter(x -> FieldDescribeExt.of(x).isShowMask()).collect(Collectors.toList());
        if (CollectionUtils.empty(maskFields)) {
            return;
        }
        serviceFacade.fillMaskFieldValue(controllerContext.getUser(), Lists.newArrayList(data), maskFields, false);
        maskFields.stream()
                .filter(x -> ObjectDataExt.of(data).containsField(FieldDescribeExt.getShowFieldName(x.getApiName())))
                .forEach(x -> FieldDescribeExt.of(x).setHide(true));
        stopWatch.lap("processMaskFields");
    }

    private void validateMaskFields() {
        User user = controllerContext.getUser();
        String ownerId = Optional.ofNullable(data)
                .map(ObjectDataExt::of)
                .flatMap(x -> x.getOwnerOrOutOwnerIdOptional(user))
                .orElse(null);
        maskFieldLogicService = serviceFacade.getBean(MaskFieldLogicService.class);
        Map<String, List<IFieldDescribe>> maskFieldMap = maskFieldLogicService.getMaskFields(user, Lists.newArrayList(describe), ownerId);
        Set<String> maskFieldNames = CollectionUtils.nullToEmpty(maskFieldMap.get(describe.getApiName())).stream()
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toSet());
        if (fieldsToEdit.stream().anyMatch(it -> maskFieldNames.contains(it.getApiName()))) {
            throw new ValidateException(I18NExt.text(I18NKey.CANNOT_EDIT_FIELD_QUICKLY_BECAUSE_OF_MASK_FIELD_REFERENCE));
        }
    }

    protected Result buildResult() {
        Result result = new Result();
        result.setFields(ObjectFieldDescribeDocument.ofList(fieldsToEdit));
        return result;
    }

    private void addCascadeParentField(List<IFieldDescribe> fields) {
        addActiveField(fields, ObjectDescribeExt.of(describe).findCascadeParentFields(arg.getFieldApiName()));
    }

    private void addCascadeDetailField(List<IFieldDescribe> fields) {
        addActiveField(fields, ObjectDescribeExt.of(describe).findCascadeDetailFields(arg.getFieldApiName()));
    }

    private void addActiveField(List<IFieldDescribe> fields, Collection<String> toAddFieldApiNames) {
        if (CollectionUtils.empty(toAddFieldApiNames)) {
            return;
        }
        Set<String> fieldApiNames = fields.stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet());
        toAddFieldApiNames.stream()
                .filter(x -> !fieldApiNames.contains(x))
                .forEach(x -> ObjectDescribeExt.of(describe).getActiveFieldDescribeSilently(x).ifPresent(fields::add));
    }

    private void syncFieldPropertyFromLayout(List<IFieldDescribe> fields) {
        fields.stream().map(FieldDescribeExt::of).forEach(fieldExt -> {
            IFormField formField = LayoutExt.of(layout).getField(fieldExt.getApiName()).orElse(null);
            //从布局上同步字段显示设置，比如：是否只读、是否必填、是否平铺、是否整行展示等
            if (formField != null) {
                Map<String, Object> map = FormFieldExt.of(formField).toMap();
                map.forEach((key, value) -> {
                    //如果字段描述设置了必填，则以字段描述的设置为准
                    if (BooleanUtils.isTrue(fieldExt.isRequired()) && IFormField.IS_REQUIRED.equals(key)) {
                        return;
                    }
                    if (Objects.nonNull(value) && !NOT_SYNC_PROPS.contains(key)) {
                        fieldExt.set(key, value);
                    }
                });
            } else {
                fieldExt.setHide(true);
                fieldExt.setReadOnly(true);
            }
        });
    }

    @Data
    public static class Arg {
        private String dataId;
        private String fieldApiName;
        private String layoutApiName;
        private Boolean skipDataValidation;

        public boolean skipDataValidation() {
            return Boolean.TRUE.equals(skipDataValidation);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private List<ObjectFieldDescribeDocument> fields;
        // TODO: 2023/11/20 新增data，返回数据多语
    }
}
