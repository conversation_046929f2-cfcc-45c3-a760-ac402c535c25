package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey
import com.facishare.paas.metadata.api.GroupField
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.*
import com.facishare.paas.metadata.impl.describe.DateTimeRangeFieldDescribe
import com.facishare.paas.metadata.impl.ui.layout.FieldSection
import com.facishare.paas.metadata.impl.ui.layout.FormField
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent
import spock.lang.Specification

class FormComponentExtTest extends Specification {

    def setup() {
        // Mock灰度配置，允许所有操作
        GroovyMock(UdobjGrayConfig, global: true)
        UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FORM_COMPONENT_RESET_WHEN_CHANGE_GRAY, _) >> true
    }

    def "test of factory method"() {
        given: "一个真实的FormComponent实现"
        def formComponent = new FormComponent(["api_name": "test_form", "field_section": []])

        when: "使用of方法创建FormComponentExt"
        def formComponentExt = FormComponentExt.of(formComponent)

        then: "应该正确包装表单组件"
        formComponentExt != null
        formComponentExt.getName() == "test_form"
        formComponentExt.getFieldSections() != null
    }

    def "test of factory method with FormComponentExt"() {
        given: "一个FormComponentExt实例"
        def originalFormComponent = new FormComponent(["api_name": "test_form", "field_section": []])
        def formComponentExt = FormComponentExt.of(originalFormComponent)

        when: "再次包装FormComponentExt"
        def wrappedFormComponentExt = FormComponentExt.of(formComponentExt)

        then: "应该返回内部的FormComponent"
        wrappedFormComponentExt.getFormComponent() == originalFormComponent
    }

    def "test generateFormField"() {
        given: "字段参数"
        def fieldApiName = "test_field"
        def renderType = "text"
        def isReadOnly = true
        def isRequired = false

        and: "Mock LayoutExt"
        GroovyMock(LayoutExt, global: true)
        LayoutExt.getRenderType(fieldApiName, renderType) >> renderType

        when: "生成FormField"
        def formField = FormComponentExt.generateFormField(fieldApiName, renderType, isReadOnly, isRequired)

        then: "应该正确设置属性"
        formField != null
        formField.getFieldName() == fieldApiName
        formField.isReadOnly() == isReadOnly
        formField.isRequired() == isRequired
    }

    def "test hasNoField when no fields exist"() {
        given: "没有字段的表单组件"
        def formComponent = new FormComponent(["api_name": "test_form", "field_section": []])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "检查是否没有字段"
        def result = formComponentExt.hasNoField()

        then: "应该返回true"
        result == true
    }

    def "test hasNoField when fields exist"() {
        given: "有字段的表单组件"
        def formField = new FormField()
        formField.setFieldName("test_field")
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "检查是否没有字段"
        def result = formComponentExt.hasNoField()

        then: "应该返回false"
        result == false
    }

    def "test getFieldList"() {
        given: "包含字段的表单组件"
        def formField1 = new FormField()
        formField1.setFieldName("field1")
        def formField2 = new FormField()
        formField2.setFieldName("field2")
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField1, formField2])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "获取字段列表"
        def fieldList = formComponentExt.getFieldList()

        then: "应该返回正确的字段名列表"
        fieldList == ["field1", "field2"]
    }

    def "test getBaseFieldSection when exists"() {
        given: "包含基础字段分组的表单组件"
        def baseFieldSection = new FieldSection(["api_name": FormComponentExt.BASE_FIELD_SECTION_API_NAME])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([baseFieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "获取基础字段分组"
        def result = formComponentExt.getBaseFieldSection()

        then: "应该返回基础字段分组"
        result.isPresent()
        result.get().getName() == FormComponentExt.BASE_FIELD_SECTION_API_NAME
    }

    def "test getBaseFieldSection when not exists"() {
        given: "不包含基础字段分组的表单组件"
        def otherFieldSection = new FieldSection(["api_name": "other_section"])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([otherFieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "获取基础字段分组"
        def result = formComponentExt.getBaseFieldSection()

        then: "应该返回空Optional"
        !result.isPresent()
    }

    def "test getFieldSection"() {
        given: "包含字段分组的表单组件"
        def fieldSection = new FieldSection(["api_name": "test_section"])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "获取第一个字段分组"
        def result = formComponentExt.getFieldSection()

        then: "应该返回第一个字段分组"
        result.isPresent()
        result.get().getName() == "test_section"
    }

    def "test getLastFieldSection"() {
        given: "包含多个字段分组的表单组件"
        def fieldSection1 = new FieldSection(["api_name": "section1"])
        def fieldSection2 = new FieldSection(["api_name": "section2"])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection1, fieldSection2])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "获取最后一个字段分组"
        def result = formComponentExt.getLastFieldSection()

        then: "应该返回最后一个字段分组"
        result.isPresent()
        result.get().getName() == "section2"
    }

    def "test getField when field exists"() {
        given: "包含指定字段的表单组件"
        def formField = new FormField()
        formField.setFieldName("target_field")
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "获取指定字段"
        def result = formComponentExt.getField("target_field")

        then: "应该返回该字段"
        result.isPresent()
        result.get().getFieldName() == "target_field"
    }

    def "test getField when field not exists"() {
        given: "不包含指定字段的表单组件"
        def formField = new FormField()
        formField.setFieldName("other_field")
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "获取不存在的字段"
        def result = formComponentExt.getField("target_field")

        then: "应该返回空Optional"
        !result.isPresent()
    }

    def "test containsField"() {
        given: "包含字段的表单组件"
        def formField = new FormField()
        formField.setFieldName("existing_field")
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        expect: "应该正确判断字段是否存在"
        formComponentExt.containsField("existing_field") == true
        formComponentExt.containsField("non_existing_field") == false
    }

    def "test fillAllFields for normal object"() {
        given: "普通对象描述和表单组件"
        def fieldDescribe1 = Mock(IFieldDescribe) {
            getApiName() >> "field1"
            getType() >> "text"
            isRequired() >> false
        }
        def fieldDescribe2 = Mock(IFieldDescribe) {
            getApiName() >> "field2"
            getType() >> "number"
            isRequired() >> true
        }
        def describe = Mock(ObjectDescribeExt) {
            isChangeOrderObject() >> false
            stream() >> [fieldDescribe1, fieldDescribe2].stream()
            getApiName() >> "test_object"
        }

        and: "Mock LayoutExt"
        GroovyMock(LayoutExt, global: true)
        LayoutExt.getRenderType("test_object", "field1", "text") >> "text"
        LayoutExt.getRenderType("test_object", "field2", "number") >> "number"

        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "填充所有字段"
        formComponentExt.fillAllFields(describe)

        then: "应该向第一个分组添加隐藏字段"
        formComponent.getFieldSections().get(0).getFields().size() == 2
    }

    def "test fillAllFields for change order object"() {
        given: "变更单对象描述和表单组件"
        def describe = Mock(ObjectDescribeExt) {
            isChangeOrderObject() >> true
            stream() >> [].stream()
        }

        def fieldSection1 = new FieldSection(["api_name": "section1"])
        fieldSection1.setFields([])
        def fieldSection2 = new FieldSection(["api_name": "section2"])
        fieldSection2.setFields([])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection1, fieldSection2])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "填充所有字段"
        formComponentExt.fillAllFields(describe)

        then: "应该向最后一个分组添加隐藏字段"
        fieldSection1.getFields().size() == 0
        fieldSection2.getFields().size() == 0
    }

    def "test removeField"() {
        given: "包含字段的表单组件"
        def formField1 = new FormField()
        formField1.setFieldName("field1")
        def formField2 = new FormField()
        formField2.setFieldName("field2")
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField1, formField2])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "移除指定字段"
        formComponentExt.removeField("field1")

        then: "应该从分组中移除该字段"
        formComponentExt.getFieldSections().get(0).getFields().size() == 1
        formComponentExt.getFieldSections().get(0).getFields().get(0).getFieldName() == "field2"
    }

    def "test removeFields"() {
        given: "包含字段的表单组件"
        def formField1 = new FormField()
        formField1.setFieldName("field1")
        def formField2 = new FormField()
        formField2.setFieldName("field2")
        def formField3 = new FormField()
        formField3.setFieldName("field3")
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField1, formField2, formField3])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "移除多个字段"
        formComponentExt.removeFields(["field1", "field3"] as Set)

        then: "应该从分组中移除指定字段"
        formComponentExt.getFieldSections().get(0).getFields().size() == 1
        formComponentExt.getFieldSections().get(0).getFields().get(0).getFieldName() == "field2"
    }

    def "test hideFields"() {
        given: "包含字段的表单组件"
        def requiredField = new FormField()
        requiredField.setFieldName("required_field")
        requiredField.setRequired(true)
        def ownerField = new FormField()
        ownerField.setFieldName(IObjectData.OWNER)
        ownerField.setRequired(true)
        def normalField = new FormField()
        normalField.setFieldName("normal_field")
        normalField.setRequired(false)
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([requiredField, ownerField, normalField])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "隐藏字段"
        formComponentExt.hideFields(["required_field", "owner", "normal_field"] as Set)

        then: "应该保留必填字段但移除owner字段"
        formComponentExt.getFieldSections().get(0).getFields().size() == 1
        formComponentExt.getFieldSections().get(0).getFields().get(0).getFieldName() == "required_field"
    }

    def "test setRequired"() {
        given: "表单组件和对象描述"
        def requiredField = Mock(IFieldDescribe) {
            isRequired() >> true
        }
        def describe = Mock(ObjectDescribeExt) {
            getFieldDescribeSilently("test_field") >> Optional.of(requiredField)
        }

        def formField = new FormField()
        formField.setFieldName("test_field")
        formField.setRequired(false)
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "设置必填属性"
        formComponentExt.setRequired(describe)

        then: "应该设置字段为必填"
        def updatedFormField = formComponent.getFieldSections().get(0).getFields().get(0)
        updatedFormField.isRequired() == true
    }

    def "test setReadOnly"() {
        given: "表单组件"
        def formField1 = new FormField()
        formField1.setFieldName("field1")
        formField1.setReadOnly(false)
        def formField2 = new FormField()
        formField2.setFieldName("field2")
        formField2.setReadOnly(false)
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField1, formField2])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "设置只读属性"
        formComponentExt.setReadOnly(["field1"] as Set, true)

        then: "应该只设置指定字段为只读"
        def updatedFormField1 = formComponent.getFieldSections().get(0).getFields().find { it.getFieldName() == "field1" }
        def updatedFormField2 = formComponent.getFieldSections().get(0).getFields().find { it.getFieldName() == "field2" }
        updatedFormField1.isReadOnly() == true
        updatedFormField2.isReadOnly() == false
    }

    def "test removeFieldByTypes with ObjectDescribeExt"() {
        given: "表单组件和对象描述"
        def textField = Mock(IFieldDescribe) {
            getType() >> "text"
        }
        def numberField = Mock(IFieldDescribe) {
            getType() >> "number"
        }
        def describe = Mock(ObjectDescribeExt) {
            getFieldDescribeSilently("text_field") >> Optional.of(textField)
            getFieldDescribeSilently("number_field") >> Optional.of(numberField)
        }

        def formField1 = new FormField(["field_name": "text_field"])
        def formField2 = new FormField(["field_name": "number_field"])
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField1, formField2])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "根据类型移除字段"
        formComponentExt.removeFieldByTypes(describe, "text")

        then: "应该移除指定类型的字段"
        formComponent.getFieldSections().get(0).getFields().size() == 1
        formComponent.getFieldSections().get(0).getFields().get(0).getFieldName() == "number_field"
    }

    def "test removeFieldByTypes without ObjectDescribeExt"() {
        given: "表单组件"
        def formField1 = new FormField(["field_name": "field1", "render_type": "text"])
        def formField2 = new FormField(["field_name": "field2", "render_type": "number"])
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField1, formField2])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "根据渲染类型移除字段"
        formComponentExt.removeFieldByTypes("text")

        then: "应该移除指定渲染类型的字段"
        formComponent.getFieldSections().get(0).getFields().size() == 1
        formComponent.getFieldSections().get(0).getFields().get(0).getFieldName() == "field2"
    }

    def "test removeFieldByTypesExceptApiNameList"() {
        given: "表单组件"
        def formField1 = new FormField(["field_name": "field1", "render_type": "text"])
        def formField2 = new FormField(["field_name": "field2", "render_type": "text"])
        def formField3 = new FormField(["field_name": "field3", "render_type": "number"])
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField1, formField2, formField3])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "根据类型移除字段，但排除指定字段"
        formComponentExt.removeFieldByTypesExceptApiNameList(["field1"], "text")

        then: "应该保留排除列表中的字段"
        formComponent.getFieldSections().get(0).getFields().size() == 2
        formComponent.getFieldSections().get(0).getFields().find { it.getFieldName() == "field1" } != null
        formComponent.getFieldSections().get(0).getFields().find { it.getFieldName() == "field3" } != null
    }

    def "test retainFieldsByPageType"() {
        given: "表单组件和页面类型"
        def formField1 = new FormField(["field_name": "field1", "page_type": ["create"]])
        def formField2 = new FormField(["field_name": "field2", "page_type": ["edit"]])
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField1, formField2])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "根据页面类型保留字段"
        formComponentExt.retainFieldsByPageType("create")

        then: "应该只保留符合页面类型的字段"
        formComponent.getFieldSections().get(0).getFields().size() == 1
        formComponent.getFieldSections().get(0).getFields().get(0).getFieldName() == "field1"
    }

    def "test removeEmptySection"() {
        given: "包含空分组的表单组件"
        def emptySection = new FieldSection(["api_name": "empty_section"])
        emptySection.setFields([])
        def nonEmptySection = new FieldSection(["api_name": "non_empty_section"])
        nonEmptySection.setFields([new FormField(["field_name": "test_field"])])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([emptySection, nonEmptySection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "移除空分组"
        formComponentExt.removeEmptySection()

        then: "应该只保留非空分组"
        formComponent.getFieldSections().size() == 1
        formComponent.getFieldSections().get(0).getName() == "non_empty_section"
    }

    def "test removeSectionExcludeParamList"() {
        given: "包含分组的表单组件"
        def emptySection = new FieldSection(["api_name": "empty_section"])
        emptySection.setFields([])
        def protectedSection = new FieldSection(["api_name": "protected_section"])
        protectedSection.setFields([])
        def nonEmptySection = new FieldSection(["api_name": "non_empty_section"])
        nonEmptySection.setFields([new FormField(["field_name": "test_field"])])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([emptySection, protectedSection, nonEmptySection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "移除分组但排除指定分组"
        formComponentExt.removeSectionExcludeParamList(["protected_section"])

        then: "应该保留排除列表中的分组和非空分组"
        formComponent.getFieldSections().size() == 2
        formComponent.getFieldSections().any { it.getName() == "protected_section" }
        formComponent.getFieldSections().any { it.getName() == "non_empty_section" }
    }

    def "test adjustFieldRenderType"() {
        given: "表单组件"
        def formField = new FormField(["field_name": "test_field", "render_type": "old_type"])
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "调整字段渲染类型"
        formComponentExt.adjustFieldRenderType("test_object")

        then: "应该保持原有的渲染类型（因为不是特殊字段）"
        def updatedFormField = formComponent.getFieldSections().get(0).getFields().get(0)
        updatedFormField.getRenderType() == "old_type"
    }



    def "test addFields when base section exists"() {
        given: "表单组件和要添加的字段"
        def baseSection = new FieldSection(["api_name": FormComponentExt.BASE_FIELD_SECTION_API_NAME])
        baseSection.setFields([])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([baseSection])
        def formComponentExt = FormComponentExt.of(formComponent)
        def formFields = [new FormField(["field_name": "new_field"])]

        when: "添加字段"
        formComponentExt.addFields(formFields)

        then: "应该向基础分组添加字段"
        def updatedBaseSection = formComponent.getFieldSections().find { it.getName() == FormComponentExt.BASE_FIELD_SECTION_API_NAME }
        updatedBaseSection.getFields().size() == 1
        updatedBaseSection.getFields().get(0).getFieldName() == "new_field"
    }

    def "test addFields when base section not exists"() {
        given: "没有基础分组的表单组件"
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([])
        def formComponentExt = FormComponentExt.of(formComponent)
        def formFields = [new FormField(["field_name": "new_field"])]

        and: "Mock I18NExt"
        GroovyMock(com.facishare.paas.appframework.core.i18n.I18NExt, global: true)
        com.facishare.paas.appframework.core.i18n.I18NExt.text(_) >> "基础信息"

        when: "添加字段"
        formComponentExt.addFields(formFields)

        then: "应该创建新的基础分组"
        formComponent.getFieldSections().size() == 1
        formComponent.getFieldSections().get(0).getName() == FormComponentExt.BASE_FIELD_SECTION_API_NAME
    }

    def "test addField when field already exists"() {
        given: "表单组件中已包含该字段"
        def field = Mock(IFieldDescribe) {
            getApiName() >> "existing_field"
        }
        def formField = new FormField(["field_name": "existing_field"])
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "尝试添加已存在的字段"
        formComponentExt.addField(field, false, false, "text")

        then: "应该不执行任何操作"
        noExceptionThrown()
        formComponent.getFieldSections().get(0).getFields().size() == 1
    }

    def "test addField when base section not exists throws exception"() {
        given: "没有基础分组的表单组件"
        def field = Mock(IFieldDescribe) {
            getApiName() >> "new_field"
        }
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "尝试添加字段但没有基础分组"
        formComponentExt.addField(field, false, false, "text")

        then: "应该抛出验证异常"
        thrown(ValidateException)
    }

    def "test addField for count field"() {
        given: "COUNT类型字段"
        def field = Mock(IFieldDescribe) {
            getApiName() >> "count_field"
            getType() >> IFieldType.COUNT
        }
        def baseSection = new FieldSection(["api_name": FormComponentExt.BASE_FIELD_SECTION_API_NAME])
        baseSection.setFields([])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([baseSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        and: "Mock LayoutExt"
        GroovyMock(LayoutExt, global: true)
        LayoutExt.getRenderType("count_field", "number") >> "number"

        when: "添加COUNT字段"
        formComponentExt.addField(field, false, false, "number")

        then: "应该设置为只读"
        def updatedBaseSection = formComponent.getFieldSections().find { it.getName() == FormComponentExt.BASE_FIELD_SECTION_API_NAME }
        updatedBaseSection.getFields().size() == 1
        updatedBaseSection.getFields().get(0).isReadOnly() == true
    }

    def "test addField for select field"() {
        given: "选择字段"
        def field = Mock(IFieldDescribe) {
            getApiName() >> "select_field"
            getType() >> IFieldType.SELECT_ONE
        }
        def baseSection = new FieldSection(["api_name": FormComponentExt.BASE_FIELD_SECTION_API_NAME])
        baseSection.setFields([])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([baseSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        and: "Mock FieldDescribeExt"
        GroovyMock(FieldDescribeExt, global: true)
        FieldDescribeExt.of(field) >> Mock(FieldDescribeExt) {
            isSelectOne() >> true
            isSelectMany() >> false
        }

        and: "Mock LayoutExt"
        GroovyMock(LayoutExt, global: true)
        LayoutExt.getRenderType("select_field", "select") >> "select"

        and: "Mock FormFieldExt"
        GroovyMock(FormFieldExt, global: true)
        FormFieldExt.of(_) >> Mock(FormFieldExt) {
            setTiled(true) >> {}
        }

        when: "添加选择字段"
        formComponentExt.addField(field, false, false, "select")

        then: "应该设置为平铺展示"
        def updatedBaseSection = formComponent.getFieldSections().find { it.getName() == FormComponentExt.BASE_FIELD_SECTION_API_NAME }
        updatedBaseSection.getFields().size() == 1
    }

    def "test addField for long text field"() {
        given: "长文本字段"
        def field = Mock(IFieldDescribe) {
            getApiName() >> "long_text_field"
            getType() >> IFieldType.LONG_TEXT
        }
        def baseSection = new FieldSection(["api_name": FormComponentExt.BASE_FIELD_SECTION_API_NAME])
        baseSection.setFields([])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([baseSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        and: "Mock FieldDescribeExt"
        GroovyMock(FieldDescribeExt, global: true)
        FieldDescribeExt.of(field) >> Mock(FieldDescribeExt) {
            isSelectOne() >> false
            isSelectMany() >> false
        }

        and: "Mock LayoutExt"
        GroovyMock(LayoutExt, global: true)
        LayoutExt.getRenderType("long_text_field", "long_text") >> "long_text"

        and: "Mock FormFieldExt"
        GroovyMock(FormFieldExt, global: true)
        FormFieldExt.of(_) >> Mock(FormFieldExt) {
            setFullLine(true) >> {}
        }

        when: "添加长文本字段"
        formComponentExt.addField(field, false, false, "long_text")

        then: "应该设置为整行展示"
        def updatedBaseSection = formComponent.getFieldSections().find { it.getName() == FormComponentExt.BASE_FIELD_SECTION_API_NAME }
        updatedBaseSection.getFields().size() == 1
    }

    def "test addGroupField for non-group field"() {
        given: "非组字段"
        def field = Mock(IFieldDescribe) {
            getType() >> "text"
        }
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "尝试添加非组字段"
        formComponentExt.addGroupField(field, [])

        then: "应该不执行任何操作"
        formComponent.getFieldSections().size() == 0
    }

    def "test addGroupField for date range field"() {
        given: "日期范围组字段"
        def groupField = new DateTimeRangeFieldDescribe()
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "添加日期范围组字段"
        formComponentExt.addGroupField(groupField, [Mock(IFieldDescribe)])

        then: "应该不执行任何操作"
        formComponent.getFieldSections().size() == 0
    }

    def "test addGroupField for payment group"() {
        given: "支付组字段"
        def payment = Mock(Payment) {
            getType() >> IFieldType.GROUP
            getGroupType() >> GroupField.GROUP_TYPE_PAYMENT
            getAmountIsReadonly() >> true
            getPayAmountFieldApiName() >> "amount_field"
            getApiName() >> "payment_group"
            getLabel() >> "Payment Group"
        }
        def amountField = Mock(IFieldDescribe) {
            getApiName() >> "amount_field"
            getType() >> "currency"
        }
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "添加支付组字段"
        formComponentExt.addGroupField(payment, [amountField])

        then: "应该创建支付组分组"
        formComponent.getFieldSections().size() == 1
        formComponent.getFieldSections().get(0).getName() == "payment_group"
    }

    def "test updateGroupField for payment group"() {
        given: "支付组字段和现有分组"
        def payment = Mock(Payment) {
            getType() >> IFieldType.GROUP
            getGroupType() >> GroupField.GROUP_TYPE_PAYMENT
            getAmountIsReadonly() >> false
            getPayAmountFieldApiName() >> "amount_field"
            getApiName() >> "payment_group"
        }
        def amountFormField = new FormField()
        amountFormField.setFieldName("amount_field")
        amountFormField.setReadOnly(true)
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([amountFormField])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "更新支付组字段"
        formComponentExt.updateGroupField(payment, [], true)

        then: "应该更新金额字段的只读属性"
        def updatedFormField = formComponent.getFieldSections().get(0).getFields().find { it.getFieldName() == "amount_field" }
        updatedFormField.isReadOnly() == false
    }

    def "test updateGroupField for area group with town support"() {
        given: "支持乡镇的区域组字段"
        def area = Mock(Area) {
            getType() >> IFieldType.GROUP
            getGroupType() >> GroupField.GROUP_TYPE_AREA
            getApiName() >> "area_group"
            getIsSupportTown() >> true
            getAreaTownFieldApiName() >> "town_field"
            getIsSupportVillage() >> false
        }
        def townField = Mock(IFieldDescribe) {
            getApiName() >> "town_field"
            getType() >> IFieldType.TOWN
            isRequired() >> false
        }
        def existingFormField = new FormField()
        existingFormField.setFieldName("existing_field")
        def areaSection = new FieldSection(["api_name": "area_group"])
        areaSection.setFields([existingFormField])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([areaSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "更新区域组字段"
        formComponentExt.updateGroupField(area, [townField], true)

        then: "应该添加乡镇字段"
        areaSection.getFields().size() >= 1
    }

    def "test getWhatFormFields"() {
        given: "包含字段的表单组件"
        def formField = new FormField()
        formField.setFieldName("original_field")
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        and: "Mock WhatComponentExt"
        GroovyMock(WhatComponentExt, global: true)
        WhatComponentExt.getWhatFieldName("what_api", "original_field") >> "what_api.original_field"

        when: "获取What字段"
        def result = formComponentExt.getWhatFormFields("what_api")

        then: "应该返回带What前缀的字段"
        result.size() == 1
        result.get(0).getFieldName() == "what_api.original_field"
    }

    def "test setTargetFieldApiName"() {
        given: "表单组件和对象描述"
        def fieldSection = new FieldSection()
        fieldSection.setName("existing_field")
        def describe = Mock(IObjectDescribe) {
            containsField("existing_field") >> true
        }
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "设置目标字段API名"
        formComponentExt.setTargetFieldApiName(describe)

        then: "应该设置target_field_api_name属性"
        formComponent.getFieldSections().get(0).get("target_field_api_name") == "existing_field"
    }

    def "test removeDuplicatedFormFieldByFormComponent"() {
        given: "包含重复字段的表单组件"
        def formField1 = new FormField()
        formField1.setFieldName("field1")
        def formField2 = new FormField() // 重复字段
        formField2.setFieldName("field1")
        def formField3 = new FormField()
        formField3.setFieldName("field2")
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField1, formField2, formField3])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "移除重复字段"
        formComponentExt.removeDuplicatedFormFieldByFormComponent([] as Set)

        then: "应该只保留第一个出现的字段"
        formComponent.getFieldSections().get(0).getFields().size() == 2
        formComponent.getFieldSections().get(0).getFields().get(0).getFieldName() == "field1"
        formComponent.getFieldSections().get(0).getFields().get(1).getFieldName() == "field2"
    }

    def "test orderFormFields"() {
        given: "表单组件和排序字段列表"
        def orderFormField = new FormField()
        orderFormField.setFieldName("field1")
        def fieldDescribe = Mock(IFieldDescribe) {
            getApiName() >> "field1"
            getType() >> "text"
            isRequired() >> false
        }
        def describe = Mock(ObjectDescribeExt) {
            getFieldDescribes() >> [fieldDescribe]
            stream() >> [fieldDescribe].stream()
            getApiName() >> "test_object"
        }
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        and: "Mock LayoutExt"
        GroovyMock(LayoutExt, global: true)
        LayoutExt.getRenderType("test_object", "field1", "text") >> "text"

        when: "排序字段"
        formComponentExt.orderFormFields([orderFormField], describe)

        then: "应该重新设置字段顺序"
        formComponent.getFieldSections().size() == 1
        formComponent.getFieldSections().get(0).getFields().size() == 1
    }

    def "test reference field config methods"() {
        given: "表单组件"
        def formComponent = new FormComponent(["api_name": "test_form"])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "设置引用字段配置"
        formComponentExt.setReferenceFieldConfig("layout_api")

        then: "应该设置配置值"
        formComponent.get(FormComponentExt.REFERENCE_FIELD_CONFIG, String.class) == "layout_api"

        when: "检查是否为引用布局字段配置"
        def result = formComponentExt.isReferenceLayoutFieldConfig()

        then: "应该返回true"
        result == true
    }

    def "test stream"() {
        given: "包含字段的表单组件"
        def formField1 = new FormField(["field_name": "field1"])
        def formField2 = new FormField(["field_name": "field2"])
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField1, formField2])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "获取字段流"
        def fieldStream = formComponentExt.stream()
        def fields = fieldStream.collect()

        then: "应该返回所有字段"
        fields.size() == 2
        fields.contains(formField1)
        fields.contains(formField2)
    }

    def "test getFormFields"() {
        given: "包含字段的表单组件"
        def formField1 = new FormField(["field_name": "field1"])
        def formField2 = new FormField(["field_name": "field2"])
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField1, formField2])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)

        when: "获取表单字段列表"
        def result = formComponentExt.getFormFields()

        then: "应该返回所有字段"
        result.size() == 2
        result.contains(formField1)
        result.contains(formField2)
    }

    def "test forEachFormField"() {
        given: "包含字段的表单组件"
        def formField1 = new FormField(["field_name": "field1"])
        def formField2 = new FormField(["field_name": "field2"])
        def fieldSection = new FieldSection(["api_name": "test_section"])
        fieldSection.setFields([formField1, formField2])
        def formComponent = new FormComponent(["api_name": "test_form"])
        formComponent.setFieldSections([fieldSection])
        def formComponentExt = FormComponentExt.of(formComponent)
        def counter = 0

        when: "遍历所有字段"
        formComponentExt.forEachFormField { field ->
            counter++
        }

        then: "应该遍历所有字段"
        counter == 2
    }
} 