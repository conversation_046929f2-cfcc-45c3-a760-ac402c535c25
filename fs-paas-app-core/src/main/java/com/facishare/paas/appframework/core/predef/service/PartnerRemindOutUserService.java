package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.InternationalItem;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.OutInfoChangeModel;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.prm.enums.ChannelNoticeBizType;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import joptsimple.internal.Strings;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2018/9/28 17:15
 * @instruction
 */
@Service
@Slf4j
public class PartnerRemindOutUserService {

    @Autowired
    private OrgService orgService;

    @Autowired
    private OutChannelCrmNotificationService outChannelCrmNotificationService;

    public void remindOutUser(User user, String apiName, Integer remindRecordType, List<OutInfoChangeModel> outInfoChangeModels) {
        // 下游数据
        User newUser = user;
        if (Strings.isNullOrEmpty(user.getUserName())) {
            newUser = orgService.getUser(user.getTenantId(), user.getUserIdOrOutUserIdIfOutUser());
        }
        RequestContext context = RequestContextManager.getContext();

        CrmMessage message = CrmMessage.builder()
                .requestContext(context)
                .user(newUser)
                .describeApiName(apiName)
                .build();
        for (OutInfoChangeModel info : outInfoChangeModels) {
            if (info.getOldOutEI().equals(info.getNewOutEI()) && info.getOldOutUserId().equals(info.getNewOutUserId())) {
                continue;
            }
            message.setInfo(info);
            if (remindRecordType == 92) {
                customSendMessage(info, newUser, apiName, remindRecordType, message);
            } else {
                oldSendMessage(info, newUser, apiName, remindRecordType, message);
            }
        }
    }

    //自定义对象方法
    private void customSendMessage(OutInfoChangeModel info, User user, String apiName, Integer remindRecordType, CrmMessage message) {
        RequestContext context = RequestContextManager.getContext();
        String outOperator = context.getUser().getOutUserId();
        if (info.getNewOutEI() != 0 && info.getNewOutUserId() != 0 && !Objects.equals(outOperator, String.valueOf(info.getNewOutUserId()))) {
            sendNewCrmMessage(info, user, info.getNewOutUserId(), apiName,
                    remindRecordType, message.getNewTitle(),
                    message.getNewContent(),
                    message.getNewContentKey(),
                    message.getNewContentParameters(),
                    message.getNewTitleKey(),
                    message.getNewTitleParameters());
        }
        //通知老合作伙伴负责人
        if (info.getOldOutEI() != 0 && info.getOldOutUserId() != 0 && !Objects.equals(outOperator, String.valueOf(info.getOldOutUserId()))) {
            sendNewCrmMessage(info, user, info.getOldOutUserId(), apiName, remindRecordType, message.getOldTitle(), message.getOldContent(), message.getOldContentKey(), message.getOldContentParameters(), message.getOldTitleKey(), message.getOldTitleParameters());
        }
    }

    private void sendNewCrmMessage(OutInfoChangeModel info, User user, Integer receiverId, String apiName, Integer remindRecordType, String newTitle, String newContent, String newContentKey, List<String> newContentParameters, String newTitleKey, List<String> newTitleParameters) {
        Map<String, String> urlParameter = Maps.newHashMap();
        urlParameter.put("objectApiName", apiName);
        urlParameter.put("objectId", info.getDataId());
        NewCrmNotification newCrmNotification = NewCrmNotification.builder()
                .sourceId(UUID.randomUUID().toString())
                .type(remindRecordType)
                .title(newTitle)
                .objectApiName(apiName)
                .objectId(info.getDataId())
                .fullContent(newContent)
                .receiverIDs(Sets.newHashSet(receiverId))
                .senderId(RequestContextManager.getContext().getUser().getUserId())
                .appId(AppIdMapping.appIdMapping.getOrDefault(PrmConstant.PRM_APP_ID, PrmConstant.PRM_APP_ID))
                .urlParameter(urlParameter)
                .urlType(1)
                .fullContentInfo(InternationalItem.builder().internationalKey(newContentKey).internationalParameters(newContentParameters).build())
                .titleInfo(InternationalItem.builder().internationalKey(newTitleKey).internationalParameters(newTitleParameters).build())
                .build();
        outChannelCrmNotificationService.fillSendOutChannelInfo(user, newCrmNotification, ChannelNoticeBizType.ALLOCATE_OBJECT, info);
    }

    //老对象方法 type表示 是更换合作伙伴还是更换合作伙伴联系人
    private void oldSendMessage(OutInfoChangeModel info, User user, String apiName, Integer remindRecordType, CrmMessage message) {
        RequestContext context = RequestContextManager.getContext();
        String outOperator = context.getUser().getOutUserId();
        if (info.getNewOutEI() != 0 && info.getNewOutUserId() != 0 && !Objects.equals(outOperator, String.valueOf(info.getNewOutUserId()))) {
            sendNewCrmMessage(info, user, info.getNewOutUserId(), apiName, remindRecordType, message.getNewTitle(), message.getNewContent(), message.getNewContentKey(), message.getNewContentParameters(), message.getNewTitleKey(), message.getNewTitleParameters());
        }
        //通知老合作伙伴负责人
        if (info.getOldOutEI() != 0 && info.getOldOutUserId() != 0 && !Objects.equals(outOperator, String.valueOf(info.getOldOutUserId()))) {
            sendNewCrmMessage(info, user, info.getOldOutUserId(), apiName, remindRecordType, message.getOldTitle(), message.getOldContent(), message.getOldContentKey(), message.getOldContentParameters(), message.getOldTitleKey(), message.getOldTitleParameters());
        }
    }


    public Integer getChangePartnerRemindRecordType(String apiName) {
        Integer remindRecordType;
        switch (apiName) {
            case "LeadsObj":
                remindRecordType = 96;
                break;
            case "AccountObj":
                remindRecordType = 98;
                break;
            case "OpportunityObj":
                remindRecordType = 100;
                break;
            case "SalesOrderObj":
                remindRecordType = 102;
                break;
            default:
                remindRecordType = 0;
                break;
        }
        return remindRecordType;
    }

    public Integer getChangePartnerOwnerRemindRecordType(String apiName) {
        Integer remindRecordType;
        switch (apiName) {
            case "LeadsObj":
                remindRecordType = 97;
                break;
            case "AccountObj":
                remindRecordType = 99;
                break;
            case "OpportunityObj":
                remindRecordType = 101;
                break;
            case "SalesOrderObj":
                remindRecordType = 103;
                break;
            default:
                remindRecordType = 0;
                break;
        }
        return remindRecordType;
    }

    @Data
    @Builder
    static class CrmMessage {
        private User user;
        private OutInfoChangeModel info;
        private RequestContext requestContext;
        private String describeApiName;

        public String getOldContent() {
            return I18N.text(I18NKey.UNASSIGN_725, user.getUserName(), info.getDisplayName(), info.getDataName());
        }

        public String getNewContent() {
            return I18N.text(I18NKey.ASSIGN_725, user.getUserName(), info.getDisplayName(), info.getDataName());
        }

        public String getOldContentKey() {
            return I18NKey.UNASSIGN_725;
        }

        public List<String> getOldContentParameters() {
            if (info.getIsPreDefineObj()) {
                return Lists.newArrayList(user.getUserName(), String.format("#I18N#%s", GetI18nKeyUtil.getDescribeDisplayNameKey(describeApiName)), info.getDataName());
            } else {
                return Lists.newArrayList(user.getUserName(), info.getDisplayName(), info.getDataName());
            }
        }

        public String getOldTitleKey() {
            return I18NKey.OUTOWNER_CHANGE;
        }

        public List<String> getOldTitleParameters() {
            if (info.getIsPreDefineObj()) {
                return Lists.newArrayList(String.format("#I18N#%s", GetI18nKeyUtil.getDescribeDisplayNameKey(describeApiName)));
            } else {
                return Lists.newArrayList(info.getDisplayName());
            }
        }


        public String getNewContentKey() {
            return I18NKey.ASSIGN_725;
        }

        public List<String> getNewContentParameters() {
            if (info.getIsPreDefineObj()) {
                return Lists.newArrayList(user.getUserName(), String.format("#I18N#%s", GetI18nKeyUtil.getDescribeDisplayNameKey(describeApiName)), info.getDataName());
            } else {
                return Lists.newArrayList(user.getUserName(), info.getDisplayName(), info.getDataName());
            }
        }

        public String getNewTitleKey() {
            return I18NKey.FOLLOW_CUSTOME;
        }

        public List<String> getNewTitleParameters() {
            if (info.getIsPreDefineObj()) {
                return Lists.newArrayList(String.format("#I18N#%s", GetI18nKeyUtil.getDescribeDisplayNameKey(describeApiName)));
            } else {
                return Lists.newArrayList(info.getDisplayName());
            }
        }

        public String getOldTitle() {
            return I18N.text(I18NKey.OUTOWNER_CHANGE, info.getDisplayName());
        }

        public String getNewTitle() {
            return I18N.text(I18NKey.FOLLOW_CUSTOME, info.getDisplayName());
        }
    }

    public <T> T getObjectI18nFieldValue(@NotNull IObjectData data, @NotNull String fieldName, @NotNull Class<T> clazz) {
        String i18nKey = fieldName + "__r";
        T value = data.get(i18nKey, clazz);
        if (value == null) {
            return data.get(fieldName, clazz);
        }
        if (value instanceof String && StringUtils.isBlank((String) value)) {
            return data.get(fieldName, clazz);
        }
        return value;
    }
}
