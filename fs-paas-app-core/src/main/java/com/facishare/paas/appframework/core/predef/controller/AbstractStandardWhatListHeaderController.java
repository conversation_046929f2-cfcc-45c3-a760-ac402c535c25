package com.facishare.paas.appframework.core.predef.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.QueryTemplateDocument;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.CommonFilterField;
import com.facishare.paas.appframework.metadata.layout.LayoutAgentType;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.appframework.metadata.layout.component.BaseFlowTaskListComponentExt;
import com.facishare.paas.appframework.metadata.layout.component.FlowTaskListComponentExt;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IOrderBy;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.bson.Document;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.crm.userdefobj.DefObjConstants.flowInvisibleFieldNameMap;

public abstract class AbstractStandardWhatListHeaderController<A extends AbstractStandardWhatListHeaderController.Arg> extends PreDefineController<A, AbstractStandardWhatListHeaderController.Result> {

    protected ObjectDescribeExt objectDescribeExt;
    protected ObjectDescribeExt whatObjectDescribeExt;
    protected LayoutExt layoutExt;
    protected FlowTaskLayoutExt flowTaskLayoutExt;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected void before(A arg) {
        super.before(arg);
        initDescribe();
        initLayout();
    }

    private void initDescribe() {
        IObjectDescribe objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), arg.getApiName());
        objectDescribeExt = ObjectDescribeExt.of(objectDescribe.copy());
        IObjectDescribe whatObjectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), arg.getWhatApiName());
        whatObjectDescribeExt = ObjectDescribeExt.of(whatObjectDescribe.copy());
        // 特殊处理 recordType 业务类型的 options,添加value使其和单选的options保持一致
        whatObjectDescribeExt.fillValueToRecordType();
    }

    @Override
    protected Result doService(A arg) {
        List<ISearchTemplate> templateList = getTemplates();

        List<IHeadField> headFields = getHeadFieldsFromFlowTaskListComponentExt();
        List<DocumentBaseEntity> fieldListConfigWithWhatField = getFieldListConfigWithField(headFields);
        List<DocumentBaseEntity> visibleFieldsWidth = getFieldWidthConfigs(fieldListConfigWithWhatField, headFields);

        Result ret = Result.builder()
                .templates(QueryTemplateDocument.ofList(templateList))
                .fieldList(fieldListConfigWithWhatField)
                .visibleFieldsWidth(visibleFieldsWidth)
                .quickFilterField(getQuickFilterField())
                .filterFields(getFilterFields())
                .orderFields(getOrderFields())
                .layout(LayoutDocument.of(layoutExt))
                .build();
        if (arg.separationDescribe()) {
            ret.setDescribeMap(describeMap());
            ret.setDescribeExtMap(describeExtMap());
            return ret;
        }

        objectDescribeExt.mergeWhatObjectFields(whatObjectDescribeExt);
        ret.setObjectDescribeExt(fillObjectDescribeExt(objectDescribeExt.getObjectDescribe()));

        if (AppFrameworkConfig.isGrayWhatListDescribeCache(controllerContext.getTenantId(), arg.getApiName())) {
            Map<String, Integer> describeVersionMap = arg.getDescribeVersionMap();
            ObjectDescribeDocument objectDescribeDocument = ObjectDescribeDocument.handleDescribeCache(describeVersionMap, objectDescribeExt.getObjectDescribe());
            if (Objects.nonNull(objectDescribeDocument)) {
                ret.setObjectDescribe(objectDescribeDocument);
            } else {
                ret.setObjectDescribe(null);
            }
        } else {
            ret.setObjectDescribe(ObjectDescribeDocument.of(objectDescribeExt));
        }

        return ret;
    }

    private List<DocumentBaseEntity> getOrderFields() {
        if (isMobile()) {
            return getOrders().stream()
                    .map(it -> new DocumentBaseEntity(Document.parse(it.toJsonString())))
                    .collect(Collectors.toList());
        }
        return null;
    }

    private List<DocumentBaseEntity> buildFieldList(List<DocumentBaseEntity> fieldListConfigWithWhatField, List<IHeadField> headFields) {
        if (CollectionUtils.notEmpty(fieldListConfigWithWhatField)) {
            return fieldListConfigWithWhatField;
        }
        if (CollectionUtils.empty(headFields)) {
            layoutExt.getFieldShowList(Collections.emptyList());
        }
        return headFields.stream()
                .map(headField -> {
                    DocumentBaseEntity document = new DocumentBaseEntity();
                    document.put(headField.getFieldName(), true);
                    document.put("width", headField.getWidth());
                    return document;
                })
                .collect(Collectors.toList());
    }

    private void initLayout() {
        // 获取流程对象详情页布局
        layoutExt = findLayout();
        // 获取流程待办布局
        flowTaskLayoutExt = findFlowTaskLayoutExt();
    }

    private FlowTaskLayoutExt findFlowTaskLayoutExt() {
        ILayout layout = serviceFacade.getLayoutLogicService().getFlowTaskListLayoutWitchComponents(buildLayoutContext(), objectDescribeExt, whatObjectDescribeExt, PageType.ListHeader, getLayoutAgentType(), null);
        if (Objects.isNull(layout)) {
            return null;
        }
        return FlowTaskLayoutExt.of(layout);
    }

    protected LayoutLogicService.LayoutContext buildLayoutContext() {
        return LayoutLogicService.LayoutContext.of(controllerContext.getUser(), controllerContext.getAppId());
    }

    private LayoutAgentType getLayoutAgentType() {
        if (RequestUtil.isMobileDeviceRequest() || LayoutAgentType.MOBILE.getCode().equals(arg.getLayoutAgentType())) {
            return LayoutAgentType.MOBILE;
        }
        return LayoutAgentType.WEB;
    }

    private LayoutExt findLayout() {
        ILayout objectDefaultLayout = serviceFacade.getLayoutLogicService().findObjectLayoutWithWhatDescribe(controllerContext.getUser(), objectDescribeExt, whatObjectDescribeExt);
        return LayoutExt.of(objectDefaultLayout);
    }

    protected Map<String, ObjectDescribeDocument> describeExtMap() {
        Map<String, ObjectDescribeDocument> result = Maps.newHashMap();
        ObjectDescribeDocument describeExt = fillObjectDescribeExt(objectDescribeExt);
        putIfNotNull(result, describeExt, objectDescribeExt.getApiName());
        ObjectDescribeDocument whatDescribeExt = fillObjectDescribeExt(whatObjectDescribeExt);
        putIfNotNull(result, whatDescribeExt, whatObjectDescribeExt.getApiName());
        return result;
    }

    private Map<String, ObjectDescribeDocument> describeMap() {
        Map<String, ObjectDescribeDocument> result = Maps.newHashMap();
        Map<String, Integer> describeVersionMap = arg.getDescribeVersionMap();
        ObjectDescribeDocument describeDocument = ObjectDescribeDocument.handleDescribeCache(describeVersionMap, objectDescribeExt);
        putIfNotNull(result, describeDocument, objectDescribeExt.getApiName());

        ObjectDescribeDocument whatDescribeDocument = ObjectDescribeDocument.handleDescribeCache(describeVersionMap, whatObjectDescribeExt);
        putIfNotNull(result, whatDescribeDocument, whatObjectDescribeExt.getApiName());
        return result;
    }

    private void putIfNotNull(Map<String, ObjectDescribeDocument> result, ObjectDescribeDocument whatDescribeDocument, String apiName) {
        if (Objects.nonNull(whatDescribeDocument)) {
            result.put(apiName, whatDescribeDocument);
        }
    }

    private List<DocumentBaseEntity> getFieldWidthConfigs(List<DocumentBaseEntity> fieldListConfigWithWhatField, List<IHeadField> headFields) {
        Map<String, IHeadField> headFieldMap = headFields.stream()
                .collect(Collectors.toMap(IHeadField::getFieldName, Function.identity()));
        return fieldListConfigWithWhatField.stream()
                .map(LayoutRuleExt.FieldConfig::fromMap)
                .map(fieldConfig -> merge(fieldConfig, headFieldMap))
                .filter(this::filterFlowInvisibleFieldName)
                .map(headField -> new DocumentBaseEntity(headField.toMap()))
                .collect(Collectors.toList());
    }

    private HeadField merge(LayoutRuleExt.FieldConfig fieldConfig, Map<String, IHeadField> headFieldMap) {
        HeadField customHeadField = HeadField.fromFieldNameAndWidth(fieldConfig.getTuple().getKey(), fieldConfig.getWidth());
        customHeadField.setIsShow(BooleanUtils.isNotFalse((Boolean) fieldConfig.getTuple().getValue()));
        IHeadField headField = headFieldMap.get(customHeadField.getFieldName());
        if (Objects.isNull(headField)) {
            return customHeadField;
        }
        Map<String, Object> objectMap = Maps.newHashMap(((HeadField) headField).toMap());
        objectMap.putAll(customHeadField.toMap());
        return new HeadField(objectMap);
    }

    private List<Map<String, Object>> convertToDocument(List<IHeadField> headFields) {
        if (CollectionUtils.empty(headFields)) {
            return Collections.emptyList();
        }
        List<Map<String, Object>> result = Lists.newArrayList();
        for (IHeadField headField : headFields) {
            if (filterFlowInvisibleFieldName(headField)) {
                Map<String, Object> fieldConfig = Maps.newHashMap();
                fieldConfig.put(headField.getFieldName(), headField.isIsShow());
                fieldConfig.put("width", headField.getWidth());
                result.add(fieldConfig);
            }
        }
        return result;
    }

    private boolean filterFlowInvisibleFieldName(IHeadField headField) {
        if (!flowInvisibleFieldNameMap.containsKey(arg.getApiName())) {
            return true;
        }
        return !flowInvisibleFieldNameMap.get(arg.getApiName()).contains(headField.getFieldName());
    }

    private ObjectDescribeDocument fillObjectDescribeExt(IObjectDescribe objectDescribe) {
        final Map<String, Map<String, Object>> fieldExtMap = Maps.newHashMap();
        Map<String, Map<String, Object>> quoteFieldOptionExt = serviceFacade.fillQuoteFieldOption(objectDescribe);
        fieldExtMap.putAll(CollectionUtils.nullToEmpty(quoteFieldOptionExt));
        fillDescribeAttribute(objectDescribe, fieldExtMap);
        if (CollectionUtils.notEmpty(fieldExtMap)) {
            Map<String, Object> fieldsExt = Maps.newHashMap();
            fieldsExt.put("fields", fieldExtMap);
            return ObjectDescribeDocument.of(fieldsExt);
        }
        return null;
    }

    private void fillDescribeAttribute(IObjectDescribe objectDescribe, Map<String, Map<String, Object>> fieldExtMap) {
        ObjectDescribeExt.of(objectDescribe).getAllActiveRefFieldDescribesExcludeWhatField().forEach(refFieldDescribe -> {
            String refObjTargetApiName = FieldDescribeExt.of(refFieldDescribe).getRefObjTargetApiName();
            if (!AppFrameworkConfig.isSupportDisplayNameField(refObjTargetApiName) && !AppFrameworkConfig.supportTreeView(controllerContext.getTenantId(), refObjTargetApiName)) {
                return;
            }
            IObjectDescribe refDescribe = serviceFacade.findObjectWithoutCopyIfGray(controllerContext.getTenantId(), refObjTargetApiName);
            ObjectDescribeExt refDescribeExt = ObjectDescribeExt.of(refDescribe);
            if (refDescribeExt.isSupportDisplayName()) {
                FieldDescribeExt field = FieldDescribeExt.of(refFieldDescribe);
                field.addDisplayNameSwitch(refDescribeExt.isOpenDisplayName());
                Map<String, Object> fieldMap = field.toMap();
                fieldExtMap.put(field.getApiName(), fieldMap);
            }
            if (refDescribeExt.isSupportTreeViewObject()) {
                FieldDescribeExt field = FieldDescribeExt.of(refFieldDescribe);
                field.addTreeViewSwitch(refDescribeExt.isSupportTreeView());
                Map<String, Object> fieldMap = field.toMap();
                fieldExtMap.put(field.getApiName(), fieldMap);
            }
        });
    }

    private List<IOrderBy> getOrders() {
        if (Objects.isNull(flowTaskLayoutExt)) {
            return Collections.emptyList();
        }
        Optional<? extends BaseFlowTaskListComponentExt> flowTaskListMobileComponent;
        if (isMobile()) {
            flowTaskListMobileComponent = flowTaskLayoutExt.getFlowTaskListMobileComponent();
        } else {
            flowTaskListMobileComponent = flowTaskLayoutExt.getFlowTaskListComponent();
        }
        return flowTaskListMobileComponent
                .map(BaseFlowTaskListComponentExt::getOrders)
                .orElse(Collections.emptyList());
    }

    private List<CommonFilterField.FilterField> getFilterFields() {
        if (!isMobile()) {
            return null;
        }
        List<CommonFilterField.FilterField> filterFields = infraServiceFacade.findFilterFields(controllerContext.getUser(), objectDescribeExt, null, whatObjectDescribeExt.getApiName(), null);
        if (CollectionUtils.notEmpty(filterFields)) {
            return filterFields;
        }
        return Optional.ofNullable(flowTaskLayoutExt)
                .flatMap(FlowTaskLayoutExt::getFlowTaskListMobileComponent)
                .map(BaseFlowTaskListComponentExt::getFilterInfo)
                .map(BaseFlowTaskListComponentExt.FilterInfo::getFields)
                .map(fields -> fields.stream()
                        .map(it -> new CommonFilterField.FilterField(it, null, null))
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    private List<String> getQuickFilterField() {
        if (isMobile()) {
            return null;
        }
        return Optional.ofNullable(flowTaskLayoutExt)
                .flatMap(FlowTaskLayoutExt::getFlowTaskListComponent)
                .map(FlowTaskListComponentExt::getFilterInfo)
                .map(BaseFlowTaskListComponentExt.FilterInfo::getFields)
                .orElse(Collections.emptyList());
    }

    private List<DocumentBaseEntity> getFieldListConfigWithField(List<IHeadField> headFields) {
        if (isMobile()) {
            return Collections.emptyList();
        }
        List<Map<String, Object>> fieldListConfigWithWhatField = infraServiceFacade.findFieldListConfigWithWhatField(
                controllerContext.getUser(), objectDescribeExt.getApiName(),
                whatObjectDescribeExt.getApiName(), arg.getSessionKey());
        if (CollectionUtils.notEmpty(fieldListConfigWithWhatField)) {
            return layoutExt.getFieldShowList(fieldListConfigWithWhatField);
        }
        return layoutExt.getFieldShowList(convertToDocument(headFields), CollectionUtils.empty(headFields));
    }

    private List<IHeadField> getHeadFieldsFromFlowTaskListComponentExt() {
        return Optional.ofNullable(flowTaskLayoutExt)
                .flatMap(FlowTaskLayoutExt::getFlowTaskListComponent)
                .map(FlowTaskListComponentExt::getHeadFields)
                .orElse(Collections.emptyList());
    }

    private boolean isMobile() {
        return getLayoutAgentType() == LayoutAgentType.MOBILE;
    }

    private List<ISearchTemplate> getTemplates() {
        List<ISearchTemplate> templates = serviceFacade.findByDescribeApiNameAndExtendAttribute(objectDescribeExt.getApiName(), null, controllerContext.getUser());
        fillOrdersWithLayout(templates);
        return templates;
    }

    private void fillOrdersWithLayout(List<ISearchTemplate> templates) {
        List<IOrderBy> orders = getOrders();
        if (CollectionUtils.empty(orders)) {
            return;
        }
        for (ISearchTemplate template : templates) {
            if (!SearchTemplateExt.of(template).isCustomScene()) {
                template.setOrders(orders);
            }
        }
    }

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Arg {
        private Boolean includeLayout;
        private String apiName;
        private String layoutType;
        private String whatApiName;
        private String sessionKey;
        //端上缓存的describe的版本号
        @JsonAlias("describeVersionMap")
        private Map<String, Integer> describeVersionMap;
        private Boolean separationDescribe;
        //布局适用端(web或mobile)
        private String layoutAgentType;

        public boolean separationDescribe() {
            return BooleanUtils.isTrue(separationDescribe);
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private List<DocumentBaseEntity> fieldList;
        private ObjectDescribeDocument objectDescribe;
        private LayoutDocument layout;
        private List<QueryTemplateDocument> templates;
        /**
         * 用于引用字段筛选的拓展字段
         */
        private ObjectDescribeDocument objectDescribeExt;
        private List<DocumentBaseEntity> visibleFieldsWidth;

        private Map<String, ObjectDescribeDocument> describeMap;
        private Map<String, ObjectDescribeDocument> describeExtMap;


        /**
         * web 端快速筛选字段
         */
        private List<String> quickFilterField;
        /**
         * 移动端保存的筛选字段
         */
        private List<CommonFilterField.FilterField> filterFields;

        /**
         * 排序字段
         */
        private List<DocumentBaseEntity> orderFields;
    }
}
