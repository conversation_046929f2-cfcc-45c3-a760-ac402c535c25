package com.facishare.paas.appframework.core.predef.service;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.layout.UpdateComponentConfig;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2019/6/27
 */
@ServiceModule("component")
@Component
public class ObjectComponentService {

    @Autowired
    private ConfigService configService;

    @ServiceMethod("update_component_config")
    public UpdateComponentConfig.Result updateComponentConfig(UpdateComponentConfig.Arg arg, ServiceContext context) {
        if (Strings.isNullOrEmpty(arg.getObjectApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        String key = ComponentExt.getConfigKey(arg.getObjectApiName());
        //重置只需将用户配置删除，端上刷新页面
        if (arg.isReset()) {
            configService.deleteUserConfig(context.getUser(), key);
            return UpdateComponentConfig.Result.builder().success(true).build();
        }

        //去掉没有api_name或api_name重复的组件
        ComponentExt.removeEmptyOrDuplicateComponent(arg.getComponents());

        String config = configService.findUserConfig(context.getUser(), key);
        //对于web端的请求，将老配置中的is_hidden合并到新的配置中，因为web端还没有支持个人配置隐藏组件
//        mergeWithDbConfig(arg.getComponents(), config);
        String newConfig = JSON.toJSONString(arg.getComponents());
        if (Strings.isNullOrEmpty(config)) {
            configService.createUserConfig(context.getUser(), key, newConfig, ConfigValueType.JSON);
        } else {
            configService.updateUserConfig(context.getUser(), key, newConfig, ConfigValueType.JSON);
        }

        return UpdateComponentConfig.Result.builder().success(true).build();
    }

    private void mergeWithDbConfig(List<Map<String, Object>> newConfig, String dbConfig) {
        if (Strings.isNullOrEmpty(dbConfig)) {
            return;
        }
        if (RequestUtil.isMobileRequest()) {
            return;
        }
        Map<String, Map> dbConfigMap = JSON.parseArray(dbConfig, Map.class).stream().collect(Collectors.toMap(x -> (String) x.get(IComponent.NAME), x -> x, (x, y) -> x));
        Map<String, Map> newConfigMap = newConfig.stream().collect(Collectors.toMap(x -> (String) x.get(IComponent.NAME), x -> x, (x, y) -> x));
        newConfigMap.forEach((name, component) -> {
            if (!dbConfigMap.containsKey(name)) {
                return;
            }
            component.put(ComponentExt.IS_UNPOPULAR, dbConfigMap.get(name).getOrDefault(ComponentExt.IS_UNPOPULAR, false));
        });
    }

}
