package com.facishare.paas.appframework.metadata.layout.resource;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.layout.resource.module.LayoutView;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/6/27
 */
public interface LayoutResourceService {

    List<LayoutView> findLayoutViewResource(User user, IObjectDescribe objectDescribe, String layoutType);
}
