package com.facishare.paas.appframework.core.predef.facade;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.handler.incrementupdate.IncrementUpdateHandler;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;

import java.util.List;

public interface IncrementUpdateActionServiceFacade {
    List<IDuplicatedSearch> checkDuplicate(User user, IncrementUpdateHandler.Arg arg);
}
