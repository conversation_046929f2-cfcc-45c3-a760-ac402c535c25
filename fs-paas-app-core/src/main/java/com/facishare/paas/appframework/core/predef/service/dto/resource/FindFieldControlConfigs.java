package com.facishare.paas.appframework.core.predef.service.dto.resource;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.repository.model.MtResource;
import de.lab4inf.math.util.Strings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2024/3/22
 */
public interface FindFieldControlConfigs {

    @Data
    class Arg {
        private String describeApiName;

        public void validate() {
            if (Strings.isNullOrEmpty(describeApiName)) {
                throw new ValidateException("describeApiName cannot be empty");
            }
        }
    }

    @Data
    class Result {
        private List<FieldControlInfo> fieldControlInfos;

        public static Result fromMtResource(Collection<MtResource> resources) {
            Result result = new Result();
            if (CollectionUtils.empty(resources)) {
                return result;
            }
            List<FieldControlInfo> controlInfos = resources.stream()
                    .map(FieldControlInfo::fromMtResource)
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .collect(Collectors.toList());
            result.setFieldControlInfos(controlInfos);
            return result;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor(staticName = "of")
    class FieldControlInfo {
        private String fieldName;
        private String controlType;

        public static Optional<FieldControlInfo> fromMtResource(MtResource resource) {
            String controlLevel = resource.getControlLevel();
            String sourceType = resource.getSourceType();
            if (MtResource.CONTROL_LEVEL_UNCHANGEABLE.equals(controlLevel) && MtResource.SOURCE_TYPE_FIELD_CONTROL.equals(sourceType)) {
                return Optional.of(new FieldControlInfo(resource.getResourceValue(), resource.getSourceValue()));
            }
            return Optional.empty();
        }
    }

}
