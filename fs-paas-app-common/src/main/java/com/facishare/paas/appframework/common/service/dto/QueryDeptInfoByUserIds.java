package com.facishare.paas.appframework.common.service.dto;

import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2017/10/17
 */
public interface QueryDeptInfoByUserIds {

    @Data
    @Builder
    class Arg {
        private String tenantId;
        private String appId;
        private String userId;
        private List<String> idList;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<MainDeptInfo> result = new ArrayList<MainDeptInfo>();
        private boolean success;

        public List<String> getLeaderIds() {
            if (CollectionUtils.isNotEmpty(result)) {
                return result.stream().map(MainDeptInfo::getLeaderId).filter((leaderId) -> !"-1".equals(leaderId)).collect(Collectors.toList());
            }
            return Lists.newArrayList();
        }
    }

    @Data
    class MainDeptInfo {
        public static final String TYPE_DEPT = "dept";
        public static final String TYPE_ORG = "org";
        private String userId;
        private String userName;
        private String deptId;
        private String deptName;
        private String leaderId;
        private String leaderName;
        private String deptType;
    }


}
