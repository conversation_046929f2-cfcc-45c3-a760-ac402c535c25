package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.layout.factory.IComponentFactoryManager;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class MobileFlowTaskListLayoutBuilderTest {

  @Mock
  private ILayout layout;

  @Mock
  private IComponentFactoryManager componentFactoryManager;

  @Mock
  private DescribeLogicService describeLogicService;

  @Mock
  private LayoutLogicService layoutLogicService;

  @Mock
  private User user;

  @Mock
  private IObjectDescribe objectDescribe;

  @Mock
  private IObjectDescribe whatObjectDescribe;

  @BeforeEach
  void setUp() {
    // 设置基本的mock行为
    when(user.getTenantId()).thenReturn("test_tenant");
    when(objectDescribe.getApiName()).thenReturn("test_object");
    when(whatObjectDescribe.getApiName()).thenReturn("what_object");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试MobileFlowTaskListLayoutBuilder构造函数正常场景
   */
  @Test
  @DisplayName("正常场景 - 构造MobileFlowTaskListLayoutBuilder对象")
  void testMobileFlowTaskListLayoutBuilderConstructor_Success() {
    // 执行被测试方法
    MobileFlowTaskListLayoutBuilder builder = MobileFlowTaskListLayoutBuilder.builder()
        .layout(layout)
        .pageType(PageType.List)
        .componentFactoryManager(componentFactoryManager)
        .describeLogicService(describeLogicService)
        .layoutLogicService(layoutLogicService)
        .user(user)
        .objectDescribe(objectDescribe)
        .whatObjectDescribe(whatObjectDescribe)
        .build();

    // 验证结果
    assertNotNull(builder);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试不同PageType的构造
   */
  @Test
  @DisplayName("正常场景 - 测试不同PageType")
  void testMobileFlowTaskListLayoutBuilder_DifferentPageTypes() {
    // 测试Designer页面类型
    MobileFlowTaskListLayoutBuilder designerBuilder = MobileFlowTaskListLayoutBuilder.builder()
        .layout(layout)
        .pageType(PageType.Designer)
        .componentFactoryManager(componentFactoryManager)
        .describeLogicService(describeLogicService)
        .layoutLogicService(layoutLogicService)
        .user(user)
        .objectDescribe(objectDescribe)
        .whatObjectDescribe(whatObjectDescribe)
        .build();

    assertNotNull(designerBuilder);

    // 测试Detail页面类型
    MobileFlowTaskListLayoutBuilder detailBuilder = MobileFlowTaskListLayoutBuilder.builder()
        .layout(layout)
        .pageType(PageType.Detail)
        .componentFactoryManager(componentFactoryManager)
        .describeLogicService(describeLogicService)
        .layoutLogicService(layoutLogicService)
        .user(user)
        .objectDescribe(objectDescribe)
        .whatObjectDescribe(whatObjectDescribe)
        .build();

    assertNotNull(detailBuilder);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试null参数的场景
   */
  @Test
  @DisplayName("边界场景 - null参数处理")
  void testMobileFlowTaskListLayoutBuilder_NullParameters() {
    // 测试objectDescribe为null
    MobileFlowTaskListLayoutBuilder builder1 = MobileFlowTaskListLayoutBuilder.builder()
        .layout(layout)
        .pageType(PageType.List)
        .componentFactoryManager(componentFactoryManager)
        .describeLogicService(describeLogicService)
        .layoutLogicService(layoutLogicService)
        .user(user)
        .objectDescribe(null)
        .whatObjectDescribe(whatObjectDescribe)
        .build();

    assertNotNull(builder1);

    // 测试whatObjectDescribe为null
    MobileFlowTaskListLayoutBuilder builder2 = MobileFlowTaskListLayoutBuilder.builder()
        .layout(layout)
        .pageType(PageType.List)
        .componentFactoryManager(componentFactoryManager)
        .describeLogicService(describeLogicService)
        .layoutLogicService(layoutLogicService)
        .user(user)
        .objectDescribe(objectDescribe)
        .whatObjectDescribe(null)
        .build();

    assertNotNull(builder2);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试Builder模式的完整性
   */
  @Test
  @DisplayName("正常场景 - 测试Builder模式完整性")
  void testMobileFlowTaskListLayoutBuilder_BuilderPattern() {
    // 测试Builder的链式调用
    assertDoesNotThrow(() -> {
      MobileFlowTaskListLayoutBuilder builder = MobileFlowTaskListLayoutBuilder.builder()
          .layout(layout)
          .pageType(PageType.List)
          .componentFactoryManager(componentFactoryManager)
          .describeLogicService(describeLogicService)
          .layoutLogicService(layoutLogicService)
          .user(user)
          .objectDescribe(objectDescribe)
          .whatObjectDescribe(whatObjectDescribe)
          .build();
      
      assertNotNull(builder);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试基本功能验证
   */
  @Test
  @DisplayName("正常场景 - 验证基本功能")
  void testMobileFlowTaskListLayoutBuilder_BasicFunctionality() {
    // 执行被测试方法
    MobileFlowTaskListLayoutBuilder builder = MobileFlowTaskListLayoutBuilder.builder()
        .layout(layout)
        .pageType(PageType.List)
        .componentFactoryManager(componentFactoryManager)
        .describeLogicService(describeLogicService)
        .layoutLogicService(layoutLogicService)
        .user(user)
        .objectDescribe(objectDescribe)
        .whatObjectDescribe(whatObjectDescribe)
        .build();

    // 验证基本功能
    assertNotNull(builder);
    assertDoesNotThrow(() -> {
      builder.toString();
    });
    assertDoesNotThrow(() -> {
      builder.hashCode();
    });
    assertDoesNotThrow(() -> {
      builder.equals(builder);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试对象状态一致性
   */
  @Test
  @DisplayName("正常场景 - 测试对象状态一致性")
  void testMobileFlowTaskListLayoutBuilder_StateConsistency() {
    // 创建两个相同配置的对象
    MobileFlowTaskListLayoutBuilder builder1 = MobileFlowTaskListLayoutBuilder.builder()
        .layout(layout)
        .pageType(PageType.List)
        .componentFactoryManager(componentFactoryManager)
        .describeLogicService(describeLogicService)
        .layoutLogicService(layoutLogicService)
        .user(user)
        .objectDescribe(objectDescribe)
        .whatObjectDescribe(whatObjectDescribe)
        .build();

    MobileFlowTaskListLayoutBuilder builder2 = MobileFlowTaskListLayoutBuilder.builder()
        .layout(layout)
        .pageType(PageType.List)
        .componentFactoryManager(componentFactoryManager)
        .describeLogicService(describeLogicService)
        .layoutLogicService(layoutLogicService)
        .user(user)
        .objectDescribe(objectDescribe)
        .whatObjectDescribe(whatObjectDescribe)
        .build();

    // 验证对象独立性
    assertNotNull(builder1);
    assertNotNull(builder2);
    assertNotSame(builder1, builder2);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试异常处理能力
   */
  @Test
  @DisplayName("异常场景 - 测试异常处理")
  void testMobileFlowTaskListLayoutBuilder_ExceptionHandling() {
    // 测试在异常情况下的处理
    assertDoesNotThrow(() -> {
      MobileFlowTaskListLayoutBuilder builder = MobileFlowTaskListLayoutBuilder.builder()
          .layout(layout)
          .pageType(PageType.List)
          .componentFactoryManager(componentFactoryManager)
          .describeLogicService(describeLogicService)
          .layoutLogicService(layoutLogicService)
          .user(user)
          .objectDescribe(objectDescribe)
          .whatObjectDescribe(whatObjectDescribe)
          .build();

      // 验证对象在异常情况下的稳定性
      assertNotNull(builder);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试最小参数集合
   */
  @Test
  @DisplayName("正常场景 - 测试最小参数集合")
  void testMobileFlowTaskListLayoutBuilder_MinimalParameters() {
    // 测试最小参数集合
    MobileFlowTaskListLayoutBuilder builder = MobileFlowTaskListLayoutBuilder.builder()
        .layout(layout)
        .pageType(PageType.List)
        .user(user)
        .build();

    assertNotNull(builder);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试边界条件
   */
  @Test
  @DisplayName("边界场景 - 测试边界条件")
  void testMobileFlowTaskListLayoutBuilder_BoundaryConditions() {
    // 测试所有参数为null的情况
    MobileFlowTaskListLayoutBuilder builder = MobileFlowTaskListLayoutBuilder.builder()
        .layout(null)
        .pageType(null)
        .componentFactoryManager(null)
        .describeLogicService(null)
        .layoutLogicService(null)
        .user(null)
        .objectDescribe(null)
        .whatObjectDescribe(null)
        .build();

    // 验证在边界条件下对象仍能正常创建
    assertNotNull(builder);
  }
}
