package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.resource.*;
import com.facishare.paas.appframework.metadata.mtresource.ConfigurationPackageResourceLogicService;
import com.facishare.paas.appframework.metadata.mtresource.IMtResourceService;
import com.facishare.paas.appframework.metadata.mtresource.model.ConfigurationPackageResource;
import com.facishare.paas.appframework.metadata.mtresource.model.FieldControlInfoHelper;
import com.facishare.paas.appframework.metadata.repository.model.MtResource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by zhaooju on 2023/7/12
 */
@Slf4j
@Service
@ServiceModule("resource")
public class ObjectResourceService {

    @Autowired
    private ConfigurationPackageResourceLogicService configurationPackageResourceLogicService;

    @Autowired
    private IMtResourceService mtResourceService;

    @ServiceMethod("find")
    public FindObjectResource.Result find(ServiceContext serviceContext, FindObjectResource.Arg arg) {
        List<ConfigurationPackageResource> resources = configurationPackageResourceLogicService.find(serviceContext.getUser(),
                arg.getResourceParentValue(), arg.getResourceType(), arg.getResourceQuery(), arg.getSourceType(), arg.getSourceValue());
        FindObjectResource.Result result = new FindObjectResource.Result();
        result.setResult(resources);
        return result;
    }

    @ServiceMethod("modify_resource")
    public ModifyResource.Result modifyResource(ServiceContext serviceContext, ModifyResource.Arg arg) {
        configurationPackageResourceLogicService.modifyResource(serviceContext.getUser(), arg.getResources());
        return new ModifyResource.Result();
    }

    @ServiceMethod("count")
    public CountByResourceType.Result countByResourceType(ServiceContext serviceContext, CountByResourceType.Arg arg) {
        Integer total = configurationPackageResourceLogicService.countByResourceType(serviceContext.getUser(),
                arg.getResourceParentValue(), arg.getResourceType());
        return CountByResourceType.Result.of(total);
    }

    @ServiceMethod("findFieldControlConfigs")
    public FindFieldControlConfigs.Result findFieldControlConfigs(ServiceContext serviceContext, FindFieldControlConfigs.Arg arg) {
        arg.validate();
        List<MtResource> resourceList = configurationPackageResourceLogicService.findFieldControlConfigs(serviceContext.getUser(), arg.getDescribeApiName());
        return FindFieldControlConfigs.Result.fromMtResource(resourceList);
    }

    @ServiceMethod("updateFieldControlConfigs")
    public UpdateFieldControlConfigs.Result updateFieldControlConfigs(ServiceContext serviceContext, UpdateFieldControlConfigs.Arg arg) {
        FieldControlInfoHelper helper = arg.toFieldControlInfoHelper();
        configurationPackageResourceLogicService.updateFieldControlConfigs(serviceContext.getUser(), arg.getDescribeApiName(), helper);
        return new UpdateFieldControlConfigs.Result();
    }

}
