package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.metadata.search.SearchQuery
import com.facishare.paas.metadata.api.search.IFilter
import com.facishare.paas.metadata.impl.search.Filter
import com.facishare.paas.metadata.impl.search.Operator
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import spock.lang.Specification
import spock.lang.Unroll

class SearchTemplateQueryExtTest extends Specification {
    def "ValidateWheresAndFilters"() {
        given:
        def value = 1
        when:
        // 这是一个示例测试，不需要具体逻辑
        def result = value == 1
        then:
        result
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理过滤器分组的正常场景，验证是否正确设置filterGroup
     */
    def "processFilterGroupTest_Normal"() {
        given: "准备测试数据"
        def query = new SearchTemplateQuery()
        def filters = [
                new Filter(fieldName: "field1", fieldValues: ["value1"]),
                new Filter(fieldName: "field2", fieldValues: ["value2"])
        ]
        query.setFilters(filters)
        query.setPattern("1 and 2")
        def searchTemplateQueryExt = SearchTemplateQueryExt.of(query)

        when: "执行处理过滤器分组"
        def result = searchTemplateQueryExt.processFilterGroupByPatternExpression()

        then: "验证结果"
        result != null
        result.filters.size() == 2
        result.filters[0].filterGroup == "1"
        result.filters[1].filterGroup == "1"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理过滤器分组的异常场景，包括空filters和空pattern
     */
    @Unroll
    def "processFilterGroupTest_Empty_#caseName"() {
        given: "准备测试数据"
        def query = new SearchTemplateQuery()
        query.setFilters(filters)
        query.setPattern(pattern)
        def searchTemplateQueryExt = SearchTemplateQueryExt.of(query)

        when: "执行处理过滤器分组"
        def result = searchTemplateQueryExt.processFilterGroupByPatternExpression()

        then: "验证结果"
        result != null
        result.filters == filters

        where:
        caseName       | filters                                                    | pattern
        "EmptyFilter"  | []                                                         | "1 and 2"
        "EmptyPattern" | [new Filter(fieldName: "field1", fieldValues: ["value1"])] | ""
        "BothEmpty"    | []                                                         | ""
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理复杂的过滤器分组场景，包括AND和OR组合
     */
    def "processFilterGroupTest_ComplexPattern"() {
        given: "准备测试数据"
        def query = new SearchTemplateQuery()
        def filters = [
                new Filter(fieldName: "field1", fieldValues: ["value1"]),
                new Filter(fieldName: "field2", fieldValues: ["value2"]),
                new Filter(fieldName: "field3", fieldValues: ["value3"])
        ]
        query.setFilters(filters)
        query.setPattern("(1 and 2) or 3")
        def searchTemplateQueryExt = SearchTemplateQueryExt.of(query)

        when: "执行处理过滤器分组"
        def result = searchTemplateQueryExt.processFilterGroupByPatternExpression()

        then: "验证结果"
        result != null
        result.filters.size() == 3
        result.filters[0].filterGroup == "1"
        result.filters[1].filterGroup == "1"
        result.filters[2].filterGroup == "2"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理无效pattern的场景
     */
    def "processFilterGroupTest_InvalidPattern"() {
        given: "准备测试数据"
        def query = new SearchTemplateQuery()
        def filters = [
                new Filter(fieldName: "field1", fieldValues: ["value1"]),
                new Filter(fieldName: "field2", fieldValues: ["value2"])
        ]
        query.setFilters(filters)
        query.setPattern("invalid pattern")
        def searchTemplateQueryExt = SearchTemplateQueryExt.of(query)

        when: "执行处理过滤器分组"
        def result = searchTemplateQueryExt.processFilterGroupByPatternExpression()

        then: "验证结果"
        result != null
        result.filters.size() == 2
        result.filters.every { it.filterGroup == "" }
    }

    // ===== parsePatternToSearchQuery 方法的测试用例 =====

    /**
     * 测试 parsePatternToSearchQuery 的正常场景
     * 验证是否正确解析pattern并替换为实际的filter
     */
    def "parsePatternToSearchQuery_Normal"() {
        given: "准备测试数据"
        def query = new SearchTemplateQuery()
        def filters = [
                createFilter("field1", "value1", Operator.EQ),
                createFilter("field2", "value2", Operator.EQ)
        ]
        query.setFilters(filters)
        query.setPattern("1 and 2")
        def searchTemplateQueryExt = SearchTemplateQueryExt.of(query)

        when: "执行解析pattern"
        def result = searchTemplateQueryExt.parsePatternToSearchQuery()

        then: "验证结果"
        result != null
        result.getConnector() == SearchQuery.Connector.AND
        result.getSearchQueryContainer().size() == 2
        result.getSearchQueryContainer()[0].getFilter().getFieldName() == "field1"
        result.getSearchQueryContainer()[1].getFilter().getFieldName() == "field2"
    }

    /**
     * 测试 parsePatternToSearchQuery 的OR场景
     */
    def "parsePatternToSearchQuery_OrPattern"() {
        given: "准备测试数据"
        def query = new SearchTemplateQuery()
        def filters = [
                createFilter("field1", "value1", Operator.EQ),
                createFilter("field2", "value2", Operator.NEQ)
        ]
        query.setFilters(filters)
        query.setPattern("1 or 2")
        def searchTemplateQueryExt = SearchTemplateQueryExt.of(query)

        when: "执行解析pattern"
        def result = searchTemplateQueryExt.parsePatternToSearchQuery()

        then: "验证结果"
        result != null
        result.getConnector() == SearchQuery.Connector.OR
        result.getSearchQueryContainer().size() == 2
        result.getSearchQueryContainer()[0].getFilter().getFieldName() == "field1"
        result.getSearchQueryContainer()[1].getFilter().getFieldName() == "field2"
        result.getSearchQueryContainer()[1].getFilter().getOperator() == Operator.NEQ
    }

    /**
     * 测试 parsePatternToSearchQuery 的复杂嵌套场景
     */
    def "parsePatternToSearchQuery_ComplexNested"() {
        given: "准备测试数据"
        def query = new SearchTemplateQuery()
        def filters = [
                createFilter("field1", "value1", Operator.EQ),
                createFilter("field2", "value2", Operator.EQ),
                createFilter("field3", "value3", Operator.EQ)
        ]
        query.setFilters(filters)
        query.setPattern("(1 and 2) or 3")
        def searchTemplateQueryExt = SearchTemplateQueryExt.of(query)

        when: "执行解析pattern"
        def result = searchTemplateQueryExt.parsePatternToSearchQuery()

        then: "验证结果"
        result != null
        result.getConnector() == SearchQuery.Connector.OR
        result.getSearchQueryContainer().size() == 2

        // 验证第一个子节点是AND关系
        def firstChild = result.getSearchQueryContainer()[0]
        firstChild.getConnector() == SearchQuery.Connector.AND
        firstChild.getSearchQueryContainer().size() == 2
        firstChild.getSearchQueryContainer()[0].getFilter().getFieldName() == "field1"
        firstChild.getSearchQueryContainer()[1].getFilter().getFieldName() == "field2"

        // 验证第二个子节点是单个filter
        def secondChild = result.getSearchQueryContainer()[1]
        secondChild.isFilterNode()
        secondChild.getFilter().getFieldName() == "field3"
    }

    /**
     * 测试 parsePatternToSearchQuery 的异常场景
     */
    @Unroll
    def "parsePatternToSearchQuery_EdgeCases_#caseName"() {
        given: "准备测试数据"
        def query = new SearchTemplateQuery()
        query.setFilters(filters)
        query.setPattern(pattern)
        def searchTemplateQueryExt = SearchTemplateQueryExt.of(query)

        when: "执行解析pattern"
        def result = searchTemplateQueryExt.parsePatternToSearchQuery()

        then: "验证结果"
        result == expectedResult

        where:
        caseName       | filters                                         | pattern   | expectedResult
        "EmptyPattern" | [createFilter("field1", "value1", Operator.EQ)] | ""        | null
        "BlankPattern" | [createFilter("field1", "value1", Operator.EQ)] | "   "     | null
        "EmptyFilters" | []                                              | "1 and 2" | null
        "NullPattern"  | [createFilter("field1", "value1", Operator.EQ)] | null      | null
    }

    /**
     * 测试 parsePatternToSearchQuery 的无效pattern场景
     */
    def "parsePatternToSearchQuery_InvalidPattern"() {
        given: "准备测试数据"
        def query = new SearchTemplateQuery()
        def filters = [
                createFilter("field1", "value1", Operator.EQ),
                createFilter("field2", "value2", Operator.EQ)
        ]
        query.setFilters(filters)
        query.setPattern("invalid syntax")
        def searchTemplateQueryExt = SearchTemplateQueryExt.of(query)

        when: "执行解析pattern"
        def result = searchTemplateQueryExt.parsePatternToSearchQuery()

        then: "验证结果"
        result == null
    }

    /**
     * 测试 parsePatternToSearchQuery 的索引越界场景
     */
    def "parsePatternToSearchQuery_IndexOutOfBounds"() {
        given: "准备测试数据"
        def query = new SearchTemplateQuery()
        def filters = [
                createFilter("field1", "value1", Operator.EQ)
        ]
        query.setFilters(filters)
        query.setPattern("1 and 3")  // 索引3超出范围
        def searchTemplateQueryExt = SearchTemplateQueryExt.of(query)

        when: "执行解析pattern"
        def result = searchTemplateQueryExt.parsePatternToSearchQuery()

        then: "验证结果"
        result != null
        // 当只剩一个有效filter时，应该直接返回filter节点
        result.isFilterNode()
        result.getFilter().getFieldName() == "field1"
        result.getFilter().getFieldValues()[0] == "value1"
        result.getFilter().getOperator() == Operator.EQ
    }

    /**
     * 测试单个filter的场景
     */
    def "parsePatternToSearchQuery_SingleFilter"() {
        given: "准备测试数据"
        def query = new SearchTemplateQuery()
        def filters = [
                createFilter("field1", "value1", Operator.EQ)
        ]
        query.setFilters(filters)
        query.setPattern("1")
        def searchTemplateQueryExt = SearchTemplateQueryExt.of(query)

        when: "执行解析pattern"
        def result = searchTemplateQueryExt.parsePatternToSearchQuery()

        then: "验证结果"
        result != null
        result.isFilterNode()
        result.getFilter().getFieldName() == "field1"
        result.getFilter().getFieldValues()[0] == "value1"
        result.getFilter().getOperator() == Operator.EQ
    }

    /**
     * 测试 parsePatternToSearchQuery 的部分索引越界场景（保持AND结构）
     */
    def "parsePatternToSearchQuery_PartialIndexOutOfBounds"() {
        given: "准备测试数据"
        def query = new SearchTemplateQuery()
        def filters = [
                createFilter("field1", "value1", Operator.EQ),
                createFilter("field2", "value2", Operator.EQ)
        ]
        query.setFilters(filters)
        query.setPattern("1 and 2 and 5")  // 索引5超出范围
        def searchTemplateQueryExt = SearchTemplateQueryExt.of(query)

        when: "执行解析pattern"
        def result = searchTemplateQueryExt.parsePatternToSearchQuery()

        then: "验证结果"
        result != null
        // 应该保持AND结构，包含两个有效的filter
        result.getConnector() == SearchQuery.Connector.AND
        result.getSearchQueryContainer().size() == 2
        result.getSearchQueryContainer()[0].getFilter().getFieldName() == "field1"
        result.getSearchQueryContainer()[1].getFilter().getFieldName() == "field2"
    }

    /**
     * 辅助方法：创建Filter对象
     */
    private static IFilter createFilter(String fieldName, String fieldValue, Operator operator) {
        def filter = new Filter()
        filter.setFieldName(fieldName)
        filter.setFieldValues([fieldValue])
        filter.setOperator(operator)
        return filter
    }
}
