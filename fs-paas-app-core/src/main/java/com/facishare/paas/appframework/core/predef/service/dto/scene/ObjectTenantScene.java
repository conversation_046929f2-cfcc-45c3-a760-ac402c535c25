package com.facishare.paas.appframework.core.predef.service.dto.scene;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.facishare.paas.appframework.metadata.dto.scene.SceneDTO;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * create by z<PERSON><PERSON> on 2019/04/30
 */
public interface ObjectTenantScene {
    @Data
    @JsonNaming(SnakeCaseStrategy.class)
    class Arg {
        private SceneDTO scene;
        private String describeApiName;
        private String extendAttribute;
        @JsonAlias("appId")
        private String appId;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    @JsonNaming(SnakeCaseStrategy.class)
    class Result {
        private IScene scene;
    }
}
