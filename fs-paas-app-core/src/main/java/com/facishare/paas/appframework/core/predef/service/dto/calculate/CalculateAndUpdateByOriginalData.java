package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Created by zhouwr on 2023/9/13.
 */
public interface CalculateAndUpdateByOriginalData {
    @Data
    class Arg {
        private String objectApiName;
        private String dataId;
        private Map<String, Object> beforeData;
    }

    @Data
    class Result {
    }
}
