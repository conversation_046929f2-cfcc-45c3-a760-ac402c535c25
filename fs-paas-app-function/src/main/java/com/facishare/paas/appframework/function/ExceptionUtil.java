package com.facishare.paas.appframework.function;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;

/**
 * Created by liyiguang on 2019/3/12.
 */
@Slf4j
public abstract class ExceptionUtil {
    public static String formatError(String error, int lineNumber) {
        if (error == null) {
            return error;
        }

        StringBuilder stringBuilder = new StringBuilder();
        try {
            BufferedReader reader = new BufferedReader(new StringReader(error));
            for (int i = 0; i < lineNumber; i++) {
                String line = reader.readLine();
                if (line == null) {
                    break;
                } else {
                    stringBuilder.append(line);
                    if (i < lineNumber - 1) {
                        stringBuilder.append('\n');
                    }

                }
            }
        } catch (IOException e) {
            log.error("", e);
        }

        return stringBuilder.toString();
    }
}
