package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.enterprise.common.util.PrivilegeFieldsUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.config.*;
import com.facishare.paas.appframework.metadata.dto.scene.*;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.initscene.SceneInitManager;
import com.facishare.paas.appframework.metadata.initscene.SceneInitProvider;
import com.facishare.paas.appframework.metadata.scene.ValidatorUtil;
import com.facishare.paas.appframework.metadata.scene.validate.SystemSceneVD;
import com.facishare.paas.appframework.metadata.scene.validate.TenantSceneVD;
import com.facishare.paas.appframework.metadata.scene.validator.Preconditions;
import com.facishare.paas.appframework.metadata.scene.wipe.WipeMessage;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.search.Ranges;
import com.facishare.paas.metadata.api.search.Ranges.OutAll;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.api.service.IFieldListConfigService;
import com.facishare.paas.metadata.api.service.ISearchTemplateService;
import com.facishare.paas.metadata.common.MetadataContext;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.SearchTemplate;
import com.facishare.paas.metadata.support.GrayHelper;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.lambda.tuple.Tuple;
import org.jooq.lambda.tuple.Tuple3;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.crm.userdefobj.DefObjConstants.UDOBJ;
import static com.facishare.paas.appframework.metadata.SearchTemplateExt.DEFAULT_NEW_SCENES;
import static com.facishare.paas.appframework.metadata.SearchTemplateExt.DefaultScene.*;
import static com.facishare.paas.appframework.metadata.SearchTemplateExt.SceneType.DEFAULT_SCENE;

/**
 * create by zhaoju on 2019/04/30
 */
@Slf4j
@Service("sceneLogicService")
public class SceneLogicServiceImpl implements SceneLogicService {

    @Autowired
    private ISearchTemplateService searchTemplateService;
    @Autowired
    private SceneInitManager sceneInitManager;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private LayoutLogicService layoutLogicService;
    @Autowired
    private LogService logService;
    @Autowired
    private GrayHelper grayHelper;
    @Autowired
    private OptionalFeaturesService optionalFeaturesService;
    @Autowired
    private IFieldListConfigService fieldListConfigService;
    @Autowired
    private AppDefaultRocketMQProducer wipeCustomListSettingsProducer;
    @Autowired
    private ObjectControlLevelLogicService objectControlLevelLogicService;

    @Override
    public List<IScene> findScenes(String describeApiName, User user, String extendAttribute) {
        return findScenes(describeApiName, user, extendAttribute, null);
    }
    /*
     * @param isActive: true 查询启用, false 反之. null表示全部
     */

    @Override
    public List<IScene> findScenes(String describeApiName, User user, String extendAttribute, Boolean isActive) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
        List<ISearchTemplate> searchTemplates;
        try {
            MetadataContext metadataContext = buildMetaDataContext(user);
            List<String> extendAttributeList = null;
            if (!Strings.isNullOrEmpty(extendAttribute)) {
                extendAttributeList = Arrays.asList(extendAttribute.split(","));
            }
            searchTemplates = searchTemplateService.findForTenantAndAttributeList(user.getTenantId(),
                    describeApiName, null, null, extendAttributeList, metadataContext);
            // 由于数据库active有三种情况 true/false/null , null 相当于 true, 需要在这里过滤而非作为参数
            if (Objects.nonNull(isActive)) {
                searchTemplates.removeIf(isActive
                        ? x -> BooleanUtils.isFalse(x.getIsAvailable())
                        : x -> BooleanUtils.isNotFalse(x.getIsAvailable()));
            }
        } catch (MetadataServiceException e) {
            log.warn("findScenes error. describeApiName=>{}, tenant=>{}, user=>{}",
                    describeApiName, user.getTenantId(), JSON.toJSONString(user), e);
            throw new MetaDataBusinessException(e);
        }
        List<ISearchTemplate> templates = SearchTemplateExt.searchTemplatesOrderByTypeAndCreateTime(searchTemplates);
        return getSceneList(user, describe, templates);
    }

    @Override
    public IScene findSceneByApiName(String describeApiName, String sceneApiName, String tenantId, String extendAttribute) {
        ISearchTemplate searchTemplate;
        try {
            MetadataContext metadataContext = buildMetaDataContext(User.systemUser(tenantId));
            searchTemplate = searchTemplateService.findForTenantByApiName(tenantId, describeApiName, sceneApiName, extendAttribute, metadataContext);
        } catch (MetadataServiceException e) {
            log.warn("findSceneByApiName error, describeApiName=>{}, sceneApiName=>{}, tenantId=>{}", describeApiName, sceneApiName, tenantId, e);
            throw new MetaDataBusinessException(e);
        }
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName);
        SceneDTO sceneInfo = SceneDTO.fromSearchTemplate(searchTemplate, describe);
        return sceneInfo;
    }

    @Override
    public IScene findSceneByApiName(String describeApiName, String sceneApiName, User user, String extendAttribute) {
        return findSceneByApiName(user, describeApiName, sceneApiName, extendAttribute, null);
    }

    @Override
    public IScene findSceneByApiName(User user, String describeApiName, String sceneApiName, String extendAttribute, String appId) {
        ISearchTemplate searchTemplate;
        try {
            MetadataContext metadataContext = buildMetaDataContext(user);
            searchTemplate = searchTemplateService.findForTenantByApiName(user.getTenantId(), describeApiName, sceneApiName, extendAttribute, metadataContext);
            if (Objects.isNull(searchTemplate)) {
                searchTemplate = searchTemplateService.getSysSearchTemplateByApiName(sceneApiName,
                        metadataContext, describeApiName, extendAttribute);
            }
        } catch (MetadataServiceException e) {
            log.warn("findSceneByApiName error, describeApiName=>{}, sceneApiName=>{}, tenantId=>{}",
                    describeApiName, sceneApiName, user.getTenantId(), e);
            throw new MetaDataBusinessException(e);
        }
        if (Objects.isNull(searchTemplate)) {
            return null;
        }

        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
        SearchTemplateExt templateExt = SearchTemplateExt.of(searchTemplate);
        // 第一次编辑预置场景时，默认展示的字段和顺序
        if (templateExt.isDefaultScene() && templateExt.isUseFieldList() && CollectionUtils.empty(searchTemplate.getFieldList())) {
            List<String> fieldList = getFieldList(user, describe, appId);
            Set<String> fieldSet = Sets.newLinkedHashSet(CollectionUtils.nullToEmpty(fieldList));
            templateExt.handleHeadFieldList(fieldSet, Collections.emptyList(), describe);
        }
        //默认系统场景不下发wheres
        if (templateExt.isDefaultScene() &&
                (SearchTemplateExt.DEFAULT_UDOBJ_SCENES.contains(templateExt.getApiName())
                        || DEFAULT_NEW_SCENES.contains(templateExt.getApiName()))) {
            templateExt.setWheres(new ArrayList<>());
        } else {
            OptionalFeaturesSwitchDTO optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), describe);
            List<Wheres> wheres = templateExt.getWheres();
            wheres.forEach(where -> CollectionUtils.nullToEmpty(where.getFilters())
                    .removeIf(filter -> !optionalFeaturesSwitch.getIsRelatedTeamEnabled() && filter.getFieldName().contains(ObjectDataExt.RELEVANT_TEAM))
            );
            templateExt.setWheres(wheres);
            if (!optionalFeaturesSwitch.getIsRelatedTeamEnabled() && StringUtils.equalsAny(searchTemplate.getBaseSceneApiName(), INVOLVED_WITH_ME.getApiName(), INVOLVED_WITH_SUB.getApiName())) {
                searchTemplate.setBaseSceneApiName(ALL.getApiName());
            }
        }
        //补充数据范围和label
        templateExt.handleBaseDefaultScene();
        String label = fillBaseSceneLabel(searchTemplate.getBaseSceneApiName(), describeApiName, user, extendAttribute);
        SceneDTO sceneInfo = SceneDTO.fromSearchTemplate(searchTemplate, describe, label);
        if (Objects.nonNull(sceneInfo)) {
            OptionalFeaturesSwitchDTO optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), describe);
            CollectionUtils.nullToEmpty(sceneInfo.getHeadField())
                    .removeIf(field -> !optionalFeaturesSwitch.getIsRelatedTeamEnabled() && StringUtils.equals(field, ObjectDataExt.RELEVANT_TEAM));
        }
        return sceneInfo;
    }

    /**
     * 预置对象按产品给的字段顺序展示
     * 自定义对象按默认布局中的字段顺序展示
     *
     * @param user
     * @param describe
     * @return
     */
    private List<String> getFieldList(User user, IObjectDescribe describe, String appId) {
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(user, appId);
        ILayout defaultLayout = layoutLogicService.findObjectLayoutWithType(layoutContext, "", describe, ILayout.LIST_LAYOUT_TYPE, null);
        // 补充相关团队字段
        LayoutExt layoutExt = LayoutExt.of(defaultLayout);
        if (!ObjectDescribeExt.of(describe).isSlaveObject()) {
            layoutExt.fillRelevantTeamField();
        }
        List<String> fieldList = layoutExt.getFieldList();
        ObjectDescribeExt.of(describe).addDateTimeRangeEndTime(fieldList);
        List<String> orders = PrivilegeFieldsUtils.objectApiName2FiledOrderInfo.get(describe.getApiName());
        if (CollectionUtils.empty(orders)) {
            return fieldList;
        }
        return CollectionUtils.sortByGivenOrder(fieldList, orders, x -> x);
    }

    public String fillBaseSceneLabel(String baseSceneApiName, String describeApiName, User user, String extendAttribute) {
        if (Objects.isNull(baseSceneApiName)) {
            return null;
        }
        List<IScene> baseScenes = findBaseScenes(describeApiName, extendAttribute, user);
        for (IScene base : baseScenes) {
            if (baseSceneApiName.equals(base.getApiName())) {
                return base.getDisplayName();
            }
        }
        return null;
    }

    @Override
    public IScene findCustomSceneById(String describeApiName, String sceneId, String tenantId) {
        ISearchTemplate searchTemplate;
        try {
            searchTemplate = searchTemplateService.findById(sceneId, describeApiName, tenantId);
        } catch (MetadataServiceException e) {
            log.warn("findCustomSceneById error,tenant:{},describeApiName:{},id:{}", tenantId, describeApiName, sceneId, e);
            throw new MetaDataBusinessException(e);
        }
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName);
        SceneDTO sceneInfo = SceneDTO.fromSearchTemplate(searchTemplate, describe);
        return sceneInfo;
    }

    @Override
    public ITenantScene createTenantScene(ITenantScene tenantScene, String describeApiName, String extendAttribute, User user) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
        ValidatorUtil.validate(TenantSceneVD.fromCreate(tenantScene, describe));
        ISearchTemplate searchTemplate = tenantScene.toSearchTemplate();
        searchTemplate.setCreatedBy(user.getUserId());
        searchTemplate.setPackage(DefObjConstants.PACKAGE_NAME_CRM);
        searchTemplate.setTenantId(user.getTenantId());
        searchTemplate.setType(SearchTemplateExt.TENANT_SCENE);
        searchTemplate.setObjectDescribeId(describe.getId());
        searchTemplate.setIsAvailable(true);
        searchTemplate.setExtendAttribute(extendAttribute);
        searchTemplate.setIsCascade(tenantScene.isCascade());
        try {
            ISearchTemplate created = searchTemplateService.createForTenant(searchTemplate, buildMetaDataContext(user));
            logService.log(user, EventType.ADD, ActionType.CREATE_TENANT_SCENE, describeApiName,
                    I18N.text(I18NKey.TENANT_SCENE_SPECIFY, created.getLabel() + created.getApiName()));
        } catch (MetadataServiceException e) {
            log.warn("createTenantScene error. describeApiName=>{}, tenant=>{}, user=>{}, tenantScene=>{}",
                    describeApiName, user.getTenantId(), JSON.toJSONString(user), searchTemplate.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
        return tenantScene;
    }

    @Override
    public ITenantScene updateTenantScene(ITenantScene tenantScene, String describeApiName, User user) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
        ValidatorUtil.validate(TenantSceneVD.fromUpdate(tenantScene, describe));
        ISearchTemplate searchTemplate = tenantScene.toSearchTemplate(describe);
        // 更新最后修改人和最后修改时间
        searchTemplate.setLastModifiedBy(user.getUserId());
        searchTemplate.setLastModifiedTime(System.currentTimeMillis());
        try {
            MetadataContext metadataContext = buildMetaDataContext(user);
            // 处理更新后顺序丢失的问题
            ISearchTemplate tenantSceneInDB = searchTemplateService.findForTenantByApiName(user.getTenantId(),
                    tenantScene.getObjectDescribeApiName(), tenantScene.getApiName(), tenantScene.getExtendAttribute(), metadataContext);
            searchTemplate.setDefaultScenePriority(tenantSceneInDB.getDefaultScenePriority());
            ISearchTemplate update = searchTemplateService.updateForTenant(searchTemplate, metadataContext);
            logService.log(user, EventType.MODIFY, ActionType.UPDATE_TENANT_SCENE, describeApiName,
                    I18N.text(I18NKey.TENANT_SCENE_SPECIFY, update.getLabel() + tenantScene.getApiName()));
        } catch (MetadataServiceException e) {
            log.warn("updateTenantScene error. describeApiName=>{}, tenant=>{}, user=>{}, tenantScene=>{}",
                    describeApiName, user.getTenantId(), JSON.toJSONString(user), searchTemplate.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
        return tenantScene;
    }

    @Override
    public ISystemScene updateSystemScene(ISystemScene systemScene, User user) {
        String objectApiName = systemScene.getObjectDescribeApiName();
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), objectApiName);
        ValidatorUtil.validate(SystemSceneVD.of(systemScene, describe));
        ISearchTemplate searchTemplate = systemScene.toSearchTemplate();
        String extendAttribute = systemScene.getExtendAttribute();
        String sceneApiName = systemScene.getApiName();
        MetadataContext metadataContext = buildMetaDataContext(user);

        try {
            ISearchTemplate templateToUpdate = searchTemplateService.findForTenantByApiName(user.getTenantId(), objectApiName, sceneApiName, extendAttribute, metadataContext);
            if (Objects.nonNull(templateToUpdate)) {
                // 设置排序字段
                mergeTemplate(searchTemplate, templateToUpdate, metadataContext);
                ISearchTemplate template = searchTemplateService.updateForTenant(templateToUpdate, metadataContext);
                return SceneDTO.fromSearchTemplate(template, describe);
            }
            ISearchTemplate template = searchTemplateService.getSysSearchTemplateByApiName(sceneApiName, metadataContext, objectApiName, extendAttribute);
            if (Objects.nonNull(template)) {
                ISearchTemplate templateToInsert = SearchTemplateExt.of(template).copyToTenant(user, metadataContext);
                // 设置排序字段
                mergeTemplate(searchTemplate, templateToInsert, metadataContext);
                ISearchTemplate resultTemplate = searchTemplateService.createForTenant(templateToInsert, metadataContext);
                return SceneDTO.fromSearchTemplate(resultTemplate, describe);
            }
            log.warn("template not exit, ei:{},objectApiName:{},sceneApiName:{},extendAttribute:{}", user.getTenantId(), objectApiName, sceneApiName, extendAttribute);
            return SceneDTO.fromSearchTemplate(null, describe);
        } catch (MetadataServiceException e) {
            log.warn("updateSystemScene error. tenant=>{}, user=>{}, tenantScene=>{}", user.getTenantId(), JSON.toJSONString(user), searchTemplate.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }

    }

    private void replaceScenes(SceneDTO sourceScene, ISearchTemplate destScene, IObjectDescribe objDesc, MetadataContext mdCtx) {
        if (SearchTemplateExt.of(destScene).isUseFieldList()
                && CollectionUtils.notEmpty(sourceScene.getWebCoverTo())
                && sourceScene.getWebCoverTo().contains(destScene.getApiName())) {
            destScene.setShowTag(sourceScene.isShowTag());
            destScene.setFieldList(HeadField.fromFieldList2(sourceScene.getFieldList(), objDesc));
        }
        if (SearchTemplateExt.of(destScene).isUseMobileField()
                && CollectionUtils.notEmpty(sourceScene.getMobileCoverTo())
                && sourceScene.getMobileCoverTo().contains(destScene.getApiName())) {
            destScene.setMobileField(sourceScene.getMobileField());
        }
        defaultTemplateCheck(destScene, mdCtx);
    }

    private void defaultTemplateCheck(ISearchTemplate destScene, MetadataContext mdCtx) {
        if (DEFAULT_SCENE.getType().equals(destScene.getType())) {
            if (Boolean.TRUE.equals(mdCtx.getObjectProperty(IS_OUTER))) {
                Ranges ranges = new Ranges();
                ranges.setOutTenants(Lists.newArrayList(OutAll.TENANT));
                destScene.setRanges(ranges);
            } else {
                destScene.setIsAll(true);
            }
        }
    }

    @Override
    public boolean replaceOtherScenes(IObjectDescribe objDesc, SceneDTO sourceScene, String extendAttribute,
                                      List<Tuple3<String, String, String>> api2Type2DisplayForDefault, User user) {
        List<String> allScenes = CollectionUtils.concatAndDistinct(sourceScene.getWebCoverTo(), sourceScene.getMobileCoverTo());
        if (CollectionUtils.empty(allScenes)) {
            return true;
        }
        try {
            MetadataContext mdCtx = buildMetaDataContext(user);
            List<ISearchTemplate> updateScenes = searchTemplateService.findForTenantByApiNames(user.getTenantId(), objDesc.getApiName(), allScenes, extendAttribute, mdCtx);
            if (CollectionUtils.empty(updateScenes) || allScenes.size() != updateScenes.size()) {
                //需要插入数据
                List<String> updateSceneApiNames = Optional.ofNullable(updateScenes).map(apis -> apis.stream().map(ISearchTemplate::getApiName).collect(Collectors.toList())).orElse(Lists.newArrayList());
                List<String> insertSceneApiNames = Lists.newArrayList(allScenes);
                insertSceneApiNames.removeAll(updateSceneApiNames);
                List<ISearchTemplate> insertScenes = searchTemplateService.getSysSearchTemplateByApiNames(insertSceneApiNames, mdCtx, objDesc.getApiName(), extendAttribute);
                if (CollectionUtils.notEmpty(insertScenes)) {
                    for (int i = 0; i < insertScenes.size(); i++) {
                        ISearchTemplate insertScene = SearchTemplateExt.of(insertScenes.get(i)).copyToTenant(user, mdCtx);
                        replaceScenes(sourceScene, insertScene, objDesc, mdCtx);
                        insertScenes.set(i, insertScene);
                        if (api2Type2DisplayForDefault != null && DEFAULT_SCENE.getType().equals(insertScene.getType())) {
                            Tuple3<String, String, String> tuple = Tuple.tuple(insertScene.getApiName(), insertScene.getLabel(), insertScene.getType());
                            api2Type2DisplayForDefault.add(tuple);
                        }
                    }
                    searchTemplateService.batchCreateForTenant(insertScenes, mdCtx, objDesc.getApiName());
                }
            }
            if (CollectionUtils.notEmpty(updateScenes)) {
                for (int i = 0; i < updateScenes.size(); i++) {
                    ISearchTemplate updateScene = updateScenes.get(i);
                    replaceScenes(sourceScene, updateScene, objDesc, mdCtx);
                    updateScenes.set(i, updateScene);
                    if (api2Type2DisplayForDefault != null && DEFAULT_SCENE.getType().equals(updateScene.getType())) {
                        Tuple3<String, String, String> tuple = Tuple.tuple(updateScene.getApiName(), updateScene.getLabel(), updateScene.getType());
                        api2Type2DisplayForDefault.add(tuple);
                    }
                }
                searchTemplateService.batchUpdate4Tenant(updateScenes, mdCtx);
            }
        } catch (Exception e) {
            log.error("replaceOtherScenes error", e);
            return false;
        }
        return true;
    }

    private void mergeTemplate(ISearchTemplate sourceTemplate, ISearchTemplate targetTemplate, MetadataContext mdCtx) {
        targetTemplate.setOrders(sourceTemplate.getOrders());
        // 设置列表头显示列
        targetTemplate.setFieldListType(sourceTemplate.getFieldListType());
        targetTemplate.setFieldList(sourceTemplate.getFieldList());
        // 场景描述
        targetTemplate.setDescription(Strings.nullToEmpty(sourceTemplate.getDescription()));
        // 移动端摘要字段相关字段
        targetTemplate.setMobileFieldType(sourceTemplate.getMobileFieldType());
        targetTemplate.setMobileField(sourceTemplate.getMobileField());
        targetTemplate.setShowTag(sourceTemplate.getShowTag());
        defaultTemplateCheck(targetTemplate, mdCtx);
    }

    @Override
    public ISystemScene updatedDefaultOrders(OrderByExt orders, String objectApiName, String sceneApiName, String extendAttribute, User user) {
        try {
            IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), objectApiName);
            ISearchTemplate template = searchTemplateService.findForTenantByApiName(user.getTenantId(), objectApiName, sceneApiName, extendAttribute);
            MetadataContext metadataContext = MetadataContextExt.of(user).getMetadataContext();
            if (Objects.nonNull(template)) {
                template.setOrders(Lists.newArrayList(orders.getOrderBy()));
                ISearchTemplate searchTemplate = searchTemplateService.updateForTenant(template, metadataContext);
                return SceneDTO.fromSearchTemplate(searchTemplate, describe);
            }

            List<ISearchTemplate> templates = searchTemplateService.findForTenant(user.getTenantId(), objectApiName, null, null, extendAttribute);
            List<ISearchTemplate> systemTemplates = templates.stream()
                    .filter(t -> SearchTemplateExt.of(t).isDefaultScene())
                    .map(t -> SearchTemplateExt.of(t).copyToTenant(user, metadataContext))
                    .collect(Collectors.toList());
            Optional<ISearchTemplate> templateOptional = systemTemplates.stream()
                    .filter(t -> sceneApiName.equals(t.getApiName()) && Objects.equals(extendAttribute, t.getExtendAttribute()))
                    .peek(t -> t.setOrders(Lists.newArrayList(orders.getOrderBy())))
                    .findFirst();
            if (templateOptional.isPresent()) {
                ISearchTemplate resultTemplate = searchTemplateService.createForTenant(templateOptional.get(), metadataContext);
                return SceneDTO.fromSearchTemplate(resultTemplate, describe);
            }
            log.warn("template not exit, ei:{},objectApiName:{},sceneApiName:{},extendAttribute:{}", user.getTenantId(),
                    objectApiName, sceneApiName, extendAttribute);
            return SceneDTO.fromSearchTemplate(null, describe);
        } catch (MetadataServiceException e) {
            log.warn("updatedDefaultOrders error. tenant=>{}, user=>{}, orders=>{}", user.getTenantId(), JSON.toJSONString(user), JSON.toJSONString(orders), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void enableTenantScene(String describeApiName, String sceneApiName, User user) {
        try {
            ISearchTemplate template = new SearchTemplate();
            template.setTenantId(user.getTenantId());
            template.setApiName(sceneApiName);
            template.setObjectDescribeApiName(describeApiName);
            ISearchTemplate updated = searchTemplateService.enableForTenant(template, buildMetaDataContext(user));
            logService.log(user, EventType.ENABLE, ActionType.ENABLE_TENANT_SCENE, describeApiName,
                    I18N.text(I18NKey.TENANT_SCENE_SPECIFY, updated.getLabel() + sceneApiName));
        } catch (MetadataServiceException e) {
            log.warn("enableTenantScene error. describeApiName=>{}, sceneApiName=>{}, user=>{}", describeApiName, sceneApiName, JSON.toJSONString(user));
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void disableTenantScene(String describeApiName, String sceneApiName, User user) {
        try {
            ISearchTemplate template = new SearchTemplate();
            template.setTenantId(user.getTenantId());
            template.setApiName(sceneApiName);
            template.setObjectDescribeApiName(describeApiName);
            ISearchTemplate updated = searchTemplateService.disableForTenant(template, buildMetaDataContext(user));
            logService.log(user, EventType.DISABLE, ActionType.DISABLE_TENANT_SCENE, describeApiName,
                    I18N.text(I18NKey.TENANT_SCENE_SPECIFY, updated == null ? sceneApiName : sceneApiName + sceneApiName));
        } catch (MetadataServiceException e) {
            log.warn("disableTenantScene error. describeApiName=>{}, sceneApiName=>{}, user=>{}", describeApiName, sceneApiName, JSON.toJSONString(user));
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void enableScene(String describeApiName, String sceneApiName, String type, String extendAttribute, User user) {
        try {
            ISearchTemplate template = new SearchTemplate();
            template.setType(type);
            template.setTenantId(user.getTenantId());
            template.setApiName(sceneApiName);
            template.setObjectDescribeApiName(describeApiName);
            template.setExtendAttribute(extendAttribute);
            ISearchTemplate updated = searchTemplateService.enableForTenant(template, buildMetaDataContext(user));
            logService.log(user, EventType.ENABLE, ActionType.ENABLE_TENANT_SCENE, describeApiName,
                    I18N.text(I18NKey.TENANT_SCENE_SPECIFY, updated.getLabel() + sceneApiName));
        } catch (MetadataServiceException e) {
            log.warn("enableTenantScene error. describeApiName=>{}, sceneApiName=>{}, user=>{}", describeApiName, sceneApiName, JSON.toJSONString(user));
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void disableScene(String describeApiName, String sceneApiName, String type, String extendAttribute, User user) {
        try {
            ISearchTemplate template = new SearchTemplate();
            template.setType(type);
            template.setTenantId(user.getTenantId());
            template.setApiName(sceneApiName);
            template.setExtendAttribute(extendAttribute);
            template.setObjectDescribeApiName(describeApiName);
            ISearchTemplate updated = searchTemplateService.disableForTenant(template, buildMetaDataContext(user));
            // 这里加一个判空，兼容场景已经被禁用或删除的情况
            if (Objects.isNull(updated)) {
                return;
            }
            logService.log(user, EventType.DISABLE, ActionType.DISABLE_TENANT_SCENE, describeApiName,
                    I18N.text(I18NKey.TENANT_SCENE_SPECIFY, updated.getLabel() + sceneApiName));
        } catch (MetadataServiceException e) {
            log.warn("disableTenantScene error. describeApiName=>{}, sceneApiName=>{}, user=>{}", describeApiName, sceneApiName, JSON.toJSONString(user));
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void deleteTenantScene(String describeApiName, String sceneApiName, String extendAttribute, User user) {
        try {
            MetadataContext metadataContext = buildMetaDataContext(user);
            ISearchTemplate tenantScene = searchTemplateService.findForTenantByApiName(user.getTenantId(), describeApiName, sceneApiName, extendAttribute, metadataContext);
            Preconditions.checkArgument(sceneDeleteAble(tenantScene), I18NExt.text(I18NKey.CURRENT_SCENARIO_CANNOT_BE_DELETED));
            searchTemplateService.deleteForTenant(tenantScene, metadataContext);
            logService.log(user, EventType.DELETE, ActionType.DELETE_TENANT_SCENE, describeApiName,
                    I18N.text(I18NKey.TENANT_SCENE_SPECIFY, tenantScene.getLabel() + sceneApiName));
        } catch (MetadataServiceException e) {
            log.warn("deleteTenantScene error. describeApiName=>{}, sceneApiName=>{}, user=>{}", describeApiName, sceneApiName, JSON.toJSONString(user), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public boolean validateSceneLabelRepeat(String describeApiName, String label, String id, String tenantId) {
        if (Strings.isNullOrEmpty(label)) {
            return false;
        }
        try {
            List<ISearchTemplate> byLabels = searchTemplateService.findForTenantByLabel(tenantId, describeApiName, label);
            return CollectionUtils.notEmpty(byLabels) && byLabels.stream().anyMatch(x -> !Objects.equals(id, x.getId()));
        } catch (MetadataServiceException e) {
            log.warn("validateSceneLabelRepeat error. describeApiName=>{}, label=>{}, id=>{}, tenantId=>{}", describeApiName, label, id, tenantId, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public boolean validateSceneApiNameRepeat(String describeApiName, String sceneApiName, String id, String tenantId, String extendAttribute) {
        if (Strings.isNullOrEmpty(sceneApiName)) {
            return false;
        }
        try {
            ISearchTemplate byApiNam = searchTemplateService.findForTenantByApiName(tenantId, describeApiName, sceneApiName, extendAttribute);
            return Objects.nonNull(byApiNam) && !Objects.equals(id, byApiNam.getId());
        } catch (MetadataServiceException e) {
            log.warn("validateSceneApiNameRepeat error. describeApiName=>{}, sceneApiName=>{}, id=>{}, tenantId=>{}", describeApiName, sceneApiName, id, tenantId, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public boolean validateSceneApiNameRepeat(String describeApiName, String sceneApiName, String id, String extendAttribute, User user) {
        if (Strings.isNullOrEmpty(sceneApiName)) {
            return false;
        }
        try {
            MetadataContext metadataContext = buildMetaDataContext(user);
            ISearchTemplate byApiNam = searchTemplateService.findForTenantByApiName(user.getTenantId(), describeApiName, sceneApiName, extendAttribute, metadataContext);
            return Objects.nonNull(byApiNam) && !Objects.equals(id, byApiNam.getId());
        } catch (MetadataServiceException e) {
            log.warn("validateSceneApiNameRepeat error. describeApiName=>{}, sceneApiName=>{}, id=>{}, user=>{}",
                    describeApiName, sceneApiName, id, user, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public ValidateResult validateApiNameAndLabel(String describeApiName, String sceneApiName, String label,
                                                  String id, String tenantId, String extendAttribute) {
        return ValidateResult.builder()
                .apiName(validateSceneApiNameRepeat(describeApiName, sceneApiName, id, tenantId, extendAttribute))
//                .label(validateSceneLabelRepeat(describeApiName, label, id, tenantId))
                .build();
    }

    @Override
    public void validateTenantSceneSum(String describeApiName, String tenantId, String extendAttribute) {
        try {
            int sum = searchTemplateService.getTenantSearchTemplateSum(tenantId, describeApiName, extendAttribute);
            tenantSceneSumValidate(tenantId, sum);
        } catch (MetadataServiceException e) {
            log.warn("validateTenantSceneSum error. describeApiName=>{}, tenantId=>{}", describeApiName, tenantId, e);
            throw new MetaDataBusinessException(e);
        }
    }

    private void tenantSceneSumValidate(String tenantId, int sum) {
        int maxSum = grayHelper.getTenantSceneLimit(tenantId);
        Preconditions.checkArgument(sum < maxSum, I18N.text(I18NKey.TENANT_SCENE_COUNT_VALIDATE, maxSum));
    }

    @Override
    public void validateTenantSceneSum(String describeApiName, User user) {
        try {
            MetadataContext metadataContext = buildMetaDataContext(user);
            int sum = searchTemplateService.getTenantSearchTemplateSum(user.getTenantId(), describeApiName, "", metadataContext);
            tenantSceneSumValidate(user.getTenantId(), sum);
        } catch (MetadataServiceException e) {
            log.warn("validateTenantSceneSum error. describeApiName=>{}, user=>{}", describeApiName, user, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void initSystemScene(String describeApiName, String extendAttribute, User user, MetadataContext metadataContext) {
        List<ISearchTemplate> defaultSearchTemplateList = getDefaultSearchTemplates(user, describeApiName, extendAttribute);
        try {
            String sceneApiName = metadataContext.getProperty("sceneApiName");
            if (StringUtils.isNotBlank(sceneApiName)) {
                defaultSearchTemplateList = defaultSearchTemplateList.stream()
                        .filter(x -> Objects.equals(x.getApiName(), sceneApiName))
                        .collect(Collectors.toList());
                String searchId = metadataContext.getProperty("searchId");
                if (StringUtils.isNotEmpty(searchId)) {
                    defaultSearchTemplateList.forEach(x -> x.setId(searchId));
                }
            }
            if ("insert".equals(metadataContext.getProperty("option"))) {
                long now = System.currentTimeMillis();
                for (ISearchTemplate template : defaultSearchTemplateList) {
                    template.setCreateTime(now);
                    template.setLastModifiedTime(now);
                    template.setVersion(1);
                    now++;
                }
            }
            searchTemplateService.updateSysTemplate(defaultSearchTemplateList, metadataContext);
        } catch (MetadataServiceException e) {
            log.warn("initSystemScene error. describeApiName=>{}, tenantId=>{}", describeApiName, user.getTenantId(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IScene> findBaseScenes(String describeApiName, String extendAttribute, User user) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
        List<ISearchTemplate> orderedTemplates = getBaseSearchTemplate(describe.getApiName(), extendAttribute, user);
        filterSearchTemplateByRelatedTeamSwitch(user, describe, orderedTemplates);
        orderedTemplates.removeIf(x -> DEFAULT_NEW_SCENES.contains(x.getApiName()));
        filterBigObjectNotSupportScene(describe, orderedTemplates);
        return SceneDTO.fromSearchTemplateList(orderedTemplates, describe);
    }

    private static void filterBigObjectNotSupportScene(IObjectDescribe describe, List<ISearchTemplate> orderedTemplates) {
        if (describe.isBigObject()) {
            orderedTemplates.removeIf(template -> StringUtils.equalsAny(template.getApiName(), SHARE_WITH_ME.getApiName(), FOLLOW.getApiName()));
            orderedTemplates.removeIf(template -> DEFAULT_NEW_SCENES.contains(template.getApiName()));
        }
    }

    private void filterSearchTemplateByRelatedTeamSwitch(User user, IObjectDescribe describe, List<ISearchTemplate> orderedTemplates) {
        // 根据相关团队开关过滤『我负责的』『我下属负责的』场景
        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), describe);
        orderedTemplates.removeIf(template -> !optionalFeaturesSwitch.getIsRelatedTeamEnabled() &&
                (StringUtils.equalsAny(template.getApiName(), INVOLVED_WITH_ME.getApiName(), INVOLVED_WITH_SUB.getApiName())));
    }

    private List<IScene> getSceneList(User user, IObjectDescribe describe, List<ISearchTemplate> orderedTemplates) {
        filterSearchTemplateByRelatedTeamSwitch(user, describe, orderedTemplates);
        filterBigObjectNotSupportScene(describe, orderedTemplates);
        SearchTemplateExt.handleSceneOrder(orderedTemplates, Lists.newArrayList(), user);
        return SceneDTO.fromSearchTemplateList(orderedTemplates, describe);
    }


    @Override
    public List<ISearchTemplate> findBaseSearchTemplates(String describeApiName, String extendAttribute, User user) {
        List<ISearchTemplate> searchTemplates = getBaseSearchTemplate(describeApiName, extendAttribute, user);
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
        if (!StringUtils.equals(describeApiName, UDOBJ)) {
            filterSearchTemplateByRelatedTeamSwitch(user, describe, searchTemplates);
        }
        filterBigObjectNotSupportScene(describe, searchTemplates);
        return searchTemplates;
    }

    @Override
    public void setUpDefaultScenePriority(String describeApiName, List<String> sceneApiNames, String extendAttribute, User user) {
        try {
            MetadataContext metadataContext = buildMetaDataContext(user);
            // 查询当前对象所有的场景（租户和预置场景）
            List<String> extendAttributeList = null;
            if (!Strings.isNullOrEmpty(extendAttribute)) {
                extendAttributeList = Arrays.asList(extendAttribute.split(","));
            }
            List<ISearchTemplate> searchTemplates = searchTemplateService.findForTenantAndAttributeList(user.getTenantId(),
                    describeApiName, null, null, extendAttributeList, metadataContext);
            // 相关团队开关关闭后将『我负责的』『我下属负责的』场景优先级设置为最低。
            int sceneSize = sceneApiNames.size();
            for (ISearchTemplate iSearchTemplate : searchTemplates) {
                int index = sceneApiNames.indexOf(iSearchTemplate.getApiName());
                iSearchTemplate.setDefaultScenePriority(index == -1 ? sceneSize++ : index);
            }
            // 只查询在 tenant 表中保存了的场景（租户场景，和设置了排序字段的预设场景）
            List<ISearchTemplate> forTenantFromDb = searchTemplateService.findForTenantAndAttributeListFromDb(user.getTenantId(), describeApiName, extendAttributeList, metadataContext);
            Map<String, ISearchTemplate> templateMap = forTenantFromDb.stream().collect(Collectors.toMap(ISearchTemplate::getApiName, it -> it));
            // 按当前场景是否在 tenant 表中，将场景分为待更新和待新建两部分（按id更新）
            List<ISearchTemplate> toUpdate = Lists.newArrayList();
            List<ISearchTemplate> toInsert = Lists.newArrayList();
            for (ISearchTemplate template : searchTemplates) {
                if (templateMap.containsKey(template.getApiName())) {
                    ISearchTemplate searchTemplate = templateMap.get(template.getApiName());
                    searchTemplate.setDefaultScenePriority(template.getDefaultScenePriority());
                    toUpdate.add(searchTemplate);
                } else {
                    ISearchTemplate searchTemplate = SearchTemplateExt.of(template).copyToTenant(user, metadataContext);
                    searchTemplate.setDefaultScenePriority(template.getDefaultScenePriority());
                    toInsert.add(searchTemplate);
                }
            }

            if (CollectionUtils.notEmpty(toUpdate)) {
                searchTemplateService.batchUpdate4Tenant(toUpdate, metadataContext);
            }

            if (CollectionUtils.notEmpty(toInsert)) {
                searchTemplateService.batchCreateForTenant(toInsert, metadataContext, describeApiName);
            }
            logService.log(user, EventType.MODIFY, ActionType.UPDATE_TENANT_SCENE, describeApiName,
                    I18N.text(I18NKey.SET_SCENE_PRIORITY));
        } catch (MetadataServiceException e) {
            log.warn("setUpDefaultScenePriority fail, describeApiName=>{}, tenantId=>{}, user=>{}", describeApiName, user.getTenantId(), user, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public IUdefConfig findSceneConfig(String describeApiName, String sceneApiName, User user) {
        IUdefConfig sceneConfig = StandardConfig.SCENE_CONFIG.getConfig(describeApiName, sceneApiName);
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CONFIGURATION_PACKAGE, user.getTenantId())) {
            return sceneConfig;
        }
        List<ObjectControlLevelLogicService.ObjectControlLevelInfo> objectControlLevelInfos = objectControlLevelLogicService.queryControlLevel(
                user, describeApiName, ControlLevelResourceType.SCENE);
        return generateAndMergeSceneConfig(describeApiName, sceneApiName, sceneConfig, objectControlLevelInfos);
    }

    @Override
    public List<IUdefConfig> findSceneConfigList(User user, String describeApiName, List<IScene> sceneList) {
        List<IUdefConfig> sceneConfig = SceneExt.findButtonConfig(sceneList);
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CONFIGURATION_PACKAGE, user.getTenantId())) {
            return sceneConfig;
        }
        List<ObjectControlLevelLogicService.ObjectControlLevelInfo> objectControlLevelInfos = objectControlLevelLogicService.queryControlLevel(
                user, describeApiName, ControlLevelResourceType.SCENE);
        Map<String, IUdefConfig> configMap = sceneConfig.stream()
                .collect(Collectors.toMap(IUdefConfig::getApiName, Function.identity()));
        return sceneList.stream()
                .map(scene -> generateAndMergeSceneConfig(describeApiName, scene.getApiName(),
                        configMap.get(scene.getApiName()), objectControlLevelInfos))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private IUdefConfig generateAndMergeSceneConfig(String describeApiName, String sceneApiName, IUdefConfig sceneConfig,
                                                    List<ObjectControlLevelLogicService.ObjectControlLevelInfo> objectControlLevelInfos) {
        if (CollectionUtils.empty(objectControlLevelInfos)) {
            return sceneConfig;
        }
        for (ObjectControlLevelLogicService.ObjectControlLevelInfo objectControlLevelInfo : objectControlLevelInfos) {
            String primaryKey = objectControlLevelInfo.getPrimaryKey();
            String parentFieldValue = objectControlLevelInfo.getParentFieldValue();
            if (!Objects.equals(primaryKey, sceneApiName) || !Objects.equals(parentFieldValue, describeApiName)) {
                continue;
            }
            if (Objects.isNull(sceneConfig)) {
                sceneConfig = new SceneConfig();
                sceneConfig.setDescribeApiName(parentFieldValue);
                sceneConfig.setApiName(primaryKey);
            }
            sceneConfig.setControlLevel(objectControlLevelInfo.getControlLevel());
            return sceneConfig;
        }
        return sceneConfig;
    }

    @Override
    public void deleteSceneByDescribe(String describeApiName, User user) {
        try {
            MetadataContext metadataContext = MetadataContextExt.of(user).getMetadataContext();
            searchTemplateService.deleteSceneByDescribe(describeApiName, metadataContext);
        } catch (MetadataServiceException e) {
            log.warn("error in deleteSceneByDescribe, ei:{}, describeApiName:{}",
                    user.getTenantId(), describeApiName, e);
//            throw new MetaDataBusinessException(e.getMessage());
        }

    }

    private List<ISearchTemplate> getBaseSearchTemplate(String describeApiName, String extendAttribute, User user) {
        try {

            List<ISearchTemplate> defaultSearchTemplates = searchTemplateService
                    .findDefaultSceneByDescribe(buildMetaDataContext(user), describeApiName, extendAttribute);
            List<String> supportedDefaultScenes = Stream.of(SearchTemplateExt.DefaultScene.values())
                    .filter(it -> !user.isOutUser() || it.isSupportOuter())
                    .map(SearchTemplateExt.DefaultScene::getApiName)
                    .collect(Collectors.toList());
            List<ISearchTemplate> templates = defaultSearchTemplates.stream()
                    .filter(s -> supportedDefaultScenes.contains(s.getApiName())).collect(Collectors.toList());
            return SearchTemplateExt.searchTemplatesOrderByTypeAndCreateTime(templates);
        } catch (MetadataServiceException e) {
            log.warn("findByObjectDescribeAPIName error. describeApiName=>{}, tenantId=>{}",
                    describeApiName, user.getTenantId(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    private List<ISearchTemplate> getDefaultSearchTemplates(User user, String apiName, String extendAttribute) {
        SceneInitProvider provider = sceneInitManager.getProvider(apiName);
        List<ISearchTemplate> defaultTemplateList = provider.getDefaultSearchTemplateList(user, apiName, extendAttribute);
        int index = 0;
        for (ISearchTemplate defaultTemplate : defaultTemplateList) {
            defaultTemplate.setUserId(user.getUserId());
            defaultTemplate.setIsHidden(false);
            defaultTemplate.setOrder(++index);
            defaultTemplate.setId(null);
            defaultTemplate.setExtendAttribute(extendAttribute);
        }
        return defaultTemplateList;
    }

    private MetadataContext buildMetaDataContext(User user) {
        RequestContext context = RequestContextManager.getContext();
        boolean isOuter = false;
        if (Objects.nonNull(context)) {
            isOuter = BooleanUtils.isTrue(context.getAttribute(IS_OUTER));
        }
        return SceneExt.getOutTenantInfo(user, isOuter).getMetadataContext();
    }

    private boolean sceneDeleteAble(ISearchTemplate template) {
        return template != null && !Boolean.TRUE.equals(template.getIsAvailable());
    }

    @Override
    public void deleteCustomForApiNameAndUserId(String describeApiName, String apiName, User user) {
        try {
            if (!user.isOutUser()) {
                searchTemplateService.deleteCustomForApiNameOrUserId(user.getTenantId(), describeApiName, apiName, user.getUserId());
            } else {
                searchTemplateService.deleteCustomForApiNameAndOutTenantUser(user.getTenantId(), describeApiName, apiName, user.getOutTenantId(), user.getOutUserId());
            }
        } catch (MetadataServiceException e) {
            log.warn("delete customScene error. describeApiName:{}, apiName:{}, user:{}", describeApiName, apiName, JacksonUtils.toJsonExcludeNullValue(user), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void ownCustomListConfigUserIdsToSend(String objApiName, String objDisplayName, List<Tuple3<String, String, String>> api2Display2Type,
                                                 String extendAttribute, User user) {
        List<String> apiNames = Lists.newArrayList();
        List<String> displays = Lists.newArrayList();
        try {
            //覆盖预设租户场景的个人场景的用户集合
            api2Display2Type.forEach(t -> {
                apiNames.add(t.v1());
                displays.add(t.v2());
            });
            int batchSize = AppFrameworkConfig.WIPE_CUSTOM_SETTINGS_BATCH_SIZE;
            int start = 0;
            MetadataContext metadataContext = MetadataContextExt.of(user).getMetadataContext();
            List<String> userIds;
            do {
                userIds = searchTemplateService.findCustomTemplateUserIdByDescribeTypeAndApiNames(apiNames, user.getTenantId(),
                        objApiName, DEFAULT_SCENE.getType(), extendAttribute, batchSize, start, metadataContext);
                //发 MQ Message
                if (CollectionUtils.notEmpty(userIds)) {
                    WipeMessage message = WipeMessage.builder()
                            .tenantId(user.getTenantId())
                            .objApi(objApiName)
                            .objDisplay(objDisplayName)
                            .apiNames(apiNames)
                            .sceneLabels(displays)
                            .isOut(user.isOutUser())
                            .wipeCustomTemplateUserIds(userIds)
                            .build();
                    wipeCustomListSettingsProducer.sendMessage(JacksonUtils.toJson(message).getBytes(StandardCharsets.UTF_8));
                    start += batchSize;
                }
            } while (CollectionUtils.notEmpty(userIds));
            //设置个人列表页字段列宽的用户集合
            start = 0;
            do {
                userIds = fieldListConfigService.getUserIdByFieldWidth(user.getTenantId(), Lists.newArrayList(objApiName),
                        extendAttribute, batchSize, start, metadataContext);
                if (CollectionUtils.notEmpty(userIds)) {
                    WipeMessage message = WipeMessage.builder()
                            .tenantId(user.getTenantId())
                            .objApi(objApiName)
                            .objDisplay(objDisplayName)
                            .apiNames(apiNames)
                            .sceneLabels(displays)
                            .isOut(user.isOutUser())
                            .wipeCustomSettingsUserIds(userIds)
                            .build();
                    wipeCustomListSettingsProducer.sendMessage(JacksonUtils.toJson(message).getBytes(StandardCharsets.UTF_8));
                    start += batchSize;
                }
            } while (CollectionUtils.notEmpty(userIds));
        } catch (MetadataServiceException e) {
            log.warn("findOwnCustomListConfig error. describeApiName:{}, apiNames:{}, user:{}", objApiName, apiNames, JacksonUtils.toJsonExcludeNullValue(user), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Data
    @Builder
    public static class ValidateResult {
        private boolean apiName;
        private boolean label;
    }
}
