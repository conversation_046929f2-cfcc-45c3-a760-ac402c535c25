/**
 * Facishare PAAS Organization
 * This is PAAS organization service project
 *
 * OpenAPI spec version: v1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


package com.facishare.crm.privilege.swagger.model;

import java.util.Map;
import java.util.Objects;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * GroupPojo
 */
@javax.annotation.Generated(value = "class io.swagger.codegen.languages.JavaClientCodegen", date = "2017-07-25T12:04:51.098+08:00")
public class GroupPojo   {
  @SerializedName("id")
  private String id = null;

  @SerializedName("tenantId")
  private String tenantId = null;

  @SerializedName("appId")
  private String appId = null;

  @SerializedName("name")
  private String name = null;

  @SerializedName("status")
  private Integer status = null;

  @SerializedName("type")
  private Integer type = null;

  @SerializedName("description")
  private String description = null;

  @SerializedName("delFlag")
  private Boolean delFlag = false;

  @SerializedName("languages")
  private Map<String, String> languages = null;

  @SerializedName("oldLanguages")
  private Map<String, String> oldLanguages = null;

  @SerializedName("defName")
  private String defName;

  public GroupPojo id(String id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public GroupPojo tenantId(String tenantId) {
    this.tenantId = tenantId;
    return this;
  }

   /**
   * Get tenantId
   * @return tenantId
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getTenantId() {
    return tenantId;
  }

  public void setTenantId(String tenantId) {
    this.tenantId = tenantId;
  }

  public GroupPojo appId(String appId) {
    this.appId = appId;
    return this;
  }

   /**
   * Get appId
   * @return appId
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getAppId() {
    return appId;
  }

  public void setAppId(String appId) {
    this.appId = appId;
  }

  public GroupPojo name(String name) {
    this.name = name;
    return this;
  }

   /**
   * Get name
   * @return name
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public GroupPojo status(Integer status) {
    this.status = status;
    return this;
  }

   /**
   * Get status
   * @return status
  **/
  @ApiModelProperty(example = "null", value = "")
  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public GroupPojo type(Integer type) {
    this.type = type;
    return this;
  }

   /**
   * Get type
   * @return type
  **/
  @ApiModelProperty(example = "null", value = "")
  public Integer getType() {
    return type;
  }

  public void setType(Integer type) {
    this.type = type;
  }

  public GroupPojo description(String description) {
    this.description = description;
    return this;
  }

   /**
   * Get description
   * @return description
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public GroupPojo delFlag(Boolean delFlag) {
    this.delFlag = delFlag;
    return this;
  }

   /**
   * Get delFlag
   * @return delFlag
  **/
  @ApiModelProperty(example = "null", value = "")
  public Boolean getDelFlag() {
    return delFlag;
  }

  public void setDelFlag(Boolean delFlag) {
    this.delFlag = delFlag;
  }

  public GroupPojo languages(Map<String, String> languages) {
    this.languages = languages;
    return this;
  }

  @ApiModelProperty(example = "null", value = "")
  public Map<String, String> getLanguages() {
    return languages;
  }

  public void setLanguages(Map<String, String> languages) {
    this.languages = languages;
  }

  public GroupPojo oldLanguages(Map<String, String> oldLanguages) {
    this.oldLanguages = oldLanguages;
    return this;
  }

  @ApiModelProperty(example = "null", value = "")
  public Map<String, String> getOldLanguages() {
    return oldLanguages;
  }

  public void setOldLanguages(Map<String, String> oldLanguages) {
    this.oldLanguages = oldLanguages;
  }


  public GroupPojo defName(String defName) {
    this.defName = defName;
    return this;
  }


  @ApiModelProperty(example = "null", value = "")
  public String getDefName() {
    return defName;
  }

  public void setDefName(String defName) {
    this.defName = defName;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    GroupPojo groupPojo = (GroupPojo) o;
    return Objects.equals(this.id, groupPojo.id) &&
        Objects.equals(this.tenantId, groupPojo.tenantId) &&
        Objects.equals(this.appId, groupPojo.appId) &&
        Objects.equals(this.name, groupPojo.name) &&
        Objects.equals(this.status, groupPojo.status) &&
        Objects.equals(this.type, groupPojo.type) &&
        Objects.equals(this.description, groupPojo.description) &&
        Objects.equals(this.delFlag, groupPojo.delFlag);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, tenantId, appId, name, status, type, description, delFlag);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class GroupPojo {\n");
    
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    tenantId: ").append(toIndentedString(tenantId)).append("\n");
    sb.append("    appId: ").append(toIndentedString(appId)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    description: ").append(toIndentedString(description)).append("\n");
    sb.append("    delFlag: ").append(toIndentedString(delFlag)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

