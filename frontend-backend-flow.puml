@startuml 前后台接口区分流程

actor 用户
participant "前端" as Frontend
participant "RequestFilter" as Filter
participant "RequestContext" as Context
participant "多语言处理" as I18n

note over Frontend: 判断请求类型
alt 后台接口
    Frontend -> Frontend: 在traceId上增加"manage"标识
else 前台接口
    Frontend -> Frontend: 使用普通traceId
end

Frontend -> Filter: 发送请求(包含traceId)

note over Filter: 请求过滤与上下文初始化
Filter -> Filter: 解析请求traceId

alt traceId包含"manage"
    Filter -> Context: 初始化RequestContext
    Context -> Context: 设置isFromManage=true
else 不包含"manage"
    Filter -> Context: 初始化普通RequestContext
    Context -> Context: 设置isFromManage=false
end

note over I18n: 多语言处理逻辑
I18n -> I18n: 获取多语言信息

alt isFromManage=true && isFromRest=false
    I18n -> I18n: 使用实时多语言处理
else 其他情况
    I18n -> I18n: 使用普通多语言处理
end

@enduml 