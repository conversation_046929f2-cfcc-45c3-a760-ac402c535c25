package com.facishare.paas.appframework.core.predef.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.service.dto.data.CanAddLookUpData;
import com.facishare.paas.appframework.core.predef.service.dto.data.HasLookupFields;
import com.facishare.paas.appframework.core.predef.service.dto.data.ValidateLookupData;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.metadatahandle.LookupDataValidator;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@ServiceModule("lookup")
@Component
public class ObjectLookupService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ProductCategoryService productCategoryService;

    @ServiceMethod("check")
    public CanAddLookUpData.Result canAddLookUpData(CanAddLookUpData.Arg arg, ServiceContext context) {
        CanAddLookUpData.Result.ResultBuilder builder = CanAddLookUpData.Result.builder();
        IObjectDescribe describe = serviceFacade.findObject(context.getUser().getTenantId(), arg.getTargetDescribeApiName());
        Optional<ObjectReferenceWrapper> field = ObjectDescribeExt.of(describe)
                .getFieldDescribeSilently(arg.getLookupFieldName())
                .map(ObjectReferenceWrapper::of);
        if (!field.isPresent()) {
            return builder.helpText(I18NExt.text(I18NKey.FIELD_DISABLED,
                    describe.getDisplayName() + "." + arg.getLookupFieldName())).build();
        }
        if (!field.get().isActive()) {
            return builder.helpText(I18NExt.text(I18NKey.FIELD_DISABLED,
                    describe.getDisplayName() + "." + field.get().getLabel())).build();
        }

        if (AppFrameworkConfig.isSkipValidateLookupGrayTenant(context.getTenantId())) {
            return builder.canAdd(true).build();
        }
        try {
            ObjectReferenceWrapper objectReferenceField = field.get();
            List<Wheres> wheres = objectReferenceField.getWheresBy();
            if (CollectionUtils.empty(wheres)) { //没有设置过滤条件, 返回true
                return builder.canAdd(true).build();
            }
            // 有多组wheres条件，并包含三角、四角、五角、函数和（主对象和本对象）过滤条件，可以直接新建
            if (WheresExt.multiWheres(wheres) && WheresExt.hasSpecialFilter(wheres)) {
                return builder.canAdd(true).build();
            }

            if (Utils.PRODUCT_API_NAME.equals(arg.getDescribeApiName()) || Utils.SPU_API_NAME.equals(arg.getDescribeApiName())) {
                productCategoryService.handleCategoryWhere(context.getTenantId(), context.getUser().getUserId(), wheres);
            }
            //查看是否满足id 为当前对象, 满足LookUP筛选条件的数据
            //组装查询条件
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setOffset(0);
            searchTemplateQuery.setLimit(1);
            Filter filter = new Filter();
            filter.setFieldName(IObjectData.ID);
            filter.setOperator(Operator.EQ);
            filter.setFieldValues(Lists.newArrayList(arg.getDataId()));
            searchTemplateQuery.setFilters(Lists.newArrayList(filter));

            // 移除所有多角关系的lookup过滤条件
            SearchTemplateQueryExt.of(searchTemplateQuery).removeAllRelated(wheres);
            // 移除本对象字段和主对象字段过滤条件
            SearchTemplateQueryExt.removeMasterAndNativeObjVariableFilter(wheres);
            long count = wheres.stream().filter(where -> CollectionUtils.notEmpty(where.getFilters()))
                    .mapToLong(where -> where.getFilters().size())
                    .sum();
            searchTemplateQuery.setWheres(count > 0 ? wheres : null);
            SearchTemplateQueryExt.of(searchTemplateQuery).handleWheresFilterWithLookupFunction();

            QueryResult<IObjectData> queryResult =
                    serviceFacade.findBySearchQuery(context.getUser(), arg.getDescribeApiName(),
                            searchTemplateQuery);

            if (CollectionUtils.empty(queryResult.getData())) {
                builder.canAdd(false).helpText(getHelpText(describe, objectReferenceField));
            } else {
                builder.canAdd(true);
            }
        } catch (Exception e) {
            builder.canAdd(false).helpText(getHelpText(describe, field.get()));
        }
        return builder.build();
    }

    private String getHelpText(IObjectDescribe describe, ObjectReferenceWrapper objectReferenceField) {
        if (StringUtils.isBlank(objectReferenceField.getHelpText())) {
            return I18N.text(I18NKey.DATA_NOT_SATISFIED_CREAT_CONDITION, describe.getDisplayName());
        }
        return String.format("%s,%s", I18N.text(I18NKey.DATA_NOT_SATISFIED_CREAT_CONDITION, describe.getDisplayName()), objectReferenceField.getHelpText());
    }

    @ServiceMethod("hasLookup")
    public HasLookupFields.Result hasLookupFields(HasLookupFields.Arg arg, ServiceContext context) {
        HasLookupFields.Result result = new HasLookupFields.Result();
        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), arg.getDescribeApiName());
        for (IFieldDescribe fieldDescribe : describe.getFieldDescribes()) {
            if (IFieldType.MASTER_DETAIL.equals(fieldDescribe.getType()) ||
                    IFieldType.OBJECT_REFERENCE.equals(fieldDescribe.getType()) ||
                    IFieldType.OBJECT_REFERENCE_MANY.equals(fieldDescribe.getType())
            ) {
                result.setHasLookupFields(true);
            }
        }
        return result;
    }

    @ServiceMethod("validateLookupData")
    public ValidateLookupData.Result validateLookupData(ValidateLookupData.Arg arg, ServiceContext context) {
        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), arg.getDescribeApiName());

        List<IObjectReferenceField> fields = getReferenceFields(describe, arg.getFieldApiNames());
        if (CollectionUtils.empty(fields)) {
            return ValidateLookupData.Result.empty();
        }

        IObjectData objectData = arg.getObjectData().toObjectData();
        Set<String> fieldApiNames = fields.stream()
                .map(fieldDescribe -> LookupDataValidator.builder()
                        .objectData(objectData)
                        .referenceField(fieldDescribe)
                        .user(context.getUser())
                        .metaDataFindService(serviceFacade)
                        .describeLogicService(serviceFacade)
                        .productCategoryService(productCategoryService)
                        .build())
                .map(LookupDataValidator::validate)
                .filter(fieldApiName -> !Strings.isNullOrEmpty(fieldApiName))
                .collect(Collectors.toSet());

        return ValidateLookupData.Result.of(fieldApiNames);
    }

    private static List<IObjectReferenceField> getReferenceFields(IObjectDescribe describe, List<String> fieldApiNames) {
        return ObjectDescribeExt.of(describe)
                .getFieldByApiNames(fieldApiNames)
                .stream()
                .filter(it -> it instanceof IObjectReferenceField)
                .map(it -> (IObjectReferenceField) it)
                .collect(Collectors.toList());
    }
}
