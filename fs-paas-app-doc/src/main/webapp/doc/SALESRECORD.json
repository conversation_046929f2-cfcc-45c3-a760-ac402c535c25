{"swagger": "2.0", "info": {"title": "AppFrameWork REST API", "description": "销售记录相关REST API", "version": "1.0"}, "host": "************:8234", "schemes": ["http"], "basePath": "/v1/inner/rest/salesRecord", "produces": ["application/json"], "commonParams": {"userInfo": {"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, "eid": {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}}, "paths": {"/feedData": {"post": {"summary": "check feed data permission ", "description": "校验数据权限", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/FeedDataArg"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/FeedDataResult"}}}}}, "/checkPermission": {"post": {"summary": "check feed data permission ", "description": "校验数据权限", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CheckPermissionArg"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CheckPermissionResult"}}}}}}, "definitions": {"FeedDataArg": {"type": "array", "items": {"$ref": "#/definitions/FeedData"}}, "FeedData": {"type": "object", "properties": {"dataId": {"type": "string"}, "dataType": {"type": "string"}, "data": {"type": "string"}, "tenantId": {"type": "string"}, "userId": {"type": "string"}, "displayName": {"type": "string"}}}, "FeedDataResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"feed_data": {"type": "array", "items": {"$ref": "#/definitions/FeedData"}}}}}}, "CheckPermissionArg": {"type": "array", "items": {"$ref": "#/definitions/CheckPermissionData"}}, "CheckPermissionData": {"type": "object", "properties": {"dataId": {"type": "string"}, "dataType": {"type": "string"}, "tenantId": {"type": "string"}, "userId": {"type": "string"}}}, "CheckPermissionResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"permission_result": {"type": "integer"}}}}}}}