import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.appframework.common.util.UdobjGrayUtil
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController
import com.facishare.paas.appframework.core.util.RequestUtil
import com.facishare.paas.appframework.metadata.LayoutExt
import com.facishare.paas.appframework.metadata.ObjectDescribeExt
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderConfig
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra
import com.facishare.paas.appframework.metadata.layout.LayoutTypes
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService
import com.facishare.paas.appframework.metadata.repository.model.MtChangeOrderRule
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService
import com.facishare.paas.metadata.api.IRecordTypeOption
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.LayoutRuleInfo
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.RecordTypeOption
import com.facishare.paas.metadata.impl.ui.layout.Layout
import com.facishare.paas.metadata.ui.layout.ILayout
import com.fxiaoke.bizconf.bean.ConfigPojo
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import spock.lang.Shared
import spock.lang.Specification

/**
 * @Desc
 * <AUTHOR>
 * @CreateTime 2019-10-18 17:33
 */

class StandardDescribeLayoutControllerTest extends Specification {
    ServiceFacade serviceFacade
    InfraServiceFacade infraServiceFacade
    FunctionPrivilegeService functionPrivilegeService
    ControllerContext controllerContext
    StandardDescribeLayoutController controller
    SpringBeanHolder springBeanHolder
    MaskFieldLogicService maskFieldLogicService
    StandardDescribeLayoutController.Arg arg

    @Shared
    String changeConfigStr = "{\"describeApiName\":\"udobj\",\"ei\":\"white:71660|70533|71698|86163|71557|85753|74255|78057|86238|86127|84051|71698|78586|82958|90137|90219|90270|90275|83952|82909|90234|84847|90540|90636|90533|88561|90801|89386\",\"changeOrderApiName\":\"udobj\",\"changeOrderStoreTableName\":\"mt_data\",\"supportFields\":\"black: unit|product_image|is_saleable|max_amount|min_amount|price_editable|price_mode|product_group_id|product_life_status|product_status|amount_editable|increment|is_package\",\"supportFieldTypes\":\"black:quote\",\"changeFields\":\"white:price_book_product_id|discount|product_price|sale_contract_line_id|subtotal|price_book_id|remark|quantity|price_book_price|sales_price|dynamic_amount|name\",\"changeFieldType\":\"black:auto_number|quote|signature|sign_in|payment|count|formula|master_detail\",\"originalFields\":\"white:price_book_product_id|discount|product_price|sale_contract_line_id|subtotal|price_book_id|remark|quantity|price_book_price|sales_price|dynamic_amount\",\"originalCount\":10,\"originalFieldType\":\"white:number|currency|percentile|text\"}\n"

    @Shared
    String changeRuleStr = "{\"id\":\"662789079b5ff7000195556a\",\"version\":1,\"tenantId\":\"74255\",\"objectDescribeApiName\":\"MtChangeRuleObj\",\"createTime\":1713867015537,\"createBy\":\"1000\",\"lastModifiedTime\":1713867015537,\"lastModifiedBy\":\"1000\",\"label\":\"测试111\",\"api_name\":\"change_rule_w0sm5__c\",\"original_describe_api_name\":\"object_LRH4n__c\",\"change_describe_api_name\":\"object_LRH4n__c__changeObj__c\",\"effective_timing\":\"1\",\"calibration_timing\":[\"0\"],\"is_active\":true,\"change_condition\":{\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"value_type\":0,\"operator\":\"ISN\",\"field_name\":\"name\",\"field_values\":[]}]}],\"message\":\"sadas \",\"where_type\":\"field\"},\"verify_condition\":{\"condition\":\"1!=1\",\"description\":\"\",\"message\":\"asda \",\"default_to_zero\":true},\"field_mapping\":[{\"source_api_name\":\"object_LRH4n__c\",\"target_api_name\":\"object_LRH4n__c__changeObj__c\",\"field_mapping\":[{\"source_field_api_name\":\"name\",\"target_field_api_name\":\"change_name\",\"changeable\":true,\"record_original_value\":false}]}]}\n"
    @Shared
    MtChangeOrderRule changeOrderRule

    @Shared
    String describeStr = "{\"tenant_id\":\"74255\",\"package\":\"CRM\",\"is_active\":true,\"last_modified_time\":1713866823623,\"create_time\":1713261101638,\"description\":\"\",\"last_modified_by\":\"1000\",\"display_name\":\"A对象\",\"created_by\":\"1000\",\"version\":14,\"is_open_display_name\":false,\"index_version\":1,\"icon_index\":0,\"is_deleted\":false,\"api_name\":\"object_LRH4n__c\",\"icon_path\":\"\",\"is_udef\":true,\"define_type\":\"custom\",\"short_name\":\"Fnl\",\"_id\":\"661e4a2d34831e0001405aa6\",\"fields\":{\"tenant_id\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713261101638,\"pattern\":\"\",\"is_unique\":false,\"description\":\"tenant_id\",\"label\":\"tenant_id\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"tenant_id\",\"define_type\":\"system\",\"index_name\":\"ei\",\"max_length\":200,\"status\":\"released\"},\"lock_rule\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713261101411,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"锁定规则\",\"is_unique\":false,\"rules\":[],\"default_value\":\"default_lock_rule\",\"label\":\"锁定规则\",\"type\":\"lock_rule\",\"field_num\":1,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_rule\",\"define_type\":\"package\",\"_id\":\"661e4a2d34831e0001405a8d\",\"is_single\":false,\"label_r\":\"锁定规则\",\"is_index_field\":false,\"index_name\":\"s_0\",\"status\":\"new\"},\"data_own_organization\":{\"describe_api_name\":\"object_LRH4n__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"department\",\"is_required\":false,\"wheres\":[],\"optional_type\":\"department\",\"define_type\":\"package\",\"is_single\":true,\"label_r\":\"归属组织\",\"index_name\":\"a_0\",\"is_index\":true,\"is_active\":true,\"create_time\":1713261101627,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"归属组织\",\"is_need_convert\":false,\"api_name\":\"data_own_organization\",\"_id\":\"661e4a2d34831e0001405a95\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"released\"},\"version_number\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1713866823690,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":true,\"label\":\"版本号\",\"type\":\"text\",\"is_required\":false,\"api_name\":\"version_number\",\"define_type\":\"package\",\"_id\":\"66278847ec1968605dc15652\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"t_10\",\"max_length\":256,\"status\":\"released\",\"description\":\"\"},\"origin_source\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713261101638,\"is_unique\":false,\"label\":\"数据来源\",\"type\":\"select_one\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"origin_source\",\"options\":[{\"label\":\"数据同步\",\"value\":\"0\"}],\"define_type\":\"system\",\"is_extend\":false,\"index_name\":\"s_os\",\"config\":{\"display\":0},\"status\":\"released\",\"description\":\"\"},\"lock_user\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713261101505,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"加锁人\",\"is_unique\":false,\"label\":\"加锁人\",\"type\":\"employee\",\"field_num\":4,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_user\",\"define_type\":\"package\",\"_id\":\"661e4a2d34831e0001405a8f\",\"is_single\":true,\"label_r\":\"加锁人\",\"is_index_field\":false,\"index_name\":\"a_1\",\"status\":\"new\"},\"changed_status\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1713866823690,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"default_value\":\"normal\",\"label\":\"变更状态\",\"type\":\"select_one\",\"is_required\":false,\"api_name\":\"changed_status\",\"options\":[{\"label\":\"正常\",\"value\":\"normal\"},{\"label\":\"变更中\",\"value\":\"in_change\"}],\"define_type\":\"package\",\"_id\":\"66278847ec1968605dc15656\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"s_9\",\"config\":{},\"status\":\"released\",\"description\":\"\"},\"mc_exchange_rate\":{\"describe_api_name\":\"object_LRH4n__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"type\":\"number\",\"decimal_places\":8,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"汇率\",\"index_name\":\"d_0\",\"max_length\":18,\"is_index\":true,\"is_active\":true,\"create_time\":1713261101624,\"is_encrypted\":false,\"display_style\":\"input\",\"step_value\":1,\"length\":10,\"default_value\":\"\",\"label\":\"汇率\",\"field_num\":6,\"api_name\":\"mc_exchange_rate\",\"_id\":\"661e4a2d34831e0001405a92\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\"},\"is_deleted\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":false,\"create_time\":1713261101638,\"is_unique\":false,\"description\":\"is_deleted\",\"default_value\":false,\"label\":\"is_deleted\",\"type\":\"true_or_false\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"is_deleted\",\"options\":[],\"define_type\":\"system\",\"index_name\":\"is_del\",\"status\":\"released\"},\"life_status_before_invalid\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713261101506,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"description\":\"作废前生命状态\",\"is_unique\":false,\"label\":\"作废前生命状态\",\"type\":\"text\",\"field_num\":7,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"life_status_before_invalid\",\"define_type\":\"package\",\"_id\":\"661e4a2d34831e0001405a90\",\"is_single\":false,\"label_r\":\"作废前生命状态\",\"is_index_field\":false,\"index_name\":\"t_0\",\"max_length\":256,\"status\":\"new\"},\"object_describe_api_name\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713261101638,\"pattern\":\"\",\"is_unique\":false,\"description\":\"object_describe_api_name\",\"label\":\"object_describe_api_name\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"object_describe_api_name\",\"define_type\":\"system\",\"index_name\":\"api_name\",\"max_length\":200,\"status\":\"released\"},\"owner_department\":{\"describe_api_name\":\"object_LRH4n__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":true,\"label_r\":\"负责人主属部门\",\"index_name\":\"owner_dept\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1713261101614,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"负责人主属部门\",\"is_need_convert\":false,\"api_name\":\"owner_department\",\"_id\":\"661e4a2d34831e0001405a84\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\"},\"out_owner\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1713261101638,\"is_unique\":false,\"label\":\"外部负责人\",\"type\":\"employee\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"out_owner\",\"define_type\":\"system\",\"is_single\":true,\"index_name\":\"o_owner\",\"config\":{\"display\":1},\"status\":\"released\",\"description\":\"\"},\"mc_functional_currency\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713261101625,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"label\":\"本位币\",\"type\":\"select_one\",\"field_num\":9,\"is_required\":false,\"api_name\":\"mc_functional_currency\",\"options\":[{\"not_usable\":false,\"label\":\"CNY - China Yuan\",\"value\":\"CNY\"}],\"define_type\":\"package\",\"_id\":\"661e4a2d34831e0001405a93\",\"is_single\":false,\"label_r\":\"本位币\",\"is_index_field\":false,\"index_name\":\"s_4\",\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"owner\":{\"describe_api_name\":\"object_LRH4n__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"employee\",\"is_required\":true,\"wheres\":[],\"define_type\":\"package\",\"is_single\":true,\"label_r\":\"负责人\",\"index_name\":\"owner\",\"is_index\":true,\"is_active\":true,\"create_time\":1713261101613,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"负责人\",\"is_need_convert\":false,\"api_name\":\"owner\",\"_id\":\"661e4a2d34831e0001405a83\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\"},\"changed_time\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1713866823690,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"label\":\"变更时间\",\"type\":\"date_time\",\"time_zone\":\"GMT+8\",\"is_required\":false,\"api_name\":\"changed_time\",\"define_type\":\"package\",\"date_format\":\"yyyy-MM-dd HH:mm:ss\",\"_id\":\"66278847ec1968605dc15653\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"l_4\",\"status\":\"released\",\"description\":\"\"},\"lock_status\":{\"describe_api_name\":\"object_LRH4n__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"锁定状态\",\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"options\":[{\"label\":\"未锁定\",\"value\":\"0\"},{\"label\":\"锁定\",\"value\":\"1\"}],\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"锁定状态\",\"index_name\":\"s_1\",\"is_index\":true,\"is_active\":true,\"create_time\":1713261101502,\"is_encrypted\":false,\"default_value\":\"0\",\"label\":\"锁定状态\",\"field_num\":2,\"is_need_convert\":false,\"api_name\":\"lock_status\",\"_id\":\"661e4a2d34831e0001405a8e\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"package\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713261101638,\"pattern\":\"\",\"is_unique\":false,\"description\":\"package\",\"label\":\"package\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"package\",\"define_type\":\"system\",\"index_name\":\"pkg\",\"max_length\":200,\"status\":\"released\"},\"last_modified_time\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":true,\"create_time\":1713261101638,\"is_unique\":false,\"description\":\"last_modified_time\",\"label\":\"最后修改时间\",\"type\":\"date_time\",\"time_zone\":\"\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"last_modified_time\",\"define_type\":\"system\",\"date_format\":\"yyyy-MM-dd HH:mm:ss\",\"index_name\":\"md_time\",\"status\":\"released\"},\"changed_reason\":{\"expression_type\":\"json\",\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1713866823690,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":false,\"label\":\"变更原因\",\"type\":\"long_text\",\"is_required\":false,\"api_name\":\"changed_reason\",\"define_type\":\"package\",\"_id\":\"66278847ec1968605dc15654\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"t_11\",\"max_length\":100000,\"status\":\"released\",\"description\":\"\"},\"create_time\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":true,\"create_time\":1713261101638,\"is_unique\":false,\"description\":\"create_time\",\"label\":\"创建时间\",\"type\":\"date_time\",\"time_zone\":\"\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"create_time\",\"define_type\":\"system\",\"date_format\":\"yyyy-MM-dd HH:mm:ss\",\"index_name\":\"crt_time\",\"status\":\"released\"},\"life_status\":{\"describe_api_name\":\"object_LRH4n__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"options\":[{\"label\":\"未生效\",\"value\":\"ineffective\"},{\"label\":\"审核中\",\"value\":\"under_review\"},{\"label\":\"正常\",\"value\":\"normal\"},{\"label\":\"变更中\",\"value\":\"in_change\"},{\"label\":\"作废\",\"value\":\"invalid\"}],\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"生命状态\",\"index_name\":\"s_2\",\"is_index\":true,\"is_active\":true,\"create_time\":1713261101621,\"is_encrypted\":false,\"default_value\":\"normal\",\"label\":\"生命状态\",\"field_num\":3,\"is_need_convert\":false,\"api_name\":\"life_status\",\"_id\":\"661e4a2d34831e0001405a8b\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"last_modified_by\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1713261101638,\"is_unique\":false,\"label\":\"最后修改人\",\"type\":\"employee\",\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"last_modified_by\",\"define_type\":\"system\",\"is_single\":true,\"index_name\":\"md_by\",\"status\":\"released\",\"description\":\"\"},\"out_tenant_id\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713261101638,\"pattern\":\"\",\"is_unique\":false,\"description\":\"out_tenant_id\",\"label\":\"外部企业\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"out_tenant_id\",\"define_type\":\"system\",\"index_name\":\"o_ei\",\"config\":{\"display\":0},\"max_length\":200,\"status\":\"released\"},\"mc_currency\":{\"describe_api_name\":\"object_LRH4n__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"options\":[{\"label\":\"BYR - 白俄罗斯卢布\",\"value\":\"BYR\"},{\"label\":\"AED - UAE Dirham\",\"value\":\"AED\"},{\"label\":\"BIF - 布隆迪法郎\",\"value\":\"BIF\"},{\"label\":\"BRL - 巴西币\",\"value\":\"BRL\"},{\"label\":\"BOB - 玻利维亚的玻利维亚诺\",\"value\":\"BOB\"},{\"label\":\"BYN - 白俄罗斯卢布\",\"value\":\"BYN\"},{\"label\":\"BZD - 伯利兹元\",\"value\":\"BZD\"},{\"label\":\"BOV - 玻利维亚姆夫多尔\",\"value\":\"BOV\"},{\"label\":\"BRB - 巴西克鲁塞罗（旧）\",\"value\":\"BRB\"},{\"label\":\"BSD - 巴哈马元\",\"value\":\"BSD\"},{\"label\":\"qwe\",\"value\":\"qwe\"},{\"label\":\"zas\",\"value\":\"zas\"},{\"label\":\"翻译ASD\",\"value\":\"asd\"},{\"label\":\"AWG - 阿鲁巴岛弗罗林\",\"value\":\"AWG\"},{\"label\":\"ada\",\"value\":\"ada\"},{\"label\":\"BTN - 不丹卢比\",\"value\":\"BTN\"},{\"label\":\"BGN - Bulgarian Lev\",\"value\":\"BGN\"},{\"label\":\"BHD - Bahraini Dinar\",\"value\":\"BHD\"},{\"label\":\"BND - Brunei Dollar\",\"value\":\"BND\"},{\"label\":\"BMD - 百慕大元\",\"value\":\"BMD\"},{\"label\":\"AOA - 安哥拉宽扎\",\"value\":\"AOA\"},{\"label\":\"ANG - 荷属安地列斯盾\",\"value\":\"ANG\"},{\"label\":\"ARS - 阿根廷比索\",\"value\":\"ARS\"},{\"label\":\"AMD - 亚美尼亚打兰\",\"value\":\"AMD\"},{\"label\":\"AUD - Australian Dollar\",\"value\":\"AUD\"},{\"label\":\"AFN - Afghanistan Afghani (New)\",\"value\":\"AFN\"},{\"label\":\"AZN - 阿塞拜疆马纳特\",\"value\":\"AZN\"},{\"label\":\"USD - U.S. Dollar\",\"value\":\"USD\"},{\"label\":\"BAM - 自由兑换马克\",\"value\":\"BAM\"},{\"label\":\"BBD - 巴巴多斯元\",\"value\":\"BBD\"},{\"label\":\"BDT - 孟加拉国塔卡\",\"value\":\"BDT\"},{\"label\":\"BWP - 博茨瓦纳普拉\",\"value\":\"BWP\"},{\"label\":\"ALL - 阿尔巴尼亚列克\",\"value\":\"ALL\"},{\"label\":\"CNY - China Yuan\",\"value\":\"CNY\"}],\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"币种\",\"index_name\":\"s_3\",\"is_index\":true,\"is_active\":true,\"create_time\":1713261101623,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"币种\",\"field_num\":5,\"api_name\":\"mc_currency\",\"_id\":\"661e4a2d34831e0001405a91\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"version\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":false,\"create_time\":1713261101638,\"length\":8,\"is_unique\":false,\"description\":\"version\",\"label\":\"version\",\"type\":\"number\",\"decimal_places\":0,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"version\",\"define_type\":\"system\",\"index_name\":\"version\",\"round_mode\":4,\"status\":\"released\"},\"created_by\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1713261101638,\"is_unique\":false,\"label\":\"创建人\",\"type\":\"employee\",\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"created_by\",\"define_type\":\"system\",\"is_single\":true,\"index_name\":\"crt_by\",\"status\":\"released\",\"description\":\"\"},\"relevant_team\":{\"describe_api_name\":\"object_LRH4n__c\",\"embedded_fields\":{\"teamMemberEmployee\":{\"is_index\":true,\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"teamMemberEmployee\",\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员员工\",\"label\":\"成员员工\",\"type\":\"employee\",\"is_single\":true,\"help_text\":\"成员员工\"},\"teamMemberRole\":{\"is_index\":true,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"teamMemberRole\",\"options\":[{\"label\":\"负责人\",\"value\":\"1\"},{\"label\":\"普通成员\",\"value\":\"4\"}],\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员角色\",\"label\":\"成员角色\",\"type\":\"select_one\",\"help_text\":\"成员角色\"},\"teamMemberPermissionType\":{\"is_index\":true,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"teamMemberPermissionType\",\"options\":[{\"label\":\"只读\",\"value\":\"1\"},{\"label\":\"读写\",\"value\":\"2\"}],\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员权限类型\",\"label\":\"成员权限类型\",\"type\":\"select_one\",\"help_text\":\"成员权限类型\"}},\"is_index\":true,\"is_active\":true,\"create_time\":1713261101622,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"label\":\"相关团队\",\"type\":\"embedded_object_list\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"relevant_team\",\"define_type\":\"package\",\"_id\":\"661e4a2d34831e0001405a8c\",\"is_single\":false,\"label_r\":\"相关团队\",\"is_index_field\":false,\"index_name\":\"a_team\",\"help_text\":\"相关团队\",\"status\":\"new\"},\"record_type\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1713261101620,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"record_type\",\"is_unique\":false,\"label\":\"业务类型\",\"type\":\"record_type\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"record_type\",\"options\":[{\"is_active\":true,\"api_name\":\"default__c\",\"description\":\"预设业务类型\",\"label\":\"预设业务类型\"}],\"define_type\":\"package\",\"_id\":\"661e4a2d34831e0001405a8a\",\"is_single\":false,\"label_r\":\"业务类型\",\"is_index_field\":false,\"index_name\":\"r_type\",\"config\":{},\"help_text\":\"\",\"status\":\"released\"},\"data_own_department\":{\"describe_api_name\":\"object_LRH4n__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"department\",\"is_required\":false,\"wheres\":[],\"optional_type\":\"department\",\"define_type\":\"package\",\"is_single\":true,\"label_r\":\"归属部门\",\"index_name\":\"data_owner_dept_id\",\"is_index\":true,\"is_active\":true,\"create_time\":1713261101615,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"归属部门\",\"is_need_convert\":false,\"api_name\":\"data_own_department\",\"_id\":\"661e4a2d34831e0001405a85\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\"},\"changed_by\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1713866823691,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"label\":\"变更人\",\"type\":\"employee\",\"is_required\":false,\"api_name\":\"changed_by\",\"define_type\":\"package\",\"_id\":\"66278847ec1968605dc15655\",\"is_index_field\":false,\"is_single\":true,\"index_name\":\"a_6\",\"status\":\"released\",\"description\":\"\"},\"name\":{\"describe_api_name\":\"object_LRH4n__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"description\":\"name\",\"is_unique\":true,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":true,\"define_type\":\"system\",\"input_mode\":\"\",\"is_single\":false,\"label_r\":\"主属性\",\"index_name\":\"name\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1713261101812,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"主属性\",\"api_name\":\"name\",\"_id\":\"661e4a2d34831e0001405a82\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\"},\"mc_exchange_rate_version\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713261101626,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"description\":\"\",\"is_unique\":false,\"label\":\"汇率版本\",\"type\":\"text\",\"field_num\":8,\"is_required\":false,\"api_name\":\"mc_exchange_rate_version\",\"define_type\":\"package\",\"_id\":\"661e4a2d34831e0001405a94\",\"is_single\":false,\"label_r\":\"汇率版本\",\"is_index_field\":false,\"index_name\":\"t_1\",\"max_length\":256,\"help_text\":\"\",\"status\":\"new\"},\"_id\":{\"describe_api_name\":\"object_LRH4n__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713261101638,\"pattern\":\"\",\"is_unique\":false,\"description\":\"_id\",\"label\":\"_id\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"_id\",\"define_type\":\"system\",\"index_name\":\"_id\",\"max_length\":200,\"status\":\"released\"}},\"release_version\":\"6.4\",\"actions\":{}}\n"

    @Shared
    IObjectDescribe describe = null

    @Shared
    String detailDescribeStr = "{\"tenant_id\":\"74255\",\"package\":\"CRM\",\"is_active\":true,\"last_modified_time\":1713876950592,\"create_time\":1713162885248,\"description\":\"\",\"last_modified_by\":\"1000\",\"display_name\":\"列表页布局\",\"created_by\":\"1000\",\"version\":28,\"is_open_display_name\":false,\"index_version\":1,\"icon_index\":0,\"is_deleted\":false,\"api_name\":\"object_p0o6I__c\",\"icon_path\":\"\",\"is_udef\":true,\"define_type\":\"custom\",\"short_name\":\"34Y\",\"_id\":\"661cca857bafa80001638a24\",\"fields\":{\"tenant_id\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713162885248,\"pattern\":\"\",\"is_unique\":false,\"description\":\"tenant_id\",\"label\":\"tenant_id\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"tenant_id\",\"define_type\":\"system\",\"index_name\":\"ei\",\"max_length\":200,\"status\":\"released\"},\"lock_rule\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713162885185,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"锁定规则\",\"is_unique\":false,\"rules\":[],\"default_value\":\"default_lock_rule\",\"label\":\"锁定规则\",\"type\":\"lock_rule\",\"field_num\":1,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_rule\",\"define_type\":\"package\",\"_id\":\"661cca857bafa80001638a1b\",\"is_single\":false,\"label_r\":\"锁定规则\",\"is_index_field\":false,\"index_name\":\"s_0\",\"status\":\"new\"},\"data_own_organization\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1713162885254,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"label\":\"归属组织\",\"type\":\"department\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"data_own_organization\",\"define_type\":\"package\",\"_id\":\"661cca857bafa80001638a23\",\"is_single\":true,\"label_r\":\"归属组织\",\"is_index_field\":false,\"index_name\":\"a_0\",\"status\":\"released\",\"description\":\"\"},\"field_4S3lk__c\":{\"describe_api_name\":\"object_p0o6I__c\",\"default_is_expression\":true,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"department\",\"is_required\":false,\"wheres\":[],\"enable_clone\":true,\"optional_type\":\"department\",\"define_type\":\"custom\",\"is_single\":true,\"index_name\":\"a_5\",\"is_index\":true,\"is_active\":true,\"create_time\":1713432520989,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"部门\",\"field_num\":18,\"api_name\":\"field_4S3lk__c\",\"_id\":\"6620e7ccbf17740001a6a384\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\"},\"origin_source\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713162885248,\"is_unique\":false,\"label\":\"数据来源\",\"type\":\"select_one\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"origin_source\",\"options\":[{\"label\":\"数据同步\",\"value\":\"0\"}],\"define_type\":\"system\",\"is_extend\":false,\"index_name\":\"s_os\",\"config\":{\"display\":0},\"status\":\"released\",\"description\":\"\"},\"lock_user\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713162885185,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"加锁人\",\"is_unique\":false,\"label\":\"加锁人\",\"type\":\"employee\",\"field_num\":4,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_user\",\"define_type\":\"package\",\"_id\":\"661cca857bafa80001638a1d\",\"is_single\":true,\"label_r\":\"加锁人\",\"is_index_field\":false,\"index_name\":\"a_1\",\"status\":\"new\"},\"field_5QVeY__c\":{\"describe_api_name\":\"object_p0o6I__c\",\"default_is_expression\":true,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"employee\",\"is_required\":false,\"wheres\":[],\"enable_clone\":true,\"define_type\":\"custom\",\"is_single\":true,\"index_name\":\"a_4\",\"is_index\":true,\"is_active\":true,\"create_time\":1713171486905,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"人员\",\"field_num\":16,\"api_name\":\"field_5QVeY__c\",\"_id\":\"661cec1f7bafa80001684eeb\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\"},\"mc_exchange_rate\":{\"describe_api_name\":\"object_p0o6I__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"number\",\"decimal_places\":8,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"汇率\",\"index_name\":\"d_0\",\"max_length\":18,\"is_index\":true,\"is_active\":true,\"create_time\":1713162885251,\"is_encrypted\":false,\"length\":10,\"label\":\"汇率\",\"field_num\":6,\"api_name\":\"mc_exchange_rate\",\"_id\":\"661cca857bafa80001638a20\",\"is_index_field\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\"},\"is_deleted\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":false,\"create_time\":1713162885248,\"is_unique\":false,\"description\":\"is_deleted\",\"default_value\":false,\"label\":\"is_deleted\",\"type\":\"true_or_false\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"is_deleted\",\"options\":[],\"define_type\":\"system\",\"index_name\":\"is_del\",\"status\":\"released\"},\"field_yE4rr__c\":{\"describe_api_name\":\"object_p0o6I__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":false,\"type\":\"long_text\",\"default_to_zero\":false,\"is_required\":false,\"enable_clone\":true,\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"t_3\",\"max_length\":2000,\"is_index\":true,\"is_active\":true,\"create_time\":1713166153115,\"is_encrypted\":false,\"min_length\":0,\"default_value\":\"\",\"label\":\"多行文本\",\"field_num\":11,\"api_name\":\"field_yE4rr__c\",\"_id\":\"661cd74a7bafa80001670713\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"life_status_before_invalid\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713162885186,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"description\":\"作废前生命状态\",\"is_unique\":false,\"label\":\"作废前生命状态\",\"type\":\"text\",\"field_num\":7,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"life_status_before_invalid\",\"define_type\":\"package\",\"_id\":\"661cca857bafa80001638a1e\",\"is_single\":false,\"label_r\":\"作废前生命状态\",\"is_index_field\":false,\"index_name\":\"t_0\",\"max_length\":256,\"status\":\"new\"},\"object_describe_api_name\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713162885248,\"pattern\":\"\",\"is_unique\":false,\"description\":\"object_describe_api_name\",\"label\":\"object_describe_api_name\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"object_describe_api_name\",\"define_type\":\"system\",\"index_name\":\"api_name\",\"max_length\":200,\"status\":\"released\"},\"owner_department\":{\"describe_api_name\":\"object_p0o6I__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":true,\"label_r\":\"负责人主属部门\",\"index_name\":\"owner_dept\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1713162885241,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"负责人主属部门\",\"is_need_convert\":false,\"api_name\":\"owner_department\",\"_id\":\"661cca857bafa80001638a12\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"out_owner\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1713162885248,\"is_unique\":false,\"label\":\"外部负责人\",\"type\":\"employee\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"out_owner\",\"define_type\":\"system\",\"is_single\":true,\"index_name\":\"o_owner\",\"config\":{\"display\":1},\"status\":\"released\",\"description\":\"\"},\"mc_functional_currency\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713162885252,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"label\":\"本位币\",\"type\":\"select_one\",\"field_num\":9,\"is_required\":false,\"api_name\":\"mc_functional_currency\",\"options\":[{\"not_usable\":false,\"label\":\"CNY - China Yuan\",\"value\":\"CNY\"}],\"define_type\":\"package\",\"_id\":\"661cca857bafa80001638a21\",\"is_single\":false,\"label_r\":\"本位币\",\"is_index_field\":false,\"index_name\":\"s_4\",\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"owner\":{\"describe_api_name\":\"object_p0o6I__c\",\"default_is_expression\":false,\"is_index\":true,\"is_active\":true,\"create_time\":1713162885240,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"where_type\":\"field\",\"default_value\":\"\",\"label\":\"负责人\",\"type\":\"employee\",\"is_need_convert\":false,\"is_required\":true,\"wheres\":[],\"api_name\":\"owner\",\"define_type\":\"package\",\"_id\":\"661cca857bafa80001638a11\",\"is_single\":true,\"label_r\":\"负责人\",\"is_index_field\":false,\"index_name\":\"owner\",\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"lock_status\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1713162885185,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"锁定状态\",\"is_unique\":false,\"default_value\":\"0\",\"label\":\"锁定状态\",\"type\":\"select_one\",\"field_num\":2,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_status\",\"options\":[{\"label\":\"未锁定\",\"value\":\"0\"},{\"label\":\"锁定\",\"value\":\"1\"}],\"define_type\":\"package\",\"_id\":\"661cca857bafa80001638a1c\",\"is_single\":false,\"label_r\":\"锁定状态\",\"is_index_field\":false,\"index_name\":\"s_1\",\"config\":{},\"status\":\"new\"},\"field_w8oF8__c\":{\"describe_api_name\":\"object_p0o6I__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"enable_clone\":true,\"options\":[{\"label\":\"示例选项\",\"value\":\"option1\"},{\"not_usable\":true,\"label\":\"其他\",\"value\":\"other\"}],\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"s_5\",\"is_index\":true,\"is_active\":true,\"create_time\":1713166156364,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"单选\",\"field_num\":12,\"api_name\":\"field_w8oF8__c\",\"_id\":\"661cd74d7bafa8000167074d\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"package\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713162885248,\"pattern\":\"\",\"is_unique\":false,\"description\":\"package\",\"label\":\"package\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"package\",\"define_type\":\"system\",\"index_name\":\"pkg\",\"max_length\":200,\"status\":\"released\"},\"last_modified_time\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":true,\"create_time\":1713162885248,\"is_unique\":false,\"description\":\"last_modified_time\",\"label\":\"最后修改时间\",\"type\":\"date_time\",\"time_zone\":\"\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"last_modified_time\",\"define_type\":\"system\",\"date_format\":\"yyyy-MM-dd HH:mm:ss\",\"index_name\":\"md_time\",\"status\":\"released\"},\"create_time\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":true,\"create_time\":1713162885248,\"is_unique\":false,\"description\":\"create_time\",\"label\":\"创建时间\",\"type\":\"date_time\",\"time_zone\":\"\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"create_time\",\"define_type\":\"system\",\"date_format\":\"yyyy-MM-dd HH:mm:ss\",\"index_name\":\"crt_time\",\"status\":\"released\"},\"field_4j6xv__c\":{\"describe_api_name\":\"object_p0o6I__c\",\"default_is_expression\":true,\"auto_adapt_places\":false,\"is_unique\":false,\"where_type\":\"field\",\"type\":\"department_many\",\"is_required\":false,\"wheres\":[],\"enable_clone\":true,\"optional_type\":\"department\",\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"a_6\",\"is_index\":true,\"is_active\":true,\"create_time\":1713434192108,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"部门(多选)\",\"field_num\":20,\"api_name\":\"field_4j6xv__c\",\"_id\":\"6620ee51569951000173a134\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"life_status\":{\"describe_api_name\":\"object_p0o6I__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"options\":[{\"label\":\"未生效\",\"value\":\"ineffective\"},{\"label\":\"审核中\",\"value\":\"under_review\"},{\"label\":\"正常\",\"value\":\"normal\"},{\"label\":\"变更中\",\"value\":\"in_change\"},{\"label\":\"作废\",\"value\":\"invalid\"}],\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"生命状态\",\"index_name\":\"s_2\",\"is_index\":true,\"is_active\":true,\"create_time\":1713162885248,\"is_encrypted\":false,\"default_value\":\"normal\",\"label\":\"生命状态\",\"field_num\":3,\"is_need_convert\":false,\"api_name\":\"life_status\",\"_id\":\"661cca857bafa80001638a19\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"field_esuc1__c\":{\"describe_api_name\":\"object_p0o6I__c\",\"default_is_expression\":true,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"enable_clone\":true,\"define_type\":\"custom\",\"input_mode\":\"\",\"is_single\":false,\"index_name\":\"t_2\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1713166148812,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"单行文本\",\"field_num\":10,\"api_name\":\"field_esuc1__c\",\"_id\":\"661cd7457bafa800016706d8\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\"},\"last_modified_by\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1713162885248,\"is_unique\":false,\"label\":\"最后修改人\",\"type\":\"employee\",\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"last_modified_by\",\"define_type\":\"system\",\"is_single\":true,\"index_name\":\"md_by\",\"status\":\"released\",\"description\":\"\"},\"field_W1xVR__c\":{\"describe_api_name\":\"object_p0o6I__c\",\"default_is_expression\":true,\"is_index\":true,\"is_active\":true,\"create_time\":1713434261766,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"where_type\":\"field\",\"default_value\":\"\",\"label\":\"人员(多选)\",\"type\":\"employee_many\",\"field_num\":21,\"is_required\":false,\"wheres\":[],\"enable_clone\":true,\"api_name\":\"field_W1xVR__c\",\"define_type\":\"custom\",\"_id\":\"6620ee96569951000173a206\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"a_7\",\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"out_tenant_id\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713162885248,\"pattern\":\"\",\"is_unique\":false,\"description\":\"out_tenant_id\",\"label\":\"外部企业\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"out_tenant_id\",\"define_type\":\"system\",\"index_name\":\"o_ei\",\"config\":{\"display\":0},\"max_length\":200,\"status\":\"released\"},\"mc_currency\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1713162885250,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"币种\",\"type\":\"select_one\",\"field_num\":5,\"is_required\":false,\"api_name\":\"mc_currency\",\"options\":[{\"not_usable\":false,\"label\":\"BYR - 白俄罗斯卢布\",\"value\":\"BYR\"},{\"not_usable\":false,\"label\":\"AED - UAE Dirham\",\"value\":\"AED\"},{\"not_usable\":false,\"label\":\"BIF - 布隆迪法郎\",\"value\":\"BIF\"},{\"not_usable\":false,\"label\":\"BRL - 巴西币\",\"value\":\"BRL\"},{\"not_usable\":false,\"label\":\"BOB - 玻利维亚的玻利维亚诺\",\"value\":\"BOB\"},{\"not_usable\":false,\"label\":\"BYN - 白俄罗斯卢布\",\"value\":\"BYN\"},{\"not_usable\":false,\"label\":\"BZD - 伯利兹元\",\"value\":\"BZD\"},{\"not_usable\":false,\"label\":\"BOV - 玻利维亚姆夫多尔\",\"value\":\"BOV\"},{\"not_usable\":false,\"label\":\"BRB - 巴西克鲁塞罗（旧）\",\"value\":\"BRB\"},{\"not_usable\":false,\"label\":\"BSD - 巴哈马元\",\"value\":\"BSD\"},{\"not_usable\":false,\"label\":\"qwe\",\"value\":\"qwe\"},{\"not_usable\":false,\"label\":\"zas\",\"value\":\"zas\"},{\"not_usable\":false,\"label\":\"翻译ASD\",\"value\":\"asd\"},{\"not_usable\":false,\"label\":\"AWG - 阿鲁巴岛弗罗林\",\"value\":\"AWG\"},{\"not_usable\":false,\"label\":\"ada\",\"value\":\"ada\"},{\"not_usable\":false,\"label\":\"BTN - 不丹卢比\",\"value\":\"BTN\"},{\"not_usable\":false,\"label\":\"BGN - Bulgarian Lev\",\"value\":\"BGN\"},{\"not_usable\":false,\"label\":\"BHD - Bahraini Dinar\",\"value\":\"BHD\"},{\"not_usable\":false,\"label\":\"BND - Brunei Dollar\",\"value\":\"BND\"},{\"not_usable\":false,\"label\":\"BMD - 百慕大元\",\"value\":\"BMD\"},{\"not_usable\":false,\"label\":\"AOA - 安哥拉宽扎\",\"value\":\"AOA\"},{\"not_usable\":false,\"label\":\"ANG - 荷属安地列斯盾\",\"value\":\"ANG\"},{\"not_usable\":false,\"label\":\"ARS - 阿根廷比索\",\"value\":\"ARS\"},{\"not_usable\":false,\"label\":\"AMD - 亚美尼亚打兰\",\"value\":\"AMD\"},{\"not_usable\":false,\"label\":\"AUD - Australian Dollar\",\"value\":\"AUD\"},{\"not_usable\":false,\"label\":\"AFN - Afghanistan Afghani (New)\",\"value\":\"AFN\"},{\"not_usable\":false,\"label\":\"AZN - 阿塞拜疆马纳特\",\"value\":\"AZN\"},{\"not_usable\":false,\"label\":\"USD - U.S. Dollar\",\"value\":\"USD\"},{\"not_usable\":false,\"label\":\"BAM - 自由兑换马克\",\"value\":\"BAM\"},{\"not_usable\":false,\"label\":\"BBD - 巴巴多斯元\",\"value\":\"BBD\"},{\"not_usable\":false,\"label\":\"BDT - 孟加拉国塔卡\",\"value\":\"BDT\"},{\"not_usable\":false,\"label\":\"BWP - 博茨瓦纳普拉\",\"value\":\"BWP\"},{\"not_usable\":false,\"label\":\"ALL - 阿尔巴尼亚列克\",\"value\":\"ALL\"},{\"not_usable\":false,\"label\":\"CNY - China Yuan\",\"value\":\"CNY\"}],\"define_type\":\"package\",\"_id\":\"661cca857bafa80001638a1f\",\"is_single\":false,\"label_r\":\"币种\",\"is_index_field\":false,\"index_name\":\"s_3\",\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"version\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":false,\"create_time\":1713162885248,\"length\":8,\"is_unique\":false,\"description\":\"version\",\"label\":\"version\",\"type\":\"number\",\"decimal_places\":0,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"version\",\"define_type\":\"system\",\"index_name\":\"version\",\"round_mode\":4,\"status\":\"released\"},\"created_by\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1713162885248,\"is_unique\":false,\"label\":\"创建人\",\"type\":\"employee\",\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"created_by\",\"define_type\":\"system\",\"is_single\":true,\"index_name\":\"crt_by\",\"status\":\"released\",\"description\":\"\"},\"relevant_team\":{\"describe_api_name\":\"object_p0o6I__c\",\"embedded_fields\":{\"teamMemberEmployee\":{\"is_index\":true,\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"teamMemberEmployee\",\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员员工\",\"label\":\"成员员工\",\"type\":\"employee\",\"is_single\":true,\"help_text\":\"成员员工\"},\"teamMemberRole\":{\"is_index\":true,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"teamMemberRole\",\"options\":[{\"label\":\"负责人\",\"value\":\"1\"},{\"label\":\"普通成员\",\"value\":\"4\"}],\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员角色\",\"label\":\"成员角色\",\"type\":\"select_one\",\"help_text\":\"成员角色\"},\"teamMemberPermissionType\":{\"is_index\":true,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"teamMemberPermissionType\",\"options\":[{\"label\":\"只读\",\"value\":\"1\"},{\"label\":\"读写\",\"value\":\"2\"}],\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员权限类型\",\"label\":\"成员权限类型\",\"type\":\"select_one\",\"help_text\":\"成员权限类型\"}},\"is_index\":true,\"is_active\":true,\"create_time\":1713162885249,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"label\":\"相关团队\",\"type\":\"embedded_object_list\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"relevant_team\",\"define_type\":\"package\",\"_id\":\"661cca857bafa80001638a1a\",\"is_single\":false,\"label_r\":\"相关团队\",\"is_index_field\":false,\"index_name\":\"a_team\",\"help_text\":\"相关团队\",\"status\":\"new\",\"description\":\"\"},\"record_type\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1713162885247,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"record_type\",\"is_unique\":false,\"label\":\"业务类型\",\"type\":\"record_type\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"record_type\",\"options\":[{\"is_active\":true,\"api_name\":\"default__c\",\"description\":\"预设业务类型\",\"label\":\"预设业务类型\"}],\"define_type\":\"package\",\"_id\":\"661cca857bafa80001638a18\",\"is_single\":false,\"label_r\":\"业务类型\",\"is_index_field\":false,\"index_name\":\"r_type\",\"config\":{},\"help_text\":\"\",\"status\":\"released\"},\"field_ep12h__c\":{\"describe_api_name\":\"object_p0o6I__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"type\":\"select_many\",\"default_to_zero\":false,\"is_required\":false,\"enable_clone\":true,\"options\":[{\"label\":\"示例选项\",\"value\":\"option1\"},{\"not_usable\":true,\"label\":\"其他\",\"value\":\"other\"}],\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"a_2\",\"is_index\":true,\"is_active\":true,\"create_time\":1713166159295,\"is_encrypted\":false,\"default_value\":[],\"label\":\"多选\",\"field_num\":13,\"api_name\":\"field_ep12h__c\",\"_id\":\"661cd74f7bafa80001670787\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"field_E9C2c__c\":{\"describe_api_name\":\"object_p0o6I__c\",\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"master_detail\",\"is_required\":true,\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"s_7\",\"is_index\":true,\"is_active\":true,\"create_time\":1713854433059,\"is_encrypted\":false,\"label\":\"主从关系\",\"target_api_name\":\"object_LRH4n__c\",\"show_detail_button\":false,\"target_related_list_name\":\"target_related_list_keqPj__c\",\"field_num\":22,\"target_related_list_label\":\"列表页布局\",\"api_name\":\"field_E9C2c__c\",\"is_create_when_master_create\":true,\"_id\":\"662757e270ccc70001ba37d8\",\"is_index_field\":true,\"is_required_when_master_create\":true,\"help_text\":\"\",\"status\":\"new\"},\"data_own_department\":{\"describe_api_name\":\"object_p0o6I__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"where_type\":\"field\",\"type\":\"department\",\"is_required\":false,\"wheres\":[],\"optional_type\":\"department\",\"define_type\":\"package\",\"is_single\":true,\"label_r\":\"归属部门\",\"index_name\":\"data_owner_dept_id\",\"is_index\":true,\"is_active\":true,\"create_time\":1713162885242,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"归属部门\",\"is_need_convert\":false,\"api_name\":\"data_own_department\",\"_id\":\"661cca857bafa80001638a13\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"field_ww8Mc__c\":{\"describe_api_name\":\"object_p0o6I__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"where_type\":\"field\",\"type\":\"object_reference\",\"relation_outer_data_privilege\":\"not_related\",\"related_where_type\":\"\",\"is_required\":false,\"wheres\":[],\"enable_clone\":true,\"define_type\":\"custom\",\"input_mode\":\"\",\"is_single\":false,\"index_name\":\"s_6\",\"is_index\":true,\"is_active\":true,\"create_time\":1713430683387,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"查找关联\",\"target_api_name\":\"object_XS11X__c\",\"target_related_list_name\":\"target_related_list_nxqDk__c\",\"field_num\":17,\"target_related_list_label\":\"列表页布局\",\"action_on_target_delete\":\"set_null\",\"related_wheres\":[],\"api_name\":\"field_ww8Mc__c\",\"_id\":\"6620e09cbf17740001a67736\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"field_7Edoq__c\":{\"describe_api_name\":\"object_p0o6I__c\",\"default_is_expression\":true,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"enable_clone\":true,\"define_type\":\"custom\",\"input_mode\":\"\",\"is_single\":false,\"index_name\":\"t_4\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1713432672429,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"单行文本部门下\",\"field_num\":19,\"api_name\":\"field_7Edoq__c\",\"_id\":\"6620e861bf17740001a6a6e5\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"field_CC3of__c\":{\"describe_api_name\":\"object_p0o6I__c\",\"auto_adapt_places\":false,\"is_unique\":false,\"file_source\":[],\"type\":\"image\",\"is_required\":false,\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"a_3\",\"support_file_types\":[\"jpg\",\"gif\",\"jpeg\",\"png\"],\"is_index\":true,\"file_amount_limit\":1,\"is_active\":true,\"watermark\":[{\"type\":\"variable\",\"value\":\"current_user\"},{\"type\":\"variable\",\"value\":\"current_time\"},{\"type\":\"variable\",\"value\":\"current_address\"}],\"create_time\":1713166167910,\"is_encrypted\":false,\"label\":\"图片\",\"is_watermark\":false,\"field_num\":15,\"file_size_limit\":20971520,\"is_ocr_recognition\":false,\"api_name\":\"field_CC3of__c\",\"is_need_cdn\":false,\"_id\":\"661cd7587bafa800016707fc\",\"is_index_field\":false,\"identify_type\":\"\",\"help_text\":\"单个图片不得超过20M\",\"status\":\"new\",\"description\":\"\"},\"name\":{\"describe_api_name\":\"object_p0o6I__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"description\":\"name\",\"is_unique\":true,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":true,\"define_type\":\"system\",\"input_mode\":\"\",\"is_single\":false,\"label_r\":\"主属性\",\"index_name\":\"name\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1713162885369,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"主属性\",\"api_name\":\"name\",\"_id\":\"661cca857bafa80001638a10\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\"},\"field_b1Yk1__c\":{\"describe_api_name\":\"object_p0o6I__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"remove_mask_roles\":{},\"is_unique\":false,\"type\":\"number\",\"decimal_places\":2,\"default_to_zero\":true,\"is_required\":false,\"enable_clone\":true,\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"d_1\",\"max_length\":14,\"is_index\":true,\"is_active\":true,\"create_time\":1713166164186,\"is_encrypted\":false,\"display_style\":\"input\",\"step_value\":1,\"length\":12,\"default_value\":\"\",\"label\":\"数字\",\"field_num\":14,\"api_name\":\"field_b1Yk1__c\",\"_id\":\"661cd7557bafa800016707c2\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"mc_exchange_rate_version\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713162885253,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"description\":\"\",\"is_unique\":false,\"label\":\"汇率版本\",\"type\":\"text\",\"field_num\":8,\"is_required\":false,\"api_name\":\"mc_exchange_rate_version\",\"define_type\":\"package\",\"_id\":\"661cca857bafa80001638a22\",\"is_single\":false,\"label_r\":\"汇率版本\",\"is_index_field\":false,\"index_name\":\"t_1\",\"max_length\":256,\"help_text\":\"\",\"status\":\"new\"},\"_id\":{\"describe_api_name\":\"object_p0o6I__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1713162885248,\"pattern\":\"\",\"is_unique\":false,\"description\":\"_id\",\"label\":\"_id\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"_id\",\"define_type\":\"system\",\"index_name\":\"_id\",\"max_length\":200,\"status\":\"released\"}},\"release_version\":\"6.4\",\"actions\":{}}\n"

    @Shared
    String layoutStr = "{\"buttons\":[],\"components\":[{\"show_image\":\"\",\"api_name\":\"table_component\",\"ref_object_api_name\":\"object_LRH4n__c\",\"include_fields\":[{\"api_name\":\"name\",\"label\":\"主属性\",\"render_type\":\"text\",\"field_name\":\"name\",\"is_show_label\":true},{\"api_name\":\"created_by\",\"label\":\"创建人\",\"render_type\":\"employee\",\"field_name\":\"created_by\",\"is_show_label\":true},{\"api_name\":\"last_modified_time\",\"label\":\"最后修改时间\",\"render_type\":\"date_time\",\"field_name\":\"last_modified_time\",\"is_show_label\":true}],\"type\":\"table\",\"is_show_tag\":false,\"buttons\":[]}],\"last_modified_time\":1713261103504,\"is_deleted\":false,\"version\":1,\"create_time\":1713261103504,\"_id\":\"661e4a2f34831e0001405d42\",\"agent_type\":\"agent_type_mobile\",\"is_show_fieldname\":true,\"layout_description\":null,\"api_name\":\"list_layout_ut9NP__c\",\"what_api_name\":null,\"default_component\":null,\"created_by\":\"1000\",\"display_name\":\"移动端默认列表页\",\"is_default\":true,\"last_modified_by\":\"1000\",\"layout_type\":\"list\",\"package\":\"CRM\",\"ref_object_api_name\":\"object_LRH4n__c\",\"tenant_id\":\"74255\",\"ui_event_ids\":[],\"hidden_buttons\":[],\"hidden_components\":[],\"namespace\":null,\"enable_mobile_layout\":null,\"layout_structure\":{\"layout\":[{\"columns\":[{\"width\":\"100%\"}],\"components\":[[\"table_component\"]]}]}}\n"

    @Shared
    ILayout layout = null

    @Shared
    IObjectDescribe detailDescribe = null

    def initCondition() {
        User user = User.builder().tenantId("74255").userId("1000").build()
//        requestContext = RequestContext.builder().tenantId("74255").user(new Optional<User>(user)).build();

        controller = new StandardDescribeLayoutController()
        arg = new StandardDescribeLayoutController.Arg()
        arg.setApiname("AccountObj")
        arg.setData_id("**************")
        controller.arg = arg
        controllerContext = Mock()
        serviceFacade = Mock(ServiceFacade)
        maskFieldLogicService = Mock(MaskFieldLogicService)

        controller.controllerContext = controllerContext
        controller.serviceFacade = serviceFacade

        infraServiceFacade = Mock(InfraServiceFacade)
        springBeanHolder = Mock(SpringBeanHolder)
        functionPrivilegeService = Mock(FunctionPrivilegeService)
        infraServiceFacade.getSpringBeanHolder() >> springBeanHolder
        controller.setInfraServiceFacade(infraServiceFacade)

        RequestUtil.isMobileRequestBeforeVersion(_) >> false
        Map<String, List<IRecordTypeOption>> validRecordTypeListMap = getRecordOption()
        LayoutRuleInfo layoutRuleInfo = new LayoutRuleInfo()
        layoutRuleInfo.setLayoutApiName("layout_api_name")
        layoutRuleInfo.setStatus(0)

        serviceFacade.filterUnMatchRecordTypes(_, _, _, _) >> validRecordTypeListMap
        serviceFacade.findValidRecordTypeListMap(_, _) >> Maps.newHashMap()

        UdobjGrayUtil.GRAY_CONFIG = JacksonUtils.fromJson(configGryStr, UdobjGrayUtil.GrayConfig.class)

        ChangeOrderConfig.changeOrderOriginalDescribeGray = ["udobj": JacksonUtils.fromJson(changeConfigStr, ChangeOrderConfig.ChangeOrderConfigItem.class)];

        changeOrderRule = JacksonUtils.fromJson(changeRuleStr, MtChangeOrderRule.class)

        describe = ObjectDescribeExt.of(JacksonUtils.fromJson(describeStr, Map.class))

        detailDescribe = ObjectDescribeExt.of(JacksonUtils.fromJson(detailDescribeStr, Map.class))

        layout = LayoutExt.of(JacksonUtils.fromJson(layoutStr, Map.class))
    }

    def setup() {
        initCondition()
    }

    @Shared
    String layoutParam = ""

    @Shared
    String configGryStr = "{\"functions\":[{\"functionName\":\"gray_import_add_action\",\"whiteRule\":[{\"keys\":[\"udobj\",\"SalesOrderObj\",\"DeliveryNoteObj\",\"SalesOrderProductObj\",\"ReturnedGoodsInvoiceObj\",\"GoodsReceivedNoteObj\",\"OutboundDeliveryNoteObj\",\"StockCheckNoteObj\",\"RequisitionNoteObj\",\"PurchaseOrderObj\",\"PurchaseReturnNoteObj\"],\"ei\":\"white:*\"}]},{\"functionName\":\"follow_gray\",\"whiteRule\":[{\"keys\":[\"udobj\",\"AccountObj\",\"LeadsObj\",\"NewOpportunityObj\"],\"ei\":\"white:*\"}]},{\"functionName\":\"batch_calculate_result_check_modify\",\"blackRule\":{\"keys\":[],\"ei\":\"white:*\"},\"whiteRule\":[{\"keys\":[\"udobj\"],\"ei\":\"white:*\"}]},{\"functionName\":\"describe_layout_check_func_privilege\",\"blackRule\":{\"keys\":[],\"ei\":\"white:*\"}},{\"functionName\":\"select_data_gray_ei\",\"whiteRule\":[{\"keys\":[\"udobj\",\"TPMActivityObj\",\"TPMActivityProofObj\",\"TPMActivityProofAuditObj\",\"TPMDealerActivityCostObj\",\"SupplyStoreObj\",\"TPMActivityAgreementObj\",\"AreaManageObj\",\"VisitRouteObj\",\"RouteCustomerObj\",\"CoveredStoresObj\",\"SalesOrderObj\",\"SalesOrderProductObj\",\"AccountObj\",\"AccountMainDataObj\",\"CheckinsObj\",\"SPUObj\",\"PaymentObj\",\"CustomerAccountObj\",\"CheckinsImgObj\",\"WechatFanObj\",\"ServiceRecordObj\",\"ContractObj\",\"InvoiceApplicationObj\",\"CasesObj\",\"QuoteLinesObj\",\"DeviceObj\",\"PrepayDetailObj\",\"PaymentPlanObj\",\"HighSeasObj\",\"DeliveryNoteProductObj\",\"StockDetailsObj\",\"DeliveryNoteObj\",\"StockObj\",\"OutboundDeliveryNoteObj\",\"GoodsReceivedNoteObj\",\"GoodsReceivedNoteProductObj\",\"LeadsObj\",\"ContactObj\",\"NewOpportunityObj\",\"ProductObj\",\"OpportunityObj\",\"SpecialSupplyObj\",\"ServiceKnowledgeObj\",\"TPMStoreWriteOffObj\",\"QuoteObj\",\"SerialNumberObj\",\"PurchaseOrderProductObj\",\"AccessoryConsumeObj\",\"AccessoryConsumeProductObj\",\"AccessoryExchangeDetailObj\",\"AccessoryExchangeObj\",\"AccountTransactionFlowObj\",\"ActiveRecordObj\",\"AppraiseObj\",\"AppraiseObj\",\"ApprovalInstanceObj\",\"ApprovalTaskObj\",\"BatchObj\",\"BatchStockObj\",\"BomAttributeConstraintLinesObj\",\"BomAttributeConstraintObj\",\"BomCoreObj\",\"BOMObj\",\"BpmTask\",\"CasesAccessoryUseInfoObj\",\"CasesCheckGroupItemObj\",\"CasesCheckGroupObj\",\"CostAdjustmentNoteObj\",\"CostAdjustmentNoteProductObj\",\"CustomerServiceSessionObj\",\"DealerReturnApplicationObj\",\"DealerReturnProduct\",\"DeviceComponentsObj\",\"DeviceObj\",\"EmployeeWarehouseAdjustmentNoteObj\",\"EmployeeWarehouseDetailObj\",\"EmployeeWarehouseInOutRecordObj\",\"EmployeeWarehouseObj\",\"ExchangeGoodsNoteObj\",\"ExchangeGoodsNoteProductObj\",\"ExchangeReturnNoteObj\",\"ExchangeReturnNoteProductObj\",\"IndividualStockTransactionsObj\",\"NewOpportunityLinesObj\",\"OutboundDeliveryNoteProductObj\",\"PriceBookProductObj\",\"ProductGroupObj\",\"PurchaseOrderObj\",\"PurchaseReturnNoteObj\",\"PurchaseReturnNoteProductObj\",\"QuoteObj\",\"ReceiveMaterialBillObj\",\"ReceiveMaterialBillProductObj\",\"RefundMaterialBillObj\",\"RefundMaterialBillProductObj\",\"RequisitionNoteObj\",\"RequisitionNoteProductObj\",\"ReturnedGoodsInvoiceObj\",\"ReturnedGoodsInvoiceProductObj\",\"SerialNumberObj\",\"ServiceKnowledgeObj\",\"ServiceRecordObj\",\"ServiceReportObj\",\"ServiceRequestObj\",\"ShelfReportDetailObj\",\"ShelfReportObj\",\"SLAResultObj\",\"SOPActionInstanceObj\",\"SOPInstanceObj\",\"SOPProcedureInstanceObj\",\"SparePartsApplicationDetailObj\",\"SparePartsApplicationObj\",\"SparePartsConsumptionDetailObj\",\"SparePartsConsumptionObj\",\"SparePartsDeliveryDetailObj\",\"SparePartsDeliveryObj\",\"SparePartsMaintenancePlanObj\",\"SparePartsMaintenanceTaskDetailObj\",\"SparePartsMaintenanceTaskObj\",\"SparePartsReturnDetailObj\",\"SparePartsReturnObj\",\"StockCheckNoteObj\",\"StockCheckNoteProductObj\",\"SupplierObj\",\"WarehouseObj\",\"WebImVisitorObj\"],\"ei\":\"white:*\"}]},{\"functionName\":\"optimizeDetailObjectCalculateInBatchCalculate\",\"remark\":\"batchCalculate接口从对象查找关联对象统计字段计算优化\",\"whiteRule\":[{\"keys\":[\"udobj\"],\"ei\":\"white:74255|78057\"},{\"keys\":[\"TPMDealerActivityCostObj\"],\"ei\":\"white:*\"}]},{\"functionName\":\"lock_by_series_id_in_calculate_and_ui_event_and_add_edit_request\",\"remark\":\"batchCalculate、TriggerEvent和Add/Edit接口根据seriesId加锁，防止并发调用\",\"blackRule\":{\"keys\":[\"AccountObj\"],\"ei\":\"white:*\"},\"whiteRule\":[]}]}\n"


    @Shared
    String addLayoutResult = "{\"_id\":\"5dae7e22a5083da25948d78a\",\"api_name\":\"layout_kWsvn__c\",\"buttons\":[{\"action\":\"Add\",\"action_type\":\"default\",\"api_name\":\"Add_button_default\",\"label\":\"新建\"},{\"action\":\"Import\",\"action_type\":\"default\",\"api_name\":\"Import_button_default\",\"label\":\"导入\"},{\"action\":\"Export\",\"action_type\":\"default\",\"api_name\":\"Export_button_default\",\"label\":\"导出\"},{\"action\":\"IntelligentForm\",\"action_type\":\"default\",\"api_name\":\"IntelligentForm_button_default\",\"label\":\"智能表单\"},{\"action\":\"ExportFile\",\"action_type\":\"default\",\"api_name\":\"ExportFile_button_default\",\"label\":\"导出图片/附件\"}],\"components\":[{\"api_name\":\"form_component\",\"buttons\":[],\"field_section\":[{\"api_name\":\"base_field_section__c\",\"column\":2,\"form_fields\":[{\"field_name\":\"name\",\"is_readonly\":false,\"is_required\":true,\"render_type\":\"text\"},{\"field_name\":\"owner\",\"is_readonly\":false,\"is_required\":true,\"render_type\":\"employee\"},{\"field_name\":\"data_own_department\",\"is_readonly\":true,\"is_required\":false,\"render_type\":\"department\"},{\"field_name\":\"field_b42Y8__c\",\"is_readonly\":false,\"is_required\":false,\"render_type\":\"long_text\"}],\"header\":\"基本信息\",\"is_show\":true,\"tab_index\":\"ltr\"}],\"header\":\"详细信息\",\"is_hidden\":false,\"order\":2,\"type\":\"form\"}],\"config\":{\"edit\":1,\"remove\":0,\"create_time\":1571716642494,\"created_by\":\"1000\",\"default_component\":\"form_component\",\"display_name\":\"默认布局\",\"events\":[],\"is_default\":true,\"is_deleted\":false,\"last_modified_by\":\"1000\",\"last_modified_time\":1571716654549,\"layout_description\":\"\",\"layout_type\":\"detail\",\"package\":\"CRM\",\"ref_object_api_name\":\"object_170EW__c\",\"tenant_id\":\"78057\",\"top_info\":{\"api_name\":\"top_info\",\"buttons\":[],\"field_section\":[{\"api_name\":\"detail\",\"form_fields\":[{\"field_name\":\"owner\",\"render_type\":\"employee\"},{\"field_name\":\"owner_department\",\"render_type\":\"text\"},{\"field_name\":\"last_modified_time\",\"render_type\":\"date_time\"},{\"field_name\":\"record_type\",\"render_type\":\"record_type\"}]}],\"header\":\"顶部信息\",\"type\":\"simple\"},\"version\":3}}\n"

    @Shared
    String editLayoutResult = "{\"_id\":\"5dae7e22a5083da25948d78a\",\"api_name\":\"layout_kWsvn__c\",\"buttons\":[{\"action\":\"Add\",\"action_type\":\"default\",\"api_name\":\"Add_button_default\",\"label\":\"新建\"},{\"action\":\"Import\",\"action_type\":\"default\",\"api_name\":\"Import_button_default\",\"label\":\"导入\"},{\"action\":\"Export\",\"action_type\":\"default\",\"api_name\":\"Export_button_default\",\"label\":\"导出\"},{\"action\":\"IntelligentForm\",\"action_type\":\"default\",\"api_name\":\"IntelligentForm_button_default\",\"label\":\"智能表单\"},{\"action\":\"ExportFile\",\"action_type\":\"default\",\"api_name\":\"ExportFile_button_default\",\"label\":\"导出图片/附件\"}],\"components\":[{\"api_name\":\"form_component\",\"buttons\":[],\"field_section\":[{\"api_name\":\"base_field_section__c\",\"column\":2,\"form_fields\":[{\"field_name\":\"name\",\"is_readonly\":false,\"is_required\":true,\"render_type\":\"text\"},{\"field_name\":\"owner\",\"is_readonly\":false,\"is_required\":true,\"render_type\":\"employee\"},{\"field_name\":\"data_own_department\",\"is_readonly\":true,\"is_required\":false,\"render_type\":\"department\"},{\"field_name\":\"field_b42Y8__c\",\"is_readonly\":true,\"is_required\":false,\"render_type\":\"long_text\"}],\"header\":\"基本信息\",\"is_show\":true,\"tab_index\":\"ltr\"}],\"header\":\"详细信息\",\"is_hidden\":false,\"order\":2,\"type\":\"form\"}],\"config\":{\"edit\":1,\"remove\":0},\"create_time\":1571716642494,\"created_by\":\"1000\",\"default_component\":\"form_component\",\"display_name\":\"默认布局\",\"events\":[],\"is_default\":true,\"is_deleted\":false,\"last_modified_by\":\"1000\",\"last_modified_time\":1571716654549,\"layout_description\":\"\",\"layout_type\":\"detail\",\"package\":\"CRM\",\"ref_object_api_name\":\"object_170EW__c\",\"tenant_id\":\"78057\",\"top_info\":{\"api_name\":\"top_info\",\"buttons\":[],\"field_section\":[{\"api_name\":\"detail\",\"form_fields\":[{\"field_name\":\"owner\",\"render_type\":\"employee\"},{\"field_name\":\"owner_department\",\"render_type\":\"text\"},{\"field_name\":\"last_modified_time\",\"render_type\":\"date_time\"},{\"field_name\":\"record_type\",\"render_type\":\"record_type\"}]}],\"header\":\"顶部信息\",\"type\":\"simple\"},\"version\":3}"


    def "before test"() {
        given:
        when:
        controllerContext.getUser() >> new User("74255", "1000")
        serviceFacade.findObject(_, _) >> new ObjectDescribe()
        controller.before(arg)
        then:
        noExceptionThrown()
    }

    def "doService test"() {
        when:
        serviceFacade.findObjectData(_, _, _) >> new ObjectData()
        serviceFacade.findObject(_, _) >> new ObjectDescribe()
        serviceFacade.findObjectLayoutWithTypeIncludeAllComponent(_, _, _, _, _) >> new Layout()
        controllerContext.getUser() >> new User("74255", "1000")
        infraServiceFacade.findValidLayoutRuleByLayout(_, _, _) >> [new LayoutRuleInfo()]
        serviceFacade.findDescribeExtraByRenderType(_, _, _, _, _, _, _) >> DescribeExtra.of("AccountObj", [:])
        controller.doService(arg)
        then:
        noExceptionThrown()
    }

    def "doService flowLayout"() {
        given:
        layout.setNamespace("flow")
        when:
        serviceFacade.findObjectData(_, _, _) >> new ObjectData()
        serviceFacade.findObject(_, _) >> new ObjectDescribe()
        serviceFacade.findObjectLayoutWithTypeIncludeAllComponent(_, _, _, _, _) >> layout
        controllerContext.getUser() >> new User("74255", "1000")
        infraServiceFacade.findValidLayoutRuleByLayout(_, _, _) >> [new LayoutRuleInfo()]
        serviceFacade.findDescribeExtraByRenderType(_, _, _, _, _, _, _) >> DescribeExtra.of("AccountObj", [:])
        controller.doService(arg)
        then:
        noExceptionThrown()
    }


    def "doService editLayout"() {
        given:
        layout.setLayoutType("edit")
        when:
        serviceFacade.findObjectData(_, _, _) >> new ObjectData()
        serviceFacade.findObject(_, _) >> new ObjectDescribe()
        serviceFacade.findObjectLayoutWithTypeIncludeAllComponent(_, _, _, _, _) >> layout
        controllerContext.getUser() >> new User("74255", "1000")
        infraServiceFacade.findValidLayoutRuleByLayout(_, _, _) >> [new LayoutRuleInfo()]
        serviceFacade.findDescribeExtraByRenderType(_, _, _, _, _, _, _) >> DescribeExtra.of("AccountObj", [:])
        infraServiceFacade.isGrayFieldAlign(_, _) >> true
        controller.doService(arg)
        then:
        noExceptionThrown()
    }

    def "doService processMaskField"() {
        given:
        layout.setLayoutType("edit")
        arg.setLayout_type("edit")
        when:
        serviceFacade.findObjectData(_, _, _) >> new ObjectData()
        serviceFacade.findObject(_, _) >> describe
        serviceFacade.findObjectLayoutWithTypeIncludeAllComponent(_, _, _, _, _) >> layout
        controllerContext.getUser() >> new User("74255", "1000")
        infraServiceFacade.findValidLayoutRuleByLayout(_, _, _) >> [new LayoutRuleInfo()]
        serviceFacade.findDescribeExtraByRenderType(_, _, _, _, _, _, _) >> DescribeExtra.of("AccountObj", [:])
        infraServiceFacade.isGrayFieldAlign(_, _) >> true
        controller.doService(arg)
        then:
        noExceptionThrown()
    }

    def "doService calculateExpression"() {
        given:
        arg.setLayout_type("add")
        when:
        serviceFacade.findObjectData(_, _, _) >> new ObjectData()
        serviceFacade.findObject(_, _) >> new ObjectDescribe()
        serviceFacade.findObjectLayoutWithTypeIncludeAllComponent(_, _, _, _, _) >> layout
        controllerContext.getUser() >> new User("74255", "1000")
        infraServiceFacade.findValidLayoutRuleByLayout(_, _, _) >> [new LayoutRuleInfo()]
        serviceFacade.calculateExpressionForCreateData(_, _, _) >> new ObjectData()
        serviceFacade.findDescribeExtraByRenderType(_, _, _, _, _, _, _) >> DescribeExtra.of("AccountObj", [:])
        controller.doService(arg)
        then:
        noExceptionThrown()
    }

    def "doservice initChangeaOrderRule"() {
        given:
        arg.setLayout_type(LayoutTypes.EDIT)
        arg.setActionType("change_order")
        arg.setInclude_detail_describe(true)
        def info = new LayoutRuleInfo()
        info.setStatus(1)
        when:
        controllerContext.getObjectApiName() >> "sjassic__c"
        controllerContext.getTenantId() >> "74255"
        serviceFacade.findObjectData(_, _, _) >> new ObjectData()
        serviceFacade.findObject(_, _) >> describe
        serviceFacade.findDetailDescribes(_, _) >> [detailDescribe]
        serviceFacade.filterDescribesWithActionCode(_, _, _) >> [detailDescribe]
        serviceFacade.findObjectLayoutWithTypeIncludeAllComponent(_, _, _, _, _) >> new Layout()
        controllerContext.getUser() >> new User("74255", "1000")
        infraServiceFacade.findByDescribeApiName(_, _) >> [changeOrderRule]
        serviceFacade.findRecordTypes(_, _) >> ["object_p0o6I__c": [new RecordTypeOption("默认业务类型", "default__c", "dsadss", true)]]
        serviceFacade.findMobileListLayoutByDescApis(_, _, _, _) >> ["object_p0o6I__c": ["default__c": layout]]
        serviceFacade.findObjectLayoutWithType(_, _, _, _, _, _) >> new Layout()
        serviceFacade.getUnauthorizedFields(_ as User, _ as String) >> []
        serviceFacade.getBean(_) >> maskFieldLogicService
        infraServiceFacade.findLayoutRuleByDescribe(_, _) >> [info]
        infraServiceFacade.findValidLayoutRuleByLayout(_, _, _) >> [info]
        serviceFacade.findDescribeExtraByRenderType(_, _, _, _, _, _, _) >> DescribeExtra.of("AccountObj", [:])

        infraServiceFacade.queryConfigDataIncludeDefault(_, _, _, _, _) >> ["default__c": new ConfigPojo()]
        controller.doService(arg)

        then:
        noExceptionThrown()
    }


    def getRecordOption() {
        Map<String, List<IRecordTypeOption>> validRecordTypeListMap = Maps.newHashMap()
        IRecordTypeOption option = new RecordTypeOption()
        option.setApiName("record_1");
        validRecordTypeListMap.put("detail_api_name", Lists.newArrayList(option))
        validRecordTypeListMap
    }


}