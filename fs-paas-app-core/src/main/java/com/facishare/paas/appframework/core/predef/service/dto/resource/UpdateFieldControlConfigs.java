package com.facishare.paas.appframework.core.predef.service.dto.resource;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.mtresource.model.FieldControlInfoHelper;
import com.facishare.paas.appframework.metadata.repository.model.MtResource;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * Created by z<PERSON><PERSON>ju on 2024/3/23
 */
public interface UpdateFieldControlConfigs {

    @Data
    @Slf4j
    class Arg {
        private String describeApiName;
        private List<FieldControlInfo> fieldControlInfos;

        public void validate() {
            if (Strings.isNullOrEmpty(describeApiName)) {
                throw new ValidateException("describeApiName cannot be empty");
            }
        }


        public FieldControlInfoHelper toFieldControlInfoHelper() {
            validate();
            FieldControlInfoHelper.FieldControlInfoHelperItem strictControl = FieldControlInfoHelper.FieldControlInfoHelperItem.create(MtResource.SOURCE_VALUE_STRICT);
            FieldControlInfoHelper.FieldControlInfoHelperItem weakControl = FieldControlInfoHelper.FieldControlInfoHelperItem.create(MtResource.SOURCE_VALUE_WEAK);
            FieldControlInfoHelper.FieldControlInfoHelperItem deleteControl = FieldControlInfoHelper.FieldControlInfoHelperItem.create(null);
            if (CollectionUtils.empty(fieldControlInfos)) {
                return new FieldControlInfoHelper(describeApiName, strictControl, weakControl, deleteControl);
            }
            for (FieldControlInfo fieldControlInfo : fieldControlInfos) {
                String controlType = fieldControlInfo.getControlType();
                String fieldName = fieldControlInfo.getFieldName();
                if (Strings.isNullOrEmpty(fieldName)) {
                    log.warn("fieldName is empty!, describeApiName:{}, controlType:{}", describeApiName, controlType);
                    continue;
                }
                if (MtResource.SOURCE_VALUE_STRICT.equals(controlType)) {
                    strictControl.appendFieldName(fieldName);
                } else if (MtResource.SOURCE_VALUE_WEAK.equals(controlType)) {
                    weakControl.appendFieldName(fieldName);
                } else if (Strings.isNullOrEmpty(controlType)) {
                    deleteControl.appendFieldName(fieldName);
                }
            }
            return new FieldControlInfoHelper(describeApiName, strictControl, weakControl, deleteControl);
        }
    }

    class Result {

    }

    @Data
    class FieldControlInfo {
        private String fieldName;
        private String controlType;
    }

}
