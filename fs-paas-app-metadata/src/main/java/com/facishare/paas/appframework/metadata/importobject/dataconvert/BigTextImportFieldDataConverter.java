package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.RichTextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BigTextImportFieldDataConverter extends BaseImportFieldDataConverter {

    @Override
    public List<String> getSupportedFieldTypes() {
        return Lists.newArrayList(IFieldType.BIG_TEXT);
    }

    @Override
    public ConvertResult convertFieldData(IObjectData objectData, IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe, User user) {
        String bigTextValue = getStringValue(objectData, fieldDescribe.getApiName());
        if (StringUtils.isBlank(bigTextValue)) {
            objectData.set(RichTextExt.getRichTextAbstractName(fieldDescribe.getApiName()), null);
            return ConvertResult.buildSuccess(null);
        }
        objectData.set(RichTextExt.getRichTextAbstractName(fieldDescribe.getApiName()), StringUtils.substring(bigTextValue, 0, 2000));
        return ConvertResult.buildSuccess(bigTextValue);
    }
}