package com.facishare.paas.appframework.privilege.dto;

import lombok.Builder;
import lombok.Data;

public interface DataPrivilegeCalcProgress {

    @Builder
    @Data
    class Arg {
        String tenantId;
    }

    @Builder
    @Data
    class Result {
        DataPrivilegeCalcProgress.Body body;
        Integer code;
        String message;
    }

    @Data
    class Body {
        String tenantId;
        Integer total;
        Integer current;
        Integer percent;
        Boolean done;
    }

}
