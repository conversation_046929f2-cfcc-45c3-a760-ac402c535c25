package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectMappingService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Created by liwei on 2019/5/16
 */
public class StandardObjectMappingAction extends PreDefineAction<StandardObjectMappingAction.Arg, StandardObjectMappingAction.Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result doAct(Arg arg) {
        User user = actionContext.getUser();
        //获取映射规则
        List<IObjectMappingRuleInfo> rules = infraServiceFacade.findByApiName(user, arg.getRuleApiName());
        if (CollectionUtils.empty(rules)) {
            throw new ValidateException(I18N.text(I18NKey.MAPPING_RULE_DISABLED_OR_DELETED));
        }

        IObjectData objectData = serviceFacade.findObjectData(user, arg.getSourceId(), arg.getSourceApiName());
        //获取从对象数据
        Map<String, List<IObjectData>> details = Maps.newHashMap();
        List<IObjectDescribe> detailDescribes = serviceFacade.findDetailDescribes(user.getTenantId(), arg.getSourceApiName());
        detailDescribes.forEach(detail -> {
            Optional<MasterDetailFieldDescribe> masterField = ObjectDescribeExt.of(detail).getMasterDetailFieldDescribe();
            masterField.ifPresent(m -> {
                if (Objects.equals(m.getIsCreateWhenMasterCreate(), Boolean.TRUE)) {
                    List<IObjectData> detailObjectDataList = findDetailDataList(user, objectData, detail);
                    details.put(detail.getApiName(), detailObjectDataList);
                }
            });
        });
        Result result = new Result();
        result.setSuccess(true);
        if (CollectionUtils.notEmpty(arg.getDetailPlus()) && CollectionUtils.notEmpty(details)) {
            arg.getDetailPlus().forEach((apiName, values) -> {
                if (CollectionUtils.notEmpty(values) && CollectionUtils.notEmpty(details.get(apiName))
                        && details.get(apiName).size() > values.size()) {
                    result.setSuccess(false);
                    result.setMessage("detailApiName:" + apiName + " detailPlus is not match of detailData");
                }
            });
        }
        if (!result.isSuccess()) {
            return result;
        }
        //构建arg
        ObjectMappingService.MappingDataArg mappingDataArg = new ObjectMappingService.MappingDataArg();
        mappingDataArg.setDetails(details);
        mappingDataArg.setRuleApiName(arg.getRuleApiName());
        mappingDataArg.setObjectData(objectData);

        ObjectMappingService.MappingDataResult mappingDataResult = infraServiceFacade.mappingData(user, mappingDataArg);
        IObjectData mappingObjectData = mappingDataResult.getObjectData();
        Map<String, List<IObjectData>> mappingDetails = mappingDataResult.getDetails();

        if (mappingObjectData != null && CollectionUtils.notEmpty(arg.getPlus())) {
            mergeData(mappingObjectData, arg.getPlus());
        }
        if (CollectionUtils.notEmpty(mappingDetails) && CollectionUtils.notEmpty(arg.getDetailPlus())) {
            for (Map.Entry<String, List<IObjectData>> entry : mappingDetails.entrySet()) {
                String apiName = entry.getKey();
                List<IObjectData> dataList = entry.getValue();
                mergeDataList(dataList, arg.getDetailPlus().get(apiName));
            }
        }

        BaseObjectSaveAction.Result saveResult = doSaveData(mappingObjectData, mappingDetails);
        result.setObjectData(saveResult.getObjectData());
        result.setDetails(saveResult.getDetails());
        return result;
    }

    private List<IObjectData> findDetailDataList(User user, IObjectData masterObjectData, IObjectDescribe detailDescribe) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OBJECT_MAPPING_FIND_DETAIL_DATA_ORDER_BY_GRAY, user.getTenantId())) {
            return serviceFacade.findDetailObjectDataList(detailDescribe, masterObjectData, user);
        }
        SearchTemplateQuery query = serviceFacade.buildDetailSearchTemplateQuery(user, ObjectDescribeExt.of(detailDescribe), masterObjectData);
        return serviceFacade.findBySearchQuery(user, detailDescribe.getApiName(), query)
                .getData();
    }

    @Override
    protected StandardObjectMappingAction.Result after(StandardObjectMappingAction.Arg arg, StandardObjectMappingAction.Result result) {
        return super.after(arg, result);
    }

    private BaseObjectSaveAction.Result doSaveData(IObjectData objectData, Map<String, List<IObjectData>> mappingDetails) {
        if (Objects.isNull(objectData)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(ObjectDataDocument.of(objectData));
        arg.setDetails(ObjectDataDocument.ofMap(mappingDetails));
        String describeApiName = objectData.getDescribeApiName();
        ActionContext context = new ActionContext(actionContext.getRequestContext(), describeApiName, "Add");
        if (!Objects.equals(describeApiName, actionContext.getObjectApiName())
                && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.TRIGGER_REMOTE_ACTION_GRAY_OBJECT, describeApiName)) {
            return serviceFacade.triggerRemoteAction(context, arg, BaseObjectSaveAction.Result.class);
        }
        return serviceFacade.triggerAction(context, arg, BaseObjectSaveAction.Result.class);
    }

    private void mergeDataList(List<IObjectData> dataList, List<Map<String, Object>> valueList) {
        if (CollectionUtils.empty(dataList) || CollectionUtils.empty(valueList)) {
            return;
        }
        for (int i = 0; i < dataList.size() && i < valueList.size(); i++) {
            mergeData(dataList.get(i), valueList.get(i));
        }
    }

    private void mergeData(IObjectData objectData, Map<String, Object> values) {
        if (CollectionUtils.empty(values) || Objects.isNull(objectData)) {
            return;
        }
        values.forEach((fieldName, value) -> objectData.set(fieldName, value));
    }

    @Data
    public static class Arg {
        private String sourceApiName;
        private String sourceId;
        private String ruleApiName;
        private Map<String, Object> plus;
        private Map<String, List<Map<String, Object>>> detailPlus;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private ObjectDataDocument objectData;
        private Map<String, List<ObjectDataDocument>> details;
        private boolean success;
        private String message;
    }
}
