package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProvider;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProviderDefinition.GetCustomInitRoleActionCodes;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProviderDefinition.GetSupportedActionCodes;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProviderDefinition.IsAdminInitByDefault;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProviderManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@ServiceModule("FunctionPrivilegeProvider")
public class ObjectFunctionPrivilegeProviderService {

    @Autowired
    private FunctionPrivilegeProviderManager providerManager;

    @ServiceMethod("getSupportedActionCodes")
    public GetSupportedActionCodes.Result getSupportedActionCodes(GetSupportedActionCodes.Arg arg, ServiceContext context) {
        FunctionPrivilegeProvider provider = providerManager.getLocalProvider(arg.getDescribeApiName());
        return GetSupportedActionCodes.Result.builder()
                .supportedActionCodes(provider.getSupportedActionCodes())
                .build();
    }

    @ServiceMethod("getCustomInitRoleActionCodes")
    public GetCustomInitRoleActionCodes.Result getCustomInitRoleActionCodes(GetCustomInitRoleActionCodes.Arg arg, ServiceContext context) {
        FunctionPrivilegeProvider provider = providerManager.getLocalProvider(arg.getDescribeApiName());
        return GetCustomInitRoleActionCodes.Result.builder()
                .customInitRoleActionCodes(provider.getCustomInitRoleActionCodes())
                .build();
    }

    @ServiceMethod("isAdminInitByDefault")
    public IsAdminInitByDefault.Result isAdminInitByDefault(IsAdminInitByDefault.Arg arg, ServiceContext context) {
        FunctionPrivilegeProvider provider = providerManager.getLocalProvider(arg.getDescribeApiName());
        return IsAdminInitByDefault.Result.builder()
                .isAdminInitByDefault(provider.isAdminInitByDefault())
                .build();
    }

}
