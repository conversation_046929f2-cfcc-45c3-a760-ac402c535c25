package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.*;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.userlogin.api.model.validatecode.BuildValidateCode;

import java.util.List;
import java.util.Set;

public interface PhoneNumberService {

    String CRM_ACTION = "CRM";
    String LOGIN_DYNAMIC_PASSWORD = "LoginDynamicPassword";

    List<QueryPhoneNumberInformation.Result> batchQueryPhoneNumberInfo(Set<String> mobileSet);

    QueryPhoneNumberInformation.Result queryPhoneNumberInfo(String mobile);

    QueryPhoneNumberInformation.Result queryPhoneNumberInfo(String mobile, boolean municipalityIgnoreCity);

    /**
     * 发送短信验证码
     * @deprecated Use {@link #sendSmsCode(String, User, String, String, String, String, String)} instead.
     * 
     * @param user        操作人
     * @param areaCode    区域号中国大陆为 +86
     * @param ip          clientIP
     * @param mobile      移动手机号码
     * @param captchaCode 选填,图形验证码
     * @param captchaId   选填,标识图形验证码的id
     * @return 最终结果
     */
    @Deprecated
    String queryVerificationCode(User user, String ea, String areaCode, String ip, String mobile, String captchaCode, String captchaId);

    /**
     * 校验短信验证码
     * @deprecated Use {@link #verifySmsCode(User, String, String, String)}
     * 
     * @param phone   手机
     * @param smsCode 短信验证码
     * @return
     */
    @Deprecated
    String checkVerificationCode(String areaCode, String phone, String smsCode);

    /**
     * 生成图形验证码
     * @deprecated <p> Use {@link AppCaptchaService#createCaptchaCode(User)} instead.
     */
    @Deprecated
    GetImageCode.Result refreshCaptcha();

    /**
     * 查询企业短信服务（fs-marketing）开通状态
     *
     * @param tenantId ei
     * @return 是否开通
     */
    boolean checkSmsStatus(String tenantId);

    /**
     * @deprecated Use {@link #sendSmsCode(String, User, String, String, String, String, String)} instead.
     * 上面的接口直接提供给用户，扣减企业配额的
     * 这个发送短信验证码目前提供给内部使用，不扣减短信配额
     *
     * @return
     */
    @Deprecated
    String sendSMValidateCode(User user, String ea, String areaCode, String ip, String mobile, String captchaCode, String captchaId);

    /**
     * 校验短信验证码，不扣配额
     * @deprecated Use {@link #verifySmsCode(User, String, String, String)}
     *
     * @param areaCode
     * @param phone
     * @param smsCode
     * @return
     */
    @Deprecated
    String verifyValidateCode(String areaCode, String phone, String smsCode);

    /**
     * 查询图片验证码
     * @deprecated <p> Use {@link AppCaptchaService#createCaptchaCode(User)} instead.
     *
     * @return
     */
    @Deprecated
    GetImageCode.Result getImageCode();

    /**
     * 生成短信验证码
     * @param user
     * @param mobile
     * @param ip
     * @param captchaCode
     * @param captchaId
     * @param expireTime 过期时间 秒
     * @return
     */
    GenerateVerificationCode.Result generateVerificationCode(User user, String mobile, String ip, String captchaCode, String captchaId, int expireTime);

    void sendCodeByAPL(User user, String functionApiName, String methodName, GenerateVerificationCode.SendVerificationCodeArg arg);


    /**
     * 验证短信验证码
     *
     * @param user   登录用户
     * @param code   短信验证码
     * @param mobile 手机号码
     * @return 验证结果
     */
    @Deprecated
    String verifyCode(User user, String code, String mobile);


    /**
     * 生成短信验证码
     * <p> 通过 {@link #verifySmsCode(User, String, String, String)} 验证验证码
     *
     * @param user 操作人
     * @param ip 操作客户端 ip
     * @param areaCode 区号
     * @param mobile 移动手机号码
     * @param captchaCode 根据图形提交的图形验证码
     * @param captchaId 图形验证码ID
     * @param expireTime 有效期
     * @return 结果
     */
    BuildValidateCode.Result createSmsCode(User user, String ip, String areaCode, String mobile, String captchaCode, String captchaId, int expireTime);

    /**
     * 验证短信验证码
     * <p> 通过 {@link #createSmsCode(User, String, String, String, String, String, int)} 生成验证码
     *
     * @param user 验证人
     * @param areaCode 区号
     * @param mobile 移动手机号码
     * @param smsCode 短信验证码
     * @return 是否成功
     */
    String verifySmsCode(User user, String areaCode, String mobile, String smsCode);


    /**
     * 发送短信验证码
     * biz: CRM
     *
     * @param ea          企业账号
     * @param user        操作人
     * @param ip          操作客户端 IP 地址
     * @param areaCode    区码
     * @param mobile      移动手机号码
     * @param captchaCode 图形验证码
     * @param captchaId   图形验证码 ID
     * @return SendValidateCodeEnum
     */
    String sendSmsCode(String ea, User user, String ip, String areaCode, String mobile, String captchaCode, String captchaId);


}
