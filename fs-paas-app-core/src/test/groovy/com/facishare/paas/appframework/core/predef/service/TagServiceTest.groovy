package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.common.service.OrgService
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.DescribeLogicService
import com.facishare.paas.appframework.metadata.TagLogicService
import com.facishare.paas.appframework.metadata.dto.tag.TagGroupTag
import com.facishare.paas.metadata.api.describe.ISubTagDescribe
import com.facishare.paas.metadata.api.describe.ITagDescribe
import com.facishare.paas.metadata.impl.describe.SubTagDescribe
import com.facishare.paas.metadata.impl.describe.TagDescribe
import com.google.common.collect.Lists
import spock.lang.Specification

/**
 * <AUTHOR> @date 2020/2/26 7:39 下午
 *
 */
class TagServiceTest extends Specification {
    ServiceFacade serviceFacade
    TagService tagService;
    ServiceContext serviceContext
    TagLogicService tagLogicService = Mock(TagLogicService.class)
    DescribeLogicService describeLogicService = Mock(DescribeLogicService.class)
    OrgService orgService = Mock(OrgService.class)

    def setup() {
        serviceFacade = Mock(ServiceFacade)
        tagService = new TagService()
        tagService.tagLogicService = tagLogicService
        tagService.describeLogicService = describeLogicService
        tagService.orgService = orgService
        serviceContext = new ServiceContext(
                RequestContext.builder().user(new User("78057", "1000")).build(),
                "", "")
    }

    def "test find all tags"() {
        given: "准备查询参数"
        def arg = new com.facishare.paas.appframework.core.predef.service.dto.tag.ListAllTags.Arg()
        arg.describeApiName = "TestObject"
        arg.name = "test"

        and: "Mock服务调用"
        serviceFacade.findAllTags("TestObject", "78057", "test", null) >> buildTagDescribes()
        serviceFacade.findTagGroupsByIds("78057", _) >> buildTagGroups()

        when: "查找所有标签"
        def result = tagService.findAllTagsByDescribeApiName(arg, serviceContext)

        then: "应该返回正确结果"
        result != null
        1 * serviceFacade.findAllTags("TestObject", "78057", "test", null)
        1 * serviceFacade.findTagGroupsByIds("78057", _)
    }

    def "test find data tags"() {
        given: "准备查询参数"
        def arg = new com.facishare.paas.appframework.core.predef.service.dto.tag.FindDataTags.Arg()
        arg.describeApiName = "AccountObj"
        arg.dataId = "**********"

        and: "Mock服务调用"
        serviceFacade.findTagsByDataId("78057", "AccountObj", "**********") >> buildTagDescribes()
        serviceFacade.findTagGroupsByIds("78057", _) >> buildTagGroups()

        when: "查找数据标签"
        def result = tagService.findDataTags(arg, serviceContext)

        then: "应该返回正确结果"
        result != null
        1 * serviceFacade.findTagsByDataId("78057", "AccountObj", "**********")
        1 * serviceFacade.findTagGroupsByIds("78057", _)
    }

    def "test createTag with valid arguments"() {
        given: "准备创建参数"
        def arg = new com.facishare.paas.appframework.core.predef.service.dto.tag.CreateOrUpdateTag.Arg()
        arg.tagApiName = "test_tag"
        arg.tagName = "Test Tag"
        arg.groupId = "tag-group-1"

        and: "Mock服务调用"
        serviceFacade.createSubTag(_, _) >> buildTagDescribes()[0]

        when: "创建标签"
        def result = tagService.createTag(arg, serviceContext)

        then: "应该返回正确结果"
        result != null
        result.subTag != null
        1 * serviceFacade.createSubTag(_, _)
    }

    def "test deleteTag with valid arguments"() {
        given: "准备删除参数"
        def arg = new com.facishare.paas.appframework.core.predef.service.dto.tag.DeleteTag.Arg()
        arg.tagApiName = "test_tag"
        arg.groupId = "tag-group-1"

        when: "删除标签"
        def result = tagService.deleteTag(arg, serviceContext)

        then: "应该返回正确结果"
        result != null
        result.success == true
        1 * serviceFacade.deleteSubTag("78057", "tag-group-1", "test_tag", _)
    }

    def "test createTagGroup with valid arguments"() {
        given: "准备创建标签组参数"
//        def arg = new com.facishare.paas.appframework.core.predef.service.dto.tag.CreateOrUpdateTagGroup.Arg()
        arg.tagGroupApiName = "test_group"
        arg.tagGroupName = "Test Group"
        arg.describeApiName = "TestObject"

        and: "Mock服务调用"
        serviceFacade.createTagGroup(_, _) >> buildTagGroups()[0]

        when: "创建标签组"
        def result = tagService.createTagGroup(arg, serviceContext)

        then: "应该返回正确结果"
        result != null
        result.tagGroup != null
        1 * serviceFacade.createTagGroup(_, _)
    }

    def buildTagGroupTag() {
        TagGroupTag tag = new TagGroupTag()
        tag.setTagGroupName("default")
        tag.setTagNames(Lists.newArrayList("label1", "labor"))
        return Lists.newArrayList(tag)
    }

    def buildTagDescribes() {
        ISubTagDescribe tag = new SubTagDescribe()
        tag.setName("tag1")
        tag.setDescribeApiName("AccountObj")
        tag.setGrade(1)
        tag.setId("123")
        tag.setTagId("456")
        return Lists.newArrayList(tag)
    }

    def buildTagGroups() {
        ITagDescribe group = new TagDescribe()
        group.setId("456")
        group.setDescribeApiName("AccountObj")
        group.setType("默认分组")
        return Lists.newArrayList(group)
    }

}
