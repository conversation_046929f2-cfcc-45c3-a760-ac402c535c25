package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * create by z<PERSON><PERSON> on 2019/01/16
 */
public interface BatchUpdateEntityShareModel {
    @Data
    @EqualsAndHashCode(callSuper = true)
    class Arg extends BasePrivilegeArg {
        private List<EntitySharePojo> entityShares;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {
        private List<String> result;
    }
}
