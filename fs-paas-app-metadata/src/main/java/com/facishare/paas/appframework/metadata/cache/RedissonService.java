package com.facishare.paas.appframework.metadata.cache;

import com.facishare.paas.appframework.core.model.User;
import lombok.Data;
import org.redisson.api.RLock;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.concurrent.TimeUnit;
import java.util.function.UnaryOperator;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/12/10
 */
public interface RedissonService {

    RLock tryLock(long waitTime, long leaseTime, TimeUnit unit, String key);

    RLock tryLockWithErrorMsg(long waitTime, long leaseTime, TimeUnit unit, String key, String message);

    RLock tryLockWithErrorMsg(long waitTime, long leaseTime, TimeUnit unit, String key, String message, int errorCode);

    RLock tryLock(User user, String describeApiName, String key);

    RLock tryMultiLock(long waitTime, long leaseTime, TimeUnit unit, String... keys);

    /**
     * 获取公平锁
     *
     * @param waitTime
     * @param leaseTime
     * @param unit
     * @param key
     * @return
     */
    RLock tryFairLock(long waitTime, long leaseTime, TimeUnit unit, String key);

    boolean isLocked(String key);

    void unlock(RLock lock);

    boolean limiter(String key, TimeUnit unit, int max);

    <T> T incrementAndGet(String key, long timeToLive, TimeUnit timeUnit, UnaryOperator<T> incrementAndGet, T defaultValue);

    @Data
    class LimiterInfo {
        private static final long ONE_DAY = (long) (24 * 60 * 60) * 1000;

        private long createTime = System.currentTimeMillis();
        private int currentNumber = 1;

        public LimiterInfo incrementAndGet() {
            long today = LocalDateTime.of(LocalDate.now(), LocalTime.of(0, 0, 0))
                    .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            LimiterInfo limiterInfo = new LimiterInfo();
            if (createTime - today > 0 && createTime - today < ONE_DAY) {
                limiterInfo.setCreateTime(createTime);
                limiterInfo.setCurrentNumber(currentNumber + 1);
            }
            return limiterInfo;
        }
    }
}
