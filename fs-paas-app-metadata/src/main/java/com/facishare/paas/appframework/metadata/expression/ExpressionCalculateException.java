package com.facishare.paas.appframework.metadata.expression;

import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;

/**
 * 表达式计算异常
 * <p>
 * Created by <PERSON><PERSON>yi<PERSON><PERSON> on 2018/4/19.
 */
public class ExpressionCalculateException extends AppBusinessException {

    public ExpressionCalculateException(String message) {
        super(message, AppFrameworkErrorCode.EXPRESSION_ERROR);
    }
}
