{"basicFilterCondition": [{"fieldName": "is_deleted", "operator": "IN", "order": 2, "values": ["0"]}], "deleteOriginalData": false, "fieldDescribe": [{"fieldBizType": "TenantId", "fieldType": "String", "name": "tenant_id", "order": 1}, {"fieldBizType": "ApiName", "fieldType": "String", "name": "plugin_api_name", "order": 2}, {"fieldType": "String", "name": "define_type", "order": 3}, {"fieldType": "String", "name": "app_type", "order": 4}, {"fieldBizType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldBizValue": "{\"value\": \"false\"}", "fieldType": "Boolean", "name": "is_binding", "order": 2147483647}, {"fieldBizType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldBizValue": "{\"value\": \"false\"}", "fieldType": "Boolean", "name": "is_active", "order": 2147483647}, {"fieldType": "String", "name": "extra_info", "order": 2147483647}, {"fieldBizType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldBizValue": "{\"value\": \"{}\"}", "fieldType": "String", "name": "dev_info", "order": 2147483647}, {"fieldBizType": "IdGenerator", "fieldType": "String", "name": "id", "order": 2147483647}, {"fieldBizType": "Name", "fieldType": "String", "name": "name", "order": 2147483647}, {"fieldType": "String", "name": "icon", "order": 2147483647}, {"fieldBizType": "ApiName", "fieldType": "String", "name": "object_describe_api_name", "order": 2147483647}, {"fieldBizType": "UserId", "fieldType": "String", "name": "created_by", "order": 2147483647}, {"fieldBizType": "UserId", "fieldType": "String", "name": "last_modified_by", "order": 2147483647}, {"fieldBizType": "Time", "fieldType": "TimeStamp", "name": "create_time", "order": 2147483647}, {"fieldBizType": "Time", "fieldType": "TimeStamp", "name": "last_modified_time", "order": 2147483647}, {"fieldType": "Number", "name": "version", "order": 2147483647}, {"fieldType": "String", "name": "is_deleted", "order": 2147483647}], "inBoundCheck": {"carefulChecks": [], "checkTableDescriptors": [], "dependencyTableDescriptors": [], "unchangeableField": [], "uniqueIndices": []}, "logicDeleteFieldValue": {}, "order": 1, "principalIds": [308, 5396, 7618], "processorDescribe": {"inBoundTag": "PG", "outBoundTag": "PG", "selectHandlerType": {"configName": "dev_package_plugin", "handlerType": "PG"}}, "slaveTableDescribe": [{"filters": [], "foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "api_name", "operator": "IN", "refFieldName": "extra_info", "refReader": "JsonPath:$.function_api_name"}], "tableId": "paas_metadata_mt_udef_function"}, {"filters": [], "foreignKeyDescribe": [{"fieldName": "tenantId", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "apiName", "operator": "IN", "refFieldName": "extra_info", "refReader": "JsonPath:$.pwc_api_name"}], "tableId": "webpage-customer-mongo_ComponentEntity"}, {"filters": [{"fieldName": "app_type", "operator": "IN", "order": 1, "values": ["OnlineDoc"]}, {"fieldName": "define_type", "operator": "IN", "order": 2, "values": ["system_personal"]}], "foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "define_type", "operator": "IN", "refFieldName": "define_type"}, {"fieldName": "app_type", "operator": "IN", "refFieldName": "app_type"}, {"fieldName": "plugin_api_name", "operator": "IN", "refFieldName": "plugin_api_name"}, {"fieldName": "id", "operator": "IN", "refFieldName": "id"}], "tableId": "dev_onlinedoc_plugin_devinfo_field"}, {"filters": [{"fieldName": "app_type", "operator": "IN", "order": 1, "values": ["OnlineDoc"]}, {"fieldName": "define_type", "operator": "IN", "order": 2, "values": ["system"]}], "foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "define_type", "operator": "IN", "refFieldName": "define_type"}, {"fieldName": "app_type", "operator": "IN", "refFieldName": "app_type"}, {"fieldName": "plugin_api_name", "operator": "IN", "refFieldName": "plugin_api_name"}, {"fieldName": "id", "operator": "IN", "refFieldName": "id"}], "tableId": "dev_onlinedoc_plugin_devinfo_field_system"}], "tableId": "dev_onlinedoc_plugin_field", "tableName": "dev_package_plugin", "uniqueKeys": ["tenant_id", "plugin_api_name"]}