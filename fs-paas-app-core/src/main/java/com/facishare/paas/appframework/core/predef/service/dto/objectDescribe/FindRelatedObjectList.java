package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface FindRelatedObjectList {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("describeApiName")
        @SerializedName("describeApiName")
        String describeApiName;

        @JSONField(name = "M2")
        @JsonProperty("includeDetailList")
        @SerializedName("includeDetailList")
        private boolean includeDetailList;

        @JSONField(name = "M3")
        @JsonProperty("includeRefList")
        @SerializedName("includeRefList")
        private boolean includeRefList;

        @JsonProperty("includeRefManyList")
        @SerializedName("includeRefManyList")
        private boolean includeRefManyList;

        @JSONField(name = "M4")
        @JsonProperty("excludeInvalid")
        @SerializedName("excludeInvalid")
        private Boolean excludeInvalid;

        private boolean includeWhatList;

        private String renderType;

        private String sourceInfo;
        @JsonProperty("isConvertRule")
        @SerializedName("isConvertRule")
        private boolean isConvertRule;

        private String ruleApiName;

        private Boolean includeBigObject;
        private Boolean includeSocialObject;

        private boolean includeCurrentObject;

        private boolean simplyDescribe;

    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "M1")
        private List<ObjectDescribeDocument> detailDescribeList;

        @JSONField(name = "M2")
        private List<ObjectDescribeDocument> lookupDescribeList;

        private List<ObjectDescribeDocument> lookupManyDescribeList;

        private List<ObjectDescribeDocument> whatDescribeList;

        private ManageGroupDTO manageGroup;

        private ObjectDescribeDocument objectDescribe;
    }
}
