package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.treeview.TreeViewService;
import com.facishare.paas.appframework.metadata.treeview.TreeViewServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

public class StandardTreeViewPathController extends AbstractStandardController<StandardTreeViewPathController.Arg, StandardTreeViewPathController.Result> {
    protected ObjectDescribeExt objectDescribeExt;
    protected TreeViewService treeViewService;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        IObjectDescribe describe = findObject();
        objectDescribeExt = ObjectDescribeExt.of(describe);
        stopWatch.lap("findObject");
        treeViewService = serviceFacade.getBean(TreeViewServiceImpl.class);
    }
    @Override
    protected Result doService(Arg arg) {
        Result result = new Result();
        List<TreePath> treePathList = Lists.newArrayList();
        result.setTreePathList(treePathList);
        if (!objectDescribeExt.isSupportTreeViewObject()) {
            return result;
        }
        Optional<IFieldDescribe> fieldDescribe = objectDescribeExt.getActiveLookupFieldDescribes().stream()
                .filter(field -> FieldDescribeExt.of(field).isTreeViewSelfAssociatedField())
                .findFirst();
        if (!fieldDescribe.isPresent()) {
            return result;
        }
        String treeViewFieldApiName = ((ObjectReferenceFieldDescribe) fieldDescribe.get()).getTreeViewField();
        List<IObjectData> objectDataList = treeViewService.findTreeViewObjDataListByIds(controllerContext.getUser(), getArg().getDataIds(), controllerContext.getObjectApiName(), objectDescribeExt.getTreeViewObjectFieldsToReturn());
        stopWatch.lap("findObjectData");
        getTreeViewDataWholePath(treePathList, treeViewFieldApiName, objectDataList);
        stopWatch.lap("getTreeViewDataWholePath");
        return result;
    }

    private void getTreeViewDataWholePath(List<TreePath> treePathList, String treeViewFieldApiName, List<IObjectData> objectDataList) {
        CollectionUtils.nullToEmpty(objectDataList).forEach(data -> {
            String treeViewPath = data.get(treeViewFieldApiName, String.class);
            String label = getLabel(data) ;
            List<String> parentNames = Lists.newArrayList();
            if (StringUtils.isNotEmpty(treeViewPath)) {
                List<String> parentIds = Lists.newArrayList(treeViewPath.split("\\."));
                List<IObjectData> parentObjectDataList = treeViewService.findTreeViewObjDataListByIds(controllerContext.getUser(), parentIds,
                        controllerContext.getObjectApiName(), objectDescribeExt.getTreeViewObjectFieldsToReturn());
                parentObjectDataList.sort(Comparator.comparingInt(o -> parentIds.indexOf(o.getId())));
                parentObjectDataList.forEach(objectData -> parentNames.add(getLabel(objectData)));
            }
            parentNames.add(label);
            String wholePath = StringUtils.join(parentNames, "/");
            TreePath treePath = TreePath.builder()
                    .dataId(data.getId())
                    .wholePath(wholePath)
                    .build();
            treePathList.add(treePath);
        });
    }

    private String getLabel(IObjectData objectData) {
        String label = objectDescribeExt.isSupportDisplayName() ? objectData.getDisplayName() : objectData.getName();
        return Objects.nonNull(label) ? label : "";
    }

    private IObjectDescribe findObject() {
        IObjectDescribe objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), controllerContext.getObjectApiName());
        ObjectDescribeExt ret = ObjectDescribeExt.of(objectDescribe);
        //先在这个地方处理，可以到跟底层处理掉
        ret.removeFieldDescribe("extend_obj_data_id");
        return ret;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg {
        private List<String> dataIds;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private List<TreePath> treePathList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TreePath {
        private String dataId;
        private String wholePath;
    }
}
