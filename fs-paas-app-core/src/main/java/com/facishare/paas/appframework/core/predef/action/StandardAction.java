package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

/**
 * 对象基本操作
 * <p>
 * Created by liyiguang on 2017/6/20.
 */
public enum StandardAction {

    Delete("Delete"),
    Edit("Edit"),
    Add("Add"),
    BulkCreate("Add"),
    BulkEditByField("Edit"),
    BulkEditByRecord("Edit"),
    BulkDelete("Delete"),
    Invalid("Abolish"),
    BulkInvalid("Abolish"),
    AddTeamMember("EditTeamMember"),
    EditTeamMember("EditTeamMember"),
    DeleteTeamMember("EditTeamMember"),
    Lock("Lock"),
    <PERSON>lock("Unlock"),
    SignIn("SignIn"),
    SignOut("SignOut"),
    ExportVerify("Export"),
    Export("Export"),
    ExportExcelTemplateVerify("Export"),
    ExportExcelTemplate("Export"),
    ChangeOwner("ChangeOwner"),
    BulkAssociate("Relate"),
    BulkDisassociate("Relate"),
    BulkRecover("Recover"), //无权限检查
    FlowCompleted(), //无权限检查
    Clone("Add"),
    InsertImportVerify("Import"),
    InsertImportData("Import"),
    UpdateImportVerify("Import"),
    UpdateImportData("Import"),
    InsertImportTemplate(""),
    UpdateImportTemplate(""),
    //联合导入模板Action
    UnionInsertImportTemplate(""),
    FlowStartCallback(""),//无权限检查
    ChangePartner("ChangePartner"),
    ChangePartnerOwner("ChangePartnerOwner"),
    DeletePartner("DeletePartner"),
    ExportToLocal("Export"),
    // 导出图片附件
    ExportFileAttachment("Export", "PictureAnnexDownload"),
    ExportFileVerify("Export", "PictureAnnexDownload"),
    ObjectMapping("ObjectMapping"),
    Merge("Merge"),
    DuplicateSearch("DuplicateSearch"),
    SimpleDuplicateSearch("SimpleDuplicateSearch"),
    RelatedDuplicateSearch("RelatedDuplicateSearch"),
    RelatedSimpleDuplicateSearch("RelatedSimpleDuplicateSearch"),
    IncrementUpdate("Edit"),
    //增加联合导入Action
    UnionInsertImportVerify("Import"),
    UnionInsertImportData("Import"),
    // 触发UI事件
    TriggerEvent("TriggerEvent"),

    // 批量按钮
    AsyncBulkLock("Lock"),
    AsyncBulkUnlock("Unlock"),
    AsyncBulkAddTeamMember("EditTeamMember"),
    AsyncBulkChangeOwner("EditTeamMember"),
    AsyncBulkDeleteTeamMember("EditTeamMember"),
    AsyncBulkInvalid("Abolish"),
    AsyncBulkChangePartnerOwner("ChangePartnerOwner"),
    AsyncBulkDeletePartner("DeletePartner"),
    AsyncBulkChangePartner("ChangePartner"),

    BulkCustomButton(),
    AddOuterScene(),
    DeleteOuterScene(),
    EditOuterScene(),

    UIAction() {
        @Override
        public String toString() {
            return "UI";
        }
    },
    BulkUIAction() {
        @Override
        public String toString() {
            return "BulkUI";
        }
    },

    // 草稿
    AddDraft(),
    UpdateDraft(),
    DeleteDraft(),
    // excel打印
    ExcelPrint(),
    // word打印
    WordPrint(),

    // 标签
    BatchUpdateTag(),
    // 批量添加标签
    BulkHangTag(),

    // 支付回调
    PayComplete("PayComplete"),

    //优先级
    Priority("Priority"),

    // 新建的 UI 按钮
    AddUI("Add"),
    // 编辑 UI 按钮
    EditUI("Edit"),
    // 转换 UI 按钮
    TransferUI("Transfer"),

    //打印
    Print(),
    //Html 单个打印
    HtmlPrint(),
    //批量打印
    BulkPrint(),
    //更新法律基础
    UpdateGdpr("UpdateGdpr"),
    //批量更新法律基础
    AsyncBulkUpdateGdpr("UpdateGdpr"),
    //项目请求锁定数据
    GdprProjectRequestLock(),
    //项目请求解锁数据
    GdprProjectRequestUnLock(),
    //项目请求作废数据
    GdprProjectRequestInvalid(),

    AsyncBulkCancelEntry(),
    AsyncBulkEnterAccount(),
    CancelEntry(),
    EnterAccount(),

    AsyncBulkRecover(),
    // 变更单 编辑原单发起变更操作
    Change("Change"),
    // 变更单 编辑变更单发起变更操作
    ReChangeOrder("Edit"),
    // 变更单 生效操作
    Effective("Effective"),
    // 转换规则 转换和参照新建操作
    Convert("Transform", "ReferenceCreate"),
    ConvertSave("Transform", "ReferenceCreate"),
    RecordLog(),
    //根据打印模板导出
    ExportByPrintTemplateVerify(),
    ExportByPrintTemplate(),
    //关注
    Follow(),
    Unfollow(),
    AsyncBulkFollow(),
    AsyncBulkUnfollow(),
    DesignerCreateLayout(),
    DesignerUpdateLayout(),
    ImportDataAdd(),
    // 发邮件
    SendEmailUI()
    ;


    private final List<String> functionPrivilegeCodes;

    StandardAction(String... privilegeCodes) {
        this.functionPrivilegeCodes = Lists.newArrayList(privilegeCodes);
    }

    public List<String> getFunPrivilegeCodes() {
        return functionPrivilegeCodes;
    }

    public ActionClassInfo getDefaultActionClassInfo() {
        String className = STANDARD_ACTION_PACKAGE + "." + "Standard" + this + "Action";
        return new ActionClassInfo(className);
    }

    private static final Map<String, StandardAction> actions = Maps.newHashMap();

    static {
        for (StandardAction action : StandardAction.values()) {
            actions.put(action.name(), action);
        }
    }

    public static StandardAction valueOfAction(String actionCode) {
        return actions.get(actionCode);
    }

    public static final String STANDARD_ACTION_PACKAGE = StandardAction.class.getPackage().getName();
}
