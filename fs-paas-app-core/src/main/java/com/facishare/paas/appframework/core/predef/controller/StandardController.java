package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

/**
 * 对象基本操作
 * <p>
 * Created by liyiguang on 2017/6/20.
 */
public enum StandardController {

    List("List"),
    Detail("View"),
    NewDetail("View"),
    WebDetail("View"),
    RelatedObjectDigest("View"),
    Related("View"),
    RelatedCount(),
    RelatedCountSocial(),
    RelatedList("List"),
    RelatedListSocial("List"),
    DetailList("List"),
    DetailListSocial("List"),
    TreeRelatedList(),
    ListHeader(),
    DescribeLayout(),
    SimpleDetail(),
    PartDetail(),
    SearchList("List"),
    WhatList("List"),
    WhatListHeader(),
    DuplicateSearch(),
    RelatedDuplicateSearch(),
    SimpleDuplicateSearch(),
    RelatedSimpleDuplicateSearch(),
    SnapShotForWeb(),
    SnapShotForMob(),
    NewLogInfoListForMob(),
    NewLogInfoListForWeb(),
    DraftDetail(),
    DraftList,
    DetailListHeader,
    QuickEditLayout,
    ButtonLayout,
    // 获取列表页布局
    ListLayout(),
    ListCount(),
    // 列表查询
    FindBySearchTemplateQuery("List"),
    ValidRecordType(),
    DesignerLayout(),
    DesignerLayoutResource(),
    DesignerRelatedLayoutResource(),
    DesignerListLayoutResource(),
    DesignerLayoutBusinessComponent(),
    ImportObject(),
    DetailLayout("View"),
    SummaryField(),
    CRMObjectList(),
    PluginList(),
    // 计算默认值
    CalculateDefaultValue(),
    TreeList("List"),
    TreeViewPath(),
    DetailLayoutMerge(),
    ImportView(),
    ObjectTabConfig(),
    ObjectDataButton(),
    OuterScene(),
    OuterSceneList(),
    CrossObjectSupportFields(),
    ChangeDataMerge(),
    ;

    private final List<String> functionPrivilegeCodes;

    StandardController(String... privilegeCodes) {
        this.functionPrivilegeCodes = Lists.newArrayList(privilegeCodes);
    }

    public List<String> getFuncPrivilegeCodes() {
        return functionPrivilegeCodes;
    }

    public ControllerClassInfo getControllerClassInfo() {
        String className = STANDARD_CONTROLLER_PACKAGE + "." + "Standard" + this + "Controller";
        return new ControllerClassInfo(className);
    }

    private static final Map<String, StandardController> controllers = Maps.newHashMap();

    static {
        for (StandardController controller : StandardController.values()) {
            controllers.put(controller.name(), controller);
        }
    }

    public static StandardController valueOfController(String methodName) {
        return controllers.get(methodName);
    }

    public static final String STANDARD_CONTROLLER_PACKAGE = StandardController.class.getPackage().getName();

}
