package com.facishare.paas.appframework.rest.dto.data;

import com.fxiaoke.stone.commons.domain.constant.AuthModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 清理 signedUrl
 */
public interface CleanSignedUrl {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        private Map<String, Object> data;
        private Boolean nested;
    }
    
    @Builder
    @Data
    class Result {
        private Map<String, Object> data;
    }
}
