package com.facishare.paas.appframework.metadata.gdpr;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.PlatServiceProxy;
import com.facishare.paas.appframework.common.service.dto.FindEnterpriseName;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18N<PERSON>ey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.BaseEntity;
import com.facishare.paas.appframework.metadata.repository.model.GdprCompliance;
import com.facishare.paas.appframework.metadata.repository.model.GdprLegalBase;
import com.facishare.paas.appframework.metadata.repository.model.GdprProjectRequest;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.appframework.metadata.switchcache.SwitchCacheService;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectFieldExtra;
import com.facishare.paas.metadata.api.describe.SelectMany;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.ObjectFieldExtra;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.timezone.DateTimeFormat;
import com.facishare.paas.timezone.DateTimeFormatUtils;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.repository.model.GdprLegalBase.GDPR_LEGAL_BASE_AGREE_PERSON;
import static com.facishare.paas.appframework.metadata.repository.model.GdprLegalBase.GDPR_LEGAL_BASE_STATUS;

/**
 * <AUTHOR>
 * @date 2021/5/8 17:11
 */
@Service("gdprService")
@Slf4j
public class GdprServiceImpl implements GdprService {

    public static final int limit = 500;

    @Autowired
    private IRepository<GdprCompliance> complianceRepository;
    @Autowired
    private IRepository<GdprLegalBase> legalBaseRepository;
    @Autowired
    private IRepository<GdprProjectRequest> projectRequestRepository;
    @Autowired
    private ConfigService configService;
    @Autowired
    private SwitchCacheService switchCacheService;
    @Autowired
    private LogService logService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private PlatServiceProxy platServiceProxy;

    public static final String GDPR_AGREE_PERSON = "account";
    public static final String GDPR_EMAIL = "email";

    // 合规类型常量
    public static final String COMPLIANCE_TYPE_GDPR = "GDPR";

    // 安全级别常量
    public static final String SECURITY_LEVEL_SENSITIVE = "sensitive";
    public static final String SECURITY_LEVEL_ORDINARY = "ordinary";

    @Transactional
    @Override
    public void openGdprCompliance(User user, GdprCompliance gdprCompliance) {
        if (CollectionUtils.empty(gdprCompliance.getApiNames())) {
            log.warn("compliance apiNames is null,user:{}", user);
            return;
        }
        Set<String> removeApiNames = Sets.newHashSet();
        Optional<GdprCompliance> gdprComplianceData = findGdprComplianceData(user);
        if (gdprComplianceData.isPresent()) {
            List<String> oldApiNames = gdprComplianceData.get().getApiNames();
            if (CollectionUtils.notEmpty(oldApiNames)) {
                removeApiNames = oldApiNames.stream()
                        .filter(x -> !gdprCompliance.getApiNames().contains(x))
                        .collect(Collectors.toSet());
            }
        }
        gdprCompliance.setApiNames(gdprCompliance.getApiNames().stream().distinct().collect(Collectors.toList()));
        gdprCompliance.setOpenStatus(true);
        gdprCompliance.setTenantOpenStatus(true);
        try {
            configService.upsertTenantConfig(user, GDPR_COMPLIANCE_KEY, JacksonUtils.toJson(gdprCompliance), ConfigValueType.JSON);
            //更新现在对象的数据以及缓存
            gdprCompliance.getApiNames().forEach(objectApiName ->
                    switchCacheService.syncSwitchStatus(user.getTenantId(), GDPR_TYPE, objectApiName, Sets.newHashSet(GDPR_COMPLIANCE_KEY)));
            //清楚删除对象的数据以及缓存
            removeApiNames.forEach(objectApiName ->
                    switchCacheService.syncSwitchStatus(user.getTenantId(), GDPR_TYPE, objectApiName, Sets.newHashSet(GDPR_COMPLIANCE_KEY)));
        } catch (Exception e) {
            log.error("openGdprCompliance error! user:{} ", user, e);
            throw new MetaDataBusinessException("openGdprCompliance error");
        }
    }

    @Transactional
    @Override
    public void closeGdprCompliance(User user) {
        try {
            Optional<GdprCompliance> gdprComplianceData = findGdprComplianceData(user);
            if (gdprComplianceData.isPresent()) {
                GdprCompliance gdprCompliance = gdprComplianceData.get();
                if (CollectionUtils.empty(gdprCompliance.getApiNames())) {
                    return;
                }
                gdprCompliance.setOpenStatus(false);
                gdprCompliance.setTenantOpenStatus(false);
                //同步对象级开关
                configService.upsertTenantConfig(user, GDPR_COMPLIANCE_KEY, JacksonUtils.toJson(gdprCompliance), ConfigValueType.JSON);
                gdprCompliance.getApiNames().forEach(objectApiName ->
                        switchCacheService.syncSwitchStatus(user.getTenantId(), GDPR_TYPE, objectApiName, Sets.newHashSet(GDPR_COMPLIANCE_KEY)));
            }
        } catch (Exception e) {
            log.warn("closeGdprCompliance error! user:{} ", user, e);
            throw new MetaDataBusinessException("closeGdprCompliance error");
        }
    }

    @Override
    public boolean findGdprComplianceStatus(User user, String apiName) {
        String complianceConfig = configService.findTenantConfig(user, GDPR_COMPLIANCE_KEY);
        if (complianceConfig != null) {
            GdprCompliance gdprCompliance = JacksonUtils.fromJson(complianceConfig, GdprCompliance.class);
            if (gdprCompliance != null) {
                if (StringUtils.isBlank(apiName)) {
                    return gdprCompliance.isTenantOpenStatus();
                }
                if (CollectionUtils.notEmpty(gdprCompliance.getApiNames())) {
                    if (gdprCompliance.getApiNames().contains(apiName)) {
                        return gdprCompliance.isOpenStatus();
                    }
                }
            }
        }
        return false;
    }

    @Override
    public boolean findGdprComplianceStatusByCache(User user, String apiName) {
        if (StringUtils.isBlank(apiName)) {
            return findGdprComplianceStatus(user, apiName);
        }
        if (AppFrameworkConfig.isGraySwitchCache(user.getTenantId())) {
            return switchCacheService.findSwitchStatus(user.getTenantId(), GDPR_TYPE, GDPR_COMPLIANCE_KEY, apiName,
                    () -> findGdprComplianceStatus(user, apiName));
        }
        return findGdprComplianceStatus(user, apiName);
    }

    @Override
    public Optional<GdprCompliance> findGdprComplianceData(User user) {
        String complianceConfig = configService.findTenantConfig(user, GDPR_COMPLIANCE_KEY);
        if (Objects.nonNull(complianceConfig)) {
            GdprCompliance compliance = JacksonUtils.fromJson(complianceConfig, GdprCompliance.class);
            if (Objects.nonNull(compliance) && compliance.isOpenStatus()) {
                return Optional.of(compliance);
            }
        }
        return Optional.empty();
    }

    @Override
    public void saveGdprFormLang(User user, String lang) {
        configService.upsertTenantConfig(user, GDPR_COMPLIANCE_LANG_KEY, lang, ConfigValueType.STRING);
    }

    @Override
    public GdprFormData findGdprFormData(User user) {
        GdprFormData formData = GdprFormData.builder().build();
        FindEnterpriseName.Arg arg = new FindEnterpriseName.Arg();
        arg.setTenantId(user.getTenantId());
        try {
            FindEnterpriseName.Result result = platServiceProxy.findEnterpriseName(arg);
            formData.setEnterpriseName(result.getEnterpriseName());
            String lang = configService.findTenantConfig(user, GDPR_COMPLIANCE_LANG_KEY);
            formData.setLang(lang);
        } catch (Exception e) {
            log.error("getEnterpriseName error!,user:{}", user);
            throw new MetaDataBusinessException("search enterprise info error");
        }
        return formData;
    }

    @Override
    public void submitGdprFormData(User user, String id, List<String> agreeFirstOption, String remark) {
        Optional<GdprLegalBase> legalBase = findGdprLegalBaseById(user, id);
        if (!legalBase.isPresent()) {
            log.warn("submitGdprFormData error,gdpr data not exist,user:{},id:{}", user, id);
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.GDPR_LEGAL_BASE_CLOSE, "GDPR法律基础已关闭，请重新开启"));// ignoreI18n
        }
        boolean isLinkExpire = isLinkExpire(user, legalBase.get());
        if (isLinkExpire) {
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.GDPR_LINK_INVALID, "链接已过期，请重新发起同意表单"));// ignoreI18n
        }
        GdprLegalBase gdprLegalBase = new GdprLegalBase();
        gdprLegalBase.setAgreeFirstOption(agreeFirstOption);
        gdprLegalBase.setRemark(remark);
        gdprLegalBase.setAgreeDate(System.currentTimeMillis());
        gdprLegalBase.setAgreePerson(GDPR_AGREE_PERSON);
        mergeAddOrUpdateGdprLegalBaseDetailData(gdprLegalBase, legalBase.get(), user);
        legalBaseRepository.bulkUpsert(user, Lists.newArrayList(gdprLegalBase));
        recordAddGdprLog(user, gdprLegalBase);
    }

    @Override
    public boolean isLinkExpire(User user, GdprLegalBase gdprLegalBase) {
        boolean isLinkExpire = false;
        Optional<GdprCompliance> gdprComplianceData = findGdprComplianceData(user);
        if (!gdprComplianceData.isPresent()) {
            log.warn("submitGdprFormData error,gdpr does not open,user:{}", user);
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.GDPR_COMPLIANCE_CLOSE, "合规性配置已关闭，请重新开启"));// ignoreI18n
        }
        GdprCompliance gdprCompliance = gdprComplianceData.get();
        if (gdprCompliance.isTenantOpenStatus() && !gdprCompliance.getApiNames().contains(gdprLegalBase.getApiName())) {
            log.warn("submitGdprFormData error,gdpr does not open,user:{}", user);
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.GDPR_COMPLIANCE_CLOSE, "合规性配置已关闭，请重新开启"));// ignoreI18n
        }
        String periodType = gdprCompliance.getPeriodType();
        Integer period = gdprCompliance.getPeriod();
        Long linkCreateTime = gdprLegalBase.getLinkCreateTime();
        if (StringUtils.isNotBlank(periodType) && Objects.nonNull(period) && Objects.nonNull(linkCreateTime)) {
            LocalDateTime maxExpireTime = LocalDateTime.now();
            LocalDateTime localDateTime = LocalDateUtils.convertTimeMillisToDateTime(linkCreateTime);
            if (Objects.equals("month", periodType)) {
                maxExpireTime = localDateTime.plusMonths(period);
            }
            if (Objects.equals("day", periodType)) {
                maxExpireTime = localDateTime.plusDays(period);
            }
            long expireTime = LocalDateUtils.convertDateTimeToTimeMillis(maxExpireTime);
            if (expireTime < System.currentTimeMillis()) {
                isLinkExpire = true;
            }
        }
        return isLinkExpire;
    }

    @Override
    public boolean validateGdprCompliance(User user, String operation, String apiName, String dataId) {
        if (!findGdprComplianceStatusByCache(user, apiName)) {
            return false;
        }
        Optional<GdprCompliance> objectGdpr = findGdprComplianceData(user);
        if (objectGdpr.isPresent()) {
            GdprCompliance gdprCompliance = objectGdpr.get();
            if (gdprCompliance.isOpenStatus()) {
                if (CollectionUtils.notEmpty(gdprCompliance.getApiNames())
                        && gdprCompliance.getApiNames().contains(apiName)
                        && CollectionUtils.notEmpty(gdprCompliance.getUnusableOperation())
                        && gdprCompliance.getUnusableOperation().contains(operation)) {
                    Optional<GdprLegalBase> legalBase = findGdprLegalBaseByApiNameAndDataId(user, apiName, dataId);
                    if (legalBase.isPresent()) {
                        GdprLegalBase gdprLegalBase = legalBase.get();
                        return Boolean.TRUE.equals(gdprLegalBase.getOpenStatus())
                                && GdprLegalBase.LegalBase.AGREE.getValue().equals(gdprLegalBase.getLegalBase())
                                && GdprLegalBase.AgreeStatus.PENDING.getValue().equals(gdprLegalBase.getLegalBaseStatus());
                    }
                }
            }
        }
        return false;
    }


    public List<String> filterDelDataIds(User user, String operation, String apiName, List<IObjectData> dataList) {
        List<String> result = Lists.newArrayList();
        Optional<GdprCompliance> objectGdpr = findGdprComplianceData(user);
        List<String> dataIds = dataList.stream().map(DBRecord::getId).collect(Collectors.toList());
        if (CollectionUtils.empty(dataIds)) {
            return result;
        }
        if (objectGdpr.isPresent()) {
            GdprCompliance gdprCompliance = objectGdpr.get();
            if (gdprCompliance.isOpenStatus()) {
                if (CollectionUtils.notEmpty(gdprCompliance.getApiNames())
                        && gdprCompliance.getApiNames().contains(apiName)
                        && CollectionUtils.notEmpty(gdprCompliance.getUnusableOperation())
                        && gdprCompliance.getUnusableOperation().contains(operation)) {
                    List<GdprLegalBase> legalBaseList = queryGdprLegalBase(user, apiName, dataIds);
                    if (CollectionUtils.empty(legalBaseList)) {
                        return result;
                    }
                    result = legalBaseList.stream()
                            .filter(x -> GdprLegalBase.LegalBase.AGREE.getValue().equals(x.getLegalBase())
                                    && GdprLegalBase.AgreeStatus.PENDING.getValue().equals(x.getLegalBaseStatus()))
                            .map(GdprLegalBase::getDataId)
                            .collect(Collectors.toList());
                }
            }
        }
        return result;
    }

    private Query buildSearchQuery(String tenantId, String describeApiName) {
        List<IFilter> filters = Lists.newArrayList();
        filters.add(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, tenantId).getFilter());
        filters.add(FilterExt.of(Operator.EQ, IObjectData.DESCRIBE_API_NAME, describeApiName).getFilter());
        SearchQuery searchQuery = SearchQueryImpl.filters(filters);
        return Query.builder().limit(GdprServiceImpl.limit).searchQuery(searchQuery).build();
    }

    @Override
    public List<GdprCompliance> findGdprCompliance(User user, String apiName) {
        if (StringUtils.isBlank(apiName)) {
            log.warn("findGdprPersonalField error,apiName is null,user:{}", user);
            return Lists.newArrayList();
        }
        if (!findGdprComplianceStatusByCache(user, apiName)) {
            return Lists.newArrayList();
        }

        // 原有逻辑，从GdprCompliance表查询数据
        Query query = buildSearchQuery(user.getTenantId(), GdprCompliance.GDPR_COMPLIANCE_OBJ);
        query.and(FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, Lists.newArrayList(String.valueOf(DELETE_STATUS.NORMAL.getValue()))).getFilter());
        query.and(FilterExt.of(Operator.EQ, GdprCompliance.GDPR_COMPLIANCE_API_NAME, apiName).getFilter());
        List<GdprCompliance> compliances = complianceRepository.findBy(user, query, GdprCompliance.class);

        // 灰度判断，是否启用字段合规性设置功能
        if (!UdobjGrayConfig.isAllow(UdobjGrayKey.FIELD_COMPLIANCE_SETTING_GRAY, user.getTenantId())) {
            return compliances;
        }
        Optional<GdprCompliance> first = compliances.stream()
                .filter(x -> Objects.equals(x.getApiName(), apiName) && x.isOpenStatus())
                .findFirst();
        Map<String, List<IObjectFieldExtra>> fieldExtras = describeLogicService.findDescribeExtra(user, Collections.singleton(apiName));
        
        // 从字段扩展属性中提取安全配置信息
        Set<String> sensitiveFields = extractFieldsBySecurityLevel(fieldExtras, apiName, true);
        Set<String> ordinaryFields = extractFieldsBySecurityLevel(fieldExtras, apiName, false);

        // 处理合规性配置
        GdprCompliance gdprCompliance = processComplianceConfiguration(
                user, apiName, first, sensitiveFields, ordinaryFields, compliances);
        if (Objects.isNull(gdprCompliance)) {
            return compliances;
        }
        // 更新字段列表
        gdprCompliance.setSensitiveFields(Lists.newArrayList(sensitiveFields));
        gdprCompliance.setOrdinaryFields(Lists.newArrayList(ordinaryFields));
        // 从字段扩展属性中获取数据
        return compliances;
    }

    @Override
    public void updateGdprPersonalField(User user, String apiName, List<String> sensitiveFields, List<String> ordinaryFields) {
        if (!findGdprComplianceStatusByCache(user, apiName)) {
            return;
        }
        int sensitiveSize = sensitiveFields.size();
        int ordinarySize = ordinaryFields.size();
        if (sensitiveSize + ordinarySize > 30) {
            throw new ValidateException(I18N.text(I18NKey.GDPR_PERSONAL_FIELD_NUM));
        }
        Query query = buildSearchQuery(user.getTenantId(), GdprCompliance.GDPR_COMPLIANCE_OBJ);
        query.and(FilterExt.of(Operator.EQ, GdprCompliance.GDPR_COMPLIANCE_API_NAME, apiName).getFilter());
        List<GdprCompliance> complianceList = complianceRepository.findBy(user, query, GdprCompliance.class);
        updateGdprPersonalField(user, apiName, sensitiveFields, ordinaryFields, complianceList);
    }

    private GdprCompliance updateGdprPersonalField(User user, String apiName, List<String> sensitiveFields, List<String> ordinaryFields, List<GdprCompliance> complianceList) {
        Optional<GdprCompliance> objectGdpr = findGdprComplianceData(user);
        if (objectGdpr.isPresent()) {
            GdprCompliance compliance = complianceList.stream()
                    .filter(x -> Objects.equals(apiName, x.getApiName()))
                    .findFirst()
                    .orElse(new GdprCompliance());
            compliance.setApiName(apiName);
            compliance.setUnusableOperation(objectGdpr.get().getUnusableOperation());
            compliance.setForbidExport(objectGdpr.get().getForbidExport());
            compliance.setForbidOpenApi(objectGdpr.get().getForbidOpenApi());
            compliance.setOpenStatus(true);
            compliance.setDeleted(false);
            compliance.setTenantId(user.getTenantId());
            compliance.setObjectDescribeApiName(GdprCompliance.GDPR_COMPLIANCE_OBJ);
            compliance.setSensitiveFields(sensitiveFields);
            compliance.setOrdinaryFields(ordinaryFields);
            compliance.setLastModifiedBy(user.getUserId());
            compliance.setLastModifiedTime(System.currentTimeMillis());
            complianceRepository.bulkUpsert(user, Lists.newArrayList(compliance));
            return compliance;
        }
        return null;
    }

    @Override
    public List<String> needFilterGdprFields(User user, String apiName, String operation) {
        if (!findGdprComplianceStatusByCache(user, apiName)) {
            return Lists.newArrayList();
        }
        List<GdprCompliance> personalField = findGdprCompliance(user, apiName);
        if (CollectionUtils.notEmpty(personalField)) {
            return personalField.stream()
                    .filter(x -> Objects.equals(x.getApiName(), apiName))
                    .filter(GdprCompliance::isOpenStatus)
                    .map(x -> {
                        List<String> fields = Lists.newArrayList();
                        if (ObjectAction.BATCH_EXPORT.getActionCode().equals(operation)) {
                            if (GdprCompliance.SENSITIVE.equals(x.getForbidExport())) {
                                if (CollectionUtils.notEmpty(x.getSensitiveFields())) {
                                    fields.addAll(x.getSensitiveFields());
                                }
                            }
                            if (GdprCompliance.ORDINARY_AND_SENSITIVE.equals(x.getForbidExport())) {
                                if (CollectionUtils.notEmpty(x.getSensitiveFields())) {
                                    fields.addAll(x.getSensitiveFields());
                                }
                                if (CollectionUtils.notEmpty(x.getOrdinaryFields())) {
                                    fields.addAll(x.getOrdinaryFields());
                                }
                            }
                        }
                        if (RequestContext.OPENAPI_PEER_NAME.equals(operation)) {
                            if (GdprCompliance.SENSITIVE.equals(x.getForbidOpenApi())) {
                                if (CollectionUtils.notEmpty(x.getSensitiveFields())) {
                                    fields.addAll(x.getSensitiveFields());
                                }
                            }
                            if (GdprCompliance.ORDINARY_AND_SENSITIVE.equals(x.getForbidOpenApi())) {
                                if (CollectionUtils.notEmpty(x.getSensitiveFields())) {
                                    fields.addAll(x.getSensitiveFields());
                                }
                                if (CollectionUtils.notEmpty(x.getOrdinaryFields())) {
                                    fields.addAll(x.getOrdinaryFields());
                                }
                            }
                        }
                        return fields;
                    })
                    .flatMap(Collection::stream)
                    .distinct()
                    .collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    /**
     * 查询所有GdprCompliance记录
     */
    @Override
    public List<GdprCompliance> findGdprComplianceListByApiNames(User user, List<String> apiNames) {
        List<IFilter> filters = Lists.newArrayList();
        filters.add(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter());
        filters.add(FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter());
        filters.add(FilterExt.of(Operator.EQ, IObjectData.DESCRIBE_API_NAME, GdprCompliance.GDPR_COMPLIANCE_OBJ).getFilter());
        filters.add(FilterExt.of(Operator.IN, GdprCompliance.GDPR_COMPLIANCE_API_NAME, apiNames).getFilter());
        filters.add(FilterExt.of(Operator.EQ, GdprCompliance.OPEN_STATUS, Boolean.TRUE.toString()).getFilter());
        SearchQuery searchQuery = SearchQueryImpl.filters(filters);
        Query query = Query.builder()
                .limit(GdprServiceImpl.limit)
                .searchQuery(searchQuery)
                .build();
        return complianceRepository.findBy(user, query, GdprCompliance.class);
    }

    /**
     * 迁移单个对象的字段安全配置到字段扩展属性
     */
    @Override
    public void migrateObjectFieldsToExtra(User user, GdprCompliance compliance) {
        String apiName = compliance.getApiName();
        List<String> sensitiveFields = compliance.getSensitiveFields();
        List<String> ordinaryFields = compliance.getOrdinaryFields();

        // 如果没有配置任何字段，跳过
        if (CollectionUtils.empty(sensitiveFields) && CollectionUtils.empty(ordinaryFields)) {
            return;
        }
        // 更新字段扩展属性
        updateFieldSecurityInFieldExtra(user, apiName, sensitiveFields, ordinaryFields);
    }

    @Override
    public void openGdprLegalBase(User user, String apiName, String dataId) {
        Optional<GdprLegalBase> dbData = findGdprLegalBaseByApiNameAndDataId(user, apiName, dataId);
        GdprLegalBase data = new GdprLegalBase();
        data.setOpenStatus(true);
        fillSystemFieldInfo(user, apiName, dataId, data);
        if (dbData.isPresent()) {
            data.setId(dbData.get().getId());
        } else {
            data.setId(IdGenerator.get());
        }
        legalBaseRepository.bulkUpsert(user, Lists.newArrayList(data));
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), GdprLegalBase.GDPR_LEGAL_BASE_OBJ);
        logService.logCustomMessageOnly(user, EventType.ADD, ActionType.GDPR, describe, data.convertTo(), I18N.text(I18NKey.GDPR_OPEN_SUCCESS));
    }

    private void fillSystemFieldInfo(User user, String apiName, String dataId, GdprLegalBase data) {
        data.setApiName(apiName);
        data.setDataId(dataId);
        data.setObjectDescribeApiName(GdprLegalBase.GDPR_LEGAL_BASE_OBJ);
        data.setTenantId(user.getTenantId());
        data.setLastModifiedBy(user.getUserId());
        data.setLastModifiedTime(System.currentTimeMillis());
    }

    @Override
    public void closeGdprLegalBase(User user, String apiName, String dataId) {
        Optional<GdprLegalBase> legalBase = findGdprLegalBaseByApiNameAndDataId(user, apiName, dataId);
        if (!legalBase.isPresent()) {
            log.warn("closeGdprLegalBase error,user={},apiName={}", user, apiName);
            return;
        }
        GdprLegalBase data = new GdprLegalBase();
        data.setOpenStatus(false);
        data.setId(legalBase.get().getId());
        fillSystemFieldInfo(user, apiName, dataId, data);
        legalBaseRepository.bulkUpsert(user, Lists.newArrayList(data));
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), GdprLegalBase.GDPR_LEGAL_BASE_OBJ);
        logService.logCustomMessageOnly(user, EventType.DELETE, ActionType.GDPR, describe, data.convertTo(), I18N.text(I18NKey.GDPR_CLOSE_SUCCESS));
    }

    @Override
    public Optional<GdprLegalBase> findGdprLegalBaseByApiNameAndDataId(User user, String apiName, String dataId) {
        if (StringUtils.isBlank(apiName) || StringUtils.isBlank(dataId)) {
            log.warn("findGdprLegalBaseByApiNameAndDataId param error! apiName:{},dataId:{} ", apiName, dataId);
            return Optional.empty();
        }
        Query query = buildSearchQuery(user.getTenantId(), GdprLegalBase.GDPR_LEGAL_BASE_OBJ);
        query.and(FilterExt.of(Operator.EQ, GdprLegalBase.GDPR_LEGAL_BASE_API_NAME, apiName).getFilter());
        query.and(FilterExt.of(Operator.EQ, GdprLegalBase.GDPR_LEGAL_BASE_DATA_ID, dataId).getFilter());
        List<GdprLegalBase> legalBaseList = legalBaseRepository.findBy(user, query, GdprLegalBase.class);
        Optional<GdprLegalBase> legalBase = legalBaseList.stream()
                .filter(x -> Objects.equals(x.getDataId(), dataId) && Objects.equals(x.getApiName(), apiName))
                .findFirst();
        return legalBase;
    }

    public Optional<GdprLegalBase> findGdprLegalBaseById(User user, String id) {
        if (StringUtils.isBlank(id)) {
            log.warn("findGdprLegalBaseByApiNameAndDataId param error,id is null ");
            return Optional.empty();
        }
        Query query = buildSearchQuery(user.getTenantId(), GdprLegalBase.GDPR_LEGAL_BASE_OBJ);
        query.and(FilterExt.of(Operator.EQ, IObjectData.ID, id).getFilter());
        List<GdprLegalBase> legalBaseList = legalBaseRepository.findBy(user, query, GdprLegalBase.class);
        Optional<GdprLegalBase> legalBase = legalBaseList.stream()
                .filter(x -> Objects.equals(x.getId(), id))
                .findFirst();
        return legalBase;
    }


    @Override
    public List<GdprLegalBase> queryGdprLegalBase(User user, String apiName, List<String> dataIds) {
        if (!Boolean.TRUE.equals(findGdprComplianceStatusByCache(user, apiName))) {
            log.warn("gdpr does not opened,user:{},apiName:{}", user, apiName);
            return Lists.newArrayList();
        }
        if (StringUtils.isBlank(apiName) || CollectionUtils.empty(dataIds)) {
            log.warn("queryGdprLegalBase param error! apiName:{},dataIds:{}", apiName, dataIds);
            return Lists.newArrayList();
        }
        Query query = buildSearchQuery(user.getTenantId(), GdprLegalBase.GDPR_LEGAL_BASE_OBJ);
        query.and(FilterExt.of(Operator.EQ, GdprLegalBase.GDPR_LEGAL_BASE_API_NAME, apiName).getFilter());
        query.and(FilterExt.of(Operator.IN, GdprLegalBase.GDPR_LEGAL_BASE_DATA_ID, dataIds).getFilter());
        query.and(FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter());
        return legalBaseRepository.findBy(user, query, GdprLegalBase.class);
    }

    @Override
    public void updateGdprLegalBase(User user, String apiName, String dataId, String legalBase, String remark) {
        if (StringUtils.isBlank(apiName) || StringUtils.isBlank(dataId)) {
            throw new ValidateException(I18N.text(I18NKey.GDPR_PARAMS_ERROR));
        }
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), GdprLegalBase.GDPR_LEGAL_BASE_OBJ);
        Optional<IFieldDescribe> legalBaseFieldDescribe = ObjectDescribeExt.of(describe).getFieldDescribeSilently(GdprLegalBase.GDPR_LEGAL_BASE);
        if (!legalBaseFieldDescribe.isPresent()) {
            log.warn("legal base field not found!");
            return;
        }
        if (legalBaseFieldDescribe.get() instanceof SelectOneFieldDescribe) {
            List<String> legalBaseOptions = ((SelectOneFieldDescribe) legalBaseFieldDescribe.get())
                    .getSelectOptions().stream()
                    .map(ISelectOption::getValue)
                    .collect(Collectors.toList());
            if (CollectionUtils.empty(legalBaseOptions)) {
                log.warn("legalBaseOptions is empty!");
                throw new ValidateException(I18N.text(I18NKey.GDPR_PARAMS_ERROR));
            }
            if (!legalBaseOptions.contains(legalBase)) {
                log.warn("legalBaseOptions not contain gdprLegalBase:{}!", legalBase);
                throw new ValidateException(I18N.text(I18NKey.GDPR_PARAMS_ERROR));
            }
        }
        Optional<GdprLegalBase> gdprLegalBase = findGdprLegalBaseByApiNameAndDataId(user, apiName, dataId);
        GdprLegalBase data = new GdprLegalBase();
        data.setOpenStatus(true);
        data.setLegalBase(legalBase);
        gdprLegalBase.ifPresent(dbData -> {
            data.setId(dbData.getId());
            data.setLinkCreateTime(dbData.getLinkCreateTime());
            if (Objects.equals(legalBase, dbData.getLegalBase())) {
                data.setRemark(remark);
            }
        });
        if (GdprLegalBase.LegalBase.AGREE.getValue().equals(legalBase)) {
            data.setLegalBaseStatus(GdprLegalBase.AgreeStatus.PENDING.getValue());
        }
        fillSystemFieldInfo(user, apiName, dataId, data);
        legalBaseRepository.bulkUpsert(user, Lists.newArrayList(data));
        IObjectData dbObjectData = new ObjectData();
        if (gdprLegalBase.isPresent()) {
            dbObjectData = gdprLegalBase.get().convertTo();
        }
        IObjectData objectData = data.convertTo();
        Map<String, Object> updateFieldMap = diffMasterData(dbObjectData, objectData, describe);
        objectData.setName(I18N.text(I18NKey.GDPR_LEGAL_BASE));
        logService.log(user, EventType.MODIFY, ActionType.Modify, describe, objectData, updateFieldMap, dbObjectData);
    }

    @Override
    public void bulkInvalidGdprLegalBase(User user, String apiName, List<String> dataIds) {
        boolean isOpenGdpr = findGdprComplianceStatusByCache(user, apiName);
        if (!isOpenGdpr) {
            return;
        }
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), GdprLegalBase.GDPR_LEGAL_BASE_OBJ);
        if (CollectionUtils.empty(dataIds)) {
            return;
        }
        Lists.partition(dataIds, 500).forEach(ids -> {
            List<GdprLegalBase> legalBaseList = findByIds(user, apiName, ids);
            if (CollectionUtils.empty(legalBaseList)) {
                return;
            }
            List<GdprLegalBase> result = legalBaseRepository.bulkInvalid(user, legalBaseList);
            recordBulkRecord(result, describe, user, ActionType.Invalid);
        });
    }

    @Override
    public void bulkRecoverGdprLegalBase(User user, String apiName, List<String> dataIds) {
        boolean isOpenGdpr = findGdprComplianceStatusByCache(user, apiName);
        if (!isOpenGdpr) {
            return;
        }
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), GdprLegalBase.GDPR_LEGAL_BASE_OBJ);
        if (CollectionUtils.empty(dataIds)) {
            return;
        }
        Lists.partition(dataIds, 500).forEach(ids -> {
            List<GdprLegalBase> legalBaseList = findByIds(user, apiName, ids);
            if (CollectionUtils.empty(legalBaseList)) {
                return;
            }
            List<GdprLegalBase> result = legalBaseRepository.bulkRecover(user, legalBaseList);
            recordBulkRecord(result, describe, user, ActionType.Recovery);
        });
    }

    @Override
    public void bulkDeleteGdprLegalBase(User user, String apiName, List<String> dataIds) {
        boolean isOpenGdpr = findGdprComplianceStatusByCache(user, apiName);
        if (!isOpenGdpr) {
            return;
        }
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), GdprLegalBase.GDPR_LEGAL_BASE_OBJ);
        if (CollectionUtils.empty(dataIds)) {
            return;
        }
        Lists.partition(dataIds, 500).forEach(ids -> {
            List<GdprLegalBase> legalBaseList = findByIds(user, apiName, ids);
            if (CollectionUtils.empty(legalBaseList)) {
                return;
            }
            List<GdprLegalBase> result = legalBaseRepository.bulkDelete(user, legalBaseList);
            recordBulkRecord(result, describe, user, ActionType.Delete);
        });
    }

    private void recordBulkRecord(List<GdprLegalBase> result, IObjectDescribe describe, User user, ActionType actionType) {
        List<IObjectData> objectDataList = result.stream()
                .map(BaseEntity::convertTo)
                .peek(x -> x.setName(I18N.text(I18NKey.GDPR_LEGAL_BASE)))
                .collect(Collectors.toList());
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(describe.getApiName(), describe);
        logService.masterDetailLog(user, EventType.MODIFY, actionType, describeMap, objectDataList);
    }

    private List<GdprLegalBase> findByIds(User user, String apiName, List<String> ids) {
        Query query = Query.builder().limit(ids.size()).build();
        query.and(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter());
        query.and(FilterExt.of(Operator.EQ, IObjectData.DESCRIBE_API_NAME, GdprLegalBase.GDPR_LEGAL_BASE_OBJ).getFilter());
        query.and(FilterExt.of(Operator.EQ, GdprLegalBase.GDPR_LEGAL_BASE_API_NAME, apiName).getFilter());
        query.and(FilterExt.of(Operator.IN, GdprLegalBase.GDPR_LEGAL_BASE_DATA_ID, ids).getFilter());
        List<GdprLegalBase> legalBaseList = legalBaseRepository.findBy(user, query, GdprLegalBase.class);
        if (CollectionUtils.empty(legalBaseList)) {
            return Lists.newArrayList();
        }
        return legalBaseList;
    }

    @Override
    @Transactional
    public void updateGdprLegalBase(User user, String apiName, String dataId, String legalBase) {
        updateGdprLegalBase(user, apiName, dataId, legalBase, "");
    }

    @Override
    public void createLegalBaseLink(User user, String dataId, String apiName) {
        Optional<GdprLegalBase> legalBase = findGdprLegalBaseByApiNameAndDataId(user, apiName, dataId);
        if (!legalBase.isPresent()) {
            return;
        }
        GdprLegalBase data = new GdprLegalBase();
        String key = legalBase.get().getId() + "_" + user.getTenantId();
        String linkId = EncryptUtil.encode(key);
        data.setId(legalBase.get().getId());
        data.setOpenStatus(true);
        data.setLinkId(linkId);
        data.setLinkCreateTime(System.currentTimeMillis());
        data.setLegalBase(GdprLegalBase.LegalBase.AGREE.getValue());
        data.setLegalBaseStatus(GdprLegalBase.AgreeStatus.WAITING.getValue());
        fillSystemFieldInfo(user, apiName, dataId, data);
        legalBaseRepository.bulkUpsert(user, Lists.newArrayList(data));
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), GdprLegalBase.GDPR_LEGAL_BASE_OBJ);
        logService.logCustomMessageOnly(user, EventType.ADD, ActionType.GDPR, describe, data.convertTo(),
                I18N.text(I18NKey.GDPR_CREATE_LEGAL_BASE_LINK));
    }

    @Override
    public void addGdprLegalBaseDetail(User user, GdprLegalBase gdprLegalBase) {
        Optional<GdprLegalBase> dbData = findGdprLegalBaseByApiNameAndDataId(user, gdprLegalBase.getApiName(), gdprLegalBase.getDataId());
        if (!dbData.isPresent()) {
            log.warn("addGdprLegalBaseDetail error,user={}", user);
            return;
        }
        mergeAddOrUpdateGdprLegalBaseDetailData(gdprLegalBase, dbData.get(), user);
        legalBaseRepository.bulkUpsert(user, Lists.newArrayList(gdprLegalBase));
        recordAddGdprLog(user, gdprLegalBase);
    }

    private void recordAddGdprLog(User user, GdprLegalBase legalBaseData) {
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), GdprLegalBase.GDPR_LEGAL_BASE_OBJ);
        Optional<IFieldDescribe> legalBaseStatus = ObjectDescribeExt.of(describe).getFieldDescribeSilently(GDPR_LEGAL_BASE_STATUS);
        String status = "";
        if (legalBaseStatus.isPresent() && legalBaseStatus.get() instanceof SelectOne) {
            status = SelectOneExt.of((SelectOne) legalBaseStatus.get()).getLabelByValue(legalBaseData.getLegalBaseStatus());
        }
        String userName = getAgreePersonName(user, legalBaseData);
        Optional<IFieldDescribe> contactWay = ObjectDescribeExt.of(describe).getFieldDescribeSilently("contact_way");
        String way = " ";
        if (contactWay.isPresent() && contactWay.get() instanceof SelectOne) {
            way = SelectOneExt.of((SelectOne) contactWay.get()).getLabelByValue(legalBaseData.getContactWay());
        }
        Optional<IFieldDescribe> agreeFirstOptionField = ObjectDescribeExt.of(describe).getFieldDescribeSilently("agree_first_option");
        String agreeFirstOption = " ";
        if (agreeFirstOptionField.isPresent() && agreeFirstOptionField.get() instanceof SelectMany) {
            agreeFirstOption = ((SelectMany) agreeFirstOptionField.get()).getSelectOptions().stream()
                    .filter(x -> legalBaseData.getAgreeFirstOption().contains(x.getValue()))
                    .map(ISelectOption::getLabel)
                    .collect(Collectors.joining(","));
        }
        logService.logCustomMessageOnly(user, EventType.ADD, ActionType.GDPR, describe, legalBaseData.convertTo(),
                I18N.text(I18NKey.GDPR_ADD_LEGAL_BASE_DETAIL_2, status, agreeFirstOption, DateTimeFormatUtils.format(legalBaseData.getAgreeDate(),
                        DateTimeFormat.DATE.getType()), way, userName, legalBaseData.getRemark()));
    }

    private String getAgreePersonName(User user, GdprLegalBase legalBaseData) {
        String userName = "";
        if (Objects.equals(user.getUserId(), legalBaseData.getAgreePerson())) {
            List<UserInfo> userInfos = orgService.getUserNameByIds(user.getTenantId(), user.getUserId(), Lists.newArrayList(user.getUserId()));
            userName = userInfos.stream().filter(x -> Objects.equals(x.getId(), legalBaseData.getAgreePerson()))
                    .map(UserInfo::getNickname).findFirst().orElse("");
        }
        if (Objects.equals(GDPR_AGREE_PERSON, legalBaseData.getAgreePerson())) {
            userName = I18NExt.text(I18NKey.GDPR_AGREE_PERSON_ACCOUNT);
        }
        return userName;
    }


    @Override
    public void updateGdprLegalBaseDetail(User user, GdprLegalBase gdprLegalBase) {
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), GdprLegalBase.GDPR_LEGAL_BASE_OBJ);

        Optional<GdprLegalBase> legalBase = findGdprLegalBaseByApiNameAndDataId(user, gdprLegalBase.getApiName(), gdprLegalBase.getDataId());
        if (!legalBase.isPresent()) {
            log.warn("updateGdprLegalBaseDetail findById not exist,user={}", user);
            return;
        }
        GdprLegalBase dbData = legalBase.get();
        mergeAddOrUpdateGdprLegalBaseDetailData(gdprLegalBase, dbData, user);
        legalBaseRepository.bulkUpsert(user, Lists.newArrayList(gdprLegalBase));

        IObjectData dbObjectData = dbData.convertTo();
        String oldPersonName = getAgreePersonName(user, dbData);
        dbObjectData.set(GDPR_LEGAL_BASE_AGREE_PERSON, oldPersonName);

        IObjectData objectData = gdprLegalBase.convertTo();
        String newPersonName = getAgreePersonName(user, gdprLegalBase);
        objectData.set(GDPR_LEGAL_BASE_AGREE_PERSON, newPersonName);

        Map<String, Object> updateFieldMap = diffMasterData(dbObjectData, objectData, describe);
        describe.setDisplayName(I18N.text(I18NKey.GDPR_LEGAL_BASE_DETAIL));
        objectData.setName(I18N.text(I18NKey.GDPR_LEGAL_BASE_DETAIL));
        logService.log(user, EventType.MODIFY, ActionType.Modify, describe, objectData, updateFieldMap, dbObjectData);
    }

    private void mergeAddOrUpdateGdprLegalBaseDetailData(GdprLegalBase data, GdprLegalBase dbData, User user) {
        if (StringUtils.isBlank(data.getAgreePerson())) {
            data.setAgreePerson(user.getUserId());
        }
        if (StringUtils.isBlank(data.getContactWay())) {
            data.setContactWay(GDPR_EMAIL);
        }
        data.setLegalBase(GdprLegalBase.LegalBase.AGREE.getValue());
        data.setLegalBaseStatus(GdprLegalBase.AgreeStatus.OBTAIN.getValue());
        data.setId(dbData.getId());
        data.setOpenStatus(dbData.getOpenStatus());
        data.setLinkCreateTime(dbData.getLinkCreateTime());
        fillSystemFieldInfo(user, dbData.getApiName(), dbData.getDataId(), data);
    }

    private Map<String, Object> diffMasterData(IObjectData dbMasterData, IObjectData objectData, IObjectDescribe objectDescribe) {
        Map<String, Object> updatedFieldMap = Maps.newHashMap();
        Map<String, Object> updateFields = ObjectDataExt.of(dbMasterData).diff(objectData, objectDescribe);
        if (CollectionUtils.notEmpty(updateFields)) {
            ObjectDataExt dataExt = ObjectDataExt.of(Maps.newHashMap(updateFields));
            dataExt.removeInvalidFieldForApproval(objectDescribe);
            updatedFieldMap.putAll(updateFields);
        }
        return updatedFieldMap;
    }

    @Override
    public void deleteGdprLegalBaseDetail(User user, String id) {
        Optional<GdprLegalBase> gdprLegalBase = findGdprLegalBaseById(user, id);
        if (!gdprLegalBase.isPresent()) {
            log.warn("deleteGdprLegalBaseDetail findBy error,user={}", user);
            return;
        }
        GdprLegalBase dbData = gdprLegalBase.get();
        GdprLegalBase data = new GdprLegalBase();
        data.setId(dbData.getId());
        data.setOpenStatus(dbData.getOpenStatus());
        data.setLegalBase(GdprLegalBase.LegalBase.AGREE.getValue());
        data.setLegalBaseStatus(GdprLegalBase.AgreeStatus.PENDING.getValue());
        fillSystemFieldInfo(user, dbData.getApiName(), dbData.getDataId(), data);
        legalBaseRepository.bulkUpsert(user, Lists.newArrayList(data));
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), GdprLegalBase.GDPR_LEGAL_BASE_OBJ);
        logService.logCustomMessageOnly(user, EventType.MODIFY, ActionType.GDPR, describe, data.convertTo(), I18N.text(I18NKey.GDPR_DELETE_LEGAL_BASE_DETAIL));
    }

    public void removeGdprData(User user, String apiName, List<IObjectData> dataList, String operation) {
        if (!findGdprComplianceStatusByCache(user, apiName)) {
            return;
        }
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        List<String> delIds = filterDelDataIds(user, operation, apiName, dataList);
        dataList.removeIf(x -> delIds.contains(x.getId()));
    }

    @Override
    public List<GdprProjectRequest> findGdprProjectRequest(User user, String apiName, String dataId) {
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "#findGdprProjectRequestData" + user.getTenantId());
        Query query = buildSearchQuery(user.getTenantId(), GdprProjectRequest.GDPR_PROJECT_REQUEST_OBJ);
        if (StringUtils.isNotBlank(apiName)) {
            query.and(FilterExt.of(Operator.EQ, GdprProjectRequest.GDPR_PROJECT_REQUEST_API_NAME, apiName).getFilter());
        }
        if (StringUtils.isNotBlank(dataId)) {
            query.and(FilterExt.of(Operator.EQ, GdprProjectRequest.GDPR_PROJECT_REQUEST_DATA_ID, dataId).getFilter());
        }
        query.addOrders(OrderByExt.orderByField(IObjectData.LAST_MODIFIED_TIME, false));
        List<GdprProjectRequest> projectRequestList = projectRequestRepository.findBy(user, query, GdprProjectRequest.class);
        stopWatch.lap("findGdprProjectRequestData");
        return projectRequestList;
    }

    @Override
    public List<GdprProjectRequest> findGdprProjectRequestByType(User user, String apiName, String type, String dataId) {
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "#findGdprProjectRequestData" + user.getTenantId());
        Query query = buildSearchQuery(user.getTenantId(), GdprProjectRequest.GDPR_PROJECT_REQUEST_OBJ);
        if (StringUtils.isNotBlank(apiName)) {
            query.and(FilterExt.of(Operator.EQ, GdprProjectRequest.GDPR_PROJECT_REQUEST_API_NAME, apiName).getFilter());
        }
        if (StringUtils.isNotBlank(type)) {
            query.and(FilterExt.of(Operator.EQ, GdprProjectRequest.GDPR_PROJECT_REQUEST_TYPE, type).getFilter());
        }
        if (StringUtils.isNotBlank(dataId)) {
            query.and(FilterExt.of(Operator.EQ, GdprProjectRequest.GDPR_PROJECT_REQUEST_DATA_ID, dataId).getFilter());
        }
        List<GdprProjectRequest> projectRequestList = projectRequestRepository.findBy(user, query, GdprProjectRequest.class);
        stopWatch.lap("findGdprProjectRequestData");
        return projectRequestList;
    }

    @Override
    public void addGdprProjectRequest(User user, String apiName, String dataId, String type) {
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "#addGdprProjectRequestData" + user.getTenantId());
        List<GdprProjectRequest> dataByType = findGdprProjectRequestByType(user, apiName, type, dataId);
        if (CollectionUtils.notEmpty(dataByType) && !GdprProjectRequest.GDPR_PROJECT_REQUEST_TYPE_STATUS_CLOSE.equals(dataByType.get(0).getStatus())) {
            throw new ValidateException(I18N.text(I18NKey.GDPR_ADD_PROJECT_REQUEST_DUPLICATE));
        }
        GdprProjectRequest projectRequest = buildGdprProjectRequest(user, apiName, dataId, type);
        projectRequestRepository.create(user, projectRequest);
        stopWatch.lap("createGdprProjectRequestData");
    }

    private GdprProjectRequest buildGdprProjectRequest(User user, String apiName, String dataId, String type) {
        Long time = System.currentTimeMillis();
        GdprProjectRequest gdprProjectRequest = new GdprProjectRequest();
        gdprProjectRequest.setTenantId(user.getTenantId());
        gdprProjectRequest.setDeleted(false);
        gdprProjectRequest.setObjectDescribeApiName(GdprProjectRequest.GDPR_PROJECT_REQUEST_OBJ);
        gdprProjectRequest.setDataId(dataId);
        gdprProjectRequest.setType(type);
        gdprProjectRequest.setApiName(apiName);
        gdprProjectRequest.setStatus(GdprProjectRequest.GDPR_PROJECT_REQUEST_TYPE_STATUS_OPEN);
        gdprProjectRequest.setCreateBy(user.getUserId());
        gdprProjectRequest.setCreateTime(time);
        gdprProjectRequest.setLastModifiedBy(user.getUserId());
        gdprProjectRequest.setLastModifiedTime(time);
        return gdprProjectRequest;
    }

    @Override
    public void updateGdprProjectRequest(User user, String id) {
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "#updateGdprProjectRequestData#" + user.getTenantId());
        List<GdprProjectRequest> projectRequestList = findGdprProjectRequestById(user, id);
        if (CollectionUtils.empty(projectRequestList)) {
            return;
        }
        for (GdprProjectRequest projectRequest : projectRequestList) {
            projectRequest.setStatus(GdprProjectRequest.GDPR_PROJECT_REQUEST_TYPE_STATUS_CLOSE);
        }
        projectRequestRepository.bulkUpsert(user, projectRequestList);
        stopWatch.lap("updateGdprProjectRequest");
    }

    @Override
    public void updateGdprProjectRequestByType(User user, String id, String type) {
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "#updateGdprProjectRequestByType#" + user.getTenantId());
        List<GdprProjectRequest> projectRequestList = findGdprProjectRequestById(user, id);
        if (CollectionUtils.empty(projectRequestList)) {
            return;
        }
        for (GdprProjectRequest projectRequest : projectRequestList) {
            if (GdprProjectRequest.GDPR_PROJECT_REQUEST_TYPE_STATUS_LOCK.equals(type)) {
                projectRequest.setStatus(type);
            } else {
                projectRequest.setStatus(GdprProjectRequest.GDPR_PROJECT_REQUEST_TYPE_STATUS_CLOSE);
            }
        }
        projectRequestRepository.bulkUpsert(user, projectRequestList);
        stopWatch.lap("updateGdprProjectRequest");
    }

    @Override
    public void deleteGdprProjectRequest(User user, String id) {
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "#deleteGdprProjectRequest#" + user.getTenantId());
        List<GdprProjectRequest> projectRequestList = findGdprProjectRequestById(user, id);
        if (CollectionUtils.empty(projectRequestList)) {
            return;
        }
        Optional<GdprProjectRequest> projectRequest = projectRequestList.stream().filter(x -> Objects.equals(x.getId(), id)).findFirst();
        if (projectRequest.isPresent()) {
            if (!GdprProjectRequest.GDPR_PROJECT_REQUEST_TYPE_STATUS_OPEN.equals(projectRequest.get().getStatus())) {
                throw new ValidateException(I18N.text(I18NKey.GDPR_PROJECT_REQUEST_CLOSE));
            }
        }
        projectRequestRepository.bulkInvalidAndDelete(user, projectRequestList);
        stopWatch.lap("deleteGdprProjectRequest");
    }


    public void closeGdprProjectRequestByApiName(User user, List<String> apiNames) {
        Query query = buildSearchQuery(user.getTenantId(), GdprProjectRequest.GDPR_PROJECT_REQUEST_OBJ);
        query.and(FilterExt.of(Operator.IN, GdprProjectRequest.GDPR_PROJECT_REQUEST_API_NAME, apiNames).getFilter());
        Integer count = projectRequestRepository.findCountOnly(user, query, GdprProjectRequest.class);
        query.resetOrders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.ID, true)));
        int totalPage = SearchTemplateQueryExt.calculateTotalPage(count, limit);
        for (int pageNum = 1; pageNum <= totalPage; pageNum++) {
            query.setOffset(SearchTemplateQueryExt.calculateOffset(pageNum, limit));
            List<GdprProjectRequest> projectRequestList = projectRequestRepository.findBy(user, query, GdprProjectRequest.class);
            projectRequestRepository.bulkInvalidAndDelete(user, projectRequestList);
        }
    }


    /**
     * 根据id查询数据项目请求数据
     *
     * @param user 用户
     * @param id   项目请求数据id
     * @return 数据项目请求信息
     */
    private List<GdprProjectRequest> findGdprProjectRequestById(User user, String id) {
        Query query = buildSearchQuery(user.getTenantId(), GdprProjectRequest.GDPR_PROJECT_REQUEST_OBJ);
        query.and(FilterExt.of(Operator.EQ, IObjectData.ID, id).getFilter());
        return projectRequestRepository.findBy(user, query, GdprProjectRequest.class);
    }

    /**
     * 更新字段扩展属性中的安全设置
     * @param user 用户，代表企业
     * @param apiName 对象API名称
     * @param sensitiveFields 敏感字段列表
     * @param ordinaryFields 普通字段列表
     */
    private void updateFieldSecurityInFieldExtra(User user, String apiName, List<String> sensitiveFields, List<String> ordinaryFields) {
        try {
            // 1. 获取当前所有字段扩展属性
            Map<String, List<IObjectFieldExtra>> fieldExtras = describeLogicService.findDescribeExtra(user, Collections.singleton(apiName));
            List<IObjectFieldExtra> extras = fieldExtras.containsKey(apiName) ? fieldExtras.get(apiName) : new ArrayList<>();

            // 2. 创建需要更新的字段扩展属性集合
            List<IObjectFieldExtra> updateList = new ArrayList<>();

            // 3. 处理敏感字段
            if (CollectionUtils.notEmpty(sensitiveFields)) {
                processFields(extras, sensitiveFields, updateList, true);
            }

            // 4. 处理普通字段
            if (CollectionUtils.notEmpty(ordinaryFields)) {
                processFields(extras, ordinaryFields, updateList, false);
            }

            // 5. 批量更新字段扩展属性
            if (CollectionUtils.notEmpty(updateList)) {
                // 使用describeLogicService更新字段扩展属性
                describeLogicService.upsertObjectFieldExtra(user, apiName, updateList);
            }
        } catch (Exception e) {
            log.error("Failed to update field security settings to field extension properties: " + apiName, e);
            throw new RuntimeException("Failed to update field security settings", e);
        }
    }

    /**
     * 处理字段集合，为每个字段创建或更新扩展属性
     *
     * @param extras 现有字段扩展属性集合
     * @param fields 待处理的字段列表
     * @param updateList 更新列表
     * @param isSensitive 是否为敏感字段
     */
    private void processFields(List<IObjectFieldExtra> extras, List<String> fields,
                               List<IObjectFieldExtra> updateList, boolean isSensitive) {
        for (String fieldApiName : fields) {
            // 查找现有的字段扩展属性
            IObjectFieldExtra existing = findFieldExtra(extras, fieldApiName);
            Map<String, String> complianceSetting = isSensitive ?
                    createSensitiveComplianceSetting() : createOrdinaryComplianceSetting();

            if (Objects.nonNull(existing)) {
                // 如果存在，更新现有的扩展属性
                ObjectFieldExtra objectFieldExtra = (ObjectFieldExtra) existing;
                ObjectFieldExtra newExtra = new ObjectFieldExtra(Maps.newHashMap(objectFieldExtra.getContainerDocument()));
                newExtra.setComplianceSetting(complianceSetting);
                updateList.add(newExtra);
            } else {
                // 如果不存在，创建新的扩展属性
                ObjectFieldExtra newExtra = new ObjectFieldExtra();
                newExtra.setFieldApiName(fieldApiName);
                newExtra.setComplianceSetting(complianceSetting);
                updateList.add(newExtra);
            }
        }
    }

    /**
     * 在扩展属性列表中查找指定字段的扩展属性
     */
    private IObjectFieldExtra findFieldExtra(List<IObjectFieldExtra> extras, String fieldApiName) {
        if (CollectionUtils.empty(extras)) {
            return null;
        }
        return extras.stream()
                .filter(extra -> fieldApiName.equals(extra.getFieldApiName()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 创建敏感字段的合规性设置
     */
    private Map<String, String> createSensitiveComplianceSetting() {
        Map<String, String> complianceSetting = Maps.newHashMap();
        complianceSetting.put(COMPLIANCE_TYPE_GDPR, SECURITY_LEVEL_SENSITIVE);
        return complianceSetting;
    }

    /**
     * 创建普通字段的合规性设置
     */
    private Map<String, String> createOrdinaryComplianceSetting() {
        Map<String, String> complianceSetting = Maps.newHashMap();
        complianceSetting.put(COMPLIANCE_TYPE_GDPR, SECURITY_LEVEL_ORDINARY);
        return complianceSetting;
    }

    /**
     * 判断是否为敏感字段的合规性设置
     */
    private boolean isSensitiveComplianceSetting(Map<String, String> complianceSetting) {
        return CollectionUtils.notEmpty(complianceSetting) &&
                SECURITY_LEVEL_SENSITIVE.equals(complianceSetting.get(COMPLIANCE_TYPE_GDPR));
    }

    /**
     * 判断是否为普通字段的合规性设置
     */
    private boolean isOrdinaryComplianceSetting(Map<String, String> complianceSetting) {
        return CollectionUtils.notEmpty(complianceSetting) &&
                SECURITY_LEVEL_ORDINARY.equals(complianceSetting.get(COMPLIANCE_TYPE_GDPR));
    }

    /**
     * 从字段扩展属性中提取指定安全级别的字段
     *
     * @param fieldExtras 字段扩展属性
     * @param apiName 对象API名称
     * @param isSensitive 是否提取敏感字段(true为敏感字段，false为普通字段)
     * @return 指定安全级别的字段集合
     */
    private Set<String> extractFieldsBySecurityLevel(
            Map<String, List<IObjectFieldExtra>> fieldExtras, 
            String apiName, 
            boolean isSensitive) {
        
        Set<String> resultFields = Sets.newHashSet();
        
        if (CollectionUtils.empty(fieldExtras) || !fieldExtras.containsKey(apiName)) {
            return resultFields;
        }
        
        List<IObjectFieldExtra> extras = fieldExtras.get(apiName);
        if (CollectionUtils.empty(extras)) {
            return resultFields;
        }
        
        for (IObjectFieldExtra extra : extras) {
            Map<String, String> complianceSetting = extra.getComplianceSetting();
            String fieldApiName = extra.getFieldApiName();
            
            if (CollectionUtils.empty(complianceSetting) || StringUtils.isBlank(fieldApiName)) {
                continue;
            }
            
            boolean isSensitiveField = isSensitiveComplianceSetting(complianceSetting);
            if (isSensitive && isSensitiveField) {
                resultFields.add(fieldApiName);
            } else if (!isSensitive && isOrdinaryComplianceSetting(complianceSetting)) {
                resultFields.add(fieldApiName);
            }
        }
        
        return resultFields;
    }
    
    /**
     * 处理合规性配置
     *
     * @param user 用户
     * @param apiName 对象API名称
     * @param existingCompliance 已存在的合规性配置
     * @param sensitiveFields 敏感字段集合
     * @param ordinaryFields 普通字段集合
     * @param compliances 合规性配置列表
     * @return 处理后的合规性配置，如果不需要返回null
     */
    private GdprCompliance processComplianceConfiguration(
            User user,
            String apiName,
            Optional<GdprCompliance> existingCompliance,
            Set<String> sensitiveFields,
            Set<String> ordinaryFields,
            List<GdprCompliance> compliances) {
        if (existingCompliance.isPresent()) {
            return existingCompliance.get();
        }
        // 如果没有找到现有配置但有安全字段，则创建新配置
        if (CollectionUtils.empty(sensitiveFields) && CollectionUtils.empty(ordinaryFields)) {
            return null;
        }
        // 创建新的合规性配置
        GdprCompliance gdprCompliance = updateGdprPersonalField(user, apiName, null, null, compliances);
        if (Objects.isNull(gdprCompliance)) {
            return null;
        }
        // 添加到结果列表
        compliances.add(gdprCompliance);
        return gdprCompliance;
    }
}
