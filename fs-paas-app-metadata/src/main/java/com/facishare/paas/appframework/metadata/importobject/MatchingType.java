package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * create by z<PERSON><PERSON> on 2019/05/07
 */
public enum MatchingType {
    ID(1),
    NAME(2),
    UNIQUE_RULE(3),
    SPECIFIED_FIELD(4),
    ;

    private static final Map<Integer, MatchingType> typeMap;

    static {
        typeMap = Stream.of(values()).collect(Collectors.toMap(MatchingType::getType, Function.identity()));
    }

    @Getter(onMethod_ = {@JsonValue})
    private int type;

    MatchingType(int type) {
        this.type = type;
    }

    @<PERSON>son<PERSON>reator
    public static MatchingType of(int type) {
        MatchingType matchingType = typeMap.get(type);
        if (Objects.isNull(matchingType)) {
            throw new ValidateException("unSupport matchingType:" + type);
        }
        return matchingType;
    }
}
