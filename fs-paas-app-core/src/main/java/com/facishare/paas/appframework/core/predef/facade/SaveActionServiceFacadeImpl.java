package com.facishare.paas.appframework.core.predef.facade;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.DuplicateSearchValidationException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.handler.SaveActionHandler;
import com.facishare.paas.appframework.core.predef.handler.edit.EditActionHandler;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.facishare.paas.appframework.metadata.dto.ConvertRuleDataContainer;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchResult;
import com.facishare.paas.appframework.metadata.repository.model.MtConvertRule;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.api.IdGenerator;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2023/1/28.
 */
@Slf4j
@Service("saveActionServiceFacade")
public class SaveActionServiceFacadeImpl implements SaveActionServiceFacade {

    private static final Joiner VERTICAL_JOINER = Joiner.on("|").useForNull("");

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ApprovalFlowServiceFacade approvalFlowServiceFacade;
    @Autowired
    private RedissonService redissonService;
    @Autowired
    private InfraServiceFacade infraServiceFacade;

    @Override
    public List<IObjectData> getRelateDataForCreateWorkFlow(User user, IObjectDescribe objectDescribe, IObjectData objectData) {
        List<IObjectData> dataListForCreateWorkFlow = Lists.newArrayList();
        if (approvalFlowServiceFacade.isApprovalNotExist()
                && approvalFlowServiceFacade.needTriggerMasterApproval(objectDescribe)
                && isTriggerCreateApprovalFlow()) {
            IObjectData realMasterData = approvalFlowServiceFacade.getRealMasterObjectData(user, objectDescribe, objectData);
            List<IObjectDescribe> realDetailDescribes = serviceFacade.findDetailDescribesCreateWithMaster(user.getTenantId(),
                    realMasterData.getDescribeApiName());
            realDetailDescribes.forEach(x -> {
                try {
                    List<IObjectData> realDetailDataList = serviceFacade.findDetailObjectDataWithPage(user,
                            realMasterData.getDescribeApiName(), realMasterData.getId(), x, 1, 1000, null).getData();
                    dataListForCreateWorkFlow.addAll(realDetailDataList);
                } catch (Exception e) {
                    log.error("getDataListForCreateWorkFlow error,tenantId:{},apiName:{}", user.getTenantId(), x.getApiName(), e);
                }
            });
            dataListForCreateWorkFlow.add(realMasterData);
        }
        return dataListForCreateWorkFlow;
    }

    private boolean isTriggerCreateApprovalFlow() {
        return approvalFlowServiceFacade.getApprovalFlowTriggerType() == ApprovalFlowTriggerType.CREATE;
    }

    @Override
    public void updateDetailObjectDataLifeStatus(HandlerContext context, SaveActionHandler.Arg arg, ObjectLifeStatus lastLifeStatus,
                                                 boolean triggerApprovalFlowSuccess) {
        IObjectData realMasterData = null;
        if (approvalFlowServiceFacade.needTriggerMasterApproval(arg.getObjectDescribe())) {
            realMasterData = approvalFlowServiceFacade.getRealMasterObjectData(context.getUser(), arg.getObjectDescribe(), arg.objectData());
        }
        approvalFlowServiceFacade.updateDetailObjectDataLifeStatus(context.getInterfaceCode(), context.getUser(), lastLifeStatus,
                arg.objectData(), arg.detailObjectData(), arg.getDetailDescribeMap(), realMasterData, triggerApprovalFlowSuccess);
    }

    @Override
    public void batchValidateEditDataConstraintByFields(User user, EditActionHandler.Arg arg) {
        IObjectData masterData = ObjectDataExt.of(arg.getObjectData()).copy();
        //主对象
        serviceFacade.batchValidateDataConstraintByFields(user, Lists.newArrayList(masterData), arg.getObjectDescribe(), true);
        //新建的从对象
        Map<String, List<IObjectData>> detailAddDataListMap = ObjectDataExt.groupByDescribeApiName(ObjectDataDocument.ofDataList(arg.getDetailsToAdd()));
        for (Map.Entry<String, List<IObjectData>> detailAddData : detailAddDataListMap.entrySet()) {
            String detailDescribeApiName = detailAddData.getKey();
            IObjectDescribe detailDescribe = arg.getDetailDescribeMap().get(detailDescribeApiName);
            List<IObjectData> detailAddDataList = detailAddData.getValue();
            List<IObjectData> copyDetailAddDataList = ObjectDataExt.copyList(detailAddDataList);
            serviceFacade.batchValidateDataConstraintByFields(user, copyDetailAddDataList, detailDescribe, false);
        }
        //编辑的从对象
        Map<String, List<IObjectData>> detailsUpdateDataListMap = ObjectDataExt.groupByDescribeApiName(ObjectDataDocument.ofDataList(arg.getDetailsToUpdate()));
        for (Map.Entry<String, List<IObjectData>> detailUpdateData : detailsUpdateDataListMap.entrySet()) {
            String detailDescribeApiName = detailUpdateData.getKey();
            IObjectDescribe detailDescribe = arg.getDetailDescribeMap().get(detailDescribeApiName);
            List<IObjectData> detailUpdateDataList = detailUpdateData.getValue();
            List<IObjectData> copyDetailUpdateDataList = ObjectDataExt.copyList(detailUpdateDataList);
            serviceFacade.batchValidateDataConstraintByFields(user, copyDetailUpdateDataList, detailDescribe, true);
        }
    }

    @Override
    public void buildDuplicateImportAddError(User user, List<DuplicateSearchResult.DuplicateData> duplicateData, IObjectDescribe objectDescribe, boolean isFromImport) {
        if (!isFromImport) {
            return;
        }
        List<String> dataIds = CollectionUtils.nullToEmpty(duplicateData).stream()
                .map(DuplicateSearchResult.DuplicateData::getDataIds)
                .filter(CollectionUtils::notEmpty)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.empty(dataIds)) {
            return;
        }
        Map<String, String> nameByIds = serviceFacade.findNameByIds(user, objectDescribe.getApiName(), dataIds);
        StringBuilder duplicateError = new StringBuilder();
        duplicateError.append(I18NExt.getOrDefault(I18NKey.IMPORT_ADD_EXIST_DUPLICATE, "导入触发新建查重，存在重复数据")).append(":");// ignoreI18n
        for (DuplicateSearchResult.DuplicateData data : duplicateData) {
            List<String> duplicateDataIds = data.getDataIds();
            if (CollectionUtils.empty(duplicateDataIds)) {
                continue;
            }
            for (String duplicateDataId : duplicateDataIds) {
                String name = nameByIds.get(duplicateDataId);
                if (Objects.nonNull(name)) {
                    duplicateError.append(name).append("(").append(duplicateDataId).append(")").append(",");
                } else {
                    duplicateError.append("(").append(duplicateDataId).append(")").append(",");
                }
            }
            duplicateError.append(I18NExt.getOrDefault(I18NKey.IMPORT_ADD_MATCH_RULE, "匹配规则")).append(":").append(data.getRuleLabel()).append(";");// ignoreI18n
        }
        throw new DuplicateSearchValidationException(duplicateError.toString());
    }

    @Override
    public void checkIfUpdateKeyLocked(String seriesId, String dataId, String objectApiName, User user) {
        String updateKey = buildUpdateKey(seriesId, dataId, objectApiName, user);
        if (Strings.isNullOrEmpty(updateKey)) {
            return;
        }
        if (redissonService.isLocked(updateKey)) {
            throw new ValidateException(I18N.text(I18NKey.DATA_EXPIRED));
        }
    }

    @Override
    public void tryLockWithUpdateKey(BaseObjectSaveAction.Arg arg, String objectApiName, User user) {
        if (!arg.hasOriginalData()) {
            return;
        }
        String updateKey = buildUpdateKey(arg.getSeriesId(), arg.getObjectData().getId(), objectApiName, user);
        if (Strings.isNullOrEmpty(updateKey)) {
            return;
        }
        try {
            redissonService.tryLock(0, 300, TimeUnit.SECONDS, updateKey);
        } catch (Exception e) {
            log.warn("tryLockWithUpdateKey error,updateKey:{} ", updateKey, e);
        }
    }

    private String buildUpdateKey(String seriesId, String dataId, String objectApiName, User user) {
        if (Strings.isNullOrEmpty(seriesId)) {
            return null;
        }
        return VERTICAL_JOINER.join("UK", seriesId, dataId, objectApiName, user.getTenantId());
    }

    @Override
    public ConvertRuleDataContainer buildConvertRuleDataContainer(User user, BaseObjectSaveAction.OptionInfo optionInfo) {
        String fromApiName = optionInfo.getFromApiName();
        String fromId = optionInfo.getFromId();
        List<String> fromMasterIds = optionInfo.getFromMasterIds();
        String convertRuleApiName = optionInfo.getConvertRule();
        if (StringUtils.isAnyEmpty(fromApiName, convertRuleApiName) || CollectionUtils.empty(fromMasterIds)) {
            log.warn("initConvertRuleAndSourceObjectData parameter error , optionInfo : {}", JSON.toJSONString(optionInfo));
            return null;
        }
        List<MtConvertRule> convertRuleList = infraServiceFacade.findConvertRuleInInternalObjByApiName(user, convertRuleApiName, true);
        if (CollectionUtils.empty(convertRuleList)) {
            throw new ValidateException(I18NExt.text(I18NKey.CONVERT_RULE_DISABLE_OR_DELETE));
        }
        List<String> sourceApiNames = convertRuleList.stream()
                .map(MtConvertRule::getSourceObjectDescribeApiName)
                .distinct().collect(Collectors.toList());
        Map<String, IObjectDescribe> sourceObjectDescribes = serviceFacade.findObjectsWithoutCopy(user.getTenantId(), sourceApiNames);
        List<IObjectDescribe> detailObjectDescribes = sourceObjectDescribes.values()
                .stream().filter(describe -> !StringUtils.equals(fromApiName, describe.getApiName()))
                .collect(Collectors.toList());
        return ConvertRuleDataContainer.builder()
                .sourceId(fromId)
                .eventId(IdGenerator.get())
                .fromMasterIds(fromMasterIds)
                .fromDetails(optionInfo.getFromDetails())
                .objectDescribe(sourceObjectDescribes.get(fromApiName))
                .detailDescribe(detailObjectDescribes)
                .convertRuleList(convertRuleList)
                .build();
    }
}
