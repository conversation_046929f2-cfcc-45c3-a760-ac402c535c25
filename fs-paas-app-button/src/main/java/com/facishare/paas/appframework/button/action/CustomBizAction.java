package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.button.dto.CustomBizButtonExecutor;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.rest.InnerHeaders;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2021/5/17 18:39
 */
@Slf4j
@Component
public class CustomBizAction implements ActionExecutor {
    @Autowired
    CustomBizActionProxy customBizActionProxy;

    @Override
    public ActionExecutorType getType() {
        return ActionExecutorType.CUSTOM_BIZ;
    }

    public static Map<String, String> urlMap;

    static {
        ConfigFactory.getConfig("fs-paas-custom-biz-action", iConfig -> {
            log.warn("fs-paas-custom-biz-action content ={}", iConfig.getString());
            urlMap = JacksonUtils.fromJson(iConfig.getString(), Map.class);
        });
    }

    @Override
    public ButtonExecutor.Result invoke(ButtonExecutor.Arg arg, ActionExecutorContext context) {
        if (context == null) {
            log.warn("custom action context is null");
            return ButtonExecutor.Result.empty();
        }

        Map<String, String> headers = RestUtils.buildHeaders(context.getUser());
        //生成一个新的requestId，用于幂等
        headers.put(InnerHeaders.POST_ID, UUID.randomUUID().toString());

        if (StringUtils.isBlank(context.getAction().getBizKey())) {
            log.warn("custom action biz key is null,user={}", context.getUser());
            return ButtonExecutor.Result.empty();
        }

        String url = urlMap.get(context.getAction().getBizKey());
        if (StringUtils.isBlank(url)) {
            log.warn("custom action url is null,user={}", context.getUser());
            return ButtonExecutor.Result.empty();
        }

        if (StringUtils.isBlank(arg.getButtonApiName())) {
            arg.setButtonApiName(context.getButton().getApiName());
        }

        CustomBizButtonExecutor.Arg buttonArg = CustomBizButtonExecutor.Arg.of(arg.getDescribeApiName(),
                arg.getObjectDataId(), arg.getDataIds(), arg.getButtonApiName(), arg.getArgs(), arg.getObjectData(),
                arg.toDetails(), context.getAction().getBizKey(), context.getAction().getStage(), arg.getActionParams());

        CustomBizActionProxy.ButtonActionResponse response = customBizActionProxy.startCustomBizButton(buttonArg, url, headers);
        if (response.getErrCode() != 0) {
            throw new MetaDataBusinessException(response.getErrMessage());
        }
        return ButtonExecutor.Result.builder()
                .objectData(response.getResult().toObjectData())
                .details(response.getResult().toDetails())
                .targetDescribeApiName(response.getResult().getTargetDescribeApiName())
                .hasReturnValue(response.getResult().isHasReturnValue())
                .returnValue(response.getResult().getReturnValue())
                .returnType(response.getResult().getReturnType())
                .block(response.getResult().isBlock())
                .build();
    }
}
