/**
 * Facishare PAAS Organization
 * This is PAAS organization service project
 *
 * OpenAPI spec version: v1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


package com.facishare.crm.privilege.swagger.model;

import java.util.Objects;
import com.facishare.crm.privilege.swagger.model.PageInfo;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;


/**
 * FilterGroupArg
 */
@javax.annotation.Generated(value = "class io.swagger.codegen.languages.JavaClientCodegen", date = "2017-07-25T12:04:51.098+08:00")
public class FilterGroupArg   {
  @SerializedName("tenantId")
  private String tenantId = null;

  @SerializedName("appId")
  private String appId = null;

  @SerializedName("userId")
  private String userId = null;

  @SerializedName("groupIdList")
  private List<String> groupIdList = new ArrayList<String>();

  @SerializedName("isFilterByUser")
  private Boolean isFilterByUser = false;

  @SerializedName("isPublic")
  private Boolean isPublic = false;

  @SerializedName("page")
  private PageInfo page = null;

  public FilterGroupArg tenantId(String tenantId) {
    this.tenantId = tenantId;
    return this;
  }

   /**
   * Get tenantId
   * @return tenantId
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getTenantId() {
    return tenantId;
  }

  public void setTenantId(String tenantId) {
    this.tenantId = tenantId;
  }

  public FilterGroupArg appId(String appId) {
    this.appId = appId;
    return this;
  }

   /**
   * Get appId
   * @return appId
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getAppId() {
    return appId;
  }

  public void setAppId(String appId) {
    this.appId = appId;
  }

  public FilterGroupArg userId(String userId) {
    this.userId = userId;
    return this;
  }

   /**
   * Get userId
   * @return userId
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getUserId() {
    return userId;
  }

  public void setUserId(String userId) {
    this.userId = userId;
  }

  public FilterGroupArg groupIdList(List<String> groupIdList) {
    this.groupIdList = groupIdList;
    return this;
  }

  public FilterGroupArg addGroupIdListItem(String groupIdListItem) {
    this.groupIdList.add(groupIdListItem);
    return this;
  }

   /**
   * Get groupIdList
   * @return groupIdList
  **/
  @ApiModelProperty(example = "null", value = "")
  public List<String> getGroupIdList() {
    return groupIdList;
  }

  public void setGroupIdList(List<String> groupIdList) {
    this.groupIdList = groupIdList;
  }

  public FilterGroupArg isFilterByUser(Boolean isFilterByUser) {
    this.isFilterByUser = isFilterByUser;
    return this;
  }

   /**
   * Get isFilterByUser
   * @return isFilterByUser
  **/
  @ApiModelProperty(example = "null", value = "")
  public Boolean getIsFilterByUser() {
    return isFilterByUser;
  }

  public void setIsFilterByUser(Boolean isFilterByUser) {
    this.isFilterByUser = isFilterByUser;
  }

  public FilterGroupArg isPublic(Boolean isPublic) {
    this.isPublic = isPublic;
    return this;
  }

   /**
   * Get isPublic
   * @return isPublic
  **/
  @ApiModelProperty(example = "null", value = "")
  public Boolean getIsPublic() {
    return isPublic;
  }

  public void setIsPublic(Boolean isPublic) {
    this.isPublic = isPublic;
  }

  public FilterGroupArg page(PageInfo page) {
    this.page = page;
    return this;
  }

   /**
   * Get page
   * @return page
  **/
  @ApiModelProperty(example = "null", value = "")
  public PageInfo getPage() {
    return page;
  }

  public void setPage(PageInfo page) {
    this.page = page;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FilterGroupArg filterGroupArg = (FilterGroupArg) o;
    return Objects.equals(this.tenantId, filterGroupArg.tenantId) &&
        Objects.equals(this.appId, filterGroupArg.appId) &&
        Objects.equals(this.userId, filterGroupArg.userId) &&
        Objects.equals(this.groupIdList, filterGroupArg.groupIdList) &&
        Objects.equals(this.isFilterByUser, filterGroupArg.isFilterByUser) &&
        Objects.equals(this.isPublic, filterGroupArg.isPublic) &&
        Objects.equals(this.page, filterGroupArg.page);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tenantId, appId, userId, groupIdList, isFilterByUser, isPublic, page);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FilterGroupArg {\n");
    
    sb.append("    tenantId: ").append(toIndentedString(tenantId)).append("\n");
    sb.append("    appId: ").append(toIndentedString(appId)).append("\n");
    sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
    sb.append("    groupIdList: ").append(toIndentedString(groupIdList)).append("\n");
    sb.append("    isFilterByUser: ").append(toIndentedString(isFilterByUser)).append("\n");
    sb.append("    isPublic: ").append(toIndentedString(isPublic)).append("\n");
    sb.append("    page: ").append(toIndentedString(page)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

