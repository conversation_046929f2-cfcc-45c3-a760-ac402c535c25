package com.facishare.paas.appframework.common.service.dto;

import com.facishare.organization.api.model.department.DepartmentNameLanguage;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ConvertUtils;
import com.facishare.social.department.model.DepartmentDto;
import com.facishare.social.department.model.DepartmentDto.DepartmentRecordType;
import com.facishare.social.department.model.NameLanguage;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * create by zhao<PERSON> on 2021/01/13
 */
@Data
public class DeptInfo {

    public static String TYPE_DEPT = "dept";
    public static String TYPE_ORG = "org";

    private String userId;
    private String userName;
    private String deptId;
    private String deptName;
    private String deptCode;
    /**
     * 部门类型：
     * 组织 org
     * 部门 dept
     */
    private String deptType;
    private String status;

    public boolean disabled() {
        return !DepartmentDto.Status.NORMAL.getValue().equals(status);
    }


    public static DeptInfo convertToDeptInfo(DepartmentDto departmentDto) {
        DeptInfo deptInfo = new DeptInfo();
        deptInfo.setDeptId(departmentDto.getId());
        deptInfo.setDeptName(getDeptName(departmentDto));
        deptInfo.setDeptCode(departmentDto.getDeptCode());
        deptInfo.setStatus((String) departmentDto.getStatus());
        if (Objects.equals(departmentDto.getRecordType(), DepartmentRecordType.department.getType())) {
            deptInfo.setDeptType(DeptInfo.TYPE_DEPT);
        } else if (Objects.equals(departmentDto.getRecordType(), DepartmentRecordType.organization.getType())) {
            deptInfo.setDeptType(DeptInfo.TYPE_ORG);
        }
        return deptInfo;
    }

    private static String getDeptName(DepartmentDto dept) {
//        NameLanguage departmentName = dept.getNameLanguage();
        NameLanguage departmentName = NameLanguage.getNameLanguage(dept);
        if (Objects.isNull(departmentName)) {
            return dept.getName();
        }
        String language = I18N.getContext().getLanguage();
        String deptName;
        switch (language) {
            case I18N.EN:
                deptName = departmentName.getEn();
                break;
            case I18N.ZH_CN:
                deptName = departmentName.getZh_CN();
                break;
            case I18N.ZH_TW:
                deptName = departmentName.getZh_TW();
                break;
            default:
                deptName = dept.getName();
        }
        if (StringUtils.isBlank(deptName)) {
            return dept.getName();
        }
        return deptName;
    }

    /**
     * 部门信息（不含部门编码）
     *
     * @param departmentDtoList 接口返回部门信息
     * @return 转换的部门信息
     */
    public static List<DeptInfo> convertToDeptInfo(List<com.facishare.organization.api.model.department.DepartmentDto> departmentDtoList) {

        if (CollectionUtils.empty(departmentDtoList)) {
            return Lists.newArrayList();
        }
        List<DeptInfo> deptList = Lists.newArrayList();
        for (com.facishare.organization.api.model.department.DepartmentDto departmentDto : departmentDtoList) {
            DeptInfo deptInfo = new DeptInfo();
            deptInfo.setDeptId(ConvertUtils.integerToString(departmentDto.getDepartmentId()));
            deptInfo.setDeptName(getDeptName(departmentDto));
            // com.facishare.organization.api.model.department.DepartmentDto 没有部门编码（dept_code）
            // deptInfo.setDeptCode(departmentDto.getDeptCode());
            if (Objects.equals(departmentDto.getRecordType(), com.facishare.organization.api.model.department.DepartmentDto.RECORD_TYPE_DEPARTMENT)) {
                deptInfo.setDeptType(DeptInfo.TYPE_DEPT);
            } else if (Objects.equals(departmentDto.getRecordType(), com.facishare.organization.api.model.department.DepartmentDto.RECORD_TYPE_ORGANIZATION)) {
                deptInfo.setDeptType(DeptInfo.TYPE_ORG);
            }
            deptList.add(deptInfo);
        }


        return deptList;
    }

    private static String getDeptName(com.facishare.organization.api.model.department.DepartmentDto dept) {
        DepartmentNameLanguage departmentName = dept.getDepartmentNameLanguage();
        if (Objects.isNull(departmentName)) {
            return dept.getName();
        }
        String language = I18N.getContext().getLanguage();
        String deptName;
        switch (language) {
            case I18N.EN:
                deptName = departmentName.getEn();
                break;
            case I18N.ZH_CN:
                deptName = departmentName.getZh_CN();
                break;
            case I18N.ZH_TW:
                deptName = departmentName.getZh_TW();
                break;
            default:
                deptName = dept.getName();
        }
        if (StringUtils.isBlank(deptName)) {
            return dept.getName();
        }
        return deptName;
    }
}
