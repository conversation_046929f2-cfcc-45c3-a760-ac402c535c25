package com.facishare.paas.appframework.core.model;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

/**
 *
 * Created by liyiguang on 2017/7/30.
 */
@Component
public class ServiceBeanPostProcessor implements BeanPostProcessor {

    @Autowired
    private ServiceRegistry serviceRegistry;

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        serviceRegistry.register(bean);
        return bean;
    }
}
