package com.facishare.paas.appframework.function.dto;

import com.facishare.function.Function;
import lombok.Builder;
import lombok.Data;

/**
 * Created by fengjy in 2020/1/14 15:57
 */
public interface DebugFunction {

    @Data
    @Builder
    class Arg{
        Function function;
        String objectAPIName;
        String objectId;
        String requestId;
    }

    @Data
    class Result{
        private boolean success;

        private Object result;
        private String log;
        private String compileError;
        private String runtimeError;
        private int status;

        public static final int RUNNING = 1;
        public static final int END = 2;
    }
}
