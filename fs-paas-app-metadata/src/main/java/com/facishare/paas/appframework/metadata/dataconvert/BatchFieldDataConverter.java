package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.List;

/**
 * Created by zhouwr on 2019/4/1
 */
public interface BatchFieldDataConverter {

    /**
     * 转换器支持的字段类型
     *
     * @return
     */
    List<String> getSupportedFieldTypes();

    /**
     * 将指定字段的数据转成前端展现的格式
     *
     * @param dataList      数据集合
     * @param fieldDescribe 字段描述
     * @param user          用户信息
     * @deprecated This feature will be removed,
     * use {@link #convertFieldData(List, IFieldDescribe, DataConvertContext)}
     */
    @Deprecated
    default void convertFieldData(List<IObjectData> dataList, IFieldDescribe fieldDescribe, User user) {
        convertFieldData(dataList, fieldDescribe, DataConvertContext.of(user));
    }

    // TODO: 2023/9/28 确定接口作用，判断是否需要处理多区域
    void convertFieldData(List<IObjectData> dataList, IFieldDescribe fieldDescribe, DataConvertContext context);

}
