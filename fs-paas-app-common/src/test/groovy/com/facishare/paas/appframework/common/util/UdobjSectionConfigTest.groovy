package com.facishare.paas.appframework.common.util

import com.facishare.paas.appframework.common.service.model.AIFormulaConfigDTO
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.github.autoconf.ConfigFactory
import com.github.autoconf.api.IConfig
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

/**
 * GenerateByAI
 * 测试内容描述：UdobjSectionConfig工具类的单元测试
 */
class UdobjSectionConfigTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试默认的AIFormulaConfigDTO配置
     */
    def "测试默认的AIFormulaConfigDTO配置"() {
        when: "获取AIFormulaConfigDTO"
        def config = UdobjSectionConfig.getAiFormulaConfigDTO()
        
        then: "应返回默认配置或已加载的配置"
        config != null
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试loadAiFormula方法通过IConfig加载AI公式配置
     */
    def "测试loadAiFormula方法"() {
        given: "模拟IConfig"
        def config = Mock(IConfig)
        config.getInt("fieldN", 50) >> 100
        config.getInt("optionN", 5) >> 10
        config.getDouble("temperature", 0.0) >> 0.5
        config.getInt("maxTokens", 2048) >> 4096
        config.get("model", "qwen-plus") >> "gpt-3.5-turbo"
        config.get("fieldTypeExplain") >> "TEXT:文本,NUMBER:数字"
        config.get("noSupportApis") >> "api1,api2"
        config.get("noSupportTypes") >> "type1,type2"
        config.get("noSupportGlobsVars") >> "var1,var2"
        
        and: "模拟getAll方法"
        def allConfig = [
            "obj1.noSupportFields": "field1,field2",
            "obj2.noSupportFields": "field3,field4",
            "otherConfig": "value"
        ]
        config.getAll() >> allConfig
        
        and: "为包含.noSupportFields的键添加配置"
        config.get("obj1.noSupportFields") >> "field1,field2"
        config.get("obj2.noSupportFields") >> "field3,field4"
        
        when: "调用loadAiFormula方法"
        Whitebox.invokeMethod(UdobjSectionConfig, "loadAiFormula", config)
        def result = UdobjSectionConfig.getAiFormulaConfigDTO()
        
        then: "应更新为新的配置"
        result.fieldN == 100
        result.optionN == 10
        result.temperature == 0.5
        result.maxTokens == 4096
        result.model == "gpt-3.5-turbo"
        result.fieldTypeExplain == ["TEXT": "文本", "NUMBER": "数字"]
        result.noSupportApis == ["api1", "api2"] as Set
        result.noSupportTypes == ["type1", "type2"] as Set
        result.noSupportGlobsVars == ["var1", "var2"] as Set
        result.noSupportFields == [
            "obj1": ["field1", "field2"] as Set,
            "obj2": ["field3", "field4"] as Set
        ]
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试loadAiFormula方法在配置不全时的处理
     */
    def "测试loadAiFormula方法在配置不全时的处理"() {
        given: "保存之前的配置"
        def originalConfig = UdobjSectionConfig.getAiFormulaConfigDTO()
        
        and: "模拟IConfig，缺少必要的配置"
        def config = Mock(IConfig)
        config.getInt("fieldN", 50) >> 100
        config.getInt("optionN", 5) >> 10
        config.getDouble("temperature", 0.0) >> 0.5
        config.getInt("maxTokens", 2048) >> 4096
        config.get("model", "qwen-plus") >> "gpt-3.5-turbo"
        config.get("fieldTypeExplain") >> "TEXT:文本,NUMBER:数字"
        config.get("noSupportApis") >> ""  // 空值
        config.get("noSupportTypes") >> "" // 空值
        config.get("noSupportGlobsVars") >> "var1,var2"
        config.getAll() >> [:]
        
        when: "调用loadAiFormula方法"
        Whitebox.invokeMethod(UdobjSectionConfig, "loadAiFormula", config)
        
        then: "应保持原有配置不变"
        UdobjSectionConfig.getAiFormulaConfigDTO() == originalConfig
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试loadMap和loadSet私有方法
     */
    def "测试loadMap和loadSet私有方法"() {
        when: "使用loadMap解析字符串"
        def mapResult = Whitebox.invokeMethod(UdobjSectionConfig, "loadMap", "key1:value1,key2:value2")
        
        then: "应返回正确的Map"
        mapResult == ["key1": "value1", "key2": "value2"]
        
        when: "使用loadMap解析空字符串"
        mapResult = Whitebox.invokeMethod(UdobjSectionConfig, "loadMap", "")
        
        then: "应返回空Map"
        mapResult == [:]
        
        when: "使用loadSet解析字符串"
        def setResult = Whitebox.invokeMethod(UdobjSectionConfig, "loadSet", "value1,value2,value3")
        
        then: "应返回正确的Set"
        setResult == ["value1", "value2", "value3"] as Set
        
        when: "使用loadSet解析空字符串"
        setResult = Whitebox.invokeMethod(UdobjSectionConfig, "loadSet", "")
        
        then: "应返回空Set"
        setResult == [] as Set
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试loadMap从IConfig加载配置的方法
     */
    def "测试从IConfig加载Map配置"() {
        given: "模拟IConfig"
        def config = Mock(IConfig)
        def allConfig = [
            "obj1.keyword": "value1,value2",
            "obj2.keyword": "value3,value4",
            "other": "value"
        ]
        config.getAll() >> allConfig
        config.get("obj1.keyword") >> "value1,value2"
        config.get("obj2.keyword") >> "value3,value4"
        
        when: "调用loadMap方法"
        def result = Whitebox.invokeMethod(UdobjSectionConfig, "loadMap", config, ".keyword")
        
        then: "应返回正确的Map<String, Set<String>>"
        result == [
            "obj1": ["value1", "value2"] as Set,
            "obj2": ["value3", "value4"] as Set
        ]
    }
} 