package com.facishare.paas.appframework.core.predef.service.dto.option;

import com.facishare.paas.appframework.metadata.repository.model.MtOptionSet;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * create by z<PERSON><PERSON> on 2021/12/14
 */
public interface FindAllOptionSet {
    class Arg {

    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        private List<MtOptionSet> options;
    }
}
