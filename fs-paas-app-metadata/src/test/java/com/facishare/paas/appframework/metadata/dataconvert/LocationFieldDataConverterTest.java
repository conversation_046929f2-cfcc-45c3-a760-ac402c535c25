package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.appframework.metadata.ObjectImportConfig;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试LocationFieldDataConverter类的位置字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class LocationFieldDataConverterTest {

  @Mock
  private IObjectDataProxyService dataService;

  @Mock
  private SessionContext sessionContext;

  private LocationFieldDataConverter converter;
  private IFieldDescribe fieldDescribe;
  private IObjectData objectData;

  @BeforeEach
  void setUp() {
    converter = new LocationFieldDataConverter();
    fieldDescribe = createLocationFieldDescribe("location_field");
    
    // 设置转换器的属性
    converter.setFieldDescribe(fieldDescribe);
    converter.setDataService(dataService);
    
    // 设置SessionContext的默认行为
    lenient().when(sessionContext.getEId()).thenReturn(123456L);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法正常转换位置信息（非灰度模式）
   */
  @Test
  @DisplayName("测试convertFieldData - 正常位置转换（非灰度模式）")
  void testConvertFieldData_NormalLocationConversion() throws Exception {
    // 准备测试数据 - 经度#%$纬度#%$地址格式
    String locationValue = "116.397428#%$39.90923#%$北京市朝阳区";
    objectData = createObjectData("location_field", locationValue);
    converter.setObjectData(objectData);

    try (MockedStatic<ObjectImportConfig> mockedConfig = mockStatic(ObjectImportConfig.class)) {
      mockedConfig.when(() -> ObjectImportConfig.isGrayLocationField("123456"))
          .thenReturn(false);

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果 - 非灰度模式只返回地址部分
      assertEquals("北京市朝阳区", result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法灰度模式转换位置信息
   */
  @Test
  @DisplayName("测试convertFieldData - 灰度模式位置转换")
  void testConvertFieldData_GrayModeLocationConversion() throws Exception {
    // 准备测试数据
    String locationValue = "116.397428#%$39.90923#%$北京市朝阳区";
    objectData = createObjectData("location_field", locationValue);
    converter.setObjectData(objectData);

    try (MockedStatic<ObjectImportConfig> mockedConfig = mockStatic(ObjectImportConfig.class)) {
      mockedConfig.when(() -> ObjectImportConfig.isGrayLocationField("123456"))
          .thenReturn(true);

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果 - 灰度模式返回所有部分用|分隔
      assertEquals("116.397428|39.90923|北京市朝阳区", result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法灰度模式处理0坐标
   */
  @Test
  @DisplayName("测试convertFieldData - 灰度模式处理0坐标")
  void testConvertFieldData_GrayModeZeroCoordinates() throws Exception {
    // 准备测试数据 - 经纬度都为0的情况
    String locationValue = "0#%$0#%$北京市朝阳区";
    objectData = createObjectData("location_field", locationValue);
    converter.setObjectData(objectData);

    try (MockedStatic<ObjectImportConfig> mockedConfig = mockStatic(ObjectImportConfig.class)) {
      mockedConfig.when(() -> ObjectImportConfig.isGrayLocationField("123456"))
          .thenReturn(true);

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果 - 经纬度都为0时只返回地址
      assertEquals("北京市朝阳区", result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理null值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null值")
  void testConvertFieldData_HandlesNullValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("location_field", null);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理空字符串
   */
  @Test
  @DisplayName("测试convertFieldData - 处理空字符串")
  void testConvertFieldData_HandlesEmptyString() throws Exception {
    // 准备测试数据
    objectData = createObjectData("location_field", "");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理空白字符串
   */
  @Test
  @DisplayName("测试convertFieldData - 处理空白字符串")
  void testConvertFieldData_HandlesBlankString() throws Exception {
    // 准备测试数据
    objectData = createObjectData("location_field", "   ");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理格式不完整的位置信息
   */
  @Test
  @DisplayName("测试convertFieldData - 格式不完整的位置信息")
  void testConvertFieldData_IncompleteLocationFormat() throws Exception {
    // 准备测试数据 - 只有两个部分的位置信息
    String locationValue = "116.397428#%$39.90923";
    objectData = createObjectData("location_field", locationValue);
    converter.setObjectData(objectData);

    try (MockedStatic<ObjectImportConfig> mockedConfig = mockStatic(ObjectImportConfig.class)) {
      mockedConfig.when(() -> ObjectImportConfig.isGrayLocationField("123456"))
          .thenReturn(false);

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果 - 格式不完整时返回空字符串
      assertEquals("", result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法灰度模式处理格式不完整的位置信息
   */
  @Test
  @DisplayName("测试convertFieldData - 灰度模式格式不完整的位置信息")
  void testConvertFieldData_GrayModeIncompleteLocationFormat() throws Exception {
    // 准备测试数据 - 只有两个部分的位置信息
    String locationValue = "116.397428#%$39.90923";
    objectData = createObjectData("location_field", locationValue);
    converter.setObjectData(objectData);

    try (MockedStatic<ObjectImportConfig> mockedConfig = mockStatic(ObjectImportConfig.class)) {
      mockedConfig.when(() -> ObjectImportConfig.isGrayLocationField("123456"))
          .thenReturn(true);

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果 - 灰度模式返回所有部分
      assertEquals("116.397428|39.90923", result);
    }
  }

  /**
   * 提供不同格式的位置数据测试
   */
  private static Stream<Arguments> provideLocationData() {
    return Stream.of(
        Arguments.of("116.397428#%$39.90923#%$北京市朝阳区", false, "北京市朝阳区"),
        Arguments.of("116.397428#%$39.90923#%$北京市朝阳区", true, "116.397428|39.90923|北京市朝阳区"),
        Arguments.of("0#%$0#%$北京市朝阳区", true, "北京市朝阳区"),
        Arguments.of("116.397428#%$39.90923", false, ""),
        Arguments.of("116.397428#%$39.90923", true, "116.397428|39.90923"),
        Arguments.of("invalid_format", false, ""),
        Arguments.of("invalid_format", true, "invalid_format"),
        Arguments.of("", false, ""),
        Arguments.of("", true, "")
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理不同格式的位置数据
   */
  @ParameterizedTest
  @MethodSource("provideLocationData")
  @DisplayName("测试convertFieldData - 不同格式的位置数据")
  void testConvertFieldData_DifferentLocationFormats(String locationValue, boolean isGrayMode, String expectedResult) throws Exception {
    // 准备测试数据
    objectData = createObjectData("location_field", locationValue);
    converter.setObjectData(objectData);

    try (MockedStatic<ObjectImportConfig> mockedConfig = mockStatic(ObjectImportConfig.class)) {
      mockedConfig.when(() -> ObjectImportConfig.isGrayLocationField("123456"))
          .thenReturn(isGrayMode);

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果
      assertEquals(expectedResult, result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理字段不存在的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 字段不存在")
  void testConvertFieldData_FieldNotExists() throws Exception {
    // 准备测试数据 - 创建不包含目标字段的数据
    objectData = createObjectData("other_field", "116.397428#%$39.90923#%$北京市朝阳区");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 字段不存在时应该返回空字符串
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理特殊字符
   */
  @Test
  @DisplayName("测试convertFieldData - 处理特殊字符")
  void testConvertFieldData_SpecialCharacters() throws Exception {
    // 准备测试数据 - 包含特殊字符的地址
    String locationValue = "116.397428#%$39.90923#%$北京市朝阳区@#$%^&*()";
    objectData = createObjectData("location_field", locationValue);
    converter.setObjectData(objectData);

    try (MockedStatic<ObjectImportConfig> mockedConfig = mockStatic(ObjectImportConfig.class)) {
      mockedConfig.when(() -> ObjectImportConfig.isGrayLocationField("123456"))
          .thenReturn(false);

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果 - 特殊字符应该被保留
      assertEquals("北京市朝阳区@#$%^&*()", result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试转换器的基本属性设置
   */
  @Test
  @DisplayName("测试转换器属性设置")
  void testConverterProperties() {
    // 验证属性设置
    assertEquals(fieldDescribe, converter.getFieldDescribe());
    assertEquals(dataService, converter.getDataService());
  }

  /**
   * 创建测试用的位置字段描述
   */
  private IFieldDescribe createLocationFieldDescribe(String apiName) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试位置字段");
    fieldMap.put("type", IFieldType.LOCATION);
    return FieldDescribeFactory.newInstance(fieldMap);
  }

  /**
   * 创建测试用的对象数据
   */
  private IObjectData createObjectData(String fieldName, Object value) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldName, value);
    return new ObjectData(dataMap);
  }
}
