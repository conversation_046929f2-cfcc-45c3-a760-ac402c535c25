package com.facishare.paas.appframework.core.predef.listener.fxt;

import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ActionListener;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.prm.PartnerCoreService;
import com.facishare.paas.appframework.prm.model.IPartnerData;
import com.facishare.paas.appframework.prm.model.OutResources;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 访销通 新建动作监听
 * 基于 代理通动作字段
 */
@Component
public class FxtAddActionListener implements ActionListener<BaseObjectSaveAction.Arg, BaseObjectSaveAction.Result> {

    @Autowired
    PartnerCoreService partnerCoreService;

    @Override
    public void before(ActionContext context, BaseObjectSaveAction.Arg arg) {
        User user = context.getUser();
        String partnerId = partnerCoreService.getPartnerId(user);
        fillDefaultValue(user, arg.getObjectData(), partnerId);
        arg.getDetails().forEach((apiName, objectDataList) ->
                objectDataList.forEach(objectData -> fillDefaultValue(user, objectData, partnerId)));
    }

    private void fillDefaultValue(User user, ObjectDataDocument objectData, String partnerId) {
        objectData.put(IObjectData.OWNER, Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
        objectData.put(IPartnerData.OUT_RESOURCES, OutResources.FXT.getValue());
        objectData.put(IPartnerData.PARTNER_ID, partnerId);
    }

    @Override
    public void after(ActionContext context, BaseObjectSaveAction.Arg arg, BaseObjectSaveAction.Result result) {

    }
}
