package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.RichTextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试RichTextFieldDataConverter类的富文本字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class RichTextFieldDataConverterTest {

  @Mock
  private IObjectDataProxyService dataService;

  @Mock
  private SessionContext sessionContext;

  private RichTextFieldDataConverter converter;
  private IFieldDescribe fieldDescribe;
  private IObjectData objectData;

  @BeforeEach
  void setUp() {
    converter = new RichTextFieldDataConverter();
    fieldDescribe = createRichTextFieldDescribe("rich_text_field");
    
    // 设置转换器的属性
    converter.setFieldDescribe(fieldDescribe);
    converter.setDataService(dataService);
    
    // 设置SessionContext的默认行为
    lenient().when(sessionContext.getEId()).thenReturn(123456L);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理可处理的富文本
   */
  @Test
  @DisplayName("测试convertFieldData - 可处理的富文本")
  void testConvertFieldData_ProcessableRichText() throws Exception {
    // 准备测试数据
    String richTextContent = "<p>富文本内容</p>";
    String abstractContent = "富文本内容";
    objectData = createObjectData("rich_text_field", richTextContent);
    objectData.set("rich_text_field__o", abstractContent);
    converter.setObjectData(objectData);

    try (MockedStatic<RichTextExt> mockedRichTextExt = mockStatic(RichTextExt.class);
         MockedStatic<ObjectDataExt> mockedObjectDataExt = mockStatic(ObjectDataExt.class)) {
      
      mockedRichTextExt.when(() -> RichTextExt.isProcessableRichText(fieldDescribe))
          .thenReturn(true);
      mockedRichTextExt.when(() -> RichTextExt.getRichTextAbstractName("rich_text_field"))
          .thenReturn("rich_text_field__o");
      
      ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);
      mockedObjectDataExt.when(() -> ObjectDataExt.of(objectData))
          .thenReturn(mockObjectDataExt);
      when(mockObjectDataExt.get("rich_text_field__o", String.class))
          .thenReturn(abstractContent);

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果 - 应该返回抽象内容
      assertEquals(abstractContent, result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理协作富文本（灰度模式）
   */
  @Test
  @DisplayName("测试convertFieldData - 协作富文本（灰度模式）")
  void testConvertFieldData_CooperativeRichTextGrayMode() throws Exception {
    // 准备测试数据
    String richTextContent = "<p>协作富文本内容</p>";
    String abstractContent = "协作富文本内容";
    objectData = createObjectData("rich_text_field", richTextContent);
    objectData.set("rich_text_field__o", abstractContent);
    converter.setObjectData(objectData);

    try (MockedStatic<RichTextExt> mockedRichTextExt = mockStatic(RichTextExt.class);
         MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class);
         MockedStatic<ObjectDataExt> mockedObjectDataExt = mockStatic(ObjectDataExt.class)) {
      
      mockedRichTextExt.when(() -> RichTextExt.isProcessableRichText(fieldDescribe))
          .thenReturn(false);
      mockedConfig.when(() -> AppFrameworkConfig.cooperativeRichTextGray("123456", "rich_text_field"))
          .thenReturn(true);
      mockedRichTextExt.when(() -> RichTextExt.isProcessableCooperativeRichText(fieldDescribe))
          .thenReturn(true);
      mockedRichTextExt.when(() -> RichTextExt.getRichTextAbstractName("rich_text_field"))
          .thenReturn("rich_text_field__o");
      
      ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);
      mockedObjectDataExt.when(() -> ObjectDataExt.of(objectData))
          .thenReturn(mockObjectDataExt);
      when(mockObjectDataExt.get("rich_text_field__o", String.class))
          .thenReturn(abstractContent);

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果 - 应该返回抽象内容
      assertEquals(abstractContent, result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理普通富文本
   */
  @Test
  @DisplayName("测试convertFieldData - 普通富文本")
  void testConvertFieldData_NormalRichText() throws Exception {
    // 准备测试数据
    String richTextContent = "<p>普通富文本内容</p>";
    objectData = createObjectData("rich_text_field", richTextContent);
    converter.setObjectData(objectData);

    try (MockedStatic<RichTextExt> mockedRichTextExt = mockStatic(RichTextExt.class);
         MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
      
      mockedRichTextExt.when(() -> RichTextExt.isProcessableRichText(fieldDescribe))
          .thenReturn(false);
      mockedConfig.when(() -> AppFrameworkConfig.cooperativeRichTextGray("123456", "rich_text_field"))
          .thenReturn(false);

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果 - 应该返回原始内容
      assertEquals(richTextContent, result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理null值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null值")
  void testConvertFieldData_HandlesNullValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("rich_text_field", null);
    converter.setObjectData(objectData);

    try (MockedStatic<RichTextExt> mockedRichTextExt = mockStatic(RichTextExt.class);
         MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
      
      mockedRichTextExt.when(() -> RichTextExt.isProcessableRichText(fieldDescribe))
          .thenReturn(false);
      mockedConfig.when(() -> AppFrameworkConfig.cooperativeRichTextGray("123456", "rich_text_field"))
          .thenReturn(false);

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果 - 应该返回null
      assertNull(result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理空字符串
   */
  @Test
  @DisplayName("测试convertFieldData - 处理空字符串")
  void testConvertFieldData_HandlesEmptyString() throws Exception {
    // 准备测试数据
    objectData = createObjectData("rich_text_field", "");
    converter.setObjectData(objectData);

    try (MockedStatic<RichTextExt> mockedRichTextExt = mockStatic(RichTextExt.class);
         MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
      
      mockedRichTextExt.when(() -> RichTextExt.isProcessableRichText(fieldDescribe))
          .thenReturn(false);
      mockedConfig.when(() -> AppFrameworkConfig.cooperativeRichTextGray("123456", "rich_text_field"))
          .thenReturn(false);

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果 - 应该返回空字符串
      assertEquals("", result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理字段不存在的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 字段不存在")
  void testConvertFieldData_FieldNotExists() throws Exception {
    // 准备测试数据 - 创建不包含目标字段的数据
    objectData = createObjectData("other_field", "<p>其他内容</p>");
    converter.setObjectData(objectData);

    try (MockedStatic<RichTextExt> mockedRichTextExt = mockStatic(RichTextExt.class);
         MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
      
      mockedRichTextExt.when(() -> RichTextExt.isProcessableRichText(fieldDescribe))
          .thenReturn(false);
      mockedConfig.when(() -> AppFrameworkConfig.cooperativeRichTextGray("123456", "rich_text_field"))
          .thenReturn(false);

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果 - 字段不存在时应该返回null
      assertNull(result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理协作富文本（非灰度模式）
   */
  @Test
  @DisplayName("测试convertFieldData - 协作富文本（非灰度模式）")
  void testConvertFieldData_CooperativeRichTextNonGrayMode() throws Exception {
    // 准备测试数据
    String richTextContent = "<p>协作富文本内容</p>";
    objectData = createObjectData("rich_text_field", richTextContent);
    converter.setObjectData(objectData);

    try (MockedStatic<RichTextExt> mockedRichTextExt = mockStatic(RichTextExt.class);
         MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
      
      mockedRichTextExt.when(() -> RichTextExt.isProcessableRichText(fieldDescribe))
          .thenReturn(false);
      mockedConfig.when(() -> AppFrameworkConfig.cooperativeRichTextGray("123456", "rich_text_field"))
          .thenReturn(false);
      mockedRichTextExt.when(() -> RichTextExt.isProcessableCooperativeRichText(fieldDescribe))
          .thenReturn(true);

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果 - 非灰度模式应该返回原始内容
      assertEquals(richTextContent, result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试转换器的基本属性设置
   */
  @Test
  @DisplayName("测试转换器属性设置")
  void testConverterProperties() {
    // 验证属性设置
    assertEquals(fieldDescribe, converter.getFieldDescribe());
    assertEquals(dataService, converter.getDataService());
  }

  /**
   * 创建测试用的富文本字段描述
   */
  private IFieldDescribe createRichTextFieldDescribe(String apiName) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试富文本字段");
    fieldMap.put("type", IFieldType.RICH_TEXT);
    fieldMap.put("describe_api_name", apiName);
    return FieldDescribeFactory.newInstance(fieldMap);
  }

  /**
   * 创建测试用的对象数据
   */
  private IObjectData createObjectData(String fieldName, Object value) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldName, value);
    return new ObjectData(dataMap);
  }
}
