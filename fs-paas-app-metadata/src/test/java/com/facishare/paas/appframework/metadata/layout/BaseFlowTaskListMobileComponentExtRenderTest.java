package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.core.model.User;

import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.appframework.metadata.layout.component.BaseFlowTaskListComponentExt;
import com.facishare.paas.metadata.api.search.IOrderBy;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class BaseFlowTaskListMobileComponentExtRenderTest {

  @Mock
  private ObjectDescribeExt describeExt;

  @Mock
  private ObjectDescribeExt whatDescribeExt;

  @Mock
  private IObjectDescribe objectDescribe;

  @Mock
  private User user;

  @Mock
  private ILayout objectLayout;

  @Mock
  private BaseFlowTaskListComponentExt flowTaskListComponentExt;

  @Mock
  private BaseFlowTaskListComponentExt.FilterInfo filterInfo;

  @Mock
  private IOrderBy orderBy;

  private List<IOrderBy> mockOrders;
  private List<String> mockFilterFields;

  // 创建一个具体的测试实现类
  private static class TestBaseFlowTaskListMobileComponentExtRender extends BaseFlowTaskListMobileComponentExtRender {

    private final BaseFlowTaskListComponentExt componentExt;

    protected TestBaseFlowTaskListMobileComponentExtRender(ObjectDescribeExt describeExt, ObjectDescribeExt whatDescribeExt, 
                                                          User user, ILayout objectLayout, PageType pageType,
                                                          BaseFlowTaskListComponentExt componentExt) {
      super(describeExt, whatDescribeExt, user, objectLayout, pageType);
      this.componentExt = componentExt;
    }

    @Override
    protected BaseFlowTaskListComponentExt getFlowTaskListComponentExt() {
      return componentExt;
    }

    @Override
    protected void renderFieldList() {
      // 测试实现
    }
  }

  @BeforeEach
  void setUp() {
    mockOrders = Lists.newArrayList();
    mockOrders.add(orderBy);
    mockFilterFields = Lists.newArrayList("field1", "field2");

    // 设置基本的mock行为
    when(describeExt.getObjectDescribe()).thenReturn(objectDescribe);
    when(objectDescribe.getFieldDescribes()).thenReturn(Lists.newArrayList());
    when(objectDescribe.getApiName()).thenReturn("test_object");
    when(flowTaskListComponentExt.getOrders()).thenReturn(mockOrders);
    when(flowTaskListComponentExt.getFilterInfo()).thenReturn(filterInfo);
    when(filterInfo.empty()).thenReturn(false);
    when(filterInfo.getFields()).thenReturn(mockFilterFields);
    when(orderBy.getFieldName()).thenReturn("field1");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试BaseFlowTaskListMobileComponentExtRender构造函数正常场景
   */
  @Test
  @DisplayName("正常场景 - 构造BaseFlowTaskListMobileComponentExtRender对象")
  void testBaseFlowTaskListMobileComponentExtRenderConstructor_Success() {
    // 执行被测试方法
    TestBaseFlowTaskListMobileComponentExtRender render = new TestBaseFlowTaskListMobileComponentExtRender(
        describeExt, whatDescribeExt, user, objectLayout, PageType.Detail, flowTaskListComponentExt);

    // 验证结果
    assertNotNull(render);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试render方法正常场景
   */
  @Test
  @DisplayName("正常场景 - 执行基础流程任务列表移动端组件渲染")
  void testRender_Success() {
    // 执行被测试方法
    TestBaseFlowTaskListMobileComponentExtRender render = new TestBaseFlowTaskListMobileComponentExtRender(
        describeExt, whatDescribeExt, user, objectLayout, PageType.Detail, flowTaskListComponentExt);

    assertDoesNotThrow(() -> {
      render.render();
    });

    // 验证方法调用
    verify(flowTaskListComponentExt).getOrders();
    verify(flowTaskListComponentExt).getFilterInfo();
    verify(flowTaskListComponentExt).resetOrders(any());
    verify(flowTaskListComponentExt).resetFilterInfo(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试设计器模式渲染
   */
  @Test
  @DisplayName("正常场景 - 设计器模式渲染")
  void testRender_DesignerMode() {
    // 执行被测试方法
    TestBaseFlowTaskListMobileComponentExtRender render = new TestBaseFlowTaskListMobileComponentExtRender(
        describeExt, whatDescribeExt, user, objectLayout, PageType.Designer, flowTaskListComponentExt);

    assertDoesNotThrow(() -> {
      render.render();
    });

    // 在设计器模式下，不会移除不支持的字段
    verify(flowTaskListComponentExt).resetOrders(mockOrders);
    verify(flowTaskListComponentExt).resetFilterInfo(filterInfo);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试空排序字段的处理
   */
  @Test
  @DisplayName("正常场景 - 空排序字段处理")
  void testRender_EmptyOrders() {
    // 准备测试数据
    when(flowTaskListComponentExt.getOrders()).thenReturn(Lists.newArrayList());

    // 执行被测试方法
    TestBaseFlowTaskListMobileComponentExtRender render = new TestBaseFlowTaskListMobileComponentExtRender(
        describeExt, whatDescribeExt, user, objectLayout, PageType.Detail, flowTaskListComponentExt);

    assertDoesNotThrow(() -> {
      render.render();
    });

    // 验证会添加默认排序
    verify(flowTaskListComponentExt).resetOrders(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试空过滤信息的处理
   */
  @Test
  @DisplayName("边界场景 - 空过滤信息处理")
  void testRender_EmptyFilterInfo() {
    // 准备测试数据
    when(flowTaskListComponentExt.getFilterInfo()).thenReturn(null);

    // 执行被测试方法
    TestBaseFlowTaskListMobileComponentExtRender render = new TestBaseFlowTaskListMobileComponentExtRender(
        describeExt, whatDescribeExt, user, objectLayout, PageType.Detail, flowTaskListComponentExt);

    assertDoesNotThrow(() -> {
      render.render();
    });

    // 验证不会重置过滤信息
    verify(flowTaskListComponentExt, never()).resetFilterInfo(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试过滤信息为空的处理
   */
  @Test
  @DisplayName("边界场景 - 过滤信息为空的处理")
  void testRender_FilterInfoEmpty() {
    // 准备测试数据
    when(filterInfo.empty()).thenReturn(true);

    // 执行被测试方法
    TestBaseFlowTaskListMobileComponentExtRender render = new TestBaseFlowTaskListMobileComponentExtRender(
        describeExt, whatDescribeExt, user, objectLayout, PageType.Detail, flowTaskListComponentExt);

    assertDoesNotThrow(() -> {
      render.render();
    });

    // 验证不会重置过滤信息
    verify(flowTaskListComponentExt, never()).resetFilterInfo(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getSupportFields方法
   */
  @Test
  @DisplayName("正常场景 - 获取支持字段")
  void testGetSupportFields_Success() {
    // 执行被测试方法
    TestBaseFlowTaskListMobileComponentExtRender render = new TestBaseFlowTaskListMobileComponentExtRender(
        describeExt, whatDescribeExt, user, objectLayout, PageType.Detail, flowTaskListComponentExt);

    List<String> supportFields = render.getSupportFields();

    // 验证结果
    assertNotNull(supportFields);
    // 由于实际实现中使用LayoutExt.of(objectLayout).getFieldList()，这里只验证不为null
    assertNotNull(supportFields);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isDesigner方法
   */
  @Test
  @DisplayName("正常场景 - 判断是否为设计器模式")
  void testIsDesigner_Success() {
    // 测试设计器模式
    TestBaseFlowTaskListMobileComponentExtRender designerRender = new TestBaseFlowTaskListMobileComponentExtRender(
        describeExt, whatDescribeExt, user, objectLayout, PageType.Designer, flowTaskListComponentExt);

    assertTrue(designerRender.isDesigner());

    // 测试非设计器模式
    TestBaseFlowTaskListMobileComponentExtRender normalRender = new TestBaseFlowTaskListMobileComponentExtRender(
        describeExt, whatDescribeExt, user, objectLayout, PageType.Detail, flowTaskListComponentExt);

    assertFalse(normalRender.isDesigner());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试不同PageType的处理
   */
  @Test
  @DisplayName("正常场景 - 不同PageType处理")
  void testRender_DifferentPageTypes() {
    // 测试Edit页面类型
    TestBaseFlowTaskListMobileComponentExtRender editRender = new TestBaseFlowTaskListMobileComponentExtRender(
        describeExt, whatDescribeExt, user, objectLayout, PageType.Edit, flowTaskListComponentExt);

    assertDoesNotThrow(() -> {
      editRender.render();
    });

    // 测试List页面类型
    TestBaseFlowTaskListMobileComponentExtRender listRender = new TestBaseFlowTaskListMobileComponentExtRender(
        describeExt, whatDescribeExt, user, objectLayout, PageType.List, flowTaskListComponentExt);

    assertDoesNotThrow(() -> {
      listRender.render();
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试字段过滤逻辑
   */
  @Test
  @DisplayName("正常场景 - 字段过滤逻辑")
  void testRender_FieldFiltering() {
    // 准备测试数据 - 排序字段不在支持字段列表中
    when(orderBy.getFieldName()).thenReturn("unsupported_field");

    // 执行被测试方法
    TestBaseFlowTaskListMobileComponentExtRender render = new TestBaseFlowTaskListMobileComponentExtRender(
        describeExt, whatDescribeExt, user, objectLayout, PageType.Detail, flowTaskListComponentExt);

    assertDoesNotThrow(() -> {
      render.render();
    });

    // 验证会移除不支持的排序字段
    verify(flowTaskListComponentExt).resetOrders(any());
    verify(flowTaskListComponentExt).resetFilterInfo(any());
  }
}
