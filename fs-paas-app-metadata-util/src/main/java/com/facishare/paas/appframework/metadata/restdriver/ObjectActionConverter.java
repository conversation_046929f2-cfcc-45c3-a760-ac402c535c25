package com.facishare.paas.appframework.metadata.restdriver;

/**
 * Created by zhouwr on 2017/11/6
 */
public interface ObjectActionConverter {
    /**
     * 将actionCode转成functionCode
     *
     * @param actionCode
     * @return
     */
    String toFunctionCode(String actionCode);

    /**
     * 将functionCode转成actionCode
     *
     * @param functionCode
     * @return
     */
    String toActionCode(String functionCode);

    /**
     * 根据actionCode获取按钮名称
     *
     * @return
     */
    String getLabelByActionCode(String actionCode);
}
