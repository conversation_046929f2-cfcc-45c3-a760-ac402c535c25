package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.metadata.ButtonUsePageType;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.expansion.DescribeExpansionRender;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.appframework.metadata.layout.MasterDetailGroupComponentBuilder;
import com.facishare.paas.appframework.metadata.layout.component.IComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by liwei on 2019/11/12
 */
public class StandardDetailListHeaderController extends PreDefineController<StandardDetailListHeaderController.Arg, StandardDetailListHeaderController.Result> {
    private ObjectDescribeExt objectDescribeExt;
    private MasterDetailFieldDescribe masterDetailFieldDescribe;

    @Override
    protected void before(StandardDetailListHeaderController.Arg arg) {
        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(),
                controllerContext.getObjectApiName());
        objectDescribeExt = ObjectDescribeExt.of(describe);
    }

    @Override
    protected Result doService(Arg arg) {
        Map<String, LayoutDocument> layoutsOfRecordTypes = getLayoutsOfRecordTypes();
        boolean hasEditPermission = checkEditPermission();
        return buildResult(layoutsOfRecordTypes, hasEditPermission);
    }

    protected Result buildResult(Map<String, LayoutDocument> layoutsOfRecordTypes, boolean hasEditPermission) {
        Result result = new Result();
        result.setDetailLayouts(layoutsOfRecordTypes);
        if (arg.includeDescribe()) {
            result.setObjectDescribe(ObjectDescribeDocument.handleDescribeCache(arg.getDescribeVersionMap(), objectDescribeExt.getObjectDescribe()));
        }
        result.setHasEditPermission(hasEditPermission);
        result.setObjectDescribeExt(buildDescribeExt());
        return result;
    }

    protected boolean checkEditPermission() {
        return !objectDescribeExt.isSlaveObjectCreateWithMasterAndHiddenDetailButton();
    }

    protected ObjectDescribeDocument buildDescribeExt() {
        DescribeExtra describeExtra = serviceFacade.findDescribeExtraByRenderType(controllerContext.getUser(), objectDescribeExt,
                Collections.emptyList(), DescribeExpansionRender.RenderType.Detail, true);
        return ObjectDescribeDocument.of(describeExtra);
    }

    protected List<IButton> getDetailButtons() {
        IObjectDescribe targetDescribe = serviceFacade.findObject(controllerContext.getTenantId(), getMasterDetailFieldDescribe().getTargetApiName());
        stopWatch.lap("getDetailButtons.findObject");
        IObjectData targetData = serviceFacade.findObjectDataIncludeDeleted(controllerContext.getUser(),
                arg.getMasterDataId(), targetDescribe.getApiName());
        stopWatch.lap("getDetailButtons.findObjectDataIncludeDeleted");

        List<IButton> buttonList = MasterDetailGroupComponentBuilder.builder()
                .buttonLogicService(serviceFacade)
                .user(controllerContext.getUser())
                .objectDescribeExt(ObjectDescribeExt.of(targetDescribe))
                .objectData(targetData)
                .build()
                .getButtons(objectDescribeExt.getObjectDescribe());

        if (CollectionUtils.notEmpty(arg.getRelatedListComponent())) {
            return filterByComponent(buttonList);
        }

        return buttonList;
    }

    private MasterDetailFieldDescribe getMasterDetailFieldDescribe() {
        if (Objects.nonNull(masterDetailFieldDescribe)) {
            return masterDetailFieldDescribe;
        }
        return masterDetailFieldDescribe = objectDescribeExt.getMasterDetailFieldDescribe()
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.MASTER_DETAIL_FIELD_NOT_EXIST, objectDescribeExt.getDisplayName())));
    }

    private List<IButton> filterByComponent(List<IButton> buttonList) {
        return ListComponentExt.of(arg.getRelatedListComponent()).fillButtonInfoPageType(IComponentInfo.PAGE_TYPE_RELATED)
                .filterButtonByUsePage(buttonList, ButtonUsePageType.ListNormal, IComponentInfo.PAGE_TYPE_RELATED);
    }

    private Map<String, LayoutDocument> getLayoutsOfRecordTypes() {
        Map<String, LayoutDocument> result = Maps.newLinkedHashMap();
        List<IButton> detailButtons = getDetailButtons();
        stopWatch.lap("getDetailButtons");

        RecordTypeFieldDescribe recordTypeFieldDescribe = objectDescribeExt.getRecordTypeField()
                .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR)));
        List<String> recordTypes = recordTypeFieldDescribe.getRecordTypeOptions().stream().map(x -> x.getApiName()).collect(Collectors.toList());
        Map<String, ILayout> layoutMap = serviceFacade.getLayoutLogicService().findObjectLayoutWithType(controllerContext.getUser(), recordTypes,
                objectDescribeExt.getObjectDescribe(), arg.layoutType, null);
        stopWatch.lap("findObjectLayoutWithType");

        List<String> matchRecordTypes = serviceFacade.findValidAndMatchRecordTypes(controllerContext.getUser(),
                getMasterDetailFieldDescribe().getTargetApiName(), arg.getMasterRecordType(), objectDescribeExt.getApiName());
        stopWatch.lap("findValidAndMatchRecordTypes");

        for (String recordType : recordTypes) {
            ILayout layout = layoutMap.get(recordType);
            if (layout == null) {
                continue;
            }
            //拷贝一下layout，防止not_match被覆盖
            layout = LayoutExt.of(layout).copy();
            layout.setButtons(detailButtons);
            boolean isMatch = matchRecordTypes.contains(recordType);
            layout.set("not_match", !isMatch);
            result.put(recordType, LayoutDocument.of(layout));
        }

        return result;
    }

    protected LayoutLogicService.LayoutContext buildLayoutContext() {
        return LayoutLogicService.LayoutContext.of(controllerContext.getUser(), controllerContext.getAppId());
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Data
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Arg {
        private String masterDataId;
        private String masterRecordType;
        private String layoutType;
        private Boolean includeDescribe = true;
        private Map<String, Integer> describeVersionMap;

        //Detail接口返回的布局中的相关对象组件(用于加工场景和按钮)
        @JSONField(name = "related_list_component")
        @JsonProperty("related_list_component")
        @SerializedName("related_list_component")
        private Map<String, Object> relatedListComponent;

        public boolean includeDescribe() {
            return !Boolean.FALSE.equals(includeDescribe);
        }
    }

    @Data
    @NoArgsConstructor
    public static class Result {
        private ObjectDescribeDocument objectDescribe;
        private Map<String, LayoutDocument> detailLayouts;
        //是否有编辑权限
        private Boolean hasEditPermission;
        /**
         * 引用字段的拓展描述
         */
        private ObjectDescribeDocument objectDescribeExt;
    }

}
