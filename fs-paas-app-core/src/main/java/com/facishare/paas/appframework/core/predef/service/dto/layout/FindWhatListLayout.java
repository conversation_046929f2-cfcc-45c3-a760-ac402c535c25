package com.facishare.paas.appframework.core.predef.service.dto.layout;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface FindWhatListLayout {
    @Data
    class Arg {
        @JSONField(name = "M2")
        @JsonProperty("api_name")
        @SerializedName("api_name")
        private String objectDescribeApiName;

        @JSONField(name = "M2")
        @JsonProperty("what_api_name")
        @SerializedName("what_api_name")
        private String whatApiName;

        private String appId;
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "M3")
        private List<LayoutDocument> listLayout;

        @J<PERSON><PERSON>ield(name = "M4")
        private ObjectDescribeDocument objectDescribe;

        @J<PERSON>NField(name = "M5")
        private ObjectDescribeDocument whatDescribe;
    }
}
