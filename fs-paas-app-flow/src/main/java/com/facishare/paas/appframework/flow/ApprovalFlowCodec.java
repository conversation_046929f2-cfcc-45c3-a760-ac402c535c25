package com.facishare.paas.appframework.flow;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.facishare.rest.core.codec.IRestCodeC;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * 因为默认的codec是不支持null值的，但是有可能有的字段被修改成null了，审批流回调需要传回这个null值，所以
 * 调用审批流的接口使用带null值的序列化方法。
 * <p>
 * Created by zhouwr on 2018/3/9
 */
@Slf4j
public class ApprovalFlowCodec implements IRestCodeC {
    @Override
    public <T> byte[] encodeArg(T obj) {
        return JSON.toJSONString(obj, SerializerFeature.WriteMapNullValue, SerializerFeature.DisableCircularReferenceDetect,
                SerializerFeature.WriteBigDecimalAsPlain).getBytes(StandardCharsets.UTF_8);
    }

    @Override
    public <T> T decodeResult(int statusCode, Map<String, List<String>> headers, byte[] bytes, Class<T> clazz) {
        if (String.class.equals(clazz)) {
            return (T) new String(bytes, StandardCharsets.UTF_8);
        }

        String json = new String(bytes, StandardCharsets.UTF_8);
        return JSON.parseObject(json, clazz);
    }
}
