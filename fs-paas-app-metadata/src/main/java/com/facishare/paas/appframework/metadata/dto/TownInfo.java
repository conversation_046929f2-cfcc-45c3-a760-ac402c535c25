package com.facishare.paas.appframework.metadata.dto;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Created by zhangxf in 2021/1/26 17:18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TownInfo {
    private String districtId;
    private String townId;
    private String townName;

    public static final String TOWN_ID = "value";
    public static final String TOWN_NAME = "label";

    public static List<TownInfo> getTownInfos(Map<String, List<Map<String, String>>> townInfoByDistricts) {
        List<TownInfo> townInfos = Lists.newArrayList();
        townInfoByDistricts.forEach((did, infos) -> {
            infos.forEach(x -> {
                TownInfo townInfo = new TownInfo();
                townInfo.setDistrictId(did);
                townInfo.setTownId(x.get(TOWN_ID));
                townInfo.setTownName(x.get(TOWN_NAME));
                townInfos.add(townInfo);
            });
        });
        return townInfos;
    }
}
