package com.facishare.paas.appframework.core.predef.service;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.controller.StandardController;
import com.facishare.paas.appframework.core.predef.controller.StandardImportObjectController;
import com.facishare.paas.appframework.core.predef.service.dto.file.ExecuteExportTaskHookFunction;
import com.facishare.paas.appframework.core.predef.service.dto.objectImport.GetExportObjects;
import com.facishare.paas.appframework.core.predef.service.dto.objectImport.GetImportObject;
import com.facishare.paas.appframework.core.predef.service.dto.objectImport.GetImportObjectList;
import com.facishare.paas.appframework.core.predef.service.dto.objectImport.GetImportObjectList.ImportObjectDTO;
import com.facishare.paas.appframework.core.predef.service.dto.objectImport.GetRelatedObjects;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.function.FunctionRestProxy;
import com.facishare.paas.appframework.function.dto.DeleteRelation;
import com.facishare.paas.appframework.function.dto.FindRelationBySource;
import com.facishare.paas.appframework.function.dto.SaveRelation;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.ExportTaskHookService;
import com.facishare.paas.appframework.metadata.ImportService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.ImportConfig;
import com.facishare.paas.appframework.metadata.dto.*;
import com.facishare.paas.appframework.metadata.importobject.*;
import com.facishare.paas.appframework.metadata.objects.ObjectListConfig;
import com.facishare.paas.appframework.metadata.switchcache.SwitchCacheService;
import com.facishare.paas.appframework.metadata.switchcache.provider.SwitchCacheProviderManager;
import com.facishare.paas.appframework.metadata.switchcache.provider.UniqueRuleSwitchCacheProvider;
import com.facishare.paas.appframework.metadata.util.ActiveRecordConfig;
import com.facishare.paas.appframework.metadata.util.OrganizationConfigUtil;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Quote;
import com.facishare.paas.metadata.api.service.IUniqueRuleService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.crm.openapi.Utils.*;
import static com.facishare.paas.appframework.metadata.importobject.DefaultObjectImportProvider.INSERT_IMPORT;
import static com.facishare.paas.appframework.metadata.importobject.ObjectImportEnum.*;
import static com.facishare.paas.metadata.api.describe.IObjectDescribe.DEFINE_TYPE_CUSTOM;
import static com.facishare.paas.metadata.api.describe.IObjectDescribe.DEFINE_TYPE_PACKAGE;
import static java.util.Collections.singletonList;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

/**
 * Created by liyiguang on 2017/10/11.
 */
@ServiceModule("object_import")
@Component
@Slf4j
public class ObjectImportService {
    private static final Set<String> UNION_IMPORT_OBJECT_CODE = Sets.newHashSet(MULTI_IMPORT_CUSTOMER_CONTACT.getObjectCode(),
            MULTI_IMPORT_CUSTOMER_ORDER_CUSTOMER_ORDER_PRODUCT.getObjectCode(),
            MULTI_IMPORT_RETURN_ORDER_RETURN_ORDER_PRODUCT.getObjectCode(),
            MULTIIMPORT_ACCOUNT_ACCOUNTFININFO.getObjectCode());
    @Autowired
    private ObjectImportInitManager objectImportInitManager;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private IUniqueRuleService uniqueRuleService;
    @Autowired
    private UserRoleInfoService userRoleInfoService;
    @Autowired
    private ImportObjectModuleManager importObjectModuleManager;
    @Autowired
    private SwitchCacheService switchCacheService;
    @Autowired
    private FunctionRestProxy functionRestProxy;
    @Autowired
    private FunctionLogicService functionLogicService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ExportTaskHookService exportTaskHookService;
    @Autowired
    private ImportService importService;

    /**
     * 联合导入获取从对象
     */
    @ServiceMethod("getRelatedObjects")
    public GetRelatedObjects.Result getRelatedObjects(GetRelatedObjects.Arg arg, ServiceContext context) {
        List<IObjectDescribe> describes;
        if (UnionImportConfig.canNotBeMaster(arg.getDescribeApiName())) {
            describes = Lists.newArrayList();
        } else {
            describes = describeLogicService.findSimpleDetailDescribes(context.getTenantId(), arg.getDescribeApiName());
            describes.removeIf(d -> UnionImportConfig.canNotBeDetail(context.getTenantId(), d.getApiName()));
        }
        List<IObjectDescribe> validObjectList = describeLogicService.filterDescribesWithActionCode(context.getUser(),
                describes, ObjectAction.BATCH_IMPORT.getActionCode());
        return GetRelatedObjects.Result.builder().describes(ObjectDescribeDocument.ofList(validObjectList)).build();
    }

    @ServiceMethod("getExportDetailObjects")
    public GetExportObjects.Result getExportDetailObjects(GetExportObjects.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getDescribeApiName())) {
            return GetExportObjects.Result.builder().build();
        }
        if (!UnionImportConfig.canExportMasterDetail(arg.getDescribeApiName())) {
            GetExportObjects.Result result = GetExportObjects.Result.builder().build();
            if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.IMPORT_SUPPORT_FILE_GRAY, context.getTenantId())) {
                return result;
            }
            Map<String, Boolean> funPrivilegeCheck = functionPrivilegeService.funPrivilegeCheck(context.getUser(), arg.getDescribeApiName(),
                    StandardAction.ExportFileAttachment.getFunPrivilegeCodes());
            result.setSupportFileAttachment(BooleanUtils.isTrue(funPrivilegeCheck.getOrDefault(ObjectAction.PICTURE_ANNEX_DOWNLOAD.getActionCode(), false)));
            return result;
        }
        GetExportObjects.Result result = GetExportObjects.Result.builder().build();
        List<IObjectDescribe> detailDescribes = describeLogicService.findDetailDescribes(context.getTenantId(), arg.getDescribeApiName());
        // List<IObjectDescribe> validObjectList = describeLogicService.filterDescribesWithActionCode(context.getUser(), detailDescribes, ObjectAction.BATCH_EXPORT.getActionCode());
        List<String> detailApiNames = detailDescribes.stream().map(IObjectDescribe::getApiName).collect(Collectors.toList());
        List<String> objectApiNames = Lists.newArrayList();
        objectApiNames.add(arg.getDescribeApiName());
        objectApiNames.addAll(detailApiNames);
        Map<String, Map<String, Boolean>> batchFunPrivilegeCheck = functionPrivilegeService.batchFunPrivilegeCheck(context.getUser(), objectApiNames,
                StandardAction.ExportFileAttachment.getFunPrivilegeCodes());
        detailDescribes.removeIf(x -> CollectionUtils.notEmpty(batchFunPrivilegeCheck.get(x.getApiName()))
                && !batchFunPrivilegeCheck.get(x.getApiName()).getOrDefault(ObjectAction.BATCH_EXPORT.getActionCode(), false));
        List<ObjectDescribeDocument> details = Lists.newArrayList();
        for (IObjectDescribe detailDescribe : detailDescribes) {
            Map<String, Object> map = Maps.newHashMap();
            map.put(IObjectDescribe.API_NAME, detailDescribe.getApiName());
            map.put(IObjectDescribe.DISPLAY_NAME, detailDescribe.getDisplayName());
            details.add(ObjectDescribeDocument.of(map));
        }
        if (CollectionUtils.notEmpty(details)) {
            result.setDescribes(details);
        }
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.IMPORT_SUPPORT_FILE_GRAY, context.getTenantId())) {
            return result;
        }
        Map<String, Boolean> masterFunPrivilege = batchFunPrivilegeCheck.get(arg.getDescribeApiName());
        if (CollectionUtils.notEmpty(masterFunPrivilege)) {
            result.setSupportFileAttachment(BooleanUtils.isTrue(masterFunPrivilege.getOrDefault(ObjectAction.PICTURE_ANNEX_DOWNLOAD.getActionCode(), false)));
        }
        for (String detailApiName : detailApiNames) {
            Map<String, Boolean> detailFunPrivilege = batchFunPrivilegeCheck.get(detailApiName);
            if (CollectionUtils.notEmpty(detailFunPrivilege)) {
                Boolean supportDetailFileAttachment = detailFunPrivilege.getOrDefault(ObjectAction.PICTURE_ANNEX_DOWNLOAD.getActionCode(), false);
                if (BooleanUtils.isTrue(supportDetailFileAttachment)) {
                    result.setSupportDetailFileAttachment(true);
                    break;
                }
            }
        }
        Map<String, Set<String>> unauthorizedFields = functionPrivilegeService.getUnauthorizedFields(context.getUser(), detailApiNames);
        List<GetExportObjects.ExportDetailObject> exportDetailObjects = Lists.newArrayList();
        detailDescribes.forEach(describe -> {
            GetExportObjects.ExportDetailObject exportDetailObject = GetExportObjects.ExportDetailObject.builder().build();
            Boolean isHavePrivilege = CollectionUtils.notEmpty(batchFunPrivilegeCheck.get(describe.getApiName()))
                    && batchFunPrivilegeCheck.get(describe.getApiName()).getOrDefault(ObjectAction.PICTURE_ANNEX_DOWNLOAD.getActionCode(), false);
            if (BooleanUtils.isNotTrue(isHavePrivilege)) {
                return;
            }
            exportDetailObject.setDetailApiName(describe.getApiName());
            List<Map<String, Object>> fields = Lists.newArrayList();
            List<IFieldDescribe> imageFileAttachmentFields = ObjectDescribeExt.of(describe).getActiveFieldDescribes().stream()
                    .filter(x -> IFieldType.IMAGE.equals(x.getType())
                            || IFieldType.FILE_ATTACHMENT.equals(x.getType())
                            || IFieldType.SIGNATURE.equals(x.getType())
                            || (IFieldType.QUOTE.equals(x.getType()) && IFieldType.IMAGE.equals(((Quote) x).getQuoteFieldType())))
                    .collect(Collectors.toList());
            if (CollectionUtils.empty(imageFileAttachmentFields)) {
                return;
            }
            Set<String> noPrivilegeFields = CollectionUtils.nullToEmpty(unauthorizedFields.get(describe.getApiName()));
            imageFileAttachmentFields.forEach(field -> {
                if (noPrivilegeFields.contains(field.getApiName())) {
                    return;
                }
                Map<String, Object> map = Maps.newHashMap();
                map.put(IFieldDescribe.API_NAME, field.getApiName());
                map.put(IFieldDescribe.TYPE, field.getType());
                map.put(IFieldDescribe.LABEL, field.getLabel());
                fields.add(map);
            });
            exportDetailObject.setFieldDescribes(fields);
            exportDetailObjects.add(exportDetailObject);
        });
        result.setExportDetailObjects(exportDetailObjects);
        return result;
    }

    /**
     * 导入工具的对象列表中每个对象支持的导入操作，通过注册{@link ObjectImportInitProvider}接口来获取.
     * 对于未注册的预置对象， 默认会走自定义对象的处理逻辑{@link DefaultObjectImportProvider}
     *
     * @param context
     * @return
     */
    @ServiceMethod("get_import_object_list")
    public GetImportObjectList.Result getImportObjectList(GetImportObjectList.Arg arg, ServiceContext context) {
        List<ImportObjectDTO> importObjectList;
        boolean management = Objects.nonNull(arg) && arg.isManagement();
        if (AppFrameworkConfig.isImportObjectListGray(context.getTenantId())) {
            importObjectList = getImportObjectByModule(context.getUser(), management);
        } else {
            importObjectList = getImportObject(context.getUser(), management);
        }

        return GetImportObjectList.Result.builder().importObjectList(importObjectList).build();
    }

    private List<ImportObjectDTO> getImportObjectByModule(User user, boolean management) {
        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        List<String> objectModule = AppFrameworkConfig.getImportObjectModule(user.getTenantId());
        Map<String, List<ImportObjectDTO>> importObjectMap = Maps.newConcurrentMap();
        objectModule.forEach(module -> task.submit(() -> {
            ImportObjectProvider provider = importObjectModuleManager.getProvider(module);

            ImportObjectProvider.ImportModuleContext context = ImportObjectProvider.ImportModuleContext.of(management);
            List<ImportObjectModule.ImportModule> importObjectModule = provider.getImportObjectModule(user, context);
            List<ImportObjectDTO> list = importObjectModule.stream()
                    .map(ImportObjectDTO::fromImportObjectModule)
                    .collect(Collectors.toList());
            importObjectMap.putIfAbsent(module, list);
        }));

        // 获取还没有灰度 module 对应的对象
        List<ImportObjectDTO> importObjectDTOList = Lists.newCopyOnWriteArrayList();
        task.submit(() -> {
            Set<String> objects = AppFrameworkConfig.getObjectsByModules(objectModule.toArray(new String[]{}));
            List<ImportObjectDTO> importObjectDTOS = getImportObjectByDescribeDefineType(user, management, DEFINE_TYPE_PACKAGE);
            importObjectDTOS.removeIf(it -> objects.contains(it.getObjectApiName()));
            importObjectDTOList.addAll(importObjectDTOS);
        });

        try {
            task.await(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("getImportObjectByModule error,", e);
        }

        List<ImportObjectDTO> result = objectModule.stream()
                .map(module -> importObjectMap.getOrDefault(module, Collections.emptyList()))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        result.addAll(importObjectDTOList);
        // 第一次排序、按对象的默认顺序
        result = CollectionUtils.sortByGivenOrder(result, ObjectDescribeExt.getObjectOrderList(), ImportObjectDTO::getObjectApiName);
        // 第二次排序、但导入的对象
        return CollectionUtils.sortByGivenOrder(result, AppFrameworkConfig.getImportObjectModuleOrder(), ImportObjectDTO::getObjectCode);
    }

    @ServiceMethod("get_import_object")
    public GetImportObject.Result getImportObject(GetImportObject.Arg arg, ServiceContext context) {
        // 获取对象导入信息
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), getPredecessorApiNameByObjectCode(arg.getObjectCode()));
        ObjectImportInitProvider provider = objectImportInitManager.getLocalProvider(getApiNameByObjectCode(arg.getObjectCode()));

        if (Objects.isNull(arg.getDetailObjectApiName())) {
            IUniqueRule uniqueRule = getUniqueRuleByDescribeApiName(context, describe, arg.getObjectCode());
            Optional<ImportObject> importObjectOpt = provider.getImportObject(describe, uniqueRule);
            if (!importObjectOpt.isPresent()) {
                return GetImportObject.Result.builder().build();
            }
            // 填充唯一性规则信息
            ImportObjectDTO importObject = ImportObjectDTO.from(importObjectOpt.get(), uniqueRule, describe);
            fillRuleSettingRight(importObject.getImportUniquenessRule(), context);
            return GetImportObject.Result.builder().importObject(importObject).build();
        } else {
            // 联合导入通用逻辑
            IObjectDescribe detailDescribe = describeLogicService.findObject(context.getTenantId(), arg.getDetailObjectApiName());
            Optional<ImportObject> masterImportObjectOp = provider.getImportObject(describe, null);
            Optional<ImportObject> detailImportObjectOp = provider.getImportObject(detailDescribe, null);
            // 合并联合导入信息
            if (!masterImportObjectOp.isPresent() || !detailImportObjectOp.isPresent()) {
                return GetImportObject.Result.builder().build();
            }
            ImportObjectDTO masterImportObject = ImportObjectDTO.from(masterImportObjectOp.get(), null, describe);
            ImportObjectDTO detailImportObject = ImportObjectDTO.from(masterImportObjectOp.get(), null, detailDescribe);
            ImportObjectDTO importObjectDTO = ImportObjectDTO.builder()
                    .unionImportApiNameList(Lists.newArrayList(arg.getObjectCode(), arg.getDetailObjectApiName()))  // 主在前，从在后
                    .isEnableUnionDuplicateChecking(masterImportObject.getIsEnableUnionDuplicateChecking() &&
                            detailImportObject.getIsEnableUnionDuplicateChecking())
                    .isNotSupportSaleEvent(Boolean.TRUE)
                    .supportType(ImportType.UNSUPPORT_UPDATE_IMPORT)  // 不支持更新导入
                    .isOpenWorkFlow(masterImportObject.getIsOpenWorkFlow() && detailImportObject.getIsOpenWorkFlow())
                    .matchingTypes(getUnionMatchingTypes(masterImportObject, detailImportObject))
                    .build();
            return GetImportObject.Result.builder().importObject(importObjectDTO).build();

        }
    }

    @ServiceMethod("get_import_object_module")
    public ImportObjectModule.Result getImportObjectModule(ImportObjectModule.Arg arg, ServiceContext context) {
        List<ImportObjectModule.ImportModule> importObjectModules = importObjectModuleManager.getLocalProvider(arg.getObjectModule())
                .map(importObjectProvider -> importObjectProvider.getImportObjectModule(context.getUser(), arg.getContext()))
                .orElse(Collections.emptyList());
        ImportObjectModule.Result result = new ImportObjectModule.Result();
        result.setImportObjectModules(importObjectModules);
        return result;
    }

    @ServiceMethod("get_import_object_info")
    public ImportObjectInfo.Result getImportObject(ImportObjectInfo.Arg arg, ServiceContext context) {
        // 获取对象导入信息
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getObjectApiName());
        ObjectImportInitProvider provider = objectImportInitManager.getLocalProvider(arg.getObjectCode());
        IUniqueRule uniqueRule = getUniqueRuleByDescribeApiName(context, describe, arg.getObjectCode());

        Optional<ImportObject> importObjectOpt = provider.getImportObject(describe, uniqueRule);

        return importObjectOpt.map(ImportObjectInfo.Result::of)
                .orElseGet(() -> ImportObjectInfo.Result.of(null));
    }

    @ServiceMethod("find_config")
    public FindImportConfig.Result findImportConfig(FindImportConfig.Arg arg, ServiceContext context) {
        ImportConfig insertImportConfig = buildImportConfig(context.getUser(), arg.getDescribeApiName(),
                ImportConfig.INSERT, false, arg::toFindRelationArg);
        ImportConfig updateImportConfig = buildImportConfig(context.getUser(), arg.getDescribeApiName(),
                ImportConfig.UPDATE, false, arg::toFindRelationArg);

        ImportConfig insertImportPreConfig = buildImportConfig(context.getUser(), arg.getDescribeApiName(),
                ImportConfig.INSERT, true, arg::toFindRelationArg);
        ImportConfig updateImportPreConfig = buildImportConfig(context.getUser(), arg.getDescribeApiName(),
                ImportConfig.UPDATE, true, arg::toFindRelationArg);
        ImportTenantSetting importSettings = findImportSetting(context, arg.getDescribeApiName());
        ImportReferenceMapping referenceMapping = findImportReferenceMapping(context, arg.getDescribeApiName());
        return FindImportConfig.Result.of(insertImportConfig, updateImportConfig,
                insertImportPreConfig, updateImportPreConfig,
                importSettings.getInsertImport(), importSettings.getUpdateImport(),
                referenceMapping);
    }

    private ImportReferenceMapping findImportReferenceMapping(ServiceContext context, String describeApiName) {
        User user = context.getUser();
        boolean allow = UdobjGrayUtil.isObjectAndTenantGray("import_reference_mapping_new", user.getTenantId(), describeApiName);
        if (!allow) {
            return ImportReferenceMapping.builder().build();
        }
        ImportReferenceMapping importReferenceMapping = importService.findImportReferenceMapping(user, describeApiName);
        importReferenceMapping.setImportReferenceMappingGray(true);
        return importReferenceMapping;
    }


    public ImportTenantSetting findImportSetting(ServiceContext context, String describeApiName) {
        User user = context.getUser();
        String importSettingKey = buildImportSettingKey(describeApiName);
        String tenantConfig = configService.findTenantConfig(user, importSettingKey);
        ImportTenantSetting importTenantSetting;
        if (StringUtils.isBlank(tenantConfig)) {
            importTenantSetting = initImportTenantSetting(describeApiName);
        } else {
            importTenantSetting = JSON.parseObject(tenantConfig, ImportTenantSetting.class);
        }
        ImportObjectDTO importObject = findImportObject(context, describeApiName);
        ImportSetting insertImport = importTenantSetting.getInsertImport();
        if (Objects.isNull(insertImport)) {
            insertImport = new ImportSetting();
        }
        insertImport.setGrayImportAddAction(UdobjGrayUtil.isObjectAndTenantGray("gray_import_add_action", context.getTenantId(), describeApiName));
        insertImport.setOpenApprovalFlow(BooleanUtils.isTrue(importObject.getIsApprovalFlow()));
        insertImport.setOpenWorkFlow(BooleanUtils.isTrue(importObject.getIsOpenWorkFlow()));
        ImportSetting updateImport = importTenantSetting.getUpdateImport();
        if (Objects.isNull(updateImport)) {
            updateImport = new ImportSetting();
        }
        updateImport.setOpenWorkFlow(BooleanUtils.isTrue(importObject.getIsOpenWorkFlow()));
        return importTenantSetting;
    }

    private ImportTenantSetting initImportTenantSetting(String describeApiName) {
        ImportTenantSetting mangerSetting = ImportTenantSetting.builder().build();
        ImportSetting insertImport = ImportSetting.builder()
                .insertImportSwitch(false)
                .triggerWorkFlow(false)
                .triggerApprovalFlow(false)
                .describeApiName(describeApiName)
                .build();
        mangerSetting.setInsertImport(insertImport);
        ImportSetting updateImport = ImportSetting.builder()
                .updateImportSwitch(false)
                .triggerWorkFlow(false)
                .describeApiName(describeApiName)
                .build();
        mangerSetting.setUpdateImport(updateImport);
        return mangerSetting;
    }

    private ImportObjectDTO findImportObject(ServiceContext context, String describeApiName) {
        ControllerContext controllerContext = new ControllerContext(context.getRequestContext(), describeApiName, StandardController.ImportObject.name());
        StandardImportObjectController.Arg arg = new StandardImportObjectController.Arg();
        arg.setObjectCode(describeApiName);
        StandardImportObjectController.Result result = serviceFacade.triggerController(controllerContext, arg, StandardImportObjectController.Result.class);
        ImportObjectDTO importObject;
        if (Objects.nonNull(result) && Objects.nonNull(result.getImportObject())) {
            importObject = result.getImportObject();
        } else {
            importObject = ImportObjectDTO.builder().isApprovalFlow(false).isOpenWorkFlow(false).build();
        }
        return importObject;
    }


    private ImportConfig buildImportConfig(User user, String describeApiName, String importType,
                                           boolean importPreProcessing,
                                           Function<String, FindRelationBySource.Arg> getArg) {
        Objects.requireNonNull(getArg);
        String clientInfo = RequestUtil.getClientInfo();
        String funcRelationValue = ImportConfig.getFuncRelationValue(describeApiName, importType, importPreProcessing);
        FindRelationBySource.Arg arg = getArg.apply(funcRelationValue);

        FindRelationBySource.Result relationBySource = functionRestProxy.findRelationBySource(user.getTenantId(), user.getUserId(), clientInfo, arg);
        return CollectionUtils.nullToEmpty(relationBySource.getItems()).stream()
                .findFirst()
                .map(it -> {
                    IUdefFunction function = functionLogicService.findUDefFunction(user, it.getFuncApiName(), describeApiName);
                    if (Objects.isNull(function)) {
                        return ImportConfig.empty(describeApiName, importType, importPreProcessing);
                    }
                    return ImportConfig.from(it, function, importType, importPreProcessing);
                })
                .orElse(ImportConfig.empty(describeApiName, importType, importPreProcessing));
    }

    @ServiceMethod("save_config")
    public SaveImportConfig.Result saveImportConfig(SaveImportConfig.Arg arg, ServiceContext context) {
        User user = context.getUser();
        ImportConfig insert = arg.getInsert();
        updateFuncRelation(user, insert);

        ImportConfig update = arg.getUpdate();
        updateFuncRelation(user, update);

        ImportConfig insertPre = arg.getInsertPreProcessing();
        updateFuncRelation(user, insertPre);

        ImportConfig updatePre = arg.getUpdatePreProcessing();
        updateFuncRelation(user, updatePre);

        ImportSetting insertImport = arg.getInsertImport();
        ImportSetting updateImport = arg.getUpdateImport();
        updateImportConfig(user, insertImport, updateImport);
        ImportReferenceMapping importReferenceMapping = arg.getImportReferenceMapping();
        updateImportReferenceMapping(user, importReferenceMapping);
        return new SaveImportConfig.Result();
    }

    private void updateImportReferenceMapping(User user, ImportReferenceMapping importReferenceMapping) {
        if (Objects.isNull(importReferenceMapping)) {
            return;
        }
        ImportReferenceMapping referenceMapping = new ImportReferenceMapping();
        referenceMapping.setReferenceFieldMappingSwitch(BooleanUtils.isTrue(importReferenceMapping.getReferenceFieldMappingSwitch()));
        referenceMapping.setReferenceFieldMapping(importReferenceMapping.getReferenceFieldMapping());
        referenceMapping.setObjectApiName(importReferenceMapping.getObjectApiName());
        configService.upsertTenantConfig(user,
                ImportReferenceMapping.buildImportReferenceMappingKey(importReferenceMapping.getObjectApiName()),
                JSON.toJSONString(referenceMapping), ConfigValueType.JSON);
    }

    private void updateImportConfig(User user, ImportSetting insertImport, ImportSetting updateImport) {
        ImportTenantSetting importSettings = new ImportTenantSetting();
        importSettings.setInsertImport(insertImport);
        importSettings.setUpdateImport(updateImport);
        configService.upsertTenantConfig(user, buildImportSettingKey(insertImport.getDescribeApiName()),
                JSON.toJSONString(importSettings), ConfigValueType.JSON);
    }

    private String buildImportSettingKey(String describeApiName) {
        return "importSetting_" + describeApiName;
    }

    private void updateFuncRelation(User user, ImportConfig importConfig) {
        if (Objects.isNull(importConfig)) {
            return;
        }
        if (Strings.isNullOrEmpty(importConfig.getFunctionApiName())) {
            deletedFuncRelation(user, importConfig);
            return;
        }
        String clientInfo = RequestUtil.getClientInfo();
        FindRelationBySource.FuncRelationInfo funcRelationInfo = importConfig.toFuncRelationInfo((apiName) -> {
            IObjectDescribe describe = describeLogicService.findObjectUseThreadLocalCache(user.getTenantId(), apiName);
            return describe.getDisplayName();
        });
        SaveRelation.Arg arg = SaveRelation.Arg.of(Lists.newArrayList(funcRelationInfo));
        functionRestProxy.saveRelation(user.getTenantId(), user.getUserId(), clientInfo, arg);
    }

    private void deletedFuncRelation(User user, ImportConfig importConfig) {
        String clientInfo = RequestUtil.getClientInfo();
        String type = ImportConfig.IMPORT_RELATION_TYPE;
        String value = importConfig.toFuncRelationValue();
        FindRelationBySource.Arg arg = FindRelationBySource.Arg.of(type, value);
        FindRelationBySource.Result relationBySource = functionRestProxy.findRelationBySource(user.getTenantId(), user.getUserId(), clientInfo, arg);
        if (Objects.isNull(relationBySource) || CollectionUtils.empty(relationBySource.getItems())) {
            return;
        }
        relationBySource.getItems().stream()
                .map(FindRelationBySource.FuncRelationInfo::getFuncApiName)
                .map(it -> DeleteRelation.Arg.builder().funcApiName(it).type(type).value(value).build())
                .findFirst()
                .ifPresent(it -> functionRestProxy.deleteRelation(user.getTenantId(), user.getUserId(), clientInfo, it));
    }

    /*
     * 获取联合导入所支持的匹配方式
     * 取主从都支持的识别方式且不支持唯一性规则
     */
    private Map<String, List<MatchingType>> getUnionMatchingTypes(ImportObjectDTO masterImportObject,
                                                                  ImportObjectDTO detailImportObject) {
        // 主对象
        Map<String, List<MatchingType>> masterMap = masterImportObject.getMatchingTypes();
        List<MatchingType> masterTypes = masterMap.getOrDefault(INSERT_IMPORT, Lists.newArrayList(MatchingType.NAME));

        // 从对象
        Map<String, List<MatchingType>> detailMap = masterImportObject.getMatchingTypes();
        List<MatchingType> detailTypes = masterMap.getOrDefault(INSERT_IMPORT, Lists.newArrayList(MatchingType.NAME));

        // 取新建导入交集
        Map<String, List<MatchingType>> result = Maps.newHashMap();
        masterTypes.retainAll(detailTypes);
        result.put(INSERT_IMPORT, masterTypes);
        return result;
    }

    private IUniqueRule getUniqueRuleByDescribeApiName(ServiceContext context, IObjectDescribe describe, String objectCode) {
        if (UNION_IMPORT_OBJECT_CODE.contains(objectCode)) {
            return null;
        }
        return switchCacheService.findBySwitchCache(context.getTenantId(),
                SwitchCacheProviderManager.UNIQUE_RULE,
                UniqueRuleSwitchCacheProvider.USE_WHEN_IMPORT_EXCEL,
                describe.getApiName(),
                () -> uniqueRuleService.findByDescribeApiName(context.getTenantId(), describe.getApiName())
        ).orElse(null);
    }

    private void fillRuleSettingRight(ImportObject.ImportUniquenessRule uniquenessRule, ServiceContext context) {
        if (Objects.nonNull(uniquenessRule)) {
            uniquenessRule.setRuleSettingRight(userRoleInfoService.isAdmin(context.getUser()));
        }
    }

    private List<ImportObjectDTO> getImportObjectByDescribeDefineType(User user, boolean management, String describeDefineType) {
        //1.查询当前企业下所有对象描述，过导入的功能权限
        List<IObjectDescribe> validObjectList = getDescribeList(user, management, describeDefineType);

        // 2.分别处理预置对象和自定义对象，确保所有的预设对象在自定义对象之前
        Map<String, List<IObjectDescribe>> describeByDefineType = validObjectList.stream()
                .collect(groupingBy(IObjectDescribe::getDefineType));
        // 处理预置对象
        List<ImportObjectDTO> importObjectList = Lists.newArrayList();
        if (!DEFINE_TYPE_CUSTOM.equals(describeDefineType)) {
            List<IObjectDescribe> packageObjectList = describeByDefineType.get(IObjectDescribe.PACKAGE);
            importObjectList = getObjectList(packageObjectList, user.getTenantId());
        }

        // 处理自定义对象
        List<ImportObjectDTO> objectDTOList = Lists.newArrayList();
        if (!DEFINE_TYPE_PACKAGE.equals(describeDefineType)) {
            List<IObjectDescribe> customObjectList = describeByDefineType.get(IObjectDescribe.DEFINE_TYPE_CUSTOM);
            objectDTOList = CollectionUtils.nullToEmpty(customObjectList).stream().map(x -> {
                ObjectImportInitProvider provider = objectImportInitManager.getLocalProvider(x.getApiName());
                return provider.getImportObject(x, null);
            }).filter(Optional::isPresent).map(Optional::get).map(ImportObjectDTO::from).collect(Collectors.toList());
        }

        //3.合并处理结果
        importObjectList.addAll(objectDTOList);
        return importObjectList;
    }

    private List<ImportObjectDTO> getImportObject(User user, boolean management) {
        return getImportObjectByDescribeDefineType(user, management, null);
    }

    private List<IObjectDescribe> getDescribeList(User user, boolean management, String describeDefineType) {
        // 管理后台的请求，只返回人员对象
        if (management) {
            final HashSet<String> orgApiNames = Sets.newHashSet(PERSONNEL_OBJ_API_NAME);
            if (OrganizationConfigUtil.isImportDimensionGray(user.getTenantId())) {
                orgApiNames.add(DIMENSION_OBJ_API_NAME);
            }
            if (OrganizationConfigUtil.isImportDepartmentGray(user.getTenantId())) {
                orgApiNames.add(DEPARTMENT_OBJ_API_NAME);
            }
            List<IObjectDescribe> describeList = describeLogicService.findObjects(user.getTenantId(), orgApiNames).values().stream().collect(Collectors.toList());
            return describeList;
        }
        List<IObjectDescribe> describeList = describeLogicService.findObjectsByTenantId(user.getTenantId(), describeDefineType,
                true, false, false, true, ObjectListConfig.IMPORT);
        // 过滤功能权限
        return describeLogicService.filterDescribesWithActionCode(user,
                describeList, ObjectAction.BATCH_IMPORT.getActionCode());
    }

    private List<ImportObjectDTO> getObjectList(List<IObjectDescribe> objectList, String tenantId) {
        if (CollectionUtils.empty(objectList)) {
            return Lists.newArrayList();
        }
        Map<String, IObjectDescribe> describeMap = objectList.stream().collect(toMap(IObjectDescribe::getApiName, x -> x, (x, y) -> x, LinkedHashMap::new));

        // 特殊处理目标值、销售记录、多单位和商品产品的关联对象
        fullObjectDescribeByApiNames(tenantId, describeMap, Lists.newArrayList(GOAL_VALUE_API_NAME, SALE_EVENT_API_NAME,
                MULTI_UNIT_RELATED_API_NAME, SUBPRODUCTCATALOG_API_NAME, PRODUCT_GROUP_API_NAME));
        // 合并objectOrder和describeMap中的对象apiName。确保不会少对象
        List<String> apiNames = Stream.of(AppFrameworkConfig.getObjectOrder(), describeMap.keySet()).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        // 特殊处理产品组合对象,如果有子产品明细导入权限，产品组合就支持导入
        if (!apiNames.contains(SUBPRODUCT_API_NAME)) {
            apiNames.remove(SUBPRODUCTCATALOG_API_NAME);
        }
        if (!apiNames.contains(BOM_API_NAME)) {
            apiNames.remove(PRODUCT_GROUP_API_NAME);
        }
        // 销售记录导入灰度配置开关
        if (ActiveRecordConfig.isInActiveRecordImportGray(tenantId)) {
            apiNames.remove(SALE_EVENT_API_NAME);
        }
        //key:apiName、value:objectCode
        List<Tuple<String, String>> tupleList = apiNames.stream()
                .flatMap(apiName -> getUnionImport().getOrDefault(apiName, singletonList(Tuple.of(apiName, apiName))).stream())
                .collect(Collectors.toList());
        return tupleList.stream().map(x -> {
            ObjectImportInitProvider provider = objectImportInitManager.getLocalProvider(getApiName(x.getValue()));
            return provider.getImportObject(describeMap.get(x.getKey()), null);
        }).filter(Optional::isPresent).map(Optional::get).map(ImportObjectDTO::from).collect(Collectors.toList());
    }

    private String getApiName(String objectCode) {
        return getApiNameByObjectCode(objectCode);
    }

    private void fullObjectDescribeByApiNames(String tenantId, Map<String, IObjectDescribe> describeMap,
                                              Collection<String> apiNames) {
        Map<String, IObjectDescribe> describes = describeLogicService.findObjects(tenantId, apiNames);
        describes.forEach(describeMap::putIfAbsent);

    }

    @ServiceMethod("executeExportTaskHookFunction")
    public ExecuteExportTaskHookFunction.Result executeExportTaskHookFunction(ServiceContext serviceContext, ExecuteExportTaskHookFunction.Arg arg) {
        ExportTaskHookService.Arg hookArg = new ExportTaskHookService.Arg();
        hookArg.setDescribeApiName(arg.getDescribeApiName());
        hookArg.setType(arg.getImportType());
        hookArg.setPath(arg.getFilePath());
        hookArg.setOriginalFilename(arg.getOriginalFilename());
        hookArg.setFileType(arg.getFileType());
        hookArg.setExpiredDay(arg.getExpiredDay());
        ExportTaskHookService.Result result;
        if ("before".equalsIgnoreCase(arg.getType())) {
            result = exportTaskHookService.before(serviceContext.getUser(), hookArg);
        } else {
            result = exportTaskHookService.after(serviceContext.getUser(), hookArg);
        }
        return ExecuteExportTaskHookFunction.Result.builder()
                .filePath(result.getFilePath())
                .build();
    }

}
