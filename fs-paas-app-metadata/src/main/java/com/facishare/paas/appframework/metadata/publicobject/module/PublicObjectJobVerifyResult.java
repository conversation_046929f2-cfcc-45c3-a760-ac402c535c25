package com.facishare.paas.appframework.metadata.publicobject.module;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.publicobject.verify.VerifyResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2023/12/23
 */
@Data
public class PublicObjectJobVerifyResult {
    private final List<String> messageList;
    private final List<InternationalVerifyMessage> verifyMessages;
    private final IObjectDescribe describe;
    private final List<EnterpriseRelationSimpleInfo> enterpriseRelationSimpleInfos;

    private PublicObjectJobVerifyResult(List<String> messageList, List<InternationalVerifyMessage> verifyMessages, IObjectDescribe describe,
                                        List<EnterpriseRelationSimpleInfo> enterpriseRelationSimpleInfos) {
        this.messageList = ImmutableList.copyOf(messageList);
        this.verifyMessages = ImmutableList.copyOf(verifyMessages);
        this.describe = describe;
        this.enterpriseRelationSimpleInfos = enterpriseRelationSimpleInfos;
    }

    public boolean success() {
        return CollectionUtils.empty(messageList);
    }

    public static PublicObjectJobVerifyResult fromVerifyResult(User user, VerifyResult verifyResult,
                                                               IObjectDescribe describe, PublicObjectJobParamVerifyInfo jobParam) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        Map<String, IObjectDescribe> describeExtMap = Maps.newHashMap();

        if (Objects.equals(user.getTenantId(), jobParam.getUpstreamTenantId())) {
            for (IFieldDescribe publicField : describeExt.getPublicFields()) {
                describeExt.copyField4Ext(describeExtMap, publicField);
            }
        }
        return new PublicObjectJobVerifyResult(verifyResult.getMessageList(), verifyResult.getMessages(), describeExtMap.get(describe.getApiName()),
                Lists.newArrayList(CollectionUtils.nullToEmpty(jobParam.getEnterpriseRelationSimpleInfos())));

    }

    public String getMessage() {
        if (success()) {
            return null;
        }
        if (CollectionUtils.notEmpty(verifyMessages)) {
            return verifyMessages.stream()
                    .map(InternationalVerifyMessage::toString)
                    .collect(Collectors.joining(","));
        }
        return String.join(";", messageList);
    }
}
