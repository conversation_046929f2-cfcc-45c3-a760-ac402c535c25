package com.facishare.crm.privilege.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.common.exception.CRMErrorCode;
import com.facishare.crm.common.exception.CrmCheckedException;
import com.facishare.crm.privilege.controller.arg.BatchAddUsersToRolesArg;
import com.facishare.crm.privilege.controller.arg.UpdateUserRoleArg;
import com.facishare.crm.privilege.controller.arg.*;
import com.facishare.crm.privilege.exception.PaasPrivilegeException;
import com.facishare.crm.privilege.model.valueobject.CrmResult;
import com.facishare.crm.privilege.swagger.ApiClient;
import com.facishare.crm.privilege.swagger.ApiException;
import com.facishare.crm.privilege.swagger.SwaggerClientUtil;
import com.facishare.crm.privilege.swagger.api.PaasAuthApiApi;
import com.facishare.crm.privilege.swagger.api.QueryApi;
import com.facishare.crm.privilege.swagger.api.UserRoleApi;
import com.facishare.crm.privilege.swagger.model.*;
import com.facishare.crm.privilege.util.*;
import com.facishare.crm.privilege.util.pb.DepartmentInfoVo;
import com.facishare.crm.privilege.util.pb.EmployeeInfoVo;
import com.facishare.crm.privilege.util.pb.QueryQuotaResult;
import com.facishare.crm.privilege.util.pb.SaveUsersResult;
import com.facishare.crm.valueobject.SessionContext;
import com.facishare.organization.adapter.api.model.biz.department.Department;
import com.facishare.organization.adapter.api.model.biz.department.arg.BatchGetDepartmentArg;
import com.facishare.organization.adapter.api.model.biz.departmentmember.MainDepartment;
import com.facishare.organization.adapter.api.model.biz.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.adapter.api.model.biz.employee.arg.BatchGetEmployeeIdsByDepartmentIdsArg;
import com.facishare.organization.adapter.api.service.DepartmentService;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.privilege.JudgeMangePrivilegeService;
import com.facishare.paas.appframework.privilege.UserRoleInfoProxy;
import com.facishare.paas.appframework.privilege.dto.ForbiddenUsers;
import com.facishare.paas.appframework.privilege.dto.JudgeMangePrivilegeResult;
import com.facishare.paas.appframework.privilege.dto.QueryRoleInfoListByForbiddenUsers;
import com.facishare.paas.appframework.privilege.util.FunctionPrivillegeConfig;
import com.fxiaoke.release.FsGrayRelease;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.Example;
import io.swagger.annotations.ExampleProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.ws.rs.HeaderParam;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.crm.privilege.util.LogUtil.*;
import static com.facishare.paas.appframework.privilege.util.Headers.PAAS_PRIVILEGE_HEADDER;
import static org.apache.commons.collections4.ListUtils.intersection;

@Service
@Slf4j
@Deprecated
public class UserService {

    private IChangeableConfig props;
    @Autowired
    private OpenService openService;
    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private PushSessionService pushSessionService;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private SyncCrmVisibleService syncCrmVisibleService;
    @Autowired
    private ForbiddenUsersService forbiddenUsersService;
    @Autowired
    private UserRoleInfoProxy userRoleInfoProxy;
    @Autowired
    private JudgeMangePrivilegeService judgeMangePrivilegeService;
    @Autowired
    private EmployeeService orgAdapterApiEmployeeService;
    @Autowired
    private DepartmentService orgAdapterApiDepartmentService;
    private ApiClient authApiClient;
    private ApiClient orgApiClient;
    private PaasAuthApiApi paasAuthApiApi = SwaggerApiUtil.getPaasAuthApiApi();
    private String dhtRoleCode = "00000000000000000000000000000021";

    public static Map<String, Object> createPageInfo(int pageSize, int pageNumber, int pageCount, long totalCount, Map map, Map userInfo) {
        Map<String, Object> page = new HashMap<>();
        Map<String, Object> pageInfo = new HashMap<>();
        pageInfo.put("pageSize", pageSize);
        pageInfo.put("pageNumber", pageNumber);
        pageInfo.put("pageCount", pageCount);
        pageInfo.put("totalCount", totalCount);
        page.put("map", map);
        page.put("page", pageInfo);
        page.put("userInfo", userInfo);
        return page;
    }

    @PostConstruct
    private void init() {
        props = ConfigFactory.getConfig(Constant.getFS_CRM_PRIVILEGE());
        log.info("UserService props {}", Arrays.toString(props.getLines().toArray()));
        authApiClient = SwaggerClientUtil.getApiClient(Constant.BASE_URL_AUTH_KEY, props);
        orgApiClient = SwaggerClientUtil.getApiClient(Constant.BASE_URL_ORG_KEY, props);
    }


    private List<String> getUserIdsByDepartment(SessionContext sessionContext, List<Integer> departmentIds) {
        if (CollectionUtils.isEmpty(departmentIds)) {
            return Lists.newArrayList();
        }
        final BatchGetEmployeeIdsByDepartmentIdsArg arg1 = new BatchGetEmployeeIdsByDepartmentIdsArg();
        arg1.setDepartmentIds(departmentIds);
        arg1.setIncludeLowDepartment(true);
        arg1.setRunStatus(com.facishare.organization.adapter.api.model.biz.RunStatus.ACTIVE);
        arg1.setMainDepartment(MainDepartment.ALL);
        arg1.setEnterpriseId(sessionContext.getEId().intValue());
        return orgAdapterApiEmployeeService.batchGetEmployeeIdsByDepartmentIds(arg1)
                .getEmployeeIds()
                .stream()
                .map(String::valueOf)
                .collect(Collectors.toList());
    }

    /**
     * 用户原有角色 与 新角色合并
     */
    public CrmResult batchAddUsersToRoles(BatchAddUsersToRolesArg arg, SessionContext sessionContext) {
        log.info("batchAddUsersToRoles..");
        CrmResult crmResult = new CrmResult();
        if (StringUtils.isEmpty(arg.getRoleCodes()) || StringUtils.isEmpty(arg.getMajorRoleCode()) || arg.getUpdateFlag() == null) {
            log.error(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getMessage());
            crmResult.setMsg(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getMessage());
            crmResult.setCode(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getCode());
            return crmResult;
        }

        Set<String> userIds = convertToEmployeeIdsArg(arg.getUserIds(), arg.getEmployeeIds(), arg.getDepartmentIds(), sessionContext);

        Set<String> roleCodes = Sets.newHashSet(arg.getRoleCodes().split(","));

        String adminCode = GetRoleUtils.getAdminRoleCode();
        if (roleCodes.contains(adminCode)) {
            roleCodes.remove(adminCode);
        }

        List<Integer> addUserIdList = Lists.newArrayList();
        try {
            userIds.forEach(uid -> {
                addUserIdList.add(Integer.valueOf(uid));
            });
        } catch (NumberFormatException e) {
            log.error("error", e);
            crmResult.setMsg(I18N.text(I18NKey.PARAM_ERROR));
            crmResult.setCode(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getCode());
            return crmResult;
        }

        if (!addUserIdList.isEmpty()) {
            try {
                SaveUsersResult saveUsersResult = openService.saveUsers(sessionContext.getEa(), addUserIdList);
                if (200 != saveUsersResult.getCode()) {//200成功，75050配额不足，另外就是其他错误
                    crmResult.setCode(saveUsersResult.getCode());
                    crmResult.setMsg(I18N.text(I18NKey.QUOTA_LACK));
                    return crmResult;
                }
                //从禁用名单中剔除
                List<String> allUserIds = Lists.newArrayList(SetUtils.emptyIfNull(userIds));
                List<Integer> allUserIdsInteger = allUserIds.stream()
                        .map(o -> Integer.valueOf(o))
                        .collect(Collectors.toList());
                com.facishare.paas.appframework.privilege.dto.AuthContext forbiddenUsersAuthContext =
                        getBuild(sessionContext);
                ForbiddenUsers.Arg forbiddenUsersArg = new ForbiddenUsers.Arg(forbiddenUsersAuthContext, allUserIds, false);
                forbiddenUsersService.updateForbiddenUsers(forbiddenUsersArg);
            } catch (IOException e) {
                log.error("error", e);
                crmResult.setCode(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getCode());
                crmResult.setMsg(I18N.text(I18NKey.QUOTA_FAIL));
                return crmResult;
            }
        }

        //审计日志
        taskExecutor.execute(() -> authLog(sessionContext, userIds, arg.getMajorRoleCode(), roleCodes, bizOperationName_Add));

        BaseResult baseResult;

        AddRoleUserArg addRoleUserArg = new AddRoleUserArg();
        AuthContext authContext = getAuthContext(sessionContext);
        try {
            addRoleUserArg.setRoles(Lists.newArrayList(roleCodes));
            addRoleUserArg.setUsers(Lists.newArrayList(userIds));
            addRoleUserArg.setDefaultRole(arg.getMajorRoleCode());
            addRoleUserArg.setUpdateDefaultRole(arg.getUpdateFlag());
            addRoleUserArg.setAuthContext(authContext);

            baseResult = paasAuthApiApi.addRoleToUser(addRoleUserArg);
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "addRoleToUser", JSONObject.toJSON(addRoleUserArg), e);
            crmResult.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
            crmResult.setMsg(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getMessage());
            return crmResult;
        }

        if (baseResult.getErrCode() != 0) {
            crmResult.setCode(baseResult.getErrCode());
            crmResult.setMsg(baseResult.getErrMessage());
            return crmResult;
        }

        List<Integer> uids = Lists.newArrayList();
        userIds.forEach(uid -> {
            uids.add(Integer.valueOf(uid));
        });
        taskExecutor.execute(() -> pushSessionService.pushSessionByFieldPrivilegeUpdate(Lists.newArrayList(uids), sessionContext.getEa(), null));
        //CRM通知
        taskExecutor.execute(() -> crmPushToUsers(sessionContext, roleCodes, userIds));

        crmResult.setResult(Boolean.TRUE);
        return crmResult;
    }

    private com.facishare.paas.appframework.privilege.dto.AuthContext getBuild(SessionContext sessionContext) {
        return com.facishare.paas.appframework.privilege.dto.AuthContext.builder()
                .userId(String.valueOf(sessionContext.getUserId()))
                .appId(Constant.APP_ID)
                .tenantId(String.valueOf(sessionContext.getEId()))
                .build();
    }

    private Map<String, Set<String>> getUsersRoleCodes(SessionContext sessionContext, Set<String> userIds) throws ApiException {
        Map<String, Set<String>> userId_roles = Maps.newHashMap();

        UserRoleApi api = new UserRoleApi(authApiClient);
        QueryUserRoleCodesByUsersArg uarg = new QueryUserRoleCodesByUsersArg();
        AuthContext authContext = getAuthContext(sessionContext);
        uarg.setAuthContext(authContext);
        uarg.setUsers(Lists.newArrayList(userIds));


        try {
            Map<String, List<String>> result = api.queryUserRoleCodesByUsers(uarg).getResult();
            for (Map.Entry<String, List<String>> e : result.entrySet()) {
                userId_roles.put(e.getKey(), Sets.newHashSet(e.getValue()));
            }
            return userId_roles;
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "userRoleInfo", JSONObject.toJSON(uarg), e);
            throw e;
        }

    }

    private void authLog(SessionContext sessionContext, Set<String> userIds, String majorRoleCode, Set<String> roleCodes, Integer bizOperationName) {
        long eid = sessionContext.getEId();
        int loginUserId = sessionContext.getUserId();

        if ("1".equals(props.get("audit_log", "0"))) {
            List<UserInfo> userInfos = userInfoService.getUserNames(Lists.newArrayList(userIds), eid, loginUserId);
            Map<String, UserInfo> userInfoMap = Maps.newHashMap();
            userInfos.forEach(ui -> {
                userInfoMap.put(ui.getId(), ui);
            });
            for (String uid : userIds) {
                // 员工 {员工昵称}，角色 {角色名称、...}；员工 {员工昵称}，角色 {角色名称、...}
                // 审计日志
                String userName = null == userInfoMap.get(uid) ? "" : userInfoMap.get(uid).getName();
                List<RolePojo> rolePojos = null;
                try {
                    rolePojos = getRoleByCode(roleCodes, eid, loginUserId);
                } catch (ApiException e) {
                    log.error("error", e);
                    continue;
                }
                String roleNames = roleNameJoin(rolePojos);

                String logStr;
                //删除人员角色不需要记录主角色
                if (bizOperationName.equals(bizOperationName_Delete)) {
                    logStr = I18N.text(I18NKey.USER_ROLE, userName, roleNames);
                } else {
                    String majorRoleName = rolePojos.stream()
                            .filter(item -> item.getRoleCode().equals(majorRoleCode))
                            .map(RolePojo::getRoleName)
                            .findAny()
                            .orElseGet(() -> "");
                    logStr = I18N.text(I18NKey.USER_MAJOR_ROLE, userName, majorRoleName, roleNames);
                }

                Map<String, Object> log = LogUtil.buildLOG(String.valueOf(eid), bizOperationName, userInfoService.getLoginUserName(eid, loginUserId), String.valueOf(loginUserId), logStr, "41");
                AuditLogUtil.sendLog(JSONObject.toJSONString(log));
            }

        }
    }

    private String roleNameJoin(List<RolePojo> rolePojos) {
        if (null == rolePojos || rolePojos.isEmpty()) return "";
        StringBuilder roleNames = new StringBuilder(rolePojos.get(0).getRoleName());
        for (int i = 1; i < rolePojos.size(); i++) {
            roleNames.append(",").append(rolePojos.get(i).getRoleName());
        }
        return roleNames.toString();
    }

    private List<RolePojo> getRoleByCode(Set<String> roleCodes, Long eid, Integer loginUserId) throws ApiException {

        UserRoleApi api = new UserRoleApi(authApiClient);

        AuthContext authContext = new AuthContext();
        authContext.setTenantId(String.valueOf(eid));
        authContext.setUserId(String.valueOf(loginUserId));
        authContext.setAppId(Constant.getAppId());
        QueryRoleInfoWithRoleCodesArg qarg = new QueryRoleInfoWithRoleCodesArg();
        qarg.setAuthContext(authContext);
        qarg.setRoleCodes(Lists.newArrayList(roleCodes));
        RoleResponse roleResponse;
        try {
            roleResponse = api.queryRoleInfoWithCodes(qarg);
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "queryRoleInfoWithCodes", JSONObject.toJSON(qarg), e);
            throw e;
        }

        if (0 != roleResponse.getErrCode()) {
            log.error(Constant.paas_error_log + " code={} msg={}", roleResponse.getErrCode(), roleResponse.getErrMessage());
        } else {
            return roleResponse.getResult().getRoles();
        }
        return Lists.newArrayList();
    }

    private CrmResult requestParamCheck(String... params) {
        for (String param : params) {
            if (StringUtils.isEmpty(param)) {
                CrmResult crmResult = new CrmResult();
                crmResult.setCode(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getCode());
                crmResult.setMsg(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getMessage());
                return crmResult;
            }
        }
        return null;
    }

    private CrmResult requestParamCheck(List<String> userIds, String... params) {

        CrmResult crmResult = new CrmResult();
        crmResult.setCode(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getCode());
        crmResult.setMsg(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getMessage());
        if (CollectionUtils.isEmpty(userIds)) {
            return crmResult;
        }

        for (String param : params) {
            if (StringUtils.isEmpty(param)) {

                return crmResult;
            }
        }
        return null;
    }

    private CrmResult requestParamCheck(Map map) {
        CrmResult crmResult = new CrmResult();
        crmResult.setCode(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getCode());
        crmResult.setMsg(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getMessage());
        if (MapUtils.isEmpty(map)) {
            return crmResult;
        }

        return null;
    }

    public CrmResult updateUserRole(UpdateUserRoleArg arg, SessionContext sessionContext) {
        log.info("updateUserRole..");

        CrmResult errorCrmResult = requestParamCheck(arg.getUserId(), arg.getRoleCodes(), arg.getMajorRoleCode());
        if (requestParamCheck() != null) return errorCrmResult;


        CrmResult crmResult = new CrmResult();

        Set<String> roles = Sets.newHashSet(arg.getRoleCodes().split(","));
        String adminRoleCode = GetRoleUtils.getAdminRoleCode();
        Map<String, Set<String>> userIdOldRoleCodes;
        try {
            userIdOldRoleCodes = getUsersRoleCodes(sessionContext, Sets.newHashSet(arg.getUserId()));
        } catch (ApiException e) {
            log.error("error", e);
            crmResult.setMsg(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getMessage());
            crmResult.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
            return crmResult;
        }

        // 如果一个人原来是crm管理员,那么更新后的角色才能有crm管理员
        Set<String> userRoles = userIdOldRoleCodes.get(arg.getUserId());
        if (userRoles != null && userRoles.contains(adminRoleCode)) { //不允许删除CRM管理员角色 只能开平删除 通过MQ 同步过来
            roles.add(adminRoleCode);
        } else {
            roles.remove(adminRoleCode);
        }

        try {
            if (!updateUserRole(crmResult, roles, arg.getUserId(), arg.getMajorRoleCode(), sessionContext, bizOperationName_Modify)) {
                return crmResult;
            }
        } catch (ApiException e) {
            crmResult.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
            crmResult.setMsg(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getMessage() + e);
            return crmResult;
        }
        // CRM通知
        crmPushToUserBath(sessionContext, roles, userIdOldRoleCodes);

        List<Integer> adminUids = Lists.newArrayList();
        adminUids.add(Integer.valueOf(arg.getUserId()));
        pushSessionService.pushSessionByFieldPrivilegeUpdate(Lists.newArrayList(adminUids), sessionContext.getEa(), null);

        crmResult.setResult(Boolean.TRUE);
        return crmResult;
    }

    public CrmResult crmUserList(CrmUserListArg arg, SessionContext sessionContext) {
        log.info("crmUserList..");
        CrmResult crmResult = new CrmResult();
        JudgeMangePrivilegeResult judgeMangePrivilegeResult = getJudgeMangePrivilegeResult(sessionContext);
        if (judgeMangePrivilegeResult.isHavePrivilege()) {
            String deptId = arg.getDeptId();
            String username = arg.getUsername();

            List<String> users = getManangeUsers(sessionContext, judgeMangePrivilegeResult, deptId, username);

            try {
                Map<String, Object> m = searchUsers(users, arg.getPageNumber(), arg.getPageSize(), null, sessionContext);
                crmResult.setResult(m);
                return crmResult;
            } catch (ApiException e) {
                crmResult.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
                crmResult.setMsg(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getMessage());
                return crmResult;
            }
        } else {
            crmResult.setCode(CRMErrorCode.FS_CRM_PRIVILEGE_PAAS_MANGE.getCode());
            crmResult.setMsg(CRMErrorCode.FS_CRM_PRIVILEGE_PAAS_MANGE.getMessage());
            return crmResult;
        }
    }

    private JudgeMangePrivilegeResult getJudgeMangePrivilegeResult(SessionContext sessionContext) {
        if (FunctionPrivillegeConfig.getCrmUserListGrayEnterpriseIds()
                .contains("*") || FunctionPrivillegeConfig.getCrmUserListGrayEnterpriseIds()
                .contains(String.valueOf(sessionContext.getEId()))) {
            return judgeMangePrivilegeService.judgeMangePrivilegeGray(sessionContext);
        }
        return judgeMangePrivilegeService.judgeMangePrivilege(sessionContext);
    }

    public List<String> queryUsers(String deptId, String username, SessionContext sessionContext) {
        log.info("queryUsers..");
        QueryApi api = new QueryApi(orgApiClient);
        DeptKeyArg deptKeyArg = new DeptKeyArg();
        deptKeyArg.setAppId(Constant.getAppId());
        if (StringUtils.isNotEmpty(deptId)) {
            deptKeyArg.setDeptList(Lists.newArrayList(deptId));
        }
        deptKeyArg.setTenantId(String.valueOf(sessionContext.getEId()));
        deptKeyArg.setUserId(String.valueOf(sessionContext.getUserId()));
        if (StringUtils.isNotEmpty(username))
            deptKeyArg.setKey(username);
        try {
            ListStringResult listStringResult = api.queryUserIdByDeptKey(deptKeyArg);
            return listStringResult.getResult();
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "queryUserIdByDeptKey", JSONObject.toJSON(deptKeyArg), e);
        }

        return Lists.newArrayList();
    }

    public CrmResult roleUsers(RoleUsersArg arg, SessionContext sessionContext) {
        log.info("roleUsers..");
        CrmResult crmResult = new CrmResult();
        JudgeMangePrivilegeResult judgeMangePrivilegeResult = getJudgeMangePrivilegeResult(sessionContext);
        if (judgeMangePrivilegeResult.isHavePrivilege()) {
            if (StringUtils.isEmpty(arg.getRoleCode())) {
                crmResult.setCode(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getCode());
                crmResult.setMsg(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getMessage());
                return crmResult;
            }
            String deptId = arg.getDeptId();
            String username = arg.getUsername();
            List<String> users = getManangeUsers(sessionContext, judgeMangePrivilegeResult, deptId, username);
            Map<String, Object> m = Maps.newHashMap();
            try {
                m = searchUsers(users, arg.getPageNumber(), arg.getPageSize(), arg.getRoleCode(), sessionContext);
            } catch (ApiException e) {
                log.error(Constant.paas_error_log);
                crmResult.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
                crmResult.setMsg(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getMessage());
                return crmResult;
            }
            crmResult.setResult(m);
            return crmResult;
        } else {
            log.error("error");
            crmResult.setCode(CRMErrorCode.FS_CRM_PRIVILEGE_PAAS_MANGE.getCode());
            crmResult.setMsg(CRMErrorCode.FS_CRM_PRIVILEGE_PAAS_MANGE.getMessage());
            return crmResult;
        }
    }

    private List<String> getManangeUsers(SessionContext sessionContext, JudgeMangePrivilegeResult judgeMangePrivilegeResult, String deptId, String username) {
        List<String> users = null;
        if (StringUtils.isNotBlank(deptId) || StringUtils.isNotBlank(username)) {
            List<String> possUsers = queryUsers(deptId, username, sessionContext);
            //分管范围全公司
            if (CollectionUtils.isEmpty(judgeMangePrivilegeResult.getEmployees())) {
                users = possUsers;
            } else {
                users = intersection(possUsers, judgeMangePrivilegeResult.getEmployees());
            }
        } else {
            users = judgeMangePrivilegeResult.getEmployees();
        }
        return users;
    }

    private Map<String, Object> searchUsers(List<String> users, Integer pageNumber, Integer pageSize, String roleCode, SessionContext sessionContext) throws ApiException {
        QueryUserRolesByUsersArg queryUserRolesByUsersArg = new QueryUserRolesByUsersArg();

        AuthContext authContext = getAuthContext(sessionContext);
        queryUserRolesByUsersArg.setAuthContext(authContext);

        PageInfo pageInfo = new PageInfo();
        pageInfo.setCurrentPage(pageNumber);
        pageInfo.setPageSize(pageSize);
        queryUserRolesByUsersArg.setPageInfo(pageInfo);
        queryUserRolesByUsersArg.setUsers(users);
        if (null != roleCode) {
            queryUserRolesByUsersArg.setRoleCode(roleCode);
        }

        //排除订货角色，排除PERSONNEL_ROLE_CODE不起效
        queryUserRolesByUsersArg.setExcludeRoles(Lists.newArrayList(dhtRoleCode));

        QueryRoleInfoListByUsersResponse queryRoleInfoListByUsersResponse;
        try {
            queryRoleInfoListByUsersResponse = paasAuthApiApi.queryRoleInfoListByUsers(queryUserRolesByUsersArg);
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "queryUserRolesByUsers", JSONObject.toJSON(queryUserRolesByUsersArg), e);
            throw e;
        }
        QueryRoleInfoListByUsersPageInfoResponse tmpResult = queryRoleInfoListByUsersResponse.getResult();
        PageInfo pageInfo1 = tmpResult.getPageInfo();

        Map<String, List<UserRoleInfoPojo>> user2Roles = tmpResult.getUserRoles();

        if (MapUtils.isNotEmpty(user2Roles)) {
            user2Roles.entrySet().forEach(entry -> {
                final List<UserRoleInfoPojo> value = entry.getValue();
                if (CollectionUtils.isNotEmpty(value)) {
                    entry.setValue(value.stream()
                            .filter(e -> !Objects.equals(e.getRoleCode(), GetRoleUtils.PERSONNEL_ROLE_CODE))
                            .collect(Collectors.toList()));
                }
            });
        }

        Map<String, EmployeeInfoVo> userInfoMap = convertToEmployeeInfoVo(user2Roles.keySet(), sessionContext.getEId()
                .intValue(), sessionContext.getUserId());

        return createPageInfo(pageInfo1.getPageSize(), pageInfo1.getCurrentPage(), pageInfo1.getTotalPage(), pageInfo1.getTotal(), user2Roles, userInfoMap);
    }

    public Map<String, EmployeeInfoVo> convertToEmployeeInfoVo(Set<String> employeeIdStrSet, Integer enterpriseId, Integer userId) {
        if (CollectionUtils.isEmpty(employeeIdStrSet)) {
            return Maps.newHashMap();
        }

        final BatchGetEmployeeDtoArg arg = new BatchGetEmployeeDtoArg();
        arg.setEmployeeIds(employeeIdStrSet.stream().map(Integer::valueOf).collect(Collectors.toList()));
        arg.setRunStatus(com.facishare.organization.adapter.api.model.biz.RunStatus.ALL);
        arg.setEnterpriseId(enterpriseId);
        arg.setCurrentEmployeeId(userId);
        final List<EmployeeDto> employees = orgAdapterApiEmployeeService.batchGetEmployeeDto(arg).getEmployees();

        Set<Integer> departmentIds = Sets.newHashSet();
        employees.forEach(e -> departmentIds.addAll(e.getDepartmentIds()));

        final BatchGetDepartmentArg arg1 = new BatchGetDepartmentArg();
        arg1.setDepartmentIds(Lists.newArrayList(departmentIds));
        arg1.setRunStatus(RunStatus.ACTIVE);
        arg1.setEnterpriseId(enterpriseId);
        arg1.setCurrentEmployeeId(userId);
        final Map<Integer, Department> departmentMap = orgAdapterApiDepartmentService.batchGetDepartment(arg1)
                .getDepartments().stream()
                .collect(Collectors.toMap(Department::getDepartmentId, Function.identity()));

        return employees.stream().map(employeeDto -> {
            final EmployeeInfoVo employeeInfo = new EmployeeInfoVo();
            employeeInfo.setEmployeeId(String.valueOf(employeeDto.getEmployeeId()));
            employeeInfo.setName(employeeDto.getName());
            final Department department = departmentMap.get(employeeDto.getMainDepartmentId());
            if (Objects.nonNull(department)) {
                employeeInfo.setMainDepartment(new DepartmentInfoVo(department.getDepartmentId(), department.getName()));
            }
            employeeInfo.setPost(employeeDto.getPost());
            if (CollectionUtils.isNotEmpty(employeeDto.getDepartmentIds())) {
                employeeInfo.setDepartmentInfoList(employeeDto.getDepartmentIds()
                        .stream()
                        .map(departmentMap::get)
                        .filter(Objects::nonNull)
                        .map(departmentDto -> new DepartmentInfoVo(departmentDto.getDepartmentId(), departmentDto.getName()))
                        .collect(Collectors.toList()));
            }
            employeeInfo.setStatus(employeeDto.getStatus().getValue());
            return employeeInfo;
        }).collect(Collectors.toMap(EmployeeInfoVo::getEmployeeId, Function.identity()));
    }

    public CrmResult queryQuota(QueryQuotaArg arg, SessionContext sessionContext) {
        CrmResult crmResult = new CrmResult();
        Map<String, Object> resultValue = Maps.newHashMapWithExpectedSize(2);

        try {
            Set<Integer> userIds = openService.queryUsers(sessionContext.getEa()).getUserIds();
            int userNum = CollectionUtils.isEmpty(userIds) ? 0 : userIds.size();
            //已使用配额
            resultValue.put("userNum", userNum);
        } catch (IOException e) {
            log.error(Constant.paas_error_log);
            crmResult.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
            crmResult.setMsg(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getMessage());
            return crmResult;
        }

        try {
            QueryQuotaResult quotaResult;
            quotaResult = openService.queryQuota(sessionContext.getEa());
            Integer quota = quotaResult.getQuota();
            //总配额
            resultValue.put("quota", quota);
        } catch (IOException e) {
            log.error(Constant.open_error_log, e);
            crmResult.setCode(CRMErrorCode.PRIVILEGE_SYSTEM_ERROR.getCode());
            crmResult.setMsg(CRMErrorCode.PRIVILEGE_SYSTEM_ERROR.getMessage());
            return crmResult;
        }

        RequestContext requestContext = RequestContextManager.getContext();
        com.facishare.paas.appframework.privilege.dto.AuthContext authContext = getAuthContext(requestContext);
        Map<String, Integer> pageMap = Maps.newHashMap();
        pageMap.put("pageSize", 1);
        pageMap.put("currentPage", 1);
        QueryRoleInfoListByForbiddenUsers.Arg queryRoleInfoListByForbiddenUsersArg =
                new QueryRoleInfoListByForbiddenUsers.Arg(authContext, Boolean.TRUE, null, pageMap);
        QueryRoleInfoListByForbiddenUsers.Result result = userRoleInfoProxy.queryRoleInfoListByForbiddenUsers(queryRoleInfoListByForbiddenUsersArg,
                PAAS_PRIVILEGE_HEADDER.defaultHeader());
        Integer total = result.getResult().getPageInfo().get("total");
        //禁用CRM用户数
        resultValue.put("total", total);

        crmResult.setResult(resultValue);
        return crmResult;
    }

    private com.facishare.paas.appframework.privilege.dto.AuthContext getAuthContext(RequestContext requestContext) {
        return com.facishare.paas.appframework.privilege.dto.AuthContext.builder()
                .tenantId(requestContext.getTenantId())
                .appId(Constant.APP_ID)
                .userId(requestContext.getUser().getUserId())
                .outerTenantId(requestContext.getUser().isOutUser() ? requestContext.getUser().getOutTenantId() : null)
                .build();
    }

    public CrmResult copyUserRoleToUsers(CopyUserRoleToUserArg arg, SessionContext sessionContext) {
        //增加crm可见范围
        Set<String> allUserIds = convertToEmployeeIdsArg(arg.getUserIds(), arg.getEmployeeIds(), arg.getDepartmentIds(), sessionContext);

        if (!FsGrayRelease.isAllow("auth", "checkLicenseLimit", String.valueOf(sessionContext.getEId()))) {
            List<Integer> allUserIdsInteger = allUserIds.stream().map(Integer::valueOf).collect(Collectors.toList());
            CrmResult crmResult1 = syncCrmVisibleService.syncOpenCrmVisible(allUserIdsInteger, sessionContext.getEa());
            if (crmResult1.getCode() != CrmResult.SUCCESS) {
                return crmResult1;
            }

//        从禁用名单中剔除
            com.facishare.paas.appframework.privilege.dto.AuthContext forbiddenUsersAuthContext =
                    getBuild(sessionContext);
            ForbiddenUsers.Arg forbiddenUsersArg = new ForbiddenUsers.Arg(forbiddenUsersAuthContext, Lists.newArrayList(allUserIds), false);
            forbiddenUsersService.updateForbiddenUsers(forbiddenUsersArg);
        }


        CrmResult errorCrmResult = requestParamCheck(arg.getUserIdOld(), arg.getUserIds(), arg.getMajorRoleCode());
        if (allUserIds.size() > AppFrameworkConfig.getBatch_Copy_Role_To_User_Limit()) {
            CrmResult crmResult = new CrmResult();
            crmResult.setCode(CRMErrorCode.PRIVILEGE_TOO_MANY.getCode());
            crmResult.setMsg(I18N.text(CRMErrorCode.PRIVILEGE_TOO_MANY.getMessage()));
            return crmResult;
        }
        if (requestParamCheck() != null) return errorCrmResult;

        CrmResult crmResult = new CrmResult();

        List<RolePojo> roleList;
        try {
            roleList = queryUserRole(arg.getUserIdOld(), sessionContext);
        } catch (ApiException e) {
            log.error("error", e);
            crmResult.setMsg(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getMessage());
            crmResult.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
            return crmResult;
        }
        Set<String> roleCodes = Sets.newHashSet();
        roleList.forEach(r -> {
            roleCodes.add(r.getRoleCode());
        });

        // CRM通知
        //临时保存用户原有角色
        Map<String, Set<String>> userId_oldRoleCodes = null;
        try {
            userId_oldRoleCodes = getUsersRoleCodes(sessionContext, allUserIds);
        } catch (ApiException e) {
            log.error("error", e);
            crmResult.setMsg(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getMessage());
            crmResult.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
            return crmResult;
        }

        for (String userId : allUserIds) {

            Set<String> rCodes = Sets.newHashSet(roleCodes);

            String adminRoleCode = GetRoleUtils.getAdminRoleCode();
            if (userId_oldRoleCodes.get(userId).contains(adminRoleCode)) {
                rCodes.add(adminRoleCode);//CRM管理员角色不能删除
            } else {
                rCodes.remove(adminRoleCode);//如果原来不是CRM管理员 则不能添加CRM管理员角色
            }

            try {
                if (!updateUserRole(crmResult, rCodes, userId, arg.getMajorRoleCode(), sessionContext, bizOperationName_Add)) {
                    return crmResult;
                }
            } catch (ApiException e) {
                log.error("error", e);
                return crmResult;
            }
            crmPushToUser(sessionContext, rCodes, userId_oldRoleCodes.get(userId), userId);
        }

        List<Integer> uids = Lists.newArrayList();
        allUserIds.forEach(uid -> {
            uids.add(Integer.valueOf(uid));
        });
        pushSessionService.pushSessionByFieldPrivilegeUpdate(Lists.newArrayList(uids), sessionContext.getEa(), null);

        crmResult.setResult("success");
        return crmResult;
    }

    private Set<String> convertToEmployeeIdsArg(String userIds, List<Integer> employeeIds, List<Integer> departmentIds, SessionContext sessionContext) {
        Set<String> allUserIds = Sets.newHashSet();
        if (StringUtils.isNotBlank(userIds)) {
            allUserIds.addAll(Lists.newArrayList(userIds.split(",")));
        }

        if (CollectionUtils.isNotEmpty(employeeIds)) {
            allUserIds.addAll(employeeIds.stream().map(String::valueOf).collect(Collectors.toList()));
        }

        allUserIds.addAll(getUserIdsByDepartment(sessionContext, departmentIds));
        return allUserIds;
    }

    /**
     * 角色修改 给一批用户推送CRM通知
     */
    private void crmPushToUserBath(SessionContext sessionContext, Set<String> newRoleCodes, Map<String, Set<String>> userId_oldRoleCodes) {
        //CRM通知
        for (Map.Entry<String, Set<String>> userId_role : userId_oldRoleCodes.entrySet()) {
            Set<String> oldRoleCode = userId_role.getValue();
            String userId = userId_role.getKey();
            crmPushToUser(sessionContext, newRoleCodes, oldRoleCode, userId);
        }
    }

    private void crmPushToUsers(SessionContext sessionContext, Set<String> newRoleCodes, Set<String> userIds) {
        long eid = sessionContext.getEId();
        Integer loginUserId = sessionContext.getUserId();

        List<RolePojo> roleList = Lists.newArrayList();
        try {
            roleList = getRoleByCode(newRoleCodes, eid, loginUserId);
        } catch (ApiException e) {
            log.error("error", e);
        }

        Map<String, String> roleCode_roleName = Maps.newHashMap();
        roleList.forEach(rolePojo -> {
            roleCode_roleName.put(rolePojo.getRoleCode(), rolePojo.getRoleName());
        });
        for (String roleCode : newRoleCodes) {
            AddRemindRecordUtil.callRemindRecordMethod(sessionContext, userIds.stream()
                    .map(Integer::parseInt)
                    .collect(Collectors.toList()), roleCode_roleName.get(roleCode), AddRemindRecordUtil.getAddDec());
        }

    }

    /**
     * 角色修改 给单个用户推送CRM通知
     */
    private void crmPushToUser(SessionContext sessionContext, Set<String> newRoleCodes, Set<String> oldRoleCode, String userId) {

        long eid = sessionContext.getEId();
        Integer loginUserId = sessionContext.getUserId();

        Set<String> newRoles1 = Sets.newHashSet(newRoleCodes);
        Set<String> oldRoles1 = Sets.newHashSet(oldRoleCode);

//        Set<String> newRoles2 = Sets.newHashSet(newRoleCodes);
//        Set<String> oldRoles2 = Sets.newHashSet(oldRoleCode);

        Set<String> newRoles3 = Sets.newHashSet(newRoleCodes);
        Set<String> oldRoles3 = Sets.newHashSet(oldRoleCode);
        newRoles3.addAll(oldRoles3);

        List<RolePojo> roleList = Lists.newArrayList();
        try {
            roleList = getRoleByCode(newRoles3, eid, loginUserId);
        } catch (ApiException e) {
            log.error("error", e);
        }

        Map<String, String> roleCode_roleName = Maps.newHashMap();
        roleList.forEach(r -> {
            roleCode_roleName.put(r.getRoleCode(), r.getRoleName());
        });

        newRoles1.removeAll(oldRoles1);
        newRoles1.forEach(addRole -> { //增加的角色
            AddRemindRecordUtil.callRemindRecordMethod(sessionContext, Lists.newArrayList(Integer.valueOf(userId)), roleCode_roleName.get(addRole), AddRemindRecordUtil.getAddDec());
        });

//        oldRoles2.removeAll(newRoles2);
//        oldRoles2.forEach(delRole -> { //删除的角色
//            AddRemindRecordUtil.callRemindRecordMethod(loginUserId, Lists.newArrayList(Integer.valueOf(userId)), roleCode_roleName.get(delRole), AddRemindRecordUtil.DELETE_DEC, sessionContext.getEId().toString());
//        });
    }

    private boolean updateUserRole(CrmResult crmResult, Set<String> roleCodes, String userId, String majorRoleCode, SessionContext sessionContext, Integer optFlag) throws ApiException {
        long eid = sessionContext.getEId();
        int loginUserId = sessionContext.getUserId();

        Set<String> roles = Sets.newHashSet(roleCodes);
        //审计日志
        if (isAudit_log()) {
            String userName = userInfoService.getUserName(userId, eid, loginUserId);
            List<RolePojo> rolePojos;
            try {
                rolePojos = getRoleByCode(roleCodes, eid, loginUserId);
            } catch (ApiException e) {
                log.error("error", e);
                crmResult.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
                crmResult.setMsg(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getMessage());
                throw e;
            }
            String roleNames = roleNameJoin(rolePojos);
            String majorRoleName = rolePojos.stream()
                    .filter(item -> item.getRoleCode().equals(majorRoleCode))
                    .map(RolePojo::getRoleName)
                    .findAny()
                    .orElseGet(() -> "");
            String logStr = I18N.text(I18NKey.USER_MAJOR_ROLE, userName, majorRoleName, roleNames);
            Map<String, Object> log = LogUtil.buildLOG(String.valueOf(eid), optFlag, userInfoService.getLoginUserName(eid, loginUserId), String.valueOf(loginUserId), logStr, "41");
            AuditLogUtil.sendLog(JSONObject.toJSONString(log));
        }

        if (FsGrayRelease.isAllow("auth", "checkLicenseLimit", String.valueOf(eid))) {
            return updateUserRoleProcessing(crmResult, userId, majorRoleCode, sessionContext, roles);
        } else {
            boolean b;

            try {
                b = updateCRMUser(roleCodes, userId, sessionContext);
            } catch (IOException e) {
                log.error("error");
                crmResult.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
                crmResult.setMsg(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getMessage());
                return Boolean.FALSE;
            }
            if (b) { //如果可见范围修改成功才修改角色 如果修改可见范围不成功不能修改角色
                if (CollectionUtils.isNotEmpty(roles)) {
//                从禁用名单中剔除
                    com.facishare.paas.appframework.privilege.dto.AuthContext forbiddenUsersAuthContext =
                            getBuild(sessionContext);
                    ForbiddenUsers.Arg forbiddenUsersArg = new ForbiddenUsers.Arg(forbiddenUsersAuthContext, Arrays.asList(userId), false);
                    forbiddenUsersService.updateForbiddenUsers(forbiddenUsersArg);
                }

                return updateUserRoleProcessing(crmResult, userId, majorRoleCode, sessionContext, roles);
            } else {
                crmResult.setCode(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getCode());
                crmResult.setMsg(I18N.text(I18NKey.VISIBLE_SCOPE_FAIL));
                crmResult.setResult(Boolean.FALSE);
                return Boolean.FALSE;
            }
//
        }

    }

    private boolean updateUserRoleProcessing(final CrmResult crmResult, final String userId, final String majorRoleCode, final SessionContext sessionContext, final Set<String> roles) throws ApiException {
        AuthContext authContext = getAuthContext(sessionContext);
        UpdateRoleUserArg uarg = new UpdateRoleUserArg();
        uarg.setAuthContext(authContext);
        uarg.setRoles(Lists.newArrayList(roles));
        uarg.setUsers(Lists.newArrayList(userId));
        uarg.setDefaultRole(majorRoleCode);

        BaseResult baseResult;
        try {
            baseResult = paasAuthApiApi.updateRoleToUser(uarg);
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "updateUserRole", JSONObject.toJSON(uarg), e);
            crmResult.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
            crmResult.setMsg(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getMessage());
            throw e;
        }
        if (0 != baseResult.getErrCode()) {
            crmResult.setCode(baseResult.getErrCode());
            crmResult.setMsg(baseResult.getErrMessage());
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @NotNull
    private AuthContext getAuthContext(SessionContext sessionContext) {
        AuthContext authContext = new AuthContext();
        authContext.setTenantId(String.valueOf(sessionContext.getEId()));
        authContext.setUserId(String.valueOf(sessionContext.getUserId()));
        authContext.setAppId(Constant.getAppId());
        return authContext;
    }

    private boolean isAudit_log() {
        return "1".equals(props.get("audit_log", "0"));
    }

    /**
     * 修改可见 范围
     */
    private boolean updateCRMUser(Set<String> roleCodes, String userId, SessionContext sessionContext) throws ApiException, IOException {
        List<RolePojo> oldRoles;
        try {
            oldRoles = queryUserRole(userId, sessionContext);
        } catch (ApiException e) {
            log.error(Constant.paas_error_log, e);
            throw e;
        }

        //如果以前没有角色 现在新加角色开通可见范围
        if ((null == oldRoles || oldRoles.isEmpty()) && !roleCodes.isEmpty()) {
            try {
                List<Integer> userIds = Lists.newArrayList(Integer.valueOf(userId));
                SaveUsersResult saveUsersResult = openService.saveUsers(sessionContext.getEa(), userIds);
                if (200 != saveUsersResult.getCode()) return Boolean.FALSE;
            } catch (Exception e) {
                log.error("error", e);
                throw e;
            }
        }
        //如果有角色 现在角色全部删除   则移除范围
        if (null != oldRoles && !oldRoles.isEmpty() && roleCodes.isEmpty()) {
            try {
                List<Integer> userIds = Lists.newArrayList(Integer.valueOf(userId));
                openService.removeUsers(sessionContext.getEa(), userIds);
            } catch (Exception e) {
                log.error("error", e);
                throw e;
            }
        }
        return Boolean.TRUE;
    }

    public List<RolePojo> queryUserRole(String userId, SessionContext sessionContext) throws ApiException {

        UserRoleApi api = new UserRoleApi(authApiClient);
        UserRoleInfoArg uarg = new UserRoleInfoArg();
        AuthContext authContext = getAuthContext(sessionContext);
        uarg.setAuthContext(authContext);
        uarg.setUserId(userId);
        try {
            return api.userRoleInfo(uarg).getResult();
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "userRoleInfo", JSONObject.toJSON(uarg), e);
            throw e;
        }
    }

    public CrmResult removeCrmUser(RemoveCrmUserArg arg, SessionContext sessionContext) {

        CrmResult errorCrmResult = requestParamCheck(arg.getUserIds());
        if (requestParamCheck() != null) return errorCrmResult;

        CrmResult crmResult = new CrmResult();

        Set<String> emptyRoleUsers = Sets.newHashSet(arg.getUserIds().split(","));

        Map<String, Set<String>> userId_oldRoleCodes;
        try {
            userId_oldRoleCodes = getUsersRoleCodes(sessionContext, emptyRoleUsers);
        } catch (ApiException e) {
            log.error("error", e);
            crmResult.setMsg(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getMessage());
            crmResult.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
            return crmResult;
        }

        Set<String> crmAdminUser = Sets.newHashSet();
        Iterator<String> it = emptyRoleUsers.iterator();

        while (it.hasNext()) {
            String uid = it.next();
            boolean isAdmin = false;
            try {
                isAdmin = userInfoService.isAdmin(String.valueOf(sessionContext.getEId()), uid);
            } catch (CrmCheckedException e) {
                log.error("Call isAdmin error, errocode:" + e.getErrorCode().getStrCode(), e);
                crmResult.setMsg(e.getMessage());
                crmResult.setCode(e.getErrorCode().getCode());
                return crmResult;
            }
            if (isAdmin) {
                crmAdminUser.add(uid);
                it.remove();
            }
        }

        Set<String> adminRoleCodes = Sets.newHashSet(GetRoleUtils.getAdminRoleCode());
        if (!crmAdminUser.isEmpty()) {
            AuthContext authContext = getAuthContext(sessionContext);
            UpdateRoleUserArg uarg = new UpdateRoleUserArg();
            uarg.setRoles(Lists.newArrayList(adminRoleCodes));
            uarg.setUsers(Lists.newArrayList(crmAdminUser));
            uarg.setDefaultRole(GetRoleUtils.getAdminRoleCode());
            uarg.setAuthContext(authContext);

            BaseResult baseResult;
            try {
                baseResult = paasAuthApiApi.updateRoleToUser(uarg);
            } catch (ApiException e) {
                log.error(Constant.paas_error_log + " m={} p={}", "updateUserRoles", JSONObject.toJSON(uarg), e);
                crmResult.setMsg(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getMessage());
                crmResult.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
                return crmResult;
            }
            if (0 != baseResult.getErrCode()) {
                crmResult.setMsg(baseResult.getErrMessage());
                crmResult.setCode(baseResult.getErrCode());
                return crmResult;
            }
        }

        //CRM通知----------CRM 管理员----------------
        Map<String, Set<String>> admin_userId_oldRoleCodes = Maps.newHashMap();
        for (String uid : crmAdminUser) {
            admin_userId_oldRoleCodes.put(uid, userId_oldRoleCodes.get(uid));
        }
        crmPushToUserBath(sessionContext, adminRoleCodes, admin_userId_oldRoleCodes);
        //CRM通知----------CRM 管理员----------------

        Set<String> emptyRoles = Sets.newHashSet();
        for (String userId : emptyRoleUsers) {
            try {
                if (!updateUserRole(crmResult, emptyRoles, userId, null, sessionContext, bizOperationName_Delete))
                    return crmResult;
            } catch (ApiException e) {
                log.error("error", e);
                return crmResult;
            }
        }

        //CRM通知----------没有角色用户----------------
        Map<String, Set<String>> emptyRole_userId_oldRoleCodes = Maps.newHashMap();
        for (String uid : emptyRoleUsers) {

            admin_userId_oldRoleCodes.put(uid, userId_oldRoleCodes.get(uid));
        }

        crmPushToUserBath(sessionContext, emptyRoles, emptyRole_userId_oldRoleCodes);
        //CRM通知----------没有角色用户----------------

        List<Integer> uids = Lists.newArrayList();
        emptyRoleUsers.forEach(uid -> {
            uids.add(Integer.valueOf(uid));
        });
        pushSessionService.pushSessionByFieldPrivilegeUpdate(Lists.newArrayList(uids), sessionContext.getEa(), null);


        List<Integer> adminUids = Lists.newArrayList();
        crmAdminUser.forEach(uid -> {
            adminUids.add(Integer.valueOf(uid));
        });
        pushSessionService.pushSessionByFieldPrivilegeUpdate(Lists.newArrayList(adminUids), sessionContext.getEa(), null);

        crmResult.setResult("success");
        return crmResult;
    }

    public CrmResult addDhtRoleUser(@ApiParam(value = "企业ID") @HeaderParam("x-fs-ei") String tenantId, @ApiParam(examples = @Example(value = @ExampleProperty(mediaType = "json", value = "{\"userIds\":[\"100\",\"101\"]}")), required = true) String body) { // ignoreI18n
        CrmResult result = new CrmResult();
        List<String> userIdsList = Lists.newArrayList();
        if (checkParam(body, result, userIdsList)) return result;

        PaasAuthApiApi api = new PaasAuthApiApi(authApiClient);
        AuthContext authContext = new AuthContext();
        authContext.setTenantId(String.valueOf(tenantId));
        authContext.setUserId("-1000");
        authContext.setAppId(Constant.getAppId());

        AddRoleUserArg addArg = new AddRoleUserArg();
        addArg.setAuthContext(authContext);
        addArg.setUsers(userIdsList);
        addArg.setRoles(Lists.newArrayList(dhtRoleCode));
        addArg.setUpdateDefaultRole(Boolean.FALSE);
        addArg.setDefaultRole(dhtRoleCode);
        try {
            log.info("removeSailUser ei {} body {}", tenantId, body);

            if ("1".equals(props.get("audit_log", "0"))) {
                authLogUsers(tenantId, userIdsList, bizOperationName_Add, I18N.text(I18NKey.ORDER_PASS), Sets.newHashSet(dhtRoleCode));
            }
            api.addRoleToUser(addArg);
            result.setCode(0);
            result.setMsg("success");
        } catch (ApiException e) {
            log.error("error", e);
            result.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
            result.setMsg(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getMessage() + e.getMessage());
        }
        return result;
    }

    private boolean checkParam(@ApiParam(examples = @Example(value = @ExampleProperty(mediaType = "json", value = "{\"userIds\":[\"100\",\"101\"]}")), required = true) String body, CrmResult result, List<String> userIdsList) {
        try {
            JSONObject jsonObject = JSON.parseObject(body);
            JSONArray userIds = jsonObject.getJSONArray("userIds");
            for (int i = 0; i < userIds.size(); i++) {
                userIdsList.add(userIds.get(i).toString());
            }
        } catch (Exception e) {
            log.error("error", e);
            result.setCode(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getCode());
            result.setMsg(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getMessage());
            return true;
        }
        return false;
    }

    private void authLogUsers(String tenantId, List<String> userIdsList, Integer bizOperation, String loginUserName, Set<String> roleCodes) {
        long eid = Long.parseLong(tenantId);
        int loginUserId = -1000;
        List<UserInfo> userInfos = userInfoService.getUserNames(Lists.newArrayList(userIdsList), eid, loginUserId);
        Map<String, UserInfo> userInfoMap = Maps.newHashMap();
        userInfos.forEach(ui -> {
            userInfoMap.put(ui.getId(), ui);
        });
        for (String uid : userIdsList) {
//                员工 {员工昵称}，角色 {角色名称、...}；员工 {员工昵称}，角色 {角色名称、...}
            //审计日志
            String userName = null == userInfoMap.get(uid) ? "" : userInfoMap.get(uid).getName();
            List<RolePojo> rolePojos = null;
            try {
                rolePojos = getRoleByCode(roleCodes, eid, loginUserId);
            } catch (ApiException e) {
                log.error("error", e);
                continue;
            }
            String roleNames = roleNameJoin(rolePojos);

            String logStr = I18N.text(I18NKey.USER_ROLE, userName, roleNames);
            Map<String, Object> log = LogUtil.buildLOG(String.valueOf(eid), bizOperation, loginUserName, String.valueOf(loginUserId), logStr, "41");
            AuditLogUtil.sendLog(JSONObject.toJSONString(log));
        }
    }

    public CrmResult removeDhtRoleUser(@ApiParam(value = "企业ID") @HeaderParam("x-fs-ei") String tenantId, @ApiParam(examples = @Example(value = @ExampleProperty(mediaType = "json", value = "{\"userIds\":[\"100\",\"101\"]}")), required = true) String body) { // ignoreI18n
        CrmResult result = new CrmResult();
        List<String> userIdsList = Lists.newArrayList();
        if (checkParam(body, result, userIdsList)) return result;


        UserRoleApi api = new UserRoleApi(authApiClient);
        AuthContext authContext = new AuthContext();
        authContext.setTenantId(String.valueOf(tenantId));
        authContext.setUserId("-1000");
        authContext.setAppId(Constant.getAppId());

        DelUserFromRoleArg arg = new DelUserFromRoleArg();
        arg.setAuthContext(authContext);

        arg.setRoleCode(dhtRoleCode);
        arg.setUsers(userIdsList);
        try {
            log.info("removeSailUser ei {} body {}", tenantId, body);

            if ("1".equals(props.get("audit_log", "0"))) {
                authLogUsers(tenantId, userIdsList, bizOperationName_Delete, I18N.text(I18NKey.ORDER_PASS), Sets.newHashSet(dhtRoleCode));
            }
            api.delUserFromRole(arg);
            result.setCode(0);
            result.setMsg("success");
        } catch (ApiException e) {
            log.error("error", e);
            result.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
            result.setMsg(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getMessage() + e.getMessage());
        }
        return result;
    }

    public CrmResult updateUsersMajorRole(UpdateUsersMajorRole arg, SessionContext sessionContext) {
        CrmResult errorCrmResult = requestParamCheck(arg.getUserIds(), arg.getMajorRoleCode());
        if (requestParamCheck() != null) return errorCrmResult;

        CrmResult crmResult = new CrmResult();

        UpdateUserDefaultRoleArg roleArg = new UpdateUserDefaultRoleArg();
        roleArg.setAuthContext(getAuthContext(sessionContext));
        roleArg.setUsers(arg.getUserIds());
        roleArg.setRoleCode(arg.getMajorRoleCode());


        try {
            BaseResult baseResult = paasAuthApiApi.updateUserDefaultRole(roleArg);

            if (!baseResult.getSuccess()) {
                crmResult.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
                crmResult.setMsg("detail:" + baseResult.getErrMessage());
                return crmResult;
            }
        } catch (ApiException e) {
            crmResult.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
            crmResult.setMsg(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getMessage() + e);
            return crmResult;
        }

        crmResult.setCode(0);
        crmResult.setResult(Boolean.TRUE);
        return crmResult;
    }

    public CrmResult updateUserMajorRoleRespectively(UpdateUserMajorRoleRespectivelyArg arg, SessionContext sessionContext) {
        //增加crm可见范围
        List<String> allUserIds = Lists.newArrayList(arg.getUserId2RoleInfoMap().keySet());
        List<Integer> allUserIdsInteger = allUserIds.stream().map(o -> Integer.valueOf(o)).collect(Collectors.toList());
        syncCrmVisibleService.syncOpenCrmVisible(allUserIdsInteger, sessionContext.getEa());
        //从禁用名单中剔除
        com.facishare.paas.appframework.privilege.dto.AuthContext forbiddenUsersAuthContext =
                getBuild(sessionContext);
        ForbiddenUsers.Arg forbiddenUsersArg = new ForbiddenUsers.Arg(forbiddenUsersAuthContext, allUserIds, false);
        forbiddenUsersService.updateForbiddenUsers(forbiddenUsersArg);

        CrmResult errorCrmResult = requestParamCheck(arg.getUserId2RoleInfoMap());
        if (requestParamCheck() != null) return errorCrmResult;

        Map<String, Map<String, Boolean>> userId2RoleInfoMap = arg.getUserId2RoleInfoMap();
        List<UserRolePojo> userRolePojoList = Lists.newArrayList();
        userId2RoleInfoMap.forEach((userId, roleInfo) -> {
                    roleInfo.forEach((roleCode, isMajorRoleCode) -> {
                                UserRolePojo pojo = new UserRolePojo();
                                pojo.setAppId(Constant.APP_ID);
                                pojo.setTenantId(sessionContext.getEId().toString());
                                pojo.setRoleCode(roleCode);
                                pojo.setDefaultRole(isMajorRoleCode);
                                pojo.setOrgId(userId);

                                userRolePojoList.add(pojo);
                            }
                    );
                }
        );


        CrmResult crmResult = new CrmResult();

        BatchUpdateUserRoleArg userRoleArg = new BatchUpdateUserRoleArg();
        userRoleArg.setAuthContext(getAuthContext(sessionContext));
        userRoleArg.setUserRolePojoList(userRolePojoList);

        Map<String, Set<String>> usersRoleCodesMap;
        try {
            usersRoleCodesMap = getUsersRoleCodes(sessionContext, Sets.newHashSet(allUserIds));
        } catch (ApiException e) {
            log.error("error", e);
            crmResult.setMsg(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getMessage());
            crmResult.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
            return crmResult;
        }

        allUserIds.forEach(userId -> {
            Set<String> dbRoleCodes = usersRoleCodesMap.getOrDefault(userId, Collections.emptySet());
            if (CollectionUtils.isEmpty(dbRoleCodes)) {
                return;
            }
            //db里有的，但是arg里没有的code找出来
            Set<String> argRoleCodes = userId2RoleInfoMap.get(userId).keySet();
            //移除的角色只有一个
            String filterRoleCode = dbRoleCodes.stream()
                    .filter(item -> !argRoleCodes.contains(item))
                    .findAny()
                    .orElseGet(() -> null);

            if (!Objects.isNull(filterRoleCode)) {
                taskExecutor.execute(() -> authLog(sessionContext, Sets.newHashSet(userId), null, Sets.newHashSet(filterRoleCode), bizOperationName_Delete));
            }
        });

        try {
            BaseResult baseResult = paasAuthApiApi.batchUpdateUserRole(userRoleArg);
            if (!baseResult.getSuccess()) {
                crmResult.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
                crmResult.setMsg("detail:" + baseResult.getErrMessage());
                return crmResult;
            }
        } catch (ApiException e) {
            crmResult.setCode(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getCode());
            crmResult.setMsg(CRMErrorCode.PRIVILEGE_PAAS_ERROR.getMessage() + e);
            return crmResult;
        }


        crmResult.setCode(0);
        crmResult.setResult(Boolean.TRUE);
        return crmResult;

    }

    private List<String> getUserIds4DelRole(String roleCode, AuthContext auth) throws ApiException, CrmCheckedException {
        CheckRoleUserArg checkRoleUserArg = new CheckRoleUserArg();
        checkRoleUserArg.setRoleCode(roleCode);
        checkRoleUserArg.setAuthContext(auth);
        return getUserIdsByCheckRoleUser(checkRoleUserArg);
    }

    private List<String> getUserIds4DelUserByRole(String roleCode, List<String> userIds, AuthContext auth) throws ApiException, CrmCheckedException {

        CheckRoleUserArg checkRoleUserArg = new CheckRoleUserArg();
        checkRoleUserArg.setRoleCode(roleCode);
        checkRoleUserArg.setAuthContext(auth);
        checkRoleUserArg.setUsers(userIds);
        return getUserIdsByCheckRoleUser(checkRoleUserArg);
    }

    private List<String> getUserIdsByCheckRoleUser(CheckRoleUserArg checkRoleUserArg) throws CrmCheckedException, ApiException {
        CheckDefaultRoleResponse baseResult;
        try {
            baseResult = paasAuthApiApi.checkRoleUser(checkRoleUserArg);
            if (baseResult.getSuccess()) {
                return baseResult.getResult();
            }
            throw new CrmCheckedException(CRMErrorCode.FS_CRM_PRIVILEGE_PAAS_REST_ERROR, "checkRoleUserArg:" + checkRoleUserArg + " errorMsg:" + baseResult.getErrMessage());
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "checkRoleUser", checkRoleUserArg, e);
            throw e;
        }
    }

    public List<String> getUserRole(String tenantId, String userId) throws PaasPrivilegeException {
        UserRoleApi api = new UserRoleApi(authApiClient);

        AuthContext auth = new AuthContext();
        auth.setAppId(Constant.APP_ID);
        auth.setTenantId(tenantId);
        auth.setUserId(userId);

        UserRoleInfoArg arg = new UserRoleInfoArg();
        arg.setAuthContext(auth);
        arg.setUserId(userId);

        UserRoleInfoResponse response;
        try {
            response = api.userRoleInfo(arg);
            if (!response.getSuccess()) {
                throw new PaasPrivilegeException("arg:" + arg + " detail:" + response.getErrMessage(), CRMErrorCode.PAAS_PRIVILEGE_FAILED);
            }
            return response.getResult().stream().map(RolePojo::getRoleCode).collect(Collectors.toList());
        } catch (ApiException e) {
            throw new PaasPrivilegeException(e, CRMErrorCode.PAAS_ERROR);
        }
    }

}
