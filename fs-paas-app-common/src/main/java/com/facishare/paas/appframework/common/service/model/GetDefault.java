package com.facishare.paas.appframework.common.service.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

public interface GetDefault {

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        @JsonProperty("describe_api_name")
        private String describeApiName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        @JsonProperty("config")
        private Map<String, Object> config;
    }
}
