package com.facishare.paas.appframework.core.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created by zhouwr on 2024/1/5.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ValidationResult implements Serializable {
    //是否阻断（否的话可以继续提交）
    private boolean block;
    //继续提交时是否自动跳过整个Validator的执行
    private boolean skipWhenForceSubmit;
    //提示信息
    private String message;
    //校验器中返回提示信息的校验步骤的唯一标识（用于继续提交以后判断跳过哪一步校验）
    private String stepKey;
}
