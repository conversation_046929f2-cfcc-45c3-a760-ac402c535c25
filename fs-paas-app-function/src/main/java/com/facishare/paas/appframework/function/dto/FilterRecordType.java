package com.facishare.paas.appframework.function.dto;

import com.facishare.paas.appframework.core.model.User;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-03-28-12:05
 */
public interface FilterRecordType {

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private List<String> recordTypeNames;
    }

    @Data
    @Builder
    class Arg {
        private User user;
        private String functionApiName;
    }


}
