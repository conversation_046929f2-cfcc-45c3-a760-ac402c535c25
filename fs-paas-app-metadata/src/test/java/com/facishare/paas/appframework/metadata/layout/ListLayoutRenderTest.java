package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.layout.factory.ListComponentFactory;
import com.facishare.paas.appframework.metadata.layout.factory.ViewComponentFactory;
import com.facishare.paas.appframework.metadata.layout.resource.LayoutResourceService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.webpage.customer.api.service.WebPageService;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.appframework.metadata.layout.strcuture.LayoutStructureExt;
import com.facishare.paas.appframework.metadata.layout.strcuture.StructureLayout;
import com.facishare.paas.appframework.metadata.layout.strcuture.PageLayout;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.collect.Sets;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;


@ExtendWith(MockitoExtension.class)
class ListLayoutRenderTest {



  @Mock
  private ListLayoutExt listLayoutExt;

  @Mock
  private ObjectDescribeExt describeExt;

  @Mock
  private User user;

  @Mock
  private SceneLogicService sceneLogicService;

  @Mock
  private CustomButtonService customButtonService;

  @Mock
  private WebPageService webPageService;

  @Mock
  private ButtonLogicService buttonLogicService;

  @Mock
  private LicenseService licenseService;

  @Mock
  private LayoutLogicService layoutLogicService;

  @Mock
  private LayoutResourceService layoutResourceService;

  @Mock
  private ViewComponentFactory viewComponentFactory;

  @Mock
  private ListComponentFactory listComponentFactory;

  @Mock
  private OptionalFeaturesService optionalFeaturesService;

  @Mock
  private FunctionPrivilegeService functionPrivilegeService;

  private Map<String, Localization> localizationMap;

  @BeforeEach
  void setUp() {
    localizationMap = Maps.newHashMap();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ListLayoutRender构造函数正常场景
   */
  @Test
  @DisplayName("正常场景 - 构造ListLayoutRender对象")
  void testListLayoutRenderConstructor_Success() {
    // 执行被测试方法
    ListLayoutRender listLayoutRender = ListLayoutRender.builder()
        .listLayoutExt(listLayoutExt)
        .describeExt(describeExt)
        .user(user)
        .sceneLogicService(sceneLogicService)
        .customButtonService(customButtonService)
        .webPageService(webPageService)
        .buttonLogicService(buttonLogicService)
        .licenseService(licenseService)
        .layoutLogicService(layoutLogicService)
        .layoutResourceService(layoutResourceService)
        .viewComponentFactory(viewComponentFactory)
        .listComponentFactory(listComponentFactory)
        .optionalFeaturesService(optionalFeaturesService)
        .functionPrivilegeService(functionPrivilegeService)
        .localizationMap(localizationMap)
        .pageType(PageType.List)
        .layoutAgentType(LayoutAgentType.WEB)
        .renderPageType("list")
        .ignoreButton(false)
        .build();

    // 验证结果
    assertNotNull(listLayoutRender);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ListLayoutRender基本功能
   */
  @Test
  @DisplayName("正常场景 - 验证列表布局渲染器基本功能")
  void testListLayoutRender_BasicFunctionality() {
    ListLayoutRender listLayoutRender = ListLayoutRender.builder()
        .listLayoutExt(listLayoutExt)
        .describeExt(describeExt)
        .user(user)
        .sceneLogicService(sceneLogicService)
        .customButtonService(customButtonService)
        .webPageService(webPageService)
        .buttonLogicService(buttonLogicService)
        .licenseService(licenseService)
        .layoutLogicService(layoutLogicService)
        .layoutResourceService(layoutResourceService)
        .viewComponentFactory(viewComponentFactory)
        .listComponentFactory(listComponentFactory)
        .optionalFeaturesService(optionalFeaturesService)
        .functionPrivilegeService(functionPrivilegeService)
        .localizationMap(localizationMap)
        .pageType(PageType.List)
        .layoutAgentType(LayoutAgentType.WEB)
        .renderPageType("list")
        .ignoreButton(false)
        .build();

    // 验证基本功能
    assertNotNull(listLayoutRender);
    assertDoesNotThrow(() -> {
      listLayoutRender.toString();
    });
  }
}
