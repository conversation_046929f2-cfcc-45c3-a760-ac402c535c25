package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.common.exception.CrmDefObjCheckedException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * GenerateByAI
 * 测试FieldDataConverterFactory类的转换器创建功能
 */
@ExtendWith(MockitoExtension.class)
class FieldDataConverterFactoryTest {

  @Mock
  private IFieldDescribe fieldDescribe;

  @Mock
  private IObjectData objectData;

  @Mock
  private IObjectDataProxyService dataService;

  @BeforeEach
  void setUp() {
    // 使用lenient模式避免不必要的stubbing错误
    lenient().when(fieldDescribe.getApiName()).thenReturn("test_field");
    lenient().when(fieldDescribe.getLabel()).thenReturn("测试字段");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFieldDataConverter方法为已注册的字段类型创建正确的转换器
   */
  @ParameterizedTest
  @MethodSource("provideRegisteredFieldTypes")
  @DisplayName("测试getFieldDataConverter - 已注册字段类型创建正确转换器")
  void testGetFieldDataConverter_RegisteredFieldTypes(String fieldType, Class<? extends AbstractFieldDataConverter> expectedClass) throws CrmDefObjCheckedException {
    // 准备测试数据
    when(fieldDescribe.getType()).thenReturn(fieldType);

    // 执行被测试方法
    AbstractFieldDataConverter result = FieldDataConverterFactory.getFieldDataConverter(fieldDescribe, objectData, dataService);

    // 验证结果
    assertNotNull(result);
    assertEquals(expectedClass, result.getClass());
    assertEquals(fieldDescribe, result.getFieldDescribe());
    assertEquals(objectData, result.getObjectData());
    assertEquals(dataService, result.getDataService());
  }

  /**
   * 提供已注册字段类型的测试数据
   */
  private static Stream<Arguments> provideRegisteredFieldTypes() {
    return Stream.of(
        Arguments.of(IFieldType.TEXT, TextFieldDataConverter.class),
        Arguments.of(IFieldType.NUMBER, NumberFieldDataConverter.class),
        Arguments.of(IFieldType.DATE, DateDataConverter.class)
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFieldDataConverter方法为未注册的字段类型返回NonHandleFieldDataConverter
   */
  @Test
  @DisplayName("测试getFieldDataConverter - 未注册字段类型返回NonHandleFieldDataConverter")
  void testGetFieldDataConverter_UnregisteredFieldType() throws CrmDefObjCheckedException {
    // 准备测试数据
    when(fieldDescribe.getType()).thenReturn("UNKNOWN_FIELD_TYPE");

    // 执行被测试方法
    AbstractFieldDataConverter result = FieldDataConverterFactory.getFieldDataConverter(fieldDescribe, objectData, dataService);

    // 验证结果
    assertNotNull(result);
    assertEquals(NonHandleFieldDataConverter.class, result.getClass());
    assertSame(NonHandleFieldDataConverter.instance(), result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFieldDataConverter方法当fieldDescribe为null时抛出异常
   */
  @Test
  @DisplayName("测试getFieldDataConverter - fieldDescribe为null抛出异常")
  void testGetFieldDataConverterThrowsNullPointerException_WhenFieldDescribeIsNull() {
    // 执行并验证异常
    NullPointerException exception = assertThrows(NullPointerException.class, () -> {
      FieldDataConverterFactory.getFieldDataConverter(null, objectData, dataService);
    });

    // 验证异常信息
    assertNotNull(exception);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFieldDataConverter方法处理objectData为null的情况
   */
  @Test
  @DisplayName("测试getFieldDataConverter - objectData为null正常处理")
  void testGetFieldDataConverter_NullObjectData() throws CrmDefObjCheckedException {
    // 准备测试数据
    when(fieldDescribe.getType()).thenReturn(IFieldType.TEXT);

    // 执行被测试方法
    AbstractFieldDataConverter result = FieldDataConverterFactory.getFieldDataConverter(fieldDescribe, null, dataService);

    // 验证结果
    assertNotNull(result);
    assertEquals(TextFieldDataConverter.class, result.getClass());
    assertEquals(fieldDescribe, result.getFieldDescribe());
    assertNull(result.getObjectData());
    assertEquals(dataService, result.getDataService());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFieldDataConverter方法处理dataService为null的情况
   */
  @Test
  @DisplayName("测试getFieldDataConverter - dataService为null正常处理")
  void testGetFieldDataConverter_NullDataService() throws CrmDefObjCheckedException {
    // 准备测试数据
    when(fieldDescribe.getType()).thenReturn(IFieldType.TEXT);

    // 执行被测试方法
    AbstractFieldDataConverter result = FieldDataConverterFactory.getFieldDataConverter(fieldDescribe, objectData, null);

    // 验证结果
    assertNotNull(result);
    assertEquals(TextFieldDataConverter.class, result.getClass());
    assertEquals(fieldDescribe, result.getFieldDescribe());
    assertEquals(objectData, result.getObjectData());
    assertNull(result.getDataService());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFieldDataConverter方法为相同字段类型创建不同实例
   */
  @Test
  @DisplayName("测试getFieldDataConverter - 相同字段类型创建不同实例")
  void testGetFieldDataConverter_DifferentInstancesForSameFieldType() throws CrmDefObjCheckedException {
    // 准备测试数据
    when(fieldDescribe.getType()).thenReturn(IFieldType.TEXT);

    // 执行被测试方法
    AbstractFieldDataConverter result1 = FieldDataConverterFactory.getFieldDataConverter(fieldDescribe, objectData, dataService);
    AbstractFieldDataConverter result2 = FieldDataConverterFactory.getFieldDataConverter(fieldDescribe, objectData, dataService);

    // 验证结果
    assertNotNull(result1);
    assertNotNull(result2);
    assertEquals(result1.getClass(), result2.getClass());
    assertNotSame(result1, result2); // 应该是不同的实例
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFieldDataConverter方法验证转换器的属性设置
   */
  @Test
  @DisplayName("测试getFieldDataConverter - 验证转换器属性设置")
  void testGetFieldDataConverter_ConverterPropertiesSet() throws CrmDefObjCheckedException {
    // 准备测试数据
    when(fieldDescribe.getType()).thenReturn(IFieldType.NUMBER);

    // 执行被测试方法
    AbstractFieldDataConverter result = FieldDataConverterFactory.getFieldDataConverter(fieldDescribe, objectData, dataService);

    // 验证结果
    assertNotNull(result);
    assertEquals(NumberFieldDataConverter.class, result.getClass());
    
    // 验证所有属性都被正确设置
    assertEquals(fieldDescribe, result.getFieldDescribe());
    assertEquals(objectData, result.getObjectData());
    assertEquals(dataService, result.getDataService());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试特殊字段类型的映射关系
   */
  @Test
  @DisplayName("测试特殊字段类型的映射关系")
  void testSpecialFieldTypeMappings() throws CrmDefObjCheckedException {
    // 测试AUTO_NUMBER映射到FormulaFieldDataConverter
    when(fieldDescribe.getType()).thenReturn(IFieldType.AUTO_NUMBER);
    AbstractFieldDataConverter autoNumberConverter = FieldDataConverterFactory.getFieldDataConverter(fieldDescribe, objectData, dataService);
    assertEquals(FormulaFieldDataConverter.class, autoNumberConverter.getClass());

    // 测试QUOTE映射到CountDataConverter
    when(fieldDescribe.getType()).thenReturn(IFieldType.QUOTE);
    AbstractFieldDataConverter quoteConverter = FieldDataConverterFactory.getFieldDataConverter(fieldDescribe, objectData, dataService);
    assertEquals(CountDataConverter.class, quoteConverter.getClass());

    // 测试HTML_RICH_TEXT映射到RichTextFieldDataConverter
    when(fieldDescribe.getType()).thenReturn(IFieldType.HTML_RICH_TEXT);
    AbstractFieldDataConverter htmlRichTextConverter = FieldDataConverterFactory.getFieldDataConverter(fieldDescribe, objectData, dataService);
    assertEquals(RichTextFieldDataConverter.class, htmlRichTextConverter.getClass());
  }
}
