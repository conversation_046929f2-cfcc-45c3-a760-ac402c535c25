package com.facishare.paas.appframework.core.predef.action

import com.facishare.paas.I18N
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.common.util.Tuple
import com.facishare.paas.appframework.core.model.OutInfoChangeModel
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.predef.service.PartnerRemindOutUserService
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.appframework.metadata.TeamMember
import com.facishare.paas.appframework.prm.util.PrmConstant
import com.facishare.paas.common.util.UdobjConstants
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

/**
 * StandardChangePartnerOwnerAction单元测试
 */
@Unroll
class StandardChangePartnerOwnerActionTest extends Specification {
    
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试dealOldOwner方法正常场景，将原外部负责人设置为普通团队成员
     */
    def "dealOldOwnerTest_Success"() {
        given:
        // 初始化测试数据和环境
        def action = new StandardChangePartnerOwnerAction()
        def objectData = new ObjectData(["_id": "1", "name": "test"])
        def objectDataExt = Mock(ObjectDataExt)
        def oldOutTenantId = "12345"
        def oldOwnerIds = ["67890"]
        def strategy = UdobjConstants.CHANGE_OWNER_STRATEGY.SET_ORIGINAL_OWNER_TO_NORMAL_MEMBER.getValue()
        def oldOwnerTeamMemberPermissionType = "1" // READONLY
        
        // 设置relatedTeamEnabled为true
        action.relatedTeamEnabled = true
        
        // Mock静态方法
        ObjectDataExt.metaClass.static.of = { IObjectData data -> objectDataExt }
        
        when:
        Whitebox.invokeMethod(action, "dealOldOwner", objectData, oldOutTenantId, strategy, oldOwnerIds, oldOwnerTeamMemberPermissionType)
        
        then:
        1 * objectDataExt.addTeamMembers(_) >> { args ->
            def teamMembers = args[0]
            assert teamMembers.size() == 1
            def teamMember = teamMembers[0]
            assert teamMember.employee[0] == "67890"
            assert teamMember.role == TeamMember.Role.NORMAL_STAFF
            assert teamMember.permission == TeamMember.Permission.READONLY
            assert teamMember.outTenantId == oldOutTenantId
        }
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试dealOldOwner方法，当oldOwnerIds为空时，应该直接返回不做任何处理
     */
    def "dealOldOwnerTest_EmptyOldOwnerIds"() {
        given:
        def action = new StandardChangePartnerOwnerAction()
        def objectData = new ObjectData(["_id": "1", "name": "test"])
        def oldOutTenantId = "12345"
        def oldOwnerIds = []
        def strategy = UdobjConstants.CHANGE_OWNER_STRATEGY.SET_ORIGINAL_OWNER_TO_NORMAL_MEMBER.getValue()
        def oldOwnerTeamMemberPermissionType = "1"
        
        def objectDataExt = Mock(ObjectDataExt)
        ObjectDataExt.metaClass.static.of = { IObjectData data -> objectDataExt }
        
        when:
        Whitebox.invokeMethod(action, "dealOldOwner", objectData, oldOutTenantId, strategy, oldOwnerIds, oldOwnerTeamMemberPermissionType)
        
        then:
        0 * objectDataExt.addTeamMembers(_) // 不应该调用addTeamMembers方法
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试dealOldOwner方法，当策略为移除原负责人时，不应该添加团队成员
     */
    def "dealOldOwnerTest_RemoveOriginalOwnerStrategy"() {
        given:
        def action = new StandardChangePartnerOwnerAction()
        def objectData = new ObjectData(["_id": "1", "name": "test"])
        def oldOutTenantId = "12345"
        def oldOwnerIds = ["67890"]
        def strategy = UdobjConstants.CHANGE_OWNER_STRATEGY.REMOVE_ORIGINAL_OWNER_FROM_RELEVANT_TEAM.getValue()
        def oldOwnerTeamMemberPermissionType = "1"
        
        // 设置relatedTeamEnabled为true
        action.relatedTeamEnabled = true
        
        def objectDataExt = Mock(ObjectDataExt)
        ObjectDataExt.metaClass.static.of = { IObjectData data -> objectDataExt }
        
        when:
        Whitebox.invokeMethod(action, "dealOldOwner", objectData, oldOutTenantId, strategy, oldOwnerIds, oldOwnerTeamMemberPermissionType)
        
        then:
        0 * objectDataExt.addTeamMembers(_) // 不应该调用addTeamMembers方法
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试dealOldOwner方法，当relatedTeamEnabled为false时，不应该添加团队成员
     */
    def "dealOldOwnerTest_RelatedTeamDisabled"() {
        given:
        def action = new StandardChangePartnerOwnerAction()
        def objectData = new ObjectData(["_id": "1", "name": "test"])
        def oldOutTenantId = "12345"
        def oldOwnerIds = ["67890"]
        def strategy = UdobjConstants.CHANGE_OWNER_STRATEGY.SET_ORIGINAL_OWNER_TO_NORMAL_MEMBER.getValue()
        def oldOwnerTeamMemberPermissionType = "1"
        
        // 设置relatedTeamEnabled为false
        action.relatedTeamEnabled = false
        
        def objectDataExt = Mock(ObjectDataExt)
        ObjectDataExt.metaClass.static.of = { IObjectData data -> objectDataExt }
        
        when:
        Whitebox.invokeMethod(action, "dealOldOwner", objectData, oldOutTenantId, strategy, oldOwnerIds, oldOwnerTeamMemberPermissionType)
        
        then:
        0 * objectDataExt.addTeamMembers(_) // 不应该调用addTeamMembers方法
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试dealOldOwner方法，当oldOwnerTeamMemberPermissionType为空时，不应该添加团队成员
     */
    def "dealOldOwnerTest_EmptyPermissionType"() {
        given:
        def action = new StandardChangePartnerOwnerAction()
        def objectData = new ObjectData(["_id": "1", "name": "test"])
        def oldOutTenantId = "12345"
        def oldOwnerIds = ["67890"]
        def strategy = UdobjConstants.CHANGE_OWNER_STRATEGY.SET_ORIGINAL_OWNER_TO_NORMAL_MEMBER.getValue()
        def oldOwnerTeamMemberPermissionType = ""
        
        // 设置relatedTeamEnabled为true
        action.relatedTeamEnabled = true
        
        def objectDataExt = Mock(ObjectDataExt)
        ObjectDataExt.metaClass.static.of = { IObjectData data -> objectDataExt }
        
        when:
        Whitebox.invokeMethod(action, "dealOldOwner", objectData, oldOutTenantId, strategy, oldOwnerIds, oldOwnerTeamMemberPermissionType)
        
        then:
        0 * objectDataExt.addTeamMembers(_) // 不应该调用addTeamMembers方法
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试dealOldOwner方法，当oldOwnerId为空时，不应该添加团队成员
     */
    def "dealOldOwnerTest_EmptyOldOwnerId"() {
        given:
        def action = new StandardChangePartnerOwnerAction()
        def objectData = new ObjectData(["_id": "1", "name": "test"])
        def oldOutTenantId = "12345"
        def oldOwnerIds = [""]
        def strategy = UdobjConstants.CHANGE_OWNER_STRATEGY.SET_ORIGINAL_OWNER_TO_NORMAL_MEMBER.getValue()
        def oldOwnerTeamMemberPermissionType = "1"
        
        // 设置relatedTeamEnabled为true
        action.relatedTeamEnabled = true
        
        def objectDataExt = Mock(ObjectDataExt)
        ObjectDataExt.metaClass.static.of = { IObjectData data -> objectDataExt }
        
        when:
        Whitebox.invokeMethod(action, "dealOldOwner", objectData, oldOutTenantId, strategy, oldOwnerIds, oldOwnerTeamMemberPermissionType)
        
        then:
        0 * objectDataExt.addTeamMembers(_) // 不应该调用addTeamMembers方法
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getRelatedTeamEnabled方法
     */
    def "getRelatedTeamEnabledTest"() {
        given:
        def action = new StandardChangePartnerOwnerAction()
        action.relatedTeamEnabled = enabled
        
        when:
        def result = action.getRelatedTeamEnabled()
        
        then:
        result == expected
        
        where:
        enabled || expected
        true    || true
        false   || false
        null    || null
    }
} 