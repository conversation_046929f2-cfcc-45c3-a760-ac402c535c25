package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.OrganizationInfo;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.SelectOptionUtil;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderConfig;
import com.facishare.paas.appframework.metadata.domain.DomainPluginDefinition;
import com.facishare.paas.appframework.metadata.domain.DomainPluginDefinitionHolder;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginInstance;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.predef.action.StandardCloneAction.Arg;
import static com.facishare.paas.appframework.core.predef.action.StandardCloneAction.Result;
import static com.facishare.paas.common.util.UdobjConstants.*;
import static com.facishare.paas.metadata.api.DBRecord.*;
import static com.facishare.paas.metadata.api.IObjectData.CREATE_ENTERPRISE;

@Slf4j
public class StandardCloneAction extends AbstractStandardAction<Arg, Result> {
    private final List<String> noCloneTypeList = Lists.newArrayList(IFieldType.AUTO_NUMBER, IFieldType.IMAGE,
            IFieldType.FILE_ATTACHMENT, IFieldType.SIGNATURE);
    private final Map<String, Object> nullValueMap = Maps.newHashMap();

    private final Map<String, Object> systemDefaultValue = Maps.newHashMap();

    private List<IObjectDescribe> detailDescribes = Lists.newArrayList();

    private final List<String> filterFieldTypeList = Lists.newArrayList(IFieldType.RECORD_TYPE);
    private IObjectData objectData;

    private Set<String> enableCloneFieldTypes = Sets.newHashSet();

    @Override
    protected IObjectData getPreObjectData() {
        return objectData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return objectData;
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.CLONE.getButtonApiName();
    }

    @Override
    protected boolean isBatchAction() {
        return false;
    }

    @Override
    protected void init() {
        objectData = serviceFacade.findObjectData(actionContext.getUser(), arg.getObjectDataId(), actionContext.getObjectApiName());
        detailDescribes = serviceFacade.findDetailDescribesCreateWithMaster(actionContext.getTenantId(), objectDescribe.getApiName());
        //根据业务插件处理字段描述上的enable_clone属性
        processDescribeByDomainPlugin();
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        nullValueMap.put(IFieldType.FILE_ATTACHMENT, Lists.newArrayList());
        nullValueMap.put(IFieldType.SIGNATURE, Lists.newArrayList());
        nullValueMap.put(IFieldType.IMAGE, Lists.newArrayList());
        nullValueMap.put(IFieldType.AUTO_NUMBER, null);

        systemDefaultValue.put(LOCK_RULE_API_NAME, null);
        systemDefaultValue.put(LOCK_STATUS_API_NAME, LOCK_STATUS_VALUE_UNLOCK);
        systemDefaultValue.put(LOCK_USER_API_NAME, null);
        systemDefaultValue.put(LIFE_STATUS_API_NAME, LIFE_STATUS_VALUE_NORMAL);
        systemDefaultValue.put(LIFE_STATUS_BEFORE_INVALID_API_NAME, null);
        systemDefaultValue.put(IS_DELETED, false);
        systemDefaultValue.put(CREATE_TIME, null);
        systemDefaultValue.put(CREATED_BY, Lists.newArrayList(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()));
        systemDefaultValue.put(LAST_MODIFIED_BY, Lists.newArrayList(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()));
        systemDefaultValue.put(LAST_MODIFIED_TIME, null);
        systemDefaultValue.put(ObjectDataExt.OWNER_DEPARTMENT, null);
        systemDefaultValue.put("owner_department_id", null);
        if (objectDescribe.isPublicObject()) {
            systemDefaultValue.put(CREATE_ENTERPRISE, null);
        }
        enableCloneFieldTypes.addAll(AppFrameworkConfig.getFieldEnableCloneFieldTypes(actionContext.getTenantId()));

    }

    /**
     * 跳过按钮的前置执行动作
     *
     * @return
     */
    @Override
    protected boolean skipPreFunction() {
        return Objects.isNull(objectData);
    }

    /**
     * 跳过按钮后动作
     *
     * @return
     */
    @Override
    protected boolean skipPostFunction() {
        return Objects.isNull(objectData);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.Add.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    @Override
    protected Result doAct(Arg arg) {
        Map<String, List<IObjectData>> detailMap = handleDetail(objectData);
        filterOrResetFieldValue(objectDescribe, objectData, detailDescribes, detailMap);
        handleData(Lists.newArrayList(objectData), objectDescribe);

        //掩码字段不拷贝
        removeMaskFieldValue(detailMap);
        //将数据负责人设置为当前操作人
        setDataOwner(detailMap);
        //计算前处理，保底默认值能被设置上，让使用了默认值的计算公式计算正确
        //在对象字段读写权限后处理，使得些被清空的字段值可以回填上默认值
        resetFieldValueByDisableClone(objectDescribe, objectData, detailDescribes, detailMap);
        // 对于单选或多选字段只保留当前字段可用选项
        resetFieldValueBySelectOption(objectDescribe, objectData, detailDescribes, detailMap);
        serviceFacade.calculateForClone(actionContext.getUser(), objectDescribe, objectData, detailDescribes, detailMap);
        //Objects.equals(a.getType(), IFieldType.PHONE_NUMBER) && !ObjectDataExt.isValueEmpty(objectData.get(a.getApiName()))
        serviceFacade.fillPhoneNumberInformation(objectDescribe, objectData);
        fillDataInfo(objectDescribe, Lists.newArrayList(objectData));
        Result result = new Result();
        result.setObjectData(ObjectDataDocument.of(objectData));
        result.setDetails(ObjectDataDocument.ofMap(detailMap));
        return result;
    }

    private void processDescribeByDomainPlugin() {
        List<String> objectApiNames = Lists.newArrayList(objectDescribe.getApiName());
        if (CollectionUtils.notEmpty(detailDescribes)) {
            detailDescribes.forEach(x -> objectApiNames.add(x.getApiName()));
        }
        List<DomainPluginInstance> pluginInstances = infraServiceFacade.findPluginInstances(actionContext.getTenantId(),
                objectApiNames, null, Lists.newArrayList(objectData.getRecordType()));
        if (CollectionUtils.empty(pluginInstances)) {
            return;
        }
        Set<String> masterDisableCloneFields = Sets.newHashSet();
        Map<String, Set<String>> detailDisableCloneFieldMap = Maps.newHashMap();
        pluginInstances.forEach(pluginInstance -> {
            DomainPluginDefinition pluginDefinition = DomainPluginDefinitionHolder.getPluginDefinition(pluginInstance.getPluginApiName());
            if (Objects.isNull(pluginDefinition)) {
                return;
            }
            if (objectDescribe.getApiName().equals(pluginInstance.getRefObjectApiName())) {
                masterDisableCloneFields.addAll(pluginInstance.masterFields(pluginDefinition.masterDisableCloneFields()));
                pluginDefinition.detailDisableCloneFieldMap().forEach((detailKey, fieldKeys) -> {
                    List<String> detailDisableCloneFields = pluginInstance.detailFields(detailKey, fieldKeys);
                    if (CollectionUtils.notEmpty(detailDisableCloneFields)) {
                        String detailObjectApiName = pluginInstance.detailObjectApiName(detailKey);
                        detailDisableCloneFieldMap.computeIfAbsent(detailObjectApiName, k -> Sets.newHashSet()).addAll(detailDisableCloneFields);
                    }
                });
            } else {
                List<String> detailDisableCloneFields = pluginInstance.masterFields(pluginDefinition.masterDisableCloneFields());
                if (CollectionUtils.notEmpty(detailDisableCloneFields)) {
                    String detailObjectApiName = pluginInstance.getRefObjectApiName();
                    detailDisableCloneFieldMap.computeIfAbsent(detailObjectApiName, k -> Sets.newHashSet()).addAll(detailDisableCloneFields);
                }
            }
        });
        if (CollectionUtils.notEmpty(masterDisableCloneFields)) {
            this.objectDescribe = ObjectDescribeExt.of(objectDescribe).copyOnWrite();
            ObjectDescribeExt.of(objectDescribe).getFieldByApiNames(Lists.newArrayList(masterDisableCloneFields)).forEach(field -> field.setEnableClone(false));
        }
        detailDescribes.forEach(detailDescribe -> {
            Set<String> disableCloneFields = detailDisableCloneFieldMap.get(detailDescribe.getApiName());
            if (CollectionUtils.notEmpty(disableCloneFields)) {
                ObjectDescribeExt.of(detailDescribe).getFieldByApiNames(Lists.newArrayList(disableCloneFields)).forEach(field -> field.setEnableClone(false));
            }
        });
    }

    private void fillDataInfo(IObjectDescribe describe, List<IObjectData> dataList) {
        fillLookupName(describe, dataList);
        fillExtendFieldInfo(describe, dataList);
    }

    private void setDataOwner(Map<String, List<IObjectData>> detailMap) {
        User currentUser = actionContext.getUser();
        OrganizationInfo org = serviceFacade.findMainOrgAndDeptByUserId(actionContext.getTenantId(), currentUser.getUserId(), Lists.newArrayList(currentUser.getUserId()));
        String mainDeptId = org.getMainDeptId(currentUser.getUserId());
        String mainOrgId = org.getMainOrgId(currentUser.getUserId());
        ObjectDataExt.of(objectData).setDataOwner(currentUser);
        ObjectDataExt.of(objectData).setDataOwnDepartmentId(mainDeptId);
        ObjectDataExt.of(objectData).setDataOwnOrganizationId(mainOrgId);
        detailMap.forEach((apiName, detailDataList) -> detailDataList.forEach(data -> {
            ObjectDataExt.of(data).setDataOwner(currentUser);
            ObjectDataExt.of(data).setDataOwnDepartmentId(mainDeptId);
            ObjectDataExt.of(data).setDataOwnOrganizationId(mainOrgId);
        }));
    }

    @Override
    protected Result after(Arg arg, Result result) {
        return super.after(arg, result);
    }

    private void removeMaskFieldValue(Map<String, List<IObjectData>> detailMap) {
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(objectDescribe.getApiName(), objectDescribe);
        detailDescribes.forEach(x -> describeMap.put(x.getApiName(), x));
        serviceFacade.removeMaskFieldValue(actionContext.getUser(), objectData, detailMap, describeMap);
    }

    private Map<String, List<IObjectData>> handleDetail(IObjectData masterData) {
        if (CollectionUtils.empty(detailDescribes)) {
            return Maps.newHashMap();
        }

        Map<String, List<IObjectData>> resultMap = Maps.newConcurrentMap();
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();

        List<String> detailApiNames = detailDescribes.stream().map(x -> x.getApiName()).collect(Collectors.toList());
        List<String> enableDetailApiNames = doDetailFunPrivilegeCheck(detailApiNames);
        detailDescribes = detailDescribes.stream().filter(x -> enableDetailApiNames.contains(x.getApiName())).collect(Collectors.toList());
        for (IObjectDescribe detailDescribe : detailDescribes) {
            parallelTask.submit(() -> {
                SearchTemplateQuery query = buildSearchTemplateQuery(ObjectDescribeExt.of(detailDescribe), masterData);
                List<IObjectData> detailObjectDataList = serviceFacade.findBySearchQuery(actionContext.getUser(),
                        detailDescribe, detailDescribe.getApiName(), query).getData();
                String mdApiName = ObjectDescribeExt.of(detailDescribe).getMasterDetailFieldDescribe().get().getApiName();
                handleData(detailObjectDataList, detailDescribe);
                // 补充拓展信息
                fillDataInfo(detailDescribe, detailObjectDataList);
                detailObjectDataList.forEach(x -> x.set(mdApiName, null));
                removeUnMatchRecordTypeDetailData(masterData, detailDescribe, detailObjectDataList);
                resultMap.put(detailDescribe.getApiName(), detailObjectDataList);
            });
        }

        try {
            parallelTask.await(10000, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            log.error("find detail data error,", e);
            throw new MetaDataException(SystemErrorCode.METADATA_TIMEOUT_ERROR);
        }

        return resultMap;
    }

    /*
     * 构造按order_by顺序查询从对象数据的searchquery
     */
    private SearchTemplateQuery buildSearchTemplateQuery(ObjectDescribeExt detailDescribe, IObjectData masterData) {
        SearchTemplateQuery query = serviceFacade.getSearchTemplateQuery(actionContext.getUser(),
                detailDescribe, null, "{\"limit\":2000,\"offset\":0}", true);
        query.setNeedReturnCountNum(false);
        // 设置主数据
        handleMasterData(query, masterData, detailDescribe);
        // 优先按照order_by字段排序
        handleOrderBys(query);
        return query;
    }

    protected void handleOrderBys(SearchTemplateQuery query) {
        SearchTemplateQueryExt.of(query).orderByFirst();
    }

    private void handleMasterData(SearchTemplateQuery query, IObjectData masterData, ObjectDescribeExt detailDescribe) {
        String refFieldName = detailDescribe.getMasterDetailFieldName(masterData.getDescribeApiName())
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, refFieldName, masterData.getId());
    }

    private void removeUnMatchRecordTypeDetailData(IObjectData masterData, IObjectDescribe detailDescribe, List<IObjectData> detailObjectDataList) {
        List<String> matchRecordTypes = serviceFacade.findValidAndMatchRecordTypes(actionContext.getUser(), objectDescribe.getApiName(),
                masterData.getRecordType(), detailDescribe.getApiName());
        detailObjectDataList.removeIf(x -> !matchRecordTypes.contains(x.getRecordType()));
    }

    private void fillLookupName(IObjectDescribe detailDescribe, List<IObjectData> detailObjectDataList) {
        serviceFacade.fillObjectDataWithRefObject(detailDescribe, detailObjectDataList, actionContext.getUser(), null, true);
        infraServiceFacade.fillQuoteFieldValue(actionContext.getUser(), detailObjectDataList, detailDescribe, null, false);
    }

    private void handleData(List<IObjectData> objectDataList, IObjectDescribe describe) {
        Set<String> unWriteAbleFields = serviceFacade.getReadonlyFields(actionContext.getUser(), describe.getApiName());
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        List<String> filterPaymentFields = filterPaymentFields(describe);

        for (IObjectData objectData : objectDataList) {
            setAllNotCloneFiledNullValue(unWriteAbleFields, describeExt, filterPaymentFields, objectData);
        }
        //引用图片字段不复制
        cleanDetailQuoteImageField(describe, objectDataList);
    }

    private void setAllNotCloneFiledNullValue(Set<String> unWriteAbleFields, ObjectDescribeExt describeExt, List<String> filterPaymentFields, IObjectData objectData) {
        ObjectDataExt dataExt = ObjectDataExt.of(objectData);
        dataExt.setId(null);
        //图片，附件，签字等类型字段不复制
        describeExt.filter(field -> noCloneTypeList.contains(field.getType())).forEach(field -> {
            setNotCloneFiledNullValue(dataExt, field);
        });
        systemDefaultValue.forEach(dataExt::set);
        //团队成员不复制
        dataExt.setTeamMembers(Lists.newArrayList());
        //只读字段值清空
        unWriteAbleFields.forEach(a -> {
            if (filterFieldTypeList.contains(a)) {
                return;
            }
            Optional<IFieldDescribe> field = describeExt.getFieldDescribeSilently(a);
            field.ifPresent(f -> dataExt.set(a, nullValueMap.get(f.getType())));
        });

        filterPaymentFields.forEach(a -> dataExt.set(a, null));
        //extend_obj_data_id字段不复制
        dataExt.remove(ObjectDataExt.EXTEND_OBJ_DATA_ID);
        // 复制原单对象，不复制变更单相关对象
        dataExt.remove(ChangeOrderConfig.getOriginalDescribeFields(dataExt.getDescribeApiName()));
    }

    private void setNotCloneFiledNullValue(ObjectDataExt dataExt, IFieldDescribe field) {
        if (AppFrameworkConfig.getClonePicTenantIds().contains(actionContext.getTenantId())) {
            if (field.getType().equals(IFieldType.FILE_ATTACHMENT)) {
                return;
            }
            if (field.getType().equals(IFieldType.IMAGE)) {
                Image i = (Image) field;
                if (!i.getIsWaterMark()) {
                    return;
                }
            }
        }
        dataExt.set(field.getApiName(), nullValueMap.get(field.getType()));
    }

    private void fillExtendFieldInfo(IObjectDescribe describe, List<IObjectData> objectDataList) {
        serviceFacade.fillDimensionFieldValue(actionContext.getUser(), describe, objectDataList);
        serviceFacade.fillUserInfo(describe, objectDataList, actionContext.getUser());
        serviceFacade.fillDepartmentInfo(describe, objectDataList, actionContext.getUser());
        serviceFacade.fillCountryAreaLabel(describe, objectDataList, actionContext.getUser());
        serviceFacade.fillRichTextImageInfo(describe, objectDataList, actionContext.getUser());
    }

    protected List<String> doDetailFunPrivilegeCheck(List<String> detailApiNames) {
        Map<String, Map<String, Boolean>> privilegeMap = serviceFacade.batchFunPrivilegeCheck(actionContext.getUser(), detailApiNames, StandardAction.Add.getFunPrivilegeCodes());
        List<String> enableDetailApiNames = Lists.newArrayList();
        privilegeMap.forEach((apiName, result) -> {
            if (Boolean.TRUE.equals(result.get(ObjectAction.CREATE.getActionCode()))) {
                enableDetailApiNames.add(apiName);
            }
        });
        return enableDetailApiNames;
    }

    private List<String> filterPaymentFields(IObjectDescribe describe) {
        List<String> filterFieldApiNames = Lists.newArrayList();
        ObjectDescribeExt.of(describe).getPaymentFieldDescribe().ifPresent(x -> {
            filterFieldApiNames.add(x.getPayTimeFieldApiName());
            filterFieldApiNames.add(x.getPayTypeFieldApiName());
        });
        return filterFieldApiNames;
    }

    protected void cleanDetailQuoteImageField(IObjectDescribe objectDescribe,
                                              List<IObjectData> detailObjectDataList) {
        List<Quote> quoteList = objectDescribe.getFieldDescribes().stream()
                .map(x -> FieldDescribeExt.of(x))
                .filter(x -> x.isQuoteField())
                .map(x -> (Quote) x.getFieldDescribe())
                .filter(x -> StringUtils.equals(IFieldType.IMAGE, x.getQuoteFieldType()))
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(quoteList)) {
            detailObjectDataList.forEach(x -> quoteList.forEach(field -> x.set(field.getApiName(), null)));
        }
    }

    public void filterOrResetFieldValue(IObjectDescribe objectDescribe, IObjectData objectData, List<IObjectDescribe> detailDescribes,
                                        Map<String, List<IObjectData>> detailDataMap) {
    }

    /**
     * 将目标数据中不可复制的字段设置为空或者默认值
     *
     * @param objectDescribe
     * @param destObjectData    目标数据
     * @param detailDescribes
     * @param destDetailDataMap
     */
    private void resetFieldValueByDisableClone(IObjectDescribe objectDescribe, IObjectData destObjectData, List<IObjectDescribe> detailDescribes,
                                               Map<String, List<IObjectData>> destDetailDataMap) {
        if (CollectionUtils.empty(enableCloneFieldTypes)) {
            return;
        }
        //主数据
        resetFieldValueByDisableClone(objectDescribe, destObjectData);
        //从数据
        if (CollectionUtils.notEmpty(destDetailDataMap)) {
            destDetailDataMap.forEach((objApi, dataList) -> {
                if (CollectionUtils.empty(dataList)) {
                    return;
                }
                Optional<IObjectDescribe> objDescOpt = detailDescribes.stream().filter(objDesc -> StringUtils.equals(objApi, objDesc.getApiName())).findFirst();
                objDescOpt.ifPresent(objDesc -> {
                    dataList.forEach(data -> resetFieldValueByDisableClone(objDesc, data));
                });
            });
        }
    }

    private void resetFieldValueByDisableClone(IObjectDescribe objectDescribe, IObjectData destObjectData) {
        //字段不支持复制 且 字段有默认值 且 默认值为常量 将该字段设置为默认值
        ObjectDescribeExt.of(objectDescribe).getFieldDescribesSilently()
                .forEach(f -> {
                    if (noCloneTypeList.contains(f.getType())) {
                        return;
                    }
                    if (systemDefaultValue.containsKey(f.getApiName()) || StringUtils.equalsAny(f.getApiName(), IObjectData.ID)) {
                        return;
                    }
                    if (!enableCloneFieldTypes.contains(f.getType())) {
                        return;
                    }
                    if (!f.getEnableClone() && BooleanUtils.isFalse(f.getDefaultIsExpression())) {
                        if (ObjectUtils.isEmpty(f.getDefaultValue())) {
                            destObjectData.set(f.getApiName(), null);
                        } else {
                            destObjectData.set(f.getApiName(), f.getDefaultValue());
                        }
                        //清空字段记录其他的信息
                        ObjectDataExt.remove(Lists.newArrayList(destObjectData), FieldDescribeExt.of(f).getAnyFieldExtendNames());
                    }
                });
    }

    /**
     * 只对单选或多选使用，保留可用选项
     *
     * @param masterObjDesc  主对象描述
     * @param masterObjData  主对象数据
     * @param detailDescList 从对象描述
     * @param detailDataMap  从对象数据
     */
    private void resetFieldValueBySelectOption(IObjectDescribe masterObjDesc, IObjectData masterObjData,
                                               List<IObjectDescribe> detailDescList, Map<String, List<IObjectData>> detailDataMap) {

        List<SelectOne> masterSelectFields = ObjectDescribeExt.of(masterObjDesc).getActiveSelectFields(true);
        if (CollectionUtils.notEmpty(masterSelectFields)) {
            resetFieldValueBySelectOption(ObjectDescribeExt.of(masterObjDesc).getSelectOneFields(), masterObjData);
        }

        if (CollectionUtils.empty(detailDataMap)) {
            return;
        }
        Map<String, List<SelectOne>> detailFieldsMap = detailDescList.stream()
                .collect(Collectors.toMap(IObjectDescribe::getApiName, objDesc ->
                        ObjectDescribeExt.of(objDesc).getActiveSelectFields(true)));

        detailDataMap.forEach((objApi, dataList) -> {
            if (CollectionUtils.empty(dataList)) {
                return;
            }
            List<SelectOne> detailSelectFields = detailFieldsMap.get(objApi);

            if (CollectionUtils.empty(detailSelectFields)) {
                return;
            }

            dataList.forEach(data -> resetFieldValueBySelectOption(detailSelectFields, data));
        });

    }


    /**
     * 只对单选或多选使用，保留可用选项
     *
     * @param fields      字段描述
     * @param destObjData 对象数据
     */
    private void resetFieldValueBySelectOption(List<SelectOne> fields, IObjectData destObjData) {
        fields.forEach(f -> {
            // 字段开启可复制
            if (!f.getEnableClone()) {
                return;
            }
            List<ISelectOption> options = f.getSelectOptions();
            Map<String, Boolean> valueUsableMap = options
                    .stream()
                    .collect(Collectors.toMap(ISelectOption::getValue, option -> !option.isNotUsable()));
            Object value = destObjData.get(f.getApiName());
            Object finalValue = SelectOptionUtil.clearUpOptions(value, valueUsableMap);
            destObjData.set(f.getApiName(), finalValue);
        });
    }

    @Data
    public static class Arg {
        @JSONField(name = "M1")
        @JsonProperty("objectDataId")
        @SerializedName("objectDataId")
        private String objectDataId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        @JSONField(name = "M1")
        @JsonProperty("objectData")
        private ObjectDataDocument objectData;

        @JSONField(name = "M2")
        @JsonProperty("details")
        private Map<String, List<ObjectDataDocument>> details;
    }
}
