package com.facishare.paas.appframework.common.util;

import com.alibaba.fastjson.JSON;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 联合导入的相关配置对象
 */
public class UnionImportConfig {
    private static final String ALL = "ALL";
    private static final Splitter CONFIG_SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();
    /**
     * 不支持作为主对象的对象列表
     */
    private static List<String> notSupportMasterDescribes = Lists.newArrayList();
    /**
     * 不支持作为从对象的对象列表
     */
    private static List<String> notSupportDetailDescribes = Lists.newArrayList();

    /**
     * 支持主從一起導出的主對象
     */
    private static List<String> supportMasterDetailExportApiName = Lists.newArrayList();


    private static List<String> UNION_IMPORT_UNSUPPORT_DETAIL_GRAY = Lists.newArrayList();

    private static List<String> UNION_IMPORT_UNSUPPORT_DETAIL_GRAY_EI = Lists.newArrayList();

    static {
        ConfigFactory.getConfig("fs-crm-object-import-config", config -> {
            notSupportMasterDescribes = JSON.parseArray(config.get("union_import_unsupport_master"), String.class);
            notSupportDetailDescribes = JSON.parseArray(config.get("union_import_unsupport_detail"), String.class);
            UNION_IMPORT_UNSUPPORT_DETAIL_GRAY = JSON.parseArray(config.get("union_import_unsupport_detail_gray"), String.class);
            UNION_IMPORT_UNSUPPORT_DETAIL_GRAY_EI = Lists.newArrayList(CONFIG_SPLITTER.split(config.get("union_import_unsupport_detail_gray_ei", "")));
            supportMasterDetailExportApiName = JSON.parseArray(config.get("support_master_detail_export_api_name"), String.class);
        });
    }

    public static boolean canNotBeMaster(String apiName) {
        return notSupportMasterDescribes.contains(apiName);
    }

    public static boolean canNotBeDetail(String tenantId, String apiName) {
        if (UNION_IMPORT_UNSUPPORT_DETAIL_GRAY_EI.contains(ALL) || UNION_IMPORT_UNSUPPORT_DETAIL_GRAY_EI.contains(tenantId)) {
            return UNION_IMPORT_UNSUPPORT_DETAIL_GRAY.contains(apiName);
        }
        return notSupportDetailDescribes.contains(apiName);
    }

    public static boolean canExportMasterDetail(String apiName) {
        if (StringUtils.isBlank(apiName)) {
            return false;
        }
        if (apiName.endsWith("__c")) {
            return true;
        }
        return CollectionUtils.notEmpty(supportMasterDetailExportApiName) && supportMasterDetailExportApiName.contains(apiName);
    }

}
