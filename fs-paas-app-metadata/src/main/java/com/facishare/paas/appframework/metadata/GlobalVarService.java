package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.GlobalVariableResult;
import com.facishare.paas.metadata.api.describe.IGlobalVariableDescribe;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 全局变量服务
 * Created by linqiuying on 17/10/25.
 */
public interface GlobalVarService {

    GlobalVariableResult create(IGlobalVariableDescribe globalVariableDescribe);

    GlobalVariableResult update(IGlobalVariableDescribe globalVariableDescribe);

    GlobalVariableResult delete(String apiName, String tenantId);

    IGlobalVariableDescribe findGlobalVariableInfo(String apiName, String tenantId);

    IGlobalVariableDescribe findGlobalVariableInfo(String apiName, Boolean realTimeTrans, String tenantId);

    IGlobalVariableDescribe findGlobalVariableInfo(String apiName, Boolean realTimeTrans, String lang, String tenantId);

    List<IGlobalVariableDescribe> getGlobalVariableList(String tenantId);

    GlobalVariableResult findGlobalVariableList(String label, String tenantId);

    GlobalVariableResult findGlobalVariableList(String label, Boolean realTimeTrans, String tenantId);

    Object parseValue(IGlobalVariableDescribe globalVariable);

    Object parseValue(IGlobalVariableDescribe globalVariable,boolean isUseTransValueByGlobalVariable);

    Map<String, IGlobalVariableDescribe> findGlobalVariables(String tenantId, Collection<String> apiNames);
}
