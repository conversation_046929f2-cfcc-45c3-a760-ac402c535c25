package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

import static com.facishare.paas.appframework.metadata.SceneLogicService.IS_OUTER;

public class StandardOuterSceneController extends PreDefineController<StandardOuterSceneController.Arg, StandardOuterSceneController.Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected void before(Arg arg) {
        controllerContext.setAttribute(IS_OUTER, true);
        super.before(arg);
    }

    @Override
    protected Result doService(Arg arg) {
        IScene scene = infraServiceFacade.findSceneByApiName(controllerContext.getUser(), arg.getDescribeApiName(), arg.getSceneApiName(),
                arg.getExtendAttribute(), arg.getAppId());
        return new Result(scene, Maps.newHashMap());
    }

    @Data
    public static class Arg {
        @JSONField(name = "M1")
        @JsonProperty("describe_api_name")
        @SerializedName("describe_api_name")
        private String describeApiName;
        @JSONField(name = "M2")
        @JsonProperty("scene_api_name")
        @SerializedName("scene_api_name")
        private String sceneApiName;
        @JSONField(name = "M3")
        @JsonProperty("extend_attribute")
        @SerializedName("extend_attribute")
        private String extendAttribute;
        @JSONField(name = "M4")
        @JsonProperty("app_id")
        @SerializedName("app_id")
        private String appId;
        @JSONField(name = "extra_params")
        @JsonProperty("extra_params")
        @SerializedName("extra_params")
        private Map<String, Object> extraParams;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        IScene scene;
        private Map<String, Object> extraResult;
    }
}
