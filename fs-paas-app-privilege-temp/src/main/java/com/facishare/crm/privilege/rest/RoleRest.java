package com.facishare.crm.privilege.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.common.exception.CRMErrorCode;
import com.facishare.crm.common.exception.CrmCheckedException;
import com.facishare.crm.privilege.model.valueobject.CrmResult;
import com.facishare.crm.privilege.service.UserInfoService;
import com.facishare.crm.privilege.service.UserService;
import com.facishare.crm.privilege.swagger.ApiClient;
import com.facishare.crm.privilege.swagger.SwaggerClientUtil;
import com.facishare.crm.privilege.swagger.api.PaasAuthApiApi;
import com.facishare.crm.privilege.swagger.api.UserRoleApi;
import com.facishare.crm.privilege.swagger.model.*;
import com.facishare.crm.privilege.util.Constant;
import com.facishare.crm.privilege.util.ValidateUtil;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.Example;
import io.swagger.annotations.ExampleProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by Jacky on 12/15/16.
 */
@Slf4j
@Component
@Path("/v1/privilege/role")
@io.swagger.annotations.Api(description = "CRM权限模块的Role相关的REST服务")
public class RoleRest {
  @Autowired
  private UserInfoService userInfoService;
  @Autowired
  private UserService userService;

  private static final String TENANT_ID_KEY = "tenantId";
  private static final String API_NAMES_KEY = "apiNames";
  private static final String ROLE_CODE_KEY = "roleCode";
  private static final String ROLE_NAME_KEY = "roleName";
  private static final String USER_ID_KEY = "userId";
  private static final String DESCRIPTION_KEY = "description";
  private static final String EA_KEY = "ea";
  private static final String USER_IDS_KEY = "userIds";
  private static final String USER_ROLE_INFO = "userRoleInfo";


  private IChangeableConfig props;
  private PaasAuthApiApi paasAuthApiApi;
  private UserRoleApi userRoleApi;

  @PostConstruct
  private void init() {
    props = ConfigFactory.getConfig(Constant.getFS_CRM_PRIVILEGE());
    log.info("UserService props {}", Arrays.toString(props.getLines().toArray()));
    ApiClient authApiClient = SwaggerClientUtil.getApiClient(Constant.BASE_URL_AUTH_KEY, props);
    paasAuthApiApi = new PaasAuthApiApi(authApiClient);
    userRoleApi = new UserRoleApi(authApiClient);

  }


  @GET
  @Path("/rolecodecheck/{roleCode}")
  @Produces({"application/json"})
  @io.swagger.annotations.ApiOperation(value = "checkIfUserHasRole", notes = "判断当前登录人是否具有某个权限", response = CrmResult.class, tags = {"role"})
  public Response checkIfUserHasRole(@ApiParam(value = "RoleCode,例如：00000000000000000000000000000006是CRM管理员") @PathParam("roleCode") String roleCode, // ignoreI18n
                                     @ApiParam(value = "企业ID") @HeaderParam("x-fs-ei") String tenantId,
                                     @ApiParam(value = "当前操作人的ID") @HeaderParam("x-fs-userInfo") String userInfo,
                                     @ApiParam(value = "调用方Host地址") @HeaderParam("x-fs-peer-host") String peerHost) {

    CrmResult crmResult = new CrmResult();
    try {
      Boolean hasRole = userInfoService.isAdmin(tenantId, userInfo, roleCode);
      crmResult.setResult(hasRole);
      crmResult.setCode(CrmResult.SUCCESS);
    } catch (CrmCheckedException e) {
      crmResult.setCode(e.getErrorCode().getCode());
      crmResult.setMsg(e.getMessage());
      return Response.status(Response.Status.BAD_REQUEST).entity(crmResult).build();
    } catch (Exception ex) {
      crmResult.setCode(CRMErrorCode.FS_CRM_PRIVILEGE_SYSTEM_UNKNOWN_ERROR.getCode());
      crmResult.setMsg(ex.getMessage());
      return Response.status(Response.Status.BAD_REQUEST).entity(crmResult).build();
    }
    return Response.ok().entity(crmResult).build();
  }

  @GET
  @Path("/isadmin")
  @Produces({"application/json"})
  @io.swagger.annotations.ApiOperation(value = "checkIfUserHasRole", notes = "判断当前登录人是否具有CRM Admin权限", response = CrmResult.class, tags = {"role"})
  public Response checkIfUserHasCRMAdminRole(@ApiParam(value = "企业ID") @HeaderParam("x-fs-ei") String tenantId, // ignoreI18n
                                             @ApiParam(value = "当前操作人的ID") @HeaderParam("x-fs-userInfo") String userInfo,
                                             @ApiParam(value = "调用方Host地址") @HeaderParam("x-fs-peer-host") String peerHost) {

    CrmResult crmResult = new CrmResult();
    try {
      Boolean isAdmin = userInfoService.isAdmin(tenantId, userInfo);
      crmResult.setResult(isAdmin);
      crmResult.setCode(CrmResult.SUCCESS);
    } catch (CrmCheckedException e) {
      crmResult.setCode(e.getErrorCode().getCode());
      crmResult.setMsg(e.getMessage());
      return Response.status(Response.Status.BAD_REQUEST).entity(crmResult).build();
    } catch (Exception ex) {
      crmResult.setCode(CRMErrorCode.FS_CRM_PRIVILEGE_SYSTEM_UNKNOWN_ERROR.getCode());
      crmResult.setMsg(ex.getMessage());
      return Response.status(Response.Status.BAD_REQUEST).entity(crmResult).build();
    }
    return Response.ok().entity(crmResult).build();
  }


  @POST
  @Path("/add_sail_user")
  @Produces({"application/json"})
  @io.swagger.annotations.ApiOperation(value = "addUser", notes = "增加订货通角色下用户", response = CrmResult.class, tags = {"dht"})
  public Response addSailUser(
      @ApiParam(value = "企业ID") @HeaderParam("x-fs-ei") String tenantId
      , @ApiParam(examples = @Example(value = @ExampleProperty(mediaType = "json", value = "{\"userIds\":[\"100\",\"101\"]}")), required = true) String body
  ) {
    CrmResult result = userService.addDhtRoleUser(tenantId, body);

    return Response.ok().entity(result).build();
  }


  @POST
  @Path("/remove_sail_user")
  @Produces({"application/json"})
  @io.swagger.annotations.ApiOperation(value = "deleteUser", notes = "删除订货通角色下用户", response = CrmResult.class, tags = {"dht"})
  public Response removeSailUser(
      @ApiParam(value = "企业ID") @HeaderParam("x-fs-ei") String tenantId
      , @ApiParam(examples = @Example(value = @ExampleProperty(mediaType = "json", value = "{\"userIds\":[\"100\",\"101\"]}")), required = true) String body
  ) {

    CrmResult result = userService.removeDhtRoleUser(tenantId, body);

    return Response.ok().entity(result).build();
  }


  @POST
  @Path("/batchupdateuserrole")
  @Produces({"application/json"})
  public Response batchUpdateUserRole(String body) {
    JSONObject jsonObject = JSON.parseObject(body);
    String tenantId = jsonObject.getString(TENANT_ID_KEY);
    Map<String, Map<String, Boolean>> userRoleInfo = (Map<String, Map<String, Boolean>>) jsonObject.get(USER_ROLE_INFO);

    boolean isValidParam = ValidateUtil.isValidParam(tenantId);
    if (isValidParam) {
      try {
        AuthContext authContext = new AuthContext();
        authContext.setAppId(Constant.APP_ID);
        authContext.setUserId("1000");
        authContext.setTenantId(tenantId);

        BatchUpdateUserRoleArg arg = new BatchUpdateUserRoleArg();
        arg.setAuthContext(authContext);

        List<UserRolePojo> userRolePojoList = Lists.newArrayList();
        userRoleInfo.forEach((userId, roleInfo) -> {
          roleInfo.forEach((roleCode, isMajorRole) -> {
            UserRolePojo pojo = new UserRolePojo();
            pojo.setOrgId(userId);
            pojo.setRoleCode(roleCode);
            pojo.setDefaultRole(isMajorRole);
            pojo.setAppId(Constant.APP_ID);
            pojo.setTenantId(tenantId);

            userRolePojoList.add(pojo);
          });

        });
        arg.setUserRolePojoList(userRolePojoList);
        BaseResult result = paasAuthApiApi.batchUpdateUserRole(arg);

        if (!result.getSuccess()) {
          return ValidateUtil.getResponse(result.getErrCode(), result.getErrMessage());
        } else {
          return ValidateUtil.getSuccessResponse(result.getSuccess());
        }
      } catch (Exception e) {
        return ValidateUtil.getResponse(CRMErrorCode.PAAS_ERROR.getCode(), e.getMessage());
      }
    } else {
      return ValidateUtil.getResponse(CRMErrorCode.PARAMETER_IS_WRONG.getCode(), CRMErrorCode.PARAMETER_IS_WRONG.getMessage());
    }
  }


  @POST
  @Path("/updateusersmajorrole")
  @Produces({"application/json"})
  public Response updateUsersMajorRole(String body) {
    JSONObject jsonObject = JSON.parseObject(body);
    String tenantId = jsonObject.getString(TENANT_ID_KEY);
    List<String> userIds = (List<String>) jsonObject.get(USER_IDS_KEY);
    String roleCode = jsonObject.getString(ROLE_CODE_KEY);

    boolean isValidParam = ValidateUtil.isValidParam(tenantId);
    if (isValidParam) {
      try {
        AuthContext authContext = new AuthContext();
        authContext.setAppId(Constant.APP_ID);
        authContext.setUserId("1000");
        authContext.setTenantId(tenantId);

        UpdateUserDefaultRoleArg arg = new UpdateUserDefaultRoleArg();
        arg.setAuthContext(authContext);
        arg.setRoleCode(roleCode);
        arg.setUsers(userIds);

        BaseResult result = paasAuthApiApi.updateUserDefaultRole(arg);

        if (!result.getSuccess()) {
          return ValidateUtil.getResponse(result.getErrCode(), result.getErrMessage());
        } else {
          return ValidateUtil.getSuccessResponse(result.getSuccess());
        }
      } catch (Exception e) {
        return ValidateUtil.getResponse(CRMErrorCode.PAAS_ERROR.getCode(), e.getMessage());
      }
    } else {
      return ValidateUtil.getResponse(CRMErrorCode.PARAMETER_IS_WRONG.getCode(), CRMErrorCode.PARAMETER_IS_WRONG.getMessage());
    }
  }


  @POST
  @Path("/getusersroleinfo")
  @Produces({"application/json"})
  public Response getUserRole(String body) {
    JSONObject jsonObject = JSON.parseObject(body);
    String tenantId = jsonObject.getString(TENANT_ID_KEY);

    List<String> userIds = (List<String>) jsonObject.get(USER_IDS_KEY);

    boolean isValidParam = ValidateUtil.isValidParam(userIds, tenantId);
    if (isValidParam) {
      try {
        AuthContext authContext = new AuthContext();
        authContext.setAppId(Constant.APP_ID);
        authContext.setUserId("1000");
        authContext.setTenantId(tenantId);

        QueryUserRolesByUsersArg arg = new QueryUserRolesByUsersArg();
        arg.setAuthContext(authContext);
        arg.setUsers(userIds);

        QueryRoleInfoListByUsersResponse result = paasAuthApiApi.queryRoleInfoListByUsers(arg);

        if (!result.getSuccess()) {
          return ValidateUtil.getResponse(result.getErrCode(), result.getErrMessage());
        } else {
          return ValidateUtil.getSuccessResponse(result.getResult().getUserRoles());
        }
      } catch (Exception e) {
        return ValidateUtil.getResponse(CRMErrorCode.PAAS_ERROR.getCode(), e.getMessage());
      }
    } else {
      return ValidateUtil.getResponse(CRMErrorCode.PARAMETER_IS_WRONG.getCode(), CRMErrorCode.PARAMETER_IS_WRONG.getMessage());
    }
  }


}
