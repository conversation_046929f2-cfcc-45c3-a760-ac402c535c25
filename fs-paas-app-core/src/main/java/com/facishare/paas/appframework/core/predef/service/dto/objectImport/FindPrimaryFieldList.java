package com.facishare.paas.appframework.core.predef.service.dto.objectImport;

import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.Data;

public interface FindPrimaryFieldList {
    @Data
    class Result {
        private String describeApiName;
        private String describeDisplayName;
        private String primaryFieldName = "name";

        public Result() {}

        public Result(IObjectDescribe objectDescribe) {
            describeApiName = objectDescribe.getApiName();
            describeDisplayName = objectDescribe.getDisplayName();
        }
    }

    @Data
    class Arg {

    }
}
