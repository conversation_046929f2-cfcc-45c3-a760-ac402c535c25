package com.facishare.paas.appframework.metadata.mask;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.Builder;
import lombok.Data;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON>haooju on 2022/12/16
 */
public interface MaskFieldLogicService {
    Map<String, List<IFieldDescribe>> getMaskFields(User user, Collection<IObjectDescribe> describeList, String ownerId);

    void processMaskFieldValue(User user, IObjectData masterData, Map<String, List<IObjectData>> detailDataMap,
                               Map<String, IObjectDescribe> describeMap, MaskFieldConfig maskFieldConfig);

    void fillMaskFieldValue(User user, IObjectDescribe describe, List<String> maskFieldApiNames, List<IObjectData> dataList);

    void fillMaskFieldValue(User user, IObjectDescribe describe, List<IObjectData> dataList, List<IFieldDescribe> fields, MaskFieldConfig maskFieldConfig);

    void encodeDefaultValueByFieldDescribe(User user, IFieldDescribe fieldDescribe);

    void decodeMaskFieldEncryptValue(User user, List<IObjectData> dataList, IObjectDescribe describe);

    List<String> getMaskFieldTypes(String tenantId, String describeApiName);

    List<IFieldDescribe> maskFieldRoleFilter(User user, List<IFieldDescribe> maskFields);


    @Data
    @Builder
    class MaskFieldConfig {
        private boolean isFill;
        private boolean removeOrigValue;
        private boolean isRemoveDirectly;
        private boolean isEncrypt;
        private boolean useCurrentUser;
        private boolean needEncryptMaskFieldsWithDescribe;

        public static MaskFieldConfig defaultMaskFieldConfig() {
            return MaskFieldLogicService.MaskFieldConfig.builder()
                    .removeOrigValue(true)
                    .isRemoveDirectly(false)
                    .isFill(true)
                    .isEncrypt(true)
                    .useCurrentUser(false)
                    .build();
        }

        public static MaskFieldConfig createByRemoveMaskOrigValue(Boolean removeMaskOrigValue) {
            MaskFieldConfig maskFieldConfig = defaultMaskFieldConfig();
            maskFieldConfig.setRemoveOrigValue(removeMaskOrigValue);
            return maskFieldConfig;
        }
    }
}
