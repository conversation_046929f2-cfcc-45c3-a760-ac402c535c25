package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.function.FunctionLogicService
import com.facishare.paas.metadata.api.describe.AutoNumber
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

/**
 * Created by zhaooju on 2022/5/30
 */
class FunctionAutoNumberImplTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def "test incrementNumberByFunction function not exit"() {
        given:
        def functionLogicService = Stub(FunctionLogicService)
        def autoNumberLogicService = Stub(AutoNumberLogicService)
        def functionAutoNumber = new FunctionAutoNumberImpl(functionLogicService: functionLogicService, autoNumberLogicService: autoNumberLogicService)

        def describe = Stub(IObjectDescribe)
        def autoNumber = Stub(AutoNumber)
        def user = Stub(User)

        autoNumber.getFuncApiName() >> functionApiName
        autoNumber.getDescribeApiName() >> describeApiName
        functionLogicService.findUDefFunction(user, functionApiName, describeApiName) >> null
        when:
        functionAutoNumber.incrementNumberByFunction(describe, autoNumber, [], user)
        then:
        thrown(ValidateException)

        where:
        functionApiName   | describeApiName   || function
        "functionApiName" | "describeApiName" || null
    }

}
