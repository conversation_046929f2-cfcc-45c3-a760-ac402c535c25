package com.facishare.paas.appframework.core.predef.service;


import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.validateRule.*;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.DescribeLayoutValidateModel;
import com.facishare.paas.appframework.metadata.dto.RuleResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.IRule;
import com.facishare.paas.metadata.impl.LayoutRuleInfo;
import com.facishare.paas.metadata.impl.Rule;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by liyiguang on 2017/10/11.
 */
@ServiceModule("validate_rule")
@Component
@Slf4j
public class ObjectValidRuleService {

    @Autowired
    private ValidateRuleServiceImpl validateRuleService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private LogService logService;
    @Autowired
    private LayoutRuleLogicService layoutRuleLogicService;
    @Autowired
    private RecordTypeLogicService recordTypeLogicService;


    private String buildLogMessage(String actionKey, String ruleApi) {
        return String.join("",
                I18NExt.text(actionKey),
                " ",
                I18NExt.text(I18NKey.VALIDATE_RULE),
                ": ",
                ruleApi);
    }

    @ServiceMethod("create")
    public CreateRule.Result create(CreateRule.Arg arg, ServiceContext context) {
        CreateRule.Result result = new CreateRule.Result();
        IRule rule = new Rule();
        rule.fromJsonString(arg.getJson_data());
        rule.setTenantId(context.getTenantId());
        rule.setCreatedBy(context.getUser().getUserId());
        rule.setLastModifiedBy(context.getUser().getUserId());
        RuleResult ruleResult = validateRuleService.create(rule);
        result.setSuccess(ruleResult.isSuccess());
        logService.log(context.getUser(), EventType.ADD, ActionType.UPDATE_OBJ, rule.getDescribeApiName(),
                buildLogMessage(I18NKey.CREATE, rule.getApiName())
        );
        return result;
    }

    @ServiceMethod("update")
    public UpdateRule.Result update(UpdateRule.Arg arg, ServiceContext context) {
        UpdateRule.Result result = new UpdateRule.Result();

        IRule rule = new Rule();
        rule.fromJsonString(arg.getJson_data());
        RuleResult ruleResult = validateRuleService.update(context.getUser(), rule);
        result.setSuccess(ruleResult.isSuccess());
        logService.log(context.getUser(), EventType.MODIFY, ActionType.UPDATE_OBJ, rule.getDescribeApiName(),
                buildLogMessage(I18NKey.EDIT, rule.getApiName())
        );
        return result;
    }

    @ServiceMethod("delete")
    public DeleteRule.Result delete(DeleteRule.Arg arg, ServiceContext context) {
        DeleteRule.Result result = new DeleteRule.Result();
        RuleResult ruleResult = validateRuleService.delete(arg.getDescribeApiName(), context.getTenantId(), arg.getRuleApiName());
        result.setSuccess(ruleResult.isSuccess());
        logService.log(context.getUser(), EventType.DELETE, ActionType.UPDATE_OBJ, arg.getDescribeApiName(),
                buildLogMessage(I18NKey.DELETED, arg.getRuleApiName())
        );
        return result;
    }

    @ServiceMethod("findRuleList")
    public FindValidRuleList.Result findRuleList(FindValidRuleList.Arg arg, ServiceContext context) {
        FindValidRuleList.Result result = new FindValidRuleList.Result();
        RuleResult ruleResult = validateRuleService.findRuleList(arg.getDescribeApiName(), context.getTenantId(), arg.getRuleName());
        result.setSuccess(ruleResult.isSuccess());
        result.setRuleList(ruleResult.getRuleList());
        return result;
    }

    @ServiceMethod("findRuleInfo")
    public FindRuleInfo.Result findRuleInfo(FindRuleInfo.Arg arg, ServiceContext context) {
        FindRuleInfo.Result result = new FindRuleInfo.Result();
        RuleResult ruleResult = validateRuleService.findRuleInfo(arg.getDescribeApiName(), context.getTenantId(), arg.getRuleApiName());
        result.setSuccess(ruleResult.isSuccess());
        result.setRule(ruleResult.getRule());
        return result;
    }

    @ServiceMethod("isActive")
    public ActiveRule.Result isActive(ActiveRule.Arg arg, ServiceContext context) {
        ActiveRule.Result result = new ActiveRule.Result();
        RuleResult ruleResult = validateRuleService.isActive(arg.getDescribeApiName(), context.getUser(), arg.getRuleApiName(), arg.isActive());
        result.setSuccess(ruleResult.isSuccess());
        boolean toActive = arg.isActive();
        logService.log(context.getUser(), toActive ? EventType.ENABLE : EventType.DISABLE, ActionType.UPDATE_OBJ,
                arg.getDescribeApiName(), buildLogMessage(toActive ? I18NKey.ENABLE : I18NKey.DISABLE, arg.getRuleApiName())
        );
        return result;
    }

    @ServiceMethod("validateRule")
    public ValidateRule.Result validateRule(ValidateRule.Arg arg, ServiceContext context) {
        validateParam(arg, context.getTenantId());
        ValidateRule.Result result = new ValidateRule.Result();
        Map<String, List<IObjectData>> details = Maps.newHashMap();
        arg.getDetails()
                .forEach((key, value) -> details.put(key, value.stream()
                        .map(ObjectDataDocument::toObjectData)
                        .collect(Collectors.toList())));
        IObjectData objectData = arg.getObjectData().toObjectData();
        Set<String> apiNames = getApiNames(arg, details);
        Map<String, IObjectDescribe> objectDescribes = describeLogicService.findObjects(context.getTenantId(), apiNames);
        RuleResult ruleResult = validateRuleService.validateRule(context.getUser(), arg.getOption(), objectDescribes, objectData, details);
        if (ruleResult.isMatch()) {
            List<String> messages = Lists.newArrayList(ruleResult.getBlockMessages());
            messages.addAll(ruleResult.getNonBlockMessages());
            result.setMessage(Joiner.on("; ").join(messages));
            result.setBlockMessages(ruleResult.getBlockMessages());
            result.setNonBlockMessages(ruleResult.getNonBlockMessages());
        }
        result.setSave(ruleResult.isSave());
        return result;
    }

    private void validateParam(ValidateRule.Arg arg, String tenantId) {
        if (Objects.isNull(arg)) {
            log.warn("param is empty: ei:{}", tenantId);
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_EMPTY));
        }
        if (Objects.isNull(arg.getObjectData())) {
            log.warn("param error, ei:{}", tenantId);
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
    }

    private Set<String> getApiNames(ValidateRule.Arg arg, Map<String, List<IObjectData>> details) {
        Set<String> apiNames = Sets.newHashSet(details.keySet());
        String objectApiName = arg.getObjectApiName();
        if (!Strings.isNullOrEmpty(objectApiName)) {
            apiNames.add(objectApiName);
        }
        IObjectData objectData = arg.getObjectData().toObjectData();
        objectApiName = objectData.getDescribeApiName();
        if (!Strings.isNullOrEmpty(objectApiName)) {
            apiNames.add(objectApiName);
        }
        return apiNames;
    }

    @ServiceMethod("cacheValidate")
    public CacheValidate.Result cacheValidate(CacheValidate.Arg arg, ServiceContext context) {
        CacheValidate.Result result = CacheValidate.Result.builder()
                .success(false)
                .build();
        if (Objects.isNull(arg) || StringUtils.isEmpty(arg.getObjectApiName()) || Objects.isNull(arg.getDescribeVersion())) {
            return result;
        }
        Map<String, Long> describeApiNameAndVersionMap = CollectionUtils.nullToEmpty(arg.getDetailCacheValid()).stream()
                .collect(Collectors.toMap(CacheValidate.DetailObjCacheValid::getObjectApiName, CacheValidate.DetailObjCacheValid::getDescribeVersion));
        describeApiNameAndVersionMap.put(arg.getObjectApiName(), arg.getDescribeVersion());
        Map<String, IObjectDescribe> objectDescribes = describeLogicService.findObjectsWithoutCopy(context.getTenantId(), describeApiNameAndVersionMap.keySet());
        for (IObjectDescribe describe : objectDescribes.values()) {
            if (describe.getVersion() > describeApiNameAndVersionMap.get(describe.getApiName())) {
                return result;
            }
        }
        List<DescribeLayoutValidateModel> layoutRuleValidateModels = buildDescribeLayoutValidateModels(arg);
        if (!layoutRuleLogicService.layoutRuleValidate(context.getUser(), layoutRuleValidateModels)) {
            return result;
        }
        if (StringUtils.isNoneEmpty(arg.getLayoutType(), arg.getRecordType())
                && !recordTypeLogicService.validateLayoutAndRecordType(context.getUser(), objectDescribes, layoutRuleValidateModels)) {
            return result;
        }

        result.setSuccess(true);
        return result;
    }

    private List<DescribeLayoutValidateModel> buildDescribeLayoutValidateModels(CacheValidate.Arg arg) {
        List<LayoutRuleInfo> layoutRuleInfos = CollectionUtils.nullToEmpty(arg.getLayoutRule()).stream()
                .map(x -> LayoutRuleExt.of(x).getRuleInfo()).
                collect(Collectors.toList());
        List<DescribeLayoutValidateModel> layoutRuleValidateModels = Lists.newArrayList();
        DescribeLayoutValidateModel.LayoutRuleValidateInfo layoutRuleValidateInfo = DescribeLayoutValidateModel.LayoutRuleValidateInfo.builder()
                .layoutApiName(arg.getLayoutApiName())
                .layoutLastModifiedTime(arg.getLayoutLastModifiedTime())
                .layoutRuleInfos(layoutRuleInfos)
                .build();
        DescribeLayoutValidateModel masterValidateModel = DescribeLayoutValidateModel.builder()
                .objectApiName(arg.getObjectApiName())
                .isMaster(true)
                .layoutVersion(arg.getLayoutVersion())
                .layoutRuleValidateInfos(Lists.newArrayList(layoutRuleValidateInfo))
                .masterRecordType(arg.getRecordType())
                .layoutType(arg.getLayoutType())
                .build();
        layoutRuleValidateModels.add(masterValidateModel);
        CollectionUtils.nullToEmpty(arg.getDetailCacheValid()).forEach(detail -> {
            List<DescribeLayoutValidateModel.LayoutRuleValidateInfo> layoutRuleValidateInfos = Lists.newArrayList();
            for (CacheValidate.DetailLayoutRelatedInfo detailLayoutRelatedInfo : CollectionUtils.nullToEmpty(detail.getDetailLayoutList())) {
                List<LayoutRuleInfo> detailLayoutRuleInfos = CollectionUtils.nullToEmpty(detailLayoutRelatedInfo.getLayoutRule()).stream()
                        .map(x -> LayoutRuleExt.of(x).getRuleInfo()).
                        collect(Collectors.toList());
                DescribeLayoutValidateModel.LayoutRuleValidateInfo detailLayoutValidateInfo = DescribeLayoutValidateModel.LayoutRuleValidateInfo.builder()
                        .layoutApiName(detailLayoutRelatedInfo.getLayoutApiName())
                        .layoutLastModifiedTime(detailLayoutRelatedInfo.getLayoutLastModifiedTime())
                        .layoutRuleInfos(detailLayoutRuleInfos)
                        .recordType(detailLayoutRelatedInfo.getRecordType())
                        .build();
                layoutRuleValidateInfos.add(detailLayoutValidateInfo);
            }
            DescribeLayoutValidateModel detailValidateModel = DescribeLayoutValidateModel.builder()
                    .objectApiName(detail.getObjectApiName())
                    .layoutRuleValidateInfos(layoutRuleValidateInfos)
                    .build();
            layoutRuleValidateModels.add(detailValidateModel);
        });
        return layoutRuleValidateModels;
    }
}
