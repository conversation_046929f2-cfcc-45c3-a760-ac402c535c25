package com.facishare.paas.appframework.common.service.model;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/7/13
 */
public enum ManageGroupType {
    OBJECT("obj"),
    LAYOUT("obj_layout"),
    DUPLICATE_SEARCH_RULE("obj_duplicate"),
    SCORE_RULE("obj_score"),
    BUSINESS_ROLE("businessRole"),
	LEADS_POOL_RULE("leadsPoolRule"),
    ;

    private String type;

    ManageGroupType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }
}
