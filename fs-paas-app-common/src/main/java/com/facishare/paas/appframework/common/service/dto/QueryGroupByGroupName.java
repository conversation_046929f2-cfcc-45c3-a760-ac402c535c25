package com.facishare.paas.appframework.common.service.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface QueryGroupByGroupName {

    @Data
    @Builder
    class Arg {
        private String tenantId;
        private String appId;
        private String userId;
        private String name;
        private boolean isFilterByUser;
        private boolean isPublic;
        private Integer status;
        private List<String> nameList;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<QueryGroupByGroupName.UserGroupInfo> result;
        private boolean success = false;
    }

    @Data
    class UserGroupInfo {
        private String id;
        private String tenantId;
        private String appId;
        private String name;
        private int status;
        private int type;
        private String description;
        private boolean delFlag;
    }

}
