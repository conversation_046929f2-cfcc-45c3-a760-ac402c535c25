package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.multiCurrency.*;
import com.facishare.paas.appframework.metadata.MtCurrency;
import com.facishare.paas.appframework.metadata.MtExchangeRate;
import com.facishare.paas.appframework.metadata.MultiCurrencyLogicService;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.multicurrency.RefreshInternational;
import com.facishare.paas.appframework.metadata.repository.model.MtCurrencyExchange;
import com.facishare.paas.metadata.api.QueryResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
@ServiceModule("currency")
public class ObjectMultiCurrencyService {

    @Autowired
    private MultiCurrencyLogicService multiCurrencyLogicService;

    /**
     * 开启多货币
     */
    @ServiceMethod("open_multi_currency")
    public OpenMultiCurrency.Result openMultiCurrency(OpenMultiCurrency.Arg arg, ServiceContext context) {
        log.warn("openMultiCurrency,params={}", arg);
        multiCurrencyLogicService.openMultiCurrency(arg.getFunctionalCurrency(), context.getUser());
        return OpenMultiCurrency.Result.builder().build();
    }

    @ServiceMethod("refresh_multi_currency")
    public OpenMultiCurrency.Result refreshMultiCurrency(RefreshInternational.Arg arg, ServiceContext context) {
        multiCurrencyLogicService.refreshMultiCurrency(arg, context.getUser());
        return OpenMultiCurrency.Result.builder().build();
    }

    /**
     * 查询多货币开启状态
     */
    @ServiceMethod("multi_currency_status")
    public FindMultiCurrencyStatus.Result multiCurrencyStatus(ServiceContext context) {
        if (context.getUser() == null) {
            log.error("query tenant multiCurrencyStatus failed,tenant_id={},user={}", context.getTenantId(), context.getUser());
            return FindMultiCurrencyStatus.Result.builder().build();
        }
        Integer status = multiCurrencyLogicService.findMultiCurrencyStatus(context.getUser());
        return FindMultiCurrencyStatus.Result.builder().status(status).build();
    }

    /**
     * 查询货币列表
     */
    @ServiceMethod("find_currency_list")
    public FindCurrencyList.Result findCurrencyList(ServiceContext context) {
        List<MtCurrency> currencies = multiCurrencyLogicService.findCurrencyList(context.getUser());
        return FindCurrencyList.Result.builder().currencyList(currencies).build();
    }

    /**
     * 查询可用货币列表
     */
    @ServiceMethod("find_unused_currency_list")
    public FindUnusedCurrencyList.Result findUnusedCurrencyList(ServiceContext context) {
        List<MtCurrency> unusedCurrencyList = multiCurrencyLogicService.findUnusedCurrencyList(context.getUser());
        List<FindUnusedCurrencyList.UnusedCurrency> unusedCurrencies = Lists.newArrayList();
        for (MtCurrency currency : unusedCurrencyList) {
            FindUnusedCurrencyList.UnusedCurrency unused = FindUnusedCurrencyList.UnusedCurrency.builder().build();
            unused.setCurrencyLabel(currency.getCurrencyLabel());
            unused.setCurrencyCode(currency.getCurrencyCode());
            unusedCurrencies.add(unused);
        }
        return FindUnusedCurrencyList.Result.builder().currencyList(unusedCurrencies).build();
    }

    @ServiceMethod("find_all_currency")
    public FindUnusedCurrencyList.Result findAllCurrency(ServiceContext context) {
        List<MtCurrency> allCurrency = multiCurrencyLogicService.findAllCurrency(context.getUser());
        List<FindUnusedCurrencyList.UnusedCurrency> currencyList = Lists.newArrayList();
        for (MtCurrency currency : allCurrency) {
            FindUnusedCurrencyList.UnusedCurrency unused = FindUnusedCurrencyList.UnusedCurrency.builder().build();
            unused.setCurrencyLabel(currency.getCurrencyLabel());
            unused.setCurrencyCode(currency.getCurrencyCode());
            currencyList.add(unused);
        }
        return FindUnusedCurrencyList.Result.builder().currencyList(currencyList).build();
    }

    /**
     * 添加货币
     */
    @ServiceMethod("add_currency")
    public AddCurrency.Result addCurrency(AddCurrency.Arg arg, ServiceContext context) {
        multiCurrencyLogicService.addCurrency(arg.to(), context.getUser());
        return AddCurrency.Result.builder().build();
    }

    /**
     * 编辑货币
     */
    @ServiceMethod("edit_currency")
    public EditCurrency.Result editCurrency(EditCurrency.Arg arg, ServiceContext context) {
        multiCurrencyLogicService.editCurrency(arg.to(), context.getUser());
        return EditCurrency.Result.builder().build();
    }

    /**
     * 禁用货币
     */
    @ServiceMethod("disable_currency")
    public DisableCurrency.Result disableCurrency(DisableCurrency.Arg arg, ServiceContext context) {
        multiCurrencyLogicService.disableCurrency(arg.getCurrencyCode(), context.getUser());
        return DisableCurrency.Result.builder().build();
    }

    /**
     * 启用货币
     */
    @ServiceMethod("enable_currency")
    public EnableCurrency.Result enableCurrency(EnableCurrency.Arg arg, ServiceContext context) {
        multiCurrencyLogicService.enableCurrency(arg.getCurrencyCode(), arg.getExchangeRate(), context.getUser());
        return EnableCurrency.Result.builder().build();
    }


    /**
     * 根据货币代码查询货币信息
     */
    @ServiceMethod("find_currency_by_code")
    public FindCurrencyByCode.Result findCurrencyByCode(FindCurrencyByCode.Arg arg, ServiceContext context) {
        MtCurrency currencyExchange = multiCurrencyLogicService.findCurrencyByCode(arg.getCurrencyCode(), context.getUser());
        return FindCurrencyByCode.Result.builder()
                .currencyCode(currencyExchange.getCurrencyCode())
                .currencyLabel(currencyExchange.getCurrencyLabel())
                .exchangeRate(currencyExchange.getExchangeRate())
                .build();
    }


    @ServiceMethod("refresh_currency_options")
    public RefreshCurrencyOptions.Result refreshCurrencyOptions(RefreshCurrencyOptions.Arg arg, ServiceContext context) {
        multiCurrencyLogicService.bulkRefreshCurrencyOptions(arg.getTenantIds());
        return RefreshCurrencyOptions.Result.builder().build();
    }

    /**
     * 批量编辑汇率
     */
    @ServiceMethod("batch_modify_rate")
    public BatchModifyRate.Result batchModifyRate(BatchModifyRate.Arg arg, ServiceContext context) {
        multiCurrencyLogicService.batchModifyRate(arg.getExchangeRateList(), context.getUser());
        return BatchModifyRate.Result.builder().build();
    }

    /**
     * 分页查询汇率
     */
    @ServiceMethod("query_rate")
    public QueryRate.Result queryRate(QueryRate.Arg arg, ServiceContext context) {
        QueryResult<MtExchangeRate> queryResult = multiCurrencyLogicService.queryRate(arg.getCurrencyCode(),
                arg.getStartTime(), arg.getEndTime(), arg.getPageSize(), arg.getPageNumber(), context.getUser());
        int pageCount = SearchTemplateQueryExt.calculateTotalPage(queryResult.getTotalNumber(), arg.getPageSize());
        return QueryRate.Result.builder()
                .exchangeRateList(queryResult.getData())
                .pageCount(pageCount)
                .pageNumber(arg.getPageNumber())
                .pageSize(arg.getPageSize())
                .totalCount(queryResult.getTotalNumber())
                .build();
    }

    /**
     * 查询本位币
     */
    @ServiceMethod("find_functional_currency")
    public FindFunctionalCurrency.Result findFunctionalCurrency(FindFunctionalCurrency.Arg arg, ServiceContext context) {
        MtCurrency mtCurrency = multiCurrencyLogicService.findFunctionalCurrency(context.getUser());
        String currencyCode = mtCurrency == null ? null : mtCurrency.getCurrencyCode();
        return FindFunctionalCurrency.Result.builder().currencyCode(currencyCode).build();
    }


    @ServiceMethod("update_functional_currency")
    public UpdateFunctionalCurrency.Result updateFunctionalCurrency(UpdateFunctionalCurrency.Arg arg, ServiceContext context) {
        log.warn("update functional currency begin,arg:{},user:{}", arg.getFunctionalCurrency(), context.getUser());
        multiCurrencyLogicService.updateFunctionalCurrency(context.getUser(), arg.getFunctionalCurrency(), BooleanUtils.isTrue(arg.getSkipValidate()));
        return UpdateFunctionalCurrency.Result.builder().build();
    }


    @ServiceMethod("find_currency_exchange_rate")
    public FindCurrencyExchangeRate.Result findCurrencyExchangeRate(FindCurrencyExchangeRate.Arg arg, ServiceContext context) {
        List<MtCurrencyExchange> currencyExchangeList = multiCurrencyLogicService.findCurrencyExchangeList(context.getUser(), arg.getToCurrencyCode());
        List<MtCurrency> currencyList = multiCurrencyLogicService.findCurrencyList(context.getUser());
        List<MtCurrencyExchange> supplementCurrencyExchanges = Lists.newArrayList();
        List<String> existFromCurrencyCodes = currencyExchangeList.stream().map(x -> x.getFromCurrencyCode()).collect(Collectors.toList());
        currencyList.stream()
                .map(x -> x.getCurrencyCode())
                .filter(x -> !existFromCurrencyCodes.contains(x))
                .filter(currencyCode -> !Objects.equals(currencyCode, arg.getToCurrencyCode()))
                .forEach(x -> {
                    MtCurrencyExchange currencyExchange = new MtCurrencyExchange();
                    currencyExchange.setFromCurrencyCode(x);
                    currencyExchange.setToCurrencyCode(arg.getToCurrencyCode());
                    currencyExchange.setExchangeRate("");
                    supplementCurrencyExchanges.add(currencyExchange);
                });
        currencyExchangeList.addAll(supplementCurrencyExchanges);
        currencyExchangeList.forEach(x -> {
            x.setFromCurrencyCodeLabel(x.getFromCurrencyCodeLabel());
            x.setToCurrencyCodeLabel(x.getToCurrencyCodeLabel());
        });
        return FindCurrencyExchangeRate.Result.builder().currencyExchanges(currencyExchangeList).build();
    }

    @ServiceMethod("update_currency_exchange_rate")
    public UpdateCurrencyExchangeRate.Result updateCurrencyExchangeRate(UpdateCurrencyExchangeRate.Arg arg, ServiceContext context) {
        multiCurrencyLogicService.updateCurrencyExchanges(context.getUser(), arg.getCurrencyExchangeArgs(), arg.getToCurrencyCode());
        return UpdateCurrencyExchangeRate.Result.builder().build();
    }

    @ServiceMethod("find_exchange_rate_by_currency")
    public FindExchangeRateByCurrency.Result findExchangeRateByCurrency(FindExchangeRateByCurrency.Arg arg, ServiceContext context) {
        MtCurrencyExchange currencyExchange = multiCurrencyLogicService.findCurrencyExchange(context.getUser(),
                arg.getFromCurrencyCode(), arg.getToCurrencyCode());
        return FindExchangeRateByCurrency.Result.builder()
                .exchangeRate(currencyExchange == null ? null : currencyExchange.getExchangeRate())
                .build();
    }
}
