package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class SceneDocument extends DocumentBaseEntity {
    private static final long serialVersionUID = 798704736255976480L;

    public SceneDocument() {

    }

    private SceneDocument(Map<String, Object> data) {
        super(data);
    }

    public static SceneDocument of(IScene scene) {
        Map<String, Object> data = Maps.newLinkedHashMap();
        data.put("api_name", scene.getApiName());
        data.put("display_name", scene.getDisplayName());
        data.put("type", scene.getType());
        return new SceneDocument(data);
    }

    public static SceneDocument ofTemplate(ISearchTemplate template) {
        Map<String, Object> data = Maps.newLinkedHashMap();
        data.put("api_name", template.getApiName());
        data.put("display_name", template.getLabel());
        data.put("type", template.getType());
        return new SceneDocument(data);
    }

    public static List<SceneDocument> ofList(Collection<IScene> scenes) {
        return scenes.stream().map(SceneDocument::of).collect(Collectors.toList());
    }

    public static List<SceneDocument> ofTemplates(Collection<ISearchTemplate> templates) {
        return templates.stream().map(SceneDocument::ofTemplate).collect(Collectors.toList());
    }
}
