package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.ImportVerifyActionDomainPlugin;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.DuplicatedSearchExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.UniqueRuleExt;
import com.facishare.paas.appframework.metadata.dto.ImportReferenceMapping;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public abstract class BaseImportVerifyAction extends BaseImportAction {
    private boolean isFuncPrivilegePass;
    private String permissionErrorMessage;
    private List<IFieldDescribe> validFieldList;

    @Override
    protected void before(Arg arg) {
        log.info("arg {},actionContext {}", arg, actionContext);
        try {
            super.before(arg);
            isFuncPrivilegePass = true;
        } catch (PermissionError e) {
            isFuncPrivilegePass = false;
            permissionErrorMessage = e.getMessage();
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        Result importResult = new Result();
        importResult.setSuccess(false);

        if (!isFuncPrivilegePass) {
            objectDescribe = serviceFacade.findObject(actionContext.getTenantId(), actionContext.getObjectApiName());
            importResult.setMessage(permissionErrorMessage);
            return importResult;
        }
        String message = verifyChangeOrderObject();
        if (!Strings.isNullOrEmpty(message)) {
            importResult.setMessage(message);
            return importResult;
        }
        String errorMessage = verify(arg.getRows().get(0));
        if (!Strings.isNullOrEmpty(errorMessage)) {
            importResult.setMessage(errorMessage);
        } else {
            importResult.setSuccess(true);
        }
        return importResult;
    }

    protected String verifyChangeOrderObject() {
        if (ObjectDescribeExt.of(objectDescribe).isChangeOrderObject()) {
            return I18NExt.text(I18NKey.CHANGE_ORDER_OBJECT_NOT_SUPPORT_IMPORT);
        }
        return null;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        recordLog();
        return after;
    }

    protected abstract void recordLog();

    protected abstract List<IFieldDescribe> getValidImportFields();

    protected abstract String customVerify();

    String verify(ObjectDataDocument sourceData) {
        try {
            if (BooleanUtils.isFalse(objectDescribe.isActive())) {
                return I18NExt.getOrDefault(I18NKey.OBJECT_INVALID, "对象『{0}』已被禁用", objectDescribe.getDisplayName());// ignoreI18n
            }
            //获取有权限的字段
            validFieldList = getValidImportFields();
            //通过领域插件定制导入表头
            getValidImportFieldsDomainPlugin();
            // 处理唯一性规则字段
            handelUniqueRuleFields(validFieldList);
            if (CollectionUtils.empty(validFieldList)) {
                return I18N.text(I18NKey.NO_PRIVILEGE_IMPORT_FIELDS);
            }
            // 对于validFieldList, 补充数据ID列
            if (arg.getMatchingType() == BaseImportDataAction.MATCHING_TYPE_ID) {
                if (isSupportFieldMapping()) {
                    ImportExportExt.updateImportID(validFieldList, arg.getImportType());
                } else {
                    ImportExportExt.supportID(validFieldList, arg.getImportType());
                }
            }
            //业务类型设置为必填
            validFieldList.forEach(fieldDescribe -> {
                if (Objects.equals(IFieldType.RECORD_TYPE, fieldDescribe.getType())) {
                    RecordTypeFieldDescribe recordTypeFieldDescribe = (RecordTypeFieldDescribe) fieldDescribe;
                    if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.IMPORT_RECORD_TYPE, actionContext.getTenantId())
                            && recordTypeFieldDescribe.getRecordTypeOptions().size() == 1) {
                        String label = fieldDescribe.getLabel();
                        if (fieldDescribe.isRequired()) {
                            label = label + I18N.text(I18NKey.MUST_FILL_IN);
                        }
                        if (!sourceData.containsKey(label)) {
                            sourceData.put(fieldDescribe.getLabel(), null);
                        }
                        fieldDescribe.setRequired(Boolean.FALSE);
                    } else {
                        fieldDescribe.setRequired(Boolean.TRUE);
                    }
                }
            });

            if (BooleanUtils.isTrue(arg.getSupportFieldMapping())) {
                return verifyFieldsByMatchType(validFieldList, sourceData, arg.getMatchingType());
            }
            //校验字段, 1: 按ID导入校验, 2: 按label导入校验
            return validateFields(validFieldList, sourceData, arg.getMatchingType());
        } catch (Exception e) {
            log.error("Error in verify data in BulkImportService", e);
            return e.getMessage();
        }
    }

    private String verifyFieldsByMatchType(List<IFieldDescribe> validFieldList, ObjectDataDocument sourceData, Integer matchingType) {
        IObjectData data = convertColIndexToApiName(sourceData);
        switch (matchingType) {
            case BaseImportDataAction.MATCHING_TYPE_ID:
                return verifyFields(IObjectData.ID, validFieldList, data);
            case BaseImportDataAction.MATCHING_TYPE_NAME:
                return verifyFields(IObjectData.NAME, validFieldList, data);
            case BaseImportDataAction.MATCHING_TYPE_UNIQUE_RULE:
                return verifyFieldsByUniqueRule(validFieldList, data);
            case BaseImportDataAction.MATCHING_TYPE_SPECIFIED_FIELD:
                return verifyFields(arg.getSpecifiedField(), validFieldList, data);
            default:
                return "Import System Error!";
        }
    }

    protected IObjectData convertColIndexToApiName(ObjectDataDocument sourceData) {
        if (CollectionUtils.empty(fieldMappings)) {
            return null;
        }
        IObjectData data = new ObjectData();
        Set<Map.Entry<String, Object>> entries = sourceData.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            String apiName = fieldMappings.get(objectDescribe.getApiName()).stream()
                    .filter(x -> Objects.equals(x.getColIndex(), entry.getKey()))
                    .map(FieldMapping::getApiName)
                    .findFirst().orElse(null);
            if (StringUtils.isBlank(apiName)) {
                continue;
            }
            String value = Objects.isNull(entry.getValue()) ? "" : String.valueOf(entry.getValue()).trim();
            data.set(apiName, value);
        }
        return data;
    }

    private String verifyFields(String matchTypeField, List<IFieldDescribe> validFieldList, IObjectData data) {
        String errorMessage = validateMatchTypeField(matchTypeField, data);
        if (StringUtils.isNotBlank(errorMessage)) {
            return errorMessage;
        }
        return verifyFields(validFieldList, data);
    }

    protected String verifyFieldsByUniqueRule(List<IFieldDescribe> validFieldList, IObjectData data) {
        String errorMessage = verifyFields(validFieldList, data);
        if (StringUtils.isNotBlank(errorMessage)) {
            return errorMessage;
        }
        return verifyUniqueRule(validFieldList, data);
    }

    protected String verifyFields(List<IFieldDescribe> validFieldList, IObjectData data) {
        String errorMessage;
        errorMessage = validateImportReferenceMapping(ObjectDataDocument.of(data));
        if (StringUtils.isNotBlank(errorMessage)) {
            return errorMessage;
        }
        errorMessage = validateDuplicatedSearchRule();
        if (StringUtils.isNotBlank(errorMessage)) {
            return errorMessage;
        }
        errorMessage = validateRequiredFields(validFieldList, data);
        if (StringUtils.isNotBlank(errorMessage)) {
            return errorMessage;
        }
        errorMessage = customVerify();
        if (!Strings.isNullOrEmpty(errorMessage)) {
            return errorMessage;
        }
        return null;
    }

    private String validateImportReferenceMapping(ObjectDataDocument data) {
        if (!importReferenceFieldMappingSwitch) {
            return null;
        }

        List<ImportReferenceMapping.ReferenceFieldMapping> referenceFieldMapping = importReferenceMapping.getReferenceFieldMapping();

        Set<String> objectApiNames = new HashSet<>();

        for (ImportReferenceMapping.ReferenceFieldMapping mapping : referenceFieldMapping) {
            if (mapping.getTargetObjectApiName() != null) {
                objectApiNames.add(mapping.getTargetObjectApiName());
            }
        }
        List<String> errorMsg = Lists.newArrayList();
        Map<String, IObjectDescribe> describeMap = serviceFacade.findObjectsWithoutCopy(actionContext.getTenantId(), objectApiNames);
        for (ImportReferenceMapping.ReferenceFieldMapping fieldMapping : referenceFieldMapping) {
            String objectApiName = fieldMapping.getObjectApiName();
            if (!Objects.equals(objectApiName, objectDescribe.getApiName())) {
                continue;
            }

            String objectReferenceFieldApiName = fieldMapping.getObjectReferenceFieldApiName();
            Optional<IFieldDescribe> orFieldDescribe = ObjectDescribeExt.of(objectDescribe).getActiveFieldDescribeSilently(objectReferenceFieldApiName);
            if (!orFieldDescribe.isPresent()) {
                continue;
            }

            String label;
            if (BooleanUtils.isTrue(arg.getSupportFieldMapping())) {
                label = orFieldDescribe.get().getApiName();
            } else {
                label = orFieldDescribe.get().getLabel() + "_" + I18N.text(I18NKey.UNIQUE_FILL_IN);
                if (orFieldDescribe.get().isRequired()) {
                    label = label + I18N.text(I18NKey.MUST_FILL_IN);
                }
            }
            if (!data.containsKey(label)) {
                continue;
            }

            String targetObjectApiName = fieldMapping.getTargetObjectApiName();
            String specifiedUniqueFieldApiName = fieldMapping.getSpecifiedUniqueFieldApiName();
            IObjectDescribe describe = describeMap.get(targetObjectApiName);
            if (Objects.nonNull(describe)) {
                Optional<IFieldDescribe> fieldDescribeSilently = ObjectDescribeExt.of(describe).getActiveFieldDescribeSilently(specifiedUniqueFieldApiName);
                if (fieldDescribeSilently.isPresent()) {
                    if (!fieldDescribeSilently.get().isUnique() && !IObjectData.ID.equals(specifiedUniqueFieldApiName)) {
                        errorMsg.add(I18N.text(I18NKey.EXCEL_OBJECT_REFERENCE_ERROR, orFieldDescribe.get().getLabel(), fieldDescribeSilently.get().getLabel()));
                    }
                } else {
                    errorMsg.add(I18N.text(I18NKey.EXCEL_OBJECT_REFERENCE_FIELD_NOT_EXIST, orFieldDescribe.get().getLabel(), specifiedUniqueFieldApiName));
                }
            } else {
                errorMsg.add(I18N.text(I18NKey.EXCEL_OBJECT_REFERENCE_OBJECT_NOT_EXIST, orFieldDescribe.get().getLabel(), targetObjectApiName));
            }

        }
        if (CollectionUtils.notEmpty(errorMsg)) {
            return String.join("\n", errorMsg);
        }
        return null;
    }

    protected String validateRequiredFields(List<IFieldDescribe> validFieldList, IObjectData data) {
        List<String> requiredNotExistFieldLabels = Lists.newArrayList();
        for (IFieldDescribe field : validFieldList) {
            if (!field.isRequired()) {
                continue;
            }
            if (!data.containsField(field.getApiName())) {
                requiredNotExistFieldLabels.add(field.getLabel());
            }
        }
        if (CollectionUtils.notEmpty(requiredNotExistFieldLabels)) {
            return I18N.text(I18NKey.EXCEL_COLUMN_AND_TEMPLATE_DIFFERENT_1, String.join(",", requiredNotExistFieldLabels));
        }
        return null;
    }

    @Override
    protected final ImportVerifyActionDomainPlugin.Arg buildDomainPluginArg(String method, List<String> recordTypeList) {
        return ImportVerifyActionDomainPlugin.Arg.builder()
                .headerFieldList(CollectionUtils.nullToEmpty(validFieldList).stream().map(IFieldDescribe::getApiName).collect(Collectors.toList()))
                .build();
    }

    @Override
    protected final void processDomainPluginResult(String method, DomainPlugin.Arg pluginArg, DomainPlugin.Result pluginResult) {
        ImportVerifyActionDomainPlugin.Result domainPluginResult = (ImportVerifyActionDomainPlugin.Result) pluginResult;
        if (CollectionUtils.notEmpty(domainPluginResult.getHeaderFieldListToRemove()) && CollectionUtils.notEmpty(validFieldList)) {
            validFieldList.removeIf(x -> domainPluginResult.getHeaderFieldListToRemove().contains(x.getApiName()));
        }
    }

    private void getValidImportFieldsDomainPlugin() {
        if (CollectionUtils.empty(validFieldList)) {
            return;
        }
        runDomainPlugin("getValidImportFields", true, (domainPlugin, pluginArg) ->
                ((ImportVerifyActionDomainPlugin) domainPlugin).getValidImportFields(actionContext, (ImportVerifyActionDomainPlugin.Arg) pluginArg));
    }

    protected List<IFieldDescribe> handelUniqueRuleFields(List<IFieldDescribe> sortedFieldList) {
        return sortedFieldList;
    }

    /**
     * 对表头进行校验，非必填字段不校验
     *
     * @param matchingType 1:按ID导入,2:按name导入
     */
    private String validateFields(List<IFieldDescribe> validFieldList, ObjectDataDocument sourceData, Integer matchingType) {
        String errorMessage = validateImportReferenceMapping(sourceData);
        if (StringUtils.isNotBlank(errorMessage)) {
            return errorMessage;
        }
        Set<Map.Entry<String, Object>> entrySet = sourceData.entrySet();
        switch (matchingType) {
            case BaseImportDataAction.MATCHING_TYPE_ID:
                errorMessage = validateMatchTypeField(IObjectData.ID, entrySet);
                if (StringUtils.isNotBlank(errorMessage)) {
                    return errorMessage;
                }
                return validateFieldsByID(validFieldList, entrySet);
            case BaseImportDataAction.MATCHING_TYPE_NAME:
                errorMessage = validateMatchTypeField(IObjectData.NAME, entrySet);
                if (StringUtils.isNotBlank(errorMessage)) {
                    return errorMessage;
                }
                return validateFieldsByName(validFieldList, entrySet);
            case BaseImportDataAction.MATCHING_TYPE_UNIQUE_RULE:
                return validateFieldsByUniqueRule(validFieldList, entrySet);
            case BaseImportDataAction.MATCHING_TYPE_SPECIFIED_FIELD:
                errorMessage = validateMatchTypeField(arg.getSpecifiedField(), entrySet);
                if (StringUtils.isNotBlank(errorMessage)) {
                    return errorMessage;
                }
                return validateFieldsBySpecifiedField(validFieldList, entrySet);
            default:
                return "Import System Error!";
        }
    }

    protected String validateMatchTypeField(String matchTypeField, Set<Map.Entry<String, Object>> entrySet) {
        return null;
    }

    protected String validateMatchTypeField(String matchTypeField, IObjectData data) {
        return null;
    }

    /**
     * 校验指定字段是否存在
     */
    protected String validateFieldsBySpecifiedField(List<IFieldDescribe> validFieldList, Set<Map.Entry<String, Object>> entrySet) {
        IFieldDescribe fieldDescribe = ObjectDescribeExt.of(objectDescribe).getFieldDescribe(arg.getSpecifiedField());
        if (BooleanUtils.isNotTrue(fieldDescribe.isUnique())) {
            return I18N.text(I18NKey.UPDATE_IMPORT_NON_UNIQUE_FIELD, fieldDescribe.getLabel());
        }
        String error = validateFieldsByName(validFieldList, entrySet);
        if (!Strings.isNullOrEmpty(error)) {
            return error;
        }
        return null;
    }

    /**
     * 校验唯一性id
     */
    protected String validateFieldsByID(List<IFieldDescribe> validFieldList, Set<Map.Entry<String, Object>> entrySet) {
        // 校验查重规则
        String errorMessage = validateDuplicatedSearchRule();
        if (StringUtils.isNotBlank(errorMessage)) {
            return errorMessage;
        }
        for (IFieldDescribe field : validFieldList) {
            //非必填字段不校验
            if (!field.isRequired()) {
                continue;
            }
            //主属性和必填关联字段不校验(用ID校验)
            if (IObjectData.NAME.equals(field.getApiName()) || IFieldType.MASTER_DETAIL.equals(field.getType()) ||
                    IFieldType.OBJECT_REFERENCE.equals(field.getType())) {
                continue;
            }
            String error = validate(field, entrySet);
            if (StringUtils.isNotBlank(error)) {
                return error;
            }
        }
        return null;
    }

    /**
     * 不校验唯一性id
     */
    protected String validateFieldsByName(List<IFieldDescribe> validFieldList, Set<Map.Entry<String, Object>> entrySet) {
        // 校验查重规则
        String error = validateDuplicatedSearchRule();
        if (StringUtils.isNotBlank(error)) {
            return error;
        }
        for (IFieldDescribe field : validFieldList) {
            // 非必填字段或者ID字段不校验
            if (!field.isRequired() || (Objects.nonNull(field.getExtendInfo())
                    && field.getExtendInfo().containsKey(ImportExportExt.IMPORT_TYPE))) {
                continue;
            }
            error = validate(field, entrySet);
            if (StringUtils.isNotBlank(error)) {
                return error;
            }
        }
        return null;
    }

    protected String validateFieldsByUniqueRule(List<IFieldDescribe> validFieldList, Set<Map.Entry<String, Object>> entrySet) {
        // 校验查重规则
        String error = validateDuplicatedSearchRule();
        if (!Strings.isNullOrEmpty(error)) {
            return error;
        }
        error = validateFieldsByName(validFieldList, entrySet);
        if (!Strings.isNullOrEmpty(error)) {
            return error;
        }
        return validateUniqueRule(validFieldList, entrySet);
    }

    protected String validateDuplicatedSearchRule() {
        duplicatedSearchList.removeIf(x -> !DuplicatedSearchExt.isSupportImport(x));
        if (CollectionUtils.empty(duplicatedSearchList)) {
            return null;
        }
        duplicatedSearchList.removeIf(x -> !DuplicatedSearchExt.isEffective(x));
        if (CollectionUtils.empty(duplicatedSearchList)) {
            return I18NExt.getOrDefault(I18NKey.DUPLICATED_SEARCH_RULE_UN_EFFECTIVE, "查重规则正在生效中，请稍后重试");// ignoreI18n
        }
        return null;
    }

    protected String verifyUniqueRule(List<IFieldDescribe> validFieldList, IObjectData data) {
        if (Objects.isNull(uniqueRule) || !uniqueRule.isUseWhenImportExcel()) {
            return null;
        }
        if (!UniqueRuleExt.isEffective(uniqueRule)) {
            log.warn("Uniqueness rule is in effect, describeApiName=>{}, user=>{}", objectDescribe.getApiName(),
                    JSON.toJSONString(actionContext.getUser()));
            return I18N.text(I18NKey.UNIQUENESS_RULE_IS_IN_EFFECT);
        }
        // 校验功能权限
        List<String> ruleFieldName = UniqueRuleExt.of(uniqueRule).getRuleFieldName();
        // 不在validFieldList中出现的字段
        List<String> notValidFields = getNotValidFieldsByRuleField(validFieldList, ruleFieldName);

        if (isFieldNotExist(notValidFields)) {
            log.warn("Uniqueness field not exist, describeApiName=>{}, user=>{}", objectDescribe.getApiName(),
                    JSON.toJSONString(actionContext.getUser()));
            return I18N.text(I18NKey.UNIQUENESS_FIELD_NOT_EXIST);
        }

        List<IFieldDescribe> fieldDescribes = notValidFields.stream()
                .map(x -> objectDescribe.getFieldDescribe(x)).collect(Collectors.toList());
        if (CollectionUtils.notEmpty(fieldDescribes)) {
            String labels = getByMapper(fieldDescribes, IFieldDescribe::getLabel);
            String apiNames = getByMapper(fieldDescribes, IFieldDescribe::getApiName);
            log.warn("No field permissions, apiNames=>{}, labels=>{}", apiNames, labels);
            return I18N.text(I18NKey.NOT_HAVE_FIELD_PERMISSION_CANNOT_IMPORT, labels);
        }
        // 校验表头字段
        List<IFieldDescribe> validImportFieldList = getValidImportFieldList(validFieldList, ruleFieldName);
        String labels = validImportFieldList.stream()
                .filter(field -> !data.containsField(field.getApiName()))
                .map(IFieldDescribe::getLabel)
                .collect(Collectors.joining("],["));
        if (!Strings.isNullOrEmpty(labels)) {
            return I18N.text(I18NKey.UNIQUENESS_RULE_MISSING_COLUMN, labels);
        }
        return null;
    }

    /**
     * 唯一性规则表头字段校验
     */
    protected String validateUniqueRule(List<IFieldDescribe> validFieldList, Set<Map.Entry<String, Object>> entrySet) {
        if (Objects.isNull(uniqueRule) || !uniqueRule.isUseWhenImportExcel()) {
            return null;
        }
        if (!UniqueRuleExt.isEffective(uniqueRule)) {
            log.warn("Uniqueness rule is in effect, describeApiName=>{}, user=>{}", objectDescribe.getApiName(),
                    JSON.toJSONString(actionContext.getUser()));
            return I18N.text(I18NKey.UNIQUENESS_RULE_IS_IN_EFFECT);
        }
        // 校验功能权限
        List<String> ruleFieldName = UniqueRuleExt.of(uniqueRule).getRuleFieldName();
        // 不在validFieldList中出现的字段
        List<String> notValidFields = getNotValidFieldsByRuleField(validFieldList, ruleFieldName);

        if (isFieldNotExist(notValidFields)) {
            log.warn("Uniqueness field not exist, describeApiName=>{}, user=>{}", objectDescribe.getApiName(),
                    JSON.toJSONString(actionContext.getUser()));
            return I18N.text(I18NKey.UNIQUENESS_FIELD_NOT_EXIST);
        }

        List<IFieldDescribe> fieldDescribes = notValidFields.stream()
                .map(x -> objectDescribe.getFieldDescribe(x)).collect(Collectors.toList());
        if (CollectionUtils.notEmpty(fieldDescribes)) {
            String labels = getByMapper(fieldDescribes, IFieldDescribe::getLabel);
            String apiNames = getByMapper(fieldDescribes, IFieldDescribe::getApiName);
            log.warn("No field permissions, apiNames=>{}, labels=>{}", apiNames, labels);
            return I18N.text(I18NKey.NOT_HAVE_FIELD_PERMISSION_CANNOT_IMPORT, labels);
        }
        // 校验表头字段
        List<IFieldDescribe> validImportFieldList = getValidImportFieldList(validFieldList, ruleFieldName);
        String labels = validImportFieldList.stream()
                .filter(field -> entrySet.stream().noneMatch(entry -> validTitles(entry.getKey(), field)))
                .map(IFieldDescribe::getLabel)
                .collect(Collectors.joining("],["));
        if (!Strings.isNullOrEmpty(labels)) {
            return I18N.text(I18NKey.UNIQUENESS_RULE_MISSING_COLUMN, labels);
        }
        return null;
    }

    protected List<IFieldDescribe> getValidImportFieldList(List<IFieldDescribe> validFieldList, List<String> ruleFieldName) {
        return validFieldList.stream().filter(field -> ruleFieldName.contains(field.getApiName())).collect(Collectors.toList());
    }

    protected List<String> getNotValidFieldsByRuleField(List<IFieldDescribe> validFieldList, List<String> ruleFieldName) {
        return ruleFieldName.stream()
                .filter(x -> validFieldList.stream().noneMatch(field -> Objects.equals(x, field.getApiName())))
                .collect(Collectors.toList());
    }

    private boolean isFieldNotExist(List<String> notValidFields) {
        return notValidFields.stream().map(x -> objectDescribe.getFieldDescribe(x))
                .anyMatch(x -> Objects.isNull(x) || !Boolean.TRUE.equals(x.isActive()));
    }

    private String getByMapper(List<IFieldDescribe> fieldDescribes, Function<IFieldDescribe, String> mapper) {
        return fieldDescribes.stream().map(mapper).collect(Collectors.joining("][", "[", "]"));
    }

    private String validate(IFieldDescribe field, Set<Map.Entry<String, Object>> entrySet) {
        boolean isExist = false;
        List<String> forLog = Lists.newArrayList();
        for (Map.Entry<String, Object> entry : entrySet) {
            String title = entry.getKey();
            forLog.add(title);
            if ("rowNo".equals(title)) {
                continue;
            }
            if (validTitles(title, field)) {
                isExist = true;
                break;
            }
        }
        if (!isExist) {
            log.warn("Field is not exist,apiName:{}, label:{}, importTitles:{}", field.getApiName(), field.getLabel(), forLog);
            return I18N.text(I18NKey.EXCEL_COLUMN_AND_TEMPLATE_DIFFERENT_1, field.getLabel());
        }
        String errorMessage = customVerify();
        if (!Strings.isNullOrEmpty(errorMessage)) {
            return errorMessage;
        }
        return null;
    }
}
