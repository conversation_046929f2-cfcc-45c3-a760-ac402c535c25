package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.i18n.I18NExt
import com.facishare.paas.appframework.metadata.DescribeLogicService
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.elasticsearch.common.util.set.Sets
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class ObjectDataSyncServiceTest extends Specification {

    ObjectDataSyncService objectDataSyncService
    DescribeLogicService describeLogicService = Mock(DescribeLogicService)

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def setup() {
        objectDataSyncService = new ObjectDataSyncService(describeLogicService: describeLogicService)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateAddFieldDescribes方法在字段描述为空情况下的行为
     */
    def "validateAddFieldDescribesTest空字段描述"() {
        given:
        String tenantId = "74255"
        Collection<IFieldDescribe> fieldDescribes = []

        when:
        objectDataSyncService.validateAddFieldDescribes(tenantId, fieldDescribes)

        then:
        noExceptionThrown()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateAddFieldDescribes方法在字段描述中没有引用对象字段的情况下的行为
     */
    def "validateAddFieldDescribesTest没有引用对象字段"() {
        given:
        String tenantId = "74255"
        Collection<IFieldDescribe> fieldDescribes = [new TextFieldDescribe(["api_name": "textField", "target_api_name": "AccountObj"])]

        when:
        objectDataSyncService.validateAddFieldDescribes(tenantId, fieldDescribes)

        then:
        noExceptionThrown()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateAddFieldDescribes方法在引用对象字段存在于目标企业的情况下的行为
     */
    def "validateAddFieldDescribesTest引用对象字段存在"() {
        given:
        String tenantId = "74255"
        // 创建引用对象字段
        IFieldDescribe objectRefField = new ObjectReferenceFieldDescribe(["api_name": "accountField", "target_api_name": "AccountObj"])
        Collection<IFieldDescribe> fieldDescribes = [objectRefField]

        and: "模拟引用对象存在"
        Map<String, IObjectDescribe> describeMap = ["AccountObj": new ObjectDescribe()]
        describeLogicService.findObjectsWithoutCopy(tenantId, Sets.newHashSet(["AccountObj"])) >> describeMap

        when:
        objectDataSyncService.validateAddFieldDescribes(tenantId, fieldDescribes)

        then:
        noExceptionThrown()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateAddFieldDescribes方法在有多个引用对象字段且都存在于目标企业的情况下的行为
     */
    def "validateAddFieldDescribesTest多个引用对象字段存在"() {
        given:
        String tenantId = "74255"
        // 创建多个引用对象字段
        IFieldDescribe objectRefField1 = new ObjectReferenceFieldDescribe(["api_name": "accountField", "target_api_name": "AccountObj"])
        IFieldDescribe objectRefField2 = new ObjectReferenceFieldDescribe(["api_name": "contactField", "target_api_name": "ContactObj"])
        Collection<IFieldDescribe> fieldDescribes = [objectRefField1, objectRefField2]

        and: "模拟引用对象存在"
        Map<String, IObjectDescribe> describeMap = [
                "AccountObj": new ObjectDescribe(),
                "ContactObj": new ObjectDescribe()
        ]
        describeLogicService.findObjectsWithoutCopy(tenantId, Sets.newHashSet(["AccountObj", "ContactObj"])) >> describeMap

        when:
        objectDataSyncService.validateAddFieldDescribes(tenantId, fieldDescribes)

        then:
        noExceptionThrown()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateAddFieldDescribes方法在引用对象字段不存在于目标企业时抛出异常的行为
     */
    def "validateAddFieldDescribesError引用对象字段不存在"() {
        given:
        String tenantId = "74255"
        // 创建引用对象字段
        IFieldDescribe objectRefField = new ObjectReferenceFieldDescribe(["api_name": "customField", "target_api_name": "CustomObj__c"])
        Collection<IFieldDescribe> fieldDescribes = [objectRefField]

        and: "模拟引用对象不存在"
        Map<String, IObjectDescribe> describeMap = [:]
        describeLogicService.findObjectsWithoutCopy(tenantId, Sets.newHashSet(["CustomObj__c"])) >> describeMap

        when:
        objectDataSyncService.validateAddFieldDescribes(tenantId, fieldDescribes)

        then:
        thrown(ValidateException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateAddFieldDescribes方法在多个引用对象字段中部分不存在于目标企业时抛出异常的行为
     */
    def "validateAddFieldDescribesError部分引用对象字段不存在"() {
        given:
        String tenantId = "74255"
        // 创建多个引用对象字段，其中一个引用不存在的对象

        IFieldDescribe objectRefField1 = new ObjectReferenceFieldDescribe(["api_name": "accountField", "target_api_name": "AccountObj"])
        IFieldDescribe objectRefField2 = new ObjectReferenceFieldDescribe(["api_name": "customField", "target_api_name": "NonExistentObj__c"])
        Collection<IFieldDescribe> fieldDescribes = [objectRefField1, objectRefField2]

        and: "模拟部分引用对象存在"
        Map<String, IObjectDescribe> describeMap = ["AccountObj": new ObjectDescribe()]
        describeLogicService.findObjectsWithoutCopy(tenantId, Sets.newHashSet(["AccountObj", "NonExistentObj__c"])) >> describeMap


        when:
        objectDataSyncService.validateAddFieldDescribes(tenantId, fieldDescribes)

        then:
        thrown(ValidateException)
    }
}