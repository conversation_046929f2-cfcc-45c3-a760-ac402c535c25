package com.facishare.paas.appframework.core.predef.service.dto.layout;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * Created by zhouwr on 2017/10/27
 */
public interface FindByObjDescribeApiName {
    @Data
    class Arg {
        @JSONField(name = "M1")
        private String objectDescribeApiName;
        private String layoutType;
        private String namespace;
        // 是否包含所有命名空间的布局
        private Boolean includeAllNamespace;
        private String sourceInfo;

        // 用于查询流程待办布局的对象apiName
        private String whatDescribeApiName;

        /**
         * 应用Id
         */
        private String appId;

        public boolean includeAllNamespace() {
            return includeAllNamespace != null && includeAllNamespace;
        }
    }

    @Data
    @AllArgsConstructor
    class Result {
        @J<PERSON>NField(name = "M7")
        private List<LayoutDocument> layouts;
        private ManageGroupDTO manageGroup;
    }
}
