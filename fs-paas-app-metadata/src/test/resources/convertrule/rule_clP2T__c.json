[{"_id": "64ae89744ccb540001c29b83", "rule_name": "主从转换-『可用』", "rule_api_name": "rule_clP2T__c", "target_api_name": "object_ssu7t__c", "source_api_name": "object_3BbVb__c", "target_display_name": "zj-主", "source_display_name": "zxf-主", "define_type": "custom", "master_rule_api_name": null, "last_modified_by": "1000", "last_modified_time": 1690265192833, "created_by": "1000", "create_time": 1689160052061, "status": 0, "close_field_value": true, "associated_field_api_name": "field_O1lbo__c", "where_type": "function", "wheres": [{"connector": "OR", "filters": [{"value_type": 9, "operator": "IN", "field_name": "id", "field_values": ["convert_rule_filter__c"]}]}], "message": "您选择的zxf-主数据不符合条件，请重新选择。", "is_back_write": true, "back_write_model": "submit", "is_close_logic": true, "close_strategy": 0, "close_field_api_name": "field_dy79C__c", "close_wheres": null, "excess_check_mode": "no_control", "excess_condition": "$field_O1lbo__c__r.field_B0c4B__c$  + $field_6U0pa__c$ == 240", "default_to_zero": true, "excess_prompt": "超额了，超额了，超额了!！", "remark": "可用对象", "field_mapping": [{"_id": "64bf6669637fbc0001064d24", "source_field_api_name": "record_type", "target_field_api_name": "record_type", "option_mapping": [{"_id": "64bf6669637fbc0001064d25", "source_option": "record_2PqA1__c", "target_option": "record_1p9T3__c"}]}, {"_id": "64bf6669637fbc0001064d26", "source_field_api_name": "name", "target_field_api_name": "name"}, {"_id": "64bf6669637fbc0001064d27", "source_field_api_name": "field_p3b8C__c", "target_field_api_name": "field_b27fh__c"}], "master_api_name": null}, {"_id": "64ae89744ccb540001c29b84", "rule_name": "zxf-从 转换为 zj-从", "rule_api_name": "rule_1g031__c", "target_api_name": "object_pHn1o__c", "source_api_name": "object_Nc1Ek__c", "target_display_name": "zj-从", "source_display_name": "zxf-从", "define_type": "custom", "master_rule_api_name": "rule_clP2T__c", "last_modified_by": "1000", "last_modified_time": 1690265192833, "created_by": "1000", "create_time": 1689160052062, "status": 0, "close_field_value": true, "associated_field_api_name": "field_99l28__c", "where_type": null, "wheres": null, "message": null, "is_back_write": null, "back_write_model": "submit", "is_close_logic": null, "close_strategy": 0, "close_field_api_name": "field_hkfv8__c", "close_wheres": [{"connector": "OR", "filters": [{"value_type": 0, "operator": "GTE", "field_name": "field_20oz0__c", "field_values": ["10"]}]}], "excess_check_mode": "no_control", "excess_condition": null, "default_to_zero": true, "excess_prompt": null, "remark": "从备注一下！？！？！", "field_mapping": [{"_id": "64bf6669637fbc0001064d29", "source_field_api_name": "record_type", "target_field_api_name": "record_type", "option_mapping": [{"_id": "64bf6669637fbc0001064d2a", "source_option": "default__c", "target_option": "default__c"}, {"_id": "64bf6669637fbc0001064d2b", "source_option": "record_Gd8l2__c", "target_option": "record_GCCwk__c"}]}, {"_id": "64bf6669637fbc0001064d2c", "source_field_api_name": "name", "target_field_api_name": "name"}], "master_api_name": "object_3BbVb__c"}, {"_id": "64e56faf90e0870001c0fe89", "rule_name": "zxf-从2 转换为 zj-从2", "rule_api_name": "rule_Z18Sc__c", "target_api_name": "object_ptTZE__c", "source_api_name": "object_axkN2__c", "target_display_name": "zj-从2", "source_display_name": "zxf-从2", "define_type": "custom", "master_rule_api_name": "rule_clP2T__c", "last_modified_by": "1000", "last_modified_time": 1692757935839, "created_by": "1000", "create_time": 1692757935839, "status": 0, "close_field_value": true, "associated_field_api_name": "field_7Y5oh__c", "where_type": null, "wheres": [], "message": null, "is_back_write": null, "back_write_model": "submit", "is_close_logic": null, "close_strategy": 0, "close_field_api_name": "field_hkfv8__c", "close_wheres": [{"connector": "OR", "filters": [{"value_type": 0, "operator": "GT", "field_name": "field_20oz0__c", "field_values": ["10"]}]}], "excess_check_mode": "no_control", "excess_condition": null, "default_to_zero": true, "excess_prompt": null, "remark": null, "field_mapping": [{"_id": "64e56fb090e0870001c0feb9", "source_field_api_name": "record_type", "target_field_api_name": "record_type", "option_mapping": [{"_id": "64e56fb090e0870001c0feba", "source_option": "default__c", "target_option": "default__c"}, {"_id": "64e56fb090e0870001c0febb", "source_option": "record_Gd8l2__c", "target_option": "default__c"}]}, {"_id": "64e56fb090e0870001c0febc", "source_field_api_name": "name", "target_field_api_name": "name"}], "master_api_name": "object_3BbVb__c"}]