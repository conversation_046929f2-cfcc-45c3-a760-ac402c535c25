package com.facishare.paas.appframework.privilege;


import com.facishare.paas.appframework.privilege.dto.FeedPrivilegeInfo;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(
        codec = "com.facishare.paas.appframework.common.service.codec.AppDefaultCodeC",
        value = "FEED-PRIVILEGE",
        desc = "feed跟进动态", // ignoreI18n
        contentType = "application/json")
public interface FeedPrivilegeProxy {

    @POST(value = "/API/v1/rest/object/FeedsManage/service/getActivityDataPermission", desc = "查询跟进动态")
    FeedPrivilegeInfo.Result getActivityDataPermission(@HeaderMap Map<String, String> headers, @Body FeedPrivilegeInfo.Arg arg);

    @POST(value = "/API/v1/rest/object/FeedsManage/service/upsertActivityDataPermission", desc = "更新跟进动态")
    FeedPrivilegeInfo.Result upsertActivityDataPermission(@HeaderMap Map<String, String> headers, @Body FeedPrivilegeInfo.Arg arg);
}
