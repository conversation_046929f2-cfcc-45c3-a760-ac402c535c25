package com.facishare.paas.appframework.common.util

import com.alibaba.fastjson.JSON
import org.apache.commons.lang3.Range
import spock.lang.Specification

class TenantUtilTest extends Specification {
    def "IsNewTenant"() {
        given:
        def rawConfig = '''[{"env":"FONESHARE","minEI":1,"nowEI":747752},{"env":"SBT","minEI":40010001,"nowEI":40010040},{"env":"KSC","minEI":40020001,"nowEI":40020016},{"env":"HUAWEI","minEI":60000001,"nowEI":60000521},{"env":"UCD","minEI":61000001,"nowEI":61000035},{"env":"ALE","minEI":62000001,"nowEI":62007266},{"env":"AWS","minEI":67000001,"nowEI":67000016}]'''
        def ALL_ENVS = JSON.parseArray(rawConfig, TenantUtil.TenantEnvInfo.class);
        def tenant = 749197L
        def has = false
                when :
        for (TenantUtil.TenantEnvInfo info : ALL_ENVS) {
            if (Range.between(info.getMinEI(), info.getNowEI()).contains(tenant)) {
                has = true
            }
        }
        print has

        then:
        1==1
    }
}
