package com.facishare.paas.appframework.log

import com.facishare.paas.I18N
import com.facishare.paas.appframework.log.dto.InternationalItem
import com.facishare.paas.appframework.log.dto.LogInfo
import com.facishare.paas.appframework.log.dto.ModifyRecord
import com.facishare.paas.appframework.log.dto.TeamMemberInfo
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import spock.lang.Specification

/**
 * GenerateByAI
 * Test class for TeamMemberModifyRecordProvider
 */
class TeamMemberModifyRecordProviderTest extends Specification {
    TeamMemberModifyRecordProvider provider
    LogInfo logInfo
    LogInfo.ObjectSnapshot snapshot
    Map<String, Object> snapshotMap

    void setup() {
        provider = new TeamMemberModifyRecordProvider()
        logInfo = new LogInfo()
        snapshotMap = Maps.newHashMap()
        snapshot = LogInfo.ObjectSnapshot.builder()
                .snapshot(snapshotMap)
                .build()
        logInfo.setSnapshot(snapshot)
    }

    /**
     * GenerateByAI
     * Test getApiName returns correct value
     */
    def "testGetApiNameTest"() {
        when:
        def result = provider.getApiName()

        then:
        result == "TeamMember"
    }

    /**
     * GenerateByAI
     * Test getModifyRecord with team member messages
     */
    def "testGetModifyRecordWithTeamMemberMessagesTest"() {
        given:
        def msgs = [
            TeamMemberInfo.Msg.builder()
                .operationType(ActionType.AddEmployee.getId())
                .msgMap(TeamMemberInfo.MsgMap.builder()
                    .operation("test_operation")
                    .permission(TeamMemberInfo.Info.builder()
                        .label("permission_label")
                        .value("permission_value")
                        .oldValue("old_permission_value")
                        .internationalLabel(InternationalItem.builder().build())
                        .internationalValue(InternationalItem.builder().build())
                        .internationalOldValue(InternationalItem.builder().build())
                        .build())
                    .role(TeamMemberInfo.Info.builder()
                        .label("role_label")
                        .value("role_value")
                        .oldValue("old_role_value")
                        .internationalLabel(InternationalItem.builder().build())
                        .internationalValue(InternationalItem.builder().build())
                        .internationalOldValue(InternationalItem.builder().build())
                        .build())
                    .build())
                .build()
        ]
        snapshot.setMsgs(msgs)

        GroovyMock(I18N, global: true)
        I18N.text("test_operation") >> "Translated Operation"

        when:
        ModifyRecord result = provider.getModifyRecord(logInfo)

        then:
        result != null
        result.msgList != null
        result.msgList.size() == 1
        result.msgList[0].operationLabel == ActionType.getNameById(ActionType.AddEmployee.getId())
        result.msgList[0].msgMap.operation == "Translated Operation"
    }

    /**
     * GenerateByAI
     * Test getModifyRecord with empty messages
     */
    def "testGetModifyRecordWithEmptyMessagesTest"() {
        given:
        snapshot.setMsgs(Lists.newArrayList())

        when:
        ModifyRecord result = provider.getModifyRecord(logInfo)

        then:
        result != null
        result.msgList != null
        result.msgList.isEmpty()
    }

    /**
     * GenerateByAI
     * Test getModifyRecord with null messages
     */
    def "testGetModifyRecordWithNullMessagesTest"() {
        given:
        snapshot.setMsgs(null)

        when:
        ModifyRecord result = provider.getModifyRecord(logInfo)

        then:
        result != null
        result.msgList != null
        result.msgList.isEmpty()
    }
} 