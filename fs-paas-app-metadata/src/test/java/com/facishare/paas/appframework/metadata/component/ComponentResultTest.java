package com.facishare.paas.appframework.metadata.component;

import com.facishare.paas.appframework.metadata.ComponentDefineType;
import com.facishare.paas.metadata.ui.layout.ICustomComponent;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class ComponentResultTest {

  private ComponentResult componentResult;

  @BeforeEach
  void setUp() {
    componentResult = new ComponentResult();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ComponentResult的基本属性设置和获取
   */
  @Test
  @DisplayName("正常场景 - 测试基本属性设置和获取")
  void testBasicProperties() {
    // 设置属性
    int code = 0;
    String message = "success";
    List<ComponentData> data = Lists.newArrayList();
    
    componentResult.setCode(code);
    componentResult.setMessage(message);
    componentResult.setData(data);
    
    // 验证属性
    assertEquals(code, componentResult.getCode(), "状态码应该正确设置");
    assertEquals(message, componentResult.getMessage(), "消息应该正确设置");
    assertEquals(data, componentResult.getData(), "数据应该正确设置");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isSuccess方法 - 成功场景
   */
  @Test
  @DisplayName("正常场景 - 测试isSuccess方法返回true")
  void testIsSuccess_True() {
    componentResult.setCode(0);
    assertTrue(componentResult.isSuccess(), "状态码为0时应该返回成功");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isSuccess方法 - 失败场景
   */
  @Test
  @DisplayName("正常场景 - 测试isSuccess方法返回false")
  void testIsSuccess_False() {
    componentResult.setCode(1);
    assertFalse(componentResult.isSuccess(), "状态码非0时应该返回失败");
    
    componentResult.setCode(-1);
    assertFalse(componentResult.isSuccess(), "负数状态码时应该返回失败");
    
    componentResult.setCode(500);
    assertFalse(componentResult.isSuccess(), "错误状态码时应该返回失败");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试toCustomComponents方法 - 空数据场景
   */
  @Test
  @DisplayName("边界场景 - 测试toCustomComponents方法处理空数据")
  void testToCustomComponents_EmptyData() {
    // 测试null数据
    componentResult.setData(null);
    List<ICustomComponent> result = componentResult.toCustomComponents();
    assertNotNull(result, "结果不应为空");
    assertTrue(result.isEmpty(), "空数据应返回空列表");
    
    // 测试空列表
    componentResult.setData(Lists.newArrayList());
    result = componentResult.toCustomComponents();
    assertNotNull(result, "结果不应为空");
    assertTrue(result.isEmpty(), "空列表应返回空列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试toCustomComponents方法 - 正常数据转换
   */
  @Test
  @DisplayName("正常场景 - 测试toCustomComponents方法正常数据转换")
  void testToCustomComponents_NormalData() {
    // 创建测试数据
    ComponentData data1 = new ComponentData();
    data1.setApiName("component1");
    data1.setName("组件1");
    data1.setUrl("http://test1.com");
    
    ComponentData data2 = new ComponentData();
    data2.setApiName("component2");
    data2.setName("组件2");
    data2.setUrl("http://test2.com");
    
    List<ComponentData> dataList = Lists.newArrayList(data1, data2);
    componentResult.setData(dataList);
    
    // 执行转换
    List<ICustomComponent> result = componentResult.toCustomComponents();
    
    // 验证结果
    assertNotNull(result, "结果不应为空");
    assertEquals(2, result.size(), "应该转换出2个组件");
    
    // 验证第一个组件
    ICustomComponent component1 = result.get(0);
    assertEquals(ComponentDefineType.CUSTOM.getType(), component1.getDefineType(), "定义类型应为custom");
    assertEquals("component1", component1.getName(), "组件名称应正确");
    assertEquals("组件1", component1.getHeader(), "组件标题应正确");
    assertEquals("component1", component1.getCustomCompApiName(), "自定义组件API名称应正确");
    assertEquals("http://test1.com", component1.getCustomCompUrl(), "自定义组件URL应正确");
    
    // 验证第二个组件
    ICustomComponent component2 = result.get(1);
    assertEquals(ComponentDefineType.CUSTOM.getType(), component2.getDefineType(), "定义类型应为custom");
    assertEquals("component2", component2.getName(), "组件名称应正确");
    assertEquals("组件2", component2.getHeader(), "组件标题应正确");
    assertEquals("component2", component2.getCustomCompApiName(), "自定义组件API名称应正确");
    assertEquals("http://test2.com", component2.getCustomCompUrl(), "自定义组件URL应正确");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试toCustomComponents方法 - 包含null值的数据
   */
  @Test
  @DisplayName("边界场景 - 测试toCustomComponents方法处理包含null值的数据")
  void testToCustomComponents_WithNullValues() {
    // 创建包含null值的测试数据
    ComponentData data = new ComponentData();
    data.setApiName(null);
    data.setName(null);
    data.setUrl(null);
    
    List<ComponentData> dataList = Lists.newArrayList(data);
    componentResult.setData(dataList);
    
    // 执行转换
    List<ICustomComponent> result = componentResult.toCustomComponents();
    
    // 验证结果
    assertNotNull(result, "结果不应为空");
    assertEquals(1, result.size(), "应该转换出1个组件");
    
    ICustomComponent component = result.get(0);
    assertEquals(ComponentDefineType.CUSTOM.getType(), component.getDefineType(), "定义类型应为custom");
    assertNull(component.getName(), "组件名称应为null");
    assertNull(component.getHeader(), "组件标题应为null");
    assertNull(component.getCustomCompApiName(), "自定义组件API名称应为null");
    assertNull(component.getCustomCompUrl(), "自定义组件URL应为null");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试toCustomComponents方法 - 单个组件数据
   */
  @Test
  @DisplayName("正常场景 - 测试toCustomComponents方法处理单个组件")
  void testToCustomComponents_SingleComponent() {
    // 创建单个组件数据
    ComponentData data = new ComponentData();
    data.setApiName("single_component");
    data.setName("单个组件");
    data.setUrl("http://single.com");
    
    List<ComponentData> dataList = Lists.newArrayList(data);
    componentResult.setData(dataList);
    
    // 执行转换
    List<ICustomComponent> result = componentResult.toCustomComponents();
    
    // 验证结果
    assertNotNull(result, "结果不应为空");
    assertEquals(1, result.size(), "应该转换出1个组件");
    
    ICustomComponent component = result.get(0);
    assertEquals("single_component", component.getName(), "组件名称应正确");
    assertEquals("单个组件", component.getHeader(), "组件标题应正确");
    assertEquals("single_component", component.getCustomCompApiName(), "自定义组件API名称应正确");
    assertEquals("http://single.com", component.getCustomCompUrl(), "自定义组件URL应正确");
  }
}
