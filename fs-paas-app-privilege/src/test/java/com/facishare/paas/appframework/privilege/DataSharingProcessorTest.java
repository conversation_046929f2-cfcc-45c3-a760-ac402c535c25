package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.privilege.dto.DataSharing;
import com.facishare.paas.appframework.privilege.dto.EntitySharePojo;
import com.facishare.paas.appframework.privilege.dto.RangesExt;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.ErrorCode;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;

import static com.facishare.paas.appframework.privilege.DataPrivilegeService.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试内容描述：测试DataSharingProcessor类的数据共享处理相关方法
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class DataSharingProcessorTest {

  @Mock
  private OrgService orgService;
  
  @Mock
  private UserRoleInfoService userRoleInfoService;
  
  @Mock
  private IObjectDescribeService objectDescribeService;
  
  @InjectMocks
  private DataSharingProcessor dataSharingProcessor;

  private User testUser;

  @BeforeEach
  void setUp() {
    testUser = User.builder()
        .tenantId("123456")
        .userId("78910")
        .build();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试processorByDataSharing方法，正常处理数据共享
   */
  @Test
  @DisplayName("正常场景 - 测试processorByDataSharing方法")
  void testProcessorByDataSharing_NormalCase() throws MetadataServiceException {
    try (MockedStatic<ActionContextExt> actionContextExtMock = mockStatic(ActionContextExt.class)) {
      
      // 准备测试数据
      DataSharing dataSharing = createTestDataSharing();
      Table<Integer, String, String> mockTable = HashBasedTable.create();
      
      Map<String, String> userNameMap = new HashMap<>();
      userNameMap.put("user1", "用户1");
      userNameMap.put("user2", "用户2");
      
      List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = Lists.newArrayList();
      QueryDeptInfoByDeptIds.DeptInfo deptInfo = new QueryDeptInfoByDeptIds.DeptInfo();
      deptInfo.setDeptId("dept1");
      deptInfo.setDeptName("部门1");
      deptInfos.add(deptInfo);
      
      Map<String, String> roleMap = new HashMap<>();
      roleMap.put("role1", "角色1");
      
      Map<String, String> groupNameMap = new HashMap<>();
      groupNameMap.put("group1", "用户组1");
      
      List<IObjectDescribe> describeList = Lists.newArrayList();
      IObjectDescribe describe = mock(IObjectDescribe.class);
      when(describe.getApiName()).thenReturn("TestObj");
      describeList.add(describe);
      
      Map<String, IObjectDescribe> describeMap = new HashMap<>();
      describeMap.put("TestObj", describe);
      
      ActionContextExt actionContextExt = mock(ActionContextExt.class);
      IActionContext mockContext = mock(IActionContext.class);
      when(actionContextExt.getContext()).thenReturn(mockContext);
      
      // 配置Mock行为
      actionContextExtMock.when(() -> ActionContextExt.of(testUser)).thenReturn(actionContextExt);
      
      when(orgService.getUserNameMapByIds(eq(testUser.getTenantId()), eq(testUser.getUserId()), anyList())).thenReturn(userNameMap);
      when(orgService.getDeptInfoNameByIds(eq(testUser.getTenantId()), eq(testUser.getUserId()), anyList())).thenReturn(deptInfos);
      when(userRoleInfoService.queryRoleNameByRoleCode(eq(testUser), anyList())).thenReturn(roleMap);
      when(orgService.getGroupNameByIds(eq(testUser.getTenantId()), eq(testUser.getUserId()), anyList())).thenReturn(groupNameMap);
      when(objectDescribeService.findDescribeListByApiNames(eq(testUser.getTenantId()), anyList(), any())).thenReturn(describeList);
      
      // Mock EntitySharePojo.EntitySharePojoHelper.of方法
      try (MockedStatic<EntitySharePojo.EntitySharePojoHelper> helperMock = mockStatic(EntitySharePojo.EntitySharePojoHelper.class)) {
        EntitySharePojo.EntitySharePojoHelper expectedHelper = mock(EntitySharePojo.EntitySharePojoHelper.class);
        helperMock.when(() -> EntitySharePojo.EntitySharePojoHelper.of(any(Table.class), eq(describeMap))).thenReturn(expectedHelper);
        
        // 执行被测试方法
        EntitySharePojo.EntitySharePojoHelper result = dataSharingProcessor.processorByDataSharing(dataSharing, testUser);
        
        // 验证结果
        assertEquals(expectedHelper, result);
        verify(orgService).getUserNameMapByIds(eq(testUser.getTenantId()), eq(testUser.getUserId()), anyList());
        verify(orgService, times(2)).getDeptInfoNameByIds(eq(testUser.getTenantId()), eq(testUser.getUserId()), anyList());
        verify(userRoleInfoService).queryRoleNameByRoleCode(eq(testUser), anyList());
        verify(orgService).getGroupNameByIds(eq(testUser.getTenantId()), eq(testUser.getUserId()), anyList());
        verify(objectDescribeService).findDescribeListByApiNames(eq(testUser.getTenantId()), anyList(), any());
      }
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试processorByEntitySharePojo方法，正常处理实体共享对象
   */
  @Test
  @DisplayName("正常场景 - 测试processorByEntitySharePojo方法")
  void testProcessorByEntitySharePojo_NormalCase() throws MetadataServiceException {
    try (MockedStatic<Lists> listsMock = mockStatic(Lists.class);
         MockedStatic<Sets> setsMock = mockStatic(Sets.class);
         MockedStatic<ActionContextExt> actionContextExtMock = mockStatic(ActionContextExt.class)) {
      
      // 准备测试数据
      List<EntitySharePojo> entitySharePojoList = createTestEntitySharePojoList();
      Table<Integer, String, String> mockTable = HashBasedTable.create();
      
      Map<String, String> userNameMap = new HashMap<>();
      userNameMap.put("user1", "用户1");
      
      List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = Lists.newArrayList();
      QueryDeptInfoByDeptIds.DeptInfo deptInfo = new QueryDeptInfoByDeptIds.DeptInfo();
      deptInfo.setDeptId("dept1");
      deptInfo.setDeptName("部门1");
      deptInfos.add(deptInfo);
      
      Map<String, String> roleMap = new HashMap<>();
      roleMap.put("role1", "角色1");
      
      Map<String, String> groupNameMap = new HashMap<>();
      groupNameMap.put("group1", "用户组1");
      
      List<IObjectDescribe> describeList = Lists.newArrayList();
      IObjectDescribe describe = mock(IObjectDescribe.class);
      when(describe.getApiName()).thenReturn("TestObj");
      describeList.add(describe);
      
      Map<String, IObjectDescribe> describeMap = new HashMap<>();
      describeMap.put("TestObj", describe);
      
      ActionContextExt actionContextExt = mock(ActionContextExt.class);
      IActionContext mockContext = mock(IActionContext.class);
      when(actionContextExt.getContext()).thenReturn(mockContext);
      
      // 配置Mock行为
      listsMock.when(() -> Lists.newArrayList(any(Set.class))).thenReturn(new ArrayList<>());
      setsMock.when(Sets::newHashSet).thenReturn(new HashSet<>());
      actionContextExtMock.when(() -> ActionContextExt.of(testUser)).thenReturn(actionContextExt);
      
      when(orgService.getUserNameMapByIds(eq(testUser.getTenantId()), eq(testUser.getUserId()), anyList())).thenReturn(userNameMap);
      when(orgService.getDeptInfoNameByIds(eq(testUser.getTenantId()), eq(testUser.getUserId()), anyList())).thenReturn(deptInfos);
      when(userRoleInfoService.queryRoleNameByRoleCode(eq(testUser), anyList())).thenReturn(roleMap);
      when(orgService.getGroupNameByIds(eq(testUser.getTenantId()), eq(testUser.getUserId()), anyList())).thenReturn(groupNameMap);
      when(objectDescribeService.findDescribeListByApiNames(eq(testUser.getTenantId()), anyList(), any())).thenReturn(describeList);
      
      // Mock EntitySharePojo.EntitySharePojoHelper.of方法
      try (MockedStatic<EntitySharePojo.EntitySharePojoHelper> helperMock = mockStatic(EntitySharePojo.EntitySharePojoHelper.class)) {
        EntitySharePojo.EntitySharePojoHelper expectedHelper = mock(EntitySharePojo.EntitySharePojoHelper.class);
        helperMock.when(() -> EntitySharePojo.EntitySharePojoHelper.of(any(Table.class), eq(describeMap))).thenReturn(expectedHelper);
        
        // 执行被测试方法
        EntitySharePojo.EntitySharePojoHelper result = dataSharingProcessor.processorByEntitySharePojo(entitySharePojoList, testUser);
        
        // 验证结果
        assertEquals(expectedHelper, result);
        verify(orgService).getUserNameMapByIds(eq(testUser.getTenantId()), eq(testUser.getUserId()), anyList());
        verify(orgService).getDeptInfoNameByIds(eq(testUser.getTenantId()), eq(testUser.getUserId()), anyList());
        verify(userRoleInfoService).queryRoleNameByRoleCode(eq(testUser), anyList());
        verify(orgService).getGroupNameByIds(eq(testUser.getTenantId()), eq(testUser.getUserId()), anyList());
        verify(objectDescribeService).findDescribeListByApiNames(eq(testUser.getTenantId()), anyList(), any());
      }
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试findObjects方法，正常查找对象描述
   */
  @Test
  @DisplayName("正常场景 - 测试findObjects方法")
  void testFindObjects_NormalCase() throws MetadataServiceException {
    try (MockedStatic<ActionContextExt> actionContextExtMock = mockStatic(ActionContextExt.class)) {
      
      // 准备测试数据
      List<String> describeApiNameList = Lists.newArrayList("TestObj1", "TestObj2");
      
      List<IObjectDescribe> describeList = Lists.newArrayList();
      IObjectDescribe describe1 = mock(IObjectDescribe.class);
      when(describe1.getApiName()).thenReturn("TestObj1");
      IObjectDescribe describe2 = mock(IObjectDescribe.class);
      when(describe2.getApiName()).thenReturn("TestObj2");
      describeList.add(describe1);
      describeList.add(describe2);
      
      ActionContextExt actionContextExt = mock(ActionContextExt.class);
      IActionContext mockContext = mock(IActionContext.class);
      when(actionContextExt.getContext()).thenReturn(mockContext);
      
      // 配置Mock行为
      actionContextExtMock.when(() -> ActionContextExt.of(testUser)).thenReturn(actionContextExt);
      when(objectDescribeService.findDescribeListByApiNames(eq(testUser.getTenantId()), eq(describeApiNameList), any())).thenReturn(describeList);
      
      // 使用反射调用私有方法
      try {
        java.lang.reflect.Method method = DataSharingProcessor.class.getDeclaredMethod("findObjects", List.class, User.class);
        method.setAccessible(true);
        
        // 执行被测试方法
        @SuppressWarnings("unchecked")
        Map<String, IObjectDescribe> result = (Map<String, IObjectDescribe>) method.invoke(dataSharingProcessor, describeApiNameList, testUser);
        
        // 验证结果
        assertEquals(2, result.size());
        assertEquals(describe1, result.get("TestObj1"));
        assertEquals(describe2, result.get("TestObj2"));
        verify(objectDescribeService).findDescribeListByApiNames(eq(testUser.getTenantId()), eq(describeApiNameList), any());
      } catch (Exception e) {
        fail("反射调用失败: " + e.getMessage());
      }
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试findObjects方法，抛出MetadataServiceException异常
   */
  @Test
  @DisplayName("异常场景 - 测试findObjects方法抛出MetadataServiceException")
  void testFindObjectsThrowsMetaDataBusinessException_MetadataServiceException() throws MetadataServiceException {
    try (MockedStatic<ActionContextExt> actionContextExtMock = mockStatic(ActionContextExt.class)) {
      
      // 准备测试数据
      List<String> describeApiNameList = Lists.newArrayList("TestObj1");
      
      ActionContextExt actionContextExt = mock(ActionContextExt.class);
      IActionContext mockContext = mock(IActionContext.class);
      when(actionContextExt.getContext()).thenReturn(mockContext);

      MetadataServiceException metadataException = new MetadataServiceException(ErrorCode.SYSTEM_ERROR, "测试异常");
      
      // 配置Mock行为
      actionContextExtMock.when(() -> ActionContextExt.of(testUser)).thenReturn(actionContextExt);
      when(objectDescribeService.findDescribeListByApiNames(eq(testUser.getTenantId()), eq(describeApiNameList), any())).thenThrow(metadataException);
      
      // 使用反射调用私有方法并验证异常
      try {
        java.lang.reflect.Method method = DataSharingProcessor.class.getDeclaredMethod("findObjects", List.class, User.class);
        method.setAccessible(true);
        
        // 执行并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
          method.invoke(dataSharingProcessor, describeApiNameList, testUser);
        });
        
        // 验证异常被正确抛出 - 反射调用会包装异常
        assertNotNull(exception);
        // 验证objectDescribeService被调用
        verify(objectDescribeService).findDescribeListByApiNames(eq(testUser.getTenantId()), eq(describeApiNameList), any());
      } catch (Exception e) {
        fail("反射调用失败: " + e.getMessage());
      }
    }
  }

  // 辅助方法：创建测试用的DataSharing对象
  private DataSharing createTestDataSharing() {
    RangesExt sourceRanges = mock(RangesExt.class);
    when(sourceRanges.getUser()).thenReturn(Lists.newArrayList("user1"));
    when(sourceRanges.getDepartment()).thenReturn(Lists.newArrayList("dept1"));
    when(sourceRanges.getRole()).thenReturn(Lists.newArrayList("role1"));
    when(sourceRanges.getUserGroup()).thenReturn(Lists.newArrayList("group1"));
    when(sourceRanges.getShareRuleOrgIds()).thenReturn(Lists.newArrayList("org1"));

    RangesExt targetRanges = mock(RangesExt.class);
    when(targetRanges.getUser()).thenReturn(Lists.newArrayList("user2"));
    when(targetRanges.getDepartment()).thenReturn(Lists.newArrayList("dept2"));
    when(targetRanges.getRole()).thenReturn(Lists.newArrayList("role2"));
    when(targetRanges.getUserGroup()).thenReturn(Lists.newArrayList("group2"));
    when(targetRanges.getShareRuleOrgIds()).thenReturn(Lists.newArrayList("org2"));

    DataSharing dataSharing = mock(DataSharing.class);
    when(dataSharing.getSourceRanges()).thenReturn(sourceRanges);
    when(dataSharing.getTargetRanges()).thenReturn(targetRanges);
    when(dataSharing.getDescribeApiNameList()).thenReturn(Lists.newArrayList("TestObj"));

    return dataSharing;
  }

  // 辅助方法：创建测试用的EntitySharePojo列表
  private List<EntitySharePojo> createTestEntitySharePojoList() {
    List<EntitySharePojo> list = Lists.newArrayList();

    EntitySharePojo pojo1 = mock(EntitySharePojo.class);
    when(pojo1.getEntityId()).thenReturn("TestObj");
    when(pojo1.getReceiveType()).thenReturn(USER_TYPE);
    when(pojo1.getReceiveId()).thenReturn("user1");
    when(pojo1.getShareType()).thenReturn(ROLE_TYPE);
    when(pojo1.getShareId()).thenReturn("role1");
    list.add(pojo1);

    EntitySharePojo pojo2 = mock(EntitySharePojo.class);
    when(pojo2.getEntityId()).thenReturn("TestObj");
    when(pojo2.getReceiveType()).thenReturn(DEPART_TYPE);
    when(pojo2.getReceiveId()).thenReturn("dept1");
    when(pojo2.getShareType()).thenReturn(USER_GROUP_TYPE);
    when(pojo2.getShareId()).thenReturn("group1");
    list.add(pojo2);

    return list;
  }
}
