package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.domain.DomainPluginManager;
import com.facishare.paas.appframework.core.model.handler.HandlerManager;
import com.facishare.paas.appframework.core.model.plugin.PluginManager;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.handler.HandlerLogicService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class StandardReChangeOrderActionTest {

    @Spy
    private StandardReChangeOrderAction standardReChangeOrderAction;

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private InfraServiceFacade infraServiceFacade;

    @Mock
    private IObjectDescribe objectDescribe;

    @Mock
    private ActionContext actionContext;

    @Mock
    private User user;

    @Mock
    private IObjectData objectData;

    @Mock
    private SpringBeanHolder springBeanHolder;

    @Mock
    private HandlerManager handlerManager;

    @Mock
    private HandlerLogicService handlerLogicService;

    @Mock
    private PluginManager pluginManager;

    @Mock
    private DomainPluginManager domainPluginManager;

    @BeforeEach
    void setUp() {
        // 配置Mock行为 - 确保SpringBeanHolder及其依赖不为null
        lenient().when(infraServiceFacade.getSpringBeanHolder()).thenReturn(springBeanHolder);
        lenient().when(springBeanHolder.getHandlerManager()).thenReturn(handlerManager);
        lenient().when(springBeanHolder.getHandlerLogicService()).thenReturn(handlerLogicService);
        lenient().when(springBeanHolder.getPluginManager()).thenReturn(pluginManager);
        lenient().when(springBeanHolder.getDomainPluginManager()).thenReturn(domainPluginManager);

        // 手动注入依赖
        standardReChangeOrderAction.setServiceFacade(serviceFacade);
        standardReChangeOrderAction.setInfraServiceFacade(infraServiceFacade);

        ReflectionTestUtils.setField(standardReChangeOrderAction, "objectDescribe", objectDescribe);
        ReflectionTestUtils.setField(standardReChangeOrderAction, "actionContext", actionContext);
        ReflectionTestUtils.setField(standardReChangeOrderAction, "dataList", Lists.newArrayList(objectData));

        // 设置默认的arg对象避免空指针异常
        StandardReChangeOrderAction.Arg defaultArg = new StandardReChangeOrderAction.Arg();
        defaultArg.setObjectDataId("testDataId");
        ReflectionTestUtils.setField(standardReChangeOrderAction, "arg", defaultArg);

        lenient().when(actionContext.getUser()).thenReturn(user);
        lenient().when(objectDescribe.getApiName()).thenReturn("testApi");
        lenient().when(objectDescribe.getOriginalDescribeApiName()).thenReturn("originalTestApi");
        lenient().when(objectData.getId()).thenReturn("testDataId");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFuncPrivilegeCodes方法返回正确的权限代码列表
     */
    @Test
    @DisplayName("正常场景 - 获取功能权限代码")
    void testGetFuncPrivilegeCodesSuccess() {
        // 执行被测试方法
        List<String> result = standardReChangeOrderAction.getFuncPrivilegeCodes();

        // 验证结果
        assertNotNull(result);
        assertEquals(StandardAction.ReChangeOrder.getFunPrivilegeCodes(), result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDataPrivilegeIds方法返回正确的数据权限ID列表
     */
    @Test
    @DisplayName("正常场景 - 获取数据权限ID")
    void testGetDataPrivilegeIdsSuccess() {
        // 准备测试数据
        StandardReChangeOrderAction.Arg arg = new StandardReChangeOrderAction.Arg();
        arg.setObjectDataId("testObjectId");

        // 执行被测试方法
        List<String> result = standardReChangeOrderAction.getDataPrivilegeIds(arg);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("testObjectId", result.get(0));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试initDataList方法正常初始化数据列表
     */
    @Test
    @DisplayName("正常场景 - 初始化数据列表")
    void testInitDataListSuccess() {
        // 配置Mock行为
        doNothing().when(standardReChangeOrderAction).callSuperInitDataList();

        // 执行被测试方法
        standardReChangeOrderAction.initDataList();

        // 验证Mock交互
        verify(standardReChangeOrderAction).callSuperInitDataList();

        // 验证objectData被正确设置
        IObjectData actualObjectData = (IObjectData) ReflectionTestUtils.getField(standardReChangeOrderAction, "objectData");
        assertEquals(objectData, actualObjectData);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试before方法正常执行前置检查
     */
    @Test
    @DisplayName("正常场景 - 执行前置检查")
    void testBeforeSuccess() {
        // 准备测试数据
        StandardReChangeOrderAction.Arg arg = new StandardReChangeOrderAction.Arg();
        ReflectionTestUtils.setField(standardReChangeOrderAction, "objectData", objectData);

        // 配置Mock行为
        doNothing().when(standardReChangeOrderAction).callSuperBefore(arg);
        lenient().when(infraServiceFacade.canStartChangeOrder(any(User.class), any(IObjectDescribe.class), any(IObjectData.class), anyBoolean()))
                .thenReturn(true);

        // 执行被测试方法
        standardReChangeOrderAction.before(arg);

        // 验证Mock交互
        verify(standardReChangeOrderAction).callSuperBefore(arg);
        verify(infraServiceFacade).canStartChangeOrder(user, objectDescribe, objectData, true);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试before方法在不能开始变更单时抛出ValidateException异常
     */
    @Test
    @DisplayName("异常场景 - 不能开始变更单")
    void testBeforeThrowsValidateExceptionWhenCannotStartChangeOrder() {
        // 准备测试数据
        StandardReChangeOrderAction.Arg arg = new StandardReChangeOrderAction.Arg();
        ReflectionTestUtils.setField(standardReChangeOrderAction, "objectData", objectData);

        // 配置Mock行为
        doNothing().when(standardReChangeOrderAction).callSuperBefore(arg);
        lenient().when(infraServiceFacade.canStartChangeOrder(any(User.class), any(IObjectDescribe.class), any(IObjectData.class), anyBoolean()))
                .thenReturn(false);

        try (MockedStatic<I18NExt> i18nExtMock = mockStatic(I18NExt.class)) {
            i18nExtMock.when(() -> I18NExt.text(I18NKey.CAN_NOT_START_CHANGE_ORDER))
                    .thenReturn("不能开始变更单");

            // 执行并验证异常
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                standardReChangeOrderAction.before(arg);
            });

            // 验证异常信息
            assertEquals("不能开始变更单", exception.getMessage());
        }

        // 验证Mock交互
        verify(standardReChangeOrderAction).callSuperBefore(arg);
        verify(infraServiceFacade).canStartChangeOrder(user, objectDescribe, objectData, true);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doAct方法正常执行操作并返回结果
     */
    @Test
    @DisplayName("正常场景 - 执行操作")
    void testDoActSuccess() {
        // 准备测试数据
        StandardReChangeOrderAction.Arg arg = new StandardReChangeOrderAction.Arg();
        ReflectionTestUtils.setField(standardReChangeOrderAction, "objectData", objectData);

        ObjectDataExt objectDataExt = mock(ObjectDataExt.class);
        when(objectDataExt.getOriginalDataId()).thenReturn("originalDataId");
        when(objectDataExt.getChangeOrderRule()).thenReturn("changeOrderRule");
        when(objectDataExt.getId()).thenReturn("changeOrderId");

        // 配置Mock行为
        try (MockedStatic<ObjectDataExt> objectDataExtMock = mockStatic(ObjectDataExt.class);
             MockedStatic<RequestContextManager> requestContextManagerMock = mockStatic(RequestContextManager.class)) {

            objectDataExtMock.when(() -> ObjectDataExt.of(objectData)).thenReturn(objectDataExt);
            requestContextManagerMock.when(RequestContextManager::getContext).thenReturn(mock(com.facishare.paas.appframework.core.model.RequestContext.class));

            // 执行被测试方法
            StandardReChangeOrderAction.Result result = standardReChangeOrderAction.doAct(arg);

            // 验证结果
            assertNotNull(result);
            assertEquals("originalTestApi", result.getOriginalApiName());
            assertEquals("originalDataId", result.getOriginalDataId());
        }

        // 验证Mock交互
        verify(serviceFacade).triggerAction(any(ActionContext.class), any(StandardChangeAction.Arg.class), eq(StandardChangeAction.Result.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试callSuperInitDataList辅助方法
     */
    @Test
    @DisplayName("正常场景 - 调用父类initDataList方法")
    void testCallSuperInitDataListSuccess() {
        // 准备测试数据
        StandardReChangeOrderAction.Arg arg = new StandardReChangeOrderAction.Arg();
        arg.setObjectDataId("objectDataId");

        // 创建真实对象来测试辅助方法
        StandardReChangeOrderAction realAction = new StandardReChangeOrderAction();

        realAction.setArg(arg);

        // 执行被测试方法
        assertDoesNotThrow(() -> realAction.callSuperInitDataList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试callSuperBefore辅助方法
     */
    @Test
    @DisplayName("正常场景 - 调用父类before方法")
    void testCallSuperBeforeSuccess() {
        // 准备测试数据
        StandardReChangeOrderAction.Arg arg = new StandardReChangeOrderAction.Arg();

        // 创建真实对象来测试辅助方法
        StandardReChangeOrderAction realAction = new StandardReChangeOrderAction();
        realAction.setActionContext(actionContext);

        // 执行被测试方法
        assertDoesNotThrow(() -> realAction.callSuperBefore(arg));
    }
} 