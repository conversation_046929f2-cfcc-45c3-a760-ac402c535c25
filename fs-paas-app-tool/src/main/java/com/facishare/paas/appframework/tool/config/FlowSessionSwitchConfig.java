package com.facishare.paas.appframework.tool.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.fxiaoke.functions.utils.Maps;
import com.github.autoconf.ConfigFactory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;

@Slf4j
public class FlowSessionSwitchConfig {

    private final static String FS_FLOW_SESSION_SWITCH = "fs-flow-session-switch";

    private static Map<Integer, Cloud> cloudMap = Maps.newHashMap();


    static {
        ConfigFactory.getConfig(FS_FLOW_SESSION_SWITCH, configStr -> {
            JSONObject configJsonObj = JSON.parseObject(configStr.getString());
            log.warn("reload config fs-flow-session-switch,content:{}", configStr.getString());
            cloudMap = parseCloudFromConfig(configJsonObj, "clouds");
        });
    }

    private static Map<Integer, Cloud> parseCloudFromConfig(JSONObject configJsonObject, String key) {
        try {
            JSONObject cloudJSONMap = configJsonObject.getJSONObject(key);
            if (CollectionUtils.empty(cloudJSONMap)) {
                return Maps.newHashMap();
            }
            Map<Integer, Cloud> cloudMap = Maps.newHashMap();
            cloudJSONMap.forEach((configKey, value) -> {
                if (Objects.isNull(value)){
                    return;
                }
                Cloud cloud = JSON.parseObject(String.valueOf(value),Cloud.class);
                if (Objects.nonNull(cloud)) {
                    cloudMap.put(Integer.valueOf(configKey), cloud);
                }
            });
            log.info("parseCloudFromConfig result! key:{},cloudMap:{}", key, cloudMap);
            return cloudMap;
        } catch (Exception e) {
            log.error("parseCloudFromConfig error! key:{}", key, e);
            return Maps.newHashMap();
        }
    }


    public static Cloud getCloudByKey(Integer key) {
        return cloudMap.get(key);
    }


    @Data
    public static class Cloud {
        String name;
        String url;
    }

}
