package com.facishare.paas.appframework.metadata.relation;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.QueryJob.JobInfo;
import com.facishare.paas.appframework.common.service.dto.QueryJob.ResultInfo;
import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.expression.*;
import com.facishare.paas.appframework.metadata.options.OptionSetLogicService;
import com.facishare.paas.appframework.metadata.relation.CalculateRelation.RelateField;
import com.facishare.paas.appframework.metadata.relation.FieldRelationValidator.ValidateField;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe;
import com.facishare.paas.metadata.service.impl.UdefFunctionService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.google.common.collect.*;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.appframework.common.service.dto.QueryJob.FORMULA_JOB_TYPE;

/**
 * Created by zhouwr on 2018/5/23
 */
@Slf4j
@Service("fieldRelationCalculateService")
public class FieldRelationCalculateServiceImpl implements FieldRelationCalculateService {

    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private FieldRelationGraphService fieldRelationGraphService;
    @Autowired
    private JobScheduleService jobScheduleService;
    @Autowired
    private ReferenceLogicService referenceLogicService;
    @Autowired
    private UdefFunctionService udefFunctionService;
    @Autowired
    private LogService logService;
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private MetaDataComputeServiceImpl computeService;
    @Autowired
    private CustomButtonServiceImpl buttonService;
    @Autowired
    private PostActionServiceImpl actionService;
    @Autowired
    private FunctionLogicService functionLogicService;
    @Autowired
    private OptionSetLogicService optionSetLogicService;
    @Autowired
    private ExpressionCalculateLogicService expressionCalculateLogicService;

    private static final Set<String> NOT_REPOSITORY_QUOTE_TYPE = ImmutableSet.of(IFieldType.OBJECT_REFERENCE, IFieldType.OBJECT_REFERENCE_MANY);

    @Override
    public CalculateFields getCalculateFieldsForBatchCreate(IObjectDescribe describe, boolean excludeDefaultValue) {
        FieldRelationGraph graph = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(describe, null,
                excludeDefaultValue, false, true, true);
        Set<FieldNode> calculateNodes = Sets.newHashSet();

        //计算公式没有使用其他字段的默认值和计算字段
        List<IFieldDescribe> calculateFields = excludeDefaultValue ? ObjectDescribeExt.of(describe).getCalculateFieldsExcludeDefaultValue() :
                ObjectDescribeExt.of(describe).getCalculateFields();
        calculateFields.forEach(field -> {
            graph.getNode(describe.getApiName(), field.getApiName()).ifPresent(node -> {
                if (CollectionUtils.empty(graph.predecessors(node))) {
                    calculateNodes.add(node);
                }
            });
        });

        //计算链路中没有lookup对象统计字段的默认值、计算字段
        Set<String> objectApiNames = Sets.newHashSet(describe.getApiName());
        ObjectDescribeExt.of(describe).getActiveFieldDescribes().forEach(field -> {
            graph.getNode(describe.getApiName(), field.getApiName()).ifPresent(node -> {
                Set<FieldNode> relateNodes = graph.getRelateNodes(node, objectApiNames, describe.getTenantId());
                calculateNodes.addAll(relateNodes);
            });
        });

        //过滤依赖链路上包含lookup对象统计字段的默认值、计算字段
        Set<FieldNode> toRemoveNodes = Sets.newHashSet();
        calculateNodes.stream().filter(FieldNode::isCountField).forEach(x -> {
            toRemoveNodes.addAll(graph.reachableNodes(x));
            toRemoveNodes.add(x);
        });
        calculateNodes.removeAll(toRemoveNodes);

        Map<String, List<RelateField>> calculateFieldMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(calculateNodes)) {
            calculateFieldMap.put(describe.getApiName(), calculateNodes.stream().map(FieldNode::toRelateField).collect(Collectors.toList()));
        }
        return CalculateFields.of(graph, calculateFieldMap);
    }

    @Override
    public CalculateFields getCalculateFieldsByChanges(IObjectDescribe masterDescribe, List<IObjectDescribe> detailDescribes,
                                                       Map<String, Object> masterChange, Map<String, Map<String, Object>> detailChangeMap) {
        List<IObjectDescribe> describeList = Lists.newArrayList(masterDescribe);
        describeList.addAll(CollectionUtils.nullToEmpty(detailDescribes));
        Map<String, IObjectDescribe> describeMap = describeList.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, x -> x));
        Set<String> describeApiNames = describeList.stream().map(IObjectDescribe::getApiName).collect(Collectors.toSet());

        FieldRelationGraph graph = fieldRelationGraphService.buildReverseDependencyGraph(describeList, true,
                true, true);
        Map<String, Set<RelateField>> relateFieldMap = Maps.newHashMap();
        getRelateFields(masterDescribe.getTenantId(), relateFieldMap, graph, masterDescribe.getApiName(),
                CollectionUtils.nullToEmpty(masterChange).keySet(), Sets.newHashSet(describeApiNames));

        CollectionUtils.nullToEmpty(detailChangeMap).forEach((objectApiName, detailChange) -> {
            if (!describeApiNames.contains(objectApiName)) {
                return;
            }
            Set<ObjectAction> actions = detailChange.keySet().stream().map(ObjectAction::of).collect(Collectors.toSet());
            if (actions.contains(ObjectAction.CREATE) || actions.contains(ObjectAction.INVALID) || actions.contains(ObjectAction.DELETE)) {
                Set<String> changedFields = ObjectDescribeExt.of(describeMap.get(objectApiName)).getActiveFieldDescribes()
                        .stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet());
                getRelateFields(masterDescribe.getTenantId(), relateFieldMap, graph, objectApiName, changedFields, describeApiNames);
            } else if (actions.contains(ObjectAction.UPDATE)) {
                Map<String, Map<String, Object>> updates = (Map<String, Map<String, Object>>) detailChange.get(ObjectAction.UPDATE.getActionCode());
                Set<String> changedFields = Sets.newHashSet();
                updates.forEach((dataId, changeMap) -> changedFields.addAll(changeMap.keySet()));
                getRelateFields(masterDescribe.getTenantId(), relateFieldMap, graph, objectApiName, changedFields, describeApiNames);
            }
        });

        return CalculateFields.ofSet(graph, relateFieldMap);
    }

    private void getRelateFields(String tenantId, Map<String, Set<RelateField>> relateFieldMap, FieldRelationGraph graph,
                                 String changedObject, Set<String> changedFields, Set<String> calculateObjects) {
        changedFields.forEach(field -> {
            graph.getNode(changedObject, field).ifPresent(node -> {
                Set<FieldNode> nodeSet = graph.getRelateNodes(node, calculateObjects, tenantId);
                nodeSet.forEach(relateNode -> {
                    relateFieldMap.putIfAbsent(relateNode.getObjectApiName(), Sets.newHashSet());
                    relateFieldMap.get(relateNode.getObjectApiName()).add(relateNode.toRelateField());
                });
            });
        });
    }

    @Override
    public FieldRelation computeCalculateRelation(@NonNull IObjectDescribe masterDescribe, List<IObjectDescribe> detailDescribes) {
        return computeCalculateRelationWithExt(masterDescribe, detailDescribes, null);
    }

    @Override
    public FieldRelation computeCalculateRelationWithExt(@NonNull IObjectDescribe masterDescribe, List<IObjectDescribe> detailDescribes,
                                                         Map<String, IObjectDescribe> describeExtMap) {
        FieldRelation fieldRelation = new FieldRelation();
        Map<String, IObjectDescribe> describeMap = Maps.newLinkedHashMap();
        describeMap.put(masterDescribe.getApiName(), masterDescribe);
        CollectionUtils.nullToEmpty(detailDescribes).forEach(x -> describeMap.put(x.getApiName(), x));

        //获取字段依赖关系图
        FieldRelationGraph graph = buildFieldRelationGraph(masterDescribe, detailDescribes);

        //处理主从所有字段的依赖关系
        processAllFieldRelation(fieldRelation, graph, describeMap, describeExtMap);
        //处理主从对象统计字段的依赖关系
        processMasterDetailCountFieldRelation(graph, detailDescribes, describeMap, describeExtMap);
        //处理lookup对象的统计字段的依赖关系
        processLookupCountFieldRelation(fieldRelation, graph, describeMap, describeExtMap);
        //处理从对象describe级别的计算关系
        processDescribeCalculateRelation(graph, detailDescribes, describeMap, describeExtMap);

        //设置字段级别的calculate_relation
        describeMap.forEach((objectApiName, describe) -> {
            fieldRelation.getCalculateRelation(objectApiName).forEach((fieldName, calculateRelation) -> {
                if (calculateRelation.hasCalculateFields() && describe.containsField(fieldName)) {
                    if (describeExtMap == null) {
                        ObjectDescribeExt.of(describe).setFieldCalculateRelation(fieldName, calculateRelation);
                    } else {
                        IFieldDescribe field = ObjectDescribeExt.of(describe).copyField4Ext(describeExtMap, describe.getFieldDescribe(fieldName));
                        FieldDescribeExt.of(field).setCalculateRelation(calculateRelation);
                    }
                }
            });
        });

        fieldRelation.setGraph(graph);

        return fieldRelation;
    }

    private void processDescribeCalculateRelation(FieldRelationGraph graph, List<IObjectDescribe> detailDescribes,
                                                  Map<String, IObjectDescribe> describeMap, Map<String, IObjectDescribe> describeExtMap) {
        if (CollectionUtils.empty(detailDescribes)) {
            return;
        }
        String tenantId = detailDescribes.get(0).getTenantId();
        boolean isGrayMasterLookupCalculateFix = UdobjGrayConfig.isAllow(UdobjGrayConfigKey.MASTER_LOOKUP_COUNT_CALCULATE_WHEN_DELETE_DETAIL_FIX_GRAY, tenantId);
        detailDescribes.forEach(describe -> {
            Map<String, Set<RelateField>> relateFields;
            if (describeExtMap == null) {
                relateFields = ObjectDescribeExt.of(describe).getRelateFields();
            } else {
                relateFields = ObjectDescribeExt.of(describeExtMap.getOrDefault(describe.getApiName(), new ObjectDescribe())).getRelateFields();
            }
            if (CollectionUtils.empty(relateFields)) {
                return;
            }
            //找出从对象describe级别计算关系中字段影响到的计算字段、统计字段、默认值、引用字段，将其放入该对象的describe级别的计算关系中
            Maps.newHashMap(relateFields).forEach((apiName, fields) -> Sets.newHashSet(fields).forEach(field -> {
                Optional<FieldNode> fieldNode = graph.getNode(apiName, field.getFieldName());
                if (!fieldNode.isPresent()) {
                    log.warn("node not exit, describeApiName:{}, targetApiName:{}, fieldName:{}", describe.getApiName(), apiName, field.getFieldName());
                    return;
                }
                //灰度企业处理lookup对象的统计和计算字段，否则只处理页面对象的字段
                if (isGrayMasterLookupCalculateFix) {
                    graph.getRelateNodes(fieldNode.get(), describeMap.keySet(), tenantId).stream()
                            .forEach(node -> {
                                if (describeExtMap == null) {
                                    ObjectDescribeExt.of(describe).addRelateField(node.getObjectApiName(), node.toRelateField());
                                } else {
                                    ObjectDescribeExt.of(ObjectDescribeExt.of(describe).copy4Ext(describeExtMap))
                                            .addRelateField(node.getObjectApiName(), node.toRelateField());
                                }
                            });
                } else {
                    graph.reachableNodes(fieldNode.get()).stream()
                            .filter(node -> describeMap.containsKey(node.getObjectApiName()))
                            .filter(node -> node.isCalculateField() || node.isQuoteField())
                            .forEach(node -> {
                                if (describeExtMap == null) {
                                    ObjectDescribeExt.of(describe).addRelateField(node.getObjectApiName(), node.toRelateField());
                                } else {
                                    ObjectDescribeExt.of(ObjectDescribeExt.of(describe).copy4Ext(describeExtMap))
                                            .addRelateField(node.getObjectApiName(), node.toRelateField());
                                }
                            });
                }
            }));
        });
    }

    private FieldRelationGraph buildFieldRelationGraph(IObjectDescribe masterDescribe, List<IObjectDescribe> detailDescribes) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FIELD_RELATION_GRAPH_GRAY, masterDescribe.getTenantId())) {
            return fieldRelationGraphService.buildGraphWithOnlyLookupObjects(masterDescribe, detailDescribes, false, true, false, true);
        }
        return fieldRelationGraphService.buildGraphWithOnlyLookupObjects(masterDescribe, detailDescribes);
    }

    private void processAllFieldRelation(FieldRelation fieldRelation, FieldRelationGraph graph, Map<String, IObjectDescribe> describeMap,
                                         Map<String, IObjectDescribe> describeExtMap) {
        describeMap.values().stream().map(x -> ObjectDescribeExt.of(x)).forEach(describe ->
                describe.getActiveFieldDescribes().stream().map(field -> FieldDescribeExt.of(field)).forEach(field -> {
                    graph.getNode(describe.getApiName(), field.getApiName()).ifPresent(node -> {
                        //递归解析每个字段影响到的字段
                        String tenantId = describe.getTenantId();
                        Set<FieldNode> nodeEdgePairs = graph.getRelateNodes(node, describeMap.keySet(), tenantId);
                        fieldRelation.addRelation(nodeEdgePairs, node);

                        //公式里只有常量或全局变量的需要放入describe级别的计算列表
                        //多币种相关的字段需要放入describe级别的计算列表
                        if ((field.isCalculateField() && CollectionUtils.empty(graph.predecessors(node)))
                                || field.isMultiCurrencyCalculateFields()) {
                            if (describeExtMap == null) {
                                describe.addRelateField(node.getObjectApiName(), node.toRelateField());
                            } else {
                                IObjectDescribe cpDescribe = describe.copy4Ext(describeExtMap);
                                ObjectDescribeExt.of(cpDescribe).addRelateField(node.getObjectApiName(), node.toRelateField());
                            }
                        }
                    });
                }));
    }

    private void processMasterDetailCountFieldRelation(FieldRelationGraph graph,
                                                       List<IObjectDescribe> detailDescribes,
                                                       Map<String, IObjectDescribe> describeMap,
                                                       Map<String, IObjectDescribe> describeExtMap) {
        if (CollectionUtils.empty(detailDescribes)) {
            return;
        }
        //将统计字段和依赖统计字段的计算字段加入从对象describe级别的计算列表
        describeMap.values().forEach(describe -> ObjectDescribeExt.of(describe).getCountFields().stream()
                .filter(count -> describeMap.containsKey(count.getSubObjectDescribeApiName()))
                .forEach(count -> {
                    FieldNode node = graph.getNode(describe.getApiName(), count.getApiName()).get();
                    IObjectDescribe subDescribe = describeMap.get(count.getSubObjectDescribeApiName());
                    ObjectDescribeExt subDescribeExt;
                    if (describeExtMap == null) {
                        subDescribeExt = ObjectDescribeExt.of(subDescribe);
                    } else {
                        subDescribeExt = ObjectDescribeExt.of(ObjectDescribeExt.of(subDescribe).copy4Ext(describeExtMap));
                    }
                    subDescribeExt.addRelateField(node.getObjectApiName(), node.toRelateField());
                    graph.reachableNodes(node).stream()
                            .filter(relateNode -> describeMap.containsKey(relateNode.getObjectApiName()))
                            .forEach(relateNode -> subDescribeExt.addRelateField(relateNode.getObjectApiName(), relateNode.toRelateField()));
                }));

        //从对象的统计字段以及相关的计算字段加入从对象的describe级别的计算列表
        detailDescribes.forEach(detailDescribe -> {
            ObjectDescribeExt detailDescribeExt;
            if (describeExtMap == null) {
                detailDescribeExt = ObjectDescribeExt.of(detailDescribe);
            } else {
                detailDescribeExt = ObjectDescribeExt.of(ObjectDescribeExt.of(detailDescribe).copy4Ext(describeExtMap));
            }
            detailDescribeExt.getCountFields().forEach(count ->
                    graph.getNode(detailDescribe.getApiName(), count.getApiName()).ifPresent(node -> {
                        detailDescribeExt.addRelateField(node.getObjectApiName(), node.toRelateField());
                        graph.reachableNodes(node).stream()
                                .filter(relateNode -> describeMap.containsKey(relateNode.getObjectApiName()))
                                .forEach(relateNode -> detailDescribeExt.addRelateField(relateNode.getObjectApiName(), relateNode.toRelateField()));
                    }));
        });
    }

    private void processLookupCountFieldRelation(FieldRelation fieldRelation,
                                                 FieldRelationGraph graph,
                                                 Map<String, IObjectDescribe> pageDescribes,
                                                 Map<String, IObjectDescribe> describeExtMap) {
        Map<String, IObjectDescribe> describeMap = graph.getDescribeMap();
        Set<FieldNode> fieldNodes = graph.nodes().stream()
                .filter(x -> pageDescribes.containsKey(x.getObjectApiName()))
                .filter(x -> x.isCalculateField() || x.isQuoteField())
//                .filter(x -> !FieldDescribeExt.isMultiCurrencyFields(x.getFieldApiName()))
                .collect(Collectors.toSet());
        fieldNodes.forEach(x -> {
            Set<NodeEdgePair> nodeEdgePairs = graph.getDependentCalculateNodeEdgePairsWithinLookup(x, pageDescribes.keySet());
            if (CollectionUtils.empty(nodeEdgePairs)) {
                return;
            }

            //将lookup对象的统计字段、计算字段加入页面对象的计算字段依赖的普通字段的计算列表
            List<FieldNode> lookupDependentCalculateNodes = nodeEdgePairs.stream().filter(it -> !isPageNode(pageDescribes, it))
                    .map(NodeEdgePair::getFieldNode).collect(Collectors.toList());
            graph.transpose().reachableNodes(x).stream()
                    .filter(y -> pageDescribes.containsKey(y.getObjectApiName()))
                    .filter(y -> !y.isFormula() && !y.isCountField())
                    .forEach(y -> fieldRelation.addRelation(lookupDependentCalculateNodes, y));

            //将该计算字段以及lookup对象的统计字段、计算字段加入页面对象的计算列表
            nodeEdgePairs.stream()
                    .filter(NodeEdgePair::isCountField)
                    .forEach(countNode -> {
                        Count count = (Count) describeMap.get(countNode.getObjectApiName()).getFieldDescribe(countNode.getFieldApiName());
                        IObjectDescribe subDescribe = pageDescribes.get(count.getSubObjectDescribeApiName());
                        ObjectDescribeExt subDescribeExt;
                        if (describeExtMap == null) {
                            subDescribeExt = ObjectDescribeExt.of(subDescribe);
                        } else {
                            subDescribeExt = ObjectDescribeExt.of(ObjectDescribeExt.of(subDescribe).copy4Ext(describeExtMap));
                        }
                        nodeEdgePairs.forEach(y -> subDescribeExt.addRelateField(y.getObjectApiName(), y.toRelateField()));
                        subDescribeExt.addRelateField(x.getObjectApiName(), x.toRelateField());
                    });

        });
    }

    private boolean isPageNode(Map<String, IObjectDescribe> pageDescribes, NodeEdgePair nodeEdgePair) {
        return pageDescribes.containsKey(nodeEdgePair.getObjectApiName())
                && nodeEdgePair.getRelateEdgeNodes().stream().anyMatch(n -> FieldRelationGraph.RELATE_TYPES.contains(n.getRelateType()));
    }

    @Override
    public CalculateFields computeCalculateFields(List<String> calcFieldNames, IObjectDescribe describe) {
        if (CollectionUtils.empty(calcFieldNames)) {
            return CalculateFields.of(null, null);
        }

        Map<String, List<RelateField>> calculateFields = Maps.newHashMap();
        Map<String, Set<RelateField>> calculateFieldSet = Maps.newHashMap();

        Map<String, IObjectDescribe> describeMap = Maps.newLinkedHashMap();
        describeMap.put(describe.getApiName(), describe);

        List<IObjectDescribe> describeListForGraph = Lists.newArrayList(describeMap.values());
        ObjectDescribeExt.of(describe).getMasterDetailFieldDescribe().ifPresent(x -> {
            IObjectDescribe realMasterDescribe = describeLogicService.findObjectWithoutCopyIfGray(describe.getTenantId(), x.getTargetApiName());
            describeListForGraph.add(realMasterDescribe);
        });

        //获取构图
        FieldRelationGraph graph = fieldRelationGraphService.buildReverseDependencyGraph(describeListForGraph, false);

        calcFieldNames.forEach(x -> graph.getNode(describe.getApiName(), x).ifPresent(node -> {
            calculateFieldSet.putIfAbsent(describe.getApiName(), Sets.newHashSet());
            calculateFieldSet.get(describe.getApiName()).add(node.toRelateField());

            //需要计算间接影响到的计算字段和统计字段
            Set<FieldNode> relateNodes = graph.getRelateNodes(node, Sets.newHashSet(describe.getApiName()));
            relateNodes.forEach(rNode -> {
                calculateFieldSet.putIfAbsent(rNode.getObjectApiName(), Sets.newHashSet());
                calculateFieldSet.get(rNode.getObjectApiName()).add(rNode.toRelateField());
            });
        }));

        calculateFieldSet.forEach((k, v) -> calculateFields.put(k, Lists.newArrayList(v)));

        return CalculateFields.of(graph, calculateFields);
    }

    @Override
    public CalculateFields computeCalculateFieldsForAddAction(@NonNull IObjectDescribe masterDescribe,
                                                              List<IObjectDescribe> detailDescribes,
                                                              boolean excludeDefaultValue) {
        return computeCalculateFieldsForAddAction(masterDescribe, detailDescribes, excludeDefaultValue,
                false, false);
    }

    @Override
    public CalculateFields computeCalculateFieldsForAddAction(@NonNull IObjectDescribe masterDescribe,
                                                              List<IObjectDescribe> detailDescribes,
                                                              boolean excludeDefaultValue,
                                                              boolean includeLookupObjects,
                                                              boolean excludeLookupRelateField) {
        Set<FieldNode> calculateNodes = Sets.newHashSet();
        Map<String, Map<String, Set<RelateField>>> relateFieldMap = Maps.newHashMap();

        Map<String, IObjectDescribe> describeMap = Maps.newLinkedHashMap();
        describeMap.put(masterDescribe.getApiName(), masterDescribe);
        CollectionUtils.nullToEmpty(detailDescribes).forEach(x -> describeMap.put(x.getApiName(), x));
        List<IObjectDescribe> describeListForGraph = Lists.newArrayList(describeMap.values());

        //构建字段依赖关系图
        FieldRelationGraph graph;
        if (includeLookupObjects) {
            graph = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(masterDescribe, detailDescribes,
                    excludeDefaultValue, false, true);
        } else {
            ObjectDescribeExt.of(masterDescribe).getMasterDetailFieldDescribe().ifPresent(x -> {
                IObjectDescribe realMasterDescribe = describeLogicService.findObjectWithoutCopyIfGray(masterDescribe.getTenantId(), x.getTargetApiName());
                describeListForGraph.add(realMasterDescribe);
            });
            graph = fieldRelationGraphService.buildReverseDependencyGraph(describeListForGraph, excludeDefaultValue,
                    false, true);
        }

        describeMap.forEach((objectApiName, describe) -> {
            Set<FieldNode> objectRelateNodes = Sets.newHashSet();
            if (includeLookupObjects) {
                //计算公式没有使用其他字段的默认值和计算字段
                List<IFieldDescribe> calculateFields = excludeDefaultValue ? ObjectDescribeExt.of(describe).getCalculateFieldsExcludeDefaultValue() :
                        ObjectDescribeExt.of(describe).getCalculateFields();
                calculateFields.forEach(field -> {
                    graph.getNode(describe.getApiName(), field.getApiName()).ifPresent(node -> {
                        if (CollectionUtils.empty(graph.predecessors(node))) {
                            objectRelateNodes.add(node);
                        }
                    });
                });
            } else {
                //主对象和从对象的所有计算字段都需要计算
                List<IFieldDescribe> tocCalculateFields = excludeDefaultValue ? ObjectDescribeExt.of(describe).getCalculateFieldsExcludeDefaultValue() :
                        ObjectDescribeExt.of(describe).getCalculateFieldsAndMultiCurrencyCalculateFields();
                tocCalculateFields.forEach(formula ->
                        graph.getNode(describe.getApiName(), formula.getApiName()).ifPresent(objectRelateNodes::add));
            }

            //从本对象的字段出发，经过其他对象的统计或计算字段再回到本对象的计算字段，整条链路上的计算和统计字段都需要计算
            ObjectDescribeExt.of(describe).getActiveFieldDescribes().forEach(field -> {
                graph.getNode(describe.getApiName(), field.getApiName()).ifPresent(fieldNode -> {
                    Set<FieldNode> fieldRelateNodes = graph.getRelateNodes(fieldNode, describeMap.keySet(), describe.getTenantId());
                    objectRelateNodes.addAll(fieldRelateNodes);
                });
            });

            //主对象统计从对象的所有统计字段都需要计算
            if (CollectionUtils.notEmpty(detailDescribes) && objectApiName.equals(masterDescribe.getApiName())) {
                detailDescribes.forEach(detailDescribe -> {
                    ObjectDescribeExt.of(describe).getCountFields(detailDescribe.getApiName()).forEach(count -> {
                        graph.getNode(describe.getApiName(), count.getApiName()).ifPresent(objectRelateNodes::add);
                    });
                });
            }

            //主从一起新建的从对象需要重新计算币种
            if (!excludeDefaultValue && ObjectDescribeExt.of(describe).isSlaveObjectCreateWithMaster()) {
                ObjectDescribeExt.of(describe).getCurrencyField().flatMap(currencyField ->
                                graph.getNode(describe.getApiName(), currencyField.getApiName()))
                        .ifPresent(objectRelateNodes::add);
            }

            if (CollectionUtils.notEmpty(objectRelateNodes)) {
                calculateNodes.addAll(objectRelateNodes);
                relateFieldMap.put(objectApiName, FieldNode.toRelateFieldSetMap(objectRelateNodes));
            }
        });

        //过滤依赖链路上包含lookup对象统计字段的默认值、计算字段
        if (excludeLookupRelateField) {
            Set<FieldNode> toRemoveNodes = Sets.newHashSet();
            calculateNodes.stream().filter(FieldNode::isCountField).forEach(x -> {
                if (CollectionUtils.notEmpty(detailDescribes) && Objects.equals(x.getObjectApiName(), masterDescribe.getApiName())) {
                    return;
                }
                toRemoveNodes.addAll(graph.reachableNodes(x));
                toRemoveNodes.add(x);
            });
            calculateNodes.removeAll(toRemoveNodes);
            FieldNode.toRelateFieldSetMap(toRemoveNodes).forEach((relateApiName, fields) -> {
                relateFieldMap.forEach((objectApiName, relateFields) -> {
                    if (relateFields.containsKey(relateApiName)) {
                        relateFields.get(relateApiName).removeAll(fields);
                    }
                });
            });
        }

        return CalculateFields.ofSet(graph, FieldNode.toRelateFieldSetMap(calculateNodes), relateFieldMap);
    }

    @Override
    public CalculateFields computeCalculateFieldsForEditData(EditCalculateParam editCalculateParam) {
        IObjectDescribe masterDescribe = editCalculateParam.getMasterDescribe();
        Map<String, Object> masterModifyData = editCalculateParam.getMasterModifyData();
        IObjectData masterData = editCalculateParam.getMasterData();
        Map<String, List<IObjectData>> detailObjectData = CollectionUtils.nullToEmpty(editCalculateParam.getDetailDataMap());
        Map<String, IObjectDescribe> detailDescribeMap = editCalculateParam.getDetailDescribeMap();
        Set<String> describeApiNames = editCalculateParam.getAllDescribeApiNames();
        List<IObjectDescribe> describeListWithOnlyMasterDetail = Lists.newArrayList(masterDescribe);
        describeListWithOnlyMasterDetail.addAll(detailDescribeMap.values());

        //只有主对象和从对象的字段依赖关系图
        FieldRelationGraph graphWithOnlyMasterDetail = fieldRelationGraphService.buildReverseDependencyGraph(describeListWithOnlyMasterDetail,
                editCalculateParam.isExcludeDefaultValue(), editCalculateParam.isIncludeQuoteField(), true);
        //包含主对象、从对象和各自关联对象的字段依赖关系图
        FieldRelationGraph graph;
        String tenantId = masterDescribe.getTenantId();
        if (Objects.nonNull(editCalculateParam.getGraph())) {
            graph = editCalculateParam.getGraph();
        } else {
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FIELD_RELATION_GRAPH_GRAY, tenantId)) {
                graph = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(masterDescribe, editCalculateParam.getDetailDescribes(),
                        editCalculateParam.isExcludeDefaultValue(), editCalculateParam.isIncludeQuoteField(), true, true);
            } else {
                graph = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(masterDescribe, editCalculateParam.getDetailDescribes(),
                        editCalculateParam.isExcludeDefaultValue(), editCalculateParam.isIncludeQuoteField(), true);
            }
        }

        Set<FieldNode> allCalculateNodes = Sets.newHashSet();
        Set<FieldNode> masterRelateNodes = Sets.newHashSet();
        Set<FieldNode> masterRelateNodesWithOnlyMasterDetail = Sets.newHashSet();
        Map<String, Map<String, Set<RelateField>>> allRelateFieldMap = Maps.newHashMap();

        if (CollectionUtils.notEmpty(masterModifyData)) {
            IObjectData oldMasterObjectData = editCalculateParam.getOldMasterData();
            masterModifyData.keySet().forEach(fieldName -> {
                graph.getNode(masterDescribe.getApiName(), fieldName).ifPresent(node -> {
                    Set<FieldNode> relateNodes = graph.getRelateNodes(node, describeApiNames, tenantId);
                    masterRelateNodes.addAll(relateNodes);

                    //如果是修改了lookup字段触发关联对象的统计字段的计算，需要将修改前的关联对象的数据id保存到被修改的数据中
                    IFieldDescribe field = masterDescribe.getFieldDescribe(fieldName);
                    if (FieldDescribeExt.of(field).isObjectReferenceField()) {
                        String oldLookupId;
                        try {
                            oldLookupId = (String) oldMasterObjectData.get(fieldName);
                        } catch (RuntimeException e) {
                            log.warn("get lookup field value fail! objectApiName:{}, fieldName:{}", masterDescribe.getApiName(), fieldName);
                            throw e;
                        }
                        if (!Strings.isNullOrEmpty(oldLookupId)) {
                            //通过__old字段传递需要计算统计字段的关联对象的老数据id
                            ObjectDataExt.of(masterData).setOldFieldValue(fieldName, oldLookupId);
                        }
                    }
                });
                graphWithOnlyMasterDetail.getNode(masterDescribe.getApiName(), fieldName).ifPresent(node -> {
                    Set<FieldNode> relateNodes = graphWithOnlyMasterDetail.getRelateNodes(node, describeApiNames, tenantId);
                    masterRelateNodesWithOnlyMasterDetail.addAll(relateNodes);
                });
            });
            allCalculateNodes.addAll(masterRelateNodes);
        }
        Map<String, List<CalculateObjectData>> detailCalculateDataMap = Maps.newHashMap();
        Map<String, Map<String, Set<RelateField>>> toRemoveCalculateFieldMap = Maps.newHashMap();
        detailObjectData.forEach((detailApiName, detailDataList) -> {
            IObjectDescribe detailDescribe = detailDescribeMap.get(detailApiName);
            if (Objects.isNull(detailDescribe)) {
                log.warn("describe:{} not exit!", detailApiName);
                return;
            }
            List<IFieldDescribe> defaultValueFields = ObjectDescribeExt.of(detailDescribe).getDefaultValueFields();
            detailCalculateDataMap.put(detailApiName, Lists.newArrayList());
            //初始化从对象的计算数据
            detailDataList.forEach(detailData -> {
                CalculateObjectData detailCalculateData = CalculateObjectData.of(editCalculateParam.getIndexByObjectData(detailData),
                        detailData, Sets.newHashSet(), Maps.newHashMap());
                detailCalculateData.addCalculateFields(FieldNode.toRelateFieldSetMap(masterRelateNodesWithOnlyMasterDetail).get(detailApiName));
                detailCalculateDataMap.get(detailApiName).add(detailCalculateData);
            });

            Set<FieldNode> oneDetailRelateNodes = Sets.newHashSet();
            Map<String, Set<RelateField>> toRemoveCalculateFields = Maps.newHashMap();
            //新建的从
            List<IObjectData> addDataList = CollectionUtils.nullToEmpty(editCalculateParam.getDetailAddDataMap()).get(detailApiName);
            //删除的从
            List<IObjectData> deleteDataList = CollectionUtils.nullToEmpty(editCalculateParam.getDetailDeleteDataMap()).get(detailApiName);
            //编辑的从
            Map<String, IObjectData> updateMap = CollectionUtils.nullToEmpty(editCalculateParam.getDetailModifyDataMap())
                    .getOrDefault(detailApiName, Collections.emptyList())
                    .stream().collect(Collectors.toMap(editCalculateParam::getIndexByObjectData, x -> x));

            if (CollectionUtils.notEmpty(addDataList)) {
                Set<FieldNode> detailRelateNodes = Sets.newHashSet();

                //计算公式没有使用其他字段的默认值和计算字段
                List<IFieldDescribe> calculateFields = editCalculateParam.isExcludeDefaultValue() ?
                        ObjectDescribeExt.of(detailDescribe).getCalculateFieldsExcludeDefaultValue() :
                        ObjectDescribeExt.of(detailDescribe).getCalculateFields();
                calculateFields.forEach(field -> {
                    graph.getNode(detailDescribe.getApiName(), field.getApiName()).ifPresent(node -> {
                        if (CollectionUtils.empty(graph.predecessors(node))) {
                            detailRelateNodes.add(node);
                        }
                    });
                });

                //从本对象的字段出发，经过其他对象的统计或计算字段再回到本对象的计算字段，整条链路上的计算和统计字段都需要计算
                ObjectDescribeExt.of(detailDescribe).getActiveFieldDescribes().forEach(field -> {
                    graph.getNode(detailDescribe.getApiName(), field.getApiName()).ifPresent(fieldNode -> {
                        Set<FieldNode> relateNodes = graph.getRelateNodes(fieldNode, describeApiNames, tenantId);
                        detailRelateNodes.addAll(relateNodes);
                    });
                });

                addDataList.forEach(data -> {
                    //给每条从设置需要计算的字段
                    CalculateObjectData.findByIndex(detailCalculateDataMap.get(detailApiName), editCalculateParam.getIndexByObjectData(data))
                            .ifPresent(detailCalculateData -> {
                                detailCalculateData.addCalculateFields(FieldNode.toRelateFieldSetMap(detailRelateNodes).get(detailApiName));
                                detailCalculateData.addRelateFields(FieldNode.toRelateFieldSetMap(detailRelateNodes));
                            });
                    //找到不需要计算的默认值字段
                    defaultValueFields.forEach(field -> {
                        if (editCalculateParam.unCalculateFields(detailApiName, data, field.getApiName())) {
                            graph.getNode(detailApiName, field.getApiName()).ifPresent(node -> {
                                toRemoveCalculateFields.computeIfAbsent(editCalculateParam.getIndexByObjectData(data), k -> Sets.newHashSet()).add(node.toRelateField());
                            });
                        }
                    });
                });
                oneDetailRelateNodes.addAll(detailRelateNodes);
            }

            if (CollectionUtils.notEmpty(deleteDataList)) {
                Set<FieldNode> detailRelateNodes = Sets.newHashSet();

                //从主对象的统计字段出发，经过主对象的计算字段再回到从对象的计算字段，整条链路上的计算和统计字段都需要计算
                ObjectDescribeExt.of(masterDescribe).getCountFields(detailApiName).forEach(field -> {
                    graph.getNode(masterDescribe.getApiName(), field.getApiName()).ifPresent(fieldNode -> {
                        Set<FieldNode> relateNodes = graph.getRelateNodes(fieldNode, describeApiNames, tenantId);
                        detailRelateNodes.add(fieldNode);
                        detailRelateNodes.addAll(relateNodes);
                    });
                });

                //从本对象的lookup字段出发，经过其他对象的统计或计算字段再回到页面对象的计算字段，整条链路上的计算和统计字段都需要计算
                ObjectDescribeExt.of(detailDescribe).getActiveSingleReferenceFieldDescribes().forEach(field -> {
                    graph.getNode(detailDescribe.getApiName(), field.getApiName()).ifPresent(fieldNode -> {
                        Set<FieldNode> successors = graph.successors(fieldNode);
                        Set<FieldNode> relateNodes = graph.getRelateNodes(fieldNode, describeApiNames, tenantId);
                        successors.stream()
                                .filter(x -> !Objects.equals(x.getObjectApiName(), detailApiName))
                                .filter(relateNodes::contains)
                                .forEach(x -> {
                                    Set<FieldNode> lookupRelateNodes = graph.reachableNodes(x).stream()
                                            .filter(relateNodes::contains).collect(Collectors.toSet());
                                    detailRelateNodes.add(x);
                                    detailRelateNodes.addAll(lookupRelateNodes);
                                });
                    });
                });

                //如果是lookup字段触发关联对象的统计字段的计算，需要将该统计字段放入在被删除的从对象数据的relateFields中
                detailRelateNodes.stream()
                        .filter(x -> x.isCountField() && !Objects.equals(masterDescribe.getApiName(), x.getObjectApiName()))
                        .forEach(x -> {
                            Count count = (Count) graph.getDescribeMap().get(x.getObjectApiName()).getFieldDescribe(x.getFieldApiName());
                            if (!Objects.equals(count.getSubObjectDescribeApiName(), detailApiName)) {
                                return;
                            }
                            deleteDataList.forEach(oldData -> {
                                String oldLookupId = (String) oldData.get(count.getFieldApiName());
                                if (Strings.isNullOrEmpty(oldLookupId)) {
                                    return;
                                }
                                String index = editCalculateParam.getIndexByObjectData(oldData);
                                List<CalculateObjectData> calculateObjectDataList = detailCalculateDataMap.computeIfAbsent(detailApiName, k -> Lists.newArrayList());
                                CalculateObjectData oldCalculateData = CalculateObjectData.findByIndex(calculateObjectDataList, index).orElse(null);
                                if (Objects.isNull(oldCalculateData)) {
                                    oldCalculateData = CalculateObjectData.of(editCalculateParam.getIndexByObjectData(oldData),
                                            oldData, null, null, true);
                                    calculateObjectDataList.add(oldCalculateData);
                                }
                                //通过已删除的数据传递需要计算统计字段的关联对象的数据id
                                oldCalculateData.addRelateFields(x.getObjectApiName(), Sets.newHashSet(x.toRelateField()));
                            });
                        });
                oneDetailRelateNodes.addAll(detailRelateNodes);
            }

            if (CollectionUtils.notEmpty(updateMap)) {
                Set<FieldNode> detailRelateNodes = Sets.newHashSet();
                updateMap.forEach((index, data) -> {
                    List<IFieldDescribe> fields = ObjectDescribeExt.of(detailDescribe).getFieldByApiNames(ObjectDataExt.of(data).toMap().keySet());
                    if (CollectionUtils.empty(fields)) {
                        return;
                    }

                    IObjectData oldData = CollectionUtils.nullToEmpty(editCalculateParam.getOldDetailDataMap())
                            .getOrDefault(detailApiName, Collections.emptyList()).stream()
                            .filter(x -> Objects.equals(editCalculateParam.getIndexByObjectData(x), index)).findFirst().orElse(null);
                    CalculateObjectData detailCalculateData = CalculateObjectData.findByIndex(detailCalculateDataMap.get(detailApiName), index).get();

                    //从本对象的字段出发，经过其他对象的统计或计算字段再回到本对象的计算字段，整条链路上的计算和统计字段都需要计算
                    fields.forEach(field -> {
                        graph.getNode(detailDescribe.getApiName(), field.getApiName()).ifPresent(fieldNode -> {
                            Set<FieldNode> relateNodes = graph.getRelateNodes(fieldNode, describeApiNames, tenantId);
                            detailRelateNodes.addAll(relateNodes);
                        });

                        //如果是修改了lookup字段触发关联对象的统计字段的计算，需要将修改前的关联对象的数据id保存到被修改的数据中
                        if (Objects.nonNull(oldData) && FieldDescribeExt.of(field).isObjectReferenceField()) {
                            String oldLookupId;
                            try {
                                oldLookupId = (String) oldData.get(field.getApiName());
                            } catch (RuntimeException e) {
                                log.warn("get lookup field value fail! objectApiName:{}, fieldName:{}", detailDescribe.getApiName(), field.getApiName());
                                throw e;
                            }
                            if (!Strings.isNullOrEmpty(oldLookupId)) {
                                //通过__old字段传递需要计算统计字段的关联对象的老数据id
                                ObjectDataExt.of(detailCalculateData.getObjectData()).setOldFieldValue(field.getApiName(), oldLookupId);
                            }
                        }
                    });
                    //给每条从设置需要计算的字段
                    detailCalculateData.addCalculateFields(FieldNode.toRelateFieldSetMap(detailRelateNodes).get(detailApiName));
                    detailCalculateData.addRelateFields(FieldNode.toRelateFieldSetMap(detailRelateNodes));

                    //找到需要过滤的默认值字段
                    defaultValueFields.forEach(field -> {
                        if (editCalculateParam.unCalculateFields(detailApiName, data, field.getApiName())) {
                            graph.getNode(detailApiName, field.getApiName()).ifPresent(node -> {
                                toRemoveCalculateFields.computeIfAbsent(index, k -> Sets.newHashSet()).add(node.toRelateField());
                            });
                        }
                    });
                });
                oneDetailRelateNodes.addAll(detailRelateNodes);
            }
            allCalculateNodes.addAll(oneDetailRelateNodes);
            if (CollectionUtils.notEmpty(toRemoveCalculateFields)) {
                toRemoveCalculateFieldMap.put(detailApiName, toRemoveCalculateFields);
            }
        });

        //将依赖链路上包含主对象字段的从对象字段放入本对象所有数据的calculateFields中
        Set<FieldNode> masterRelateDetailNodes = Sets.newHashSet();
        allCalculateNodes.stream()
                .filter(x -> Objects.equals(masterDescribe.getApiName(), x.getObjectApiName()))
                .forEach(x -> graph.reachableNodes(x).stream()
                        .filter(allCalculateNodes::contains)
                        .filter(y -> detailCalculateDataMap.containsKey(y.getObjectApiName()))
                        .forEach(masterRelateDetailNodes::add));
        FieldNode.toRelateFieldSetMap(masterRelateDetailNodes).forEach((detailApiName, calculateFields) ->
                detailCalculateDataMap.get(detailApiName).forEach(detailCalculateData ->
                        detailCalculateData.addCalculateFields(calculateFields)));

        //从主从对象出发，如果依赖链路上包含lookup对象统计字段，如果是主对象，将统计字段相关字段放入从对象上和主对象关联同一条lookup对象的所有数据的calculateFields中。
        //如果是从对象，将统计字段相关字段放入本对象和其他从对象上和被统计的从对象关联同一条lookup对象的所有数据的calculateFields中，如果主对象也关联了同一条lookup数据，将相关字段放入allCalculateNodes中。
        Lists.newArrayList(allCalculateNodes).stream()
                .filter(FieldNode::isCountField)
                .filter(x -> !describeApiNames.contains(x.getObjectApiName()))
                .forEach(x -> {
                    Count count = (Count) graph.getDescribeMap().get(x.getObjectApiName()).getFieldDescribe(x.getFieldApiName());
                    if (!describeApiNames.contains(count.getSubObjectDescribeApiName())) {
                        return;
                    }
                    Set<String> lookupIds;
                    String lookupFieldName = count.getFieldApiName();
                    if (masterDescribe.getApiName().equals(count.getSubObjectDescribeApiName())) {
                        lookupIds = Lists.newArrayList((String) masterData.get(lookupFieldName), (String) ObjectDataExt.of(masterData).getOldFieldValue(lookupFieldName)).stream()
                                .filter(StringUtils::isNotEmpty)
                                .collect(Collectors.toSet());
                    } else {
                        lookupIds = detailCalculateDataMap.getOrDefault(count.getSubObjectDescribeApiName(), Collections.emptyList()).stream()
                                .filter(d -> d.matchRelateField(x))
                                .flatMap(d -> Lists.newArrayList((String) d.getObjectData().get(lookupFieldName), (String) ObjectDataExt.of(d.getObjectData()).getOldFieldValue(lookupFieldName)).stream()
                                        .filter(StringUtils::isNotEmpty))
                                .collect(Collectors.toSet());
                    }
                    if (CollectionUtils.empty(lookupIds)) {
                        return;
                    }
                    Set<FieldNode> lookupCalculateNodes = graph.reachableNodes(x).stream()
                            .filter(y -> Objects.equals(y.getObjectApiName(), x.getObjectApiName()))
                            .filter(allCalculateNodes::contains)
                            .collect(Collectors.toSet());
                    lookupCalculateNodes.add(x);
                    Set<String> lookupCalculateFieldNames = lookupCalculateNodes.stream()
                            .map(FieldNode::getFieldApiName)
                            .collect(Collectors.toSet());
                    Set<FieldNode> firstSubNodes = lookupCalculateNodes.stream().flatMap(lookupNode ->
                                    graph.successors(lookupNode).stream()
                                            .filter(y -> describeApiNames.contains(y.getObjectApiName())))
                            .collect(Collectors.toSet());
                    firstSubNodes.stream()
                            .collect(Collectors.groupingBy(FieldNode::getObjectApiName))
                            .forEach((subApiName, firstNodeGroup) -> {
                                if (masterDescribe.getApiName().equals(x.getObjectApiName()) && masterDescribe.getApiName().equals(subApiName)) {
                                    return;
                                }
                                IObjectDescribe subDescribe = graph.getDescribeMap().get(subApiName);
                                firstNodeGroup.forEach(firstNode -> {
                                    Set<FieldNode> subRelateNodes = Sets.newHashSet(firstNode);
                                    subRelateNodes.addAll(graph.reachableNodes(firstNode).stream()
                                            .filter(allCalculateNodes::contains)
                                            .filter(n -> subApiName.equals(n.getObjectApiName()))
                                            .collect(Collectors.toSet()));

                                    IFieldDescribe firstField = subDescribe.getFieldDescribe(firstNode.getFieldApiName());
                                    Expression expression = ExpressionFactory.createExpression(subDescribe, firstField, true);
                                    Set<String> lookupFields = expression.getRefObjectFieldVariables().stream()
                                            .filter(r -> Objects.equals(x.getObjectApiName(), r.getTargetObjectAPIName())
                                                    && lookupCalculateFieldNames.contains(r.getTargetFieldAPIName()))
                                            .map(ExpressionVariableFactory.RefObjectFieldVar::getFieldName)
                                            .collect(Collectors.toSet());
                                    if (masterDescribe.getApiName().equals(subApiName)) {
                                        boolean lookupIdMatched = lookupFields.stream()
                                                .map(f -> (String) masterData.get(f))
                                                .filter(StringUtils::isNotEmpty)
                                                .anyMatch(lookupIds::contains);
                                        if (lookupIdMatched) {
                                            allCalculateNodes.addAll(subRelateNodes);
                                        }
                                    } else {
                                        detailCalculateDataMap.getOrDefault(subApiName, Collections.emptyList())
                                                .forEach(d -> {
                                                    boolean lookupIdMatched = lookupFields.stream()
                                                            .map(f -> (String) d.getObjectData().get(f))
                                                            .filter(StringUtils::isNotEmpty)
                                                            .anyMatch(lookupIds::contains);
                                                    if (lookupIdMatched) {
                                                        d.addCalculateFields(FieldNode.toRelateFieldSet(subRelateNodes, subApiName));
                                                        allCalculateNodes.addAll(subRelateNodes);
                                                    }
                                                });
                                    }
                                });
                            });
                });

        //从某个从对象出发，如果主从对象中有其他对象的计算字段或默认值使用了统计该对象的统计字段，并且这个字段是需要计算的，那么把关联对象上的统计字段和相关计算字段加入计算列表
        describeApiNames.forEach(describeApiName -> {
            IObjectDescribe describe = graph.getDescribeMap().get(describeApiName);
            if (Objects.isNull(describe)) {
                log.warn("describe:{} not exit!", describeApiName);
                return;
            }
            ObjectDescribeExt.of(describe).getActiveSingleReferenceFieldDescribes().forEach(refField -> {
                graph.getNode(describeApiName, refField.getApiName()).ifPresent(refNode -> {
                    Set<FieldNode> relateNodes = graph.getRelateNodes(refNode, describeApiNames, tenantId);
                    relateNodes.stream()
                            .filter(FieldNode::isCountField)
                            .filter(x -> !describeApiNames.contains(x.getObjectApiName()))
                            .forEach(x -> {
                                Count count = (Count) graph.getDescribeMap().get(x.getObjectApiName()).getFieldDescribe(x.getFieldApiName());
                                if (!describe.getApiName().equals(count.getSubObjectDescribeApiName())) {
                                    return;
                                }
                                Set<FieldNode> refRelateNodes = relateNodes.stream()
                                        .filter(n -> x.getObjectApiName().equals(n.getObjectApiName()))
                                        .collect(Collectors.toSet());
                                Set<FieldNode> reachableNodes = graph.reachableNodes(x).stream()
                                        .filter(allCalculateNodes::contains)
                                        .filter(y -> describeApiNames.contains(y.getObjectApiName()))
                                        .collect(Collectors.toSet());
                                AtomicBoolean relatedNodesAdded = new AtomicBoolean(false);
                                for (FieldNode fieldNode : reachableNodes) {
                                    if (masterDescribe.getApiName().equals(fieldNode.getObjectApiName())) {
                                        if (relatedNodesAdded.compareAndSet(false, true)) {
                                            allCalculateNodes.addAll(refRelateNodes);
                                        }
                                        allRelateFieldMap.computeIfAbsent(masterDescribe.getApiName(), k -> Maps.newHashMap())
                                                .computeIfAbsent(x.getObjectApiName(), k -> Sets.newHashSet()).add(x.toRelateField());
                                    } else {
                                        detailCalculateDataMap.getOrDefault(fieldNode.getObjectApiName(), Collections.emptyList()).forEach(d -> {
                                            if (d.matchCalculateField(fieldNode)) {
                                                if (relatedNodesAdded.compareAndSet(false, true)) {
                                                    allCalculateNodes.addAll(refRelateNodes);
                                                }
                                                d.addRelateFields(x.getObjectApiName(), Sets.newHashSet(x.toRelateField()));
                                            }
                                        });
                                    }
                                }
                            });
                });
            });
        });

        //过滤依赖链路上包含lookup对象统计字段的默认值、计算字段
        if (editCalculateParam.isExcludeLookupRelateField()) {
            Set<FieldNode> toRemoveNodes = Sets.newHashSet();
            allCalculateNodes.stream().filter(FieldNode::isCountField).forEach(x -> {
                if (describeApiNames.contains(x.getObjectApiName())) {
                    return;
                }
                toRemoveNodes.addAll(graph.reachableNodes(x));
                toRemoveNodes.add(x);
            });
            if (CollectionUtils.notEmpty(toRemoveNodes)) {
                allCalculateNodes.removeAll(toRemoveNodes);
                allRelateFieldMap.forEach((apiName, relateFieldMap) -> relateFieldMap.forEach((k, v) -> v.removeAll(FieldNode.toRelateFieldSet(toRemoveNodes, k))));
                detailCalculateDataMap.forEach((detailApiName, calculateDataList) -> {
                    calculateDataList.forEach(calculateData -> {
                        calculateData.removeCalculateFields(FieldNode.toRelateFieldSet(toRemoveNodes, detailApiName));
                        calculateData.removeRelateFields(FieldNode.toRelateFieldSetMap(toRemoveNodes));
                    });
                });
            }
        }

        //过滤主对象不需要计算的默认值字段
        allCalculateNodes.removeIf(x -> Objects.equals(x.getObjectApiName(), masterDescribe.getApiName())
                && x.isDefaultValue()
                && editCalculateParam.unCalculateFields(masterDescribe.getApiName(), masterData, x.getFieldApiName()));

        //过滤从对象不需要计算的默认值字段
        toRemoveCalculateFieldMap.forEach((detailApiName, toRemoveCalculateFields) -> {
            toRemoveCalculateFields.forEach((index, fields) -> {
                CalculateObjectData.findByIndex(detailCalculateDataMap.get(detailApiName), index)
                        .ifPresent(detailCalculateData -> {
                            detailCalculateData.removeCalculateFields(fields);
                        });
            });
        });

        return CalculateFields.of(graph, FieldNode.toRelateFieldMap(allCalculateNodes), allRelateFieldMap, detailCalculateDataMap);
    }

    @Override
    public CalculateFields computeCalculateFieldsForBatchEditData(BatchEditCalculateParam batchEditCalculateParam) {
        IObjectDescribe objectDescribe = batchEditCalculateParam.getObjectDescribe();
        //每条数据变更的字段及value
        Map<String, Map<String, Object>> masterModifyData = batchEditCalculateParam.getMasterModifyData();
//            主对象及其查找关联对象构图
        FieldRelationGraph graph = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(objectDescribe, Lists.newArrayList(),
                batchEditCalculateParam.isExcludeDefaultValue(), batchEditCalculateParam.isIncludeQuoteField(),
                true, true);


        Set<FieldNode> allCalculateNodes = Sets.newHashSet();

        masterModifyData.forEach((id, updateMap) -> {

            //当前数据被修改的字段
            List<IFieldDescribe> fieldDescribes = ObjectDescribeExt.of(objectDescribe).getFieldByApiNames(updateMap.keySet());
            if (CollectionUtils.empty(fieldDescribes)) {
                return;
            }
            fieldDescribes.forEach(fieldDescribe -> {
                String fieldApiName = fieldDescribe.getApiName();
                graph.getNode(objectDescribe.getApiName(), fieldApiName).ifPresent(node -> {
                    //获取我当前要改的数据受影响的节点
                    Set<FieldNode> relateNodes = graph.getRelateNodes(node, Sets.newHashSet(objectDescribe.getApiName()),
                            objectDescribe.getTenantId());
                    allCalculateNodes.addAll(relateNodes);
                });
            });
        });


        return CalculateFields.of(graph, FieldNode.toRelateFieldMap(allCalculateNodes));
    }

    @Override
    public CalculateFields convertToCalculateFields(String tenantId,
                                                    Map<String, List<String>> fieldNameMap,
                                                    Map<String, IObjectDescribe> describeMap) {
        if (CollectionUtils.empty(fieldNameMap)) {
            return CalculateFields.of(null, Maps.newHashMap());
        }

        if (CollectionUtils.empty(describeMap)) {
            List<String> objectApiNames = Lists.newArrayList(fieldNameMap.keySet());
            describeMap = describeLogicService.findObjectsWithoutCopyIfGray(tenantId, objectApiNames);
        }
        FieldRelationGraph graph = fieldRelationGraphService.buildReverseDependencyGraph(Lists.newArrayList(describeMap.values()),
                false, true, true);

        Map<String, List<RelateField>> calculateFields = Maps.newHashMap();
        fieldNameMap.forEach((apiName, fieldList) -> {
            List<RelateField> relateFields = fieldList.stream()
                    .filter(x -> graph.getNode(apiName, x).isPresent())
                    .map(x -> graph.getNode(apiName, x).get().toRelateField())
                    .collect(Collectors.toList());
            calculateFields.put(apiName, relateFields);
        });

        return CalculateFields.of(graph, calculateFields);
    }

    @Override
    public void validateFieldRelation(IObjectDescribe newDescribe, IObjectDescribe describeInDb, List<ValidateField> fields) {
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + ".validateFieldRelation");
        //编辑的统计字段不做校验
        fields = fields.stream().filter(x -> !FieldDescribeExt.of(x.getFieldDescribe()).isCountField() ||
                        (FieldDescribeExt.of(x.getFieldDescribe()).isCountField() && !describeInDb.containsField(x.getApiName())))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(fields)) {
            return;
        }
        log.debug("validateByField tenantId:{},apiName:{},fields:{}", describeInDb.getTenantId(), describeInDb.getApiName(), fields);

        List<IFieldDescribe> fieldDescribes = fields.stream().map(x -> x.getFieldDescribe()).collect(Collectors.toList());
        stopWatch.lap("getFieldDescribes");

        FieldRelationGraph graph;
        if (FormulaGrayConfig.isInFieldValidateWhiteList(describeInDb.getTenantId())) {
            graph = fieldRelationGraphService.buildFullDependencyGraph(fieldDescribes, newDescribe,
                    false, true, false, true, true);
            stopWatch.lap("buildFullDependencyGraph");
        } else {
            graph = fieldRelationGraphService.buildFullDependencyGraphIncludeSecondLevel(fieldDescribes, newDescribe,
                    false, true, true, true);
            stopWatch.lap("buildFullDependencyGraphIncludeSecondLevel");
        }
        FieldRelationValidator.builder()
                .describe(newDescribe)
                .fields(fields)
                .graph(graph)
                .build()
                .doValidate();
        stopWatch.lap("fields.doValidate");

        List<ValidateField> typeChangedFields = fields.stream().filter(x -> x.isTypeChanged()).collect(Collectors.toList());
        Set<String> associationObjectApiNames = Sets.newHashSet();
        typeChangedFields.forEach(x -> {
            Set<FieldNode> relateNodes = graph.transpose().reachableNodes(graph.getNode(describeInDb.getApiName(), x.getApiName()).get());
            associationObjectApiNames.addAll(relateNodes.stream().filter(y -> !describeInDb.getApiName().equals(y.getObjectApiName()))
                    .map(y -> y.getObjectApiName()).collect(Collectors.toList()));
        });
        stopWatch.lap("typeChangedFields");
        List<IObjectDescribe> associationDescribes = associationObjectApiNames.stream().map(x -> graph.getDescribeMap().get(x)).collect(Collectors.toList());

        // 关联对象和主对象
        List<IObjectDescribe> deeperRelateDescribes = describeLogicService.findAssociationDescribesWithoutCopyIfGray(describeInDb.getTenantId(), associationDescribes);
        List<IObjectDescribe> extDescribes = Lists.newArrayList(deeperRelateDescribes);
        stopWatch.lap("findAssociationDescribes");

        // 相关对象和从对象
        associationDescribes.forEach(x -> {
            List<IObjectDescribe> deeperRelatedDescribes = describeLogicService.findRelatedDescribesWithoutCopyIfGray(describeInDb.getTenantId(), x.getApiName());
            extDescribes.addAll(deeperRelatedDescribes);
        });
        stopWatch.lap("findRelatedDescribes");
        extDescribes.removeIf(x -> describeInDb.getApiName().equals(x.getApiName()));

        //再校验一遍从普通字段变为默认值的字段
        FieldRelationGraph extGraph = fieldRelationGraphService.buildDependencyGraph(graph, extDescribes,
                false, false, true, true);
        FieldRelationValidator.builder()
                .describe(newDescribe)
                .fields(typeChangedFields)
                .graph(extGraph)
                .build()
                .doValidate();
        stopWatch.lap("typeChangedFields.doValidate");
        stopWatch.logSlow(3000);
    }

    @Override
    public List<IFieldDescribe> validateByField(IObjectDescribe describe, IFieldDescribe fieldDescribe) {
        return validateByFields(describe, Lists.newArrayList(fieldDescribe));
    }

    @Override
    public List<IFieldDescribe> validateByFields(IObjectDescribe describeInDb, List<IFieldDescribe> fieldDescribes) {
        return validateByFields(describeInDb, fieldDescribes, true);
    }

    @Override
    public List<IFieldDescribe> validateByFields(IObjectDescribe describeInDb, List<IFieldDescribe> fieldDescribes, boolean updateReference) {
        return validateByFields(describeInDb, fieldDescribes, updateReference, null);
    }

    @Override
    public List<IFieldDescribe> validateByFields(IObjectDescribe describeInDb, List<IFieldDescribe> fieldDescribes,
                                                 boolean updateReference, List<SimpleExpression.VariableInfo> extVariables) {
        List<ValidateField> changeFieldList = Lists.newArrayList();
        List<IFieldDescribe> expressionChangedFields = Lists.newArrayList();

        fieldDescribes.stream().map(x -> FieldDescribeExt.of(x)).filter(x -> x.isActive() && (x.isCountField() || x.isCalculateField())).forEach(x -> {
            IFieldDescribe oldField = ObjectDescribeExt.of(describeInDb).getActiveFieldDescribeSilently(x.getApiName()).orElse(null);
            if (oldField != null) {
                boolean isFieldChanged = false;
                boolean isReturnTypeChanged = false;
                boolean isTypeChanged = false;

                if (x.isCountField()) {
                    isFieldChanged = CountExt.of(x.getFieldDescribe()).isChanged((Count) oldField);
                } else if (x.isFormula()) {
                    isFieldChanged = FormulaExt.of(x.getFieldDescribe()).isChanged((Formula) oldField);
                    isReturnTypeChanged = FormulaExt.of(x.getFieldDescribe()).isReturnTypeChanged((Formula) oldField);
                    if (FormulaExt.of(x.getFieldDescribe()).isExpressionChanged((Formula) oldField)) {
                        expressionChangedFields.add(x.getFieldDescribe());
                    }
                } else if (x.hasFormulaDefaultValue()) {
                    isTypeChanged = !FieldDescribeExt.of(oldField).hasFormulaDefaultValue();
                    Object newDefaultValue = x.getDefaultValue();
                    Object oldDefaultValue = oldField.getDefaultValue();
                    isFieldChanged = isTypeChanged || !Objects.equals(newDefaultValue, oldDefaultValue);
                    if (isFieldChanged) {
                        expressionChangedFields.add(x.getFieldDescribe());
                    }
                }

                if (isFieldChanged) {
                    changeFieldList.add(ValidateField.of(x.getFieldDescribe(), isReturnTypeChanged, isTypeChanged));
                }
            } else {
                changeFieldList.add(ValidateField.of(x.getFieldDescribe(), false, false));
                if (x.isFormula() || x.hasFormulaDefaultValue()) {
                    expressionChangedFields.add(x.getFieldDescribe());
                }
            }
        });

        //构造新的describe用于字段依赖关系校验
        IObjectDescribe newDescribe = ObjectDescribeExt.of(describeInDb).mergeFields(fieldDescribes);
        //对计算字段和默认值字段的表达式进行编译检查
        compileCheckFormulaFields(describeInDb.getTenantId(), newDescribe, expressionChangedFields, extVariables);
        //校验是否使用了最后修改时间字段
        checkLastModifiedTimeInFields(describeInDb.getTenantId(), changeFieldList.stream().map(ValidateField::getFieldDescribe).collect(Collectors.toList()));
        //字段新建编辑层级校验
        validateFieldRelation(newDescribe, describeInDb, changeFieldList);

        List<IFieldDescribe> computeFieldList = changeFieldList.stream().map(x -> x.getFieldDescribe())
                .filter(x -> !FieldDescribeExt.of(x).hasFormulaDefaultValue())
                .collect(Collectors.toList());

        List<IFieldDescribe> formulaFields = changeFieldList.stream()
                .map(x -> x.getFieldDescribe())
                .filter(x -> FieldDescribeExt.of(x).isFormula())
                .collect(Collectors.toList());

        //判断计算字段是否支持落地(时间日期全局变量、引用人员类型)  is_index
        checkFormulaFieldsIndexValue(newDescribe, formulaFields, updateReference);

        //判断是否引用了自定义的全局变量
        checkReferenceOfFormulaField(newDescribe, formulaFields, updateReference);

        List<IFieldDescribe> filterFormulaFields = formulaFields.stream()
                .filter(x -> !x.isIndex())
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(filterFormulaFields)) {
            computeFieldList.removeAll(filterFormulaFields);
        }

        //禁用的对象不触发全量计算
        if (!describeInDb.isActive()) {
            return Lists.newArrayList();
        }
        return computeFieldList;
    }

    private void checkLastModifiedTimeInFields(String tenantId, List<IFieldDescribe> fieldDescribes) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FILTER_LAST_MODIFIED_TIME_FIELD_FOR_FORMULA, tenantId)) {
            return;
        }
        if (CollectionUtils.empty(fieldDescribes)) {
            return;
        }
        fieldDescribes.forEach(fieldDescribe -> {
            if (FieldDescribeExt.of(fieldDescribe).isCountField()) {
                Count count = (Count) fieldDescribe;
                if (CountExt.of(count).usingLastModifiedTime()) {
                    throw new ValidateException(I18NExt.getOrDefault(I18NKey.LAST_MODIFIED_TIME_FIELD_NOT_SUPPORT_COUNT,
                            "统计字段不支持使用最后修改时间字段作为汇总字段或过滤范围:{0}", fieldDescribe.getLabel()));// ignoreI18n
                }
            } else if (FieldDescribeExt.of(fieldDescribe).isQuoteField()) {
                Quote quote = (Quote) fieldDescribe;
                if (QuoteExt.of(quote).usingLastModifiedTime()) {
                    throw new ValidateException(I18NExt.getOrDefault(I18NKey.LAST_MODIFIED_TIME_FIELD_NOT_SUPPORT_QUOTE,
                            "引用字段字段不支持引用最后修改时间字段:{0}", fieldDescribe.getLabel()));// ignoreI18n
                }
            } else if (FieldDescribeExt.of(fieldDescribe).isFormula()) {
                Formula formula = (Formula) fieldDescribe;
                if (FormulaExt.of(formula).usingLastModifiedTime()) {
                    throw new ValidateException(I18NExt.getOrDefault(I18NKey.LAST_MODIFIED_TIME_FIELD_NOT_SUPPORT_FORMULA,
                            "计算公式中不支持使用最后修改时间字段:{0}", fieldDescribe.getLabel()));// ignoreI18n
                }
            }
        });
    }

    private void compileCheckFormulaFields(String tenantId, IObjectDescribe objectDescribe, List<IFieldDescribe> fieldDescribes,
                                           List<SimpleExpression.VariableInfo> extVariables) {
        if (CollectionUtils.empty(fieldDescribes)) {
            return;
        }
        fieldDescribes.forEach(fieldDescribe -> {
            ExpressionDTO expressionDTO = null;
            List<ExpressionDTO.FormVariableDTO> extFields = CollectionUtils.nullToEmpty(extVariables).stream()
                    .map(ExpressionDTO.FormVariableDTO::of)
                    .collect(Collectors.toList());
            if (FieldDescribeExt.of(fieldDescribe).isFormula()) {
                Formula formula = (Formula) fieldDescribe;
                expressionDTO = ExpressionDTO.builder()
                        .expressionType(ExpressionDTO.ExpressionType.FORMULA)
                        .objectDescribeApiName(objectDescribe.getApiName())
                        .calculateFieldApiName(formula.getApiName())
                        .returnType(formula.getReturnType())
                        .expression(formula.getExpression())
                        .decimalPlaces(formula.getDecimalPlaces())
                        .setDefaultToZero(formula.getDefaultToZero())
                        .expressionLabel(formula.getLabel())
                        .extFields(extFields)
                        .build();
            } else if (FieldDescribeExt.of(fieldDescribe).hasFormulaDefaultValue()) {
                expressionDTO = ExpressionDTO.builder()
                        .expressionType(ExpressionDTO.ExpressionType.DEFAULT_VALUE)
                        .objectDescribeApiName(objectDescribe.getApiName())
                        .calculateFieldApiName(fieldDescribe.getApiName())
                        .returnType(fieldDescribe.getType())
                        .expression(fieldDescribe.getDefaultValue() == null ? null : fieldDescribe.getDefaultValue().toString())
                        .decimalPlaces(FieldDescribeExt.of(fieldDescribe).getDecimalPlaces())
                        .setDefaultToZero(fieldDescribe.getDefaultToZero())
                        .expressionLabel(fieldDescribe.getLabel())
                        .extFields(extFields)
                        .build();
            }
            //不校验空表达式
            if (expressionDTO == null || Strings.isNullOrEmpty(expressionDTO.getExpression())) {
                return;
            }
            try {
                expressionCalculateLogicService.compileCheck(tenantId, expressionDTO, objectDescribe);
            } catch (AppBusinessException e) {
                throw e;
            } catch (Exception e) {
                log.warn("expression compile error,tenantId:{},expressionDTO:{}", tenantId, expressionDTO, e);
                throw new MetaDataBusinessException(I18N.text(I18NKey.FORMULA_SYNTAX_ERROR) + ":" + fieldDescribe.getLabel());
            }
        });
    }

    @Override
    public List<IFieldDescribe> notEndJobForEach(User user, String objApiName, List<IFieldDescribe> fieldDescribes) {
        if (CollectionUtils.empty(fieldDescribes)) {
            return Lists.newArrayList();
        }
        ResultInfo resultInfo = jobScheduleService.queryNotEndJob(user, objApiName, FORMULA_JOB_TYPE);
        List<JobInfo> jobInfoList = resultInfo.getJobInfoList();
        if (CollectionUtils.empty(jobInfoList)) {
            return Lists.newArrayList();
        }
        Set<String> names = Sets.newHashSet();
        jobInfoList.forEach(jobInfo -> {
            String jobParam = jobInfo.getJobParam();
            List<String> fields = JacksonUtils.fromJson(jobParam, new TypeReference<List<String>>() {
            });
            if (CollectionUtils.notEmpty(fields)) {
                names.addAll(fields);
            }
        });
        if (CollectionUtils.empty(names)) {
            return Lists.newArrayList();
        }

        return fieldDescribes.stream()
                .filter(fieldDescribe -> names.contains(fieldDescribe.getApiName())).collect(Collectors.toList());
    }


    @Override
    public List<IFieldDescribe> validateByObjectDescribe(IObjectDescribe objectDescribe, IObjectDescribe describeInDb) {
        //新增或变更了统计字段
        List<ValidateField> changedList = Lists.newArrayList();
        List<Count> changedCounts = ObjectDescribeExt.of(objectDescribe).diffCountField(describeInDb);
        if (CollectionUtils.notEmpty(changedCounts)) {
            List<IFieldDescribe> newCount = ObjectDescribeExt.of(objectDescribe).getNewCount(describeInDb);
            describeLogicService.maxCountValidate(User.systemUser(describeInDb.getTenantId()), true, newCount);
            changedCounts.forEach(x -> changedList.add(ValidateField.of(x, false, false)));
        }

        List<IFieldDescribe> expressionChangedFields = Lists.newArrayList();
        //新增或变更了计算字段
        List<Formula> changedFormulas = ObjectDescribeExt.of(objectDescribe).diffFormulaField(describeInDb);
        if (CollectionUtils.notEmpty(changedFormulas)) {
            changedFormulas.forEach(x -> {
                IFieldDescribe oldField = describeInDb.getFieldDescribe(x.getApiName());
                if (oldField != null && FieldDescribeExt.of(oldField).isFormula()) {
                    boolean returnTypeChanged = FormulaExt.of(x).isReturnTypeChanged((Formula) oldField);
                    changedList.add(ValidateField.of(x, returnTypeChanged, false));
                    if (FormulaExt.of(x).isExpressionChanged((Formula) oldField)) {
                        expressionChangedFields.add(x);
                    }
                } else {
                    changedList.add(ValidateField.of(x, false, false));
                    expressionChangedFields.add(x);
                }
            });
        }

        //字段校验计算字段、统计字段和默认值
        List<IFieldDescribe> changedDefaultValues = ObjectDescribeExt.of(objectDescribe).diffDefaultValueField(describeInDb);
        changedDefaultValues.forEach(x -> {
            IFieldDescribe oldField = describeInDb.getFieldDescribe(x.getApiName());
            if (oldField != null) {
                boolean typeChanged = !FieldDescribeExt.of(oldField).hasFormulaDefaultValue();
                changedList.add(ValidateField.of(x, false, typeChanged));
                if (!Objects.equals(x.getDefaultValue(), oldField.getDefaultValue())) {
                    expressionChangedFields.add(x);
                }
            } else {
                changedList.add(ValidateField.of(x, false, false));
                expressionChangedFields.add(x);
            }
        });

        //对新增的字段和表达式变更的字段进行编译检查
        compileCheckFormulaFields(describeInDb.getTenantId(), objectDescribe, expressionChangedFields, null);
        //校验是否使用了最后修改时间字段
        checkLastModifiedTimeInFields(describeInDb.getTenantId(), changedList.stream().map(ValidateField::getFieldDescribe).collect(Collectors.toList()));
        //字段新建编辑层级校验
        validateFieldRelation(objectDescribe, describeInDb, changedList);

        List<IFieldDescribe> fieldList = changedList.stream()
                .filter(x -> !FieldDescribeExt.of(x.getFieldDescribe()).hasFormulaDefaultValue())
                .map(x -> x.getFieldDescribe())
                .collect(Collectors.toList());

        //判断计算字段是否支持落地
        List<IFieldDescribe> formulaFields = changedList.stream()
                .map(x -> x.getFieldDescribe())
                .filter(x -> FieldDescribeExt.of(x).isFormula())
                .collect(Collectors.toList());
        checkFormulaFieldsIndexValue(objectDescribe, formulaFields, true);

        //判断是否引用了自定义的全局变量
        checkReferenceOfFormulaField(objectDescribe, formulaFields, true);

        List<IFieldDescribe> filterFormulaFields = formulaFields.stream()
                .filter(x -> !x.isIndex())
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(filterFormulaFields)) {
            fieldList.removeAll(filterFormulaFields);
        }

        //禁用的对象不触发全量计算
        if (!objectDescribe.isActive()) {
            return Lists.newArrayList();
        }
        return fieldList;
    }

    @Override
    public void checkDecimalDigitChangeByDescribe(IObjectDescribe describe, IObjectDescribe describeInDb) {
        List<IFieldDescribe> changedList = Lists.newArrayList();
        List<IFieldDescribe> fieldDescribes = describe.getFieldDescribes();
        fieldDescribes.stream().map(x -> FieldDescribeExt.of(x)).filter(x -> x.isActive() && (StringUtils.equals("number", x.getType())
                || StringUtils.equals("currency", x.getType()))).forEach(x -> {
            IFieldDescribe oldField = ObjectDescribeExt.of(describeInDb).getActiveFieldDescribeSilently(x.getApiName()).orElse(null);
            if (oldField != null) {
                int newPlaces = x.get("decimal_places", Integer.class);
                int oldPlaces = oldField.get("decimal_places", Integer.class);
                if (newPlaces != oldPlaces) {
                    changedList.add(x.getFieldDescribe());
                }
            }
        });
        if (CollectionUtils.notEmpty(changedList)) {
            checkDecimalDigitChangeByFields(describe, changedList, false);
        }
    }

    @Override
    public void checkDecimalDigitChangeByFields(IObjectDescribe describe, List<IFieldDescribe> fieldDescribes, Boolean isFilter) {
        List<String> changedList = Lists.newArrayList();
        Map<String, List<IFieldDescribe>> fieldMap = Maps.newConcurrentMap();
        if (isFilter) {
            fieldDescribes.stream().map(x -> FieldDescribeExt.of(x)).filter(x -> x.isActive() && (StringUtils.equals("number", x.getType())
                    || StringUtils.equals("currency", x.getType()))).forEach(x -> {
                IFieldDescribe oldField = ObjectDescribeExt.of(describe).getActiveFieldDescribeSilently(x.getApiName()).orElse(null);
                if (oldField != null) {
                    int newPlaces = x.get("decimal_places", Integer.class);
                    int oldPlaces = oldField.get("decimal_places", Integer.class);
                    if (newPlaces != oldPlaces) {
                        changedList.add(x.getApiName());
                    }
                }
            });
        } else {
            List<String> fieldApiNames = fieldDescribes.stream().map(x -> x.getApiName()).collect(Collectors.toList());
            changedList.addAll(fieldApiNames);
        }

        if (CollectionUtils.notEmpty(changedList)) {
            String apiName = describe.getApiName();
            FieldRelationGraph graph = fieldRelationGraphService.buildReverseFullDependencyGraph(describe,
                    null, true, false, false, true, false);
            Map<String, IObjectDescribe> describeMap = graph.getDescribeMap();
            changedList.forEach(x -> {
                Optional<FieldNode> node = graph.getNode(apiName, x);
                node.ifPresent(n -> {
                    Set<FieldNode> successors = graph.successors(n);
                    if (CollectionUtils.notEmpty(successors)) {
                        successors.stream().filter(y -> y.isCountField() || y.isFormula()).forEach(y -> {
                            IObjectDescribe objectDescribe = describeMap.get(y.getObjectApiName());
                            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(y.getFieldApiName());
                            fieldMap.putIfAbsent(y.getObjectApiName(), Lists.newArrayList());
                            List<IFieldDescribe> fieldList = fieldMap.get(y.getObjectApiName());
                            fieldList.add(fieldDescribe);
                        });
                    }
                });
            });
            if (CollectionUtils.notEmpty(fieldMap.keySet())) {
                User user = User.systemUser(describe.getTenantId());
                fieldMap.forEach((k, v) -> {
                    //重新计算历史数据
                    List<String> fieldNames = v.stream().map(f -> f.getApiName()).collect(Collectors.toList());
                    jobScheduleService.submitCalculateJob(user, fieldNames, k);
                });
            }
        }
    }

    @Override
    public List<IFieldDescribe> findUnStoredFormulaFields(IObjectDescribe objectDescribe, List<IFieldDescribe> formulaFields, List<String> includeType) {
        FieldRelationGraph relationGraph = null;
        if (CollectionUtils.empty(formulaFields)) {
            formulaFields = ObjectDescribeExt.of(objectDescribe).getFormulaFields();
        }
        if (CollectionUtils.empty(includeType)) {
            includeType = Expression.UNSTORED_TYPE;
        }
        if (includeType.contains(Expression.DEPARTMENT)) {
            relationGraph = fieldRelationGraphService.buildDependencyGraph(Lists.newArrayList(objectDescribe), true);
        }

        List<String> typeList = includeType;
        FieldRelationGraph graph = relationGraph;
        List<IFieldDescribe> unUnStoredFields = Lists.newArrayList();
        //直接、间接使用全局变量和人员类型的计算字段
        formulaFields.forEach(x -> {
            Expression expression = ExpressionFactory.createExpression(objectDescribe, x, true);
            if (typeList.contains(Expression.GLOBAL_VARIABLE) && expression.containsDynamicGlobalVariable()) {
                unUnStoredFields.add(x);
                return;
            }
            if (typeList.contains(Expression.TIME_FUNCTION) && expression.isTimeFunctionUnStoredType()) {
                unUnStoredFields.add(x);
                return;
            }
            //灰度了引用人员字段的计算字段支持筛选的企业不用更新is_index
            if (typeList.contains(Expression.EMPLOYEE) && !AppFrameworkConfig.isGrayFilterEmployeeFormula(objectDescribe.getTenantId())) {
                expression.getRefObjectFieldVariables().stream()
                        .filter(y -> Objects.equals(IFieldType.EMPLOYEE, y.getFieldType()))
                        .findFirst()
                        .ifPresent(y -> unUnStoredFields.add(x));
            }
            if (typeList.contains(Expression.DEPARTMENT)) {
                if (expression.isContainOwnerDepartment()) {
                    unUnStoredFields.add(x);
                }
                if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.GRAY_FILTER_BY_DEPARTMENT_FORMULA_TENANTS, objectDescribe.getTenantId())) {
                    return;
                }
                Set<FieldNode> successors = graph.successors(FieldNode.of(objectDescribe.getApiName(), x.getApiName())).stream().filter(y -> StringUtils.equals(objectDescribe.getApiName(), y.getObjectApiName())).collect(Collectors.toSet());
                successors.stream().filter(y -> Objects.equals(IFieldType.DEPARTMENT, objectDescribe.getFieldDescribe(y.getFieldApiName()).getType()))
                        .findFirst()
                        .ifPresent(y -> unUnStoredFields.add(x));
            }

        });

        if (CollectionUtils.notEmpty(unUnStoredFields)) {
            List<IFieldDescribe> fieldDescribes = indirectUnStoredFormulaFields(objectDescribe, unUnStoredFields, graph);
            return fieldDescribes;
        }
        return unUnStoredFields;
    }

    @Override
    public Map<String, Set<String>> findUnReCalculateFields(IObjectDescribe objectDescribe, Map<String, IObjectDescribe> detailDescribeMap, FieldRelationGraph relationGraph) {
        List<String> typeList = Expression.UNSTORED_TYPE;
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap(detailDescribeMap);
        describeMap.put(objectDescribe.getApiName(), objectDescribe);
        List<IFieldDescribe> unReCalculateFields = Lists.newArrayList();
        describeMap.values().forEach(describe -> {
            List<IFieldDescribe> calculateFields = ObjectDescribeExt.of(describe).getCalculateFields();
            List<IFieldDescribe> unUnStoredFields = Lists.newArrayList();
            //直接、间接使用全局变量和人员类型的计算字段
            calculateFields.forEach(x -> {
                Expression expression = ExpressionFactory.createExpression(describe, x, true);
                if (typeList.contains(Expression.GLOBAL_VARIABLE) && expression.containsDynamicGlobalVariable()) {
                    unUnStoredFields.add(x);
                    return;
                }
                if (typeList.contains(Expression.TIME_FUNCTION) && expression.isTimeFunctionUnStoredType()) {
                    unUnStoredFields.add(x);
                }
            });
            unReCalculateFields.addAll(unUnStoredFields);
        });
        Map<String, Set<String>> resultMap = Maps.newHashMap();
        Set<String> expectedObjectApiNames = Sets.newHashSet(describeMap.keySet());
        unReCalculateFields.stream()
                .map(field -> relationGraph.getNode(field.getDescribeApiName(), field.getApiName()).get())
                .map(fieldNode -> {
                    Set<FieldNode> relateNodes = relationGraph.getRelateNodes(fieldNode, expectedObjectApiNames, objectDescribe.getTenantId());
                    relateNodes.add(fieldNode);
                    return relateNodes;
                })
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(FieldNode::getObjectApiName))
                .forEach((describeApiName, fieldNodes) -> {
                    Set<String> fieldNames = fieldNodes.stream()
                            .map(FieldNode::getFieldApiName)
                            .collect(Collectors.toSet());
                    resultMap.put(describeApiName, fieldNames);
                });
        return resultMap;
    }

    private List<IFieldDescribe> indirectUnStoredFormulaFields(IObjectDescribe objectDescribe, List<IFieldDescribe> unUnStoredFields, FieldRelationGraph relationGraph) {
        String describeApiName = objectDescribe.getApiName();
        log.info("indirectUnStoredFormulaFields describeApiName:{}", describeApiName);
        if (Objects.isNull(relationGraph)) {
            relationGraph = fieldRelationGraphService.buildDependencyGraph(Lists.newArrayList(objectDescribe), true);
        }
        FieldRelationGraph graph = relationGraph;

        List<FieldNode> nodeList = ObjectDescribeExt.of(objectDescribe).getFormulaFields().stream().filter(x -> !unUnStoredFields.contains(x)).map(x -> graph.getNode(describeApiName, x.getApiName()).get()).collect(Collectors.toList());
        Set<String> indirectFieldApiNames = Sets.newHashSet();
        List<FieldNode> unStoredNodes = unUnStoredFields.stream().map(x -> graph.getNode(describeApiName, x.getApiName()).get()).collect(Collectors.toList());
        List<String> allUnStoredNodes = unStoredNodes.stream().map(x -> x.getFieldApiName()).collect(Collectors.toList());

        graph.fetchRelateNodes(nodeList, allUnStoredNodes, unStoredNodes);
        if (CollectionUtils.notEmpty(allUnStoredNodes)) {
            indirectFieldApiNames.addAll(allUnStoredNodes);
        }
        if (CollectionUtils.notEmpty(indirectFieldApiNames)) {
            List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes().stream().filter(x -> indirectFieldApiNames.contains(x.getApiName())).collect(Collectors.toList());
            return fieldDescribes;
        }
        return null;
    }

    private void checkFormulaFieldsIndexValue(IObjectDescribe objectDescribe, List<IFieldDescribe> formulaFields, boolean isUpdateOtherFields) {
        if (CollectionUtils.empty(formulaFields)) {
            return;
        }
        List<IFieldDescribe> unStoredFormulaFields = findUnStoredFormulaFields(objectDescribe, formulaFields, Lists.newArrayList());
        List<String> unStoredFormulaApiNames = unStoredFormulaFields.stream().map(x -> x.getApiName()).collect(Collectors.toList());
        List<String> formulaApiNames = formulaFields.stream().map(x -> x.getApiName()).collect(Collectors.toList());
        if (isUpdateOtherFields) {
            formulaFields.stream().filter(x -> unStoredFormulaApiNames.contains(x.getApiName())).forEach(x -> x.setIndex(false));
            formulaFields.stream().filter(x -> !x.isIndex()).filter(x -> !unStoredFormulaApiNames.contains(x.getApiName())).forEach(x -> x.setIndex(true));
            List<IFieldDescribe> otherUnStoredFields = unStoredFormulaFields.stream()
                    .filter(x -> !formulaApiNames.contains(x.getApiName()))
                    .filter(x -> StringUtils.equals(objectDescribe.getApiName(), x.getDescribeApiName()))
                    .collect(Collectors.toList());
            if (CollectionUtils.notEmpty(otherUnStoredFields)) {
                describeLogicService.updateFieldDescribe(objectDescribe, otherUnStoredFields);
            }
        } else {
            objectDescribe.getFieldDescribes().stream().filter(x -> unStoredFormulaApiNames.contains(x.getApiName())).forEach(x -> x.setIndex(false));
        }
    }

    @Override
    public void deleteFormulaReferenceByDescribe(String tenantId, IObjectDescribe oldDescribe, IObjectDescribe newDescribe) {
        List<String> deleteFormulaApiNames = ObjectDescribeExt.of(oldDescribe).getFieldDescribesSilently().stream()
                .filter(x -> FieldDescribeExt.of(x).isFormula())
                .map(IFieldDescribe::getApiName)
                .filter(x -> Objects.isNull(newDescribe) || !newDescribe.containsField(x))
                .collect(Collectors.toList());
        deleteFormulaReference(tenantId, oldDescribe.getApiName(), deleteFormulaApiNames);
    }

    @Override
    public void deleteFormulaReferenceByFields(String tenantId, String objectApiName, List<IFieldDescribe> fieldDescribes) {
        if (CollectionUtils.empty(fieldDescribes)) {
            return;
        }
        List<String> formulaApiNames = fieldDescribes.stream()
                .filter(x -> FieldDescribeExt.of(x).isFormula())
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toList());
        deleteFormulaReference(tenantId, objectApiName, formulaApiNames);
    }

    @Override
    public void deleteFormulaReference(String tenantId, String objectApiName, List<String> formulaApiNames) {
        if (CollectionUtils.empty(formulaApiNames)) {
            return;
        }
        try {
            List<String> sourceValues = formulaApiNames.stream()
                    .map(x -> String.format("%s.%s", objectApiName, x))
                    .collect(Collectors.toList());
            referenceLogicService.batchDeleteReference(tenantId, SourceTypes.FORMULA, sourceValues);
        } catch (Exception e) {
            log.error("deleteFormulaReference failed tenantId:{},objectApiName:{},formulaApiNames:{}", tenantId,
                    objectApiName, formulaApiNames, e);
        }
    }

    @Override
    public List<ReferenceData> checkReferenceOfFormulaField(IObjectDescribe objectDescribe, List<IFieldDescribe> formulaFields, boolean isUpdateReferenceData) {
        if (CollectionUtils.empty(formulaFields)) {
            return Lists.newArrayList();
        }
        Set<ReferenceData> items = Sets.newHashSet();
        String sourceType = SourceTypes.FORMULA;
        // TODO: 2024/9/20 I18n
        String sourceLabel = "计算字段"; // ignoreI18n
        formulaFields.forEach(x -> {
            String sourceValue = objectDescribe.getApiName() + "." + x.getApiName();
            Expression expression = ExpressionFactory.createExpression(objectDescribe, x, true);
            //暂时不过滤日期时间的全局变量
            Set<String> variableAPINames = expression.getDependentGlobalVariableAPINames();
            if (CollectionUtils.notEmpty(variableAPINames)) {
                variableAPINames.forEach(y -> {
                    ReferenceData referenceData = ReferenceData.builder()
                            .sourceType(sourceType)
                            .sourceLabel(sourceLabel)
                            .sourceValue(sourceValue)
                            .targetType(TargetTypes.GLOBAL_VARIABLE)
                            .targetValue(y)
                            .build();
                    items.add(referenceData);
                });
            }

            if (!isUpdateReferenceData || AppFrameworkConfig.isGrayFilterEmployeeFormula(objectDescribe.getTenantId())) {
                //保存计算字段和人员字段的依赖关系
                expression.getRefObjectFieldVariables().stream()
                        .filter(y -> IFieldType.EMPLOYEE.equals(y.getFieldType()))
                        .forEach(y -> {
                            ReferenceData referenceData = ReferenceData.builder()
                                    .sourceType(sourceType)
                                    .sourceLabel(sourceLabel)
                                    .sourceValue(sourceValue)
                                    .targetType(TargetTypes.RELATED_DESCRIBE_FIELD)
                                    .targetValue(ObjectDescribeExt.PERSONNEL_OBJ_API_NAME + "." + y.getTargetFieldAPIName() + "." + y.getFieldName())
                                    .build();
                            items.add(referenceData);
                        });
            }
            if (!isUpdateReferenceData || UdobjGrayConfig.isAllow(UdobjGrayConfigKey.GRAY_FILTER_BY_DEPARTMENT_FORMULA_TENANTS, objectDescribe.getTenantId())) {
                //保存计算字段和部门字段的依赖关系
                expression.getRefObjectFieldVariables().stream()
                        .filter(y -> IFieldType.DEPARTMENT.equals(y.getFieldType()))
                        .forEach(y -> {
                            ReferenceData referenceData = ReferenceData.builder()
                                    .sourceType(sourceType)
                                    .sourceLabel(sourceLabel)
                                    .sourceValue(sourceValue)
                                    .targetType(TargetTypes.RELATED_DESCRIBE_FIELD)
                                    .targetValue(ObjectDescribeExt.DEPARTMENT_OBJ_API_NAME + "." + y.getTargetFieldAPIName() + "." + y.getFieldName())
                                    .build();
                            items.add(referenceData);
                        });
            }
        });
        if (isUpdateReferenceData && CollectionUtils.notEmpty(items)) {
            referenceLogicService.deleteAndCreateReference(objectDescribe.getTenantId(), Lists.newArrayList(items));
        }
        return Lists.newArrayList(items);
    }

    @Override
    public void findRelationFormulaOfGlobalVariable(IGlobalVariableDescribe globalVariable, IGlobalVariableDescribe dbGlobalVariable) {
        //对比值是否变化
        if (globalVariable == null || dbGlobalVariable == null) {
            return;
        }
        if (Objects.equals(globalVariable.getValue(), dbGlobalVariable.getValue())) {
            return;
        }
        String tenantId = globalVariable.getTenantId();
        List<ReferenceData> items = referenceLogicService.findReferenceByTarget(tenantId, TargetTypes.GLOBAL_VARIABLE, globalVariable.getApiName());
        if (CollectionUtils.notEmpty(items)) {
            Map<String, List<String>> fieldMap = Maps.newConcurrentMap();
            items.forEach(x -> {
                String sourceValue = x.getSourceValue();
                String[] split = sourceValue.split("\\.");
                String describeApiName = split[0];
                String fieldApiName = split[1];
                fieldMap.putIfAbsent(describeApiName, Lists.newArrayList());
                fieldMap.get(describeApiName).add(fieldApiName);
            });
            User user = User.systemUser(tenantId);
            //全局变量值变更触发相关计算字段重新计算，已存历史数据全量计算，暂不处理历史数据范围
            jobScheduleService.submitCalculateJob(user, fieldMap);
        }

    }

    @Override
    public void checkSelectOneChangeOfDescribe(IObjectDescribe newDescribe, IObjectDescribe describeInDb) {
        List<IFieldDescribe> fieldDescribes = newDescribe.getFieldDescribes().stream()
                .filter(x -> Objects.equals(IFieldType.SELECT_ONE, x.getType()))
                .filter(x -> describeInDb.containsField(x.getApiName()))
                .collect(Collectors.toList());
        checkSelectOneChangeOfFields(newDescribe, describeInDb, fieldDescribes, true);
    }

    @Override
    public void checkSelectOneChangeOfFields(IObjectDescribe newDescribe, IObjectDescribe describeInDb, List<IFieldDescribe> fieldDescribes, boolean isNeedDiff) {
        if (CollectionUtils.empty(fieldDescribes)) {
            return;
        }
        List<IFieldDescribe> changedFields = Lists.newArrayList();
        Set<String> optionDeletedFields = Sets.newHashSet();
        ObjectDescribeExt draftExt = ObjectDescribeExt.of(describeInDb);
        if (isNeedDiff) {
            fieldDescribes.forEach(x -> {
                if (Objects.equals(IFieldType.SELECT_ONE, x.getType())) {
                    // 针对选项集，重新填充选项
                    optionSetLogicService.fillSelectOption(User.systemUser(describeInDb.getTenantId()), x);
                    Optional<IFieldDescribe> field = draftExt.getFieldDescribeSilently(x.getApiName());
                    if (!field.isPresent()) {
                        return;
                    }
                    SelectOne newField = (SelectOne) x;
                    SelectOne oldField = (SelectOne) field.get();
                    Map<String, Object> newFieldMap = newField.getSelectOptions().stream().collect(Collectors.toMap(ISelectOption::getValue, o -> Strings.nullToEmpty(o.getLabel())));
                    Map<String, Object> oldFieldMap = oldField.getSelectOptions().stream().collect(Collectors.toMap(ISelectOption::getValue, o -> Strings.nullToEmpty(o.getLabel())));
                    MapDifference<String, Object> difference = Maps.difference(newFieldMap, oldFieldMap);
                    //修改了选项label或删除了选项需要触发计算字段的全量计算
                    if (CollectionUtils.notEmpty(difference.entriesDiffering()) || CollectionUtils.notEmpty(difference.entriesOnlyOnRight())) {
                        changedFields.add(x);
                    }
                    if (CollectionUtils.notEmpty(difference.entriesOnlyOnRight())) {
                        optionDeletedFields.add(x.getApiName());
                    }
                }
            });
        } else {
            changedFields.addAll(fieldDescribes);
        }

        if (CollectionUtils.notEmpty(changedFields)) {
            Map<String, List<String>> fieldMap = Maps.newHashMap();
            String apiName = describeInDb.getApiName();
            // 相关对象和从对象
            List<IObjectDescribe> relatedDescribes = describeLogicService.findRelatedDescribesWithoutCopyIfGray(describeInDb.getTenantId(), apiName);
            relatedDescribes.removeIf(x -> apiName.equals(x.getApiName()));

            List<FieldRelationGraphBuilder.GraphLayer> graphLayers = Lists.newArrayList();
            graphLayers.add(FieldRelationGraphBuilder.GraphLayer.of(Lists.newArrayList(newDescribe)));
            graphLayers.add(FieldRelationGraphBuilder.GraphLayer.of(relatedDescribes));

            FieldRelationGraph graph = FieldRelationGraphBuilder.builder()
                    .describeLogicService(describeLogicService)
                    .graphLayers(graphLayers)
                    .excludeDefaultValue(true)
                    .fillFieldType(true)
                    .ignoreInvalidVariable(true)
                    .build()
                    .getGraph();

            changedFields.forEach(x -> {
                Optional<FieldNode> node = graph.getNode(x.getDescribeApiName(), x.getApiName());
                if (node.isPresent()) {
                    Set<FieldNode> predecessors = graph.predecessors(node.get());
                    List<FieldNode> calculateNodes = predecessors.stream().filter(y -> y.isFormula()).collect(Collectors.toList());
                    calculateNodes.forEach(y -> {
                        Formula formulaField = (Formula) graph.getDescribeMap().get(y.getObjectApiName()).getFieldDescribe(y.getFieldApiName());
                        if (formulaField == null
                                || (!optionDeletedFields.contains(y.getFieldApiName()) && !formulaField.getExpression().replace(x.getApiName() + Expression.VALUE, "").contains(x.getApiName()))) {
                            return;
                        }
                        String objectApiName = y.getObjectApiName();
                        fieldMap.putIfAbsent(objectApiName, Lists.newArrayList());
                        fieldMap.get(objectApiName).add(y.getFieldApiName());
                    });
                }
            });

            if (CollectionUtils.notEmpty(fieldMap)) {
                User user = User.builder().tenantId(describeInDb.getTenantId()).userId(User.SUPPER_ADMIN_USER_ID).build();
                //选项更新、选项删除触发相关计算字段重新计算，已存历史数据全量计算，暂不处理历史数据范围
                jobScheduleService.submitCalculateJob(user, fieldMap);
            }
        }
    }

    @Override
    public List<IFieldDescribe> checkQuoteField(IObjectDescribe describe, IFieldDescribe fieldDescribe) {
        return checkQuoteFields(describe, Lists.newArrayList(fieldDescribe));
    }

    private List<IFieldDescribe> checkQuoteFields(IObjectDescribe describe, List<IFieldDescribe> fieldDescribes) {
        List<Quote> changeFieldList = Lists.newArrayList();

        // 收集需要变更的引用字段
        fieldDescribes.stream()
                .filter(x -> x.isActive() && FieldDescribeExt.of(x).isQuoteField())
                .map(x -> (Quote) x)
                .forEach(quote -> {
                    IFieldDescribe oldField = ObjectDescribeExt.of(describe).getActiveFieldDescribeSilently(quote.getApiName()).orElse(null);
                    if (oldField != null) {
                        if (!oldField.isIndex() && quote.isIndex()) {
                            checkLastModifiedTimeInFields(describe.getTenantId(), Lists.newArrayList(quote));
                            changeFieldList.add(quote);
                        } else if (!Objects.equals(FieldDescribeExt.of(oldField).getIsSingle(), FieldDescribeExt.of(quote).getIsSingle())) {
                            QuoteExt.of(quote).syncSingleFlag(oldField);
                        }
                    } else {
                        checkLastModifiedTimeInFields(describe.getTenantId(), Lists.newArrayList(quote));
                        if (quote.isIndex()) {
                            changeFieldList.add(quote);
                        }
                    }
                });

        if (CollectionUtils.notEmpty(changeFieldList)) {

            // 验证引用字段修改
            changeFieldList.forEach(quote -> validateQuoteFieldModification(describe.getTenantId(), quote));
            // 引用人员、部门字段。需要更新引用字段上的 is_single
            processPersonnelReferenceFields(describe, changeFieldList);
        }

        return Lists.newArrayList(changeFieldList);
    }

    /**
     * 处理人员和部门引用字段的 is_single
     *
     * @param describe        对象描述
     * @param changeFieldList 变更字段列表
     */
    private void processPersonnelReferenceFields(IObjectDescribe describe, List<Quote> changeFieldList) {
        // 构建需要查询的对象Map
        Map<String, List<QuoteExt>> objectQuoteMap = changeFieldList.stream()
                .filter(quote -> ObjectDescribeExt.EMPLOYEE_FIELD_TYPES.contains(quote.getQuoteFieldType())
                        || ObjectDescribeExt.DEPARTMENT_FIELD_TYPES.contains(quote.getQuoteFieldType()))
                .map(QuoteExt::of)
                .collect(Collectors.groupingBy(quoteExt -> {
                    Tuple<String, String> quoteField = quoteExt.parseQuoteField();
                    return quoteExt.getQuotedObjectApiName(describe, quoteField.getKey());
                }));

        if (CollectionUtils.empty(objectQuoteMap)) {
            return;
        }
        Map<String, IObjectDescribe> quotedDescribeMap = describeLogicService.findObjects(describe.getTenantId(),
                Lists.newArrayList(objectQuoteMap.keySet()));

        // 处理人员部门类型的引用字段
        objectQuoteMap.forEach((objectApiName, quotes) -> {
            IObjectDescribe quotedDescribe = quotedDescribeMap.get(objectApiName);
            if (Objects.isNull(quotedDescribe)) {
                log.warn("Quoted object describe not found. tenantId:{}, objectApiName:{}",
                        describe.getTenantId(), objectApiName);
                return;
            }

            quotes.forEach(quote -> {
                String fieldApiName = quote.parseQuoteField().getValue();
                IFieldDescribe quotedField = quotedDescribe.getFieldDescribe(fieldApiName);
                if (Objects.isNull(quotedField)) {
                    log.warn("Quoted field not found. tenantId:{}, objectApiName:{}, fieldApiName:{}",
                            describe.getTenantId(), objectApiName, fieldApiName);
                    return;
                }
                quote.syncSingleFlag(quotedField);
            });
        });
    }

    private void validateQuoteFieldModification(String tenantId, Quote fieldDescribe) {
        if (NOT_REPOSITORY_QUOTE_TYPE.contains(fieldDescribe.getQuoteFieldType())) {
            throw new ValidateException(I18N.text(I18NKey.QUOTE_FILED_MODIFY_VALIDATE_EXCEPTION_NEW));
        }
    }

    @Override
    public List<IFieldDescribe> checkQuoteFieldsByObjectDescribe(IObjectDescribe objectDescribe, IObjectDescribe describeInDb) {
        List<IFieldDescribe> quoteFields = objectDescribe.getFieldDescribes().stream()
                .filter(x -> FieldDescribeExt.of(x).isQuoteField())
                .collect(Collectors.toList());
        return checkQuoteFields(describeInDb, quoteFields);
    }

    @Override
    public void checkObjectReferenceField(User user, IObjectDescribe describe, IFieldDescribe fieldDescribe) {
        checkObjectReferenceFields(user, describe, Lists.newArrayList(fieldDescribe));
    }

    @Override
    public void cleanReferenceFieldRelation(User user, IObjectDescribe describe, IFieldDescribe fieldDescribe) {
        _cleanReferenceFieldRelation(user, describe, fieldDescribe);
    }

    private void _cleanReferenceFieldRelation(User user, IObjectDescribe describe, IFieldDescribe fieldDescribe) {
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
        if (fieldDescribeExt.isWhereRelation()) {
            List<ReferenceData> referenceDataList = _findWhereFunctionFilter(fieldDescribeExt).toReferenceData();
            functionLogicService.batchDeleteRelation(user, referenceDataList);
        }
        //删除计算字段的引用关系
        if (fieldDescribeExt.isFormula()) {
            deleteFormulaReference(user.getTenantId(), describe.getApiName(), Lists.newArrayList(fieldDescribe.getApiName()));
        }
    }

    @Override
    public List<ReferenceData> buildReferenceFieldRelation(User user, IFieldDescribe fieldDescribe) {
        List<ReferenceData> referenceDataList = Lists.newArrayList();
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
        if (fieldDescribeExt.isWhereRelation()) {
            referenceDataList.addAll(_findWhereFunctionFilter(fieldDescribeExt).toReferenceData());
            return referenceDataList;
        }
        if (fieldDescribeExt.isWhereRelation()) {
            String functionApiName = findWhereFunctionFilter(fieldDescribeExt);
            ReferenceData referenceData = ReferenceData.builder()
                    .sourceType(SourceTypes.SCOPE_RULE)
                    .sourceLabel(fieldDescribe.getLabel())
                    .sourceValue(fieldDescribe.getApiName())
                    .targetType(TargetTypes.FUNCTION)
                    .targetValue(functionApiName)
                    .build();
            referenceDataList.add(referenceData);
        }

        return referenceDataList;
    }

    @Override
    public void checkObjectReferenceFields(User user, IObjectDescribe describe, List<IFieldDescribe> fieldDescribes) {
        _checkObjectReferenceFields(user, describe, fieldDescribes);
    }

    private void _checkObjectReferenceFields(User user, IObjectDescribe describe, List<IFieldDescribe> fieldDescribes) {
        List<WhereRelation> updateWhereFunctions = Lists.newArrayList();
        List<WhereRelation> cleanWhereFunctions = Lists.newArrayList();
        fieldDescribes.stream()
                .map(FieldDescribeExt::of)
                .filter(FieldDescribeExt::isWhereRelation)
                .forEach(x -> {
                    x.setDescribeApiName(describe.getApiName());
                    IFieldDescribe oldField = ObjectDescribeExt.of(describe).getFieldDescribeSilently(x.getApiName()).orElse(null);
                    if (oldField != null) {
                        WhereRelation functionFilter = _findWhereFunctionFilter(x);
                        WhereRelation oldFunctionFilter = _findWhereFunctionFilter(FieldDescribeExt.of(oldField));
                        if (WhereRelation.equals(functionFilter, oldFunctionFilter)) {
                            return;
                        }
                        cleanWhereFunctions.add(oldFunctionFilter);
                        updateWhereFunctions.add(functionFilter);
                    } else {
                        WhereRelation functionFiler = _findWhereFunctionFilter(x);
                        updateWhereFunctions.add(functionFiler);
                    }
                });
        _updateFunctionUsedInfo(user, cleanWhereFunctions, true);
        _updateFunctionUsedInfo(user, updateWhereFunctions, false);

    }

    @Override
    public void checkObjectReferenceFieldsByObjectDescribe(User user, IObjectDescribe objectDescribe, IObjectDescribe describeInDb) {
        List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes().stream()
                .map(x -> FieldDescribeExt.of(x))
                .map(x -> (IFieldDescribe) x.getFieldDescribe())
                .collect(Collectors.toList());
        checkObjectReferenceFields(user, describeInDb, fieldDescribes);
    }

    @Override
    public void computeCalculateFieldsForMappingAction(User user, String buttonApiName, String originalObjectApiName, IObjectDescribe masterDescribe, List<IObjectDescribe> detailDescribes, IObjectData masterData,
                                                       Map<String, List<IObjectData>> detailDataMap, List<String> filterDetail) {
        computeCalculateFieldsForConvertAction(user, masterDescribe, detailDescribes, masterData, detailDataMap, filterDetail, () -> getIObjectMappingRuleInfo(user, buttonApiName, originalObjectApiName));
    }

    @Override
    public void computeCalculateFieldsForConvertAction(User user, IObjectDescribe masterDescribe, List<IObjectDescribe> detailDescribes, IObjectData masterData, Map<String, List<IObjectData>> detailDataMap, List<String> filterDetail, Supplier<List<IObjectMappingRuleInfo>> supplier) {
        List<IObjectDescribe> describeListForGraph = Lists.newArrayList(detailDescribes);
        describeListForGraph.add(masterDescribe);
        Map<String, List<RelateField>> calculateFieldMap = Maps.newHashMap();
        Set<FieldNode> fieldNodeSet = Sets.newHashSet();
        FieldRelationGraph graph = fieldRelationGraphService.buildReverseDependencyGraph(describeListForGraph, false,
                false, true);
        List<IObjectDescribe> filterDetailDescribes = detailDescribes.stream().filter(x -> filterDetail.contains(x.getApiName())).collect(Collectors.toList());
        filterDetailDescribes.forEach(detailDescribe -> {
            ObjectDescribeExt.of(masterDescribe).getCountFields(detailDescribe.getApiName()).forEach(count -> {
                graph.getNode(masterDescribe.getApiName(), count.getApiName()).ifPresent(fieldNode -> {
                    fieldNodeSet.add(fieldNode);
                    Set<FieldNode> reachNodes = graph.reachableNodes(fieldNode).stream()
                            .filter(x -> !x.isQuoteField())
                            .collect(Collectors.toSet());
                    fieldNodeSet.addAll(reachNodes);
                });
            });
        });
        if (CollectionUtils.notEmpty(fieldNodeSet)) {
            for (FieldNode fieldNode : fieldNodeSet) {
                String objectApiName = fieldNode.getObjectApiName();
                calculateFieldMap.putIfAbsent(objectApiName, Lists.newArrayList());
                calculateFieldMap.get(objectApiName).add(fieldNode.toRelateField());
            }
            CalculateFields calculateFields = CalculateFields.of(graph, calculateFieldMap);
            List<IObjectMappingRuleInfo> rules = supplier.get();
            Lists.newArrayList(calculateFields.getCalculateFieldMap().keySet()).forEach(objectApiName -> {
                Set<String> ruleFields = Sets.newHashSet();
                rules.stream().filter(a -> Objects.equals(a.getTargetApiName(), objectApiName)).forEach(rule -> {
                    List<IObjectMappingRuleDetailInfo> fieldMapping = rule.getFieldMapping();
                    fieldMapping.forEach(f -> ruleFields.add(f.getTargetFieldName()));
                });
                calculateFields.getCalculateFieldMap().get(objectApiName).removeIf(x -> ruleFields.contains(x.getFieldName()));
            });

            computeService.batchCalculateBySortFields(user, masterData, detailDataMap, calculateFields);
        }
    }

    @Override
    public void checkRecordLabelChange(IObjectDescribe oldDescribe, IRecordTypeOption option, boolean needDiff) {
        boolean changed = !needDiff;
        if (needDiff) {
            RecordTypeFieldDescribe fieldDescribe = (RecordTypeFieldDescribe) oldDescribe.getFieldDescribe(IFieldType.RECORD_TYPE);
            Optional<IRecordTypeOption> oldOption = fieldDescribe.getRecordTypeOptions().stream()
                    .filter(x -> StringUtils.equals(x.getApiName(), option.getApiName()))
                    .findFirst();
            changed = oldOption.isPresent() && !StringUtils.equals(option.getLabel(), oldOption.get().getLabel());
        }
        if (changed) {
            // 相关对象和从对象
            List<IObjectDescribe> relatedDescribes = describeLogicService.findRelatedDescribesWithoutCopyIfGray(oldDescribe.getTenantId(), oldDescribe.getApiName());

            List<FieldRelationGraphBuilder.GraphLayer> graphLayers = Lists.newArrayList();
            graphLayers.add(FieldRelationGraphBuilder.GraphLayer.of(Lists.newArrayList(oldDescribe)));
            graphLayers.add(FieldRelationGraphBuilder.GraphLayer.of(relatedDescribes));

            FieldRelationGraph graph = FieldRelationGraphBuilder.builder()
                    .describeLogicService(describeLogicService)
                    .graphLayers(graphLayers)
                    .excludeDefaultValue(true)
                    .fillFieldType(true)
                    .ignoreInvalidVariable(true)
                    .build()
                    .getGraph();

            Map<String, List<String>> fieldMap = Maps.newHashMap();
            graph.getNode(oldDescribe.getApiName(), IFieldType.RECORD_TYPE).ifPresent(recordTypeNode -> {
                graph.predecessors(recordTypeNode).stream().filter(node -> node.isFormula()).forEach(formulaNode -> {
                    Formula formulaField = (Formula) graph.getDescribeMap().get(formulaNode.getObjectApiName())
                            .getFieldDescribe(formulaNode.getFieldApiName());
                    if (formulaField == null
                            || (needDiff && !formulaField.getExpression().contains(IFieldType.RECORD_TYPE + Expression.LABEL))) {
                        return;
                    }
                    fieldMap.putIfAbsent(formulaNode.getObjectApiName(), Lists.newArrayList());
                    fieldMap.get(formulaNode.getObjectApiName()).add(formulaNode.getFieldApiName());
                });
            });

            if (CollectionUtils.notEmpty(fieldMap)) {
                User user = User.builder().tenantId(oldDescribe.getTenantId()).userId(User.SUPPER_ADMIN_USER_ID).build();
                //业务类型名称变更触发相关计算字段重新计算，已存历史数据全量计算，暂不处理历史数据范围
                jobScheduleService.submitCalculateJob(user, fieldMap);
            }
        }
    }

    private List<IObjectMappingRuleInfo> getIObjectMappingRuleInfo(User user, String buttonApiName, String originalObjectApiName) {
        IUdefButton button = buttonService.findButtonByApiName(user, buttonApiName, originalObjectApiName);
        if (Objects.isNull(button)) {
            throw new ValidateException(I18N.text(I18NKey.BUTTON_IS_DISABLE_OR_DELETE));
        }
        List<IUdefAction> actionList = actionService.findActionList(user, button, originalObjectApiName);
        List<IObjectMappingRuleInfo> rules = Lists.newArrayList();
        actionList.stream().filter(x -> StringUtils.equals("", x.getActionType()))
                .forEach(x -> {
                    String actionParameter = x.getActionParamter();
                    ObjectMappingExt.ObjectMappingActionParam paramMap = JSON.parseObject(actionParameter, ObjectMappingExt.ObjectMappingActionParam.class);
                    String ruleApiName = paramMap.getObjectMappingApiName();
                    List<IObjectMappingRuleInfo> list = objectMappingService.findByApiName(user, ruleApiName);
                    if (CollectionUtils.notEmpty(list)) {
                        rules.addAll(list);
                    }
                });
        return rules;
    }

    private void validateCountFieldNode(Set<String> apiNamesInPage, FieldRelationGraph graph, FieldNode node) {
        //为了兼容660的业务，暂时放开对销售订单的限制
        if (ObjectAPINameMapping.SalesOrder.getApiName().equals(node.getObjectApiName())) {
            return;
        }
        Count count = (Count) graph.getDescribeMap().get(node.getObjectApiName()).getFieldDescribe(node.getFieldApiName());
        if (apiNamesInPage.contains(count.getSubObjectDescribeApiName())) {
            IObjectDescribe describe = graph.getDescribeMap().get(node.getObjectApiName());
            String label = describe.getDisplayName();
            label = label + "." + describe.getFieldDescribe(node.getFieldApiName()).getLabel();
            throw new ValidateException(I18N.text(I18NKey.VALIDATE_RULE_VALIDATE_EXCEPTION, label));
        }
    }

    private String findWhereFunctionFilter(FieldDescribeExt fieldDescribe) {
        int functionType = 9;
        String functionApiName = "";
        List<Wheres> wheres = fieldDescribe.get(ObjectReferenceFieldDescribe.WHERES, List.class);
        if (CollectionUtils.notEmpty(wheres)) {
            for (Wheres where : wheres) {
                List<IFilter> filters = where.getFilters();
                List<IFilter> filterList = filters.stream().filter(y -> Objects.equals(y.getValueType(), functionType)).collect(Collectors.toList());
                if (CollectionUtils.notEmpty(filterList)) {
                    if (filterList.size() > 1) {
                        throw new ValidateException(I18N.text(I18NKey.OBJECT_REFERENCE_FUNCTION_VALIDATE));
                    }
                    functionApiName = filterList.get(0).getFieldValues().get(0);
                    break;
                }
            }
        }
        return functionApiName;
    }

    private WhereRelation _findWhereFunctionFilter(FieldDescribeExt fieldDescribe) {
        WhereRelation.WhereRelationBuilder functionRelationBuilder = WhereRelation.builder()
                .fieldDescribe(fieldDescribe);
        if (CollectionUtils.notEmpty(fieldDescribe.getWheres()) && CollectionUtils.notEmpty(fieldDescribe.getWheres().get(0).getFilters())) {
            List whereList = fieldDescribe.get(ObjectReferenceFieldDescribe.WHERES, List.class);
            Wheres wheres = (Wheres) ObjectDescribeExt.getWheresBy(whereList).get(0);
            List<IFilter> filters = wheres.getFilters().stream()
                    .filter(it -> Objects.equals(it.getValueType(), FilterExt.FilterValueTypes.FUNCTION_VARIABLE))
                    .collect(Collectors.toList());
            if (filters.size() > 1) {
                throw new ValidateException(I18N.text(I18NKey.OBJECT_REFERENCE_FUNCTION_VALIDATE));
            }
            if (CollectionUtils.notEmpty(filters)) {
                String functionApiName = filters.get(0).getFieldValues().get(0);
                functionRelationBuilder.whereFunctionApiName(functionApiName);
            }
        }
        if (CollectionUtils.notEmpty(fieldDescribe.getRelatedWheres()) && CollectionUtils.notEmpty(fieldDescribe.getRelatedWheres().get(0).getFilters())) {
            List wheres = fieldDescribe.get(ObjectReferenceFieldDescribe.RELATION_WHERES, List.class);
            Wheres relatedWhere = (Wheres) ObjectDescribeExt.getWheresBy(wheres).get(0);
            List<IFilter> filters = relatedWhere.getFilters().stream()
                    .filter(it -> Objects.equals(it.getValueType(), FilterExt.FilterValueTypes.FUNCTION_VARIABLE))
                    .collect(Collectors.toList());
            if (filters.size() > 1) {
                throw new ValidateException(I18N.text(I18NKey.OBJECT_REFERENCE_FUNCTION_VALIDATE));
            }
            if (CollectionUtils.notEmpty(filters)) {
                String functionApiName = filters.get(0).getFieldValues().get(0);
                functionRelationBuilder.relatedWhereFunctionApiName(functionApiName);
            }
        }
        return functionRelationBuilder.build();
    }

    private void _updateFunctionUsedInfo(User user, List<WhereRelation> whereRelations, boolean isNeedClean) {
        if (CollectionUtils.empty(whereRelations)) {
            return;
        }
        IActionContext actionContext = new ActionContext();
        actionContext.setUserId(user.getUserId());
        String status = isNeedClean ? "not_used" : "used";

        for (WhereRelation relation : whereRelations) {
            List<ReferenceData> referenceDataList = relation.toReferenceData();
            if (CollectionUtils.empty(referenceDataList)) {
                continue;
            }
            if (isNeedClean) {
                _updateOldFunctionUserInfo(user, relation, actionContext, status);
                functionLogicService.batchDeleteRelation(user, referenceDataList);
            } else {
                functionLogicService.saveRelation(user, referenceDataList);
            }
            logUdefFunction(user, relation);
        }
    }

    private void logUdefFunction(User user, WhereRelation relation) {
        if (!Strings.isNullOrEmpty(relation.getWhereFunctionApiName())) {
            logService.logUdefFunction(user, EventType.MODIFY, ActionType.UPDATE_FUNCTION, relation.getDescribeApiName(), relation.getWhereFunctionApiName(), null);
        }
        if (!Strings.isNullOrEmpty(relation.getRelatedWhereFunctionApiName())) {
            logService.logUdefFunction(user, EventType.MODIFY, ActionType.UPDATE_FUNCTION, relation.getDescribeApiName(), relation.getRelatedWhereFunctionApiName(), null);
        }
    }

    private void updateOldFunctionUserInfo(User user, String fieldApiName, String functionApiName, String describeAPiName, IActionContext actionContext, String status) {
        IUdefFunction function = udefFunctionService.findFunctionByApiName(user.getTenantId(), functionApiName, describeAPiName);
        if (function == null) {
            return;
        }
        Map userInfo = function.getUsedInfo();
        if (function != null && CollectionUtils.notEmpty(userInfo) && userInfo.get("api_name") != null
                && fieldApiName.equals(userInfo.get("api_name").toString())) {
            udefFunctionService.updateUdefFunctionStatus(user.getTenantId(), functionApiName,
                    describeAPiName, status, Maps.newHashMap(), actionContext);

        }
    }

    private ReferenceData buildReferenceData(IFieldDescribe fieldDescribe, String functionApiName) {
        ReferenceData referenceData = ReferenceData.builder()
                .sourceType(SourceTypes.SCOPE_RULE)
                .sourceLabel(fieldDescribe.getLabel())
                .sourceValue(fieldDescribe.getApiName())
                .targetType(TargetTypes.FUNCTION)
                .targetValue(functionApiName).build();
        return referenceData;
    }

    private void _updateOldFunctionUserInfo(User user, WhereRelation relation, IActionContext actionContext, String status) {
        if (!Strings.isNullOrEmpty(relation.getWhereFunctionApiName())) {
            updateOldFunctionUserInfo(user, relation.getFieldApiName(), relation.getWhereFunctionApiName(), relation.getDescribeApiName(), actionContext, status);
        }
        if (!Strings.isNullOrEmpty(relation.getRelatedWhereFunctionApiName())) {
            updateOldFunctionUserInfo(user, relation.getFieldApiName(), relation.getRelatedWhereFunctionApiName(), relation.getDescribeApiName(), actionContext, status);
        }
    }

    @Data
    @Builder
    public static class WhereRelation {
        private FieldDescribeExt fieldDescribe;
        private String whereFunctionApiName;
        private String relatedWhereFunctionApiName;

        public static boolean equals(WhereRelation a, WhereRelation b) {
            if (Objects.equals(a, b)) {
                return true;
            }
            return Objects.equals(a.getFieldApiName(), b.getFieldApiName())
                    && Objects.equals(a.getDescribeApiName(), b.getDescribeApiName())
                    && Objects.equals(a.getWhereFunctionApiName(), b.getWhereFunctionApiName())
                    && Objects.equals(a.getRelatedWhereFunctionApiName(), b.getRelatedWhereFunctionApiName());
        }

        public boolean isEmpty() {
            return Strings.isNullOrEmpty(whereFunctionApiName) && Strings.isNullOrEmpty(relatedWhereFunctionApiName);
        }

        public List<ReferenceData> toReferenceData() {
            if (isEmpty()) {
                return Lists.newArrayList();
            }
            return Stream.of(toWhereReferenceData(), toRelatedWhereReferenceData())
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }

        public ReferenceData toWhereReferenceData() {
            if (Strings.isNullOrEmpty(whereFunctionApiName)) {
                return null;
            }
            return ReferenceData.builder()
                    .sourceType(SourceTypes.SCOPE_RULE)
                    .sourceLabel(fieldDescribe.getLabel())
                    .sourceValue(fieldDescribe.getApiName())
                    .targetType(TargetTypes.FUNCTION)
                    .targetValue(whereFunctionApiName)
                    .build();
        }

        public ReferenceData toRelatedWhereReferenceData() {
            if (Strings.isNullOrEmpty(relatedWhereFunctionApiName)) {
                return null;
            }
            return ReferenceData.builder()
                    .sourceType(SourceTypes.DESCRIBE_FIELD)
                    .sourceLabel(fieldDescribe.getLabel())
                    .sourceValue(String.format("%s.%s", fieldDescribe.getDescribeApiName(), fieldDescribe.getApiName()))
                    .targetType(TargetTypes.FUNCTION)
                    .targetValue(relatedWhereFunctionApiName)
                    .build();
        }

        public String getFieldApiName() {
            return fieldDescribe.getApiName();
        }

        public String getDescribeApiName() {
            return fieldDescribe.getDescribeApiName();
        }
    }
}
