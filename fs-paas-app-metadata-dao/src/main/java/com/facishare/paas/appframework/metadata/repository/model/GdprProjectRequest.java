package com.facishare.paas.appframework.metadata.repository.model;

import com.facishare.paas.appframework.metadata.repository.annotation.Entity;
import com.facishare.paas.appframework.metadata.repository.annotation.ObjectField;
import com.facishare.paas.appframework.metadata.repository.annotation.TextField;
import com.facishare.paas.appframework.metadata.repository.annotation.TrueOrFalseField;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2021/5/8 16:35
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity(apiName = GdprProjectRequest.GDPR_PROJECT_REQUEST_OBJ)
public class GdprProjectRequest extends BaseEntity {
    public static final String GDPR_PROJECT_REQUEST_OBJ = "GdprProjectRequestObj";

    public static final String GDPR_PROJECT_REQUEST_DATA_ID = "data_id";

    public static final String GDPR_PROJECT_REQUEST_API_NAME = "api_name";

    public static final String GDPR_PROJECT_REQUEST_TYPE = "type";

    public static final String GDPR_PROJECT_REQUEST_TYPE_QUERY = "query";
    public static final String GDPR_PROJECT_REQUEST_TYPE_EXPORT = "export";
    public static final String GDPR_PROJECT_REQUEST_TYPE_STOP = "stop";
    public static final String GDPR_PROJECT_REQUEST_TYPE_DELETE = "delete";
    public static final String GDPR_PROJECT_REQUEST_TYPE_UPDATE = "update";

    public static final String GDPR_PROJECT_REQUEST_TYPE_STATUS_OPEN = "open";
    public static final String GDPR_PROJECT_REQUEST_TYPE_STATUS_CLOSE = "close";
    public static final String GDPR_PROJECT_REQUEST_TYPE_STATUS_LOCK = "lock";
    public static final String GDPR_PROJECT_REQUEST_STATUS = "status";


    @TrueOrFalseField(field = @ObjectField(apiName = IObjectData.IS_DELETED, label = "is_deleted"))
    private boolean deleted;

    @TextField(field = @ObjectField(apiName = IObjectDescribe.API_NAME, label = "对象apiName"))
    private String apiName;

    @TextField(field = @ObjectField(apiName = "data_id", label = "数据id"))
    private String dataId;

    @TextField(field = @ObjectField(apiName = "status", label = "数据项目请求状态"))
    private String status;

    @TextField(field = @ObjectField(apiName = "type", label = "数据项目请求类型"))
    private String type;
}
