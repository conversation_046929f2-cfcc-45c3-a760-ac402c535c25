package com.facishare.paas.appframework.metadata.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public interface MultiDuplicateSearchResult {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Arg {
        String tenantId;
        String describeApiName;
        IDuplicatedSearch.Type type;
        List<String> ruleApiNameList;
        private Map<String, Object> objectData;
        @JSONField(name = "isIdValue")
        boolean isIdValue;
        boolean removeSelf = false;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    class Result {
        private String code;
        DataResult result;
        String message;
        public boolean success() {
            return "200".equals(code);
        }

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class DataResult {
        String ruleApiName;
        List<DuplicateDataList> duplicateDataList = Lists.newArrayList();
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    class DuplicateDataList {
        //查重规则apiName
        String apiName;
        List<String> dataIds;
    }


}
