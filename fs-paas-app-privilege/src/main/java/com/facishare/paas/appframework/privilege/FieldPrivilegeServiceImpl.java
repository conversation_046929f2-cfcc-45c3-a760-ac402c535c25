package com.facishare.paas.appframework.privilege;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.privilege.util.AuthContextExt;
import com.facishare.paas.auth.common.exception.AuthException;
import com.facishare.paas.auth.model.AuthContext;
import com.fxiaoke.paas.auth.factory.FieldClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by luxin on 2018/4/24.
 */
@Service("fieldPrivilegeService")
public class FieldPrivilegeServiceImpl implements FieldPrivilegeService {
    @Autowired
    private FieldClient fieldClient;

    @Override
    public Map<String, Map<String, Integer>> listUserEntitiesFiledPrivilege(String tenantId, String userId, List<String> apiNames) {
        AuthContext authContext = buildAuthContext(new User(tenantId, userId));
        try {
            return fieldClient.userEntityIdsFieldPermission(authContext, Sets.newHashSet(apiNames));
        } catch (AuthException e) {
            throw new PermissionError(I18N.text(I18NKey.PERMISSION_ERROR), e.getCode());
        }
    }

    private com.facishare.paas.auth.model.AuthContext buildAuthContext(User user) {
        return com.facishare.paas.auth.model.AuthContext.builder()
                .appId(AuthContextExt.getAppId(user))
                .tenantId(user.getTenantId())
                .userId(user.isOutUser() ? user.getOutUserId() : user.getUserId())
                .outerTenantId(user.isOutUser() ? user.getOutTenantId() : null)
                .identityType(RequestUtil.getOutIdentityType())
                .properties(AuthContextExt.buildProperties(user))
                .build();
    }


    @Override
    public Map<String, Integer> getUserFieldPrivilege(String tenantId, String userId, String apiName) {
        return getUserFieldPrivilege(new User(tenantId, userId), apiName);
    }

    @Override
    public Map<String, Integer> getUserFieldPrivilege(User user, String apiName) {
        AuthContext authContext = buildAuthContext(user);
        try {
            return fieldClient.userFieldPermission(authContext, apiName);
        } catch (AuthException e) {
            throw new PermissionError(I18N.text(I18NKey.PERMISSION_ERROR), e.getCode());
        }
    }

    @Override
    public Map<String, Set<String>> getUserNoExportFieldPrivilege(User user, Set<String> objectApiNames) {
        AuthContext authContext = buildAuthContext(user);
        try {
            return fieldClient.notSupportExportFields(authContext, Lists.newArrayList(objectApiNames));
        } catch (AuthException e) {
            throw new PermissionError(I18N.text(I18NKey.PERMISSION_ERROR), e.getCode());
        }
    }
}
