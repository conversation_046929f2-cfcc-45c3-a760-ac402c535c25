package com.facishare.paas.appframework.core.model.handler;

/**
 * Created by zhouwr on 2023/2/15.
 */
public interface HandlerAttributes {
    /**
     * ==================common===================
     */
    String SKIP_FUNCTION_PRIVILEGE_CHECK = "skipFunctionPrivilegeCheck";

    /**
     * ==================action===================
     */
    String SKIP_DATA_PRIVILEGE_CHECK = "skipDataPrivilegeCheck";
    String SKIP_APPROVAL_FLOW = "skipApprovalFlow";
    String SKIP_RECORD_LOG = "skipRecordLog";

    /**
     * ==================Add和Edit===================
     */
    String SKIP_DUPLICATE_SEARCH_CHECK = "skipDuplicateSearchCheck";
    String SKIP_VALIDATION_FUNCTION_CHECK = "skipValidationFunctionCheck";
    String SKIP_VALIDATION_RULE_CHECK = "skipValidationRuleCheck";
    String SKIP_UNIQUE_RULE_CHECK = "skipUniqueRuleCheck";

    String IS_CURRENCY_EMPTY = "isCurrencyEmpty";
}
