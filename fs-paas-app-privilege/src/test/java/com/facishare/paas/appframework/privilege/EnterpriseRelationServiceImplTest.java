package com.facishare.paas.appframework.privilege;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.privilege.util.AppIdUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.support.GDSHandler;
import com.fxiaoke.enterpriserelation2.arg.BatchGetRelationDownstreamAndOwnerArg;
import com.fxiaoke.enterpriserelation2.arg.GetMapperObjectOwnerIdArg;
import com.fxiaoke.enterpriserelation2.arg.UpstreamAndDownstreamOuterTenantIdOutArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.RelationDownstreamResult;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationObjService;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试内容描述：测试EnterpriseRelationServiceImpl类的方法
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class EnterpriseRelationServiceImplTest {

    @Mock
    private EnterpriseRelationService enterpriseRelationService;
    
    @Mock
    private EnterpriseRelationObjService enterpriseRelationObjService;
    
    @Mock
    private GDSHandler gdsHandler;
    
    @InjectMocks
    private EnterpriseRelationServiceImpl enterpriseRelationServiceImpl;

    /**
     * GenerateByAI
     * 测试内容描述：测试getRelationDownstreamInfo方法，使用默认参数版本
     */
    @Test
    @DisplayName("正常场景 - 测试getRelationDownstreamInfo方法使用默认参数")
    void testGetRelationDownstreamInfo_WithDefaultParameters() {
        try (MockedStatic<HeaderObj> headerObjMock = mockStatic(HeaderObj.class)) {
            // 准备测试数据
            String tenantId = "123456";
            Set<String> partnerIdList = new HashSet<>(Arrays.asList("partner1", "partner2"));

            Map<String, RelationDownstreamResult> expectedData = new HashMap<>();
            expectedData.put("partner1", createRelationDownstreamResult(10001, 20001L));
            expectedData.put("partner2", createRelationDownstreamResult(10002, 20002L));
            
            RestResult<Map<String, RelationDownstreamResult>> mockResult = createSuccessRestResult(expectedData);
            HeaderObj mockHeaderObj = mock(HeaderObj.class);
            headerObjMock.when(() -> HeaderObj.newInstance(123456)).thenReturn(mockHeaderObj);
            
            // 配置Mock行为
            when(enterpriseRelationService.batchGetRelationDownstreamAndOwner(any(HeaderObj.class), any(BatchGetRelationDownstreamAndOwnerArg.class)))
                .thenAnswer(invocation -> {
                    BatchGetRelationDownstreamAndOwnerArg arg = invocation.getArgument(1);
                    assertEquals(Utils.PARTNER_API_NAME, arg.getObjectApiName());
                    assertEquals(123456, arg.getUpstreamTenantId());
                    assertTrue(arg.getCrmDataIds().containsAll(partnerIdList));
                    return mockResult;
                });
            
            // 执行被测试方法
            Map<String, RelationDownstreamResult> result = enterpriseRelationServiceImpl.getRelationDownstreamInfo(tenantId, partnerIdList);
            
            // 验证结果
            assertEquals(expectedData, result);
            verify(enterpriseRelationService).batchGetRelationDownstreamAndOwner(any(HeaderObj.class), any(BatchGetRelationDownstreamAndOwnerArg.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRelationDownstreamInfo方法，使用指定对象API名称版本
     */
    @Test
    @DisplayName("正常场景 - 测试getRelationDownstreamInfo方法使用指定对象API名称")
    void testGetRelationDownstreamInfo_WithObjectApiName() {
        try (MockedStatic<HeaderObj> headerObjMock = mockStatic(HeaderObj.class);
             MockedStatic<Lists> listsMock = mockStatic(Lists.class)) {
            
            // 准备测试数据
            String tenantId = "123456";
            String objectApiName = "CustomObj";
            Collection<String> objectDataIds = Arrays.asList("data1", "data2");

            Map<String, RelationDownstreamResult> expectedData = new HashMap<>();
            expectedData.put("data1", createRelationDownstreamResult(10001, 20001L));
            expectedData.put("data2", createRelationDownstreamResult(10002, 20002L));
            
            RestResult<Map<String, RelationDownstreamResult>> mockResult = createSuccessRestResult(expectedData);
            HeaderObj mockHeaderObj = mock(HeaderObj.class);
            headerObjMock.when(() -> HeaderObj.newInstance(123456)).thenReturn(mockHeaderObj);
            listsMock.when(() -> Lists.newArrayList(objectDataIds)).thenReturn(new ArrayList<>(objectDataIds));
            
            // 配置Mock行为
            when(enterpriseRelationService.batchGetRelationDownstreamAndOwner(any(HeaderObj.class), any(BatchGetRelationDownstreamAndOwnerArg.class)))
                .thenAnswer(invocation -> {
                    BatchGetRelationDownstreamAndOwnerArg arg = invocation.getArgument(1);
                    assertEquals(objectApiName, arg.getObjectApiName());
                    assertEquals(123456, arg.getUpstreamTenantId());
                    assertTrue(arg.getCrmDataIds().containsAll(objectDataIds));
                    return mockResult;
                });
            
            // 执行被测试方法
            Map<String, RelationDownstreamResult> result = enterpriseRelationServiceImpl.getRelationDownstreamInfo(tenantId, objectApiName, objectDataIds);
            
            // 验证结果
            assertEquals(expectedData, result);
            verify(enterpriseRelationService).batchGetRelationDownstreamAndOwner(any(HeaderObj.class), any(BatchGetRelationDownstreamAndOwnerArg.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRelationDownstreamInfo方法，接口调用失败的情况
     */
    @Test
    @DisplayName("异常场景 - 测试getRelationDownstreamInfo方法接口调用失败")
    void testGetRelationDownstreamInfo_ServiceCallFailed() {
        try (MockedStatic<HeaderObj> headerObjMock = mockStatic(HeaderObj.class);
             MockedStatic<Maps> mapsMock = mockStatic(Maps.class)) {
            
            // 准备测试数据
            String tenantId = "123456";
            Set<String> partnerIdList = new HashSet<>(Arrays.asList("partner1", "partner2"));
            
            RestResult<Map<String, RelationDownstreamResult>> mockResult = createFailedRestResult(204, "接口调用失败");
            HeaderObj mockHeaderObj = mock(HeaderObj.class);
            headerObjMock.when(() -> HeaderObj.newInstance(123456)).thenReturn(mockHeaderObj);
            mapsMock.when(Maps::newHashMap).thenReturn(new HashMap<>());
            
            // 配置Mock行为
            when(enterpriseRelationService.batchGetRelationDownstreamAndOwner(any(HeaderObj.class), any(BatchGetRelationDownstreamAndOwnerArg.class)))
                .thenReturn(mockResult);
            
            // 执行被测试方法
            Map<String, RelationDownstreamResult> result = enterpriseRelationServiceImpl.getRelationDownstreamInfo(tenantId, partnerIdList);
            
            // 验证结果 - 失败应返回空Map
            assertTrue(result.isEmpty());
            verify(enterpriseRelationService).batchGetRelationDownstreamAndOwner(any(HeaderObj.class), any(BatchGetRelationDownstreamAndOwnerArg.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRelationDownstreamInfo方法，返回结果为null的情况
     */
    @Test
    @DisplayName("异常场景 - 测试getRelationDownstreamInfo方法返回null")
    void testGetRelationDownstreamInfo_NullResult() {
        try (MockedStatic<HeaderObj> headerObjMock = mockStatic(HeaderObj.class);
             MockedStatic<Maps> mapsMock = mockStatic(Maps.class)) {
            
            // 准备测试数据
            String tenantId = "123456";
            Set<String> partnerIdList = new HashSet<>(Arrays.asList("partner1", "partner2"));
            
            HeaderObj mockHeaderObj = mock(HeaderObj.class);
            headerObjMock.when(() -> HeaderObj.newInstance(123456)).thenReturn(mockHeaderObj);
            mapsMock.when(Maps::newHashMap).thenReturn(new HashMap<>());
            
            // 配置Mock行为 - 返回null
            when(enterpriseRelationService.batchGetRelationDownstreamAndOwner(any(HeaderObj.class), any(BatchGetRelationDownstreamAndOwnerArg.class)))
                .thenReturn(null);
            
            // 执行被测试方法
            Map<String, RelationDownstreamResult> result = enterpriseRelationServiceImpl.getRelationDownstreamInfo(tenantId, partnerIdList);
            
            // 验证结果 - null应返回空Map
            assertTrue(result.isEmpty());
            verify(enterpriseRelationService).batchGetRelationDownstreamAndOwner(any(HeaderObj.class), any(BatchGetRelationDownstreamAndOwnerArg.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试fillOutEnterpriseInfo方法正常场景
     */
    @Test
    @DisplayName("正常场景 - 测试fillOutEnterpriseInfo方法")
    void testFillOutEnterpriseInfo_NormalCase() {
        try (MockedStatic<ObjectDescribeExt> objectDescribeExtMock = mockStatic(ObjectDescribeExt.class);
             MockedStatic<ObjectDataExt> objectDataExtMock = mockStatic(ObjectDataExt.class)) {

            // 准备测试数据
            User user = User.builder().tenantId("123456").userId("78910").build();
            IObjectDescribe describe = mock(IObjectDescribe.class);
            ObjectDescribeExt describeExt = mock(ObjectDescribeExt.class);

            IObjectData objectData1 = mock(IObjectData.class);
            IObjectData objectData2 = mock(IObjectData.class);
            ObjectDataExt objectDataExt1 = mock(ObjectDataExt.class);
            ObjectDataExt objectDataExt2 = mock(ObjectDataExt.class);

            List<IObjectData> dataList = Arrays.asList(objectData1, objectData2);

            // 配置Mock行为
            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(describe)).thenReturn(describeExt);
            when(describeExt.isPRMEnabled()).thenReturn(true);
            // user.isOutUser()是基于outTenantId和outUserId判断的，不需要Mock

            objectDataExtMock.when(() -> ObjectDataExt.of(objectData1)).thenReturn(objectDataExt1);
            objectDataExtMock.when(() -> ObjectDataExt.of(objectData2)).thenReturn(objectDataExt2);

            when(objectDataExt1.getPartnerId()).thenReturn("partner1");
            when(objectDataExt2.getPartnerId()).thenReturn("partner2");

            // Mock getRelationDownstreamInfo方法
            EnterpriseRelationServiceImpl spyService = spy(enterpriseRelationServiceImpl);
            Map<String, RelationDownstreamResult> relationResult = new HashMap<>();
            relationResult.put("partner1", createRelationDownstreamResult(10001, 20001L));
            relationResult.put("partner2", createRelationDownstreamResult(10002, 20002L));
            doReturn(relationResult).when(spyService).getRelationDownstreamInfo(eq("123456"), any(Set.class));

            // 执行被测试方法
            spyService.fillOutEnterpriseInfo(user, dataList, describe);

            // 验证Mock交互
            verify(objectDataExt1).setOutTenantId("10001");
            verify(objectDataExt1).setOutOwner(Lists.newArrayList("20001"));
            verify(objectDataExt1).synchronizeOutTeamMemberOwner("10001", "20001");

            verify(objectDataExt2).setOutTenantId("10002");
            verify(objectDataExt2).setOutOwner(Lists.newArrayList("20002"));
            verify(objectDataExt2).synchronizeOutTeamMemberOwner("10002", "20002");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试fillOutEnterpriseInfo方法，PRM未启用的情况
     */
    @Test
    @DisplayName("边界场景 - 测试fillOutEnterpriseInfo方法PRM未启用")
    void testFillOutEnterpriseInfo_PRMDisabled() {
        try (MockedStatic<ObjectDescribeExt> objectDescribeExtMock = mockStatic(ObjectDescribeExt.class)) {

            // 准备测试数据
            User user = User.builder().tenantId("123456").userId("78910").build();
            IObjectDescribe describe = mock(IObjectDescribe.class);
            ObjectDescribeExt describeExt = mock(ObjectDescribeExt.class);
            List<IObjectData> dataList = Arrays.asList(mock(IObjectData.class));

            // 配置Mock行为
            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(describe)).thenReturn(describeExt);
            when(describeExt.isPRMEnabled()).thenReturn(false);

            // 执行被测试方法
            enterpriseRelationServiceImpl.fillOutEnterpriseInfo(user, dataList, describe);

            // 验证 - PRM未启用时应该直接返回，不进行后续操作
            verify(describeExt).isPRMEnabled();
            verifyNoMoreInteractions(describeExt);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试fillOutEnterpriseInfo方法，用户是外部用户的情况
     */
    @Test
    @DisplayName("边界场景 - 测试fillOutEnterpriseInfo方法用户是外部用户")
    void testFillOutEnterpriseInfo_OutUser() {
        try (MockedStatic<ObjectDescribeExt> objectDescribeExtMock = mockStatic(ObjectDescribeExt.class)) {

            // 准备测试数据
            User user = User.builder().tenantId("123456").userId("78910").outTenantId("10001").outUserId("20001").build();
            IObjectDescribe describe = mock(IObjectDescribe.class);
            ObjectDescribeExt describeExt = mock(ObjectDescribeExt.class);
            List<IObjectData> dataList = Arrays.asList(mock(IObjectData.class));

            // 配置Mock行为
            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(describe)).thenReturn(describeExt);
            when(describeExt.isPRMEnabled()).thenReturn(true);

            // 执行被测试方法
            enterpriseRelationServiceImpl.fillOutEnterpriseInfo(user, dataList, describe);

            // 验证 - 外部用户时应该直接返回，不进行后续操作
            verify(describeExt).isPRMEnabled();
            verifyNoMoreInteractions(describeExt);
        }
    }

    // 辅助方法
    private RelationDownstreamResult createRelationDownstreamResult(Integer downstreamOuterTenantId, Long relationOwnerOuterUid) {
        RelationDownstreamResult result = new RelationDownstreamResult();
        result.setDownstreamOuterTenantId(downstreamOuterTenantId);
        result.setRelationOwnerOuterUid(relationOwnerOuterUid);
        return result;
    }

    private <T> RestResult<T> createSuccessRestResult(T data) {
        // 使用Mock对象来模拟RestResult
        RestResult<T> result = mock(RestResult.class);
        when(result.isSuccess()).thenReturn(true);
        when(result.getData()).thenReturn(data);
        return result;
    }

    private <T> RestResult<T> createFailedRestResult(int errCode, String errMsg) {
        // 使用Mock对象来模拟RestResult
        RestResult<T> result = mock(RestResult.class);
        when(result.isSuccess()).thenReturn(false);
        // 只有在测试中实际使用时才配置这些方法
        return result;
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getUpstreamMapperObjectId方法正常场景
     */
    @Test
    @DisplayName("正常场景 - 测试getUpstreamMapperObjectId方法")
    void testGetUpstreamMapperObjectId_NormalCase() {
        try (MockedStatic<HeaderObj> headerObjMock = mockStatic(HeaderObj.class)) {
            // 准备测试数据
            User outUser = User.builder()
                .tenantId("123456")
                .userId("78910")
                .outTenantId("10001")
                .outUserId("20001")
                .build();
            String apiName = "CustomObj";
            String ea = "test-ea";
            String expectedResult = "mapped-object-id";

            RestResult<String> mockResult = createSuccessRestResult(expectedResult);

            HeaderObj mockHeaderObj = mock(HeaderObj.class);
            headerObjMock.when(() -> HeaderObj.newInstance(123456, 10001L, 20001L)).thenReturn(mockHeaderObj);

            // 配置Mock行为
            when(gdsHandler.getEAByEI("123456")).thenReturn(ea);
            when(enterpriseRelationService.getMapperObjectId(any(HeaderObj.class), any(UpstreamAndDownstreamOuterTenantIdOutArg.class)))
                .thenAnswer(invocation -> {
                    UpstreamAndDownstreamOuterTenantIdOutArg arg = invocation.getArgument(1);
                    assertEquals(10001L, arg.getDownstreamOuterTenantId());
                    assertEquals(apiName, arg.getObjectApiName());
                    assertEquals(ea, arg.getUpstreamEa());
                    return mockResult;
                });

            // 执行被测试方法
            String result = enterpriseRelationServiceImpl.getUpstreamMapperObjectId(outUser, apiName);

            // 验证结果
            assertEquals(expectedResult, result);
            verify(gdsHandler).getEAByEI("123456");
            verify(enterpriseRelationService).getMapperObjectId(any(HeaderObj.class), any(UpstreamAndDownstreamOuterTenantIdOutArg.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMapperObjectOwnerId方法正常场景
     */
    @Test
    @DisplayName("正常场景 - 测试getMapperObjectOwnerId方法")
    void testGetMapperObjectOwnerId_NormalCase() {
        try (MockedStatic<HeaderObj> headerObjMock = mockStatic(HeaderObj.class)) {
            // 准备测试数据
            User user = User.builder()
                .tenantId("123456")
                .userId("78910")
                .outTenantId("10001")
                .outUserId("20001")
                .build();
            String appId = "test-app";

            RestResult<Integer> mockResult = mock(RestResult.class);
            when(mockResult.isSuccess()).thenReturn(true);
            when(mockResult.getData()).thenReturn(30001);

            HeaderObj mockHeaderObj = mock(HeaderObj.class);
            headerObjMock.when(() -> HeaderObj.newInstance(123456, 10001L, 20001L)).thenReturn(mockHeaderObj);

            // 配置Mock行为
            when(enterpriseRelationService.getMapperObjectOwnerId(any(HeaderObj.class), any(GetMapperObjectOwnerIdArg.class)))
                .thenAnswer(invocation -> {
                    GetMapperObjectOwnerIdArg arg = invocation.getArgument(1);
                    assertEquals(appId, arg.getFsAppId());
                    assertEquals(10001L, arg.getDownstreamOuterTenantId());
                    assertEquals(123456, arg.getUpstreamTenantId());
                    return mockResult;
                });

            // 执行被测试方法
            String result = enterpriseRelationServiceImpl.getMapperObjectOwnerId(user, appId);

            // 验证结果
            assertEquals("30001", result);
            verify(enterpriseRelationService).getMapperObjectOwnerId(any(HeaderObj.class), any(GetMapperObjectOwnerIdArg.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMapperObjectOwnerId方法，用户不是外部用户
     */
    @Test
    @DisplayName("边界场景 - 测试getMapperObjectOwnerId方法用户不是外部用户")
    void testGetMapperObjectOwnerId_NonOutUser() {
        // 准备测试数据
        User user = User.builder()
            .tenantId("123456")
            .userId("78910")
            .build();
        String appId = "test-app";

        // 执行被测试方法
        String result = enterpriseRelationServiceImpl.getMapperObjectOwnerId(user, appId);

        // 验证结果 - 非外部用户应返回null
        assertNull(result);
        verifyNoInteractions(enterpriseRelationService);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMapperObjectOwnerId方法，接口调用失败
     */
    @Test
    @DisplayName("异常场景 - 测试getMapperObjectOwnerId方法接口调用失败")
    void testGetMapperObjectOwnerId_ServiceCallFailed() {
        try (MockedStatic<HeaderObj> headerObjMock = mockStatic(HeaderObj.class)) {
            // 准备测试数据
            User user = User.builder()
                .tenantId("123456")
                .userId("78910")
                .outTenantId("10001")
                .outUserId("20001")
                .build();
            String appId = "test-app";

            RestResult<Integer> mockResult = createFailedRestResult(500, "服务调用失败");

            HeaderObj mockHeaderObj = mock(HeaderObj.class);
            headerObjMock.when(() -> HeaderObj.newInstance(123456, 10001L, 20001L)).thenReturn(mockHeaderObj);

            // 配置Mock行为
            when(enterpriseRelationService.getMapperObjectOwnerId(any(HeaderObj.class), any(GetMapperObjectOwnerIdArg.class)))
                .thenReturn(mockResult);

            // 执行被测试方法
            String result = enterpriseRelationServiceImpl.getMapperObjectOwnerId(user, appId);

            // 验证结果 - 失败应返回null
            assertNull(result);
            verify(enterpriseRelationService).getMapperObjectOwnerId(any(HeaderObj.class), any(GetMapperObjectOwnerIdArg.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMapperObjectOwnerId方法，抛出异常
     */
    @Test
    @DisplayName("异常场景 - 测试getMapperObjectOwnerId方法抛出异常")
    void testGetMapperObjectOwnerId_ThrowsException() {
        try (MockedStatic<HeaderObj> headerObjMock = mockStatic(HeaderObj.class)) {
            // 准备测试数据
            User user = User.builder()
                .tenantId("123456")
                .userId("78910")
                .outTenantId("10001")
                .outUserId("20001")
                .build();
            String appId = "test-app";

            HeaderObj mockHeaderObj = mock(HeaderObj.class);
            headerObjMock.when(() -> HeaderObj.newInstance(123456, 10001L, 20001L)).thenReturn(mockHeaderObj);

            // 配置Mock行为 - 抛出异常
            when(enterpriseRelationService.getMapperObjectOwnerId(any(HeaderObj.class), any(GetMapperObjectOwnerIdArg.class)))
                .thenThrow(new RuntimeException("网络异常"));

            // 执行被测试方法
            String result = enterpriseRelationServiceImpl.getMapperObjectOwnerId(user, appId);

            // 验证结果 - 异常应返回null
            assertNull(result);
            verify(enterpriseRelationService).getMapperObjectOwnerId(any(HeaderObj.class), any(GetMapperObjectOwnerIdArg.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultDataOwner方法正常场景
     */
    @Test
    @DisplayName("正常场景 - 测试getDefaultDataOwner方法")
    void testGetDefaultDataOwner_NormalCase() {
        try (MockedStatic<HeaderObj> headerObjMock = mockStatic(HeaderObj.class);
             MockedStatic<AppIdUtil> appIdUtilMock = mockStatic(AppIdUtil.class)) {

            // 准备测试数据
            User outUser = User.builder()
                .tenantId("123456")
                .userId("78910")
                .outTenantId("10001")
                .outUserId("20001")
                .build();
            String apiName = "CustomObj";
            String appId = "test-app";

            RestResult<Integer> mockResult = mock(RestResult.class);
            when(mockResult.isSuccess()).thenReturn(true);
            when(mockResult.getData()).thenReturn(30001);

            HeaderObj mockHeaderObj = mock(HeaderObj.class);
            headerObjMock.when(() -> HeaderObj.newInstance(123456, 10001L, 20001L)).thenReturn(mockHeaderObj);
            appIdUtilMock.when(AppIdUtil::getAppIdWithNoDefault).thenReturn(appId);

            // 配置Mock行为
            when(enterpriseRelationObjService.getObjectDataOwnerId(any(HeaderObj.class), any(GetMapperObjectOwnerIdArg.class)))
                .thenAnswer(invocation -> {
                    GetMapperObjectOwnerIdArg arg = invocation.getArgument(1);
                    assertEquals(appId, arg.getFsAppId());
                    assertEquals(123456, arg.getUpstreamTenantId());
                    assertEquals(10001L, arg.getDownstreamOuterTenantId());
                    assertEquals(apiName, arg.getObjectApiName());
                    return mockResult;
                });

            // 执行被测试方法
            String result = enterpriseRelationServiceImpl.getDefaultDataOwner(outUser, apiName);

            // 验证结果
            assertEquals("30001", result);
            verify(enterpriseRelationObjService).getObjectDataOwnerId(any(HeaderObj.class), any(GetMapperObjectOwnerIdArg.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultDataOwner方法，接口调用失败
     */
    @Test
    @DisplayName("异常场景 - 测试getDefaultDataOwner方法接口调用失败")
    void testGetDefaultDataOwner_ServiceCallFailed() {
        try (MockedStatic<HeaderObj> headerObjMock = mockStatic(HeaderObj.class);
             MockedStatic<AppIdUtil> appIdUtilMock = mockStatic(AppIdUtil.class)) {

            // 准备测试数据
            User outUser = User.builder()
                .tenantId("123456")
                .userId("78910")
                .outTenantId("10001")
                .outUserId("20001")
                .build();
            String apiName = "CustomObj";
            String appId = "test-app";

            RestResult<Integer> mockResult = createFailedRestResult(500, "服务调用失败");

            HeaderObj mockHeaderObj = mock(HeaderObj.class);
            headerObjMock.when(() -> HeaderObj.newInstance(123456, 10001L, 20001L)).thenReturn(mockHeaderObj);
            appIdUtilMock.when(AppIdUtil::getAppIdWithNoDefault).thenReturn(appId);

            // 配置Mock行为
            when(enterpriseRelationObjService.getObjectDataOwnerId(any(HeaderObj.class), any(GetMapperObjectOwnerIdArg.class)))
                .thenReturn(mockResult);

            // 执行被测试方法
            String result = enterpriseRelationServiceImpl.getDefaultDataOwner(outUser, apiName);

            // 验证结果 - 失败应返回null
            assertNull(result);
            verify(enterpriseRelationObjService).getObjectDataOwnerId(any(HeaderObj.class), any(GetMapperObjectOwnerIdArg.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultDataOwnerByUser方法，外部用户且在灰度名单中
     */
    @Test
    @DisplayName("正常场景 - 测试getDefaultDataOwnerByUser方法外部用户在灰度名单")
    void testGetDefaultDataOwnerByUser_OutUserInGrayList() {
        try (MockedStatic<UdobjGrayConfig> grayConfigMock = mockStatic(UdobjGrayConfig.class)) {
            // 准备测试数据
            User user = User.builder()
                .tenantId("123456")
                .userId("78910")
                .outTenantId("10001")
                .outUserId("20001")
                .build();
            String expectedOwner = "30001";

            // 配置Mock行为
            grayConfigMock.when(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OUT_USER_CREATE_DATA_OWNER_ID_CALC_EI, "123456"))
                .thenReturn(true);
            grayConfigMock.when(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEFAULT_VALUE_SUPPORT_OWNER_GRAY, "123456"))
                .thenReturn(false);

            // Mock getDefaultDataOwner方法
            EnterpriseRelationServiceImpl spyService = spy(enterpriseRelationServiceImpl);
            doReturn(expectedOwner).when(spyService).getDefaultDataOwner(user, null);

            // 执行被测试方法
            Optional<String> result = spyService.getDefaultDataOwnerByUser(user);

            // 验证结果
            assertTrue(result.isPresent());
            assertEquals(expectedOwner, result.get());
            verify(spyService).getDefaultDataOwner(user, null);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultDataOwnerByUser方法，非外部用户
     */
    @Test
    @DisplayName("边界场景 - 测试getDefaultDataOwnerByUser方法非外部用户")
    void testGetDefaultDataOwnerByUser_NonOutUser() {
        // 准备测试数据
        User user = User.builder()
            .tenantId("123456")
            .userId("78910")
            .build();

        // 执行被测试方法
        Optional<String> result = enterpriseRelationServiceImpl.getDefaultDataOwnerByUser(user);

        // 验证结果 - 非外部用户应返回空Optional
        assertFalse(result.isPresent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultDataOwnerByUser方法，外部用户但不在灰度名单中
     */
    @Test
    @DisplayName("边界场景 - 测试getDefaultDataOwnerByUser方法外部用户不在灰度名单")
    void testGetDefaultDataOwnerByUser_OutUserNotInGrayList() {
        try (MockedStatic<UdobjGrayConfig> grayConfigMock = mockStatic(UdobjGrayConfig.class)) {
            // 准备测试数据
            User user = User.builder()
                .tenantId("123456")
                .userId("78910")
                .outTenantId("10001")
                .outUserId("20001")
                .build();

            // 配置Mock行为 - 不在灰度名单中
            grayConfigMock.when(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OUT_USER_CREATE_DATA_OWNER_ID_CALC_EI, "123456"))
                .thenReturn(false);
            grayConfigMock.when(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEFAULT_VALUE_SUPPORT_OWNER_GRAY, "123456"))
                .thenReturn(false);

            // 执行被测试方法
            Optional<String> result = enterpriseRelationServiceImpl.getDefaultDataOwnerByUser(user);

            // 验证结果 - 不在灰度名单应返回空Optional
            assertFalse(result.isPresent());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultDataOwnerByUser方法，外部用户在第二个灰度名单中
     */
    @Test
    @DisplayName("正常场景 - 测试getDefaultDataOwnerByUser方法外部用户在第二个灰度名单")
    void testGetDefaultDataOwnerByUser_OutUserInSecondGrayList() {
        try (MockedStatic<UdobjGrayConfig> grayConfigMock = mockStatic(UdobjGrayConfig.class)) {
            // 准备测试数据
            User user = User.builder()
                .tenantId("123456")
                .userId("78910")
                .outTenantId("10001")
                .outUserId("20001")
                .build();
            String expectedOwner = "30001";

            // 配置Mock行为
            grayConfigMock.when(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OUT_USER_CREATE_DATA_OWNER_ID_CALC_EI, "123456"))
                .thenReturn(false);
            grayConfigMock.when(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEFAULT_VALUE_SUPPORT_OWNER_GRAY, "123456"))
                .thenReturn(true);

            // Mock getDefaultDataOwner方法
            EnterpriseRelationServiceImpl spyService = spy(enterpriseRelationServiceImpl);
            doReturn(expectedOwner).when(spyService).getDefaultDataOwner(user, null);

            // 执行被测试方法
            Optional<String> result = spyService.getDefaultDataOwnerByUser(user);

            // 验证结果
            assertTrue(result.isPresent());
            assertEquals(expectedOwner, result.get());
            verify(spyService).getDefaultDataOwner(user, null);
        }
    }
}
