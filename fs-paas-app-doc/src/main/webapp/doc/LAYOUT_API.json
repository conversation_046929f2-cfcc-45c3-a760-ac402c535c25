{"swagger": "2.0", "info": {"title": "AppFrameWork REST API", "description": "PaaS 应用框架REST API", "version": "1.0"}, "host": "************:8234", "schemes": ["http"], "basePath": "/v1/inner/rest/layout", "produces": ["application/json"], "commonParams": {"userInfo": {"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, "eid": {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, "peerHost": {"name": "x-fs-peer-host", "in": "header", "description": "调用方Host地址", "required": false, "type": "string"}, "peerName": {"name": "x-fs-peer-name", "in": "header", "description": "调用方Name，例如FBS,OpenAPI,CRM等", "required": false, "type": "string"}, "descApiName": {"name": "describeAPIName", "in": "path", "required": true, "type": "string", "description": "元数据对象描述的API name", "default": "customer"}}, "paths": {"/{tenantId}/{layoutId}": {"get": {"summary": "find layout by id", "description": "根据id查询layout", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"name": "tenantId", "in": "path", "required": true, "describe": "企业id", "type": "string"}, {"name": "layoutId", "in": "path", "required": true, "describe": "布局id", "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/LayoutDocument"}}}}, "delete": {"summary": "delete layout by id", "description": "根据id删除布局", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"name": "tenantId", "in": "path", "required": true, "describe": "企业id", "type": "string"}, {"name": "layoutId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/LayoutDocument"}}}}}, "/{tenantId}": {"post": {"summary": "create layout", "description": "创建布局", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"name": "tenantId", "in": "path", "required": true, "describe": "企业id", "type": "string"}, {"name": "<PERSON><PERSON><PERSON>", "in": "body", "required": true, "schema": {"$ref": "#/definitions/LayoutJson"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/LayoutDocument"}}}}, "put": {"summary": "update layout", "description": "更新布局", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"name": "tenantId", "in": "path", "required": true, "describe": "企业id", "type": "string"}, {"name": "<PERSON><PERSON><PERSON>", "in": "body", "required": true, "schema": {"$ref": "#/definitions/LayoutJson"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/LayoutDocument"}}}}}, "/{tenantId}/find/{layoutApiName}/{descApiName}": {"get": {"summary": "find layout by api<PERSON><PERSON> and describe apiName", "description": "根据布局和对象描述的apiName查询布局", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"name": "tenantId", "in": "path", "required": true, "describe": "企业id", "type": "string"}, {"name": "layoutApiName", "in": "path", "required": true, "type": "string"}, {"name": "descApiName", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/LayoutDocument"}}}}}, "/{tenantId}/find/{descApiName}": {"get": {"summary": "find layout bydescribe apiName", "description": "根据对象描述的apiName查询布局", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"name": "tenantId", "in": "path", "required": true, "describe": "企业id", "type": "string"}, {"name": "descApiName", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/LayoutDocumentList"}}}}}, "/{tenantId}/find_default/{descApiName}/{layoutType}": {"get": {"summary": "find default layout", "description": "查询默认布局", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"name": "tenantId", "in": "path", "required": true, "describe": "企业id", "type": "string"}, {"name": "layoutType", "in": "path", "required": true, "type": "string"}, {"name": "descApiName", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/LayoutDocument"}}}}}}, "definitions": {"LayoutJson": {"type": "object"}, "LayoutDocument": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"ui_layout": {"type": "object"}}}}}, "LayoutDocumentList": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"queryResult": {"type": "array", "items": {"$ref": "#/definitions/LayoutDocument"}}}}}}}}