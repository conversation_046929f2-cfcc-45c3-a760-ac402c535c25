package com.facishare.paas.appframework.core.predef.facade.dto;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by zhao<PERSON>ju on 2025/3/11
 */
public interface MergeChangeOrderData {
    @Data
    @Builder
    class Arg {
        private IObjectDescribe changeOrderDescribe;
        private IObjectData changeOrderData;
        private IObjectDescribe originalDescribe;
        private IObjectData originalData;

    }

    @Data
    @Builder
    class Result {
        private IObjectData masterObjectData;
        private Map<String, List<IObjectData>> detailsData;
    }
}
