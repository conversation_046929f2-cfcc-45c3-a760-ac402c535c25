package com.facishare.paas.appframework.common.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 外部员工信息实体类
 * 用于存储和传递外部企业员工的基本信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OuterEmployeeInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 外部用户ID
     */
    private String outerUserId;

    /**
     * 员工姓名
     */
    private String outerUserName;

    /**
     * 外部企业ID
     */
    private String outerTenantId;

    /**
     * 外部企业名称
     */
    private String outerTenantName;

    /**
     * 员工状态 (1:正常, 0:禁用)
     */
    private Integer status;
}
