package com.facishare.paas.appframework.metadata.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;

@Slf4j
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LocationInfo {

    private BigDecimal longitude;
    private BigDecimal latitude;
    private String dataId;

    public static LocationInfo of(String value, String sourceDataId) {
        if (StringUtils.isEmpty(value) || StringUtils.isEmpty(sourceDataId)) {
            return null;
        }
        String[] split = value.split(":");
        if (split.length != 2) {
            log.warn("location info error! value:{},sourceDataId:{}", value, sourceDataId);
            return null;
        }
        return new LocationInfo(BigDecimal.valueOf(Double.parseDouble(split[0])), BigDecimal.valueOf(Double.parseDouble(split[1])), sourceDataId);
    }

    @Override
    public String toString() {
        if (Objects.isNull(longitude) || Objects.isNull(latitude)) {
            return "";
        }
        return longitude + ":" + latitude;
    }
}
