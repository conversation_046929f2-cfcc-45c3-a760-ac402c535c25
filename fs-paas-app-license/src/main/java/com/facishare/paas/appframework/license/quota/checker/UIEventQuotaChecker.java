package com.facishare.paas.appframework.license.quota.checker;

import lombok.Builder;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-09-10 17:49
 */
public class UIEventQuotaChecker extends BaseQuotaChecker {
    @Builder
    public UIEventQuotaChecker(Map<String, Integer> paraMap) {
        super(paraMap);
    }
    @Override
    public int getMaxCount(QuotaInfo quotaInfo) {
        return paraMap.getOrDefault(quotaInfo.getParaKey(), 0);
    }
    @Override
    public boolean check(QuotaInfo quotaInfo) {
        return getMaxCount(quotaInfo) >= quotaInfo.getActualCount();
    }
}
