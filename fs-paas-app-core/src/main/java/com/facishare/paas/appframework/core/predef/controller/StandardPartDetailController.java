package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.LayoutStructure;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 用于BPM待办详情页
 * 根据layoutType下发基本的详情页布局、或移动端列表页布局
 * create by z<PERSON><PERSON> on 2019/07/30
 */
public class StandardPartDetailController extends StandardDetailController {

    @Override
    protected ILayout getLayout() {
        if (ILayout.LIST_LAYOUT_TYPE.equals(arg.getLayoutType())) {
            List<ILayout> listLayout = serviceFacade.getLayoutLogicService().findMobileListLayout(buildLayoutContext(), describe, false);
            if (CollectionUtils.empty(listLayout)) {
                log.warn("listLayout not found, ei=>{}, apiName=>{}, user=>{}", controllerContext.getTenantId(), describe.getApiName(), controllerContext.getUser());
                return new Layout();
            }
            return listLayout.get(0);
        }
        ILayout layout = serviceFacade.getLayoutLogicService().findObjectLayout(buildLayoutContext(), data.getRecordType(), describe.getApiName());
        // 流程只取 topInfo 组件的信息，需要将该组件放到最外层
        LayoutExt layoutExt = LayoutExt.of(layout);
        if (!layoutExt.isNewLayout()) {
            return layout;
        }
        // 恢复为旧版本布局
        LayoutStructure.restoreLayout(layoutExt, PageType.Detail);
        return layout;
    }

    @Override
    protected void fillRefObjectName(IObjectData data, IObjectDescribe describe) {
        super.fillRefObjectName(data, describe);
        serviceFacade.parsePaymentObjOrderNames(describe, Lists.newArrayList(data), controllerContext.getUser(), false);
        stopWatch.lap("parsePaymentObjOrderNames");
    }
}
