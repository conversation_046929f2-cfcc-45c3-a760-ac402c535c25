package com.facishare.paas.appframework.core.predef.service.dto.tag;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.metadata.dto.scene.SceneDTO;
import com.facishare.paas.metadata.api.describe.ITagDescribe;
import com.facishare.paas.metadata.api.search.Ranges;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/19 11:16 上午
 */
public interface CreateTagGroup {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        @JsonProperty("describe_api_names")
        @JSONField(name = "describe_api_names")
        List<String> describeApiNames;

        @JsonProperty("api_name")
        @JSONField(name = "api_name")
        String apiName;

        //是否适用全部对象
        @JsonProperty("is_applied_to_all")
        @JSONField(name = "is_applied_to_all")
        Boolean isAppliedToAll = false;

        @JsonProperty("tag_group_name")
        @JSONField(name = "tag_group_name")
        String tagGroupName;

        @JsonProperty("tag_define_type")
        @JSONField(name = "tag_define_type")
        String tagDefineType;

        @JsonProperty("is_mutex")
        @JSONField(name = "is_mutex")
        Boolean isMutex = false;

        @JsonProperty("label_names")
        @JSONField(name = "label_names")
        List<CreateOrUpdateTag.Arg> tagInfoList = Lists.newArrayList();

        @JsonProperty("ranges")
        @JSONField(name = "ranges")
        SceneDTO.Range ranges = new SceneDTO.Range();

        @JsonProperty("group_description")
        @JSONField(name = "group_description")
        String groupDescription;
    }

    @Builder
    @Data
    class Result {
        ITagDescribe tagDescribe;
    }
}
