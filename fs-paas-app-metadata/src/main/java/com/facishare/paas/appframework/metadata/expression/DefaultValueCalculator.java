package com.facishare.paas.appframework.metadata.expression;

import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.expression.ExpressionService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.facishare.paas.metadata.api.describe.MasterDetail;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 默认值计算器
 * <p>
 * Created by liyiguang on 2018/3/23.
 */
@Slf4j
public class DefaultValueCalculator extends AbstractFieldCalculator {

    private MetaDataFindService metaDataFindService;

    private IFieldDescribe field;

    public DefaultValueCalculator(MetaDataFindService metaDataFindService, ExpressionService service,
                                  IObjectDescribe objectDescribe, IFieldDescribe field, boolean ignoreInvalidVariable) {
        super(service, createExpression(objectDescribe, field, ignoreInvalidVariable), objectDescribe);
        this.metaDataFindService = metaDataFindService;
        this.field = field;
    }

    @Override
    public void doCalculate(IObjectData data, Map<String, Object> globalVariableData, Map<String, Map<String, IObjectData>> objectDataMap,
                            Map<String, Object> extData, boolean isLookupDependencyForOthers) {
        try {
            //不是公式的默认值直接返回
            FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(field);
            if (!fieldDescribeExt.hasFormulaDefaultValue()
                    && !fieldDescribeExt.isMultiCurrencyFields()
                    && !fieldDescribeExt.isOwnerField()) {
                result = field.getDefaultValue();
                return;
            }

            super.doCalculate(data, globalVariableData, objectDataMap, extData, isLookupDependencyForOthers);
            if (result != null && result instanceof BigDecimal) {
                BigDecimal ret = (BigDecimal) result;
                result = ret.toPlainString();
            }

            //lookup字段的默认值计算完了需要重新获取下数据
            fillAssociatedObjectData(objectDataMap, isLookupDependencyForOthers);
        } catch (AppBusinessException e) {
            log.warn("calculate default value failed:{},tenantId:{},objectApiName:{},fieldApiName:{},fieldDescribe:{}",
                    e.getMessage(), objectDescribe.getTenantId(), objectDescribe.getApiName(), field.getApiName(), field);
            result = NA;
        } catch (Exception e) {
            log.error("calculate default value error:{},tenantId:{},objectApiName:{},fieldApiName:{},fieldDescribe:{},expression:{},globalVariableData:{},objectDataMap:{}",
                    e.getMessage(), objectDescribe.getTenantId(), objectDescribe.getApiName(), field.getApiName(),
                    field, expression, globalVariableData, objectDataMap, e);
            result = NA;
        }
    }

    private void fillAssociatedObjectData(Map<String, Map<String, IObjectData>> objectDataMap, boolean isLookupDependencyForOthers) {
        if (!isLookupDependencyForOthers) {
            return;
        }
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(field);
        if (!fieldDescribeExt.isLookupField() && !fieldDescribeExt.isEmployeeField() && !fieldDescribeExt.isDepartmentField()) {
            return;
        }
        if (ObjectDataExt.isValueEmpty(result)) {
            return;
        }
        String dataId = result instanceof List ? ((List) result).get(0).toString() : result.toString();
        String targetApiName = getTargetApiName(fieldDescribeExt);
        queryObjectData(objectDataMap, objectDescribe.getTenantId(), targetApiName, dataId);
    }

    private String getTargetApiName(FieldDescribeExt fieldDescribeExt) {
        if (fieldDescribeExt.isEmployeeField()) {
            return ObjectDescribeExt.EMPLOYEE_OBJ_API_NAME;
        }
        if (fieldDescribeExt.isDepartmentField()) {
            return ObjectDescribeExt.CALCULATE_DEPARTMENT_OBJ_API_NAME;
        }
        IObjectReferenceField referenceField = (IObjectReferenceField) field;
        return referenceField.getTargetApiName();
    }

    private void queryObjectData(Map<String, Map<String, IObjectData>> objectDataMap, String tenantId, String objectApiName, String dataId) {
        objectDataMap.putIfAbsent(objectApiName, Maps.newHashMap());
        if (objectDataMap.get(objectApiName).containsKey(dataId)) {
            return;
        }
        if (ObjectDescribeExt.EMPLOYEE_OBJ_API_NAME.equals(objectApiName)) {
            List<IObjectData> dataList = metaDataFindService.findEmployeeInfoByUserIdsIgnoreFormula(tenantId, Lists.newArrayList(dataId));
            dataList.forEach(d -> objectDataMap.get(objectApiName).put(d.get(ObjectDataExt.USER_ID).toString(), d));
        } else if (ObjectDescribeExt.CALCULATE_DEPARTMENT_OBJ_API_NAME.equals(objectApiName)) {
            List<IObjectData> dataList = metaDataFindService.findDepartmentInfoByDepartIdsIgnoreFormula(tenantId, Lists.newArrayList(dataId));
            dataList.forEach(d -> objectDataMap.get(objectApiName).put(d.get(ObjectDataExt.DEPT_ID).toString(), d));
        } else {
            IObjectData data = metaDataFindService.findObjectDataIgnoreFormula(User.systemUser(tenantId), dataId, objectApiName);
            objectDataMap.get(objectApiName).put(dataId, data);
        }
    }

    @Override
    public String key() {
        return field.getApiName();
    }

    public static Expression createExpression(IObjectDescribe describe, IFieldDescribe field) {
        return createExpression(describe, field, false);
    }

    public static Expression createExpression(IObjectDescribe describe, IFieldDescribe field, boolean ignoreInvalidVariable) {
        int decimalPlaces = FieldDescribeExt.of(field).getDecimalPlaces();
        String defaultValue = getDefaultValue(describe, field);
        return Expression.builder()
                .expression(defaultValue)
                .describe(describe)
                .returnType(field.getType())
                .decimalPlaces(decimalPlaces)
                .nullAsZero(field.getDefaultToZero() == null ? true : field.getDefaultToZero())
                .useValue(Boolean.TRUE.equals(field.getIsUseValue()))
                .fieldName(field.getApiName())
                .expressionLabel(describe.getDisplayName() + "." + field.getLabel())
                .ignoreInvalidVariable(ignoreInvalidVariable)
                .isDefaultValue(true)
                .build();
    }

    private static String getDefaultValue(IObjectDescribe describe, IFieldDescribe field) {
        //特殊处理币种字段，默认值等于主对象的币种
        if (FieldDescribeExt.CURRENCY_FIELD.equals(field.getApiName())) {
            Optional<MasterDetail> masterDetailOpt = ObjectDescribeExt.of(describe).getMasterDetailField();
            if (masterDetailOpt.isPresent()) {
                return String.format("$%s__r." + FieldDescribeExt.CURRENCY_FIELD + "$", masterDetailOpt.get().getApiName());
            }
        }
        // 从对象负责人字段的默认值特殊处理为主对象的负责人
        if (IObjectData.OWNER.equals(field.getApiName())
                && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEFAULT_VALUE_SUPPORT_OWNER_GRAY, describe.getTenantId())) {
            Optional<MasterDetail> masterDetailOpt = ObjectDescribeExt.of(describe).getMasterDetailField();
            if (masterDetailOpt.isPresent()) {
                return String.format("$%s__r." + IObjectData.OWNER + "$", masterDetailOpt.get().getApiName());
            }
        }
        return field.getDefaultValue() == null ? "" : field.getDefaultValue().toString();
    }
}
