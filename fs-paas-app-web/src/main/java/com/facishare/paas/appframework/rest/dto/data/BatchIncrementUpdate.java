package com.facishare.paas.appframework.rest.dto.data;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/12/13
 */
public interface BatchIncrementUpdate {
    @Data
    @Builder
    class Arg {
        private String dataListJson;
        private String apiName;

    }

    @Data
    class Result {
        private List<ObjectDataDocument> dataDocuments;

        @java.beans.ConstructorProperties({"dataDocuments"})
        private Result(List<ObjectDataDocument> dataDocuments) {
            this.dataDocuments = dataDocuments;
        }

        public static Result of(List<IObjectData> objectDataList) {
            return new Result(ObjectDataDocument.ofList(objectDataList));
        }
    }
}
