package com.facishare.paas.appframework.metadata.dto.sfa;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2018/1/29
 */
public interface BatchCalculateCountFields {

    @Data
    @Builder
    class Arg {
        @SerializedName("ObjectApiName")
        @JSONField(name = "ObjectApiName")
        private String objectApiName;
        @SerializedName("ObjectDataId")
        @JSONField(name = "ObjectDataId")
        private String objectDataId;
        @SerializedName("CountFieldList")
        @JSONField(name = "CountFieldList")
        private List<CountField> countFieldList;
    }

    @Getter
    @Setter
    class Result extends BaseResult {
        private CalculateResult value;
    }

    @Data
    class CalculateResult {
        @SerializedName("CountFieldValues")
        @JSONField(name = "CountFieldValues")
        private Map<String, Object> countFieldValues;
    }

    @Data
    class CountField {
        @SerializedName("ApiName")
        @JSONField(name = "ApiName")
        private String apiName;
        @SerializedName("RelatedFieldName")
        @JSONField(name = "RelatedFieldName")
        private String relatedFieldName;
        @SerializedName("CountType")
        @JSONField(name = "CountType")
        private String countType;
        @SerializedName("CountFieldApiName")
        @JSONField(name = "CountFieldApiName")
        private String countFieldApiName;
        @SerializedName("CountObjectApiName")
        @JSONField(name = "CountObjectApiName")
        private String countObjectApiName;
        @SerializedName("DecimalScale")
        @JSONField(name = "DecimalScale")
        private int decimalScale;
        @SerializedName("ReturnType")
        @JSONField(name = "ReturnType")
        private String returnType;
        @SerializedName("Wheres")
        @JSONField(name = "Wheres")
        private List<Where> wheres;

        public static CountField of(Count countFieldDescribe, List<Wheres> wheres) {
            CountField countField = new CountField();
            countField.setApiName(countFieldDescribe.getApiName());
            countField.setRelatedFieldName(countFieldDescribe.getFieldApiName());
            countField.setCountType(countFieldDescribe.getCountType());
            countField.setCountFieldApiName(countFieldDescribe.getCountFieldApiName());
            countField.setCountObjectApiName(countFieldDescribe.getSubObjectDescribeApiName());
            countField.setDecimalScale(countFieldDescribe.getDecimalPlaces());
            countField.setReturnType(countFieldDescribe.getReturnType());
            if (CollectionUtils.notEmpty(wheres)) {
                countField.setWheres(wheres.stream().map(Where::of).collect(Collectors.toList()));
            }

            return countField;
        }
    }

    @Data
    class Where {
        @SerializedName("Connector")
        @JSONField(name = "Connector")
        private String connector;
        @SerializedName("Filters")
        @JSONField(name = "Filters")
        private List<Filter> filters;

        public static Where of(Wheres wheres) {
            Where where = new Where();
            where.setConnector(wheres.getConnector());
            List<IFilter> filters = wheres.getFilters();
            if (CollectionUtils.notEmpty(filters)) {
                where.setFilters(filters.stream().map(Filter::of).collect(Collectors.toList()));
            }

            return where;
        }
    }

    @Data
    class Filter {
        @SerializedName("FieldName")
        @JSONField(name = "FieldName")
        private String fieldName;
        @SerializedName("Operator")
        @JSONField(name = "Operator")
        private String operator;
        @SerializedName("FieldValues")
        @JSONField(name = "FieldValues")
        private List<String> fieldValues;
        @SerializedName("ValueType")
        @JSONField(name = "ValueType")
        private int valueType;

        public static Filter of(IFilter iFilter) {
            Filter filter = new Filter();
            filter.setFieldName(iFilter.getFieldName());
            filter.setOperator(iFilter.getOperator().name());
            filter.setFieldValues(iFilter.getFieldValues());
            filter.setValueType(iFilter.getValueType());

            return filter;
        }
    }
}
