package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils.ParallelTask;
import com.facishare.paas.appframework.core.exception.RecordTypeNotFound;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.flow.ExtraDataKeys;
import com.facishare.paas.appframework.flow.mq.WorkflowProducer;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.MetaDataActionService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Payment;
import com.facishare.paas.metadata.api.describe.SignIn;
import com.facishare.paas.metadata.impl.IRule;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Slf4j
public class StandardBulkCreateAction extends BaseObjectApprovalAction<StandardBulkCreateAction.Arg, StandardBulkCreateAction.Result> {

    protected List<IObjectData> objectDataList;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.BulkCreate.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Collections.emptyList();
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        init();
        validate();
    }

    @Override
    protected void init() {
        objectDataList = arg.getDataList().stream().map(ObjectDataDocument::toObjectData).collect(
                Collectors.toList());
    }

    protected void validate() {
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }

        if (ObjectDescribeExt.of(objectDescribe).getMasterDetailFieldDescribe().isPresent() &&
                !AppFrameworkConfig.isAllowChildBulkCreate(actionContext.getTenantId(), objectDescribe.getApiName())) {
            throw new ValidateException(I18N.text(I18NKey.OBJECT_EXIST_MD_RELATION_CAN_NOT_BATCH_CREAT));
        }

        if (!AppFrameworkConfig.isSkipValidateLookupGrayTenant(actionContext.getTenantId())) {
            validateMasterAndLookupData();
        }
        validateRules();
    }

    private void validateMasterAndLookupData() {
        ParallelTask parallelExecutor = ParallelUtils.createParallelTask();
        objectDataList.forEach(data -> parallelExecutor.submit(
                () -> validateObjectData(data, objectDescribe)
        ));
        try {
            boolean result = parallelExecutor.await(10, TimeUnit.SECONDS);
            if (!result) {
                throw new ValidateException(I18N.text(I18NKey.DATA_VALIDATE_FAILED_PLEASE_AGAIN));
            }
        } catch (TimeoutException ex) {
            log.error("{}, Arg: {}", ex.getMessage(), arg, ex);
            throw new ValidateException(I18N.text(I18NKey.DATA_VALIDATE_FAILED_PLEASE_AGAIN));
        }
    }

    /**
     * Important: Thread safe guarantee.
     */
    private void validateObjectData(IObjectData data, IObjectDescribe describe) {
        ObjectDataExt.of(data).validate(describe);
        ObjectDescribeExt.of(describe).getLookupFieldDescribes().forEach(f ->
                serviceFacade.validateLookupData(actionContext.getUser(), data, f)
        );
    }

    private void validateRules() {
        infraServiceFacade.doValidate(actionContext.getUser(), IRule.CREATE, objectDescribe, objectDataList);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        recordLog();
        triggerFlows();
        return result;
    }

    protected void recordLog() {
        serviceFacade.log(actionContext.getUser(), EventType.ADD, ActionType.Add, objectDescribe,
                objectDataList);
    }

    protected void triggerFlows() {
        Set<String> triggerApprovalDataIds = triggerApprovalFlowForCreatedData(actionContext.getUser(), objectDataList);

        if (needTriggerWorkFlow()) {
            List<IObjectData> triggerWorkFlowDataList = getWorkflowTargetDataList(objectDataList, triggerApprovalDataIds);
            batchStartWorkflowForCreatedData(actionContext.getUser(), triggerWorkFlowDataList);
        }
    }

    private void batchStartWorkflowForCreatedData(User user, List<IObjectData> targetDataList) {
        if (CollectionUtils.isEmpty(targetDataList)) {
            return;
        }
        targetDataList.forEach(data -> startWorkflowForSingleData(user, data));

    }

    void startWorkflowForSingleData(User user, IObjectData createdData) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(createdData);
        if (!objectDataExt.isNormal() && !objectDataExt.isInChange()) {
            return;
        }
        infraServiceFacade.startWorkFlow(createdData.getId(), createdData.getDescribeApiName(),
                WorkflowProducer.TRIGGER_START, user, Maps.newHashMap(), actionContext.getEventId());
    }

    private List<IObjectData> getWorkflowTargetDataList(List<IObjectData> allDataList, Set<String> approvalTriggeredDataIds) {
        if (CollectionUtils.isEmpty(approvalTriggeredDataIds)) {
            return allDataList;
        }
        return allDataList.stream()
                .filter(data -> !approvalTriggeredDataIds.contains(data.getId()))
                .collect(Collectors.toList());
    }

    private Set<String> triggerApprovalFlowForCreatedData(User user, List<IObjectData> createdDataList) {
        if (!needTriggerApprovalFlow()) {
            return Sets.newHashSet();
        }
        boolean shouldTriggerWorkFlow = needTriggerWorkFlow();

        Map<String, Map<String, Object>> callbackDataMap = prepareApprovalCallbackData(shouldTriggerWorkFlow, createdDataList);
        Set<String> approvalTriggeredDataIds = extractDataIds(createdDataList);
        startApprovalFlowAsynchronous(createdDataList, ApprovalFlowTriggerType.CREATE, Maps.newHashMap(), callbackDataMap);
        return approvalTriggeredDataIds;
    }

    private Set<String> extractDataIds(List<IObjectData> dataList) {
        return dataList.stream()
                .map(IObjectData::getId)
                .collect(Collectors.toSet());
    }

    private Map<String, Map<String, Object>> prepareApprovalCallbackData(boolean shouldTriggerWorkFlow, List<IObjectData> createdDataList) {
        Map<String, Map<String, Object>> callbackDataMap = Maps.newHashMap();
        for (IObjectData objectData : createdDataList) {
            String dataId = objectData.getId();
            Map<String, Object> callbackData = Maps.newHashMap();
            callbackData.put(ExtraDataKeys.TRIGGER_WORK_FLOW, shouldTriggerWorkFlow);
            callbackDataMap.put(dataId, callbackData);
        }
        return callbackDataMap;
    }

    @Override
    protected Result doAct(Arg arg) {
        Result result = new Result();
        List<IObjectData> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(objectDataList)) {
            initializeOwner(objectDescribe);
            initializeSystemFieldValues();
            list.addAll(doSaveData());
        }
        result.setDataList(list.stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
        return result;
    }

    private List<IObjectData> doSaveData() {
        MetaDataActionService.CreateAttributes attributes = MetaDataActionService.CreateAttributes.builder()
                .calculateDefaultValue(arg.calculateDefaultValue())
                .build();
        return serviceFacade.bulkSaveObjectData(objectDataList, actionContext.getUser(), attributes);
    }

    private void initializeSystemFieldValues() {
        objectDataList.forEach(data -> initializeSystemFieldValues(data, objectDescribe));
    }

    /**
     * Important: Thread safe guarantee.
     */
    private void initializeSystemFieldValues(IObjectData data, IObjectDescribe describe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        initializeRecordType(data, describeExt);
        data.setTenantId(actionContext.getTenantId());
        data.setCreatedBy(actionContext.getUser().getUserIdOrOutUserIdIfOutUser());
        data.setLastModifiedBy(actionContext.getUser().getUserIdOrOutUserIdIfOutUser());

        //处理外部联系人
        ObjectDataExt dataExt = ObjectDataExt.of(data);
        dataExt.setOutUser(actionContext.getUser());

        data.set(ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectLifeStatus.INEFFECTIVE.getCode());
        describeExt.getSignInFieldDescribe().ifPresent(f -> {
            data.set(f.getVisitStatusFieldApiName(), SignIn.SIGN_STATUS_INCOMPLETE);
            data.set(f.getSignInStatusFieldApiName(), SignIn.SIGN_STATUS_INCOMPLETE);
            data.set(f.getSignOutStatusFieldApiName(), SignIn.SIGN_STATUS_INCOMPLETE);
        });
        describeExt.getPaymentFieldDescribe().ifPresent(f -> {
            data.set(f.getPayStatusFieldApiName(), Payment.PAY_STATUS_INCOMPLETE);
            data.set(f.getPayAmountFieldApiName(), 0);
        });
        dataExt.setDefaultTeamMember();
        dataExt.setDefaultOutOwner2TeamMember();
    }

    private void initializeRecordType(IObjectData data, ObjectDescribeExt describeExt) {
        String recordType = data.getRecordType();
        if (StringUtils.isBlank(recordType) || "default".equals(recordType) || "sail"
                .equals(recordType)) {
            data.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        } else {
            IRecordTypeOption option = describeExt.getRecordTypeOption(recordType)
                    .orElseThrow(() -> new RecordTypeNotFound(I18N.text(I18NKey.RECORD_TYPE_NOT_EXIST)));
            if (!option.isActive()) {
                throw new RecordTypeNotFound(I18N.text(I18NKey.RECORD_TYPE_IS_DISABLED, option.getLabel()));
            }
        }
    }

    private void initializeOwner(IObjectDescribe describe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        Optional<MasterDetailFieldDescribe> masterDetailFieldDescribe = describeExt.getMasterDetailFieldDescribe();
        if (masterDetailFieldDescribe.isPresent()) {
            String masterFieldName = masterDetailFieldDescribe.get().getApiName();
            String masterApiName = masterDetailFieldDescribe.get().getTargetApiName();
            // In case with different master id
            Map<Object, String> masterOwners = Maps.newHashMap();
            objectDataList.forEach(d -> {
                if (!masterOwners.containsKey(d.get(masterFieldName))) {
                    IObjectDescribe masterDescribe = serviceFacade.findObject(d.getTenantId(), masterApiName);
                    ObjectDataExt masterData = ObjectDataExt.of(serviceFacade.findObjectData(d.getTenantId(), d.get(masterFieldName).toString(), masterDescribe));
                    if (masterData.getOwnerId().isPresent()) {
                        ObjectDataExt.of(d).setOwnerId(masterData.getOwnerId().get());
                    }
                    masterOwners.put(d.get(masterFieldName), masterData.getOwnerId().get());
                } else {
                    ObjectDataExt.of(d).setOwnerId(masterOwners.get(d.get(masterFieldName)));
                }
            });
        }
    }

    @Override
    protected boolean needTriggerWorkFlow() {
        return BooleanUtils.isTrue(arg.getTriggerWorkFlow());
    }

    @Override
    protected boolean needTriggerApprovalFlow() {
        return BooleanUtils.isNotFalse(arg.getTriggerApprovalFlow());
    }

    @Data
    public static class Arg {
        @JsonProperty("data_list")
        List<ObjectDataDocument> dataList;
        Boolean calculateDefaultValue;

        private Boolean triggerWorkFlow;
        private Boolean triggerApprovalFlow;

        public boolean calculateDefaultValue() {
            return Boolean.TRUE.equals(calculateDefaultValue);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        @JsonProperty("data_list")
        List<ObjectDataDocument> dataList;
    }
}
