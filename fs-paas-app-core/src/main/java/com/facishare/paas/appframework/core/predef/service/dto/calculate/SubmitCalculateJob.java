package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2018/10/30
 */
public interface SubmitCalculateJob {

    @Data
    class Arg {
        private List<CalculateJob> calculateJobList;
        private boolean manual = false;
        private Long lastModifiedTime;
    }

    @Data
    @AllArgsConstructor
    class Result {
        private Map<String, List<String>> calculateFieldMap;
    }

    @Data
    class CalculateJob {
        private String objectApiName;
        private List<String> fieldApiNameList;
    }
}
