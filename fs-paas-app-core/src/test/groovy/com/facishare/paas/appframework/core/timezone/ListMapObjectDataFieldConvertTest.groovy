package com.facishare.paas.appframework.core.timezone

import com.facishare.paas.appframework.core.timezone.annotation.ObjectDataField
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import spock.lang.Specification

import java.util.function.Function

/**
 * GenerateByAI
 * 测试内容描述：ListMapObjectDataFieldConvert列表映射对象数据字段转换器的单元测试
 */
class ListMapObjectDataFieldConvertTest extends Specification {

    def converter = ListMapObjectDataFieldConvert.getInstance()
    def mockDescribe = Mock(IObjectDescribe)
    def mockFunction = Mock(Function)

    def setup() {
        mockFunction.apply(_) >> mockDescribe

        // Mock ObjectDataExt静态方法
        ObjectDataExt.metaClass.static.convertDateFieldValueToSystemZone = { IObjectDescribe describe, List dataList ->
            // 模拟系统时区转换
            return dataList.collect { data ->
                def result = new ObjectData()
                result.setDescribeApiName(data.getDescribeApiName())
                result.set("converted_to_system", true)
                result.set("id", data.get("id"))
                return result
            }
        }

        ObjectDataExt.metaClass.static.convertDateFieldValueToCustomZone = { IObjectDescribe describe, List dataList ->
            // 模拟自定义时区转换
            return dataList.collect { data ->
                def result = new ObjectData()
                result.setDescribeApiName(data.getDescribeApiName())
                result.set("converted_to_custom", true)
                result.set("id", data.get("id"))
                return result
            }
        }

        ObjectDataExt.metaClass.static.of = { Map map ->
            def objectData = new ObjectData()
            objectData.setDescribeApiName("TestApi")
            map.each { k, v -> objectData.set(k, v) }
            return objectData
        }
    }

    def cleanup() {
        // 清理元类修改
        ObjectDataExt.metaClass = null
    }

    def "test getInstance returns singleton"() {
        when: "多次获取实例"
        def instance1 = ListMapObjectDataFieldConvert.getInstance()
        def instance2 = ListMapObjectDataFieldConvert.getInstance()

        then: "应该返回相同的实例"
        instance1.is(instance2)
        instance1 == instance2
    }

    def "test getType returns LIST_MAP"() {
        when: "获取类型"
        def type = converter.getType()

        then: "应该返回LIST_MAP类型"
        type == ObjectDataField.Type.LIST_MAP
    }

    def "test convert2SystemZone with null value"() {
        when: "转换null值到系统时区"
        def result = converter.convert2SystemZone(null, mockFunction)

        then: "应该返回null"
        result == null
        0 * mockFunction.apply(_)
    }

    def "test convert2SystemZone with valid map"() {
        given: "准备有效的Map数据"
        def testMap = [
            "api1": [
                ["id": "1", "name": "item1"],
                ["id": "2", "name": "item2"]
            ],
            "api2": [
                ["id": "3", "name": "item3"]
            ]
        ]

        when: "转换Map到系统时区"
        def result = converter.convert2SystemZone(testMap, mockFunction)

        then: "应该正确转换"
        result instanceof Map
        result.size() == 2
        result["api1"] instanceof List
        result["api2"] instanceof List
        2 * mockFunction.apply(_) >> mockDescribe
    }

    def "test convert2SystemZone with empty map"() {
        given: "准备空Map"
        def emptyMap = [:]

        when: "转换空Map到系统时区"
        def result = converter.convert2SystemZone(emptyMap, mockFunction)

        then: "应该返回Map"
        result instanceof Map
        result.isEmpty()
        0 * mockFunction.apply(_)
    }

    def "test convert2SystemZone with empty lists in map"() {
        given: "准备包含空列表的Map"
        def mapWithEmptyLists = [
            "api1": [],
            "api2": []
        ]

        when: "转换包含空列表的Map"
        def result = converter.convert2SystemZone(mapWithEmptyLists, mockFunction)

        then: "应该正确处理空列表"
        result instanceof Map
        result.size() == 2
        result["api1"] instanceof List
        result["api1"].isEmpty()
        result["api2"] instanceof List
        result["api2"].isEmpty()
        2 * mockFunction.apply(_) >> mockDescribe
    }

    def "test convert2CustomZone with null value"() {
        when: "转换null值到自定义时区"
        def result = converter.convert2CustomZone(null, mockFunction)

        then: "应该返回null"
        result == null
        0 * mockFunction.apply(_)
    }

    def "test convert2CustomZone with valid map"() {
        given: "准备有效的Map数据"
        def testMap = [
            "api1": [
                ["id": "1", "name": "item1"],
                ["id": "2", "name": "item2"]
            ],
            "api2": [
                ["id": "3", "name": "item3"]
            ]
        ]

        when: "转换Map到自定义时区"
        def result = converter.convert2CustomZone(testMap, mockFunction)

        then: "应该正确转换"
        result instanceof Map
        result.size() == 2
        result["api1"] instanceof List
        result["api2"] instanceof List
        2 * mockFunction.apply(_) >> mockDescribe
    }

    def "test convert2SystemZone with mixed content"() {
        given: "准备混合内容的Map"
        def mixedMap = [
            "api1": [
                ["id": "1", "name": "item1"],
                '{"id":"2","name":"item2"}'
            ],
            "api2": [
                '{"id":"3","name":"item3"}'
            ]
        ]

        when: "转换混合内容Map"
        def result = converter.convert2SystemZone(mixedMap, mockFunction)

        then: "应该正确转换"
        result instanceof Map
        result.size() == 2
        2 * mockFunction.apply(_) >> mockDescribe
    }

    def "test convert2SystemZone calls function for each api"() {
        given: "准备多API的Map"
        def multiApiMap = [
            "Account": [["id": "1"]],
            "Contact": [["id": "2"]],
            "Opportunity": [["id": "3"]]
        ]

        when: "转换多API Map"
        converter.convert2SystemZone(multiApiMap, mockFunction)

        then: "应该为每个API调用函数"
        1 * mockFunction.apply("Account") >> mockDescribe
        1 * mockFunction.apply("Contact") >> mockDescribe
        1 * mockFunction.apply("Opportunity") >> mockDescribe
    }

    def "test convert2CustomZone calls function for each api"() {
        given: "准备多API的Map"
        def multiApiMap = [
            "Account": [["id": "1"]],
            "Contact": [["id": "2"]],
            "Opportunity": [["id": "3"]]
        ]

        when: "转换多API Map"
        converter.convert2CustomZone(multiApiMap, mockFunction)

        then: "应该为每个API调用函数"
        1 * mockFunction.apply("Account") >> mockDescribe
        1 * mockFunction.apply("Contact") >> mockDescribe
        1 * mockFunction.apply("Opportunity") >> mockDescribe
    }

    def "test convert2SystemZone with non-map throws exception"() {
        given: "准备非Map类型"
        def nonMap = "not a map"

        when: "转换非Map类型"
        converter.convert2SystemZone(nonMap, mockFunction)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test convert2CustomZone with non-map throws exception"() {
        given: "准备非Map类型"
        def nonMap = "not a map"

        when: "转换非Map类型"
        converter.convert2CustomZone(nonMap, mockFunction)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test convert2SystemZone with invalid list content"() {
        given: "准备包含无效列表内容的Map"
        def invalidMap = [
            "api1": [123, 456] // 不支持的类型
        ]

        when: "转换包含无效内容的Map"
        converter.convert2SystemZone(invalidMap, mockFunction)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test convert2CustomZone with invalid list content"() {
        given: "准备包含无效列表内容的Map"
        def invalidMap = [
            "api1": [123, 456] // 不支持的类型
        ]

        when: "转换包含无效内容的Map"
        converter.convert2CustomZone(invalidMap, mockFunction)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test convert2SystemZone preserves map structure"() {
        given: "准备复杂Map结构"
        def complexMap = [
            "api1": [
                ["id": "1", "nested": ["field": "value"]],
                ["id": "2", "list": ["item1", "item2"]]
            ],
            "api2": [
                ["id": "3", "complex": ["nested": ["deep": "value"]]]
            ]
        ]

        when: "转换复杂Map结构"
        def result = converter.convert2SystemZone(complexMap, mockFunction)

        then: "应该保持Map结构"
        result instanceof Map
        result.size() == 2
        result["api1"] instanceof List
        result["api2"] instanceof List
        2 * mockFunction.apply(_) >> mockDescribe
    }

    def "test convert2CustomZone preserves map structure"() {
        given: "准备复杂Map结构"
        def complexMap = [
            "api1": [
                ["id": "1", "nested": ["field": "value"]],
                ["id": "2", "list": ["item1", "item2"]]
            ]
        ]

        when: "转换复杂Map结构"
        def result = converter.convert2CustomZone(complexMap, mockFunction)

        then: "应该保持Map结构"
        result instanceof Map
        result.size() == 1
        result["api1"] instanceof List
        1 * mockFunction.apply(_) >> mockDescribe
    }

    def "test conversion with large dataset"() {
        given: "准备大数据集"
        def largeMap = [:]
        (1..100).each { i ->
            largeMap["api$i"] = (1..10).collect { j ->
                ["id": "${i}_${j}", "name": "item_${i}_${j}"]
            }
        }

        when: "转换大数据集"
        def result = converter.convert2SystemZone(largeMap, mockFunction)

        then: "应该正确处理大数据集"
        result instanceof Map
        result.size() == 100
        100 * mockFunction.apply(_) >> mockDescribe
    }

    def "test thread safety of singleton"() {
        given: "准备并发测试"
        def instances = []

        when: "并发获取实例"
        def threads = (1..10).collect { i ->
            Thread.start {
                def instance = ListMapObjectDataFieldConvert.getInstance()
                synchronized (instances) {
                    instances << instance
                }
            }
        }
        threads.each { it.join() }

        then: "所有实例应该相同"
        instances.size() == 10
        instances.every { it.is(instances[0]) }
    }

    def "test conversion performance"() {
        given: "准备测试数据"
        def testMap = [
            "api1": [["id": "1"], ["id": "2"]],
            "api2": [["id": "3"]]
        ]

        when: "多次转换"
        def startTime = System.currentTimeMillis()
        1000.times {
            converter.convert2SystemZone(testMap, mockFunction)
        }
        def endTime = System.currentTimeMillis()

        then: "性能应该可接受"
        (endTime - startTime) < 10000 // 10秒内完成1000次转换
    }

    def "test inheritance from AbstractObjectDataFieldConvert"() {
        expect: "应该继承自AbstractObjectDataFieldConvert"
        converter instanceof AbstractObjectDataFieldConvert
        converter instanceof ObjectDataFieldConvert
    }

    def "test Helper inner class"() {
        expect: "Helper类应该正确实现单例模式"
        // 通过反射验证Helper类的存在
        def helperClass = ListMapObjectDataFieldConvert.getDeclaredClasses().find { it.simpleName == "Helper" }
        helperClass != null
        helperClass.getDeclaredFields().any { it.name == "INSTANCE" }
    }

    def "test convert with null function throws exception"() {
        given: "准备测试数据"
        def testMap = ["api1": [["id": "1"]]]

        when: "使用null函数转换"
        converter.convert2SystemZone(testMap, null)

        then: "应该抛出异常"
        thrown(Exception)
    }

    def "test convert with map containing non-list values"() {
        given: "准备包含非列表值的Map"
        def invalidMap = [
            "api1": "not a list",
            "api2": 123
        ]

        when: "转换包含非列表值的Map"
        converter.convert2SystemZone(invalidMap, mockFunction)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }
}
