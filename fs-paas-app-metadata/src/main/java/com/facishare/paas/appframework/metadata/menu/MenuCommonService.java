package com.facishare.paas.appframework.metadata.menu;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;

public interface MenuCommonService {
    IObjectData createMenuItem(User user, String apiName);

    IObjectData createOrUpdateMenuItemWithOrder(User user, String apiName, Integer order);

    IObjectData findDefaultCrmMenu(User user);

    List<IObjectData> findMenuItemByApiName(User user, String menuId, List<String> apiNames);

    IObjectData findLastCrmMenuItem(User user, String menuId);

    IObjectData buildMenuItem(User user, String menuId, IObjectDescribe menuItemDescribe, String objectApiName);
}
