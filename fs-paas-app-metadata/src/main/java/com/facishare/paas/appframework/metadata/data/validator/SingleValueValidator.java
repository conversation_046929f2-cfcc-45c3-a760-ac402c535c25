package com.facishare.paas.appframework.metadata.data.validator;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Objects;
import java.util.Set;

import static com.facishare.paas.appframework.core.i18n.I18NKey.FIELD_DATA_TYPE_WRONG;

/**
 * 单值字段验证器，支持 SELECT_ONE 类型
 * 确保字段值不是集合类型（即为单值）
 */
@Slf4j
@Component
public class SingleValueValidator implements IDataTypeValidator {
  @Override
  public Set<String> supportFieldTypes() {
    return Sets.newHashSet(IFieldType.SELECT_ONE);
  }

  @Override
  public void validateDataType(String fieldApiName, IObjectData data, IObjectDescribe describe) {
    if (checkParamError(fieldApiName, data, describe)) {
      return;
    }

    Object fieldValue = data.get(fieldApiName);
    if (Objects.isNull(fieldValue) || !(fieldValue instanceof Collection)) {
      return;
    }

    throw new ValidateException(I18NExt.getOrDefault(FIELD_DATA_TYPE_WRONG, FIELD_DATA_TYPE_WRONG, getField(fieldApiName, describe).getLabel()+fieldApiName, fieldValue));
  }
}
