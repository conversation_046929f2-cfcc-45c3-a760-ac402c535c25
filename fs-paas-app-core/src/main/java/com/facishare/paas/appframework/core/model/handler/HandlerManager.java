package com.facishare.paas.appframework.core.model.handler;

import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;

/**
 * 业务处理器管理器，缓存所有业务处理器，对外提供注册和查找业务处理器的接口
 * Created by zhouwr on 2023/1/6.
 */
public interface HandlerManager {
    /**
     * 注册业务处理器
     *
     * @param handler 业务处理器
     */
    void register(Handler handler);

    /**
     * 根据配置信息查找业务处理器
     *
     * @param handlerDescribe 业务处理器的配置信息
     * @param <T>             业务处理器的具体类型
     * @return 如果管理器中存在指定ApiName的业务处理器，则返回该业务处理器，否则返回指定interfaceCode和handlerType的远程处理器
     */
    <T extends Handler> T getHandler(SimpleHandlerDescribe handlerDescribe);
}
