package com.facishare.paas.appframework.core.predef.service.dto.tag;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.metadata.dto.scene.SceneDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface FindTagGroupInfo {
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    class Arg {
        @JsonProperty("group_api_name")
        @JSONField(name = "group_api_name")
        String groupApiName;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    class Result {

        @JsonProperty("id")
        @JSONField(name = "id")
        String groupId;

        @JsonProperty("describe_api_names")
        @JSONField(name = "describe_api_names")
        List<String> describeApiNames;

        @JsonProperty("api_name")
        @JSONField(name = "api_name")
        String apiName;

        //是否适用全部对象
        @JsonProperty("is_applied_to_all")
        @JSONField(name = "is_applied_to_all")
        Boolean isAppliedToAll;

        @JsonProperty("tag_group_name")
        @JSONField(name = "tag_group_name")
        String tagGroupName;

        @JsonProperty("tag_define_type")
        @JSONField(name = "tag_define_type")
        String tagDefineType;

        @JsonProperty("is_mutex")
        @JSONField(name = "is_mutex")
        Boolean isMutex;

        @JsonProperty("ranges")
        @JSONField(name = "ranges")
        SceneDTO.Range ranges;

        @JsonProperty("group_description")
        @JSONField(name = "group_description")
        String groupDescription;

        @JsonProperty("label_names")
        @JSONField(name = "label_names")
        List<CreateOrUpdateTag.Arg> tagInfoList = Lists.newArrayList();
    }
}
