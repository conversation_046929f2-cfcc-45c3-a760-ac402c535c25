package com.facishare.paas.appframework.core.timezone

import com.facishare.paas.appframework.core.model.ObjectDataDocument
import com.facishare.paas.appframework.core.timezone.annotation.ObjectDataField
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import spock.lang.Specification

import java.util.function.Function

/**
 * GenerateByAI
 * 测试内容描述：AbstractObjectDataFieldConvert抽象类的单元测试
 */
class AbstractObjectDataFieldConvertTest extends Specification {

    // 测试用的具体实现类
    static class TestAbstractObjectDataFieldConvert extends AbstractObjectDataFieldConvert {
        @Override
        ObjectDataField.Type getType() {
            return ObjectDataField.Type.SINGLE
        }

        @Override
        def convert2SystemZone(value, Function function) {
            return value
        }

        @Override
        def convert2CustomZone(value, Function function) {
            return value
        }
    }

    def converter = new TestAbstractObjectDataFieldConvert()

    def "test CONVERT_MARK constant"() {
        expect: "常量值应该正确"
        AbstractObjectDataFieldConvert.CONVERT_MARK == "_convert_mark_"
    }

    def "test toObjectData with ObjectDataDocument"() {
        given: "创建ObjectDataDocument"
        def objectData = Mock(IObjectData)
        def document = Mock(ObjectDataDocument)
        document.toObjectData() >> objectData

        when: "转换ObjectDataDocument"
        def result = converter.toObjectData(document)

        then: "应该返回正确的IObjectData"
        result == objectData
    }

    def "test toObjectData with Map"() {
        given: "创建Map"
        def map = ["key": "value", "id": "123"]

        when: "转换Map"
        def result = converter.toObjectData(map)

        then: "应该返回IObjectData实例"
        result instanceof IObjectData
    }

    def "test toObjectData with JSON String"() {
        given: "创建JSON字符串"
        def jsonString = '{"id":"123","name":"test"}'

        when: "转换JSON字符串"
        def result = converter.toObjectData(jsonString)

        then: "应该返回IObjectData实例"
        result instanceof IObjectData
        result instanceof ObjectData
    }

    def "test toObjectData with unsupported type throws exception"() {
        given: "创建不支持的类型"
        def unsupportedValue = 123

        when: "转换不支持的类型"
        converter.toObjectData(unsupportedValue)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test toObjectData with null throws exception"() {
        when: "转换null值"
        converter.toObjectData(null)

        then: "应该抛出NullPointerException"
        thrown(NullPointerException)
    }

    def "test toDataList with valid List"() {
        given: "创建包含Map的List"
        def list = [
            ["id": "1", "name": "item1"],
            ["id": "2", "name": "item2"]
        ]

        when: "转换List"
        def result = converter.toDataList(list)

        then: "应该返回IObjectData列表"
        result instanceof List
        result.size() == 2
        result.every { it instanceof IObjectData }
    }

    def "test toDataList with empty List"() {
        given: "创建空List"
        def emptyList = []

        when: "转换空List"
        def result = converter.toDataList(emptyList)

        then: "应该返回空List"
        result instanceof List
        result.isEmpty()
    }

    def "test toDataList with non-List throws exception"() {
        given: "创建非List类型"
        def nonList = "not a list"

        when: "转换非List类型"
        converter.toDataList(nonList)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test toDetailData with valid Map"() {
        given: "创建包含List的Map"
        def detailMap = [
            "api1": [["id": "1"], ["id": "2"]],
            "api2": [["id": "3"]]
        ]

        when: "转换详情Map"
        def result = converter.toDetailData(detailMap)

        then: "应该返回正确的结构"
        result instanceof Map
        result.size() == 2
        result["api1"] instanceof List
        result["api1"].size() == 2
        result["api2"] instanceof List
        result["api2"].size() == 1
        result["api1"].every { it instanceof IObjectData }
        result["api2"].every { it instanceof IObjectData }
    }

    def "test toDetailData with null throws exception"() {
        when: "转换null值"
        converter.toDetailData(null)

        then: "应该抛出NullPointerException"
        thrown(NullPointerException)
    }

    def "test toDetailData with non-Map throws exception"() {
        given: "创建非Map类型"
        def nonMap = "not a map"

        when: "转换非Map类型"
        converter.toDetailData(nonMap)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test toCalculateDetailData with valid Map"() {
        given: "创建计算详情Map"
        def calculateMap = [
            "api1": [
                "key1": ["id": "1"],
                "key2": ["id": "2"]
            ],
            "api2": [
                "key3": ["id": "3"]
            ]
        ]

        when: "转换计算详情Map"
        def result = converter.toCalculateDetailData(calculateMap)

        then: "应该返回正确的结构"
        result instanceof Map
        result.size() == 2
        result["api1"] instanceof List
        result["api1"].size() == 2
        result["api2"] instanceof List
        result["api2"].size() == 1

        // 验证转换标记
        result["api1"].each { objectData ->
            assert objectData instanceof IObjectData
            assert objectData.get(AbstractObjectDataFieldConvert.CONVERT_MARK) != null
        }
    }

    def "test toCalculateDetail with valid Map"() {
        given: "创建计算Map"
        def calculateMap = [
            "api1": [
                "key1": ["id": "1"],
                "key2": ["id": "2"]
            ]
        ]

        when: "转换计算Map"
        def result = converter.toCalculateDetail(calculateMap)

        then: "应该返回正确的结构"
        result instanceof Map
        result.size() == 1
        result["api1"] instanceof Map
        result["api1"].size() == 2
        result["api1"]["key1"] instanceof IObjectData
        result["api1"]["key2"] instanceof IObjectData
    }

    def "test toEntity with ObjectDataDocument"() {
        given: "创建IObjectData和ObjectDataDocument"
        def objectData = Mock(IObjectData)
        def document = Mock(ObjectDataDocument)

        when: "转换为ObjectDataDocument实体"
        def result = converter.toEntity(objectData, document)

        then: "应该返回ObjectDataDocument"
        result instanceof ObjectDataDocument
    }

    def "test toEntity with Map"() {
        given: "创建IObjectData和Map"
        def objectData = new ObjectData()
        objectData.set("id", "123")
        objectData.set("name", "test")
        def map = [:]

        when: "转换为Map实体"
        def result = converter.toEntity(objectData, map)

        then: "应该返回Map"
        result instanceof Map
    }

    def "test toEntity with String"() {
        given: "创建IObjectData和String"
        def objectData = Mock(IObjectData)
        objectData.toJsonString() >> '{"id":"123"}'
        def string = ""

        when: "转换为String实体"
        def result = converter.toEntity(objectData, string)

        then: "应该返回JSON字符串"
        result == '{"id":"123"}'
    }

    def "test toEntity with unsupported type throws exception"() {
        given: "创建IObjectData和不支持的类型"
        def objectData = Mock(IObjectData)
        def unsupportedEntity = 123

        when: "转换为不支持的实体类型"
        converter.toEntity(objectData, unsupportedEntity)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test toEntity with null objectData throws exception"() {
        given: "创建null IObjectData"
        def entity = [:]

        when: "使用null IObjectData转换"
        converter.toEntity(null, entity)

        then: "应该抛出NullPointerException"
        thrown(NullPointerException)
    }

    def "test toListEntity with valid List"() {
        given: "创建IObjectData列表和List实体"
        def objectDataList = [Mock(IObjectData), Mock(IObjectData)]
        def listEntity = [[:], [:]]

        when: "转换为List实体"
        def result = converter.toListEntity(objectDataList, listEntity)

        then: "应该返回List"
        result instanceof List
    }

    def "test toListEntity with null throws exception"() {
        given: "创建IObjectData列表"
        def objectDataList = [Mock(IObjectData)]

        when: "使用null实体转换"
        converter.toListEntity(objectDataList, null)

        then: "应该抛出NullPointerException"
        thrown(NullPointerException)
    }

    def "test toListEntity with non-List throws exception"() {
        given: "创建IObjectData列表和非List实体"
        def objectDataList = [Mock(IObjectData)]
        def nonListEntity = "not a list"

        when: "转换为非List实体"
        converter.toListEntity(objectDataList, nonListEntity)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test toDetailEntity with valid Map"() {
        given: "创建详情数据和Map实体"
        def detail = [
            "api1": [Mock(IObjectData), Mock(IObjectData)],
            "api2": [Mock(IObjectData)]
        ]
        def mapEntity = [
            "api1": [[:], [:]],
            "api2": [[:]],
            "api3": [[]] // 这个在detail中不存在
        ]

        when: "转换为详情实体"
        def result = converter.toDetailEntity(detail, mapEntity)

        then: "应该返回Map"
        result instanceof Map
    }

    def "test toDetailEntity with null throws exception"() {
        given: "创建详情数据"
        def detail = ["api1": [Mock(IObjectData)]]

        when: "使用null实体转换"
        converter.toDetailEntity(detail, null)

        then: "应该抛出NullPointerException"
        thrown(NullPointerException)
    }

    def "test toDetailEntity with non-Map throws exception"() {
        given: "创建详情数据和非Map实体"
        def detail = ["api1": [Mock(IObjectData)]]
        def nonMapEntity = "not a map"

        when: "转换为非Map实体"
        converter.toDetailEntity(detail, nonMapEntity)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test toCalculateEntity with valid Map"() {
        given: "创建计算详情和Map实体"
        def calculateDetail = [
            "api1": [
                "key1": Mock(IObjectData),
                "key2": Mock(IObjectData)
            ]
        ]
        def mapEntity = [
            "api1": [
                "key1": [:],
                "key2": [:]
            ]
        ]

        when: "转换为计算实体"
        def result = converter.toCalculateEntity(calculateDetail, mapEntity)

        then: "应该返回Map"
        result instanceof Map
    }

    def "test toCalculateEntity with empty values"() {
        given: "创建包含空值的计算详情和实体"
        def calculateDetail = [
            "api1": [:]
        ]
        def mapEntity = [
            "api1": [:]
        ]

        when: "转换为计算实体"
        def result = converter.toCalculateEntity(calculateDetail, mapEntity)

        then: "应该正确处理空值"
        result instanceof Map
        result["api1"] == [:]
    }
}
