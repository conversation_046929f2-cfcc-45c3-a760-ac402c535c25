package com.facishare.paas.appframework.metadata.dto;

import com.facishare.paas.appframework.core.rest.BaseAPIResult;
import com.facishare.paas.appframework.metadata.importobject.ImportObjectProvider;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.*;

import java.util.List;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/03/16
 */
public interface ImportObjectModule {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        private String objectModule;
        private ImportObjectProvider.ImportModuleContext context;
    }

    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    class RestResult extends BaseAPIResult {
        private Result data;
    }

    @Data
    class Result {
        private List<ImportModule> importObjectModules;
    }

    @Data
    class ImportModule {
        private String objectApiName;
        private String objectCode;
        private String objectName;
        private String describeType;

        public static ImportModule of(IObjectDescribe describe) {
            ImportModule objectModule = new ImportModule();
            objectModule.setDescribeType(describe.getDefineType());
            objectModule.setObjectApiName(describe.getApiName());
            objectModule.setObjectCode(describe.getApiName());
            objectModule.setObjectName(describe.getDisplayName());
            return objectModule;
        }
    }
}
