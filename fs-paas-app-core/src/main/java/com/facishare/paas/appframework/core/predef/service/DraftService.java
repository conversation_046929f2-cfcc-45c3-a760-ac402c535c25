package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.InfraServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.ObjectDataDraftDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.draft.*;
import com.facishare.paas.appframework.core.predef.service.dto.draft.DraftData.Arg;
import com.facishare.paas.appframework.core.predef.service.dto.draft.DraftData.Result;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IObjectDataDraft;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Service
@ServiceModule("draft")
public class DraftService {

    @Autowired
    private ObjectDataDraftService objectDataDraftService;
    @Autowired
    private MetaDataService metaDataService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private InfraServiceFacadeImpl infraServiceFacade;

    @ServiceMethod("list")
    public Result findDraftListByUser(Arg arg, ServiceContext context) {
        List<IObjectDataDraft> dataDrafts;
        Integer totalNum = null;
        boolean notPaging = arg.getLimit() == 0 && arg.getOffset() == 0;
        List<String> dataSources = CollectionUtils.empty(arg.getSpecifyDataSource()) ? Lists.newArrayList("Add", "Edit") : arg.getSpecifyDataSource();
        if (notPaging) {
            dataDrafts = objectDataDraftService.findDraftByUser(context.getUser());
        } else {
            if (BooleanUtils.isNotTrue(arg.getIncludeEdit())) {
                dataSources.remove("Edit");
            }
            QueryResult<IObjectDataDraft> draftQueryResult = objectDataDraftService.findDraftByTemplate(context.getUser(), arg.getDescribeApiName(), arg.getRecordType(), dataSources, arg.getLimit(), arg.getOffset());
            totalNum = draftQueryResult.getTotalNumber();
            dataDrafts = draftQueryResult.getData();
        }
        List<ObjectDataDraftDocument> result = Lists.newArrayList();
        boolean ignoreData = BooleanUtils.isTrue(arg.getIgnoreData());
        if (notPaging) {
            dataDrafts = filterDataDrafts(dataDrafts, arg.getDescribeApiName(), arg.getRecordType());
        }
        Set<String> apiNames = dataDrafts.stream().map(IObjectDataDraft::getDescribeApiName)
                .filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        Map<String, IObjectDescribe> objectDescribeMap = describeLogicService.findObjectsWithoutCopy(context.getTenantId(), apiNames);
        for (IObjectDataDraft draft : dataDrafts) {
            IObjectData masterDraftData = draft.getMasterDraftData();
            if (Objects.isNull(masterDraftData)) {
                continue;
            }
            String dataSource = draft.getDataSource();
            IObjectDescribe objectDescribe = objectDescribeMap.get(masterDraftData.getDescribeApiName());
            if ((BooleanUtils.isNotTrue(arg.getIncludeEdit()) && StringUtils.equals(dataSource, "Edit")) ||
                    (StringUtils.isNotEmpty(dataSource) && !dataSources.contains(dataSource))) {
                continue;
            }
            Object objectDisplayName = Objects.isNull(objectDescribe) ? masterDraftData.get(IObjectDescribe.DISPLAY_NAME) : objectDescribe.getDisplayName();
            draft.set("draft_owner_object", objectDisplayName);
            masterDraftData.set("draft_owner_object", objectDisplayName);
            draft.set(IObjectDescribe.RECORD_TYPE, masterDraftData.getRecordType());
            String recordTypeLabel = null;
            if (Objects.nonNull(objectDescribe)) {
                Optional<RecordTypeFieldDescribe> recordTypeFieldDescribe = ObjectDescribeExt.of(objectDescribe).getRecordTypeField();
                if (recordTypeFieldDescribe.isPresent()) {
                    Map<String, String> recordTypeMap = recordTypeFieldDescribe.get().getRecordTypeOptions().stream()
                            .collect(Collectors.toMap(IRecordTypeOption::getApiName, IRecordTypeOption::getLabel));
                    recordTypeLabel = recordTypeMap.getOrDefault(masterDraftData.getRecordType(), (String) masterDraftData.get("record_type__r"));
                }
            }
            draft.set("record_type__r", recordTypeLabel);

            if (ignoreData) {
                if (StringUtils.equals(dataSource, "Edit")) {
                    draft.set("master_id", masterDraftData.getId());
                }
                draft.setSlaveDraftData(null);
                draft.set("master_draft_data", Maps.newHashMap());
            }
            
            // 处理对象草稿数据中存在签名图片和附件
            dealNPathSign(objectDescribe, draft, context);

            result.add(ObjectDataDraftDocument.of(draft));
        }
        return Result.builder().drafts(result).totalNum(totalNum).build();
    }

    private List<IObjectDataDraft> filterDataDrafts(List<IObjectDataDraft> dataDrafts, String describeApiName, String recordType) {
        // 移动端不支持对象过滤
        if (RequestUtil.isMobileRequest() || RequestUtil.isMobileDeviceRequest()) {
            List<String> notSupportObjectsInDraft = AppFrameworkConfig.getNotSupportObjectsInMobileDraft();
            dataDrafts = dataDrafts.stream()
                    .filter(x -> !notSupportObjectsInDraft.contains(x.getDescribeApiName()))
                    .collect(Collectors.toList());
        }
        if (StringUtils.isNotEmpty(describeApiName)) {
            if (StringUtils.isNotEmpty(recordType)) {
                dataDrafts = dataDrafts.stream()
                        .filter(x -> describeApiName.equals(x.getDescribeApiName())
                                && recordType.equals(x.getMasterDraftData().getRecordType()))
                        .collect(Collectors.toList());
            } else {
                dataDrafts = dataDrafts.stream()
                        .filter(x -> describeApiName.equals(x.getDescribeApiName()))
                        .collect(Collectors.toList());
            }
        }
        return dataDrafts;
    }
    
    private void dealNPathSign(IObjectDescribe master, IObjectDataDraft draft, ServiceContext context) {
        IObjectData masterDraft = draft.getMasterDraftData();
        dealNPathSign(master, Lists.newArrayList(masterDraft), context);
        Map<String, List<IObjectData>> detailDraftsMap = draft.getSlaveDraftData();
        if (MapUtils.isEmpty(detailDraftsMap)) {
            return;
        }
        Set<String> detailApis = detailDraftsMap.keySet();
        Map<String, IObjectDescribe> detailDescMap = describeLogicService.findObjectsWithoutCopy(context.getTenantId(), detailApis);
        detailDraftsMap.forEach((detailApi, drafts) -> {
            if (CollectionUtils.empty(drafts)) {
                return;
            }
            IObjectDescribe draftDesc = detailDescMap.get(detailApi);
            if (Objects.isNull(draftDesc)) {
                return;
            }
            dealNPathSign(draftDesc, drafts, context);
        });
    }
    
    private void dealNPathSign(IObjectDescribe objDesc, List<IObjectData> dataDrafts, ServiceContext context) {
        IActionContext ctx = ActionContextExt.of(context.getUser()).getContext();
        infraServiceFacade.getFileStoreService().generateNPathSignedUrl(ctx,
                AppFrameworkConfig.signedUrlFieldTypes(ctx.getEnterpriseId()), objDesc, dataDrafts);
    }

    @ServiceMethod("delete_by_user")
    public DeleteDraftByUser.Result deleteDraftByUser(DeleteDraftByUser.Arg arg, ServiceContext context) {
        boolean success = objectDataDraftService.deleteDraftByUser(context.getUser());
        return DeleteDraftByUser.Result.builder().success(success).build();
    }

    @ServiceMethod("delete_by_ids")
    public DeleteDraftByIds.Result deleteDraftByIds(DeleteDraftByIds.Arg arg, ServiceContext context) {
        if (CollectionUtils.empty(arg.getDraftIdList())) {
            return DeleteDraftByIds.Result.builder().success(true).build();
        }

        objectDataDraftService.deleteDraftByIds(context.getUser().getTenantId(), Lists.newArrayList(arg.getDraftIdList()));
        return DeleteDraftByIds.Result.builder().success(true).build();
    }

    @ServiceMethod("draft_function_check")
    public DraftFuncCheck.Result draftFunctionCheck(DraftFuncCheck.Arg arg, ServiceContext context) {
        if (StringUtils.isEmpty(arg.getDraftId())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        Optional<IObjectDataDraft> draft = objectDataDraftService.findDraftById(context.getTenantId(), arg.getDraftId());
        if (!draft.isPresent()) {
            throw new ValidateException(I18N.text(I18NKey.draft_data_deleted));
        }
        IObjectData masterDraftData = draft.get().getMasterDraftData();
        if (Objects.nonNull(masterDraftData)) {
            String dataSource = draft.get().getDataSource();
            String id = masterDraftData.getId();
            if (StringUtils.isNotEmpty(id) && StringUtils.equals(dataSource, "Edit")) {
                IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopy(context.getTenantId(), masterDraftData.getDescribeApiName());
                IObjectData objectData = metaDataService.findObjectData(context.getUser(), id, objectDescribe);
                //流程草稿不需要校验数据的锁定状态
                if (!ObjectDataDraftExt.of(draft.get()).isFlowBizType() && ObjectDataExt.of(objectData).isLock()) {
                    throw new ValidateException(I18N.text(I18NKey.DATA_HAS_BEEN_LOCKED_PLEASE_UNLOCK));
                }
                functionPrivilegeService.doFunPrivilegeCheck(context.getUser(), masterDraftData.getDescribeApiName(), Lists.newArrayList(dataSource));
                Map<String, Permissions> prmissionsMap = metaDataService.checkPrivilege(context.getUser(), Lists.newArrayList(objectData), objectDescribe, dataSource);
                if (Permissions.READ_WRITE != prmissionsMap.get(objectData.getId())) {
                    throw new ValidateException(I18N.text(I18NKey.CANNOT_EDIT_WITHOUT_DATA_PERMISSION));
                }
            }
        }
        return DraftFuncCheck.Result.buildResult(true);
    }

    @ServiceMethod("find_draftid_by_biztype_and_bizid")
    public FindDraftIdByBizTypeAndBizId.Result findDraftIdByBizTypeAndBizId(FindDraftIdByBizTypeAndBizId.Arg arg, ServiceContext context) {
        IObjectDataDraft draft = objectDataDraftService.findByBizTypeAndBizId(context.getUser(), arg.getBizType(), arg.getBizId());
        String draftId = Objects.isNull(draft) ? null : draft.getId();
        String recordType = Objects.isNull(draft) || Objects.isNull(draft.getMasterDraftData()) ? null : draft.getMasterDraftData().getRecordType();
        return FindDraftIdByBizTypeAndBizId.Result.builder().draftId(draftId).recordType(recordType).build();
    }

    @ServiceMethod("delete_draft_by_biztype_and_bizids")
    public DeleteDraftByBizTypeAndBizIds.Result deleteDraftByBizTypeAndBizIds(DeleteDraftByBizTypeAndBizIds.Arg arg, ServiceContext context) {
        objectDataDraftService.deleteByBizTypeAndBizIds(context.getTenantId(), arg.getBizType(), arg.getBizIds());
        return DeleteDraftByBizTypeAndBizIds.Result.builder().success(true).build();
    }

    @ServiceMethod("exist_previous_draft")
    public DraftTimestamp.Result existPreviousDraft(DraftTimestamp.Arg arg, ServiceContext context) {
        if (Objects.isNull(arg.getTimestamp())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        boolean existed = objectDataDraftService.existPreviousDraft(context.getUser(), arg.getTimestamp());
        return DraftTimestamp.Result.builder().ret(existed).build();
    }

    @ServiceMethod("delete_by_timestamp")
    public DraftTimestamp.Result deleteByTimestamp(DraftTimestamp.Arg arg, ServiceContext context) {
        if (Objects.isNull(arg.getTimestamp())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        boolean deleted = objectDataDraftService.deleteDraftByTimestamp(context.getUser(), arg.getTimestamp());
        return DraftTimestamp.Result.builder().ret(deleted).build();
    }

}
