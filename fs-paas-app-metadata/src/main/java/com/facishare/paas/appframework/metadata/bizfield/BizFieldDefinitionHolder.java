package com.facishare.paas.appframework.metadata.bizfield;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.domain.DomainPluginDefinition.Resource;
import com.fxiaoke.release.GrayRule;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2023/9/11.
 */
@Slf4j
public class BizFieldDefinitionHolder {
    private static Map<String, BizFieldDefinition> definitionMap;
    private static Map<String, Map<String, GrayRule>> grayRuleMap;

    static {
        ConfigFactory.getConfig("fs-paas-biz-field-config", config -> {
            try {
                log.info("reload fs-paas-biz-field-config:{}", config.getString());
                reload(config);
            } catch (Exception e) {
                log.error("reload fs-paas-biz-field-config failed", e);
            }
        });
    }

    private static void reload(IConfig config) {
        String content = config.getString();
        if (Strings.isNullOrEmpty(content)) {
            definitionMap = Maps.newLinkedHashMap();
            grayRuleMap = Maps.newHashMap();
            return;
        }
        ConfigData configData = JSON.parseObject(content, ConfigData.class);

        //解析业务字段定义
        Map<String, BizFieldDefinition> newDefinitionMap = Maps.newLinkedHashMap();
        if (CollectionUtils.notEmpty(configData.getBizFieldDefinitions())) {
            configData.getBizFieldDefinitions().forEach(definition -> {
                if (Strings.isNullOrEmpty(definition.getApiName())) {
                    log.warn("apiName is empty:{}", definition);
                    return;
                }
                if (newDefinitionMap.containsKey(definition.getApiName())) {
                    log.warn("apiName is already existed:{}", definition);
                    return;
                }
                newDefinitionMap.put(definition.getApiName(), definition);
            });
        }
        definitionMap = newDefinitionMap;

        //解析灰度规则
        Map<String, Map<String, GrayRule>> newGrayRuleMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(configData.getGrayRules())) {
            newGrayRuleMap = UdobjGrayConfig.parseGrayRules(configData.getGrayRules());
        }
        grayRuleMap = newGrayRuleMap;
    }

    public static List<Resource> getResources(String bizFieldApiName, String actionCode, String agentType) {
        if (CollectionUtils.empty(definitionMap)) {
            return null;
        }
        BizFieldDefinition definition = definitionMap.get(bizFieldApiName);
        if (Objects.isNull(definition)) {
            return null;
        }
        List<Resource> resources = definition.getResources(actionCode, agentType);
        return Objects.isNull(resources) ? null :
                resources.stream()
                        .map(Resource::copy)
                        .collect(Collectors.toList());
    }

    public static List<BizFieldDefinition> getAllBizFieldDefinitions(String tenantId, String objectApiName) {
        String formatObjectApiName = Strings.isNullOrEmpty(objectApiName) ? UdobjGrayConfigKey.UDOBJ : objectApiName;
        return Lists.newArrayList(definitionMap.values()).stream()
                .filter(x -> {
                    if (CollectionUtils.empty(grayRuleMap)) {
                        return false;
                    }
                    Map<String, GrayRule> bizFieldGrayRuleMap = grayRuleMap.get(x.getApiName());
                    if (CollectionUtils.empty(bizFieldGrayRuleMap)) {
                        return false;
                    }
                    GrayRule grayRule = bizFieldGrayRuleMap.get(formatObjectApiName);
                    if (Objects.isNull(grayRule) && isCustomObject(formatObjectApiName)) {
                        grayRule = bizFieldGrayRuleMap.get(UdobjGrayConfigKey.UDOBJ);
                    }
                    if (Objects.isNull(grayRule)) {
                        grayRule = bizFieldGrayRuleMap.get(UdobjGrayConfigKey.DEFAULT);
                    }
                    return Objects.nonNull(grayRule) && grayRule.isAllow(tenantId);
                })
                .collect(Collectors.toList());
    }

    private static boolean isCustomObject(String objectApiName) {
        return UdobjGrayConfig.isCustomObject(objectApiName);
    }

    @Data
    private static class ConfigData {
        private List<BizFieldDefinition> bizFieldDefinitions;
        private Map<String, Map<String, String>> grayRules;
    }
}
