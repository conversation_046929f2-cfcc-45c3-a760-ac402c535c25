/**
 * <AUTHOR>
 * @codeName OnlineDocWpsPersonal
 * @description 新建
 * @createTime 2024-06-24
 */
class OnlineDocWpsPersonal implements OnlineDocPersonalPlugin {

  //用户授权url
  private static String AUTH_CODE_URL = "https://openapi.wps.cn/oauthapi/v2/authorize?response_type=code&autologin=false&scope=cloud_file"
  //获取token url
  private static String QUERY_TOKEN_URL = "https://openapi.wps.cn/oauthapi/v2/token?"
  //刷新token url
  private static String REFRESH_URL = "https://openapi.wps.cn/oauthapi/v2/token/refresh?"
  //获取文件列表url
  private static String QUERY_FILE_LIST_URL = "https://openapi.wps.cn/oauthapi/v3/app/files/list?order_by=mtime&order=DESC"
  //获取预览url
  private static String PREVIEW_URL = "https://openapi.wps.cn/oauthapi/v3/app/files/link?"
  //授权回调地址后缀
  private static String CALLBACK_URL_SUFFIX = "/wps/authRedirect"


  //保存token信息到本地缓存,wps默认24小时
  private void saveAccessToken2Cache(String tenantId, String userId, String pluginApiName, Map<String, Object> runtimeData, int expiresSecond) {
    Map tokenMap = [:]
    tokenMap.putAll(runtimeData)
    tokenMap.put("access_token_expires_millis", DateTime.now().toTimestamp() + (expiresSecond - 30) * 1000)

    String key = "OnlineDocWpsPer_token_" + userId + "_" + pluginApiName
    Cache cache = Fx.cache.getDefaultCache()
    String value = Fx.json.toJson(tokenMap)
    cache.put(key, value, expiresSecond)
    log.info("saveAccessToken2Cache key:" + key)
  }

  //获取token信息从本地缓存中
  private String getAccessTokenFromCache(String tenantId, String userId, String pluginApiName) {
    String key = "OnlineDocWpsPer_token_" + userId + "_" + pluginApiName
    Cache cache = Fx.cache.getDefaultCache()
    String value = cache.get(key) as String
    if (value == null) {
      log.info("getAccessTokenFromCache key:" + key + " value:null")
      return null
    }
    Map tokenMap = Fx.json.parse(value)
    String token = tokenMap["access_token"] as String
    Long expired = tokenMap["access_token_expires_millis"] as Long
    if (expired <= DateTime.now().toTimestamp()) {
      log.info("getAccessTokenFromCache key:" + key + " value:expired")
      return null
    }
    log.info("getAccessTokenFromCache key:" + key)
    return token
  }

  //获取个人授权url
  public GetPersonalAuthUrl.Result getPersonalAuthUrl(GetPersonalAuthUrl.Arg arg, FunctionContext context) {
    //标准日志，请勿删除
    log.info("getPersonalAuthUrl arg:" + arg)

    /*此处开始加业务*/
    String appId = arg.getDevInfo().get("appId")
    String callbackUrl = arg.getDevInfo().get("callbackUrl") + CALLBACK_URL_SUFFIX

    //拼装纷享业务参数
    Map<String, String> authMap = [:]
    authMap.put("pluginApiName", arg.getPluginApiName())
    authMap.put("enterpriseId", context.getTenantId())
    authMap.put("employeeId", context.getUserId())
    //拼接如state中
    String state = Fx.crypto.base64.encode(Fx.json.toJson(authMap) as byte[])
    String authUrl = AUTH_CODE_URL + "&appid=" + appId + "&redirect_uri=" + callbackUrl + "&state=" + state

    GetPersonalAuthUrl.Result result = new GetPersonalAuthUrl.Result()
    result.setErrorCode(0)
    result.setAuthUrl(authUrl)
    //标准日志，请勿删除
    log.info("getPersonalAuthUrl result:" + result)
    return result
  }

  //处理callback回来的数据
  public ActionCallback.Result actionCallback(ActionCallback.Arg arg, FunctionContext context) {
    //标准日志，请勿删除
    log.info("actionCallback arg:" + arg)

    /*此处开始加业务*/
    //获取用户授权token
    ActionCallback.Result result = queryAccessToken(arg, context)
    //标准日志，请勿删除
    log.info("actionCallback result:" + result)
    return result
  }

  //请求文件列表
  public QueryFileList.Result queryFileList(QueryFileList.Arg arg, FunctionContext context) {
    //标准日志，请勿删除
    log.info("queryFileList arg:" + arg)

    /*此处开始加业务*/
    Map accessTokenMap = getOrRefreshAccessToken(arg.getPluginApiName(), arg.getDevInfo(), arg.getAuthRuntimeData(), context)
    String accessToken = accessTokenMap.get("accessToken") as String
    Integer errorCode = accessTokenMap.get("errorCode") as Integer
    String errorMessage = accessTokenMap.get("errorMessage") as String
    if (errorCode != 0) {
      //返回失败结果
      return buildQueryFileListFailedResult(arg.getPluginApiName(), errorCode, errorMessage, arg.getDevInfo(), context)
    }
    //查询文件列表
    Map<String, Object> extraInfo = (arg.getExtraInfo() == null) ? [:] : arg.getExtraInfo()
    if (extraInfo.isEmpty()) {
      extraInfo["pageNumber"] = 0
      extraInfo["open_fileid"] = "0"
    }
    String openId = (extraInfo["open_fileid"] == null) ? "0" : extraInfo["open_fileid"] as String
    Integer pageNumber = (extraInfo["pageNumber"] == null) ? 0 : extraInfo["pageNumber"] as Integer

    //加载下一页
    if (arg.isNeedNextPage()) {
      pageNumber += 1
    }
    //进入指定文件夹
    if (arg.getFolder() != null) {
      openId = arg.getFolder().getFileId()
    }
    extraInfo["pageNumber"] = pageNumber
    extraInfo["open_fileid"] = openId
    //请求文件列表清单
    Map<String, Object> fileListMap = queryFiles(arg.getPluginApiName(), arg.getDevInfo(), accessToken, pageNumber, openId)
    errorCode = fileListMap.get("errorCode") as int
    errorMessage = fileListMap.get("errorMessage") as String
    List<Map> fileList = fileListMap.get("fileList") as List
    if (errorCode != 0) {
      //返回失败结果
      return buildQueryFileListFailedResult(arg.getPluginApiName(), errorCode, errorMessage, arg.getDevInfo(), context)
    }
    //返回结果
    QueryFileList.Result result = new QueryFileList.Result()
    result.setErrorCode(errorCode)
    result.setErrorMessage(errorMessage)
    result.setFileList(fileList)
    result.setHasMore(fileListMap["hasMore"] as boolean)
    result.setExtraInfo(extraInfo)
    //标准日志，请勿删除
    log.info("queryFileList result:" + result)
    return result
  }

  private QueryFileList.Result buildQueryFileListFailedResult(String pluginApiName, int errorCode, String errorMessage, Map devInfo, FunctionContext context) {
    //refreshToken失效了，用户授权过期
    if (errorCode == 100050 || errorCode == 100013) {
      //检查是否需要授权
      GetPersonalAuthUrl.Arg authUrlArg = new GetPersonalAuthUrl.Arg()
      authUrlArg.setPluginApiName(pluginApiName)
      authUrlArg.setDevInfo(devInfo)
      GetPersonalAuthUrl.Result authUrlResult = getPersonalAuthUrl(authUrlArg, context)
      //
      QueryFileList.Result result = new QueryFileList.Result()
      result.setErrorCode(errorCode)
      result.setErrorMessage(errorMessage)
      result.setAuthUrl(authUrlResult.getAuthUrl())
      return result
    }
    Fx.message.throwException(errorMessage)
  }

  //获取预览url，wps使用
  public GetPreviewUrl.Result getPreviewUrl(GetPreviewUrl.Arg arg, FunctionContext context) {
    //标准日志，请勿删除
    log.info("getPreviewUrl arg:" + arg)

    /*此处开始加业务*/
    Map accessTokenMap = getOrRefreshAccessToken(arg.getPluginApiName(), arg.getDevInfo(), arg.getAuthRuntimeData(), context)
    String accessToken = accessTokenMap.get("accessToken") as String
    Integer errorCode = accessTokenMap.get("errorCode") as Integer
    String accessErrorMessage = accessTokenMap.get("errorMessage") as String
    if (errorCode != 0) {
      Fx.message.throwException(accessErrorMessage)
    }
    String fileId = arg.getFile().getFileId()
    String appId = arg.getDevInfo().get("appId")
    String getPreviewUrl = PREVIEW_URL + "appid=" + appId + "&access_token=" + accessToken + "&open_fileid=" + fileId
    Map headers = ["Content-Type" : "application/json"]
    def (Boolean error, HttpResult httpResult, String errorMessage) = Fx.http.get(getPreviewUrl, headers, 10000, false, 0)
    log.info("getPreviewUrl request finish:" + httpResult)
    //检查请求错误
    if (error || httpResult == null) {
      log.info("getPreviewUrl request null error :" + errorMessage)
      Fx.message.throwException(errorMessage)
    }
    Map contentMap = null
    if (httpResult.statusCode == 200) {
      contentMap = httpResult.content as Map
    } else {
      contentMap = Fx.json.parse(httpResult.content as String)
    }
    Integer bizErrorCode = contentMap.get("result") as Integer
    String bizErrorMessage = contentMap.get("msg") as String
    //检查请求业务错误
    if (bizErrorCode != 0) {
      log.info("getPreviewUrl request biz failed error:" + contentMap)
      try {
        Map msgMap = Fx.json.parse(bizErrorMessage)
        String msg = msgMap["msg"]
        Fx.message.throwException((msg != null) ? msg : bizErrorMessage)
      } catch (Exception e) {
        log.info("getPreviewUrl request biz failed ex:" + e.getMessage())
        Fx.message.throwException(bizErrorMessage)
      }
    }
    //返回结果
    GetPreviewUrl.Result result = new GetPreviewUrl.Result()
    result.setErrorCode(0)
    result.setFileUrl(contentMap.get("link_url") as String)
    //标准日志，请勿删除
    log.info("getPreviewUrl result:" + result)
    return result
  }

  //获取runtimeData
  private Map getOrRefreshAccessToken(String pluginApiName, Map devInfo, Map authRuntimeData, FunctionContext context) {
    String accessTokenInCache = getAccessTokenFromCache(context.getTenantId(), context.getUserId(), pluginApiName)
    if (accessTokenInCache == null) {
      Map<String, Object> refreshMap = refreshAccessToken(pluginApiName, devInfo, authRuntimeData, context)
      return refreshMap
    }
    Map<String, Object> refreshMap = [:]
    refreshMap.put("errorCode", 0)
    refreshMap.put("accessToken", accessTokenInCache)
    return refreshMap
  }

  //请求access token
  private ActionCallback.Result queryAccessToken(ActionCallback.Arg arg, FunctionContext context) {
    String code = arg.getCode()
    String appId = arg.getDevInfo().get("appId")
    String appKey = arg.getDevInfo().get("appKey")
    String queryTokenUrl = QUERY_TOKEN_URL + "appid=" + appId + "&appkey=" + appKey + "&code=" + code
    Map headers = ["Content-Type" : "application/json"]
    log.info("queryAccessToken request begin:" + queryTokenUrl)
    def (Boolean error, HttpResult httpResult, String errorMessage) = Fx.http.get(queryTokenUrl, headers, 10000, false, 0)
    log.info("queryAccessToken request finish:" + httpResult)
    //检查请求错误
    if (error || httpResult == null) {
      log.info("queryAccessToken request null error :" + errorMessage)
      ActionCallback.Result result = new ActionCallback.Result()
      result.setErrorCode(-1)
      result.setErrorMessage("request error:" + errorMessage)
      return result
    }
    Map contentMap = null
    if (httpResult.statusCode == 200) {
      contentMap = httpResult.content as Map
    } else {
      contentMap = Fx.json.parse(httpResult.content as String)
    }
    Integer bizErrorCode = contentMap.get("result") as Integer
    String bizErrorMessage = contentMap.get("msg") as String
    //检查请求业务错误
    if (bizErrorCode != 0) {
      log.info("queryAccessToken request biz failed error:" + contentMap)
      ActionCallback.Result result = new ActionCallback.Result()
      result.setErrorCode(bizErrorCode)
      result.setErrorMessage(bizErrorMessage)
      return result
    }
    //补充过期时间戳
    Map tokenMap = contentMap["token"] as Map
    Integer expiresSecond = tokenMap["expires_in"] as Integer
    //保存到本地缓存
    saveAccessToken2Cache(context.getTenantId(), context.getUserId(), arg.getPluginApiName(), tokenMap, expiresSecond)
    //返回结果
    ActionCallback.Result result = new ActionCallback.Result()
    result.setErrorCode(0)
    result.setRuntimeData(tokenMap)
    result.setExpiredTime(DateTime.now().toTimestamp() + 90 * 24 * 3600 * 1000L)
    return result
  }

  //刷新access token
  private Map<String, Object> refreshAccessToken(String pluginApiName, Map<String, String> devInfo, Map<String, Object> authRuntimeData, FunctionContext context) {
    log.info("refreshAccessToken arg pluginApiName:" + pluginApiName)
    Map authMap = authRuntimeData
    if (authMap.isEmpty()) {
      //从未授权，模拟金山错误码，下发授权url
      Map<String, Object> resultMap = [:]
      resultMap.put("errorCode", 100050)
      resultMap.put("errorMessage", "never to auth")
      log.info("refreshAccessToken failed resultMap:" + resultMap)
      return resultMap
    }
    String appId = devInfo.get("appId")
    String appKey = devInfo.get("appKey")
    String refreshToken = authMap.get("refresh_token")
    String queryRefreshTokenUrl = REFRESH_URL + "appid=" + appId + "&appkey=" + appKey + "&refresh_token=" + refreshToken
    Map headers = ["Content-Type" : "application/json"]
    Map requestBody = [:]
    log.info("refreshAccessToken request begin:" + queryRefreshTokenUrl)
    def (Boolean error, HttpResult httpResult, String errorMessage) = Fx.http.post(queryRefreshTokenUrl, headers, requestBody, 10000, false, 0)
    log.info("refreshAccessToken request finish:" + httpResult)
    //检查请求错误
    if (error || httpResult == null) {
      log.info("refreshAccessToken request null error :" + errorMessage)
      Map<String, Object> resultMap = [:]
      resultMap.put("errorCode", -1)
      resultMap.put("errorMessage", "request error:" + errorMessage)
      return resultMap
    }
    Map contentMap = null
    if (httpResult.statusCode == 200) {
      contentMap = httpResult.content as Map
    } else {
      contentMap = Fx.json.parse(httpResult.content as String)
    }
    Integer bizErrorCode = contentMap.get("result") as Integer
    String bizErrorMessage = contentMap.get("msg") as String
    //检查请求业务错误
    if (bizErrorCode != 0) {
      Map<String, Object> resultMap = [:]
      resultMap.put("errorCode", bizErrorCode)
      resultMap.put("errorMessage", bizErrorMessage)
      log.info("refreshAccessToken request biz failed error:" + contentMap)
      return resultMap
    }
    //补充过期时间戳
    Map tokenMap = contentMap["token"] as Map
    String accessToken = tokenMap["access_token"] as String
    Integer expiresSecond = tokenMap["expires_in"] as Integer
    //保存到本地缓存
    saveAccessToken2Cache(context.getTenantId(), context.getUserId(), pluginApiName, tokenMap, expiresSecond)
    //返回结果
    Map<String, Object> resultMap = [:]
    resultMap.put("errorCode", 0)
    resultMap.put("accessToken", accessToken)
    log.info("refreshAccessToken finish success:" + resultMap.size())
    return resultMap
  }

  //查询文件列表
  private Map<String, Object> queryFiles(String pluginApiName, Map<String, String> devInfo, String accessToken, int pageNumber, String openId) {
    int pageSize = 20
    String appId = devInfo.get("appId")
    String queryFileListUrl = QUERY_FILE_LIST_URL + "&access_token=" + accessToken + "&appid=" + appId + "&open_parentid=" + openId + "&offset=" + pageNumber * pageSize + "&count=" + pageSize
    Map headers = ["Content-Type" : "application/json"]
    //发起请求
    log.info("queryFiles request begin:" + queryFileListUrl)
    def (Boolean error, HttpResult httpResult, String errorMessage) = Fx.http.get(queryFileListUrl, headers, 10000, false, 0)
    log.info("queryFiles request finish:" + httpResult)
    //检查请求错误
    if (error || httpResult == null) {
      log.info("queryFiles request null error :" + errorMessage)
      Map<String, Object> resultMap = [:]
      resultMap.put("errorCode", -1)
      resultMap.put("errorMessage", "request error:" + errorMessage)
      return resultMap
    }
    Map contentMap = null
    if (httpResult.statusCode == 200) {
      contentMap = httpResult.content as Map
    } else {
      contentMap = Fx.json.parse(httpResult.content as String)
    }
    Integer bizErrorCode = contentMap.get("result") as Integer
    String bizErrorMessage = contentMap.get("msg") as String
    //检查请求业务错误
    if (bizErrorCode != 0) {
      log.info("queryFiles request biz failed error:" + contentMap)
      Map<String, Object> resultMap = [:]
      resultMap.put("errorCode", bizErrorCode)
      resultMap.put("errorMessage", bizErrorMessage)
      return resultMap
    }
    List<Map> fileList = contentMap.get("files") as List
    Map<String, Object> resultMap = [:]
    resultMap.put("errorCode", 0)
    resultMap.put("fileList", convert2FileInfo(pluginApiName, fileList))
    resultMap.put("hasMore", fileList == null ? false : fileList.size() >= pageSize)
    return resultMap
  }

  //转换成规范结构
  private List<Map> convert2FileInfo(String pluginApiName, List<Map> fileList) {
    List<Map> fileInfoList = []
    fileList.each() { item ->
      Map fileInfo = [:]
      fileInfo.put("fileId",     item.get("open_fileid"))
      fileInfo.put("fileName",   item.get("file_name"))
      fileInfo.put("fileExt",    findFileExtension(item.get("file_name") as String))
      fileInfo.put("fileType",   convertFileType(item.get("file_type") as String))
      fileInfo.put("fileSize",   item.get("file_size"))
      fileInfo.put("createTime", 1000 * (item.get("ctime") as long))
      fileInfo.put("updateTime", 1000 * (item.get("mtime") as long))
      fileInfo.put("pluginApiName", pluginApiName)
      fileInfoList.add(fileInfo)
    }
    return fileInfoList
  }

  String findFileExtension(String fileName) {
    def parts = fileName.split("\\.")
    if (parts.size() > 1) {
      return parts[parts.size() - 1]
    }
    return null
  }

  //检查文件类型，只支持文件夹、文件、未知
  private String convertFileType(String fileType) {
    switch(fileType) {
      case "file":
      case "sharefile":
        return "file"
      case "folder":
      case "linkfolder":
      case "sharefolder":
        return "folder"
      default:
        return "unknown"
    }
  }

  static void main(String[] args) {

  }
}