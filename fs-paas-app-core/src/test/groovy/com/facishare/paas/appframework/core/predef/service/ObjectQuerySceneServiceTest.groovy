package com.facishare.paas.appframework.core.predef.service


import com.alibaba.fastjson.JSONArray
import com.facishare.paas.appframework.common.util.Tuple
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import spock.lang.Specification

/**
 * create by z<PERSON><PERSON> on 2019/11/04
 */
class ObjectQuerySceneServiceTest extends Specification {
    ObjectQuerySceneService objectQuerySceneService
    ServiceFacade serviceFacade

    def setup() {
        serviceFacade = Mock(ServiceFacade)
        objectQuerySceneService = new ObjectQuerySceneService("serviceFacade": serviceFacade)
    }

    def "Test Find Field Width Config"() {
        expect:
        1 == 1
        /*
        given:
        def serviceContext = buildServiceContext(buildUser(tenantId, userId))
        def arg = FindFieldWidthConfig.Arg.of(describeApiName, extendAttribute)
        when:
        def result = objectQuerySceneService.findFieldWidthConfig(arg, serviceContext)
        then:
        1 * serviceFacade.findFieldWidthConfig(_, _, _) >> {
            return createFieldWidthConfig(fieldWidthConfigJson)
        }
        noExceptionThrown()
        println JSON.toJSONString(result)
        JSON.toJSONString(result.getVisibleFieldsWidth()) == fieldWidthConfigJson
        where:
        tenantId | userId | describeApiName   | extendAttribute | fieldWidthConfigJson
        "71698"  | "1000" | "object_nzufh__c" | null            | '''[{"field_name":"name","width":247},{"field_name":"field_b86Wz__c","width":145},{"field_name":"field_U2Cc1__c","width":145},{"field_name":"field_GNxmV__c","width":145},{"field_name":"field_5aMIb__c","width":145},{"field_name":"field_0SHcq__c","width":145},{"field_name":"owner","width":145},{"field_name":"owner_department","width":145},{"field_name":"record_type","width":145},{"field_name":"data_own_department","width":145}]'''
        */
    }

    def createFieldWidthConfig(String fieldWidthConfigJson) {
        def fieldWidthConfig = JSONArray.parseArray(fieldWidthConfigJson, Map)
        return fieldWidthConfig.collect { it ->
            def key = it.get("field_name")
            def value = it.get("width")
            return Tuple.of(key, value)
        }
    }

    def buildServiceContext(User user) {
        return new ServiceContext(RequestContext.builder().user(Optional.ofNullable(user)).build(), "", "")
    }

    def buildUser(String tenantId, String userId) {
        return new User(tenantId, userId)
    }
}
