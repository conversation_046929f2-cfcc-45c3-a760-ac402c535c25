package com.facishare.paas.appframework.core.model.domain;

import com.facishare.paas.appframework.core.model.ActionContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2021/10/18.
 */
public interface ActionDomainPlugin<A extends DomainPlugin.Arg, R extends DomainPlugin.Result> extends DomainPlugin {

    String BEFORE = "before";

    String PRE_ACT = "preAct";

    String POST_ACT = "postAct";

    String AFTER = "after";

    String FINALLY_DO = "finallyDo";

    R before(ActionContext context, A arg);

    default R preAct(ActionContext context, A arg) {return null;}

    default R postAct(ActionContext context, A arg) {return null;}

    R after(ActionContext context, A arg);

    default R finallyDo(ActionContext context, A arg) {
        return null;
    }
}
