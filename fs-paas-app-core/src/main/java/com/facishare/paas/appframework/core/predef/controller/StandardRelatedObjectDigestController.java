package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedObjectDigestController.Arg;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedObjectDigestController.Result;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.RefObjectDescribeListResult;
import com.facishare.paas.appframework.metadata.expansion.DescribeExpansionRender;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.appframework.metadata.options.SelectFieldDependenceLogicService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.google.common.collect.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class StandardRelatedObjectDigestController extends PreDefineController<Arg, Result> {

    private IObjectDescribe describe;
    private IObjectData objectData;
    Map<String, IObjectDescribe> relatedDescribes;
    private DescribeExtra describeExtra;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        describe = findObject(arg);
        objectData = findObjectData(arg);
    }


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardController.RelatedObjectDigest.getFuncPrivilegeCodes();
    }

    @Override
    protected Result doService(Arg arg) {
        List<RelatedObjectField> relatedObjectFields = arg.getRelatedObjectFields();
        List<RefObjectDescribeListResult> refObjectResultList = Lists.newArrayList();
        List<ObjectReferenceFieldDescribe> refFields = ObjectDescribeExt.of(describe).getActiveSingleReferenceFieldDescribes();
        List<RelatedObjectData> relatedObjectDataList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(refFields) && CollectionUtils.notEmpty(relatedObjectFields)) {
            List<String> refFieldApiNames = refFields.stream().map(ObjectReferenceFieldDescribe::getApiName).collect(Collectors.toList());
            List<String> refApiNames = refFields.stream().map(ObjectReferenceFieldDescribe::getTargetApiName).collect(Collectors.toList());
            List<String> relatedObjectApiNames = relatedObjectFields.stream()
                    .filter(it -> refFieldApiNames.contains(it.getFieldApiName()))
                    .map(RelatedObjectField::getDescribeApiName)
                    .filter(refApiNames::contains)
                    .distinct()
                    .collect(Collectors.toList());
            findDescribesAndExtension(refObjectResultList, relatedObjectApiNames);

            // 同一个对象数据合并后统一处理
            Map<String, Set<String>> relatedObjFieldApiNameMap = Maps.newHashMap();
            Table<String, String, String> relatedObjFieldApiNameTable = HashBasedTable.create();
            relatedObjectFields.forEach(relatedObjectField -> {
                String objectApiName = relatedObjectField.getDescribeApiName();
                String fieldApiName = relatedObjectField.getFieldApiName();
                String dataId = objectData.get(fieldApiName, String.class);
                if (StringUtils.isEmpty(dataId)) {
                    return;
                }
                relatedObjFieldApiNameMap.computeIfAbsent(objectApiName, k -> Sets.newHashSet()).addAll(relatedObjectField.getRelatedObjectFieldApiNames());
                relatedObjFieldApiNameTable.put(objectApiName, fieldApiName, dataId);
            });
            relatedObjFieldApiNameTable.rowKeySet().forEach(objectApiName -> {
                Map<String, String> row = relatedObjFieldApiNameTable.row(objectApiName);
                List<IObjectData> dataList = queryDataList(objectApiName, relatedObjFieldApiNameMap.get(objectApiName), Lists.newArrayList(row.values()));
                if (CollectionUtils.empty(dataList)) {
                    return;
                }
                fillExtendFieldInfo(relatedDescribes.get(objectApiName), dataList);
                Map<String, IObjectData> dataMap = dataList.stream().collect(Collectors.toMap(IObjectData::getId, it -> it, (x, y) -> x));
                row.keySet().forEach(relatedFieldApiName -> {
                    RelatedObjectData relatedObjectData = RelatedObjectData.builder()
                            .describeApiName(objectApiName)
                            .relatedFieldApiName(relatedFieldApiName)
                            .objectData(ObjectDataDocument.of(dataMap.get(objectData.get(relatedFieldApiName, String.class))))
                            .build();
                    relatedObjectDataList.add(relatedObjectData);
                });
            });
        }
        return Result.builder()
                .relatedObjectList(refObjectResultList)
                .relatedObjectDataList(relatedObjectDataList)
                .build();
    }

    private List<IObjectData> queryDataList(String objectApiName, Set<String> relatedFieldApiName, List<String> refDataIds) {
        List<String> relatedObjectFieldApiNamesFormArg = relatedFieldApiName.stream()
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());

        List<String> dataIdList = refDataIds.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.empty(relatedObjectFieldApiNamesFormArg) || CollectionUtils.empty(dataIdList)) {
            return Lists.newArrayList();
        }
        relatedObjectFieldApiNamesFormArg.add(IObjectData.ID);
        relatedObjectFieldApiNamesFormArg.add(IObjectData.DESCRIBE_API_NAME);
        MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                .user(controllerContext.getUser())
                .skipRelevantTeam(true)
                .projectionFields(relatedObjectFieldApiNamesFormArg)
                .searchRichTextExtra(true)
                .calculateFormula(true)
                .calculateQuote(true)
                .convertQuoteForView(true)
                .build();
        return serviceFacade.findObjectDataByIdsWithQueryContext(queryContext, dataIdList, objectApiName);
    }

    private void findDescribesAndExtension(List<RefObjectDescribeListResult> refObjectResultList, List<String> relatedObjectApiNames) {
        // 查询描述及拓展描述
        relatedDescribes = serviceFacade.findObjects(describe.getTenantId(), relatedObjectApiNames);
        describeExtracted(Lists.newArrayList(relatedDescribes.values()));
        relatedDescribes.keySet().forEach(apiName ->
                refObjectResultList.add(RefObjectDescribeListResult.builder()
                        .describeApiName(apiName)
                        .objectDescribe(ObjectDescribeDocument.handleDescribeCache(arg.getDescribeVersionMap(), relatedDescribes.get(apiName)))
                        .objectDescribeExt(describeExtra.getDescribeExpansionByApiName(apiName))
                        .build()));
    }

    private IObjectData findObjectData(Arg arg) {
        return serviceFacade.getObjectDataIncludeDeleted(buildActionContext(), arg.getDataId(),
                arg.getDescribeApiName());
    }

    //告诉元数据不要处理引用字段
    private IActionContext buildActionContext() {
        IActionContext context = ActionContextExt.of(controllerContext.getUser()).disableDeepQuote().getContext();
        ActionContextExt actionContextExt = ActionContextExt.of(context);
        ActionContextExt.of(context).setSearchRichTextExtra(true);
        actionContextExt.setSkipRelevantTeam(RequestUtil.skipRelevantTeam());
        return context;
    }

    private void describeExtracted(List<IObjectDescribe> relatedObjDescribes) {
        describeExtra = DescribeExpansionRender.builder()
                .renderType(DescribeExpansionRender.RenderType.Detail)
                .user(controllerContext.getUser())
                .describeLogicService(serviceFacade)
                .fieldRelationCalculateService(infraServiceFacade)
                .selectFieldDependenceLogicService(serviceFacade.getBean(SelectFieldDependenceLogicService.class))
                .optionalFeaturesService(infraServiceFacade)
                .describeCacheable(true)
                .build()
                .render(relatedObjDescribes);
        stopWatch.lap("describeExtracted");
    }

    private IObjectDescribe findObject(Arg arg) {
        //灰度企业不拷贝对象描述
        if (AppFrameworkConfig.notCopyDescribeInController(controllerContext.getTenantId(), arg.getDescribeApiName())) {
            return serviceFacade.findObjectWithoutCopy(controllerContext.getTenantId(), arg.getDescribeApiName());
        }
        return serviceFacade.findObjectIncludeMultiField(controllerContext.getTenantId(), arg.getDescribeApiName());
    }

    private void fillExtendFieldInfo(IObjectDescribe describe, List<IObjectData> dataList) {
        //补充引用字段数据(包括负责人所在部门)  「通过calculateQuote参数补充」
        // todo 后期改成  serviceFacade.fillExtendFieldInfo();
        //补充关联对象的名称
        fillRefObjectName(describe, dataList);
        stopWatch.lap("fillRefObjectName");
        fillOutUserInfo(describe, dataList);
        stopWatch.lap("fillOutUserInfo");

        fillCountryAreaLabel(describe, dataList);
        stopWatch.lap("fillCountryAreaLabel");
        fillMaskFieldValue(describe, dataList);
        stopWatch.lap("fillMaskFieldValue");
        fillDimensionFieldValue(describe, dataList);
        stopWatch.lap("fillDimensionFieldValue");
        fillDataVisibilityRange(describe, dataList);
        stopWatch.lap("fillDataVisibilityRange");
    }

    private void fillRefObjectName(IObjectDescribe describe, List<IObjectData> dataList) {
        serviceFacade.fillObjectDataWithRefObject(describe, dataList, controllerContext.getUser(),
                null, false);
    }

    private void fillOutUserInfo(IObjectDescribe describe, List<IObjectData> dataList) {
        serviceFacade.fillUserInfo(describe, dataList, controllerContext.getUser());
        serviceFacade.fillDepartmentInfo(describe, dataList, controllerContext.getUser());
        serviceFacade.fillImageInformation(describe, dataList, controllerContext.getUser());
        serviceFacade.fillRichTextImageInfo(describe, dataList, controllerContext.getUser());
    }


    private void fillDimensionFieldValue(IObjectDescribe describe, List<IObjectData> dataList) {
        serviceFacade.fillDimensionFieldValue(controllerContext.getUser(), describe, dataList);
    }

    private void fillCountryAreaLabel(IObjectDescribe describe, List<IObjectData> dataList) {
        serviceFacade.fillCountryAreaLabel(describe, dataList, controllerContext.getUser());
    }

    private void fillMaskFieldValue(IObjectDescribe describe, List<IObjectData> dataList) {
        serviceFacade.fillMaskFieldValue(controllerContext.getUser(), dataList, describe, RequestUtil.isCepRequest());
    }

    private void fillDataVisibilityRange(IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        serviceFacade.fillDataVisibilityRange(controllerContext.getUser(), objectDescribe, dataList);
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg {

        /**
         * {
         * "dataId": "61b1ae8863a33500016e1aab",
         * "describeApiName": "object_0u48F__c",
         * "relatedObjectFields": [
         * {
         * "describeApiName": "AccountObj",// todo 后续可去掉
         * "fieldApiName": "field_kQRqy__c",
         * "relatedObjectFieldApiNames": [
         * "province",
         * "field_VbKps__c"
         * ]
         * }
         * ]
         * }
         */
        private String dataId;
        private String describeApiName;
        private List<RelatedObjectField> relatedObjectFields;
        private Map<String, Integer> describeVersionMap;


    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {

        private List<RelatedObjectData> relatedObjectDataList;
        private List<RefObjectDescribeListResult> relatedObjectList;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RelatedObjectField {
        private String describeApiName;
        private String fieldApiName;
        private List<String> relatedObjectFieldApiNames;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RelatedObjectData {
        private String describeApiName;
        private String relatedFieldApiName;
        private Map objectData;
    }
}
