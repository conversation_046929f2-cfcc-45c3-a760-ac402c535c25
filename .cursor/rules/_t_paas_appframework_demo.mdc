---
description: 
globs: 
alwaysApply: false
---
- ServiceContext、RequestContext和RequestContextManager构造时参考下面代码
```groovy
        def user = User.systemUser('74255')
        def requestContext = RequestContext.builder()
                .tenantId('74255')
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .build()
        RequestContextManager.setContext(requestContext)
        def context = new ServiceContext(requestContext, "cacheValidate", "cacheValidate")
```
- 当代码中遇到 IFieldDescribe 构造
- 字段类型请参考 com.facishare.paas.metadata.api.describe.IFieldType 中的定义
```groovy
// 必需导入：
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IFieldType
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory

// 构造示例：
IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance([
    "api_name": "employeeField", 
    "type": IFieldType.EMPLOYEE
])
```
- 当代码中遇到 IObjectDescribe 时构造示例
```groovy
    // 必需导入：
    import com.facishare.paas.metadata.api.describe.IObjectDescribe
    import com.facishare.paas.metadata.impl.describe.ObjectDescribe
    import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory
    import com.facishare.paas.metadata.impl.describe.FieldDescribe

    // 构造示例：
    IObjectDescribe describe = new ObjectDescribe()
    describe.setApiName("AccountObj")
    IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance([
        "api_name": "employeeField", 
        "type": "employee"
    ])
    describe.setFieldDescribes([fieldDescribe])
```
- 当代码中遇到 ObjectDescribeExt 时，需要依赖 IObjectDescribe 时构造示例
```groovy
ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(describe)

```

- 当代码中遇到 IObjectData 构造
- ObjectData构造时key为IFieldDescribe构造中的api_name。
```groovy
// 构造示例一：
IObjectData objectData = new ObjectData(['_id':'2','name':'产品2', 'object_describe_api_name': 'master__c'])
// 构造示例二：
IObjectData objectData = new ObjectData()
objectData.fromJsonString('''{"name":"test-1000","owner":"1000","mc_currency":"CNY","object_describe_api_name":"master__c"}''')
```

- 当遇到Wheres类需要初始化时，优先使用JSON字符串初始化
```groovy
    // 使用JSON字符串初始化Wheres对象
    def wheresJson = "{\"filters\":[{\"value_type\":9,\"operator\":\"EQ\",\"field_name\":\"test_field\",\"field_values\":[\"test_function_api\"]}],\"connector\":\"AND\"}"
    def where = new Wheres()
    where.fromJsonString(wheresJson)
```

- 构造Filter对象的示例
```groovy
    // 使用JSON字符串初始化Filter对象
    def filterJson = "{\"field_name\":\"status\",\"operator\":\"EQ\",\"field_values\":[\"1\"],\"value_type\":0}"
    def filter = new Filter()
    filter.fromJsonString(filterJson)
    
```

- 当遇到单测中使用 AppFrameworkConfig.java 中方法时，需要读取 AppFrameworkConfig.java 文件，根据文件中该方法中使用的变量定义，使用 Whitebox.setInternalState 模拟。
- 示例：
```groovy
    import com.facishare.paas.appframework.common.util.AppFrameworkConfig

    Whitebox.setInternalState(AppFrameworkConfig, "maxQueryLimit", 1000)
    
    Whitebox.setInternalState(AppFrameworkConfig, "mobileSupportUiPaasButtonGrayEi", ['74255','78057'] as Set)
```
- 后缀为Action/Controller类在编写测试类时测试都需要注入
```groovy
import com.facishare.paas.appframework.core.model.*

ServiceFacade serviceFacade = Mock(ServiceFacade)
Whitebox.setInternalState(被测类, "serviceFacade", serviceFacade)

InfraServiceFacade infraServiceFacade = Mock(InfraServiceFacade)
Whitebox.setInternalState(被测类, "infraServiceFacade", infraServiceFacade)

```