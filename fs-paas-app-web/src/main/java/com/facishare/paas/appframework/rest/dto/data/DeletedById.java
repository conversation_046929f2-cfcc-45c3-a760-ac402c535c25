package com.facishare.paas.appframework.rest.dto.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface DeletedById {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        String dataId;
        String describeAPIName;
        String eventId;
        boolean includeDescribe;
    }

    class Result extends CommonResult.ObjectDataResult{}
}
