package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.*;
import com.facishare.rest.core.annotation.*;

import java.util.Map;

@RestResource(
        value = "PAAS_REFERENCE_SERVICE",
        desc = "引用服务", // ignoreI18n
        contentType = "application/json"
)
public interface ReferenceServiceProxy {

    @GET(value = "/api/v1/paas/reference/entities/{tenantId}/byTarget")
    FindReferenceByTarget.Result findByTarget(@PathParams Map pathParam,
                                              @QueryParam("targetType") String targetType,
                                              @QueryParam("targetValue") String targetValue,
                                              @QueryParam("isPrefixMatch") String isPrefixMatch,
                                              @QueryParam("limit") String limit);

    @POST(value = "/api/v1/paas/reference/entities/{tenantId}/byTargetList", desc = "批量查询引用关系")
    QueryByTargetList.Result queryByTargetList(@PathParam("tenantId") String tenantId, @Body QueryByTargetList.Arg arg);

    @POST(value = "api/v1/paas/reference/entities/{tenantId}/deleteAndCreate", desc = " 先删除再创建引用关系")
    DeleteAndCreateReference.Result deleteAndCreate(@PathParams Map pathParam, @Body DeleteAndCreateReference.Arg arg);

    @POST(value = "api/v1/paas/reference/entities/{tenantId}/deleteAndCreate", desc = " 先删除再创建引用关系，删除时指定targetValue")
    DeleteAndCreateReference.Result deleteAndCreate(@PathParams Map pathParam, @QueryParamsMap Map<String, String> queryParams, @Body DeleteAndCreateReference.Arg arg);

    @DELETE(value = "/api/v1/paas/reference/entities/{tenantId}", desc = "删除引用关系")
    DeleteReference.Result deleteReference(@PathParam("tenantId") String tenantId,
                                           @QueryParam("sourceType") String sourceType,
                                           @QueryParam("sourceValue") String sourceValue);

    @DELETE(value = "/api/v1/paas/reference/entities/{tenantId}", desc = "删除引用关系")
    DeleteReference.Result deleteReference(@PathParam("tenantId") String tenantId,
                                           @QueryParam("sourceType") String sourceType,
                                           @QueryParam("sourceValue") String sourceValue,
                                           @QueryParam("targetValue") String targetValue);

    @DELETE(value = "/api/v1/paas/reference/entities/{tenantId}", desc = "删除引用关系，指定targetType")
    DeleteReference.Result deleteReference(@PathParam("tenantId") String tenantId,
                                           @QueryParam("sourceType") String sourceType,
                                           @QueryParam("sourceValue") String sourceValue,
                                           @QueryParam("targetValue") String targetValue,
                                           @QueryParam("targetType") String targetType);

    @POST(value = "/api/v1/paas/reference/entities/{tenantId}/batch/delete", desc = "批量删除引用关系")
    BatchDeleteReference.Result batchDeleteReference(@PathParam("tenantId") String tenantId, @Body BatchDeleteReference.Arg arg);

    @GET(value = "/api/v1/paas/reference/entities/{tenantId}/bySource", desc = "根据引用实体获取引用关系列表")
    FindReferenceByTarget.Result findRelationsBySource(@PathParam("tenantId") String tenantId,
                                                       @QueryParam("sourceType") String sourceType,
                                                       @QueryParam("sourceValue") String sourceValue);
}
