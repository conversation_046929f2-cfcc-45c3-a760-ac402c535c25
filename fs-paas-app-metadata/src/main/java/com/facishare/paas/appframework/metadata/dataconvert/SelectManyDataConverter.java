package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.SelectMany;
import com.facishare.paas.metadata.impl.describe.SelectManyFieldDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 多选转换器
 *
 * <AUTHOR>
 * @date 17/5/18
 */
public class SelectManyDataConverter extends AbstractFieldDataConverter {
    @Override
    public String convertFieldData(SessionContext sessionContext) {
        String value = "";
        IObjectData objectData = getObjectData();
        Object o = objectData.get(getFieldDescribe().getApiName());
        if (!(o instanceof List)) {
            return value;
        }
        Object[] array = ((List) o).toArray();
        List<ISelectOption> options = ((SelectManyFieldDescribe) getFieldDescribe()).getSelectOptions();
        if (array.length == 0 || CollectionUtils.isEmpty(options)) {
            return value;
        }
        List<String> result = Lists.newArrayList();
        for (Object item : array) {
            for (ISelectOption option : options) {
                if (option.getValue().equals(String.valueOf(item))) {
                    if (Objects.equals(SelectMany.OPTION_OTHER_VALUE, item)) {
                        String otherValue = (String) getObjectData().get(getFieldDescribe().getApiName() + "__o");
                        if (Strings.isNullOrEmpty(otherValue)) {
                            result.add(option.getLabel());
                        } else {
                            result.add(option.getLabel() + "：" + otherValue);
                        }
                        break;
                    }
                    result.add(option.getLabel());
                    break;
                }
            }
        }
        if (CollectionUtils.isEmpty(result)) {
            return "";
        }
        return StringUtils.join(result, "|");
    }
}
