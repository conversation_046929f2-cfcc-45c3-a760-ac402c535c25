package com.facishare.paas.appframework.metadata;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Objects;

@Data
@Builder
public class PersistentDataCalcExt {

    private String evaluateRange;
    private List<WheresExt> wheresExts;
    private String wheresType;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PersistentDataCalcExt that = (PersistentDataCalcExt) o;
        return Objects.equals(evaluateRange, that.evaluateRange) && Objects.equals(wheresExts, that.wheresExts) && Objects.equals(wheresType, that.wheresType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(evaluateRange, wheresExts, wheresType);
    }
}
