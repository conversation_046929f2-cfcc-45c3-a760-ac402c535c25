package com.facishare.paas.appframework.payment;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.payment.dto.PaymentRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface PaymentService {

    List<PaymentRecord> findPaymentList(User user, IObjectDescribe describe, IObjectData data);

    String getPaymentUrl(GetPaymentUrlArg arg);

    @Data
    @Builder
    class GetPaymentUrlArg {
        private String dataId;
        private String orderName;
        private long amount;
        private String apiName;
    }
}
