package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.predef.service.ObjectDuplicatedSearchService;
import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.GetRelatedResults;
import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.GetResult;
import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.SimpleSearch;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;

public class StandardRelatedSimpleDuplicateSearchController extends PreDefineController<SimpleSearch.Arg, GetRelatedResults.Result> {

    private ObjectDuplicatedSearchService objectDuplicatedSearchService =
            SpringUtil.getContext().getBean("objectDuplicatedSearchService", ObjectDuplicatedSearchService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected GetRelatedResults.Result doService(SimpleSearch.Arg arg) {
        IDuplicatedSearch duplicatedSearch = null;
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(controllerContext.getTenantId(), controllerContext.getObjectApiName())) {
            String ruleApiName = arg.getDuplicateRuleApiName();
            if (BooleanUtils.isFalse(arg.getIsNeedDuplicate()) && StringUtils.isNotBlank(arg.getRuleApiNameIntercepted())) {
                ruleApiName = arg.getRuleApiNameIntercepted();
            }
            duplicatedSearch = serviceFacade.findDuplicatedSearchByRuleApiNameOrFirstRule(controllerContext.getObjectApiName(),
                    ruleApiName, IDuplicatedSearch.Type.TOOL, controllerContext.getTenantId(), false);
        } else {
            duplicatedSearch = serviceFacade.findDuplicatedSearchByApiNameAndType(controllerContext.getTenantId(),
                    arg.getDescribeApiName(), IDuplicatedSearch.Type.TOOL, false);
        }
        IObjectDescribe objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), arg.getDescribeApiName());
        List<String> apiNames = objectDuplicatedSearchService.getApiNames(duplicatedSearch);
        apiNames.removeIf(x -> StringUtils.equals(x, arg.getDescribeApiName()));
        List<String> conditionFields = objectDuplicatedSearchService.getConditionFields(duplicatedSearch);
        IObjectData objectData = objectDuplicatedSearchService.getSimpleSearchObjectData(arg.getKeyword(), conditionFields, objectDescribe);
        ControllerContext controllerContext = new ControllerContext(this.controllerContext.getRequestContext(), arg.getDescribeApiName(), "DuplicateSearch");
        List<GetResult.Result> results = Lists.newArrayList();
        apiNames.forEach(x -> {
            GetResult.Arg build = GetResult.Arg.builder()
                    .describeApiName(arg.getDescribeApiName())
                    .type(IDuplicatedSearch.Type.TOOL)
                    .objectData(ObjectDataDocument.of(objectData))
                    .isNeedDuplicate(arg.getIsNeedDuplicate())
                    .includeObjectDescribes(false)
                    .pageSize(arg.getPageSize())
                    .duplicateRuleApiName(arg.getDuplicateRuleApiName())
                    .ruleApiNameIntercepted(arg.getRuleApiNameIntercepted())
                    .pageNumber(arg.getPageNumber())
                    .relatedApiName(x)
                    .build();
            GetResult.Result result = serviceFacade.triggerController(controllerContext, build, GetResult.Result.class);
            if (Objects.isNull(result)) {
                return;
            }
            if (CollectionUtils.empty(result.getDataList())) {
                return;
            }
            results.add(result);
        });
        return GetRelatedResults.Result.builder().results(results).build();
    }
}

