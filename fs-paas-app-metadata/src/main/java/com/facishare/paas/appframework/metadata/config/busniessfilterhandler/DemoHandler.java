package com.facishare.paas.appframework.metadata.config.busniessfilterhandler;

import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.metadata.config.util.TenantConfigUtil;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * Created by zhangxf in 2021/1/25 10:48
 */
public class DemoHandler implements BusinessFilterHandler {

    @Autowired
    ConfigService configService;

    @Override
    public String getObjectApiName() {
        return "demoObj";
    }

    @Override
    public void handle(String tenantId, Map<String, Object> businessConfig, Map<String, Object> filterConfig) {
        boolean isSupportDemo = TenantConfigUtil.isDemoEnable(configService, tenantId);
        if (isSupportDemo) {
            if (businessConfig.containsKey("ui_event")) {
                businessConfig.put("ui_event", 0);
            }
        }
    }
}
