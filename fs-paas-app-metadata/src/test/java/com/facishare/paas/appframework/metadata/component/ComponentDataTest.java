package com.facishare.paas.appframework.metadata.component;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ComponentDataTest {

  private ComponentData componentData;

  @BeforeEach
  void setUp() {
    componentData = new ComponentData();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ComponentData的基本属性设置和获取
   */
  @Test
  @DisplayName("正常场景 - 测试基本属性设置和获取")
  void testBasicProperties() {
    // 设置属性
    String apiName = "test_component_api";
    String name = "测试组件";
    String url = "http://test.example.com/component";
    
    componentData.setApiName(apiName);
    componentData.setName(name);
    componentData.setUrl(url);
    
    // 验证属性
    assertEquals(apiName, componentData.getApiName(), "API名称应该正确设置");
    assertEquals(name, componentData.getName(), "组件名称应该正确设置");
    assertEquals(url, componentData.getUrl(), "URL应该正确设置");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ComponentData的空值处理
   */
  @Test
  @DisplayName("边界场景 - 测试空值处理")
  void testNullValues() {
    // 设置空值
    componentData.setApiName(null);
    componentData.setName(null);
    componentData.setUrl(null);
    
    // 验证空值
    assertNull(componentData.getApiName(), "API名称可以为空");
    assertNull(componentData.getName(), "组件名称可以为空");
    assertNull(componentData.getUrl(), "URL可以为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ComponentData的空字符串处理
   */
  @Test
  @DisplayName("边界场景 - 测试空字符串处理")
  void testEmptyStrings() {
    // 设置空字符串
    componentData.setApiName("");
    componentData.setName("");
    componentData.setUrl("");
    
    // 验证空字符串
    assertEquals("", componentData.getApiName(), "API名称可以为空字符串");
    assertEquals("", componentData.getName(), "组件名称可以为空字符串");
    assertEquals("", componentData.getUrl(), "URL可以为空字符串");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ComponentData的equals和hashCode方法（由Lombok生成）
   */
  @Test
  @DisplayName("正常场景 - 测试equals和hashCode方法")
  void testEqualsAndHashCode() {
    // 创建两个相同的对象
    ComponentData data1 = new ComponentData();
    data1.setApiName("test_api");
    data1.setName("测试组件");
    data1.setUrl("http://test.com");
    
    ComponentData data2 = new ComponentData();
    data2.setApiName("test_api");
    data2.setName("测试组件");
    data2.setUrl("http://test.com");
    
    // 验证equals
    assertEquals(data1, data2, "相同属性的对象应该相等");
    assertEquals(data1.hashCode(), data2.hashCode(), "相同对象的hashCode应该相等");
    
    // 修改一个属性
    data2.setApiName("different_api");
    assertNotEquals(data1, data2, "不同属性的对象应该不相等");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ComponentData的toString方法（由Lombok生成）
   */
  @Test
  @DisplayName("正常场景 - 测试toString方法")
  void testToString() {
    componentData.setApiName("test_api");
    componentData.setName("测试组件");
    componentData.setUrl("http://test.com");
    
    String toString = componentData.toString();
    
    // 验证toString包含所有属性
    assertNotNull(toString, "toString不应为空");
    assertTrue(toString.contains("test_api"), "toString应包含apiName");
    assertTrue(toString.contains("测试组件"), "toString应包含name");
    assertTrue(toString.contains("http://test.com"), "toString应包含url");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ComponentData的特殊字符处理
   */
  @Test
  @DisplayName("边界场景 - 测试特殊字符处理")
  void testSpecialCharacters() {
    // 设置包含特殊字符的值
    String specialApiName = "test_api_@#$%";
    String specialName = "测试组件<>&\"'";
    String specialUrl = "http://test.com/path?param=value&other=测试";
    
    componentData.setApiName(specialApiName);
    componentData.setName(specialName);
    componentData.setUrl(specialUrl);
    
    // 验证特殊字符正确保存
    assertEquals(specialApiName, componentData.getApiName(), "特殊字符API名称应该正确保存");
    assertEquals(specialName, componentData.getName(), "特殊字符组件名称应该正确保存");
    assertEquals(specialUrl, componentData.getUrl(), "特殊字符URL应该正确保存");
  }
}
