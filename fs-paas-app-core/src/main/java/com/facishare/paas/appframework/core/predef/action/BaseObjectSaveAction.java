package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.*;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.predef.facade.ApprovalFlowServiceFacade;
import com.facishare.paas.appframework.core.predef.facade.SaveActionServiceFacade;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.flow.ExtraDataKeys;
import com.facishare.paas.appframework.flow.dto.StartApprovalFlow;
import com.facishare.paas.appframework.flow.mq.WorkflowProducer;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.license.quota.TenantLicenseInfo;
import com.facishare.paas.appframework.license.util.ModulePara;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.*;
import com.facishare.paas.appframework.metadata.exception.MetadataDataDuplicateBusinessException;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.metadata.relation.EditCalculateParam;
import com.facishare.paas.appframework.metadata.relation.FieldRelationGraph;
import com.facishare.paas.appframework.metadata.relation.FieldRelationGraphService;
import com.facishare.paas.appframework.metadata.state.MergeStateContainer;
import com.facishare.paas.appframework.metadata.switchcache.provider.SwitchCacheProviderManager;
import com.facishare.paas.appframework.metadata.switchcache.provider.UniqueRuleSwitchCacheProvider;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.describe.PaymentFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SignInFieldDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.bizconf.bean.ConfigPojo;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.fxiaoke.transaction.tcc.api.context.GlobalTransactionApplicationData;
import com.github.trace.TraceContext;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.util.RequestUtil.VERSION_670;

/**
 * 创建更新
 * <p>
 * Created by liyiguang on 2017/9/28.
 */
@Slf4j
public abstract class BaseObjectSaveAction<A extends BaseObjectSaveAction.Arg> extends BaseObjectApprovalAction<A, BaseObjectSaveAction.Result> {

    public static final String DUPLICATE_GLOBAL_LOCK_PRE = "DUPLICATE_GLOBAL_LOCK";
    protected IObjectData objectData;
    protected Map<String, List<IObjectData>> detailObjectData;
    protected Map<String, IObjectDescribe> objectDescribes = Maps.newHashMap();
    protected Map<String, IObjectDescribe> detailDescribeMap = Maps.newHashMap();
    protected Map<String, List<SaveMasterAndDetailData.RelatedObjectData>> relatedObjectData = Maps.newHashMap();
    protected Map<String, IObjectDescribe> relatedDescribes = Maps.newHashMap();
    protected RuleResult ruleResult;
    protected boolean isDuplicate;
    protected IUniqueRule uniqueRule;

    protected ObjectLifeStatus lastLifeStatus;
    //是否审批例外人
    protected boolean isAssigneesExceptional = false;
    private boolean isFirstModifyOwner = true;

    private RLock uniqueLock;
    private RLock duplicateSearchLock;

    protected boolean writeDB;

    private RLock seriesLock;

    protected MaskFieldLogicService maskFieldLogicService;

    protected ConvertRuleDataContainer convertRuleDataContainer;

    protected abstract String getIRule();

    protected abstract ObjectAction getObjectAction();

    @Override
    protected final String getIndustryCode(A arg) {
        return arg.getIndustryCode();
    }

    @Override
    protected void customProcessArg(A arg) {
        if (StringUtils.isNotBlank(arg.getSeriesId())
                && UdobjGrayUtil.isObjectAndTenantGray(UdobjGrayKey.LOCK_BY_SERIES_ID_IN_CALCULATE_AND_UI_EVENT_AND_ADD_EDIT_REQUEST, actionContext.getTenantId(), actionContext.getObjectApiName())) {
            String lockKey = RequestUtil.buildSeriesKey(arg.getSeriesId(), actionContext.getTenantId());
            seriesLock = infraServiceFacade.tryLockWithErrorMsg(0, 30, TimeUnit.SECONDS, lockKey,
                    I18NExt.text(I18NKey.PREVIOUS_REQUEST_PROCESSING_ALERT), AppFrameworkErrorCode.PREVIOUS_REQUEST_PROCESSING_ALERT.getCode());
        }
    }

    @Override
    protected final boolean supportValidationResult() {
        return arg.supportValidationResult();
    }

    @Override
    protected final List<ValidatorInfo> getSkippedValidatorList() {
        return arg.skippedValidatorList();
    }

    @Override
    protected final Result buildValidationResult(ValidationResultDocument validationResultDocument) {
        return Result.builder().validationResult(validationResultDocument).build();
    }

    protected Result buildValidateResult() {
        fillMaskFieldValueWhenBuildValidationResult();
        if (checkBlockValidationRules()) {
            return buildBlockValidationResult();
        }
        if (isDuplicate) {
            return Result.builder()
                    .objectData(ObjectDataDocument.of(objectData))
                    .details(ObjectDataDocument.ofMap(detailObjectData))
                    .relatedDataList(RelatedDataDocument.fromMap(relatedObjectData))
                    .isDuplicate(isDuplicate)
                    .build();
        }
        if (null != validatedFunctionResult && validatedFunctionResult.isHasReturnValue()) {
            return builderValidationFunctionResult();
        }
        return Result.builder()
                .objectData(ObjectDataDocument.of(objectData))
                .details(ObjectDataDocument.ofMap(detailObjectData))
                .relatedDataList(RelatedDataDocument.fromMap(relatedObjectData))
                .isDuplicate(Boolean.FALSE)
                .build();
    }

    private Result builderValidationFunctionResult() {
        String message = validatedFunctionResult.getReturnValue() == null ? "" : validatedFunctionResult.getReturnValue().toString();

        boolean submitReturnData = isSubmitReturnData();
        ValidationMessage validateMessage = ValidationMessage.builder()
                .submitReturnData(submitReturnData)
                .isMatch(true)
                .build()
                .setMessage(message, validatedFunctionResult.isBlock());

        return Result.builder()
                .objectData(ObjectDataDocument.of(objectData))
                .details(ObjectDataDocument.ofMap(detailObjectData))
                .relatedDataList(RelatedDataDocument.fromMap(relatedObjectData))
                .funcValidateMessage(validateMessage)
                .build();
    }

    protected final void updateDetailObjectDataLifeStatus() {
        ApprovalFlowServiceFacade approvalFlowServiceFacade = serviceFacade.getBean(ApprovalFlowServiceFacade.class);
        approvalFlowServiceFacade.updateDetailObjectDataLifeStatus(actionContext.getActionCode(), actionContext.getUser(),
                lastLifeStatus, objectData, detailObjectData, detailDescribeMap, getRealMasterData(),
                isApprovalFlowStartSuccess(objectData.getId()));
    }

    /**
     * 二次提交的数据补充掩码
     */
    private void fillMaskFieldValueWhenBuildValidationResult() {
        try {
            User user = actionContext.getUser();
            fillMaskFieldValue(user, objectDescribe, Lists.newArrayList(objectData));
            detailObjectData.forEach((describeApiName, detailDataList) -> {
                IObjectDescribe describe = detailDescribeMap.get(describeApiName);
                fillMaskFieldValue(user, describe, detailDataList);
            });
            processRelatedObjectData((apiName, relatedDataList) -> {
                IObjectDescribe describe = relatedDescribes.get(apiName);
                fillMaskFieldValue(user, describe, relatedDataList);
            });
        } catch (Exception e) {
            log.warn("fillMaskFieldValueWhenBuildValidationResult fail!", e);
        }
    }

    private boolean isSubmitReturnData() {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SUBMIT_RETURN_DATA, actionContext.getTenantId())
                || AppFrameworkConfig.isValidationFunctionMergeDetailDataGray(actionContext.getTenantId(), actionContext.getObjectApiName());
    }

    private Result buildBlockValidationResult() {
        String matchObjApiName = Objects.nonNull(ruleResult.getData()) ?
                ObjectDataExt.of(ruleResult.getData()).getDescribeApiName() : "";
        String masterApiName = ObjectDataExt.of(objectData).getDescribeApiName();
        String matchDataIndex = Objects.equals(masterApiName, matchObjApiName) ? "" : ObjectDataExt.of(ruleResult.getData()).getDataIndex();
        List<String> matchDataIndexList = StringUtils.isBlank(matchDataIndex) ? Lists.newArrayList() : Lists.newArrayList(matchDataIndex);
        return Result.builder()
                .objectData(ObjectDataDocument.of(objectData))
                .details(ObjectDataDocument.ofMap(detailObjectData))
                .relatedDataList(RelatedDataDocument.fromMap(relatedObjectData))
                .validationRuleMessage(ValidationMessage.builder()
                        .isMatch(ruleResult.isMatch())
                        .blockMessages(ruleResult.getBlockMessages())
                        .nonBlockMessages(ruleResult.getNonBlockMessages())
                        .matchObjApi(matchObjApiName)
                        .matchDataIndex(matchDataIndexList)
                        .build())
                .build();
    }

    @Override
    protected void before(A arg) {
        super.before(arg);
        tryLock();
        validateArg();
        stopWatch.lap("validateArg");
        validate();
    }

    private void tryLock() {
        uniqueRuleLock();
        stopWatch.lap("uniqueRuleLock");
    }


    protected void uniqueRuleLock() {
        if (!actionContext.isFromOpenAPI() && !actionContext.isFromSmartForm()) {
            return;
        }
        if (Objects.isNull(uniqueRule) || !uniqueRule.isUseWhenCallOpenApi()) {
            return;
        }

        String uniqueLockKey = UniqueRuleExt.of(uniqueRule).getUniqueRuleKey(objectDescribe, objectData);
        uniqueLock = infraServiceFacade.tryLock(actionContext.getUser(), objectDescribe.getApiName(), uniqueLockKey);
        if (uniqueLock == null) {
            log.warn("try uniqueLock fail, ei:{}, apiName:{}, key:{}", actionContext.getTenantId(),
                    objectDescribe.getApiName(), uniqueLockKey);
            throw new ValidateException(I18N.text(I18NKey.UNIQ_SAVE_DATA_FAIL));
        }
    }

    void syncDetailDataPartner(String masterPartnerId, IObjectDescribe detailDescribe, IObjectData detail) {
        if (ObjectDescribeExt.of(detailDescribe).isPRMEnabled() && StringUtils.isNotEmpty(masterPartnerId)) {
            ObjectDataExt detailDataExt = ObjectDataExt.of(detail);
            detailDataExt.setPartnerId(masterPartnerId);
        }
    }

    @Override
    protected void finallyDo() {
        infraServiceFacade.unlock(uniqueLock);
        stopWatch.lap("uniqueLock unlock");
        infraServiceFacade.unlock(duplicateSearchLock);
        stopWatch.lap("duplicateSearchLock unlock");
        infraServiceFacade.unlock(seriesLock);
        stopWatch.lap("seriesLock unlock");
        super.finallyDo();
    }

    @Override
    protected void init() {
        super.init();
        stopWatch.lap("initDataList");

        //将业务信息缓存到context中，修改记录会用到
        if (arg.getBizInfo() != null) {
            actionContext.setBizInfo(arg.getBizInfo());
        }
        if (AppFrameworkConfig.isLogTypeObject(actionContext.getTenantId(), actionContext.getObjectApiName())) {
            //不走唯一性规则
            actionContext.skipUniqueRuleCheck();
            //不走查重
            actionContext.skipDuplicatedRuleCheck();
        }

        initDetailsAndRelatedDescribe();
        stopWatch.lap("initDetailsAndRelatedDescribe");
        initObjectData();
        stopWatch.lap("initObjectData");
        initDetailData();
        stopWatch.lap("initDetailData");

        calculateDefaultValueAndLog();
        stopWatch.lap("calculateDefaultValueAndLog");

        removeUnnecessaryFields();
        stopWatch.lap("removeUnnecessaryFields");

        processLookUpField2Id();
        stopWatch.lap("processLookUpFieldNameToId");
        initRelatedData();
        stopWatch.lap("initRelatedData");

        //提取并设置富文本的摘要信息，如果端上没有提交
        extractAbstractOfRichText(objectData, objectDescribe);
        stopWatch.lap("extractAbstractOfRichText");

        //纠正字段值
        correctFieldValue();
        stopWatch.lap("correctFieldValue");

        findUniqueRule();
        stopWatch.lap("findUniqRule");

        //查询当前用户是否审批例外人
        isAssigneesExceptional = assigneesExceptional();
        stopWatch.lap("isAssigneesExceptional");

        actionContext.setAttribute("needAllRedirectResult", needAllRedirectResult());
        // 转换规则新建源单数据初始化
        initConvertRuleList();
        stopWatch.lap("initConvertRuleList");
    }

    protected void fillMultiLang() {
        try {
            if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ADD_AND_EDIT_FILL_MULTI_LANG__R, actionContext.getTenantId()) || !arg.isFillMultiLang()) {
                return;
            }
            if (Objects.nonNull(objectData) && Objects.nonNull(objectDescribe)) {
                ObjectDataExt.of(objectData).handleMultiLangField(objectDescribe);
            }
            if (CollectionUtils.notEmpty(detailObjectData) && CollectionUtils.notEmpty(detailDescribeMap)) {
                detailObjectData.forEach((describeApiName, dataList) -> {
                    if (CollectionUtils.empty(dataList) || StringUtils.isEmpty(describeApiName)) {
                        return;
                    }
                    IObjectDescribe detailDescribe = detailDescribeMap.get(describeApiName);
                    if (Objects.isNull(detailDescribe)) {
                        return;
                    }
                    dataList.forEach(data -> ObjectDataExt.of(data).handleMultiLangField(detailDescribe));
                });
            }
        } catch (Exception e) {
            log.warn("fillMultiLang error", e);
        }

    }

    protected void initConvertRuleList() {
        if (!isFromTransForm() && !isFromReferenceCreate()) {
            return;
        }
        SaveActionServiceFacade saveActionServiceFacade = serviceFacade.getBean(SaveActionServiceFacade.class);
        convertRuleDataContainer = saveActionServiceFacade.buildConvertRuleDataContainer(actionContext.getUser(), arg.getOptionInfo());
    }

    private boolean isFromTransForm() {
        return Optional.ofNullable(arg.getOptionInfo()).map(OptionInfo::isFromTransform).orElse(false);
    }

    private boolean isFromReferenceCreate() {
        return Optional.ofNullable(arg.getOptionInfo()).map(OptionInfo::isFromReferenceCreate).orElse(false);
    }

    @Override
    protected void processHandlerResult(HandlerContext handlerContext, Handler.Arg<A> handlerArg, Handler.Result<Result> handlerResult) {
        super.processHandlerResult(handlerContext, handlerArg, handlerResult);
        // 根据 handler 执行结果，给 writeDB 赋值
        Optional.ofNullable(handlerResult)
                .map(Handler.Result::getInterfaceResult)
                .map(Result::getWriteDB)
                .ifPresent(it -> this.writeDB = it);
    }

    /**
     * 1、记录是主数据的生命状态
     * 2、记录是否写库成功
     *
     * @param globalTransactionApplicationData
     */
    @Override
    protected void beforeGlobalTransactionCompletion(GlobalTransactionApplicationData globalTransactionApplicationData) {
        super.beforeGlobalTransactionCompletion(globalTransactionApplicationData);
        globalTransactionApplicationData.setAttribute(GlobalTransactionConstant.OBJECT_SAVE_LIFE_STATUS, ObjectDataExt.of(objectData).getLifeStatusText());
        globalTransactionApplicationData.setAttribute(GlobalTransactionConstant.OBJECT_SAVE_WRITE_DB_FLAG, writeDB);
    }

    private void calculateDefaultValueAndLog() {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CALCULATE_DEFAULT_VALUE_AND_REPORT_LOG, actionContext.getTenantId())) {
            return;
        }
        if (CollectionUtils.empty(getMasterCalculateFields()) && CollectionUtils.empty(getDetailCalculateFields())) {
            return;
        }
        if (continueToSubmitForCepRequest()) {
            return;
        }
        long now = System.currentTimeMillis();
        try {
            IObjectData objectDataCopy = ObjectDataExt.of(this.objectData).copy();
            IObjectData dbMasterData = findDbMasterData();
            IObjectData dbMasterDataCopy = Objects.isNull(dbMasterData) ? null : ObjectDataExt.of(dbMasterData).copy();
            Map<String, List<IObjectData>> detailObjectDataCopy = ObjectDataExt.copyMap(this.detailObjectData);
            Map<String, List<IObjectData>> dbDetailDataMapCopy = ObjectDataExt.copyMap(findDbDetailDataMap());
            // 补充index
            ObjectDataExt.fillDataIndex(detailObjectDataCopy, dbDetailDataMapCopy);
            String parameters = JacksonUtils.toJson(arg);
            // 异步计算默认值，并上报日志
            ParallelUtils.createBackgroundTask()
                    .submit(() -> {
                        // 构造 calculateParam
                        EditCalculateParam param = buildEditCalculateParam(objectDataCopy, dbMasterDataCopy, detailObjectDataCopy, dbDetailDataMapCopy);
                        if (!param.canCalculate()) {
                            return;
                        }
                        serviceFacade.calculateForEditData(actionContext.getUser(), param);
                        diffDataAndLog(param, objectDataCopy, detailObjectDataCopy, now, parameters);
                    }).run();
        } catch (Exception e) {
            log.warn("calculateDefaultValueAndLog fail!, ei:{}, describeApiName:{}", actionContext.getTenantId(), actionContext.getObjectApiName(), e);
        }
    }

    private boolean continueToSubmitForCepRequest() {
        return RequestUtil.isCepRequest() && (skipValidationFunction() || !useValidationRules() || arg.skipDuplicateSearchCheck());
    }

    private void diffDataAndLog(EditCalculateParam param, IObjectData objectData, Map<String, List<IObjectData>> detailObjectData, long now, String parameters) {
        Map<String, Object> masterDataDiff = param.diffMasterWithDefaultValue(objectData);
        Map<String, List<Map<String, Object>>> detailsDataDiff = param.diffDetailsWithDefaultValue(detailObjectData);
        if (CollectionUtils.empty(masterDataDiff) && CollectionUtils.empty(detailsDataDiff)) {
            log.info("calculate default value not difference! ei:{}, describeApiName:{}", actionContext.getTenantId(), actionContext.getObjectApiName());
            return;
        }
        Map<String, Object> message = Maps.newHashMap();
        message.put("objectData", masterDataDiff);
        message.put("details", detailsDataDiff);
        long cost = System.currentTimeMillis() - now;
        AuditLogDTO dto = AuditLogDTO.builder()
                .appName(EditCalculateParam.APP_NAME)
                .serverIp(EditCalculateParam.SERVER_IP)
                .profile(EditCalculateParam.PROFILE)
                .tenantId(actionContext.getTenantId())
                .objectApiNames(actionContext.getObjectApiName())
                .objectIds(objectData.getId())
                .eventId(objectData.getId())
                .userId(actionContext.getUser().getUserIdOrOutUserIdIfOutUser())
                .action("validate_default_value")
                .module(actionContext.getActionCode())
                .message(JacksonUtils.toJson(message))
                .parameters(parameters)
                .traceId(TraceContext.get().getTraceId())
                .cost(cost)
                .build();
        BizLogClient.send("biz-audit-log", Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
    }

    protected Map<String, Map<String, Set<String>>> getDetailCalculateFields() {
        return arg.getDetailCalculateFields();
    }

    protected Set<String> getMasterCalculateFields() {
        return arg.getMasterCalculateFields();
    }

    protected Map<String, List<IObjectData>> findDbDetailDataMap() {
        return Collections.emptyMap();
    }

    protected IObjectData findDbMasterData() {
        return ObjectDataExt.of(this.dataList.get(0)).copy();
    }

    private EditCalculateParam buildEditCalculateParam(IObjectData objectData, IObjectData oldMasterData,
                                                       Map<String, List<IObjectData>> detailObjectData,
                                                       Map<String, List<IObjectData>> oldDetailDataMap) {
        // 编辑需要合并db的数据
        if (Objects.nonNull(oldMasterData)) {
            ObjectDataExt.mergeWithDbData(Lists.newArrayList(objectData), Lists.newArrayList(oldMasterData));
            ObjectDataExt.mergeWithDbData(detailObjectData, oldDetailDataMap);
        }
        FieldRelationGraphService fieldRelationGraphService = serviceFacade.getBean(FieldRelationGraphService.class);
        FieldRelationGraph graph = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(objectDescribe, Lists.newArrayList(detailDescribeMap.values()),
                false, true, true);
        IObjectData masterData = ObjectDataExt.of(objectData).copy();
        Map<String, List<IObjectData>> detailDataMap = ObjectDataExt.copyMap(detailObjectData);
        return EditCalculateParam.builder()
                .masterData(masterData)
                .detailDataMap(detailDataMap)
                .oldMasterData(oldMasterData)
                .oldDetailDataMap(oldDetailDataMap)
                .masterDescribe(objectDescribe)
                .detailDescribeMap(detailDescribeMap)
                .excludeDefaultValue(false)
                .includeQuoteField(true)
                .excludeLookupRelateField(false)
                .filterDefaultValueByCalculateFields(true)
                .masterCalculateFields(getMasterCalculateFields())
                .detailCalculateFields(getDetailCalculateFields())
                .getDataIndex(data -> ObjectDataExt.of(data).getDataIndex())
                .graph(graph)
                .build()
                .initModifyData(infraServiceFacade, ObjectDataExt::setDataIndex);
    }

    private void removeUnnecessaryFields() {
        ObjectDescribeExt.of(objectDescribe).getCountFields().stream()
                .filter(x -> !detailObjectData.containsKey(x.getSubObjectDescribeApiName()))
                .forEach(x -> ObjectDataExt.of(objectData).remove(x.getApiName()));
        detailObjectData.forEach((detailApiName, detailDataList) -> {
            IObjectDescribe detailDescribe = objectDescribes.get(detailApiName);
            ObjectDescribeExt.of(detailDescribe).getCountFields().forEach(x ->
                    detailDataList.forEach(detailData -> ObjectDataExt.of(detailData).remove(x.getApiName())));
        });
    }

    @Override
    protected void customProcessResult(Result result) {
        fillMaskFieldValue(result);
        fillMultiLang();
    }

    private void fillMaskFieldValue(Result result) {
        User user = actionContext.getUser();
        if (Objects.isNull(result.getObjectData())) {
            return;
        }
        try {
            IObjectData data = result.getObjectData().toObjectData();
            fillMaskFieldValue(user, objectDescribe, Lists.newArrayList(data));
            Map<String, List<IObjectData>> detailMap = ObjectDataDocument.ofDataMap(result.getDetails());

            if (CollectionUtils.notEmpty(detailMap)) {
                detailMap.forEach((describeApiName, detailDataList) -> {
                    IObjectDescribe describe = detailDescribeMap.get(describeApiName);
                    fillMaskFieldValue(user, describe, detailDataList);
                });
            }

            Map<String, List<SaveMasterAndDetailData.RelatedObjectData>> relatedMap = RelatedDataDocument.toRelatedObjectDataMap(result.getRelatedDataList());
            if (CollectionUtils.notEmpty(relatedMap)) {
                relatedMap.forEach((apiName, related) -> {
                    List<IObjectData> dataList = related.stream()
                            .map(SaveMasterAndDetailData.RelatedObjectData::getDataList)
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList());
                    IObjectDescribe describe = relatedDescribes.get(apiName);
                    fillMaskFieldValue(user, describe, dataList);
                });
            }
        } catch (Exception e) {
            log.warn("fillMaskFieldValue fail!", e);
        }
    }

    private void initDetailsAndRelatedDescribe() {
        objectDescribes.put(objectDescribe.getApiName(), objectDescribe);
        List<IObjectDescribe> detailDescribes = serviceFacade.findDetailDescribes(actionContext.getTenantId(), objectDescribe.getApiName());

        if (CollectionUtils.notEmpty(detailDescribes)) {
            detailDescribes.forEach(x -> {
                detailDescribeMap.put(x.getApiName(), x);
                objectDescribes.put(x.getApiName(), x);
            });
        }

        Map<String, List<RelatedDataDocument>> relatedDataList = arg.getRelatedDataList();
        if (CollectionUtils.empty(relatedDataList)) {
            return;
        }
        relatedDescribes = serviceFacade.findObjects(actionContext.getTenantId(), relatedDataList.keySet());
    }

    private void initObjectData() {
        this.objectData = synchronizeObjectData(arg.getObjectData().toObjectData());
        objectData.setTenantId(actionContext.getTenantId());
        objectData.setDescribeApiName(objectDescribe.getApiName());
        decodeMaskFieldEncryptValue(objectDescribe, Lists.newArrayList(objectData));
        // 移除自增编号
        ObjectDataExt.of(objectData).removeAutoNumber(objectDescribe);
        // 移除图片中的 signedUrl
        ObjectDataExt.of(objectData).removeSignedUrl(objectDescribe);
    }

    protected IObjectData synchronizeObjectData(IObjectData data) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SYNCHRONIZE_DATA_GRAY, actionContext.getTenantId())) {
            return ObjectDataExt.synchronize(data);
        }
        return data;
    }

    protected final void decodeMaskFieldEncryptValue(IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        if (!AppFrameworkConfig.maskFieldEncryptGray(actionContext.getTenantId(), objectDescribe.getApiName())) {
            return;
        }
        if (Objects.isNull(maskFieldLogicService)) {
            maskFieldLogicService = serviceFacade.getBean(MaskFieldLogicService.class);
        }
        maskFieldLogicService.decodeMaskFieldEncryptValue(actionContext.getUser(), dataList, objectDescribe);
    }

    private void initRelatedData() {
        Map<String, List<RelatedDataDocument>> relatedDataList = arg.getRelatedDataList();
        if (CollectionUtils.empty(relatedDataList)) {
            return;
        }
        relatedDataList.forEach((describeApiName, relatedDataInfo) -> {
            IObjectDescribe objectDescribe = relatedDescribes.get(describeApiName);
            List<SaveMasterAndDetailData.RelatedObjectData> relatedObjectDataList = Lists.newArrayList();
            for (RelatedDataDocument dataInfo : relatedDataInfo) {
                if (!serviceFacade.isRelatedListFormSupportObject(actionContext.getUser(), actionContext.getObjectApiName(), objectDescribe, dataInfo.getRelatedFieldName())) {
                    throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
                }
                SaveMasterAndDetailData.RelatedObjectData relatedData = dataInfo.toRelatedObjectData(data -> {
                    data.setDescribeApiName(describeApiName);
                    data.setTenantId(actionContext.getTenantId());
                });
                relatedObjectDataList.add(relatedData);
            }
            relatedObjectData.put(describeApiName, relatedObjectDataList);
        });

        // 屏蔽部分字段的值
        processRelatedObjectData((apiName, dataList) -> {
            IObjectDescribe objectDescribe = relatedDescribes.get(apiName);
            if (Objects.isNull(objectDescribe)) {
                return;
            }
            decodeMaskFieldEncryptValue(objectDescribe, dataList);
            // 移除不支持的字段
            ObjectDataExt.removeByTypes(objectDescribe, dataList, IFieldType.HTML_RICH_TEXT, IFieldType.AUTO_NUMBER, IFieldType.COUNT);
            // 纠正错误的数据
            ObjectDataExt.correctValue(actionContext.getUser(), dataList, objectDescribe);
        });
    }

    private void initDetailData() {
        this.detailObjectData = Maps.newHashMap();
        arg.getDetails()
                .forEach((key, value) -> detailObjectData.put(key, value.stream()
                        .map(ObjectDataDocument::toObjectData)
                        .map(this::synchronizeObjectData)
                        .peek(x -> {
                            x.setTenantId(actionContext.getTenantId());
                            x.setDescribeApiName(key);
                        })
                        .collect(Collectors.toList())));
        if (CollectionUtils.empty(detailObjectData)) {
            return;
        }

        detailObjectData.forEach((apiName, detailDataList) -> {
            if (!objectDescribes.containsKey(apiName)) {
                throw new ObjectDefNotFoundError(I18N.text(I18NKey.OBJECT_NOT_EXIST, apiName));
            }
            IObjectDescribe detailDescribe = objectDescribes.get(apiName);
            decodeMaskFieldEncryptValue(detailDescribe, detailDataList);
            detailDataList.forEach(detailData -> {
                // 移除自增编号
                ObjectDataExt.of(detailData).removeAutoNumber(detailDescribe);
                //初始化删除从对象的统计字段的数据
                ObjectDataExt.of(detailData).removeCount(detailDescribe);
                // 移除图片中的 signedUrl
                ObjectDataExt.of(detailData).removeSignedUrl(objectDescribe);
            });
        });
    }

    private void processLookUpField2Id() {
        if (infraServiceFacade.containLookUpFieldMapping(actionContext.getUser(), objectDescribe, arg.getFieldMapping())) {
            infraServiceFacade.handleDataLookUpField(actionContext.getUser(), objectDescribe, arg.getFieldMapping(), Lists.newArrayList(objectData));
        }
        detailObjectData.forEach((detailDescribeApiName, detailObjectData) -> {
            IObjectDescribe detailDescribe = detailDescribeMap.get(detailDescribeApiName);
            if (infraServiceFacade.containLookUpFieldMapping(actionContext.getUser(), detailDescribe, arg.getFieldMapping())) {
                infraServiceFacade.handleDataLookUpField(actionContext.getUser(), detailDescribe, arg.getFieldMapping(), detailObjectData);
            }
        });
        if (BooleanUtils.isNotTrue(arg.getNeedConvertLookup())) {
            return;
        }
        Map<String, List<IFieldDescribe>> targetApiNameAndLookUpFields = getTargetApiNameAndLookUpFields();
        Map<String, IObjectDescribe> targetDescribes = serviceFacade.findObjects(actionContext.getTenantId(), targetApiNameAndLookUpFields.keySet());
        Map<String, Set<String>> targetApiNameAndLookupFieldValues = getTargetApiNameAndLookupFieldValues(targetApiNameAndLookUpFields);
        targetApiNameAndLookUpFields.forEach((targetApiName, fieldDescribes) -> {
            IObjectDescribe targetDescribe = targetDescribes.get(targetApiName);
            if (Objects.isNull(targetDescribe)) {
                return;
            }
            checkTargetNameField(fieldDescribes, targetDescribe);
            Set<String> nameList = targetApiNameAndLookupFieldValues.get(targetApiName);
            log.info("targetApiName:{},nameList:{}", targetApiName, nameList);
            Map<String, String> nameIdMap = serviceFacade.findObjectIdByName(actionContext.getUser(), targetApiName, Lists.newArrayList(nameList));
            convertLookUpName2Id(fieldDescribes, nameIdMap);
        });
    }

    private void checkTargetNameField(List<IFieldDescribe> fieldDescribes, IObjectDescribe targetDescribe) {
        IFieldDescribe nameField = ObjectDescribeExt.of(targetDescribe).getActiveFieldDescribe(IObjectData.NAME);
        List<IObjectData> dataList = getLookupConvertDataList();
        for (IFieldDescribe fieldDescribe : fieldDescribes) {
            IObjectDescribe describe = objectDescribes.get(fieldDescribe.getDescribeApiName());
            Set<String> nameList = getNameList(fieldDescribe, dataList);
            if (BooleanUtils.isNotTrue(nameField.isUnique()) && CollectionUtils.notEmpty(nameList)) {
                log.warn("processLookUpFieldNameToId error,describe:{},target:{},field:{}", describe.getApiName(), targetDescribe.getApiName(), fieldDescribe.getApiName());
                throw new ValidateException(I18NExt.text(I18NKey.NAME_NOT_UNIQUE, describe.getDisplayName(), fieldDescribe.getLabel(), targetDescribe.getDisplayName()));
            }
        }
    }

    private void convertLookUpName2Id(List<IFieldDescribe> fieldDescribes, Map<String, String> nameIdMap) {
        List<IObjectData> dataList = getLookupConvertDataList();
        for (IFieldDescribe fieldDescribe : fieldDescribes) {
            for (IObjectData data : dataList) {
                if (!Objects.equals(fieldDescribe.getDescribeApiName(), data.getDescribeApiName())) {
                    continue;
                }
                if (!ObjectDataExt.of(data).containsField(FieldDescribeExt.getLookupNameByFieldName(fieldDescribe.getApiName()))) {
                    continue;
                }
                if (Objects.equals(IFieldType.OBJECT_REFERENCE_MANY, fieldDescribe.getType())) {
                    List<String> values = ObjectDataExt.of(data).get(FieldDescribeExt.getLookupNameByFieldName(fieldDescribe.getApiName()), List.class);
                    if (CollectionUtils.empty(values)) {
                        continue;
                    }
                    List<String> ids = Lists.newArrayList();
                    for (String name : values) {
                        String id = checkAndGetId(nameIdMap, name, fieldDescribe);
                        ids.add(id);
                    }
                    data.set(fieldDescribe.getApiName(), ids);
                } else {
                    String name = ObjectDataExt.of(data).get(FieldDescribeExt.getLookupNameByFieldName(fieldDescribe.getApiName()), String.class);
                    if (StringUtils.isBlank(name)) {
                        continue;
                    }
                    String id = checkAndGetId(nameIdMap, name, fieldDescribe);
                    data.set(fieldDescribe.getApiName(), id);
                }
            }
        }
    }


    private String checkAndGetId(Map<String, String> nameIdMap, String name, IFieldDescribe fieldDescribe) {
        String id = nameIdMap.get(name);
        if (StringUtils.isBlank(id)) {
            throw new ValidateException(I18NExt.text(I18NKey.DATA_NOT_FIND, fieldDescribe.getLabel(), name));
        }
        return id;
    }

    private Map<String, Set<String>> getTargetApiNameAndLookupFieldValues(Map<String, List<IFieldDescribe>> objectApiNameAndFields) {
        Map<String, Set<String>> targetApiNameAndLookUpFieldValues = Maps.newHashMap();
        List<IObjectData> dataList = getLookupConvertDataList();
        objectApiNameAndFields.forEach((apiName, fieldDescribes) -> fieldDescribes.forEach(fieldDescribe -> {
            Set<String> nameList = getNameList(fieldDescribe, dataList);
            targetApiNameAndLookUpFieldValues.computeIfAbsent(apiName, (key) -> Sets.newHashSet()).addAll(nameList);
        }));
        return targetApiNameAndLookUpFieldValues;
    }

    private List<IObjectData> getLookupConvertDataList() {
        List<IObjectData> dataList = Lists.newArrayList();
        dataList.add(objectData);
        detailObjectData.values().forEach(dataList::addAll);
        return dataList;
    }

    private Map<String, List<IFieldDescribe>> getTargetApiNameAndLookUpFields() {
        Map<String, List<IFieldDescribe>> targetApiNameAndLookUpFields = Maps.newHashMap();
        objectDescribes.values().forEach(describe -> ObjectDescribeExt.of(describe).getFieldDescribes().stream()
                .filter(field -> (Objects.equals(IFieldType.OBJECT_REFERENCE, field.getType())
                        || Objects.equals(IFieldType.OBJECT_REFERENCE_MANY, field.getType())
                        || (Objects.equals(IFieldType.MASTER_DETAIL, field.getType()) && CollectionUtils.empty(arg.getDetails())))
                        && field.isActive())
                .forEach(field -> {
                    String targetApiName;
                    if (Objects.equals(IFieldType.OBJECT_REFERENCE_MANY, field.getType())
                            || Objects.equals(IFieldType.OBJECT_REFERENCE, field.getType())) {
                        targetApiName = ObjectReferenceWrapper.of(field).getTargetApiName();
                    } else if (Objects.equals(IFieldType.MASTER_DETAIL, field.getType())) {
                        MasterDetailFieldDescribe fieldDescribe = (MasterDetailFieldDescribe) field;
                        targetApiName = fieldDescribe.getTargetApiName();
                    } else {
                        return;
                    }
                    targetApiNameAndLookUpFields.computeIfAbsent(targetApiName, (key) -> Lists.newArrayList()).add(field);
                }));
        return targetApiNameAndLookUpFields;
    }

    private Set<String> getNameList(IFieldDescribe fieldDescribe, List<IObjectData> dataList) {
        Set<String> result = Sets.newHashSet();
        for (IObjectData data : dataList) {
            if (Objects.equals(IFieldType.OBJECT_REFERENCE_MANY, fieldDescribe.getType())) {
                Object values = ObjectDataExt.of(data).get(FieldDescribeExt.getLookupNameByFieldName(fieldDescribe.getApiName()));
                if (Objects.isNull(values)) {
                    continue;
                }
                if (!(values instanceof List)) {
                    IObjectDescribe objectDescribe = objectDescribes.get(fieldDescribe.getDescribeApiName());
                    log.error("field value type should List,fieldApiName:{}", fieldDescribe.getApiName());
                    throw new ValidateException(I18NExt.text(I18NKey.REFERENCE_MANY_TYPE_ERROR, objectDescribe.getDisplayName(), fieldDescribe.getLabel()));
                }
                List<Object> manyValues = (List<Object>) values;
                if (CollectionUtils.empty(manyValues)) {
                    continue;
                }
                if (!(manyValues.get(0) instanceof String)) {
                    log.error("field value type should string,fieldApiName:{}", fieldDescribe.getApiName());
                    IObjectDescribe objectDescribe = objectDescribes.get(fieldDescribe.getDescribeApiName());
                    throw new ValidateException(I18NExt.text(I18NKey.REFERENCE_MANY_VALUE_TYPE_ERROR, objectDescribe.getDisplayName(), fieldDescribe.getLabel()));
                }
                List<String> fieldValues = manyValues.stream().filter(Objects::nonNull)
                        .map(String::valueOf).collect(Collectors.toList());
                result.addAll(Sets.newHashSet(fieldValues));
            } else {
                String value = ObjectDataExt.of(data).get(FieldDescribeExt.getLookupNameByFieldName(fieldDescribe.getApiName()), String.class);
                if (StringUtils.isBlank(value)) {
                    continue;
                }
                result.add(value);
            }
        }
        return result;
    }

    protected final void processRelatedObjectData(BiConsumer<String, List<IObjectData>> action) {
        if (CollectionUtils.empty(relatedObjectData)) {
            return;
        }
        relatedObjectData.forEach((apiName, relatedObjectData) -> {
            List<IObjectData> dataList = relatedObjectData.stream()
                    .map(SaveMasterAndDetailData.RelatedObjectData::getDataList)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            action.accept(apiName, dataList);
        });
    }

    private void correctFieldValue() {
        ObjectDataExt.correctValue(actionContext.getUser(), Lists.newArrayList(objectData), objectDescribe);
        detailObjectData.forEach((apiName, detailDataList) -> {
            if (!objectDescribes.containsKey(apiName)) {
                return;
            }
            ObjectDataExt.correctValue(actionContext.getUser(), detailDataList, objectDescribes.get(apiName));
        });
    }

    private void findUniqueRule() {
        if (!actionContext.needCheckUniquenessRule()) {
            return;
        }

        uniqueRule = infraServiceFacade.findBySwitchCache(actionContext.getTenantId(),
                SwitchCacheProviderManager.UNIQUE_RULE,
                UniqueRuleSwitchCacheProvider.USE_WHEN_CALL_OPEN_API,
                objectDescribe.getApiName(),
                () -> serviceFacade.findByDescribeApiName(actionContext.getTenantId(), objectDescribe.getApiName())
        ).orElse(null);
    }

    protected final void resetLastLifeStatus() {
        lastLifeStatus = ObjectDataExt.of(objectData).getLifeStatus();
    }

    protected final boolean isLifeStatusChanged() {
        return ObjectDataExt.of(objectData).getLifeStatus() != lastLifeStatus;
    }

    //记录生命状态的变更记录
    protected final void recordLifeStatusModifyLog() {
        if (!isLifeStatusChanged()) {
            return;
        }
        Map<String, Object> updateField = Maps.newHashMap();
        updateField.put(ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectDataExt.of(objectData).getLifeStatus().getCode());
        Map<String, Object> dbData = Maps.newHashMap();
        dbData.put(ObjectLifeStatus.LIFE_STATUS_API_NAME, lastLifeStatus.getCode());
        dbData.put(ObjectDataExt.ID, objectData.getId());
        serviceFacade.log(User.systemUser(actionContext.getTenantId()), EventType.MODIFY, ActionType.Modify,
                objectDescribe, objectData, updateField, ObjectDataExt.of(dbData).getObjectData());
    }

    protected final boolean checkBlockValidationRules() {
        return Objects.nonNull(ruleResult) && ruleResult.isMatch()
                && !RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_650)
                && acceptNonBlockingResult();
    }

    protected void validateArg() {
        //校验arg参数的完整性和合法性
        ObjectDataExt.of(objectData).validate(objectDescribe);
        validateRelatedArg();
        //校验币种信息
        checkCurrency();
        //校验自由审批参数
        validateFreeApprovalDef();
    }

    private void validateFreeApprovalDef() {
        if (CollectionUtils.empty(arg.getFreeApprovalDef())) {
            return;
        }
        serviceFacade.freeApprovalDefValidate(actionContext.getUser(), arg.getFreeApprovalDef());
    }

    private void validateRelatedArg() {
        processRelatedObjectData((apiName, dataList) -> {
            IObjectDescribe objectDescribe = relatedDescribes.get(apiName);
            dataList.forEach(data -> ObjectDataExt.of(data).validate(objectDescribe));
        });
    }

    private void checkCurrency() {
        checkMasterAndDetailCurrency();
        stopWatch.lap("checkMasterAndDetailCurrency");
        checkRelatedCurrency();
        stopWatch.lap("checkRelatedCurrency");
    }

    private void checkMasterAndDetailCurrency() {
        if (!ObjectDescribeExt.of(objectDescribe).containsMultiCurrencyField()) {
            return;
        }
        //校验币种是否存在
        ObjectDataExt.of(objectData).checkCurrencyOption(objectDescribe);
        //校验汇率有值，币种为空的场景
        ObjectDataExt.of(objectData).checkCurrency();
        CollectionUtils.nullToEmpty(detailObjectData).forEach((apiName, dataList) ->
                dataList.forEach(data -> ObjectDataExt.of(data).checkCurrency()));

        //校验主和从的币种是否一致
        validateMasterDetailCurrency();
    }

    private void checkRelatedCurrency() {
        if (CollectionUtils.empty(relatedObjectData)) {
            return;
        }

        processRelatedObjectData((apiName, relatedDataList) -> {
            IObjectDescribe describe = relatedDescribes.get(apiName);
            if (Objects.isNull(describe) || !ObjectDescribeExt.of(describe).containsMultiCurrencyField()) {
                return;
            }
            relatedDataList.stream()
                    .map(ObjectDataExt::of)
                    //校验币种是否存在
                    .peek(data -> data.checkCurrencyOption(describe))
                    //校验汇率有值，币种为空的场景
                    .forEach(ObjectDataExt::checkCurrency);
        });
    }

    protected void validateMasterDetailCurrency() {
        CollectionUtils.nullToEmpty(detailObjectData).forEach((apiName, dataList) ->
                dataList.forEach(data -> ObjectDataExt.of(data).checkDetailCurrency(objectData)));
    }

    protected void validate() {
        if (skipBaseValidate()) {
            return;
        }

        //校验主对象的锁定状态和生命状态
        validateDataStatus();
        stopWatch.lap("validateMasterStatus");

        validateDataType();
        stopWatch.lap("validateDataType");

        validateWhatListCount();
        stopWatch.lap("validateWhatListCount");

        //校验富文本是否合法
        validateRichTextValue();
        stopWatch.lap("validateRichTextValue");

        validateTypeForDataOwnDept();
        stopWatch.lap("validateTypeForDataOwnDept");

        if (!skipValidateLookupData()) {
            validateLookupData(objectData, objectDescribe);
            stopWatch.lap("validateLookupData");
        }
        // 校验变更规则的校验条件
        validateChangeRuleWithChangeData();
        stopWatch.lap("validateChangeRuleWithChangeData");
        // 推拉单超额校验
        validateConvertRulesExcessCheck();
        stopWatch.lap("validateConvertRulesExcessCheck");

        //验证规则校验
        validateValidationRules(objectData, detailObjectData, getIRule());
        stopWatch.lap("validateRule");

        //校验从对象是否合法
        objectDescribes.keySet().stream().filter(x -> !x.equals(objectDescribe.getApiName())).forEach(x ->
                validateDetail(x, detailObjectData.getOrDefault(x, Lists.newArrayList())));
        stopWatch.lap("validateDetails");
        //校验相关对象是否合法
        validateRelated();
        stopWatch.lap("validateRelated");

        // 校验唯一性规则
        checkUniquenessRule();
        stopWatch.lap("checkUniquenessRule");

        //自定义函数校验
        callValidationFunction();
        stopWatch.lap("validateCustomFunction");

        //校验查重规则
        checkDuplicate();
        stopWatch.lap("duplicateSearch");

        if (isDuplicate || checkBlockValidationRules()) {
            throw new AcceptableValidateException(buildValidateResult());
        }

        //校验人员部门字段梳理
        checkDepartmentEmployeeManyMaxNum(objectDescribe, objectData);
        // 查找关联多选，校验字段个数
        checkObjectReferenceManyMaxNum(objectDescribe, objectData);
        // 校验日期范围字段值
        checkDateRangeFieldValue();
    }

    protected void validateConvertRulesExcessCheck() {
        if (Objects.isNull(convertRuleDataContainer)) {
            return;
        }
        infraServiceFacade.doConvertRulesExcessCheck(objectDescribe, objectData, detailDescribeMap, detailObjectData, convertRuleDataContainer.getConvertRuleList());
    }

    private void checkDateRangeFieldValue() {
        List<String> errorMsg = ObjectDataExt.of(objectData).validateDateRangeField(objectDescribe);
        if (CollectionUtils.notEmpty(errorMsg)) {
            throw new ValidateException(errorMsg.get(0));
        }
        if (CollectionUtils.notEmpty(detailObjectData)) {
            detailObjectData.forEach((apiName, dataList) ->
                    dataList.forEach(data -> {
                        List<String> detailErrorMsg = ObjectDataExt.of(data).validateDateRangeField(detailDescribeMap.get(apiName));
                        if (CollectionUtils.notEmpty(detailErrorMsg)) {
                            throw new ValidateException(detailErrorMsg.get(0));
                        }
                    }));
        }
    }


    protected void validateChangeRuleWithChangeData() {

    }

    private boolean skipValidateLookupData() {
        return AppFrameworkConfig.isSkipValidateLookupGrayTenant(actionContext.getTenantId())
                || BooleanUtils.isTrue(arg.getSkipValidateLookupData());
    }

    private void validateWhatListCount() {
        List<WhatList> whatListFields = ObjectDescribeExt.of(objectDescribe).getWhatListFields();
        int maxCount = AppFrameworkConfig.getMaxWhatListDataCount(actionContext.getTenantId());
        CollectionUtils.nullToEmpty(whatListFields).forEach(whatList -> {
            List<ObjectDataExt.WhatListData> list = ObjectDataExt.of(objectData).parseWhatListData(whatList);
            if (list.size() > maxCount) {
                throw new ValidateException(I18NExt.getOrDefault(I18NKey.WHAT_LIST_DATA_EXCEED_MAX_COUNT,
                        I18NKey.WHAT_LIST_DATA_EXCEED_MAX_COUNT, whatList.getLabel(), maxCount));
            }
        });
    }

    private void validateDataType() {
        //校验主对象
        serviceFacade.validateDataType(objectDescribe, Lists.newArrayList(objectData), actionContext.getUser());
        //校验从对象
        detailObjectData.entrySet()
                .stream()
                .filter(entry -> objectDescribes.containsKey(entry.getKey()))
                .forEach(entry -> serviceFacade.validateDataType(objectDescribes.get(entry.getKey()),
                        Lists.newArrayList(entry.getValue()), actionContext.getUser()));

        // 校验相关对象
        processRelatedObjectData((apiName, dataList) -> {
            IObjectDescribe objectDescribe = relatedDescribes.get(apiName);
            serviceFacade.validateDataType(objectDescribe, dataList, actionContext.getUser());
        });
    }

    private void checkObjectReferenceManyMaxNum(IObjectDescribe objectDescribe, IObjectData objectData) {
        List<IFieldDescribe> objectRefManyFields = ObjectDescribeExt.of(objectDescribe).getAllObjectRefManyFieldDescribes();
        if (CollectionUtils.empty(objectRefManyFields) || Objects.isNull(objectData)) {
            return;
        }
        ObjectDataExt dataExt = ObjectDataExt.of(objectData);
        for (IFieldDescribe objectRefManyField : objectRefManyFields) {
            List<String> fieldValues = dataExt.getManyField(objectRefManyField.getApiName());
            int maxNum = FieldManyMaxConfig.getObjectReferenceManyMaxLimit(actionContext.getTenantId(), objectDescribe.getApiName());
            if (fieldValues.size() > maxNum) {
                throw new ValidateException(I18N.text(I18NKey.OBJECT_REFERENCE_MANY_FIELD_BEYOND_MAX_LIMIT, maxNum, objectRefManyField.getLabel()));
            }
        }
    }

    private void checkDepartmentEmployeeManyMaxNum(IObjectDescribe objectDescribe, IObjectData data) {
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(objectDescribe);
        List<IFieldDescribe> manyFields = objectDescribeExt.getFieldDescribes().stream()
                .filter(field ->
                        field.getType().equals(IFieldType.EMPLOYEE_MANY) || field.getType().equals(IFieldType.DEPARTMENT_MANY)
                ).collect(Collectors.toList());
        if (CollectionUtils.empty(manyFields)) {
            return;
        }
        ObjectDataExt dataExt = ObjectDataExt.of(objectData);
        manyFields.stream()
                .filter(a -> Objects.nonNull(data.get(a.getApiName()))).forEach(fieldDescribe -> {
                    List<String> fieldValues = dataExt.getManyField(fieldDescribe.getApiName());
                    int maxNum = FieldDescribeExt.getMaxNum(actionContext.getTenantId(), actionContext.getObjectApiName());
                    if (fieldValues.size() > maxNum) {
                        throw new ValidateException(I18NExt.getOrDefault(I18NKey.EMPLOYEE_DEPARTMENT_MANY_FIELD_BEYOND_MAX_LIMIT_V2, I18NKey.EMPLOYEE_DEPARTMENT_MANY_FIELD_BEYOND_MAX_LIMIT_V2, maxNum, fieldDescribe.getLabel()));
                    }
                });
    }

    // 获取主对象的字段值
    private List<String> getManyField(String apiName) {
        try {
            Object value = objectData.get(apiName);
            if (null == value) {
                return Lists.newArrayList();
            }
            String str;
            if (value instanceof String) {
                str = (String) value;
            } else {
                str = JSON.toJSONString(value);
            }
            return JSON.parseObject(str, List.class);
        } catch (Exception e) {
            log.warn("getManyField error! apiName:{}, objectData:{}", apiName, objectData.toJsonString(), e);
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
    }

    private void validateRichTextValue() {
        List<HtmlRichText> richTextFields = ObjectDescribeExt.of(objectDescribe).getRichTextFields();
        if (CollectionUtils.empty(richTextFields)) {
            return;
        }

        richTextFields.stream()
                .filter(a -> Objects.nonNull(objectData.get(a.getApiName())))
                .forEach(a -> RichTextExt.validateHtml(String.valueOf(objectData.get(a.getApiName()))));
    }

    private void validateDataStatus() {
        validateMasterStatus();
    }

    //校验主对象的锁定状态和生命状态
    private void validateMasterStatus() {
        if (!needTriggerMasterApproval()) {
            return;
        }
        //主对象的审批例外人不校验锁定状态和生命状态
        if (isAssigneesExceptional) {
            return;
        }
        IObjectData realMasterData = getRealMasterObjectData(objectData);
        if (realMasterData == null) {
            throw new ValidateException(I18N.text(I18NKey.MASTER_DATA_INVALID_OR_DELETED));
        }
        if (ObjectDataExt.of(realMasterData).isLock()) {
            throw new ValidateException(I18N.text(I18NKey.CANNOT_OPERATE_BECAUSE_OF_MASTER_LOCKED));
        }
        if (ObjectDataExt.of(realMasterData).isInChange()) {
            throw new ValidateException(I18N.text(I18NKey.CANNOT_OPERATE_BECAUSE_OF_MASTER_IN_CHANGE));
        }
    }

    @Override
    protected IObjectData getPreObjectData() {
        return objectData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return objectData;
    }


    @Override
    protected Map<String, List<IObjectData>> getPostObjectDetails() {
        return detailObjectData;
    }

    @Override
    protected Map<String, Object> getActionParams(A arg) {
        return super.getActionParams(arg);
    }

    /**
     * 跳过按钮的前置执行动作
     *
     * @return
     */
    @Override
    protected boolean skipPreFunction() {
        return true;
    }

    protected abstract void checkUniquenessRule();

    protected final Optional<UniqueRuleSearchResult.DuplicateData> getDuplicateDataByUniqueRuleSearch() {
        UniqueRuleQuery.Arg arg = UniqueRuleQuery.Arg.from(objectData);
        UniqueRuleQuery.Result duplicateData = serviceFacade.findDuplicateData(actionContext.getTenantId(), arg, objectDescribe, uniqueRule);
        if (duplicateData.isEmpty()) {
            return Optional.empty();
        }
        List<String> dataIds = duplicateData.getUniqueRuleData().stream()
                .flatMap(uniqueRuleData -> uniqueRuleData.getDuplicateIds().stream())
                .filter(id -> !Objects.equals(id, objectData.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(dataIds)) {
            return Optional.empty();
        }
        return Optional.of(UniqueRuleSearchResult.DuplicateData.of(objectData.getId(), dataIds));
    }

    private boolean useValidationRules() {
        return Objects.isNull(arg.getOptionInfo()) || Optional.ofNullable(arg.getOptionInfo().getUseValidationRule()).orElse(Boolean.TRUE);
    }

    private boolean skipValidationFunction() {
        return Objects.nonNull(arg.getOptionInfo()) && arg.getOptionInfo().isSkipFuncValidate();
    }

    @Override
    protected String getButtonApiName() {
        return actionContext.getActionCode() + "_button_default";
    }

    @Override
    protected boolean skipCheckButtonConditions() {
        return true;
    }

    protected void callValidationFunction() {
        // 补充临时 id
        List<IObjectData> noIdDataTuple = null;
        try {
            noIdDataTuple = fillDetailTemporaryId();
            ButtonExecutor.Arg executorArg = ButtonExecutor.Arg.builder()
                    .buttonApiName(getButtonApiName())
                    .describeApiName(actionContext.getObjectApiName())
                    .objectDataId(objectData.getId())
                    .args(getArgs())
                    .objectData(objectData)
                    .details(detailObjectData)
                    .relatedDataList(relatedObjectData)
                    .skipValidationFunction(skipValidationFunction())
                    .build();
            log.debug("callValidationFunction arg:{}", executorArg);
            validatedFunctionResult = infraServiceFacade.triggerValidationFunction(actionContext.getUser(), executorArg);
            log.debug("callValidationFunction result:{}", validatedFunctionResult);
            // 合并从对象数据
            mergeDetailData();

            // 有返回值，抛出阻断异常
            if (validationFunctionIsBlock() && (RequestUtil.isMobileRequestBeforeVersion(VERSION_670) || !acceptNonBlockingResult())) {
                String message = validatedFunctionResult.getReturnValue() == null ? "" : validatedFunctionResult.getReturnValue().toString();
                throw new ValidateException(message);
            }

            // 有返回值，抛出阻断异常
            if (validatedFunctionResult.isHasReturnValue() && acceptNonBlockingResult()) {
                throw new AcceptableValidateException(buildValidateResult());
            }
        } finally {
            // 删除临时id
            ObjectDataExt.removeTemporaryId(noIdDataTuple);
        }

    }

    private List<IObjectData> fillDetailTemporaryId() {
        if (!AppFrameworkConfig.isValidationFunctionMergeDetailDataGray(actionContext.getTenantId(), actionContext.getObjectApiName())) {
            return Collections.emptyList();
        }
        if (CollectionUtils.empty(detailObjectData)) {
            return Collections.emptyList();
        }
        return ObjectDataExt.fillTemporaryId(detailObjectData);
    }

    private void mergeDetailData() {
        if (!AppFrameworkConfig.isValidationFunctionMergeDetailDataGray(actionContext.getTenantId(), actionContext.getObjectApiName())) {
            return;
        }
        ButtonExecutor.DetailDataMergeResult detailDataMergeResult = validatedFunctionResult.getDetailDataMergeResult();
        if (Objects.isNull(detailDataMergeResult)) {
            return;
        }
        MergeStateContainer sourceMergeStateContainer = getSourceMergeStateContainer();
        if (Objects.isNull(sourceMergeStateContainer)) {
            return;
        }
        MergeStateContainer container = MergeStateContainer.of(detailDataMergeResult.getDetailsToAdd(), detailDataMergeResult.getDetailsToUpdate(), detailDataMergeResult.getDetailsToDelete());
        sourceMergeStateContainer.merge(container);
        replaceDetailDataMergeResult(sourceMergeStateContainer);
        // 校验币种
        checkMasterAndDetailCurrency();
        if (log.isInfoEnabled()) {
            log.info("detailObjectData:{}", JacksonUtils.toJson(arg.getDetails()));
        }
    }

    protected void replaceDetailDataMergeResult(MergeStateContainer sourceMergeStateContainer) {

    }

    protected MergeStateContainer getSourceMergeStateContainer() {
        return null;
    }

    private boolean acceptNonBlockingResult() {
        if (RequestUtil.isCepRequest()) {
            return true;
        }
        return arg.acceptNonBlockingResult();
    }

    private boolean acceptRedirectAction() {
        if (RequestUtil.isCepRequest()) {
            return true;
        }
        return arg.acceptRedirectAction();
    }

    private boolean validationFunctionIsBlock() {
        return validatedFunctionResult.isHasReturnValue() && validatedFunctionResult.isBlock();
    }

    protected final List<IObjectData> getAllObjectData() {
        List<IObjectData> ret = detailObjectData.values().stream().flatMap(x -> x.stream()).collect(Collectors.toList());
        ret.add(objectData);
        return ret;
    }

    protected void setDefaultRecordType(IObjectData objectData, IObjectDescribe describe) {
        objectData.setTenantId(describe.getTenantId());
        String recordType = objectData.getRecordType();
        if (Strings.isNullOrEmpty(recordType) || "default".equals(recordType) || "sail".equals(recordType)) {
            objectData.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        } else {
            validateRecordType(describe, recordType);
        }
    }

    protected void validateRecordType(IObjectDescribe describe, String recordType) {
        if (Strings.isNullOrEmpty(recordType)) {
            return;
        }
        Optional<IRecordTypeOption> optional = ObjectDescribeExt.of(describe).getRecordTypeOption(recordType);
        IRecordTypeOption option = optional.orElseThrow(() -> new RecordTypeNotFound(I18N.text(I18NKey.RECORD_TYPE_NOT_EXIST)));
        if (!option.isActive()) {
            throw new RecordTypeNotFound(I18N.text(I18NKey.RECORD_TYPE_IS_DISABLED, option.getLabel()));
        }
    }

    // 是否查重
    protected boolean isDuplicateSearch() {
        return Objects.isNull(arg.getOptionInfo()) || Optional.ofNullable(arg.getOptionInfo().getIsDuplicateSearch()).orElse(Boolean.TRUE);
    }

    /**
     * @return 执行 Redirect 的时候,忽略客户端类型,返回所有结果
     */
    protected boolean needAllRedirectResult() {
        return Optional.ofNullable(arg.getOptionInfo()).map(OptionInfo::getNeedAllRedirectResult).orElse(false);
    }

    // 是否重复
    protected void checkDuplicate() {
        if (!serviceFacade.needDuplicateSearch(actionContext.getRequestContext(), objectDescribe.getApiName(), arg.duplicateRuleApiName(), arg.skipDuplicateSearchCheck())) {
            return;
        }
        //给查重数据加锁，防止数据并发新建，导致查重漏校验
        duplicateSearchLock = serviceFacade.duplicateSearchLock(actionContext.getUser(), objectDescribe, objectData);
        stopWatch.lap("duplicateSearchLock");
        // 单独新建对象，需要在调用查重前，确保数据中有负责人字段
        if (getObjectAction() == ObjectAction.CREATE) {
            modifyOwnerOrTeamMemberBeforeCreate(objectData, objectDescribe);
        }
        doDuplicateSearch();
    }

    private void doDuplicateSearch() {
        //进行查重
        List<DuplicateSearchResult.DuplicateData> duplicateData = getDuplicateData();
        isDuplicate = CollectionUtils.notEmpty(duplicateData) && duplicateData.stream().anyMatch(x -> CollectionUtils.notEmpty(x.getDataIds()));
        if (isDuplicate && arg.fromImport()) {
            SaveActionServiceFacade saveActionServiceFacade = serviceFacade.getBean(SaveActionServiceFacade.class);
            saveActionServiceFacade.buildDuplicateImportAddError(actionContext.getUser(), duplicateData, objectDescribe, true);
        }
        if (isDuplicate && !acceptNonBlockingResult() && !actionContext.isFromFunction()) {
            throw new DuplicateSearchValidationException(I18N.text(I18NKey.DUPLICATED_DATA));
        }
    }

    // 查询重复数据
    protected List<DuplicateSearchResult.DuplicateData> getDuplicateData() {
        return serviceFacade.getDuplicateData(actionContext.getUser(), objectDescribe, objectData, arg.duplicateRuleApiName(), arg.skipFuzzyRuleDuplicateSearch());
    }

    protected void validateValidationRules(IObjectData objectData,
                                           Map<String, List<IObjectData>> details,
                                           String ruleOperation) {
        if (!useValidationRules()) {
            return;
        }

        if (AppFrameworkConfig.isGraySwitchCache(actionContext.getTenantId())) {
            // 所有对象都没有开启唯一性规则，则直接返回
            boolean allIsFalse = objectDescribes.keySet().stream()
                    .noneMatch(objectApiName -> {
                        Boolean status = infraServiceFacade.findSwitchStatusIfPresent(actionContext.getTenantId(),
                                SwitchCacheProviderManager.VALIDATE_RULE, ruleOperation, objectApiName).orElse(null);
                        return BooleanUtils.isNotFalse(status);
                    });
            if (allIsFalse) {
                return;
            }
        }
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap(objectDescribes);
        describeMap.putAll(relatedDescribes);

        Map<String, List<IObjectData>> relatedDataMap = getRelatedDataMap();
        ruleResult = infraServiceFacade.validateRule(actionContext.getUser(), ruleOperation, describeMap, objectData, details, relatedDataMap);
        // 6.5之前的终端抛异常,根据开关控制前端抛异常
        if (ruleResult.isEnableBlocking() && !acceptNonBlockingResult()) {
            throw new ValidateException(Joiner.on(", ").join(ruleResult.getBlockMessages()));
        }
        // 阻断的抛异常
        if (checkBlockValidationRules()) {
            throw new AcceptableValidateException(buildValidateResult());
        }
    }

    private Map<String, List<IObjectData>> getRelatedDataMap() {
        Map<String, List<IObjectData>> relatedDataMap = Maps.newHashMap();
        if (CollectionUtils.empty(relatedObjectData)) {
            return relatedDataMap;
        }
        relatedObjectData.forEach((apiName, relatedData) -> {
            List<IObjectData> dataList = relatedData.stream()
                    .map(SaveMasterAndDetailData.RelatedObjectData::getDataList)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            relatedDataMap.computeIfAbsent(apiName, key -> Lists.newArrayList()).addAll(dataList);
        });
        return relatedDataMap;
    }

    private void validateRelated() {
        if (CollectionUtils.empty(relatedObjectData)) {
            return;
        }
        validateRelatedListFormComponentCount();
        processRelatedObjectData(this::validateRelated);
    }

    private void validateRelatedListFormComponentCount() {
        if (CollectionUtils.empty(relatedObjectData)) {
            return;
        }
        TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo.builder()
                .licenseService(serviceFacade)
                .user(actionContext.getUser())
                .build()
                .init(Sets.newHashSet(ModulePara.ModuleBiz.MASTER_RELATED.getBizCode()));
        // 校验对象组件的个数
        tenantLicenseInfo.validateRelatedListFormComponentCount(getRelatedObjectCount(), actionContext.getObjectApiName());

        // 提交数据,校验每个组件单独包含的数据条数
        relatedObjectData.forEach((relatedApiName, relatedObjectDataList) -> {
            relatedObjectDataList.forEach(it -> {
                String name = String.format("%s_%s", relatedApiName, it.getRelatedFieldName());
                tenantLicenseInfo.checkRelatedObjectCount(it.size(), name);
            });
        });

    }

    private int getRelatedObjectCount() {
        return relatedObjectData.values().stream()
                .mapToInt(List::size)
                .sum();
    }

    private void validateRelated(String apiName, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }

        IObjectDescribe objectDescribe = relatedDescribes.get(apiName);
        // 校验部门,人员多选
        objectDataList.forEach(data -> checkDepartmentEmployeeManyMaxNum(objectDescribe, data));
        objectDataList.forEach(data -> checkObjectReferenceManyMaxNum(objectDescribe, data));
    }

    protected void validateDetail(String detailApiName, List<IObjectData> detailDataList) {
        //根据资源包校验从对象数据的条数
        if (!AppFrameworkConfig.isNotCheckCountDetailObject(detailApiName)) {
            TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo.builder()
                    .licenseService(serviceFacade)
                    .user(actionContext.getUser())
                    .build()
                    .init(Sets.newHashSet(ModulePara.ModuleBiz.MASTER_DETAIL.getBizCode()));
            tenantLicenseInfo.checkDetailObjectCount(detailDataList.size(), detailApiName);
        }

        //TODO 前端没有传apiName的只能从库里取数据，这个校验会影响性能，最好能把主从关系的IsRequiredWhenMasterCreate选项去掉。
        IObjectDescribe describe = this.objectDescribes.get(detailApiName);
        MasterDetailFieldDescribe masterDetailField = ObjectDescribeExt.of(describe).getMasterDetailFieldDescribe()
                .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR)));
        if (masterDetailField.getIsRequiredWhenMasterCreate()) {
            if (!this.detailObjectData.containsKey(detailApiName)) {
                detailDataList = serviceFacade.findDetailObjectDataWithPage(actionContext.getUser(), objectDescribe.getApiName(), objectData.getId(),
                        describe, 1, 1, null).getData();
            }
            if (CollectionUtils.empty(detailDataList)) {
                if (isFromChangeOrder()) {
                    throw new ValidateException(I18N.text(I18NKey.DETAIL_OBJ_NONE_CHANGE_RULE, describe.getDisplayName()));
                }
                throw new ValidateException(I18N.text(I18NKey.CREAT_MASTER_DATA_DETAIL_DATA_CAN_NOT_IS_NULL, describe.getDisplayName()));
            }
        }
        if (CollectionUtils.notEmpty(detailDataList)) {
            detailDataList.forEach(data -> {
                checkDepartmentEmployeeManyMaxNum(describe, data);
                checkObjectReferenceManyMaxNum(describe, data);
            });
        }
    }

    protected void addMasterDetailFieldIntoDetailDataList(String masterId,
                                                          IObjectDescribe detailDescribe,
                                                          List<IObjectData> detailDataList) {
        if (CollectionUtils.empty(detailDataList)) {
            return;
        }
        String mdFieldApiName = ObjectDescribeExt.of(detailDescribe)
                .getMasterDetailFieldName(objectDescribe.getApiName()).orElseThrow(() -> new ValidateException(I18N.text(I18NKey.DETAIL_OBJECT_IS_NOT_FIND_MD_FIELD)));
        detailDataList.forEach(data -> data.set(mdFieldApiName, masterId));
    }

    protected void modifyObjectDataBeforeCreate(IObjectData objectData, IObjectDescribe describe) {
        //在正式更新之前,修改objectData中的一些值,比如修改时间、业务类型、signIn字段的默认值等。
        modifySystemAndPackageFieldsOfObjectDataBeforeCreate(objectData, describe);
        //设置owner以及teamMember
        modifyOwnerOrTeamMemberBeforeCreate(objectData, describe);
        //富文本中图片文件临时转正式
        convertPathForRichText(objectData, describe);
        //临时文件转正式
        serviceFacade.processData(describe, Lists.newArrayList(objectData));
        // 变更单对象的字段默认值
        setDefaultForChangeOrder(objectData, describe);
    }

    protected void convertPathForRichText(IObjectData objectData, IObjectDescribe describe) {
        infraServiceFacade.convertPathForHtmlRichText(actionContext.getUser(), objectData, describe);
        infraServiceFacade.convertPathForCooperativeRichText(actionContext.getUser(), objectData, describe);
    }

    private void extractAbstractOfRichText(IObjectData objectData, IObjectDescribe describe) {
        ObjectDataExt.of(objectData).extractAbstractOfRichText(describe);
    }

    protected void modifySystemAndPackageFieldsOfObjectDataBeforeCreate(IObjectData objectData, IObjectDescribe describe) {
        //新建的数据过滤lock_user字段，防止lock_user字段的默认值是""导致元数据校验报错
        ObjectDataExt.of(objectData).remove(ObjectLockStatus.LOCK_USER_API_NAME);

        // 修改objectData,写入tenantId、创建人、最后修改人、最后修改时间、生命周期等系统字段。
        setDefaultSystemInfo(objectData);
        //设置默认业务类型
        setDefaultRecordType(objectData, describe);
        //设置签到字段的相关默认值
        setDefaultForSignIn(objectData, describe);
        //设置支付字段的相关默认值
        setDefaultForPayment(objectData, describe);
    }

    protected void setDefaultForChangeOrder(IObjectData objectData, IObjectDescribe describe) {
    }

    protected void modifyOwnerOrTeamMemberBeforeCreate(IObjectData objectData, IObjectDescribe describe) {
        //如果是从对象,那么要从库中的主对象中获取从owner
        if (ObjectDescribeExt.of(describe).isSlaveObject()) {
            // 同步主对象负责人，只执行一次
            if (!isFirstModifyOwner(describe)) {
                return;
            }
            // 同步主对象的负责人、外部负责人
            synchronizeDetailOwnerFromMasterObjectDataInDB(objectData, describe);
        } else {
            //如果是主对象,要根据客户传过来的owner,塞到相关团队成员中
            setDefaultTeamMember(objectData);
            addCreatorToTeamMember(objectData, actionContext.getUser());
        }
    }

    protected void addInterconnectToTeamMember(User user, IObjectDescribe describe, IObjectData objectData) {
        serviceFacade.setupTeamInterconnectedDepartments(user, describe, Lists.newArrayList(objectData));
    }

    protected void addCreatorToTeamMember(IObjectData objectData, User user) {
        if (actionContext.isFromFunction() || actionContext.isFromOpenAPI()
                || actionContext.isFromSmartForm() || !Strings.isNullOrEmpty(user.getOutTenantId())
                || objectData.getOwner().contains(user.getUserId())) {
            return;
        }
        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = infraServiceFacade.findOptionalFeaturesSwitch(actionContext.getTenantId(), objectDescribe);
        if (!optionalFeaturesSwitch.getIsRelatedTeamEnabled()) {
            return;
        }
        String configValue = queryConfigValue(user, objectData.getDescribeApiName());
        if (StringUtils.isBlank(configValue)) {
            return;
        }
        ObjectDataExt dataExt = ObjectDataExt.of(objectData);
        if (Boolean.TRUE.toString().equals(configValue)) {
            TeamMember teamMember = new TeamMember(user.getUserId(), TeamMember.Role.NORMAL_STAFF,
                    TeamMember.Permission.READONLY);
            dataExt.addTeamMembers(Lists.newArrayList(teamMember));
        }
        if (StringUtils.equals(configValue, TeamMember.Permission.READONLY.getValue())) {
            TeamMember teamMember = new TeamMember(user.getUserId(), TeamMember.Role.NORMAL_STAFF,
                    TeamMember.Permission.READONLY);
            dataExt.addTeamMembers(Lists.newArrayList(teamMember));
        }
        if (StringUtils.equals(configValue, TeamMember.Permission.READANDWRITE.getValue())) {
            TeamMember teamMember = new TeamMember(user.getUserId(), TeamMember.Role.NORMAL_STAFF,
                    TeamMember.Permission.READANDWRITE);
            dataExt.addTeamMembers(Lists.newArrayList(teamMember));
        }
    }

    private String queryConfigValue(User user, String describeApiName) {
        String configCode = BizConfServiceImpl.getSupportTeamAddCreatorConfigCode(describeApiName);
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CREATOR_TO_TEAM_MEMBER_QUERY_CONFIG_GRAY, user.getTenantId())) {
            return serviceFacade.findTenantConfig(user, configCode);
        }
        List<ConfigPojo> configPojoList = infraServiceFacade.queryConfigData(user.getTenantId(),
                configCode, Lists.newArrayList(describeApiName));
        return CollectionUtils.nullToEmpty(configPojoList).stream()
                .filter(x -> Objects.equals(x.getKey(), configCode))
                .map(ConfigPojo::getConfigValue)
                .findFirst()
                .orElse(null);
    }


    private boolean isFirstModifyOwner(IObjectDescribe describe) {
        // 避免将负责人加入相关团队多次
        // 避免对sku和spu的影响，判断一下objectApiName
        if (isFirstModifyOwner || !Objects.equals(actionContext.getObjectApiName(), describe.getApiName())) {
            isFirstModifyOwner = false;
            return true;
        }
        return false;
    }

    protected void setDefaultSystemInfo(IObjectData objectData) {
        objectData.setTenantId(actionContext.getTenantId());
        if (!(isSpecifyCreatedBy() || designatedCreatedBy()) || Strings.isNullOrEmpty(objectData.getCreatedBy())) {
            objectData.setCreatedBy(actionContext.getUser().getUserIdOrOutUserIdIfOutUser());
        }
        objectData.setLastModifiedBy(actionContext.getUser().getUserIdOrOutUserIdIfOutUser());
        // 不指定时间（isSpecifyTime 为 false），或创建时间为为空时，指定创建时间为当前时间
        if (!isSpecifyTime() || Objects.isNull(objectData.getCreateTime())) {
            objectData.setCreateTime(System.currentTimeMillis());
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        //设置锁定状态为未锁定
        if (Strings.isNullOrEmpty(objectDataExt.getLockStatus())) {
            objectDataExt.setLockStatus(ObjectLockStatus.UNLOCK.getStatus());
        }

        // //处理外部联系人
        // if (!actionContext.getUser().isOutGuestUser()) {
        //     objectDataExt.setOutUser(actionContext.getUser(), true, ObjectDescribeExt.of(objectDescribe).isManualByOuterAllocateOwner(actionContext.getUser()));
        // }

        //数据初始状态是未生效，不需要触发审批流的话默认正常
        if (!objectDescribe.isBigObject()) {
            if (needTriggerApprovalFlow()) {
                objectDataExt.setLifeStatus(ObjectLifeStatus.INEFFECTIVE);
            } else {
                objectDataExt.setLifeStatus(ObjectLifeStatus.NORMAL);
            }
        }
        // 一期公共对象不支持主从对象
        if (objectDescribe.isPublicObject()) {
            // 1. 一期只能新建公有数据，不能创建私有数据
            // 2. 公有数据默认所有已升级公共对象的企业所有员工可见
            objectDataExt.initPublicDataFlagBeforeCreate(actionContext.getUser());
        }
    }

    /**
     * 如果当前对象为从对象
     * 同步主对象的负责人和外部负责人信息
     *
     * @param objectData 数据
     * @param describe   描述
     */
    protected void synchronizeDetailOwnerFromMasterObjectDataInDB(IObjectData objectData, IObjectDescribe describe) {

        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        describeExt.getMasterDetailFieldDescribe().ifPresent(masterDetailFieldDescribe -> {
            String masterDescribeApiName = masterDetailFieldDescribe.getTargetApiName();
            String masterId = objectData.get(masterDetailFieldDescribe.getApiName(), String.class);
            //值得注意的是,如果masterId==null也是正常的情况,因为此函数的进入场景可能是主从一起新建时的从对象,这时候就是没有masterId的。
            if (!Strings.isNullOrEmpty(masterId)) {
                IObjectDescribe masterDescribe = serviceFacade.findObject(objectData.getTenantId(), masterDescribeApiName);
                IObjectData masterObjectData = serviceFacade.findObjectData(objectData.getTenantId(), masterId, masterDescribe);
                //masterData有可能是空的,因为masterId是有可能为了验证规则,提前就系统生成的id,而不是真正数据库中的id。
                if (masterObjectData != null) {
                    ObjectDataExt masterDataExt = ObjectDataExt.of(masterObjectData);
                    ObjectDataExt detailObjectDataExt = ObjectDataExt.of(objectData);
                    Optional<String> ownerId = masterDataExt.getOwnerId();
                    ownerId.ifPresent(detailObjectDataExt::setOwnerId);

                    // 从主对象同步外部负责人信息
                    masterDataExt.getOutOwnerId().ifPresent(it -> {
                        detailObjectDataExt.setOutOwner(Lists.newArrayList(it));
                        detailObjectDataExt.setOutTenantId(masterDataExt.getOutTenantId());
                        // 同步合作伙伴
                        if (describeExt.isPRMEnabled()) {
                            detailObjectDataExt.setPartnerId(masterDataExt.getPartnerId());
                        }
                    });
                }
            }
        });
    }

    protected void setDefaultTeamMember(IObjectData objectData) {
        ObjectDataExt dataExt = ObjectDataExt.of(objectData);
        dataExt.setDefaultTeamMember(isFromChangeOrder());
        dataExt.setDefaultOutOwner2TeamMember();
    }

    protected void setDefaultForPayment(IObjectData objectData, IObjectDescribe describe) {
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(describe);
        Optional<PaymentFieldDescribe> paymentFieldDescribe = objectDescribeExt.getPaymentFieldDescribe();
        paymentFieldDescribe.ifPresent(x -> {
            objectData.set(x.getPayStatusFieldApiName(), Payment.PAY_STATUS_INCOMPLETE);
            if (Objects.isNull(objectData.get(x.getPayAmountFieldApiName()))) {
                objectData.set(x.getPayAmountFieldApiName(), 0);
            }
        });
    }

    protected void setDefaultForSignIn(IObjectData objectData, IObjectDescribe describe) {
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(describe);
        Optional<SignInFieldDescribe> signInFieldDescribe = objectDescribeExt.getSignInFieldDescribe();
        signInFieldDescribe.ifPresent(x -> {
            objectData.set(x.getSignInInfoListFieldApiName(), null);
            objectData.set(x.getIntervalFieldApiName(), null);
            objectData.set(x.getVisitStatusFieldApiName(), SignIn.SIGN_STATUS_INCOMPLETE);
            objectData.set(x.getSignInStatusFieldApiName(), SignIn.SIGN_STATUS_INCOMPLETE);
            objectData.set(x.getSignInTimeFieldApiName(), null);
            objectData.set(x.getSignOutStatusFieldApiName(), SignIn.SIGN_STATUS_INCOMPLETE);
            objectData.set(x.getSignOutTimeFieldApiName(), null);
            objectData.set(x.getSignInLocationFieldApiName(), null);
            objectData.set(x.getSignOutLocationFieldApiName(), null);
        });
    }

    protected void validateLookupData(IObjectData objectData, IObjectDescribe describe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        List<ObjectReferenceWrapper> fields = describeExt.getActiveReferenceFieldDescribes();
        doValidateLookupData(objectData, fields);
    }

    void doValidateLookupData(IObjectData objectData, List<ObjectReferenceWrapper> fields) {
        if (CollectionUtils.empty(fields)) {
            return;
        }

        for (ObjectReferenceWrapper fieldDescribe : fields) {
            //如果字段值没有编号
            serviceFacade.validateLookupData(actionContext.getUser(), objectData, fieldDescribe);
        }
    }

    /**
     * 给从对象的数据按照顺序依次升序设置order值
     * <p>
     * 如果采用间隔插入方式，需要考虑增扩order的情况，随着删除新增的数据的增长，order序号也会无限增加
     * 所以最好固定以一定范围内的数据赋值order
     *
     * @param detailObjectData
     */
    protected void setOrderForDetailData(Map<String, List<IObjectData>> detailObjectData) {
        ObjectDataExt.setOrderForDetailData(detailObjectData);
    }

    protected void logAsync(List<IObjectData> dataList, EventType eventType, ActionType actionType) {
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() ->
                        serviceFacade.masterDetailLog(actionContext.getUser(), eventType, actionType, objectDescribes,
                                dataList, null, null, getLogExtendsInfo(), ConvertRuleLogicService.toConvertSourceContainer(convertRuleDataContainer)))
                .run();

    }

    protected final Map<String, Object> getLogExtendsInfo() {
        Map<String, Object> map = Maps.newHashMap();
        map.put(LogInfo.TRIGGER_WORK_FLOW, needTriggerWorkFlow());
        map.put(LogInfo.TRIGGER_APPROVAL_FLOW, needTriggerApprovalFlow());
        map.put(LogInfo.SERIAL_ID, arg.getSeriesId());
        return map;
    }

    protected List<IObjectData> getRelateDataForCreateWorkFlow() {
        List<IObjectData> dataListForCreateWorkFlow = Lists.newArrayList();
        if (isApprovalNotExist() && needTriggerMasterApproval() && triggerType == ApprovalFlowTriggerType.CREATE) {
            IObjectData realMasterData = getRealMasterObjectData(objectData);
            List<IObjectDescribe> realDetailDescribes = serviceFacade.findDetailDescribesCreateWithMaster(actionContext.getTenantId(),
                    realMasterData.getDescribeApiName());
            realDetailDescribes.forEach(x -> {
                try {
                    List<IObjectData> realDetailDataList = serviceFacade.findDetailObjectDataWithPage(actionContext.getUser(),
                            realMasterData.getDescribeApiName(), realMasterData.getId(), x, 1, 1000, null).getData();
                    dataListForCreateWorkFlow.addAll(realDetailDataList);
                } catch (Exception e) {
                    log.error("getDataListForCreateWorkFlow error,tenantId:{},apiName:{}", actionContext.getTenantId(), x.getApiName(), e);
                }
            });
            dataListForCreateWorkFlow.add(realMasterData);
        }
        return dataListForCreateWorkFlow;
    }

    protected void doStartCreateWorkFlow(List<IObjectData> dataListForCreateWorkFlow) {
        dataListForCreateWorkFlow.forEach(data -> {
            try {
                infraServiceFacade.startWorkFlow(data.getId(), data.getDescribeApiName(), WorkflowProducer.TRIGGER_START,
                        actionContext.getUser(), Maps.newHashMap(), actionContext.getEventId());
            } catch (Exception e) {
                log.error("startCreateWorkFlow error,tenantId:{},describeApiName:{},dataId:{},eventId:{}", actionContext.getTenantId(),
                        data.getDescribeApiName(), data.getId(), actionContext.getEventId(), e);
            }
        });
    }

    protected boolean assigneesExceptional() {
        String objectApiName = objectData.getDescribeApiName();
        String dataId = objectData.getId();
        if (needTriggerMasterApproval()) {
            MasterDetailFieldDescribe masterDetail = ObjectDescribeExt.of(objectDescribe).getMasterDetailFieldDescribe()
                    .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR)));
            objectApiName = masterDetail.getTargetApiName();
            dataId = (String) objectData.get(masterDetail.getApiName());
        }
        if (Strings.isNullOrEmpty(objectApiName) || Strings.isNullOrEmpty(dataId)) {
            return false;
        }
        return serviceFacade.isAssigneesExceptional(actionContext.getUser(), objectApiName, Lists.newArrayList(dataId));
    }

    /**
     * 保存数据之前，将所有引用字段的值清除
     *
     * @param dataList
     * @param describe
     */
    private void handleQuoteValueBeforeCreate(List<IObjectData> dataList, IObjectDescribe describe) {
        List<Quote> quotes = ObjectDescribeExt.of(describe).getQuoteFieldDescribes();
        if (CollectionUtils.empty(quotes) || CollectionUtils.empty(dataList)) {
            return;
        }
        Set<String> quoteFieldApiName = quotes.stream().map(Quote::getApiName).collect(Collectors.toSet());
        dataList.stream().map(ObjectDataExt::of).forEach(dataExt -> dataExt.remove(quoteFieldApiName));
    }

    protected void handleQuoteValueBeforeCreate() {
        handleQuoteValueBeforeCreate(Lists.newArrayList(objectData), objectDescribe);

        // 处理从对象
        if (CollectionUtils.notEmpty(detailObjectData)) {
            detailObjectData.forEach((apiName, objectDataList) -> handleQuoteValueBeforeCreate(objectDataList, objectDescribes.get(apiName)));
        }

        // 处理相关对象
        if (CollectionUtils.notEmpty(relatedObjectData)) {
            processRelatedObjectData((apiName, objectDataList) -> handleQuoteValueBeforeCreate(objectDataList, relatedDescribes.get(apiName)));
        }
    }

    private boolean isSpecifyTime() {
        return BooleanUtils.isTrue((Boolean) actionContext.getAttribute(ActionContextExt.IS_SPECIFY_TIME));
    }

    private boolean isSpecifyCreatedBy() {
        return BooleanUtils.isTrue((Boolean) actionContext.getAttribute(ActionContextExt.IS_SPECIFY_CREATED_BY));
    }

    private boolean designatedCreatedBy() {
        return BooleanUtils.isTrue((Boolean) actionContext.getAttribute(ActionContextExt.DESIGNATED_CREATED_BY));
    }


    @Override
    protected final boolean skipRedirectAction() {
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_745)) {
            return true;
        }
        return !acceptRedirectAction();
    }

    @Override
    protected final IObjectData getRedirectObjectData() {
        return objectData;
    }

    @Override
    protected final Map<String, List<IObjectData>> getRedirectObjectDetails() {
        return detailObjectData;
    }

    @Override
    protected final void buildRedirectResult(Result result, ButtonExecutor.Result redirectFunctionResult) {
        if (redirectFunctionResult.isHasReturnValue()) {
            if ("one_flow".equals(redirectFunctionResult.getReturnType())) {
                Map<String, Object> oneFlowResult = Maps.newHashMap();
                oneFlowResult.put("action", redirectFunctionResult.getReturnType());
                oneFlowResult.put("one_flow_instance_id", redirectFunctionResult.getReturnValue());
                result.setUiActionResult(oneFlowResult);
            } else {
                result.setUiActionResult(redirectFunctionResult.getReturnValue());
            }
        }
    }

    @Override
    protected Map<String, Object> getArgs() {
        A arg = this.getArg();
        Map<String, Object> param = Maps.newHashMap();
        param.put(ExtraDataKeys.OPTION_INFO, arg.getOptionInfo());
        return param;
    }

    @Override
    protected final List<IFieldDescribe> getMaskEncryptFields(User user, IObjectDescribe describe) {
        Map<String, List<String>> maskFieldApiNames = arg.getMaskFieldApiNames();
        if (CollectionUtils.empty(maskFieldApiNames)) {
            return Lists.newArrayList();
        }
        return ObjectDescribeExt.of(describe).getFieldByApiNames(maskFieldApiNames.get(describe.getApiName()));
    }

    protected final Map<String, Object> getExtraCallbackData() {
        Map<String, Object> extraCallbackData = getArgs();
        if (CollectionUtils.notEmpty(customCallbackData)) {
            extraCallbackData.putAll(customCallbackData);
        }
        return extraCallbackData;
    }

    private void validateTypeForDataOwnDept() {
        infraServiceFacade.validateTypeForDataOwnDept(actionContext.getUser(), objectDescribe, objectData);
    }

    protected boolean isFromChangeOrder() {
        return BooleanUtils.isTrue(arg.getFromChangeOrder());
    }

    @Data
    public static class Arg {
        @JSONField(name = "M1")
        //兼容接口数据规格
        @JsonProperty("object_data")
        private ObjectDataDocument objectData;

        @JsonProperty("field_mapping")
        private List<FieldMapping> fieldMapping;

        @JSONField(name = "M2")
        private Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();
        @JsonProperty("related_data_list")
        @JSONField(name = "related_data_list")
        private Map<String, List<RelatedDataDocument>> relatedDataList;

        private StartApprovalFlow.OriginInfo originInfo;
        @JSONField(name = "M3")
        private OptionInfo optionInfo;

        @JsonProperty("duplicate_rule_api_name")
        @JSONField(name = "duplicate_rule_api_name")
        private String duplicateRuleApiName;

        //业务信息（用于在修改记录中记录业务方的id和附加id）
        private RequestContext.BizInfo bizInfo;

        //是否增量更新
        private Boolean incrementUpdate;

        private Boolean fillOutOwner;

        private Boolean needConvertLookup;

        //不触发审批流
        private Boolean skipApprovalFlow;

        //跳过数据状态(锁定状态、生命状态)的校验
        private Boolean skipDataStatusValidate;

        /**
         * 不校验查找关联数据
         */
        private Boolean skipValidateLookupData;

        //保存数据快照，数据变更不会直接入库
        private Boolean useSnapshot;

        //是否返回非阻断校验结果（包括验证规则、查重规则、前验证函数），前端通过其他rest接口中转的时候需要传true
        private Boolean acceptNonBlockingResult;
        // 需要执行 RedirectAction
        private Boolean acceptRedirectAction;

        //主对象的原始数据（进编辑页之前查到的数据，只需要传页面上有变更的字段）
        private ObjectDataDocument originalData;
        //从对象的原始数据（进编辑页之前查到的数据，没有变更的从对象不用传，其他从对象传参规则：
        //1、更新的只传变更的字段；
        //2、没有更新的只传_id；
        //）
        private Map<String, List<ObjectDataDocument>> originalDetails;

        //自由审批的相关信息
        private Map<String, Object> freeApprovalDef;

        /**
         * 需要掩码加密的字段
         */
        private Map<String, List<String>> maskFieldApiNames;

        //行业Code
        private String industryCode;
        /**
         * change_order 表示为变更单的编辑操作
         */
        @JSONField(name = "action_type")
        @JsonProperty("action_type")
        private String actionType;

        private Set<String> masterCalculateFields;
        private Map<String, Map<String, Set<String>>> detailCalculateFields;

        //页面id，对于同一个新建/编辑页调用的请求，都用同一个id
        private String seriesId;

        private Boolean fromChangeOrder;

        public boolean incrementUpdate() {
            return Boolean.TRUE.equals(incrementUpdate);
        }

        public String createFrom() {
            return Optional.ofNullable(optionInfo)
                    .map(OptionInfo::createFrom)
                    .orElse(null);
        }

        public Map<String, List<ObjectDataDocument>> getDetails() {
            if (Objects.isNull(details)) {
                details = Maps.newHashMap();
            }
            return details;
        }

        public String duplicateRuleApiName() {
            return Objects.isNull(getOptionInfo()) ? null : getOptionInfo().duplicateRuleApiName;
        }

        public boolean skipApprovalFlow() {
            return Boolean.TRUE.equals(skipApprovalFlow);
        }

        public boolean skipDataStatusValidate() {
            return Boolean.TRUE.equals(skipDataStatusValidate);
        }

        public boolean skipFuzzyRuleDuplicateSearch() {
            if (Objects.isNull(optionInfo)) {
                return false;
            }
            return Boolean.TRUE.equals(optionInfo.getSkipFuzzyRuleDuplicateSearch());
        }

        public boolean useSnapshot() {
            return Boolean.TRUE.equals(useSnapshot);
        }

        public boolean acceptNonBlockingResult() {
            return Boolean.TRUE.equals(acceptNonBlockingResult);
        }

        public boolean acceptRedirectAction() {
            return BooleanUtils.isTrue(acceptRedirectAction);
        }

        public boolean skipVersionCheck() {
            return Objects.nonNull(optionInfo) && Boolean.TRUE.equals(optionInfo.getSkipVersionCheck());
        }

        public boolean hasOriginalData() {
            return CollectionUtils.notEmpty(originalData) || CollectionUtils.notEmpty(originalDetails);
        }

        public boolean skipDuplicateSearchCheck() {
            if (Objects.isNull(optionInfo)) {
                return false;
            }
            return Boolean.FALSE.equals(optionInfo.getIsDuplicateSearch());
        }

        public boolean useParamsIdempotent() {
            return Objects.nonNull(optionInfo) && Optional.ofNullable(optionInfo.getUseParamsIdempotent()).orElse(Boolean.FALSE);
        }

        public boolean calculateDefaultValue() {
            return Objects.nonNull(optionInfo) && Boolean.TRUE.equals(optionInfo.getCalculateDefaultValue());
        }

        public boolean fromImport() {
            return Objects.nonNull(optionInfo) && Boolean.TRUE.equals(optionInfo.isFromImport());
        }

        public boolean supportValidationResult() {
            return Objects.nonNull(optionInfo) && Boolean.TRUE.equals(optionInfo.getSupportValidationResult());
        }

        public List<ValidatorInfo> skippedValidatorList() {
            return Objects.isNull(optionInfo) ? Lists.newArrayList() : optionInfo.getSkippedValidatorList();
        }

        public Boolean enableRealTimeCalculateDataAuth() {
            return Objects.isNull(optionInfo) ? null : optionInfo.getEnableRealTimeCalculateDataAuth();
        }

        public boolean enableUniqueCheckResult() {
            return Objects.nonNull(optionInfo) && BooleanUtils.isTrue(optionInfo.getEnableUniqueCheckResult());
        }

        public boolean ignoreSendingRemind() {
            return !Objects.isNull(optionInfo) && Boolean.TRUE.equals(optionInfo.getIgnoreSendingRemind());
        }

        public boolean realTimeCalculateDetailAuth() {
            return Optional.ofNullable(optionInfo)
                    .map(OptionInfo::getRealTimeCalculateDetailAuth)
                    .orElse(false);
        }

        public boolean isFillMultiLang() {
            return Optional.ofNullable(optionInfo)
                    .map(OptionInfo::getFillMultiLang)
                    .orElse(false);
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result implements Serializable {
        private static final long serialVersionUID = -7783570982879927167L;

        @JSONField(name = "M1")
        private ObjectDataDocument objectData;

        @JSONField(name = "M2")
        //兼容接口数据规格
        @JsonProperty("newObjectData")
        private Map<String, List<ObjectDataDocument>> details;
        private Map<String, List<RelatedDataDocument>> relatedDataList;
        @JSONField(name = "M3")
        private ValidationMessage validationRuleMessage;
        @JSONField(name = "M5")
        private Boolean isDuplicate;
        @JSONField(name = "M6")
        private ValidationMessage funcValidateMessage;

        //单独新建从触发主对象审批流的结果
        @JSONField(name = "M7")
        private MasterApprovalResult masterApprovalResult;

        //是否触发了审批流
        private Boolean triggerApproval;

        @JSONField(name = "M8")
        private ValidationMessage paramsIdempotentMessage;

        @JSONField(name = "M9")
        private Object uiActionResult;

        //数据是否入库
        private Boolean writeDB;

        //是否被版本号校验拦截
        private Boolean versionCheckBlocked;
        //主对象的冲突字段
        private DataConflicts dataConflicts;
        //从对象的冲突数据
        private Map<String, Map<String, DataConflicts>> detailConflicts;

        private String changeOrderApiName;
        private String changeOrderDataId;

        //各种校验器返回的校验结果（支持非阻断提示信息）
        private ValidationResultDocument validationResult;
        //数据是否发生变更(Edit接口使用)
        private Boolean dataChanged;

        private DuplicateDataVerificationResult duplicateDataVerificationResult;
    }

    @Data
    @Builder
    public static class DuplicateDataVerificationResult {
        public static final String UNIQUE_FIELD_TYPE = "unique_field";
        public static final String UNIQUE_RULE_TYPE = "unique_rule";
        private String describeApiName;
        private List<String> duplicateDataIds;
        private List<String> fieldNames;
        private String message;
        private String type;

        public static DuplicateDataVerificationResult fromFieldDuplicateMessage(String message, MetadataDataDuplicateBusinessException.DuplicateMessage duplicateMessage) {
            return DuplicateDataVerificationResult.builder()
                    .describeApiName(duplicateMessage.getDescribeApiName())
                    .message(message)
                    .duplicateDataIds(duplicateMessage.getDataIds())
                    .fieldNames(Lists.newArrayList(duplicateMessage.getDuplicateFieldName()))
                    .type(UNIQUE_FIELD_TYPE)
                    .build();
        }

        public static DuplicateDataVerificationResult fromUniqueRule(IUniqueRule uniqueRule,
                                                                     IObjectDescribe objectDescribe,
                                                                     UniqueRuleSearchResult.DuplicateData uniqueRuleDuplicateResult) {
            if (Objects.isNull(uniqueRule)) {
                return null;
            }
            List<String> dataIds = CollectionUtils.nullToEmpty(uniqueRuleDuplicateResult.getDataIds()).stream()
                    .limit(10)
                    .collect(Collectors.toList());
            UniqueRuleExt uniqueRuleExt = UniqueRuleExt.of(uniqueRule);
            List<String> ruleFieldNames = uniqueRuleExt.getRuleFieldName();
            String message = I18N.text(I18NKey.VALID_UNIQUENESS_MESSAGE_DB, uniqueRuleExt.joiningFieldLabel(" and ", objectDescribe));
            return DuplicateDataVerificationResult.builder()
                    .describeApiName(objectDescribe.getApiName())
                    .message(message)
                    .duplicateDataIds(dataIds)
                    .fieldNames(ruleFieldNames)
                    .type(UNIQUE_RULE_TYPE)
                    .build();
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DataConflicts implements Serializable {
        private static final long serialVersionUID = 899008819732428879L;
        //有冲突的字段apiName
        private List<String> fields;
        //数据库中的数据
        private ObjectDataDocument lastData;
        //页面提交的数据
        private ObjectDataDocument currentData;
    }

    @Data
    public static class OptionInfo {
        public static final String CREATE_FROM = "__create_from";
        public static final String FROM_DRAFT = "draft";
        public static final String FROM_CLONE = "clone";
        public static final String FROM_MAPPING = "mapping";
        public static final String FROM_TRANSFORM = "transform";
        public static final String FROM_IMPORT = "import";
        public static final String FROM_REFERENCE_CREATE = "reference_create";
        public static final String FROM_RELATED = "related";
        public static final String FROM_CHANGE = "change";

        private Boolean useValidationRule;
        private Boolean isDuplicateSearch;
        private String duplicateRuleApiName;
        private boolean skipFuncValidate;
        //是否跳过版本号校验
        private Boolean skipVersionCheck;

        private Boolean skipFuzzyRuleDuplicateSearch;

        private boolean fromDraft;
        private boolean fromClone;
        private boolean fromMapping;
        private boolean fromTransform;
        private boolean fromReferenceCreate;
        private boolean fromImport;
        private Boolean useParamsIdempotent;
        private Boolean needAllRedirectResult;
        private String fromId;
        private List<String> fromMasterIds;
        private List<DetailResource> fromDetails;
        private String fromApiName;
        private String convertRule;
        //是否计算默认值
        private Boolean calculateDefaultValue;
        //是否支持validationResult
        private Boolean supportValidationResult;
        //需要跳过的校验器
        private List<ValidatorInfo> skippedValidatorList;
        //是否实时计算数据权限
        private Boolean enableRealTimeCalculateDataAuth;
        //是否忽略发送 CRM 提醒
        private Boolean ignoreSendingRemind;
        //扩展信息,用于记录日志相关信息
        private Map<String, Object> extendsLogInfo;
        // 返回校验重复的数据
        private Boolean enableUniqueCheckResult;

        private Boolean realTimeCalculateDetailAuth;

        private Boolean fillMultiLang;


        public String createFrom() {
            if (fromDraft) {
                return FROM_DRAFT;
            }
            if (fromClone) {
                return FROM_CLONE;
            }
            if (fromMapping) {
                return FROM_MAPPING;
            }
            if (fromTransform) {
                return FROM_TRANSFORM;
            }
            if (fromReferenceCreate) {
                return FROM_REFERENCE_CREATE;
            }
            if (fromImport) {
                return FROM_IMPORT;
            }
            return null;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationMessage implements Serializable {
        private static final long serialVersionUID = 5224580537892434981L;

        private boolean isMatch;
        private List<String> blockMessages;
        private List<String> nonBlockMessages;
        private Boolean submitReturnData;

        private List<String> matchDataIndex;
        private String matchObjApi;

        public ValidationMessage setMessage(String message, boolean isBlock) {
            if (isBlock) {
                setBlockMessages(Lists.newArrayList(I18N.text(I18NKey.FRONT_VALIDATE_FUNCTION_FAILED, message)));
            } else {
                setNonBlockMessages(Lists.newArrayList(I18N.text(I18NKey.FRONT_VALIDATE_FUNCTION_FAILED, message)));
            }
            return this;
        }
    }

    @Data
    public static class RelatedDataDocument implements Serializable {
        private static final long serialVersionUID = -989700085211637873L;
        private String relatedFieldName;
        private List<ObjectDataDocument> dataList;

        public SaveMasterAndDetailData.RelatedObjectData toRelatedObjectData(Consumer<IObjectData> action) {
            List<IObjectData> objectDataList = getDataList().stream()
                    .map(ObjectDataDocument::toObjectData)
                    .peek(action)
                    .collect(Collectors.toList());
            return SaveMasterAndDetailData.RelatedObjectData.create(relatedFieldName, objectDataList);
        }

        public static RelatedDataDocument from(SaveMasterAndDetailData.RelatedObjectData relatedObjectData) {
            RelatedDataDocument relatedDataDocument = new RelatedDataDocument();
            relatedDataDocument.setRelatedFieldName(relatedObjectData.getRelatedFieldName());
            relatedDataDocument.setDataList(ObjectDataDocument.ofList(relatedObjectData.getDataList()));
            return relatedDataDocument;
        }

        public static Map<String, List<RelatedDataDocument>> fromMap(Map<String, List<SaveMasterAndDetailData.RelatedObjectData>> map) {
            if (Objects.isNull(map)) {
                return null;
            }
            Map<String, List<RelatedDataDocument>> result = Maps.newHashMap();
            if (CollectionUtils.empty(map)) {
                return result;
            }
            map.forEach((apiName, list) -> {
                List<RelatedDataDocument> relatedDataDocuments = list.stream().map(RelatedDataDocument::from).collect(Collectors.toList());
                result.put(apiName, relatedDataDocuments);
            });
            return result;
        }

        public SaveMasterAndDetailData.RelatedObjectData toRelatedObjectData() {
            return SaveMasterAndDetailData.RelatedObjectData.create(relatedFieldName, ObjectDataDocument.ofDataList(dataList));
        }

        public static Map<String, List<SaveMasterAndDetailData.RelatedObjectData>> toRelatedObjectDataMap(Map<String, List<RelatedDataDocument>> documentMap) {
            if (Objects.isNull(documentMap)) {
                return null;
            }
            Map<String, List<SaveMasterAndDetailData.RelatedObjectData>> result = Maps.newLinkedHashMap();
            documentMap.forEach((k, v) -> result.put(k, v.stream().map(RelatedDataDocument::toRelatedObjectData).collect(Collectors.toList())));
            return result;
        }
    }

}
