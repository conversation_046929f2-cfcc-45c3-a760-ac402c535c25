package com.facishare.paas.appframework.rest.service;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.dto.InternationalItem;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.rest.dto.data.Pay;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Payment;
import com.facishare.paas.metadata.impl.describe.PaymentFieldDescribe;
import com.facishare.paas.timezone.DateTimeFormat;
import com.facishare.paas.timezone.DateTimeFormatUtils;
import com.facishare.paas.timezone.TimeZoneContextHolder;
import com.facishare.paas.timezone.config.TimeZoneConfig;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.facishare.pay.toB.online.order.enums.PayChannelEnum.*;

@Slf4j
@Service
public class PayRestService {

    @Autowired
    ServiceFacade serviceFacade;

    @Autowired
    CRMNotificationService crmRestService;

    public Pay.Result payCallback(Pay.Arg arg, RequestContext context) {
        Pay.Result result = new Pay.Result();
        if (Objects.isNull(arg)) {
            throw new ValidateException("Parameter error");
        }

        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), arg.getApiName());

        ActionContext actionContext = new ActionContext();
        actionContext.setEnterpriseId(context.getTenantId());
        actionContext.setUserId(DefObjConstants.SUPER_PRIVILEGE_USER_ID);
        IObjectData data = serviceFacade.findObjectData(actionContext, arg.getBusiNo(), arg.getApiName());
        IObjectData dbData = ObjectDataExt.of(data).copy();

        Optional<PaymentFieldDescribe> optionalPaymentFieldDescribe = ObjectDescribeExt.of(describe).
                getPaymentFieldDescribe();
        optionalPaymentFieldDescribe.ifPresent(p -> {
            data.set(p.getPayStatusFieldApiName(), Payment.PAY_STATUS_COMPLETE);
            data.set(p.getPayTimeFieldApiName(), arg.getFinishTime());
            data.set(p.getPayTypeFieldApiName(), getPayType(arg.getPayType()));
            IObjectData update = serviceFacade.updateObjectData(context.getUser(), data, actionContext);
            if (!Objects.isNull(update)) {
                Map<String, Object> updateFields = ObjectDataExt.of(dbData).diff(data, describe);
                serviceFacade.log(getSystemUser(context.getTenantId()), EventType.MODIFY, ActionType.Modify, describe, data, updateFields, dbData);
                //发送crm提醒
                sendUdobjRemind(update, describe, p, context.getTenantId());
            }
        });
        result.setResult("success");
        return result;
    }

    private void sendUdobjRemind(IObjectData update, IObjectDescribe describe,
                                 PaymentFieldDescribe paymentFieldDescribe, String tenantId) {
        //发送crm提醒
//        CRMNotification notification = CRMNotification.builder()
//                .content(String.format(I18N.text(I18NKey.PAY_REMIND_CONTENT),
//                        describe.getDisplayName(), update.getName(),
//                        update.get(paymentFieldDescribe.getPayAmountFieldApiName()),
//                        getDateTimeText(update, paymentFieldDescribe, tenantId)))
//                .dataId(update.getId())
//                .sender(User.SUPPER_ADMIN_USER_ID)
//                .remindRecordType(CRMNotification.PAY_CALLBACK_REMIND_TYPE)
//                .title(I18N.text(I18NKey.RECEIPT_MESSAGE_TITLE))
//                .receiverIds(Sets.newHashSet(ObjectDataExt.of(update).getOwnerIdInt()))
//                .fixContent2ID(String.format("{\"apiname\":\"%s\"}", describe.getApiName()))
//                .build();
//        crmRestService.sendCRMNotification(user, notification);

        User user = getSystemUser(tenantId);
        NewCrmNotification newCrmNotification = NewCrmNotification.builder()
                .senderId(User.SUPPER_ADMIN_USER_ID)
                .type(NewCrmNotification.PAY_CALLBACK_REMIND_TYPE)
                .receiverIDs(Sets.newHashSet(ObjectDataExt.of(update).getOwnerIdInt()))
                .title("收款成功通知")// ignoreI18n
                .titleInfo(InternationalItem.builder()
                        .internationalKey(I18NKey.RECEIPT_MESSAGE_TITLE)
                        .build())
                .fullContent(String.format("对象名称：%s，对象主属性：%s，金额：%s，支付时间：%s",// ignoreI18n
                        describe.getDisplayName(),
                        update.getName(),
                        update.get(paymentFieldDescribe.getPayAmountFieldApiName()),
                        getDateTimeText(update, paymentFieldDescribe, tenantId)))
                .fullContentInfo(InternationalItem.builder()
                        .internationalKey(I18NKey.NEW_PAY_REMIND_CONTENT)
                        .internationalParameters(Lists.newArrayList(
                                describe.getDisplayName(),
                                update.getName(),
                                update.get(paymentFieldDescribe.getPayAmountFieldApiName(), String.class),
                                getDateTimeText(update, paymentFieldDescribe, tenantId)))
                        .build())
                .remindSender(true)
                .urlType(1)
                .objectApiName(describe.getApiName())
                .objectId(update.getId())
                .build()
                .addUrlParameter("objectApiName", describe.getApiName())
                .addUrlParameter("objectId", update.getId());

        crmRestService.sendNewCrmNotification(user, newCrmNotification);
    }

    private String getDateTimeText(IObjectData update, PaymentFieldDescribe paymentFieldDescribe, String tenantId) {
        if (TimeZoneConfig.INSTANCE.isGray(tenantId)) {
            return DateTimeFormatUtils.formatWithTimezoneInfo(update.get(paymentFieldDescribe.getPayTimeFieldApiName()),
                    TimeZoneContextHolder.getTenantTimeZone(), DateTimeFormat.DATE_TIME.getType());
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return simpleDateFormat.format(update.get(paymentFieldDescribe.getPayTimeFieldApiName()));
    }


    private User getSystemUser(String tenantId) {
        return new User(tenantId, User.SUPPER_ADMIN_USER_ID);
    }

    private String getPayType(String payType) {
        if (Strings.isNullOrEmpty(payType)) {
            return null;
        }

        switch (payType) {
            case "ALIPAY":
                return ALIPAY.getDescription();
            case "WEIXIN":
                return WEIXIN.getDescription();
            case "BANK":
                return BANK.getDescription();
            case "WALLET":
                return WALLET.getDescription();
            case "APPLEPAY":
                return APPLEPAY.getDescription();
            default:
                return null;
        }
    }

}
