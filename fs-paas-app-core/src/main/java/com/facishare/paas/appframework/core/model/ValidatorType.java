package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.core.model.domain.ActionDomainPlugin;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * Created by zhouwr on 2024/1/5.
 */
@Getter
public enum ValidatorType {
    DOMAIN_PLUGIN_BEFORE("DomainPluginBefore"), DOMAIN_PLUGIN_PREACT("DomainPluginPreact"), HANDLER("Handler"), BUTTON_VALIDATE("buttonValidate");

    private final String code;

    ValidatorType(String code) {
        this.code = code;
    }

    public static ValidatorType of(String code) {
        return Arrays.stream(values()).filter(x -> Objects.equals(code, x.getCode())).findFirst().orElse(null);
    }

    public static ValidatorType getDomainPluginValidatorTypeByMethod(String method) {
        if (ActionDomainPlugin.BEFORE.equals(method)) {
            return DOMAIN_PLUGIN_BEFORE;
        }
        if (ActionDomainPlugin.PRE_ACT.equals(method)) {
            return DOMAIN_PLUGIN_PREACT;
        }
        return null;
    }
}
