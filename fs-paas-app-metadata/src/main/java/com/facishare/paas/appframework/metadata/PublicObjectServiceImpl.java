package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.service.IPublicObjectDataService;
import com.facishare.paas.metadata.api.service.IPublicObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class PublicObjectServiceImpl implements PublicObjectService {
    @Autowired
    private IPublicObjectDataService publicObjectDataService;
    @Autowired
    private IPublicObjectDescribeService publicObjectDescribeService;

    @Override
    public void convertPublicData(User user, Map<String, List<String>> dataMap) {
        if(Objects.isNull(user) || CollectionUtils.empty(dataMap)) {
            return;
        }

        IActionContext context = ActionContextExt.of(user).getContext();
        try {
            publicObjectDataService.convertPublicObjectData(context, dataMap);
        } catch (MetadataServiceException e) {
            log.warn("convertPublicData fail, user:{}, dataMap:{}", user, dataMap, e);
            throw new ValidateException(e.getMessage());
        }
    }

    @Override
    public void convertPrivateData(User user, Map<String, List<String>> dataMap) {
        if(Objects.isNull(user) || CollectionUtils.empty(dataMap)) {
            return;
        }

        IActionContext context = ActionContextExt.of(user).getContext();
        try {
            publicObjectDataService.convertPrivateObjectData(context, dataMap);
        } catch (MetadataServiceException e) {
            log.warn("convertPrivateData fail, user:{}, dataMap:{}", user, dataMap, e);
            throw new ValidateException(e.getMessage());
        }
    }

    @Override
    public void convertPublicDescribe(User user, List<String> apiNameList) {
        if(Objects.isNull(user) || CollectionUtils.empty(apiNameList)) {
            return;
        }
        try {
            publicObjectDescribeService.convertPublicObject(user.getTenantId(), apiNameList);
        } catch (MetadataServiceException e) {
            log.warn("convertPublicDescribe fail, user:{}, apiNameList:{}", user, apiNameList, e);
            throw new ValidateException(e.getMessage());
        }

    }

    @Override
    public void enablePublicDescribe(User user, String upstreamTenantId, List<String> apiNameList) {
        if(Objects.isNull(user) || CollectionUtils.empty(apiNameList) || StringUtils.isBlank(upstreamTenantId)) {
            return;
        }

        try {
            publicObjectDescribeService.enablePublicObject(user.getTenantId(), upstreamTenantId, apiNameList);
        } catch (MetadataServiceException e) {
            log.warn("enablePublicDescribe fail, user:{}, upstreamTenantId:{}, apiNameList:{}",
                    user,upstreamTenantId, apiNameList, e);
            throw new ValidateException(e.getMessage());
        }
    }

    @Override
    public void disablePublicDescribe(User user, List<String> apiNameList) {
        if(Objects.isNull(user) || CollectionUtils.empty(apiNameList)) {
            return;
        }
        try {
            publicObjectDescribeService.disablePublicObject(user.getTenantId(), apiNameList);
        } catch (MetadataServiceException e) {
            log.warn("disablePublicDescribe fail, user:{}, apiNameList:{}",
                    user, apiNameList, e);
            throw new ValidateException(e.getMessage());
        }
    }

    @Override
    public List<String> findPublicDescribeApiNames(User user, List<String> apiNameList) {
        if(Objects.isNull(user) || CollectionUtils.empty(apiNameList)) {
            return Lists.newArrayList();
        }
        try {
            return publicObjectDescribeService.findPublicObject(user.getTenantId(), apiNameList);
        } catch (MetadataServiceException e) {
            log.warn("findPublicDescribeApiNames fail, user:{}, apiNameList:{}",
                    user, apiNameList, e);
            throw new ValidateException(e.getMessage());
        }
    }

    @Override
    public Map<String, List<String>> findPublicObjectDataByIds(User user, Map<String, List<String>> dataMap) {
        if(Objects.isNull(user) || CollectionUtils.empty(dataMap)) {
            return Maps.newHashMap();
        }
        IActionContext context = ActionContextExt.of(user).getContext();
        try {
            return publicObjectDataService.findPublicObjectDataByIds(context, dataMap);
        } catch (MetadataServiceException e) {
            log.warn("findPublicObjectDataByIds fail, user:{}, dataMap:{}", user, dataMap, e);
            throw new ValidateException(e.getMessage());
        }
    }
}
