package com.facishare.paas.appframework.metadata.cache.local

import com.facishare.paas.metadata.api.service.IUdefButtonService
import org.springframework.cache.CacheManager
import org.springframework.cache.interceptor.KeyGenerator
import spock.lang.Specification

/**
 * Created by zhaooju on 2022/5/20
 */
class CacheProxyFactoryTest extends Specification {

    def "test validateArgs noExceptionThrown"() {
        given:
        def cacheManager = Stub(CacheManager)
        def keyGenerator = Stub(KeyGenerator)
        CacheProxyFactory cacheProxyFactory = new CacheProxyFactory(cacheManager, keyGenerator)
        when:
        cacheProxyFactory.validateArgs(clazz, instance)
        then:
        noExceptionThrown()
        where:
        clazz              | instance
        IUdefButtonService | Stub(IUdefButtonService)
    }

    def "test validateArgs "() {
        given:
        def cacheManager = Stub(CacheManager)
        def keyGenerator = Stub(KeyGenerator)
        CacheProxyFactory cacheProxyFactory = new CacheProxyFactory(cacheManager, keyGenerator)
        when:
        cacheProxyFactory.validateArgs(clazz, instance)
        then:
        thrown(IllegalArgumentException)
        where:
        clazz              | instance
        IUdefButtonService | new Object()
        Object             | new Object()
    }
}
