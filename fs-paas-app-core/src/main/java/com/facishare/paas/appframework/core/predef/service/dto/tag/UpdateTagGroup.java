package com.facishare.paas.appframework.core.predef.service.dto.tag;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.metadata.dto.scene.SceneDTO;
import com.facishare.paas.metadata.api.describe.ITagDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/19 1:51 下午
 */
public interface UpdateTagGroup {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {

        @JsonProperty("id")
        @JSONField(name = "id")
        String tagGroupId;

        @JsonProperty("api_name")
        @JSONField(name = "api_name")
        String apiName;

        @JsonProperty("describe_api_names")
        @JSONField(name = "describe_api_names")
        List<String> describeApiNames;

        @JsonProperty("is_applied_to_all")
        @JSONField(name = "is_applied_to_all")
        Boolean isAppliedToAll = false;

        @JsonProperty("tag_group_name")
        @JSONField(name = "tag_group_name")
        String tagGroupName;

        @JsonProperty("tag_define_type")
        @JSONField(name = "tag_define_type")
        String tagDefineType;

        @JsonProperty("group_description")
        @JSONField(name = "group_description")
        String groupDescription;

        @JsonProperty("is_mutex")
        @JSONField(name = "is_mutex")
        Boolean isMutex = false;

        @JsonProperty("ranges")
        @JSONField(name = "ranges")
        SceneDTO.Range ranges = new SceneDTO.Range();

    }

    @Builder
    @Data
    class Result {
        ITagDescribe tagDescribe;
    }
}
