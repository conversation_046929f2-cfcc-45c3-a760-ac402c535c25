package com.facishare.paas.appframework.common.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * Created by liyiguang on 2017/8/24.
 */
public abstract class CollectionUtils {


    public static boolean notEmpty(Collection<?> collection) {
        return collection != null && !collection.isEmpty();
    }

    public static boolean empty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }

    public static boolean empty(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }

    public static boolean notEmpty(Map<?, ?> map) {
        return map != null && !map.isEmpty();
    }

    public static <T> List<T> nullToEmpty(List<T> list) {
        return Objects.isNull(list) ? Lists.newArrayList() : list;
    }

    public static <T> Set<T> nullToEmpty(Set<T> set) {
        return Objects.isNull(set) ? Sets.newHashSet() : set;
    }

    public static <K, V> Map<K, V> nullToEmpty(Map<K, V> map) {
        return Objects.isNull(map) ? Maps.newHashMap() : map;
    }

    public static boolean isEqual(Collection<?> a, Collection<?> b) {
        if (empty(a) && empty(b)) {
            return true;
        }
        if (empty(a) || empty(b)) {
            return false;
        }
        return org.apache.commons.collections4.CollectionUtils.isEqualCollection(a, b);
    }

    // 按照 orderedList 稳定排序 chaosList, 若未在 orderedList 放到最后
    public static <T, K> List<T> sortByGivenOrder(List<T> chaosList, List<K> orderedList, Function<T, K> funcGetKey) {
        if (CollectionUtils.empty(chaosList) || CollectionUtils.empty(orderedList)) {
            return chaosList;
        }
        AtomicInteger integer = new AtomicInteger(0);
        Map<K, Integer> indexMap = orderedList.stream().collect(Collectors.toMap(it -> it, it -> integer.getAndIncrement(), (x, y) -> x));
//        Map<K, Integer> collect = IntStream.range(0, orderedList.size() - 1)
//                .collect(Maps::newHashMap, (result, index) -> result.put(orderedList.get(index), index), (ll, rr) -> { });
        return chaosList.stream()
                .sorted(Comparator.comparingInt(it -> indexMap.getOrDefault(funcGetKey.apply(it), orderedList.size())))
                .collect(Collectors.toList());
    }

    public static <T> List<T> addIfAbsent(List<T> list1, List<T> list2, BiPredicate<? super T, ? super T> equals) {
        List<T> result = Lists.newArrayList(list1);
        if (CollectionUtils.empty(list2)) {
            return result;
        }
        list2.stream()
                .filter(it -> list1.stream().noneMatch(x -> equals.test(x, it)))
                .forEach(result::add);
        return result;
    }

    public static int size(Collection<?> collection) {
        return empty(collection) ? 0 : collection.size();
    }

    public static int size(Map<?, ?> map) {
        return empty(map) ? 0 : map.size();
    }

    public static boolean isSingleton(Collection<?> collection) {
        return size(collection) == 1;
    }

    public static Collection<?> firstNotEmpty(final Collection<?>... values) {
        if (values != null) {
            for (final Collection<?> val : values) {
                if (notEmpty(val)) {
                    return val;
                }
            }
        }
        return null;
    }

    @SafeVarargs
    public static <T> List<T> concatAndDistinct(List<T>... values) {
        return Arrays
                .stream(values)
                .filter(CollectionUtils::notEmpty)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 合并后，不包含空的项
     * @param values
     * @return
     * @param <T>
     */
    @SafeVarargs
    public static <T> List<T> concatAndNotContainEmpty(List<T>... values) {
        return Arrays
                .stream(values)
                .filter(CollectionUtils::notEmpty)
                .flatMap(List::stream)
                .filter(ObjectUtils::isNotEmpty)
                .collect(Collectors.toList());
    }
}
