package com.facishare.paas.appframework.rest.controller;

import com.facishare.crm.common.exception.CRMErrorCode;
import com.facishare.crm.common.exception.CrmCheckedException;
import com.facishare.crm.privilege.service.CommonGroupService;
import com.facishare.crm.privilege.swagger.ApiException;
import com.facishare.crm.privilege.util.ValidateUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.jaxrs.annotation.RestAPI;
import com.facishare.paas.appframework.metadata.exception.PrivilegeBusinessException;
import com.facishare.paas.appframework.rest.dto.data.CreateGroup;
import com.facishare.paas.appframework.rest.dto.data.UpdateGroupMembers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.POST;
import javax.ws.rs.Path;

/**
 * create by zhaoju on 2020/02/28
 */
@Controller
@Slf4j
@Path("/v1/inner/rest/user_group")
@RestAPI
public class UserGroupController {
    @Autowired
    private CommonGroupService commonGroupService;

    @POST
    public CreateGroup.Result createGroup(CreateGroup.Arg arg) {
        RequestContext context = RequestContextManager.getContext();
        if (!ValidateUtil.isValidParam(context.getTenantId(), arg.getGroupName())) {
            throw new ValidateException(CRMErrorCode.PARAMETER_IS_WRONG.getMessage(), CRMErrorCode.PARAMETER_IS_WRONG.getCode());
        }

        try {
            Object groupId = commonGroupService.createGroup(context.getTenantId(), arg.getGroupName(), arg.getDescription());
            return CreateGroup.Result.builder().groupId(groupId).build();
        } catch (ApiException e) {
            throw new PrivilegeBusinessException(e.getMessage(), CRMErrorCode.PAAS_ERROR.getCode());
        } catch (CrmCheckedException e) {
            throw new PrivilegeBusinessException(e.getMessage(), e.getIntErrorCode());
        }
    }

    @POST
    @Path("/add_users")
    public UpdateGroupMembers.Result updateGroupMembers(UpdateGroupMembers.Arg arg) {
        RequestContext context = RequestContextManager.getContext();
        if (!ValidateUtil.isValidParam(arg.getUserIds(), context.getTenantId(), arg.getGroupId())) {
            throw new ValidateException(CRMErrorCode.PARAMETER_IS_WRONG.getMessage(), CRMErrorCode.PARAMETER_IS_WRONG.getCode());
        }

        try {
            commonGroupService.updateGroupMembers(context.getTenantId(), arg.getGroupId(), arg.getUserIds());
            return new UpdateGroupMembers.Result();
        } catch (ApiException e) {
            throw new PrivilegeBusinessException(e.getMessage(), CRMErrorCode.PAAS_ERROR.getCode());
        } catch (CrmCheckedException e) {
            throw new PrivilegeBusinessException(e.getMessage(), e.getIntErrorCode());
        }

    }
}
