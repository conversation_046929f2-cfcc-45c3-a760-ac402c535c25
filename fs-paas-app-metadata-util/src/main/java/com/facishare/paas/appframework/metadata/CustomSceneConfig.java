package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * create by zhao<PERSON> on 2019/08/14
 * 默认保存的都是场景id
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomSceneConfig {
    private static final String CUSTOM_SCENE_CONFIG_KEY = "customScene.config";

    private String defaultScene;
    private List<String> orderBy;
    private List<String> hiddenScene;

    public static CustomSceneConfig fromJson(String json) {
        return JSON.parseObject(json, CustomSceneConfig.class);
    }

    public String toJsonString() {
        return JSON.toJSONString(this);
    }

    public static String getCustomSceneConfigKey(String describeApiName, String extendAttribute) {
        if (Strings.isNullOrEmpty(describeApiName)) {
            throw new ValidateException(I18NExt.text(I18NKey.OBJECT_API_NAME_CANNOT_BE_EMPTY));
        }
        return Stream.of(CUSTOM_SCENE_CONFIG_KEY, describeApiName, extendAttribute)
                .filter(x -> !Strings.isNullOrEmpty(x)).collect(Collectors.joining("."));
    }

    public static Set<String> getCustomSceneConfigKeys(String describeApiName, Collection<String> extendAttributes) {
        if (CollectionUtils.empty(extendAttributes)) {
            return Sets.newHashSet();
        }
        return extendAttributes.stream()
                .map(extendAttribute -> getCustomSceneConfigKey(describeApiName, extendAttribute))
                .collect(Collectors.toSet());
    }

    public static CustomSceneConfig buildByDefault(String defaultScene) {
        return CustomSceneConfig.builder().defaultScene(defaultScene).build();
    }
}
