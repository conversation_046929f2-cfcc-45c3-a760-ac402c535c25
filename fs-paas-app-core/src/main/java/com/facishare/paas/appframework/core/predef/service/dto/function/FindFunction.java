package com.facishare.paas.appframework.core.predef.service.dto.function;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.NoArgsConstructor;

/**
 * Created by liyiguang on 2018/2/4.
 */
public interface FindFunction {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        @JSONField(name = "api_name")
        @SerializedName(value = "api_name")
        @JsonProperty(value = "api_name")
        String apiName;

        @JSONField(name = "binding_object_api_name")
        @SerializedName(value = "binding_object_api_name")
        @JsonProperty(value = "binding_object_api_name")
        String bindingObjectAPIName;
    }

    @Data
    @Builder
    class Result {
        FunctionInfo function;
    }
}

