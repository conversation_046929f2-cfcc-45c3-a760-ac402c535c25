package com.facishare.paas.appframework.core.model;

/**
 * Created by zhouwr on 2017/10/11
 */
public class ContextManager {

    public static ControllerContext buildControllerContext(String apiName, String methodName) {
        RequestContext requestContext = RequestContextManager.getContext();
        ControllerContext controllerContext = new ControllerContext(requestContext, apiName, methodName, null);

        return controllerContext;
    }

    public static ServiceContext buildServiceContext(String serviceName, String serviceMethod) {
        RequestContext requestContext = RequestContextManager.getContext();
        ServiceContext serviceContext = new ServiceContext(requestContext, serviceName, serviceMethod);

        return serviceContext;
    }

    public static ActionContext buildActionContext(String apiName, String action) {
        RequestContext requestContext = RequestContextManager.getContext();
        ActionContext actionContext = new ActionContext(requestContext, apiName, action);

        return actionContext;
    }

}
