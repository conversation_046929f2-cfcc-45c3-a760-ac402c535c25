package com.facishare.paas.appframework.common.graph

import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.ConcurrentHashMap

/**
 * GenerateByAI
 * 测试内容描述：ElementOrder元素排序类的单元测试
 */
class ElementOrderTest extends Specification {

    def "test unordered element order"() {
        when: "创建无序元素顺序"
        def order = ElementOrder.unordered()

        then: "应该是无序类型"
        order.type() == ElementOrder.Type.UNORDERED
        order.comparator() == null
    }

    def "test insertion element order"() {
        when: "创建插入顺序元素顺序"
        def order = ElementOrder.insertion()

        then: "应该是插入顺序类型"
        order.type() == ElementOrder.Type.INSERTION
        order.comparator() == null
    }

    def "test natural element order"() {
        when: "创建自然顺序元素顺序"
        def order = ElementOrder.<String>natural()

        then: "应该是排序类型且有比较器"
        order.type() == ElementOrder.Type.SORTED
        order.comparator() != null
    }

    def "test sorted element order with custom comparator"() {
        given: "创建自定义比较器"
        def customComparator = { a, b -> b.compareTo(a) } as Comparator<String>

        when: "创建排序元素顺序"
        def order = ElementOrder.sorted(customComparator)

        then: "应该是排序类型且使用自定义比较器"
        order.type() == ElementOrder.Type.SORTED
        order.comparator() == customComparator
    }

    @Unroll
    def "test equals method: #order1 equals #order2 = #expected"() {
        expect: "验证equals方法"
        (order1.equals(order2)) == expected

        where:
        order1                              | order2                              | expected
        ElementOrder.unordered()            | ElementOrder.unordered()            | true
        ElementOrder.insertion()            | ElementOrder.insertion()            | true
        ElementOrder.natural()              | ElementOrder.natural()              | true
        ElementOrder.unordered()            | ElementOrder.insertion()            | false
        ElementOrder.unordered()            | ElementOrder.natural()              | false
        ElementOrder.insertion()            | ElementOrder.natural()              | false
        ElementOrder.unordered()            | "not an element order"              | false
        ElementOrder.unordered()            | null                                | false
    }

    def "test hashCode consistency"() {
        given: "创建相同的元素顺序"
        def order1 = ElementOrder.unordered()
        def order2 = ElementOrder.unordered()
        def insertion1 = ElementOrder.insertion()
        def insertion2 = ElementOrder.insertion()

        expect: "相等的对象应该有相同的hashCode"
        order1.hashCode() == order2.hashCode()
        insertion1.hashCode() == insertion2.hashCode()
    }

    def "test toString format"() {
        given: "创建不同类型的元素顺序"
        def unordered = ElementOrder.unordered()
        def insertion = ElementOrder.insertion()
        def natural = ElementOrder.<String>natural()
        def customComparator = { a, b -> a.compareTo(b) } as Comparator<String>
        def sorted = ElementOrder.sorted(customComparator)

        expect: "验证toString格式"
        unordered.toString().contains("UNORDERED")
        insertion.toString().contains("INSERTION")
        natural.toString().contains("SORTED")
        natural.toString().contains("comparator")
        sorted.toString().contains("SORTED")
        sorted.toString().contains("comparator")
    }

    def "test createMap with unordered type"() {
        given: "创建无序元素顺序"
        def order = ElementOrder.<String>unordered()

        when: "创建映射"
        def map = order.createMap(10)

        then: "应该创建HashMap"
        map instanceof HashMap
        map.isEmpty()
    }

    def "test createMap with insertion type"() {
        given: "创建插入顺序元素顺序"
        def order = ElementOrder.<String>insertion()

        when: "创建映射"
        def map = order.createMap(10)

        then: "应该创建LinkedHashMap"
        map instanceof LinkedHashMap
        map.isEmpty()
    }

    def "test createMap with sorted type"() {
        given: "创建排序元素顺序"
        def order = ElementOrder.<String>natural()

        when: "创建映射"
        def map = order.createMap(10)

        then: "应该创建TreeMap"
        map instanceof TreeMap
        map.isEmpty()
    }

    def "test createMap with custom sorted comparator"() {
        given: "创建自定义排序元素顺序"
        def reverseComparator = { a, b -> b.compareTo(a) } as Comparator<String>
        def order = ElementOrder.sorted(reverseComparator)

        when: "创建映射并添加元素"
        def map = order.createMap(10)
        map.put("c", 3)
        map.put("a", 1)
        map.put("b", 2)

        then: "应该按自定义顺序排序"
        map instanceof TreeMap
        map.keySet() as List == ["c", "b", "a"]
    }

    def "test cast method"() {
        given: "创建元素顺序"
        def order = ElementOrder.<Object>unordered()

        when: "转换类型"
        def castedOrder = order.<String>cast()

        then: "应该返回相同的实例但类型不同"
        castedOrder.is(order)
        castedOrder.type() == ElementOrder.Type.UNORDERED
    }

    def "test type enum values"() {
        expect: "验证Type枚举值"
        ElementOrder.Type.values().length == 3
        ElementOrder.Type.UNORDERED != null
        ElementOrder.Type.INSERTION != null
        ElementOrder.Type.SORTED != null
    }

    def "test comparator method with sorted order"() {
        given: "创建排序元素顺序"
        def customComparator = { a, b -> a.length() - b.length() } as Comparator<String>
        def order = ElementOrder.sorted(customComparator)

        when: "获取比较器"
        def retrievedComparator = order.comparator()

        then: "应该返回相同的比较器"
        retrievedComparator == customComparator
    }

    def "test comparator method with non-sorted order throws exception"() {
        given: "创建非排序元素顺序"
        def order = ElementOrder.unordered()

        when: "尝试获取比较器"
        order.comparator()

        then: "应该抛出IllegalStateException"
        thrown(IllegalStateException)
    }

    def "test natural order with comparable elements"() {
        given: "创建自然顺序"
        def order = ElementOrder.<Integer>natural()

        when: "创建映射并添加元素"
        def map = order.createMap(5)
        map.put(3, "three")
        map.put(1, "one")
        map.put(2, "two")

        then: "应该按自然顺序排序"
        map.keySet() as List == [1, 2, 3]
    }

    def "test equals with custom comparators"() {
        given: "创建相同的自定义比较器"
        def comparator1 = { a, b -> a.compareTo(b) } as Comparator<String>
        def comparator2 = { a, b -> a.compareTo(b) } as Comparator<String>
        def order1 = ElementOrder.sorted(comparator1)
        def order2 = ElementOrder.sorted(comparator2)

        expect: "不同实例的比较器应该不相等"
        !order1.equals(order2)
    }

    def "test equals with same comparator instance"() {
        given: "创建使用相同比较器实例的元素顺序"
        def comparator = { a, b -> a.compareTo(b) } as Comparator<String>
        def order1 = ElementOrder.sorted(comparator)
        def order2 = ElementOrder.sorted(comparator)

        expect: "使用相同比较器实例应该相等"
        order1.equals(order2)
    }

    def "test immutability"() {
        given: "创建元素顺序"
        def order = ElementOrder.unordered()

        expect: "元素顺序应该是不可变的"
        !order.class.methods.any { it.name.startsWith("set") }
    }

    def "test thread safety of static factory methods"() {
        given: "多线程环境"
        def results = new ConcurrentHashMap<String, ElementOrder>()

        when: "并发调用静态工厂方法"
        def threads = (1..10).collect { i ->
            Thread.start {
                results.put("unordered_$i", ElementOrder.unordered())
                results.put("insertion_$i", ElementOrder.insertion())
                results.put("natural_$i", ElementOrder.<String>natural())
            }
        }
        threads.each { it.join() }

        then: "所有结果应该正确"
        results.size() == 30
        results.values().count { it.type() == ElementOrder.Type.UNORDERED } == 10
        results.values().count { it.type() == ElementOrder.Type.INSERTION } == 10
        results.values().count { it.type() == ElementOrder.Type.SORTED } == 10
    }

    def "test edge cases with null comparator"() {
        when: "使用null比较器创建排序顺序"
        ElementOrder.sorted(null)

        then: "应该抛出NullPointerException"
        thrown(NullPointerException)
    }

    def "test createMap with different expected sizes"() {
        given: "创建元素顺序"
        def order = ElementOrder.<String>unordered()

        expect: "不同的期望大小都应该工作"
        order.createMap(0) instanceof HashMap
        order.createMap(1) instanceof HashMap
        order.createMap(100) instanceof HashMap
        order.createMap(1000) instanceof HashMap
    }

    def "test type safety with generics"() {
        given: "创建不同类型的元素顺序"
        def stringOrder = ElementOrder.<String>natural()
        def integerOrder = ElementOrder.<Integer>natural()

        when: "创建映射"
        def stringMap = stringOrder.createMap(5)
        def integerMap = integerOrder.createMap(5)

        then: "类型应该正确"
        stringMap instanceof Map<String, ?>
        integerMap instanceof Map<Integer, ?>
    }
}
