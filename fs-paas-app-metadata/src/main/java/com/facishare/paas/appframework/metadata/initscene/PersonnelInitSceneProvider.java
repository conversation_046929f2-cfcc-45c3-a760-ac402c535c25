package com.facishare.paas.appframework.metadata.initscene;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.SearchTemplateExt;
import com.facishare.paas.appframework.metadata.util.SearchTemplateUtil;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.service.ISearchTemplateService;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Component
public class PersonnelInitSceneProvider implements SceneInitProvider {
    private final HashMap<String, List<String>> labelMapping = Maps.newHashMap();
    @Autowired
    private ISearchTemplateService searchTemplateService;

    {
        labelMapping.put("all_employee", Lists.newArrayList("name",
                "phone",
                "user_name",
                "main_department",
                "vice_departments",
                "position",
                "leader",
                "date_of_joining", "is_pause_login", "is_active"));
        labelMapping.put("not_active", Lists.newArrayList("name",
                "phone",
                "user_name",
                "created_by",
                "main_department",
                "vice_departments",
                "position",
                "leader", "is_pause_login", "is_active"));
        labelMapping.put("pause_login", Lists.newArrayList("name",
                "last_modified_by",
                "last_modified_time",
                "phone",
                "user_name",
                "main_department",
                "vice_departments",
                "position",
                "leader", "is_pause_login", "is_active"));
        labelMapping.put("leader_null", Lists.newArrayList("name",
                "phone",
                "user_name",
                "main_department",
                "vice_departments",
                "position",
                "last_modified_by",
                "last_modified_time", "is_pause_login", "is_active"));
        labelMapping.put("in_this_level_department", Lists.newArrayList("name",
                "phone",
                "user_name",
                "main_department",
                "vice_departments",
                "position",
                "leader", "is_pause_login", "is_active"));
        labelMapping.put("in_main_department", Lists.newArrayList("name",
                "phone",
                "user_name",
                "main_department",
                "vice_departments",
                "position",
                "leader", "is_pause_login", "is_active"));
        labelMapping.put("stop_employee", Lists.newArrayList("name",
                "stop_time",
                "phone",
                "user_name",
                "main_department",
                "vice_departments",
                "position",
                "leader", "is_pause_login", "is_active"));
        labelMapping.put("main_department_null", Lists.newArrayList("name",
                "phone",
                "user_name",
                "main_department",
                "vice_departments",
                "position",
                "leader", "is_pause_login", "is_active"));
    }

    @Override
    public String getApiName() {
        return ObjectDescribeExt.PERSONNEL_OBJ_API_NAME;
    }

    @Override
    public List<ISearchTemplate> getDefaultSearchTemplateList(User user, String apiName, String extendAttribute) {
        Map<String, String> filterMaps = Maps.newLinkedHashMap();
        //在职员工
        filterMaps.put("all_employee", "[{\"field_name\":\"status\",\"field_values\":[\"0\"],\"operator\":\"EQ\",\"connector\":\"AND\",\"fieldNum\":0,\"isObjectReference\":false,\"isIndex\":false}]");
        //未激活的
        filterMaps.put("not_active", "[{\"field_name\":\"is_active\",\"field_values\":[false],\"operator\":\"EQ\",\"connector\":\"AND\",\"fieldNum\":0,\"isObjectReference\":false,\"isIndex\":false},{\"field_name\":\"status\",\"field_values\":[\"0\"],\"operator\":\"EQ\",\"connector\":\"AND\",\"fieldNum\":0,\"isObjectReference\":false,\"isIndex\":false}]");
        //禁止登录
        filterMaps.put("pause_login", "[{\"field_name\":\"is_pause_login\",\"field_values\":[true],\"operator\":\"EQ\",\"connector\":\"AND\",\"fieldNum\":0,\"isObjectReference\":false,\"isIndex\":false},{\"field_name\":\"status\",\"field_values\":[\"0\"],\"operator\":\"EQ\",\"connector\":\"AND\",\"fieldNum\":0,\"isObjectReference\":false,\"isIndex\":false}]");
        //无汇报对象
        filterMaps.put("leader_null", "[{\"field_name\":\"leader\",\"field_values\":[\"\"],\"operator\":\"IS\",\"connector\":\"AND\",\"fieldNum\":0,\"isObjectReference\":false,\"isIndex\":false},{\"field_name\":\"status\",\"field_values\":[\"0\"],\"operator\":\"EQ\",\"connector\":\"AND\",\"fieldNum\":0,\"isObjectReference\":false,\"isIndex\":false}]");
//        //仅看本级部门
        filterMaps.put("in_this_level_department", "[{\"field_name\":\"status\",\"field_values\":[\"0\"],\"operator\":\"EQ\",\"connector\":\"AND\",\"fieldNum\":0,\"isObjectReference\":false,\"isIndex\":false}]");
        //仅看主属
        filterMaps.put("in_main_department", "[{\"field_name\":\"status\",\"field_values\":[\"0\"],\"operator\":\"EQ\",\"connector\":\"AND\",\"fieldNum\":0,\"isObjectReference\":false,\"isIndex\":false}]");

        //已停用
        filterMaps.put("stop_employee", "[{\"field_name\":\"status\",\"field_values\":[\"1\"],\"operator\":\"EQ\",\"connector\":\"AND\",\"fieldNum\":0,\"isObjectReference\":false,\"isIndex\":false}]");

        //未分配主属部门
        filterMaps.put("main_department_null", "[{\"field_name\":\"main_department\",\"field_values\":[\"\"],\"operator\":\"IS\",\"connector\":\"AND\",\"fieldNum\":0,\"isObjectReference\":false,\"isIndex\":false},{\"field_name\":\"status\",\"field_values\":[\"0\"],\"operator\":\"EQ\",\"connector\":\"AND\",\"fieldNum\":0,\"isObjectReference\":false,\"isIndex\":false}]");

        List<ISearchTemplate> defaultSearchTemplates = Lists.newArrayList();

        Type filterType = new TypeToken<List<Filter>>() {
        }.getType();

        //api_name 场景英文标识 ,apiName 为BpmTask
        filterMaps.forEach((api_name, filterString) -> {
            ISearchTemplate template = SearchTemplateUtil.getSearchTemplate(
                    user.getTenantId(),
                    apiName,
                    getLabel(api_name),
                    "in_progress".equals(api_name),
                    JsonUtil.fromJson(filterString, filterType),
                    labelMapping.get(api_name),
                    SearchTemplateExt.USE_FIELD_LIST
            );
            template.setApiName(api_name);
            defaultSearchTemplates.add(template);
        });
        return defaultSearchTemplates;
    }

    public String getLabel(String api_name) {
        //        todo 多语言处理
        Map<String, String> labelMapping = Maps.newHashMap();
        labelMapping.put("all_employee", "在职员工");
        labelMapping.put("not_active", "未激活的");
        labelMapping.put("pause_login", "禁止登录");
        labelMapping.put("leader_null", "无汇报对象");
        labelMapping.put("in_this_level_department", "仅看本级部门");
        labelMapping.put("in_main_department", "仅看主属");
        labelMapping.put("stop_employee", "已停用");
        labelMapping.put("main_department_null", "未分配主属部门");
        return labelMapping.get(api_name);
    }

}
