package com.facishare.paas.appframework.metadata


import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.metadata.api.data.IUniqueRule
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.*
import spock.lang.Shared
import spock.lang.Specification

class UniqueRuleExtTest extends Specification {

    @Shared
    IUniqueRule uniqueRule;

    String uniqueStr1 = "{\n" +
            "  \"id\": \"5ce7a379a5083d6a7cdee5ad\",\n" +
            "  \"tenant_id\": \"74255\",\n" +
            "  \"describe_api_name\": \"AccountObj\",\n" +
            "  \"use_when_import_excel\": false,\n" +
            "  \"use_when_call_open_api\": true,\n" +
            "  \"useable_rules\": {\n" +
            "    \"version\": 4,\n" +
            "    \"rules\": [\n" +
            "      {\n" +
            "        \"connector\": \"OR\",\n" +
            "        \"conditions\": [\n" +
            "          {\n" +
            "            \"connector\": \"AND\",\n" +
            "            \"field_name\": \"tel\",\n" +
            "            \"field_value\": \"PRECISE\",\n" +
            "            \"mapping_field\": \"\"\n" +
            "          },\n" +
            "          {\n" +
            "            \"connector\": \"AND\",\n" +
            "            \"field_name\": \"name\",\n" +
            "            \"field_value\": \"PRECISE\",\n" +
            "            \"mapping_field\": \"\"\n" +
            "          },\n" +
            "          {\n" +
            "            \"connector\": \"AND\",\n" +
            "            \"field_name\": \"remark\",\n" +
            "            \"field_value\": \"PRECISE\",\n" +
            "            \"mapping_field\": \"\"\n" +
            "          }\n" +
            "        ]\n" +
            "      }\n" +
            "    ]\n" +
            "  },\n" +
            "  \"pending_rules\": {\n" +
            "    \"version\": 4,\n" +
            "    \"rules\": [\n" +
            "      {\n" +
            "        \"connector\": \"OR\",\n" +
            "        \"conditions\": [\n" +
            "          {\n" +
            "            \"connector\": \"AND\",\n" +
            "            \"field_name\": \"tel\",\n" +
            "            \"field_value\": \"PRECISE\",\n" +
            "            \"mapping_field\": \"\"\n" +
            "          },\n" +
            "          {\n" +
            "            \"connector\": \"AND\",\n" +
            "            \"field_name\": \"name\",\n" +
            "            \"field_value\": \"PRECISE\",\n" +
            "            \"mapping_field\": \"\"\n" +
            "          },\n" +
            "          {\n" +
            "            \"connector\": \"AND\",\n" +
            "            \"field_name\": \"remark\",\n" +
            "            \"field_value\": \"PRECISE\",\n" +
            "            \"mapping_field\": \"\"\n" +
            "          }\n" +
            "        ]\n" +
            "      }\n" +
            "    ]\n" +
            "  },\n" +
            "  \"effective\": true,\n" +
            "  \"create_time\": 1558684537992,\n" +
            "  \"last_modified_time\": 1680606911399,\n" +
            "  \"version\": 13\n" +
            "}"

    String uniqueStr = "{\n" +
            "    \"tenant_id\": \"725883\",\n" +
            "    \"describe_api_name\": \"ContactObj\",\n" +
            "    \"effective\": true,\n" +
            "    \"pending_rules\": {\"rules\": [{\"connector\": \"OR\", \"conditions\": [{\"connector\": \"AND\", \"field_name\": \"name\", \"field_value\": \"PRECISE\", \"mapping_field\": \"\"}, {\"connector\": \"AND\", \"field_name\": \"mobile\", \"field_value\": \"PRECISE\", \"mapping_field\": \"\"}, {\"connector\": \"AND\", \"field_name\": \"tel\", \"field_value\": \"PRECISE\", \"mapping_field\": \"\"}]}], \"version\": 1},\n" +
            "    \"last_modified_time\": 1680160781379,\n" +
            "    \"create_time\": 1680146719998,\n" +
            "    \"useable_rules\": {\"rules\": [{\"connector\": \"OR\", \"conditions\": [{\"connector\": \"AND\", \"field_name\": \"name\", \"field_value\": \"PRECISE\", \"mapping_field\": \"\"}, {\"connector\": \"AND\", \"field_name\": \"mobile\", \"field_value\": \"PRECISE\", \"mapping_field\": \"\"}, {\"connector\": \"AND\", \"field_name\": \"tel\", \"field_value\": \"PRECISE\", \"mapping_field\": \"\"}]}], \"version\": 1},\n" +
            "    \"use_when_import_excel\": true,\n" +
            "    \"id\": \"6425011fcaf8ce000144fd03\",\n" +
            "    \"use_when_call_open_api\": false,\n" +
            "    \"version\": 1\n" +
            "}"


    def "test getUniqueRuleKeySet"() {
        when:
        uniqueRule = JacksonUtils.fromJson(uniqueStr1, IUniqueRule.class)
        Map<String, Object> dataMap = [name: name, tel: tel, "remark": remark]
        def objectData = new ObjectData(dataMap)
        IObjectDescribe describe = new ObjectDescribe();
        IFieldDescribe nameFieldDescribe = new TextFieldDescribe()
        nameFieldDescribe.setApiName("name")
        IFieldDescribe phoneNumberFieldDescribe = new PhoneNumberFieldDescribe()
        phoneNumberFieldDescribe.setApiName("tel")
        IFieldDescribe longTextFieldDescribe = new LongTextFieldDescribe()
        longTextFieldDescribe.setApiName("remark")

        describe.setFieldDescribes([nameFieldDescribe, phoneNumberFieldDescribe, longTextFieldDescribe])
        def key = UniqueRuleExt.of(uniqueRule).getUniqueRuleKeySet(describe, objectData)
        then:
        res.equals(key)
        where:
        name    | remark | tel    || res
        "测试1" | null   | "tell" || ["测试1", null, "tell"]
        ""      | null   | "tell" || ["", null, "tell"]
        null    | null   | "tell" || [null, null, "tell"]
        ""      | null   | ""     || ["", null, ""]
    }

    def "test getUniqueRuleKeySet2"() {
        when:
        uniqueRule = JacksonUtils.fromJson(uniqueStr, IUniqueRule.class)
        Map<String, Object> dataMap = [name: name, tel: tel, "mobile": mobile]
        def objectData = new ObjectData(dataMap)
        IObjectDescribe describe = new ObjectDescribe();
        IFieldDescribe nameFieldDescribe = new TextFieldDescribe()
        nameFieldDescribe.setApiName("name")
        NumberFieldDescribe phoneNumberFieldDescribe = new NumberFieldDescribe()
        phoneNumberFieldDescribe.setApiName("tel")
        IFieldDescribe longTextFieldDescribe = new LongTextFieldDescribe()
        longTextFieldDescribe.setApiName("mobile")

        describe.setFieldDescribes([nameFieldDescribe, phoneNumberFieldDescribe, longTextFieldDescribe])
        def key = UniqueRuleExt.of(uniqueRule).getUniqueRuleKeySet(describe, objectData)
        then:
        res.equals(key)
        where:
        mobile | name    | tel        || res
        null   | "测试1" | "123"      || [null, "测试1", "123"]
        null   | ""      | "122"      || [null, "", "122"]
        null   | null    | "1122.000" || [null, null, "1122.000"]
        null   | ""      | ""         || [null, "", null]

        ""     | "测试1" | "1122.000" || ["", "测试1", "1122.000"]
        ""     | ""      | "122"      || ["", "", "122"]
        ""     | null    | "122"      || ["", null, "122"]
        ""     | ""      | ""         || ["", "", null]
    }

    def "test getUniqueRuleKey"() {
        when:
        uniqueRule = JacksonUtils.fromJson(uniqueStr, IUniqueRule.class)
        Map<String, Object> dataMap = [name: name, tel: tel, "mobile": mobile]
        def objectData = new ObjectData(dataMap)
        IObjectDescribe describe = new ObjectDescribe();
        IFieldDescribe nameFieldDescribe = new TextFieldDescribe()
        nameFieldDescribe.setApiName("name")
        IFieldDescribe phoneNumberFieldDescribe = new PhoneNumberFieldDescribe()
        phoneNumberFieldDescribe.setApiName("tel")
        PhoneNumberFieldDescribe longTextFieldDescribe = new PhoneNumberFieldDescribe()
        longTextFieldDescribe.setApiName("mobile")

        describe.setFieldDescribes([nameFieldDescribe, phoneNumberFieldDescribe, longTextFieldDescribe])
        def key = UniqueRuleExt.of(uniqueRule).getUniqueRuleKey(describe, objectData)
        then:
        println "====" + key
//        res.equals(key.toString())
        where:
        name                | mobile | tel || res
//        "测试1" | null   | "tell" || "测试1\n\ntell"
//        ""      | null   | "tell" || "\n\ntell"
//        null    | null   | "tell" || "\n\ntell"
//        ""      | null   | ""     || "\n\n\n"
        "库尔班江 麦麦提敏" | ""     | ""  || _
        "齐兴东"            | ""     | ""  || _
    }


}
