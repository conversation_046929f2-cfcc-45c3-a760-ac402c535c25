package com.facishare.paas.appframework.privilege.model;

import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/10/18
 */
public class EnterpriseRelationTeamMemberStrategy {

    private static final Map<String, TeamMemberStrategy> STRATEGY_MAP = Maps.newHashMap();

    static {
        STRATEGY_MAP.put(FieldDescribeExt.RELATION_OUTER_OWNER, new OuterOwnerTeamMemberStrategy());
        STRATEGY_MAP.put(FieldDescribeExt.RELATION_OUTER_EMPLOY_READ, new OuterEmployeeReadTeamMemberStrategy());
        STRATEGY_MAP.put(FieldDescribeExt.RELATION_OUTER_EMPLOY_WRITE, new OuterEmployeeWriteTeamMemberStrategy());
        STRATEGY_MAP.put(FieldDescribeExt.RELATION_OUTER_TENANT_READ, new OuterTenantReadTeamMemberStrategy());
        STRATEGY_MAP.put(FieldDescribeExt.RELATION_OUTER_TENANT_WRITE, new OuterTenantWriteTeamMemberStrategy());
    }

    public static TeamMemberStrategy getStrategy(String str) {
        return STRATEGY_MAP.getOrDefault(str, (outTenant, outUser) -> null);
    }

    public interface TeamMemberStrategy {
        TeamMember getTeamMember(String outTenantId, String outUserId);
    }

    private static class OuterOwnerTeamMemberStrategy implements TeamMemberStrategy {
        @Override
        public TeamMember getTeamMember(String outTenantId, String outUserId) {
            return new TeamMember(outUserId, TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE, outTenantId, TeamMember.MemberType.EMPLOYEE);
        }
    }

    private static class OuterEmployeeReadTeamMemberStrategy implements TeamMemberStrategy {
        @Override
        public TeamMember getTeamMember(String outTenantId, String outUserId) {
            return new TeamMember(outUserId, TeamMember.Role.NORMAL_STAFF, TeamMember.Permission.READONLY, outTenantId, TeamMember.MemberType.EMPLOYEE);
        }
    }

    private static class OuterEmployeeWriteTeamMemberStrategy implements TeamMemberStrategy {
        @Override
        public TeamMember getTeamMember(String outTenantId, String outUserId) {
            return new TeamMember(outUserId, TeamMember.Role.NORMAL_STAFF, TeamMember.Permission.READANDWRITE, outTenantId, TeamMember.MemberType.EMPLOYEE);
        }
    }

    private static class OuterTenantWriteTeamMemberStrategy implements TeamMemberStrategy {
        @Override
        public TeamMember getTeamMember(String outTenantId, String outUserId) {
            return new TeamMember(outTenantId, TeamMember.Role.NORMAL_STAFF, TeamMember.Permission.READANDWRITE, outTenantId, TeamMember.MemberType.OUT_TENANT);
        }
    }

    private static class OuterTenantReadTeamMemberStrategy implements TeamMemberStrategy {
        @Override
        public TeamMember getTeamMember(String outTenantId, String outUserId) {
            return new TeamMember(outTenantId, TeamMember.Role.NORMAL_STAFF, TeamMember.Permission.READONLY, outTenantId, TeamMember.MemberType.OUT_TENANT);
        }
    }
}
