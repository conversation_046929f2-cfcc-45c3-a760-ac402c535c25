package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * create by z<PERSON><PERSON> on 2019/05/07
 */
public enum ImportType {
    DEFAULT(1),
    /**
     * 不支持更新导入
     */
    UNSUPPORT_UPDATE_IMPORT(0),
    /**
     * 不支持新建导入
     */
    UNSUPPORT_INSERT_IMPORT(3),
    ;
    @Getter(onMethod_ = {@JsonValue})
    private int type;

    private static final Map<Integer, ImportType> typeMap;

    static {
        typeMap = Stream.of(values()).collect(Collectors.toMap(ImportType::getType, Function.identity()));
    }

    ImportType(int type) {
        this.type = type;
    }

    @JsonCreator
    public static ImportType of(Integer type) {
        ImportType importType = typeMap.get(type);
        if (Objects.isNull(importType)) {
            throw new ValidateException("unSupport importType:" + type);
        }
        return importType;
    }
}
