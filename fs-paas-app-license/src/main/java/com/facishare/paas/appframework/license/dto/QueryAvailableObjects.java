package com.facishare.paas.appframework.license.dto;

import lombok.*;

import java.util.Set;

public interface QueryAvailableObjects {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Arg {
        private AuthContext context;
        private String crmKey;
        private String moduleType;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    class Result extends BaseResult {
        private Set<String> result;
    }
}
