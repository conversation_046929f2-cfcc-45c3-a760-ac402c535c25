package com.facishare.paas.appframework.metadata

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.common.util.Tuple
import com.facishare.paas.appframework.config.ConfigService
import com.facishare.paas.appframework.config.ConfigServiceImpl
import com.facishare.paas.appframework.config.OptionalFeaturesService
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO
import com.facishare.paas.appframework.license.LicenseService
import com.facishare.paas.appframework.license.LicenseServiceImpl
import com.facishare.paas.appframework.metadata.cache.RedissonService
import com.facishare.paas.appframework.metadata.cache.RedissonServiceImpl
import com.facishare.paas.appframework.metadata.config.ObjectConfigService
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService
import com.facishare.paas.appframework.privilege.FunctionPrivilegeServiceImpl
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.search.ISearchTemplate
import com.facishare.paas.metadata.api.service.ISearchTemplateService
import com.facishare.paas.metadata.common.MetadataContext
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.search.SearchTemplate
import com.facishare.paas.metadata.impl.ui.layout.Layout
import com.facishare.paas.metadata.service.impl.SearchTemplateServiceImpl
import com.facishare.paas.metadata.ui.layout.ILayout
import org.redisson.RedissonLock
import org.redisson.api.RLock
import spock.lang.Specification

import java.time.Instant

class SearchTemplateLogicServiceImplTest extends Specification {
    SearchTemplateLogicService searchTemplateLogicService
    ISearchTemplateService searchTemplateService = Mock(SearchTemplateServiceImpl)
    DescribeLogicService describeLogicService = Mock(DescribeLogicServiceImpl)
    DataListHeaderConfigService dataListHeaderConfigService = Mock(DataListHeaderConfigServiceImpl)
    LayoutLogicService layoutLogicService = Mock(LayoutLogicServiceImpl)
    ConfigService configService = Mock(ConfigServiceImpl)
    LicenseService licenseService = Mock(LicenseServiceImpl)
    RedissonService redissonService = Mock(RedissonServiceImpl)
    FunctionPrivilegeService functionPrivilegeService = Mock(FunctionPrivilegeServiceImpl.class)
    ObjectConfigService objectConfigService = Mock(ObjectConfigService.class)
    OptionalFeaturesService optionalFeaturesService = Mock(OptionalFeaturesService.class)

    def setup() {
        searchTemplateLogicService = new SearchTemplateLogicServiceImpl(
                "searchTemplateService": searchTemplateService,
                "dataListHeaderConfigService": dataListHeaderConfigService,
                "layoutLogicService": layoutLogicService,
                "describeLogicService": describeLogicService,
                "configService": configService,
                "licenseService": licenseService,
                "redissonService": redissonService,
                "functionPrivilegeService": functionPrivilegeService,
                "objectConfigService": objectConfigService,
                "optionalFeaturesService": optionalFeaturesService)


    }

    def "test findByIdJoinTenant"() {
        given:
        User user = new User("74255", "1000")
        ISearchTemplate searchTemplate = searchTemplateValue
        when:
        searchTemplateService.findByIdAndType(_, _, _, _) >> searchTemplate;
        searchTemplateService.findById(_, _, _) >> searchTemplate
        objectConfigService.isWebConfigGray(_, _) >> true
        searchTemplateService.findByIdAndTypeNoMerge(_, _, _, _) >> searchTemplate
        searchTemplateService.sequenceFindById(_, _, _) >> searchTemplate
        searchTemplateService.findForTenantByApiName(_, _, _, _, _) >> searchTemplate
        searchTemplateService.findCustomTemplateByApiNameNoMerge(_, _, _, _, _, _) >> searchTemplate
        def result = searchTemplateLogicService.findByIdJoinTenant("62ecf1e41941b00001c77a2b",
                "object_pYq1b__c", sceneType, user)
        print(result);
        then:
        noExceptionThrown()
        where:
        sceneType | searchTemplateValue         || res
        "default" | setSearchTemplate("default") | _
        "tenant"  | setSearchTemplate("tenant")  | _
        null      | new SearchTemplate()         | _
    }

    def "test findByIdJoinTenant throw exception"() {
        given:
        User user = new User("74255", "1000")
        ISearchTemplate searchTemplate = Mock()
        when:
        searchTemplateService.findByIdAndType(_, _, _, _) >> null;
        searchTemplateService.findById(_, _, _) >> searchTemplate
        def result = searchTemplateLogicService.findByIdJoinTenant("62ecf1e41941b00001c77a2b",
                "object_pYq1b__c", "default", user)
        print(result);
        then:
        thrown(ValidateException.class)
    }

    def "test findOutSearchTemplate"() {
        given:
        User user = new User("74255", "1000")
        ISearchTemplate searchTemplate1 = Mock()
        ISearchTemplate searchTemplate2 = Mock()
        when:
        searchTemplateService.findByObjectDescribeAPINameAndCode(_, _, _) >> [searchTemplate1, searchTemplate2]
        def result = searchTemplateLogicService.findOutSearchTemplate("object_pYq1b__c", user)
        print(result);
        then:
        result != null
    }

    def "test findOutSearchTemplate String describeApiName, User user, String extendAttribute"() {
        given:
        User user = new User("74255", "1000")
        ISearchTemplate searchTemplate1 = new SearchTemplate();
        searchTemplate1.setCreateTime(Instant.now().getEpochSecond())
        ISearchTemplate searchTemplate2 = new SearchTemplate();
        searchTemplate2.setCreateTime(Instant.now().getEpochSecond())

        when:
        searchTemplateService.findForCustom(_, _, _) >> [searchTemplate1, searchTemplate2]
        def result = searchTemplateLogicService.findOutSearchTemplate("object_pYq1b__c", user, "hscustomermgr")
        print(result);
        then:
        result != null
    }

    def "findByDescribeApiNameAndExtendAttribute"() {
        given:
        User user = new User("74255", "1000")
        ISearchTemplate searchTemplate = new SearchTemplate()
        List searchTemplateList = [searchTemplate]
        List describeApiNames = ["AccountObj"]
        IObjectDescribe describe = new ObjectDescribe()
        OptionalFeaturesSwitchDTO optionalFeaturesSwitchDTO = new OptionalFeaturesSwitchDTO()
        when:
        searchTemplateService.findForCustom(_ as MetadataContext, _ as String, _ as String) >> searchTemplateList
        searchTemplateService.getDefaultSceneFromSysCache(_, _, _) >> searchTemplate;
        licenseService.queryAvailableObject(_) >> describeApiNames
        describeLogicService.findObjectWithoutCopyIfGray(_, _) >> describe
        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitchDTO
        def result = searchTemplateLogicService.findByDescribeApiNameAndExtendAttribute(
                describeApiName, "hscustomermgr", user);
        then:
        result == res != null;
        where:
        describeApiName | res
        "AccountObj"    | _
        "CRMFeedObj"    | _
    }

    def "test findByDescribeApiNameWithHandelFieldList"() {
        given:
        User user = new User("74255", "1000")
        ISearchTemplate searchTemplate = new SearchTemplate()
        List searchTemplateList = [searchTemplate]
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")
        List<Map<String, Object>> fieldListConfig = [["tuple": true]]
        OptionalFeaturesSwitchDTO optionalFeaturesSwitchDTO = new OptionalFeaturesSwitchDTO()
        ILayout layout = Mock()
        when:
        searchTemplateService.findForCustom(_, _, _) >> searchTemplateList
        describeLogicService.findObject(_, _) >> objectDescribe
        dataListHeaderConfigService.findFieldListConfig(_, _, _) >> fieldListConfig
        layoutLogicService.findObjectLayoutWithType(_, _, _, _, _) >> layout
        describeLogicService.findObjectWithoutCopyIfGray(_, _) >> objectDescribe
        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitchDTO
        def result = searchTemplateLogicService.findByDescribeApiNameWithHandelFieldList("AccountObj", "hscustomermgr", user)
        then:
        result != null;
    }

    def "test handelTemplateFieldList"() {
        given:
        User user = new User("74255", "1000")
        IObjectDescribe objectDescribe = new ObjectDescribe();
        objectDescribe.setApiName("AccountObj")
        LayoutRuleExt.FieldConfig fieldListConfig = LayoutRuleExt.FieldConfig.fromMap(["tuple": true])
        when:
        def result = searchTemplateLogicService.handelTemplateFieldList(searchTemplateList, objectDescribe, ["field"], [fieldListConfig])
        then:
        result != null;
        where:
        searchTemplateList     || res
        [new SearchTemplate()] || _
        []                     || _
    }

    def "test setDefaultScene"() {
        given:
        User user = new User("74255", "1000")
        Map<String, String> customSceneConfig = [
                "defaultScene": "default",
                "orderBy"     : ["1", "2", "3"],
                "hiddenScene" : ["scene_mlE91__c"]
        ]
        String fieldListConfigJSON = JSON.toJSONString(LayoutRuleExt.FieldConfig.fromMap(["tuple": true]))
        String jsonMap = JSON.toJSONString(customSceneConfig);
        when:
        configService.findUserConfig(_, _) >> jsonMap;
        configService.queryUserConfigs(_, _) >> ["key": fieldListConfigJSON]
        def result = searchTemplateLogicService.setDefaultScene(searchTemplateParam, user)
        then:
        result != null;
        where:
        searchTemplateParam                                                                                                         || res
        new SearchTemplate(["extend_attribute": "hscustomermember", "object_describe_api_name": "AccountObj"] as Map)               || _
        new SearchTemplate(["extend_attribute": "hscustomermember,hscustomermgr", "object_describe_api_name": "AccountObj"] as Map) || _
    }

    def "test setDefaultScene findUserConfig is null"() {
        given:
        User user = new User("74255", "1000")
        Map<String, String> customSceneConfig = [
                "defaultScene": "default",
                "orderBy"     : ["1", "2", "3"],
                "hiddenScene" : ["scene_mlE91__c"]
        ]
        String fieldListConfigJSON = JSON.toJSONString(LayoutRuleExt.FieldConfig.fromMap(["tuple": true]))
        when:
        configService.findUserConfig(_, _) >> null;
        configService.queryUserConfigs(_, _) >> ["key": fieldListConfigJSON]
        def result = searchTemplateLogicService.setDefaultScene(searchTemplateParam, user)
        then:
        result != null;
        where:
        searchTemplateParam                                                                                                         || res
        new SearchTemplate(["extend_attribute": "hscustomermember", "object_describe_api_name": "AccountObj"] as Map)               || _
        new SearchTemplate(["extend_attribute": "hscustomermember,hscustomermgr", "object_describe_api_name": "AccountObj"] as Map) || _
    }

    def "test getCustomSceneConfig"() {
        given:
        User user = new User("74255", "1000")
        Map<String, String> customSceneConfig = [
                "defaultScene": "default",
                "orderBy"     : ["1", "2", "3"],
                "hiddenScene" : ["scene_mlE91__c"]
        ]
        String jsonMap = JSON.toJSONString(customSceneConfig);
        when:
        configService.findUserConfig(_, _) >> jsonMap;
        def result = searchTemplateLogicService.getCustomSceneConfig(user, "AccountObj", "hscustomermember,hscustomermgr")
        then:
        result != null;
    }

    def "test createOrUpdateFieldWidthConfig"() {
        given:
        User user = new User("74255", "1000")
        Map<String, String> customSceneConfig = [
                "defaultScene": "default",
                "orderBy"     : ["1", "2", "3"],
                "hiddenScene" : ["scene_mlE91__c"]
        ]
        HeadField headField = HeadField.fromFieldNameAndWidth("field_s23sq__c", 1)
        String jsonMap = JSON.toJSONString(customSceneConfig);
        Tuple<String, Number> tuple = Tuple.of("field_s23sq__c", 1)
        when:
        configService.findUserConfig(_, _) >> jsonMap;
        dataListHeaderConfigService.findFieldWidthConfig(_, _, _) >> [tuple]
        def result = searchTemplateLogicService.createOrUpdateFieldWidthConfig("AccountObj", "hscustomermember,hscustomermgr", [headField], user)
        then:
        noExceptionThrown();
    }

    def "test getAuthorizedFields"() {
        given:
        User user = new User("74255", "1000")
        IObjectDescribe objectDescribe = new ObjectDescribe();
        when:
        functionPrivilegeService.getUnauthorizedFields(_, _) >> ["test"]
        def result = searchTemplateLogicService.getAuthorizedFields(objectDescribe, user);
        then:
        result != null;
    }

    def "test findDefaultOutTemplate"() {
        given:
        User user = new User("74255", "1000")
        ISearchTemplate searchTemplate = new SearchTemplate();
        searchTemplate.setApiName("ALL");
        searchTemplate.setIsDefault(true)
        when:
        searchTemplateService.findForCustom(_, _, _) >> [searchTemplate]
        searchTemplateLogicService.findByIdJoinTenant(_, _, _, _) >> searchTemplate
        def result = searchTemplateLogicService.findDefaultOutTemplate("AccountObj", user);
        then:
        result != null;
    }


    def "test adjustOrder"() {
        given:
        def list = []
        CustomSceneService.AdjustOrderDTO adjustOrderDTO = new CustomSceneService.AdjustOrderDTO()
        adjustOrderDTO.setType("default")
        list << adjustOrderDTO
        ISearchTemplate searchTemplate = Mock(SearchTemplateExt)
        RLock rLock = Mock(RedissonLock)
        when:
        searchTemplateService.findByIdAndType(_, _, _, _) >> searchTemplate
        redissonService.tryFairLock(_, _, _, _) >> rLock
        searchTemplateLogicService.adjustOrder(list, "SalesOrderObj", User.systemUser("74255"))
        then:
        1 * redissonService.unlock(_)

    }

    def "FindByIdWithHandelFieldList"() {
        given:
        def list = []
        User user = Mock()
        IObjectDescribe objectDescribe = new ObjectDescribe();
        objectDescribe.setApiName("CRMFeedObj")
        ISearchTemplate iSearchTemplate = Mock()
        Layout layout = new Layout()
        def layoutExt = LayoutExt.of(layout)

        when:
        searchTemplateService.findByIdAndType(_, _, _, _) >> iSearchTemplate
        describeLogicService.findObjectWithoutCopyIfGray(_, _) >> objectDescribe
        dataListHeaderConfigService.findFieldListConfig(_, _, _) >> list;
        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitchDTO
        layoutLogicService.findObjectLayoutWithType(_, _, _, _, _) >> layoutExt
        def reuslt = searchTemplateLogicService.findByIdWithHandelFieldList("123", "AccountObj", "sss", user)
        then:
        reuslt != null
        where:
        optionalFeaturesSwitchDTO          || result
        getOptionalFeatureSwitchDto(false) || _
        getOptionalFeatureSwitchDto(true)  || _
    }

    def "Create"() {
        given:
        IObjectDescribe describe = new ObjectDescribe();
        ISearchTemplate searchTemplate = new SearchTemplate()
        searchTemplate.setObjectDescribeApiName("AccountObj")
        User user = Mock()
        OptionalFeaturesSwitchDTO optionalFeaturesSwitchDTO = new OptionalFeaturesSwitchDTO()
        when:
        describeLogicService.findObjectWithoutCopyIfGray(_, _) >> describe;
        searchTemplateService.create(_, _) >> searchTemplate
        searchTemplateService.findForCustom(_, _, _) >> [searchTemplate]
        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitchDTO
        configService.findUserConfig(_, _) >> ""
        searchTemplateService.getDefaultSceneFromSysCache(_, _, _) >> searchTemplate
        licenseService.queryAvailableObject() >> [""]
        def result = searchTemplateLogicService.create(searchTemplate, user)
        then:
        result != null
    }

    def "update"() {
        given:
        ISearchTemplate searchTemplate = new SearchTemplate();
        IObjectDescribe describe = new ObjectDescribe();
        describe.setApiName("AccountObj")
        searchTemplate.setObjectDescribeApiName("AccountObj")
        User user = Mock()
        OptionalFeaturesSwitchDTO optionalFeaturesSwitchDTO = new OptionalFeaturesSwitchDTO()
        when:
        searchTemplateService.findByIdAndType(_, _, _, _) >> searchTemplate
        describeLogicService.findObjectWithoutCopyIfGray(_, _) >> describe
        searchTemplateService.update(_, _) >> searchTemplate
        objectConfigService.isWebConfigGray(_, _) >> true
        searchTemplateService.findForCustom(_, _, _) >> [searchTemplate]
        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitchDTO
        configService.findUserConfig(_, _) >> ""
        searchTemplateService.getDefaultSceneFromSysCache(_, _, _) >> searchTemplate
        licenseService.queryAvailableObject(_) >> ["AccountObj"]
        def result = searchTemplateLogicService.update(searchTemplate, user)
        then:
        result != null
        optionalFeaturesSwitchDTO || result
        getOptionalFeatureSwitchDto(false) || _
        getOptionalFeatureSwitchDto(true) || _
    }

    def "updateIsNewScene"() {
        given:
        ISearchTemplate searchTemplate = new SearchTemplate();
        searchTemplate.setType("tenant")
        User user = Mock()
        when:
        searchTemplateService.findById(_, _, _) >> searchTemplate
        searchTemplateService.update(_, _) >> searchTemplate
        def result = searchTemplateLogicService.updateIsNewScene("1", "AccountObj", user);
        then:
        result != null
    }

    def "createFieldListConfig"() {
        given:

        User user = Mock()
        RLock lock = Mock()
        List<IHeadField> list = [new HeadField()]
        when:
        searchTemplateService.findByIdAndTypeNoMerge(_, _, _, _) >> searchTemplate1
        redissonService.tryLock(_, _, _) >> lock
        searchTemplateService.findCustomTemplateByApiNameNoMerge(_, _, _, _, _, _) >> searchTemplate2
        searchTemplateService.update(_, _) >> setSearchTemplate()
        res == searchTemplateLogicService.createFieldListConfig("AccountObj", "1", "tenant", null, list, user)
        then:
        noExceptionThrown()
        where:
        searchTemplate1             | searchTemplate2             || res
        null                        | setSearchTemplate("tenant") || null
        setSearchTemplate("tenant") | setSearchTemplate("tenant") || setSearchTemplate("tenant")
        setSearchTemplate("tenant") | null                        || setSearchTemplate("tenant")
        setSearchTemplateCustom()   | setSearchTemplate("tenant") || setSearchTemplateCustom()

    }

    def setSearchTemplate(type) {
        ISearchTemplate searchTemplate = new SearchTemplate();
        searchTemplate.setType(type)
        return searchTemplate
    }

    def setSearchTemplateCustom() {
        ISearchTemplate searchTemplate = new SearchTemplate();
        searchTemplate.setType("tenant")
        searchTemplate.setType("custom")
        return searchTemplate
    }

    def "createOrUpdateFieldWidthConfig"() {

    }

    OptionalFeaturesSwitchDTO getOptionalFeatureSwitchDto(flag) {
        OptionalFeaturesSwitchDTO optionalFeaturesSwitchDTO = new OptionalFeaturesSwitchDTO()
        optionalFeaturesSwitchDTO.setIsRelatedTeamEnabled(flag)
        return optionalFeaturesSwitchDTO
    }
}
