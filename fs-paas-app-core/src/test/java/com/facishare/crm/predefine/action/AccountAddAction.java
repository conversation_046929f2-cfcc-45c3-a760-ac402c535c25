package com.facishare.crm.predefine.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.metadata.impl.ObjectData;
import lombok.Builder;
import lombok.Data;

import java.util.List;

import static com.facishare.crm.predefine.action.AccountAddAction.Arg;
import static com.facishare.crm.predefine.action.AccountAddAction.Result;

/**
 * Created by liyiguang on 2017/7/9.
 */
public class AccountAddAction extends PreDefineAction<Arg,Result> {

    public AccountAddAction() {
        this.argClass = Arg.class;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    @Data
    public static class Arg {
        @JSONField(name = "M1")
        String object_data; //数据格式向前兼容

        public ObjectData getObjectData() {
            ObjectData objectData = new ObjectData();
            objectData.fromJsonString(object_data);
            return objectData;
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        return null;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        return super.after(arg, result);
    }

    @Data
    @Builder
    public static class Result {
        @JSONField(name = "M3")
        Object objectData;
    }
}
