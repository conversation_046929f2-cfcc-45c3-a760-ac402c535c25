package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试SelectOneDataConverter类的单选字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class SelectOneDataConverterTest {

  @Mock
  private IObjectDataProxyService dataService;

  @Mock
  private SessionContext sessionContext;

  @Mock
  private SelectOneFieldDescribe mockFieldDescribe;

  @Mock
  private ISelectOption mockOption1;

  @Mock
  private ISelectOption mockOption2;

  @Mock
  private ISelectOption mockOtherOption;

  private SelectOneDataConverter converter;
  private IFieldDescribe fieldDescribe;
  private IObjectData objectData;

  @BeforeEach
  void setUp() {
    converter = new SelectOneDataConverter();
    fieldDescribe = createSelectOneFieldDescribe("select_field");
    objectData = createObjectData("select_field", "option1");

    // 设置转换器的属性
    converter.setFieldDescribe(fieldDescribe);
    converter.setObjectData(objectData);
    converter.setDataService(dataService);

    // 设置mock选项的行为 - 使用lenient避免UnnecessaryStubbingException
    lenient().when(mockOption1.getValue()).thenReturn("option1");
    lenient().when(mockOption1.getLabel()).thenReturn("选项1");
    lenient().when(mockOption2.getValue()).thenReturn("option2");
    lenient().when(mockOption2.getLabel()).thenReturn("选项2");
    lenient().when(mockOtherOption.getValue()).thenReturn(SelectOne.OPTION_OTHER_VALUE);
    lenient().when(mockOtherOption.getLabel()).thenReturn("其他");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法正常转换单选值
   */
  @Test
  @DisplayName("测试convertFieldData - 正常单选值转换")
  void testConvertFieldData_NormalSelectOneConversion() throws Exception {
    // 准备测试数据 - 使用mock字段描述
    when(mockFieldDescribe.getApiName()).thenReturn("select_field");
    when(mockFieldDescribe.getSelectOptions()).thenReturn(Arrays.asList(mockOption1, mockOption2));
    converter.setFieldDescribe(mockFieldDescribe);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("选项1", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理null值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null值")
  void testConvertFieldData_HandlesNullValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("select_field", null);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理空字符串
   */
  @Test
  @DisplayName("测试convertFieldData - 处理空字符串")
  void testConvertFieldData_HandlesEmptyString() throws Exception {
    // 准备测试数据
    objectData = createObjectData("select_field", "");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理"其他"选项
   */
  @Test
  @DisplayName("测试convertFieldData - 处理其他选项")
  void testConvertFieldData_HandlesOtherOption() throws Exception {
    // 准备测试数据 - 选择"其他"选项
    when(mockFieldDescribe.getApiName()).thenReturn("select_field");
    when(mockFieldDescribe.getSelectOptions()).thenReturn(Arrays.asList(mockOption1, mockOtherOption));
    converter.setFieldDescribe(mockFieldDescribe);
    
    objectData = createObjectData("select_field", SelectOne.OPTION_OTHER_VALUE);
    objectData.set("select_field__o", "自定义值");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 应该返回"其他: 自定义值"
    assertEquals("其他: 自定义值", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理"其他"选项但无自定义值
   */
  @Test
  @DisplayName("测试convertFieldData - 其他选项无自定义值")
  void testConvertFieldData_OtherOptionWithoutCustomValue() throws Exception {
    // 准备测试数据 - 选择"其他"选项但没有自定义值
    when(mockFieldDescribe.getApiName()).thenReturn("select_field");
    when(mockFieldDescribe.getSelectOptions()).thenReturn(Arrays.asList(mockOption1, mockOtherOption));
    converter.setFieldDescribe(mockFieldDescribe);
    
    objectData = createObjectData("select_field", SelectOne.OPTION_OTHER_VALUE);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 应该只返回"其他"
    assertEquals("其他", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理无匹配选项的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 无匹配选项")
  void testConvertFieldData_NoMatchingOption() throws Exception {
    // 准备测试数据 - 值不匹配任何选项
    when(mockFieldDescribe.getApiName()).thenReturn("select_field");
    when(mockFieldDescribe.getSelectOptions()).thenReturn(Arrays.asList(mockOption1, mockOption2));
    converter.setFieldDescribe(mockFieldDescribe);
    
    objectData = createObjectData("select_field", "unknown_option");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 无匹配选项时应该返回空字符串
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理空选项列表
   */
  @Test
  @DisplayName("测试convertFieldData - 空选项列表")
  void testConvertFieldData_EmptyOptionsList() throws Exception {
    // 准备测试数据 - 空的选项列表
    when(mockFieldDescribe.getApiName()).thenReturn("select_field");
    when(mockFieldDescribe.getSelectOptions()).thenReturn(Arrays.asList());
    converter.setFieldDescribe(mockFieldDescribe);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 空选项列表时应该返回空字符串
    assertEquals("", result);
  }

  /**
   * 提供不同类型的选择值测试数据
   */
  private static Stream<Arguments> provideSelectValues() {
    return Stream.of(
        Arguments.of("option1", "选项1", true),
        Arguments.of("option2", "选项2", true),
        Arguments.of("nonexistent", "", false),
        Arguments.of(null, "", false),
        Arguments.of("", "", false)
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理不同类型的选择值
   */
  @ParameterizedTest
  @MethodSource("provideSelectValues")
  @DisplayName("测试convertFieldData - 不同类型的选择值")
  void testConvertFieldData_DifferentSelectValues(Object inputValue, String expectedResult, boolean shouldMatch) throws Exception {
    // 准备测试数据
    when(mockFieldDescribe.getApiName()).thenReturn("select_field");
    when(mockFieldDescribe.getSelectOptions()).thenReturn(Arrays.asList(mockOption1, mockOption2));
    converter.setFieldDescribe(mockFieldDescribe);
    
    objectData = createObjectData("select_field", inputValue);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals(expectedResult, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理字段不存在的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 字段不存在")
  void testConvertFieldData_FieldNotExists() throws Exception {
    // 准备测试数据 - 创建不包含目标字段的数据
    when(mockFieldDescribe.getApiName()).thenReturn("select_field");
    when(mockFieldDescribe.getSelectOptions()).thenReturn(Arrays.asList(mockOption1, mockOption2));
    converter.setFieldDescribe(mockFieldDescribe);
    
    objectData = createObjectData("other_field", "option1");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 字段不存在时应该返回空字符串
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试转换器的基本属性设置
   */
  @Test
  @DisplayName("测试转换器属性设置")
  void testConverterProperties() {
    // 验证属性设置
    assertEquals(fieldDescribe, converter.getFieldDescribe());
    assertEquals(objectData, converter.getObjectData());
    assertEquals(dataService, converter.getDataService());
  }

  /**
   * 创建测试用的单选字段描述
   */
  private IFieldDescribe createSelectOneFieldDescribe(String apiName) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试单选字段");
    fieldMap.put("type", IFieldType.SELECT_ONE);
    return FieldDescribeFactory.newInstance(fieldMap);
  }

  /**
   * 创建测试用的对象数据
   */
  private IObjectData createObjectData(String fieldName, Object value) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldName, value);
    return new ObjectData(dataMap);
  }
}
