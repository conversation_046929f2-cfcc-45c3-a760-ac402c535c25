package com.facishare.paas.appframework.privilege.dto;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Delegate;

import java.util.List;

/**
 * wiki  http://wiki.firstshare.cn/pages/viewpage.action?pageId=49349283
 * create by <PERSON><PERSON><PERSON> on 2019/08/26
 */
public interface QueryRoleInfoWithCodes {
    @Data
    @Builder
    class Arg {
        private AuthContext authContext;
        /**
         * 角色编号列表
         */
        private List<String> roleCodes;
        /**
         * 角色名
         */
        private String roleName;
        /**
         * 角色类型
         */
        private Integer roleType;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    class Result extends BasePrivilegeResult {
        @Delegate
        private RoleInfoHelper result;
    }

    @Data
    class RoleInfoHelper {
        private List<GetUserRoleInfo.RoleInfo> roles;
        private PageInfo pageInfo;
    }

}
