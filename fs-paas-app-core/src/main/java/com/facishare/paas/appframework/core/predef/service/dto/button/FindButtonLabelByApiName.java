package com.facishare.paas.appframework.core.predef.service.dto.button;

import com.facishare.paas.appframework.core.util.Lang;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * Created by zhaooju on 2023/5/16
 */
public interface FindButtonLabelByApiName {
    @Data
    class Arg {
        private String describeApiName;
        private Set<String> buttonApiNames;
    }

    @Data
    @Builder
    class Result {
        @Singular
        private List<LangValue> langValues;
    }

    @Data
    class LangValue {
        private String apiName;
        private String lang;
        private String value;

        public LangValue(String apiName, String lang, String value) {
            this.apiName = apiName;
            this.lang = lang;
            this.value = value;
        }

        public static List<LangValue> build(String apiName, Localization localization) {
            List<LangValue> result = Lists.newArrayList();
            if (Objects.isNull(localization)) {
                return result;
            }
            for (Lang lang : Lang.values()) {
                String value = localization.get(lang.getValue(), false);
                result.add(new LangValue(apiName, lang.getValue(), value));
            }
            return result;
        }
    }
}
