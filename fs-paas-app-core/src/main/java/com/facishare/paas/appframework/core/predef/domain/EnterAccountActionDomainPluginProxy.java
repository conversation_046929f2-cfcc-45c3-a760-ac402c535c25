package com.facishare.paas.appframework.core.predef.domain;

import com.facishare.rest.core.annotation.*;

import java.util.Map;

@RestResource(
        value = "NCRM",
        desc = "入账领域插件RPC代理类", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC"
)
public interface EnterAccountActionDomainPluginProxy {
    @POST(desc = "入账领域插件rest接口")
    EnterAccountActionDomainPlugin.RestResult post(@ServiceURLParam String url, @Body EnterAccountActionDomainPlugin.Arg arg, @HeaderMap Map<String, String> header);
}
