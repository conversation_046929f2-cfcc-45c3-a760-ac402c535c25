package com.facishare.paas.appframework.metadata.repository.annotation;

import com.facishare.paas.appframework.metadata.repository.fieldhandler.ObjectFieldType;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.Number;

import java.lang.annotation.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/8/11.
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@FieldType(type = IFieldType.CURRENCY)
public @interface CurrencyField {
    ObjectField field();

    int length() default 18;

    int decimalPlaces() default 2;

    int roundMode() default Number.ROUND_HALF_UP;

    ObjectFieldType fieldType() default ObjectFieldType.STRING;
}
