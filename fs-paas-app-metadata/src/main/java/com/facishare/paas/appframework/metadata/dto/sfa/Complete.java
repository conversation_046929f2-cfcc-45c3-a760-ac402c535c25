package com.facishare.paas.appframework.metadata.dto.sfa;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

public interface Complete {

    @Data
    @Builder
    class Arg {
        @SerializedName("entityID")
        @JSONField(name = "entityID")
        private String entityID;

        @SerializedName("dataID")
        @JSONField(name = "dataID")
        private String dataID;

        @SerializedName("triggerType")
        @JSONField(name = "triggerType")
        private String triggerType;

        @SerializedName("paasStatus")
        @JSONField(name = "paasStatus")
        private String paasStatus;

        @SerializedName("data")
        @JSONField(name = "data")
        private Object data;
    }

    @Getter
    @Setter
    class Result extends BaseResult {
        private Boolean value;
    }
}
