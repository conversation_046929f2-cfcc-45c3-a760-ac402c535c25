package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.GetPrintTemplate;
import com.facishare.paas.appframework.common.service.dto.PrintTemplate;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.rest.InnerHeaders;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.rest.core.codec.IRestCodeC;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.facishare.rest.core.exception.RestProxyRuntimeException;
import com.fasterxml.jackson.databind.JsonNode;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestResource(value = "CRMTemplate", desc = "打印模版服务", contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.PrintTemplateServiceProxy$Codec")
public interface PrintTemplateServiceProxy {
    String X_FS_EMPLOYEE_ID = "x-fs-employee-id";

    @POST(value = "/v1/inner/object/printTemplate/service/print", desc = "打印")
    PrintTemplate.Result print(@HeaderParam(InnerHeaders.TENANT_ID) String tenantId,
                               @HeaderParam(X_FS_EMPLOYEE_ID) String employeeId,
                               @HeaderParam(InnerHeaders.USER_ID) String userId,
                               @Body PrintTemplate.Arg arg);

    @POST(value = "/v1/inner/object/printTemplate/service/findPrintTemplate", desc = "查询打印模板")
    GetPrintTemplate.Result findPrintTemplate(@HeaderParam(InnerHeaders.TENANT_ID) String tenantId,
                                              @HeaderParam(X_FS_EMPLOYEE_ID) String employeeId,
                                              @HeaderParam(InnerHeaders.USER_ID) String userId,
                                              @Body GetPrintTemplate.Arg arg);


    class Codec implements IRestCodeC {
        @Override
        public <T> byte[] encodeArg(T obj) {
            if (Objects.isNull(obj)) {
                return null;
            } else {
                return obj instanceof String ? ((String) obj).getBytes(StandardCharsets.UTF_8) : JacksonUtils.toJson(obj).getBytes(StandardCharsets.UTF_8);
            }
        }

        @Override
        public <T> T decodeResult(int statusCode, Map<String, List<String>> headers, byte[] bytes, Class<T> clazz) {
            String bodyString = new String(bytes, StandardCharsets.UTF_8);
            //apiBus 网关超时
            if (statusCode == 504) {
                throw new RestProxyRuntimeException(statusCode, bodyString);
            }
            if (statusCode != 200) {
                throw new RuntimeException("decode error,body:" + bodyString);
            }
            JsonNode root = JacksonUtils.readTree(bodyString);
            int errCode = root.get("errCode").asInt();
            if (errCode != 0) {
                throw new RestProxyBusinessException(errCode, root.get("errMessage").asText());
            }
            return JacksonUtils.fromJson(root.get("result").toString(), clazz);
        }
    }
}
