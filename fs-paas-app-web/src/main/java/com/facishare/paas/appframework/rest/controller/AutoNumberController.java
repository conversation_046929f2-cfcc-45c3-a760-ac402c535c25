package com.facishare.paas.appframework.rest.controller;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.jaxrs.annotation.RestAPI;
import com.facishare.paas.appframework.metadata.AutoNumberLogicService;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.rest.dto.data.CalculateAutoNumber;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import java.util.List;
import java.util.Map;

/**
 * create by zhaoju on 2018/08/24
 */
@Controller
@Path("/v1/inner/rest/auto_number")
@Slf4j
@RestAPI
public class AutoNumberController {
    @Autowired
    private AutoNumberLogicService autoNumberLogicService;
    @Autowired
    private DescribeLogicService describeLogicService;

    @POST
    @Path("/calculate_auto_number_value")
    public Map calculateAutoNumberValue(Map<String, Object> data) {
        RequestContext context = RequestContextManager.getContext();
        IObjectData iObjectData = autoNumberLogicService.calculateAutoNumberValue(context.getTenantId(), data);
        // 返回ObjectData内部维护的Map
        return ((ObjectData) iObjectData).getContainerDocument();
    }

    @POST
    @Path("/calculate_auto_number_value/{apiName}")
    public CalculateAutoNumber.Result calculateAutoNumberValue(CalculateAutoNumber.Arg arg,
                                                               @PathParam("apiName") String apiName) {
        String tenantId = RequestContextManager.getContext().getTenantId();
        IObjectDescribe describe = describeLogicService.findObject(tenantId, apiName);
        try {
            List<IObjectData> dataList = arg.getObjectDataList();
            List<IObjectData> resultDataList = autoNumberLogicService.calculateAutoNumberValue(describe, dataList);
            return CalculateAutoNumber.Result.fromDataList(resultDataList);
        } catch (MetadataServiceException e) {
            log.warn("calculateAutoNumberValue error, tenantId:{}, describeApiName:{}", tenantId.replaceAll("[\r\n]", ""), apiName.replaceAll("[\r\n]", ""), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @POST
    @Path("/update_and_create_auto_number")
    public Result updateAndCreateAutoNumberField(Map<String, Object> describeMap) {
        IObjectDescribe describe = new ObjectDescribe(describeMap);
        String tenantId = describe.getTenantId();
        String apiName = describe.getApiName();
        IObjectDescribe oldDescribe = describeLogicService.findObject(tenantId, apiName);
        try {
            autoNumberLogicService.diffAutoNumberField(describe, oldDescribe);
            return Result.builder().apiName(apiName).build();
        } catch (MetadataServiceException e) {
            log.warn("saveObjectData error,tenantId:{},describeApiName:{}", tenantId, apiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Data
    @Builder
    private static class Result {
        @JsonProperty(value = "api_name")
        private String apiName;
    }

}
