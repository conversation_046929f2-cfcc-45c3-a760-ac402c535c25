package com.facishare.paas.appframework.metadata.dto.sfa;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by zhouwr on 2017/11/17
 */
public interface GetFunctionRights {
    @Data
    @Builder
    class Arg {
        @SerializedName("ObjectType")
        @JSONField(name = "ObjectType")
        private String objectType;
        @SerializedName("ObjectID")
        @J<PERSON>NField(name = "ObjectID")
        private String objectId;
    }

    @Getter
    @Setter
    class Result extends BaseResult {
        private List<FunctionRight> value = Lists.newArrayList();
    }

    @Data
    class FunctionRight {
        @SerializedName("FunctionNo")
        @JSONField(name = "FunctionNo")
        private int functionCode;
        @SerializedName("Right")
        @JSONField(name = "Right")
        private int right;

        private boolean hasPrivilege;
        private String actionCode;
        private String actionLabel;
        private String objectApiName;
    }

}
