package com.facishare.paas.appframework.core.model

import com.facishare.paas.appframework.core.predef.controller.StandardController
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController
import spock.lang.Specification

/**
 * Created by liyiguang on 2017/7/9.
 */
class ControllerClassLoaderImplTest extends Specification {
    def "test loadControllerClass"() {
        given:
        def classLoader = new ControllerClassLoaderImpl()
        when:
        def clazz = classLoader.loadControllerClass(controllerClassInfo)
        then:
        clazz == expectedClass
        where:
        controllerClassInfo                                                              || expectedClass
        new ControllerClassInfo(StandardController.Detail.controllerClassInfo.className) || StandardDetailController.class
    }
}
