package com.facishare.paas.appframework.privilege.util;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

import com.google.common.collect.Sets;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * GenerateByAI
 * 测试内容描述：测试FunctionPrivillegeConfig类的方法
 */
@ExtendWith(MockitoExtension.class)
class FunctionPrivillegeConfigJTest {

    // 移除静态初始化，改为在每个测试方法中使用MockedStatic

    /**
     * GenerateByAI
     * 测试内容描述：测试openGrayOld_master_detail_obj_api_names_enterpriseids方法
     */
    @ParameterizedTest
    @MethodSource("provideOpenGrayOldMasterDetailTestData")
    void testOpenGrayOld_master_detail_obj_api_names_enterpriseids(Set<String> setElements, String enterpriseId, boolean expected) {
        // 测试准备 - 设置测试用的静态字段oldMasterDetailObjApiNamesEnterpriseIdSet
        Set<String> originalSet = (Set<String>) ReflectionTestUtils.getField(FunctionPrivillegeConfig.class, "oldMasterDetailObjApiNamesEnterpriseIdSet");
        Set<String> testSet = new HashSet<>(setElements);
        ReflectionTestUtils.setField(FunctionPrivillegeConfig.class, "oldMasterDetailObjApiNamesEnterpriseIdSet", testSet);

        try {
            // 执行被测试方法
            boolean result = FunctionPrivillegeConfig.openGrayOld_master_detail_obj_api_names_enterpriseids(enterpriseId);

            // 结果验证
            assertEquals(expected, result);
        } finally {
            // 恢复原始值
            ReflectionTestUtils.setField(FunctionPrivillegeConfig.class, "oldMasterDetailObjApiNamesEnterpriseIdSet", originalSet);
        }
    }

    static Stream<Arguments> provideOpenGrayOldMasterDetailTestData() {
        return Stream.of(
            Arguments.of(Collections.emptySet(), "123", false),
            Arguments.of(Sets.newHashSet("123", "456"), "123", true),
            Arguments.of(Sets.newHashSet("123", "456"), "789", false),
            Arguments.of(Sets.newHashSet("*"), "any", true),
            Arguments.of(Sets.newHashSet("*", "123"), "456", true)
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试openSlaveUseMasterFunction方法
     */
    @ParameterizedTest
    @MethodSource("provideOpenSlaveUseMasterFunctionTestData")
    void testOpenSlaveUseMasterFunction(Set<String> setElements, String enterpriseId, boolean expected) {
        // 测试准备 - 设置测试用的静态字段slaveUseMasterFunctionEnterpriseIdSet
        Set<String> originalSet = (Set<String>) ReflectionTestUtils.getField(FunctionPrivillegeConfig.class, "slaveUseMasterFunctionEnterpriseIdSet");
        Set<String> testSet = new HashSet<>(setElements);
        ReflectionTestUtils.setField(FunctionPrivillegeConfig.class, "slaveUseMasterFunctionEnterpriseIdSet", testSet);

        try {
            // 执行被测试方法
            boolean result = FunctionPrivillegeConfig.openSlaveUseMasterFunction(enterpriseId);

            // 结果验证
            assertEquals(expected, result);
        } finally {
            // 恢复原始值
            ReflectionTestUtils.setField(FunctionPrivillegeConfig.class, "slaveUseMasterFunctionEnterpriseIdSet", originalSet);
        }
    }

    static Stream<Arguments> provideOpenSlaveUseMasterFunctionTestData() {
        return Stream.of(
            Arguments.of(Collections.emptySet(), "123", false),
            Arguments.of(Sets.newHashSet("123", "456"), "123", true),
            Arguments.of(Sets.newHashSet("123", "456"), "789", false),
            Arguments.of(Sets.newHashSet("*"), "any", true),
            Arguments.of(Sets.newHashSet("*", "123"), "456", true)
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试openPersonnelRoleEditableFunctionCodesGray方法
     */
    @ParameterizedTest
    @MethodSource("provideOpenPersonnelRoleEditableFunctionCodesGrayTestData")
    void testOpenPersonnelRoleEditableFunctionCodesGray(Set<String> setElements, String enterpriseId, boolean expected) {
        // 测试准备 - 设置测试用的静态字段personnelRoleEditableFunctionCodesGrayEnterpriseIds
        Set<String> originalSet = (Set<String>) ReflectionTestUtils.getField(FunctionPrivillegeConfig.class, "personnelRoleEditableFunctionCodesGrayEnterpriseIds");
        Set<String> testSet = new HashSet<>(setElements);
        ReflectionTestUtils.setField(FunctionPrivillegeConfig.class, "personnelRoleEditableFunctionCodesGrayEnterpriseIds", testSet);

        try {
            // 执行被测试方法
            boolean result = FunctionPrivillegeConfig.openPersonnelRoleEditableFunctionCodesGray(enterpriseId);

            // 结果验证
            assertEquals(expected, result);
        } finally {
            // 恢复原始值
            ReflectionTestUtils.setField(FunctionPrivillegeConfig.class, "personnelRoleEditableFunctionCodesGrayEnterpriseIds", originalSet);
        }
    }

    static Stream<Arguments> provideOpenPersonnelRoleEditableFunctionCodesGrayTestData() {
        return Stream.of(
            Arguments.of(Collections.emptySet(), "123", false),
            Arguments.of(Sets.newHashSet("123", "456"), "123", true),
            Arguments.of(Sets.newHashSet("123", "456"), "789", false),
            Arguments.of(Sets.newHashSet("*"), "any", true),
            Arguments.of(Sets.newHashSet("*", "123"), "456", true)
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getter方法，确保它们正常工作
     */
    @Test
    void testGetterMethods() {
        // 执行被测试方法
        List<String> slaveNoUseFunctionCodes = FunctionPrivillegeConfig.getSlaveNoUseFunctionCodes();
        Set<String> personnelRoleEditableFunctionCodes = FunctionPrivillegeConfig.getPersonnelRoleEditableFunctionCodes();
        Map<String, MasterSlaveFunction> slaveMap = FunctionPrivillegeConfig.getSlaveMap();

        // 结果验证
        assertNotNull(slaveNoUseFunctionCodes);
        assertNotNull(personnelRoleEditableFunctionCodes);
        assertNotNull(slaveMap);
    }
} 