<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <import resource="classpath:spring/common.xml"/>
    <import resource="classpath:spring/fs-paas-auth-client.xml"/>
    <import resource="classpath:enterpriserelation2/enterpriserelation.xml"/>

    <bean id="functionPrivilegeProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.privilege.FunctionPrivilegeProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="feedPrivilegeProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.privilege.FeedPrivilegeProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="dataPrivilegeProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.privilege.DataPrivilegeProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="dataAuthServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.privilege.DataAuthServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="userRoleInfoProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.privilege.UserRoleInfoProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="fieldPrivilegeProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.privilege.FieldPrivilegeProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="roleProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.privilege.RoleProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>


    <bean id="outDataPrivilegeProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.privilege.OutDataPrivilegeProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="dataAuthProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.privilege.DataAuthProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="dataPrivilegeProviderProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.privilege.model.DataPrivilegeProviderProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="functionPrivilegeProviderProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProviderProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>
    <!--    <bean id="enterpriseRetrofitFactory" class="com.github.zhxing.retrofitspring.fxiaoke.ConfigRetrofitSpringFactory"-->
    <!--          p:configNames="fs-enterpriserelation-rest-api" init-method="init">-->
    <!--        <property name="headers">-->
    <!--            <map>-->
    <!--                <entry key="x-eip-appid" value="x_app_framework"/>-->
    <!--            </map>-->
    <!--        </property>-->
    <!--    </bean>-->
    <!--    <bean class="com.github.zhxing.retrofitspring.RetrofitSpringFactoryBean"-->
    <!--          p:type="com.fxiaoke.enterpriserelation.service.EnterpriseRelationService"-->
    <!--          p:factory-ref="enterpriseRetrofitFactory"/>-->
    <!--    <bean class="com.github.zhxing.retrofitspring.RetrofitSpringFactoryBean"-->
    <!--          p:type="com.fxiaoke.enterpriserelation.service.FxiaokeAccountService"-->
    <!--          p:factory-ref="enterpriseRetrofitFactory"/>-->
    <!--    <bean class="com.github.zhxing.retrofitspring.RetrofitSpringFactoryBean"-->
    <!--          p:type="com.fxiaoke.enterpriserelation.service.PublicEmployeeService"-->
    <!--          p:factory-ref="enterpriseRetrofitFactory"/>-->
    <!--    <bean class="com.github.zhxing.retrofitspring.RetrofitSpringFactoryBean"-->
    <!--          p:type="com.fxiaoke.enterpriserelation.service.AuthService" p:factory-ref="enterpriseRetrofitFactory"/>-->

    <import resource="privilege-dubbo.xml"/>


</beans>
