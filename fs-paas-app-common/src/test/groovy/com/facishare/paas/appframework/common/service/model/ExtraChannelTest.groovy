package com.facishare.paas.appframework.common.service.model

import spock.lang.Specification

/**
 * GenerateByAI
 * 测试内容描述：ExtraChannel类的单元测试
 */
class ExtraChannelTest extends Specification {

    def "test default constructor"() {
        when: "创建ExtraChannel实例"
        def extraChannel = new ExtraChannel()

        then: "默认值应该为0和null"
        extraChannel.outChannelType == 0
        extraChannel.appId == null
        extraChannel.receiverChannelType == 0
    }

    def "test getter and setter methods for outChannelType"() {
        given: "创建ExtraChannel实例"
        def extraChannel = new ExtraChannel()

        when: "设置outChannelType"
        extraChannel.setOutChannelType(4)

        then: "getter方法应该返回正确的值"
        extraChannel.getOutChannelType() == 4
    }

    def "test getter and setter methods for appId"() {
        given: "创建ExtraChannel实例"
        def extraChannel = new ExtraChannel()

        when: "设置appId"
        extraChannel.setAppId("testApp")

        then: "getter方法应该返回正确的值"
        extraChannel.getAppId() == "testApp"
    }

    def "test getter and setter methods for receiverChannelType"() {
        given: "创建ExtraChannel实例"
        def extraChannel = new ExtraChannel()

        when: "设置receiverChannelType"
        extraChannel.setReceiverChannelType(6)

        then: "getter方法应该返回正确的值"
        extraChannel.getReceiverChannelType() == 6
    }

    def "test setting all properties"() {
        given: "创建ExtraChannel实例"
        def extraChannel = new ExtraChannel()

        when: "设置所有属性"
        extraChannel.setOutChannelType(4)
        extraChannel.setAppId("myApp")
        extraChannel.setReceiverChannelType(6)

        then: "所有getter方法应该返回正确的值"
        extraChannel.getOutChannelType() == 4
        extraChannel.getAppId() == "myApp"
        extraChannel.getReceiverChannelType() == 6
    }

    def "test setting null appId"() {
        given: "创建ExtraChannel实例"
        def extraChannel = new ExtraChannel()

        when: "设置null appId"
        extraChannel.setAppId(null)

        then: "应该能够设置null值"
        extraChannel.getAppId() == null
    }

    def "test setting empty appId"() {
        given: "创建ExtraChannel实例"
        def extraChannel = new ExtraChannel()

        when: "设置空字符串appId"
        extraChannel.setAppId("")

        then: "应该能够设置空字符串"
        extraChannel.getAppId() == ""
    }

    def "test setting negative channel types"() {
        given: "创建ExtraChannel实例"
        def extraChannel = new ExtraChannel()

        when: "设置负数的channel类型"
        extraChannel.setOutChannelType(-1)
        extraChannel.setReceiverChannelType(-2)

        then: "应该能够设置负数值"
        extraChannel.getOutChannelType() == -1
        extraChannel.getReceiverChannelType() == -2
    }

    def "test toString method with all properties set"() {
        given: "创建ExtraChannel实例并设置所有属性"
        def extraChannel = new ExtraChannel()
        extraChannel.setOutChannelType(4)
        extraChannel.setAppId("testApp")
        extraChannel.setReceiverChannelType(6)

        when: "调用toString方法"
        def result = extraChannel.toString()

        then: "应该返回正确的JSON格式字符串"
        result == '{"outChannelType":4,"appId":"testApp","receiverChannelType":6}'
    }

    def "test toString method with null appId"() {
        given: "创建ExtraChannel实例，appId为null"
        def extraChannel = new ExtraChannel()
        extraChannel.setOutChannelType(1)
        extraChannel.setAppId(null)
        extraChannel.setReceiverChannelType(2)

        when: "调用toString方法"
        def result = extraChannel.toString()

        then: "应该返回包含null的JSON格式字符串"
        result == '{"outChannelType":1,"appId":"null","receiverChannelType":2}'
    }

    def "test toString method with empty appId"() {
        given: "创建ExtraChannel实例，appId为空字符串"
        def extraChannel = new ExtraChannel()
        extraChannel.setOutChannelType(3)
        extraChannel.setAppId("")
        extraChannel.setReceiverChannelType(5)

        when: "调用toString方法"
        def result = extraChannel.toString()

        then: "应该返回包含空字符串的JSON格式字符串"
        result == '{"outChannelType":3,"appId":"","receiverChannelType":5}'
    }

    def "test toString method with default values"() {
        given: "创建ExtraChannel实例，使用默认值"
        def extraChannel = new ExtraChannel()

        when: "调用toString方法"
        def result = extraChannel.toString()

        then: "应该返回默认值的JSON格式字符串"
        result == '{"outChannelType":0,"appId":"null","receiverChannelType":0}'
    }

    def "test toString method with special characters in appId"() {
        given: "创建ExtraChannel实例，appId包含特殊字符"
        def extraChannel = new ExtraChannel()
        extraChannel.setOutChannelType(1)
        extraChannel.setAppId("app\"with\\quotes")
        extraChannel.setReceiverChannelType(2)

        when: "调用toString方法"
        def result = extraChannel.toString()

        then: "应该返回包含特殊字符的JSON格式字符串"
        result == '{"outChannelType":1,"appId":"app"with\\quotes","receiverChannelType":2}'
    }

    def "test multiple property updates"() {
        given: "创建ExtraChannel实例"
        def extraChannel = new ExtraChannel()

        when: "多次更新属性"
        extraChannel.setOutChannelType(1)
        extraChannel.setAppId("firstApp")
        extraChannel.setReceiverChannelType(2)

        then: "第一次设置的值应该正确"
        extraChannel.getOutChannelType() == 1
        extraChannel.getAppId() == "firstApp"
        extraChannel.getReceiverChannelType() == 2

        when: "再次更新属性"
        extraChannel.setOutChannelType(3)
        extraChannel.setAppId("secondApp")
        extraChannel.setReceiverChannelType(4)

        then: "更新后的值应该正确"
        extraChannel.getOutChannelType() == 3
        extraChannel.getAppId() == "secondApp"
        extraChannel.getReceiverChannelType() == 4
    }

    def "test large integer values for channel types"() {
        given: "创建ExtraChannel实例"
        def extraChannel = new ExtraChannel()

        when: "设置大整数值"
        extraChannel.setOutChannelType(Integer.MAX_VALUE)
        extraChannel.setReceiverChannelType(Integer.MIN_VALUE)

        then: "应该能够正确设置和获取大整数值"
        extraChannel.getOutChannelType() == Integer.MAX_VALUE
        extraChannel.getReceiverChannelType() == Integer.MIN_VALUE
    }
}
