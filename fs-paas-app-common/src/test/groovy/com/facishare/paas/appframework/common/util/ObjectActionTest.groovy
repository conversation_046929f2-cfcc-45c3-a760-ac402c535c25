package com.facishare.paas.appframework.common.util

import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：ObjectAction枚举类的单元测试
 */
class ObjectActionTest extends Specification {

    def "test basic action codes exist"() {
        expect: "验证基本操作码存在"
        ObjectAction.DELETE != null
        ObjectAction.VIEW_DETAIL != null
        ObjectAction.VIEW_LIST != null
        ObjectAction.CREATE != null
        ObjectAction.UPDATE != null
        ObjectAction.BATCH_IMPORT != null
        ObjectAction.BATCH_EXPORT != null
        ObjectAction.INVALID != null
        ObjectAction.RECOVER != null
        ObjectAction.PRINT != null
    }

    @Unroll
    def "test getActionCode method returns correct value for #action"() {
        expect: "验证getActionCode方法返回正确的操作码"
        action.getActionCode() == expectedCode

        where:
        action                      | expectedCode
        ObjectAction.DELETE         | "Delete"
        ObjectAction.VIEW_DETAIL    | "View"
        ObjectAction.VIEW_LIST      | "List"
        ObjectAction.CREATE         | "Add"
        ObjectAction.UPDATE         | "Edit"
        ObjectAction.BATCH_IMPORT   | "Import"
        ObjectAction.BATCH_EXPORT   | "Export"
        ObjectAction.INVALID        | "Abolish"
        ObjectAction.RECOVER        | "Recover"
        ObjectAction.PRINT          | "Print"
        ObjectAction.LOCK           | "Lock"
        ObjectAction.UNLOCK         | "Unlock"
    }

    @Unroll
    def "test getI18NKey method returns correct value for #action"() {
        expect: "验证getI18NKey方法返回正确的国际化键"
        action.getI18NKey() != null
        action.getI18NKey().length() > 0

        where:
        action << [
            ObjectAction.DELETE,
            ObjectAction.VIEW_DETAIL,
            ObjectAction.CREATE,
            ObjectAction.UPDATE,
            ObjectAction.INVALID,
            ObjectAction.RECOVER
        ]
    }

    def "test getActionLabel method returns non-null values"() {
        when: "调用getActionLabel方法"
        def labels = ObjectAction.values().collect { it.getActionLabel() }

        then: "所有标签都不应该为null"
        labels.every { it != null }
        labels.every { it.length() > 0 }
    }

    @Unroll
    def "test getButtonApiName method for actions with button API names"() {
        expect: "验证有按钮API名称的操作"
        action.getButtonApiName() == expectedButtonApiName

        where:
        action                      | expectedButtonApiName
        ObjectAction.DELETE         | "Delete_button_default"
        ObjectAction.CREATE         | "Add_button_default"
        ObjectAction.UPDATE         | "Edit_button_default"
        ObjectAction.INVALID        | "Abolish_button_default"
        ObjectAction.RECOVER        | "Recover_button_default"
        ObjectAction.PRINT          | "Print_button_default"
        ObjectAction.LOCK           | "Lock_button_default"
        ObjectAction.UNLOCK         | "Unlock_button_default"
    }

    def "test actions without button API names return null"() {
        expect: "验证没有按钮API名称的操作返回null"
        ObjectAction.VIEW_DETAIL.getButtonApiName() == null
        ObjectAction.VIEW_LIST.getButtonApiName() == null
        ObjectAction.BATCH_IMPORT.getButtonApiName() == null
        ObjectAction.BATCH_EXPORT.getButtonApiName() == null
    }

    def "test enum values method returns all values"() {
        when: "调用values方法"
        def values = ObjectAction.values()

        then: "应该返回大量枚举值"
        values.length > 100  // ObjectAction有很多枚举值
        values.contains(ObjectAction.DELETE)
        values.contains(ObjectAction.CREATE)
        values.contains(ObjectAction.UPDATE)
        values.contains(ObjectAction.INVALID)
    }

    def "test valueOf string method works correctly"() {
        expect: "验证字符串valueOf方法正常工作"
        ObjectAction.valueOf("DELETE") == ObjectAction.DELETE
        ObjectAction.valueOf("CREATE") == ObjectAction.CREATE
        ObjectAction.valueOf("UPDATE") == ObjectAction.UPDATE
        ObjectAction.valueOf("INVALID") == ObjectAction.INVALID
    }

    def "test valueOf string with invalid name throws exception"() {
        when: "使用无效的枚举名称"
        ObjectAction.valueOf("INVALID_ACTION_CODE")

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test action codes are unique"() {
        given: "获取所有操作码"
        def allActionCodes = ObjectAction.values().collect { it.getActionCode() }

        when: "检查唯一性"
        def uniqueActionCodes = allActionCodes.toSet()

        then: "所有操作码应该是唯一的"
        uniqueActionCodes.size() == allActionCodes.size()
    }

    def "test button API names are unique when not null"() {
        given: "获取所有非null的按钮API名称"
        def allButtonApiNames = ObjectAction.values()
                .collect { it.getButtonApiName() }
                .findAll { it != null }

        when: "检查唯一性"
        def uniqueButtonApiNames = allButtonApiNames.toSet()

        then: "所有按钮API名称应该是唯一的"
        uniqueButtonApiNames.size() == allButtonApiNames.size()
    }

    def "test team member related actions"() {
        expect: "验证团队成员相关操作"
        ObjectAction.ADD_TEAM_MEMBER.getActionCode() == "AddTeamMember"
        ObjectAction.EDIT_TEAM_MEMBER.getActionCode() == "EditTeamMember"
        ObjectAction.DELETE_TEAM_MEMBER.getActionCode() == "DeleteTeamMember"
        ObjectAction.CHANGE_OWNER.getActionCode() == "ChangeOwner"
    }

    def "test BPM related actions"() {
        expect: "验证业务流程相关操作"
        ObjectAction.START_BPM.getActionCode() == "StartBPM"
        ObjectAction.VIEW_ENTIRE_BPM.getActionCode() == "ViewEntireBPM"
        ObjectAction.STOP_BPM.getActionCode() == "StopBPM"
        ObjectAction.CHANGE_BPM_APPROVER.getActionCode() == "ChangeBPMApprover"
    }

    def "test sign in/out actions"() {
        expect: "验证签到签退操作"
        ObjectAction.SIGN_IN.getActionCode() == "SignIn"
        ObjectAction.SIGN_OUT.getActionCode() == "SignOut"
    }

    def "test bulk operations"() {
        expect: "验证批量操作"
        ObjectAction.BULK_DELETE.getActionCode() == "BulkDelete"
        ObjectAction.BULK_INVALID.getActionCode() == "BulkInvalid"
        ObjectAction.BULK_RECOVER.getActionCode() == "BulkRecover"
        ObjectAction.BULK_RELATE.getActionCode() == "BulkRelate"
        ObjectAction.BULK_DISRELATE.getActionCode() == "BulkDisRelate"
    }

    def "test communication actions"() {
        expect: "验证通信相关操作"
        ObjectAction.DIAL.getActionCode() == "Dial"
        ObjectAction.SEND_MAIL.getActionCode() == "SendMail"
        ObjectAction.DISCUSS.getActionCode() == "Discuss"
        ObjectAction.SCHEDULE.getActionCode() == "Schedule"
        ObjectAction.REMIND.getActionCode() == "Remind"
    }

    def "test special actions with custom getActionLabel"() {
        expect: "验证有自定义getActionLabel方法的操作"
        ObjectAction.CREATE_SAVE_CREATE_CONTACT.getActionLabel() != null
        ObjectAction.CREATE_SAVE_CREATE_PROJECT_STAGE.getActionLabel() != null
        ObjectAction.AddCampaignMembers.getActionLabel() != null
    }

    def "test toString method returns enum name"() {
        expect: "验证toString方法返回枚举名称"
        ObjectAction.DELETE.toString() == "DELETE"
        ObjectAction.CREATE.toString() == "CREATE"
        ObjectAction.UPDATE.toString() == "UPDATE"
        ObjectAction.INVALID.toString() == "INVALID"
    }

    def "test name method returns correct names"() {
        expect: "验证name方法返回正确的枚举名称"
        ObjectAction.DELETE.name() == "DELETE"
        ObjectAction.VIEW_DETAIL.name() == "VIEW_DETAIL"
        ObjectAction.CREATE.name() == "CREATE"
        ObjectAction.UPDATE.name() == "UPDATE"
        ObjectAction.BATCH_IMPORT.name() == "BATCH_IMPORT"
    }

    def "test action categories"() {
        given: "不同类别的操作"
        def crudActions = [ObjectAction.CREATE, ObjectAction.VIEW_DETAIL, ObjectAction.UPDATE, ObjectAction.DELETE]
        def batchActions = [ObjectAction.BATCH_IMPORT, ObjectAction.BATCH_EXPORT, ObjectAction.BULK_DELETE]
        def workflowActions = [ObjectAction.START_BPM, ObjectAction.STOP_BPM, ObjectAction.INVALID, ObjectAction.RECOVER]

        expect: "所有操作都有有效的操作码和标签"
        crudActions.every { it.getActionCode() != null && it.getActionLabel() != null }
        batchActions.every { it.getActionCode() != null && it.getActionLabel() != null }
        workflowActions.every { it.getActionCode() != null && it.getActionLabel() != null }
    }

    def "test action code patterns"() {
        given: "获取所有操作码"
        def actionCodes = ObjectAction.values().collect { it.getActionCode() }

        expect: "操作码应该遵循命名规范"
        actionCodes.every { code ->
            // 操作码不应该为空
            code != null && code.length() > 0
            // 操作码应该是有效的标识符格式
            code.matches(/^[A-Za-z][A-Za-z0-9_]*$/)
        }
    }

    def "test button API name patterns"() {
        given: "获取所有非null的按钮API名称"
        def buttonApiNames = ObjectAction.values()
                .collect { it.getButtonApiName() }
                .findAll { it != null }

        expect: "按钮API名称应该遵循命名规范"
        buttonApiNames.every { name ->
            // 按钮API名称应该以_button结尾或包含button
            name.contains("button") || name.contains("Button")
        }
    }
}
