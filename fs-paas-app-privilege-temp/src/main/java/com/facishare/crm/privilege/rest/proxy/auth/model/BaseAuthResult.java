package com.facishare.crm.privilege.rest.proxy.auth.model;

import com.facishare.rest.core.util.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 调用底层rest服务的返回结果结构,适用于解码器:RestResponseAuthCodeC
 * 底层REST服务的BaseContext。
 * Type1类型:海波团队REST接口的Context。
 * Created by yusb on 17/4/13.
 */

@Slf4j
@Data
public class BaseAuthResult {
  protected int errCode;//错误码0 || 200是正常的
  protected String errKey;//错误码英文说明
  protected String errMessage;//错误信息（由业务端生成）
  protected String errDescription;//错误码中文说明

  public boolean isSuccess() {
    return errCode == 0 || errCode == 200;
  }

  @Override
  public String toString() {
    return JsonUtil.toJson(this);
  }
}
