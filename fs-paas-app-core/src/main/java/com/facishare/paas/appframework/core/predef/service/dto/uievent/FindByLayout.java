package com.facishare.paas.appframework.core.predef.service.dto.uievent;

import com.facishare.paas.appframework.core.model.UIEventDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface FindByLayout {
    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        private String describeApiName;
        private String layoutApiName;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private List<UIEventDocument> events;
    }
}
