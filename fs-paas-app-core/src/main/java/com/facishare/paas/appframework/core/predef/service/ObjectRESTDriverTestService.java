package com.facishare.paas.appframework.core.predef.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISearchQuery;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.SearchQuery;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 对象描述服务接口
 * <p>
 * Created by liyiguang on 2017/6/17.
 */
@ServiceModule("object_data_rest")
@Component
public class ObjectRESTDriverTestService {

    @Resource(
            name = "objectDataService"
    )
    IObjectDataService dataService;

    /**
     * 创建对象业务类型
     *
     * @param arg
     * @return
     */
    @ServiceMethod("findbysearchquery")
    public Object findbysearchquery(Object arg, ServiceContext context) {

        ISearchQuery searchQuery = SearchQuery.fromJsonString(JSON.toJSONString(arg));
        ActionContext actionContext = new ActionContext();
        actionContext.setEnterpriseId(context.getTenantId());
        actionContext.setPrivilegeCheck(false);
        actionContext.setUserId(context.getUser().getUserId());

        try {
            QueryResult<IObjectData> result = dataService.findBySearchQuery(searchQuery, context.getTenantId(), "AccountObj", actionContext);
            return JSONObject.toJSONString(result);
        } catch (MetadataServiceException e) {
        }

        return null;
    }
}
