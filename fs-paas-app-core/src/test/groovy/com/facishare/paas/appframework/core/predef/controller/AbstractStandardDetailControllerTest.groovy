package com.facishare.paas.appframework.core.predef.controller


import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.metadata.ObjectDescribeExt
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe
import com.facishare.paas.appframework.metadata.repository.model.GdprCompliance
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.LayoutRuleInfo
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.ui.layout.Layout
import com.facishare.paas.metadata.ui.layout.ILayout
import com.facishare.rest.core.util.JsonUtil
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.Maps
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class AbstractStandardDetailControllerTest extends Specification {

    String describeJson = "{\"tenant_id\":\"74255\",\"package\":\"CRM\",\"is_active\":true,\"last_modified_time\":1708668610478,\"create_time\":1582532106516,\"description\":\"\",\"last_modified_by\":\"1000\",\"display_name\":\"lihh-对象A\",\"created_by\":\"1000\",\"version\":221,\"is_open_display_name\":true,\"index_version\":200,\"icon_index\":0,\"is_deleted\":false,\"api_name\":\"object_Zpv1K__c\",\"icon_path\":\"\",\"is_udef\":true,\"define_type\":\"custom\",\"_id\":\"5e53860a2535410001044fc6\",\"fields\":{\"field_01Y2e__c\":{\"return_type\":\"currency\",\"describe_api_name\":\"object_Zpv1K__c\",\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"count\",\"decimal_places\":5,\"sub_object_describe_apiname\":\"object_pgQ8s__c\",\"is_required\":false,\"wheres\":[],\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"d_1\",\"field_api_name\":\"field_plLYn__c\",\"is_index\":true,\"default_result\":\"d_null\",\"is_active\":true,\"create_time\":1582770039731,\"is_encrypted\":false,\"count_type\":\"sum\",\"default_value\":\"\",\"count_field_api_name\":\"field_l1D1s__c\",\"label\":\"统计字段\",\"field_num\":38,\"count_to_zero\":false,\"api_name\":\"field_01Y2e__c\",\"count_field_type\":\"currency\",\"_id\":\"5e5727783d92600001ffc89c\",\"is_index_field\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\"},\"lock_rule\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1582603785346,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"锁定规则\",\"is_unique\":false,\"rules\":[],\"default_value\":\"default_lock_rule\",\"label\":\"锁定规则\",\"type\":\"lock_rule\",\"field_num\":1,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_rule\",\"define_type\":\"package\",\"_id\":\"5e549e0993b821000171a10d\",\"is_single\":false,\"label_r\":\"锁定规则\",\"is_index_field\":false,\"index_name\":\"s_8\",\"help_text\":\"\",\"status\":\"new\"},\"field_x7p5l__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1582622856192,\"is_encrypted\":false,\"is_support_town\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"group_type\":\"area\",\"label\":\"地区定位\",\"type\":\"group\",\"is_required\":false,\"api_name\":\"field_x7p5l__c\",\"define_type\":\"custom\",\"_id\":\"5e54e888cc86e9000167a32a\",\"is_single\":false,\"is_index_field\":false,\"fields\":{\"area_country\":\"field_FziHr__c\",\"area_location\":\"field_azdvs__c\",\"area_detail_address\":\"field_AN55g__c\",\"area_city\":\"field_54SvN__c\",\"area_province\":\"field_Qy63Q__c\",\"area_district\":\"field_rPr65__c\"},\"index_name\":\"s_7\",\"help_text\":\"\",\"status\":\"new\"},\"field_FziHr__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1582622856186,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"国家\",\"type\":\"country\",\"field_num\":27,\"used_in\":\"component\",\"is_required\":false,\"api_name\":\"field_FziHr__c\",\"options\":[],\"define_type\":\"custom\",\"_id\":\"5e54e888cc86e9000167a32c\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"s_4\",\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"mc_exchange_rate\":{\"describe_api_name\":\"object_Zpv1K__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"number\",\"decimal_places\":6,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"汇率\",\"index_name\":\"d_3\",\"max_length\":16,\"is_index\":true,\"is_active\":true,\"create_time\":1609299257452,\"is_encrypted\":false,\"length\":10,\"default_value\":\"\",\"label\":\"汇率\",\"api_name\":\"mc_exchange_rate\",\"_id\":\"5febf5396268f90001d34f5f\",\"is_index_field\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\"},\"life_status_before_invalid\":{\"describe_api_name\":\"object_Zpv1K__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"description\":\"作废前生命状态\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"label_r\":\"作废前生命状态\",\"index_name\":\"t_2\",\"max_length\":256,\"is_index\":false,\"is_active\":true,\"create_time\":1582603785344,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"作废前生命状态\",\"field_num\":4,\"is_need_convert\":false,\"api_name\":\"life_status_before_invalid\",\"_id\":\"5e549e0993b821000171a110\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\"},\"field_54SvN__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"city\",\"used_in\":\"component\",\"is_required\":false,\"options\":[],\"define_type\":\"custom\",\"is_single\":false,\"cascade_parent_api_name\":\"field_Qy63Q__c\",\"index_name\":\"s_3\",\"is_index\":true,\"is_active\":true,\"create_time\":1582622856188,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"市\",\"field_num\":29,\"api_name\":\"field_54SvN__c\",\"_id\":\"5e54e888cc86e9000167a32f\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"owner_department\":{\"describe_api_name\":\"object_Zpv1K__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":true,\"label_r\":\"负责人主属部门\",\"index_name\":\"owner_dept\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1582532106514,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"负责人主属部门\",\"is_need_convert\":false,\"api_name\":\"owner_department\",\"_id\":\"5e53860a2535410001044fcd\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\"},\"field_TsklH__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"custom\",\"input_mode\":\"\",\"is_single\":false,\"index_name\":\"t_3\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1582618158170,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"单行文本\",\"field_num\":14,\"api_name\":\"field_TsklH__c\",\"_id\":\"5e54d62e93b821000172f6c7\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\"},\"lock_status\":{\"describe_api_name\":\"object_Zpv1K__c\",\"auto_adapt_places\":false,\"description\":\"锁定状态\",\"is_unique\":false,\"type\":\"select_one\",\"is_required\":false,\"options\":[{\"font_color\":\"#2a304d\",\"label\":\"未锁定\",\"value\":\"0\"},{\"font_color\":\"#2a304d\",\"label\":\"锁定\",\"value\":\"1\"}],\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"锁定状态\",\"index_name\":\"s_10\",\"is_index\":true,\"is_active\":true,\"create_time\":1582532106488,\"is_encrypted\":false,\"default_value\":\"0\",\"label\":\"锁定状态\",\"field_num\":2,\"is_need_convert\":false,\"api_name\":\"lock_status\",\"_id\":\"5e53860a2535410001044fc9\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"package\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1582532106516,\"pattern\":\"\",\"is_unique\":false,\"description\":\"package\",\"label\":\"package\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"package\",\"define_type\":\"system\",\"index_name\":\"pkg\",\"max_length\":200,\"status\":\"released\"},\"create_time\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":true,\"create_time\":1582532106516,\"is_unique\":false,\"description\":\"create_time\",\"label\":\"创建时间\",\"type\":\"date_time\",\"time_zone\":\"\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"create_time\",\"define_type\":\"system\",\"date_format\":\"yyyy-MM-ddHH:mm:ss\",\"index_name\":\"crt_time\",\"status\":\"released\"},\"field_FW6R7__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1582626435840,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"^\\\\w+([-+.]*\\\\w+)*@\\\\w+([-.]\\\\w+)*\\\\.\\\\w+([-.]\\\\w+)*\$\",\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"邮箱-掩码字段管理\",\"type\":\"email\",\"field_num\":36,\"is_required\":false,\"api_name\":\"field_FW6R7__c\",\"define_type\":\"custom\",\"_id\":\"5e54f683cc86e9000167c267\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"w_2\",\"is_show_mask\":true,\"help_text\":\"\",\"status\":\"new\"},\"display_name\":{\"expression_type\":\"js\",\"return_type\":\"text\",\"describe_api_name\":\"object_Zpv1K__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":true,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"index_name\":\"t_5\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"expression\":\"\",\"create_time\":1626316025937,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"显示字段\",\"api_name\":\"display_name\",\"_id\":\"60ef9cfa23cb7c0001319f04\",\"is_index_field\":false,\"help_text\":\"aaa\",\"status\":\"new\"},\"version\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":false,\"create_time\":1582532106516,\"length\":8,\"is_unique\":false,\"description\":\"version\",\"label\":\"version\",\"type\":\"number\",\"decimal_places\":0,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"version\",\"define_type\":\"system\",\"index_name\":\"version\",\"round_mode\":4,\"status\":\"released\"},\"created_by\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1582532106516,\"is_unique\":false,\"label\":\"创建人\",\"type\":\"employee\",\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"created_by\",\"define_type\":\"system\",\"is_single\":true,\"index_name\":\"crt_by\",\"status\":\"released\",\"description\":\"\"},\"relevant_team\":{\"describe_api_name\":\"object_Zpv1K__c\",\"embedded_fields\":{\"teamMemberEmployee\":{\"is_index\":true,\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"teamMemberEmployee\",\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员员工\",\"label\":\"成员员工\",\"type\":\"employee\",\"is_single\":true,\"help_text\":\"成员员工\"},\"teamMemberRole\":{\"is_index\":true,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"teamMemberRole\",\"options\":[{\"label\":\"负责人\",\"value\":\"1\"},{\"label\":\"普通成员\",\"value\":\"4\"}],\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员角色\",\"label\":\"成员角色\",\"type\":\"select_one\",\"help_text\":\"成员角色\"},\"teamMemberPermissionType\":{\"is_index\":true,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"teamMemberPermissionType\",\"options\":[{\"label\":\"只读\",\"value\":\"1\"},{\"label\":\"读写\",\"value\":\"2\"}],\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员权限类型\",\"label\":\"成员权限类型\",\"type\":\"select_one\",\"help_text\":\"成员权限类型\"}},\"is_index\":true,\"is_active\":true,\"create_time\":1582603785384,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"相关团队\",\"type\":\"embedded_object_list\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"relevant_team\",\"define_type\":\"package\",\"_id\":\"5e549e0993b821000171a10f\",\"is_single\":false,\"label_r\":\"相关团队\",\"is_index_field\":false,\"index_name\":\"a_team\",\"help_text\":\"相关团队\",\"status\":\"new\"},\"field_c2eIW__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"used_in\":\"component\",\"is_required\":false,\"define_type\":\"custom\",\"input_mode\":\"\",\"is_single\":false,\"index_name\":\"t_7\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1582620124795,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"收款方式\",\"field_num\":20,\"api_name\":\"field_c2eIW__c\",\"_id\":\"5e54dddd93b8210001730ac1\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\"},\"data_own_department\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1582532106516,\"is_unique\":false,\"label\":\"归属部门\",\"type\":\"department\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"data_own_department\",\"define_type\":\"package\",\"is_single\":true,\"index_name\":\"data_owner_dept_id\",\"status\":\"released\",\"description\":\"\"},\"field_bB0G5__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"date_time\",\"default_to_zero\":false,\"used_in\":\"component\",\"is_required\":false,\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"l_1\",\"is_index\":true,\"is_active\":true,\"create_time\":1582620124797,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"收款时间\",\"time_zone\":\"GMT+8\",\"field_num\":22,\"api_name\":\"field_bB0G5__c\",\"date_format\":\"yyyy-MM-ddHH:mm:ss\",\"_id\":\"5e54dddd93b8210001730ac2\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\"},\"name\":{\"describe_api_name\":\"object_Zpv1K__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"description\":\"name\",\"is_unique\":true,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":true,\"define_type\":\"system\",\"input_mode\":\"\",\"is_single\":false,\"label_r\":\"主属性\",\"index_name\":\"name\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1582532106716,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"主属性\",\"api_name\":\"name\",\"_id\":\"5e53860a2535410001044fcc\",\"is_index_field\":false,\"help_text\":\"test\",\"status\":\"new\"},\"_id\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1582532106516,\"pattern\":\"\",\"is_unique\":false,\"description\":\"_id\",\"label\":\"_id\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"_id\",\"define_type\":\"system\",\"index_name\":\"_id\",\"max_length\":200,\"status\":\"released\"},\"tenant_id\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1582532106516,\"pattern\":\"\",\"is_unique\":false,\"description\":\"tenant_id\",\"label\":\"tenant_id\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"tenant_id\",\"define_type\":\"system\",\"index_name\":\"ei\",\"max_length\":200,\"status\":\"released\"},\"data_own_organization\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1647329867032,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"label\":\"归属组织\",\"type\":\"department\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"data_own_organization\",\"define_type\":\"package\",\"_id\":\"6230424b04a720000162e488\",\"is_single\":true,\"label_r\":\"归属组织\",\"is_index_field\":false,\"index_name\":\"a_4\",\"status\":\"released\",\"description\":\"\"},\"field_azdvs__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1582622856191,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"定位\",\"is_geo_index\":false,\"type\":\"location\",\"field_num\":32,\"used_in\":\"component\",\"is_required\":false,\"api_name\":\"field_azdvs__c\",\"define_type\":\"custom\",\"_id\":\"5e54e888cc86e9000167a32b\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"t_1\",\"help_text\":\"\",\"status\":\"new\"},\"field_KvOh8__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"type\":\"currency\",\"decimal_places\":2,\"default_to_zero\":true,\"used_in\":\"component\",\"is_required\":false,\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"d_2\",\"max_length\":14,\"is_index\":true,\"is_active\":true,\"create_time\":1582620124794,\"is_encrypted\":false,\"length\":12,\"default_value\":\"\",\"label\":\"收款金额\",\"currency_unit\":\"￥\",\"field_num\":19,\"api_name\":\"field_KvOh8__c\",\"_id\":\"5e54dddc93b8210001730abe\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\"},\"field_q8w5p__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"file_source\":[],\"type\":\"image\",\"is_required\":false,\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"a_3\",\"support_file_types\":[\"jpg\",\"gif\",\"jpeg\",\"png\"],\"is_index\":true,\"file_amount_limit\":1,\"is_active\":true,\"watermark\":[{\"type\":\"variable\",\"value\":\"current_user\"},{\"type\":\"variable\",\"value\":\"current_time\"},{\"type\":\"variable\",\"value\":\"current_address\"}],\"create_time\":1583813196171,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"图片\",\"is_watermark\":false,\"field_num\":39,\"file_size_limit\":20971520,\"is_ocr_recognition\":false,\"api_name\":\"field_q8w5p__c\",\"is_need_cdn\":false,\"_id\":\"5e67124c7473d80001c3a5b0\",\"is_index_field\":false,\"identify_type\":\"\",\"help_text\":\"单个图片不得超过20M\",\"status\":\"new\"},\"field_1p9Sn__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"^[0-9+-;,]{0,100}\$\",\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"type\":\"phone_number\",\"is_required\":false,\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"p_1\",\"verification\":false,\"is_index\":true,\"is_active\":true,\"create_time\":1582626138612,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"手机-掩码字段管理\",\"field_num\":35,\"api_name\":\"field_1p9Sn__c\",\"_id\":\"5e54f55acc86e9000167c04a\",\"is_index_field\":false,\"is_show_mask\":true,\"help_text\":\"\",\"status\":\"new\"},\"origin_source\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1582532106516,\"is_unique\":false,\"label\":\"数据来源\",\"type\":\"select_one\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"origin_source\",\"options\":[{\"label\":\"数据同步\",\"value\":\"0\"}],\"define_type\":\"system\",\"is_extend\":false,\"index_name\":\"s_os\",\"config\":{\"display\":0},\"status\":\"released\",\"description\":\"\"},\"lock_user\":{\"describe_api_name\":\"object_Zpv1K__c\",\"auto_adapt_places\":false,\"description\":\"加锁人\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"employee\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"is_single\":true,\"label_r\":\"加锁人\",\"index_name\":\"a_1\",\"is_index\":false,\"is_active\":true,\"create_time\":1582603785345,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"加锁人\",\"field_num\":5,\"is_need_convert\":false,\"api_name\":\"lock_user\",\"_id\":\"5e549e0993b821000171a10e\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\"},\"field_AN55g__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"used_in\":\"component\",\"is_required\":false,\"define_type\":\"custom\",\"input_mode\":\"\",\"is_single\":false,\"index_name\":\"t_4\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1582622856190,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"详细地址\",\"field_num\":31,\"api_name\":\"field_AN55g__c\",\"_id\":\"5e54e888cc86e9000167a32d\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\"},\"field_kXaRr__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1582624889811,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\\\\w+((-w+)|(\\\\.\\\\w+))*\\\\@[A-Za-z0-9]+((\\\\.|-)[A-Za-z0-9]+)*\\\\.[A-Za-z0-9]+\",\"remove_mask_roles\":{\"data_roles\":[\"data_owner_main_dept_leader\",\"owner\"]},\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"邮箱\",\"type\":\"email\",\"field_num\":34,\"is_required\":false,\"api_name\":\"field_kXaRr__c\",\"define_type\":\"custom\",\"_id\":\"5e54f079cc86e9000167bdeb\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"w_1\",\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\"},\"field_Qy63Q__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"province\",\"used_in\":\"component\",\"is_required\":false,\"options\":[],\"define_type\":\"custom\",\"is_single\":false,\"cascade_parent_api_name\":\"field_FziHr__c\",\"index_name\":\"s_9\",\"is_index\":true,\"is_active\":true,\"create_time\":1582622856187,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"省\",\"field_num\":28,\"api_name\":\"field_Qy63Q__c\",\"_id\":\"5e54e888cc86e9000167a32e\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"is_deleted\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":false,\"create_time\":1582532106516,\"is_unique\":false,\"description\":\"is_deleted\",\"default_value\":false,\"label\":\"is_deleted\",\"type\":\"true_or_false\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"is_deleted\",\"options\":[],\"define_type\":\"system\",\"index_name\":\"is_del\",\"status\":\"released\"},\"field_cxd52__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1582620124796,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"[]\",\"label\":\"收款状态\",\"type\":\"select_one\",\"field_num\":21,\"used_in\":\"component\",\"is_required\":false,\"api_name\":\"field_cxd52__c\",\"options\":[{\"font_color\":\"#2a304d\",\"label\":\"未收款\",\"value\":\"incomplete\"},{\"font_color\":\"#2a304d\",\"label\":\"已收款\",\"value\":\"complete\"}],\"define_type\":\"custom\",\"_id\":\"5e54dddd93b8210001730abf\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"s_14\",\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"object_describe_api_name\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1582532106516,\"pattern\":\"\",\"is_unique\":false,\"description\":\"object_describe_api_name\",\"label\":\"object_describe_api_name\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"object_describe_api_name\",\"define_type\":\"system\",\"index_name\":\"api_name\",\"max_length\":200,\"status\":\"released\"},\"field_1rbUH__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1582620124798,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"group_type\":\"payment\",\"label\":\"支付(收款)组件\",\"type\":\"group\",\"amount_is_readonly\":false,\"is_required\":false,\"api_name\":\"field_1rbUH__c\",\"define_type\":\"custom\",\"_id\":\"5e54dddd93b8210001730ac0\",\"is_single\":false,\"amount_input_type\":\"manual_input\",\"is_index_field\":false,\"fields\":{\"pay_time_field\":\"field_bB0G5__c\",\"pay_status_field\":\"field_cxd52__c\",\"pay_type_field\":\"field_c2eIW__c\",\"pay_amount_field\":\"field_KvOh8__c\"},\"index_name\":\"s_6\",\"help_text\":\"\",\"status\":\"new\"},\"field_hwsqZ__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1582603769358,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"单选-父\",\"type\":\"select_one\",\"field_num\":11,\"is_required\":false,\"api_name\":\"field_hwsqZ__c\",\"options\":[{\"font_color\":\"#2a304d\",\"child_options\":[{\"field_8Kml4__c\":[\"option1\"]}],\"label\":\"示例选项\",\"value\":\"option1\"},{\"font_color\":\"#2a304d\",\"not_usable\":true,\"label\":\"其他\",\"value\":\"other\"}],\"define_type\":\"custom\",\"_id\":\"5e549df993b821000171a0ee\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"s_12\",\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"out_owner\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1582532106516,\"is_unique\":false,\"label\":\"外部负责人\",\"type\":\"employee\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"out_owner\",\"define_type\":\"system\",\"is_single\":true,\"index_name\":\"o_owner\",\"config\":{\"display\":1},\"status\":\"released\",\"description\":\"\"},\"mc_functional_currency\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1609299257461,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"本位币\",\"type\":\"select_one\",\"is_required\":false,\"api_name\":\"mc_functional_currency\",\"options\":[{\"not_usable\":false,\"label\":\"CNY-ChinaYuan\",\"value\":\"CNY\"}],\"define_type\":\"package\",\"_id\":\"5febf5396268f90001d34f60\",\"is_single\":false,\"label_r\":\"本位币\",\"is_index_field\":false,\"index_name\":\"s_15\",\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"owner\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1582532106513,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"default_value\":\"\",\"label\":\"负责人\",\"type\":\"employee\",\"is_need_convert\":false,\"is_required\":true,\"wheres\":[],\"api_name\":\"owner\",\"define_type\":\"package\",\"_id\":\"5e53860a2535410001044fc7\",\"is_single\":true,\"label_r\":\"负责人\",\"is_index_field\":false,\"index_name\":\"owner\",\"help_text\":\"\",\"status\":\"new\"},\"field_rPr65__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"district\",\"used_in\":\"component\",\"is_required\":false,\"options\":[],\"define_type\":\"custom\",\"is_single\":false,\"cascade_parent_api_name\":\"field_54SvN__c\",\"index_name\":\"s_5\",\"is_index\":true,\"is_active\":true,\"create_time\":1582622856189,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"区\",\"field_num\":30,\"api_name\":\"field_rPr65__c\",\"_id\":\"5e54e888cc86e9000167a330\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"last_modified_time\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":true,\"create_time\":1582532106516,\"is_unique\":false,\"description\":\"last_modified_time\",\"label\":\"最后修改时间\",\"type\":\"date_time\",\"time_zone\":\"\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"last_modified_time\",\"define_type\":\"system\",\"date_format\":\"yyyy-MM-ddHH:mm:ss\",\"index_name\":\"md_time\",\"status\":\"released\"},\"field_p86gy__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"^[0-9+-;,]{0,100}\$\",\"remove_mask_roles\":{\"data_roles\":[\"owner\",\"data_owner_main_dept_leader\"]},\"description\":\"\",\"is_unique\":false,\"type\":\"phone_number\",\"is_required\":false,\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"p_2\",\"verification\":false,\"is_index\":true,\"is_active\":true,\"create_time\":1582624724859,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"手机\",\"field_num\":33,\"api_name\":\"field_p86gy__c\",\"_id\":\"5e54efd5cc86e9000167bdcd\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\"},\"field_8Kml4__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1582603775790,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"单选-子\",\"type\":\"select_one\",\"field_num\":13,\"is_required\":true,\"api_name\":\"field_8Kml4__c\",\"options\":[{\"font_color\":\"#2a304d\",\"label\":\"示例选项\",\"value\":\"option1\"},{\"font_color\":\"#2a304d\",\"not_usable\":true,\"label\":\"其他\",\"value\":\"other\"}],\"define_type\":\"custom\",\"_id\":\"5e549dff93b821000171a0f0\",\"is_single\":false,\"cascade_parent_api_name\":\"field_hwsqZ__c\",\"is_index_field\":false,\"index_name\":\"s_13\",\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"life_status\":{\"describe_api_name\":\"object_Zpv1K__c\",\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"select_one\",\"is_required\":true,\"options\":[{\"font_color\":\"#2a304d\",\"label\":\"未生效\",\"value\":\"ineffective\"},{\"font_color\":\"#2a304d\",\"label\":\"审核中\",\"value\":\"under_review\"},{\"font_color\":\"#2a304d\",\"label\":\"正常\",\"value\":\"normal\"},{\"font_color\":\"#2a304d\",\"label\":\"变更中\",\"value\":\"in_change\"},{\"font_color\":\"#2a304d\",\"label\":\"作废\",\"value\":\"invalid\"}],\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"生命状态\",\"index_name\":\"s_11\",\"is_index\":true,\"is_active\":true,\"create_time\":1582532106521,\"is_encrypted\":false,\"default_value\":\"normal\",\"label\":\"生命状态\",\"field_num\":3,\"is_need_convert\":false,\"api_name\":\"life_status\",\"_id\":\"5e53860a2535410001044fca\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"last_modified_by\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1582532106516,\"is_unique\":false,\"label\":\"最后修改人\",\"type\":\"employee\",\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"last_modified_by\",\"define_type\":\"system\",\"is_single\":true,\"index_name\":\"md_by\",\"status\":\"released\",\"description\":\"\"},\"out_tenant_id\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1582532106516,\"pattern\":\"\",\"is_unique\":false,\"description\":\"out_tenant_id\",\"label\":\"外部企业\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"out_tenant_id\",\"define_type\":\"system\",\"index_name\":\"o_ei\",\"config\":{\"display\":0},\"max_length\":200,\"status\":\"released\"},\"mc_currency\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1609299257452,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"币种\",\"type\":\"select_one\",\"is_required\":false,\"api_name\":\"mc_currency\",\"options\":[{\"not_usable\":false,\"label\":\"BDT-孟加拉国塔卡\",\"value\":\"BDT\"},{\"not_usable\":false,\"label\":\"BHD-BahrainiDinar\",\"value\":\"BHD\"},{\"not_usable\":false,\"label\":\"ARS-阿根廷比索\",\"value\":\"ARS\"},{\"not_usable\":false,\"label\":\"AOA-安哥拉宽扎\",\"value\":\"AOA\"},{\"not_usable\":false,\"label\":\"BGN-BulgarianLev\",\"value\":\"BGN\"},{\"not_usable\":false,\"label\":\"AZN-阿塞拜疆马纳特\",\"value\":\"AZN\"},{\"not_usable\":false,\"label\":\"BBD-巴巴多斯元\",\"value\":\"BBD\"},{\"not_usable\":false,\"label\":\"ANG-荷属安地列斯盾\",\"value\":\"ANG\"},{\"not_usable\":false,\"label\":\"BAM-自由兑换马克\",\"value\":\"BAM\"},{\"not_usable\":false,\"label\":\"AUD-AustralianDollar\",\"value\":\"AUD\"},{\"not_usable\":false,\"label\":\"ada名称\",\"value\":\"ada\"},{\"not_usable\":false,\"label\":\"AMD-亚美尼亚打兰\",\"value\":\"AMD\"},{\"not_usable\":false,\"label\":\"CNY-ChinaYuan\",\"value\":\"CNY\"},{\"not_usable\":false,\"label\":\"翻译ASD\",\"value\":\"asd\"},{\"not_usable\":false,\"label\":\"自定义QWE\",\"value\":\"qwe\"},{\"not_usable\":false,\"label\":\"自定义zas\",\"value\":\"zas\"},{\"not_usable\":false,\"label\":\"BSD-巴哈马元\",\"value\":\"BSD\"},{\"not_usable\":false,\"label\":\"BRB-巴西克鲁塞罗（旧）\",\"value\":\"BRB\"},{\"not_usable\":false,\"label\":\"BOV-玻利维亚姆夫多尔\",\"value\":\"BOV\"},{\"not_usable\":false,\"label\":\"ALL-阿尔巴尼亚列克\",\"value\":\"ALL\"},{\"not_usable\":false,\"label\":\"BZD-伯利兹元\",\"value\":\"BZD\"},{\"not_usable\":false,\"label\":\"BYN-白俄罗斯卢布\",\"value\":\"BYN\"},{\"not_usable\":false,\"label\":\"BOB-玻利维亚的玻利维亚诺\",\"value\":\"BOB\"},{\"not_usable\":false,\"label\":\"BRL-巴西币\",\"value\":\"BRL\"},{\"not_usable\":false,\"label\":\"BIF-布隆迪法郎\",\"value\":\"BIF\"},{\"not_usable\":false,\"label\":\"BND-BruneiDollar\",\"value\":\"BND\"},{\"not_usable\":false,\"label\":\"BMD-百慕大元\",\"value\":\"BMD\"},{\"not_usable\":false,\"label\":\"AED-UAEDirham\",\"value\":\"AED\"},{\"not_usable\":false,\"label\":\"AWG-阿鲁巴岛弗罗林\",\"value\":\"AWG\"},{\"not_usable\":false,\"label\":\"USD-U.S.Dollar\",\"value\":\"USD\"},{\"not_usable\":false,\"label\":\"AFN-AfghanistanAfghani(New)\",\"value\":\"AFN\"}],\"define_type\":\"package\",\"_id\":\"5febf5396268f90001d34f5e\",\"is_single\":false,\"label_r\":\"币种\",\"is_index_field\":false,\"index_name\":\"s_16\",\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"record_type\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1582532106520,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"record_type\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"业务类型\",\"type\":\"record_type\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"record_type\",\"options\":[{\"is_active\":true,\"font_color\":\"#2a304d\",\"api_name\":\"default__c\",\"description\":\"预设业务类型\",\"label\":\"预设业务类型\"},{\"is_active\":true,\"font_color\":\"#2a304d\",\"api_name\":\"record_85qpf__c\",\"label\":\"业务1\"}],\"define_type\":\"package\",\"_id\":\"5e53860a2535410001044fcf\",\"is_single\":false,\"label_r\":\"业务类型\",\"is_index_field\":false,\"index_name\":\"r_type\",\"config\":{},\"help_text\":\"\",\"status\":\"released\"},\"field_bGg7y__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"function\",\"type\":\"object_reference\",\"is_required\":false,\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"value_type\":9,\"connector\":\"AND\",\"isIndex\":false,\"fieldNum\":0,\"operator\":\"IN\",\"isObjectReference\":false,\"field_name\":\"id\",\"field_values\":[\"func_tux2x__c\"]}]}],\"define_type\":\"custom\",\"input_mode\":\"\",\"is_single\":false,\"index_name\":\"s_1\",\"is_index\":true,\"is_active\":true,\"create_time\":1582532485204,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"lihh-对象C\",\"target_api_name\":\"object_84uM5__c\",\"target_related_list_name\":\"target_related_list_0C8kB__c\",\"field_num\":6,\"target_related_list_label\":\"lihh-对象A\",\"action_on_target_delete\":\"set_null\",\"api_name\":\"field_bGg7y__c\",\"_id\":\"5e5387852535410001048c5f\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\"},\"field_d0WJ5__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1582620357895,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"default_value\":\"\",\"label\":\"人员\",\"type\":\"employee\",\"field_num\":23,\"is_required\":false,\"wheres\":[],\"api_name\":\"field_d0WJ5__c\",\"define_type\":\"custom\",\"_id\":\"5e54dec693b8210001730ca0\",\"is_single\":true,\"is_index_field\":false,\"index_name\":\"a_2\",\"help_text\":\"\",\"status\":\"new\"},\"mc_exchange_rate_version\":{\"describe_api_name\":\"object_Zpv1K__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"label_r\":\"汇率版本\",\"index_name\":\"t_6\",\"max_length\":256,\"is_index\":false,\"is_active\":true,\"create_time\":1609299257461,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"汇率版本\",\"api_name\":\"mc_exchange_rate_version\",\"_id\":\"5febf5396268f90001d34f61\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\"},\"field_wI1j5__c\":{\"describe_api_name\":\"object_Zpv1K__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"object_reference\",\"is_required\":false,\"wheres\":[],\"define_type\":\"custom\",\"input_mode\":\"\",\"is_single\":false,\"index_name\":\"s_2\",\"is_index\":true,\"is_active\":true,\"create_time\":1582533531762,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"价目表\",\"target_api_name\":\"PriceBookObj\",\"target_related_list_name\":\"target_related_list_Sk1ip__c\",\"field_num\":7,\"target_related_list_label\":\"lihh-对象A\",\"action_on_target_delete\":\"set_null\",\"api_name\":\"field_wI1j5__c\",\"_id\":\"5e538b9c253541000104a797\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\"}},\"release_version\":\"6.4\",\"actions\":{}}\n"

    String relatedTeamJson = "{\n" +
            "          \"teamMemberEmployee\":\"1000\",\n" +
            "          \"teamMemberRole\": \"1\",\n" +
            "          \"teamMemberRoleList\": [\n" +
            "              \"1\"\n" +
            "          ],\n" +
            "          \"teamMemberPermissionType\": \"2\",\n" +
            "          \"outTenantId\": \"\",\n" +
            "          \"sourceType\": \"\",\n" +
            "          \"teamMemberType\": \"0\",\n" +
            "          \"teamMemberDeptCascade\": \"0\"\n" +
            "      }";

    StandardDetailController standardDetailController
    ControllerContext controllerContext = Mock(ControllerContext)
    ServiceFacade serviceFacade = Mock(ServiceFacade)
    SpringBeanHolder springBeanHolder
    InfraServiceFacade infraServiceFacade = Mock(InfraServiceFacade)
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def setup() {
        AppFrameworkConfig.masterDetailApprovalWhiteList = ["78057"]
        AppFrameworkConfig.masterDetailApprovalGrayList = ["74255"]

        springBeanHolder = Spy(SpringBeanHolder)
        infraServiceFacade.getSpringBeanHolder() >> springBeanHolder
        standardDetailController = new StandardDetailController(
                controllerContext: controllerContext,
                serviceFacade: serviceFacade,
                infraServiceFacade: infraServiceFacade)
    }


    def "before method sets up context correctly when mergeSnapshot is true"() {
        given:
        def arg = new AbstractStandardDetailController.Arg()
        arg.setMergeSnapshot(true)
        Whitebox.setInternalState(standardDetailController, "arg", arg)

        when:
        standardDetailController.before(arg)
        then:
        thrown(ValidateException)
    }

    def "before method sets up context correctly when mergeSnapshot is false"() {
        given:
        def arg = new AbstractStandardDetailController.Arg()
        arg.setMergeSnapshot(true)
        arg.setLayoutVersion("V3")
        arg.setObjectDataId("6038b6f017eeb6000192fee4")
        arg.setObjectDescribeApiName("object_6P0i5__c")
        arg.setFromRecycleBin(false)
        arg.setBizInfo(new RequestContext.BizInfo("1", "2", "3"))
        arg.setMergeSnapshot(true)
        arg.setSerializeEmpty(true)
        arg.setMergeSnapshot(mergeSnapshot)
        Whitebox.setInternalState(standardDetailController, "arg", arg)
        IObjectDescribe describe = new ObjectDescribe();

        IFieldDescribe fieldDescribe = new MasterDetailFieldDescribe()
        fieldDescribe.setTargetApiName("AccountObj")
        fieldDescribe.setApiName("field_dasdas__c")
        fieldDescribe.setDescribeApiName("object_sdasda__c")
        describe.setFieldDescribes([fieldDescribe])
        describe.setTenantId("74255")
        IObjectData objectData = new ObjectData()
        User user = Mock()

        def context = RequestContext.builder().user(User.systemUser("74255")).build()
        context.setAttribute("useSnapshotForApproval", true)
        RequestContextManager.setContext(context)

        def snapshot = new ObjectDataSnapshot()
        snapshot.setMasterSnapshot(["apiName": "field__c"])

        when:
        serviceFacade.findObjectWithoutCopy(_, _) >> describe
        serviceFacade.findRelatedDescribes(_, _) >> [describe]
        controllerContext.getUser() >> user
        user.isSupperAdmin() >> false
        serviceFacade.findObjectData(_, _, _) >> objectData

        serviceFacade.calculateForSnapshot(_, _, _, _) >> objectData
        infraServiceFacade.findAndMergeSnapshot(_, _, _, _, _) >> snapshot
        standardDetailController.before(arg)
        then:
        noExceptionThrown()
        where:
        mergeSnapshot || res
        true          || _
        false         || _
    }


    def "doService"() {
        given:
        def arg = new AbstractStandardDetailController.Arg()
        arg.setCalculateFormula(true)
        Whitebox.setInternalState(standardDetailController, "arg", arg)
        IObjectDescribe describe = ObjectDescribeExt.of(JsonUtil.fromJson(describeJson, ObjectDescribe.class));
        Whitebox.setInternalState(standardDetailController, "describe", describe)
        IObjectData objectData = new ObjectData()
        Whitebox.setInternalState(standardDetailController, "data", objectData)
        ILayout layout = new Layout();
        def context = RequestContext.builder().peerName("OpenAPI").user(User.systemUser("74255")).build()
        context.setAttribute("useSnapshotForApproval", true)
        RequestContextManager.setContext(context)
        def compliance = new GdprCompliance()
        compliance.setTenantId("74255")
        when:
        infraServiceFacade.bulkCalculate(describe, [objectData], ObjectDescribeExt.of(describe).getFieldDescribes())
        infraServiceFacade.findGdprCompliance(_, _) >> [new GdprCompliance()]
        controllerContext.getUser() >> new User("74255", "1000")
        serviceFacade.getLayoutWithComponents(_, _, _, _, _, _, _, _) >> layout;
        serviceFacade.findValidLayoutRuleByLayout(_, _, _) >> [new LayoutRuleInfo()]
        infraServiceFacade.findGdprCompliance(_, _) >> [compliance]
        serviceFacade.findObject(_, _) >> describe
        standardDetailController.doService(arg)
        then:
        noExceptionThrown()
    }

    def "finallyDo test"() {
        given:
        def arg = new AbstractStandardDetailController.Arg()
        arg.setFormatData(true)
        IObjectDescribe describe = ObjectDescribeExt.of(JsonUtil.fromJson(describeJson, ObjectDescribe.class));
        IObjectData objectData = new ObjectData()
        Whitebox.setInternalState(standardDetailController, "data", objectData)
        Whitebox.setInternalState(standardDetailController, "arg", arg)
        Whitebox.setInternalState(standardDetailController, "describe", describe)
        controllerContext.getUser() >> User.systemUser("74255")
        when:
        standardDetailController.finallyDo()
        then:
        noExceptionThrown()
    }


    def "buildHandlerArg test"(){
        when:
        standardDetailController.buildHandlerArg(new SimpleHandlerDescribe());
        then:
        noExceptionThrown()
    }


    def "registerHandlerExecuteFunctions test"(){
        when:
        standardDetailController.registerHandlerExecuteFunctions(Maps.newHashMap());
        then:
        noExceptionThrown()
    }

}
