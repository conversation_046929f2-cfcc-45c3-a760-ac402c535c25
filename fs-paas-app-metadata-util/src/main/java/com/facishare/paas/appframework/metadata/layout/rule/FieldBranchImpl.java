package com.facishare.paas.appframework.metadata.layout.rule;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.impl.AbstractDocumentBasedDBRecord;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * create by z<PERSON><PERSON> on 2021/07/19
 */
public class FieldBranchImpl extends AbstractDocumentBasedDBRecord implements IFieldBranch {
    public FieldBranchImpl() {
        super();
    }

    public FieldBranchImpl(Map map) {
        super(map);
    }

    @Override
    public List<IBranch> getBranches() {
        List<Map> branches = (List<Map>) get(BRANCHES);
        if (CollectionUtils.empty(branches)) {
            return Collections.emptyList();
        }
        return branches.stream()
                .map(BranchImpl::new)
                .collect(Collectors.toList());
    }

    @Override
    public ILayoutRuleFilter getMainFieldFilter() {
        Map mainFieldFilter = get(MAIN_FIELD_FILTER, Map.class);
        return new LayoutRuleFilter(mainFieldFilter);
    }

    public void convertDateFieldFilter(ObjectDescribeExt describeExt, boolean toSystemZone) {

    }
}
