package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.metadata.CountryAreaManager;
import com.facishare.paas.appframework.metadata.importobject.dataconvert.ConvertResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.CityFiledDescribe;
import com.facishare.paas.metadata.support.CountryAreaService;
import com.facishare.paas.metadata.util.SpringContextUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.powermock.reflect.Whitebox;
import org.springframework.context.ApplicationContext;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * CountryAreaImportFieldDataConverter单元测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class CountryAreaImportFieldDataConverterTest {

  @Mock
  private CountryAreaService countryAreaService;

  @Mock
  private ApplicationContext applicationContext;

  @Mock
  private IObjectDescribe mockObjectDescribe;

  @Mock
  private IFieldDescribe mockParentFieldDescribe;

  private Map<String, Object> areMap;

  @BeforeEach
  void setUp() {
    // 初始化areMap
    areMap = new HashMap<>();
    areMap.put("is_index", false);
    areMap.put("is_need_convert", false);
    areMap.put("is_required", false);
    areMap.put("api_name", "city");
    
    Map<String, Object> option = new HashMap<>();
    option.put("resource_bundle_key", "");
    option.put("label", "北京市");
    option.put("standard_code", "110100000000");
    option.put("value", "283");
    
    Map<String, Object> childOption = new HashMap<>();
    childOption.put("district", Arrays.asList("628", "629", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645"));
    option.put("child_options", Arrays.asList(childOption));
    
    areMap.put("options", Arrays.asList(option));
    areMap.put("define_type", "package");
    areMap.put("is_unique", false);
    areMap.put("label", "市");
    areMap.put("type", "city");
    areMap.put("cascade_parent_api_name", "province");
    areMap.put("status", "released");

    // 设置Spring上下文
    Whitebox.setInternalState(SpringContextUtil.class, "CONTEXT", applicationContext);
    Whitebox.setInternalState(CountryAreaManager.class, "countryAreaService", countryAreaService);
  }

  @Test
  void testConvertFieldData() {
    // given
    CountryAreaImportFieldDataConverter converter = new CountryAreaImportFieldDataConverter();
    ObjectData data = new ObjectData();
    data.set("field_vzkHy__c", "中国");
    data.set("field_wDRWh__c", "山西省");
    data.set("field_CU0Rd__c", "北京市");
    data.set("field_0iW1h__c", "河北");
    
    CityFiledDescribe fieldDescribe = new CityFiledDescribe();
    fieldDescribe.setCascadeParentApiName("field_wDRWh__c");
    fieldDescribe.setApiName("field_CU0Rd__c");
    
    User user = new User("74255", "1000");

    // Mock CountryAreaManager.getCountryArea to return a proper map
    Map<String, Map> mockCountryAreaMap = new HashMap<>();
    mockCountryAreaMap.put("city", areMap);

    Map<String, Object> provinceMap = new HashMap<>();
    Map<String, Object> provinceOption = new HashMap<>();
    provinceOption.put("label", "山西省");
    provinceOption.put("value", "14");

    Map<String, Object> provinceChildOption = new HashMap<>();
    provinceChildOption.put("city", Arrays.asList("283"));
    provinceOption.put("child_options", Arrays.asList(provinceChildOption));

    provinceMap.put("options", Arrays.asList(provinceOption));
    mockCountryAreaMap.put("province", provinceMap);

    // 使用Whitebox设置CountryAreaManager的内部状态
    Map<Lang, Object> langMap = new HashMap<>();
    langMap.put(Lang.zh_CN, mockCountryAreaMap);
    Whitebox.setInternalState(CountryAreaManager.class, "COUNTRY_AREA", langMap);

    // Mock对象描述
    when(mockParentFieldDescribe.getLabel()).thenReturn("省份");
    when(mockObjectDescribe.getFieldDescribe("field_wDRWh__c")).thenReturn(mockParentFieldDescribe);

    // when
    ConvertResult result = converter.convertFieldData(data, mockObjectDescribe, fieldDescribe, user);

    // then
    assertNotNull(result);
    assertEquals("283", result.getValue()); // 期望返回北京市的code
    assertTrue(result.isSuccess());
  }
}
