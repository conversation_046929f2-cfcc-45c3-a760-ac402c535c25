package com.facishare.paas.appframework.metadata.data.validator;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TextValidator 单元测试
 */
class TextValidatorTest {

  private TextValidator validator;

  @BeforeEach
  void setUp() {
    validator = new TextValidator();
  }

  @Test
  void testSupportFieldTypes() {
    Set<String> supportedTypes = validator.supportFieldTypes();

    assertEquals(14, supportedTypes.size());
    assertTrue(supportedTypes.contains(IFieldType.TEXT));
    assertTrue(supportedTypes.contains(IFieldType.OBJECT_REFERENCE));
    assertTrue(supportedTypes.contains(IFieldType.AUTO_NUMBER));
    assertTrue(supportedTypes.contains(IFieldType.COUNTRY));
    assertTrue(supportedTypes.contains(IFieldType.PROVINCE));
    assertTrue(supportedTypes.contains(IFieldType.CITY));
    assertTrue(supportedTypes.contains(IFieldType.DISTRICT));
    assertTrue(supportedTypes.contains(IFieldType.PHONE_NUMBER));
    assertTrue(supportedTypes.contains(IFieldType.URL));
    assertTrue(supportedTypes.contains(IFieldType.RECORD_TYPE));
    assertTrue(supportedTypes.contains(IFieldType.MASTER_DETAIL));
    assertTrue(supportedTypes.contains(IFieldType.LOCATION));
    assertTrue(supportedTypes.contains(IFieldType.HTML_RICH_TEXT));
    assertTrue(supportedTypes.contains(IFieldType.EMAIL));
    // 确认已移除的类型不在支持列表中
    assertFalse(supportedTypes.contains(IFieldType.SELECT_ONE));
  }

  @Test
  void testValidateDataType_WithNullValue_ShouldPass() {
    // Given
    String fieldApiName = "test_field";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.TEXT);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, null);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithStringValue_ShouldPass() {
    // Given
    String fieldApiName = "test_field";
    String fieldValue = "test_string_value";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.TEXT);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithEmptyStringValue_ShouldPass() {
    // Given
    String fieldApiName = "test_field";
    String fieldValue = "";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.EMAIL);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithUrlType_ShouldPass() {
    // Given
    String fieldApiName = "test_field";
    String fieldValue = "https://www.example.com";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.URL);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithPhoneNumberType_ShouldPass() {
    // Given
    String fieldApiName = "test_field";
    String fieldValue = "13800138000";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.PHONE_NUMBER);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithIntegerValue_ShouldThrowException() {
    // Given
    String fieldApiName = "test_field";
    Integer fieldValue = 123;
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.TEXT);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    ValidateException exception = assertThrows(ValidateException.class,
        () -> validator.validateDataType(fieldApiName, objectData, describe));
    
    assertNotNull(exception.getMessage());
  }

  @Test
  void testValidateDataType_WithBooleanValue_ShouldThrowException() {
    // Given
    String fieldApiName = "test_field";
    Boolean fieldValue = Boolean.TRUE;
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.TEXT);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    ValidateException exception = assertThrows(ValidateException.class,
        () -> validator.validateDataType(fieldApiName, objectData, describe));
    
    assertNotNull(exception.getMessage());
  }

  @Test
  void testValidateDataType_WithArrayValue_ShouldThrowException() {
    // Given
    String fieldApiName = "test_field";
    String[] fieldValue = {"value1", "value2"};
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.TEXT);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    ValidateException exception = assertThrows(ValidateException.class,
        () -> validator.validateDataType(fieldApiName, objectData, describe));
    
    assertNotNull(exception.getMessage());
  }

  @Test
  void testValidateDataType_WithNullFieldApiName_ShouldReturn() {
    // Given
    String fieldApiName = null;
    IObjectDescribe describe = createObjectDescribe("test_field", IFieldType.TEXT);
    IObjectData objectData = new ObjectData(Collections.emptyMap());

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithEmptyFieldApiName_ShouldReturn() {
    // Given
    String fieldApiName = "";
    IObjectDescribe describe = createObjectDescribe("test_field", IFieldType.TEXT);
    IObjectData objectData = new ObjectData(Collections.emptyMap());

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithNullData_ShouldReturn() {
    // Given
    String fieldApiName = "test_field";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.TEXT);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, null, describe));
  }

  @Test
  void testValidateDataType_WithNullDescribe_ShouldReturn() {
    // Given
    String fieldApiName = "test_field";
    IObjectData objectData = new ObjectData(Collections.emptyMap());

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, null));
  }

  @Test
  void testValidateMaxLength_ShouldNotThrowException() {
    // Given - validateMaxLength 方法当前被注释掉了，不执行实际校验
    String fieldApiName = "test_field";
    String fieldValue = "very_long_string_value_that_might_exceed_max_length";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.TEXT);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    assertDoesNotThrow(() -> validator.validateMaxLength(fieldApiName, objectData, describe));
  }

  /**
   * 创建测试用的 IObjectDescribe 对象
   */
  private IObjectDescribe createObjectDescribe(String fieldApiName, String fieldType) {
    IObjectDescribe describe = new ObjectDescribe();
    describe.setApiName("TestObj");
    
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", fieldApiName);
    fieldMap.put("type", fieldType);
    fieldMap.put("label", "测试字段");
    
    IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
    
    describe.setFieldDescribes(Arrays.asList(fieldDescribe));
    return describe;
  }
}
