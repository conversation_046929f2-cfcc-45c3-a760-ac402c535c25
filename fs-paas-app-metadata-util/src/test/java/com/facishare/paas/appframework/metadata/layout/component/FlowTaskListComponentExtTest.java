package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.appframework.metadata.HeadField;
import com.facishare.paas.appframework.metadata.IHeadField;
import com.facishare.paas.metadata.impl.ui.layout.component.CommonComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedConstruction;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * FlowTaskListComponentExt单元测试类
 */
@ExtendWith(MockitoExtension.class)
class FlowTaskListComponentExtTest {

  @Mock
  private IComponent mockComponent;

  private FlowTaskListComponentExt flowTaskListComponentExt;

  @BeforeEach
  void setUp() {
    flowTaskListComponentExt = FlowTaskListComponentExt.of(mockComponent);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试of方法通过IComponent创建FlowTaskListComponentExt对象
   */
  @Test
  @DisplayName("测试of方法通过IComponent创建对象")
  void testOfMethodWithIComponent() {
    IComponent component = mock(IComponent.class);
    
    FlowTaskListComponentExt result = FlowTaskListComponentExt.of(component);
    
    assertNotNull(result);
    assertEquals(component, result.getComponent());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试of方法通过Map创建FlowTaskListComponentExt对象
   */
  @Test
  @DisplayName("测试of方法通过Map创建对象")
  void testOfMethodWithMap() {
    try (MockedConstruction<CommonComponent> mockedConstruction = mockConstruction(CommonComponent.class)) {
      Map<String, Object> map = new HashMap<>();
      map.put("test", "value");
      
      FlowTaskListComponentExt result = FlowTaskListComponentExt.of(map);
      
      assertNotNull(result);
      
      // 验证CommonComponent的构造和方法调用
      List<CommonComponent> constructed = mockedConstruction.constructed();
      assertEquals(1, constructed.size());
      
      CommonComponent commonComponent = constructed.get(0);
      verify(commonComponent).setType(BaseFlowTaskListComponentExt.COMPONENT_TYPE_TASK_LIST);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试resetHeadField方法当headFields为null时不执行操作
   */
  @Test
  @DisplayName("测试resetHeadField方法 - headFields为null时不执行操作")
  void testResetHeadFieldWhenHeadFieldsIsNull() {
    // 执行测试
    flowTaskListComponentExt.resetHeadField(null);
    
    // 验证没有调用set方法
    verify(mockComponent, never()).set(anyString(), any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试resetHeadField方法当headFields不为空时正常设置
   */
  @Test
  @DisplayName("测试resetHeadField方法 - headFields不为空时正常设置")
  void testResetHeadFieldWhenHeadFieldsIsNotEmpty() {
    // 准备测试数据
    IHeadField headField1 = mock(IHeadField.class);
    IHeadField headField2 = mock(IHeadField.class);
    
    Map<String, Object> map1 = new HashMap<>();
    map1.put("field_name", "field1");
    map1.put("is_show", true);
    
    Map<String, Object> map2 = new HashMap<>();
    map2.put("field_name", "field2");
    map2.put("is_show", false);
    
    when(headField1.toMap()).thenReturn(map1);
    when(headField2.toMap()).thenReturn(map2);
    
    List<IHeadField> headFields = Lists.newArrayList(headField1, headField2);
    
    // 执行测试
    flowTaskListComponentExt.resetHeadField(headFields);
    
    // 验证set方法被调用
    verify(mockComponent).set(eq(BaseFlowTaskListComponentExt.FIELD_LIST), any(List.class));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getHeadFields方法当field_list为null时返回空列表
   */
  @Test
  @DisplayName("测试getHeadFields方法 - field_list为null时返回空列表")
  void testGetHeadFieldsWhenFieldListIsNull() {
    when(mockComponent.get(BaseFlowTaskListComponentExt.FIELD_LIST)).thenReturn(null);
    
    List<IHeadField> result = flowTaskListComponentExt.getHeadFields();
    
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getHeadFields方法当field_list不为空时返回HeadField列表
   */
  @Test
  @DisplayName("测试getHeadFields方法 - field_list不为空时返回HeadField列表")
  void testGetHeadFieldsWhenFieldListIsNotEmpty() {
    // 准备测试数据
    Map<String, Object> fieldMap1 = new HashMap<>();
    fieldMap1.put("field_name", "field1");
    fieldMap1.put("is_show", true);
    fieldMap1.put("width", 100);
    
    Map<String, Object> fieldMap2 = new HashMap<>();
    fieldMap2.put("field_name", "field2");
    fieldMap2.put("is_show", false);
    fieldMap2.put("width", 200);
    
    List<Map> fieldList = Lists.newArrayList(fieldMap1, fieldMap2);
    when(mockComponent.get(BaseFlowTaskListComponentExt.FIELD_LIST)).thenReturn(fieldList);
    
    // 执行测试
    List<IHeadField> result = flowTaskListComponentExt.getHeadFields();
    
    // 验证结果
    assertNotNull(result);
    assertEquals(2, result.size());
    
    // 验证第一个HeadField
    IHeadField headField1 = result.get(0);
    assertEquals("field1", headField1.getFieldName());
    assertTrue(headField1.isIsShow());
    assertEquals(100, headField1.getWidth().intValue());
    
    // 验证第二个HeadField
    IHeadField headField2 = result.get(1);
    assertEquals("field2", headField2.getFieldName());
    assertFalse(headField2.isIsShow());
    assertEquals(200, headField2.getWidth().intValue());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试resetHeadField和getHeadFields方法的完整流程
   */
  @Test
  @DisplayName("测试resetHeadField和getHeadFields方法的完整流程")
  void testResetAndGetHeadFieldsCompleteFlow() {
    // 准备测试数据
    HeadField headField1 = new HeadField();
    headField1.setFieldName("testField1");
    headField1.setIsShow(true);
    headField1.setWidth(150);
    
    HeadField headField2 = new HeadField();
    headField2.setFieldName("testField2");
    headField2.setIsShow(false);
    headField2.setWidth(250);
    
    List<IHeadField> originalHeadFields = Lists.newArrayList(headField1, headField2);
    
    // Mock component的行为
    List<Map> storedFieldList = new ArrayList<>();
    doAnswer(invocation -> {
      storedFieldList.clear();
      storedFieldList.addAll((List<Map>) invocation.getArgument(1));
      return null;
    }).when(mockComponent).set(eq(BaseFlowTaskListComponentExt.FIELD_LIST), any(List.class));
    
    when(mockComponent.get(BaseFlowTaskListComponentExt.FIELD_LIST)).thenAnswer(invocation -> storedFieldList);
    
    // 执行resetHeadField
    flowTaskListComponentExt.resetHeadField(originalHeadFields);
    
    // 执行getHeadFields
    List<IHeadField> retrievedHeadFields = flowTaskListComponentExt.getHeadFields();
    
    // 验证结果
    assertNotNull(retrievedHeadFields);
    assertEquals(2, retrievedHeadFields.size());
    
    assertEquals("testField1", retrievedHeadFields.get(0).getFieldName());
    assertTrue(retrievedHeadFields.get(0).isIsShow());
    assertEquals(150, retrievedHeadFields.get(0).getWidth().intValue());
    
    assertEquals("testField2", retrievedHeadFields.get(1).getFieldName());
    assertFalse(retrievedHeadFields.get(1).isIsShow());
    assertEquals(250, retrievedHeadFields.get(1).getWidth().intValue());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试resetHeadField方法当headFields为空列表时正常处理
   */
  @Test
  @DisplayName("测试resetHeadField方法 - headFields为空列表时正常处理")
  void testResetHeadFieldWhenHeadFieldsIsEmpty() {
    List<IHeadField> emptyHeadFields = Lists.newArrayList();
    
    // 执行测试
    flowTaskListComponentExt.resetHeadField(emptyHeadFields);
    
    // 验证set方法被调用，传入空列表
    verify(mockComponent).set(eq(BaseFlowTaskListComponentExt.FIELD_LIST), eq(Lists.newArrayList()));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试继承自BaseFlowTaskListComponentExt的方法是否可用
   */
  @Test
  @DisplayName("测试继承的方法可用性")
  void testInheritedMethods() {
    // 测试getComponent方法（继承自父类）
    IComponent component = flowTaskListComponentExt.getComponent();
    assertEquals(mockComponent, component);
    
    // 测试常量可用性
    assertEquals("task_list", BaseFlowTaskListComponentExt.COMPONENT_TYPE_TASK_LIST);
    assertEquals("field_list", BaseFlowTaskListComponentExt.FIELD_LIST);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试Map构造方法中CommonComponent的类型设置
   */
  @Test
  @DisplayName("测试Map构造方法中的类型设置")
  void testMapConstructorTypeSettings() {
    try (MockedConstruction<CommonComponent> mockedConstruction = mockConstruction(CommonComponent.class,
        (mock, context) -> {
          // 验证构造函数参数
          assertEquals(1, context.arguments().size());
          assertTrue(context.arguments().get(0) instanceof Map);
        })) {
      
      Map<String, Object> testMap = new HashMap<>();
      testMap.put("name", "testComponent");
      
      FlowTaskListComponentExt result = FlowTaskListComponentExt.of(testMap);
      
      assertNotNull(result);
      
      // 验证CommonComponent被正确构造
      List<CommonComponent> constructed = mockedConstruction.constructed();
      assertEquals(1, constructed.size());
      
      // 验证setType方法被调用
      CommonComponent commonComponent = constructed.get(0);
      verify(commonComponent).setType(BaseFlowTaskListComponentExt.COMPONENT_TYPE_TASK_LIST);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试常量值的正确性
   */
  @Test
  @DisplayName("测试常量值")
  void testConstants() {
    assertEquals("task_list", BaseFlowTaskListComponentExt.COMPONENT_TYPE_TASK_LIST);
    assertEquals("task_list_mobile", BaseFlowTaskListComponentExt.COMPONENT_TYPE_TASK_LIST_MOBILE);
    assertEquals("field_list", BaseFlowTaskListComponentExt.FIELD_LIST);
    assertEquals("filter_info", BaseFlowTaskListComponentExt.FILTER_INFO);
    assertEquals("orders", BaseFlowTaskListComponentExt.ORDERS);
  }
}
