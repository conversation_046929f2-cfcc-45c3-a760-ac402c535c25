package com.facishare.paas.appframework.log;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.TenantInfo;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.dto.EmpSimpleInfo;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import com.facishare.paas.appframework.log.dto.TeamMemberInfo;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.TeamRoleInfo;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.paas.appframework.log.dto.ModifyRecord.SNAP_TYPE_SHOW;

@Component("abstractModifyRecordProvider")
public class AbstractModifyRecordProvider implements ModifyRecordInitProvider {
    @Override
    public String getApiName() {
        return null;
    }

    @Override
    public ModifyRecord getModifyRecord(LogInfo logInfo) {
        ModifyRecord modifyRecord = new ModifyRecord();
        modifyRecord.setLogID(logInfo.getLogId());
        modifyRecord.setOperationTime(logInfo.getOperationTime());
        modifyRecord.setOwner(getEmpSimpleInfo(logInfo));
        modifyRecord.setOperationType(logInfo.getBizOperationName());
        modifyRecord.setLogInfo(logInfo);
        LogInfo.ObjectSnapshot snapshot = logInfo.getSnapshot();
        if (Objects.nonNull(snapshot)) {
            modifyRecord.setConfigMsg(snapshot.getConfigMsg());
            modifyRecord.setTraceId(snapshot.getTraceId());
        }
        return modifyRecord;
    }

    @Override
    public void logTeamMember(IObjectData oldData,
                              IObjectData data,
                              IObjectDescribe objectDescribe,
                              Map<String, List<TeamMemberInfo.Member>> memberInfos,
                              List<TeamMemberInfo.Msg> msgList,
                              List<LogInfo.LintMessage> textMsg,
                              List<TeamRoleInfo> teamRoleInfos) {
    }

    @Override
    public void logTeamMember(IObjectData oldData,
                              IObjectData data,
                              IObjectDescribe objectDescribe,
                              Map<String, List<TeamMemberInfo.Member>> memberInfos,
                              List<TeamMemberInfo.Msg> msgList,
                              List<LogInfo.LintMessage> textMsg,
                              Map<String, TenantInfo> userTenantMap,
                              List<TeamRoleInfo> teamRoleInfos) {

        logTeamMember(oldData, data, objectDescribe, memberInfos, msgList, textMsg, teamRoleInfos);
        // 补充企业名称
        for (TeamMemberInfo.Msg msg : msgList) {
            TeamMemberInfo.Member member = msg.getMember();
            if (CollectionUtils.notEmpty(userTenantMap)) {
                if (Objects.nonNull(userTenantMap.get(member.getId()))) {
                    member.setOutTenantName(userTenantMap.get(member.getId()).getName());
                }
                if (Objects.nonNull(userTenantMap.get(member.getOldId()))) {
                    member.setOldOutTenantName(userTenantMap.get(member.getOldId()).getName());
                }
            }

        }
    }


    protected int getSnapShotType(LogInfo logInfo) {
        if (logInfo == null) {
            return ModifyRecord.SNAP_TYPE_NOT_SHOW;
        }
        if (Objects.equals(ActionType.Add.getId(), logInfo.getBizOperationName()) ||
                Objects.equals(ActionType.Modify.getId(), logInfo.getBizOperationName()) ||
                Objects.equals(ActionType.UpdateImport.getId(), logInfo.getBizOperationName())) {
            return SNAP_TYPE_SHOW;
        } else {
            return ModifyRecord.SNAP_TYPE_NOT_SHOW;
        }
    }

    protected EmpSimpleInfo getEmpSimpleInfo(LogInfo logInfo) {
        EmpSimpleInfo empSimpleInfo = new EmpSimpleInfo();
        //部分回调接口可能会出现没有传入UserId的情况，这种情况大部分是系统的回调，先简单兼容
        empSimpleInfo.setEmployeeID(StringUtils.isEmpty(logInfo.getUserId()) ? -10000 : Integer.parseInt(logInfo.getUserId()));
        String userName;
        if (StringUtils.isEmpty(logInfo.getUserName()) || User.SUPPER_ADMIN_USER_ID_INT == empSimpleInfo.getEmployeeID()) {
            userName = I18N.text(I18NKey.SYSTEM);
        } else {
            userName = logInfo.getUserName();
        }
        empSimpleInfo.setName(userName);
        return empSimpleInfo;
    }

    protected final String getRoleNameKey(List<TeamRoleInfo> teamRoleInfos, TeamMember member) {
        String roleNameKey;
        if (CollectionUtils.notEmpty(teamRoleInfos)) {
            roleNameKey = teamRoleInfos.stream()
                    .filter(roleInfo -> StringUtils.equals(member.getRoleCode(), roleInfo.getRoleType()))
                    .findFirst()
                    .map(TeamRoleInfo::getRoleNameTransKey)
                    .orElse("");
        } else {
            roleNameKey = DefObjConstants.DATA_PRIVILEGE_ROLE_TYPE.getI18NLabelByValue(member.getRoleCode());
        }
        return roleNameKey;
    }

}
