package com.facishare.paas.appframework.common.util

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.JsonNode
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

/**
 * GenerateByAI
 * 测试内容描述：JacksonUtils工具类的单元测试
 */
class JacksonUtilsTest extends Specification {



    // 定义测试用的POJO类
    static class TestPojo {
        @JsonProperty("name")
        String name

        @JsonProperty("age")
        Integer age

        @JsonProperty("active")
        Boolean active

        boolean equals(o) {
            if (this.is(o)) return true
            if (getClass() != o.class) return false

            TestPojo testPojo = (TestPojo) o

            if (active != testPojo.active) return false
            if (age != testPojo.age) return false
            if (name != testPojo.name) return false

            return true
        }

        int hashCode() {
            int result
            result = (name != null ? name.hashCode() : 0)
            result = 31 * result + (age != null ? age.hashCode() : 0)
            result = 31 * result + (active != null ? active.hashCode() : 0)
            return result
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象序列化为JSON字符串
     */
    def "toJson should serialize object to json string"() {
        given:
        def testPojo = new TestPojo(name: "张三", age: 30, active: true)

        when:
        def jsonString = JacksonUtils.toJson(testPojo)

        then:
        jsonString != null
        jsonString.contains('"name":"张三"')
        jsonString.contains('"age":30')
        jsonString.contains('"active":true')
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象序列化为排除null值的JSON字符串
     */
    def "toJsonExcludeNullValue should serialize object excluding null values"() {
        given:
        def testPojo = new TestPojo(name: "张三", age: null, active: true)

        when:
        def jsonString = JacksonUtils.toJsonExcludeNullValue(testPojo)

        then:
        jsonString != null
        jsonString.contains('"name":"张三"')
        !jsonString.contains('"age":')
        jsonString.contains('"active":true')
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试JSON字符串反序列化为对象
     */
    def "fromJson should deserialize json string to object"() {
        given:
        def jsonString = '{"name":"张三","age":30,"active":true}'
        def expectedPojo = new TestPojo(name: "张三", age: 30, active: true)

        when:
        def actualPojo = JacksonUtils.fromJson(jsonString, TestPojo.class)

        then:
        actualPojo != null
        actualPojo == expectedPojo
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试JSON字符串反序列化为集合对象
     */
    def "fromJson should deserialize json string to collection with TypeReference"() {
        given:
        def jsonString = '[{"name":"张三","age":30,"active":true},{"name":"李四","age":25,"active":false}]'
        def typeReference = new TypeReference<List<TestPojo>>() {}

        when:
        def pojoList = JacksonUtils.fromJson(jsonString, typeReference)

        then:
        pojoList != null
        pojoList.size() == 2
        pojoList[0].name == "张三"
        pojoList[0].age == 30
        pojoList[0].active == true
        pojoList[1].name == "李四"
        pojoList[1].age == 25
        pojoList[1].active == false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试JSON字符串反序列化为参数化类型
     */
    def "fromJson should deserialize json string with parametric type"() {
        given:
        def jsonString = '[{"name":"张三","age":30,"active":true},{"name":"李四","age":25,"active":false}]'

        when:
        def pojoList = JacksonUtils.fromJson(jsonString, List.class, TestPojo.class)

        then:
        pojoList != null
        pojoList.size() == 2
        pojoList[0].name == "张三"
        pojoList[0].age == 30
        pojoList[0].active == true
        pojoList[1].name == "李四"
        pojoList[1].age == 25
        pojoList[1].active == false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试JSON字符串转换为JsonNode
     */
    def "readTree should convert json string to JsonNode"() {
        given:
        def jsonString = '{"name":"张三","age":30,"nested":{"field":"value"}}'

        when:
        def jsonNode = JacksonUtils.readTree(jsonString)

        then:
        jsonNode != null
        jsonNode instanceof JsonNode
        jsonNode.get("name").asText() == "张三"
        jsonNode.get("age").asInt() == 30
        jsonNode.get("nested").get("field").asText() == "value"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象转换为其他类型
     */
    def "convertValue should convert object to another type"() {
        given:
        def testPojo = new TestPojo(name: "张三", age: 30, active: true)

        when:
        def map = JacksonUtils.convertValue(testPojo, Map.class)

        then:
        map != null
        map instanceof Map
        map.name == "张三"
        map.age == 30
        map.active == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取TypeReference
     */
    def "getTypeReference should return correct TypeReference"() {
        given:
        def type = List.class

        when:
        def typeReference = JacksonUtils.getTypeReference(type)

        then:
        typeReference != null
        typeReference instanceof TypeReference
        typeReference.getType() == type
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理无效JSON的异常情况
     */
    def "fromJson should handle invalid json gracefully"() {
        given:
        def invalidJson = '{invalid json}'

        when:
        JacksonUtils.fromJson(invalidJson, TestPojo.class)

        then:
        thrown(Exception)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理空JSON输入
     */
    @Unroll
    def "Jackson utils should handle null and empty inputs: #input"() {
        when:
        def result = JacksonUtils.toJson(input)

        then:
        result == expectedJson

        where:
        input | expectedJson
        null  | "null"
        ""    | '""'
        []    | "[]"
        [:]   | "{}"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理空JSON字符串输入的readTree方法
     */
    def "readTree should handle null or empty json"() {
        expect:
        JacksonUtils.readTree(null) == null
        JacksonUtils.readTree("") == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理空JSON字符串输入的fromJson方法
     */
    def "fromJson should handle null or empty json"() {
        expect:
        JacksonUtils.fromJson(null, TestPojo.class) == null
        JacksonUtils.fromJson("", TestPojo.class) == null
        JacksonUtils.fromJson(null, new TypeReference<TestPojo>() {}) == null
        JacksonUtils.fromJson("", new TypeReference<TestPojo>() {}) == null
        JacksonUtils.fromJson(null, List.class, TestPojo.class) == null
        JacksonUtils.fromJson("", List.class, TestPojo.class) == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试复杂对象的序列化和反序列化
     */
    def "should handle complex objects with nested structures"() {
        given:
        def complexObject = [
            id: 1,
            name: "复杂对象",
            nested: [
                field1: "嵌套值1",
                field2: 42,
                array: [1, 2, 3, 4, 5]
            ],
            list: [
                new TestPojo(name: "项目1", age: 10, active: true),
                new TestPojo(name: "项目2", age: 20, active: false)
            ]
        ]

        when:
        def json = JacksonUtils.toJson(complexObject)
        def deserializedObject = JacksonUtils.fromJson(json, Map.class)

        then:
        deserializedObject != null
        deserializedObject.id == 1
        deserializedObject.name == "复杂对象"
        deserializedObject.nested.field1 == "嵌套值1"
        deserializedObject.nested.field2 == 42
        deserializedObject.nested.array == [1, 2, 3, 4, 5]
        deserializedObject.list.size() == 2
        deserializedObject.list[0].name == "项目1"
        deserializedObject.list[1].name == "项目2"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试性能
     */
    def "performance test for large objects"() {
        given:
        // 创建大量数据的对象
        def largeList = []
        for (int i = 0; i < 1000; i++) {
            largeList.add(new TestPojo(name: "名称-${i}", age: i, active: i % 2 == 0))
        }

        when:
        def startTime = System.nanoTime()
        def json = JacksonUtils.toJson(largeList)
        def endTime = System.nanoTime()
        def serializationTime = (endTime - startTime) / 1_000_000 // 毫秒

        startTime = System.nanoTime()
        def deserializedList = JacksonUtils.fromJson(json, new TypeReference<List<TestPojo>>() {})
        endTime = System.nanoTime()
        def deserializationTime = (endTime - startTime) / 1_000_000 // 毫秒

        then:
        // 序列化和反序列化应在合理的时间内完成
        serializationTime < 2000 // 2秒内
        deserializationTime < 2000 // 2秒内
        deserializedList.size() == 1000
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试将JSON字符串转换为Map对象
     */
    def "fromJson should convert json string to Map correctly"() {
        given:
        def jsonString = '{"name":"测试名称","value":123,"isValid":true,"nested":{"key":"嵌套值"}}'
        
        when:
        def result = JacksonUtils.fromJson(jsonString, Map.class)
        
        then:
        result != null
        result instanceof Map
        result.name == "测试名称"
        result.value == 123
        result.isValid == true
        result.nested instanceof Map
        result.nested.key == "嵌套值"
    }
} 