package com.facishare.paas.appframework.common.service.dto;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Delegate;

import java.util.List;

public interface QueryRoleInfoByCodes {
    @Data
    @Builder
    class Arg {
        private AuthContext authContext;
        /**
         * 角色编号列表
         */
        private List<String> roleCodes;
        /**
         * 角色名
         */
        private String roleName;
        /**
         * 角色类型
         */
        private Integer roleType;
    }

    @Data
    class Result {
        private int errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        @Delegate
        private RoleInfoHelper result;

        public boolean isSuccess() {
            return this.errCode == 0;
        }
    }

    @Data
    class RoleInfoHelper {
        private List<QueryUserRoleInfo.RoleInfo> roles;
    }
}
