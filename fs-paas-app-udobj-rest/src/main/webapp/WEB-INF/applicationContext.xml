<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
             http://www.springframework.org/schema/aop
       http://www.springframework.org/schema/aop/spring-aop.xsd">

    <import resource="classpath:spring/app-web.xml"/>

    <!--jvm监控-->
    <!--service 监控-->
    <bean id="serviceProfiler" class="com.facishare.paas.appframework.common.aop.CrmServiceProfiler"/>
    <aop:config proxy-target-class="true">
        <aop:aspect ref="serviceProfiler">
            <aop:around method="profile" pointcut="execution(* com.facishare.paas.metadata.service..*(..))"/>
        </aop:aspect>
    </aop:config>

</beans>
