package com.facishare.paas.appframework.common.util

import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：NumberUtil工具类的单元测试
 */
class NumberUtilTest extends Specification {

    /**
     * GenerateByAI
     * 测试内容描述：测试常量值是否正确
     */
    def "constants test"() {
        expect:
        // 测试各常量值是否符合预期
        NumberUtil.INTEGER_ZERO == 0
        NumberUtil.INTEGER_ONE == 1
        NumberUtil.INTEGER_TWENTY == 20
        NumberUtil.INTEGER_ONE_THOUSAND == 1000
        NumberUtil.INTEGER_TWO_THOUSAND == 2000
        
        // 验证常量为Integer类型
        NumberUtil.INTEGER_ZERO instanceof Integer
        NumberUtil.INTEGER_ONE instanceof Integer
        NumberUtil.INTEGER_TWENTY instanceof Integer
        NumberUtil.INTEGER_ONE_THOUSAND instanceof Integer
        NumberUtil.INTEGER_TWO_THOUSAND instanceof Integer
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试firstNonNull方法对不同类型数字的处理
     */
    def "firstNonNull with different number types test"() {
        given:
        def integerValue = 123
        def longValue = 456L
        def doubleValue = 789.0d
        def floatValue = 123.45f
        def bigDecimalValue = new BigDecimal("987.65")
        
        expect:
        // 测试不同类型的数字
        NumberUtil.firstNonNull(integerValue, longValue) == integerValue
        NumberUtil.firstNonNull(longValue, doubleValue) == longValue
        NumberUtil.firstNonNull(doubleValue, floatValue) == doubleValue
        NumberUtil.firstNonNull(floatValue, bigDecimalValue) == floatValue
        NumberUtil.firstNonNull(bigDecimalValue, integerValue) == bigDecimalValue
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试firstNonNull方法处理null值
     */
    def "firstNonNull with null values test"() {
        given:
        def integerValue = 123
        Integer nullInteger = null
        
        expect:
        // 第一个参数为null，应返回第二个非null参数
        NumberUtil.firstNonNull(null, integerValue) == integerValue
        
        // 所有参数都为null，应返回null
        NumberUtil.firstNonNull(null, null) == null
        
        // 混合null和非null值
        NumberUtil.firstNonNull(nullInteger, 456, 789) == 456
        NumberUtil.firstNonNull(nullInteger, nullInteger, 789) == 789
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试firstNonNull方法处理空参数
     */
    def "firstNonNull with empty args test"() {
        expect:
        // 不传参数应返回null
        NumberUtil.firstNonNull() == null
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试firstNonNull方法处理边界值
     */
    @Unroll
    def "firstNonNull with boundary values #desc test"() {
        expect:
        NumberUtil.firstNonNull(values as Number[]) == expected
        
        where:
        desc              | values                | expected
        "最大整数"         | [Integer.MAX_VALUE]   | Integer.MAX_VALUE
        "最小整数"         | [Integer.MIN_VALUE]   | Integer.MIN_VALUE
        "最大长整数"       | [Long.MAX_VALUE]      | Long.MAX_VALUE
        "最小长整数"       | [Long.MIN_VALUE]      | Long.MIN_VALUE
        "零值"            | [0]                   | 0
        "负零浮点数"       | [-0.0d, 1.0d]         | -0.0d
        "最小正浮点数"     | [Double.MIN_VALUE]    | Double.MIN_VALUE
        "非数字浮点数"     | [Double.NaN, 1.0d]    | Double.NaN
        "正无穷大"        | [Double.POSITIVE_INFINITY, 1.0d] | Double.POSITIVE_INFINITY
        "负无穷大"        | [Double.NEGATIVE_INFINITY, 1.0d] | Double.NEGATIVE_INFINITY
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试firstNonNull方法的性能
     */
    def "firstNonNull performance test"() {
        given:
        def largeArray = new Integer[1000]
        // 填充999个null和最后一个非null值
        for (int i = 0; i < 999; i++) {
            largeArray[i] = null
        }
        largeArray[999] = 42
        
        when:
        def startTime = System.nanoTime()
        def result = NumberUtil.firstNonNull(largeArray)
        def endTime = System.nanoTime()
        def duration = (endTime - startTime) / 1_000_000 // 转换为毫秒
        
        then:
        // 结果应为数组中唯一的非null值
        result == 42
        // 性能检查：处理1000个元素应该很快（小于100毫秒）
        duration < 100
    }
} 