package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.ui.layout.IGridComponent;
import lombok.Getter;
import lombok.experimental.Delegate;

import java.util.List;

public class GridComponentExt {

    @Delegate
    @Getter
    private IGridComponent component;

    private GridComponentExt(IGridComponent component) {
        this.component = component;
    }

    public static GridComponentExt of(IGridComponent component) {
        return new GridComponentExt(component);
    }

    public List<List<String>> getComponents() {
        return (List<List<String>>) component.get("components");
    }

}
