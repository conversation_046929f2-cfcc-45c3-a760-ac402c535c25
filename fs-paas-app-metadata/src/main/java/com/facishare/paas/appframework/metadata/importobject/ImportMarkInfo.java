package com.facishare.paas.appframework.metadata.importobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/6/29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportMarkInfo {
    private String markLabel;
    private String dataId;
    private Boolean triggerApprovalAfterImport;
    private Boolean triggerWorkFlowAfterImport;
}
