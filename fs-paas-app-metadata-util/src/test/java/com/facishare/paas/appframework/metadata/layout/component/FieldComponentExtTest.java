package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.appframework.metadata.TableColumnExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * FieldComponentExt单元测试类
 */
@ExtendWith(MockitoExtension.class)
class FieldComponentExtTest {

  private FieldComponentExt fieldComponentExt;

  @BeforeEach
  void setUp() {
    fieldComponentExt = new FieldComponentExt();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试默认构造函数创建对象
   */
  @Test
  @DisplayName("测试默认构造函数")
  void testDefaultConstructor() {
    FieldComponentExt component = new FieldComponentExt();
    assertNotNull(component);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试Map构造函数创建对象
   */
  @Test
  @DisplayName("测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = new HashMap<>();
    map.put(FieldComponentExt.TYPE, FieldComponentExt.TYPE_FIELD);
    map.put(FieldComponentExt.API_NAME, "testField");
    
    FieldComponentExt component = new FieldComponentExt(map);
    
    assertNotNull(component);
    assertEquals(FieldComponentExt.TYPE_FIELD, component.getType());
    assertEquals("testField", component.getApiName());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试buildNameColumn静态方法创建name列
   */
  @Test
  @DisplayName("测试buildNameColumn静态方法")
  void testBuildNameColumn() {
    FieldComponentExt nameColumn = FieldComponentExt.buildNameColumn();
    
    assertNotNull(nameColumn);
    assertEquals(IObjectData.NAME, nameColumn.getApiName());
    assertEquals(IFieldType.TEXT, nameColumn.getRenderType());
    assertEquals(FieldComponentExt.TYPE_FIELD, nameColumn.getType());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试type字段的getter和setter方法
   */
  @Test
  @DisplayName("测试type字段的getter和setter")
  void testTypeGetterAndSetter() {
    String testType = "testType";
    
    fieldComponentExt.setType(testType);
    
    assertEquals(testType, fieldComponentExt.getType());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试apiName字段的getter和setter方法
   */
  @Test
  @DisplayName("测试apiName字段的getter和setter")
  void testApiNameGetterAndSetter() {
    String testApiName = "testApiName";
    
    fieldComponentExt.setApiName(testApiName);
    
    assertEquals(testApiName, fieldComponentExt.getApiName());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试renderType字段的getter和setter方法
   */
  @Test
  @DisplayName("测试renderType字段的getter和setter")
  void testRenderTypeGetterAndSetter() {
    String testRenderType = "testRenderType";
    
    fieldComponentExt.setRenderType(testRenderType);
    
    assertEquals(testRenderType, fieldComponentExt.getRenderType());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试label字段的getter和setter方法
   */
  @Test
  @DisplayName("测试label字段的getter和setter")
  void testLabelGetterAndSetter() {
    String testLabel = "testLabel";
    
    fieldComponentExt.setLabel(testLabel);
    
    assertEquals(testLabel, fieldComponentExt.getLabel());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isShowLabel字段的getter和setter方法
   */
  @Test
  @DisplayName("测试isShowLabel字段的getter和setter")
  void testIsShowLabelGetterAndSetter() {
    Boolean testIsShowLabel = true;
    
    fieldComponentExt.setIsShowLabel(testIsShowLabel);
    
    assertEquals(testIsShowLabel, fieldComponentExt.getIsShowLabel());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isField方法当type为field时返回true
   */
  @Test
  @DisplayName("测试isField方法 - type为field时返回true")
  void testIsFieldWhenTypeIsField() {
    fieldComponentExt.setType(FieldComponentExt.TYPE_FIELD);
    
    assertTrue(fieldComponentExt.isField());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isField方法当type不为field时返回false
   */
  @Test
  @DisplayName("测试isField方法 - type不为field时返回false")
  void testIsFieldWhenTypeIsNotField() {
    fieldComponentExt.setType(FieldComponentExt.TYPE_TAG);
    
    assertFalse(fieldComponentExt.isField());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isTag方法当type为tag时返回true
   */
  @Test
  @DisplayName("测试isTag方法 - type为tag时返回true")
  void testIsTagWhenTypeIsTag() {
    fieldComponentExt.setType(FieldComponentExt.TYPE_TAG);
    
    assertTrue(fieldComponentExt.isTag());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isTag方法当type不为tag时返回false
   */
  @Test
  @DisplayName("测试isTag方法 - type不为tag时返回false")
  void testIsTagWhenTypeIsNotTag() {
    fieldComponentExt.setType(FieldComponentExt.TYPE_FIELD);
    
    assertFalse(fieldComponentExt.isTag());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试toTableColumn方法当isField为true时返回ITableColumn
   */
  @Test
  @DisplayName("测试toTableColumn方法 - isField为true时返回ITableColumn")
  void testToTableColumnWhenIsField() {
    try (MockedStatic<TableColumnExt> mockedTableColumnExt = mockStatic(TableColumnExt.class)) {
      // 准备测试数据
      fieldComponentExt.setType(FieldComponentExt.TYPE_FIELD);
      fieldComponentExt.setApiName("testApi");
      fieldComponentExt.setLabel("testLabel");
      fieldComponentExt.setRenderType("text");
      fieldComponentExt.setIsShowLabel(true);
      
      // Mock TableColumnExt
      TableColumnExt mockTableColumnExt = mock(TableColumnExt.class);
      ITableColumn mockTableColumn = mock(ITableColumn.class);
      
      mockedTableColumnExt.when(() -> TableColumnExt.of(anyString(), anyString(), anyString()))
          .thenReturn(mockTableColumnExt);
      when(mockTableColumnExt.getTableColumn()).thenReturn(mockTableColumn);
      
      // 执行测试
      ITableColumn result = fieldComponentExt.toTableColumn();
      
      // 验证结果
      assertNotNull(result);
      assertEquals(mockTableColumn, result);
      
      // 验证方法调用
      mockedTableColumnExt.verify(() -> TableColumnExt.of("testApi", "testLabel", "text"));
      verify(mockTableColumnExt).setIsShowLabel(true);
      verify(mockTableColumnExt).getTableColumn();
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试toTableColumn方法当isField为false时返回null
   */
  @Test
  @DisplayName("测试toTableColumn方法 - isField为false时返回null")
  void testToTableColumnWhenIsNotField() {
    fieldComponentExt.setType(FieldComponentExt.TYPE_TAG);
    
    ITableColumn result = fieldComponentExt.toTableColumn();
    
    assertNull(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试type为null时isField和isTag都返回false
   */
  @Test
  @DisplayName("测试type为null时的边界情况")
  void testNullType() {
    fieldComponentExt.setType(null);
    
    assertFalse(fieldComponentExt.isField());
    assertFalse(fieldComponentExt.isTag());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试常量值的正确性
   */
  @Test
  @DisplayName("测试常量值")
  void testConstants() {
    assertEquals("type", FieldComponentExt.TYPE);
    assertEquals("api_name", FieldComponentExt.API_NAME);
    assertEquals("render_type", FieldComponentExt.RENDER_TYPE);
    assertEquals("label", FieldComponentExt.LABEL);
    assertEquals("style", FieldComponentExt.STYLE);
    assertEquals("is_show_label", FieldComponentExt.IS_SHOW_LABEL);
    assertEquals("field", FieldComponentExt.TYPE_FIELD);
    assertEquals("tag", FieldComponentExt.TYPE_TAG);
  }
}
