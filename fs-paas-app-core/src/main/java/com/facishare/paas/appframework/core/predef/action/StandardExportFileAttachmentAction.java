package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.util.FileSizeConverter;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.metadata.util.XmlUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.QuoteFieldDescribe;
import com.facishare.paas.token.model.TokenInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2021/02/18
 */
public class StandardExportFileAttachmentAction extends AbstractExportAction<StandardExportFileAttachmentAction.Arg, StandardExportFileAttachmentAction.Result> {

    /**
     * 每次查询数据最大允许数量
     */
    public static final int DATA_BATCH_SIZE = 100;
    /**
     * 导出到网盘方式一： 单级目录
     */
    private static final int EXPORT_METHOD_ONE_LAYER = 1;
    /**
     * 导出到网盘方式二： 三级目录
     */
    private static final int EXPORT_METHOD_THREE_LAYER = 2;
    public static final String EXPORT_FILE = "export_file";

    /* 导出方式 */
    private int method;
    /* 导出字段 */
    protected List<IFieldDescribe> fieldDescribeToExport = Lists.newArrayList();

    protected List<IObjectData> objectDataList = Lists.newArrayList();

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.ExportFileAttachment.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    @Override
    protected void init() {
        super.init();
        method = arg.getMethod();
        fieldDescribeToExport = getFieldDescribeToExport();
    }

    private List<IFieldDescribe> getFieldDescribeToExport() {
        //需要导出的字段apiName
        List<IFieldDescribe> fieldDescribes = ObjectDescribeExt.of(objectDescribe).getFieldByApiNames(arg.getFieldApiNames()).stream()
                .map(field -> {
                    if (IFieldType.QUOTE.equals(field.getType())) {
                        String type = ((QuoteFieldDescribe) field).getQuoteFieldType();
                        Map<String, Object> map = Maps.newHashMap(FieldDescribeExt.of(field).toMap());
                        map.put(IFieldDescribe.TYPE, type);
                        return FieldDescribeFactory.newInstance(map);
                    }
                    return field;
                }).collect(Collectors.toList());
        if (CollectionUtils.empty(fieldDescribes)) {
            return fieldDescribes;
        }
        List<String> needFilterGdprFields = infraServiceFacade.needFilterGdprFields(actionContext.getUser(), objectDescribe.getApiName(), ObjectAction.BATCH_EXPORT.getActionCode());
        fieldDescribes.removeIf(x -> needFilterGdprFields.contains(x.getApiName()));
        if (CollectionUtils.empty(fieldDescribes)) {
            throw new MetaDataBusinessException(I18NExt.getOrDefault(I18NKey.GDPR_EXPORT_NOT_SUPPORT, "GDPR不支持导出该字段"));// ignoreI18n
        }
        return fieldDescribes;
    }

    public void calculateFileSize(List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        for (IFieldDescribe fieldDescribe : fieldDescribeToExport) {
            for (IObjectData data : dataList) {
                Object o = data.get(fieldDescribe.getApiName());
                if (!(o instanceof List)) {
                    continue;
                }
                List<ExportFile> exportFiles;
                try {
                    exportFiles = JSONArray.parseArray(o.toString(), ExportFile.class);
                } catch (Exception e) {
                    continue;
                }
                if (CollectionUtils.empty(exportFiles)) {
                    continue;
                }
                for (ExportFile exportFile : exportFiles) {
                    Long size = exportFile.getSize();
                    if (Objects.nonNull(size)) {
                        exportFileSize += size;
                    } else if (!FieldDescribeExt.of(fieldDescribe).isSignatureField()) {
                        exportFileSize += 1024 * 1024 * 3L;
                    }
                    if (exportFileSize > exportFileMaxSize) {
                        throw new ValidateException(I18NExt.getOrDefault(I18NKey.EXPORT_FILE_MAX_SIZE_ERROR,
                                "导出图片附件大小{0},超过最大允许大小{1},请减少数据量后重新操作.", // ignoreI18n
                                FileSizeConverter.convertFileSize(exportFileSize), FileSizeConverter.convertFileSize(exportFileMaxSize)));
                    }
                }
            }
        }
    }

    @Override
    protected String getToken(Arg arg) {
        return arg.getToken();
    }

    @Override
    protected void customInit() {

    }

    @Override
    protected boolean hasToken() {
        return StringUtils.isNotBlank(arg.getToken());
    }

    @Override
    protected String getSearchTemplateId() {
        return arg.getSearchTemplateId();
    }

    @Override
    protected String getSearchQueryInfo() {
        return arg.getSearchQueryInfo();
    }

    @Override
    protected List<String> getDataIdList() {
        return arg.getDataIdList();
    }

    @Override
    protected String getSearchTemplateType() {
        return arg.getSearchTemplateType();
    }

    @Override
    protected boolean isIgnoreSceneFilter() {
        return arg.isIgnoreSceneFilter();
    }

    @Override
    protected boolean isIgnoreSceneRecordType() {
        return arg.isIgnoreSceneRecordType();
    }

    @Override
    protected boolean isNoExportRelevantTeam() {
        return true;
    }

    @Override
    protected void doExport() {
        log.warn("export to local start,tenantId:{},arg:{}", actionContext.getTenantId(), arg);
        queryData();
        String folder;
        try {
            folder = createFolder();
        } catch (AppBusinessException e) {
            log.warn("createFolder failed! tenantId:{},arg:{}", actionContext.getTenantId(), arg, e);
            String traceId = TraceContext.get().getTraceId();
            TokenInfo tokenInfo = TokenInfo.buildError(token, "srcTrace:" + traceId + ERROR_SPLIT + e.getMessage());
            tokenService.complete(tokenInfo, tokenExpireSeconds);
            return;
        } catch (Exception e) {
            log.error("createFolder fail, ei:{}, apiName:{}", actionContext.getTenantId(), actionContext.getObjectApiName(), e);
            TokenInfo tokenInfo = TokenInfo.buildError(token, "createFolder fail!");
            tokenService.complete(tokenInfo, tokenExpireSeconds);
            return;
        }
        TokenInfo tokenInfo = TokenInfo.buildSuccess(token, folder);
        tokenService.complete(tokenInfo, tokenExpireSeconds);
    }

    private String createFolder() {
        String xml = null;
        if (method == EXPORT_METHOD_ONE_LAYER) {
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.EXPORT_FILE_NAME_SUPPORT_RENAME, actionContext.getTenantId())) {
                xml = XmlUtil.create1LayerXml(objectDescribe, fieldDescribeToExport, objectDataList, true);
            } else {
                xml = XmlUtil.create1LayerXml(objectDescribe, fieldDescribeToExport, objectDataList);
            }
        } else if (method == EXPORT_METHOD_THREE_LAYER) {
            xml = XmlUtil.create3LayerXml(objectDescribe, fieldDescribeToExport, objectDataList);
        }
        log.info("tenantId:{}, userId:{}, xml:{}", actionContext.getTenantId(), actionContext.getUser().getUserId(), xml);
        // 不需要判断，导出的数据中有没有图片|附件。统一都打包
        infraServiceFacade.packedFile(actionContext.getUser(), xml, arg.getJobId(), null, skipDuplicatedFile());
        return EXPORT_FILE + "|" + 0;
    }

    @Override
    protected void consumerDataList(List<IObjectData> dataList) {
        calculateFileSize(dataList);
        // 获取文件名称
        convertImageAndAttachFile(dataList);
        objectDataList.addAll(dataList.stream().map(this::copyData).collect(Collectors.toList()));
    }


    protected boolean skipDuplicatedFile() {
        if (UdobjGrayConfig.isAllow("export_skip_duplicated_file_gray", actionContext.getTenantId())) {
            return true;
        }
        return arg.isSkipDuplicatedFile();
    }

    /**
     * 一份简版的 data 只保留 name 和需要导出的图片附件字段
     *
     * @param data
     * @return
     */
    private IObjectData copyData(IObjectData data) {
        IObjectData newData = new ObjectData();
        newData.setName(data.getName());
        fieldDescribeToExport.stream()
                .map(IFieldDescribe::getApiName)
                .forEach(fieldName -> newData.set(fieldName, data.get(fieldName)));
        return newData;
    }

    private void convertImageAndAttachFile(List<IObjectData> dataList) {

        for (IFieldDescribe fieldDescribe : fieldDescribeToExport) {
            for (IObjectData data : dataList) {
                Object value = infraServiceFacade.convertData(data, fieldDescribe, actionContext.getUser());
                data.set(fieldDescribe.getApiName(), value);
            }
        }
    }

    @Override
    protected int getDataBatchSize() {
        return DATA_BATCH_SIZE;
    }

    @Override
    protected Result generateResult(String token) {
        TokenInfo tokenInfo = tokenService.query(token);
        if (tokenInfo == null) {
            throw new MetaDataException("token not exist!");
        }
        int num = Optional.ofNullable(tokenInfo.getProgress()).orElse(0);
        if (tokenInfo.isOngoing()) {
            return StandardExportFileAttachmentAction.Result.builder()
                    .ext("zip")
                    .token(token)
                    .path("")
                    .totalCount(totalCount)
                    .currentCount(num)
                    .exportType(EXPORT_FILE)
                    .build();
        }

        if (tokenInfo.isSuccess()) {
            if (null == objectDescribe) {
                objectDescribe = serviceFacade.findObject(actionContext.getTenantId(), actionContext.getObjectApiName());
            }
            String pathAndSize = tokenInfo.getResult();
            String[] arr = pathAndSize.split("\\|");
            String fileName = getFileName();
            return Result.builder()
                    .ext("zip")
                    .token(token)
                    .path(arr[0])
                    .size(Long.parseLong(arr[1]))
                    .totalCount(totalCount)
                    .exportType("export_file")
                    .fileName(fileName)
                    .currentCount(num).build();
        }

        if (tokenInfo.isError()) {
            String errorMsg = tokenInfo.getMessage();
            if (StringUtils.isNotBlank(errorMsg) && errorMsg.contains(ERROR_SPLIT)) {
                String message = StringUtils.substringAfter(errorMsg, ERROR_SPLIT);
                throw new MetaDataBusinessException(message);
            }
            throw new MetaDataException("export to local failed! " + errorMsg);
        }
        throw new MetaDataException("export to local failed!");
    }

    @Data
    public static class Arg {
        @JSONField(name = "object_describe_api_name")
        @JsonProperty(value = "object_describe_api_name")
        @SerializedName(value = "object_describe_api_name")
        String describeApiName;

        @JSONField(name = "dataIdList")
        @JsonProperty(value = "dataIdList")
        @SerializedName(value = "dataIdList")
        List<String> dataIdList;

        @JSONField(name = "search_template_id")
        @JsonProperty(value = "search_template_id")
        @SerializedName(value = "search_template_id")
        String searchTemplateId;

        @JSONField(name = "search_query_info")
        @JsonProperty(value = "search_query_info")
        @SerializedName(value = "search_query_info")
        String searchQueryInfo;

        @JsonProperty(value = "search_template_type")
        private String searchTemplateType;

        @JsonProperty("ignore_scene_filter")
        private boolean isIgnoreSceneFilter;

        @JsonProperty("ignore_scene_record_type")
        private boolean isIgnoreSceneRecordType;

        @JSONField(name = "method")
        @JsonProperty(value = "method")
        @SerializedName(value = "method")
        int method;

        @JSONField(name = "field_api_name_list")
        @JsonProperty(value = "field_api_name_list")
        @SerializedName(value = "field_api_name_list")
        List<String> fieldApiNames;

        @JsonProperty(value = "skip_duplicated_file")
        private boolean skipDuplicatedFile;

        /**
         * 任务 token 用于异步导出
         */
        private String token;

        private String jobId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        @JSONField(name = "token")
        private String token;

        private String ext;

        private String path;

        @JsonProperty("total_count")
        private int totalCount;

        private int currentCount;

        @JsonProperty("export_type")
        private String exportType;

        @JsonProperty("file_name")
        private String fileName;

        private long size;
    }

    @Data
    public static class ExportFile {
        @JSONField(name = "ext")
        String extension;
        @JSONField(name = "path")
        String path;
        @JSONField(name = "filename")
        String fileName;
        @JSONField(name = "size")
        Long size;
    }
}
