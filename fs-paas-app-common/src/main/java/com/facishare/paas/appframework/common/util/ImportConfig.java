package com.facishare.paas.appframework.common.util;

import com.github.autoconf.ConfigFactory;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/8/5 18:04
 */
public abstract class ImportConfig {

    @Getter
    private static int exportPageSize = 200;
    @Getter
    private static int tokenExpireSeconds = 3600;
    @Getter
    private static int exportRowsThrottle = 100000;
    @Getter
    private static int exportRowsThrottleVip = 150000;
    @Getter
    private static int exportMDThrottle = 30000;
    @Getter
    private static int exportFileExpireDay = 15;
    @Getter
    private static int exportPrintTemplateExcelThrottle = 100;
    @Getter
    private static int exportPrintTemplateExcelThrottleVip = 150;
    @Getter
    private static int exportSplitSize = 200000;
    @Getter
    private static int exportFileAttachmentThrottle = 5000;
    @Getter
    private static int exportFileAttachmentThrottleVip = 10000;
    @Getter
    private static long exportXmlMaxLength = 1024 * 1024 * 8L;
    @Getter
    private static long exportFileMaxSize = 1024 * 1024 * 1024 * 10L;
    @Getter
    private static long embeddedExcelImageMaxSize = 1024 * 1024 * 80L;


    static {
        ConfigFactory.getConfig("fs-crm-object-import-config", iConfig -> {
            exportPageSize = iConfig.getInt("export_page_size", 200);
            tokenExpireSeconds = iConfig.getInt("tokenExpireSeconds", 3600);
            exportRowsThrottle = iConfig.getInt("exportRowsThrottle", 100000);
            exportRowsThrottleVip = iConfig.getInt("exportRowsThrottleVip", 150000);
            exportMDThrottle = iConfig.getInt("exportMDThrottle", 30000);
            exportFileExpireDay = iConfig.getInt("exportFileExpireDay", 15);
            exportPrintTemplateExcelThrottle = iConfig.getInt("exportPrintTemplateExcelThrottle", 100);
            exportSplitSize = iConfig.getInt("exportSplitSize", 200000);
            exportFileAttachmentThrottle = iConfig.getInt("exportFileAttachmentThrottle", 5000);
            exportFileAttachmentThrottleVip = iConfig.getInt("exportFileAttachmentThrottleVip", 10000);
            exportXmlMaxLength = iConfig.getLong("exportXmlMaxLength", 1024 * 1024 * 8L);
            exportFileMaxSize = iConfig.getLong("exportFileMaxSize", 1024 * 1024 * 1024 * 10L);
            embeddedExcelImageMaxSize = iConfig.getLong("embeddedExcelImageMaxSize", 1024 * 1024 * 80L);
        });
    }

    public static Long getFileExpiredTimeWithNow() {
        return System.currentTimeMillis() + getExportFileExpireDay() * 24 * 60 * 60 * 1000L;
    }
}
