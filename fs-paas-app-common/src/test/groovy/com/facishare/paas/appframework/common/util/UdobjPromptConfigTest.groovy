package com.facishare.paas.appframework.common.util

import com.facishare.paas.appframework.common.service.model.AIFormulaConfigDTO
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.github.autoconf.ConfigFactory
import com.github.autoconf.api.IConfig
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

/**
 * GenerateByAI
 * 测试内容描述：UdobjPromptConfig工具类的单元测试
 */
class UdobjPromptConfigTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getFormulaPrompt方法
     */
    def "测试getFormulaPrompt方法获取AI公式提示词"() {
        when: "调用getFormulaPrompt方法"
        def prompt = UdobjPromptConfig.getFormulaPrompt()
        
        then: "应返回预设的或已加载的提示词"
        prompt != null
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试配置加载过程
     */
    def "测试配置加载逻辑"() {
        given: "保存原始提示词"
        def originalPrompt = UdobjPromptConfig.getFormulaPrompt()
        
        and: "模拟配置加载回调"
        def config = Mock(IConfig)
        def newPrompt = "新的AI公式提示词"
        config.getString() >> newPrompt
        
        when: "手动调用配置加载回调"
        Whitebox.setInternalState(UdobjPromptConfig, "AI_FORMULA_PROMPT", AIFormulaConfigDTO.GENERATE_EXPRESSION)
        def callback = Whitebox.getInternalState(UdobjPromptConfig.class, "AI_FORMULA_PROMPT_FILE")
        if (callback instanceof String) {
            ConfigFactory.getConfig(callback).changes().accept(config)
        }
        
        and: "再次获取提示词"
        def updatedPrompt = UdobjPromptConfig.getFormulaPrompt()
        
        then: "应加载新的提示词"
        updatedPrompt == newPrompt
        
        cleanup: "恢复原始提示词"
        Whitebox.setInternalState(UdobjPromptConfig, "AI_FORMULA_PROMPT", originalPrompt)
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试配置加载异常处理
     */
    def "测试配置加载异常处理"() {
        given: "保存原始提示词"
        def originalPrompt = UdobjPromptConfig.getFormulaPrompt()
        
        and: "模拟配置加载异常"
        def config = Mock(IConfig)
        config.getString() >> { throw new RuntimeException("模拟加载异常") }
        
        when: "手动调用配置加载回调"
        Whitebox.setInternalState(UdobjPromptConfig, "AI_FORMULA_PROMPT", AIFormulaConfigDTO.GENERATE_EXPRESSION)
        def callback = Whitebox.getInternalState(UdobjPromptConfig.class, "AI_FORMULA_PROMPT_FILE")
        if (callback instanceof String) {
            try {
                ConfigFactory.getConfig(callback).changes().accept(config)
            } catch (Exception e) {
                // 忽略异常，应由UdobjPromptConfig内部处理
            }
        }
        
        and: "再次获取提示词"
        def updatedPrompt = UdobjPromptConfig.getFormulaPrompt()
        
        then: "应保留原始提示词"
        updatedPrompt == AIFormulaConfigDTO.GENERATE_EXPRESSION
        
        cleanup: "恢复原始提示词"
        Whitebox.setInternalState(UdobjPromptConfig, "AI_FORMULA_PROMPT", originalPrompt)
    }
} 