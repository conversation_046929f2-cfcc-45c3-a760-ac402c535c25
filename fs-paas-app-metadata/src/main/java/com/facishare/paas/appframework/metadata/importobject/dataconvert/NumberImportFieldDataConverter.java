package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.NumberExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.describe.NumberFieldDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * create by z<PERSON><PERSON> on 2019/07/26
 */
@Slf4j
@Component
public class NumberImportFieldDataConverter extends BaseImportFieldDataConverter {
    @Override
    public List<String> getSupportedFieldTypes() {
        return Lists.newArrayList(IFieldType.NUMBER, IFieldType.CURRENCY);
    }

    @Override
    public ConvertResult convertFieldData(IObjectData objectData, IFieldDescribe fieldDescribe, User user) {
        String stringValue = getStringValue(objectData, fieldDescribe.getApiName());
        NumberExt numberExt = NumberExt.of((NumberFieldDescribe) fieldDescribe);
        if (numberExt.checkNumberDigit(stringValue)) {
            if (Strings.isNullOrEmpty(stringValue)) {
                return ConvertResult.buildSuccess(null);
            }
            return ConvertResult.buildSuccess(numberExt.convertValue(stringValue));
        }

        if (IFieldType.CURRENCY.equals(fieldDescribe.getType())) {
            return ConvertResult.buildError(I18N.text(I18NKey.FORMAT_ERROR_PLEASE_INPUT_AMOUNT,
                    fieldDescribe.getLabel(), numberExt.getLength(), numberExt.getDecimalPlaces()));
        }

        if (numberExt.getDecimalPlaces() == 0) {
            //整形
            return ConvertResult.buildError(I18N.text(I18NKey.FORMAT_ERROR_PLEASE_INPUT_INTEGER,
                    fieldDescribe.getLabel(), numberExt.getLength()));
        }
        return ConvertResult.buildError(I18N.text(I18NKey.FORMAT_ERROR_PLEASE_INPUT_DECIMAL,
                fieldDescribe.getLabel(), numberExt.getLength(), numberExt.getDecimalPlaces()));
    }

}
