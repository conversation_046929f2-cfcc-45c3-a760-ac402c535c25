package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.core.model.ControllerListener;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController.Arg;
import com.facishare.paas.appframework.core.predef.listener.prm.PrmDetailControllerListener;

import java.util.List;


/**
 * 标准详情页接口
 * <p>
 * Created by liyiguang on 2017/7/3.
 */
public class StandardDetailController extends AbstractStandardDetailController<Arg> {

    @Override
    public List<Class<? extends ControllerListener<Arg, Result>>> getControllerListenerClassList() {
        List<Class<? extends ControllerListener<Arg, Result>>> controllerListenerClassList = super.getControllerListenerClassList();
        if (AppIdMapping.isPRM(controllerContext.getAppId())) {
            controllerListenerClassList.add(PrmDetailControllerListener.class);
        }
        return controllerListenerClassList;
    }

}
