package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.TagDocument;
import com.facishare.paas.appframework.core.model.TagGroupDocument;
import com.facishare.paas.appframework.core.predef.service.dto.switchcache.FindSystemTagGroupByApplyObject;
import com.facishare.paas.appframework.core.predef.service.dto.tag.*;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.TagLogicService;
import com.facishare.paas.appframework.metadata.dto.scene.SceneDTO;
import com.facishare.paas.metadata.api.DataAndSubTag;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.ISubTagDescribe;
import com.facishare.paas.metadata.api.describe.ITagDescribe;
import com.facishare.paas.metadata.api.search.Ranges;
import com.facishare.paas.metadata.impl.describe.SubTagDescribe;
import com.facishare.paas.metadata.impl.search.TagFilter;
import com.facishare.paas.metadata.impl.search.TagOperator;
import com.facishare.paas.metadata.impl.search.TagOrder;
import com.facishare.paas.metadata.impl.search.TagQueryInfo;
import com.facishare.paas.reference.data.EntityReferenceArg;
import com.facishare.paas.reference.service.EntityReferenceService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.i18n.I18NKey.*;
import static java.util.stream.Collectors.*;

/**
 * <AUTHOR>
 * @date 2020/2/11 3:51 下午
 */
@ServiceModule("tag")
@Service
@Slf4j
public class TagService {
    @Autowired
    private TagLogicService tagLogicService;
    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private EntityReferenceService entityReferenceService;

    @Autowired
    private OrgService orgService;

    private static final Object lock = new Object();

    public static final String DEFAUL_TAG_GROUP = "default_group";

    public static String pattern = "^[A-Za-z]([A-Za-z0-9]|_(?!_))+(__c)$";

    public static final String TAG_TYPE_CUSTOM = "custom";

    public static final String TAG_TYPE_SYSTEM = "system";

    public static final String TAG_MANAGEMENT = "tag_management";
    /**
     * 查询所有标签，支持模糊查询
     */
    @ServiceMethod("findAllTags")
    public ListAllTags.Result findAllTagsByDescribeApiName(ListAllTags.Arg arg, ServiceContext context) {
        // 如果是老终端(700或者705)，返回空，终端应该升级到710才能查到标签数据
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_710) && Objects.isNull(arg.getDescribeApiName())) {
            return ListAllTags.Result.builder().build();
        }
        // 从paas查询指定对象下的所有标签并根据标签组分组(以前就有的逻辑，只是整理了一下，没有改原逻辑)
        Boolean isActive = Objects.isNull(arg.getIsActive()) ? null : !arg.getIsActive() ? null : true;
        List<ISubTagDescribe> tags = tagLogicService.findAllTags(arg.getDescribeApiName(),
                context.getTenantId(), arg.getName(), isActive);
        if (CollectionUtils.empty(tags)) {
            return ListAllTags.Result.builder().build();
        }
        Map<String, List<ISubTagDescribe>> tagGroupMap = tags.stream().collect(groupingBy(ISubTagDescribe::getTagId));
        // 查询标签分组信息
        List<ITagDescribe> groups = tagLogicService.findTagGroupsByIds(context.getTenantId(), tagGroupMap.keySet());

        // 组装结果
        List<ListAllTags.TagDetail> details = Lists.newArrayList();
        for (ITagDescribe group : groups) {
            //人工打标签不能打系统级标签
            if (BooleanUtils.isTrue(arg.getIsExcludeSystem()) && TAG_TYPE_SYSTEM.equals(group.getTagDefineType())
                    || !arg.getIncludeDisableTagGroup() && !group.getIsActive()) {
                continue;
            }
            List<ISubTagDescribe> ts = tagGroupMap.get(group.getId());
            if (!CollectionUtils.empty(ts)) {
                ListAllTags.TagDetail detail = ListAllTags.TagDetail.builder()
                        .tagGroupName(group.getType())
                        .defineType(group.getTagDefineType())
                        .tags(TagDocument.ofList(ts)).build();
                details.add(detail);
            }
        }
        return ListAllTags.Result.builder().groups(details).build();
    }

    /**
     * 查询指定数据已打标签
     */
    @ServiceMethod("findDataTags")
    public FindDataTags.Result findDataTags(FindDataTags.Arg arg, ServiceContext context) {
        // 查询数据绑定的标签
        List<ISubTagDescribe> tags = tagLogicService.findTagsByDataId(arg.getDescribeApiName(), arg.getDataId(),
                ActionContextExt.of(context.getUser()).getContext());
        if (CollectionUtils.empty(tags)) {
            return FindDataTags.Result.builder().build();
        }
        // 查询标签组名称
        List<ITagDescribe> tagGroups = tagLogicService.findTagGroupsByIds(context.getTenantId(),
                tags.stream().map(ISubTagDescribe::getTagId).collect(toSet()));
        // 构造结果
        List<FindDataTags.TagGroup> groups = Lists.newArrayList();
        for (ITagDescribe tagGroup : tagGroups) {
            List<FindDataTags.TagDetail> tagDetails = Lists.newArrayList();
            tags.stream().filter(t -> tagGroup.getId().equals(t.getTagId()))
                    .map(t -> new FindDataTags.TagDetail(t.getName(), t.getId(), t.getIsActive() && t.getTagDescribeIsActive()))
                    .forEach(tagDetails::add);
            FindDataTags.TagGroup group = FindDataTags.TagGroup.builder().tagGroupId(tagGroup.getId())
                    .tagGroupName(tagGroup.getType())
                    .tags(tagDetails)
                    .tagDefineType(tagGroup.getTagDefineType())
                    .build();
            groups.add(group);
        }
        return FindDataTags.Result.builder().groups(groups).build();
    }


    // ---------------------------------------- 标签后台管理使用接口

    public FindTagGroups.Result findTagInfo(FindTagGroups.Arg arg, ServiceContext context, boolean isReturnSubTagInfo) {
        Map<String, Long> tagCountMap;
        List<ITagDescribe> tagGroups;

        boolean isOnTime = false;
        if(TAG_MANAGEMENT.equals(arg.getSourceInfo())){
            isOnTime = true;
        }

        if (Objects.nonNull(arg.getQueryInfo()) && CollectionUtils.notEmpty(arg.getQueryInfo().getConditions())) {
            TagQueryInfo tagQueryInfo = covertQueryInfo(arg.getQueryInfo(), null);
            tagCountMap = tagLogicService.batchQuerySubTagCount(context.getTenantId(), tagQueryInfo);
            tagGroups = tagLogicService.findTagGroupByQueryInfo(context.getTenantId(), tagQueryInfo, isOnTime);
            Set<String> filterTagId = tagCountMap.keySet();
            tagGroups.removeIf(x -> !filterTagId.contains(x.getId()));
        } else {
            tagGroups = tagLogicService.findTagGroups(context.getTenantId(), isOnTime);
            tagCountMap = tagLogicService.findTagCountByGroup(context.getTenantId());
        }
        Map<String, List<ISubTagDescribe>> subTagMap = Maps.newHashMap();
        if (isReturnSubTagInfo) {
            subTagMap = tagLogicService.bulkFindSubTagByTagIds(context, tagGroups.stream().map(ITagDescribe::getId).collect(toList()), isOnTime);
        }

        // 对标签组按照创建时间排序
        tagGroups.sort(Comparator.comparing(t -> Objects.isNull(t.getCreateTime()) ? Long.valueOf(0L) : t.getCreateTime()));

        // 查询适用对象的信息
        List<String> describeApiNames = tagGroups.stream()
                .flatMap(t -> CollectionUtils.nullToEmpty(t.getTagRange()).stream()).distinct()
                .collect(toList());
        List<IObjectDescribe> describes = describeLogicService.findDescribeListWithoutFields(context.getTenantId(), describeApiNames);
        // 构造返回结果
        List<FindTagGroups.Group> resultGroups = Lists.newArrayList();
        for (ITagDescribe tg : tagGroups) {
            List<String> range = tg.getTagRange();
            List<ObjectDescribeDocument> documents = Lists.newArrayList();
            if (!CollectionUtils.empty(range)) {
                documents = describes.stream().filter(d -> range.contains(d.getApiName()))
                        .map(ObjectDescribeDocument::of).collect(toList());
            }
            resultGroups.add(FindTagGroups.Group.builder()
                    .tagGroupId(tg.getId())
                    .tagGroupName(tg.getType())
                    .defineType(tg.getTagDefineType())
                    .isAppliedToAll(tg.getIsAll())
                    .tagCount(Objects.isNull(tagCountMap.get(tg.getId())) ? Long.valueOf(0L) : tagCountMap.get(tg.getId()))
                    .targetDescribes(documents)
                    .tagGroupApiName(tg.getApiName())
                    .active(tg.getIsActive())
                    .all(tg.getIsAll())
                    .labelNames(CollectionUtils.notEmpty(subTagMap) ? buildLabelName(subTagMap.get(tg.getId()), tg.getApiName()) : Lists.newArrayList())
                    .ranges(SceneDTO.Range.fromRanges(tg.getRange()))
                    .mutex(tg.getIsMutex())
                    .groupDescription(tg.getGroupDescription())
                    .build());
        }

        return FindTagGroups.Result.builder().groups(resultGroups).build();
    }


    /**
     * 根据对象，获取所有分组以及各个分组下标签数量
     * 如果没有默认分组，则创建默认分组
     */
    @ServiceMethod("findTagGroups")
    public FindTagGroups.Result findTagGroups(FindTagGroups.Arg arg, ServiceContext context) {
        return findTagInfo(arg, context, false);
    }

    @ServiceMethod("findTagAndSubTag")
    public FindTagGroups.Result findTagAndSubTag(FindTagGroups.Arg arg, ServiceContext context) {
        return findTagInfo(arg, context, true);
    }

    private List<CreateOrUpdateTag.Arg> buildLabelName(List<ISubTagDescribe> subTagDescribeList, String tabGroupApiName) {
        if (CollectionUtils.empty(subTagDescribeList)) {
            return Lists.newArrayList();
        }
        List<CreateOrUpdateTag.Arg> resultList = Lists.newArrayList();
        for (ISubTagDescribe iSubTagDescribe : subTagDescribeList) {
            resultList.add(CreateOrUpdateTag.Arg.builder()
                    .tagName(iSubTagDescribe.getName())
                    .tagApiName(iSubTagDescribe.getTagApiName())
                    .enable(iSubTagDescribe.getIsActive())
                    .groupApiName(tabGroupApiName)
                    .build());
        }
        return resultList;

    }


    /**
     * 新建标签分组
     */
    @ServiceMethod("createTagGroup")
    public CreateTagGroup.Result createTagGroup(CreateTagGroup.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getApiName()) || !arg.getApiName().matches(pattern) || arg.getTagGroupName().length() > 10) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        if (arg.getIsAppliedToAll()) {
            arg.setDescribeApiNames(Lists.newArrayList());
        }
        //如果是标签组类型是人工打标签，则将适用范围和是否互斥属性置空
        if (TAG_TYPE_CUSTOM.equals(arg.getTagDefineType())) {
            arg.setRanges(new SceneDTO.Range());
            arg.setIsMutex(null);
        }
        List<CreateOrUpdateTag.Arg> tabInfo = arg.getTagInfoList();
        List<ISubTagDescribe> subTagDescribeList = Lists.newArrayList();
        for (CreateOrUpdateTag.Arg tagInfo : tabInfo) {
            ISubTagDescribe tag = new SubTagDescribe();
            tag.setGrade(1);
            tag.setTagApiName(tagInfo.getTagApiName());
            tag.setName(tagInfo.getTagName());
            tag.setIsActive(tagInfo.getEnable());
            tag.setTenantId(context.getTenantId());
            tag.setDescription(tagInfo.getDescription());
            subTagDescribeList.add(tag);
        }
        ITagDescribe tagGroup = tagLogicService.createTagGroup(
                arg.getTagGroupName(),
                arg.getApiName(),
                arg.getDescribeApiNames(),
                arg.getIsAppliedToAll(),
                subTagDescribeList,
                arg.getTagDefineType(),
                arg.getRanges().toRanges(),
                arg.getIsMutex(),
                arg.getGroupDescription(),
                context.getUser());
        return CreateTagGroup.Result.builder().tagDescribe(tagGroup).build();
    }

    /**
     * 更新标签分组名称
     */
    @ServiceMethod("updateTagGroup")
    public UpdateTagGroup.Result updateTagGroup(UpdateTagGroup.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getApiName()) || !arg.getApiName().matches(pattern) || arg.getTagGroupName().length() > 10) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        if (arg.getIsAppliedToAll()) {
            arg.setDescribeApiNames(Lists.newArrayList());
        }
        ITagDescribe dbTagGroup = tagLogicService.findTagGroupsByIds(context.getTenantId(),
                Lists.newArrayList(arg.getTagGroupId())).stream().findFirst().orElse(null);
        if (Objects.isNull(dbTagGroup)) {
            throw new ValidateException(I18NExt.text(I18NKey.TAG_GROUP_DELETED));
        }
        //不能改变标签类型
        if (!StringUtils.equals(dbTagGroup.getTagDefineType(), arg.getTagDefineType())) {
            throw new ValidateException(I18NExt.text(I18NKey.TAG_GROUP_TYPE_IS_NOT_CHANGE));
        }
        //校验被规则绑定的适用对象是否改变
        if (TAG_TYPE_SYSTEM.equals(dbTagGroup.getTagDefineType())) {
            validateTagRange(arg.getDescribeApiNames(), dbTagGroup.getTagRange(), dbTagGroup.getApiName(), context);
        }
        dbTagGroup.setId(arg.getTagGroupId());
        dbTagGroup.setGroupDescription(arg.getGroupDescription());
        dbTagGroup.setType(arg.getTagGroupName());
        dbTagGroup.setApiName(arg.getApiName());
        dbTagGroup.setTagRange(arg.getDescribeApiNames());
        dbTagGroup.setIsAll(arg.getIsAppliedToAll());
        dbTagGroup.setTenantId(context.getTenantId());
        Ranges ranges = Objects.nonNull(arg.getRanges()) ? arg.getRanges().toRanges() : new Ranges();
        dbTagGroup.setRanges(ranges);
        dbTagGroup.setIsMutex(arg.getIsMutex());
        ITagDescribe iTagDescribe = tagLogicService.updateTagGroupName(dbTagGroup, context.getUser());
        return UpdateTagGroup.Result.builder().tagDescribe(iTagDescribe).build();
    }

//    private void validateSubTagList(UpdateTagGroup.Arg arg, String tenantId) {
//        List<ISubTagDescribe> subTagList = tagLogicService.findTagsByTagGroupId(arg.getTagGroupId(), tenantId);
//        Map<String, ISubTagDescribe> tagMap = Maps.newHashMap();
//        subTagList.forEach(x -> tagMap.put(x.getTagApiName(), x));
//        List<String> dbSubTagApiNameList = subTagList.stream().map(ISubTagDescribe::getTagApiName).collect(toList());
//        List<String> subTagApiNameList = arg.getLabelNames().stream().map(CreateOrUpdateTag.Arg::getTagApiName).collect(toList());
//        //过滤出被删除为禁用的标签
//        Set<String> difference = Sets.difference(Sets.newHashSet(dbSubTagApiNameList), Sets.newHashSet(subTagApiNameList));
//        List<ISubTagDescribe> enableTag = difference.stream().filter(x -> tagMap.get(x).getIsActive()).map(tagMap::get).collect(toList());
//        if (CollectionUtils.notEmpty(enableTag)) {
//            StringBuilder enableTagStr = new StringBuilder();
//            enableTag.forEach(x -> enableTagStr.append(x.getName()).append(","));
//            enableTagStr.deleteCharAt(enableTagStr.length() - 1);
//            throw new ValidateException(I18NExt.text(I18NKey.TAG_NOT_DISABLE, enableTagStr));
//        }
//    }

    @ServiceMethod("enableOrDisableTagGroup")
    public UpdateTagGroupState.Result enableOrDisableTagGroup(UpdateTagGroupState.Arg arg, ServiceContext context) {
        ITagDescribe group = tagLogicService.findTagGroupByApiName(arg.getGroupApiName(), context.getUser());
        if (Objects.isNull(group)) {
            throw new ValidateException(I18NExt.text(I18NKey.TAG_GROUP_DELETED));
        }
        if (TAG_TYPE_SYSTEM.equals(group.getTagDefineType()) && BooleanUtils.isFalse(arg.getEnable())) {
            validateTagGroupQuoteRule(context, group);
        }
        tagLogicService.enableOrDisableTagGroup(context, Lists.newArrayList(group.getId()), BooleanUtils.isTrue(arg.getEnable()));
        return UpdateTagGroupState.Result.builder().success(true).build();
    }


    private void validateTag(String groupApiName, String tagId, String tenantId) {
        validate(groupApiName + "." + tagId, EntityReferenceService.REFOBJ_TAG_GROUP_TAG, tenantId, TAG, Lists.newArrayList(), Lists.newArrayList());
        validate("tag." + tagId, EntityReferenceService.REFOBJ_TAG_GROUP_TAG, tenantId, TAG, Lists.newArrayList(), Lists.newArrayList());

    }

    /**
     * @param targetValue          引用关系的值
     * @param targetType           引用关系的类型
     * @param tenantId
     * @param typeI18n             提示信息类型（标签｜｜标签分组）
     * @param diffDescribeApiNames 针对变更标签组适用对象，被删除的对象
     */
    private void validate(String targetValue, String targetType, String tenantId, String typeI18n, Collection<String> diffDescribeApiNames, List<String> targetValueList) {
        List<EntityReferenceArg> ruleList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(targetValueList)) {
            ruleList = entityReferenceService.findByTargetTypeMatchTypeAndTargetValues(tenantId, targetType, targetValueList, EntityReferenceService.MatchType.CONTAINS, 5);
        } else {
            ruleList = entityReferenceService.findByTargetTypeAndTargetValue(tenantId, targetType, targetValue, EntityReferenceService.MatchType.CONTAINS, 5);
        }
        if (CollectionUtils.notEmpty(diffDescribeApiNames)) {
            ruleList.removeIf(x -> !diffDescribeApiNames.contains(x.getTargetValue().split("\\.")[0]));
        }
        if (CollectionUtils.empty(ruleList)) {
            return;
        }
        //分组绑定的规则
        StringBuilder ruleNameList = new StringBuilder();
        // TODO: 2024/9/20 I18n
        ruleList.forEach(x -> ruleNameList.append(x.getSourceLabel().replace("评分规则:", "")).append(",")); // ignoreI18n
        if (StringUtils.isNotBlank(ruleNameList.toString())) {
            ruleNameList.deleteCharAt(ruleNameList.lastIndexOf(","));
        }
        //规则对应的对象
        StringBuilder ruleBindObj = new StringBuilder();
        Map<String, List<String>> objRuleMap = Maps.newHashMap();
        // TODO: 2024/9/20 I18n
        ruleList.forEach(x -> objRuleMap.computeIfAbsent(x.getTargetValue().split("\\.")[0], k -> Lists.newArrayList()).add(x.getSourceLabel().replace("评分规则:", ""))); // ignoreI18n
        Map<String, String> objectDisplayMap = describeLogicService.findDisplayNameByApiNames(tenantId, objRuleMap.keySet());
        for (Map.Entry<String, List<String>> objRule : objRuleMap.entrySet()) {
            StringBuilder objBindRuleStr = new StringBuilder();
            List<String> rules = objRule.getValue();
            StringBuilder ruleListStr = new StringBuilder();
            rules.forEach(x -> ruleListStr.append(x).append("、"));
            if (StringUtils.isNotBlank(ruleListStr.toString())) {
                ruleListStr.deleteCharAt(ruleListStr.lastIndexOf("、"));
            }
            objBindRuleStr.append(I18N.text(SOURCE_RULE_BIND_OBJ, ruleListStr, objectDisplayMap.get(objRule.getKey())));
            ruleBindObj.append(objBindRuleStr);
        }
        if (CollectionUtils.empty(diffDescribeApiNames)) {
            throw new ValidateException(I18N.text(TAG_GROUP_BIND_RULE, I18N.text(typeI18n), ruleNameList) + ruleBindObj + I18N.text(DISABLE_TAG_OR_GROUP_TITLE));
        }
        throw new ValidateException(I18N.text(TAG_GROUP_BIND_RULE, I18N.text(typeI18n), ruleNameList) + ruleBindObj + I18N.text(UPDATE_TAG_GROUP_OBJECT_RANGE));

    }

    /**
     * 校验改变的标签适用对象是否绑定有规则，如果绑定了规则，则抛出异常提示
     *
     * @param newDescribeApiNames
     * @param oldDescribeApiNames
     * @param tagGroupName
     * @param context
     */
    private void validateTagRange(List<String> newDescribeApiNames, List<String> oldDescribeApiNames, String tagGroupName, ServiceContext context) {
        Set<String> diffDescribeApiNames = Sets.difference(Sets.newHashSet(oldDescribeApiNames), Sets.newHashSet(newDescribeApiNames));

        if (CollectionUtils.empty(diffDescribeApiNames)) {
            return;
        }
        String targetValue = "." + tagGroupName + ".";
        validate(targetValue, EntityReferenceService.TAG_GROUP, context.getTenantId(), TAG_GROUP, diffDescribeApiNames, Lists.newArrayList());
    }


    /**
     * 校验该标签分组是否被规则引用
     *
     * @param context
     * @param group
     */
    private void validateTagGroupQuoteRule(ServiceContext context, ITagDescribe group) {
        String targetValue = "." + group.getApiName() + ".";
        validate(targetValue, EntityReferenceService.TAG_GROUP, context.getTenantId(), TAG_GROUP, Lists.newArrayList(), Lists.newArrayList());
        List<ISubTagDescribe> subTagDescribes = tagLogicService.findTagsByTagGroupId(group.getId(), context.getTenantId());
        if (CollectionUtils.notEmpty(subTagDescribes)) {
            List<String> tagValueList = subTagDescribes.stream().map(x -> "tag." + x.getId()).collect(toList());
            validate("", EntityReferenceService.REFOBJ_TAG_GROUP_TAG, context.getTenantId(), TAG_GROUP, Lists.newArrayList(), tagValueList);
        }
    }

    /**
     * 删除标签分组
     */
    @ServiceMethod("deleteTagGroup")
    public DeleteTagGroup.Result deleteTagGroup(DeleteTagGroup.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getTagGroupId())) {
            throw new ValidateException(I18NExt.text(PARAM_ERROR));
        }
        List<ITagDescribe> tagGroupsByIds = tagLogicService.findTagGroupsByIds(context.getTenantId(), Lists.newArrayList(arg.getTagGroupId()));
        if (CollectionUtils.empty(tagGroupsByIds)) {
            throw new ValidateException(I18NExt.text(I18NKey.TAG_GROUP_DELETED));
        }
        ITagDescribe iTagDescribe = tagGroupsByIds.get(0);
        if (TAG_TYPE_SYSTEM.equals(iTagDescribe.getTagDefineType()) && iTagDescribe.getIsActive()) {
            throw new ValidateException(I18NExt.text(I18NKey.TAG_GROUP_NOT_DISABLE));
        }
        tagLogicService.deleteTagGroup(arg.getTagGroupId(), context.getTenantId());
        return DeleteTagGroup.Result.builder().build();
    }

    /**
     * 根据标签分组ID查询标签
     */
    @ServiceMethod("findTagsByGroupIdOrName")
    public FindTagsByGroupId.Result findTagsByGroupId(FindTagsByGroupId.Arg arg, ServiceContext context) {

        boolean isOnTime = false;
        if(TAG_MANAGEMENT.equals(arg.getSourceInfo())){
            isOnTime = true;
        }

        TagQueryInfo tagQueryInfo = covertQueryInfo(arg.getQuery_info(), arg.getOrder_info());
        List<ISubTagDescribe> tags = tagLogicService.findSubTagByQueryInfo(context.getTenantId(), arg.getTag_group_id(), tagQueryInfo, isOnTime);
        // 批量查询用户名称
        List<String> userIds = tags.stream()
                .map(t -> t.getCreatedBy())
                .distinct()
                .collect(toList());
        Map<String, String> idNameMap = orgService.getUserNameMapByIds(context.getTenantId(),
                context.getUser().getUserId(),
                userIds);

        // 批量查询标签分组
        List<ITagDescribe> groups = tagLogicService.findTagGroupsByIds(context.getTenantId(),
                tags.stream().map(ISubTagDescribe::getTagId).collect(toSet()), isOnTime);
        // groupId: group
        Map<String, ITagDescribe> groupMap = groups.stream().collect(Collectors.toMap(ITagDescribe::getId, g -> g));

        List<TagDocument> tagDocuments = Lists.newArrayList();
        for (ISubTagDescribe tag : tags) {
            TagDocument doc = TagDocument.of(tag)
                    .createUser(idNameMap.get(tag.getCreatedBy()))
                    .groupName(groupMap.get(tag.getTagId()).getType());
            tagDocuments.add(doc);
        }

        return FindTagsByGroupId.Result.builder().tags(tagDocuments).build();
    }

    private TagQueryInfo covertQueryInfo(TagQuery.QueryInfo queryInfo, TagQuery.OrderInfo orderInfo) {
        TagQueryInfo tagQueryInfo = new TagQueryInfo();
        if (Objects.nonNull(queryInfo)) {
            List<TagQuery.Conditions> conditions = queryInfo.getConditions();
            if (CollectionUtils.notEmpty(conditions)) {
                List<TagFilter> tagFilters = Lists.newArrayList();

                for (TagQuery.Conditions condition : conditions) {
                    TagFilter tagFilter = new TagFilter();
                    tagFilter.setSubTag("tag".equals(condition.getType()));
                    tagFilter.setFieldName(condition.getFieldName());
                    tagFilter.setFiledValue(Lists.newArrayList(condition.getFilterValue()));
                    tagFilter.setOperator(TagOperator.valueOf(condition.getOperator()));
                    tagFilters.add(tagFilter);
                }

                tagQueryInfo.setTagFilters(tagFilters);
            }
        }
        if (Objects.nonNull(orderInfo)) {
            TagOrder tagOrder = new TagOrder();
            tagOrder.setAsc("ASC".equals(orderInfo.getOrderOperator()));
            tagOrder.setOrderField(orderInfo.getOrderBy());
            tagQueryInfo.setTagOrders(Lists.newArrayList(tagOrder));
        }
        return tagQueryInfo;
    }

    /**
     * 在某标签组下新建标签
     */
    @ServiceMethod("createTag")
    public CreateOrUpdateTag.Result createTag(CreateOrUpdateTag.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getTagApiName()) || !arg.getTagApiName().matches(pattern) || arg.getTagName().length() > 10) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        ISubTagDescribe tag = new SubTagDescribe();
        tag.setGrade(1);
        tag.setTagApiName(arg.getTagApiName());
        tag.setName(arg.getTagName());
        tag.setTagId(arg.getGroupId());
        tag.setTenantId(context.getTenantId());
        tag.setDescription(arg.getDescription());
        ISubTagDescribe subTagDescribe = tagLogicService.createTag(tag, context.getUser());
        return CreateOrUpdateTag.Result.builder().subTagDescribe(TagDocument.of(subTagDescribe)).build();
    }

    /**
     * 增量更新标签
     */
    @ServiceMethod("updateTag")
    public CreateOrUpdateTag.Result updateTag(CreateOrUpdateTag.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getTagApiName()) || !arg.getTagApiName().matches(pattern) || arg.getTagName().length() > 10) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        ISubTagDescribe tag = new SubTagDescribe();
        tag.setTagApiName(arg.getTagApiName());
        tag.setId(arg.getTagId());
        tag.setTagId(arg.getGroupId());
        tag.setName(arg.getTagName());
        tag.setTenantId(context.getTenantId());
        tag.setDescription(arg.getDescription());
        List<String> fields = Lists.newArrayList(
                SubTagDescribe.NAME, SubTagDescribe.DESCRIPTION, SubTagDescribe.TAG_ID, SubTagDescribe.LAST_MODIFIED_TIME); // TAG_ID是标签组ID
        ISubTagDescribe subTagDescribe = tagLogicService.updateTag(tag, fields, context.getUser());
        return CreateOrUpdateTag.Result.builder().subTagDescribe(TagDocument.of(subTagDescribe)).build();
    }

    /**
     * 根据标签id查询标签详细信息
     */
    @ServiceMethod("findTagById")
    public FindTagById.Result findTagById(FindTagById.Arg arg, ServiceContext context) {
        boolean isOnTime = false;
        if(TAG_MANAGEMENT.equals(arg.getSourceInfo())){
            isOnTime = true;
        }

        ISubTagDescribe tag = tagLogicService.findTagById(arg.getTagId(), context.getUser(), isOnTime);
        if(Objects.isNull(tag)) {
            return FindTagById.Result.builder().build();
        }

        List<ITagDescribe> groups = tagLogicService.findTagGroupsByIds(context.getTenantId(), Lists.newArrayList(tag.getTagId()), isOnTime);
        String groupName = "";
        if (CollectionUtils.notEmpty(groups)) {
            groupName = groups.get(0).getType(); // getType()是获取标签组名称
        }
        return FindTagById.Result.builder().tag(TagDocument.of(tag).groupName(groupName)).build();
    }

    /**
     * 删除标签
     */
    @ServiceMethod("deleteTag")
    public CreateOrUpdateTag.Result deleteTag(CreateOrUpdateTag.Arg arg, ServiceContext context) {
        tagLogicService.deleteTag(arg.getGroupId(), arg.getTagId(), context.getTenantId());
        return CreateOrUpdateTag.Result.builder().build();
    }

    /**
     * 启用标签
     */
    @ServiceMethod("enableTag")
    public CreateOrUpdateTag.Result enableTag(CreateOrUpdateTag.Arg arg, ServiceContext context) {
        tagLogicService.enableTag(arg.getTagId(), context.getUser());
        return CreateOrUpdateTag.Result.builder().build();
    }

    /**
     * 禁用标签
     */
    @ServiceMethod("disableTag")
    public CreateOrUpdateTag.Result disableTag(CreateOrUpdateTag.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getTagId())) {
            throw new ValidateException(I18NExt.text(arg.getTagId()));
        }
        validateTag(arg.getGroupApiName(), arg.getTagId(), context.getTenantId());
        tagLogicService.disableTag(arg.getTagId(), context.getUser());
        return CreateOrUpdateTag.Result.builder().build();
    }

    @ServiceMethod("findTagGroupByName")
    public FindTagGroupByName.Result findTagGroupByName(FindTagGroupByName.Arg arg, ServiceContext context) {
        ITagDescribe tagGroupByApiName = tagLogicService.findTagGroupByApiName(arg.getGroupApiName(), context.getUser());
        if (Objects.isNull(tagGroupByApiName)) {
            return FindTagGroupByName.Result.builder().build();
        }
        return FindTagGroupByName.Result.builder().name(tagGroupByApiName.getType())
                .apiNameList(tagGroupByApiName.getTagRange())
                .tagGroupId(tagGroupByApiName.getId())
                .isAppliedToAll(tagGroupByApiName.getIsAll()).build();
    }

    /**
     * 根据标签名称，模糊匹配标签
     */
    @ServiceMethod("findTagsByName")
    public FindTagsByName.Result findTagsByName(FindTagsByName.Arg arg, ServiceContext context) {
        List<ISubTagDescribe> tags = tagLogicService.findTagsByKeyword(arg.getKeyword(), context.getUser());
        if (CollectionUtils.empty(tags)) {
            return FindTagsByName.Result.builder().build();
        }
        // 按标签组分组
        Map<String, List<ISubTagDescribe>> tagsByGroup = tags.stream()
                .collect(Collectors.groupingBy(t -> t.getTagId()));
        // 批量查询标签组信息
        List<ITagDescribe> tagGroups = tagLogicService.findTagGroupsByIds(context.getTenantId(), tagsByGroup.keySet());
        Map<String, ITagDescribe> tagGroupMap = tagGroups.stream()
                .collect(Collectors.toMap(t -> t.getId(), t -> t));

        // 拼装结果
        List<FindTagsByName.Group> groups = Lists.newArrayList();
        tagGroupMap.forEach((id, tagGroup) -> {
            FindTagsByName.Group group = FindTagsByName.Group.builder()
                    .tagGroupId(id)
                    .tagGroupApiName(tagGroup.getApiName())
                    .tagGroupName(tagGroup.getType())
                    .tags(TagDocument.ofList(tagsByGroup.get(id)))
                    .build();
            groups.add(group);
        });

        return FindTagsByName.Result.builder().groups(groups).build();
    }

    @ServiceMethod("findAllTagByBulkDataId")
    public FindAllTagByBulkDataId.Result findAllTagByBulkDataId(FindAllTagByBulkDataId.Arg arg, ServiceContext context) {
        List<DataAndSubTag> list = tagLogicService.findAllTagByBulkDataId(arg.getDescribeApiName(), arg.getDataIds(), context.getUser());
        List<FindAllTagByBulkDataId.DataTag> resultList = FindAllTagByBulkDataId.DataTag.fromDataAndSubTagList(list);
        return FindAllTagByBulkDataId.Result.builder().list(resultList).build();
    }

    @ServiceMethod("hasTagRuleGray")
    public Boolean hasTagRuleGray(ServiceContext context) {
        return AppFrameworkConfig.getIsSupportSystemTag(context.getTenantId());
    }

    //是否需要权限
    @ServiceMethod("findSystemTagGroupByApplyObject")
    public FindSystemTagGroupByApplyObject.Result findSystemTagGroupByApplyObject(ServiceContext context, FindSystemTagGroupByApplyObject.Arg arg) {
        //查询所有适用对象中包含该对象的规则
        List<ITagDescribe> tagGroups = tagLogicService.findAllTagGroupByObjectApiNameInObjRange(arg.getDescribeApiName(), context.getTenantId());
        List<ITagDescribe> systemTagGroupList = tagGroups.stream().filter(ITagDescribe::getIsActive).filter(x -> TAG_TYPE_SYSTEM.equals(x.getTagDefineType())).collect(toList());
        List<FindSystemTagGroupByApplyObject.ResultInfo> tagGroupInfoList = Lists.newArrayList();
        Map<String, List<ISubTagDescribe>> groupIdSubTagMap = tagLogicService.bulkFindSubTagByTagIds(context, systemTagGroupList.stream().map(ITagDescribe::getId).collect(toList()));
        for (ITagDescribe tagGroup : systemTagGroupList) {
            List<ISubTagDescribe> subTags = CollectionUtils.nullToEmpty(groupIdSubTagMap.get(tagGroup.getId()));
            subTags.removeIf(x -> !x.getIsActive());
            if (CollectionUtils.empty(subTags)) {
                continue;
            }
            List<FindSystemTagGroupByApplyObject.TagInfo> tagInfos = Lists.newArrayList();
            subTags.forEach(x ->
                    tagInfos.add(FindSystemTagGroupByApplyObject.TagInfo.builder()
                            .tagId(x.getId())
                            .tagName(x.getName())
                            .tagApiName(x.getTagApiName())
                            .description(x.getDescription())
                            .enable(x.getIsActive())
                            .build()));
            FindSystemTagGroupByApplyObject.ResultInfo resultInfo = FindSystemTagGroupByApplyObject.ResultInfo.builder()
                    .enable(tagGroup.getIsActive())
                    .tagGroupApiName(tagGroup.getApiName())
                    .tagGroupId(tagGroup.getId())
                    .tagGroupName(tagGroup.getType())
                    .tagList(tagInfos)
                    .build();
            tagGroupInfoList.add(resultInfo);
        }
        return FindSystemTagGroupByApplyObject.Result.builder().result(tagGroupInfoList).build();
    }

    @ServiceMethod("bulkDataTag")
    public Map<String, List<String>> bulkDataTag(ServiceContext context, BulkDataTag.Arg arg) {
        if (CollectionUtils.empty(arg.getDataIdToTagId()) || StringUtils.isEmpty(arg.getDescribeApiName())) {
            return Maps.newHashMap();
        }
        return tagLogicService.multiDataBatchBoundTags(arg.getDataIdToTagId(), arg.getDescribeApiName(), context.getUser());
    }

    @ServiceMethod("findTagByTagIds")
    public FindTagByTagIds.Result findTagByTagIds(FindTagByTagIds.Arg arg, ServiceContext context) {
        if (Objects.isNull(arg) || CollectionUtils.empty(arg.getTagIds())) {
            return FindTagByTagIds.Result.builder().tagList(Lists.newArrayList()).build();
        }
        List<ISubTagDescribe> tagList = tagLogicService.findSubTagBySubTagIds(context.getUser(), arg.getTagIds());
        List<TagDocument> tagDocumentList = CollectionUtils.nullToEmpty(tagList).stream().map(TagDocument::of).collect(toList());
        return FindTagByTagIds.Result.builder().tagList(tagDocumentList).build();
    }

    @ServiceMethod("findTagInfoByQuery")
    public FindTagInfoByNameOrApiName.Result findTagInfoByNameOrApiName(FindTagInfoByNameOrApiName.Arg arg, ServiceContext context) {
        if (StringUtils.isEmpty(arg.getType())
                || Objects.isNull(arg.getQueryInfo())
                || (StringUtils.isEmpty(arg.getTagGroupId()) && StringUtils.equals("tag", arg.getType()))) {
            throw new ValidateException(I18N.text(PARAM_EMPTY));
        }
        TagQueryInfo tagQueryInfo = covertQueryInfo(arg.getQueryInfo(), null);
        List<ISubTagDescribe> subTagDescribeList = Lists.newArrayList();
        List<TagGroupDocument> tagDescribeList = Lists.newArrayList();
        if ("tag".equals(arg.getType())) {
            subTagDescribeList = tagLogicService.findSubTagByQueryInfo(context.getTenantId(), arg.getTagGroupId(), tagQueryInfo);
        } else {
            tagDescribeList = TagGroupDocument.ofList(tagLogicService.findTagGroupByQueryInfo(context.getTenantId(), tagQueryInfo));
        }
        return FindTagInfoByNameOrApiName.Result.builder()
                .tagDescribeList(tagDescribeList)
                .subTagDescribeList(subTagDescribeList.stream().map(TagDocument::of).collect(toList()))
                .build();
    }

}

