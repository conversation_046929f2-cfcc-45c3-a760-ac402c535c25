package com.facishare.paas.appframework.common.util;

import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfigFactory;
import org.jsoup.nodes.Element;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class HtmlExtTest {

    /**
     * GenerateByAI
     * 测试内容描述：测试格式化图片路径功能
     */
    @ParameterizedTest
    @MethodSource("provideImagePathTestData")
    @DisplayName("测试formatImagePath - 图片路径格式化")
    void testFormatImagePath(String originalUrl, String expectedFormattedUrl) {
        // 使用MockedStatic模拟静态方法
        try (MockedStatic<StoneCGIConfig> stoneCGIConfigMock = mockStatic(StoneCGIConfig.class);
             MockedStatic<ConfigFactory> configFactoryMock = mockStatic(ConfigFactory.class)) {
            
            // 配置静态方法Mock
            stoneCGIConfigMock.when(StoneCGIConfig::getFileShareSkey).thenReturn("1111111111111111");
            
            IConfigFactory configFactory = mock(IConfigFactory.class);
            configFactoryMock.when(ConfigFactory::getInstance).thenReturn(configFactory);
            
            // 创建测试元素
            Element element = new Element("img");
            element.attr("src", originalUrl);
            HtmlExt.HtmlElementExt htmlElement = HtmlExt.HtmlElementExt.of(element);
            
            // 执行被测试方法
            htmlElement.formatImagePath();
            
            // 验证结果
            assertEquals(expectedFormattedUrl, element.attr("src"));
        }
    }

    /**
     * 提供图片路径测试数据
     */
    private static Stream<Arguments> provideImagePathTestData() {
        return Stream.of(
            Arguments.of("/o/123/jpg", "/i/123/jpg"),
            Arguments.of("/image/s/5356E9242EFF24C4E77ABB7451C23BFC1D2E7355D4787F61310E99856DF7A98346E19795573D647CC6EE268FB90A0D7D800E3CB29AD5E01A9156FC7452B4F58067C3EC7C16836787F9C1E70646F751942B5A058A65C26A92/0*300/jpg", 
                      "/image/i/N_202111_20_5e8c3e066b20463082fbddaa06c25800/0*300/jpg"),
            Arguments.of("https://url.com/some/path.jpg", "https://url.com/some/path.jpg"),
            Arguments.of("https://attachment.outlook.live.net/owa/MSA%3A471102375%40qq.com/service.svc/s/GetAttachmentThumbnail?id=AQMkADAwATM0MDAAMS0wNzI1LWI4Y2YtMDACLTAwCgBGAAADlmJondGRF0WFtkLqBOxGnAcAZ4TBcEsDOU2B6GlQQckNUwAAAgEJAAAAZ4TBcEsDOU2B6GlQQckNUwAFq12WaQAAAAESABAAcjErhuzLD0%2BQjvhsHTPn4g%3D%3D&thumbnailType=2&isc=1&token=*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************&X-OWA-CANARY=DIv4JGv6FUCiwU_JgQXrsEBW5ag_FtwYM_CIqtHUlvIpnbFWohtiVfaozyj6qJmGXCunsh03qe8.&owa=outlook.live.com&scriptVer=20240105004.04&clientId=F746A0AA28F74B80AC1597FF5EFCDEAF&animation=true",
                      "https://attachment.outlook.live.net/owa/MSA%3A471102375%40qq.com/service.svc/s/GetAttachmentThumbnail?id=AQMkADAwATM0MDAAMS0wNzI1LWI4Y2YtMDACLTAwCgBGAAADlmJondGRF0WFtkLqBOxGnAcAZ4TBcEsDOU2B6GlQQckNUwAAAgEJAAAAZ4TBcEsDOU2B6GlQQckNUwAFq12WaQAAAAESABAAcjErhuzLD0%2BQjvhsHTPn4g%3D%3D&thumbnailType=2&isc=1&token=*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************&X-OWA-CANARY=DIv4JGv6FUCiwU_JgQXrsEBW5ag_FtwYM_CIqtHUlvIpnbFWohtiVfaozyj6qJmGXCunsh03qe8.&owa=outlook.live.com&scriptVer=20240105004.04&clientId=F746A0AA28F74B80AC1597FF5EFCDEAF&animation=true"),
            Arguments.of("https://yxy.zsjr002511.com/FSC/EM/File/ViewTempImg?TempFileName=TN_6bddc8ea554448c093294813e07df1cc&appId=FSAID_11490c84",
                      "https://yxy.zsjr002511.com/FSC/EM/File/ViewTempImg?TempFileName=TN_6bddc8ea554448c093294813e07df1cc&appId=FSAID_11490c84")
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取所有临时图片路径功能
     */
    @ParameterizedTest
    @MethodSource("provideGetAllImagePathTestData")
    @DisplayName("测试getAllTempImageSrc - 获取所有临时图片路径")
    void testGetAllTempImageSrc(String htmlContent, String expectedTnPath, int expectedSize, boolean shouldContain) {
        // 创建HtmlExt对象
        HtmlExt htmlExt = HtmlExt.of(htmlContent);
        
        // 执行被测试方法
        Map<String, List<HtmlExt.HtmlElementExt>> imageSrc = htmlExt.getAllTempImageSrc();
        
        // 验证结果
        assertEquals(expectedSize, imageSrc.size());
        if (shouldContain && expectedTnPath != null) {
            assertTrue(imageSrc.containsKey(expectedTnPath));
        }
    }

    /**
     * 提供获取所有图片路径测试数据
     */
    private static Stream<Arguments> provideGetAllImagePathTestData() {
        return Stream.of(
            Arguments.of(
                "<p><img src=\"https://yxy.zsjr002511.com/FSC/EM/File/ViewTempImg?TempFileName=TN_6bddc8ea554448c093294813e07df1cc\" style=\"max-width: 100%;\"></p>",
                "TN_6bddc8ea554448c093294813e07df1cc",
                1,
                true
            ),
            Arguments.of(
                "<p><img src=\"https://img.fxiaoke.com/image/s/85D7CEEA2656ACA35F6EA30D926F1616F533AA89829876D352B0C5239A682CEAE4D20788590476B9F40FF2CA0DDB20115FCFE960A2F40DE8E8B34315A8E6FC24667EE3ECE6C28F3D00443EEC9D8689F61BFD3936F5E4F6F4/0*0/jpg\" style=\"max-width: 100%;\"></p>",
                null,
                0,
                false
            ),
            Arguments.of(
                "<p><img src=\"https://img.fxiaoke.com/image/i/N_202411_28_b6a09016bda4413691d73ee8b72fc297/0*0/jpg\" style=\"max-width: 100%;\"></p >",
                null,
                0,
                false
            ),
            Arguments.of(
                "<p><img src=\"https://img.fxiaoke.com/image/i/TN_202411_28_b6a09016bda4413691d73ee8b72fc297/0*0/jpg\" style=\"max-width: 100%;\"></p >",
                "TN_202411_28_b6a09016bda4413691d73ee8b72fc297",
                1,
                true
            )
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空HTML内容的处理
     */
    @Test
    @DisplayName("测试getAllTempImageSrc - 空HTML内容")
    void testGetAllTempImageSrc_EmptyHtml() {
        // 测试空字符串
        HtmlExt htmlExt1 = HtmlExt.of("");
        Map<String, List<HtmlExt.HtmlElementExt>> result1 = htmlExt1.getAllTempImageSrc();
        assertEquals(0, result1.size());
        
        // 测试null
        HtmlExt htmlExt2 = HtmlExt.of(null);
        Map<String, List<HtmlExt.HtmlElementExt>> result2 = htmlExt2.getAllTempImageSrc();
        assertEquals(0, result2.size());
        
        // 测试没有图片的HTML
        HtmlExt htmlExt3 = HtmlExt.of("<p>这是一段没有图片的文本</p>");
        Map<String, List<HtmlExt.HtmlElementExt>> result3 = htmlExt3.getAllTempImageSrc();
        assertEquals(0, result3.size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多个临时图片的HTML内容
     */
    @Test
    @DisplayName("测试getAllTempImageSrc - 多个临时图片")
    void testGetAllTempImageSrc_MultipleImages() {
        String htmlContent = "<p>" +
                "<img src=\"https://yxy.zsjr002511.com/FSC/EM/File/ViewTempImg?TempFileName=TN_first_image\" style=\"max-width: 100%;\">" +
                "<img src=\"https://img.fxiaoke.com/image/s/normal_image/0*0/jpg\" style=\"max-width: 100%;\">" +
                "<img src=\"https://img.fxiaoke.com/image/i/TN_second_image/0*0/jpg\" style=\"max-width: 100%;\">" +
                "</p>";
        
        HtmlExt htmlExt = HtmlExt.of(htmlContent);
        Map<String, List<HtmlExt.HtmlElementExt>> imageSrc = htmlExt.getAllTempImageSrc();
        
        // 应该包含2个临时图片
        assertEquals(2, imageSrc.size());
        assertTrue(imageSrc.containsKey("TN_first_image"));
        assertTrue(imageSrc.containsKey("TN_second_image"));
    }
} 