package com.facishare.paas.appframework.core.predef.controller

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.metadata.ObjectDescribeExt
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe
import com.facishare.paas.appframework.metadata.repository.model.GdprCompliance
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.search.SearchTemplate
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.facishare.paas.metadata.impl.ui.layout.Layout
import org.powermock.reflect.Whitebox
import spock.lang.Shared
import spock.lang.Specification
import java.lang.reflect.Field

class BaseListControllerTest extends Specification {
    StandardListController listController
    ServiceFacade serviceFacade = Mock(ServiceFacade)
    InfraServiceFacade infraServiceFacade = Mock(InfraServiceFacade)
    ControllerContext controllerContext = Mock(ControllerContext)

    @Shared
    String describeJson = '''{"fields":{"returned_goods_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"退货单金额(元)","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"ReturnedGoodsInvoiceObj","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["normal"]}]},{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["in_change"]}]}],"define_type":"package","is_single":false,"index_name":"d_11","field_api_name":"order_id","max_length":14,"is_index":true,"is_active":true,"create_time":1562060912729,"count_type":"sum","count_field_api_name":"returned_goods_inv_amount","label":"退货单金额(元)","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"returned_goods_amount","count_field_type":"currency","_id":"5d3abb7e319d19982fcc968b","is_index_field":false,"config":{"edit":1,"enable":0,"attrs":{"wheres":0,"is_required":1,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"},"refund_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"已退款金额（元）","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"RefundObj","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["normal"]}]},{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["in_change"]}]}],"define_type":"package","is_single":false,"index_name":"d_12","field_api_name":"order_id","max_length":14,"is_index":true,"is_active":true,"create_time":1562060912729,"count_type":"sum","count_field_api_name":"refunded_amount","label":"已退款金额(元)","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"refund_amount","count_field_type":"currency","_id":"5d3abb7e319d19982fcc968c","is_index_field":false,"config":{"edit":1,"enable":0,"attrs":{"wheres":0,"is_required":1,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"},"opportunity_id":{"describe_api_name":"SalesOrderObj","description":"商机名称","is_unique":false,"type":"object_reference","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":2,"operator":"EQ","field_name":"account_id","field_values":["$account_id$"]}]}],"define_type":"package","cascade_parent_api_name":"account_id","is_single":false,"index_name":"s_11","is_index":true,"is_active":false,"create_time":*************,"label":"商机名称","target_api_name":"OpportunityObj","target_related_list_name":"opportunity_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"api_name":"opportunity_id","_id":"5da70441319d19799edf068c","is_index_field":false,"config":{"edit":1,"enable":1,"display":0,"attrs":{"target_related_list_label":1,"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"status":"new"},"new_opportunity_id":{"describe_api_name":"SalesOrderObj","description":"商机2.0","is_unique":false,"type":"object_reference","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":2,"operator":"EQ","field_name":"account_id","field_values":["$account_id$"]}]}],"define_type":"package","cascade_parent_api_name":"account_id","is_single":false,"index_name":"s_8","is_index":true,"is_active":true,"create_time":*************,"label":"商机2.0","target_api_name":"NewOpportunityObj","target_related_list_name":"new_opportunity_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"api_name":"new_opportunity_id","_id":"5da70519319d19799edf068d","is_index_field":false,"config":{"edit":1,"enable":1,"attrs":{"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"status":"new"},"lock_rule":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"锁定规则","is_unique":false,"default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"5d1b2870319d19c15d3f9c6d","is_extend":false,"is_index_field":false,"is_single":false,"index_name":"s_1","help_text":"锁定规则","status":"new"},"settle_type":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":false,"create_time":1562060912676,"description":"结算方式","is_unique":false,"label":"结算方式","type":"select_one","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"settle_type","options":[{"resource_bundle_key":"","label":"预付","value":"1","config":{"edit":0,"enable":0,"remove":0}},{"resource_bundle_key":"","label":"现付","value":"2","config":{"edit":0,"enable":0,"remove":0}},{"resource_bundle_key":"","label":"赊销","value":"3","config":{"edit":0,"enable":0,"remove":0}}],"define_type":"package","option_id":"ce581138cb14e66193091162d8e2ecc8","_id":"5d1b2870319d19c15d3f9c6e","is_index_field":false,"is_single":false,"config":{"add":0,"edit":1,"enable":0,"display":0,"attrs":{"is_required":0,"options":0,"label":1,"help_text":1}},"index_name":"s_2","status":"released"},"current_level":{"describe_api_name":"SalesOrderObj","default_is_expression":false,"description":"已审批节点数量","is_unique":false,"type":"number","decimal_places":0,"default_to_zero":true,"is_required":false,"define_type":"system","is_single":false,"index_name":"d_1","max_length":14,"is_index":false,"is_active":false,"create_time":1562060912676,"length":14,"default_value":"0","label":"已审批节点数量","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"current_level","_id":"5d1b2870319d19c15d3f9c6f","is_index_field":false,"config":{"edit":0,"enable":0,"display":0,"attrs":{"is_required":0,"help_text":1}},"round_mode":4,"status":"released"},"discount":{"describe_api_name":"SalesOrderObj","default_is_expression":false,"is_index":false,"is_active":true,"create_time":1562060912676,"pattern":"","description":"整单折扣","is_unique":false,"default_value":"100","label":"整单折扣","type":"percentile","is_abstract":null,"field_num":null,"default_to_zero":true,"is_need_convert":false,"is_required":false,"api_name":"discount","define_type":"package","_id":"5d1b2870319d19c15d3f9c70","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1}},"index_name":"d_2","status":"released"},"order_time":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912676,"description":"下单日期","is_unique":false,"label":"下单日期","time_zone":"GMT+8","type":"date","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":true,"api_name":"order_time","define_type":"package","date_format":"yyyy-MM-dd","_id":"5d1b2870319d19c15d3f9c71","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":0,"attrs":{"is_required":0,"default_value":1,"label":1,"help_text":1}},"index_name":"l_1","status":"released"},"receivable_amount":{"describe_api_name":"SalesOrderObj","expression_type":"js","return_type":"currency","description":"待回款金额（元）","is_unique":false,"type":"formula","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"package","is_single":false,"index_name":"d_3","max_length":14,"is_index":true,"expression":"$order_amount$+$refund_amount$-$payment_amount$-$returned_goods_amount$","is_active":true,"create_time":1562060912676,"label":"待回款金额（元）","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"receivable_amount","_id":"5d1b2870319d19c15d3f9c72","is_index_field":false,"config":{"edit":1,"enable":0,"attrs":{"is_required":1,"formula":1,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"},"logistics_status":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"发货状态","is_unique":false,"label":"发货状态","type":"select_one","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"logistics_status","options":[{"resource_bundle_key":"","label":"待发货","value":"1","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"部分发货","value":"2","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已发货","value":"3","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"部分收货","value":"4","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已收货","value":"5","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"package","option_id":"a7ad9d6143f2ea3a984dc74c3dade3fc","_id":"5d1b2870319d19c15d3f9c73","is_index_field":false,"is_single":false,"config":{"edit":0},"index_name":"s_3","status":"released"},"ship_to_id":{"describe_api_name":"SalesOrderObj","description":"收货人","is_unique":false,"type":"object_reference","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":2,"operator":"EQ","field_name":"account_id","field_values":["$account_id$"]}]}],"define_type":"package","cascade_parent_api_name":"account_id","is_single":false,"index_name":"s_4","is_index":true,"is_active":true,"create_time":*************,"label":"收货人","target_api_name":"ContactObj","target_related_list_name":"ship_to_salesorder_list","is_abstract":null,"field_num":null,"target_related_list_label":"收货人订单列表","action_on_target_delete":"cascade_delete","is_need_convert":false,"api_name":"ship_to_id","_id":"5d1b2870319d19c15d3f9c74","is_index_field":false,"config":{"edit":1,"enable":1,"attrs":{"target_related_list_label":1,"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"status":"released"},"order_status":{"describe_api_name":"SalesOrderObj","is_use_value":true,"description":"状态（原）","is_unique":false,"type":"select_one","is_required":false,"options":[{"resource_bundle_key":"","label":"确认中","value":"6","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已确认","value":"7","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已驳回","value":"8","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已撤回","value":"9","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已发货","value":"10","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已收货","value":"11","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"作废","value":"99","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"system","option_id":"c5eec7d2d4e9a4a458a488e904ea0d84","is_single":false,"index_name":"s_5","is_index":true,"expression":"CASE($life_status$,'invalid','99','ineffective','8','under_review','6',CASE($logistics_status$,'3','10','5','11','7'))","is_active":true,"create_time":1562060912697,"label":"状态（原）","is_abstract":null,"field_num":null,"is_need_convert":false,"is_need_calculate":true,"api_name":"order_status","_id":"5d1b2870319d19c15d3f9c75","is_index_field":false,"config":{"edit":0,"enable":0,"display":1,"attrs":{"is_required":0,"help_text":1}},"status":"released"},"ship_to_add":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912698,"pattern":"","description":"收货地址","is_unique":false,"label":"收货地址","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"ship_to_add","define_type":"package","_id":"5d1b2870319d19c15d3f9c76","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1}},"index_name":"t_1","max_length":256,"status":"released"},"extend_obj_data_id":{"describe_api_name":"SalesOrderObj","default_is_expression":false,"pattern":"","description":"extend_obj_data_id","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"system","is_extend":false,"is_single":false,"index_name":"t_2","max_length":256,"is_index":false,"is_active":true,"create_time":1562060912698,"default_value":"","label":"extend_obj_data_id","is_abstract":null,"field_num":null,"api_name":"extend_obj_data_id","_id":"5d1b2870319d19c15d3f9c77","is_index_field":false,"help_text":"","status":"released"},"life_status_before_invalid":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912698,"pattern":"","description":"作废前生命状态","is_unique":false,"label":"作废前生命状态","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"life_status_before_invalid","define_type":"package","_id":"5d1b2870319d19c15d3f9c78","is_extend":false,"is_index_field":false,"is_single":false,"index_name":"t_3","help_text":"作废前生命状态","max_length":256,"status":"new"},"order_amount":{"describe_api_name":"SalesOrderObj","default_is_expression":true,"description":"销售订单金额(元)","is_unique":false,"type":"currency","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"package","is_single":false,"index_name":"d_4","max_length":14,"is_index":true,"is_active":true,"create_time":1562060912698,"length":18,"default_value":"$product_amount$*$discount$","label":"销售订单金额(元)","currency_unit":"￥","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"order_amount","_id":"5d1b2870319d19c15d3f9c79","is_index_field":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1,"max_length":1,"decimal_places":1}},"round_mode":4,"status":"released"},"owner_department":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912698,"pattern":"","description":"负责人主属部门","is_unique":false,"label":"负责人主属部门","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"owner_department","define_type":"package","_id":"5d1b2870319d19c15d3f9c7a","is_index_field":false,"is_single":false,"index_name":"owner_dept","max_length":100,"status":"released"},"signature_attachment":{"describe_api_name":"SalesOrderObj","file_amount_limit":10,"is_index":true,"is_active":true,"create_time":1562060912698,"description":"电子签章附件","is_unique":false,"label":"电子签章附件","type":"file_attachment","is_abstract":null,"field_num":null,"file_size_limit":10485760,"is_required":false,"api_name":"signature_attachment","define_type":"package","_id":"5d1b2870319d19c15d3f9c7b","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":1,"attrs":{"is_required":0,"help_text":1}},"index_name":"a_1","support_file_types":[],"status":"released"},"plan_payment_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"已计划回款金额","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"PaymentPlanObj","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["normal"]}]}],"define_type":"package","is_single":false,"index_name":"d_5","field_api_name":"order_id","max_length":14,"is_index":true,"is_active":true,"create_time":1562060912698,"count_type":"sum","count_field_api_name":"plan_payment_amount","label":"已计划回款金额","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"plan_payment_amount","count_field_type":"currency","_id":"5d1b2870319d19c15d3f9c7c","is_index_field":false,"config":{"edit":1,"enable":1,"attrs":{"wheres":1,"is_required":0,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"},"lock_status":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912706,"description":"锁定状态","is_unique":false,"default_value":"0","label":"锁定状态","type":"select_one","is_abstract":null,"field_num":null,"is_required":false,"api_name":"lock_status","options":[{"label":"未锁定","value":"0","config":{"edit":1,"enable":1,"remove":1}},{"label":"锁定","value":"1","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"package","option_id":"f8f6e3b07ca18b590c9a3cf86e014576","_id":"5d1b2870319d19c15d3f9c7d","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"s_6","status":"new"},"resource":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"来源","is_unique":false,"label":"来源","type":"select_one","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"resource","options":[{"resource_bundle_key":"","label":"CRM","value":"0","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"订货通","value":"1","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"package","option_id":"38d27afa1afa05bfa3f1fbf6b1c74b60","_id":"5d1b2870319d19c15d3f9c7e","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"display":1,"attrs":{"is_required":0,"help_text":1}},"index_name":"s_7","status":"released"},"submit_time":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"提交时间","is_unique":false,"label":"提交时间","time_zone":"GMT+8","type":"date_time","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"submit_time","define_type":"package","date_format":"yyyy-MM-dd HH:mm","_id":"5d1b2870319d19c15d3f9c7f","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"attrs":{"is_required":1,"label":1,"help_text":1}},"index_name":"l_2","status":"released"},"quote_id":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"报价单","is_unique":false,"label":"报价单","target_api_name":"QuoteObj","type":"object_reference","target_related_list_name":"quote_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"is_required":false,"api_name":"quote_id","define_type":"package","_id":"5d1b2870319d19c15d3f9c81","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"index_name":"s_9","status":"new"},"payment_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"已回款金额(元)","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"OrderPaymentObj","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["normal"]}]}],"define_type":"package","is_single":false,"index_name":"d_6","field_api_name":"order_id","is_index":true,"is_active":true,"create_time":*************,"count_type":"sum","length":14,"count_field_api_name":"payment_amount","label":"已回款金额(元)","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"payment_amount","count_field_type":"currency","_id":"5d1b2870319d19c15d3f9c82","is_index_field":false,"config":{"edit":1,"enable":0,"attrs":{"wheres":0,"is_required":1,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"},"delivery_comment":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"","description":"发货备注","is_unique":false,"label":"发货备注","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"delivery_comment","define_type":"package","_id":"5d1b2870319d19c15d3f9c83","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"t_4","max_length":1000,"status":"released"},"relevant_team":{"describe_api_name":"SalesOrderObj","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","is_unique":false,"define_type":"package","description":"成员员工","label":"成员员工","type":"employee","is_single":false,"index_name":"stringList_2","help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"is_unique":false,"define_type":"package","description":"成员角色","label":"成员角色","type":"select_one","index_name":"string_4","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"is_unique":false,"define_type":"package","description":"成员权限类型","label":"成员权限类型","type":"select_one","index_name":"string_5","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":*************,"is_unique":false,"description":"","label":"相关团队","type":"embedded_object_list","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"5d1b2870319d19c15d3f9c84","is_index_field":false,"is_single":false,"index_name":"s_10","help_text":"相关团队","status":"released"},"confirmed_receive_date":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"收货时间","is_unique":false,"label":"收货时间","time_zone":"GMT+8","type":"date_time","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"confirmed_receive_date","define_type":"package","date_format":"yyyy-MM-dd HH:mm","_id":"5d1b2870319d19c15d3f9c85","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"l_3","status":"released"},"delivery_date":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"交货日期","is_unique":false,"label":"交货日期","time_zone":"GMT+8","type":"date","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"delivery_date","define_type":"package","date_format":"yyyy-MM-dd","_id":"5d1b2870319d19c15d3f9c86","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1}},"index_name":"l_4","status":"released"},"price_book_id":{"describe_api_name":"SalesOrderObj","description":"价目表","is_unique":false,"type":"object_reference","is_required":false,"define_type":"package","cascade_parent_api_name":"account_id","is_single":false,"index_name":"s_12","is_index":true,"is_active":false,"create_time":*************,"label":"价目表","target_api_name":"PriceBookObj","target_related_list_name":"price_book_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"api_name":"price_book_id","_id":"5d1b2870319d19c15d3f9c88","is_index_field":false,"config":{"edit":1,"enable":0,"display":0,"attrs":{"target_related_list_label":1,"wheres":0,"is_required":1,"default_value":0,"label":1,"help_text":1}},"status":"new"},"name":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"prefix":"{yyyy}{mm}{dd}-","description":"销售订单编号","is_unique":true,"serial_number":6,"start_number":1,"label":"销售订单编号","type":"auto_number","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":true,"api_name":"name","define_type":"package","_id":"5b066c5f9e787b82577aebe4","postfix":"","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":0,"attrs":{"is_required":0,"label":1,"help_text":1}},"index_name":"name","status":"released"},"bill_money_to_confirm":{"describe_api_name":"SalesOrderObj","description":"待确认的开票金额","is_unique":false,"type":"currency","decimal_places":2,"is_required":false,"define_type":"system","is_single":false,"index_name":"d_7","max_length":14,"is_index":false,"is_active":true,"create_time":*************,"length":18,"label":"待确认的开票金额","currency_unit":"￥","is_abstract":true,"field_num":null,"is_need_convert":false,"api_name":"bill_money_to_confirm","_id":"5d1b2870319d19c15d3f9c89","is_index_field":false,"config":{"edit":0,"enable":0,"display":0,"attrs":{"is_required":0,"help_text":1}},"round_mode":4,"status":"released"},"delivered_amount_sum":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":false,"create_time":*************,"length":14,"description":"已发货金额","is_unique":false,"label":"已发货金额","type":"number","is_abstract":null,"decimal_places":2,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"delivered_amount_sum","define_type":"package","_id":"5d1b2870319d19c15d3f9c8a","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":0,"display":0,"attrs":{"is_required":0,"default_value":1,"label":1,"help_text":1}},"index_name":"d_8","round_mode":4,"status":"released"},"payment_money_to_confirm":{"describe_api_name":"SalesOrderObj","description":"待确认的回款金额","is_unique":false,"type":"currency","decimal_places":2,"is_required":false,"define_type":"system","is_single":false,"index_name":"d_9","max_length":14,"is_index":false,"is_active":true,"create_time":*************,"length":18,"label":"待确认的回款金额","currency_unit":"￥","is_abstract":true,"field_num":null,"is_need_convert":false,"api_name":"payment_money_to_confirm","_id":"5d1b2870319d19c15d3f9c8b","is_index_field":false,"config":{"edit":0,"enable":0,"display":0,"attrs":{"is_required":0,"help_text":1}},"round_mode":4,"status":"released"},"remark":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"","description":"备注","is_unique":false,"label":"备注","type":"long_text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"remark","define_type":"package","_id":"5d1b2870319d19c15d3f9c8c","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1}},"index_name":"t_5","max_length":2000,"status":"released"},"promotion_id":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":false,"create_time":*************,"description":"促销活动","is_unique":false,"label":"促销活动","target_api_name":"PromotionObj","type":"object_reference","target_related_list_name":"promotion_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"is_required":false,"api_name":"promotion_id","define_type":"package","_id":"5d1b2870319d19c15d3f9c8d","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":0,"display":0,"attrs":{"target_related_list_label":1,"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"index_name":"s_13","status":"new"},"lock_user":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912717,"description":"加锁人","is_unique":false,"label":"加锁人","type":"employee","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"5d1b2870319d19c15d3f9c8e","is_extend":false,"is_index_field":false,"is_single":true,"index_name":"a_2","help_text":"加锁人","status":"new"},"invoice_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"已开票金额(元)","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"InvoiceApplicationObj","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["normal"]}]},{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["in_change"]}]}],"define_type":"package","is_single":false,"index_name":"d_10","field_api_name":"order_id","is_index":true,"is_active":true,"create_time":1562060912717,"count_type":"sum","length":0,"count_field_api_name":"invoice_applied_amount","label":"已开票金额(元)","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"invoice_amount","count_field_type":"currency","_id":"5d1b2870319d19c15d3f9c8f","is_index_field":false,"config":{"edit":1,"enable":0,"attrs":{"wheres":0,"is_required":1,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"},"shipping_warehouse_id":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":false,"create_time":1562060912717,"description":"订货仓库","is_unique":false,"label":"订货仓库","target_api_name":"WarehouseObj","type":"object_reference","target_related_list_name":"shipping_warehouse_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"is_required":false,"api_name":"shipping_warehouse_id","define_type":"package","_id":"5d1b2870319d19c15d3f9c90","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":0,"display":0,"attrs":{"target_related_list_label":1,"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"index_name":"s_14","status":"new"},"partner_id":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":false,"create_time":1562060912717,"description":"合作伙伴","is_unique":false,"label":"合作伙伴","target_api_name":"PartnerObj","type":"object_reference","target_related_list_name":"partner_salesorder_list","is_abstract":null,"field_num":null,"target_related_list_label":"订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"is_required":false,"api_name":"partner_id","define_type":"package","_id":"5d1b2870319d19c15d3f9c91","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"display":0,"attrs":{"target_related_list_label":1,"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"index_name":"s_15","status":"new"},"receipt_type":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912729,"pattern":"","description":"收款类型","is_unique":false,"label":"收款类型","type":"select_one","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"receipt_type","options":[{"resource_bundle_key":"","label":"保证金","value":"1","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"服务费","value":"2","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"贷款","value":"3","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"package","option_id":"cec07bca47160cb2dac1d64e3fb04751","_id":"5d1b2870319d19c15d3f9c92","is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"attrs":{"is_required":1,"options":1,"label":1,"help_text":1}},"index_name":"s_16","status":"released"},"out_resources":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":false,"create_time":1562060912737,"description":"外部来源","is_unique":false,"label":"外部来源","type":"select_one","is_abstract":null,"field_num":null,"is_required":false,"api_name":"out_resources","options":[{"not_usable":false,"label":"PRM","value":"partner","config":{}}],"define_type":"package","option_id":"37e59fdcc9cdb736d4c89da45675ec08","_id":"5d1b2870319d19c15d3f9c95","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"display":0,"attrs":{"options":0,"help_text":1}},"index_name":"s_17","status":"new"},"product_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"产品合计","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"SalesOrderProductObj","is_required":false,"define_type":"package","is_single":false,"index_name":"d_13","field_api_name":"order_id","max_length":14,"is_index":true,"is_active":true,"create_time":1562060912737,"count_type":"sum","count_field_api_name":"subtotal","label":"产品合计","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"product_amount","count_field_type":"currency","_id":"5d1b2870319d19c15d3f9c96","is_index_field":false,"config":{"edit":1,"enable":1,"attrs":{"wheres":1,"is_required":0,"label":1,"help_text":1,"max_length":1,"decimal_places":1}},"round_mode":4,"status":"released"},"owner":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912737,"length":8,"description":"负责人","is_unique":false,"label":"负责人","type":"employee","is_abstract":null,"decimal_places":0,"field_num":null,"is_need_convert":true,"is_required":true,"api_name":"owner","define_type":"package","_id":"5d1b2870319d19c15d3f9c97","is_index_field":false,"is_single":true,"config":{"edit":0,"enable":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"owner","round_mode":4,"status":"released"},"life_status":{"describe_api_name":"SalesOrderObj","description":"生命状态","is_unique":false,"type":"select_one","is_required":false,"options":[{"label":"未生效","value":"ineffective","config":{"edit":1,"enable":1,"remove":1}},{"label":"审核中","value":"under_review","config":{"edit":1,"enable":1,"remove":1}},{"label":"正常","value":"normal","config":{"edit":1,"enable":1,"remove":1}},{"label":"变更中","value":"in_change","config":{"edit":1,"enable":1,"remove":1}},{"label":"作废","value":"invalid","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"package","option_id":"18de2c4eae8cf7a180ffb0f175010e6f","is_extend":false,"is_single":false,"index_name":"s_18","is_index":true,"is_active":true,"create_time":1562060912747,"default_value":"normal","label":"生命状态","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"life_status","_id":"5d1b2870319d19c15d3f9c98","is_index_field":false,"config":{},"help_text":"生命状态","status":"released"},"is_user_define_work_flow":{"describe_api_name":"SalesOrderObj","is_active":true,"create_time":1562060912748,"description":"是否是自定义的工作流","is_unique":false,"default_value":false,"label":"是否是自定义的工作流","type":"true_or_false","is_abstract":true,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"is_user_define_work_flow","define_type":"system","_id":"5d1b2870319d19c15d3f9c99","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"display":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"b_1","status":"released"},"ship_to_tel":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912748,"pattern":"^[0-9+\\\\-;,]{0,100}$","description":"收货人电话","is_unique":false,"label":"收货人电话","type":"phone_number","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"ship_to_tel","define_type":"package","_id":"5d1b2870319d19c15d3f9c9a","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1}},"index_name":"p_1","max_length":100,"status":"released"},"work_flow_id":{"describe_api_name":"SalesOrderObj","is_index":false,"is_active":false,"create_time":1562060912748,"pattern":"","description":"自由流程","is_unique":false,"label":"自由流程","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"work_flow_id","define_type":"system","_id":"5d1b2870319d19c15d3f9c9b","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"display":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"t_6","max_length":100,"status":"released"},"record_type":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"record_type","is_unique":false,"label":"业务类型","type":"record_type","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":true,"api_name":"record_type","options":[{"is_active":true,"api_name":"default__c","description":"预设业务类型","label":"预设业务类型"}],"define_type":"package","option_id":"504eae3cf17c3d7223be4a3e0e893ecb","_id":"5d1b2870319d19c15d3f9c9c","is_index_field":false,"is_single":false,"index_name":"r_type","config":{},"status":"released"},"account_id":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"客户名称","is_unique":false,"label":"客户名称","target_api_name":"AccountObj","type":"object_reference","target_related_list_name":"account_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"is_required":true,"api_name":"account_id","define_type":"package","_id":"5d1b2870319d19c15d3f9c9d","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":0,"attrs":{"target_related_list_label":1,"wheres":1,"is_required":0,"default_value":0,"label":1,"help_text":1}},"index_name":"s_19","status":"new"},"commision_info":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"","description":"提成信息","is_unique":false,"label":"提成信息","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"commision_info","define_type":"package","_id":"5d1b2870319d19c15d3f9c9e","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1}},"index_name":"t_7","max_length":100,"status":"released"},"confirmed_delivery_date":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"发货时间","is_unique":false,"label":"发货时间","time_zone":"GMT+8","type":"date_time","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"confirmed_delivery_date","define_type":"package","date_format":"yyyy-MM-dd HH:mm","_id":"5d1b2870319d19c15d3f9c9f","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"l_5","status":"released"},"_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"_id","api_name":"_id","description":"_id","status":"released","index_name":"_id","create_time":1527155146247},"created_by":{"type":"employee","define_type":"system","is_index":true,"is_need_convert":true,"is_required":false,"is_unique":false,"is_single":true,"api_name":"created_by","status":"released","label":"创建人","is_active":true,"index_name":"crt_by","create_time":1527155146247,"description":""},"last_modified_by":{"type":"employee","define_type":"system","is_index":true,"is_need_convert":true,"is_required":false,"is_unique":false,"is_single":true,"api_name":"last_modified_by","status":"released","is_active":true,"index_name":"md_by","label":"最后修改人","create_time":1527155146247,"description":""},"package":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"package","api_name":"package","description":"package","status":"released","create_time":1527155146247,"index_name":"pkg"},"tenant_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":true,"is_unique":false,"max_length":200,"pattern":"","label":"tenant_id","api_name":"tenant_id","description":"tenant_id","status":"released","create_time":1527155146247,"index_name":"ei"},"object_describe_api_name":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":true,"is_unique":false,"max_length":200,"pattern":"","label":"object_describe_api_name","api_name":"object_describe_api_name","description":"object_describe_api_name","status":"released","index_name":"api_name","create_time":1527155146247},"version":{"type":"number","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"round_mode":4,"length":8,"decimal_places":0,"label":"version","api_name":"version","description":"version","status":"released","index_name":"version","create_time":1527155146247},"object_describe_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"object_describe_id","api_name":"object_describe_id","description":"object_describe_id","status":"released","index_name":"object_describe_id","create_time":1527155146247},"create_time":{"type":"date_time","define_type":"system","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"time_zone":"","date_format":"yyyy-MM-dd HH:mm:ss","label":"创建时间","api_name":"create_time","description":"create_time","status":"released","index_name":"crt_time","create_time":1527155146247},"last_modified_time":{"type":"date_time","define_type":"system","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"time_zone":"","date_format":"yyyy-MM-dd HH:mm:ss","label":"最后修改时间","api_name":"last_modified_time","description":"last_modified_time","status":"released","index_name":"md_time","create_time":1527155146247},"is_deleted":{"type":"true_or_false","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"label":"is_deleted","api_name":"is_deleted","description":"is_deleted","default_value":false,"status":"released","index_name":"is_del","create_time":1527155146247},"out_tenant_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"外部企业","api_name":"out_tenant_id","description":"out_tenant_id","status":"released","index_name":"o_ei","create_time":1527155146247,"config":{"display":0}},"out_owner":{"type":"employee","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"is_single":true,"api_name":"out_owner","index_name":"o_owner","status":"released","label":"外部负责人","config":{"display":1},"create_time":1527155146247,"description":""},"data_own_department":{"type":"department","define_type":"package","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"is_single":true,"api_name":"data_own_department","status":"released","label":"归属部门","is_active":true,"index_name":"data_owner_dept_id","create_time":1527155146247,"description":""}},"actions":{"Lock":{"tenant_id":"-100","action_class":"LockAction","action_id":"5d1b2870319d19c15d3f9ca6","describe_id":"5b0689ca9e787b86896a1a24","action_code":"Lock","source_type":"java_spring","label":"加锁"},"ChangeOwner":{"tenant_id":"-100","action_class":"ChangeOwnerAction","action_id":"5d1b2870319d19c15d3f9ca5","describe_id":"5b0689ca9e787b86896a1a24","action_code":"ChangeOwner","source_type":"java_spring"},"Unlock":{"tenant_id":"-100","action_class":"UnlockAction","action_id":"5d1b2870319d19c15d3f9ca4","describe_id":"5b0689ca9e787b86896a1a24","action_code":"Unlock","source_type":"java_spring","label":"解锁"},"ConfirmDelivery":{"tenant_id":"-100","action_class":"ConfirmDeliveryAction","action_id":"5d1b2870319d19c15d3f9ca3","describe_id":"5b0689ca9e787b86896a1a24","action_code":"ConfirmDelivery","source_type":"java_spring"},"AddDeliveryNote":{"tenant_id":"-100","action_class":"AddDeliveryNoteAction","action_id":"5d1b2870319d19c15d3f9ca2","describe_id":"5b0689ca9e787b86896a1a24","action_code":"AddDeliveryNote","source_type":"java_spring","label":"创建发货单"},"AddTeamMember":{"tenant_id":"-100","action_class":"AddTeamMemberAction","action_id":"5d1b2870319d19c15d3f9ca1","describe_id":"5b0689ca9e787b86896a1a24","action_code":"AddTeamMember","source_type":"java_spring","label":"添加团队成员"},"ConfirmReceive":{"tenant_id":"-100","action_class":"ConfirmReceiveAction","action_id":"5d1b2870319d19c15d3f9ca0","describe_id":"5b0689ca9e787b86896a1a24","action_code":"ConfirmReceive","source_type":"java_spring"}},"index_version":1,"_id":"5b0689ca9e787b86896a1a24","tenant_id":"78586","is_udef":null,"api_name":"SalesOrderObj","created_by":"-1000","last_modified_by":"-1000","display_name":"销售订单","package":"CRM","record_type":null,"is_active":true,"icon_path":null,"version":11,"release_version":"6.4","plural_name":null,"define_type":"package","is_deleted":false,"last_modified_time":*************,"create_time":1527155146247,"store_table_name":"biz_sales_order","module":null,"icon_index":null,"description":"","visible_scope":null}'''
    @Shared
    String defaultMobileLayout = '''{"buttons":[],"components":[{"show_image":"","buttons":[],"api_name":"table_component","ref_object_api_name":"object_solitude__c","include_fields":[{"api_name":"name","label":"主属性","render_type":"text","field_name":"name","is_show_label":true},{"api_name":"created_by","label":"创建人","render_type":"employee","field_name":"created_by","is_show_label":true},{"api_name":"last_modified_time","label":"最后修改时间","render_type":"date_time","field_name":"last_modified_time","is_show_label":true}],"type":"table","is_show_tag":false}],"last_modified_time":1709621802842,"is_deleted":false,"version":2,"create_time":1692776935145,"_id":"64e5b9e7c5f90a0001880fc2","agent_type":"agent_type_mobile","is_show_fieldname":true,"layout_description":null,"api_name":"list_layout_J9tjO__c","what_api_name":null,"default_component":null,"created_by":"1000","display_name":"移动端默认列表页","is_default":true,"last_modified_by":"1000","layout_type":"list","package":"CRM","ref_object_api_name":"object_solitude__c","tenant_id":"74255","ui_event_ids":[],"hidden_buttons":[],"hidden_components":[],"namespace":null,"enable_mobile_layout":null,"layout_structure":{"layout":[{"columns":[{"width":"100%"}],"components":[["table_component"]]}]}}'''
    @Shared
    String defaultLayout = '''{"buttons":[],"components":[],"last_modified_time":1708238482797,"is_deleted":false,"version":20,"create_time":1654516712450,"_id":"629dd412b1627f0001782da6","agent_type":null,"is_show_fieldname":null,"layout_description":"","api_name":"default_list_layout","what_api_name":null,"default_component":null,"layout_structure":{"page_layout":{"layout_list":[{"layout":[{"components":[[]],"columns":[{"width":"100%"}]},{"components":[["list_component"]],"columns":[{"width":"100%"}]}],"isOpenRange":false,"components":[{"field_section":[],"buttons":[],"related_list_name":"","button_info":[{"exposed_button":2,"hidden":[],"page_type":"list","render_type":"list_normal","order":["Add_button_default","IntelligentForm_button_default","Import_button_default","Export_button_default","ExportFile_button_default"]},{"exposed_button":null,"hidden":[],"page_type":"list","render_type":"list_batch","order":["BulkHangTag_button_default","ChangeOwner_button_default","Abolish_button_default","AddTeamMember_button_default","DeleteTeamMember_button_default","Lock_button_default","Unlock_button_default","Export_button_default","ExportFile_button_default","ChangePartner_button_default","ChangePartnerOwner_button_default","DeletePartner_button_default","Print_button_default","button_Kwyf2__c","button_17rhd__c","Follow_button_default","Unfollow_button_default","button_S7tDe__c"]},{"exposed_button":0,"hidden":[],"page_type":"list","render_type":"list_single","order":["button_Kwyf2__c","button_17rhd__c","button_qC31n__c","Transform_button_default","button_S7tDe__c"]}],"nameI18nKey":"paas.udobj.list_page","filters_info":[{"page_type":"list","fields":["start_time__c","end_time__c"]}],"type":"list","isSticky":false,"all_page_summary_info":[],"view_info":[{"name":"list_view","is_default":true,"is_show":true},{"name":"split_view","is_default":false,"is_show":true},{"display_type":"list","time_dimension":["create_time","last_modified_time"],"name":"calendar_view","fields":["name","owner","start_time__c","end_time__c"],"is_default":false,"is_show":true},{"name":"map_view","bubble_info":{"field":"","options":[]},"fields":["field_2Cn77__c","name","owner_department","start_time__c","end_time__c"],"is_default":false,"is_show":true,"location_field":"field_EgkrH__c"}],"enable_selected_layout":false,"define_view_info":["list_view","split_view","calendar_view","map_view"],"api_name":"list_component","header":"列表页","filter_info":[],"grayLimit":1,"summary_info":[{"type":"sum","field_name":"field_zt7pl__c"},{"type":"sum","field_name":"field_L3B64__c"}],"scene_info":[{"hidden":[],"page_type":"list","render_type":"drop_down","order":["All","Participate","InCharge","SubInCharge","InChargeDept","Shared","SubParticipate","scene_Bs8st__c","scene_98Z0o__c","scene_2rBXc__c"]}]}],"labelPageName":"自定义页面","customerPageLayoutType":6,"canDelete":false,"labelIndex":"0"}],"pageMultiType":0,"defaultLabelIndex":0},"layout_structure_type":6},"created_by":"1000","display_name":"列表页默认布局","is_default":true,"last_modified_by":"1000","layout_type":"list_layout","package":"CRM","ref_object_api_name":"object_qep6N__c","tenant_id":"74255","ui_event_ids":[],"hidden_buttons":[],"hidden_components":["component_TcpiQ__c","filters","navigate","biDashboardCom"],"namespace":null,"enable_mobile_layout":false}'''
    @Shared
    String udefObjDescribeJson1 = '''{"tenant_id":"590064","package":"CRM","is_active":true,"last_modified_time":1655449827867,"create_time":1583823321321,"description":"*配置不许修改*\\n1、各种类型字段、依赖关系;\\n2、布局规则、验证规则;\\n3、自定义按钮验证;\\n4、上下游;\\n5、查重;\\n6、创建人自动添加到相关团队；\\n7、包含what和whatlist类型的字段；","last_modified_by":"1002","display_name":"HG-通用对象1","created_by":"1001","version":248,"is_open_display_name":false,"index_version":200,"icon_index":9,"is_deleted":false,"api_name":"object_3sbgg__c","icon_path":"A_201705_26_2d6252adc2ed42f1a3b50b849b9a7f97.png","is_udef":true,"define_type":"custom","_id":"5e6739d9db17cf0001e306f0","fields":{"field_l933i__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{"data_roles":["owner"]},"pattern":"^[0-9+-;,]{0,100}$","is_unique":false,"description":"","type":"phone_number","is_required":false,"define_type":"custom","is_single":false,"index_name":"p_2","verification":false,"is_index":true,"is_active":true,"create_time":1596182711239,"is_encrypted":false,"default_value":"","label":"手机-掩码","field_num":46,"api_name":"field_l933i__c","_id":"5f23d0b7b453c300011ba4e2","is_index_field":false,"is_show_mask":true,"status":"new","help_text":"去掩码角色=数据负责人"},"field_782a6__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1641958899620,"is_encrypted":false,"auto_adapt_places":false,"pattern":"^(((http[s]?|ftp):\\\\/\\\\/|www\\\\.)[a-z0-9\\\\.\\\\-]+\\\\.([a-z]{2,4})|((http[s]?|ftp):\\\\/\\\\/)?(([01]?[\\\\d]{1,2})|(2[0-4][\\\\d])|(25[0-5]))(\\\\.(([01]?[\\\\d]{1,2})|(2[0-4][\\\\d])|(25[0-5]))){3})(:\\\\d+)?(\\\\/[a-z0-9\\\\$\\\\^\\\\*\\\\+\\\\?\\\\(\\\\)\\\\{\\\\}\\\\.\\\\-_~!@#%&:;\\\\/=<>]*)?","is_unique":false,"description":"","default_value":"","label":"网址-详情","type":"url","field_num":93,"is_required":false,"api_name":"field_782a6__c","define_type":"custom","_id":"61de4df340f43700013833b6","is_index_field":false,"is_single":false,"index_name":"w_5","status":"new","help_text":""},"lock_rule":{"describe_api_name":"object_3sbgg__c","is_index":false,"is_active":true,"create_time":1608536844709,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定规则","default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","field_num":1,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"5fe0530ccfdf3c00016b116a","is_index_field":false,"label_r":"锁定规则","is_single":false,"index_name":"s_21","status":"new","help_text":""},"field_WV578__c":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"","type":"district","used_in":"component","is_required":false,"options":[],"define_type":"custom","cascade_parent_api_name":"field_bMCq1__c","is_single":false,"index_name":"s_15","is_index":true,"is_active":true,"create_time":1583823321354,"is_encrypted":false,"default_value":"","label":"区","field_num":2,"api_name":"field_WV578__c","_id":"5e6739d9db17cf0001e306f2","is_index_field":false,"config":{},"status":"new","help_text":""},"field_Sc27N__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"custom","input_mode":"scan","is_single":false,"index_name":"t_14","max_length":100,"is_index":true,"is_active":true,"create_time":1596182711237,"is_encrypted":false,"default_value":"","label":"文本-扫码+手写","field_num":45,"api_name":"field_Sc27N__c","_id":"5f23d0b7b453c300011ba4e1","is_index_field":false,"status":"new","help_text":""},"field_azp2e__c":{"describe_api_name":"object_3sbgg__c","sign_in_button_name":"签到","is_enable_sign_out":true,"auto_adapt_places":false,"is_unique":false,"description":"","group_type":"sign_in","type":"group","is_enable_modify_position":false,"sign_out_button_name":"签退","is_required":false,"define_type":"custom","is_single":false,"index_name":"s_17","is_index":false,"is_active":true,"create_time":1583823321336,"is_encrypted":false,"default_value":"","label":"签到组件","api_name":"field_azp2e__c","_id":"5e6739d9db17cf0001e306f4","fields":{"visit_status_field":"field_4xNiT__c","sign_in_location_field":"field_xe490__c","sign_in_status_field":"field_z3iaI__c","sign_in_time_field":"field_14eaj__c","sign_out_status_field":"field_pKo0c__c","interval_field":"field_1984l__c","sign_out_location_field":"field_J3rKk__c","sign_in_info_list_field":"sign_in_info__c","sign_out_time_field":"field_0Qfln__c"},"is_index_field":false,"status":"new","help_text":""},"field_z3iaI__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1583823321339,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"incomplete","label":"签到状态","type":"select_one","field_num":5,"used_in":"component","is_required":false,"api_name":"field_z3iaI__c","options":[{"font_color":"#2a304d","label":"已完成","value":"complete"},{"font_color":"#2a304d","label":"未完成","value":"incomplete"},{"font_color":"#2a304d","not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","_id":"5e6739d9db17cf0001e30768","is_index_field":false,"is_single":false,"config":{},"index_name":"s_25","status":"new","help_text":""},"field_4xNiT__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1583823321343,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"incomplete","label":"拜访状态","type":"select_one","field_num":6,"used_in":"component","is_required":false,"api_name":"field_4xNiT__c","options":[{"font_color":"#2a304d","label":"已完成","value":"complete"},{"font_color":"#2a304d","label":"未完成","value":"incomplete"},{"font_color":"#2a304d","not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","_id":"5e6739d9db17cf0001e30769","is_index_field":false,"is_single":false,"config":{},"index_name":"s_26","status":"new","help_text":""},"field_mL1ab__c":{"describe_api_name":"object_3sbgg__c","return_type":"text","expression_type":"js","auto_adapt_places":false,"is_unique":false,"description":"","type":"formula","decimal_places":2,"default_to_zero":false,"is_required":false,"define_type":"custom","is_single":false,"index_name":"t_25","is_index":true,"expression":"$object_describe_api_name$+' ' +$_id$","is_active":true,"create_time":1649214328481,"is_encrypted":false,"label":"info","field_num":106,"api_name":"field_mL1ab__c","_id":"624d0378985f6b0001c26013","is_index_field":false,"status":"new","help_text":""},"field_bMCq1__c":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"","type":"city","used_in":"component","is_required":false,"options":[],"define_type":"custom","cascade_parent_api_name":"field_v5R9B__c","is_single":false,"index_name":"s_11","is_index":true,"is_active":true,"create_time":1583823321353,"is_encrypted":false,"default_value":"","label":"市","field_num":9,"api_name":"field_bMCq1__c","_id":"5e6739d9db17cf0001e3076c","is_index_field":false,"config":{},"status":"new","help_text":""},"mc_exchange_rate":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":6,"default_to_zero":true,"is_required":false,"define_type":"package","label_r":"汇率","is_single":false,"index_name":"d_5","max_length":16,"is_index":true,"is_active":true,"create_time":1608693415886,"is_encrypted":false,"step_value":1,"display_style":"input","length":10,"default_value":"","label":"汇率","api_name":"mc_exchange_rate","_id":"5fe2b6a71567f400010b4ebd","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"field_1yPy5__c":{"describe_api_name":"object_3sbgg__c","auto_location":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"location","used_in":"component","is_required":false,"define_type":"custom","is_single":false,"index_name":"t_4","is_index":true,"is_active":true,"create_time":1616643723780,"is_encrypted":false,"is_geo_index":false,"label":"定位2","field_num":71,"api_name":"field_1yPy5__c","range_limit":false,"radius_range":100,"_id":"605c068b13598c00075f07cc","is_index_field":false,"status":"new","help_text":""},"field_g045g__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","wheres":[],"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_3","is_index":true,"is_active":true,"create_time":1619681262995,"is_encrypted":false,"target_api_name":"object_1W4fD__c","label":"HG-主对象-附近数据","target_related_list_name":"target_related_list_Gac1a__c","field_num":72,"target_related_list_label":"HG-通用对象1-附近查找","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_g045g__c","_id":"608a5ff0673529000140a768","is_index_field":true,"status":"new","help_text":""},"field_7ra4k__c":{"describe_api_name":"object_3sbgg__c","auto_location":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"location","used_in":"component","is_required":false,"define_type":"custom","is_single":false,"index_name":"t_3","is_index":true,"is_active":true,"create_time":1583823321356,"is_encrypted":false,"default_value":"","is_geo_index":false,"label":"定位","field_num":13,"api_name":"field_7ra4k__c","range_limit":false,"radius_range":100,"_id":"5e6739d9db17cf0001e30770","is_index_field":false,"status":"new","help_text":""},"life_status_before_invalid":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"作废前生命状态","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"作废前生命状态","is_single":false,"index_name":"t_13","max_length":256,"is_index":false,"is_active":true,"create_time":1608536844708,"is_encrypted":false,"default_value":"","label":"作废前生命状态","field_num":14,"is_need_convert":false,"api_name":"life_status_before_invalid","_id":"5fe0530ccfdf3c00016b116b","is_index_field":false,"status":"new","help_text":""},"field_yutgi__c":{"describe_api_name":"object_3sbgg__c","is_index":false,"is_active":true,"create_time":1608523236766,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"外部人员-多选","type":"out_employee","field_num":52,"is_required":false,"api_name":"field_yutgi__c","define_type":"package","_id":"5fe01de43cc05a00012ba76a","is_index_field":false,"is_single":false,"index_name":"a_18","status":"new","help_text":""},"field_2snD1__c":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"","type":"city","used_in":"component","is_required":false,"options":[],"define_type":"custom","cascade_parent_api_name":"field_WjKw7__c","is_single":false,"index_name":"s_41","is_index":true,"is_active":true,"create_time":1641958899623,"is_encrypted":false,"default_value":"","label":"市-x","field_num":96,"api_name":"field_2snD1__c","_id":"61de4df340f43700013833b9","is_index_field":false,"config":{},"status":"new","help_text":""},"field_Xhm22__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1583823321348,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"收款状态","type":"select_one","field_num":18,"used_in":"component","is_required":false,"api_name":"field_Xhm22__c","options":[{"font_color":"#2a304d","label":"未收款","value":"incomplete"},{"font_color":"#2a304d","label":"已收款","value":"complete"},{"font_color":"#2a304d","not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","_id":"5e6739d9db17cf0001e30777","is_index_field":false,"is_single":false,"config":{},"index_name":"s_35","status":"new","help_text":""},"field_dm3qx__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_9","max_length":14,"is_index":true,"is_active":true,"create_time":1650682003106,"is_encrypted":false,"step_value":1,"display_style":"step","length":12,"default_value":"","label":"数字","field_num":109,"api_name":"field_dm3qx__c","_id":"62636893cd44a10001a63cc3","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"field_459OP__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1641958899621,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"248","label":"国家-x","type":"country","field_num":94,"used_in":"component","is_required":false,"api_name":"field_459OP__c","options":[],"define_type":"custom","_id":"61de4df340f43700013833b7","is_index_field":false,"is_single":false,"config":{},"index_name":"s_42","status":"new","help_text":""},"field_0Qfln__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"date_time","default_to_zero":false,"used_in":"component","is_required":false,"define_type":"custom","is_single":false,"index_name":"l_2","is_index":true,"is_active":true,"create_time":1583823321340,"is_encrypted":false,"default_value":"","label":"签退时间","time_zone":"GMT+8","field_num":23,"api_name":"field_0Qfln__c","date_format":"yyyy-MM-dd HH:mm","_id":"5e6739d9db17cf0001e3077d","is_index_field":false,"status":"new","help_text":""},"field_wG6yH__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{},"is_unique":false,"description":"","type":"currency","decimal_places":2,"default_to_zero":true,"used_in":"component","is_required":false,"define_type":"custom","is_single":false,"index_name":"d_3","max_length":14,"is_index":true,"is_active":true,"create_time":1583823321346,"is_encrypted":false,"length":12,"default_value":"","label":"收款金额","currency_unit":"￥","field_num":25,"api_name":"field_wG6yH__c","_id":"5e6739d9db17cf0001e30780","is_index_field":false,"is_show_mask":false,"round_mode":4,"status":"new","help_text":""},"field_iF0qG__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1596182711240,"is_encrypted":false,"auto_adapt_places":false,"remove_mask_roles":{"data_roles":["owner","data_owner_main_dept_leader"]},"pattern":"\\\\w+((-w+)|(\\\\.\\\\w+))*\\\\@[A-Za-z0-9]+((\\\\.|-)[A-Za-z0-9]+)*\\\\.[A-Za-z0-9]+","is_unique":false,"description":"","default_value":"","label":"邮箱-掩码","type":"email","field_num":47,"is_required":false,"api_name":"field_iF0qG__c","define_type":"custom","_id":"5f23d0b7b453c300011ba4e3","is_index_field":false,"is_single":false,"is_show_mask":true,"index_name":"w_1","status":"new","help_text":"去掩码角色：负责人+所属部门的负责人"},"version":{"describe_api_name":"object_3sbgg__c","is_index":false,"create_time":1583823321321,"length":8,"description":"version","is_unique":false,"label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"field_xe490__c":{"describe_api_name":"object_3sbgg__c","auto_location":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"location","used_in":"component","is_required":false,"define_type":"custom","is_single":false,"index_name":"t_2","is_index":true,"is_active":true,"create_time":1583823321338,"is_encrypted":false,"default_value":"","is_geo_index":false,"label":"签到地址","field_num":27,"api_name":"field_xe490__c","range_limit":false,"radius_range":100,"_id":"5e6739d9db17cf0001e30784","is_index_field":false,"status":"new","help_text":""},"field_3N5lk__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_8","max_length":14,"is_index":true,"is_active":true,"create_time":1650365410436,"is_encrypted":false,"step_value":1,"display_style":"input","length":12,"default_value":"","label":"数字-千分位","field_num":108,"api_name":"field_3N5lk__c","_id":"625e93e2ce8f8500014dd258","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"field_KKTKB__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1616643723780,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"区2","type":"district","field_num":68,"used_in":"component","is_required":false,"api_name":"field_KKTKB__c","options":[],"define_type":"custom","_id":"605c068b13598c00075f07c9","is_index_field":false,"cascade_parent_api_name":"field_em0wd__c","is_single":false,"config":{},"index_name":"s_14","status":"new","help_text":""},"tenant_id":{"describe_api_name":"object_3sbgg__c","is_index":false,"is_active":true,"create_time":1583823321321,"pattern":"","description":"tenant_id","is_unique":false,"label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","status":"released","max_length":200},"field_m4cC0__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","wheres":[],"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_7","is_index":true,"is_active":true,"create_time":1611546542988,"is_encrypted":false,"target_api_name":"object_1W4fD__c","label":"HG主对象-全部","target_related_list_name":"target_related_list_2ghC4__c","field_num":54,"target_related_list_label":"HG-通用对象1","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_m4cC0__c","_id":"600e3fafc70f5f0001181643","is_index_field":true,"status":"new","help_text":""},"data_own_organization":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1611846111739,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"归属组织","type":"department","is_need_convert":false,"wheres":[],"is_required":false,"api_name":"data_own_organization","define_type":"package","_id":"6012d1df5d449e0001cdabdb","is_index_field":false,"label_r":"归属组织","is_single":true,"index_name":"a_2","status":"released","help_text":""},"field_UbH83__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1583823321325,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"单选","type":"select_one","field_num":4,"is_required":false,"api_name":"field_UbH83__c","options":[{"font_color":"#2a304d","label":"示例选项","value":"option1"},{"font_color":"#30c776","label":"单选1","value":"n6T0tz6qi"},{"font_color":"#ff8000","label":"单选2","value":"K2szV023M"},{"font_color":"#936de3","label":"单选3","value":"CjIt1ED7w"},{"font_color":"#d3e41e","label":"summer","value":"9I2b0zr6K"},{"font_color":"#2a304d","label":"today","value":"3ScBhy9zY"},{"font_color":"#e613ea","label":"\\"腾的说法\\"","value":"f8nUt462z"},{"label":"a","value":"92bhghRAi"},{"label":"b","value":"34z1his6s"},{"label":"c","value":"v2but17Ky"},{"label":"d","value":"ondv7eM0m"},{"label":"e","value":"dOnrCooeE"},{"label":"f","value":"f962ww3Q2"},{"label":"g","value":"S6yzb11w2"},{"label":"h","value":"la1di2Cq0"},{"label":"l","value":"lcvLjG98r"},{"label":"o","value":"s7q130Ufn"},{"label":"p","value":"11GyT538x"},{"label":"q","value":"X58Zc8t51"},{"label":"r","value":"6soSP3c19"},{"label":"ss","value":"t32Z47xhK"},{"label":"t","value":"qi6KS42Yr"},{"label":"u","value":"k7zrILp2r"},{"label":"v","value":"Cy30d0tt9"},{"label":"w","value":"B6qc87509"},{"label":"s","value":"xthtfXWxn"},{"label":"y","value":"yZHaR1rmz"},{"label":"z","value":"C0US6XODd"},{"label":"示例选项3323","value":"cph7u2kcq"},{"label":"示例选项32432","value":"Hypp43D61"},{"label":"示例选项4324324","value":"szq02T107"},{"label":"示例选项432432","value":"DMLXay01u"},{"font_color":"#ff522a","label":"其他","value":"other"}],"define_type":"custom","_id":"5e6739d9db17cf0001e3073d","is_index_field":false,"is_single":false,"config":{},"index_name":"s_27","status":"new","help_text":""},"field_oba0e__c":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"","file_source":[],"type":"file_attachment","is_required":false,"define_type":"custom","is_single":false,"support_file_types":[],"index_name":"a_8","file_amount_limit":10,"is_index":true,"is_active":true,"create_time":1583823321330,"is_encrypted":false,"default_value":"","label":"附件","field_num":7,"file_size_limit":104857600,"api_name":"field_oba0e__c","_id":"5e6739d9db17cf0001e3076a","is_index_field":false,"status":"new","help_text":"单个文件不得超过100M\\n可上传10张"},"field_jO2R8__c":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"","file_source":[],"type":"image","is_required":false,"define_type":"custom","is_single":false,"support_file_types":["jpg","gif","jpeg","png"],"index_name":"a_12","file_amount_limit":5,"is_index":true,"watermark":[{"type":"variable","value":"current_user"},{"type":"variable","value":"current_time"},{"type":"variable","value":"current_address"}],"is_active":true,"create_time":1583823321329,"is_encrypted":false,"default_value":"","label":"图片-水印","is_watermark":true,"field_num":8,"is_ocr_recognition":false,"file_size_limit":20971520,"api_name":"field_jO2R8__c","is_need_cdn":false,"_id":"5e6739d9db17cf0001e3076b","is_index_field":false,"identify_type":"","status":"new","help_text":"单个图片不得超过20M\\n可上传5张"},"field_vWF6u__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1622710186351,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","where_type":"field","label":"部门(多选)","type":"department_many","field_num":77,"wheres":[],"is_required":false,"api_name":"field_vWF6u__c","define_type":"custom","_id":"60b897aa3c04f40001dbcacb","is_index_field":false,"is_single":false,"index_name":"a_3","status":"new","help_text":""},"field_J3rKk__c":{"describe_api_name":"object_3sbgg__c","auto_location":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"location","used_in":"component","is_required":false,"define_type":"custom","is_single":false,"index_name":"t_1","is_index":true,"is_active":true,"create_time":1583823321341,"is_encrypted":false,"default_value":"","is_geo_index":false,"label":"签退地址","field_num":10,"api_name":"field_J3rKk__c","range_limit":false,"radius_range":100,"_id":"5e6739d9db17cf0001e3076d","is_index_field":false,"status":"new","help_text":""},"field_CZ823__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","type":"object_reference_many","wheres":[],"is_required":false,"define_type":"custom","is_single":false,"index_name":"a_21","is_index":true,"is_active":true,"create_time":1629256048789,"is_encrypted":false,"target_api_name":"object_1W4fD__c","label":"hg主对象(多)","target_related_list_name":"target_related_list_SwRId__c","field_num":81,"target_related_list_label":"HG-通用对象1(多)","action_on_target_delete":"set_null","api_name":"field_CZ823__c","_id":"611c79707f8f0a000149e301","is_index_field":false,"status":"new","help_text":""},"field_tLv3r__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"function","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","wheres":[{"connector":"OR","filters":[{"value_type":9,"operator":"IN","field_values":["func_M678g__c"],"field_name":"id"}]}],"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_38","is_index":true,"is_active":true,"create_time":1637437655036,"is_encrypted":false,"target_api_name":"object_1W4fD__c","label":"查找关联","target_related_list_name":"target_related_list_N2jmy__c","field_num":83,"target_related_list_label":"HG-通用对象1(预设)","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_tLv3r__c","_id":"619950d7f3a1a100018dbebf","is_index_field":true,"status":"new","help_text":""},"field_id_field1__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"used_in":"component","is_required":false,"define_type":"custom","input_mode":"","cascade_parent_api_name":"field_api_name_field1__c","is_single":false,"index_name":"t_23","max_length":256,"is_index":true,"is_active":true,"create_time":1649214120511,"is_encrypted":false,"default_value":"","label":"what关联数据1","field_num":105,"api_name":"field_id_field1__c","_id":"624d02a85fc9840001954210","is_index_field":false,"status":"new","help_text":""},"field_pKo0c__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1583823321342,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"incomplete","label":"签退状态","type":"select_one","field_num":15,"used_in":"component","is_required":false,"api_name":"field_pKo0c__c","options":[{"font_color":"#2a304d","label":"已完成","value":"complete"},{"font_color":"#2a304d","label":"未完成","value":"incomplete"},{"font_color":"#2a304d","not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","_id":"5e6739d9db17cf0001e30772","is_index_field":false,"is_single":false,"config":{},"index_name":"s_24","status":"new","help_text":""},"field_Ic1vW__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1641958899624,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"区-x","type":"district","field_num":97,"used_in":"component","is_required":false,"api_name":"field_Ic1vW__c","options":[],"define_type":"custom","_id":"61de4df340f43700013833ba","is_index_field":false,"cascade_parent_api_name":"field_2snD1__c","is_single":false,"config":{},"index_name":"s_43","status":"new","help_text":""},"field_Xt5rZ__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":true,"auto_adapt_places":false,"remove_mask_roles":{},"pattern":"^[0-9+-;,]{0,100}$","is_unique":false,"description":"","type":"phone_number","is_required":false,"define_type":"custom","is_single":false,"index_name":"p_1","verification":false,"is_index":true,"is_active":true,"create_time":1597109212665,"is_encrypted":false,"default_value":"$field_m4cC0__c__r.field_f8i97__c$","label":"手机","field_num":48,"api_name":"field_Xt5rZ__c","_id":"5f31f3dc76019f00019e11c4","is_index_field":false,"is_show_mask":false,"status":"new","help_text":"手机号码触发UI事件-校验"},"field_JnGaP__c":{"describe_api_name":"object_3sbgg__c","is_index":false,"is_active":true,"create_time":1616643723780,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"乡镇2","type":"town","field_num":69,"used_in":"component","is_required":false,"api_name":"field_JnGaP__c","options":[],"define_type":"custom","_id":"605c068b13598c00075f07ca","is_index_field":false,"cascade_parent_api_name":"field_KKTKB__c","is_single":false,"config":{},"index_name":"s_36","status":"new","help_text":""},"field_gb1gE__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":true,"auto_adapt_places":false,"remove_mask_roles":{},"is_unique":false,"description":"","type":"currency","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_2","max_length":14,"is_index":true,"is_active":true,"create_time":1583823321326,"is_encrypted":false,"length":12,"default_value":"IF($field_UbH83__c$== '单选1', 10,IF($field_UbH83__c$== '单选2', 20, 30))","label":"金额","currency_unit":"￥","field_num":16,"api_name":"field_gb1gE__c","_id":"5e6739d9db17cf0001e30774","is_index_field":false,"is_show_mask":false,"round_mode":4,"status":"new","help_text":""},"mc_functional_currency":{"describe_api_name":"object_3sbgg__c","is_index":false,"is_active":true,"create_time":1608693415895,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"本位币","type":"select_one","is_required":false,"api_name":"mc_functional_currency","options":[{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"5fe2b6a71567f400010b4ebe","is_index_field":false,"label_r":"本位币","is_single":false,"config":{},"index_name":"s_32","status":"new","help_text":""},"field_og2vn__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_values":["default__c"],"field_name":"record_type"},{"value_type":0,"operator":"HASANYOF","field_values":["option1","yGO1lh1uP","other"],"field_name":"field_CC5TG__c"}]}],"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_6","is_index":true,"is_active":true,"create_time":1611546627764,"is_encrypted":false,"target_api_name":"object_1W4fD__c","label":"HG-主对象-普通条件","target_related_list_name":"target_related_list_02pZ9__c","field_num":55,"target_related_list_label":"HG-通用对象1-普通条件","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_og2vn__c","_id":"600e40049fd14a00016ed189","is_index_field":true,"status":"new","help_text":"条件：\\n1、业务类型=预设业务类型\\n2、单选属于：第一季度、第二季度、其他"},"field_24E1Y__c":{"describe_api_name":"object_3sbgg__c","is_index":false,"is_active":true,"create_time":1583823321345,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"支付组件","group_type":"payment","type":"group","amount_is_readonly":false,"is_required":false,"api_name":"field_24E1Y__c","define_type":"custom","_id":"5e6739d9db17cf0001e30779","fields":{"pay_time_field":"field_1tD9s__c","pay_status_field":"field_Xhm22__c","pay_type_field":"field_q7SHg__c","pay_amount_field":"field_wG6yH__c"},"is_index_field":false,"amount_input_type":"manual_input","is_single":false,"index_name":"s_20","status":"new","help_text":""},"last_modified_time":{"describe_api_name":"object_3sbgg__c","is_index":true,"create_time":1583823321321,"description":"last_modified_time","is_unique":false,"label":"最后修改时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"md_time","status":"released"},"field_9n19v__c":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"","type":"province","used_in":"component","is_required":false,"options":[],"define_type":"custom","cascade_parent_api_name":"field_ve91m__c","is_single":false,"index_name":"s_23","is_index":true,"is_active":true,"create_time":1616643723780,"is_encrypted":false,"default_value":"","label":"省2","field_num":66,"api_name":"field_9n19v__c","_id":"605c068b13598c00075f07c7","is_index_field":false,"config":{},"status":"new","help_text":""},"life_status":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"","type":"select_one","is_required":false,"options":[{"font_color":"#2a304d","label":"未生效","value":"ineffective"},{"font_color":"#2a304d","label":"审核中","value":"under_review"},{"font_color":"#2a304d","label":"正常","value":"normal"},{"font_color":"#2a304d","label":"变更中","value":"in_change"},{"font_color":"#2a304d","label":"作废","value":"invalid"}],"define_type":"package","label_r":"生命状态","is_single":false,"index_name":"s_31","is_index":true,"is_active":true,"create_time":1583823321322,"is_encrypted":false,"default_value":"normal","label":"生命状态","field_num":22,"is_need_convert":false,"api_name":"life_status","_id":"5e6739d9db17cf0001e3077c","is_index_field":false,"config":{},"status":"new","help_text":""},"field_yP931__c":{"describe_api_name":"object_3sbgg__c","is_index":false,"is_active":true,"create_time":1583823321350,"is_encrypted":false,"auto_adapt_places":false,"is_support_town":false,"is_unique":false,"description":"","default_value":"","label":"地区定位-未开启乡镇","group_type":"area","type":"group","is_required":false,"api_name":"field_yP931__c","define_type":"custom","_id":"5e6739d9db17cf0001e3077e","fields":{"area_country":"field_tn1oR__c","area_location":"field_7ra4k__c","area_detail_address":"field_7Qtch__c","area_city":"field_bMCq1__c","area_province":"field_v5R9B__c","area_district":"field_WV578__c"},"is_index_field":false,"is_single":false,"index_name":"s_19","status":"new","help_text":""},"field_id_field__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"used_in":"component","is_required":false,"define_type":"custom","input_mode":"","cascade_parent_api_name":"field_api_name_field__c","is_single":false,"index_name":"t_21","max_length":256,"is_index":true,"is_active":true,"create_time":1648877949809,"is_encrypted":false,"default_value":"","label":"what关联数据","field_num":103,"api_name":"field_id_field__c","_id":"6247e17d9a5d2a0001d4ec0a","is_index_field":false,"status":"new","help_text":""},"field_6vAoR__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1583823321332,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":true,"label":"布尔值","type":"true_or_false","field_num":26,"is_required":false,"api_name":"field_6vAoR__c","options":[{"font_color":"#2a304d","label":"是","value":true},{"font_color":"#2a304d","label":"否","value":false}],"define_type":"custom","_id":"5e6739d9db17cf0001e30781","is_index_field":false,"is_single":false,"index_name":"b_1","status":"new","help_text":""},"out_tenant_id":{"describe_api_name":"object_3sbgg__c","is_index":false,"is_active":true,"create_time":1583823321321,"pattern":"","description":"out_tenant_id","is_unique":false,"label":"out_tenant_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","config":{"display":0},"index_name":"o_ei","status":"released","max_length":200},"related_object":{"describe_api_name":"object_3sbgg__c","is_index":false,"is_active":true,"create_time":1641898135339,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"动态关联","group_type":"what_list","type":"group","relation_table":"biz_behavior_record_relation","wheres":[{"func_api_name":"func_wwaGP__c"}],"is_required":false,"api_name":"related_object","define_type":"package","_id":"61dd609726e0410001831576","fields":{"id_field":"related_object_data","api_name_field":"related_api_names"},"is_index_field":false,"is_single":false,"index_name":"s_39","status":"released","help_text":""},"field_api_name_field__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"used_in":"component","is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_22","max_length":256,"is_index":true,"is_active":true,"create_time":1648877949809,"is_encrypted":false,"default_value":"","label":"what关联对象","field_num":102,"api_name":"field_api_name_field__c","_id":"6247e17d9a5d2a0001d4ec09","is_index_field":false,"status":"new","help_text":""},"field_hDD28__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"function","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","wheres":[{"connector":"OR","filters":[{"value_type":9,"operator":"IN","field_values":["func_l1tdw__c"],"field_name":"id"}]}],"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_4","is_index":true,"is_active":true,"create_time":1611558778138,"is_encrypted":false,"target_api_name":"object_1W4fD__c","label":"HG-主对象-QueryTemplate","target_related_list_name":"target_related_list_dl02n__c","field_num":57,"target_related_list_label":"HG-通用对象1-template","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_hDD28__c","_id":"600e6f7aa2f664000131b53f","is_index_field":true,"status":"new","help_text":"QueryTemplate"},"field_bD19E__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"function","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","wheres":[{"connector":"OR","filters":[{"value_type":9,"operator":"IN","field_values":["func_l7p7r__c"],"field_name":"id"}]}],"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_5","is_index":true,"is_active":true,"create_time":1611546844000,"is_encrypted":false,"target_api_name":"object_1W4fD__c","label":"HG-主对象-函数-List","target_related_list_name":"target_related_list_ww87l__c","field_num":56,"target_related_list_label":"HG-通用对象1-List","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_bD19E__c","_id":"600e40dcc3e9e500017c1a8b","is_index_field":true,"status":"new","help_text":"List"},"field_mgw82__c":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"","file_source":[],"type":"image","is_required":false,"define_type":"custom","is_single":false,"support_file_types":["jpg","gif","jpeg","png"],"index_name":"a_10","file_amount_limit":100,"is_index":true,"watermark":[{"type":"variable","value":"current_user"},{"type":"variable","value":"current_time"},{"type":"variable","value":"current_address"}],"is_active":true,"create_time":1589769075910,"is_encrypted":false,"label":"图片2-100张","is_watermark":false,"field_num":37,"is_ocr_recognition":false,"file_size_limit":20971520,"api_name":"field_mgw82__c","is_need_cdn":false,"_id":"5ec1f3744d1392000148eb78","is_index_field":false,"identify_type":"","status":"new","help_text":"单个图片不得超过20M"},"field_209tq__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{},"pattern":"^[0-9+-;,]{0,100}$","is_unique":false,"description":"","type":"phone_number","is_required":false,"define_type":"custom","is_single":false,"index_name":"p_3","verification":false,"is_index":true,"is_active":true,"create_time":1641957911348,"is_encrypted":false,"default_value":"","label":"手机1","field_num":88,"api_name":"field_209tq__c","_id":"61de4a1730da62000150c590","is_index_field":false,"is_show_mask":false,"status":"new","help_text":""},"field_gyxsi__c":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"","file_source":[],"type":"image","is_required":false,"define_type":"custom","is_single":false,"support_file_types":["jpg","gif","jpeg","png"],"index_name":"a_9","file_amount_limit":10,"is_index":true,"watermark":[{"type":"variable","value":"current_user"},{"type":"variable","value":"current_time"},{"type":"variable","value":"current_address"}],"is_active":true,"create_time":1616484102317,"is_encrypted":false,"label":"图片ocr-身份证","is_watermark":false,"field_num":63,"is_ocr_recognition":true,"file_size_limit":20971520,"api_name":"field_gyxsi__c","is_need_cdn":false,"_id":"60599706fa5dc80001d8a556","is_index_field":false,"identify_type":"IdCard","status":"new","help_text":"单个图片不得超过20M"},"field_npMyn__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1583823321335,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","where_type":"field","label":"人员","type":"employee","field_num":28,"wheres":[],"is_required":false,"api_name":"field_npMyn__c","define_type":"custom","_id":"5e6739d9db17cf0001e30785","is_index_field":false,"is_single":true,"index_name":"a_4","status":"new","help_text":""},"field_r348s__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{},"pattern":"^[0-9+-;,]{0,100}$","is_unique":false,"description":"","type":"phone_number","is_required":false,"define_type":"custom","is_single":false,"index_name":"p_4","verification":false,"is_index":true,"is_active":true,"create_time":1641958899619,"is_encrypted":false,"default_value":"","label":"手机-详情","field_num":92,"api_name":"field_r348s__c","_id":"61de4df340f43700013833b5","is_index_field":false,"is_show_mask":false,"status":"new","help_text":""},"field_tn1oR__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1583823321351,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"248","label":"国家","type":"country","field_num":29,"used_in":"component","is_required":false,"api_name":"field_tn1oR__c","options":[],"define_type":"custom","_id":"5e6739d9db17cf0001e30786","is_index_field":false,"is_single":false,"config":{},"index_name":"s_12","status":"new","help_text":""}'''
    @Shared
    String udefObjDescribeJson2 = ''',"field_iACg1__c":{"describe_api_name":"object_3sbgg__c","auto_location":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"location","used_in":"component","is_required":false,"define_type":"custom","is_single":false,"index_name":"t_15","is_index":true,"is_active":true,"create_time":1641958899626,"is_encrypted":false,"is_geo_index":false,"label":"定位-x","field_num":99,"api_name":"field_iACg1__c","range_limit":false,"radius_range":100,"_id":"61de4df340f43700013833bc","is_index_field":false,"status":"new","help_text":""},"field_ejq16__c":{"describe_api_name":"object_3sbgg__c","is_index":false,"is_active":true,"create_time":1616643723618,"is_encrypted":false,"auto_adapt_places":false,"is_support_town":true,"is_unique":false,"description":"","label":"地区定位-开启定位","group_type":"area","type":"group","is_required":false,"api_name":"field_ejq16__c","define_type":"custom","_id":"605c068b13598c00075f07cd","fields":{"area_country":"field_ve91m__c","area_location":"field_1yPy5__c","area_detail_address":"field_oK6t6__c","area_town":"field_JnGaP__c","area_city":"field_em0wd__c","area_province":"field_9n19v__c","area_district":"field_KKTKB__c"},"is_index_field":false,"is_single":false,"index_name":"s_18","status":"new","help_text":""},"field_rxej1__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1637839612882,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"function","label":"人员3","type":"employee","field_num":85,"wheres":[{"connector":"OR","filters":[{"value_type":9,"operator":"IN","field_values":["func_l1tdw__c"],"field_name":"id"}]}],"is_required":false,"api_name":"field_rxej1__c","define_type":"custom","_id":"619f72fd61368100011a5a64","is_index_field":false,"is_single":true,"index_name":"a_23","status":"new","help_text":""},"field_r1i2a__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{"role":["00000000000000000000000000000006"]},"is_unique":false,"description":"","type":"currency","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_1","max_length":14,"is_index":true,"is_active":true,"create_time":1589872524117,"is_encrypted":false,"length":12,"default_value":"","label":"金额-掩码","currency_unit":"￥","field_num":38,"api_name":"field_r1i2a__c","_id":"5ec3878cb87faf00011a1a4b","is_index_field":false,"is_show_mask":true,"round_mode":4,"status":"new","help_text":"除了管理员，其他人都掩码展示"},"field_1tD9s__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"date_time","default_to_zero":false,"used_in":"component","is_required":false,"define_type":"custom","is_single":false,"index_name":"l_4","is_index":true,"is_active":true,"create_time":1583823321349,"is_encrypted":false,"default_value":"","label":"收款时间","time_zone":"GMT+8","field_num":33,"api_name":"field_1tD9s__c","date_format":"yyyy-MM-dd HH:mm:ss","_id":"5e6739d9db17cf0001e3078b","is_index_field":false,"status":"new","help_text":""},"field_k1KOn__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1596180755081,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"单选-子","type":"select_one","field_num":42,"is_required":false,"api_name":"field_k1KOn__c","options":[{"font_color":"#2a304d","label":"子1-1","value":"option1"},{"font_color":"#2a304d","label":"子1-2","value":"hYhUuSzGl"},{"font_color":"#2a304d","label":"子2-1","value":"04fx2IP3g"},{"font_color":"#2a304d","label":"子2-2","value":"bvv8GA2my"},{"font_color":"#2a304d","label":"其他","value":"other"}],"define_type":"custom","_id":"5f23c913c0bc250001d175b2","is_index_field":false,"cascade_parent_api_name":"field_eZF2k__c","is_single":false,"config":{},"index_name":"s_30","status":"new","help_text":""},"field_rBj2I__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":true,"auto_adapt_places":false,"is_unique":false,"description":"","type":"date","default_to_zero":false,"is_required":false,"define_type":"custom","is_single":false,"index_name":"l_1","is_index":true,"is_active":true,"create_time":1583823321327,"is_encrypted":false,"default_value":"$currentDate__g$","label":"日期","time_zone":"GMT+8","field_num":34,"api_name":"field_rBj2I__c","date_format":"yyyy-MM-dd","_id":"5e6739d9db17cf0001e3078c","is_index_field":false,"status":"new","help_text":""},"field_whaht1__c":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_support_town":false,"is_unique":false,"description":"","group_type":"what","type":"group","wheres":"[]","is_required":false,"define_type":"custom","is_single":false,"index_name":"s_46","is_index":false,"id_field":"field_id_field__c","is_active":true,"create_time":1648877949668,"is_encrypted":false,"label":"what组件","api_name":"field_whaht1__c","_id":"6247e17d9a5d2a0001d4ec0b","fields":{"id_field":"field_id_field__c","api_name_field":"field_api_name_field__c"},"is_index_field":false,"api_name_field":"field_api_name_field__c","status":"new","help_text":""},"field_14eaj__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"date_time","default_to_zero":false,"used_in":"component","is_required":false,"define_type":"custom","is_single":false,"index_name":"l_3","is_index":true,"is_active":true,"create_time":1583823321337,"is_encrypted":false,"default_value":"","label":"签到时间","time_zone":"GMT+8","field_num":35,"api_name":"field_14eaj__c","date_format":"yyyy-MM-dd HH:mm","_id":"5e6739d9db17cf0001e3078d","is_index_field":false,"status":"new","help_text":""},"field_whaht__c":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_support_town":false,"is_unique":false,"description":"","group_type":"what","type":"group","wheres":"[]","is_required":false,"define_type":"custom","is_single":false,"index_name":"s_47","is_index":false,"id_field":"field_id_field1__c","is_active":true,"create_time":1649214120368,"is_encrypted":false,"label":"what组件1","api_name":"field_whaht__c","_id":"624d02a85fc9840001954211","fields":{"id_field":"field_id_field1__c","api_name_field":"field_api_name_field1__c"},"is_index_field":false,"api_name_field":"field_api_name_field1__c","status":"new","help_text":""},"field_i12Ct__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_16","max_length":100,"is_index":true,"is_active":true,"create_time":1641958899616,"is_encrypted":false,"default_value":"","label":"单行文本-详情","field_num":89,"api_name":"field_i12Ct__c","_id":"61de4df340f43700013833b2","is_index_field":false,"status":"new","help_text":""},"field_p24bC__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","wheres":[],"is_required":false,"define_type":"custom","input_mode":"scan","is_single":false,"index_name":"s_2","is_index":true,"is_active":true,"create_time":1596180539266,"is_encrypted":false,"target_api_name":"NewOpportunityObj","label":"商机2.0","target_related_list_name":"target_related_list_Gqr6n__c","field_num":40,"target_related_list_label":"zm-回归测试","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_p24bC__c","_id":"5f23c83bc0bc250001d1745d","is_index_field":true,"status":"new","help_text":"1、可扫码\\n2、扫码配合函数处理："},"field_WjKw7__c":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"","type":"province","used_in":"component","is_required":false,"options":[],"define_type":"custom","cascade_parent_api_name":"field_459OP__c","is_single":false,"index_name":"s_44","is_index":true,"is_active":true,"create_time":1641958899622,"is_encrypted":false,"default_value":"","label":"省-x","field_num":95,"api_name":"field_WjKw7__c","_id":"61de4df340f43700013833b8","is_index_field":false,"config":{},"status":"new","help_text":""},"field_06oJ8__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","wheres":[],"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_1","is_index":true,"is_active":true,"create_time":*************,"is_encrypted":false,"target_api_name":"AccountObj","label":"客户","target_related_list_name":"target_related_list_cZk1o__c","field_num":75,"lookup_roles":["0_1_w","0_4_w"],"target_related_list_label":"HG-通用对象1","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_06oJ8__c","_id":"60b840e880773700011bf7e1","is_index_field":true,"status":"new","help_text":""},"field_XZ011__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":*************,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"人员多值","type":"employee","field_num":59,"wheres":[],"is_required":false,"api_name":"field_XZ011__c","define_type":"custom","_id":"604ae14297f95400010347ee","is_index_field":false,"is_single":false,"index_name":"a_6","status":"new","help_text":""},"field_R18Zl__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"long_text","default_to_zero":false,"is_required":false,"define_type":"custom","is_single":false,"index_name":"t_17","max_length":2000,"is_index":true,"is_active":true,"create_time":1641958899617,"min_length":0,"is_encrypted":false,"default_value":"","label":"多行文本-详情整行","field_num":90,"api_name":"field_R18Zl__c","_id":"61de4df340f43700013833b3","is_index_field":false,"status":"new","help_text":""},"field_oK6t6__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"used_in":"component","is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_12","max_length":300,"is_index":true,"is_active":true,"create_time":1616643723780,"is_encrypted":false,"default_value":"","label":"详细地址2","field_num":70,"api_name":"field_oK6t6__c","_id":"605c068b13598c00075f07cb","is_index_field":false,"status":"new","help_text":""},"owner_department":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"负责人主属部门","is_single":true,"index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1583823321320,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"5e6739d9db17cf0001e30773","is_index_field":false,"status":"new","help_text":""},"field_Ck2uv__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"custom","input_mode":"scan_only","is_single":false,"index_name":"t_11","max_length":100,"is_index":true,"is_active":true,"create_time":1596182711236,"is_encrypted":false,"default_value":"","label":"文本-仅扫码","field_num":44,"api_name":"field_Ck2uv__c","_id":"5f23d0b7b453c300011ba4e0","is_index_field":false,"status":"new","help_text":""},"field_XbMv3__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1583823321331,"is_encrypted":false,"auto_adapt_places":false,"remove_mask_roles":{},"pattern":"^\\\\w+([-+.]*\\\\w+)*@\\\\w+([-.]\\\\w+)*\\\\.\\\\w+([-.]\\\\w+)*$","is_unique":false,"description":"","default_value":"","label":"邮箱","type":"email","field_num":19,"is_required":false,"api_name":"field_XbMv3__c","define_type":"custom","_id":"5e6739d9db17cf0001e30778","is_index_field":false,"is_single":false,"is_show_mask":false,"index_name":"w_2","status":"new","help_text":""},"package":{"describe_api_name":"object_3sbgg__c","is_index":false,"is_active":true,"create_time":1583823321321,"pattern":"","description":"package","is_unique":false,"label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","status":"released","max_length":200},"field_ErXgM__c":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"","file_source":[],"type":"image","is_required":false,"define_type":"custom","is_single":false,"support_file_types":["jpg","gif","jpeg","png"],"index_name":"a_16","file_amount_limit":5,"is_index":true,"watermark":[{"type":"variable","value":"current_user"},{"type":"variable","value":"current_time"},{"type":"variable","value":"current_address"}],"is_active":true,"create_time":1616484102316,"is_encrypted":false,"label":"图片ocr-发票","is_watermark":false,"field_num":62,"is_ocr_recognition":true,"file_size_limit":20971520,"api_name":"field_ErXgM__c","is_need_cdn":false,"_id":"60599706fa5dc80001d8a555","is_index_field":false,"identify_type":"VatInvoice","status":"new","help_text":"单个图片不得超过20M"},"lock_status":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"锁定状态","type":"select_one","is_required":false,"options":[{"font_color":"#2a304d","label":"未锁定","value":"0"},{"font_color":"#2a304d","label":"锁定","value":"1"}],"define_type":"package","label_r":"锁定状态","is_single":false,"index_name":"s_34","is_index":true,"is_active":true,"create_time":1583823321309,"is_encrypted":false,"default_value":"0","label":"锁定状态","field_num":20,"is_need_convert":false,"api_name":"lock_status","_id":"5e6739d9db17cf0001e3077a","is_index_field":false,"config":{},"status":"new","help_text":""},"create_time":{"describe_api_name":"object_3sbgg__c","is_index":true,"create_time":1583823321321,"description":"create_time","is_unique":false,"label":"创建时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"crt_time","status":"released"},"field_4I7gZ__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":true,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_10","max_length":100,"is_index":true,"is_active":true,"create_time":1591007300240,"is_encrypted":false,"default_value":"$record_type$","label":"单行文本-默","field_num":39,"api_name":"field_4I7gZ__c","_id":"5ed4d844345e0400012c412a","is_index_field":false,"status":"new","help_text":""},"field_Y0BNn__c":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"","file_source":[],"type":"image","is_required":false,"define_type":"custom","is_single":false,"support_file_types":["jpg","gif","jpeg","png"],"index_name":"a_15","file_amount_limit":5,"is_index":true,"watermark":[{"type":"variable","value":"current_user"},{"type":"variable","value":"current_time"},{"type":"variable","value":"current_address"}],"is_active":true,"create_time":1583823321328,"is_encrypted":false,"default_value":"","label":"图片","is_watermark":false,"field_num":24,"is_ocr_recognition":false,"file_size_limit":20971520,"api_name":"field_Y0BNn__c","is_need_cdn":false,"_id":"5e6739d9db17cf0001e3077f","is_index_field":false,"identify_type":"","status":"new","help_text":"单个图片不得超过20M\\n可删除5张"},"field_y23Oj__c":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"","file_source":[],"type":"image","is_required":false,"define_type":"custom","is_single":false,"support_file_types":["jpg","gif","jpeg","png"],"index_name":"a_14","file_amount_limit":5,"is_index":true,"watermark":[{"type":"variable","value":"current_user"},{"type":"variable","value":"current_time"},{"type":"variable","value":"current_address"}],"is_active":true,"create_time":1616484102315,"is_encrypted":false,"label":"图片ocr-通用","is_watermark":false,"field_num":61,"is_ocr_recognition":true,"file_size_limit":20971520,"api_name":"field_y23Oj__c","is_need_cdn":false,"_id":"60599706fa5dc80001d8a554","is_index_field":false,"identify_type":"Accurate","status":"new","help_text":"单个图片不得超过20M"},"related_object_data":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1627976765265,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"关联业务数据","label":"关联业务数据","type":"what_list_data","is_abstract":true,"field_num":87,"used_in":"component","is_required":false,"api_name":"related_object_data","define_type":"package","_id":"61dd609726e0410001831575","is_index_field":false,"is_single":false,"index_name":"s_40","status":"released","help_text":"","max_length":10000},"created_by":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1583823321321,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released","description":""},"field_ve91m__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1616643723780,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"国家2","type":"country","field_num":65,"used_in":"component","is_required":false,"api_name":"field_ve91m__c","options":[],"define_type":"custom","_id":"605c068b13598c00075f07c6","is_index_field":false,"is_single":false,"config":{},"index_name":"s_13","status":"new","help_text":""},"field_eZF2k__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1596180727816,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"单选-父","type":"select_one","field_num":41,"is_required":false,"api_name":"field_eZF2k__c","options":[{"font_color":"#2a304d","child_options":[{"field_k1KOn__c":["option1","hYhUuSzGl"]}],"label":"父1","value":"option1"},{"font_color":"#2a304d","child_options":[{"field_k1KOn__c":["04fx2IP3g","bvv8GA2my"]}],"label":"父2","value":"N1Tzi81kP"},{"font_color":"#2a304d","child_options":[{"field_k1KOn__c":["other"]}],"label":"父3","value":"663Zn0kjw"},{"font_color":"#2a304d","not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","_id":"5f23c8f7c0bc250001d174e9","is_index_field":false,"is_single":false,"config":{},"index_name":"s_33","status":"new","help_text":""},"relevant_team":{"describe_api_name":"object_3sbgg__c","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","description":"Member Employee","define_type":"package","is_unique":false,"label":"Member Employee","is_single":true,"type":"employee","help_text":"Member Employee"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"description":"Member Role","define_type":"package","is_unique":false,"label":"Member Role","type":"select_one","help_text":"Member Role"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"description":"Member Permission Type","define_type":"package","is_unique":false,"label":"Member Permission Type","type":"select_one","help_text":"Member Permission Type"}},"is_index":true,"is_active":true,"create_time":1608536844710,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"5fe0530ccfdf3c00016b116c","is_index_field":false,"label_r":"相关团队","is_single":false,"index_name":"a_team","status":"new","help_text":"相关团队"},"field_38l60__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","type":"object_reference_many","wheres":[],"is_required":false,"define_type":"custom","is_single":false,"index_name":"a_20","is_index":true,"is_active":true,"create_time":1629187095723,"is_encrypted":false,"target_api_name":"object_p8Nnn__c","label":"zm-所有字段（多选）","target_related_list_name":"target_related_list_8d2ag__c","field_num":80,"target_related_list_label":"HG-通用对象1","action_on_target_delete":"set_null","api_name":"field_38l60__c","_id":"611b6c1721484b0001270cc3","is_index_field":false,"status":"new","help_text":""},"field_wxdmo__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1637839612881,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"function","label":"人员2","type":"employee","field_num":84,"wheres":[{"connector":"OR","filters":[{"value_type":9,"operator":"IN","field_values":["func_20J18__c"],"field_name":"id"}]}],"is_required":false,"api_name":"field_wxdmo__c","define_type":"custom","_id":"619f72fd61368100011a5a63","is_index_field":false,"is_single":true,"index_name":"a_24","status":"new","help_text":""},"field_8v4D2__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1622710181558,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","where_type":"field","label":"人员(多选)","type":"employee_many","field_num":76,"wheres":[],"is_required":false,"api_name":"field_8v4D2__c","define_type":"custom","_id":"60b897a5f8517b0001955212","is_index_field":false,"is_single":false,"index_name":"a_7","status":"new","help_text":""},"field_22JE1__c":{"describe_api_name":"object_3sbgg__c","return_type":"number","expression_type":"js","auto_adapt_places":false,"is_unique":false,"description":"","type":"formula","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_7","is_index":true,"expression":"$field_gb1gE__c$*$field_QBIz8__c$","is_active":true,"create_time":1629170866173,"is_encrypted":false,"label":"计算1","field_num":78,"api_name":"field_22JE1__c","_id":"611b2cb221484b000123fbd8","is_index_field":false,"status":"new","help_text":"金额*数字-步进器"},"data_own_department":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1583823321321,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"归属部门","type":"department","is_need_convert":false,"wheres":[],"is_required":false,"api_name":"data_own_department","define_type":"package","_id":"6247e1a2ce08c400014c8d71","is_index_field":false,"label_r":"归属部门","is_single":true,"index_name":"data_owner_dept_id","status":"released","help_text":""},"field_yutgr__c":{"describe_api_name":"object_3sbgg__c","is_index":false,"is_active":true,"create_time":1608523187354,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"外部人员-单选","type":"out_employee","field_num":51,"is_required":false,"api_name":"field_yutgr__c","define_type":"package","_id":"5fe01db3cfdf3c00016ab20b","is_index_field":false,"is_single":true,"index_name":"a_17","status":"new","help_text":""},"name":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"prefix":"{yyyy}-{mm}-{dd}-","auto_adapt_places":false,"pattern":"","is_unique":true,"description":"name","start_number":1,"type":"text","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","postfix":"","label_r":"主属性","is_single":false,"index_name":"name","max_length":100,"is_index":true,"auto_number_type":"normal","is_active":true,"create_time":1583823321444,"is_encrypted":false,"default_value":"","serial_number":3,"label":"主属性","condition":"DAY","api_name":"name","func_api_name":"","_id":"5e6739d9db17cf0001e30787","is_index_field":false,"status":"new","help_text":""},"_id":{"describe_api_name":"object_3sbgg__c","is_index":false,"is_active":true,"create_time":1583823321321,"pattern":"","description":"_id","is_unique":false,"label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","status":"released","max_length":200},"field_7Qtch__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"used_in":"component","is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_9","max_length":300,"is_index":true,"is_active":true,"create_time":*************,"is_encrypted":false,"default_value":"","label":"详细地址","field_num":32,"api_name":"field_7Qtch__c","_id":"5e6739d9db17cf0001e3078a","is_index_field":false,"status":"new","help_text":""},"related_api_names":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"关联业务对象","type":"select_many","used_in":"component","is_required":false,"options":[{"label":"客户","value":"AccountObj"},{"label":"市场活动","value":"MarketingEventObj"},{"label":"HG主对象(配置勿动)","value":"object_1W4fD__c"},{"label":"wkl-七星","value":"object_310kQ__c"},{"label":"hh数据权限测试","value":"object_aKL2K__c"},{"label":"wkl-自定义对象","value":"object_qi16N__c"},{"label":"HG-UI事件主","value":"object_VWt6Z__c"},{"label":"HG通用2","value":"object_Y2qEA__c"}],"define_type":"package","is_single":false,"index_name":"a_25","is_index":true,"is_active":true,"create_time":*************,"is_encrypted":false,"default_value":[],"label":"关联业务对象","field_num":86,"api_name":"related_api_names","is_dynamic":true,"_id":"61dd609726e0410001831574","is_index_field":false,"config":{},"status":"released","help_text":""},"field_r2slM__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":true,"auto_adapt_places":false,"is_unique":false,"description":"","type":"date_time","default_to_zero":false,"is_required":false,"define_type":"custom","is_single":false,"index_name":"l_6","is_index":true,"is_active":true,"create_time":1650019235275,"is_encrypted":false,"default_value":"DATETIMEVALUE(''+$field_rBj2I__c$+' '+$currentTime__g$)","label":"日期时间-计算","time_zone":"GMT+8","field_num":107,"api_name":"field_r2slM__c","date_format":"yyyy-MM-dd HH:mm","_id":"62594ba3f9d78c000175caf2","is_index_field":false,"status":"new","help_text":""},"field_Wj2J5__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"date_time","default_to_zero":false,"is_required":true,"define_type":"custom","is_single":false,"index_name":"l_5","is_index":true,"is_active":true,"create_time":1629184997000,"is_encrypted":false,"default_value":"","label":"日期时间","time_zone":"GMT+8","field_num":79,"api_name":"field_Wj2J5__c","date_format":"yyyy-MM-dd HH:mm","_id":"611b63e521484b000126b086","is_index_field":false,"status":"new","help_text":""},"sign_in_info__c":{"describe_api_name":"object_3sbgg__c","embedded_fields":{"system_risk__c":{"is_index":false,"is_active":true,"is_need_convert":false,"is_required":false,"api_name":"system_risk__c","pattern":"","define_type":"custom","is_unique":false,"label":"系统风险","type":"text","max_length":256,"status":"new"},"biz_type__c":{"is_index":false,"is_active":true,"is_need_convert":false,"is_required":false,"api_name":"biz_type__c","options":[{"label":"","value":"sign_in"},{"label":"","value":"sign_out"}],"define_type":"custom","is_unique":false,"label":"业务类型","type":"select_one","status":"new"},"location__c":{"is_index":false,"is_active":true,"is_need_convert":false,"is_required":false,"api_name":"location__c","define_type":"custom","is_unique":false,"label":"定位","type":"location","status":"new"},"sign_time__c":{"is_index":false,"is_active":true,"is_need_convert":false,"is_required":false,"api_name":"sign_time__c","define_type":"custom","is_unique":false,"date_format":"yyyy-MM-dd HH:mm:ss","label":"时间","time_zone":"GMT+8","type":"date_time","status":"new"},"status__c":{"is_index":false,"is_active":true,"is_need_convert":false,"is_required":false,"api_name":"status__c","options":[{"label":"已签到","value":"sign_in_complete"},{"label":"已签退","value":"sign_out_complete"}],"define_type":"custom","is_unique":false,"label":"状态","type":"select_one","status":"new"},"device_no__c":{"is_index":false,"is_active":true,"is_need_convert":false,"is_required":false,"api_name":"device_no__c","pattern":"","define_type":"custom","is_unique":false,"label":"签到设备号","type":"text","max_length":256,"status":"new"}},"is_index":false,"is_active":true,"create_time":1587456089648,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"签到信息","type":"embedded_object_list","used_in":"component","is_need_convert":false,"is_required":false,"api_name":"sign_in_info__c","define_type":"custom","_id":"5e9ea85996164800015e6071","is_index_field":false,"is_single":false,"index_name":"s_16","status":"new","help_text":""},"field_61okU__c":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"","file_source":[],"type":"image","is_required":false,"define_type":"custom","is_single":false,"support_file_types":["jpg","gif","jpeg","png"],"index_name":"a_13","file_amount_limit":5,"is_index":true,"watermark":[{"type":"variable","value":"current_user"},{"type":"variable","value":"current_time"},{"type":"variable","value":"current_address"}],"is_active":true,"create_time":1616484102318,"is_encrypted":false,"label":"图片ocr-户口本","is_watermark":false,"field_num":64,"is_ocr_recognition":true,"file_size_limit":20971520,"api_name":"field_61okU__c","is_need_cdn":false,"_id":"60599706fa5dc80001d8a557","is_index_field":false,"identify_type":"HouseHoldRegister","status":"new","help_text":"单个图片不得超过20M"},"field_98wh2__c":{"describe_api_name":"object_3sbgg__c","is_index":false,"is_active":true,"create_time":1641958899627,"is_encrypted":false,"auto_adapt_places":false,"is_support_town":false,"is_unique":false,"description":"","label":"地区定位","group_type":"area","type":"group","is_required":false,"api_name":"field_98wh2__c","define_type":"custom","_id":"61de4df340f43700013833bd","fields":{"area_country":"field_459OP__c","area_location":"field_iACg1__c","area_detail_address":"field_1Jht3__c","area_city":"field_2snD1__c","area_province":"field_WjKw7__c","area_district":"field_Ic1vW__c"},"is_index_field":false,"is_single":false,"index_name":"s_45","status":"new","help_text":""},"field_OM4bS__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","wheres":[],"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_8","is_index":true,"is_active":true,"create_time":1608622774689,"is_encrypted":false,"target_api_name":"object_3sbgg__c","label":"HG-通用对象1-自关联","target_related_list_name":"target_related_list_kJVu6__c","field_num":53,"target_related_list_label":"HG-通用对象1","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_OM4bS__c","_id":"5fe1a2b648741400016dbbd8","is_index_field":true,"status":"new","help_text":""},"field_rj9jA__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"custom","input_mode":"scan","is_single":false,"index_name":"t_8","max_length":100,"is_index":true,"is_active":true,"create_time":1583823321324,"is_encrypted":false,"default_value":"","label":"单行文本","field_num":3,"api_name":"field_rj9jA__c","_id":"5e6739d9db17cf0001e306f5","is_index_field":false,"status":"new","help_text":"更换负责人-前置函数（包含：前置函数）"},"field_1Jht3__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"used_in":"component","is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_18","max_length":300,"is_index":true,"is_active":true,"create_time":1641958899625,"is_encrypted":false,"default_value":"","label":"详细地址-x","field_num":98,"api_name":"field_1Jht3__c","_id":"61de4df340f43700013833bb","is_index_field":false,"status":"new","help_text":""},"lock_user":{"describe_api_name":"object_3sbgg__c","is_index":false,"is_active":true,"create_time":1608536844708,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"加锁人","where_type":"field","label":"加锁人","type":"employee","field_num":11,"is_need_convert":false,"wheres":[],"is_required":false,"api_name":"lock_user","define_type":"package","_id":"5fe0530ccfdf3c00016b116d","is_index_field":false,"label_r":"加锁人","is_single":true,"index_name":"a_5","status":"new","help_text":""},"field_W94o6__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","type":"object_reference_many","wheres":[],"is_required":false,"define_type":"custom","is_single":false,"index_name":"a_22","is_index":true,"is_active":true,"create_time":1637056770102,"is_encrypted":false,"target_api_name":"ContactObj","label":"联系人(多选)","target_related_list_name":"target_related_list_1r4Kf__c","field_num":82,"target_related_list_label":"HG-通用对象1(多)","action_on_target_delete":"set_null","api_name":"field_W94o6__c","_id":"61938102f27bb300017c8e1e","is_index_field":false,"status":"new","help_text":""},"field_bVw7G__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1583823321333,"is_encrypted":false,"auto_adapt_places":false,"pattern":"^(((http[s]?|ftp):\\\\/\\\\/|www\\\\.)[a-z0-9\\\\.\\\\-]+\\\\.([a-z]{2,4})|((http[s]?|ftp):\\\\/\\\\/)?(([01]?[\\\\d]{1,2})|(2[0-4][\\\\d])|(25[0-5]))(\\\\.(([01]?[\\\\d]{1,2})|(2[0-4][\\\\d])|(25[0-5]))){3})(:\\\\d+)?(\\\\/[a-z0-9\\\\$\\\\^\\\\*\\\\+\\\\?\\\\(\\\\)\\\\{\\\\}\\\\.\\\\-_~!@#%&:;\\\\/=<>]*)?","is_unique":false,"description":"","default_value":"","label":"网址","type":"url","field_num":12,"is_required":false,"api_name":"field_bVw7G__c","define_type":"custom","_id":"5e6739d9db17cf0001e3076f","is_index_field":false,"is_single":false,"index_name":"w_4","status":"new","help_text":""},"is_deleted":{"describe_api_name":"object_3sbgg__c","is_index":false,"create_time":1583823321321,"description":"is_deleted","is_unique":false,"default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","define_type":"system","index_name":"is_del","status":"released"},"partner_id":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"合作伙伴","where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","wheres":[],"is_required":false,"define_type":"package","input_mode":"","is_single":false,"index_name":"s_9","is_index":true,"is_active":true,"create_time":1620380218100,"is_encrypted":false,"target_api_name":"PartnerObj","label":"合作伙伴","target_related_list_name":"partner_object_3sbgg__c_list","field_num":73,"target_related_list_label":"HG-通用对象1","action_on_target_delete":"set_null","related_wheres":[],"api_name":"partner_id","_id":"60950a3a0f04930001920001","is_index_field":true,"status":"new","help_text":""},"field_Hby9g__c":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"","file_source":[],"type":"image","is_required":false,"define_type":"custom","is_single":false,"support_file_types":["jpg","gif","jpeg","png"],"index_name":"a_11","file_amount_limit":100,"is_index":true,"watermark":[{"type":"variable","value":"current_user"},{"type":"variable","value":"current_time"},{"type":"variable","value":"current_address"}],"is_active":true,"create_time":1589769061399,"is_encrypted":false,"label":"图片-100张","is_watermark":false,"field_num":36,"is_ocr_recognition":false,"file_size_limit":20971520,"api_name":"field_Hby9g__c","is_need_cdn":false,"_id":"5ec1f3654d1392000148eae8","is_index_field":false,"identify_type":"","status":"new","help_text":"单个图片不得超过20M\\n可上传100张"},"field_QBIz8__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_6","max_length":14,"is_index":true,"is_active":true,"create_time":1629109868274,"is_encrypted":false,"step_value":10,"display_style":"step","length":12,"default_value":"10","label":"数字-步进器","field_num":58,"api_name":"field_QBIz8__c","_id":"611a3e6c21484b00011cdab8","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"object_describe_api_name":{"describe_api_name":"object_3sbgg__c","is_index":false,"is_active":true,"create_time":1583823321321,"pattern":"","description":"object_describe_api_name","is_unique":false,"label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","status":"released","max_length":200},"field_api_name_field1__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"used_in":"component","is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_24","max_length":256,"is_index":true,"is_active":true,"create_time":1649214120511,"is_encrypted":false,"default_value":"","label":"what关联对象1","field_num":104,"api_name":"field_api_name_field1__c","_id":"624d02a85fc984000195420f","is_index_field":false,"status":"new","help_text":""},"out_owner":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1583823321321,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"config":{"display":1},"index_name":"o_owner","status":"released","description":""},"out_resources":{"describe_api_name":"object_3sbgg__c","is_index":false,"is_active":true,"create_time":1620380218119,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"外部来源","default_value":"","label":"外部来源","type":"select_one","field_num":74,"is_need_convert":false,"is_required":false,"api_name":"out_resources","options":[{"font_color":"#2a304d","label":"代理通","value":"partner"}],"define_type":"package","_id":"60950a3a0f04930001920002","is_index_field":false,"is_single":false,"config":{},"index_name":"s_28","status":"new","help_text":""},"field_1984l__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":1,"default_to_zero":true,"used_in":"component","is_required":false,"define_type":"custom","is_single":false,"index_name":"d_4","max_length":16,"is_index":true,"is_active":true,"create_time":1583823321344,"is_encrypted":false,"step_value":1,"display_style":"input","length":15,"default_value":"","label":"间隔时长","field_num":17,"api_name":"field_1984l__c","_id":"5e6739d9db17cf0001e30775","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"owner":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1583823321319,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","where_type":"field","label":"负责人","type":"employee","is_need_convert":false,"wheres":[],"is_required":true,"api_name":"owner","define_type":"package","_id":"5e6739d9db17cf0001e30776","is_index_field":false,"label_r":"负责人","is_single":true,"index_name":"owner","status":"new","help_text":""},"field_v5R9B__c":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"","type":"province","used_in":"component","is_required":false,"options":[],"define_type":"custom","cascade_parent_api_name":"field_tn1oR__c","is_single":false,"index_name":"s_22","is_index":true,"is_active":true,"create_time":1583823321352,"is_encrypted":false,"default_value":"","label":"省","field_num":21,"api_name":"field_v5R9B__c","_id":"5e6739d9db17cf0001e3077b","is_index_field":false,"config":{},"status":"new","help_text":""},"field_11fVl__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"ISN","field_values":[],"field_name":"field_nh6D8__c"}]}],"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_37","is_index":true,"is_active":true,"create_time":1628842680902,"is_encrypted":false,"target_api_name":"object_1W4fD__c","label":"hG主对象","target_related_list_name":"target_related_list_iH2ZK__c","field_num":50,"target_related_list_label":"HG-通用对象1(关联不为空)","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_11fVl__c","_id":"61162aba73d194000103b508","is_index_field":true,"status":"new","help_text":""},"field_D4Dia__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1641958899618,"is_encrypted":false,"auto_adapt_places":false,"remove_mask_roles":{},"pattern":"\\\\w+((-w+)|(\\\\.\\\\w+))*\\\\@[A-Za-z0-9]+((\\\\.|-)[A-Za-z0-9]+)*\\\\.[A-Za-z0-9]+","is_unique":false,"description":"","default_value":"","label":"邮箱-详情","type":"email","field_num":91,"is_required":false,"api_name":"field_D4Dia__c","define_type":"custom","_id":"61de4df340f43700013833b4","is_index_field":false,"is_single":false,"is_show_mask":false,"index_name":"w_6","status":"new","help_text":""},"last_modified_by":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1583823321321,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released","description":""},"mc_currency":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1608693415886,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"币种","type":"select_one","is_required":false,"api_name":"mc_currency","options":[{"label":"AFN - 阿富汗的阿富汗尼（新）","value":"AFN"},{"label":"CUP - 古巴比索","value":"CUP"},{"label":"EUR - Euro","value":"EUR"},{"not_usable":true,"label":"AED - UAE Dirham","value":"AED"},{"label":"ALL - Albanian Lek","value":"ALL"},{"label":"AUD - 澳元","value":"AUD"},{"label":"ANG - 荷属安地列斯盾","value":"ANG"},{"label":"AOA - 安哥拉宽扎","value":"AOA"},{"label":"AMD - 亚美尼亚打兰","value":"AMD"},{"label":"USD - U.S. Dollar","value":"USD"},{"label":"BAM - 自由兑换马克","value":"BAM"},{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"5fe2b6a71567f400010b4ebc","is_index_field":false,"label_r":"币种","is_single":false,"config":{},"index_name":"s_29","status":"new","help_text":""},"record_type":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1583823321321,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"record_type","default_value":"","label":"业务类型","type":"record_type","is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"font_color":"#2a304d","api_name":"default__c","description":"预设业务类型","label":"预设业务类型"},{"is_active":true,"font_color":"#2a304d","api_name":"record_HXwpt__c","label":"依赖关系+图片"},{"is_active":true,"font_color":"#2a304d","api_name":"record_nksaM__c","label":"扫码&掩码"},{"is_active":true,"font_color":"#2a304d","api_name":"record_Mekcg__c","label":"UI事件"},{"is_active":true,"font_color":"#2a304d","api_name":"record_vix1H__c","label":"布局规则测试"},{"is_active":true,"api_name":"record_ne436__c","label":"详情页展示"},{"is_active":true,"api_name":"record_aygGC__c","label":"what和whatlist"}],"define_type":"package","_id":"5e6739d9db17cf0001e30782","is_index_field":false,"label_r":"业务类型","is_single":false,"config":{},"index_name":"r_type","status":"released","help_text":""},"field_n4C16__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1608522906791,"is_encrypted":false,"auto_adapt_places":false,"pattern":"^(((http[s]?|ftp):\\\\/\\\\/|www\\\\.)[a-z0-9\\\\.\\\\-]+\\\\.([a-z]{2,4})|((http[s]?|ftp):\\\\/\\\\/)?(([01]?[\\\\d]{1,2})|(2[0-4][\\\\d])|(25[0-5]))(\\\\.(([01]?[\\\\d]{1,2})|(2[0-4][\\\\d])|(25[0-5]))){3})(:\\\\d+)?(\\\\/[a-z0-9\\\\$\\\\^\\\\*\\\\+\\\\?\\\\(\\\\)\\\\{\\\\}\\\\.\\\\-_~!@#%&:;\\\\/=<>]*)?","is_unique":false,"description":"","default_value":"","label":"网址1","type":"url","field_num":49,"is_required":false,"api_name":"field_n4C16__c","define_type":"custom","_id":"5fe01c9a3925e200010642b8","is_index_field":false,"is_single":false,"index_name":"w_3","status":"new","help_text":""},"field_em0wd__c":{"describe_api_name":"object_3sbgg__c","auto_adapt_places":false,"is_unique":false,"description":"","type":"city","used_in":"component","is_required":false,"options":[],"define_type":"custom","cascade_parent_api_name":"field_9n19v__c","is_single":false,"index_name":"s_10","is_index":true,"is_active":true,"create_time":1616643723780,"is_encrypted":false,"default_value":"","label":"市2","field_num":67,"api_name":"field_em0wd__c","_id":"605c068b13598c00075f07c8","is_index_field":false,"config":{},"status":"new","help_text":""},"field_H8f6P__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1583823321334,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","where_type":"field","label":"部门","type":"department","field_num":30,"wheres":[],"is_required":false,"api_name":"field_H8f6P__c","define_type":"custom","_id":"5e6739d9db17cf0001e30788","is_index_field":false,"is_single":true,"index_name":"a_1","status":"new","help_text":""},"mc_exchange_rate_version":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"汇率版本","is_single":false,"index_name":"t_7","max_length":256,"is_index":false,"is_active":true,"create_time":1608693415895,"is_encrypted":false,"default_value":"","label":"汇率版本","api_name":"mc_exchange_rate_version","_id":"5fe2b6a71567f400010b4ebf","is_index_field":false,"status":"new","help_text":""},"field_q7SHg__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"used_in":"component","is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_6","max_length":100,"is_index":true,"is_active":true,"create_time":1583823321347,"is_encrypted":false,"default_value":"","label":"收款方式","field_num":31,"api_name":"field_q7SHg__c","_id":"5e6739d9db17cf0001e30789","is_index_field":false,"status":"new","help_text":""},"field_2HX3a__c":{"describe_api_name":"object_3sbgg__c","is_index":true,"is_active":true,"create_time":1596180800915,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":[],"label":"多选-子","type":"select_many","field_num":43,"is_required":false,"api_name":"field_2HX3a__c","options":[{"font_color":"#2a304d","label":"多选1","value":"3tF7Ln2dh"},{"font_color":"#ff8000","label":"多选2","value":"CxKz7u2cO"},{"font_color":"#30c776","label":"多选3","value":"6hOov7R63"},{"font_color":"#936de3","label":"多选4","value":"87L102suX"},{"font_color":"#2a304d","label":"多选5","value":"1uQSxRO8c"},{"label":"a","value":"t368fTGab"},{"label":"b","value":"H5BYk0PfT"},{"label":"c","value":"5c108iX32"},{"label":"d","value":"6jKid1Ksb"},{"label":"e","value":"Co0s6r355"},{"label":"f","value":"OHqy737Uq"},{"label":"g","value":"wZz1plc4J"},{"label":"h","value":"mSb3CeTk9"},{"label":"I","value":"gGy885J24"},{"label":"J","value":"Ry130BnIZ"},{"label":"k","value":"82EAPl6N5"},{"label":"L","value":"rDmg11Q7E"},{"label":"M","value":"b77tE52Kp"},{"label":"N","value":"10jZCpsmA"},{"label":"O","value":"gyW5Mq4p7"},{"label":"P","value":"Hv29w91hA"},{"label":"Q","value":"1CK5Wlq4t"},{"label":"R","value":"3wG14Z718"},{"label":"S","value":"AaesE4e2P"},{"label":"T","value":"yN1zB67i9"},{"label":"U","value":"92j9w9p33"},{"label":"V","value":"k2e120oO7"},{"label":"W","value":"o10J43bct"},{"label":"X","value":"17fOE0ox4"},{"label":"Y","value":"1H7JgwJn8"},{"label":"ZZ","value":"4AqigXlVI"},{"font_color":"#2a304d","not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","_id":"5f23c941c0bc250001d1760f","is_index_field":false,"is_single":false,"config":{},"index_name":"a_19","status":"new","help_text":""},"field_3jmg1__c":{"describe_api_name":"object_3sbgg__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"long_text","default_to_zero":false,"is_required":false,"define_type":"custom","is_single":false,"index_name":"t_5","max_length":2000,"is_index":true,"is_active":true,"create_time":1616484102314,"min_length":0,"is_encrypted":false,"default_value":"","label":"识别结果","field_num":60,"api_name":"field_3jmg1__c","_id":"60599706fa5dc80001d8a553","is_index_field":false,"status":"new","help_text":""}},"release_version":"6.4","actions":{}}'''
    @Shared
    String udefObjDescribeJson = udefObjDescribeJson1 + udefObjDescribeJson2
    @Shared
    String layoutJson = "{\"_id\":\"5e6739d9db17cf0001e307a3\",\"api_name\":\"layout_EnUCd__c\",\"layout_type\":\"list\",\"display_name\":\"移动端默认列表页\",\"layout_description\":null,\"ref_object_api_name\":\"object_3sbgg__c\",\"is_default\":false,\"is_deleted\":false,\"package\":\"CRM\",\"version\":26,\"hidden_components\":[],\"layout_structure\":{\"layout\":[{\"components\":[[\"table_component\"]],\"columns\":[{\"width\":\"100%\"}]}]},\"buttons\":[{\"action_type\":\"default\",\"api_name\":\"Add_button_default\",\"action\":\"Add\",\"label\":\"新建\"},{\"action_type\":\"default\",\"api_name\":\"IntelligentForm_button_default\",\"action\":\"IntelligentForm\",\"label\":\"智能表单\"},{\"action_type\":\"default\",\"api_name\":\"DuplicateCheckObj_button_default\",\"action\":\"DuplicateCheckObj\",\"label\":\"查重工具\"},{\"tenant_id\":\"590064\",\"describe_api_name\":\"object_3sbgg__c\",\"is_active\":true,\"last_modified_time\":1608621838086,\"create_time\":1608621838086,\"action_type\":\"custom\",\"button_type\":\"redirect\",\"description\":\"\",\"param_form\":[],\"label\":\"AppAction列表通用\",\"last_modified_by\":\"1001\",\"version\":1,\"created_by\":\"1001\",\"jump_url\":\"\",\"is_deleted\":false,\"wheres\":[],\"api_name\":\"button_ysPe5__c\",\"lock_data_show_button\":false,\"action\":\"button_ysPe5__c\",\"define_type\":\"custom\",\"_id\":\"5fe19f0e48741400016d9511\",\"use_pages\":[\"list_normal\"],\"actions\":[\"5fe19f0e48741400016d9510\"],\"redirect_type\":\"uipaas\"},{\"tenant_id\":\"590064\",\"describe_api_name\":\"object_3sbgg__c\",\"is_active\":true,\"last_modified_time\":1608621207114,\"create_time\":1608621207114,\"action_type\":\"custom\",\"button_type\":\"redirect\",\"description\":\"\",\"param_form\":[],\"label\":\"WebAction列表通用\",\"last_modified_by\":\"1001\",\"version\":1,\"created_by\":\"1001\",\"jump_url\":\"\",\"is_deleted\":false,\"wheres\":[],\"api_name\":\"button_762EJ__c\",\"lock_data_show_button\":false,\"action\":\"button_762EJ__c\",\"define_type\":\"custom\",\"_id\":\"5fe19c9748741400016d8903\",\"use_pages\":[\"list_normal\"],\"actions\":[\"5fe19c9748741400016d8902\"],\"redirect_type\":\"uipaas\"},{\"tenant_id\":\"590064\",\"describe_api_name\":\"object_3sbgg__c\",\"is_active\":true,\"last_modified_time\":1608620842526,\"create_time\":1608620842526,\"action_type\":\"custom\",\"button_type\":\"redirect\",\"description\":\"\",\"param_form\":[],\"label\":\"OpenDialogAction列表通用\",\"last_modified_by\":\"1001\",\"version\":1,\"created_by\":\"1001\",\"jump_url\":\"\",\"is_deleted\":false,\"wheres\":[],\"api_name\":\"button_1eXf8__c\",\"lock_data_show_button\":false,\"action\":\"button_1eXf8__c\",\"define_type\":\"custom\",\"_id\":\"5fe19b2a87590d0001655883\",\"use_pages\":[\"list_normal\"],\"actions\":[\"5fe19b2a87590d0001655882\"],\"redirect_type\":\"uipaas\"},{\"tenant_id\":\"590064\",\"describe_api_name\":\"object_3sbgg__c\",\"is_active\":true,\"last_modified_time\":1608621304347,\"create_time\":1596455334246,\"action_type\":\"custom\",\"button_type\":\"redirect\",\"description\":\"\",\"param_form\":[],\"label\":\"AlertAction-列表通用\",\"last_modified_by\":\"1001\",\"version\":2,\"created_by\":\"1001\",\"jump_url\":\"\",\"is_deleted\":false,\"wheres\":[],\"api_name\":\"button_uCbga__c\",\"lock_data_show_button\":false,\"action\":\"button_uCbga__c\",\"define_type\":\"custom\",\"_id\":\"5f27f9a6c0bc250001f136f8\",\"use_pages\":[\"list_normal\"],\"actions\":[\"5fe19cf687590d0001656235\"],\"redirect_type\":\"uipaas\"}],\"last_modified_time\":1709779089822,\"create_time\":1583823321540,\"agent_type\":\"agent_type_mobile\",\"is_show_fieldname\":true,\"what_api_name\":null,\"default_component\":null,\"created_by\":\"1001\",\"last_modified_by\":\"1002\",\"tenant_id\":\"590064\",\"ui_event_ids\":[],\"hidden_buttons\":[],\"namespace\":null,\"enable_mobile_layout\":null,\"components\":[{\"field_section\":[],\"buttons\":[],\"api_name\":\"table_component\",\"related_list_name\":\"\",\"unDeletable\":true,\"header\":\"移动端摘要\",\"type\":\"table\",\"grayLimit\":1,\"display_mode\":\"loose\",\"ref_object_api_name\":\"object_3sbgg__c\",\"show_image_size\":\"medium\",\"new_layout\":{\"show_image\":\"\",\"row_sections\":[],\"show_image_size\":\"medium\"},\"is_show_tag\":true,\"is_new_layout\":false,\"include_fields\":[{\"api_name\":\"name\",\"field_name\":\"name\",\"is_show_label\":true,\"render_type\":\"text\"},{\"api_name\":\"related_api_names\",\"field_name\":\"related_api_names\",\"is_show_label\":true,\"render_type\":\"select_many\"},{\"api_name\":\"record_type\",\"field_name\":\"record_type\",\"is_show_label\":true,\"render_type\":\"record_type\"},{\"api_name\":\"field_l933i__c\",\"field_name\":\"field_l933i__c\",\"is_show_label\":true,\"render_type\":\"phone_number\"},{\"api_name\":\"field_3N5lk__c\",\"field_name\":\"field_3N5lk__c\",\"is_show_label\":true,\"render_type\":\"number\"},{\"api_name\":\"field_ve91m__c\",\"field_name\":\"field_ve91m__c\",\"is_show_label\":true,\"render_type\":\"country\"},{\"api_name\":\"field_459OP__c\",\"field_name\":\"field_459OP__c\",\"is_show_label\":true,\"render_type\":\"country\"}],\"show_image\":null}]}"


    def setup() {
        controllerContext.getUser() >> User.systemUser("74255")
        infraServiceFacade.getSpringBeanHolder() >> new SpringBeanHolder()
        listController = new StandardListController('serviceFacade': serviceFacade, 'infraServiceFacade': infraServiceFacade, "controllerContext": controllerContext,
        )
    }

    def "test init"() {
        given:
        StandardListController.Arg arg = new StandardListController.Arg()
        arg.setTemplateApiName('template_qep6N__c')
        arg.setIncludeButtonInfo(true)
        Whitebox.setInternalState(listController, "arg", arg)
        IObjectDescribe objectDescribe = new ObjectDescribeDocument(JSON.parseObject(describeJson)).toObjectDescribe()
        serviceFacade.findObject(*_) >> objectDescribe
        serviceFacade.findObjectWithoutCopy(*_) >> objectDescribe
        when:
        serviceFacade.findByDescribeApiNameAndExtendAttribute(*_) >> []
        listController.init()
        then:
        noExceptionThrown()
    }

    def "test doInit"() {
        given:
        StandardListController.Arg arg = new StandardListController.Arg()
        arg.setTemplateApiName('template_qep6N__c')
        Whitebox.setInternalState(listController, "arg", arg)
        IObjectDescribe objectDescribe = new ObjectDescribeDocument(JSON.parseObject(describeJson)).toObjectDescribe()
        serviceFacade.findObject(*_) >> objectDescribe
        serviceFacade.findObjectWithoutCopy(*_) >> objectDescribe
        serviceFacade.findByDescribeApiNameAndExtendAttribute(*_) >> []
        when:
        def requestContext = RequestContext.builder().user(User.systemUser("74255")).tenantId('74255').build()
        RequestContextManager.setContext(requestContext)
        listController.doInit()
        then:
        noExceptionThrown()
    }

    def "test buildHandlerArg"() {
        when:
        def buildHandlerArg = listController.buildHandlerArg(SimpleHandlerDescribe.builder().build())
        then:
        noExceptionThrown()
    }

    def "test doService"() {
        given:
        def requestContext = RequestContext.builder().user(User.systemUser("74255"))
                .tenantId('74255')
                .peerName("OpenAPI")
                .build()
        RequestContextManager.setContext(requestContext)

        StandardListController.Arg arg = new StandardListController.Arg()
        arg.setTemplateApiName('template_qep6N__c')
        arg.setIncludeInvalidData(true)
        arg.setIncludeButtonInfo(false)
        Whitebox.setInternalState(listController, "arg", arg)
        IObjectDescribe objectDescribe = new ObjectDescribeDocument(JSON.parseObject(describeJson)).toObjectDescribe()
        Whitebox.setInternalState(listController, "objectDescribe", ObjectDescribeExt.of(objectDescribe))
        serviceFacade.getSearchTemplateQueryWithIgnoreSceneFilter(*_) >> new SearchTemplateQuery()
        when:
        serviceFacade.findSearchTemplateByIdAndType(*_) >> new SearchTemplate([:])
        def queryResult = new QueryResult()
        ObjectData objectData = new ObjectData()
        queryResult.setData([objectData])
        serviceFacade.findBySearchQueryWithDeleted(*_) >> queryResult
        def layout = new Layout(JSON.parseObject(defaultMobileLayout).toJavaObject(Map.class))
        serviceFacade.findMobileListLayout(*_) >> [layout]
        serviceFacade.getUnauthorizedFields(*_) >> []
        def listLayout = new Layout(JSON.parseObject(defaultLayout).toJavaObject(Map.class))
        infraServiceFacade.findGdprCompliance(*_) >> [new GdprCompliance()]

        serviceFacade.getListLayoutWitchComponents(*_) >> listLayout
        def result = listController.doService(arg)
        then:
        result != null
    }

    def "test convertFieldForView"() {
        given:
        StandardListController.Arg arg = new StandardListController.Arg()
        arg.setTemplateApiName('template_qep6N__c')
        arg.setIncludeInvalidData(true)
        arg.setIncludeButtonInfo(false)
        IObjectDescribe objectDescribe = new ObjectDescribeDocument(JSON.parseObject(udefObjDescribeJson)).toObjectDescribe()
        Whitebox.setInternalState(listController, "arg", arg)
        def abLayout = new LayoutDocument(JSON.parseObject(layoutJson)).toLayout()
        Whitebox.setInternalState(listController, "layout", abLayout)
        Whitebox.setInternalState(listController, "objectDescribe", ObjectDescribeExt.of(objectDescribe))
        when:
        def view = listController.convertFieldForView(User.systemUser("74255"), objectDescribe.getFieldDescribes(), [new ObjectData()])
        then:
        noExceptionThrown()
    }
}
