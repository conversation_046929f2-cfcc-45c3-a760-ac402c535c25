package com.facishare.paas.appframework.core.model

import spock.lang.Specification

class ObjectDataDocumentTest extends Specification {
    def "test projectField"() {
        given:
        def fieldProjection = ['name', 'a', 'field_no_exist']
        def data = ObjectDataDocument.of(["name":"123", "a":1, "b":23.5])
        def dataList = [data]

        when:
        ObjectDataDocument.projectField(dataList, fieldProjection)

        then:
        dataList.get(0).containsKey("name")
        dataList.get(0).contains<PERSON>ey("a")
        !dataList.get(0).contains<PERSON>ey("b")

    }

    def "test projectField with empty fieldProjection"() {
        given:
        def fieldProjection = []
        def data = ObjectDataDocument.of(["name":"123", "a":1, "b":23.5])
        def dataList = [data]

        when:
        ObjectDataDocument.projectField(dataList, fieldProjection)

        then:
        dataList.get(0).contains<PERSON>ey("name")
        dataList.get(0).contains<PERSON><PERSON>("a")
        dataList.get(0).contains<PERSON><PERSON>("b")

    }
}
