package com.facishare.paas.appframework.core.predef.handler.add;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.handler.add.AddActionHandler;
import org.springframework.stereotype.Component;

/**
 * Created by zhaooju on 2023/9/5
 */
@Component
@HandlerProvider(name = "_testAddExceptionHandler")
public class TestAddExceptionHandler implements AddActionHandler {
    @Override
    public Result handle(HandlerContext context, Arg arg) {
        throw new ValidateException("_testAddExceptionHandler");
    }
}
