package com.facishare.paas.appframework.common.service

import com.facishare.paas.appframework.common.service.dto.FindTodoCount
import com.facishare.paas.appframework.common.service.dto.FindTodoList
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.RestUtils
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class MessageBoxServiceImplTest extends Specification {
    
    MessageBoxServiceImpl messageBoxService
    MessageBoxServiceProxy messageBoxServiceProxy = Mock(MessageBoxServiceProxy)
    
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    
    def setup() {
        messageBoxService = new MessageBoxServiceImpl()
        messageBoxService.messageBoxServiceProxy = messageBoxServiceProxy
    }
    
    def "测试findTodoCount方法 - 正常场景"() {
        given:
        def user = Mock(User)
        user.getTenantId() >> "12345"
        user.getUserId() >> "67890"
        
        def headers = ["X-FS-TENANT-ID": "12345", "X-FS-USER-ID": "67890"]
        
        def arg = Mock(FindTodoCount.Arg)
        def result = Mock(FindTodoCount.Result)
        
        result.isSuccess() >> true
        result.getTotal() >> 10
        result.getUnread() >> 5
        
        when:
        def actualResult = messageBoxService.findTodoCount(user, arg)
        
        then:
        1 * RestUtils.buildHeaders(user) >> headers
        1 * messageBoxServiceProxy.findTodoCount(headers, arg) >> result
        
        actualResult == result
        actualResult.success
        actualResult.total == 10
        actualResult.unread == 5
    }
    
    def "测试findTodoCount方法 - 失败场景"() {
        given:
        def user = Mock(User)
        def headers = ["X-FS-TENANT-ID": "12345", "X-FS-USER-ID": "67890"]
        
        def arg = Mock(FindTodoCount.Arg)
        def result = Mock(FindTodoCount.Result)
        
        result.isSuccess() >> false
        result.getErrorMsg() >> "查询失败"
        
        when:
        def actualResult = messageBoxService.findTodoCount(user, arg)
        
        then:
        1 * RestUtils.buildHeaders(user) >> headers
        1 * messageBoxServiceProxy.findTodoCount(headers, arg) >> result
        
        actualResult == result
        !actualResult.success
        actualResult.errorMsg == "查询失败"
    }
    
    def "测试findTodoCount方法 - 各种边界值"() {
        given:
        def user = Mock(User)
        def headers = ["X-FS-TENANT-ID": "12345", "X-FS-USER-ID": "67890"]
        
        def arg = Mock(FindTodoCount.Arg)
        def result = Mock(FindTodoCount.Result)
        
        result.isSuccess() >> true
        result.getTotal() >> total
        result.getUnread() >> unread
        
        when:
        def actualResult = messageBoxService.findTodoCount(user, arg)
        
        then:
        1 * RestUtils.buildHeaders(user) >> headers
        1 * messageBoxServiceProxy.findTodoCount(headers, arg) >> result
        
        actualResult == result
        actualResult.total == total
        actualResult.unread == unread
        
        where:
        total | unread
        0     | 0
        1     | 0
        100   | 100
        100   | 50
        10    | 5
        10    | 5
        10    | 5
        10    | 5
    }
    
    def "测试findTodoList方法 - 正常场景"() {
        given:
        def user = Mock(User)
        user.getTenantId() >> "12345"
        user.getUserId() >> "67890"
        
        def headers = ["X-FS-TENANT-ID": "12345", "X-FS-USER-ID": "67890"]
        
        def arg = Mock(FindTodoList.Arg)
        def result = Mock(FindTodoList.Result)
        
        def todoItem1 = [id: "1001", title: "待办1", createTime: System.currentTimeMillis(), read: false]
        def todoItem2 = [id: "1002", title: "待办2", createTime: System.currentTimeMillis(), read: true]
        def todoItems = [todoItem1, todoItem2]
        
        result.isSuccess() >> true
        result.getTotal() >> 2
        result.getTodoList() >> todoItems
        
        when:
        def actualResult = messageBoxService.findTodoList(user, arg)
        
        then:
        1 * RestUtils.buildHeaders(user) >> headers
        1 * messageBoxServiceProxy.findTodoList(headers, arg) >> result
        
        actualResult == result
        actualResult.success
        actualResult.total == 2
        actualResult.todoList.size() == 2
        actualResult.todoList[0].id == "1001"
        actualResult.todoList[0].title == "待办1"
        actualResult.todoList[0].read == false
        actualResult.todoList[1].id == "1002"
        actualResult.todoList[1].title == "待办2"
        actualResult.todoList[1].read == true
    }
    
    def "测试findTodoList方法 - 失败场景"() {
        given:
        def user = Mock(User)
        def headers = ["X-FS-TENANT-ID": "12345", "X-FS-USER-ID": "67890"]
        
        def arg = Mock(FindTodoList.Arg)
        def result = Mock(FindTodoList.Result)
        
        result.isSuccess() >> false
        result.getErrorMsg() >> "查询失败"
        
        when:
        def actualResult = messageBoxService.findTodoList(user, arg)
        
        then:
        1 * RestUtils.buildHeaders(user) >> headers
        1 * messageBoxServiceProxy.findTodoList(headers, arg) >> result
        
        actualResult == result
        !actualResult.success
        actualResult.errorMsg == "查询失败"
    }
    
    def "测试findTodoList方法 - 空列表场景"() {
        given:
        def user = Mock(User)
        def headers = ["X-FS-TENANT-ID": "12345", "X-FS-USER-ID": "67890"]
        
        def arg = Mock(FindTodoList.Arg)
        def result = Mock(FindTodoList.Result)
        
        result.isSuccess() >> true
        result.getTotal() >> 0
        result.getTodoList() >> []
        
        when:
        def actualResult = messageBoxService.findTodoList(user, arg)
        
        then:
        1 * RestUtils.buildHeaders(user) >> headers
        1 * messageBoxServiceProxy.findTodoList(headers, arg) >> result
        
        actualResult == result
        actualResult.success
        actualResult.total == 0
        actualResult.todoList.isEmpty()
    }
} 