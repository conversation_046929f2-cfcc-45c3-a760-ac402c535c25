package com.facishare.paas.appframework.flow.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.nio.charset.Charset;
import java.util.UUID;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2017/7/12.
 */
@Slf4j
@Component
public class RecordTypeProducer {
    private String group;
    private String nameSrv;
    private String topic;

    public static final String FLAG_ADD = "add";
    public static final String FLAG_EDIT = "edit";
    public static final String FLAG_DISABLE = "disable";
    public static final String FLAG_ENABLE = "enable";
    public static final String FLAG_DELETE = "delete";

    private DefaultMQProducer producer;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-crm-java-udobj-mq", iConfig -> {
            try {
                group = iConfig.get("RECORDTYPE-GROUP");
                nameSrv = iConfig.get("RECORDTYPE-NAMESERVER");
                if (Strings.isNullOrEmpty(nameSrv)) {
                    log.error("cannot find NAMESRVADDR in {}", iConfig.getName());
                }
                topic = iConfig.get("RECORDTYPE-TOPICS");

                if (null == producer || !nameSrv.equals(producer.getNamesrvAddr())) {
                    //地址变了
                    newProducer();
                }
            } catch (Exception e) {
                log.error("Error in getConfig in recordTypeSync MQ Producer", e);
            }
        });
    }

    private void newProducer() throws MQClientException {
        if (producer != null) {
            producer.shutdown();
        }
        producer = new DefaultMQProducer(group);
        producer.setNamesrvAddr(nameSrv);
        producer.setInstanceName(UUID.randomUUID().toString());
        producer.start();
        producer.setRetryTimesWhenSendFailed(0);
    }

    public void sendMessage(RecordTypeMessage recordTypeMessage) {
        if (null == recordTypeMessage) {
            log.warn("RecordTypeMessage is null, don't send");
            return;
        }

        try {
            String msgBody = JSON.toJSONString(recordTypeMessage);
            Message msg = new Message(topic,
                    recordTypeMessage.getFlag(),
                    "",
                    msgBody.getBytes(Charset.forName("utf-8")));

            producer.send(msg, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    if (sendResult == null) {
                        log.error("sendResult=null");
                    } else {
                        SendStatus status = sendResult.getSendStatus();
                        if (status.equals(SendStatus.SEND_OK)) {
                            log.debug("Send Change RecordType MQ Success, mqId:{}, status={}, messageBody:{}", sendResult.getMsgId(), status, msgBody);
                        } else {
                            log.error("Send Change RecordType MQ Success, But Status Not OK. mqId:{}, status={}, messageBody:{}", sendResult.getMsgId(), status, msgBody);
                        }
                    }
                }

                @Override
                public void onException(Throwable e) {
                    log.error("Exception in sending MQ for recordType change, messageBody:{}", msgBody, e);
                }
            });
        } catch (InterruptedException e) {
            log.error("Error in sending RecordType MQ message", e);
            Thread.currentThread().interrupt();
        } catch (RemotingException | MQClientException e) {
            log.error("Error in sending RecordType MQ message", e);
        }
    }

    @PreDestroy
    private void onDestroy() {
        if (producer != null) {
            producer.shutdown();
        }
    }

    @Data
    public static class RecordTypeMessage {
        @JSONField(name = "Flag")
        private String flag;
        @JSONField(name = "TenantID")
        private String tenantId;
        @JSONField(name = "ObjApiName")
        private String objApiName;
        @JSONField(name = "RecordApiName")
        private String recordApiName;
        @JSONField(name = "RecordLabel")
        private String recordLabel;

    }
}

