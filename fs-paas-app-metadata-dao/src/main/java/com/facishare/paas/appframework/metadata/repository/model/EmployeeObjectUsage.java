package com.facishare.paas.appframework.metadata.repository.model;

import com.facishare.paas.appframework.metadata.repository.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity(apiName = EmployeeObjectUsage.EmployeeObjectUsageObj)
public class EmployeeObjectUsage extends BaseEntity {

    public static final String EmployeeObjectUsageObj = "EmployeeObjectUsageObj";

    @EmployeeField(field = @ObjectField(apiName = "operator", label = "操作人"))
    private String userId;
    //对象名
    @TextField(field = @ObjectField(apiName = "operation_object", label = "操作对象"))
    private String apiName;
    //数据id
    @TextField(field = @ObjectField(apiName = "operation_data", label = "数据id"))
    private String dataId;
    //业务操作名称
    @SelectOneField(field = @ObjectField(apiName = "operation", label = "操作行为"))
    private String bizOperationName;
    //操作时间currentTimeMillis
    @DateTimeField(field = @ObjectField(apiName = "operation_time", label = "操作时间"))
    private Long operationTime;

    @DepartmentField(field = @ObjectField(apiName = "operation_dept", label = "部门"))
    private String deptId;

    @TextField(field = @ObjectField(apiName = "operation_label", label = "数据label"))
    private String displayName;
}
