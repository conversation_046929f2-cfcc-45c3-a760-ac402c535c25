package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GenerateByAI
 * 测试EmailFieldDataConverter类的邮箱字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class EmailFieldDataConverterTest {

  @Mock
  private IObjectDataProxyService dataService;

  @Mock
  private SessionContext sessionContext;

  private EmailFieldDataConverter converter;
  private IFieldDescribe fieldDescribe;
  private IObjectData objectData;

  @BeforeEach
  void setUp() {
    converter = new EmailFieldDataConverter();
    fieldDescribe = createEmailFieldDescribe("email_field");
    objectData = createObjectData("email_field", "<EMAIL>");
    
    // 设置转换器的属性
    converter.setFieldDescribe(fieldDescribe);
    converter.setObjectData(objectData);
    converter.setDataService(dataService);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法正常转换邮箱地址
   */
  @Test
  @DisplayName("测试convertFieldData - 正常邮箱转换")
  void testConvertFieldData_NormalEmailConversion() throws Exception {
    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("<EMAIL>", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理null值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null值")
  void testConvertFieldData_HandlesNullValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("email_field", null);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertNull(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理空字符串
   */
  @Test
  @DisplayName("测试convertFieldData - 处理空字符串")
  void testConvertFieldData_HandlesEmptyString() throws Exception {
    // 准备测试数据
    objectData = createObjectData("email_field", "");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * 提供不同格式的邮箱地址测试数据
   */
  private static Stream<Arguments> provideEmailValues() {
    return Stream.of(
        Arguments.of("<EMAIL>", "<EMAIL>"),
        Arguments.of("<EMAIL>", "<EMAIL>"),
        Arguments.of("<EMAIL>", "<EMAIL>"),
        Arguments.of("<EMAIL>", "<EMAIL>"),
        Arguments.of("chinese用户@domain.com", "chinese用户@domain.com"),
        Arguments.of("<EMAIL>", "<EMAIL>"),
        Arguments.of("simple@localhost", "simple@localhost"),
        Arguments.of("<EMAIL>", "<EMAIL>")
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理不同格式的邮箱地址
   */
  @ParameterizedTest
  @MethodSource("provideEmailValues")
  @DisplayName("测试convertFieldData - 不同格式的邮箱地址")
  void testConvertFieldData_DifferentEmailFormats(String inputEmail, String expectedResult) throws Exception {
    // 准备测试数据
    objectData = createObjectData("email_field", inputEmail);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals(expectedResult, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理包含特殊字符的邮箱
   */
  @Test
  @DisplayName("测试convertFieldData - 包含特殊字符的邮箱")
  void testConvertFieldData_EmailWithSpecialCharacters() throws Exception {
    // 准备测试数据
    String specialEmail = "<EMAIL>";
    objectData = createObjectData("email_field", specialEmail);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals(specialEmail, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理大小写混合的邮箱
   */
  @Test
  @DisplayName("测试convertFieldData - 大小写混合的邮箱")
  void testConvertFieldData_MixedCaseEmail() throws Exception {
    // 准备测试数据
    String mixedCaseEmail = "<EMAIL>";
    objectData = createObjectData("email_field", mixedCaseEmail);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 应该保持原始大小写
    assertEquals(mixedCaseEmail, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理字段不存在的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 字段不存在")
  void testConvertFieldData_FieldNotExists() throws Exception {
    // 准备测试数据 - 创建不包含目标字段的数据
    objectData = createObjectData("other_field", "<EMAIL>");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 字段不存在时应该返回null
    assertNull(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理非字符串类型的值
   */
  @Test
  @DisplayName("测试convertFieldData - 非字符串类型值")
  void testConvertFieldData_NonStringValue() throws Exception {
    // 准备测试数据 - 存储非字符串类型的值
    objectData = createObjectData("email_field", 12345);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - ObjectData.get(String, String.class)会进行类型转换
    assertEquals("12345", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理包含空格的邮箱
   */
  @Test
  @DisplayName("测试convertFieldData - 包含空格的邮箱")
  void testConvertFieldData_EmailWithSpaces() throws Exception {
    // 准备测试数据
    String emailWithSpaces = "  <EMAIL>  ";
    objectData = createObjectData("email_field", emailWithSpaces);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 应该保持原始值（包括空格）
    assertEquals(emailWithSpaces, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理多个邮箱地址（虽然不是标准格式）
   */
  @Test
  @DisplayName("测试convertFieldData - 多个邮箱地址")
  void testConvertFieldData_MultipleEmails() throws Exception {
    // 准备测试数据
    String multipleEmails = "<EMAIL>;<EMAIL>";
    objectData = createObjectData("email_field", multipleEmails);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 应该返回原始字符串
    assertEquals(multipleEmails, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试转换器的基本属性设置
   */
  @Test
  @DisplayName("测试转换器属性设置")
  void testConverterProperties() {
    // 验证属性设置
    assertEquals(fieldDescribe, converter.getFieldDescribe());
    assertEquals(objectData, converter.getObjectData());
    assertEquals(dataService, converter.getDataService());
  }

  /**
   * 创建测试用的邮箱字段描述
   */
  private IFieldDescribe createEmailFieldDescribe(String apiName) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试邮箱字段");
    fieldMap.put("type", IFieldType.EMAIL);
    return FieldDescribeFactory.newInstance(fieldMap);
  }

  /**
   * 创建测试用的对象数据
   */
  private IObjectData createObjectData(String fieldName, Object value) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldName, value);
    return new ObjectData(dataMap);
  }
}
