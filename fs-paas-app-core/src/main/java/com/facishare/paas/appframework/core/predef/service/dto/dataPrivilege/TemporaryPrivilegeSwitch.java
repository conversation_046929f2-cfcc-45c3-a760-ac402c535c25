package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * create by <PERSON><PERSON><PERSON> on 2018/10/31
 */
public interface TemporaryPrivilegeSwitch {
    boolean ENABLE = true;
    boolean DISABLE = false;
    boolean SUCCESS = true;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        private String apiName;
    }

    @Data
    @Builder
    class Result {
        private boolean success;
        private String message;
    }
}
