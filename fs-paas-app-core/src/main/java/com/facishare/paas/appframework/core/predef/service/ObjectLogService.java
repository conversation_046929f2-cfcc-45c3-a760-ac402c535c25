package com.facishare.paas.appframework.core.predef.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.model.enums.ObjectActionInfo;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.controller.StandardController;
import com.facishare.paas.appframework.core.predef.service.dto.log.PageInfo;
import com.facishare.paas.appframework.core.predef.service.dto.log.*;
import com.facishare.paas.appframework.flow.ApprovalFlowStatus;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.AuditLogConfig;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogModuleGroupEnum;
import com.facishare.paas.appframework.log.dto.*;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.expansion.DescribeExpansionRender;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.exception.ErrorCode;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.describe.PaymentFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.bizconf.arg.ConfigArg;
import com.fxiaoke.bizconf.bean.ValueType;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.objects.ObjectListConfig.AUDIT_LOG;
import static com.facishare.paas.common.util.UdobjConstants.LOCK_STATUS_VALUE_LOCK;

@Slf4j
@ServiceModule("modifyLog")
@Component
public class ObjectLogService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private OptionalFeaturesService optionalFeaturesService;

    @ServiceMethod("getLogInfoListForWeb")
    public GetLogInfoListForWeb.Result getLogInfoListForWeb(GetLogInfoListForWeb.Arg arg, ServiceContext context) {
        WebSearchResult logResult;
        if (Strings.isNullOrEmpty(arg.getDetailApiName())) {
            logResult = serviceFacade.webSearchModifyRecord(arg.getApiName(), arg.getObjectId(),
                    arg.getPageSize(), arg.getPageNumber(), context.getUser());
        } else {
            logResult = serviceFacade.webSearchModifyRecordForMaster(arg.getObjectId(), arg.getDetailApiName(),
                    arg.getPageSize(), arg.getPageNumber(), context.getUser());
        }
        GetLogInfoListForWeb.Result result = new GetLogInfoListForWeb.Result();
        result.setPageInfo(toPageInfo(logResult.getPageInfo()));
        List<LogRecord> logRecordList = new ArrayList<>(logResult.getModifyRecordList().size());
        logResult.getModifyRecordList().forEach(f -> logRecordList.add(modifyRecordToLogRecord(f)));
        result.setModifyRecordList(logRecordList);

        return result;
    }

    /**
     * 修改记录新结构web端接口
     */
    @ServiceMethod("getNewLogInfoListForWeb")
    public GetNewLogInfoListForWeb.Result getNewLogInfoListForWeb(GetNewLogInfoListForWeb.Arg arg, ServiceContext context) {
        ControllerContext controllerContext = new ControllerContext(context.getRequestContext(),
                arg.getApiName(), StandardController.NewLogInfoListForWeb.name());

        return serviceFacade.triggerController(controllerContext, arg, GetNewLogInfoListForWeb.Result.class);
    }

    @ServiceMethod("getLogInfoListForMob")
    public GetLogInfoListForMod.Result getLogInfoListForMob(GetLogInfoListForMod.Arg arg, ServiceContext context) {
        MobSearchResult logResult;
        if (Strings.isNullOrEmpty(arg.getDetailApiName())) {
            logResult = serviceFacade.mobSearchModifyRecord(arg.getApiName(), arg.getObjectId(), arg.getPageSize(),
                    arg.getOperationTime(), context.getUser());
        } else {
            logResult = serviceFacade.mobSearchModifyRecordForMaster(arg.getObjectId(), arg.getDetailApiName(),
                    arg.getPageSize(), arg.getOperationTime(), context.getUser());
        }
        GetLogInfoListForMod.Result result = new GetLogInfoListForMod.Result();
        result.setTotalCount(logResult.getTotalCount());
        List<LogRecord> logRecordList = new ArrayList<>(logResult.getModifyRecordList().size());

        //产品的修改记录,不能点击 特殊处理
        if ("ProductObj".equals(arg.getApiName())) {
            logResult.getModifyRecordList().forEach(f -> logRecordList.add(modifyRecordToLogRecordForProduct(f)));
        } else {
            logResult.getModifyRecordList().forEach(f -> logRecordList.add(modifyRecordToLogRecord(f)));
        }

        result.setModifyRecordList(logRecordList);
        return result;
    }

    /**
     * 修改记录新结构终端接口
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("getNewLogInfoListForMob")
    public GetNewLogInfoListForMod.Result getNewLogInfoListForMob(GetNewLogInfoListForMod.Arg arg, ServiceContext context) {
        ControllerContext controllerContext = new ControllerContext(context.getRequestContext(),
                arg.getApiName(), StandardController.NewLogInfoListForMob.name());

        GetNewLogInfoListForMod.Result result = serviceFacade.triggerController(controllerContext, arg, GetNewLogInfoListForMod.Result.class);
        result.setObjectDescribeExtra(getFieldExtra(context.getUser(), arg.getApiName()));
        return result;
    }

    @ServiceMethod("getSnapShotForWeb")
    public GetSnapShotForWeb.Result getSnapShotForWeb(GetSnapShotForWeb.Arg arg, ServiceContext context) {
        ControllerContext controllerContext = new ControllerContext(context.getRequestContext(),
                arg.getApiName(), StandardController.SnapShotForWeb.name());
        GetSnapShotForWeb.Result result = serviceFacade.triggerController(controllerContext, arg, GetSnapShotForWeb.Result.class);
        result.setObjectDescribeExt(getFieldExtra(context.getUser(), arg.getApiName()));
        return result;
    }

    @ServiceMethod("getSnapShotForMob")
    public GetSnapShotForMod.Result getSnapShotForMob(GetSnapShotForMod.Arg arg, ServiceContext context) {
        ControllerContext controllerContext = new ControllerContext(context.getRequestContext(),
                arg.getApiName(), StandardController.SnapShotForMob.name());
        GetSnapShotForMod.Result result = serviceFacade.triggerController(controllerContext, arg, GetSnapShotForMod.Result.class);
        result.setObjectDescribeExtra(getFieldExtra(context.getUser(), arg.getApiName()));
        return result;
    }

    @ServiceMethod("getLogModuleGroup")
    public GetLogModuleGroup.Result getLogModuleGroup(GetLogModuleGroup.Arg arg, ServiceContext context) {
        GetLogModuleGroup.Result result = new GetLogModuleGroup.Result();
        List<LogModuleGroup> logModuleGroups = new ArrayList<>(2);
        logModuleGroups.add(getObjectModuleGroup(context));
        logModuleGroups.add(getManagerLogModuleGroup(context.getTenantId()));
        logModuleGroups.add(getManagerObjectLogModuleGroup());
        logModuleGroups.add(getOperationTypeGroup());
        result.setModuleGroupList(logModuleGroups);
        return result;
    }


    @ServiceMethod("getLogModuleList")
    public GetLogsByModule.Result getLogsByModule(GetLogsByModule.Arg arg, ServiceContext context) {
        GetLogByModuleResult logResult = serviceFacade.getLogByModule(arg.getModule(), arg.getPageSize(), arg.getPageNumber(), context.getUser());
        GetLogsByModule.Result result = new GetLogsByModule.Result();
        result.setPageInfo(toPageInfo(logResult.getPageInfo()));
        List<LogRecord> logRecordList = new ArrayList<>(logResult.getLogInfos().size());
        logResult.getLogInfos().forEach(f -> logRecordList.add(modifyRecordToLogRecord(f)));
        result.setLogInfos(logRecordList);
        return result;
    }

    @ServiceMethod("getManageLogList")
    public GetManageLogList.Result getManageLogList(GetManageLogList.Arg arg, ServiceContext context) {
        List<Filter> filters = queryInfoToFilter(arg.getQueryInfo().getConditions());
        GetManageLogListResult logResult = serviceFacade.getManageLogList(context.getUser(), arg.getQueryInfo().getFilterMainID(),
                arg.getPageSize(), arg.getPageNumber(), filters, arg.getQueryInfo().getSortField(), arg.getQueryInfo().getSortType());
        GetManageLogList.Result result = new GetManageLogList.Result();
        result.setPageInfo(toPageInfo(logResult.getPageInfo()));
        result.setMsgs(logInfoToLogMsg(logResult.getMsgs(), arg.getQueryInfo().getFilterMainID()));
        return result;
    }

    @ServiceMethod("getTenantLogInterval")
    public GetTenantLogInterval.Result getTenantLogInterval(GetTenantLogInterval.Arg arg, ServiceContext context) {
        GetTenantLogInterval.Result result = new GetTenantLogInterval.Result();
        String tenantQueryInterval = AuditLogConfig.getTenantQueryInterval(context.getTenantId(), arg.getLogType());
        if (StringUtils.isNotBlank(tenantQueryInterval)) {
            result.setQueryInterval(tenantQueryInterval);
        }
        result.setGrayCHRead(AuditLogConfig.isGrayAuditLogChRead(context.getTenantId()));
        return result;
    }

    @ServiceMethod("recoverSnap")
    public RecoverSnap.Result recoverSnap(RecoverSnap.Arg arg, ServiceContext context) {
        LogInfo logInfo = serviceFacade.getLogById(arg.getDescribeApiName(), arg.getLogId(), context.getUser());
        IObjectDescribe objectDescribe = serviceFacade.findObject(context.getTenantId(), arg.getDescribeApiName());

        Map<String, Object> snapShot = logInfo.getSnapshot().getSnapshot();

        if (!hasCanRecoverPrivilege(objectDescribe, ObjectDataExt.of(snapShot), context)) {
            throw new ValidateException(ErrorCode.FS_PAAS_MDS_DATA_NO_PRILEGE.getMessage());
        }

        Map<String, IFieldDescribe> describeMap = ObjectDescribeExt.of(objectDescribe).getFieldDescribeMap();
        snapShot.remove("create_time");
        snapShot.remove("last_modified_time");
        for (Map.Entry<String, Object> entry : snapShot.entrySet()) {
            IFieldDescribe fieldDescribe = describeMap.get(entry.getKey());
            if (fieldDescribe == null)
                continue;
            if (fieldDescribe.getType().equals(IFieldType.TIME) || fieldDescribe.getType().equals(IFieldType.DATE)
                    || fieldDescribe.getType().equals(IFieldType.DATE_TIME)) {
                String valueStr = String.valueOf(entry.getValue());
                long timeStamp = 0;
                try {
                    timeStamp = Long.parseLong(valueStr);
                } catch (Exception e) {
                    log.warn("Error in convert to date or time", e);
                }
                entry.setValue(timeStamp);
            } else if (entry.getKey().equals(DBRecord.IS_DELETED)) {
                entry.setValue(String.valueOf(entry.getValue()).equals("true"));
            } else if (fieldDescribe.getType().equals(IFieldType.TRUE_OR_FALSE)) {
                entry.setValue(String.valueOf(entry.getValue()).equals("true"));
            }
        }

        //将snapshot中没有的field设置为空
        fillAbsentFieldNull(objectDescribe, snapShot);

        //处理支付组件，不能被恢复
        Optional<PaymentFieldDescribe> paymentFieldDescribe = ObjectDescribeExt.of(objectDescribe).getPaymentFieldDescribe();
        paymentFieldDescribe.ifPresent(a -> {
            List<IFieldDescribe> fieldList = null;
            try {
                fieldList = a.getFieldList(objectDescribe);
            } catch (MetadataServiceException e) {
                log.error("Never reach here", e);
            }
            if (CollectionUtils.notEmpty(fieldList)) {
                fieldList.forEach(b -> snapShot.remove(b.getApiName()));
            }
        });

        IObjectData objectData = ObjectDataExt.of(snapShot).getObjectData();
        String id = objectData.getId();
        Map document = ((DocumentBasedBean) objectData).getContainerDocument();
        if (document.get("relevant_team") != null) {
            document.remove("relevant_team");
        }
        //恢复记录忽略版本号，不更新生命状态、锁定状态
        document.remove(DBRecord.VERSION);
        document.remove(ObjectLockStatus.LOCK_STATUS_API_NAME);
        document.remove(ObjectLifeStatus.LIFE_STATUS_API_NAME);
        document.remove(ObjectLifeStatus.LIFE_STATUS_BEFORE_INVALID_API_NAME);

        objectData = serviceFacade.updateObjectData(context.getUser(), objectData);

        //update后 id 和 describeApiName 没了, 重新赋值
        objectData.setId(id);
        objectData.setDescribeApiName(arg.getDescribeApiName());
        serviceFacade.log(context.getUser(), EventType.ADD, ActionType.RecoryByModify, objectDescribe, objectData);
        return new RecoverSnap.Result();
    }

    //函数日志参数
    @ServiceMethod("getFunctionLogList")
    public GetFunctionLogList.Result getFunctionLogList(GetFunctionLogList.Arg arg, ServiceContext context) {

        SearchFunctionModel.Result searchResult = serviceFacade.getFunctionLog(context.getUser(),
                arg.getLogId(),
                arg.getFunctionApiName(),
                arg.getOperationTimeFrom(),
                arg.getSuccess(),
                arg.getOperationTimeTo(),
                arg.getSorts(),
                arg.getTraceId(),
                arg.getName(),
                arg.getPage(),
                arg.getPageSize());

        GetFunctionLogList.Result result = new GetFunctionLogList.Result();
        result.setTotalPage(searchResult.getTotalPage());
        result.setResults(searchResult.getResults());
        result.setPageSize(arg.getPageSize());
        result.setPage(arg.getPage());
        result.setTotalCount(searchResult.getTotalCount());
        return result;
    }

    @ServiceMethod("getFunctionLogListV2")
    public GetFunctionLogListV2.Result getFunctionLogListV2(GetFunctionLogListV2.Arg arg, ServiceContext context) {
        SearchFunctionLogV2.Result searchResult = serviceFacade.getFunctionLogV2(context.getUser(),
                arg.getLogId(),
                arg.getFunctionApiName(),
                arg.getTraceId(),
                arg.getOperationTimeFrom(),
                arg.getSuccess(),
                arg.getOperationTimeTo(),
                arg.getSorts(),
                arg.getName(),
                arg.getPageSize(),
                arg.getNextPage());
        GetFunctionLogListV2.Result result = new GetFunctionLogListV2.Result();
        result.setResults(searchResult.getResults());
        result.setHasMore(searchResult.getHasMore());
        return result;
    }

    @ServiceMethod("getFunctionLogDetail")
    public GetFunctionLogDetail.Result getFunctionLogDetailList(GetFunctionLogDetail.Arg arg, ServiceContext context) {
        SearchFunctionDetailModel.Result searchResult = serviceFacade.getFunctionLogDetail(context.getUser(), arg.getTraceId(), arg.getLogId());
        return buildGetFunctionLogDetailResult(searchResult);
    }

    @ServiceMethod("getNewFunctionLogDetail")
    public GetFunctionLogDetail.Result getNewFunctionLogDetail(GetFunctionLogDetail.Arg arg, ServiceContext context) {
        SearchFunctionDetailModel.Result searchResult = serviceFacade.getNewFunctionLogDetail(context.getUser(), arg.getTraceId(), arg.getLogId());
        return buildGetFunctionLogDetailResult(searchResult);
    }

    @NotNull
    private GetFunctionLogDetail.Result buildGetFunctionLogDetailResult(SearchFunctionDetailModel.Result searchResult) {
        GetFunctionLogDetail.Result result = new GetFunctionLogDetail.Result();
        result.setException(searchResult.getException());
        result.setParameters(searchResult.getParameters());
        result.setName(searchResult.getName());
        result.setReturnValue(searchResult.getReturnValue());
        result.setDurationTime(searchResult.getDurationTime());
        result.setSuccess(searchResult.getSuccess());
        result.setOperationTime(searchResult.getOperationTime());
        result.setFunctionApiName(searchResult.getFunctionApiName());
        result.setResults(searchResult.getResults());
        return result;
    }

    @ServiceMethod("searchFunctionEsLogDetail")
    public SearchFunctionEsLogDetail.Result searchFunctionEsLogDetail(SearchFunctionEsLogDetail.Arg arg, ServiceContext context) {
        return serviceFacade.searchFunctionEsLogDetail(context.getUser(), arg);
    }

    @ServiceMethod("searchFunctionEsLog")
    public SearchFunctionEsLog.Result searchFunctionEsLog(SearchFunctionEsLog.Arg arg, ServiceContext context) {
        return serviceFacade.searchFunctionEsLog(context.getUser(), arg);
    }

    @ServiceMethod("getLoginLogList")
    public LoginLog.Result getLoginLogList(LoginLog.Arg arg, ServiceContext context) {
        return serviceFacade.getLoginLog(context.getUser(), arg);
    }

    @ServiceMethod("getAuditLogList")
    public AuditLog.Result getAuditLogList(AuditLog.Arg arg, ServiceContext context) {
        return serviceFacade.getAuditLog(context.getUser(), arg);
    }

    @ServiceMethod("getAuditLogCount")
    public AuditLog.Result getAuditLogCount(AuditLog.Arg arg, ServiceContext context) {
        return serviceFacade.getAuditLogCount(context.getUser(), arg);
    }

    @ServiceMethod("deleteLog")
    public DeleteLog.Result deleteLog(DeleteLog.Arg arg, ServiceContext context) {
        ParallelUtils.createBackgroundTask().submit(() -> {
            if (CollectionUtils.notEmpty(arg.getObjectIds())) {
                serviceFacade.deleteLog(context.getUser(), arg);
            } else {
                ISearchTemplateQuery searchTemplateQuery = SearchTemplateQuery.fromJsonString(arg.getSearchQuery());
                searchTemplateQuery.setLimit(200);
                serviceFacade.queryDataAndHandle(context.getUser(), (SearchTemplateQuery) searchTemplateQuery, arg.getDescribeApiName(),
                        200, -1, true, queryResult -> {
                            if (CollectionUtils.empty(queryResult.getData())) {
                                return;
                            }
                            List<String> ids = queryResult.getData().stream().map(DBRecord::getId).collect(Collectors.toList());
                            arg.setObjectIds(ids);
                            serviceFacade.deleteLog(context.getUser(), arg);
                        });
            }
        }).run();
        return DeleteLog.Result.builder().build();
    }


    @ServiceMethod("getModifyLogList")
    public ModifyLog.Result getModifyLogList(ModifyLog.Arg arg, ServiceContext context) {
        return serviceFacade.getModifyLog(context.getUser(), arg);
    }


    @ServiceMethod("findLogAnalysis")
    public LogAnalysis.Result findLogAnalysis(LogAnalysis.Arg arg, ServiceContext context) {
        String loginConfig = serviceFacade.findTenantConfig(context.getUser(), LogAnalysis.LogAnalysisType.LOGINLOG_ANALYSIS.getType());
        String operationLogConfig = serviceFacade.findTenantConfig(context.getUser(), LogAnalysis.LogAnalysisType.AUDITLOG_ANALYSIS.getType());
        List<LogAnalysis.OperationLog> operationLogResult = JacksonUtils.fromJson(operationLogConfig, new TypeReference<List<LogAnalysis.OperationLog>>() {
        });
        return LogAnalysis.Result.builder()
                .loginLog(Boolean.parseBoolean(loginConfig))
                .operationLogResult(operationLogResult)
                .operationLog(CollectionUtils.notEmpty(operationLogResult))
                .build();
    }

    @ServiceMethod("findOperationLogRelationship")
    public LogAnalysis.Result findOperationLogRelationship(LogAnalysis.Arg arg, ServiceContext context) {
        LogAnalysis.Result result = LogAnalysis.Result.builder().build();
        List<IObjectDescribe> describeApiNames = serviceFacade.findDescribeList(context.getTenantId(), false, "", false, true, true, "");
        if (CollectionUtils.empty(describeApiNames)) {
            return result;
        }
        Map<String, List<String>> operationLogRelationship = AuditLogConfig.operation_log_relationship;
        if (CollectionUtils.empty(operationLogRelationship)) {
            return result;
        }
        Set<String> operationDescribeApiNames = operationLogRelationship.keySet();
        List<String> commonOperations = operationLogRelationship.get(AuditLogConfig.UDOBJ);
        List<LogAnalysis.OperationLog> operationLogs = Lists.newArrayList();
        for (IObjectDescribe describeApiName : describeApiNames) {
            List<String> operations = Lists.newArrayList(commonOperations);
            if (operationDescribeApiNames.contains(describeApiName.getApiName())) {
                operations.addAll(operationLogRelationship.get(describeApiName.getApiName()));
            }
            operationLogs.add(LogAnalysis.OperationLog.builder()
                    .describeApiName(describeApiName.getApiName())
                    .displayName(describeApiName.getDisplayName())
                    .operation(operations).build());
        }
        result.setOperationLogResult(operationLogs);
        return result;
    }

    @ServiceMethod("updateLogAnalysis")
    @Transactional
    public LogAnalysis.Result updateLogAnalysis(LogAnalysis.Arg arg, ServiceContext context) {
        //登录日志
        serviceFacade.upsertTenantConfig(context.getUser(), ObjectDescribeExt.LOG_ANALYSIS_IS_OPEN + Utils.EMPLOYEE_LOGIN_USAGE_API_NAME, String.valueOf(Boolean.TRUE.equals(arg.getLoginLog())), ConfigValueType.STRING);
        serviceFacade.upsertTenantConfig(context.getUser(), LogAnalysis.LogAnalysisType.LOGINLOG_ANALYSIS.getType(), String.valueOf(Boolean.TRUE.equals(arg.getLoginLog())), ConfigValueType.STRING);
        //对象行为分析日志
        if (Boolean.TRUE.equals(arg.getOperationLog())) {
            List<LogAnalysis.OperationLog> operationLogArgs = arg.getOperationLogArgs();
            String operationLogConfig = serviceFacade.findTenantConfig(context.getUser(), LogAnalysis.LogAnalysisType.AUDITLOG_ANALYSIS.getType());
            List<LogAnalysis.OperationLog> existOperationLogs = JacksonUtils.fromJson(operationLogConfig, new TypeReference<List<LogAnalysis.OperationLog>>() {
            });
            Set<String> describeApiNames = CollectionUtils.nullToEmpty(operationLogArgs).stream()
                    .map(LogAnalysis.OperationLog::getDescribeApiName).collect(Collectors.toSet());
            if (CollectionUtils.notEmpty(existOperationLogs)) {
                Set<String> removeApiNames = existOperationLogs.stream().map(LogAnalysis.OperationLog::getDescribeApiName)
                        .filter(x -> !describeApiNames.contains(x)).collect(Collectors.toSet());
                removeApiNames.forEach(apiName -> {
                    String removeKey = LogAnalysis.LogAnalysisType.AUDITLOG_ANALYSIS.getType() + "_" + apiName;
                    serviceFacade.deleteTenantConfig(context.getUser(), removeKey);
                });
            }
            if (CollectionUtils.notEmpty(operationLogArgs)) {
                serviceFacade.upsertTenantConfig(context.getUser(), ObjectDescribeExt.LOG_ANALYSIS_IS_OPEN + Utils.EMPLOYEE_OBJECT_USAGE_API_NAME, Boolean.TRUE.toString(), ConfigValueType.STRING);
                batchUpsertLogAnalysisTenantConfig(context.getUser(), operationLogArgs);
                updateEmployeeObjectUsageFieldOption(describeApiNames, context.getTenantId());
            } else {
                serviceFacade.upsertTenantConfig(context.getUser(), ObjectDescribeExt.LOG_ANALYSIS_IS_OPEN + Utils.EMPLOYEE_OBJECT_USAGE_API_NAME, Boolean.FALSE.toString(), ConfigValueType.STRING);
                serviceFacade.deleteTenantConfig(context.getUser(), LogAnalysis.LogAnalysisType.AUDITLOG_ANALYSIS.getType());
            }
        } else {
            serviceFacade.upsertTenantConfig(context.getUser(), ObjectDescribeExt.LOG_ANALYSIS_IS_OPEN + Utils.EMPLOYEE_OBJECT_USAGE_API_NAME, Boolean.FALSE.toString(), ConfigValueType.STRING);
            String operationLogConfig = serviceFacade.findTenantConfig(context.getUser(), LogAnalysis.LogAnalysisType.AUDITLOG_ANALYSIS.getType());
            if (StringUtils.isBlank(operationLogConfig)) {
                return LogAnalysis.Result.builder().build();
            }
            List<LogAnalysis.OperationLog> operationLogs = JacksonUtils.fromJson(operationLogConfig, new TypeReference<List<LogAnalysis.OperationLog>>() {
            });
            if (CollectionUtils.empty(operationLogs)) {
                return LogAnalysis.Result.builder().build();
            }
            operationLogs.forEach(operationLog -> {
                String key = LogAnalysis.LogAnalysisType.AUDITLOG_ANALYSIS.getType() + "_" + operationLog.getDescribeApiName();
                serviceFacade.deleteTenantConfig(context.getUser(), key);
            });
            serviceFacade.deleteTenantConfig(context.getUser(), LogAnalysis.LogAnalysisType.AUDITLOG_ANALYSIS.getType());
        }
        return LogAnalysis.Result.builder().build();
    }

    private void updateEmployeeObjectUsageFieldOption(Set<String> options, String tenantId) {
        if (CollectionUtils.empty(options)) {
            return;
        }
        IObjectDescribe describe = serviceFacade.findObject(tenantId, Utils.EMPLOYEE_OBJECT_USAGE_API_NAME);
        IFieldDescribe fieldDescribe = ObjectDescribeExt.of(describe).getFieldDescribe("operation_object");
        if (fieldDescribe instanceof SelectOne) {
            SelectOne selectOneFieldDescribe = (SelectOne) fieldDescribe;
            List<ISelectOption> selectOptions = selectOneFieldDescribe.getSelectOptions();
            List<String> existValues = selectOptions.stream().filter(Objects::nonNull)
                    .map(ISelectOption::getValue).collect(Collectors.toList());
            options.addAll(existValues);
            List<IObjectDescribe> optionDescribes = serviceFacade.findDescribeListWithoutFields(tenantId, options);
            Map<String, String> optionValueAndLabel = optionDescribes.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, IObjectDescribe::getDisplayName));
            options.removeIf(existValues::contains);
            for (ISelectOption selectOption : selectOptions) {
                selectOption.setLabel(optionValueAndLabel.getOrDefault(selectOption.getValue(), selectOption.getValue()));
            }
            for (String optionValue : options) {
                ISelectOption selectOption = new SelectOption();
                selectOption.setValue(optionValue);
                selectOption.setLabel(optionValueAndLabel.getOrDefault(optionValue, optionValue));
                selectOneFieldDescribe.addSelectOption(selectOption);
            }
            serviceFacade.updateFieldDescribe(describe, Lists.newArrayList(selectOneFieldDescribe));
        }
    }


    private void batchUpsertLogAnalysisTenantConfig(User user, List<LogAnalysis.OperationLog> operationLogArgs) {
        if (CollectionUtils.empty(operationLogArgs)) {
            return;
        }
        List<ConfigArg> configArgs = Lists.newArrayList();
        ConfigArg configTenantArg = ConfigArg.builder().key(LogAnalysis.LogAnalysisType.AUDITLOG_ANALYSIS.getType())
                .value(JacksonUtils.toJson(operationLogArgs))
                .valueType(ValueType.JSON).build();
        configArgs.add(configTenantArg);
        operationLogArgs.forEach(operationLog -> {
            String key = LogAnalysis.LogAnalysisType.AUDITLOG_ANALYSIS.getType() + "_" + operationLog.getDescribeApiName();
            ConfigArg configArg = ConfigArg.builder().key(key)
                    .value(JacksonUtils.toJson(operationLog.getOperation()))
                    .valueType(ValueType.JSON).build();
            configArgs.add(configArg);
        });
        serviceFacade.batchUpsertTenantConfig(user, configArgs);
    }

    private LogModuleGroup getOperationTypeGroup() {
        LogModuleGroup logModuleGroup = new LogModuleGroup();
        logModuleGroup.setGroupId(4);
        logModuleGroup.setGroupName(I18NExt.getOrDefault(I18NKey.LOG_OPERATION_TYPE, "操作行为"));// ignoreI18n
        List<LogModule> logModuleList = Lists.newArrayList();
        Map<String, String> actionTypes = ActionType.getActionTypes();
        Map<String, String> logActionType = AuditLogConfig.log_action_type;
        Map<String, Set<String>> actionNameAndIds = Maps.newHashMap();
        for (String value : logActionType.values()) {
            String name = I18NExt.text(value);
            actionTypes.put(value, name);
        }
        actionTypes.forEach((id, name) -> actionNameAndIds.computeIfAbsent(name, x -> Sets.newHashSet()).add(id));
        actionNameAndIds.forEach((name, ids) -> {
            LogModule logModule = LogModule.builder().idList(ids).name(name).build();
            logModuleList.add(logModule);
        });
        logModuleGroup.setLogModuleList(logModuleList);
        return logModuleGroup;
    }

    private LogModuleGroup getManagerObjectLogModuleGroup() {
        LogModuleGroup logModuleGroup = new LogModuleGroup();
        logModuleGroup.setGroupId(3);
        logModuleGroup.setGroupName(I18NExt.getOrDefault(I18NKey.LOG_OPERATION_OBJECT, "操作对象"));// ignoreI18n
        List<LogModule> logModuleList = Lists.newArrayList();
        List<Map<String, String>> logOperationObject = AuditLogConfig.log_operation_object;
        for (Map<String, String> object : logOperationObject) {
            for (String value : object.values()) {
                String objectName = I18NExt.getOrDefault(value, value);
                logModuleList.add(LogModule.builder().id(value).name(objectName).isDefault(false).build());
            }
        }
        logModuleGroup.setLogModuleList(logModuleList);
        return logModuleGroup;
    }

    private boolean hasCanRecoverPrivilege(IObjectDescribe objectDescribe, ObjectDataExt dataExt, ServiceContext context) {
        //针对回款等迁移对象，修改记录中没有id，作此兼容处理
        if (Strings.isNullOrEmpty(dataExt.getId())) {
            return false;
        }
        if (Objects.isNull(objectDescribe)) {
            return false;
        }
        //商品&产品&规格&规格值不允许下发恢复按钮
        if (Utils.PRODUCT_API_NAME.equals(objectDescribe.getApiName()) || Utils.SPU_API_NAME.equals(objectDescribe.getApiName()) || Utils.SPECIFICATION_API_NAME.equals(objectDescribe.getApiName()) || Utils.SPECIFICATION_VALUE_API_NAME.equals(objectDescribe.getApiName())) {
            return false;
        }

        //已作废数据的修改记录不展示恢复按钮
        IObjectData sourceData = serviceFacade.findObjectDataIncludeDeleted(context.getUser(), dataExt.getId(), objectDescribe.getApiName());
        if (ObjectDataExt.of(sourceData).isInvalid()) {
            return false;
        }

        //已锁定数据的修改记录不展示恢复按钮
        if (Objects.equals(String.valueOf(sourceData.get(UdobjConstants.LOCK_STATUS_API_NAME)), LOCK_STATUS_VALUE_LOCK)) {
            return false;
        }

        //校验功能权限
        List<String> funcPrivilegeCodes = Collections.singletonList(ObjectAction.MODIFYLOG_RECOVER.getActionCode());
        Map<String, Boolean> privileges = serviceFacade.funPrivilegeCheck(context.getUser(), objectDescribe.getApiName(), funcPrivilegeCodes);
        if (!privileges.getOrDefault(ObjectAction.MODIFYLOG_RECOVER.getActionCode(), false)) {
            return false;
        }

        //校验数据权限
        List<String> dataIds = Lists.newArrayList(dataExt.getId());
        Map<String, Permissions> dataPrivilege = serviceFacade.checkDataPrivilege(context.getUser(), dataIds, objectDescribe);
        if (dataPrivilege.get(dataExt.getId()) != Permissions.READ_WRITE) {
            return false;
        }

        //校验编辑审批
        Map<String, Tuple<String, ApprovalFlowStatus>> flowStatusMapForDataId = serviceFacade.batchGetApprovalTriggerType(context.getUser(), dataIds);
        if (CollectionUtils.notEmpty(flowStatusMapForDataId)) {
            for (Map.Entry<String, Tuple<String, ApprovalFlowStatus>> flowStatusForDataId : flowStatusMapForDataId.entrySet()) {
                String id = flowStatusForDataId.getKey();
                Tuple<String, ApprovalFlowStatus> status = flowStatusMapForDataId.get(id);
                if (status.getKey().equals("Update") && status.getValue().equals(ApprovalFlowStatus.IN_PROGRESS)) {
                    return false;
                }
            }
        }

        return true;
    }

    private void fillAbsentFieldNull(IObjectDescribe objectDescribe, Map<String, Object> snapShot) {
        List<IFieldDescribe> fieldDescribes;
        fieldDescribes = objectDescribe.getFieldDescribes();
        if (CollectionUtils.empty(fieldDescribes)) {
            return;
        }
        for (IFieldDescribe field : fieldDescribes) {
            if (Objects.equals(IFieldDescribe.DEFINE_TYPE_SYSTEM, field.getDefineType())) {
                continue;
            }
            if (snapShot.containsKey(field.getApiName())) {
                continue;
            }
            snapShot.put(field.getApiName(), null);
        }
    }

    private LogModuleGroup getObjectModuleGroup(ServiceContext context) {
        LogModuleGroup logModuleGroup = new LogModuleGroup();
        logModuleGroup.setGroupId(1);
        logModuleGroup.setGroupName(I18N.text(I18NKey.OBJECT));
        List<IObjectDescribe> objectDescribes = serviceFacade.findObjectsByTenantId(context.getTenantId(), false, true, false, true, AUDIT_LOG);
        if (AppFrameworkConfig.isOptionalFeaturesSupport(context.getTenantId())) {
            Map<String, OptionalFeaturesSwitchDTO> optionalFeaturesSwitchMap = optionalFeaturesService.batchQueryOptionalFeaturesSwitch(context.getTenantId(), objectDescribes);
            objectDescribes.removeIf(obj -> Objects.nonNull(optionalFeaturesSwitchMap.get(obj.getApiName()))
                    && !optionalFeaturesSwitchMap.get(obj.getApiName()).getIsModifyRecordEnabled());
        }
        List<LogModule> logModuleList = objectDescribes.stream()
                .map(describe -> LogModule.builder()
                        .id(describe.getApiName())
                        .name(describe.getDisplayName())
                        .build())
                .collect(Collectors.toList());
        //设置CRM日志默认显示对象
        if (CollectionUtils.notEmpty(logModuleList) && !logModuleList.isEmpty()) {
            logModuleList.get(0).setDefault(true);
        }
        logModuleGroup.setLogModuleList(logModuleList);
        return logModuleGroup;
    }


    private LogModuleGroup getManagerLogModuleGroup(String tenantId) {
        String version = serviceFacade.getVersion(tenantId);
        LogModuleGroup logModuleGroup = new LogModuleGroup();
        logModuleGroup.setGroupId(2);
        logModuleGroup.setGroupName(I18N.text(I18NKey.MANAGEMENT));
        List<LogModule> logModuleList = LogModuleGroup.getLogModuleGroupEnumsByVersion(version).stream()
                .map(x -> LogModule.builder().id(x.getCode()).name(x.getName()).isDefault(false).build())
                .collect(Collectors.toList());
        filterModuleByLicense(tenantId, logModuleList);
        Map<String, String> moduleGroup = AuditLogConfig.log_module_group;
        for (String value : moduleGroup.values()) {
            String moduleName = I18NExt.getOrDefault(value, value);
            logModuleList.add(LogModule.builder().id(value).name(moduleName).isDefault(false).build());
        }
        logModuleGroup.setLogModuleList(logModuleList);
        return logModuleGroup;
    }

    private void filterModuleByLicense(String tenantId, List<LogModule> logModuleList) {
        // 没有开通商机对象的企业，在 CRM 日志中不下发 销售流程
        Set<String> objectApiNames = serviceFacade.queryAvailableObject(tenantId);
        if (!objectApiNames.contains(Utils.OPPORTUNITY_API_NAME)) {
            logModuleList.removeIf(x -> LogModuleGroupEnum.SALE_FLOW_SETTING.getCode().equals(x.getId()));
        }
    }

    private List<Filter> queryInfoToFilter(List<GetManageLogList.FilterConditionInfo> infos) {
        if (infos == null) {
            return Lists.newArrayList();
        }
        List<Filter> filters = new ArrayList<>(infos.size());
        for (GetManageLogList.FilterConditionInfo info : infos) {
            Filter filter = new Filter();
            filter.setFieldName(info.getFieldName());
            filter.setComparison(info.getComparison());
            filter.setFilterValue(info.getFilterValue());
            filters.add(filter);
        }
        return filters;
    }

    private PageInfo toPageInfo(com.facishare.paas.appframework.log.dto.PageInfo info) {
        if (Objects.isNull(info)) {
            return null;
        }
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageCount(info.getPageCount());
        pageInfo.setPageNumber(info.getPageNumber());
        pageInfo.setPageSize(info.getPageSize());
        pageInfo.setTotalCount(info.getTotalCount());
        return pageInfo;
    }

    private List<GetManageLogList.LogMsg> logInfoToLogMsg(List<LogInfo> logInfos, String objectAPIName) {
        if (CollectionUtils.empty(logInfos)) {
            return Lists.newArrayList();
        }
        List<GetManageLogList.LogMsg> logMsgs = new ArrayList<>(logInfos.size());
        for (LogInfo logInfo : logInfos) {
            GetManageLogList.LogMsg logMsg = new GetManageLogList.LogMsg();
            LogInfo.ObjectSnapshot snapshot = logInfo.getSnapshot();
            List<GetManageLogList.TextMsg> msgs = Lists.newArrayList();
            if (snapshot != null) {
                List<LogInfo.LintMessage> lintMessages = snapshot.getTextMsg();
                for (LogInfo.LintMessage lintMessage : lintMessages) {
                    GetManageLogList.TextMsg msg = new GetManageLogList.TextMsg();
                    msg.setDataID(lintMessage.getDataID());
                    msg.setObjectApiName(objectAPIName);
                    msg.setText(lintMessage.convertToI18nText());
                    msg.setType(lintMessage.getType());
                    msg.setObjectType(lintMessage.getObjectType());
                    msgs.add(msg);
                }
            }
            logMsg.setMsgList(msgs);
            logMsg.setTextMessage(logInfo.convertToI18nTextMessage());
            logMsg.setOperType(logInfo.getBizOperationName());
            logMsg.setOperName(ActionType.getNameById(logInfo.getBizOperationName()));
            logMsg.setOperTime(logInfo.getOperationTime());
            logMsg.setUserId(StringUtils.isBlank(logInfo.getUserId()) ? 0 : Integer.parseInt(logInfo.getUserId()));
            logMsg.setUserName(logInfo.getUserName());
            logMsg.setLogId(logInfo.getLogId());
            logMsgs.add(logMsg);
        }
        return logMsgs;
    }

    private String getObjectApiName(LogInfo.LintMessage lintMessage) {
        return Optional.ofNullable(lintMessage.getObjectApiName())
                .orElse(lintMessage.getObjectType() == 0 || ObjectAPINameMapping.NotSFAObject.equals(ObjectAPINameMapping.of(lintMessage.getObjectType()))
                        ? ""
                        : ObjectAPINameMapping.of(lintMessage.getObjectType()).getApiName());
    }

    private LogRecord modifyRecordToLogRecord(ModifyRecord modifyRecord) {
        return LogRecord.builder()
                .logID(modifyRecord.getLogID())
                .logMsg(modifyRecord.getLogMsg())
                .operationTime(modifyRecord.getOperationTime())
                .operationType(modifyRecord.getOperationType())
                .owner(modifyRecord.getOwner())
                .snapShotType(modifyRecord.getSnapShotType())
                .peerName(modifyRecord.getPeerName())
                .operationLabel(modifyRecord.getOperationLabel())
                .objectInfo(modifyRecord.getObjectInfo())
                .msgList(modifyRecord.getMsgList())
                .objectData(modifyRecord.getObjectData())
                .peerReason(modifyRecord.getPeerReason())
                .detailInfos(modifyRecord.getDetailInfos())
                .masterLogId(modifyRecord.getMasterLogId())
                .dataName(modifyRecord.getDataName())
                .approvalFlowInfo(modifyRecord.getApprovalFlowInfo())
                .build();
    }

    private LogRecord modifyRecordToLogRecordForProduct(ModifyRecord modifyRecord) {
        return LogRecord.builder()
                .logID(modifyRecord.getLogID())
                .logMsg(modifyRecord.getLogMsg())
                .operationTime(modifyRecord.getOperationTime())
                .operationType(modifyRecord.getOperationType())
                .owner(modifyRecord.getOwner())
                .snapShotType(ModifyRecord.SNAP_TYPE_NOT_SHOW)
                .build();
    }

    private Map<String, Object> getFieldExtra(User user, String apiName) {
        DescribeExtra describeExtra = serviceFacade.findDescribeExtraByRenderType(user, serviceFacade.findObject(user.getTenantId(), apiName),
                Collections.emptyList(), DescribeExpansionRender.RenderType.Detail, true);
        return ObjectDescribeDocument.of(describeExtra);
    }

    /**
     * 批量查询ObjectAction枚举信息
     * @param arg 包含actionCodes的查询参数
     * @param context 服务上下文
     * @return ObjectAction枚举信息列表
     */
    @ServiceMethod("batchQueryObjectActions")
    public BatchQueryObjectActions.Result batchQueryObjectActions(BatchQueryObjectActions.Arg arg, ServiceContext context) {
        if (CollectionUtils.empty(arg.getActionCodes())) {
            return new BatchQueryObjectActions.Result(Lists.newArrayList());
        }

        List<ObjectActionInfo> actionInfos = Lists.newArrayList();
        
        // 遍历请求的actionCodes，从ObjectAction枚举中查找对应信息
        for (String actionCode : arg.getActionCodes()) {
            ObjectAction action = ObjectAction.of(actionCode);
            if (action != ObjectAction.UNKNOWN_ACTION) {
                ObjectActionInfo actionInfo = new ObjectActionInfo();
                actionInfo.setActionCode(action.getActionCode());
                actionInfo.setActionLabelKey(action.getI18NKey());
                actionInfo.setDefaultActionLabel(action.getDefaultActionLabel());
                actionInfo.setButtonApiName(action.getButtonApiName());
                actionInfo.setActionLabel(action.getActionLabel());
                actionInfos.add(actionInfo);
            }
        }

        return new BatchQueryObjectActions.Result(actionInfos);
    }
}
