package com.facishare.paas.appframework.core.predef.service.dto.search;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface FindSearchObjectList {
    @Data
    class Arg {
        /**
         * 用于筛选开关为"开"的对象的开关key，例如：is_global_search_enabled、is_follow_up_dynamic_enabled等
         * 如果为空，则默认使用is_global_search_enabled
         * 系统会保留该开关为"开"的对象，过滤掉开关为"关"的对象
         */
        private String enabledSwitchKey;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        @JSONField(name = "M1")
        private List<ObjectDescribeDocument> objectDescribeList;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ResultV3 {
        private List<String> describeApiNameList;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ResultV2 {
        private List<ObjectInfo> objectDescribeList;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ObjectInfo{
        String displayName;
        String describeApiName;
    }
}
