{"tenant_id": "74255", "package": "CRM", "is_active": true, "last_modified_time": 1692757420489, "create_time": 1692756159358, "description": "", "last_modified_by": "1000", "display_name": "zxf-从2", "created_by": "1000", "version": 2, "is_open_display_name": false, "index_version": 1, "icon_index": 0, "is_deleted": false, "api_name": "object_axkN2__c", "icon_path": "", "is_udef": true, "define_type": "custom", "short_name": "BAH", "_id": "64e568bf90e0870001c0fab4", "fields": {"field_20oz0__c": {"describe_api_name": "object_axkN2__c", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "enable_clone": true, "define_type": "custom", "is_single": false, "index_name": "d_1", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1692756159274, "is_encrypted": false, "length": 12, "default_value": "", "label": "金额", "currency_unit": "￥", "field_num": 1, "api_name": "field_20oz0__c", "_id": "64e568bf90e0870001c0fa8f", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "tenant_id": {"describe_api_name": "object_axkN2__c", "is_index": false, "is_active": true, "create_time": 1692756159358, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "index_name": "ei", "max_length": 200, "status": "released"}, "lock_rule": {"describe_api_name": "object_axkN2__c", "is_index": false, "is_active": true, "create_time": 1692756159274, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定规则", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "field_num": 2, "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "_id": "64e568bf90e0870001c0fa91", "is_single": false, "label_r": "锁定规则", "is_index_field": false, "index_name": "s_1", "help_text": "", "status": "new"}, "data_own_organization": {"describe_api_name": "object_axkN2__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "department", "is_required": false, "wheres": [], "define_type": "package", "is_single": true, "label_r": "归属组织", "index_name": "a_1", "is_index": true, "is_active": true, "create_time": 1692756159274, "is_encrypted": false, "default_value": "", "label": "归属组织", "is_need_convert": false, "api_name": "data_own_organization", "_id": "64e568bf90e0870001c0fa92", "is_index_field": false, "help_text": "", "status": "released"}, "field_8Esi7__c": {"describe_api_name": "object_axkN2__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "enable_clone": true, "define_type": "custom", "is_single": false, "index_name": "t_1", "max_length": 2000, "is_index": true, "is_active": true, "create_time": 1692756159274, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "多行文本", "field_num": 5, "api_name": "field_8Esi7__c", "_id": "64e568bf90e0870001c0fa93", "is_index_field": false, "help_text": "", "status": "new"}, "field_M7eCe__c": {"describe_api_name": "object_axkN2__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "enable_clone": true, "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "t_2", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1692756159274, "is_encrypted": false, "default_value": "", "label": "单行文本", "field_num": 6, "api_name": "field_M7eCe__c", "_id": "64e568bf90e0870001c0fa94", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "lock_user": {"describe_api_name": "object_axkN2__c", "is_index": false, "is_active": true, "create_time": 1692756159274, "is_encrypted": false, "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "where_type": "field", "label": "加锁人", "type": "employee", "field_num": 8, "is_need_convert": false, "is_required": false, "wheres": [], "api_name": "lock_user", "define_type": "package", "_id": "64e568bf90e0870001c0fa95", "is_single": true, "label_r": "加锁人", "is_index_field": false, "index_name": "a_2", "help_text": "", "status": "new"}, "field_1oy6g__c": {"describe_api_name": "object_axkN2__c", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": [], "type": "image", "is_required": false, "define_type": "custom", "is_single": false, "index_name": "a_3", "support_file_types": ["jpg", "gif", "jpeg", "png"], "is_index": true, "file_amount_limit": 9, "is_active": true, "watermark": [{"type": "variable", "value": "current_user"}, {"type": "variable", "value": "current_time"}, {"type": "variable", "value": "current_address"}], "create_time": 1692756159274, "is_encrypted": false, "label": "图片多个", "is_watermark": false, "field_num": 10, "file_size_limit": 20971520, "is_ocr_recognition": false, "api_name": "field_1oy6g__c", "is_need_cdn": false, "_id": "64e568bf90e0870001c0fa96", "is_index_field": false, "identify_type": "", "help_text": "单个图片不得超过20M", "status": "new"}, "mc_exchange_rate": {"describe_api_name": "object_axkN2__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "number", "decimal_places": 6, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "label_r": "汇率", "index_name": "d_2", "max_length": 16, "is_index": true, "is_active": true, "create_time": 1692756159341, "is_encrypted": false, "length": 10, "label": "汇率", "field_num": 11, "api_name": "mc_exchange_rate", "_id": "64e568bf90e0870001c0fa97", "is_index_field": false, "round_mode": 4, "help_text": "", "status": "new"}, "is_deleted": {"describe_api_name": "object_axkN2__c", "is_index": false, "create_time": 1692756159358, "is_unique": false, "description": "is_deleted", "default_value": false, "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "index_name": "is_del", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "object_axkN2__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "label_r": "作废前生命状态", "index_name": "t_3", "max_length": 256, "is_index": false, "is_active": true, "create_time": 1692756159274, "is_encrypted": false, "default_value": "", "label": "作废前生命状态", "field_num": 12, "is_need_convert": false, "api_name": "life_status_before_invalid", "_id": "64e568bf90e0870001c0fa99", "is_index_field": false, "help_text": "", "status": "new"}, "object_describe_api_name": {"describe_api_name": "object_axkN2__c", "is_index": false, "is_active": true, "create_time": 1692756159358, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "index_name": "api_name", "max_length": 200, "status": "released"}, "owner_department": {"describe_api_name": "object_axkN2__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "label_r": "负责人主属部门", "index_name": "owner_dept", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1692756159274, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "_id": "64e568bf90e0870001c0fa9b", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "out_owner": {"describe_api_name": "object_axkN2__c", "is_index": true, "is_active": true, "create_time": 1692756159358, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "index_name": "o_owner", "config": {"display": 1}, "status": "released"}, "mc_functional_currency": {"describe_api_name": "object_axkN2__c", "is_index": false, "is_active": true, "create_time": 1692756159342, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "本位币", "type": "select_one", "field_num": 16, "is_required": false, "api_name": "mc_functional_currency", "options": [{"label": "CNY - China Yuan", "value": "CNY"}], "define_type": "package", "_id": "64e568bf90e0870001c0fa9d", "is_single": false, "label_r": "本位币", "is_index_field": false, "index_name": "s_6", "config": {}, "help_text": "", "status": "new"}, "owner": {"describe_api_name": "object_axkN2__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "employee", "is_required": true, "wheres": [], "define_type": "package", "is_single": true, "label_r": "负责人", "index_name": "owner", "is_index": true, "is_active": true, "create_time": 1692756159274, "is_encrypted": false, "default_value": "", "label": "负责人", "is_need_convert": false, "api_name": "owner", "_id": "64e568bf90e0870001c0fa9e", "is_index_field": false, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "object_axkN2__c", "default_is_expression": false, "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "label_r": "锁定状态", "index_name": "s_2", "is_index": true, "is_active": true, "create_time": 1692756159274, "is_encrypted": false, "default_value": "0", "label": "锁定状态", "field_num": 3, "is_need_convert": false, "api_name": "lock_status", "_id": "64e568bf90e0870001c0fa9f", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "field_hkfv8__c": {"describe_api_name": "object_axkN2__c", "is_index": true, "is_active": true, "create_time": 1692756159274, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "default_value": false, "label": "从关闭标识", "type": "true_or_false", "field_num": 4, "is_required": false, "enable_clone": true, "api_name": "field_hkfv8__c", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "custom", "_id": "64e568bf90e0870001c0faa0", "is_single": false, "is_index_field": false, "index_name": "b_1", "help_text": "", "status": "new"}, "package": {"describe_api_name": "object_axkN2__c", "is_index": false, "is_active": true, "create_time": 1692756159358, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "index_name": "pkg", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "object_axkN2__c", "is_index": true, "create_time": 1692756159358, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "md_time", "status": "released"}, "create_time": {"describe_api_name": "object_axkN2__c", "is_index": true, "create_time": 1692756159358, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "crt_time", "status": "released"}, "life_status": {"describe_api_name": "object_axkN2__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "label_r": "生命状态", "index_name": "s_3", "is_index": true, "is_active": true, "create_time": 1692756159274, "is_encrypted": false, "default_value": "normal", "label": "生命状态", "field_num": 7, "is_need_convert": false, "api_name": "life_status", "_id": "64e568bf90e0870001c0faa4", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "object_axkN2__c", "is_index": true, "is_active": true, "create_time": 1692756159358, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "index_name": "md_by", "status": "released"}, "out_tenant_id": {"describe_api_name": "object_axkN2__c", "is_index": false, "is_active": true, "create_time": 1692756159358, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "index_name": "o_ei", "config": {"display": 0}, "max_length": 200, "status": "released"}, "mc_currency": {"describe_api_name": "object_axkN2__c", "is_index": true, "is_active": true, "create_time": 1692756159343, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "", "label": "币种", "type": "select_one", "field_num": 9, "is_required": false, "api_name": "mc_currency", "options": [{"label": "CNY - China Yuan", "value": "CNY"}, {"label": "ALL - 阿尔巴尼亚列克", "value": "ALL"}, {"label": "BZD - 伯利兹元", "value": "BZD"}, {"label": "BYN - 白俄罗斯卢布", "value": "BYN"}, {"label": "BOB - 玻利维亚的玻利维亚诺", "value": "BOB"}, {"label": "BRL - 巴西币", "value": "BRL"}, {"label": "BIF - 布隆迪法郎", "value": "BIF"}, {"label": "BND - 文莱元", "value": "BND"}, {"not_usable": true, "label": "BHD - <PERSON><PERSON>", "value": "BHD"}, {"label": "BMD - 百慕大元", "value": "BMD"}, {"label": "AMD - 亚美尼亚打兰", "value": "AMD"}, {"not_usable": true, "label": "BAM - 自由兑换马克", "value": "BAM"}, {"not_usable": true, "label": "BDT - 孟加拉国塔卡", "value": "BDT"}, {"not_usable": true, "label": "BBD - 巴巴多斯元", "value": "BBD"}, {"not_usable": true, "label": "ARS - 阿根廷比索", "value": "ARS"}, {"not_usable": true, "label": "BGN - Bulgarian Lev", "value": "BGN"}, {"not_usable": true, "label": "AOA - 安哥拉宽扎", "value": "AOA"}, {"not_usable": true, "label": "AZN - 阿塞拜疆马纳特", "value": "AZN"}, {"not_usable": true, "label": "ANG - 荷属安地列斯盾", "value": "ANG"}, {"not_usable": true, "label": "AUD - Australian Dollar", "value": "AUD"}, {"label": "AED - UAE Dirham", "value": "AED"}, {"label": "AFN - Afghanistan Afghani (New)", "value": "AFN"}, {"label": "USD - U.S. Dollar", "value": "USD"}, {"label": "AWG - 阿鲁巴岛弗罗林", "value": "AWG"}], "define_type": "package", "_id": "64e568bf90e0870001c0faa7", "is_single": false, "label_r": "币种", "is_index_field": false, "index_name": "s_4", "config": {}, "help_text": "", "status": "new", "calculate_relation": {"relate_fields": {"object_axkN2__c": [{"fieldName": "mc_exchange_rate_version", "order": 3, "type": "D"}, {"fieldName": "mc_exchange_rate", "order": 3, "type": "D"}, {"fieldName": "mc_functional_currency", "order": 3, "type": "D"}]}, "calculate_fields": {"object_axkN2__c": ["mc_exchange_rate_version", "mc_functional_currency", "mc_exchange_rate"]}}}, "version": {"describe_api_name": "object_axkN2__c", "is_index": false, "create_time": 1692756159358, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "index_name": "version", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "object_axkN2__c", "is_index": true, "is_active": true, "create_time": 1692756159358, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "index_name": "crt_by", "status": "released"}, "relevant_team": {"describe_api_name": "object_axkN2__c", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "create_time": 1692756159274, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "_id": "64e568bf90e0870001c0faaa", "is_single": false, "label_r": "相关团队", "is_index_field": false, "index_name": "a_team", "help_text": "相关团队", "status": "new"}, "record_type": {"describe_api_name": "object_axkN2__c", "is_index": true, "is_active": true, "create_time": 1692756159274, "is_encrypted": false, "auto_adapt_places": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型", "value": "default__c"}, {"is_active": true, "api_name": "record_Gd8l2__c", "label": "从2业务类型", "value": "record_Gd8l2__c"}, {"is_active": false, "api_name": "record_d1m17__c", "label": "禁用的业务类型", "value": "record_d1m17__c"}], "define_type": "package", "_id": "64e568bf90e0870001c0faab", "is_single": false, "label_r": "业务类型", "is_index_field": false, "index_name": "r_type", "config": {}, "help_text": "", "status": "released"}, "data_own_department": {"describe_api_name": "object_axkN2__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "department", "is_required": false, "wheres": [], "define_type": "package", "is_single": true, "label_r": "归属部门", "index_name": "data_owner_dept_id", "is_index": true, "is_active": true, "create_time": 1692756159274, "is_encrypted": false, "default_value": "", "label": "归属部门", "is_need_convert": false, "api_name": "data_own_department", "_id": "64e568bf90e0870001c0faac", "is_index_field": false, "help_text": "", "status": "released"}, "field_5t0iD__c": {"describe_api_name": "object_axkN2__c", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": [], "type": "image", "is_required": false, "define_type": "custom", "is_single": false, "index_name": "a_4", "support_file_types": ["jpg", "gif", "jpeg", "png"], "is_index": true, "file_amount_limit": 1, "is_active": true, "watermark": [{"type": "variable", "value": "current_user"}, {"type": "variable", "value": "current_time"}, {"type": "variable", "value": "current_address"}], "create_time": 1692756159274, "is_encrypted": false, "label": "图片", "is_watermark": false, "field_num": 13, "file_size_limit": 20971520, "is_ocr_recognition": false, "api_name": "field_5t0iD__c", "is_need_cdn": false, "_id": "64e568bf90e0870001c0faad", "is_index_field": false, "identify_type": "", "help_text": "单个图片不得超过20M", "status": "new"}, "name": {"describe_api_name": "object_axkN2__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "name", "is_unique": true, "type": "text", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "is_single": false, "label_r": "主属性", "index_name": "name", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1692756159274, "is_encrypted": false, "default_value": "", "label": "主属性", "api_name": "name", "_id": "64e568bf90e0870001c0faae", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "order_by": {"describe_api_name": "object_axkN2__c", "is_index": false, "create_time": 1692756159358, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "index_name": "l_by", "round_mode": 4, "status": "released"}, "mc_exchange_rate_version": {"describe_api_name": "object_axkN2__c", "is_index": false, "is_active": true, "create_time": 1692756159344, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "label": "汇率版本", "type": "text", "field_num": 14, "is_required": false, "api_name": "mc_exchange_rate_version", "define_type": "package", "_id": "64e568bf90e0870001c0fab0", "is_single": false, "label_r": "汇率版本", "is_index_field": false, "index_name": "t_4", "max_length": 256, "help_text": "", "status": "new"}, "field_4rjBU__c": {"describe_api_name": "object_axkN2__c", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "master_detail", "is_required": true, "define_type": "custom", "is_single": false, "index_name": "s_5", "is_index": true, "is_active": true, "create_time": 1692756159274, "is_encrypted": false, "label": "主从关系", "target_api_name": "object_3BbVb__c", "show_detail_button": false, "target_related_list_name": "target_related_list_7chPN__c", "field_num": 15, "target_related_list_label": "zxf-从", "api_name": "field_4rjBU__c", "is_create_when_master_create": true, "_id": "64e568bf90e0870001c0fab1", "is_index_field": true, "is_required_when_master_create": false, "help_text": "", "status": "new", "calculate_relation": {"relate_fields": {"object_axkN2__c": [{"fieldName": "mc_exchange_rate_version", "order": 3, "type": "D"}, {"fieldName": "mc_exchange_rate", "order": 3, "type": "D"}, {"fieldName": "mc_currency", "order": 2, "type": "D"}, {"fieldName": "mc_functional_currency", "order": 3, "type": "D"}]}, "calculate_fields": {"object_axkN2__c": ["mc_exchange_rate_version", "mc_functional_currency", "mc_currency", "mc_exchange_rate"]}}}, "_id": {"describe_api_name": "object_axkN2__c", "is_index": false, "is_active": true, "create_time": 1692756159358, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "index_name": "_id", "max_length": 200, "status": "released"}, "field_2oC1W__c": {"expression_type": "js", "return_type": "currency", "describe_api_name": "object_axkN2__c", "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "formula", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "custom", "is_single": false, "index_name": "d_3", "is_index": true, "is_active": false, "expression": "$field_20oz0__c$+1", "create_time": 1692756159274, "is_encrypted": false, "label": "计算字段-金额", "field_num": 17, "api_name": "field_2oC1W__c", "_id": "64e568bf90e0870001c0fab3", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}}, "release_version": "6.4", "actions": {}, "calculate_relation": {"relate_fields": {"object_axkN2__c": [{"fieldName": "mc_exchange_rate_version", "order": 3, "type": "D"}, {"fieldName": "mc_exchange_rate", "order": 3, "type": "D"}, {"fieldName": "mc_functional_currency", "order": 3, "type": "D"}]}, "calculate_fields": {"object_axkN2__c": ["mc_exchange_rate_version", "mc_functional_currency", "mc_exchange_rate"]}}}