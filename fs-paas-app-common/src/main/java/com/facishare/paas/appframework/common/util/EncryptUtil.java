package com.facishare.paas.appframework.common.util;

import com.fxiaoke.common.Guard;
import com.github.autoconf.ConfigFactory;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

@UtilityClass
@Slf4j
public final class EncryptUtil {

    public static volatile String encryptKey = "5&I(1wsJ25@zH+eU";

    static {
        ConfigFactory.getConfig("fs-paas-appframework-config", config -> {
            try {
                encryptKey = config.get("encrypt_key", "5&I(1wsJ25@zH+eU");
            } catch (Exception e) {
                log.error("load fs-paas-appframework-config error", e);
            }
        });
    }

    public static final Guard encrypt = new Guard(encryptKey);

    public String encode(String raw) {
        try {
            return encrypt.encode(raw);
        } catch (Exception e) {
            throw new RuntimeException("cannot encode " + raw);
        }
    }

    public String decode(String token) {
        try {
            return encrypt.decode(token);
        } catch (Exception e) {
            throw new RuntimeException("cannot decode " + token);
        }
    }
}
