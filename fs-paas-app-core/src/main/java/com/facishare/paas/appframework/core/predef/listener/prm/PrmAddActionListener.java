package com.facishare.paas.appframework.core.predef.listener.prm;

import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ActionListener;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.prm.PartnerCoreService;
import com.facishare.paas.appframework.prm.model.IPartnerData;
import com.facishare.paas.appframework.prm.model.OutResources;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * prm标准新建动作监听类
 *
 * <AUTHOR>
 */
@Component
public class PrmAddActionListener implements ActionListener<BaseObjectSaveAction.Arg, BaseObjectSaveAction.Result> {

    @Autowired
    PartnerCoreService partnerCoreService;

    @Override
    public void before(ActionContext context, BaseObjectSaveAction.Arg arg) {
        User user = context.getUser();
        String partnerId = partnerCoreService.getPartnerId(user);
        fillDefaultValue(arg.getObjectData(), partnerId);
        arg.getDetails().forEach((apiName, objectDataList) ->
                objectDataList.forEach(objectData -> fillDefaultValue(objectData, partnerId)));
    }

    private void fillDefaultValue(ObjectDataDocument objectData, String partnerId) {
        objectData.put(IPartnerData.OUT_RESOURCES, OutResources.PARTNER.getValue());
        objectData.put(IPartnerData.PARTNER_ID, partnerId);
    }

    @Override
    public void after(ActionContext context, BaseObjectSaveAction.Arg arg, BaseObjectSaveAction.Result result) {

    }
}
