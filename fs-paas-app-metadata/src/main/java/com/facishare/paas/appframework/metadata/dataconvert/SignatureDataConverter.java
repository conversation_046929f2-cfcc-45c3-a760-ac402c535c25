package com.facishare.paas.appframework.metadata.dataconvert;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.common.exception.CrmDefObjCheckedException;
import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

public class SignatureDataConverter extends AbstractFieldDataConverter {
    @Override
    public String convertFieldData(SessionContext sessionContext) throws CrmDefObjCheckedException, MetadataServiceException {

        IObjectData objectData = getObjectData();
        IFieldDescribe fieldDescribe = getFieldDescribe();
        Object value = objectData.get(fieldDescribe.getApiName());
        if (!(value instanceof List)) {
            return "";
        }

        List<Signature> signatures = JSONArray.parseArray(value.toString(), Signature.class);
        return signatures.stream().map(Signature::getFilePathForXml).collect(Collectors.joining("|"));
    }

    @Data
    private static class Signature {
        @JSONField(name = "ext")
        String extension;
        @JSONField(name = "path")
        String path;

        public String getFilePathForXml() {
            if (StringUtils.isNotBlank(extension) && !StringUtils.endsWith(path, extension)) {
                return (path + "." + extension) + "#" + path;
            }
            return path + "#" + path;
        }
    }
}
