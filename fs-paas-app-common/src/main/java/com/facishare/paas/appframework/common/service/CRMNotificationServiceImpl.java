package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.SendCrmMessageModel;
import com.facishare.paas.appframework.common.service.dto.SendNewCrmMessageModel;
import com.facishare.paas.appframework.common.service.model.CRMNotification;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.fxiaoke.api.IdGenerator;
import com.github.autoconf.ConfigFactory;
import joptsimple.internal.Strings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * CRM通知服务
 * <p>
 * Created by liyiguang on 2018/3/28.
 */
@Slf4j
@Service("crmNotificationService")
public class CRMNotificationServiceImpl implements CRMNotificationService {

    @Autowired
    private SendCrmMessageProxy sendCrmMessageProxy;

    @Autowired
    private SendNewCrmMessageProxy sendNewCrmMessageProxy;

    @Autowired
    private MessagePlatformService messagePlatformService;
    private static boolean SUPPORT_CRM_REMIND = true;
    private static boolean CLOSE_OLD_CRM_REMIND = false;

    static {
        ConfigFactory.getConfig("fs-paas-appframework-config", config -> {
            SUPPORT_CRM_REMIND = config.getBool("support_crm_remind", true);
            CLOSE_OLD_CRM_REMIND = config.getBool("close_old_crm_remind", false);
            log.warn("reload config fs-paas-appframework-config,SUPPORT_CRM_REMIND:{},CLOSE_OLD_CRM_REMIND:{}",
                    SUPPORT_CRM_REMIND, CLOSE_OLD_CRM_REMIND);
        });
    }

    @Override
    public void sendCRMNotification(User user, CRMNotification notification) {
        if (CLOSE_OLD_CRM_REMIND) {
            sendCrmRemind(user, notification);
        }

        //接入消息平台
        sendToMessagePlatform(user, notification);

    }

    private void sendCrmRemind(User user, CRMNotification notification) {
        SendCrmMessageModel.Arg sendArg = SendCrmMessageModel.Arg.builder()
                .employeeId(notification.getSender())
                .content(notification.getContent())
                .title(notification.getTitle())
                .remindRecordType(notification.getRemindRecordTypeWithDefaultValue())
                .content2Id(notification.getContent2Id())
                .dataId(notification.getDataId())
                .receiverIds(notification.getReceiverIds())
                .fixContent2ID(notification.getFixContent2ID())
                .outEI(notification.getOutEI())
                .appId(notification.getAppId())
                .build();

        SendCrmMessageModel.Result result = sendCrmMessageProxy.sendCrmMessages(user.getTenantId(), sendArg);
        if (Objects.isNull(result) || !result.isSuccess()) {
            log.warn("Error in sendCRMNotification, user:{}, arg:{}, result:{}", user, notification, result);
        }
    }

    private void sendToMessagePlatform(User user, CRMNotification notification) {
        try {
            messagePlatformService.sendTextMessage(user, notification);
        } catch (Exception e) {
            log.error("Error in sendToMessagePlatform, user:{}, notification:{}, result:{}", user, notification, e);
        }
    }

    //调用新的消息通知接口
    @Override
    public void sendNewCrmNotification(User user, NewCrmNotification newCrmNotification) {
        if (SUPPORT_CRM_REMIND) {
            sendNewCrmRemind(user, newCrmNotification);
        }
        try {
            //接入消息平台
            messagePlatformService.sendPlatFormMessage(user, newCrmNotification);
        } catch (Exception e) {
            log.error("Error in sendPlatFormMessage, user:{}, notification:{}, result:{}", user, newCrmNotification, e);
        }
    }

    private void sendNewCrmRemind(User user, NewCrmNotification newCrmNotification) {
        SendNewCrmMessageModel.Arg sendArg = SendNewCrmMessageModel.Arg.builder()
                .sourceId(Strings.isNullOrEmpty(newCrmNotification.getSourceId()) ? IdGenerator.get() : newCrmNotification.getSourceId())
                .senderId(newCrmNotification.getSenderId())
                .type(newCrmNotification.getType())
                .receiverIDs(newCrmNotification.getReceiverIDs())
                .departments(newCrmNotification.getDepartments())
                .remindSender(newCrmNotification.isRemindSender())
                .title(newCrmNotification.getTitle())
                .titleInfo(newCrmNotification.getTitleInfo())
                .fullContent(newCrmNotification.getFullContent())
                .fullContentInfo(newCrmNotification.getFullContentInfo())
                .urlType(newCrmNotification.getUrlType())
                .urlParameter(newCrmNotification.getUrlParameter())
                .appId(newCrmNotification.getAppId())
                .lastSummary(newCrmNotification.getLastSummary())
                .lastSummaryInfo(newCrmNotification.getLastSummaryInfo())
                .innerPlatformWebUrl(newCrmNotification.getInnerPlatformWebUrl())
                .innerPlatformMobileUrl(newCrmNotification.getInnerPlatformMobileUrl())
                .outPlatformUrl(newCrmNotification.getOutPlatformUrl())
                .bodyForm(newCrmNotification.getBodyForm())
                .extraChannelList(newCrmNotification.getExtraChannelList())
                .extraDataMap(newCrmNotification.getExtraDataMap())
                .templateIdKeyListMap(newCrmNotification.getTemplateIdKeyListMap())
                .build();

        SendNewCrmMessageModel.RemindRecordItem remindRecordItem = SendNewCrmMessageModel.RemindRecordItem.builder()
                .ei(user.getTenantIdInt())
                .uuid(sendArg.getSourceId())
                .remindRecordItem(sendArg)
                .build();
        SendNewCrmMessageModel.Result result = sendNewCrmMessageProxy.sendNewCrmMessages(RestUtils.buildHeaders(user), remindRecordItem);
        if (result.isSuccess()) {
            log.debug("send new crm message success,ei={},sendArg={},result={}", user.getTenantId(), sendArg, result);
        } else {
            log.warn("send new crm messages failed,ei={},sendArg={},result={}", user.getTenantId(), sendArg, result);
        }
    }
}
