package com.facishare.paas.appframework.common.service;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.organization.adapter.api.model.organizationwithouter.OrganizationEmployee;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.DepartmentNameLanguage;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.PersonnelNameLanguage;
import com.facishare.organization.api.model.type.DepartmentStatus;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.*;
import com.facishare.paas.appframework.common.util.ConvertUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.social.department.DepartmentObjService;
import com.facishare.social.department.model.BatchGetDepartmentByDeptCodes;
import com.facishare.social.department.model.BatchGetDepartmentByDeptIds;
import com.facishare.social.personnel.model.PersonnelDto;
import com.fxiaoke.enterpriserelation2.data.ErDepartmentSimpleData;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 组织架构服务封装 <p> Created by liyiguang on 2017/8/18.
 */
@Slf4j
@Primary
@Service("orgServiceDirect")
public class OrgServiceDirectImpl implements OrgService {

    private final Cache<String, String> userNameLocalCache;
    @Autowired
    OrgServiceProxy orgServiceProxy;
    @Autowired
    OuterOrganizationService outerOrganizationService;
    @Resource(name = "employeeServiceForOrg")
    EmployeeService employeeService;
    @Autowired
    DepartmentService departmentService;
    @Autowired
    DepartmentObjService departmentObjService;

    public OrgServiceDirectImpl() {
        userNameLocalCache = CacheBuilder.newBuilder()
                .expireAfterAccess(1, TimeUnit.HOURS)
                .maximumSize(100000)
                .build();
    }

    @Override
    public List<String> getMembersByDeptIds(User user, List<String> deptIds, Integer userStatus) {
        return employeeService.batchGetEmployeeIdsByDeptIds(user.getTenantId(), deptIds, userStatus, true);
    }

    @Override
    public User getUser(String tenantId, String userId) {
        User ret = new User(tenantId, userId);
        if (!ret.isSupperAdmin()) {
            ret.setUserName(getUserName(tenantId, userId));
        }
        return ret;
    }

    @Override
    public Map<String, String> getDeptName(String tenantId, String userId, List<String> userIds) {
        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> deptInfoMap = getMainDeptInfo(tenantId, userId, userIds);
        Map<String, String> deptNameMap = Maps.newHashMap();
        deptInfoMap.forEach((x, y) -> {
            deptNameMap.put(x, y.getDeptName());
        });

        return deptNameMap;
    }

    private String getUserName(String tenantId, String userId) {
        if (!isEffectiveId(userId) && !User.SUPPER_ADMIN_USER_ID.equals(userId)) {
            log.warn("userId not effective, tenantId:{}, userId:{}", tenantId, userId);
            return null;
        }
        String key = tenantId + "." + userId;
        String name = userNameLocalCache.getIfPresent(key);
        if (name != null) {
            return name;
        }

        if (checkIsOutUser(userId)) {
            // 外部人员，查询互联接口
            List<OrganizationEmployee> outUserList = outerOrganizationService.batchGetEmployee(tenantId, Lists.newArrayList(userId));
            if (CollectionUtils.isNotEmpty(outUserList)) {
                return outUserList.get(0).getName();
            } else {
                return null;
            }
        } else {
            // 内部人员，调用组织架构
            EmployeeDto employee = employeeService.getUserInfo(tenantId, userId);
            return employee == null ? null : getEmployeeName(employee);
        }
    }

    private boolean isEffectiveId(String id) {
        return NumberUtils.isDigits(id) && !"0".equals(id);
    }

    @Override
    public List<UserInfo> getUserNameByIds(String tenantId, String userId, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        List<String> innerIds = Lists.newArrayList();
        List<String> outIds = Lists.newArrayList();
        splitUserIds(userIds, innerIds, outIds);
        // 查询内部人员信息
        List<EmployeeDto> innerUsers = employeeService.batchGetUserInfo(tenantId, innerIds);
        List<UserInfo> userInfos = batchConvertEmployeeDtoToUserInfo(innerUsers);
        // 查询外部人员信息
        List<OrganizationEmployee> outUsers = outerOrganizationService.batchGetEmployee(tenantId, outIds);
        userInfos.addAll(batchConvertOutEmployeeToUserInfo(outUsers));
        return userInfos;
    }

    @Override
    public Map<String, UserInfo> getUserInfoMapByIds(String tenantId, String userId, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Maps.newHashMap();
        }
        Map<String, UserInfo> userInfoMap = Maps.newHashMap();
        List<UserInfo> userInfoList = getUserNameByIds(tenantId, userId, userIds);
        if (CollectionUtils.isNotEmpty(userInfoList)) {
            userInfoList.forEach(x -> userInfoMap.put(x.getId(), x));
        }
        return userInfoMap;
    }

    @Override
    public List<UserInfoExt> getUserExtByIds(String tenantId, String userId, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        List<EmployeeDto> users = employeeService.batchGetUserInfo(tenantId, userIds);
        List<UserInfo> userInfos = batchConvertEmployeeDtoToUserInfo(users);
        List<UserInfoExt> result = Lists.newArrayList();
        for (UserInfo user : userInfos) {
            UserInfoExt userExt = new UserInfoExt();
            userExt.setActive(user.getStatus() == 0);
            userExt.setCreateTime(user.getCreateTime());
            userExt.setName(user.getName());
            userExt.setNickname(user.getNickname());
            userExt.setModifyTime(user.getModifyTime());
            userExt.setDept(user.getDept());
            userExt.setDescription(user.getDescription());
            userExt.setEmail(user.getEmail());
            userExt.setPhone(user.getPhone());
            userExt.setId(user.getId());
            userExt.setPicAddr(user.getPicAddr());
            userExt.setPost(user.getPost());
            userExt.setTenantId(user.getTenantId());
            userExt.setSupervisorId(user.getSupervisorId());
            result.add(userExt);
        }
        return result;
    }

    @Override
    public List<String> getSubDeptByDeptId(String tenantId, String userId, String deptId, boolean isAll) {

        List<DepartmentDto> departments = departmentService.getLowDepartment(tenantId, deptId, QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE);
        if (isAll) {
            return departments.stream().map(x -> ConvertUtils.integerToString(x.getDepartmentId())).collect(Collectors.toList());
        } else {
            return departments.stream().filter(x -> x.parentId() != null && x.parentId().toString().equals(deptId))
                    .map(x -> ConvertUtils.integerToString(x.getDepartmentId())).collect(Collectors.toList());
        }
    }

    @Override
    public Map<String, String> getUserNameMapByIds(String tenantId, String userId, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Maps.newHashMap();
        }
        Map<String, String> userId2NameMap = Maps.newHashMap();
        List<UserInfo> userInfoList = getUserNameByIds(tenantId, userId, userIds);
        if (CollectionUtils.isNotEmpty(userInfoList)) {
            userInfoList.forEach(x -> userId2NameMap.put(x.getId(), x.getName()));
        }
        return userId2NameMap;
    }

    @Override
    public Map<String, QueryDeptInfoByUserIds.MainDeptInfo> getMainDeptInfo(String tenantId, String userId,
                                                                            List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Maps.newHashMap();
        }
        // 查询员员工信息
        List<EmployeeDto> employeeList = employeeService.batchGetUserInfo(tenantId, userIds);
        // 获取所有员工的主属部门ID
        Set<String> mainDeptIdList = employeeList.stream().map(EmployeeDto::getMainDepartmentId)
                .filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toSet());
        // 查询部门信息
        List<DepartmentDto> departmentList = departmentService.batchGetDepartment(tenantId, mainDeptIdList);
        // 查询部门老大信息
        Set<String> leaderIdList = departmentList.stream().map(DepartmentDto::getPrincipalId)
                .filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toSet());
        List<EmployeeDto> leaderInfoList = employeeService.batchGetUserInfo(tenantId, leaderIdList);
        List<QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfoList =
                convertAllInfoToMainDeptInfo(employeeList, leaderInfoList, departmentList);
        return mainDeptInfoList.stream().filter(d -> Objects.nonNull(d.getUserId()))
                .collect(Collectors.toMap(d -> d.getUserId(), d -> d));
    }

    @Override
    public List<QueryDeptInfoByUserIds.MainDeptInfo> getDeptInfoByUserIds(String tenantId, String userId,
                                                                          List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        List<QueryDeptInfoByUserIds.MainDeptInfo> deptInfoList = Lists.newArrayList();
        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> deptInfoMap = getMainDeptInfo(tenantId, userId, userIds);
        deptInfoMap.forEach((x, y) -> deptInfoList.add(y));

        return deptInfoList;
    }

    @Override
    public List<QueryDeptInfoByDeptIds.DeptInfo> getDeptInfoNameByIds(String tenantId, String userId, List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = getDeptInfoByIdsAndStatus(tenantId, idList, QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE);
        return deptInfos;
    }

    public List<QueryDeptInfoByDeptIds.DeptInfo> getDeptInfoByIdsAndStatus(String tenantId, List<String> idList, QueryDeptInfoByDeptIds.DeptStatusEnum status) {
        List<String> innerIds = Lists.newArrayList();
        List<String> outIds = Lists.newArrayList();
        splitDepartmentIds(idList, innerIds, outIds);
        List<DepartmentDto> innerDepartments = departmentService.batchGetDepartment(tenantId, innerIds, status);
        List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = batchConvertDepartmentDtoToDeptInfo(innerDepartments);
        List<ErDepartmentSimpleData> erDepartmentSimpleDataList = outerOrganizationService.batchGetOutDepartment(User.systemUser(tenantId), outIds, status);
        deptInfos.addAll(batchConvertErDepartmentDtoToDeptInfo(erDepartmentSimpleDataList));
        return deptInfos;
    }

    private List<QueryDeptInfoByDeptIds.DeptInfo> batchConvertErDepartmentDtoToDeptInfo(List<ErDepartmentSimpleData> erDepartmentSimpleDataList) {
        List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = Lists.newArrayList();
        for (ErDepartmentSimpleData erDepartmentName : erDepartmentSimpleDataList) {
            deptInfos.add(convertErDepartmentDtoToDeptInfo(erDepartmentName));
        }
        return deptInfos;
    }

    private QueryDeptInfoByDeptIds.DeptInfo convertErDepartmentDtoToDeptInfo(ErDepartmentSimpleData erDepartmentSimpleData) {
        QueryDeptInfoByDeptIds.DeptInfo info = new QueryDeptInfoByDeptIds.DeptInfo();
        info.setDeptId(erDepartmentSimpleData.getId());
        info.setDeptName(erDepartmentSimpleData.getName());
        info.setParentId(erDepartmentSimpleData.getPid());
        info.setStatus(erDepartmentSimpleData.getStatus() == 1 ? 0 : 1);
        return info;
    }

    private void splitDepartmentIds(List<String> idList, List<String> innerIds, List<String> outIds) {
        List<String> innerIdList = idList.stream()
                .filter(NumberUtils::isDigits)
                .collect(Collectors.toList());
        innerIds.addAll(innerIdList);
        //有的调用方传了null的id过来，这里兼容一下
        List<String> outIdList = idList.stream().filter(id -> !innerIdList.contains(id))
                .filter(id -> !StringUtils.equals("null", id))
                .collect(Collectors.toList());
        outIds.addAll(outIdList);
    }

    @Override
    public List<QueryDeptInfoByDeptIds.DeptInfo> getAllDeptInfoNameByIds(String tenantId, String userId, List<String> idList) {
        //有的调用方传了null的id过来，这里兼容一下
        idList = idList.stream()
                .filter(NumberUtils::isDigits)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }

        List<DepartmentDto> departments = departmentService.batchGetDepartment(tenantId, idList);
        return batchConvertDepartmentDtoToDeptInfo(departments);
    }

    @Override
    public List<QueryDeptInfoByDeptIds.DeptInfo> getDeptInfoNameByIdsAndStatus(String tenantId, String userId, List<String> idList, QueryDeptInfoByDeptIds.DeptStatusEnum deptStatus) {
        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = getDeptInfoByIdsAndStatus(tenantId, idList, deptStatus);
        deptInfos.stream()
                .filter(QueryDeptInfoByDeptIds.DeptInfo::disabled)
                .forEach(deptInfo -> deptInfo.setDeptName(I18N.text(I18NKey.DISABLED, deptInfo.getDeptName())));
        return deptInfos;
    }

    /**
     * 查询用户所有上级部门，包含所属部门
     */
    @Override
    public List<String> queryAllSuperDeptByUserId(String tenantId, String userId, String id) {
        EmployeeDto employee = employeeService.getUserInfo(tenantId, id);
        if (Objects.isNull(employee) || CollectionUtils.isEmpty(employee.getDepartmentIds())) {
            return Lists.newArrayList();
        }
        List<Integer> belongDeptIds = employee.getDepartmentIds();
        Set<String> belongDeptSet = belongDeptIds.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toSet());
        List<DepartmentDto> departments = departmentService.batchGetDepartment(tenantId, belongDeptSet, QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE);
        Set<String> superDeptIds = departments.stream().flatMap(d -> d.getAncestors().stream())
                .filter(Objects::nonNull)
                .map(String::valueOf)
                .collect(Collectors.toSet());
        superDeptIds.addAll(belongDeptSet);
        superDeptIds.remove("999999");
        return Lists.newArrayList(superDeptIds);
    }

    /*
     * 获取用户组，此接口暂时不迁
     */
    @Override
    public List<String> queryGroupByUserIds(String tenantId, String userId) {
        QueryGroupByUserIds.Arg arg = QueryGroupByUserIds.Arg.builder()
                .appId(DefObjConstants.PACKAGE_NAME_CRM)
                .tenantId(tenantId)
                .userIdList(Lists.newArrayList(userId))
                .isFilterByUser(Boolean.FALSE)
                .isPublic(Boolean.TRUE)
                .userId(userId)
                .status(0)
                .build();
        QueryGroupByUserIds.Result result = orgServiceProxy.queryGroupByUserIds(arg);
        if (result.isSuccess()) {
            return Lists.newArrayList(result.getResult());
        } else {
            log.error("queryGroupByUserIds error,arg:{},result={}", arg, result);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<String> getUserIdsByName(String tenantId, String userName, String userId) {
        List<EmployeeDto> employees = employeeService.batchGetUserByNickName(tenantId, userName);
        return employees.stream().map(e -> ConvertUtils.integerToString(e.getEmployeeId())).collect(Collectors.toList());
    }

    @Override
    public List<QueryDeptInfoByUserIds.MainDeptInfo> queryDeptByUserBatch(String tenantId, String userId,
                                                                          List<String> userIds) {

        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> userIdDeptMap = getMainDeptInfo(tenantId, userId, userIds);
        return new ArrayList<>(userIdDeptMap.values());
    }

    @Override
    public List<QueryDeptByName.DeptInfo> getDeptByName(String tenantId, String userId, List<String> names) {
        List<DepartmentDto> departments = departmentService.batchGetDepartmentByNames(tenantId, names, QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE);
        return batchConvertDepartmentDtoToDeptInfo2(departments);
    }

    @Override
    public List<DeptInfo> getDeptInfoByName(String tenantId, String userId, List<String> names) {
        List<DepartmentDto> departments = departmentService.batchGetDepartmentByNames(tenantId, names, QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE);
        return DeptInfo.convertToDeptInfo(departments);
    }

    @Override
    public List<UserInfo> getUserByName(String tenantId, String userId, List<String> names) {
        return batchConvertEmployeeDtoToUserInfo(employeeService.batchGetUserByNickNames(tenantId, names, 0));
    }

    @Override
    public List<String> getMembersByDeptIds(User user, List<String> deptIds) {
        return getMembersByDeptIds(user, deptIds, null);
    }

    /*
     * 用户组接口暂时不切换
     */
    public List<String> getMembersByGroupIds(User user, List<String> groupIds) {
        QueryMembersByGroupIds.Arg arg = QueryMembersByGroupIds.Arg.builder()
                .appId(DefObjConstants.PACKAGE_NAME_CRM)
                .tenantId(user.getTenantId())
                .userId(user.getUserId())
                .groupIdList(groupIds)
                .build();
        QueryMembersByGroupIds.Result result = orgServiceProxy.getMembersByGroupIds(arg);
        if (!result.isSuccess()) {
            log.error("getMembersByGroupIds error,arg:{},result:{}", arg, result);
            return Lists.newArrayList();
        }

        return result.getResult().stream().map(x -> ConvertUtils.integerToString(x)).collect(Collectors.toList());
    }

    public List<String> getDeptLeaders(User user, List<String> deptIds) {
        List<DepartmentDto> departments = departmentService.batchGetDepartment(user.getTenantId(), deptIds);
        List<String> leaderIds = departments.stream().filter(d -> Objects.nonNull(d.getPrincipalId()))
                .map(d -> ConvertUtils.integerToString(d.getPrincipalId())).distinct().collect(Collectors.toList());
        return leaderIds;
    }

    public List<String> getDeptLeadersByUserIds(User user, List<String> userIds) {
        // 查询人员
        List<EmployeeDto> employees = employeeService.batchGetUserInfo(user.getTenantId(), userIds);
        // 获取人员主属部门
        List<String> mainDeptIds = employees.stream().filter(e -> Objects.nonNull(e.getMainDepartmentIds()))
                .flatMap(e -> ConvertUtils.batchConvertIntegerToString(e.getMainDepartmentIds()).stream())
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mainDeptIds)) {
            return Lists.newArrayList();
        }
        // 获取部门信息
        List<DepartmentDto> departments = departmentService.batchGetDepartment(user.getTenantId(), mainDeptIds);
        return departments.stream().filter(d -> Objects.nonNull(d.getPrincipalId()))
                .map(d -> ConvertUtils.integerToString(d.getPrincipalId())).collect(Collectors.toList());
    }

    public List<String> getReportingObjectsByUserIds(User user, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        List<EmployeeDto> employeeList = employeeService.batchGetUserInfo(user.getTenantId(), userIds);
        return employeeList.stream().filter(e -> Objects.nonNull(e.getLeaderId()))
                .map(e -> ConvertUtils.integerToString(e.getLeaderId())).distinct().collect(Collectors.toList());
    }

    @Override
    public List<UserInfo> getSubordinatesByUserId(String tenantId, String userId, String
            id, boolean isCascade) {
        List<EmployeeDto> employeeList = employeeService.querySubordinatesByUserId(tenantId, id, 0, isCascade);
        return batchConvertEmployeeDtoToUserInfo(employeeList);
    }

    @Override
    public List<String> getSubordinateIdsByUserId(String tenantId, String userId, String id, boolean isCascade) {
        return employeeService.querySubordinateIdsByUserId(tenantId, id, 0, isCascade);
    }

    @Override
    public Map<String, List<QueryAllSuperDeptsByDeptIds.DeptInfo>> getAllSuperDeptsByDeptIds(String tenantId, String userId,
                                                                                             List<String> deptIds) {
        Map<String, List<DepartmentDto>> idMap = departmentService.batchGetUpperDepartment(tenantId, deptIds, true);
        Map<String, List<QueryAllSuperDeptsByDeptIds.DeptInfo>> result = Maps.newHashMap();
        idMap.forEach((deptId, upperDeptList) -> {
            List<QueryAllSuperDeptsByDeptIds.DeptInfo> infos = Lists.newArrayList();
            for (DepartmentDto upper : upperDeptList) {
                QueryAllSuperDeptsByDeptIds.DeptInfo info = new QueryAllSuperDeptsByDeptIds.DeptInfo();
                info.setId(ConvertUtils.integerToString(upper.getDepartmentId()));
                info.setAncestors(ConvertUtils.batchConvertIntegerToString(upper.getAncestors()));
                info.setCreateTime(upper.getCreateTime());
                info.setModifyTime(upper.getUpdateTime());
                info.setDescription(upper.getDescription());
                info.setManagerId(String.valueOf(info.getManagerId()));
                info.setName(getDeptName(upper));
                info.setParentId(upper.parentId());
                info.setTenantId(ConvertUtils.integerToString(upper.getEnterpriseId()));
                Integer status = upper.getStatus() == DepartmentStatus.NORMAL ? 0 : 1;
                info.setStatus(status);
                infos.add(info);
            }
            result.put(deptId, infos);
        });
        return result;
    }

    @Override
    public Map<String, List<String>> getAllSuperDeptIdsByDeptIds(String tenantId, String userId, List<String> deptIds) {
        return departmentService.batchGetUpperDepartmentIds(tenantId, deptIds, true);
    }

    @Override
    public List<QueryResponsibleDeptsByUserIds.DeptInfo> getResponsibleDeptsByUserIds(String tenantId, String userId, List<String> managerIds, Integer deptStatus) {
        List<DepartmentDto> departments = departmentService.batchGetDepartmentByPrincipal(tenantId, managerIds, QueryDeptInfoByDeptIds.DeptStatusEnum.of(deptStatus));
        return departments.stream().map(QueryResponsibleDeptsByUserIds.DeptInfo::convert).collect(Collectors.toList());
    }

    @Override
    public List<QueryDeptInfoByUserId.DeptInfo> getDeptInfoByUserId(String tenantId, String userId, String id) {
        List<DepartmentDto> departments = departmentService.getDepartmentByEmployeeId(tenantId, id);
        List<QueryDeptInfoByUserId.DeptInfo> infos = Lists.newArrayList();
        for (DepartmentDto department : departments) {
            QueryDeptInfoByUserId.DeptInfo info = new QueryDeptInfoByUserId.DeptInfo();
            info.setDeptId(ConvertUtils.integerToString(department.getDepartmentId()));
            info.setDeptName(getDeptName(department));
            info.setLeaderUserId(ConvertUtils.integerToString(department.getPrincipalId()));
            info.setParentId(ConvertUtils.integerToString(department.parentId()));
            infos.add(info);
        }
        return infos;
    }

    @Override
    public Map<String, List<QueryDeptByName.DeptInfo>> getSubDeptsByDeptIds(String tenantId, String userId, List<String> deptIds, Integer childDeptStatus) {
        Map<String, List<DepartmentDto>> departments = departmentService.batchGetLowDepartment(
                tenantId, deptIds, QueryDeptInfoByDeptIds.DeptStatusEnum.of(childDeptStatus), Boolean.TRUE);
        Map<String, List<QueryDeptByName.DeptInfo>> infos = Maps.newHashMap();
        departments.forEach((id, subs) -> {
            infos.put(id, batchConvertDepartmentDtoToDeptInfo2(subs));
        });
        return infos;
    }

    @Override
    public Map<String, List<String>> getSubDeptsByDeptIdsMap(String tenantId, String userId, List<String> deptIds, Integer childDeptStatus) {
        return departmentService.batchGetLowDepartmentIdsMap(
                tenantId, deptIds, QueryDeptInfoByDeptIds.DeptStatusEnum.of(childDeptStatus), Boolean.TRUE);
    }

    @Override
    public Map<String, List<QueryMembersByDeptIds.Member>> getMemberMapByDeptIds(User user, List<String> deptIds) {

        Map<Integer, List<EmployeeDto>> deptEmployeeMap = employeeService.getEmployeesByDepartment(user.getTenantId(),
                deptIds, Boolean.TRUE);

        Map<String, List<QueryMembersByDeptIds.Member>> result = Maps.newHashMap();
        deptEmployeeMap.forEach((id, employees) -> {
            List<QueryMembersByDeptIds.Member> members = Lists.newArrayList();
            for (EmployeeDto emp : employees) {
                QueryMembersByDeptIds.Member member = new QueryMembersByDeptIds.Member(ConvertUtils.integerToString(emp.getEmployeeId()),
                        getEmployeeName(emp), emp.getEmail(), ConvertUtils.integerToString(emp.getMainDepartmentId()), emp.getPost());
                members.add(member);
            }
            result.put(ConvertUtils.integerToString(id), members);
        });
        return result;
    }

    @Override
    public Map<String, List<QueryMemberInfosByDeptIds.Member>> getMemberInfoMapByDeptIds(User user, List<String> deptIds, Boolean includeLowDept, Integer userStatus, Integer deptUserType) {
        Map<Integer, List<EmployeeDto>> deptEmployeeMap = employeeService.getEmployeesByDepartment(user.getTenantId(),
                Sets.newHashSet(deptIds), includeLowDept, userStatus, deptUserType);

        Map<String, List<QueryMemberInfosByDeptIds.Member>> result = Maps.newHashMap();
        deptEmployeeMap.forEach((deptId, employees) -> {
            List<QueryMemberInfosByDeptIds.Member> members = Lists.newArrayList();
            for (EmployeeDto emp : employees) {
                QueryMemberInfosByDeptIds.Member member = QueryMemberInfosByDeptIds.Member.builder()
                        .id(ConvertUtils.integerToString(emp.getEmployeeId()))
                        .name(emp.getFullName())
                        .createTime(emp.getCreateTime())
                        .description(emp.getDescription())
                        .email(emp.getEmail())
                        .modifyTime(emp.getUpdateTime())
                        .nickname(getEmployeeName(emp))
                        .phone(emp.getTelephone())
                        .picAddr(emp.getProfileImage())
                        .position(emp.getPost())
                        .status(convertEmployeeEntityStatusToUserInfoStatus(emp.getStatus()))
                        .supervisorId(ConvertUtils.integerToString(emp.getLeaderId()))
                        .tenantId(ConvertUtils.integerToString(emp.getEnterpriseId()))
                        .title(null)
                        .build();
                members.add(member);
            }
            result.put(ConvertUtils.integerToString(deptId), members);
        });
        return result;
    }

    @Override
    public List<GetNDeptPathByUserId.DeptInfo> getNDeptPathByUserId(User user, String userId, int n) {
        EmployeeDto employee = employeeService.getUserInfo(user.getTenantId(), userId);
        String mainDeptId = ConvertUtils.integerToString(employee.getMainDepartmentId());
        if (Objects.isNull(mainDeptId)) {
            return Lists.newArrayList();
        }
        List<DepartmentDto> departments = departmentService.getUpperDepartments(user.getTenantId(), mainDeptId, true);
        List<GetNDeptPathByUserId.DeptInfo> deptInfoList = Lists.newArrayList();
        for (DepartmentDto department : departments) {
            GetNDeptPathByUserId.DeptInfo info = GetNDeptPathByUserId.DeptInfo.builder()
                    .deptId(ConvertUtils.integerToString(department.getDepartmentId()))
                    .name(getDeptName(department))
                    .status(department.getStatus().getValue())
                    .managerId(ConvertUtils.integerToString(department.getPrincipalId()))
                    .parentId(ConvertUtils.integerToString(department.parentId())).build();
            deptInfoList.add(info);
        }
        //把上级部门路径的顺序从高到低转换成从低级到高级
        if (CollectionUtils.isEmpty(deptInfoList)) {
            return Lists.newArrayList();
        }
        List<GetNDeptPathByUserId.DeptInfo> result = Lists.newArrayList();
        deptInfoList.forEach(deptInfo -> result.add(deptInfo));
        return result.subList(0, Math.min(n, result.size()));

    }

    @Override
    public List<QueryDeptByName.DeptInfo> getDeptByName(String tenantId, String userId, List<String> names, Integer deptStatus) {
        List<DepartmentDto> departments = departmentService.batchGetDepartmentByNames(tenantId, names, QueryDeptInfoByDeptIds.DeptStatusEnum.of(deptStatus));
        return batchConvertDepartmentDtoToDeptInfo2(departments);
    }

    @Override
    public List<DeptInfo> getDeptInfoByNameAndStatus(String tenantId, String userId, List<String> names, Integer deptStatus) {
        List<DepartmentDto> departments = departmentService.batchGetDepartmentByNames(tenantId, names, QueryDeptInfoByDeptIds.DeptStatusEnum.of(deptStatus));
        return DeptInfo.convertToDeptInfo(departments);
    }

    @Override
    public List<UserInfo> getUserByName(String tenantId, String userId, List<String> names, Integer userStatus) {
        List<EmployeeDto> employeeList = employeeService.batchGetUserByNickNames(tenantId, names, userStatus);
        return batchConvertEmployeeDtoToUserInfo(employeeList);
    }


    @Override
    public List<UserInfo> getUserByCodes(User user, List<String> codes) {
        List<PersonnelDto> personnelDtoList = employeeService.batchGetUserByCodes(user, codes);
        return batchConvertPersonnelDtoToUserInfo(personnelDtoList);
    }

    @Override
    public Map<String, List<UserInfo>> batchQuerySupervisorByUserId(String tenantId, String userId, List<String> userIds) {
        Map<Integer, List<EmployeeDto>> employeeMap = employeeService.batchGetAllEmployeeLeaderMap(tenantId, userIds, null, 2);
        Map<String, List<UserInfo>> result = Maps.newHashMap();
        employeeMap.forEach((k, v) -> result.put(ConvertUtils.integerToString(k), batchConvertEmployeeDtoToUserInfo(v)));
        return result;
    }

    @Override
    public List<UserInfo> batchGetResponsibleEmployeeByUserId(String tenantId, String userId, Set<String> userIds) {
        List<OrganizationEmployee> result = outerOrganizationService.batchGetResponsibleEmployee(tenantId, userId, userIds);
        return batchConvertOutEmployeeToUserInfo(result);
    }

    @Override
    public Map<String, String> getGroupNameByIds(String tenantId, String userId, List<String> groupIds) {
        List<QueryGroupByIds.UserGroupInfo> result = getGroupInfoByIds(tenantId, userId, groupIds);
        return result.stream().collect(Collectors.toMap(QueryGroupByIds.UserGroupInfo::getId, QueryGroupByIds.UserGroupInfo::getName, (x, y) -> y));
    }

    @Override
    public List<QueryGroupByIds.UserGroupInfo> getGroupInfoByIds(String tenantId, String userId, List<String> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Lists.newArrayList();
        }
        QueryGroupByIds.Arg arg = QueryGroupByIds.Arg.builder()
                .tenantId(tenantId)
                .userId(userId)
                .appId(DefObjConstants.PACKAGE_NAME_CRM)
                .isFilterByUser(false)
                .isPublic(true)
                .groupIdList(groupIds)
                .build();
        QueryGroupByIds.Result result = orgServiceProxy.queryGroupByIds(arg);
        if (!result.isSuccess()) {
            log.error("getGroupNameByIds error,arg:{},result={}", arg, result);
            return Lists.newArrayList();
        }
        return result.getResult();
    }

    @Override
    public List<QueryGroupByGroupName.UserGroupInfo> getGroupInfoByGroupName(String tenantId, String userId, String groupName, Integer status) {
        if (org.apache.commons.lang3.StringUtils.isBlank(groupName)) {
            groupName = null;
        }
        QueryGroupByGroupName.Arg arg = QueryGroupByGroupName.Arg
                .builder()
                .tenantId(tenantId)
                .userId(userId)
                .name(groupName)
                .appId(DefObjConstants.PACKAGE_NAME_CRM)
                .isFilterByUser(false)
                .isPublic(true)
                .status(status)
                .build();
        QueryGroupByGroupName.Result result = orgServiceProxy.queryGroupByGroupName(arg);
        if (!result.isSuccess()) {
            log.error("getGroupInfoByGroupName error,arg:{},result={}", arg, result);
            return Lists.newArrayList();
        }
        return result.getResult();
    }

    @Override
    public List<QueryGroupByGroupName.UserGroupInfo> getGroupInfoByGroupNames(String tenantId, String userId, List<String> groupNames, Integer status) {
        if (CollectionUtils.isEmpty(groupNames)) {
            return Lists.newArrayList();
        }
        QueryGroupByGroupName.Arg arg = QueryGroupByGroupName.Arg
                .builder()
                .tenantId(tenantId)
                .userId(userId)
                .nameList(groupNames)
                .appId(DefObjConstants.PACKAGE_NAME_CRM)
                .isFilterByUser(false)
                .isPublic(true)
                .status(status)
                .build();
        QueryGroupByGroupName.Result result = orgServiceProxy.queryGroupByGroupNames(arg);
        if (!result.isSuccess()) {
            log.error("getGroupInfoByGroupNames error,arg:{},result={}", arg, result);
            return Lists.newArrayList();
        }
        return result.getResult();
    }

    @Override
    public List<QueryTenantGroupByIds.TenantGroupInfo> getTenantGroupInfoByIds(String tenantId, String userId, List<String> tenantGroupIds) {
        if (CollectionUtils.isEmpty(tenantGroupIds)) {
            return Lists.newArrayList();
        }
        QueryTenantGroupByIds.Arg arg = QueryTenantGroupByIds.Arg.builder()
                .context(QueryTenantGroupByIds.TenantGroupContext.builder().tenantId(tenantId).userId(userId).build())
                .ids(tenantGroupIds)
                .build();
        QueryTenantGroupByIds.Result result = orgServiceProxy.queryTenantGroupByIds(arg);
        if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getResult())) {
            return Lists.newArrayList();
        }
        return result.getResult();
    }

    @Override
    public List<OutUserInfo> batchGetOutUsers(String tenantId, Set<String> outUserIds) {
        List<OrganizationEmployee> employees = outerOrganizationService.batchGetEmployee(tenantId, outUserIds);
        return employees.stream().map(emp ->
                        OutUserInfo.builder()
                                .id(emp.getEmployeeId())
                                .tenantId(emp.getEnterpriseId())
                                .name(emp.getName())
                                .profile(emp.getProfileImage())
                                .status(convertEmployeeEntityStatusToUserInfoStatus(EmployeeEntityStatus.valueOf(emp.getStatus())))
                                .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<DeptInfo> batchGetDeptInfosByDeptCodes(User user, List<String> deptCodes) {
        if (CollectionUtils.isEmpty(deptCodes)) {
            return Lists.newArrayList();
        }
        List<DeptInfo> deptInfos = Lists.newArrayList();
        BatchGetDepartmentByDeptCodes.Argument argument = new BatchGetDepartmentByDeptCodes.Argument();
        argument.setOperatorId(user.getUserId());
        argument.setTenantId(user.getTenantId());
        argument.setDeptCodes(deptCodes);
        List<com.facishare.social.department.model.DepartmentDto> departmentByDeptCodes =
                departmentObjService.getDepartmentByDeptCodes(argument);
        departmentByDeptCodes.forEach(departmentDto -> {
            DeptInfo deptInfo = DeptInfo.convertToDeptInfo(departmentDto);
            deptInfos.add(deptInfo);
        });
        return deptInfos;
    }

    @Override
    public List<DeptInfo> batchGetDeptInfosByDeptIds(User user, List<String> deptIds) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return Lists.newArrayList();
        }
        List<DeptInfo> deptInfos = Lists.newArrayList();
        BatchGetDepartmentByDeptIds.Argument argument = new BatchGetDepartmentByDeptIds.Argument();
        argument.setOperatorId(user.getUserId());
        argument.setTenantId(user.getTenantId());
        argument.setDeptIds(deptIds);
        List<com.facishare.social.department.model.DepartmentDto> departmentByDeptCodes =
                departmentObjService.getDepartmentByDeptIds(argument);
        departmentByDeptCodes.forEach(departmentDto -> {
            DeptInfo deptInfo = DeptInfo.convertToDeptInfo(departmentDto);
            if (deptInfo.disabled()) {
                deptInfo.setDeptName(I18N.text(I18NKey.DISABLED, deptInfo.getDeptName()));
            }
            deptInfos.add(deptInfo);
        });
        return deptInfos;
    }


    public List<QueryDeptInfoByDeptIds.DeptInfo> batchGetOutDeptInfosByDeptIds(User user, List<String> deptIds, QueryDeptInfoByDeptIds.DeptStatusEnum status) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return Lists.newArrayList();
        }
        List<ErDepartmentSimpleData> erDepartmentSimpleData = outerOrganizationService.batchGetOutDepartment(user, deptIds, status);
        List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = batchConvertErDepartmentDtoToDeptInfo(erDepartmentSimpleData);
        deptInfos.stream()
                .filter(QueryDeptInfoByDeptIds.DeptInfo::disabled)
                .forEach(deptInfo -> deptInfo.setDeptName(I18N.text(I18NKey.DISABLED, deptInfo.getDeptName())));
        return deptInfos;
    }

    @Override
    public OrganizationInfo findMainOrgAndDeptByUserId(String tenantId, String userId, Collection<String> userIds) {
        // 不查询 -10000 的员工信息
        Set<String> employeeIds = userIds.stream()
                .filter(id -> !User.SUPPER_ADMIN_USER_ID.equals(id))
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(employeeIds)) {
            return OrganizationInfo.empty();
        }
        // 查询员员工信息
        List<EmployeeDto> employeeList = employeeService.batchGetUserInfo(tenantId, employeeIds);
        // 获取所有员工的主属部门ID
        List<String> mainDeptIdList = employeeList.stream()
                .map(EmployeeDto::getMainDepartmentId)
                .filter(Objects::nonNull)
                .map(String::valueOf)
                .distinct()
                .collect(Collectors.toList());
        // 查询上级部门
        Map<String, List<DepartmentDto>> departmentMap = departmentService.batchGetUpperDepartment(tenantId, mainDeptIdList, true);

        List<OrganizationInfo.OrgInfo> orgInfos = employeeList.stream()
                .map(emp -> {
                    DeptInfo mainOrg = getMainOrg(departmentMap, emp);
                    DeptInfo mainDept = getMainDept(departmentMap, emp);
                    return OrganizationInfo.OrgInfo.builder()
                            .userId(mainOrg.getUserId())
                            .mainDept(mainDept)
                            .mainOrg(mainOrg)
                            .build();
                })
                .collect(Collectors.toList());
        return OrganizationInfo.of(orgInfos);
    }

    private DeptInfo getMainDept(Map<String, List<DepartmentDto>> departmentMap, EmployeeDto emp) {
        return getMainByRecordType(departmentMap, emp, DepartmentDto.RECORD_TYPE_DEPARTMENT);
    }

    private DeptInfo getMainOrg(Map<String, List<DepartmentDto>> departmentMap, EmployeeDto emp) {
        return getMainByRecordType(departmentMap, emp, DepartmentDto.RECORD_TYPE_ORGANIZATION);
    }

    private DeptInfo getMainByRecordType(Map<String, List<DepartmentDto>> departmentMap, EmployeeDto emp, String recordType) {
        DeptInfo mainOrg = new DeptInfo();
        mainOrg.setUserId(ConvertUtils.integerToString(emp.getEmployeeId()));
        mainOrg.setUserName(emp.getFullName());
        String mainDeptId = ConvertUtils.integerToString(emp.getMainDepartmentId());
        // 倒序取第一个组织就是主属组织/部门
        Lists.reverse(departmentMap.getOrDefault(mainDeptId, Collections.emptyList())).stream()
                .filter(it -> Objects.equals(recordType, it.getRecordType()))
                .findFirst()
                .ifPresent(mainOrganization -> {
                    mainOrg.setDeptId(ConvertUtils.integerToString(mainOrganization.getDepartmentId()));
                    mainOrg.setDeptName(getDeptName(mainOrganization));
                });
        return mainOrg;
    }

    /*
     * 将员工信息、部门信息、部门老大信息汇总转为MainDeptInfo
     */
    private List<QueryDeptInfoByUserIds.MainDeptInfo> convertAllInfoToMainDeptInfo(List<EmployeeDto> employeeList,
                                                                                   List<EmployeeDto> leaderInfoList,
                                                                                   List<DepartmentDto> departmentList) {
        List<QueryDeptInfoByUserIds.MainDeptInfo> result = Lists.newArrayList();
        // 根据人员信息创建MainDeptInfo
        for (EmployeeDto emp : employeeList) {
            QueryDeptInfoByUserIds.MainDeptInfo mainDeptInfo = new QueryDeptInfoByUserIds.MainDeptInfo();
            mainDeptInfo.setUserId(ConvertUtils.integerToString(emp.getEmployeeId()));
            mainDeptInfo.setUserName(emp.getFullName());
            Integer mainDeptId = emp.getMainDepartmentId();
            if (Objects.nonNull(mainDeptId)) {
                mainDeptInfo.setDeptId(ConvertUtils.integerToString(emp.getMainDepartmentId()));
            }
            // 填充部门信息
            boolean flag = false;
            for (DepartmentDto dept : departmentList) {
                if (flag) {
                    break;
                }
                if (ConvertUtils.integerToString(dept.getDepartmentId()).equals(mainDeptInfo.getDeptId())) {
                    mainDeptInfo.setDeptName(getDeptName(dept));
                    mainDeptInfo.setLeaderId(ConvertUtils.integerToString(dept.getPrincipalId()));
                    if (DepartmentDto.RECORD_TYPE_DEPARTMENT.equals(dept.getRecordType())) {
                        mainDeptInfo.setDeptType(QueryDeptInfoByUserIds.MainDeptInfo.TYPE_DEPT);
                    } else if (DepartmentDto.RECORD_TYPE_ORGANIZATION.equals(dept.getRecordType())) {
                        mainDeptInfo.setDeptType(QueryDeptInfoByUserIds.MainDeptInfo.TYPE_ORG);
                    }
                    // 填充部门老大名称
                    for (EmployeeDto leader : leaderInfoList) {
                        if (ConvertUtils.integerToString(leader.getEmployeeId()).equals(mainDeptInfo.getLeaderId())) {
                            mainDeptInfo.setLeaderName(leader.getFullName());
                            flag = true;
                            break;
                        }
                    }
                }
            }
            result.add(mainDeptInfo);
        }
        return result;
    }

    private boolean checkIsOutUser(String userId) {
        return StringUtils.length(userId) > 8;
    }

    /**
     * 区分内部人员和外部人员
     */
    private void splitUserIds(List<String> userIds, List<String> innerIds, List<String> outIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        for (Object id : userIds) {
            String value = String.valueOf(id);
            if (checkIsOutUser(value)) {
                outIds.add(value);
            } else {
                innerIds.add(value);
            }
        }
    }

    private List<QueryDeptByName.DeptInfo> batchConvertDepartmentDtoToDeptInfo2(List<DepartmentDto> departments) {
        List<QueryDeptByName.DeptInfo> result = Lists.newArrayList();
        for (DepartmentDto department : departments) {
            result.add(convertDepartmentDtoToDeptInfo2(department));
        }
        return result;
    }

    private QueryDeptByName.DeptInfo convertDepartmentDtoToDeptInfo2(DepartmentDto department) {
        QueryDeptByName.DeptInfo info = new QueryDeptByName.DeptInfo();
        info.setAncestors(ConvertUtils.batchConvertIntegerToString(department.getAncestors()));
        info.setCreateTime(department.getCreateTime());
        info.setModifyTime(department.getUpdateTime());
        info.setDescription(department.getDescription());
        info.setId(ConvertUtils.integerToString(department.getDepartmentId()));
        info.setManagerId(ConvertUtils.integerToString(department.getPrincipalId()));
        info.setName(getDeptName(department));
        info.setOrder(department.getDepartmentOrder());
        info.setParentId(department.parentId());
        info.setTenantId(ConvertUtils.integerToString(department.getEnterpriseId()));
        info.setStatus(convertDepartmentStatusToDeptStatus(department.getStatus()).getCode());
        if (Objects.equals(department.getRecordType(), DepartmentDto.RECORD_TYPE_DEPARTMENT)) {
            info.setDeptType(QueryDeptByName.TYPE_DEPT);
        } else if (Objects.equals(department.getRecordType(), DepartmentDto.RECORD_TYPE_ORGANIZATION)) {
            info.setDeptType(QueryDeptByName.TYPE_ORG);
        }
        return info;
    }


    /*
     * 批量将departmentDto转为DeptInfo
     */
    private List<QueryDeptInfoByDeptIds.DeptInfo> batchConvertDepartmentDtoToDeptInfo(List<DepartmentDto> department) {
        List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = Lists.newArrayList();
        for (DepartmentDto dept : department) {
            deptInfos.add(convertDepartmentDtoToDeptInfo(dept));
        }
        return deptInfos;
    }

    private QueryDeptInfoByDeptIds.DeptInfo convertDepartmentDtoToDeptInfo(DepartmentDto dept) {
        QueryDeptInfoByDeptIds.DeptInfo info = new QueryDeptInfoByDeptIds.DeptInfo();
        info.setDeptId(ConvertUtils.integerToString(dept.getDepartmentId()));
        info.setDeptName(getDeptName(dept));
        info.setLeaderUserId(ConvertUtils.integerToString(dept.getPrincipalId()));
        info.setParentId(ConvertUtils.integerToString(dept.parentId()));
        info.setStatus(convertDepartmentStatusToDeptStatus(dept.getStatus()).getCode());
        if (Objects.equals(dept.getRecordType(), DepartmentDto.RECORD_TYPE_DEPARTMENT)) {
            info.setDeptType(QueryDeptInfoByDeptIds.TYPE_DEPT);
        } else if (Objects.equals(dept.getRecordType(), DepartmentDto.RECORD_TYPE_ORGANIZATION)) {
            info.setDeptType(QueryDeptInfoByDeptIds.TYPE_ORG);
        }
        return info;
    }

    private String getDeptName(DepartmentDto dept) {
        DepartmentNameLanguage departmentName = dept.getDepartmentNameLanguage();
        if (Objects.isNull(departmentName)) {
            return dept.getName();
        }
        String language = I18N.getContext().getLanguage();
        String deptName;
        switch (language) {
            case I18N.EN:
                deptName = departmentName.getEn();
                break;
            case I18N.ZH_CN:
                deptName = departmentName.getZh_CN();
                break;
            case I18N.ZH_TW:
                deptName = departmentName.getZh_TW();
                break;
            default:
                deptName = dept.getName();
        }
        if (StringUtils.isBlank(deptName)) {
            return dept.getName();
        }
        return deptName;
    }

    private QueryDeptInfoByDeptIds.DeptStatusEnum convertDepartmentStatusToDeptStatus(DepartmentStatus status) {
        switch (status) {
            case NORMAL:
                return QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE;
            case STOP:
            case DELETE:
                return QueryDeptInfoByDeptIds.DeptStatusEnum.DISABLE;
        }
        return QueryDeptInfoByDeptIds.DeptStatusEnum.ALL;
    }

    /*
     * 批量转换EmployeeDto为UserInfo
     */
    private List<UserInfo> batchConvertEmployeeDtoToUserInfo(List<EmployeeDto> employeeDtoList) {
        List<UserInfo> userInfos = Lists.newArrayList();
        for (EmployeeDto employeeDto : employeeDtoList) {
            userInfos.add(convertEmployeeDtoToUserInfo(employeeDto));
        }
        return userInfos;
    }

    private List<UserInfo> batchConvertPersonnelDtoToUserInfo(List<PersonnelDto> personnelDtos) {
        List<UserInfo> userInfos = Lists.newArrayList();
        for (PersonnelDto personnelDto : personnelDtos) {
            userInfos.add(convertPersonnelDtoToUserInfo(personnelDto));
        }
        return userInfos;
    }

    private UserInfo convertPersonnelDtoToUserInfo(PersonnelDto personnelDto) {
        UserInfo userInfo = new UserInfo();
        userInfo.setId(personnelDto.getUserId());
        userInfo.setName(personnelDto.getName());
        userInfo.setNickname(personnelDto.getName());
        userInfo.setDept(ConvertUtils.integerToString(PersonnelDto.getMainDepartmentInt(personnelDto)));
        userInfo.setCreateTime(personnelDto.getCreateTime());
        userInfo.setDescription(personnelDto.getDescription());
        userInfo.setEmail(personnelDto.getEmail());
        userInfo.setPhone(personnelDto.getTelephone());
        userInfo.setMobile(personnelDto.getMobile());
        userInfo.setModifyTime(personnelDto.getLastModifiedTime());
        userInfo.setPost(personnelDto.getPost());
        userInfo.setTenantId(personnelDto.getTenantId());
        userInfo.setStatus(ConvertUtils.convertStringToInteger(personnelDto.getStatus()));
        userInfo.setSupervisorId(ConvertUtils.integerToString(PersonnelDto.getLeaderIdInt(personnelDto)));
        userInfo.setEmpNum(personnelDto.getEmpNum());
        return userInfo;
    }

    private UserInfo convertEmployeeDtoToUserInfo(EmployeeDto employeeDto) {
        UserInfo userInfo = new UserInfo();
        userInfo.setId(ConvertUtils.integerToString(employeeDto.getEmployeeId()));
        userInfo.setName(getEmployeeName(employeeDto));
        userInfo.setNickname(getEmployeeName(employeeDto));
        userInfo.setDept(ConvertUtils.integerToString(employeeDto.getMainDepartmentId()));
        userInfo.setCreateTime(employeeDto.getCreateTime());
        userInfo.setDescription(employeeDto.getDescription());
        userInfo.setEmail(employeeDto.getEmail());
        userInfo.setPhone(employeeDto.getTelephone());
        userInfo.setMobile(employeeDto.getMobile());
        userInfo.setModifyTime(employeeDto.getUpdateTime());
        userInfo.setPicAddr(employeeDto.getProfileImage());
        userInfo.setPost(employeeDto.getPost());
        userInfo.setTenantId(ConvertUtils.integerToString(employeeDto.getEnterpriseId()));
        userInfo.setStatus(convertEmployeeEntityStatusToUserInfoStatus(employeeDto.getStatus()));
        userInfo.setSupervisorId(ConvertUtils.integerToString(employeeDto.getLeaderId()));
        userInfo.setEmpNum(employeeDto.getEmpNum());
        return userInfo;
    }

    private String getEmployeeName(EmployeeDto employeeDto) {
        PersonnelNameLanguage nameLanguage = employeeDto.getNameLanguage();
        if (Objects.isNull(nameLanguage)) {
            return employeeDto.getName();
        }
        String language = I18N.getContext().getLanguage();
        String employeeName;
        switch (language) {
            case I18N.EN:
                employeeName = nameLanguage.getEn();
                break;
            case I18N.ZH_CN:
                employeeName = nameLanguage.getZhCn();
                break;
            case I18N.ZH_TW:
                employeeName = nameLanguage.getZhTw();
                break;
            default:
                employeeName = employeeDto.getName();
        }
        if (StringUtils.isBlank(employeeName)) {
            return employeeDto.getName();
        }
        return employeeName;
    }

    private Integer convertEmployeeEntityStatusToUserInfoStatus(EmployeeEntityStatus status) {
        if (status == null) {
            return 0;
        }
        switch (status) {
            case STOP:
            case DELETE:
                return 1;
            case NORMAL:
                return 0;
        }
        return 2;
    }

    private List<UserInfo> batchConvertOutEmployeeToUserInfo(List<OrganizationEmployee> outUsers) {
        List<UserInfo> userInfos = Lists.newArrayList();
        for (OrganizationEmployee outUser : outUsers) {
            userInfos.add(convertOutEmployeeToUserInfo(outUser));
        }
        return userInfos;
    }

    private UserInfo convertOutEmployeeToUserInfo(OrganizationEmployee outUser) {
        UserInfo userInfo = new UserInfo();
        userInfo.setId(outUser.getEmployeeId());
        userInfo.setName(outUser.getName());
        userInfo.setNickname(outUser.getName());
        userInfo.setTenantId(outUser.getEnterpriseId());
        userInfo.setEnterpriseName(outUser.getEnterpriseName());
        userInfo.setPicAddr(outUser.getProfileImage());
        userInfo.setDept(outUser.getMainDepartmentId());
        userInfo.setStatus(convertEmployeeEntityStatusToUserInfoStatus(EmployeeEntityStatus.valueOf(outUser.getStatus())));

        return userInfo;
    }
}
