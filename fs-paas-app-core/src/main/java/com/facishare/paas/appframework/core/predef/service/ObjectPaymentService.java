package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.PaymentException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.service.dto.payment.GetPaymentUrl;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.payment.PaymentService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.PaymentFieldDescribe;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Optional;

import static com.facishare.paas.metadata.api.describe.Payment.PAY_STATUS_COMPLETE;

/**
 * Created by liyiguang on 2017/10/11.
 */

@ServiceModule("payment")
@Component
public class ObjectPaymentService {
    @Autowired
    private ServiceFacade serviceFacade;

    @ServiceMethod("find_payment_list")
    public GetPaymentUrl.Result getPaymentUrl(GetPaymentUrl.Arg arg, ServiceContext context) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(context.getTenantId(), arg.getDescribeApiName());
        IObjectData data = serviceFacade.findObjectData(context.getTenantId(), arg.getObjectDataId(), objectDescribe);
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        Optional<PaymentFieldDescribe> paymentFieldDescribe = describeExt.getPaymentFieldDescribe();
        if (!paymentFieldDescribe.isPresent()) {
            return null;
        }

        //已支付检查
        String payStatus = String.valueOf(data.get(paymentFieldDescribe.get().getPayStatusFieldApiName()));
        if (PAY_STATUS_COMPLETE.equals(payStatus)) {
            throw new PaymentException(I18N.text(I18NKey.COMPLETED_PAYMENT));
        }
        Object o = data.get(paymentFieldDescribe.get().getPayAmountFieldApiName());
        if (o == null) {
            throw new PaymentException(I18N.text(I18NKey.COLLECTION_AMOUNT_IS_EMPTY));
        }

        BigDecimal bigDecimal = new BigDecimal(String.valueOf(o));
        long amount = bigDecimal.multiply(new BigDecimal(100)).longValue();
        if (amount <= 0) {
            throw new PaymentException(I18N.text(I18NKey.COLLECTION_AMOUNT_IS_ILLEGAL));
        }

        PaymentService.GetPaymentUrlArg theArg = PaymentService.GetPaymentUrlArg.builder()
                .amount(amount)
                .apiName(arg.getDescribeApiName())
                .dataId(arg.getObjectDataId())
                .orderName(data.getName())
                .build();
        String paymentUrl = serviceFacade.getPaymentUrl(theArg);
        return GetPaymentUrl.Result.builder().paymentUrl(paymentUrl).build();
    }

}
