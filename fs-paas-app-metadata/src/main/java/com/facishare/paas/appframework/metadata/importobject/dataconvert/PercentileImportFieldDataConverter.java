package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.PercentileExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.describe.PercentileFieldDescribe;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 百分数
 * create by <PERSON><PERSON><PERSON> on 2019/07/26
 */
@Component
public class PercentileImportFieldDataConverter extends BaseImportFieldDataConverter {
    @Override
    public List<String> getSupportedFieldTypes() {
        return Lists.newArrayList(IFieldType.PERCENTILE);
    }

    @Override
    public ConvertResult convertFieldData(IObjectData objectData, IFieldDescribe fieldDescribe, User user) {
        final PercentileExt descExt = PercentileExt.of((PercentileFieldDescribe) fieldDescribe);
        String valueStr = getStringValue(objectData, descExt.getApiName());
        if (StringUtils.isEmpty(valueStr)) {
            return ConvertResult.buildSuccess(null);
        } else {
            String validStr = valueStr.replace("%", "");
            if (!NumberUtils.isParsable(valueStr)) {
                //不能解析为数字，比如：1.
                return ConvertResult.buildError(I18NExt.text(I18NKey.FORMAT_ERROR_PLEASE_INPUT_PARSABLE,
                        fieldDescribe.getLabel()));
            }

            BigDecimal value = NumberUtils.createBigDecimal(validStr);
            final int scale = value.scale();
            final int precision = value.precision();

            if (descExt.isAutoAdaptPlaces()) {
                if (descExt.getDecimalPlaces() == 0
                        && (scale > 0 || NumberUtils.compare(precision - scale, descExt.getLength()) > 0)) {
                    //需要一个整数，但(不是一个整数 或者 整数位数超过了描述中可以设置的最大整数位数)
                    return ConvertResult.buildError(I18NExt.text(I18NKey.FORMAT_ERROR_PLEASE_INPUT_PERCENTILE_INTEGER,
                            descExt.getLabel(), descExt.getLength()));
                } else if (descExt.getDecimalPlaces() > 0
                        && (NumberUtils.compare(scale, descExt.getDecimalPlaces()) > 0 || NumberUtils.compare(precision - scale, descExt.getLength()) > 0)) {
                    //需要一个小数，但(小数位超过了描述可以设置的最大小数位数 或者 整数位数超过了描述中可以设置的最大整数位数)
                    return ConvertResult.buildError(I18NExt.text(I18NKey.FORMAT_ERROR_PLEASE_INPUT_PERCENTILE_DECIMAL,
                            descExt.getLabel(), descExt.getLength(), descExt.getDecimalPlaces()));
                }
                if (NumberUtils.compare(precision, descExt.getMaxLength()) > 0) {
                    //数字长度超过了总的位数
                    return ConvertResult.buildError(I18NExt.text(I18NKey.FORMAT_ERROR_PLEASE_INPUT_NUMBER_MAX_LENGTH,
                            descExt.getLabel()));
                }
            }

            return ConvertResult.buildSuccess(validStr);
        }
    }
}
