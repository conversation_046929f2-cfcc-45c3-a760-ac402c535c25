package com.facishare.paas.appframework.core.model

import com.facishare.crm.predefine.CRMPreDefineObject
import spock.lang.Specification

/**
 * Created by liyiguang on 2017/6/20.
 */
class PreDefineObjectRegistryTest extends Specification {

    def "test getPreDefineObject"() {
        when:
        CRMPreDefineObject.init()

        def predefineObject = PreDefineObjectRegistry.getPreDefineObject(apiName)
        def actionClassInfo = predefineObject.getDefaultActionClassInfo(actionCode)
        then:
        predefineObject == expectPredefineObject
        predefineObject.apiName == apiName
        actionClassInfo == expectActionClassInfo
        where:
        apiName      | actionCode || expectActionClassInfo                                       | expectPredefineObject
        "AccountObj" | "Add"      || CRMPreDefineObject.Account.getDefaultActionClassInfo("Add") | CRMPreDefineObject.Account
    }
}
