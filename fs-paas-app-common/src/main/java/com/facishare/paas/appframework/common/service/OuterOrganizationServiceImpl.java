package com.facishare.paas.appframework.common.service;

import com.facishare.converter.EIEAConverter;
import com.facishare.organization.adapter.api.model.organizationwithouter.BatchGetEmployee;
import com.facishare.organization.adapter.api.model.organizationwithouter.BatchGetResponsibleEmployee;
import com.facishare.organization.adapter.api.model.organizationwithouter.OrganizationEmployee;
import com.facishare.organization.adapter.api.service.OrganizationWithOuterService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.model.OuterDepartmentInfo;
import com.facishare.paas.appframework.common.service.dto.OuterEmployeeInfo;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.support.GDSHandler;
import com.fxiaoke.enterpriserelation2.BatchGetErDepartmentInfoByNameResult;
import com.fxiaoke.enterpriserelation2.arg.*;
import com.fxiaoke.enterpriserelation2.cache.data.SimpleOrgErDepartmentData;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.data.ErDepartmentSimpleData;
import com.fxiaoke.enterpriserelation2.data.PublicEmployeeObjOwnDepOrOrgData;
import com.fxiaoke.enterpriserelation2.erdepartment.ErDepartmentService;
import com.fxiaoke.enterpriserelation2.result.*;
import com.fxiaoke.enterpriserelation2.result.data.EmployeeSimpleData;
import com.fxiaoke.enterpriserelation2.result.data.RelationEmployeeIdInfoData;
import com.fxiaoke.enterpriserelation2.service.ErDepartmentRpcService;
import com.fxiaoke.enterpriserelation2.service.FxiaokeAccountService;
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeObjService;
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService;
import com.google.common.collect.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/12/26 4:00 下午
 */
@Service("outerOrganizationService")
@Slf4j
public class OuterOrganizationServiceImpl implements OuterOrganizationService {
    public static final int PAGE_SIZE = 500;
    @Autowired
    OrganizationWithOuterService organizationWithOuterService;
    @Autowired
    ErDepartmentService erDepartmentService;
    @Autowired
    PublicEmployeeService publicEmployeeService;
    @Autowired
    PublicEmployeeObjService publicEmployeeObjService;
    @Autowired
    FxiaokeAccountService fxiaokeAccountService;
    @Autowired
    GDSHandler gdsHandler;
    @Resource
    private EIEAConverter eieaConverter;
    @Autowired
    private ErDepartmentRpcService erDepartmentRpcService;

    @Override
    public List<OrganizationEmployee> batchGetEmployee(String tenantId, Collection<String> outUserIds) {
        if (CollectionUtils.empty(outUserIds)) {
            return Lists.newArrayList();
        }
        Set<String> outUserIdSet = outUserIds.stream().filter(NumberUtils::isDigits).collect(Collectors.toSet());
        if (CollectionUtils.empty(outUserIdSet)) {
            return Lists.newArrayList();
        }
        try {
            BatchGetEmployee.Arg arg = new BatchGetEmployee.Arg();
            arg.setEnterpriseId(tenantId);
            arg.setEmployeeIds(outUserIdSet);
            BatchGetEmployee.Result result = organizationWithOuterService.batchGetEmployee(arg);
            return CollectionUtils.nullToEmpty(result.getEmployees());
        } catch (Exception e) {
            log.error("batch get outer employee error, downstreamTenantId:{}, outUserIds:{}", tenantId, outUserIds, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<ErDepartmentSimpleData> batchGetOutDepartment(User user, Collection<String> outDepartmentIds, QueryDeptInfoByDeptIds.DeptStatusEnum status) {
        if (CollectionUtils.empty(outDepartmentIds)) {
            return Lists.newArrayList();
        }
        try {
            RestResult<List<ErDepartmentSimpleData>> result = erDepartmentService.batchGetErDepartmentSimpleDataByIds(user.getTenantIdInt(), outDepartmentIds, status.getCode());
            return CollectionUtils.nullToEmpty(result.getData());
        } catch (Exception e) {
            log.error("batch get out department error, downstreamTenantId:{}, outDepartmentIds:{}", user.getTenantId(), outDepartmentIds, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<OrganizationEmployee> batchGetResponsibleEmployee(String tenantId, String userId, Collection<String> userIds) {
        if (CollectionUtils.empty(userIds)) {
            return Lists.newArrayList();
        }
        try {
            BatchGetResponsibleEmployee.Arg arg = new BatchGetResponsibleEmployee.Arg();
            arg.setEmployeeIds(Lists.newArrayList(userIds));
            arg.setIncludeLowDepartment(false);
            arg.setCurrentEmployeeId(userId);
            arg.setEnterpriseId(tenantId);
            BatchGetResponsibleEmployee.Result result = organizationWithOuterService.batchGetResponsibleEmployee(arg);
            return CollectionUtils.nullToEmpty(result.getResponsibleEmployeeMap());
        } catch (Exception e) {
            log.error("batch get responsible employee, downstreamTenantId:{}, userIds;{}", tenantId, userIds, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询当前下游企业在上游企业的外部联系人
     *
     * @param tenantId    上游企业id
     * @param outTenantId 下游外部企业id
     * @return 查询到的上游对接人
     */
    @Override
    public List<User> getUpstreamPartnerContact(String tenantId, String outTenantId) {
        HeaderObj header = HeaderObj.newInstance(Integer.valueOf(tenantId));
        // 查询上游企业的外部企业Id
        GetOuterTenantIdByEaArg getOuterTenantIdByEaArg = new GetOuterTenantIdByEaArg();
        getOuterTenantIdByEaArg.setEa(gdsHandler.getEAByEI(tenantId));
        RestResult<Long> outerTenantId = fxiaokeAccountService.getOuterTenantIdByEa(header, getOuterTenantIdByEaArg);
        if (!outerTenantId.isSuccess()) {
            log.warn("getOuterTenantIdByEa fail, arg:{}, result:{}", JacksonUtils.toJson(getOuterTenantIdByEaArg), JacksonUtils.toJson(outerTenantId));
            throw new ValidateException(outerTenantId.getErrMsg());
        }
        // 查询当前下游企业在上游企业的外部联系人id（outerUid）
        SourceAndDestOuterTenantIdOutArg sourceAndDestOuterTenantIdOutArg = new SourceAndDestOuterTenantIdOutArg();
        sourceAndDestOuterTenantIdOutArg.setSourceOuterTenantId(outerTenantId.getData());
        sourceAndDestOuterTenantIdOutArg.setDestOuterTenantId(Long.valueOf(outTenantId));
        sourceAndDestOuterTenantIdOutArg.setUpstreamTenantId(Integer.valueOf(tenantId));
        RestResult<List<PubliceEmployeeDataResult>> publicEmployees = publicEmployeeService.listPublicEmployeesByDestOuterTenantId(header, sourceAndDestOuterTenantIdOutArg);
        if (!publicEmployees.isSuccess()) {
            log.warn("listPublicEmployeesByDestOuterTenantId fail, arg:{}, result:{}",
                    JacksonUtils.toJson(sourceAndDestOuterTenantIdOutArg), JacksonUtils.toJson(publicEmployees));
            throw new ValidateException(publicEmployees.getErrMsg());
        }
        // 将外部人员 outerUid 转换为内部人员id
        List<Long> outerIds = publicEmployees.getData().stream()
                .map(PubliceEmployeeDataResult::getOuterUid)
                .collect(Collectors.toList());
        BatchGetFsAccountByOuterUidsArg arg = new BatchGetFsAccountByOuterUidsArg();
        arg.setOuterUids(outerIds);
        RestResult<BatchGetFsAccountByOuterUidsResult> result = fxiaokeAccountService.batchGetFsAccountByOuterUids(header, arg);
        if (!result.isSuccess()) {
            log.warn("batchGetFsAccountByOuterUids fail, arg:{}, result:{}", JacksonUtils.toJson(arg), JacksonUtils.toJson(result));
            throw new ValidateException(result.getErrMsg());
        }
        return result.getData().getOuterUidFsAccountMap().values().stream()
                .filter(it -> Objects.nonNull(it.getEmployeeId()))
                .map(it -> new User(tenantId, it.getEmployeeId().toString()))
                .collect(Collectors.toList());
    }

    @Override
    public Optional<User> getOwnerOutUserByOutTenant(User user, String outTenantId) {
        HeaderObj header = HeaderObj.newInstance(Integer.valueOf(user.getTenantId()));
        GetOutUidArg arg = new GetOutUidArg();
        arg.setUpstreamTenantId(user.getTenantIdInt());
        arg.setDownstreamOuterTenantId(Long.valueOf(outTenantId));
        RestResult<Long> result = publicEmployeeService.getDownstreamRelationOwnerOuterUid(header, arg);
        if (!result.isSuccess()) {
            log.error("getDownstreamRelationOwnerOuterUid fail, ei:{}, outTenantId:{}", user.getTenantId(), outTenantId);
            return Optional.empty();
        }
        return Optional.ofNullable(result.getData())
                .map(id -> User.builder()
                        .outTenantId(outTenantId)
                        .outUserId(String.valueOf(id))
                        .build());
    }

    /**
     * 批量查询下游企业的对接人信息
     */
    public List<User> batchGetOutUsersByOutTenants(User user, List<String> outTenantIds) {
        List<User> outUserInfos = Lists.newArrayList();
        if (CollectionUtils.empty(outTenantIds)) {
            return outUserInfos;
        }
        HeaderObj header = HeaderObj.newInstance(Integer.valueOf(user.getTenantId()));
        ListDownstreamEmployeesByDownstreamOuterTenantIdsArg body = new ListDownstreamEmployeesByDownstreamOuterTenantIdsArg();
        List<Long> downstreamOuterTenantIds = outTenantIds.stream().map(Long::valueOf).collect(Collectors.toList());
        body.setUpstreamEa(gdsHandler.getEAByEI(user.getTenantId()));
        body.setDownstreamOuterTenantIds(downstreamOuterTenantIds);
        body.setLimit(500);
        List<ListDownstreamEmployeesByDownstreamOuterTenantIdsResult> downstreamEmployees = Lists.newArrayList();
        int outerOrganizationMaxPageSize = AppFrameworkConfig.getOuterOrganizationMaxPageSize();
        for (int i = 0; i < outerOrganizationMaxPageSize; i++) {
            body.setOffset(i);
            RestResult<List<ListDownstreamEmployeesByDownstreamOuterTenantIdsResult>> restResult = publicEmployeeService.listDownstreamEmployeesByDownstreamOuterTenantIds(header, body);
            if (!restResult.isSuccess()) {
                throw new ValidateException(I18N.text(I18NKey.GET_DOWNSTREAM_TENANT_ERROR, restResult.getErrMsg()));
            }
            if (Objects.nonNull(restResult.getData())) {
                downstreamEmployees.addAll(restResult.getData());
            }
        }
        downstreamEmployees.stream().filter(Objects::nonNull).forEach(x -> outUserInfos.add(User.builder()
                .outTenantId(String.valueOf(x.getOuterTenantId()))
                .outUserId(String.valueOf(x.getOuterUid()))
                .build()));
        return outUserInfos;
    }

    @Override
    public List<UserInfo> getOutUsersByName(User user, String describeApiName, List<Map<String, String>> employeeData) {
        List<UserInfo> userInfos = Lists.newArrayList();
        HeaderObj header = HeaderObj.newInstance(Integer.valueOf(user.getTenantId()));
        List<MapperIdAndEmployeeNameData> employeeNameData = Lists.newArrayList();
        for (Map<String, String> data : employeeData) {
            MapperIdAndEmployeeNameData outEmployee = new MapperIdAndEmployeeNameData();
            outEmployee.setMapperId(data.get("partner_id"));
            outEmployee.setEmployeeName(data.get(DBRecord.OUT_OWNER));
            employeeNameData.add(outEmployee);
        }
        BatchGetDownstreamOuterUidsByMapperIdAndEmployeeNameArg arg = new BatchGetDownstreamOuterUidsByMapperIdAndEmployeeNameArg();
        arg.setUpstreamTenantId(user.getTenantIdInt());
        arg.setMapperApiName(describeApiName);
        arg.setMapperIdAndEmployeeNameDatas(employeeNameData);
        RestResult<List<BatchGetDownstreamOuterUidsByMapperIdAndEmployeeNameResult>> result = publicEmployeeService.batchGetDownstreamOuterUidsByMapperIdAndEmployeeName(header, arg);
        if (CollectionUtils.empty(result.getData())) {
            return userInfos;
        }
        result.getData().forEach(x -> {
            if (CollectionUtils.empty(x.getDownstreamOuterUids())) {
                return;
            }
            userInfos.add(UserInfo.builder()
                    .id(String.valueOf(x.getDownstreamOuterUids().get(0)))
                    .name(x.getEmployeeName())
                    .nickname(x.getEmployeeName())
                    .build()

            );
        });
        return userInfos;
    }

    @Override
    public List<OuterAccountVo> listOutersByUpstreamTenantId(User user) {
        ListDownstreamOuterUidsArg arg = new ListDownstreamOuterUidsArg();
        arg.setUpstreamEi(user.getTenantIdInt());
        arg.setIdentityType(1);
        arg.setLimit(500);
        HeaderObj header = HeaderObj.newInstance(Integer.valueOf(user.getTenantId()));
        int outerOrganizationMaxPageSize = AppFrameworkConfig.getOuterOrganizationMaxPageSize();
        List<OuterAccountVo> outerAccountVos = Lists.newArrayList();
        for (int i = 0; i < outerOrganizationMaxPageSize; i++) {
            arg.setOffset(i);
            RestResult<List<OuterAccountVo>> restResult = publicEmployeeService.listDownstreamOuterUids(header, arg);
            if (!restResult.isSuccess()) {
                log.error("listDownstreamOuterUids error, header:{}, arg:{}, result:{}", header, arg, restResult);
                throw new ValidateException(restResult.getErrMsg());
            }
            if (Objects.nonNull(restResult.getData())) {
                outerAccountVos.addAll(restResult.getData());
            }
        }
        return outerAccountVos;
    }

    @Override
    public Map<String, List<OuterAccountVo>> listOuterOwnerByNames(User user, Collection<String> names) {
        if (CollectionUtils.empty(names)) {
            return Maps.newHashMap();
        }

        HeaderObj header = HeaderObj.newInstance(Integer.valueOf(user.getTenantId()));

        ListDownstreamOuterUserInfoArg arg = new ListDownstreamOuterUserInfoArg();
        arg.setUpstreamTenantId(Integer.valueOf(user.getTenantId()));
        arg.setDownstreamEmployeeNames(Sets.newHashSet(names));

        RestResult<Map<String, List<OuterAccountVo>>> restResult = publicEmployeeService.listPulbicEmployeeByNames(header, arg);

        if (!restResult.isSuccess()) {
            log.error("listPulbicEmployeeByNames error, header:{}, arg:{}, result:{}", header, arg, restResult);
            throw new ValidateException(restResult.getErrMsg());
        }

        return restResult.getData();
    }

    @Override
    public Map<String, Boolean> isOuterUsersByTenantId(User user, Collection<String> outerUserIds) {
        if (CollectionUtils.empty(outerUserIds)) {
            return Maps.newHashMap();
        }
        Set<Long> outUserIds = outerUserIds.stream()
                .filter(NumberUtils::isCreatable)
                .map(Long::valueOf)
                .collect(Collectors.toSet());
        if (CollectionUtils.empty(outUserIds)) {
            return Maps.newHashMap();
        }

        HeaderObj header = HeaderObj.newInstance(user.getTenantIdInt());
        BatchIsPublicEmployeesArg arg = new BatchIsPublicEmployeesArg();
        arg.setUpstreamTenantId(user.getTenantIdInt());
        arg.setDownstreamOuterUids(outUserIds);

        RestResult<Map<Long, Boolean>> restResult = publicEmployeeService.batchIsPublicEmployees(header, arg);

        if (!restResult.isSuccess()) {
            log.error("batchIsPublicEmployees error, header:{}, arg:{}, result:{}", header, arg, restResult);
            throw new ValidateException(restResult.getErrMsg());
        }

        Map<String, Boolean> resultMap = Maps.newHashMap();

        restResult.getData().forEach((userId, checkResult) -> {
            resultMap.put(String.valueOf(userId), checkResult);
        });
        return resultMap;
    }

    @Override
    public List<PublicEmployeeObjOwnDepOrOrgData> batchGetOuterOwnDepOrOrg(User user, List<OuterAccountVo> outerAccountVos) {
        if (CollectionUtils.empty(outerAccountVos)) {
            return Lists.newArrayList();
        }
        RestResult<List<PublicEmployeeObjOwnDepOrOrgData>> listRestResult = erDepartmentService.batchGetPublicEmployeeObjOwnDepOrOrg(Integer.valueOf(user.getTenantId()), outerAccountVos);
        return listRestResult.getData();
    }

    public Long getOuterTenantIdByEI(User user, String tenantId) {
        GetOuterTenantIdByEaArg arg = new GetOuterTenantIdByEaArg();
        String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
        arg.setEa(ea);
        HeaderObj header = HeaderObj.newInstance(user.getTenantIdInt());
        RestResult<Long> restResult = fxiaokeAccountService.getOuterTenantIdByEa(header, arg);
        if (!restResult.isSuccess()) {
            log.warn("getOuterTenantIdByEi fail! ei:{}, downstreamTenantId:{}, msg:{}", user.getTenantId(), tenantId, restResult.getErrMsg());
            throw new ValidateException(restResult.getErrMsg());
        }
        return restResult.getData();
    }

    @Override
    public String getEAByOuterTenantId(User user, Long outerTenantId) {
        HeaderObj header = HeaderObj.newInstance(user.getTenantIdInt());
        GetEaByOuterTenantIdArg arg = new GetEaByOuterTenantIdArg();
        arg.setOuterTenantId(outerTenantId);
        RestResult<String> restResult = fxiaokeAccountService.getEaByOuterTenantId(header, arg);
        if (!restResult.isSuccess()) {
            log.warn("getEaByOuterTenantId fail! ei:{}, outerTenantId:{}, msg:{}", user.getTenantId(), outerTenantId, restResult.getErrMsg());
            throw new ValidateException(restResult.getErrMsg());
        }
        return restResult.getData();
    }

    @Override
    public Map<String, String> batchGetOuterTenantIdByEI(User user, Collection<String> eis) {
        List<Integer> ids = eis.stream()
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        BiMap<Integer, String> id2EAMap = HashBiMap.create(eieaConverter.enterpriseIdToAccount(ids));

        HeaderObj header = HeaderObj.newInstance(user.getTenantIdInt());
        BatchGetOuterTenantIdByEaArg arg = new BatchGetOuterTenantIdByEaArg();
        arg.setEas(Lists.newArrayList(id2EAMap.values()));
        RestResult<BatchGetOuterTenantIdByEaResult> restResult = fxiaokeAccountService.batchGetOuterTenantIdByEa(header, arg);
        if (!restResult.isSuccess()) {
            log.warn("getEaByOuterTenantId fail! ei:{}, eis:{}, msg:{}", user.getTenantId(), eis, restResult.getErrMsg());
            throw new ValidateException(restResult.getErrMsg());
        }
        Map<String, String> resultMap = Maps.newHashMap();
        Map<String, Long> ea2OuterTenantIdMap = Optional.ofNullable(restResult.getData())
                .map(BatchGetOuterTenantIdByEaResult::getEa2OuterTenantIdMap)
                .orElse(Collections.emptyMap());

        BiMap<String, Integer> inverse = id2EAMap.inverse();
        ea2OuterTenantIdMap.forEach((ea, outTenantId) -> Optional.ofNullable(inverse.get(ea))
                .ifPresent(id -> resultMap.put(String.valueOf(id), String.valueOf(outTenantId))));

        return ImmutableMap.copyOf(resultMap);
    }

    @Override
    public List<EmployeeSimpleData> batchGetRelationOwnerByOutTenant(User user, String downstreamTenantId, Integer queryType) {
        EmployeeQueryConditionArg arg = new EmployeeQueryConditionArg();
        arg.setSrcOuterTenantId(getOuterTenantIdByEI(user, user.getTenantId()));
        arg.setDestOuterTenantId(getOuterTenantIdByEI(user, downstreamTenantId));
        arg.setNeedFsAccountInfo(true);
        arg.setQueryType(queryType);
        arg.setPageSize(PAGE_SIZE);
        HeaderObj header = HeaderObj.newInstance(user.getTenantIdInt());
        int pageNumber = 1;
        int count;
        List<EmployeeSimpleData> result = Lists.newArrayList();
        do {
            arg.setPageNumber(pageNumber++);
            RestResult<PublicEmployeePageResult> restResult = publicEmployeeService.queryPublicEmployees(header, arg);
            if (!restResult.isSuccess()) {
                log.warn("batchGetRelationOwnerByOutTenant fail! ei:{}, downstreamTenantId:{}, queryType:{}, msg:{}",
                        user.getTenantId(), downstreamTenantId, queryType, restResult.getErrMsg());
                throw new ValidateException(restResult.getErrMsg());
            }
            List<EmployeeSimpleData> employeeSimpleDataList = Optional.ofNullable(restResult.getData())
                    .map(PublicEmployeePageResult::getEmployeeSimpleVos)
                    .orElse(Collections.emptyList());
            count = employeeSimpleDataList.size();
            result.addAll(employeeSimpleDataList);
        } while (count >= PAGE_SIZE);

        return result.stream()
                .filter(EmployeeSimpleData::getDataManager)
                .collect(Collectors.toList());
    }


    @Override
    public List<RelationEmployeeIdInfoData> batchGetOutUserInfoByEnterpriseAndNames(User user, Collection<String> names) {
        if (CollectionUtils.empty(names)) {
            return Lists.newArrayList();
        }

        HeaderObj header = HeaderObj.newInstance(Integer.valueOf(user.getTenantId()));

        BatchGetPublicEmployeeIdInfoArg arg = new BatchGetPublicEmployeeIdInfoArg();
        arg.setFsUserId(user.getUserId());
        arg.setNames(Lists.newArrayList(names));
        arg.setUpstreamEi(user.getTenantIdInt());

        RestResult<ListDownstreamEmployeeIdInfoResult> restResult = publicEmployeeObjService.batchGetPublicEmployeeIdInfoByName(header, arg);

        if (Objects.isNull(restResult) || Objects.isNull(restResult.getData())) {
            log.warn("batchGetPublicEmployeeIdInfoByName empty, header:{}, arg:{}", header, arg);
            return Lists.newArrayList();
        }

        if (!restResult.isSuccess()) {
            log.error("batchGetPublicEmployeeIdInfoByName error, header:{}, arg:{}, result:{}", header, arg, restResult);
            throw new ValidateException(restResult.getErrMsg());
        }

        return restResult.getData().getEmployeeIdInfos();
    }

    @Override
    public Map<String, OuterDepartmentInfo> findErDepartmentIdsByDeptIds(User user, Collection<String> deptIds, QueryDeptInfoByDeptIds.DeptStatusEnum status) {
        if (CollectionUtils.empty(deptIds)) {
            return Maps.newHashMap();
        }
        RestResult<Map<String, SimpleOrgErDepartmentData>> restResult = erDepartmentService.findOuterOrgDepartmentIdByInnerDepartmentId(user.getTenantIdInt(), Lists.newArrayList(deptIds), status.getCode());
        if (Objects.isNull(restResult)) {
            log.warn("findOuterOrgDepartmentIdByInnerDepartmentId empty, header:{}, arg:{}", user.getTenantIdInt(), deptIds);
            return Maps.newHashMap();
        }
        if (!restResult.isSuccess()) {
            log.error("findOuterOrgDepartmentIdByInnerDepartmentId error, header:{}, arg:{}, result:{}", user.getTenantIdInt(), deptIds, restResult);
            throw new ValidateException(restResult.getErrMsg());
        }
        Map<String, SimpleOrgErDepartmentData> data = restResult.getData();
        if (CollectionUtils.empty(data)) {
            return Maps.newHashMap();
        }
        Map<String, OuterDepartmentInfo> resultMap = Maps.newHashMap();
        data.forEach((key, value) -> resultMap.put(key, convertToOuterDepartmentInfo(value)));
        return resultMap;
    }

    @Override
    public List<OuterEmployeeInfo> batchGetOutEmployeeInfoByName(User user, List<OuterEmployeeInfo> nameInfos, Integer status) {
        if (CollectionUtils.empty(nameInfos)) {
            return Lists.newArrayList();
        }

        HeaderObj header = HeaderObj.newInstance(user.getTenantIdInt());
        BatchGetOutUserInfoByNameArg arg = new BatchGetOutUserInfoByNameArg();
        arg.setTenantId(user.getTenantId());
        List<BatchGetOutUserInfoByNameArg.Param> params = Lists.newArrayList();
        for (OuterEmployeeInfo nameInfo : nameInfos) {
            BatchGetOutUserInfoByNameArg.Param param = new BatchGetOutUserInfoByNameArg.Param();
            param.setOuterUserName(nameInfo.getOuterUserName());
            param.setOuterTenantName(nameInfo.getOuterTenantName());
            params.add(param);
        }
        arg.setParams(params);
        arg.setType(status);

        RestResult<BatchGetOutUserInfoByNameResult> restResult = publicEmployeeObjService.batchGetOutUserInfoByName(header, arg);

        if (!restResult.isSuccess()) {
            log.error("Failed to batch query external employee information, header:{}, arg:{}, result:{}", header, arg, restResult);
            throw new ValidateException(I18N.text(I18NKey.GET_OUTER_EMPLOYEE_ERROR, restResult.getErrMsg()));
        }

        return Optional.ofNullable(restResult.getData())
                .map(BatchGetOutUserInfoByNameResult::getOuterUserInfoList)
                .map(list -> list.stream()
                        .map(this::convertToOuterEmployeeInfo)
                        .collect(Collectors.toList()))
                .orElse(Lists.newArrayList());
    }

    private OuterEmployeeInfo convertToOuterEmployeeInfo(BatchGetOutUserInfoByNameResult.OuterUserInfo outerUserInfo) {
        try {
            return OuterEmployeeInfo.builder()
                    .outerUserId(String.valueOf(outerUserInfo.getOuterUserId()))
                    .outerUserName(outerUserInfo.getOuterEmployeeName())
                    .outerTenantId(String.valueOf(outerUserInfo.getOuterTenantId()))
                    .outerTenantName(outerUserInfo.getOuterTenantName())
                    .status(outerUserInfo.getType())
                    .build();
        } catch (Exception e) {
            log.error("Failed to convert external employee information", e);
            throw new ValidateException(I18N.text(I18NKey.DATA_CONVERT_ERROR));
        }
    }

    /**
     * 批量根据外部部门名称查询外部部门信息
     *
     * @param user            当前用户
     * @param departmentInfos 部门信息列表
     * @param status          部门状态
     * @return 外部部门信息列表
     */
    @Override
    public List<OuterDepartmentInfo> batchGetOutDepartmentInfoByName(User user, List<OuterDepartmentInfo> departmentInfos, Integer status) {
        if (CollectionUtils.empty(departmentInfos)) {
            return Lists.newArrayList();
        }

        HeaderObj header = HeaderObj.newInstance(user.getTenantIdInt());
        BatchGetErDepartmentInfoByNameArg arg = new BatchGetErDepartmentInfoByNameArg();
        List<BatchGetErDepartmentInfoByNameArg.Param> params = Lists.newArrayList();
        for (OuterDepartmentInfo departmentInfo : departmentInfos) {
            BatchGetErDepartmentInfoByNameArg.Param param = new BatchGetErDepartmentInfoByNameArg.Param();
            param.setOuterDepartmentName(departmentInfo.getOuterDepartmentName());
            param.setOuterTenantName(departmentInfo.getOuterTenantName());
            params.add(param);
        }
        arg.setParams(params);
        arg.setStatus(status);

        RestResult<BatchGetErDepartmentInfoByNameResult> restResult = erDepartmentRpcService.batchGetErDepartmentInfoByNames(header, arg);

        if (!restResult.isSuccess()) {
            log.error("Failed to batch query external department information, header:{}, arg:{}, result:{}", header, arg, restResult);
            throw new ValidateException(I18N.text(I18NKey.GET_OUTER_DEPARTMENT_ERROR, restResult.getErrMsg()));
        }

        return Optional.ofNullable(restResult.getData())
                .map(BatchGetErDepartmentInfoByNameResult::getOuterErDeptInfoList)
                .map(list -> list.stream()
                        .map(this::convertToOuterDepartmentInfo)
                        .collect(Collectors.toList()))
                .orElse(Lists.newArrayList());
    }

    private OuterDepartmentInfo convertToOuterDepartmentInfo(BatchGetErDepartmentInfoByNameResult.OuterErDeptInfo deptInfo) {
        try {
            return OuterDepartmentInfo.builder()
                    .outerDepartmentId(String.valueOf(deptInfo.getOuterDepartmentId()))
                    .outerDepartmentName(deptInfo.getOuterDepartmentName())
                    .outerTenantId(String.valueOf(deptInfo.getOuterTenantId()))
                    .outerTenantName(deptInfo.getOuterTenantName())
                    .status(deptInfo.getStatus())
                    .build();
        } catch (Exception e) {
            log.error("Failed to convert external department information", e);
            throw new ValidateException(I18N.text(I18NKey.DATA_CONVERT_ERROR));
        }
    }

    private OuterDepartmentInfo convertToOuterDepartmentInfo(SimpleOrgErDepartmentData deptInfo) {
        try {
            return OuterDepartmentInfo.builder()
                    .outerDepartmentId(deptInfo.getErDeptId())
                    .outerTenantId(String.valueOf(deptInfo.getOuterTenantId()))
                    .status(deptInfo.getStatus())
                    .build();
        } catch (Exception e) {
            log.error("Failed to convert external department information", e);
            throw new ValidateException(I18N.text(I18NKey.DATA_CONVERT_ERROR));
        }
    }
}
