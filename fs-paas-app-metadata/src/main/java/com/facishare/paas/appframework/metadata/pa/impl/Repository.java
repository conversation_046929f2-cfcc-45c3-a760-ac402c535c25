package com.facishare.paas.appframework.metadata.pa.impl;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataServiceImpl;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.repository.annotation.Entity;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.mapping.Mapper;
import com.facishare.paas.appframework.metadata.repository.model.BaseEntity;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 使用注解形式完成ObjectData的CRUD操作,
 * 以后考虑可以支持ObjectDescribe的Create
 * <p>
 * Created by zhenglei on 16/8/4.
 */
@Slf4j
@Component
public class Repository<T extends BaseEntity> implements IRepository<T> {

    private static final Set<String> NOT_SUPPORT_UPDATE_FIELDS = ImmutableSet.of(IObjectData.CREATE_TIME, IObjectData.CREATED_BY,
            IObjectData.LAST_MODIFIED_TIME, IObjectData.LAST_MODIFIED_BY, IObjectData.VERSION, IObjectData.IS_DELETED);

    private static final Set<String> NOT_SUPPORT_UPDATE_FIELDS_EXCLUDE_TIME = ImmutableSet.of(IObjectData.CREATED_BY,
            IObjectData.LAST_MODIFIED_BY, IObjectData.VERSION, IObjectData.IS_DELETED);

    @Autowired
    private MetaDataServiceImpl metaDataService;

    @Override
    public T create(User user, T entity) {
        entity.setTenantId(user.getTenantId());
        //从领域模型对象中收集数据保存到ObjectData中
        IObjectData data = entity.convertTo();
        //调用服务,保存数据
        IObjectData saved = metaDataService.saveObjectData(user, data);
        //将保存后的数据同步到领域模型对象中
        return entity.convert(saved);
    }

    @Override
    public T update(User user, T entity) {
        entity.setTenantId(user.getTenantId());
        //从领域模型对象中收集数据保存到ObjectData中
        IObjectData data = entity.convertTo();
        ObjectDataExt.of(data).remove(NOT_SUPPORT_UPDATE_FIELDS);
        //调用服务,保存数据
        IObjectData saved = metaDataService.updateObjectData(user, data);
        //将保存后的数据同步到领域模型对象中
        return entity.convert(saved);
    }

    @Override
    public List<T> bulkCreate(User user, List<T> entityList) {
        if (CollectionUtils.empty(entityList)) {
            return Lists.newArrayList();
        }

        //获取ObjectDescribe的API name
        List<IObjectData> dataList = convertToIObjectData(user, entityList);

        //调用服务,保存数据
        List<IObjectData> savedObjectDataList = metaDataService.bulkSaveObjectData(dataList, user);

        //将保存后的数据同步到领域模型对象中
        return batchDataToEntityList(savedObjectDataList, entityList.get(0));
    }

    @Override
    public List<T> bulkUpdate(User user, List<T> entityList) {
        if (CollectionUtils.empty(entityList)) {
            return Lists.newArrayList();
        }
        //获取ObjectDescribe的API name
        List<IObjectData> dataList = convertToIObjectData(user, entityList);
        dataList.forEach(x -> ObjectDataExt.of(x).remove(NOT_SUPPORT_UPDATE_FIELDS));
        List<IObjectData> updatedDataList = metaDataService.batchUpdate(dataList, user);
        return batchDataToEntityList(updatedDataList, entityList.get(0));
    }

    @Override
    public List<T> bulkUpdate(IActionContext context, User user, List<T> entityList) {
        if (CollectionUtils.empty(entityList)) {
            return Lists.newArrayList();
        }
        //获取ObjectDescribe的API name
        List<IObjectData> dataList = convertToIObjectData(user, entityList);
        if (Objects.equals(context.get("specify_time"), Boolean.TRUE)) {
            dataList.forEach(x -> ObjectDataExt.of(x).remove(NOT_SUPPORT_UPDATE_FIELDS_EXCLUDE_TIME));
        } else {
            dataList.forEach(x -> ObjectDataExt.of(x).remove(NOT_SUPPORT_UPDATE_FIELDS));
        }
        List<IObjectData> updatedDataList = metaDataService.batchUpdate(context, dataList, user);
        return batchDataToEntityList(updatedDataList, entityList.get(0));
    }

    @Override
    public List<T> bulkUpdateByFields(User user, List<T> entityList, List<String> bulkUpdateByFields) {
       return bulkUpdateByFields(user, entityList, dataList -> {}, bulkUpdateByFields);
    }

    @Override
    public List<T> bulkUpdateExceptIgnoreFields(User user, List<T> entityList, Set<String> ignoreFields) {
        if (CollectionUtils.empty(entityList)) {
            return Lists.newArrayList();
        }
        T entity = entityList.get(0);
        // 黑名单转白名单
        Set<String> allFieldApis = entity.fetchAllFieldApis();
        allFieldApis.removeIf(BaseEntity.noModifiedFiles::contains);
        allFieldApis.removeIf(x -> CollectionUtils.nullToEmpty(ignoreFields).contains(x));

        return bulkUpdateByFields(user, entityList, Lists.newArrayList(allFieldApis));
    }

    @Override
    public List<T> bulkUpdateByFields(User user, List<T> entityList, Consumer<List<IObjectData>> consumer, List<String> updateFieldList) {
        if (CollectionUtils.empty(entityList) || CollectionUtils.empty(updateFieldList)) {
            return Lists.newArrayList();
        }
        //获取ObjectDescribe的API name
        List<IObjectData> dataList = convertToIObjectData(user, entityList);
        dataList.forEach(x -> ObjectDataExt.of(x).remove(NOT_SUPPORT_UPDATE_FIELDS));
        consumer.accept(dataList);
        List<IObjectData> updatedDataList = metaDataService.batchUpdateByFields(user, dataList, updateFieldList);
        return batchDataToEntityList(updatedDataList, entityList.get(0));
    }

    @Override
    public List<T> bulkUpsert(User user, List<T> entityList) {
        if (CollectionUtils.empty(entityList)) {
            return Lists.newArrayList();
        }

        List<IObjectData> dataList = convertToIObjectData(user, entityList);

        // 根据 id upsert
        List<IObjectData> savedObjectDataList = metaDataService.bulkUpsertObjectData(dataList, user);
        //将保存后的数据同步到领域模型对象中

        return batchDataToEntityList(savedObjectDataList, entityList.get(0));
    }

    @Override
    public void bulkInvalidAndDelete(User user, List<T> entityList) {
        if (CollectionUtils.empty(entityList)) {
            return;
        }

        List<IObjectData> dataList = convertToIObjectData(user, entityList);

        // 直接删除
        metaDataService.bulkInvalidAndDeleteWithSuperPrivilege(dataList, user);
    }

    private List<IObjectData> convertToIObjectData(User user, List<T> entityList) {
        return entityList.stream()
                .map(BaseEntity::convertTo)
                .peek(x -> x.setTenantId(user.getTenantId()))
                .collect(Collectors.toList());
    }

    private List<T> batchDataToEntityList(List<IObjectData> dataList, T entity) {
        List<T> result = Lists.newArrayList();
        for (IObjectData data : dataList) {
            T convert = entity.convert(data);
            result.add(convert);
        }
        return result;
    }

    private List<T> batchDataToEntityList(Class<T> clazz, List<IObjectData> dataList) {
        List<T> result = Lists.newArrayList();
        Mapper mapper = new Mapper();
        for (IObjectData objectData : dataList) {
            T entity = mapper.fromObjectData(objectData, clazz);
            result.add(entity);
        }
        return result;
    }

    @NotNull
    private Entity getEntityAnnotation(Class<? extends BaseEntity> entityClass) {
        Entity entityAnnotation = entityClass.getAnnotation(Entity.class);
        if (Objects.isNull(entityAnnotation)) {
            throw new IllegalArgumentException("Not use @entity annotation");
        }
        return entityAnnotation;
    }

    @Override
    public void queryDataAndHandle(User user, Query query, int dataBatchSize, int maxQueryCount, Class<T> objectType, Consumer<List<T>> consumer) {
        Entity entityAnnotation = getEntityAnnotation(objectType);
        SearchTemplateQuery searchTemplateQuery = (SearchTemplateQuery) query.toSearchTemplateQuery();
        metaDataService.queryDataAndHandle(user, searchTemplateQuery, entityAnnotation.apiName(), dataBatchSize, maxQueryCount, true,
                queryResult -> {
                    List<T> entityList = batchDataToEntityList(objectType, queryResult.getData());
                    // 处理查询到的数据
                    consumer.accept(entityList);
                });
    }

    @Override
    public QueryResult<T> findByQuery(User user, Query query, Class<T> objectType) {
        Entity entityAnnotation = getEntityAnnotation(objectType);
        SearchTemplateQuery searchTemplateQuery = (SearchTemplateQuery) query.toSearchTemplateQuery();
        QueryResult<IObjectData> queryResult = metaDataService.findBySearchQuery(user, entityAnnotation.apiName(), searchTemplateQuery);
        List<T> entityList = batchDataToEntityList(objectType, queryResult.getData());
        QueryResult<T> result = new QueryResult<>();
        result.setTotalNumber(queryResult.getTotalNumber());
        result.setData(entityList);
        return result;
    }

    @Override
    public List<T> findBy(User user, Query query, Class<T> objectType) {
        Entity entityAnnotation = getEntityAnnotation(objectType);
        SearchTemplateQuery searchTemplateQuery = (SearchTemplateQuery) query.toSearchTemplateQuery();
        QueryResult<IObjectData> queryResult = metaDataService.findBySearchQuery(user, entityAnnotation.apiName(), searchTemplateQuery);
        return batchDataToEntityList(objectType, queryResult.getData());
    }


    @Override
    public Integer findCountOnly(User user, Query query, Class<T> objectType) {
        Entity entityAnnotation = getEntityAnnotation(objectType);
        SearchTemplateQuery searchTemplateQuery = (SearchTemplateQuery) query.toSearchTemplateQuery();
        searchTemplateQuery.setLimit(1);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setFindExplicitTotalNum(true);
        QueryResult<IObjectData> queryResult = metaDataService.findBySearchQuery(user, entityAnnotation.apiName(), searchTemplateQuery);
        return queryResult.getTotalNumber();
    }

    @Override
    public List<T> bulkInvalid(User user, List<T> entityList) {
        if (CollectionUtils.empty(entityList)) {
            return Lists.newArrayList();
        }

        List<IObjectData> dataList = convertToIObjectData(user, entityList);
        //批量刪除数据
        List<IObjectData> bulkDeleteDataList = metaDataService.bulkInvalid(dataList, user);

        return batchDataToEntityList(bulkDeleteDataList, entityList.get(0));
    }

    @Override
    public List<T> bulkRecover(User user, List<T> entityList) {
        if (CollectionUtils.empty(entityList)) {
            return Lists.newArrayList();
        }

        List<IObjectData> dataList = convertToIObjectData(user, entityList);
        //批量刪除数据
        List<IObjectData> bulkRecoverDataList = metaDataService.bulkRecover(dataList, user);

        return batchDataToEntityList(bulkRecoverDataList, entityList.get(0));
    }

    @Override
    public List<T> bulkDelete(User user, List<T> entityList) {
        if (CollectionUtils.empty(entityList)) {
            return Lists.newArrayList();
        }

        List<IObjectData> dataList = convertToIObjectData(user, entityList);
        //批量刪除数据
        List<IObjectData> bulkDeleteDataList = metaDataService.bulkDeleteDirect(dataList, user);

        return batchDataToEntityList(bulkDeleteDataList, entityList.get(0));
    }

    @Override
    public void bulkDeleteWithInternalDescribe(User user, List<T> entityList) {
        if (CollectionUtils.empty(entityList)) {
            return;
        }

        List<IObjectData> dataList = convertToIObjectData(user, entityList);
        //批量刪除数据
         metaDataService.bulkDeleteWithInternalDescribe(dataList, user);
    }
}
