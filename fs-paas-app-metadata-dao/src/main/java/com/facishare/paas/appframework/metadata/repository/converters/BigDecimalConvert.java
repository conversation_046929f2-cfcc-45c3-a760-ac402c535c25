package com.facishare.paas.appframework.metadata.repository.converters;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/04/20
 */
public class BigDecimalConvert implements Converter<BigDecimal> {

    private static final BigDecimalConvert BIG_DECIMAL_CONVERT = new BigDecimalConvert();

    public static ConverterFactory getConverterFactory() {
        return Converters.newFactory(BigDecimal.class, getConvert());
    }

    private static BigDecimalConvert getConvert() {
        return BIG_DECIMAL_CONVERT;
    }

    private BigDecimalConvert() {
    }

    @Override
    public BigDecimal convert(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof BigInteger) {
            return new BigDecimal((BigInteger) value);
        }
        if (value instanceof Double) {
            return BigDecimal.valueOf((Double) value);
        }
        if (value instanceof Long) {
            return new BigDecimal((Long) value);
        }
        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }
        return new BigDecimal(value.toString());
    }
}
