package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by zhouwr on 2022/9/1.
 */
public interface FindDescribe {
    @Data
    class Arg {
        private String describeApiName;
        private Boolean includeDetails;
        private String sourceInfo;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Result {
        private ObjectDescribeDocument describe;
        private List<ObjectDescribeDocument> details;
        private ManageGroupDTO manageGroup;
    }
}
