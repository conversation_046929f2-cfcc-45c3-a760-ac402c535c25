package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * FiltersComponentExt单元测试类
 */
@ExtendWith(MockitoExtension.class)
class FiltersComponentExtTest {

  @Mock
  private IComponent mockComponent;

  private FiltersComponentExt filtersComponentExt;

  @BeforeEach
  void setUp() {
    when(mockComponent.getType()).thenReturn(FiltersComponentExt.COMPONENT_TYPE_FILTERS);
    filtersComponentExt = FiltersComponentExt.of(mockComponent);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试of方法正常创建FiltersComponentExt对象
   */
  @Test
  @DisplayName("测试of方法正常创建对象")
  void testOfMethodSuccess() {
    IComponent component = mock(IComponent.class);
    when(component.getType()).thenReturn(FiltersComponentExt.COMPONENT_TYPE_FILTERS);
    
    FiltersComponentExt result = FiltersComponentExt.of(component);
    
    assertNotNull(result);
    assertEquals(component, result.getComponent());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试of方法当component类型不匹配时抛出异常
   */
  @Test
  @DisplayName("测试of方法 - component类型不匹配时抛出异常")
  void testOfMethodThrowsExceptionWhenTypeNotMatch() {
    IComponent component = mock(IComponent.class);
    when(component.getType()).thenReturn("wrong_type");
    
    IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
      FiltersComponentExt.of(component);
    });
    
    assertEquals("type error!", exception.getMessage());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFilters方法当filters为空时返回空列表
   */
  @Test
  @DisplayName("测试getFilters方法 - filters为空时返回空列表")
  void testGetFiltersWhenEmpty() {
    when(mockComponent.get("filters")).thenReturn(null);

    List<FiltersComponentExt.BiFilter> result = filtersComponentExt.getFilters();

    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFilters方法当filters不为空时返回BiFilter列表
   */
  @Test
  @DisplayName("测试getFilters方法 - filters不为空时返回BiFilter列表")
  void testGetFiltersWhenNotEmpty() {
    // 准备测试数据
    Map<String, Object> filterMap1 = new HashMap<>();
    filterMap1.put("filterType", "selector");
    filterMap1.put("filterData", "data1");
    
    Map<String, Object> filterMap2 = new HashMap<>();
    filterMap2.put("filterType", "date");
    filterMap2.put("filterData", "data2");
    
    List<Map> filtersList = Lists.newArrayList(filterMap1, filterMap2);
    when(mockComponent.get("filters")).thenReturn(filtersList);
    
    // 执行测试
    List<FiltersComponentExt.BiFilter> result = filtersComponentExt.getFilters();
    
    // 验证结果
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals("selector", result.get(0).getFilterType());
    assertEquals("data1", result.get(0).getFilterData());
    assertEquals("date", result.get(1).getFilterType());
    assertEquals("data2", result.get(1).getFilterData());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试buildFilters静态方法当filters为空时返回空列表
   */
  @Test
  @DisplayName("测试buildFilters静态方法 - filters为空时返回空列表")
  void testBuildFiltersWhenEmpty() {
    List<FiltersComponentExt.BiFilter> result = FiltersComponentExt.buildFilters(null);

    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试buildFilters静态方法当filters不为空时返回BiFilter列表
   */
  @Test
  @DisplayName("测试buildFilters静态方法 - filters不为空时返回BiFilter列表")
  void testBuildFiltersWhenNotEmpty() {
    try (MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class)) {
      // 准备测试数据
      Map<String, Object> filterMap = new HashMap<>();
      filterMap.put("filterType", "selector");
      filterMap.put("filterData", "testData");
      
      List<Map> filtersList = Lists.newArrayList(filterMap);
      mockedCollectionUtils.when(() -> CollectionUtils.empty(filtersList)).thenReturn(false);
      
      // 执行测试
      List<FiltersComponentExt.BiFilter> result = FiltersComponentExt.buildFilters(filtersList);
      
      // 验证结果
      assertNotNull(result);
      assertEquals(1, result.size());
      assertEquals("selector", result.get(0).getFilterType());
      assertEquals("testData", result.get(0).getFilterData());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFilter方法找到匹配的filter
   */
  @Test
  @DisplayName("测试getFilter方法 - 找到匹配的filter")
  void testGetFilterFound() {
    // 准备测试数据
    Map<String, Object> filterMap1 = new HashMap<>();
    filterMap1.put("filterType", "selector");
    filterMap1.put("filterData", "data1");
    
    Map<String, Object> filterMap2 = new HashMap<>();
    filterMap2.put("filterType", "date");
    filterMap2.put("filterData", "data2");
    
    List<Map> filtersList = Lists.newArrayList(filterMap1, filterMap2);
    when(mockComponent.get("filters")).thenReturn(filtersList);
    
    // 执行测试
    Optional<FiltersComponentExt.BiFilter> result = filtersComponentExt.getFilter("selector");
    
    // 验证结果
    assertTrue(result.isPresent());
    assertEquals("selector", result.get().getFilterType());
    assertEquals("data1", result.get().getFilterData());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFilter方法未找到匹配的filter
   */
  @Test
  @DisplayName("测试getFilter方法 - 未找到匹配的filter")
  void testGetFilterNotFound() {
    // 准备测试数据
    Map<String, Object> filterMap = new HashMap<>();
    filterMap.put("filterType", "selector");
    filterMap.put("filterData", "data");
    
    List<Map> filtersList = Lists.newArrayList(filterMap);
    when(mockComponent.get("filters")).thenReturn(filtersList);
    
    // 执行测试
    Optional<FiltersComponentExt.BiFilter> result = filtersComponentExt.getFilter("notexist");
    
    // 验证结果
    assertFalse(result.isPresent());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试updateSelectorFilter方法当filter不是List时不执行更新
   */
  @Test
  @DisplayName("测试updateSelectorFilter方法 - filter不是List时不执行更新")
  void testUpdateSelectorFilterWhenNotList() {
    // 执行测试
    filtersComponentExt.updateSelectorFilter("not a list");
    
    // 验证没有异常抛出，方法正常返回
    assertNotNull(filtersComponentExt);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试updateSelectorFilter方法当没有selector类型的filter时不执行更新
   */
  @Test
  @DisplayName("测试updateSelectorFilter方法 - 没有selector类型的filter时不执行更新")
  void testUpdateSelectorFilterWhenNoSelectorFilter() {
    // 准备测试数据 - 没有selector类型的filter
    Map<String, Object> filterMap = new HashMap<>();
    filterMap.put("filterType", "date");
    filterMap.put("filterData", "dateData");
    
    List<Map> newFiltersList = Lists.newArrayList(filterMap);
    
    // Mock现有的filters为空 - 移除不必要的stubbing
    
    // 执行测试
    filtersComponentExt.updateSelectorFilter(newFiltersList);
    
    // 验证没有异常抛出
    assertNotNull(filtersComponentExt);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试BiFilter的默认构造函数
   */
  @Test
  @DisplayName("测试BiFilter默认构造函数")
  void testBiFilterDefaultConstructor() {
    FiltersComponentExt.BiFilter biFilter = new FiltersComponentExt.BiFilter();
    assertNotNull(biFilter);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试BiFilter的Map构造函数
   */
  @Test
  @DisplayName("测试BiFilter的Map构造函数")
  void testBiFilterMapConstructor() {
    Map<String, Object> map = new HashMap<>();
    map.put("filterType", "selector");
    map.put("filterData", "testData");
    
    FiltersComponentExt.BiFilter biFilter = new FiltersComponentExt.BiFilter(map);
    
    assertNotNull(biFilter);
    assertEquals("selector", biFilter.getFilterType());
    assertEquals("testData", biFilter.getFilterData());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试BiFilter的getter和setter方法
   */
  @Test
  @DisplayName("测试BiFilter的getter和setter方法")
  void testBiFilterGetterAndSetter() {
    FiltersComponentExt.BiFilter biFilter = new FiltersComponentExt.BiFilter();
    
    biFilter.setFilterType("date");
    biFilter.setFilterData("dateData");
    
    assertEquals("date", biFilter.getFilterType());
    assertEquals("dateData", biFilter.getFilterData());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试BiFilter的needMergeFilterType方法
   */
  @Test
  @DisplayName("测试BiFilter的needMergeFilterType方法")
  void testBiFilterNeedMergeFilterType() {
    FiltersComponentExt.BiFilter biFilter = new FiltersComponentExt.BiFilter();
    
    // 测试selector类型
    biFilter.setFilterType(FiltersComponentExt.BiFilter.FILTER_TYPE_SELECTOR);
    assertTrue(biFilter.needMergeFilterType());
    
    // 测试date类型
    biFilter.setFilterType(FiltersComponentExt.BiFilter.FILTER_TYPE_DATE);
    assertTrue(biFilter.needMergeFilterType());
    
    // 测试其他类型
    biFilter.setFilterType("other");
    assertFalse(biFilter.needMergeFilterType());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试BiFilter的mergeFrom方法
   */
  @Test
  @DisplayName("测试BiFilter的mergeFrom方法")
  void testBiFilterMergeFrom() {
    FiltersComponentExt.BiFilter biFilter1 = new FiltersComponentExt.BiFilter();
    biFilter1.setFilterData("originalData");
    
    FiltersComponentExt.BiFilter biFilter2 = new FiltersComponentExt.BiFilter();
    biFilter2.setFilterData("newData");
    
    // 执行合并
    biFilter1.mergeFrom(biFilter2);
    
    // 验证结果
    assertEquals("newData", biFilter1.getFilterData());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试BiFilter的mergeFrom方法当参数为null时不执行合并
   */
  @Test
  @DisplayName("测试BiFilter的mergeFrom方法 - 参数为null时不执行合并")
  void testBiFilterMergeFromWhenNull() {
    FiltersComponentExt.BiFilter biFilter = new FiltersComponentExt.BiFilter();
    biFilter.setFilterData("originalData");
    
    // 执行合并
    biFilter.mergeFrom(null);
    
    // 验证结果 - 数据应该没有变化
    assertEquals("originalData", biFilter.getFilterData());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试常量值的正确性
   */
  @Test
  @DisplayName("测试常量值")
  void testConstants() {
    assertEquals("filters", FiltersComponentExt.COMPONENT_TYPE_FILTERS);
    assertEquals("filterType", FiltersComponentExt.BiFilter.FILTER_TYPE);
    assertEquals("filterData", FiltersComponentExt.BiFilter.FILTER_DATA);
    assertEquals("selector", FiltersComponentExt.BiFilter.FILTER_TYPE_SELECTOR);
    assertEquals("date", FiltersComponentExt.BiFilter.FILTER_TYPE_DATE);
    assertEquals("pageDefault", FiltersComponentExt.BiFilter.FILTER_TYPE_PAGED_EFAULT);
  }
}
