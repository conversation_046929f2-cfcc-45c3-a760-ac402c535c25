package com.facishare.paas.appframework.metadata.multicurrency;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.appframework.metadata.MtExchangeRate;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.dto.QueryExchangeRate;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Component
@Slf4j
public class ExchangeRateService {
    public static final String MT_EXCHANGE_RATE_API_NAME = "MtExchangeRateObj";

    @Autowired
    private MetaDataService metaDataService;

    public QueryResult<IObjectData> queryExchangeRateData(User user, QueryExchangeRate.Arg arg) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        int offset = SearchTemplateQueryExt.calculateOffset(arg.getPageNumber(), arg.getPageSize());
        searchTemplateQuery.setLimit(arg.getPageSize());
        searchTemplateQuery.setOffset(offset);
        SearchTemplateQueryExt templateQueryExt = SearchTemplateQueryExt.of(searchTemplateQuery);
        templateQueryExt.addFilter(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId());
        if (StringUtils.isNotBlank(arg.getCurrencyCode())) {
            templateQueryExt.addFilter(Operator.EQ, MtExchangeRate.CURRENCY_CODE, arg.getCurrencyCode());
        }
        if (arg.getStartTime() != null) {
            templateQueryExt.addFilter(Operator.GTE, IObjectData.CREATE_TIME, arg.getStartTime().toString());
        }
        if (arg.getEndTime() != null) {
            templateQueryExt.addFilter(Operator.LT, IObjectData.CREATE_TIME, arg.getEndTime().toString());
        }
        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName(IObjectData.CREATE_TIME);
        orderBy.setIsAsc(false);
        searchTemplateQuery.setOrders(Lists.newArrayList(orderBy));
        return metaDataService.findBySearchQuery(user, MT_EXCHANGE_RATE_API_NAME, searchTemplateQuery);
    }

    public List<MtExchangeRate> batchInsertExchangeRate(User user, List<MtExchangeRate> exchangeRateList) {
        List<MtExchangeRate> exchangeRateResult = Lists.newArrayList();
        List<IObjectData> objectDataList = Lists.newArrayList();
        exchangeRateList.forEach(x -> objectDataList.add(MtExchangeRate.buildExchangeRate(x, user)));
        List<IObjectData> insertResult = metaDataService.bulkSaveObjectData(objectDataList, user);
        insertResult.forEach(x -> exchangeRateResult.add(MtExchangeRate.buildMtExchangeRate(x)));
        return exchangeRateResult;
    }
}
