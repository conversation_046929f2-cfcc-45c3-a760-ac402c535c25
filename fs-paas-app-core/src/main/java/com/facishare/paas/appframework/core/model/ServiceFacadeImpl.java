
package com.facishare.paas.appframework.core.model;


import com.facishare.netdisk.api.model.type.V5FileInfo;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.coordination.FeedService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.flow.ApprovalFlowService;
import com.facishare.paas.appframework.flow.StageThrusterService;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.ActionFilterResult;
import com.facishare.paas.appframework.metadata.plugin.FunctionPluginConfLogicService;
import com.facishare.paas.appframework.payment.PaymentService;
import com.facishare.paas.appframework.privilege.FieldPrivilegeService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.support.GDSHandler;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 服务门面代理类实现
 * <p>
 * Created by liyiguang on 2017/6/20.
 */
@Service("serviceFacade")
@Slf4j
public class ServiceFacadeImpl implements ServiceFacade, ApplicationContextAware {

    private Map<Class<?>, Object> serviceMap = Maps.newHashMap();

    @Autowired
    private FileStoreService fileStoreService;

    @Autowired
    @Delegate
    private MetaDataService metaDataService;

    @Autowired
    @Delegate
    private CustomButtonService customButtonService;

    @Autowired
    @Delegate
    private CustomSceneService customSceneService;

    @Autowired
    @Delegate
    private FunctionPrivilegeService functionPrivilegeService;

    @Autowired
    @Delegate
    private LogService logService;

    @Autowired
    @Delegate
    private ApprovalFlowService approvalFlowService;

    @Autowired
    @Delegate
    private LicenseService licenseService;

    @Autowired
    @Delegate
    private RecordTypeLogicService recordTypeLogicService;

    @Autowired
    @Delegate
    private UserRoleInfoService userRoleInfoService;

    @Autowired
    @Delegate
    private OrgService orgService;

    @Autowired
    @Delegate
    private FeedService feedService;

    @Autowired
    @Delegate
    private PaymentService paymentService;

    @Getter
    @Autowired
    private LayoutLogicService layoutLogicService;

    @Getter
    @Resource(name = "functionPluginConfLogicService")
    private FunctionPluginConfLogicService functionPluginConfLogicService;

    @Autowired
    @Delegate
    private DescribeLogicService describeLogicService;

    @Autowired
    @Delegate
    private ButtonLogicService buttonLogicService;

    @Autowired
    private ActionLocateService actionLocateService;

    @Autowired
    private ControllerLocateService controllerLocateService;

    @Autowired
    @Delegate
    private GDSHandler gdsHandler;

    @Autowired
    @Delegate
    private FieldPrivilegeService fieldPrivilegeService;

    @Autowired
    @Delegate
    private DuplicatedSearchService duplicatedSearchService;

    private ApplicationContext applicationContext;

    @Autowired
    @Delegate
    private StageThrusterService stageThrusterService;

    @Autowired
    @Delegate
    private UniqueRuleLogicService uniqueRuleLogicService;

    @Getter
    @Autowired
    private FunctionLogicService functionLogicService;

    @Autowired
    @Delegate
    private ObjectDataFlowService objectDataFlowService;

    @Autowired
    @Delegate
    private DuplicatedSearchDataService duplicatedSearchDataService;

    @Getter
    @Autowired
    private MultiCurrencyLogicService multiCurrencyLogicService;

    @Delegate
    @Autowired
    private MetaDataGlobalService metaDataGlobalService;

    @Delegate
    @Autowired
    private ConfigService configService;

    @Delegate
    @Autowired
    private DefObjLifeStatusService defObjLifeStatusService;

    @Autowired
    private ExportTaskHookService exportTaskHookService;

    @Override
    public ExportTaskHookService getExportTaskHookService() {
        return exportTaskHookService;
    }

    @Override
    public <T> void registerService(Class<T> clazz, T service) {
        serviceMap.put(clazz, service);
    }

    @Override
    public <T> T getService(Class<T> clazz) {
        return (T) serviceMap.get(clazz);
    }

    @Override
    public <A, T> T triggerAction(ActionContext actionContext, A a, Class<T> resultType) {
        Object oldTriggerInvoke = actionContext.getAttribute(RequestContext.Attributes.TRIGGER_INVOKE);
        try {
            actionContext.setAttribute(RequestContext.Attributes.TRIGGER_INVOKE, true);
            Action<A, T> action = actionLocateService.locateAction(actionContext, a, resultType);
            return action.act(a);
        } finally {
            actionContext.setAttribute(RequestContext.Attributes.TRIGGER_INVOKE, oldTriggerInvoke);
        }
    }

    @Override
    public <A, T> T triggerRemoteAction(ActionContext actionContext, A a, Class<T> resultType) {
        Object oldTriggerInvoke = actionContext.getAttribute(RequestContext.Attributes.TRIGGER_INVOKE);
        try {
            actionContext.setAttribute(RequestContext.Attributes.TRIGGER_INVOKE, true);
            Action<A, T> action = actionLocateService.locateRemoteAction(actionContext, a, resultType);
            return action.act(a);
        } finally {
            actionContext.setAttribute(RequestContext.Attributes.TRIGGER_INVOKE, oldTriggerInvoke);
        }
    }

    @Override
    public <A, T> T triggerController(ControllerContext controllerContext, A a, Class<T> resultType) {
        Object oldTriggerInvoke = controllerContext.getAttribute(RequestContext.Attributes.TRIGGER_INVOKE);
        try {
            controllerContext.setAttribute(RequestContext.Attributes.TRIGGER_INVOKE, true);
            Controller<A, T> controller = controllerLocateService.locateController(controllerContext, a, resultType);
            return controller.service(a);
        } finally {
            controllerContext.setAttribute(RequestContext.Attributes.TRIGGER_INVOKE, oldTriggerInvoke);
        }
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public <T> T getBean(Class<T> clazz) {
        try {
            return applicationContext.getBean(clazz);
        } catch (NoSuchBeanDefinitionException e) {
            log.warn("cannot find bean of type:{}", clazz.getName());
            return null;
        }
    }

    /**
     * 根据objectData的生命状态和锁定状态来判断数据是否是否拥有某操作
     *
     * @param objectData
     * @param apiName
     */
    @Override
    public void checkActionByLockStatusAndLifeStatus(IObjectData objectData, ObjectAction actionEnum, User user, String apiName, boolean isDetailObjCheckMode) {
        checkActionByLockStatusAndLifeStatus(Lists.newArrayList(objectData), actionEnum, user, apiName, isDetailObjCheckMode);
    }

    /**
     * 根据objectData的生命状态和锁定状态来判断数据是否是否拥有某操作
     *
     * @param objectDataList
     * @param apiName
     */
    @Override
    public void checkActionByLockStatusAndLifeStatus(List<IObjectData> objectDataList, ObjectAction actionEnum, User user, String apiName, boolean isDetailObjCheckMode) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }

        ActionFilterResult actionFilterResult = validateActionByLockStatusAndLifeStatus(objectDataList, actionEnum, user, apiName, isDetailObjCheckMode);
        if (CollectionUtils.empty(actionFilterResult.getActionList())) {
            throw new ValidateException(I18N.text(I18NKey.UNSUPPORT_OPERATION_REVERSE, actionFilterResult.getFilterDescription(), actionEnum.getActionLabel()));
        }
    }

    @Override
    public ActionFilterResult validateActionByLockStatusAndLifeStatus(List<IObjectData> objectDataList, ObjectAction actionEnum, User user, String apiName, boolean isDetailObjCheckMode) {
        if (CollectionUtils.empty(objectDataList)) {
            return null;
        }
        List<IObjectData> dataListToReFind = Lists.newArrayList();
        List<IObjectData> finalDataList = Lists.newArrayList();
        //如果不是新建的时候,如果没有生命周期字段,那么就先查找一下
        for (IObjectData objectData : objectDataList) {
            ObjectDataExt dataExt = ObjectDataExt.of(objectData);
            if (!dataExt.toMap().containsKey(UdobjConstants.LIFE_STATUS_API_NAME)
                    || !dataExt.toMap().containsKey(UdobjConstants.LOCK_STATUS_API_NAME)) {
                dataListToReFind.add(objectData);
            } else {
                finalDataList.add(objectData);
            }
        }
        List<IObjectData> objectDataByIds = findObjectDataByIds(user.getTenantId(),
                dataListToReFind.stream().map(it -> it.getId()).collect(Collectors.toList()), apiName);
        finalDataList.addAll(objectDataByIds);

        return filterActionsWithLockStatusAndLifeStatus(user, apiName, finalDataList, Lists.newArrayList(actionEnum.getActionCode()), isDetailObjCheckMode);
    }

    @Override
    public void getNPathsWithoutPermission(String ea, String userId, List<V5FileInfo> files) {
        fileStoreService.getNPathsWithoutPermission(ea, userId, files);
    }

    @Override
    public List<FileStoreService.PathPair> saveImageFromTempFilesAndNames(String tenantId, String userId, List<FileStoreService.PathOriginNames> pathOriginNames, String fileExt) {
        return fileStoreService.saveImageFromTempFilesAndNames(tenantId, userId, pathOriginNames, fileExt);
    }

}
