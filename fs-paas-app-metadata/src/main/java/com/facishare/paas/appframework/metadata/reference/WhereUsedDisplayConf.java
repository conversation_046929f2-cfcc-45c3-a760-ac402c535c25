package com.facishare.paas.appframework.metadata.reference;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface WhereUsedDisplayConf {

    @Data
    class Conf {
        @JsonProperty("conf_items")
        private List<ConfItem> confItems;
    }

    @Data
    class ConfItem {
        // 使用者类型
        @JsonProperty("invoker_type")
        private String invokerType;
        // 多语配置
        @JsonProperty("i18n_conf")
        private I18nConf i18nConf;
        // displayName : i18n_conf : {}
        @JsonProperty("entity")
        private Map<String, EntityConf> entity;
    }

    @Data
    class I18nConf {
        // 多语 key
        @JsonProperty("key")
        private String key;
        // 多语不存在默认显示内容
        @JsonProperty("default_val")
        private String defaultVal;
        // 参数多语
        @JsonProperty("param_key_i18n")
        private List<String> paramKeyI18n;
        // 是否显示 API Name 到 实体内容中
        // 与 api_to_param_index 相关
        @JsonProperty("api_wrapper_label")
        private boolean apiWrapperLabel;
        // API Name 与 param 绑定顺序
        @JsonProperty("api_to_param_index")
        private List<Integer> apiToParamIndex;
    }

    @Data
    class EntityConf {
        @JsonProperty("i18n_conf")
        private I18nConf i18nConf;
    }

    @Builder
    @Data
    class MenuConf {
        @JsonProperty("labelI18n")
        private I18nConf labelI18n;
        @JsonProperty("invoker_type")
        private String invokerType;
    }
}
