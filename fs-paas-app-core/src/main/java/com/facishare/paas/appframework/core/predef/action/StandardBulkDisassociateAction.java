package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.ObjectAction;

/**
 * Created by liyi<PERSON>ng on 2017/10/23.
 */
// TODO: 2017/10/24 qiuying
public class StandardBulkDisassociateAction extends BaseObjectAssociateAction {

    protected ObjectAction getStandardAction() {
        return ObjectAction.BULK_DISRELATE;
    }

    @Override
    protected void doStandardAssociate(Arg arg) {
//        serviceFacade.bulkDisassociate(arg.getAssociateObjId(), associateDescribe, arg.getAssociatedObjIds(), objectDescribe,
//                arg.getAssociatedObjRelatedListName(), actionContext.getUser());

        serviceFacade.bulkDisassociate(arg.getAssociateObjId(), associateDescribe, objectDescribe,
                actionContext.getUser(), associatedDataList, associateFieldApiName, updateFieldApiNames);
    }
}
