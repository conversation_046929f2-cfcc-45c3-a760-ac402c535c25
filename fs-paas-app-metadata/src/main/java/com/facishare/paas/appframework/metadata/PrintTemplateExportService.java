package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.service.dto.GetPrintTemplate;
import com.facishare.paas.appframework.common.service.dto.PrintTemplate;
import com.facishare.paas.appframework.core.model.User;

/**
 * <AUTHOR>
 * @Description 打印模板服务
 * @date 2021/9/22-22:16
 */
public interface PrintTemplateExportService {
    /**
     * 打印
     *
     * @param user 用户
     * @param arg  参数
     * @return {@link PrintTemplate.Result}
     */
    PrintTemplate.Result print(User user, PrintTemplate.Arg arg);


    /**
     * 找到打印模板
     *
     * @param user 用户
     * @param arg  参数
     * @return {@link GetPrintTemplate.Result}
     */
    GetPrintTemplate.Result findPrintTemplate(User user, GetPrintTemplate.Arg arg);
}
