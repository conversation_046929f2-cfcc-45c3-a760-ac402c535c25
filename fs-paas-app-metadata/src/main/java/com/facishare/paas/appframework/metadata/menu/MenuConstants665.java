package com.facishare.paas.appframework.metadata.menu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.metadata.menu.model.MenuItemConfigObject;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class MenuConstants665 {
    public static String MENU_API_NAME = "MenuObj";
    public static String MENU_ITEM_API_NAME = "MenuItemObj";
    public static String ROLE_SOURCE_API_NAME = "RoleSourceObj";
    public static String MENU_WORKBENCH_API_NAME = "MenuWorkbenchObj";
    public static String COMMONLY_USED_MENU_ITEM_API_NAME = "CommonlyUsedMenuItem";
    //配置项，默认CRM菜单的key
    public static String CONFIG_MENU_DEFAULT = "Crm_Menu_Default";
    public static String CONFIG_PKG_CRM = "CRM";

    //预制crm菜单的apiname
    public static String MENU_SYSTEM_APINAME = "crm_menu";

    public static final String DEVICE_TYPE_ALL = "all";
    //web端
    public static final String DEVICE_TYPE_WEB = "web";
    //终端
    public static final String DEVICE_TYPE_MOBILE = "mobile";

    //系统配置的预设对象菜单
    public static List<MenuItemConfigObject> configMenuItemList = null;
    public static Map<String, MenuItemConfigObject> configMenuItemMap = Maps.newHashMap();
    public static Map<String, String> quickAddExcludeDevice = Maps.newHashMap();
    public static JSONObject grayMenuRuleConfig;
    //A版支持的菜单
    public static Set<String> basicVersionSupportApiNames;
    //制造业包支持的菜单
    public static Set<String> packageManufacture;
    //快销包支持的菜单
    public static Set<String> packageFmcg;
    //特殊的菜单权限控制依赖关系
    public static JSONObject privilegeActionListDepends;
    //初始化菜单分组
    public static JSONObject menuItemsGroupDefaultConfig;
    //报表需要的权限
    public static String showReportByFunctionNo;
    //终端默认的常用菜单
    public static String defaultCommonUsedMenuItem;
    //默认常用需要过权限
    public static Set<String> defaultNeedCheckPrivilege;
    //在终端特殊的对象
    public static String specialConfigMenuItem;
    //多语言分组名称对应的key
    public static JSONObject groupNameToKey;
    //白名单
    public static Set<String> apiNamesNoCheckVersion;
    static {
        ConfigFactory.getInstance().getConfig("fs-crm-menu-config-665", config -> {
            try {
                apiNamesNoCheckVersion = Sets.newHashSet(config.get("api_names_no_check_version").split(","));
                specialConfigMenuItem = config.get("special_config_menuItem");
                defaultCommonUsedMenuItem = config.get("default_common_used_menu_item");
                defaultNeedCheckPrivilege = Sets.newHashSet(config.get("default_need_check_privilege").split(","));
                menuItemsGroupDefaultConfig = JSON.parseObject(config.get("menu_items_group_default_config"));
                showReportByFunctionNo = config.get("report_fuction_no");
                String systemObjects = config.get("menu_items_default_config");
                String menuQuickAddConfig = config.get("menu_quick_add_config");
                configMenuItemList = JSON.parseArray(systemObjects, MenuItemConfigObject.class);
                quickAddExcludeDevice = JSON.parseObject(menuQuickAddConfig, Map.class);
                configMenuItemMap = configMenuItemList.stream().collect(Collectors.toMap(MenuItemConfigObject::getApiName, o -> o));
                String grayMenuRuleConfigStr = config.get("gray_menu_rule");
                if (StringUtils.isNotEmpty(grayMenuRuleConfigStr)) {
                    grayMenuRuleConfig = JSON.parseObject(grayMenuRuleConfigStr);
                }
                String basicVersionSupportApiNamesStr = config.get("A_Version_AllShow");
                if (StringUtils.isNotEmpty(basicVersionSupportApiNamesStr)) {
                    basicVersionSupportApiNames = Sets.newHashSet(basicVersionSupportApiNamesStr.split(","));
                }
                String privilegeActionListDependsStr = config.get("privilege_action_list_depends");
                if (StringUtils.isNotEmpty(privilegeActionListDependsStr)) {
                    privilegeActionListDepends = JSON.parseObject(privilegeActionListDependsStr);
                }
                String packageManufactureConfig = config.get("package_manufacture");
                if (StringUtils.isNotEmpty(packageManufactureConfig)) {
                    packageManufacture = Sets.newHashSet(JSON.parseArray(packageManufactureConfig, String.class));
                }
                String packageFmcgConfig = config.get("package_fmcg");
                if (StringUtils.isNotEmpty(packageFmcgConfig)) {
                    packageFmcg = Sets.newHashSet(JSON.parseArray(packageFmcgConfig, String.class));
                }
                String nameGroups = config.get("menu_items_group_key");
                if (StringUtils.isNotEmpty(nameGroups)) {
                    groupNameToKey = JSON.parseObject(nameGroups);
                }

                log.info("reload fs-crm-menu-config-665,content:{}", config.getString());
            } catch (Exception e) {
                log.error("reload fs-crm-menu-config-665 failed:{}", config.getString(), e);
            }

        });
    }
}
