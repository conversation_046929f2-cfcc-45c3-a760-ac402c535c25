package com.facishare.paas.appframework.core.model

import com.facishare.paas.appframework.common.util.ObjectAction
import com.facishare.paas.metadata.api.IUdefButton
import com.facishare.paas.metadata.impl.UdefButton
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

class ButtonDocumentTest extends Specification {

    def i18nClient = Mock(I18nClient)
    def i18nServiceImpl = Mock(I18nServiceImpl)

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    
    def "test of method with null button"() {
        when: "使用null作为button参数"
        def result = ButtonDocument.of(null, true)
        
        then: "验证结果为null"
        result == null
    }
    
    def "test of method with empty apiName"() {
        given: "准备一个apiName为空的button"
        def button = new UdefButton()
        button.setApiName("")
        
        and: "Mock ObjectAction"
        GroovyMock(ObjectAction, global: true)
        ObjectAction.getActionCodeByButtonApiName("") >> ""
        
        when: "调用被测试方法"
        def result = ButtonDocument.of(button, true)
        
        then: "验证结果"
        result != null
        result.get("api_name") == ""
        result.get("actionCode") == ""
    }
    

    

    
    def "test of method with includeActionCode false"() {
        given: "准备一个UdefButton对象"
        def button = new UdefButton()
        button.setApiName("test_button")
        
        when: "调用of方法，includeActionCode为false"
        def result = ButtonDocument.of(button, false)
        
        then: "验证结果不包含actionCode"
        result != null
        result.get("api_name") == "test_button"
        !result.containsKey("actionCode")
    }

} 