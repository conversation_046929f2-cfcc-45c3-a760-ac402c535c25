package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.metadata.dto.ProductAllCategoriesModel;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by luxin on 2019-01-22.
 */
public interface ProductCategoryService {

    /**
     * 获取全部分类
     *
     * @param tenantId 企业 id
     * @param userId   员工 id
     * @return 分类树形结构
     */
    List<ProductAllCategoriesModel.CategoryPojo> getProductAllCategories(String tenantId, String userId);


    /**
     * 获目标分类所有的子分类code,包括自身
     *
     * @param tenantId     企业 id
     * @param userId       员工 id
     * @param categoryCode 目标分类的 code, not blank
     * @return 目标分类的 code 及子分类的 codes
     */
    Set<String> getCategoryChildrenCategoryCodesContainSelf(String tenantId, String userId, String categoryCode);

    Set<String> getCategoryChildrenCategoryCodesContainSelf(String categoryCode, List<ProductAllCategoriesModel.CategoryPojo> productAllCategories);

    void handleCategoryWhere(String tenantId, String userId, List<Wheres> wheres);

    void handleCategoryFilters(String tenantId, String userId, List<IFilter> filters);
}
