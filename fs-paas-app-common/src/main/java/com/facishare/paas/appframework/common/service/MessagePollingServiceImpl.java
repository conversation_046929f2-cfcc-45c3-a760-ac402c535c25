package com.facishare.paas.appframework.common.service;


import com.facishare.paas.appframework.common.service.model.PollingKeys;
import com.facishare.paas.appframework.common.service.model.PollingMsgEndType;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.metadata.support.GDSHandler;
import com.facishare.polling.api.arg.UpdatePollingDataArg;
import com.facishare.polling.api.enums.PollingOSType;
import com.facishare.polling.api.util.PollingMessageProducer;
import com.facishare.polling.api.util.RangeBuilder;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class MessagePollingServiceImpl implements MessagePollingService {
    @Autowired
    private GDSHandler gdsHandler;
    @Resource
    private PollingMessageProducer pollingMessageProducer;

    @Override
    public void sendPollingMessage(User user, String pollingKey, PollingMsgEndType pollingMegEndType, boolean isRealTime) {
        doSendPollingMessage(user, pollingKey, pollingMegEndType.toPollingOSType(), isRealTime, false);
    }

    @Override
    public void sendWholeNetworkPollingMessage(User user, String pollingKey, PollingMsgEndType pollingMegEndType, boolean isRealTime) {
        doSendPollingMessage(user, pollingKey, pollingMegEndType.toPollingOSType(), isRealTime, true);
    }

    @Override
    public void notifyDescribeLayoutChange(String tenantId, boolean isWholeNetwork) {
        User user = User.systemUser(tenantId);
        boolean isRealTime = false;
        if (isWholeNetwork) {
            sendWholeNetworkPollingMessage(user, PollingKeys.DESCRIBE_LAYOUT_CHANGE_ALL, PollingMsgEndType.MOBILE, isRealTime);
        } else {
            isRealTime = !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NO_REALTIME_DESCRIBE_LAYOUT_CHANGE_POLLING_EI, tenantId);
            sendPollingMessage(user, PollingKeys.DESCRIBE_LAYOUT_CHANGE, PollingMsgEndType.MOBILE, isRealTime);
        }
        log.info("notifyDescribeLayoutChange,tenantId:{},isWholeNetwork:{},isRealTime:{}", tenantId, isWholeNetwork, isRealTime);
    }

    private void doSendPollingMessage(User user, String pollingKey, PollingOSType osType, boolean isRealTime, boolean isWholeNetwork) {
        try {
            UpdatePollingDataArg arg = new UpdatePollingDataArg();
            arg.setKey(pollingKey);
            arg.setVersion(System.currentTimeMillis());
            arg.setOsType(osType);
            if (isWholeNetwork) {
                arg.setRange(RangeBuilder.buildWholeNetworkRange());
            } else {
                String ea = gdsHandler.getEAByEI(user.getTenantId());
                arg.setRange(RangeBuilder.buildEnterprisesRange(Lists.newArrayList(ea)));
            }
            arg.setRealTime(isRealTime);

            //使用下面的接入方式
            pollingMessageProducer.sendMessage(arg);
        } catch (Exception e) {
            log.error("Error in doSendPollingMessage, ei:{}, user:{}, pollingKey:{}, osType:{}, isRealTime:{}",
                    user.getTenantId(), user.getUserId(), pollingKey, osType, isRealTime, e);
        }
    }
}
