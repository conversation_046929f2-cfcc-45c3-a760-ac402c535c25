package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.FormTable;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.layout.component.SummaryKeyComponentInfo;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collection;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;


@ExtendWith(MockitoExtension.class)
class FormComponentRenderTest {



  @Mock
  private ObjectDescribeExt objectDescribeExt;

  @Mock
  private User user;

  @Mock
  private IObjectData data;

  @Mock
  private FunctionPrivilegeService functionPrivilegeService;

  private List<FormComponentExt> formComponentExtList;
  private List<SummaryKeyComponentInfo> summaryKeyComponentInfos;
  private List<FormTable> formTableList;
  private List<IFormField> orderFormFieldList;
  private Collection<String> unauthorizedFields;

  @BeforeEach
  void setUp() {
    formComponentExtList = Lists.newArrayList();
    summaryKeyComponentInfos = Lists.newArrayList();
    formTableList = Lists.newArrayList();
    orderFormFieldList = Lists.newArrayList();
    unauthorizedFields = Lists.newArrayList();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试FormComponentRender构造函数正常场景
   */
  @Test
  @DisplayName("正常场景 - 构造FormComponentRender对象")
  void testFormComponentRenderConstructor_Success() {
    // 执行被测试方法
    FormComponentRender formComponentRender = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .summaryKeyComponentInfos(summaryKeyComponentInfos)
        .formTableList(formTableList)
        .layoutType("DETAIL")
        .recordType("test_record")
        .data(data)
        .functionPrivilegeService(functionPrivilegeService)
        .unauthorizedFields(unauthorizedFields)
        .dbLayoutType("test_layout")
        .orderFormFieldList(orderFormFieldList)
        .build();

    // 验证结果
    assertNotNull(formComponentRender);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试构造函数传入null参数
   */
  @Test
  @DisplayName("异常场景 - 传入null参数")
  void testFormComponentRenderConstructor_NullParameters() {
    // 执行被测试方法
    FormComponentRender formComponentRender = FormComponentRender.builder()
        .user(null)
        .describeExt(null)
        .formComponentExtList(null)
        .build();

    // 验证结果
    assertNotNull(formComponentRender);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试render方法执行渲染逻辑
   */
  @Test
  @DisplayName("正常场景 - 执行表单组件渲染")
  void testRender_Success() {
    FormComponentRender formComponentRender = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .summaryKeyComponentInfos(summaryKeyComponentInfos)
        .formTableList(formTableList)
        .layoutType("DETAIL")
        .recordType("test_record")
        .data(data)
        .functionPrivilegeService(functionPrivilegeService)
        .unauthorizedFields(unauthorizedFields)
        .dbLayoutType("test_layout")
        .orderFormFieldList(orderFormFieldList)
        .build();

    // 执行被测试方法
    assertDoesNotThrow(() -> {
      formComponentRender.render();
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试render方法不同布局类型
   */
  @Test
  @DisplayName("正常场景 - 测试不同布局类型渲染")
  void testRender_DifferentLayoutTypes() {
    // 测试EDIT布局类型
    FormComponentRender editRender = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .layoutType("EDIT")
        .build();

    assertDoesNotThrow(() -> {
      editRender.render();
    });

    // 测试ADD布局类型
    FormComponentRender addRender = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .layoutType("ADD")
        .build();

    assertDoesNotThrow(() -> {
      addRender.render();
    });
  }


}
