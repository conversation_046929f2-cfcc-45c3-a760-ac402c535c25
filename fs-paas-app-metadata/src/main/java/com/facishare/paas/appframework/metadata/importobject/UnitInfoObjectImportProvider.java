package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.facishare.crm.openapi.Utils.UNIT_INFO_API_NAME;

/**
 * <AUTHOR>
 * @date 2019-08-09 17:48
 * @instruction  单位不支持导入
 */
@Component
public class UnitInfoObjectImportProvider extends DefaultObjectImportProvider {
    @Override
    public String getObjectCode() {
        return UNIT_INFO_API_NAME;
    }

    @Override
    public Optional<ImportObject> getImportObject(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return Optional.empty();
    }
}
