package com.facishare.paas.appframework.metadata.config;

import com.facishare.converter.EIEAConverter;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.metadata.mtresource.IMtResourceService;
import com.facishare.paas.appframework.metadata.mtresource.model.ConfigurationPackageResource;
import com.facishare.paas.appframework.metadata.repository.model.MtResource;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.CopyOnWriteMap;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by fengjy in 2020/6/23 16:17
 */
@Slf4j
@Service("objectConfigService")
public class ObjectConfigServiceImpl implements ObjectConfigService {

    public static final ImmutableMap<String, Serializable> CONFIG_MAP = ImmutableMap.of("edit", 0, "remove", 0, "enable", 0, "attrs", ImmutableMap.of("is_required", 1));
    public static final String CONTROL_LEVEL = "controlLevel";
    public static final String CONTROL_SOURCE = "controlSource";
    @Autowired
    private ObjectConfigManager configManager;
    @Autowired
    private ObjectConfigProvider configProvider;
    @Autowired
    private BusinessFilterProvider businessFilterProvider;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private TenantConfigManager tenantConfigManager;
    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private IMtResourceService mtResourceService;

    @Autowired
    private MaskFieldLogicService maskFieldLogicService;
    @Autowired
    private ObjectControlLevelLogicService objectControlLevelLogicService;

    @Override
    public Map<String, ObjectConfig> getObjectConfig(User user, Set<String> apiNameList) {
        return getObjectConfig(user, apiNameList, null);
    }

    @Override
    public Map<String, ObjectConfig> getObjectConfig(User user, Set<String> apiNameList, Boolean includeBusiness, Boolean includeFilters) {
        return getObjectConfig(user, apiNameList, includeBusiness, includeFilters, null);
    }

    @Override
    public Map<String, ObjectConfig> getObjectConfig(User user, Set<String> apiNameList, Boolean includeBusiness, Boolean includeFilters, String source) {
        StopWatch stopWatch = StopWatch.create(getClass() + "getObjectConfig");
        // 兼容入参中 apiNameList 为 null 的情况
        apiNameList.removeIf(Objects::isNull);
        Map<String, IObjectDescribe> objectDescribes = describeLogicService.findObjectsWithoutCopyIfGray(user.getTenantId(), apiNameList);
        stopWatch.lap("findObjects");

        Map<String, ObjectConfig> result = new HashMap<>();
        for (String apiName : apiNameList) {
            stopWatch.lap(apiName);

            IObjectDescribe objectDescribe = objectDescribes.get(apiName);

            //部分前端传入的对象APIName,部分企业查询不到描述
            if (ObjectUtils.isEmpty(objectDescribe)) {
                ObjectConfig config = ObjectConfig.builder()
                        .fields(new HashMap<>())
                        .object(new HashMap<>())
                        .layouts(new HashMap<>())
                        .layoutRules(new HashMap<>())
                        .rules(new HashMap<>())
                        .business(new HashMap<>())
                        .filters(new HashMap<>())
                        .build();
                result.put(apiName, config);
                continue;
            }
            List<String> fieldApiNames = ObjectDescribeExt.of(objectDescribe).getFieldDescribes()
                    .stream().map(IFieldDescribe::getApiName)
                    .collect(Collectors.toList());

            Map<String, Object> objectConfig = configManager.searchObjectConfig(user.getTenantId(), apiName);
            Map<String, Map<String, Object>> ruleConfig = configManager.searchRuleConfig(user.getTenantId(), apiName);
            Map<String, Map<String, Object>> fieldConfig = configManager.searchFieldConfig(user.getTenantId(), apiName, fieldApiNames);
            Map<String, Map<String, Object>> layoutConfig = configManager.searchLayoutConfig(user.getTenantId(), apiName);
            Map<String, Map<String, Object>> layoutRuleConfig = configManager.searchLayoutRuleConfig(user.getTenantId(), apiName);

            //新版config
            Map<String, Object> businessConfig = configManager.searchBusinessConfig(user.getTenantId(), apiName);
            Map<String, Object> filterConfig = configManager.searchFilterConfig(user.getTenantId(), apiName);
            Tuple<Map<String, Object>, Map<String, Map<String, Object>>> configs = configProvider.handle(user.getTenantId(),
                    apiName, objectConfig, fieldConfig, source);
            Tuple<Map<String, Object>, Map<String, Object>> businessFilterConfigs = businessFilterProvider.handle(user.getTenantId(), apiName, businessConfig, filterConfig);
            stopWatch.lap("handle");
            ObjectConfig config = ObjectConfig.builder()
                    .fields(configs.getValue())
                    .object(configs.getKey())
                    .layouts(layoutConfig)
                    .layoutRules(layoutRuleConfig)
                    .rules(ruleConfig)
                    .build();

            if (!ObjectUtils.isEmpty(objectDescribe) && Boolean.TRUE.equals(includeBusiness)) {
                config.setBusiness(businessFilterConfigs.getKey());

            }
            if (!ObjectUtils.isEmpty(objectDescribes) && Boolean.TRUE.equals(includeFilters)) {
                config.setFilters(businessFilterConfigs.getValue());
            }

            handleObjectConfig(user, objectDescribe, config);
            stopWatch.lap("handleObjectConfigByMtResource");
            result.put(apiName, config);
        }
        stopWatch.logSlow(1000);
        return result;
    }

    private void handleObjectConfig(User user, IObjectDescribe objectDescribe, ObjectConfig config) {
        handleObjectConfigByConfigurationPackage(user, objectDescribe, config);
        Map<String, Map<String, Object>> fieldConfigMap = handleObjectConfigByMtResource(user, objectDescribe, config.getFields());
        fieldConfigMap = handleObjectConfigByMaskFieldGray(user, objectDescribe, fieldConfigMap);
        config.setFields(fieldConfigMap);
    }

    private void handleObjectConfigByConfigurationPackage(User user, IObjectDescribe objectDescribe, ObjectConfig config) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CONFIGURATION_PACKAGE, user.getTenantId())) {
            return;
        }
        List<ObjectControlLevelLogicService.ObjectControlLevelInfo> objectControlLevelInfos = objectControlLevelLogicService.queryControlLevel(
                user, objectDescribe.getApiName(), ControlLevelResourceType.DESCRIBE, ControlLevelResourceType.FIELD, ControlLevelResourceType.LAYOUT);

        Map<String, List<ObjectControlLevelLogicService.ObjectControlLevelInfo>> resourceMap = objectControlLevelInfos.stream()
                .collect(Collectors.groupingBy(ObjectControlLevelLogicService.ObjectControlLevelInfo::getSourceType));
        objectConfig(objectDescribe, config, resourceMap.get(ConfigurationPackageResource.DESCRIBE_SOURCE_TYPE));
        fieldsConfig(user, objectDescribe, config, resourceMap.get(ConfigurationPackageResource.FIELD_SOURCE_TYPE));
        layoutsConfig(user, objectDescribe, config, resourceMap.get(ConfigurationPackageResource.LAYOUT_TYPE));
    }

    private void layoutsConfig(User user, IObjectDescribe objectDescribe, ObjectConfig config, List<ObjectControlLevelLogicService.ObjectControlLevelInfo> resources) {
        if (CollectionUtils.empty(resources)) {
            return;
        }
        Map<String, Map<String, Object>> layoutMaps = config.getLayouts();
        for (ObjectControlLevelLogicService.ObjectControlLevelInfo resource : resources) {
            String layoutName = resource.getPrimaryKey();
            String refObjectApiName = resource.getParentFieldValue();
            if (!Objects.equals(objectDescribe.getApiName(), refObjectApiName)) {
                continue;
            }
            layoutMaps = copyOnWrite(layoutMaps);
            layoutMaps.compute(layoutName, (key, oldValue) -> {
                Map<String, Object> value;
                if (Objects.isNull(oldValue)) {
                    value = Maps.newHashMap();
                } else {
                    value = oldValue;
                }
                value.put(CONTROL_LEVEL, resource.getControlLevel());
                return value;
            });
        }
        config.setLayouts(layoutMaps);
    }

    private void fieldsConfig(User user, IObjectDescribe objectDescribe, ObjectConfig config, List<ObjectControlLevelLogicService.ObjectControlLevelInfo> resources) {
        if (CollectionUtils.empty(resources)) {
            return;
        }
        Map<String, Map<String, Object>> fieldMaps = copyOnWrite(config.getFields());
        for (ObjectControlLevelLogicService.ObjectControlLevelInfo resource : resources) {
            String fieldName = resource.getPrimaryKey();
            String describeApiName = resource.getParentFieldValue();
            if (!Objects.equals(describeApiName, objectDescribe.getApiName()) || !objectDescribe.containsField(fieldName)) {
                continue;
            }
            fieldMaps.compute(fieldName, (key, oldValue) -> {
                Map<String, Object> value;
                if (Objects.isNull(oldValue)) {
                    value = Maps.newHashMap();
                } else {
                    value = oldValue;
                }
                value.put(CONTROL_LEVEL, resource.getControlLevel());
                return value;
            });
        }
        config.setFields(fieldMaps);
    }

    private void objectConfig(IObjectDescribe objectDescribe, ObjectConfig config, List<ObjectControlLevelLogicService.ObjectControlLevelInfo> resources) {
        if (CollectionUtils.empty(resources)) {
            return;
        }
        for (ObjectControlLevelLogicService.ObjectControlLevelInfo resource : resources) {
            String apiName = resource.getPrimaryKey();
            if (!Objects.equals(objectDescribe.getApiName(), apiName)) {
                continue;
            }
            Map<String, Object> objectMap = copyOnWrite(config.getObject());
            String controlLevel = resource.getControlLevel();
            objectMap.put(CONTROL_LEVEL, controlLevel);
            config.setObject(objectMap);
        }
    }

    /**
     * 按对象字段灰度，修改支持掩码字段的config
     *
     * @param user
     * @param objectDescribe
     * @param fieldConfigMap
     */
    private Map<String, Map<String, Object>> handleObjectConfigByMaskFieldGray(User user, IObjectDescribe objectDescribe, Map<String, Map<String, Object>> fieldConfigMap) {
        if (!AppFrameworkConfig.maskFieldEncryptGray(user.getTenantId(), objectDescribe.getApiName())) {
            return fieldConfigMap;
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        if (describeExt.isCustomObject()) {
            return fieldConfigMap;
        }
        List<String> maskFieldTypes = maskFieldLogicService.getMaskFieldTypes(user.getTenantId(), objectDescribe.getApiName());
        Set<String> fieldNames = describeExt.stream()
                .map(FieldDescribeExt::of)
                .filter(field -> !field.isCustomField())
                .filter(field -> maskFieldTypes.contains(field.getTypeOrReturnType()))
                .map(FieldDescribeExt::getApiName)
                .collect(Collectors.toSet());

        Set<String> fields = AppFrameworkConfig.maskFieldEncryptObjectFieldsGray(describeExt.getApiName(), fieldNames);
        if (CollectionUtils.empty(fields)) {
            return fieldConfigMap;
        }
        // 加工掩码字段的conf
        fieldConfigMap = copyOnWrite(fieldConfigMap);
        for (String fieldName : fields) {
            Map<String, Object> fieldConf = fieldConfigMap.computeIfAbsent(fieldName, k -> Maps.newHashMap());
            // 确保attrs是一个可变Map
            Map<String, Object> existingAttrs = (Map<String, Object>) fieldConf.get("attrs");
            Map<String, Object> attrsConf = Maps.newHashMap();
            if (CollectionUtils.notEmpty(existingAttrs)) {
                attrsConf.putAll(existingAttrs);
            }

            attrsConf.put("is_show_mask", 1);
            attrsConf.put("remove_mask_roles", 1);
            fieldConf.put("attrs", attrsConf);
        }
        return fieldConfigMap;
    }

    private Map<String, Map<String, Object>> handleObjectConfigByMtResource(User user, IObjectDescribe objectDescribe, Map<String, Map<String, Object>> fieldConfigMap) {
        StopWatch stopWatch = StopWatch.create(getClass() + "#handleObjectConfigByMtResource");
        Map<String, Map<String, Object>> result = copyOnWrite(fieldConfigMap);
        try {
            List<MtResource> mtResources = mtResourceService.queryResource(user.getTenantId(), objectDescribe.getApiName(), MtResource.RESOURCE_TYPE_FILED, null);
            stopWatch.lap("queryResource");
            if (CollectionUtils.empty(mtResources)) {
                return result;
            }
            handleBySourceType(objectDescribe, result, mtResources);
        } catch (Exception e) {
            log.warn("handleObjectConfigByMtResource failed! ei:{}, describeApiName:{}", user.getTenantId(), objectDescribe.getApiName(), e);
        } finally {
            stopWatch.logSlow(500);
        }
        return result;
    }

    private void handleBySourceType(IObjectDescribe objectDescribe, Map<String, Map<String, Object>> result, List<MtResource> mtResources) {
        List<MtResource> connectionSetting = Lists.newArrayList();
        List<MtResource> fieldControl = Lists.newArrayList();
        for (MtResource resource : mtResources) {
            String sourceType = resource.getSourceType();
            if (MtResource.SOURCE_TYPE_FIELD_CONTROL.equals(sourceType)) {
                fieldControl.add(resource);
            } else {
                connectionSetting.add(resource);
            }
        }
        handleByConnectionSetting(objectDescribe, result, connectionSetting);
        handleByFieldControl(objectDescribe, result, fieldControl);
    }

    private void handleByFieldControl(IObjectDescribe objectDescribe, Map<String, Map<String, Object>> fieldMaps, List<MtResource> resources) {
        if (CollectionUtils.empty(resources)) {
            return;
        }
        for (MtResource resource : resources) {
            String fieldName = resource.getResourceValue();
            if (!objectDescribe.containsField(fieldName)
                    || MtResource.CONTROL_LEVEL_CHANGEABLE.equals(resource.getControlLevel())) {
                continue;
            }
            fieldMaps.compute(fieldName, (key, oldValue) -> {
                Map<String, Object> value;
                if (Objects.isNull(oldValue)) {
                    value = Maps.newHashMap();
                } else {
                    value = oldValue;
                }
                // 弱管控、管控、强管控。优先级由低到高
                Object oldControlLevel = value.put(CONTROL_LEVEL, resource.getControlLevel());
                String sourceValue = resource.getSourceValue();
                if (Objects.isNull(oldControlLevel) || MtResource.SOURCE_VALUE_STRICT.equals(sourceValue)) {
                    value.put(CONTROL_SOURCE, sourceValue);
                }
                return value;
            });
        }
    }

    private void handleByConnectionSetting(IObjectDescribe objectDescribe, Map<String, Map<String, Object>> result, List<MtResource> mtResources) {
        for (MtResource mtResource : mtResources) {
            String fieldName = mtResource.getResourceValue();
            if (!objectDescribe.containsField(fieldName)
                    || MtResource.CONTROL_LEVEL_CHANGEABLE.equals(mtResource.getControlLevel())) {
                continue;
            }
            // 当前只能 可编辑(1) -> 不可编辑(0)
            result.compute(fieldName, (key, oldValue) -> {
                if (oldValue == null) {
                    return Maps.newHashMap(CONFIG_MAP);
                }
                oldValue.put("edit", 0);
                oldValue.put("enable", 0);
                oldValue.put("remove", 0);
                Object attrs = oldValue.get("attrs");
                if (Objects.isNull(attrs)) {
                    oldValue.put("attrs", Maps.newHashMap(ImmutableMap.of("is_required", 1)));
                } else {
                    ((Map) attrs).put("is_required", 1);
                    oldValue.put("attrs", attrs);
                }
                return oldValue;
            });
        }
    }

    private <T> Map<String, T> copyOnWrite(Map<String, T> fieldConfigMap) {
        if (fieldConfigMap instanceof CopyOnWriteMap) {
            return fieldConfigMap;
        }
        return CopyOnWriteMap.copy(CollectionUtils.nullToEmpty(fieldConfigMap));
    }

    @Override
    public Map<String, ObjectConfig> getObjectConfig(User user, Set<String> apiNameList, String source) {
        return getObjectConfig(user, apiNameList, false, false, source);
    }

    @Override
    public Map<String, Object> getBusinessConfig(User user, Set<String> apiNameList) {
        // 兼容入参中 apiNameList 为 null 的情况
        apiNameList.removeIf(Objects::isNull);
        Map<String, IObjectDescribe> objectDescribes = describeLogicService.findObjectsWithoutCopyIfGray(user.getTenantId(), apiNameList);
        Map<String, Object> result = new HashMap<>();
        for (String apiName : apiNameList) {
            IObjectDescribe objectDescribe = objectDescribes.get(apiName);
            //部分前端传入的对象APIName,部分企业查询不到描述
            if (ObjectUtils.isEmpty(objectDescribe)) {
                continue;
            }
            Map<String, Object> businessConfig = configManager.searchBusinessConfig(user.getTenantId(), apiName);

            if (!ObjectUtils.isEmpty(objectDescribe)) {
                result.put(apiName, businessConfig);
            }
        }
        return result;
    }

    @Override
    public Map<String, Object> getWebConfig(User user) {
        return tenantConfigManager.getWebConfig(eieaConverter.enterpriseIdToAccount(user.getTenantIdInt()));
    }

    @Override
    public boolean isWebConfigGray(User user, String grayKey) {
        Map<String, Object> webConfig = getWebConfig(user);
        if (CollectionUtils.empty(webConfig)) {
            return false;
        }
        return Boolean.TRUE.equals(webConfig.get(grayKey));
    }

    @Override
    public Map<String, ObjectConfig> queryObjectConfigByControlLevel(User user, Collection<IObjectDescribe> objectDescribes) {
        Map<String, ObjectConfig> result = Maps.newHashMap();
        if (CollectionUtils.empty(objectDescribes) || !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CONFIGURATION_PACKAGE, user.getTenantId())) {
            return result;
        }
        List<ObjectControlLevelLogicService.ObjectControlLevelInfo> objectControlLevelInfos = objectControlLevelLogicService.queryControlLevel(user, null, ControlLevelResourceType.DESCRIBE);
        Map<String, ObjectControlLevelLogicService.ObjectControlLevelInfo> objectControlLevelInfoMap = objectControlLevelInfos.stream()
                .collect(Collectors.toMap(ObjectControlLevelLogicService.ObjectControlLevelInfo::getPrimaryKey, Function.identity()));
        objectDescribes.forEach(describe -> {
            ObjectConfig objectConfig = makeAndMergeObjectConfig(describe, objectControlLevelInfoMap);
            if (Objects.nonNull(objectConfig)) {
                result.put(describe.getApiName(), objectConfig);
            }
        });
        return result;
    }

    private ObjectConfig makeAndMergeObjectConfig(IObjectDescribe describe, Map<String, ObjectControlLevelLogicService.ObjectControlLevelInfo> objectControlLevelInfoMap) {
        ObjectControlLevelLogicService.ObjectControlLevelInfo objectControlLevelInfo = objectControlLevelInfoMap.get(describe.getApiName());
        if (Objects.isNull(objectControlLevelInfo)) {
            return null;
        }
        ObjectConfig config = ObjectConfig.builder()
                .fields(new HashMap<>())
                .object(new HashMap<>())
                .layouts(new HashMap<>())
                .layoutRules(new HashMap<>())
                .rules(new HashMap<>())
                .business(new HashMap<>())
                .filters(new HashMap<>())
                .build();
        objectConfig(describe, config, Lists.newArrayList(objectControlLevelInfo));
        return config;
    }
}
