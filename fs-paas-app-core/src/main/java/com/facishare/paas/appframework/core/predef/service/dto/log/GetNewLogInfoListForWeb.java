package com.facishare.paas.appframework.core.predef.service.dto.log;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface GetNewLogInfoListForWeb {
    @Data
    class Arg {
        @JSONField(name = "M1")
        private String objectId;

        @JSONField(name = "M2")
        private String apiName;

        @JSONField(name = "M3")
        private int pageSize;

        @JSONField(name = "M4")
        private int pageNumber;

        @J<PERSON><PERSON>ield(name = "M5")
        private String detailApiName;

        @JSONField(name = "M6")
        private String operationalType;

        @JSONField(name = "M7")
        private String masterLogId;

        @JSONField(name = "M8")
        private List<String> bizIds;

        @JSONField(name = "M9")
        private List<String> otherBizIds;

        private String sourceId;

        private String sourceDetailApiName;

        private Long operationTimeFrom;
        private Long operationTimeTo;
        //用于排序的最后一个数据的时间
        private Long operationTime;
        //用于翻页的logId
        private String turnLogId;
        private Boolean needReturnCount;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        @JSONField(name = "M1")
        private PageInfo pageInfo;

        @JSONField(name = "M2")
        private List<LogRecord> modifyRecordList;

        private Map objectDescribeExtra;

        private boolean hasMore;

        private String queryInterval;
    }

}
