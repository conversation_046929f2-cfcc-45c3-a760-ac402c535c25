package com.facishare.paas.appframework.gray.config;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.TenantUtil;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OptionalFeaturesSwitchDTO {
    @JSONField(name = "is_related_team_enabled")
    @Builder.Default
    Boolean isRelatedTeamEnabled = Boolean.TRUE;
    @JSONField(name = "is_global_search_enabled")
    @Builder.Default
    Boolean isGlobalSearchEnabled = Boolean.TRUE;
    @JSONField(name = "is_follow_up_dynamic_enabled")
    @Builder.Default
    Boolean isFollowUpDynamicEnabled = Boolean.TRUE;
    @JSONField(name = "is_modify_record_enabled")
    @Builder.Default
    Boolean isModifyRecordEnabled = Boolean.TRUE;
    @JSONField(name = "multi_field_sort")
    @Builder.Default
    Boolean multiFieldSort = Boolean.FALSE;
    @JSONField(name = "cross_object_filter_button")
    @Builder.Default
    Boolean crossObjectFilterButton = Boolean.FALSE;

    /**
     * @param tenantId
     * @param describe
     * @return
     */
    public static OptionalFeaturesSwitchDTO mackDefaultByDescribe(String tenantId, IObjectDescribe describe) {
        // 变更单对象默认都是关闭
        if (Objects.nonNull(describe) && StringUtils.isNotBlank(describe.getOriginalDescribeApiName())) {
            return new OptionalFeaturesSwitchDTO(false, false, false, true, false, false);
        }
        return generateDefaultSwitch(tenantId, describe);
    }

    private static OptionalFeaturesSwitchDTO generateDefaultSwitch(String tenantId, IObjectDescribe describe) {
        if (Objects.isNull(describe)) {
            return new OptionalFeaturesSwitchDTO();
        }
        String describeApiName = describe.getApiName();
        // 老企业的自定义对象和预制对象默认开启
        if (AppFrameworkConfig.isCustomObject(describeApiName) || !TenantUtil.isOptionalFeatures(tenantId)) {
            return new OptionalFeaturesSwitchDTO();
        }
        return OptionalFeaturesSwitchDTO.builder()
                .isRelatedTeamEnabled(!AppFrameworkConfig.isCloseRelatedTeamSwitchObject(describeApiName))
                .isGlobalSearchEnabled(!AppFrameworkConfig.isCloseGlobalSearchSwitchObject(describeApiName))
                .isFollowUpDynamicEnabled(!AppFrameworkConfig.isCloseFollowUpDynamicSwitchObject(describeApiName))
                .isModifyRecordEnabled(!AppFrameworkConfig.isCloseModifyRecordSwitchObject(describeApiName))
                .multiFieldSort(false)
                .crossObjectFilterButton(false)
                .build();
    }
}
