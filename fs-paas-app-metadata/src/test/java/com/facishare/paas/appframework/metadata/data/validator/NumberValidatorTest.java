package com.facishare.paas.appframework.metadata.data.validator;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * NumberValidator 单元测试
 */
@ExtendWith(MockitoExtension.class)
class NumberValidatorTest {

  private NumberValidator validator;

  @BeforeEach
  void setUp() {
    validator = new NumberValidator();
  }

  @Test
  void testSupportFieldTypes() {
    Set<String> supportedTypes = validator.supportFieldTypes();
    
    assertEquals(3, supportedTypes.size());
    assertTrue(supportedTypes.contains(IFieldType.NUMBER));
    assertTrue(supportedTypes.contains(IFieldType.CURRENCY));
    assertTrue(supportedTypes.contains(IFieldType.PERCENTILE));
  }

  @Test
  void testValidateDataType_WithNullValue_ShouldPass() {
    // Given
    String fieldApiName = "test_field";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.NUMBER);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, null);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithIntegerValue_ShouldPass() {
    // Given
    String fieldApiName = "test_field";
    Integer fieldValue = 123;
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.NUMBER);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithLongValue_ShouldPass() {
    // Given
    String fieldApiName = "test_field";
    Long fieldValue = 123456789L;
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.CURRENCY);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithDoubleValue_ShouldPass() {
    // Given
    String fieldApiName = "test_field";
    Double fieldValue = 123.45;
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.PERCENTILE);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithBigDecimalValue_ShouldPass() {
    // Given
    String fieldApiName = "test_field";
    BigDecimal fieldValue = new BigDecimal("123.456");
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.NUMBER);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithValidNumberString_ShouldPass() {
    // Given
    String fieldApiName = "test_field";
    String fieldValue = "123.45";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.NUMBER);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithEmptyStringForPresetField_ShouldPass() {
    // Given
    String fieldApiName = "remaining_time";
    String fieldValue = "";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.NUMBER);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);
    
    FieldDescribeExt mockFieldDescribeExt = mock(FieldDescribeExt.class);
    when(mockFieldDescribeExt.isCustomField()).thenReturn(false);

    try (MockedStatic<FieldDescribeExt> mockedStatic = mockStatic(FieldDescribeExt.class)) {
      mockedStatic.when(() -> FieldDescribeExt.of(any(IFieldDescribe.class))).thenReturn(mockFieldDescribeExt);

      // When & Then
      assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
    }
  }

  @Test
  void testValidateDataType_WithInvalidNumberString_ShouldThrowException() {
    // Given
    String fieldApiName = "test_field";
    String fieldValue = "invalid_number";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.NUMBER);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    ValidateException exception = assertThrows(ValidateException.class,
        () -> validator.validateDataType(fieldApiName, objectData, describe));
    
    assertNotNull(exception.getMessage());
  }

  @Test
  void testValidateDataType_WithBooleanValue_ShouldThrowException() {
    // Given
    String fieldApiName = "test_field";
    Boolean fieldValue = Boolean.TRUE;
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.NUMBER);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    ValidateException exception = assertThrows(ValidateException.class,
        () -> validator.validateDataType(fieldApiName, objectData, describe));
    
    assertNotNull(exception.getMessage());
  }

  @Test
  void testValidateDataType_WithEmptyStringForCustomField_ShouldThrowException() {
    // Given
    String fieldApiName = "custom_field";
    String fieldValue = "";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.NUMBER);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);
    
    FieldDescribeExt mockFieldDescribeExt = mock(FieldDescribeExt.class);
    when(mockFieldDescribeExt.isCustomField()).thenReturn(true);

    try (MockedStatic<FieldDescribeExt> mockedStatic = mockStatic(FieldDescribeExt.class)) {
      mockedStatic.when(() -> FieldDescribeExt.of(any(IFieldDescribe.class))).thenReturn(mockFieldDescribeExt);

      // When & Then
      ValidateException exception = assertThrows(ValidateException.class,
          () -> validator.validateDataType(fieldApiName, objectData, describe));
      
      assertNotNull(exception.getMessage());
    }
  }

  @Test
  void testValidateDataType_WithNullFieldApiName_ShouldReturn() {
    // Given
    String fieldApiName = null;
    IObjectDescribe describe = createObjectDescribe("test_field", IFieldType.NUMBER);
    IObjectData objectData = new ObjectData(Collections.emptyMap());

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithEmptyFieldApiName_ShouldReturn() {
    // Given
    String fieldApiName = "";
    IObjectDescribe describe = createObjectDescribe("test_field", IFieldType.NUMBER);
    IObjectData objectData = new ObjectData(Collections.emptyMap());

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithNullData_ShouldReturn() {
    // Given
    String fieldApiName = "test_field";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.NUMBER);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, null, describe));
  }

  @Test
  void testValidateDataType_WithNullDescribe_ShouldReturn() {
    // Given
    String fieldApiName = "test_field";
    IObjectData objectData = new ObjectData(Collections.emptyMap());

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, null));
  }

  /**
   * 创建测试用的 IObjectDescribe 对象
   */
  private IObjectDescribe createObjectDescribe(String fieldApiName, String fieldType) {
    IObjectDescribe describe = new ObjectDescribe();
    describe.setApiName("TestObj");
    
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", fieldApiName);
    fieldMap.put("type", fieldType);
    fieldMap.put("label", "测试字段");
    
    IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
    
    describe.setFieldDescribes(Arrays.asList(fieldDescribe));
    return describe;
  }
}
