package com.facishare.paas.appframework.privilege;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.privilege.dto.DataPrivilegeCalcProgress;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("dataAuthService")
public class DataAuthServiceImpl implements DataAuthService {

    @Autowired
    private DataAuthProxy proxy;

    @Override
    public DataPrivilegeCalcProgress.Body calcProgress(String tenantId) {
        DataPrivilegeCalcProgress.Arg arg = DataPrivilegeCalcProgress.Arg.builder()
                .tenantId(tenantId)
                .build();
        DataPrivilegeCalcProgress.Result result = proxy.calcProgress(arg);
        if (result.getCode() != 0) {
            log.warn("calcProgress warn arg:{},result:{}", arg, result);
            throw new ValidateException(result.getMessage(), result.getCode());
        }
        return result.getBody();
    }
}
