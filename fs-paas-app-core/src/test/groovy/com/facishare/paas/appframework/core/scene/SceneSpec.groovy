package com.facishare.paas.appframework.core.scene


import spock.lang.Specification

/**
 * <AUTHOR> @date 2019-10-17 10:30
 *
 */
class SceneSpec extends Specification {
    // 连接113数据库
//    @Shared sql = Sql.newInstance("******************************************************",
//            "fs_pgdb_b_u_metadata","vGnN\$I40AV4z#I3u5UoA")

    def "disable predefined scene"() {
        expect:
        1 == 1
        /*
        given:
        SceneLogicService sceneLogicService = new SceneLogicServiceImpl()
        sceneLogicService.searchTemplateService = Mock(ISearchTemplateService)
        String describeApiName = "AccountObj"
        String sceneApiName = "SubParticipate"
        String type = "default"
        User user = new User("55732", "1000")

        when:
        sceneLogicService.disableScene(describeApiName, sceneApiName, type, user)

        then:
        1 * sceneLogicService.searchTemplateService.disableForTenant(_, _)
        */
    }

    def "enable predefined scene"() {
        expect:
        1 == 1
        /*
        given:
        SceneLogicService sceneLogicService = new SceneLogicServiceImpl()
        sceneLogicService.searchTemplateService = Mock(ISearchTemplateService)
        String describeApiName = "AccountObj"
        String sceneApiName = "SubParticipate"
        String type = "default"
        User user = new User("55732", "1000")

        when:
        sceneLogicService.enableScene(describeApiName, sceneApiName, type, user)

        then:
        1 * sceneLogicService.searchTemplateService.enableForTenant(_,_)
        */

    }


}
