package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.privilege.dto.EntityFieldShareInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface GetAllFieldShares {
  @Data
  class Arg {
    @JsonProperty("ruleCodes")
    @SerializedName("ruleCodes")
    @JSONField(name = "M9")
    List ruleCodes;

    @JsonProperty("receives")
    @SerializedName("receives")
    @JSONField(name = "M1")
    List receives;

    @JsonProperty("describeApiName")
    @SerializedName("describeApiName")
    @JSONField(name = "M2")
    String describeApiName;

    @JsonProperty("ruleName")
    @SerializedName("ruleName")
    @JSONField(name = "M3")
    String ruleName;

    @JsonProperty("permission")
    @SerializedName("permission")
    @JSONField(name = "M4")
    Integer permissionType;

    @JsonProperty("status")
    @SerializedName("status")
    @JSONField(name = "M5")
    Integer status;

    @JSONField(name = "M6")
    Integer pageNumber;

    @JSONField(name = "M7")
    Integer pageSize;

    @JsonProperty("receivesWithType")
    @SerializedName("receivesWithType")
    @JSONField(name = "M8")
    Map receivesWithType;

    @JsonProperty("entices")
    @SerializedName("entices")
    @JSONField(name = "M9")
    List<String> entices;

    @JsonProperty("receiveIds")
    @SerializedName("receiveIds")
    @JSONField(name = "M9")
    String receiveIds;

    Set<String> createIds;

    Set<String> modifyIds;

    Map<String, Long> createTimeRange;

    Map<String, Long> modifyTimeRange;

    Boolean outReceive;
  }


  @Builder
  @Data
  class Result {
    @JsonProperty("SharedObjectInfos")
    @SerializedName("SharedObjectInfos")
    @JSONField(name = "M1")
    private List<EntityFieldShareInfo> shareRuleList;

    @JsonProperty("Page")
    @SerializedName("Page")
    @JSONField(name = "M2")
    private PageInfo page;
  }


  @Data
  class PageInfo {
    @JsonProperty("PageCount")
    @SerializedName("PageCount")
    private Integer pageCount;
    @JsonProperty("PageNumber")
    @SerializedName("PageNumber")
    private Integer pageNumber;
    @JsonProperty("PageSize")
    @SerializedName("PageSize")
    private Integer pageSize;
    @JsonProperty("TotalCount")
    @SerializedName("TotalCount")
    private Integer totalCount;
  }
}
