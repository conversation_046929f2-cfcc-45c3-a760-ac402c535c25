package com.facishare.paas.appframework.metadata.mtresource

import com.facishare.paas.appframework.metadata.repository.api.IRepository
import com.facishare.paas.appframework.metadata.repository.model.MtResource
import spock.lang.Specification

/**
 * create by <PERSON><PERSON><PERSON> on 2021/03/05
 */
class MtResourceServiceImplTest extends Specification {
    MtResourceServiceImpl mtResourceService = new MtResourceServiceImpl()


    def "test buildSearchQuery"() {
        given:
        IRepository<MtResource> repository = Mock(IRepository)
        mtResourceService.repository = repository
        when:
        def searchQuery = mtResourceService.buildSearchQuery(tenantId, describeApiName, resourceType, resourceValues)
        def templateQuery = searchQuery.toSearchTemplateQuery()
        then:
        println(templateQuery.toJsonString())
        templateQuery.getLimit() == exceptLimit
        templateQuery.getPattern() == exceptPattern
        where:
        tenantId | describeApiName | resourceType     | resourceValues || exceptLimit | exceptPattern
        "7"      | "AccountObj"    | "Describe.Field" | ["custom__c"]  || 1           | "((((1 AND 2) AND 3) AND 4) AND 5)"

    }

    def "test disableResource"() {
        given:
        IRepository<MtResource> repository = Mock(IRepository)
        mtResourceService.repository = repository
        when:
        mtResourceService.disableResource(tenantId, describeApiName, resourceType, resourceValues, "")
        then:
        1 * repository.findBy(_, _, _) >> expectResource
        expectTimes * repository.bulkUpsert(_, _)
        where:
        tenantId | describeApiName | resourceType     | resourceValues || expectTimes | expectResource
        "7"      | "AccountObj"    | "Describe.Field" | ["custom__c"]  || 0           | []
        "7"      | "AccountObj"    | "Describe.Field" | ["custom__c"]  || 1           | [Stub(MtResource)]
    }
}
