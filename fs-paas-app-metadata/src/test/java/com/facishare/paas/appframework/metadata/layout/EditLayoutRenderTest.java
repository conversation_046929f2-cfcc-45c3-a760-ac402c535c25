package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;


@ExtendWith(MockitoExtension.class)
class EditLayoutRenderTest {



  @Mock
  private FunctionLogicService functionLogicService;

  @Mock
  private ButtonLogicService buttonLogicService;

  @Mock
  private FunctionPrivilegeService functionPrivilegeService;

  @Mock
  private DescribeLogicService describeLogicService;

  @Mock
  private LayoutLogicService layoutLogicService;

  @Mock
  private User user;

  @Mock
  private ILayout layout;

  @Mock
  private IObjectDescribe describe;

  @Mock
  private LicenseService licenseService;

  private List<IObjectDescribe> detailDescribes;
  private Map<ButtonUsePageType, List<IButton>> customButtonMap;
  private Map<String, Localization> localizationMap;

  @BeforeEach
  void setUp() {
    detailDescribes = Lists.newArrayList();
    customButtonMap = Maps.newHashMap();
    localizationMap = Maps.newHashMap();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试EditLayoutRender构造函数正常场景
   */
  @Test
  @DisplayName("正常场景 - 构造EditLayoutRender对象")
  void testEditLayoutRenderConstructor_Success() {
    // 执行被测试方法
    EditLayoutRender editLayoutRender = EditLayoutRender.builder()
        .functionLogicService(functionLogicService)
        .buttonLogicService(buttonLogicService)
        .functionPrivilegeService(functionPrivilegeService)
        .describeLogicService(describeLogicService)
        .layoutLogicService(layoutLogicService)
        .user(user)
        .pageType(PageType.Edit)
        .layoutAgentType(LayoutAgentType.WEB)
        .layout(layout)
        .describe(describe)
        .licenseService(licenseService)
        .detailDescribes(detailDescribes)
        .customButtonMap(customButtonMap)
        .localizationMap(localizationMap)
        .ignoreFunctionPrivilege(false)
        .existMultiLanguage(false)
        .appId("test_app")
        .build();

    // 验证结果
    assertNotNull(editLayoutRender);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试EditLayoutRender基本功能
   */
  @Test
  @DisplayName("正常场景 - 验证编辑布局渲染器基本功能")
  void testEditLayoutRender_BasicFunctionality() {
    EditLayoutRender editLayoutRender = EditLayoutRender.builder()
        .functionLogicService(functionLogicService)
        .buttonLogicService(buttonLogicService)
        .functionPrivilegeService(functionPrivilegeService)
        .describeLogicService(describeLogicService)
        .layoutLogicService(layoutLogicService)
        .user(user)
        .pageType(PageType.Edit)
        .layoutAgentType(LayoutAgentType.WEB)
        .layout(layout)
        .describe(describe)
        .licenseService(licenseService)
        .detailDescribes(detailDescribes)
        .customButtonMap(customButtonMap)
        .localizationMap(localizationMap)
        .ignoreFunctionPrivilege(false)
        .existMultiLanguage(false)
        .appId("test_app")
        .build();

    // 验证基本功能
    assertNotNull(editLayoutRender);
    assertDoesNotThrow(() -> {
      editLayoutRender.toString();
    });
  }
}
