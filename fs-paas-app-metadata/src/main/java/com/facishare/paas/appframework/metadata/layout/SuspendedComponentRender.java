package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.RecordTypeLogicService;
import com.facishare.paas.appframework.metadata.RelatedObjectDescribeStructure;
import com.facishare.paas.appframework.metadata.layout.component.ISuspendedComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.SuspendedActionInfo;
import com.facishare.paas.appframework.metadata.layout.component.SuspendedComponentInfoExt;
import com.facishare.paas.appframework.metadata.util.VelocityUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Builder
public class SuspendedComponentRender {
    private List<RelatedObjectDescribeStructure> relatedObjectList;
    private List<ISuspendedComponentInfo> suspendedComponentInfos;
    private RecordTypeLogicService recordTypeLogicService;
    private IObjectData objectData;
    private User user;

    public void render() {
        replaceUrlVariable();
        removeActions();
    }

    private void removeActions() {
        if (!hasQuickNewAction()) {
            return;
        }
        List<String> relatedObjectApiNames = relatedObjectList.stream()
                .map(x -> x.getRelatedObjectDescribe().getApiName()).collect(Collectors.toList());
        Map<String, List<IRecordTypeOption>> validRecordTypeListMap = recordTypeLogicService.findValidRecordTypeListMap(relatedObjectApiNames, user);
        suspendedComponentInfos.forEach(x -> {
            List<Integer> actionIndexList = SuspendedComponentInfoExt.of(x).removeActions(validRecordTypeListMap);
            if (!CollectionUtils.notEmpty(actionIndexList)) {
                return;
            }
            SuspendedComponentInfoExt.of(x).removeI18nPropsActions(actionIndexList);
        });
    }

    private boolean hasQuickNewAction() {
        return suspendedComponentInfos.stream()
                .anyMatch(component -> component.getActions().stream()
                        .anyMatch(action -> StringUtils.equals(SuspendedActionInfo.NEW_OBJECT, action.getType())));
    }

    private void replaceUrlVariable() {
        suspendedComponentInfos.forEach(suspendedComponent -> suspendedComponent.getActions().forEach(action -> {
            if (StringUtils.equals(SuspendedActionInfo.URL, action.getType())) {
                String url = VelocityUtil.replacePlaceholder(action.getUrl(), ObjectDataExt.toMap(objectData));
                action.setUrl(url);
            }
        }));
    }
}
