package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.predef.service.dto.data.*
import com.facishare.paas.appframework.metadata.DescribeLogicService
import com.facishare.paas.appframework.metadata.MetaDataService
import com.facishare.paas.appframework.metadata.PublicObjectService
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import spock.lang.Specification

/**
 * GenerateByAI
 * 测试内容描述：ObjectDataQueryService对象数据查询服务的单元测试
 */
class ObjectDataQueryServiceTest extends Specification {

    def service = new ObjectDataQueryService()
    def mockMetaDataService = Mock(MetaDataService)
    def mockDescribeLogicService = Mock(DescribeLogicService)
    def mockExpressionService = Mock(ExpressionService)
    def mockServiceFacade = Mock(ServiceFacade)
    def mockInfraServiceFacade = Mock(InfraServiceFacade)
    def mockPublicObjectService = Mock(PublicObjectService)
    def mockUser = Mock(User)
    def mockDescribe = Mock(IObjectDescribe)
    def mockObjectData = Mock(IObjectData)
    def mockQueryResult = Mock(QueryResult)
    def serviceContext = new ServiceContext()

    def setup() {
        service.metaDataService = mockMetaDataService
        service.describeLogicService = mockDescribeLogicService
        service.expressionService = mockExpressionService
        service.serviceFacade = mockServiceFacade
        service.infraServiceFacade = mockInfraServiceFacade
        service.publicObjectService = mockPublicObjectService

        serviceContext.tenantId = "test-tenant"
        serviceContext.user = mockUser
        
        mockUser.userId >> "test-user"
        mockUser.tenantId >> "test-tenant"
        mockDescribe.apiName >> "TestObject"
    }

    def "test queryByCriteria with valid arguments"() {
        given: "准备查询参数"
        def arg = new QueryByCriteria.Arg()
        arg.objectAPIName = "TestObject"
        arg.queryCriteria = [:]

        and: "Mock服务调用"
        mockDescribeLogicService.findObject("test-tenant", "TestObject") >> mockDescribe
        mockQueryResult.data >> [mockObjectData]
        mockMetaDataService.findBySearchQuery(mockUser, mockDescribe, "TestObject", _) >> mockQueryResult

        when: "执行查询"
        def result = service.queryByCriteria(arg, serviceContext)

        then: "应该返回正确结果"
        result != null
        result.dataList != null
        1 * mockDescribeLogicService.findObject("test-tenant", "TestObject")
        1 * mockMetaDataService.findBySearchQuery(mockUser, mockDescribe, "TestObject", _)
    }

    def "test queryByFilter with valid arguments"() {
        given: "准备查询参数"
        def arg = new QueryByFilter.Arg()
        arg.objectAPIName = "TestObject"
        arg.query = Mock(QueryByFilter.Query)
        arg.query.toSearchTemplateQuery() >> Mock(com.facishare.paas.metadata.api.search.ISearchTemplateQuery)

        and: "Mock服务调用"
        mockQueryResult.data >> [mockObjectData]
        mockQueryResult.totalNumber >> 10
        mockMetaDataService.findByQueryWithContext(_, "TestObject", _) >> mockQueryResult

        when: "执行查询"
        def result = service.queryByFilter(arg, serviceContext)

        then: "应该返回正确结果"
        result != null
        result.dataList != null
        result.totalCount == 10
        1 * mockMetaDataService.findByQueryWithContext(_, "TestObject", _)
    }

    def "test findDataById with valid arguments"() {
        given: "准备查询参数"
        def arg = new FindDataById.Arg()
        arg.describeApiName = "TestObject"
        arg.dataId = "test-data-id"

        and: "Mock服务调用"
        mockDescribeLogicService.findObject("test-tenant", "TestObject") >> mockDescribe
        mockMetaDataService.findObjectData(mockUser, "test-data-id", mockDescribe) >> mockObjectData

        when: "执行查询"
        def result = service.findDataById(arg, serviceContext)

        then: "应该返回正确结果"
        result != null
        result.objectData != null
        1 * mockDescribeLogicService.findObject("test-tenant", "TestObject")
        1 * mockMetaDataService.findObjectData(mockUser, "test-data-id", mockDescribe)
        1 * mockMetaDataService.doDataPrivilegeCheck(mockUser, _, mockDescribe, _)
    }

    def "test findDataById with privilege check failure"() {
        given: "准备查询参数"
        def arg = new FindDataById.Arg()
        arg.describeApiName = "TestObject"
        arg.dataId = "test-data-id"

        and: "Mock服务调用"
        mockDescribeLogicService.findObject("test-tenant", "TestObject") >> mockDescribe
        mockMetaDataService.findObjectData(mockUser, "test-data-id", mockDescribe) >> mockObjectData
        mockMetaDataService.doDataPrivilegeCheck(mockUser, _, mockDescribe, _) >> {
            throw new com.facishare.paas.appframework.core.exception.ValidateException("No privilege")
        }

        when: "执行查询"
        def result = service.findDataById(arg, serviceContext)

        then: "应该返回空结果"
        result != null
        result.objectData == null
    }

    def "test optionUsedByData with valid arguments"() {
        given: "准备查询参数"
        def arg = new OptionUsedByData.Arg()
        arg.describeApiName = "TestObject"
        arg.fieldApiName = "testField"
        arg.values = ["value1", "value2"]

        when: "执行查询"
        def result = service.optionUsedByData(serviceContext, arg)

        then: "应该返回正确结果"
        result != null
        result.used == false
        1 * mockMetaDataService.checkOptionUsedByData(mockUser, "TestObject", "testField", ["value1", "value2"])
        1 * mockMetaDataService.checkOptionUsedByReference(mockUser, "TestObject", "testField", ["value1", "value2"])
    }

    def "test convertPublicData with null arguments"() {
        given: "准备null参数"
        def arg = null

        when: "执行转换"
        def result = service.convertPublicData(arg, serviceContext)

        then: "应该返回成功结果"
        result != null
        result.success == true
        0 * mockPublicObjectService.convertPublicData(_, _)
    }

    def "test convertPublicData with empty dataMap"() {
        given: "准备空数据参数"
        def arg = new PublicObject.DataArg()
        arg.dataMap = [:]

        when: "执行转换"
        def result = service.convertPublicData(arg, serviceContext)

        then: "应该返回成功结果"
        result != null
        result.success == true
        0 * mockPublicObjectService.convertPublicData(_, _)
    }

    def "test convertPublicData with valid arguments"() {
        given: "准备有效参数"
        def arg = new PublicObject.DataArg()
        arg.dataMap = ["key1": ["value1"], "key2": ["value2"]]

        when: "执行转换"
        def result = service.convertPublicData(arg, serviceContext)

        then: "应该返回成功结果"
        result != null
        result.success == true
        1 * mockPublicObjectService.convertPublicData(mockUser, arg.dataMap)
    }

    def "test findPublicObjectDataByIds with null arguments"() {
        given: "准备null参数"
        def arg = null

        when: "执行查询"
        def result = service.findPublicObjectDataByIds(arg, serviceContext)

        then: "应该返回空结果"
        result != null
        result.dataMap == null
        0 * mockPublicObjectService.findPublicObjectDataByIds(_, _)
    }

    def "test findPublicObjectDataByIds with empty dataMap"() {
        given: "准备空数据参数"
        def arg = new PublicObject.DataArg()
        arg.dataMap = [:]

        when: "执行查询"
        def result = service.findPublicObjectDataByIds(arg, serviceContext)

        then: "应该返回空结果"
        result != null
        result.dataMap == null
        0 * mockPublicObjectService.findPublicObjectDataByIds(_, _)
    }

    def "test findPublicObjectDataByIds with valid arguments"() {
        given: "准备有效参数"
        def arg = new PublicObject.DataArg()
        arg.dataMap = ["key1": ["id1"], "key2": ["id2"]]
        def expectedResult = ["key1": ["data1"], "key2": ["data2"]]

        and: "Mock服务调用"
        mockPublicObjectService.findPublicObjectDataByIds(mockUser, arg.dataMap) >> expectedResult

        when: "执行查询"
        def result = service.findPublicObjectDataByIds(arg, serviceContext)

        then: "应该返回正确结果"
        result != null
        result.dataMap == expectedResult
        1 * mockPublicObjectService.findPublicObjectDataByIds(mockUser, arg.dataMap)
    }

    def "test service with null serviceContext"() {
        given: "准备参数"
        def arg = new QueryByCriteria.Arg()
        arg.objectAPIName = "TestObject"

        when: "使用null context执行查询"
        service.queryByCriteria(arg, null)

        then: "应该抛出异常"
        thrown(NullPointerException)
    }

    def "test service with invalid objectAPIName"() {
        given: "准备参数"
        def arg = new QueryByCriteria.Arg()
        arg.objectAPIName = "InvalidObject"

        and: "Mock服务调用抛出异常"
        mockDescribeLogicService.findObject("test-tenant", "InvalidObject") >> {
            throw new RuntimeException("Object not found")
        }

        when: "执行查询"
        service.queryByCriteria(arg, serviceContext)

        then: "应该抛出异常"
        thrown(RuntimeException)
    }

    def "test queryByFilter with dataParameter"() {
        given: "准备查询参数"
        def arg = new QueryByFilter.Arg()
        arg.objectAPIName = "TestObject"
        arg.query = Mock(QueryByFilter.Query)
        arg.dataParameter = Mock(QueryByFilter.DataParameter)
        arg.query.toSearchTemplateQuery() >> Mock(com.facishare.paas.metadata.api.search.ISearchTemplateQuery)
        arg.dataParameter.toDataRightsParameter() >> Mock(com.facishare.paas.metadata.api.search.DataRightsParameter)

        and: "Mock服务调用"
        mockQueryResult.data >> [mockObjectData]
        mockQueryResult.totalNumber >> 5
        mockMetaDataService.findByQueryWithContext(_, "TestObject", _) >> mockQueryResult

        when: "执行查询"
        def result = service.queryByFilter(arg, serviceContext)

        then: "应该返回正确结果"
        result != null
        result.dataList != null
        result.totalCount == 5
        1 * mockMetaDataService.findByQueryWithContext(_, "TestObject", _)
    }

    def "test service methods with edge cases"() {
        given: "准备边界情况参数"
        def arg = new QueryByCriteria.Arg()
        arg.objectAPIName = ""
        arg.queryCriteria = null

        when: "执行查询"
        service.queryByCriteria(arg, serviceContext)

        then: "应该处理边界情况"
        thrown(Exception)
    }
}
