package com.facishare.paas.appframework.metadata.expression;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.expression.ExpressionService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Set;

/**
 * 后动作更新字段计算器
 * Created by liyi<PERSON><PERSON> on 2018/5/17.
 */
@Slf4j
public class FieldUpdateCalculator extends AbstractExpressionValueCalculator {

    private String updateField;

    public FieldUpdateCalculator(ExpressionService service, FieldUpdateDTO fieldUpdateDTO, IObjectDescribe objectDescribe) {
        super(service, createExpression(objectDescribe, fieldUpdateDTO), objectDescribe);
        this.updateField = fieldUpdateDTO.getField();
    }

    @Override
    public String key() {
        return updateField;
    }

    @Override
    public boolean canCalculate(Set<String> availableDataSet) {
        return true;
    }

    @Override
    public void doCalculate(IObjectData data, Map<String, Object> globalVariableData, Map<String, Map<String, IObjectData>> objectDataMap,
                            Map<String, Object> extData, boolean isLookupDependencyForOthers) {
        super.doCalculate(data, globalVariableData, objectDataMap, extData, isLookupDependencyForOthers);

        if (result != null && result instanceof BigDecimal) {
            BigDecimal ret = (BigDecimal) result;
            result = ret.toPlainString();
        }
    }

    public static Expression createExpression(IObjectDescribe describe, FieldUpdateDTO fieldUpdateDTO) {

        Expression expression = Expression.builder()
                .expression(fieldUpdateDTO.getExpression() == null ? "" : fieldUpdateDTO.getExpression())
                .describe(describe)
                .returnType(fieldUpdateDTO.getReturnType())
                .nullAsZero(fieldUpdateDTO.isNullAsZero())
                .decimalPlaces(fieldUpdateDTO.getDecimalPlaces())
                .expressionLabel(I18N.text(I18NKey.FIELD_SPECIFY,
                        describe.getDisplayName() + "." + fieldUpdateDTO.getField()))
                .fieldName(fieldUpdateDTO.getField())
                .build();
        return expression;
    }
}

