package com.facishare.paas.appframework.metadata.repository.annotation;

import com.facishare.paas.appframework.metadata.repository.fieldhandler.ObjectFieldType;
import com.facishare.paas.metadata.api.describe.IFieldType;

import java.lang.annotation.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/8/11.
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@FieldType(type = IFieldType.IMAGE)
public @interface ImageField {
    ObjectField field();

    long fileSizeLimit() default 20480; // 20M

    String fileType();

    boolean allowMultiple() default false;

    int fileAmountLimit() default 1;

    String filePath();

    String[] supportedImageTypes();

    ObjectFieldType fieldType() default ObjectFieldType.JSON;
}
