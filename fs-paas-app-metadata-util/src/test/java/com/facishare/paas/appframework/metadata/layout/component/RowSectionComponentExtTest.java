package com.facishare.paas.appframework.metadata.layout.component;

import com.google.common.collect.Lists;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * RowSectionComponentExt单元测试类
 */
@ExtendWith(MockitoExtension.class)
class RowSectionComponentExtTest {

  private RowSectionComponentExt rowSectionComponentExt;

  @BeforeEach
  void setUp() {
    rowSectionComponentExt = new RowSectionComponentExt();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试默认构造函数创建对象
   */
  @Test
  @DisplayName("测试默认构造函数")
  void testDefaultConstructor() {
    RowSectionComponentExt component = new RowSectionComponentExt();
    assertNotNull(component);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试Map构造函数创建对象
   */
  @Test
  @DisplayName("测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = new HashMap<>();
    map.put(RowSectionComponentExt.TYPE, "whole");
    List<Map> fieldSectionList = Lists.newArrayList();
    map.put(RowSectionComponentExt.FIELD_SECTION, fieldSectionList);
    
    RowSectionComponentExt component = new RowSectionComponentExt(map);
    
    assertNotNull(component);
    assertEquals("whole", component.getType());
    assertEquals(fieldSectionList, component.get(RowSectionComponentExt.FIELD_SECTION));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试type字段的getter和setter方法
   */
  @Test
  @DisplayName("测试type字段的getter和setter")
  void testTypeGetterAndSetter() {
    String testType = "space_between";
    
    rowSectionComponentExt.setType(testType);
    
    assertEquals(testType, rowSectionComponentExt.getType());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFieldSection方法当fieldSection为null时返回空列表
   */
  @Test
  @DisplayName("测试getFieldSection方法 - fieldSection为null时返回空列表")
  void testGetFieldSectionWhenNull() {
    List<FieldSectionComponentExt> result = rowSectionComponentExt.getFieldSection();
    
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFieldSection方法当fieldSection不为空时返回FieldSectionComponentExt列表
   */
  @Test
  @DisplayName("测试getFieldSection方法 - fieldSection不为空时返回FieldSectionComponentExt列表")
  void testGetFieldSectionWhenNotEmpty() {
    // 准备测试数据
    Map<String, Object> fieldSectionMap1 = new HashMap<>();
    fieldSectionMap1.put("fields", Lists.newArrayList());
    
    Map<String, Object> fieldSectionMap2 = new HashMap<>();
    fieldSectionMap2.put("fields", Lists.newArrayList());
    
    List<Map> fieldSectionList = Lists.newArrayList(fieldSectionMap1, fieldSectionMap2);
    rowSectionComponentExt.set(RowSectionComponentExt.FIELD_SECTION, fieldSectionList);
    
    // 执行测试
    List<FieldSectionComponentExt> result = rowSectionComponentExt.getFieldSection();
    
    // 验证结果
    assertNotNull(result);
    assertEquals(2, result.size());
    assertTrue(result.get(0) instanceof FieldSectionComponentExt);
    assertTrue(result.get(1) instanceof FieldSectionComponentExt);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试addFieldSection方法添加FieldSectionComponentExt到空列表
   */
  @Test
  @DisplayName("测试addFieldSection方法 - 添加到空列表")
  void testAddFieldSectionToEmptyList() {
    // 准备测试数据
    FieldSectionComponentExt fieldSection = new FieldSectionComponentExt();

    // 执行测试
    rowSectionComponentExt.addFieldSection(fieldSection);

    // 验证结果
    Object fieldSectionObj = rowSectionComponentExt.get(RowSectionComponentExt.FIELD_SECTION);
    assertNotNull(fieldSectionObj);
    assertTrue(fieldSectionObj instanceof List);

    List<Map> fieldSectionList = (List<Map>) fieldSectionObj;
    assertEquals(1, fieldSectionList.size());
    assertNotNull(fieldSectionList.get(0));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试addFieldSection方法添加FieldSectionComponentExt到已有列表
   */
  @Test
  @DisplayName("测试addFieldSection方法 - 添加到已有列表")
  void testAddFieldSectionToExistingList() {
    // 准备已有数据
    Map<String, Object> existingFieldSection = new HashMap<>();
    existingFieldSection.put("fields", Lists.newArrayList());
    List<Map> existingList = Lists.newArrayList(existingFieldSection);
    rowSectionComponentExt.set(RowSectionComponentExt.FIELD_SECTION, existingList);

    // 准备新数据
    FieldSectionComponentExt newFieldSection = new FieldSectionComponentExt();

    // 执行测试
    rowSectionComponentExt.addFieldSection(newFieldSection);

    // 验证结果
    List<Map> fieldSectionList = (List<Map>) rowSectionComponentExt.get(RowSectionComponentExt.FIELD_SECTION);
    assertEquals(2, fieldSectionList.size());
    assertEquals(existingFieldSection, fieldSectionList.get(0));
    assertNotNull(fieldSectionList.get(1));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试addFieldSection方法当fieldSection对象不是List类型时不添加
   */
  @Test
  @DisplayName("测试addFieldSection方法 - fieldSection对象不是List类型时不添加")
  void testAddFieldSectionWhenFieldSectionIsNotList() {
    // 设置fieldSection为非List类型
    rowSectionComponentExt.set(RowSectionComponentExt.FIELD_SECTION, "not a list");
    
    FieldSectionComponentExt fieldSection = new FieldSectionComponentExt();
    
    // 执行测试
    rowSectionComponentExt.addFieldSection(fieldSection);
    
    // 验证结果 - fieldSection应该还是原来的值
    Object fieldSectionObj = rowSectionComponentExt.get(RowSectionComponentExt.FIELD_SECTION);
    assertEquals("not a list", fieldSectionObj);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试type字段的不同对齐方式值
   */
  @Test
  @DisplayName("测试type字段的不同对齐方式值")
  void testDifferentTypeValues() {
    // 测试whole对齐方式
    rowSectionComponentExt.setType("whole");
    assertEquals("whole", rowSectionComponentExt.getType());
    
    // 测试space_between对齐方式
    rowSectionComponentExt.setType("space_between");
    assertEquals("space_between", rowSectionComponentExt.getType());
    
    // 测试up_and_down对齐方式
    rowSectionComponentExt.setType("up_and_down");
    assertEquals("up_and_down", rowSectionComponentExt.getType());
    
    // 测试null值
    rowSectionComponentExt.setType(null);
    assertNull(rowSectionComponentExt.getType());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试addFieldSection和getFieldSection方法的完整流程
   */
  @Test
  @DisplayName("测试addFieldSection和getFieldSection方法的完整流程")
  void testAddAndGetFieldSectionCompleteFlow() {
    // 创建第一个FieldSectionComponentExt
    FieldSectionComponentExt fieldSection1 = new FieldSectionComponentExt();
    FieldComponentExt field1 = new FieldComponentExt();
    field1.setType("field");
    field1.setApiName("field1");
    fieldSection1.addFields(field1);
    
    // 创建第二个FieldSectionComponentExt
    FieldSectionComponentExt fieldSection2 = new FieldSectionComponentExt();
    FieldComponentExt field2 = new FieldComponentExt();
    field2.setType("field");
    field2.setApiName("field2");
    fieldSection2.addFields(field2);
    
    // 添加到RowSectionComponentExt
    rowSectionComponentExt.addFieldSection(fieldSection1);
    rowSectionComponentExt.addFieldSection(fieldSection2);
    
    // 获取并验证结果
    List<FieldSectionComponentExt> result = rowSectionComponentExt.getFieldSection();
    
    assertNotNull(result);
    assertEquals(2, result.size());
    
    // 验证第一个FieldSection
    FieldSectionComponentExt retrievedFieldSection1 = result.get(0);
    List<FieldComponentExt> fields1 = retrievedFieldSection1.getFields();
    assertEquals(1, fields1.size());
    assertEquals("field1", fields1.get(0).getApiName());
    
    // 验证第二个FieldSection
    FieldSectionComponentExt retrievedFieldSection2 = result.get(1);
    List<FieldComponentExt> fields2 = retrievedFieldSection2.getFields();
    assertEquals(1, fields2.size());
    assertEquals("field2", fields2.get(0).getApiName());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试常量值的正确性
   */
  @Test
  @DisplayName("测试常量值")
  void testConstants() {
    assertEquals("type", RowSectionComponentExt.TYPE);
    assertEquals("field_section", RowSectionComponentExt.FIELD_SECTION);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试JsonCompatible接口的实现
   */
  @Test
  @DisplayName("测试JsonCompatible接口实现")
  void testJsonCompatibleInterface() {
    // 设置一些数据
    rowSectionComponentExt.setType("whole");
    
    // 测试toJsonString方法
    String jsonString = rowSectionComponentExt.toJsonString();
    assertNotNull(jsonString);
    assertTrue(jsonString.contains("whole"));
    
    // 测试fromJsonString方法
    RowSectionComponentExt newComponent = new RowSectionComponentExt();
    newComponent.fromJsonString(jsonString);
    assertEquals("whole", newComponent.getType());
  }
}
