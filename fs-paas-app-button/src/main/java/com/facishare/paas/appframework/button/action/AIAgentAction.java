package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.metadata.api.IUdefAction;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2025/5/8
 */
@Slf4j
@Component
public class AIAgentAction implements ActionExecutor {

    private static final String AI_AGENT = "AIAgent";

    @Override
    public ActionExecutorType getType() {
        return ActionExecutorType.AI_AGENT;
    }

    @Override
    public ButtonExecutor.Result invoke(ButtonExecutor.Arg arg, ActionExecutorContext context) {
        IUdefAction action = context.getAction();
        AIAgentActionParameter aiAgentActionParameter = AIAgentActionParameter.fromJsonString(action.getActionParamter());
        return ButtonExecutor.Result.builder()
                .hasReturnValue(true)
                .returnValue(AIAgentActionResult.of(aiAgentActionParameter.getAgentApiName()))
                .returnType(AI_AGENT)
                .build();
    }

    @Data
    private static class AIAgentActionParameter {
        @JsonProperty("agent_api_name")
        private String agentApiName;

        public static AIAgentActionParameter fromJsonString(String jsonStr) {
            return JacksonUtils.fromJson(jsonStr, AIAgentActionParameter.class);
        }
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    public static class AIAgentActionResult {
        private String agentApiName;
    }
}
