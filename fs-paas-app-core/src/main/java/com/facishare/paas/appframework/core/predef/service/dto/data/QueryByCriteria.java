package com.facishare.paas.appframework.core.predef.service.dto.data;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.impl.search.Operator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.regex.Pattern;

/**
 * Created by liyiguang on 2018/1/22.
 */
public interface QueryByCriteria {

    @Data
    class Arg {
        String objectAPIName;
        QueryCriteria queryCriteria;

    }

    @Data
    @Builder
    class Result {
        List<ObjectDataDocument> dataList;
    }

    @Data
    class QueryCriteria {
        private static final Pattern PATTERN = Pattern.compile("and|or");

        @JsonProperty("conditionPattern")
        String criteriaPattern;
        @JsonProperty("conditions")
        List<Criteria> criteriaList;
        int limit;
        int offset;


        public boolean hasOr() {
            return !Strings.isNullOrEmpty(criteriaPattern) && criteriaPattern.contains("or");
        }


        public int getCriteriaPatternSize() {
            String pattern = getCriteriaPattern();
            if (pattern == null) {
                return 0;
            }
            return Splitter.on(PATTERN).omitEmptyStrings().splitToList(pattern).size();
        }

        public String getCriteriaPattern() {
            if (Strings.isNullOrEmpty(criteriaPattern)) {
                return null;
            }
            StringBuilder ret = new StringBuilder();
            StringBuilder index = new StringBuilder();
            for (char ch : criteriaPattern.toCharArray()) {
                if (ch <= '9' && ch >= '0') {
                    index.append(ch);
                    continue;
                } else if (ch == '(' || ch == ')') {
                    if (index.length() > 0) {
                        ret.append(Integer.parseInt(index.toString()) + 1);
                        index.setLength(0); //清空
                    }
                    continue;
                }
                ret.append(ch);
                if (index.length() > 0) {
                    ret.append(Integer.parseInt(index.toString()) + 1);
                    index.setLength(0); //清空
                }
            }
            return ret.toString();
        }
    }

    @Data
    class Criteria {
        @JsonProperty("rowNo")
        String rowNumber;
        @JsonProperty("leftSide")
        Left left;
        @JsonProperty("rightSide")
        Right right;
        String operator;

        public Operator toOperator() {
            switch (operator) {
                case "==":
                    return Operator.EQ;
                case "!=":
                    return Operator.N;
                case ">":
                    return Operator.GT;
                case "<":
                    return Operator.LT;
                case ">=":
                    return Operator.GTE;
                case "<=":
                    return Operator.LTE;
                case "empty":
                    return Operator.IS;
                case "notEmpty":
                    return Operator.ISN;
                default:
                    return null;
            }
        }
    }

    @Data
    class Left {
        String fieldName;
        String fieldType;
    }

    @Data
    class Right {
        String value;
    }
}
