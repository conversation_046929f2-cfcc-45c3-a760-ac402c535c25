package com.facishare.paas.appframework.core.predef.handler.invalid;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.handler.AbstractActTCCActionHandler;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.appframework.core.predef.facade.InvalidActionServiceFacade;
import com.facishare.paas.appframework.core.predef.handler.invalid.InvalidActionHandler.OpResult;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.transaction.tcc.annotation.TccTransactional;
import com.fxiaoke.transaction.tcc.api.context.BranchTransactionalContext;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2023/5/16.
 */
@Slf4j
@Component
@HandlerProvider(name = "defaultInvalidDataInvalidActHandler")
public class DefaultInvalidDataInvalidActHandler extends AbstractActTCCActionHandler<InvalidActionHandler.Arg, InvalidActionHandler.Result> {

    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private InvalidActionServiceFacade invalidActionServiceFacade;

    @Override
    @TccTransactional(name = "handle", confirmMethod = "commit", cancelMethod = "rollback")
    public InvalidActionHandler.Result handle(HandlerContext context, InvalidActionHandler.Arg arg) {
        InvalidActionHandler.Result baseResult = InvalidActionHandler.Result.defaultResult();
        List<IObjectData> needInvalidDataList = arg.needInvalidDataList();
        if (CollectionUtils.empty(needInvalidDataList)) {
            return buildResult(arg, baseResult);
        }
        //作废主对象
        OpResult opResult = bulkInvalidData(context, arg.getObjectDescribe(), needInvalidDataList, false);
        baseResult.setOpResult(opResult);
        List<IObjectData> invalidedMasterDataList = opResult.successDataList();
        if (CollectionUtils.notEmpty(needInvalidDataList) && CollectionUtils.empty(invalidedMasterDataList)) {
            throw new ValidateException(I18N.text(I18NKey.DATA_INVALID_FAILED, opResult.getFailReason()));
        }
        //执行按钮动作
        RuntimeException exception = null;
        try {
            doCurrentAction(context, arg, invalidedMasterDataList);
        } catch (RuntimeException e) {
            log.warn("doCurrentAction fail!", e);
            exception = e;
        }
        //作废GDPR
        invalidGdpr(context, arg, invalidedMasterDataList);
        //作废从对象
        Map<String, List<IObjectData>> detailDataMap = invalidDetailData(context, arg, invalidedMasterDataList);
        baseResult.setDetailObjectData(ObjectDataDocument.ofMap(detailDataMap));
        if (Objects.nonNull(exception)) {
            throw exception;
        }
        return buildResult(arg, baseResult);
    }

    @Override
    public boolean commit(BranchTransactionalContext branchTransactionalContext, HandlerContext context, InvalidActionHandler.Arg arg) {
        return super.commit(branchTransactionalContext, context, arg);
    }

    @Override
    public boolean rollback(BranchTransactionalContext branchTransactionalContext, HandlerContext context, InvalidActionHandler.Arg arg) {
        return super.rollback(branchTransactionalContext, context, arg);
    }

    private InvalidActionHandler.Result buildResult(InvalidActionHandler.Arg arg, InvalidActionHandler.Result result) {
        OpResult mergedOpResult = arg.mergeOpResult(result.getOpResult());
        StandardBulkInvalidAction.Result actionResult = StandardBulkInvalidAction.Result.builder()
                .objectDataList(mergedOpResult.getSuccessDataList())
                .failureObjectDataList(mergedOpResult.getFailDataList())
                .failureNotice(mergedOpResult.getFailReason())
                .build();
        result.setInterfaceResult(actionResult);
        return result;
    }

    private OpResult bulkInvalidData(HandlerContext context, IObjectDescribe describe, List<IObjectData> dataList, boolean recordLog) {
        return invalidActionServiceFacade.bulkInvalidData(context.getUser(), describe, dataList,
                needTriggerWorkFlow(context, describe), recordLog);
    }

    private boolean needTriggerWorkFlow(HandlerContext context) {
        return !context.skipWorkFlow();
    }

    private boolean needTriggerWorkFlow(HandlerContext context, IObjectDescribe describe) {
        return !describe.isBigObject() && !describe.isPublicObject() && !context.skipWorkFlow();
    }

    private void doCurrentAction(HandlerContext context, InvalidActionHandler.Arg arg, List<IObjectData> invalidedMasterDataList) {
        //2021/12/20 更新字段操作 留一个坑如果过修改字段为从对象要改前面作废从对象的逻辑，现在直接传了一个new HashMap()
        //2021/12/21 同步批量不给执行更新字段等动作
        if (invalidedMasterDataList.size() == 1) {
            Map<String, Object> args = Maps.newHashMap();
            args.put("args", arg.getInterfaceArg().getArgs());
            ActionContext actionContext = new ActionContext(context.getRequestContext(), arg.getObjectApiName(), context.getInterfaceCode());
            IObjectDescribe objectDescribe = arg.getObjectDescribe();
            infraServiceFacade.doCurrentAction(args, actionContext, objectDescribe, invalidedMasterDataList.get(0), Maps.newHashMap());
        }
    }

    private Map<String, List<IObjectData>> invalidDetailData(HandlerContext context, InvalidActionHandler.Arg arg, List<IObjectData> masterDataList) {
        return invalidActionServiceFacade.invalidDetailData(context.getUser(), masterDataList,
                arg.getDetailDescribes(), needTriggerWorkFlow(context));
    }

    private void invalidGdpr(HandlerContext context, InvalidActionHandler.Arg arg, List<IObjectData> dataList) {
        List<String> ids = dataList.stream().map(DBRecord::getId).collect(Collectors.toList());
        ParallelUtils.createBackgroundTask()
                .submit(() -> infraServiceFacade.bulkInvalidGdprLegalBase(context.getUser(), arg.getObjectApiName(), ids))
                .run();
    }
}
