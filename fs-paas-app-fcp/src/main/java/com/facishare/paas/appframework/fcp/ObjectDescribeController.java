package com.facishare.paas.appframework.fcp;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.ObjectDesignerService;
import com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.CheckAndFindLatest;
import com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.FindDescribeByApiName;
import com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.FindDescribeListByApiName;
import com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.FindDraftByApiName;
import com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.FindDraftListByApiName;
import com.facishare.paas.appframework.core.model.ContextManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by zhouwr on 2017/11/2
 */
@Component
@FcpService("objectDescribe")
public class ObjectDescribeController {

    @Autowired
    private ObjectDesignerService objectDesignerService;

    /**
     * 查询自定义对象草描述草稿
     *
     * @param arg
     * @return
     */
    @FcpMethod("findDescribeByApiName")
    public FindDescribeByApiName.Result findDescribeByApiName(FindDescribeByApiName.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("describe", "findDescribeByApiName");
        return objectDesignerService.findDescribeByApiName(arg, context);
    }

    @FcpMethod("findDraftByApiName")
    public FindDraftByApiName.Result findDraftByApiName(FindDraftByApiName.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("describe", "findDraftByApiName");
        return objectDesignerService.findDraftByApiName(arg, context);
    }

    //11.13为设计器开发,因为findByExample中不返回fieldList了。
    @FcpMethod("findDescribeListByApiName")
    public FindDescribeListByApiName.Result findDescribeListByApiName(FindDescribeListByApiName.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("describe", "FindDescribeListByApiName");
        return objectDesignerService.findDescribeListByApiName(arg, context);
    }

    @FcpMethod("findDraftListByApiName")
    public FindDraftListByApiName.Result findDraftListByApiName(FindDraftListByApiName.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("describe", "FindDescribeListByApiName");
        return objectDesignerService.findDraftListByApiName(arg, context);
    }

    @FcpMethod("checkAndFindLatest")
    public CheckAndFindLatest.Result checkAndFindLatest(CheckAndFindLatest.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("describe", "checkAndFindLatest");
        return objectDesignerService.checkAndFindLatest(arg, context);
    }
}
