package com.facishare.paas.appframework.privilege.util;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GenerateByAI
 * 测试内容描述：测试Headers枚举类的方法
 */
@ExtendWith(MockitoExtension.class)
class HeadersTest {

    @BeforeEach
    void setUp() {
        // 清理RequestContext
        RequestContextManager.removeContext();
    }

    @AfterEach
    void tearDown() {
        // 清理RequestContext
        RequestContextManager.removeContext();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildHeader方法，验证能正确构建包含x-fs-ei的header
     */
    @ParameterizedTest
    @ValueSource(strings = {"123456", "789012", "111111", ""})
    @DisplayName("参数化测试 - buildHeader方法")
    void testBuildHeader(String ei) {
        // 执行被测试方法
        Map<String, String> result = Headers.PAAS_PRIVILEGE_HEADDER.buildHeader(ei);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey("x-fs-ei"));
        assertEquals(ei, result.get("x-fs-ei"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildHeader方法处理null值的情况
     */
    @Test
    @DisplayName("测试buildHeader方法处理null值")
    void testBuildHeaderWithNull() {
        // 执行被测试方法
        Map<String, String> result = Headers.PAAS_PRIVILEGE_HEADDER.buildHeader(null);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey("x-fs-ei"));
        assertNull(result.get("x-fs-ei"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试defaultHeader方法，验证能从RequestContext中获取tenantId并构建header
     */
    @Test
    @DisplayName("测试defaultHeader方法")
    void testDefaultHeader() {
        // 准备测试数据
        String tenantId = "123456";
        User user = new User(tenantId, "78910");
        RequestContext requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(user)
                .appId("test-app")
                .requestSource(RequestContext.RequestSource.CEP)
                .build();
        RequestContextManager.setContext(requestContext);
        
        // 执行被测试方法
        Map<String, String> result = Headers.PAAS_PRIVILEGE_HEADDER.defaultHeader();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey("x-fs-ei"));
        assertEquals(tenantId, result.get("x-fs-ei"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试defaultHeader方法在没有RequestContext时的行为
     */
    @Test
    @DisplayName("测试defaultHeader方法在没有RequestContext时抛出异常")
    void testDefaultHeaderWithoutContext() {
        // 确保没有RequestContext
        RequestContextManager.removeContext();
        
        // 执行被测试方法并验证抛出异常
        assertThrows(NullPointerException.class, () -> {
            Headers.PAAS_PRIVILEGE_HEADDER.defaultHeader();
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Headers枚举值的存在性
     */
    @Test
    @DisplayName("测试Headers枚举值")
    void testHeadersEnumValues() {
        // 验证枚举值存在
        assertNotNull(Headers.PAAS_PRIVILEGE_HEADDER);
        
        // 验证枚举值数量
        Headers[] values = Headers.values();
        assertEquals(1, values.length);
        assertEquals(Headers.PAAS_PRIVILEGE_HEADDER, values[0]);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Headers枚举的valueOf方法
     */
    @Test
    @DisplayName("测试Headers枚举的valueOf方法")
    void testHeadersValueOf() {
        // 验证valueOf方法
        assertEquals(Headers.PAAS_PRIVILEGE_HEADDER, Headers.valueOf("PAAS_PRIVILEGE_HEADDER"));
        
        // 验证无效值抛出异常
        assertThrows(IllegalArgumentException.class, () -> {
            Headers.valueOf("INVALID_HEADER");
        });
    }
} 