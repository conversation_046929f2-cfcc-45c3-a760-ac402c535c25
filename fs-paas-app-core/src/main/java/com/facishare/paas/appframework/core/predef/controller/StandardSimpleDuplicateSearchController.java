package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.ObjectDuplicatedSearchService;
import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.GetResult;
import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.SimpleSearch;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.DuplicatedSearchExt;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchOrderByType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.SecurityEventTrackingDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.trace.TraceContext;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

public class StandardSimpleDuplicateSearchController extends PreDefineController<SimpleSearch.Arg, GetResult.Result> {
    private ObjectDuplicatedSearchService duplicatedSearchService =
            SpringUtil.getContext().getBean("objectDuplicatedSearchService", ObjectDuplicatedSearchService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected GetResult.Result doService(SimpleSearch.Arg arg) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), arg.getDescribeApiName());

        IDuplicatedSearch duplicatedSearchRule = null;
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(controllerContext.getTenantId(), arg.getDescribeApiName())) {
            if (StringUtils.isEmpty(arg.getDuplicateRuleApiName())) {
                duplicatedSearchRule = serviceFacade.getEnableDuplicateSearchRuleList(arg.getDescribeApiName(), IDuplicatedSearch.Type.TOOL, controllerContext.getTenantId(),
                        false, DuplicateSearchOrderByType.ORDER_BY_SORT).stream().findFirst().orElse(null);
            } else {
                duplicatedSearchRule = serviceFacade.findDuplicatedSearchByRuleApiName(arg.getDescribeApiName(), arg.getDuplicateRuleApiName(), controllerContext.getTenantId(), false);
            }
        } else {
            duplicatedSearchRule = serviceFacade.findDuplicatedSearchByApiNameAndType(controllerContext.getTenantId(),
                    arg.getDescribeApiName(), IDuplicatedSearch.Type.TOOL, false);
        }

        List<String> conditionFields = duplicatedSearchService.getConditionFields(duplicatedSearchRule);
        IObjectData objectData = duplicatedSearchService.getSimpleSearchObjectData(arg.getKeyword(), conditionFields, objectDescribe);
        GetResult.Arg build = GetResult.Arg.builder()
                .describeApiName(arg.getDescribeApiName())
                .relatedApiName(arg.getRelatedApiName())
                .type(IDuplicatedSearch.Type.TOOL)
                .objectData(ObjectDataDocument.of(objectData))
                .isNeedDuplicate(arg.getIsNeedDuplicate())
                .duplicateRuleApiName(arg.getDuplicateRuleApiName())
                .includeObjectDescribes(arg.getIncludeObjectDescribes())
                .pageNumber(arg.getPageNumber())
                .pageSize(arg.getPageSize())
                .fromSimpleSearch(true)
                .build();
        ControllerContext controllerContext = new ControllerContext(this.controllerContext.getRequestContext(), arg.getDescribeApiName(), "DuplicateSearch");
        return serviceFacade.triggerController(controllerContext, build, GetResult.Result.class);
    }

    @Override
    protected GetResult.Result after(SimpleSearch.Arg arg, GetResult.Result result) {
        postMessage(result.getDuplicatedSearch(), arg.getKeyword());
        return super.after(arg, result);
    }

    private void postMessage(IDuplicatedSearch duplicatedSearch, String keyword) {
        if (Objects.isNull(duplicatedSearch)
                || !DuplicatedSearchExt.of(duplicatedSearch).isTool()
                || StringUtils.isBlank(keyword)
                || !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.REPORT_DUPLICATE_GRAY_EI, controllerContext.getTenantId())
                || !AppFrameworkConfig.isPostMessage(duplicatedSearch.getDescribeApiName())) {
            return;
        }
        SecurityEventTrackingDTO trackingDTO = SecurityEventTrackingDTO.builder()
                .eventId(UUID.randomUUID().toString())
                .traceId(TraceContext.get().getTraceId())
                .eventTime(System.currentTimeMillis())
                .tenantId(controllerContext.getTenantId())
                .userId(controllerContext.getUser().getUserId())
                .sourceIp(Objects.isNull(RequestContextManager.getContext()) ? "" : RequestContextManager.getContext().getClientIp())
                .deviceId(Objects.isNull(RequestContextManager.getContext()) ? "" : RequestContextManager.getContext().getAttribute(RequestContext.FS_DEVICE_ID))
                .deviceType(Objects.isNull(RequestContextManager.getContext()) ? "" : RequestContextManager.getContext().getAttribute(RequestContext.FS_DEVICE_TYPE))
                .objects(controllerContext.getObjectApiName())
                .operation("duplicationCheck")
                .actionType("UIDuplicationCheckEvent")
                .keywords(keyword)
                .build();

        BizLogClient.send("biz-log-security-event-tracking", Pojo2Protobuf.toMessage(trackingDTO, com.fxiaoke.log.SecurityEventTrackingDTO.class).toByteArray());
    }
}
