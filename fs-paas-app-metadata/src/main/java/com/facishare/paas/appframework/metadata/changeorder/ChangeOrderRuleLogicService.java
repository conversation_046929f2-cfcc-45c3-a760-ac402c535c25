package com.facishare.paas.appframework.metadata.changeorder;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.repository.model.MtChangeOrderRule;

import java.util.List;
import java.util.Optional;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/3/29
 */
public interface ChangeOrderRuleLogicService {

    List<MtChangeOrderRule> findAll(User user);

    List<MtChangeOrderRule> findByDescribeApiName(User user, String describeApiName);

    Optional<MtChangeOrderRule> findByRuleName(User user, String ruleName, boolean onTime);

    Optional<MtChangeOrderRule> findByRuleName(User user, String ruleName);

    void checkCount(User user, String describeApiName);

    void create(User user, MtChangeOrderRule changeOrderRule);

    void update(User user, MtChangeOrderRule changeOrderRule);

    void enable(User user, String ruleName);

    void disable(User user, String ruleName);

    void delete(User user, String ruleName);
}
