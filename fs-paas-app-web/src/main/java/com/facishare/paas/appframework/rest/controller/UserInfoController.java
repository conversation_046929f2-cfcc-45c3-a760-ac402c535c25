package com.facishare.paas.appframework.rest.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.jaxrs.annotation.RestAPI;
import com.facishare.paas.appframework.rest.dto.data.FindAdminUsers;
import com.facishare.paas.appframework.rest.dto.data.FindUserNameByIds;
import com.facishare.paas.appframework.rest.service.UserInfoRestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import java.util.List;

@Controller
@Path("/v1/inner/rest/user")
@RestAPI
public class UserInfoController {

    @Autowired
    private UserInfoRestService userInfoRestService;

    @POST
    @Path("/getUserInfos")
    public FindUserNameByIds.Result findUserNameByIds(String json) {

        RequestContext requestContext = RequestContextManager.getContext();
        List<String> idList = JSON.parseArray(json, String.class);
        FindUserNameByIds.Arg arg = FindUserNameByIds.Arg.builder().userIdList(idList).build();

        return userInfoRestService.findNameByIds(arg, requestContext);
    }

    @GET
    @Path("/getAdminUsers")
    public FindAdminUsers.Result findUsesByRole() {
        RequestContext requestContext = RequestContextManager.getContext();
        return userInfoRestService.findAdminUsers(requestContext);
    }

}
