package com.facishare.crm.privilege.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.privilege.util.PrivilegeFieldsUtils;
import com.facishare.qixin.plugin.model.arg.BatchSessionSandwichUpdatedArg;
import com.facishare.qixin.plugin.model.arg.SessionSandwichUpdatedArg;
import com.facishare.qixin.plugin.service.SessionSandwichApiService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by luxin on 2016/12/26.
 */
@Slf4j
@Service("appPushSessionService")
public class PushSessionService {
    @Autowired
    private SessionSandwichApiService sessionSandwichApiService;

    /**
     * @param userIds
     * @param ea
     * @param pushSessionObjects
     */
    public void pushSessionByFieldPrivilegeUpdate(List<Integer> userIds, String ea, List<String> pushSessionObjects) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        BatchSessionSandwichUpdatedArg arg = new BatchSessionSandwichUpdatedArg();
        List<SessionSandwichUpdatedArg> sessionSandwichUpdatedArgList = Lists.newArrayList();
        List<String> pushSessionObjectList = Lists.newArrayList();
        if (pushSessionObjects == null) {
            pushSessionObjectList = PrivilegeFieldsUtils.getPushSessionObjects();
        } else {
            pushSessionObjectList = pushSessionObjects;
        }
        for (Integer userId : userIds) {
            SessionSandwichUpdatedArg sessionSandwichUpdatedArg = new SessionSandwichUpdatedArg();
            sessionSandwichUpdatedArg.setType(14);//字段权限变更
            sessionSandwichUpdatedArg.setEmployeeId(userId);
            sessionSandwichUpdatedArg.setEnterpriseAccount(ea);
            Map<String, Object> content = Maps.newHashMap();
            content.put("timestamp", System.currentTimeMillis());
            content.put("objectList", pushSessionObjectList);
            sessionSandwichUpdatedArg.setContent(JSONObject.toJSONString(content));
            sessionSandwichUpdatedArgList.add(sessionSandwichUpdatedArg);
        }
        arg.setEnterpriseAccount(ea);
        arg.setArgs(sessionSandwichUpdatedArgList);
        try {
            sessionSandwichApiService.batchSessionSandwichUpdated(arg);
            log.info("sessionSandwichApiService done!");
        } catch (Exception e) {
            log.error("sessionSandwichApiService failed! arg -{}", arg, e);
        }
    }
}
