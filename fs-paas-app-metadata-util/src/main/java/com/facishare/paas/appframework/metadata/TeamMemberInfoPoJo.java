package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.util.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Maps;
import lombok.*;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * Created by ysb on 2017/5/5.
 */
@Data
@AllArgsConstructor
@RequiredArgsConstructor()
@NoArgsConstructor
@EqualsAndHashCode(exclude = {"teamMemberRole", "teamMemberRoleList", "teamMemberPermissionType", "outTenantName"})
@Builder
public class TeamMemberInfoPoJo implements Comparable<TeamMemberInfoPoJo> {
    //团队成员ID列表
    @NonNull
    @JSONField(name = "teamMemberEmployee")
    @JsonProperty("teamMemberEmployee")
    private List<String> teamMemberEmployee;
    //团队成员角色
    @NonNull
    @JSONField(name = "teamMemberRole")
    @JsonProperty("teamMemberRole")
    private String teamMemberRole;
    //团队成员角色（多个角色）
    @JSONField(name = "teamMemberRoleList")
    @JsonProperty("teamMemberRoleList")
    private List<String> teamMemberRoleList;
    //团队成员权限
    @NonNull
    @JSONField(name = "teamMemberPermissionType")
    @JsonProperty("teamMemberPermissionType")
    private String teamMemberPermissionType;

    @JSONField(name = "outTenantId")
    @JsonProperty("outTenantId")
    private String outTenantId;

    @JSONField(name = "outTenantName")
    @JsonProperty("outTenantName")
    private String outTenantName;

    @JSONField(name = "outUserName")
    @JsonProperty("outUserName")
    private String outUserName;

    @JSONField(name = "outUserProfile")
    @JsonProperty("outUserProfile")
    private String outUserProfile;

    //0:内部相关团队，2:外部相关团队
    @JSONField(name = "sourceType")
    @JsonProperty("sourceType")
    private String sourceType;

    //团队成员类型
    @JSONField(name = "teamMemberType")
    @JsonProperty("teamMemberType")
    private String teamMemberType;
    //下游企业信息
    @JSONField(name = "outTeamMemberEmployee")
    @JsonProperty("outTeamMemberEmployee")
    private List<TeamMemberDTO> outTeamMemberEmployee;

    @JSONField(name = "teamMemberName")
    @JsonProperty("teamMemberName")
    private String teamMemberName;


    @JSONField(name = "teamMemberDeptCascade")
    @JsonProperty("teamMemberDeptCascade")
    private String teamMemberDeptCascade;

    @JSONField(name = "teamMemberRoleNames")
    @JsonProperty("teamMemberRoleNames")
    private String teamMemberRoleNames;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TeamMemberDTO {
        private String userId;
        private String outTenantId;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            TeamMemberDTO that = (TeamMemberDTO) o;
            return Objects.equals(userId, that.userId) && Objects.equals(outTenantId, that.outTenantId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(userId, outTenantId);
        }
    }

    public List<TeamMemberDTO> getOutTeamMemberEmployee() {
        if (TeamMember.MemberType.ROLE.getValue().equals(teamMemberType)) {
            if (CollectionUtils.empty(outTeamMemberEmployee)) {
                return outTeamMemberEmployee;
            }
            for (TeamMemberDTO teamMemberDTO : outTeamMemberEmployee) {
                teamMemberDTO.setOutTenantId("-100000000");
            }
        }
        return outTeamMemberEmployee;
    }

    public boolean isOutTeamMember() {
        return Objects.equals(sourceType, "2");
    }

    public boolean isInnerEmployeeMember() {
        return Objects.equals(TeamMember.MemberType.EMPLOYEE.getValue(), TeamMember.MemberType.of(getTeamMemberType()).getValue());
    }

    public boolean isInnerDepartmentMember() {
        return Objects.equals(TeamMember.MemberType.DEPARTMENT.getValue(), TeamMember.MemberType.of(getTeamMemberType()).getValue());
    }

    public boolean isErDepartmentMember() {
        return Objects.equals(TeamMember.MemberType.INTERCONNECT_DEPARTMENT.getValue(), TeamMember.MemberType.of(getTeamMemberType()).getValue());
    }

    public String parseInnerMemberValue() {
        if (TeamMember.MemberType.of(getTeamMemberType()).isInnerMember()) {
            if (CollectionUtils.empty(getTeamMemberEmployee())) {
                return null;
            } else {
                return getTeamMemberEmployee().get(0);
            }
        }
        return null;
    }

    public boolean isDeptCascaded() {
        return Objects.equals("1", teamMemberDeptCascade);
    }


    //先按照角色排序,值越小越靠前,再按照成员权限排序,越小越靠前
    @Override
    public int compareTo(@NotNull TeamMemberInfoPoJo o) {
        if (CollectionUtils.empty(teamMemberRoleList) && CollectionUtils.empty(o.getTeamMemberRoleList())) {
            int i = Integer.parseInt(this.teamMemberRole) - Integer.parseInt(o.getTeamMemberRole());
            if (i != 0) return i;
        }
        return Integer.parseInt(o.getTeamMemberPermissionType()) - Integer.parseInt(teamMemberPermissionType);
    }

    public Map<String, Object> toMap() {
        Map<String, Object> result = Maps.newHashMap();
        result.put("teamMemberEmployee", teamMemberEmployee);
        result.put("teamMemberRoleList", teamMemberRoleList);
        result.put("outTeamMemberEmployee", outTeamMemberEmployee);
        result.put("teamMemberPermissionType", teamMemberPermissionType);
        result.put("sourceType", sourceType);
        if (StringUtils.isBlank(teamMemberType)) {
            teamMemberType = TeamMember.MemberType.EMPLOYEE.getValue();
        }
        result.put("teamMemberType", teamMemberType);
        if (StringUtils.isBlank(teamMemberDeptCascade)) {
            teamMemberDeptCascade = "0";
        }
        result.put("teamMemberDeptCascade", teamMemberDeptCascade);
        return result;
    }

    public boolean validate() {
        TeamMember.MemberType memberType = TeamMember.MemberType.of(teamMemberType);
        if (memberType == TeamMember.MemberType.EMPLOYEE
                || memberType == TeamMember.MemberType.DEPARTMENT) {
            if (!ObjectUtils.isNumber(teamMemberEmployee, Function.identity())) {
                return false;
            }
            return ObjectUtils.isNumber(outTeamMemberEmployee, TeamMemberDTO::getUserId);
        }
        return true;
    }

}