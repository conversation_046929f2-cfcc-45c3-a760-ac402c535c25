package com.facishare.paas.appframework.common.service.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface QueryByTargetList {

    @Data
    @Builder
    class Arg {
        private String targetType;
        private List<String> targetValues;
        private int limit;
    }

    @Data
    class Result {
        private Integer code;
        private String message;
        private List<ReferenceData> values;
    }
}
