package com.facishare.paas.appframework.core.timezone;

import com.facishare.paas.appframework.core.timezone.annotation.ObjectDataField;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.Objects;
import java.util.function.Function;

/**
 * create by z<PERSON><PERSON> on 2021/06/17
 */
public class SingleObjectDataFieldConvert extends AbstractObjectDataFieldConvert {

    public static SingleObjectDataFieldConvert getInstance() {
        return Helper.INSTANCE;
    }

    private SingleObjectDataFieldConvert() {
    }

    @Override
    public ObjectDataField.Type getType() {
        return ObjectDataField.Type.SINGLE;
    }

    @Override
    public <T> T convert2SystemZone(T value, Function<String, IObjectDescribe> findDescribe) {
        Objects.requireNonNull(findDescribe);
        if (Objects.isNull(value)) {
            return null;
        }
        IObjectData objectData = toObjectData(value);
        String describeApiName = objectData.getDescribeApiName();
        IObjectDescribe describe = findDescribe.apply(describeApiName);
        IObjectData data = ObjectDataExt.convertDateFieldValueToSystemZone(describe, objectData);
        return toEntity(data, value);
    }

    @Override
    public <T> T convert2CustomZone(T value, Function<String, IObjectDescribe> findDescribe) {
        Objects.requireNonNull(findDescribe);
        if (Objects.isNull(value)) {
            return null;
        }
        IObjectData objectData = toObjectData(value);
        String describeApiName = objectData.getDescribeApiName();
        IObjectDescribe describe = findDescribe.apply(describeApiName);
        IObjectData data = ObjectDataExt.convertDateFieldValueToCustomZone(describe, objectData);
        return toEntity(data, value);
    }

    private static final class Helper {
        private static final SingleObjectDataFieldConvert INSTANCE = new SingleObjectDataFieldConvert();
    }
}
