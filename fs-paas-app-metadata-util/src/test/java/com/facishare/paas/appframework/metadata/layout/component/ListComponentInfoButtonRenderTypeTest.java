package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.appframework.metadata.ButtonUsePageType;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ListComponentInfoButtonRenderTypeTest {

  @Mock
  private IListComponentInfo listComponentInfo;

  /**
   * GenerateByAI
   * 测试内容描述：测试ButtonRenderType枚举的基本属性
   */
  @Test
  @DisplayName("正常场景 - 测试ButtonRenderType枚举基本属性")
  void testButtonRenderTypeBasicProperties() {
    // 测试LIST_NORMAL
    assertEquals("list_normal", ListComponentInfo.ButtonRenderType.LIST_NORMAL.getType(), 
                 "LIST_NORMAL类型应正确");
    
    // 测试LIST_BATCH
    assertEquals("list_batch", ListComponentInfo.ButtonRenderType.LIST_BATCH.getType(), 
                 "LIST_BATCH类型应正确");
    
    // 测试LIST_SINGLE
    assertEquals("list_single", ListComponentInfo.ButtonRenderType.LIST_SINGLE.getType(), 
                 "LIST_SINGLE类型应正确");
    
    // 测试NORMAL
    assertEquals("normal", ListComponentInfo.ButtonRenderType.NORMAL.getType(), 
                 "NORMAL类型应正确");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试fromUsePageType静态方法
   */
  @Test
  @DisplayName("正常场景 - 测试fromUsePageType静态方法")
  void testFromUsePageType() {
    // 测试各种页面类型映射
    assertEquals(ListComponentInfo.ButtonRenderType.LIST_NORMAL, 
                 ListComponentInfo.ButtonRenderType.fromUsePageType(ButtonUsePageType.ListNormal),
                 "ListNormal应映射到LIST_NORMAL");
    
    assertEquals(ListComponentInfo.ButtonRenderType.LIST_BATCH, 
                 ListComponentInfo.ButtonRenderType.fromUsePageType(ButtonUsePageType.ListBatch),
                 "ListBatch应映射到LIST_BATCH");
    
    assertEquals(ListComponentInfo.ButtonRenderType.LIST_SINGLE, 
                 ListComponentInfo.ButtonRenderType.fromUsePageType(ButtonUsePageType.DataList),
                 "DataList应映射到LIST_SINGLE");
    
    assertEquals(ListComponentInfo.ButtonRenderType.NORMAL, 
                 ListComponentInfo.ButtonRenderType.fromUsePageType(ButtonUsePageType.Edit),
                 "Edit应映射到NORMAL");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试fromUsePageType静态方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试fromUsePageType静态方法处理null值")
  void testFromUsePageType_NullValue() {
    assertNull(ListComponentInfo.ButtonRenderType.fromUsePageType(null), 
               "null页面类型应返回null");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getTypeByUsePage静态方法
   */
  @Test
  @DisplayName("正常场景 - 测试getTypeByUsePage静态方法")
  void testGetTypeByUsePage() {
    // 测试各种页面类型到类型字符串的映射
    assertEquals("list_normal", 
                 ListComponentInfo.ButtonRenderType.getTypeByUsePage(ButtonUsePageType.ListNormal),
                 "ListNormal应返回list_normal");
    
    assertEquals("list_batch", 
                 ListComponentInfo.ButtonRenderType.getTypeByUsePage(ButtonUsePageType.ListBatch),
                 "ListBatch应返回list_batch");
    
    assertEquals("list_single", 
                 ListComponentInfo.ButtonRenderType.getTypeByUsePage(ButtonUsePageType.DataList),
                 "DataList应返回list_single");
    
    assertEquals("normal", 
                 ListComponentInfo.ButtonRenderType.getTypeByUsePage(ButtonUsePageType.Edit),
                 "Edit应返回normal");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getTypeByUsePage静态方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试getTypeByUsePage静态方法处理null值")
  void testGetTypeByUsePage_NullValue() {
    assertNull(ListComponentInfo.ButtonRenderType.getTypeByUsePage(null), 
               "null页面类型应返回null");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getUsePageByType静态方法
   */
  @Test
  @DisplayName("正常场景 - 测试getUsePageByType静态方法")
  void testGetUsePageByType() {
    // 测试类型字符串到页面类型的映射
    assertEquals(ButtonUsePageType.ListNormal, 
                 ListComponentInfo.ButtonRenderType.getUsePageByType("list_normal"),
                 "list_normal应返回ListNormal");
    
    assertEquals(ButtonUsePageType.ListBatch, 
                 ListComponentInfo.ButtonRenderType.getUsePageByType("list_batch"),
                 "list_batch应返回ListBatch");
    
    assertEquals(ButtonUsePageType.DataList, 
                 ListComponentInfo.ButtonRenderType.getUsePageByType("list_single"),
                 "list_single应返回DataList");
    
    assertEquals(ButtonUsePageType.Edit, 
                 ListComponentInfo.ButtonRenderType.getUsePageByType("normal"),
                 "normal应返回Edit");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getUsePageByType静态方法 - 未知类型场景
   */
  @Test
  @DisplayName("边界场景 - 测试getUsePageByType静态方法处理未知类型")
  void testGetUsePageByType_UnknownType() {
    assertNull(ListComponentInfo.ButtonRenderType.getUsePageByType("unknown_type"), 
               "未知类型应返回null");
    
    assertNull(ListComponentInfo.ButtonRenderType.getUsePageByType(null), 
               "null类型应返回null");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试LIST_NORMAL的getExposedButtonDefaultSize方法
   */
  @Test
  @DisplayName("正常场景 - 测试LIST_NORMAL的getExposedButtonDefaultSize方法")
  void testListNormal_GetExposedButtonDefaultSize() {
    // 测试null参数
    assertEquals(1, ListComponentInfo.ButtonRenderType.LIST_NORMAL.getExposedButtonDefaultSize(null),
                 "null参数应返回默认值1");
    
    // 测试有设置exposedButton的情况
    when(listComponentInfo.getExposedButton()).thenReturn(3);
    assertEquals(3, ListComponentInfo.ButtonRenderType.LIST_NORMAL.getExposedButtonDefaultSize(listComponentInfo),
                 "应返回设置的exposedButton值");
    
    // 测试相关列表页面类型
    when(listComponentInfo.getExposedButton()).thenReturn(null);
    when(listComponentInfo.getPageType()).thenReturn(IComponentInfo.PAGE_TYPE_RELATED);
    assertEquals(-1, ListComponentInfo.ButtonRenderType.LIST_NORMAL.getExposedButtonDefaultSize(listComponentInfo),
                 "相关列表页面应返回-1");
    
    // 测试普通页面类型
    when(listComponentInfo.getPageType()).thenReturn("normal_page");
    assertEquals(1, ListComponentInfo.ButtonRenderType.LIST_NORMAL.getExposedButtonDefaultSize(listComponentInfo),
                 "普通页面应返回默认值1");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试LIST_BATCH的getExposedButtonDefaultSize方法
   */
  @Test
  @DisplayName("正常场景 - 测试LIST_BATCH的getExposedButtonDefaultSize方法")
  void testListBatch_GetExposedButtonDefaultSize() {
    // LIST_BATCH总是返回-1
    assertEquals(-1, ListComponentInfo.ButtonRenderType.LIST_BATCH.getExposedButtonDefaultSize(null),
                 "LIST_BATCH应总是返回-1");
    
    assertEquals(-1, ListComponentInfo.ButtonRenderType.LIST_BATCH.getExposedButtonDefaultSize(listComponentInfo),
                 "LIST_BATCH应总是返回-1");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试LIST_SINGLE的getExposedButtonDefaultSize方法
   */
  @Test
  @DisplayName("正常场景 - 测试LIST_SINGLE的getExposedButtonDefaultSize方法")
  void testListSingle_GetExposedButtonDefaultSize() {
    // 测试null参数
    assertEquals(0, ListComponentInfo.ButtonRenderType.LIST_SINGLE.getExposedButtonDefaultSize(null),
                 "null参数应返回默认值0");
    
    // 测试有设置exposedButton的情况
    when(listComponentInfo.getExposedButton()).thenReturn(2);
    assertEquals(2, ListComponentInfo.ButtonRenderType.LIST_SINGLE.getExposedButtonDefaultSize(listComponentInfo),
                 "应返回设置的exposedButton值");
    
    // 测试未设置exposedButton的情况
    when(listComponentInfo.getExposedButton()).thenReturn(null);
    assertEquals(0, ListComponentInfo.ButtonRenderType.LIST_SINGLE.getExposedButtonDefaultSize(listComponentInfo),
                 "未设置exposedButton应返回默认值0");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试NORMAL的getExposedButtonDefaultSize方法
   */
  @Test
  @DisplayName("正常场景 - 测试NORMAL的getExposedButtonDefaultSize方法")
  void testNormal_GetExposedButtonDefaultSize() {
    // 测试null参数
    assertEquals(1, ListComponentInfo.ButtonRenderType.NORMAL.getExposedButtonDefaultSize(null),
                 "null参数应返回默认值1");
    
    // 测试有设置exposedButton的情况
    when(listComponentInfo.getExposedButton()).thenReturn(5);
    assertEquals(5, ListComponentInfo.ButtonRenderType.NORMAL.getExposedButtonDefaultSize(listComponentInfo),
                 "应返回设置的exposedButton值");
    
    // 测试未设置exposedButton的情况
    when(listComponentInfo.getExposedButton()).thenReturn(null);
    assertEquals(1, ListComponentInfo.ButtonRenderType.NORMAL.getExposedButtonDefaultSize(listComponentInfo),
                 "未设置exposedButton应返回默认值1");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试枚举值的完整性
   */
  @Test
  @DisplayName("正常场景 - 测试枚举值完整性")
  void testEnumCompleteness() {
    // 验证所有枚举值都存在
    ListComponentInfo.ButtonRenderType[] values = ListComponentInfo.ButtonRenderType.values();
    assertEquals(4, values.length, "应有4个枚举值");
    
    // 验证每个枚举值都有对应的类型字符串
    for (ListComponentInfo.ButtonRenderType renderType : values) {
      assertNotNull(renderType.getType(), "每个枚举值都应有类型字符串");
      assertFalse(renderType.getType().isEmpty(), "类型字符串不应为空");
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试双向映射的一致性
   */
  @Test
  @DisplayName("正常场景 - 测试双向映射一致性")
  void testBidirectionalMappingConsistency() {
    // 测试类型字符串和页面类型的双向映射一致性
    for (ListComponentInfo.ButtonRenderType renderType : ListComponentInfo.ButtonRenderType.values()) {
      String type = renderType.getType();
      
      // 从类型获取页面类型，再从页面类型获取类型，应该一致
      ButtonUsePageType usePageType = ListComponentInfo.ButtonRenderType.getUsePageByType(type);
      if (usePageType != null) {
        String mappedType = ListComponentInfo.ButtonRenderType.getTypeByUsePage(usePageType);
        assertEquals(type, mappedType, "双向映射应保持一致: " + type);
      }
    }
  }
}
