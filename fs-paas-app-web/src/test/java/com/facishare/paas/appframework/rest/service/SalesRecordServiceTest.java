package com.facishare.paas.appframework.rest.service;

import com.facishare.paas.appframework.coordination.CrmService;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.privilege.DataAuthServiceProxy;
import com.facishare.paas.appframework.privilege.dto.ObjectsPermission;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.appframework.rest.dto.data.CheckPermission;
import com.facishare.paas.appframework.rest.dto.data.FeedDataByJson;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 销售记录服务单元测试
 */
@ExtendWith(MockitoExtension.class)
class SalesRecordServiceTest {

    @Mock
    private DataAuthServiceProxy dataAuthServiceProxy;

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private CrmService crmService;

    @InjectMocks
    private SalesRecordService salesRecordService;

    private RequestContext requestContext;
    private User user;

    @BeforeEach
    void setUp() {
        user = new User("74255", "1000");
        requestContext = RequestContext.builder()
                .user(user)
                .tenantId("74255")
                .build();
    }

    @Test
    void testFeedData_Success() {
        // Given
        FeedDataByJson.Arg arg = new FeedDataByJson.Arg();
        List<FeedDataByJson.FeedData> feedDataList = Lists.newArrayList();
        FeedDataByJson.FeedData feedData = new FeedDataByJson.FeedData();
        feedData.setDataType("testApiName");
        feedData.setDataId("testDataId");
        feedDataList.add(feedData);
        arg.setDataList(feedDataList);

        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        when(mockDescribe.getDisplayName()).thenReturn("Test Object");
        when(serviceFacade.findObject("74255", "testApiName")).thenReturn(mockDescribe);

        INameCache mockNameCache = mock(INameCache.class);
        when(mockNameCache.getId()).thenReturn("testDataId");
        when(mockNameCache.getName()).thenReturn("Test Name");
        when(serviceFacade.findRecordName(any(IActionContext.class), eq("testApiName"), anyList()))
                .thenReturn(Lists.newArrayList(mockNameCache));

        when(serviceFacade.isAdmin(user)).thenReturn(true);

        try (MockedStatic<ActionContextExt> mockedStatic = mockStatic(ActionContextExt.class)) {
            ActionContextExt mockActionContextExt = mock(ActionContextExt.class);
            IActionContext mockActionContext = mock(IActionContext.class);
            mockedStatic.when(() -> ActionContextExt.of(user, requestContext)).thenReturn(mockActionContextExt);
            when(mockActionContextExt.getContext()).thenReturn(mockActionContext);

            // When
            FeedDataByJson.Result result = salesRecordService.feedData(arg, requestContext);

            // Then
            assertNotNull(result);
            assertNotNull(result.getFeedDataList());
            assertEquals(1, result.getFeedDataList().size());
            assertEquals("Test Name", result.getFeedDataList().get(0).getData());
            assertEquals("Test Object", result.getFeedDataList().get(0).getDisplayName());
        }
    }

    @Test
    void testFeedData_EmptyDataList() {
        // Given
        FeedDataByJson.Arg arg = new FeedDataByJson.Arg();
        arg.setDataList(Collections.emptyList());

        // When
        FeedDataByJson.Result result = salesRecordService.feedData(arg, requestContext);

        // Then
        assertNotNull(result);
        assertNull(result.getFeedDataList());
    }

    @Test
    void testFeedData_NullDataList() {
        // Given
        FeedDataByJson.Arg arg = new FeedDataByJson.Arg();
        arg.setDataList(null);

        // When
        FeedDataByJson.Result result = salesRecordService.feedData(arg, requestContext);

        // Then
        assertNotNull(result);
        assertNull(result.getFeedDataList());
    }

    @Test
    void testFeedData_NonAdminUser() {
        // Given
        FeedDataByJson.Arg arg = new FeedDataByJson.Arg();
        List<FeedDataByJson.FeedData> feedDataList = Lists.newArrayList();
        FeedDataByJson.FeedData feedData = new FeedDataByJson.FeedData();
        feedData.setDataType("testApiName");
        feedData.setDataId("testDataId");
        feedDataList.add(feedData);
        arg.setDataList(feedDataList);

        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        when(mockDescribe.getDisplayName()).thenReturn("Test Object");
        when(serviceFacade.findObject("74255", "testApiName")).thenReturn(mockDescribe);

        INameCache mockNameCache = mock(INameCache.class);
        when(mockNameCache.getId()).thenReturn("testDataId");
        when(mockNameCache.getName()).thenReturn("Test Name");
        when(serviceFacade.findRecordName(any(IActionContext.class), eq("testApiName"), anyList()))
                .thenReturn(Lists.newArrayList(mockNameCache));

        when(serviceFacade.isAdmin(user)).thenReturn(false);
        when(crmService.getIsSubCascadeConfig("74255")).thenReturn(true);

        ObjectsPermission.Result mockPermissionResult = new ObjectsPermission.Result();
        Map<String, String> permissionMap = Maps.newHashMap();
        permissionMap.put("testDataId", "2"); // READ_WRITE
        mockPermissionResult.setResult(permissionMap);
        when(dataAuthServiceProxy.objectsPermission(anyMap(), any(ObjectsPermission.Arg.class)))
                .thenReturn(mockPermissionResult);

        try (MockedStatic<ActionContextExt> mockedStatic = mockStatic(ActionContextExt.class)) {
            ActionContextExt mockActionContextExt = mock(ActionContextExt.class);
            IActionContext mockActionContext = mock(IActionContext.class);
            mockedStatic.when(() -> ActionContextExt.of(user, requestContext)).thenReturn(mockActionContextExt);
            when(mockActionContextExt.getContext()).thenReturn(mockActionContext);

            // When
            FeedDataByJson.Result result = salesRecordService.feedData(arg, requestContext);

            // Then
            assertNotNull(result);
            assertNotNull(result.getFeedDataList());
            assertEquals(1, result.getFeedDataList().size());
            assertEquals("Test Name", result.getFeedDataList().get(0).getData());
        }
    }

    @Test
    void testFeedData_NoPermission() {
        // Given
        FeedDataByJson.Arg arg = new FeedDataByJson.Arg();
        List<FeedDataByJson.FeedData> feedDataList = Lists.newArrayList();
        FeedDataByJson.FeedData feedData = new FeedDataByJson.FeedData();
        feedData.setDataType("testApiName");
        feedData.setDataId("testDataId");
        feedDataList.add(feedData);
        arg.setDataList(feedDataList);

        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        when(mockDescribe.getDisplayName()).thenReturn("Test Object");
        when(serviceFacade.findObject("74255", "testApiName")).thenReturn(mockDescribe);

        INameCache mockNameCache = mock(INameCache.class);
        when(mockNameCache.getId()).thenReturn("testDataId");
        when(mockNameCache.getName()).thenReturn("Test Name");
        when(serviceFacade.findRecordName(any(IActionContext.class), eq("testApiName"), anyList()))
                .thenReturn(Lists.newArrayList(mockNameCache));

        when(serviceFacade.isAdmin(user)).thenReturn(false);
        when(crmService.getIsSubCascadeConfig("74255")).thenReturn(false);

        ObjectsPermission.Result mockPermissionResult = new ObjectsPermission.Result();
        Map<String, String> permissionMap = Maps.newHashMap();
        permissionMap.put("testDataId", "0"); // NO_PERMISSION
        mockPermissionResult.setResult(permissionMap);
        when(dataAuthServiceProxy.objectsPermission(anyMap(), any(ObjectsPermission.Arg.class)))
                .thenReturn(mockPermissionResult);

        try (MockedStatic<ActionContextExt> mockedStatic = mockStatic(ActionContextExt.class)) {
            ActionContextExt mockActionContextExt = mock(ActionContextExt.class);
            IActionContext mockActionContext = mock(IActionContext.class);
            mockedStatic.when(() -> ActionContextExt.of(user, requestContext)).thenReturn(mockActionContextExt);
            when(mockActionContextExt.getContext()).thenReturn(mockActionContext);

            // When
            FeedDataByJson.Result result = salesRecordService.feedData(arg, requestContext);

            // Then
            assertNotNull(result);
            assertNotNull(result.getFeedDataList());
            assertEquals(1, result.getFeedDataList().size());
            assertTrue(result.getFeedDataList().get(0).getData().contains("****"));
        }
    }

    @ParameterizedTest
    @ValueSource(strings = {"Test", "TestName", "VeryLongTestName", "A"})
    void testMaskName_DifferentLengths(String name) {
        // Given
        FeedDataByJson.Arg arg = new FeedDataByJson.Arg();
        List<FeedDataByJson.FeedData> feedDataList = Lists.newArrayList();
        FeedDataByJson.FeedData feedData = new FeedDataByJson.FeedData();
        feedData.setDataType("testApiName");
        feedData.setDataId("testDataId");
        feedDataList.add(feedData);
        arg.setDataList(feedDataList);

        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        when(serviceFacade.findObject("74255", "testApiName")).thenReturn(mockDescribe);

        INameCache mockNameCache = mock(INameCache.class);
        when(mockNameCache.getId()).thenReturn("testDataId");
        when(mockNameCache.getName()).thenReturn(name);
        when(serviceFacade.findRecordName(any(IActionContext.class), eq("testApiName"), anyList()))
                .thenReturn(Lists.newArrayList(mockNameCache));

        when(serviceFacade.isAdmin(user)).thenReturn(false);
        when(crmService.getIsSubCascadeConfig("74255")).thenReturn(false);

        ObjectsPermission.Result mockPermissionResult = new ObjectsPermission.Result();
        Map<String, String> permissionMap = Maps.newHashMap();
        permissionMap.put("testDataId", "0"); // NO_PERMISSION
        mockPermissionResult.setResult(permissionMap);
        when(dataAuthServiceProxy.objectsPermission(anyMap(), any(ObjectsPermission.Arg.class)))
                .thenReturn(mockPermissionResult);

        try (MockedStatic<ActionContextExt> mockedStatic = mockStatic(ActionContextExt.class)) {
            ActionContextExt mockActionContextExt = mock(ActionContextExt.class);
            IActionContext mockActionContext = mock(IActionContext.class);
            mockedStatic.when(() -> ActionContextExt.of(user, requestContext)).thenReturn(mockActionContextExt);
            when(mockActionContextExt.getContext()).thenReturn(mockActionContext);

            // When
            FeedDataByJson.Result result = salesRecordService.feedData(arg, requestContext);

            // Then
            assertNotNull(result);
            String maskedName = result.getFeedDataList().get(0).getData();
            if (name.length() > 4) {
                assertTrue(maskedName.contains("********"));
                assertTrue(maskedName.startsWith(name.substring(0, 2)));
                assertTrue(maskedName.endsWith(name.substring(name.length() - 2)));
            } else {
                assertEquals("****", maskedName);
            }
        }
    }

    @Test
    void testCheckPermission_Success() {
        // Given
        CheckPermission.Arg arg = new CheckPermission.Arg();
        List<CheckPermission.CheckPermissionData> checkList = Lists.newArrayList();
        CheckPermission.CheckPermissionData checkData = new CheckPermission.CheckPermissionData();
        checkData.setDataId("testDataId");
        checkData.setDataType("testApiName");
        checkList.add(checkData);
        arg.setPermissionDataList(checkList);

        IObjectData mockObjectData = mock(IObjectData.class);
        when(serviceFacade.findObjectDataByIds(any(IActionContext.class), anyList(), eq("testApiName")))
                .thenReturn(Lists.newArrayList(mockObjectData));

        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        when(serviceFacade.findObject("74255", "testApiName")).thenReturn(mockDescribe);

        Map<String, Permissions> privMap = Maps.newHashMap();
        privMap.put("testDataId", Permissions.READ_ONLY);
        when(serviceFacade.checkDataPrivilege(eq(user), anyList(), eq(mockDescribe)))
                .thenReturn(privMap);

        try (MockedStatic<ActionContextExt> mockedStatic = mockStatic(ActionContextExt.class)) {
            ActionContextExt mockActionContextExt = mock(ActionContextExt.class);
            IActionContext mockActionContext = mock(IActionContext.class);
            mockedStatic.when(() -> ActionContextExt.of(user, requestContext)).thenReturn(mockActionContextExt);
            when(mockActionContextExt.getContext()).thenReturn(mockActionContext);

            // When
            CheckPermission.Result result = salesRecordService.checkPermission(arg, requestContext);

            // Then
            assertNotNull(result);
            assertEquals(Integer.valueOf(Permissions.READ_ONLY.getValue()), result.getPermissionResult());
        }
    }

    @Test
    void testCheckPermission_EmptyCheckList() {
        // Given
        CheckPermission.Arg arg = new CheckPermission.Arg();
        arg.setPermissionDataList(Collections.emptyList());

        // When
        CheckPermission.Result result = salesRecordService.checkPermission(arg, requestContext);

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(Permissions.NO_PERMISSION.getValue()), result.getPermissionResult());
    }

    @Test
    void testFeedData_PermissionServiceFailed() {
        // Given
//        FeedDataByJson.Arg arg = new FeedDataByJson.Arg();
//        List<FeedDataByJson.FeedData> feedDataList = Lists.newArrayList();
//        FeedDataByJson.FeedData feedData = new FeedDataByJson.FeedData();
//        feedData.setDataType("testApiName");
//        feedData.setDataId("testDataId");
//        feedDataList.add(feedData);
//        arg.setDataList(feedDataList);
//
//        when(serviceFacade.isAdmin(user)).thenReturn(false);
//
//        ObjectsPermission.Result mockPermissionResult = new ObjectsPermission.Result();
//        when(dataAuthServiceProxy.objectsPermission(anyMap(), any(ObjectsPermission.Arg.class)))
//                .thenReturn(mockPermissionResult);
//
//        // When & Then
//        assertThrows(PermissionError.class, () -> {
//            salesRecordService.feedData(arg, requestContext);
//        });
    }
}
