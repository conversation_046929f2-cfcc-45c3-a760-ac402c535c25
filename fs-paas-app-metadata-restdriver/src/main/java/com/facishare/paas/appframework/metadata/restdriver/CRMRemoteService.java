package com.facishare.paas.appframework.metadata.restdriver;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.sfa.BatchCalculateCountFields;
import com.facishare.paas.appframework.metadata.dto.sfa.GetObjectCountByRecordType;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.metadata.restdriver.dto.*;
import com.facishare.paas.appframework.metadata.util.SFAHeaderUtil;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.condition.IConditions;
import com.facishare.paas.metadata.api.condition.MatchConditions;
import com.facishare.paas.metadata.api.condition.TermConditions;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.EmbeddedObjectList;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.NameCache;
import com.facishare.paas.metadata.impl.search.SearchQuery;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by liyiguang on 2017/7/18.
 */
@Service
@Slf4j
public class CRMRemoteService {
    @Autowired
    private CRMRestServiceProxy crmRestServiceProxy;
    @Autowired
    private CRMRemoteServiceProxy crmRemoteServiceProxy;
    @Autowired
    private ObjectDataConverterManager objectDataConverterManager;
    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private NewCRMRestServiceProxy newCRMRestServiceProxy;
    @Autowired
    private NewCRMRemoteServiceProxy newCRMRemoteServiceProxy;
    @Autowired
    private IndustryEnterInfoService industryEnterInfoService;

    public Object createObjectData(IActionContext context, IObjectData data) {
        Map<String, String> headers = SFAHeaderUtil.getHeaders(context);

        Map<String, String> pathParameter = Maps.newLinkedHashMap();
        pathParameter.put("objectName", ObjectAPINameMapping.toOldAPIName(data.getDescribeApiName()));

        Map<String, Object> arg = ObjectDataExt.of(data).remove(IObjectData.ID).toMap();
        String describeApiName = data.getDescribeApiName();
        arg = specialFieldConvert(arg, describeApiName);
        log.info("crmRemoteServiceProxy.create.arg={}", JSON.toJSONString(arg));
        CreateObjectData.Result createObjectDataResult;
        if (RequestUtil.isCepRequest()) {
            createObjectDataResult = crmRemoteServiceProxy.create(arg, headers, pathParameter);
        } else {
            createObjectDataResult = newCRMRemoteServiceProxy.create(arg, headers, pathParameter);
        }

        createObjectDataResult.logAndThrowExceptionIfFailed(log, "createObjectData error,headers:{},pathParameter:{},arg:{}",
                headers, pathParameter, arg);

        return createObjectDataResult.getValue();
    }

    private Map<String, Object> specialFieldConvert(Map<String, Object> arg, String describeApiName) {
        if (!Strings.isNullOrEmpty(describeApiName)) {
            String jsonStr = JSON.toJSONString(arg);
            jsonStr = specialFieldConvert(jsonStr, describeApiName);
            return JSON.parseObject(jsonStr, Map.class);
        }
        return arg;
    }

    private String specialFieldConvert(String dataJson, String apiName) {
        if ("AccountObj".equals(apiName)) {
            JSONObject jsonObject = JSON.parseObject(dataJson);
            if (jsonObject.containsKey("Country") || jsonObject.containsKey("Province")
                    || jsonObject.containsKey("City") || jsonObject.containsKey("District")) {
                String country = getStringWithoutNPE("Country", jsonObject);
                String province = getStringWithoutNPE("Province", jsonObject);
                String city = getStringWithoutNPE("City", jsonObject);
                String distinct = getStringWithoutNPE("District", jsonObject);
                String area = country + "/" + province + "/" + city + "/" + distinct;
                jsonObject.put("Area", area);
            }
            String industryLevel2 = getStringWithoutNPE("SubIndustry", jsonObject);
            if (org.apache.commons.lang.StringUtils.isNotBlank(industryLevel2)) {
                jsonObject.put("Industry", industryLevel2);
            }
            return JSON.toJSONString(jsonObject);
        }
        return dataJson;

    }

    private String getStringWithoutNPE(String fieldName, JSONObject jsonObject) {
        String rtnValue = "";
        Object obj = jsonObject.get(fieldName);
        if (obj != null) {
            rtnValue = obj.toString();
        }
        return rtnValue;
    }

    public Object updateObjectData(IActionContext context, IObjectData data) {
        return updateObjectData(context, data, false);
    }

    public Object updateObjectData(IActionContext context, IObjectData data, Boolean incrementalUpdate) {
        Map<String, String> headers = SFAHeaderUtil.getHeaders(context);

        Map<String, String> pathParameter = Maps.newLinkedHashMap();
        pathParameter.put("objectName", ObjectAPINameMapping.toOldAPIName(data.getDescribeApiName()));
        Map<String, Object> arg = ObjectDataExt.of(data).toMap();
        String describeApiName = data.getDescribeApiName();
        arg = specialFieldConvert(arg, describeApiName);
        log.info("crmRemoteServiceProxy.update.arg={}", JSON.toJSONString(arg));
        UpdateObjectData.Result updateObjectDataResult;
        if (incrementalUpdate) {
            if (RequestUtil.isCepRequest()) {
                updateObjectDataResult = crmRemoteServiceProxy.updateIncrementalUpdate(arg, headers, pathParameter);
            } else {
                updateObjectDataResult = newCRMRemoteServiceProxy.updateIncrementalUpdate(arg, headers, pathParameter);
            }
        } else {
            if (RequestUtil.isCepRequest()) {
                updateObjectDataResult = crmRemoteServiceProxy.update(arg, headers, pathParameter);
            } else {
                updateObjectDataResult = newCRMRemoteServiceProxy.update(arg, headers, pathParameter);
            }
        }

        updateObjectDataResult.logAndThrowExceptionIfFailed(log, "updateObjectData error,headers:{},pathParameter:{},arg:{}",
                headers, pathParameter, arg);

        return updateObjectDataResult.getValue();
    }

    public List<IObjectData> findObjectDataByIds(IActionContext context, String apiName, List<String> ids) {
        return findObjectDataByIds(context, apiName, ids, true);
    }

    public List<IObjectData> findObjectDataByIds(IActionContext context, String apiName, List<String> ids, Boolean
            includeUserDefinedFields) {
        String tenantId = context.getEnterpriseId();
        String userId = context.getUserId();

        Map<String, String> headers = SFAHeaderUtil.getHeaders(tenantId, userId);
        FindObjectByIds.Result result = getObjectDataByIds(headers, apiName, ids, includeUserDefinedFields, context.doCalculate());

        // TODO: 2018/9/25  特殊处理：引用老对象客户的定位字段，Address信息中添加经纬度（bug修改）
        List<Map<String, Object>> value = result.getValue();
        value.forEach(x -> {
            Object address = x.get("Address");
            if (address != null) {
                Object longitude = x.get("Longitude");
                Object latitude = x.get("Latitude");
                if (longitude != null && latitude != null) {
                    address = longitude + "#%$" + latitude + "#%$" + address;
                    x.put("Address", address);
                }
            }
        });

        ObjectDataConverter converter = objectDataConverterManager.getObjectDataConverter(apiName);
        IObjectDescribe describe = context.getObjectDescribe();
        //有的时候context中可能没有传describe，有的时候context中的describe和apiName不一致。这时都需要重新查一遍
        if (Objects.isNull(describe) || !apiName.equals(describe.getApiName())) {
            describe = describeLogicService.findObject(tenantId, apiName);
        }
        List<IObjectData> dataList = converter.toDataObjects(result.getValue(), describe);
        //fixSpecialDatas(context, describe, dataList);
        return dataList;
    }

    public IObjectData findObjectDataById(IActionContext context, String apiName, String id) {
        if (Utils.BIZ_QUERY_OBJ_API_NAME.equals(apiName) && AppFrameworkConfig.isIndustryEnterInfoGrayTenantId(context.getEnterpriseId())) {
            return industryEnterInfoService.findObjectDataById(id, context);
        }
        String tenantId = context.getEnterpriseId();
        String userId = context.getUserId();

        Map<String, String> pathParameter = Maps.newLinkedHashMap();
        pathParameter.put("objectName", ObjectAPINameMapping.toOldAPIName(apiName));
        pathParameter.put("id", id);

        Map<String, String> headers = SFAHeaderUtil.getHeaders(tenantId, userId);

        FindObjectDataById.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRemoteServiceProxy.findObjectDataById(headers, pathParameter);
        } else {
            result = newCRMRemoteServiceProxy.findObjectDataById(headers, pathParameter);
        }
        result.logAndThrowExceptionIfFailed(log, "findObjectDataById error,headers:{},pathParameter:{}",
                headers, pathParameter);

        ObjectDataConverter converter = objectDataConverterManager.getObjectDataConverter(apiName);
        IObjectDescribe describe = context.getObjectDescribe();
        if (Objects.isNull(describe)) {
            describe = describeLogicService.findObject(tenantId, apiName);
        }
        IObjectData data = converter.toObjectData(result.getValue(), describe);
        fixSpecialData(context, buildDescribeMap(describe), data);
        return data;
    }

    public QueryResult<IObjectData> findBySearchQuery(IActionContext context, String apiName, ISearchQuery
            searchQuery) {
        String tenantId = context.getEnterpriseId();
        String userId = context.getUserId();

        Map<String, String> headers = SFAHeaderUtil.getHeaders(tenantId, userId);

        Map<String, String> pathParameter = Maps.newLinkedHashMap();
        pathParameter.put("objectName", ObjectAPINameMapping.toOldAPIName(apiName));
        convertSearchQuery(apiName, searchQuery);
        List items = null;
        Integer totalNumber = 0;
        if (Utils.SALES_ORDER_PRODUCT_API_NAME.equals(apiName) || Utils.RETURN_GOODS_INVOICE_Product_API_NAME.equals
                (apiName)) {
            FindProductQuery.Result productResult;
            if (RequestUtil.isCepRequest()) {
                productResult = crmRemoteServiceProxy.findProductQuery(searchQuery, headers, pathParameter);
            } else {
                productResult = newCRMRemoteServiceProxy.findProductQuery(searchQuery, headers, pathParameter);
            }
            productResult.logAndThrowExceptionIfFailed(log, "findBySearchQuery error,headers:{},pathParameter:{},searchQuery:{}",
                    headers, pathParameter, searchQuery.toJsonString());
            items = productResult.getValue();
            totalNumber = items.size();
        } else {
            FindBySearchQuery.Result result;
            if (RequestUtil.isCepRequest()) {
                result = crmRemoteServiceProxy.findBySearchQuery(searchQuery, headers, pathParameter);
            } else {
                result = newCRMRemoteServiceProxy.findBySearchQuery(searchQuery, headers, pathParameter);
            }
            result.logAndThrowExceptionIfFailed(log, "findBySearchQuery error,headers:{},pathParameter:{},searchQuery:{}",
                    headers, pathParameter, searchQuery.toJsonString());
            items = result.getValue().getItems();
            if (result.getValue().getPage() != null) {
                totalNumber = result.getValue().getPage().getTotalCount();
            }
        }

        ObjectDataConverter converter = objectDataConverterManager.getObjectDataConverter(apiName);
        IObjectDescribe describe = context.getObjectDescribe();
        if (Objects.isNull(describe)) {
            describe = describeLogicService.findObject(tenantId, apiName);
        }
        List<IObjectData> dataList = converter.toDataObjects(items, describe);
        // TODO: 2017/10/18 特殊处理销售订单和退货单的内嵌字段
        fixSpecialDatas(context, describe, dataList);
        QueryResult queryResult = new QueryResult();
        queryResult.setData(dataList);
        queryResult.setTotalNumber(totalNumber);
        return queryResult;
    }

    private void convertProductIdRoData(String tenantId, IObjectData data, Map<String, IObjectDescribe> describeMap) {
        Object productDataMap = data.get("product_id__ro");
        if (Objects.isNull(productDataMap)) {
            return;
        }
        // TODO: 2018/11/15 需要确认此处代码是不是也不会再继续保留了？
        String apiName = Utils.PRODUCT_API_NAME;
        ObjectDataConverter dataConverter = objectDataConverterManager.getObjectDataConverter(apiName);
        IObjectDescribe describe = describeMap.computeIfAbsent(apiName, x -> describeLogicService.findObject
                (tenantId, x));
        IObjectData productData = dataConverter.toObjectData((Map<String, Object>) productDataMap, describe);
        //转换的时候自动加上了object_describe_api_name字段，此处去掉
        ObjectDataExt.of(productData).toMap().remove(IObjectData.DESCRIBE_API_NAME);
        //老代码把Price转成sales_price了，这里兼容一下
        if (ObjectDataExt.of(productData).toMap().containsKey("price")) {
            productData.set("sales_price", productData.get("price"));
        }
        data.set("product_id__ro", ObjectDataExt.of(productData).toMap());
    }

    private void fixSpecialDatas(IActionContext context, IObjectDescribe describe, List<IObjectData> dataList) {
        Map<String, IObjectDescribe> describeMap = buildDescribeMap(describe);
        dataList.forEach(k -> fixSpecialData(context, describeMap, k));
    }

    @NotNull
    private Map<String, IObjectDescribe> buildDescribeMap(IObjectDescribe describe) {
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(describe.getApiName(), describe);

        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        List<String> embeddedApiNames = describeExt.stream()
                .filter(k -> k instanceof EmbeddedObjectList)
                .map(x -> x.getApiName()).collect(Collectors.toList());

        if (CollectionUtils.notEmpty(embeddedApiNames)) {
            Map<String, IObjectDescribe> embeddedDescribes = describeLogicService.findObjects(describe.getTenantId(),
                    embeddedApiNames);
            describeMap.putAll(embeddedDescribes);
        }

        return describeMap;
    }

    private void fixSpecialData(IActionContext context, Map<String, IObjectDescribe> describeMap, IObjectData data) {
        //转换product_id_ro数据
        convertProductIdRoData(context.getEnterpriseId(), data, describeMap);
    }

    private void convertSearchQuery(String apiName, ISearchQuery iSearchQuery) {
        SearchQuery searchQuery = (SearchQuery) iSearchQuery;
        ObjectDataConverter converter = objectDataConverterManager.getObjectDataConverter(apiName);
        if (CollectionUtils.notEmpty(searchQuery.getOrders())) {
            List<Order> newOrders = searchQuery.getOrders().stream()
                    .map(k -> new Order(converter.toOldFieldName(k.getField()), k.getAscending())).collect(Collectors
                            .toList());
            searchQuery.setOrders(newOrders);
        }

        if (CollectionUtils.notEmpty(searchQuery.getConditions())) {
            List<IConditions> newListConditions = searchQuery.getConditions().stream()
                    .filter(k -> k.getConditions() != null).map(k -> {
                        IConditions newConditions = null;
                        if (k instanceof TermConditions) {
                            newConditions = new TermConditions();
                        } else {
                            newConditions = new MatchConditions();
                        }
                        newConditions.setType(k.getType());
                        Map<String, String> conditions = (Map) k.getConditions();
                        for (Map.Entry<String, String> entry : conditions.entrySet()) {
                            newConditions.addCondition(converter.toOldFieldName(entry.getKey()), entry.getValue());
                        }
                        return newConditions;
                    }).collect(Collectors.toList());
            searchQuery.setConditions(newListConditions);
        }

        if (CollectionUtils.notEmpty(searchQuery.getRangeConditions())) {
            searchQuery.getRangeConditions().forEach(x -> {
                x.setFieldName(converter.toOldFieldName(x.getFieldName()));
            });
        }

        if (CollectionUtils.notEmpty(searchQuery.getIncludedObjectReferences())) {
            List<String> includedObjectReferences = searchQuery.getIncludedObjectReferences().stream()
                    .map(x -> converter.toOldFieldName(x)).collect(Collectors.toList());
            searchQuery.setIncludedObjectReferences(includedObjectReferences);
        }

        if (searchQuery.getDataProjection() != null && CollectionUtils.notEmpty(searchQuery.getDataProjection()
                .getIncludeFields())) {
            List<String> includedFields = searchQuery.getDataProjection().getIncludeFields().stream()
                    .map(x -> converter.toOldFieldName(x)).collect(Collectors.toList());
            searchQuery.getDataProjection().setIncludeFields(includedFields);
        }
    }

    public Object invalid(String tenantId, String userId, String apiName, String id) {
        return commonOperate(tenantId, userId, apiName, id, "invalid");
    }

    public Object recover(String tenantId, String userId, String apiName, String id) {
        return commonOperate(tenantId, userId, apiName, id, "recover");
    }

    public Object delete(String tenantId, String userId, String apiName, String id) {
        return commonOperate(tenantId, userId, apiName, id, "delete");
    }

    public Object bulkInvalid(String tenantId, String userId, String apiName, Set<String> ids) {
        return commonBulkOperate(tenantId, userId, apiName, ids, "invalid");
    }

    public Object bulkRecover(String tenantId, String userId, String apiName, Set<String> ids) {
        return commonBulkOperate(tenantId, userId, apiName, ids, "recover");
    }

    public Object bulkDelete(String tenantId, String userId, String apiName, Set<String> ids) {
        return commonBulkOperate(tenantId, userId, apiName, ids, "delete");
    }

    private Object commonOperate(String tenantId, String userId, String apiName, String id, String operateName) {
        Map<String, String> headers = SFAHeaderUtil.getHeaders(tenantId, userId);

        Map<String, Object> pathParameter = Maps.newLinkedHashMap();
        pathParameter.put("id", id);

        CommonRestObj.Result result = null;
        switch (operateName) {
            case "invalid":
                pathParameter.put("objectType", String.valueOf(ObjectAPINameMapping.toIntObjectType(apiName)));
                if (RequestUtil.isCepRequest()) {
                    result = crmRemoteServiceProxy.invalid(headers, pathParameter);
                } else {
                    result = newCRMRemoteServiceProxy.invalid(headers, pathParameter);
                }
                break;
            case "recover":
                pathParameter.put("objectType", String.valueOf(ObjectAPINameMapping.toIntObjectType(apiName)));
                if (RequestUtil.isCepRequest()) {
                    result = crmRemoteServiceProxy.recover(headers, pathParameter);
                } else {
                    result = newCRMRemoteServiceProxy.recover(headers, pathParameter);
                }
                break;
            case "delete":
                pathParameter.put("objectName", String.valueOf(ObjectAPINameMapping.toOldAPIName(apiName)));
                if (RequestUtil.isCepRequest()) {
                    result = crmRemoteServiceProxy.delete(headers, pathParameter);
                } else {
                    result = newCRMRemoteServiceProxy.delete(headers, pathParameter);
                }
                break;
            default:
                result = new CommonRestObj.Result();
                result.setMessage(I18N.text(I18NKey.UNSUPPORT_OPERATION));//"不支持的操作类型"
                result.setSuccess(false);
                break;
        }
        result.logAndThrowExceptionIfFailed(log, "commonOperate error,operateName:{},headers:{},pathParameter:{},result:{}",
                operateName, headers, pathParameter, result);
        return result.getValue();
    }

    private Object commonBulkOperate(String tenantId, String userId, String apiName, Set<String> ids, String
            operateName) {
        Map<String, String> headers = SFAHeaderUtil.getHeaders(tenantId, userId);

        HashMap<String, Object> arg = new HashMap<>();
        arg.put("ObjectIDs", ids);
        arg.put("ObjectType", ObjectAPINameMapping.toIntObjectType(apiName));
        Map<String, Object> pathParameter = Maps.newLinkedHashMap();
        pathParameter.put("operateName", operateName);

        CommonRestObj.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRemoteServiceProxy.commonBulkOperate(arg, headers, pathParameter);
        } else {
            result = newCRMRemoteServiceProxy.commonBulkOperate(arg, headers, pathParameter);
        }

        result.logAndThrowExceptionIfFailed(log, "commonBulkOperate error,operateName:{},headers:{},pathParameter:{},arg:{}",
                operateName, headers, pathParameter, arg);

        return result.getValue();
    }

    public List<INameCache> findRecordName(String tenantId, String userId, String apiName, List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyList();
        }

        List<String> needed = ids.stream().distinct().collect(Collectors.toList());

        Map<String, String> names = new HashMap<>(needed.size());

        if (needed.size() > ParallelUtils.MAX_PARALLEL_NUM) {
            List<String> remained = needed;
            while (remained.size() > ParallelUtils.MAX_PARALLEL_NUM) {
                names.putAll(parallelFetchRecordNames(tenantId, userId, apiName, remained.subList(0, ParallelUtils
                        .MAX_PARALLEL_NUM)));
                remained = remained.subList(ParallelUtils.MAX_PARALLEL_NUM, remained.size());
            }
            names.putAll(parallelFetchRecordNames(tenantId, userId, apiName, remained));
        } else {
            names.putAll(parallelFetchRecordNames(tenantId, userId, apiName, needed));
        }

        return ids.stream().map(x -> {
            NameCache nameCache = new NameCache();
            nameCache.setId(x);
            nameCache.setName(names.get(x));
            return nameCache;
        }).collect(Collectors.toList());
    }

    public Map<String, String> parallelFetchRecordNames(String tenantId, String userId, String apiName, List<String>
            ids) {
        ObjectDataConverter dataConverter = objectDataConverterManager.getObjectDataConverter(apiName);
        String idFieldName = dataConverter.toOldFieldName(IObjectData.ID);
        String nameFieldName = dataConverter.toOldFieldName(IObjectData.NAME);

        if (Strings.isNullOrEmpty(idFieldName) || Strings.isNullOrEmpty(nameFieldName)) {
            log.error("field mapping not contains id or name,apiName:{},idFieldName:{},nameFieldName:{}", apiName, idFieldName, nameFieldName);
            return Collections.emptyMap();
        }

        Map<String, String> headers = SFAHeaderUtil.getHeaders(tenantId);
        FindObjectByIds.Result result = getObjectDataByIds(headers, apiName, ids, false, false);

        if (CollectionUtils.empty(result.getValue())) {
            return Collections.emptyMap();
        }

        Map<String, String> ret = Maps.newHashMap();
        result.getValue().forEach(x -> {
            if (x.containsKey(idFieldName) && x.containsKey(nameFieldName)) {
                ret.put((String) x.get(idFieldName), (String) x.get(nameFieldName));
            }
        });

        return ret;
    }

    private FindObjectByIds.Result getObjectDataByIds(Map<String, String> headers, String apiName, List<String> ids,
                                                      boolean includeUserDefinedFields, boolean includeCalculationFields) {
        if (ids.size() <= 100) {
            return findByIds(headers, apiName, ids, includeUserDefinedFields, includeCalculationFields);
        }
        //sfa接口加了每批次100条的限制，改为并行调用
        List<List<String>> allParts = Lists.partition(ids, 50);
        FindObjectByIds.Result result = new FindObjectByIds.Result();
        result.setValue(Lists.newCopyOnWriteArrayList());

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        allParts.forEach(part -> parallelTask.submit(() -> {
            FindObjectByIds.Result partResult = findByIds(headers, apiName, part, includeUserDefinedFields, includeCalculationFields);
            result.getValue().addAll(partResult.getValue());
        }));

        try {
            if (ids.size() >= 200) {
                parallelTask.await(30, TimeUnit.SECONDS);
            } else {
                parallelTask.await(6, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("Time out crmRemoteServiceProxy.findObjectDataByIds, headers:{},apiName:{},ids:{},includeUserDefinedFields:{},includeCalculationFields:{}",
                    headers, apiName, ids, includeUserDefinedFields, includeCalculationFields, e);
            throw new MetaDataException(SystemErrorCode.METADATA_ERROR, e);
        }

        return result;
    }

    private FindObjectByIds.Result findByIds(Map<String, String> headers, String apiName, List<String> ids,
                                             boolean includeUserDefinedFields, boolean includeCalculationFields) {
        FindObjectByIds.Arg arg = FindObjectByIds.Arg.builder()
                .ids(ids)
                .objectType(ObjectAPINameMapping.toIntObjectType(apiName))
                .includeUserDefinedFields(includeUserDefinedFields)
                .includeCalculationFields(includeCalculationFields)
                .build();

        FindObjectByIds.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRemoteServiceProxy.findObjectDataByIds(arg, headers);
        } else {
            result = newCRMRemoteServiceProxy.findObjectDataByIds(arg, headers);
        }

        result.logAndThrowExceptionIfFailed(log, "findByIds error,headers:{},arg:{}", headers, arg);

        return result;
    }

    public void updateObjectByFields(List<IObjectData> objectDataList, IActionContext context) {
        Map<String, String> headers = SFAHeaderUtil.getHeaders(context.getEnterpriseId());

        String objectApiName = objectDataList.get(0).getDescribeApiName();
        List<Map<String, Object>> sfaDataList = objectDataList.stream().map(x -> {
            String id = ObjectDataExt.of(x).getId();
            Map<String, Object> data = ObjectDataExt.of(x).toMap();
            data.remove("_id");
            data.remove("object_describe_api_name");
            data.remove("tenant_id");
            data.put("ObjectID", id);
            return data;
        }).collect(Collectors.toList());

        UpdateObjectByFields.Arg arg = UpdateObjectByFields.Arg.builder()
                .objectApiName(objectApiName)
                .updateFieldList(Lists.newArrayList())
                .dataList(sfaDataList)
                .recalculateAggregateFields(true)
                .build();

        UpdateObjectByFields.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRemoteServiceProxy.updateObjectByFields(headers, arg);
        } else {
            result = newCRMRemoteServiceProxy.updateObjectByFields(headers, arg);
        }

        result.logAndThrowExceptionIfFailed(log, "updateObjectByFields error,headers:{},arg:{}", headers, arg);
    }

    public void updateObjectByFields(List<IObjectData> objectDataList, List<String> fieldApiNameList, IActionContext context) {
        Map<String, String> headers = SFAHeaderUtil.getHeaders(context.getEnterpriseId());

        List<Map<String, Object>> sfaDataList = objectDataList.stream().map(x -> {
            Map<String, Object> data = Maps.newHashMap();
            data.put("ObjectID", x.getId());
            fieldApiNameList.forEach(y -> data.put(y, x.get(y)));
            return data;
        }).collect(Collectors.toList());

        UpdateObjectByFields.Arg arg = UpdateObjectByFields.Arg.builder()
                .objectApiName(objectDataList.get(0).getDescribeApiName())
                .updateFieldList(fieldApiNameList)
                .dataList(sfaDataList)
                .recalculateAggregateFields(true)
                .build();

        Object recalculateAggregateFields = context.get("recalculateAggregateFields");
        if (Objects.nonNull(recalculateAggregateFields) && !(Boolean) recalculateAggregateFields) {
            arg.setRecalculateAggregateFields(Boolean.FALSE);
        }
        UpdateObjectByFields.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRemoteServiceProxy.updateObjectByFields(headers, arg);
        } else {
            result = newCRMRemoteServiceProxy.updateObjectByFields(headers, arg);
        }

        result.logAndThrowExceptionIfFailed(log, "updateObjectByFields error,headers:{},arg:{}", headers, arg);
    }

    public Object calculateCountField(String tenantId, Count countFieldDescribe, SearchTemplateQuery searchTemplateQuery) {
        Map<String, String> headers = SFAHeaderUtil.getHeaders(tenantId);

        IFilter filter = searchTemplateQuery.getFilters().stream()
                .filter(x -> x.getFieldName().equals(countFieldDescribe.getFieldApiName()))
                .findFirst()
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));

        String masterObjectDataId = filter.getFieldValues().get(0);
        BatchCalculateCountFields.CountField countField = BatchCalculateCountFields.CountField.of(countFieldDescribe, searchTemplateQuery.getWheres());

        BatchCalculateCountFields.Arg arg = BatchCalculateCountFields.Arg.builder()
                .objectDataId(masterObjectDataId)
                .countFieldList(Lists.newArrayList(countField))
                .build();

        BatchCalculateCountFields.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.batchCalculateCountFields(headers, arg);
        } else {
            result = newCRMRestServiceProxy.batchCalculateCountFields(headers, arg);
        }

        result.logAndThrowExceptionIfFailed(log, "getObjectAggregateFieldValue error,headers:{},arg:{}", headers, arg);

        return result.getValue().getCountFieldValues().get(countFieldDescribe.getApiName());
    }

    public int getObjectCountByRecordType(String tenantId, String apiName, String recordType) {
        Map<String, String> headers = SFAHeaderUtil.getHeaders(tenantId);

        Map<String, Object> pathParameter = Maps.newLinkedHashMap();
        pathParameter.put("objectType", String.valueOf(ObjectAPINameMapping.toIntObjectType(apiName)));
        pathParameter.put("recordType", recordType);

        GetObjectCountByRecordType.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.getObjectCountByRecordType(pathParameter, headers);
        } else {
            result = newCRMRestServiceProxy.getObjectCountByRecordType(pathParameter, headers);
        }

        result.logAndThrowExceptionIfFailed(log, "getObjectCountByRecordType error,headers:{},pathParameter",
                headers, pathParameter);

        return result.getValue();
    }

    public List<Map<String, Object>> findEnums(String tenantId, List<String> enumNames) {
        Map<String, String> headers = SFAHeaderUtil.getHeaders(tenantId);
        FindEnums.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRemoteServiceProxy.findEnums(enumNames, headers);
        } else {
            result = newCRMRemoteServiceProxy.findEnums(enumNames, headers);
        }

        result.logAndThrowExceptionIfFailed(log, "getEnums error,headers:{},arg:{}", headers, enumNames);

        return result.getValue();
    }

}
