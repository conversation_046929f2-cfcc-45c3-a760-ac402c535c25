package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.service.dto.log.GetNewLogInfoListForMod;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogType;
import com.facishare.paas.appframework.log.AuditLogConfig;
import com.facishare.paas.appframework.log.dto.LogCondition;
import com.facishare.paas.appframework.log.dto.MobSearchResult;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * @Desc
 * <AUTHOR>
 * @CreateTime 2019-09-24 16:14
 */
public class StandardNewLogInfoListForMobController extends AbstractStandardNewLogInfoListController<GetNewLogInfoListForMod.Arg, GetNewLogInfoListForMod.Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected IObjectDescribe findDescribe() {
        return serviceFacade.findObjectUseThreadLocalCache(controllerContext.getTenantId(),
                Strings.isNullOrEmpty(arg.getDetailApiName()) ? arg.getApiName() : arg.getDetailApiName());
    }

    @Override
    protected List<LogRecord> getModifyRecordList(GetNewLogInfoListForMod.Result result) {
        return result.getModifyRecordList();
    }

    @Override
    protected GetNewLogInfoListForMod.Result doService(GetNewLogInfoListForMod.Arg arg) {

        MobSearchResult logResult;
        Set<String> unauthorizedFields;
        Long operationTime = arg.getOperationTime();
        if (Objects.isNull(operationTime)) {
            operationTime = System.currentTimeMillis();
        }
        LogCondition logCondition = LogCondition.builder()
                .operationalType(Optional.ofNullable(arg.getOperationalType()).orElse(""))
                .bizIds(arg.getBizIds())
                .otherBizIds(arg.getOtherBizIds())
                .pageSize(arg.getPageSize())
                .operationTimeFrom(arg.getOperationTimeFrom())
                .operationTimeTo(arg.getOperationTimeTo())
                .operationTime(operationTime)
                .needReturnCount(false)
                .build();
        if (Strings.isNullOrEmpty(arg.getDetailApiName())) {
            unauthorizedFields = serviceFacade.getUnauthorizedFields(controllerContext.getUser(), arg.getApiName());
            if (isGrayCHRead()) {
                logCondition.setModule(arg.getApiName());
                logCondition.setObjectId(arg.getObjectId());
                logResult = serviceFacade.mobSearchModifyRecord(controllerContext.getUser(), logCondition);
            } else {
                logResult = serviceFacade.mobSearchModifyRecord(arg.getApiName(), arg.getObjectId(),
                        Optional.ofNullable(arg.getOperationalType()).orElse(""), arg.getPageSize(),
                        operationTime, controllerContext.getUser(), arg.getBizIds(), arg.getOtherBizIds());
            }
        } else {
            unauthorizedFields = serviceFacade.getUnauthorizedFields(controllerContext.getUser(), arg.getDetailApiName());
            if (isGrayCHRead()) {
                logCondition.setModule(arg.getDetailApiName());
                logCondition.setMasterId(arg.getObjectId());
                logCondition.setMasterLogId(arg.getMasterLogId());
                logResult = serviceFacade.mobSearchModifyRecordForMaster(controllerContext.getUser(), logCondition);
            } else {
                logResult = serviceFacade.mobSearchModifyRecordForMaster(arg.getObjectId(), arg.getDetailApiName(),
                        arg.getMasterLogId(), Optional.ofNullable(arg.getOperationalType()).orElse(""),
                        controllerContext.getUser(), arg.getBizIds(), arg.getOtherBizIds(), arg.getPageSize(), operationTime);
            }
        }
        filterSourceDetailSnapshot(logResult.getModifyRecordList(), arg.getSourceId(), arg.getMasterLogId());
        // 过滤字段权限
        filterFieldPermission(logResult.getModifyRecordList(), unauthorizedFields);
        GetNewLogInfoListForMod.Result result = new GetNewLogInfoListForMod.Result();
        result.setDescribe(objectDescribe == null ? null : ObjectDescribeExt.of(objectDescribe).toMap());
        result.setTotalCount(logResult.getTotalCount());
        List<LogRecord> logRecordList = new ArrayList<>(logResult.getModifyRecordList().size());
        logResult.getModifyRecordList().forEach(f -> logRecordList.add(modifyRecordToLogRecord(f)));
        //下游过滤多维度修改记录
        removeOutDimensionRecords(logRecordList);
        //隐藏插件定义中配置需要隐藏的字段
        removeFieldsByDomainPlugin(logRecordList);
        handleDetailLogId(arg.getDetailApiName(), arg.getMasterLogId(), logRecordList);
        //处理字段掩码
        fillMaskFieldValue(logRecordList, objectDescribe, arg.getObjectId(), arg.getApiName());
        //处理多维度字段
        fillDimensionFieldValue(logRecordList, objectDescribe, arg.getObjectId(), arg.getApiName());
        fillEmployeeAndDepartmentFieldValue(logRecordList, objectDescribe, arg.getObjectId(), arg.getApiName());
        fillDataVisibilityRangeFieldValue(logRecordList, objectDescribe, arg.getObjectId(), arg.getApiName());
        // 处理相关团队的展示问题
        handleTeamMember(controllerContext.getUser(), logRecordList);
        handleRichTextFieldValue(objectDescribe, logRecordList);
        fillObjectReferenceManyFieldValue(objectDescribe, logRecordList);
        dealMultiLangData(objectDescribe, logRecordList);
        // 处理编辑类型的修改记录
        dealModifyDatas(objectDescribe, logRecordList);
        // 处理 log 中的 NPath
        dealNPathSign(logRecordList, IFieldType.IMAGE, IFieldType.FILE_ATTACHMENT);
        handleEmployeeMultiLang(controllerContext.getUser(), logRecordList);
        result.setModifyRecordList(logRecordList);
        String tenantQueryInterval = AuditLogConfig.getTenantQueryInterval(controllerContext.getTenantId(), LogType.AUDIT_LOG.getLogType());
        if (StringUtils.isNotBlank(tenantQueryInterval)) {
            result.setQueryInterval(tenantQueryInterval);
        }
        // 终端需要下发对象描述
        result.setObjectDescribeExtra(getFieldExtra());
        result.setHasMore(BooleanUtils.isTrue(logResult.getHasMore()));
        return result;
    }


    private void handleDetailLogId(String detailApiName, String masterLogId, List<LogRecord> logRecordList) {
        if (Strings.isNullOrEmpty(detailApiName) || Strings.isNullOrEmpty(masterLogId) || CollectionUtils.empty(logRecordList)) {
            return;
        }
        logRecordList.stream()
                .filter(Objects::nonNull)
                .filter(x -> DETAIL_MERGE_DATA_OPERATION_LIST.contains(x.getOperationType()))
                .forEach(logRecord -> {
                    if (Objects.isNull(logRecord.getObjectInfo()) || CollectionUtils.empty(logRecord.getObjectInfo().getObjectDatas())) {
                        return;
                    }
                    logRecord.getObjectInfo().getObjectDatas().forEach(data -> data.setLogID(logRecord.getLogID()));
                });
    }
}
