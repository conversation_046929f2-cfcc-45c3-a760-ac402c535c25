package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.common.service.PlatServiceProxy
import com.facishare.paas.appframework.common.service.dto.ReferenceData
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.CustomButtonService
import com.facishare.paas.appframework.metadata.PostActionService
import com.facishare.paas.appframework.metadata.ReferenceLogicService
import com.facishare.paas.appframework.metadata.sandbox.ChangeSetFindByQuery
import com.facishare.paas.metadata.impl.UdefAction
import com.facishare.paas.metadata.impl.UdefButton
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class ObjectSandboxServiceTest extends Specification {
    ObjectSandboxService objectSandboxService
    def buttonService
    PostActionService actionService
    def referenceLogicService
    def platServiceProxy

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def setup() {
        buttonService = Mock(CustomButtonService)
        actionService = Mock(PostActionService)
        referenceLogicService = Mock(ReferenceLogicService)
        platServiceProxy = Mock(PlatServiceProxy)

        objectSandboxService = new ObjectSandboxService(
                customButtonService: buttonService,
                postActionService: actionService,
                referenceLogicService: referenceLogicService,
                platServiceProxy: platServiceProxy
        )
    }

    def "test buttonReference with valid inputs"() {
        given: "准备测试数据"
        def arg = new ChangeSetFindByQuery.Arg()
        def searchQueryInfo = new ChangeSetFindByQuery.SearchQueryInfo()
        def filters = [
                new ChangeSetFindByQuery.Filter(fieldName: "api_name", values: ["testApiName"]),
                new ChangeSetFindByQuery.Filter(fieldName: "describe_api_name", values: ["testDescribeApiName"]),
                new ChangeSetFindByQuery.Filter(fieldName: "tenant_id", values: ["testTenantId"])
        ]
        searchQueryInfo.setFilters(filters)
        arg.setSearchQueryInfo(searchQueryInfo)
        arg.setEnterpriseId(123L)
        def user = User.systemUser('74255')
        def requestContext = RequestContext.builder()
                .tenantId('74255')
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .build()
        RequestContextManager.setContext(requestContext)
        def context = new ServiceContext(requestContext, "cacheValidate", "cacheValidate")

        def button = new UdefButton()
        def action = new UdefAction()
        action.setActionParamter("""{"functionApiName":"testFunction"}""")

        def referenceData = ReferenceData.builder().build()
        referenceData.setSourceValue("testApiName")

        when: "调用方法"
        buttonService.findButtonByApiNameForDesigner(user, "testApiName", "testDescribeApiName") >> button
        actionService.findActionListForDesigner(*_) >> [action]
        referenceLogicService.queryByTargetList(*_) >> [referenceData]

        ChangeSetFindByQuery.Result result = objectSandboxService.buttonReference(arg, context)

        then: "验证结果"
        result != null
        result.getCount() == 1
        result.getDataList().size() == 1
    }

    def "test buttonReference with invalid inputs"() {
        given: "准备测试数据"
        def user = User.systemUser('74255')
        def requestContext = RequestContext.builder()
                .tenantId('74255')
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .build()
        RequestContextManager.setContext(requestContext)
        def context = new ServiceContext(requestContext, "cacheValidate", "cacheValidate")

        when: "调用方法"
        def result = objectSandboxService.buttonReference(arg, context)

        then: "验证结果"
        result != null
        result.getCount() == 0
        result.getDataList().isEmpty()

        and: "设置对应的打桩"
        if (arg?.searchQueryInfo?.filters) {
            buttonService.findButtonByApiNameForDesigner(_, _, _) >> null
            actionService.findActionListForDesigner(_, _, _) >> []
            referenceLogicService.queryByTargetList(_, _) >> []
        }

        where: "测试不同的无效输入场景"
        arg << [
                null,  // 空参数
                new ChangeSetFindByQuery.Arg(),  // 空 SearchQueryInfo
                createArgWithMissingFilter("api_name"),  // 缺少 api_name
                createArgWithMissingFilter("describe_api_name"),  // 缺少 describe_api_name
                createArgWithMissingFilter("tenant_id"),  // 缺少 tenant_id
                createArgWithEmptyFilters()  // 空过滤器列表
        ]
    }

    private static ChangeSetFindByQuery.Arg createArgWithEmptyFilters() {
        def arg = new ChangeSetFindByQuery.Arg()
        def searchQueryInfo = new ChangeSetFindByQuery.SearchQueryInfo()
        searchQueryInfo.setFilters([])
        arg.setSearchQueryInfo(searchQueryInfo)
        arg.setEnterpriseId(123L)
        return arg
    }

    private static ChangeSetFindByQuery.Arg createArgWithMissingFilter(String missingField) {
        def arg = new ChangeSetFindByQuery.Arg()
        def searchQueryInfo = new ChangeSetFindByQuery.SearchQueryInfo()
        def filters = [
                new ChangeSetFindByQuery.Filter(fieldName: "api_name", values: ["testApiName"]),
                new ChangeSetFindByQuery.Filter(fieldName: "describe_api_name", values: ["testDescribeApiName"]),
                new ChangeSetFindByQuery.Filter(fieldName: "tenant_id", values: ["testTenantId"])
        ]
        filters.removeIf { it.fieldName == missingField }
        searchQueryInfo.setFilters(filters)
        arg.setSearchQueryInfo(searchQueryInfo)
        arg.setEnterpriseId(123L)
        return arg
    }
} 