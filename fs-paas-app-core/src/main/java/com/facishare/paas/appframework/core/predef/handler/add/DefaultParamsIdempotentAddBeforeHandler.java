package com.facishare.paas.appframework.core.predef.handler.add;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CacheContext;
import com.facishare.paas.appframework.common.util.ContextCacheKeys;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.handler.add.AddActionHandler;
import com.facishare.paas.appframework.core.predef.service.ParamsIdempotentService;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by zhouwr on 2023/3/16.
 */
@Slf4j
@Component
@HandlerProvider(name = "defaultParamsIdempotentAddBeforeHandler")
public class DefaultParamsIdempotentAddBeforeHandler implements AddActionHandler {
    @Autowired
    private ParamsIdempotentService paramsIdempotentService;

    @Override
    public Result handle(HandlerContext context, Arg arg) {
        if (useParamsIdempotent(context, arg)) {
            doParamsIdempotent(context, arg);
        }
        return new Result();
    }

    private boolean useParamsIdempotent(HandlerContext context, Arg arg) {
        if (!RequestUtil.needParamsIdempotent()) {
            return false;
        }
        if (!arg.getInterfaceArg().useParamsIdempotent()) {
            return false;
        }
        return AppFrameworkConfig.isGrayParamsIdempotent(context.getTenantId(), arg.getObjectApiName());
    }

    private void doParamsIdempotent(HandlerContext context, Arg arg) {
        RLock paramsIdempotentLock = paramsIdempotentService.getLock(
                context.getUser(),
                arg.getInterfaceArg().getObjectData(),
                arg.getInterfaceArg().getDetails());
        boolean lock = paramsIdempotentService.lock(paramsIdempotentLock);
        if (!lock) {
            log.warn("doParamsIdempotent lock fail,name:{}", paramsIdempotentLock.getName());
            throw new AcceptableValidateException(buildParamsIdempotentResult());
        }
        CacheContext.getContext().setCache(ContextCacheKeys.PARAMS_IDEMPOTENT_LOCK, paramsIdempotentLock);
    }

    private BaseObjectSaveAction.Result buildParamsIdempotentResult() {
        BaseObjectSaveAction.ValidationMessage validateMessage = BaseObjectSaveAction.ValidationMessage.builder()
                .isMatch(true)
                .nonBlockMessages(Lists.newArrayList(I18N.text(I18NKey.VALIDATE_PARAMS_IDEMPOTENT_FAILED)))
                .build();
        return BaseObjectSaveAction.Result.builder()
                .paramsIdempotentMessage(validateMessage)
                .build();
    }
}
