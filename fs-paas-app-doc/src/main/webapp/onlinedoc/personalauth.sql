-- Drop table
 
-- DROP TABLE dev_personal_auth;
 
CREATE TABLE IF NOT EXISTS dev_personal_auth (
    id varchar(64) NOT NULL,
    tenant_id varchar(16) NOT NULL,
    employee_id varchar(32) NOT NULL,
    app_type varchar(128) NOT NULL,
    plugin_api_name varchar(128) NOT NULL,
    runtime_data jsonb NOT NULL,
    expired_time int8 NOT NULL,
    object_describe_api_name varchar(100) NOT NULL,
    created_by varchar(32) NOT NULL,
    last_modified_by varchar(32) NOT NULL,
    create_time int8 NOT NULL,
    last_modified_time int8 NOT NULL,
    version int8 NOT NULL,
    is_deleted int2 NOT NULL,
    sys_modified_time int8 NOT NULL,
    CONSTRAINT dev_personal_auth_pkey PRIMARY KEY (id, tenant_id)
);

-- Table Index
CREATE UNIQUE INDEX IF NOT EXISTS dev_personal_auth_index_tid_eid_apptype_pluginapiname 
ON dev_personal_auth 
USING btree (tenant_id, employee_id, app_type, plugin_api_name) 
WHERE (is_deleted = 0);
 
-- Table Triggers
DROP TRIGGER IF EXISTS x_audit_changes ON dev_personal_auth;
create trigger x_audit_changes after
insert
    or
delete
    or
update
    on
    dev_personal_auth for each row execute function f_change_detail(
        'id',
        'tenant_id',
        'object_describe_api_name');

DROP TRIGGER IF EXISTS x_system_changes ON dev_personal_auth;
CREATE TRIGGER x_system_changes BEFORE 
INSERT OR UPDATE 
ON dev_personal_auth FOR EACH ROW EXECUTE PROCEDURE public.f_system_change();