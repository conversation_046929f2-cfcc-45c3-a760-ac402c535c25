package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.privilege.dto.DataSharing;
import com.facishare.paas.appframework.privilege.dto.EntitySharePojo;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.appframework.privilege.DataPrivilegeService.DEPART_TYPE;
import static com.facishare.paas.appframework.privilege.DataPrivilegeService.ORGANIZATION_TYPE;
import static com.facishare.paas.appframework.privilege.DataPrivilegeService.ROLE_TYPE;
import static com.facishare.paas.appframework.privilege.DataPrivilegeService.USER_GROUP_TYPE;
import static com.facishare.paas.appframework.privilege.DataPrivilegeService.USER_TYPE;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toSet;

/**
 * create by zhaoju on 2019/08/26
 */
@Slf4j
@Service
public class DataSharingProcessor {
    @Autowired
    private OrgService orgService;
    @Autowired
    private UserRoleInfoService userRoleInfoService;
    @Autowired
    private IObjectDescribeService objectDescribeService;

    public EntitySharePojo.EntitySharePojoHelper processorByDataSharing(DataSharing dataSharing, User user) {
        Table<Integer, String, String> rangeTable = HashBasedTable.create();

        List<String> userIds = Stream.of(dataSharing.getSourceRanges().getUser(),
                dataSharing.getTargetRanges().getUser())
                .flatMap(Collection::stream).distinct().collect(Collectors.toList());
        getUserName(user, rangeTable, userIds);

        List<String> deptIds = Stream.of(dataSharing.getSourceRanges().getDepartment(),
                dataSharing.getTargetRanges().getDepartment())
                .flatMap(Collection::stream).distinct().collect(Collectors.toList());
        getDeptName(user, rangeTable, deptIds);

        List<String> orgIds = Stream
          .of(dataSharing.getSourceRanges().getShareRuleOrgIds(), dataSharing.getTargetRanges().getShareRuleOrgIds())
          .flatMap(Collection::stream)
          .distinct()
          .collect(Collectors.toList());
        getOrganizationName(user, rangeTable, orgIds);

        List<String> roleIds = Stream.of(dataSharing.getSourceRanges().getRole(),
                dataSharing.getTargetRanges().getRole())
                .flatMap(Collection::stream).distinct().collect(Collectors.toList());
        getRoleName(user, rangeTable, roleIds);

        List<String> groupIds = Stream.of(dataSharing.getSourceRanges().getUserGroup(),
                dataSharing.getTargetRanges().getUserGroup())
                .flatMap(Collection::stream).distinct().collect(Collectors.toList());
        getUserGroupName(user, rangeTable, groupIds);

        Map<String, IObjectDescribe> describeMap = findObjects(dataSharing.getDescribeApiNameList(), user);
        return EntitySharePojo.EntitySharePojoHelper.of(rangeTable, describeMap);
    }

    public EntitySharePojo.EntitySharePojoHelper processorByEntitySharePojo(List<EntitySharePojo> entitySharePojoList, User user) {
        Table<Integer, String, String> rangeTable = HashBasedTable.create();
        Map<Integer, Set<String>> receiveMap = entitySharePojoList.stream()
                .collect(groupingBy(EntitySharePojo::getReceiveType, mapping(EntitySharePojo::getReceiveId, toSet())));
        Map<Integer, Set<String>> shareMap = entitySharePojoList.stream()
                .collect(groupingBy(EntitySharePojo::getShareType, mapping(EntitySharePojo::getShareId, toSet())));

        shareMap.forEach((type, ids) -> receiveMap.computeIfAbsent(type, x -> Sets.newHashSet()).addAll(ids));

        getUserGroupName(user, rangeTable, Lists.newArrayList(receiveMap.getOrDefault(USER_GROUP_TYPE, Sets.newHashSet())));
        getRoleName(user, rangeTable, Lists.newArrayList(receiveMap.getOrDefault(ROLE_TYPE, Sets.newHashSet())));
        getDeptName(user, rangeTable, Lists.newArrayList(receiveMap.getOrDefault(DEPART_TYPE, Sets.newHashSet())));
        getUserName(user, rangeTable, Lists.newArrayList(receiveMap.getOrDefault(USER_TYPE, Sets.newHashSet())));

        List<String> describeApiNames = entitySharePojoList.stream().map(EntitySharePojo::getEntityId).collect(Collectors.toList());
        Map<String, IObjectDescribe> describeMap = findObjects(describeApiNames, user);
        return EntitySharePojo.EntitySharePojoHelper.of(rangeTable, describeMap);
    }

    private Map<String, IObjectDescribe> findObjects(List<String> describeApiNameList, User user) {
        try {
            List<IObjectDescribe> describeList = objectDescribeService.findDescribeListByApiNames(user.getTenantId(),
                    describeApiNameList, ActionContextExt.of(user).getContext());
            return describeList.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, x -> x, (x, y) -> y));
        } catch (MetadataServiceException e) {
            log.warn("error in find describeList by apiName list,tenantId:{},apiNames:{}", user.getTenantId(), describeApiNameList, e);
            throw new MetaDataBusinessException(e);
        }
    }


    private void getUserGroupName(User user, Table<Integer, String, String> rangeTable, List<String> groupIds) {
        Map<String, String> groupNameMap = orgService.getGroupNameByIds(user.getTenantId(), user.getUserId(), groupIds);
        groupNameMap.forEach((id, name) -> rangeTable.put(USER_GROUP_TYPE, id, name));
    }

    private void getRoleName(User user, Table<Integer, String, String> rangeTable, List<String> roleIds) {
        Map<String, String> roleMap = userRoleInfoService.queryRoleNameByRoleCode(user, roleIds);
        roleMap.forEach((id, name) -> rangeTable.put(ROLE_TYPE, id, name));
    }

    private void getDeptName(User user, Table<Integer, String, String> rangeTable, List<String> deptIds) {
        List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = orgService.getDeptInfoNameByIds(user.getTenantId(), user.getUserId(), deptIds);
        Map<String, String> deptNameMap = deptInfos.stream().collect(Collectors.toMap(x -> x.getDeptId(), x -> x.getDeptName()));
        deptNameMap.forEach((id, name) -> rangeTable.put(DEPART_TYPE, id, name));
    }

    private void getOrganizationName(User user, Table<Integer, String, String> rangeTable, List<String> orgIds) {
        List<QueryDeptInfoByDeptIds.DeptInfo> orgInfos = orgService.getDeptInfoNameByIds(user.getTenantId(), user.getUserId(), orgIds);
        Map<String, String> orgNameMap = orgInfos.stream().collect(Collectors.toMap(x -> x.getDeptId(), x -> x.getDeptName()));
        orgNameMap.forEach((id, name) -> rangeTable.put(ORGANIZATION_TYPE, id, name));
    }

    private void getUserName(User user, Table<Integer, String, String> rangeTable, List<String> userIds) {
        Map<String, String> userNameMap = orgService.getUserNameMapByIds(user.getTenantId(), user.getUserId(), userIds);
        userNameMap.forEach((id, name) -> rangeTable.put(USER_TYPE, id, name));
    }

}
