package com.facishare.paas.appframework.rest.dto.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by zhouwr on 2018/12/14
 */
public interface FindBySearchTemplateQuery {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        String describeAPIName;
        Boolean includeDescribe;
        SearchTemplateQuery searchTemplateQuery;
        List<String> fieldList;
        Boolean esSearchSkipRecentUpdateCheck;
        Boolean selectSpecialFields;
        Boolean keepAllMultiLangValue;
        /**
         * 查询公共对象数据，忽略可见范围字段
         */
        Boolean ignoreBaseVisibleRange;
    }

    @Data
    class Result {

        @JSONField(name = "describe")
        @JsonProperty(value = "describe")
        @SerializedName("describe")
        ObjectDescribeDocument describeDocument;

        @JSONField(name = "queryResult")
        @JsonProperty(value = "queryResult")
        @SerializedName("queryResult")
        QueryResultInfo queryResult;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class QueryResultInfo {
        List<ObjectDataDocument> data;
        Integer totalNumber;
    }

}
