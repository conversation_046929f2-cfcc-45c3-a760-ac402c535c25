package com.facishare.paas.appframework.metadata.wishFul;

import com.google.common.collect.Lists;
import lombok.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public interface PageQueryHistories {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Arg {
        String lastTime;
        Integer pageSize;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Result {
        Boolean hasMore;
        List<HistoryInfo> items = Lists.newArrayList();
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class HistoryInfo {
        String id;
        String objApiName;
        String objDataId;
        String objDataName;
        String objDisplayName;
        String subTitle;
        String time;
        String title;
        Integer type;
        Map<String, Object> extraDataVo;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            HistoryInfo that = (HistoryInfo) o;
            return Objects.equals(objApiName, that.objApiName);
        }

        @Override
        public int hashCode() {
            return Objects.hash(objApiName);
        }
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class RecentUseObj {
        String apiName;
        String displayName;
    }
}
