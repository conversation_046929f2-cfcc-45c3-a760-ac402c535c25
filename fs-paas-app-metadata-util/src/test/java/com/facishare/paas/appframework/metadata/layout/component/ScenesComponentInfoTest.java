package com.facishare.paas.appframework.metadata.layout.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class ScenesComponentInfoTest {

  private ScenesComponentInfo scenesComponentInfo;

  @BeforeEach
  void setUp() {
    scenesComponentInfo = new ScenesComponentInfo();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ScenesComponentInfo的无参构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试无参构造函数")
  void testDefaultConstructor() {
    ScenesComponentInfo info = new ScenesComponentInfo();
    assertNotNull(info, "无参构造函数创建的对象不应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ScenesComponentInfo的Map构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = Maps.newHashMap();
    map.put(IScenesComponentInfo.ORDER, Lists.newArrayList("scene1", "scene2"));
    map.put(IScenesComponentInfo.HIDDEN, Lists.newArrayList("hidden_scene"));
    map.put(IScenesComponentInfo.RENDER_TYPE, "dropdown");
    map.put(IScenesComponentInfo.PAGE_TYPE, "list");
    
    ScenesComponentInfo info = new ScenesComponentInfo(map);
    assertNotNull(info, "Map构造函数创建的对象不应为空");
    assertEquals(2, info.getOrder().size(), "应正确设置顺序列表");
    assertEquals(1, info.getHidden().size(), "应正确设置隐藏列表");
    assertEquals("dropdown", info.getRenderType(), "应正确设置渲染类型");
    assertEquals("list", info.getPageType(), "应正确设置页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试defaultSceneInfo静态工厂方法
   */
  @Test
  @DisplayName("正常场景 - 测试defaultSceneInfo静态工厂方法")
  void testDefaultSceneInfo() {
    IScenesComponentInfo info = ScenesComponentInfo.defaultSceneInfo();
    
    assertNotNull(info, "静态工厂方法创建的对象不应为空");
    assertEquals(IListComponentInfo.RENDER_TYPE_DROP_DOWN, info.getRenderType(), "应设置默认渲染类型为下拉框");
    assertEquals(IComponentInfo.PAGE_TYPE_LIST, info.getPageType(), "应设置默认页面类型为列表");
    assertNotNull(info.getOrder(), "顺序列表不应为空");
    assertTrue(info.getOrder().isEmpty(), "默认顺序列表应为空");
    assertNotNull(info.getHidden(), "隐藏列表不应为空");
    assertTrue(info.getHidden().isEmpty(), "默认隐藏列表应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getOrder和setOrder方法
   */
  @Test
  @DisplayName("正常场景 - 测试Order属性")
  void testOrder() {
    // 初始状态应为空列表
    List<String> initialOrder = scenesComponentInfo.getOrder();
    assertNotNull(initialOrder, "初始顺序列表不应为空");
    assertTrue(initialOrder.isEmpty(), "初始顺序列表应为空");
    
    // 设置顺序列表
    List<String> orderList = Lists.newArrayList("scene_a", "scene_b", "scene_c");
    scenesComponentInfo.setOrder(orderList);
    
    List<String> retrievedOrder = scenesComponentInfo.getOrder();
    assertNotNull(retrievedOrder, "获取的顺序列表不应为空");
    assertEquals(3, retrievedOrder.size(), "顺序列表大小应为3");
    assertTrue(retrievedOrder.contains("scene_a"), "应包含scene_a");
    assertTrue(retrievedOrder.contains("scene_b"), "应包含scene_b");
    assertTrue(retrievedOrder.contains("scene_c"), "应包含scene_c");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getHidden和setHidden方法
   */
  @Test
  @DisplayName("正常场景 - 测试Hidden属性")
  void testHidden() {
    // 初始状态应为空列表
    List<String> initialHidden = scenesComponentInfo.getHidden();
    assertNotNull(initialHidden, "初始隐藏列表不应为空");
    assertTrue(initialHidden.isEmpty(), "初始隐藏列表应为空");
    
    // 设置隐藏列表
    List<String> hiddenList = Lists.newArrayList("hidden_scene_1", "hidden_scene_2");
    scenesComponentInfo.setHidden(hiddenList);
    
    List<String> retrievedHidden = scenesComponentInfo.getHidden();
    assertNotNull(retrievedHidden, "获取的隐藏列表不应为空");
    assertEquals(2, retrievedHidden.size(), "隐藏列表大小应为2");
    assertTrue(retrievedHidden.contains("hidden_scene_1"), "应包含hidden_scene_1");
    assertTrue(retrievedHidden.contains("hidden_scene_2"), "应包含hidden_scene_2");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setOrder方法 - 空列表场景
   */
  @Test
  @DisplayName("边界场景 - 测试setOrder方法处理空列表")
  void testSetOrder_EmptyList() {
    List<String> emptyList = Lists.newArrayList();
    scenesComponentInfo.setOrder(emptyList);
    
    List<String> retrievedOrder = scenesComponentInfo.getOrder();
    assertNotNull(retrievedOrder, "获取的顺序列表不应为空");
    assertTrue(retrievedOrder.isEmpty(), "设置空列表应返回空顺序列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setOrder方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试setOrder方法处理null值")
  void testSetOrder_NullValue() {
    scenesComponentInfo.setOrder(null);
    
    List<String> retrievedOrder = scenesComponentInfo.getOrder();
    assertNotNull(retrievedOrder, "获取的顺序列表不应为空");
    assertTrue(retrievedOrder.isEmpty(), "设置null值应返回空顺序列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setHidden方法 - 空列表场景
   */
  @Test
  @DisplayName("边界场景 - 测试setHidden方法处理空列表")
  void testSetHidden_EmptyList() {
    List<String> emptyList = Lists.newArrayList();
    scenesComponentInfo.setHidden(emptyList);
    
    List<String> retrievedHidden = scenesComponentInfo.getHidden();
    assertNotNull(retrievedHidden, "获取的隐藏列表不应为空");
    assertTrue(retrievedHidden.isEmpty(), "设置空列表应返回空隐藏列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setHidden方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试setHidden方法处理null值")
  void testSetHidden_NullValue() {
    scenesComponentInfo.setHidden(null);
    
    List<String> retrievedHidden = scenesComponentInfo.getHidden();
    assertNotNull(retrievedHidden, "获取的隐藏列表不应为空");
    assertTrue(retrievedHidden.isEmpty(), "设置null值应返回空隐藏列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getRenderType方法
   */
  @Test
  @DisplayName("正常场景 - 测试getRenderType方法")
  void testGetRenderType() {
    // 初始状态应为null
    assertNull(scenesComponentInfo.getRenderType(), "初始渲染类型应为null");
    
    // 通过Map构造函数设置渲染类型
    Map<String, Object> map = Maps.newHashMap();
    map.put(IScenesComponentInfo.RENDER_TYPE, "tabs");
    ScenesComponentInfo info = new ScenesComponentInfo(map);
    assertEquals("tabs", info.getRenderType(), "应正确获取渲染类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getPageType方法
   */
  @Test
  @DisplayName("正常场景 - 测试getPageType方法")
  void testGetPageType() {
    // 初始状态应为null
    assertNull(scenesComponentInfo.getPageType(), "初始页面类型应为null");
    
    // 通过Map构造函数设置页面类型
    Map<String, Object> map = Maps.newHashMap();
    map.put(IScenesComponentInfo.PAGE_TYPE, "detail");
    ScenesComponentInfo info = new ScenesComponentInfo(map);
    assertEquals("detail", info.getPageType(), "应正确获取页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试copy方法
   */
  @Test
  @DisplayName("正常场景 - 测试copy方法")
  void testCopy() {
    // 设置一些属性
    List<String> orderList = Lists.newArrayList("order1", "order2");
    List<String> hiddenList = Lists.newArrayList("hidden1");
    
    Map<String, Object> map = Maps.newHashMap();
    map.put(IScenesComponentInfo.ORDER, orderList);
    map.put(IScenesComponentInfo.HIDDEN, hiddenList);
    map.put(IScenesComponentInfo.RENDER_TYPE, "button_group");
    map.put(IScenesComponentInfo.PAGE_TYPE, "edit");
    
    ScenesComponentInfo original = new ScenesComponentInfo(map);
    IScenesComponentInfo copied = original.copy();
    
    assertNotNull(copied, "复制的对象不应为空");
    assertNotSame(original, copied, "复制的对象应是不同的实例");
    assertEquals(original.getOrder().size(), copied.getOrder().size(), "复制的对象应有相同的顺序列表大小");
    assertEquals(original.getHidden().size(), copied.getHidden().size(), "复制的对象应有相同的隐藏列表大小");
    assertEquals(original.getRenderType(), copied.getRenderType(), "复制的对象应有相同的渲染类型");
    assertEquals(original.getPageType(), copied.getPageType(), "复制的对象应有相同的页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口实现
   */
  @Test
  @DisplayName("正常场景 - 测试接口实现")
  void testInterfaceImplementation() {
    // 验证实现了IScenesComponentInfo接口
    assertTrue(scenesComponentInfo instanceof IScenesComponentInfo, 
               "应实现IScenesComponentInfo接口");
    
    // 验证实现了JsonCompatible接口
    assertTrue(scenesComponentInfo instanceof com.facishare.paas.metadata.api.JsonCompatible, 
               "应实现JsonCompatible接口");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试序列化版本号
   */
  @Test
  @DisplayName("正常场景 - 测试序列化版本号")
  void testSerialVersionUID() {
    // 验证类有序列化版本号（通过反射检查）
    try {
      java.lang.reflect.Field field = ScenesComponentInfo.class.getDeclaredField("serialVersionUID");
      field.setAccessible(true);
      long serialVersionUID = field.getLong(null);
      assertEquals(2490474085619020130L, serialVersionUID, "序列化版本号应正确");
    } catch (Exception e) {
      fail("应该有serialVersionUID字段");
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试继承的AbstractComponentInfoDocument功能
   */
  @Test
  @DisplayName("正常场景 - 测试继承功能")
  void testInheritedFunctionality() {
    // 验证继承自AbstractComponentInfoDocument
    assertTrue(scenesComponentInfo instanceof AbstractComponentInfoDocument, 
               "应继承自AbstractComponentInfoDocument");
    
    // 通过Map构造函数测试继承的功能
    Map<String, Object> map = Maps.newHashMap();
    map.put("custom_field", "custom_value");
    map.put(IScenesComponentInfo.RENDER_TYPE, "custom_render");
    
    ScenesComponentInfo info = new ScenesComponentInfo(map);
    assertEquals("custom_render", info.getRenderType(), "应正确处理渲染类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试复杂的场景配置
   */
  @Test
  @DisplayName("正常场景 - 测试复杂场景配置")
  void testComplexSceneConfiguration() {
    // 创建复杂的场景配置
    List<String> complexOrder = Lists.newArrayList(
        "all_scenes", "active_scenes", "completed_scenes", 
        "pending_scenes", "archived_scenes", "draft_scenes"
    );
    
    List<String> complexHidden = Lists.newArrayList(
        "internal_scene", "test_scene", "deprecated_scene"
    );
    
    Map<String, Object> map = Maps.newHashMap();
    map.put(IScenesComponentInfo.ORDER, complexOrder);
    map.put(IScenesComponentInfo.HIDDEN, complexHidden);
    map.put(IScenesComponentInfo.RENDER_TYPE, "advanced_tabs");
    map.put(IScenesComponentInfo.PAGE_TYPE, "dashboard");
    
    ScenesComponentInfo info = new ScenesComponentInfo(map);
    
    // 验证复杂配置
    assertEquals(6, info.getOrder().size(), "应有6个顺序场景");
    assertEquals(3, info.getHidden().size(), "应有3个隐藏场景");
    assertEquals("advanced_tabs", info.getRenderType(), "渲染类型应正确");
    assertEquals("dashboard", info.getPageType(), "页面类型应正确");
    
    // 验证所有顺序场景都存在
    for (String scene : complexOrder) {
      assertTrue(info.getOrder().contains(scene), "应包含顺序场景: " + scene);
    }
    
    // 验证所有隐藏场景都存在
    for (String scene : complexHidden) {
      assertTrue(info.getHidden().contains(scene), "应包含隐藏场景: " + scene);
    }
    
    // 测试复制功能
    IScenesComponentInfo copied = info.copy();
    assertEquals(info.getOrder().size(), copied.getOrder().size(), "复制的对象应有相同的顺序场景数量");
    assertEquals(info.getHidden().size(), copied.getHidden().size(), "复制的对象应有相同的隐藏场景数量");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试多种渲染类型
   */
  @Test
  @DisplayName("正常场景 - 测试多种渲染类型")
  void testVariousRenderTypes() {
    String[] renderTypes = {
        IListComponentInfo.RENDER_TYPE_DROP_DOWN,
        "tabs", "button_group", "radio_group", "checkbox_group", "accordion"
    };
    
    for (String renderType : renderTypes) {
      Map<String, Object> map = Maps.newHashMap();
      map.put(IScenesComponentInfo.RENDER_TYPE, renderType);
      
      ScenesComponentInfo info = new ScenesComponentInfo(map);
      assertEquals(renderType, info.getRenderType(), "应正确设置渲染类型: " + renderType);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试特殊字符场景名称
   */
  @Test
  @DisplayName("边界场景 - 测试特殊字符场景名称")
  void testSpecialCharacterSceneNames() {
    // 测试包含特殊字符的场景名称
    List<String> specialScenes = Lists.newArrayList(
        "scene_with_underscore",
        "scene-with-dash",
        "scene.with.dot",
        "<EMAIL>",
        "场景中文名称",
        "scene with spaces"
    );
    
    scenesComponentInfo.setOrder(specialScenes);
    
    List<String> retrievedOrder = scenesComponentInfo.getOrder();
    assertEquals(6, retrievedOrder.size(), "应正确处理特殊字符场景名称");
    
    for (String scene : specialScenes) {
      assertTrue(retrievedOrder.contains(scene), "应包含特殊字符场景: " + scene);
    }
  }
}
