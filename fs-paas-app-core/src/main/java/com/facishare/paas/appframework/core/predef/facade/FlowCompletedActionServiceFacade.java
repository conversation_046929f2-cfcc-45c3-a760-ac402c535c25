package com.facishare.paas.appframework.core.predef.facade;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.core.predef.handler.flowcompleted.FlowCompletedActionHandler;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.handler.HandlerType;
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2023/9/26.
 */
public interface FlowCompletedActionServiceFacade {
    void checkDuplicate(User user, IObjectDescribe objectDescribe, IObjectData data, Map<String, Object> callbackData,
                        StandardFlowCompletedAction.Arg arg);

    void updateChangeOrderStatus(User user, IObjectDescribe objectDescribe, IObjectData data, StandardFlowCompletedAction.Arg arg);

    FlowCompletedActionHandler.Result executeTriggerActionTenantHandler(HandlerContext context,
                                                                        FlowCompletedActionHandler.Arg arg,
                                                                        List<HandlerType> handlerTypes);

    FlowCompletedActionHandler.Result executeTriggerActionTenantHandler(HandlerContext context,
                                                                        List<HandlerType> handlerTypes,
                                                                        List<SimpleHandlerDescribe> handlerDescribes,
                                                                        boolean executeByFlowStartCallback,
                                                                        ApprovalFlowTriggerType triggerType,
                                                                        Map<String, Object> callbackData,
                                                                        String objectApiName,
                                                                        Map<String, IObjectDescribe> describeMap,
                                                                        IObjectData data,
                                                                        Map<String, List<IObjectData>> detailDataMap,
                                                                        IObjectData dbData);

    void doPostAction(RequestContext requestContext,
                      String objectApiName,
                      IObjectData data,
                      Map<String, List<IObjectData>> detailDataMap,
                      Map<String, Object> callbackData,
                      ObjectAction action,
                      Map<String, Object> actionParam);

    void updateDetailObjectDataLifeStatus(ApprovalFlowTriggerType triggerType,
                                          User user,
                                          IObjectData data,
                                          IObjectData dbData,
                                          Map<String, List<IObjectData>> detailDataMap,
                                          Map<String, IObjectDescribe> detailDescribeMap);
}
