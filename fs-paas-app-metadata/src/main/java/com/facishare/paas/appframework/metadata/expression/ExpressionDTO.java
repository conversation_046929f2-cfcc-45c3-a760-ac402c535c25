package com.facishare.paas.appframework.metadata.expression;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 替代 ExpressionCalculatePojo
 * <p>
 * 表达式数据传输对象，这是一个大杂烩对象，用于表达式的语法校验
 * <p>
 * Created by li<PERSON><PERSON><PERSON> on 2018/5/9.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExpressionDTO {
    private String expression;
    private String objectDescribeApiName;
    private String calculateFieldApiName;
    private String returnType;
    private String expressionType;
    private Boolean setDefaultToZero;
    private int decimalPlaces;
    private Boolean onlySupportGrounded;
    private String expressionLabel;

    @JSONField(serialize = false, deserialize = false)
    private List<FormVariableDTO> extFields;

    public boolean isFieldUpdateExpression() {
        return !Strings.isNullOrEmpty(expressionType) && ExpressionType.UPDATE_FIELD.equals(expressionType);
    }

    public boolean isValidationExpression() {
        return !Strings.isNullOrEmpty(expressionType) && ExpressionType.VALIDATION_RULE.equals(expressionType);
    }

    public boolean isDefaultValue() {
        return !Strings.isNullOrEmpty(expressionType) && ExpressionType.DEFAULT_VALUE.equals(expressionType);
    }

    public boolean isFormula() {
        return !Strings.isNullOrEmpty(expressionType) && ExpressionType.FORMULA.equals(expressionType);
    }


    public boolean isCalculateField() {
        //计算公式或默认值计算
        return isDefaultValue() || isFormula();
    }

    public String getExpressionLabel() {
        if (Strings.isNullOrEmpty(expressionLabel)) {
            return I18NExt.text(I18NKey.EXPRESSION);
        }
        return expressionLabel;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FormVariableDTO {
        private String fieldName;
        private Object value;
        private String type;

        public static FormVariableDTO of(SimpleExpression.VariableInfo variableInfo) {
            return FormVariableDTO.builder()
                    .fieldName(variableInfo.getId())
                    .type(variableInfo.getType())
                    .build();
        }
    }

    public static ExpressionDTO of(String json) {
        return JSON.parseObject(json, ExpressionDTO.class);
    }

    //计算公式的试用场景
    public interface ExpressionType {
        String VALIDATION_RULE = "rule";
        String UPDATE_FIELD = "button_field_update";
        String FORMULA = "formula";
        String DEFAULT_VALUE = "default_value";
    }
}
