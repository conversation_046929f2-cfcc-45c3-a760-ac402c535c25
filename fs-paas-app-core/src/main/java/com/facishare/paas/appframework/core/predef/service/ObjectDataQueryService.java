package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.service.dto.data.*;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.license.util.LicenseConstants;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.query.SearchQueryContext;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.expression.ExpressionService;
import com.facishare.paas.expression.type.PDate;
import com.facishare.paas.expression.type.PDateTime;
import com.facishare.paas.expression.type.PTime;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.NameCache;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 对象查询服务
 * <p>
 * Created by liyiguang on 2018/1/22.
 */
@ServiceModule("objects")
@Component
@Slf4j
public class ObjectDataQueryService {
    @Autowired
    private MetaDataService metaDataService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private ExpressionService expressionService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private PublicObjectService publicObjectService;

    private static final List<IFilter> DEFAULT_FILTERS = Lists.newArrayList();

    static {
        IFilter filter = new Filter();
        filter.setFieldName("life_status");
        filter.setOperator(Operator.NIN);
        filter.setFieldValues(Lists.newArrayList(
                UdobjConstants.LIFE_STATUS_VALUE_INVALID,
                UdobjConstants.LIFE_STATUS_VALUE_INEFFECTIVE,
                UdobjConstants.LIFE_STATUS_VALUE_UNDER_REVIEW));
        DEFAULT_FILTERS.add(filter);
    }

    @ServiceMethod("queryByCriteria")
    public QueryByCriteria.Result queryByCriteria(QueryByCriteria.Arg arg, ServiceContext context) {
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getObjectAPIName());
        QueryResult<IObjectData> queryResult = metaDataService.findBySearchQuery(
                context.getUser(), describe, arg.getObjectAPIName(), buildSearchQuery(ObjectDescribeExt.of(describe),
                        arg.getQueryCriteria()));

        QueryByCriteria.Result ret = QueryByCriteria.Result.builder()
                .dataList(ObjectDataDocument.ofList(queryResult.getData()))
                .build();

        return ret;

    }

    @ServiceMethod("findRecordName")
    public FindRecordName.Result findRecordName(FindRecordName.Arg arg, ServiceContext context) {
        if (CollectionUtils.empty(arg.getIdList())) {
            return FindRecordName.Result.builder().build();
        }
        List<INameCache> nameList = metaDataService.findRecordName(ActionContextExt.of(context.getUser()).getContext(), arg.getObjectApiName(), arg.getIdList());
        List<ObjectDataDocument> data = new ArrayList<>();
        nameList.forEach(x -> data.add(ObjectDataDocument.of(((NameCache) x).getContainerDocument())));
        FindRecordName.Result rst = FindRecordName.Result.builder().objectData(data).build();
        return rst;
    }

    @ServiceMethod("queryByFilter")
    public QueryByFilter.Result queryByFilter(QueryByFilter.Arg arg, ServiceContext context) {
        ISearchTemplateQuery query = arg.getQuery().toSearchTemplateQuery();
        query = adaptQuery(query, arg.getObjectAPIName());

        if (!Objects.isNull(arg.getDataParameter())) {
            query.setDataRightsParameter(arg.getDataParameter().toDataRightsParameter());
        }
        MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                .user(context.getUser())
                .isSimple(false)
                .esSearchSkipRecentUpdateCheck(false)
                .skipRelevantTeam(RequestUtil.skipRelevantTeam())
                .build();
        QueryResult<IObjectData> queryResult = metaDataService.findByQueryWithContext(queryContext, arg.getObjectAPIName(), SearchTemplateQueryExt.of(query).toSearchTemplateQuery());

        QueryByFilter.Result ret = QueryByFilter.Result.builder()
                .dataList(ObjectDataDocument.ofList(queryResult.getData()))
                .totalCount(queryResult.getTotalNumber())
                .build();
        return ret;

    }

    @ServiceMethod("findDataById")
    public FindDataById.Result findDataById(FindDataById.Arg arg, ServiceContext context) {
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());

        IObjectData objectData = metaDataService.findObjectData(context.getUser(), arg.getDataId(), describe);
        try {
            metaDataService.doDataPrivilegeCheck(context.getUser(), Lists.newArrayList(objectData), describe,
                    ObjectAction.VIEW_DETAIL.getActionCode());
        } catch (ValidateException e) {
            //无数据权限
            return FindDataById.Result.builder().build();
        }
        return FindDataById.Result.builder()
                .objectData(ObjectDataDocument.of(objectData))
                .build();
    }

    @ServiceMethod("findIdByName")
    public FindIdByName.Result findIdByName(FindIdByName.Arg arg, ServiceContext context) {
        log.debug("findIdByName,context:{},arg:{}", context, arg);
        if (CollectionUtils.empty(arg.getNameList()) ||
                Strings.isNullOrEmpty(arg.getObjectApiName())) {
            return FindIdByName.Result.builder().nameIdMap(Maps.newHashMap()).build();
        }

        Map<String, String> map = metaDataService.findObjectIdByName(context.getUser(), arg.getObjectApiName(), arg.getNameList());
        return FindIdByName.Result.builder().nameIdMap(map).build();
    }

    @ServiceMethod("findNameById")
    public FindNameById.Result findNameById(FindNameById.Arg arg, ServiceContext context) {
        if (CollectionUtils.empty(arg.getIdList()) || Strings.isNullOrEmpty(arg.getObjectApiName())) {
            return FindNameById.Result.builder().id2NameMap(Collections.emptyMap()).build();
        }
        Map<String, String> map = metaDataService.findNameByIds(context.getUser(), arg.getObjectApiName(), arg.getIdList());
        return FindNameById.Result.builder().id2NameMap(map).build();
    }

    @ServiceMethod("findReferencedData")
    public FindReferencedData.Result findReferencedData(FindReferencedData.Arg arg, ServiceContext context) {
        log.debug("findReferencedData,context:{},arg:{}", context, arg);
        if (CollectionUtils.empty(arg.getIdList()) || Strings.isNullOrEmpty(arg.getObjectApiName())) {
            return FindReferencedData.Result.builder().referencedDataList(Lists.newArrayList()).build();
        }

        List<String> list = metaDataService.findReferencedDataList(context.getUser(), arg.getObjectApiName(), arg.getIdList());
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(context.getTenantId(), list);
        //Map<String, String> map = I18NUtils.getObjectDisplayName(list);
        List<FindReferencedData.ReferencedData> resultList = describeMap.entrySet()
                .stream()
                .map(a -> FindReferencedData.ReferencedData.builder()
                        .apiName(a.getKey())
                        .label(a.getValue().getDisplayName())
                        .build()
                )
                .collect(Collectors.toList());
        return FindReferencedData.Result.builder().referencedDataList(resultList).build();
    }

    @ServiceMethod("validateFunction")
    public ValidateFunction.Result checkFunctionCountLimit(ValidateFunction.Arg arg, ServiceContext context) {
        Map<String, List<ObjectDataDocument>> map = CollectionUtils.empty(arg.getDetails()) ? Maps.newHashMap() : arg.getDetails();
        Map<String, List<IObjectData>> detail = map.entrySet().stream().collect(
                Collectors.toMap(Map.Entry::getKey,
                        a -> CollectionUtils.nullToEmpty(a.getValue())
                                .stream()
                                .map(ObjectDataDocument::toObjectData)
                                .collect(Collectors.toList())));
        ButtonExecutor.Arg executorArg = ButtonExecutor.Arg.builder()
                .buttonApiName(arg.getButtonApiName())
                .describeApiName(arg.getDescribeApiName())
                .objectDataId(arg.getObjectDataId())
                .args(arg.getArgs())
                .objectData(arg.getObjectData().toObjectData())
                .details(detail)
                .build();

        ButtonExecutor.Result result = infraServiceFacade.triggerValidationFunction(context.getUser(), executorArg);
        return ValidateFunction.Result.builder()
                .hasReturnValue(result.isHasReturnValue())
                .returnValue(result.getReturnValue())
                .build();
    }

    @ServiceMethod("isExistData")
    public IsExistData.Result isExistData(ServiceContext context, IsExistData.Arg arg) {
        boolean existData = metaDataService.existData(context.getTenantId(), arg.getDescribeApiName());
        return IsExistData.Result.builder().existData(existData).build();
    }

    @ServiceMethod("optionUsedByData")
    public OptionUsedByData.Result optionUsedByData(ServiceContext context, OptionUsedByData.Arg arg) {
        metaDataService.checkOptionUsedByData(context.getUser(), arg.getDescribeApiName(),
                arg.getFieldApiName(), arg.getValues());
        metaDataService.checkOptionUsedByReference(context.getUser(), arg.getDescribeApiName(),
                arg.getFieldApiName(), arg.getValues());
        return OptionUsedByData.Result.builder().used(false).build();
    }

    @ServiceMethod("supportOutTeamQuery")
    public SupportOutTeamQuery.Result supportOutTeamQuery(ServiceContext serviceContext, SupportOutTeamQuery.Arg arg) {
        if (serviceContext.getUser().isOutUser()) {
            return SupportOutTeamQuery.Result.builder().support(false).build();
        }

        boolean support = false;
        if (AppFrameworkConfig.isShowingOutTeamMemberGray(serviceContext.getTenantId())) {
            support = serviceFacade.existModule(serviceContext.getTenantId(), Sets.newHashSet(LicenseConstants.ModuleCode.INTERCONNECT_APP_BASIC_APP))
                    .getOrDefault(LicenseConstants.ModuleCode.INTERCONNECT_APP_BASIC_APP, false);
        }

        return SupportOutTeamQuery.Result.builder().support(support).build();
    }

    @ServiceMethod("convertPublicData")
    public PublicObject.DataConvertResult convertPublicData(PublicObject.DataArg arg, ServiceContext serviceContext) {
        if (Objects.isNull(arg) || CollectionUtils.empty(arg.getDataMap())) {
            return PublicObject.DataConvertResult.builder().success(true).build();
        }
        publicObjectService.convertPublicData(serviceContext.getUser(), arg.getDataMap());
        return PublicObject.DataConvertResult.builder().success(true).build();
    }

    @ServiceMethod("convertPrivateData")
    public PublicObject.DataConvertResult convertPrivateData(PublicObject.DataArg arg, ServiceContext serviceContext) {
        if (Objects.isNull(arg) || CollectionUtils.empty(arg.getDataMap())) {
            return PublicObject.DataConvertResult.builder().success(true).build();
        }
        publicObjectService.convertPrivateData(serviceContext.getUser(), arg.getDataMap());
        return PublicObject.DataConvertResult.builder().success(true).build();
    }

    @ServiceMethod("convertPublicDescribe")
    public PublicObject.DataConvertResult convertPublicDescribe(PublicObject.DescribeArg arg, ServiceContext serviceContext) {
        if (Objects.isNull(arg) || CollectionUtils.empty(arg.getObjectApiNames())) {
            return PublicObject.DataConvertResult.builder().success(true).build();
        }
        publicObjectService.convertPublicDescribe(serviceContext.getUser(), arg.getObjectApiNames());
        return PublicObject.DataConvertResult.builder().success(true).build();
    }

    @ServiceMethod("enablePublicDescribe")
    public PublicObject.DataConvertResult enablePublicDescribe(PublicObject.DescribeArg arg, ServiceContext serviceContext) {
        if (Objects.isNull(arg) || CollectionUtils.empty(arg.getObjectApiNames()) || StringUtils.isBlank(arg.getUpstreamTenantId())) {
            return PublicObject.DataConvertResult.builder().success(true).build();
        }
        publicObjectService.enablePublicDescribe(serviceContext.getUser(), arg.getUpstreamTenantId(), arg.getObjectApiNames());
        return PublicObject.DataConvertResult.builder().success(true).build();
    }

    @ServiceMethod("disablePublicDescribe")
    public PublicObject.DataConvertResult disablePublicDescribe(PublicObject.DescribeArg arg, ServiceContext serviceContext) {
        if (Objects.isNull(arg) || StringUtils.isBlank(arg.getUpstreamTenantId())) {
            return PublicObject.DataConvertResult.builder().success(true).build();
        }
        publicObjectService.disablePublicDescribe(serviceContext.getUser(), arg.getObjectApiNames());
        return PublicObject.DataConvertResult.builder().success(true).build();
    }

    @ServiceMethod("findPublicDescribeApiNames")
    public PublicObject.DescribeResult findPublicDescribeApiNames(PublicObject.DescribeArg arg, ServiceContext serviceContext) {
        if (Objects.isNull(arg) || CollectionUtils.empty(arg.getObjectApiNames())) {
            return PublicObject.DescribeResult.builder().build();
        }
        List<String> result = publicObjectService.findPublicDescribeApiNames(serviceContext.getUser(), arg.getObjectApiNames());
        return PublicObject.DescribeResult.builder().objectApiNames(result).build();
    }

    @ServiceMethod("findPublicObjectDataByIds")
    public PublicObject.DataResult findPublicObjectDataByIds(PublicObject.DataArg arg, ServiceContext serviceContext) {
        if (Objects.isNull(arg) || CollectionUtils.empty(arg.getDataMap())) {
            return PublicObject.DataResult.builder().build();
        }
        Map<String, List<String>> map = publicObjectService.findPublicObjectDataByIds(serviceContext.getUser(), arg.getDataMap());
        return PublicObject.DataResult.builder().dataMap(map).build();
    }

    @ServiceMethod("buildSearchQuery")
    public BuildSearchQuery.Result buildSearchQuery(BuildSearchQuery.Arg arg, ServiceContext serviceContext) {
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopy(serviceContext.getTenantId(), arg.getDescribeApiName());
        SearchQueryContext queryContext = SearchQueryContext.builder()
                .templateId(arg.getTemplateId())
                .searchTemplateType(arg.getSearchTemplateType())
                .usePattern(true)
                .build();
        Query searchQuery = serviceFacade.findSearchQuery(serviceContext.getUser(), objectDescribe, arg.getSearchQueryInfo(), queryContext);
        return BuildSearchQuery.Result.of(searchQuery.toSearchTemplateQuery().toJsonString());
    }

    private ISearchTemplateQuery adaptQuery(ISearchTemplateQuery query, String describeApiName) {
        if (Objects.isNull(query)) {
            query = new SearchTemplateQuery();
        }

        query.setLimit(Math.min(query.getLimit(), 500));
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, IObjectData.DESCRIBE_API_NAME, describeApiName);
        return query;
    }

    private SearchTemplateQuery buildSearchQuery(
            ObjectDescribeExt describeExt, QueryByCriteria.QueryCriteria queryCriteria) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(queryCriteria.getOffset());
        searchTemplateQuery.setLimit(queryCriteria.getLimit());

        if (!queryCriteria.hasOr()) {
            //不设置pattern
            List<IFilter> filters = queryCriteria.getCriteriaList().stream()
                    .map(x -> buildFilter(describeExt, x))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            searchTemplateQuery.addFilters(filters);
            searchTemplateQuery.addFilters(DEFAULT_FILTERS);
            return searchTemplateQuery;
        }

        List<IFilter> filters = queryCriteria.getCriteriaList().stream()
                .map(x -> buildFilter(describeExt, x))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (filters.size() != queryCriteria.getCriteriaPatternSize()) {
            log.warn("ignore filters due to filters:{} not match query pattern:{}", filters, queryCriteria.getCriteriaPattern());
            searchTemplateQuery.addFilters(DEFAULT_FILTERS);
            return searchTemplateQuery;
        }

        searchTemplateQuery.addFilters(filters);
        searchTemplateQuery.addFilters(DEFAULT_FILTERS);
        searchTemplateQuery.addFilters(buildAPINameFilter(describeExt));

        StringBuilder sb = new StringBuilder();
        int indexStart = queryCriteria.getCriteriaPatternSize();
        sb.append('(');
        sb.append(queryCriteria.getCriteriaPattern());
        sb.append(')');

        //for life status filter
        sb.append(" and ");
        sb.append(++indexStart);

        //for api name filter
        sb.append(" and ");
        sb.append(++indexStart);

        //for delete status filter
        sb.append(" and ");
        sb.append(++indexStart);

        searchTemplateQuery.setPattern(sb.toString());

        //log.debug("searchTemplateQuery:{}",searchTemplateQuery);

        return searchTemplateQuery;
    }

    private List<IFilter> buildAPINameFilter(ObjectDescribeExt describeExt) {
        IFilter filter = new Filter();
        filter.setFieldName(IObjectData.DESCRIBE_API_NAME);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(describeExt.getApiName()));

        return Lists.newArrayList(filter);
    }

    private IFilter buildFilter(ObjectDescribeExt describeExt, QueryByCriteria.Criteria criteria) {
        if (criteria.toOperator() == null) {
            log.warn("operator not support:{}", criteria.getOperator());
            return null;
        }
        Optional<IFieldDescribe> optional = describeExt.getFieldDescribeSilently(criteria.getLeft().getFieldName());
        if (!optional.isPresent()) {
            log.warn("criteria discard because of not existing field:{}" + criteria.getLeft().getFieldName());
            return null;
        }

        IFilter filter = new Filter();
        filter.setFieldName(criteria.getLeft().getFieldName());
        filter.setOperator(criteria.toOperator());
        IFieldDescribe fieldDescribe = optional.get();
        String type = fieldDescribe.getType();

        /**
         * 目前只有时间类型的字段进行计算
         */
        if ("date".equals(type) || "date_time".equals(type) || "time".equals(type)) {
            String value = getFiledValue(criteria);
            if (Strings.isNullOrEmpty(value)) {
                log.warn("criteria discard because of not existing field value:{}" + value);
                return null;
            }
            filter.setFieldValues(Lists.newArrayList(value));
        } else {
            filter.setFieldValues(Lists.newArrayList(criteria.getRight().getValue()));
        }
        return filter;
    }

    private String getFiledValue(QueryByCriteria.Criteria criteria) {
        try {
            long value = Long.parseLong(criteria.getRight().getValue());
            return String.valueOf(value);
        } catch (NumberFormatException e) {
            //带公式
        }

        Object value = expressionService.evaluate(criteria.getRight().getValue(), null);
        if (value == null) {
            return null;
        }
        if (value instanceof PDate) {
            long timestamp = ((PDate) value).toTimeStamp();
            return String.valueOf(timestamp);
        }

        if (value instanceof PDateTime) {
            long timestamp = ((PDateTime) value).toTimeStamp();
            return String.valueOf(timestamp);
        }

        if (value instanceof PTime) {
            long timestamp = ((PTime) value).toTimeStamp();
            return String.valueOf(timestamp);
        }
        return value.toString();
    }


}
