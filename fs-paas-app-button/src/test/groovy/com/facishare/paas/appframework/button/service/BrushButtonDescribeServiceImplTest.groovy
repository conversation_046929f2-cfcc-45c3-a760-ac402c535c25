package com.facishare.paas.appframework.button.service


import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.exception.FunctionException
import com.facishare.paas.appframework.core.exception.SystemErrorCode
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.function.FunctionLogicService
import com.facishare.paas.appframework.metadata.DescribeLogicServiceImpl
import com.facishare.paas.appframework.metadata.PostActionService
import com.facishare.paas.appframework.metadata.button.ButtonType
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException
import com.facishare.paas.appframework.metadata.relation.SourceTypes
import com.facishare.paas.appframework.metadata.relation.TargetTypes
import com.facishare.paas.metadata.api.IUdefAction
import com.facishare.paas.metadata.api.IUdefButton
import com.facishare.paas.metadata.api.IUdefFunction
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.service.IUdefButtonService
import com.facishare.paas.metadata.dao.pg.mapper.metadata.UdefButtonPGMapper
import com.facishare.paas.metadata.exception.MetadataServiceException
import com.facishare.paas.metadata.impl.UdefAction
import com.facishare.paas.metadata.impl.UdefButton
import com.facishare.paas.metadata.impl.UdefFunction
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.service.impl.UdefFunctionService
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

import static com.facishare.paas.appframework.metadata.ButtonExt.DEFAULT_ADD_BUTTON_API_NAME

@Unroll
class BrushButtonDescribeServiceImplTest extends Specification {

    BrushButtonDescribeServiceImpl brushButtonDescribeService
    DescribeLogicServiceImpl describeLogicService = Mock(DescribeLogicServiceImpl)
    IUdefButtonService buttonService = Mock(IUdefButtonService)
    PostActionService actionService = Mock(PostActionService)
    FunctionLogicService functionLogicService = Mock(FunctionLogicService)
    UdefButtonPGMapper udefButtonPGMapper = Mock(UdefButtonPGMapper)
    UdefFunctionService udefFunctionService = Mock(UdefFunctionService)
    BrushButtonDescribeServiceImpl brushButtonDescribeServiceMock = Mock(BrushButtonDescribeServiceImpl)

    def setup() {
        brushButtonDescribeService = new BrushButtonDescribeServiceImpl(
                describeLogicService: describeLogicService,
                buttonService: buttonService,
                actionService: actionService,
                functionLogicService: functionLogicService,
                udefButtonPGMapper: udefButtonPGMapper,
                brushButtonDescribeService: brushButtonDescribeServiceMock,
                udefFunctionService: udefFunctionService
        )
    }

    def setupSpec() {
        // Mock AppFrameworkConfig静态方法
        Whitebox.setInternalState(AppFrameworkConfig, "addEditUiActionGrayTenant", ['74255', '78057'] as Set)
        Whitebox.setInternalState(AppFrameworkConfig, "addEditUiActionGrayObject", ['AccountObj', 'ContactObj'] as Set)
        Whitebox.setInternalState(AppFrameworkConfig, "addEditUiActionGrayObjectEi", [
                '74255': ['AccountObj', 'ContactObj'] as Set,
                '78057': ['OpportunityObj'] as Set
        ])
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试刷新按钮描述正常场景，验证方法调用链
     */
    def "brushButtonDescribeTestNormal"() {
        given: "准备测试数据"
        def objectApiNames = ['AccountObj'] as Set
        def user = User.systemUser('74255')
        def filteredDescribeApiNames = ['AccountObj'] as Set
        def objects = ['AccountObj': createObjectDescribe()]
        def describeApiNames = ['AccountObj']
        def buttonMap = ['AccountObj': [createButton()]]

        when: "调用刷新按钮描述方法"
        brushButtonDescribeService.brushButtonDescribe(objectApiNames, user)

        then: "验证方法调用"
        1 * describeLogicService.findObjects(user.getTenantId(), filteredDescribeApiNames) >> objects
        1 * buttonService.findButtonsByDescribeApiNamesAndType(user.getTenantId(), describeApiNames, ButtonType.COMMON.getId()) >> buttonMap
        1 * brushButtonDescribeServiceMock.handleButtonAndAction('AccountObj', [createButton()], user)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试刷新按钮描述正常场景，当对象在灰度列表中时的完整流程
     */
    def "brushButtonDescribeTestNormalWithGrayObject"() {
        given: "准备测试数据，对象在灰度列表中"
        def objectApiNames = ['AccountObj'] as Set
        def user = User.systemUser('74255')
        def filteredDescribeApiNames = ['AccountObj'] as Set
        def objects = ['AccountObj': createObjectDescribe()]
        def describeApiNames = ['AccountObj']
        def buttonMap = ['AccountObj': [createButton()]]

        when: "调用刷新按钮描述方法"
        brushButtonDescribeService.brushButtonDescribe(objectApiNames, user)

        then: "验证方法调用"
        1 * describeLogicService.findObjects(user.getTenantId(), filteredDescribeApiNames) >> objects
        1 * buttonService.findButtonsByDescribeApiNamesAndType(user.getTenantId(), describeApiNames, ButtonType.COMMON.getId()) >> buttonMap
        1 * brushButtonDescribeServiceMock.handleButtonAndAction('AccountObj', [createButton()], user)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试刷新按钮描述时describeApiNames为空的场景
     */
    def "brushButtonDescribeTestEmptyDescribeApiNames"() {
        given: "准备测试数据，非灰度租户"
        def objectApiNames = ['AccountObj'] as Set
        def user = User.systemUser('12345') // 非灰度租户

        when: "调用刷新按钮描述方法"
        brushButtonDescribeService.brushButtonDescribe(objectApiNames, user)

        then: "不调用后续方法"
        0 * buttonService.findButtonsByDescribeApiNamesAndType(_, _, _)
        0 * brushButtonDescribeServiceMock.handleButtonAndAction(_, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试刷新按钮描述时buttonMap为空的场景
     */
    def "brushButtonDescribeTestEmptyButtonMap"() {
        given: "准备测试数据"
        def objectApiNames = ['AccountObj'] as Set
        def user = User.systemUser('74255')
        def filteredDescribeApiNames = ['AccountObj'] as Set
        def objects = ['AccountObj': createObjectDescribe()]
        def describeApiNames = ['AccountObj']

        when: "调用刷新按钮描述方法"
        brushButtonDescribeService.brushButtonDescribe(objectApiNames, user)

        then: "验证方法调用"
        1 * describeLogicService.findObjects(user.getTenantId(), filteredDescribeApiNames) >> objects
        1 * buttonService.findButtonsByDescribeApiNamesAndType(user.getTenantId(), describeApiNames, ButtonType.COMMON.getId()) >> [:]
        0 * brushButtonDescribeServiceMock.handleButtonAndAction(_, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量刷新按钮描述正常场景
     */
    def "brushButtonDescribeTestBatchNormal"() {
        given: "准备测试数据"
        def objectApiNames = ['AccountObj'] as Set
        def tenantIds = ['74255', '78057'] as Set
        def filteredDescribeApiNames = ['AccountObj'] as Set
        def objects = ['AccountObj': createObjectDescribe()]
        def describeApiNames = ['AccountObj']
        def buttonMap = ['AccountObj': [createButton()]]

        when: "调用批量刷新按钮描述方法"
        brushButtonDescribeService.brushButtonDescribe(objectApiNames, tenantIds)

        then: "验证为每个租户调用刷新方法"
        2 * describeLogicService.findObjects(_, filteredDescribeApiNames) >> objects
        2 * buttonService.findButtonsByDescribeApiNamesAndType(_, describeApiNames, ButtonType.COMMON.getId()) >> buttonMap
        2 * brushButtonDescribeServiceMock.handleButtonAndAction('AccountObj', [createButton()], _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量刷新按钮描述时tenantIds为空的场景
     */
    def "brushButtonDescribeTestBatchEmptyTenantIds"() {
        given: "准备测试数据"
        def objectApiNames = ['AccountObj'] as Set
        def tenantIds = [] as Set

        when: "调用批量刷新按钮描述方法"
        brushButtonDescribeService.brushButtonDescribe(objectApiNames, tenantIds)

        then: "不调用任何方法"
        0 * describeLogicService.findObjectsByTenantId(_, _, _, _, _)
        0 * buttonService.findButtonsByDescribeApiNamesAndType(_, _, _)
        0 * brushButtonDescribeServiceMock.handleButtonAndAction(_, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理按钮和动作正常场景
     */
    def "handleButtonAndActionTestNormal"() {
        given: "准备测试数据"
        def objectApiName = 'AccountObj'
        def buttonList = [createButtonWithActions()]
        def user = User.systemUser('74255')
        def actionList = [createAction()]

        when: "调用处理按钮和动作方法"
        brushButtonDescribeService.handleButtonAndAction(objectApiName, buttonList, user)

        then: "验证按钮通过过滤并被处理"
        1 * udefButtonPGMapper.setTenantId(user.getTenantId()) >> udefButtonPGMapper
        1 * udefButtonPGMapper.countById(user.getTenantId(), _) >> 1
        1 * buttonService.deleteUdefButton(_, objectApiName, user.getTenantId())
        1 * buttonService.create(_)
        1 * actionService.findActionList(user, _, objectApiName) >> actionList
        // 先调用NOT_USED，再调用USED
        1 * udefFunctionService.findFunctionByApiName(user.getTenantId(), 'testFunctionApi', _) >> createFunction()
        1 * functionLogicService.clearUdefFunctionUsedInfo(user, _, _, _, "not_used")
        1 * functionLogicService.deleteRelation(user, SourceTypes.BUTTON, _, 'testFunctionApi')
        1 * udefFunctionService.findFunctionByApiName(user.getTenantId(), 'testFunctionApi', _) >> createFunction()
        1 * udefFunctionService.updateUdefFunctionStatus(user.getTenantId(), 'testFunctionApi', _, "used", _, _)
        1 * functionLogicService.saveRelation(user, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理按钮和动作时按钮列表为空的场景
     */
    def "handleButtonAndActionTestEmptyButtons"() {
        given: "准备测试数据"
        def objectApiName = 'AccountObj'
        def buttonList = []
        def user = User.systemUser('74255')

        when: "调用处理按钮和动作方法"
        brushButtonDescribeService.handleButtonAndAction(objectApiName, buttonList, user)

        then: "不调用任何处理方法"
        0 * udefButtonPGMapper.setTenantId(_)
        0 * buttonService.deleteUdefButton(_, _, _)
        0 * buttonService.create(_)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理按钮和动作时过滤不符合条件的按钮
     */
    def "handleButtonAndActionTestFilterButtons"() {
        given: "准备测试数据"
        def objectApiName = 'AccountObj'
        def buttonList = [createButtonWithWrongType(), createDeletedButton()]
        def user = User.systemUser('74255')

        when: "调用处理按钮和动作方法"
        brushButtonDescribeService.handleButtonAndAction(objectApiName, buttonList, user)

        then: "不调用任何处理方法"
        0 * udefButtonPGMapper.setTenantId(_)
        0 * buttonService.deleteUdefButton(_, _, _)
        0 * buttonService.create(_)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新和创建按钮正常场景
     */
    def "updateAndCreateTestNormal"() {
        given: "准备测试数据"
        def button = createButton()
        def objectApiName = 'AccountObj'

        when: "调用更新和创建方法"
        def result = brushButtonDescribeService.updateAndCreate(button, objectApiName)

        then: "验证方法调用"
        1 * udefButtonPGMapper.setTenantId(button.getTenantId()) >> udefButtonPGMapper
        1 * udefButtonPGMapper.countById(button.getTenantId(), button.getId()) >> 1
        1 * buttonService.deleteUdefButton(_, objectApiName, button.getTenantId())
        1 * buttonService.create(_)
        result != null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新和创建按钮时按钮不存在的场景
     */
    def "updateAndCreateTestButtonNotExists"() {
        given: "准备测试数据"
        def button = createButton()
        def objectApiName = 'AccountObj'

        when: "调用更新和创建方法"
        def result = brushButtonDescribeService.updateAndCreate(button, objectApiName)

        then: "验证方法调用"
        1 * udefButtonPGMapper.setTenantId(button.getTenantId()) >> udefButtonPGMapper
        1 * udefButtonPGMapper.countById(button.getTenantId(), button.getId()) >> 0
        0 * buttonService.deleteUdefButton(_, _, _)
        0 * buttonService.create(_)
        result == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新和创建按钮时发生异常的场景
     */
    def "updateAndCreateError"() {
        given: "准备测试数据"
        def button = createButton()
        def objectApiName = 'AccountObj'

        when: "调用更新和创建方法"
        brushButtonDescribeService.updateAndCreate(button, objectApiName)

        then: "验证异常处理"
        1 * udefButtonPGMapper.setTenantId(button.getTenantId()) >> udefButtonPGMapper
        1 * udefButtonPGMapper.countById(button.getTenantId(), button.getId()) >> 1
        1 * buttonService.deleteUdefButton(_, objectApiName, button.getTenantId()) >> { 
            throw new MetadataServiceException(SystemErrorCode.METADATA_ERROR, "Test exception") 
        }
        thrown(MetaDataBusinessException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更改函数状态为USED的场景
     */
    def "changeFunctionStatusTestUsed"() {
        given: "准备测试数据"
        def user = User.systemUser('74255')
        def button = createButton()
        def actions = [createAction()]
        def status = "used"
        def function = createFunction()

        when: "调用更改函数状态方法"
        brushButtonDescribeService.changeFunctionStatus(user, button, actions, status)

        then: "验证USED状态处理"
        1 * udefFunctionService.findFunctionByApiName(user.getTenantId(), 'testFunctionApi', button.getDescribeApiName()) >> function
        1 * udefFunctionService.updateUdefFunctionStatus(user.getTenantId(), 'testFunctionApi', button.getDescribeApiName(), status, function.getUsedInfo(), _)
        1 * functionLogicService.saveRelation(user, _)
        0 * functionLogicService.clearUdefFunctionUsedInfo(_, _, _, _, _)
        0 * functionLogicService.deleteRelation(_, _, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更改函数状态为NOT_USED的场景
     */
    def "changeFunctionStatusTestNotUsed"() {
        given: "准备测试数据"
        def user = User.systemUser('74255')
        def button = createButton()
        def actions = [createAction()]
        def status = "not_used"
        def function = createFunction()

        when: "调用更改函数状态方法"
        brushButtonDescribeService.changeFunctionStatus(user, button, actions, status)

        then: "验证NOT_USED状态处理"
        1 * udefFunctionService.findFunctionByApiName(user.getTenantId(), 'testFunctionApi', button.getDescribeApiName()) >> function
        1 * functionLogicService.clearUdefFunctionUsedInfo(user, function, button.getDescribeApiName(), button.getApiName(), status)
        1 * functionLogicService.deleteRelation(user, SourceTypes.BUTTON, button.getApiName(), 'testFunctionApi')
        0 * udefFunctionService.updateUdefFunctionStatus(_, _, _, _, _, _)
        0 * functionLogicService.saveRelation(_, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更改函数状态时函数不存在的异常场景
     */
    def "changeFunctionStatusErrorFunctionNotExists"() {
        given: "准备测试数据"
        def user = User.systemUser('74255')
        def button = createButton()
        def actions = [createAction()]
        def status = "used"

        when: "调用更改函数状态方法"
        brushButtonDescribeService.changeFunctionStatus(user, button, actions, status)

        then: "验证异常抛出"
        1 * udefFunctionService.findFunctionByApiName(user.getTenantId(), 'testFunctionApi', button.getDescribeApiName()) >> null
        thrown(FunctionException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更改函数状态时没有函数动作的场景
     */
    def "changeFunctionStatusTestNoFunctionAction"() {
        given: "准备测试数据"
        def user = User.systemUser('74255')
        def button = createButton()
        def actions = [createNonFunctionAction()]
        def status = "used"

        when: "调用更改函数状态方法"
        brushButtonDescribeService.changeFunctionStatus(user, button, actions, status)

        then: "不处理任何函数"
        0 * udefFunctionService.findFunctionByApiName(_, _, _)
        0 * udefFunctionService.updateUdefFunctionStatus(_, _, _, _, _, _)
        0 * functionLogicService.clearUdefFunctionUsedInfo(_, _, _, _, _)
        0 * functionLogicService.deleteRelation(_, _, _, _)
        0 * functionLogicService.saveRelation(_, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建引用数据方法
     */
    def "buildReferenceDataTest"() {
        given: "准备测试数据"
        def button = createButton()
        def functionApiName = 'testFunctionApi'

        when: "调用构建引用数据方法"
        def result = brushButtonDescribeService.buildReferenceData(button, functionApiName)

        then: "验证引用数据"
        result.getSourceType() == SourceTypes.BUTTON
        result.getSourceLabel() == button.getLabel()
        result.getSourceValue() == button.getApiName()
        result.getTargetType() == TargetTypes.FUNCTION
        result.getTargetValue() == functionApiName
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取描述API名称正常场景
     */
    def "getDescribeApiNamesTestNormal"() {
        given: "准备测试数据"
        def objectApiNames = ['AccountObj'] as Set
        def user = User.systemUser('74255')
        def objects = ['AccountObj': createObjectDescribe()]

        when: "调用获取描述API名称方法"
        def result = brushButtonDescribeService.getDescribeApiNames(objectApiNames, user)

        then: "验证方法调用"
        1 * describeLogicService.findObjects(user.getTenantId(), objectApiNames) >> objects
        result == ['AccountObj']
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取描述API名称时objectApiNames为空的场景
     */
    def "getDescribeApiNamesTestEmptyObjectApiNames"() {
        given: "准备测试数据"
        def objectApiNames = [] as Set
        def user = User.systemUser('74255')

        when: "调用获取描述API名称方法"
        def result = brushButtonDescribeService.getDescribeApiNames(objectApiNames, user)

        then: "验证方法调用"
        1 * describeLogicService.findObjectsByTenantId(user.getTenantId(), false, false, false, false) >> [createObjectDescribe()]
        result == ['AccountObj']
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取描述API名称时租户不在灰度列表的场景
     */
    def "getDescribeApiNamesTestNonGrayTenant"() {
        given: "准备测试数据"
        def objectApiNames = ['AccountObj'] as Set
        def user = User.systemUser('12345') // 非灰度租户

        when: "调用获取描述API名称方法"
        def result = brushButtonDescribeService.getDescribeApiNames(objectApiNames, user)

        then: "返回空列表"
        result == []
        0 * describeLogicService.findObjects(_, _)
    }

    private IUdefButton createButton() {
        def button = new UdefButton()
        button.setId('button_001')
        button.setApiName(DEFAULT_ADD_BUTTON_API_NAME)
        button.setTenantId('74255')
        button.setButtonType(ButtonType.COMMON.getId())
        button.setDeleted(false)
        button.setLabel('添加按钮')
        button.setDescribeApiName('AccountObj')
        button.setActions([])
        return button
    }

    private IUdefButton createButtonWithActions() {
        def button = createButton()
        button.setActions(['action_001'])
        return button
    }

    private IUdefButton createButtonWithWrongType() {
        def button = createButton()
        button.setButtonType('redirect')
        return button
    }

    private IUdefButton createDeletedButton() {
        def button = createButton()
        button.setDeleted(true)
        return button
    }

    private IUdefAction createAction() {
        def action = new UdefAction()
        action.setId('action_001')
        action.setActionType('custom_function')
        action.setActionParamter('{"func_api_name":"testFunctionApi","func_args":[]}')
        return action
    }

    private IUdefAction createNonFunctionAction() {
        def action = new UdefAction()
        action.setId('action_002')
        action.setActionParamter('{"action_type":"convert","target_obj":"TestObj"}')
        return action
    }

    private IUdefFunction createFunction() {
        def function = new UdefFunction()
        function.setApiName('testFunctionApi')
        function.setTenantId('74255')
        function.setBindingObjectApiName('AccountObj')
        function.setUsedInfo(['api_name': 'test_button', 'label': 'Test Button'])
        return function
    }

    private IObjectDescribe createObjectDescribe() {
        def describe = new ObjectDescribe()
        describe.setApiName('AccountObj')
        describe.setTenantId('74255')
        describe.setDisplayName('客户')
        return describe
    }
} 