package com.facishare.paas.appframework.common.mq

import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer
import com.fxiaoke.rocketmq.producer.DefaultTopicMessage
import org.apache.rocketmq.client.producer.SendResult
import org.apache.rocketmq.client.producer.SendStatus
import org.apache.rocketmq.common.message.Message
import org.apache.rocketmq.common.message.MessageQueue
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class AppDefaultRocketMQProducerTest extends Specification {

    AppDefaultRocketMQProducer producer
    AutoConfMQProducer mqProducer = Mock(AutoConfMQProducer)
    SendResult successResult
    
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def setup() {
        producer = new AppDefaultRocketMQProducer("config", "sectionName")
        // 初始化一个成功的SendResult用于模拟返回
        successResult = new SendResult()
        successResult.setSendStatus(SendStatus.SEND_OK)
        
        // 设置内部mqProducer实例
        Whitebox.setInternalState(producer, "mqProducer", mqProducer)
        // 设置默认topic
        Whitebox.setInternalState(producer, "topic", "defaultTopic")
        
        // 设置mqProducer的默认行为
        mqProducer.getDefaultTopic() >> "defaultTopic"
    }
    def "测试带section的构造函数"() {
        given:
        producer = new AppDefaultRocketMQProducer("testConfig", "testSection")
        
        expect:
        producer.config == "testConfig"
        producer.sectionName == "testSection"
    }
    
    def "测试关闭方法"() {
        when:
        producer.close()
        
        then:
        1 * mqProducer.close()
    }
    
    def "测试基本的sendMessage方法"() {
        given:
        Message message = new Message("testTopic", "testTag", "testKey", 0, "testBody".bytes, true)
        
        when:
        def result = producer.sendMessage(message)
        
        then:
        1 * mqProducer.send(message) >> successResult
        result.sendStatus == SendStatus.SEND_OK
    }
    
    def "测试发送DefaultTopicMessage"() {
        given:
        byte[] messageData = "testData".bytes
        
        when:
        def result = producer.sendMessage(messageData)
        
        then:
        1 * mqProducer.send(_ as DefaultTopicMessage) >> { DefaultTopicMessage msg ->
            assert msg.body == messageData
            return successResult
        }
        result.sendStatus == SendStatus.SEND_OK
    }
    
    def "测试指定flag的sendMessage方法"() {
        given:
        byte[] messageData = "testData".bytes
        int flag = 123
        
        when:
        def result = producer.sendMessage(flag, messageData)
        
        then:
        1 * mqProducer.send(_ as Message) >> { Message msg ->
            assert msg.topic == "defaultTopic"
            assert msg.flag == flag
            assert msg.body == messageData
            return successResult
        }
        result.sendStatus == SendStatus.SEND_OK
    }
    
    def "测试指定tags和flag的sendMessage方法"() {
        given:
        byte[] messageData = "testData".bytes
        String tags = "testTag"
        int flag = 456
        
        when:
        def result = producer.sendMessage(tags, flag, messageData)
        
        then:
        1 * mqProducer.send(_ as Message) >> { Message msg ->
            assert msg.topic == "defaultTopic"
            assert msg.tags == tags
            assert msg.flag == flag
            assert msg.body == messageData
            return successResult
        }
        result.sendStatus == SendStatus.SEND_OK
    }
    
    def "测试指定topic和tags的sendMessage方法"() {
        given:
        byte[] messageData = "testData".bytes
        String topic = "customTopic"
        String tags = "customTag"
        
        when:
        def result = producer.sendMessage(topic, tags, messageData)
        
        then:
        1 * mqProducer.send(_ as Message) >> { Message msg ->
            assert msg.topic == topic
            assert msg.tags == tags
            assert msg.flag == 0
            assert msg.body == messageData
            return successResult
        }
        result.sendStatus == SendStatus.SEND_OK
    }
    
    def "测试完整参数的sendMessage方法"() {
        given:
        byte[] messageData = "testData".bytes
        String topic = "customTopic"
        String tags = "customTag"
        int flag = 789
        
        when:
        def result = producer.sendMessage(topic, tags, flag, messageData)
        
        then:
        1 * mqProducer.send(_ as Message) >> { Message msg ->
            assert msg.topic == topic
            assert msg.tags == tags
            assert msg.flag == flag
            assert msg.body == messageData
            return successResult
        }
        result.sendStatus == SendStatus.SEND_OK
    }
    
    def "测试带选择器哈希的sendMessage方法"() {
        given:
        byte[] messageData = "testData".bytes
        int selectorHash = 123
        List<MessageQueue> messageQueues = [
            new MessageQueue("topic", "broker", 0),
            new MessageQueue("topic", "broker", 1),
            new MessageQueue("topic", "broker", 2)
        ]
        
        when:
        def result = producer.sendMessage(messageData, selectorHash)
        
        then:
        1 * mqProducer.send(_ as Message, _, selectorHash) >> { Message msg, def selector, def arg ->
            // 验证消息数据
            assert msg.body == messageData
            assert msg.topic == "defaultTopic"
            
            // 验证选择器逻辑
            def selectedQueue = selector.select(messageQueues, msg, arg)
            assert selectedQueue == messageQueues[arg % messageQueues.size()]
            
            return successResult
        }
        result.sendStatus == SendStatus.SEND_OK
    }
    
    def "测试带选择器哈希的sendMessage方法 - 异常情况"() {
        given:
        byte[] messageData = "testData".bytes
        int selectorHash = 123
        
        when:
        def result = producer.sendMessage(messageData, selectorHash)
        
        then:
        1 * mqProducer.send(_ as Message, _, selectorHash) >> { throw new RuntimeException("发送失败") }
        result == null
    }
    
    def "测试带IP组的sendMessage方法"() {
        given:
        byte[] messageData = "testData".bytes
        String topic = "customTopic"
        String tags = "customTag"
        String ipGroup = "***********"
        List<MessageQueue> messageQueues = [
            new MessageQueue("topic", "broker", 0),
            new MessageQueue("topic", "broker", 1),
            new MessageQueue("topic", "broker", 2)
        ]
        
        when:
        producer.sendMessage(topic, tags, messageData, ipGroup)
        
        then:
        1 * mqProducer.send(_ as Message, _, _) >> { Message msg, def selector, def arg ->
            // 验证消息数据
            assert msg.body == messageData
            assert msg.topic == topic
            assert msg.tags == tags
            
            // 验证选择器逻辑
            def selectedQueue = selector.select(messageQueues, msg, arg)
            int hashCode = ipGroup.replace(".", "").hashCode()
            int expectedIndex = Math.abs(hashCode % messageQueues.size())
            assert selectedQueue == messageQueues[expectedIndex]
            
            return successResult
        }
    }
    
    def "测试带IP组的sendMessage方法 - 异常情况"() {
        given:
        byte[] messageData = "testData".bytes
        String topic = "customTopic"
        String tags = "customTag"
        String ipGroup = "***********"
        
        when:
        producer.sendMessage(topic, tags, messageData, ipGroup)
        
        then:
        1 * mqProducer.send(_ as Message, _, _) >> { throw new RuntimeException("发送失败") }
        notThrown(Exception) // 确保异常被捕获处理
    }
    
    def "测试不同边界值的消息发送"() {
        given:
        Message message = new Message(topic, tags, key, flag, body, waitStoreMsgOK)
        
        when:
        def result = producer.sendMessage(message)
        
        then:
        1 * mqProducer.send(message) >> successResult
        result.sendStatus == SendStatus.SEND_OK
        
        where:
        topic           | tags           | key           | flag  | body                 | waitStoreMsgOK
        "defaultTopic"  | ""             | ""            | 0     | "normal".bytes      | true
        "defaultTopic"  | null           | null          | 0     | "nullValues".bytes  | true
        "defaultTopic"  | "特殊标签"      | "特殊键值"     | 0     | "中文内容".bytes     | true
        "defaultTopic"  | "longTag"*50   | "longKey"*50  | 0     | new byte[1024*10]   | true
        "defaultTopic"  | "tag"          | "key"         | -1    | "negativeFlag".bytes| true
        "defaultTopic"  | "tag"          | "key"         | 0     | new byte[0]         | true
        "defaultTopic"  | "tag"          | "key"         | Integer.MAX_VALUE | "maxFlag".bytes | true
    }
} 