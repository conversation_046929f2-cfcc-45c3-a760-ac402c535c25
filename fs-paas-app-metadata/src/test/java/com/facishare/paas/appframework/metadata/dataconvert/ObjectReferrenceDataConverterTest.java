package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试ObjectReferrenceDataConverter类的对象引用字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class ObjectReferrenceDataConverterTest {

  @Mock
  private IObjectDataProxyService dataService;

  @Mock
  private SessionContext sessionContext;

  @Mock
  private ObjectReferenceFieldDescribe mockFieldDescribe;

  @Mock
  private IObjectData mockReferencedObject;

  @Mock
  private QueryResult<IObjectData> mockQueryResult;

  private ObjectReferrenceDataConverter converter;
  private IFieldDescribe fieldDescribe;
  private IObjectData objectData;

  @BeforeEach
  void setUp() {
    converter = new ObjectReferrenceDataConverter();
    fieldDescribe = createObjectReferenceFieldDescribe("reference_field", "target_object");
    objectData = createObjectData("reference_field", "ref_id_123");
    
    // 设置转换器的属性
    converter.setFieldDescribe(fieldDescribe);
    converter.setObjectData(objectData);
    converter.setDataService(dataService);
    
    // 设置SessionContext的默认行为
    lenient().when(sessionContext.getUserIdString()).thenReturn("user123");
    lenient().when(sessionContext.getEId()).thenReturn(123456L);
    
    // 设置mock字段描述的行为
    lenient().when(mockFieldDescribe.getApiName()).thenReturn("reference_field");
    lenient().when(mockFieldDescribe.getTargetApiName()).thenReturn("target_object");
    
    // 设置mock引用对象的行为
    lenient().when(mockReferencedObject.getName()).thenReturn("Referenced Object Name");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法转换CRM对象引用
   */
  @Test
  @DisplayName("测试convertFieldData - CRM对象引用转换")
  void testConvertFieldData_CRMObjectReference() throws Exception {
    // 准备测试数据 - CRM对象（SFA标准对象）
    objectData = createObjectData("reference_field", "crm_obj_123");
    converter.setFieldDescribe(mockFieldDescribe);
    converter.setObjectData(objectData);

    try (MockedStatic<ObjectAPINameMapping> mockedMapping = mockStatic(ObjectAPINameMapping.class)) {
      mockedMapping.when(() -> ObjectAPINameMapping.isSFANotStandardObject("target_object"))
          .thenReturn(true);

      when(dataService.findById(eq("crm_obj_123"), eq("123456"), any(IActionContext.class), eq("target_object")))
          .thenReturn(mockReferencedObject);

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果
      assertEquals("Referenced Object Name", result);
      verify(dataService).findById(eq("crm_obj_123"), eq("123456"), any(IActionContext.class), eq("target_object"));
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法转换定义对象引用
   */
  @Test
  @DisplayName("测试convertFieldData - 定义对象引用转换")
  void testConvertFieldData_DefObjectReference() throws Exception {
    // 准备测试数据 - 定义对象（非SFA标准对象）
    objectData = createObjectData("reference_field", "def_obj_123");
    converter.setFieldDescribe(mockFieldDescribe);
    converter.setObjectData(objectData);

    try (MockedStatic<ObjectAPINameMapping> mockedMapping = mockStatic(ObjectAPINameMapping.class)) {
      mockedMapping.when(() -> ObjectAPINameMapping.isSFANotStandardObject("target_object"))
          .thenReturn(false);

      when(mockQueryResult.getData()).thenReturn(Arrays.asList(mockReferencedObject));
      when(dataService.findBySearchQuery(eq("123456"), eq("target_object"), any(SearchTemplateQuery.class), any(IActionContext.class)))
          .thenReturn(mockQueryResult);

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果
      assertEquals("Referenced Object Name", result);
      verify(dataService).findBySearchQuery(eq("123456"), eq("target_object"), any(SearchTemplateQuery.class), any(IActionContext.class));
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理null值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null值")
  void testConvertFieldData_HandlesNullValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("reference_field", null);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
    verify(dataService, never()).findById(anyString(), anyString(), any(IActionContext.class), anyString());
    verify(dataService, never()).findBySearchQuery(anyString(), anyString(), any(SearchTemplateQuery.class), any(IActionContext.class));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理空字符串
   */
  @Test
  @DisplayName("测试convertFieldData - 处理空字符串")
  void testConvertFieldData_HandlesEmptyString() throws Exception {
    // 准备测试数据
    objectData = createObjectData("reference_field", "");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
    verify(dataService, never()).findById(anyString(), anyString(), any(IActionContext.class), anyString());
    verify(dataService, never()).findBySearchQuery(anyString(), anyString(), any(SearchTemplateQuery.class), any(IActionContext.class));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理定义对象查询无结果
   */
  @Test
  @DisplayName("测试convertFieldData - 定义对象查询无结果")
  void testConvertFieldData_DefObjectNoResult() throws Exception {
    // 准备测试数据
    objectData = createObjectData("reference_field", "def_obj_123");
    converter.setFieldDescribe(mockFieldDescribe);
    converter.setObjectData(objectData);

    try (MockedStatic<ObjectAPINameMapping> mockedMapping = mockStatic(ObjectAPINameMapping.class)) {
      mockedMapping.when(() -> ObjectAPINameMapping.isSFANotStandardObject("target_object"))
          .thenReturn(false);

      when(mockQueryResult.getData()).thenReturn(Collections.emptyList());
      when(dataService.findBySearchQuery(eq("123456"), eq("target_object"), any(SearchTemplateQuery.class), any(IActionContext.class)))
          .thenReturn(mockQueryResult);

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果
      assertNull(result);
    }
  }

  /**
   * 提供不同类型的引用值测试数据
   */
  private static Stream<Arguments> provideReferenceValues() {
    return Stream.of(
        Arguments.of("valid_ref_id", "Referenced Object Name"),
        Arguments.of(null, ""),
        Arguments.of("", "")
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理不同类型的引用值
   */
  @ParameterizedTest
  @MethodSource("provideReferenceValues")
  @DisplayName("测试convertFieldData - 不同类型的引用值")
  void testConvertFieldData_DifferentReferenceValues(String referenceValue, String expectedResult) throws Exception {
    // 准备测试数据
    objectData = createObjectData("reference_field", referenceValue);
    converter.setFieldDescribe(mockFieldDescribe);
    converter.setObjectData(objectData);

    if (referenceValue != null && !referenceValue.isEmpty()) {
      try (MockedStatic<ObjectAPINameMapping> mockedMapping = mockStatic(ObjectAPINameMapping.class)) {
        mockedMapping.when(() -> ObjectAPINameMapping.isSFANotStandardObject("target_object"))
            .thenReturn(true);

        when(dataService.findById(eq(referenceValue), eq("123456"), any(IActionContext.class), eq("target_object")))
            .thenReturn(mockReferencedObject);

        // 执行被测试方法
        String result = converter.convertFieldData(sessionContext);

        // 验证结果
        assertEquals(expectedResult, result);
      }
    } else {
      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果
      assertEquals(expectedResult, result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理字段不存在的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 字段不存在")
  void testConvertFieldData_FieldNotExists() throws Exception {
    // 准备测试数据 - 创建不包含目标字段的数据
    objectData = createObjectData("other_field", "ref_id_123");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 字段不存在时应该返回空字符串
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理数据服务异常
   */
  @Test
  @DisplayName("测试convertFieldData - 数据服务异常")
  void testConvertFieldData_DataServiceException() throws Exception {
    // 准备测试数据
    objectData = createObjectData("reference_field", "ref_id_123");
    converter.setFieldDescribe(mockFieldDescribe);
    converter.setObjectData(objectData);

    try (MockedStatic<ObjectAPINameMapping> mockedMapping = mockStatic(ObjectAPINameMapping.class)) {
      mockedMapping.when(() -> ObjectAPINameMapping.isSFANotStandardObject("target_object"))
          .thenReturn(true);

      when(dataService.findById(eq("ref_id_123"), eq("123456"), any(IActionContext.class), eq("target_object")))
          .thenThrow(new RuntimeException("Data service error"));

      // 执行被测试方法 - 应该抛出异常
      assertThrows(RuntimeException.class, () -> {
        converter.convertFieldData(sessionContext);
      });
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ActionContext的正确设置
   */
  @Test
  @DisplayName("测试ActionContext设置")
  void testActionContextSetup() throws Exception {
    // 准备测试数据
    objectData = createObjectData("reference_field", "ref_id_123");
    converter.setFieldDescribe(mockFieldDescribe);
    converter.setObjectData(objectData);

    try (MockedStatic<ObjectAPINameMapping> mockedMapping = mockStatic(ObjectAPINameMapping.class)) {
      mockedMapping.when(() -> ObjectAPINameMapping.isSFANotStandardObject("target_object"))
          .thenReturn(true);

      when(dataService.findById(eq("ref_id_123"), eq("123456"), any(IActionContext.class), eq("target_object")))
          .thenReturn(mockReferencedObject);

      // 执行被测试方法
      converter.convertFieldData(sessionContext);

      // 验证ActionContext的设置
      verify(dataService).findById(eq("ref_id_123"), eq("123456"), argThat(actionContext -> {
        return "user123".equals(actionContext.getUserId()) && 
               "123456".equals(actionContext.getEnterpriseId());
      }), eq("target_object"));
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试转换器的基本属性设置
   */
  @Test
  @DisplayName("测试转换器属性设置")
  void testConverterProperties() {
    // 验证属性设置
    assertEquals(fieldDescribe, converter.getFieldDescribe());
    assertEquals(objectData, converter.getObjectData());
    assertEquals(dataService, converter.getDataService());
  }

  /**
   * 创建测试用的对象引用字段描述
   */
  private IFieldDescribe createObjectReferenceFieldDescribe(String apiName, String targetApiName) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试对象引用字段");
    fieldMap.put("type", IFieldType.OBJECT_REFERENCE);
    fieldMap.put("target_api_name", targetApiName);
    return FieldDescribeFactory.newInstance(fieldMap);
  }

  /**
   * 创建测试用的对象数据
   */
  private IObjectData createObjectData(String fieldName, Object value) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldName, value);
    return new ObjectData(dataMap);
  }
}
