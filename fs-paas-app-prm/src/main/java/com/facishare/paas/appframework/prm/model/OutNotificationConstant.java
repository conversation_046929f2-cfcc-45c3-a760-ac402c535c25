package com.facishare.paas.appframework.prm.model;

/**
 * <AUTHOR>
 * @time 2023-05-10 19:34
 * @Description
 */
public interface OutNotificationConstant {

    String WX_APP_ID = "wx_app_id";
    String TEMPLATE_ID = "template_id";
    String WECHAT_TEMPLATE_ID = "wechatTemplateId";
    String FORWARD_TYPE = "forwardType";
    String INNER_NO_USE_BODY_FORM = "innerNoUseBodyForm";
    String TARGET_WX_APP_ID = "targetWxAppId";
    String ACTION_MODULE = "action_module";
    String OPEN_STATUS = "open_status";
    String WXTemplateObj = "WXTemplateObj";
    String BUSINESS_NAME = "业务名称"; // ignoreI18n
    String CURRENT_STAGE = "当前阶段"; // ignoreI18n

    /**
     * 当前阶段
     */
    String SFA_PRM_OUT_NOTIFICATION_STAGE_LABEL = "sfa.prm.out.notification.stage.label";

    /**
     * 业务名称
     */
    String SFA_PRM_OUT_NOTIFICATION_BIZ_NAME_LABEL = "sfa.prm.out.notification.biz.name.label";


}
