package com.facishare.paas.appframework.metadata.dto.sfa;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

public interface MoveOrReturn {

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Arg extends ObjectPoolCommon.Arg{
        @JSONField(name = "BackReason")
        String backReason;
        @JSONField(name = "OperationType")
        Integer operationType;
    }
}
