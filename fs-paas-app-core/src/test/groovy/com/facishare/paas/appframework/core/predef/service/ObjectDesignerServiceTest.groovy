package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.config.OptionalFeaturesService
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO
import spock.lang.Specification

class ObjectDesignerServiceTest extends Specification {

    def setup() {
    }


    def "test isSkipOptionalFeaturesProcess"() {
        given:
        ObjectDesignerService objectDesignerService = new ObjectDesignerService()
        ObjectDescribeDocument objectDescribeDocument = new ObjectDescribeDocument()
        OptionalFeaturesSwitchDTO optionalFeaturesSwitchDTO = new OptionalFeaturesSwitchDTO()
        objectDescribeDocument.put(OptionalFeaturesService.RELATED_TEAM_SWITCH, null)
        objectDescribeDocument.put(OptionalFeaturesService.GLOBAL_SEARCH_SWITCH, null)
        objectDescribeDocument.put(OptionalFeaturesService.FOLLOW_UP_DYNAMIC_SWITCH, null)
        objectDescribeDocument.put(OptionalFeaturesService.MODIFY_RECORD_SWITCH, null)
        objectDescribeDocument.put(OptionalFeaturesService.MULTI_FIELD_SORT, null)
        when:
        objectDesignerService.isSkipOptionalFeaturesProcess(objectDescribeDocument, optionalFeaturesSwitchDTO)
        then:
        noExceptionThrown()
    }


//    def initCondition() {
//        User user = User.builder().tenantId("74255").userId("1031").build()
//        requestContext = RequestContext.builder().tenantId("74255").user(new Optional<User>(user)).build();
//        serviceContext = new ServiceContext(requestContext, "", "")
//        configService.findTenantConfig(_, _) >> "0"
//        objectDesignerService.configService = configService
//        objectDesignerService.logService = logService
//        objectDesignerService.describeLogicService = describeLogicService
//    }
//
//    def "test findModifyDataPermissionTime"() {
//        given:
//        User user = User.builder().tenantId(tenantId).build()
//        objectDesignerService.configService = configService
//        def enable = objectDesignerService.whetherHavePermissionToModify(user, "object_api")
//        when:
//        Objects.equals(enable, result)
//        then:
//        noExceptionThrown()
//        where:
//        tenantId | result
//        ""       | null
//    }
//
//    def "test batchSaveRolePermissions"() {
//        given:
//        initCondition()
//        BatchSavePermissionList.Arg arg = BatchSavePermissionList.Arg.builder().describeApiName(describeApiName).permissionList(permissionList).build();
//        describeLogicService.findObject(_, _) >> new ObjectDescribe()
//        describeLogicService.findObjectsIncludeMultiField(_, _) >> getObjectDescribeMap()
//        def res = objectDesignerService.batchSaveRolePermissions(arg, serviceContext)
//        when:
//        res.success == result
//        then:
//        noExceptionThrown()
//        where:
//        describeApiName | permissionList      | result
//        "SalesOrderObj" | []                  | false
//        ""              | getPermissionList() | false
//        "SalesOrderObj" | getPermissionList() | false
//    }
//
//    def "test findTenantConfig"() {
//        given:
//        configService.findTenantConfig(_, _) >> false
//        def res = configService.findTenantConfig(null, null)
//        when:
//        res == result
//        then:
//        noExceptionThrown()
//        where:
//        describeApiName | result
//        "SalesOrderObj" | false
//    }
//
//    def getPermissionList() {
//        SavePermissionList.RolePermission rolePermission = SavePermissionList.RolePermission.builder().objectDisplayName("退货单").objectApiName("ReturnedGoodsInvoiceObj")
//                .enable(true).fieldAPiName("order_id").build()
//        SavePermissionList.RolePermission rolePermission1 = SavePermissionList.RolePermission.builder().objectDisplayName("自定义").objectApiName("UDObj")
//                .enable(false).fieldAPiName("ud_id").build()
//        BatchSavePermissionList.Permission permission = BatchSavePermissionList.Permission.builder().roleType("4").rolePermissionList(Lists.newArrayList(rolePermission, rolePermission1)).build()
//        List<BatchSavePermissionList.Permission> permissionList = Lists.newArrayList(permission)
//        permissionList
//    }
//
//    def getObjectDescribeMap() {
//        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
//        IObjectDescribe describe = new ObjectDescribe()
//        describe.setApiName("ReturnedGoodsInvoiceObj")
//        describe.setDisplayName("退货单")
//
//        IFieldDescribe fieldDescribe = new ObjectReferenceFieldDescribe();
//        fieldDescribe.setApiName("order_id")
//        describe.setFieldDescribes(Lists.newArrayList(fieldDescribe))
//
//        IObjectDescribe describe2 = new ObjectDescribe()
//        describe2.setApiName("UDObj")
//        describe2.setDisplayName("自定义")
//
//        IFieldDescribe fieldDescribe2 = new ObjectReferenceFieldDescribe();
//        fieldDescribe2.setApiName("ud_id")
//        describe2.setFieldDescribes(Lists.newArrayList(fieldDescribe2))
//
//
//        describeMap.put("ReturnedGoodsInvoiceObj", describe)
//        describeMap.put("UDObj", describe2)
//
//        describeMap
//    }
//
//    def "test findRolesPermissionList"() {
//        given:
//        initCondition()
//        FindRolesPermissionList.Arg arg = FindRolesPermissionList.Arg.builder()
//                .describeApiName("AccountObj")
//                .roleType("1").includeRoles(true)
//                .build()
//        when:
//        objectDesignerService.findRolesPermissionList(arg, serviceContext)
//        then:
//        1 == 1
//    }
//
//    def "test findCustomFieldDescribe"() {
//        given:
//        initCondition()
//        FindCustomFieldDescribe.Arg arg = FindCustomFieldDescribe.Arg.newInstance('object_KmNwa__c', 'field_llmpd__c')
//        when:
//        objectDesignerService.findCustomFieldDescribe(arg, serviceContext)
//        then:
//        1 == 1
//    }
//
//
//    def "test updateCustomFieldDescribe"() {
//        User user = User.builder().tenantId("74255").userId("1031").build()
//        requestContext = RequestContext.builder().tenantId("74255").user(new Optional<User>(user)).build();
//        serviceContext = new ServiceContext(requestContext, "", "")
//        given:
//        def describeAPIName = "object_AP2Z9__c"
//        def group_fields = "[{\"type\":\"country\",\"define_type\":\"custom\",\"api_name\":\"field_01wYT__c\",\"label\":\"国家\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"resource_bundle_key\":null,\"status\":\"new\",\"describe_api_name\":\"object_AP2Z9__c\",\"description\":\"\",\"used_in\":\"component\",\"options\":[],\"is_single\":false,\"index_name\":\"s_22\",\"create_time\":1614737036774,\"is_encrypted\":false,\"default_value\":\"\",\"is_abstract\":null,\"field_num\":24,\"inherit_type\":null,\"_id\":\"603eee8c2caac90001473d8e\",\"is_index_field\":false},{\"type\":\"location\",\"define_type\":\"custom\",\"api_name\":\"field_zuH1I__c\",\"label\":\"定位\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"resource_bundle_key\":null,\"status\":\"new\",\"is_geo_index\":false,\"describe_api_name\":\"object_AP2Z9__c\",\"create_time\":1614737036774,\"is_encrypted\":false,\"description\":\"\",\"is_abstract\":null,\"field_num\":29,\"inherit_type\":null,\"used_in\":\"component\",\"_id\":\"603eee8c2caac90001473d93\",\"is_index_field\":false,\"is_single\":false,\"index_name\":\"t_7\"},{\"type\":\"town\",\"define_type\":\"custom\",\"api_name\":\"field_iVu8h__c\",\"label\":\"乡镇\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"resource_bundle_key\":null,\"status\":\"new\",\"cascade_parent_api_name\":\"field_qN5f3__c\",\"describe_api_name\":\"object_AP2Z9__c\",\"description\":\"\",\"used_in\":\"component\",\"options\":[],\"is_single\":false,\"index_name\":\"s_23\",\"create_time\":1614737073713,\"is_encrypted\":false,\"is_abstract\":null,\"field_num\":30,\"inherit_type\":null,\"_id\":\"603eeeb12caac90001473d9c\",\"is_index_field\":false},{\"type\":\"text\",\"define_type\":\"custom\",\"api_name\":\"field_srYfa__c\",\"label\":\"详细地址\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"resource_bundle_key\":null,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"max_length\":100,\"input_mode\":\"\",\"describe_api_name\":\"object_AP2Z9__c\",\"pattern\":\"\",\"description\":\"\",\"used_in\":\"component\",\"is_single\":false,\"index_name\":\"t_8\",\"create_time\":1614737036774,\"is_encrypted\":false,\"is_abstract\":null,\"field_num\":28,\"inherit_type\":null,\"_id\":\"603eee8c2caac90001473d92\",\"is_index_field\":false},{\"type\":\"city\",\"define_type\":\"custom\",\"api_name\":\"field_Lez6C__c\",\"label\":\"市\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"resource_bundle_key\":null,\"status\":\"new\",\"cascade_parent_api_name\":\"field_f27xb__c\",\"describe_api_name\":\"object_AP2Z9__c\",\"description\":\"\",\"used_in\":\"component\",\"options\":[],\"is_single\":false,\"index_name\":\"s_20\",\"create_time\":1614737036774,\"is_encrypted\":false,\"default_value\":\"\",\"is_abstract\":null,\"field_num\":26,\"inherit_type\":null,\"_id\":\"603eee8c2caac90001473d90\",\"is_index_field\":false},{\"type\":\"province\",\"define_type\":\"custom\",\"api_name\":\"field_f27xb__c\",\"label\":\"省\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"resource_bundle_key\":null,\"status\":\"new\",\"cascade_parent_api_name\":\"field_01wYT__c\",\"describe_api_name\":\"object_AP2Z9__c\",\"description\":\"\",\"used_in\":\"component\",\"options\":[],\"is_single\":false,\"index_name\":\"s_21\",\"create_time\":1614737036774,\"is_encrypted\":false,\"default_value\":\"\",\"is_abstract\":null,\"field_num\":25,\"inherit_type\":null,\"_id\":\"603eee8c2caac90001473d8f\",\"is_index_field\":false},{\"type\":\"district\",\"define_type\":\"custom\",\"api_name\":\"field_qN5f3__c\",\"label\":\"区\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"resource_bundle_key\":null,\"status\":\"new\",\"cascade_parent_api_name\":\"field_Lez6C__c\",\"describe_api_name\":\"object_AP2Z9__c\",\"description\":\"\",\"used_in\":\"component\",\"options\":[],\"is_single\":false,\"index_name\":\"s_19\",\"create_time\":1614737036774,\"is_encrypted\":false,\"is_abstract\":null,\"field_num\":27,\"inherit_type\":null,\"_id\":\"603eee8c2caac90001473d91\",\"is_index_field\":false}]"
//        def field_describe = "{\"type\":\"group\",\"define_type\":\"custom\",\"api_name\":\"field_uLmrC__c\",\"label\":\"地区定位\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":false,\"resource_bundle_key\":null,\"status\":\"new\",\"group_type\":\"area\",\"is_support_town\":true,\"fields\":{\"area_country\":\"field_01wYT__c\",\"area_location\":\"field_zuH1I__c\",\"area_town\":\"field_iVu8h__c\",\"area_detail_address\":\"field_srYfa__c\",\"area_city\":\"field_Lez6C__c\",\"area_province\":\"field_f27xb__c\",\"area_district\":\"field_qN5f3__c\"},\"describe_api_name\":\"object_AP2Z9__c\",\"area_town\":null,\"description\":\"\",\"area_country\":null,\"is_single\":false,\"index_name\":\"s_18\",\"area_location\":null,\"area_detail_address\":null,\"create_time\":1614737036529,\"area_city\":null,\"is_encrypted\":false,\"area_district\":null,\"is_abstract\":null,\"field_num\":null,\"inherit_type\":null,\"area_province\":null,\"_id\":\"603eee8c2caac90001473d94\",\"is_index_field\":false}"
//        def layout_list = "[{\"label\":\"默认布局\",\"api_name\":\"layout_BCd17__c\",\"is_readonly\":false,\"is_required\":false,\"is_show\":true,\"layout_type\":\"detail\"}]"
//        def arg = new UpdateCustomFieldDescribe.Arg()
//        arg.setDescribeAPIName(describeAPIName)
//        arg.setField_describe(field_describe)
//        arg.setGroup_fields(group_fields)
//        arg.setLayout_list(layout_list)
//        when:
//        objectDesignerService.updateCustomFieldDescribe(arg, serviceContext)
//        then:
//        1 == 1
//    }
}
