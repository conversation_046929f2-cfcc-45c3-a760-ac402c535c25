package com.facishare.paas.appframework.privilege.dto;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * Created by zhouwr on 2017/10/13
 */
public interface GetFieldsPermission {
    @Data
    @Builder
    class Arg {
        private AuthContext authContext;
        private String entityId;
    }

    @Data
    class Result {
        private int errCode;
        private String errMessage;
        private Map<String, Integer> result;
        private boolean success;
    }
}
