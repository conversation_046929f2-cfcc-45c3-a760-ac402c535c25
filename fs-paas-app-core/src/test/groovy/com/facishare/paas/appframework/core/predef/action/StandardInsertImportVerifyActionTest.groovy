package com.facishare.paas.appframework.core.predef.action

import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe
import com.fxiaoke.functions.utils.Lists
import com.google.common.collect.Maps
import spock.lang.Specification

class StandardInsertImportVerifyActionTest extends Specification {


    def objectApiName = "object_123__c"

    def actionCode = "UnionInsertImportData"

    def tenantId = "74255"

    def userId = "1000"

    def outTenantId = "200074255"

    def outUserId = "100018916"

    def user = User.builder().tenantId(tenantId).userId(userId).outTenantId(outTenantId).outUserId(outUserId).build()

    def requestContext = RequestContext.builder().tenantId(tenantId).user(user).build()

    def actionContext = new ActionContext(requestContext, objectApiName, actionCode)

    ServiceFacade serviceFacade
    InfraServiceFacade infraServiceFacade
    IObjectDescribe objectDescribe
    SpringBeanHolder springBeanHolder

    def setup() {
        serviceFacade = Mock()
        serviceFacade = Mock()
        infraServiceFacade = Mock()
        objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        objectDescribe.setIsActive(true)
        springBeanHolder = Mock(SpringBeanHolder)
        infraServiceFacade.getSpringBeanHolder() >> springBeanHolder
    }

    def "test getFuncPrivilegeCodes"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        StandardInsertImportVerifyAction action = new StandardInsertImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade
        )
        when:
        action.getFuncPrivilegeCodes()
        then:
        noExceptionThrown()
    }

    def "test getDataPrivilegeIds"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        objectDescribe.setDisplayName("unit")
        BaseImportAction.Arg arg = new BaseImportAction.Arg()
        StandardInsertImportVerifyAction action = new StandardInsertImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade
        )
        when:
        action.getDataPrivilegeIds(arg)
        then:
        noExceptionThrown()
    }

    def "test recordLog"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        objectDescribe.setDisplayName("unit")
        StandardInsertImportVerifyAction action = new StandardInsertImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade
        )
        when:
        action.recordLog()
        then:
        noExceptionThrown()
    }

    def "test getValidImportFields"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        StandardInsertImportVerifyAction action = new StandardInsertImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                serviceFacade: serviceFacade
        )
        when:
        action.getValidImportFields()
        then:
        noExceptionThrown()
    }

    def "test validateFieldsByID"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        String fieldApiName = "field_text__c"
        fieldDescribe.setApiName(fieldApiName)
        List<IFieldDescribe> validFieldList = Lists.newArrayList(fieldDescribe)

        Map<String, Object> data = Maps.newHashMap()
        data.put(fieldApiName, "test")

        StandardInsertImportVerifyAction action = new StandardInsertImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade
        )
        when:
        action.validateFieldsByID(validFieldList, data.entrySet())
        then:
        noExceptionThrown()
    }

    def "test validateFieldsByName"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        String fieldApiName = "field_text__c"
        fieldDescribe.setApiName(fieldApiName)
        List<IFieldDescribe> validFieldList = Lists.newArrayList(fieldDescribe)

        Map<String, Object> data = Maps.newHashMap()
        data.put(fieldApiName, "test")

        StandardInsertImportVerifyAction action = new StandardInsertImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade
        )
        when:
        action.validateFieldsByName(validFieldList, data.entrySet())
        then:
        noExceptionThrown()
    }

    def "test validateFieldsByUniqueRule"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        String fieldApiName = "field_text__c"
        fieldDescribe.setApiName(fieldApiName)
        List<IFieldDescribe> validFieldList = Lists.newArrayList(fieldDescribe)

        Map<String, Object> data = Maps.newHashMap()
        data.put(fieldApiName, "test")

        StandardInsertImportVerifyAction action = new StandardInsertImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade
        )
        when:
        action.validateFieldsByUniqueRule(validFieldList, data.entrySet())
        then:
        thrown(ValidateException.class)
    }


    def "test verifyFields"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        String fieldApiName = "field_text__c"
        fieldDescribe.setApiName(fieldApiName)
        List<IFieldDescribe> validFieldList = Lists.newArrayList(fieldDescribe)

        IObjectData objectData = new ObjectData()
        objectData.set(fieldApiName, "test")

        StandardInsertImportVerifyAction action = new StandardInsertImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade
        )
        when:
        action.verifyFields(validFieldList, objectData)
        then:
        noExceptionThrown()
    }

    def "test verifyFieldsByUniqueRule"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        String fieldApiName = "field_text__c"
        fieldDescribe.setApiName(fieldApiName)
        List<IFieldDescribe> validFieldList = Lists.newArrayList(fieldDescribe)

        IObjectData objectData = new ObjectData()
        objectData.set(fieldApiName, "test")

        StandardInsertImportVerifyAction action = new StandardInsertImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade
        )
        when:
        action.verifyFieldsByUniqueRule(validFieldList, objectData)
        then:
        thrown(ValidateException.class)
    }
}
