package com.facishare.paas.appframework.rest.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.jaxrs.annotation.RestAPI;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.FileStoreService;
import com.facishare.paas.appframework.rest.dto.data.*;
import com.facishare.paas.appframework.rest.service.ObjectInternalDataRestService;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.fxiaoke.stone.commons.domain.constant.AuthModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.ws.rs.*;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestAPI
@Service
@Path("/v1/inner/rest/internal")
@Slf4j
public class ObjectInternalDataRestController {
    @Autowired
    private ObjectInternalDataRestService objectInternalDataRestService;
    
    private FileStoreService fileStoreService;

    @Autowired
    public void setFileStoreService(FileStoreService fileStoreService) {
        this.fileStoreService = fileStoreService;
    }

    /**
     * 根据数据id获取数据
     */
    @GET
    @Path("/{descAPIName}/{dataId}")
    public FindById.Result findDataById(@PathParam("descAPIName") String descAPIName,
                                        @PathParam("dataId") String dataId,
                                        @QueryParam("includeDescribe") boolean includeDescribe,
                                        @QueryParam("includeLookupName") boolean includeLookup,
                                        @QueryParam("includeQuoteValue") boolean includeQuoteValue,
                                        @QueryParam("isFillExtendField") boolean isFillExtendField,
                                        @QueryParam("useDbCalculateValue") boolean useDbCalculateValue,
                                        @QueryParam("includeStatistics") boolean includeStatistics,
                                        @QueryParam("fillMaskField") boolean fillMaskField,
                                        @QueryParam("useSnapshotForApproval") boolean useSnapshotForApproval,
                                        @QueryParam("includeDeleted") boolean includeDeleted,
                                        @QueryParam("formatData") Boolean formatData) {
        //不打印计算日志
        RequestUtil.setRecordCalculateLog(false);
        RequestContext context = RequestContextManager.getContext();
        FindById.Arg arg = FindById.Arg.builder()
                .dataId(dataId)
                .descAPIName(descAPIName)
                .includeDescribe(includeDescribe)
                .includeLookup(includeLookup)
                .includeQuoteValue(includeQuoteValue)
                .useDbCalculateValue(useDbCalculateValue)
                .includeStatistics(includeStatistics)
                .isFillExtendField(isFillExtendField)
                .fillMaskField(fillMaskField)
                .useSnapshotForApproval(useSnapshotForApproval)
                .formatData(Optional.ofNullable(formatData).orElse(true))
                .includeDeleted(includeDeleted)
                .build();

        return objectInternalDataRestService.findById(arg, context);

    }

    /**
     * 根据数据ID查询数据
     *
     * @param descAPIName 对象描述API
     * @param json        ["id1","id2"]
     * @return 数据列表
     */
    @POST
    @Path("/{descAPIName}/find_record_name")
    public FindRecordName.Result findRecordName(@PathParam("descAPIName") String descAPIName,
                                                String json) {
        RequestContext context = RequestContextManager.getContext();
        List<String> idList = JSON.parseArray(json, String.class);
        FindRecordName.Arg arg = FindRecordName.Arg.builder()
                .idList(idList)
                .descAPIName(descAPIName)
                .build();

        return objectInternalDataRestService.findRecordName(arg, context);
    }

    @POST
    @Path("/{descAPIName}/find_by_ids")
    public FindByIds.Result findDataByIds(@PathParam("descAPIName") String descAPIName,
                                          String json,
                                          @QueryParam("includeDescribe") boolean includeDescribe,
                                          @QueryParam("includeLookupName") boolean includeLookup,
                                          @QueryParam("includeQuoteValue") boolean includeQuoteValue,
                                          @QueryParam("useDbCalculateValue") boolean useDbCalculateValue) {
        //不打印计算日志
        RequestUtil.setRecordCalculateLog(false);
        RequestContext context = RequestContextManager.getContext();
        List<String> idList = JSON.parseArray(json, String.class);
        FindByIds.Arg arg = FindByIds.Arg.builder()
                .idList(idList)
                .descAPIName(descAPIName)
                .includeDescribe(includeDescribe)
                .includeLookup(includeLookup)
                .includeQuoteValue(includeQuoteValue)
                .useDbCalculateValue(useDbCalculateValue)
                .build();
        return objectInternalDataRestService.findDataByIds(arg, context);
    }
    
    @POST
    @Path("genSignedUrl")
    @SuppressWarnings("unchecked")
    public GenSignedUrl.Result genSignedUrl(GenSignedUrl.Arg arg) {
        
        AuthModel authModel = Optional.ofNullable(arg.getOptions())
                .map(GenSignedUrl.Options::getAuthModel)
                .orElse(AuthModel.SIGN);
        IActionContext ctx = ActionContextExt.of(RequestContextManager.getContext().getUser()).getContext();

        if (MapUtils.isEmpty(arg.getData())) {
            return GenSignedUrl.Result.builder().data(arg.getData()).build();
        }
        
        if (BooleanUtils.isTrue(arg.getNested())) {
            fileStoreService.generateNestedSignedUrl((Map) arg.getData(), authModel, ctx);
        } else {
            fileStoreService.generateSignedUrl(arg.getData(), authModel, ctx);
        }
        
        
        return GenSignedUrl.Result.builder().data(arg.getData()).build();
    }

    @POST
    @Path("cleanSignedUrl")
    @SuppressWarnings("unchecked")
    public CleanSignedUrl.Result cleanSignedUrl(CleanSignedUrl.Arg arg) {
        if (MapUtils.isEmpty(arg.getData())) {
            return CleanSignedUrl.Result.builder().data(arg.getData()).build();
        }
        
        if (BooleanUtils.isTrue(arg.getNested())) {
            fileStoreService.cleanNestedSignedUrl((Map) arg.getData());
        } else {
            fileStoreService.cleanSignedUrl(arg.getData());
        }
        
        
        return CleanSignedUrl.Result.builder().data(arg.getData()).build();
    }
    
}
