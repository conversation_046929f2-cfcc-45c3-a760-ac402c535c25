package com.facishare.paas.appframework.rest.service;

import com.beust.jcommander.internal.Maps;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService;
import com.facishare.paas.appframework.rest.dto.data.FindDescribeByApiNameList;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeExtService;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.collect.Lists;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 对象描述服务单元测试
 */
@ExtendWith(MockitoExtension.class)
public class ObjectDescribeRestServiceTest {
    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private FieldRelationCalculateService fieldRelationCalculateService;

    @Mock
    private IObjectDescribeExtService describeExtService;

    @InjectMocks
    private ObjectDescribeRestService objectDescribeRestService;
    private RequestContext requestContext;

    private User user;

    @BeforeEach
    void setUp() {
        user = new User("74255", "1000");
        requestContext = RequestContext.builder()
                .user(user)
                .tenantId("74255")
                .build();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据API名称列表获取对象描述时，灰度配置为false的场景
     */
    @Test
    @DisplayName("正常场景 - 灰度配置为false时使用不同的查询方法")
    public void testFindByApiNameList_GrayConfigFalse() {
        // 准备测试数据
        FindDescribeByApiNameList.Arg arg = FindDescribeByApiNameList.Arg.builder()
                .nameList(Lists.newArrayList("testApiName1"))
                .includeFields(true)
                .build();

        // 创建真实的ObjectDescribe对象而不是Mock，这样可以通过DocumentBasedBean类型转换
        Map<String, Object> mockDescribeData = Maps.newHashMap();
        mockDescribeData.put("api_name", "testApiName1");
        mockDescribeData.put("display_name", "测试对象");
        mockDescribeData.put("active", true);

        ObjectDescribe realObjectDescribe = new ObjectDescribe(new Document(mockDescribeData));

        Map<String, IObjectDescribe> mockDescribeMap = Maps.newHashMap();
        mockDescribeMap.put("testApiName1", realObjectDescribe);

        // 使用Mockito static mock拦截FsGrayRelease.getInstance调用
        // 这样可以避免真实的网络调用，提高测试性能
        try (MockedStatic<FsGrayRelease> mockedStatic = Mockito.mockStatic(FsGrayRelease.class)) {
            FsGrayReleaseBiz mockGrayReleaseBiz = mock(FsGrayReleaseBiz.class);
            mockedStatic.when(() -> FsGrayRelease.getInstance(any())).thenReturn(mockGrayReleaseBiz);
            when(mockGrayReleaseBiz.isAllow(any(), any())).thenReturn(false);

            when(describeLogicService.findObjects(eq("74255"), eq(arg.getNameList()))).thenReturn(mockDescribeMap);

            // 执行被测试方法
            FindDescribeByApiNameList.Result result = objectDescribeRestService.findByApiNameList(arg, requestContext);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getObjectDescribe());

            // 验证Mock交互
            verify(describeLogicService, times(1)).findObjects(eq("74255"), eq(arg.getNameList()));
            verify(mockGrayReleaseBiz, times(1)).isAllow(eq(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI), eq("74255"));
        }
    }
}
