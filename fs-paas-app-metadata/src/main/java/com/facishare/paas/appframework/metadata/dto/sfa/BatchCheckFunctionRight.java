package com.facishare.paas.appframework.metadata.dto.sfa;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface BatchCheckFunctionRight {
    @Data
    @NoArgsConstructor
    class Arg {
        @SerializedName("ObjectIDs")
        @JSONField(name = "ObjectIDs")
        private List<String> objectIDs;

        @SerializedName("ObjectType")
        @JSONField(name = "ObjectType")
        private  Integer objectType;

        @SerializedName("FunctionNo")
        @JSONField(name = "FunctionNo")
        private  Integer functionNo;
    }

    @Data
    class Result extends BaseResult {
        private Map<String, Boolean> value;
    }
}
