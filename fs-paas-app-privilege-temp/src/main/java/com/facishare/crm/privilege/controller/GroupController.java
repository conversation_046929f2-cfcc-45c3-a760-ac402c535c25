package com.facishare.crm.privilege.controller;


import com.facishare.crm.privilege.controller.BaseController;
import com.facishare.crm.privilege.controller.IGroupController;
import com.facishare.crm.privilege.controller.arg.*;
import com.facishare.crm.privilege.service.GroupService;
import com.facishare.crm.privilege.util.ResultUtil;
import com.facishare.fcp.model.BaseFcpServiceResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by lei on 11/21/16.
 */

@Component
public class GroupController extends BaseController implements IGroupController {

    @Autowired
    private GroupService groupService;

    @Override
    public BaseFcpServiceResult groupList(GroupListArg arg) {
        return ResultUtil.convert(groupService.groupList(arg, getSessionContext()));
    }

    @Override
    public BaseFcpServiceResult groupUsers(GroupUsersArg arg) {
        return ResultUtil.convert(groupService.groupUsers(arg, getSessionContext()));
    }

    @Override
    public BaseFcpServiceResult updateGroupUsers(UpdateGroupUsersArg arg) {
        return ResultUtil.convert(groupService.updateGroupUsers(arg, getSessionContext()));
    }

    @Override
    public BaseFcpServiceResult deleteGroups(DeleteGroupsArg arg) {
        return ResultUtil.convert(groupService.deleteGroups(arg, getSessionContext()));
    }

    @Override
    public BaseFcpServiceResult updateGroupStatus(UpdateGroupStatusArg arg) {
        return ResultUtil.convert(groupService.updateGroupStatus(arg, getSessionContext()));
    }

    @Override
    public BaseFcpServiceResult queryGroupInfo(QueryGroupInfoArg arg) {
        return ResultUtil.convert(groupService.queryGroupInfo(arg, getSessionContext()));
    }

    @Override
    public BaseFcpServiceResult updateGroupInfo(UpdateGroupInfoArg arg) {
        return ResultUtil.convert(groupService.updateGroupInfo(arg, getSessionContext()));
    }

    @Override
    public BaseFcpServiceResult createGroup(CreateGroupArg arg) {
        return ResultUtil.convert(groupService.createGroup(arg, getSessionContext()));
    }

    @Override
    public BaseFcpServiceResult queryGroupMulti(QueryMultiArg arg) {
        return ResultUtil.convert(groupService.queryGroupMulti(arg, getSessionContext()));
    }

    @Override
    public BaseFcpServiceResult queryUserGroup(QueryUserGroupArg arg) {
        return ResultUtil.convert(groupService.queryUserGroup(arg, getSessionContext()));
    }

}
