package com.facishare.paas.appframework.common.util

import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：Tuple和Pair工具类的单元测试
 */
class TupleAndPairTest extends Specification {

    /**
     * GenerateByAI
     * 测试内容描述：测试Tuple构造函数
     */
    def "Tuple constructor test"() {
        when:
        def tuple = new Tuple<String, Integer>("key", 123)
        
        then:
        tuple.key == "key"
        tuple.value == 123
        
        when:
        def emptyTuple = new Tuple<>()
        
        then:
        emptyTuple.key == null
        emptyTuple.value == null
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试Tuple.of静态工厂方法
     */
    def "Tuple.of test"() {
        when:
        def tuple = Tuple.of("key", 123)
        
        then:
        tuple.key == "key"
        tuple.value == 123
        tuple instanceof Tuple
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试Tuple的setter方法
     */
    def "Tuple setter test"() {
        given:
        def tuple = new Tuple<String, Integer>()
        
        when:
        tuple.setKey("newKey")
        tuple.setValue(456)
        
        then:
        tuple.key == "newKey"
        tuple.value == 456
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试Pair构造函数
     */
    def "Pair constructor test"() {
        when:
        def pair = new Pair<String, Integer>("key", 123)
        
        then:
        pair.key == "key"
        pair.value == 123
        
        when:
        def emptyPair = new Pair<>()
        
        then:
        emptyPair.key == null
        emptyPair.value == null
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试Pair.of静态工厂方法
     */
    def "Pair.of test"() {
        when:
        def pair = Pair.of("key", 123)
        
        then:
        pair.key == "key"
        pair.value == 123
        pair instanceof Pair
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试Pair的setter方法
     */
    def "Pair setter test"() {
        given:
        def pair = new Pair<String, Integer>()
        
        when:
        pair.setKey("newKey")
        pair.setValue(456)
        
        then:
        pair.key == "newKey"
        pair.value == 456
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试Tuple和Pair的相等性
     */
    def "equality test"() {
        given:
        def tuple1 = Tuple.of("key", 123)
        def tuple2 = Tuple.of("key", 123)
        def tuple3 = Tuple.of("differentKey", 123)
        
        def pair1 = Pair.of("key", 123)
        def pair2 = Pair.of("key", 123)
        def pair3 = Pair.of("differentKey", 123)
        
        expect:
        // Tuple相等性测试
        tuple1 == tuple2
        tuple1 != tuple3
        
        // Pair相等性测试
        pair1 == pair2
        pair1 != pair3
        
        // Tuple和Pair不相等
        tuple1 != pair1
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试Tuple和Pair的哈希码
     */
    def "hashCode test"() {
        given:
        def tuple1 = Tuple.of("key", 123)
        def tuple2 = Tuple.of("key", 123)
        
        def pair1 = Pair.of("key", 123)
        def pair2 = Pair.of("key", 123)
        
        expect:
        // 相同内容的Tuple哈希码相同
        tuple1.hashCode() == tuple2.hashCode()
        
        // 相同内容的Pair哈希码相同
        pair1.hashCode() == pair2.hashCode()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试不同类型参数
     */
    @Unroll
    def "#desc type parameters test"() {
        expect:
        def tuple = Tuple.of(key, value)
        tuple.key == key
        tuple.value == value
        
        def pair = Pair.of(key, value)
        pair.key == key
        pair.value == value
        
        where:
        desc           | key           | value
        "String,Int"   | "key"         | 123
        "Int,String"   | 123           | "value"
        "null,String"  | null          | "value"
        "String,null"  | "key"         | null
        "null,null"    | null          | null
        "Object,List"  | new Object()  | [1, 2, 3]
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试toString方法
     */
    def "toString test"() {
        given:
        def tuple = Tuple.of("key", 123)
        def pair = Pair.of("key", 123)
        
        expect:
        tuple.toString().contains("key")
        tuple.toString().contains("123")
        
        pair.toString().contains("key")
        pair.toString().contains("123")
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试使用复杂对象
     */
    def "complex objects test"() {
        given:
        def complexKey = [id: 1, name: "test"]
        def complexValue = [value: "data", count: 5]
        
        when:
        def tuple = Tuple.of(complexKey, complexValue)
        def pair = Pair.of(complexKey, complexValue)
        
        then:
        tuple.key.id == 1
        tuple.key.name == "test"
        tuple.value.value == "data"
        tuple.value.count == 5
        
        pair.key.id == 1
        pair.key.name == "test"
        pair.value.value == "data"
        pair.value.count == 5
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试在集合中使用
     */
    def "usage in collections test"() {
        given:
        def tupleList = [
            Tuple.of("key1", 1),
            Tuple.of("key2", 2),
            Tuple.of("key3", 3)
        ]
        
        def pairMap = [
            "pair1": Pair.of("key1", 1),
            "pair2": Pair.of("key2", 2),
            "pair3": Pair.of("key3", 3)
        ]
        
        expect:
        // 测试列表操作
        tupleList.size() == 3
        tupleList.find { it.key == "key2" }.value == 2
        tupleList.collect { it.value }.sum() == 6
        
        // 测试Map操作
        pairMap.size() == 3
        pairMap["pair2"].key == "key2"
        pairMap.values().collect { it.value }.sum() == 6
    }
} 