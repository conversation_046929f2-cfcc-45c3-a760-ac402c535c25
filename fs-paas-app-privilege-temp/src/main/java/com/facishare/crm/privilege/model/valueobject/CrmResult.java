package com.facishare.crm.privilege.model.valueobject;

import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 * User: gaozl
 * Date: 16-12-16
 * Time: 下午2:23
 * To change this template use File | Settings | File Templates.
 */
@Data
public class CrmResult {
    public static final int SUCCESS = 0;
    public static final int FAIL = 1;
    public static final String SUCCESS_MSG = "success";

    private int code;
    private String msg;
    private Object result;

    public CrmResult() {
        this(0, "", null);
    }

    public CrmResult(int code) {
        this(code, "", null);
    }

    public CrmResult(int code, Object result) {
        this(code, "", result);
    }

    public CrmResult(int code, String msg, Object result) {
        this.code = code;
        this.msg = msg;
        this.result = result;
    }

    public boolean isSuccess() {
        return SUCCESS == code;
    }

}
