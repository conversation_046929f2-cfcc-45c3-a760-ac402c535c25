package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.google.common.base.Strings;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * Created by linqiuying on 17/5/18.
 */
public class SelectOneDataConverter extends AbstractFieldDataConverter {
    @Override
    public String convertFieldData(SessionContext sessionContext) {
        List<ISelectOption> options = ((SelectOneFieldDescribe) getFieldDescribe()).getSelectOptions();
        Object oldData = getObjectData().get(getFieldDescribe().getApiName(), Object.class);
        if (CollectionUtils.isEmpty(options) || oldData == null || Strings.isNullOrEmpty(oldData.toString())) {
            return "";
        }
        String value = String.valueOf(oldData);
        for (ISelectOption option : options) {
            if (option.getValue().equals(value)) {
                if (Objects.equals(SelectOne.OPTION_OTHER_VALUE, value)) {
                    String otherValue = (String) getObjectData().get(getFieldDescribe().getApiName() + "__o");
                    if (Strings.isNullOrEmpty(otherValue)) {
                        return option.getLabel();
                    } else {
                        return option.getLabel() + ": " + otherValue;
                    }
                }
                return option.getLabel();
            }
        }
        return "";
    }
}
