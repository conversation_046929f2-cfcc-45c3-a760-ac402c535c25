package com.facishare.paas.appframework.core.predef.facade;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.uievent.UIEventProcess;
import com.facishare.paas.appframework.core.predef.service.calculate.CalculateWithUIActionCallbackContainer;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2024/4/11.
 */
public interface CalculateServiceFacade {

    UIEventProcess.ProcessRequest calculateWithUIActionCallback(CalculateWithUIActionCallbackContainer container,
                                                                ObjectDataDocument masterData,
                                                                Map<String, List<ObjectDataDocument>> detailData);
}
