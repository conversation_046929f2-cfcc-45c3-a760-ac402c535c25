package com.facishare.crm.userdefobj;


import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 这个类以后要干掉，请使用ObjectAction
 * <p>
 * Created by yusb on 2017/6/8.
 *
 * @see com.facishare.paas.appframework.common.util.ObjectAction
 */
@Deprecated
public enum CrmActionEnum {

    DELETE(ObjectAction.DELETE.getActionCode(), ObjectAction.DELETE.getI18NKey()),
    VIEW_DETAIL(ObjectAction.VIEW_DETAIL.getActionCode(), ObjectAction.VIEW_DETAIL.getI18NKey()),
    VIEW_LIST(ObjectAction.VIEW_LIST.getActionCode(), ObjectAction.VIEW_LIST.getI18NKey()),
    CREATE(ObjectAction.CREATE.getActionCode(), ObjectAction.CREATE.getI18NKey()),
    UPDATE(ObjectAction.UPDATE.getActionCode(), ObjectAction.UPDATE.getI18NKey()),
    BATCH_IMPORT(ObjectAction.BATCH_IMPORT.getActionCode(), ObjectAction.BATCH_IMPORT.getI18NKey()),
    BATCH_EXPORT(ObjectAction.BATCH_EXPORT.getActionCode(), ObjectAction.BATCH_EXPORT.getI18NKey()),
    INVALID(ObjectAction.INVALID.getActionCode(), ObjectAction.INVALID.getI18NKey()),
    RECOVER(ObjectAction.RECOVER.getActionCode(), ObjectAction.RECOVER.getI18NKey()),

    PRINT(ObjectAction.PRINT.getActionCode(), ObjectAction.PRINT.getI18NKey()),

    CHANGE_OWNER(ObjectAction.CHANGE_OWNER.getActionCode(), ObjectAction.CHANGE_OWNER.getI18NKey()),
    ADD_TEAM_MEMBER(ObjectAction.ADD_TEAM_MEMBER.getActionCode(), ObjectAction.ADD_TEAM_MEMBER.getI18NKey()),
    EDIT_TEAM_MEMBER(ObjectAction.EDIT_TEAM_MEMBER.getActionCode(), ObjectAction.EDIT_TEAM_MEMBER.getI18NKey()),
    DELETE_TEAM_MEMBER(ObjectAction.DELETE_TEAM_MEMBER.getActionCode(), ObjectAction.DELETE_TEAM_MEMBER.getI18NKey()),
    RELATE(ObjectAction.RELATE.getActionCode(), ObjectAction.RELATE.getI18NKey()),
    BULK_RELATE(ObjectAction.BULK_RELATE.getActionCode(), ObjectAction.BULK_RELATE.getI18NKey()),
    BULK_DISRELATE(ObjectAction.BULK_DISRELATE.getActionCode(), ObjectAction.BULK_DISRELATE.getI18NKey()),
    BULK_DELETE(ObjectAction.BULK_DELETE.getActionCode(), ObjectAction.BULK_DELETE.getI18NKey()),
    BULK_INVALID(ObjectAction.BULK_INVALID.getActionCode(), ObjectAction.BULK_INVALID.getI18NKey()),
    BULK_RECOVER(ObjectAction.BULK_RECOVER.getActionCode(), ObjectAction.BULK_RECOVER.getI18NKey()),
    START_BPM(ObjectAction.START_BPM.getActionCode(), ObjectAction.START_BPM.getI18NKey()),
    VIEW_ENTIRE_BPM(ObjectAction.VIEW_ENTIRE_BPM.getActionCode(), ObjectAction.VIEW_ENTIRE_BPM.getI18NKey()),
    STOP_BPM(ObjectAction.STOP_BPM.getActionCode(), ObjectAction.STOP_BPM.getI18NKey()),
    CHANGE_BPM_APPROVER(ObjectAction.CHANGE_BPM_APPROVER.getActionCode(), ObjectAction.CHANGE_BPM_APPROVER.getI18NKey()),
    ADD_EVENT(ObjectAction.ADD_EVENT.getActionCode(), ObjectAction.ADD_EVENT.getI18NKey()),

    SIGN_IN(ObjectAction.SIGN_IN.getActionCode(), ObjectAction.SIGN_IN.getI18NKey()),
    SIGN_OUT(ObjectAction.SIGN_OUT.getActionCode(), ObjectAction.SIGN_OUT.getI18NKey()),
    PAY(ObjectAction.PAY.getActionCode(), ObjectAction.PAY.getI18NKey()),
    INTELLIGENTFORM(ObjectAction.INTELLIGENTFORM.getActionCode(), ObjectAction.INTELLIGENTFORM.getI18NKey()),
    LOCK(ObjectAction.LOCK.getActionCode(), ObjectAction.LOCK.getI18NKey()),
    UNLOCK(ObjectAction.UNLOCK.getActionCode(), ObjectAction.UNLOCK.getI18NKey()),
    MODIFYLOG_RECOVER(ObjectAction.MODIFYLOG_RECOVER.getActionCode(), ObjectAction.MODIFYLOG_RECOVER.getI18NKey()),

    SALE_RECORD(ObjectAction.SALE_RECORD.getActionCode(), ObjectAction.SALE_RECORD.getI18NKey()),
    DIAL(ObjectAction.DIAL.getActionCode(), ObjectAction.DIAL.getI18NKey()),
    SEND_MAIL(ObjectAction.SEND_MAIL.getActionCode(), ObjectAction.SEND_MAIL.getI18NKey()),
    DISCUSS(ObjectAction.DISCUSS.getActionCode(), ObjectAction.DISCUSS.getI18NKey()),
    SCHEDULE(ObjectAction.SCHEDULE.getActionCode(), ObjectAction.SCHEDULE.getI18NKey()),
    REMIND(ObjectAction.REMIND.getActionCode(), ObjectAction.REMIND.getI18NKey());

    private String actionCode;
    private String actionLabel;


    CrmActionEnum(String actionCode, String actionLabel) {
        this.actionCode = actionCode;
        this.actionLabel = actionLabel;
    }

    public String getActionCode() {
        return actionCode;
    }

    public String getActionLabel() {
        return I18N.text(actionLabel);
    }

    public static String getLabelByCode(String actionCode) {
        for (CrmActionEnum actionEnum : CrmActionEnum.values()) {
            if (StringUtils.equals(actionCode, actionEnum.getActionCode())) {
                return actionEnum.getActionLabel();
            }
        }
        return "";
    }

    public static CrmActionEnum getEnumByActionCode(String actionCode) {
        for (CrmActionEnum actionEnum : CrmActionEnum.values()) {
            if (StringUtils.equals(actionCode, actionEnum.getActionCode())) {
                return actionEnum;
            }
        }
        return null;
    }

    public static Map<String, String> getActionCodeNameMap() {
        Map<String, String> ACTION_CODE_NAME_MAP = Maps.newLinkedHashMap();
        for (CrmActionEnum crmActionEnum : CrmActionEnum.values()) {
            ACTION_CODE_NAME_MAP.put(crmActionEnum.getActionCode(), crmActionEnum.getActionLabel());
        }
        return ACTION_CODE_NAME_MAP;
    }


    public static List<String> getUdobjFunctionList() {
        //所有的自定义对象的操作集合
        List<String> UDOBJ_FUNCTION_CODE_LIST = Lists.newArrayList();
        // 这里面记录了所有的权限操作。按照产品描述的顺序。
        // https://www.tapd.cn/20094761/prong/stories/view/1120094761001084715?url_cache_key=0f92a90a3168efef74b43e5211ab966c&action_entry_type=stories
        UDOBJ_FUNCTION_CODE_LIST.addAll(Lists.newArrayList(CrmActionEnum.VIEW_LIST.getActionCode(), CrmActionEnum.VIEW_DETAIL.getActionCode(),
                CrmActionEnum.CREATE.getActionCode(), CrmActionEnum.UPDATE.getActionCode(), CrmActionEnum.RELATE.getActionCode(),
                CrmActionEnum.INVALID.getActionCode(), CrmActionEnum.RECOVER.getActionCode(), CrmActionEnum.DELETE.getActionCode(),
                CrmActionEnum.BATCH_IMPORT.getActionCode(), CrmActionEnum.BATCH_EXPORT.getActionCode(), CrmActionEnum.CHANGE_OWNER.getActionCode(),
                CrmActionEnum.EDIT_TEAM_MEMBER.getActionCode(),
                CrmActionEnum.START_BPM.getActionCode(), CrmActionEnum.VIEW_ENTIRE_BPM.getActionCode(),
                CrmActionEnum.STOP_BPM.getActionCode(), CrmActionEnum.CHANGE_BPM_APPROVER.getActionCode(), CrmActionEnum.PRINT.getActionCode(),
                CrmActionEnum.INTELLIGENTFORM.getActionCode(), CrmActionEnum.LOCK.getActionCode(),
                CrmActionEnum.UNLOCK.getActionCode(), CrmActionEnum.MODIFYLOG_RECOVER.getActionCode()
                )
        );
        return UDOBJ_FUNCTION_CODE_LIST;
    }
}