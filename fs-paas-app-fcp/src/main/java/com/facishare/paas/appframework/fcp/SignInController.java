package com.facishare.paas.appframework.fcp;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.paas.appframework.core.model.Action;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ActionLocateService;
import com.facishare.paas.appframework.core.model.ContextManager;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSignAction;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.metadata.impl.ObjectData;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by zhouwr on 2017/11/2
 */
@Component
@FcpService("signIn")
public class SignInController {

    @Autowired
    private ActionLocateService actionLocateService;

    @FcpMethod("signIn")
    public BaseObjectSignAction.Result signIn(BaseObjectSignAction.Arg arg) {
        ObjectData objectData = new ObjectData(Document.parse(arg.getObjectData()));
        ActionContext context = ContextManager.buildActionContext(
                objectData.getDescribeApiName(), StandardAction.SignIn.name());
        Action<BaseObjectSignAction.Arg, BaseObjectSignAction.Result> action = actionLocateService.locateAction(context, arg, BaseObjectSignAction.Result.class);

        return action.act(arg);
    }

    @FcpMethod("signOut")
    public BaseObjectSignAction.Result signOut(BaseObjectSignAction.Arg arg) {
        ObjectData objectData = new ObjectData(Document.parse(arg.getObjectData()));
        ActionContext context = ContextManager.buildActionContext(
                objectData.getDescribeApiName(), StandardAction.SignOut.name());
        Action<BaseObjectSignAction.Arg, BaseObjectSignAction.Result> action = actionLocateService.locateAction(context, arg, BaseObjectSignAction.Result.class);

        return action.act(arg);
    }

}
