{"swagger": "2.0", "info": {"title": "AppFrameWork REST API", "description": "PaaS 应用框架REST API", "version": "1.0"}, "host": "************:8234", "schemes": ["http"], "basePath": "/v1/inner/rest/recordtype", "produces": ["application/json"], "commonParams": {"userInfo": {"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, "eid": {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}}, "paths": {"/find_layout_by_recordtype": {"post": {"summary": "get layout by record type", "description": "根据业务类型获取layout", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/RecordArg"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/RecordResult"}}}}}}, "definitions": {"RecordArg": {"type": "object", "properties": {"describe_api_name": {"type": "string"}, "record_api_name": {"type": "string"}}}, "RecordResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"ui_layout": {"type": "object"}}}}}}}