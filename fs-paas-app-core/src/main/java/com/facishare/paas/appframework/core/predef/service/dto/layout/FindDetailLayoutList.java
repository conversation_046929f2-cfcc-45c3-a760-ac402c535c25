package com.facishare.paas.appframework.core.predef.service.dto.layout;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface FindDetailLayoutList {
    @Data
    class Arg {
        @JSONField(name = "M2")
        private String objectDescribeApiName;

        private String appId;
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "M3")
        private List<DocumentBaseEntity> listLayout;
    }
}
