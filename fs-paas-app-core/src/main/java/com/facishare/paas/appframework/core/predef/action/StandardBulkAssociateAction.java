package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;

import java.util.Objects;

import static com.facishare.crm.common.exception.CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_CAN_NOT_LOOKUP_MYSELF;

/**
 * Created by liyiguang on 2017/10/23.
 */
public class StandardBulkAssociateAction extends BaseObjectAssociateAction {

    protected ObjectAction getStandardAction() {
        return ObjectAction.BULK_RELATE;
    }

    @Override
    protected void validate() {
        //不能自关联
        if (Objects.equals(arg.getAssociatedObjApiName(), arg.getAssociateObjApiName())
                && arg.getAssociatedObjIds().contains(arg.getAssociateObjId())) {
            throw new ValidateException(FS_CRM_DEFOBJ_CHECKED_CAN_NOT_LOOKUP_MYSELF.getMessage());
        }
        super.validate();
    }

    @Override
    protected void doStandardAssociate(Arg arg) {
        if (CollectionUtils.empty(associatedDataList)) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.DATA_IN_INVALID_OR_DELETE));
        }
        serviceFacade.bulkAssociate(arg.getAssociateObjId(), associateDescribe, objectDescribe,
                actionContext.getUser(), associatedDataList, associateFieldApiName, updateFieldApiNames);
    }
}
