package com.facishare.paas.appframework.metadata.layout;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ComponentActions;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.ButtonUsePageType;
import com.facishare.paas.appframework.metadata.LayoutButtonExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.config.ButtonConfig;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 布局工具类
 *
 * <AUTHOR>
 */
public class ButtonOrder {


    /**
     * 按钮显示模式 word ：文本模式  icon ：图标模式
     */

    public static final String DISPLAY_MODE = "display_mode";

    /**
     * 根据排序好的按钮模板列表，对目标按钮列表排序
     *
     * @param target   目标列表
     * @param template 模板列表
     * @return 排序好的按钮列表
     */
    public static List<IButton> orderingByTemplate(List<IButton> target, List<IButton> template) {

        //将button列表转为apiName列表
        List<String> tempApiNameList = template.stream().map(IButton::getName).collect(Collectors.toList());
        List<String> targetApiNameList = target.stream().map(IButton::getName).collect(Collectors.toList());
        //根据模板列表，排序目标按钮别表，排除了模板中不存在的按钮
        List<IButton> tempOrderedTarget = tempApiNameList.stream()
                .filter(targetApiNameList::contains)
                .map(x -> mapButton(target, template, x))
                .collect(Collectors.toList());
        //无重复合并，将模板中不存在的按钮(新增按钮)排到列表最后
        target.removeAll(tempOrderedTarget);
        // AI Agent 按钮默认为隐藏状态，需要用户手动放出来
        removeAIAgentButtons(target);
//        target.removeIf(it -> tempOrderedTarget.contains(it) || LayoutButtonExt.of(it).isSystemButton());
        tempOrderedTarget.addAll(target);
        return tempOrderedTarget;
    }

    private static void removeAIAgentButtons(List<IButton> target) {
        target.removeIf(button -> LayoutButtonExt.of(button).isAIAgent());
    }

    private static IButton mapButton(List<IButton> target, List<IButton> template, String buttonName) {
        Optional<IButton> templateButton = template.stream().filter(t -> buttonName.equals(t.getName()))
                .findFirst();
        Optional<IButton> targetButton = target.stream()
                .filter(t -> buttonName.equals(t.getName()))
                .findFirst();
        if (templateButton.isPresent() && targetButton.isPresent()) {
            IButton button = targetButton.get();
            Object displayModel = templateButton.get().get(DISPLAY_MODE);
            if (Objects.nonNull(displayModel)) {
                button.set(DISPLAY_MODE, displayModel);
            }
            return button;
        }
        return new Button();
    }

    /**
     * 过滤自定义按钮，已删除的按钮排除
     * 将自定义按钮转化为IButton
     *
     * @param iUdefButtons 自定义按钮列表
     * @return 可排序的自定义按钮
     */
    public static List<IButton> filteringUdefButton(List<IUdefButton> iUdefButtons) {
        List<IButton> result = Lists.newArrayList();
        if (CollectionUtils.notEmpty(iUdefButtons)) {
            result = iUdefButtons.stream()
                    .filter(b -> !b.isDeleted())
                    .filter(b -> b.getApiName().endsWith("__c"))
                    .map(x -> {
                        Button b = new Button();
                        LayoutButtonExt layoutButtonExt = LayoutButtonExt.of(b);
                        layoutButtonExt.setActionType(IButton.ACTION_TYPE_CUSTOM);
                        layoutButtonExt.setName(x.getApiName());
                        layoutButtonExt.setLabel(x.getLabel());
                        layoutButtonExt.setIsActive(x.isActive());
                        return b;
                    }).collect(Collectors.toList());
        }
        return result;
    }

    /**
     * 更新布局中，按钮列表中的自定义按钮的激活状态,去除删除的按钮
     *
     * @param customButtons 最新自定义按钮列表
     * @param layoutExt     将要下发给前端的布局，包括按钮排序
     */
    public static void updateCustomButtonWithLayout(List<IButton> customButtons, LayoutExt layoutExt) {
        if (CollectionUtils.empty(customButtons)) {
            // 无需要排序的自定义按钮，将buttonOrder中的所有自定义按钮删除
            List<IButton> buttonOrder = layoutExt.getButtonOrder();
            if (CollectionUtils.empty(buttonOrder)) {
                return;
            }
            List<IButton> buttons = buttonOrder.stream().filter(it -> LayoutButtonExt.of(it).isSystemButton()).collect(Collectors.toList());
            layoutExt.setButtonOrder(buttons);
            return;
        }
        if (CollectionUtils.empty(layoutExt.getButtonOrder())) {
            //按钮没有排序
            return;
        }

        List<IButton> copy = layoutExt.getButtonOrder();
        ListIterator<IButton> iterator = copy.listIterator();

        while (iterator.hasNext()) {
            IButton b = iterator.next();
            if (customButtons.contains(b)) {
                b.set("isActive", customButtons.get(customButtons.indexOf(b)).get("isActive", Boolean.class));
                b.setLabel(customButtons.get(customButtons.indexOf(b)).getLabel());
            } else if (b.getName().endsWith("__c")) {
                //该自定义按钮已删除
                iterator.remove();
            }
        }

        List<String> hiddenButtons = CollectionUtils.nullToEmpty(layoutExt.getHiddenButtons());
        copy.removeIf(it -> hiddenButtons.contains(it.getName()));
        layoutExt.setButtonOrder(copy);

    }

    /**
     * 追加新的自定义按钮到排序按钮列表最后
     *
     * @param layoutExt 包含排序的布局
     * @param custom    自定义按钮列表，其中可能有新增钮
     */
    public static void backhanderNewCustomButtons(LayoutExt layoutExt, List<IButton> custom) {
        List<IButton> copy = Lists.newArrayList(custom);
        copy.removeAll(layoutExt.getButtonOrder());
        // AI Agent 按钮默认为隐藏状态，需要用户手动放出来
        removeAIAgentButtons(copy);
        //新增自定义按钮，后建的排最后
        Collections.reverse(copy);
        List<IButton> newList = layoutExt.getButtonOrder();
        newList.addAll(copy);
        layoutExt.setButtonOrder(newList);
    }

    public static void fillLayoutWithButtonsByDefaultOrder(LayoutExt layoutExt, List<IButton> customButtons,
                                                           ObjectDescribeExt describeExt) {
        //获取默认预设按钮
        LayoutButtons layoutButtons = LayoutButtons.getInstance(describeExt);
        List<IButton> buttons = layoutButtons.getActionButtons();
        //自定义按钮并与预制按钮合并，自定义按钮倒序
        Collections.reverse(customButtons);
        buttons.addAll(customButtons);
        //填入最后几个预定按钮
        buttons.addAll(layoutButtons.getTailButtons());
        layoutExt.setButtonOrder(buttons);
    }

    public static List<IButton> getOrderedButtonList(List<IButton> customButtons, ObjectDescribeExt describeExt) {
        //获取默认预设按钮
        LayoutButtons layoutButtons = LayoutButtons.getInstance(describeExt);
        List<IButton> buttons = layoutButtons.getActionButtons();
        //自定义按钮并与预制按钮合并,自定义按钮倒序
        Collections.reverse(customButtons);
        buttons.addAll(customButtons);
        //填入最后几个预定按钮
        buttons.addAll(layoutButtons.getTailButtons());
        return buttons;
    }

    public static List<IButton> getOrderButtonListByUsePage(List<IButton> customButtons,
                                                            ObjectDescribeExt describeExt, ButtonUsePageType usePageType) {
        switch (usePageType) {
            case Detail:
                return getOrderedButtonList(customButtons, describeExt);
            case ListNormal:
                List<IButton> buttons = ComponentActions.LIST_PAGE_HEADER.getActionButtons();
                buttons.addAll(customButtons);
                return buttons;
            case ListBatch:
                List<IButton> batchButtons = ButtonConfig.generateButtonsByOrder(describeExt.isCustomObject() ?
                        DefObjConstants.UDOBJ : describeExt.getApiName(), describeExt.getTenantId());   // 配置文件中的全部按钮按钮
                batchButtons.addAll(customButtons);
                return batchButtons;
            default:
                return customButtons;
        }
    }

    /**
     * 合并两组按钮，并去重
     *
     * @param source 源按钮列表
     * @param target 被合并按钮列表
     */
    public static void combineSpecialButtons(List<IButton> source, List<IButton> target) {
        //去重
        target.removeAll(source);
        //移除未激活的特殊按钮
        target.removeIf(x -> {
            boolean isActive = LayoutButtonExt.of(x).isActive();
            return !isActive;
        });
        //合并
        source.addAll(target);
    }

    private static void filterByTerminal(List<IButton> defaultButtons) {
        if (RequestUtil.isMobileRequest()) {
            List<IButton> webButtons = LayoutButtons.WebOnlyButtons.getActionButtons();
            defaultButtons.removeAll(webButtons);
        }
    }

    public static void backhanderNewPredefinedButtons(LayoutExt layoutExt, ObjectDescribeExt describeExt) {
        backhanderNewPredefinedButtons(layoutExt, describeExt.getApiName(), () -> {
            LayoutButtons layoutButtons1 = LayoutButtons.getInstance(describeExt);
            List<IButton> buttons = layoutButtons1.getActionButtons();
            List<IButton> tailButtons = layoutButtons1.getTailButtons();
            buttons.addAll(tailButtons);
            return buttons;
        });
    }

    /**
     * 追加或删除了预定义按钮到
     */
    public static void backhanderNewPredefinedButtons(LayoutExt layoutExt, String describeApiName, Supplier<List<IButton>> supplier) {
        //最新按钮
        List<IButton> buttons = supplier.get();
        List<IButton> buttonsCopy = Lists.newArrayList(buttons);

        //新增预定义按钮
        List<IButton> currentButtons = layoutExt.getButtonOrder();
        convertDealToTransfer(currentButtons, describeApiName);
        buttons.removeAll(currentButtons);
        if (CollectionUtils.notEmpty(buttons)) {
            currentButtons.addAll(buttons);
        }
        //删除的预定义按钮
        currentButtons.removeIf(button -> {
            if ("default".equals(button.getActionType())) {
                //预定义按钮
                return !buttonsCopy.contains(button);
            }
            return false;
        });

        layoutExt.setButtonOrder(currentButtons);
    }

    public static void convertDealToTransfer(List<IButton> buttons, String apiName) {
        if (!Utils.LEADS_API_NAME.equals(apiName) || CollectionUtils.empty(buttons)) {
            return;
        }
        buttons.stream()
                .filter(button -> ObjectAction.DEAL.getActionCode().equals(button.getAction()))
                .findFirst()
                .ifPresent(button -> {
                    button.setName(ObjectAction.TRANSFER.getButtonApiName());
                    button.setAction(ObjectAction.TRANSFER.getActionCode());
                    button.setLabel(ObjectAction.TRANSFER.getActionLabel());
                });

    }

    /**
     * 同步layout和ObjectAction中按钮名称
     */
    public static void synchronizeName(LayoutExt layoutExt, ObjectDescribeExt describeExt) {
        LayoutButtons layoutButtons = LayoutButtons.getInstance(describeExt);
        List<IButton> defaultButtons = layoutButtons.getActionButtons();
        List<IButton> buttons = layoutExt.getButtonOrder();
        buttons.forEach(button -> {
            defaultButtons.forEach(button2 -> {
                if (button2.equals(button)) {
                    button.setLabel(button2.bgetLabel());
                }
            });
        });
        layoutExt.setButtonOrder(buttons);
    }
}
