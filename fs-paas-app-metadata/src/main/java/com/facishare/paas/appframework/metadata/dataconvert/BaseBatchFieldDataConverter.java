package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2019/4/1
 */
public abstract class BaseBatchFieldDataConverter extends AbstractBatchFieldDataConverter {

    protected abstract Map<String, String> getIdNameMap(List<String> idList, IFieldDescribe fieldDescribe, User user);

    protected String getId(IObjectData data, IFieldDescribe fieldDescribe) {
        Object o = data.get(fieldDescribe.getApiName());
        if (o instanceof List) {
            return ((List) o).get(0).toString();
        }
        return o.toString();
    }

    @Override
    protected void convert(List<IObjectData> dataList, IFieldDescribe fieldDescribe, DataConvertContext context) {
        List<String> idList = dataList.stream().map(x -> getId(x, fieldDescribe)).distinct().collect(Collectors.toList());
        Map<String, String> idNameMap = getIdNameMap(idList, fieldDescribe, context.getUser());
        dataList.forEach(x -> x.set(fieldDescribe.getApiName(), idNameMap.get(getId(x, fieldDescribe))));
    }
}
