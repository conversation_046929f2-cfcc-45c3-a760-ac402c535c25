package com.facishare.paas.appframework.core.util;

import com.facishare.organization.adapter.api.config.service.EnterpriseLanguageClient;
import com.fxiaoke.i18n.client.api.Language;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class LanguageClientUtil {
    private static EnterpriseLanguageClient client;

    @Autowired
    public void setEnterpriseLanguageClient(EnterpriseLanguageClient client) {
        LanguageClientUtil.client = client;
    }

    public static List<Language> getLanguages(Long eid){
        return client.getLanguagesInLicenseAndSettings(String.valueOf(eid));
    }

    public static List<Language> getLanguages(String tenantId){
        return client.getLanguagesInLicenseAndSettings(tenantId);
    }


    public static boolean hasLanguage(String ei) {
        if (StringUtils.isBlank(ei)) {
            return false;
        }
        String languageTag = RequestUtil.getLanguageTag();
        if (StringUtils.isBlank(languageTag)) {
            return false;
        }
        List<Language> languages = getLanguages(ei);
        if (CollectionUtils.isEmpty(languages)) {
            return false;
        }
        return languages.stream()
                .map(Language::getCode)
                .map(languageCode -> Lang.of(languageCode).getValue())
                .anyMatch(languageTag::equals);
    }




}
