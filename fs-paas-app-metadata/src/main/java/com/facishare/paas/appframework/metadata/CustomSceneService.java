package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.CustomSceneResult;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.metadata.api.SearchTemplateCode;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import lombok.Data;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Created by yusb on 2017/8/22.
 */
public interface CustomSceneService {

    CustomSceneResult create(String json, User user);

    CustomSceneResult update(String json, User user);

    CustomSceneResult useNewScene(String id, String describeApiName, User user);

    CustomSceneResult delete(String id, String describeApiName, User user);

    @Deprecated
    CustomSceneResult findById(String id, String describeApiName, User user);

    CustomSceneResult findByIdWithMerge(String id, String describeApiName, String sceneType, User user);

    CustomSceneResult findByIdJoinTenant(String id, String describeApiName, String sceneType, User user);

    CustomSceneResult adjustOrder(String json, String describeApiName, User user);

    /**
     * 请使用 findByDescribeApiNameAndExtendAttribute 方法
     *
     * @param describeApiName
     * @param extendAttribute
     * @param user
     * @return
     */
    @Deprecated
    List<ISearchTemplate> findByObjectDescribeAPIName(String describeApiName, String extendAttribute, User user);

    List<ISearchTemplate> findByDescribeApiNameAndExtendAttribute(String describeApiName, String extendAttribute, User user);

    List<ISearchTemplate> findTemplateByDescribeApiName(IObjectDescribe objectDescribe, String extendAttribute,
                                                        List<String> fieldList, List<LayoutRuleExt.FieldConfig> fieldConfigs, User user);

    List<ISearchTemplate> findByDescribeApiName(String describeApiName, String extendAttribute, List<LayoutRuleExt.FieldConfig> validFieldListConfig, User user);

    ISearchTemplate findDefaultByDescribeApiName(String describeApiName, User user);

    Optional<ISearchTemplate> findDefaultByDescribeApiName(User user, IObjectDescribe describe, String extendAttribute, ListComponentExt listComponentExt, String thirdRecordType);

    List<ISearchTemplate> findByObjectDescribeAPINameAndCode(String describeApiName, User user, Set<SearchTemplateCode> codes);

    CustomSceneResult createFieldListConfig(String describeApiName, String sceneType, String sceneId,
                                            String extendAttribute, List<IHeadField> headFields, User user);

    CustomSceneResult createOrUpdateFieldWidthConfig(String describeApiName, String sceneApiName, String sceneId,
                                                     String extendAttribute, List<IHeadField> headFields, User user);

    CustomSceneResult setDefaultScene(String json, User user);

    List<String> getAuthorizedFieldsInSceneExcludeDisable(IObjectDescribe describe, User user);

    List<String> getAuthorizedFieldsInScene(IObjectDescribe describe, User user);

    CustomSceneResult saveAs(String json, User user);

    boolean templateIsChange(List<String> ids, String apiName, User user);

    @Data
    class AdjustOrderDTO {
        private String _id;
        private Boolean is_hidden;
        private String type;
    }
}
