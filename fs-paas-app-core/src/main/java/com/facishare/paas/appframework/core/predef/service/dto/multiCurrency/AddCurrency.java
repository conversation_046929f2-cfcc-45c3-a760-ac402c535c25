package com.facishare.paas.appframework.core.predef.service.dto.multiCurrency;

import com.facishare.paas.appframework.metadata.MtCurrency;
import lombok.Builder;
import lombok.Data;

public interface AddCurrency {

    @Data
    class Arg {
        private String currencyLabel;
        private String currencyCode;
        private String exchangeRate;
        private String currencyPrefix;
        private String currencySuffix;
        private String currencySymbol;
        private String currencyUnit;
        private String currencyType;

        public MtCurrency to() {
            return MtCurrency.builder()
                    .currencyLabel(currencyLabel)
                    .currencyCode(currencyCode)
                    .exchangeRate(exchangeRate)
                    .currencyPrefix(currencyPrefix)
                    .currencySuffix(currencySuffix)
                    .currencySymbol(currencySymbol)
                    .currencyUnit(currencyUnit)
                    .currencyType(currencyType)
                    .build();
        }
    }

    @Data
    @Builder
    class Result {

    }
}
