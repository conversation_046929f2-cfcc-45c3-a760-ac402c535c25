package com.facishare.paas.appframework.metadata;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;

public enum QixinGroupChangeType {
    Empty(-1, I18N.text(I18NKey.NOT_EXIST)),
    Enable(1, I18N.text(I18NKey.ENABLE)),
    Disable(2, I18N.text(I18NKey.DISABLE)),
    Delete(3, I18N.text(I18NKey.DELETE)),
    Create(4, I18N.text(I18NKey.CREATE)),
    Edit(5, I18N.text(I18NKey.EDIT));

    int id;
    String label;

    QixinGroupChangeType(int id, String label) {
        this.id = id;
        this.label = label;
    }

    public int getId() {
        return id;
    }

    public static QixinGroupChangeType parseFromId(int id) {
        switch (id) {
            case 1:
                return Enable;
            case 2:
                return Disable;
            case 3:
                return Delete;
            default:
                return Empty;
        }
    }
}
