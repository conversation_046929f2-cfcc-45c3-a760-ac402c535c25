package com.facishare.paas.appframework.core.predef.service;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.service.MessagePollingService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.bizconf.*;
import com.facishare.paas.appframework.core.predef.service.dto.bizconf.QueryAllConfigData.BizTypeValue;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.restful.client.exception.FRestClientException;
import com.fxiaoke.bizconf.bean.BizType;
import com.fxiaoke.bizconf.bean.ConfigDefPojo;
import com.fxiaoke.bizconf.bean.ConfigPojo;
import com.fxiaoke.bizconf.factory.BizConfClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.BizConfServiceImpl.SUPPORT_TEAM_ADD_CREATOR;
import static com.facishare.paas.appframework.metadata.BizConfServiceImpl.getSupportTeamAddCreatorConfigCode;

/**
 * Created by zhouwr on 2020/7/30
 */
@Slf4j
@Component
@ServiceModule("biz_conf")
public class ObjectBizConfService {

    private static final String PACKAGE = "CRM";

    @Autowired
    private BizConfClient bizConfClient;

    @Autowired
    private RecordTypeLogicService recordTypeLogicService;

    @Autowired
    private BizConfService bizConfService;

    @Autowired
    private MessagePollingService messagePollingService;

    @Autowired
    private DescribeLogicService describeLogicService;

    @ServiceMethod("query_config_data")
    public QueryConfigData.Result queryConfigData(QueryConfigData.Arg arg, ServiceContext context) {
        try {
            List<ConfigPojo> configPojoList = bizConfClient.queryConfigByMultipleKey(context.getTenantId(), PACKAGE, arg.getConfigCode(),
                    Sets.newHashSet(arg.getBizTypeValueList()));
            List<ConfigData> configDataList = configPojoList.stream().map(ConfigData::of).collect(Collectors.toList());
            return QueryConfigData.Result.builder().configDataList(configDataList).build();
        } catch (FRestClientException e) {
            log.warn("queryConfigData failed:{},tenantId:{},arg:{}", e.getCode() + "-" + e.getMessage(), context.getTenantId(), arg, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @ServiceMethod("save_config_data")
    public SaveConfigData.Result saveConfigData(SaveConfigData.Arg arg, ServiceContext context) {
        try {
            List<ConfigPojo> configPojoList = arg.toConfigPojoList(PACKAGE, context.getTenantId(), context.getUser().getUserId());
            bizConfClient.batchUpsertMultipleConfig(context.getTenantId(), configPojoList);
            notifyDescribeLayoutChange(context.getTenantId(), configPojoList);
            return SaveConfigData.Result.builder().build();
        } catch (FRestClientException e) {
            log.warn("saveConfigData failed:{},tenantId:{},arg:{}", e.getCode() + "-" + e.getMessage(), context.getTenantId(), arg, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @ServiceMethod("query_config_def")
    public QueryConfigDef.Result queryConfigDef(QueryConfigDef.Arg arg, ServiceContext context) {
        List<ConfigDefPojo> configDefList = bizConfService.queryConfigDef(context.getTenantId(), arg.getDescribeApiName(), arg.getBizType());
        return QueryConfigDef.Result.builder().configDefList(configDefList).build();
    }

    @ServiceMethod("save_config_def")
    public SaveConfigDef.Result saveConfigDef(SaveConfigDef.Arg arg, ServiceContext context) {
        try {
            if (CollectionUtils.empty(arg.getConfigDefList())) {
                return SaveConfigDef.Result.builder().build();
            }
            arg.getConfigDefList().forEach(x -> {
                x.setTenantId(context.getTenantId());
                x.setDescribeApiName(arg.getDescribeApiName());
                x.setBizType(convertBizType(arg.getBizType()));
            });
            bizConfClient.batchUpsertConfigDef(context.getTenantId(), arg.getConfigDefList());
            List<String> keys = arg.getConfigDefList().stream().map(ConfigDefPojo::getConfigCode).collect(Collectors.toList());
            notifyDescribeLayoutChangeByKeys(context.getTenantId(), keys);
            return SaveConfigDef.Result.builder().build();
        } catch (FRestClientException e) {
            log.warn("saveConfigDef failed:{},tenantId:{},arg:{}", e.getCode() + "-" + e.getMessage(), context.getTenantId(), arg, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @ServiceMethod("delete_config_def")
    public DeleteConfigDef.Result deleteConfigDef(DeleteConfigDef.Arg arg, ServiceContext context) {
        try {
            if (CollectionUtils.empty(arg.getConfigCodeList())) {
                return DeleteConfigDef.Result.builder().build();
            }
            BizType bizType = convertBizType(arg.getBizType());
            bizConfClient.batchDeleteConfigDef(context.getTenantId(), arg.getDescribeApiName(), bizType,
                    Sets.newHashSet(arg.getConfigCodeList()));
            notifyDescribeLayoutChangeByKeys(context.getTenantId(), arg.getConfigCodeList());
            return DeleteConfigDef.Result.builder().build();
        } catch (FRestClientException e) {
            log.warn("deleteConfigDef failed:{},tenantId:{},arg:{}", e.getCode() + "-" + e.getMessage(), context.getTenantId(), arg, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @ServiceMethod("query_all_config_data")
    public QueryAllConfigData.Result queryAllConfigData(QueryAllConfigData.Arg arg, ServiceContext context) {
        try {
            List<BizTypeValue> bizTypeValueList = fetchBizTypeValues(context.getTenantId(), arg.getDescribeApiName(), arg.getBizType());
            List<ConfigDefPojo> configDefList = Lists.newArrayList();
            //查询库里已经存在的描述配置
            List<ConfigDefPojo> configDefPojoList = bizConfService.queryConfigDef(context.getTenantId(), arg.getDescribeApiName(), arg.getBizType());
            if (CollectionUtils.notEmpty(configDefPojoList)) {
                configDefList.addAll(configDefPojoList);
            }
            for (ConfigDefPojo configDefPojo : configDefList) {
                if (isSupportTeamAddCreator(configDefPojo.getConfigCode())) {
                    buildDefaultTeamMemberConfig(configDefPojo);
                }
            }
            //不区分业务类型且没有匹配相关团队加入负责人的配置项则构造一个默认值
            if (ignoreRecordType(arg.getBizType()) && configDefList.stream().noneMatch(x -> Objects.equals(x.getConfigCode(), getSupportTeamAddCreatorConfigCode(arg.getDescribeApiName())))) {
                IObjectDescribe describe = describeLogicService.findObjectWithoutCopy(context.getTenantId(), arg.getDescribeApiName());
                if (!ObjectDescribeExt.of(describe).isSlaveObject()) {
                    ConfigDefPojo configDefPojo = new ConfigDefPojo();
                    buildDefaultTeamMemberConfig(configDefPojo);
                    configDefPojo.setConfigCode(getSupportTeamAddCreatorConfigCode(arg.getDescribeApiName()));
                    configDefPojo.setDescribeApiName(arg.getDescribeApiName());
                    configDefPojo.setDisplayOrder(1);
                    configDefPojo.setLabel(I18NExt.getOrDefault(I18NKey.CREATE_TEAM_MEMBER_CREATOR_JOIN_TEAM_MEMBER, "创建数据时创建人是否加入相关团队")); // ignoreI18n
                    configDefList.add(configDefPojo);
                }
            }

            Map<String, ConfigDefPojo> configDefMap = configDefList.stream().collect(Collectors.toMap(ConfigDefPojo::getConfigCode, x -> x));
            List<String> configCodeList = configDefList.stream().map(ConfigDefPojo::getConfigCode).collect(Collectors.toList());

            //查询描述对应的配置信息
            List<ConfigPojo> configPojoList = bizConfClient.queryConfigBySingleKeys(context.getTenantId(), PACKAGE, Sets.newHashSet(configCodeList));
            Map<String, Map<String, ConfigData>> dbConfigDataMap = configPojoList.stream().collect(Collectors.groupingBy(ConfigPojo::getKey,
                    Collectors.mapping(x -> x, Collectors.toMap(ConfigPojo::getAssistantKey, ConfigData::of))));
            Map<String, List<ConfigData>> configDataMap = Maps.newLinkedHashMap();
            //使用List循环保证配置项的顺序
            configCodeList.forEach(configCode -> {
                configDataMap.put(configCode, Lists.newArrayList());
                Map<String, ConfigData> bizType2ConfigDataMap = dbConfigDataMap.getOrDefault(configCode, Collections.emptyMap());
                //使用List循环保证表头的顺序
                bizTypeValueList.forEach(bizTypeValue -> {
                    //数据库里没有配置的构造一个默认值
                    ConfigData defaultValue = ConfigData.builder()
                            .configCode(configCode)
                            .bizTypeValue(bizTypeValue.getApiName())
                            .configValue(configDefMap.get(configCode).getDefaultValue())
                            .build();

                    ConfigData configData = bizType2ConfigDataMap.getOrDefault(bizTypeValue.getApiName(), defaultValue);
                    if (StringUtils.endsWith(configData.getConfigCode(), SUPPORT_TEAM_ADD_CREATOR)) {
                        if (Boolean.TRUE.toString().equals(configData.getConfigValue())) {
                            configData.setConfigValue(TeamMember.Permission.READONLY.getValue());
                        } else if (Boolean.FALSE.toString().equals(configData.getConfigValue())) {
                            configData.setConfigValue(TeamMember.Permission.NO_PERMISSION.getValue());
                        }
                    }
                    configDataMap.get(configCode).add(configData);
                });
            });

            return QueryAllConfigData.Result.builder()
                    .bizTypeValueList(bizTypeValueList)
                    .configDefList(configDefList)
                    .configDataMap(configDataMap)
                    .build();
        } catch (FRestClientException e) {
            log.warn("queryAllConfigData failed:{},tenantId:{},arg:{}", e.getCode() + "-" + e.getMessage(), context.getTenantId(), arg, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    private boolean isSupportTeamAddCreator(String configCode) {
        return StringUtils.endsWith(configCode, SUPPORT_TEAM_ADD_CREATOR);
    }

    private void buildDefaultTeamMemberConfig(ConfigDefPojo configDefPojo) {
        configDefPojo.setConfigType(IFieldType.SELECT_ONE);
        configDefPojo.setDefaultValue(TeamMember.Permission.NO_PERMISSION.getValue());
        String label = "label";
        String value = "value";
        List<Map<String, String>> optionValue = Lists.newArrayList();
        Map<String, String> option = Maps.newHashMap();
        option.put(label, I18NExt.text(I18NKey.CREATE_TEAM_MEMBER_NO_PERMISSION));
        option.put(value, TeamMember.Permission.NO_PERMISSION.getValue());
        optionValue.add(option);
        Map<String, String> readOption = Maps.newHashMap();
        readOption.put(label, I18NExt.text(I18NKey.CREATE_TEAM_MEMBER_READ_PERMISSION));
        readOption.put(value, TeamMember.Permission.READONLY.getValue());
        optionValue.add(readOption);
        Map<String, String> writeOption = Maps.newHashMap();
        writeOption.put(label, I18NExt.text(I18NKey.CREATE_TEAM_MEMBER_WRITE_PERMISSION));
        writeOption.put(value, TeamMember.Permission.READANDWRITE.getValue());
        optionValue.add(writeOption);
        configDefPojo.setOptionValue(JSON.toJSONString(optionValue));
    }

    @ServiceMethod("save_all_config_data")
    public SaveAllConfigData.Result saveAllConfigData(SaveAllConfigData.Arg arg, ServiceContext context) {
        try {
            if (CollectionUtils.empty(arg.getConfigDataMap())) {
                return SaveAllConfigData.Result.builder().build();
            }
            List<ConfigPojo> configPojoList = Lists.newArrayList();
            arg.getConfigDataMap().forEach((configCode, dataList) -> dataList.forEach(configData -> {
                ConfigPojo configPojo = ConfigPojo.builder()
                        .pkg(PACKAGE)
                        .tenantId(context.getTenantId())
                        .key(configCode)
                        .assistantKey(configData.getBizTypeValue())
                        .configValue(configData.getConfigValue())
                        .operator(context.getUser().getUserId())
                        .build();
                configPojoList.add(configPojo);
            }));
            bizConfClient.batchUpsertMultipleConfig(context.getTenantId(), configPojoList);
            notifyDescribeLayoutChange(context.getTenantId(), configPojoList);
            return SaveAllConfigData.Result.builder().build();
        } catch (FRestClientException e) {
            log.warn("saveAllConfigData failed:{},tenantId:{},arg:{}", e.getCode() + "-" + e.getMessage(), context.getTenantId(), arg, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    private List<BizTypeValue> fetchBizTypeValues(String tenantId, String describeApiName, String bizType) {
        if (BizType.RECORD_TYPE.getValue().equals(bizType)) {
            List<IRecordTypeOption> recordTypeOptions = recordTypeLogicService.findRecordTypeOptionList(tenantId, describeApiName, true);
            return recordTypeOptions.stream().map(BizTypeValue::of).collect(Collectors.toList());
        }
        if (ignoreRecordType(bizType)) {
            return Lists.newArrayList(BizTypeValue.builder().apiName(describeApiName).label(I18NExt.getOrDefault(I18NKey.BUSINESS_LABEL, "选项")).build()); // ignoreI18n
        }
        return Lists.newArrayList();
    }

    private boolean ignoreRecordType(String bizType) {
        return BizType.DESCRIBE_API_NAME.getValue().equals(bizType);
    }

    private BizType convertBizType(String bizType) {
        return Arrays.stream(BizType.values()).filter(x -> x.getValue().equals(bizType)).findFirst()
                .orElseThrow(() -> new MetaDataBusinessException("bizType" + bizType + " is unsupported"));
    }

    private void notifyDescribeLayoutChange(String tenantId, List<ConfigPojo> configPojoList) {
        List<String> keys = configPojoList.stream().map(ConfigPojo::getKey).collect(Collectors.toList());
        notifyDescribeLayoutChangeByKeys(tenantId, keys);
    }

    private void notifyDescribeLayoutChangeByKeys(String tenantId, List<String> keys) {
        if (CollectionUtils.empty(keys)) {
            return;
        }
        if (keys.stream().noneMatch(x -> x.endsWith(BizConfServiceImpl.SUPPORT_MANUAL_ADD))) {
            return;
        }
        messagePollingService.notifyDescribeLayoutChange(tenantId, false);
    }
}
