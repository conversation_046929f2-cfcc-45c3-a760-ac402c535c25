package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.StandardConfigDocument;
import com.facishare.paas.appframework.metadata.config.IUdefConfig;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

import static com.facishare.paas.appframework.metadata.SceneLogicService.IS_OUTER;

public class StandardOuterSceneListController extends PreDefineController<StandardOuterSceneListController.Arg, StandardOuterSceneListController.Result> {

    @Override
    protected void before(Arg arg) {
        controllerContext.setAttribute(IS_OUTER, true);
        super.before(arg);
    }


    @Override
    protected Result doService(Arg arg) {
        List<IScene> scenes = infraServiceFacade.findScenes(arg.getDescribeApiName(), controllerContext.getUser(), arg.getExtendAttribute());
        List<IScene> baseScenes = infraServiceFacade.findBaseScenes(arg.getDescribeApiName(), arg.getExtendAttribute(), controllerContext.getUser());
        return Result.of(scenes, baseScenes, StandardConfigDocument.ofList(getSceneConfig(scenes)), Maps.newHashMap());
    }

    private List<IUdefConfig> getSceneConfig(List<IScene> scenes) {
        return infraServiceFacade.findSceneConfigList(controllerContext.getUser(), controllerContext.getObjectApiName(), scenes);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Arg {
        private String describeApiName;
        private String displayName;
        private Boolean isActive;
        private int pageNumber;
        private int pageSize;
        private String appId;
        private String extendAttribute;
        private Map<String, Object> extraParams;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Result {
        private List<? extends IScene> scenes;
        private List<IScene> baseScenes;
        private List<StandardConfigDocument> sceneConfigs;
        private Map<String, Object> extraResult;
    }
}
