package com.facishare.paas.appframework.metadata.plugin;

import com.facishare.paas.appframework.common.util.UdobjSectionConfig;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.LanguageClientUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.MtFunctionPluginConf;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.impl.UdefFunction;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("FunctionPluginConfLogicServiceImpl 单元测试")
class FunctionPluginConfLogicServiceImplTest {

    @Mock
    private IRepository<MtFunctionPluginConf> repository;

    @Mock
    private FunctionLogicService functionLogicService;

    @Mock
    private LogService logService;

    @InjectMocks
    private FunctionPluginConfLogicServiceImpl functionPluginConfLogicService;

    private User testUser;
    private MtFunctionPluginConf testConfig;
    private static final String TEST_TENANT_ID = "74255";
    private static final String TEST_API_NAME = "obj_qqq__c";
    private static final String TEST_REF_OBJ_API = "conf_qqq__c";
    private static final String TEST_MODULE_NAME = "controller";
    private static final String TEST_FUNCTION_API = "func_qqq__c";

    @BeforeEach
    void setUp() {
        // 初始化测试用户
        testUser = User.builder()
                .tenantId(TEST_TENANT_ID)
                .userId("test-user-001")
                .build();

        // 初始化测试配置
        testConfig = createValidTestConfig();
    }

    /**
     * 创建有效的测试配置对象，避免验证失败
     */
    private MtFunctionPluginConf createValidTestConfig() {
        MtFunctionPluginConf config = new MtFunctionPluginConf();
        config.setApiName(TEST_API_NAME);
        config.setName("测试配置");
        config.setDescription("测试描述");
        config.setRefObjectApiName(TEST_REF_OBJ_API);
        config.setPluginProvider("APLControllerPlugin");
        config.setModuleName("RelatedList");
        config.setFunctionApiName(TEST_FUNCTION_API);
        config.setMethods(Lists.newArrayList("before"));
        config.setModuleType(Lists.newArrayList("select"));
        config.setIsActive(true);
        config.setTenantId(TEST_TENANT_ID);
        config.setAgentTypes(Lists.newArrayList("PC"));
        return config;
    }


    // ===========================================
    // 2. findAllForList方法测试
    // ===========================================

    @Test
    @DisplayName("findAllForList - 正常场景：返回有效数据列表")
    void testFindAllForList_Success() {
        // Given
        List<MtFunctionPluginConf> mockConfigs = Lists.newArrayList(testConfig);
        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(mockConfigs);

        // Mock LanguageClientUtil 和 UdobjGrayConfig 静态方法
        try (MockedStatic<LanguageClientUtil> langMock = Mockito.mockStatic(LanguageClientUtil.class);
             MockedStatic<UdobjSectionConfig> udobjSectionConfigMock = Mockito.mockStatic(UdobjSectionConfig.class);
             MockedStatic<UdobjGrayConfig> grayMock = Mockito.mockStatic(UdobjGrayConfig.class)) {
            langMock.when(() -> LanguageClientUtil.hasLanguage(any())).thenReturn(false);
            grayMock.when(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OBJECT_EXTENSION, testUser.getTenantId())).thenReturn(true);
            Set<String> showModuleName = Sets.newHashSet(
                    "Export", "ExportExcelTemplate", "List", "ListHeader", "RelatedList", "SearchList", "WebDetail", "WhatList"
            );
            udobjSectionConfigMock.when(() -> UdobjSectionConfig.getShowModuleName()).thenReturn(showModuleName);

            // When
            List<MtFunctionPluginConf> result = functionPluginConfLogicService.findAllForList(TEST_REF_OBJ_API, testUser);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(TEST_API_NAME, result.get(0).getApiName());
            verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
        }
    }

    @Test
    @DisplayName("findAllForList - 边界场景：refObjApi为空")
    void testFindAllForList_EmptyRefObjApi() {
        // When
        List<MtFunctionPluginConf> result = functionPluginConfLogicService.findAllForList("", testUser);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(repository, never()).findBy(any(), any(), any());
    }

    @Test
    @DisplayName("findAllForList - 边界场景：refObjApi为null")
    void testFindAllForList_NullRefObjApi() {
        // When
        List<MtFunctionPluginConf> result = functionPluginConfLogicService.findAllForList(null, testUser);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(repository, never()).findBy(any(), any(), any());
    }

    @Test
    @DisplayName("findAllForList - 过滤隐藏的配置")
    void testFindAllForList_FilterHiddenConfigs() {
        // Given
        MtFunctionPluginConf hiddenConfig = createValidTestConfig();
        hiddenConfig.setApiName("hidden-api");
        hiddenConfig.setIsActive(false); // 假设show()方法依赖isActive字段

        List<MtFunctionPluginConf> mockConfigs = Lists.newArrayList(testConfig, hiddenConfig);
        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(mockConfigs);

        // Mock LanguageClientUtil 静态方法
        try (MockedStatic<LanguageClientUtil> mockedStatic = Mockito.mockStatic(LanguageClientUtil.class)) {
            mockedStatic.when(() -> LanguageClientUtil.hasLanguage(any())).thenReturn(false);

            // When
            List<MtFunctionPluginConf> result = functionPluginConfLogicService.findAllForList(TEST_REF_OBJ_API, testUser);

            // Then
            assertNotNull(result);
            // 这里的结果取决于show()方法的具体实现
            verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
        }
    }

    // ===========================================
    // 3. findAvailableRuntime方法测试
    // ===========================================

    @Test
    @DisplayName("findAvailableRuntime - 正常场景：返回可用的运行时配置")
    void testFindAvailableRuntime_Success() {
        // Given
        testConfig.setIsActive(true); // 确保配置是启用的
        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList(testConfig));

        // When
        Optional<MtFunctionPluginConf> result = functionPluginConfLogicService
                .findAvailableRuntime(testUser, TEST_REF_OBJ_API, TEST_MODULE_NAME);

        // Then
        assertTrue(result.isPresent());
        assertEquals(TEST_API_NAME, result.get().getApiName());
        verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
    }

    @Test
    @DisplayName("findAvailableRuntime - 边界场景：找不到匹配配置")
    void testFindAvailableRuntime_NotFound() {
        // Given
        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList());

        // When
        Optional<MtFunctionPluginConf> result = functionPluginConfLogicService
                .findAvailableRuntime(testUser, TEST_REF_OBJ_API, "NON_EXISTING_MODULE");

        // Then
        assertFalse(result.isPresent());
        verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
    }

    @Test
    @DisplayName("findAvailableRuntime - 边界场景：配置被禁用")
    void testFindAvailableRuntime_ConfigDisabled() {
        // Given
        testConfig.setIsActive(false); // 禁用配置
        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList(testConfig));

        try (MockedStatic<FsGrayRelease> mockedStatic = Mockito.mockStatic(FsGrayRelease.class)) {
            FsGrayReleaseBiz mockGrayReleaseBiz = mock(FsGrayReleaseBiz.class);
            mockedStatic.when(() -> FsGrayRelease.getInstance(any())).thenReturn(mockGrayReleaseBiz);
            when(mockGrayReleaseBiz.isAllow(any(), any())).thenReturn(true);
            // 执行测试...
            // When
            Optional<MtFunctionPluginConf> result = functionPluginConfLogicService
                    .findAvailableRuntime(testUser, TEST_REF_OBJ_API, TEST_MODULE_NAME);

            // Then
            assertFalse(result.isPresent()); // 由于配置被禁用，应该过滤掉
            verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
        }
    }

    @Test
    @DisplayName("findAvailableRuntime - 边界场景：参数为空")
    void testFindAvailableRuntime_EmptyParameters() {
        // When & Then
        Optional<MtFunctionPluginConf> result1 = functionPluginConfLogicService
                .findAvailableRuntime(testUser, "", TEST_MODULE_NAME);
        assertFalse(result1.isPresent());

        Optional<MtFunctionPluginConf> result2 = functionPluginConfLogicService
                .findAvailableRuntime(testUser, TEST_REF_OBJ_API, "");
        assertFalse(result2.isPresent());

        verify(repository, never()).findBy(any(), any(), any());
    }

    // ===========================================
    // 4. findByApiName方法测试
    // ===========================================

    @Test
    @DisplayName("findByApiName - 正常场景：根据API名称查找")
    void testFindByApiName_Success() {
        // Given
        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList(testConfig));

        // Mock LanguageClientUtil 静态方法
        try (MockedStatic<LanguageClientUtil> mockedStatic = Mockito.mockStatic(LanguageClientUtil.class)) {
            mockedStatic.when(() -> LanguageClientUtil.hasLanguage(any())).thenReturn(false);

            // When
            Optional<MtFunctionPluginConf> result = functionPluginConfLogicService.findByApiName(testUser, TEST_API_NAME);

            // Then
            assertTrue(result.isPresent());
            assertEquals(TEST_API_NAME, result.get().getApiName());
            verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
        }
    }

    @Test
    @DisplayName("findByApiName - 边界场景：API名称不存在")
    void testFindByApiName_NotFound() {
        // Given
        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList());

        // Mock LanguageClientUtil 静态方法
        try (MockedStatic<LanguageClientUtil> mockedStatic = Mockito.mockStatic(LanguageClientUtil.class)) {
            mockedStatic.when(() -> LanguageClientUtil.hasLanguage(any())).thenReturn(false);

            // When
            Optional<MtFunctionPluginConf> result = functionPluginConfLogicService
                    .findByApiName(testUser, "NON_EXISTING_API");

            // Then
            assertFalse(result.isPresent());
            verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
        }
    }

    @Test
    @DisplayName("findByApiName - 边界场景：API名称为空")
    void testFindByApiName_EmptyApiName() {
        // When
        Optional<MtFunctionPluginConf> result1 = functionPluginConfLogicService.findByApiName(testUser, "");
        Optional<MtFunctionPluginConf> result2 = functionPluginConfLogicService.findByApiName(testUser, null);

        // Then
        assertFalse(result1.isPresent());
        assertFalse(result2.isPresent());
        verify(repository, never()).findBy(any(), any(), any());
    }

    // ===========================================
    // 5. create方法测试
    // ===========================================

    @Test
    @DisplayName("create - 正常场景：成功创建配置")
    void testCreate_Success() {
        // Given
        MtFunctionPluginConf createdConfig = createValidTestConfig();
        createdConfig.setId("generated-id");

        // Mock 检查模块名称唯一性 - 返回空表示不存在重复
        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList()); // 第一次查询：检查模块名称唯一性

        // Mock 创建成功
        when(repository.create(eq(testUser), any(MtFunctionPluginConf.class)))
                .thenReturn(createdConfig);

        // Mock 创建函数引用关系
        doNothing().when(functionLogicService).deleteAndCreateRelation(eq(testUser), anyList());

        // Mock 日志记录
        doNothing().when(logService).log(any(User.class), any(EventType.class), any(ActionType.class), anyString(), anyString());

        // 准备有效的测试配置，避免验证失败
        MtFunctionPluginConf validConfig = createValidTestConfig();

        // When
        MtFunctionPluginConf result = functionPluginConfLogicService.create(testUser, validConfig);

        // Then
        assertNotNull(result);
        assertEquals("generated-id", result.getId());
        assertEquals(TEST_TENANT_ID, result.getTenantId());
        assertTrue(result.getIsActive());

        verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
        verify(repository).create(eq(testUser), any(MtFunctionPluginConf.class));
        verify(functionLogicService).deleteAndCreateRelation(eq(testUser), anyList());
        verify(logService).log(any(User.class), any(EventType.class), any(ActionType.class), anyString(), anyString());
    }

    @Test
    @DisplayName("create - 异常场景：API名称重复")
    void testCreate_ApiNameDuplicate() {
        // Given
        MtFunctionPluginConf validConfig = createValidTestConfig();

        // Mock 检查模块名称唯一性 - 返回空表示不存在重复
        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList());

        // Mock 创建时抛出业务异常
        when(repository.create(eq(testUser), any(MtFunctionPluginConf.class)))
                .thenThrow(new MetaDataBusinessException("API名称重复"));

        // When & Then
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            functionPluginConfLogicService.create(testUser, validConfig);
        });

        assertNotNull(exception);
        verify(repository).create(eq(testUser), any(MtFunctionPluginConf.class));
        verify(functionLogicService, never()).deleteAndCreateRelation(any(), any());
        verify(logService, never()).log(any(User.class), any(EventType.class), any(ActionType.class), anyString(), anyString());
    }

    @Test
    @DisplayName("create - 异常场景：模块名称重复")
    void testCreate_ModuleNameDuplicate() {
        // Given
        MtFunctionPluginConf existingConfig = createValidTestConfig();
        existingConfig.setApiName("existing-api");
        MtFunctionPluginConf validConfig = createValidTestConfig();

        // Mock 检查模块名称唯一性 - 返回已存在的配置
        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList(existingConfig));

        // When & Then
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            functionPluginConfLogicService.create(testUser, validConfig);
        });

        assertNotNull(exception);
        verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
        verify(repository, never()).create(any(), any());
        verify(functionLogicService, never()).deleteAndCreateRelation(any(), any());
        verify(logService, never()).log(any(User.class), any(EventType.class), any(ActionType.class), anyString(), anyString());
    }

    @Test
    @DisplayName("create - 异常场景：数据验证失败")
    void testCreate_ValidationFailed() {
        // Given
        MtFunctionPluginConf invalidConfig = createValidTestConfig();
        invalidConfig.setPluginProvider("INVALID_PROVIDER"); // 无效的提供者

        // When & Then
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            functionPluginConfLogicService.create(testUser, invalidConfig);
        });

        assertNotNull(exception);
        verify(repository, never()).findBy(any(), any(), any());
        verify(repository, never()).create(any(), any());
        verify(functionLogicService, never()).deleteAndCreateRelation(any(), any());
        verify(logService, never()).log(any(User.class), any(EventType.class), any(ActionType.class), anyString(), anyString());
    }

    @Test
    @DisplayName("create - 异常场景：空的方法列表")
    void testCreate_EmptyMethods() {
        // Given
        MtFunctionPluginConf invalidConfig = createValidTestConfig();
        invalidConfig.setMethods(Lists.newArrayList()); // 空的方法列表

        // When & Then
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            functionPluginConfLogicService.create(testUser, invalidConfig);
        });

        assertNotNull(exception);
        verify(repository, never()).create(any(), any());
    }

    @Test
    @DisplayName("create - 异常场景：无效的模块名称")
    void testCreate_InvalidModuleName() {
        // Given
        MtFunctionPluginConf invalidConfig = createValidTestConfig();
        invalidConfig.setModuleName("INVALID_MODULE"); // 无效的模块名称

        // When & Then
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            functionPluginConfLogicService.create(testUser, invalidConfig);
        });

        assertNotNull(exception);
        verify(repository, never()).create(any(), any());
    }

    // ===========================================
    // 6. update方法测试
    // ===========================================

    @Test
    @DisplayName("update - 正常场景：成功更新配置")
    void testUpdate_Success() {
        // Given
        MtFunctionPluginConf updatedConfig = createValidTestConfig();
        updatedConfig.setName("更新后的名称");
        updatedConfig.setDescription("更新后的描述");

        // Mock 查找现有配置
        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList(testConfig));

        // Mock 更新成功
        when(repository.bulkUpdateExceptIgnoreFields(
                eq(testUser),
                any(List.class),
                any(Set.class)))
                .thenReturn(Lists.newArrayList(updatedConfig));

        // Mock 创建函数引用关系
        doNothing().when(functionLogicService).deleteAndCreateRelation(eq(testUser), anyList());

        // Mock 日志记录
        doNothing().when(logService).log(any(User.class), any(EventType.class), any(ActionType.class), anyString(), anyString());

        // When
        MtFunctionPluginConf result = functionPluginConfLogicService.update(testUser, updatedConfig);

        // Then
        assertNotNull(result);
        assertEquals("更新后的名称", result.getName());
        verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
        verify(repository).bulkUpdateExceptIgnoreFields(
                eq(testUser),
                any(List.class),
                any(Set.class));
        verify(functionLogicService).deleteAndCreateRelation(eq(testUser), anyList());
        verify(logService).log(any(User.class), any(EventType.class), any(ActionType.class), anyString(), anyString());
    }

    @Test
    @DisplayName("update - 异常场景：配置不存在")
    void testUpdate_ConfigNotFound() {
        // Given
        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList());

        // When & Then
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            functionPluginConfLogicService.update(testUser, testConfig);
        });

        assertNotNull(exception);
        verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
        verify(repository, never()).bulkUpdateExceptIgnoreFields(
                eq(testUser),
                any(List.class),
                any(Set.class));
    }

    @Test
    @DisplayName("update - 异常场景：禁止更新的字段被修改")
    void testUpdate_ForbiddenFieldsModified() {
        // Given
        MtFunctionPluginConf modifiedConfig = createValidTestConfig();
        modifiedConfig.setPluginProvider("DIFFERENT_PROVIDER"); // 修改不允许更新的字段

        // When & Then
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            functionPluginConfLogicService.update(testUser, modifiedConfig);
        });

        assertNotNull(exception);
        verify(repository, never()).bulkUpdateExceptIgnoreFields(
                eq(testUser),
                any(List.class),
                any(Set.class));
    }

    @Test
    @DisplayName("update - 异常场景：更新失败")
    void testUpdate_UpdateFailed() {
        // Given
        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList(testConfig));

        when(repository.bulkUpdateExceptIgnoreFields(
                eq(testUser),
                any(List.class),
                any(Set.class)))
                .thenReturn(Lists.newArrayList()); // 返回空列表模拟更新失败

        // When & Then
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            functionPluginConfLogicService.update(testUser, testConfig);
        });

        assertNotNull(exception);
        verify(repository).bulkUpdateExceptIgnoreFields(
                eq(testUser),
                any(List.class),
                any(Set.class));
    }

    // ===========================================
    // 7. enable/disable方法测试
    // ===========================================

    @Test
    @DisplayName("enable - 正常场景：成功启用配置")
    void testEnable_Success() {
        // Given
        MtFunctionPluginConf enabledConfig = createValidTestConfig();
        enabledConfig.setIsActive(true);

        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList(testConfig));

        when(repository.bulkUpdateByFields(
                eq(testUser),
                anyList(),
                anyList()))
                .thenReturn(Lists.newArrayList(enabledConfig));

        doNothing().when(logService).log(any(User.class), any(EventType.class), any(ActionType.class), anyString(), anyString());

        // When
        MtFunctionPluginConf result = functionPluginConfLogicService.enable(testUser, TEST_API_NAME);

        // Then
        assertNotNull(result);
        assertTrue(result.getIsActive());
        verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
        verify(repository).bulkUpdateByFields(
                eq(testUser),
                anyList(),
                anyList());
        verify(logService).log(any(User.class), any(EventType.class), any(ActionType.class), anyString(), anyString());
    }

    @Test
    @DisplayName("enable - 边界场景：配置不存在")
    void testEnable_ConfigNotFound() {
        // Given
        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList());

        // When
        MtFunctionPluginConf result = functionPluginConfLogicService.enable(testUser, TEST_API_NAME);

        // Then
        assertNull(result);
        verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
        verify(repository, never()).bulkUpdateByFields(
                eq(testUser),
                anyList(),
                anyList());
    }

    @Test
    @DisplayName("disable - 正常场景：成功禁用配置")
    void testDisable_Success() {
        // Given
        MtFunctionPluginConf disabledConfig = createValidTestConfig();
        disabledConfig.setIsActive(false);

        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList(testConfig));

        when(repository.bulkUpdateByFields(
                eq(testUser),
                anyList(),
                anyList()))
                .thenReturn(Lists.newArrayList(disabledConfig));

        doNothing().when(logService).log(any(User.class), any(EventType.class), any(ActionType.class), anyString(), anyString());

        // When
        MtFunctionPluginConf result = functionPluginConfLogicService.disable(testUser, TEST_API_NAME);

        // Then
        assertNotNull(result);
        assertFalse(result.getIsActive());
        verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
        verify(repository).bulkUpdateByFields(
                eq(testUser),
                anyList(),
                anyList());
        verify(logService).log(any(User.class), any(EventType.class), any(ActionType.class), anyString(), anyString());
    }

    @Test
    @DisplayName("disable - 边界场景：配置不存在")
    void testDisable_ConfigNotFound() {
        // Given
        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList());

        // When
        MtFunctionPluginConf result = functionPluginConfLogicService.disable(testUser, TEST_API_NAME);

        // Then
        assertNull(result);
        verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
        verify(repository, never()).bulkUpdateByFields(
                eq(testUser),
                anyList(),
                anyList());
    }

    // ===========================================
    // 8. delete方法测试
    // ===========================================

    @Test
    @DisplayName("delete - 正常场景：成功删除配置")
    void testDelete_Success() {
        // Given
        MtFunctionPluginConf disabledConfig = createValidTestConfig();
        disabledConfig.setIsActive(false); // 设置为禁用状态才能删除

        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList(disabledConfig));

        doNothing().when(functionLogicService).batchDeleteRelation(eq(testUser), anyList());
        doNothing().when(repository).bulkInvalidAndDelete(eq(testUser), anyList());
        doNothing().when(logService).log(any(User.class), any(EventType.class), any(ActionType.class), anyString(), anyString());

        // When
        assertDoesNotThrow(() -> {
            functionPluginConfLogicService.delete(testUser, TEST_API_NAME);
        });

        // Then
        verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
        verify(functionLogicService).batchDeleteRelation(eq(testUser), anyList());
        verify(repository).bulkInvalidAndDelete(eq(testUser), anyList());
        verify(logService).log(any(User.class), any(EventType.class), any(ActionType.class), anyString(), anyString());
    }

    @Test
    @DisplayName("delete - 异常场景：配置不存在")
    void testDelete_ConfigNotFound() {
        // Given
        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList());

        // When & Then
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            functionPluginConfLogicService.delete(testUser, TEST_API_NAME);
        });

        assertNotNull(exception);
        verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
        verify(repository, never()).bulkInvalidAndDelete(
                eq(testUser),
                anyList());
    }

    @Test
    @DisplayName("delete - 异常场景：启用状态不允许删除")
    void testDelete_EnabledStatusNotAllowDelete() {
        // Given
        MtFunctionPluginConf enabledConfig = createValidTestConfig();
        enabledConfig.setIsActive(true); // 启用状态

        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList(enabledConfig));

        // When & Then
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            functionPluginConfLogicService.delete(testUser, TEST_API_NAME);
        });

        assertNotNull(exception);
        verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
        verify(repository, never()).bulkInvalidAndDelete(
                eq(testUser),
                anyList());
    }

    // ===========================================
    // 9. deleteAll方法测试
    // ===========================================

    @Test
    @DisplayName("deleteAll - 正常场景：删除指定对象的所有配置")
    void testDeleteAll_Success() {
        // Given
        List<MtFunctionPluginConf> configs = Lists.newArrayList(testConfig);
        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(configs);

        doNothing().when(functionLogicService).batchDeleteRelation(eq(testUser), anyList());
        doNothing().when(repository).bulkInvalidAndDelete(eq(testUser), anyList());

        // When
        assertDoesNotThrow(() -> {
            functionPluginConfLogicService.deleteAll(testUser, TEST_REF_OBJ_API);
        });

        // Then
        verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
        verify(functionLogicService).batchDeleteRelation(eq(testUser), anyList());
        verify(repository).bulkInvalidAndDelete(eq(testUser), anyList());
    }

    @Test
    @DisplayName("deleteAll - 边界场景：租户ID为空")
    void testDeleteAll_EmptyTenantId() {
        // Given
        User emptyTenantUser = User.builder().tenantId("").userId("test-user").build();

        // When
        assertDoesNotThrow(() -> {
            functionPluginConfLogicService.deleteAll(emptyTenantUser, TEST_REF_OBJ_API);
        });

        // Then
        verify(repository, never()).findBy(
                any(User.class),
                any(Query.class),
                eq(MtFunctionPluginConf.class));
        verify(functionLogicService, never()).batchDeleteRelation(any(User.class), anyList());
        verify(repository, never()).bulkInvalidAndDelete(
                any(User.class),
                anyList());
    }

    @Test
    @DisplayName("deleteAll - 边界场景：没有找到配置")
    void testDeleteAll_NoConfigsFound() {
        // Given
        when(repository.findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList());

        // When
        assertDoesNotThrow(() -> {
            functionPluginConfLogicService.deleteAll(testUser, TEST_REF_OBJ_API);
        });

        // Then
        verify(repository).findBy(eq(testUser), any(Query.class), eq(MtFunctionPluginConf.class));
        verify(functionLogicService, never()).batchDeleteRelation(any(), anyList());
        verify(repository, never()).bulkInvalidAndDelete(any(), anyList());
    }

    // ===========================================
    // 10. brush方法测试
    // ===========================================

    @Test
    @DisplayName("brush - 正常场景：批量创建配置")
    void testBrush_Success() {
        // Given
        List<MtFunctionPluginConf> inputConfigs = Lists.newArrayList(createValidTestConfig());
        List<MtFunctionPluginConf> createdConfigs = Lists.newArrayList(createValidTestConfig());

        // Mock 查询现有数据（返回空，表示没有重复）
        when(repository.findBy(any(User.class), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList());

        // Mock 查询函数存在性
        when(functionLogicService.findFunctionByApiNames(any(User.class), anyList(), any()))
                .thenReturn(createTestFunctionList());

        // Mock 批量创建成功
        when(repository.bulkCreate(any(User.class), anyList()))
                .thenReturn(createdConfigs);

        // Mock 创建函数引用关系
        doNothing().when(functionLogicService).deleteAndCreateRelation(any(User.class), anyList());

        // When
        List<MtFunctionPluginConf> result = functionPluginConfLogicService.brush(TEST_TENANT_ID, inputConfigs);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(repository).findBy(any(User.class), any(Query.class), eq(MtFunctionPluginConf.class));
        verify(functionLogicService).findFunctionByApiNames(any(User.class), anyList(), any());
        verify(repository).bulkCreate(any(User.class), anyList());
        verify(functionLogicService).deleteAndCreateRelation(any(User.class), anyList());
    }

    @Test
    @DisplayName("brush - 异常场景：重复数据验证")
    void testBrush_DuplicateData() {
        // Given - 创建两个具有相同API名称的配置来测试重复检测
        MtFunctionPluginConf config1 = createValidTestConfig();
        MtFunctionPluginConf config2 = createValidTestConfig(); // 相同的API名称
        List<MtFunctionPluginConf> inputConfigs = Lists.newArrayList(config1, config2);

        // When & Then
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            functionPluginConfLogicService.brush(TEST_TENANT_ID, inputConfigs);
        });

        assertNotNull(exception);
        // 验证异常消息包含重复相关信息（根据实际实现调整）
        verify(repository, never()).bulkCreate(any(), any());
    }

    @Test
    @DisplayName("brush - 异常场景：函数不存在")
    void testBrush_FunctionNotExists() {
        // Given
        List<MtFunctionPluginConf> inputConfigs = Lists.newArrayList(createValidTestConfig());

        // Mock 查询现有数据（返回空，表示没有重复）
        when(repository.findBy(any(User.class), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList());

        // Mock 函数不存在
        when(functionLogicService.findFunctionByApiNames(any(User.class), anyList(), any()))
                .thenReturn(Lists.newArrayList()); // 返回空列表表示函数不存在

        // When
        List<MtFunctionPluginConf> result = functionPluginConfLogicService.brush(TEST_TENANT_ID, inputConfigs);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty()); // 因为函数不存在，所以结果为空
        verify(repository).findBy(any(User.class), any(Query.class), eq(MtFunctionPluginConf.class));
        verify(functionLogicService).findFunctionByApiNames(any(User.class), anyList(), any());
        verify(repository, never()).bulkCreate(any(), any());
    }

    @Test
    @DisplayName("brush - 边界场景：空数据处理")
    void testBrush_EmptyData() {
        // When
        List<MtFunctionPluginConf> result1 = functionPluginConfLogicService.brush("", Lists.newArrayList(createValidTestConfig()));
        List<MtFunctionPluginConf> result2 = functionPluginConfLogicService.brush(TEST_TENANT_ID, Lists.newArrayList());
        List<MtFunctionPluginConf> result3 = functionPluginConfLogicService.brush(TEST_TENANT_ID, null);

        // Then
        assertTrue(result1.isEmpty());
        assertTrue(result2.isEmpty());
        assertTrue(result3.isEmpty());
        verify(repository, never()).bulkCreate(any(), any());
    }

    @Test
    @DisplayName("brush - 边界场景：过滤已存在的数据")
    void testBrush_FilterExistingData() {
        // Given
        MtFunctionPluginConf validConfig = createValidTestConfig();
        List<MtFunctionPluginConf> inputConfigs = Lists.newArrayList(validConfig);

        // Mock 查询现有数据（返回相同的配置，表示已存在）
        when(repository.findBy(any(User.class), any(Query.class), eq(MtFunctionPluginConf.class)))
                .thenReturn(Lists.newArrayList(validConfig));

        // When
        List<MtFunctionPluginConf> result = functionPluginConfLogicService.brush(TEST_TENANT_ID, inputConfigs);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty()); // 因为数据已存在，被过滤掉了
        verify(repository).findBy(any(User.class), any(Query.class), eq(MtFunctionPluginConf.class));
        verify(repository, never()).bulkCreate(any(), any());
    }

    @Test
    @DisplayName("brush - 异常场景：数据验证失败")
    void testBrush_ValidationFailed() {
        // Given
        MtFunctionPluginConf invalidConfig = createValidTestConfig();
        invalidConfig.setPluginProvider("INVALID_PROVIDER"); // 无效的提供者
        List<MtFunctionPluginConf> inputConfigs = Lists.newArrayList(invalidConfig);

        // When & Then
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            functionPluginConfLogicService.brush(TEST_TENANT_ID, inputConfigs);
        });

        assertNotNull(exception);
        verify(repository, never()).bulkCreate(any(), any());
    }

    /**
     * 创建测试用的IUdefFunction对象列表
     */
    private List<IUdefFunction> createTestFunctionList() {
        // 由于IUdefFunction是接口，我们返回Object类型的列表来模拟
        List<IUdefFunction> functions = new ArrayList<>();
        // 创建一个匿名对象来模拟IUdefFunction，包含getApiName方法
        IUdefFunction function = new UdefFunction();
        function.setApiName(TEST_FUNCTION_API);
        functions.add(function);
        return functions;
    }
}