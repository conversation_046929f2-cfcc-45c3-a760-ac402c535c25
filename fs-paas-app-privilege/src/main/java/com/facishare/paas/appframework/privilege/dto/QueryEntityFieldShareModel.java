package com.facishare.paas.appframework.privilege.dto;


import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by linqy on 2018/01/19.
 */
public class QueryEntityFieldShareModel {
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Arg extends BasePrivilegeArg {
        private List<String> ruleCodes;
        private List<String> receives;
        private Map<String, List<String>> receivesWithType;
        private String entityId;
        private Set<String> entityIds;
        private String ruleName;
        private Integer status;
        private Integer permission;
        private BasePageInfoDataPrivilege pageInfo;
        // 0代表所有对象
        private Integer entityType; // 2代表老对象，其他为自定义对象
        private Boolean outReceive;//null 不区分内外部  true 只查询共享給外部的  false 只查询共享給内部的
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Result extends BasePrivilegeResult {
        private QueryFieldShareResult result;

        @Data
        public static class QueryFieldShareResult {
            private PageInfo page;
            private List<FieldShareRule> content;
        }
    }
}
