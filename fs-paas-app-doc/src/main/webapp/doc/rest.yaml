openapi: 3.0.0
info:
  title: AppFramework Rest API
  description: AppFramework 提供内部使用的REST接口
  version: 1.1.0
  contact:
    name: YuanXiaolong
    email: <EMAIL>

servers:
- url: https://www.ceshi113.com/API/v1/inner/rest
  description: 测试113环境。
- url: https://www.ceshi112.com/API/v1/inner/rest
  description: 测试112环境。
paths:
  /object_data/{describeAPIName}:
    post:
      tags:
      - 对象数据接口
      summary: 创建数据
      description: 根据数据json创建数据
      parameters:
      - $ref: '#/components/parameters/tenantId'
      - $ref: '#/components/parameters/user'
      - name: describeAPIName
        in: path
        description: 对象描述名称
        required: true
        schema:
          type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                  data:
                    type: object
                    properties:
                      describe:
                        type: object
                      object_data:
                        type: object
                  message:
                    type: string
  /object_data/batch_create/{describeAPIName}:
    post:
      tags:
      - 对象数据接口
      summary: 批量创建数据
      description: 根据数据json批量创建数据
      parameters:
      - $ref: '#/components/parameters/tenantId'
      - $ref: '#/components/parameters/user'
      - name: describeAPIName
        in: path
        description: 对象描述名称
        required: true
        schema:
          type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                  data:
                    type: object
                    properties:
                      api_name:
                        type: string
                      data_list:
                        type: array
                        items:
                          type: object
                  message:
                    type: string
  /object_data/batch_update/{apiName}:
    post:
      tags:
      - 对象数据接口
      summary: 批量更新数据
      description: 根据数据json批量更新数据
      parameters:
      - $ref: '#/components/parameters/tenantId'
      - $ref: '#/components/parameters/user'
      - name: apiName
        in: path
        description: 对象描述名称
        required: true
        schema:
          type: string
      - name: ruleName
        in: query
        description: 达芬奇规则名称
        required: false
        schema:
          type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                  data:
                    type: object
                    properties:
                      result:
                        type: string
                  message:
                    type: string
components:
  parameters:
    tenantId:
      name: x-fs-ei
      in: header
      required: true
      schema:
        type: string
    user:
      name: x-fs-userInfo
      in: header
      required: true
      schema:
        type: string