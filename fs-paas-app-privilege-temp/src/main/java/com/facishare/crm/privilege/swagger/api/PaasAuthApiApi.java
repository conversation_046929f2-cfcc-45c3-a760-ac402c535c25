/**
 * Facishare PAAS Auth
 * This is PAAS auth service project
 *
 * OpenAPI spec version: v1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


package com.facishare.crm.privilege.swagger.api;

import com.facishare.crm.privilege.swagger.ApiCallback;
import com.facishare.crm.privilege.swagger.ApiClient;
import com.facishare.crm.privilege.swagger.ApiException;
import com.facishare.crm.privilege.swagger.ApiResponse;
import com.facishare.crm.privilege.swagger.Configuration;
import com.facishare.crm.privilege.swagger.Pair;
import com.facishare.crm.privilege.swagger.ProgressRequestBody;
import com.facishare.crm.privilege.swagger.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;

import com.facishare.crm.privilege.swagger.model.AddRoleRecordTypeArg;
import com.facishare.crm.privilege.swagger.model.AddRoleUserArg;
import com.facishare.crm.privilege.swagger.model.BaseResult;
import com.facishare.crm.privilege.swagger.model.AddRoleViewArg;
import com.facishare.crm.privilege.swagger.model.BatchDelFuncArg;
import com.facishare.crm.privilege.swagger.model.BatchUpdateUserRoleArg;
import com.facishare.crm.privilege.swagger.model.CheckRecordTypeArg;
import com.facishare.crm.privilege.swagger.model.CheckRoleUserArg;
import com.facishare.crm.privilege.swagger.model.CheckDefaultRoleResponse;
import com.facishare.crm.privilege.swagger.model.DelRoleArg;
import com.facishare.crm.privilege.swagger.model.DelUserFromRoleArg;
import com.facishare.crm.privilege.swagger.model.QueryRecordTypeResponse;
import com.facishare.crm.privilege.swagger.model.FindRecordTypeArg;
import com.facishare.crm.privilege.swagger.model.FindViewArg;
import com.facishare.crm.privilege.swagger.model.QueryRoleViewResponse;
import com.facishare.crm.privilege.swagger.model.QueryUserRolesByUsersArg;
import com.facishare.crm.privilege.swagger.model.QueryRoleInfoListByUsersResponse;
import com.facishare.crm.privilege.swagger.model.RoleInfoArg;
import com.facishare.crm.privilege.swagger.model.RoleResponse;
import com.facishare.crm.privilege.swagger.model.UpdateRoleRecordTypeArg;
import com.facishare.crm.privilege.swagger.model.UpdateRoleUserArg;
import com.facishare.crm.privilege.swagger.model.UpdateUserDefaultRoleArg;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PaasAuthApiApi {
    private ApiClient apiClient;

    public PaasAuthApiApi() {
        this(Configuration.getDefaultApiClient());
    }

    public PaasAuthApiApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /* Build call for addRoleRecordType */
    private okhttp3.Call addRoleRecordTypeCall(AddRoleRecordTypeArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/addRoleRecordType".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 添加角色与业务类型的关系
     * 支持批量
     * @param body  (optional)
     * @return Object
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public Object addRoleRecordType(AddRoleRecordTypeArg body) throws ApiException {
        ApiResponse<Object> resp = addRoleRecordTypeWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 添加角色与业务类型的关系
     * 支持批量
     * @param body  (optional)
     * @return ApiResponse&lt;Object&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<Object> addRoleRecordTypeWithHttpInfo(AddRoleRecordTypeArg body) throws ApiException {
        okhttp3.Call call = addRoleRecordTypeCall(body, null, null);
        Type localVarReturnType = new TypeToken<Object>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 添加角色与业务类型的关系 (asynchronously)
     * 支持批量
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call addRoleRecordTypeAsync(AddRoleRecordTypeArg body, final ApiCallback<Object> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = addRoleRecordTypeCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<Object>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for addRoleToUser */
    private okhttp3.Call addRoleToUserCall(AddRoleUserArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/defaultRole/addRoleToUser".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 批量给用户添加角色
     * 当角色列表不为空时，需要指定主角色
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult addRoleToUser(AddRoleUserArg body) throws ApiException {
        ApiResponse<BaseResult> resp = addRoleToUserWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 批量给用户添加角色
     * 当角色列表不为空时，需要指定主角色
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> addRoleToUserWithHttpInfo(AddRoleUserArg body) throws ApiException {
        okhttp3.Call call = addRoleToUserCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 批量给用户添加角色 (asynchronously)
     * 当角色列表不为空时，需要指定主角色
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call addRoleToUserAsync(AddRoleUserArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = addRoleToUserCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for addRoleView */
    private okhttp3.Call addRoleViewCall(AddRoleViewArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/addRoleView".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 新增视图权限
     * 支持批量
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult addRoleView(AddRoleViewArg body) throws ApiException {
        ApiResponse<BaseResult> resp = addRoleViewWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 新增视图权限
     * 支持批量
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> addRoleViewWithHttpInfo(AddRoleViewArg body) throws ApiException {
        okhttp3.Call call = addRoleViewCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 新增视图权限 (asynchronously)
     * 支持批量
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call addRoleViewAsync(AddRoleViewArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = addRoleViewCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for batchDelFunc */
    private okhttp3.Call batchDelFuncCall(BatchDelFuncArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/batchDelFunc".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 功能删除
     * 功能删除
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult batchDelFunc(BatchDelFuncArg body) throws ApiException {
        ApiResponse<BaseResult> resp = batchDelFuncWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 功能删除
     * 功能删除
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> batchDelFuncWithHttpInfo(BatchDelFuncArg body) throws ApiException {
        okhttp3.Call call = batchDelFuncCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 功能删除 (asynchronously)
     * 功能删除
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call batchDelFuncAsync(BatchDelFuncArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = batchDelFuncCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for batchUpdateUserRole */
    private okhttp3.Call batchUpdateUserRoleCall(BatchUpdateUserRoleArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/defaultRole/batchUpdateUserRole".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 批量设置用户各自绑定的角色（包括主角色）
     * 
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult batchUpdateUserRole(BatchUpdateUserRoleArg body) throws ApiException {
        ApiResponse<BaseResult> resp = batchUpdateUserRoleWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 批量设置用户各自绑定的角色（包括主角色）
     * 
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> batchUpdateUserRoleWithHttpInfo(BatchUpdateUserRoleArg body) throws ApiException {
        okhttp3.Call call = batchUpdateUserRoleCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 批量设置用户各自绑定的角色（包括主角色） (asynchronously)
     * 
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call batchUpdateUserRoleAsync(BatchUpdateUserRoleArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = batchUpdateUserRoleCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for checkRecordType */
    private okhttp3.Call checkRecordTypeCall(CheckRecordTypeArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/checkRecordType".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 检查是否存在使用指定对象类型作为默认类型的角色
     * 查询用户默认角色引用的对象类型
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult checkRecordType(CheckRecordTypeArg body) throws ApiException {
        ApiResponse<BaseResult> resp = checkRecordTypeWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 检查是否存在使用指定对象类型作为默认类型的角色
     * 查询用户默认角色引用的对象类型
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> checkRecordTypeWithHttpInfo(CheckRecordTypeArg body) throws ApiException {
        okhttp3.Call call = checkRecordTypeCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 检查是否存在使用指定对象类型作为默认类型的角色 (asynchronously)
     * 查询用户默认角色引用的对象类型
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call checkRecordTypeAsync(CheckRecordTypeArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = checkRecordTypeCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for checkRoleUser */
    private okhttp3.Call checkRoleUserCall(CheckRoleUserArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/defaultRole/checkRoleUser".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 校验是否有user把当前角色设为主角色，而且有其他角色
     * 返回这样的user列表
     * @param body  (optional)
     * @return CheckDefaultRoleResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public CheckDefaultRoleResponse checkRoleUser(CheckRoleUserArg body) throws ApiException {
        ApiResponse<CheckDefaultRoleResponse> resp = checkRoleUserWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 校验是否有user把当前角色设为主角色，而且有其他角色
     * 返回这样的user列表
     * @param body  (optional)
     * @return ApiResponse&lt;CheckDefaultRoleResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<CheckDefaultRoleResponse> checkRoleUserWithHttpInfo(CheckRoleUserArg body) throws ApiException {
        okhttp3.Call call = checkRoleUserCall(body, null, null);
        Type localVarReturnType = new TypeToken<CheckDefaultRoleResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 校验是否有user把当前角色设为主角色，而且有其他角色 (asynchronously)
     * 返回这样的user列表
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call checkRoleUserAsync(CheckRoleUserArg body, final ApiCallback<CheckDefaultRoleResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = checkRoleUserCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<CheckDefaultRoleResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for delDefinedRole */
    private okhttp3.Call delDefinedRoleCall(DelRoleArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/defaultRole/delDefinedRole".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 删除自定义角色
     * 校验是否有user把当前角色设为主角色，而且有其他角色
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult delDefinedRole(DelRoleArg body) throws ApiException {
        ApiResponse<BaseResult> resp = delDefinedRoleWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 删除自定义角色
     * 校验是否有user把当前角色设为主角色，而且有其他角色
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> delDefinedRoleWithHttpInfo(DelRoleArg body) throws ApiException {
        okhttp3.Call call = delDefinedRoleCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 删除自定义角色 (asynchronously)
     * 校验是否有user把当前角色设为主角色，而且有其他角色
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call delDefinedRoleAsync(DelRoleArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = delDefinedRoleCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for delRoleUser */
    private okhttp3.Call delRoleUserCall(DelUserFromRoleArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/defaultRole/delRoleUser".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 删除角色下的多个user
     * 当user把当前角色设为主角色，而且有其他角色时，逻辑中断
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult delRoleUser(DelUserFromRoleArg body) throws ApiException {
        ApiResponse<BaseResult> resp = delRoleUserWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 删除角色下的多个user
     * 当user把当前角色设为主角色，而且有其他角色时，逻辑中断
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> delRoleUserWithHttpInfo(DelUserFromRoleArg body) throws ApiException {
        okhttp3.Call call = delRoleUserCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 删除角色下的多个user (asynchronously)
     * 当user把当前角色设为主角色，而且有其他角色时，逻辑中断
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call delRoleUserAsync(DelUserFromRoleArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = delRoleUserCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for findRecordType */
    private okhttp3.Call findRecordTypeCall(FindRecordTypeArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/findRecordType".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 查询对象类型
     * 查询对象类型
     * @param body  (optional)
     * @return QueryRecordTypeResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public QueryRecordTypeResponse findRecordType(FindRecordTypeArg body) throws ApiException {
        ApiResponse<QueryRecordTypeResponse> resp = findRecordTypeWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 查询对象类型
     * 查询对象类型
     * @param body  (optional)
     * @return ApiResponse&lt;QueryRecordTypeResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<QueryRecordTypeResponse> findRecordTypeWithHttpInfo(FindRecordTypeArg body) throws ApiException {
        okhttp3.Call call = findRecordTypeCall(body, null, null);
        Type localVarReturnType = new TypeToken<QueryRecordTypeResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 查询对象类型 (asynchronously)
     * 查询对象类型
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call findRecordTypeAsync(FindRecordTypeArg body, final ApiCallback<QueryRecordTypeResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = findRecordTypeCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<QueryRecordTypeResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for findView */
    private okhttp3.Call findViewCall(FindViewArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/findView".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 查询视图
     * 查询视图
     * @param body  (optional)
     * @return QueryRoleViewResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public QueryRoleViewResponse findView(FindViewArg body) throws ApiException {
        ApiResponse<QueryRoleViewResponse> resp = findViewWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 查询视图
     * 查询视图
     * @param body  (optional)
     * @return ApiResponse&lt;QueryRoleViewResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<QueryRoleViewResponse> findViewWithHttpInfo(FindViewArg body) throws ApiException {
        okhttp3.Call call = findViewCall(body, null, null);
        Type localVarReturnType = new TypeToken<QueryRoleViewResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 查询视图 (asynchronously)
     * 查询视图
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call findViewAsync(FindViewArg body, final ApiCallback<QueryRoleViewResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = findViewCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<QueryRoleViewResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryRoleInfoListByUsers */
    private okhttp3.Call queryRoleInfoListByUsersCall(QueryUserRolesByUsersArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/defaultRole/queryRoleInfoListByUsers".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 查询指定条件的用户角色关联
     * 
     * @param body  (optional)
     * @return QueryRoleInfoListByUsersResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public QueryRoleInfoListByUsersResponse queryRoleInfoListByUsers(QueryUserRolesByUsersArg body) throws ApiException {
        ApiResponse<QueryRoleInfoListByUsersResponse> resp = queryRoleInfoListByUsersWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 查询指定条件的用户角色关联
     * 
     * @param body  (optional)
     * @return ApiResponse&lt;QueryRoleInfoListByUsersResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<QueryRoleInfoListByUsersResponse> queryRoleInfoListByUsersWithHttpInfo(QueryUserRolesByUsersArg body) throws ApiException {
        okhttp3.Call call = queryRoleInfoListByUsersCall(body, null, null);
        Type localVarReturnType = new TypeToken<QueryRoleInfoListByUsersResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 查询指定条件的用户角色关联 (asynchronously)
     * 
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryRoleInfoListByUsersAsync(QueryUserRolesByUsersArg body, final ApiCallback<QueryRoleInfoListByUsersResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryRoleInfoListByUsersCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<QueryRoleInfoListByUsersResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for roleInfo */
    private okhttp3.Call roleInfoCall(RoleInfoArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/roleInfo".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 角色查询
     * 角色查询
     * @param body  (optional)
     * @return RoleResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public RoleResponse roleInfo(RoleInfoArg body) throws ApiException {
        ApiResponse<RoleResponse> resp = roleInfoWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 角色查询
     * 角色查询
     * @param body  (optional)
     * @return ApiResponse&lt;RoleResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<RoleResponse> roleInfoWithHttpInfo(RoleInfoArg body) throws ApiException {
        okhttp3.Call call = roleInfoCall(body, null, null);
        Type localVarReturnType = new TypeToken<RoleResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 角色查询 (asynchronously)
     * 角色查询
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call roleInfoAsync(RoleInfoArg body, final ApiCallback<RoleResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = roleInfoCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<RoleResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for updateRoleRecordType */
    private okhttp3.Call updateRoleRecordTypeCall(UpdateRoleRecordTypeArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/updateRoleRecordType".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 更新角色与业务类型的关系
     * 支持批量
     * @param body  (optional)
     * @return Object
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public Object updateRoleRecordType(UpdateRoleRecordTypeArg body) throws ApiException {
        ApiResponse<Object> resp = updateRoleRecordTypeWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 更新角色与业务类型的关系
     * 支持批量
     * @param body  (optional)
     * @return ApiResponse&lt;Object&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<Object> updateRoleRecordTypeWithHttpInfo(UpdateRoleRecordTypeArg body) throws ApiException {
        okhttp3.Call call = updateRoleRecordTypeCall(body, null, null);
        Type localVarReturnType = new TypeToken<Object>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 更新角色与业务类型的关系 (asynchronously)
     * 支持批量
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call updateRoleRecordTypeAsync(UpdateRoleRecordTypeArg body, final ApiCallback<Object> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = updateRoleRecordTypeCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<Object>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for updateRoleToUser */
    private okhttp3.Call updateRoleToUserCall(UpdateRoleUserArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/defaultRole/updateRoleToUser".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 更新用户绑定的角色
     * 当角色列表不为空时，需要指定主角色
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult updateRoleToUser(UpdateRoleUserArg body) throws ApiException {
        ApiResponse<BaseResult> resp = updateRoleToUserWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 更新用户绑定的角色
     * 当角色列表不为空时，需要指定主角色
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> updateRoleToUserWithHttpInfo(UpdateRoleUserArg body) throws ApiException {
        okhttp3.Call call = updateRoleToUserCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 更新用户绑定的角色 (asynchronously)
     * 当角色列表不为空时，需要指定主角色
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call updateRoleToUserAsync(UpdateRoleUserArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = updateRoleToUserCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for updateUserDefaultRole */
    private okhttp3.Call updateUserDefaultRoleCall(UpdateUserDefaultRoleArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/defaultRole/updateUserDefaultRole".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 把当前角色设置为指定用户的主角色
     * 
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult updateUserDefaultRole(UpdateUserDefaultRoleArg body) throws ApiException {
        ApiResponse<BaseResult> resp = updateUserDefaultRoleWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 把当前角色设置为指定用户的主角色
     * 
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> updateUserDefaultRoleWithHttpInfo(UpdateUserDefaultRoleArg body) throws ApiException {
        okhttp3.Call call = updateUserDefaultRoleCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 把当前角色设置为指定用户的主角色 (asynchronously)
     * 
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call updateUserDefaultRoleAsync(UpdateUserDefaultRoleArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = updateUserDefaultRoleCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
}
