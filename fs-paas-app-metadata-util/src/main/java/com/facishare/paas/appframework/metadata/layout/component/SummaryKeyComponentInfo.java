package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.component.AbstractComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.bson.Document;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class SummaryKeyComponentInfo extends AbstractComponent implements ISummaryKeyComponentInfo {

    private static final String SUMMARY_INFO = "summary_info";

    public SummaryKeyComponentInfo() {
        super();
        initListField(BUTTONS);
        set(TYPE, SUMMARY_INFO);
    }

    public SummaryKeyComponentInfo(Map map) {
        super(map);
        initListField(BUTTONS);
        set(TYPE, SUMMARY_INFO);
    }

    public void removeFields(Set<String> removeFieldNames) {
        if (CollectionUtils.empty(removeFieldNames)) {
            return;
        }
        List<IFormField> fieldSections = getFieldSections();
        fieldSections.removeIf(x -> removeFieldNames.contains(x.getFieldName()));
        setFieldSections(fieldSections);
    }
    @Override
    public void onFieldDelete(IFieldDescribe deletedField)  {
        if (ObjectUtils.isEmpty(deletedField)) {
            return;
        }

        List<IFormField> fieldSections = getFieldSections();
        fieldSections.removeIf(x -> x.getFieldName().equals(deletedField.getApiName()));
        setFieldSections(fieldSections);
    }

    @Override
    public void onFieldUpdate(String fieldName, Map field)  {
        Object list = map.get(FIELD_SECTION);
        if (list instanceof List) {
            for (int i = 0; i < ((List) list).size(); i++) {
                FormField formField = new FormField((Map) ((List) list).get(i));
                if (formField.getFieldName() != null && formField.getFieldName().equals(fieldName)) {
                    ((List) list).set(i, field);
                    break;
                }
            }
        }
    }

    @Override
    public List<IFormField> getFieldSections() {
        List<Map> list = (List) map.get(FIELD_SECTION);
        if (null == list) {
            return Lists.newArrayList();
        }
        return list.stream().map(FormField::new).collect(Collectors.toList());
    }

    @Override
    public void setFieldSections(List<IFormField> fieldSections) {
        this.set(FIELD_SECTION, new ArrayList());
        fieldSections.forEach(this::addFieldSection);
    }

    public void addFieldSection(IFormField fieldSection) {
        if (null == map.get(FIELD_SECTION)) {
            map.put(FIELD_SECTION, new ArrayList());
        }
        Object list = map.get(FIELD_SECTION);
        if (list instanceof List) {
            ((List) list).add(Document.parse(fieldSection.toJsonString()));
        }
    }
    public static SummaryKeyComponentInfo of(IComponent component) {
        return new SummaryKeyComponentInfo(ComponentExt.of(component).toMap());
    }

    public boolean containsField(String fieldApiName) {
        return getFieldSections().stream().anyMatch(x -> fieldApiName.equals(x.getFieldName()));
    }
}
