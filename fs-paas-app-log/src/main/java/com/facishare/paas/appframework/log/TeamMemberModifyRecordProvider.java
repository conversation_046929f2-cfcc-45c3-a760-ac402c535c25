package com.facishare.paas.appframework.log;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import com.facishare.paas.appframework.log.dto.TeamMemberInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

@Component("teamMemberModifyRecordProvider")
public class TeamMemberModifyRecordProvider extends DefaultModifyRecordProvider {
    @Override
    public String getApiName() {
        return "TeamMember";
    }

    @Override
    public ModifyRecord getModifyRecord(LogInfo logInfo) {
        ModifyRecord modifyRecord = super.getModifyRecord(logInfo);
        modifyRecord.setMsgList(parseTeamMemberMsgs(logInfo.getSnapshot().getMsgs()));
        return modifyRecord;
    }

    protected List<TeamMemberInfo.Msg> parseTeamMemberMsgs(List<TeamMemberInfo.Msg> msgs) {
        CollectionUtils.nullToEmpty(msgs)
                .forEach(x -> {
                    x.setOperationLabel(ActionType.getNameById(x.getOperationType()));
                    if (Objects.nonNull(x.getMsgMap()) && Objects.nonNull(x.getMsgMap().getOperation())) {
                        x.getMsgMap().setOperation(I18N.text(x.getMsgMap().getOperation()));
                    }
                    handleTeamMemberLang(x);
                });
        return msgs;
    }

    protected final void handleTeamMemberLang(TeamMemberInfo.Msg msg) {
        TeamMemberInfo.MsgMap msgMap = msg.getMsgMap();
        if (Objects.nonNull(msgMap)) {
            TeamMemberInfo.Info permission = msgMap.getPermission();
            if (Objects.nonNull(permission)) {
                permission.setLabel(permission.convertToI18NLabel());
                permission.setOldValue(permission.convertTo18NOldValue());
                permission.setValue(permission.convertToI18NValue());
            }
            TeamMemberInfo.Info role = msgMap.getRole();
            if (Objects.nonNull(role)) {
                role.setLabel(role.convertToI18NLabel());
                role.setOldValue(role.convertTo18NOldValue());
                role.setValue(role.convertToI18NValue());
            }
        }
    }

    protected final TeamMemberInfo.Msg getTeamMemberMsg(ActionType actionType,
                                                        String oldUserId,
                                                        String userId,
                                                        List<TeamMemberInfo.Member> teamMemberInfos,
                                                        List<String> oldRoleKeyList,
                                                        List<String> roleKeyList,
                                                        String oldPermissionKey,
                                                        String permissionKey,
                                                        String operation,
                                                        String teamMemberType) {
        return TeamMemberInfo.Msg.builder()
                .operationType(actionType.getId())
                .member(TeamMemberInfo.Member.builder()
                        .id(userId)
                        .name(getMemberName(teamMemberInfos, userId))
                        .oldId(oldUserId)
                        .oldName(getMemberName(teamMemberInfos, oldUserId))
                        .build())
                .msgMap(getMsgMap(oldRoleKeyList, roleKeyList, oldPermissionKey, permissionKey, operation, teamMemberType))
                .build();
    }

    private TeamMemberInfo.MsgMap getMsgMap(List<String> oldRoleKeyList,
                                            List<String> roleKeyList,
                                            String oldPermissionKey,
                                            String permissionKey,
                                            String operation,
                                            String teamMemberType) {

        TeamMemberInfo.MsgMap msgMap = TeamMemberInfo.MsgMap.builder().build();

        if (!StringUtils.equals(oldPermissionKey, permissionKey)) {
            msgMap.setPermission(buildPermission(oldPermissionKey, permissionKey));
        }

        if (!CollectionUtils.isEqual(roleKeyList, oldRoleKeyList)) {

            StringBuilder defaultOldRoleValue = new StringBuilder();
            StringBuilder oldRoleKey = new StringBuilder();
            Map<String, String> defaultOldRoleParameterValues = Maps.newHashMap();
            if (CollectionUtils.notEmpty(oldRoleKeyList)) {
                for (int i = 0; i < oldRoleKeyList.size(); i++) {
                    oldRoleKey.append("{").append(i).append("}").append(",");
                    defaultOldRoleValue.append(I18N.text(oldRoleKeyList.get(i))).append(",");
                    defaultOldRoleParameterValues.put(oldRoleKeyList.get(i), I18N.text(oldRoleKeyList.get(i)));
                }
            }

            StringBuilder defaultRoleValue = new StringBuilder();
            StringBuilder roleKey = new StringBuilder();
            Map<String, String> defaultRoleParameterValues = Maps.newHashMap();
            if (CollectionUtils.notEmpty(roleKeyList)) {
                for (int i = 0; i < roleKeyList.size(); i++) {
                    roleKey.append("{").append(i).append("}").append(",");
                    defaultRoleValue.append(I18N.text(roleKeyList.get(i))).append(",");
                    defaultRoleParameterValues.put(roleKeyList.get(i), I18N.text(roleKeyList.get(i)));
                }
            }

            msgMap.setRole(TeamMemberInfo.Info.builder()
                    .label(I18N.text(I18NKey.TEAM_ROLE))
                    .internationalLabel(InternationalItem.builder()
                            .internationalKey(I18NKey.TEAM_ROLE)
                            .defaultInternationalValue(I18N.text(I18NKey.TEAM_ROLE))
                            .build())
                    .oldValue(defaultOldRoleValue.toString())
                    .internationalOldValue(InternationalItem.builder()
                            .internationalKey(oldRoleKey.toString())
                            .defaultInternationalValue(defaultOldRoleValue.toString())
                            .internationalParameters(oldRoleKeyList)
                            .defaultParameterValues(defaultOldRoleParameterValues)
                            .build())
                    .value(defaultRoleValue.toString())
                    .internationalValue(InternationalItem.builder()
                            .internationalKey(roleKey.toString())
                            .defaultInternationalValue(defaultRoleValue.toString())
                            .internationalParameters(roleKeyList)
                            .defaultParameterValues(defaultRoleParameterValues)
                            .build())
                    .build());
        }

        if (StringUtils.isNotEmpty(operation)) {
            msgMap.setOperation(operation);
        }
        if (StringUtils.isNotEmpty(teamMemberType)) {
            msgMap.setMemberType(teamMemberType);
        }
        return msgMap;
    }

    protected String getUserName(List<UserInfo> userInfos, String userId) {
        Optional<UserInfo> info = userInfos.stream().filter(n -> n.getId().equals(userId)).findFirst();
        String userName = "";
        if (info.isPresent()) {
            userName = info.get().getName();
        }
        return userName;
    }

    protected final String getMemberName(List<TeamMemberInfo.Member> memberInfos, String memberId) {
        String userName = "";
        if (CollectionUtils.empty(memberInfos)) {
            return userName;
        }
        Optional<TeamMemberInfo.Member> info = memberInfos.stream().filter(n -> n.getId().equals(memberId)).findFirst();
        if (info.isPresent()) {
            userName = info.get().getName();
        }
        return userName;
    }

    protected final TeamMemberInfo.Info buildPermission(String oldPermissionKey, String permissionKey) {
        return TeamMemberInfo.Info.builder()
                .label(I18N.text(I18NKey.PRIVILEGE))
                .internationalLabel(InternationalItem.builder()
                        .internationalKey(I18NKey.PRIVILEGE)
                        .defaultInternationalValue(I18N.text(I18NKey.PRIVILEGE))
                        .build())
                .oldValue(I18N.text(oldPermissionKey))
                .internationalOldValue(InternationalItem.builder()
                        .internationalKey(oldPermissionKey)
                        .defaultInternationalValue(I18N.text(oldPermissionKey))
                        .build())
                .value(I18N.text(permissionKey))
                .internationalValue(InternationalItem.builder()
                        .internationalKey(permissionKey)
                        .defaultInternationalValue(I18N.text(permissionKey))
                        .build())
                .build();
    }


    protected final TeamMemberInfo.Msg getTeamMemberInfoMsg(ActionType actionType,
                                                            String userId,
                                                            String oldUserId,
                                                            List<TeamMemberInfo.Member> teamMemberInfos,
                                                            List<String> newRoleKeyList,
                                                            String oldRoleKey,
                                                            String newPermissionKey,
                                                            String oldPermissionKey,
                                                            String operation) {
        if (StringUtils.isBlank(oldUserId)) {
            operation = "";
        }
        StringBuilder defaultNewRoleValue = new StringBuilder();
        StringBuilder newRoleKey = new StringBuilder();
        Map<String, String> defaultParameterValues = Maps.newHashMap();
        if (CollectionUtils.notEmpty(newRoleKeyList)) {
            for (int i = 0; i < newRoleKeyList.size(); i++) {
                newRoleKey.append("{").append(i).append("}").append(",");
                defaultNewRoleValue.append(I18N.text(newRoleKeyList.get(i))).append(",");
                defaultParameterValues.put(newRoleKeyList.get(i), I18N.text(newRoleKeyList.get(i)));
            }
        }
        return TeamMemberInfo.Msg.builder()
                .operationType(actionType.getId())
                .member(TeamMemberInfo.Member.builder()
                        .id(userId)
                        .name(getMemberName(teamMemberInfos, userId))
                        .oldId(oldUserId)
                        .oldName(getMemberName(teamMemberInfos, oldUserId))
                        .build())
                .msgMap(TeamMemberInfo.MsgMap.builder()
                        .permission(buildPermission(oldPermissionKey, newPermissionKey))
                        .role(TeamMemberInfo.Info.builder()
                                .label(I18N.text(I18NKey.TEAM_ROLE))
                                .internationalLabel(InternationalItem.builder()
                                        .internationalKey(I18NKey.TEAM_ROLE)
                                        .defaultInternationalValue(I18N.text(I18NKey.TEAM_ROLE))
                                        .build())
                                .value(defaultNewRoleValue.toString())
                                .internationalValue(InternationalItem.builder()
                                        .internationalKey(newRoleKey.toString())
                                        .defaultInternationalValue(defaultNewRoleValue.toString())
                                        .internationalParameters(newRoleKeyList)
                                        .defaultParameterValues(defaultParameterValues)
                                        .build())
                                .oldValue(I18N.text(oldRoleKey))
                                .internationalOldValue(InternationalItem.builder()
                                        .internationalKey(oldRoleKey)
                                        .defaultInternationalValue(I18N.text(oldRoleKey))
                                        .build())
                                .build())
                        .operation(operation)
                        .build())
                .build();
    }

    protected final String getRoleCustomKey(Set<String> roleKeyList, String prefix, String suffix) {
        StringBuilder roleKeyBuilder = new StringBuilder();
        roleKeyBuilder.append(prefix);
        if (CollectionUtils.notEmpty(roleKeyList)) {
            for (int i = 0; i < Lists.newArrayList(roleKeyList).size(); i++) {
                roleKeyBuilder.append("{").append(i).append("}").append(",");
            }
        }
        roleKeyBuilder.append(suffix);
        return roleKeyBuilder.toString();
    }

    //后台CRM日志显示
    protected final void handleTextMsg(IObjectData data, IObjectDescribe objectDescribe, List<LogInfo.LintMessage> textMsg) {
        if (CollectionUtils.notEmpty(textMsg)) {
            LogInfo.LintMessage objectLintMessage = new LogInfo.LintMessage(objectDescribe.getDisplayName(), "", "");
            if (!textMsg.contains(objectLintMessage)) {
                textMsg.add(0, objectLintMessage);
            }
            LogInfo.LintMessage dataLintMessage = new LogInfo.LintMessage(data.getName(), data.getId(), objectDescribe.getApiName());
            if (!textMsg.contains(dataLintMessage)) {
                textMsg.add(1, dataLintMessage);
            }
        }
    }
}
