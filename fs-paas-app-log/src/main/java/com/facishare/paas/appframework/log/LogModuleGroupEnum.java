package com.facishare.paas.appframework.log;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/10/09
 */
public enum LogModuleGroupEnum {

    CUSTOM_OBJECT_MANAGER("UserDefineObj", I18NKey.CUSTOM_OBJECT_MANAGER),
    CUSTOM_FUNCTION_MANAGER("UserDefineFunc", I18NKey.CUSTOM_FUNCTION_MANAGER),
    SALE_FLOW_SETTING("30", I18NKey.SALE_FLOW_SETTING),
    HIGH_SEAS("18", I18NKey.HIGH_SEAS),
    SALES_CLUE_POOL("17", I18NKey.SALES_CLUE_POOL),
    PRODUCT_SORT("32", I18NKey.PRODUCT_SORT),
    FUNCTION_PRIVILEGE_MANAGEMENT("41", I18N<PERSON>ey.FUNCTION_PRIVILEGE_MANAGEMENT),
    DATA_PRIVILEGE_MANAGEMENT("44", I18NKey.DATA_PRIVILEGE_MANAGEMENT),
    RULE_SETTING("35", I18NKey.RULE_SETTING),
    DATA_BOARD_SETTING("36", I18NKey.DATA_BOARD_SETTING),
    SALE_REPORT_SETTING("37", I18NKey.SALE_REPORT_SETTING),
    APPROVAL_FLOW_MANAGER("42", I18NKey.APPROVAL_FLOW_MANAGER),
    WORK_FLOW_MANAGER("43", I18NKey.WORK_FLOW_MANAGER),
    BPM_MANAGER("workflow_bpm", I18NKey.BPM_MANAGER),
    STAGE_MANAGER("stage", I18NKey.STAGE_MANAGER),
    BACKLOG_REMOVAL_TOOL("45", I18NKey.BACKLOG_REMOVAL_TOOL),
    FIELD_MANAGEMENT("29", I18NKey.FIELD_MANAGEMENT),
    RFMRuleOBj("RFMRuleObj", I18NKey.RFM_RULE_DISPLAY_NAME),
    TASK_DELEGATE_SETTING("task_delegate_setting", I18NKey.TASK_DELEGATE_SETTING),
    MAIN_CONTROL_STRATEGY("main_control_strategy", I18NKey.MAIN_CONTROL_STRATEGY),
    ;

    LogModuleGroupEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return I18N.text(name);
    }

    public static List<LogModuleGroupEnum> getVersionBasic() {
        return Lists.newArrayList(SALES_CLUE_POOL, FUNCTION_PRIVILEGE_MANAGEMENT, RULE_SETTING, FIELD_MANAGEMENT);
    }

    public static List<LogModuleGroupEnum> getVersionStandardPro() {
        return Lists.newArrayList(values());
    }

    public static List<LogModuleGroupEnum> getVersionStandard() {
        return Stream.of(values()).filter(x -> CUSTOM_OBJECT_MANAGER != x && CUSTOM_FUNCTION_MANAGER != x)
                .collect(Collectors.toList());
    }

}
