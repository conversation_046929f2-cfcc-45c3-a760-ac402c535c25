package com.facishare.paas.appframework.core.predef.domain;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.domain.ActionDomainPlugin;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.rest.BaseAPIResult;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * Created by zhouwr on 2022/1/17.
 */
public interface BulkInvalidActionDomainPlugin extends ActionDomainPlugin<BulkInvalidActionDomainPlugin.Arg, BulkInvalidActionDomainPlugin.Result> {

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    class Arg extends DomainPlugin.Arg {
        private List<ObjectDataDocument> objectDataList;
    }

    class Result extends DomainPlugin.Result {

    }

    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    class RestResult extends BaseAPIResult {
        private Result data;
    }

}
