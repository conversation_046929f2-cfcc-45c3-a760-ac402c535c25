package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.common.exception.CrmDefObjCheckedException;
import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.appframework.metadata.ObjectImportConfig;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by linqiuying on 17/6/9.
 */
public class LocationFieldDataConverter extends AbstractFieldDataConverter {
    @Override
    public String convertFieldData(SessionContext sessionContext) throws CrmDefObjCheckedException {
        String valueStr = getObjectData().get(getFieldDescribe().getApiName(), String.class);
        if (StringUtils.isNotBlank(valueStr)) {
            String[] fieldValue = valueStr.split("\\#\\%\\$");
            if (ObjectImportConfig.isGrayLocationField(String.valueOf(sessionContext.getEId()))) {
                if (fieldValue.length > 2) {
                    String longitude = fieldValue[0];
                    String latitude = fieldValue[1];
                    if (StringUtils.equals(longitude, "0") && StringUtils.equals(latitude, "0")) {
                        return fieldValue[2];
                    }
                }
                return String.join("|", fieldValue);
            }
            if (fieldValue.length > 2) {
                return fieldValue[2];
            }
        }
        return "";
    }
}
