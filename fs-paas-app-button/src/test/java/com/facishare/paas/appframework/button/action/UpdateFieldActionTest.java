package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeServiceImpl;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * UpdateFieldAction单元测试 重点测试多语言数据处理的变更 GenerateByAI
 */
public class UpdateFieldActionTest {

    private UpdateFieldAction updateFieldAction;
    private IObjectDescribe describe;
    private IFieldDescribe fieldDescribe;
    private ObjectDescribeExt objectDescribeExt;
    private FieldDescribeExt fieldDescribeExt;
    private IObjectData objectData;
    private IUdefButton button;
    private IUdefAction action;
    private User user;
    private ButtonExecutor.Arg buttonArg;

    private Map<String, Object> updateFieldMap;
    private Map<String, Object> args;
    private Map<String, IFieldDescribe> fieldDescribeMap;

    @Before
    public void setUp() {
        updateFieldAction = new UpdateFieldAction();

        // 创建Mock对象
        describe = Mockito.mock(IObjectDescribe.class);
        fieldDescribe = Mockito.mock(IFieldDescribe.class);
        objectDescribeExt = Mockito.mock(ObjectDescribeExt.class);
        fieldDescribeExt = Mockito.mock(FieldDescribeExt.class);
        objectData = new ObjectData();
        button = Mockito.mock(IUdefButton.class);
        action = Mockito.mock(IUdefAction.class);
        user = Mockito.mock(User.class);
        buttonArg = Mockito.mock(ButtonExecutor.Arg.class);

        // 注入Mock服务
        ReflectionTestUtils.setField(updateFieldAction, "metaDataService", Mockito.mock(MetaDataService.class));
        ReflectionTestUtils.setField(updateFieldAction, "parseVarService", Mockito.mock(ParseVarService.class));
        ReflectionTestUtils.setField(updateFieldAction, "layoutLogicService", Mockito.mock(LayoutLogicServiceImpl.class));
        ReflectionTestUtils.setField(updateFieldAction, "functionPrivilegeService", Mockito.mock(FunctionPrivilegeServiceImpl.class));
        ReflectionTestUtils.setField(updateFieldAction, "logService", Mockito.mock(LogService.class));
        ReflectionTestUtils.setField(updateFieldAction, "expressionCalculateLogicService", Mockito.mock(ExpressionCalculateLogicService.class));
        ReflectionTestUtils.setField(updateFieldAction, "userRoleInfoService", Mockito.mock(UserRoleInfoService.class));
        ReflectionTestUtils.setField(updateFieldAction, "orgService", Mockito.mock(OrgService.class));

        // 准备测试数据
        updateFieldMap = new HashMap<>();
        updateFieldMap.put("field1", "value1");
        updateFieldMap.put("field2", "value2");

        args = new HashMap<>();
        args.put("form_field1_ml", "multiLangValue1");
        args.put("form_field2_ml", "multiLangValue2");
        args.put("form_nonMultiLang", "normalValue");

        fieldDescribeMap = new HashMap<>();
        fieldDescribeMap.put("field1", fieldDescribe);
        fieldDescribeMap.put("field2", fieldDescribe);
    }

    /**
     * GenerateByAI 测试内容描述：测试getFormFieldApiName方法的基本功能
     */
    @Test
    public void testGetFormFieldApiName_NormalCase() {
        // Given
        String fieldApiName = "test_field";
        String expected = "form_test_field";

        // When
        String result = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", fieldApiName);

        // Then
        Assert.assertEquals("字段API名称应正确添加form_前缀", expected, result);
    }

    /**
     * GenerateByAI 测试内容描述：测试getFormFieldApiName方法处理空字符串的情况
     */
    @Test
    public void testGetFormFieldApiName_EmptyString() {
        // Given
        String fieldApiName = "";
        String expected = "form_";

        // When
        String result = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", fieldApiName);

        // Then
        Assert.assertEquals("空字符串应正确添加form_前缀", expected, result);
    }

    /**
     * GenerateByAI 测试内容描述：测试getFormFieldApiName方法处理null值的情况
     */
    @Test
    public void testGetFormFieldApiName_NullValue() {
        // Given
        String fieldApiName = null;
        String expected = "form_null";

        // When
        String result = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", fieldApiName);

        // Then
        Assert.assertEquals("null值应被正确处理", expected, result);
    }

    /**
     * GenerateByAI 测试内容描述：测试getMultiLangDataMap方法当updateFieldMap为空时的处理
     */
    @Test
    public void testGetMultiLangDataMap_EmptyUpdateFieldMap() {
        // Given
        Map<String, Object> emptyMap = new HashMap<>();

        // When
        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) ReflectionTestUtils.invokeMethod(
                updateFieldAction, "getMultiLangDataMap", emptyMap, describe, args);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("空的updateFieldMap应返回空结果", result.isEmpty());
    }

    /**
     * GenerateByAI 测试内容描述：测试getMultiLangDataMap方法当objectDescribe为null时的处理
     */
    @Test
    public void testGetMultiLangDataMap_NullObjectDescribe() {
        // When
        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) ReflectionTestUtils.invokeMethod(
                updateFieldAction, "getMultiLangDataMap", updateFieldMap, null, args);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("null的objectDescribe应返回空结果", result.isEmpty());
    }

    /**
     * GenerateByAI 测试内容描述：测试getMultiLangDataMap方法当args为空时的处理
     */
    @Test
    public void testGetMultiLangDataMap_EmptyArgs() {
        // Given
        Map<String, Object> emptyArgs = new HashMap<>();

        // When
        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) ReflectionTestUtils.invokeMethod(
                updateFieldAction, "getMultiLangDataMap", updateFieldMap, describe, emptyArgs);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("空的args应返回空结果", result.isEmpty());
    }

    /**
     * GenerateByAI 测试内容描述：测试getUpdateFieldMap方法整合多语言数据的功能
     */
    @Test
    public void testGetUpdateFieldMap_WithMultiLangDataIntegration() {
        // Given
        String actionParameter = "{\n"
                + "    \"fields\": [\n"
                + "        {\n"
                + "            \"field\": \"field1\",\n"
                + "            \"value\": \"value1\",\n"
                + "            \"var_type\": \"constant\",\n"
                + "            \"return_type\": \"text\",\n"
                + "            \"default_to_zero\": false,\n"
                + "            \"decimal_places\": 0\n"
                + "        }\n"
                + "    ]\n"
                + "}";

        // 配置Mock行为
        Mockito.when(action.getActionParamter()).thenReturn(actionParameter);
        Mockito.when(buttonArg.isSkipUpdateFieldActionValidation()).thenReturn(true); // 跳过验证避免复杂的Mock设置
        Mockito.when(buttonArg.getArgs()).thenReturn(args);

        // 配置ObjectDescribeExt Mock
        Mockito.when(ObjectDescribeExt.of(describe)).thenReturn(objectDescribeExt);
        Mockito.when(objectDescribeExt.getDisableFieldDescribes()).thenReturn(Collections.emptyList());
        Mockito.when(objectDescribeExt.getActiveReferenceFieldDescribes()).thenReturn(Collections.emptyList());
        Mockito.when(describe.containsField("field1")).thenReturn(true);
        Mockito.when(describe.getFieldDescribe("field1")).thenReturn(fieldDescribe);
        Mockito.when(objectDescribeExt.getFieldDescribeMap()).thenReturn(fieldDescribeMap);

        // 配置FieldDescribeExt Mock
        Mockito.when(FieldDescribeExt.of(fieldDescribe)).thenReturn(fieldDescribeExt);
        Mockito.when(fieldDescribeExt.isEnableDataMultiLang()).thenReturn(true);
        Mockito.when(fieldDescribeExt.isShowMask()).thenReturn(false);
        Mockito.when(FieldDescribeExt.getMultiLangExtraFieldName("field1")).thenReturn("field1_ml");

        // When
        Map<String, Object> result = updateFieldAction.getUpdateFieldMap(
                describe, objectData, button, action, buttonArg, user);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertEquals("字段值应正确保存", "value1", result.get("field1"));
    }

    /**
     * GenerateByAI 测试内容描述：测试多语言数据处理的边界情况 - 字段不支持多语言
     */
    @Test
    public void testGetMultiLangDataMap_FieldNotSupportMultiLang() {
        // Given - 配置字段不支持多语言
        Mockito.mockStatic(ObjectDescribeExt.class);
        Mockito.when(ObjectDescribeExt.of(describe)).thenReturn(objectDescribeExt);
        Mockito.when(objectDescribeExt.getFieldDescribeMap()).thenReturn(fieldDescribeMap);

        Mockito.mockStatic(FieldDescribeExt.class);
        Mockito.when(FieldDescribeExt.of(fieldDescribe)).thenReturn(fieldDescribeExt);
        Mockito.when(fieldDescribeExt.isEnableDataMultiLang()).thenReturn(false);

        // When
        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) ReflectionTestUtils.invokeMethod(
                updateFieldAction, "getMultiLangDataMap", updateFieldMap, describe, args);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("不支持多语言的字段应返回空结果", result.isEmpty());
    }

    /**
     * GenerateByAI 测试内容描述：验证getFormFieldApiName方法中form_前缀添加的正确性
     */
    @Test
    public void testGetFormFieldApiName_PrefixLogic() {
        // Given - 多种不同的字段名
        String[] testFieldNames = {
            "simple_field",
            "field_with_underscore",
            "fieldWithCamelCase",
            "123field",
            "field123",
            "field-with-dash"
        };

        // When & Then
        for (String fieldName : testFieldNames) {
            String result = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", fieldName);
            String expected = "form_" + fieldName;
            Assert.assertEquals(
                    String.format("字段名 '%s' 应正确添加form_前缀", fieldName),
                    expected,
                    result
            );
        }
    }
}
