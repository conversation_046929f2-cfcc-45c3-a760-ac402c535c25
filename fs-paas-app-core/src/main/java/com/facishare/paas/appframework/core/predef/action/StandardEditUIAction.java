package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.facishare.paas.appframework.core.predef.action.StandardEditUIAction.Arg;
import static com.facishare.paas.appframework.core.predef.action.StandardEditUIAction.Result;

/**
 * create by zhaoju on 2020/09/06
 */
public class StandardEditUIAction extends AbstractStandardUIAction<Arg, Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.EditUI.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectId());
    }

    @Override
    protected void init() {
        super.init();
        prepareMasterObjectData();
        prepareDetailObjectData();
    }

    protected void prepareMasterObjectData() {
        objectData = dataList.get(0);
        // 编辑时，端上传了 data，用端上传的值覆盖 db 查出来的值
        if (CollectionUtils.notEmpty(arg.getObjectData())) {
            ObjectDataExt.of(objectData).putAll(arg.getObjectData());
            decodeMaskFieldEncryptValue();
        }

        //审批中的数据需要跟snapshot合并一下
        if (arg.getBizInfo() != null && !Strings.isNullOrEmpty(arg.getBizInfo().getBiz())) {
            mergeWithSnapshot();
        }
    }

    private void mergeWithSnapshot() {
        ObjectDataSnapshot dataSnapshot = infraServiceFacade.findAndMergeSnapshot(actionContext.getTenantId(), actionContext.getObjectApiName(),
                arg.getObjectId(), arg.getBizInfo().getBiz(), arg.getBizInfo().getOtherBizId());
        stopWatch.lap("findAndMergeSnapshot");

        if (CollectionUtils.empty(dataSnapshot.getMasterSnapshot())) {
            return;
        }

        //计算变更之后的统计字段和计算字段
        IObjectData snapshotData = serviceFacade.calculateForSnapshot(actionContext.getUser(), objectDescribe, objectData, dataSnapshot);
        stopWatch.lap("calculateForSnapshot");

        ObjectDataExt.of(objectData).putAll(ObjectDataExt.of(snapshotData).toMap());
    }

    protected void prepareDetailObjectData() {
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            return;
        }
        List<IObjectDescribe> detailDescribes = serviceFacade.findDetailDescribesCreateWithMaster(actionContext.getTenantId(), objectDescribe.getApiName());
        if (CollectionUtils.empty(detailDescribes)) {
            return;
        }
        // 从对象分页查询，每组从最多50条
        detailDescribes.forEach(detailDescribe -> {
            List<IObjectData> detailDataList = serviceFacade.findDetailObjectDataWithPage(actionContext.getUser(),
                    objectData.getDescribeApiName(), objectData.getId(), detailDescribe, 1, 500, null).getData();
            detailObjectData.put(detailDescribe.getApiName(), detailDataList);
        });

    }

    @Override
    protected Result doAct(Arg arg) {
        fillInfo(objectData);
        stopWatch.lap("fillInfo");
        return Result.builder()
                .objectData(ObjectDataDocument.of(objectData))
                .build();
    }

    /**
     * 拷贝数据，编辑UI按钮执行函数的时候
     * 和函数数据diff之后，merge 的是 copy 的map
     * 所以返回的是 当前objectData
     * 期望的结果是，不支持函数修改context.data 从而修改数据
     *
     * @return
     */
    @Override
    protected IObjectData getPreObjectData() {
        return ObjectDataExt.of(objectData).copy();
    }

    @Override
    protected String getButtonApiName() {
        if (AppFrameworkConfig.isAddEditUIActionGray(actionContext.getTenantId(), actionContext.getObjectApiName())) {
            return ObjectAction.UPDATE.getButtonApiName();
        }
        return null;
    }

    @Override
    protected Collection<String> getIgnoreFields() {
        return Collections.emptySet();
    }

    @Override
    protected Map<String, List<IObjectData>> getPreObjectDataDetails() {
        return detailObjectData;
    }

    @Data
    public static class Arg {
        @JSONField(name = "object_id")
        @JsonProperty("object_id")
        private String objectId;
        @JsonProperty("object_data")
        @JSONField(name = "object_data")
        private ObjectDataDocument objectData;
        private RequestContext.BizInfo bizInfo;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        @JSONField(name = "object_data")
        @JsonProperty("object_data")
        private ObjectDataDocument objectData;
    }
}
