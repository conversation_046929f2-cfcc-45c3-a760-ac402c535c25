package com.facishare.paas.appframework.metadata.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ImportReferenceMapping {

    // 灰度
    private Boolean importReferenceMappingGray;

    // 开关
    private Boolean referenceFieldMappingSwitch;

    // 内容
    private List<ReferenceFieldMapping> referenceFieldMapping;

    // 当前对象
    private String objectApiName;

    @Data
    public static class ReferenceFieldMapping {
        // 当前对象查找关联字段apiName
        private String objectReferenceFieldApiName;
        private String objectReferenceFieldLabel;
        // 查找关联字段上的唯一性字段
        private String specifiedUniqueFieldApiName;
        private String specifiedUniqueFieldLabel;
        // 查找关联的对象的apiName
//        @JsonProperty("target_object_api_name")
        private String targetObjectApiName;
        private String targetObjectLabel;
        // 当前对象apiName
        private String objectApiName;
        private String objectLabel;
        private Boolean hasConfig;
    }

    public static String buildImportReferenceMappingKey(String describeApiName) {
        return "import_reference_mapping_" + describeApiName;
    }
}
