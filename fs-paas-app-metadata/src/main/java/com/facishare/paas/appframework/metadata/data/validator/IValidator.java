package com.facishare.paas.appframework.metadata.data.validator;

import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;

import java.util.Objects;
import java.util.Optional;
import java.util.Set;

public interface IValidator {
    default boolean checkParamError(String fieldApiName, IObjectData data, IObjectDescribe describe) {
        if (Objects.isNull(data) || Objects.isNull(describe) || Strings.isNullOrEmpty(fieldApiName)) {
            return true;
        }
        Optional<IFieldDescribe> field = ObjectDescribeExt.of(describe).getFieldDescribeSilently(fieldApiName);
        return !field.isPresent();
    }

    default IFieldDescribe getField(String fieldApiName, IObjectDescribe describe) {
        return ObjectDescribeExt.of(describe).getFieldDescribeSilently(fieldApiName)
                .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.FIELD_DISABLED, fieldApiName), AppFrameworkErrorCode.FIELD_NOT_EXIST_ERROR));
    }
}
