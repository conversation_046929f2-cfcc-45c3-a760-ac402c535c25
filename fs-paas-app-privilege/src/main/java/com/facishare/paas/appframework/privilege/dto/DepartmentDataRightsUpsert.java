package com.facishare.paas.appframework.privilege.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface DepartmentDataRightsUpsert {
    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        DepartmentDataRightsContext context;
        List<DepartmentRights> deptRights;
    }


    class Result {

    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class DepartmentRights{
        String id;
        //对象APIName
        String entityId;
        //部门id
        String deptId;
        //启用 0 禁用 1
        int status;
        //场景 0 部门内数据共享 1组织内数据共享
        int scene;
        //递归子部门
        int type;
    }
}
