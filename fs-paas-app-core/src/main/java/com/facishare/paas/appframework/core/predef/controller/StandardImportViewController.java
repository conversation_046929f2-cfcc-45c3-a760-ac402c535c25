package com.facishare.paas.appframework.core.predef.controller;


import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.DataLoaderService;
import com.facishare.paas.appframework.common.service.dto.ImportView;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectFieldDescribeDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.ImportReferenceMapping;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe;
import com.google.common.collect.Lists;
import lombok.*;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

public class StandardImportViewController extends PreDefineController<StandardImportViewController.Arg, StandardImportViewController.Result> {


    private DataLoaderService dataLoaderService;
    protected IObjectDescribe describe;
    protected Map<String, IObjectDescribe> detailDescribes;

    //新建导入
    protected static final int IMPORT_TYPE_ADD = 0;
    //更新导入
    protected static final int IMPORT_TYPE_EDIT = 1;
    //根据ID导入
    protected static final int MATCHING_TYPE_ID = 1;
    protected static final String IMPORT_FIELD_MARK = "importFieldMark";
    protected static final String IMPORT_FIELD_MARK_MULTI_LANGUAGE = "multi_language";
    public static final String PERCENTAGE_SYMBOL = "（%）";
    protected boolean importReferenceFieldMappingSwitch = false;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        dataLoaderService = serviceFacade.getBean(DataLoaderService.class);
        describe = serviceFacade.findObject(controllerContext.getTenantId(), controllerContext.getObjectApiName());
        if (isMultiSheets()) {
            List<String> detailApiNames = arg.getObjectInfo().getDetailInfo().stream().map(BaseInfo::getApiName).collect(Collectors.toList());
            detailDescribes = serviceFacade.findObjects(controllerContext.getTenantId(), detailApiNames);
        }
        ImportReferenceMapping importReferenceMapping = infraServiceFacade.findImportReferenceMapping(controllerContext.getUser(), describe.getApiName());
        importReferenceFieldMappingSwitch = BooleanUtils.isTrue(importReferenceMapping.getReferenceFieldMappingSwitch());
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.BATCH_IMPORT.getActionCode());
    }

    @Override
    protected Result doService(Arg arg) {
        ImportView.Result parse = dataLoaderService.parse(controllerContext.getUser(), arg.getFilePath(), isMultiSheets());
        List<ImportView.SheetContent> sheetContents = parse.getSheetContents();
        MasterInfo masterInfo = getMasterInfo(sheetContents);
        List<DetailInfo> detailInfo = getDetailInfo(sheetContents);
        return buildResult(masterInfo, detailInfo);
    }

    private Result buildResult(MasterInfo masterInfo, List<DetailInfo> detailInfos) {
        Result result = new Result();
        ObjectInfo objectInfo = new ObjectInfo();
        objectInfo.setMasterInfo(masterInfo);
        objectInfo.setDetailInfo(detailInfos);
        result.setObjectInfo(objectInfo);
        return result;
    }


    private List<DetailInfo> getDetailInfo(List<ImportView.SheetContent> sheetContents) {
        if (!isMultiSheets()) {
            return Lists.newArrayList();
        }
        List<String> detailApiNames = arg.getObjectInfo().getDetailInfo().stream().map(BaseInfo::getApiName).collect(Collectors.toList());
        List<DetailInfo> detailInfos = Lists.newArrayList();
        AtomicInteger index = new AtomicInteger(0);
        for (String detailApiName : detailApiNames) {
            index.incrementAndGet();
            IObjectDescribe detailDescribe = detailDescribes.get(detailApiName);
            if (Objects.isNull(detailDescribe)) {
                log.error("detailDescribes is empty");
                continue;
            }
            Optional<ImportView.SheetContent> detailSheetContent = sheetContents.stream()
                    .filter(x -> Objects.equals(x.getSheetIndex(), String.valueOf(index.get())))
                    .findFirst();
            if (detailSheetContent.isPresent()) {
                DetailInfo detailInfo = buildDetailInfo(detailSheetContent.get(), detailDescribe);
                detailInfos.add(detailInfo);
            }
        }
        return detailInfos;
    }

    private DetailInfo buildDetailInfo(ImportView.SheetContent detailSheet, IObjectDescribe detailDescribe) {
        List<IFieldDescribe> validFieldDescribe = getValidFieldDescribe(detailDescribe);
        DetailInfo detailInfo = new DetailInfo();
        detailInfo.setApiName(detailDescribe.getApiName());
        detailInfo.setLabel(detailDescribe.getDisplayName());
        detailInfo.setFields(buildFields(validFieldDescribe, detailDescribe));
        detailInfo.setExcelCols(detailSheet.getExcelCols());
        detailInfo.setFieldMapping(buildFieldMapping(validFieldDescribe, detailSheet.getExcelCols()));
        return detailInfo;
    }

    private MasterInfo getMasterInfo(List<ImportView.SheetContent> sheetContents) {
        if (CollectionUtils.empty(sheetContents)) {
            return new MasterInfo();
        }
        ImportView.SheetContent masterSheet = sheetContents.get(0);
        List<IFieldDescribe> validFieldDescribe = getValidFieldDescribe(describe);
        MasterInfo masterInfo = new MasterInfo();
        masterInfo.setApiName(describe.getApiName());
        masterInfo.setLabel(describe.getDisplayName());
        masterInfo.setFields(buildFields(validFieldDescribe, describe));
        masterInfo.setExcelCols(masterSheet.getExcelCols());
        masterInfo.setFieldMapping(buildFieldMapping(validFieldDescribe, masterSheet.getExcelCols()));
        return masterInfo;
    }

    private List<IFieldDescribe> getValidFieldDescribe(IObjectDescribe describe) {
        List<IFieldDescribe> fieldDescribes;
        if (Objects.equals(arg.getImportType(), IMPORT_TYPE_ADD)) {
            fieldDescribes = infraServiceFacade.getTemplateField(controllerContext.getUser(), describe);
        } else {
            fieldDescribes = infraServiceFacade.getUpdateImportTemplateField(controllerContext.getUser(), describe);
        }
        customFields(describe, fieldDescribes);
        return fieldDescribes;
    }

    protected void customFields(IObjectDescribe describe, List<IFieldDescribe> fieldDescribes) {

    }

    private List<ObjectFieldDescribeDocument> buildFields(List<IFieldDescribe> fieldDescribes, IObjectDescribe describe) {
        List<ObjectFieldDescribeDocument> fieldDescribeDocuments = Lists.newArrayList();
        if (CollectionUtils.empty(fieldDescribes)) {
            return fieldDescribeDocuments;
        }
        if (arg.getMatchingType() == MATCHING_TYPE_ID) {
            ImportExportExt.updateImportID(fieldDescribes, arg.getImportType());
        }
        //普通字段
        for (IFieldDescribe fieldDescribe : fieldDescribes) {
            //主从关系字段不下发
            if (IFieldType.MASTER_DETAIL.equals(fieldDescribe.getType()) && isMultiSheets()) {
                continue;
            }
            ObjectFieldDescribeDocument fieldDescribeDocument = new ObjectFieldDescribeDocument();
            fieldDescribeDocument.put(IFieldDescribe.API_NAME, fieldDescribe.getApiName());
            fieldDescribeDocument.put(IFieldDescribe.LABEL, fieldDescribe.getLabel());
            //新建导入业务类型必填
            if (IObjectData.RECORD_TYPE.equals(fieldDescribe.getApiName()) && arg.getImportType() == IMPORT_TYPE_ADD) {
                //业务类型只有一个选项模版不下发
                RecordTypeFieldDescribe recordTypeFieldDescribe = (RecordTypeFieldDescribe) fieldDescribe;
                if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.IMPORT_RECORD_TYPE, controllerContext.getTenantId())
                        && recordTypeFieldDescribe.getRecordTypeOptions().size() == 1) {
                    fieldDescribeDocument.put(IFieldDescribe.IS_REQUIRED, fieldDescribe.isRequired());
                } else {
                    fieldDescribeDocument.put(IFieldDescribe.IS_REQUIRED, true);
                }
            } else {
                fieldDescribeDocument.put(IFieldDescribe.IS_REQUIRED, fieldDescribe.isRequired());
            }
            if (Objects.nonNull(fieldDescribe.get(IMPORT_FIELD_MARK))) {
                fieldDescribeDocument.put(IMPORT_FIELD_MARK, fieldDescribe.get(IMPORT_FIELD_MARK));
            }
            fieldDescribeDocuments.add(fieldDescribeDocument);
        }
        //多语字段
        List<IFieldDescribe> multiLangField = ImportExportExt.supportMultiLangField(describe.getTenantId(), fieldDescribes, describe.getApiName());
        if (CollectionUtils.notEmpty(multiLangField)) {
            for (IFieldDescribe fieldDescribe : multiLangField) {
                ObjectFieldDescribeDocument fieldDescribeDocument = new ObjectFieldDescribeDocument();
                fieldDescribeDocument.put(IFieldDescribe.API_NAME, fieldDescribe.getApiName());
                fieldDescribeDocument.put(IFieldDescribe.LABEL, fieldDescribe.getLabel());
                fieldDescribeDocument.put(IFieldDescribe.IS_REQUIRED, fieldDescribe.isRequired());
                fieldDescribeDocument.put(IMPORT_FIELD_MARK, IMPORT_FIELD_MARK_MULTI_LANGUAGE);
                fieldDescribeDocuments.add(fieldDescribeDocument);
            }
        }
        if (isMultiSheets()) {
            if (ObjectDescribeExt.of(describe).isSlaveObject()) {
                IFieldDescribe fieldDescribe = ImportExportExt.supportUniqueID(fieldDescribes);
                ObjectFieldDescribeDocument fieldDescribeDocument = new ObjectFieldDescribeDocument();
                fieldDescribeDocument.put(IFieldDescribe.API_NAME, fieldDescribe.getApiName());
                fieldDescribeDocument.put(IFieldDescribe.LABEL, fieldDescribe.getLabel());
                fieldDescribeDocument.put(IFieldDescribe.IS_REQUIRED, fieldDescribe.isRequired());
                fieldDescribeDocument.put(IMPORT_FIELD_MARK, ImportExportExt.UNION_IMPORT_ID_MARK);
                fieldDescribeDocuments.add(fieldDescribeDocument);
            }
            IFieldDescribe fieldDescribe = ImportExportExt.supportRelatedMark(fieldDescribes);
            ObjectFieldDescribeDocument fieldDescribeDocument = new ObjectFieldDescribeDocument();
            fieldDescribeDocument.put(IFieldDescribe.API_NAME, fieldDescribe.getApiName());
            fieldDescribeDocument.put(IFieldDescribe.LABEL, fieldDescribe.getLabel());
            fieldDescribeDocument.put(IFieldDescribe.IS_REQUIRED, fieldDescribe.isRequired());
            fieldDescribeDocument.put(IMPORT_FIELD_MARK, ImportExportExt.EXT_INFO_RELATED_MARK);
            fieldDescribeDocuments.add(fieldDescribeDocument);

        }
        return fieldDescribeDocuments;
    }


    protected List<FieldMapping> buildFieldMapping(List<IFieldDescribe> fieldDescribes, List<ImportView.ExcelCol> excelCols) {
        List<FieldMapping> fieldMapping = Lists.newArrayList();
        if (CollectionUtils.empty(excelCols)) {
            return fieldMapping;
        }

        excelCols.stream().distinct().forEach(excelCol -> {
            String colName = excelCol.getColName();
            IFieldDescribe fieldDescribe = fieldDescribes.stream()
                    .filter(x -> {
                        String label = x.getLabel();
                        if (IFieldType.PERCENTILE.equals(x.getType())) {
                            label = label + PERCENTAGE_SYMBOL;
                        }
                        if ((FieldDescribeExt.of(x).isLookupField() || FieldDescribeExt.of(x).isMasterDetailField())) {
                            if (importReferenceFieldMappingSwitch) {
                                label = label + "_" + I18N.text(I18NKey.UNIQUE_FILL_IN);
                            } else if (arg.getMatchingType() == MATCHING_TYPE_ID) {
                                label = label + "_" + I18N.text(I18NKey.DATAID_LABEL);
                            }
                        }
                        //根据唯一性id 更新导入,主属性匹配规则:[唯一性id]映射[主属性]
                        if (arg.getMatchingType() == MATCHING_TYPE_ID && arg.getImportType() == IMPORT_TYPE_EDIT && IObjectData.NAME.equals(x.getApiName())) {
                            label = I18N.text(I18NKey.DATAID_LABEL);
                        }
                        if (x.isRequired() || (IObjectData.RECORD_TYPE.equals(x.getApiName()) && arg.getImportType() == IMPORT_TYPE_ADD)) {
                            label = label + I18N.text(I18NKey.MUST_FILL_IN);
                        }
                        return Objects.equals(colName, label);
                    })
                    .findFirst()
                    .orElse(null);
            if (Objects.isNull(fieldDescribe)) {
                return;
            }
            FieldMapping mapping = FieldMapping.builder()
                    .apiName(fieldDescribe.getApiName())
                    .colIndex(excelCol.getColIndex())
                    .build();
            fieldMapping.add(mapping);
        });

        return fieldMapping;
    }

    protected boolean isMultiSheets() {
        return CollectionUtils.notEmpty(arg.getObjectInfo().getDetailInfo());
    }


    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class Arg extends BaseImportAction.Arg {
        private String filePath;
        private ObjectInfo objectInfo;
    }


    @Data
    public static class ObjectInfo {
        private MasterInfo masterInfo;
        private List<DetailInfo> detailInfo;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BaseInfo {
        private String apiName;
        private String label;
        private List<ObjectFieldDescribeDocument> fields;
        private List<ImportView.ExcelCol> excelCols;
        private List<FieldMapping> fieldMapping;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class MasterInfo extends BaseInfo {
    }

    @Data
    @Builder
    public static class FieldMapping {
        private String colIndex;
        private String apiName;
        private String importFieldMark;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DetailInfo extends BaseInfo {
    }

    @Data
    public static class Result {
        private ObjectInfo objectInfo;
    }
}
