package com.facishare.paas.appframework.flow;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.flow.dto.CanMoveToStagesOfObjects;
import com.facishare.paas.appframework.flow.exception.StageThrusterException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Slf4j
@Service("stageThrusterServiceImpl")
public class StageThrusterServiceImpl implements StageThrusterService {
    @Autowired
    private StageThrusterProxy proxy;

    @Override
    public Map<String, List<String>> canMoveToStagesOfObjects(User user, String apiName, Set<String> ids) {

        if (CollectionUtils.empty(ids)) {
            return Maps.newHashMap();
        }
        List<List<String>> idsList = Lists.partition(Lists.newArrayList(ids), 1000);

        Map<String, String> header = buildHeaders(user);
        Map<String, List<String>> canMoveToStagesMap = Maps.newHashMap();
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        idsList.forEach(x -> parallelTask.submit(() -> getCanMoveToStagesOfObjects(apiName, header, canMoveToStagesMap, x)));
        try {
            parallelTask.await(5000, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            throw new StageThrusterException(SystemErrorCode.METADATA_TIMEOUT_ERROR);
        }
        return canMoveToStagesMap;
    }

    private void getCanMoveToStagesOfObjects(String apiName, Map<String, String> header, Map<String, List<String>> canMoveToStagesMap, List<String> x) {
        CanMoveToStagesOfObjects.Arg arg = CanMoveToStagesOfObjects.Arg.builder()
                .entityId(apiName)
                .objectIds(x)
                .build();
        log.info("canMoveToStagesOfObjects info, header:{}, arg:{}", JSON.toJSONString(header), JSON.toJSONString(arg));
        CanMoveToStagesOfObjects.Result result = proxy.canMoveToStagesOfObjects(arg, header);
        if (result.getCode() == 0) {
            canMoveToStagesMap.putAll(result.getData());
        } else {
            log.error("canMoveToStagesOfObjects error, header:{}, arg:{}, result:{}", JSON.toJSONString(header), JSON.toJSONString(arg), JSON.toJSONString(result));
        }
    }

    private Map<String, String> buildHeaders(User user) {
        Map<String, String> ret = RestUtils.buildHeaders(user);
        ret.put("x-tenant-id", String.valueOf(user.getTenantId()));
        ret.put("x-user-id", user.getUserIdWithFlowGray());
        return ret;
    }
}
