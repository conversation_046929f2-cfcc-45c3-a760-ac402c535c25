package com.facishare.paas.appframework.metadata.config.handler;

import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.metadata.config.util.TenantConfigUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class SaleContractLineObjConfigHandler implements ConfigHandler {

    @Autowired
    ConfigService configService;

    @Override
    public String getObjectAPIName() {
        return "SaleContractLineObj";
    }

    @Override
    public void handle(String tenantId, Map<String, Object> objectConfig, Map<String, Map<String, Object>> fieldConfig) {
        boolean isStratifiedPriceEnabled = TenantConfigUtil.isStratifiedPriceEnabled(configService, tenantId);
        if (isStratifiedPriceEnabled) {
            if (fieldConfig.containsKey("product_price")) {
                Map<String, Object> attrMap = (Map<String, Object>) fieldConfig.get("product_price").get("attrs");
                if (attrMap != null) {
                    attrMap.put("default_value", 0);
                }
                fieldConfig.get("product_price").put("attrs", attrMap);
            }

            if (fieldConfig.containsKey("sales_price")) {
                Map<String, Object> attrMap = (Map<String, Object>) fieldConfig.get("sales_price").get("attrs");
                if (attrMap != null) {
                    attrMap.put("default_value", 0);
                }
                fieldConfig.get("sales_price").put("attrs", attrMap);
            }

            if (fieldConfig.containsKey("subtotal")) {
                Map<String, Object> attrMap = (Map<String, Object>) fieldConfig.get("subtotal").get("attrs");
                if (attrMap != null) {
                    attrMap.put("default_value", 0);
                }
                fieldConfig.get("subtotal").put("attrs", attrMap);
            }
        }
    }
} 