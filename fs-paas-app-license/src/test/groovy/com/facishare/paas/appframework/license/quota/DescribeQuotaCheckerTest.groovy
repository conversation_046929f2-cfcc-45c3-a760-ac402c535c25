package com.facishare.paas.appframework.license.quota

import com.facishare.paas.appframework.license.quota.checker.DescribeQuotaChecker
import com.facishare.paas.appframework.license.quota.checker.QuotaChecker
import com.facishare.paas.appframework.license.util.ModulePara
import com.google.common.collect.Maps
import spock.lang.Specification

class DescribeQuotaCheckerTest extends Specification {

    def "test getMaxCount should return mapped value if available"() {
        given:
        def paraKey = ModulePara.CUSTOM_OBJECTS_LIMIT.getParaKey()
        def paraMap = [(paraKey): 100]
        def describeQuotaChecker = DescribeQuotaChecker.builder()
                .paraMap(paraMap)
                .build()
        def quotaInfo = QuotaChecker.QuotaInfo.builder()
                .build()

        when:
        def result = describeQuotaChecker.getMaxCount(quotaInfo)

        then:
        result == 100
    }

    def "test getMaxCount should return 0 if not mapped"() {
        given:
        def paraMap = Maps.newHashMap()
        def describeQuotaChecker = DescribeQuotaChecker.builder()
                .paraMap(paraMap)
                .build()
        def quotaInfo = QuotaChecker.QuotaInfo.builder()
                .build()

        when:
        def result = describeQuotaChecker.getMaxCount(quotaInfo)

        then:
        result == 0
    }

    def "test check should return true when actualCount <= maxCount"() {
        given:
        def paraKey = ModulePara.CUSTOM_OBJECTS_LIMIT.getParaKey()
        def paraMap = [(paraKey): 100]
        def describeQuotaChecker = DescribeQuotaChecker.builder()
                .paraMap(paraMap)
                .build()
        def quotaInfo = QuotaChecker.QuotaInfo.builder()
                .actualCount(50)
                .build()

        when:
        def result = describeQuotaChecker.check(quotaInfo)

        then:
        result == true
    }

    def "test check should return false when actualCount > maxCount"() {
        given:
        def paraKey = ModulePara.CUSTOM_OBJECTS_LIMIT.getParaKey()
        def paraMap = [(paraKey): 100]
        def describeQuotaChecker = DescribeQuotaChecker.builder()
                .paraMap(paraMap)
                .build()
        def quotaInfo = QuotaChecker.QuotaInfo.builder()
                .actualCount(150)
                .build()

        when:
        def result = describeQuotaChecker.check(quotaInfo)

        then:
        result == false
    }
} 