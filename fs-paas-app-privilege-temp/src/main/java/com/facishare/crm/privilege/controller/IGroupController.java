package com.facishare.crm.privilege.controller;

import com.facishare.crm.privilege.controller.arg.*;
import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.fcp.model.BaseFcpServiceResult;

@FcpService("groupApi")
public interface IGroupController {

//用户组
//5.用户组列表 搜索组名 成员名
//6.查询组成员列表
//7.修改组信息 组成员
//8.新建/删除用户组

    /**
     * 组列表
     *
     * @return
     */
    @FcpMethod("groupList")
    BaseFcpServiceResult groupList(GroupListArg arg);

    /**
     * 组用户
     *
     * @return
     */

    @FcpMethod("groupUsers")
    BaseFcpServiceResult groupUsers(GroupUsersArg arg);

    /**
     * 查询组信息
     *
     * @return
     */
    @FcpMethod("queryGroupInfo")
    BaseFcpServiceResult queryGroupInfo(QueryGroupInfoArg arg);

    /**
     * 修改组信息
     *
     * @return
     */
    @FcpMethod("updateGroupInfo")
    BaseFcpServiceResult updateGroupInfo(UpdateGroupInfoArg arg);

    /**
     * 创建用户组
     *
     * @return
     */

    @FcpMethod("createGroup")
    BaseFcpServiceResult createGroup(CreateGroupArg arg);


    /**
     * 修改用户组用户
     *
     * @return
     */
    @FcpMethod("updateGroupUsers")
    BaseFcpServiceResult updateGroupUsers(UpdateGroupUsersArg arg);

    /**
     * 批量删除用户组
     *
     * @return
     */
    @FcpMethod("deleteGroups")
    BaseFcpServiceResult deleteGroups(DeleteGroupsArg arg);

    /**
     * 批量修改组状态
     *
     * @param arg
     * @return
     */
    @FcpMethod("updateGroupStatus")
    BaseFcpServiceResult updateGroupStatus(UpdateGroupStatusArg arg);

    /**
     * 获取用户组的翻译值
     *
     * @param arg
     * @return
     */
    @FcpMethod("queryGroupMulti")
    BaseFcpServiceResult queryGroupMulti(QueryMultiArg arg);

    /**
     * 根据用户ID查询用户组
     *
     * @param arg
     * @return
     */
    @FcpMethod("queryUserGroup")
    BaseFcpServiceResult queryUserGroup(QueryUserGroupArg arg);
}
