package com.facishare.paas.appframework.metadata.data.validator;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;


public interface IMaxLengthValidator extends IFieldValidator {
    int AUTO_NUMBER_MAX_LENGTH = 128;
    void validateMaxLength(String fieldApiName, IObjectData data, IObjectDescribe describe) throws ValidateException;
}
