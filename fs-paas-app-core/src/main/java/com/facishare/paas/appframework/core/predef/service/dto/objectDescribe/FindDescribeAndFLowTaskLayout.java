package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import lombok.Builder;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/3/7
 */
public interface FindDescribeAndFLowTaskLayout {

    @Data
    class Arg {
        private String describeApiName;
        private String whatDescribeApiName;
        private String layoutApiName;
        private String layoutType;
        private boolean noNeedReplaceI18n = false;
        private boolean removeI18n = false;
    }

    @Data
    @Builder
    class Result {
        private ObjectDescribeDocument objectDescribe;
        private ObjectDescribeDocument whatDescribe;
        private LayoutDocument layout;
    }
}
