package com.facishare.paas.appframework.core.timezone;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.timezone.annotation.ObjectDataField;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;

/**
 * create by z<PERSON><PERSON> on 2021/06/18
 */
public class ListObjectDataFieldConvert extends AbstractObjectDataFieldConvert {

    public static ListObjectDataFieldConvert getInstance() {
        return Helper.INSTANCE;
    }

    private ListObjectDataFieldConvert() {
    }

    @Override
    public ObjectDataField.Type getType() {
        return ObjectDataField.Type.LIST;
    }

    @Override
    public <T> T convert2SystemZone(T value, Function<String, IObjectDescribe> function) {
        if (Objects.isNull(value)) {
            return null;
        }
        List<IObjectData> dataList = toDataList(value);
        if (CollectionUtils.empty(dataList)) {
            return value;
        }
        String describeApiName = dataList.get(0).getDescribeApiName();
        IObjectDescribe describe = function.apply(describeApiName);
        List<IObjectData> objectDataList = ObjectDataExt.convertDateFieldValueToSystemZone(describe, dataList);
        return toListEntity(objectDataList, value);
    }

    @Override
    public <T> T convert2CustomZone(T value, Function<String, IObjectDescribe> function) {
        if (Objects.isNull(value)) {
            return null;
        }
        List<IObjectData> dataList = toDataList(value);
        if (CollectionUtils.empty(dataList)) {
            return value;
        }
        String describeApiName = dataList.get(0).getDescribeApiName();
        IObjectDescribe describe = function.apply(describeApiName);
        List<IObjectData> objectDataList = ObjectDataExt.convertDateFieldValueToCustomZone(describe, dataList);
        return toListEntity(objectDataList, value);
    }

    private static final class Helper {
        private static final ListObjectDataFieldConvert INSTANCE = new ListObjectDataFieldConvert();
    }
}
