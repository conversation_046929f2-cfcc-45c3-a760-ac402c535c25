package com.facishare.paas.appframework.metadata.relation;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.relation.ObjectRelationGraphBuilder.ObjectEdge;
import com.facishare.paas.appframework.metadata.relation.ObjectRelationGraphBuilder.ObjectNode;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.graph.EndpointPair;
import com.google.common.graph.Network;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2019/02/21
 */
@Slf4j
public class ObjectRelationGraph {
    @Getter
    @Delegate
    private Network<ObjectNode, ObjectEdge> network;

    @Getter
    private Map<String, IObjectDescribe> describeMap;

    private Map<String, ObjectNode> objectNodeMap;

    @java.beans.ConstructorProperties({"network", "describeMap"})
    private ObjectRelationGraph(Network<ObjectNode, ObjectEdge> network, Map<String, IObjectDescribe> describeMap) {
        this.network = network;
        this.describeMap = describeMap;
        this.objectNodeMap = network.nodes().stream().collect(Collectors.toMap(ObjectNode::getDescribeApiName, x -> x));
    }

    public static ObjectRelationGraph of(Network<ObjectNode, ObjectEdge> network,
                                         Map<String, IObjectDescribe> describeMap) {
        return new ObjectRelationGraph(network, describeMap);
    }

    public Optional<ObjectNode> getNode(String describeApiName) {
        return network.nodes().stream().filter(x -> describeApiName.equals(x.getDescribeApiName())).findFirst();
    }

    public int sumOutDegree(String describeApiName) {
        Optional<ObjectNode> nodeOpt = getNode(describeApiName);
        if (!nodeOpt.isPresent()) {
            return 0;
        }
        return sumOutDegree(nodeOpt.get(), Sets.newHashSet());
    }

    private int sumOutDegree(ObjectNode node, Set<ObjectNode> visitedNodes) {
        visitedNodes.add(node);
        int outDegree = network.outDegree(node);
        Set<ObjectNode> successors = Sets.newHashSet(network.successors(node));
        successors.removeAll(visitedNodes);
        for (ObjectNode successor : successors) {
            outDegree += sumOutDegree(successor, visitedNodes);
        }
        return outDegree;
    }

    public Map<String, List<Tuple<String, String>>> specifiedRange(String apiName, String targetApiName) {
        ObjectNode objectNode = objectNodeMap.get(apiName);

        Optional<ObjectEdge> objectEdge =
                network.outEdges(objectNode).stream().filter(x -> IFieldType.MASTER_DETAIL.equals(x.getEdgeType())).findFirst();
        if (!objectEdge.isPresent()) {
            return Maps.newHashMap();
        }
        Map<String, List<Tuple<String, String>>> result = Maps.newHashMap();
        Map<ObjectEdge, List<List<ObjectEdge>>> route = getRoute(objectNode);
        log.debug("ObjectRelationGraph getRoute, route=>{}", JSON.toJSONString(route));

        // 1. 遍历构图后得到的路径，按D对象和C对象对所有符合五角关系的路径进行分类
        Map<String, List<SpecifiedRange>> specifiedRangeMap = getSpecifiedRangeMap(targetApiName, objectEdge.get(),
                route);

        // 2.遍历分类后的路径，得到显示名称和filterValue
        specifiedRangeMap.forEach((key, value) -> {
            if (CollectionUtils.empty(value)) {
                return;
            }
            Map<String, List<ObjectEdge>> diffEdge = Maps.newHashMap();
            if (value.size() > 1) { // 处理CD限定后的路径存在多条的情况
                value.forEach(range -> range.getNodeStack().forEach(s -> {
                    if (CollectionUtils.notEmpty(diffEdge.get(s))) {
                        ObjectEdge edge = range.getEdgeMap().get(s);
                        if (!diffEdge.get(s).contains(edge)) {
                            diffEdge.get(s).add(edge);
                        }
                    } else {
                        diffEdge.put(s, Lists.newArrayList(range.getEdgeMap().get(s)));
                    }
                }));
            }
            value.forEach(range -> buildResult(result, key, range, diffEdge));
        });
        return result;
    }


    @SuppressWarnings("unchecked")
    private void buildResult(Map<String, List<Tuple<String, String>>> result, String key, SpecifiedRange range,
                             Map<String, List<ObjectEdge>> diffEdge) {
        Deque<String> nodeStack = range.getNodeStack();
        Map<String, ObjectEdge> edgeMap = range.getEdgeMap();

        if (result.containsKey(key)) {
            result.get(key).add(Tuple.of(getDisplayName(nodeStack, edgeMap, diffEdge),
                    getFieldValue(nodeStack, edgeMap)));
        } else {
            result.put(key, Lists.newArrayList(Tuple.of(getDisplayName(nodeStack, edgeMap, diffEdge),
                    getFieldValue(nodeStack, edgeMap))));
        }
    }

    /**
     * 目标路径 E=B.f1.A->A.f2.C->D.f3.C->D.f5
     * ProductObj=object_eg79j__c.field_54b0X__c.object_e9J2F__c->object_e9J2F__c.field_106d0__c
     * .PriceBookObj->PriceBookProductObj.pricebook_id.PriceBookObj->PriceBookProductObj.product_id
     *
     * @param nodeStack
     * @param edgeMap
     * @return
     */
    private String getFieldValue(Deque<String> nodeStack, Map<String, ObjectEdge> edgeMap) {
        String targetApiName = nodeStack.peekLast();
        return nodeStack.stream().map(s -> {
            ObjectEdge edge = edgeMap.get(s);
            return Objects.equals(targetApiName, edge.getTargetApiName()) ?
                    String.join(".", edge.getDescribeApiName(), edge.getFieldApiName()) :
                    String.join(".", edge.getDescribeApiName(), edge.getFieldApiName(), edge.getTargetApiName());
        }).collect(Collectors.joining("->", targetApiName + "=", ""));
    }

    /**
     * 目标名称
     * 【XX(产找关联对象名)中XX(相关对象/从对象名称)】先定下的xx
     * <p>
     * 【价目表ccc中价目表明细】限定下的产品
     * 五角关系A-zj>价目表ccc>价目表明细>产品
     * 五角关系A-zj([查找关联]字段引用五角关系C-zj)>五角关系C-zj>五角关系D-zj([查找关联--C]字段引用五角关系C-zj)>查找关联--产品
     *
     * @param nodeDeque
     * @param edgeMap
     * @param diffEdge
     * @return
     */
    @SuppressWarnings("unchecked")
    private String getDisplayName(Deque<String> nodeDeque, Map<String, ObjectEdge> edgeMap,
                                  Map<String, List<ObjectEdge>> diffEdge) {
        Deque<String> nodeStack = Lists.newLinkedList(nodeDeque);
        nodeStack.poll();
        StringJoiner joiner = new StringJoiner(">");
        for (String apiName : nodeStack) {
            ObjectEdge objectEdge = edgeMap.get(apiName);
            // 处理 D->E
            if (Objects.equals(nodeStack.peekLast(), apiName)) {
                IObjectDescribe objectDescribe = describeMap.get(objectEdge.getDescribeApiName());
                joiner.add(ObjectDescribeExt.of(objectDescribe).getFieldLabelByName(objectEdge.getFieldApiName()));
                break;
            }
            // 处理 D->C
            boolean hasRepeatEdge = diffEdge.getOrDefault(apiName, Collections.EMPTY_LIST).size() > 1;
            if (Objects.equals(objectEdge.getDescribeApiName(), apiName)) {
                joiner.add(getDisplayName(apiName, objectEdge, hasRepeatEdge));
                continue;
            }
            // 处理 A->C
            joiner.add(getDisplayName(objectEdge.getDescribeApiName(), objectEdge, hasRepeatEdge));
            joiner.add(describeMap.get(apiName).getDisplayName());
        }
        log.debug("getDisplayName, nodeDeque =>{}, result=>{}", nodeDeque, joiner);
        return I18N.text(I18NKey.DETAILED_PATH, joiner);
    }

    private String getDisplayName(String apiName, ObjectEdge objectEdge, boolean hasRepeatEdge) {
        if (!hasRepeatEdge) {
            return describeMap.get(apiName).getDisplayName();
        }
        String describeApiName = objectEdge.getDescribeApiName();
        String fieldApiName = objectEdge.getFieldApiName();
        String fieldName = ObjectDescribeExt.of(describeMap.get(describeApiName)).getFieldLabelByName(fieldApiName);
        String targetDisplayName = describeMap.get(objectEdge.getTargetApiName()).getDisplayName();
        return I18N.text(I18NKey.FIELD_RELATION, describeMap.get(apiName).getDisplayName(), fieldName,
                targetDisplayName);
    }

    private Map<String, List<SpecifiedRange>> getSpecifiedRangeMap(String targetApiName, ObjectEdge objectEdge,
                                                                   Map<ObjectEdge, List<List<ObjectEdge>>> route) {
        Map<String, List<SpecifiedRange>> resultMap = Maps.newHashMap();
        route.getOrDefault(objectEdge, Lists.newArrayList()).stream()
                .filter(edges -> edgesHasTargetApiName(targetApiName, edges))
                .filter(edges -> edges.stream().noneMatch(ObjectEdge::hasMultiRelationEdge))
                .forEach(edges -> {
                    Deque<String> nodeStack = Lists.newLinkedList();
                    Map<String, ObjectEdge> edgeMap = Maps.newHashMap();
                    Optional<ObjectEdge> edgeOptional = edges.stream().map(edge -> {
                        String apiName = nodeStack.peekLast();
                        nodeStack.offerLast(getEdgeTargetApiName(apiName, edge));
                        edgeMap.put(getEdgeTargetApiName(apiName, edge), edge);
                        return Tuple.of(Objects.equals(apiName, edge.getTargetApiName()), edge);
                    }).collect(Collectors.toList()).stream().filter(Tuple::getKey).map(Tuple::getValue).findFirst();

                    edgeOptional.ifPresent(edge -> {
                        // 处理先发的可选范围的黑体字部分 C对象下D对象限定的E
                        String specifiedRangeKey = I18N.text(I18NKey.FIELD_LIMITED,
                                describeMap.get(edge.getTargetApiName()).getDisplayName(),
                                describeMap.get(edge.getDescribeApiName()).getDisplayName(),
                                describeMap.get(targetApiName).getDisplayName());
                        if (resultMap.containsKey(specifiedRangeKey)) {
                            resultMap.get(specifiedRangeKey).add(SpecifiedRange.builder()
                                    .nodeStack(nodeStack)
                                    .edgeMap(edgeMap)
                                    .edgeType(edge.getEdgeType()).build());
                        } else {
                            resultMap.put(specifiedRangeKey, Lists.newArrayList(SpecifiedRange.builder()
                                    .nodeStack(nodeStack)
                                    .edgeMap(edgeMap)
                                    .edgeType(edge.getEdgeType()).build()));
                        }
                    });

                });
        return resultMap;
    }

    private String getEdgeTargetApiName(String apiName, ObjectEdge edge) {
        return Objects.equals(apiName, edge.getTargetApiName()) ? edge.getDescribeApiName() : edge.getTargetApiName();
    }

    private boolean edgesHasTargetApiName(String targetApiName, List<ObjectEdge> edges) {
        return edges.size() > 3 &&
                targetApiName.equals(edges.get(3).getTargetApiName());
    }

    /**
     * 获取符合五角关系的指定路径
     *
     * @param objectNode
     * @return
     */
    public Map<ObjectEdge, List<List<ObjectEdge>>> getRoute(ObjectNode objectNode) {
        Deque<Tuple<ObjectNode, ObjectEdge>> edgeStack = Lists.newLinkedList();
        Map<ObjectEdge, List<List<ObjectEdge>>> resultMap = Maps.newHashMap();

        getNextObjectEdges(objectNode).stream()
                .filter(objectEdge -> IFieldType.MASTER_DETAIL.equals(objectEdge.getEdgeType()))
                .forEach(objectEdge -> {
                    edgeStack.push(Tuple.of(getKeyNode(objectEdge), objectEdge));
                    resultMap.put(objectEdge, DFS(getSuccessorsEdges(getNextObjectEdges(objectNode), objectEdge),
                            edgeStack));
                });
        return resultMap;
    }

    private Set<ObjectEdge> getSuccessorsEdges(Set<ObjectEdge> objectEdges, ObjectEdge objectEdge) {
        return objectEdges.stream().filter(n -> !objectEdge.equals(n)).collect(Collectors.toSet());
    }

    private List<List<ObjectEdge>> DFS(Collection<ObjectEdge> successorsEdges,
                                       Deque<Tuple<ObjectNode, ObjectEdge>> edgeStack) {
        List<List<ObjectEdge>> resultList = Lists.newArrayList();
        Deque<Tuple<ObjectNode, ObjectEdge>> routeStack = Lists.newLinkedList();

        // 记录循环次数，记录循环次数不能超过32767次
        int cycles = 0;
        while (!edgeStack.isEmpty()) {
            if (cycles > Short.MAX_VALUE || routeStack.size() > edges().size() || edgeStack.size() > edges().size()) {
                log.warn("cycles more than {}, routeStackSize=>{}, edgeStackSize=>{}, tenantId=>{}, userId=>{}, " +
                                "network=>{}",
                        Short.MAX_VALUE, routeStack.size(), edgeStack.size(),
                        RequestContextManager.getContext().getTenantId(),
                        RequestContextManager.getContext().getUser().getUserId(), network.toString());
                break;
            }
            Tuple<ObjectNode, ObjectEdge> objectTuple = edgeStack.poll();
            // 与目标节点间包含通路,记录路径的栈中保存的路径节点，并将当前节点从路径栈中删除
            if (successorsEdges.contains(objectTuple.getValue())) {
                resultList.add(routeStack.stream().map(Tuple::getValue).collect(Collectors.toList()));
                continue;
            }
            // 需要入栈的边跟在已有的路径栈的那条遍之后
            for (Tuple<ObjectNode, ObjectEdge> tuple = routeStack.peekLast(); !checked(tuple, objectTuple);
                 tuple = routeStack.peekLast()) {
                routeStack.pollLast();
            }

            routeStack.offer(objectTuple);
            // 限制路径栈的长度为4
            if (routeStack.size() > 3) {
                handleResultList(resultList, routeStack, Collections.emptySet());
                cycles++;
                continue;
            }
            // 已经在路径栈中的边不进入进待遍历的栈edgeStack，当C:A->C存在时不允许C:X->C边进入待遍历栈edgeStack
            List<Tuple<ObjectNode, ObjectEdge>> tupleEdgeList = getNextObjectEdges(objectTuple.getKey(),
                    routeStack.size()).stream()
                    .filter(edge -> routeStack.stream().noneMatch(x -> ObjectEdge.equals(x.getValue(), edge)))
                    .map(edge -> Tuple.of(getKeyNode(edge, routeStack.size()), edge))
//                    .filter(tuple -> !tuple.getKey().isPredecessor() ||
//                            routeStack.stream().noneMatch(x -> Objects.equals(x.getKey(), tuple.getKey())))
                    .filter(tuple -> keyNotNodeC(tuple.getKey(), routeStack)
                            || routeStack.stream().noneMatch(x -> Objects.equals(x.getKey(), tuple.getKey())))
                    .collect(Collectors.toList());
            tupleEdgeList.forEach(edgeStack::push);
            // 当前节点没有后继节点，从路径栈中删掉该节点
            handleResultList(resultList, routeStack,
                    tupleEdgeList.stream().map(Tuple::getValue).collect(Collectors.toSet()));// 用实际入路径栈edgeStack的边来判断
            cycles++;
        }
        return resultList;
    }

    private boolean keyNotNodeC(ObjectNode node, Deque<Tuple<ObjectNode, ObjectEdge>> routeStack) {
        if (CollectionUtils.empty(routeStack) || routeStack.size() < 2) {
            return true;
        }
        return !Lists.newArrayList(routeStack).get(1).getKey().equals(node);
    }

    private ObjectNode getKeyNode(ObjectEdge edge) {
        return getKeyNode(edge, 0);
    }

    private ObjectNode getKeyNode(ObjectEdge edge, int routeStackSize) {
        return routeStackSize == 2 ? network.incidentNodes(edge).nodeU() : network.incidentNodes(edge).nodeV();
    }

    private void handleResultList(List<List<ObjectEdge>> resultList, Deque<Tuple<ObjectNode, ObjectEdge>> routeStack,
                                  Set<ObjectEdge> objectEdges) {
        if (CollectionUtils.notEmpty(objectEdges)) {
            return;
        }
        log.debug("ObjectRelationGraph handleResultList, routeStack=>{}", JSON.toJSONString(routeStack));
        resultList.add(routeStack.stream().map(Tuple::getValue).collect(Collectors.toList()));
        routeStack.pollLast();
    }

    private Set<ObjectEdge> getNextObjectEdges(ObjectNode objectNode) {
        return getNextObjectEdges(objectNode, 0);
    }

    private Set<ObjectEdge> getNextObjectEdges(ObjectNode objectNode, int routeStackSize) {
        return routeStackSize == 2 ? network.inEdges(objectNode) : network.outEdges(objectNode);
    }

    private boolean checked(Tuple<ObjectNode, ObjectEdge> stackTopTuple, Tuple<ObjectNode, ObjectEdge> objectTuple) {
        if (Objects.isNull(stackTopTuple)) {
            return true;
        }
        EndpointPair<ObjectNode> objectEndpointPair = network.incidentNodes(objectTuple.getValue());
        return isaBoolean(objectTuple.getKey(), objectEndpointPair.nodeU(), objectEndpointPair.nodeV(),
                stackTopTuple.getKey());
    }

    /**
     * @param objectNode    下一个需要入路径栈的tuple中存在key中的node
     * @param objectNodeU   边的起始节点
     * @param objectNodeV   边得终到节点
     * @param topObjectNode 当前路径栈栈顶的tuple中存在key中的node
     * @return
     */
    private boolean isaBoolean(ObjectNode objectNode, ObjectNode objectNodeU, ObjectNode objectNodeV,
                               ObjectNode topObjectNode) {
        return Objects.equals(objectNode, objectNodeV) ? Objects.equals(objectNodeU, topObjectNode) :
                Objects.equals(objectNodeV, topObjectNode);
    }

    public Optional<IObjectDescribe> getObjectDescribeByObjectEdge(ObjectEdge objectEdge) {
        return Optional.ofNullable(describeMap.get(objectEdge.getDescribeApiName()));
    }

    @Data
    @Builder
    private static class SpecifiedRange {
        private Deque<String> nodeStack;
        private Map<String, ObjectEdge> edgeMap;
        private String edgeType;
    }
}
