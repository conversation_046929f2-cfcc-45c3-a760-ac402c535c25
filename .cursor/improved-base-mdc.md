---
description: fs-paas-appframework测试规范-增强版
globs: 
alwaysApply: false
---

# fs-paas-appframework单元测试规范

## 核心规则 - 必须遵循
1. 测试类命名：`被测类名+Test`或`被测类名+PowerMockTest`
2. 每个测试方法必须添加`GenerateByAI`注释和测试内容说明
3. 测试方法命名：方法名 + Test/Error + 测试场景
4. 使用Spock框架的given/when/then结构
5. 不可使用Spock的Spy机制处理父类方法调用

## 决策流程图 - PowerMock使用判断

```
开始
 |
 ↓
被测试类是否以Action/Controller结尾？
 |     |
 |     ↓
 |    是 → 检查被测试方法是否包含super.方法名()调用？
 |          |      |
 |          |      ↓
 |          |     是 → 父类是否在外部jar包中？
 |          |            |      |
 |          |            |      ↓
 |          |            |     是 → 使用PowerMock测试（创建ClassNamePowerMockTest）
 |          |            |             必须遵循301-appframework-bussiness.mdc规范
 |          |            ↓
 |          |           否 → 使用普通Spock测试（创建ClassNameTest）
 |          ↓
 |         否 → 使用普通Spock测试（创建ClassNameTest）
 ↓
否 → 使用普通Spock测试（创建ClassNameTest）
```

## 基础对象构造规范

以下对象必须严格按照301-appframework-base-unit-demo.mdc中的示例进行构造：

1. **用户与上下文相关**
   - User
   - RequestContext
   - ServiceContext

2. **元数据相关**
   - IObjectDescribe
   - IFieldDescribe
   - ObjectDescribeExt

3. **数据对象相关**
   - IObjectData
   - Wheres
   - Filter

4. **配置相关**
   - AppFrameworkConfig

## 单元测试模板 - 普通Spock测试
```groovy
package com.facishare.paas.appframework.example

import spock.lang.Specification
import spock.lang.Unroll

/**
 * TestedClass单元测试
 */
class TestedClassTest extends Specification {

    // 被测试类实例
    TestedClass testedClass
    
    // 依赖服务
    DependencyService dependencyService
    
    /**
     * 初始化测试环境 - 每个测试方法执行前都会调用
     */
    def setup() {
        // 初始化被测试类
        testedClass = new TestedClass()
        
        // 初始化Mock对象
        dependencyService = Mock(DependencyService)
        
        // 注入依赖
        testedClass.dependencyService = dependencyService
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试方法的正常执行流程
     */
    def "methodTest_NormalFlow"() {
        given:
        // 准备测试数据
        def param = "testParam"
        
        // 配置Mock行为
        dependencyService.someMethod(_) >> "mockResult"
        
        when:
        // 调用被测试方法
        def result = testedClass.method(param)
        
        then:
        // 验证结果
        result == "expectedResult"
        
        // 验证交互
        1 * dependencyService.someMethod("testParam")
        
        // 确保没有异常
        noExceptionThrown()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试方法在异常情况下的行为
     */
    def "methodError_WhenDependencyFails"() {
        given:
        // 准备测试数据
        def param = "testParam"
        
        // 配置Mock行为抛出异常
        dependencyService.someMethod(_) >> { throw new RuntimeException("test error") }
        
        when:
        // 调用被测试方法
        testedClass.method(param)
        
        then:
        // 验证异常
        def exception = thrown(BusinessException)
        exception.message == "Expected error message"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试参数化用例
     */
    @Unroll
    def "methodTest_WithParameters_#scenario"() {
        given:
        // 准备测试数据
        
        when:
        def result = testedClass.method(input)
        
        then:
        result == expected
        
        where:
        scenario     | input      | expected
        "正常情况"    | "normal"   | "normalResult"
        "边界情况"    | ""         | "emptyResult"
        "特殊字符"    | "@#$%"     | "specialResult"
    }
}
```

## Action/Controller类测试策略

对于Action或Controller类：

1. **确定测试类型**：
   - 检查方法是否包含super调用
   - 分析父类是否在外部jar包中

2. **创建合适的测试类**：
   - 如需PowerMock：创建`ClassNamePowerMockTest`类
   - 普通测试：创建`ClassNameTest`类

3. **不同方法可能需要不同测试类**：
   - 含super调用的方法放在PowerMockTest类中
   - 其他方法放在普通Test类中

## 场景示例 - 判断是否需要PowerMock

### 示例1：需要PowerMock
```java
// ExternalParentAction类位于外部jar包中
public class MyAction extends ExternalParentAction {
    @Override
    public Result process() {
        // 调用外部jar包中父类的方法
        Result parentResult = super.process();
        // 其他处理...
        return updatedResult;
    }
}
// 结论：需要创建MyActionPowerMockTest类
```

### 示例2：不需要PowerMock
```java
// LocalParentAction类在当前项目中
public class MyController extends LocalParentController {
    @Override
    public void execute() {
        // 虽然调用了super，但父类在当前项目中
        super.execute();
        // 其他处理...
    }
}
// 结论：创建MyControllerTest类即可
```

### 示例3：混合情况
```java
public class ComplexAction extends ExternalParentAction {
    // 方法1：调用了外部jar包中的父类方法
    @Override
    public Result process() {
        return super.process();
    }
    
    // 方法2：没有调用父类方法
    public void localMethod() {
        // 纯本地逻辑
    }
}
/* 结论：
 * 1. 为process方法创建ComplexActionPowerMockTest
 * 2. 为localMethod方法创建ComplexActionTest
 */
```

## 常见错误与最佳实践

### 错误1：PowerMock使用不当
```groovy
// 错误：对不需要PowerMock的方法使用PowerMock
@RunWith(PowerMockRunner)
class SimpleControllerPowerMockTest // 方法中没有super调用

// 正确：仅对需要PowerMock的方法使用PowerMock
class SimpleControllerTest // 常规测试即可
```

### 错误2：测试结构不完整
```groovy
// 错误：缺少关键部分
def "someTest"() {
    // 只有when和then，缺少given
    when:
    def result = service.method()
    then:
    result == expected
}

// 正确：完整的given-when-then结构
def "someTest"() {
    given: "准备测试数据和环境"
    // ...
    when: "执行被测试方法"
    // ...
    then: "验证结果"
    // ...
}
```

### 最佳实践1：明确测试边界
每个测试方法只测试一个功能点，边界清晰，不要尝试在一个测试方法中覆盖过多场景。

### 最佳实践2：合理使用参数化测试
使用@Unroll和where块处理多种输入情况，保持测试代码简洁。 