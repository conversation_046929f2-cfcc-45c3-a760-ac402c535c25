package com.facishare.paas.appframework.fcp;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseImportTemplateAction;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.action.StandardExportAction;
import com.facishare.paas.appframework.core.predef.service.ObjectImportService;
import com.facishare.paas.appframework.core.predef.service.dto.objectImport.GenerateImportTemplate;
import com.facishare.paas.appframework.core.predef.service.dto.objectImport.GetImportObjectList;
import com.facishare.paas.appframework.privilege.ExportUserService;
import com.facishare.paas.appframework.privilege.ExportUserServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.facishare.paas.appframework.core.predef.action.BaseImportTemplateAction.IMPORT_TYPE_ADD;

@Component
@FcpService("bulkimport")
public class ImportController {
    private final ObjectImportService objectImportService;
    @Autowired
    private ActionLocateService actionLocateService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private SerializerManager serializerManager;

    @Autowired
    private ExportUserService exportUserService;

    @Autowired
    public ImportController(ObjectImportService objectImportService) {
        this.objectImportService = objectImportService;
    }

    @FcpMethod("getImportObject")
    public GetImportObjectList.Result getImportObjectList(GetImportObjectList.Arg findImportObjectListArg) {
        ServiceContext context = ContextManager.buildServiceContext("object_import", "get_import_object_list");
        return objectImportService.getImportObjectList(findImportObjectListArg, context);
    }

    @FcpMethod("generateTemplate")
    public BaseImportTemplateAction.Result generateTemplate(GenerateImportTemplate.Arg arg) {
        ActionContext actionContext = ContextManager.buildActionContext(arg.getDescribeApiName(),
                arg.getImportType() == IMPORT_TYPE_ADD ? StandardAction.InsertImportTemplate.name()
                        : StandardAction.UpdateImportTemplate.name());

        JSONSerializer serializer = serializerManager.getSerializer(actionContext.getRequestContext().getContentType());
        String payload = serializer.encode(arg);
        Action action = actionLocateService.locateAction(actionContext, payload);
        BaseImportTemplateAction.Result result = (BaseImportTemplateAction.Result) action.act(action.getArg());
        return result;
    }

    @FcpMethod("exportData")
    public StandardExportAction.Result exportData(StandardExportAction.Arg arg) {
        ActionContext context = ContextManager.buildActionContext(arg.getObject_describe_api_name(), StandardAction.Export.name());
        StandardExportAction.Result result = serviceFacade.triggerAction(context, arg, StandardExportAction.Result.class);
        return result;
    }

    @FcpMethod("exportUser")
    public ExportUserServiceImpl.Result exportUser(ExportUserServiceImpl.Arg arg) {
        RequestContext requestContext = RequestContextManager.getContext();
        ExportUserServiceImpl.Result result = exportUserService.exportUserByRole(requestContext.getUser(), arg.getRoleCode());
        return result;
    }


}
