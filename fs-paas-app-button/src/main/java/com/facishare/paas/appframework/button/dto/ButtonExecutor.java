package com.facishare.paas.appframework.button.dto;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.dto.RuleResult;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;

/**
 * Created by linqiuying on 17/10/12.
 */
public interface ButtonExecutor {
    @Data
    @Builder
    class Arg {
        @SerializedName("describeApiName")
        private String describeApiName;

        @SerializedName("objectDataId")
        private String objectDataId;

        private List<String> dataIds;

        @SerializedName("buttonApiName")
        private String buttonApiName;

        @SerializedName("args")
        private Map<String, Object> args;   // 前台手动绑定的参数

        @SerializedName("objectData")
        private IObjectData objectData;

        @SerializedName("details")
        private Map<String, List<IObjectData>> details;

        private Supplier<Map<String, List<IObjectData>>> detailDataSupplier;

        @SerializedName("relatedDataList")
        Map<String, List<SaveMasterAndDetailData.RelatedObjectData>> relatedDataList;

        //业务方自定义的key
        @SerializedName("bizKey")
        String bizKey;

        private String actionStage;

        private Map<String, Object> actionParams;   // 按照约定, 传递给函数的自定义参数

        private boolean skipValidationFunction;
        // 触发批量函数接口
        private boolean triggerBatchFunc;
        private boolean skippedValidateRule;
        private boolean skipUpdateFieldActionValidation;

        public static Arg of(IObjectData objectData,
                             Map<String, List<IObjectData>> details,
                             Map<String, Object> args) {
            return builder()
                    .objectData(objectData)
                    .details(details)
                    .args(args)
                    .build();
        }

        public static Arg ofTriggerFunction(IObjectData objectData,
                                            Map<String, Object> args,
                                            Map<String, Object> actionParams) {
            return builder()
                    .objectData(objectData)
                    .actionParams(actionParams)
                    .args(args)
                    .build();
        }

        public static Arg ofTriggerFunction(IObjectData objectData,
                                            Map<String, Object> args,
                                            Map<String, List<IObjectData>> objectDetails,
                                            Map<String, Object> actionParams) {
            return builder()
                    .objectData(objectData)
                    .details(objectDetails)
                    .actionParams(actionParams)
                    .args(args)
                    .build();
        }

        public static Arg of(List<String> dataIds, Map<String, Object> args) {
            return builder()
                    .dataIds(dataIds)
                    .args(args)
                    .build();
        }

        public Map<String, List<IObjectData>> toDetails() {
            if (CollectionUtils.notEmpty(details)) {
                return details;
            }
            return Optional.ofNullable(detailDataSupplier)
                    .map(Supplier::get)
                    .orElseGet(Maps::newHashMap);
        }

        public void synchronizeData() {
            if (Objects.nonNull(getObjectData())) {
                setObjectData(ObjectDataExt.synchronize(getObjectData()));
            }
            if (CollectionUtils.notEmpty(getDetails())) {
                Map<String, List<IObjectData>> dataMap = Maps.newHashMap();
                getDetails().forEach((apiName, objectDataList) ->
                        dataMap.put(apiName, ObjectDataExt.synchronize(objectDataList)));
                setDetails(dataMap);
            }
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private IObjectData objectData;
        private Map<String, List<IObjectData>> details;
        private String targetDescribeApiName;
        private boolean hasReturnValue;
        private Object returnValue;
        private String returnType;
        /**
         * 默认为阻断
         */
        @Builder.Default
        private boolean block = true;

        private boolean ignoreSendingRemind;

        private DetailDataMergeResult detailDataMergeResult;
        /**
         * 函数的返回值
         */
        private Object functionResult;

        private RuleResult validateRuleResult;


        public static Result empty() {
            return Result.builder().build();
        }
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    class ExtendInfo {
        private boolean enableValidateRule;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor(staticName = "of")
    class DetailDataMergeResult {
        private List<IObjectData> detailsToAdd = Lists.newArrayList();
        private List<IObjectData> detailsToUpdate = Lists.newArrayList();
        private List<IObjectData> detailsToDelete = Lists.newArrayList();
    }
}
