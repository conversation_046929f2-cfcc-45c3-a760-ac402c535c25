package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.metadata.api.ISelectOption
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IFieldType
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.describe.SelectOne
import com.facishare.paas.metadata.api.search.IFilter
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.search.Filter
import com.facishare.paas.metadata.impl.search.Operator
import com.facishare.paas.metadata.impl.search.SearchTemplate
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

import java.lang.reflect.Field

/**
 * create by zhaoju on 2021/07/19
 */
class FilterExtTest extends Specification {
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def "test"() {
        when:
        IFilter filter = new Filter()
        List fieldValue = [1, 2, "1", "2"]
        filter.setFieldValues(fieldValue)
        def jsonString = filter.toJsonString()
        then:
        println jsonString
        def jsonMap = JacksonUtils.fromJson(jsonString, Map)
        println(JacksonUtils.toJson(jsonMap))
        1 == 1
    }

    def "test validateFilter"() {
        given:
        def describe = new ObjectDescribe(["fields": ["a": ["api_name": "a", "type": "date_time"]]])
        def filterExt = FilterExt.of(Operator.LTE, "a", "20")
        when:
        filterExt.validateFilter(describe)
        then:
        1 == 1

    }

    def "test of factory method with IFilter"() {
        given: "一个IFilter实现"
        def mockFilter = Mock(IFilter) {
            getFieldName() >> "test_field"
            getOperator() >> Operator.EQ
            getFieldValues() >> ["test_value"]
        }

        when: "使用of方法创建FilterExt"
        def filterExt = FilterExt.of(mockFilter)

        then: "应该正确包装过滤器"
        filterExt != null
        filterExt.getFieldName() == "test_field"
        filterExt.getOperator() == Operator.EQ
        filterExt.getFieldValues() == ["test_value"]
    }

    def "test of factory method with operator, fieldName and single value"() {
        when: "使用of方法创建FilterExt"
        def filterExt = FilterExt.of(Operator.EQ, "test_field", "test_value")

        then: "应该正确创建过滤器"
        filterExt != null
        filterExt.getFieldName() == "test_field"
        filterExt.getOperator() == Operator.EQ
        filterExt.getFieldValues() == ["test_value"]
    }

    def "test of factory method with operator, fieldName and value list"() {
        given: "值列表"
        def values = ["value1", "value2", "value3"]

        when: "使用of方法创建FilterExt"
        def filterExt = FilterExt.of(Operator.IN, "test_field", values)

        then: "应该正确创建过滤器"
        filterExt != null
        filterExt.getFieldName() == "test_field"
        filterExt.getOperator() == Operator.IN
        filterExt.getFieldValues() == values
    }

    def "test of factory method with value type"() {
        given: "值列表和值类型"
        def values = ["value1", "value2"]
        def valueType = FilterExt.FilterValueTypes.OBJECT_VARIABLE

        when: "使用of方法创建FilterExt"
        def filterExt = FilterExt.of(Operator.IN, "test_field", values, valueType)

        then: "应该正确创建过滤器"
        filterExt != null
        filterExt.getFieldName() == "test_field"
        filterExt.getOperator() == Operator.IN
        filterExt.getFieldValues() == values
        filterExt.getValueType() == valueType
    }

    def "test buildAllSceneFilter"() {
        when: "构建全场景过滤器"
        def filterExt = FilterExt.buildAllSceneFilter()

        then: "应该返回正确的过滤器"
        filterExt != null
        filterExt.getOperator() == Operator.IN
        filterExt.getFieldName() == "relevant_team_member__c.employee_id__c"
        filterExt.getFieldValues() == [FilterExt.ALL_SCENE_FILTER_VALUE]
    }

    def "test buildCriteriaAllOfMine"() {
        when: "构建我的全部条件过滤器"
        def filterExt = FilterExt.buildCriteriaAllOfMine()

        then: "应该返回正确的过滤器"
        filterExt != null
        filterExt.getOperator() == Operator.IN
        filterExt.getFieldName() == "relevant_team_member__c.employee_id__c"
        filterExt.getFieldValues() == [FilterExt.CRITERIA_ALL_OF_MINE]
    }

    def "test buildObjectApiNameFilter"() {
        given: "对象API名称"
        def apiName = "test_object__c"

        when: "构建对象API名称过滤器"
        def filterExt = FilterExt.buildObjectApiNameFilter(apiName)

        then: "应该返回正确的过滤器"
        filterExt != null
        filterExt.getOperator() == Operator.EQ
        filterExt.getFieldName() == "describe_api_name"
        filterExt.getFieldValues() == [apiName]
    }

    def "test buildGeoFilter"() {
        given: "地理位置参数"
        def apiName = "location_field"
        def longitude = "116.3"
        def latitude = "40.0"
        def distance = "1000"

        when: "构建地理位置过滤器"
        def filter = FilterExt.buildGeoFilter(apiName, longitude, latitude, distance)

        then: "应该返回正确的过滤器"
        filter != null
        filter.getFieldName() == apiName
        filter.getOperator() == Operator.LTE
        filter.getFieldValues() == ["(116.3,40.0)", "1000"]
    }

    @Unroll
    def "test value type checking methods - #method returns #expected"() {
        given: "带有不同值类型的过滤器"
        def filter = Mock(IFilter) {
            getValueType() >> valueType
        }
        def filterExt = FilterExt.of(filter)

        expect: "值类型检查方法应该返回正确结果"
        result == expected

        where:
        method                                   | valueType                                                | result                                             | expected
        "hasConstantValueType"                   | FilterExt.FilterValueTypes.CONSTANT                      | filterExt.hasConstantValueType()                   | true
        "hasConstantValueType"                   | null                                                     | filterExt.hasConstantValueType()                   | true
        "hasObjectVariableValueType"             | FilterExt.FilterValueTypes.OBJECT_VARIABLE               | filterExt.hasObjectVariableValueType()             | true
        "hasObjectVariableValueType"             | FilterExt.FilterValueTypes.CONSTANT                      | filterExt.hasObjectVariableValueType()             | false
        "hasRefObjectVariableValueType"          | FilterExt.FilterValueTypes.REF_OBJECT_VARIABLE           | filterExt.hasRefObjectVariableValueType()          | true
        "hasRefObjectVariableValueType"          | FilterExt.FilterValueTypes.CONSTANT                      | filterExt.hasRefObjectVariableValueType()          | false
        "hasRelatedChainObjectVariableValueType" | FilterExt.FilterValueTypes.RELATED_CHAIN_OBJECT_VARIABLE | filterExt.hasRelatedChainObjectVariableValueType() | true
        "hasLookupI18NVariableValueType"         | FilterExt.FilterValueTypes.LOOKUP_I18N_VARIABLE          | filterExt.hasLookupI18NVariableValueType()         | true
        "hasMasterFieldVariableValueType"        | FilterExt.FilterValueTypes.MASTER_FIELD_VARIABLE         | filterExt.hasMasterFieldVariableValueType()        | true
        "hasNativeObjectVariable"                | FilterExt.FilterValueTypes.NATIVE_OBJECT_VARIABLE        | filterExt.hasNativeObjectVariable()                | true
        "hasFunctionVariableValueType"           | FilterExt.FilterValueTypes.FUNCTION_VARIABLE             | filterExt.hasFunctionVariableValueType()           | true
        "hasSpecialRefObjectVariable"            | FilterExt.FilterValueTypes.SPECIAL_REF_OBJECT_VARIABLE   | filterExt.hasSpecialRefObjectVariable()            | true
    }

    def "test renderVariable"() {
        given: "带有变量的过滤器"
        def filter = Mock(IFilter) {
            getIncludeVariable() >> true
            getFieldValues() >> ["\${variable1}", "\${variable2}", "normal_value"]
        }
        def filterExt = FilterExt.of(filter)
        def variablesResult = [
                "\${variable1}": "replaced_value1",
                "\${variable2}": "replaced_value2"
        ]

        when: "渲染变量"
        filterExt.renderVariable(variablesResult)

        then: "应该替换变量值"
        1 * filter.setFieldValues(["replaced_value1", "replaced_value2", "normal_value"])
    }

    def "test renderVariable with empty result changes operator"() {
        given: "带有变量的过滤器，替换后为空"
        def filter = Mock(IFilter) {
            getIncludeVariable() >> true
            getFieldValues() >> ["\${variable1}"]
        }
        def filterExt = FilterExt.of(filter)
        def variablesResult = [:]

        when: "渲染变量"
        filterExt.renderVariable(variablesResult)

        then: "应该改变操作符为IS"
        1 * filter.setOperator(Operator.IS)
    }

    def "test replaceCurrentUser with simple replacement"() {
        given: "包含当前用户变量的过滤器"
        def filter = Mock(IFilter) {
            getValueType() >> FilterExt.FilterValueTypes.CONSTANT
            getFieldValues() >> [FilterExt.CRITERIA_ALL_OF_MINE, "other_value"]
        }
        def filterExt = FilterExt.of(filter)
        def currentUserId = "user123"

        when: "替换当前用户"
        filterExt.replaceCurrentUser(currentUserId)

        then: "应该替换当前用户ID"
        1 * filter.setFieldValues([currentUserId, "other_value"])
    }

    def "test replaceCurrentUser with includeVariable"() {
        given: "包含当前用户变量的过滤器"
        def filter = Mock(IFilter) {
            getValueType() >> FilterExt.FilterValueTypes.CONSTANT
            getIncludeVariable() >> true
            getFieldValues() >> ["currentLoginUserId__g"]
        }
        def filterExt = FilterExt.of(filter)
        def currentUserId = "user123"

        when: "替换当前用户"
        filterExt.replaceCurrentUser(currentUserId)

        then: "应该替换当前登录用户ID"
        1 * filter.setFieldValues([currentUserId])
    }

    def "test replaceCurrentUser with subquery"() {
        given: "子查询类型的过滤器"
        def searchTemplate = new SearchTemplate()
        def subFilter = new Filter()
        subFilter.setFieldValues([FilterExt.CRITERIA_ALL_OF_MINE])
        searchTemplate.setFilters([subFilter])

        def filter = Mock(IFilter) {
            getValueType() >> 10 // 子查询类型
            getFieldValues() >> [searchTemplate.toJsonString()]
        }
        def filterExt = FilterExt.of(filter)
        def currentUserId = "user123"

        when: "替换当前用户"
        filterExt.replaceCurrentUser(currentUserId)

        then: "应该处理子查询中的当前用户替换"
        1 * filter.getFieldValues() >> [searchTemplate.toJsonString()]
        1 * filter.setFieldValues(_)
    }

    def "test getVariableNameInValues"() {
        given: "包含变量的过滤器"
        def filter = Mock(IFilter) {
            getFieldValues() >> ["\$variable_name"]
        }
        def filterExt = FilterExt.of(filter)

        when: "获取变量名"
        def variableName = filterExt.getVariableNameInValues()

        then: "应该返回正确的变量名"
        variableName == "variable_name"
    }

    def "test getVariableNameInValues with dot notation"() {
        given: "包含点号的变量"
        def filter = Mock(IFilter) {
            getFieldValues() >> ["\$object.field"]
        }
        def filterExt = FilterExt.of(filter)

        when: "获取变量名"
        def variableName = filterExt.getVariableNameInValues()

        then: "应该返回点号后的部分"
        variableName == "field"
    }

    def "test getVariableNameInValuesAndIsInMaster"() {
        given: "包含主对象变量的过滤器"
        def filter = Mock(IFilter) {
            getFieldValues() >> ["\$master.field"]
        }
        def filterExt = FilterExt.of(filter)

        when: "获取变量名和主对象标识"
        def result = filterExt.getVariableNameInValuesAndIsInMaster()

        then: "应该返回正确的元组"
        result.getFirst() == true
        result.getSecond() == "field"
    }

    def "test getVariableNameInValuesAndIsInMaster without master"() {
        given: "不包含主对象的变量"
        def filter = Mock(IFilter) {
            getFieldValues() >> ["\$field"]
        }
        def filterExt = FilterExt.of(filter)

        when: "获取变量名和主对象标识"
        def result = filterExt.getVariableNameInValuesAndIsInMaster()

        then: "应该返回正确的元组"
        result.getFirst() == false
        result.getSecond() == "field"
    }

    def "test getVariableNames"() {
        given: "包含引用对象变量的过滤器"
        def filter = Mock(IFilter) {
            getFieldName() >> "test_field"
            getFieldValues() >> ["\$reference_field__r.target_field"]
            getValueType() >> FilterExt.FilterValueTypes.REF_OBJECT_VARIABLE
        }
        def filterExt = FilterExt.of(filter)
        def masterApiName = "master_object"
        def subApiName = "sub_object"

        when: "获取变量名"
        def result = filterExt.getVariableNames(masterApiName, subApiName)

        then: "应该返回正确的变量名映射"
        result.containsKey(subApiName)
        result.containsKey(masterApiName)
        result.get(subApiName).contains("test_field")
        result.get(subApiName).contains("reference_field")
        result.get(masterApiName).contains("target_field")
    }

    def "test isRectangleRelation"() {
        given: "四角关系的过滤器"
        def filter = Mock(IFilter) {
            getValueType() >> FilterExt.FilterValueTypes.REF_OBJECT_VARIABLE
            getFieldValues() >> ["\$field1.field2"]
            getFieldName() >> "normal_field"
            getFieldValueType() >> IFieldType.OBJECT_REFERENCE
        }
        def filterExt = FilterExt.of(filter)

        when: "检查是否为四角关系"
        def result = filterExt.isRectangleRelation()

        then: "应该返回true"
        result == true
    }

    def "test isTriangleRelation"() {
        given: "三角关系的过滤器"
        def filter = Mock(IFilter) {
            getValueType() >> FilterExt.FilterValueTypes.REF_OBJECT_VARIABLE
            getFieldValues() >> ["\$field1"]
            getFieldName() >> "normal_field"
            getFieldValueType() >> IFieldType.OBJECT_REFERENCE
        }
        def filterExt = FilterExt.of(filter)

        when: "检查是否为三角关系"
        def result = filterExt.isTriangleRelation()

        then: "应该返回true"
        result == true
    }

    def "test getFieldApiNameWithRectangleRelation"() {
        given: "四角关系的过滤器"
        def filter = Mock(IFilter) {
            getValueType() >> FilterExt.FilterValueTypes.REF_OBJECT_VARIABLE
            getFieldValues() >> ["\$ref_field__r.target_field"]
            getFieldName() >> "normal_field"
            getFieldValueType() >> IFieldType.OBJECT_REFERENCE
        }
        def filterExt = FilterExt.of(filter)

        when: "获取四角关系的字段API名"
        def result = filterExt.getFieldApiNameWithRectangleRelation()

        then: "应该返回正确的字段名"
        result == "target_field"
    }

    def "test getFieldApiNameWithTriangleRelation"() {
        given: "三角关系的过滤器"
        def filter = Mock(IFilter) {
            getValueType() >> FilterExt.FilterValueTypes.REF_OBJECT_VARIABLE
            getFieldValues() >> ["\$target_field"]
            getFieldName() >> "normal_field"
            getFieldValueType() >> IFieldType.OBJECT_REFERENCE
        }
        def filterExt = FilterExt.of(filter)

        when: "获取三角关系的字段API名"
        def result = filterExt.getFieldApiNameWithTriangleRelation()

        then: "应该返回正确的字段名"
        result == "target_field"
    }

    def "test getFieldApiNameWithPentagonRelation"() {
        given: "五角关系的过滤器"
        def filter = Mock(IFilter) {
            getValueType() >> FilterExt.FilterValueTypes.RELATED_CHAIN_OBJECT_VARIABLE
            getFieldValues() >> ["\$->object.field<-"]
        }
        def filterExt = FilterExt.of(filter)

        when: "获取五角关系的字段API名"
        def result = filterExt.getFieldApiNameWithPentagonRelation()

        then: "应该返回正确的字段名"
        result == "field"
    }

    def "test setMasterFieldWhenHasSpecialRefObjectVariable"() {
        given: "特殊引用对象变量的过滤器"
        def filter = Mock(IFilter) {
            getValueType() >> FilterExt.FilterValueTypes.SPECIAL_REF_OBJECT_VARIABLE
        }
        def filterExt = FilterExt.of(filter)

        when: "设置主字段标识"
        filterExt.setMasterFieldWhenHasSpecialRefObjectVariable()

        then: "应该设置isMasterField为true"
        1 * filter.setIsMasterField(true)
    }

    def "test equals method"() {
        given: "两个相同的过滤器"
        def filter1 = FilterExt.of(Operator.EQ, "field1", "value1")
        def filter2 = FilterExt.of(Operator.EQ, "field1", "value1")
        def filter3 = FilterExt.of(Operator.EQ, "field2", "value1")

        expect: "equals方法应该正确比较"
        FilterExt.equals(filter1.getFilter(), filter2.getFilter()) == true
        FilterExt.equals(filter1.getFilter(), filter3.getFilter()) == false
    }

    def "test isDefaultAllSceneFilter"() {
        given: "默认全场景过滤器"
        def defaultFilter = FilterExt.buildAllSceneFilter()
        def otherFilter = FilterExt.of(Operator.EQ, "other_field", "value")

        expect: "应该正确识别默认全场景过滤器"
        FilterExt.isDefaultAllSceneFilter(defaultFilter.getFilter()) == true
        FilterExt.isDefaultAllSceneFilter(otherFilter.getFilter()) == false
    }

    def "test isDefaultSceneFilter"() {
        given: "默认场景过滤器"
        def allSceneFilter = FilterExt.buildAllSceneFilter()
        def criteriaFilter = FilterExt.buildCriteriaAllOfMine()
        def otherFilter = FilterExt.of(Operator.EQ, "other_field", "value")

        expect: "应该正确识别默认场景过滤器"
        FilterExt.isDefaultSceneFilter(allSceneFilter.getFilter()) == true
        FilterExt.isDefaultSceneFilter(criteriaFilter.getFilter()) == true
        FilterExt.isDefaultSceneFilter(otherFilter.getFilter()) == false
    }

    def "test getFieldApiName"() {
        given: "包含点号的字段名过滤器"
        def filter = Mock(IFilter) {
            getFieldName() >> "object.field"
        }
        def filterExt = FilterExt.of(filter)

        when: "获取字段API名"
        def result = filterExt.getFieldApiName()

        then: "应该返回点号前的部分"
        result == "object"
    }

    def "test handleFilterValueReturnSkipData with empty value"() {
        given: "过滤器"
        def filter = Mock(IFilter) {
            getOperator() >> Operator.EQ
        }
        def filterExt = FilterExt.of(filter)

        when: "处理空值"
        def result = filterExt.handleFilterValueReturnSkipData(null)

        then: "应该转换操作符并返回false"
        1 * filter.setOperator(Operator.IS)
        1 * filter.setFieldValues([null])
        1 * filter.setValueType(FilterExt.FilterValueTypes.CONSTANT)
        result == false
    }

    def "test handleFilterValueReturnSkipData with non-empty value"() {
        given: "过滤器"
        def filter = Mock(IFilter)
        def filterExt = FilterExt.of(filter)

        when: "处理非空值"
        def result = filterExt.handleFilterValueReturnSkipData("test_value")

        then: "应该设置值并返回false"
        1 * filter.setFieldValues(["test_value"])
        1 * filter.setValueType(FilterExt.FilterValueTypes.CONSTANT)
        result == false
    }

    def "test handleFilterValueReturnSkipData with list value"() {
        given: "过滤器"
        def filter = Mock(IFilter)
        def filterExt = FilterExt.of(filter)
        def listValue = ["value1", "value2"]

        when: "处理列表值"
        def result = filterExt.handleFilterValueReturnSkipData(listValue)

        then: "应该转换为字符串列表"
        1 * filter.setFieldValues(["value1", "value2"])
        1 * filter.setValueType(FilterExt.FilterValueTypes.CONSTANT)
        result == false
    }

    def "test isEffectiveFilter with LIKE operator"() {
        given: "LIKE操作符的过滤器"
        def filter = Mock(IFilter) {
            getOperator() >> Operator.LIKE
            getFieldValues() >> fieldValues
        }
        def filterExt = FilterExt.of(filter)

        expect: "应该正确判断过滤器是否有效"
        filterExt.isEffectiveFilter() == expected

        where:
        fieldValues    | expected
        ["test_value"] | true
        [""]           | false
        []             | false
        null           | false
    }

    def "test isEffectiveFilter with non-LIKE operator"() {
        given: "非LIKE操作符的过滤器"
        def filter = Mock(IFilter) {
            getOperator() >> Operator.EQ
        }
        def filterExt = FilterExt.of(filter)

        when: "检查过滤器是否有效"
        def result = filterExt.isEffectiveFilter()

        then: "应该返回true"
        result == true
    }

    def "test usingLastModifiedTime"() {
        given: "不同字段名的过滤器"
        def filter = Mock(IFilter) {
            getFieldName() >> fieldName
        }
        def filterExt = FilterExt.of(filter)

        expect: "应该正确识别使用最后修改时间的过滤器"
        filterExt.usingLastModifiedTime() == expected

        where:
        fieldName                   | expected
        "last_modified_time"        | true
        "object.last_modified_time" | true
        "other_field"               | false
        "last_modified_time_other"  | false
    }

    def "test isValidTimeStamp"() {
        expect: "应该正确验证时间戳"
        FilterExt.isValidTimeStamp(timestamp) == expected

        where:
        timestamp    | expected
        "1234567890" | true
        "-123456"    | true
        "abc123"     | false
        ""           | false
        null         | true
        "123.456"    | false
    }

    def "test handleFilter"() {
        given: "过滤器列表和对象描述"
        def activeField = Mock(IFieldDescribe) {
            isActive() >> true
        }
        def inactiveField = Mock(IFieldDescribe) {
            isActive() >> false
        }
        def describe = Mock(IObjectDescribe)
        def describeExt = Mock(ObjectDescribeExt) {
            getFieldDescribeSilently("active_field") >> Optional.of(activeField)
            getFieldDescribeSilently("inactive_field") >> Optional.of(inactiveField)
            getFieldDescribeSilently("nonexistent_field") >> Optional.empty()
        }

        // Mock静态方法
        GroovyMock(ObjectDescribeExt, global: true)
        ObjectDescribeExt.of(describe) >> describeExt

        def filters = [
                createFilter("active_field", ["value1"]),
                createFilter("inactive_field", ["value2"]),
                createFilter("nonexistent_field", ["value3"]),
                createFilter("object.field", ["value4"]), // 包含点号的字段名
        ]

        when: "处理过滤器"
        def result = FilterExt.handleFilter(filters, describe)

        then: "应该只保留活跃字段和包含点号的字段"
        result.size() == 2
        result.any { it.getFieldName() == "active_field" }
        result.any { it.getFieldName() == "object.field" }
    }

    def "test handleSelectFilter"() {
        given: "选择字段和过滤器"
        def option1 = Mock(ISelectOption) { getValue() >> "option1" }
        def option2 = Mock(ISelectOption) { getValue() >> "option2" }
        def selectField = Mock(SelectOne) {
            getSelectOptions() >> [option1, option2]
        }
        def filter = createFilter("select_field", ["option1", "option3", "option2"])

        // Mock FieldDescribeExt
        GroovyMock(FieldDescribeExt, global: true)
        FieldDescribeExt.of(selectField) >> Mock(FieldDescribeExt) {
            isSelectOne() >> true
            isSelectMany() >> false
        }

        when: "处理选择过滤器"
        def result = FilterExt.handleSelectFilter(filter, selectField)

        then: "应该移除无效选项"
        result == true
        // 验证过滤器值被修改，移除了无效的option3
        filter.getFieldValues() == ["option1", "option2"]
    }

    def "test FiltersContainer"() {
        given: "过滤器列表"
        def filter1 = createFilter("field1", ["value1"])
        filter1.setFilterGroup("group1")
        def filter2 = createFilter("field2", ["value2"])
        filter2.setFilterGroup("group1")
        def filter3 = createFilter("field3", ["value3"])
        filter3.setFilterGroup("group2")

        when: "使用FiltersContainer分组"
        def container = FilterExt.FiltersContainer.of([filter1, filter2, filter3])
        def groups = container.group().collect()

        then: "应该正确分组"
        groups.size() == 2
        groups.any { it.size() == 2 } // group1有两个过滤器
        groups.any { it.size() == 1 } // group2有一个过滤器
    }

    private IFilter createFilter(String fieldName, List<String> values) {
        def filter = new Filter()
        filter.setFieldName(fieldName)
        filter.setFieldValues(values)
        filter.setOperator(Operator.EQ)
        return filter
    }

    def "test validateFilter with valid number field"() {
        given: "数字字段和有效的数字过滤器"
        def numberField = Mock(IFieldDescribe) {
            getType() >> "number"
        }
        def describe = Mock(IObjectDescribe)
        def describeExt = Mock(ObjectDescribeExt) {
            getFieldDescribeSilently("number_field") >> Optional.of(numberField)
        }

        // Mock静态方法
        GroovyMock(ObjectDescribeExt, global: true)
        ObjectDescribeExt.of(describe) >> describeExt
        GroovyMock(ObjectDescribeExt, global: true)
        ObjectDescribeExt.NUMBER_TYPE_FIELD >> ["number", "currency"]

        def filter = createFilter("number_field", ["123.45"])
        def filterExt = FilterExt.of(filter)

        when: "验证过滤器"
        filterExt.validateFilter(describe)

        then: "应该验证通过，不抛异常"
        noExceptionThrown()
    }

    def "test validateFilter with invalid number field"() {
        given: "数字字段和无效的数字过滤器"
        def numberField = Mock(IFieldDescribe) {
            getType() >> "number"
        }
        def describe = Mock(IObjectDescribe)
        def describeExt = Mock(ObjectDescribeExt) {
            getFieldDescribeSilently("number_field") >> Optional.of(numberField)
        }

        // Mock静态方法
        GroovyMock(ObjectDescribeExt, global: true)
        ObjectDescribeExt.of(describe) >> describeExt
        GroovyMock(ObjectDescribeExt, global: true)
        ObjectDescribeExt.NUMBER_TYPE_FIELD >> ["number", "currency"]

        def filter = createFilter("number_field", ["invalid_number"])
        filter.setValueType(FilterExt.FilterValueTypes.CONSTANT)
        def filterExt = FilterExt.of(filter)

        when: "验证过滤器"
        filterExt.validateFilter(describe)

        then: "应该抛出验证异常"
        thrown(ValidateException)
    }

    def "test validateFilter with nonexistent field"() {
        given: "不存在的字段"
        def describe = Mock(IObjectDescribe)
        def describeExt = Mock(ObjectDescribeExt) {
            getFieldDescribeSilently("nonexistent_field") >> Optional.empty()
        }

        // Mock静态方法
        GroovyMock(ObjectDescribeExt, global: true)
        ObjectDescribeExt.of(describe) >> describeExt

        def filter = createFilter("nonexistent_field__c", ["value"])
        def filterExt = FilterExt.of(filter)

        when: "验证过滤器"
        filterExt.validateFilter(describe)

        then: "应该抛出验证异常"
        thrown(ValidateException)
    }
}
