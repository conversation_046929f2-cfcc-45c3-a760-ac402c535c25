package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.globalvariable.*;
import com.facishare.paas.appframework.metadata.GlobalVarServiceImpl;
import com.facishare.paas.appframework.metadata.dto.GlobalVariableResult;
import com.facishare.paas.metadata.api.describe.IGlobalVariableDescribe;
import com.facishare.paas.metadata.impl.describe.GlobalVariableDescribe;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 全局变量服务
 * <p>
 * Created by liyigua<PERSON> on 2017/10/11.
 */

@ServiceModule("global_variable")
@Component
public class GlobalVariableService {
    @Autowired
    private GlobalVarServiceImpl globalVarService;

    @ServiceMethod("create")
    public CreateGlobalVariable.Result createGlobalVarialbe(CreateGlobalVariable.Arg arg, ServiceContext context) {
        CreateGlobalVariable.Result result = new CreateGlobalVariable.Result();

        IGlobalVariableDescribe globalVariableDescribe = new GlobalVariableDescribe();
        globalVariableDescribe.fromJsonString(arg.getJson_data());
        globalVariableDescribe.setTenantId(context.getTenantId());
        GlobalVariableResult globalVariableResult = globalVarService.create(globalVariableDescribe);
        result.setSuccess(globalVariableResult.isSuccess());
        return result;
    }

    @ServiceMethod("update")
    public UpdateGlobalVariable.Result updateGlobalVariable(UpdateGlobalVariable.Arg arg, ServiceContext context) {
        UpdateGlobalVariable.Result result = new UpdateGlobalVariable.Result();
        IGlobalVariableDescribe globalVariableDescribe = new GlobalVariableDescribe();
        globalVariableDescribe.fromJsonString(arg.getJson_data());
        globalVariableDescribe.setTenantId(context.getTenantId());
        GlobalVariableResult globalVariableResult = globalVarService.update(globalVariableDescribe);
        result.setSuccess(globalVariableResult.isSuccess());
        return result;
    }

    @ServiceMethod("delete")
    public DeleteGlobalVariable.Result deleteGloableVarialbe(DeleteGlobalVariable.Arg arg, ServiceContext context) {
        DeleteGlobalVariable.Result result = new DeleteGlobalVariable.Result();
        GlobalVariableResult globalVariableResult = globalVarService.delete(arg.getApiName(), context.getTenantId());
        result.setSuccess(globalVariableResult.isSuccess());
        return result;
    }

    @ServiceMethod("findGlobalVariableList")
    public GetGlobalVariableList.Result getGlobalVariableList(GetGlobalVariableList.Arg arg, ServiceContext context) {
        GetGlobalVariableList.Result result = new GetGlobalVariableList.Result();
        GlobalVariableResult globalVariableResult = globalVarService.findGlobalVariableList(arg.getLabel(), arg.getRealTimeTrans(), context.getTenantId());
        result.setSuccess(globalVariableResult.isSuccess());
        result.setGlobalVariableList(globalVariableResult.getGlobalVariableList());
        return result;
    }

    @ServiceMethod("findGlobalVariableInfo")
    public GetGloableVariableDetail.Result getGlobalVariableDetail(GetGloableVariableDetail.Arg arg, ServiceContext context) {
        GetGlobalVariableDetailAssignLang.Arg build = GetGlobalVariableDetailAssignLang.Arg.builder()
                .apiName(arg.getApiName())
                .realTimeTrans(arg.getRealTimeTrans())
                .build();
        return getGlobalVariableDetailAssignLang(build, context);
    }

    @ServiceMethod("findGlobalVariableInfoAssignLang")
    public GetGloableVariableDetail.Result getGlobalVariableDetailAssignLang(GetGlobalVariableDetailAssignLang.Arg arg, ServiceContext context) {
        GetGloableVariableDetail.Result result = new GetGloableVariableDetail.Result();
        IGlobalVariableDescribe globalVariableInfo = globalVarService.findGlobalVariableInfo(arg.getApiName(), arg.getRealTimeTrans(), arg.getLang(), context.getTenantId());
        result.setSuccess(true);
        if (globalVariableInfo != null) {
            result.setGlobal_Variable(((GlobalVariableDescribe) globalVariableInfo).getContainerDocument());
        }
        return result;
    }
}
