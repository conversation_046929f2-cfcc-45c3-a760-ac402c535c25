package com.facishare.paas.appframework.core.predef.domain;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.domain.ActionDomainPlugin;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.rest.BaseAPIResult;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by zhouweirong on 2021/10/18.
 */
public interface AddActionDomainPlugin extends ActionDomainPlugin<AddActionDomainPlugin.Arg, AddActionDomainPlugin.Result> {
    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    class Arg extends ActionDomainPlugin.Arg {
        private ObjectDataDocument objectData;
        private Map<String, List<ObjectDataDocument>> detailObjectData;
        private Boolean isApprovalFlowStartSuccess;
    }

    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    class Result extends DomainPlugin.Result {
        private ObjectDataDocument objectDataToUpdate;
        private Map<String, List<ObjectDataDocument>> detailsToAdd;
        private Map<String, List<ObjectDataDocument>> detailsToUpdate;
        private Map<String, List<String>> detailsToDelete;
        //自定义的审批流回调参数，触发审批流时放入callbackData，审批流回调时再取出来用
        private Map<String, Object> customCallbackData;

        public IObjectData masterDataToUpdate() {
            if (Objects.isNull(objectDataToUpdate)) {
                return null;
            }
            return objectDataToUpdate.toObjectData();
        }
    }

    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    class RestResult extends BaseAPIResult {
        private Result data;
    }
}
