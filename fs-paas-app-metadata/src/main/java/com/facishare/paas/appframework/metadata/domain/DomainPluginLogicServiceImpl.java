package com.facishare.paas.appframework.metadata.domain;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer;
import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestType;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ReferenceLogicService;
import com.facishare.paas.appframework.metadata.domain.DomainPluginDefinition.Action;
import com.facishare.paas.appframework.metadata.relation.SourceTypes;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginInstance;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2021/11/10.
 */
@Slf4j
@Service("domainPluginLogicService")
public class DomainPluginLogicServiceImpl implements DomainPluginLogicService {

    @Autowired
    private IRepository<DomainPluginInstance> repository;

    @Autowired
    private LicenseService licenseService;

    @Autowired
    private ConfigService configService;

    @Autowired
    private ReferenceLogicService referenceLogicService;

    @Autowired
    private AppDefaultRocketMQProducer domainPluginInstanceMQProducer;

    @Autowired
    private DescribeLogicService describeLogicService;

    @Override
    public QueryResult<DomainPluginInstance> findPluginInstanceByQuery(User user, Query query) {
        query.and(FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter());
        QueryResult<DomainPluginInstance> result = repository.findByQuery(user, query, DomainPluginInstance.class);
        result.getData().forEach(
                x -> {
                    x.setPredefined(DomainPluginDefinitionHolder.checkIfPredefined(x.getTenantId(), x));
                    // 从数据库查询用的不是建造器，没有默认值
                    x.setExcludeRecordType(Objects.isNull(x.getExcludeRecordType()) ? Boolean.FALSE : x.getExcludeRecordType());
                }
        );
        return result;
    }

    @Override
    public List<DomainPluginDefinition> findAvailablePluginDefinitionsForManagement(String tenantId, String objectApiName) {
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(tenantId, objectApiName);
        List<DomainPluginDefinition> pluginDefinitions = DomainPluginDefinitionHolder.getDefinitionForManagement();
        List<DomainPluginInstance> pluginInstances = findPluginInstances(tenantId, Lists.newArrayList(objectApiName),
                null, null, true);
        return filterPluginDefinitions(pluginDefinitions, tenantId, objectDescribe, pluginInstances, null);
    }

    private List<DomainPluginDefinition> filterPluginDefinitions(List<DomainPluginDefinition> pluginDefinitions,
                                                                 String tenantId,
                                                                 IObjectDescribe objectDescribe,
                                                                 List<DomainPluginInstance> pluginInstances,
                                                                 String status) {
        return DomainPluginDefinitionFilter.builder()
                .pluginDefinitionList(pluginDefinitions)
                .pluginInstanceList(pluginInstances)
                .tenantId(tenantId)
                .objectDescribe(objectDescribe)
                .licenseService(licenseService)
                .configService(configService)
                .status(status)
                .build()
                .doFilter()
                .getValidPluginDefinitionList();
    }

    // 按照插件定义信息过滤企业下适用于某个对象的插件
    @Override
    public List<DomainPluginInstance> filterPluginInstancesByDefinitions(String tenantId, List<DomainPluginInstance> instanceList) {
        return filterPluginInstancesByDefinitionStatus(tenantId, instanceList, null);
    }

    private List<DomainPluginInstance> filterPluginInstancesByDefinitionStatus(String tenantId, List<DomainPluginInstance> instanceList, String status) {
        if (CollectionUtils.empty(instanceList)) {
            return instanceList;
        }
        Set<String> objectApiNames = instanceList.stream().map(DomainPluginInstance::getRefObjectApiName).collect(Collectors.toSet());
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjectsWithoutCopy(tenantId, objectApiNames);
        List<DomainPluginDefinition> allDefinitions = instanceList.stream().map(DomainPluginInstance::getPluginApiName).distinct()
                .map(DomainPluginDefinitionHolder::getPluginDefinition).filter(Objects::nonNull).collect(Collectors.toList());

        List<DomainPluginInstance> result = instanceList.stream().filter(x -> describeMap.containsKey(x.getRefObjectApiName()))
                .collect(Collectors.toList());
        describeMap.forEach((objectApiName, describe) -> {
            Set<String> definitionApiNames = instanceList.stream().filter(x -> objectApiName.equals(x.getRefObjectApiName()))
                    .map(DomainPluginInstance::getPluginApiName).collect(Collectors.toSet());
            List<DomainPluginDefinition> definitions = allDefinitions.stream()
                    .filter(x -> definitionApiNames.contains(x.getApiName())).collect(Collectors.toList());
            Set<String> availableDefinitionApiNames = filterPluginDefinitions(definitions, tenantId, describe, null, status).stream()
                    .map(DomainPluginDefinition::getApiName).collect(Collectors.toSet());
            result.removeIf(x -> objectApiName.equals(x.getRefObjectApiName()) && !availableDefinitionApiNames.contains(x.getPluginApiName()));
        });
        return result;
    }

    @Override
    public Map<String, DomainPluginDefinition> findPluginDefinitionByApiNames(List<String> pluginApiNames, boolean processI18NProps) {
        Map<String, DomainPluginDefinition> result = Maps.newHashMap();
        if (CollectionUtils.empty(pluginApiNames)) {
            return result;
        }
        pluginApiNames.forEach(pluginApiName -> {
            DomainPluginDefinition definition = DomainPluginDefinitionHolder.getPluginDefinition(pluginApiName);
            if (Objects.nonNull(definition)) {
                if (processI18NProps) {
                    definition.processI18NProps();
                }
                result.put(pluginApiName, definition);
            }
        });
        return result;
    }

    @Override
    public Map<String, String> findPluginLabelByApiNames(List<String> pluginApiNames) {
        Map<String, String> pluginLabelMap = Maps.newHashMap();
        if (CollectionUtils.empty(pluginApiNames)) {
            return pluginLabelMap;
        }
        pluginApiNames.forEach(pluginApiName -> {
            String pluginLabel = DomainPluginDefinitionHolder.getPluginLabel(pluginApiName);
            pluginLabelMap.put(pluginApiName, pluginLabel);
        });
        return pluginLabelMap;
    }

    @Override
    public List<DomainPluginInstance> findPluginInstances(String tenantId, String objectApiName, String agentType, List<String> recordTypeList) {
        if (Strings.isNullOrEmpty(objectApiName)) {
            return Lists.newArrayList();
        }
        return findPluginInstances(tenantId, Lists.newArrayList(objectApiName), agentType, recordTypeList);
    }

    @Override
    public List<DomainPluginInstance> findPluginInstances(String tenantId, List<String> objectApiNames, String agentType, List<String> recordTypeList) {
        return findPluginInstances(tenantId, objectApiNames, agentType, recordTypeList, false);
    }

    @Override
    public List<DomainPluginInstance> findPluginInstancesByRunTimeStatus(String tenantId, List<String> objectApiNames,
                                                                         String agentType, List<String> recordTypeList) {
        List<DomainPluginInstance> pluginInstances = findPluginInstances(tenantId, objectApiNames, agentType, recordTypeList);
        return filterPluginInstancesByDefinitionStatus(tenantId, pluginInstances, DomainPluginDefinitionFilter.STATUS_TYPE_RUN_TIME);
    }

    private List<DomainPluginInstance> findPluginInstances(String tenantId, List<String> objectApiNames, String agentType,
                                                           List<String> recordTypeList, boolean includeDisable) {
        if (CollectionUtils.empty(objectApiNames)) {
            return Lists.newArrayList();
        }
        Query query = Query.builder().limit(AppFrameworkConfig.DEFAULT_MAX_QUERY_LIMIT).needReturnCountNum(false).needReturnQuote(false).build();
        query.and(FilterExt.of(Operator.IN, DomainPluginInstance.REF_OBJECT_API_NAME, objectApiNames).getFilter());
        if (!includeDisable) {
            query.and(FilterExt.of(Operator.EQ, DomainPluginInstance.IS_ACTIVE, String.valueOf(Boolean.TRUE)).getFilter());
        }
        List<DomainPluginInstance> instances = findPluginInstanceByQuery(User.systemUser(tenantId), query).getData();
        instances.removeIf(x -> !x.containsRecordType(recordTypeList));

        //合并预设对象插件
        List<DomainPluginInstance> predefineInstances = DomainPluginDefinitionHolder.getPluginInstancesByRecordType(tenantId, objectApiNames, agentType, recordTypeList);
        predefineInstances.removeIf(x -> instances.stream().anyMatch(y -> y.keyEquals(x)));
        instances.addAll(predefineInstances);

        //按照order从大到小排序
        return instances.stream()
                .sorted(Collections.reverseOrder(Comparator.comparingInt(x -> DomainPluginDefinitionHolder.getPluginOrder(x.getPluginApiName()))))
                .collect(Collectors.toList());
    }

    @Override
    public List<SimpleDomainPluginDescribe> findSimplePluginByActionCode(String tenantId, String objectApiName, String actionCode,
                                                                         List<String> recordTypeList, RequestType requestType) {
        List<DomainPluginInstance> instances = findPluginInstances(tenantId, objectApiName, DomainPluginInstance.AgentTypes.SERVER, recordTypeList);
        if (CollectionUtils.empty(instances)) {
            return Lists.newArrayList();
        }
        List<DomainPluginInstance> instancesByFilters = filterPluginInstancesByDefinitionStatus(tenantId, instances, DomainPluginDefinitionFilter.STATUS_TYPE_RUN_TIME);
        List<SimpleDomainPluginDescribe> result = Lists.newArrayList();
        instancesByFilters.forEach(instance -> {
            //只有domain插件支持后端接口调用
            if (DomainPluginType.Domain != DomainPluginDefinitionHolder.getPluginType(instance.getPluginApiName())) {
                return;
            }
            Map<String, Action> actions = DomainPluginDefinitionHolder.getActions(instance.getPluginApiName(), actionCode, requestType);
            if (CollectionUtils.empty(actions)) {
                return;
            }
            SimpleDomainPluginDescribe describe = new SimpleDomainPluginDescribe();
            describe.setApiName(instance.getPluginApiName());
            describe.setParams(instance.getPluginParam());
            describe.setActions(actions);
            describe.setRecordTypeList(instance.getRecordTypeList());
            result.add(describe);
        });
        return result;
    }

    @Override
    public DomainPluginInstance findPluginInstanceByApiName(String tenantId, String objectApiName, String pluginApiName) {
        //配置中心和数据库都会查询
        return findPluginInstanceByApiName(tenantId, objectApiName, pluginApiName, null, false);
    }

    @Override
    public DomainPluginInstance findPluginInstanceByApiName(String tenantId, String objectApiName, String pluginApiName,
                                                            String fieldApiName, boolean includeDisable) {
        DomainPluginInstance instance = findPluginInstanceByApiNameFromDB(tenantId, objectApiName, pluginApiName,
                fieldApiName, includeDisable);
        if (Objects.isNull(instance)) {
            instance = DomainPluginDefinitionHolder.getPluginInstanceByApiName(pluginApiName, objectApiName, fieldApiName);
        }
        return instance;
    }

    private DomainPluginInstance findPluginInstanceByApiNameFromDB(String tenantId, String objectApiName, String pluginApiName,
                                                                   String fieldApiName, boolean includeDisable) {
        if (Strings.isNullOrEmpty(objectApiName) || Strings.isNullOrEmpty(pluginApiName)) {
            return null;
        }
        Query query = Query.builder().needReturnCountNum(false).needReturnQuote(false).build();
        query.and(FilterExt.of(Operator.EQ, DomainPluginInstance.REF_OBJECT_API_NAME, objectApiName).getFilter());
        query.and(FilterExt.of(Operator.EQ, DomainPluginInstance.PLUGIN_API_NAME, pluginApiName).getFilter());
        if (!DomainPluginInstance.isFieldEmpty(fieldApiName)) {
            query.and(FilterExt.of(Operator.EQ, DomainPluginInstance.FIELD_API_NAME, fieldApiName).getFilter());
        } else {
            SearchQuery searchQuery = SearchQueryImpl.filter(FilterExt.of(Operator.EQ, DomainPluginInstance.FIELD_API_NAME, DomainPluginInstance.NONE).getFilter())
                    .or(FilterExt.of(Operator.IS, DomainPluginInstance.FIELD_API_NAME, "").getFilter());
            query.and(searchQuery);
        }
        if (!includeDisable) {
            query.and(FilterExt.of(Operator.EQ, DomainPluginInstance.IS_ACTIVE, String.valueOf(Boolean.TRUE)).getFilter());
        }
        List<DomainPluginInstance> instances = findPluginInstanceByQuery(User.systemUser(tenantId), query).getData();
        DomainPluginInstance instance = instances.stream().findFirst().orElse(null);
        return instance;
    }

    @Override
    public List<DomainPluginInstance> findPluginInstanceByPluginApiName(String tenantId, String pluginApiName) {
        if (Strings.isNullOrEmpty(pluginApiName)) {
            return Lists.newArrayList();
        }
        Query query = Query.builder().limit(AppFrameworkConfig.DEFAULT_MAX_QUERY_LIMIT).needReturnCountNum(false).needReturnQuote(false).build();
        query.and(FilterExt.of(Operator.EQ, DomainPluginInstance.PLUGIN_API_NAME, pluginApiName).getFilter());
        query.and(FilterExt.of(Operator.EQ, DomainPluginInstance.IS_ACTIVE, String.valueOf(Boolean.TRUE)).getFilter());
        List<DomainPluginInstance> instances = findPluginInstanceByQuery(User.systemUser(tenantId), query).getData();

        //合并预设对象插件
        List<DomainPluginInstance> predefineInstances = DomainPluginDefinitionHolder.getPluginInstancesByPluginApiName(tenantId, pluginApiName);
        predefineInstances.removeIf(x -> instances.stream().anyMatch(y -> y.keyEquals(x)));
        instances.addAll(predefineInstances);

        return instances;
    }

    @Override
    public DomainPluginInstance findPluginInstanceById(String tenantId, String id, boolean includeDisable) {
        Query query = Query.builder().needReturnCountNum(false).needReturnQuote(false).build();
        query.and(FilterExt.of(Operator.EQ, IObjectData.ID, id).getFilter());
        if (!includeDisable) {
            query.and(FilterExt.of(Operator.EQ, DomainPluginInstance.IS_ACTIVE, String.valueOf(Boolean.TRUE)).getFilter());
        }
        List<DomainPluginInstance> instances = findPluginInstanceByQuery(User.systemUser(tenantId), query).getData();
        return instances.stream().findFirst().orElse(null);
    }

    @Override
    public DomainPluginParam findPluginParam(String tenantId, String objectApiName, String pluginApiName) {
        DomainPluginInstance instance = findPluginInstanceByApiName(tenantId, objectApiName, pluginApiName);
        return Objects.isNull(instance) ? null : instance.getPluginParam();
    }

    @Override
    public void createPluginInstance(User user, DomainPluginInstance instance) {
        if (Strings.isNullOrEmpty(instance.getFieldApiName())) {
            DomainPluginType pluginType = DomainPluginDefinitionHolder.getPluginType(instance.getPluginApiName());
            if (DomainPluginType.Field == pluginType) {
                throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
            }
            instance.setFieldApiName(DomainPluginInstance.NONE);
        }
        DomainPluginInstance dbInstance = findPluginInstanceByApiNameFromDB(user.getTenantId(), instance.getRefObjectApiName(),
                instance.getPluginApiName(), instance.getFieldApiName(), true);
        if (Objects.nonNull(dbInstance)) {
            throw new ValidateException("instance already exists");
        }

        //校验依赖插件是否启用
        if (instance.isActive()) {
            validateByDependencies(user.getTenantId(), instance);
            // 检查依赖于该插件的的插件的业务类型
            validateRecordTypeList(user.getTenantId(), instance);
        }

        DomainPluginInstance created = repository.create(user, instance);
        instance.setId(created.getId());
        //保存插件和字段的引用关系
        savePluginReferenceData(user.getTenantId(), instance);
        sendPluginInstanceChangeMessage(user.getTenantId(), instance, ObjectAction.CREATE);
    }

    @Override
    public void updatePluginInstance(User user, DomainPluginInstance instance) {
        DomainPluginInstance dbInstance;
        if (Strings.isNullOrEmpty(instance.getId())) {
            dbInstance = findPluginInstanceByApiNameFromDB(user.getTenantId(), instance.getRefObjectApiName(),
                    instance.getPluginApiName(), instance.getFieldApiName(), true);
        } else {
            dbInstance = findPluginInstanceById(user.getTenantId(), instance.getId(), true);
        }
        if (Objects.isNull(dbInstance)) {
            throw new ValidateException("instance not exists");
        }

        if (dbInstance.isActive()) {
            validateRecordTypeList(user.getTenantId(), instance);
        }

        DomainPluginInstance oldInstance = dbInstance;
        boolean changed = false;
        DomainPluginInstance newInstance = oldInstance.copy();
        if (Objects.nonNull(instance.getRecordTypeList()) && !CollectionUtils.isEqual(instance.getRecordTypeList(), oldInstance.getRecordTypeList())) {
            newInstance.setRecordTypeList(instance.getRecordTypeList());
            changed = true;
        }
        if (Objects.nonNull(instance.getPluginParam()) && !Objects.equals(instance.getPluginParam(), oldInstance.getPluginParam())) {
            newInstance.setPluginParam(instance.getPluginParam());
            changed = true;
        }
        if (!Strings.isNullOrEmpty(instance.getFieldApiName()) && !Objects.equals(instance.getFieldApiName(), oldInstance.getFieldApiName())) {
            newInstance.setFieldApiName(instance.getFieldApiName());
            changed = true;
        }
        if (!Objects.isNull(instance.getExcludeRecordType()) && !Objects.equals(instance.getExcludeRecordType(), oldInstance.getExcludeRecordType())) {
            newInstance.setExcludeRecordType(instance.getExcludeRecordType());
            changed = true;
        }
        if (changed) {
            repository.update(user, newInstance);
            updatePluginReferenceData(user.getTenantId(), oldInstance, newInstance);
            sendPluginInstanceChangeMessage(user.getTenantId(), instance, ObjectAction.UPDATE);
        }
    }

    private void savePluginReferenceData(String tenantId, DomainPluginInstance instance) {
        if (Objects.isNull(instance.getPluginParam())) {
            return;
        }
        String pluginLabel = DomainPluginDefinitionHolder.getPluginLabel(instance.getPluginApiName());
        List<ReferenceData> referenceDataList = instance.buildReferenceDataList(pluginLabel);
        if (CollectionUtils.notEmpty(referenceDataList)) {
            referenceLogicService.deleteAndCreateReference(tenantId, referenceDataList);
        }
    }

    private void updatePluginReferenceData(String tenantId, DomainPluginInstance oldInstance, DomainPluginInstance newInstance) {
        if (Objects.equals(oldInstance.getPluginParam(), newInstance.getPluginParam())) {
            return;
        }
        String pluginLabel = DomainPluginDefinitionHolder.getPluginLabel(oldInstance.getPluginApiName());
        List<ReferenceData> oldReferenceDataList = oldInstance.buildReferenceDataList(pluginLabel);
        List<ReferenceData> newReferenceDataList = newInstance.buildReferenceDataList(pluginLabel);
        if (CollectionUtils.isEqual(oldReferenceDataList, newReferenceDataList)) {
            return;
        }
        if (CollectionUtils.empty(newReferenceDataList)) {
            deletePluginReferenceData(tenantId, oldInstance);
        } else {
            referenceLogicService.deleteAndCreateReference(tenantId, newReferenceDataList);
        }
    }

    private void deletePluginReferenceData(String tenantId, DomainPluginInstance instance) {
        String sourceValue = instance.getPluginApiName() + "." + instance.getRefObjectApiName();
        referenceLogicService.deleteReference(tenantId, SourceTypes.DOMAIN_PLUGIN, sourceValue);
    }

    @Override
    public void updatePluginInstanceStatus(User user, String objectApiName, String pluginApiName, String fieldApiName, boolean isActive) {
        DomainPluginInstance instance = findPluginInstanceByApiNameFromDB(user.getTenantId(), objectApiName, pluginApiName,
                fieldApiName, true);
        doUpdateInstanceStatus(user, isActive, instance);
    }

    @Override
    public void updatePluginInstanceStatusById(User user, String id, boolean isActive) {
        DomainPluginInstance instance = findPluginInstanceById(user.getTenantId(), id, true);
        doUpdateInstanceStatus(user, isActive, instance);
    }

    private void doUpdateInstanceStatus(User user, boolean isActive, DomainPluginInstance instance) {
        if (Objects.isNull(instance)) {
            throw new ValidateException("instance not exists");
        }

        if (instance.isActive() == isActive) {
            return;
        }

        //校验依赖插件是否启用
        if (isActive) {
            validateByDependencies(user.getTenantId(), instance);
            validateRecordTypeList(user.getTenantId(), instance);
        }

        instance.setActive(isActive);
        repository.update(user, instance);
        if (!instance.isActive() && isActive) {
            sendPluginInstanceChangeMessage(user.getTenantId(), instance, ObjectAction.RECOVER);
        } else if (instance.isActive() && !isActive) {
            sendPluginInstanceChangeMessage(user.getTenantId(), instance, ObjectAction.INVALID);
        }
    }

    @Override
    public void invalidPluginInstance(User user, String objectApiName, String pluginApiName, String fieldApiName) {
        DomainPluginInstance instance = findPluginInstanceByApiNameFromDB(user.getTenantId(), objectApiName, pluginApiName,
                fieldApiName, true);
        if (Objects.isNull(instance)) {
            throw new ValidateException("instance not exists");
        }
        if (instance.isActive()) {
            throw new ValidateException("instance cannot be deleted until it's disable");
        }
        repository.bulkInvalid(user, Lists.newArrayList(instance));
        deletePluginReferenceData(user.getTenantId(), instance);
        sendPluginInstanceChangeMessage(user.getTenantId(), instance, ObjectAction.DELETE);
    }

    @Override
    public void invalidPluginInstanceById(User user, String id) {
        DomainPluginInstance instance = findPluginInstanceById(user.getTenantId(), id, true);
        if (Objects.isNull(instance)) {
            throw new ValidateException("instance not exists");
        }
        if (instance.isActive()) {
            throw new ValidateException("instance cannot be deleted until it's disable");
        }
        repository.bulkInvalid(user, Lists.newArrayList(instance));
        deletePluginReferenceData(user.getTenantId(), instance);
        sendPluginInstanceChangeMessage(user.getTenantId(), instance, ObjectAction.DELETE);
    }

    private void sendPluginInstanceChangeMessage(String tenantId, DomainPluginInstance instance, ObjectAction action) {
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                public void afterCommit() {
                    doSendMessage(tenantId, instance, action);
                }
            });
        } else {
            doSendMessage(tenantId, instance, action);
        }
    }

    private void doSendMessage(String tenantId, DomainPluginInstance instance, ObjectAction action) {
        DomainPluginInstanceEvent changeEvent = DomainPluginInstanceEvent.builder()
                .tenantId(tenantId)
                .actionCode(action.getActionCode())
                .id(instance.getId())
                .pluginApiName(instance.getPluginApiName())
                .objectApiName(instance.getRefObjectApiName())
                .fieldApiName(instance.getFieldApiName())
                .build();
        SendResult sendResult = domainPluginInstanceMQProducer.sendMessage(JSON.toJSONBytes(changeEvent), Integer.parseInt(tenantId));
        log.info("send plugin instance change message,body:{},result:{}", changeEvent, sendResult);
    }

    @Override
    public Map<String, Boolean> findPluginStatus(String tenantId, String objectApiName, List<String> pluginApiNames) {
        if (Strings.isNullOrEmpty(objectApiName) || CollectionUtils.empty(pluginApiNames)) {
            return Maps.newHashMap();
        }
        List<DomainPluginInstance> pluginInstances = findPluginInstances(tenantId, objectApiName, null, null);
        Set<String> dbPluginApiNames = pluginInstances.stream().map(DomainPluginInstance::getPluginApiName).collect(Collectors.toSet());
        Map<String, Boolean> pluginStatusMap = Maps.newHashMap();
        pluginApiNames.forEach(pluginApiName -> pluginStatusMap.put(pluginApiName, dbPluginApiNames.contains(pluginApiName)));
        return pluginStatusMap;
    }

    // A --依赖--> B时, 修改B实例的业务类型需要需要与A实例完全一致(黑白名单,业务类型)
    private void validateRecordTypeList(String tenantId, DomainPluginInstance instance) {
        // 查询依赖于该插件的插件
        List<String> sonPluginApiNames = DomainPluginDefinitionHolder.findSonPluginApiNames(instance.getPluginApiName());
        List<String> errorPluginLabels = new ArrayList<>();
        sonPluginApiNames.forEach(
                sonPluginApiName -> {
                    // 子插件实例，即使没有启用
                    DomainPluginInstance sonInstance = findPluginInstanceByApiName(tenantId, instance.getRefObjectApiName(),
                            sonPluginApiName, instance.getFieldApiName(), true);
                    if (Objects.nonNull(sonInstance) && !instance.sameRecordType(sonInstance)) {
                        errorPluginLabels.add(DomainPluginDefinitionHolder.getPluginLabel(sonPluginApiName));
                    }
                }
        );
        if (CollectionUtils.notEmpty(errorPluginLabels)) {
            throw new ValidateException(I18NExt.text(I18NKey.DOMAIN_PLUGIN_RECORD_TYPE_NOT_SAME,
                    errorPluginLabels.toString()));
        }
    }

    @Override
    public void validateByDependencies(String tenantId, DomainPluginInstance instance) {
        DomainPluginDefinition definition = DomainPluginDefinitionHolder.getPluginDefinition(instance.getPluginApiName());
        if (Objects.isNull(definition)) {
            throw new ValidateException("plugin definition not exists");
        }
        List<String> dependencies = definition.dependencies();
        if (CollectionUtils.empty(dependencies)) {
            return;
        }
        Map<String, Boolean> pluginStatusMap = findPluginStatus(tenantId, instance.getRefObjectApiName(), dependencies);
        for (String dependency : dependencies) {
            if (!pluginStatusMap.containsKey(dependency) || Boolean.FALSE.equals(pluginStatusMap.get(dependency))) {
                String dependencyLabel = DomainPluginDefinitionHolder.getPluginLabel(dependency);
                throw new ValidateException(I18NExt.getOrDefault(I18NKey.DOMAIN_PLUGIN_DEPENDENCY_NOT_ENABLE,
                        "当前插件依赖【{0}】插件，请先绑定【{1}】插件", dependencyLabel, dependencyLabel));// ignoreI18n
            }
        }
    }

    @Override
    public FieldConfig findFieldConfigByDomainPlugin(String tenantId,
                                                     String objectApiName,
                                                     List<String> detailObjectApiNames,
                                                     List<String> recordTypeList,
                                                     String pageType) {
        List<String> objectApiNames = Lists.newArrayList(objectApiName);
        if (CollectionUtils.notEmpty(detailObjectApiNames)) {
            objectApiNames.addAll(detailObjectApiNames);
        } else {
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopy(tenantId, objectApiName);
            ObjectDescribeExt.of(objectDescribe).getMasterAPIName().ifPresent(objectApiNames::add);
        }
        List<DomainPluginInstance> pluginInstances = findPluginInstances(tenantId, objectApiNames,
                null, null);
        if (CollectionUtils.empty(pluginInstances)) {
            return new FieldConfig();
        }
        Map<String, Set<String>> readOnlyFieldMap = Maps.newHashMap();
        Map<String, Set<String>> hiddenFieldMap = Maps.newHashMap();
        pluginInstances.forEach(pluginInstance -> {
            if (objectApiName.equals(pluginInstance.getRefObjectApiName()) && !pluginInstance.containsRecordType(recordTypeList)) {
                return;
            }
            DomainPluginDefinition pluginDefinition = DomainPluginDefinitionHolder.getPluginDefinition(pluginInstance.getPluginApiName());
            if (Objects.isNull(pluginDefinition)) {
                return;
            }
            List<String> masterReadOnlyFields = pluginDefinition.masterReadOnlyFields();
            List<String> masterHiddenFields = pluginInstance.masterFields(pluginDefinition.masterHiddenFields(pageType));
            if (CollectionUtils.notEmpty(masterReadOnlyFields)) {
                readOnlyFieldMap.computeIfAbsent(objectApiName, k -> Sets.newHashSet()).addAll(masterReadOnlyFields);
            }
            if (CollectionUtils.notEmpty(masterHiddenFields)) {
                hiddenFieldMap.computeIfAbsent(objectApiName, k -> Sets.newHashSet()).addAll(masterHiddenFields);
            }
            pluginDefinition.detailReadOnlyFieldMap().forEach((detailKey, fieldKeys) -> {
                List<String> detailReadOnlyFields = pluginInstance.detailFields(detailKey, fieldKeys);
                if (CollectionUtils.notEmpty(detailReadOnlyFields)) {
                    String detailObjectApiName = pluginInstance.detailObjectApiName(detailKey);
                    readOnlyFieldMap.computeIfAbsent(detailObjectApiName, k -> Sets.newHashSet()).addAll(detailReadOnlyFields);
                }
            });
            pluginDefinition.detailHiddenFieldMap(pageType).forEach((detailKey, fieldKeys) -> {
                List<String> detailHiddenFields = pluginInstance.detailFields(detailKey, fieldKeys);
                if (CollectionUtils.notEmpty(detailHiddenFields)) {
                    String detailObjectApiName = pluginInstance.detailObjectApiName(detailKey);
                    hiddenFieldMap.computeIfAbsent(detailObjectApiName, k -> Sets.newHashSet()).addAll(detailHiddenFields);
                }
            });
        });
        return FieldConfig.builder().readOnlyFieldMap(readOnlyFieldMap).hiddenFieldMap(hiddenFieldMap).build();
    }

    @Override
    public Set<String> getHiddenFieldsByDomainPluginDescribe(String pageType, List<SimpleDomainPluginDescribe> pluginDescribes) {
        Set<String> hiddenFields = Sets.newHashSet();
        if (CollectionUtils.empty(pluginDescribes)) {
            return hiddenFields;
        }
        pluginDescribes.forEach(pluginDescribe -> {
            DomainPluginDefinition pluginDefinition = DomainPluginDefinitionHolder.getPluginDefinition(pluginDescribe.getApiName());
            if (Objects.isNull(pluginDefinition)) {
                return;
            }
            List<String> masterHiddenFields = pluginDescribe.masterFields(pluginDefinition.masterHiddenFields(pageType));
            if (CollectionUtils.notEmpty(masterHiddenFields)) {
                hiddenFields.addAll(masterHiddenFields);
            }
        });
        return hiddenFields;
    }

}
