package com.facishare.paas.appframework.common.util

import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Timeout
import java.lang.reflect.Field

import java.util.concurrent.TimeUnit

/**
 * GenerateByAI
 * 测试内容描述：ThreadUtils工具类的单元测试
 */
class ThreadUtilsTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试sleepSilently方法在不被中断的情况下正常执行
     */
    @Timeout(5)
    def "sleepSilently方法在正常情况下应该等待指定的时间"() {
        given: "记录开始时间"
        def startTime = System.currentTimeMillis()
        
        when: "调用sleep方法等待100毫秒"
        ThreadUtils.sleepSilently(100, TimeUnit.MILLISECONDS)
        def endTime = System.currentTimeMillis()
        def elapsedTime = endTime - startTime
        
        then: "应该至少等待了指定的时间"
        elapsedTime >= 100
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试sleepSilently方法在被中断的情况下的行为
     */
    def "sleepSilently方法在线程被中断时应该恢复中断状态"() {
        given: "创建一个将被中断的线程"
        def interrupted = false
        def thread = new Thread({
            try {
                ThreadUtils.sleepSilently(1000, TimeUnit.MILLISECONDS)
            } finally {
                // 检查线程是否被标记为中断状态
                interrupted = Thread.currentThread().isInterrupted()
            }
        })
        
        when: "启动线程并立即中断它"
        thread.start()
        thread.interrupt()
        thread.join() // 等待线程执行完成
        
        then: "线程的中断状态应该被恢复"
        interrupted
    }
} 