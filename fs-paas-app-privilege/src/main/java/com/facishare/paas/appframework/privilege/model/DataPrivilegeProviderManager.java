package com.facishare.paas.appframework.privilege.model;

import com.facishare.paas.appframework.common.util.ObjectRegister;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
public class DataPrivilegeProviderManager implements ApplicationContextAware {

    private Map<String, DataPrivilegeProvider> providerMap = Maps.newHashMap();
    @Autowired
    DataPrivilegeProviderProxy proxy;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        initDataPrivilegeProviderMap(applicationContext);
    }

    private void initDataPrivilegeProviderMap(ApplicationContext applicationContext) {
        Map<String, DataPrivilegeProvider> springBeanMap = applicationContext.getBeansOfType(DataPrivilegeProvider.class);
        springBeanMap.values().forEach(provider -> {
            if (StringUtils.isNotEmpty(provider.getApiName())) {
                providerMap.put(provider.getApiName(), provider);
            }
        });
    }

    public DataPrivilegeProvider getProvider(String apiName) {
        if(ObjectDescribeExt.isCustomObject(apiName)) {
            return null;
        }

        DataPrivilegeProvider dataPrivilegeProvider = providerMap.get(apiName);
        if (Objects.isNull(dataPrivilegeProvider) && !ObjectRegister.containsObject(apiName)) {
            return getRemoteProvider(apiName);
        }
        return dataPrivilegeProvider;
    }

    public DataPrivilegeProvider getLocalProvider(String apiName) {
        return providerMap.get(apiName);
    }

    private DataPrivilegeProvider getRemoteProvider(String apiName) {
        return RemoteDataPrivilegeProvider.builder()
                .proxy(proxy)
                .describeApiName(apiName)
                .build();
    }
}
