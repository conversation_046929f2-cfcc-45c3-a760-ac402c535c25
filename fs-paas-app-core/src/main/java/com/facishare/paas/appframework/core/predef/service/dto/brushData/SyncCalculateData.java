package com.facishare.paas.appframework.core.predef.service.dto.brushData;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Quote;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2022/8/26
 */
public interface SyncCalculateData {
    @Data
    class Arg {
        private String describeApiName;
        private Set<String> fieldNames;
    }

    class Result {

    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class CalculateInfo {
        private IObjectDescribe describe;
        private Set<String> fieldNames;
        private User user;

        public Set<Count> getCountFields() {
            return ObjectDescribeExt.of(describe).stream()
                    .filter(it -> fieldNames.contains(it.getApiName()))
                    .map(FieldDescribeExt::of)
                    .filter(FieldDescribeExt::isCountField)
                    .map(FieldDescribeExt::<Count>getFieldDescribe)
                    .collect(Collectors.toSet());
        }

        public Set<IFieldDescribe> getCalculateFields() {
            return ObjectDescribeExt.of(describe).stream()
                    .filter(it -> fieldNames.contains(it.getApiName()))
                    .map(FieldDescribeExt::of)
                    .filter(FieldDescribeExt::isCalculateField)
                    .map(FieldDescribeExt::<IFieldDescribe>getFieldDescribe)
                    .collect(Collectors.toSet());
        }

        public Set<Quote> getQuoteFields() {
            return ObjectDescribeExt.of(describe).stream()
                    .filter(it -> fieldNames.contains(it.getApiName()))
                    .map(FieldDescribeExt::of)
                    .filter(FieldDescribeExt::isQuoteField)
                    .map(FieldDescribeExt::<Quote>getFieldDescribe)
                    .collect(Collectors.toSet());
        }
    }
}
