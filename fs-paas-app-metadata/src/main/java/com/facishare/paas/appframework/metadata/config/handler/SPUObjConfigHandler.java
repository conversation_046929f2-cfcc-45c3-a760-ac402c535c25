package com.facishare.paas.appframework.metadata.config.handler;

import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.metadata.config.util.ProductCategoryConfigUtils;
import com.facishare.paas.appframework.metadata.config.util.TenantConfigUtil;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SPUObjConfigHandler implements ConfigHandler {
    @Autowired
    IObjectDescribeService objectDescribeService;
    @Autowired
    ConfigService configService;
    @Override
    public String getObjectAPIName() {
        return "SPUObj";
    }

    @Override
    public void handle(String tenantId, Map<String, Object> objectConfig, Map<String, Map<String, Object>> fieldConfig) {
        ProductCategoryConfigUtils.hideCategoryByGray(tenantId, fieldConfig);
        boolean isMultipleUnitEnabled = TenantConfigUtil.isMultipleUnitEnabled(configService,tenantId);
        if(isMultipleUnitEnabled){
            try {
                fieldConfig.get("unit").put("add",0);
                fieldConfig.get("unit").put("edit",0);
                IObjectDescribe productDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId,getObjectAPIName());
                if(productDescribe != null && productDescribe.getFieldDescribes() != null){
                    productDescribe.getFieldDescribes().stream().filter(x-> "unit".equals(x.getApiName())).findFirst().ifPresent(f->{
                        if(f.get("options") == null){
                            return;
                        }
                        List<Map<String,Object>> fieldOptions = (List<Map<String,Object>>) f.get("options");
                        if(fieldOptions == null){
                            return;
                        }
                        List<String> valueList = fieldOptions.stream().map(o-> o.get("value").toString()).collect(Collectors.toList());
                        Map<String,Map<String,Object>> optionConfig = (Map<String,Map<String,Object>>)fieldConfig.get("unit").get("options");
                        Map<String,Object> optionConfigValue = Maps.newHashMap();
                        optionConfigValue.put("edit",0);
                        optionConfigValue.put("remove",0);
                        optionConfigValue.put("enable",0);
                        valueList.forEach(v->{
                            optionConfig.put(v,optionConfigValue);
                        });
                    });
                }
            } catch (MetadataServiceException e) {
                log.error(e.getMessage(),e);
            }
        }
    }
}
