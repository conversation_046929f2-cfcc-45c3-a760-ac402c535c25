package com.facishare.paas.appframework.core.predef.service.dto.stageview;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.metadata.dto.StageViewInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * create by z<PERSON><PERSON> on 2020/02/05
 */
public interface QueryStageView {
    @Data
    class Arg {
        @JsonProperty("describe_api_name")
        @JSONField(name = "describe_api_name")
        private String describeApiName;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        @JsonProperty("stage_view_infos")
        @JSONField(name = "stage_view_infos")
        private List<StageViewInfo> stageViewInfos;
    }
}
