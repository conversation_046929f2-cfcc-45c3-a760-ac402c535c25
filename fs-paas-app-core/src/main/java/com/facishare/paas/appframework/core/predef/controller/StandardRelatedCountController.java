package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.RelatedObjectDescribeStructure;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.facishare.paas.metadata.api.describe.MasterDetail;
import com.facishare.paas.metadata.api.search.IRelatedListQuery;
import com.facishare.paas.metadata.impl.describe.WhatFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.RelatedListQuery;
import com.facishare.paas.metadata.impl.search.SearchQuery;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2019/7/2
 */
public class StandardRelatedCountController extends PreDefineController<StandardRelatedCountController.Arg, StandardRelatedCountController.Result> {

    private static final int QUERY_BATCH_NUM = 3;

    private IObjectDescribe objectDescribe;
    private Map<String, RelatedObjectDescribeStructure> relatedObjects;
    private Map<String, String> whatParamMap = new HashMap<>();

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardController.RelatedCount.getFuncPrivilegeCodes();
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), controllerContext.getObjectApiName());
        stopWatch.lap("findObject");

        relatedObjects = findRelatedObjects();
        stopWatch.lap("findRelatedObjects");
    }

    @Override
    protected Result doService(Arg arg) {
        IRelatedListQuery.QueryResult<IObjectData> relatedObjectData = bulkFindRelatedDataCount();
        stopWatch.lap("bulkFindRelatedDataCount");

        Result result = buildResult(relatedObjectData);
        stopWatch.lap("buildResult");

        return result;
    }

    protected Map<String, RelatedObjectDescribeStructure> findRelatedObjects() {
        if (CollectionUtils.empty(arg.getRelatedList())) {
            return Maps.newHashMap();
        }
        List<String> relatedApiNames = arg.getRelatedList().stream().map(x -> x.getObjectApiName()).distinct().collect(Collectors.toList());
        Map<String, IObjectDescribe> relatedDescribes = serviceFacade.findObjectsIncludeMultiField(controllerContext.getTenantId(), relatedApiNames);
        Map<String, RelatedObjectDescribeStructure> relatedObjects = Maps.newLinkedHashMap();
        arg.getRelatedList().stream().filter(x -> relatedDescribes.containsKey(x.getObjectApiName())).forEach(x -> {
            RelatedObjectDescribeStructure relatedObject = RelatedObjectDescribeStructure.builder()
                    .relatedObjectDescribe(relatedDescribes.get(x.getObjectApiName()))
                    .fieldApiName(x.getFieldApiName())
                    .build();

            IFieldDescribe fieldDescribe = relatedDescribes.get(x.getObjectApiName()).getFieldDescribe(x.getFieldApiName());
            if (fieldDescribe == null) {
                return;
            }
            if (fieldDescribe instanceof MasterDetail) {
                MasterDetail masterDetail = (MasterDetail) fieldDescribe;
                relatedObject.setRelatedListName(masterDetail.getTargetRelatedListName());
                relatedObject.setRelatedListLabel(masterDetail.getTargetRelatedListLabel());
            } else if (fieldDescribe instanceof IObjectReferenceField) {
                IObjectReferenceField referenceField = (IObjectReferenceField) fieldDescribe;
                relatedObject.setRelatedListName(referenceField.getTargetRelatedListName());
                relatedObject.setRelatedListLabel(referenceField.getTargetRelatedListLabel());
            }
            relatedObjects.put(getRelatedKey(x.getObjectApiName(), x.getFieldApiName()), relatedObject);
        });
        return relatedObjects;
    }

    protected Result buildResult(IRelatedListQuery.QueryResult<IObjectData> relatedObjectData) {
        Result result = Result.builder().build();
        List<RelatedCount> relatedCounts = relatedObjectData.getResultList().stream()
                .map(x -> RelatedCount.builder()
                        .objectApiName(x.getChildApiName())
                        .fieldApiName(whatParamMap.containsKey(x.getChildApiName() + x.getFieldApiName()) ? whatParamMap.get(x.getChildApiName()+x.getFieldApiName()) : x.getFieldApiName())
                        .totalNum(x.getTotalNumber())
                        .build())
                .collect(Collectors.toList());
        result.setRelatedCount(relatedCounts);

        return result;
    }

    private IRelatedListQuery.QueryResult<IObjectData> bulkFindRelatedDataCount() {
        IRelatedListQuery.QueryResult<IObjectData> result = new IRelatedListQuery.QueryResult<>();

        if (CollectionUtils.empty(relatedObjects)) {
            return result;
        }

        result.setResultList(Lists.newCopyOnWriteArrayList());
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        Lists.partition(Lists.newArrayList(relatedObjects.values()), QUERY_BATCH_NUM).forEach(relatedObjects ->
                parallelTask.submit(() -> {
                    IRelatedListQuery.QueryResult<IObjectData> groupResult = findRelatedDataCount(relatedObjects);
                    result.getResultList().addAll(CollectionUtils.nullToEmpty(groupResult.getResultList()));
                }));
        try {
            parallelTask.await(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("findRelatedDataCount failed,tenantId:{},objectApiName:{},arg:{}", controllerContext.getTenantId(),
                    controllerContext.getObjectApiName(), arg, e);
        }

        return result;
    }

    private IRelatedListQuery.QueryResult<IObjectData> findRelatedDataCount(List<RelatedObjectDescribeStructure> relatedObjects) {
        IRelatedListQuery relatedListQuery = getRelatedListQuery(arg.getObjectDataId(), objectDescribe.getApiName(),
                0, 1, relatedObjects, controllerContext.getUser());
        return serviceFacade.findRelatedObjectData(relatedListQuery, controllerContext.getUser());
    }

    private IRelatedListQuery getRelatedListQuery(String associateObjectDataId, String associateObjectDescribeApiName,
                                                  Integer offset, Integer limit,
                                                  List<RelatedObjectDescribeStructure> relatedObjects, User user) {
        IRelatedListQuery searchQuery = new RelatedListQuery();
        searchQuery.setDataId(associateObjectDataId);
        searchQuery.setTenantId(user.getTenantId());
        searchQuery.setObjectDescribeAPIName(associateObjectDescribeApiName);

        boolean isAdmin = isAdmin(user);
        List<IRelatedListQuery.RelatedObjectQuery> objectQueryList = Lists.newArrayList();
        relatedObjects.forEach(relatedObject -> {
            IRelatedListQuery.RelatedObjectQuery objectQuery = getRelatedObjectQuery(offset, limit, user, relatedObject, isAdmin);
            //供业务方加工query
            processRelatedObjectQuery(objectQuery, relatedObject);
            objectQueryList.add(objectQuery);
        });

        searchQuery.setRelatedObjectQueryList(objectQueryList);
        return searchQuery;
    }

    private IRelatedListQuery.RelatedObjectQuery getRelatedObjectQuery(Integer offset, Integer limit, User user, RelatedObjectDescribeStructure relatedObject,
                                                                       boolean isAdmin) {
        IObjectDescribe relatedObjectDescribe = relatedObject.getRelatedObjectDescribe();
        IRelatedListQuery.RelatedObjectQuery objectQuery = new IRelatedListQuery.RelatedObjectQuery();
        objectQuery.setLimit(limit);
        objectQuery.setOffset(offset);
        IFieldDescribe fieldDescribe = relatedObjectDescribe.getFieldDescribe(relatedObject.getFieldApiName());
        if (FieldDescribeExt.of(fieldDescribe).isWhatField()) {
            String idFieldApiName = ((WhatFieldDescribe) fieldDescribe).getIdFieldApiName();
            objectQuery.setFieldApiName(idFieldApiName);
            whatParamMap.put(fieldDescribe.getDescribeApiName() + idFieldApiName, relatedObject.getFieldApiName());
        } else {
            objectQuery.setFieldApiName(relatedObject.getFieldApiName());
        }
        objectQuery.setRelatedListName(relatedObject.getRelatedListName());
        objectQuery.setReferenceApiName(relatedObjectDescribe.getApiName());
        objectQuery.setFindExplicitTotalNum(true);

        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(limit);
        searchQuery.setOffset(offset);
        SearchTemplateQuery searchTemplateQuery = serviceFacade.getSearchTemplateQuery(user, ObjectDescribeExt.of(relatedObjectDescribe),
                "", searchQuery.toJsonString(), isAdmin);
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(searchTemplateQuery);
        if (FieldDescribeExt.of(fieldDescribe).isWhatField()) {
            queryExt.addFilter(Operator.EQ, ((WhatFieldDescribe) fieldDescribe).getApiNameFieldApiName(), controllerContext.getObjectApiName());
        }
        queryExt.addIsDeletedFalseFilter();
        objectQuery.setFilters(queryExt.getFilters());
        objectQuery.setOrders(queryExt.getConvertOrders());
        objectQuery.setPermissionType(queryExt.getPermissionType());
        if (queryExt.getDataRightsParameter() != null) {
            objectQuery.setLinkAppDataAuthRange(queryExt.getDataRightsParameter().getLinkAppDataAuthRange());
        }

        // 线索转换日志不走权限
        if (AppFrameworkConfig.getRelatedNotNeedDataAuth().contains(relatedObject.getRelatedObjectDescribe().getApiName())) {
            objectQuery.setIsNeedDataAuth(false);
            return objectQuery;
        }

        //外部用户需要校验数据权限
        if (user.isOutUser()) {
            objectQuery.setIsNeedDataAuth(true);
            return objectQuery;
        }

        // 管理员不走数据权限
        if (isAdmin) {
            objectQuery.setIsNeedDataAuth(false);
            return objectQuery;
        }

        objectQuery.setIsNeedDataAuth(true);
        return objectQuery;
    }

    protected void processRelatedObjectQuery(IRelatedListQuery.RelatedObjectQuery objectQuery, RelatedObjectDescribeStructure relatedObject) {

    }

    private boolean isAdmin(User user) {
        if (user.isOutUser()) {
            return false;
        }
        return user.isSupperAdmin() || serviceFacade.isAdmin(user);
    }

    private String getRelatedKey(String objectApiName, String fieldApiName) {
        return objectApiName + "-" + fieldApiName;
    }

    @Data
    public static class Arg {
        private String objectDataId;
        private List<RelatedObject> relatedList;
    }

    @Data
    public static class RelatedObject {
        private String objectApiName;
        private String fieldApiName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private List<RelatedCount> relatedCount;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RelatedCount {
        private String objectApiName;
        private String fieldApiName;
        private int totalNum;
    }
}
