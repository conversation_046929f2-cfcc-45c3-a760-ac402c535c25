package com.facishare.paas.appframework.rest.controller;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.predef.service.dto.objectImport.FindImportInfosByNames;
import com.facishare.paas.appframework.jaxrs.annotation.RestAPI;
import com.facishare.paas.appframework.rest.dto.data.BulkImport;
import com.facishare.paas.appframework.rest.service.BulkImportRestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import java.util.List;

@Controller
@Path("/v1/inner/rest/bulkImport")
@Slf4j
@RestAPI
public class BulkImportRestController {

    @Autowired
    private BulkImportRestService bulkImportRestService;

    /**
     * 查询所有对象主属性字段信息
     * @return 返回主属性信息列表
     */
    @POST
    @Path("/findPrimaryFieldList")
    public List<BulkImport.PrimaryFieldInfo> findPrimaryFieldList() {
        RequestContext context = RequestContextManager.getContext();
        return bulkImportRestService.findPrimaryFieldList(context);
    }

    @POST
    @Path("/findImportInfosByNames")
    @Produces({"application/json"})
    @Consumes({"application/json"})
    public List<BulkImport.ImportRowInfo> findImportInfosByNames(FindImportInfosByNames.Arg arg) {
        RequestContext context = RequestContextManager.getContext();
        BulkImport.Arg arg1 = BulkImport.Arg.builder()
                .cellStructList(arg.getCellStructList()).build();
        return bulkImportRestService.findImportInfoListByNames(arg1, context);
    }
}
