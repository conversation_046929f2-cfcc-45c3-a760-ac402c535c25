package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.predef.facade.dto.MergeChangeOrderData;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Created by zhaooju on 2025/3/7
 */
@Slf4j
public class StandardChangeDataMergeController extends PreDefineController<StandardChangeDataMergeController.Arg, StandardChangeDataMergeController.Result> {
    protected IObjectDescribe changeObjectDescribe;
    protected IObjectData changeDbMasterData;

    protected IObjectDescribe originalObjectDescribe;
    protected IObjectData originalDbMasterData;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardController.Detail.getFuncPrivilegeCodes();
    }

    @Override
    protected void doInitBefore() {
        super.doInitBefore();
        changeObjectDescribe = findDescribe(controllerContext.getObjectApiName());
        if (!ObjectDescribeExt.of(changeObjectDescribe).isChangeOrderObject()) {
            throw new ValidateException("change object is not change order object");
        }
    }

    @Override
    protected void doInit() {
        super.doInit();
        initDescribe();
        initData();
        checkDataPrivilege();
    }

    private void checkDataPrivilege() {
        doDataPrivilegeCheck(changeObjectDescribe, changeDbMasterData);
        doDataPrivilegeCheck(originalObjectDescribe, originalDbMasterData);
    }

    protected void doDataPrivilegeCheck(IObjectDescribe describe, IObjectData data) {
        serviceFacade.doDataPrivilegeCheck(controllerContext.getUser(), Lists.newArrayList(data),
                describe, ObjectAction.VIEW_DETAIL.getActionCode());
    }

    private void initDescribe() {
        originalObjectDescribe = findDescribe(changeObjectDescribe.getOriginalDescribeApiName());
    }

    private void initData() {
        changeDbMasterData = findData(changeObjectDescribe.getApiName(), arg.getObjectDataId());
        String originalDataId = ObjectDataExt.of(changeDbMasterData).getOriginalDataId();
        originalDbMasterData = findData(originalObjectDescribe.getApiName(), originalDataId);
    }

    private IObjectData findData(String objectApiName, String dataId) {
        MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                .user(controllerContext.getUser())
                .skipRelevantTeam(true)
                .includeInvalid(false)
                .searchRichTextExtra(true)
                .keepAllMultiLangValue(true)
                .isSimple(false)
                .build();
        List<IObjectData> dataList = serviceFacade.findObjectDataByIdsWithQueryContext(queryContext, Lists.newArrayList(dataId), objectApiName);
        if (CollectionUtils.empty(dataList)) {
            throw new ObjectDataNotFoundException(I18N.text(I18NKey.DATA_NOT_USED));
        }
        return dataList.get(0);
    }

    @Override
    protected void doFunPrivilegeCheck() {
        super.doFunPrivilegeCheck();
        // 校验原单对象「查看详情」的功能权限
        serviceFacade.doFunPrivilegeCheck(controllerContext.getUser(), changeObjectDescribe.getOriginalDescribeApiName(), StandardController.Detail.getFuncPrivilegeCodes());

    }

    private IObjectDescribe findDescribe(String objectApiName) {
        return serviceFacade.findObjectWithoutCopy(controllerContext.getTenantId(), objectApiName);
    }

    @Override
    protected Result doService(Arg arg) {
        MergeChangeOrderData.Result mergedChangeOrderData = infraServiceFacade.mergeChangeOrderData(controllerContext.getUser(), buildMergeChangeOrderData());
        Map<String, List<IObjectData>> detailsDataMap = mergedChangeOrderData.getDetailsData();
        // 按已有的从对象 order_by 排一下序
        sortDetailDataByOrderBy(detailsDataMap);
        fillExtendFieldInfo(mergedChangeOrderData.getMasterObjectData(), detailsDataMap);
        return buildResult(mergedChangeOrderData);
    }

    private void sortDetailDataByOrderBy(Map<String, List<IObjectData>> detailsDataMap) {
        detailsDataMap.forEach((apiName, objectDataList) -> ObjectDataExt.sortByOrderBy(objectDataList));
    }

    private Result buildResult(MergeChangeOrderData.Result mergedChangeOrderData) {
        return Result.builder()
                .originalData(ObjectDataDocument.of(mergedChangeOrderData.getMasterObjectData()))
                .originalDetails(ObjectDataDocument.ofMap(mergedChangeOrderData.getDetailsData()))
                .build();
    }

    private MergeChangeOrderData.Arg buildMergeChangeOrderData() {
        return MergeChangeOrderData.Arg.builder()
                .originalData(originalDbMasterData)
                .originalDescribe(originalObjectDescribe)
                .changeOrderDescribe(changeObjectDescribe)
                .changeOrderData(changeDbMasterData)
                .build();
    }


    private void fillExtendFieldInfo(IObjectData masterObjectData, Map<String, List<IObjectData>> detailsData) {
        IObjectData syncMasterData = ObjectDataExt.synchronize(masterObjectData);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            asyncFillFieldInfo(Lists.newArrayList(syncMasterData), originalObjectDescribe);
        });
        Map<String, List<IObjectData>> syncDetailData = Maps.newHashMap();
        if (CollectionUtils.notEmpty(detailsData)) {
            Map<String, IObjectDescribe> objectDescribeMap = serviceFacade.findObjects(controllerContext.getTenantId(), detailsData.keySet());
            detailsData.forEach((detailApiName, detailDataList) -> {
                IObjectDescribe detailDescribe = objectDescribeMap.get(detailApiName);
                if (Objects.isNull(detailDescribe)) {
                    return;
                }
                List<IObjectData> syncDetailDataList = ObjectDataExt.synchronize(detailDataList);
                syncDetailData.put(detailApiName, syncDetailDataList);
                parallelTask.submit(() -> {
                    asyncFillFieldInfo(syncDetailDataList, detailDescribe);
                });
            });
        }

        try {
            parallelTask.await(10, TimeUnit.SECONDS);
            // 掩码需要等到最后执行
            fillMaskFieldValue(syncMasterData, originalObjectDescribe, Maps.newHashMap(), syncDetailData);
        } catch (Exception e) {
            log.error("Error in fill info of fillExtendFieldInfo, ei:{}", controllerContext.getTenantId(), e);
        }
    }

    private void asyncFillFieldInfo(List<IObjectData> objectDataList, IObjectDescribe objectDescribe) {
        List<IObjectData> dataList = ObjectDataExt.synchronize(objectDataList);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            //补充引用字段数据(包括负责人所在部门)
            fillQuoteFieldValue(dataList, objectDescribe);
        }).submit(() -> {
            //补充关联对象的名称
            fillRefObjectName(dataList, objectDescribe);
        }).submit(() -> {
            fillOutUserInfo(dataList, objectDescribe);
        }).submit(() -> {
            fillCountryAreaLabel(dataList, objectDescribe);
        }).submit(() -> {
            fillDimensionFieldValue(dataList, objectDescribe);
        }).submit(() -> {
            serviceFacade.fillPhoneNumberInformation(objectDescribe, dataList);
        }).submit(() -> {
            serviceFacade.fillCurrencyFieldInfo(objectDescribe, dataList, controllerContext.getUser());
        }).submit(() -> {
            serviceFacade.fillDataVisibilityRange(controllerContext.getUser(), objectDescribe, dataList);
        });
        try {
            parallelTask.await(7, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Error in fill info of asyncFillFieldInfo, ei:{}, object:{}", controllerContext.getTenantId(), objectDescribe.getApiName(), e);
        }
    }

    private void fillQuoteFieldValue(List<IObjectData> objectDataList, IObjectDescribe describe) {
        infraServiceFacade.fillQuoteFieldValue(controllerContext.getUser(), objectDataList, describe, null, false);
    }


    private void fillMaskFieldValue(IObjectData masterData, IObjectDescribe masterDescribe,
                                    Map<String, IObjectDescribe> detailDescribeMap, Map<String, List<IObjectData>> detailDataMap) {
        if (AppFrameworkConfig.maskFieldEncryptGray(controllerContext.getTenantId(), controllerContext.getObjectApiName())) {
            MaskFieldLogicService maskFieldLogicService = serviceFacade.getBean(MaskFieldLogicService.class);
            Map<String, IObjectDescribe> describeMap = Maps.newHashMap(detailDescribeMap);
            describeMap.put(masterDescribe.getApiName(), masterDescribe);
            maskFieldLogicService.processMaskFieldValue(controllerContext.getUser(), masterData, detailDataMap, describeMap,
                    MaskFieldLogicService.MaskFieldConfig.createByRemoveMaskOrigValue(needRemoveMaskOrigValue()));
            return;
        }
        boolean removeOrigValue = needRemoveMaskOrigValue();
        serviceFacade.fillMaskFieldValue(controllerContext.getUser(), Lists.newArrayList(masterData), masterDescribe, removeOrigValue);
        detailDataMap.forEach((detailApiName, detailDataList) -> {
            IObjectDescribe detailDescribe = detailDescribeMap.get(detailApiName);
            if (Objects.isNull(detailDescribe)) {
                return;
            }
            serviceFacade.fillMaskFieldValue(controllerContext.getUser(), detailDataList, detailDescribe, removeOrigValue);
        });
    }

    protected boolean needRemoveMaskOrigValue() {
        return RequestUtil.isCepRequest();
    }

    private void fillRefObjectName(List<IObjectData> objectDataList, IObjectDescribe describe) {
        serviceFacade.fillObjectDataWithRefObject(describe, objectDataList, controllerContext.getUser(), null, false);
    }

    private void fillOutUserInfo(List<IObjectData> objectDataList, IObjectDescribe describe) {
        serviceFacade.fillUserInfo(describe, objectDataList, controllerContext.getUser());
        serviceFacade.fillDepartmentInfo(describe, objectDataList, controllerContext.getUser());
        serviceFacade.fillImageInformation(describe, objectDataList, controllerContext.getUser());
        serviceFacade.fillRichTextImageInfo(describe, objectDataList, controllerContext.getUser());
    }

    private void fillCountryAreaLabel(List<IObjectData> objectDataList, IObjectDescribe describe) {
        serviceFacade.fillCountryAreaLabel(describe, objectDataList, controllerContext.getUser());
    }

    private void fillDimensionFieldValue(List<IObjectData> objectDataList, IObjectDescribe describe) {
        serviceFacade.fillDimensionFieldValue(controllerContext.getUser(), describe, objectDataList);
    }

    @Data
    public static class Arg {
        @JsonProperty("object_id")
        @JSONField(name = "object_id")
        private String objectDataId;
    }

    @Data
    @Builder
    public static class Result {
        private ObjectDataDocument originalData;
        private Map<String, List<ObjectDataDocument>> originalDetails;
    }

}
