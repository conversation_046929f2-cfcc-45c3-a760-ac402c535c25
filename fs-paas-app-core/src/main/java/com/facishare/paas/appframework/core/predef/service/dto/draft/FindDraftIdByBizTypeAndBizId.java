package com.facishare.paas.appframework.core.predef.service.dto.draft;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by zhouwr on 2023/12/12.
 */
public interface FindDraftIdByBizTypeAndBizId {
    @Data
    class Arg {
        private String bizType;
        private String bizId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Result {
        //草稿id
        private String draftId;
        //主对象数据的业务类型
        private String recordType;
    }
}
