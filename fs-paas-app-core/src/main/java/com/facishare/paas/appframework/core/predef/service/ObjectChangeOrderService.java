package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.changeorder.*;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.changeorder.*;
import com.facishare.paas.appframework.metadata.repository.model.MtChangeOrderRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * Created by zhaooju on 2023/3/20
 */
@ServiceModule("change_order")
@Service
public class ObjectChangeOrderService {
    @Autowired
    private ChangeOrderLogicService changeOrderLogicService;
    @Autowired
    private ChangeOrderRuleLogicService changeOrderRuleLogicService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private LicenseService licenseService;

    @ServiceMethod("findSupportObjects")
    public FindDescribeApiNames.Result findSupportObjects(ServiceContext context) {
        List<DescribeInfo> describeInfos = changeOrderLogicService.findSupportChangeOrderDescribes(context.getTenantId());
        return FindDescribeApiNames.Result.of(describeInfos);
    }

    @ServiceMethod("findDescribesByOriginalApiName")
    public FindDescribesByOriginalApiName.Result findDescribesByOriginalApiName(FindDescribesByOriginalApiName.Arg arg, ServiceContext context) {
        OriginalAndChangeDescribes changeOrderDescribes = changeOrderLogicService.findDescribesByOriginalApiName(context.getUser(), arg.getDescribeApiName());
        return FindDescribesByOriginalApiName.Result.builder()
                .changeOrderDescribes(FindDescribesByOriginalApiName.ChangeOrderAllDescribes.of(changeOrderDescribes))
                .build();
    }

    @ServiceMethod("open")
    public OpenChangeOrder.Result openChangeOrder(OpenChangeOrder.Arg arg, ServiceContext context) {
        OpenChangeOrderResult openChangeOrderResult = changeOrderLogicService.openChangeOrder(context.getUser(), arg.getDescribeApiName());
        return OpenChangeOrder.Result.buildResult(openChangeOrderResult);
    }

    @ServiceMethod("close")
    public OpenChangeOrder.Result closeChangeOrder(OpenChangeOrder.Arg arg, ServiceContext context) {
        changeOrderLogicService.closeChangeOrder(context.getUser(), arg.getDescribeApiName());
        return OpenChangeOrder.Result.buildSuccess();
    }

    @ServiceMethod("validateCount")
    public OpenChangeOrder.Result checkChangeRuleCount(OpenChangeOrder.Arg arg, ServiceContext context) {
        changeOrderRuleLogicService.checkCount(context.getUser(), arg.getDescribeApiName());
        return OpenChangeOrder.Result.buildSuccess();
    }

    @ServiceMethod("findChangeRules")
    public FindChangeRule.FindAllResult findChangeRules(ServiceContext context) {
        List<MtChangeOrderRule> changeOrderRules = changeOrderRuleLogicService.findAll(context.getUser());
        List<FindChangeRule.MtChangeOrderRuleDTO> changeOrderRuleDTOList = convertTo(context.getUser(), changeOrderRules);
        return FindChangeRule.FindAllResult.of(changeOrderRuleDTOList);
    }

    private List<FindChangeRule.MtChangeOrderRuleDTO> convertTo(User user, List<MtChangeOrderRule> changeOrderRules) {
        List<FindChangeRule.MtChangeOrderRuleDTO> changeOrderRuleDTOList = Lists.newArrayList();
        Set<String> describeApiNames = Sets.newHashSet();
        for (MtChangeOrderRule changeOrderRule : changeOrderRules) {
            describeApiNames.add(changeOrderRule.getChangeDescribeApiName());
            describeApiNames.add(changeOrderRule.getOriginalDescribeApiName());
            changeOrderRuleDTOList.add(FindChangeRule.MtChangeOrderRuleDTO.from(changeOrderRule));
        }
        Map<String, String> displayNameByApiNames = describeLogicService.findDisplayNameByApiNames(user.getTenantId(), describeApiNames);
        for (FindChangeRule.MtChangeOrderRuleDTO changeOrderRuleDTO : changeOrderRuleDTOList) {
            String changeDescribeApiName = changeOrderRuleDTO.getChangeDescribeApiName();
            changeOrderRuleDTO.setChangeDescribeDisplayName(displayNameByApiNames.get(changeDescribeApiName));
            String originalDescribeApiName = changeOrderRuleDTO.getOriginalDescribeApiName();
            changeOrderRuleDTO.setOriginalDescribeDisplayName(displayNameByApiNames.get(originalDescribeApiName));
        }
        return changeOrderRuleDTOList;
    }

    @ServiceMethod("findChangeRule")
    public FindChangeRule.FindOneResult findChangeRule(FindChangeRule.Arg arg, ServiceContext context) { // 后台获得变更规则详细信息
        MtChangeOrderRule changeOrderRule = changeOrderRuleLogicService.findByRuleName(context.getUser(), arg.getRuleApiName(), true)
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.CHANGE_RULE_NOT_EXIST_OR_DELETED)));
        return FindChangeRule.FindOneResult.of(changeOrderRule);
    }

    @ServiceMethod("addChangeRule")
    public SaveChangeRule.Result createChangeRule(SaveChangeRule.Arg arg, ServiceContext context) {
        changeOrderRuleLogicService.create(context.getUser(), arg.getChangeRule());
        return SaveChangeRule.Result.buildSuccess();
    }

    @ServiceMethod("updateChangeRule")
    public SaveChangeRule.Result updateChangeRule(SaveChangeRule.Arg arg, ServiceContext context) {
        changeOrderRuleLogicService.update(context.getUser(), arg.getChangeRule());
        return SaveChangeRule.Result.buildSuccess();
    }

    @ServiceMethod("enableChangeRule")
    public FindChangeRule.Result enableChangeRule(FindChangeRule.Arg arg, ServiceContext context) {
        changeOrderRuleLogicService.enable(context.getUser(), arg.getRuleApiName());
        return FindChangeRule.Result.buildSuccess();
    }

    @ServiceMethod("disableChangeRule")
    public FindChangeRule.Result disableChangeRule(FindChangeRule.Arg arg, ServiceContext context) {
        changeOrderRuleLogicService.disable(context.getUser(), arg.getRuleApiName());
        return FindChangeRule.Result.buildSuccess();
    }

    @ServiceMethod("deleteChangeRule")
    public FindChangeRule.Result deleteChangeRule(FindChangeRule.Arg arg, ServiceContext context) {
        changeOrderRuleLogicService.delete(context.getUser(), arg.getRuleApiName());
        return FindChangeRule.Result.buildSuccess();
    }

    @ServiceMethod("syncChangeFields")
    public OpenChangeOrder.Result syncChangeFieldsByOriginalDescribe(OpenChangeOrder.Arg arg, ServiceContext context) {
        changeOrderLogicService.syncChangeOrderDescribeWithOriginalDescribe(context.getUser(), arg.getDescribeApiName());
        return OpenChangeOrder.Result.buildSuccess();
    }

    @ServiceMethod("isOpenChangeOrder")
    public IsOpenChangeOrder.Result isOpenChangeOrder(IsOpenChangeOrder.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getDescribeApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_EMPTY));
        }
        boolean isOpen = changeOrderLogicService.isOpenChangeOrder(context.getUser(), arg.getDescribeApiName());
        return IsOpenChangeOrder.Result.builder().result(isOpen).build();
    }

    @ServiceMethod("isOpenChangeOrderForMasterObj")
    public IsOpenChangeOrder.Result isOpenChangeOrderForMasterObj(IsOpenChangeOrder.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getDescribeApiName()) || !licenseService.isSupportChangeOrder(context.getTenantId())) {
            return IsOpenChangeOrder.Result.builder().result(false).build();
        }
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        Optional<MasterDetailFieldDescribe> masterDetailFieldDescribeOptional = ObjectDescribeExt.of(describe).getMasterDetailFieldDescribe();
        if (!masterDetailFieldDescribeOptional.isPresent()) {
            return IsOpenChangeOrder.Result.builder().result(false).build();
        }
        MasterDetailFieldDescribe masterDetailFieldDescribe = masterDetailFieldDescribeOptional.get();
        boolean openChangeOrder = changeOrderLogicService.isOpenChangeOrder(context.getUser(), masterDetailFieldDescribe.getTargetApiName());
        return IsOpenChangeOrder.Result.builder().result(openChangeOrder).build();
    }
}
