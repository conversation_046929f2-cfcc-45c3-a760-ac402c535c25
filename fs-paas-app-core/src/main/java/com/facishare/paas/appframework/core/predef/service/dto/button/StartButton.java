package com.facishare.paas.appframework.core.predef.service.dto.button;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface StartButton {

    @Data
    class Arg {
        @JSONField(name = "M1")
        private String objectDataId;
        @JSONField(name = "M2")
        private List args;
    }
    @Data
    class Result {
        @JSONField(name = "M2")
        private Map object_data;
        private Map details;
        private String target_describe_api_name;
    }
}
