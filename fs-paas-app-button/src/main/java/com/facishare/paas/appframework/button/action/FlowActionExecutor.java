package com.facishare.paas.appframework.button.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.flow.FlowCommonService;
import com.facishare.paas.appframework.flow.dto.OneFlowExecuteRequest;
import com.facishare.paas.appframework.flow.dto.OneFlowExecuteResponse;
import com.facishare.paas.appframework.metadata.IAcionType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.util.*;

/**
 * OneFlow执行器
 */
@Slf4j
@Component
public class FlowActionExecutor implements ActionExecutor {

    @Autowired
    private FlowCommonService flowCommonService;

    @Autowired
    private ArgumentProcessorService argumentProcessorService;

    @Override
    public ActionExecutorType getType() {
        return ActionExecutorType.CURRENT_ONE_FLOW;
    }

    @Override
    public ButtonExecutor.Result invoke(ButtonExecutor.Arg arg, ActionExecutorContext context) {
        IUdefAction action = context.getAction();
        IObjectData objectData = arg.getObjectData();
        User user = context.getUser();
        IObjectDescribe describe = context.getDescribe();
        IUdefButton button = context.getButton();

        // Parse OneFlow parameters
        FlowActionParameter flowActionParameter = FlowActionParameter.fromJson(action.getActionParamter());
        log.debug("startCustomButton objectData:{} actionParameter:{}", objectData, flowActionParameter);

        try {
            // Process OneFlow parameters
            List<OneFlowArg> processedArgs = argumentProcessorService.processOneFlowArguments(
                    flowActionParameter.getFlowArgList(), arg.getArgs(), objectData, describe, user, button);

            // Build parameter map
            Map<String, Object> flowArgs = new HashMap<>();
            if (CollectionUtils.notEmpty(processedArgs)) {
                processedArgs.forEach(arg1 -> flowArgs.put(arg1.getName(), arg1.getRealValue()));
            }

            OneFlowExecuteRequest request = new OneFlowExecuteRequest();
            request.setFlowApiName(flowActionParameter.getFlowApiName());
            request.setFlowArgs(flowArgs);
            request.setAsync(flowActionParameter.isAsync());
            request.setTenantId(Long.valueOf(user.getTenantId()));
            request.setUserId(Long.parseLong(user.getUserIdOrOutUserIdIfOutUser()));
            request.setUserName(user.getUserName());
            // Call OneFlow service
            OneFlowExecuteResponse response = flowCommonService.executeFlow(user, request);
            // Process execution result
            return buildExecuteResult(response);
        } catch (RuntimeException e) {
            log.error("Failed to execute OneFlow", e);
            return buildErrorResult(e, flowActionParameter);
        }
    }

    /**
     * 构建执行结果
     *
     * @param response 执行响应
     * @return 按钮执行结果
     */
    private ButtonExecutor.Result buildExecuteResult(OneFlowExecuteResponse response) {
        return ButtonExecutor.Result.builder()
                .hasReturnValue(true)
                .returnType(IAcionType.ONE_FLOW)
                .returnValue(response.getWorkflowInstanceId())
                .build();
    }

    /**
     * 构建错误结果
     * 参考AbstractFuncAction.java中handleException的实现逻辑
     *
     * @param e 异常
     * @return 按钮执行结果
     */
    private ButtonExecutor.Result buildErrorResult(RuntimeException e, FlowActionParameter flowActionParameter) {
        if (e instanceof ValidateException) {
            throw e;
        }
        if (e instanceof RestProxyBusinessException) {
            throw new ValidateException(e.getMessage(), ((RestProxyBusinessException) e).getCode());
        }
        Throwable rootCause = ExceptionUtils.getRootCause(e);
        if (rootCause instanceof SocketTimeoutException) {
            String flowApiName = Optional.ofNullable(flowActionParameter)
                    .map(FlowActionParameter::getFlowApiName)
                    .orElse("");
            throw new ValidateException(flowApiName + I18N.text(I18NKey.ONE_FLOW_TIMEOUT));
        }
        throw new ValidateException(I18N.text(I18NKey.ONE_FLOW_FAIL));
    }

    /**
     * OneFlow动作参数
     */
    @Data
    public static class FlowActionParameter {
        /**
         * 流程ID
         */
        @JSONField(name = "oneflow_api_name")
        private String flowApiName;

        /**
         * 流程参数列表
         */
        @JSONField(name = "oneflow_args")
        private List<OneFlowArg> flowArgList;

        /**
         * 是否异步
         */
        private boolean async;

        /**
         * 从JSON字符串创建参数对象
         *
         * @param jsonStr JSON字符串
         * @return 参数对象
         */
        public static FlowActionParameter fromJson(String jsonStr) {
            FlowActionParameter parameter = JSON.parseObject(jsonStr, FlowActionParameter.class);
            if (parameter.getFlowArgList() == null) {
                parameter.setFlowArgList(Collections.emptyList());
            }
            return parameter;
        }
    }
}
