package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.impl.DocumentBasedBean;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/07/30
 */
public class MapViewBubbleInfo extends AbstractComponentInfoDocument implements IMapViewBubbleInfo {
    private static final long serialVersionUID = -3676610604284316884L;

    public MapViewBubbleInfo() {

    }

    public MapViewBubbleInfo(Map map) {
        super(map);
    }

    @Override
    public String getField() {
        return get(FIELD, String.class);
    }

    @Override
    public List<IMapViewOptionBubbleInfo> getOptions() {
        List<Map> options = get(OPTIONS, List.class);
        return CollectionUtils.empty(options) ? Collections.emptyList()
                : options.stream().map(MapViewOptionBubbleInfo::new).collect(Collectors.toList());
    }

    @Override
    public void setOptions(List<IMapViewOptionBubbleInfo> options) {
        if (CollectionUtils.empty(options)) {
            set(OPTIONS, Collections.emptyList());
            return;
        }
        List<Map> collect = options.stream()
                .map(it -> ((MapViewOptionBubbleInfo) it))
                .map(DocumentBasedBean::getContainerDocument)
                .collect(Collectors.toList());
        set(OPTIONS, collect);
    }
}
