package com.facishare.paas.appframework.common.service.model

import com.facishare.paas.appframework.common.service.dto.InternationalItem
import com.facishare.paas.appframework.common.service.dto.SendNewCrmMessageModel
import com.google.common.collect.Maps
import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：NewCrmNotification类的单元测试
 */
class NewCrmNotificationTest extends Specification {

    def "test constants are defined correctly"() {
        expect: "验证常量值正确定义"
        NewCrmNotification.CUSTOM_REMIND_RECORD_TYPE == 92
        NewCrmNotification.CUSTOM_REMIND_DETAIL_RECORD_TYPE == 115
        NewCrmNotification.CUSTOM_REMIND_JOB_SCHEDULE_TYPE == 118
        NewCrmNotification.PAY_CALLBACK_REMIND_TYPE == 71
    }

    def "test builder pattern"() {
        when: "使用builder创建实例"
        def notification = NewCrmNotification.builder()
                .sourceId("source123")
                .senderId("sender456")
                .type(92)
                .receiverIDs(["1001", "1002"] as Set)
                .remindSender(true)
                .title("Test Title")
                .fullContent("Test Content")
                .urlType(1)
                .appId("testApp")
                .objectApiName("TestObj")
                .objectId("obj123")
                .build()

        then: "验证设置的值"
        notification.sourceId == "source123"
        notification.senderId == "sender456"
        notification.type == 92
        notification.receiverIDs.contains("1001")
        notification.receiverIDs.contains("1002")
        notification.remindSender == true
        notification.title == "Test Title"
        notification.fullContent == "Test Content"
        notification.urlType == 1
        notification.appId == "testApp"
        notification.objectApiName == "TestObj"
        notification.objectId == "obj123"
    }

    @Unroll
    def "test getTypeWithDefaultValue with type=#type"() {
        given: "创建通知实例"
        def notification = NewCrmNotification.builder()
                .type(type)
                .build()

        when: "调用getTypeWithDefaultValue方法"
        def result = notification.getTypeWithDefaultValue()

        then: "验证返回值"
        result == expected

        where:
        type | expected
        null | 92  // CUSTOM_REMIND_RECORD_TYPE
        92   | 92
        115  | 115
        118  | 118
        71   | 71
        999  | 999
    }

    def "test addUrlParameter method"() {
        given: "创建通知实例"
        def notification = NewCrmNotification.builder().build()

        when: "添加URL参数"
        def result = notification.addUrlParameter("key1", "value1")

        then: "应该返回自身并设置参数"
        result == notification
        notification.urlParameter != null
        notification.urlParameter.get("key1") == "value1"

        when: "继续添加参数"
        notification.addUrlParameter("key2", "value2")

        then: "应该包含所有参数"
        notification.urlParameter.size() == 2
        notification.urlParameter.get("key1") == "value1"
        notification.urlParameter.get("key2") == "value2"
    }

    def "test addUrlParameter with existing urlParameter"() {
        given: "创建带有现有URL参数的通知实例"
        def existingParams = Maps.newHashMap()
        existingParams.put("existing", "value")
        def notification = NewCrmNotification.builder()
                .urlParameter(existingParams)
                .build()

        when: "添加新参数"
        notification.addUrlParameter("new", "newValue")

        then: "应该保留现有参数并添加新参数"
        notification.urlParameter.size() == 2
        notification.urlParameter.get("existing") == "value"
        notification.urlParameter.get("new") == "newValue"
    }

    @Unroll
    def "test isTextMessage with urlType=#urlType"() {
        given: "创建通知实例"
        def notification = NewCrmNotification.builder()
                .urlType(urlType)
                .build()

        when: "调用isTextMessage方法"
        def result = notification.isTextMessage()

        then: "验证返回值"
        result == expected

        where:
        urlType | expected
        0       | true
        1       | false
        2       | false
        -1      | false
    }

    def "test getObjectApiName with urlParameter override"() {
        given: "创建带有objectApiName的通知实例"
        def notification = NewCrmNotification.builder()
                .objectApiName("originalApi")
                .build()

        when: "没有URL参数时"
        def result1 = notification.getObjectApiName()

        then: "应该返回原始值"
        result1 == "originalApi"

        when: "添加URL参数中的objectApiName"
        notification.addUrlParameter("objectApiName", "overrideApi")
        def result2 = notification.getObjectApiName()

        then: "应该返回URL参数中的值"
        result2 == "overrideApi"

        when: "URL参数中的objectApiName为空"
        notification.addUrlParameter("objectApiName", "")
        def result3 = notification.getObjectApiName()

        then: "应该返回原始值"
        result3 == "originalApi"

        when: "URL参数中的objectApiName为null"
        notification.addUrlParameter("objectApiName", null)
        def result4 = notification.getObjectApiName()

        then: "应该返回原始值"
        result4 == "originalApi"
    }

    def "test getObjectId with urlParameter override"() {
        given: "创建带有objectId的通知实例"
        def notification = NewCrmNotification.builder()
                .objectId("originalId")
                .build()

        when: "没有URL参数时"
        def result1 = notification.getObjectId()

        then: "应该返回原始值"
        result1 == "originalId"

        when: "添加URL参数中的id"
        notification.addUrlParameter("id", "overrideId")
        def result2 = notification.getObjectId()

        then: "应该返回URL参数中的值"
        result2 == "overrideId"

        when: "URL参数中的id为空"
        notification.addUrlParameter("id", "")
        def result3 = notification.getObjectId()

        then: "应该返回原始值"
        result3 == "originalId"

        when: "URL参数中的id为null"
        notification.addUrlParameter("id", null)
        def result4 = notification.getObjectId()

        then: "应该返回原始值"
        result4 == "originalId"
    }

    def "test getObjectApiName and getObjectId with empty urlParameter"() {
        given: "创建带有空URL参数的通知实例"
        def notification = NewCrmNotification.builder()
                .objectApiName("testApi")
                .objectId("testId")
                .urlParameter(Maps.newHashMap())
                .build()

        when: "调用getter方法"
        def apiName = notification.getObjectApiName()
        def objectId = notification.getObjectId()

        then: "应该返回原始值"
        apiName == "testApi"
        objectId == "testId"
    }

    def "test all fields can be set and retrieved"() {
        given: "创建包含所有字段的通知实例"
        def titleInfo = InternationalItem.builder().build()
        def fullContentInfo = InternationalItem.builder().build()
        def lastSummaryInfo = InternationalItem.builder().build()
        def departments = [new SendNewCrmMessageModel.DepartmentReceiver()]
        def bodyForm = [new KeyValueItem()]
        def extraChannelList = ["channel1", "channel2"]
        def extraDataMap = ["key": "value"]
        def templateIdKeyListMap = ["template": [["key": "value"]]]

        def notification = NewCrmNotification.builder()
                .sourceId("source")
                .senderId("sender")
                .type(92)
                .receiverIDs(["1001"] as Set)
                .departments(departments)
                .outEmployees(["2001"] as Set)
                .remindSender(true)
                .title("title")
                .titleInfo(titleInfo)
                .fullContent("content")
                .fullContentInfo(fullContentInfo)
                .urlType(1)
                .urlParameter(["param": "value"])
                .appId("app")
                .lastSummary("summary")
                .lastSummaryInfo(lastSummaryInfo)
                .innerPlatformWebUrl("webUrl")
                .innerPlatformMobileUrl("mobileUrl")
                .outPlatformUrl("outUrl")
                .objectApiName("api")
                .objectId("id")
                .bodyForm(bodyForm)
                .extraChannelList(extraChannelList)
                .extraDataMap(extraDataMap)
                .templateIdKeyListMap(templateIdKeyListMap)
                .build()

        expect: "所有字段都应该正确设置"
        notification.sourceId == "source"
        notification.senderId == "sender"
        notification.type == 92
        notification.receiverIDs.contains("1001")
        notification.departments == departments
        notification.outEmployees.contains("2001")
        notification.remindSender == true
        notification.title == "title"
        notification.titleInfo == titleInfo
        notification.fullContent == "content"
        notification.fullContentInfo == fullContentInfo
        notification.urlType == 1
        notification.urlParameter.get("param") == "value"
        notification.appId == "app"
        notification.lastSummary == "summary"
        notification.lastSummaryInfo == lastSummaryInfo
        notification.innerPlatformWebUrl == "webUrl"
        notification.innerPlatformMobileUrl == "mobileUrl"
        notification.outPlatformUrl == "outUrl"
        notification.objectApiName == "api"
        notification.objectId == "id"
        notification.bodyForm == bodyForm
        notification.extraChannelList == extraChannelList
        notification.extraDataMap == extraDataMap
        notification.templateIdKeyListMap == templateIdKeyListMap
    }
}
