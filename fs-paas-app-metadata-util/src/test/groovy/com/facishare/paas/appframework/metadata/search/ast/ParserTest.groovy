package com.facishare.paas.appframework.metadata.search.ast

import com.facishare.paas.appframework.metadata.search.SearchQuery
import spock.lang.Specification
import spock.lang.Unroll

/**
 * Parser类的Groovy单元测试
 */
class ParserTest extends Specification {

    /**
     * GenerateByAI
     * 测试内容描述：测试解析单个数字的场景，验证是否能正确解析为过滤器节点
     */
    @Unroll
    def "parseExpressionTest_SingleNumber"() {
        given: "准备包含单个数字的token列表"
        def tokens = [new Token(TokenType.NUMBER, number)]
        def parser = new Parser(tokens)

        when: "执行解析"
        def result = parser.parseExpression()

        then: "验证结果是过滤器节点且包含正确的索引信息"
        result.isFilterNode()
        result.getFilter() != null
        result.getFilter().getFieldName() == "__FILTER_INDEX__"
        result.getFilter().getFieldValues()[0] == number

        where: "测试数据"
        number << ["123", "0", "-1", "999999"]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析AND表达式的场景，验证操作符和操作数是否正确
     */
    @Unroll
    def "parseExpressionTest_AndExpression"() {
        given: "准备AND表达式的token列表"
        def tokens = [
                new Token(TokenType.NUMBER, left),
                new Token(TokenType.AND, "AND"),
                new Token(TokenType.NUMBER, right)
        ]
        def parser = new Parser(tokens)

        when: "执行解析"
        def result = parser.parseExpression()

        then: "验证结果是AND操作符节点且结构正确"
        !result.isFilterNode()
        result.getConnector() == SearchQuery.Connector.AND
        result.getSearchQueryContainer().size() == 2
        
        with(result.getSearchQueryContainer()[0]) {
            isFilterNode()
            getFilter().getFieldName() == "__FILTER_INDEX__"
            getFilter().getFieldValues()[0] == left
        }
        
        with(result.getSearchQueryContainer()[1]) {
            isFilterNode()
            getFilter().getFieldName() == "__FILTER_INDEX__"
            getFilter().getFieldValues()[0] == right
        }

        where: "测试数据"
        left  | right
        "1"   | "2"
        "100" | "200"
        "0"   | "1"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析OR表达式的场景，验证操作符和操作数是否正确
     */
    @Unroll
    def "parseExpressionTest_OrExpression"() {
        given: "准备OR表达式的token列表"
        def tokens = [
                new Token(TokenType.NUMBER, left),
                new Token(TokenType.OR, "OR"),
                new Token(TokenType.NUMBER, right)
        ]
        def parser = new Parser(tokens)

        when: "执行解析"
        def result = parser.parseExpression()

        then: "验证结果是OR操作符节点且结构正确"
        !result.isFilterNode()
        result.getConnector() == SearchQuery.Connector.OR
        result.getSearchQueryContainer().size() == 2
        
        with(result.getSearchQueryContainer()[0]) {
            isFilterNode()
            getFilter().getFieldName() == "__FILTER_INDEX__"
            getFilter().getFieldValues()[0] == left
        }
        
        with(result.getSearchQueryContainer()[1]) {
            isFilterNode()
            getFilter().getFieldName() == "__FILTER_INDEX__"
            getFilter().getFieldValues()[0] == right
        }

        where: "测试数据"
        left  | right
        "1"   | "2"
        "100" | "200"
        "0"   | "1"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析带括号的表达式场景，验证括号内的表达式是否被正确解析
     */
    def "parseExpressionTest_ParenthesesExpression"() {
        given: "准备带括号的表达式token列表"
        def tokens = [
                new Token(TokenType.LPAREN, "("),
                new Token(TokenType.NUMBER, "1"),
                new Token(TokenType.AND, "AND"),
                new Token(TokenType.NUMBER, "2"),
                new Token(TokenType.RPAREN, ")")
        ]
        def parser = new Parser(tokens)

        when: "执行解析"
        def result = parser.parseExpression()

        then: "验证结果是AND操作符节点且结构正确"
        !result.isFilterNode()
        result.getConnector() == SearchQuery.Connector.AND
        result.getSearchQueryContainer().size() == 2
        result.getSearchQueryContainer().every { it.isFilterNode() }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析复杂嵌套表达式的场景，验证多层嵌套的表达式是否被正确解析
     */
    def "parseExpressionTest_ComplexExpression"() {
        given: "准备复杂表达式的token列表"
        def tokens = [
                new Token(TokenType.NUMBER, "1"),
                new Token(TokenType.AND, "AND"),
                new Token(TokenType.LPAREN, "("),
                new Token(TokenType.NUMBER, "2"),
                new Token(TokenType.OR, "OR"),
                new Token(TokenType.NUMBER, "3"),
                new Token(TokenType.RPAREN, ")")
        ]
        def parser = new Parser(tokens)

        when: "执行解析"
        def result = parser.parseExpression()

        then: "验证结果是AND节点且包含正确的子结构"
        !result.isFilterNode()
        result.getConnector() == SearchQuery.Connector.AND
        result.getSearchQueryContainer().size() == 2
        result.getSearchQueryContainer()[0].isFilterNode()
        
        with(result.getSearchQueryContainer()[1]) {
            !isFilterNode()
            getConnector() == SearchQuery.Connector.OR
            getSearchQueryContainer().size() == 2
            getSearchQueryContainer().every { it.isFilterNode() }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析非法表达式的场景，验证是否抛出预期的异常
     */
    @Unroll
    def "parseExpressionError_InvalidExpression"() {
        given: "准备非法表达式的token列表"
        def tokens = invalidTokens
        def parser = new Parser(tokens)

        when: "执行解析"
        parser.parseExpression()

        then: "验证抛出RuntimeException异常"
        thrown(RuntimeException)

        where: "测试数据"
        invalidTokens << [
                [new Token(TokenType.AND, "AND")],
                [new Token(TokenType.OR, "OR")],
                [new Token(TokenType.RPAREN, ")")]
        ]
    }
} 