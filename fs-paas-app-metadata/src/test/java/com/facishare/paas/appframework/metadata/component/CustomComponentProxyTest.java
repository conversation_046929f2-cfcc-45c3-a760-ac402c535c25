package com.facishare.paas.appframework.metadata.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CustomComponentProxyTest {

  @Mock
  private CustomComponentProxy customComponentProxy;

  private Map<String, String> headers;

  @BeforeEach
  void setUp() {
    headers = Maps.newHashMap();
    headers.put("Content-Type", "application/json");
    headers.put("Authorization", "Bearer test-token");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试queryComponentList方法的正常调用
   */
  @Test
  @DisplayName("正常场景 - 测试queryComponentList方法")
  void testQueryComponentList_NormalScenario() {
    // 准备测试数据
    QueryComponentList.Arg arg = QueryComponentList.Arg.builder()
        .targetScope("ObjectDetailPage")
        .objectApiName("TestObj")
        .build();
    
    // 准备返回结果
    QueryComponentList.Result expectedResult = new QueryComponentList.Result();
    expectedResult.setCode(0);
    expectedResult.setMessage("success");
    
    ComponentData componentData = new ComponentData();
    componentData.setApiName("test_component");
    componentData.setName("测试组件");
    componentData.setUrl("http://test.com");
    
    expectedResult.setData(Lists.newArrayList(componentData));
    
    // 配置Mock行为
    when(customComponentProxy.queryComponentList(any(QueryComponentList.Arg.class), anyMap()))
        .thenReturn(expectedResult);
    
    // 执行方法调用
    QueryComponentList.Result result = customComponentProxy.queryComponentList(arg, headers);
    
    // 验证结果
    assertNotNull(result, "返回结果不应为空");
    assertEquals(0, result.getCode(), "状态码应为0");
    assertEquals("success", result.getMessage(), "消息应为success");
    assertNotNull(result.getData(), "数据不应为空");
    assertEquals(1, result.getData().size(), "应返回1个组件数据");
    assertEquals("test_component", result.getData().get(0).getApiName(), "组件API名称应正确");
    
    // 验证Mock交互
    verify(customComponentProxy, times(1)).queryComponentList(arg, headers);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试batchQueryComponentData方法的正常调用
   */
  @Test
  @DisplayName("正常场景 - 测试batchQueryComponentData方法")
  void testBatchQueryComponentData_NormalScenario() {
    // 准备测试数据
    BatchQueryComponentData.Arg arg = BatchQueryComponentData.Arg.builder()
        .apiNames(Lists.newArrayList("component1", "component2"))
        .build();
    
    // 准备返回结果
    BatchQueryComponentData.Result expectedResult = new BatchQueryComponentData.Result();
    expectedResult.setCode(0);
    expectedResult.setMessage("success");
    
    ComponentData data1 = new ComponentData();
    data1.setApiName("component1");
    data1.setName("组件1");
    data1.setUrl("http://component1.com");
    
    ComponentData data2 = new ComponentData();
    data2.setApiName("component2");
    data2.setName("组件2");
    data2.setUrl("http://component2.com");
    
    expectedResult.setData(Lists.newArrayList(data1, data2));
    
    // 配置Mock行为
    when(customComponentProxy.batchQueryComponentData(any(BatchQueryComponentData.Arg.class), anyMap()))
        .thenReturn(expectedResult);
    
    // 执行方法调用
    BatchQueryComponentData.Result result = customComponentProxy.batchQueryComponentData(arg, headers);
    
    // 验证结果
    assertNotNull(result, "返回结果不应为空");
    assertEquals(0, result.getCode(), "状态码应为0");
    assertEquals("success", result.getMessage(), "消息应为success");
    assertNotNull(result.getData(), "数据不应为空");
    assertEquals(2, result.getData().size(), "应返回2个组件数据");
    assertEquals("component1", result.getData().get(0).getApiName(), "第一个组件API名称应正确");
    assertEquals("component2", result.getData().get(1).getApiName(), "第二个组件API名称应正确");
    
    // 验证Mock交互
    verify(customComponentProxy, times(1)).batchQueryComponentData(arg, headers);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试queryComponentList方法的错误场景
   */
  @Test
  @DisplayName("异常场景 - 测试queryComponentList方法返回错误")
  void testQueryComponentList_ErrorScenario() {
    // 准备测试数据
    QueryComponentList.Arg arg = QueryComponentList.Arg.builder()
        .targetScope("InvalidScope")
        .objectApiName("InvalidObj")
        .build();
    
    // 准备错误返回结果
    QueryComponentList.Result errorResult = new QueryComponentList.Result();
    errorResult.setCode(500);
    errorResult.setMessage("Internal Server Error");
    errorResult.setData(null);
    
    // 配置Mock行为
    when(customComponentProxy.queryComponentList(any(QueryComponentList.Arg.class), anyMap()))
        .thenReturn(errorResult);
    
    // 执行方法调用
    QueryComponentList.Result result = customComponentProxy.queryComponentList(arg, headers);
    
    // 验证错误结果
    assertNotNull(result, "返回结果不应为空");
    assertEquals(500, result.getCode(), "状态码应为500");
    assertEquals("Internal Server Error", result.getMessage(), "错误消息应正确");
    assertFalse(result.isSuccess(), "应返回失败状态");
    
    // 验证Mock交互
    verify(customComponentProxy, times(1)).queryComponentList(arg, headers);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试batchQueryComponentData方法的错误场景
   */
  @Test
  @DisplayName("异常场景 - 测试batchQueryComponentData方法返回错误")
  void testBatchQueryComponentData_ErrorScenario() {
    // 准备测试数据
    BatchQueryComponentData.Arg arg = BatchQueryComponentData.Arg.builder()
        .apiNames(Lists.newArrayList("invalid_component"))
        .build();
    
    // 准备错误返回结果
    BatchQueryComponentData.Result errorResult = new BatchQueryComponentData.Result();
    errorResult.setCode(404);
    errorResult.setMessage("Component not found");
    errorResult.setData(Lists.newArrayList());
    
    // 配置Mock行为
    when(customComponentProxy.batchQueryComponentData(any(BatchQueryComponentData.Arg.class), anyMap()))
        .thenReturn(errorResult);
    
    // 执行方法调用
    BatchQueryComponentData.Result result = customComponentProxy.batchQueryComponentData(arg, headers);
    
    // 验证错误结果
    assertNotNull(result, "返回结果不应为空");
    assertEquals(404, result.getCode(), "状态码应为404");
    assertEquals("Component not found", result.getMessage(), "错误消息应正确");
    assertFalse(result.isSuccess(), "应返回失败状态");
    assertNotNull(result.getData(), "数据不应为null");
    assertTrue(result.getData().isEmpty(), "数据应为空列表");
    
    // 验证Mock交互
    verify(customComponentProxy, times(1)).batchQueryComponentData(arg, headers);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试queryComponentList方法的空headers场景
   */
  @Test
  @DisplayName("边界场景 - 测试queryComponentList方法使用空headers")
  void testQueryComponentList_EmptyHeaders() {
    // 准备测试数据
    QueryComponentList.Arg arg = QueryComponentList.Arg.builder()
        .targetScope("ObjectDetailPage")
        .objectApiName("TestObj")
        .build();
    
    Map<String, String> emptyHeaders = Maps.newHashMap();
    
    // 准备返回结果
    QueryComponentList.Result expectedResult = new QueryComponentList.Result();
    expectedResult.setCode(0);
    expectedResult.setMessage("success");
    
    // 配置Mock行为
    when(customComponentProxy.queryComponentList(any(QueryComponentList.Arg.class), anyMap()))
        .thenReturn(expectedResult);
    
    // 执行方法调用
    QueryComponentList.Result result = customComponentProxy.queryComponentList(arg, emptyHeaders);
    
    // 验证结果
    assertNotNull(result, "返回结果不应为空");
    assertEquals(0, result.getCode(), "状态码应为0");
    
    // 验证Mock交互
    verify(customComponentProxy, times(1)).queryComponentList(arg, emptyHeaders);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试batchQueryComponentData方法的空API名称列表场景
   */
  @Test
  @DisplayName("边界场景 - 测试batchQueryComponentData方法使用空API名称列表")
  void testBatchQueryComponentData_EmptyApiNames() {
    // 准备测试数据
    BatchQueryComponentData.Arg arg = BatchQueryComponentData.Arg.builder()
        .apiNames(Lists.newArrayList())
        .build();
    
    // 准备返回结果
    BatchQueryComponentData.Result expectedResult = new BatchQueryComponentData.Result();
    expectedResult.setCode(0);
    expectedResult.setMessage("success");
    expectedResult.setData(Lists.newArrayList());
    
    // 配置Mock行为
    when(customComponentProxy.batchQueryComponentData(any(BatchQueryComponentData.Arg.class), anyMap()))
        .thenReturn(expectedResult);
    
    // 执行方法调用
    BatchQueryComponentData.Result result = customComponentProxy.batchQueryComponentData(arg, headers);
    
    // 验证结果
    assertNotNull(result, "返回结果不应为空");
    assertEquals(0, result.getCode(), "状态码应为0");
    assertTrue(result.getData().isEmpty(), "数据应为空列表");
    
    // 验证Mock交互
    verify(customComponentProxy, times(1)).batchQueryComponentData(arg, headers);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试方法调用次数验证
   */
  @Test
  @DisplayName("正常场景 - 测试方法调用次数验证")
  void testMethodCallCounts() {
    // 准备测试数据
    QueryComponentList.Arg queryArg = QueryComponentList.Arg.builder()
        .targetScope("ObjectDetailPage")
        .objectApiName("TestObj")
        .build();
    
    BatchQueryComponentData.Arg batchArg = BatchQueryComponentData.Arg.builder()
        .apiNames(Lists.newArrayList("component1"))
        .build();
    
    // 配置Mock行为
    when(customComponentProxy.queryComponentList(any(), anyMap()))
        .thenReturn(new QueryComponentList.Result());
    when(customComponentProxy.batchQueryComponentData(any(), anyMap()))
        .thenReturn(new BatchQueryComponentData.Result());
    
    // 多次调用方法
    customComponentProxy.queryComponentList(queryArg, headers);
    customComponentProxy.queryComponentList(queryArg, headers);
    customComponentProxy.batchQueryComponentData(batchArg, headers);
    
    // 验证调用次数
    verify(customComponentProxy, times(2)).queryComponentList(any(), anyMap());
    verify(customComponentProxy, times(1)).batchQueryComponentData(any(), anyMap());
  }
}
