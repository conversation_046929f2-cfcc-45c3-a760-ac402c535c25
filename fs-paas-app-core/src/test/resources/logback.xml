<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.github.trace" level="ERROR"/>
    <logger name="com.facishare" level="INFO"/>
    <logger name="com.github.autoconf" level="ERROR"/>
    <logger name="org.apache.commons.beanutils" level="ERROR"/>

    <root level="WARN">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>