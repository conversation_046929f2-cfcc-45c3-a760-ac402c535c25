package com.facishare.paas.appframework.log;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.TeamMemberInfo;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.TeamRoleInfo;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component("teamMemberEditModifyRecordProvider")
public class TeamMemberEditModifyRecordProvider extends TeamMemberModifyRecordProvider {
    @Override
    public String getApiName() {
        return ActionType.ModifySale.getId();
    }

    @Override
    public void logTeamMember(IObjectData oldData,
                              IObjectData data,
                              IObjectDescribe objectDescribe,
                              Map<String, List<TeamMemberInfo.Member>> memberInfos,
                              List<TeamMemberInfo.Msg> msgList,
                              List<LogInfo.LintMessage> textMsg,
                              List<TeamRoleInfo> teamRoleInfos) {

        List<TeamMember> newTeamMembers = ObjectDataExt.of(data).getTeamMembers();
        if (CollectionUtils.empty(newTeamMembers)) {
            return;
        }
        List<TeamMember> oldTeamMembers = ObjectDataExt.of(oldData).getTeamMembers();

        Map<String, Map<String, List<TeamMember>>> newTeamMemberTypeMap = newTeamMembers.stream()
                .collect(Collectors.groupingBy(x -> x.getMemberType().getValue(),
                        Collectors.groupingBy(TeamMember::getEmployee)));

        newTeamMemberTypeMap.forEach((teamMemberType, teamMemberIdMap) -> {
            teamMemberIdMap.forEach((memberId, newTeamMemberInfo) -> {
                Set<TeamMember.Permission> oldPermissionSet = Sets.newLinkedHashSet();
                Set<TeamMember.Permission> permissionSet = Sets.newLinkedHashSet();
                Set<String> oldRoleKeyList = Sets.newLinkedHashSet();
                Set<String> roleKeyList = Sets.newLinkedHashSet();
                // 获取老数据的permissionName和roleName
                getMemberInfo(oldTeamMembers, oldPermissionSet, oldRoleKeyList, memberId, teamMemberType, teamRoleInfos);
                // 获取新数据的permissionName和roleName
                getMemberInfo(newTeamMemberInfo, permissionSet, roleKeyList, memberId, teamMemberType, teamRoleInfos);

                if (CollectionUtils.isEqual(permissionSet, oldPermissionSet) && CollectionUtils.isEqual(roleKeyList, oldRoleKeyList)) {
                    return;
                }

                String maxOldPermissionKey = TeamMember.Permission.getMaxPermission(oldPermissionSet).getLabelKey();
                String maxNewPermissionKey = TeamMember.Permission.getMaxPermission(permissionSet).getLabelKey();

                // 按旧的格式记录
                if (!StringUtils.equals(maxNewPermissionKey, maxOldPermissionKey) || !CollectionUtils.isEqual(roleKeyList, oldRoleKeyList)) {

                    String oldRoleName = oldRoleKeyList.stream().map(I18N::text).collect(Collectors.joining(","));
                    String roleName = roleKeyList.stream().map(I18N::text).collect(Collectors.joining(","));

                    String text = I18N.text(I18NKey.EDIT_TEAM_MEMBER_LOG_2,
                            objectDescribe.getDisplayName(),
                            data.getName(),
                            getMemberName(memberInfos.get(teamMemberType), memberId),
                            I18N.text(maxOldPermissionKey),
                            oldRoleName,
                            I18N.text(maxNewPermissionKey),
                            roleName);


                    Map<String, String> defaultParameterValues = Maps.newHashMap();
                    defaultParameterValues.put(GetI18nKeyUtil.getDescribeDisplayNameKey(objectDescribe.getApiName()), objectDescribe.getDisplayName());
                    defaultParameterValues.put(maxOldPermissionKey, I18N.text(maxOldPermissionKey));
                    defaultParameterValues.put(maxNewPermissionKey, I18N.text(maxNewPermissionKey));
                    roleKeyList.forEach(x -> defaultParameterValues.put(x, I18N.text(x)));
                    oldRoleKeyList.forEach(x -> defaultParameterValues.put(x, I18N.text(x)));

                    Map<String, List<String>> listParameterValues = Maps.newHashMap();
                    String oldRoleCustomKey = getRoleCustomKey(oldRoleKeyList, "", "");
                    String newRoleCustomKey = getRoleCustomKey(roleKeyList, "", ".");
                    listParameterValues.put(oldRoleCustomKey, Lists.newArrayList(oldRoleKeyList));
                    listParameterValues.put(newRoleCustomKey, Lists.newArrayList(roleKeyList));

                    List<String> internationalParameters = Lists.newArrayList();
                    internationalParameters.add(GetI18nKeyUtil.getDescribeDisplayNameKey(objectDescribe.getApiName()));
                    internationalParameters.add(data.getName());
                    internationalParameters.add(getMemberName(memberInfos.get(teamMemberType), memberId));
                    internationalParameters.add(maxOldPermissionKey);
                    internationalParameters.add(oldRoleCustomKey);
                    internationalParameters.add(maxNewPermissionKey);
                    internationalParameters.add(newRoleCustomKey);

                    InternationalItem internationalItem = InternationalItem.builder()
                            .internationalKey(I18NKey.EDIT_TEAM_MEMBER_LOG_2)
                            .defaultInternationalValue(text)
                            .internationalParameters(internationalParameters)
                            .listParameterValues(listParameterValues)
                            .defaultParameterValues(defaultParameterValues)
                            .build();

                    textMsg.add(new LogInfo.LintMessage(text, "", "", internationalItem));
                }

                msgList.add(
                        getTeamMemberMsg(
                                ActionType.ModifySale,
                                memberId,
                                memberId,
                                memberInfos.get(teamMemberType),
                                Lists.newArrayList(oldRoleKeyList),
                                Lists.newArrayList(roleKeyList),
                                maxOldPermissionKey,
                                maxNewPermissionKey,
                                null,
                                teamMemberType
                        ));
            });
        });
        handleTextMsg(data, objectDescribe, textMsg);
    }


    private void getMemberInfo(List<TeamMember> teamMembers,
                               Set<TeamMember.Permission> permissions,
                               Set<String> roleKeyList,
                               String memberId,
                               String teamMemberType,
                               List<TeamRoleInfo> teamRoleInfos) {

        List<TeamMember> teamMemberList = CollectionUtils.nullToEmpty(teamMembers).stream()
                .filter(f -> Objects.equals(f.getEmployee(), memberId) && Objects.equals(teamMemberType, f.getMemberType().getValue()))
                .collect(Collectors.toList());

        if (CollectionUtils.notEmpty(teamMemberList)) {
            for (TeamMember teamMember : teamMemberList) {
                permissions.add(teamMember.getPermission());
                String oldRoleKey = getRoleNameKey(teamRoleInfos, teamMember);
                roleKeyList.add(oldRoleKey);
            }
        }
    }
}
