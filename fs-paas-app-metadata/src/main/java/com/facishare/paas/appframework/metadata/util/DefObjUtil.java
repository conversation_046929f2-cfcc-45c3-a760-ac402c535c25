package com.facishare.paas.appframework.metadata.util;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.facishare.paas.common.util.UdobjConstants.SUPPORT_ACTION_CLASS_NAME_EXT;

/**
 * Created by luohl on 2017/10/30.
 */
@Service
@Slf4j
public class DefObjUtil {


    public void addFieldDescribeCreateTime(IObjectDescribe objectDescribe) {
        if (objectDescribe == null) return;
        //i用来保证每个fieldDescribe的创建时间都不一样,原因是为了排序。虽然感觉不太科学,感觉是不是可以等以后再优化吧...
        int i = 0;
        for (IFieldDescribe fieldDescribe : objectDescribe.getFieldDescribes()) {
            if (fieldDescribe.getCreateTime() == null) {
                fieldDescribe.setCreateTime(System.currentTimeMillis() + (i++));
            }
        }
    }

    public static String getActionClassNameByActionCode(String supportCode) {
        return supportCode + SUPPORT_ACTION_CLASS_NAME_EXT;
    }


}
