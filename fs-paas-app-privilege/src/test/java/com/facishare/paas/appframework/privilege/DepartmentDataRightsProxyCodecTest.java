package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.privilege.dto.DepartmentDataRights;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DepartmentDataRightsProxy.Codec 单元测试
 * 
 * <AUTHOR> Generated
 */
@ExtendWith(MockitoExtension.class)
class DepartmentDataRightsProxyCodecTest {

  private DepartmentDataRightsProxy.Codec codec;

  @BeforeEach
  void setUp() {
    codec = new DepartmentDataRightsProxy.Codec();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试encodeArg方法，正常对象序列化
   */
  @Test
  @DisplayName("正常场景 - 测试encodeArg方法对象序列化")
  void testEncodeArg_Success() {
    // 准备测试数据
    DepartmentDataRights testObj = new DepartmentDataRights();
    testObj.setId("test123");
    testObj.setEntityId("TestEntity");
    testObj.setDeptId("dept001");
    testObj.setStatus(1);
    
    // 执行被测试方法
    byte[] result = codec.encodeArg(testObj);
    
    // 验证结果
    assertNotNull(result);
    assertTrue(result.length > 0);
    
    // 验证序列化的内容包含预期字段
    String jsonString = new String(result);
    assertTrue(jsonString.contains("test123"));
    assertTrue(jsonString.contains("TestEntity"));
    assertTrue(jsonString.contains("dept001"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试encodeArg方法，null对象处理
   */
  @Test
  @DisplayName("边界场景 - 测试encodeArg方法null对象")
  void testEncodeArg_NullObject() {
    // 执行被测试方法
    byte[] result = codec.encodeArg(null);
    
    // 验证结果
    assertNotNull(result);
    assertEquals("null", new String(result));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试decodeResult方法，成功响应解析
   */
  @Test
  @DisplayName("正常场景 - 测试decodeResult方法成功响应")
  void testDecodeResult_Success() {
    // 准备测试数据
    String jsonResponse = "{\n" +
      "  \"errCode\": 0,\n" +
      "  \"errMessage\": \"success\",\n" +
      "  \"result\": {\n" +
      "    \"id\": \"test123\",\n" +
      "    \"entityId\": \"TestEntity\",\n" +
      "    \"deptId\": \"dept001\",\n" +
      "    \"status\": 1\n" +
      "  }\n" +
      "}";
    byte[] responseBytes = jsonResponse.getBytes();
    Map<String, List<String>> headers = Maps.newHashMap();
    
    // 执行被测试方法
    DepartmentDataRights result = codec.decodeResult(200, headers, responseBytes, DepartmentDataRights.class);
    
    // 验证结果
    assertNotNull(result);
    assertEquals("test123", result.getId());
    assertEquals("TestEntity", result.getEntityId());
    assertEquals("dept001", result.getDeptId());
    assertEquals(1, result.getStatus());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试decodeResult方法，List类型响应解析
   */
  @Test
  @DisplayName("正常场景 - 测试decodeResult方法List响应")
  void testDecodeResult_ListResponse() {
    // 准备测试数据
    String jsonResponse = "{\n" +
      "  \"errCode\": 0,\n" +
      "  \"errMessage\": \"success\",\n" +
      "  \"result\": [\n" +
      "    {\n" +
      "      \"id\": \"test123\",\n" +
      "      \"entityId\": \"TestEntity1\",\n" +
      "      \"deptId\": \"dept001\",\n" +
      "      \"status\": 1\n" +
      "    },\n" +
      "    {\n" +
      "      \"id\": \"test456\",\n" +
      "      \"entityId\": \"TestEntity2\",\n" +
      "      \"deptId\": \"dept002\",\n" +
      "      \"status\": 2\n" +
      "    }\n" +
      "  ]\n" +
      "}";
    byte[] responseBytes = jsonResponse.getBytes();
    Map<String, List<String>> headers = Maps.newHashMap();
    
    // 执行被测试方法
    @SuppressWarnings("unchecked")
    List<DepartmentDataRights> result = (List<DepartmentDataRights>) codec.decodeResult(200, headers, responseBytes, List.class);
    
    // 验证结果
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals("test123", result.get(0).getId());
    assertEquals("TestEntity1", result.get(0).getEntityId());
    assertEquals("test456", result.get(1).getId());
    assertEquals("TestEntity2", result.get(1).getEntityId());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试decodeResult方法，void类型响应
   */
  @Test
  @DisplayName("正常场景 - 测试decodeResult方法void响应")
  void testDecodeResult_VoidResponse() {
    // 准备测试数据
    String jsonResponse = "{\n" +
      "  \"errCode\": 0,\n" +
      "  \"errMessage\": \"success\"\n" +
      "}";
    byte[] responseBytes = jsonResponse.getBytes();
    Map<String, List<String>> headers = Maps.newHashMap();
    
    // 执行被测试方法
    Object result = codec.decodeResult(200, headers, responseBytes, void.class);
    
    // 验证结果
    assertNull(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试decodeResult方法，HTTP状态码非200
   */
  @Test
  @DisplayName("异常场景 - 测试decodeResult方法HTTP状态码非200")
  void testDecodeResult_NonSuccessStatusCode() {
    // 准备测试数据
    String errorResponse = "Internal Server Error";
    byte[] responseBytes = errorResponse.getBytes();
    Map<String, List<String>> headers = Maps.newHashMap();
    
    // 执行被测试方法并验证异常
    RuntimeException exception = assertThrows(RuntimeException.class, () -> {
      codec.decodeResult(500, headers, responseBytes, DepartmentDataRights.class);
    });
    
    // 验证异常信息
    assertTrue(exception.getMessage().contains("decode error,body:"));
    assertTrue(exception.getMessage().contains("Internal Server Error"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试decodeResult方法，业务错误码非0
   */
  @Test
  @DisplayName("异常场景 - 测试decodeResult方法业务错误")
  void testDecodeResult_BusinessError() {
    // 准备测试数据
    String jsonResponse = "{\n" +
      "  \"errCode\": 1001,\n" +
      "  \"errMessage\": \"业务处理失败\",\n" +
      "  \"result\": null\n" +
      "}";
    byte[] responseBytes = jsonResponse.getBytes();
    Map<String, List<String>> headers = Maps.newHashMap();
    
    // 执行被测试方法并验证异常
    RestProxyBusinessException exception = assertThrows(RestProxyBusinessException.class, () -> {
      codec.decodeResult(200, headers, responseBytes, DepartmentDataRights.class);
    });
    
    // 验证异常信息
    assertEquals(1001, exception.getCode());
    assertEquals("业务处理失败", exception.getMessage());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试decodeResult方法，无效JSON格式
   */
  @Test
  @DisplayName("异常场景 - 测试decodeResult方法无效JSON")
  void testDecodeResult_InvalidJson() {
    // 准备测试数据
    String invalidJson = "invalid json content";
    byte[] responseBytes = invalidJson.getBytes();
    Map<String, List<String>> headers = Maps.newHashMap();
    
    // 执行被测试方法并验证异常
    RuntimeException exception = assertThrows(RuntimeException.class, () -> {
      codec.decodeResult(200, headers, responseBytes, DepartmentDataRights.class);
    });
    
    // 验证异常信息
    assertTrue(exception.getMessage().contains("decode error"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试decodeResult方法，空响应体
   */
  @Test
  @DisplayName("边界场景 - 测试decodeResult方法空响应体")
  void testDecodeResult_EmptyResponse() {
    // 准备测试数据
    byte[] responseBytes = null;
    Map<String, List<String>> headers = Maps.newHashMap();
    
    // 执行被测试方法并验证异常
    RuntimeException exception = assertThrows(RuntimeException.class, () -> {
      codec.decodeResult(500, headers, responseBytes, DepartmentDataRights.class);
    });
    
    // 验证异常信息
    assertTrue(exception.getMessage().contains("decode error,body:"));
  }
}
