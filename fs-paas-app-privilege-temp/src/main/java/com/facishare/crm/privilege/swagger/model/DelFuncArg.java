/**
 * Facishare PAAS Auth
 * This is PAAS auth service project
 *
 * OpenAPI spec version: v1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


package com.facishare.crm.privilege.swagger.model;

import java.util.Objects;
import com.facishare.crm.privilege.swagger.model.AuthContext;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * DelFuncArg
 */
@javax.annotation.Generated(value = "class io.swagger.codegen.languages.JavaClientCodegen", date = "2017-05-08T14:35:53.743+08:00")
public class DelFuncArg   {
  @SerializedName("authContext")
  private AuthContext authContext = null;

  @SerializedName("funcId")
  private String funcId = null;

  public DelFuncArg authContext(AuthContext authContext) {
    this.authContext = authContext;
    return this;
  }

   /**
   * Get authContext
   * @return authContext
  **/
  @ApiModelProperty(example = "null", value = "")
  public AuthContext getAuthContext() {
    return authContext;
  }

  public void setAuthContext(AuthContext authContext) {
    this.authContext = authContext;
  }

  public DelFuncArg funcId(String funcId) {
    this.funcId = funcId;
    return this;
  }

   /**
   * Get funcId
   * @return funcId
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getFuncId() {
    return funcId;
  }

  public void setFuncId(String funcId) {
    this.funcId = funcId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DelFuncArg delFuncArg = (DelFuncArg) o;
    return Objects.equals(this.authContext, delFuncArg.authContext) &&
        Objects.equals(this.funcId, delFuncArg.funcId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(authContext, funcId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DelFuncArg {\n");
    
    sb.append("    authContext: ").append(toIndentedString(authContext)).append("\n");
    sb.append("    funcId: ").append(toIndentedString(funcId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

