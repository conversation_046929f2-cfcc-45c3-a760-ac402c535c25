package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.metadata.api.IUdefAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description TODO
 * @date 2021/10/20-23:07
 */
@Slf4j
@Component
public class UIEventAction implements ActionExecutor {
    @Override
    public ActionExecutorType getType() {
        return ActionExecutorType.UI_EVENT;
    }

    @Override
    public ButtonExecutor.Result invoke(ButtonExecutor.Arg arg, ActionExecutorContext context) {
        AbstractFuncAction.ActionParameter actionParameter = getActionParameter(context.getAction());
        log.debug("startCustomButton actionParameter:{}", actionParameter);
        return ButtonExecutor.Result.builder()
                .hasReturnValue(true)
                .returnType("UIEvent")
                .returnValue(actionParameter.getUiEventId())
                .build();
    }
    protected AbstractFuncAction.ActionParameter getActionParameter(IUdefAction action) {
        return AbstractFuncAction.ActionParameter.fromJson(action.getActionParamter());
    }
}
