package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.appframework.metadata.dto.ImageInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试ImageFieldDataConverter类的图片字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class ImageFieldDataConverterTest {

  @Mock
  private IObjectDataProxyService dataService;

  @Mock
  private SessionContext sessionContext;

  @Mock
  private ImageInfo mockImage1;

  @Mock
  private ImageInfo mockImage2;

  private ImageFieldDataConverter converter;
  private IFieldDescribe fieldDescribe;
  private IObjectData objectData;

  @BeforeEach
  void setUp() {
    converter = new ImageFieldDataConverter();
    fieldDescribe = createImageFieldDescribe("image_field");
    
    // 设置转换器的属性
    converter.setFieldDescribe(fieldDescribe);
    converter.setDataService(dataService);
    
    // 设置SessionContext的默认行为
    lenient().when(sessionContext.getEId()).thenReturn(123456L);
    
    // 设置mock图片的行为
    lenient().when(mockImage1.getFilePathForXml("123456")).thenReturn("/path/to/image1.jpg");
    lenient().when(mockImage2.getFilePathForXml("123456")).thenReturn("/path/to/image2.png");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法正常转换图片列表
   */
  @Test
  @DisplayName("测试convertFieldData - 正常图片列表转换")
  void testConvertFieldData_NormalImageConversion() {
    // 准备测试数据 - 创建图片数据
    List<Map<String, Object>> imageData = Arrays.asList(
        createImageData("image1.jpg", "/path/to/image1.jpg"),
        createImageData("image2.png", "/path/to/image2.png")
    );
    objectData = createObjectData("image_field", imageData);
    converter.setObjectData(objectData);

    try (MockedStatic<ImageInfo> mockedImageInfo = mockStatic(ImageInfo.class)) {
      mockedImageInfo.when(() -> ImageInfo.convert(any()))
          .thenReturn(Arrays.asList(mockImage1, mockImage2));

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果
      assertEquals("/path/to/image1.jpg|/path/to/image2.png", result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理空列表
   */
  @Test
  @DisplayName("测试convertFieldData - 处理空列表")
  void testConvertFieldData_HandlesEmptyList() {
    // 准备测试数据
    objectData = createObjectData("image_field", Collections.emptyList());
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理null值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null值")
  void testConvertFieldData_HandlesNullValue() {
    // 准备测试数据
    objectData = createObjectData("image_field", null);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理字符串类型值（兼容老对象）
   */
  @Test
  @DisplayName("测试convertFieldData - 处理字符串类型值")
  void testConvertFieldData_HandlesStringValue() {
    // 准备测试数据 - 兼容老对象部分图片字段数据结构为字符串
    String imageString = "/path/to/old_image.jpg";
    objectData = createObjectData("image_field", imageString);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 字符串类型应该直接返回
    assertEquals(imageString, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理其他非List类型值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理其他非List类型值")
  void testConvertFieldData_HandlesOtherNonListValue() {
    // 准备测试数据 - 非String和非List的其他类型
    objectData = createObjectData("image_field", 12345);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理null SessionContext
   */
  @Test
  @DisplayName("测试convertFieldData - null SessionContext")
  void testConvertFieldData_NullSessionContext() {
    // 准备测试数据
    List<Map<String, Object>> imageData = Arrays.asList(
        createImageData("image1.jpg", "/path/to/image1.jpg")
    );
    objectData = createObjectData("image_field", imageData);
    converter.setObjectData(objectData);

    // 设置mock图片在tenantId为null时的行为
    when(mockImage1.getFilePathForXml(null)).thenReturn("/path/to/image1.jpg");

    try (MockedStatic<ImageInfo> mockedImageInfo = mockStatic(ImageInfo.class)) {
      mockedImageInfo.when(() -> ImageInfo.convert(any()))
          .thenReturn(Arrays.asList(mockImage1));

      // 执行被测试方法 - 传入null SessionContext
      String result = converter.convertFieldData(null);

      // 验证结果
      assertEquals("/path/to/image1.jpg", result);
      verify(mockImage1).getFilePathForXml(null);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理单个图片
   */
  @Test
  @DisplayName("测试convertFieldData - 单个图片")
  void testConvertFieldData_SingleImage() {
    // 准备测试数据
    List<Map<String, Object>> imageData = Arrays.asList(
        createImageData("single.jpg", "/path/to/single.jpg")
    );
    objectData = createObjectData("image_field", imageData);
    converter.setObjectData(objectData);

    try (MockedStatic<ImageInfo> mockedImageInfo = mockStatic(ImageInfo.class)) {
      mockedImageInfo.when(() -> ImageInfo.convert(any()))
          .thenReturn(Arrays.asList(mockImage1));

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果
      assertEquals("/path/to/image1.jpg", result);
    }
  }

  /**
   * 提供不同类型的图片数据测试
   */
  private static Stream<Arguments> provideImageData() {
    return Stream.of(
        Arguments.of(Collections.emptyList(), ""),
        Arguments.of(null, ""),
        Arguments.of("old_image_string.jpg", "old_image_string.jpg"),
        Arguments.of(12345, ""),
        Arguments.of(true, "")
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理不同类型的图片数据
   */
  @ParameterizedTest
  @MethodSource("provideImageData")
  @DisplayName("测试convertFieldData - 不同类型的图片数据")
  void testConvertFieldData_DifferentImageData(Object inputValue, String expectedResult) {
    // 准备测试数据
    objectData = createObjectData("image_field", inputValue);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals(expectedResult, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理字段不存在的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 字段不存在")
  void testConvertFieldData_FieldNotExists() {
    // 准备测试数据 - 创建不包含目标字段的数据
    objectData = createObjectData("other_field", Arrays.asList());
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 字段不存在时应该返回空字符串
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理ImageInfo.convert异常
   */
  @Test
  @DisplayName("测试convertFieldData - ImageInfo.convert异常")
  void testConvertFieldData_ImageInfoConvertException() {
    // 准备测试数据
    List<Map<String, Object>> imageData = Arrays.asList(
        createImageData("image1.jpg", "/path/to/image1.jpg")
    );
    objectData = createObjectData("image_field", imageData);
    converter.setObjectData(objectData);

    try (MockedStatic<ImageInfo> mockedImageInfo = mockStatic(ImageInfo.class)) {
      mockedImageInfo.when(() -> ImageInfo.convert(any()))
          .thenThrow(new RuntimeException("ImageInfo convert error"));

      // 执行被测试方法 - 应该抛出异常
      assertThrows(RuntimeException.class, () -> {
        converter.convertFieldData(sessionContext);
      });
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试转换器的基本属性设置
   */
  @Test
  @DisplayName("测试转换器属性设置")
  void testConverterProperties() {
    // 验证属性设置
    assertEquals(fieldDescribe, converter.getFieldDescribe());
    assertEquals(dataService, converter.getDataService());
  }

  /**
   * 创建测试用的图片字段描述
   */
  private IFieldDescribe createImageFieldDescribe(String apiName) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试图片字段");
    fieldMap.put("type", IFieldType.IMAGE);
    return FieldDescribeFactory.newInstance(fieldMap);
  }

  /**
   * 创建测试用的对象数据
   */
  private IObjectData createObjectData(String fieldName, Object value) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldName, value);
    return new ObjectData(dataMap);
  }

  /**
   * 创建测试用的图片数据
   */
  private Map<String, Object> createImageData(String fileName, String filePath) {
    Map<String, Object> imageData = new HashMap<>();
    imageData.put("fileName", fileName);
    imageData.put("filePath", filePath);
    return imageData;
  }
}
