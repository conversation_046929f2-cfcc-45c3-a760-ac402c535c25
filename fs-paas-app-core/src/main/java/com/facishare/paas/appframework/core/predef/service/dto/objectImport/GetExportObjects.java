package com.facishare.paas.appframework.core.predef.service.dto.objectImport;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface GetExportObjects {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {

        @JsonProperty("describe_api_name")
        @JSONField(name = "describe_api_name")
        String describeApiName;
    }

    @Data
    @Builder
    class Result {
        List<ObjectDescribeDocument> describes;
        boolean supportFileAttachment;
        List<ExportDetailObject> exportDetailObjects;
        boolean supportDetailFileAttachment;
    }

    @Data
    @Builder
    class ExportDetailObject {
        String detailApiName;
        List<Map<String, Object>> fieldDescribes;
    }
}
