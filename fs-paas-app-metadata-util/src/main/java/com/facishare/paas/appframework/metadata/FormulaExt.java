package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.describe.Formula;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.experimental.Delegate;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * Created by liwei on 2018/10/9
 */
public class FormulaExt {

    @Getter
    @Delegate
    private Formula formula;

    private FormulaExt(Formula formula) {
        this.formula = formula;
    }

    public static FormulaExt of(Formula formula) {
        return new FormulaExt(formula);
    }

    public boolean supportFilter() {
        return formula.isIndex() && StringUtils.isBlank(formula.getFilterExpression());
    }

    public boolean isChanged(Formula formula) {
        return !this.equals(FormulaExt.of(formula));
    }

    public boolean isReturnTypeChanged(Formula formula) {
        FormulaExt formulaExt = FormulaExt.of(formula);
        if (!Objects.equals(getReturnType(), formulaExt.getReturnType())) {
            return true;
        }
        return false;
    }

    public boolean isExpressionChanged(Formula formula) {
        return !Objects.equals(getExpression(), formula.getExpression());
    }

    public boolean usingLastModifiedTime() {
        String expression = getExpression();
        return StringUtils.contains(expression, "$" + DBRecord.LAST_MODIFIED_TIME + "$")
                || StringUtils.contains(expression, "." + DBRecord.LAST_MODIFIED_TIME + "$");
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FormulaExt formulaExt = (FormulaExt) o;
        if (formula == formulaExt.formula) {
            return true;
        }

        if (!Objects.equals(getApiName(), formulaExt.getApiName())) {
            return false;
        }
        if (!Objects.equals(getExpression(), formulaExt.getExpression())) {
            return false;
        }
        if (!Objects.equals(getDecimalPlaces(), formulaExt.getDecimalPlaces())) {
            return false;
        }
        if (!Objects.equals(BooleanUtils.isNotTrue(getDefaultToZero()), BooleanUtils.isNotTrue(formulaExt.getDefaultToZero()))) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        List<Object> values = Lists.newArrayList();
        values.add(getApiName());
        values.add(getExpression());
        values.add(getDecimalPlaces());

        return Objects.hashCode(values.toArray());
    }

}
