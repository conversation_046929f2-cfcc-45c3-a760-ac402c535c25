package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.List;

/**
 * Created by zhaopx on 2018/5/5.
 */
public abstract class BaseInsertImportTemplateAction<A extends BaseImportTemplateAction.Arg> extends BaseImportTemplateAction<A> {
    @Override
    protected List<IFieldDescribe> getImportTemplateField(BaseImportTemplateAction.Arg arg) {
        return infraServiceFacade.getTemplateField(actionContext.getUser(), objectDescribe, arg.getRecordType());
    }
}
