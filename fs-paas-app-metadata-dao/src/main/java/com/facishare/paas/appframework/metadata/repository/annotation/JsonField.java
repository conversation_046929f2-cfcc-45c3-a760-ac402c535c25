package com.facishare.paas.appframework.metadata.repository.annotation;

import com.facishare.paas.appframework.metadata.repository.fieldhandler.FieldHandlerFactory;
import com.facishare.paas.appframework.metadata.repository.fieldhandler.ObjectFieldType;
import com.facishare.paas.metadata.api.describe.IFieldType;

import java.lang.annotation.*;

/**
 * create by z<PERSON><PERSON> on 2021/12/24
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@FieldType(type = FieldHandlerFactory.JSON_FIELD)
public @interface JsonField {

    ObjectField field();

    ObjectFieldType fieldType() default ObjectFieldType.JSON;
}
