package com.facishare.paas.appframework.metadata.config.handler;

import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.metadata.config.util.TenantConfigUtil;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class SalesOrderObjConfigHandler implements ConfigHandler {
    @Autowired
    ConfigService configService;
    @Override
    public String getObjectAPIName() {
        return "SalesOrderObj";
    }

    @Override
    public void handle(String tenantId, Map<String, Object> objectConfig, Map<String, Map<String, Object>> fieldConfig) {
        boolean isCustomerAccountEnable = TenantConfigUtil.isCustomerAccountEnabled(configService,tenantId);
        if(isCustomerAccountEnable){
            if(fieldConfig.containsKey("settle_type")){
                fieldConfig.get("settle_type").put("display",1);
            }
        }
        boolean isDeliveryNoteEnabled = TenantConfigUtil.isDeliveryNoteEnabled(configService,tenantId);
        if(isDeliveryNoteEnabled){
            if(fieldConfig.containsKey("delivered_amount_sum")){
                Map<String,Object> attrsConfig = Maps.newHashMap();
                attrsConfig.put("is_required",0);
                attrsConfig.put("default_value",1);
                attrsConfig.put("label",1);
                attrsConfig.put("help_text",1);
                attrsConfig.put("length",1);
                attrsConfig.put("decimal_places",1);
                attrsConfig.put("max_length",1);
                fieldConfig.get("delivered_amount_sum").put("display",1);
                fieldConfig.get("delivered_amount_sum").put("attrs",attrsConfig);
            }
        }
        boolean isNewOppOpen = TenantConfigUtil.isOpenNewOpportunity(configService, tenantId);
        if(isNewOppOpen){
            if(fieldConfig.containsKey("new_opportunity_id")){
                fieldConfig.get("new_opportunity_id").put("display",1);
            }
        }
        boolean isDingHuoTongEnabled = TenantConfigUtil.isDingHuoTongEnabled(configService,tenantId);
        if(isDingHuoTongEnabled){
            if(fieldConfig.containsKey("opportunity_id")){
                Map<String,Object> attrsConfig = Maps.newHashMap();
                attrsConfig.put("target_related_list_label",1);
                attrsConfig.put("wheres",1);
                attrsConfig.put("is_required",0);
                attrsConfig.put("default_value",0);
                attrsConfig.put("label",1);
                attrsConfig.put("help_text",1);
                fieldConfig.get("opportunity_id").put("display",1);
                fieldConfig.get("opportunity_id").put("attrs",attrsConfig);
            }
        }
        boolean isInventoryEnabled = TenantConfigUtil.isInventoryEnabled(configService,tenantId);
        if(isInventoryEnabled){
            if(fieldConfig.containsKey("shipping_warehouse_id")){
                fieldConfig.get("shipping_warehouse_id").put("display",1);
            }
        }
        boolean isPromotionEnabled = TenantConfigUtil.isPromotionEnabled(configService,tenantId);
        if(isPromotionEnabled){
            if(fieldConfig.containsKey("promotion_id")){
                Map<String,Object> attrsConfig = Maps.newHashMap();
                attrsConfig.put("label",1);
                attrsConfig.put("target_related_list_label",1);
                attrsConfig.put("is_required",0);
                attrsConfig.put("default_value",0);
                attrsConfig.put("is_unique",0);
                attrsConfig.put("wheres",1);
                fieldConfig.get("promotion_id").put("display",1);
                fieldConfig.get("promotion_id").put("attrs",attrsConfig);
            }
        }

        boolean isPricePolicyEnabled = TenantConfigUtil.isPricePolicyEnabled(configService,tenantId);
        boolean isOpenCoupon = TenantConfigUtil.isOpenCoupon(configService,tenantId);
        boolean isOpenRebate = TenantConfigUtil.isOpenRebate(configService,tenantId);
        //开启价格政策后不允许修改默认值
        if (isPricePolicyEnabled || isOpenCoupon || isOpenRebate) {
            if (fieldConfig.containsKey("discount")) {
                Map<String, Object> attrsConfig = Maps.newHashMap();
                attrsConfig.put("is_required", 1);
                attrsConfig.put("default_value", 0);
                attrsConfig.put("label", 1);
                attrsConfig.put("help_text", 1);
                attrsConfig.put("max_length", 1);
                attrsConfig.put("decimal_places", 1);
                attrsConfig.put("length", 1);
                fieldConfig.get("discount").put("display", 1);
                fieldConfig.get("discount").put("enable", 1);
                fieldConfig.get("discount").put("edit", 1);
                fieldConfig.get("discount").put("attrs", attrsConfig);
            }

            if (fieldConfig.containsKey("order_amount")) {
                Map<String, Object> attrsConfig = Maps.newHashMap();
                attrsConfig.put("is_required", 1);
                attrsConfig.put("default_value", 0);
                attrsConfig.put("label", 1);
                attrsConfig.put("help_text", 1);
                attrsConfig.put("max_length", 1);
                attrsConfig.put("decimal_places", 1);
                attrsConfig.put("length", 1);
                fieldConfig.get("order_amount").put("display", 1);
                fieldConfig.get("order_amount").put("enable", 1);
                fieldConfig.get("order_amount").put("edit", 1);
                fieldConfig.get("order_amount").put("attrs", attrsConfig);
            }
        }
        //开启返利或者优惠券不可修改计算公式
        if (isOpenCoupon || isOpenRebate) {
            if (fieldConfig.containsKey("receivable_amount")) {
                Map<String, Object> attrsConfig = Maps.newHashMap();
                attrsConfig.put("is_required", 1);
                attrsConfig.put("formula", 0);
                attrsConfig.put("label", 1);
                attrsConfig.put("help_text", 1);
                attrsConfig.put("max_length", 1);
                attrsConfig.put("decimal_places", 1);
                attrsConfig.put("length", 1);
                fieldConfig.get("receivable_amount").put("display", 1);
                fieldConfig.get("receivable_amount").put("enable", 0);
                fieldConfig.get("receivable_amount").put("edit", 1);
                fieldConfig.get("receivable_amount").put("attrs", attrsConfig);
            }
        }
    }
}
