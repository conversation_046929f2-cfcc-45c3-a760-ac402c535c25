package com.facishare.paas.appframework.metadata.duplicated;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.common.service.DepartmentService;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.common.util.ScriptConfig;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.DuplicatedSearchExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.cache.RedisDao;
import com.facishare.paas.appframework.metadata.dto.BatchMultiQueryDuplicateSearch;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchDataInfo;
import com.facishare.paas.appframework.metadata.dto.LocationInfo;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2022/8/9
 */
@Slf4j
@Service
public class DuplicatedSearchDataRedisStoreService implements DuplicatedSearchDataStoreService {
    @Autowired
    private RedisDao redisDao;

    @Autowired
    private DepartmentService departmentService;
    /**
     * keys 是所有的key
     * arg1 是过期时间
     * arg2 是json string {"key1":["id1","id2"],"key2":["id1","id3"]}
     * <p>
     * 使用 sadd 将id保存到对饮的 key下面
     */
    public static final String SAVE_LUA_SCRIPT = "local expireTimes = tonumber(ARGV[1])" +
            "   local value = ARGV[2]" +
            "   local map = cjson.decode(value)" +
            "   for i, key in ipairs(KEYS) do" +
            "       redis.call(\"sadd\", key, unpack(map[key]))" +
            "       redis.call(\"expire\", key, expireTimes)" +
            "   end" +
            "   return 1";
    /**
     * keys 是所有key [["key1","key2"],["key2","key3"]]
     * arg1 是用于存放临时变量的key
     * <p>
     * 使用 sinterstore 和 sunion 命令 得到 key中存放的 ids 的交集
     */
    public static final String SEARCH_LUA_SCRIPT = "local arg = ARGV[1]" +
            "    local temp_keys = {}" +
            "    for i, value in ipairs(KEYS) do" +
            "       local body = cjson.decode(value)" +
            "       local temp_key = arg..\"_\"..tostring(i)" +
            "       redis.call(\"sinterstore\", temp_key, unpack(body))" +
            "       table.insert(temp_keys, temp_key)" +
            "    end" +
            "    local result = redis.call(\"sunion\", unpack(temp_keys))" +
            "    redis.call(\"del\", unpack(temp_keys))" +
            "    return result";

    /**
     * 批量删除key
     */
    public static final String DEL_LUA_SCRIPT = "redis.call(\"del\", unpack(KEYS))" +
            "    return 1";

    @Override
    public Optional<DuplicateSearchDataInfo> searchDuplicatedData(IObjectDescribe objectDescribe, IDuplicatedSearch duplicatedSearch, IObjectData objectData) {
        DuplicatedSearchKeyContainer duplicatedSearchKeyContainer = DuplicatedSearchKeyContainer.of(duplicatedSearch, objectData);
        return Optional.ofNullable(searchDuplicatedData(objectDescribe, duplicatedSearchKeyContainer, null));
    }

    private DuplicateSearchDataInfo searchDuplicatedData(IObjectDescribe objectDescribe, DuplicatedSearchKeyContainer keyContainer, String taskId) {
        Set<String> searchKeys = keyContainer.getSearchKeys(taskId);
        if (CollectionUtils.empty(searchKeys)) {
            return null;
        }
        String redisTempKey = getRedisTempKey(keyContainer, taskId, objectDescribe.getTenantId());
        Collection<String> dataIds = redisDao.evalScript(SEARCH_LUA_SCRIPT, Lists.newArrayList(searchKeys), redisTempKey);

        DuplicateSearchDataInfo result = DuplicateSearchDataInfo.of(objectDescribe.getApiName(),
                keyContainer.getDuplicatedSearch().getRuleApiName(), keyContainer.getSourceDataId(), dataIds);

        if (result.isDuplicated()) {
            log.warn("searchDuplicatedData in redis! ei:{},taskId:{}, searchKey:{},redisTempKey:{} result:{}", objectDescribe.getTenantId(), taskId, searchKeys, redisTempKey, JacksonUtils.toJson(result));
        }
        return result;
    }

    private DuplicateSearchDataInfo searchDuplicatedDataV2(IObjectDescribe objectDescribe, DuplicatedSearchKeyContainer keyContainer, String taskId) {
        List<String> searchKeys = keyContainer.getSearchKeysV2(taskId);
        if (CollectionUtils.empty(searchKeys)) {
            return null;
        }
        String redisTempKey = getRedisTempKey(keyContainer, taskId, objectDescribe.getTenantId());
        long start = System.currentTimeMillis();
        Collection<String> dataIds = redisDao.evalScript(ScriptConfig.getSearchLuaScriptV2(), searchKeys, redisTempKey);
        log.info("SEARCH_LUA_SCRIPT_V2 cost:{}", System.currentTimeMillis() - start);
        DuplicateSearchDataInfo result = DuplicateSearchDataInfo.of(objectDescribe.getApiName(),
                keyContainer.getDuplicatedSearch().getRuleApiName(), keyContainer.getSourceDataId(), dataIds);

        if (result.isDuplicated()) {
            log.warn("searchDuplicatedDataV2 in redis! ei:{},taskId:{}, searchKey:{},redisTempKey:{} result:{}", objectDescribe.getTenantId(), taskId, searchKeys, redisTempKey, JacksonUtils.toJson(result));
        }
        return result;
    }


    private String getRedisTempKey(DuplicatedSearchKeyContainer keyContainer, String taskId, String tenantId) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DUPLICATED_SEARCH_REDIS_TEMP_KEY_PRE_GRAY, tenantId) && Strings.isNullOrEmpty(taskId)) {
            return keyContainer.getRedisTempKey(IdGenerator.get());
        }
        return keyContainer.getRedisTempKey(taskId);
    }

    @Override
    public List<DuplicateSearchDataInfo> searchDuplicatedData(IObjectDescribe objectDescribe, IDuplicatedSearch duplicatedSearch, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return Collections.emptyList();
        }
        return dataList.stream()
                .map(data -> searchDuplicatedData(objectDescribe, duplicatedSearch, data))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
    }

    @Override
    public List<DuplicateSearchDataInfo> searchDuplicatedData(IObjectDescribe objectDescribe,
                                                              List<IObjectData> dataList,
                                                              List<BatchMultiQueryDuplicateSearch.DuplicateData> duplicateDataList,
                                                              Map<String, IDuplicatedSearch> duplicatedSearchMap,
                                                              List<IObjectData> redisObjectData) {
        if (CollectionUtils.empty(dataList) || CollectionUtils.empty(duplicateDataList)) {
            return Collections.emptyList();
        }

        List<DuplicateSearchDataInfo> duplicateSearchDataInfoList = Lists.newArrayList();

        Map<String, BatchMultiQueryDuplicateSearch.DuplicateData> dataApiNameAndRuleInfo = duplicateDataList.stream()
                .collect(Collectors.toMap(x -> ObjectDataExt.of(x.getData()).getId(), it -> it, (x, y) -> x));
        for (IObjectData iObjectData : dataList) {
            BatchMultiQueryDuplicateSearch.DuplicateData duplicateData = dataApiNameAndRuleInfo.get(iObjectData.getId());
            if (Objects.isNull(duplicateData)) {
                continue;
            }
            List<String> ruleApiNameList = duplicateData.getRuleApiNameList();
            List<IDuplicatedSearch> searchList = ruleApiNameList.stream().map(duplicatedSearchMap::get).collect(Collectors.toList());
            for (IDuplicatedSearch duplicatedSearch : searchList) {
                //查重
                Optional<DuplicateSearchDataInfo> duplicateSearchDataInfo = searchDuplicatedData(objectDescribe, duplicatedSearch, iObjectData);
                if (duplicateSearchDataInfo.isPresent() && !duplicateSearchDataInfo.get().isEmpty()) {
                    DuplicateSearchDataInfo searchDataInfo = duplicateSearchDataInfo.get();
                    searchDataInfo.setRuleApiName(duplicatedSearch.getRuleApiName());
                    filterDuplicatedDataByDataScope(redisObjectData, objectDescribe, DuplicatedSearchExt.of(duplicatedSearch), Lists.newArrayList(searchDataInfo));
                    duplicateSearchDataInfoList.add(searchDataInfo);
                }
            }
        }
        return duplicateSearchDataInfoList;
    }

    /**
     * 多数据,多规则查重,每条数据都要针对每个符合生效条件的规则做查重
     *
     * @param user
     * @param objectDescribe
     * @param duplicatedSearchList
     * @param objectDataList
     * @param useMultiRule
     * @return
     */
    @Override
    public List<DuplicateSearchDataInfo> multiSaveAndDuplicateData(User user, IObjectDescribe objectDescribe,
                                                                   List<IDuplicatedSearch> duplicatedSearchList,
                                                                   List<IObjectData> objectDataList, boolean useMultiRule) {
        List<DuplicateSearchDataInfo> duplicateSearchDataInfos = Lists.newArrayList();
        Set<String> keys = Sets.newHashSet();
        //将所有数据存redis
        try {

            String taskId = IdGenerator.get();
            List<DuplicatedSearchKeyContainer> keyContainers = Lists.newArrayList();
            for (IDuplicatedSearch duplicatedSearch : duplicatedSearchList) {
                keyContainers.addAll(objectDataList.stream()
                        .map(objectData -> DuplicatedSearchKeyContainer.of(duplicatedSearch, objectData))
                        .collect(Collectors.toList()));
            }
            //将数据存入到redis中
            if (AppFrameworkConfig.isGrayDuplicatedSupportDistance(user.getTenantId())) {
                keys.addAll(saveDuplicateDataV2(keyContainers, 5, taskId));
            } else {
                keys.addAll(saveDuplicateData(keyContainers, 5, taskId));
            }

            List<DuplicatedSearchExt> duplicatedSearchExtList = duplicatedSearchList.stream().map(DuplicatedSearchExt::of).collect(Collectors.toList());
            //给数据中补充父级部门信息
            DuplicatedSearchExt.fillParentDeptToObjectData(objectDataList, objectDescribe, departmentService, duplicatedSearchExtList);

            Set<String> ids = Sets.newHashSet();
            //查询重复数据
            for (DuplicatedSearchExt duplicatedSearchExt : duplicatedSearchExtList) {
                //根据where条件过滤出符合的key查重
                List<String> idList = objectDataList.stream()
                        .filter(it -> !ids.contains(it.getId()))
                        .filter(x -> duplicatedSearchExt.filterDuplicateSearchByWheres(x, objectDescribe))
                        .map(IObjectData::getId)
                        .collect(Collectors.toList());
                //开启单规则,则符合本查重规则的数据,只查一次重
                if (!useMultiRule) {
                    ids.addAll(idList);
                }
                //如果当前的查重规则是模糊匹配的话,则本批数据不查重,直接入库
                if (duplicatedSearchExt.machFuzzyRule()) {
                    continue;
                }
                List<DuplicatedSearchKeyContainer> filterContainer = keyContainers.stream()
                        //过滤掉不是该规则的key
                        .filter(x -> StringUtils.equals(x.getDuplicatedSearch().getRuleApiName(), duplicatedSearchExt.getRuleApiName()))
                        //过滤根据where条件查出的数据
                        .filter(x -> idList.contains(x.getSourceDataId()))
                        .collect(Collectors.toList());
                List<DuplicateSearchDataInfo> duplchatedInfoList = searchDuplicatedData(objectDescribe, filterContainer, taskId);
                filterDuplicatedDataByDataScope(objectDataList, objectDescribe, duplicatedSearchExt, duplchatedInfoList);
                duplicateSearchDataInfos.addAll(duplchatedInfoList);
            }
            log.info("import redisDup result! duplicateSearchDataInfos:{}", duplicateSearchDataInfos);
            return duplicateSearchDataInfos;
        } finally {
            ObjectDataExt.removeParentDeptSymbol(objectDataList);
            ObjectDataExt.removeGeoField(objectDataList,duplicatedSearchList);
            if (CollectionUtils.notEmpty(keys)) {
                del(keys);
            }
        }
    }

    private void filterDuplicatedDataByDataScope(List<IObjectData> objectDataList, IObjectDescribe objectDescribe, DuplicatedSearchExt duplicatedSearchExt, List<DuplicateSearchDataInfo> duplchatedInfoList) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DUPLICATE_RULE_SUPPORT_DATA_SCOPE, objectDescribe.getTenantId())) {
            return;
        }
        Map<String, IObjectData> objectDataMap = objectDataList.stream().collect(Collectors.toMap(DBRecord::getId, it -> it, (x, y) -> y));
        //根据数据范围过滤查重数据
        for (DuplicateSearchDataInfo duplicateSearchDataInfo : duplchatedInfoList) {
            Set<String> filterData = duplicateSearchDataInfo.getDataIds().stream()
                    .filter(x -> duplicatedSearchExt.filterDuplicateSearchByDataScope(objectDataMap.get(x), objectDescribe))
                    .collect(Collectors.toSet());
            duplicateSearchDataInfo.setDataIds(Sets.newLinkedHashSet(filterData));
        }
    }

    private List<DuplicateSearchDataInfo> searchDuplicatedData(IObjectDescribe objectDescribe, List<DuplicatedSearchKeyContainer> keyContainers, String taskId) {
        if (CollectionUtils.empty(keyContainers)) {
            return Collections.emptyList();
        }
        if (AppFrameworkConfig.isGrayDuplicatedSupportDistance(objectDescribe.getTenantId())) {
            return keyContainers.stream()
                    .map(keyContainer -> searchDuplicatedDataV2(objectDescribe, keyContainer, taskId))
                    .filter(Objects::nonNull)
                    .filter(DuplicateSearchDataInfo::isDuplicated)
                    .collect(Collectors.toList());
        }
        return keyContainers.stream()
                .map(keyContainer -> searchDuplicatedData(objectDescribe, keyContainer, taskId))
                .filter(Objects::nonNull)
                .filter(DuplicateSearchDataInfo::isDuplicated)
                .collect(Collectors.toList());
    }

    @Override
    public void saveDuplicateData(User user, IObjectDescribe describe, IDuplicatedSearch duplicatedSearch, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        List<DuplicatedSearchKeyContainer> keyContainers = dataList.stream()
                .map(objectData -> DuplicatedSearchKeyContainer.of(duplicatedSearch, objectData))
                .collect(Collectors.toList());
        saveDuplicateData(keyContainers, AppFrameworkConfig.getDuplicatedSearchRedisExpireTimes(), null);
    }

    private Set<String> saveDuplicateData(List<DuplicatedSearchKeyContainer> keyContainers, int expireTimes, String taskId) {
        if (CollectionUtils.empty(keyContainers)) {
            return Collections.emptySet();
        }
        Map<String, Set<String>> resultMap = Maps.newHashMap();
        for (DuplicatedSearchKeyContainer keyContainer : keyContainers) {
            Set<String> saveKeys = keyContainer.getSaveKeys(taskId);
            if (CollectionUtils.empty(saveKeys)) {
                continue;
            }
            String dataId = keyContainer.getSourceDataId();
            for (String saveKey : saveKeys) {
                resultMap.computeIfAbsent(saveKey, k -> Sets.newHashSet()).add(dataId);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("searchDuplicateSearch save info ,taskId:{},keys:{}", taskId, JSON.toJSONString(resultMap.keySet()));
        }
        redisDao.evalScript(SAVE_LUA_SCRIPT, Lists.newArrayList(resultMap.keySet()), String.valueOf(expireTimes), JacksonUtils.toJson(resultMap));
        return ImmutableSet.copyOf(resultMap.keySet());
    }

    private Set<String> saveDuplicateDataV2(List<DuplicatedSearchKeyContainer> keyContainers, int expireTimes, String taskId) {
        if (CollectionUtils.empty(keyContainers)) {
            return Collections.emptySet();
        }
        JSONObject resultMap = new JSONObject();
        Set<String> keys = Sets.newHashSet();
        Map<String, Set<String>> dataDupRes = Maps.newHashMap();
        Map<String, Map<String, List<BigDecimal>>> geoDupRes = Maps.newHashMap();
        for (DuplicatedSearchKeyContainer keyContainer : keyContainers) {
            List<DuplicatedSearchKeyDto> saveKeyDto = keyContainer.getSaveKeysV2(taskId, keyContainer.getSourceDataId());
            if (CollectionUtils.empty(saveKeyDto)) {
                continue;
            }
            for (DuplicatedSearchKeyDto duplicatedSearchKeyDto : saveKeyDto) {
                //处理普通key
                Map<String, String> dataDup = duplicatedSearchKeyDto.getDataDup();
                dataDup.forEach((key, dataId) -> {
                            dataDupRes.computeIfAbsent(key, k -> Sets.newHashSet()).add(dataId);
                            keys.add(key);
                        }
                );
                //处理定位类型的key
                Map<String, LocationInfo> geoDup = duplicatedSearchKeyDto.getGeoDup();
                geoDup.forEach((key, value) -> {
                    geoDupRes.computeIfAbsent(key, k -> Maps.newHashMap()).put(value.getDataId(), Lists.newArrayList(value.getLongitude(), value.getLatitude()));
                    keys.add(key);
                });
            }
        }
        resultMap.put("dataDup", dataDupRes);
        resultMap.put("geoDup", geoDupRes);
        if (log.isDebugEnabled()) {
            log.debug("searchDuplicateSearchV2 save info ,taskId:{},keys:{}", taskId, JSON.toJSONString(resultMap));
        }
        long start = System.currentTimeMillis();
        redisDao.evalScript(ScriptConfig.getSaveLuaScriptV2(), Lists.newArrayList(keys), String.valueOf(expireTimes), JacksonUtils.toJson(resultMap));
        log.info("SAVE_LUA_SCRIPT_V2 cost:{}", System.currentTimeMillis() - start);
        return ImmutableSet.copyOf(keys);
    }

    @Override
    public List<DuplicateSearchDataInfo> searchDuplicatedDataInDataList(IObjectDescribe objectDescribe, IDuplicatedSearch duplicatedSearch, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return Collections.emptyList();
        }
        String taskId = IdGenerator.get();
        Set<String> keys = null;
        List<DuplicateSearchDataInfo> result;
        try {
            List<DuplicatedSearchKeyContainer> keyContainers = dataList.stream()
                    .map(objectData -> DuplicatedSearchKeyContainer.of(duplicatedSearch, objectData))
                    .collect(Collectors.toList());
            keys = saveDuplicateData(keyContainers, 5, taskId);
            result = searchDuplicatedData(objectDescribe, keyContainers, taskId);
        } finally {
            if (CollectionUtils.notEmpty(keys)) {
                del(keys);
            }
        }
        return result;
    }

    private void del(Collection<String> keys) {
        redisDao.evalScript(DEL_LUA_SCRIPT, Lists.newArrayList(keys));
    }

}
