package com.facishare.paas.appframework.metadata.exception;

import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ErrorCode;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/02/28
 */
public class PrivilegeBusinessException extends AppBusinessException {
    public PrivilegeBusinessException(String message, int errorCode) {
        super(message, errorCode);
    }

    public PrivilegeBusinessException(String message, ErrorCode errorCode) {
        super(message, errorCode);
    }

    public PrivilegeBusinessException(String message, Throwable cause, int errorCode) {
        super(message, cause, () -> errorCode);
    }

}
