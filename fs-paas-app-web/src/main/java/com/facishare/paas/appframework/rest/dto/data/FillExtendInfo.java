package com.facishare.paas.appframework.rest.dto.data;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by zhouwr on 2024/9/4.
 */
public interface FillExtendInfo {
    @Data
    class Arg {
        private String describeApiName;
        private List<ObjectDataDocument> dataList;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Result {
        private List<ObjectDataDocument> dataList;
    }
}
