package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.UIEventDocument;
import com.facishare.paas.appframework.core.predef.service.dto.uievent.CreateUIEvent;
import com.facishare.paas.appframework.core.predef.service.dto.uievent.DeleteById;
import com.facishare.paas.appframework.core.predef.service.dto.uievent.FindByLayout;
import com.facishare.paas.appframework.core.predef.service.dto.uievent.FindByObject;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.UIEventLogicService;
import com.facishare.paas.metadata.api.IUIEvent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
@ServiceModule("ui_event")
public class ObjectUIEventService {
    @Autowired
    private LayoutLogicService layoutLogicService;
    @Autowired
    private UIEventLogicService eventLogicService;

    @ServiceMethod("create")
    public CreateUIEvent.Result createUIEvent(CreateUIEvent.Arg arg, ServiceContext context) {
        ILayout layout = layoutLogicService.findLayoutByApiName(context.getUser(), arg.getLayoutApiName(), arg.getDescribeApiName());
        List<Map<String, Object>> events = CollectionUtils.nullToEmpty(layout.getEvents());
        events.addAll(arg.getEvents());
        layout.setEvents(events);

        layout.set("is_deal_ui", true);
        ILayout updatedLayout = layoutLogicService.updateLayout(context.getUser(), layout);
        List<UIEventDocument> eventDocumentList = CollectionUtils.nullToEmpty(updatedLayout.getEvents())
                .stream()
                .map(UIEventDocument::of)
                .collect(Collectors.toList());
        return CreateUIEvent.Result.builder().events(eventDocumentList).build();
    }

    @ServiceMethod("find_by_layout")
    public FindByLayout.Result findByLayout(FindByLayout.Arg arg, ServiceContext context) {
        ILayout layout = layoutLogicService.findLayoutByApiName(context.getUser(), arg.getLayoutApiName(), arg.getDescribeApiName());
        List<Map<String, Object>> events = CollectionUtils.nullToEmpty(layout.getEvents());

        List<UIEventDocument> eventDocumentList = CollectionUtils.nullToEmpty(events)
                .stream()
                .map(UIEventDocument::of)
                .collect(Collectors.toList());
        return FindByLayout.Result.builder().events(eventDocumentList).build();
    }

    @ServiceMethod("delete_by_id")
    public DeleteById.Result findByLayout(DeleteById.Arg arg, ServiceContext context) {
        ILayout layout = layoutLogicService.findLayoutByApiName(context.getUser(), arg.getLayoutApiName(), arg.getDescribeApiName());
        List<Map<String, Object>> events = CollectionUtils.nullToEmpty(layout.getEvents());

        events.removeIf(a-> CollectionUtils.nullToEmpty(arg.getEventIdList()).contains(a.getOrDefault(IUIEvent.ID, null)));
        layout.setEvents(events);
        layout.set("is_deal_ui", true);
        layoutLogicService.updateLayout(context.getUser(), layout);
        return DeleteById.Result.builder().success(true).build();
    }

    @ServiceMethod("find_by_object")
    public FindByObject.Result findByObject(FindByObject.Arg arg, ServiceContext context) {
        List<IUIEvent> eventList = eventLogicService.findEventByObject(arg.getDescribeApiName(), context.getTenantId());
        List<UIEventDocument> eventDocumentList = CollectionUtils.nullToEmpty(eventList)
                .stream()
                .map(UIEventDocument::of)
                .collect(Collectors.toList());
        return FindByObject.Result.builder().events(eventDocumentList).build();
    }
}
