package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.Captcha;
import com.facishare.paas.appframework.common.service.dto.CaptchaVerify;
import com.facishare.paas.appframework.core.model.User;

public interface AppCaptchaService {
    /**
     * 获取登录验证码的图形（Captcha）
     * <p>主动生成（由调用方生成）
     *
     * @param user          操作人
     * @param captchaCode   验证码
     * @param captchaId     验证码标识
     * @param expireSeconds 有效期（秒）
     * @return 验证码图形
     */
    Captcha.Result createCaptchaCode(User user, String captchaCode, String captchaId, Integer expireSeconds);

    /**
     * 获取登录验证码的图形（Captcha），有效期 180s
     * <p>被动生成（由被调用发生成）
     *
     * @param user 操作人
     * @return 验证码图形
     */
    Captcha.Result createCaptchaCode(User user);


    /**
     *  验证验证码是否正确
     * @param user 操作人
     * @param captchaCode 输入用于验证的验证码
     * @param captchaId 图形验证码标识
     * @return true 正确，false 错误
     */
    CaptchaVerify.Result verifyCaptchaCode(User user, String captchaCode, String captchaId);
}
