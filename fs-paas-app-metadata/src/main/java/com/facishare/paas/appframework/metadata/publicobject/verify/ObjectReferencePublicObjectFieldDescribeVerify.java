package com.facishare.paas.appframework.metadata.publicobject.verify;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.WheresExt;
import com.facishare.paas.appframework.metadata.publicobject.module.InternationalVerifyMessage;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicFieldType;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2023/12/20
 */
@Slf4j
@Component
public class ObjectReferencePublicObjectFieldDescribeVerify extends AbstractPublicObjectFieldDescribeVerify {

    @Autowired
    private DescribeLogicService describeLogicService;

    @Override
    public List<String> getSupportedFieldTypes() {
        return ImmutableList.of(IFieldType.OBJECT_REFERENCE, IFieldType.OBJECT_REFERENCE_MANY,
                IFieldType.DEPARTMENT, IFieldType.DEPARTMENT_MANY, IFieldType.OUT_DEPARTMENT,
                IFieldType.EMPLOYEE, IFieldType.EMPLOYEE_MANY, IFieldType.OUT_EMPLOYEE);
    }

    @Override
    protected VerifyResult verifyPublicFieldByDependency(User user, IObjectDescribe describe, IFieldDescribe fieldDescribe) {
        if (!isPublicField(describe, fieldDescribe.getApiName())) {
            return null;
        }
        VerifyResult verifyResult = VerifyResult.buildEmpty();
        verifyResult.merge(verifyByWhere(user, describe, fieldDescribe));
        verifyResult.merge(verifyByRelationWhere(user, fieldDescribe));
        return verifyResult;
    }

    private VerifyResult verifyByWhere(User user, IObjectDescribe describe, IFieldDescribe fieldDescribe) {
        VerifyResult verifyResult = VerifyResult.buildEmpty();
        List<WheresExt> wheresExtList = getWheresExts(fieldDescribe, ObjectReferenceFieldDescribe.WHERES);
        if (FilterExt.FUNCTION_WHERE_TYPE.equals(fieldDescribe.get(ObjectReferenceFieldDescribe.WHERE_TYPE))) {
            String functionApiName = getFunctionApiName(wheresExtList);
            if (!Strings.isNullOrEmpty(functionApiName)) {
                InternationalVerifyMessage verifyMessage = InternationalVerifyMessage.of(
                        I18NKey.NOT_SUPPORT_PUBLIC_FIELD_WITH_FUNCTION,
                        I18NExt.text(I18NKey.NOT_SUPPORT_PUBLIC_FIELD_WITH_FUNCTION, fieldDescribe.getLabel(), functionApiName),
                        InternationalVerifyMessage.buildFieldLabel(fieldDescribe),
                        InternationalVerifyMessage.of(functionApiName));
                verifyResult.merge(VerifyResult.buildError(verifyMessage));
            }
        }
        Map<String, Set<String>> fieldNamesInFilter = getFieldNamesInFilter(fieldDescribe, ObjectReferenceFieldDescribe.WHERES);
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(user.getTenantId(), fieldNamesInFilter.keySet());
        describeMap.put(describe.getApiName(), describe);

        PublicFieldType publicFieldType = PublicFieldType.fromFieldDescribe(describe, fieldDescribe.getApiName());

        fieldNamesInFilter.forEach((describeApiName, fieldNames) -> {
            IObjectDescribe objectDescribe = describeMap.get(describeApiName);
            if (Objects.isNull(objectDescribe)) {
                return;
            }
            fieldNames.forEach(fieldName -> {
                PublicFieldType fieldType = PublicFieldType.fromFieldDescribe(objectDescribe, fieldName);
                if (!publicFieldType.dependentOn(fieldType)) {
                    IFieldDescribe field = objectDescribe.getFieldDescribe(fieldName);
                    if (Objects.isNull(field)) {
                        return;
                    }
                    log.warn("verifyPublicFieldByFieldRelation ei:{},fieldName:{},publicFieldType:{},dependentOnField:{},dependentOnFieldTYpe:{}",
                            user.getTenantId(), fieldDescribe.getApiName(), publicFieldType, field.getApiName(), fieldType);
                    InternationalVerifyMessage verifyMessage = InternationalVerifyMessage.of(
                            I18NKey.PUBLIC_FIELD_CANNOT_DEPENDENT_ON,
                            I18NExt.text(I18NKey.PUBLIC_FIELD_CANNOT_DEPENDENT_ON, fieldDescribe.getLabel(),
                                    publicFieldType.getName(), objectDescribe.getDisplayName(), fieldType.getName(), field.getLabel()),
                            InternationalVerifyMessage.buildFieldLabel(fieldDescribe),
                            InternationalVerifyMessage.of(publicFieldType.getI18nKey(), publicFieldType.getName()),
                            InternationalVerifyMessage.buildDescribeDisplayName(objectDescribe),
                            InternationalVerifyMessage.of(fieldType.getI18nKey(), fieldType.getName()),
                            InternationalVerifyMessage.buildFieldLabel(field));
                    verifyResult.append(verifyMessage);
                }
            });
        });
        return verifyResult;
    }

    private VerifyResult verifyByRelationWhere(User user, IFieldDescribe fieldDescribe) {
        VerifyResult verifyResult = VerifyResult.buildEmpty();
        List<WheresExt> wheresExtList = getWheresExts(fieldDescribe, ObjectReferenceFieldDescribe.RELATION_WHERES);
        if (!FilterExt.FUNCTION_WHERE_TYPE.equals(fieldDescribe.get(ObjectReferenceFieldDescribe.RELATION_WHERES_TYPE))) {
            String functionApiName = getFunctionApiName(wheresExtList);
            if (!Strings.isNullOrEmpty(functionApiName)) {
                InternationalVerifyMessage verifyMessage = InternationalVerifyMessage.of(
                        I18NKey.NOT_SUPPORT_PUBLIC_FIELD_WITH_FUNCTION,
                        I18NExt.text(I18NKey.NOT_SUPPORT_PUBLIC_FIELD_WITH_FUNCTION, fieldDescribe.getLabel(), functionApiName),
                        InternationalVerifyMessage.buildFieldLabel(fieldDescribe),
                        InternationalVerifyMessage.of(functionApiName));
                verifyResult.merge(VerifyResult.buildError(verifyMessage));
            }
        }
        return verifyResult;
    }

    private String getFunctionApiName(List<WheresExt> wheresExtList) {
        for (WheresExt wheresExt : wheresExtList) {
            List<FilterExt> filterExtList = wheresExt.getFilterExts();
            for (FilterExt filterExt : filterExtList) {
                if (ObjectUtils.equals(filterExt.getValueType(), FilterExt.FilterValueTypes.FUNCTION_VARIABLE)) {
                    return filterExt.getFieldValues().get(0);
                }
            }
        }
        return null;
    }

    private Map<String, Set<String>> getFieldNamesInFilter(IFieldDescribe fieldDescribe, String whereKey) {
        Map<String, Set<String>> result = Maps.newHashMap();
        List<WheresExt> wheresExtList = getWheresExts(fieldDescribe, whereKey);
        String masterApiName = fieldDescribe.getDescribeApiName();
        String subApiName = getTargetApiName(fieldDescribe);
        wheresExtList.forEach(wheresExt -> {
            List<FilterExt> filterExtList = wheresExt.getFilterExts();
            filterExtList.forEach(filterExt -> {
                filterExt.getVariableNames(masterApiName, subApiName).forEach((k, v) -> {
                    result.computeIfAbsent(k, it -> Sets.newHashSet()).addAll(v);
                });
            });
        });

        return result;
    }

    private String getTargetApiName(IFieldDescribe fieldDescribe) {
        if (IFieldType.EMPLOYEE.equals(fieldDescribe.getType()) || IFieldType.EMPLOYEE_MANY.equals(fieldDescribe.getType())
                || IFieldType.OUT_EMPLOYEE.equals(fieldDescribe.getType())) {
            return Utils.PERSONNEL_OBJ_API_NAME;
        }
        if (IFieldType.DEPARTMENT.equals(fieldDescribe.getType()) || IFieldType.DEPARTMENT_MANY.equals(fieldDescribe.getType())
                || IFieldType.OUT_DEPARTMENT.equals(fieldDescribe.getType())) {
            return Utils.DEPARTMENT_OBJ_API_NAME;
        }
        return fieldDescribe.get(ObjectReferenceFieldDescribe.TARGET_API_NAME, String.class);
    }

    private List<WheresExt> getWheresExts(IFieldDescribe fieldDescribe, String whereKey) {
        List<Wheres> wheresList = SearchTemplateQueryExt.of(getWheres(fieldDescribe, whereKey)).getWheres();
        return wheresList.stream().map(WheresExt::of).collect(Collectors.toList());
    }

    private List<Map> getWheres(IFieldDescribe fieldDescribe, String whereKey) {
        return CollectionUtils.nullToEmpty(fieldDescribe.get(whereKey, List.class));
    }
}
