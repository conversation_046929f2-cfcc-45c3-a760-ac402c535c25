package com.facishare.paas.appframework.metadata.config.handler

import com.facishare.paas.appframework.config.ConfigService
import spock.lang.Specification
import spock.lang.Unroll

class InvoiceApplicationLinesObjConfigHandlerTest extends Specification {

    ConfigService configService = Mock(ConfigService)
    InvoiceApplicationLinesObjConfigHandler handler = new InvoiceApplicationLinesObjConfigHandler(configService: configService)

    def 'getObjectAPIName should return correct API name'() {
        expect:
        handler.getObjectAPIName() == 'InvoiceApplicationLinesObj'
    }

    @Unroll
    def 'handle method updates field attributes based on configurations #testName'() {
        given:
        String tenantId = 'tenant1'
        Map<String, Object> objectConfig = [:]
        Map<String, Map<String, Object>> fieldConfig = [
                order_id         : [attrs: [:]],
                product_id       : [attrs: [:]],
                order_product_id : [attrs: [:]],
                invoiced_amount  : [attrs: [:]],
                invoiced_quantity: [attrs: [:]]
        ]

        when:
        configService.findTenantConfig(_, 'invoice_mode') >> "sales_order_product"
        configService.findTenantConfig(_, 'invoice_order_binding_status') >> "0"
        handler.handle(tenantId, objectConfig, fieldConfig)

        then:
        if (isSalesOrderProductInvoiceModel) {
            fieldConfig['order_id']['attrs']['is_readonly'] == 0
            fieldConfig['product_id']['attrs']['is_readonly'] == 0
        }

        if (isInvoiceOrderDecoupling) {
            fieldConfig['order_id']['attrs']['is_required'] == 1
            fieldConfig['order_id']['attrs']['is_readonly'] == 1
            fieldConfig['product_id']['attrs']['is_required'] == 1
            fieldConfig['product_id']['attrs']['is_readonly'] == 1
            fieldConfig['order_product_id']['attrs']['is_required'] == 1
            fieldConfig['order_product_id']['attrs']['is_readonly'] == 1
            fieldConfig['invoiced_amount']['attrs']['default_value'] == 1
            fieldConfig['invoiced_quantity']['attrs']['is_required'] == 1
            fieldConfig['invoiced_quantity']['attrs']['is_readonly'] == 1
        }

        where:
        testName                                          | isSalesOrderProductInvoiceModel | isInvoiceOrderDecoupling
        'when both configurations are true'               | true                            | true
        'when only SalesOrderProductInvoiceModel is true' | true                            | false
        'when only InvoiceOrderDecoupling is true'        | false                           | true
        'when neither configuration is true'              | false                           | false
    }
}
