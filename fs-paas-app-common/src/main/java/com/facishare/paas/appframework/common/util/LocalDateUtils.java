package com.facishare.paas.appframework.common.util;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.timezone.DateTimeFormatUtils.getDateFormatter;

@Slf4j
public class LocalDateUtils {

    //日期格式化属性
    public static final String DAY_DATE = "yyyy-MM-dd";
    public static final String QUOTE_DATE = "yyyy-QQQ";
    public static final String MONTH_DATE = "yyyy-MM";
    public static final String YEAR_DATE = "yyyy";

    private static final List<String> DATE_TYPES = Lists.newArrayList(DAY_DATE, QUOTE_DATE, MONTH_DATE, YEAR_DATE);

    public static List<LocalDate> getDateListOfLastWeek() {
        LocalDate firstDayOfLastWeek = LocalDate.now().minusWeeks(1).with(DayOfWeek.MONDAY);
        return Stream.iterate(firstDayOfLastWeek, x -> x.plusDays(1)).limit(7).collect(Collectors.toList());
    }

    public static List<LocalDate> getDateListOfLastWeekForCalculate(long maxAggregateDate) {
        LocalDate firstDayOfLastWeek = LocalDate.now().minusWeeks(1).with(DayOfWeek.MONDAY);
        LocalDate maxAggregateDateLocal = convertTimeMillisToDate(maxAggregateDate);
        return getDateListBetweenDates(firstDayOfLastWeek, maxAggregateDateLocal);
    }

    public static List<LocalDate> getDateListOfThisWeek() {
        LocalDate firstDayOfThisWeek = LocalDate.now().with(DayOfWeek.MONDAY);
        return Stream.iterate(firstDayOfThisWeek, x -> x.plusDays(1)).limit(7).collect(Collectors.toList());
    }

    public static List<LocalDate> getDateListOfThisWeekForCalculate(long maxAggregateDate) {
        LocalDate firstDayOfThisWeek = LocalDate.now().with(DayOfWeek.MONDAY);
        LocalDate maxAggregateDateLocal = convertTimeMillisToDate(maxAggregateDate);
        return getDateListBetweenDates(firstDayOfThisWeek, maxAggregateDateLocal);
    }

    public static List<LocalDate> getDateListOfLastMonth() {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfLastMonth = today.minusMonths(1).withDayOfMonth(1);
        YearMonth lastMonth = YearMonth.from(firstDayOfLastMonth);
        return Stream.iterate(firstDayOfLastMonth, x -> x.plusDays(1)).limit(lastMonth.lengthOfMonth()).collect(Collectors.toList());
    }

    public static List<LocalDate> getDateListOfLastMonthForCalculate(long maxAggregateDate) {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfLastMonth = today.minusMonths(1).withDayOfMonth(1);
        LocalDate maxAggregateDateLocal = convertTimeMillisToDate(maxAggregateDate);
        return getDateListBetweenDates(firstDayOfLastMonth, maxAggregateDateLocal);
    }

    public static List<LocalDate> getDateListOfThisMonth() {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfThisMonth = today.withDayOfMonth(1);
        YearMonth thisMonth = YearMonth.from(firstDayOfThisMonth);
        return Stream.iterate(firstDayOfThisMonth, x -> x.plusDays(1)).limit(thisMonth.lengthOfMonth()).collect(Collectors.toList());
    }

    public static List<LocalDate> getDateListOfThisMonthForCalculate(long maxAggregateDate) {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfThisMonth = today.withDayOfMonth(1);
        LocalDate maxAggregateDateLocal = convertTimeMillisToDate(maxAggregateDate);
        return getDateListBetweenDates(firstDayOfThisMonth, maxAggregateDateLocal);
    }

    public static List<LocalDate> getDateListOfLastQuarter() {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfLastQuarter = getFirstDayOfLastQuarter(today);
        YearMonth lastMonthOfLastQuarter = YearMonth.from(firstDayOfLastQuarter.plusMonths(2));
        LocalDate lastDayOfLastQuarter = firstDayOfLastQuarter.plusMonths(2).withDayOfMonth(lastMonthOfLastQuarter.lengthOfMonth());
        return getDateListBetweenDates(firstDayOfLastQuarter, lastDayOfLastQuarter);
    }

    public static List<LocalDate> getDateListOfLastQuarterForCalculate(long maxAggregateDate) {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfLastQuarter = getFirstDayOfLastQuarter(today);
        LocalDate maxAggregateDateLocal = convertTimeMillisToDate(maxAggregateDate);
        return getDateListBetweenDates(firstDayOfLastQuarter, maxAggregateDateLocal);
    }

    public static List<LocalDate> getDateListOfThisQuarter() {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfThisQuarter = getFirstDayOfQuarter(today);
        YearMonth lastMonthOfThisQuarter = YearMonth.from(firstDayOfThisQuarter.plusMonths(2));
        LocalDate lastDayOfThisQuarter = firstDayOfThisQuarter.plusMonths(2).withDayOfMonth(lastMonthOfThisQuarter.lengthOfMonth());
        return getDateListBetweenDates(firstDayOfThisQuarter, lastDayOfThisQuarter);
    }

    public static List<LocalDate> getDateListOfThisQuarterFoCalculate(long maxAggregateDate) {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfThisQuarter = getFirstDayOfQuarter(today);
        LocalDate maxAggregateDateLocal = convertTimeMillisToDate(maxAggregateDate);
        return getDateListBetweenDates(firstDayOfThisQuarter, maxAggregateDateLocal);
    }

    public static List<LocalDate> getDateListBetweenDates(LocalDate firstDate, LocalDate lastDate) {
        if (firstDate.isAfter(lastDate)) {
            return Lists.newArrayList();
        }
        return Stream.iterate(firstDate, x -> x.plusDays(1)).limit(firstDate.until(lastDate, ChronoUnit.DAYS) + 1).collect(Collectors.toList());
    }

    public static List<LocalDate> getDateListBetweenTimeMillis(long firstTime, long lastTime) {
        if (firstTime > lastTime) {
            return Lists.newArrayList();
        }
        LocalDate firstDate = convertTimeMillisToDate(firstTime);
        LocalDate lastDate = convertTimeMillisToDate(lastTime);
        return getDateListBetweenDates(firstDate, lastDate);
    }

    public static LocalDate convertTimeMillisToDate(long timeMillis) {
        return Instant.ofEpochMilli(timeMillis).atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static LocalDate convertTimeMillisToDateZoneTime(long timeMillis) {
        return Instant.ofEpochMilli(timeMillis).atZone(ZoneId.of("Asia/Shanghai")).toLocalDate();
    }

    public static LocalDateTime convertTimeMillisToDateTime(long timeMillis) {
        return Instant.ofEpochMilli(timeMillis).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static long convertDateToTimeMillis(LocalDate date) {
        return date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static long convertDateTimeToTimeMillis(LocalDateTime date) {
        return date.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static long truncateWithDay(Object value) {
        if (value instanceof Long) {
            return convertDateToTimeMillis(convertTimeMillisToDate((Long) value));
        }
        return convertDateToTimeMillis(convertTimeMillisToDate(Long.parseLong(value.toString())));
    }

    public static long plusOneDay(long time) {
        LocalDate date = convertTimeMillisToDate(time);
        return convertDateToTimeMillis(date.plusDays(1));
    }

    /**
     * time类型只支持时间戳和年月日类型，其余时间类型过来以后会原样返回
     *
     * @param dateFormat
     * @param time
     * @return
     */
    public static String transDateByFormat(String dateFormat, Long time) {
        if (Objects.isNull(time)) {
            return null;
        }
        if (StringUtils.isBlank(dateFormat)) {
            log.info("transDateByFormat param error! dateFormat:{},time:{}", dateFormat, time);
            dateFormat = DAY_DATE;
        }
        if (DATE_TYPES.contains(dateFormat)) {
            DateTimeFormatter dateTimeFormatter = getDateFormatter(dateFormat);
            return convertTimeMillisToDateZoneTime(time).format(dateTimeFormatter);
        } else {
            SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
            return sdf.format(new Date(time));
        }
    }

    private static int getQuarterByDate(LocalDate date) {
        int month = date.getMonthValue();
        if (month <= 3) {
            return 1;
        }
        if (month <= 6) {
            return 2;
        }
        if (month <= 9) {
            return 3;
        }
        return 4;
    }

    private static int getLastQuarterByDate(LocalDate date) {
        int quarter = getQuarterByDate(date);
        if (quarter == 1) {
            return 4;
        }
        return quarter - 1;
    }

    private static int getFirstMonthByQuarter(int quarter) {
        if (quarter == 1) {
            return 1;
        }
        if (quarter == 2) {
            return 4;
        }
        if (quarter == 3) {
            return 7;
        }
        return 10;
    }

    private static LocalDate getFirstDayOfQuarter(LocalDate date) {
        int quarter = getQuarterByDate(date);
        int month = getFirstMonthByQuarter(quarter);
        return date.withMonth(month).withDayOfMonth(1);
    }

    private static LocalDate getFirstDayOfLastQuarter(LocalDate date) {
        int quarter = getLastQuarterByDate(date);
        int month = getFirstMonthByQuarter(quarter);
        if (quarter == 4) {
            return LocalDate.of(date.getYear() - 1, month, 1);
        }
        return date.withMonth(month).withDayOfMonth(1);
    }

    public static void main(String[] args) {
//        List<LocalDate> result = getDateListOfThisQuarter();
//        System.out.println(result);
//
        long now = System.currentTimeMillis();
        LocalDateTime localDateTime = convertTimeMillisToDateTime(now);
        long l = convertDateTimeToTimeMillis(localDateTime);

        System.out.println(now);
        System.out.println(localDateTime);
        System.out.println(l);

//        long now = System.currentTimeMillis();
//        long minTime = now - 5 * 24 * 60 * 60 * 1000;
//        System.out.println(getDateListBetweenTimeMillis(minTime, now));
    }
}
