package com.facishare.paas.appframework.common.service.codec

import com.facishare.paas.appframework.core.rest.BaseAPIResult
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.rest.core.exception.RestProxyRuntimeException
import spock.lang.Specification
import spock.lang.Unroll

import java.nio.charset.StandardCharsets

/**
 * GenerateByAI
 * 测试内容描述：RestResultCodeC编解码器的单元测试
 */
class RestResultCodeCTest extends Specification {

    def codec = new RestResultCodeC()

    def "test encodeArg with null object"() {
        when: "编码null对象"
        def result = codec.encodeArg(null)

        then: "应该返回null"
        result == null
    }

    def "test encodeArg with String object"() {
        given: "准备字符串对象"
        def testString = "test string"

        when: "编码字符串对象"
        def result = codec.encodeArg(testString)

        then: "应该返回UTF-8编码的字节数组"
        result == testString.getBytes(StandardCharsets.UTF_8)
        new String(result, StandardCharsets.UTF_8) == testString
    }

    def "test encodeArg with non-String object"() {
        given: "准备非字符串对象"
        def testObject = [name: "test", value: 123]

        when: "编码非字符串对象"
        def result = codec.encodeArg(testObject)

        then: "应该返回JSON编码的字节数组"
        result != null
        def jsonString = new String(result, StandardCharsets.UTF_8)
        jsonString.contains("test")
        jsonString.contains("123")
    }

    def "test encodeArg with complex object"() {
        given: "准备复杂对象"
        def complexObject = [
                id    : 1,
                name  : "test",
                nested: [
                        value : "nested value",
                        number: 42
                ],
                list  : [1, 2, 3]
        ]

        when: "编码复杂对象"
        def result = codec.encodeArg(complexObject)

        then: "应该正确编码为JSON字节数组"
        result != null
        def jsonString = new String(result, StandardCharsets.UTF_8)
        jsonString.contains("test")
        jsonString.contains("nested value")
        jsonString.contains("42")
    }

    @Unroll
    def "test decodeResult with status code #statusCode"() {
        given: "准备响应数据"
        def responseBody = "test response"
        def bytes = responseBody.getBytes(StandardCharsets.UTF_8)
        def headers = [:]

        when: "解码响应"
        if (statusCode >= 300) {
            codec.decodeResult(statusCode, headers, bytes, String.class)
        } else {
            def result = codec.decodeResult(statusCode, headers, bytes, String.class)
            assert result == responseBody
        }

        then: "验证结果"
        noExceptionThrown()


        where:

        statusCode << [200, 201, 299]
    }

    def "test decodeResult with String class"() {
        given: "准备字符串响应"
        def responseBody = "test string response"
        def bytes = responseBody.getBytes(StandardCharsets.UTF_8)
        def headers = [:]

        when: "解码为String类型"
        def result = codec.decodeResult(200, headers, bytes, String.class)

        then: "应该返回原始字符串"
        result == responseBody
    }

    def "test decodeResult with Void type"() {
        given: "准备响应数据"
        def responseBody = "some response"
        def bytes = responseBody.getBytes(StandardCharsets.UTF_8)
        def headers = [:]

        when: "解码为Void类型"
        def result = codec.decodeResult(200, headers, bytes, Void.TYPE)

        then: "应该返回null"
        result == null
    }

    def "test decodeResult with non-BaseAPIResult object"() {
        given: "准备非BaseAPIResult的JSON响应"
        def responseJson = '{"name":"test","value":123}'
        def bytes = responseJson.getBytes(StandardCharsets.UTF_8)
        def headers = [:]

        when: "解码为Map类型"
        def result = codec.decodeResult(200, headers, bytes, Map.class)

        then: "应该正确解析JSON"
        result instanceof Map
        result.name == "test"
        result.value == 123
    }

    def "test decodeResult with successful BaseAPIResult"() {
        given: "准备成功的BaseAPIResult响应"
        def successResult = new TestBaseAPIResult(true, 0, "Success", "test data")
        def responseJson = '{"success":true,"code":0,"message":"Success","data":"test data"}'
        def bytes = responseJson.getBytes(StandardCharsets.UTF_8)
        def headers = [:]

        when: "解码为BaseAPIResult类型"
        def result = codec.decodeResult(200, headers, bytes, TestBaseAPIResult.class)

        then: "应该正确解析并返回结果"
        result instanceof TestBaseAPIResult
        result.isSuccess()
        result.getCode() == 0
        result.getMessage() == "Success"
        result.getData() == "test data"
    }

    def "test decodeResult with failed BaseAPIResult"() {
        given: "准备失败的BaseAPIResult响应"
        def responseJson = '{"success":false,"code":400,"message":"Bad Request","data":null}'
        def bytes = responseJson.getBytes(StandardCharsets.UTF_8)
        def headers = [:]

        when: "解码失败的BaseAPIResult"
        codec.decodeResult(200, headers, bytes, TestBaseAPIResult.class)

        then: "应该抛出ValidateException"
        def exception = thrown(ValidateException)
        exception.message == "Bad Request"
        exception.code == 400
    }

    def "test decodeResult with malformed JSON"() {
        given: "准备格式错误的JSON"
        def malformedJson = '{"name":"test","value":'  // 不完整的JSON
        def bytes = malformedJson.getBytes(StandardCharsets.UTF_8)
        def headers = [:]

        when: "解码格式错误的JSON"
        codec.decodeResult(200, headers, bytes, Map.class)

        then: "应该抛出异常"
        thrown(Exception)
    }

    def "test decodeResult with empty response"() {
        given: "准备空响应"
        def bytes = "".getBytes(StandardCharsets.UTF_8)
        def headers = [:]

        when: "解码空响应为String"
        def result = codec.decodeResult(200, headers, bytes, String.class)

        then: "应该返回空字符串"
        result == ""
    }

    def "test decodeResult with Chinese characters"() {
        given: "准备包含中文字符的响应"
        def chineseResponse = "测试中文响应"
        def bytes = chineseResponse.getBytes(StandardCharsets.UTF_8)
        def headers = [:]

        when: "解码中文响应"
        def result = codec.decodeResult(200, headers, bytes, String.class)

        then: "应该正确处理中文字符"
        result == chineseResponse
    }

    def "test decodeResult with special characters"() {
        given: "准备包含特殊字符的JSON"
        def specialJson = '{"message":"Special chars: !@#$%^&*()","emoji":"😀"}'
        def bytes = specialJson.getBytes(StandardCharsets.UTF_8)
        def headers = [:]

        when: "解码包含特殊字符的JSON"
        def result = codec.decodeResult(200, headers, bytes, Map.class)

        then: "应该正确处理特殊字符"
        result instanceof Map
        result.message == "Special chars: !@#\$%^&*()"
        result.emoji == "😀"
    }

    def "test encodeArg with special characters"() {
        given: "准备包含特殊字符的字符串"
        def specialString = "Special chars: !@#\$%^&*() 中文 😀"

        when: "编码特殊字符串"
        def result = codec.encodeArg(specialString)

        then: "应该正确编码特殊字符"
        result != null
        new String(result, StandardCharsets.UTF_8) == specialString
    }

    def "test encodeArg with empty string"() {
        given: "准备空字符串"
        def emptyString = ""

        when: "编码空字符串"
        def result = codec.encodeArg(emptyString)

        then: "应该返回空字节数组"
        result != null
        result.length == 0
        new String(result, StandardCharsets.UTF_8) == emptyString
    }

    def "test decodeResult with successful HTTP status codes"() {
        given: "准备测试数据"
        def headers = [:]

        expect: "验证成功状态码的处理"
        codec.decodeResult(200, headers, "OK".getBytes(StandardCharsets.UTF_8), String.class) == "OK"
        codec.decodeResult(201, headers, "Created".getBytes(StandardCharsets.UTF_8), String.class) == "Created"
        codec.decodeResult(204, headers, "".getBytes(StandardCharsets.UTF_8), String.class) == ""
    }

    def "test decodeResult with status code 300 throws exception"() {
        given: "准备测试数据"
        def responseBody = "Error response"
        def bytes = responseBody.getBytes(StandardCharsets.UTF_8)
        def headers = [:]

        when: "解码状态码300的响应"
        codec.decodeResult(300, headers, bytes, String.class)

        then: "应该抛出RestProxyRuntimeException"
        def exception = thrown(RestProxyRuntimeException)
        exception.statusCode == 300
        exception.message.contains("Error response")
    }

    def "test decodeResult with status code 404 throws exception"() {
        given: "准备测试数据"
        def responseBody = "Not Found"
        def bytes = responseBody.getBytes(StandardCharsets.UTF_8)
        def headers = [:]

        when: "解码状态码404的响应"
        codec.decodeResult(404, headers, bytes, String.class)

        then: "应该抛出RestProxyRuntimeException"
        def exception = thrown(RestProxyRuntimeException)
        exception.statusCode == 404
    }

    def "test decodeResult with status code 500 throws exception"() {
        given: "准备测试数据"
        def responseBody = "Internal Server Error"
        def bytes = responseBody.getBytes(StandardCharsets.UTF_8)
        def headers = [:]

        when: "解码状态码500的响应"
        codec.decodeResult(500, headers, bytes, String.class)

        then: "应该抛出RestProxyRuntimeException"
        def exception = thrown(RestProxyRuntimeException)
        exception.statusCode == 500
    }

    // 测试用的BaseAPIResult实现类
    static class TestBaseAPIResult extends BaseAPIResult {
        private Object data

        TestBaseAPIResult() {}

        TestBaseAPIResult(boolean success, int code, String message, Object data) {
            this.success = success
            this.code = code
            this.message = message
            this.data = data
        }

        Object getData() {
            return data
        }

        void setData(Object data) {
            this.data = data
        }
    }
}
