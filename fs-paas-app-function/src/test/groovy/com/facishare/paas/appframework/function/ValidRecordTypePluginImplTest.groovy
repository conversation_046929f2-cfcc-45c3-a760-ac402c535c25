package com.facishare.paas.appframework.function

import com.alibaba.fastjson.JSON
import com.facishare.function.biz.api.service.FunctionService
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.function.dto.FilterRecordType
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class ValidRecordTypePluginImplTest extends Specification {

    ValidRecordTypePluginImpl validRecordTypePlugin
    FunctionService functionService
    
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
    }
    
    def setup() {
        functionService = Mock(FunctionService)
        validRecordTypePlugin = new ValidRecordTypePluginImpl(functionService: functionService)
    }
    
    def "test filterRecordType"() {
        given: "准备参数和模拟返回值"
        def tenantId = "74255"
        User user = User.systemUser(tenantId)
        FilterRecordType.Arg arg = FilterRecordType.Arg.builder()
                .user(user)
                .functionApiName("testFunction")
                .build()
        
        def expectedResult = FilterRecordType.Result.builder()
                .recordTypeNames(["type1", "type2", "type3"])
                .build()
        String resultJson = JSON.toJSONString(expectedResult)
        
        when: "调用filterRecordType方法"
        functionService.executeFuncMethod(user.getTenantId(), user.getUserId(), "testFunction", "filterRecordType", _) >> resultJson
        FilterRecordType.Result result = validRecordTypePlugin.filterRecordType(arg)
        
        then: "返回结果应符合预期"
        result != null
        result.getRecordTypeNames().size() == 3
        result.getRecordTypeNames().contains("type1")
        result.getRecordTypeNames().contains("type2")
        result.getRecordTypeNames().contains("type3")
    }
    
    def "test filterRecordType with empty result"() {
        given: "准备参数和空结果"
        def tenantId = "74255"
        User user = User.systemUser(tenantId)
        FilterRecordType.Arg arg = FilterRecordType.Arg.builder()
                .user(user)
                .functionApiName("testFunction")
                .build()
        
        def emptyResult = FilterRecordType.Result.builder()
                .recordTypeNames([])
                .build()
        String resultJson = JSON.toJSONString(emptyResult)
        
        when: "调用filterRecordType方法"
        functionService.executeFuncMethod(user.getTenantId(), user.getUserId(), "testFunction", "filterRecordType", _) >> resultJson
        FilterRecordType.Result result = validRecordTypePlugin.filterRecordType(arg)
        
        then: "返回结果应为空列表"
        result != null
        result.getRecordTypeNames().size() == 0
    }
    
    def "test filterRecordType with failure result"() {
        given: "准备参数和失败结果"
        def tenantId = "74255"
        User user = User.systemUser(tenantId)
        FilterRecordType.Arg arg = FilterRecordType.Arg.builder()
                .user(user)
                .functionApiName("testFunction")
                .build()
        
        def failureResult = FilterRecordType.Result.builder()
                .recordTypeNames(null)
                .build()
        String resultJson = JSON.toJSONString(failureResult)
        
        when: "调用filterRecordType方法"
        functionService.executeFuncMethod(user.getTenantId(), user.getUserId(), "testFunction", "filterRecordType", _) >> resultJson
        FilterRecordType.Result result = validRecordTypePlugin.filterRecordType(arg)
        
        then: "返回结果应表示失败"
        result != null
        result.getRecordTypeNames() == null
    }
} 