package com.facishare.paas.appframework.log;

import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import org.springframework.stereotype.Component;

@Component("createModifyRecordProvider")
public class CreateModifyRecordProvider extends ObjectInfoModelModifyRecordProvider {
    @Override
    public String getApiName() {
        return ActionType.Add.getId();
    }

    @Override
    public ModifyRecord getModifyRecord(LogInfo logInfo) {
        ModifyRecord modifyRecord = super.getModifyRecord(logInfo);
        modifyRecord.setDetailInfos(logInfo.getSnapshot().getDetailInfo());
        modifyRecord.setMasterLogId(logInfo.getMasterLogId());
        modifyRecord.setConvertEventId(logInfo.getSnapshot().getConvertEventId());
        modifyRecord.setSourceDetailInfos(logInfo.getSnapshot().getSourceDetailInfo());
        fillConvertRuleInfo(logInfo, modifyRecord);
        return modifyRecord;
    }
}
