package com.facishare.crm.privilege.rest.proxy.metadata;

import com.facishare.crm.userdefobj.DefObjConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * Created by yusb on 2017/7/29.
 */
@AllArgsConstructor
@Data
@Builder
public class FindDescribeListArg {

  /**
   * @param isDraft 是否要Draft还是Describe
   * @param isIncludeSystemObj 是否包含老的预置对象
   * @param isIncludeFieldDescribe 是否包含fieldDescribe
   * @param isIncludeUnActived 是否包含禁用的对象
   * @param packageName 写死"CRM" DefConstants.PACKAGE_NAME_CRM
   * @param isAsc null表示不用排序, true表示按照创建时间升序,false表示按照创建时间降序
   * @param isIncludeDetailObj 是否包含从对象
   * 参数默认都是false
   *
   */

  private boolean isDraft = false;
  private boolean isIncludeSystemObj = false;
  private boolean isIncludeFieldDescribe = false;
  private boolean isIncludeUnActived = false;
  private String packageName = DefObjConstants.PACKAGE_NAME_CRM;
  private Boolean isAsc = null;
  private boolean isExcludeDetailObj = false;

}
