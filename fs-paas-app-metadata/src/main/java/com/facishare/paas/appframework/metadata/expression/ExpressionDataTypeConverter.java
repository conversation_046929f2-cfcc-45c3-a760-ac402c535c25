package com.facishare.paas.appframework.metadata.expression;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;

/**
 * 数据类型转换
 * Created by liyiguang on 2018/4/23.
 */
public interface ExpressionDataTypeConverter {

    Object toExpressionCalculateObject(Object object);

    Object toExpressionCalculateObject(Object object, IFieldDescribe fieldDescribe, boolean useValue, String tenantId);

    Object toPaaSObject(Object object);
}
