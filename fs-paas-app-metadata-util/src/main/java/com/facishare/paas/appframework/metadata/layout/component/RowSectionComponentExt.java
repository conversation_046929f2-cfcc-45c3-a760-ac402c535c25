package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.metadata.api.JsonCompatible;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.google.common.collect.Lists;
import org.bson.Document;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/2/23
 */
public class RowSectionComponentExt extends DocumentBasedBean implements JsonCompatible {
    public static final String TYPE = "type";
    public static final String FIELD_SECTION = "field_section";

    public RowSectionComponentExt() {
    }

    public RowSectionComponentExt(Map map) {
        super(map);
    }

    /**
     * 对齐方式
     *
     * @return whole 整行 space_between 两端  up_and_down 上下
     */
    public String getType() {
        return get(TYPE, String.class);
    }

    public void setType(String type) {
        set(TYPE, type);
    }

    public List<FieldSectionComponentExt> getFieldSection() {
        List<Map> fieldSection = (List<Map>) get(FIELD_SECTION);
        if (Objects.isNull(fieldSection)) {
            return Lists.newArrayList();
        }
        return fieldSection.stream()
                .map(FieldSectionComponentExt::new)
                .collect(Collectors.toList());
    }

    public void addFieldSection(FieldSectionComponentExt fieldSectionComponentExt) {
        Object o = map.computeIfAbsent(FIELD_SECTION, k -> Lists.newArrayList());
        if (o instanceof List) {
            ((List<Map>) o).add(Document.parse(fieldSectionComponentExt.toJsonString()));
        }
    }
}
