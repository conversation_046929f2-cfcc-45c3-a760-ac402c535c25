package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试AutoNumberFieldDataConverter类的自动编号字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class AutoNumberFieldDataConverterTest {

  @Mock
  private IObjectDataProxyService dataService;

  @Mock
  private SessionContext sessionContext;

  private AutoNumberFieldDataConverter converter;
  private IFieldDescribe fieldDescribe;
  private IObjectData objectData;

  @BeforeEach
  void setUp() {
    converter = new AutoNumberFieldDataConverter();
    fieldDescribe = createAutoNumberFieldDescribe("auto_number_field");
    
    // 设置转换器的属性
    converter.setFieldDescribe(fieldDescribe);
    converter.setDataService(dataService);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法正常转换自动编号
   */
  @Test
  @DisplayName("测试convertFieldData - 正常自动编号转换")
  void testConvertFieldData_NormalAutoNumberConversion() throws Exception {
    // 准备测试数据
    String autoNumber = "AUTO-2023-001";
    objectData = createObjectData("auto_number_field", autoNumber);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals(autoNumber, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理null值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null值")
  void testConvertFieldData_HandlesNullValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("auto_number_field", null);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertNull(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理空字符串
   */
  @Test
  @DisplayName("测试convertFieldData - 处理空字符串")
  void testConvertFieldData_HandlesEmptyString() throws Exception {
    // 准备测试数据
    objectData = createObjectData("auto_number_field", "");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理数字类型的自动编号
   */
  @Test
  @DisplayName("测试convertFieldData - 数字类型自动编号")
  void testConvertFieldData_NumericAutoNumber() throws Exception {
    // 准备测试数据
    Integer autoNumber = 123456;
    objectData = createObjectData("auto_number_field", autoNumber);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("123456", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理长整型自动编号
   */
  @Test
  @DisplayName("测试convertFieldData - 长整型自动编号")
  void testConvertFieldData_LongAutoNumber() throws Exception {
    // 准备测试数据
    Long autoNumber = 9876543210L;
    objectData = createObjectData("auto_number_field", autoNumber);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("9876543210", result);
  }

  /**
   * 提供不同类型的自动编号数据测试
   */
  private static Stream<Arguments> provideAutoNumberData() {
    return Stream.of(
        Arguments.of("AUTO-2023-001", "AUTO-2023-001"),
        Arguments.of("", ""),
        Arguments.of(null, null),
        Arguments.of(123, "123"),
        Arguments.of(456L, "456"),
        Arguments.of(0, "0"),
        Arguments.of("0", "0")
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理不同类型的自动编号数据
   */
  @ParameterizedTest
  @MethodSource("provideAutoNumberData")
  @DisplayName("测试convertFieldData - 不同类型的自动编号数据")
  void testConvertFieldData_DifferentAutoNumberData(Object inputValue, String expectedResult) throws Exception {
    // 准备测试数据
    objectData = createObjectData("auto_number_field", inputValue);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals(expectedResult, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理字段不存在的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 字段不存在")
  void testConvertFieldData_FieldNotExists() throws Exception {
    // 准备测试数据 - 创建不包含目标字段的数据
    objectData = createObjectData("other_field", "AUTO-2023-001");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 字段不存在时应该返回null
    assertNull(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理复杂格式的自动编号
   */
  @Test
  @DisplayName("测试convertFieldData - 复杂格式自动编号")
  void testConvertFieldData_ComplexFormatAutoNumber() throws Exception {
    // 准备测试数据 - 包含特殊字符的自动编号
    String complexAutoNumber = "ORD-2023-12-31-001-ABC";
    objectData = createObjectData("auto_number_field", complexAutoNumber);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals(complexAutoNumber, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理包含中文的自动编号
   */
  @Test
  @DisplayName("测试convertFieldData - 包含中文的自动编号")
  void testConvertFieldData_ChineseAutoNumber() throws Exception {
    // 准备测试数据 - 包含中文字符的自动编号
    String chineseAutoNumber = "订单-2023-001";
    objectData = createObjectData("auto_number_field", chineseAutoNumber);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals(chineseAutoNumber, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试转换器的基本属性设置
   */
  @Test
  @DisplayName("测试转换器属性设置")
  void testConverterProperties() {
    // 验证属性设置
    assertEquals(fieldDescribe, converter.getFieldDescribe());
    assertEquals(dataService, converter.getDataService());
  }

  /**
   * 创建测试用的自动编号字段描述
   */
  private IFieldDescribe createAutoNumberFieldDescribe(String apiName) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试自动编号字段");
    fieldMap.put("type", IFieldType.AUTO_NUMBER);
    return FieldDescribeFactory.newInstance(fieldMap);
  }

  /**
   * 创建测试用的对象数据
   */
  private IObjectData createObjectData(String fieldName, Object value) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldName, value);
    return new ObjectData(dataMap);
  }
}
