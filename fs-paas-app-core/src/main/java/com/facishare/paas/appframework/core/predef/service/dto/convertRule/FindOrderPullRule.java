package com.facishare.paas.appframework.core.predef.service.dto.convertRule;

import com.facishare.paas.appframework.core.model.MappingRuleDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


public interface FindOrderPullRule {
    @Data
    class Arg {
        private String objectApiName;
        private Boolean isDoublePull;
        private String recordType;

        public boolean isDoublePull() {
            return Boolean.TRUE.equals(isDoublePull);
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private List<MappingRuleDocument> ruleList;
    }
}
