package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

import static com.facishare.crm.openapi.Utils.PRICE_BOOK_API_NAME;

/**
 * 价目表
 * create by <PERSON><PERSON><PERSON> on 2019/03/31
 */
@Component
public class PriceBookObjectProvider extends DefaultObjectImportProvider {
    @Override
    public String getObjectCode() {
        return PRICE_BOOK_API_NAME;
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return ImportType.DEFAULT;
    }

    @Override
    protected int getDuplicateJudgmentType(IObjectDescribe objectDescribe) {
        return UNSUPPORT_UPDATE_IMPORT;
    }
}
