package com.facishare.paas.appframework.core.predef.handler;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CacheContext;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ContextCacheKeys;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.exception.DuplicateSearchValidationException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.handler.HandlerAttributes;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.facade.ApprovalFlowServiceFacade;
import com.facishare.paas.appframework.core.predef.facade.SaveActionServiceFacade;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Created by zhouwr on 2023/1/16.
 */
@Slf4j
@Component
@HandlerProvider(name = "defaultDuplicateSearchSaveBeforeHandler")
public class DefaultDuplicateSearchSaveBeforeHandler implements SaveActionHandler<SaveActionHandler.Arg, SaveActionHandler.Result> {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ApprovalFlowServiceFacade approvalFlowServiceFacade;
    @Autowired
    private SaveActionServiceFacade saveActionServiceFacade;

    @Override
    public Result handle(HandlerContext context, Arg arg) {
        if (context.skipBaseValidate() || Boolean.TRUE.equals(context.getAttribute(HandlerAttributes.SKIP_DUPLICATE_SEARCH_CHECK))) {
            return null;
        }
        if (!serviceFacade.needDuplicateSearch(context.getRequestContext(), arg.getObjectApiName(), arg.getInterfaceArg().duplicateRuleApiName(),
                arg.getInterfaceArg().skipDuplicateSearchCheck())) {
            return null;
        }
        checkDuplicate(context, arg);
        return null;
    }


    protected void checkDuplicate(HandlerContext context, Arg arg) {
        //拷贝一下防止修改原数据
        IObjectData objectData = ObjectDataExt.of(arg.objectData()).copy();
        // 单独新建对象，需要在调用查重前，确保数据中有负责人字段
        if (isAddAction(context)) {
            modifyOwnerOrTeamMemberBeforeCreate(context, arg, objectData);
        }
        RLock lock = serviceFacade.duplicateSearchLock(context.getUser(), arg.getObjectDescribe(), objectData);
        cacheLockInContext(context, lock);
        doDuplicate(context, arg, objectData);
    }

    private void doDuplicate(HandlerContext context, Arg arg, IObjectData objectData) {
        List<DuplicateSearchResult.DuplicateData> duplicateData = getDuplicateData(context, arg, objectData);
        boolean isDuplicate = CollectionUtils.notEmpty(duplicateData) && duplicateData.stream().anyMatch(x -> CollectionUtils.notEmpty(x.getDataIds()));
        if (isDuplicate && arg.getInterfaceArg().fromImport()) {
            saveActionServiceFacade.buildDuplicateImportAddError(context.getUser(), duplicateData, arg.getObjectDescribe(), true);
        }
        if (isDuplicate && !arg.acceptNonBlockingResult() && !context.getRequestContext().isFromFunction()) {
            throw new DuplicateSearchValidationException(I18N.text(I18NKey.DUPLICATED_DATA));
        }
        if (isDuplicate) {
            throw new AcceptableValidateException(buildDuplicateSearchResult(arg, objectData));
        }
    }

    private BaseObjectSaveAction.Result buildDuplicateSearchResult(Arg arg, IObjectData objectData) {
        return BaseObjectSaveAction.Result.builder()
                .objectData(ObjectDataDocument.of(objectData))
                .details(arg.getDetailObjectData())
                .relatedDataList(arg.getRelatedObjectData())
                .isDuplicate(true)
                .build();
    }

    private boolean isAddAction(HandlerContext context) {
        return StandardAction.Add.name().equals(context.getInterfaceCode());
    }

    private void cacheLockInContext(HandlerContext context, RLock lock) {
        if (Objects.isNull(lock)) {
            return;
        }
        CacheContext.getContext().setCache(ContextCacheKeys.DUPLICATE_SEARCH_LOCK, lock);
    }

    protected void modifyOwnerOrTeamMemberBeforeCreate(HandlerContext context, Arg arg, IObjectData objectData) {
        IObjectDescribe describe = arg.getObjectDescribe();
        //如果是从对象,那么要从库中的主对象中获取从owner
        if (ObjectDescribeExt.of(describe).isSlaveObject()) {
            // 同步主对象的负责人、外部负责人
            synchronizeDetailOwnerFromMasterObjectDataInDB(context, objectData, describe);
        }
    }

    private void synchronizeDetailOwnerFromMasterObjectDataInDB(HandlerContext context, IObjectData objectData, IObjectDescribe describe) {
        IObjectData masterObjectData = approvalFlowServiceFacade.getRealMasterObjectData(context.getUser(), describe, objectData);
        //masterData有可能是空的,因为masterId是有可能为了验证规则,提前就系统生成的id,而不是真正数据库中的id。
        if (masterObjectData != null) {
            ObjectDataExt masterDataExt = ObjectDataExt.of(masterObjectData);
            ObjectDataExt detailObjectDataExt = ObjectDataExt.of(objectData);
            Optional<String> ownerId = masterDataExt.getOwnerId();
            ownerId.ifPresent(detailObjectDataExt::setOwnerId);

            // 从主对象同步外部负责人信息
            masterDataExt.getOutOwnerId().ifPresent(it -> {
                detailObjectDataExt.setOutOwner(Lists.newArrayList(it));
                detailObjectDataExt.setOutTenantId(masterDataExt.getOutTenantId());
                // 同步合作伙伴
                if (ObjectDescribeExt.of(describe).isPRMEnabled()) {
                    detailObjectDataExt.setPartnerId(masterDataExt.getPartnerId());
                }
            });
        }
    }

    protected List<DuplicateSearchResult.DuplicateData> getDuplicateData(HandlerContext context, Arg arg, IObjectData objectData) {
        return serviceFacade.getDuplicateData(context.getUser(), arg.getObjectDescribe(), objectData, arg.getInterfaceArg().duplicateRuleApiName(), arg.getInterfaceArg().skipFuzzyRuleDuplicateSearch());
    }
}
