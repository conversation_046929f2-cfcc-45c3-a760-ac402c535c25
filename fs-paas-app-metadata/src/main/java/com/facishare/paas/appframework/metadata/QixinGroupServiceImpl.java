package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.describe.IObjectCluster;
import com.facishare.paas.metadata.api.service.IObjectClusterService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service("qixinGroupService")
public class QixinGroupServiceImpl implements QixinGroupService {
    @Autowired
    private IObjectClusterService objectClusterService;

    @Override
    public boolean isEnableQixinGroup(User user, String apiName) {
        IObjectCluster iObjectCluster = find(user, apiName);
        if (Objects.isNull(iObjectCluster)) {
            return false;
        }

        return QixinGroupConfig.of(iObjectCluster).isObjectEnabled();
    }

    @Override
    public void saveConfig(User user, String apiName, QixinGroupChangeType status) {
        switch (status) {
            case Enable:
                enableConfig(user, apiName);
                break;
            case Disable:
                disableConfig(user, apiName);
                break;
            case Delete:
                deleteConfig(user, apiName);
                break;
            case Create:
                createConfig(user, apiName);
                break;
            case Edit:
                //
                break;
            default:
                break;
        }
    }

    @Override
    public IObjectCluster createConfig(User user, String apiName) {
        try {
            return objectClusterService.create(user.getTenantId(), apiName);
        } catch (MetadataServiceException e) {
            log.warn("Error in create Qixin ChatGroup config, ei:{}, apiName:{}", user.getTenantId(), apiName, e);
        }
        return null;
    }

    @Override
    public IObjectCluster enableConfig(User user, String apiName) {
        try {
            createIfNotExist(user, apiName);
            return objectClusterService.enable(user.getTenantId(), apiName);
        } catch (MetadataServiceException e) {
            log.warn("Error in enable Qixin ChatGroup config, ei:{}, apiName:{}", user.getTenantId(), apiName, e);
        }
        return null;
    }

    private void createIfNotExist(User user, String apiName) {
        if(Objects.isNull(find(user, apiName))) {
            createConfig(user, apiName);
        }
    }

    @Override
    public IObjectCluster disableConfig(User user, String apiName) {
        try {
            createIfNotExist(user, apiName);
            return objectClusterService.disable(user.getTenantId(), apiName);
        } catch (MetadataServiceException e) {
            log.warn("Error in disable Qixin ChatGroup config, ei:{}, apiName:{}", user.getTenantId(), apiName, e);
        }
        return null;
    }

    @Override
    public IObjectCluster deleteConfig(User user, String apiName) {
        try {
            return objectClusterService.delete(user.getTenantId(), apiName);
        } catch (MetadataServiceException e) {
            log.warn("Error in delete Qixin ChatGroup config, ei:{}, apiName:{}", user.getTenantId(), apiName, e);
        }
        return null;
    }

    @Override
    public IObjectCluster find(User user, String apiName) {
        IObjectCluster iObjectCluster = null;
        try {
            iObjectCluster = objectClusterService.find(user.getTenantId(), apiName);
        } catch (MetadataServiceException e) {
            log.warn("Error in find Qixin ChatGroup config, ei:{}, apiName:{}", user.getTenantId(), apiName, e);
        }
        return iObjectCluster;
    }
}
