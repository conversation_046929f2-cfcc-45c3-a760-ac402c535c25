package com.facishare.paas.appframework.core.predef.handler.flowcompleted;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.handler.AbstractActTCCActionHandler;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.core.predef.facade.AuditLogServiceFacade;
import com.facishare.paas.appframework.core.predef.facade.FlowCompletedActionServiceFacade;
import com.facishare.paas.appframework.core.predef.facade.SaveActionServiceFacadeImpl;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.flow.ExtraDataKeys;
import com.facishare.paas.appframework.metadata.DataSnapshotLogicService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.dto.ConvertRuleDataContainer;
import com.facishare.paas.appframework.metadata.dto.UpdateMasterAndDetailData;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.transaction.tcc.annotation.TccTransactional;
import com.fxiaoke.transaction.tcc.api.context.BranchTransactionalContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.model.RequestContext.Biz.ApprovalFlow;

/**
 * Created by zhouwr on 2023/9/26.
 */
@Slf4j
@Component
@HandlerProvider(name = "defaultFlowCompletedActHandler")
public class DefaultFlowCompletedActHandler extends AbstractActTCCActionHandler<FlowCompletedActionHandler.Arg, FlowCompletedActionHandler.Result> {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private FlowCompletedActionServiceFacade flowCompletedActionServiceFacade;
    @Autowired
    private AuditLogServiceFacade auditLogServiceFacade;
    @Autowired
    private DataSnapshotLogicService dataSnapshotLogicService;
    @Autowired
    private SaveActionServiceFacadeImpl saveActionServiceFacade;

    @Override
    @TccTransactional(name = "handle", confirmMethod = "commit", cancelMethod = "rollback")
    public FlowCompletedActionHandler.Result handle(HandlerContext context, FlowCompletedActionHandler.Arg arg) {
        if (!arg.getInterfaceArg().isPass()) {
            updateDataLifeStatus(context, arg);
            recordLifeStatusModifyLog(context.getUser(), arg.data(), arg.getObjectDescribe(), ObjectDataExt.of(arg.dbData()).getLifeStatus());
            //只有编辑后才删除 日志
            if (Objects.equals(ApprovalFlowTriggerType.UPDATE, arg.getInterfaceArg().approvalFlowTriggerType())) {
                deleteDataSnapshot(context.getUser(), arg.getObjectApiName(), arg.getInterfaceArg().getDataId());
                deleteHiddenLog(context.getUser(), arg.getObjectApiName(), arg.getInterfaceArg().getDataId());
            }
            updateChangeOrderStatus(context, arg);
            return buildResult(false);
        }
        boolean needUpdateLifeStatus = doActionWithTriggerType(context, arg);
        // 更新变更单状态
        updateChangeOrderStatus(context, arg);
        if (needUpdateLifeStatus) {
            updateDataLifeStatus(context, arg);
            recordLifeStatusModifyLog(context.getUser(), arg.data(), arg.getObjectDescribe(), ObjectDataExt.of(arg.dbData()).getLifeStatus());
        }
        return buildResult(true);
    }

    @Override
    public boolean commit(BranchTransactionalContext branchTransactionalContext, HandlerContext context, FlowCompletedActionHandler.Arg arg) {
        return super.commit(branchTransactionalContext, context, arg);
    }

    @Override
    public boolean rollback(BranchTransactionalContext branchTransactionalContext, HandlerContext context, FlowCompletedActionHandler.Arg arg) {
        return super.rollback(branchTransactionalContext, context, arg);
    }

    private FlowCompletedActionHandler.Result buildResult(boolean writeDB) {
        FlowCompletedActionHandler.Result result = new FlowCompletedActionHandler.Result();
        result.setInterfaceResult(new StandardFlowCompletedAction.Result(true, writeDB));
        return result;
    }

    private void updateDataLifeStatus(HandlerContext context, FlowCompletedActionHandler.Arg arg) {
        User user = context.getUser();
        IObjectData data = arg.data();
        //驳回或撤回作废审批把last_life_status_before_invalid清空
        if (ApprovalFlowTriggerType.INVALID == arg.getInterfaceArg().approvalFlowTriggerType() && !arg.getInterfaceArg().isPass()) {
            serviceFacade.batchUpdateByFields(context.getUser(), Lists.newArrayList(data),
                    ObjectLifeStatus.LIFE_STATUS_AND_LAST_LIFE_STATUS_BEFORE_INVALID_FIELD);
        } else {
            serviceFacade.batchUpdateByFields(user, Lists.newArrayList(data), ObjectLifeStatus.LIFE_STATUS_FIELD);
        }
        //同步更新从对象的生命状态
        updateDetailObjectDataLifeStatus(arg, user, data);
    }

    private void updateDetailObjectDataLifeStatus(FlowCompletedActionHandler.Arg arg, User user, IObjectData data) {
        flowCompletedActionServiceFacade.updateDetailObjectDataLifeStatus(arg.getInterfaceArg().approvalFlowTriggerType(),
                user, data, arg.dbData(), arg.detailDataMap(), arg.detailDescribeMap());
    }

    private void recordLifeStatusModifyLog(User user, IObjectData data, IObjectDescribe describe, ObjectLifeStatus lastLifeStatus) {
        auditLogServiceFacade.recordLifeStatusModifyLog(user.getTenantId(), data, describe, lastLifeStatus);
    }

    private void deleteDataSnapshot(User user, String objectApiName, String dataId) {
        dataSnapshotLogicService.deleteSnapshot(user, objectApiName, dataId, ApprovalFlow.getCode(), RequestUtil.getOtherBizId());
    }

    private void deleteHiddenLog(User user, String objectApiName, String dataId) {
        RequestContext.BizInfo bizInfo = RequestUtil.getBizInfo();
        if (Objects.isNull(bizInfo) || Strings.isNullOrEmpty(bizInfo.getOtherBizId())) {
            return;
        }
        serviceFacade.deleteAuditLog(user.getTenantId(), objectApiName, dataId, bizInfo.getOtherBizId());
    }

    private void updateChangeOrderStatus(HandlerContext context, FlowCompletedActionHandler.Arg arg) {
        flowCompletedActionServiceFacade.updateChangeOrderStatus(context.getUser(), arg.getObjectDescribe(), arg.data(), arg.getInterfaceArg());
    }

    private boolean doActionWithTriggerType(HandlerContext context, FlowCompletedActionHandler.Arg arg) {
        boolean ret = true;
        ApprovalFlowTriggerType triggerType = arg.getInterfaceArg().approvalFlowTriggerType();
        switch (triggerType) {
            case UPDATE:
                doEditAction(context, arg);
                ret = false;
                break;
            case INVALID:
                doInvalidAction(context, arg);
                ret = false;
                break;
            default:
                break;
        }
        return ret;
    }

    private void doEditAction(HandlerContext context, FlowCompletedActionHandler.Arg arg) {
        ConvertRuleDataContainer convertRuleDataContainer = builderConvertRuleContainerFromCallBackData(context.getUser(), arg.getCallbackData());
        UpdateMasterAndDetailData.Arg updateArg = UpdateMasterAndDetailData.Arg.builder()
                .incrementUpdate(true)
                .incrementUpdateDetail(true)
                .updateIgnoreNotExistsData(true)
                .masterObjectData(arg.data())
                .toUpdateMap(arg.getEditActionInfo().getUpdatedFieldMap().getOrDefault(arg.getObjectApiName(),
                        Collections.emptyMap()).get(arg.getInterfaceArg().getDataId()))
                .detailsToAdd(ObjectDataDocument.ofDataMap(arg.getEditActionInfo().getDetailsToAdd()))
                .detailsToUpdate(ObjectDataDocument.ofDataMap(arg.getEditActionInfo().getDetailsToUpdate()))
                .detailsToInvalid(ObjectDataDocument.ofDataMap(arg.getEditActionInfo().getDetailsToInvalid()))
                .detailsToDelete(ObjectDataDocument.ofDataMap(arg.getEditActionInfo().getDetailsToDelete()))
                .convertRuleDataContainer(convertRuleDataContainer)
                .actionType("flowCompletedHandler")
                .build();
        serviceFacade.updateMasterAndDetailData(context.getUser(), updateArg);
        //同步更新从对象的生命状态
        updateDetailObjectDataLifeStatus(arg, context.getUser(), arg.data());
    }

    private ConvertRuleDataContainer builderConvertRuleContainerFromCallBackData(User user, Map<String, Object> callbackData) {
        Object o = callbackData.get(ExtraDataKeys.OPTION_INFO);
        BaseObjectSaveAction.OptionInfo optionInfo = JSON.parseObject(JSON.toJSONString(o), BaseObjectSaveAction.OptionInfo.class);
        if (Objects.nonNull(optionInfo) && optionInfo.isFromReferenceCreate()) {
            return saveActionServiceFacade.buildConvertRuleDataContainer(user, optionInfo);
        }
        return null;
    }

    private void doInvalidAction(HandlerContext context, FlowCompletedActionHandler.Arg arg) {
        //作废主对象
        serviceFacade.bulkInvalid(Lists.newArrayList(arg.data()), context.getUser());
        //2021/12/17 作废后执行按钮上的current动作
        RuntimeException exception = null;
        ActionContext actionContext = new ActionContext(context.getRequestContext(), arg.getObjectApiName(), StandardAction.FlowCompleted.name());
        try {
            infraServiceFacade.doCurrentAction(arg.getCallbackData(), actionContext, arg.getObjectDescribe(),
                    arg.data(), arg.detailDataMap());
        } catch (RuntimeException e) {
            log.warn("doCurrentAction fail", e);
            exception = e;
        }
        //批量作废从对象
        if (CollectionUtils.notEmpty(arg.getDetailDataMap())) {
            arg.detailDataMap().forEach((detailApiName, detailDataList) -> serviceFacade.bulkInvalid(detailDataList, context.getUser()));
        }
        dealGdpr(context.getUser(), arg.getObjectApiName(), Lists.newArrayList(arg.data()));
        if (Objects.nonNull(exception)) {
            throw exception;
        }
    }

    private void dealGdpr(User user, String describeApiName, List<IObjectData> dataList) {
        List<String> ids = dataList.stream().map(IObjectData::getId).collect(Collectors.toList());
        ParallelUtils.createBackgroundTask()
                .submit(() -> infraServiceFacade.bulkInvalidGdprLegalBase(user, describeApiName, ids))
                .run();
    }
}
