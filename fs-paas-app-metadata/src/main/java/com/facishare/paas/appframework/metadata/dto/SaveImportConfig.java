package com.facishare.paas.appframework.metadata.dto;

import lombok.Data;

import java.util.Objects;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/07/14
 */
public interface SaveImportConfig {

    @Data
    class Arg {
        private ImportConfig insert;
        private ImportConfig update;
        private ImportConfig insertPreProcessing;
        private ImportConfig updatePreProcessing;
        private ImportSetting insertImport;
        private ImportSetting updateImport;
        private ImportReferenceMapping importReferenceMapping;

        public ImportConfig getInsert() {
            if (Objects.isNull(insert)) {
                return null;
            }
            insert.setDefineType(ImportConfig.IMPORT);
            insert.setImportType(ImportConfig.INSERT);
            return insert;
        }

        public ImportConfig getUpdate() {
            if (Objects.isNull(update)) {
                return null;
            }
            update.setDefineType(ImportConfig.IMPORT);
            update.setImportType(ImportConfig.UPDATE);
            return update;
        }

        public ImportConfig getInsertPreProcessing() {
            if (Objects.isNull(insertPreProcessing)) {
                return null;
            }
            insertPreProcessing.setDefineType(ImportConfig.IMPORT_PRE_PROCESSING);
            insertPreProcessing.setImportType(ImportConfig.INSERT);
            return insertPreProcessing;
        }

        public ImportConfig getUpdatePreProcessing() {
            if (Objects.isNull(updatePreProcessing)) {
                return null;
            }
            updatePreProcessing.setDefineType(ImportConfig.IMPORT_PRE_PROCESSING);
            updatePreProcessing.setImportType(ImportConfig.UPDATE);
            return updatePreProcessing;
        }

        public ImportSetting getInsertImport() {
            if (Objects.isNull(insertImport)) {
                return null;
            }
            return insertImport;
        }

        public ImportSetting getUpdateImport() {
            if (Objects.isNull(updateImport)) {
                return null;
            }
            return updateImport;
        }

    }

    class Result {

    }
}
