package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.appframework.core.predef.service.dto.resource.ModifyResource
import com.facishare.paas.appframework.metadata.mtresource.model.ConfigurationPackageResource
import joptsimple.internal.Strings
import spock.lang.Specification

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/18
 */
class ObjectResourceServiceTest extends Specification {

    def "build modify_resource arg"() {
        when:
        def resources = makeFieldResources(tenantId, describeApiName, fieldNames)
        resources.addAll(makeDescribeResources(tenantId, describeApiName))
        then:
        def result = new ModifyResource.Arg("resources": resources)
        println(JacksonUtils.toJson(result))
        noExceptionThrown()
        where:
        tenantId | describeApiName   | fieldNames
        "74255"  | "object_2mmga__c" | ["name", "field_jH19d__c"]
    }

    def makeFieldResources(def tenantId, def describeApiName, def fieldNames) {
        return fieldNames.collect {
            def resourceValues = makeResourceValues(tenantId, describeApiName, it)
            return makeConfigurationPackageResource("mt_field", resourceValues)
        }
    }

    def makeConfigurationPackageResource(def resourceType, def resourceValues) {
        return new ConfigurationPackageResource("resourceType": resourceType, "resourceValues": resourceValues, "controlLevel": "0", "sourceType": "brush_data")
    }

    def makeResourceValues(def tenantId, def describeApiName, def fieldName = null) {
        def values = [ConfigurationPackageResource.ResourceValue.of("tenant_id", tenantId),
                      ConfigurationPackageResource.ResourceValue.of("describe_api_name", describeApiName)]
        if (!Strings.isNullOrEmpty(fieldName)) {
            values.add(ConfigurationPackageResource.ResourceValue.of("api_name", fieldName))
        }
        return values
    }

    def makeDescribeResources(def tenantId, def describeApiName) {
        def resourceValues = makeResourceValues(tenantId, describeApiName)
        return makeConfigurationPackageResource("mt_describe", resourceValues)
    }


    def "build modify_resource arg 2"() {
        when:
        def describeFieldMap = JacksonUtils.fromJson(json, Map)
        def resultList = []
        describeFieldMap.each { describeApiName, fieldNames ->
            def resources = makeFieldResources(tenantId, describeApiName, fieldNames)
            def result = new ModifyResource.Arg("resources": resources)
            resultList.addAll(result)
            println(makeCurlCmd(tenantId, JacksonUtils.toJson(result)))
        }
        then:
//        println(JacksonUtils.toJson(resultList))
        noExceptionThrown()
        where:
        tenantId | json
        "40080116"  | '''{"currency__c": ["digital_code__c", "another_name__c", "currency_code__c", "display_name", "currency_name__c", "data_status__c", "name"], "operation_org__c": ["registration_type__c", "property_code__c", "organizer__c", "cmhk_business_type__c", "english_name__c", "operation_state__c", "lastmodifyrecordtime__c", "property_status__c", "display_name", "full_name__c", "name"], "processing_records__c": ["data_identification__c", "Solution__c", "Processing_Records__c", "Processing_time__c", "Problem_Feedback_Form__c", "Processed_by__c", "name"], "country__c": ["chinese_full_name__c", "trading_platform_country_encode__c", "trading_platform_country_name__c", "chinese_simple_name__c", "numeric_code__c", "national_flag__c", "three_character_code__c", "two_character_code__c", "display_name", "english_full_name__c", "international_telephone_area_code__c", "english_simple_name__c", "data_status__c", "name"], "contract_plan__c": ["plan_no__c", "crm_contract_number__c", "implementing_date__c", "remind__c", "contract_plan_id__c", "contract_terms__c", "reminder__c", "name"], "contract_estimate__c": ["counterpart_name__c", "estimate_level__c", "estimate_no__c", "crm_contract_number__c", "contract_code__c", "contract_plan_id__c", "name", "estimate_desc__c"], "contract_implement__c": ["crm_contract_number__c", "completion_basis__c", "is_plan__c", "implementing_date__c", "remind__c", "implement_no__c", "contract_plan_id__c", "notes__c", "contract_terms__c", "reality_implementing_time__c", "reminder__c", "name"], "collectionTerms__c": ["collectionTerm_name__c", "nature_payment_name__c", "crm_contract_number__c", "collection_amount__c", "collectionTerm_explain__c", "collectionTerm_date__c", "name", "Amount_ratio__c"], "counterparts__c": ["counterpart_name__c", "bank_name__c", "contract__c", "account_name__c", "account_number__c", "name"], "ContractSignObj__c": ["expectedStart_date__c", "contract__c", "expectedEnd_date__c", "reminder__c", "name"], "contract_issues__c": ["issues_startDate__c", "issues_endDate__c", "issues_type__c", "crm_contract_number__c", "payment_amount__c", "issues_desc__c", "issues_file__c", "name"], "customer_fin__c": ["bank_account_number__c", "bank_name__c", "remark__c", "telephone__c", "address_english__c", "other_code__c", "fax__c", "address__c", "bank_english_name__c", "conutry__c", "city__c", "remittance_route_number__c", "province__c", "display_name", "bank_international_code__c", "bank_code__c", "data_status__c", "name"], "AccountObj": ["account_type", "province", "mainaccount_opinion__c", "risk_list_level_infor__c", "customer_code__c", "biz_reg__c", "registered_capital__c", "risk_list_level__c", "create_org__c", "registered_capital_currencye__c", "city", "customer_registered_address__c", "region__c", "uniform_social_credit_code", "customer_establish_date__c", "legal_representative", "registered_capital", "groupId__c", "customer_is_valid__c", "country", "categorycode__c", "identification_information__c", "account_main_data_id", "district", "name", "customer_is_strategy__c", "mainaccount_audittime__c", "remark", "caseNo__c", "categoryname__c", "mainaccount_auditflag__c", "rick_approval_status__c"], "AccountMainDataObj": ["account_type", "mainaccount_registered_address__c", "identification_information__c", "mainaccount_opinion__c", "mainaccount_establish_date__c", "customer_code__c", "field_pv6mC__c", "mainaccount_region__c", "name", "create_org__c", "customer_is_strategy__c", "mainaccount_audittime__c", "remark", "mainaccount_categorycode__c", "mainaccount_biz_reg__c", "mainaccount_mdm_infor__c", "field_58P22__c", "uniform_social_credit_code", "field_19wx0__c", "legal_representative", "mainaccount_registered_capital__c", "mainaccount_auditflag__c", "registered_capital", "field_uj20P__c", "customer_is_valid__c", "mainaccount_categoryname__c", "mainaccount_mdm_datetime__c"], "user_login_information__c": ["tenant_personnel_combination__c", "module_name__c", "dept_level1__c", "business_organization__c", "user_name__c", "name", "personnel_information_schedule__c", "dept_level2__c", "user_coding__c", "field_WuryP__c", "system_name__c", "login_date__c", "belong_business_organization__c", "tenant_id__c", "vip_or_not__c", "tenant_name__c", "first_department_code__c", "subordinate_department_code__c"], "indicator_data__c": ["number_of_daily_active_users__c", "weekly_user_login_count__c", "module_name__c", "get_time__c", "users_logged_in__c", "instance_name__c", "contract_quantity_incremental__c", "name_of_secondary_sector__c", "associate_monthly_collection_objects__c", "business_opportunities_complete__c", "customer_data_complete__c", "name", "total_number_of_business_opportunities_this_week__c", "last_week_total_number_of_customer_master_data__c", "total_customer_master_data_for_this_week__c", "customer_data_Incremental__c", "total_number_of_customers_this_week__c", "last_week_total_number_of_business_opportunitie__c", "data_date__c", "total_daily_logins__c", "user_subscription_volume__c", "last_week_total_number_of_sales_contracts__c", "source_name__c", "business_opportunities_incremental__c", "last_week_total_number_of_customers__c", "renter_name__c", "weekly_user_subscriptions__c", "total_number_of_user_logins_number__c", "total_sales_contracts_for_this_week__c", "tenant_name__c", "users_authorized_number__c", "weekly_active_users__c", "customer_masterdata_complete__c", "total_number_of_user_logins__c", "contract_quantity_complete__c", "customer_masterdata_Incremental__c"], "monthly_indicator_data__c": ["month_end_customer_data__c", "year__c", "monthly_active_users__c", "module_name__c", "get_time__c", "monthly_login_users__c", "instance_name__c", "contract_quantity_incremental__c", "name", "month_end_customer_master_data__c", "month_end_authorization_quantity__c", "month_end_business_opportunity_data__c", "customer_data_Incremental__c", "data_date__c", "monthly_opening_volume__c", "business_opportunities_incremental__c", "month_end_contract_data__c", "tenant_name__c", "customer_masterdata_Incremental__c", "month__c"], "org__c": ["parent_db__c", "hr_org_type__c", "parent_id__c", "dept_code__c", "is_create_personnel__c", "buss_unit__c", "org_leader_name__c", "org_paths__c", "new_results__c", "hr_expiry_date__c", "org_type__c", "eng_name__c", "org_desc__c", "org_leader_id__c", "name", "hr_order_num__c", "short_name__c", "hr_org_status__c", "cmp_level__c", "eng_short_name__c", "ps_org_id_back__c", "eff_date__c", "buss_sys_id__c"], "staff__c": ["effective_date__c", "person_code__c", "new_personnel_results__c", "gender__c", "person_type__c", "business_phone__c", "group_organization_name__c", "post_name__c", "full_name__c", "if_main_post__c", "position__c", "name_spell__c", "data_status__c", "organization_code__c", "name", "email__c"], "object_orgdetailed__c": ["effective_date__c", "main_department_BI__c", "person_code__c", "status__c", "Per_info_table__c", "tenant_personnel_combination__c", "First_department__c", "main_sub_department__c", "is_active__c", "business_name__c", "tenant_id__c", "subsidiary_department__c", "tenant_name__c", "user_name__c", "organization_code__c", "name"], "NewOpportunityObj": ["close_date", "name", "collaborationDept__c", "collaborator__c", "collaborationType__c", "amount", "account_id", "account_id__c", "groupId__c"], "work_order__c": ["unified_construction__c", "field_qx1Kc__c", "demand_priority__c", "expected_resolution_date__c", "requirement_classification__c", "annex__c", "problem_detailed_description__c", "problem_classification__c", "source_id__c", "memo__c", "problem_subclass__c", "finish_time__c", "IsSolve__c", "work_order_souce_id__c", "processing_time__c", "name", "satisfaction_evaluation__c", "is_deal__c", "urgency__c", "process_time_value__c", "Put_forward_person__c", "ProcessingStatus__c", "opinions_and_suggestions__c", "OPSystem__c", "treatment_level__c", "related_modules_involved__c", "Usingdevices__c", "Solution__c", "question_title__c", "screenshot_processing_result__c", "picture__c", "contact_number__c", "processing_records__c", "currentP__c", "is_handled_group__c"], "SaleContractObj": ["change_reason__c", "collectionTerms__c", "contract_file_addr__c", "effective_date__c", "seal_desc__c", "renewsList__c", "contract_copies__c", "handling_unit__c", "law_cont_id__c", "payment_amount__c", "currency__c", "quote_id", "contract_archived_time__c", "exchange_rate__c", "base_unexecuted_amount", "contract_urgency__c", "contract_security__c", "handling_department__c", "contract_sign_addr__c", "decisionSupportFile__c", "contract_paper_code__c", "money_flow__c", "payment_method_desc__c", "termination_date__c", "contract_details_addr__c", "cont_change_flag__c", "original_contract_name__c", "clarify_payment_amount__c", "approval_document_type__c", "amount_description__c", "contract_code__c", "file_desc__c", "unexecuted_amount", "fcontract_file__c", "contract_draft_url__c", "contract_security_unit__c", "account_id", "contract_term__c", "file_date__c", "winning_date__c", "final_approval_authority__c", "contract_name__c", "excluding_taxe_payment_amount_tax__c", "iscopy__c", "is_renew__c", "file__c", "base_executed_amount", "representative_sign__c", "our_signatory__c", "counterparty_signatory__c", "explanation_term_desc__c", "base_amount", "contract_subcategories__c", "other_contract_holdingDept__c", "contract_issues_addr__c", "contract_status__c", "contract_estimate_addr__c", "countContractEvaluation__c", "contract_archived_dept__c", "scanned_copies__c", "implement_list_url__c", "contract_time", "counterparts__c", "contract_archived__c", "name", "change_desc__c", "payment_method__c", "original_contract_code__c", "contract_title__c", "excluding_taxe_payment_amount__c", "contract_implement_addr__c", "signing_date__c", "change_basis__c", "executed_amount", "partner_id", "handled_by__c", "major_contracts__c", "seal_count__c", "contract_seal_addr__c", "out_resources", "opportunity_name__c", "amount", "account_name__c", "contract_subject__c", "contract_plan_addr__c", "cre_status__c", "handling_date__c", "contract_category__c", "handling_contact_information__c", "RelatedContract__c"], "division__c": ["division_level__c", "superior_encode__c", "division_english_short_name__c", "division_desc__c", "display_name", "division_english_name__c", "telephone_area_code__c", "country__c", "data_status__c", "division_name__c", "name"], "requirement__c": ["unified_construction__c", "demand_priority__c", "expected_resolution_date__c", "requirement_classification__c", "related_modules_involved__c", "problem_feedback__c", "related_attachments__c", "version__c", "requirement_itle__c", "requirement_status__c", "needs_assessment_opinions__c", "contact_number__c", "requirement_proposal_date__c", "name", "detailed_description__c"], "object_org__c": ["parent_name__c", "parent_id__c", "property_code__c", "status__c", "plate__c", "dept_id__c", "dept_children__c", "hr_org_code__c", "org_paths__c", "is_online__c", "dept_name__c", "tenant_id__c", "tenant_name__c", "name", "departmental_ancestor_path__c", "org_level__c"], "ExchangeRate__c": ["ER_OSystemID__c", "ER_TC_Number__c", "ER_Accuracy__c", "ER_Number__c", "ER_Org__c", "ER_Valid_Date__c", "ER_OC_Number__c", "ER_Type__c", "ER_Value__c", "name", "ER_Conversion__c"]}'''
    }

    def makeCurlCmd(def tenantId, def argJson) {
        def curCmd = """curl -i -X POST \\
   -H "x-fs-userInfo:-10000" \\
   -H "x-fs-ei:${tenantId}" \\
   -H "Content-Type:application/json" \\
   -d \\
'${argJson}' \\
 'http://localhost/API/v1/rest/object/resource/service/modify_resource' >> out.log"""
    }

    def "build modify_resource arg 3"() {
        when:
        def describeFieldMap = JacksonUtils.fromJson(json, Map)
        def resourceList = []
        describeFieldMap.each { describeApiName, fieldNames ->
            def resources = makeFieldResources(tenantId, describeApiName, fieldNames)
            resourceList.addAll(resources)
        }
        then:
        def result = new ModifyResource.Arg("resources": resourceList)
        println(makeCurlCmd(tenantId,JacksonUtils.toJson(result)))
        noExceptionThrown()
        where:
        tenantId | json
        "********"  | '''{"SaleContractObj": ["contract_name__c"], "AccountObj": ["account_type", "mainaccount_opinion__c", "risk_list_level_infor__c", "customer_code__c", "biz_reg__c", "risk_list_level__c", "region__c", "uniform_social_credit_code", "groupId__c", "customer_is_valid__c", "country", "name", "customer_is_strategy__c", "mainaccount_audittime__c", "categoryname__c", "mainaccount_auditflag__c", "opinion__c", "audittime__c", "auditflag__c"], "AccountMainDataObj": ["account_type", "identification_information__c", "mainaccount_opinion__c", "customer_code__c", "mainaccount_region__c", "name", "customer_is_strategy__c", "mainaccount_audittime__c", "mainaccount_biz_reg__c", "field_58P22__c", "uniform_social_credit_code", "mainaccount_auditflag__c", "customer_is_valid__c", "mainaccount_categoryname__c"], "NewOpportunityObj": ["name", "collaborationDept__c", "account_id__c", "collaborator__c", "collaborationType__c", "account_id", "groupId__c"]}'''
    }
}
