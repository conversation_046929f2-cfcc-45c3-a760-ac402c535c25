package com.facishare.paas.appframework.metadata.layout.component;

import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class SuspendedActionInfoTest {

  private SuspendedActionInfo suspendedActionInfo;

  @BeforeEach
  void setUp() {
    suspendedActionInfo = new SuspendedActionInfo();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试SuspendedActionInfo的无参构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试无参构造函数")
  void testDefaultConstructor() {
    SuspendedActionInfo info = new SuspendedActionInfo();
    assertNotNull(info, "无参构造函数创建的对象不应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试SuspendedActionInfo的Map构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = Maps.newHashMap();
    map.put(SuspendedActionInfo.TYPE, "create");
    map.put(SuspendedActionInfo.URL, "http://example.com/create");
    map.put(SuspendedActionInfo.RELATED_OBJECT_API_NAME, "Account");
    map.put(SuspendedActionInfo.RELATED_OBJECT_RECORD_TYPE, "standard");
    
    SuspendedActionInfo info = new SuspendedActionInfo(map);
    assertNotNull(info, "Map构造函数创建的对象不应为空");
    assertEquals("create", info.getType(), "应正确设置类型");
    assertEquals("http://example.com/create", info.getUrl(), "应正确设置URL");
    assertEquals("Account", info.getRelatedObjectApiName(), "应正确设置相关对象API名称");
    assertEquals("standard", info.getRelatedObjectRecordType(), "应正确设置相关对象记录类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getType和setType方法
   */
  @Test
  @DisplayName("正常场景 - 测试Type属性")
  void testType() {
    // 初始状态应为null
    assertNull(suspendedActionInfo.getType(), "初始类型应为null");
    
    // 设置类型
    suspendedActionInfo.setType("edit");
    assertEquals("edit", suspendedActionInfo.getType(), "应正确设置类型");
    
    // 设置为null
    suspendedActionInfo.setType(null);
    assertNull(suspendedActionInfo.getType(), "应支持设置为null");
    
    // 设置为空字符串
    suspendedActionInfo.setType("");
    assertEquals("", suspendedActionInfo.getType(), "应支持设置为空字符串");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getUrl和setUrl方法
   */
  @Test
  @DisplayName("正常场景 - 测试Url属性")
  void testUrl() {
    // 初始状态应为null
    assertNull(suspendedActionInfo.getUrl(), "初始URL应为null");
    
    // 设置URL
    suspendedActionInfo.setUrl("https://example.com/action");
    assertEquals("https://example.com/action", suspendedActionInfo.getUrl(), "应正确设置URL");
    
    // 设置为null
    suspendedActionInfo.setUrl(null);
    assertNull(suspendedActionInfo.getUrl(), "应支持设置为null");
    
    // 设置为空字符串
    suspendedActionInfo.setUrl("");
    assertEquals("", suspendedActionInfo.getUrl(), "应支持设置为空字符串");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getRelatedObjectApiName方法
   */
  @Test
  @DisplayName("正常场景 - 测试getRelatedObjectApiName方法")
  void testGetRelatedObjectApiName() {
    // 初始状态应为null
    assertNull(suspendedActionInfo.getRelatedObjectApiName(), "初始相关对象API名称应为null");
    
    // 通过Map构造函数设置相关对象API名称
    Map<String, Object> map = Maps.newHashMap();
    map.put(SuspendedActionInfo.RELATED_OBJECT_API_NAME, "Contact");
    SuspendedActionInfo info = new SuspendedActionInfo(map);
    assertEquals("Contact", info.getRelatedObjectApiName(), "应正确获取相关对象API名称");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getRelatedObjectRecordType方法
   */
  @Test
  @DisplayName("正常场景 - 测试getRelatedObjectRecordType方法")
  void testGetRelatedObjectRecordType() {
    // 初始状态应为null
    assertNull(suspendedActionInfo.getRelatedObjectRecordType(), "初始相关对象记录类型应为null");
    
    // 通过Map构造函数设置相关对象记录类型
    Map<String, Object> map = Maps.newHashMap();
    map.put(SuspendedActionInfo.RELATED_OBJECT_RECORD_TYPE, "custom");
    SuspendedActionInfo info = new SuspendedActionInfo(map);
    assertEquals("custom", info.getRelatedObjectRecordType(), "应正确获取相关对象记录类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试常量定义
   */
  @Test
  @DisplayName("正常场景 - 测试常量定义")
  void testConstants() {
    assertEquals("type", SuspendedActionInfo.TYPE, "TYPE常量应正确");
    assertEquals("url", SuspendedActionInfo.URL, "URL常量应正确");
    assertEquals("new_object", SuspendedActionInfo.NEW_OBJECT, "NEW_OBJECT常量应正确");
    assertEquals("related_object_api_name", SuspendedActionInfo.RELATED_OBJECT_API_NAME, 
                 "RELATED_OBJECT_API_NAME常量应正确");
    assertEquals("related_object_record_type", SuspendedActionInfo.RELATED_OBJECT_RECORD_TYPE, 
                 "RELATED_OBJECT_RECORD_TYPE常量应正确");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口实现
   */
  @Test
  @DisplayName("正常场景 - 测试接口实现")
  void testInterfaceImplementation() {
    // 验证实现了JsonCompatible接口
    assertTrue(suspendedActionInfo instanceof com.facishare.paas.metadata.api.JsonCompatible, 
               "应实现JsonCompatible接口");
    
    // 验证继承自DocumentBasedBean
    assertTrue(suspendedActionInfo instanceof com.facishare.paas.metadata.impl.DocumentBasedBean, 
               "应继承自DocumentBasedBean");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试继承的DocumentBasedBean功能
   */
  @Test
  @DisplayName("正常场景 - 测试继承功能")
  void testInheritedFunctionality() {
    // 通过Map构造函数测试继承的功能
    Map<String, Object> map = Maps.newHashMap();
    map.put("custom_field", "custom_value");
    map.put(SuspendedActionInfo.TYPE, "delete");
    
    SuspendedActionInfo info = new SuspendedActionInfo(map);
    assertEquals("delete", info.getType(), "应正确处理类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试完整的悬浮操作配置
   */
  @Test
  @DisplayName("正常场景 - 测试完整悬浮操作配置")
  void testCompleteSuspendedActionConfiguration() {
    // 设置完整的悬浮操作配置
    suspendedActionInfo.setType("new_object");
    suspendedActionInfo.setUrl("/app/create/Account");
    
    // 创建包含所有字段的配置
    Map<String, Object> map = Maps.newHashMap();
    map.put(SuspendedActionInfo.TYPE, "new_object");
    map.put(SuspendedActionInfo.URL, "/app/create/Account");
    map.put(SuspendedActionInfo.RELATED_OBJECT_API_NAME, "Account");
    map.put(SuspendedActionInfo.RELATED_OBJECT_RECORD_TYPE, "business");
    
    SuspendedActionInfo completeInfo = new SuspendedActionInfo(map);
    
    // 验证完整配置
    assertEquals("new_object", completeInfo.getType(), "类型应正确");
    assertEquals("/app/create/Account", completeInfo.getUrl(), "URL应正确");
    assertEquals("Account", completeInfo.getRelatedObjectApiName(), "相关对象API名称应正确");
    assertEquals("business", completeInfo.getRelatedObjectRecordType(), "相关对象记录类型应正确");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试多种操作类型
   */
  @Test
  @DisplayName("正常场景 - 测试多种操作类型")
  void testVariousActionTypes() {
    String[] actionTypes = {"create", "edit", "delete", "view", "clone", "new_object"};
    
    for (String actionType : actionTypes) {
      suspendedActionInfo.setType(actionType);
      assertEquals(actionType, suspendedActionInfo.getType(), "应正确设置操作类型: " + actionType);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试URL格式
   */
  @Test
  @DisplayName("正常场景 - 测试URL格式")
  void testUrlFormats() {
    String[] urls = {
        "http://example.com/action",
        "https://secure.example.com/action",
        "/relative/path/action",
        "/app/create/Object",
        "/app/edit/Object/123",
        "javascript:void(0)"
    };
    
    for (String url : urls) {
      suspendedActionInfo.setUrl(url);
      assertEquals(url, suspendedActionInfo.getUrl(), "应正确设置URL格式: " + url);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试特殊字符处理
   */
  @Test
  @DisplayName("边界场景 - 测试特殊字符处理")
  void testSpecialCharacters() {
    // 测试包含特殊字符的类型和URL
    String specialType = "action_with_@#$%_chars";
    String specialUrl = "/app/action?param=value&other=test";
    String specialApiName = "Custom_Object__c";
    
    Map<String, Object> map = Maps.newHashMap();
    map.put(SuspendedActionInfo.TYPE, specialType);
    map.put(SuspendedActionInfo.URL, specialUrl);
    map.put(SuspendedActionInfo.RELATED_OBJECT_API_NAME, specialApiName);
    
    SuspendedActionInfo info = new SuspendedActionInfo(map);
    
    assertEquals(specialType, info.getType(), "应正确处理特殊字符类型");
    assertEquals(specialUrl, info.getUrl(), "应正确处理特殊字符URL");
    assertEquals(specialApiName, info.getRelatedObjectApiName(), "应正确处理特殊字符API名称");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试空值和边界值
   */
  @Test
  @DisplayName("边界场景 - 测试空值和边界值")
  void testEmptyAndBoundaryValues() {
    // 测试空字符串
    suspendedActionInfo.setType("");
    suspendedActionInfo.setUrl("");
    
    assertEquals("", suspendedActionInfo.getType(), "应支持空字符串类型");
    assertEquals("", suspendedActionInfo.getUrl(), "应支持空字符串URL");
    
    // 测试长字符串
    String longType = "very_long_action_type_name_that_exceeds_normal_length_expectations";
    String longUrl = "https://very-long-domain-name.example.com/very/long/path/to/action/endpoint";
    
    suspendedActionInfo.setType(longType);
    suspendedActionInfo.setUrl(longUrl);
    
    assertEquals(longType, suspendedActionInfo.getType(), "应支持长字符串类型");
    assertEquals(longUrl, suspendedActionInfo.getUrl(), "应支持长字符串URL");
  }
}
