package com.facishare.paas.appframework.metadata.reference;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ref.RefMessage.ActionType;
import com.facishare.paas.appframework.core.model.ref.RefMessage.CreateArg;
import com.facishare.paas.appframework.core.model.ref.RefMessage.DeleteArg;
import com.facishare.paas.appframework.core.model.ref.RefMessage.Ref;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.core.util.WhereUsedUtil;
import com.facishare.paas.appframework.core.util.WhereUsedUtil.SOURCE_MATCH_TYPE;
import com.facishare.paas.appframework.metadata.AutoNumberExt;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.Filter;
import com.facishare.paas.appframework.metadata.dto.Wheres;
import com.facishare.paas.appframework.metadata.expression.Expression;
import com.facishare.paas.appframework.metadata.expression.ExpressionVariableFactory.ObjectFieldVar;
import com.facishare.paas.appframework.metadata.expression.ExpressionVariableFactory.RefObjectFieldVar;
import com.facishare.paas.metadata.api.GroupField;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.api.describe.Currency;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.reference.service.EntityReferenceService;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Pattern;


@Slf4j
public class FieldRef {

    /**
     * 应用方（source）类型，对应配置文件
     */
    public static final String REF_TYPE_VAL = "field";

    /**
     * 对象.字段
     */
    public static final String REF_DISPLAY_TYPE_VAL = "o.f";

    private List<CreateArg> createArgs = Lists.newArrayList();
    private List<DeleteArg> deleteArgs = Lists.newArrayList();

    /**
     * 用来去重
     */
    private final Map<String, Set<String>> distinctUsedObj2Field = Maps.newHashMap();

    private FieldRef() {
    }

    public static Ref buildRefByField(ActionType actionType, IFieldDescribe sourceFieldDesc, IObjectDescribe sourceObjDesc,
                                      Map<String, IObjectDescribe> refObjDesMap) {
        FieldRef fieldRef = new FieldRef();
        return fieldRef.buildRefs(actionType, sourceFieldDesc, sourceObjDesc, refObjDesMap);
    }

    public static List<Ref> buildRefByField(ActionType actionType, List<IFieldDescribe> sourceFieldDescList, IObjectDescribe sourceObjDesc,
                                            Map<String, IObjectDescribe> refObjDesMap) {
        List<Ref> refList = Lists.newArrayList();
        for (IFieldDescribe sourceFieldDesc : sourceFieldDescList) {
            Ref ref = buildRefByField(actionType, sourceFieldDesc, sourceObjDesc, refObjDesMap);
            if (Objects.nonNull(ref)) {
                refList.add(ref);
            }
        }
        return refList;
    }

    private void appendCreateArg(IObjectDescribe sourceObjDesc, IFieldDescribe sourceFieldDesc,
                                 IObjectDescribe targetObjDesc, IFieldDescribe targetFieldDesc) {

        if (Objects.isNull(createArgs)) {
            createArgs = Lists.newArrayList();
        }

        if (ObjectUtils.anyNull(sourceObjDesc, sourceFieldDesc, targetObjDesc, targetFieldDesc)) {
            return;
        }

        // 大前提： targetFieldDesc 必须是自定义字段 因为只有自定义字段才能查被用在何处
        if (!FieldDescribeExt.of(targetFieldDesc).isCustomField()) {
            return;
        }

        // 使用了部分对象的字段不需要记录
        if (skipByTargetObj(targetObjDesc.getApiName())) {
            return;
        }

        // 去重 ：！！！目前 source 内容都一致，不需要根据source去重
        Set<String> distinctField = distinctUsedObj2Field.computeIfAbsent(targetObjDesc.getApiName(), fields -> Sets.newHashSet());
        if (!distinctField.add(targetFieldDesc.getApiName())) {
            // 存在直接跳过
            return;
        }

        CreateArg createArg = CreateArg.builder()
                .refType(REF_TYPE_VAL)
                .sourceDisplayType(REF_DISPLAY_TYPE_VAL)
                .sourceValue(WhereUsedUtil.buildSourceValue(sourceObjDesc.getApiName(), sourceFieldDesc.getApiName()))
                .sourceLabel(WhereUsedUtil.buildSourceLabel(sourceObjDesc.getDisplayName(), sourceFieldDesc.getLabel()))
                .targetType(EntityReferenceService.DESCRIBE_FIELD_TYPE)
                .targetValue(WhereUsedUtil.buildTargetValue(targetObjDesc.getApiName(), targetFieldDesc.getApiName()))
                .targetLabel(WhereUsedUtil.buildTargetLabel(targetObjDesc.getDisplayName(), targetFieldDesc.getLabel()))
                .build();
        createArgs.add(createArg);
    }

    private void buildDefaultDeleteArg(IObjectDescribe sourceObjDesc, IFieldDescribe sourceFieldDesc,
                                       IObjectDescribe targetObjDesc, IFieldDescribe targetFieldDesc) {
        if (Objects.isNull(deleteArgs)) {
            deleteArgs = Lists.newArrayList();
        }

        if (ObjectUtils.anyNull(sourceObjDesc, sourceFieldDesc)) {
            return;
        }
        DeleteArg deleteArg = DeleteArg.builder()
                .refType(REF_TYPE_VAL)
                .archivedSourceDisplayTypes(RefFieldService.ARCHIVED_TYPE_MAP.get(REF_TYPE_VAL))
                .sourceMatchType(SOURCE_MATCH_TYPE.EQ.name())
                .sourceValue(WhereUsedUtil.buildSourceValue(sourceObjDesc.getApiName(), sourceFieldDesc.getApiName()))
                .build();
        deleteArgs.add(deleteArg);

    }

    private Ref buildRefs(ActionType actionType, IFieldDescribe sourceFieldDesc, IObjectDescribe sourceObjDesc, Map<String, IObjectDescribe> refObjDesMap) {

        try {
            if (Objects.equals(actionType, ActionType.CREATE) || Objects.equals(actionType, ActionType.DELETE_AND_CREATE)) {
                getCreateRefsByFieldWhere(sourceFieldDesc, sourceObjDesc, refObjDesMap);
                getCreateRefsByFieldRelatedWheres(sourceFieldDesc, sourceObjDesc, refObjDesMap);
                getCreateRefsByExpression(sourceFieldDesc, sourceObjDesc, refObjDesMap);
                getCreateRefsByCountField(sourceFieldDesc, sourceObjDesc, refObjDesMap);
                getCreateRefsByFieldWatermark(sourceFieldDesc, sourceObjDesc, refObjDesMap);
            }
            if (Objects.equals(actionType, ActionType.DELETE) || Objects.equals(actionType, ActionType.DELETE_AND_CREATE)) {
                buildDefaultDeleteArg(sourceObjDesc, sourceFieldDesc, null, null);
            }
        } catch (Throwable e) {
            log.warn("buildRefs fail, sourceField:{}, sourceObj:{}, tenant:{}", sourceFieldDesc.getApiName(), sourceObjDesc.getApiName(), sourceObjDesc.getTenantId());
            return null;
        }
        return Ref.builder().action(actionType.getCode()).createArgs(createArgs).deleteArgs(deleteArgs).tenantId(sourceObjDesc.getTenantId()).build();
    }


    /**
     * 字段属性：where
     * 第 1 类：
     * 数据作为选项，用于基于字段条件确定可选数据范围：
     * 查找关联、查找关联多选 选一条或者多条 查找关联对象数据
     * 人员、人员多选 选一个或者多个 人员
     * 部门、部门多选 选一个或者多个 部门
     * 第 2 类：
     * 被查找关联的对象的数据通过查找关联、查找关联多选、主从关系字段等可以对 关联的数据做某种统计（计数、某字段求和等）
     * 比如在 M 查找关联 D
     * M 可以对 D 进行某种统计计算，基于字段确定 D 参与统计计算的关联数据范围：
     * 统计字段
     *
     * @param sourceFieldDesc 源字段
     * @param sourceObjDesc   源对象
     * @param refObjDesMap    目标对象集合
     * @return
     */
    private void getCreateRefsByFieldWhere(IFieldDescribe sourceFieldDesc, IObjectDescribe sourceObjDesc,
                                           Map<String, IObjectDescribe> refObjDesMap) {

        // APL代码（where_type: function） !!!不支持，因为APL代码更新脱离了字段更新
        if (!StringUtils.equals(sourceFieldDesc.get(ObjectReferenceFieldDescribe.WHERE_TYPE, String.class), FieldDescribeExt.FIELD_WHERE_TYPE)) {
            return;
        }

        IObjectDescribe targetObjDesc = null;
        FieldDescribeExt sourceFieldDescExt = FieldDescribeExt.of(sourceFieldDesc);
        // 人员对象（"type": "employee"、"type":"employee_many"）的字段 : 人员对象（PersonnelObj）
        if (sourceFieldDescExt.isEmployeeField() || sourceFieldDescExt.isEmployeeManyField()) {
            // targetObj 为 PersonnelObj
            targetObjDesc = refObjDesMap.get(Utils.PERSONNEL_OBJ_API_NAME);
        } else if (sourceFieldDescExt.isDepartmentField() || sourceFieldDescExt.isDepartmentManyField()) {
            // targetObj 为 DepartmentObj
            targetObjDesc = refObjDesMap.get(Utils.DEPARTMENT_OBJ_API_NAME);
        } else if (sourceFieldDescExt.isLookupField()) {
            // targetObj 为 target_api_name 属性值
            targetObjDesc = refObjDesMap.get(sourceFieldDescExt.getRefObjTargetApiName());
        } else if (sourceFieldDescExt.isCountField()) {
            // targetObj 为 sub_object_describe_apiname 属性值
            Count countField = sourceFieldDescExt.getFieldDescribe();
            targetObjDesc = refObjDesMap.get(countField.getSubObjectDescribeApiName());
        }

        if (Objects.isNull(targetObjDesc)) {
            return;
        }

        if (CollectionUtils.empty(sourceFieldDescExt.getWheres())) {
            return;
        }
        for (Wheres where : sourceFieldDescExt.getWheres()) {
            if (CollectionUtils.empty(where.getFilters())) {
                continue;
            }
            for (Filter filter : where.getFilters()) {
                // 只记录该对象上字段
                if (StringUtils.contains(filter.getFieldName(), ".")) {
                    continue;
                }
                // 只记录 filter 的 fieldName
                // 暂不记录 filter 的 fieldValues 中的存在的变量
                IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(filter.getFieldName());
                appendCreateArg(sourceObjDesc, sourceFieldDesc, targetObjDesc, targetFieldDesc);
            }
        }
        return;
    }

    /**
     * 1. 字段属性默认值是表达式（前提：default_is_expression: true） default_value
     * 2. 计算字段的表达式
     * 3. 自增编号字段 前缀+后缀
     * 本对象字段值 本对象字段使用本对象字段
     * 查找关联对象值 本对象字段使用本对象查找关联字段 本对象字段使用关联对象上的字段
     *
     * @param sourceFieldDesc 源字段描述
     * @param sourceObjDesc   源对象描述
     * @param refObjDesMap    目标对象描述集合
     * @return 需要的创建关系
     */

    public void getCreateRefsByExpression(IFieldDescribe sourceFieldDesc, IObjectDescribe sourceObjDesc,
                                          Map<String, IObjectDescribe> refObjDesMap) {
        IObjectDescribe targetObjDesc = null;
        // 默认值（default_value）为 计算公式 "default_is_expression": true
        // 计算字段（expression）
        // 自增编号（default_value 属性值）
        // 支付组件 收款金额字段（pay_amount_field）的 default_value 属性值
        String expressionStr = null;
        FieldDescribeExt sourceFieldDescExt = FieldDescribeExt.of(sourceFieldDesc);
        if (sourceFieldDescExt.isGroupField()) {
            // 字段组，根据字段组类型取对应的内部字段
            GroupField groupField = sourceFieldDescExt.getFieldDescribe();
            if (GroupField.GROUP_TYPE_PAYMENT.equals(groupField.getGroupType())) {
                // 支付 获取金额字段 pay_amount_field
                Payment paymentGroupField = sourceFieldDescExt.getFieldDescribe();
                Currency payAmountField = null;
                try {
                    payAmountField = paymentGroupField.getPayAmountField(sourceObjDesc);
                } catch (MetadataServiceException e) {
                    log.warn("getPayAmountField fail, tenantId:{}, objAPi:{}, fieldApi:{}, payAmountFieldApi:{}",
                            sourceObjDesc.getTenantId(), sourceObjDesc.getApiName(), sourceFieldDesc.getApiName(),
                            paymentGroupField.getPayAmountFieldApiName());
                }
                expressionStr = Optional.ofNullable(payAmountField)
                        .filter(IFieldDescribe::getDefaultIsExpression)
                        .map(IFieldDescribe::getDefaultValue)
                        .map(String::valueOf)
                        .orElse(null);

            } else if (GroupField.GROUP_TYPE_SIGN_IN.equals(groupField.getGroupType())) {
                // 签到组件 获取字段属性 目标位置 quote_field
                SignIn signInField = sourceFieldDescExt.getFieldDescribe();
                expressionStr = signInField.getQuoteField();
            }
        } else if (sourceFieldDescExt.hasFormulaDefaultValue()) {
            expressionStr = Optional.ofNullable(sourceFieldDescExt.getDefaultValue()).map(Object::toString).orElse(null);
        } else if (sourceFieldDescExt.isFormula()) {
            expressionStr = sourceFieldDescExt.getExpression();
        } else if (sourceFieldDescExt.isQuoteField()) {
            expressionStr = "$" + sourceFieldDescExt.getQuoteField() + "$";
        } else if (sourceFieldDescExt.isAutoNumber()) {
            AutoNumber autoNumField = sourceFieldDescExt.getFieldDescribe();
            expressionStr = String.join("", autoNumField.getPrefix(), autoNumField.getPostfix());
        }

        // 灰度企业跳过字段表达式校验和替换
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SKIP_FIELD_EXPRESSION_VALIDATION_GRAY, sourceObjDesc.getTenantId())) {
            Pattern fieldVar = Pattern.compile(AutoNumberExt.FIELD_NAME_PATTERN);
            StringBuilder sb = new StringBuilder();
            ReUtil.findAll(fieldVar,
                    StrUtil.concat(true, expressionStr),
                    matcher -> sb.append(matcher.group())
            );
            expressionStr = sb.toString();
        }

        if (StringUtils.isBlank(expressionStr)) {
            return;
        }
        Expression expression = Expression.builder()
                .expression(expressionStr)
                .expressionLabel("")
                .describe(sourceObjDesc)
                .build();

        expression.init();

        // 本对象字段
        targetObjDesc = sourceObjDesc;
        for (ObjectFieldVar objFieldVar : expression.getObjectFieldVariables()) {
            IFieldDescribe targetFieldDesc = objFieldVar.getFieldDescribe();
            appendCreateArg(sourceObjDesc, sourceFieldDesc, targetObjDesc, targetFieldDesc);
        }

        for (RefObjectFieldVar refObjFieldVar : expression.getRefObjectFieldVariables()) {

            // 查找关系字段记录
            String lookupFieldApiName = refObjFieldVar.getFieldName();
            IFieldDescribe lookupFieldDesc = sourceObjDesc.getFieldDescribe(lookupFieldApiName);
            appendCreateArg(sourceObjDesc, sourceFieldDesc, targetObjDesc, lookupFieldDesc);

            // 关联对象上的字段
            targetObjDesc = refObjDesMap.get(refObjFieldVar.getTargetObjectAPIName());
            if (Objects.isNull(targetObjDesc)) {
                continue;
            }

            IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(refObjFieldVar.getTargetFieldAPIName());
            if (Objects.isNull(targetFieldDesc)) {
                continue;
            }
            appendCreateArg(sourceObjDesc, sourceFieldDesc, targetObjDesc, targetFieldDesc);
        }
    }

    /**
     * 统计字段记录使用关系
     * 第1类关系 公式使用 用于统计的被查找关联对象的字段
     * 第2类关系 被统计数据基于条件范围使用 字段
     * 使用了查找关联对象的查找关联字段 和 用于统计计算的字段（count为纯计数，不需要有字段参与计算）
     *
     * @param sourceFieldDesc
     * @param sourceObjDesc
     * @param refObjDesMap
     * @return
     */
    public void getCreateRefsByCountField(IFieldDescribe sourceFieldDesc, IObjectDescribe sourceObjDesc,
                                          Map<String, IObjectDescribe> refObjDesMap) {
        FieldDescribeExt sourceFieldDescExt = FieldDescribeExt.of(sourceFieldDesc);
        if (!sourceFieldDescExt.isCountField()) {
            return;
        }

        Count countField = sourceFieldDescExt.getFieldDescribe();
        String targetObjApi = countField.getSubObjectDescribeApiName();
        IObjectDescribe targetObjDesc = refObjDesMap.get(targetObjApi);

        // 查找关系字段记录
        String lookupFieldApiName = countField.getFieldApiName();
        if (StringUtils.isBlank(lookupFieldApiName)) {
            //历史主从字段该值为空，直接查从对象上的主从字段
            lookupFieldApiName = ObjectDescribeExt.of(targetObjDesc)
                    .getMasterDetailField()
                    .map(MasterDetail::getApiName)
                    .orElse(null);
            if (StringUtils.isBlank(lookupFieldApiName)) {
                return;
            }
        }
        IFieldDescribe lookupFieldDesc = targetObjDesc.getFieldDescribe(lookupFieldApiName);
        appendCreateArg(sourceObjDesc, sourceFieldDesc, targetObjDesc, lookupFieldDesc);

        if (StringUtils.isBlank(countField.getCountFieldApiName())) {
            // 计数统计不需要指定字段
            return;
        }
        // 需要指定字段的统计
        String targetFieldApi = countField.getCountFieldApiName();
        IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(targetFieldApi);
        appendCreateArg(sourceObjDesc, sourceFieldDesc, targetObjDesc, targetFieldDesc);

    }

    /**
     * 字段属性水印配置字段
     *
     * @param sourceFieldDesc
     * @param sourceObjDesc
     * @param refObjDesMap
     * @return
     */
    public void getCreateRefsByFieldWatermark(IFieldDescribe sourceFieldDesc, IObjectDescribe sourceObjDesc,
                                              Map<String, IObjectDescribe> refObjDesMap) {
        FieldDescribeExt sourceFieldDescExt = FieldDescribeExt.of(sourceFieldDesc);
        if (!sourceFieldDescExt.isImageField()) {
            return;
        }
        Image imageField = sourceFieldDescExt.getFieldDescribe();
        if (!imageField.getIsWaterMark() || CollectionUtils.empty(imageField.getWatermark())) {
            return;
        }

        // 图片字段 水印配置字段
        for (Map<String, Object> value : imageField.getWatermark()) {
            String valueType = (String) value.get("type");
            String valueExpression = (String) value.get("value");
            if (!Objects.equals("field", valueType)) {
                continue;
            }
            // 只记录本对象字段
            if (StringUtils.contains(valueExpression, ".")) {
                continue;
            }

            IObjectDescribe targetObjDesc = sourceObjDesc;
            IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(valueExpression);
            if (Objects.isNull(targetFieldDesc.getApiName())) {
                continue;
            }
            appendCreateArg(sourceObjDesc, sourceFieldDesc, targetObjDesc, targetFieldDesc);
        }
    }

    /**
     * 相关列表页选要查找关联对象数据，当前数据被所选数据查找关联
     *
     * @param sourceFieldDesc
     * @param sourceObjDesc
     * @param refObjDesMap
     * @return
     */
    public void getCreateRefsByFieldRelatedWheres(IFieldDescribe sourceFieldDesc, IObjectDescribe sourceObjDesc,
                                                  Map<String, IObjectDescribe> refObjDesMap) {

        // APL代码（where_type: function） !!!不支持，因为APL代码更新脱离了字段更新
        if (!StringUtils.equals(sourceFieldDesc.get(ObjectReferenceFieldDescribe.RELATION_WHERES_TYPE, String.class), FieldDescribeExt.FIELD_WHERE_TYPE)) {
            return;
        }

        FieldDescribeExt sourceFieldDescExt = FieldDescribeExt.of(sourceFieldDesc);

        if (CollectionUtils.empty(sourceFieldDescExt.getWheres())) {
            return;
        }
        IObjectDescribe targetObjDesc = sourceObjDesc;
        for (Wheres relatedWheres : sourceFieldDescExt.getRelatedWheres()) {
            if (CollectionUtils.empty(relatedWheres.getFilters())) {
                continue;
            }
            for (Filter filter : relatedWheres.getFilters()) {
                // 只记录该对象上字段
                if (StringUtils.contains(filter.getFieldName(), ".")) {
                    continue;
                }
                // 只记录 filter 的 fieldName
                // 暂不记录 filter 的 fieldValues 中的存在的变量
                IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(filter.getFieldName());
                appendCreateArg(sourceObjDesc, sourceFieldDesc, targetObjDesc, targetFieldDesc);
            }
        }
    }

    private boolean skipByTargetObj(String targetObjApi) {
        return AppFrameworkConfig.skipRecordRefByTargetObjGray(targetObjApi);
    }
}
