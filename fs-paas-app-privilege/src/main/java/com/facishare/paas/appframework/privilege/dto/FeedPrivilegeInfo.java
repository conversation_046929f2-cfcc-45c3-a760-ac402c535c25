package com.facishare.paas.appframework.privilege.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 跟进动态feed
 * @date 2020/12/18
 */
public interface FeedPrivilegeInfo {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        private String objectApiName;
        private Integer dataRoleType;
        private Boolean enable;
        private Boolean outPermission;
    }

    @Data
    class Result {
        private int code;
        private String message;
        private FeedResult data;
    }

    @Data
    class FeedResult {
        private boolean enable;
    }

}
