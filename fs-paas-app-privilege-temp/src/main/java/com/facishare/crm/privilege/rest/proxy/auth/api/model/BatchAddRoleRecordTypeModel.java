package com.facishare.crm.privilege.rest.proxy.auth.api.model;

import com.facishare.crm.privilege.rest.proxy.auth.model.BaseArg_Auth;
import com.facishare.crm.privilege.rest.proxy.auth.model.BaseAuthResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by luxin on 2017/8/4.
 */
public class BatchAddRoleRecordTypeModel {

  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class Arg extends BaseArg_Auth {
    private List<String> entityIds;
    private String roleCode;
    private String recordTypeId;
  }

  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class Result extends BaseAuthResult {
  }

}
