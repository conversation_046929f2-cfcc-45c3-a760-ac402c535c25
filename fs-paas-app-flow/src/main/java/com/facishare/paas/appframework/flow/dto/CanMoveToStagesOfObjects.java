package com.facishare.paas.appframework.flow.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface CanMoveToStagesOfObjects {

    @Data
    @Builder
    class Arg {
        private String entityId;
        private List<String> objectIds;
    }

    @Data
    class Result {
        private int code;
        private String message;
        private Map<String, List<String>> data;
    }
}
