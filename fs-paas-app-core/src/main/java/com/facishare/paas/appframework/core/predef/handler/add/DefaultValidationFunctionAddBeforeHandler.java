package com.facishare.paas.appframework.core.predef.handler.add;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.handler.AbstractValidationFunctionSaveBeforeHandler;
import com.facishare.paas.appframework.core.predef.handler.add.AddActionHandler;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.appframework.metadata.state.MergeStateContainer;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2023/1/28.
 */
@Slf4j
@Component
@HandlerProvider(name = "defaultValidationFunctionAddBeforeHandler")
public class DefaultValidationFunctionAddBeforeHandler extends AbstractValidationFunctionSaveBeforeHandler<AddActionHandler.Arg, AddActionHandler.Result> {

    @Override
    protected AddActionHandler.Result buildResult(HandlerContext context, AddActionHandler.Arg arg) {
        return new AddActionHandler.Result();
    }

    @Override
    protected String getButtonApiName(HandlerContext context, AddActionHandler.Arg arg) {
        if (AppFrameworkConfig.isAddEditUIActionGray(context.getTenantId(), arg.getObjectApiName())) {
            return ObjectAction.CREATE_SAVE.getButtonApiName();
        }
        return ButtonExt.DEFAULT_ADD_BUTTON_API_NAME;
    }

    @Override
    protected MergeStateContainer getSourceMergeStateContainer(HandlerContext context, AddActionHandler.Arg arg) {
        List<IObjectData> objectDataList = arg.detailObjectData().values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        return MergeStateContainer.of(objectDataList, Collections.emptyList(), Collections.emptyList());
    }

    @Override
    protected void replaceDetailDataMergeResult(HandlerContext context, AddActionHandler.Arg arg, MergeStateContainer sourceMergeStateContainer) {
        Map<String, List<IObjectData>> detailObjectData = getDetails(arg, sourceMergeStateContainer);
        arg.setDetailObjectData(ObjectDataDocument.ofMap(detailObjectData));
    }

    private Map<String, List<IObjectData>> getDetails(AddActionHandler.Arg arg, MergeStateContainer sourceMergeStateContainer) {
        Map<String, List<IObjectData>> result = Maps.newHashMap();
        List<IObjectData> dataList = sourceMergeStateContainer.detailsToAdd();
        if (CollectionUtils.empty(dataList)) {
            return result;
        }
        for (IObjectData data : dataList) {
            IObjectDescribe describe = arg.getDetailDescribe(data.getDescribeApiName());
            if (Objects.isNull(describe)) {
                continue;
            }
            result.computeIfAbsent(data.getDescribeApiName(), it -> Lists.newArrayList()).add(data);
        }
        String masterDataId = arg.objectData().getId();
        result.forEach((apiName, details) -> addMasterDetailFieldIntoDetailDataList(arg.getObjectApiName(), masterDataId,
                arg.getDetailDescribe(apiName), details));
        return result;
    }
}
