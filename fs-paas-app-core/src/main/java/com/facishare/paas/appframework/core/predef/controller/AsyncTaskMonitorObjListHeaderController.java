package com.facishare.paas.appframework.core.predef.controller;

import com.google.common.collect.Lists;

import java.util.List;

import static com.facishare.crm.userdefobj.DefObjConstants.internalObjShowFieldsMap;

public class AsyncTaskMonitorObjListHeaderController extends BaseListHeaderController<StandardListHeaderController.Arg> {


    @Override
    protected boolean isBackground() {
        return true;
    }

    @Override
    protected List<String> getAuthorizedFields() {
        return Lists.newArrayList(internalObjShowFieldsMap.get(arg.getApiName()));
    }
}
