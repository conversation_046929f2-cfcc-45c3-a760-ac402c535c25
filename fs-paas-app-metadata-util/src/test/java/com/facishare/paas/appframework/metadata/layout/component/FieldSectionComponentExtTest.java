package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.collect.Lists;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * FieldSectionComponentExt单元测试类
 */
@ExtendWith(MockitoExtension.class)
class FieldSectionComponentExtTest {

  private FieldSectionComponentExt fieldSectionComponentExt;

  @BeforeEach
  void setUp() {
    fieldSectionComponentExt = new FieldSectionComponentExt();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试默认构造函数创建对象
   */
  @Test
  @DisplayName("测试默认构造函数")
  void testDefaultConstructor() {
    FieldSectionComponentExt component = new FieldSectionComponentExt();
    assertNotNull(component);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试Map构造函数创建对象
   */
  @Test
  @DisplayName("测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = new HashMap<>();
    List<Map> fieldsList = new ArrayList<>();
    map.put(FieldSectionComponentExt.FIELDS, fieldsList);
    
    FieldSectionComponentExt component = new FieldSectionComponentExt(map);
    
    assertNotNull(component);
    assertEquals(fieldsList, component.get(FieldSectionComponentExt.FIELDS));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFields方法当fields为null时返回空列表
   */
  @Test
  @DisplayName("测试getFields方法 - fields为null时返回空列表")
  void testGetFieldsWhenFieldsIsNull() {
    List<FieldComponentExt> result = fieldSectionComponentExt.getFields();
    
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFields方法当fields不为空时返回FieldComponentExt列表
   */
  @Test
  @DisplayName("测试getFields方法 - fields不为空时返回FieldComponentExt列表")
  void testGetFieldsWhenFieldsIsNotEmpty() {
    // 准备测试数据
    Map<String, Object> fieldMap1 = new HashMap<>();
    fieldMap1.put("type", "field");
    fieldMap1.put("api_name", "field1");
    
    Map<String, Object> fieldMap2 = new HashMap<>();
    fieldMap2.put("type", "tag");
    fieldMap2.put("api_name", "field2");
    
    List<Map> fieldsList = Lists.newArrayList(fieldMap1, fieldMap2);
    fieldSectionComponentExt.set(FieldSectionComponentExt.FIELDS, fieldsList);
    
    // 执行测试
    List<FieldComponentExt> result = fieldSectionComponentExt.getFields();
    
    // 验证结果
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals("field", result.get(0).getType());
    assertEquals("field1", result.get(0).getApiName());
    assertEquals("tag", result.get(1).getType());
    assertEquals("field2", result.get(1).getApiName());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试addFields方法添加FieldComponentExt到空列表
   */
  @Test
  @DisplayName("测试addFields方法 - 添加到空列表")
  void testAddFieldsToEmptyList() {
    // 准备测试数据
    FieldComponentExt fieldComponent = new FieldComponentExt();
    fieldComponent.setType("field");
    fieldComponent.setApiName("testField");

    // 执行测试
    fieldSectionComponentExt.addFields(fieldComponent);

    // 验证结果
    Object fieldsObj = fieldSectionComponentExt.get(FieldSectionComponentExt.FIELDS);
    assertNotNull(fieldsObj);
    assertTrue(fieldsObj instanceof List);

    List<Map> fieldsList = (List<Map>) fieldsObj;
    assertEquals(1, fieldsList.size());
    assertNotNull(fieldsList.get(0));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试addFields方法添加FieldComponentExt到已有列表
   */
  @Test
  @DisplayName("测试addFields方法 - 添加到已有列表")
  void testAddFieldsToExistingList() {
    // 准备已有数据
    List<Map> existingFields = new ArrayList<>();
    Map<String, Object> existingField = new HashMap<>();
    existingField.put("type", "existing");
    existingFields.add(existingField);
    fieldSectionComponentExt.set(FieldSectionComponentExt.FIELDS, existingFields);

    // 准备新数据
    FieldComponentExt fieldComponent = new FieldComponentExt();
    fieldComponent.setType("field");
    fieldComponent.setApiName("newField");

    // 执行测试
    fieldSectionComponentExt.addFields(fieldComponent);

    // 验证结果
    List<Map> fieldsList = (List<Map>) fieldSectionComponentExt.get(FieldSectionComponentExt.FIELDS);
    assertEquals(2, fieldsList.size());
    assertEquals(existingField, fieldsList.get(0));
    assertNotNull(fieldsList.get(1));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeFields方法当fields为空时不执行删除
   */
  @Test
  @DisplayName("测试removeFields方法 - fields为空时不执行删除")
  void testRemoveFieldsWhenFieldsIsEmpty() {
    // 准备测试数据 - fields为null
    Predicate<FieldComponentExt> predicate = field -> true;

    // 执行测试 - 应该不会抛出异常
    fieldSectionComponentExt.removeFields(predicate);

    // 验证没有异常抛出即可
    assertNotNull(fieldSectionComponentExt);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeFields方法当predicate为null时不执行删除
   */
  @Test
  @DisplayName("测试removeFields方法 - predicate为null时不执行删除")
  void testRemoveFieldsWhenPredicateIsNull() {
    try (MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class)) {
      // 准备测试数据
      List<Map> fieldsList = new ArrayList<>();
      Map<String, Object> fieldMap = new HashMap<>();
      fieldMap.put("type", "field");
      fieldsList.add(fieldMap);
      fieldSectionComponentExt.set(FieldSectionComponentExt.FIELDS, fieldsList);
      
      // Mock CollectionUtils.empty返回false
      mockedCollectionUtils.when(() -> CollectionUtils.empty(any(List.class))).thenReturn(false);
      
      // 执行测试
      fieldSectionComponentExt.removeFields(null);
      
      // 验证结果 - 列表应该没有变化
      List<Map> result = (List<Map>) fieldSectionComponentExt.get(FieldSectionComponentExt.FIELDS);
      assertEquals(1, result.size());
      assertEquals(fieldMap, result.get(0));
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeFields方法正常删除匹配的字段
   */
  @Test
  @DisplayName("测试removeFields方法 - 正常删除匹配的字段")
  void testRemoveFieldsNormalCase() {
    try (MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class)) {
      // 准备测试数据
      Map<String, Object> fieldMap1 = new HashMap<>();
      fieldMap1.put("type", "field");
      fieldMap1.put("api_name", "field1");
      
      Map<String, Object> fieldMap2 = new HashMap<>();
      fieldMap2.put("type", "tag");
      fieldMap2.put("api_name", "field2");
      
      List<Map> fieldsList = new ArrayList<>();
      fieldsList.add(fieldMap1);
      fieldsList.add(fieldMap2);
      fieldSectionComponentExt.set(FieldSectionComponentExt.FIELDS, fieldsList);
      
      // Mock CollectionUtils.empty返回false
      mockedCollectionUtils.when(() -> CollectionUtils.empty(fieldsList)).thenReturn(false);
      
      // 定义删除条件：删除type为"field"的字段
      Predicate<FieldComponentExt> predicate = field -> "field".equals(field.getType());
      
      // 执行测试
      fieldSectionComponentExt.removeFields(predicate);
      
      // 验证结果
      List<Map> result = (List<Map>) fieldSectionComponentExt.get(FieldSectionComponentExt.FIELDS);
      assertEquals(1, result.size());
      assertEquals(fieldMap2, result.get(0));
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试常量值的正确性
   */
  @Test
  @DisplayName("测试常量值")
  void testConstants() {
    assertEquals("fields", FieldSectionComponentExt.FIELDS);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试addFields方法当fields对象不是List类型时不添加
   */
  @Test
  @DisplayName("测试addFields方法 - fields对象不是List类型时不添加")
  void testAddFieldsWhenFieldsIsNotList() {
    // 设置fields为非List类型
    fieldSectionComponentExt.set(FieldSectionComponentExt.FIELDS, "not a list");
    
    FieldComponentExt fieldComponent = new FieldComponentExt();
    fieldComponent.setType("field");
    
    // 执行测试
    fieldSectionComponentExt.addFields(fieldComponent);
    
    // 验证结果 - fields应该还是原来的值
    Object fieldsObj = fieldSectionComponentExt.get(FieldSectionComponentExt.FIELDS);
    assertEquals("not a list", fieldsObj);
  }
}
