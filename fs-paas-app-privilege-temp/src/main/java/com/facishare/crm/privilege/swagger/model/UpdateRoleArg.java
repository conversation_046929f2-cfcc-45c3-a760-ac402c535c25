/**
 * Facishare PAAS Auth
 * This is PAAS auth service project
 *
 * OpenAPI spec version: v1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


package com.facishare.crm.privilege.swagger.model;

import java.util.Objects;
import com.facishare.crm.privilege.swagger.model.AuthContext;
import com.facishare.crm.privilege.swagger.model.RolePojo;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * UpdateRoleArg
 */
@javax.annotation.Generated(value = "class io.swagger.codegen.languages.JavaClientCodegen", date = "2017-05-16T15:01:04.279+08:00")
public class UpdateRoleArg   {
  @SerializedName("authContext")
  private AuthContext authContext = null;

  @SerializedName("rolePojo")
  private RolePojo rolePojo = null;

  public UpdateRoleArg authContext(AuthContext authContext) {
    this.authContext = authContext;
    return this;
  }

   /**
   * Get authContext
   * @return authContext
  **/
  @ApiModelProperty(example = "null", value = "")
  public AuthContext getAuthContext() {
    return authContext;
  }

  public void setAuthContext(AuthContext authContext) {
    this.authContext = authContext;
  }

  public UpdateRoleArg rolePojo(RolePojo rolePojo) {
    this.rolePojo = rolePojo;
    return this;
  }

   /**
   * Get rolePojo
   * @return rolePojo
  **/
  @ApiModelProperty(example = "null", value = "")
  public RolePojo getRolePojo() {
    return rolePojo;
  }

  public void setRolePojo(RolePojo rolePojo) {
    this.rolePojo = rolePojo;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    UpdateRoleArg updateRoleArg = (UpdateRoleArg) o;
    return Objects.equals(this.authContext, updateRoleArg.authContext) &&
        Objects.equals(this.rolePojo, updateRoleArg.rolePojo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(authContext, rolePojo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class UpdateRoleArg {\n");
    
    sb.append("    authContext: ").append(toIndentedString(authContext)).append("\n");
    sb.append("    rolePojo: ").append(toIndentedString(rolePojo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

