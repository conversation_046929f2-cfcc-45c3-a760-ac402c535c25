package com.facishare.paas.appframework.license;

import com.facishare.paas.appframework.license.dto.BaseResult;
import com.facishare.paas.appframework.license.exception.LicenseException;
import com.facishare.rest.core.codec.DefaultRestCodec;
import com.facishare.rest.core.codec.IRestCodeC;
import com.facishare.rest.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2017/9/13
 */
@Slf4j
public class LicenseRestCodeC implements IRestCodeC {

    @Override
    public <T> byte[] encodeArg(T t) {
        return DefaultRestCodec.instance.encodeArg(t);
    }

    @Override
    public <T> T decodeResult(int statusCode, Map<String, List<String>> headers, byte[] bytes, Class<T> clazz) {
        T ret = DefaultRestCodec.instance.decodeResult(statusCode, headers, bytes, clazz);
        validateResult(ret);
        return ret;
    }

    protected void validateResult(Object ret) {
        BaseResult result = (BaseResult) ret;
        if (!result.isSuccess()) {
            log.error("validateResult error,result={}", JsonUtil.toJson(ret));
            throw new LicenseException(result.getErrMessage());
        }
    }

}
