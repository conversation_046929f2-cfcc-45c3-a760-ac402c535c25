package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.coordination.dto.GetCRMFeedByObjectID;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedController.Arg;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedController.Result;
import com.facishare.paas.appframework.log.dto.MobSearchResult;
import com.facishare.paas.appframework.log.dto.MobSearchRichResult;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.appframework.payment.dto.PaymentRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IRelatedListQuery;
import com.facishare.paas.metadata.api.search.IRelatedListQuery.QueryResult.RelatedObjectResult;
import com.facishare.paas.metadata.dao.pg.entity.metadata.RelevantTeam;
import com.facishare.paas.metadata.impl.describe.*;
import com.facishare.paas.metadata.impl.search.RelatedListQuery;
import com.facishare.paas.metadata.impl.search.SearchQuery;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.LayoutExt.PAYMENT_DESCRIBE_API_NAME;

/**
 * 标准关联列表页(终端使用)
 * <p>
 * Created by liyiguang on 2017/10/11.
 */
public class StandardRelatedController extends PreDefineController<Arg, Result> {

    protected List<RelatedObjectDescribeStructure> relatedDescribeList;
    protected List<RelatedObjectDescribeStructure> detailDescribeList;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardController.Related.getFuncPrivilegeCodes();
    }

    @Override
    protected Result doService(Arg arg) {
        //1 查找所有关联对象的数据
        IObjectDescribe objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), arg.getObjectDescribeApiName());
        stopWatch.lap("findObject");

        List<IObjectDescribe> related = serviceFacade.findRelatedDescribesByListPrivilege(controllerContext.getUser(), arg.getObjectDescribeApiName());
        relatedDescribeList = getRelatedDescribes(objectDescribe, related);
        detailDescribeList = getDetailDescribes(objectDescribe, related);
        stopWatch.lap("getRelatedDescribes");

        List<Map<String, Object>> relatedDataList;
        IRelatedListQuery.QueryResult<IObjectData> relatedObjectData = new IRelatedListQuery.QueryResult<>();
        if (arg.getLimit() > 0 && CollectionUtils.notEmpty(relatedDescribeList)) {
            relatedObjectData = bulkFindRelatedDataList(relatedDescribeList);
            stopWatch.lap("bulkFindRelatedDataList");
        }

        relatedDataList = getRelatedDataList(relatedDescribeList, relatedObjectData);
        IObjectData objectData = serviceFacade.findObjectData(controllerContext.getUser(), arg.getObjectDataId(), objectDescribe);
        stopWatch.lap("findObjectData");

        //2 自定义对象，补充修改记录，销售记录，支付记录
        List<Map<String, Object>> resultList = Lists.newCopyOnWriteArrayList();
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            //加入修改记录的describe和data
            List<Map<String, Object>> modifyRecordList = fillModifyRecord(objectDescribe, controllerContext.getUser(), arg.getObjectDataId(), arg.getLimit());
            resultList.addAll(modifyRecordList);
            stopWatch.lap("fillModifyRecord");
        });
        parallelTask.submit(() -> {
            //加入销售记录的describe和data
            List<Map<String, Object>> salesRecordList = fillSaleRecord(objectDescribe, controllerContext.getUser(), arg.getObjectDataId());
            resultList.addAll(salesRecordList);
            stopWatch.lap("fillSaleRecord");
        });
        parallelTask.submit(() -> {
            //加入支付记录的describe和data
            List<Map<String, Object>> paymentRecordList = fillPaymentRecord(objectDescribe, controllerContext.getUser(), objectData, 2);
            resultList.addAll(paymentRecordList);
            stopWatch.lap("fillPaymentRecord");
        });
        try {
            parallelTask.await(6, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("parallelTask error", e);
        } finally {
            relatedDataList.addAll(resultList);
        }

        // 填充字段信息
        fillFieldValue(related, relatedObjectData);
        stopWatch.lap("fillFieldFieldValue");

        Result.ResultBuilder builder = Result.builder();
        //3 查询layout，if necessary
        if (arg.getIsIncludeLayout()) {
            ILayout layout = serviceFacade.getLayoutLogicService().getLayoutWithComponents(buildLayoutContext(), objectData.getRecordType(),
                    objectDescribe, objectData, relatedDescribeList, detailDescribeList, PageType.Related);
            stopWatch.lap("getLayoutWithComponents");
            builder.layout(LayoutDocument.of(layout));
        }

        //4 查询objectDescribe，if necessary
        if (arg.getIsIncludeDescribe()) {
            builder = builder.describe(ObjectDescribeDocument.handleDescribeCache(arg.getDescribeVersionMap(), objectDescribe));
        }

        //5 返回结果
        return builder.refObjects(relatedDataList).build();
    }

    private IRelatedListQuery.QueryResult<IObjectData> bulkFindRelatedDataList(List<RelatedObjectDescribeStructure> relatedDescribeList) {
        IRelatedListQuery.QueryResult<IObjectData> result = new IRelatedListQuery.QueryResult<>();

        if (CollectionUtils.empty(relatedDescribeList)) {
            return result;
        }

        int groupSize = 5;
        if (relatedDescribeList.size() <= groupSize) {
            return findRelatedDataList(relatedDescribeList);
        }

        List<List<RelatedObjectDescribeStructure>> relatedDescribeGroups = Lists.partition(relatedDescribeList, 5);
        result.setResultList(Lists.newCopyOnWriteArrayList());
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        relatedDescribeGroups.forEach(relatedGroup -> parallelTask.submit(() -> {
            IRelatedListQuery.QueryResult<IObjectData> groupResult = findRelatedDataList(relatedGroup);
            result.getResultList().addAll(CollectionUtils.nullToEmpty(groupResult.getResultList()));
        }));
        try {
            parallelTask.await(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("findRelatedDataList time out,context:{},arg:{}", controllerContext, arg, e);
            throw new MetaDataException(e.getMessage(), e);
        }

        return result;
    }

    private IRelatedListQuery.QueryResult<IObjectData> findRelatedDataList(List<RelatedObjectDescribeStructure> relatedDescribeList) {
        IRelatedListQuery relatedListQuery = getRelatedListQuery(arg.getObjectDataId(), arg.getObjectDescribeApiName(),
                arg.getOffset(), arg.getLimit(), relatedDescribeList, controllerContext.getUser());
        return serviceFacade.findRelatedObjectData(relatedListQuery, controllerContext.getUser());
    }

    private void fillFieldValue(List<IObjectDescribe> related, IRelatedListQuery.QueryResult<IObjectData> relatedObjectData) {
        if (!ObjectDescribeExt.isSFAObject(arg.getObjectDescribeApiName())) {
            return;
        }
        List<RelatedObjectResult<IObjectData>> resultList = relatedObjectData.getResultList();
        if (CollectionUtils.empty(related) || CollectionUtils.empty(resultList)) {
            return;
        }
        Map<String, IObjectDescribe> describeMap = related.stream()
                .collect(Collectors.toMap(IObjectDescribe::getApiName, describe -> describe, (x, y) -> x));

        // 只补充lookup到老对象的老对象的相关信息
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        resultList.stream()
                .filter(relatedData -> ObjectDescribeExt.isSFAObject(relatedData.getChildApiName()))
                .filter(relatedData -> describeMap.containsKey(relatedData.getChildApiName()))
                .filter(relatedData -> CollectionUtils.notEmpty(relatedData.getDataList()))
                .forEach(relatedData -> parallelTask.submit(() -> fillObjectName(describeMap.get(
                        relatedData.getChildApiName()), relatedData.getDataList())));
        try {
            parallelTask.await(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Error in fill fillFieldFieldValue, ei:{}", controllerContext.getTenantId(), e);
        }
    }

    private void fillObjectName(IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        serviceFacade.fillObjectDataWithRefObject(objectDescribe, dataList, controllerContext.getUser(), null);
    }


    private List<RelatedObjectDescribeStructure> getRelatedDescribes(IObjectDescribe describe, List<IObjectDescribe> related) {
        List<RelatedObjectDescribeStructure> relatedObjects = ObjectDescribeExt.of(describe).getRelatedObjectDescribeStructures(related);
        return relatedObjects;
    }

    private List<RelatedObjectDescribeStructure> getDetailDescribes(IObjectDescribe describe, List<IObjectDescribe> related) {
        return ObjectDescribeExt.of(describe).getDetailObjectDescribeStructures(related);
    }

    protected LayoutLogicService.LayoutContext buildLayoutContext() {
        return LayoutLogicService.LayoutContext.of(controllerContext.getUser(), controllerContext.getAppId());
    }

    private List<Map<String, Object>> fillPaymentRecord(IObjectDescribe sourceObject, User user, IObjectData objectData, Integer limit) {
        List<Map<String, Object>> list = Lists.newArrayList();
        if (sourceObject == null) {
            return list;
        }

        Optional<PaymentFieldDescribe> paymentField = ObjectDescribeExt.of(sourceObject).getPaymentFieldDescribe();
        if (!paymentField.isPresent()) {
            return list;
        }

        ILayout layout = serviceFacade.getLayoutLogicService().findObjectLayoutWithType(buildLayoutContext(), objectData.getRecordType(),
                sourceObject, ILayout.DETAIL_LAYOUT_TYPE, objectData);
        stopWatch.lap("findObjectLayoutWithType");

        //如果layout中没有支付组件，则不需要后续处理
        if (!LayoutExt.of(layout).isFieldInLayout(paymentField.get().getPayAmountFieldApiName())) {
            return list;
        }

        //调用支付服务获取数据
        List<PaymentRecord> paymentList = serviceFacade.findPaymentList(user, sourceObject, objectData);
        stopWatch.lap("findPaymentList");

        Map<String, Object> map = Maps.newHashMap();
        map.put("api_name", PAYMENT_DESCRIBE_API_NAME);

        ObjectDescribeExt describe = ObjectDescribeExt.buildPaymentDescribe();

        int totalNumber = 0;
        if (CollectionUtils.notEmpty(paymentList)) {
            totalNumber = paymentList.size();
        }
        map.put("describe", describe.toMap());
        map.put("total", totalNumber);
        map.put("offset", 0);
        map.put("limit", limit);
        map.put("related_list_name", PAYMENT_DESCRIBE_API_NAME + "_LIST");

        List<Map<String, Object>> dataList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(paymentList)) {
            int index = 0;
            for (PaymentRecord data : paymentList) {
                if (index >= limit) {
                    break;
                }
                Map<String, Object> oneData = Maps.newHashMap();
                Map<String, Object> dataDetail = Maps.newHashMap();
                dataDetail.put("amount", data.getAmount());
                dataDetail.put("fee", data.getFee());
                dataDetail.put("payEnterpriseName", data.getPayEnterpriseName());
                dataDetail.put("remark", data.getRemark());
                dataDetail.put("payType", data.getPayType());
                dataDetail.put("finishTime", data.getFinishTime());
                dataDetail.put("transTime", data.getTransTime());
                dataDetail.put("relatedObject", data.getRelatedObject());
                dataDetail.put("relatedObjectName", data.getRelatedObjectName());
                dataDetail.put("payStatus", data.getPayStatus());
                dataDetail.put("detailUrl", data.getDetailUrl());
                dataDetail.put("name", data.getOrderNo());
                dataDetail.put(IObjectData.ID, data.getOrderNo());
                oneData.put("data", dataDetail);
                dataList.add(oneData);
                index++;
            }
        }
        map.put("data", dataList);
        list.add(map);
        return list;
    }


    private List<Map<String, Object>> fillSaleRecord(IObjectDescribe sourceObject, User user, String dataId) {
        List<Map<String, Object>> list = Lists.newArrayList();
        Map<String, Object> recordInfo = Maps.newHashMap();

        IObjectDescribe describe = new ObjectDescribe();
        describe.setApiName("operation_log");
        fillRecordHeader(recordInfo, describe);

        IFieldDescribe userField = new EmployeeFieldDescribe();
        userField.setApiName("sender_id");
        IFieldDescribe messageField = new TextFieldDescribe();
        messageField.setApiName("content");
        IFieldDescribe operationTimeField = new DateTimeFieldDescribe();
        operationTimeField.setApiName("create_time");
        describe.setFieldDescribes(Lists.newArrayList(userField, messageField, operationTimeField));

        //调用feed服务获取数据
        List<GetCRMFeedByObjectID.SimpleFeed> feedList = serviceFacade.findFeedList(sourceObject.getApiName(), dataId, user, findFeedListHasPermission());
        int totalNumber = 0;
        if (!CollectionUtils.empty(feedList)) {
            totalNumber = feedList.size();
        }
        recordInfo.put("describe", ObjectDescribeDocument.of(describe));
        recordInfo.put("total", totalNumber);
        recordInfo.put("offset", 0);
        recordInfo.put("limit", totalNumber);

        List<Map<String, Object>> dataList = Lists.newArrayList();
        if (!CollectionUtils.empty(feedList)) {
            for (GetCRMFeedByObjectID.SimpleFeed data : feedList) {
                Map<String, Object> oneData = Maps.newHashMap();
                Map<String, Object> dataDetail = Maps.newHashMap();
                dataDetail.put("sender_id", data.getSenderId());
                dataDetail.put("content", data.getContent());
                dataDetail.put("create_time", data.getCreateTime());
                dataDetail.put(IObjectData.ID, data.getFeedId());
                oneData.put("data", dataDetail);
                dataList.add(oneData);
            }
        }
        recordInfo.put("data", dataList);
        list.add(recordInfo);
        return list;
    }


    protected void fillRecordHeader(Map<String, Object> recordInfo, IObjectDescribe describe) {
        recordInfo.put("api_name", "sale_log");
        describe.setDisplayName(I18N.text(I18NKey.SALE_RECORD));
    }

    protected boolean findFeedListHasPermission() {
        return true;
    }

    private List<Map<String, Object>> fillModifyRecord(IObjectDescribe sourceObject,
                                                       User user, String dataId, Integer limit) {
        List<Map<String, Object>> list = Lists.newArrayList();
        //如果不是主对象
        if (CollectionUtils.empty(detailDescribeList)) {
            MobSearchResult logResult = serviceFacade.mobSearchModifyRecord(sourceObject.getApiName(), dataId,
                    limit, 0, user);
            List<ModifyRecord> modifyRecordList = logResult.getModifyRecordList();
            int totalNumber = logResult.getTotalCount();
            Map<String, Object> map = getStringObjectMap("operation_log", modifyRecordList, totalNumber);
            list.add(map);
        } else {
            //如果是主对象
            List<String> detailApiNameList =
                    detailDescribeList.stream().map(x -> x.getRelatedObjectDescribe().getApiName()).collect(Collectors.toList());
            MobSearchRichResult mobSearchRichResult =
                    serviceFacade.mobSearchModifyRecordForMaster(dataId, sourceObject.getApiName(),
                            detailApiNameList, limit, 0, user);
            list.add(getStringObjectMap("operation_log",
                    mobSearchRichResult.getAllModifyRecordListMap().get(sourceObject.getApiName()),
                    mobSearchRichResult.getTotalCountMap().get(sourceObject.getApiName())));
            //循环加入从对象的修改记录
            for (String apiName : detailApiNameList) {
                Map<String, Object> map = getStringObjectMap("operation_log_" + apiName,
                        mobSearchRichResult.getAllModifyRecordListMap().get(apiName),
                        mobSearchRichResult.getTotalCountMap().get(apiName));
                list.add(map);
            }
        }
        return list;
    }

    @NotNull
    private Map<String, Object> getStringObjectMap(String logApiName, List<ModifyRecord> modifyRecordList, int totalNumber) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("api_name", logApiName);
        ObjectDescribe describe = new ObjectDescribe();
        describe.setApiName(logApiName);
        IFieldDescribe userField = new EmployeeFieldDescribe();
        userField.setApiName("user_id");
        IFieldDescribe messageField = new TextFieldDescribe();
        messageField.setApiName("log_msg");
        IFieldDescribe operationTimeField = new DateTimeFieldDescribe();
        operationTimeField.setApiName("operation_time");
        describe.setFieldDescribes(Lists.newArrayList(userField, messageField, operationTimeField));

        map.put("describe", describe.getContainerDocument());
        map.put("total", totalNumber);
        map.put("offset", 0);
        map.put("limit", totalNumber);

        List<Map<String, Object>> dataList = Lists.newArrayList();
        if (!CollectionUtils.empty(modifyRecordList)) {
            for (ModifyRecord data : modifyRecordList) {
                Map<String, Object> oneData = Maps.newHashMap();
                Map<String, Object> dataDetail = Maps.newHashMap();
                dataDetail.put("user_id", data.getOwner() == null ? null : data.getOwner().getEmployeeID());
                dataDetail.put("log_msg", data.getLogMsg());
                dataDetail.put("operation_time", data.getOperationTime());
                oneData.put("data", dataDetail);
                dataList.add(oneData);
            }
        }
        map.put("data", dataList);
        return map;
    }

    private IRelatedListQuery getRelatedListQuery(String associateObjectDataId, String associateObjectDescribeApiName,
                                                  Integer offset, Integer limit,
                                                  List<RelatedObjectDescribeStructure> associatedDescribes, User user) {
        IRelatedListQuery searchQuery = new RelatedListQuery();
        searchQuery.setDataId(associateObjectDataId);
        searchQuery.setTenantId(user.getTenantId());

        searchQuery.setObjectDescribeAPIName(associateObjectDescribeApiName);
        List<IRelatedListQuery.RelatedObjectQuery> list = Lists.newArrayList();
        List<RelevantTeam> relevantTeams = getRelevantTeams(user.getTenantId());

        Map<String, IObjectDescribe> objectDescribeMap = associatedDescribes.stream()
                .collect(Collectors.toMap(x -> x.getRelatedObjectDescribe().getApiName(), RelatedObjectDescribeStructure::getRelatedObjectDescribe, (x, y) -> x));

        //遍历相关表
        boolean isAdmin = isAdmin(user);
        for (RelatedObjectDescribeStructure associatedDescribe : associatedDescribes) {
            IRelatedListQuery.RelatedObjectQuery objectQuery = getRelatedObjectQuery(offset, limit, user, associatedDescribe, relevantTeams, objectDescribeMap, isAdmin);
            list.add(objectQuery);
        }

        searchQuery.setRelatedObjectQueryList(list);
        return searchQuery;
    }

    private List<RelevantTeam> getRelevantTeams(String tenantId) {
        //查询主对象相关团队成员
        Map<String, List<String>> apiNameAndIdsMap = Maps.newHashMap();
        apiNameAndIdsMap.put(arg.getObjectDescribeApiName(), Lists.newArrayList(arg.getObjectDataId()));
        Map<String, Map<String, List<RelevantTeam>>> teamMemberMaps = serviceFacade.batchFindTeamMember(tenantId, apiNameAndIdsMap);
        if (CollectionUtils.empty(teamMemberMaps) || CollectionUtils.empty(teamMemberMaps.get(arg.getObjectDescribeApiName()))) {
            return Lists.newArrayList();
        }
        return Optional.ofNullable(teamMemberMaps.get(arg.getObjectDescribeApiName()).get(arg.getObjectDataId())).orElse(Lists.newArrayList());
    }

    private IRelatedListQuery.RelatedObjectQuery getRelatedObjectQuery(Integer offset, Integer limit, User user, RelatedObjectDescribeStructure associatedDescribe,
                                                                       List<RelevantTeam> relevantTeams, Map<String, IObjectDescribe> objectDescribeMap, boolean isAdmin) {
        IRelatedListQuery.RelatedObjectQuery objectQuery = new IRelatedListQuery.RelatedObjectQuery();
        objectQuery.setLimit(limit);
        objectQuery.setOffset(offset);
        objectQuery.setRelatedListName(associatedDescribe.getRelatedListName());
        objectQuery.setFieldApiName(associatedDescribe.getFieldApiName());
        IObjectDescribe relatedObjectDescribe = associatedDescribe.getRelatedObjectDescribe();
        objectQuery.setReferenceApiName(relatedObjectDescribe.getApiName());
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(limit);
        searchQuery.setOffset(offset);

        SearchTemplateQuery searchTemplateQuery = serviceFacade.getSearchTemplateQuery(user, ObjectDescribeExt.of(relatedObjectDescribe),
                "", searchQuery.toJsonString(), false, isAdmin);
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(searchTemplateQuery);
        queryExt.addIsDeletedFalseFilter();
        objectQuery.setFilters(queryExt.getFilters());
        objectQuery.setOrders(queryExt.getConvertOrders());
        objectQuery.setPermissionType(queryExt.getPermissionType());
        if (queryExt.getDataRightsParameter() != null) {
            objectQuery.setLinkAppDataAuthRange(queryExt.getDataRightsParameter().getLinkAppDataAuthRange());
        }

        // 线索转换日志不走权限
        if (AppFrameworkConfig.getRelatedNotNeedDataAuth().contains(associatedDescribe.getRelatedObjectDescribe().getApiName())) {
            objectQuery.setIsNeedDataAuth(false);
            return objectQuery;
        }

        //外部用户需要校验数据权限
        if (user.isOutUser()) {
            objectQuery.setIsNeedDataAuth(true);
            return objectQuery;
        }

        // 管理员不走数据权限
        if (isAdmin) {
            objectQuery.setIsNeedDataAuth(false);
            return objectQuery;
        }

        //如果有lookupRolesPermission，不走数据权限
        boolean lookupRolesPermission = false;
        if (CollectionUtils.notEmpty(relevantTeams)) {
            if (ObjectDescribeExt.of(associatedDescribe.getRelatedObjectDescribe()).isSlaveObject()) {
                Optional<String> masterAPIName = ObjectDescribeExt.of(associatedDescribe.getRelatedObjectDescribe()).getMasterAPIName();
                if (masterAPIName.isPresent() && Objects.nonNull(objectDescribeMap.get(masterAPIName.get()))) {
                    lookupRolesPermission = ObjectDescribeExt.of(objectDescribeMap.get(masterAPIName.get())).isRelatedAndLookupRolesObject(relevantTeams, user, arg.getObjectDescribeApiName());
                }
            } else {
                lookupRolesPermission = ObjectDescribeExt.of(associatedDescribe.getRelatedObjectDescribe()).isRelatedAndLookupRolesObject(relevantTeams, user, arg.getObjectDescribeApiName());
            }
        }
        objectQuery.setIsNeedDataAuth(!lookupRolesPermission);

        return objectQuery;
    }

    private boolean isAdmin(User user) {
        if (user.isOutUser()) {
            return false;
        }
        return user.isSupperAdmin() || serviceFacade.isAdmin(user);
    }

    protected List<Map<String, Object>> getRelatedDataList(List<RelatedObjectDescribeStructure> relatedDescribeList,
                                                           IRelatedListQuery.QueryResult<IObjectData> queryResult) {
        if (CollectionUtils.empty(relatedDescribeList)) {
            return Lists.newArrayList();
        }

        Map<String, IObjectDescribe> relatedObjectMap = Maps.newHashMap();
        relatedDescribeList.forEach(x -> relatedObjectMap.putIfAbsent(x.getRelatedObjectDescribe().getApiName(), x.getRelatedObjectDescribe()));

        Map<String, RelatedObjectResult<IObjectData>> queryResultMap = Maps.newHashMap();
        CollectionUtils.nullToEmpty(queryResult.getResultList()).forEach(x ->
                queryResultMap.put(x.getChildApiName() + "|" + x.getRelatedListName(), x));

        List<Map<String, Object>> result = relatedDescribeList.stream().map(x -> {
            Map<String, Object> map = Maps.newHashMap();
            map.put("describe", ObjectDescribeDocument.handleDescribeCache(arg.getDescribeVersionMap(), x.getRelatedObjectDescribe()));
            map.put("api_name", x.getRelatedObjectDescribe().getApiName());
            map.put("related_list_name", x.getRelatedListName());
            map.put("offset", arg.getOffset());
            map.put("limit", arg.getLimit());

            int totalNumber = 0;
            List<Map<String, ObjectDataDocument>> data = Lists.newArrayList();
            RelatedObjectResult<IObjectData> dataResult = queryResultMap.get(
                    x.getRelatedObjectDescribe().getApiName() + "|" + x.getRelatedListName());
            if (Objects.nonNull(dataResult)) {
                CollectionUtils.nullToEmpty(dataResult.getDataList()).forEach(y -> {
                    Map<String, ObjectDataDocument> dataMap = Maps.newHashMap();
                    dataMap.put("data", ObjectDataDocument.of(y));
                    data.add(dataMap);
                });
                totalNumber = dataResult.getTotalNumber();
            }
            map.put("total", totalNumber);
            map.put("data", data);

            return map;
        }).collect(Collectors.toList());

        return result;
    }

    @Data
    public static class Arg {
        @JSONField(name = "M1")
        String objectDataId;
        @JSONField(name = "M2")
        String objectDescribeApiName;
        @JSONField(name = "M3")
        int limit;
        @JSONField(name = "M4")
        int offset;
        @JSONField(name = "M5")
        Boolean isOnlyUserDefObj = Boolean.FALSE;
        @JSONField(name = "M6")
        Boolean isIncludeLayout = Boolean.FALSE;
        @JSONField(name = "M7")
        Boolean isIncludeDescribe = Boolean.FALSE;
        @JSONField(name = "M8")
        private Map<String, Integer> describeVersionMap;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        @JSONField(name = "M1")
        List<Map<String, Object>> refObjects;
        @JSONField(name = "M2")
        LayoutDocument layout;
        @JSONField(name = "M3")
        ObjectDescribeDocument describe;
    }
}
