package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.button.dto.UpdatesPojo;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DateTimeUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.GlobalVarService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.dataconvert.DataConvertContext;
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverter;
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverterManager;
import com.facishare.paas.appframework.metadata.expression.Expression;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.impl.describe.SelectManyFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.timezone.DateTimeFormatUtils;
import com.facishare.paas.timezone.TimeZoneContextHolder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by linqiuying on 2018/2/4.
 */
@Slf4j
@Component
public class ParseVarService {
    @Autowired
    private GlobalVarService globalVarService;
    @Autowired
    private FieldDataConverterManager fieldDataConverterManager;
    @Autowired
    private OrgService orgService;

    public List<Variable> parseVar(List<Variable> variables, User user) {
        return parseVar(variables, user, false);
    }

    public List<Variable> parseVar(List<Variable> variables, User user, boolean isUseTransValueByGlobalVariable) {
        Map<String, IGlobalVariableDescribe> globalVars = batchGetGlobalVars(calcDependentGlobalConstantsAPIName(variables), user);

        List<IGlobalVariableDescribe> globalVariableDescribeList = Lists.newArrayList();
        globalVars.forEach((x, y) -> globalVariableDescribeList.add(y));

        Map<String, Object> parseValueMap = batchParseValue(globalVariableDescribeList, isUseTransValueByGlobalVariable);

        variables.forEach(variable -> {
            String variableName = variable.getVariableName().replace("$", "");
            IObjectDescribe describe = variable.getDescribe();
            switch (variable.getType()) {
                case GLOBAL_CONSTANT:
                    IGlobalVariableDescribe globalVariableDescribe = globalVars.get(variableName);
                    variable.setValue(parseValueMap.get(variableName));
                    variable.setFieldType(globalVariableDescribe.getType());
                    break;
                case OBJECT_FIELD:
                    IFieldDescribe fieldDescribe = getFieldDescribeSilently(describe, variableName);
                    if (fieldDescribe != null) {
                        variable.setValue(variable.getData().get(variableName));
                        variable.setFieldType(fieldDescribe.getType());
                    }
                    break;
                case VAR_FIELD:
                    fillVariableValueForVarField(variable, variableName);
                    break;
                case BUTTON_VARIABLES:
                    fillVariableValueForButtonVariables(variable, variableName, user);
                    break;
                case CURRENT_OBJECT:
                    // 当前对象变量处理，值为整个对象数据
                    variable.setValue(ObjectDataExt.toMap(variable.getData()));
                    break;
                default:
            }
        });
        return variables;
    }

    public List<Variable> parseVarForDisplay(List<Variable> variables, User user) {
        Map<String, IGlobalVariableDescribe> globalVars = batchGetGlobalVars(calcDependentGlobalConstantsAPIName(variables), user);
        List<IGlobalVariableDescribe> globalVariableDescribeList = Lists.newArrayList();
        globalVars.forEach((x, y) -> globalVariableDescribeList.add(y));
        Map<String, Object> parseValueMap = batchParseValue(globalVariableDescribeList);
        for (Variable variable : variables) {
            String variableName = variable.getVariableName().replace("$", "");
            IObjectDescribe describe = variable.getDescribe();
            switch (variable.getType()) {
                case GLOBAL_CONSTANT:
                    IGlobalVariableDescribe globalVariableDescribe = globalVars.get(variableName);
                    Object text = parseValue(parseValueMap.get(variableName), globalVariableDescribe.getType());
                    variable.setValue(text);
                    variable.setFieldType(globalVariableDescribe.getType());
                    break;
                case OBJECT_FIELD:
                    IFieldDescribe fieldDescribe = getFieldDescribeSilently(describe, variableName);
                    if (fieldDescribe != null) {
                        variable.setValue(getDisplayValue(fieldDescribe, user, variable.getData()));
                        variable.setFieldType(fieldDescribe.getType());
                    }
                    break;
                case VAR_FIELD:
                    getDisplayValueForVarField(variable, variableName, user);
                    break;
                case BUTTON_VARIABLES:
                    fillVariableValueForButtonVariablesForDisplay(variable, variableName, user);
                    break;
                case CURRENT_OBJECT:
                    variable.setValue(ObjectDataExt.toMap(variable.getData()));
                    break;
                default:
            }
        }
        return variables;
    }

    private void getDisplayValueForVarField(Variable variable, String variableName, User user) {
        List<Map> paramForm = variable.getButton().getParamForm();
        Map<Object, Object> paramFormMap = Maps.newHashMap();
        paramForm.forEach(x -> paramFormMap.put(x.get(IFieldDescribe.API_NAME), x.get(IFieldDescribe.TYPE)));
        Object value = variable.getArgs().get(variableName);
        String type = String.valueOf(paramFormMap.get(variableName));
        variable.setValue(parseValue(value, type));
        if (IFieldType.DEPARTMENT.equals(type)) {
            List<QueryDeptInfoByDeptIds.DeptInfo> deptInfoList = orgService.getDeptInfoNameByIds(user.getTenantId(), user.getUserId(), (List<String>) value);
            if (CollectionUtils.notEmpty(deptInfoList)) {
                deptInfoList.forEach(x -> variable.setValue(x.getDeptName()));
            }
        }
        if (IFieldType.EMPLOYEE.equals(type)) {
            List<UserInfo> userNameByIds = orgService.getUserNameByIds(user.getTenantId(), user.getUserId(), (List<String>) value);
            if (CollectionUtils.notEmpty(userNameByIds)) {
                userNameByIds.forEach(x -> variable.setValue(x.getName()));
            }
        }
        if (IFieldType.SELECT_ONE.equals(type)) {
            IFieldDescribe selectOneFieldDescribe = variable.getDescribe().getFieldDescribe(StringUtils.substringAfterLast(variableName, "form_"));
            List<ISelectOption> selectOneOptions = ((SelectOneFieldDescribe) selectOneFieldDescribe).getSelectOptions();
            Map<String, ISelectOption> selectOneOptionMaps = selectOneOptions.stream().collect(Collectors.toMap(a -> a.getValue(), a -> a));
            if (Objects.nonNull(selectOneOptionMaps.get(value))) {
                if (SelectOne.OPTION_OTHER_VALUE.equals(value)) {
                    variable.setValue(selectOneOptionMaps.get(value).getLabel() + "：" + variable.getArgs().get(variableName + "__o"));
                } else {
                    variable.setValue(selectOneOptionMaps.get(value).getLabel());
                }
            }
        }
        if (IFieldType.SELECT_MANY.equals(type)) {
            IFieldDescribe selectManyFieldDescribe = variable.getDescribe().getFieldDescribe(StringUtils.substringAfterLast(variableName, "form_"));
            List<ISelectOption> selectManyOptions = ((SelectManyFieldDescribe) selectManyFieldDescribe).getSelectOptions();
            Map<String, ISelectOption> selectManyOptionMaps = selectManyOptions.stream().collect(Collectors.toMap(a -> a.getValue(), a -> a));
            if (Objects.nonNull(value)) {
                StringBuilder selectManyValue = new StringBuilder();
                ((List) value).forEach(x -> {
                    if (SelectMany.OPTION_OTHER_VALUE.equals(x)) {
                        selectManyValue.append(selectManyOptionMaps.get(x).getLabel()).append("：").append(variable.getArgs().get(variableName + "__o")).append("，");
                    } else {
                        selectManyValue.append(selectManyOptionMaps.get(x).getLabel()).append("，");
                    }
                });
                variable.setValue(StringUtils.substringBeforeLast(selectManyValue.toString(), "，"));
            }
        }

        for (Map<String, Object> field : paramForm) {
            String apiName = String.valueOf(field.get(IFieldDescribe.API_NAME));
            if (apiName.equals(variableName)) {
                variable.setFieldType(String.valueOf(field.get(IFieldDescribe.TYPE)));
            }
        }
    }

    private Object parseValue(Object value, String type) {
        if (DateTimeUtils.isGrayTimeZone()) {
            try {
                return DateTimeFormatUtils.formatWithTimezoneInfo(value, TimeZoneContextHolder.getTenantTimeZone(), type);
            } catch (Exception e) {
                log.warn("formatISO fail, value:{}, type:{}", value, type, e);
                return value;
            }
        }
        return ObjectDataExt.parseVale(value, type);
    }

    private void fillVariableValueForVarField(Variable variable, String variableName) {
        variable.setValue(variable.getArgs().get(variableName));
        List<Map> paramForm = variable.getButton().getParamForm();
        for (Map<String, Object> field : paramForm) {
            String apiName = String.valueOf(field.get(IFieldDescribe.API_NAME));
            if (apiName.equals(variableName)) {
                variable.setFieldType(String.valueOf(field.get(IFieldDescribe.TYPE)));
            }
        }
    }

    private void fillVariableValueForButtonVariablesForDisplay(Variable variable, String variableName, User user) {
        if ("var_executor".equals(variableName)) {
            List<UserInfo> userNameByIds = orgService.getUserNameByIds(user.getTenantId(), user.getUserId(), Collections.singletonList(user.getUserId()));
            if (CollectionUtils.notEmpty(userNameByIds)) {
                userNameByIds.forEach(x -> variable.setValue(x.getName()));
            }
            variable.setFieldType(IFieldType.EMPLOYEE);
            return;
        }
        if ("var_execution_time".equals(variableName)) {
            if (DateTimeUtils.isGrayTimeZone()) {
                Object text = parseValue(System.currentTimeMillis(), IFieldType.DATE_TIME);
                variable.setValue(text);
                variable.setFieldType(IFieldType.DATE_TIME);
                return;
            }
            variable.setValue(parseValue(System.currentTimeMillis(), IFieldType.DATE_TIME));
            variable.setFieldType(IFieldType.DATE_TIME);
        }

    }


    private void fillVariableValueForButtonVariables(Variable variable, String variableName, User user) {
        if (!"var_executor".equals(variableName)) {
            return;
        }
        variable.setValue(Lists.newArrayList(user.getUserIdOrOutUserIdIfOutUser()));
        variable.setFieldType(IFieldType.EMPLOYEE);
    }

    private Object getDisplayValue(IFieldDescribe fieldDescribe, User user, IObjectData objectData) {
        FieldDataConverter fieldDataConverter = fieldDataConverterManager.getFieldDataConverter(fieldDescribe.getType());
        //发送crm消息，暂不支持多区域
        String value = fieldDataConverter.convertFieldData(objectData, fieldDescribe, DataConvertContext.of(user, true, true));
        if (Objects.equals(fieldDescribe.getType(), IFieldType.PERCENTILE)) {
            return value + "%";
        }
        return value;
    }

    public Object getData(String apiName, Map<String, Object> args, IObjectData objectData, User user, IObjectDescribe describe, IUdefButton button) {
        Variable variable = new Variable(apiName, describe, objectData, button, args);
        return parseVar(Lists.newArrayList(variable), user).get(0).getValue();
    }

    public Object getDisplayData(String apiName, Map<String, Object> args, IObjectData objectData, User user, IObjectDescribe describe, IUdefButton button) {
        Variable variable = new Variable(apiName, describe, objectData, button, args);
        return parseVarForDisplay(Lists.newArrayList(variable), user).get(0).getValue();
    }

    public Variable getVar(String apiName, Map<String, Object> args, IObjectData objectData, User user, IObjectDescribe describe, IUdefButton button) {
        Variable variable = new Variable(apiName, describe, objectData, button, args);
        return parseVar(Lists.newArrayList(variable), user).get(0);
    }

    public List<Object> getDataList(List<String> apiName, Map<String, Object> args, IObjectData objectData, User user, IObjectDescribe describe, IUdefButton button) {
        List<Variable> varList = getVarList(apiName, args, objectData, user, describe, button);
        return varList.stream().map(x -> x.getValue()).collect(Collectors.toList());
    }

    public List<Variable> getVarList(List<String> apiName, Map<String, Object> args, IObjectData objectData, User user, IObjectDescribe describe, IUdefButton button) {
        return getVarList(apiName, args, objectData, user, describe, button, false);
    }

    public List<Variable> getVarList(List<String> apiName, Map<String, Object> args, IObjectData objectData, User user, IObjectDescribe describe, IUdefButton button, boolean isUseTransValueByGlobalVariable) {
        List<Variable> variables = apiName.stream().map(x -> new Variable(x, describe, objectData, button, args)).collect(Collectors.toList());
        return parseVar(variables, user, isUseTransValueByGlobalVariable);
    }

    public List<Variable> getVarListByField(List<UpdatesPojo.Field> fieldList, Map<String, Object> args, IObjectData objectData, User user, IObjectDescribe describe, IUdefButton button) {
        List<Variable> variables = fieldList
                .stream()
                .map(x -> new Variable(x.getField(), Expression.of(String.valueOf(x.getValue())).parseVariableNames().get(0), describe, objectData, button, args))
                .collect(Collectors.toList());
        return parseVar(variables, user);
    }

    private Map<String, IGlobalVariableDescribe> batchGetGlobalVars(List<String> apiNames, User user) {
        Map<String, IGlobalVariableDescribe> ret = Maps.newHashMap();
        if (CollectionUtils.notEmpty(apiNames)) {
            ret = globalVarService.findGlobalVariables(user.getTenantId(), apiNames);
        }

        return ret;
    }

    private Map<String, Object> batchParseValue(List<IGlobalVariableDescribe> globalVariableDescribeList) {
        return batchParseValue(globalVariableDescribeList, false);
    }

    private Map<String, Object> batchParseValue(List<IGlobalVariableDescribe> globalVariableDescribeList, boolean isUseTransValueByGlobalVariable) {
        Map<String, Object> ret = Maps.newHashMap();
        for (IGlobalVariableDescribe x : globalVariableDescribeList) {
            ret.put(x.getApiName(), globalVarService.parseValue(x, isUseTransValueByGlobalVariable));
        }
        return ret;
    }

    private IFieldDescribe getFieldDescribeSilently(IObjectDescribe iObjectDescribe, String apiName) {
        return iObjectDescribe.getFieldDescribe(apiName);
    }

    private List<String> calcDependentGlobalConstantsAPIName(List<Variable> variables) {
        return variables
                .stream()
                .filter(x -> Variable.Type.GLOBAL_CONSTANT == x.getType())
                .map(y -> y.getVariableName())
                .collect(Collectors.toList());
    }

}
