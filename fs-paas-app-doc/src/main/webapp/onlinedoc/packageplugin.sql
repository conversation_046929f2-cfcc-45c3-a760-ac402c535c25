-- Drop table

-- DROP TABLE dev_package_plugin;

CREATE TABLE IF NOT EXISTS dev_package_plugin (
    id varchar(64) NOT NULL,
    tenant_id varchar(16) NOT NULL,
    define_type varchar(128) NOT NULL,
    app_type varchar(128) NOT NULL,
    name varchar(128) NOT NULL,
    icon varchar(128) NOT NULL,
    icon_sign varchar(128) NOT NULL,
    plugin_api_name varchar(128) NOT NULL,
    is_active bool NOT NULL DEFAULT false,
    is_binding bool NOT NULL DEFAULT false,
    extra_info jsonb NULL,
    dev_info jsonb NULL,
    runtime_data jsonb NULL,
    object_describe_api_name varchar(100) NOT NULL,
    created_by varchar(32) NOT NULL,
    last_modified_by varchar(32) NOT NULL,
    create_time int8 NOT NULL,
    last_modified_time int8 NOT NULL,
    version int8 NOT NULL,
    is_deleted int2 NOT NULL,
    sys_modified_time int8 NOT NULL,
    CONSTRAINT dev_package_plugin_pkey PRIMARY KEY (id, tenant_id)
    );

-- Table Index
CREATE UNIQUE INDEX IF NOT EXISTS dev_package_plugin_index_tid_pluginapiname
    ON dev_package_plugin
    USING btree (tenant_id, plugin_api_name)
    WHERE (is_deleted = 0);


-- Table Triggers
DROP TRIGGER IF EXISTS x_audit_changes ON dev_package_plugin;
CREATE TRIGGER x_audit_changes after
    insert
    or
delete
or
update
    on
        dev_package_plugin for each row execute function f_change_detail(
    'id',
    'tenant_id',
    'object_describe_api_name');

DROP TRIGGER IF EXISTS x_system_changes ON dev_package_plugin;
CREATE TRIGGER x_system_changes BEFORE
    INSERT OR UPDATE
                  ON dev_package_plugin FOR EACH ROW EXECUTE PROCEDURE public.f_system_change();