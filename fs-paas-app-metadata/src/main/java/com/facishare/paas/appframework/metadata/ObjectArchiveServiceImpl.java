package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.ScheduleConfigDTO;
import com.facishare.paas.appframework.metadata.dto.ScheduleConfigDocument;
import com.facishare.paas.metadata.api.IObjectArchiveRuleInfo;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectArchiveRuleService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Service("objectArchiveService")
public class ObjectArchiveServiceImpl implements ObjectArchiveService {
    @Autowired
    private IObjectArchiveRuleService objectArchiveRuleService;
    @Resource
    private DescribeLogicService describeLogicService;

    @Override
    public void create(User user, List<IObjectArchiveRuleInfo> ruleList) {
        try {
            ruleList.forEach(ScheduleConfigDTO::convert2ScheduleConfig);
            objectArchiveRuleService.createArchiveRule(ruleList);
        } catch (MetadataServiceException e) {
            log.warn("Error in createArchiveRule", e);
            throw new ValidateException(e.getMessage());
        }
    }

    @Override
    public void update(User user, List<IObjectArchiveRuleInfo> ruleList) {
        try {
            ruleList.forEach(ScheduleConfigDTO::convert2ScheduleConfig);
            objectArchiveRuleService.setArchiveRule(ruleList);
        } catch (MetadataServiceException e) {
            log.warn("Error in setArchiveRule", e);
            throw new ValidateException(e.getMessage());
        }
    }

    @Override
    public List<IObjectArchiveRuleInfo> list(User user, String ruleName, String ruleApiName) {
        try {
            List<IObjectArchiveRuleInfo> objectArchiveRuleList = objectArchiveRuleService.getObjectArchiveRule(user.getTenantId(), ruleName, ruleApiName, -1);
            fillObjectDisplayName(user, objectArchiveRuleList);
            return objectArchiveRuleList;
        } catch (MetadataServiceException e) {
            log.warn("Error in getArchiveRule", e);
            throw new ValidateException(e.getMessage());
        }
    }

    @Override
    public List<IObjectArchiveRuleInfo> find(User user, String ruleApiName) {
        try {
            List<IObjectArchiveRuleInfo> objectArchiveRuleList = objectArchiveRuleService.getObjectArchiveRuleByRuleApiName(user.getTenantId(), ruleApiName, ActionContextExt.of(user).getContext());
            objectArchiveRuleList.forEach(ScheduleConfigDocument::convert2ScheduleConfigDTO);
            fillObjectDisplayName(user, objectArchiveRuleList);
            return objectArchiveRuleList;
        } catch (MetadataServiceException e) {
            log.warn("Error in getArchiveRule", e);
            throw new ValidateException(e.getMessage());
        }
    }

    private void fillObjectDisplayName(User user, List<IObjectArchiveRuleInfo> objectArchiveRuleList) {
        Set<String> apiNameSet = Sets.newHashSet();
        objectArchiveRuleList.forEach(rule -> {
            apiNameSet.add(rule.getSourceApiName());
            apiNameSet.add(rule.getTargetApiName());
        });
        Map<String, String> apiNameAndDisplayNameMap = describeLogicService.queryDisplayNameByApiNames(user.getTenantId(), Lists.newArrayList(apiNameSet));
        objectArchiveRuleList.forEach(rule -> {
            rule.setSourceDisplayName(apiNameAndDisplayNameMap.get(rule.getSourceApiName()));
            rule.setTargetDisplayName(apiNameAndDisplayNameMap.get(rule.getTargetApiName()));
        });
    }

    @Override
    public void disable(User user, String ruleApiName) {
        try {
            objectArchiveRuleService.invalidRule(user.getTenantId(), ruleApiName);
        } catch (MetadataServiceException e) {
            log.warn("Error in invalidRule", e);
            throw new ValidateException(e.getMessage());
        }
    }

    @Override
    public void disableByObjectApiName(User user, String objectApiName) {
        try {
            List<IObjectArchiveRuleInfo> objectArchiveRuleList = objectArchiveRuleService.getObjectArchiveRule(user.getTenantId(), null, null, -1);
            objectArchiveRuleList.stream().filter(rule -> StringUtils.equalsAny(objectApiName,  rule.getSourceApiName(), rule.getTargetApiName()))
                    .map(IObjectArchiveRuleInfo::getRuleApiName)
                    .forEach(ruleApiName -> disable(user, ruleApiName));
        } catch (MetadataServiceException e) {
            log.warn("Error in disableByObjectApiName", e);
            throw new ValidateException(e.getMessage());
        }
    }

    @Override
    public void enable(User user, String ruleApiName) {
        try {
            List<IObjectArchiveRuleInfo> objectArchiveRuleInfos = find(user, ruleApiName);
            Set<String> apiNameSet = Sets.newHashSet();
            objectArchiveRuleInfos.forEach(rule -> {
                apiNameSet.add(rule.getSourceApiName());
                apiNameSet.add(rule.getTargetApiName());
            });
            Map<String, IObjectDescribe> describeMap = describeLogicService.findObjectsWithoutCopy(user.getTenantId(), apiNameSet);
            apiNameSet.forEach(apiName -> {
                IObjectDescribe objectDescribe = describeMap.get(apiName);
                if (!describeMap.containsKey(apiName) || (Objects.nonNull(objectDescribe) && !objectDescribe.isActive())) {
                    throw new ValidateException(I18NExt.text(I18NKey.SOURCE_OR_TARGET_OBJECT_UNEXIST_OR_DISABLED, apiName));
                }
            });
            objectArchiveRuleService.validRule(user.getTenantId(), ruleApiName);
        } catch (MetadataServiceException e) {
            log.warn("Error in validRule", e);
            throw new ValidateException(e.getMessage());
        }
    }

    @Override
    public void delete(User user, String ruleApiName) {
        try {
            objectArchiveRuleService.bulkDeleteByRuleApiName(user.getTenantId(), Lists.newArrayList(ruleApiName));
        } catch (MetadataServiceException e) {
            log.warn("Error in deleteArchiveRule", e);
            throw new ValidateException(e.getMessage());
        }
    }
}
