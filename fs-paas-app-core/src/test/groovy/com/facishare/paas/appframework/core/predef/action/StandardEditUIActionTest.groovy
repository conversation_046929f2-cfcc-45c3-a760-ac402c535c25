package com.facishare.paas.appframework.core.predef.action


import com.facishare.paas.appframework.core.model.User

/**
 * create by <PERSON><PERSON><PERSON> on 2020/09/06
 */
class StandardEditUIActionTest extends BaseActionTest {

    def "test_AddUIAction"() {
        expect:
        1 == 1
        /*
        given: "init user"
        user = buildUser(tenantId, userId)
        and: "init RequestContext"
        initRequestContext(user)
        and: "init arg"
        def arg = JacksonUtils.fromJson(json, StandardEditUIAction.Arg)
        println arg
        when:
        def result = execute(apiName, StandardAction.EditUI.toString(), arg)
        then:
        println result
        noExceptionThrown()
        where:
        tenantId | userId | apiName           | json                                           || _
        "74255"  | "1000" | "object_2mmga__c" | '''{"object_id":"5f530ea58b29860001834926"}''' || _
        */
    }

    def initRequestContext(User user) {
        requestContext = buildRequestContext(user.getTenantId(), user.getUserId())
    }
}
