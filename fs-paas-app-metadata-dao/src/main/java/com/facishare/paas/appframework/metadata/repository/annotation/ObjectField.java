package com.facishare.paas.appframework.metadata.repository.annotation;

import java.lang.annotation.*;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ObjectField {
    String apiName();

    String description() default "";

    String helpText() default "";

    String label() default "";

    boolean isRequired() default false;

    boolean isUnique() default false;
}
