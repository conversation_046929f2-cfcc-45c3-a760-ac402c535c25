package com.facishare.paas.appframework.metadata.expression;

import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.GlobalVarService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.expression.ExpressionService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by liyiguang on 2018/5/17.
 */
@Slf4j
public class BulkFieldUpdateCalculator extends AbstractBulkCalculator {

    private Map<String, FieldUpdateDTO> fieldUpdateDTOMap;
    private Map<String, String> formVariableTypes;


    @Builder
    public BulkFieldUpdateCalculator(
            ExpressionService expressionService, DescribeLogicService describeLogicService,
            GlobalVarService globalVarService, IObjectDescribe describe, List<FieldUpdateDTO> fieldUpdateDTOList, Map<String, String> formVariableTypes) {
        super(expressionService, describeLogicService, globalVarService, describe);
        Objects.requireNonNull(fieldUpdateDTOList);
        this.formVariableTypes = formVariableTypes;

        fieldUpdateDTOMap = Maps.newLinkedHashMap();
        fieldUpdateDTOList.forEach(x -> fieldUpdateDTOMap.put(x.getField(), x));

    }

    @Override
    protected Map<String, String> getExtVariableTypes() {
        return formVariableTypes;
    }

    @Override
    public void initCalculator() {
        fieldUpdateDTOMap.forEach((k, v) -> {
            FieldUpdateCalculator fieldUpdateCalculator = new FieldUpdateCalculator(expressionService, v, describe);
            fieldUpdateCalculator.init();
            calculators.put(fieldUpdateCalculator.key(), fieldUpdateCalculator);
        });
    }

    public void doCalculate(IObjectData data, Map<String, Object> globalVariableData,
                            Map<String, Map<String, IObjectData>> objectDataMap, Map<String, Object> formData,
                            Map<String, String> exchangeRateMap) {
        objectDataMap.putIfAbsent(describe.getApiName(), Maps.newHashMap());
        objectDataMap.get(describe.getApiName()).put(data.getId(), data);

        Map<String, Object> resultMap = Maps.newLinkedHashMap();
        calculators.forEach((k, v) -> {
            v.bindDependentObjectDataId(data, null, exchangeRateMap);
            v.doCalculate(data, globalVariableData, objectDataMap, formData, false);
            Object result = v.getResult();
            fieldUpdateDTOMap.get(k).setResult(result);
            resultMap.put(k, result);
        });
        log.info("calcForm ei:{},on:{},di:{},r:{}", describe.getTenantId(), describe.getApiName(), data.getId(), resultMap);
    }

}
