package com.facishare.paas.appframework.rest.dto.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public interface FeedDataByJson {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        List<FeedData> dataList;
    }

    @Data
    class Result {
        @JSONField(name = "feed_data")
        @JsonProperty(value = "feed_data")
        @SerializedName("feed_data")
        List<FeedData> feedDataList;
    }

    @Data
    class FeedData {
        String dataId;
        String dataType;
        String data;
        String tenantId;
        String userId;
        String displayName;
    }
}
