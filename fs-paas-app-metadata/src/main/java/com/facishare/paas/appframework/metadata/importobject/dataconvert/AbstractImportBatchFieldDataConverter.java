package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * create by z<PERSON><PERSON> on 2019/07/27
 */
@Slf4j
public abstract class AbstractImportBatchFieldDataConverter implements ImportBatchFieldDataConverter {

    protected abstract List<BatchConvertResult> convert(List<BatchConvertData> convertDataList, IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe, DataConverterContext context);

    @Override
    public List<BatchConvertResult> convertFieldData(List<BatchConvertData> convertDataList, IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe, User user) {
        return convertFieldData(convertDataList, objectDescribe, fieldDescribe, DataConverterContext.builder().user(user).build());
    }

    @Override
    public List<BatchConvertResult> convertFieldData(List<BatchConvertData> convertDataList, IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe, DataConverterContext context) {
        List<BatchConvertData> dataListToConvert = convertDataList.stream()
                .filter(Objects::nonNull)
                .filter(x -> Objects.nonNull(x.getObjectData()))
                .collect(Collectors.toList());

        if (CollectionUtils.empty(dataListToConvert)) {
            return Lists.newArrayList();
        }
        try {
            return convert(dataListToConvert, objectDescribe, fieldDescribe, context);
        } catch (Exception e) {
            log.error("convertFieldData error, ei:{}, apiName:{}, fieldApiName:{}, user:{}",
                    context.getTenantId(), fieldDescribe.getDescribeApiName(), fieldDescribe.getApiName(), context.getUser(), e);
            return convertDataList.stream()
                    .map(x -> BatchConvertResult.of(ConvertResult.buildError(I18N.text(I18NKey.UNKNOWN_EXCEPTION)), x.getNo()))
                    .collect(Collectors.toList());
        }
    }

    protected Set<String> getSetValues(List<IObjectData> dataList, IFieldDescribe fieldDescribe) {
        Set<String> results = Sets.newHashSet();
        for (IObjectData data : dataList) {
            ObjectDataExt objectDataExt = ObjectDataExt.of(data);
            String name = objectDataExt.getStringValueInImport(fieldDescribe.getApiName());
            if (Strings.isNullOrEmpty(name)) {
                if (objectDataExt.containsField(fieldDescribe.getApiName())) {
                    data.set(fieldDescribe.getApiName(), null);
                }
                continue;
            }
            Collections.addAll(results, name.split("\\|"));
        }
        return results;
    }
}
