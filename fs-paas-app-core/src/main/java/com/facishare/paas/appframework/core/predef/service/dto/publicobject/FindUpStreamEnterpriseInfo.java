package com.facishare.paas.appframework.core.predef.service.dto.publicobject;

import com.facishare.paas.appframework.metadata.publicobject.module.SimpleEnterpriseInfo;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/5/29
 */
public interface FindUpStreamEnterpriseInfo {

    @Data
    class Arg {
        private String describeApiName;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        private SimpleEnterpriseInfo enterpriseInfo;
    }
}
