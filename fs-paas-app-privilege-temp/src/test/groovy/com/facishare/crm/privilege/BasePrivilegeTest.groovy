package com.facishare.crm.privilege

import com.facishare.crm.privilege.rest.proxy.privilege.api.pojo.EntityOpennessPojo
import com.facishare.crm.valueobject.SessionContext
import org.springframework.test.context.ContextConfiguration
import spock.lang.Shared
import spock.lang.Specification

/** 权限子模块基本测试类,在权限测试中需引入此类。
 * Created by yusb on 2017/4/18.
 */
//@ContextConfiguration(locations = "classpath:privilege-config.xml")
abstract class BasePrivilegeTest extends Specification{

  @Shared SessionContext ctx
  @Shared EntityOpennessPojo spockEntityOpennessPojo = EntityOpennessPojo.builder().build()
  @Shared Long SPOCK_TENANT_ID_LONG = Long.parseLong("54819")
  @Shared Integer SPOCK_USER_ID_INT = 1000
  @Shared String SPOCK_TENANT_ID_STRING = "54819"
  @Shared String SPOCK_USER_ID_STRING = "1000"
  @Shared String SPOCK_APP_ID = "CRM"
  @Shared String SPOCK_TEST_STRING = "Spock Test"
  @Shared String SPOCK_DESCRIBE_API_NAME = "object_test_for_spock__c"

  // Run before all the tests:
  def setupSpec() {
    ctx = new SessionContext()
    ctx.setEId(SPOCK_TENANT_ID_LONG)
    ctx.setUserId(SPOCK_USER_ID_INT)
    print "authContext initialization done!" +
        " Ei:" + ctx.EId + "UserId:" + ctx.userId

    spockEntityOpennessPojo.setAppId(SPOCK_APP_ID)
    spockEntityOpennessPojo.setCreateTime(System.currentTimeMillis())
    spockEntityOpennessPojo.setCreator(SPOCK_TEST_STRING)
    spockEntityOpennessPojo.setDelFlag(Boolean.FALSE)
    spockEntityOpennessPojo.setEntityId(SPOCK_DESCRIBE_API_NAME)
    spockEntityOpennessPojo.setPermission(EntityOpennessPojo.PERMISSION_READ_OR_WRITE)
    spockEntityOpennessPojo.setScope(EntityOpennessPojo.SCOPE_ALL)
    spockEntityOpennessPojo.setModifier(SPOCK_TEST_STRING)
    spockEntityOpennessPojo.setModifyTime(System.currentTimeMillis())
    spockEntityOpennessPojo.setTenantId(SPOCK_TENANT_ID_STRING)
    print "spockEntityOpennessPojo initialization done!" +
        " entityId:" + spockEntityOpennessPojo.entityId +
        " permission:" + spockEntityOpennessPojo.permission + " scope:"+spockEntityOpennessPojo.scope

    print "setup after all tests!"
  }

  // Run after all the tests, even after failures:
  def cleanupSpec() {
    print "Cleanup after all tests!"
  }
}