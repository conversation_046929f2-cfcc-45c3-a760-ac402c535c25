package com.facishare.paas.appframework.metadata.restdriver.dto;

import com.facishare.paas.appframework.metadata.dto.sfa.BaseResult;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by liyiguang on 2017/9/17.
 */
public interface FindBySearchQuery {

    @Getter
    @Setter
    class Result extends BaseResult {
        Value value;

        @Data
        public static class Value{
            List items;
            Page page;

            @Data
            public static class Page{
                Integer totalCount;
                Integer pageCount;
            }
        }
    }

}
