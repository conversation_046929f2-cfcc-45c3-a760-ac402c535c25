package com.facishare.paas.appframework.core.predef.facade.dto;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.facade.ObjectDataMapping;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.RichTextExt;
import com.facishare.paas.appframework.metadata.repository.model.MtChangeOrderRule;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiFunction;

/**
 * Created by zhaooju on 2023/4/6
 */
public interface CreateChangeOrder {

    @Data
    class Arg {
        private IObjectDescribe describe;
        private IObjectData masterData;
        private Map<String, List<IObjectData>> detailToAddMap;
        private Map<String, List<IObjectData>> detailToUpdateMap;
        private Map<String, List<IObjectData>> detailToDeleteMap;

        //主对象的原始数据（进编辑页之前查到的数据，只需要传页面上有变更的字段）
        private IObjectData originalData;
        //从对象的原始数据（进编辑页之前查到的数据，没有变更的从对象不用传，其他从对象传参规则：
        //1、更新的只传变更的字段；
        //2、没有更新的只传_id；
        //）
        private Map<String, List<IObjectData>> originalDetails;

        private Map<String, Object> callBackData;


        private Boolean useSnapshot;
        private Boolean skipApprovalFlow;
        private Boolean skipDataStatusValidate;
        private Boolean acceptNonBlockingResult;
        private RequestContext.BizInfo bizInfo;

        public Table<String, String, IObjectData> toOriginalDataTable() {
            HashBasedTable<String, String, IObjectData> table = HashBasedTable.create();
            table.put(describe.getApiName(), originalData.getId(), originalData);
            originalDetails.forEach((apiName, dataList) -> {
                dataList.forEach(data -> {
                    table.put(apiName, data.getId(), data);
                });
            });
            return table;
        }

    }

    @Data
    class Result {
        private final boolean success;
        private final String message;
        private final String changeOrderApiName;
        private final String changeOrderDataId;

        private Result(boolean success, String message, String changeOrderApiName, String changeOrderDataId) {
            this.success = success;
            this.message = message;
            this.changeOrderApiName = changeOrderApiName;
            this.changeOrderDataId = changeOrderDataId;
        }

        public static Result buildSuccess(String changeOrderApiName, String changeOrderDataId) {
            return new Result(true, null, changeOrderApiName, changeOrderDataId);
        }

        public static Result buildError(String message) {
            return new Result(false, message, null, null);
        }
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class ChangeOrderDataTuple {
        public static final String CHANGED_TYPE_ADD = "add";
        public static final String CHANGED_TYPE_DELETED = "deleted";
        public static final String CHANGED_TYPE_UPDATE = "update";


        private final IObjectData changeObjectData;
        private final IObjectData originalObjectData;

        public static CreateChangeOrder.ChangeOrderDataTuple buildChangeObjectDataByRule(MtChangeOrderRule.ObjectFieldMapper objectFieldMapper,
                                                                                         IObjectDescribe changeDescribe, IObjectData objectData,
                                                                                         IObjectData originalData, String changedType,
                                                                                         BiFunction<String, IObjectData, ObjectDataMapping.MappingData> triMappingFunction) {
            String originalApiName = objectData.getDescribeApiName();
            ObjectDataMapping.MappingData mappingData = triMappingFunction.apply(originalApiName, objectData);
            IObjectData changeObjectData = mappingData.getObjectData();
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(changeDescribe);

            if (describeExt.isSlaveObject() && !Strings.isNullOrEmpty(changedType)) {
                changeObjectData.setOrderBy(objectData.getOrderBy());
                // 数据没有变，不需要记录变更类型
                if (!(CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_UPDATE.equals(changedType)
                        && !mappingData.isChange())) {
                    changeObjectData.set(ObjectDataExt.CHANGED_TYPE, changedType);
                }
            }
            if (Objects.isNull(originalData)) {
                return CreateChangeOrder.ChangeOrderDataTuple.of(changeObjectData, null);
            }
            // 给 changeObjectData 中需要有记录原值的字段赋值
            ObjectDataExt.of(objectData).toMap().forEach((apiName, value) -> {
                if (objectFieldMapper.recordOriginalValueBySource(originalApiName, apiName)) {
                    String originalFieldName = FieldDescribeExt.getOriginalFieldName(apiName);
                    changeObjectData.set(originalFieldName, originalData.get(apiName));
                }
            });

            ObjectDataMapping.MappingData changedMappingData = triMappingFunction.apply(originalApiName, originalData);
            IObjectData changedObjectData = changedMappingData.getObjectData();
            ObjectDataExt diffData = ObjectDataExt.of(ObjectDataExt.of(changeObjectData).diff(changedObjectData, changeDescribe));
            if (describeExt.isSlaveObject()) {
                describeExt.getActiveFieldDescribeSilently(ObjectDataExt.ORIGINAL_DETAIL_DATA).ifPresent(it -> {
                    String dataId = originalData.getId();
                    changeObjectData.set(it.getApiName(), dataId);
                    diffData.set(it.getApiName(), dataId);
                });
            } else {
                describeExt.getActiveFieldDescribeSilently(ObjectDataExt.ORIGINAL_DATA).ifPresent(it -> {
                    String dataId = originalData.getId();
                    changeObjectData.set(it.getApiName(), dataId);
                    diffData.set(it.getApiName(), dataId);
                });
            }
            fillRichTextAbstractName(diffData, changeDescribe, changedObjectData);
            return CreateChangeOrder.ChangeOrderDataTuple.of(changeObjectData, diffData);
        }

        private static void fillRichTextAbstractName(ObjectDataExt diffData, IObjectDescribe changeDescribe,
                                                     IObjectData changedObjectData) {
            for (String apiName : Sets.newHashSet(diffData.toMap().keySet())) {
                IFieldDescribe fieldDescribe = changeDescribe.getFieldDescribe(apiName);
                if (Objects.isNull(fieldDescribe)) {
                    continue;
                }
                if (RichTextExt.isProcessableRichText(fieldDescribe)) {
                    String richTextAbstractName = RichTextExt.getRichTextAbstractName(fieldDescribe.getApiName());
                    diffData.set(richTextAbstractName, changedObjectData.get(richTextAbstractName));
                }
            }
        }
    }

    @FunctionalInterface
    interface TriMappingFunction {
        ObjectDataMapping.MappingData apply(String apiName, IObjectData objectData, boolean flag);
    }
}
