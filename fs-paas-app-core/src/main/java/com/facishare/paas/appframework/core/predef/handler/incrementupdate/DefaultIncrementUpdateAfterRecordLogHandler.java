package com.facishare.paas.appframework.core.predef.handler.incrementupdate;

import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@HandlerProvider(name = "defaultIncrementUpdateAfterRecordLogHandler")
public class DefaultIncrementUpdateAfterRecordLogHandler implements IncrementUpdateHandler {

    @Autowired
    private ServiceFacade serviceFacade;

    @Override
    public Result handle(HandlerContext context, Arg arg) {
        recordLog(context, arg);
        return new Result();
    }


    private void recordLog(HandlerContext context, Arg arg) {
        Map<String, Object> diffMap;
        IObjectData dbData = ObjectDataExt.of(arg.getDbObjectData()).copy();
        IObjectData data = ObjectDataExt.of(arg.getObjectData()).copy();
        if (arg.getSnapshotResult() != null) {
            diffMap = arg.getSnapshotResult().getDiffMap();
            //将快照合并到dbData中供修改记录使用
            ObjectDataExt.of(dbData).putAll(arg.getSnapshotResult().getSnapshot().getMasterSnapshot());
        } else {
            diffMap = ObjectDataExt.of(dbData).diff(data, arg.getObjectDescribe());
        }
        String bizId = RequestContextManager.getContext().getBizId();

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() ->
                        serviceFacade.log(context.getUser(), EventType.MODIFY, ActionType.Modify, arg.getObjectDescribe(), data,
                                diffMap, dbData, null, null, bizId, getLogExtendsInfo(context)))
                .run();
    }

    private Map<String, Object> getLogExtendsInfo(HandlerContext context) {
        Map<String, Object> map = Maps.newHashMap();
        map.put(LogInfo.TRIGGER_WORK_FLOW, !context.skipWorkFlow());
        map.put(LogInfo.TRIGGER_APPROVAL_FLOW, !context.skipApprovalFlow());
        return map;
    }
}
