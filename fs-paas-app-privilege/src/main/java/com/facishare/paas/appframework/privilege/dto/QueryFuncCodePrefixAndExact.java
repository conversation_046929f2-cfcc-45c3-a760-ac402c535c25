package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.Builder;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

public interface QueryFuncCodePrefixAndExact {

    @Data
    @Builder
    class Arg {
        private AuthContext authContext;
        private List<String> exactFuncCodes;
        private String prefixFuncCode;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {
        private Map<String, Boolean> result;
    }

}
