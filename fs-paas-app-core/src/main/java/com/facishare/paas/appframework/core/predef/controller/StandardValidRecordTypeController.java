package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class StandardValidRecordTypeController extends AbstractStandardController<StandardValidRecordTypeController.Arg, StandardValidRecordTypeController.Result> {
    @Override
    protected Result doService(Arg arg) {
        Result result = new Result();
        if (arg.isForImport()) {
            if (ObjectDescribeExt.UnsupportedImportRecordTypeDescribes.contains(controllerContext.getObjectApiName())) {
                return result;
            }
        }
        List<IRecordTypeOption> recordTypeList = serviceFacade.findValidRecordTypeList(controllerContext.getObjectApiName(), controllerContext.getUser());
        List<Map> dataList = recordTypeList.stream()
                .map(recordType -> (((DocumentBasedBean) recordType).getContainerDocument()))
                .collect(Collectors.toList());
        result.setRecordList(dataList);
        return result;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Data
    public static class Result {
        @JSONField(name = "M8")
        @JsonProperty("record_list")
        private List<Map> recordList;
    }

    @Data
    public static class Arg {
        //        @JSONField(name = "M1")
//        String describeApiName;
        @JSONField(name = "M2")
        @SerializedName("is_only_active")
        @JsonProperty("is_only_active")
        boolean onlyActive;

        @SerializedName("is_import")
        @JsonProperty("is_import")
        boolean forImport;

        @SerializedName("ext_params")
        @JsonProperty("ext_params")
        private Map<String, Object> extParams;
    }
}

