package com.facishare.paas.appframework.common.service.model;

/**
 * <AUTHOR>
 * @time 2023-05-09 15:20
 * @Description
 */
public class ExtraChannel {
    private int outChannelType;
    private String appId;
    private int receiverChannelType;

    public ExtraChannel() {
    }

    public int getOutChannelType() {
        return outChannelType;
    }

    public void setOutChannelType(int outChannelType) {
        this.outChannelType = outChannelType;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public int getReceiverChannelType() {
        return receiverChannelType;
    }

    public void setReceiverChannelType(int receiverChannelType) {
        this.receiverChannelType = receiverChannelType;
    }

    @Override
    public String toString() {
        return "{\"outChannelType\":" + outChannelType +
                ",\"appId\":\"" + appId + "\"" +
                ",\"receiverChannelType\":" + receiverChannelType +
                "}";
    }
}
