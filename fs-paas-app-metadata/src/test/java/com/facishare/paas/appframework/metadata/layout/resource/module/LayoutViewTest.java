package com.facishare.paas.appframework.metadata.layout.resource.module;

import com.facishare.paas.appframework.metadata.layout.resource.LayoutViewResourceProxyImpl;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class LayoutViewTest {

  @Mock
  private LayoutViewResourceProxyImpl.LayoutViewDTO layoutViewDTO;

  @BeforeEach
  void setUp() {
    // 设置基本的mock行为
    when(layoutViewDTO.getApiName()).thenReturn("test_api");
    when(layoutViewDTO.getLabel()).thenReturn("Test Label");
    when(layoutViewDTO.getDefaultShow()).thenReturn(true);
    when(layoutViewDTO.getDefaultView()).thenReturn(false);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试from方法正常场景
   */
  @Test
  @DisplayName("正常场景 - 从DTO创建LayoutView")
  void testFrom_Success() {
    // 执行被测试方法
    LayoutView result = LayoutView.from(layoutViewDTO);

    // 验证结果
    assertNotNull(result);
    assertEquals("test_api", result.getApiName());
    assertEquals("Test Label", result.getLabel());
    assertTrue(result.getDefaultShow());
    assertFalse(result.getDefaultView());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试from方法null参数
   */
  @Test
  @DisplayName("边界场景 - from方法null参数")
  void testFrom_NullParameter() {
    // 执行被测试方法并验证异常
    assertThrows(NullPointerException.class, () -> {
      LayoutView.from(null);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试fromList方法正常场景
   */
  @Test
  @DisplayName("正常场景 - 从DTO列表创建LayoutView列表")
  void testFromList_Success() {
    // 准备测试数据
    LayoutViewResourceProxyImpl.LayoutViewDTO dto2 = mock(LayoutViewResourceProxyImpl.LayoutViewDTO.class);
    when(dto2.getApiName()).thenReturn("test_api_2");
    when(dto2.getLabel()).thenReturn("Test Label 2");
    when(dto2.getDefaultShow()).thenReturn(false);
    when(dto2.getDefaultView()).thenReturn(true);

    List<LayoutViewResourceProxyImpl.LayoutViewDTO> dtoList = Lists.newArrayList(layoutViewDTO, dto2);

    // 执行被测试方法
    List<LayoutView> result = LayoutView.fromList(dtoList);

    // 验证结果
    assertNotNull(result);
    assertEquals(2, result.size());
    
    LayoutView view1 = result.get(0);
    assertEquals("test_api", view1.getApiName());
    assertEquals("Test Label", view1.getLabel());
    assertTrue(view1.getDefaultShow());
    assertFalse(view1.getDefaultView());

    LayoutView view2 = result.get(1);
    assertEquals("test_api_2", view2.getApiName());
    assertEquals("Test Label 2", view2.getLabel());
    assertFalse(view2.getDefaultShow());
    assertTrue(view2.getDefaultView());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试fromList方法空列表
   */
  @Test
  @DisplayName("边界场景 - fromList方法空列表")
  void testFromList_EmptyList() {
    // 执行被测试方法
    List<LayoutView> result = LayoutView.fromList(Lists.newArrayList());

    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试fromList方法null参数
   */
  @Test
  @DisplayName("边界场景 - fromList方法null参数")
  void testFromList_NullParameter() {
    // 执行被测试方法
    List<LayoutView> result = LayoutView.fromList(null);

    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getDefaultShow方法Boolean处理
   */
  @Test
  @DisplayName("正常场景 - 测试getDefaultShow方法Boolean处理")
  void testGetDefaultShow_BooleanHandling() {
    // 测试true值
    when(layoutViewDTO.getDefaultShow()).thenReturn(true);
    LayoutView view1 = LayoutView.from(layoutViewDTO);
    assertTrue(view1.getDefaultShow());

    // 测试false值
    when(layoutViewDTO.getDefaultShow()).thenReturn(false);
    LayoutView view2 = LayoutView.from(layoutViewDTO);
    assertFalse(view2.getDefaultShow());

    // 测试null值
    when(layoutViewDTO.getDefaultShow()).thenReturn(null);
    LayoutView view3 = LayoutView.from(layoutViewDTO);
    assertFalse(view3.getDefaultShow());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getDefaultView方法Boolean处理
   */
  @Test
  @DisplayName("正常场景 - 测试getDefaultView方法Boolean处理")
  void testGetDefaultView_BooleanHandling() {
    // 测试true值
    when(layoutViewDTO.getDefaultView()).thenReturn(true);
    LayoutView view1 = LayoutView.from(layoutViewDTO);
    assertTrue(view1.getDefaultView());

    // 测试false值
    when(layoutViewDTO.getDefaultView()).thenReturn(false);
    LayoutView view2 = LayoutView.from(layoutViewDTO);
    assertFalse(view2.getDefaultView());

    // 测试null值
    when(layoutViewDTO.getDefaultView()).thenReturn(null);
    LayoutView view3 = LayoutView.from(layoutViewDTO);
    assertFalse(view3.getDefaultView());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试equals和hashCode方法
   */
  @Test
  @DisplayName("正常场景 - 测试equals和hashCode方法")
  void testEqualsAndHashCode() {
    // 创建两个相同的LayoutView对象
    LayoutView view1 = LayoutView.from(layoutViewDTO);
    LayoutView view2 = LayoutView.from(layoutViewDTO);

    // 验证equals和hashCode
    assertEquals(view1, view2);
    assertEquals(view1.hashCode(), view2.hashCode());
    
    // 验证自反性
    assertEquals(view1, view1);
    
    // 验证与null的比较
    assertNotEquals(view1, null);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试toString方法
   */
  @Test
  @DisplayName("正常场景 - 测试toString方法")
  void testToString() {
    // 执行被测试方法
    LayoutView view = LayoutView.from(layoutViewDTO);
    String result = view.toString();

    // 验证结果
    assertNotNull(result);
    assertTrue(result.contains("test_api"));
    assertTrue(result.contains("Test Label"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getter方法
   */
  @Test
  @DisplayName("正常场景 - 测试getter方法")
  void testGetters() {
    // 执行被测试方法
    LayoutView view = LayoutView.from(layoutViewDTO);

    // 验证getter方法
    assertEquals("test_api", view.getApiName());
    assertEquals("Test Label", view.getLabel());
    assertTrue(view.getDefaultShow());
    assertFalse(view.getDefaultView());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试不同数据类型的处理
   */
  @Test
  @DisplayName("正常场景 - 测试不同数据类型")
  void testDifferentDataTypes() {
    // 准备测试数据 - 空字符串和特殊字符
    when(layoutViewDTO.getApiName()).thenReturn("");
    when(layoutViewDTO.getLabel()).thenReturn("特殊字符@#$%");
    
    // 执行被测试方法
    LayoutView view = LayoutView.from(layoutViewDTO);

    // 验证结果
    assertEquals("", view.getApiName());
    assertEquals("特殊字符@#$%", view.getLabel());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试fromList方法包含null元素
   */
  @Test
  @DisplayName("边界场景 - fromList方法包含null元素")
  void testFromList_ContainsNullElement() {
    // 准备测试数据
    List<LayoutViewResourceProxyImpl.LayoutViewDTO> dtoList = Lists.newArrayList(layoutViewDTO, null);

    // 执行被测试方法并验证异常
    assertThrows(NullPointerException.class, () -> {
      LayoutView.fromList(dtoList);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试对象不可变性
   */
  @Test
  @DisplayName("正常场景 - 测试对象不可变性")
  void testImmutability() {
    // 创建LayoutView对象
    LayoutView view = LayoutView.from(layoutViewDTO);

    // 验证字段是final的（通过反射或行为验证）
    assertNotNull(view.getApiName());
    assertNotNull(view.getLabel());
    
    // 多次调用getter应该返回相同的值
    assertEquals(view.getApiName(), view.getApiName());
    assertEquals(view.getLabel(), view.getLabel());
    assertEquals(view.getDefaultShow(), view.getDefaultShow());
    assertEquals(view.getDefaultView(), view.getDefaultView());
  }
}
