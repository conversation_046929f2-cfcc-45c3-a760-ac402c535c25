package com.facishare.paas.appframework.common.util;

import com.github.autoconf.helper.ConfigHelper;

public abstract class RuntimeUtils {
    private static final String appName = ConfigHelper.getProcessInfo().getName();
    private static final String serverIp = ConfigHelper.getProcessInfo().getIp();
    private static final String profile = ConfigHelper.getProcessInfo().getProfile();

    public static String getAppName() {
        return appName;
    }

    public static String getServerIp() {
        return serverIp;
    }

    public static String getProfile() {
        return profile;
    }

}
