package com.facishare.paas.appframework.core.timezone;

import com.facishare.paas.appframework.core.timezone.annotation.ObjectDataField;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * create by z<PERSON><PERSON> on 2021/06/17
 */
public class ListMapObjectDataFieldConvert extends AbstractObjectDataFieldConvert {

    public static ListMapObjectDataFieldConvert getInstance() {
        return Helper.INSTANCE;
    }

    private ListMapObjectDataFieldConvert() {
    }

    @Override
    public ObjectDataField.Type getType() {
        return ObjectDataField.Type.LIST_MAP;
    }

    @Override
    public <T> T convert2SystemZone(T value, Function<String, IObjectDescribe> findDescribe) {
        if (Objects.isNull(value)) {
            return null;
        }
        Map<String, List<IObjectData>> detailData = toDetailData(value);

        Map<String, List<IObjectData>> objectDetails = Maps.newHashMap();
        detailData.forEach((apiName, dataList) -> {
            IObjectDescribe describe = findDescribe.apply(apiName);
            List<IObjectData> objectDataList = ObjectDataExt.convertDateFieldValueToSystemZone(describe, dataList);
            objectDetails.put(apiName, objectDataList);
        });
        return toDetailEntity(objectDetails, value);
    }

    @Override
    public <T> T convert2CustomZone(T value, Function<String, IObjectDescribe> findDescribe) {
        if (Objects.isNull(value)) {
            return null;
        }
        Map<String, List<IObjectData>> detailData = toDetailData(value);

        Map<String, List<IObjectData>> objectDetails = Maps.newHashMap();
        detailData.forEach((apiName, dataList) -> {
            IObjectDescribe describe = findDescribe.apply(apiName);
            List<IObjectData> objectDataList = ObjectDataExt.convertDateFieldValueToCustomZone(describe, dataList);
            objectDetails.put(apiName, objectDataList);
        });
        return toDetailEntity(objectDetails, value);
    }

    private static final class Helper {
        private final static ListMapObjectDataFieldConvert INSTANCE = new ListMapObjectDataFieldConvert();
    }
}
