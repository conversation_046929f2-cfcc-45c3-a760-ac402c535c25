/**
 * <AUTHOR> create by liy on 2024/6/24
 */
public interface OnlineDocPersonalPlugin {

    //企业授权
    default EnterpriseAuth.Result enterpriseAuth(EnterpriseAuth.Arg arg) {
        return new EnterpriseAuth.Result();
    }

    //获取个人授权url
    GetPersonalAuthUrl.Result getPersonalAuthUrl(GetPersonalAuthUrl.Arg arg, FunctionContext context);

    //处理callback回来的数据
    ActionCallback.Result actionCallback(ActionCallback.Arg arg, FunctionContext context);

    //请求文件列表
    QueryFileList.Result queryFileList(QueryFileList.Arg arg, FunctionContext context);

    //获取预览url
    GetPreviewUrl.Result getPreviewUrl(GetPreviewUrl.Arg arg, FunctionContext context);
}