<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
    <context:component-scan base-package="com.facishare.paas.appframework"/>
    <import resource="classpath:spring/cores.xml"/>
    <import resource="classpath:spring/action.xml"/>
    <import resource="classpath:spring/app-metadata.xml"/>
    <import resource="classpath:spring/payment.xml"/>
    <import resource="classpath:spring/restdriver.xml"/>
    <import resource="classpath:privilege-temp.xml"/>
    <import resource="classpath:spring/global-transaction-tcc.xml"/>
</beans>