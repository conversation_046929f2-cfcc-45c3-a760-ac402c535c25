package com.facishare.paas.appframework.core.predef.service.dto.messagebox;

import com.facishare.paas.appframework.common.service.dto.FindTodoList;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

public interface FindTodoMessageList {
    @Data
    class Arg {
        Integer status;
        String type;
        String apiName;
        Integer limit;
        Integer offset;
    }

    @Data
    @Builder
    class Result {
        List<MessageDocument> messageList;

        public static Result from(FindTodoList.Result result) {
            if (Objects.isNull(result) || result.isEmpty()) {
                return Result.builder().messageList(Lists.newArrayList()).build();
            }

            List<MessageDocument> resultList = Lists.newArrayList();
            result.getMessageList().forEach(a->{
                FindTodoList.ExtendField extendField = Optional.ofNullable(a.getExtendFieldObj())
                        .orElse(FindTodoList.ExtendField.builder().build());
                MessageDocument item = MessageDocument.builder()
                        .apiName(a.getApiName())
                        .createTime(a.getCreateTime())
                        .id(a.getId())
                        .objectId(a.getObjectId())
                        .status(a.getStatus())
                        .title(a.getTitle())
                        .type(a.getType())
                        .url(a.getUrl())
                        .extendField(extendField)
                        .displayName(a.getDescribeDisplayName())
                        .objectName(a.getObjectName())
                        .build();
                resultList.add(item);
            });

            return Result.builder().messageList(resultList).build();
        }
    }


    @Data
    @Builder
    class MessageDocument {
        String apiName;
        Long createTime;
        String id;
        String objectId;
        String status;
        String title;
        String type;
        String url;
        FindTodoList.ExtendField extendField;

        String displayName;
        String objectName;
    }


}
