package com.facishare.paas.appframework.core.predef.service.dto.tag;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface SaveTag {
    @Data
    class Arg {
        String describeApiName;
        List<TagData> tagList;
    }

    @Data
    @Builder
    class Result {

    }

    @Data
    class TagData {
        private String dataId;
        private List<TagInfo> tagInfoList;
    }

    @Data
    class TagInfo {
        private String tagId;
        private String level1;
        private String level2;
        private String level3;
    }
}
