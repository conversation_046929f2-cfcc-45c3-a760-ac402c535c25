{"swagger": "2.0", "info": {"title": "AppFrameWork TEMPORARY_RIGHTS API", "description": "PaaS 临时权限 API", "version": "1.0"}, "host": "************:8234", "schemes": ["HTTP"], "basePath": "/FHH/EM1HNCRM/API/v1/object/data_privilege", "produces": ["application/json"], "paths": {"/service/queryTemporaryRights": {"post": {"tags": ["Temporary_Rights API"], "summary": "query temporary rights", "description": "查询临时权限列表", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/queryTemporaryRightsArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/queryTemporaryRightsResult"}}}}}, "/service/TemporaryPermissionSwitch": {"post": {"tags": ["Temporary_Rights API"], "summary": "enable or disable temporary rights switch ", "description": "控制临时权限开关，若关闭清空所有临时权限数据", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/TemporaryPermissionSwitchArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/TemporaryPermissionSwitchResult"}}}}}, "/service/UpdateOwnerTemporaryRights": {"post": {"tags": ["Temporary_Rights API"], "summary": "update owners temporary Rights", "description": "更新dataId下拥有临时数据权限的人员", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UpdateOwnerTemporaryRightsArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/UpdateOwnerTemporaryRightsResult"}}}}}}, "definitions": {"TemporaryPrivilegeInfoOwners": {"type": "object", "properties": {"key(userId)": {"type": "string"}, "value(userLabel)": {"type": "string"}}}, "TemporaryPrivilegeInfo": {"type": "object", "properties": {"object_describe_apiName": {"type": "string"}, "object_describe_label": {"type": "string"}, "object_data_id": {"type": "string"}, "object_data_label": {"type": "string"}, "owners": {"$ref": "#/definitions/TemporaryPrivilegeInfoOwners"}}}, "queryTemporaryRightsArg": {"type": "object", "properties": {"object_describe_apiName": {"type": "string"}, "object_data_id": {"type": "string"}, "page_size": {"type": "integer"}, "page_number": {"type": "integer"}}}, "queryTemporaryRightsResult": {"type": "object", "properties": {"resultList": {"type": "array", "items": {"$ref": "#/definitions/TemporaryPrivilegeInfo"}}, "totalNumber": {"type": "integer"}}}, "TemporaryPermissionSwitchArg": {"type": "object", "properties": {"switch": {"type": "boolean"}, "level": {"type": "integer"}, "validity_term": {"type": "integer"}}}, "TemporaryPermissionSwitchResult": {"type": "object", "properties": {"success": {"type": "boolean"}, "errorInfo": {"type": "string"}}}, "UpdateOwnerTemporaryRightsArg": {"type": "object", "properties": {"describe_api_name": {"type": "string"}, "object_data_id": {"type": "string"}, "owners": {"type": "array", "items": {"type": "string"}}}}, "UpdateOwnerTemporaryRightsResult": {"type": "object", "properties": {"success": {"type": "boolean"}, "errorInfo": {"type": "string"}}}}}