package com.facishare.paas.appframework.privilege.model;

import com.facishare.paas.appframework.privilege.util.FunctionInfoConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by luxin on 2018/7/14.
 */
@Data
@Builder
@AllArgsConstructor
public class ObjectPrivilegeInfo implements Cloneable {
    private List<FunctionInfo> functionInfoList;

    // 对象的apiName
    private String apiName;
    // 对象的中文名
    private String displayName;
    // 对象是否具有字段权限
    private Boolean isHaveFieldPrivilege;
    // 查看列表的权限码
    private String viewListFuncCode;

    @Override
    public ObjectPrivilegeInfo clone() throws CloneNotSupportedException {
        ObjectPrivilegeInfo result = (ObjectPrivilegeInfo) super.clone();
        result.functionInfoList = ((List<FunctionInfo>) ((ArrayList<FunctionInfo>) functionInfoList).clone());

        return result;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FunctionInfo {
        private String displayName;
        private String funcCode;
        private Boolean isEditable;
    }

}
