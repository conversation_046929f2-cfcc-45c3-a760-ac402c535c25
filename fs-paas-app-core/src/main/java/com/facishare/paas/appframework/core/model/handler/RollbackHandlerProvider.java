package com.facishare.paas.appframework.core.model.handler;

import java.lang.annotation.*;

/**
 * RollbackHandler注解，用于定义Handler的唯一标识
 * Created by zhouwr on 2023/7/17.
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RollbackHandlerProvider {
    /**
     * 接口编码
     */
    String interfaceCode();

    /**
     * 对象ApiName
     */
    String objectApiName();
}
