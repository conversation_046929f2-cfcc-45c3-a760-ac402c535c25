package com.facishare.paas.appframework.core.predef.service.dto.objectImport;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/05/10
 */
public interface GetImportObject {
    @Data
    class Arg {
        private String objectCode;
        private String detailObjectApiName;    // 联合导入时使用
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "M1")
        private GetImportObjectList.ImportObjectDTO importObject;
    }
}
