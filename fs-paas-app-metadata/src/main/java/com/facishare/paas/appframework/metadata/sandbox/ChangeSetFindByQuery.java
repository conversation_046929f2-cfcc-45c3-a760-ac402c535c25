package com.facishare.paas.appframework.metadata.sandbox;

import lombok.*;

import java.util.List;
import java.util.Map;


public interface ChangeSetFindByQuery {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        private String enterpriseAccount;
        private Long enterpriseId;
        private String tableName;
        private Long operatorId;
        private SearchQueryInfo searchQueryInfo;
        private List<String> fields;
        private List<String> sortFields;
        private String sortType;
        private Integer pageSize;
        private Integer pageIndex;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class SearchQueryInfo {
        private List<Filter> filters;
    }
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Filter {
        private String fieldName;
        private List<String> values;
        private String fieldType;
        private String operator;
    }

    @Builder
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private List<Map<String, Object>> dataList;
        private Integer count;
    }
}


