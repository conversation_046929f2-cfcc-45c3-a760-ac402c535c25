package com.facishare.paas.appframework.metadata.layout.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class FiltersComponentInfoTest {

  private FiltersComponentInfo filtersComponentInfo;

  @BeforeEach
  void setUp() {
    filtersComponentInfo = new FiltersComponentInfo();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试FiltersComponentInfo的无参构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试无参构造函数")
  void testDefaultConstructor() {
    FiltersComponentInfo info = new FiltersComponentInfo();
    assertNotNull(info, "无参构造函数创建的对象不应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试FiltersComponentInfo的Map构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = Maps.newHashMap();
    map.put(IFiltersComponentInfo.FIELDS, Lists.newArrayList("field1", "field2"));
    map.put(IFiltersComponentInfo.RENDER_TYPE, "dropdown");
    map.put(IFiltersComponentInfo.PAGE_TYPE, "list");
    
    FiltersComponentInfo info = new FiltersComponentInfo(map);
    assertNotNull(info, "Map构造函数创建的对象不应为空");
    assertEquals(2, info.getFields().size(), "应正确设置字段列表");
    assertEquals("dropdown", info.getRenderType(), "应正确设置渲染类型");
    assertEquals("list", info.getPageType(), "应正确设置页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ofFilterInfos静态工厂方法
   */
  @Test
  @DisplayName("正常场景 - 测试ofFilterInfos静态工厂方法")
  void testOfFilterInfos() {
    List<String> filterInfos = Lists.newArrayList("name", "status", "created_date");
    String pageType = "list_page";
    
    FiltersComponentInfo info = FiltersComponentInfo.ofFilterInfos(filterInfos, pageType);
    
    assertNotNull(info, "静态工厂方法创建的对象不应为空");
    assertEquals(3, info.getFields().size(), "应正确设置字段数量");
    assertTrue(info.getFields().contains("name"), "应包含name字段");
    assertTrue(info.getFields().contains("status"), "应包含status字段");
    assertTrue(info.getFields().contains("created_date"), "应包含created_date字段");
    assertEquals(pageType, info.getPageType(), "应正确设置页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ofFilterInfos静态工厂方法 - 空列表场景
   */
  @Test
  @DisplayName("边界场景 - 测试ofFilterInfos静态工厂方法处理空列表")
  void testOfFilterInfos_EmptyList() {
    List<String> emptyFilterInfos = Lists.newArrayList();
    String pageType = "empty_page";
    
    FiltersComponentInfo info = FiltersComponentInfo.ofFilterInfos(emptyFilterInfos, pageType);
    
    assertNotNull(info, "静态工厂方法创建的对象不应为空");
    assertNotNull(info.getFields(), "字段列表不应为空");
    assertTrue(info.getFields().isEmpty(), "字段列表应为空");
    assertEquals(pageType, info.getPageType(), "应正确设置页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ofFilterInfos静态工厂方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试ofFilterInfos静态工厂方法处理null值")
  void testOfFilterInfos_NullValues() {
    FiltersComponentInfo info = FiltersComponentInfo.ofFilterInfos(null, null);
    
    assertNotNull(info, "静态工厂方法创建的对象不应为空");
    assertNotNull(info.getFields(), "字段列表不应为空");
    assertTrue(info.getFields().isEmpty(), "null字段列表应返回空列表");
    assertNull(info.getPageType(), "null页面类型应为null");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFields方法
   */
  @Test
  @DisplayName("正常场景 - 测试getFields方法")
  void testGetFields() {
    // 初始状态应为空列表
    List<String> initialFields = filtersComponentInfo.getFields();
    assertNotNull(initialFields, "初始字段列表不应为空");
    assertTrue(initialFields.isEmpty(), "初始字段列表应为空");
    
    // 通过Map构造函数设置字段
    List<String> testFields = Lists.newArrayList("filter1", "filter2", "filter3");
    Map<String, Object> map = Maps.newHashMap();
    map.put(IFiltersComponentInfo.FIELDS, testFields);
    
    FiltersComponentInfo info = new FiltersComponentInfo(map);
    List<String> fields = info.getFields();
    
    assertNotNull(fields, "字段列表不应为空");
    assertEquals(3, fields.size(), "字段列表大小应为3");
    assertEquals(testFields, fields, "字段列表应与设置的相同");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getRenderType方法
   */
  @Test
  @DisplayName("正常场景 - 测试getRenderType方法")
  void testGetRenderType() {
    // 初始状态应为null
    assertNull(filtersComponentInfo.getRenderType(), "初始渲染类型应为null");
    
    // 通过Map构造函数设置渲染类型
    Map<String, Object> map = Maps.newHashMap();
    map.put(IFiltersComponentInfo.RENDER_TYPE, "checkbox");
    FiltersComponentInfo info = new FiltersComponentInfo(map);
    assertEquals("checkbox", info.getRenderType(), "应正确获取渲染类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getPageType方法
   */
  @Test
  @DisplayName("正常场景 - 测试getPageType方法")
  void testGetPageType() {
    // 初始状态应为null
    assertNull(filtersComponentInfo.getPageType(), "初始页面类型应为null");
    
    // 通过Map构造函数设置页面类型
    Map<String, Object> map = Maps.newHashMap();
    map.put(IFiltersComponentInfo.PAGE_TYPE, "detail");
    FiltersComponentInfo info = new FiltersComponentInfo(map);
    assertEquals("detail", info.getPageType(), "应正确获取页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试copy方法
   */
  @Test
  @DisplayName("正常场景 - 测试copy方法")
  void testCopy() {
    // 设置一些属性
    List<String> fields = Lists.newArrayList("field1", "field2");
    Map<String, Object> map = Maps.newHashMap();
    map.put(IFiltersComponentInfo.FIELDS, fields);
    map.put(IFiltersComponentInfo.RENDER_TYPE, "multiselect");
    map.put(IFiltersComponentInfo.PAGE_TYPE, "search");
    
    FiltersComponentInfo original = new FiltersComponentInfo(map);
    IFiltersComponentInfo copied = original.copy();
    
    assertNotNull(copied, "复制的对象不应为空");
    assertNotSame(original, copied, "复制的对象应是不同的实例");
    assertEquals(original.getFields().size(), copied.getFields().size(), "复制的对象应有相同的字段数量");
    assertEquals(original.getRenderType(), copied.getRenderType(), "复制的对象应有相同的渲染类型");
    assertEquals(original.getPageType(), copied.getPageType(), "复制的对象应有相同的页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口实现
   */
  @Test
  @DisplayName("正常场景 - 测试接口实现")
  void testInterfaceImplementation() {
    // 验证实现了IFiltersComponentInfo接口
    assertTrue(filtersComponentInfo instanceof IFiltersComponentInfo, 
               "应实现IFiltersComponentInfo接口");
    
    // 验证实现了JsonCompatible接口
    assertTrue(filtersComponentInfo instanceof com.facishare.paas.metadata.api.JsonCompatible, 
               "应实现JsonCompatible接口");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试序列化版本号
   */
  @Test
  @DisplayName("正常场景 - 测试序列化版本号")
  void testSerialVersionUID() {
    // 验证类有序列化版本号（通过反射检查）
    try {
      java.lang.reflect.Field field = FiltersComponentInfo.class.getDeclaredField("serialVersionUID");
      field.setAccessible(true);
      long serialVersionUID = field.getLong(null);
      assertEquals(4690285183161718860L, serialVersionUID, "序列化版本号应正确");
    } catch (Exception e) {
      fail("应该有serialVersionUID字段");
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试继承的AbstractComponentInfoDocument功能
   */
  @Test
  @DisplayName("正常场景 - 测试继承功能")
  void testInheritedFunctionality() {
    // 验证继承自AbstractComponentInfoDocument
    assertTrue(filtersComponentInfo instanceof AbstractComponentInfoDocument, 
               "应继承自AbstractComponentInfoDocument");
    
    // 通过Map构造函数测试继承的功能
    Map<String, Object> map = Maps.newHashMap();
    map.put("custom_field", "custom_value");
    map.put(IFiltersComponentInfo.FIELDS, Lists.newArrayList("test_field"));
    
    FiltersComponentInfo info = new FiltersComponentInfo(map);
    assertEquals(1, info.getFields().size(), "应正确处理字段列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试复杂的过滤器配置
   */
  @Test
  @DisplayName("正常场景 - 测试复杂过滤器配置")
  void testComplexFilterConfiguration() {
    // 创建复杂的过滤器配置
    List<String> complexFields = Lists.newArrayList(
        "name", "status", "created_date", "modified_date", 
        "owner", "department", "priority", "category"
    );
    
    FiltersComponentInfo info = FiltersComponentInfo.ofFilterInfos(complexFields, "advanced_search");
    
    // 验证复杂配置
    assertEquals(8, info.getFields().size(), "应有8个过滤字段");
    assertEquals("advanced_search", info.getPageType(), "页面类型应正确");
    
    // 验证所有字段都存在
    for (String field : complexFields) {
      assertTrue(info.getFields().contains(field), "应包含字段: " + field);
    }
    
    // 测试复制功能
    IFiltersComponentInfo copied = info.copy();
    assertEquals(info.getFields().size(), copied.getFields().size(), "复制的对象应有相同的字段数量");
    assertEquals(info.getPageType(), copied.getPageType(), "复制的对象应有相同的页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试多种渲染类型
   */
  @Test
  @DisplayName("正常场景 - 测试多种渲染类型")
  void testVariousRenderTypes() {
    String[] renderTypes = {"dropdown", "checkbox", "radio", "multiselect", "daterange", "search"};
    
    for (String renderType : renderTypes) {
      Map<String, Object> map = Maps.newHashMap();
      map.put(IFiltersComponentInfo.RENDER_TYPE, renderType);
      map.put(IFiltersComponentInfo.FIELDS, Lists.newArrayList("test_field"));
      
      FiltersComponentInfo info = new FiltersComponentInfo(map);
      assertEquals(renderType, info.getRenderType(), "应正确设置渲染类型: " + renderType);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试特殊字符字段名
   */
  @Test
  @DisplayName("边界场景 - 测试特殊字符字段名")
  void testSpecialCharacterFieldNames() {
    // 测试包含特殊字符的字段名
    List<String> specialFields = Lists.newArrayList(
        "field_with_underscore", 
        "field-with-dash", 
        "field.with.dot", 
        "field__c", 
        "<EMAIL>"
    );
    
    FiltersComponentInfo info = FiltersComponentInfo.ofFilterInfos(specialFields, "special_test");
    
    assertEquals(5, info.getFields().size(), "应正确处理特殊字符字段名");
    
    for (String field : specialFields) {
      assertTrue(info.getFields().contains(field), "应包含特殊字符字段: " + field);
    }
  }
}
