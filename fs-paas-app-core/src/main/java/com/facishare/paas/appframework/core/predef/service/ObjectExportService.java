package com.facishare.paas.appframework.core.predef.service;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.export.ExportApiTemplate;
import com.facishare.paas.appframework.core.predef.service.dto.export.ExportAuditLogTemplate;
import com.facishare.paas.appframework.core.predef.service.dto.export.ExportDTO;
import com.facishare.paas.appframework.core.predef.service.dto.export.ExportLoginLogTemplate;
import com.facishare.paas.appframework.core.predef.service.dto.objectImport.GetBatchPrintExportState;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.log.dto.AuditLog;
import com.facishare.paas.appframework.log.dto.AuditLogInfo;
import com.facishare.paas.appframework.log.dto.LoginLog;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 导出服务
 * @date 2021/9/13-14:41
 */
@ServiceModule("object_export")
@Component
@Slf4j
public class ObjectExportService {


    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private LogService logService;
    @Autowired
    private OrgService orgService;

    @ServiceMethod("isSupportBatchPrintExport")
    public GetBatchPrintExportState.Result isSupportBatchPrintExport(GetBatchPrintExportState.Arg arg, ServiceContext context) {
        return GetBatchPrintExportState.Result.builder()
                .supportPrintExport(AppFrameworkConfig.isSupportBatchPrintExport(context.getTenantId(), arg.getObjectApiName()))
                .build();
    }


    @ServiceMethod("exportApiVerify")
    public ExportDTO.VerifyResult exportApiVerify(ExportDTO.Arg arg, ServiceContext context) {
        ExportDTO.VerifyResult verifyResult = new ExportDTO.VerifyResult();
        if (Objects.isNull(arg)) {
            log.warn("exportVerify error,please check arg,tenantId:{}", context.getTenantId());
            throw new ValidateException("please check arg");
        }
        log.info("exportVerify start,arg:{}", JSON.toJSONString(arg));
        ExportDTO.ExportArg exportArg = JSON.parseObject(arg.getSearchQuery(), ExportDTO.ExportArg.class);
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(context.getTenantId(), Sets.newHashSet(exportArg.getDescribeApiNames()));
        long count = 0;
        if (CollectionUtils.notEmpty(describeMap) && CollectionUtils.notEmpty(describeMap.values())) {
            count = describeMap.values().stream().map(IObjectDescribe::getFieldDescribes)
                    .flatMap(Collection::stream).filter(x -> !ExportApiTemplate.blackFields.contains(x.getApiName()))
                    .count();
        }
        verifyResult.setTotalCount((int) count);
        return verifyResult;
    }


    @ServiceMethod("findApiExportHeader")
    public ExportDTO.ExportHeaderResult findApiExportHeader(ExportDTO.Arg arg, ServiceContext context) {
        if (Objects.isNull(arg)) {
            log.warn("findApiExportHeader error,please check arg,tenantId:{}", context.getTenantId());
            throw new ValidateException("please check arg");
        }
        log.info("findExportHeader start,arg:{}", JSON.toJSONString(arg));
        ExportDTO.ExportArg exportArg = JSON.parseObject(arg.getSearchQuery(), ExportDTO.ExportArg.class);
        List<IObjectDescribe> objectDescribes = describeLogicService.findDescribeListWithoutFields(context.getTenantId(), Sets.newHashSet(exportArg.getDescribeApiNames()));
        List<ExportDTO.GroupHeader> groupHeaders = Lists.newArrayList();

        //构造第一个sheet模板
        ExportDTO.Group group0 = new ExportDTO.Group();
        group0.setIndex("objectAndFieldApi");
        group0.setName(I18NExt.getOrDefault(I18NKey.EXPORT_API_OBJECT_AND_FIELD, "对象及字段API"));// ignoreI18n
        ExportDTO.GroupHeader groupHeader0 = new ExportDTO.GroupHeader();
        groupHeader0.setGroup(group0);
        groupHeader0.setHeaders(ExportApiTemplate.getApiTemplateHeader());
        groupHeaders.add(groupHeader0);

        //构造导入模板
        for (IObjectDescribe describe : objectDescribes) {
            ExportDTO.GroupHeader groupHeader = new ExportDTO.GroupHeader();
            ExportDTO.Group group = new ExportDTO.Group();
            group.setIndex(describe.getApiName());
            group.setName(describe.getDisplayName());
            groupHeader.setGroup(group);
            groupHeader.setHeaders(ExportApiTemplate.getApiImportTemplateHeader());
            groupHeaders.add(groupHeader);
        }

        ExportDTO.ExportHeaderResult result = new ExportDTO.ExportHeaderResult();
        result.setGroupHeaders(groupHeaders);
        result.setTotalCount(objectDescribes.size());
        return result;
    }


    @ServiceMethod("findApiExportData")
    public ExportDTO.ExportApiDataResult findApiExportData(ExportDTO.Arg arg, ServiceContext context) {
        if (Objects.isNull(arg)) {
            log.warn("findApiExportData error,please check arg,tenantId:{}", context.getTenantId());
            throw new ValidateException("please check arg");
        }
        ExportDTO.ExportApiDataResult result = new ExportDTO.ExportApiDataResult();
        log.info("findApiExportData start,arg:{}", JSON.toJSONString(arg));
        ExportDTO.ExportArg exportArg = JSON.parseObject(arg.getSearchQuery(), ExportDTO.ExportArg.class);
        List<String> describeApiNames = exportArg.getDescribeApiNames();

        ExportDTO.ExportCallBackData callBackData = arg.getCallBackData();
        log.info("findApiExportData callBackData:{}", JSON.toJSONString(callBackData));

        List<String> callBackDataDescribeApiNames = Lists.newArrayList();
        if (Objects.nonNull(callBackData)) {
            callBackDataDescribeApiNames.addAll(callBackData.getDescribeApiNames());
        }
        describeApiNames.removeIf(callBackDataDescribeApiNames::contains);
        if (CollectionUtils.empty(describeApiNames)) {
            result.setEnd(true);
            result.setGroupDataList(Lists.newArrayList());
            return result;
        }

        int pageSize = Math.min(arg.getPageSize(), describeApiNames.size());
        List<String> subDescribeApiName = describeApiNames.subList(0, pageSize);
        List<IObjectDescribe> objectDescribes = describeLogicService.findObjectList(context.getTenantId(), Sets.newHashSet(subDescribeApiName));

        List<ExportDTO.GroupData> groupDataList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(objectDescribes)) {
            objectDescribes.forEach(describe -> {
                //构造第一个sheet的完成数据
                ExportDTO.GroupData groupData = new ExportDTO.GroupData();
                groupData.setIndex("objectAndFieldApi");
                groupData.setDataList(ExportApiTemplate.convertToExportTemplate(describe));
                groupDataList.add(groupData);

                //构造导入对象的模板数据
                ExportDTO.GroupData subData = new ExportDTO.GroupData();
                subData.setIndex(describe.getApiName());
                subData.setDataList(ExportApiTemplate.convertToImportTemplate(describe));
                groupDataList.add(subData);
            });
        }

        result.setGroupDataList(groupDataList);
        callBackDataDescribeApiNames.addAll(subDescribeApiName);
        describeApiNames.removeIf(callBackDataDescribeApiNames::contains);
        if (CollectionUtils.empty(describeApiNames)) {
            result.setEnd(true);
        } else {
            Map<String, Object> callBackDataToMap = ExportDTO.ExportCallBackData.convertCallBackDataToMap(callBackDataDescribeApiNames);
            result.setEnd(false);
            result.setCallBackData(callBackDataToMap);
        }
        return result;
    }

    @ServiceMethod("findLoginExportData")
    public ExportDTO.ExportLoginResult findLoginExportData(ExportDTO.Arg arg, ServiceContext context) {
        ExportDTO.ExportLoginResult result = new ExportDTO.ExportLoginResult();
        LoginLog.Arg loginArg = new LoginLog.Arg();
        LoginLog.LoginInfoCondition condition = JacksonUtils.fromJson(arg.getSearchQuery(), LoginLog.LoginInfoCondition.class);
        loginArg.setLoginCondition(condition);
        loginArg.setPageSize(arg.getPageSize());
        if (Objects.nonNull(arg.getCallBackData())) {
            if (StringUtils.isNotBlank(arg.getCallBackData().getLogId())) {
                loginArg.setId(arg.getCallBackData().getLogId());
            } else {
                return result;
            }
        }
        LoginLog.Result loginLog = logService.getLoginLog(context.getUser(), loginArg);
        if (Objects.nonNull(loginLog) && CollectionUtils.notEmpty(loginLog.getResults())) {
            List<LoginLog.LoginInfo> loginLogResults = loginLog.getResults();
            List<ExportLoginLogTemplate> dataList = loginLogResults.stream()
                    .map(ExportLoginLogTemplate::convertToLoginLogTemplate)
                    .collect(Collectors.toList());
            result.setEnd(Boolean.FALSE.equals(loginLog.getHasMore()));
            result.setDataList(dataList);
            ExportDTO.ExportCallBackData callBackData = new ExportDTO.ExportCallBackData();
            callBackData.setLogId(loginLogResults.get(loginLogResults.size() - 1).getId());
            result.setCallBackData(callBackData.convertCallBackDataToMap());
        }
        return result;
    }

    @ServiceMethod("findAuditLogExportData")
    public ExportDTO.ExportAuditLogResult findAuditLogExportData(ExportDTO.Arg arg, ServiceContext context) {
        log.info("findAuditLogExportData arg:{}", JSON.toJSONString(arg));
        ExportDTO.ExportAuditLogResult result = new ExportDTO.ExportAuditLogResult();
        AuditLog.Arg auditArg = new AuditLog.Arg();
        AuditLogInfo condition = JacksonUtils.fromJson(arg.getSearchQuery(), AuditLogInfo.class);
        if (Objects.isNull(condition)) {
            log.warn("findCRMExportData error,arg is {}", JSON.toJSONString(arg));
            result.setEnd(true);
            return result;
        }
        auditArg.setAuditLogCondition(condition);
        auditArg.setPageSize(arg.getPageSize());
        auditArg.setIsExport(true);
        if (Objects.nonNull(arg.getCallBackData())) {
            if (CollectionUtils.notEmpty(arg.getCallBackData().getSearchAfter())) {
                auditArg.setSearchAfter(arg.getCallBackData().getSearchAfter());
            } else if (StringUtils.isNotBlank(arg.getCallBackData().getLogId())) {
                auditArg.setLogId(arg.getCallBackData().getLogId());
            } else {
                log.warn("findCRMExportData callBackData is {}", JSON.toJSONString(arg.getCallBackData()));
                result.setEnd(true);
                return result;
            }
        }
        User user = context.getUser();
        AuditLog.Result auditLog = logService.getAuditLog(user, auditArg);
        if (Objects.nonNull(auditLog) && CollectionUtils.notEmpty(auditLog.getResults())) {
            List<AuditLogInfo> auditLogResults = auditLog.getResults();
            Set<String> describeApiNames = auditLogResults.stream().map(AuditLogInfo::getObjectName).collect(Collectors.toSet());
            List<IObjectDescribe> describeList = describeLogicService.findObjectList(context.getTenantId(), describeApiNames);
            List<String> userIds = auditLogResults.stream().filter(x -> StringUtils.isBlank(x.getUserName())).map(AuditLogInfo::getUserId).distinct().collect(Collectors.toList());
            Map<String, String> userNameMapByIds = orgService.getUserNameMapByIds(user.getTenantId(), user.getUserId(), userIds);
            for (AuditLogInfo auditLogResult : auditLogResults) {
                if (StringUtils.isBlank(auditLogResult.getUserName())) {
                    auditLogResult.setUserName(userNameMapByIds.get(auditLogResult.getUserId()));
                }
            }
            List<ExportAuditLogTemplate> dataList = auditLogResults.stream().map(x -> ExportAuditLogTemplate.convertToExportAuditLogTemplate(x, describeList)).collect(Collectors.toList());
            result.setEnd(Boolean.FALSE.equals(auditLog.getHasMore()));
            result.setDataList(dataList);
            ExportDTO.ExportCallBackData callBackData = new ExportDTO.ExportCallBackData();
            callBackData.setLogId(auditLogResults.get(auditLogResults.size() - 1).getLogId());
            List<Object> searchAfter = auditLogResults.get(auditLogResults.size() - 1).getSearchAfter();
            if (CollectionUtils.notEmpty(searchAfter)) {
                callBackData.setSearchAfter(searchAfter);
            }
            result.setCallBackData(callBackData.convertCallBackDataToMap());
        } else {
            result.setEnd(true);
        }
        return result;
    }

    @ServiceMethod("findCRMExportData")
    public ExportDTO.ExportCRMLogResult findCRMExportData(ExportDTO.Arg arg, ServiceContext context) {
        log.info("findCRMExportData arg:{}", JSON.toJSONString(arg));
        ExportDTO.ExportCRMLogResult result = new ExportDTO.ExportCRMLogResult();
        AuditLog.Arg auditArg = new AuditLog.Arg();
        AuditLogInfo condition = JacksonUtils.fromJson(arg.getSearchQuery(), AuditLogInfo.class);
        if (Objects.isNull(condition) || StringUtils.isBlank(condition.getModule())) {
            log.warn("findCRMExportData error,arg is {}", JSON.toJSONString(arg));
            result.setEnd(true);
            return result;
        }
        auditArg.setAuditLogCondition(condition);
        auditArg.setPageSize(arg.getPageSize());
        auditArg.setIsExport(true);
        if (Objects.nonNull(arg.getCallBackData())) {
            if (CollectionUtils.notEmpty(arg.getCallBackData().getSearchAfter())) {
                auditArg.setSearchAfter(arg.getCallBackData().getSearchAfter());
            } else if (StringUtils.isNotBlank(arg.getCallBackData().getLogId())) {
                auditArg.setLogId(arg.getCallBackData().getLogId());
            } else {
                log.warn("findCRMExportData callBackData is {}", JSON.toJSONString(arg.getCallBackData()));
                result.setEnd(true);
                return result;
            }
        }
        AuditLog.Result auditLog = logService.getAuditLog(context.getUser(), auditArg);
        if (Objects.nonNull(auditLog) && CollectionUtils.notEmpty(auditLog.getResults())) {
            List<AuditLogInfo> auditLogResults = auditLog.getResults();
            List<ExportAuditLogTemplate> dataList = auditLogResults.stream().map(ExportAuditLogTemplate::convertToExportCRMTemplate).collect(Collectors.toList());
            result.setEnd(Boolean.FALSE.equals(auditLog.getHasMore()));
            result.setDataList(dataList);
            ExportDTO.ExportCallBackData callBackData = new ExportDTO.ExportCallBackData();
            callBackData.setLogId(auditLogResults.get(auditLogResults.size() - 1).getLogId());
            List<Object> searchAfter = auditLogResults.get(auditLogResults.size() - 1).getSearchAfter();
            if (CollectionUtils.notEmpty(searchAfter)) {
                callBackData.setSearchAfter(searchAfter);
            }
            result.setCallBackData(callBackData.convertCallBackDataToMap());
        } else {
            result.setEnd(true);
        }
        return result;
    }

    @ServiceMethod("exportVerify")
    public ExportDTO.VerifyResult exportVerify(ExportDTO.Arg arg, ServiceContext context) {
        ExportDTO.VerifyResult result = new ExportDTO.VerifyResult();
        log.info("exportVerify start,tenantId:{},arg:{}", context.getTenantId(), JSON.toJSONString(arg));
        int count = 0;
        if (StringUtils.equals(arg.getExportBizType(), "loginLog")) {
            LoginLog.Arg loginArg = new LoginLog.Arg();
            LoginLog.LoginInfoCondition condition = JSON.parseObject(arg.getSearchQuery(), LoginLog.LoginInfoCondition.class);
            loginArg.setLoginCondition(condition);
            LoginLog.Result loginLog = logService.getLoginLog(context.getUser(), loginArg);
            if (Objects.nonNull(loginLog)) {
                count = loginLog.getTotalCount();
            }
        }
        if (StringUtils.equals(arg.getExportBizType(), "auditLog") || StringUtils.equals(arg.getExportBizType(), "crmLog")) {
            AuditLog.Arg auditArg = new AuditLog.Arg();
            AuditLogInfo condition = JSON.parseObject(arg.getSearchQuery(), AuditLogInfo.class);
            auditArg.setAuditLogCondition(condition);
            AuditLog.Result auditLog = logService.getAuditLogCount(context.getUser(), auditArg);
            if (Objects.nonNull(auditLog)) {
                count = auditLog.getTotalCount();
            }
        }
        result.setTotalCount(count);
        log.info("exportVerify end,tenantId:{},result:{}", context.getTenantId(), JSON.toJSONString(result));
        return result;
    }

    @ServiceMethod("findLogExportHeader")
    public ExportDTO.ExportHeaderResult findExportHeader(ExportDTO.Arg arg, ServiceContext context) {
        ExportDTO.ExportHeaderResult result = new ExportDTO.ExportHeaderResult();
        log.info("findExportHeader start,tenantId:{},arg:{}", context.getTenantId(), JSON.toJSONString(arg));
        List<ExportDTO.Header> template;
        int count = 0;
        if (StringUtils.equals(arg.getExportBizType(), "loginLog")) {
            LoginLog.Arg loginArg = new LoginLog.Arg();
            LoginLog.LoginInfoCondition condition = JSON.parseObject(arg.getSearchQuery(), LoginLog.LoginInfoCondition.class);
            loginArg.setLoginCondition(condition);
            LoginLog.Result loginLog = logService.getLoginLog(context.getUser(), loginArg);
            if (Objects.nonNull(loginLog)) {
                count = loginLog.getTotalCount();
            }
            template = ExportLoginLogTemplate.getLoginLogTemplate();
        } else {
            if (StringUtils.equals(arg.getExportBizType(), "auditLog")) {
                template = ExportAuditLogTemplate.getAuditLogTemplate();
            } else if (StringUtils.equals(arg.getExportBizType(), "crmLog")) {
                template = ExportAuditLogTemplate.getCrmLogTemplate();
            } else {
                log.warn("findExportHeader error,tenantId:{},arg:{}", context.getTenantId(), JSON.toJSONString(arg));
                return result;
            }
            AuditLog.Arg auditArg = new AuditLog.Arg();
            AuditLogInfo condition = JSON.parseObject(arg.getSearchQuery(), AuditLogInfo.class);
            auditArg.setAuditLogCondition(condition);
            AuditLog.Result auditLog = logService.getAuditLogCount(context.getUser(), auditArg);
            if (Objects.nonNull(auditLog)) {
                count = auditLog.getTotalCount();
            }
        }
        log.info("findExportHeader end,tenantId:{},result:{}", context.getTenantId(), JSON.toJSONString(result));
        result.setExportHeaders(template);
        result.setTotalCount(count);
        return result;
    }
}
