package com.facishare.paas.appframework.metadata.repository.model;

import com.facishare.paas.appframework.metadata.repository.annotation.Entity;
import com.facishare.paas.appframework.metadata.repository.annotation.ObjectField;
import com.facishare.paas.appframework.metadata.repository.annotation.SelectOneField;
import com.facishare.paas.appframework.metadata.repository.annotation.TextField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity(apiName = MtResource.MT_RESOURCE_OBJ_API_NAME)
public class MtResource extends BaseEntity {
    public static final String MT_RESOURCE_OBJ_API_NAME = "MtResourceObj";

    public static final String RESOURCE_TYPE = "resource_type";
    public static final String RESOURCE_TYPE_FILED = "Describe.Field";

    public static final String RESOURCE_VALUE = "resource_value";
    public static final String SOURCE_TYPE = "source_type";
    public static final String SOURCE_VALUE = "source_value";

    public static final String SOURCE_TYPE_FIELD_CONTROL = "field_control";
    public static final String SOURCE_VALUE_STRICT = "strict_control";
    public static final String SOURCE_VALUE_WEAK = "weak_control";

    public static final String STATUS = "status";
    public static final String STATUS_TYPE_DISABLE = "1";
    public static final String STATUS_TYPE_ENABLE = "0";

    public static final String CONTROL_LEVEL_UNCHANGEABLE = "0";
    public static final String CONTROL_LEVEL_CHANGEABLE = "1";

    public static final String SOURCE_TYPE_CONNECTION_SETTINGS = "CONNECTION_SETTINGS";

    public static final String RESOURCE_PARENT_VALUE = "resource_parent_value";

    @TextField(field = @ObjectField(apiName = RESOURCE_PARENT_VALUE, label = "resource_parent_value"))
    private String resourceParentValue;

    @TextField(field = @ObjectField(apiName = RESOURCE_TYPE, label = "资源类型"))
    private String resourceType;

    @TextField(field = @ObjectField(apiName = RESOURCE_VALUE, label = "资源值"))
    private String resourceValue;

    @SelectOneField(field = @ObjectField(apiName = "control_level", label = "控制级别"))
    private String controlLevel;

    @TextField(field = @ObjectField(apiName = SOURCE_TYPE, label = "来源类型"))
    private String sourceType;

    @TextField(field = @ObjectField(apiName = SOURCE_VALUE, label = "来源"))
    private String sourceValue;

    @SelectOneField(field = @ObjectField(apiName = "status", label = "状态"))
    private String status;

}

