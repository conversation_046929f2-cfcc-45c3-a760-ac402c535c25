package com.facishare.paas.appframework.core.predef.service.dto.function;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.impl.UdefFunction;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 创建Button关联的函数信息
 * <p>
 * Created by liyiguang on 2018/2/3.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FunctionInfo {

    private String id;

    @JSONField(name = "api_name")       //M1
    @SerializedName(value = "api_name") //兼容FCP
    @JsonProperty(value = "api_name")
    String apiName;

    @JSONField(name = "binding_object_api_name")
    @SerializedName(value = "binding_object_api_name")
    @JsonProperty(value = "binding_object_api_name")
    String bindingObjectApiName;

    @JSONField(name = "function_name")
    @SerializedName(value = "function_name")
    @JsonProperty(value = "function_name")
    String functionName;

    @JSONField(name = "is_active")
    @SerializedName(value = "is_active")
    @JsonProperty(value = "is_active")
    Boolean isActive;

    @JSONField(name = "create_time")
    @SerializedName(value = "create_time")
    @JsonProperty(value = "create_time")
    Long createTime;

    @JSONField(name = "create_by")
    @SerializedName(value = "create_by")
    @JsonProperty(value = "create_by")
    String createBy;

    @JSONField(name = "return_type")
    @SerializedName(value = "return_type")
    @JsonProperty(value = "return_type")
    String returnType;

    @JSONField(name = "status")
    @SerializedName(value = "status")
    @JsonProperty(value = "status")
    String status;

    @JSONField(name = "name_space")
    @SerializedName(value = "name_space")
    @JsonProperty(value = "name_space")
    String nameSpace;

    @JSONField(name = "remark")
    @SerializedName(value = "remark")
    @JsonProperty(value = "remark")
    String remark;

    @JSONField(name = "binding_object_label")
    @SerializedName(value = "binding_object_label")
    @JsonProperty(value = "binding_object_label")
    String bindingObjectLabel;

    @JSONField(name = "commit_log")
    @SerializedName(value = "commit_log")
    @JsonProperty(value = "commit_log")
    String commitLog;

    @JSONField(name = "last_modified_by")
    @SerializedName(value = "last_modified_by")
    @JsonProperty(value = "last_modified_by")
    String lastModifiedBy;

    @JSONField(name = "type")
    @SerializedName(value = "type")
    @JsonProperty(value = "type")
    String type;

    @JSONField(name = "lang")
    @SerializedName(value = "lang")
    @JsonProperty(value = "lang")
    int lang;

    List<Parameter> parameters;
    String body;
    int version;
    String application;

    Map<String,Object> usedInfo;
    String developerInfo;

    public List<Parameter> getParameters() {
        if (parameters == null) {
            return Collections.emptyList();
        }
        return parameters;
    }

    public static FunctionInfo of(IUdefFunction uDefFunction) {
        if(uDefFunction == null){
            return null;
        }


        List<Parameter> parameters = Collections.emptyList();
        if(uDefFunction.getParameters() != null){
            parameters = uDefFunction.getParameters().stream().map(Parameter::of).collect(Collectors.toList());
        }

        FunctionInfo functionInfo = FunctionInfo.builder()
                .id(uDefFunction.getId())
                .apiName(uDefFunction.getApiName())
                .bindingObjectApiName(uDefFunction.getBindingObjectApiName())
                .parameters(parameters)
                .bindingObjectLabel(uDefFunction.getBindingObjectLabel())
                .remark(uDefFunction.getRemark())
                .status(uDefFunction.getStatus())
                .isActive(uDefFunction.isActive())
                .usedInfo(uDefFunction.getUsedInfo())
                .createTime(uDefFunction.getCreateTime())
                .createBy(uDefFunction.getCreatedBy())
                .returnType(uDefFunction.getReturnType())
                .functionName(uDefFunction.getFunctionName())
                .version(uDefFunction.getVersion())
                .body(uDefFunction.getBody())
                .application(uDefFunction.getApplication())
                .nameSpace(uDefFunction.getNameSpace())
                .commitLog(uDefFunction.getCommitLog())
                .developerInfo(uDefFunction.getDeveloperInfo())
                .lastModifiedBy(uDefFunction.getLastModifiedBy())
                .type(uDefFunction.getType())
                .lang(uDefFunction.getLang())
                .build();

        return functionInfo;
    }

    public static List<FunctionInfo> of(List<IUdefFunction> uDefFunction) {
        if(uDefFunction == null){
            return null;
        }

        List<FunctionInfo> functionInfoList = uDefFunction.stream()
                .map(x -> FunctionInfo.builder()
                        .id(x.getId())
                        .apiName(x.getApiName())
                        .bindingObjectApiName(x.getBindingObjectApiName())
                        .isActive(x.isActive())
                        .status(x.getStatus())
                        .bindingObjectLabel(x.getBindingObjectLabel())
                        .createTime(x.getCreateTime())
                        .createBy(x.getCreatedBy())
                        .usedInfo(x.getUsedInfo())
                        .returnType(x.getReturnType())
                        .functionName(x.getFunctionName())
                        .version(x.getVersion())
                        .application(x.getApplication())
                        .nameSpace(x.getNameSpace())
                        .remark(x.getRemark())
                        .commitLog(x.getCommitLog())
                        .developerInfo(x.getDeveloperInfo())
                        .lastModifiedBy(x.getLastModifiedBy())
                        .type(x.getType())
                        .lang(x.getLang())
                        .build()).collect(Collectors.toList());

        return functionInfoList;

    }

    public static List<FunctionInfo> of(List<IUdefFunction> uDefFunction, Map<String, String> employeeMap) {
        if(uDefFunction == null){
            return null;
        }

        List<FunctionInfo> functionInfoList = uDefFunction.stream()
                .map(x -> FunctionInfo.builder()
                        .id(x.getId())
                        .apiName(x.getApiName())
                        .bindingObjectApiName(x.getBindingObjectApiName())
                        .isActive(x.isActive())
                        .status(x.getStatus())
                        .bindingObjectLabel(x.getBindingObjectLabel())
                        .createTime(x.getCreateTime())
                        .createBy(x.getCreatedBy())
                        .usedInfo(x.getUsedInfo())
                        .returnType(x.getReturnType())
                        .functionName(x.getFunctionName())
                        .version(x.getVersion())
                        .application(x.getApplication())
                        .nameSpace(x.getNameSpace())
                        .remark(x.getRemark())
                        .commitLog(x.getCommitLog())
                        .lang(x.getLang())
                        .lastModifiedBy(employeeMap.get(x.getLastModifiedBy()))
                        .build()).collect(Collectors.toList());

        return functionInfoList;
    }

    public IUdefFunction toUDefFunction() {

        IUdefFunction function = new UdefFunction();
        if(this.getId() != null){
            function.setId(this.getId());
        }
        function.setApiName(this.getApiName());
        function.setBindingObjectApiName(this.getBindingObjectApiName());
        function.setParameters(getParameters().stream().map(x -> x.toJSON()).collect(Collectors.toList()));
        function.setReturnType(this.getReturnType());
        function.setBindingObjectLabel(this.getBindingObjectLabel());
        function.setRemark(this.getRemark());
        function.setIsActive(this.getIsActive());
        function.setStatus(this.getStatus());
        function.setFunctionName(this.getFunctionName());
        function.setVersion(this.getVersion());
        function.setBody(this.getBody());
        function.setApplication(this.getApplication());
        function.setNameSpace(this.getNameSpace());
        function.setCommitLog(this.getCommitLog());
        function.setType(this.getType());
        function.setLang(this.getLang());

        return function;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Parameter {
        String name;
        String type;
        Object value;
        String remark;
        @JSONField(name = "default_value")
        @SerializedName(value = "default_value")
        @JsonProperty(value = "default_value")
        String defaultValue;

        public String toJSON() {
            return JSON.toJSONString(this);
        }

        public static Parameter of(String json) {
            return JSON.parseObject(json, Parameter.class);
        }
    }
}
