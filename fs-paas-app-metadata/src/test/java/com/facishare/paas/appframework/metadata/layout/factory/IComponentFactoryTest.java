package com.facishare.paas.appframework.metadata.layout.factory;

import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IComponent;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class IComponentFactoryTest {

  @Mock
  private IObjectDescribe describe;

  @Mock
  private IComponent component;

  /**
   * GenerateByAI
   * 测试内容描述：测试IComponentFactory接口基本功能
   */
  @Test
  @DisplayName("正常场景 - 验证IComponentFactory接口基本功能")
  void testIComponentFactory_BasicFunctionality() {
    // 创建一个具体的IComponentFactory实现用于测试
    IComponentFactory componentFactory = new IComponentFactory() {
      @Override
      public String supportComponentType() {
        return "test_component";
      }

      @Override
      public IComponent createDefaultComponent(ILayoutFactory.Context context, IObjectDescribe describe) {
        return component;
      }
    };

    // 验证接口方法
    assertEquals("test_component", componentFactory.supportComponentType());
    
    ILayoutFactory.Context context = mock(ILayoutFactory.Context.class);
    IComponent result = componentFactory.createDefaultComponent(context, describe);
    assertSame(component, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口的多态性
   */
  @Test
  @DisplayName("正常场景 - 测试接口多态性")
  void testIComponentFactory_Polymorphism() {
    // 创建两个不同的实现
    IComponentFactory factory1 = new IComponentFactory() {
      @Override
      public String supportComponentType() {
        return "type1";
      }

      @Override
      public IComponent createDefaultComponent(ILayoutFactory.Context context, IObjectDescribe describe) {
        return component;
      }
    };

    IComponentFactory factory2 = new IComponentFactory() {
      @Override
      public String supportComponentType() {
        return "type2";
      }

      @Override
      public IComponent createDefaultComponent(ILayoutFactory.Context context, IObjectDescribe describe) {
        return component;
      }
    };

    // 验证多态性
    assertNotEquals(factory1.supportComponentType(), factory2.supportComponentType());
    assertEquals("type1", factory1.supportComponentType());
    assertEquals("type2", factory2.supportComponentType());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口方法的null处理
   */
  @Test
  @DisplayName("边界场景 - 测试null参数处理")
  void testIComponentFactory_NullHandling() {
    IComponentFactory componentFactory = new IComponentFactory() {
      @Override
      public String supportComponentType() {
        return null;
      }

      @Override
      public IComponent createDefaultComponent(ILayoutFactory.Context context, IObjectDescribe describe) {
        return null;
      }
    };

    // 验证null返回值
    assertNull(componentFactory.supportComponentType());
    assertNull(componentFactory.createDefaultComponent(null, null));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口的异常处理
   */
  @Test
  @DisplayName("异常场景 - 测试异常处理")
  void testIComponentFactory_ExceptionHandling() {
    IComponentFactory componentFactory = new IComponentFactory() {
      @Override
      public String supportComponentType() {
        throw new RuntimeException("Test exception");
      }

      @Override
      public IComponent createDefaultComponent(ILayoutFactory.Context context, IObjectDescribe describe) {
        throw new RuntimeException("Test exception");
      }
    };

    // 验证异常传播
    assertThrows(RuntimeException.class, componentFactory::supportComponentType);
    assertThrows(RuntimeException.class, () -> 
        componentFactory.createDefaultComponent(null, null));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口的默认行为
   */
  @Test
  @DisplayName("正常场景 - 测试接口默认行为")
  void testIComponentFactory_DefaultBehavior() {
    // 验证接口可以被正确实现
    assertDoesNotThrow(() -> {
      IComponentFactory componentFactory = new IComponentFactory() {
        @Override
        public String supportComponentType() {
          return "default";
        }

        @Override
        public IComponent createDefaultComponent(ILayoutFactory.Context context, IObjectDescribe describe) {
          return component;
        }
      };
      
      assertNotNull(componentFactory);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口的类型安全性
   */
  @Test
  @DisplayName("正常场景 - 测试接口类型安全性")
  void testIComponentFactory_TypeSafety() {
    IComponentFactory componentFactory = new IComponentFactory() {
      @Override
      public String supportComponentType() {
        return "safe_type";
      }

      @Override
      public IComponent createDefaultComponent(ILayoutFactory.Context context, IObjectDescribe describe) {
        return component;
      }
    };

    // 验证类型安全
    assertTrue(componentFactory instanceof IComponentFactory);
    
    String type = componentFactory.supportComponentType();
    assertTrue(type instanceof String);
    
    ILayoutFactory.Context context = mock(ILayoutFactory.Context.class);
    IComponent result = componentFactory.createDefaultComponent(context, describe);
    assertTrue(result instanceof IComponent);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口的继承特性
   */
  @Test
  @DisplayName("正常场景 - 测试接口继承特性")
  void testIComponentFactory_Inheritance() {
    // 创建一个抽象实现
    abstract class AbstractComponentFactory implements IComponentFactory {
      protected String baseType = "base";
      
      @Override
      public String supportComponentType() {
        return baseType;
      }
    }

    // 创建具体实现
    IComponentFactory concreteFactory = new AbstractComponentFactory() {
      @Override
      public IComponent createDefaultComponent(ILayoutFactory.Context context, IObjectDescribe describe) {
        return component;
      }
    };

    // 验证继承
    assertEquals("base", concreteFactory.supportComponentType());
    assertNotNull(concreteFactory.createDefaultComponent(null, describe));
  }
}
