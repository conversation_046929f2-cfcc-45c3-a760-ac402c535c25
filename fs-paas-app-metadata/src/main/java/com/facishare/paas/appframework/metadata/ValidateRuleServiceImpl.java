package com.facishare.paas.appframework.metadata;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.dto.RuleResult;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.expression.RuleCalculateResult;
import com.facishare.paas.appframework.metadata.switchcache.SwitchCacheService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.IRule;
import com.facishare.paas.metadata.impl.Rule;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.service.impl.RuleServiceImpl;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.switchcache.provider.SwitchCacheProviderManager.VALIDATE_RULE;
import static com.facishare.paas.appframework.metadata.switchcache.provider.ValidateRuleSwitchCacheProvider.CREATE;
import static com.facishare.paas.appframework.metadata.switchcache.provider.ValidateRuleSwitchCacheProvider.UPDATE;

/**
 * Created by linqiuying on 17/8/7.
 */

@Slf4j
@Service("validateRuleService")
public class ValidateRuleServiceImpl implements ValidateRuleService {
    @Autowired
    private RuleServiceImpl ruleService;
    @Autowired
    private MetaDataService metaDataService;
    @Autowired
    private ExpressionCalculateLogicService expressionCalculateLogicService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private SwitchCacheService switchCacheService;
    @Autowired
    private I18nSettingService i18nSettingService;
    @Autowired
    private LicenseService licenseService;

    @Override
    public RuleResult create(IRule rule) {
        RuleResult result = new RuleResult();
        IRule iRule;
        try {
            checkExpression(rule);
            iRule = ruleService.create(rule);
            syncSwitchCache(rule);
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e.getMessage());
        }
        if (iRule != null) {
            result.setSuccess(true);
        }
        return result;
    }

    private void checkExpression(IRule rule) throws MetadataServiceException {
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(rule.getTenantId(), rule.getDescribeApiName());
        if (objectDescribe == null) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.OBJECT_DESC_UNEXIST));
        }
        // 从对象的验证规则可以配置为不阻拦。在主从新建的时候，从对象只会弹窗提示阻拦的验证规则
        /*
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject() && !Optional.ofNullable(rule.getEnableBlocking()).orElse(true)) {
            throw new ValidateException(I18N.text(I18NKey.DETAIL_CANNOT_SET_VALIDATE_RULE));
        }
        */
    }

    @Override
    public RuleResult update(User user, IRule rule) {
        RuleResult result = new RuleResult();

        IRule iRule;
        try {
            //增加最后修改人
            rule.setLastModifiedBy(user.getUserId());
            rule.setLastModifiedTime(System.currentTimeMillis());
            rule.setTenantId(user.getTenantId());
            checkExpression(rule);
            iRule = ruleService.update(rule);
            syncSwitchCache(rule);
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e.getMessage());
        }
        if (iRule != null) {
            syncTransValue(iRule);
            result.setSuccess(true);
        }
        return result;
    }

    private void syncTransValue(IRule rule) {
        if (Objects.isNull(rule) || StringUtils.isBlank(rule.getMessage())) {
            return;
        }
        Map<String, String> keyToNewName = Maps.newHashMap();
        keyToNewName.put(getValidateTransKey(rule.getDescribeApiName(), rule.getApiName()), rule.getMessage());
        i18nSettingService.syncTransValue(keyToNewName, I18N.getContext().getLanguage(), rule.getTenantId());
    }

    @Override
    public RuleResult delete(String describeApiName, String tenantId, String ruleApiName) {
        RuleResult result = new RuleResult();
        IRule oldRule;
        IRule iRule;
        try {
            oldRule = ruleService.findByApiName(tenantId, describeApiName, ruleApiName);
            if (oldRule == null) {
                throw new MetaDataBusinessException(I18NExt.text(I18NKey.VALIDATION_RULE_NOT_EXIST_OR_DELETED));
            }
            iRule = ruleService.delete(oldRule);
            syncSwitchCache(oldRule);
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e.getMessage());
        }
        if (iRule != null) {
            result.setSuccess(true);
        }
        return result;
    }

    @Override
    public void deleteByDescribeApiName(String tenantId, String describeApiName) {
        try {
            ruleService.deleteByDescribeApiName(tenantId, describeApiName);
        } catch (MetadataServiceException e) {
            log.warn("deleteByDescribeApiName error,tenantId:{},describeApiName:{}", tenantId, describeApiName);
        }
    }

    @Override
    public List<IRule> findRuleList(String tenantId, String describeApiName) {
        try {
            List<IRule> ruleList = ruleService.findByDescribeApiName(tenantId, describeApiName);
            ruleList.forEach(x -> fillMultiLanguage(x, false));
            return ruleList;
        } catch (MetadataServiceException e) {
            log.warn("findRuleList error,tenantId:{},describeApiName:{}", tenantId, describeApiName);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }


    @Override
    public List<IRule> findRuleList(String tenantId, String describeApiName, boolean isOnTime) {
        try {
            List<IRule> ruleList = ruleService.findByDescribeApiName(tenantId, describeApiName);
            ruleList.forEach(x -> fillMultiLanguage(x, isOnTime));
            return ruleList;
        } catch (MetadataServiceException e) {
            log.warn("findRuleList error,tenantId:{},describeApiName:{}", tenantId, describeApiName);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @Override
    public RuleResult findRuleList(String describeApiName, String tenantId, String ruleName) {
        RuleResult result = new RuleResult();
        List<IRule> ruleList = findRuleList(tenantId, describeApiName, true);
        if (CollectionUtils.empty(ruleList)) {
            return result;
        }
        ArrayList resultList = Lists.newArrayList();
        for (IRule rule : ruleList) {
            if (Strings.isNullOrEmpty(ruleName) || rule.getRuleName().contains(ruleName)) {
                resultList.add(((DocumentBasedBean) rule).getContainerDocument());
            }
        }
        result.setRuleList(resultList);
        result.setSuccess(true);
        return result;
    }

    @Override
    public RuleResult findRuleInfo(String describeApiName, String tenantId, String ruleApiName) {
        RuleResult result = new RuleResult();
        IRule rule;
        try {
            rule = ruleService.findByApiName(tenantId, describeApiName, ruleApiName);
            fillMultiLanguage(rule, true);
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e.getMessage());
        }
        result.setRule(rule == null ? null : ((Rule) rule).getContainerDocument());
        result.setSuccess(true);
        return result;
    }

    private void fillMultiLanguage(IRule rule, boolean isOnTime) {
        if (Objects.isNull(rule) || !licenseService.isSupportMultiLanguage(rule.getTenantId())) {
            return;
        }
        String transKey = getValidateTransKey(rule.getDescribeApiName(), rule.getApiName());
        String message = rule.getMessage();
        if (isOnTime) {
            Map<String, Localization> localizationMap = i18nSettingService.getLocalization(Lists.newArrayList(transKey), rule.getTenantId(), true, isOnTime);
            if (CollectionUtils.notEmpty(localizationMap)) {
                Localization localization = localizationMap.get(transKey);
                message = localization.get(I18N.getContext().getLanguage(), message);
            }
        } else {
            message = I18NExt.getOnlyTextOrDefault(transKey, rule.getMessage());
        }
        rule.setMessage(message);
    }

    @Override
    public RuleResult isActive(String describeApiName, User user, String ruleApiName, boolean isActive) {
        IRule rule;
        try {
            rule = ruleService.findByApiName(user.getTenantId(), describeApiName, ruleApiName);
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e.getMessage());
        }
        rule.setIsActive(isActive);
        return update(user, rule);
    }

    @Override
    public void doValidate(User user, String operation, IObjectDescribe describe, List<IObjectData> dataList) {
        RuleResult ruleResult = validateRule(user, operation, describe, dataList);
        if (ruleResult.isMatch()) {
            throw new ValidateException(ruleResult.getFailMessage());
        }
    }

    @Override
    public RuleResult validateRule(User user, String operation, IObjectDescribe describe, List<IObjectData> dataList) {
        return validateRule(user, operation, describe, dataList, true);
    }

    @Override
    public RuleResult validateRule(User user, String operation, IObjectDescribe describe, List<IObjectData> dataList, boolean includeNoBlockRule) {
        List<IObjectData> cpDataList = ObjectDataExt.copyList(dataList);
        RuleCalculateResult calculateResult = new RuleCalculateResult();

        MasterDetailFieldDescribe mdField = ObjectDescribeExt.of(describe).getMasterDetailFieldDescribe().orElse(null);
        if (Objects.nonNull(mdField)) {
            List<IRule> ruleList = getValidRules(user, describe.getApiName(), operation, includeNoBlockRule);
            if (CollectionUtils.empty(ruleList)) {
                return RuleResult.of(calculateResult);
            }

            if (IRule.UPDATE.equals(operation)) {
                metaDataService.mergeWithDbData(user.getTenantId(), describe.getApiName(), cpDataList);
            }

            IObjectDescribe masterDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), mdField.getTargetApiName());
            List<String> masterDataIdList = cpDataList.stream().map(x -> x.get(mdField.getApiName(), String.class))
                    .filter(x -> !Strings.isNullOrEmpty(x)).distinct().collect(Collectors.toList());
            List<IObjectData> masterDataList = metaDataService.findObjectDataByIdsIgnoreFormula(user.getTenantId(), masterDataIdList, masterDescribe.getApiName());
            Map<String, IObjectData> masterDataMap = masterDataList.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));

            Map<String, List<IObjectData>> masterId2DetailDataMap = cpDataList.stream()
                    .collect(Collectors.groupingBy(x -> StringUtils.trimToEmpty(x.get(mdField.getApiName(), String.class))));

            for (Map.Entry<String, List<IObjectData>> entry : masterId2DetailDataMap.entrySet()) {
                IObjectData masterData = masterDataMap.get(entry.getKey());
                List<IObjectData> detailDataList = entry.getValue();

                if (Objects.nonNull(masterData)) {
                    metaDataService.calculateCountFieldsWithDetailDataAndDbValue(masterData, detailDataList, masterDescribe,
                            describe, ObjectDescribeExt.of(masterDescribe).getCountFields(describe.getApiName()));
                }

                calculateResult = expressionCalculateLogicService.validateRules(describe, cpDataList, ruleList, masterData);
            }
        } else {
            calculateResult = calculateRule(user, operation, null, cpDataList, describe, includeNoBlockRule);
        }

        return RuleResult.of(calculateResult);
    }

    @Override
    public RuleResult validateRule(User user, String operation, Map<String, IObjectDescribe> describeMap, IObjectData masterObjectData,
                                   Map<String, List<IObjectData>> detailDataMap) {
        return validateRule(user, operation, describeMap, masterObjectData, detailDataMap, null);
    }

    @Override
    public RuleResult validateRule(User user, String operation, Map<String, IObjectDescribe> describeMap,
                                   IObjectData masterObjectData, Map<String, List<IObjectData>> detailDataMap,
                                   Map<String, List<IObjectData>> relatedDataList) {
        if (Objects.isNull(masterObjectData)) {
            for (Map.Entry<String, List<IObjectData>> entry : detailDataMap.entrySet()) {
                RuleResult result = validateRule(user, operation, describeMap.get(entry.getKey()), entry.getValue());
                if (result.isMatch()) {
                    return result;
                }
            }
        } else if (CollectionUtils.empty(detailDataMap) && CollectionUtils.empty(relatedDataList)) {
            return validateRule(user, operation, describeMap.get(masterObjectData.getDescribeApiName()), Lists.newArrayList(masterObjectData));
        } else {
            IObjectData cpMasterObjectData = ObjectDataExt.of(masterObjectData).copy();
            Map<String, List<IObjectData>> cpDetailDataMap = Maps.newHashMap();
            CollectionUtils.nullToEmpty(detailDataMap).forEach((k, v) -> cpDetailDataMap.put(k, ObjectDataExt.copyList(v)));
            Map<String, List<IObjectData>> cpRelatedDataMap = Maps.newHashMap();
            CollectionUtils.nullToEmpty(relatedDataList).forEach((k, v) -> cpRelatedDataMap.put(k, ObjectDataExt.copyList(v)));

            //检验主对象
            metaDataService.calculateCountFieldsInMemory(cpMasterObjectData, cpDetailDataMap, describeMap);
            RuleCalculateResult calculateResult = calculateRule(user, operation, null, Lists.newArrayList(cpMasterObjectData),
                    describeMap.get(masterObjectData.getDescribeApiName()));

            //校验从对象
            if (!calculateResult.hasEnableBlock()) {
                bulkValidateRule(user, operation, describeMap, cpMasterObjectData, cpDetailDataMap, calculateResult);
            }
            // 校验相关对象
            if (!calculateResult.hasEnableBlock()) {
                bulkValidateRule(user, operation, describeMap, cpMasterObjectData, cpRelatedDataMap, calculateResult);
            }
            return RuleResult.of(calculateResult);
        }
        return RuleResult.of(new RuleCalculateResult());
    }

    private void bulkValidateRule(User user, String operation, Map<String, IObjectDescribe> describeMap, IObjectData masterData,
                                  Map<String, List<IObjectData>> dataMap, RuleCalculateResult result) {
        if (CollectionUtils.empty(dataMap) || CollectionUtils.empty(describeMap)) {
            return;
        }
        for (Map.Entry<String, List<IObjectData>> entry : dataMap.entrySet()) {
            List<IObjectData> dataList = entry.getValue();
            IObjectDescribe describe = describeMap.get(entry.getKey());
            // 主从一起编辑的时候从对象需要区分是新建还是编辑, 主编辑->从新建/编辑 主新建->从新建
            List<IObjectData> dataListToAdd = dataList.stream().filter(x -> ObjectDataExt.of(x).isNewData()).collect(Collectors.toList());
            List<IObjectData> dataListToUpdate = dataList.stream().filter(x -> !ObjectDataExt.of(x).isNewData()).collect(Collectors.toList());

            RuleCalculateResult ruleResult = calculateRule(user, IRule.CREATE, masterData, dataListToAdd, describe);
            if (ruleResult.hasEnableBlock()) {
                result.setMatch(ruleResult.isMatch());
                result.setData(ruleResult.getData());
                // 从对象只提示阻拦的验证规则
                result.getMatchResults().addAll(ruleResult.getEnableBlockMatchResults());
                break;
            }
            ruleResult = calculateRule(user, operation, masterData, dataListToUpdate, describe);
            if (ruleResult.hasEnableBlock()) {
                result.setMatch(ruleResult.isMatch());
                result.setData(ruleResult.getData());
                // 从对象只提示阻拦的验证规则
                result.getMatchResults().addAll(ruleResult.getEnableBlockMatchResults());
                break;
            }
        }
    }

    private RuleCalculateResult calculateRule(User user, String operation, IObjectData masterObjectData,
                                              List<IObjectData> dataList, IObjectDescribe describe) {
        return calculateRule(user, operation, masterObjectData, dataList, describe, true);
    }

    private RuleCalculateResult calculateRule(User user, String operation, IObjectData masterObjectData,
                                              List<IObjectData> dataList, IObjectDescribe describe, boolean isIncludeNoBlockRule) {
        RuleCalculateResult calculateResult = new RuleCalculateResult();
        if (CollectionUtils.empty(dataList)) {
            return calculateResult;
        }

        String describeApiName = describe.getApiName();
        List<IRule> ruleList = getValidRules(user, describeApiName, operation, Objects.isNull(masterObjectData) && isIncludeNoBlockRule);
        if (CollectionUtils.empty(ruleList)) {
            return calculateResult;
        }

        if (IRule.UPDATE.equals(operation)) {
            metaDataService.mergeWithDbData(user.getTenantId(), describe.getApiName(), dataList);
        }

        return expressionCalculateLogicService.validateRules(describe, dataList, ruleList, masterObjectData);
    }

    private List<IRule> getValidRules(User user, String describeApiName, String operation, boolean needNoBlockRule) {
        List<IRule> ruleList = findRuleList(user.getTenantId(), describeApiName, false);
        ruleList = ruleList.stream()
                .filter(x -> x.isActive())
                .filter(x -> CollectionUtils.notEmpty(x.getScene()) && x.getScene().contains(operation))
                .collect(Collectors.toList());
        if (!needNoBlockRule) {
            ruleList.removeIf(x -> Boolean.FALSE.equals(x.getEnableBlocking()));
        }
        return ruleList;
    }

    /**
     * 同步变更
     */
    private void syncSwitchCache(IRule rule) {
        if (Objects.isNull(rule)) {
            return;
        }
        try {
            switchCacheService.syncSwitchStatus(rule.getTenantId(), VALIDATE_RULE, rule.getDescribeApiName(), Sets.newHashSet(CREATE, UPDATE));
        } catch (Exception e) {
            log.warn("syncSwitchCache fail, ei:{}, describeApiName:{}", rule.getTenantId(), rule.getDescribeApiName());
        }
    }
//    validate.object_2I7ye__c.rule.rule_Ega32__c.message

    private static String getValidateTransKey(String describeApiName, String apiName) {
        return "validate." + describeApiName + ".rule." + apiName + ".message";
    }

}
