package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.cache.RedissonServiceImpl;
import com.google.common.collect.Maps;
import com.google.common.hash.Hashing;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 参数幂等
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ParamsIdempotentService {
    private static final String REDIS_KEY_PREFIX = "ParamsIdempotent";
    private static final int WAIT_TIME = -1;
    private static final int LEASE_TIME = 60;
    @Autowired
    private RedissonServiceImpl redissonService;

    public RLock getLock(User user, ObjectDataDocument objectData, Map<String, List<ObjectDataDocument>> details) {
        String key = getKey(user, objectData, details);
        return redissonService.getLock(key);
    }

    public boolean lock(RLock lock) {
        boolean tryLock;
        try {
            tryLock = lock.tryLock(WAIT_TIME, LEASE_TIME, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.warn("ParamsIdempotentService try lock failure", e);
            tryLock = false;
            Thread.currentThread().interrupt();
        }
        return tryLock;
    }

    public void unlock(RLock lock) {
        if (lock == null) {
            return;
        }
        try {
            lock.unlock();
        } catch (Exception e) {
            log.warn("ParamsIdempotentService unlock failure", e);
        }
    }

    private String getKey(User user, ObjectDataDocument objectData, Map<String, List<ObjectDataDocument>> details) {
        return String.format("%s:%s:%s:%s"
                , REDIS_KEY_PREFIX
                , user.getTenantId()
                , user.getUserId()
                , generateParameter(objectData, details));
    }

    private String generateParameter(ObjectDataDocument objectData, Map<String, List<ObjectDataDocument>> details) {
        Object requestId = objectData.remove("requestId");
        //按key排序
        TreeMap masterTreeMap = new TreeMap(objectData);
        String parameter = com.alibaba.fastjson.JSON.toJSONString(masterTreeMap);
        if (CollectionUtils.notEmpty(details)) {
            TreeMap detailTreeMap = Maps.newTreeMap();
            details.forEach((apiName, detailDataList) -> {
                List<TreeMap> detailTree = detailDataList.stream()
                        .map(detailData -> new TreeMap(detailData))
                        .collect(Collectors.toList());
                detailTreeMap.put(apiName, detailTree);
            });
            parameter += com.alibaba.fastjson.JSON.toJSONString(detailTreeMap);
        }

        if (Objects.nonNull(requestId)) {
            objectData.put("requestId", requestId);
        }

        return Hashing.sha256()
                .hashString(parameter, Charset.forName("UTF-8"))
                .toString();
    }
}
