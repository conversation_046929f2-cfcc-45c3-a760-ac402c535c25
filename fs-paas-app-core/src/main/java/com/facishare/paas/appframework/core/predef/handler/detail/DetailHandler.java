package com.facishare.paas.appframework.core.predef.handler.detail;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.handler.ControllerHandler;
import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Objects;

/**
 * Created by zhouwr on 2024/5/27.
 */
public interface DetailHandler extends ControllerHandler<DetailHandler.Arg, DetailHandler.Result> {
    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class Arg extends ControllerHandler.Arg<AbstractStandardDetailController.Arg, AbstractStandardDetailController.Result> {
        private ObjectDataDocument objectData;

        public IObjectData objectData() {
            if (Objects.isNull(objectData)) {
                return null;
            }
            return objectData.toObjectData();
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class Result extends Handler.Result<AbstractStandardDetailController.Result> {
        private Boolean defaultEnableQixinGroup;
        private Boolean skipDataPrivilegeCheck;
        private ObjectDataDocument objectData;
    }
}
