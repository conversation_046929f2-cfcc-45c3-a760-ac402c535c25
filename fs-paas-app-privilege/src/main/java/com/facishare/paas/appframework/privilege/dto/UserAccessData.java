package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2017/10/23
 */
public interface UserAccessData {

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Arg extends BasePrivilegeArg {
        private String entityId;
        private Boolean userParentDeptCascade;
        private Boolean userDeputyDept;
        private Boolean deptConvertToUser;
        private Boolean userSubordinatesCascade;
        private Boolean userResponsibleDeptUsersCascade;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {
        private Map<String, List<String>> result;
    }
}
