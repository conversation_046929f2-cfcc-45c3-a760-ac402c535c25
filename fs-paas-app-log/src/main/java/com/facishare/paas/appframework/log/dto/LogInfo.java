package com.facishare.paas.appframework.log.dto;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Image;
import com.facishare.paas.metadata.impl.ObjectData;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by liyiguang on 2017/8/16.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogInfo {
    private String logId;
    //企业id
    private String corpId;
    private String outTenantId;
    private String appId;
    private String module;
    //对象apiName
    private String objectName;
    //数据id
    private String objectId;
    private String operation;
    private String bizOperationName;
    //操作时间
    private long operationTime = System.currentTimeMillis();
    private String userId;
    private String userName;
    private String textMessage;
    private InternationalItem internationalTextMessage;
    private String masterId;
    private String peerName;
    private String masterLogId;
    private String bizId;
    private String otherBizId;
    private boolean hidden;
    private String level;
    private Long operationTimeFrom;
    private Long operationTimeTo;
    private String operationObject;
    @JSONField(name = "jsonMessage")
    private ObjectSnapshot snapshot;
    @JSONField(name = "memo")
    private String memo = "0";
    private String bizAppId;

    private List<Object> searchAfter;

    public String convertToI18nTextMessage() {
        String result = null;
        if (Objects.nonNull(internationalTextMessage)) {
            result = internationalTextMessage.getInternationalValue();
        }
        if (StringUtils.isBlank(result)) {
            result = textMessage;
        }
        return result;
    }

    //修改记录需要补充__r,__l的字段类型的字段
    public static List<String> needFillFieldInfoList = Lists.newArrayList(
            IFieldType.MASTER_DETAIL, IFieldType.OBJECT_REFERENCE, IFieldType.EMPLOYEE, IFieldType.DEPARTMENT,
            IFieldType.COUNTRY, IFieldType.PROVINCE, IFieldType.CITY, IFieldType.DISTRICT, IFieldType.TOWN, IFieldType.VILLAGE,
            IFieldType.DIMENSION, IFieldType.EMPLOYEE_MANY, IFieldType.DEPARTMENT_MANY, IFieldType.OBJECT_REFERENCE_MANY,
            IFieldType.OUT_EMPLOYEE, IFieldType.OUT_DEPARTMENT
    );

    public static final String TRIGGER_WORK_FLOW = "triggerWorkFlow";
    public static final String TRIGGER_APPROVAL_FLOW = "triggerApprovalFlow";
    public static final String MASTER_ID_MARK = "_masterIdMark";
    public static final String SERIAL_ID = "seriesId";

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ObjectSnapshot {
        List<LintMessage> textMsg;
        Map<String, Object> snapshot;
        // 记录相关团队修改信息
        List<TeamMemberInfo.Msg> msgs;
        // 记录编辑数据的信息
        List<DiffObjectData> objectDataList;
        // 记录前后变化的数据
        List<DiffData> diffData;
        // 修改/变更原因
        String peerReason;
        private String peerReasonI18nKey;
        // 主从一起操作时，从对象信息
        List<DetailInfo> detailInfo;
        List<Map<String, Object>> sourceSnapshot;
        // 转换时从对象来源信息
        List<DetailInfo> sourceDetailInfo;
        // 转换规则事件id
        String convertEventId;
        // 记录traceId
        String traceId;
        // 记录审批流修改信息
        private ApprovalFlowInfo approvalFlowInfo;
        // 具体操作名称
        private String peerDisplayName;
        private String peerDisplayNameI18nKey;
        private InternationalItem internationalPeerDisplayName;
        // 来源的源头名称(如：审批流调流程后动作，流程后动作调fs-crm，此字段记录审批流)
        private String modelName;

        private Map<String, Object> extendsInfo;

        public String getMessage() {
            if (CollectionUtils.empty(textMsg)) {
                return "";
            }
            return textMsg.stream().map(LintMessage::convertToI18nText).collect(Collectors.joining(" "));
        }

        public List<LintMessage> getLinkMessage() {
            if (CollectionUtils.empty(textMsg)) {
                return Lists.newArrayList();
            }
            return textMsg.stream().peek(x -> x.setText(x.convertToI18nText())).collect(Collectors.toList());
        }

        public String getObjectApiNameByTextMsg() {
            if (CollectionUtils.empty(textMsg)) {
                return "";
            }
            return textMsg.stream().filter(Objects::nonNull).map(LintMessage::getObjectApiName)
                    .filter(Objects::nonNull).findFirst().orElse("");
        }

        public String getDataName() {
            return getDataName(snapshot);
        }

        public String getDataName(Map<String, Object> snapshot) {
            if (CollectionUtils.empty(snapshot)) {
                return "";
            }
            Object name = snapshot.get(IObjectData.NAME);
            return name == null ? "" : name.toString();
        }

        public String getDisplayName() {
            return getDisplayName(snapshot);
        }

        public String getDisplayName(Map<String, Object> snapshot) {
            if (CollectionUtils.empty(snapshot)) {
                return "";
            }
            Object displayName = snapshot.get(IObjectDescribe.DISPLAY_NAME);
            return displayName == null ? "" : displayName.toString();
        }

        public String getObjectApiName() {
            return getObjectApiName(snapshot);
        }

        public String getObjectApiName(Map<String, Object> snapshot) {
            if (CollectionUtils.empty(snapshot)) {
                return "";
            }
            Object objectApiName = snapshot.get(IObjectData.DESCRIBE_API_NAME);
            return objectApiName == null ? "" : objectApiName.toString();
        }

        public String getObjectId() {
            return getObjectId(snapshot);
        }

        public String getObjectId(Map<String, Object> snapshot) {
            if (CollectionUtils.empty(snapshot)) {
                return "";
            }
            Object objectId = snapshot.get(IObjectData.ID);
            return objectId == null ? "" : objectId.toString();
        }

        public String getPeerReason() {
            return StringUtils.isBlank(peerReason) ? null : I18N.text(I18NKey.CHANGE_REASON) + peerReason;
        }

        public String convertToI18NPeerReason() {
            String result = null;
            if (StringUtils.isNotBlank(peerReasonI18nKey)) {
                result = I18N.text(peerReasonI18nKey);
            }
            if (StringUtils.isBlank(result)) {
                result = peerReason;
            }
            if (StringUtils.isNotBlank(result)) {
                return I18N.text(I18NKey.CHANGE_REASON) + result;
            }
            return result;
        }


        public String getConfigMsg() {
            if (CollectionUtils.empty(extendsInfo)) {
                return null;
            }
            List<String> configList = Lists.newArrayList();
            Object triggerWorkFlow = extendsInfo.get(TRIGGER_WORK_FLOW);
            if (Objects.equals(triggerWorkFlow, Boolean.FALSE)) {
                configList.add(I18NExt.getOrDefault(I18NKey.LOG_CONFIG_TRIGGER_WORK_FLOW, "触发工作流-否"));// ignoreI18n
            }
            Object triggerApprovalFlow = extendsInfo.get(TRIGGER_APPROVAL_FLOW);
            if (Objects.equals(triggerApprovalFlow, Boolean.FALSE)) {
                configList.add(I18NExt.getOrDefault(I18NKey.LOG_CONFIG_TRIGGER_APPROVAL_FLOW, "触发审批流-否"));// ignoreI18n
            }
            if (CollectionUtils.notEmpty(configList)) {
                String configInfo = String.join(",", configList);
                return I18NExt.getOrDefault(I18NKey.LOG_CONFIG_MESSAGE, "配置信息：{0}", configInfo);// ignoreI18n
            }
            return null;
        }

        public String convertToI18NPeerDisplayName() {
            String result = null;
            if (Objects.nonNull(internationalPeerDisplayName)) {
                result = internationalPeerDisplayName.getInternationalValue();
            }
            if (StringUtils.isNotBlank(peerDisplayNameI18nKey)) {
                result = I18N.text(peerDisplayNameI18nKey);
            }
            if (StringUtils.isBlank(result)) {
                result = peerDisplayName;
            }
            return result;
        }
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    public static class ApprovalFlowInfo {
        // 人员信息
        private EmployeeInfo owner;
        // 操作类型
        private String operationType;
        // 操作类型名称
        private String operationLabel;
        // 操作类型名称多语key
        private String operationLabelKey;
        // 对象名称
        private String objectLabel;
        // 流程通过这个字段控制是否显示对象名称
        private boolean showObjectLabel;

        public String convertToI18NOperationLabel() {
            String result = null;
            if (StringUtils.isNotBlank(operationLabelKey)) {
                result = I18N.text(operationLabelKey);
            }
            if (StringUtils.isBlank(result)) {
                result = operationLabel;
            }
            return result;
        }
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    public static class EmployeeInfo {
        private String name;
        private int employeeID;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetailInfo {
        private String objectApiName;
        private String objectLabel;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DiffObjectData {
        private String fieldApiName;
        private String renderType;
        private Map<String, Object> value;
        private Map<String, Object> oldValue;
        private Map<String, Object> rawValue;
        private Map<String, Object> rawOldValue;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DiffData {
        private String field;
        private Object before;
        private Object after;
    }

    @Data
    public static class LintMessage {
        int type;
        String text;
        InternationalItem internationalText;
        String dataID;
        String objectApiName;
        int objectType;

        public LintMessage() {

        }

        public LintMessage(String text, String dataID, String objectApiName) {
            this.text = StringUtils.trimToEmpty(text);
            this.dataID = dataID;
            this.objectApiName = objectApiName;
            if (dataID == null || dataID.isEmpty()) {
                type = 0;
            } else {
                type = 1;
            }
        }

        public LintMessage(String text, String dataID, String objectApiName, InternationalItem internationalText) {
            this.text = StringUtils.trimToEmpty(text);
            this.dataID = dataID;
            this.objectApiName = objectApiName;
            this.internationalText = internationalText;
            if (dataID == null || dataID.isEmpty()) {
                type = 0;
            } else {
                type = 1;
            }
        }

        public String convertToI18nText() {
            String result = null;
            if (Objects.nonNull(internationalText)) {
                result = internationalText.getInternationalValue();
            }
            if (StringUtils.isBlank(result)) {
                result = text;
            }
            return result;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            LintMessage that = (LintMessage) o;
            return type == that.type && Objects.equals(text, that.text) && Objects.equals(dataID, that.dataID) && Objects.equals(objectApiName, that.objectApiName);
        }

        @Override
        public int hashCode() {
            return Objects.hash(type, text, dataID, objectApiName);
        }
    }

    public static ObjectSnapshot createSnapshot(IObjectDescribe describe, IObjectData data) {
        LintMessage txtMessage = new LintMessage(describe.getDisplayName(), "", "");
        LintMessage lintMessage = new LintMessage(data.getName(), data.getId(), describe.getApiName());

        ObjectSnapshot ret = ObjectSnapshot.builder()
                .textMsg(Lists.newArrayList(txtMessage, lintMessage))
                .snapshot(getSnapshot(describe, data))
                .traceId(TraceContext.get().getTraceId())
                .build();
        return ret;
    }

    public static ObjectSnapshot createSnapshot(IObjectDescribe describe, IObjectData data, Map<String, Object> extendsInfo) {
        LintMessage txtMessage = new LintMessage(describe.getDisplayName(), "", "");
        LintMessage lintMessage = new LintMessage(data.getName(), data.getId(), describe.getApiName());

        ObjectSnapshot ret = ObjectSnapshot.builder()
                .textMsg(Lists.newArrayList(txtMessage, lintMessage))
                .snapshot(getSnapshot(describe, data))
                .extendsInfo(extendsInfo)
                .traceId(TraceContext.get().getTraceId())
                .build();
        return ret;
    }

    public static ObjectSnapshot createSnapshot(IObjectDescribe describe, IObjectData data, Map<String, Object> updatedFieldMap,
                                                Map<String, IObjectData> dbDataMap, Map<String, Object> extendsInfo) {
        // 如果data中没主属性，取dbData中的主属性存到snapshot中
        if (StringUtils.isBlank(data.getName()) && dbDataMap.get(data.getId()) != null) {
            data.setName(dbDataMap.get(data.getId()).getName());
        }

        LintMessage txtMessage = new LintMessage(describe.getDisplayName(), "", "");
        LintMessage lintMessage = new LintMessage(data.getName(), data.getId(), describe.getApiName());

        List<LogInfo.DiffObjectData> objectDataList = Lists.newArrayList();
        ObjectDataExt.of(updatedFieldMap).removeFieldForLog(describe);
        CollectionUtils.nullToEmpty(updatedFieldMap).forEach((fieldApiName, fieldValue) -> {
            if (StringUtils.isBlank(fieldApiName) ||
                    fieldApiName.endsWith("__r") ||
                    fieldApiName.endsWith("__l")) {
                return;
            }
            IFieldDescribe fieldDescribe = ObjectDataExt.getLogFieldDescribe(describe, fieldApiName);
            if (Objects.isNull(fieldDescribe)) {
                return;
            }
            String fieldType = fieldDescribe.getType();
            Map<String, Object> oldValue = Maps.newHashMap();
            Map<String, Object> value = Maps.newHashMap();

            oldValue.put(fieldApiName, clearValue(getOldValue(data, dbDataMap, fieldApiName), fieldDescribe));
            value.put(fieldApiName, data.get(fieldApiName));

            //补充__r,__l等字段信息
            if (needFillFieldInfoList.contains(fieldType)) {
                fillExtendValue(data, dbDataMap, fieldDescribe, oldValue, value);
            }
            //单选、多选其他补充__o
            if (IFieldType.SELECT_ONE.equals(fieldType) || IFieldType.SELECT_MANY.equals(fieldType)) {
                if (updatedFieldMap.containsKey(FieldDescribeExt.getSelectOther(fieldApiName))) {
                    return;
                }
                fillOtherValue(data, dbDataMap, fieldApiName, oldValue, value);
            }
            //处理富文本
            boolean isBigText = IFieldType.BIG_TEXT.equals(fieldType);
            boolean isCooperativeRichText = RichTextExt.isProcessableCooperativeRichText(fieldDescribe);
            if (RichTextExt.isProcessableRichText(fieldDescribe) || isBigText || isCooperativeRichText) {
                String richTextAbstractName = RichTextExt.getRichTextAbstractName(fieldDescribe.getApiName());
                if (Objects.equals(fieldApiName, richTextAbstractName)) {
                    return;
                }
                Object oldRowValue = getOldValue(data, dbDataMap, fieldDescribe.getApiName());
                Object rowValue = data.get(fieldDescribe.getApiName());
                if (isCooperativeRichText) {
                    if (Objects.nonNull(rowValue)) {
                        String oldText = null;
                        if (Objects.nonNull(oldRowValue) && oldRowValue instanceof JSONObject) {
                            oldText = ((JSONObject) oldRowValue).getString("text");
                        }
                        String rowText = rowValue instanceof JSONObject ? ((JSONObject) rowValue).getString("text") : null;
                        if (StringUtils.isEmpty(oldText) && StringUtils.isEmpty(rowText)) {
                            return;
                        }
                    }
                } else {
                    if (Objects.nonNull(oldRowValue)) {
                        oldValue.put(richTextAbstractName, isBigText ? oldRowValue : RichTextExt.parseTextFromHtml((String) oldRowValue));
                    }
                    if (Objects.nonNull(rowValue)) {
                        value.put(richTextAbstractName, isBigText ? rowValue : RichTextExt.parseTextFromHtml((String) rowValue));
                    }
                }
            }
            if (AppFrameworkConfig.objectMultiLangGray(describe.getTenantId(), describe.getApiName())) {
                if (updatedFieldMap.containsKey(FieldDescribeExt.getMultiLangExtraFieldName(fieldApiName))) {
                    return;
                }
                fillMultiLangValue(data, dbDataMap, fieldApiName, fieldDescribe, oldValue, value);
            }

            DiffObjectData objectData = DiffObjectData.builder()
                    .fieldApiName(getFieldApiName(fieldApiName))
                    .renderType(fieldType)
                    .oldValue(oldValue)
                    .value(value)
                    .build();

            objectDataList.add(objectData);
        });


        return ObjectSnapshot.builder()
                .textMsg(Lists.newArrayList(txtMessage, lintMessage))
                .snapshot(getSnapshot(describe, data))
                .extendsInfo(extendsInfo)
                .objectDataList(objectDataList)
                .peerReason(Objects.isNull(RequestContextManager.getContext()) ? "" : RequestContextManager.getContext().getPeerReason())
                .traceId(TraceContext.get().getTraceId())
                .build();
    }

    public static ObjectSnapshot createSnapshot(IObjectDescribe describe, IObjectData data, Map<String, Object> updatedFieldMap,
                                                Map<String, IObjectData> dbDataMap) {
        return createSnapshot(describe, data, updatedFieldMap, dbDataMap, Maps.newHashMap());
    }

    private static String getFieldApiName(String fieldName) {
        if (FieldDescribeExt.isSelectOtherField(fieldName)) {
            return FieldDescribeExt.getSelectFieldFromOtherField(fieldName);
        }
        if (FieldDescribeExt.isMultiLangExtraField(fieldName)) {
            return FieldDescribeExt.getMultiLangFieldFromExtraField(fieldName);
        }
        return fieldName;
    }

    private static void fillMultiLangValue(IObjectData data, Map<String, IObjectData> dbDataMap, String fieldName, IFieldDescribe fieldDescribe, Map<String, Object> oldValue, Map<String, Object> value) {

        if (BooleanUtils.isNotTrue(fieldDescribe.getEnableMultiLang())) {
            return;
        }
        if (FieldDescribeExt.isMultiLangExtraField(fieldName)) {
            fieldName = fieldDescribe.getApiName();
            if (Objects.isNull(oldValue.get(fieldName))) {
                oldValue.put(fieldName, getOldValue(data, dbDataMap, fieldName));
            }
            if (Objects.isNull(value.get(fieldName))) {
                value.put(fieldName, data.get(fieldName));
            }
            return;
        }
        fieldName = FieldDescribeExt.getMultiLangExtraFieldName(fieldName);
        if (Objects.nonNull(data.get(fieldName))) {
            value.put(fieldName, data.get(fieldName));
        }
        Object dbValue = getOldValue(data, dbDataMap, fieldName);
        if (Objects.nonNull(dbValue)) {
            oldValue.put(fieldName, dbValue);
        }
    }

    private static void fillExtendValue(IObjectData data, Map<String, IObjectData> dbDataMap, IFieldDescribe fieldDescribe, Map<String, Object> oldValue, Map<String, Object> value) {
        String fieldName = FieldDescribeExt.of(fieldDescribe).getFieldExtendName();
        Object dbValue = getOldValue(data, dbDataMap, fieldName);
        Object updateValue = data.get(fieldName);
        if (Objects.nonNull(dbValue)) {
            oldValue.put(fieldName, dbValue);
        }
        if (Objects.nonNull(updateValue)) {
            value.put(fieldName, updateValue);
        }
    }

    private static void fillOtherValue(IObjectData data, Map<String, IObjectData> dbDataMap, String fieldApiName, Map<String, Object> oldValue, Map<String, Object> value) {
        if (fieldApiName.endsWith("__o")) {
            fieldApiName = StringUtils.substringBefore(fieldApiName, "__o");
            if (Objects.isNull(oldValue.get(fieldApiName))) {
                oldValue.put(fieldApiName, getOldValue(data, dbDataMap, fieldApiName));
            }
            if (Objects.isNull(value.get(fieldApiName))) {
                value.put(fieldApiName, data.get(fieldApiName));
            }
            return;
        }

        if (Objects.nonNull(data.get(FieldDescribeExt.getSelectOther(fieldApiName)))) {
            value.put(FieldDescribeExt.getSelectOther(fieldApiName), data.get(FieldDescribeExt.getSelectOther(fieldApiName)));
        }

        Object dbValue = getOldValue(data, dbDataMap, FieldDescribeExt.getSelectOther(fieldApiName));
        if (Objects.nonNull(dbValue)) {
            oldValue.put(FieldDescribeExt.getSelectOther(fieldApiName), dbValue);
        }
    }

    /**
     * 可能额外生成了一些不需要存储内容，不需要存储，在此清理
     * 比如 图片\附件数据中的 signedUrl
     *
     * @param value         清理前
     * @param fieldDescribe 根据字段类型
     * @return 清理后
     */
    @SuppressWarnings("unchecked")
    private static Object clearValue(Object value, IFieldDescribe fieldDescribe) {
        if (Objects.isNull(value)) {
            return null;
        }
        if (StringUtils.equalsAny(fieldDescribe.getType(), IFieldType.IMAGE, IFieldType.FILE_ATTACHMENT)) {
            if (value instanceof Collection) {
                ((Collection<?>) value).forEach(item -> {
                    if (item instanceof Map && ((Map) item).containsKey(ImageExt.SIGNATURE_URL)) {
                        ((Map) item).put(ImageExt.SIGNATURE_URL, null);
                    }
                });
            }
        }
        return value;
    }

    private static Object getOldValue(IObjectData data, Map<String, IObjectData> dbDataMap, String fieldApiName) {
        if (Objects.isNull(data) || CollectionUtils.empty(dbDataMap) || Objects.isNull(dbDataMap.get(data.getId()))) {
            return null;
        }
        return dbDataMap.get(data.getId()).get(fieldApiName);
    }

    private static Map<String, Object> getSnapshot(IObjectDescribe describe, IObjectData data) {
        Map snapshot = Optional.ofNullable(((ObjectData) data).getContainerDocument()).map(x -> new HashMap(x)).orElse(Maps.newHashMap());
        if (describe != null) {
            snapshot.put(IObjectDescribe.DISPLAY_NAME, describe.getDisplayName());
        }
        // 处理图片字段
        clearValue(describe, data);
        return snapshot;
    }

    public static List<Map<String, Object>> getSourceSnapshot(IObjectDescribe describe, List<IObjectData> dataList) {
        List<Map<String, Object>> sourceSnapshot = Lists.newArrayList();
        dataList.stream().forEach(data -> {
            Map snapshot = Optional.ofNullable(((ObjectData) data).getContainerDocument()).map(x -> new HashMap(x)).orElse(Maps.newHashMap());
            snapshot.put(IObjectDescribe.DISPLAY_NAME, describe.getDisplayName());
            ObjectDescribeExt.of(describe).getMasterDetailFieldDescribe()
                    .ifPresent(fieldDescribe -> snapshot.put(MASTER_ID_MARK, snapshot.get(fieldDescribe.getApiName())));
            sourceSnapshot.add(snapshot);
        });
        return sourceSnapshot;
    }

    private static void clearValue(IObjectDescribe objDesc, IObjectData objData) {
        List<Image> imageFields = ObjectDescribeExt.of(objDesc).getImageFields();
        if (Objects.isNull(imageFields)) {
            return;
        }
        imageFields.forEach(field -> {
            Object value = objData.get(field.getApiName());
            if (ObjectUtils.isEmpty(value)) {
                return;
            }
            objData.set(field.getApiName(), clearValue(value, field));
        });
    }


    public static ObjectSnapshot createDataSnapshotWithInternalMessage(IObjectDescribe describe, IObjectData data, String message, InternationalItem internationalText) {
        LintMessage txtMessage = new LintMessage(describe.getDisplayName(), "", "");
        LintMessage lintMessage = new LintMessage(data.getName(), data.getId(), describe.getApiName());
        LintMessage customMessage = new LintMessage(message, "", "", internationalText);
        return ObjectSnapshot.builder()
                .textMsg(Lists.newArrayList(txtMessage, lintMessage, customMessage))
                .snapshot(getSnapshot(describe, data))
                .traceId(TraceContext.get().getTraceId())
                .build();
    }

    public static ObjectSnapshot createSnapshotWithInternalMessage(IObjectDescribe describe, IObjectData data, String message, InternationalItem internationalText) {
        LintMessage customMessage = new LintMessage(message, "", "", internationalText);
        return ObjectSnapshot.builder()
                .textMsg(Lists.newArrayList(customMessage))
                .snapshot(getSnapshot(describe, data))
                .traceId(TraceContext.get().getTraceId())
                .build();
    }

    public static ObjectSnapshot createSnapshotWithCustomMessage(IObjectDescribe describe, IObjectData data, String message) {
        LintMessage txtMessage = new LintMessage(describe.getDisplayName(), "", "");
        LintMessage lintMessage = new LintMessage(data.getName(), data.getId(), describe.getApiName());
        LintMessage customMessage = new LintMessage(message, "", "");
        return ObjectSnapshot.builder()
                .textMsg(Lists.newArrayList(txtMessage, lintMessage, customMessage))
                .snapshot(getSnapshot(describe, data))
                .traceId(TraceContext.get().getTraceId())
                .build();
    }

    public static ObjectSnapshot createSnapshotWithOnlyCustomMessage(IObjectDescribe describe, IObjectData data, String message) {
        LintMessage customMessage = new LintMessage(message, "", "");
        return ObjectSnapshot.builder()
                .textMsg(Lists.newArrayList(customMessage))
                .snapshot(getSnapshot(describe, data))
                .traceId(TraceContext.get().getTraceId())
                .build();
    }


}
