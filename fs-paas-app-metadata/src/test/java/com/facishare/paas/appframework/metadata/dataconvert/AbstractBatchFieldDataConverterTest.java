package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GenerateByAI
 * 测试AbstractBatchFieldDataConverter抽象类的通用逻辑
 */
class AbstractBatchFieldDataConverterTest extends BaseDataConverterTest {

  private TestBatchFieldDataConverter converter;

  @BeforeEach
  void setUp() {
    super.setUp();
    converter = new TestBatchFieldDataConverter();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法当context为null时抛出异常
   */
  @Test
  @DisplayName("测试convertFieldData - context为null抛出异常")
  void testConvertFieldDataThrowsNullPointerException_WhenContextIsNull() {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createTextFieldDescribe("test_field");
    List<IObjectData> dataList = createObjectDataList(createObjectData("test_field", "value"));

    // 执行并验证异常
    NullPointerException exception = assertThrows(NullPointerException.class, () -> {
      converter.convertFieldData(dataList, fieldDescribe, (DataConvertContext) null);
    });

    // 验证异常信息
    assertEquals("context", exception.getMessage());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法当数据列表为空时不调用convert方法
   */
  @Test
  @DisplayName("测试convertFieldData - 空数据列表不调用convert")
  void testConvertFieldData_EmptyDataList() {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createTextFieldDescribe("test_field");
    List<IObjectData> dataList = Collections.<IObjectData>emptyList();

    // 执行被测试方法
    converter.convertFieldData(dataList, fieldDescribe, context);

    // 验证结果
    assertFalse(converter.wasConvertCalled());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法过滤空值数据
   */
  @ParameterizedTest
  @MethodSource("provideEmptyValueTestData")
  @DisplayName("测试convertFieldData - 过滤空值数据")
  void testConvertFieldData_FilterEmptyValues(Object emptyValue) {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createTextFieldDescribe("test_field");
    List<IObjectData> dataList = Arrays.asList(
        createObjectData("test_field", emptyValue),
        createObjectData("test_field", "valid_value")
    );

    // 执行被测试方法
    converter.convertFieldData(dataList, fieldDescribe, context);

    // 验证结果
    assertTrue(converter.wasConvertCalled());
    assertEquals(1, converter.getReceivedDataList().size());
    assertEquals("valid_value", converter.getReceivedDataList().get(0).get("test_field"));
  }

  /**
   * 提供空值测试数据
   */
  private static Stream<Arguments> provideEmptyValueTestData() {
    return Stream.of(
        Arguments.of((Object) null),
        Arguments.of(""),
        Arguments.of(Collections.emptyList()),
        Arguments.of(new ArrayList<>())
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法当所有数据都为空值时不调用convert方法
   */
  @Test
  @DisplayName("测试convertFieldData - 所有数据为空值不调用convert")
  void testConvertFieldData_AllEmptyValues() {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createTextFieldDescribe("test_field");
    List<IObjectData> dataList = Arrays.asList(
        createObjectData("test_field", null),
        createObjectData("test_field", ""),
        createObjectData("test_field", Collections.emptyList())
    );

    // 执行被测试方法
    converter.convertFieldData(dataList, fieldDescribe, context);

    // 验证结果
    assertFalse(converter.wasConvertCalled());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法正常处理有效数据
   */
  @Test
  @DisplayName("测试convertFieldData - 正常处理有效数据")
  void testConvertFieldData_ValidData() {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createTextFieldDescribe("test_field");
    List<IObjectData> dataList = Arrays.asList(
        createObjectData("test_field", "value1"),
        createObjectData("test_field", "value2"),
        createObjectData("test_field", "value3")
    );

    // 执行被测试方法
    converter.convertFieldData(dataList, fieldDescribe, context);

    // 验证结果
    assertTrue(converter.wasConvertCalled());
    assertEquals(3, converter.getReceivedDataList().size());
    assertEquals(fieldDescribe, converter.getReceivedFieldDescribe());
    assertEquals(context, converter.getReceivedContext());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法混合有效和无效数据的处理
   */
  @Test
  @DisplayName("测试convertFieldData - 混合有效和无效数据")
  void testConvertFieldData_MixedValidAndInvalidData() {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createTextFieldDescribe("test_field");
    List<IObjectData> dataList = Arrays.asList(
        createObjectData("test_field", null),
        createObjectData("test_field", "valid1"),
        createObjectData("test_field", ""),
        createObjectData("test_field", "valid2"),
        createObjectData("test_field", "   "), // 空白字符串不会被过滤掉
        createObjectData("test_field", "valid3")
    );

    // 执行被测试方法
    converter.convertFieldData(dataList, fieldDescribe, context);

    // 验证结果
    assertTrue(converter.wasConvertCalled());
    assertEquals(4, converter.getReceivedDataList().size()); // 包含空白字符串

    List<Object> receivedValues = converter.getReceivedDataList().stream()
        .map(data -> data.get("test_field"))
        .collect(Collectors.toList());
    assertTrue(receivedValues.contains("valid1"));
    assertTrue(receivedValues.contains("valid2"));
    assertTrue(receivedValues.contains("   ")); // 空白字符串会被保留
    assertTrue(receivedValues.contains("valid3"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理字段不存在的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 字段不存在")
  void testConvertFieldData_FieldNotExists() {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createTextFieldDescribe("non_existing_field");
    List<IObjectData> dataList = Arrays.asList(
        createObjectData("other_field", "value1"),
        createObjectData("another_field", "value2")
    );

    // 执行被测试方法
    converter.convertFieldData(dataList, fieldDescribe, context);

    // 验证结果
    assertFalse(converter.wasConvertCalled());
  }

  /**
   * 测试用的AbstractBatchFieldDataConverter实现类
   */
  private static class TestBatchFieldDataConverter extends AbstractBatchFieldDataConverter {
    private boolean convertCalled = false;
    private List<IObjectData> receivedDataList;
    private IFieldDescribe receivedFieldDescribe;
    private DataConvertContext receivedContext;

    @Override
    protected void convert(List<IObjectData> dataList, IFieldDescribe fieldDescribe, DataConvertContext context) {
      this.convertCalled = true;
      this.receivedDataList = new ArrayList<>(dataList);
      this.receivedFieldDescribe = fieldDescribe;
      this.receivedContext = context;
    }

    @Override
    public List<String> getSupportedFieldTypes() {
      return Arrays.asList(IFieldType.TEXT);
    }

    public boolean wasConvertCalled() {
      return convertCalled;
    }

    public List<IObjectData> getReceivedDataList() {
      return receivedDataList;
    }

    public IFieldDescribe getReceivedFieldDescribe() {
      return receivedFieldDescribe;
    }

    public DataConvertContext getReceivedContext() {
      return receivedContext;
    }
  }
}
