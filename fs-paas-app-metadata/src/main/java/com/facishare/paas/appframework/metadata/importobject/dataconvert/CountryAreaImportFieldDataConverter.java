package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.CountryAreaManager;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2019/07/26
 */
@Slf4j
@Component
public class CountryAreaImportFieldDataConverter extends BaseImportFieldDataConverter {

    @Override
    public List<String> getSupportedFieldTypes() {
        return Lists.newArrayList(IFieldType.COUNTRY, IFieldType.PROVINCE, IFieldType.CITY, IFieldType.DISTRICT);
    }

    @Override
    public ConvertResult convertFieldData(IObjectData objectData, IFieldDescribe fieldDescribe, User user) {
        return convertFieldData(objectData, null, fieldDescribe, user);
    }

    @Override
    public ConvertResult convertFieldData(IObjectData objectData, IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe, User user) {
        String value = getStringValue(objectData, fieldDescribe.getApiName());
        //字段值为空，不处理
        if (Strings.isNullOrEmpty(value)) {
            return ConvertResult.buildSuccess(null);
        }
        Map<String, Map> countryArea = CountryAreaManager.getCountryArea(user.getTenantId());
        String type = fieldDescribe.getType();
        Map areaInfo = countryArea.get(type);
        if (CollectionUtils.empty(areaInfo)) {
            log.warn("import countryArea is empty");
            return ConvertResult.buildSuccess(null);
        }
        List<Map<String, Object>> options = (List<Map<String, Object>>) areaInfo.get("options");
        if (CollectionUtils.empty(options)) {
            log.warn("import countryArea options is empty");
            return ConvertResult.buildSuccess(null);
        }
        if (IFieldType.COUNTRY.equals(type)) {
            Optional<Map<String, Object>> areaOptional = options.stream()
                    .filter(x -> Objects.equals(x.get("label"), value) || Objects.equals(x.get("value"), value))
                    .findFirst();
            return areaOptional.map(codeInfo -> ConvertResult.buildSuccess(codeInfo.get("value")))
                    .orElseGet(() -> ConvertResult.buildError(I18NExt.getOrDefault(I18NKey.AREA_NOT_NULL,
                            fieldDescribe.getLabel() + "不能为空", fieldDescribe.getLabel())));// ignoreI18n
        } else {
            //父级在前面已经转化为code，在data中只能拿到父级code，而子级拿到的是label
            String parentApiName = ((SelectOne) fieldDescribe).getCascadeParentApiName();
            IFieldDescribe parentFieldDescribe = objectDescribe.getFieldDescribe(parentApiName);
            //若父级不存在，拿到的是label，即用户在模板中填充的值
            String parentCode = objectData.get(parentApiName, String.class);
            if (StringUtils.isBlank(parentCode)) {
                return ConvertResult.buildError(I18NExt.getOrDefault(I18NKey.AREA_NOT_NULL,
                        parentFieldDescribe.getLabel() + "不能为空", parentFieldDescribe.getLabel()));// ignoreI18n
            }
            String parentType = getParentType(fieldDescribe.getType());
            Map parentArea = countryArea.get(parentType);
            List<Map<String, Object>> parentOptions = (List<Map<String, Object>>) parentArea.get("options");
            Map<String, Object> parentAreaInfo = parentOptions.stream()
                    .filter(x -> Objects.equals(x.get("label"), parentCode) || Objects.equals(x.get("value"), parentCode))
                    .findFirst()
                    .orElse(Collections.emptyMap());
            //如果为空，代表该父属性不存在，并未转化为code，拿到的是label
            if (CollectionUtils.empty(parentAreaInfo)) {
                return ConvertResult.buildError(I18NExt.getOrDefault(I18NKey.IMPORT_AREA_NOT_MATCH,
                        "【{0}】与【{1}】级联关系不存在，或【{2}】名称不正确。", parentCode, value, value));// ignoreI18n
            }
            List<Map<String, List<String>>> childOptionsList = (List<Map<String, List<String>>>) parentAreaInfo.get("child_options");
            if (CollectionUtils.notEmpty(childOptionsList)) {
                List<String> childAreaCode = childOptionsList.stream()
                        .map(x -> x.get(type))
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList());
                if (CollectionUtils.notEmpty(childAreaCode)) {
                    Map<String, Object> childAreaInfo = options.stream()
                            .filter(x -> Objects.equals(x.get("label"), value)
                                    && childAreaCode.contains(String.valueOf(x.get("value"))))
                            .findFirst()
                            .orElse(null);
                    if (CollectionUtils.notEmpty(childAreaInfo)) {
                        return ConvertResult.buildSuccess(childAreaInfo.get("value"));
                    }
                }
            }
            return ConvertResult.buildError(I18NExt.getOrDefault(I18NKey.IMPORT_AREA_NOT_MATCH,
                    "【{0}】与【{1}】级联关系不存在，或【{2}】名称不正确。", parentAreaInfo.get("label"), value, value));// ignoreI18n
        }
    }

    private String getParentType(String type) {
        switch (type) {
            case IFieldType.PROVINCE:
                return IFieldType.COUNTRY;
            case IFieldType.CITY:
                return IFieldType.PROVINCE;
            case IFieldType.DISTRICT:
                return IFieldType.CITY;
            default:
                return type;
        }
    }
}
