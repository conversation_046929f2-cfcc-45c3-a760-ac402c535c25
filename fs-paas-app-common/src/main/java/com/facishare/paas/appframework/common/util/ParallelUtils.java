package com.facishare.paas.appframework.common.util;

import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.multiRegion.MultiRegionContext;
import com.facishare.paas.multiRegion.MultiRegionContextHolder;
import com.facishare.paas.timezone.TimeZoneContext;
import com.facishare.paas.timezone.TimeZoneContextHolder;
import com.fxiaoke.common.concurrent.DynamicExecutors;
import com.github.trace.TraceContext;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * Created by liyiguang on 16/11/18.
 */
public class ParallelUtils {
    public static final int MAX_PARALLEL_NUM = 200;
    private static final Logger LOG = LoggerFactory.getLogger(ParallelUtils.class);
    private static final ExecutorService executorService;
    private static final ExecutorService backgroundExecutorService;

    static {
        ThreadFactory workerFactory = new ThreadFactoryBuilder().setNameFormat("ParallelUtils-%d").setDaemon(true).build();
        executorService = DynamicExecutors.newThreadPool(50, 300, 10000L, 1000, workerFactory);

        ThreadFactory backgroundWorkerFactory = new ThreadFactoryBuilder().setNameFormat("ParallelUtils-Background-%d").setDaemon(true).build();
        backgroundExecutorService = DynamicExecutors.newThreadPool(5, 50, 10000L, 1000, backgroundWorkerFactory);
    }

    public static ParallelTask createParallelTask() {
        return new ParallelTaskImpl(executorService);
    }

    public static ParallelTask createBackgroundTask() {
        return new ParallelTaskImpl(backgroundExecutorService);
    }

    public interface ParallelTask {

        ParallelTask submit(Runnable runnable);

        boolean await(long timeout, TimeUnit timeUnit) throws TimeoutException;
        
        /**
         * 等待所有任务完成，可选择是否处理异常
         * 
         * @param timeout 超时时间
         * @param timeUnit 时间单位
         * @param propagateExceptions 是否传递异常（true则抛出ExecutionException，false则保持原有行为）
         * @return 是否所有任务都成功完成
         * @throws TimeoutException 如果等待超时
         * @throws ExecutionException 如果任务执行过程中有异常且propagateExceptions为true
         */
        boolean await(long timeout, TimeUnit timeUnit, boolean propagateExceptions) throws TimeoutException, ExecutionException;

        void run();

    }


    private static class ParallelTaskImpl implements ParallelTask {
        private final List<Runnable> runnableList = new ArrayList<>();
        private final ExecutorService executor;

        private Map<String, String> mdcMap;
        private TraceContext traceContext;
        private RequestContext context;
        private TimeZoneContext timeZoneContext;
        private MultiRegionContext multiRegionContext;
        private CacheContext cacheContext;

        public ParallelTaskImpl(ExecutorService executor) {
            this.executor = executor;
            getMDC();
        }

        private void getMDC() {
            try {
                mdcMap = MDC.getCopyOfContextMap();
                traceContext = TraceContext.get().copy();
                context = RequestContextManager.getContext();
                timeZoneContext = TimeZoneContextHolder.getTimezoneContext();
                multiRegionContext = MultiRegionContextHolder.getMultiRegionContext();
                cacheContext = CacheContext.getContext();
            } catch (Exception e) {
                LOG.warn("getMDC", e);
            }
        }

        private void putMDC() {
            try {
                if (mdcMap != null) {
                    MDC.setContextMap(mdcMap);
                } else {
                    MDC.put("traceId", traceContext.getTraceId());
                    MDC.put("userId", traceContext.getUid());
                }
                TraceContext._set(traceContext);
                RequestContextManager.setContext(context);
                TimeZoneContextHolder.setTimezoneContext(timeZoneContext);
                MultiRegionContextHolder.setMultiZoneContext(multiRegionContext);
                CacheContext.setContext(cacheContext);
            } catch (Exception e) {
                LOG.warn("putMDC", e);
            }
        }

        private void clearMDC() {
            try {
                MDC.clear();
                TraceContext.remove();
                RequestContextManager.removeContext();
                TimeZoneContextHolder.clearContext();
                MultiRegionContextHolder.clearContext();
                CacheContext.clearContext();
            } catch (Exception e) {
                LOG.warn("clearMDC", e);
            }
        }


        @Override
        public ParallelTask submit(Runnable runnable) {
            if (runnable != null) {
                if (runnableList.size() <= MAX_PARALLEL_NUM) {
                    runnableList.add(runnable);
                } else {
                    throw new ParallelTaskValidateException("Max Parallel Task Number:" + MAX_PARALLEL_NUM);
                }
            }
            return this;
        }

        @Override
        public boolean await(long timeout, TimeUnit timeUnit) throws TimeoutException {
            try {
                return await(timeout, timeUnit, false);
            } catch (ExecutionException e) {
                // 理论上不会执行到这里，因为propagateExceptions=false时不会抛出ExecutionException
                // 但为了类型安全，仍需捕获
                return false;
            }
        }
        
        @Override
        public boolean await(long timeout, TimeUnit timeUnit, boolean propagateExceptions) throws TimeoutException, ExecutionException {
            // 清空之前可能存在的异常
            ConcurrentLinkedQueue<Throwable> exceptions = new ConcurrentLinkedQueue<>();
            final AtomicBoolean ret = new AtomicBoolean(true);

            if (runnableList.isEmpty()) {
                return true;
            }
            CountDownLatch countDownLatch = new CountDownLatch(runnableList.size());

            for (Runnable runnable : runnableList) {
                try {
                    executor.submit(() -> {
                        try {
                            putMDC();
                            runnable.run();
                        } catch (AppBusinessException e) {
                            ret.compareAndSet(true, false);
                            if (propagateExceptions) {
                                exceptions.add(e); // 只在需要传递异常时收集
                            }
                            LOG.warn("execute task error", e);
                        } catch (Exception e) {
                            ret.compareAndSet(true, false);
                            if (propagateExceptions) {
                                exceptions.add(e); // 只在需要传递异常时收集
                            }
                            LOG.error("execute task error", e);
                        } finally {
                            countDownLatch.countDown();
                            clearMDC();
                        }
                    });
                } catch (Exception e) {
                    LOG.error("submit task error!", e);
                    if (propagateExceptions) {
                        exceptions.add(e);
                    }
                    throw e;
                }
            }

            try {
                boolean finished = countDownLatch.await(timeout, timeUnit);
                if (!finished) {
                    throw createTimeoutException();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new TimeoutException("execute task interrupted");
            }

            // 如果需要传递异常且有异常，则抛出
            if (propagateExceptions && !exceptions.isEmpty()) {
                Throwable firstException = exceptions.peek();
                if (firstException instanceof RuntimeException) {
                    throw (RuntimeException) firstException;
                } else {
                    // 对于checked异常，仍然需要包装
                    throw new ExecutionException("Task execution failed with exception", firstException);
                }
            }

            return ret.get();
        }

        @Override
        public void run() {
            if (runnableList.isEmpty()) {
                return;
            }

            for (Runnable runnable : runnableList) {
                try {
                    executor.submit(() -> {
                        try {
                            putMDC();
                            runnable.run();
                        } catch (AppBusinessException e) {
                            LOG.warn("execute task error", e);
                        } catch (Exception e) {
                            LOG.error("execute task error", e);
                        } finally {
                            clearMDC();
                        }
                    });
                } catch (Exception e) {
                    LOG.error("submit task error!", e);
                }
            }
        }

        /**
         * 创建超时异常
         */
        private TimeoutException createTimeoutException() {
            if (executor instanceof ThreadPoolExecutor) {
                ThreadPoolExecutor poolExecutor = (ThreadPoolExecutor) executor;
                return new TimeoutException(String.format("execute task timeout,poolSize:%s,activeCount:%s,queueSize:%s",
                        poolExecutor.getPoolSize(), poolExecutor.getActiveCount(), poolExecutor.getQueue().size()));
            } else {
                return new TimeoutException("execute task timeout");
            }
        }
    }

//    public static void main(String[] args) throws Exception {
//        boolean ret = createParallelTask()
//                .submit(() -> System.out.println("1"))
//                .submit(() -> System.out.println("2"))
//                .await(1, TimeUnit.MILLISECONDS);
//
//        System.out.println(ret);
//
//        ret = createParallelTask()
//                .submit(() -> {
//                    System.out.println("1");
//                    throw new RuntimeException("test");
//                })
//                .submit(() -> System.out.println("2"))
//                .await(1, TimeUnit.MILLISECONDS);
//
//        System.out.println(ret);
//    }

    public static class ParallelTaskValidateException extends RuntimeException {
        public ParallelTaskValidateException() {
        }

        public ParallelTaskValidateException(String message) {
            super(message);
        }

        public ParallelTaskValidateException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
