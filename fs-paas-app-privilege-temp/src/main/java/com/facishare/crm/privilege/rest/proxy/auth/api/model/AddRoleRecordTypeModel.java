package com.facishare.crm.privilege.rest.proxy.auth.api.model;

import com.facishare.crm.privilege.rest.proxy.auth.api.pojo.RecordTypePojo;
import com.facishare.crm.privilege.rest.proxy.auth.model.BaseArg_Auth;
import com.facishare.crm.privilege.rest.proxy.auth.model.BaseAuthResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by yusb on 17/4/13.
 */
public class AddRoleRecordTypeModel {

  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class Arg extends BaseArg_Auth {
    private List<RecordTypePojo> recordTypePojos;
    private String entityId;
    private String recordTypeId;
  }

  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class Result extends BaseAuthResult {
  }

}
