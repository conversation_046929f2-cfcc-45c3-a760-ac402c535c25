package com.facishare.paas.appframework.privilege.dto;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.OutDataPrivilege;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * Created by zhouwr on 2017/10/23
 */
public enum Permissions {
    NO_PERMISSION("0", I18NKey.CONSTANT_NO_PERMISSION),
    READ_ONLY("1", I18NKey.constant_read_only),
    READ_WRITE("2", I18NKey.constant_read_write);

    private String value;
    private String labelKey;

    Permissions(String value, String labelKey) {
        this.value = value;
        this.labelKey = labelKey;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return I18N.text(labelKey);
    }

    public int intValue() {
        return Integer.parseInt(value);
    }

    public static String getLabelByValue(String value) {
        for (Permissions permission : Permissions.values()) {
            if (StringUtils.equals(value, permission.value)) {
                return permission.getLabel();
            }
        }
        return "";
    }

    public static Permissions getEnumByValue(String value) {
        if (value == null) return NO_PERMISSION;
        for (Permissions permission : Permissions.values()) {
            if (StringUtils.equals(value, permission.value)) {
                return permission;
            }
        }
        return NO_PERMISSION;
    }

    public final static Map<OutDataPrivilege, Permissions> OUT_DATA_PRIVILEGE_PERMISSIONS_MAP = Maps.newHashMap();

    static {
        OUT_DATA_PRIVILEGE_PERMISSIONS_MAP.put(OutDataPrivilege.PUBLIC_READONLY, Permissions.READ_ONLY);
        OUT_DATA_PRIVILEGE_PERMISSIONS_MAP.put(OutDataPrivilege.PUBLIC_WRITE, Permissions.READ_WRITE);
        OUT_DATA_PRIVILEGE_PERMISSIONS_MAP.put(OutDataPrivilege.SAME_DOWNSTREAM_READONLY, Permissions.READ_ONLY);
        OUT_DATA_PRIVILEGE_PERMISSIONS_MAP.put(OutDataPrivilege.SAME_DOWNSTREAM_WRITE, Permissions.READ_WRITE);
    }
}
