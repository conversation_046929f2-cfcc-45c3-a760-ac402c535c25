package com.facishare.paas.appframework.function.dto;

import com.facishare.function.Function;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface Analyze {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        Function function;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        boolean success;
        String logInfo;
        List<Violation> violations;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Violation {
        int line;
        int priority;
        String message;
    }
}
