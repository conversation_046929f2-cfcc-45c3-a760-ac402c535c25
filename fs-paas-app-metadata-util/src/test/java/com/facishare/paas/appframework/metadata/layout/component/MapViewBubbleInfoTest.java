package com.facishare.paas.appframework.metadata.layout.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class MapViewBubbleInfoTest {

  private MapViewBubbleInfo mapViewBubbleInfo;

  @BeforeEach
  void setUp() {
    mapViewBubbleInfo = new MapViewBubbleInfo();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试MapViewBubbleInfo的无参构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试无参构造函数")
  void testDefaultConstructor() {
    MapViewBubbleInfo info = new MapViewBubbleInfo();
    assertNotNull(info, "无参构造函数创建的对象不应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试MapViewBubbleInfo的Map构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = Maps.newHashMap();
    map.put(IMapViewBubbleInfo.FIELD, "location_field");
    
    // 创建选项数据
    Map<String, Object> option1 = Maps.newHashMap();
    option1.put("value", "option1");
    option1.put("label", "选项1");
    
    Map<String, Object> option2 = Maps.newHashMap();
    option2.put("value", "option2");
    option2.put("label", "选项2");
    
    List<Map<String, Object>> options = Lists.newArrayList(option1, option2);
    map.put(IMapViewBubbleInfo.OPTIONS, options);
    
    MapViewBubbleInfo info = new MapViewBubbleInfo(map);
    assertNotNull(info, "Map构造函数创建的对象不应为空");
    assertEquals("location_field", info.getField(), "应正确设置字段名");
    assertEquals(2, info.getOptions().size(), "应正确设置选项数量");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getField方法
   */
  @Test
  @DisplayName("正常场景 - 测试getField方法")
  void testGetField() {
    // 初始状态应为null
    assertNull(mapViewBubbleInfo.getField(), "初始字段名应为null");
    
    // 通过Map构造函数设置字段名
    Map<String, Object> map = Maps.newHashMap();
    map.put(IMapViewBubbleInfo.FIELD, "address_field");
    MapViewBubbleInfo info = new MapViewBubbleInfo(map);
    assertEquals("address_field", info.getField(), "应正确获取字段名");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getOptions方法 - 正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试getOptions方法")
  void testGetOptions_NormalScenario() {
    // 创建选项数据
    Map<String, Object> option1 = Maps.newHashMap();
    option1.put("value", "red");
    option1.put("label", "红色");
    option1.put("color", "#FF0000");
    
    Map<String, Object> option2 = Maps.newHashMap();
    option2.put("value", "blue");
    option2.put("label", "蓝色");
    option2.put("color", "#0000FF");
    
    List<Map<String, Object>> optionsData = Lists.newArrayList(option1, option2);
    
    Map<String, Object> map = Maps.newHashMap();
    map.put(IMapViewBubbleInfo.OPTIONS, optionsData);
    
    MapViewBubbleInfo info = new MapViewBubbleInfo(map);
    List<IMapViewOptionBubbleInfo> options = info.getOptions();
    
    assertNotNull(options, "选项列表不应为空");
    assertEquals(2, options.size(), "应有2个选项");
    assertTrue(options.get(0) instanceof MapViewOptionBubbleInfo, "选项应是MapViewOptionBubbleInfo实例");
    assertTrue(options.get(1) instanceof MapViewOptionBubbleInfo, "选项应是MapViewOptionBubbleInfo实例");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getOptions方法 - 空列表场景
   */
  @Test
  @DisplayName("边界场景 - 测试getOptions方法处理空列表")
  void testGetOptions_EmptyList() {
    Map<String, Object> map = Maps.newHashMap();
    map.put(IMapViewBubbleInfo.OPTIONS, Lists.newArrayList());
    
    MapViewBubbleInfo info = new MapViewBubbleInfo(map);
    List<IMapViewOptionBubbleInfo> options = info.getOptions();
    
    assertNotNull(options, "选项列表不应为空");
    assertTrue(options.isEmpty(), "空列表应返回空选项列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getOptions方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试getOptions方法处理null值")
  void testGetOptions_NullValue() {
    Map<String, Object> map = Maps.newHashMap();
    map.put(IMapViewBubbleInfo.OPTIONS, null);
    
    MapViewBubbleInfo info = new MapViewBubbleInfo(map);
    List<IMapViewOptionBubbleInfo> options = info.getOptions();
    
    assertNotNull(options, "选项列表不应为空");
    assertTrue(options.isEmpty(), "null值应返回空选项列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getOptions方法 - 未设置选项场景
   */
  @Test
  @DisplayName("边界场景 - 测试getOptions方法未设置选项")
  void testGetOptions_NoOptions() {
    // 不设置OPTIONS字段
    MapViewBubbleInfo info = new MapViewBubbleInfo();
    List<IMapViewOptionBubbleInfo> options = info.getOptions();
    
    assertNotNull(options, "选项列表不应为空");
    assertTrue(options.isEmpty(), "未设置选项应返回空列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setOptions方法 - 正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试setOptions方法")
  void testSetOptions_NormalScenario() {
    // 创建选项对象
    Map<String, Object> optionMap1 = Maps.newHashMap();
    optionMap1.put("value", "green");
    optionMap1.put("label", "绿色");
    MapViewOptionBubbleInfo option1 = new MapViewOptionBubbleInfo(optionMap1);
    
    Map<String, Object> optionMap2 = Maps.newHashMap();
    optionMap2.put("value", "yellow");
    optionMap2.put("label", "黄色");
    MapViewOptionBubbleInfo option2 = new MapViewOptionBubbleInfo(optionMap2);
    
    List<IMapViewOptionBubbleInfo> options = Lists.newArrayList(option1, option2);
    
    // 设置选项
    mapViewBubbleInfo.setOptions(options);
    
    // 验证设置结果
    List<IMapViewOptionBubbleInfo> retrievedOptions = mapViewBubbleInfo.getOptions();
    assertNotNull(retrievedOptions, "获取的选项列表不应为空");
    assertEquals(2, retrievedOptions.size(), "应有2个选项");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setOptions方法 - 空列表场景
   */
  @Test
  @DisplayName("边界场景 - 测试setOptions方法处理空列表")
  void testSetOptions_EmptyList() {
    List<IMapViewOptionBubbleInfo> emptyOptions = Lists.newArrayList();
    
    mapViewBubbleInfo.setOptions(emptyOptions);
    
    List<IMapViewOptionBubbleInfo> retrievedOptions = mapViewBubbleInfo.getOptions();
    assertNotNull(retrievedOptions, "获取的选项列表不应为空");
    assertTrue(retrievedOptions.isEmpty(), "设置空列表应返回空选项列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setOptions方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试setOptions方法处理null值")
  void testSetOptions_NullValue() {
    mapViewBubbleInfo.setOptions(null);
    
    List<IMapViewOptionBubbleInfo> retrievedOptions = mapViewBubbleInfo.getOptions();
    assertNotNull(retrievedOptions, "获取的选项列表不应为空");
    assertTrue(retrievedOptions.isEmpty(), "设置null值应返回空选项列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口实现
   */
  @Test
  @DisplayName("正常场景 - 测试接口实现")
  void testInterfaceImplementation() {
    // 验证实现了IMapViewBubbleInfo接口
    assertTrue(mapViewBubbleInfo instanceof IMapViewBubbleInfo, 
               "应实现IMapViewBubbleInfo接口");
    
    // 验证实现了JsonCompatible接口
    assertTrue(mapViewBubbleInfo instanceof com.facishare.paas.metadata.api.JsonCompatible, 
               "应实现JsonCompatible接口");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试序列化版本号
   */
  @Test
  @DisplayName("正常场景 - 测试序列化版本号")
  void testSerialVersionUID() {
    // 验证类有序列化版本号（通过反射检查）
    try {
      java.lang.reflect.Field field = MapViewBubbleInfo.class.getDeclaredField("serialVersionUID");
      field.setAccessible(true);
      long serialVersionUID = field.getLong(null);
      assertEquals(-3676610604284316884L, serialVersionUID, "序列化版本号应正确");
    } catch (Exception e) {
      fail("应该有serialVersionUID字段");
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试继承的AbstractComponentInfoDocument功能
   */
  @Test
  @DisplayName("正常场景 - 测试继承功能")
  void testInheritedFunctionality() {
    // 验证继承自AbstractComponentInfoDocument
    assertTrue(mapViewBubbleInfo instanceof AbstractComponentInfoDocument, 
               "应继承自AbstractComponentInfoDocument");
    
    // 通过Map构造函数测试继承的功能
    Map<String, Object> map = Maps.newHashMap();
    map.put("custom_field", "custom_value");
    map.put(IMapViewBubbleInfo.FIELD, "test_field");
    
    MapViewBubbleInfo info = new MapViewBubbleInfo(map);
    assertEquals("test_field", info.getField(), "应正确处理字段名");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试完整的地图气泡配置
   */
  @Test
  @DisplayName("正常场景 - 测试完整地图气泡配置")
  void testCompleteMapBubbleConfiguration() {
    // 创建完整的地图气泡配置
    Map<String, Object> option1 = Maps.newHashMap();
    option1.put("value", "high");
    option1.put("label", "高优先级");
    option1.put("color", "#FF0000");
    option1.put("icon", "high_priority_icon");
    
    Map<String, Object> option2 = Maps.newHashMap();
    option2.put("value", "medium");
    option2.put("label", "中优先级");
    option2.put("color", "#FFFF00");
    option2.put("icon", "medium_priority_icon");
    
    Map<String, Object> option3 = Maps.newHashMap();
    option3.put("value", "low");
    option3.put("label", "低优先级");
    option3.put("color", "#00FF00");
    option3.put("icon", "low_priority_icon");
    
    List<Map<String, Object>> optionsData = Lists.newArrayList(option1, option2, option3);
    
    Map<String, Object> map = Maps.newHashMap();
    map.put(IMapViewBubbleInfo.FIELD, "priority_field");
    map.put(IMapViewBubbleInfo.OPTIONS, optionsData);
    
    MapViewBubbleInfo info = new MapViewBubbleInfo(map);
    
    // 验证完整配置
    assertEquals("priority_field", info.getField(), "字段名应正确");
    assertEquals(3, info.getOptions().size(), "应有3个选项");
    
    // 验证选项类型
    for (IMapViewOptionBubbleInfo option : info.getOptions()) {
      assertTrue(option instanceof MapViewOptionBubbleInfo, "每个选项都应是MapViewOptionBubbleInfo实例");
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试选项的设置和获取循环
   */
  @Test
  @DisplayName("正常场景 - 测试选项设置和获取循环")
  void testOptionsSetGetCycle() {
    // 创建初始选项
    Map<String, Object> optionMap = Maps.newHashMap();
    optionMap.put("value", "test_value");
    optionMap.put("label", "测试标签");
    MapViewOptionBubbleInfo option = new MapViewOptionBubbleInfo(optionMap);
    
    List<IMapViewOptionBubbleInfo> originalOptions = Lists.newArrayList(option);
    
    // 设置选项
    mapViewBubbleInfo.setOptions(originalOptions);
    
    // 获取选项
    List<IMapViewOptionBubbleInfo> retrievedOptions = mapViewBubbleInfo.getOptions();
    
    // 验证设置和获取的一致性
    assertEquals(originalOptions.size(), retrievedOptions.size(), "选项数量应一致");
    
    // 再次设置获取的选项
    mapViewBubbleInfo.setOptions(retrievedOptions);
    List<IMapViewOptionBubbleInfo> secondRetrievedOptions = mapViewBubbleInfo.getOptions();
    
    assertEquals(retrievedOptions.size(), secondRetrievedOptions.size(), "多次设置获取应保持一致");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试特殊字符字段名
   */
  @Test
  @DisplayName("边界场景 - 测试特殊字符字段名")
  void testSpecialCharacterFieldName() {
    // 测试包含特殊字符的字段名
    String[] specialFieldNames = {
        "field_with_underscore",
        "field-with-dash",
        "field.with.dot",
        "field__c",
        "<EMAIL>",
        "字段中文名称"
    };
    
    for (String fieldName : specialFieldNames) {
      Map<String, Object> map = Maps.newHashMap();
      map.put(IMapViewBubbleInfo.FIELD, fieldName);
      
      MapViewBubbleInfo info = new MapViewBubbleInfo(map);
      assertEquals(fieldName, info.getField(), "应正确处理特殊字符字段名: " + fieldName);
    }
  }
}
