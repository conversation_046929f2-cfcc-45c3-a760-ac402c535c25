package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.ui.layout.IFormField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.Delegate;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * create by zhao<PERSON> on 2018/11/17
 */
@AllArgsConstructor(staticName = "of")
public class FormFieldExt {

    public static final String DATA_OWN_DEPARTMENT = "data_own_department";
    public static final String FULL_LINE = "full_line";

    @Getter
    @Delegate
    private IFormField formComponent;

    public boolean isDataOwnDepartment() {
        return DATA_OWN_DEPARTMENT.equals(getFieldName());
    }

    public static IFormField buildDataOwnDepartment() {
        FormField field = new FormField();
        field.setFieldName(DATA_OWN_DEPARTMENT);
        field.setReadOnly(true);
        field.setRequired(false);
        field.setRenderType(IFieldType.DEPARTMENT);
        return field;
    }

    public static IFormField buildRelevantTeam() {
        FormField field = new FormField();
        field.setFieldName(ObjectDataExt.RELEVANT_TEAM);
        field.setReadOnly(true);
        field.setRequired(false);
        field.setRenderType(ObjectDataExt.RELEVANT_TEAM);
        return field;
    }

    public static FormFieldExt buildFromField(IFieldDescribe fieldDescribe) {
        IFormField formField = new FormField();
        formField.setReadOnly(false);
        formField.setRequired(fieldDescribe.isRequired());
        formField.setRenderType(fieldDescribe.getType());
        formField.setFieldName(fieldDescribe.getApiName());
        return of(formField);
    }

    public static List<IFormField> buildFromFields(List<IFieldDescribe> fieldDescribes) {
        if (CollectionUtils.empty(fieldDescribes)) {
            return Collections.emptyList();
        }
        return fieldDescribes.stream()
                .map(FormFieldExt::buildFromField)
                .map(FormFieldExt::getFormComponent)
                .collect(Collectors.toList());
    }

    public boolean isNotEmpty() {
        return formComponent.getRenderType() != null || formComponent.getFieldName() != null ;
    }

    public Map<String, Object> toMap() {
        return ((DocumentBasedBean) formComponent).getContainerDocument();
    }

    public boolean readOnly() {
        return Boolean.TRUE.equals(isReadOnly());
    }

    public boolean required() {
        return Boolean.TRUE.equals(isRequired());
    }

    public void setTiled(boolean isTiled) {
        set("is_tiled", isTiled);
    }

    public List<String> getPageType() {
        return (List<String>) get("page_type");
    }

    //设计器新建页面存的create，前端获取布局传的add，需要兼容一下
    public boolean showInPage(String page) {
        List<String> pageType = getPageType();
        return CollectionUtils.empty(pageType) || pageType.contains(page) || (LayoutTypes.ADD.equals(page) && pageType.contains("create"));
    }

    public Boolean getFullLine() {
        return (Boolean) get(FULL_LINE);
    }

    public void setFullLine(boolean fullLine) {
        set(FULL_LINE, fullLine);
    }
}
