import org.springframework.util.CollectionUtils

/**
 * <AUTHOR>
 * @codeName SyncOrg2Wps
 * @description init
 * @createTime 2024-12-02
 * @函数需求编号
 */

@AplController(baseUrl = "/syncOrg2Wps")
class SyncOrg2Wps {

    //查询部门
    @AplRequestMapping(value = "/depts", method = RequestMethod.GET)
    public HttpResponse depts(HttpRequest request) {
        Map parameters = request.getParameters()
        log.info("query depts parameters:" + parameters)
        List pageSizeList = parameters["page_size"] as List
        List pageTokenList = parameters["page_token"] as List
        String deptId = (parameters["deptId"] as List).get(0)
        Integer pageSize = 1000 //Integer.parseInt(CollectionUtils.isEmpty(pageSizeList) ? "0" : pageSizeList.get(0) as String)
        Integer pageToken = Integer.parseInt(CollectionUtils.isEmpty(pageTokenList) ? "0" : pageTokenList.get(0) as String)
        //查询部门列表
        def (Boolean error, Map depts, String errorMessage) = Fx.org.findSubordinateDepartments(deptId, false)
        if (error) {
            Map returnBody = ["code": 500]
            String body = Fx.json.toJson(returnBody)
            log.info("query depts error:" + deptId + " errorMessage:" + errorMessage)
            return HttpResponse.ok().header("Content-Type", "application/json").body(body)
        }
        log.info("query depts finish size:" + depts.size() + " item:" + (depts.values().size() > 0 ? Fx.json.toJson(depts.values().getAt(0)) : null))
        List resources = convert2WpsDept(deptId, depts, pageSize, pageToken)
        Map lastOne = resources.size() < pageSize ? null : resources.get(resources.size() - 1) as Map
        String nextPageToken = lastOne == null ? "" : lastOne["dept_id"] as String
        log.info("nextPageToken:" + nextPageToken + " resources:" + resources.size())
        Map returnBody = ["code": 0, "resources": resources, "next_page_token": nextPageToken]
        String body = Fx.json.toJson(returnBody)
        return HttpResponse.ok()
                .header("Content-Type", "application/json")
                .header("Content-Length", body.getBytes().length as String)
                .body(body)
    }

    //查询人员
    @AplRequestMapping(value = "/users", method = RequestMethod.GET)
    public HttpResponse users(HttpRequest request) {
        Map parameters = request.getParameters()
        log.info("query users parameters:" + parameters)
        List pageSizeList = parameters["page_size"] as List
        List pageTokenList = parameters["page_token"] as List
        String deptId = (parameters["deptId"] as List).get(0)
        Integer pageSize = 1000 //Integer.parseInt(CollectionUtils.isEmpty(pageSizeList) ? "1000" : pageSizeList.get(0) as String)
        Integer pageToken = Integer.parseInt(CollectionUtils.isEmpty(pageTokenList) ? "0" : pageTokenList.get(0) as String)
        //查询部门列表
        def (Boolean error, List users, String errorMessage) = Fx.org.findEmployeeByDepartmentId(deptId)
        if (error) {
            Map returnBody = ["code": 500]
            String body = Fx.json.toJson(returnBody)
            log.info("query users error deptId:" + deptId + " errorMessage:" + errorMessage)
            return HttpResponse.ok().header("Content-Type", "application/json").body(body)
        }
        log.info("query users finish size:" + users.size() + " item:" + (users.size() > 0 ? Fx.json.toJson(users.get(0)) : null))
        Map openUserIdMap = userId2OpenUserId(userId2Int(users))
        List resources = convert2WpsUser(deptId, users, openUserIdMap, pageSize, pageToken)
        Map lastOne = resources.size() < pageSize ? null : resources.get(resources.size() - 1) as Map
        String nextPageToken = lastOne == null ? "" : lastOne["user_id"] as String
        log.info("nextPageToken:" + nextPageToken + " resources:" + resources.size())
        Map returnBody = ["code": 0, "resources": resources, "next_page_token": nextPageToken]
        String body = Fx.json.toJson(returnBody)
        return HttpResponse.ok()
                .header("Content-Type", "application/json")
                .header("Content-Length", body.getBytes().length as String)
                .body(body)
    }

    //userid转int
    private List<Integer> userId2Int(List<Map> users) {
        List employeeIds = []
        users.each { i ->
            employeeIds.add(Integer.parseInt(i["employee"] as String))
        }
        return employeeIds
    }

    //fsdept转wpsdept
    private List convert2WpsDept(String deptId, Map deptsMap, int pageSize, int pageToken) {
        deptsMap.remove(deptId)
        deptsMap.remove("999998")
        List allWpsDepts = []
        deptsMap.values().each { deptMap ->
            Map dept = [:]
            //NORMAL(1),STOP(2),DELETE(3)
            Integer status = deptMap["status"] as Integer
            String parentId = deptMap["superordinateDepartmentId"]
            dept["dept_id"] = deptMap["departmentId"]
            dept["dept_name"] = deptMap["name"]
            if (deptId.equals(parentId) && status == 1) {
                allWpsDepts.add(dept)
            }
        }
        //按照部门id排序
        allWpsDepts = allWpsDepts.sort { it -> return it["dept_id"] }
        //截取分页部门
        List subWpsDepts = []
        allWpsDepts.each { it ->
            String departmentId = it["dept_id"] as String
            if (Integer.parseInt(departmentId) > pageToken && subWpsDepts.size() < pageSize) {
                subWpsDepts.add(it)
            }
        }
        return subWpsDepts
    }

    //fsuser转wpsuser
    private List convert2WpsUser(String deptId, List<Map> users, Map<String, String> openUserIdMap, int pageSize, int pageToken) {
        List allWpsUsers = []
        users.each { i ->
            String employeeId = i["employee"]
            String mainDeptId = i["departmentId"] as String
            Integer status = i["status"] as Integer //NORMAL(1), STOP(2), DELETE(3)
            String position = i["position"] as String
            List deptIds = []
            if (mainDeptId != null) { deptIds.add(mainDeptId) }
            deptIds.addAll(i["ViceDepartmentIds"] as List)
            String guid = openUserIdMap[employeeId]
            if (guid != null && status == 1 && deptIds.contains(deptId) && position != null) {
                Map w = [:]
                w["dept_ids"] = deptIds
                w["guid"] = guid
                w["user_name"] = i["name"]
                w["user_mobile"] = i["mobile"]
                w["user_id"] = employeeId
                allWpsUsers.add(w)
            }
        }
        //按照人员id排序
        allWpsUsers = allWpsUsers.sort { it -> return it["user_id"] }
        //截取分页人员
        List subWpsUsers = []
        allWpsUsers.each { it ->
            String employeeId = it["user_id"] as String
            if (Integer.parseInt(employeeId) > pageToken && subWpsUsers.size() < pageSize) {
                subWpsUsers.add(it)
            }
        }
        return subWpsUsers
    }

    //user转openuserid
    private Map<String, String> userId2OpenUserId(List<Integer> employeeIds) {
        log.info("user2OpenUserId begin employeeIds:" + employeeIds.size())
        if (employeeIds == null || employeeIds.isEmpty()) {
            log.info("user2OpenUserId failed missing employeeIds:" + employeeIds)
            return [:]
        }
        Map header = ["x-peer-name": "customerFunction"]
        Map arg = ["appId": "FSAID_131e6e6", "employeeIds": employeeIds]
        def (Boolean error, HttpResult httpResult, String errorMessage) = Fx.proxy.callAPI("openapi.fsId2OpenUserId", null, header, arg)
        Map contentMap = httpResult.content as Map
        Integer errorCode = contentMap["errorCode"] as Integer
        String bizErrorMessage = contentMap["errorMessage"] as String
        String json = contentMap.data
        if (errorCode != 0) {
            log.info("user2OpenUserId errorMessage:" + errorMessage)
            log.info("user2OpenUserId httpResult:" + httpResult)
            Fx.message.throwException(bizErrorMessage)
        }
        Map result = Fx.json.parse(json)
        log.info("user2OpenUserId end data：" + result.size() + " item:" + (result.values().size() > 0 ? Fx.json.toJson(result.values().getAt(0)) : null))
        return result
    }

    //测试
    public static void main(String[] args) {
        log.info("开始")
        //Map parameters = Maps.newHashMap()
        //parameters.put("deptId", ["999999"])
        //HttpRequest httpRequest = new HttpRequest()
        //httpRequest.setParameters(parameters)
        //SyncOrg2Wps syncOrg2Wps = Fx.klass.newInstance('SyncOrg2Wps') as SyncOrg2Wps
        //HttpResponse resp = syncOrg2Wps.depts(httpRequest)
        //log.info("resp.getBody:" + resp.getBody())
        //Map ouIdMap = syncOrg2Wps.userId2OpenUserId([308, 1])
        //log.info("ouIdMap: " + ouIdMap["1"])
        //log.info("ouIdMap: " + ouIdMap["308"])
        //def (Boolean error, Map depts, String errorMessage) = Fx.org.findSubordinateDepartments("1201", false)
        //log.info("depts:" + Fx.json.toJson(depts))
        log.info("结束")
    }

}