package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.dto.ImageInfo;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/11/29 4:13 PM
 */
@Slf4j
public class ImageFieldDataConverter extends AbstractFieldDataConverter {
    @Override
    public String convertFieldData(SessionContext sessionContext) {
        String value = "";

        IObjectData objectData = getObjectData();
        Object o = objectData.get(getFieldDescribe().getApiName());
        if (!(o instanceof List)) {
            //兼容老对象部分图片字段数据结构为字符串
            if (o instanceof String) {
                return o.toString();
            }
            return value;
        } else if (CollectionUtils.empty((List) o)) {
            return value;
        }

        List<ImageInfo> images = ImageInfo.convert(o);
        String tenantId = Optional.ofNullable(sessionContext)
                .map(SessionContext::getEId)
                .map(Object::toString)
                .orElse(null);
        return images.stream()
                .map(it -> it.getFilePathForXml(tenantId))
                .collect(Collectors.joining("|"));
    }
}
