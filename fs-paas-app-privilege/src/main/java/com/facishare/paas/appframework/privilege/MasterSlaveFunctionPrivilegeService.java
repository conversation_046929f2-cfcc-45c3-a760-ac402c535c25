package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.common.util.ContextCacheUtil;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.privilege.dto.CheckFunctionPrivilege;
import com.facishare.paas.appframework.privilege.util.FunctionPrivillegeConfig;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.auth.common.exception.AuthException;
import com.facishare.paas.auth.model.AuthContext;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.fxiaoke.paas.auth.factory.FuncClient;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by shun on 2019/11/13
 */
@Service("masterSlaveFunctionPrivilegeService")
@Slf4j
public class MasterSlaveFunctionPrivilegeService {
    private static final Joiner FUNC_CODE_JOINER = Joiner.on(PrivilegeConstants.FUNC_CODE_SPLIT);

    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private FuncClient funcClient;

    public CheckFunctionPrivilege.Result checkFunctionPrivilege(AuthContext context, List<String> funcCodeList) {
        StopWatch stopWatch = StopWatch.create("MasterSlaveFunctionPrivilegeService");
        try {
            Map<String, Boolean> result = Maps.newHashMap();
            //尝试从context缓存取数据
            Map<String, Boolean> cacheResult = ContextCacheUtil.getFunctionPrivilegeCache(context.getUserId(), funcCodeList);
            if (CollectionUtils.notEmpty(cacheResult)) {
                result.putAll(cacheResult);
                funcCodeList = funcCodeList.stream().filter(x -> !cacheResult.containsKey(x)).collect(Collectors.toList());
            }
            stopWatch.lap("getFunctionPrivilegeCache");
            //缓存中没有的从client获取
            if (CollectionUtils.notEmpty(funcCodeList)) {
                Map<String, Boolean> permissionCheck = funcClient.userFuncPermissionCheck(context, Sets.newHashSet(funcCodeList));
                result.putAll(CollectionUtils.nullToEmpty(permissionCheck));
            }
            stopWatch.lap("userFuncPermissionCheck");
            stopWatch.logSlow(3000);
            return CheckFunctionPrivilege.Result.builder().success(true).result(result).build();
        } catch (AuthException e) {
            log.warn("userFuncPermissionCheck failed,context:{},funcCodeLists:{}", context, funcCodeList, e);
            stopWatch.logSlow(3000);
            return CheckFunctionPrivilege.Result.builder().success(false).errCode(e.getCode()).errMessage(e.getMessage()).build();
        }
    }
    public CheckFunctionPrivilege.Result checkFunctionDataPrivilege(AuthContext context, List<String> funcCodeList) {
        try {
            Map<String, Boolean> result = Maps.newHashMap();
            if (CollectionUtils.notEmpty(funcCodeList)) {
                Map<String, Boolean> dataPermissionCheck = funcClient.userFuncPermissionCheckWithoutAppId(context, Sets.newHashSet(funcCodeList));
                result.putAll(CollectionUtils.nullToEmpty(dataPermissionCheck));
            }
            return CheckFunctionPrivilege.Result.builder().success(true).result(result).build();
        } catch (AuthException e) {
            log.warn("checkFunctionDataPrivilege failed,context:{},funcCodeLists:{}", context, funcCodeList, e);
            return CheckFunctionPrivilege.Result.builder().success(false).errCode(e.getCode()).errMessage(e.getMessage()).build();
        }
    }


    @Deprecated
    public CheckFunctionPrivilege.Result checkFunctionPrivilege(CheckFunctionPrivilege.Arg funcCodePrivilegeArg) {
        AuthContext context = funcCodePrivilegeArg.getAuthContext().convert();
        return checkFunctionPrivilege(context, funcCodePrivilegeArg.getFuncCodeLists());
    }

    public Set<String> getSlaveHasNoFunctionCodes(long tenantId) {
        return getSlaveHasNoFunctionCodes(String.valueOf(tenantId));
    }

    private Set<String> getSlaveHasNoFunctionCodesWithoutCheck(String tenantId) {
        final User user = new User(tenantId, "-10000");
        List<String> list = null;
        try {
            list = objectDescribeService.findDetailDescribeApiNamesWithMasterCreated(tenantId, ActionContextExt.of(user).getContext());
        } catch (MetadataServiceException e) {
            log.error(String.format("findDetailDescribeApiNamesWithMasterCreated tenantId %s", tenantId), e);
        }
        List<String> apiNames = Objects.isNull(list) ? Lists.newArrayList() : list;

        Set<String> result = Sets.newHashSet();
        apiNames.stream().forEach(apiName -> result.addAll(convertObjectFunctionCodes(apiName)));
        return result;
    }

    public Set<String> getSlaveHasNoFunctionCodes(String tenantId) {
        boolean isInMasterDetailApprovalGrayList = AppFrameworkConfig.isInMasterDetailApprovalGrayList(tenantId);
        if (!isInMasterDetailApprovalGrayList) {
            return Sets.newHashSet();
        }
        return getSlaveHasNoFunctionCodesWithoutCheck(tenantId);
    }

    private List<String> convertObjectFunctionCodes(String apiName) {
        return FunctionPrivillegeConfig.getSlaveNoUseFunctionCodes().stream().map(functionCode -> FUNC_CODE_JOINER.join(apiName, functionCode)).collect(Collectors.toList());
    }
}
