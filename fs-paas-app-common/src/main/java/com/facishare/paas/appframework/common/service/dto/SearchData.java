package com.facishare.paas.appframework.common.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface SearchData {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        String tenantId;
        String userId;
        String keyword;
        Set<String> weightApiName;
        Set<String> searchApiNames;
        int size;
        String outTenantId;
        String outUserId;
        Boolean includeNameResults;
        Boolean includeApiIdsMap;
        Boolean accurateQuery;
        @Builder.Default
        Boolean optimizeBatchQuery = Boolean.FALSE;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        Integer totalSize;
        Map<String, List<String>> apiIdsMap;
    }

}
