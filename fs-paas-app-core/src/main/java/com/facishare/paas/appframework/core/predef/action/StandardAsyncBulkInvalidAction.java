package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.ObjectAction;

import java.util.List;
import java.util.stream.Collectors;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/10/12
 */
public class StandardAsyncBulkInvalidAction extends AbstractStandardAsyncBulkAction<StandardBulkInvalidAction.Arg, StandardInvalidAction.Arg> {

    @Override
    protected String getDataIdByParam(StandardInvalidAction.Arg param) {
        return param.getObjectDataId();
    }

    @Override
    protected List<StandardInvalidAction.Arg> getButtonParams() {
        return arg.toStandardInvalidActionArgList();
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.INVALID.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return StandardAction.Invalid.toString();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.Invalid.getFunPrivilegeCodes();
    }
}
