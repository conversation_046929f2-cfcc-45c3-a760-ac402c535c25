package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.TableColumnExt;
import com.facishare.paas.appframework.metadata.WhatComponentExt;
import com.facishare.paas.appframework.metadata.layout.component.BaseFlowTaskListComponentExt;
import com.facishare.paas.appframework.metadata.layout.component.FlowTaskListMobileComponentExt;
import com.facishare.paas.metadata.impl.ui.layout.component.WhatComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.google.common.collect.ImmutableList;
import de.lab4inf.math.util.Strings;
import lombok.Builder;

import java.util.List;
import java.util.Objects;

/**
 * Created by zhaooju on 2024/3/11
 */
public class FlowTaskListMobileComponentRender extends BaseFlowTaskListMobileComponentExtRender {
    private final FlowTaskListMobileComponentExt flowTaskListMobileComponentExt;
    private final List<String> supportFields;

    @Builder
    protected FlowTaskListMobileComponentRender(ObjectDescribeExt describeExt, ObjectDescribeExt whatDescribeExt, User user,
                                                FlowTaskListMobileComponentExt flowTaskListMobileComponentExt, ILayout objectLayout,
                                                List<String> supportFields, PageType pageType) {
        super(describeExt, whatDescribeExt, user, objectLayout, pageType);
        this.flowTaskListMobileComponentExt = flowTaskListMobileComponentExt;
        this.supportFields = Objects.nonNull(supportFields) ? ImmutableList.copyOf(supportFields) : null;
    }

    @Override
    protected void renderFieldList() {
        String mobileFieldType = flowTaskListMobileComponentExt.getMobileFieldType();
        if (!FlowTaskListMobileComponentExt.MOBILE_FIELD_TYPE_USER_DEFINE.equals(mobileFieldType)) {
            if (Strings.isNullOrEmpty(mobileFieldType)) {
                flowTaskListMobileComponentExt.setMobileFieldType(FlowTaskListMobileComponentExt.MOBILE_FIELD_TYPE_USER_LAYOUT);
            }
            return;
        }
        List<ITableColumn> includeFields = getIncludeFields();
        WhatComponentExt whatComponentExt = getWhatComponentExt(includeFields);
        WhatComponentRender.builder()
                .describeExt(describeExt)
                .whatDescribeExt(whatDescribeExt)
                .whatComponentExt(whatComponentExt)
                .build()
                .render();
        if (!isDesigner()) {
            List<String> supportFields = getSupportFields();
            includeFields.removeIf(it -> !supportFields.contains(it.getName()));
        }
        includeFields = whatComponentExt.getIncludeFields();
        flowTaskListMobileComponentExt.resetIncludeFields(includeFields);
    }

    @Override
    protected List<String> getSupportFields() {
        if (Objects.nonNull(supportFields)) {
            return supportFields;
        }
        return super.getSupportFields();
    }

    private WhatComponentExt getWhatComponentExt(List<ITableColumn> includeFields) {
        WhatComponent whatComponent = new WhatComponent();
        whatComponent.setIncludeFields(includeFields);
        return WhatComponentExt.of(whatComponent);
    }

    private List<ITableColumn> getIncludeFields() {
        List<ITableColumn> includeFields = flowTaskListMobileComponentExt.getIncludeFields();
        if (CollectionUtils.notEmpty(includeFields)) {
            return includeFields;
        }
        return TableColumnExt.buildWhatListDefaultColumns(describeExt, whatDescribeExt);
    }

    @Override
    protected BaseFlowTaskListComponentExt getFlowTaskListComponentExt() {
        return flowTaskListMobileComponentExt;
    }
}
