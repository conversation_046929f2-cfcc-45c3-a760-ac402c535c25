package com.facishare.paas.appframework.jaxrs.provider;

import com.facishare.paas.appframework.core.exception.AppBizException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.jaxrs.model.InnerAPIResult;
import com.facishare.paas.appframework.jaxrs.model.RestAPIResult;
import org.springframework.stereotype.Component;

import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.ext.Provider;


@Provider
@Component
public class AppBizExceptionMapper extends AbstractExceptionMapper<AppBizException> {

    @Override
    protected void logError(AppBizException exception, RequestContext context) {
        log.warn("request failed:{}-{},context:{}", exception.getErrorCode(), exception.getMessage(), context, exception);
    }

    @Override
    protected Response toCepResponse(AppBizException exception, RequestContext context) {
        Response.ResponseBuilder builder = Response.status(exception.getStatus())
                .header(ERROR_CODE_HEAD, exception.getCepErrorCode())
                .header(ERROR_MESSAGE_HEAD, exception.getMessage())
                .header(ERROR_PARAMS_HEAD, exception.getEncodeSupplement())
                .type(MediaType.TEXT_PLAIN);
        return builder.build();
    }

    @Override
    protected Response toInnerResponse(AppBizException exception, RequestContext context) {
        return Response.status(exception.getStatus())
                .entity(InnerAPIResult.fail(exception.getErrorCode(), exception.getMessage()))
                .build();
    }

    @Override
    protected Response toRestResponse(AppBizException exception, RequestContext context) {
        return Response.status(exception.getStatus())
                .entity(RestAPIResult.fail(exception.getErrorCode(), exception.getMessage()))
                .build();
    }


}
