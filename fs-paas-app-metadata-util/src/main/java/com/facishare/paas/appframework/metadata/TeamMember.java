package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 团队成员
 * <p>
 * Created by liyiguang on 2017/8/24.
 */
@Getter
public class TeamMember implements Comparable<TeamMember> {

    public static final String TEAM_MEMBER_EMPLOYEE_API_NAME = "teamMemberEmployee";
    public static final String TEAM_MEMBER_PERMISSION_TYPE_API_NAME = "teamMemberPermissionType";
    public static final String TEAM_MEMBER_ROLE_API_NAME = "teamMemberRole";
    public static final String TEAM_MEMBER_ROLE_LIST_API_NAME = "teamMemberRoleList";
    public static final String TEAM_MEMBER_TYPE = "teamMemberType";
    public static final String TEAM_MEMBER_EMPLOYEE_SOURCE_TYPE = "sourceType";
    public static final String TEAM_MEMBER_OUT_EMPLOYEE_EI = "outTenantId";
    public static final String TEAM_MEMBER_DEPT_CASCADE = "teamMemberDeptCascade";

    private List<String> employee = new ArrayList<>(1);
    private Permission permission;
    private Role role;
    private List<Role> roleList;
    private String roleCode;
    private List<String> roleCodeList;
    private String sourceType;
    private String outTenantId;
    private MemberType memberType;
    private String teamMemberDeptCascade;

    public Map<String, Object> toMap() {
        Map<String, Object> ret = Maps.newHashMap();
        ret.put(TEAM_MEMBER_EMPLOYEE_API_NAME, employee);
        ret.put(TEAM_MEMBER_PERMISSION_TYPE_API_NAME, permission.getValue());
        if (StringUtils.isNotEmpty(roleCode)) {
            ret.put(TEAM_MEMBER_ROLE_API_NAME, roleCode);
        } else if (CollectionUtils.notEmpty(roleCodeList)) {
            Set<String> values = Sets.newHashSet(roleCodeList);
            ret.put(TEAM_MEMBER_ROLE_API_NAME, values.toString());
        } else if (CollectionUtils.notEmpty(roleList)) {
            Set<String> values = roleList.stream().map(Role::getValue).collect(Collectors.toSet());
            ret.put(TEAM_MEMBER_ROLE_API_NAME, values.toString());
        } else {
            ret.put(TEAM_MEMBER_ROLE_API_NAME, role.getValue());
        }

        if (Strings.isNotEmpty(outTenantId)) {
            ret.put(TEAM_MEMBER_OUT_EMPLOYEE_EI, outTenantId);
            ret.put(TEAM_MEMBER_EMPLOYEE_SOURCE_TYPE, "2");
        }

        if (Strings.isEmpty(memberType.getValue())) {
            ret.put(TEAM_MEMBER_TYPE, MemberType.EMPLOYEE.getValue());
        } else {
            ret.put(TEAM_MEMBER_TYPE, memberType.getValue());
        }

        if (StringUtils.isBlank(teamMemberDeptCascade) || memberType != MemberType.DEPARTMENT) {
            ret.put(TEAM_MEMBER_DEPT_CASCADE, "0");
        } else {
            ret.put(TEAM_MEMBER_DEPT_CASCADE, teamMemberDeptCascade);
        }

        return ret;
    }

    public TeamMember(String userId, Role role, Permission permission) {
        init(userId, role, permission, null, null, null);
    }

    public TeamMember(String userId, Role role, Permission permission, MemberType memberType) {
        init(userId, role, permission, null, memberType, null);
    }

    public TeamMember(String userId, Role role, Permission permission, String outTenantId) {
        init(userId, role, permission, outTenantId, null, null);
    }

    public TeamMember(String userId, String roleType, Permission permission, String outTenantId) {
        init(userId, roleType, permission, outTenantId, null, null);
    }

    public TeamMember(String userId, Role role, Permission permission, String outTenantId, MemberType memberType) {
        init(userId, role, permission, outTenantId, memberType, null);
    }

    public TeamMember(String userId, Role role, Permission permission, String outTenantId, MemberType memberType, String teamMemberDeptCascade) {
        init(userId, role, permission, outTenantId, memberType, teamMemberDeptCascade);
    }

    public TeamMember(String userId, String roleType, Permission permission, String outTenantId, MemberType memberType, String teamMemberDeptCascade) {
        init(userId, roleType, permission, outTenantId, memberType, teamMemberDeptCascade);
    }

    public TeamMember(String userId, String roleType, Role role, Permission permission, String outTenantId, MemberType memberType, String teamMemberDeptCascade) {
        init(userId, roleType, role, permission, outTenantId, memberType, teamMemberDeptCascade);
    }

    public static boolean isTeamRoleGray(String tenantId) {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CUSTOM_TEAM_ROLE_GRAY_EI, tenantId);
    }

    public static boolean isTeamMemberTypeExportGray(String tenantId) {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CUSTOM_TEAM_TYPE_GRAY_EI, tenantId);
    }

    public boolean isOutMember() {
        return Objects.equals(sourceType, "2");
    }

    private void init(String userId, String roleType, Permission permission, String outTenantId, MemberType memberType, String teamMemberDeptCascade) {
        init(userId, roleType, null, permission, outTenantId, memberType, teamMemberDeptCascade);
    }

    private void init(String userId, Role role, Permission permission, String outTenantId, MemberType memberType, String teamMemberDeptCascade) {
        init(userId, role.getValue(), role, permission, outTenantId, memberType, teamMemberDeptCascade);
    }

    private void init(String userId, String roleType, Role role, Permission permission, String outTenantId, MemberType memberType, String teamMemberDeptCascade) {
        if (Objects.isNull(userId)) {
            throw new ValidateException("employee is null");
        }
        if (Objects.isNull(role) && StringUtils.isEmpty(roleType)) {
            throw new ValidateException("role is null");
        }
        if (Objects.isNull(permission)) {
            throw new ValidateException("permission is null");
        }
        employee.add(userId);
        this.role = role;
        if (Objects.nonNull(role)) {
            this.roleCode = role.getValue();
        } else {
            this.roleCode = roleType;
        }
        this.permission = permission;
        if (MemberType.OUT_TENANT_GROUP.equals(memberType)) {
            outTenantId = "-100000000";
        }
        this.outTenantId = outTenantId;
        if (Strings.isNotEmpty(outTenantId)) {
            sourceType = "2";
        }

        if (Objects.isNull(memberType)) {
            this.memberType = MemberType.EMPLOYEE;
        } else {
            this.memberType = memberType;
        }

        if (this.memberType != MemberType.DEPARTMENT || StringUtils.isBlank(teamMemberDeptCascade)) {
            this.teamMemberDeptCascade = "0";
        } else {
            this.teamMemberDeptCascade = teamMemberDeptCascade;
        }
    }

    public TeamMember(Map<String, Object> map) {
        this.employee = (List<String>) map.get(TEAM_MEMBER_EMPLOYEE_API_NAME);
        this.permission = Permission.of(map.get(TEAM_MEMBER_PERMISSION_TYPE_API_NAME).toString());
        String teamMemberRole = map.get(TEAM_MEMBER_ROLE_API_NAME).toString();
        this.role = Role.of(teamMemberRole);
        if (StringUtils.isNotEmpty(teamMemberRole) && teamMemberRole.contains("[")) {
            List<String> valueList = JSONObject.parseArray(teamMemberRole, String.class);
            this.roleList = valueList.stream().map(Role::of).collect(Collectors.toList());
            this.role = null;
        }
        this.roleCode = teamMemberRole;
        if (StringUtils.isNotEmpty(teamMemberRole) && teamMemberRole.contains("[")) {
            this.roleCodeList = JSONObject.parseArray(teamMemberRole, String.class);
            this.roleCode = null;
        }
        init(map);
    }

    private void init(Map<String, Object> map) {
        if (Objects.nonNull(map.get(TEAM_MEMBER_TYPE))) {
            this.memberType = MemberType.of(map.get(TEAM_MEMBER_TYPE).toString());
        } else {
            this.memberType = MemberType.EMPLOYEE;
        }
        if (Objects.nonNull(map.get(TEAM_MEMBER_OUT_EMPLOYEE_EI))) {
            this.outTenantId = String.valueOf(map.get(TEAM_MEMBER_OUT_EMPLOYEE_EI));
        }
        if (Objects.nonNull(map.get(TEAM_MEMBER_EMPLOYEE_SOURCE_TYPE))) {
            this.sourceType = String.valueOf(map.get(TEAM_MEMBER_EMPLOYEE_SOURCE_TYPE));
        }
        if (Objects.nonNull(map.get(TEAM_MEMBER_DEPT_CASCADE))) {
            this.teamMemberDeptCascade = String.valueOf(map.get(TEAM_MEMBER_DEPT_CASCADE));
        } else {
            this.teamMemberDeptCascade = "0";
        }
    }

    public String getEmployee() {
        return employee.get(0);
    }

    public List<String> getEmployeeList() {
        return employee;
    }


    public MemberType getMemberType() {
        if (Objects.isNull(memberType)) {
            return MemberType.EMPLOYEE;
        }
        return memberType;
    }

    public TeamMember merge(TeamMember teamMember) {
        if (!Objects.equals(this, teamMember)) {
            return null;
        }
        Role newRole = role;
        if (Role.OWNER == role || Role.OWNER == teamMember.getRole()) {
            newRole = Role.OWNER;
        }
        Permission newPermission = Stream.of(getPermission(), teamMember.getPermission())
                .max(Comparator.comparingInt(it -> Integer.parseInt(it.getValue())))
                .orElseGet(() -> this.permission);
        return new TeamMember(getEmployee(), teamMember.getRoleCode(), newRole, newPermission, outTenantId, memberType, teamMemberDeptCascade);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TeamMember that = (TeamMember) o;
        return Objects.equals(employee, that.employee) && Objects.equals(memberType, that.memberType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(employee, memberType);
    }

    @Override
    public int compareTo(@NotNull TeamMember o) {
        int ret = role.compareTo(o.role);
        if (ret == 0) {
            return permission.compareTo(o.permission);
        }
        return ret;
    }

    public void fillEmployeeFieldObject(Map data, Object object) {
        data.put(TEAM_MEMBER_EMPLOYEE_API_NAME + "__r", object);
    }

    public RoleWithPermission getRoleWithPermission() {
        return RoleWithPermission.getByRoleAndPermission(role, permission);
    }

    public enum Permission {

        READONLY("1", I18NKey.PERMISSION_READONLY, "只读"),// ignoreI18n
        READANDWRITE("2", I18NKey.PERMISSION_READANDWRITE, "读写"),// ignoreI18n
        NO_PERMISSION("0", I18NKey.NO_PERMISSION, "无权限");// ignoreI18n

        private String value;
        private String label;
        private String labelKey;

        Permission(String value, String labelKey, String label) {
            this.value = value;
            this.labelKey = labelKey;
            this.label = label;
        }

        public String getLabel() {
            return I18NExt.getOrDefault(labelKey, label);
        }

        public String getLabelKey() {
            return labelKey;
        }

        public String getValue() {
            return value;
        }

        public static Permission of(String value) {
            switch (value) {
                case "1":
                    return READONLY;
                case "2":
                    return READANDWRITE;
                default:
                    return NO_PERMISSION;
            }
        }

        public static Permission getMaxPermission(Set<Permission> permissions) {
            Permission max = NO_PERMISSION;
            for (Permission permission : permissions) {
                max = getMaxPermission(max, permission);
            }
            return max;
        }

        public static Permission getMaxPermission(Permission oldPermission, Permission newPermission) {
            if (Objects.equals(oldPermission, newPermission)) {
                return newPermission;
            }
            if (READANDWRITE.equals(oldPermission) || READANDWRITE.equals(newPermission)) {
                return READANDWRITE;
            }
            if (READONLY.equals(oldPermission) || READONLY.equals(newPermission)) {
                return READONLY;
            }
            return NO_PERMISSION;
        }
    }

    @Deprecated
    public static TeamMember createByRoleWithPermission(String userId, RoleWithPermission roleWithPermission) {
        return new TeamMember(userId, roleWithPermission.role, roleWithPermission.permission);
    }

    public static List<TeamMember> createByRoleWithPermissions(String userId, List<RoleWithPermission> roleWithPermissionList) {
        List<TeamMember> result = Lists.newArrayList();
        ListIterator<RoleWithPermission> lit = roleWithPermissionList.listIterator();
        Map<Role, Permission> rolePermissionMap = Maps.newHashMap();
        while (lit.hasNext()) {
            RoleWithPermission roleWithPermission = lit.next();
            if (rolePermissionMap.containsKey(roleWithPermission.getRole()) && roleWithPermission.getPermission() == Permission.READANDWRITE) {
                rolePermissionMap.put(roleWithPermission.getRole(), Permission.READANDWRITE);
            } else if (!rolePermissionMap.containsKey(roleWithPermission.getRole())) {
                rolePermissionMap.put(roleWithPermission.getRole(), roleWithPermission.getPermission());
            }
        }
        if (CollectionUtils.notEmpty(rolePermissionMap)) {
            rolePermissionMap.forEach((role, p) -> {
                result.add(new TeamMember(userId, role, p));
            });
        }
        return result;
    }

    public static List<TeamMember> createByRoleTypeWithPermissions(String userId, List<String> rolePermissionList) {
        List<TeamMember> result = Lists.newArrayList();
        ListIterator<String> lit = rolePermissionList.listIterator();
        Map<String, Permission> rolePermissionMap = Maps.newHashMap();
        while (lit.hasNext()) {
            String roleWithPermission = lit.next();
            String[] rolePermissionArray = roleWithPermission.split("_");
            if (rolePermissionMap.containsKey(rolePermissionArray[0]) && Permission.of(rolePermissionArray[1]) == Permission.READANDWRITE) {
                rolePermissionMap.put(rolePermissionArray[0], Permission.READANDWRITE);
            } else if (!rolePermissionMap.containsKey(rolePermissionArray[0])) {
                rolePermissionMap.put(rolePermissionArray[0], Permission.of(rolePermissionArray[1]));
            }
        }
        if (CollectionUtils.notEmpty(rolePermissionMap)) {
            rolePermissionMap.forEach((roleType, permission) ->
                    result.add(new TeamMember(userId, roleType, permission, null, null, null)));
        }
        return result;
    }

    public enum RoleWithPermission {
        FOLLOWER_READONLY(Role.FOLLOWER, I18NKey.FOLLOWER_TEAM_MEMBER_READONLY, Permission.READONLY),
        FOLLOWER_READWRITE(Role.FOLLOWER, I18NKey.FOLLOWER_TEAM_MEMBER_READWRITE, Permission.READANDWRITE),
        SERVICE_STAFF_READONLY(Role.SERVICE_STAFF, I18NKey.AFTERSALES_TEAM_MEMBER_READONLY, Permission.READONLY),
        SERVICE_STAFF_READWRITE(Role.SERVICE_STAFF, I18NKey.AFTERSALES_TEAM_MEMBER_READWRITE, Permission.READANDWRITE),
        NORMAL_STAFF_READONLY(Role.NORMAL_STAFF, I18NKey.NORMAL_TEAM_MEMBER_READONLY, Permission.READONLY),
        NORMAL_STAFF_READWRITE(Role.NORMAL_STAFF, I18NKey.NORMAL_TEAM_MEMBER_READWRITE, Permission.READANDWRITE),
        ;

        private Role role;
        private String labelKey;
        private Permission permission;

        private final static Map<String, RoleWithPermission> CACHE = new WeakHashMap<>();

        RoleWithPermission(Role r, String l, Permission p) {
            this.role = r;
            this.labelKey = l;
            this.permission = p;
        }

        public static RoleWithPermission getByRoleAndPermission(Role role, Permission permission) {
            for (RoleWithPermission rp : values()) {
                if (rp.role == role && rp.permission == permission) {
                    return rp;
                }
            }
            return null;
        }

        public static RoleWithPermission getByRoleAndPermission(String roleAndPermission) {
            for (RoleWithPermission rp : values()) {
                String rap = rp.getRole().getValue() + "_" + rp.getPermission().getValue();
                if (Objects.equals(roleAndPermission, rap)) {
                    return rp;
                }
            }
            return null;
        }

        public String getLabel() {
            return I18N.text(labelKey);
        }

        public Role getRole() {
            return role;
        }

        public Permission getPermission() {
            return permission;
        }

        public static boolean contains(String label) {
            if (Strings.isBlank(label)) {
                return false;
            }
            for (RoleWithPermission role : values()) {
                String l = I18N.text(role.labelKey);
                if (label.equals(l)) {
                    return true;
                }
            }
            return false;
        }


        public static RoleWithPermission getByLabel(String label) {
            //防止多线程多次创建对象
            synchronized (CACHE) {
                return CACHE.computeIfAbsent(label, RoleWithPermission::find);
            }
        }

        private static RoleWithPermission find(String label) {
            if (Strings.isBlank(label)) {
                return null;
            }
            for (RoleWithPermission role : values()) {
                if (label.equals(I18N.text(role.labelKey))) {
                    return role;
                }
            }
            return null;
        }
    }

    public enum OuterRole {
        OUTER_OWNER("1", I18NKey.OUTER_OWNER, "外部负责人"),// ignoreI18n
        OUTER_NORMAL_STAFF("4", I18NKey.OUTER_NORMAL_STAFF, "外部普通成员"),// ignoreI18n
        NOT_EXIST("0", I18NKey.constant_not_exist, "不在其中");// ignoreI18n

        private String value;
        private String labelKey;
        private String defaultLabel;

        OuterRole(String value, String labelKey, String defaultLabel) {
            this.value = value;
            this.labelKey = labelKey;
            this.defaultLabel = defaultLabel;
        }

        public String getValue() {
            return value;
        }

        public String getLabel() {
            return I18NExt.getOrDefault(labelKey, defaultLabel);
        }

        public String getI18NLabel() {
            return labelKey;
        }

        public static String getLabelByValue(String value) {
            switch (value) {
                case "1":
                    return I18N.text(OUTER_OWNER.labelKey);
                case "4":
                    return I18N.text(OUTER_NORMAL_STAFF.labelKey);
                default:
                    return I18N.text(NOT_EXIST.labelKey);
            }
        }

        public static String getI18NLabelByValue(String value) {
            for (OuterRole role : OuterRole.values()) {
                if (StringUtils.equals(value, role.value)) {
                    return role.getI18NLabel();
                }
            }
            return "";
        }

        public static OuterRole of(String value) {
            switch (value) {
                case "1":
                    return OUTER_OWNER;
                case "2":
                    return OUTER_NORMAL_STAFF;
                default:
                    return NOT_EXIST;
            }
        }

    }

    public enum Role {

        OWNER("1", I18NKey.constant_owner, "负责人"),// ignoreI18n
        FOLLOWER("2", I18NKey.constant_follower, "联合跟进人"),// ignoreI18n
        SERVICE_STAFF("3", I18NKey.constant_service_staff, "售后服务人员"),// ignoreI18n
        NORMAL_STAFF("4", I18NKey.constant_normal_staff, "普通成员"),// ignoreI18n
        NOT_EXIST("0", I18NKey.constant_not_exist, "不在其中"),// ignoreI18n
        ;

        private String value;
        private String labelKey;
        private String defaultLabel;

        Role(String value, String labelKey, String defaultLabel) {
            this.value = value;
            this.labelKey = labelKey;
            this.defaultLabel = defaultLabel;
        }

        public String getValue() {
            return value;
        }

        public String getLabel() {
            return I18NExt.getOrDefault(labelKey, defaultLabel);
        }

        public static Role of(String value) {
            switch (value) {
                case "1":
                    return OWNER;
                case "2":
                    return FOLLOWER;
                case "3":
                    return SERVICE_STAFF;
                case "4":
                    return NORMAL_STAFF;
                default:
                    return NOT_EXIST;
            }
        }
    }

    public enum MemberType {
        EMPLOYEE("0", I18NKey.MEMBER_TYPE_EMPLOYEE, "人员"),// ignoreI18n
        DEPARTMENT("2", I18NKey.MEMBER_TYPE_DEPARTMENT, "部门"),// ignoreI18n
        GROUP("1", I18NKey.MEMBER_TYPE_GROUP, "用户组"),// ignoreI18n
        ROLE("4", I18NKey.MEMBER_TYPE_ROLE, "角色"),// ignoreI18n
        OUT_TENANT("5", I18NKey.MEMBER_TYPE_OUT_TENANT, "下游企业"),// ignoreI18n
        OUT_TENANT_GROUP("6", I18NKey.MEMBER_TYPE_OUT_TENANT_GROUP, "下游企业组"),// ignoreI18n
        INTERCONNECT_DEPARTMENT("7", I18NKey.MEMBER_TYPE_INTERCONNECT_DEPARTMENT, "互联部门"),// ignoreI18n
        ;


        @Getter
        private String value;
        private String labelKey;
        private String defaultLabel;

        MemberType(String value, String labelKey, String defaultLabel) {
            this.value = value;
            this.labelKey = labelKey;
            this.defaultLabel = defaultLabel;
        }

        public String getLabel() {
            return I18NExt.getOrDefault(labelKey, defaultLabel);
        }

        public boolean isInnerMember() {
            return !(Objects.equals(value, OUT_TENANT.getValue())
                    || Objects.equals(value, OUT_TENANT_GROUP.getValue())
                    || Objects.equals(value, INTERCONNECT_DEPARTMENT.getValue()));
        }

        public static MemberType of(String value) {
            if (StringUtils.isBlank(value)) {
                return EMPLOYEE;
            }
            switch (value) {
                case "1":
                    return GROUP;
                case "2":
                    return DEPARTMENT;
                case "4":
                    return ROLE;
                case "5":
                    return OUT_TENANT;
                case "6":
                    return OUT_TENANT_GROUP;
                case "7":
                    return INTERCONNECT_DEPARTMENT;
                default:
                    return EMPLOYEE;
            }
        }
    }


    @Getter
    public enum DeptCascade {
        CASCADE("1", "级联"), NO_CASCADE("0", "不级联");// ignoreI18n

        private String value;
        private String label;

        DeptCascade(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public static DeptCascade of(String value) {
            switch (value) {
                case "1":
                    return CASCADE;
                case "0":
                    return NO_CASCADE;
                default:
                    return null;
            }
        }
    }
}
