package com.facishare.paas.appframework.core.predef.controller

import com.facishare.paas.appframework.core.model.*
import spock.lang.Specification

/**
 * Created by zhouwr on 2019/12/6
 */
class StandardDetailControllerTest extends Specification {

    StandardDetailController controller
    ServiceFacade serviceFacade

    def setup() {
        def requestContext = RequestContext.builder()
                .tenantId("55732")
                .user(new User("55732", "1000"))
                .build()
        RequestContextManager.setContext(requestContext)
        def controllerContext = new ControllerContext(requestContext, "object_7oz5u__c", "Detail")

        def arg = new AbstractStandardDetailController.Arg()
        arg.setObjectDataId("")
        arg.setObjectDescribeApiName(controllerContext.getObjectApiName())

        serviceFacade = Mock(ServiceFacade)
//        serviceFacade.isEnableQixinGroup(_, _) >> true

        controller = new StandardDetailController()
        controller.controllerContext = controllerContext
        controller.arg = arg
        controller.serviceFacade = serviceFacade
    }

    def "test defaultEnableQixinGroup"() {
        expect:
        1 == 1
        /*
        given:
        serviceFacade.isEnableQixinGroup(_, _) >> a
        expect:
        controller.defaultEnableQixinGroup() == b
        where:
        a     | b
        true  | true
        false | false
        */
    }

    def "test before"() {
        expect:
        1 == 1
        /*
        given:
        def describe = new ObjectDescribe()
        describe.setTenantId(controller.controllerContext.getTenantId())
        describe.setApiName(controller.controllerContext.getObjectApiName())

        def data = new ObjectData()
        data.setTenantId(controller.controllerContext.getTenantId())
        data.setDescribeApiName(controller.controllerContext.getObjectApiName())
        data.setId(controller.arg.getObjectDataId())
        when:
        controller.before(controller.arg)
        then:
        1 * serviceFacade.doFunPrivilegeCheck(_, _, _)
        1 * serviceFacade.findObjectIncludeMultiField(_, _) >> describe
        1 * serviceFacade.findObjectData(_, _, _) >> data
        1 * serviceFacade.doDataPrivilegeCheck(_, _, _, _)
        noExceptionThrown()
        */
    }

}
