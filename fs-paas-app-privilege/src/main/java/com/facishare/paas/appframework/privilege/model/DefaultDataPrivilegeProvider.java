package com.facishare.paas.appframework.privilege.model;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class DefaultDataPrivilegeProvider implements DataPrivilegeProvider {

    @Override
    public String getApiName() {
        return "";
    }

    @Override
    public Map<String,Map<String, Permissions>> checkBusinessPrivilege(User user, Map<String, Permissions> dataPrivilegeMap,
                                                           List<IObjectData> dataList,
                                                           List<String> actionCodes) {

        return null;
    }

    @Override
    public Map<String, Permissions> checkBusinessPrivilege(User user, Map<String, Permissions> dataPrivilegeMap,
                                                           List<IObjectData> dataList,
                                                           String actionCode) {
        return checkBusinessPrivilege(user,dataPrivilegeMap,dataList, Lists.newArrayList(actionCode)).get(actionCode);
    }

}
