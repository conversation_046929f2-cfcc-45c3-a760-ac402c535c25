package com.facishare.paas.appframework.metadata.handler;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.repository.model.HandlerDefinition;
import lombok.Getter;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by z<PERSON>wr on 2023/1/31.
 */
public enum HandlerType {

    BEFORE("before", 1, 999999),
    ACT("act", 1000000, 1999999),
    AFTER("after", 2000000, 2999999),
    FINALLY("finally", 3000000, 3999999);

    @Getter
    private final String code;
    private final int minOrder;
    private final int maxOrder;

    HandlerType(String code, int minOrder, int maxOrder) {
        this.code = code;
        this.minOrder = minOrder;
        this.maxOrder = maxOrder;
    }

    public static HandlerType getByCode(String code) {
        for (HandlerType handlerType : HandlerType.values()) {
            if (handlerType.getCode().equals(code)) {
                return handlerType;
            }
        }
        throw new ValidateException("handlerType not exist, handlerType: " + code);
    }

    public void validateOrder(int order, String handlerApiName) {
        if (order < minOrder || order > maxOrder) {
            throw new ValidateException("order exceed limit, minOrder: " + minOrder + ", maxOrder: " + maxOrder +
                    ", order: " + order + ", handlerType: " + code + ", handlerApiName: " + handlerApiName);
        }
    }

    public void calculateTenantHandlerOrder(List<HandlerInfo> handlerList) {
        if (CollectionUtils.empty(handlerList)) {
            return;
        }
        List<HandlerInfo> targetHandlerList = handlerList.stream().filter(x -> this.getCode().equals(x.getHandlerType()))
                .collect(Collectors.toList());
        for (int index = 0; index < targetHandlerList.size(); index++) {
            HandlerInfo handler = targetHandlerList.get(index);
            if (!HandlerDefinition.provideByTenant(handler.getProviderType())) {
                continue;
            }
            int prevOrder = index == 0 ? this.minOrder : targetHandlerList.get(index - 1).getExecuteOrder();
            HandlerInfo nextSystemHandler = null;
            for (int nextIndex = index + 1; nextIndex < targetHandlerList.size(); nextIndex++) {
                if (!HandlerDefinition.provideByTenant(targetHandlerList.get(nextIndex).getProviderType())) {
                    nextSystemHandler = targetHandlerList.get(nextIndex);
                    break;
                }
            }
            int nextOrder = Objects.nonNull(nextSystemHandler) ? nextSystemHandler.getExecuteOrder() : this.maxOrder;
            handler.setExecuteOrder((prevOrder + nextOrder) / 2);
        }
    }
}
