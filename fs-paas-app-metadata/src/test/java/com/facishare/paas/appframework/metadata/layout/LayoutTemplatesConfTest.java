package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.core.model.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;

@ExtendWith(MockitoExtension.class)
class LayoutTemplatesConfTest {

    @Mock
    private User user;

    @BeforeEach
    void setUp() {
        // 使用 lenient() 来避免 UnnecessaryStubbingException
        lenient().when(user.getTenantId()).thenReturn("74255");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取布局模板的正常场景
     */
    @Test
    @DisplayName("正常场景 - 获取布局模板")
    void testGetLayoutTemplates_NormalCase() {
        // Given
        String describeApiName = "test_object";
        String business = "abstract";
        Integer cardStyle = 1;

        // When
        List<Map<String, Object>> result = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, business, cardStyle);

        // Then
        assertNotNull(result);
        // 由于配置是动态加载的，这里只验证返回值不为null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取布局模板时传入null参数
     */
    @Test
    @DisplayName("边界场景 - 传入null参数")
    void testGetLayoutTemplates_WithNullParameters() {
        // Given
        String describeApiName = null;
        String business = null;
        Integer cardStyle = null;

        // When
        List<Map<String, Object>> result = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, business, cardStyle);

        // Then
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取布局模板时传入空字符串
     */
    @Test
    @DisplayName("边界场景 - 传入空字符串参数")
    void testGetLayoutTemplates_WithEmptyStringParameters() {
        // Given
        String describeApiName = "";
        String business = "";
        Integer cardStyle = 1;

        // When
        List<Map<String, Object>> result = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, business, cardStyle);

        // Then
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取布局模板时使用不同的卡片样式
     */
    @Test
    @DisplayName("正常场景 - 测试不同卡片样式")
    void testGetLayoutTemplates_DifferentCardStyles() {
        // Given
        String describeApiName = "test_object";
        String business = "abstract";

        // When & Then
        // 测试多列卡片样式
        List<Map<String, Object>> result1 = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, business, 1);
        assertNotNull(result1);

        // 测试全列卡片样式
        List<Map<String, Object>> result2 = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, business, 2);
        assertNotNull(result2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取布局模板时使用不同的业务场景
     */
    @Test
    @DisplayName("正常场景 - 测试不同业务场景")
    void testGetLayoutTemplates_DifferentBusinessScenarios() {
        // Given
        String describeApiName = "test_object";
        Integer cardStyle = 1;

        // When & Then
        // 测试抽象业务场景
        List<Map<String, Object>> result1 = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, "abstract", cardStyle);
        assertNotNull(result1);

        // 测试具体业务场景
        List<Map<String, Object>> result2 = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, "specific", cardStyle);
        assertNotNull(result2);

        // 测试空业务场景
        List<Map<String, Object>> result3 = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, "", cardStyle);
        assertNotNull(result3);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取布局模板时使用不同的对象API名称
     */
    @Test
    @DisplayName("正常场景 - 测试不同对象API名称")
    void testGetLayoutTemplates_DifferentDescribeApiNames() {
        // Given
        String business = "abstract";
        Integer cardStyle = 1;

        // When & Then
        // 测试具体对象API名称
        List<Map<String, Object>> result1 = LayoutTemplatesConf.getLayoutTemplates(user, "account", business, cardStyle);
        assertNotNull(result1);

        // 测试另一个对象API名称
        List<Map<String, Object>> result2 = LayoutTemplatesConf.getLayoutTemplates(user, "opportunity", business, cardStyle);
        assertNotNull(result2);

        // 测试自定义对象API名称
        List<Map<String, Object>> result3 = LayoutTemplatesConf.getLayoutTemplates(user, "custom_object__c", business, cardStyle);
        assertNotNull(result3);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取布局模板时使用不同的租户ID
     */
    @Test
    @DisplayName("正常场景 - 测试不同租户ID")
    void testGetLayoutTemplates_DifferentTenantIds() {
        // Given
        String describeApiName = "test_object";
        String business = "abstract";
        Integer cardStyle = 1;

        // When & Then
        // 测试租户ID 74255
        lenient().when(user.getTenantId()).thenReturn("74255");
        List<Map<String, Object>> result1 = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, business, cardStyle);
        assertNotNull(result1);

        // 测试租户ID 78057
        lenient().when(user.getTenantId()).thenReturn("78057");
        List<Map<String, Object>> result2 = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, business, cardStyle);
        assertNotNull(result2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取布局模板的基本功能验证
     */
    @Test
    @DisplayName("正常场景 - 基本功能验证")
    void testGetLayoutTemplates_BasicFunctionality() {
        // Given
        String describeApiName = "test_object";
        String business = "abstract";
        Integer cardStyle = 1;

        // When
        List<Map<String, Object>> result = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, business, cardStyle);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof List);
        // 验证返回的列表中的元素都是Map类型
        for (Object item : result) {
            assertTrue(item instanceof Map);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取布局模板时的异常处理
     */
    @Test
    @DisplayName("异常场景 - 用户为null时的处理")
    void testGetLayoutTemplates_NullUser() {
        // Given
        String describeApiName = "test_object";
        String business = "abstract";
        Integer cardStyle = 1;

        // When
        List<Map<String, Object>> result = LayoutTemplatesConf.getLayoutTemplates(null, describeApiName, business, cardStyle);

        // Then
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取布局模板时的边界值测试
     */
    @Test
    @DisplayName("边界场景 - 边界值测试")
    void testGetLayoutTemplates_BoundaryValues() {
        // Given
        String describeApiName = "test_object";
        String business = "abstract";

        // When & Then
        // 测试卡片样式为0
        List<Map<String, Object>> result1 = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, business, 0);
        assertNotNull(result1);

        // 测试卡片样式为负数
        List<Map<String, Object>> result2 = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, business, -1);
        assertNotNull(result2);

        // 测试卡片样式为很大的数
        List<Map<String, Object>> result3 = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, business, 999);
        assertNotNull(result3);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取布局模板时的性能验证
     */
    @Test
    @DisplayName("正常场景 - 性能验证")
    void testGetLayoutTemplates_Performance() {
        // Given
        String describeApiName = "test_object";
        String business = "abstract";
        Integer cardStyle = 1;

        // When
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < 100; i++) {
            LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, business, cardStyle);
        }
        long endTime = System.currentTimeMillis();

        // Then
        long duration = endTime - startTime;
        // 验证100次调用在合理时间内完成（这里设置为5秒）
        assertTrue(duration < 5000, "100次调用耗时过长: " + duration + "ms");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取布局模板时的一致性验证
     */
    @Test
    @DisplayName("正常场景 - 一致性验证")
    void testGetLayoutTemplates_Consistency() {
        // Given
        String describeApiName = "test_object";
        String business = "abstract";
        Integer cardStyle = 1;

        // When
        List<Map<String, Object>> result1 = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, business, cardStyle);
        List<Map<String, Object>> result2 = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, business, cardStyle);

        // Then
        assertNotNull(result1);
        assertNotNull(result2);
        assertEquals(result1.size(), result2.size());
        // 验证多次调用返回相同的结果
        for (int i = 0; i < result1.size(); i++) {
            assertEquals(result1.get(i), result2.get(i));
        }
    }



    /**
     * GenerateByAI
     * 测试内容描述：测试不同的business值
     */
    @Test
    @DisplayName("测试不同的business值")
    void testGetLayoutTemplates_DifferentBusiness() {
        // Given
        String describeApiName = "TestObj";
        Integer cardStyle = 1;

        // When & Then
        // 测试 business = null
        List<Map<String, Object>> result1 = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, null, cardStyle);
        assertNotNull(result1);

        // 测试 business = "abstract"
        List<Map<String, Object>> result2 = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, "abstract", cardStyle);
        assertNotNull(result2);

        // 测试 business = "custom"
        List<Map<String, Object>> result3 = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, "custom", cardStyle);
        assertNotNull(result3);

        // 测试空字符串
        List<Map<String, Object>> result4 = LayoutTemplatesConf.getLayoutTemplates(user, describeApiName, "", cardStyle);
        assertNotNull(result4);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空的describeApiName
     */
    @Test
    @DisplayName("测试空的describeApiName")
    void testGetLayoutTemplates_EmptyDescribeApiName() {
        // Given
        String business = "test";
        Integer cardStyle = 1;

        // When & Then
        // 测试 describeApiName = null
        List<Map<String, Object>> result1 = LayoutTemplatesConf.getLayoutTemplates(user, null, business, cardStyle);
        assertNotNull(result1);

        // 测试 describeApiName = ""
        List<Map<String, Object>> result2 = LayoutTemplatesConf.getLayoutTemplates(user, "", business, cardStyle);
        assertNotNull(result2);

        // 测试 describeApiName = "   " (空白字符)
        List<Map<String, Object>> result3 = LayoutTemplatesConf.getLayoutTemplates(user, "   ", business, cardStyle);
        assertNotNull(result3);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试CardStyle枚举
     */
    @Test
    @DisplayName("测试CardStyle枚举")
    void testCardStyleEnum() {
        // When & Then
        // 验证枚举值存在
        assertNotNull(LayoutTemplatesConf.CardStyle.MULTI_COLUMN);
        assertNotNull(LayoutTemplatesConf.CardStyle.FULL_COlUMN);

        // 验证枚举值数量
        assertEquals(2, LayoutTemplatesConf.CardStyle.values().length);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试LayoutTemplatesConfItem的构造函数和方法（通过反射）
     */
    @Test
    @DisplayName("测试LayoutTemplatesConfItem - 通过反射")
    void testLayoutTemplatesConfItem_ViaReflection() throws Exception {
        // Given
        ObjectMapper mapper = new ObjectMapper();
        JsonNode layoutTemplate = mapper.createObjectNode()
                .put("templateName", "test_template")
                .put("card_style", 1)
                .put("gray_ei", "74255");

        // 添加 unUseScene 数组
        ArrayNode unUseScene = mapper.createArrayNode();
        unUseScene.add("test_business");
        ((ObjectNode) layoutTemplate).set("unUseScene", unUseScene);

        // When - 通过反射创建 LayoutTemplatesConfItem
        Class<?> itemClass = Class.forName("com.facishare.paas.appframework.metadata.layout.LayoutTemplatesConf$LayoutTemplatesConfItem");
        Constructor<?> constructor = itemClass.getDeclaredConstructor(JsonNode.class);
        constructor.setAccessible(true);
        Object item = constructor.newInstance(layoutTemplate);

        // Then - 测试各种方法
        Method isAllowMethod = itemClass.getDeclaredMethod("isAllow", String.class);
        isAllowMethod.setAccessible(true);
        Boolean isAllow = (Boolean) isAllowMethod.invoke(item, "74255");
        assertTrue(isAllow);

        Method isAllowBusinessMethod = itemClass.getDeclaredMethod("isAllowBusiness", String.class);
        isAllowBusinessMethod.setAccessible(true);
        Boolean isAllowBusiness = (Boolean) isAllowBusinessMethod.invoke(item, "other_business");
        assertTrue(isAllowBusiness);

        Method isAllowCardStyleMethod = itemClass.getDeclaredMethod("isAllowCardStyle", Integer.class);
        isAllowCardStyleMethod.setAccessible(true);
        Boolean isAllowCardStyle = (Boolean) isAllowCardStyleMethod.invoke(item, 1);
        assertTrue(isAllowCardStyle);

        Method getTemplateMethod = itemClass.getDeclaredMethod("getTemplate");
        getTemplateMethod.setAccessible(true);
        Map<String, Object> template = (Map<String, Object>) getTemplateMethod.invoke(item);
        assertNotNull(template);
        assertEquals("test_template", template.get("templateName"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试LayoutTemplatesConfItem - 无灰度规则
     */
    @Test
    @DisplayName("测试LayoutTemplatesConfItem - 无灰度规则")
    void testLayoutTemplatesConfItem_NoGrayRule() throws Exception {
        // Given
        ObjectMapper mapper = new ObjectMapper();
        JsonNode layoutTemplate = mapper.createObjectNode()
                .put("templateName", "test_template_no_gray");

        // When - 通过反射创建 LayoutTemplatesConfItem
        Class<?> itemClass = Class.forName("com.facishare.paas.appframework.metadata.layout.LayoutTemplatesConf$LayoutTemplatesConfItem");
        Constructor<?> constructor = itemClass.getDeclaredConstructor(JsonNode.class);
        constructor.setAccessible(true);
        Object item = constructor.newInstance(layoutTemplate);

        // Then - 测试 isAllow 方法（无灰度规则应该返回true）
        Method isAllowMethod = itemClass.getDeclaredMethod("isAllow", String.class);
        isAllowMethod.setAccessible(true);
        Boolean isAllow = (Boolean) isAllowMethod.invoke(item, "any_tenant");
        assertTrue(isAllow);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试LayoutTemplatesConfItem - 无unUseScene
     */
    @Test
    @DisplayName("测试LayoutTemplatesConfItem - 无unUseScene")
    void testLayoutTemplatesConfItem_NoUnUseScene() throws Exception {
        // Given
        ObjectMapper mapper = new ObjectMapper();
        JsonNode layoutTemplate = mapper.createObjectNode()
                .put("templateName", "test_template_no_scene");

        // When - 通过反射创建 LayoutTemplatesConfItem
        Class<?> itemClass = Class.forName("com.facishare.paas.appframework.metadata.layout.LayoutTemplatesConf$LayoutTemplatesConfItem");
        Constructor<?> constructor = itemClass.getDeclaredConstructor(JsonNode.class);
        constructor.setAccessible(true);
        Object item = constructor.newInstance(layoutTemplate);

        // Then - 测试 isAllowBusiness 方法
        Method isAllowBusinessMethod = itemClass.getDeclaredMethod("isAllowBusiness", String.class);
        isAllowBusinessMethod.setAccessible(true);
        Boolean isAllowBusiness = (Boolean) isAllowBusinessMethod.invoke(item, "any_business");
        assertTrue(isAllowBusiness);
    }
}
