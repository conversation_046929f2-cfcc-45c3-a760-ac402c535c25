package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public class StandardUpdateGdprAction extends AbstractStandardAction<StandardUpdateGdprAction.Arg, StandardUpdateGdprAction.Result> {

    @Override
    protected Result doAct(Arg arg) {
        if (CollectionUtils.empty(dataList)) {
            return Result.builder().build();
        }
        for (IObjectData data : dataList) {
            try {
                infraServiceFacade.updateGdprLegalBase(actionContext.getUser(), arg.getApiName(), data.getId(), arg.getLegalBase());
            } catch (Exception e) {
                log.warn("update gdpr error!", e);
                throw new MetaDataBusinessException(I18N.text(I18NKey.GDPR_LEGAL_BASE_UPDATE_FAILED), e);
            }
        }
        return Result.builder().build();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        serviceFacade.sendActionMq(actionContext.getUser(), dataList, ObjectAction.UPDATE_GDPR);
        return super.after(arg, result);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return arg.getDataIds();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg {
        private String apiName;
        private List<String> dataIds;
        private String legalBase;
        private ObjectDataDocument args;
        private String buttonApiName;

        public static StandardUpdateGdprAction.Arg of(String apiName, String dataId, String legalBase) {
            return StandardUpdateGdprAction.Arg.builder()
                    .apiName(apiName)
                    .dataIds(Lists.newArrayList(dataId))
                    .legalBase(legalBase).build();
        }

        public static StandardUpdateGdprAction.Arg ofArg(String apiName, String dataId, ObjectDataDocument args) {
            return Arg.builder()
                    .apiName(apiName)
                    .dataIds(Lists.newArrayList(dataId))
                    .legalBase(args.toObjectData().get("form_legal_base", String.class))
                    .build();
        }
    }


    @Data
    @Builder
    @NoArgsConstructor
    public static class Result {
    }
}
