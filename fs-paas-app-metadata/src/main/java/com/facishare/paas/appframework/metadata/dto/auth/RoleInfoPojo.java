package com.facishare.paas.appframework.metadata.dto.auth;

import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.auth.common.constant.AuthConstant.RoleType;
import lombok.Data;

import java.util.Objects;

@Data
public class RoleInfoPojo {
    private String roleCode;
    private String roleName;
    private int roleType;
    private String appId;
    private String tenantId;
    private String description;
    private Boolean delFlag;

    public static boolean isCrmRole(RoleInfoPojo roleInfo) {
        Objects.requireNonNull(roleInfo);
        int roleType = roleInfo.getRoleType();
        return RoleType.DEFAULT == roleType || RoleType.CUSTOMIZED == roleType;
    }

    public static boolean isOuterRole(RoleInfoPojo roleInfo) {
        Objects.requireNonNull(roleInfo);
        int roleType = roleInfo.getRoleType();
        return RoleType.OUTER_DEFAULT == roleType
                || RoleType.OUTER_CUSTOMIZED == roleType
                || RoleType.OUTER_BASE_ROLE == roleType;
    }

    public JSONObject toJSONObject() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("roleCode", roleCode);
        jsonObject.put("roleName", roleName);
        jsonObject.put("roleType", roleType);
        jsonObject.put("appId", appId);
        jsonObject.put("tenantId", tenantId);
        jsonObject.put("description", description);
        jsonObject.put("delFlag", delFlag);
        return jsonObject;
    }
}
