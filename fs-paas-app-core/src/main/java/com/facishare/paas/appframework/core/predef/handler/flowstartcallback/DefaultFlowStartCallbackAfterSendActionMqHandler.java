package com.facishare.paas.appframework.core.predef.handler.flowstartcallback;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2023/10/12.
 */
@Component
@HandlerProvider(name = "defaultFlowStartCallbackAfterSendActionMqHandler")
public class DefaultFlowStartCallbackAfterSendActionMqHandler implements FlowStartCallbackHandler {

    @Autowired
    private ServiceFacade serviceFacade;

    @Override
    public Result handle(HandlerContext context, Arg arg) {
        ApprovalFlowTriggerType triggerType = arg.getInterfaceArg().triggerType();
        switch (triggerType) {
            case UPDATE:
                sendEditActionMq(context, arg);
                break;
            case INVALID:
                sendInvalidActionMq(context, arg);
                break;
            default:
                break;
        }
        return new Result();
    }

    private void sendInvalidActionMq(HandlerContext context, Arg arg) {
        //先拷贝一下数据，防止并发修改报错
        IObjectData cpData = ObjectDataExt.of(arg.data()).copy();
        Map<String, List<IObjectData>> cpDetailDataMap = ObjectDataExt.copyMap(arg.detailDataMap());

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> {
            serviceFacade.sendActionMq(context.getUser(), Lists.newArrayList(cpData), ObjectAction.INVALID);
            cpDetailDataMap.forEach((k, v) -> serviceFacade.sendActionMq(context.getUser(), v, ObjectAction.INVALID));
        });
        parallelTask.run();
    }

    private void sendEditActionMq(HandlerContext context, Arg arg) {
        //先拷贝一下数据，防止并发修改报错
        List<IObjectData> cpData = Lists.newArrayList(ObjectDataExt.of(arg.data()).copy());
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> {
            serviceFacade.sendActionMq(context.getUser(), cpData, ObjectAction.UPDATE);
        });
        parallelTask.run();
    }
}
