package com.facishare.paas.appframework.log.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ObjectInfo {
    private String objectApiName;
    private String objectLabel;
    private List<ObjectData> objectDatas;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ObjectData {
        private String dataName;
        private String dataId;
        private String masterId;
        private String logID;

    }
}
