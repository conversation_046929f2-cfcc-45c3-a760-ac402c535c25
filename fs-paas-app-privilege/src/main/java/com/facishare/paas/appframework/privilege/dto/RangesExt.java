package com.facishare.paas.appframework.privilege.dto;

import com.facishare.paas.metadata.api.search.Ranges;
import lombok.*;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RangesExt extends Ranges {

  private List<String> shareRuleOrgIds;
  private List<String> outTenantIds;
  private List<String> outTenantGroups;
  private List<RangeOutUser> outUsersList;
  private List<String> targetOutRoleIds;

}
