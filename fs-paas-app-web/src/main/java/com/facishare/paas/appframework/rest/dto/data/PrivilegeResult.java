package com.facishare.paas.appframework.rest.dto.data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created by luxin on 2019/4/17.
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PrivilegeResult<T> implements Serializable {
    public static final String SUCCESS = "Success";

    public static final Integer SUCCESS_CODE = 0;

    private String msg;
    private Integer code;
    private Boolean success;
    private T result;

    public static <T> PrivilegeResult ofSuccess(T result) {
        return new PrivilegeResult(SUCCESS, SUCCESS_CODE, true, result);
    }

    public static PrivilegeResult ofFail(Integer errorCode, String msg) {
        PrivilegeResult result = new PrivilegeResult();
        result.setCode(errorCode);
        result.setMsg(msg);
        result.setSuccess(false);
        return result;
    }

}
