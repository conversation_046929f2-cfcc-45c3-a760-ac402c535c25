package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.FindObjectPageComponentList;
import com.facishare.paas.appframework.common.util.WebPageConfig;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/7/26
 */
@Slf4j
@Service
public class WebPageProxyService {
    private final WebPageProxy webPageProxy;

    private static final Cache<String, List<FindObjectPageComponentList.ObjectPageComponent>> cache = Caffeine.newBuilder()
            .maximumSize(10)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    public WebPageProxyService(WebPageProxy webPageProxy) {
        this.webPageProxy = webPageProxy;
    }


    public List<FindObjectPageComponentList.ObjectPageComponent> findCustomerWidget() {
        return WebPageConfig.findAllObjectPageComponentList();
    }

    public List<FindObjectPageComponentList.ObjectPageComponent> findCustomerWidget(User user, String bizId, String templeType) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FIND_CUSTOMER_WIDGET_LOCAL_CONFIG_GRAY, user.getTenantId())) {
            return WebPageConfig.findObjectPageComponentList(bizId, templeType);
        }
        FindObjectPageComponentList.Arg arg = new FindObjectPageComponentList.Arg();
        arg.setBizId(bizId);
        arg.setTempleType(templeType);
        try {
            return cache.get(arg.toString(), key -> findObjectPageComponentList(user, arg));
        } catch (Exception e) {
            log.warn("findCustomerWidget fail! ei:{}, bizId:{}, templeType:{}", user.getTenantId(), bizId, templeType, e);
        }
        return Collections.emptyList();
    }

    private List<FindObjectPageComponentList.ObjectPageComponent> findObjectPageComponentList(User user, FindObjectPageComponentList.Arg arg) {
        Map<String, String> headers = RestUtils.buildHeaders(user);
        FindObjectPageComponentList.Result result = webPageProxy.findObjectPageComponentList(headers, arg);
        return result.getObjectPageComponents();
    }
}
