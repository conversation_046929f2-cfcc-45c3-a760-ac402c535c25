package com.facishare.paas.appframework.metadata.exception;

import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;

/**
 * Created by liyiguang on 2017/8/25.
 */
public class MetaDataException extends APPException {
    public MetaDataException(String message, Throwable cause) {
        super(message, cause);
    }

    public MetaDataException(String message) {
        super(message);
    }

    public MetaDataException(SystemErrorCode errorCode, Throwable cause) {
        super(errorCode, cause);
    }

    public MetaDataException(SystemErrorCode errorCode) {
        super(errorCode);
    }
}
