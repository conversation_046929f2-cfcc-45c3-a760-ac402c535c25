package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.impl.ui.layout.TableColumn;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.google.common.collect.Lists;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * FlowTaskListMobileComponentExt单元测试类
 */
@ExtendWith(MockitoExtension.class)
class FlowTaskListMobileComponentExtTest {

  @Mock
  private IComponent mockComponent;

  private FlowTaskListMobileComponentExt flowTaskListMobileComponentExt;

  @BeforeEach
  void setUp() {
    flowTaskListMobileComponentExt = FlowTaskListMobileComponentExt.of(mockComponent);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试of方法创建FlowTaskListMobileComponentExt对象
   */
  @Test
  @DisplayName("测试of方法创建对象")
  void testOfMethod() {
    IComponent component = mock(IComponent.class);
    
    FlowTaskListMobileComponentExt result = FlowTaskListMobileComponentExt.of(component);
    
    assertNotNull(result);
    assertEquals(component, result.getComponent());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getMobileFieldType和setMobileFieldType方法
   */
  @Test
  @DisplayName("测试mobileFieldType的getter和setter")
  void testMobileFieldTypeGetterAndSetter() {
    String testMobileFieldType = "testType";
    
    when(mockComponent.get(FlowTaskListMobileComponentExt.MOBILE_FIELD_TYPE, String.class))
        .thenReturn(testMobileFieldType);
    
    String result = flowTaskListMobileComponentExt.getMobileFieldType();
    
    assertEquals(testMobileFieldType, result);
    
    // 测试setter
    flowTaskListMobileComponentExt.setMobileFieldType(testMobileFieldType);
    verify(mockComponent).set(FlowTaskListMobileComponentExt.MOBILE_FIELD_TYPE, testMobileFieldType);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试mobileFieldTypeIsUserDefine方法当类型为USER_DEFINE时返回true
   */
  @Test
  @DisplayName("测试mobileFieldTypeIsUserDefine方法 - 类型为USER_DEFINE时返回true")
  void testMobileFieldTypeIsUserDefineWhenUserDefine() {
    when(mockComponent.get(FlowTaskListMobileComponentExt.MOBILE_FIELD_TYPE, String.class))
        .thenReturn(FlowTaskListMobileComponentExt.MOBILE_FIELD_TYPE_USER_DEFINE);
    
    boolean result = flowTaskListMobileComponentExt.mobileFieldTypeIsUserDefine();
    
    assertTrue(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试mobileFieldTypeIsUserDefine方法当类型不为USER_DEFINE时返回false
   */
  @Test
  @DisplayName("测试mobileFieldTypeIsUserDefine方法 - 类型不为USER_DEFINE时返回false")
  void testMobileFieldTypeIsUserDefineWhenNotUserDefine() {
    when(mockComponent.get(FlowTaskListMobileComponentExt.MOBILE_FIELD_TYPE, String.class))
        .thenReturn(FlowTaskListMobileComponentExt.MOBILE_FIELD_TYPE_USER_LAYOUT);
    
    boolean result = flowTaskListMobileComponentExt.mobileFieldTypeIsUserDefine();
    
    assertFalse(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试mobileFieldTypeIsUserDefine方法当类型为null时返回false
   */
  @Test
  @DisplayName("测试mobileFieldTypeIsUserDefine方法 - 类型为null时返回false")
  void testMobileFieldTypeIsUserDefineWhenNull() {
    when(mockComponent.get(FlowTaskListMobileComponentExt.MOBILE_FIELD_TYPE, String.class))
        .thenReturn(null);
    
    boolean result = flowTaskListMobileComponentExt.mobileFieldTypeIsUserDefine();
    
    assertFalse(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIncludeFields方法当includeFields为空时返回空列表
   */
  @Test
  @DisplayName("测试getIncludeFields方法 - includeFields为空时返回空列表")
  void testGetIncludeFieldsWhenEmpty() {
    try (MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class)) {
      when(mockComponent.get(FlowTaskListMobileComponentExt.INCLUDE_FIELDS, List.class))
          .thenReturn(null);
      mockedCollectionUtils.when(() -> CollectionUtils.nullToEmpty(any(List.class)))
          .thenReturn(Lists.newArrayList());
      
      List<ITableColumn> result = flowTaskListMobileComponentExt.getIncludeFields();
      
      assertNotNull(result);
      assertTrue(result.isEmpty());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIncludeFields方法当includeFields不为空时返回TableColumn列表
   */
  @Test
  @DisplayName("测试getIncludeFields方法 - includeFields不为空时返回TableColumn列表")
  void testGetIncludeFieldsWhenNotEmpty() {
    try (MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class)) {
      // 准备测试数据
      Map<String, Object> columnMap1 = new HashMap<>();
      columnMap1.put("api_name", "field1");
      columnMap1.put("label", "Field 1");
      
      Map<String, Object> columnMap2 = new HashMap<>();
      columnMap2.put("api_name", "field2");
      columnMap2.put("label", "Field 2");
      
      List<Map> columnsList = Lists.newArrayList(columnMap1, columnMap2);
      
      when(mockComponent.get(FlowTaskListMobileComponentExt.INCLUDE_FIELDS, List.class))
          .thenReturn(columnsList);
      mockedCollectionUtils.when(() -> CollectionUtils.nullToEmpty(columnsList))
          .thenReturn(columnsList);
      
      // 执行测试
      List<ITableColumn> result = flowTaskListMobileComponentExt.getIncludeFields();
      
      // 验证结果
      assertNotNull(result);
      assertEquals(2, result.size());
      assertTrue(result.get(0) instanceof TableColumn);
      assertTrue(result.get(1) instanceof TableColumn);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试resetIncludeFields方法当includeFields为null时设置空列表
   */
  @Test
  @DisplayName("测试resetIncludeFields方法 - includeFields为null时设置空列表")
  void testResetIncludeFieldsWhenNull() {
    try (MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class)) {
      mockedCollectionUtils.when(() -> CollectionUtils.nullToEmpty(any(List.class)))
          .thenReturn(Lists.newArrayList());
      
      flowTaskListMobileComponentExt.resetIncludeFields(null);
      
      verify(mockComponent).set(FlowTaskListMobileComponentExt.INCLUDE_FIELDS, Lists.newArrayList());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试resetIncludeFields方法当includeFields不为空时正常设置
   */
  @Test
  @DisplayName("测试resetIncludeFields方法 - includeFields不为空时正常设置")
  void testResetIncludeFieldsWhenNotEmpty() {
    try (MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class);
         MockedStatic<Document> mockedDocument = mockStatic(Document.class)) {
      
      // 准备测试数据
      ITableColumn column1 = mock(ITableColumn.class);
      ITableColumn column2 = mock(ITableColumn.class);
      
      when(column1.toJsonString()).thenReturn("{\"api_name\":\"field1\"}");
      when(column2.toJsonString()).thenReturn("{\"api_name\":\"field2\"}");
      
      Document doc1 = mock(Document.class);
      Document doc2 = mock(Document.class);
      
      mockedDocument.when(() -> Document.parse("{\"api_name\":\"field1\"}")).thenReturn(doc1);
      mockedDocument.when(() -> Document.parse("{\"api_name\":\"field2\"}")).thenReturn(doc2);
      
      List<ITableColumn> includeFields = Lists.newArrayList(column1, column2);
      mockedCollectionUtils.when(() -> CollectionUtils.nullToEmpty(includeFields))
          .thenReturn(includeFields);
      
      // Mock get方法返回List
      List<Document> mockList = new ArrayList<>();
      when(mockComponent.get(FlowTaskListMobileComponentExt.INCLUDE_FIELDS)).thenReturn(mockList);
      
      // 执行测试
      flowTaskListMobileComponentExt.resetIncludeFields(includeFields);
      
      // 验证set方法被调用
      verify(mockComponent).set(FlowTaskListMobileComponentExt.INCLUDE_FIELDS, Lists.newArrayList());
      
      // 验证Document.parse被调用
      mockedDocument.verify(() -> Document.parse("{\"api_name\":\"field1\"}"));
      mockedDocument.verify(() -> Document.parse("{\"api_name\":\"field2\"}"));
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试resetIncludeFields方法当includeFields为空列表时正常处理
   */
  @Test
  @DisplayName("测试resetIncludeFields方法 - includeFields为空列表时正常处理")
  void testResetIncludeFieldsWhenEmptyList() {
    try (MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class)) {
      List<ITableColumn> emptyList = Lists.newArrayList();
      mockedCollectionUtils.when(() -> CollectionUtils.nullToEmpty(emptyList))
          .thenReturn(emptyList);
      
      flowTaskListMobileComponentExt.resetIncludeFields(emptyList);
      
      verify(mockComponent).set(FlowTaskListMobileComponentExt.INCLUDE_FIELDS, Lists.newArrayList());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试addIncludeField私有方法通过resetIncludeFields间接测试
   */
  @Test
  @DisplayName("测试addIncludeField方法通过resetIncludeFields间接测试")
  void testAddIncludeFieldThroughResetIncludeFields() {
    try (MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class);
         MockedStatic<Document> mockedDocument = mockStatic(Document.class)) {
      
      // 准备测试数据
      ITableColumn column = mock(ITableColumn.class);
      
      // 简化测试，不需要Document mock
      
      List<ITableColumn> includeFields = Lists.newArrayList(column);
      mockedCollectionUtils.when(() -> CollectionUtils.nullToEmpty(includeFields))
          .thenReturn(includeFields);

      // 执行测试
      flowTaskListMobileComponentExt.resetIncludeFields(includeFields);

      // 验证set方法被调用
      verify(mockComponent).set(eq(FlowTaskListMobileComponentExt.INCLUDE_FIELDS), any());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试常量值的正确性
   */
  @Test
  @DisplayName("测试常量值")
  void testConstants() {
    assertEquals("mobile_field_type", FlowTaskListMobileComponentExt.MOBILE_FIELD_TYPE);
    assertEquals("0", FlowTaskListMobileComponentExt.MOBILE_FIELD_TYPE_USER_LAYOUT);
    assertEquals("1", FlowTaskListMobileComponentExt.MOBILE_FIELD_TYPE_USER_DEFINE);
    assertEquals("include_fields", FlowTaskListMobileComponentExt.INCLUDE_FIELDS);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试继承自BaseFlowTaskListComponentExt的方法是否可用
   */
  @Test
  @DisplayName("测试继承的方法可用性")
  void testInheritedMethods() {
    // 测试getComponent方法（继承自父类）
    IComponent component = flowTaskListMobileComponentExt.getComponent();
    assertEquals(mockComponent, component);
    
    // 测试常量可用性
    assertEquals("task_list", BaseFlowTaskListComponentExt.COMPONENT_TYPE_TASK_LIST);
    assertEquals("task_list_mobile", BaseFlowTaskListComponentExt.COMPONENT_TYPE_TASK_LIST_MOBILE);
  }
}
