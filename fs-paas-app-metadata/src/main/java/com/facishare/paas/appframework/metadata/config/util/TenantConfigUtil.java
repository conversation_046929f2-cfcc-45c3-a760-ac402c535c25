package com.facishare.paas.appframework.metadata.config.util;

import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.User;

import java.util.Objects;

public class TenantConfigUtil {

    /**
     * 客户账户是否开启
     * @param configService
     * @param tenantId
     * @return
     */
    public static boolean isCustomerAccountEnabled(ConfigService configService, String tenantId) {
        return getBizEnable(configService, tenantId, "29");
    }

    /**
     * 发货单是否开启
     * @param configService
     * @param tenantId
     * @return
     */

    public static boolean isDeliveryNoteEnabled(ConfigService configService, String tenantId) {
        return getBizEnable(configService, tenantId, "33");
    }

    /**
     * 库存是否开启
     * @param configService
     * @param tenantId
     * @return
     */
    public static boolean isInventoryEnabled(ConfigService configService, String tenantId) {
        return getBizEnable(configService, tenantId, "30");
    }

    /**
     * 促销是否开启
     * @param configService
     * @param tenantId
     * @return
     */
    public static boolean isPromotionEnabled(ConfigService configService, String tenantId) {
        return getBizEnable(configService, tenantId, "31");
    }

    /**
     * 订货通是否开启
     * @param configService
     * @param tenantId
     * @return
     */
    public static boolean isDingHuoTongEnabled(ConfigService configService, String tenantId) {
        return getBizEnable(configService, tenantId, "23");
    }

    public static boolean isPartnerEnabled(ConfigService configService, String tenantId) {
        return getBizEnable(configService, tenantId, "is_open_partner");
    }

    /**
     * 快消是否开启
     * @param configService
     * @param tenantId
     * @return
     */
    public static boolean isFastSellingEnabled(ConfigService configService, String tenantId) {
        return getBizEnable(configService, tenantId, "24");
    }

    /**
     * 商机2.0
     * @param configService
     * @param tenantId
     * @return
     */
    public static boolean isOpenNewOpportunity(ConfigService configService, String tenantId) {
        return compareBizConfigValue(configService, tenantId, "config_newopportunity_open", "open");
    }

    /**
     * 价目表开关
     * @param configService
     * @param tenantId
     * @return
     */
    public static boolean isPriceBookEnabled(ConfigService configService, String tenantId) {
        return getBizEnable(configService, tenantId, "28");
    }


    /**
     * 是否开启多单位
     * @param configService
     * @param tenantId
     * @return
     */
    public static boolean isMultipleUnitEnabled(ConfigService configService, String tenantId) {
        return getBizEnable(configService, tenantId, "multiple_unit");
    }


    /**
     * 设备管理
     * @param configService
     * @param tenantId
     * @return
     */
    public static boolean isDeviceManagementEnabled(ConfigService configService, String tenantId) {
        return getBizEnable(configService, tenantId, "35");
    }

    /**
     * SPU
     * @param configService
     * @param tenantId
     * @return
     */
    public static boolean isSpuEnabled(ConfigService configService, String tenantId) {
        return getBizEnable(configService, tenantId, "spu");
    }


    /**
     * 是否开票模式3
     * @param configService
     * @param tenantId
     * @return
     */
    public static boolean isSalesOrderProductInvoiceModel(ConfigService configService, String tenantId) {
        return compareBizConfigValue(configService, tenantId, "invoice_mode", "sales_order_product");
    }

    public static boolean compareBizConfigValue(ConfigService configService, String tenantId, String key, String value) {
        String configResult = configService.findTenantConfig(User.systemUser(tenantId), key);
        return value.equals(configResult);
    }

    public static boolean isPricePolicyEnabled(ConfigService configService, String tenantId) {
        return getBizEnable(configService, tenantId, "price_policy");
    }

    private static boolean getBizEnable(ConfigService configService, String tenantId, String key) {
        String configResult = configService.findTenantConfig(User.systemUser(tenantId), key);
        return "1".equals(configResult);
    }

    public static boolean isDemoEnable(ConfigService configService, String tenantId) {
        return getBizEnable(configService, tenantId, "demo");
    }

    public static boolean isMultiOrderDeliverEnable(ConfigService configService, String tenantId) {
        return getBizEnable(configService, tenantId, "delivery_note_multi_order_deliver_switch");
    }

    public static boolean isOpenCoupon(ConfigService configService, String tenantId) {
        return getBizEnable(configService, tenantId, "coupon");
    }


    public static boolean isOpenRebate(ConfigService configService, String tenantId) {
        return getBizEnable(configService, tenantId, "rebate");
    }

    public static boolean isInvoiceOrderDecoupling(ConfigService configService, String tenantId) {
        return compareBizConfigValue(configService, tenantId, "invoice_order_binding_status", "0");
    }

    public static boolean isStratifiedPriceEnabled(ConfigService configService, String tenantId) {
        return getBizEnable(configService, tenantId, "stratified_pricing");
    }
}
