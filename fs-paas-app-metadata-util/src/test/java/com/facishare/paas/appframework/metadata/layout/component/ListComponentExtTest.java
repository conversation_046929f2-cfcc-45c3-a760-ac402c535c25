package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.appframework.metadata.ButtonUsePageType;
import com.facishare.paas.appframework.metadata.layout.LayoutContext;
import com.facishare.paas.appframework.metadata.layout.LayoutStructure;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.describe.CurrencyFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.impl.ui.layout.component.CommonComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ListComponentExtTest {

    private ListComponentExt listComponentExt;
    private Map<String, Object> testComponentMap;

    @BeforeEach
    void setUp() {
        testComponentMap = new HashMap<>();
        testComponentMap.put("type", "list");
        testComponentMap.put("api_name", "list_component");
        testComponentMap.put("header", "测试列表页");
        
        List<Map<String, Object>> buttonInfo = new ArrayList<>();
        Map<String, Object> normalButton = new HashMap<>();
        normalButton.put("render_type", "list_normal");
        normalButton.put("page_type", "list");
        normalButton.put("order", Arrays.asList("create", "edit"));
        normalButton.put("hidden", Arrays.asList("delete"));
        normalButton.put("exposed_button", 2);
        buttonInfo.add(normalButton);
        
        Map<String, Object> batchButton = new HashMap<>();
        batchButton.put("render_type", "list_batch");
        batchButton.put("page_type", "list");
        batchButton.put("order", Arrays.asList("batch_delete"));
        batchButton.put("hidden", new ArrayList<>());
        batchButton.put("exposed_button", 1);
        buttonInfo.add(batchButton);
        testComponentMap.put("button_info", buttonInfo);
        
        List<Map<String, Object>> sceneInfo = new ArrayList<>();
        Map<String, Object> scene = new HashMap<>();
        scene.put("render_type", "drop_down");
        scene.put("page_type", "list");
        scene.put("order", Arrays.asList("all", "my"));
        scene.put("hidden", Arrays.asList("deleted"));
        sceneInfo.add(scene);
        testComponentMap.put("scene_info", sceneInfo);
        
        List<Map<String, Object>> viewInfo = new ArrayList<>();
        Map<String, Object> listView = new HashMap<>();
        listView.put("name", "list_view");
        listView.put("is_show", true);
        listView.put("is_default", true);
        viewInfo.add(listView);
        
        Map<String, Object> calendarView = new HashMap<>();
        calendarView.put("name", "calendar_view");
        calendarView.put("is_show", false);
        calendarView.put("is_default", false);
        viewInfo.add(calendarView);
        testComponentMap.put("view_info", viewInfo);
        
        List<Map<String, Object>> filtersInfo = new ArrayList<>();
        Map<String, Object> filter = new HashMap<>();
        filter.put("fields", Arrays.asList("name", "status"));
        filter.put("page_type", "list");
        filtersInfo.add(filter);
        testComponentMap.put("filters_info", filtersInfo);
        
        List<Map<String, Object>> renderTypeInfo = new ArrayList<>();
        Map<String, Object> renderType = new HashMap<>();
        renderType.put("name", "table");
        renderType.put("page_type", "list");
        renderTypeInfo.add(renderType);
        testComponentMap.put("render_type_info", renderTypeInfo);
        
        List<Map<String, Object>> summaryInfo = new ArrayList<>();
        Map<String, Object> summary = new HashMap<>();
        summary.put("field_name", "amount");
        summary.put("type", "sum");
        summary.put("api_name", "amount_sum");
        summaryInfo.add(summary);
        testComponentMap.put("summary_info", summaryInfo);
        
        List<Map<String, Object>> allPageSummaryInfo = new ArrayList<>();
        Map<String, Object> allPageSummary = new HashMap<>();
        allPageSummary.put("field_name", "count");
        allPageSummary.put("type", "count");
        allPageSummary.put("api_name", "total_count");
        allPageSummaryInfo.add(allPageSummary);
        testComponentMap.put("all_page_summary_info", allPageSummaryInfo);
        
        List<Map<String, Object>> aggregateFieldInfo = new ArrayList<>();
        Map<String, Object> aggregate = new HashMap<>();
        aggregate.put("render_type", "current");
        aggregate.put("page_type", "list");
        List<Map<String, Object>> summaryFields = new ArrayList<>();
        Map<String, Object> summaryField = new HashMap<>();
        summaryField.put("field_name", "amount");
        summaryField.put("type", "sum");
        summaryField.put("api_name", "amount_sum");
        summaryFields.add(summaryField);
        aggregate.put("summary_fields", summaryFields);
        aggregateFieldInfo.add(aggregate);
        testComponentMap.put("aggregate_field_info", aggregateFieldInfo);
        
        testComponentMap.put("enable_selected_layout", true);
        
        Map<String, Object> sideListFilter = new HashMap<>();
        sideListFilter.put("items_source", "custom");
        sideListFilter.put("custom_items", Arrays.asList("name", "status"));
        testComponentMap.put("side_list_filter", sideListFilter);
        
        Document topListFilter = new Document();
        topListFilter.put("switch", true);
        testComponentMap.put("top_list_filter", topListFilter);
        
        testComponentMap.put("display_totals", true);
        testComponentMap.put("selected_display_totals", false);
        testComponentMap.put("display_mode_style", "tile");
        testComponentMap.put("field_api_name", "parent_id");
        
        listComponentExt = ListComponentExt.of(testComponentMap);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ListComponentExt的静态工厂方法of(IComponent)创建实例
     */
    @Test
    @DisplayName("测试of(IComponent)创建实例")
    void testOfIComponentCreation() {
        // 准备测试数据
        IComponent component = new CommonComponent(testComponentMap);

        // 执行测试
        ListComponentExt result = ListComponentExt.of(component);

        // 验证结果
        assertNotNull(result);
        assertEquals(component, result.getComponent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ListComponentExt的静态工厂方法of(Map)创建实例
     */
    @Test
    @DisplayName("测试of(Map)创建实例")
    void testOfMapCreation() {
        // 执行测试
        ListComponentExt result = ListComponentExt.of(testComponentMap);

        // 验证结果
        assertNotNull(result);
        assertEquals("list", result.getType());
        assertEquals("list_component", result.getName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试filterSummaryInfos静态方法过滤汇总信息
     */
    @Test
    @DisplayName("测试filterSummaryInfos静态方法过滤汇总信息")
    void testFilterSummaryInfos() {
        // 准备测试数据
        ObjectDescribe describe = new ObjectDescribe();
        Map<String, Object> amountFieldMap = new HashMap<>();
        amountFieldMap.put("api_name", "amount");
        amountFieldMap.put("is_active", true);
        CurrencyFieldDescribe amountField = new CurrencyFieldDescribe(amountFieldMap);
        
        Map<String, Object> countFieldMap = new HashMap<>();
        countFieldMap.put("api_name", "count");
        countFieldMap.put("is_active", true);
        CurrencyFieldDescribe countField = new CurrencyFieldDescribe(countFieldMap);
        
        describe.addFieldDescribeList(Arrays.asList(amountField, countField));
        
        ISummaryComponentInfo summaryInfo1 = mock(ISummaryComponentInfo.class);
        when(summaryInfo1.getFieldName()).thenReturn("amount");
        
        ISummaryComponentInfo summaryInfo2 = mock(ISummaryComponentInfo.class);
        when(summaryInfo2.getFieldName()).thenReturn("count");
        
        List<ISummaryComponentInfo> summaryInfos = Arrays.asList(summaryInfo1, summaryInfo2);
        List<String> authorizedFields = Arrays.asList("amount");

        // 执行测试
        List<ISummaryComponentInfo> result = ListComponentExt.filterSummaryInfos(describe, summaryInfos, authorizedFields);

        // 验证结果
        assertEquals(1, result.size());
        assertEquals("amount", result.get(0).getFieldName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试filterSummaryInfos方法处理空列表
     */
    @Test
    @DisplayName("测试filterSummaryInfos方法处理空列表")
    void testFilterSummaryInfosWithEmptyList() {
        // 准备测试数据
        IObjectDescribe describe = mock(IObjectDescribe.class);
        List<ISummaryComponentInfo> summaryInfos = new ArrayList<>();
        List<String> authorizedFields = Arrays.asList("amount");

        // 执行测试
        List<ISummaryComponentInfo> result = ListComponentExt.filterSummaryInfos(describe, summaryInfos, authorizedFields);

        // 验证结果
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试国际化key的获取和设置
     */
    @Test
    @DisplayName("测试国际化key的获取和设置")
    void testNameI18nKeyOperations() {
        // 准备测试数据
        String testKey = "test.i18n.key";

        // 执行测试
        listComponentExt.setNameI18nKey(testKey);
        String result = listComponentExt.getNameI18nKey();

        // 验证结果
        assertEquals(testKey, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试fillSceneInfoPageType方法填充场景信息的页面类型
     */
    @Test
    @DisplayName("测试fillSceneInfoPageType方法填充场景信息的页面类型")
    void testFillSceneInfoPageType() {
        // 准备测试数据
        String pageType = "related";

        // 执行测试
        ListComponentExt result = listComponentExt.fillSceneInfoPageType(pageType);

        // 验证结果
        assertEquals(listComponentExt, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试fillButtonInfoPageType方法填充按钮信息的页面类型
     */
    @Test
    @DisplayName("测试fillButtonInfoPageType方法填充按钮信息的页面类型")
    void testFillButtonInfoPageType() {
        // 准备测试数据
        String pageType = "related";

        // 执行测试
        ListComponentExt result = listComponentExt.fillButtonInfoPageType(pageType);

        // 验证结果
        assertEquals(listComponentExt, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取按钮信息列表
     */
    @Test
    @DisplayName("测试获取按钮信息列表")
    void testGetButtonInfo() {
        // 执行测试
        List<IListComponentInfo> result = listComponentExt.getButtonInfo();

        // 验证结果
        assertEquals(2, result.size());
        assertEquals("list_normal", result.get(0).getRenderType());
        assertEquals("list_batch", result.get(1).getRenderType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空按钮信息的情况
     */
    @Test
    @DisplayName("测试空按钮信息的情况")
    void testGetButtonInfoWithEmpty() {
        // 准备测试数据
        ListComponentExt emptyComponent = ListComponentExt.of(new HashMap<>());

        // 执行测试
        List<IListComponentInfo> result = emptyComponent.getButtonInfo();

        // 验证结果
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试重置按钮信息列表
     */
    @Test
    @DisplayName("测试重置按钮信息列表")
    void testResetButtonInfos() {
        // 保存原始按钮信息
        List<IListComponentInfo> originalButtonInfos = listComponentExt.getButtonInfo();
        List<IListComponentInfo> newButtonInfos = Arrays.asList(
            ListComponentInfo.listNormal(),
            ListComponentInfo.listBatch()
        );

        // 执行测试
        listComponentExt.resetButtonInfos(newButtonInfos);
        List<IListComponentInfo> result = listComponentExt.getButtonInfo();

        // 验证结果
        assertEquals(2, result.size());

        // 恢复原始按钮信息
        listComponentExt.resetButtonInfos(originalButtonInfos);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试添加按钮信息
     */
    @Test
    @DisplayName("测试添加按钮信息")
    void testAddButtonInfo() {
        // 保存原始按钮信息
        List<IListComponentInfo> originalButtonInfos = listComponentExt.getButtonInfo();
        int originalSize = originalButtonInfos.size();
        IListComponentInfo buttonInfo = ListComponentInfo.listSingle();

        // 执行测试
        listComponentExt.addButtonInfo(buttonInfo);
        List<IListComponentInfo> result = listComponentExt.getButtonInfo();

        // 验证结果
        assertEquals(originalSize + 1, result.size());

        // 恢复原始按钮信息
        listComponentExt.resetButtonInfos(originalButtonInfos);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取场景信息列表
     */
    @Test
    @DisplayName("测试获取场景信息列表")
    void testGetSceneInfo() {
        // 执行测试
        List<IScenesComponentInfo> result = listComponentExt.getSceneInfo();

        // 验证结果
        assertEquals(1, result.size());
        assertEquals("drop_down", result.get(0).getRenderType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据页面类型获取场景信息
     */
    @Test
    @DisplayName("测试根据页面类型获取场景信息")
    void testGetSceneInfoByPageType() {
        // 执行测试
        Optional<IScenesComponentInfo> result = listComponentExt.getSceneInfoByPageType("list");

        // 验证结果
        assertTrue(result.isPresent());
        assertEquals("list", result.get().getPageType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据不存在的页面类型获取场景信息
     */
    @Test
    @DisplayName("测试根据不存在的页面类型获取场景信息")
    void testGetSceneInfoByPageTypeWithNonExistent() {
        // 执行测试
        Optional<IScenesComponentInfo> result = listComponentExt.getSceneInfoByPageType("non_existent");

        // 验证结果
        assertFalse(result.isPresent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据页面类型获取通用信息
     */
    @ParameterizedTest
    @MethodSource("provideGeneralInfoTestData")
    @DisplayName("测试根据页面类型获取通用信息")
    void testGetGeneralInfoByPageType(boolean enableSelectedLayout, String pageType, boolean expectedPresent) {
        // 准备测试数据
        Map<String, Object> componentMap = new HashMap<>();
        componentMap.put("enable_selected_layout", enableSelectedLayout);
        List<Map<String, Object>> generalInfo = new ArrayList<>();
        Map<String, Object> listInfo = new HashMap<>();
        listInfo.put("page_type", "list");
        listInfo.put("data", "list_data");
        generalInfo.add(listInfo);

        Map<String, Object> selectedInfo = new HashMap<>();
        selectedInfo.put("page_type", "selected");
        selectedInfo.put("data", "selected_data");
        generalInfo.add(selectedInfo);
        componentMap.put("general_info", generalInfo);

        ListComponentExt component = ListComponentExt.of(componentMap);

        // 执行测试
        Optional<GeneralComponentInfo> result = component.getGeneralInfoByPageType(pageType);

        // 验证结果
        if (expectedPresent) {
            assertTrue(result.isPresent());
        } else {
            assertFalse(result.isPresent());
        }
    }

    /**
     * 提供通用信息测试数据
     */
    private static Stream<Arguments> provideGeneralInfoTestData() {
        return Stream.of(
            Arguments.of(true, "selected", true),   // 启用选中布局，查询selected页面类型，但实际会查找list，应该找到
            Arguments.of(false, "selected", true),  // 未启用选中布局，查询selected页面类型，应该找到（因为测试数据中有selected）
            Arguments.of(true, "list", true),       // 启用选中布局，查询list页面类型，会查找list，应该找到
            Arguments.of(false, "list", true)       // 未启用选中布局，查询list页面类型，应该找到
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取通用信息列表
     */
    @Test
    @DisplayName("测试获取通用信息列表")
    void testGetGeneralInfo() {
        // 准备测试数据
        Map<String, Object> componentMap = new HashMap<>();
        List<Map<String, Object>> generalInfo = new ArrayList<>();
        Map<String, Object> info = new HashMap<>();
        info.put("page_type", "list");
        info.put("data", "test_data");
        generalInfo.add(info);
        componentMap.put("general_info", generalInfo);

        ListComponentExt component = ListComponentExt.of(componentMap);

        // 执行测试
        List<GeneralComponentInfo> result = component.getGeneralInfo();

        // 验证结果
        assertEquals(1, result.size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试重置场景信息列表
     */
    @Test
    @DisplayName("测试重置场景信息列表")
    void testResetSceneInfos() {
        // 保存原始场景信息
        List<IScenesComponentInfo> originalSceneInfos = listComponentExt.getSceneInfo();
        List<IScenesComponentInfo> sceneInfos = Arrays.asList(ScenesComponentInfo.defaultSceneInfo());

        // 执行测试
        listComponentExt.resetSceneInfos(sceneInfos);
        List<IScenesComponentInfo> result = listComponentExt.getSceneInfo();

        // 验证结果
        assertEquals(1, result.size());

        // 恢复原始场景信息
        listComponentExt.resetSceneInfos(originalSceneInfos);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试添加场景信息
     */
    @Test
    @DisplayName("测试添加场景信息")
    void testAddSceneInfo() {
        // 保存原始场景信息
        List<IScenesComponentInfo> originalSceneInfos = listComponentExt.getSceneInfo();
        int originalSize = originalSceneInfos.size();
        IScenesComponentInfo sceneInfo = ScenesComponentInfo.defaultSceneInfo();

        // 执行测试
        listComponentExt.addSceneInfo(sceneInfo);
        List<IScenesComponentInfo> result = listComponentExt.getSceneInfo();

        // 验证结果
        assertEquals(originalSize + 1, result.size());

        // 恢复原始场景信息
        listComponentExt.resetSceneInfos(originalSceneInfos);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取渲染类型信息列表
     */
    @Test
    @DisplayName("测试获取渲染类型信息列表")
    void testGetRenderTypeInfo() {
        // 执行测试
        List<IRenderTypeComponentInfo> result = listComponentExt.getRenderTypeInfo();

        // 验证结果
        assertEquals(1, result.size());
        assertEquals("table", result.get(0).getRenderType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据页面类型获取渲染类型信息
     */
    @Test
    @DisplayName("测试根据页面类型获取渲染类型信息")
    void testGetRenderTypeInfoByPageType() {
        // 执行测试
        Optional<IRenderTypeComponentInfo> result = listComponentExt.getRenderTypeInfoByPageType("list");

        // 验证结果
        assertTrue(result.isPresent());
        assertEquals("list", result.get().getPageType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试重置渲染类型信息列表
     */
    @Test
    @DisplayName("测试重置渲染类型信息列表")
    void testResetRenderTypeInfos() {
        // 准备测试数据
        List<IRenderTypeComponentInfo> renderTypeInfos = Arrays.asList(new RenderTypeComponentInfo());

        // 执行测试
        assertDoesNotThrow(() -> listComponentExt.resetRenderTypeInfos(renderTypeInfos));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试添加渲染类型信息
     */
    @Test
    @DisplayName("测试添加渲染类型信息")
    void testAddRenderTypeInfo() {
        // 准备测试数据
        RenderTypeComponentInfo renderTypeInfo = new RenderTypeComponentInfo();

        // 执行测试
        assertDoesNotThrow(() -> listComponentExt.addRenderTypeInfo(renderTypeInfo));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取过滤信息字段列表
     */
    @Test
    @DisplayName("测试获取过滤信息字段列表")
    void testGetFiltersInfos() {
        // 执行测试
        List<String> result = listComponentExt.getFiltersInfos("list");

        // 验证结果
        assertEquals(2, result.size());
        assertTrue(result.contains("name"));
        assertTrue(result.contains("status"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取过滤组件信息列表
     */
    @Test
    @DisplayName("测试获取过滤组件信息列表")
    void testGetFiltersComponentInfo() {
        // 执行测试
        List<IFiltersComponentInfo> result = listComponentExt.getFiltersComponentInfo();

        // 验证结果
        assertTrue(result.size() >= 1);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取过滤信息（兼容旧版本）
     */
    @Test
    @DisplayName("测试获取过滤信息（兼容旧版本）")
    void testGetFilterInfos() {
        // 准备测试数据
        Map<String, Object> componentMap = new HashMap<>();
        componentMap.put("filter_info", "[\"name\", \"status\"]");
        ListComponentExt component = ListComponentExt.of(componentMap);

        // 执行测试
        List<String> result = component.getFilterInfos();

        // 验证结果
        assertEquals(2, result.size());
        assertTrue(result.contains("name"));
        assertTrue(result.contains("status"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取空过滤信息
     */
    @Test
    @DisplayName("测试获取空过滤信息")
    void testGetFilterInfosWithNull() {
        // 准备测试数据
        ListComponentExt component = ListComponentExt.of(new HashMap<>());

        // 执行测试
        List<String> result = component.getFilterInfos();

        // 验证结果
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试重置过滤信息列表
     */
    @Test
    @DisplayName("测试重置过滤信息列表")
    void testResetFiltersInfos() {
        // 保存原始过滤信息
        List<IFiltersComponentInfo> originalFiltersInfos = listComponentExt.getFiltersComponentInfo();
        List<IFiltersComponentInfo> filtersInfos = Arrays.asList(FiltersComponentInfo.ofFilterInfos(Arrays.asList("name"), "list"));

        // 执行测试
        listComponentExt.resetFiltersInfos(filtersInfos);
        List<IFiltersComponentInfo> result = listComponentExt.getFiltersComponentInfo();

        // 验证结果
        assertTrue(result.size() >= 1);

        // 恢复原始过滤信息
        listComponentExt.resetFiltersInfos(originalFiltersInfos);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试添加过滤信息
     */
    @Test
    @DisplayName("测试添加过滤信息")
    void testAddFiltersInfo() {
        // 保存原始过滤信息
        List<IFiltersComponentInfo> originalFiltersInfos = listComponentExt.getFiltersComponentInfo();
        IFiltersComponentInfo filtersInfo = FiltersComponentInfo.ofFilterInfos(Arrays.asList("test_field"), "list");

        // 执行测试
        assertDoesNotThrow(() -> listComponentExt.addFiltersInfo(filtersInfo));

        // 恢复原始过滤信息
        listComponentExt.resetFiltersInfos(originalFiltersInfos);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取视图信息列表
     */
    @Test
    @DisplayName("测试获取视图信息列表")
    void testGetViewInfos() {
        // 执行测试
        List<IViewComponentInfo> result = listComponentExt.getViewInfos();

        // 验证结果
        assertEquals(2, result.size());
        assertEquals("list_view", result.get(0).getName());
        assertEquals("calendar_view", result.get(1).getName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试重置视图信息列表
     */
    @Test
    @DisplayName("测试重置视图信息列表")
    void testResetViewInfos() {
        // 保存原始视图信息
        List<IViewComponentInfo> originalViewInfos = listComponentExt.getViewInfos();
        Map<String, Object> viewData = new HashMap<>();
        viewData.put("name", "test_view");
        viewData.put("is_show", true);
        List<IViewComponentInfo> viewInfos = Arrays.asList(new ViewComponentInfo(viewData));

        // 执行测试
        listComponentExt.resetViewInfos(viewInfos);
        List<IViewComponentInfo> result = listComponentExt.getViewInfos();

        // 验证结果
        assertTrue(result.size() >= 1);

        // 恢复原始视图信息
        listComponentExt.resetViewInfos(originalViewInfos);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据使用页面和渲染页面类型获取按钮信息
     */
    @Test
    @DisplayName("测试根据使用页面和渲染页面类型获取按钮信息")
    void testGetButtonInfoByUsePageAndRenderPageType() {
        // 执行测试
        Optional<IListComponentInfo> result = listComponentExt.getButtonInfoByUsePageAndRenderPageType(ButtonUsePageType.ListNormal, "list");

        // 验证结果
        assertTrue(result.isPresent());
        assertEquals("list_normal", result.get().getRenderType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据使用页面和不存在的渲染页面类型获取按钮信息
     */
    @Test
    @DisplayName("测试根据使用页面和不存在的渲染页面类型获取按钮信息")
    void testGetButtonInfoByUsePageAndRenderPageTypeWithNonExistent() {
        // 执行测试
        Optional<IListComponentInfo> result = listComponentExt.getButtonInfoByUsePageAndRenderPageType(ButtonUsePageType.Create, "non_existent");

        // 验证结果
        assertFalse(result.isPresent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试按使用页面过滤按钮列表
     */
    @Test
    @DisplayName("测试按使用页面过滤按钮列表")
    void testFilterButtonByUsePage() {
        // 准备测试数据
        IButton button1 = mock(IButton.class);
        when(button1.getName()).thenReturn("create");

        IButton button2 = mock(IButton.class);
        when(button2.getName()).thenReturn("edit");

        IButton button3 = mock(IButton.class);
        when(button3.getName()).thenReturn("delete");

        List<IButton> buttons = Arrays.asList(button1, button2, button3);

        // 执行测试
        List<IButton> result = listComponentExt.filterButtonByUsePage(buttons, ButtonUsePageType.ListNormal, "list");

        // 验证结果 - create和edit，delete被隐藏
        assertEquals(2, result.size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试按使用页面过滤自定义按钮列表
     */
    @Test
    @DisplayName("测试按使用页面过滤自定义按钮列表")
    void testFilterUdefButtonsByUsePage() {
        // 准备测试数据
        IUdefButton udefButton1 = mock(IUdefButton.class);
        when(udefButton1.getApiName()).thenReturn("custom_button1");

        IUdefButton udefButton2 = mock(IUdefButton.class);
        when(udefButton2.getApiName()).thenReturn("custom_button2");

        List<IUdefButton> udefButtons = Arrays.asList(udefButton1, udefButton2);

        // 执行测试
        List<IUdefButton> result = listComponentExt.filterUdefButtonsByUsePage(udefButtons, ButtonUsePageType.ListNormal, "list");

        // 验证结果
        assertTrue(result.size() >= 0);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试过滤模板功能
     */
    @Test
    @DisplayName("测试过滤模板功能")
    void testFilterTemplate() {
        // 准备测试数据
        ISearchTemplate template1 = mock(ISearchTemplate.class);
        when(template1.getApiName()).thenReturn("all");

        ISearchTemplate template2 = mock(ISearchTemplate.class);
        when(template2.getApiName()).thenReturn("my");

        ISearchTemplate template3 = mock(ISearchTemplate.class);
        when(template3.getApiName()).thenReturn("deleted");

        List<ISearchTemplate> templates = Arrays.asList(template1, template2, template3);

        // 执行测试
        List<ISearchTemplate> result = listComponentExt.filterTemplate(templates, null, "list");

        // 验证结果 - deleted被隐藏
        assertEquals(2, result.size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取列表通用按钮
     */
    @Test
    @DisplayName("测试获取列表通用按钮")
    void testGetListNormalButtons() {
        // 准备测试数据
        Map<String, Object> componentMap = new HashMap<>();
        List<Map<String, Object>> buttons = new ArrayList<>();
        Map<String, Object> button1 = new HashMap<>();
        button1.put("name", "create");
        button1.put("api_name", "create");
        buttons.add(button1);

        Map<String, Object> button2 = new HashMap<>();
        button2.put("name", "edit");
        button2.put("api_name", "edit");
        buttons.add(button2);

        componentMap.put("list_normal_buttons", buttons);
        ListComponentExt component = ListComponentExt.of(componentMap);

        // 执行测试
        List<IButton> result = component.getListNormalButtons();

        // 验证结果
        assertEquals(2, result.size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试重置列表通用按钮
     */
    @Test
    @DisplayName("测试重置列表通用按钮")
    void testResetListNormalButtons() {
        // 使用独立的组件进行测试，避免影响共享对象
        ListComponentExt testComponent = ListComponentExt.of(new HashMap<>());
        Map<String, Object> buttonData = new HashMap<>();
        buttonData.put("name", "test_button");
        List<IButton> buttons = Arrays.asList(new Button(buttonData));

        // 执行测试
        testComponent.resetListNormalButtons(buttons);
        List<IButton> result = testComponent.getListNormalButtons();

        // 验证结果
        assertTrue(result.size() >= 1);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取列表批量按钮
     */
    @Test
    @DisplayName("测试获取列表批量按钮")
    void testGetListBatchButtons() {
        // 准备测试数据
        Map<String, Object> componentMap = new HashMap<>();
        List<Map<String, Object>> buttons = new ArrayList<>();
        Map<String, Object> button = new HashMap<>();
        button.put("name", "batch_delete");
        button.put("api_name", "batch_delete");
        buttons.add(button);

        componentMap.put("list_batch_buttons", buttons);
        ListComponentExt component = ListComponentExt.of(componentMap);

        // 执行测试
        List<IButton> result = component.getListBatchButtons();

        // 验证结果
        assertEquals(1, result.size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试重置列表批量按钮
     */
    @Test
    @DisplayName("测试重置列表批量按钮")
    void testResetListBatchButtons() {
        // 使用独立的组件进行测试，避免影响共享对象
        ListComponentExt testComponent = ListComponentExt.of(new HashMap<>());
        Map<String, Object> buttonData = new HashMap<>();
        buttonData.put("name", "batch_test");
        List<IButton> buttons = Arrays.asList(new Button(buttonData));

        // 执行测试
        testComponent.resetListBatchButtons(buttons);
        List<IButton> result = testComponent.getListBatchButtons();

        // 验证结果
        assertTrue(result.size() >= 1);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据页面类型获取汇总组件信息
     */
    @Test
    @DisplayName("测试根据页面类型获取汇总组件信息")
    void testGetSummaryComponentInfoByPageType() {
        // 执行测试
        List<ISummaryComponentInfo> result = listComponentExt.getSummaryComponentInfoByPageType("list");

        // 验证结果
        assertEquals(1, result.size());
        assertEquals("amount", result.get(0).getFieldName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试重置汇总组件信息
     */
    @Test
    @DisplayName("测试重置汇总组件信息")
    void testResetSummaryComponentInfo() {
        // 使用独立的组件进行测试，避免影响共享对象
        Map<String, Object> componentMap = new HashMap<>();
        List<Map<String, Object>> summaryInfo = new ArrayList<>();
        Map<String, Object> summary = new HashMap<>();
        summary.put("field_name", "test_field");
        summary.put("type", "sum");
        summary.put("api_name", "test_sum");
        summaryInfo.add(summary);
        componentMap.put("summary_info", summaryInfo);

        ListComponentExt testComponent = ListComponentExt.of(componentMap);
        List<ISummaryComponentInfo> summaryInfos = Arrays.asList(new SummaryComponentInfo());

        // 执行测试
        assertDoesNotThrow(() -> testComponent.resetSummaryComponentInfo(summaryInfos));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据页面类型获取全页汇总组件信息
     */
    @Test
    @DisplayName("测试根据页面类型获取全页汇总组件信息")
    void testGetAllPageSummaryComponentInfoByPageType() {
        // 执行测试
        List<ISummaryComponentInfo> result = listComponentExt.getAllPageSummaryComponentInfoByPageType("list");

        // 验证结果
        assertTrue(result.size() >= 0);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试设置全页汇总组件信息
     */
    @Test
    @DisplayName("测试设置全页汇总组件信息")
    void testSetAllPageSummaryComponentInfo() {
        // 使用独立的组件进行测试，避免影响共享对象
        ListComponentExt testComponent = ListComponentExt.of(new HashMap<>());
        ISummaryComponentInfo summaryInfo = mock(ISummaryComponentInfo.class);
        when(summaryInfo.toJsonString()).thenReturn("{\"field_name\":\"test\",\"type\":\"sum\"}");
        List<ISummaryComponentInfo> summaryInfos = Arrays.asList(summaryInfo);

        // 执行测试
        assertDoesNotThrow(() -> testComponent.setAllPageSummaryComponentInfo(summaryInfos));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据页面类型获取选中数据汇总组件信息
     */
    @Test
    @DisplayName("测试根据页面类型获取选中数据汇总组件信息")
    void testGetSelectedDataSummaryComponentInfoByPageType() {
        // 执行测试
        List<ISummaryComponentInfo> result = listComponentExt.getSelectedDataSummaryComponentInfoByPageType("list");

        // 验证结果
        assertTrue(result.size() >= 0);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据页面类型获取聚合信息
     */
    @Test
    @DisplayName("测试根据页面类型获取聚合信息")
    void testGetAggregateInfosByPageType() {
        // 执行测试
        List<IAggregateComponentInfo> result = listComponentExt.getAggregateInfosByPageType("list");

        // 验证结果
        assertTrue(result.size() >= 0);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取聚合信息列表
     */
    @Test
    @DisplayName("测试获取聚合信息列表")
    void testGetAggregateInfos() {
        // 执行测试
        List<IAggregateComponentInfo> result = listComponentExt.getAggregateInfos();

        // 验证结果
        assertTrue(result.size() >= 0);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试重置聚合信息
     */
    @Test
    @DisplayName("测试重置聚合信息")
    void testResetAggregateInfos() {
        // 使用独立的组件进行测试，避免影响共享对象
        Map<String, Object> componentMap = new HashMap<>();
        List<Map<String, Object>> aggregateFieldInfo = new ArrayList<>();
        Map<String, Object> aggregate = new HashMap<>();
        aggregate.put("render_type", "current");
        aggregate.put("page_type", "list");
        aggregate.put("summary_fields", new ArrayList<>());
        aggregateFieldInfo.add(aggregate);
        componentMap.put("aggregate_field_info", aggregateFieldInfo);

        ListComponentExt testComponent = ListComponentExt.of(componentMap);
        IAggregateComponentInfo aggregateInfo = mock(IAggregateComponentInfo.class);
        when(aggregateInfo.toJsonString()).thenReturn("{\"render_type\":\"current\",\"page_type\":\"list\"}");
        List<IAggregateComponentInfo> aggregateInfos = Arrays.asList(aggregateInfo);

        // 执行测试
        assertDoesNotThrow(() -> testComponent.resetAggregateInfos(aggregateInfos));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试是否启用聚合功能
     */
    @Test
    @DisplayName("测试是否启用聚合功能")
    void testEnableAggregate() {
        // 执行测试
        boolean result = listComponentExt.enableAggregate();

        // 验证结果 - 因为测试数据中有aggregate_field_info
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取汇总字段名称列表
     */
    @Test
    @DisplayName("测试获取汇总字段名称列表")
    void testGetSummaryFieldNames() {
        // 执行测试
        List<String> result = listComponentExt.getSummaryFieldNames();

        // 验证结果
        assertTrue(result.size() >= 1);
        assertTrue(result.contains("amount"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试是否启用选中布局
     */
    @Test
    @DisplayName("测试是否启用选中布局")
    void testIsEnableSelectedLayout() {
        // 执行测试
        boolean result = listComponentExt.isEnableSelectedLayout();

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试设置启用选中布局
     */
    @Test
    @DisplayName("测试设置启用选中布局")
    void testSetEnableSelectedLayout() {
        // 保存原始值
        boolean originalValue = listComponentExt.isEnableSelectedLayout();

        // 执行测试
        listComponentExt.setEnableSelectedLayout(false);
        boolean result = listComponentExt.isEnableSelectedLayout();

        // 验证结果
        assertFalse(result);

        // 恢复原始值
        listComponentExt.setEnableSelectedLayout(originalValue);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据名称获取视图信息
     */
    @Test
    @DisplayName("测试根据名称获取视图信息")
    void testGetViewInfoByName() {
        // 执行测试
        Optional<IViewComponentInfo> result = listComponentExt.getViewInfoByName("list_view");

        // 验证结果
        assertTrue(result.isPresent());
        assertEquals("list_view", result.get().getName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据不存在的名称获取视图信息
     */
    @Test
    @DisplayName("测试根据不存在的名称获取视图信息")
    void testGetViewInfoByNameWithNonExistent() {
        // 执行测试
        Optional<IViewComponentInfo> result = listComponentExt.getViewInfoByName("non_existent_view");

        // 验证结果
        assertFalse(result.isPresent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试设置和获取属性
     */
    @Test
    @DisplayName("测试设置和获取属性")
    void testAttributeOperations() {
        // 准备测试数据
        String key = "test_key";
        String value = "test_value";
        // 保存原始值（如果存在）
        Object originalValue = listComponentExt.getAttribute(key);

        // 执行测试
        listComponentExt.setAttribute(key, value);
        Object result = listComponentExt.getAttribute(key);

        // 验证结果
        assertEquals(value, result);

        // 恢复或清除属性
        if (originalValue != null) {
            listComponentExt.setAttribute(key, originalValue);
        } else {
            listComponentExt.setAttribute(key, null);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试从布局填充属性
     */
    @Test
    @DisplayName("测试从布局填充属性")
    void testFillAttributeFromLayout() {
        // 使用独立的组件进行测试，避免影响共享对象
        ListComponentExt testComponent = ListComponentExt.of(new HashMap<>());
        ILayout layout = mock(ILayout.class);
        when(layout.getEnableMobileLayout()).thenReturn(true);
        Map<String, Object> layoutStructure = new HashMap<>();
        layoutStructure.put("field_align", "center");
        when(layout.getLayoutStructure()).thenReturn(layoutStructure);

        // 执行测试
        testComponent.fillAttributeFromLayout(layout);

        // 验证结果
        assertEquals(true, testComponent.getAttribute(ILayout.ENABLE_MOBILE_LAYOUT));
        assertEquals("center", testComponent.getAttribute(LayoutStructure.FIELD_ALIGN));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试定义视图信息
     */
    @Test
    @DisplayName("测试定义视图信息")
    void testDefineViewInfo() {
        // 使用独立的组件进行测试，避免影响共享对象
        ListComponentExt testComponent = ListComponentExt.of(new HashMap<>());
        String viewName = "test_view";

        // 执行测试
        testComponent.defineViewInfo(viewName);
        boolean result = testComponent.isDefineViewInfo(viewName);

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试重复定义视图信息
     */
    @Test
    @DisplayName("测试重复定义视图信息")
    void testDefineViewInfoWithDuplicate() {
        // 使用独立的组件进行测试，避免影响共享对象
        ListComponentExt testComponent = ListComponentExt.of(new HashMap<>());
        String viewName = "duplicate_view";

        // 执行测试
        testComponent.defineViewInfo(viewName);
        testComponent.defineViewInfo(viewName); // 重复定义

        // 验证结果
        assertTrue(testComponent.isDefineViewInfo(viewName));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取标签页隐藏的主字段
     */
    @Test
    @DisplayName("测试获取标签页隐藏的主字段")
    void testGetTabHiddenMasterField() {
        // 准备测试数据
        Map<String, Object> componentMap = new HashMap<>();
        Map<String, Object> statisticsRender = new HashMap<>();
        statisticsRender.put("hidden", Arrays.asList("field1", "field2"));
        componentMap.put("statistics_render", statisticsRender);
        ListComponentExt component = ListComponentExt.of(componentMap);

        // 执行测试
        List<String> result = component.getTabHiddenMasterField();

        // 验证结果
        assertEquals(2, result.size());
        assertTrue(result.contains("field1"));
        assertTrue(result.contains("field2"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取标签页显示的主字段
     */
    @Test
    @DisplayName("测试获取标签页显示的主字段")
    void testGetTabShowMasterField() {
        // 准备测试数据
        Map<String, Object> componentMap = new HashMap<>();
        Map<String, Object> statisticsRender = new HashMap<>();
        statisticsRender.put("order", Arrays.asList("field3", "field4"));
        componentMap.put("statistics_render", statisticsRender);
        ListComponentExt component = ListComponentExt.of(componentMap);

        // 执行测试
        List<String> result = component.getTabShowMasterField();

        // 验证结果
        assertEquals(2, result.size());
        assertTrue(result.contains("field3"));
        assertTrue(result.contains("field4"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取引用对象API名称
     */
    @Test
    @DisplayName("测试获取引用对象API名称")
    void testGetRefObjectApiName() {
        // 准备测试数据
        Map<String, Object> componentMap = new HashMap<>();
        componentMap.put("ref_object_api_name", "test_object");
        ListComponentExt component = ListComponentExt.of(componentMap);

        // 执行测试
        String result = component.getRefObjectApiName();

        // 验证结果
        assertEquals("test_object", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取按钮信息（如果不存在则初始化）
     */
    @Test
    @DisplayName("测试获取按钮信息（如果不存在则初始化）")
    void testGetButtonInfoIfAbsentInit() {
        // 执行测试
        List<IListComponentInfo> result = listComponentExt.getButtonInfoIfAbsentInit();

        // 验证结果 - 至少有normal和batch
        assertTrue(result.size() >= 2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取场景信息或默认值
     */
    @Test
    @DisplayName("测试获取场景信息或默认值")
    void testGetSceneInfoOrDefault() {
        // 执行测试
        List<IScenesComponentInfo> result = listComponentExt.getSceneInfoOrDefault();

        // 验证结果
        assertTrue(result.size() >= 1);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取侧栏列表过滤项
     */
    @Test
    @DisplayName("测试获取侧栏列表过滤项")
    void testGetSideListFilterItems() {
        // 执行测试
        List<String> result = listComponentExt.getSideListFilterItems();

        // 验证结果
        assertEquals(2, result.size());
        assertTrue(result.contains("name"));
        assertTrue(result.contains("status"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取顶部列表过滤开关
     */
    @Test
    @DisplayName("测试获取顶部列表过滤开关")
    void testGetTopListFilterSwitch() {
        // 执行测试
        boolean result = listComponentExt.getTopListFilterSwitch();

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试移动端是否显示汇总
     */
    @Test
    @DisplayName("测试移动端是否显示汇总")
    void testIsDisplayTotalsFromMobile() {
        // 执行测试
        boolean result = listComponentExt.isDisplayTotalsFromMobile();

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试选中数据是否显示汇总
     */
    @Test
    @DisplayName("测试选中数据是否显示汇总")
    void testIsDisplayTotalsFromSelected() {
        // 执行测试
        boolean result = listComponentExt.isDisplayTotalsFromSelected();

        // 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试移除显示汇总配置
     */
    @Test
    @DisplayName("测试移除显示汇总配置")
    void testRemoveDisplayTotals() {
        // 使用独立的组件进行测试，避免影响共享对象
        Map<String, Object> componentMap = new HashMap<>();
        componentMap.put("display_totals", true);
        componentMap.put("selected_display_totals", false);
        ListComponentExt testComponent = ListComponentExt.of(componentMap);

        // 执行测试
        assertDoesNotThrow(() -> testComponent.removeDisplayTotals());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试相关列表是否为平铺模式
     */
    @Test
    @DisplayName("测试相关列表是否为平铺模式")
    void testIsRelatedListTileMode() {
        // 执行测试
        boolean result = listComponentExt.isRelatedListTileMode();

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取相关列表字段API名称
     */
    @Test
    @DisplayName("测试获取相关列表字段API名称")
    void testGetRelatedListFieldApiName() {
        // 执行测试
        String result = listComponentExt.getRelatedListFieldApiName();

        // 验证结果
        assertEquals("parent_id", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空值的国际化key获取
     */
    @Test
    @DisplayName("测试空值的国际化key获取")
    void testGetNameI18nKeyWithNull() {
        // 准备测试数据
        ListComponentExt component = ListComponentExt.of(new HashMap<>());

        // 执行测试
        String result = component.getNameI18nKey();

        // 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空值的启用选中布局
     */
    @Test
    @DisplayName("测试空值的启用选中布局")
    void testIsEnableSelectedLayoutWithNull() {
        // 准备测试数据
        ListComponentExt component = ListComponentExt.of(new HashMap<>());

        // 执行测试
        boolean result = component.isEnableSelectedLayout();

        // 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试移动端布局上下文模拟
     */
    @Test
    @DisplayName("测试移动端布局上下文模拟")
    void testHideTotalsMobileByPageType() {
        try (MockedStatic<LayoutContext> mockedLayoutContext = mockStatic(LayoutContext.class)) {
            // 模拟移动端布局
            mockedLayoutContext.when(LayoutContext::isMobileLayout).thenReturn(true);

            // 执行测试
            List<ISummaryComponentInfo> result = listComponentExt.getSummaryComponentInfoByPageType("list");

            // 验证结果
            assertTrue(result.size() >= 0);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取空属性的情况
     */
    @Test
    @DisplayName("测试获取空属性的情况")
    void testGetAttributeWithNonExistent() {
        // 执行测试
        Object result = listComponentExt.getAttribute("non_existent_key");

        // 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试默认顶部列表过滤开关
     */
    @Test
    @DisplayName("测试默认顶部列表过滤开关")
    void testGetTopListFilterSwitchWithDefault() {
        // 准备测试数据
        ListComponentExt component = ListComponentExt.of(new HashMap<>());

        // 执行测试
        boolean result = component.getTopListFilterSwitch();

        // 验证结果 - 默认值
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空聚合字段信息时的启用聚合功能
     */
    @Test
    @DisplayName("测试空聚合字段信息时的启用聚合功能")
    void testEnableAggregateWithEmpty() {
        // 准备测试数据
        ListComponentExt component = ListComponentExt.of(new HashMap<>());

        // 执行测试
        boolean result = component.enableAggregate();

        // 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空汇总信息时的获取汇总字段名称
     */
    @Test
    @DisplayName("测试空汇总信息时的获取汇总字段名称")
    void testGetSummaryFieldNamesWithEmpty() {
        // 准备测试数据
        ListComponentExt component = ListComponentExt.of(new HashMap<>());

        // 执行测试
        List<String> result = component.getSummaryFieldNames();

        // 验证结果
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空视图信息时的根据名称获取视图信息
     */
    @Test
    @DisplayName("测试空视图信息时的根据名称获取视图信息")
    void testGetViewInfoByNameWithEmptyViewInfo() {
        // 准备测试数据
        ListComponentExt component = ListComponentExt.of(new HashMap<>());

        // 执行测试
        Optional<IViewComponentInfo> result = component.getViewInfoByName("any_view");

        // 验证结果
        assertFalse(result.isPresent());
    }
}
