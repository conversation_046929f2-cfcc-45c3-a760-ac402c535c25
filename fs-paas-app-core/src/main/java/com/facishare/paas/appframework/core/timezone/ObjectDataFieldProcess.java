package com.facishare.paas.appframework.core.timezone;

import com.facishare.common.RefectionUtil;
import com.facishare.paas.appframework.core.timezone.annotation.ObjectDataField;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * create by z<PERSON><PERSON> on 2021/06/15
 */
@Slf4j
public class ObjectDataFieldProcess {

    private static final Map<ObjectDataField.Type, ObjectDataFieldConvert> objectDataFieldConvert;

    static {
        Map<ObjectDataField.Type, ObjectDataFieldConvert> convertMap = Maps.newHashMap();
        convertMap.put(SingleObjectDataFieldConvert.getInstance().getType(), SingleObjectDataFieldConvert.getInstance());
        convertMap.put(ListObjectDataFieldConvert.getInstance().getType(), ListObjectDataFieldConvert.getInstance());
        convertMap.put(ListMapObjectDataFieldConvert.getInstance().getType(), ListMapObjectDataFieldConvert.getInstance());
        convertMap.put(MapMapObjectDataFieldConvert.getInstance().getType(), MapMapObjectDataFieldConvert.getInstance());
        objectDataFieldConvert = ImmutableMap.copyOf(convertMap);
    }

    public static void convert2SystemZone(Object value, Function<String, IObjectDescribe> function) {
        Objects.requireNonNull(function);
        convert(value, true, 0, function);
    }

    public static void convert2CustomZone(Object value, Function<String, IObjectDescribe> function) {
        Objects.requireNonNull(function);
        convert(value, false, 0, function);
    }

    private static void convert(Object value, boolean toSystem, int count, Function<String, IObjectDescribe> function) {
        if (++count > 3) {
            return;
        }
        Objects.requireNonNull(function);
        if (Objects.isNull(value)) {
            return;
        }

        Field[] fields = RefectionUtil.getDeclaredAndInheritedFields(value.getClass(), false);

        for (Field field : fields) {
            field.setAccessible(true);
            ObjectDataField annotation = field.getAnnotation(ObjectDataField.class);
            Object fieldValue = getFieldValue(value, field);
            if (Objects.nonNull(annotation)) {
                Object newValue = convertValue(annotation.value(), fieldValue, toSystem, function);
                setFieldValue(value, field, newValue);
            } else {
                if (fieldValue instanceof Iterable) {
                    for (Object o : ((Iterable) fieldValue)) {
                        convert(o, toSystem, count, function);
                    }
                } else {
                    convert(fieldValue, toSystem, count, function);
                }
            }
        }

    }

    private static Object convertValue(ObjectDataField.Type type, Object oldValue, boolean toSystem, Function<String, IObjectDescribe> function) {
        try {
            ObjectDataFieldConvert convert = ObjectDataFieldProcess.objectDataFieldConvert.get(type);
            return toSystem ? convert.convert2SystemZone(oldValue, function)
                    : convert.convert2CustomZone(oldValue, function);
        } catch (RuntimeException e) {
            log.error("convertValue fail! type:{}, value:{}", type, oldValue, e);
            throw e;
        }
    }

    private static void setFieldValue(Object obj, Field field, Object value) {
        try {
            field.set(obj, value);
        } catch (IllegalAccessException e) {
            log.error("setFieldValue fail, obj:{},fieldName:{},value:{}", obj, field.getName(), value, e);
            throw new IllegalArgumentException();
        }
    }

    private static Object getFieldValue(Object obj, Field field) {
        try {
            return field.get(obj);
        } catch (IllegalAccessException e) {
            log.error("getFieldValue fail, fieldName:{},obj:{}", field.getName(), obj, e);
            throw new IllegalArgumentException();
        }
    }
}
