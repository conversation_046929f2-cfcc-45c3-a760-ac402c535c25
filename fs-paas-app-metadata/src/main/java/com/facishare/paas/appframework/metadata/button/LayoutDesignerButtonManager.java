package com.facishare.paas.appframework.metadata.button;

import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.privilege.model.DataPrivilegeProvider;
import com.facishare.paas.appframework.privilege.model.RemoteDataPrivilegeProvider;
import joptsimple.internal.Strings;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * create by zhao<PERSON> on 2020/03/07
 */
@Component
public class LayoutDesignerButtonManager implements ApplicationContextAware {

    private Map<String, LayoutDesignerButtonProvider> providerMap = Collections.emptyMap();
    @Autowired
    private DefaultLayoutDesignerButtonProvider defaultLayoutDesignerButtonProvider;
    @Autowired
    private LayoutDesignerButtonProviderProxy proxy;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        initLayoutDesignerButtonProvider(applicationContext);
    }

    private void initLayoutDesignerButtonProvider(ApplicationContext applicationContext) {
        Map<String, LayoutDesignerButtonProvider> beans = applicationContext.getBeansOfType(LayoutDesignerButtonProvider.class);
        providerMap = beans.values().stream()
                .filter(provider -> !Strings.isNullOrEmpty(provider.getApiName()))
                .collect(Collectors.toMap(LayoutDesignerButtonProvider::getApiName, it -> it));
    }

    public LayoutDesignerButtonProvider getProvider(String apiName) {
        if(ObjectDescribeExt.isCustomObject(apiName)) {
            return defaultLayoutDesignerButtonProvider;
        }

        LayoutDesignerButtonProvider provider = providerMap.getOrDefault(apiName, null);
        if(Objects.isNull(provider)) {
            return getRemoteProvider(apiName);
        }
        return provider;
    }

    public LayoutDesignerButtonProvider getLocalProvider(String apiName) {
        return providerMap.getOrDefault(apiName, defaultLayoutDesignerButtonProvider);
    }

    private LayoutDesignerButtonProvider getRemoteProvider(String apiName) {
        return RemoteLayoutDesignerButtonProvider.builder()
                .proxy(proxy)
                .describeApiName(apiName)
                .build();
    }
}
