package com.facishare.paas.appframework.privilege.dto;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;
import java.util.Objects;

import static com.facishare.paas.appframework.privilege.DataPrivilegeService.*;

/**
 * create by zhao<PERSON> on 2019/08/25
 */
@Data
@Builder
public class DataSharing {
    private User user;
    private int permissionType;
    private RangesExt sourceRanges;
    private RangesExt targetRanges;
    private List<String> describeApiNameList;
    private Integer receiveDeptCascade;
    private int basedType;

    public List<EntitySharePojo> generateEntityShareList() {
        List<EntitySharePojo> resultList = Lists.newArrayList();
        resultList.addAll(generateSourceShareList(USER_GROUP_TYPE, getSourceRanges().getUserGroup()));
        resultList.addAll(generateSourceShareList(ROLE_TYPE, getSourceRanges().getRole()));
        resultList.addAll(generateSourceShareList(USER_TYPE, getSourceRanges().getUser()));
        resultList.addAll(generateSourceShareList(DEPART_TYPE, getSourceRanges().getDepartment()));
        resultList.addAll(generateSourceShareList(ORGANIZATION_TYPE, getSourceRanges().getShareRuleOrgIds()));
        return resultList;
    }

    private List<EntitySharePojo> generateSourceShareList(int shareType, List<String> sourceIDList) {
        List<EntitySharePojo> shareList = Lists.newArrayList();
        if (CollectionUtils.empty(sourceIDList)) {
            return shareList;
        }

        for (String sourceId : sourceIDList) {
            if (ObjectUtils.isEmpty(sourceId)){
                continue;
            }
            for (String apiName : describeApiNameList) {
                List<EntitySharePojo> userSharaList = generateTargetShareList(getTargetRanges().getUser(), shareType, USER_TYPE, sourceId, apiName);
                List<EntitySharePojo> circleSharaList = generateTargetShareList(getTargetRanges().getDepartment(), shareType, DEPART_TYPE, sourceId, apiName, getReceiveDeptCascade());
                List<EntitySharePojo> organizationSharaList = generateTargetShareList(getTargetRanges().getShareRuleOrgIds(), shareType, ORGANIZATION_TYPE, sourceId, apiName, getReceiveDeptCascade());
                List<EntitySharePojo> groupSharaList = generateTargetShareList(getTargetRanges().getUserGroup(), shareType, USER_GROUP_TYPE, sourceId, apiName);
                List<EntitySharePojo> roleSharaList = generateTargetShareList(getTargetRanges().getRole(), shareType, ROLE_TYPE, sourceId, apiName);
                List<EntitySharePojo> outTenantShareList = generateTargetOutTenantShareList(getTargetRanges().getOutTenantIds(),shareType,OUT_TENANT_TYPE,sourceId,apiName);
                List<EntitySharePojo> outTenantGroupShareList = generateTargetGroupShareList(getTargetRanges().getOutTenantGroups(),shareType, OUT_TENANT_GROUP_TYPE,sourceId,apiName);
                List<EntitySharePojo> outUserShareList = generateTargetOutUserShareList(getTargetRanges().getOutUsersList(),shareType,USER_TYPE,sourceId,apiName);
                List<EntitySharePojo> outUserRuleShareList = generateTargetOutUserRuleShareList(getTargetRanges().getOutRoles(),shareType,ROLE_TYPE,sourceId,apiName);
                shareList.addAll(userSharaList);
                shareList.addAll(circleSharaList);
                shareList.addAll(organizationSharaList);
                shareList.addAll(groupSharaList);
                shareList.addAll(roleSharaList);
                shareList.addAll(outTenantShareList);
                shareList.addAll(outTenantGroupShareList);
                shareList.addAll(outUserShareList);
                shareList.addAll(outUserRuleShareList);
            }
        }
        return shareList;
    }

    private List<EntitySharePojo> generateTargetOutTenantShareList(List<String> outTenantIds, int shareType, Integer receiveType, String sourceId, String apiName) {
        List<EntitySharePojo> shareList = Lists.newArrayList();
        if (CollectionUtils.empty(outTenantIds)) {
            return shareList;
        }
        for (String targetID : outTenantIds) {
            if (ObjectUtils.isEmpty(sourceId)){
                continue;
            }

            EntitySharePojo entitySharePojo = EntitySharePojo.builder()
              .tenantId(user.getTenantId())
              .appId(DefObjConstants.PACKAGE_NAME_CRM)
              .creator(user.getUserId())
              .entityId(apiName)
              .permission(permissionType)
              .shareId(sourceId)
              .shareType(shareType)
              .receiveId(targetID)
              .receiveType(Objects.equals(ALL_OUT_TENANT_ID,targetID) ? ALL_OUT_TENANT_TYPE : receiveType)
              .status(1)
              .receiveTenantId(targetID)
              .basedType(basedType)
              .build();

            shareList.add(entitySharePojo);
        }
        return shareList;

    }

    private List<EntitySharePojo> generateTargetGroupShareList(List<String> outTenantGroups, int shareType, Integer receiveType, String sourceId, String apiName) {
        List<EntitySharePojo> shareList = Lists.newArrayList();
        if (CollectionUtils.empty(outTenantGroups)) {
            return shareList;
        }
        for (String targetID : outTenantGroups) {
            if (ObjectUtils.isEmpty(sourceId)){
                continue;
            }

            EntitySharePojo entitySharePojo = EntitySharePojo.builder()
              .tenantId(user.getTenantId())
              .appId(DefObjConstants.PACKAGE_NAME_CRM)
              .creator(user.getUserId())
              .entityId(apiName)
              .permission(permissionType)
              .shareId(sourceId)
              .shareType(shareType)
              .receiveId(targetID)
              .receiveType(receiveType)
              .status(1)
              .receiveTenantId(ALL_OUT_TENANT_ID)
              .basedType(basedType)
              .build();

            shareList.add(entitySharePojo);
        }
        return shareList;

    }

    private List<EntitySharePojo> generateTargetOutUserRuleShareList(List<String> outRoles, int shareType, Integer receiveType, String sourceId, String apiName) {
        List<EntitySharePojo> shareList = Lists.newArrayList();
        if (CollectionUtils.empty(outRoles)) {
            return shareList;
        }
        for (String outRule : outRoles) {
            if (ObjectUtils.isEmpty(outRule)){
                continue;
            }

            EntitySharePojo entitySharePojo = EntitySharePojo.builder()
              .tenantId(user.getTenantId())
              .appId(DefObjConstants.PACKAGE_NAME_CRM)
              .creator(user.getUserId())
              .entityId(apiName)
              .permission(permissionType)
              .shareId(sourceId)
              .shareType(shareType)
              .receiveId(outRule)
              .receiveTenantId(ALL_OUT_TENANT_ID)
              .receiveType(receiveType)
              .status(1)
              .basedType(basedType)
              .build();

            shareList.add(entitySharePojo);
        }
        return shareList;

    }

    private List<EntitySharePojo> generateTargetOutUserShareList(List<RangeOutUser> outUsersList, int shareType, int receiveType, String sourceId, String apiName){
        List<EntitySharePojo> shareList = Lists.newArrayList();
        if (CollectionUtils.empty(outUsersList)) {
            return shareList;
        }
        for (RangeOutUser rangeOutUsers : outUsersList) {
            if (ObjectUtils.isEmpty(rangeOutUsers.getOutTenantId()) || ObjectUtils.isEmpty(rangeOutUsers.getOutUserId())){
                continue;
            }

            EntitySharePojo entitySharePojo = EntitySharePojo.builder()
              .tenantId(user.getTenantId())
              .appId(DefObjConstants.PACKAGE_NAME_CRM)
              .creator(user.getUserId())
              .entityId(apiName)
              .permission(permissionType)
              .shareId(sourceId)
              .shareType(shareType)
              .receiveId(rangeOutUsers.getOutUserId())
              .receiveTenantId(rangeOutUsers.getOutTenantId())
              .receiveType(receiveType)
              .status(1)
              .basedType(basedType)
              .build();

            shareList.add(entitySharePojo);
        }
        return shareList;
    }

    private List<EntitySharePojo> generateTargetShareList(List<String> targetIDList, int shareType, int receiveType, String sourceId, String apiName, Integer receiveCascade) {
        List<EntitySharePojo> shareList = Lists.newArrayList();
        if (CollectionUtils.empty(targetIDList)) {
            return shareList;
        }
        for (String targetID : targetIDList) {
            EntitySharePojo entitySharePojo = EntitySharePojo.builder()
                    .tenantId(user.getTenantId())
                    .appId(DefObjConstants.PACKAGE_NAME_CRM)
                    .creator(user.getUserId())
                    .entityId(apiName)
                    .permission(permissionType)
                    .shareId(sourceId)
                    .shareType(shareType)
                    .receiveId(targetID)
                    .receiveType(receiveType)
                    .status(1)
                    .receiveDeptCascade(receiveCascade)
                    .basedType(basedType)
                    .build();

            shareList.add(entitySharePojo);
        }
        return shareList;
    }

    private List<EntitySharePojo> generateTargetShareList(List<String> targetIDList, int shareType, int receiveType, String sourceId, String apiName) {
        List<EntitySharePojo> shareList = Lists.newArrayList();
        if (CollectionUtils.empty(targetIDList)) {
            return shareList;
        }


        for (String targetID : targetIDList) {
            EntitySharePojo entitySharePojo = EntitySharePojo.builder()
                    .tenantId(user.getTenantId())
                    .appId(DefObjConstants.PACKAGE_NAME_CRM)
                    .creator(user.getUserId())
                    .entityId(apiName)
                    .permission(permissionType)
                    .shareId(sourceId)
                    .shareType(shareType)
                    .receiveId(targetID)
                    .receiveType(receiveType)
                    .basedType(basedType)
                    .status(1)
                    .build();

            shareList.add(entitySharePojo);
        }
        return shareList;
    }



}
