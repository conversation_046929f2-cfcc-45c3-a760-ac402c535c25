package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.User
import spock.lang.Specification

class OrgServiceTest extends Specification {
    RequestContext requestContext = RequestContext.builder()
            .tenantId("74255")
            .user(new User("74255", "1000"))
            .build()

    ServiceContext serviceContext = new ServiceContext(requestContext, "user", "find_main_dept_info_by_user_id")

    OrgUserService orgUserService

    def "test findMainDeptInfoByUserId"() {
        expect:
        1 == 1
        /*
        given:
        FindMainDeptInfoByUserId.Arg arg = FindMainDeptInfoByUserId.Arg.builder().userIds(Sets.newHashSet("1000")).build()
        when:
        def result = orgUserService.findMainDeptInfoByUserId(arg, serviceContext)
        then:
        print(result)
        */
    }
}
