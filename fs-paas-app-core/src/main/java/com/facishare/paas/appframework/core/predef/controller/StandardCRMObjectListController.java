package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.WhatList;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.functions.model.RelatedObject;
import com.fxiaoke.functions.model.RelatedObjectData;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.openapi.Utils.ACTIVE_RECORD_API_NAME;

public class StandardCRMObjectListController extends PreDefineController<StandardCRMObjectListController.Arg, StandardCRMObjectListController.Result> {

    @Override
    protected Result doService(Arg arg) {
        List<IObjectDescribe> describeList;
        String functionAPIName = getFunctionAPIName();
        if (StringUtils.isNotEmpty(functionAPIName)) {
            List<String> apiNames = getObjectApiNamesByFunction(functionAPIName);
            List<IObjectDescribe> describes = serviceFacade.findDescribeListWithoutFields(controllerContext.getUser().getTenantId(), apiNames);
            List<IObjectDescribe> describeListFiltered = serviceFacade.filterDescribesWithActionCode(controllerContext.getUser(), describes, ObjectAction.VIEW_LIST.getActionCode());
            describeList = describeListFiltered.stream()
                    .filter(describe -> BooleanUtils.isTrue(describe.isActive()))
                    .collect(Collectors.toList());
        } else {
            describeList = serviceFacade.findDescribeByPrivilegeAndModule(controllerContext.getUser(),
                    ObjectAction.VIEW_LIST.getActionCode(), !arg.isIncludeSystemObj(),
                    !arg.isIncludeUnActived(), false, arg.isAsc());
            describeList = CollectionUtils.sortByGivenOrder(describeList, AppFrameworkConfig.getWhatListObjectSortList(), IObjectDescribe::getApiName);
        }
        describeList.removeIf(a -> Objects.equals(a.getApiName(), ObjectAPINameMapping.ReturnedGoodsInvoiceProduct.getApiName()));
        if (Objects.equals(ACTIVE_RECORD_API_NAME, controllerContext.getObjectApiName()) && AppFrameworkConfig.isOptionalFeaturesSupport(controllerContext.getTenantId())) {
            Map<String, OptionalFeaturesSwitchDTO> featuresSwitchDTOMap = infraServiceFacade.batchQueryOptionalFeaturesSwitch(controllerContext.getTenantId(), describeList);
            describeList.removeIf(describe -> {
                OptionalFeaturesSwitchDTO optionalFeaturesSwitchDTO = featuresSwitchDTOMap.get(describe.getApiName());
                return Objects.nonNull(optionalFeaturesSwitchDTO) && !optionalFeaturesSwitchDTO.getIsFollowUpDynamicEnabled();
            });
        }
        List<StandardCRMObjectListController.ObjectInfo> objectInfoList =
                describeList.stream().map(x -> StandardCRMObjectListController.ObjectInfo.of(x.getApiName(), x.getDisplayName()))
                        .collect(Collectors.toList());

        return StandardCRMObjectListController.Result.builder()
                .objectList(objectInfoList)
                .build();
    }

    private String getFunctionAPIName() {
        IObjectDescribe objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), controllerContext.getObjectApiName());
        List<WhatList> whatListFields = ObjectDescribeExt.of(objectDescribe).getWhatListFields();

        return CollectionUtils.nullToEmpty(whatListFields).stream()
                .findFirst()
                .map(whatList -> FieldDescribeExt.of(whatList).getFuncApiNameFromWhatListField())
                .orElse(null);
    }

    private List<String> getObjectApiNamesByFunction(String funcApiName) {
        IUdefFunction function = serviceFacade.getFunctionLogicService().findUDefFunction(controllerContext.getUser(), funcApiName, controllerContext.getObjectApiName());
        if (Objects.isNull(function)) {
            throw new ValidateException(I18N.text(I18NKey.NOT_FIND_FUNCTION, funcApiName));
        }
        infraServiceFacade.fillQuoteValueVirtualField(controllerContext.getUser(), ObjectDataExt.of(arg.getObjectData()), Maps.newHashMap());

        RunResult runResult = serviceFacade.getFunctionLogicService().executeUDefFunction(controllerContext.getUser(), function, null, ObjectDataExt.of(arg.getObjectData()), Maps.newHashMap());
        if (!runResult.isSuccess()) {
            throw new FunctionException(runResult.getErrorInfo());
        }
        Object functionResult = runResult.getFunctionResult();
        if ("RelatedObject".equals(runResult.getReturnType()) && Objects.nonNull(functionResult)) {
            RelatedObject relatedObject = JSON.parseObject(JSON.toJSONString(functionResult), RelatedObject.class);
            List<RelatedObjectData> objectList = relatedObject.getObjectList();
            return CollectionUtils.nullToEmpty(objectList).stream()
                    .map(RelatedObjectData::getApiName)
                    .distinct()
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Data
    public static class Arg {
        @JsonProperty("isIncludeSystemObj")
        @SerializedName("isIncludeSystemObj")
        private boolean isIncludeSystemObj = true;

        @JsonProperty("isIncludeFieldDescribe")
        @SerializedName("isIncludeFieldDescribe")
        private boolean isIncludeFieldDescribe = false;

        @JsonProperty("isAsc")
        @SerializedName("isAsc")
        private boolean isAsc = false;

        @JsonProperty("isIncludeUnActived")
        @SerializedName("isIncludeUnActived")
        private boolean isIncludeUnActived = false;

        @JsonProperty("objectData")
        @SerializedName("objectData")
        private ObjectDataDocument objectData;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private List<StandardCRMObjectListController.ObjectInfo> objectList;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor(staticName = "of")
    public static class ObjectInfo {
        private String apiName;
        private String displayName;
    }
}
