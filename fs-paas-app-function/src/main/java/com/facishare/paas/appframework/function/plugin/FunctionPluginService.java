package com.facishare.paas.appframework.function.plugin;

import com.facishare.paas.appframework.core.model.User;
import lombok.Builder;
import lombok.Getter;

import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/7/5
 */
public interface FunctionPluginService {

    <T> T executeFuncMethod(FunctionPluginContext<T> context, String functionApiName, String methodName);

    class FunctionPluginContext<T> {
        @Getter
        private final User user;
        private final Supplier<List<String>> argProcess;
        private final Function<String, T> resultProcess;

        @Builder
        private FunctionPluginContext(User user, Supplier<List<String>> argProcess, Function<String, T> resultProcess) {
            this.user = user;
            this.argProcess = argProcess;
            this.resultProcess = resultProcess;
        }

        public String getTenantId() {
            return user.getTenantId();
        }

        public String getUserId() {
            return user.getUserId();
        }

        public List<String> getArg() {
            return argProcess.get();
        }

        public T getResult(String body) {
            return resultProcess.apply(body);
        }
    }
}
