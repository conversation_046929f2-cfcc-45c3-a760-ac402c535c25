package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GenerateByAI
 * 测试BaseFieldConverter抽象类的通用逻辑
 */
class BaseFieldConverterTest extends BaseDataConverterTest {

  private TestFieldConverter converter;

  @BeforeEach
  void setUp() {
    super.setUp();
    converter = new TestFieldConverter();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法当字段值为null时返回空字符串
   */
  @Test
  @DisplayName("测试convertFieldData - 字段值为null返回空字符串")
  void testConvertFieldData_NullValue() {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createTextFieldDescribe("test_field");
    IObjectData objectData = createObjectData("test_field", null);

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, fieldDescribe, context);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法当字段值为空字符串时返回空字符串
   */
  @ParameterizedTest
  @MethodSource("provideEmptyStringTestData")
  @DisplayName("测试convertFieldData - 空字符串值返回空字符串")
  void testConvertFieldData_EmptyStringValue(Object value) {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createTextFieldDescribe("test_field");
    IObjectData objectData = createObjectData("test_field", value);

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, fieldDescribe, context);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * 提供空字符串测试数据
   */
  private static Stream<Arguments> provideEmptyStringTestData() {
    return Stream.of(
        Arguments.of("")  // 只有真正的空字符串会被Strings.isNullOrEmpty()判断为空
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法当字段值有效时调用convert方法
   */
  @Test
  @DisplayName("测试convertFieldData - 有效值调用convert方法")
  void testConvertFieldData_ValidValue() {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createTextFieldDescribe("test_field");
    IObjectData objectData = createObjectData("test_field", "test_value");

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, fieldDescribe, context);

    // 验证结果
    assertEquals("converted_test_value", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理不同类型的有效值
   */
  @ParameterizedTest
  @MethodSource("provideValidValueTestData")
  @DisplayName("测试convertFieldData - 不同类型有效值")
  void testConvertFieldData_DifferentValidValues(Object value, String expectedPrefix) {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createTextFieldDescribe("test_field");
    IObjectData objectData = createObjectData("test_field", value);

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, fieldDescribe, context);

    // 验证结果
    assertTrue(result.startsWith("converted_"));
    assertTrue(result.contains(expectedPrefix));
  }

  /**
   * 提供有效值测试数据
   */
  private static Stream<Arguments> provideValidValueTestData() {
    return Stream.of(
        Arguments.of("text_value", "text_value"),
        Arguments.of(123, "123"),
        Arguments.of(123.45, "123.45"),
        Arguments.of(true, "true"),
        Arguments.of(Arrays.asList("item1", "item2"), "[item1, item2]")
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法当字段在对象数据中不存在时返回空字符串
   */
  @Test
  @DisplayName("测试convertFieldData - 字段不存在返回空字符串")
  void testConvertFieldData_FieldNotExists() {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createTextFieldDescribe("non_existing_field");
    IObjectData objectData = createObjectData("other_field", "some_value");

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, fieldDescribe, context);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法传递正确的参数给convert方法
   */
  @Test
  @DisplayName("测试convertFieldData - 参数传递验证")
  void testConvertFieldData_ParameterPassing() {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createNumberFieldDescribe("number_field");
    IObjectData objectData = createObjectData("number_field", 42);

    // 创建验证参数的转换器
    ParameterVerifyingConverter verifyingConverter = new ParameterVerifyingConverter();

    // 执行被测试方法
    String result = verifyingConverter.convertFieldData(objectData, fieldDescribe, context);

    // 验证结果
    assertEquals("verified", result);
    assertTrue(verifyingConverter.wasConvertCalled());
    assertEquals(objectData, verifyingConverter.getReceivedObjectData());
    assertEquals(fieldDescribe, verifyingConverter.getReceivedFieldDescribe());
    assertEquals(context, verifyingConverter.getReceivedContext());
  }

  /**
   * 测试用的BaseFieldConverter实现类
   */
  private static class TestFieldConverter extends BaseFieldConverter {
    @Override
    protected String convert(IObjectData objectData, IFieldDescribe fieldDescribe, DataConvertContext context) {
      return "converted_" + objectData.get(fieldDescribe.getApiName());
    }

    @Override
    public List<String> getSupportedFieldTypes() {
      return Arrays.asList(IFieldType.TEXT);
    }
  }

  /**
   * 用于验证参数传递的转换器
   */
  private static class ParameterVerifyingConverter extends BaseFieldConverter {
    private boolean convertCalled = false;
    private IObjectData receivedObjectData;
    private IFieldDescribe receivedFieldDescribe;
    private DataConvertContext receivedContext;

    @Override
    protected String convert(IObjectData objectData, IFieldDescribe fieldDescribe, DataConvertContext context) {
      this.convertCalled = true;
      this.receivedObjectData = objectData;
      this.receivedFieldDescribe = fieldDescribe;
      this.receivedContext = context;
      return "verified";
    }

    @Override
    public List<String> getSupportedFieldTypes() {
      return Arrays.asList(IFieldType.NUMBER);
    }

    public boolean wasConvertCalled() {
      return convertCalled;
    }

    public IObjectData getReceivedObjectData() {
      return receivedObjectData;
    }

    public IFieldDescribe getReceivedFieldDescribe() {
      return receivedFieldDescribe;
    }

    public DataConvertContext getReceivedContext() {
      return receivedContext;
    }
  }
}
