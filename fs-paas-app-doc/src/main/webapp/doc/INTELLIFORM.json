{"swagger": "2.0", "info": {"title": "AppFrameWork REST API", "description": "PaaS 应用框架REST API", "version": "1.0"}, "host": "************:8234", "schemes": ["http"], "basePath": "/API/v1/inner/rest/intelliForm", "produces": ["application/json"], "paths": {"/intelliForm/object_data/change_owner": {"post": {"tags": ["Form API"], "summary": "change owner", "description": "更换负责人", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": false, "schema": {"$ref": "#/definitions/ChangeOwnerArg"}}], "responses": {"200": {"description": "successful"}}}}, "/intelliForm/object_data/create": {"post": {"tags": ["Form API"], "summary": "create object data", "description": "创建数据", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": false, "schema": {"$ref": "#/definitions/CreateDataArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/CreateDataResult"}}}}}, "/intelliForm/object_data/find_by_id": {"post": {"tags": ["Form API"], "summary": "find object data by dataId", "description": "根据数据ID查找对象详情", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": false, "schema": {"$ref": "#/definitions/FindByIdArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/FindByIdResult"}}}}}, "/intelliForm/object_data/find_list": {"post": {"tags": ["Form API"], "summary": "find object data list", "description": "分页查询数据列表", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": false, "schema": {"$ref": "#/definitions/FindDataListArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/FindDataListResult"}}}}}, "/intelliForm/data_permission/update_common_permission": {"post": {"tags": ["Form API"], "summary": "update data permission rest API", "description": "更新数据权限接口", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": false, "schema": {"$ref": "#/definitions/UpdateCommonPermissionArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/UpdateCommonPermissionResult"}}}}}, "/intelliForm/object_describe/config_common_privilege": {"post": {"tags": ["Form API"], "summary": "config data permission rest API", "description": "设置权限接口", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": false, "schema": {"$ref": "#/definitions/ConfigCommonPrivilegeArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/ConfigCommonPrivilegeResult"}}}}}, "/intelliForm/share_rule/add_or_update": {"post": {"tags": ["Form API"], "summary": "add or update share rules", "description": "修改共享规则", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": false, "schema": {"$ref": "#/definitions/AddOrUpdateShareRuleArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/AddOrUpdateShareRuleResult"}}}}}, "/intelliForm/share_rule/delete": {"post": {"tags": ["Form API"], "summary": "delete share rules", "description": "删除共享规则", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": false, "schema": {"$ref": "#/definitions/DelShareRuleArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/DelShareRuleResult"}}}}}, "/intelliForm/share_rule/query": {"post": {"tags": ["Form API"], "summary": "query share rules", "description": "查找共享规则", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": false, "schema": {"$ref": "#/definitions/GetShareRulesArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/GetShareRulesResult"}}}}}, "/intelliForm/object_describe/find_valid_record_type": {"post": {"tags": ["Form API"], "summary": "find valid record type", "description": "查询有效业务类型", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": false, "schema": {"$ref": "#/definitions/FindRecordTypeArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/FindRecordTypeResult"}}}}}, "/intelliForm/object_describe/add_field": {"post": {"tags": ["Form API"], "summary": "add field", "description": "添加字段接口", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": false, "schema": {"$ref": "#/definitions/AddFieldArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/AddFieldResult"}}}}}, "/intelliForm/object_describe/create": {"post": {"tags": ["Form API"], "summary": "create object describe", "description": "创建对象描述接口", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": false, "schema": {"$ref": "#/definitions/CreateArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/CreateResult"}}}}}, "/intelliForm/object_describe/detail": {"post": {"tags": ["Form API"], "summary": "find object describe detail", "description": "查询对象描述详情", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": false, "schema": {"$ref": "#/definitions/DetailArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/DetailResult"}}}}}, "/intelliForm/object_describe/disable": {"post": {"tags": ["Form API"], "summary": "disable object", "description": "禁用对象", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": false, "schema": {"$ref": "#/definitions/DisableArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/DisableResult"}}}}}, "/intelliForm/object_describe/find_by_id": {"post": {"tags": ["Form API"], "summary": "find by id", "description": "根据ID查找对象", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": false, "schema": {"$ref": "#/definitions/FindDescByIdArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/FindDescByIdResult"}}}}}, "/intelliForm/object_describe/page_list": {"post": {"tags": ["Form API"], "summary": "find object describe", "description": "查询对象描述", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": false, "schema": {"$ref": "#/definitions/FindByPageArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/FindByPageResult"}}}}}, "/intelliForm/object_describe/replace": {"post": {"tags": ["Form API"], "summary": "replace object", "description": "替换对象", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": false, "schema": {"$ref": "#/definitions/ReplaceArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/ReplaceResult"}}}}}}, "definitions": {"ChangeOwnerArg": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/ChangeOwnerData"}}, "describe_api_name": {"type": "string"}, "old_owner_strategy": {"type": "string"}, "old_owner_team_member_role": {"type": "string"}, "old_owner_team_member_permission_type": {"type": "string"}}}, "ChangeOwnerData": {"type": "object", "properties": {"object_data_id": {"type": "string"}, "owner_id": {"type": "array", "items": {"type": "string"}}}}, "CreateDataArg": {"type": "object", "properties": {"object_data": {"type": "object"}}}, "CreateDataResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"object_data": {"type": "object"}}}}}, "FindByIdArg": {"type": "object", "properties": {"object_data_id": {"type": "string"}, "describe_api_name": {"type": "string"}, "layout_id": {"type": "string"}}}, "FindByIdResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"object_data": {"type": "object"}, "object_describe": {"type": "object"}, "layout": {"type": "object"}}}}}, "FindDataListArg": {"type": "object", "properties": {"pageSize": {"type": "integer"}, "describe_api_name": {"type": "string"}, "lastTime": {"type": "integer"}}}, "FindDataListResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"object_data_list": {"type": "array", "items": {"type": "object"}}}}}}, "UpdateCommonPermissionArg": {"type": "object", "properties": {"ObjectDataPermissionInfos": {"type": "array", "items": {"$ref": "#/definitions/ObjectDataPermissionInfo"}}}}, "ObjectDataPermissionInfo": {"type": "object", "properties": {"ObjectDescribeApiName": {"type": "string"}, "ObjectDescribeDisplayName": {"type": "string"}, "PermissionType": {"type": "string"}}}, "UpdateCommonPermissionResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"success": {"type": "boolean"}}}}}, "ConfigCommonPrivilegeArg": {"type": "object", "properties": {"describe_api_name": {"type": "string"}}}, "ConfigCommonPrivilegeResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"success": {"type": "boolean"}}}}}, "AddOrUpdateShareRuleArg": {"type": "object", "properties": {"ObjectDescribeApiNames": {"type": "array", "items": {"type": "string"}}, "PermissionType": {"type": "integer"}, "SourceCircleIDs": {"type": "array", "items": {"type": "integer"}}, "SourceEmployeeIDs": {"type": "array", "items": {"type": "integer"}}, "SourceUserGroupIDs": {"type": "array", "items": {"type": "string"}}, "TargetCircleIDs": {"type": "array", "items": {"type": "integer"}}, "TargetEmployeeIDs": {"type": "array", "items": {"type": "integer"}}, "TargetUserGroupIDs": {"type": "array", "items": {"type": "string"}}}}, "AddOrUpdateShareRuleResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"sharedRuleIds": {"type": "array", "items": {"type": "string"}}}}}}, "DelShareRuleArg": {"type": "object", "properties": {"SharedRuleIds": {"type": "array", "items": {"type": "string"}}}}, "DelShareRuleResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"value": {"type": "boolean"}}}}}, "GetShareRulesArg": {"type": "object", "properties": {"Keyword": {"type": "string"}, "KeywordScope": {"type": "integer"}, "ObjectDescribeApiName": {"type": "string"}, "PermissionType": {"type": "integer"}, "Status": {"type": "integer"}, "pageNumber": {"type": "integer"}, "pageSize": {"type": "integer"}, "KeywordType": {"type": "integer"}}}, "GetShareRulesResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"SharedObjectInfos": {"type": "array", "items": {"type": "object"}}, "Page": {"type": "object"}}}}}, "FindRecordTypeArg": {"type": "object", "properties": {"describe_api_name": {"type": "string"}}}, "FindRecordTypeResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"record_type_list": {"type": "array", "items": {"type": "object"}}}}}}, "AddFieldArg": {"type": "object", "properties": {"describe_api_name": {"type": "string"}, "field_describe": {"type": "string"}}}, "AddFieldResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"object_describe": {"type": "object"}}}}}, "CreateArg": {"type": "object", "properties": {"json_data": {"type": "string"}, "include_layout": {"type": "boolean"}, "json_layout": {"type": "string"}, "is_show_in_menu": {"type": "boolean"}, "json_list_layout": {"type": "string"}}}, "CreateResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"object_describe": {"type": "object"}, "layout": {"type": "object"}}}}}, "DetailArg": {"type": "object", "properties": {"describe_api_name": {"type": "string"}, "is_exclude_describe": {"type": "boolean"}}}, "DetailResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"object_describe": {"type": "object"}, "layout": {"type": "object"}}}}}, "DisableArg": {"type": "object", "properties": {"describe_api_name": {"type": "string"}}}, "DisableResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"object_describe": {"type": "object"}}}}}, "FindDescByIdArg": {"type": "object", "properties": {"describe_id": {"type": "string"}}}, "FindDescByIdResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"object_describe": {"type": "object"}}}}}, "FindByPageArg": {"type": "object", "properties": {"orderBy": {"type": "string"}, "isAsc": {"type": "boolean"}, "isActive": {"type": "boolean"}, "defineType": {"type": "string"}}}, "FindByPageResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"describes": {"type": "array", "items": {"type": "object"}}, "page": {"type": "object"}}}}}, "ReplaceArg": {"type": "object"}, "ReplaceResult": {"type": "object", "properties": {"object_describe": {"type": "object"}}}}}