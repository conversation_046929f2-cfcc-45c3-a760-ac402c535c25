package com.facishare.paas.appframework.metadata

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.TypeReference
import com.facishare.paas.appframework.common.graph.ValueGraph
import com.facishare.paas.appframework.common.graph.ValueGraphBuilder
import com.facishare.paas.appframework.common.service.OrgService
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService
import com.facishare.paas.appframework.metadata.relation.*
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.action.ActionContext
import com.facishare.paas.metadata.api.action.IActionContext
import com.facishare.paas.metadata.api.describe.Count
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe
import com.facishare.paas.metadata.impl.describe.CurrencyFieldDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import spock.lang.Specification

class MetaDataComputeServiceImplTest extends Specification {

    String TENANT_ID = "74255"
    User user

    MetaDataComputeServiceImpl metaDataComputeService
    DescribeLogicService describeLogicService = Mock(DescribeLogicService)
    ObjectDataServiceImpl objectDataService = Mock(ObjectDataServiceImpl)
    ObjectDataProxy dataProxy = Mock(ObjectDataProxy)
    ExpressionCalculateLogicService expressionCalculateLogicService = Mock(ExpressionCalculateLogicService)
    QuoteValueService quoteValueService = Mock(QuoteValueService)
    MetaDataMiscService metaDataMiscService = Mock(MetaDataMiscService)
    MetaDataFindService metaDataFindService = Mock(MetaDataFindService)
    MetaDataActionService metaDataActionService = Mock(MetaDataActionService)
    OrgService orgService = Mock(OrgService)
    FieldRelationGraphService fieldRelationGraphService = Mock(FieldRelationGraphService)
    FieldRelationCalculateService fieldRelationCalculateService = Mock(FieldRelationCalculateService)

    def setup() {
        user = User.systemUser(TENANT_ID)
        metaDataComputeService = new MetaDataComputeServiceImpl(dataProxy: dataProxy, objectDataService: objectDataService,
                describeLogicService: describeLogicService, expressionCalculateLogicService: expressionCalculateLogicService,
                quoteValueService: quoteValueService, metaDataMiscService: metaDataMiscService, metaDataFindService: metaDataFindService,
                metaDataActionService: metaDataActionService, orgService: orgService, fieldRelationGraphService: fieldRelationGraphService,
                fieldRelationCalculateService: fieldRelationCalculateService)
    }

    def "test calculateAndUpdateByOriginalData with empty param"() {
        given:

        when:
        metaDataComputeService.calculateAndUpdateByOriginalData(user, objectApiName, dataId, null)
        then:
        noExceptionThrown()
        where:
        objectApiName | dataId
        null          | null
        ""            | null
        null          | ""
        "object"      | null
        "object"      | ""
        null          | "id1"
        ""            | "id1"
    }

    def "test calculateAndUpdateByOriginalData"() {
        given:
        Map<String, IObjectDescribe> describeMap = ["testApiName": new ObjectDescribe()]
        ValueGraph<FieldNode, RelateEdge> graph = ValueGraphBuilder.directed().build()
        when:
        describeLogicService.findObject(_, _) >> { new ObjectDescribe() }
        metaDataFindService.findObjectDataIgnoreStatusAndFormula(_, _, _) >> { new ObjectData() }
        fieldRelationGraphService.buildReverseFullDependencyGraph(_, _, _, _, _, _, _) >> { FieldRelationGraph.of(graph, describeMap) }
        metaDataFindService.findObjectDataByIdsExcludeInvalidIgnoreAll(_, _, _) >> { Lists.newArrayList { new ObjectData() } }
        metaDataComputeService.calculateAndUpdateByOriginalData(user, "testApiName", "id1", null)
        then:
        noExceptionThrown()
    }

    def "test calculateAndUpdateFormulaFields with empty param"() {
        given:
        when:
        def result = metaDataComputeService.calculateAndUpdateFormulaFields(user, "testApiName", dataIds, fieldApiNames, false)
        then:
        result == expetedResult
        where:
        dataIds | fieldApiNames || expetedResult
        null    | null          || [:]
        []      | null          || [:]
        null    | []            || [:]
        []      | []            || [:]
        ["id1"] | null          || [:]
        ["id1"] | []            || [:]
        null    | ["field1"]    || [:]
        []      | ["field1"]    || [:]
    }

    def "test calculateAndUpdateFormulaFields"() {
        given:
        when:
        describeLogicService.findObject(_, _) >> {
            describe
        }
        metaDataFindService.findObjectDataByIdsIgnoreAll(_, _, _) >> { dataList }
        expressionCalculateLogicService.bulkCalculate(_, _, _) >> { args ->
            List<IObjectData> objectDataList = (List<IObjectData>) args[1]
            if (Objects.nonNull(objectDataList)) {
                objectDataList.each { ObjectDataExt.of(it).putAll(calculateResult.getOrDefault(it.getId(), Collections.emptyMap())) }
            }
        }
        def result = metaDataComputeService.calculateAndUpdateFormulaFields(user, describe.getApiName(), dataIds, fieldApiNames, false)
        then:
        result == expetedResult
        where:
        dataIds | fieldApiNames | describe                                                                                                                           | dataList                       | calculateResult               || expetedResult
        ["id1"] | ["field2"]    | new ObjectDescribe()                                                                                                               | []                             | [:]                           || [:]
        ["id1"] | ["field1"]    | new ObjectDescribe(["api_name": "testApiName", "fields": ["field1": ["api_name": "field1", "type": "formula", "is_index": true]]]) | [new ObjectData("_id": "id1")] | ["id1": ["field1": "value1"]] || ["id1": ["field1": "value1"]]
    }

    def "test calculateCountField"() {
        given:
        def masterObjectApiName = "object_kf4gW__c"
        def masterObjectDataId = "id"
        def countFields = Lists.newArrayList(new CountFieldDescribe(new HashMap<>()))
        when:
        describeLogicService.findObjectWithoutCopyIfGray(_, _) >> { new ObjectDescribe() }
        metaDataComputeService.calculateCountField(user, masterObjectApiName, masterObjectDataId, countFields)
        then:
        noExceptionThrown()
    }

    def "test calculateCountField with dataList"() {
        given:
        def masterObjectApiName = "master"
        def masterObjectDataId = "id"
        def subObjetApiName = "detail1"
        def countField = new CountFieldDescribe(["describe_api_name": masterObjectApiName, "api_name": "field1", "type": "count", "count_type": "count", "sub_object_describe_apiname": subObjetApiName, "decimal_places": 2, "field_api_name": "ref_field"])
        def masterDescribe = new ObjectDescribe(["api_name": subObjetApiName, "tenant_id": TENANT_ID, "fields": ["field1": countField]])
        def dataList = [new ObjectData(["object_describe_api_name": masterObjectApiName, "_id": masterObjectDataId])]
        when:
        describeLogicService.findObjectsWithoutCopyIfGray(_, _) >> {
            return ["detail1": new ObjectDescribe(["api_name": subObjetApiName, "tenant_id": TENANT_ID, "fields": ["ref_field": ["type": "object_reference", "api_name": "ref_field", "describe_api_name": subObjetApiName, "target_api_name": masterObjectApiName]]])]
        }
        metaDataComputeService.calculateCountField(dataList, masterDescribe, [countField])
        then:
        noExceptionThrown()
    }

    def "test calculateCountField with data"() {
        given:
        def masterObjectData = new ObjectData()
        masterObjectData.setDescribeApiName("object_kf4gW__c")
        def masterDescribe = new ObjectDescribe()
        masterDescribe.setTenantId(TENANT_ID)
        masterDescribe.setApiName("object_kf4gW__c")
        def detailDescribe = new ObjectDescribe()
        def countFields = Lists.newArrayList(new CountFieldDescribe(new HashMap<>()))
        when:
        metaDataComputeService.calculateCountField(masterObjectData, masterDescribe, detailDescribe, countFields)
        then:
        noExceptionThrown()
    }

    def "test getCountValue"() {
        given:
        def count = new CountFieldDescribe(new HashMap<>())
        def query = new SearchTemplateQuery()
        when:
        metaDataComputeService.getCountValue(TENANT_ID, count, query)
        then:
        noExceptionThrown()
    }

    def "test getCountValueWithoutFormat"() {
        given:
        def count = new CountFieldDescribe(new HashMap<>())
        def query = new SearchTemplateQuery()
        when:
        metaDataComputeService.getCountValueWithoutFormat(TENANT_ID, count, query)
        then:
        noExceptionThrown()
    }

    def "test getCountValueWithFunctionalCurrency"() {
        given:
        def count = new CountFieldDescribe(new HashMap<>())
        def query = new SearchTemplateQuery()
        when:
        metaDataComputeService.getCountValueWithFunctionalCurrency(user, count, query)
        then:
        noExceptionThrown()
    }

    def "test getCountValue1"() {
        given:
        def count = new CountFieldDescribe(new HashMap<>())
        def query = new SearchTemplateQuery()
        when:
        metaDataComputeService.getCountValue(user, count, query)
        then:
        noExceptionThrown()
    }

    def "test getCountValue1 #needDataAuth"() {
        given:
        def count = new CountFieldDescribe(new HashMap<>())
        def query = new SearchTemplateQuery()
        def context = new ActionContext()
        def describe = new ObjectDescribe()
        when:
        metaDataComputeService.getCountValue(TENANT_ID, count, query, context, needDataAuth, describe)
        then:
        noExceptionThrown()
        where:
        needDataAuth | _
        false        | _
        true         | _
    }

    def "test getAggregateResult"() {
        given:
        def describeApiName = "testApiName"
        def countFieldApiName = "count1"
        def countType = "sum"
        def decimalPlaces = 2
        def query = new SearchTemplateQuery()
        when:
        metaDataComputeService.getAggregateResult(user, describeApiName, countFieldApiName, countType, decimalPlaces, query)
        then:
        noExceptionThrown()
    }

    def "test calculateAndUpdateCountFields with empty param"() {
        given:
        def masterDataIds = null
        def countFieldList = null
        when:
        metaDataComputeService.calculateAndUpdateCountFields(user, masterDataIds, null, countFieldList)
        then:
        noExceptionThrown()
    }

    def "test calculateAndUpdateCountFields"() {
        given:
        def masterDataIds = ["data1"]
        def masterDescribe = new ObjectDescribe()
        def count = new CountFieldDescribe(["api_name": "count1", "type": "count", "count_type": "count", "describe_api_name": "testApiName", "sub_object_describe_apiname": "subApiName", "decimal_places": 2])
        def countFieldList = [count]
        when:
        describeLogicService.findObjectsWithoutCopyIfGray(_, _) >> { ["subApiName": new ObjectDescribe(["api_name": "subApiName"])] }
        metaDataComputeService.calculateAndUpdateCountFields(user, masterDataIds, masterDescribe, countFieldList)
        then:
        noExceptionThrown()
    }

    def "test calculateAndUpdateMasterCountFields with empty param"() {
        given:
        IActionContext actionContext = new ActionContext()
        def masterDataIds = null
        def countFieldList = null
        when:
        metaDataComputeService.calculateAndUpdateMasterCountFields(actionContext, masterDataIds, null, null, countFieldList)
        then:
        noExceptionThrown()
    }

    def "test calculateAndUpdateMasterCountFields"() {
        given:
        IActionContext actionContext = ActionContextExt.of(user).getContext()
        def masterDataIds = ["data1"]
        def count = new CountFieldDescribe(["api_name": "count1", "type": "count", "count_type": "count", "describe_api_name": "testApiName", "sub_object_describe_apiname": "subApiName", "decimal_places": 2])
        def countFieldList = [count]
        def masterDescribe = new ObjectDescribe(["api_name": "testApiName", "fields": ["count1": count]])
        def detailDescribe = new ObjectDescribe(["api_name": "subApiName"])
        when:
        metaDataComputeService.calculateAndUpdateMasterCountFields(actionContext, masterDataIds, masterDescribe, detailDescribe, countFieldList)
        then:
        noExceptionThrown()
    }

    def "test calculateAndUpdateMasterCountFieldsWithData with empty param"() {
        given:
        IActionContext actionContext = ActionContextExt.of(user).getContext()
        def masterDataList = null
        def countFieldList = null
        when:
        metaDataComputeService.calculateAndUpdateMasterCountFieldsWithData(actionContext, null, masterDataList, null, countFieldList)
        then:
        noExceptionThrown()
    }

    def "test calculateAndUpdateMasterCountFieldsWithData"() {
        given:
        IActionContext actionContext = ActionContextExt.of(user).getContext()
        def masterDataList = [new ObjectData("_id": "data1")]
        def count = new CountFieldDescribe(["api_name": "count1", "type": "count", "count_type": "count", "describe_api_name": "testApiName", "sub_object_describe_apiname": "subApiName", "decimal_places": 2])
        def countFieldList = [count]
        def masterDescribe = new ObjectDescribe(["api_name": "testApiName", "fields": ["count1": count]])
        def detailDescribe = new ObjectDescribe(["api_name": "subApiName"])
        when:
        metaDataComputeService.calculateAndUpdateMasterCountFieldsWithData(actionContext, masterDescribe, masterDataList, detailDescribe, countFieldList)
        then:
        noExceptionThrown()
    }

    def "test calculateCountFieldsInMemory"() {
        given:
        IObjectData masterData = new ObjectData("object_describe_api_name": "testApiName")
        Map<String, List<IObjectData>> detailDataMap = [:]
        Map<String, IObjectDescribe> describeMap = ["testApiName": new ObjectDescribe(["api_name": "testApiName"])]
        when:
        metaDataComputeService.calculateCountFieldsInMemory(masterData, detailDataMap, describeMap)
        then:
        noExceptionThrown()
    }

    def "test calculateCountFieldsInMemoryWithFields with empty param"() {
        given:
        IObjectData masterData = new ObjectData("object_describe_api_name": "testApiName")
        Map<String, List<IObjectData>> detailDataMap = [:]
        Map<String, IObjectDescribe> describeMap = ["testApiName": new ObjectDescribe(["api_name": "testApiName"])]
        List<Count> countFields = []
        when:
        metaDataComputeService.calculateCountFieldsInMemory(masterData, detailDataMap, describeMap, countFields)
        then:
        noExceptionThrown()
    }

    def "test calculateCountFieldsInMemoryWithFields"() {
        given:
        Count count = new CountFieldDescribe(["api_name": "count1", "type": "count", "count_type": "sum", "describe_api_name": "testApiName", "sub_object_describe_apiname": "detail1", "count_field_api_name": "amount", "decimal_places": 2])
        IObjectData masterData = new ObjectData("object_describe_api_name": "testApiName")
        Map<String, List<IObjectData>> detailDataMap = ["detail1": [new ObjectData("_id": "d_id1", "amount": "100")]]
        Map<String, IObjectDescribe> describeMap = ["testApiName": new ObjectDescribe(["api_name": "testApiName", "fields": ["count1": count]]),
                                                    "detail1"    : new ObjectDescribe("api_name": "detail1", "fields": ["amount": new CurrencyFieldDescribe(["api_name": "amount", "decimal_places": 2])])]
        List<Count> countFields = [count]
        when:
        metaDataComputeService.calculateCountFieldsInMemory(masterData, detailDataMap, describeMap, countFields)
        then:
        masterData.get("count1") == "100.00"
    }

    def "test calculateExpressionForCreateData"() {
        given:
        def describeJson = '''{"tenant_id":"74255","package":"CRM","is_active":true,"last_modified_time":1656591693275,"create_time":1590130408770,"description":"","last_modified_by":"1000","display_name":"zwr-test1","created_by":"1000","version":148,"is_open_display_name":false,"index_version":200,"icon_index":0,"is_deleted":false,"api_name":"object_z51b8__c","icon_path":"","is_udef":true,"define_type":"custom","short_name":"GCu","_id":"5ec776e83c85d30001493304","fields":{"lock_rule":{"describe_api_name":"object_z51b8__c","is_index":false,"is_active":true,"create_time":1632477185841,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定规则","default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","field_num":1,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"614da002014e590001b53c25","is_index_field":false,"label_r":"锁定规则","is_single":false,"index_name":"s_5","status":"new","help_text":""},"field_EZ45h__c":{"describe_api_name":"object_z51b8__c","return_type":"text","expression_type":"js","is_index":true,"expression":"$record_type._value$","is_active":true,"create_time":1632479247908,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段4","type":"formula","field_num":33,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_EZ45h__c","define_type":"custom","_id":"614da8108343290001ecca7c","is_index_field":false,"is_single":false,"index_name":"t_1","status":"new","help_text":""},"field_6639u__c":{"describe_api_name":"object_z51b8__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"used_in":"component","is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_15","max_length":100,"is_index":true,"is_active":true,"create_time":1634212798896,"is_encrypted":false,"default_value":"","label":"收款方式","field_num":44,"api_name":"field_6639u__c","_id":"61681bbe7ceac00001f60181","is_index_field":false,"status":"new","help_text":""},"mc_exchange_rate":{"describe_api_name":"object_z51b8__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":6,"default_to_zero":true,"is_required":false,"define_type":"package","label_r":"汇率","is_single":false,"index_name":"d_5","max_length":16,"is_index":true,"is_active":true,"create_time":1609299296062,"is_encrypted":false,"step_value":1,"display_style":"input","length":10,"default_value":"","label":"汇率","api_name":"mc_exchange_rate","_id":"5febf5606268f90001d35153","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"field_02Q4Q__c":{"describe_api_name":"object_z51b8__c","return_type":"text","expression_type":"js","is_index":true,"expression":"$record_type._label$","is_active":true,"create_time":1632479272856,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段5","type":"formula","field_num":34,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_02Q4Q__c","define_type":"custom","_id":"614da8298343290001ecca8b","is_index_field":false,"is_single":false,"index_name":"t_2","status":"new","help_text":""},"life_status_before_invalid":{"describe_api_name":"object_z51b8__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"作废前生命状态","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"作废前生命状态","is_single":false,"index_name":"t_16","max_length":256,"is_index":false,"is_active":true,"create_time":1632477185841,"is_encrypted":false,"default_value":"","label":"作废前生命状态","field_num":4,"is_need_convert":false,"api_name":"life_status_before_invalid","_id":"614da002014e590001b53c27","is_index_field":false,"status":"new","help_text":""},"field_KNSUz__c":{"describe_api_name":"object_z51b8__c","return_type":"text","expression_type":"js","is_index":true,"expression":"$name$+\\"_test\\"","is_active":true,"create_time":1650724719995,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段-测试聚合框架灰度","type":"formula","field_num":42,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_KNSUz__c","define_type":"custom","_id":"62640f717e2b470001fbbbc7","is_index_field":false,"is_single":false,"index_name":"t_23","status":"new","help_text":""},"owner_department":{"describe_api_name":"object_z51b8__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"负责人主属部门","is_single":true,"index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1590130408631,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"5ec776e83c85d3000149330b","is_index_field":false,"status":"new","help_text":""},"field_06yL1__c":{"describe_api_name":"object_z51b8__c","return_type":"date_time","expression_type":"js","is_index":false,"expression":"NOW()","is_active":true,"create_time":1618294806488,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段","type":"formula","field_num":23,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_06yL1__c","define_type":"custom","_id":"607538177fc0d20001e210c9","is_index_field":false,"is_single":false,"index_name":"l_4","status":"new","help_text":""},"package":{"describe_api_name":"object_z51b8__c","is_index":false,"is_active":true,"create_time":1590130408770,"pattern":"","description":"package","is_unique":false,"label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","status":"released","max_length":200},"lock_status":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1590130408613,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定状态","default_value":"0","label":"锁定状态","type":"select_one","field_num":2,"is_need_convert":false,"is_required":false,"api_name":"lock_status","options":[{"font_color":"#2a304d","label":"未锁定","value":"0"},{"font_color":"#2a304d","label":"锁定","value":"1"}],"define_type":"package","_id":"5ec776e83c85d30001493307","is_index_field":false,"label_r":"锁定状态","is_single":false,"config":{},"index_name":"s_6","status":"new","help_text":""},"create_time":{"describe_api_name":"object_z51b8__c","is_index":true,"create_time":1590130408770,"description":"create_time","is_unique":false,"label":"创建时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"crt_time","status":"released"},"field_X1D6M__c":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1594891793949,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"incomplete","label":"签到状态","type":"select_one","field_num":12,"used_in":"component","is_required":false,"api_name":"field_X1D6M__c","options":[{"font_color":"#2a304d","label":"已完成","value":"complete"},{"font_color":"#2a304d","label":"未完成","value":"incomplete"},{"font_color":"#2a304d","not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","_id":"5f101e11cf7dbd0001746d8e","is_index_field":false,"is_single":false,"config":{},"index_name":"s_7","status":"new","help_text":""},"field_M3Lx6__c":{"describe_api_name":"object_z51b8__c","return_type":"text","expression_type":"js","is_index":true,"expression":"$name$.substring(0,3)","is_active":true,"create_time":1622014691601,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算-截取字符串","type":"formula","field_num":27,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_M3Lx6__c","define_type":"custom","_id":"60adfae51db51d000183d22f","is_index_field":false,"is_single":false,"index_name":"t_3","status":"new","help_text":""},"field_4Aq04__c":{"describe_api_name":"object_z51b8__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{"data_roles":["owner"]},"is_unique":false,"description":"","type":"currency","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_1","max_length":14,"is_index":true,"is_active":true,"create_time":1626936853669,"is_encrypted":false,"length":12,"default_value":"","label":"金额","currency_unit":"￥","field_num":29,"api_name":"field_4Aq04__c","_id":"60f916155bcb7f0001c5f418","is_index_field":false,"is_show_mask":true,"round_mode":4,"status":"new","help_text":""},"field_aBdpK__c":{"describe_api_name":"object_z51b8__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_6","max_length":14,"is_index":true,"is_active":true,"create_time":1590130433610,"is_encrypted":false,"step_value":1,"display_style":"input","length":12,"default_value":"","label":"数字","field_num":6,"api_name":"field_aBdpK__c","_id":"5ec777013c85d30001493361","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"field_zpl31__c":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1624436399595,"is_encrypted":false,"auto_adapt_places":false,"quote_field_type":"number","is_unique":false,"description":"","label":"引用字段-test2.数字","type":"quote","field_num":28,"quote_field":"field_t2Tdh__c__r.field_0CPYf__c","is_required":false,"api_name":"field_zpl31__c","define_type":"custom","_id":"60d2eeafcbd0b20001f99d08","is_index_field":false,"is_single":false,"index_name":"d_8","status":"new","help_text":""},"field_e63ey__c":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1633682626738,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","where_type":"field","label":"人员(多选)","type":"employee_many","field_num":38,"wheres":[],"is_required":false,"api_name":"field_e63ey__c","define_type":"custom","_id":"616004c2bde01d00019ffbcb","is_index_field":false,"is_single":false,"index_name":"a_4","status":"new","help_text":""},"created_by":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1590130408770,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released","description":""},"version":{"describe_api_name":"object_z51b8__c","is_index":false,"create_time":1590130408770,"length":8,"description":"version","is_unique":false,"label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"field_Xr3j1__c":{"describe_api_name":"object_z51b8__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_17","max_length":100,"is_index":true,"is_active":true,"create_time":1590130435945,"is_encrypted":false,"default_value":"hahaha","label":"单行文本","field_num":7,"api_name":"field_Xr3j1__c","_id":"5ec777033c85d30001493363","is_index_field":false,"status":"new","help_text":""},"relevant_team":{"describe_api_name":"object_z51b8__c","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","description":"Member Employee","define_type":"package","is_unique":false,"label":"Member Employee","is_single":true,"type":"employee","help_text":"Member Employee"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"description":"Member Role","define_type":"package","is_unique":false,"label":"Member Role","type":"select_one","help_text":"Member Role"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"description":"Member Permission Type","define_type":"package","is_unique":false,"label":"Member Permission Type","type":"select_one","help_text":"Member Permission Type"}},"is_index":true,"is_active":true,"create_time":1632477185859,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"614da002014e590001b53c28","is_index_field":false,"label_r":"相关团队","is_single":false,"index_name":"a_team","status":"new","help_text":"相关团队"},"field_gxn26__c":{"describe_api_name":"object_z51b8__c","return_type":"text","expression_type":"js","is_index":true,"expression":"$field_t2Tdh__c__r.field_Plm2N__c$","is_active":true,"create_time":1645431828696,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段-test2.文本","type":"formula","field_num":41,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_gxn26__c","define_type":"custom","_id":"62134c150ff995000156b1bf","is_index_field":false,"is_single":false,"index_name":"t_22","status":"new","help_text":""},"field_OA27o__c":{"describe_api_name":"object_z51b8__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"date_time","default_to_zero":false,"used_in":"component","is_required":false,"define_type":"custom","is_single":false,"index_name":"l_1","is_index":true,"is_active":true,"create_time":1594891793840,"is_encrypted":false,"default_value":"","label":"签到时间","time_zone":"GMT+8","field_num":10,"api_name":"field_OA27o__c","date_format":"yyyy-MM-dd HH:mm","_id":"5f101e11cf7dbd0001746d8c","is_index_field":false,"status":"new","help_text":""},"data_own_department":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1590130408770,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"归属部门","type":"department","is_need_convert":false,"wheres":[],"is_required":false,"api_name":"data_own_department","define_type":"package","_id":"627e0f522c8a6a0001e58893","is_index_field":false,"label_r":"归属部门","is_single":true,"index_name":"data_owner_dept_id","status":"released","help_text":""},"field_0312W__c":{"describe_api_name":"object_z51b8__c","is_index":false,"is_active":true,"create_time":1601023087364,"is_encrypted":false,"auto_adapt_places":false,"quote_field_type":"date","is_unique":false,"description":"","label":"引用字段-test2.日期","type":"quote","quote_field":"field_t2Tdh__c__r.field_7r0bc__c","is_required":false,"api_name":"field_0312W__c","define_type":"custom","_id":"5f6dac6fdbbf7400012c7c9e","is_index_field":false,"is_single":false,"index_name":"l_6","status":"new","help_text":""},"name":{"describe_api_name":"object_z51b8__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":true,"description":"name","type":"text","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","label_r":"主属性","is_single":false,"index_name":"name","max_length":100,"is_index":true,"is_active":true,"create_time":1590130408976,"is_encrypted":false,"default_value":"","label":"主属性","api_name":"name","_id":"5ec776e83c85d3000149330a","is_index_field":false,"status":"new","help_text":""},"_id":{"describe_api_name":"object_z51b8__c","is_index":false,"is_active":true,"create_time":1590130408770,"pattern":"","description":"_id","is_unique":false,"label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","status":"released","max_length":200},"tenant_id":{"describe_api_name":"object_z51b8__c","is_index":false,"is_active":true,"create_time":1590130408770,"pattern":"","description":"tenant_id","is_unique":false,"label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","status":"released","max_length":200},"field_3r45k__c":{"describe_api_name":"object_z51b8__c","is_index":false,"is_active":true,"create_time":1634212798697,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"支付(收款)组件","group_type":"payment","type":"group","amount_is_readonly":false,"is_required":false,"api_name":"field_3r45k__c","define_type":"custom","_id":"61681bbf7ceac00001f60184","fields":{"pay_time_field":"field_w1OG2__c","pay_status_field":"field_51IUc__c","pay_type_field":"field_6639u__c","pay_amount_field":"field_YNn45__c"},"is_index_field":false,"amount_input_type":"manual_input","is_single":false,"index_name":"s_3","status":"new","help_text":""},"field_z0zT1__c":{"describe_api_name":"object_z51b8__c","return_type":"number","expression_type":"js","is_index":true,"expression":"$field_aBdpK__c$+200","is_active":true,"create_time":1618834831544,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段-聚合框架1","type":"formula","field_num":24,"decimal_places":2,"default_to_zero":true,"is_required":false,"api_name":"field_z0zT1__c","define_type":"custom","_id":"607d758f7f52f1000119564f","is_index_field":false,"is_single":false,"index_name":"d_3","status":"new","help_text":""},"sign_in_info__c":{"describe_api_name":"object_z51b8__c","embedded_fields":{"system_risk__c":{"is_index":false,"is_active":true,"is_need_convert":false,"is_required":false,"api_name":"system_risk__c","pattern":"","define_type":"custom","is_unique":false,"label":"系统风险","type":"text","max_length":256,"status":"new"},"biz_type__c":{"is_index":false,"is_active":true,"is_need_convert":false,"is_required":false,"api_name":"biz_type__c","options":[{"label":"","value":"sign_in"},{"label":"","value":"sign_out"}],"define_type":"custom","is_unique":false,"label":"业务类型","type":"select_one","status":"new"},"location__c":{"is_index":false,"is_active":true,"is_need_convert":false,"is_required":false,"api_name":"location__c","define_type":"custom","is_unique":false,"label":"定位","type":"location","status":"new"},"sign_time__c":{"is_index":false,"is_active":true,"is_need_convert":false,"is_required":false,"api_name":"sign_time__c","define_type":"custom","is_unique":false,"date_format":"yyyy-MM-dd HH:mm:ss","label":"时间","time_zone":"GMT+8","type":"date_time","status":"new"},"status__c":{"is_index":false,"is_active":true,"is_need_convert":false,"is_required":false,"api_name":"status__c","options":[{"label":"已签到","value":"sign_in_complete"},{"label":"已签退","value":"sign_out_complete"}],"define_type":"custom","is_unique":false,"label":"状态","type":"select_one","status":"new"},"device_no__c":{"is_index":false,"is_active":true,"is_need_convert":false,"is_required":false,"api_name":"device_no__c","pattern":"","define_type":"custom","is_unique":false,"label":"签到设备号","type":"text","max_length":256,"status":"new"}},"is_index":false,"is_active":true,"create_time":1594891794033,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"签到信息","type":"embedded_object_list","used_in":"component","is_need_convert":false,"is_required":false,"api_name":"sign_in_info__c","define_type":"custom","_id":"5f101e12cf7dbd0001746d96","is_index_field":false,"is_single":false,"index_name":"s_2","status":"new","help_text":""},"data_own_organization":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1647329953649,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"归属组织","type":"department","is_need_convert":false,"wheres":[],"is_required":false,"api_name":"data_own_organization","define_type":"package","_id":"623042a104a720000162e8fb","is_index_field":false,"label_r":"归属组织","is_single":true,"index_name":"a_5","status":"released","help_text":""},"field_t2Tdh__c":{"describe_api_name":"object_z51b8__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","type":"object_reference","relation_outer_data_privilege":"false","related_where_type":"","wheres":[],"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_1","is_index":true,"is_active":true,"create_time":1601023053561,"is_encrypted":false,"target_api_name":"object_b0M5G__c","label":"查找关联-test2","target_related_list_name":"target_related_list_Xzk6o__c","field_num":20,"target_related_list_label":"zwr-test1","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_t2Tdh__c","_id":"5f6dac4ddbbf7400012c7c87","is_index_field":true,"status":"new","help_text":""},"field_347xO__c":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1633682623218,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"人员","type":"employee","field_num":37,"wheres":[],"is_required":false,"api_name":"field_347xO__c","define_type":"custom","_id":"616004bfbde01d00019ffbb5","is_index_field":false,"is_single":true,"index_name":"a_2","status":"new","help_text":""},"field_wM2gc__c":{"describe_api_name":"object_z51b8__c","return_type":"text","expression_type":"js","is_index":true,"expression":"$field_mek43__c._value$","is_active":true,"create_time":1617193160691,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段3","type":"formula","field_num":21,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_wM2gc__c","define_type":"custom","_id":"606468c94a8f0f00245eeb86","is_index_field":false,"is_single":false,"index_name":"t_4","status":"new","help_text":""},"field_Ff8n0__c":{"describe_api_name":"object_z51b8__c","return_type":"number","expression_type":"js","is_index":true,"expression":"$field_aBdpK__c$+600","is_active":true,"create_time":1591329625800,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段2","type":"formula","field_num":9,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_Ff8n0__c","define_type":"custom","_id":"5ed9c35a194dff0001289307","is_index_field":false,"is_single":false,"index_name":"d_4","status":"new","help_text":""},"field_07jCm__c":{"describe_api_name":"object_z51b8__c","return_type":"text","expression_type":"js","is_index":true,"expression":"$field_mek43__c$","is_active":true,"create_time":1632907814098,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段6","type":"formula","field_num":36,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_07jCm__c","define_type":"custom","_id":"615432276cf94500016ba7ab","is_index_field":false,"is_single":false,"index_name":"t_5","status":"new","help_text":""},"field_gCk9g__c":{"describe_api_name":"object_z51b8__c","return_type":"text","expression_type":"js","is_index":true,"expression":"$name$+\\"_test2\\"","is_active":true,"create_time":1650725284778,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段-测试聚合框架灰度2","type":"formula","field_num":49,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_gCk9g__c","define_type":"custom","_id":"626411a47e2b470001fbbd8a","is_index_field":false,"is_single":false,"index_name":"t_24","status":"new","help_text":""},"field_r42c4__c":{"describe_api_name":"object_z51b8__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","type":"object_reference_many","wheres":[],"is_required":false,"define_type":"custom","is_single":false,"index_name":"a_1","is_index":true,"is_active":true,"create_time":1638351485957,"is_encrypted":false,"target_api_name":"object_b0M5G__c","label":"查找关联(多选)-test2","target_related_list_name":"target_related_list_uJF4f__c","field_num":48,"target_related_list_label":"zwr-test1_多选","action_on_target_delete":"set_null","api_name":"field_r42c4__c","_id":"61a7427ea7550c0001f2e5cc","is_index_field":false,"status":"new","help_text":""},"field_51IUc__c":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1634212799006,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"收款状态","type":"select_one","field_num":45,"used_in":"component","is_required":false,"api_name":"field_51IUc__c","options":[{"label":"未收款","value":"incomplete"},{"label":"已收款","value":"complete"}],"define_type":"custom","_id":"61681bbf7ceac00001f60182","is_index_field":false,"is_single":false,"config":{},"index_name":"s_8","status":"new","help_text":""},"field_2WGwr__c":{"describe_api_name":"object_z51b8__c","sign_in_button_name":"签到","is_index":false,"is_active":true,"create_time":1594891793258,"is_enable_sign_out":true,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"签到组件","group_type":"sign_in","type":"group","is_enable_modify_position":true,"sign_out_button_name":"签退","is_required":false,"api_name":"field_2WGwr__c","define_type":"custom","_id":"5f101e12cf7dbd0001746d95","fields":{"visit_status_field":"field_bUc32__c","sign_in_location_field":"field_bt6Q5__c","sign_in_status_field":"field_X1D6M__c","sign_in_time_field":"field_OA27o__c","sign_out_status_field":"field_rM3rX__c","interval_field":"field_t47d6__c","sign_out_location_field":"field_tZmh1__c","sign_in_info_list_field":"sign_in_info__c","sign_out_time_field":"field_8dzbW__c"},"is_index_field":false,"is_single":false,"index_name":"s_4","status":"new","help_text":""},"lock_user":{"describe_api_name":"object_z51b8__c","is_index":false,"is_active":true,"create_time":1632477185841,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"加锁人","where_type":"field","label":"加锁人","type":"employee","field_num":5,"is_need_convert":false,"wheres":[],"is_required":false,"api_name":"lock_user","define_type":"package","_id":"614da002014e590001b53c26","is_index_field":false,"label_r":"加锁人","is_single":true,"index_name":"a_3","status":"new","help_text":""},"field_rUpzg__c":{"describe_api_name":"object_z51b8__c","return_type":"text","expression_type":"js","is_index":true,"expression":"$field_mek43__c._label$+\\"|\\"+$field_mek43__c._value$+\\"|\\"+$record_type._label$+\\"|\\"+$record_type._value$+\\"#\\"+$field_t2Tdh__c__r.field_9P9Kh__c._label$+\\"|\\"+$field_t2Tdh__c__r.field_9P9Kh__c._value$+\\"|\\"+$field_t2Tdh__c__r.record_type._label$+\\"|\\"+$field_t2Tdh__c__r.record_type._value$","is_active":true,"create_time":1634715788817,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段-780","type":"formula","field_num":47,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_rUpzg__c","define_type":"custom","_id":"616fc88ded19780001754e12","is_index_field":false,"is_single":false,"index_name":"t_6","status":"new","help_text":""},"field_U5fTq__c":{"describe_api_name":"object_z51b8__c","return_type":"text","expression_type":"js","is_index":true,"expression":"$created_by__r.user_id$","is_active":true,"create_time":1620267982080,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段-人员字段1","type":"formula","field_num":25,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_U5fTq__c","define_type":"custom","_id":"609353cfa746470001f73ed0","is_index_field":false,"is_single":false,"index_name":"t_7","status":"new","help_text":""},"field_mek43__c":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1627975989630,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"单选1","type":"select_one","field_num":30,"is_required":false,"api_name":"field_mek43__c","options":[{"font_color":"#2a304d","label":"A-修改","value":"sV7L44a2x"},{"font_color":"#2a304d","label":"B","value":"e48jd88aS"},{"font_color":"#2a304d","label":"C33-修改999","value":"smcQJqd41"},{"font_color":"#2a304d","not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","_id":"6108f136b6756e0001aab45a","is_index_field":false,"is_single":false,"config":{},"index_name":"s_9","status":"new","help_text":""},"is_deleted":{"describe_api_name":"object_z51b8__c","is_index":false,"create_time":1590130408770,"description":"is_deleted","is_unique":false,"default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","define_type":"system","index_name":"is_del","status":"released"},"field_tZmh1__c":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1594891793949,"is_encrypted":false,"auto_location":false,"auto_adapt_places":false,"is_unique":false,"description":"","is_geo_index":false,"label":"签退地址","type":"location","field_num":14,"used_in":"component","is_required":false,"api_name":"field_tZmh1__c","range_limit":false,"define_type":"custom","radius_range":100,"_id":"5f101e11cf7dbd0001746d90","is_index_field":false,"is_single":false,"index_name":"t_12","status":"new","help_text":""},"object_describe_api_name":{"describe_api_name":"object_z51b8__c","is_index":false,"is_active":true,"create_time":1590130408770,"pattern":"","description":"object_describe_api_name","is_unique":false,"label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","status":"released","max_length":200},"out_owner":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1590130408770,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"config":{"display":1},"index_name":"o_owner","status":"released","description":""},"mc_functional_currency":{"describe_api_name":"object_z51b8__c","is_index":false,"is_active":true,"create_time":1609299296070,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"本位币","type":"select_one","is_required":false,"api_name":"mc_functional_currency","options":[{"label":"CNY - 人民币","value":"CNY"}],"define_type":"package","_id":"5febf5606268f90001d35154","is_index_field":false,"label_r":"本位币","is_single":false,"config":{},"index_name":"s_14","status":"new","help_text":""},"field_HzW4m__c":{"describe_api_name":"object_z51b8__c","return_type":"text","expression_type":"js","is_index":true,"expression":"$owner__r.phone$","is_active":true,"create_time":1617795529569,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段-人员字段","type":"formula","field_num":22,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_HzW4m__c","define_type":"custom","_id":"606d99ca324d5e00017c054f","is_index_field":false,"is_single":false,"index_name":"t_8","status":"new","help_text":""},"owner":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1590130408630,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"负责人","type":"employee","is_need_convert":false,"wheres":[],"is_required":true,"api_name":"owner","define_type":"package","_id":"5ec776e83c85d30001493305","is_index_field":false,"label_r":"负责人","is_single":true,"index_name":"owner","status":"new","help_text":""},"last_modified_time":{"describe_api_name":"object_z51b8__c","is_index":true,"create_time":1590130408770,"description":"last_modified_time","is_unique":false,"label":"最后修改时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"md_time","status":"released"},"life_status":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1590130408638,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"normal","label":"生命状态","type":"select_one","field_num":3,"is_need_convert":false,"is_required":true,"api_name":"life_status","options":[{"font_color":"#2a304d","label":"未生效","value":"ineffective"},{"font_color":"#2a304d","label":"审核中","value":"under_review"},{"font_color":"#2a304d","label":"正常","value":"normal"},{"font_color":"#2a304d","label":"变更中","value":"in_change"},{"font_color":"#2a304d","label":"作废","value":"invalid"}],"define_type":"package","_id":"5ec776e83c85d30001493308","is_index_field":false,"label_r":"生命状态","is_single":false,"config":{},"index_name":"s_10","status":"new","help_text":""},"field_bUc32__c":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1594891794032,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"incomplete","label":"拜访状态","type":"select_one","field_num":16,"used_in":"component","is_required":false,"api_name":"field_bUc32__c","options":[{"font_color":"#2a304d","label":"已完成","value":"complete"},{"font_color":"#2a304d","label":"未完成","value":"incomplete"},{"font_color":"#2a304d","not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","_id":"5f101e12cf7dbd0001746d92","is_index_field":false,"is_single":false,"config":{},"index_name":"s_11","status":"new","help_text":""},"field_w1OG2__c":{"describe_api_name":"object_z51b8__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"date_time","default_to_zero":false,"used_in":"component","is_required":false,"define_type":"custom","is_single":false,"index_name":"l_2","is_index":true,"is_active":true,"create_time":1634212799006,"is_encrypted":false,"default_value":"","label":"收款时间","time_zone":"GMT+8","field_num":46,"api_name":"field_w1OG2__c","date_format":"yyyy-MM-dd HH:mm:ss","_id":"61681bbf7ceac00001f60183","is_index_field":false,"status":"new","help_text":""},"field_Y5uJb__c":{"describe_api_name":"object_z51b8__c","default_is_expression":true,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_18","max_length":100,"is_index":true,"is_active":true,"create_time":1630380708826,"is_encrypted":false,"default_value":"$field_t2Tdh__c__r.field_GOKZz__c$","label":"单行文本-test2.默认值","field_num":32,"api_name":"field_Y5uJb__c","_id":"612da2a59b64b6000133d5f3","is_index_field":false,"status":"new","help_text":""},"last_modified_by":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1590130408770,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released","description":""},"out_tenant_id":{"describe_api_name":"object_z51b8__c","is_index":false,"is_active":true,"create_time":1590130408770,"pattern":"","description":"out_tenant_id","is_unique":false,"label":"out_tenant_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","config":{"display":0},"index_name":"o_ei","status":"released","max_length":200},"field_y34Gq__c":{"describe_api_name":"object_z51b8__c","return_type":"text","expression_type":"js","is_index":true,"expression":"IF(NULL2DEFAULT($field_t2Tdh__c__r.field_4vumU__c$,false),'是1','否0')","is_active":true,"create_time":1640689239559,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段-test2.布尔","type":"formula","field_num":39,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_y34Gq__c","define_type":"custom","_id":"61caee5b0bcf8e000121be14","is_index_field":false,"is_single":false,"index_name":"t_20","status":"new","help_text":""},"mc_currency":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1609299296062,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"币种","type":"select_one","is_required":false,"api_name":"mc_currency","options":[{"label":"BHD - Bahraini Dinar","value":"BHD"},{"not_usable":true,"label":"BDT - 孟加拉国塔卡","value":"BDT"},{"not_usable":true,"label":"BGN - Bulgarian Lev","value":"BGN"},{"label":"AWG - 阿鲁巴岛弗罗林","value":"AWG"},{"label":"USD - U.S. Dollar","value":"USD"},{"label":"AED - UAE Dirham","value":"AED"},{"label":"ALL - 阿尔巴尼亚列克","value":"ALL"},{"label":"BAM - 自由兑换马克","value":"BAM"},{"label":"ANG - 荷属安地列斯盾","value":"ANG"},{"label":"ARS - 阿根廷比索","value":"ARS"},{"label":"AMD - 亚美尼亚打兰","value":"AMD"},{"label":"AUD - Australian Dollar","value":"AUD"},{"label":"AOA - 安哥拉宽扎","value":"AOA"},{"label":"BBD - 巴巴多斯元","value":"BBD"},{"label":"AFN - Afghanistan Afghani (New)","value":"AFN"},{"label":"AZN - 阿塞拜疆马纳特","value":"AZN"},{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"5febf5606268f90001d35152","is_index_field":false,"label_r":"币种","is_single":false,"config":{},"index_name":"s_15","status":"new","help_text":""},"field_de7T7__c":{"describe_api_name":"object_z51b8__c","return_type":"text","expression_type":"js","is_index":true,"expression":"IF($owner$==$field_t2Tdh__c__r.owner$,\\"是\\",\\"否\\")","is_active":true,"create_time":1642474672568,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段-人员比较","type":"formula","field_num":40,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_de7T7__c","define_type":"custom","_id":"61e62cb010e6120001d91484","is_index_field":false,"is_single":false,"index_name":"t_21","status":"new","help_text":""},"record_type":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1590130408637,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"record_type","label":"业务类型","type":"record_type","is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"font_color":"#2a304d","api_name":"default__c","description":"预设业务类型","label":"预设业务类型"},{"is_active":true,"font_color":"#2a304d","api_name":"record_T3Yx1__c","label":"业务类型1--修改"},{"is_active":true,"font_color":"#2a304d","api_name":"record_Z5252__c","label":"业务类型22"}],"define_type":"package","_id":"5ec776e83c85d3000149330d","is_index_field":false,"label_r":"业务类型","is_single":false,"config":{},"index_name":"r_type","status":"released","help_text":""},"field_W6f2R__c":{"describe_api_name":"object_z51b8__c","return_type":"text","expression_type":"js","is_index":true,"expression":"$field_HzW4m__c$+\\"-\\"+$field_U5fTq__c$","is_active":true,"create_time":1620640922042,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算-间接人员","type":"formula","field_num":26,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_W6f2R__c","define_type":"custom","_id":"6099049b7161480001c1b70e","is_index_field":false,"is_single":false,"index_name":"t_9","status":"new","help_text":""},"field_YNn45__c":{"describe_api_name":"object_z51b8__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{},"is_unique":false,"description":"","type":"currency","decimal_places":2,"default_to_zero":true,"used_in":"component","is_required":false,"define_type":"custom","is_single":false,"index_name":"d_2","max_length":14,"is_index":true,"is_active":true,"create_time":1634212798896,"is_encrypted":false,"length":12,"default_value":"","label":"收款金额","currency_unit":"￥","field_num":43,"api_name":"field_YNn45__c","_id":"61681bbe7ceac00001f60180","is_index_field":false,"is_show_mask":false,"round_mode":4,"status":"new","help_text":""},"field_8dzbW__c":{"describe_api_name":"object_z51b8__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"date_time","default_to_zero":false,"used_in":"component","is_required":false,"define_type":"custom","is_single":false,"index_name":"l_3","is_index":true,"is_active":true,"create_time":1594891793949,"is_encrypted":false,"default_value":"","label":"签退时间","time_zone":"GMT+8","field_num":13,"api_name":"field_8dzbW__c","date_format":"yyyy-MM-dd HH:mm","_id":"5f101e11cf7dbd0001746d8f","is_index_field":false,"status":"new","help_text":""},"field_rM3rX__c":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1594891793968,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"incomplete","label":"签退状态","type":"select_one","field_num":15,"used_in":"component","is_required":false,"api_name":"field_rM3rX__c","options":[{"font_color":"#2a304d","label":"已完成","value":"complete"},{"font_color":"#2a304d","label":"未完成","value":"incomplete"},{"font_color":"#2a304d","not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","_id":"5f101e11cf7dbd0001746d91","is_index_field":false,"is_single":false,"config":{},"index_name":"s_12","status":"new","help_text":""},"field_juY8J__c":{"describe_api_name":"object_z51b8__c","return_type":"text","expression_type":"js","is_index":true,"expression":"$field_mek43__c._label$+\\"-8888888888\\"","is_active":true,"create_time":1591329143196,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段1","type":"formula","field_num":8,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_juY8J__c","define_type":"custom","_id":"5ed9c177194dff0001288c0a","is_index_field":false,"is_single":false,"index_name":"t_10","status":"new","help_text":""},"field_e3S1L__c":{"describe_api_name":"object_z51b8__c","return_type":"text","expression_type":"js","is_index":true,"expression":"\\"111=\\"","is_active":true,"create_time":1599137164519,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段-不落地","type":"formula","field_num":18,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_e3S1L__c","define_type":"custom","_id":"5f50e58df699be000119333b","is_index_field":false,"is_single":false,"index_name":"t_11","status":"new","help_text":""},"field_09nK8__c":{"describe_api_name":"object_z51b8__c","return_type":"date","expression_type":"js","is_index":true,"expression":"DATEVALUE(\\"2020-10-01\\")","is_active":true,"create_time":1599473618657,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段-测试禁用编辑","type":"formula","field_num":19,"decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"field_09nK8__c","define_type":"custom","_id":"5f5607d2dc6430000162a65d","is_index_field":false,"is_single":false,"index_name":"l_5","status":"new","help_text":""},"mc_exchange_rate_version":{"describe_api_name":"object_z51b8__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"汇率版本","is_single":false,"index_name":"t_19","max_length":256,"is_index":false,"is_active":true,"create_time":1609299296070,"is_encrypted":false,"default_value":"","label":"汇率版本","api_name":"mc_exchange_rate_version","_id":"5febf5606268f90001d35155","is_index_field":false,"status":"new","help_text":""},"field_t47d6__c":{"describe_api_name":"object_z51b8__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":1,"default_to_zero":true,"used_in":"component","is_required":false,"define_type":"custom","is_single":false,"index_name":"d_7","max_length":16,"is_index":true,"is_active":true,"create_time":1594891794032,"is_encrypted":false,"step_value":1,"display_style":"input","length":15,"default_value":"","label":"间隔时长","field_num":17,"api_name":"field_t47d6__c","_id":"5f101e12cf7dbd0001746d94","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"field_3Wcyc__c":{"describe_api_name":"object_z51b8__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"long_text","default_to_zero":false,"is_required":false,"define_type":"custom","is_single":false,"index_name":"t_14","max_length":2000,"is_index":true,"is_active":true,"create_time":1632649151247,"min_length":0,"is_encrypted":false,"default_value":"","label":"多行文本","field_num":35,"api_name":"field_3Wcyc__c","_id":"61503fbf8cacd90001e68edc","is_index_field":false,"status":"new","help_text":""},"field_D9vY4__c":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1627976009140,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"单选2","type":"select_one","field_num":31,"is_required":false,"api_name":"field_D9vY4__c","options":[{"font_color":"#2a304d","label":"A1","value":"Mn2rWy6C6"},{"font_color":"#2a304d","label":"A2","value":"vDUM6fzfh"},{"font_color":"#2a304d","label":"B1","value":"DHhun6gME"},{"font_color":"#2a304d","label":"B2","value":"l8g5pX828"},{"font_color":"#2a304d","not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","_id":"6108f149b6756e0001aab45f","is_index_field":false,"is_single":false,"config":{},"index_name":"s_13","status":"new","help_text":""},"field_bt6Q5__c":{"describe_api_name":"object_z51b8__c","is_index":true,"is_active":true,"create_time":1594891793840,"is_encrypted":false,"auto_location":false,"auto_adapt_places":false,"is_unique":false,"description":"","is_geo_index":false,"label":"签到地址","type":"location","field_num":11,"used_in":"component","is_required":false,"api_name":"field_bt6Q5__c","range_limit":false,"define_type":"custom","radius_range":100,"_id":"5f101e11cf7dbd0001746d8d","is_index_field":false,"is_single":false,"index_name":"t_13","status":"new","help_text":""}},"release_version":"6.4","actions":{}}'''
        def describe = new ObjectDescribe()
        describe.fromJsonString(describeJson)
        def recordType = "default__c"
        when:
        metaDataComputeService.calculateExpressionForCreateData(user, recordType, describe)
        then:
        noExceptionThrown()
    }

    def "test calculateCountFieldsWithDetailDataAndDbValue"() {
        given:
        def masterData = new ObjectData()
        def detailDataList = Lists.newArrayList(new ObjectData())
        def refField = new ObjectReferenceFieldDescribe()
        refField.setApiName("ref_field1")
        def count = new CountFieldDescribe()
        count.setApiName("field1")
        count.setCountType(Count.TYPE_SUM)
        count.setSubObjectDescribeApiName("detail_object")
        count.setFieldApiName(refField.getApiName())
        def countList = Lists.newArrayList(count)
        def masterDescribe = new ObjectDescribe()
        masterDescribe.addFieldDescribe(count)
        def detailDescribe = new ObjectDescribe()
        detailDescribe.setApiName("detail_object")
        detailDescribe.addFieldDescribe(refField)
        when:
        metaDataComputeService.calculateCountFieldsWithDetailDataAndDbValue(masterData, detailDataList, masterDescribe, detailDescribe, countList)
        then:
        noExceptionThrown()
    }

    def "test calculateCountFieldsWithDbDetailDataAndDbValue"() {
        given:
        def masterData = new ObjectData()
        def detailDataList = Lists.newArrayList(new ObjectData())
        def dbDetailDataList = Lists.newArrayList(new ObjectData())
        def refField = new ObjectReferenceFieldDescribe()
        refField.setApiName("ref_field1")
        def count = new CountFieldDescribe()
        count.setApiName("field1")
        count.setCountType(Count.TYPE_SUM)
        count.setSubObjectDescribeApiName("detail_object")
        count.setFieldApiName(refField.getApiName())
        def countList = Lists.newArrayList(count)
        def masterDescribe = new ObjectDescribe()
        masterDescribe.addFieldDescribe(count)
        def detailDescribe = new ObjectDescribe()
        detailDescribe.setApiName("detail_object")
        detailDescribe.addFieldDescribe(refField)
        when:
        metaDataComputeService.calculateCountFieldsWithDbDetailDataAndDbValue(masterData, detailDataList, masterDescribe, detailDescribe, countList, dbDetailDataList)
        then:
        noExceptionThrown()
    }

    def "test batchCalculateBySortFields"() {
        given:
        def describeJson = '''{"tenant_id":"74255","package":"CRM","is_active":true,"last_modified_time":1656660026707,"create_time":1614236166130,"description":"","last_modified_by":"1000","display_name":"zwr-detail1","created_by":"1000","version":35,"index_version":200,"icon_index":0,"is_deleted":false,"api_name":"object_n2hbE__c","icon_path":"","is_udef":true,"define_type":"custom","short_name":"wqz","_id":"60374a0662cf0e0001d8c325","fields":{"tenant_id":{"describe_api_name":"object_n2hbE__c","is_index":false,"is_active":true,"create_time":1614236166130,"pattern":"","description":"tenant_id","is_unique":false,"label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","status":"released","max_length":200},"lock_rule":{"describe_api_name":"object_n2hbE__c","is_index":false,"is_active":true,"create_time":1614236165700,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定规则","default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","field_num":1,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"60374a0662cf0e0001d8c31d","is_index_field":false,"label_r":"锁定规则","is_single":false,"index_name":"s_2","status":"new","help_text":""},"data_own_organization":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1647329966637,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"归属组织","type":"department","is_need_convert":false,"is_required":false,"api_name":"data_own_organization","define_type":"package","_id":"623042ae04a720000162e9c3","is_index_field":false,"label_r":"归属组织","is_single":true,"index_name":"a_2","status":"released","description":""},"field_q3W9r__c":{"describe_api_name":"object_n2hbE__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_3","max_length":14,"is_index":true,"is_active":true,"create_time":1614236186521,"is_encrypted":false,"length":12,"default_value":"","label":"数字","field_num":12,"api_name":"field_q3W9r__c","_id":"60374a1a62cf0e0001d8c35f","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"field_ZADt1__c":{"describe_api_name":"object_n2hbE__c","return_type":"number","expression_type":"js","is_index":true,"expression":"$field_g12L5__c__r.field_C6j1e__c$","is_active":true,"create_time":1656659325947,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"计算字段-master1.统计","type":"formula","field_num":15,"decimal_places":2,"default_to_zero":true,"is_required":false,"api_name":"field_ZADt1__c","define_type":"custom","_id":"62be9d7e2b4d0700019ec108","is_index_field":false,"is_single":false,"index_name":"d_4","status":"new","help_text":"","description":""},"lock_user":{"describe_api_name":"object_n2hbE__c","is_index":false,"is_active":true,"create_time":1614236165701,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"加锁人","label":"加锁人","type":"employee","field_num":4,"is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"60374a0662cf0e0001d8c31f","is_index_field":false,"label_r":"加锁人","is_single":true,"index_name":"a_1","status":"new","help_text":""},"field_p6s3B__c":{"describe_api_name":"object_n2hbE__c","return_type":"number","expression_type":"js","is_index":true,"expression":"$field_VtKpk__c__r.field_Yo91y__c$","is_active":true,"create_time":1656659847348,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"计算字段-lookup1.统计","type":"formula","field_num":17,"decimal_places":2,"default_to_zero":true,"is_required":false,"api_name":"field_p6s3B__c","define_type":"custom","_id":"62be9f872b4d0700019ec39b","is_index_field":false,"is_single":false,"index_name":"d_5","status":"new","help_text":"","description":""},"mc_exchange_rate":{"describe_api_name":"object_n2hbE__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":6,"default_to_zero":true,"is_required":false,"define_type":"package","label_r":"汇率","is_single":false,"index_name":"d_2","max_length":16,"is_index":true,"is_active":true,"create_time":1614236166060,"is_encrypted":false,"length":10,"default_value":"","label":"汇率","field_num":6,"api_name":"mc_exchange_rate","_id":"60374a0662cf0e0001d8c322","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"field_VtKpk__c":{"describe_api_name":"object_n2hbE__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","wheres":[],"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_7","is_index":true,"is_active":true,"create_time":1656659762927,"is_encrypted":false,"target_api_name":"object_e83CM__c","label":"查找关联-lookup1","target_related_list_name":"target_related_list_2K2mF__c","field_num":16,"target_related_list_label":"zwr-detail1","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_VtKpk__c","_id":"62be9f332b4d0700019ec372","is_index_field":true,"status":"new","help_text":"","description":""},"is_deleted":{"describe_api_name":"object_n2hbE__c","is_index":false,"create_time":1614236166130,"description":"is_deleted","is_unique":false,"default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","define_type":"system","index_name":"is_del","status":"released"},"life_status_before_invalid":{"describe_api_name":"object_n2hbE__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"作废前生命状态","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"作废前生命状态","is_single":false,"index_name":"t_2","max_length":256,"is_index":false,"is_active":true,"create_time":1614236165701,"is_encrypted":false,"default_value":"","label":"作废前生命状态","field_num":7,"is_need_convert":false,"api_name":"life_status_before_invalid","_id":"60374a0662cf0e0001d8c320","is_index_field":false,"status":"new","help_text":""},"object_describe_api_name":{"describe_api_name":"object_n2hbE__c","is_index":false,"is_active":true,"create_time":1614236166130,"pattern":"","description":"object_describe_api_name","is_unique":false,"label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","status":"released","max_length":200},"field_mc04S__c":{"describe_api_name":"object_n2hbE__c","return_type":"text","expression_type":"js","is_index":true,"expression":"$field_g12L5__c__r.name$","is_active":true,"create_time":1630380476438,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"计算字段-master1.name","type":"formula","field_num":14,"default_to_zero":false,"is_required":false,"api_name":"field_mc04S__c","define_type":"custom","_id":"612da1bd9b64b6000133d4ff","is_index_field":false,"is_single":false,"index_name":"t_1","status":"new","help_text":"","description":""},"out_owner":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1614236166130,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"config":{"display":1},"index_name":"o_owner","status":"released","description":""},"owner_department":{"describe_api_name":"object_n2hbE__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"负责人主属部门","is_single":true,"index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1614236166050,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"60374a0662cf0e0001d8c314","is_index_field":false,"status":"new","help_text":""},"field_W1FfY__c":{"describe_api_name":"object_n2hbE__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_4","max_length":100,"is_index":true,"is_active":true,"create_time":1614236178962,"is_encrypted":false,"default_value":"","label":"单行文本","field_num":10,"api_name":"field_W1FfY__c","_id":"60374a1362cf0e0001d8c353","is_index_field":false,"status":"new","help_text":""},"mc_functional_currency":{"describe_api_name":"object_n2hbE__c","is_index":false,"is_active":true,"create_time":1614236166061,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"本位币","type":"select_one","field_num":9,"is_required":false,"api_name":"mc_functional_currency","options":[{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"60374a0662cf0e0001d8c323","is_index_field":false,"label_r":"本位币","is_single":false,"config":{},"index_name":"s_5","status":"new","help_text":""},"field_g12L5__c":{"describe_api_name":"object_n2hbE__c","auto_adapt_places":false,"is_unique":false,"description":"","type":"master_detail","is_required":true,"define_type":"custom","is_single":false,"index_name":"s_1","is_index":true,"is_active":true,"create_time":1614246772099,"is_encrypted":false,"target_api_name":"object_oenhP__c","label":"主从关系","show_detail_button":false,"target_related_list_name":"target_related_list_oz0Xz__c","field_num":13,"target_related_list_label":"zwr-detail1","api_name":"field_g12L5__c","is_create_when_master_create":true,"_id":"6037737511c2b00001cc0f85","is_required_when_master_create":true,"is_index_field":true,"status":"new","help_text":""},"owner":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1614236166049,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"负责人","type":"employee","is_need_convert":false,"is_required":true,"api_name":"owner","define_type":"package","_id":"60374a0662cf0e0001d8c313","is_index_field":false,"label_r":"负责人","is_single":true,"index_name":"owner","status":"new","help_text":""},"last_modified_time":{"describe_api_name":"object_n2hbE__c","is_index":true,"create_time":1614236166130,"description":"last_modified_time","is_unique":false,"label":"最后修改时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"md_time","status":"released"},"package":{"describe_api_name":"object_n2hbE__c","is_index":false,"is_active":true,"create_time":1614236166130,"pattern":"","description":"package","is_unique":false,"label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","status":"released","max_length":200},"lock_status":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1614236165701,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定状态","default_value":"0","label":"锁定状态","type":"select_one","field_num":2,"is_need_convert":false,"is_required":false,"api_name":"lock_status","options":[{"label":"未锁定","value":"0"},{"label":"锁定","value":"1"}],"define_type":"package","_id":"60374a0662cf0e0001d8c31e","is_index_field":false,"label_r":"锁定状态","is_single":false,"config":{},"index_name":"s_4","status":"new","help_text":""},"create_time":{"describe_api_name":"object_n2hbE__c","is_index":true,"create_time":1614236166130,"description":"create_time","is_unique":false,"label":"创建时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"crt_time","status":"released"},"life_status":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1614236166057,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"normal","label":"生命状态","type":"select_one","field_num":3,"is_need_convert":false,"is_required":false,"api_name":"life_status","options":[{"label":"未生效","value":"ineffective"},{"label":"审核中","value":"under_review"},{"label":"正常","value":"normal"},{"label":"变更中","value":"in_change"},{"label":"作废","value":"invalid"}],"define_type":"package","_id":"60374a0662cf0e0001d8c31b","is_index_field":false,"label_r":"生命状态","is_single":false,"config":{},"index_name":"s_3","status":"new","help_text":""},"field_sKiz7__c":{"describe_api_name":"object_n2hbE__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{},"is_unique":false,"description":"","type":"currency","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_1","max_length":14,"is_index":true,"is_active":true,"create_time":1614236183866,"is_encrypted":false,"length":12,"default_value":"","label":"金额","currency_unit":"￥","field_num":11,"api_name":"field_sKiz7__c","_id":"60374a1862cf0e0001d8c356","is_index_field":false,"is_show_mask":false,"round_mode":4,"status":"new","help_text":""},"field_13tbw__c":{"describe_api_name":"object_n2hbE__c","is_index":false,"is_active":true,"create_time":1656660026441,"is_encrypted":false,"auto_adapt_places":false,"quote_field_type":"text","is_unique":false,"label":"引用字段-lookup1.name","type":"quote","quote_field":"field_VtKpk__c__r.name","is_required":false,"api_name":"field_13tbw__c","define_type":"custom","_id":"62bea03a2b4d0700019ec3cd","is_index_field":false,"is_single":false,"index_name":"t_5","status":"new","help_text":"","description":""},"last_modified_by":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1614236166130,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released","description":""},"out_tenant_id":{"describe_api_name":"object_n2hbE__c","is_index":false,"is_active":true,"create_time":1614236166130,"pattern":"","description":"out_tenant_id","is_unique":false,"label":"out_tenant_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","config":{"display":0},"index_name":"o_ei","status":"released","max_length":200},"created_by":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1614236166130,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released","description":""},"version":{"describe_api_name":"object_n2hbE__c","is_index":false,"create_time":1614236166130,"length":8,"description":"version","is_unique":false,"label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"mc_currency":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1614236166059,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"币种","type":"select_one","field_num":5,"is_required":false,"api_name":"mc_currency","options":[{"label":"BHD - Bahraini Dinar","value":"BHD"},{"not_usable":true,"label":"BDT - 孟加拉国塔卡","value":"BDT"},{"not_usable":true,"label":"BGN - Bulgarian Lev","value":"BGN"},{"label":"AWG - 阿鲁巴岛弗罗林","value":"AWG"},{"label":"USD - U.S. Dollar","value":"USD"},{"label":"AED - UAE Dirham","value":"AED"},{"label":"ALL - 阿尔巴尼亚列克","value":"ALL"},{"label":"BAM - 自由兑换马克","value":"BAM"},{"label":"ANG - 荷属安地列斯盾","value":"ANG"},{"label":"ARS - 阿根廷比索","value":"ARS"},{"label":"AMD - 亚美尼亚打兰","value":"AMD"},{"label":"AUD - Australian Dollar","value":"AUD"},{"label":"AOA - 安哥拉宽扎","value":"AOA"},{"label":"BBD - 巴巴多斯元","value":"BBD"},{"label":"AFN - Afghanistan Afghani (New)","value":"AFN"},{"label":"AZN - 阿塞拜疆马纳特","value":"AZN"},{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"60374a0662cf0e0001d8c321","is_index_field":false,"label_r":"币种","is_single":false,"config":{},"index_name":"s_6","status":"new","help_text":""},"record_type":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1614236166056,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"record_type","label":"业务类型","type":"record_type","is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"api_name":"default__c","description":"预设业务类型","label":"预设业务类型"},{"is_active":true,"api_name":"record_2qvaY__c","label":"业务类型1"}],"define_type":"package","_id":"60374a0662cf0e0001d8c31a","is_index_field":false,"label_r":"业务类型","is_single":false,"config":{},"index_name":"r_type","status":"released","help_text":""},"relevant_team":{"describe_api_name":"object_n2hbE__c","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","description":"成员员工","define_type":"package","is_unique":false,"label":"成员员工","is_single":true,"type":"employee","help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"description":"成员角色","define_type":"package","is_unique":false,"label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"description":"成员权限类型","define_type":"package","is_unique":false,"label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1614236166058,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"60374a0662cf0e0001d8c31c","is_index_field":false,"label_r":"相关团队","is_single":false,"index_name":"a_team","status":"new","help_text":"相关团队"},"data_own_department":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1614236166130,"is_unique":false,"label":"归属部门","type":"department","is_need_convert":false,"is_required":false,"api_name":"data_own_department","define_type":"package","is_single":true,"index_name":"data_owner_dept_id","status":"released","description":""},"name":{"describe_api_name":"object_n2hbE__c","default_is_expression":false,"prefix":"","auto_adapt_places":false,"is_unique":true,"description":"","start_number":1,"type":"auto_number","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","postfix":"","label_r":"主属性","is_single":false,"index_name":"name","max_length":100,"is_index":true,"auto_number_type":"normal","is_active":true,"create_time":1614236166240,"is_encrypted":false,"default_value":"00000001","serial_number":8,"label":"主属性","condition":"NONE","api_name":"name","func_api_name":"","_id":"60374a0662cf0e0001d8c312","is_index_field":false,"status":"new","help_text":""},"mc_exchange_rate_version":{"describe_api_name":"object_n2hbE__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"汇率版本","is_single":false,"index_name":"t_3","max_length":256,"is_index":false,"is_active":true,"create_time":1614236166062,"is_encrypted":false,"default_value":"","label":"汇率版本","field_num":8,"api_name":"mc_exchange_rate_version","_id":"60374a0662cf0e0001d8c324","is_index_field":false,"status":"new","help_text":""},"_id":{"describe_api_name":"object_n2hbE__c","is_index":false,"is_active":true,"create_time":1614236166130,"pattern":"","description":"_id","is_unique":false,"label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","status":"released","max_length":200}},"release_version":"6.4","actions":{}}'''
        def describe = new ObjectDescribe()
        describe.fromJsonString(describeJson)
        def masterDescribeJson = '''{"tenant_id":"74255","package":"CRM","is_active":true,"last_modified_time":1656659222262,"create_time":1614236072433,"description":"","last_modified_by":"1000","display_name":"zwr-master1","created_by":"1000","version":46,"is_open_display_name":false,"index_version":200,"icon_index":0,"is_deleted":false,"api_name":"object_oenhP__c","icon_path":"","is_udef":true,"define_type":"custom","short_name":"muh","_id":"603749a862cf0e0001d8c2d5","fields":{"tenant_id":{"describe_api_name":"object_oenhP__c","is_index":false,"is_active":true,"create_time":1614236072433,"pattern":"","description":"tenant_id","is_unique":false,"label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","status":"released","max_length":200},"lock_rule":{"describe_api_name":"object_oenhP__c","is_index":false,"is_active":true,"create_time":1614236071860,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定规则","default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","field_num":1,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"603749a862cf0e0001d8c2cd","is_index_field":false,"label_r":"锁定规则","is_single":false,"index_name":"s_1","status":"new","help_text":""},"data_own_organization":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1647329971843,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"归属组织","type":"department","is_need_convert":false,"is_required":false,"api_name":"data_own_organization","define_type":"package","_id":"623042b404a720000162ea01","is_index_field":false,"label_r":"归属组织","is_single":true,"index_name":"a_2","status":"released","description":""},"field_051Dl__c":{"describe_api_name":"object_oenhP__c","return_type":"number","expression_type":"js","is_index":true,"expression":"$field_tj8e2__c$+1","is_active":true,"create_time":1614306611874,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段","type":"formula","field_num":13,"decimal_places":2,"default_to_zero":true,"is_required":false,"api_name":"field_051Dl__c","define_type":"custom","_id":"60385d346d4e290001505367","is_index_field":false,"is_single":false,"index_name":"d_1","status":"new","help_text":""},"lock_user":{"describe_api_name":"object_oenhP__c","is_index":false,"is_active":true,"create_time":1614236071861,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"加锁人","where_type":"field","label":"加锁人","type":"employee","field_num":4,"is_need_convert":false,"wheres":[],"is_required":false,"api_name":"lock_user","define_type":"package","_id":"603749a862cf0e0001d8c2cf","is_index_field":false,"label_r":"加锁人","is_single":true,"index_name":"a_1","status":"new","help_text":""},"field_tj8e2__c":{"describe_api_name":"object_oenhP__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_3","max_length":14,"is_index":true,"is_active":true,"create_time":1614236125928,"is_encrypted":false,"step_value":1,"display_style":"input","length":12,"default_value":"","label":"数字","field_num":11,"api_name":"field_tj8e2__c","_id":"603749de62cf0e0001d8c2ff","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"mc_exchange_rate":{"describe_api_name":"object_oenhP__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":6,"default_to_zero":true,"is_required":false,"define_type":"package","label_r":"汇率","is_single":false,"index_name":"d_2","max_length":16,"is_index":true,"is_active":true,"create_time":1614236072243,"is_encrypted":false,"step_value":1,"display_style":"input","length":10,"default_value":"","label":"汇率","field_num":6,"api_name":"mc_exchange_rate","_id":"603749a862cf0e0001d8c2d2","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"is_deleted":{"describe_api_name":"object_oenhP__c","is_index":false,"create_time":1614236072433,"description":"is_deleted","is_unique":false,"default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","define_type":"system","index_name":"is_del","status":"released"},"life_status_before_invalid":{"describe_api_name":"object_oenhP__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"作废前生命状态","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"作废前生命状态","is_single":false,"index_name":"t_1","max_length":256,"is_index":false,"is_active":true,"create_time":1614236071861,"is_encrypted":false,"default_value":"","label":"作废前生命状态","field_num":7,"is_need_convert":false,"api_name":"life_status_before_invalid","_id":"603749a862cf0e0001d8c2d0","is_index_field":false,"status":"new","help_text":""},"object_describe_api_name":{"describe_api_name":"object_oenhP__c","is_index":false,"is_active":true,"create_time":1614236072433,"pattern":"","description":"object_describe_api_name","is_unique":false,"label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","status":"released","max_length":200},"out_owner":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614236072433,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"config":{"display":1},"index_name":"o_owner","status":"released","description":""},"owner_department":{"describe_api_name":"object_oenhP__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"负责人主属部门","is_single":true,"index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1614236072233,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"603749a862cf0e0001d8c2c4","is_index_field":false,"status":"new","help_text":""},"mc_functional_currency":{"describe_api_name":"object_oenhP__c","is_index":false,"is_active":true,"create_time":1614236072244,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"本位币","type":"select_one","field_num":9,"is_required":false,"api_name":"mc_functional_currency","options":[{"label":"CNY - 人民币","value":"CNY"}],"define_type":"package","_id":"603749a862cf0e0001d8c2d3","is_index_field":false,"label_r":"本位币","is_single":false,"config":{},"index_name":"s_5","status":"new","help_text":""},"field_X02ow__c":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614244358350,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"单选","type":"select_one","field_num":12,"is_required":false,"api_name":"field_X02ow__c","options":[{"font_color":"#2a304d","label":"A","value":"3tY4kt93Q"},{"font_color":"#2a304d","label":"B","value":"9n0F7toOO"},{"font_color":"#2a304d","label":"C","value":"9xg1hKkbp"},{"font_color":"#2a304d","not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","_id":"60376a06ce88fe000183a339","is_index_field":false,"is_single":false,"config":{},"index_name":"s_4","status":"new","help_text":""},"owner":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614236072232,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"负责人","type":"employee","is_need_convert":false,"wheres":[],"is_required":true,"api_name":"owner","define_type":"package","_id":"603749a862cf0e0001d8c2c3","is_index_field":false,"label_r":"负责人","is_single":true,"index_name":"owner","status":"new","help_text":""},"last_modified_time":{"describe_api_name":"object_oenhP__c","is_index":true,"create_time":1614236072433,"description":"last_modified_time","is_unique":false,"label":"最后修改时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"md_time","status":"released"},"package":{"describe_api_name":"object_oenhP__c","is_index":false,"is_active":true,"create_time":1614236072433,"pattern":"","description":"package","is_unique":false,"label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","status":"released","max_length":200},"field_0Z122__c":{"describe_api_name":"object_oenhP__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_3","max_length":100,"is_index":true,"is_active":true,"create_time":1614236123264,"is_encrypted":false,"default_value":"","label":"单行文本","field_num":10,"api_name":"field_0Z122__c","_id":"603749db62cf0e0001d8c2fc","is_index_field":false,"status":"new","help_text":""},"lock_status":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614236071860,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定状态","default_value":"0","label":"锁定状态","type":"select_one","field_num":2,"is_need_convert":false,"is_required":false,"api_name":"lock_status","options":[{"font_color":"#2a304d","label":"未锁定","value":"0"},{"font_color":"#2a304d","label":"锁定","value":"1"}],"define_type":"package","_id":"603749a862cf0e0001d8c2ce","is_index_field":false,"label_r":"锁定状态","is_single":false,"config":{},"index_name":"s_3","status":"new","help_text":""},"create_time":{"describe_api_name":"object_oenhP__c","is_index":true,"create_time":1614236072433,"description":"create_time","is_unique":false,"label":"创建时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"crt_time","status":"released"},"life_status":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614236072240,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"normal","label":"生命状态","type":"select_one","field_num":3,"is_need_convert":false,"is_required":false,"api_name":"life_status","options":[{"font_color":"#2a304d","label":"未生效","value":"ineffective"},{"font_color":"#2a304d","label":"审核中","value":"under_review"},{"font_color":"#2a304d","label":"正常","value":"normal"},{"font_color":"#2a304d","label":"变更中","value":"in_change"},{"font_color":"#2a304d","label":"作废","value":"invalid"}],"define_type":"package","_id":"603749a862cf0e0001d8c2cb","is_index_field":false,"label_r":"生命状态","is_single":false,"config":{},"index_name":"s_2","status":"new","help_text":""},"last_modified_by":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614236072433,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released","description":""},"out_tenant_id":{"describe_api_name":"object_oenhP__c","is_index":false,"is_active":true,"create_time":1614236072433,"pattern":"","description":"out_tenant_id","is_unique":false,"label":"out_tenant_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","config":{"display":0},"index_name":"o_ei","status":"released","max_length":200},"created_by":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614236072433,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released","description":""},"version":{"describe_api_name":"object_oenhP__c","is_index":false,"create_time":1614236072433,"length":8,"description":"version","is_unique":false,"label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"mc_currency":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614236072242,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"币种","type":"select_one","field_num":5,"is_required":false,"api_name":"mc_currency","options":[{"label":"BHD - Bahraini Dinar","value":"BHD"},{"not_usable":true,"label":"BDT - 孟加拉国塔卡","value":"BDT"},{"not_usable":true,"label":"BGN - Bulgarian Lev","value":"BGN"},{"label":"AWG - 阿鲁巴岛弗罗林","value":"AWG"},{"label":"USD - U.S. Dollar","value":"USD"},{"label":"AED - UAE Dirham","value":"AED"},{"label":"ALL - 阿尔巴尼亚列克","value":"ALL"},{"label":"BAM - 自由兑换马克","value":"BAM"},{"label":"ANG - 荷属安地列斯盾","value":"ANG"},{"label":"ARS - 阿根廷比索","value":"ARS"},{"label":"AMD - 亚美尼亚打兰","value":"AMD"},{"label":"AUD - Australian Dollar","value":"AUD"},{"label":"AOA - 安哥拉宽扎","value":"AOA"},{"label":"BBD - 巴巴多斯元","value":"BBD"},{"label":"AFN - Afghanistan Afghani (New)","value":"AFN"},{"label":"AZN - 阿塞拜疆马纳特","value":"AZN"},{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"603749a862cf0e0001d8c2d1","is_index_field":false,"label_r":"币种","is_single":false,"config":{},"index_name":"s_6","status":"new","help_text":""},"record_type":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614236072239,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"record_type","label":"业务类型","type":"record_type","is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"font_color":"#2a304d","api_name":"default__c","description":"预设业务类型","label":"预设业务类型"},{"is_active":true,"font_color":"#2a304d","api_name":"record_Eqd5P__c","label":"业务类型1"}],"define_type":"package","_id":"603749a862cf0e0001d8c2ca","is_index_field":false,"label_r":"业务类型","is_single":false,"config":{},"index_name":"r_type","status":"released","help_text":""},"relevant_team":{"describe_api_name":"object_oenhP__c","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","description":"成员员工","define_type":"package","is_unique":false,"label":"成员员工","is_single":true,"type":"employee","help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"description":"成员角色","define_type":"package","is_unique":false,"label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"description":"成员权限类型","define_type":"package","is_unique":false,"label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1614236072241,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"603749a862cf0e0001d8c2cc","is_index_field":false,"label_r":"相关团队","is_single":false,"index_name":"a_team","status":"new","help_text":"相关团队"},"data_own_department":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614236072433,"is_unique":false,"label":"归属部门","type":"department","is_need_convert":false,"is_required":false,"api_name":"data_own_department","define_type":"package","is_single":true,"index_name":"data_owner_dept_id","status":"released","description":""},"field_C6j1e__c":{"describe_api_name":"object_oenhP__c","return_type":"number","auto_adapt_places":false,"is_unique":false,"type":"count","sub_object_describe_apiname":"object_n2hbE__c","decimal_places":2,"wheres":[],"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_4","field_api_name":"field_g12L5__c","default_result":"d_null","is_index":true,"is_active":true,"create_time":1656659221423,"is_encrypted":false,"count_type":"sum","count_field_api_name":"field_q3W9r__c","label":"统计字段-sum(detail1.数字)","field_num":14,"count_to_zero":false,"api_name":"field_C6j1e__c","count_field_type":"number","_id":"62be9d152b4d0700019ec00e","is_index_field":false,"round_mode":4,"status":"new","help_text":"","description":""},"name":{"describe_api_name":"object_oenhP__c","default_is_expression":false,"prefix":"","auto_adapt_places":false,"is_unique":true,"description":"","start_number":1,"type":"auto_number","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","postfix":"","label_r":"主属性","is_single":false,"index_name":"name","max_length":100,"is_index":true,"auto_number_type":"normal","is_active":true,"create_time":1614236072543,"is_encrypted":false,"default_value":"00000001","serial_number":8,"label":"主属性","condition":"NONE","api_name":"name","func_api_name":"","_id":"603749a862cf0e0001d8c2c2","is_index_field":false,"status":"new","help_text":""},"mc_exchange_rate_version":{"describe_api_name":"object_oenhP__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"汇率版本","is_single":false,"index_name":"t_2","max_length":256,"is_index":false,"is_active":true,"create_time":1614236072245,"is_encrypted":false,"default_value":"","label":"汇率版本","field_num":8,"api_name":"mc_exchange_rate_version","_id":"603749a862cf0e0001d8c2d4","is_index_field":false,"status":"new","help_text":""},"_id":{"describe_api_name":"object_oenhP__c","is_index":false,"is_active":true,"create_time":1614236072433,"pattern":"","description":"_id","is_unique":false,"label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","status":"released","max_length":200}},"release_version":"6.4","actions":{}}'''
        def masterDescribe = new ObjectDescribe()
        masterDescribe.fromJsonString(masterDescribeJson)
        def lookupDescribeJson = '''{"tenant_id":"74255","package":"CRM","is_active":true,"last_modified_time":1656659804831,"create_time":1656659703581,"description":"","last_modified_by":"1000","display_name":"zwr-lookup1","created_by":"1000","version":2,"is_open_display_name":false,"index_version":1,"icon_index":0,"is_deleted":false,"api_name":"object_e83CM__c","icon_path":"","is_udef":true,"define_type":"custom","short_name":"viF","_id":"62be9ef72b4d0700019ec338","fields":{"tenant_id":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703581,"pattern":"","description":"tenant_id","is_unique":false,"label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","status":"released","max_length":200},"lock_rule":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703440,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定规则","default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","field_num":1,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"62be9ef72b4d0700019ec32f","is_index_field":false,"label_r":"锁定规则","is_single":false,"index_name":"s_1","status":"new"},"data_own_organization":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703593,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"归属组织","type":"department","is_need_convert":false,"is_required":false,"api_name":"data_own_organization","define_type":"package","_id":"62be9ef72b4d0700019ec337","is_index_field":false,"label_r":"归属组织","is_single":true,"index_name":"a_1","status":"released","description":""},"lock_user":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703440,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"加锁人","label":"加锁人","type":"employee","field_num":4,"is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"62be9ef72b4d0700019ec331","is_index_field":false,"label_r":"加锁人","is_single":true,"index_name":"a_2","status":"new"},"mc_exchange_rate":{"describe_api_name":"object_e83CM__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":6,"default_to_zero":true,"is_required":false,"define_type":"package","label_r":"汇率","is_single":false,"index_name":"d_1","max_length":16,"is_index":true,"is_active":true,"create_time":1656659703590,"is_encrypted":false,"length":10,"label":"汇率","field_num":6,"api_name":"mc_exchange_rate","_id":"62be9ef72b4d0700019ec334","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"is_deleted":{"describe_api_name":"object_e83CM__c","is_index":false,"create_time":1656659703581,"description":"is_deleted","is_unique":false,"default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","define_type":"system","index_name":"is_del","status":"released"},"field_Yo91y__c":{"describe_api_name":"object_e83CM__c","return_type":"number","auto_adapt_places":false,"is_unique":false,"type":"count","sub_object_describe_apiname":"object_n2hbE__c","decimal_places":2,"wheres":[],"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_2","field_api_name":"field_VtKpk__c","default_result":"d_null","is_index":true,"is_active":true,"create_time":1656659804467,"is_encrypted":false,"count_type":"sum","count_field_api_name":"field_q3W9r__c","label":"统计字段-sum(detail1.数字)","field_num":10,"count_to_zero":false,"api_name":"field_Yo91y__c","count_field_type":"number","_id":"62be9f5c2b4d0700019ec383","is_index_field":false,"round_mode":4,"status":"new","help_text":"","description":""},"life_status_before_invalid":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703441,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"作废前生命状态","label":"作废前生命状态","type":"text","field_num":7,"is_need_convert":false,"is_required":false,"api_name":"life_status_before_invalid","define_type":"package","_id":"62be9ef72b4d0700019ec332","is_index_field":false,"label_r":"作废前生命状态","is_single":false,"index_name":"t_1","status":"new","max_length":256},"object_describe_api_name":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703581,"pattern":"","description":"object_describe_api_name","is_unique":false,"label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","status":"released","max_length":200},"out_owner":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703581,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"config":{"display":1},"index_name":"o_owner","status":"released","description":""},"owner_department":{"describe_api_name":"object_e83CM__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"负责人主属部门","is_single":true,"index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1656659703580,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"62be9ef72b4d0700019ec326","is_index_field":false,"status":"new","help_text":"","description":""},"mc_functional_currency":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703591,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"本位币","type":"select_one","field_num":9,"is_required":false,"api_name":"mc_functional_currency","options":[{"label":"CNY - 人民币","value":"CNY"}],"define_type":"package","_id":"62be9ef72b4d0700019ec335","is_index_field":false,"label_r":"本位币","is_single":false,"config":{},"index_name":"s_5","status":"new","help_text":""},"owner":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703579,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"where_type":"field","label":"负责人","type":"employee","is_need_convert":false,"wheres":[],"is_required":true,"api_name":"owner","define_type":"package","_id":"62be9ef72b4d0700019ec325","is_index_field":false,"label_r":"负责人","is_single":true,"index_name":"owner","status":"new","help_text":"","description":""},"last_modified_time":{"describe_api_name":"object_e83CM__c","is_index":true,"create_time":1656659703581,"description":"last_modified_time","is_unique":false,"label":"最后修改时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"md_time","status":"released"},"package":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703581,"pattern":"","description":"package","is_unique":false,"label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","status":"released","max_length":200},"lock_status":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703440,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定状态","default_value":"0","label":"锁定状态","type":"select_one","field_num":2,"is_need_convert":false,"is_required":false,"api_name":"lock_status","options":[{"label":"未锁定","value":"0"},{"label":"锁定","value":"1"}],"define_type":"package","_id":"62be9ef72b4d0700019ec330","is_index_field":false,"label_r":"锁定状态","is_single":false,"config":{},"index_name":"s_2","status":"new"},"create_time":{"describe_api_name":"object_e83CM__c","is_index":true,"create_time":1656659703581,"description":"create_time","is_unique":false,"label":"创建时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"crt_time","status":"released"},"life_status":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703587,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"default_value":"normal","label":"生命状态","type":"select_one","field_num":3,"is_need_convert":false,"is_required":false,"api_name":"life_status","options":[{"label":"未生效","value":"ineffective"},{"label":"审核中","value":"under_review"},{"label":"正常","value":"normal"},{"label":"变更中","value":"in_change"},{"label":"作废","value":"invalid"}],"define_type":"package","_id":"62be9ef72b4d0700019ec32d","is_index_field":false,"label_r":"生命状态","is_single":false,"config":{},"index_name":"s_3","status":"new","help_text":"","description":""},"last_modified_by":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703581,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released","description":""},"out_tenant_id":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703581,"pattern":"","description":"out_tenant_id","is_unique":false,"label":"out_tenant_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","config":{"display":0},"index_name":"o_ei","status":"released","max_length":200},"created_by":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703581,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released","description":""},"version":{"describe_api_name":"object_e83CM__c","is_index":false,"create_time":1656659703581,"length":8,"description":"version","is_unique":false,"label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"mc_currency":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703589,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"币种","type":"select_one","field_num":5,"is_required":false,"api_name":"mc_currency","options":[{"label":"BHD - Bahraini Dinar","value":"BHD"},{"not_usable":true,"label":"BDT - 孟加拉国塔卡","value":"BDT"},{"not_usable":true,"label":"BGN - Bulgarian Lev","value":"BGN"},{"label":"AWG - 阿鲁巴岛弗罗林","value":"AWG"},{"label":"USD - U.S. Dollar","value":"USD"},{"label":"AED - UAE Dirham","value":"AED"},{"label":"ALL - 阿尔巴尼亚列克","value":"ALL"},{"label":"BAM - 自由兑换马克","value":"BAM"},{"label":"ANG - 荷属安地列斯盾","value":"ANG"},{"label":"ARS - 阿根廷比索","value":"ARS"},{"label":"AMD - 亚美尼亚打兰","value":"AMD"},{"label":"AUD - Australian Dollar","value":"AUD"},{"label":"AOA - 安哥拉宽扎","value":"AOA"},{"label":"BBD - 巴巴多斯元","value":"BBD"},{"label":"AFN - Afghanistan Afghani (New)","value":"AFN"},{"label":"AZN - 阿塞拜疆马纳特","value":"AZN"},{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"62be9ef72b4d0700019ec333","is_index_field":false,"label_r":"币种","is_single":false,"config":{},"index_name":"s_4","status":"new","help_text":""},"record_type":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703586,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"record_type","label":"业务类型","type":"record_type","is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"api_name":"default__c","description":"预设业务类型","label":"预设业务类型"}],"define_type":"package","_id":"62be9ef72b4d0700019ec32c","is_index_field":false,"label_r":"业务类型","is_single":false,"config":{},"index_name":"r_type","status":"released","help_text":""},"relevant_team":{"describe_api_name":"object_e83CM__c","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","description":"成员员工","define_type":"package","is_unique":false,"label":"成员员工","is_single":true,"type":"employee","help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"description":"成员角色","define_type":"package","is_unique":false,"label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"description":"成员权限类型","define_type":"package","is_unique":false,"label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1656659703588,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"62be9ef72b4d0700019ec32e","is_index_field":false,"label_r":"相关团队","is_single":false,"index_name":"a_team","status":"new","help_text":"相关团队","description":""},"data_own_department":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703581,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"where_type":"field","label":"归属部门","type":"department","is_need_convert":false,"wheres":[],"is_required":false,"api_name":"data_own_department","define_type":"package","_id":"62be9ef72b4d0700019ec327","is_index_field":false,"label_r":"归属部门","is_single":true,"index_name":"data_owner_dept_id","status":"new","help_text":"","description":""},"name":{"describe_api_name":"object_e83CM__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":true,"description":"name","type":"text","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","label_r":"主属性","is_single":false,"index_name":"name","max_length":100,"is_index":true,"is_active":true,"create_time":1656659703622,"is_encrypted":false,"default_value":"","label":"主属性","api_name":"name","_id":"62be9ef72b4d0700019ec324","is_index_field":false,"status":"new","help_text":""},"mc_exchange_rate_version":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703592,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","label":"汇率版本","type":"text","field_num":8,"is_required":false,"api_name":"mc_exchange_rate_version","define_type":"package","_id":"62be9ef72b4d0700019ec336","is_index_field":false,"label_r":"汇率版本","is_single":false,"index_name":"t_2","status":"new","help_text":"","max_length":256},"_id":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703581,"pattern":"","description":"_id","is_unique":false,"label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","status":"released","max_length":200}},"release_version":"6.4","actions":{}}'''
        def lookupDescribe = new ObjectDescribe()
        lookupDescribe.fromJsonString(lookupDescribeJson)
        def describeMap = Maps.newHashMap()
        describeMap.put(describe.getApiName(), describe)
        describeMap.put(masterDescribe.getApiName(), masterDescribe)
        describeMap.put(lookupDescribe.getApiName(), lookupDescribe)
        def calculateFieldMapJson = '''{"object_e83CM__c":[{"fieldName":"field_Yo91y__c","order":0}],"object_oenhP__c":[{"fieldName":"field_C6j1e__c","order":0}],"object_n2hbE__c":[{"fieldName":"field_mc04S__c","order":0},{"fieldName":"field_13tbw__c","order":0},{"fieldName":"field_ZADt1__c","order":1},{"fieldName":"field_p6s3B__c","order":1}]}'''
        def calculateFieldMap = JSON.parseObject(calculateFieldMapJson, new TypeReference<Map<String, List<CalculateRelation.RelateField>>>() {
        })
        def data = new ObjectData()
        data.setDescribeApiName(describe.getApiName())
        data.set("field_g12L5__c", "master_id1")
        data.set("field_VtKpk__c", "lookup_id1")
        def dataList = Lists.newArrayList(data)

        metaDataFindService.findObjectDataByIdsIgnoreAll(_, _, _) >> {
            (it[1] as List).collect { id ->
                def dt = new ObjectData()
                dt.setId(id)
                dt.setDescribeApiName(it[2])
                dt.setTenantId(TENANT_ID)
                return dt
            }
        }
        when:
        metaDataComputeService.batchCalculateBySortFields(user, dataList, describeMap, calculateFieldMap)
        then:
        noExceptionThrown()
    }

    def "test batchCalculateBySortFieldsWithDetailData"() {
        given:
        def describeJson = '''{"tenant_id":"74255","package":"CRM","is_active":true,"last_modified_time":1656660026707,"create_time":1614236166130,"description":"","last_modified_by":"1000","display_name":"zwr-detail1","created_by":"1000","version":35,"index_version":200,"icon_index":0,"is_deleted":false,"api_name":"object_n2hbE__c","icon_path":"","is_udef":true,"define_type":"custom","short_name":"wqz","_id":"60374a0662cf0e0001d8c325","fields":{"tenant_id":{"describe_api_name":"object_n2hbE__c","is_index":false,"is_active":true,"create_time":1614236166130,"pattern":"","description":"tenant_id","is_unique":false,"label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","status":"released","max_length":200},"lock_rule":{"describe_api_name":"object_n2hbE__c","is_index":false,"is_active":true,"create_time":1614236165700,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定规则","default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","field_num":1,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"60374a0662cf0e0001d8c31d","is_index_field":false,"label_r":"锁定规则","is_single":false,"index_name":"s_2","status":"new","help_text":""},"data_own_organization":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1647329966637,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"归属组织","type":"department","is_need_convert":false,"is_required":false,"api_name":"data_own_organization","define_type":"package","_id":"623042ae04a720000162e9c3","is_index_field":false,"label_r":"归属组织","is_single":true,"index_name":"a_2","status":"released","description":""},"field_q3W9r__c":{"describe_api_name":"object_n2hbE__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_3","max_length":14,"is_index":true,"is_active":true,"create_time":1614236186521,"is_encrypted":false,"length":12,"default_value":"","label":"数字","field_num":12,"api_name":"field_q3W9r__c","_id":"60374a1a62cf0e0001d8c35f","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"field_ZADt1__c":{"describe_api_name":"object_n2hbE__c","return_type":"number","expression_type":"js","is_index":true,"expression":"$field_g12L5__c__r.field_C6j1e__c$","is_active":true,"create_time":1656659325947,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"计算字段-master1.统计","type":"formula","field_num":15,"decimal_places":2,"default_to_zero":true,"is_required":false,"api_name":"field_ZADt1__c","define_type":"custom","_id":"62be9d7e2b4d0700019ec108","is_index_field":false,"is_single":false,"index_name":"d_4","status":"new","help_text":"","description":""},"lock_user":{"describe_api_name":"object_n2hbE__c","is_index":false,"is_active":true,"create_time":1614236165701,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"加锁人","label":"加锁人","type":"employee","field_num":4,"is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"60374a0662cf0e0001d8c31f","is_index_field":false,"label_r":"加锁人","is_single":true,"index_name":"a_1","status":"new","help_text":""},"field_p6s3B__c":{"describe_api_name":"object_n2hbE__c","return_type":"number","expression_type":"js","is_index":true,"expression":"$field_VtKpk__c__r.field_Yo91y__c$","is_active":true,"create_time":1656659847348,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"计算字段-lookup1.统计","type":"formula","field_num":17,"decimal_places":2,"default_to_zero":true,"is_required":false,"api_name":"field_p6s3B__c","define_type":"custom","_id":"62be9f872b4d0700019ec39b","is_index_field":false,"is_single":false,"index_name":"d_5","status":"new","help_text":"","description":""},"mc_exchange_rate":{"describe_api_name":"object_n2hbE__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":6,"default_to_zero":true,"is_required":false,"define_type":"package","label_r":"汇率","is_single":false,"index_name":"d_2","max_length":16,"is_index":true,"is_active":true,"create_time":1614236166060,"is_encrypted":false,"length":10,"default_value":"","label":"汇率","field_num":6,"api_name":"mc_exchange_rate","_id":"60374a0662cf0e0001d8c322","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"field_VtKpk__c":{"describe_api_name":"object_n2hbE__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","wheres":[],"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_7","is_index":true,"is_active":true,"create_time":1656659762927,"is_encrypted":false,"target_api_name":"object_e83CM__c","label":"查找关联-lookup1","target_related_list_name":"target_related_list_2K2mF__c","field_num":16,"target_related_list_label":"zwr-detail1","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_VtKpk__c","_id":"62be9f332b4d0700019ec372","is_index_field":true,"status":"new","help_text":"","description":""},"is_deleted":{"describe_api_name":"object_n2hbE__c","is_index":false,"create_time":1614236166130,"description":"is_deleted","is_unique":false,"default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","define_type":"system","index_name":"is_del","status":"released"},"life_status_before_invalid":{"describe_api_name":"object_n2hbE__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"作废前生命状态","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"作废前生命状态","is_single":false,"index_name":"t_2","max_length":256,"is_index":false,"is_active":true,"create_time":1614236165701,"is_encrypted":false,"default_value":"","label":"作废前生命状态","field_num":7,"is_need_convert":false,"api_name":"life_status_before_invalid","_id":"60374a0662cf0e0001d8c320","is_index_field":false,"status":"new","help_text":""},"object_describe_api_name":{"describe_api_name":"object_n2hbE__c","is_index":false,"is_active":true,"create_time":1614236166130,"pattern":"","description":"object_describe_api_name","is_unique":false,"label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","status":"released","max_length":200},"field_mc04S__c":{"describe_api_name":"object_n2hbE__c","return_type":"text","expression_type":"js","is_index":true,"expression":"$field_g12L5__c__r.name$","is_active":true,"create_time":1630380476438,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"计算字段-master1.name","type":"formula","field_num":14,"default_to_zero":false,"is_required":false,"api_name":"field_mc04S__c","define_type":"custom","_id":"612da1bd9b64b6000133d4ff","is_index_field":false,"is_single":false,"index_name":"t_1","status":"new","help_text":"","description":""},"out_owner":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1614236166130,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"config":{"display":1},"index_name":"o_owner","status":"released","description":""},"owner_department":{"describe_api_name":"object_n2hbE__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"负责人主属部门","is_single":true,"index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1614236166050,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"60374a0662cf0e0001d8c314","is_index_field":false,"status":"new","help_text":""},"field_W1FfY__c":{"describe_api_name":"object_n2hbE__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_4","max_length":100,"is_index":true,"is_active":true,"create_time":1614236178962,"is_encrypted":false,"default_value":"","label":"单行文本","field_num":10,"api_name":"field_W1FfY__c","_id":"60374a1362cf0e0001d8c353","is_index_field":false,"status":"new","help_text":""},"mc_functional_currency":{"describe_api_name":"object_n2hbE__c","is_index":false,"is_active":true,"create_time":1614236166061,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"本位币","type":"select_one","field_num":9,"is_required":false,"api_name":"mc_functional_currency","options":[{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"60374a0662cf0e0001d8c323","is_index_field":false,"label_r":"本位币","is_single":false,"config":{},"index_name":"s_5","status":"new","help_text":""},"field_g12L5__c":{"describe_api_name":"object_n2hbE__c","auto_adapt_places":false,"is_unique":false,"description":"","type":"master_detail","is_required":true,"define_type":"custom","is_single":false,"index_name":"s_1","is_index":true,"is_active":true,"create_time":1614246772099,"is_encrypted":false,"target_api_name":"object_oenhP__c","label":"主从关系","show_detail_button":false,"target_related_list_name":"target_related_list_oz0Xz__c","field_num":13,"target_related_list_label":"zwr-detail1","api_name":"field_g12L5__c","is_create_when_master_create":true,"_id":"6037737511c2b00001cc0f85","is_required_when_master_create":true,"is_index_field":true,"status":"new","help_text":""},"owner":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1614236166049,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"负责人","type":"employee","is_need_convert":false,"is_required":true,"api_name":"owner","define_type":"package","_id":"60374a0662cf0e0001d8c313","is_index_field":false,"label_r":"负责人","is_single":true,"index_name":"owner","status":"new","help_text":""},"last_modified_time":{"describe_api_name":"object_n2hbE__c","is_index":true,"create_time":1614236166130,"description":"last_modified_time","is_unique":false,"label":"最后修改时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"md_time","status":"released"},"package":{"describe_api_name":"object_n2hbE__c","is_index":false,"is_active":true,"create_time":1614236166130,"pattern":"","description":"package","is_unique":false,"label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","status":"released","max_length":200},"lock_status":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1614236165701,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定状态","default_value":"0","label":"锁定状态","type":"select_one","field_num":2,"is_need_convert":false,"is_required":false,"api_name":"lock_status","options":[{"label":"未锁定","value":"0"},{"label":"锁定","value":"1"}],"define_type":"package","_id":"60374a0662cf0e0001d8c31e","is_index_field":false,"label_r":"锁定状态","is_single":false,"config":{},"index_name":"s_4","status":"new","help_text":""},"create_time":{"describe_api_name":"object_n2hbE__c","is_index":true,"create_time":1614236166130,"description":"create_time","is_unique":false,"label":"创建时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"crt_time","status":"released"},"life_status":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1614236166057,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"normal","label":"生命状态","type":"select_one","field_num":3,"is_need_convert":false,"is_required":false,"api_name":"life_status","options":[{"label":"未生效","value":"ineffective"},{"label":"审核中","value":"under_review"},{"label":"正常","value":"normal"},{"label":"变更中","value":"in_change"},{"label":"作废","value":"invalid"}],"define_type":"package","_id":"60374a0662cf0e0001d8c31b","is_index_field":false,"label_r":"生命状态","is_single":false,"config":{},"index_name":"s_3","status":"new","help_text":""},"field_sKiz7__c":{"describe_api_name":"object_n2hbE__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{},"is_unique":false,"description":"","type":"currency","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_1","max_length":14,"is_index":true,"is_active":true,"create_time":1614236183866,"is_encrypted":false,"length":12,"default_value":"","label":"金额","currency_unit":"￥","field_num":11,"api_name":"field_sKiz7__c","_id":"60374a1862cf0e0001d8c356","is_index_field":false,"is_show_mask":false,"round_mode":4,"status":"new","help_text":""},"field_13tbw__c":{"describe_api_name":"object_n2hbE__c","is_index":false,"is_active":true,"create_time":1656660026441,"is_encrypted":false,"auto_adapt_places":false,"quote_field_type":"text","is_unique":false,"label":"引用字段-lookup1.name","type":"quote","quote_field":"field_VtKpk__c__r.name","is_required":false,"api_name":"field_13tbw__c","define_type":"custom","_id":"62bea03a2b4d0700019ec3cd","is_index_field":false,"is_single":false,"index_name":"t_5","status":"new","help_text":"","description":""},"last_modified_by":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1614236166130,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released","description":""},"out_tenant_id":{"describe_api_name":"object_n2hbE__c","is_index":false,"is_active":true,"create_time":1614236166130,"pattern":"","description":"out_tenant_id","is_unique":false,"label":"out_tenant_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","config":{"display":0},"index_name":"o_ei","status":"released","max_length":200},"created_by":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1614236166130,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released","description":""},"version":{"describe_api_name":"object_n2hbE__c","is_index":false,"create_time":1614236166130,"length":8,"description":"version","is_unique":false,"label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"mc_currency":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1614236166059,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"币种","type":"select_one","field_num":5,"is_required":false,"api_name":"mc_currency","options":[{"label":"BHD - Bahraini Dinar","value":"BHD"},{"not_usable":true,"label":"BDT - 孟加拉国塔卡","value":"BDT"},{"not_usable":true,"label":"BGN - Bulgarian Lev","value":"BGN"},{"label":"AWG - 阿鲁巴岛弗罗林","value":"AWG"},{"label":"USD - U.S. Dollar","value":"USD"},{"label":"AED - UAE Dirham","value":"AED"},{"label":"ALL - 阿尔巴尼亚列克","value":"ALL"},{"label":"BAM - 自由兑换马克","value":"BAM"},{"label":"ANG - 荷属安地列斯盾","value":"ANG"},{"label":"ARS - 阿根廷比索","value":"ARS"},{"label":"AMD - 亚美尼亚打兰","value":"AMD"},{"label":"AUD - Australian Dollar","value":"AUD"},{"label":"AOA - 安哥拉宽扎","value":"AOA"},{"label":"BBD - 巴巴多斯元","value":"BBD"},{"label":"AFN - Afghanistan Afghani (New)","value":"AFN"},{"label":"AZN - 阿塞拜疆马纳特","value":"AZN"},{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"60374a0662cf0e0001d8c321","is_index_field":false,"label_r":"币种","is_single":false,"config":{},"index_name":"s_6","status":"new","help_text":""},"record_type":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1614236166056,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"record_type","label":"业务类型","type":"record_type","is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"api_name":"default__c","description":"预设业务类型","label":"预设业务类型"},{"is_active":true,"api_name":"record_2qvaY__c","label":"业务类型1"}],"define_type":"package","_id":"60374a0662cf0e0001d8c31a","is_index_field":false,"label_r":"业务类型","is_single":false,"config":{},"index_name":"r_type","status":"released","help_text":""},"relevant_team":{"describe_api_name":"object_n2hbE__c","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","description":"成员员工","define_type":"package","is_unique":false,"label":"成员员工","is_single":true,"type":"employee","help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"description":"成员角色","define_type":"package","is_unique":false,"label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"description":"成员权限类型","define_type":"package","is_unique":false,"label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1614236166058,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"60374a0662cf0e0001d8c31c","is_index_field":false,"label_r":"相关团队","is_single":false,"index_name":"a_team","status":"new","help_text":"相关团队"},"data_own_department":{"describe_api_name":"object_n2hbE__c","is_index":true,"is_active":true,"create_time":1614236166130,"is_unique":false,"label":"归属部门","type":"department","is_need_convert":false,"is_required":false,"api_name":"data_own_department","define_type":"package","is_single":true,"index_name":"data_owner_dept_id","status":"released","description":""},"name":{"describe_api_name":"object_n2hbE__c","default_is_expression":false,"prefix":"","auto_adapt_places":false,"is_unique":true,"description":"","start_number":1,"type":"auto_number","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","postfix":"","label_r":"主属性","is_single":false,"index_name":"name","max_length":100,"is_index":true,"auto_number_type":"normal","is_active":true,"create_time":1614236166240,"is_encrypted":false,"default_value":"00000001","serial_number":8,"label":"主属性","condition":"NONE","api_name":"name","func_api_name":"","_id":"60374a0662cf0e0001d8c312","is_index_field":false,"status":"new","help_text":""},"mc_exchange_rate_version":{"describe_api_name":"object_n2hbE__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"汇率版本","is_single":false,"index_name":"t_3","max_length":256,"is_index":false,"is_active":true,"create_time":1614236166062,"is_encrypted":false,"default_value":"","label":"汇率版本","field_num":8,"api_name":"mc_exchange_rate_version","_id":"60374a0662cf0e0001d8c324","is_index_field":false,"status":"new","help_text":""},"_id":{"describe_api_name":"object_n2hbE__c","is_index":false,"is_active":true,"create_time":1614236166130,"pattern":"","description":"_id","is_unique":false,"label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","status":"released","max_length":200}},"release_version":"6.4","actions":{}}'''
        def describe = new ObjectDescribe()
        describe.fromJsonString(describeJson)
        def masterDescribeJson = '''{"tenant_id":"74255","package":"CRM","is_active":true,"last_modified_time":1656659222262,"create_time":1614236072433,"description":"","last_modified_by":"1000","display_name":"zwr-master1","created_by":"1000","version":46,"is_open_display_name":false,"index_version":200,"icon_index":0,"is_deleted":false,"api_name":"object_oenhP__c","icon_path":"","is_udef":true,"define_type":"custom","short_name":"muh","_id":"603749a862cf0e0001d8c2d5","fields":{"tenant_id":{"describe_api_name":"object_oenhP__c","is_index":false,"is_active":true,"create_time":1614236072433,"pattern":"","description":"tenant_id","is_unique":false,"label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","status":"released","max_length":200},"lock_rule":{"describe_api_name":"object_oenhP__c","is_index":false,"is_active":true,"create_time":1614236071860,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定规则","default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","field_num":1,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"603749a862cf0e0001d8c2cd","is_index_field":false,"label_r":"锁定规则","is_single":false,"index_name":"s_1","status":"new","help_text":""},"data_own_organization":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1647329971843,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"归属组织","type":"department","is_need_convert":false,"is_required":false,"api_name":"data_own_organization","define_type":"package","_id":"623042b404a720000162ea01","is_index_field":false,"label_r":"归属组织","is_single":true,"index_name":"a_2","status":"released","description":""},"field_051Dl__c":{"describe_api_name":"object_oenhP__c","return_type":"number","expression_type":"js","is_index":true,"expression":"$field_tj8e2__c$+1","is_active":true,"create_time":1614306611874,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段","type":"formula","field_num":13,"decimal_places":2,"default_to_zero":true,"is_required":false,"api_name":"field_051Dl__c","define_type":"custom","_id":"60385d346d4e290001505367","is_index_field":false,"is_single":false,"index_name":"d_1","status":"new","help_text":""},"lock_user":{"describe_api_name":"object_oenhP__c","is_index":false,"is_active":true,"create_time":1614236071861,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"加锁人","where_type":"field","label":"加锁人","type":"employee","field_num":4,"is_need_convert":false,"wheres":[],"is_required":false,"api_name":"lock_user","define_type":"package","_id":"603749a862cf0e0001d8c2cf","is_index_field":false,"label_r":"加锁人","is_single":true,"index_name":"a_1","status":"new","help_text":""},"field_tj8e2__c":{"describe_api_name":"object_oenhP__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_3","max_length":14,"is_index":true,"is_active":true,"create_time":1614236125928,"is_encrypted":false,"step_value":1,"display_style":"input","length":12,"default_value":"","label":"数字","field_num":11,"api_name":"field_tj8e2__c","_id":"603749de62cf0e0001d8c2ff","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"mc_exchange_rate":{"describe_api_name":"object_oenhP__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":6,"default_to_zero":true,"is_required":false,"define_type":"package","label_r":"汇率","is_single":false,"index_name":"d_2","max_length":16,"is_index":true,"is_active":true,"create_time":1614236072243,"is_encrypted":false,"step_value":1,"display_style":"input","length":10,"default_value":"","label":"汇率","field_num":6,"api_name":"mc_exchange_rate","_id":"603749a862cf0e0001d8c2d2","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"is_deleted":{"describe_api_name":"object_oenhP__c","is_index":false,"create_time":1614236072433,"description":"is_deleted","is_unique":false,"default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","define_type":"system","index_name":"is_del","status":"released"},"life_status_before_invalid":{"describe_api_name":"object_oenhP__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"作废前生命状态","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"作废前生命状态","is_single":false,"index_name":"t_1","max_length":256,"is_index":false,"is_active":true,"create_time":1614236071861,"is_encrypted":false,"default_value":"","label":"作废前生命状态","field_num":7,"is_need_convert":false,"api_name":"life_status_before_invalid","_id":"603749a862cf0e0001d8c2d0","is_index_field":false,"status":"new","help_text":""},"object_describe_api_name":{"describe_api_name":"object_oenhP__c","is_index":false,"is_active":true,"create_time":1614236072433,"pattern":"","description":"object_describe_api_name","is_unique":false,"label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","status":"released","max_length":200},"out_owner":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614236072433,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"config":{"display":1},"index_name":"o_owner","status":"released","description":""},"owner_department":{"describe_api_name":"object_oenhP__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"负责人主属部门","is_single":true,"index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1614236072233,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"603749a862cf0e0001d8c2c4","is_index_field":false,"status":"new","help_text":""},"mc_functional_currency":{"describe_api_name":"object_oenhP__c","is_index":false,"is_active":true,"create_time":1614236072244,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"本位币","type":"select_one","field_num":9,"is_required":false,"api_name":"mc_functional_currency","options":[{"label":"CNY - 人民币","value":"CNY"}],"define_type":"package","_id":"603749a862cf0e0001d8c2d3","is_index_field":false,"label_r":"本位币","is_single":false,"config":{},"index_name":"s_5","status":"new","help_text":""},"field_X02ow__c":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614244358350,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"单选","type":"select_one","field_num":12,"is_required":false,"api_name":"field_X02ow__c","options":[{"font_color":"#2a304d","label":"A","value":"3tY4kt93Q"},{"font_color":"#2a304d","label":"B","value":"9n0F7toOO"},{"font_color":"#2a304d","label":"C","value":"9xg1hKkbp"},{"font_color":"#2a304d","not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","_id":"60376a06ce88fe000183a339","is_index_field":false,"is_single":false,"config":{},"index_name":"s_4","status":"new","help_text":""},"owner":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614236072232,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"负责人","type":"employee","is_need_convert":false,"wheres":[],"is_required":true,"api_name":"owner","define_type":"package","_id":"603749a862cf0e0001d8c2c3","is_index_field":false,"label_r":"负责人","is_single":true,"index_name":"owner","status":"new","help_text":""},"last_modified_time":{"describe_api_name":"object_oenhP__c","is_index":true,"create_time":1614236072433,"description":"last_modified_time","is_unique":false,"label":"最后修改时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"md_time","status":"released"},"package":{"describe_api_name":"object_oenhP__c","is_index":false,"is_active":true,"create_time":1614236072433,"pattern":"","description":"package","is_unique":false,"label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","status":"released","max_length":200},"field_0Z122__c":{"describe_api_name":"object_oenhP__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_3","max_length":100,"is_index":true,"is_active":true,"create_time":1614236123264,"is_encrypted":false,"default_value":"","label":"单行文本","field_num":10,"api_name":"field_0Z122__c","_id":"603749db62cf0e0001d8c2fc","is_index_field":false,"status":"new","help_text":""},"lock_status":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614236071860,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定状态","default_value":"0","label":"锁定状态","type":"select_one","field_num":2,"is_need_convert":false,"is_required":false,"api_name":"lock_status","options":[{"font_color":"#2a304d","label":"未锁定","value":"0"},{"font_color":"#2a304d","label":"锁定","value":"1"}],"define_type":"package","_id":"603749a862cf0e0001d8c2ce","is_index_field":false,"label_r":"锁定状态","is_single":false,"config":{},"index_name":"s_3","status":"new","help_text":""},"create_time":{"describe_api_name":"object_oenhP__c","is_index":true,"create_time":1614236072433,"description":"create_time","is_unique":false,"label":"创建时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"crt_time","status":"released"},"life_status":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614236072240,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"normal","label":"生命状态","type":"select_one","field_num":3,"is_need_convert":false,"is_required":false,"api_name":"life_status","options":[{"font_color":"#2a304d","label":"未生效","value":"ineffective"},{"font_color":"#2a304d","label":"审核中","value":"under_review"},{"font_color":"#2a304d","label":"正常","value":"normal"},{"font_color":"#2a304d","label":"变更中","value":"in_change"},{"font_color":"#2a304d","label":"作废","value":"invalid"}],"define_type":"package","_id":"603749a862cf0e0001d8c2cb","is_index_field":false,"label_r":"生命状态","is_single":false,"config":{},"index_name":"s_2","status":"new","help_text":""},"last_modified_by":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614236072433,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released","description":""},"out_tenant_id":{"describe_api_name":"object_oenhP__c","is_index":false,"is_active":true,"create_time":1614236072433,"pattern":"","description":"out_tenant_id","is_unique":false,"label":"out_tenant_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","config":{"display":0},"index_name":"o_ei","status":"released","max_length":200},"created_by":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614236072433,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released","description":""},"version":{"describe_api_name":"object_oenhP__c","is_index":false,"create_time":1614236072433,"length":8,"description":"version","is_unique":false,"label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"mc_currency":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614236072242,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"币种","type":"select_one","field_num":5,"is_required":false,"api_name":"mc_currency","options":[{"label":"BHD - Bahraini Dinar","value":"BHD"},{"not_usable":true,"label":"BDT - 孟加拉国塔卡","value":"BDT"},{"not_usable":true,"label":"BGN - Bulgarian Lev","value":"BGN"},{"label":"AWG - 阿鲁巴岛弗罗林","value":"AWG"},{"label":"USD - U.S. Dollar","value":"USD"},{"label":"AED - UAE Dirham","value":"AED"},{"label":"ALL - 阿尔巴尼亚列克","value":"ALL"},{"label":"BAM - 自由兑换马克","value":"BAM"},{"label":"ANG - 荷属安地列斯盾","value":"ANG"},{"label":"ARS - 阿根廷比索","value":"ARS"},{"label":"AMD - 亚美尼亚打兰","value":"AMD"},{"label":"AUD - Australian Dollar","value":"AUD"},{"label":"AOA - 安哥拉宽扎","value":"AOA"},{"label":"BBD - 巴巴多斯元","value":"BBD"},{"label":"AFN - Afghanistan Afghani (New)","value":"AFN"},{"label":"AZN - 阿塞拜疆马纳特","value":"AZN"},{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"603749a862cf0e0001d8c2d1","is_index_field":false,"label_r":"币种","is_single":false,"config":{},"index_name":"s_6","status":"new","help_text":""},"record_type":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614236072239,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"record_type","label":"业务类型","type":"record_type","is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"font_color":"#2a304d","api_name":"default__c","description":"预设业务类型","label":"预设业务类型"},{"is_active":true,"font_color":"#2a304d","api_name":"record_Eqd5P__c","label":"业务类型1"}],"define_type":"package","_id":"603749a862cf0e0001d8c2ca","is_index_field":false,"label_r":"业务类型","is_single":false,"config":{},"index_name":"r_type","status":"released","help_text":""},"relevant_team":{"describe_api_name":"object_oenhP__c","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","description":"成员员工","define_type":"package","is_unique":false,"label":"成员员工","is_single":true,"type":"employee","help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"description":"成员角色","define_type":"package","is_unique":false,"label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"description":"成员权限类型","define_type":"package","is_unique":false,"label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1614236072241,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"603749a862cf0e0001d8c2cc","is_index_field":false,"label_r":"相关团队","is_single":false,"index_name":"a_team","status":"new","help_text":"相关团队"},"data_own_department":{"describe_api_name":"object_oenhP__c","is_index":true,"is_active":true,"create_time":1614236072433,"is_unique":false,"label":"归属部门","type":"department","is_need_convert":false,"is_required":false,"api_name":"data_own_department","define_type":"package","is_single":true,"index_name":"data_owner_dept_id","status":"released","description":""},"field_C6j1e__c":{"describe_api_name":"object_oenhP__c","return_type":"number","auto_adapt_places":false,"is_unique":false,"type":"count","sub_object_describe_apiname":"object_n2hbE__c","decimal_places":2,"wheres":[],"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_4","field_api_name":"field_g12L5__c","default_result":"d_null","is_index":true,"is_active":true,"create_time":1656659221423,"is_encrypted":false,"count_type":"sum","count_field_api_name":"field_q3W9r__c","label":"统计字段-sum(detail1.数字)","field_num":14,"count_to_zero":false,"api_name":"field_C6j1e__c","count_field_type":"number","_id":"62be9d152b4d0700019ec00e","is_index_field":false,"round_mode":4,"status":"new","help_text":"","description":""},"name":{"describe_api_name":"object_oenhP__c","default_is_expression":false,"prefix":"","auto_adapt_places":false,"is_unique":true,"description":"","start_number":1,"type":"auto_number","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","postfix":"","label_r":"主属性","is_single":false,"index_name":"name","max_length":100,"is_index":true,"auto_number_type":"normal","is_active":true,"create_time":1614236072543,"is_encrypted":false,"default_value":"00000001","serial_number":8,"label":"主属性","condition":"NONE","api_name":"name","func_api_name":"","_id":"603749a862cf0e0001d8c2c2","is_index_field":false,"status":"new","help_text":""},"mc_exchange_rate_version":{"describe_api_name":"object_oenhP__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"汇率版本","is_single":false,"index_name":"t_2","max_length":256,"is_index":false,"is_active":true,"create_time":1614236072245,"is_encrypted":false,"default_value":"","label":"汇率版本","field_num":8,"api_name":"mc_exchange_rate_version","_id":"603749a862cf0e0001d8c2d4","is_index_field":false,"status":"new","help_text":""},"_id":{"describe_api_name":"object_oenhP__c","is_index":false,"is_active":true,"create_time":1614236072433,"pattern":"","description":"_id","is_unique":false,"label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","status":"released","max_length":200}},"release_version":"6.4","actions":{}}'''
        def masterDescribe = new ObjectDescribe()
        masterDescribe.fromJsonString(masterDescribeJson)
        def lookupDescribeJson = '''{"tenant_id":"74255","package":"CRM","is_active":true,"last_modified_time":1656659804831,"create_time":1656659703581,"description":"","last_modified_by":"1000","display_name":"zwr-lookup1","created_by":"1000","version":2,"is_open_display_name":false,"index_version":1,"icon_index":0,"is_deleted":false,"api_name":"object_e83CM__c","icon_path":"","is_udef":true,"define_type":"custom","short_name":"viF","_id":"62be9ef72b4d0700019ec338","fields":{"tenant_id":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703581,"pattern":"","description":"tenant_id","is_unique":false,"label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","status":"released","max_length":200},"lock_rule":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703440,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定规则","default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","field_num":1,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"62be9ef72b4d0700019ec32f","is_index_field":false,"label_r":"锁定规则","is_single":false,"index_name":"s_1","status":"new"},"data_own_organization":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703593,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"归属组织","type":"department","is_need_convert":false,"is_required":false,"api_name":"data_own_organization","define_type":"package","_id":"62be9ef72b4d0700019ec337","is_index_field":false,"label_r":"归属组织","is_single":true,"index_name":"a_1","status":"released","description":""},"lock_user":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703440,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"加锁人","label":"加锁人","type":"employee","field_num":4,"is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"62be9ef72b4d0700019ec331","is_index_field":false,"label_r":"加锁人","is_single":true,"index_name":"a_2","status":"new"},"mc_exchange_rate":{"describe_api_name":"object_e83CM__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":6,"default_to_zero":true,"is_required":false,"define_type":"package","label_r":"汇率","is_single":false,"index_name":"d_1","max_length":16,"is_index":true,"is_active":true,"create_time":1656659703590,"is_encrypted":false,"length":10,"label":"汇率","field_num":6,"api_name":"mc_exchange_rate","_id":"62be9ef72b4d0700019ec334","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"is_deleted":{"describe_api_name":"object_e83CM__c","is_index":false,"create_time":1656659703581,"description":"is_deleted","is_unique":false,"default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","define_type":"system","index_name":"is_del","status":"released"},"field_Yo91y__c":{"describe_api_name":"object_e83CM__c","return_type":"number","auto_adapt_places":false,"is_unique":false,"type":"count","sub_object_describe_apiname":"object_n2hbE__c","decimal_places":2,"wheres":[],"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_2","field_api_name":"field_VtKpk__c","default_result":"d_null","is_index":true,"is_active":true,"create_time":1656659804467,"is_encrypted":false,"count_type":"sum","count_field_api_name":"field_q3W9r__c","label":"统计字段-sum(detail1.数字)","field_num":10,"count_to_zero":false,"api_name":"field_Yo91y__c","count_field_type":"number","_id":"62be9f5c2b4d0700019ec383","is_index_field":false,"round_mode":4,"status":"new","help_text":"","description":""},"life_status_before_invalid":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703441,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"作废前生命状态","label":"作废前生命状态","type":"text","field_num":7,"is_need_convert":false,"is_required":false,"api_name":"life_status_before_invalid","define_type":"package","_id":"62be9ef72b4d0700019ec332","is_index_field":false,"label_r":"作废前生命状态","is_single":false,"index_name":"t_1","status":"new","max_length":256},"object_describe_api_name":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703581,"pattern":"","description":"object_describe_api_name","is_unique":false,"label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","status":"released","max_length":200},"out_owner":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703581,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"config":{"display":1},"index_name":"o_owner","status":"released","description":""},"owner_department":{"describe_api_name":"object_e83CM__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"负责人主属部门","is_single":true,"index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1656659703580,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"62be9ef72b4d0700019ec326","is_index_field":false,"status":"new","help_text":"","description":""},"mc_functional_currency":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703591,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"本位币","type":"select_one","field_num":9,"is_required":false,"api_name":"mc_functional_currency","options":[{"label":"CNY - 人民币","value":"CNY"}],"define_type":"package","_id":"62be9ef72b4d0700019ec335","is_index_field":false,"label_r":"本位币","is_single":false,"config":{},"index_name":"s_5","status":"new","help_text":""},"owner":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703579,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"where_type":"field","label":"负责人","type":"employee","is_need_convert":false,"wheres":[],"is_required":true,"api_name":"owner","define_type":"package","_id":"62be9ef72b4d0700019ec325","is_index_field":false,"label_r":"负责人","is_single":true,"index_name":"owner","status":"new","help_text":"","description":""},"last_modified_time":{"describe_api_name":"object_e83CM__c","is_index":true,"create_time":1656659703581,"description":"last_modified_time","is_unique":false,"label":"最后修改时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"md_time","status":"released"},"package":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703581,"pattern":"","description":"package","is_unique":false,"label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","status":"released","max_length":200},"lock_status":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703440,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定状态","default_value":"0","label":"锁定状态","type":"select_one","field_num":2,"is_need_convert":false,"is_required":false,"api_name":"lock_status","options":[{"label":"未锁定","value":"0"},{"label":"锁定","value":"1"}],"define_type":"package","_id":"62be9ef72b4d0700019ec330","is_index_field":false,"label_r":"锁定状态","is_single":false,"config":{},"index_name":"s_2","status":"new"},"create_time":{"describe_api_name":"object_e83CM__c","is_index":true,"create_time":1656659703581,"description":"create_time","is_unique":false,"label":"创建时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"crt_time","status":"released"},"life_status":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703587,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"default_value":"normal","label":"生命状态","type":"select_one","field_num":3,"is_need_convert":false,"is_required":false,"api_name":"life_status","options":[{"label":"未生效","value":"ineffective"},{"label":"审核中","value":"under_review"},{"label":"正常","value":"normal"},{"label":"变更中","value":"in_change"},{"label":"作废","value":"invalid"}],"define_type":"package","_id":"62be9ef72b4d0700019ec32d","is_index_field":false,"label_r":"生命状态","is_single":false,"config":{},"index_name":"s_3","status":"new","help_text":"","description":""},"last_modified_by":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703581,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released","description":""},"out_tenant_id":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703581,"pattern":"","description":"out_tenant_id","is_unique":false,"label":"out_tenant_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","config":{"display":0},"index_name":"o_ei","status":"released","max_length":200},"created_by":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703581,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released","description":""},"version":{"describe_api_name":"object_e83CM__c","is_index":false,"create_time":1656659703581,"length":8,"description":"version","is_unique":false,"label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"mc_currency":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703589,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"币种","type":"select_one","field_num":5,"is_required":false,"api_name":"mc_currency","options":[{"label":"BHD - Bahraini Dinar","value":"BHD"},{"not_usable":true,"label":"BDT - 孟加拉国塔卡","value":"BDT"},{"not_usable":true,"label":"BGN - Bulgarian Lev","value":"BGN"},{"label":"AWG - 阿鲁巴岛弗罗林","value":"AWG"},{"label":"USD - U.S. Dollar","value":"USD"},{"label":"AED - UAE Dirham","value":"AED"},{"label":"ALL - 阿尔巴尼亚列克","value":"ALL"},{"label":"BAM - 自由兑换马克","value":"BAM"},{"label":"ANG - 荷属安地列斯盾","value":"ANG"},{"label":"ARS - 阿根廷比索","value":"ARS"},{"label":"AMD - 亚美尼亚打兰","value":"AMD"},{"label":"AUD - Australian Dollar","value":"AUD"},{"label":"AOA - 安哥拉宽扎","value":"AOA"},{"label":"BBD - 巴巴多斯元","value":"BBD"},{"label":"AFN - Afghanistan Afghani (New)","value":"AFN"},{"label":"AZN - 阿塞拜疆马纳特","value":"AZN"},{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"62be9ef72b4d0700019ec333","is_index_field":false,"label_r":"币种","is_single":false,"config":{},"index_name":"s_4","status":"new","help_text":""},"record_type":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703586,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"record_type","label":"业务类型","type":"record_type","is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"api_name":"default__c","description":"预设业务类型","label":"预设业务类型"}],"define_type":"package","_id":"62be9ef72b4d0700019ec32c","is_index_field":false,"label_r":"业务类型","is_single":false,"config":{},"index_name":"r_type","status":"released","help_text":""},"relevant_team":{"describe_api_name":"object_e83CM__c","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","description":"成员员工","define_type":"package","is_unique":false,"label":"成员员工","is_single":true,"type":"employee","help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"description":"成员角色","define_type":"package","is_unique":false,"label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"description":"成员权限类型","define_type":"package","is_unique":false,"label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1656659703588,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"62be9ef72b4d0700019ec32e","is_index_field":false,"label_r":"相关团队","is_single":false,"index_name":"a_team","status":"new","help_text":"相关团队","description":""},"data_own_department":{"describe_api_name":"object_e83CM__c","is_index":true,"is_active":true,"create_time":1656659703581,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"where_type":"field","label":"归属部门","type":"department","is_need_convert":false,"wheres":[],"is_required":false,"api_name":"data_own_department","define_type":"package","_id":"62be9ef72b4d0700019ec327","is_index_field":false,"label_r":"归属部门","is_single":true,"index_name":"data_owner_dept_id","status":"new","help_text":"","description":""},"name":{"describe_api_name":"object_e83CM__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":true,"description":"name","type":"text","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","label_r":"主属性","is_single":false,"index_name":"name","max_length":100,"is_index":true,"is_active":true,"create_time":1656659703622,"is_encrypted":false,"default_value":"","label":"主属性","api_name":"name","_id":"62be9ef72b4d0700019ec324","is_index_field":false,"status":"new","help_text":""},"mc_exchange_rate_version":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703592,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","label":"汇率版本","type":"text","field_num":8,"is_required":false,"api_name":"mc_exchange_rate_version","define_type":"package","_id":"62be9ef72b4d0700019ec336","is_index_field":false,"label_r":"汇率版本","is_single":false,"index_name":"t_2","status":"new","help_text":"","max_length":256},"_id":{"describe_api_name":"object_e83CM__c","is_index":false,"is_active":true,"create_time":1656659703581,"pattern":"","description":"_id","is_unique":false,"label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","status":"released","max_length":200}},"release_version":"6.4","actions":{}}'''
        def lookupDescribe = new ObjectDescribe()
        lookupDescribe.fromJsonString(lookupDescribeJson)
        def describeMap = Maps.newHashMap()
        describeMap.put(describe.getApiName(), describe)
        describeMap.put(masterDescribe.getApiName(), masterDescribe)
        describeMap.put(lookupDescribe.getApiName(), lookupDescribe)
        def calculateFieldMapJson = '''{"object_e83CM__c":[{"fieldName":"field_Yo91y__c","order":0}],"object_oenhP__c":[{"fieldName":"field_C6j1e__c","order":0},{"fieldName":"field_051Dl__c","order":0}],"object_n2hbE__c":[{"fieldName":"field_mc04S__c","order":0},{"fieldName":"field_13tbw__c","order":0},{"fieldName":"field_ZADt1__c","order":1},{"fieldName":"field_p6s3B__c","order":1}]}'''
        def calculateFieldMap = JSON.parseObject(calculateFieldMapJson, new TypeReference<Map<String, List<CalculateRelation.RelateField>>>() {
        })
        def detailData = new ObjectData()
        detailData.setDescribeApiName(describe.getApiName())
        detailData.set("field_g12L5__c", "master_id1")
        detailData.set("field_VtKpk__c", "lookup_id1")
        def detailDataMap = Maps.newHashMap()
        detailDataMap.put(describe.getApiName(), Lists.newArrayList(CalculateObjectData.of("1", detailData)))
        def masterData = new ObjectData()
        masterData.setDescribeApiName(masterDescribe.getApiName())

        metaDataFindService.findObjectDataByIdsIgnoreAll(_, _, _) >> {
            (it[1] as List).collect { id ->
                def dt = new ObjectData()
                dt.setId(id)
                dt.setDescribeApiName(it[2])
                dt.setTenantId(TENANT_ID)
                return dt
            }
        }
        when:
        metaDataComputeService.batchCalculateBySortFieldsWithDetailData(user, masterData, detailDataMap, describeMap, calculateFieldMap)
        then:
        noExceptionThrown()
    }

    def "test batchCalculate with empty param"() {
        given:
        List<IObjectData> dataList = []
        List<String> calculateFieldNames = []
        when:
        metaDataComputeService.batchCalculate(user, dataList, null, calculateFieldNames, false, false)
        then:
        noExceptionThrown()
    }

    def "test batchCalculate with calculateFormulaOnly true"() {
        given:
        List<IObjectData> dataList = [new ObjectData(["_id": "id1", "object_describe_api_name": "testApiName"])]
        List<String> calculateFieldNames = ["field1"]
        IObjectDescribe describe = new ObjectDescribe(["api_name": "testApiName", "fields": ["field1": ["describe_api_name": "testApiName", "api_name": "field1", "type": "formula", "expression": "1", "return_type": "number", "decimal_places": 2]]])
        when:
        metaDataComputeService.batchCalculate(user, dataList, describe, calculateFieldNames, true, true)
        then:
        noExceptionThrown()
    }

    def "test batchCalculate with calculateFormulaOnly false"() {
        given:
        List<IObjectData> dataList = [new ObjectData(["_id": "id1", "object_describe_api_name": "testApiName"])]
        List<String> calculateFieldNames = ["field1"]
        IObjectDescribe describe = new ObjectDescribe(["api_name": "testApiName", "fields": ["field1": ["describe_api_name": "testApiName", "api_name": "field1", "type": "formula", "expression": "1", "return_type": "number", "decimal_places": 2]]])
        FieldRelationGraph graph = FieldRelationGraphBuilder.builder().graphLayers([FieldRelationGraphBuilder.GraphLayer.of([describe])]).build().getGraph()
        when:
        fieldRelationCalculateService.computeCalculateFields(_, _) >> { CalculateFields.of(graph, ["testApiName": [CalculateRelation.RelateField.of("field1", 1, "F")]]) }
        metaDataComputeService.batchCalculate(user, dataList, describe, calculateFieldNames, false, true)
        then:
        noExceptionThrown()
    }

    def "test batchCalculateBySortFields with calculateDataMap"() {
        given:
        IObjectData masterData = new ObjectData(["_id": "id1", "object_describe_api_name": "testApiName"])
        IObjectDescribe describe = new ObjectDescribe(["api_name": "testApiName", "fields": ["field1": ["describe_api_name": "testApiName", "api_name": "field1", "type": "formula", "expression": "1", "return_type": "number", "decimal_places": 2]]])
        IObjectDescribe detailDescribe = new ObjectDescribe(["api_name": "detail1", "fields": ["d_field1": ["describe_api_name": "detail1", "api_name": "d_field1", "type": "formula", "expression": "2", "return_type": "number", "decimal_places": 2], "md_field": ["api_name": "md_field1", "type": "master_detail", "target_api_name": "testApiName"]]])
        FieldRelationGraph graph = FieldRelationGraphBuilder.builder().graphLayers([FieldRelationGraphBuilder.GraphLayer.of([describe, detailDescribe])]).build().getGraph()
        Map<String, Map<String, Set<CalculateRelation.RelateField>>> relateFieldMap = ["testApiName": ["id1": Sets.newHashSet([CalculateRelation.RelateField.of("field1", 1, "F")])]]
        Map<String, List<CalculateObjectData>> calculateDataMap = ["detail1": [CalculateObjectData.of("d_id1", new ObjectData(["_id": "d_id1", "object_describe_api_name": "detail1"]))]]
        CalculateFields calculateFields = CalculateFields.of(graph, null, relateFieldMap, calculateDataMap)
        when:
        metaDataComputeService.batchCalculateBySortFields(user, masterData, null, calculateFields)
        then:
        noExceptionThrown()
    }

}
