package com.facishare.paas.appframework.core.predef.service.dto.bizconf;

import com.fxiaoke.bizconf.bean.ConfigPojo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by zhouwr on 2020/7/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigData {
    String configCode;
    String bizTypeValue;
    String configValue;

    public static ConfigData of(ConfigPojo configPojo) {
        return ConfigData.builder()
                .configCode(configPojo.getKey())
                .bizTypeValue(configPojo.getAssistantKey())
                .configValue(configPojo.getConfigValue())
                .build();
    }
}
