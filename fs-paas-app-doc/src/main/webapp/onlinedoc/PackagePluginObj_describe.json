{"fields": {"tenant_id": {"type": "text", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": true, "is_unique": false, "label": "企业id", "api_name": "tenant_id", "description": "企业id", "default_value": true, "status": "released"}, "define_type": {"type": "text", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": true, "is_unique": false, "label": "预制自定义", "api_name": "define_type", "description": "预制", "default_value": true, "status": "released"}, "app_type": {"type": "text", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": true, "is_unique": false, "label": "应用类型", "api_name": "app_type", "description": "应用类型", "default_value": true, "status": "released"}, "name": {"type": "text", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": true, "is_unique": false, "label": "名称", "api_name": "name", "description": "名称", "default_value": true, "status": "released"}, "icon": {"type": "text", "define_type": "package", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "label": "图标", "api_name": "icon", "description": "图标", "default_value": true, "status": "released"}, "icon_sign": {"type": "text", "define_type": "package", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "label": "图标签名", "api_name": "icon_sign", "description": "图标签名", "default_value": true, "status": "released"}, "is_active": {"type": "true_or_false", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "是否启用", "api_name": "is_active", "description": "是否启用", "default_value": true, "status": "released"}, "is_binding": {"type": "true_or_false", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "是否绑定", "api_name": "is_binding", "description": "是否绑定", "default_value": true, "status": "released"}, "plugin_api_name": {"type": "text", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": true, "is_unique": false, "label": "插件apiName", "api_name": "plugin_api_name", "description": "插件apiName", "default_value": true, "status": "released"}, "extra_info": {"expression_type": "json", "type": "long_text", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "扩展信息", "api_name": "extra_info", "description": "扩展信息", "default_value": true, "status": "released"}, "dev_info": {"expression_type": "json", "type": "long_text", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "开发信息", "api_name": "dev_info", "description": "开发信息", "default_value": true, "status": "released"}, "runtime_data": {"expression_type": "json", "type": "long_text", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "运行时数据", "api_name": "runtime_data", "description": "运行时数据", "default_value": true, "status": "released"}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 64, "pattern": "", "label": "object_describe_api_name", "is_active": true, "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released", "index_name": "api_name"}, "created_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "created_by", "status": "released", "label": "创建人", "is_active": true, "index_name": "crt_by"}, "last_modified_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "last_modified_by", "status": "released", "is_active": true, "index_name": "md_by", "label": "最后修改人"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "创建时间", "status": "released", "index_name": "crt_time"}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "最后修改时间", "status": "released", "index_name": "md_time"}, "is_deleted": {"type": "true_or_false", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "是否删除", "api_name": "is_deleted", "description": "是否删除", "default_value": false, "status": "released", "index_name": "is_del"}}, "tenant_id": "-100", "api_name": "PackagePluginObj", "display_name": "包插件安装记录", "package": "CRM", "is_active": true, "release_version": "6.4", "define_type": "internal", "is_deleted": false, "store_table_name": "dev_package_plugin"}