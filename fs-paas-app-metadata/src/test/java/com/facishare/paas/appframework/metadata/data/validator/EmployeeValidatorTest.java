package com.facishare.paas.appframework.metadata.data.validator;

import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * EmployeeValidator 单元测试
 */
@ExtendWith(MockitoExtension.class)
class EmployeeValidatorTest {

  private EmployeeValidator validator;

  @BeforeEach
  void setUp() {
    validator = new EmployeeValidator();
  }

  @Test
  void testSupportFieldTypes() {
    Set<String> supportedTypes = validator.supportFieldTypes();
    
    assertEquals(3, supportedTypes.size());
    assertTrue(supportedTypes.contains(IFieldType.EMPLOYEE));
    assertTrue(supportedTypes.contains(IFieldType.EMPLOYEE_MANY));
    assertTrue(supportedTypes.contains(IFieldType.OUT_EMPLOYEE));
  }

  @Test
  void testValidateDataType_WithValidData_ShouldCallObjectDataExt() {
    // Given
    String fieldApiName = "test_field";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.EMPLOYEE);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, "emp_001");
    IObjectData objectData = new ObjectData(dataMap);
    
    ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);

    try (MockedStatic<ObjectDataExt> mockedStatic = mockStatic(ObjectDataExt.class)) {
      mockedStatic.when(() -> ObjectDataExt.of(objectData)).thenReturn(mockObjectDataExt);

      // When
      validator.validateDataType(fieldApiName, objectData, describe);

      // Then
      verify(mockObjectDataExt).validateEmployeeFieldDataType(any(IFieldDescribe.class));
    }
  }

  @Test
  void testValidateDataType_WithEmployeeManyType_ShouldCallObjectDataExt() {
    // Given
    String fieldApiName = "test_field";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.EMPLOYEE_MANY);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, Arrays.asList("emp_001", "emp_002"));
    IObjectData objectData = new ObjectData(dataMap);
    
    ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);

    try (MockedStatic<ObjectDataExt> mockedStatic = mockStatic(ObjectDataExt.class)) {
      mockedStatic.when(() -> ObjectDataExt.of(objectData)).thenReturn(mockObjectDataExt);

      // When
      validator.validateDataType(fieldApiName, objectData, describe);

      // Then
      verify(mockObjectDataExt).validateEmployeeFieldDataType(any(IFieldDescribe.class));
    }
  }

  @Test
  void testValidateDataType_WithOutEmployeeType_ShouldCallObjectDataExt() {
    // Given
    String fieldApiName = "test_field";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.OUT_EMPLOYEE);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, "out_emp_001");
    IObjectData objectData = new ObjectData(dataMap);
    
    ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);

    try (MockedStatic<ObjectDataExt> mockedStatic = mockStatic(ObjectDataExt.class)) {
      mockedStatic.when(() -> ObjectDataExt.of(objectData)).thenReturn(mockObjectDataExt);

      // When
      validator.validateDataType(fieldApiName, objectData, describe);

      // Then
      verify(mockObjectDataExt).validateEmployeeFieldDataType(any(IFieldDescribe.class));
    }
  }

  @Test
  void testValidateDataType_WithValidationException_ShouldThrowException() {
    // Given
    String fieldApiName = "test_field";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.EMPLOYEE);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, "invalid_emp_id");
    IObjectData objectData = new ObjectData(dataMap);

    ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);
    doThrow(new RuntimeException("Invalid employee data")).when(mockObjectDataExt).validateEmployeeFieldDataType(any(IFieldDescribe.class));

    try (MockedStatic<ObjectDataExt> mockedStatic = mockStatic(ObjectDataExt.class)) {
      mockedStatic.when(() -> ObjectDataExt.of(objectData)).thenReturn(mockObjectDataExt);

      // When & Then
      RuntimeException exception = assertThrows(RuntimeException.class,
          () -> validator.validateDataType(fieldApiName, objectData, describe));

      assertEquals("Invalid employee data", exception.getMessage());
    }
  }

  @Test
  void testValidateDataType_WithNullFieldApiName_ShouldReturn() {
    // Given
    String fieldApiName = null;
    IObjectDescribe describe = createObjectDescribe("test_field", IFieldType.EMPLOYEE);
    IObjectData objectData = new ObjectData(Collections.emptyMap());

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithEmptyFieldApiName_ShouldReturn() {
    // Given
    String fieldApiName = "";
    IObjectDescribe describe = createObjectDescribe("test_field", IFieldType.EMPLOYEE);
    IObjectData objectData = new ObjectData(Collections.emptyMap());

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithNullData_ShouldReturn() {
    // Given
    String fieldApiName = "test_field";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.EMPLOYEE);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, null, describe));
  }

  @Test
  void testValidateDataType_WithNullDescribe_ShouldReturn() {
    // Given
    String fieldApiName = "test_field";
    IObjectData objectData = new ObjectData(Collections.emptyMap());

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, null));
  }

  /**
   * 创建测试用的 IObjectDescribe 对象
   */
  private IObjectDescribe createObjectDescribe(String fieldApiName, String fieldType) {
    IObjectDescribe describe = new ObjectDescribe();
    describe.setApiName("TestObj");
    
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", fieldApiName);
    fieldMap.put("type", fieldType);
    fieldMap.put("label", "测试字段");
    
    IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
    
    describe.setFieldDescribes(Arrays.asList(fieldDescribe));
    return describe;
  }
}
