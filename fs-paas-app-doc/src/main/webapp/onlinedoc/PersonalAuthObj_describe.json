{"fields": {"tenant_id": {"type": "text", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": true, "is_unique": false, "label": "企业id", "api_name": "tenant_id", "description": "企业id", "default_value": true, "status": "released"}, "employee_id": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "employee_id", "status": "released", "label": "人员id", "is_active": true, "index_name": "crt_by"}, "app_type": {"type": "text", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": true, "is_unique": false, "label": "应用类型", "api_name": "app_type", "description": "应用类型", "default_value": true, "status": "released"}, "plugin_api_name": {"type": "text", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": true, "is_unique": false, "label": "插件apiName", "api_name": "plugin_api_name", "description": "插件apiName", "default_value": true, "status": "released"}, "runtime_data": {"expression_type": "json", "type": "long_text", "max_length": 5120, "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": true, "is_unique": false, "label": "运行时数据", "api_name": "runtime_data", "description": "运行时数据", "default_value": true, "status": "released"}, "expired_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "过期时间", "api_name": "expired_time", "description": "过期时间", "status": "released", "index_name": "crt_time"}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 64, "pattern": "", "label": "object_describe_api_name", "is_active": true, "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released", "index_name": "api_name"}, "created_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "created_by", "status": "released", "label": "创建人", "is_active": true, "index_name": "crt_by"}, "last_modified_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "last_modified_by", "status": "released", "is_active": true, "index_name": "md_by", "label": "最后修改人"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "创建时间", "status": "released", "index_name": "crt_time"}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "最后修改时间", "status": "released", "index_name": "md_time"}, "is_deleted": {"type": "true_or_false", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "是否删除", "api_name": "is_deleted", "description": "是否删除", "default_value": false, "status": "released", "index_name": "is_del"}}, "tenant_id": "-100", "api_name": "PersonalAuthObj", "display_name": "个人授权", "package": "CRM", "is_active": true, "release_version": "6.4", "define_type": "internal", "is_deleted": false, "store_table_name": "dev_personal_auth"}