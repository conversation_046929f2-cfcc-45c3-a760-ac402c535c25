package com.facishare.paas.appframework.core.model.plugin;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.RequestType;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.support.AopUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2022/4/14.
 */
@Slf4j
@Component
public class PluginManagerImpl implements PluginManager {

    private static final String REMOTE_PLUGIN_PREFIX = "_remotePlugin";

    private static final Joiner KEY_JOINER = Joiner.on("_").skipNulls();

    private final Map<String, Plugin> pluginMap = Maps.newConcurrentMap();

    @Override
    public void register(Plugin plugin) {
        Class<?> clazz = AopUtils.getTargetClass(plugin);
        PluginProvider provider = clazz.getAnnotation(PluginProvider.class);
        if (provider == null) {
            return;
        }
        String key = provider.apiName();
        pluginMap.putIfAbsent(key, plugin);
        log.info("register plugin:{}-{}", key, clazz);
    }

    @Override
    public <T extends Plugin> T getLocalPlugin(String pluginApiName) {
        return (T) pluginMap.get(pluginApiName);
    }

    @Override
    public <T extends Plugin> T getPlugin(RequestType requestType, String code, String pluginApiName) {
        Plugin plugin = pluginMap.get(pluginApiName);
        if (Objects.isNull(plugin)) {
            String remotePluginName = KEY_JOINER.join(REMOTE_PLUGIN_PREFIX, code, requestType);
            plugin = pluginMap.get(remotePluginName);
        }
        return (T) plugin;
    }

    @Override
    public <T extends Plugin> List<T> getPluginList(RequestType requestType, String objectApiName, String code, String method, String tenantId, String agentType) {
        List<String> pluginApiNames = PluginConfig.getPluginApiNames(requestType, objectApiName, code, method, tenantId, agentType);
        if (CollectionUtils.empty(pluginApiNames)) {
            return Lists.newArrayList();
        }
        return pluginApiNames.stream().map(x -> (T) getPlugin(requestType, code, x)).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
