package com.facishare.paas.appframework.jaxrs.model;

import com.facishare.paas.appframework.core.rest.APIResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by zhouwr on 2017/10/27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InnerAPIResult {
    private static final int SUCCESS_CODE = 0;
    private static final String OK = "OK";

    private int errCode;
    private String errMessage;
    private Object result;

    public static InnerAPIResult success() {
        return success(null);
    }

    public static InnerAPIResult success(Object result) {
        return InnerAPIResult.builder().errCode(SUCCESS_CODE).errMessage(OK).result(result).build();
    }

    public static InnerAPIResult fail(int errCode, String errMessage) {
        return InnerAPIResult.builder().errCode(errCode).errMessage(StringUtils.trimToEmpty(errMessage)).build();
    }

    public static InnerAPIResult from(APIResult apiResult) {
        return InnerAPIResult.builder()
                .errCode(apiResult.getCode())
                .errMessage(apiResult.getMessage())
                .result(apiResult.getData())
                .build();
    }

    public static Object from(RestAPIResult restAPIResult) {
        return InnerAPIResult.builder()
                .errCode(restAPIResult.getCode())
                .errMessage(restAPIResult.getMessage())
                .result(restAPIResult.getData())
                .build();
    }
}
