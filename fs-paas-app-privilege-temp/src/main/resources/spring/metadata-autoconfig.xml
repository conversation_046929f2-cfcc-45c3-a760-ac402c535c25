<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

  <!--auto config-->
  <bean id="autoConf"
        class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
        p:fileEncoding="UTF-8"
        p:ignoreResourceNotFound="true"
  p:ignoreUnresolvablePlaceholders="false"
  p:location="classpath:application.properties"
  p:configName="dubbo-common"/>
  <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
        c:placeholderConfigurer-ref="autoConf"/>


 <!-- test-->

</beans>