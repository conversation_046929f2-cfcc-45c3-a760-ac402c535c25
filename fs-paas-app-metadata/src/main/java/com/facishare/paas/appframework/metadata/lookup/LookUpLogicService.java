package com.facishare.paas.appframework.metadata.lookup;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.dto.FieldMapping;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;

public interface LookUpLogicService {
    void handleDataLookUpField(User user, IObjectDescribe describe, List<FieldMapping> fieldMappings, List<IObjectData> dataList);

    List<IObjectData> queryDataBySpecifiedField(MetaDataFindService.QueryContext queryContext, IObjectDescribe describe, List<FieldMapping> fieldMappings, List<IObjectData> dataList);

    boolean containIdFieldMapping(User user, IObjectDescribe describe, List<FieldMapping> fieldMappings);

    boolean containLookUpFieldMapping(User user, IObjectDescribe describe, List<FieldMapping> fieldMappings);
}
