package com.facishare.paas.appframework.metadata.config;

import com.facishare.paas.appframework.core.model.User;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON>ju on 2024/5/21
 */
public interface ObjectControlLevelLogicService {

    List<ObjectControlLevelInfo> queryControlLevel(User user, String describeApiName, ControlLevelResourceType... types);

    @Data
    @AllArgsConstructor(staticName = "of")
    class ObjectControlLevelInfo {
        private String sourceType;
        private String primaryKey;
        private String parentFieldValue;
        private String controlLevel;
    }

}
