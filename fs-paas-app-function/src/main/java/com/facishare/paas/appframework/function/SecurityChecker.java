package com.facishare.paas.appframework.function;


import com.alibaba.fastjson.JSON;
import com.github.autoconf.ConfigFactory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.net.*;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Http接口安全检查
 * <p>
 * Created by liyiguang on 2018/5/15.
 */
@Slf4j
@Component
public class SecurityChecker {
    private static final String CONFIG_NAME = "fs-paas-function-service-security-config";
    private static Pattern IP = Pattern.compile("^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$");
    private static Pattern FUNCTION_HTTP_PATTERN = Pattern.compile("((([A-Za-z]{3,9}:(?:\\/\\/)?)(?:[-;:&=\\+$,\\w]+@)?[A-Za-z0-9.-]+(:[0-9]+)?|" +
            "(?:ww\u200C\u200Bw.|[-;:&=\\+$,\\w]+@)[A-Za-z0-9.-]+)((?:\\/[\\+~%\\/.\\w-_]*)?\\??(?:[-\\+=&;%@.\\w_]*)#?\u200C\u200B(?:[\\w]*))?)");

    private SecurityConfig securityConfig;
    //默认的过期时间是 8 小时
    private static final long DEFAULT_EXPIRE_HOUR = 8 * 3600 * 1000L;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig(CONFIG_NAME, (config) -> {
            String content = new String(config.getContent());
            securityConfig = JSON.parseObject(content, SecurityConfig.class);

            //配置文件中希望配置的是小时，转换成毫秒
            Long expire = securityConfig.getExpire();
            if (ObjectUtils.isEmpty(expire)){
                securityConfig.setExpire(DEFAULT_EXPIRE_HOUR);
            }else {
                expire = expire * 3600 * 1000;
                securityConfig.setExpire(expire);
            }
        });
    }

    @Data
    public static class SecurityConfig {
        private Set<String> httpAPINameWhileList;
        private Set<String> developerPhoneList;
        //校验的过期时间 默认是8小时
        private Long expire;

        public boolean checkHttpWhileAPIName(String apiName) {
            if (httpAPINameWhileList != null && httpAPINameWhileList.contains(apiName)) {
                return true;
            }
            return false;
        }

        public boolean isAllowDeveloper(String phoneNumber) {
            // 如果手机号为空，不行
            if (ObjectUtils.isEmpty(phoneNumber)) {
                return false;
            }

            //配置文件为空运行或代码有问题，放行
            if (ObjectUtils.isEmpty(developerPhoneList)) {
                return true;
            }

            //配置文件存在，但是不为空，校验
            return developerPhoneList.contains(phoneNumber);
        }

    }

    public boolean isAllowDeveloper(String phoneNumber) {
        // 运行或代码有问题，放行
        if (Objects.isNull(securityConfig)) {
            return true;
        }

        return securityConfig.isAllowDeveloper(phoneNumber);
    }


    public boolean isExpire(long startTime){
        //反序列失败或者没配置，采用默认的8小时的过期时间
        if (ObjectUtils.isEmpty(securityConfig) || ObjectUtils.isEmpty(securityConfig.getExpire())){
            return (System.currentTimeMillis() - startTime) < DEFAULT_EXPIRE_HOUR;
        }

        return (System.currentTimeMillis() - startTime) < securityConfig.expire;
    }

    public boolean validateHttpURL(String functionAPIName, String body) {
        if (StringUtils.isBlank(body)) {
            return true;
        }

        //函数APIName白名单以内不解析函数的Body直接放行
        if (securityConfig.checkHttpWhileAPIName(functionAPIName)) {
            return true;
        }

        //白名单以外校验函数的body不允许调用内网域名和IP
        Matcher matcher = FUNCTION_HTTP_PATTERN.matcher(body);

        while (matcher.find()) {
            String host = getHost(matcher.group());
            if (host == null) {
                continue;
            }

            //如果不是ip地址,解析成ip
            if (!isIP(host)) {
                host = getIPAddress(host);
            }

            //解析失败直接放行
            if (host == null) {
                continue;
            }

            //内网ip直接禁止
            if (isInnerIP(host)) {
                return false;
            }
        }

        //默认允许全部
        return true;
    }

    private String getHost(String urlStr) {
        try {
            URL url = new URL(urlStr);
            return url.getHost();
        } catch (MalformedURLException ignore) {

        }
        return null;
    }


    private String getIPAddress(String host) {
        try {
            return InetAddress.getByName(host).getHostAddress();
        } catch (Exception ignore) {
            log.warn("DNS resolution ip failed host {}", host, ignore);
        }
        return null;
    }

    private boolean isIP(String hostName) {
        return IP.matcher(hostName).find();
    }


    /**
     * 10.0.0.0/8:      10.0.0.0～**************
     * *********/8:     *********～***************
     * **********/12:   **********～**************
     * ***********/16:  ***********～***************
     *
     * @param ipStr
     * @return
     */
    private boolean isInnerIP(String ipStr) {
        byte[] ip = textToNumericFormatV4(ipStr);

        int b0 = ip[0] & 0xFF;
        int b1 = ip[1] & 0xFF;

        switch (b0) {
            case 10:
            case 127:
                return true;
            case 172:
                if (b1 >= 16 && b1 <= 31) {
                    return true;
                }
            case 192:
                if (b1 == 168) {
                    return true;
                }
        }
        return false;
    }

    public static byte[] textToNumericFormatV4(String ipStr) {
        try {
            InetAddress address = InetAddress.getByName(ipStr);
            if (address instanceof Inet4Address) {
                return address.getAddress();
            } else {
                throw new IllegalArgumentException("Not a valid IPv4 address: " + ipStr);
            }
        } catch (UnknownHostException e) {
            throw new IllegalArgumentException("Invalid IP address format: " + ipStr, e);
        }
    }

}
