package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2018/4/27
 */
public interface BatchCalculateOfRecordType {

    @Data
    class Arg {
        //主对象的apiName
        @JSONField(name = "M1")
        private String masterObjectApiName;
        //主对象的业务类型
        @JSONField(name = "M2")
        private String recordType;
        //主对象数据
        @JSONField(name = "M3")
        private ObjectDataDocument masterData;
        //从对象数据(第一级的key是从对象的apiName，第二级的key是数据的编号)
        @JSONField(name = "M4")
        private Map<String, List<ObjectDataDocument>> detailDataMap;
        @JSONField(name = "M5")
        private String originalObjectApiName;
        @JSONField(name = "M6")
        private String buttonApiName;
        private String ruleApiName;

    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "M1")
        private ObjectDataDocument objectData;
        @JSONField(name = "M2")
        private Map<String, List<ObjectDataDocument>> detail;

    }

}
