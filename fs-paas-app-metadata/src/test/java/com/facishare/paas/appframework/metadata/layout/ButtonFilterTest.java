package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.common.util.ComponentActions;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.flow.ApprovalFlowService;
import com.facishare.paas.appframework.flow.FlowCommonService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderLogicService;
import com.facishare.paas.appframework.metadata.follow.FollowLogicService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ButtonFilterTest {

  @Mock
  private ChangeOrderLogicService changeOrderLogicService;

  @Mock
  private CustomButtonService customButtonService;

  @Mock
  private FlowCommonService flowCommonService;

  @Mock
  private MetaDataService metaDataService;

  @Mock
  private FunctionPrivilegeService functionPrivilegeService;

  @Mock
  private UserRoleInfoService userRoleInfoService;

  @Mock
  private ApprovalFlowService approvalFlowService;

  @Mock
  private DuplicatedSearchService duplicatedSearchService;

  @Mock
  private ComponentActions componentActions;

  @Mock
  private FollowLogicService followLogicService;

  @Mock
  private ObjectDescribeExt objectDescribeExt;

  @Mock
  private User user;

  @Mock
  private ObjectDataExt data;

  @Mock
  private BiFunction<IObjectData, List<String>, Map<String, Map<String, Permissions>>> dataPrivilegeFunction;

  @Mock
  private Function<IObjectData, List<String>> approvalFlowWhitePeopleFunction;

  @Mock
  private Function<IObjectDescribe, List<IUdefButton>> customButtonFunction;

  private List<IButton> buttons;
  private Map<RelatedObjectDescribeStructure, List<IButton>> relatedListButtons;

  @BeforeEach
  void setUp() {
    buttons = Lists.newArrayList();
    relatedListButtons = Maps.newHashMap();

    // 创建测试按钮
    Button button1 = new Button();
    button1.setName("test_button_1");
    button1.setAction("CREATE");
    buttons.add(button1);

    Button button2 = new Button();
    button2.setName("test_button_2");
    button2.setAction("UPDATE");
    buttons.add(button2);
  }

  private ButtonFilter createButtonFilter(ObjectDescribeExt objectDescribeExt, FunctionPrivilegeService functionPrivilegeService) {
    return ButtonFilter.builder()
        .changeOrderLogicService(changeOrderLogicService)
        .customButtonService(customButtonService)
        .flowCommonService(flowCommonService)
        .metaDataService(metaDataService)
        .functionPrivilegeService(functionPrivilegeService)
        .userRoleInfoService(userRoleInfoService)
        .approvalFlowService(approvalFlowService)
        .duplicatedSearchService(duplicatedSearchService)
        .componentActions(componentActions)
        .followLogicService(followLogicService)
        .objectDescribeExt(objectDescribeExt)
        .user(user)
        .buttons(buttons)
        .relatedListButtons(relatedListButtons)
        .dataPrivilegeFunction(dataPrivilegeFunction)
        .approvalFlowWhitePeopleFunction(approvalFlowWhitePeopleFunction)
        .customButtonFunction(customButtonFunction)
        .build();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ButtonFilter构造函数正常场景
   */
  @Test
  @DisplayName("正常场景 - 构造ButtonFilter对象")
  void testButtonFilterConstructor_Success() {
    // 准备测试数据
    when(objectDescribeExt.getApiName()).thenReturn("test_object");

    // 执行被测试方法
    ButtonFilter buttonFilter = ButtonFilter.builder()
        .changeOrderLogicService(changeOrderLogicService)
        .customButtonService(customButtonService)
        .flowCommonService(flowCommonService)
        .metaDataService(metaDataService)
        .functionPrivilegeService(functionPrivilegeService)
        .userRoleInfoService(userRoleInfoService)
        .approvalFlowService(approvalFlowService)
        .duplicatedSearchService(duplicatedSearchService)
        .componentActions(componentActions)
        .followLogicService(followLogicService)
        .objectDescribeExt(objectDescribeExt)
        .user(user)
        .buttons(buttons)
        .relatedListButtons(relatedListButtons)
        .dataPrivilegeFunction(dataPrivilegeFunction)
        .approvalFlowWhitePeopleFunction(approvalFlowWhitePeopleFunction)
        .customButtonFunction(customButtonFunction)
        .build();

    // 验证结果
    assertNotNull(buttonFilter);
    assertEquals(2, buttonFilter.getButtons().size());
    assertNotNull(buttonFilter.getRelatedListButtons());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试构造函数传入null objectDescribeExt时抛出异常
   */
  @Test
  @DisplayName("异常场景 - objectDescribeExt为null")
  void testButtonFilterConstructorThrowsNullPointerException_WhenObjectDescribeExtIsNull() {
    // 执行并验证异常
    assertThrows(NullPointerException.class, () -> {
      ButtonFilter.builder()
          .changeOrderLogicService(changeOrderLogicService)
          .customButtonService(customButtonService)
          .flowCommonService(flowCommonService)
          .metaDataService(metaDataService)
          .functionPrivilegeService(functionPrivilegeService)
          .userRoleInfoService(userRoleInfoService)
          .approvalFlowService(approvalFlowService)
          .duplicatedSearchService(duplicatedSearchService)
          .componentActions(componentActions)
          .followLogicService(followLogicService)
          .objectDescribeExt(null)
          .user(user)
          .buttons(buttons)
          .relatedListButtons(relatedListButtons)
          .dataPrivilegeFunction(dataPrivilegeFunction)
          .approvalFlowWhitePeopleFunction(approvalFlowWhitePeopleFunction)
          .customButtonFunction(customButtonFunction)
          .build();
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试doFilter方法执行所有过滤逻辑
   */
  @Test
  @DisplayName("正常场景 - 执行完整过滤流程")
  void testDoFilter_Success() {
    // 准备测试数据
    when(objectDescribeExt.getApiName()).thenReturn("test_object");
    when(objectDescribeExt.isSlaveObject()).thenReturn(false);
    when(user.isSupperAdmin()).thenReturn(false);

    ButtonFilter buttonFilter = createButtonFilter(objectDescribeExt, functionPrivilegeService);

    // 执行被测试方法
    ButtonFilter result = buttonFilter.doFilter();

    // 验证结果
    assertNotNull(result);
    assertSame(buttonFilter, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试filterByObjectDescribe方法过滤从对象按钮
   */
  @Test
  @DisplayName("正常场景 - 根据对象描述过滤按钮")
  void testFilterByObjectDescribe_SlaveObject() {
    // 准备测试数据
    when(objectDescribeExt.getApiName()).thenReturn("test_object");
    when(objectDescribeExt.isSlaveObject()).thenReturn(true);

    ButtonFilter buttonFilter = createButtonFilter(objectDescribeExt, functionPrivilegeService);

    // 执行被测试方法
    ButtonFilter result = buttonFilter.filterByObjectDescribe();

    // 验证结果
    assertNotNull(result);
    assertSame(buttonFilter, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试filterByFunctionPrivilege方法当functionPrivilegeService为null时跳过过滤
   */
  @Test
  @DisplayName("正常场景 - functionPrivilegeService为null时跳过功能权限过滤")
  void testFilterByFunctionPrivilege_ServiceIsNull() {
    // 准备测试数据
    when(objectDescribeExt.getApiName()).thenReturn("test_object");

    ButtonFilter buttonFilter = createButtonFilter(objectDescribeExt, null);

    // 执行被测试方法
    ButtonFilter result = buttonFilter.filterByFunctionPrivilege();

    // 验证结果
    assertNotNull(result);
    assertSame(buttonFilter, result);
    assertEquals(2, buttonFilter.getButtons().size()); // 按钮数量不变
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试filterByDataPrivilege方法当data为null时跳过过滤
   */
  @Test
  @DisplayName("正常场景 - data为null时跳过数据权限过滤")
  void testFilterByDataPrivilege_DataIsNull() {
    // 准备测试数据
    when(objectDescribeExt.getApiName()).thenReturn("test_object");

    ButtonFilter buttonFilter = createButtonFilter(objectDescribeExt, functionPrivilegeService);

    // 执行被测试方法
    ButtonFilter result = buttonFilter.filterByDataPrivilege();

    // 验证结果
    assertNotNull(result);
    assertSame(buttonFilter, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试filterByLockAndLifeStatus方法当data为null时跳过过滤
   */
  @Test
  @DisplayName("正常场景 - data为null时跳过锁定和生命状态过滤")
  void testFilterByLockAndLifeStatus_DataIsNull() {
    // 准备测试数据
    when(objectDescribeExt.getApiName()).thenReturn("test_object");

    ButtonFilter buttonFilter = createButtonFilter(objectDescribeExt, functionPrivilegeService);

    // 执行被测试方法
    ButtonFilter result = buttonFilter.filterByLockAndLifeStatus();

    // 验证结果
    assertNotNull(result);
    assertSame(buttonFilter, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试filterByClientInfo方法过滤移动端不支持的按钮
   */
  @Test
  @DisplayName("正常场景 - 根据客户端信息过滤按钮")
  void testFilterByClientInfo_Success() {
    // 准备测试数据
    when(objectDescribeExt.getApiName()).thenReturn("test_object");

    ButtonFilter buttonFilter = createButtonFilter(objectDescribeExt, functionPrivilegeService);

    // 执行被测试方法
    ButtonFilter result = buttonFilter.filterByClientInfo(ButtonUsePageType.Detail);

    // 验证结果
    assertNotNull(result);
    assertSame(buttonFilter, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试filterBulkButtonByPRM方法在Create页面时直接返回
   */
  @Test
  @DisplayName("正常场景 - Create页面时跳过PRM批量按钮过滤")
  void testFilterBulkButtonByPRM_CreatePage() {
    // 准备测试数据
    when(objectDescribeExt.getApiName()).thenReturn("test_object");

    ButtonFilter buttonFilter = createButtonFilter(objectDescribeExt, functionPrivilegeService);

    // 执行被测试方法
    ButtonFilter result = buttonFilter.filterBulkButtonByPRM(ButtonUsePageType.Create);

    // 验证结果
    assertNotNull(result);
    assertSame(buttonFilter, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试includeSystemButton方法排除系统按钮
   */
  @Test
  @DisplayName("正常场景 - 排除系统按钮")
  void testIncludeSystemButton_ExcludeSystemButtons() {
    // 准备测试数据
    when(objectDescribeExt.getApiName()).thenReturn("test_object");

    ButtonFilter buttonFilter = createButtonFilter(objectDescribeExt, functionPrivilegeService);

    // 执行被测试方法
    ButtonFilter result = buttonFilter.includeSystemButton(false);

    // 验证结果
    assertNotNull(result);
    assertSame(buttonFilter, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试filterByButtonFilter方法当customButtonService为null时跳过过滤
   */
  @Test
  @DisplayName("正常场景 - customButtonService为null时跳过按钮过滤")
  void testFilterByButtonFilter_ServiceIsNull() {
    // 准备测试数据
    when(objectDescribeExt.getApiName()).thenReturn("test_object");

    ButtonFilter buttonFilter = ButtonFilter.builder()
        .changeOrderLogicService(changeOrderLogicService)
        .customButtonService(null) // customButtonService为null
        .flowCommonService(flowCommonService)
        .metaDataService(metaDataService)
        .functionPrivilegeService(functionPrivilegeService)
        .userRoleInfoService(userRoleInfoService)
        .approvalFlowService(approvalFlowService)
        .duplicatedSearchService(duplicatedSearchService)
        .componentActions(componentActions)
        .followLogicService(followLogicService)
        .objectDescribeExt(objectDescribeExt)
        .user(user)
        .buttons(buttons)
        .relatedListButtons(relatedListButtons)
        .dataPrivilegeFunction(dataPrivilegeFunction)
        .approvalFlowWhitePeopleFunction(approvalFlowWhitePeopleFunction)
        .customButtonFunction(null) // customButtonFunction也为null
        .build();

    // 执行被测试方法
    ButtonFilter result = buttonFilter.filterByButtonFilter();

    // 验证结果
    assertNotNull(result);
    assertSame(buttonFilter, result);
  }
}
