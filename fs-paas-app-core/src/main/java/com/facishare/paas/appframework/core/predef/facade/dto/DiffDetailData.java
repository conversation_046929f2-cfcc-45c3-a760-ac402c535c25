package com.facishare.paas.appframework.core.predef.facade.dto;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON>haooju on 2023/5/5
 */
public interface DiffDetailData {
    @Data
    @Builder
    class Arg {
        private List<IObjectData> detailsToAdd;
        private List<IObjectData> detailsToUpdate;
        private List<IObjectData> detailsToDelete;
        private Map<String, IObjectDescribe> objectDescribes;
        private Map<String, List<IObjectData>> dbDetailDataMap;
    }

    @Data
    class Result {
        private final Map<String, Map<String, Object>> detailChangeMap;
        private final boolean detailChangeForApproval;

        private Result(Map<String, Map<String, Object>> detailChangeMap, boolean detailChangeForApproval) {
            this.detailChangeMap = detailChangeMap;
            this.detailChangeForApproval = detailChangeForApproval;
        }

        public static Result of(Map<String, Map<String, Object>> detailChangeMap, boolean detailChangeForApproval) {
            return new Result(detailChangeMap, detailChangeForApproval);
        }
    }
}
