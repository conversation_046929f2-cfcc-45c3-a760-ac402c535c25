package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

import static com.facishare.crm.openapi.Utils.NEW_OPPORTUNITY_LINES_API_NAME;

/**
 * 商机2.0明细
 * create by <PERSON><PERSON><PERSON> on 2019/03/31
 */
@Component
public class NewOpportunityLinesObjectProvider extends DefaultObjectImportProvider {
    @Override
    public String getObjectCode() {
        return NEW_OPPORTUNITY_LINES_API_NAME;
    }

    @Override
    protected boolean getOpenWorkFlow(IObjectDescribe objectDescribe) {
        return true;
    }
}
