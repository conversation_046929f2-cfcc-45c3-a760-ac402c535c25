package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.facishare.paas.metadata.impl.ui.layout.TableColumn;
import com.facishare.paas.metadata.ui.layout.*;
import com.google.common.collect.Lists;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * RelatedListFormComponentExt单元测试类
 */
@ExtendWith(MockitoExtension.class)
class RelatedListFormComponentExtTest {

  @Mock
  private IComponent mockComponent;

  private RelatedListFormComponentExt relatedListFormComponentExt;

  @BeforeEach
  void setUp() {
    when(mockComponent.getType()).thenReturn(RelatedListFormComponentExt.RELATED_LIST_FORM_TYPE);
    relatedListFormComponentExt = RelatedListFormComponentExt.of(mockComponent);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试of方法正常创建RelatedListFormComponentExt对象
   */
  @Test
  @DisplayName("测试of方法正常创建对象")
  void testOfMethodSuccess() {
    IComponent component = mock(IComponent.class);
    when(component.getType()).thenReturn(RelatedListFormComponentExt.RELATED_LIST_FORM_TYPE);
    
    RelatedListFormComponentExt result = RelatedListFormComponentExt.of(component);
    
    assertNotNull(result);
    assertEquals(component, result.getComponent());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试of方法当component类型不匹配时返回null
   */
  @Test
  @DisplayName("测试of方法 - component类型不匹配时返回null")
  void testOfMethodReturnNullWhenTypeNotMatch() {
    IComponent component = mock(IComponent.class);
    when(component.getType()).thenReturn("wrong_type");
    
    RelatedListFormComponentExt result = RelatedListFormComponentExt.of(component);
    
    assertNull(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isRelatedListFormComponent方法当类型匹配时返回true
   */
  @Test
  @DisplayName("测试isRelatedListFormComponent方法 - 类型匹配时返回true")
  void testIsRelatedListFormComponentWhenTypeMatches() {
    IComponent component = mock(IComponent.class);
    when(component.getType()).thenReturn(RelatedListFormComponentExt.RELATED_LIST_FORM_TYPE);
    
    boolean result = RelatedListFormComponentExt.isRelatedListFormComponent(component);
    
    assertTrue(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isRelatedListFormComponent方法当类型不匹配时返回false
   */
  @Test
  @DisplayName("测试isRelatedListFormComponent方法 - 类型不匹配时返回false")
  void testIsRelatedListFormComponentWhenTypeNotMatch() {
    IComponent component = mock(IComponent.class);
    when(component.getType()).thenReturn("other_type");
    
    boolean result = RelatedListFormComponentExt.isRelatedListFormComponent(component);
    
    assertFalse(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试refObjectApiName字段的getter和setter方法
   */
  @Test
  @DisplayName("测试refObjectApiName字段的getter和setter")
  void testRefObjectApiNameGetterAndSetter() {
    String testApiName = "testObjectApi";
    
    when(mockComponent.get(ITableComponent.REF_OBJECT_API_NAME, String.class))
        .thenReturn(testApiName);
    
    String result = relatedListFormComponentExt.getRefObjectApiName();
    assertEquals(testApiName, result);
    
    // 测试setter
    relatedListFormComponentExt.setRefObjectApiName(testApiName);
    verify(mockComponent).set(ITableComponent.REF_OBJECT_API_NAME, testApiName);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIncludeFields方法当fields为空时返回空列表
   */
  @Test
  @DisplayName("测试getIncludeFields方法 - fields为空时返回空列表")
  void testGetIncludeFieldsWhenEmpty() {
    when(mockComponent.get(ITableComponent.INCLUDE_FIELDS)).thenReturn(null);

    List<ITableColumn> result = relatedListFormComponentExt.getIncludeFields();

    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIncludeFields方法当fields不为空时返回TableColumn列表
   */
  @Test
  @DisplayName("测试getIncludeFields方法 - fields不为空时返回TableColumn列表")
  void testGetIncludeFieldsWhenNotEmpty() {
    try (MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class)) {
      // 准备测试数据
      Map<String, Object> columnMap1 = new HashMap<>();
      columnMap1.put("api_name", "field1");
      columnMap1.put("label", "Field 1");
      
      Map<String, Object> columnMap2 = new HashMap<>();
      columnMap2.put("api_name", "field2");
      columnMap2.put("label", "Field 2");
      
      List<Map> columnsList = Lists.newArrayList(columnMap1, columnMap2);
      
      when(mockComponent.get(ITableComponent.INCLUDE_FIELDS)).thenReturn(columnsList);
      mockedCollectionUtils.when(() -> CollectionUtils.empty(columnsList)).thenReturn(false);
      
      // 执行测试
      List<ITableColumn> result = relatedListFormComponentExt.getIncludeFields();
      
      // 验证结果
      assertNotNull(result);
      assertEquals(2, result.size());
      assertTrue(result.get(0) instanceof TableColumn);
      assertTrue(result.get(1) instanceof TableColumn);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setIncludeFields方法当fields为空时不执行设置
   */
  @Test
  @DisplayName("测试setIncludeFields方法 - fields为空时不执行设置")
  void testSetIncludeFieldsWhenEmpty() {
    relatedListFormComponentExt.setIncludeFields(null);

    // 验证set方法没有被调用
    verify(mockComponent, never()).set(eq(ITableComponent.INCLUDE_FIELDS), any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setIncludeFields方法当fields不为空时正常设置
   */
  @Test
  @DisplayName("测试setIncludeFields方法 - fields不为空时正常设置")
  void testSetIncludeFieldsWhenNotEmpty() {
    try (MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class);
         MockedStatic<Document> mockedDocument = mockStatic(Document.class)) {
      
      // 准备测试数据
      ITableColumn column1 = mock(ITableColumn.class);
      ITableColumn column2 = mock(ITableColumn.class);
      
      when(column1.toJsonString()).thenReturn("{\"api_name\":\"field1\"}");
      when(column2.toJsonString()).thenReturn("{\"api_name\":\"field2\"}");
      
      Document doc1 = mock(Document.class);
      Document doc2 = mock(Document.class);
      
      mockedDocument.when(() -> Document.parse("{\"api_name\":\"field1\"}")).thenReturn(doc1);
      mockedDocument.when(() -> Document.parse("{\"api_name\":\"field2\"}")).thenReturn(doc2);
      
      List<ITableColumn> fields = Lists.newArrayList(column1, column2);
      mockedCollectionUtils.when(() -> CollectionUtils.empty(fields)).thenReturn(false);
      
      // Mock get方法返回List
      List<Document> mockList = new ArrayList<>();
      when(mockComponent.get(ITableComponent.INCLUDE_FIELDS)).thenReturn(mockList);
      
      // 执行测试
      relatedListFormComponentExt.setIncludeFields(fields);
      
      // 验证set方法被调用
      verify(mockComponent).set(ITableComponent.INCLUDE_FIELDS, Lists.newArrayList());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试renderType字段的getter和setter方法
   */
  @Test
  @DisplayName("测试renderType字段的getter和setter")
  void testRenderTypeGetterAndSetter() {
    String testRenderType = "testRenderType";
    
    when(mockComponent.get(IComponentInfo.RENDER_TYPE, String.class))
        .thenReturn(testRenderType);
    
    String result = relatedListFormComponentExt.getRenderType();
    assertEquals(testRenderType, result);
    
    // 测试setter
    relatedListFormComponentExt.setRenderType(testRenderType);
    verify(mockComponent).set(IComponentInfo.RENDER_TYPE, testRenderType);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试fieldApiName字段的getter和setter方法
   */
  @Test
  @DisplayName("测试fieldApiName字段的getter和setter")
  void testFieldApiNameGetterAndSetter() {
    String testFieldApiName = "testFieldApi";
    
    when(mockComponent.get(IRelatedObjectList.FIELD_API_NAME, String.class))
        .thenReturn(testFieldApiName);
    
    String result = relatedListFormComponentExt.getFieldApiName();
    assertEquals(testFieldApiName, result);
    
    // 测试setter
    relatedListFormComponentExt.setFieldApiName(testFieldApiName);
    verify(mockComponent).set(IRelatedObjectList.FIELD_API_NAME, testFieldApiName);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试syncHeaderFromDescribe方法正常同步header
   */
  @Test
  @DisplayName("测试syncHeaderFromDescribe方法")
  void testSyncHeaderFromDescribe() {
    try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
         MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
      
      // 准备测试数据
      IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
      ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
      IFieldDescribe mockFieldDescribe = mock(IObjectReferenceField.class);
      FieldDescribeExt mockFieldDescribeExt = mock(FieldDescribeExt.class);
      IObjectReferenceField mockReferenceField = mock(IObjectReferenceField.class);
      
      String fieldApiName = "testField";
      String targetLabel = "Test Label";
      
      // Mock静态方法调用链
      mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
      when(mockDescribeExt.getFieldDescribeSilently(fieldApiName)).thenReturn(Optional.of(mockFieldDescribe));
      mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockFieldDescribe)).thenReturn(mockFieldDescribeExt);
      when(mockFieldDescribeExt.<IObjectReferenceField>getFieldDescribe()).thenReturn(mockReferenceField);
      when(mockReferenceField.getTargetRelatedListLabel()).thenReturn(targetLabel);
      
      // 执行测试
      relatedListFormComponentExt.syncHeaderFromDescribe(mockDescribe, fieldApiName);
      
      // 验证setHeader被调用
      verify(mockComponent).setHeader(targetLabel);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getComponentName方法当header存在时返回header
   */
  @Test
  @DisplayName("测试getComponentName方法 - header存在时返回header")
  void testGetComponentNameWhenHeaderExists() {
    String testHeader = "Test Header";
    when(mockComponent.getHeader()).thenReturn(testHeader);
    
    String result = relatedListFormComponentExt.getComponentName();
    
    assertEquals(testHeader, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getComponentName方法当header不存在时返回name
   */
  @Test
  @DisplayName("测试getComponentName方法 - header不存在时返回name")
  void testGetComponentNameWhenHeaderNotExists() {
    String testName = "Test Name";
    when(mockComponent.getHeader()).thenReturn(null);
    when(mockComponent.getName()).thenReturn(testName);
    
    String result = relatedListFormComponentExt.getComponentName();
    
    assertEquals(testName, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试filterAndSortButtons方法
   */
  @Test
  @DisplayName("测试filterAndSortButtons方法")
  void testFilterAndSortButtons() {
    try (MockedStatic<ComponentExt> mockedComponentExt = mockStatic(ComponentExt.class)) {
      // 准备测试数据
      List<IButton> buttons = Lists.newArrayList(mock(IButton.class), mock(IButton.class));
      ListComponentInfo.ButtonRenderType renderType = ListComponentInfo.ButtonRenderType.LIST_NORMAL;
      ComponentExt mockComponentExt = mock(ComponentExt.class);
      List<IButton> expectedResult = Lists.newArrayList(mock(IButton.class));
      
      // Mock静态方法
      mockedComponentExt.when(() -> ComponentExt.of(mockComponent)).thenReturn(mockComponentExt);
      when(mockComponentExt.filterAndSortButtons(buttons, renderType)).thenReturn(expectedResult);
      
      // 执行测试
      List<IButton> result = relatedListFormComponentExt.filterAndSortButtons(buttons, renderType);
      
      // 验证结果
      assertEquals(expectedResult, result);
      
      // 验证方法调用
      mockedComponentExt.verify(() -> ComponentExt.of(mockComponent));
      verify(mockComponentExt).filterAndSortButtons(buttons, renderType);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试常量值的正确性
   */
  @Test
  @DisplayName("测试常量值")
  void testConstants() {
    assertEquals("related_list_form", RelatedListFormComponentExt.RELATED_LIST_FORM_TYPE);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试Delegate注解的功能
   */
  @Test
  @DisplayName("测试Delegate注解功能")
  void testDelegateAnnotation() {
    // 测试通过Delegate注解可以直接调用IComponent的方法
    String testName = "testName";
    when(mockComponent.getName()).thenReturn(testName);
    
    String result = relatedListFormComponentExt.getName();
    
    assertEquals(testName, result);
    verify(mockComponent).getName();
  }
}
