package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.SendCrmMessageModel;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

/**
 * Created by linqy on 2018/01/24
 */
@RestResource(
        value = "SendCrmMessage",
        desc = "发送CRM消息",
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.AppDefaultCodeC"
)
public interface SendCrmMessageProxy {

    @POST(value = "/AddRemindRecord", desc = "发送CRM消息")
    SendCrmMessageModel.Result sendCrmMessages(@HeaderParam("x-fs-ei") String tenantId, @Body SendCrmMessageModel.Arg arg);

}
