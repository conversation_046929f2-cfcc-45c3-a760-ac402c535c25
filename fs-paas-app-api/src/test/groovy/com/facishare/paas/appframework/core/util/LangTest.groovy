package com.facishare.paas.appframework.core.util

import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.api.Language
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

class LangTest extends Specification {

    def i18nClient = Mock(I18nClient)

    def setup() {
        //I18nClient.metaClass.static.getInstance = { -> i18nClient }
        //如果有静态方法调用，使用Whitebox注入
        // 处理final字段
        // 使用 Groovy metaClass 替代 FieldUtils.removeFinalModifier

        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
        Lang.clear()
    }

    def cleanup() {
        Lang.clear()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建Lang对象的基本功能
     */
    @Unroll
    def "of Test with different language codes"() {
        given:
        def languages = [
                new Language(code: "zh-CN"),
                new Language(code: "zh-TW"),
                new Language(code: "en")
        ]
        i18nClient.getAllLanguage() >> languages
        i18nClient.getCodeByAliasCode(input) >> aliasCode

        when:
        def result = Lang.of(input)

        then:
        result.value == expected

        where:
        input   | aliasCode | expected
        "zh-CN" | "zh-CN"   | "zh-CN"
        "zh-TW" | "zh-TW"   | "zh-TW"
        "en"    | "en"      | "en"
        "en-US" | null      | "en"
        null    | null      | "zh-CN"
        ""      | null      | "zh-CN"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取默认语言
     */
    def "defaultLang Test"() {
        when:
        def result = Lang.defaultLang()

        then:
        result == Lang.zh_CN
        result.value == "zh-CN"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取所有支持的语言列表
     */
    def "values Test"() {
        given:
        def languages = [
                new Language(code: "zh-CN"),
                new Language(code: "zh-TW"),
                new Language(code: "en")
        ]
        i18nClient.getAllLanguage() >> languages

        when:
        def result = Lang.values()

        then:
        result.size() == 3
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据企业ID获取语言列表
     */
//    def "values Test with enterprise id"() {
//        given:
//        def ei = "123"
//        def languages = [
//                new Language(code: "zh-CN"),
//                new Language(code: "en")
//        ]
//        TraceContext.get().getEi() >> ei
//        i18nClient.getLanguage(123L) >> languages
//
//        when:
//        def result = Lang.values()
//
//        then:
//        result.size() == 0
//
//        cleanup:
//        TraceContext.remove()
//    }

    /**
     * GenerateByAI
     * 测试内容描述：测试清除ThreadLocal中的语言缓存
     */
    def "clear Test"() {
        given:
        def languages = [
                new Language(code: "zh-CN"),
                new Language(code: "en")
        ]
        i18nClient.getAllLanguage() >> languages
        Lang.values() // 初始化ThreadLocal

        when:
        Lang.clear()
        def result = Lang.values()

        then:
        result.size() == 2
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取语言代码时的异常处理
     */
    def "of Test with exception handling"() {
        given:
        def languages = [
                new Language(code: "zh-CN"),
                new Language(code: "en")
        ]
        i18nClient.getAllLanguage() >> languages
        i18nClient.getCodeByAliasCode(_) >> { throw new RuntimeException("Test exception") }

        when:
        def result = Lang.of("invalid-code")

        then:
        result == Lang.zh_CN
    }
} 