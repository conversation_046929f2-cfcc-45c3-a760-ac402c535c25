package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.tag.RemoveTag;
import com.facishare.paas.appframework.core.predef.service.dto.tag.RemoveTagByDataId;
import com.facishare.paas.appframework.core.predef.service.dto.tag.SaveTag;
import com.facishare.paas.appframework.metadata.TagService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@ServiceModule("tag")
@Component
@Slf4j
public class ObjectTagService {
    @Autowired
    private TagService tagService;

    @ServiceMethod("save_tag")
    public SaveTag.Result saveTag(SaveTag.Arg arg, ServiceContext context) {
        log.debug("enter tag/service/save_tag, arg:{}, ei:{}", arg, context.getTenantId());
/*
        Map<String, Map<String, List<String>>> map = CollectionUtils.nullToEmpty(arg.getTagList())
                .stream()
                .collect(Collectors.toMap(SaveTag.TagData::getDataId,
                        a -> CollectionUtils.nullToEmpty(a.getTagInfoList())
                                .stream()
                                .collect(Collectors.toMap(SaveTag.TagInfo::getTagId,
                                        b -> Lists.newArrayList(b.getLevel1(), b.getLevel2(), b.getLevel3())))));
*/
        Map<String, List<String>> map =
                CollectionUtils.nullToEmpty(arg.getTagList()).stream().flatMap(x -> x.getTagInfoList().stream())
                .collect(Collectors.toMap(SaveTag.TagInfo::getTagId,
                        x -> Lists.newArrayList(x.getLevel1(), x.getLevel2(), x.getLevel3())));

        tagService.saveTag(context.getUser(), arg.getDescribeApiName(), map);
        return SaveTag.Result.builder().build();
    }

    @ServiceMethod("remove_tag_by_data_id")
    public RemoveTagByDataId.Result removeTagByDataId(RemoveTagByDataId.Arg arg, ServiceContext context) {
        log.debug("enter tag/service/remove_tag_by_data_id, arg:{}, ei:{}", arg, context.getTenantId());
        tagService.bulkRemoveTagForData(context.getUser(), arg.getDataIdList(), arg.getDescribeApiName());
        return RemoveTagByDataId.Result.builder().build();
    }

    @ServiceMethod("remove_tag")
    public RemoveTag.Result removeTag(RemoveTag.Arg arg, ServiceContext context) {
        log.debug("enter tag/service/remove_tag, arg:{}, ei:{}", arg, context.getTenantId());
        tagService.bulkRemoveTagForData(context.getUser(), arg.getTagInfo(), arg.getDescribeApiName());
        return RemoveTag.Result.builder().build();
    }
}
