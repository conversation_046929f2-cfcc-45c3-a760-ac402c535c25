package com.facishare.paas.appframework.common.util;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
@Slf4j
public class TreeViewUtil {


    public static List<String> getParentAndChildIds(List<IObjectData> objectDataList, String pid, String parentApiName) {
        return getParentAndChildIds(objectDataList, pid, parentApiName, false);
    }

    public static List<String> getParentAndChildIds(List<IObjectData> objectDataList, String pid, String parentApiName, boolean isThrowException) {
        List<String> childIds = Lists.newArrayList();
        if (StringUtils.isEmpty(parentApiName)) {
            return childIds;
        }
        childIds.add(pid);
        getParentAndChildIds(objectDataList, childIds, pid, parentApiName, 0, isThrowException);
        return childIds;
    }

    private static void getParentAndChildIds(List<IObjectData> objectDataList, List<String> childIds, String pid, String parentApiName, int depth, boolean isThrowException) {
        int maxHierarchy = AppFrameworkConfig.getTreeViewObjectAllowMaxHierarchy();
        depth++;
        if (depth > maxHierarchy - 1) {
            // 不需要抛出异常时只查询最大层级的数据
            if (!isThrowException) {
               return;
            }
            // 需要抛出异常且大于最大层级才抛出异常
            if (depth > maxHierarchy) {
                throw new ValidateException(I18NExt.text(I18NKey.DATA_HIERARCHY_CAN_NOT_EXCEED, maxHierarchy));
            }
        }
        for (IObjectData data : objectDataList) {
            //遍历出父id等于参数的id，add进子节点集合
            if (StringUtils.equals(data.get(parentApiName, String.class), pid)) {
                childIds.add(data.getId());
                //递归遍历下一级
                getParentAndChildIds(objectDataList, childIds, data.getId(), parentApiName, depth, isThrowException);
            }
        }
    }
}
