package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.license.ApplicationLicense;
import com.facishare.paas.appframework.core.predef.service.dto.license.QueryLicenseFeature;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.dto.ModuleParaLicense;
import com.facishare.paas.appframework.license.dto.QuotaInfo;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.reflect.Whitebox;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * LicenseResourceService JUnit5 单元测试
 */
@ExtendWith(MockitoExtension.class)
class LicenseResourceServiceTest {

  @Mock
  private LicenseService licenseService;

  @InjectMocks
  private LicenseResourceService licenseResourceService;

  @Mock
  private ServiceContext serviceContext;

  private User user;
  private String tenantId = "74255";

  @BeforeEach
  void setUp() {
    user = User.systemUser(tenantId);
    // 使用lenient模式避免UnnecessaryStubbingException
    lenient().when(serviceContext.getUser()).thenReturn(user);
    lenient().when(serviceContext.getTenantId()).thenReturn(tenantId);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试 getQuota 方法，验证在没有指定 describeApiName 的情况下能否正确获取配额信息
   */
  @Test
  @DisplayName("getQuota测试 - 没有指定describeApiName")
  void getQuotaTest_WithoutDescribeApiName() {
    // 准备测试数据
    ApplicationLicense.Arg arg = new ApplicationLicense.Arg();
    arg.setItems(Sets.newHashSet("udobj"));
    arg.setDescribeApiName(null);

    // 模拟licenseService.batchGetModuleLicenses方法的返回值
    Map<String, List<ModuleParaLicense>> moduleParaLicenseMap = Maps.newHashMap();
    List<ModuleParaLicense> udobjLicenses = Arrays.asList(
        ModuleParaLicense.builder()
            .moduleCode("udobj")
            .paraKey("reference_field_limit")
            .paraValue("100")
            .paraType("number")
            .build(),
        ModuleParaLicense.builder()
            .moduleCode("udobj")
            .paraKey("rich_text_field_limit")
            .paraValue("10")
            .paraType("number")
            .build(),
        ModuleParaLicense.builder()
            .moduleCode("udobj")
            .paraKey("html_rich_text_field_limit")
            .paraValue("5")
            .paraType("number")
            .build()
    );
    moduleParaLicenseMap.put("udobj", udobjLicenses);

    // 配置Mock行为
    when(licenseService.batchGetModuleLicenses(any(), any())).thenReturn(moduleParaLicenseMap);

    // 执行被测试方法
    ApplicationLicense.Result result = licenseResourceService.getQuota(arg, serviceContext);

    // 验证结果
    assertNotNull(result);
    assertNotNull(result.getQuota());
    assertNotNull(result.getQuota().get("udobj"));
    assertTrue(result.getQuota().get("udobj") instanceof Map);
    assertFalse(result.getQuota().get("udobj").isEmpty());

    // 验证Mock交互
    verify(licenseService, times(1)).batchGetModuleLicenses(any(), any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试 getQuota 方法，验证在指定 describeApiName 的情况下能否正确获取配额信息并处理富文本字段限制
   */
  @Test
  @DisplayName("getQuota测试 - 指定describeApiName")
  void getQuotaTest_WithDescribeApiName() {
    // 准备测试数据
    ApplicationLicense.Arg arg = new ApplicationLicense.Arg();
    arg.setItems(Sets.newHashSet("udobj"));
    arg.setDescribeApiName("AccountObj");

    // 模拟AppFrameworkConfig静态方法
    Map<String, Integer> objectRichTextFieldMaxCount = Maps.newHashMap();
    objectRichTextFieldMaxCount.put("AccountObj", 15);
    Map<String, Integer> objectHtmlRichTextFieldMaxCount = Maps.newHashMap();
    objectHtmlRichTextFieldMaxCount.put("AccountObj", 8);

    try {
      Whitebox.setInternalState(AppFrameworkConfig.class, "objectRichTextFieldMaxCount", objectRichTextFieldMaxCount);
      Whitebox.setInternalState(AppFrameworkConfig.class, "objectHtmlRichTextFieldMaxCount", objectHtmlRichTextFieldMaxCount);

      // 模拟licenseService返回值
      Map<String, List<ModuleParaLicense>> moduleParaLicenseMap = Maps.newHashMap();
      List<ModuleParaLicense> udobjLicenses = Arrays.asList(
          ModuleParaLicense.builder()
              .moduleCode("udobj")
              .paraKey("reference_field_limit")
              .paraValue("100")
              .paraType("number")
              .build(),
          ModuleParaLicense.builder()
              .moduleCode("udobj")
              .paraKey("rich_text_field_limit")
              .paraValue("10")
              .paraType("number")
              .build(),
          ModuleParaLicense.builder()
              .moduleCode("udobj")
              .paraKey("html_rich_text_field_limit")
              .paraValue("8")
              .paraType("number")
              .build()
      );
      moduleParaLicenseMap.put("udobj", udobjLicenses);

      // 配置Mock行为
      when(licenseService.batchGetModuleLicenses(any(), any())).thenReturn(moduleParaLicenseMap);

      // 执行被测试方法
      ApplicationLicense.Result result = licenseResourceService.getQuota(arg, serviceContext);

      // 验证结果
      assertNotNull(result);
      assertNotNull(result.getQuota());
      assertNotNull(result.getQuota().get("udobj"));

      // 验证富文本字段限制是否被正确设置
      assertEquals(15, result.getQuota().get("udobj").get("rich_text_field_limit"));
      assertEquals(8, result.getQuota().get("udobj").get("html_rich_text_field_limit"));

      // 验证Mock交互
      verify(licenseService, times(1)).batchGetModuleLicenses(any(), any());

    } catch (Exception e) {
      fail("Test failed due to reflection error: " + e.getMessage());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试 queryLicenseFeature 方法，验证能否正确查询许可证功能（不包含配额查询）
   */
  @Test
  @DisplayName("queryLicenseFeature测试 - 仅功能查询")
  void queryLicenseFeatureTest() {
    // 准备测试数据
    QueryLicenseFeature.Arg arg = new QueryLicenseFeature.Arg();
    arg.setItems(Sets.newHashSet("custom_object", "custom_function"));
    arg.setModuleParaMap(null); // 不查询配额信息
    
    Map<String, Boolean> featureMap = Maps.newHashMap();
    featureMap.put("custom_object", true);
    featureMap.put("custom_function", false);

    // 配置Mock行为
    when(licenseService.existModule(tenantId, arg.getItems())).thenReturn(featureMap);
    when(licenseService.batchGetQuotaByModulePara(serviceContext.getUser(), null))
        .thenReturn(Maps.newHashMap());

    // 执行被测试方法
    QueryLicenseFeature.Result result = licenseResourceService.queryLicenseFeature(arg, serviceContext);

    // 验证结果
    assertNotNull(result);
    assertEquals(featureMap, result.getFeature());
    assertNotNull(result.getQuota());
    assertTrue(result.getQuota().isEmpty());

    // 验证Mock交互
    verify(licenseService, times(1)).existModule(tenantId, arg.getItems());
    verify(licenseService, times(1)).batchGetQuotaByModulePara(serviceContext.getUser(), null);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试 getQuota 方法在处理空 items 的情况
   */
  @Test
  @DisplayName("getQuota测试 - 空items")
  void getQuotaTest_WithEmptyItems() {
    // 准备测试数据
    ApplicationLicense.Arg arg = new ApplicationLicense.Arg();
    arg.setItems(Collections.emptySet());
    arg.setDescribeApiName(null);

    // 模拟licenseService.batchGetModuleLicenses方法的返回值
    Map<String, List<ModuleParaLicense>> moduleParaLicenseMap = Maps.newHashMap();

    // 配置Mock行为
    when(licenseService.batchGetModuleLicenses(any(), any())).thenReturn(moduleParaLicenseMap);

    // 执行被测试方法
    ApplicationLicense.Result result = licenseResourceService.getQuota(arg, serviceContext);

    // 验证结果
    assertNotNull(result);
    assertNotNull(result.getQuota());
    assertTrue(result.getQuota().isEmpty());

    // 验证Mock交互
    verify(licenseService, times(1)).batchGetModuleLicenses(any(), any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试 queryLicenseFeature 方法在处理空 items 的情况
   */
  @Test
  @DisplayName("queryLicenseFeature测试 - 空items")
  void queryLicenseFeatureTest_WithEmptyItems() {
    // 准备测试数据
    QueryLicenseFeature.Arg arg = new QueryLicenseFeature.Arg();
    arg.setItems(Collections.emptySet());
    Map<String, Boolean> featureMap = Maps.newHashMap();

    // 配置Mock行为
    when(licenseService.existModule(tenantId, arg.getItems())).thenReturn(featureMap);

    // 执行被测试方法
    QueryLicenseFeature.Result result = licenseResourceService.queryLicenseFeature(arg, serviceContext);

    // 验证结果
    assertNotNull(result);
    assertEquals(featureMap, result.getFeature());

    // 验证Mock交互
    verify(licenseService, times(1)).existModule(tenantId, arg.getItems());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试 queryLicenseFeature 方法 - 包含配额查询的正常场景
   */
  @Test
  @DisplayName("queryLicenseFeature测试 - 包含配额查询")
  void queryLicenseFeatureTest_WithQuotaQuery() {
    // 准备测试数据
    QueryLicenseFeature.Arg arg = new QueryLicenseFeature.Arg();
    arg.setItems(Sets.newHashSet("custom_object", "custom_roles"));
    
    Map<String, Set<String>> moduleParaMap = Maps.newHashMap();
    moduleParaMap.put("custom_object", Sets.newHashSet("custom_objects_limit", "custom_fields_limit"));
    moduleParaMap.put("custom_roles", Sets.newHashSet("custom_roles_limit"));
    arg.setModuleParaMap(moduleParaMap);

    // 模拟功能查询结果
    Map<String, Boolean> featureMap = Maps.newHashMap();
    featureMap.put("custom_object", true);
    featureMap.put("custom_roles", false);

    // 模拟licenseService返回的配额信息
    Map<String, Map<String, QuotaInfo>> mockQuotaResult = Maps.newHashMap();

    Map<String, QuotaInfo> customObjectQuotas = Maps.newHashMap();
    customObjectQuotas.put("custom_objects_limit", QuotaInfo.builder()
        .total(50)
        .used(12)
        .available(38)
        .build());
    customObjectQuotas.put("custom_fields_limit", QuotaInfo.builder()
        .total(200)
        .used(45)
        .available(155)
        .build());

    Map<String, QuotaInfo> customRolesQuotas = Maps.newHashMap();
    customRolesQuotas.put("custom_roles_limit", QuotaInfo.builder()
        .total(10)
        .used(3)
        .available(7)
        .build());

    mockQuotaResult.put("custom_object", customObjectQuotas);
    mockQuotaResult.put("custom_roles", customRolesQuotas);

    // 配置Mock行为
    when(licenseService.existModule(tenantId, arg.getItems())).thenReturn(featureMap);
    when(licenseService.batchGetQuotaByModulePara(serviceContext.getUser(), moduleParaMap))
        .thenReturn(mockQuotaResult);

    // 执行被测试方法
    QueryLicenseFeature.Result result = licenseResourceService.queryLicenseFeature(arg, serviceContext);

    // 验证结果
    assertNotNull(result);
    assertEquals(featureMap, result.getFeature());
    assertNotNull(result.getQuota());
    assertEquals(2, result.getQuota().size());

    // 验证custom_object模块的配额信息
    Map<String, QuotaInfo> customObjectResult = result.getQuota().get("custom_object");
    assertNotNull(customObjectResult);
    assertEquals(2, customObjectResult.size());

    QuotaInfo objectsLimitInfo = customObjectResult.get("custom_objects_limit");
    assertEquals(50, objectsLimitInfo.getTotal());
    assertEquals(12, objectsLimitInfo.getUsed());
    assertEquals(38, objectsLimitInfo.getAvailable());

    QuotaInfo fieldsLimitInfo = customObjectResult.get("custom_fields_limit");
    assertEquals(200, fieldsLimitInfo.getTotal());
    assertEquals(45, fieldsLimitInfo.getUsed());
    assertEquals(155, fieldsLimitInfo.getAvailable());

    // 验证custom_roles模块的配额信息
    Map<String, QuotaInfo> customRolesResult = result.getQuota().get("custom_roles");
    assertNotNull(customRolesResult);
    assertEquals(1, customRolesResult.size());

    QuotaInfo rolesLimitInfo = customRolesResult.get("custom_roles_limit");
    assertEquals(10, rolesLimitInfo.getTotal());
    assertEquals(3, rolesLimitInfo.getUsed());
    assertEquals(7, rolesLimitInfo.getAvailable());

    // 验证Mock交互
    verify(licenseService, times(1)).existModule(tenantId, arg.getItems());
    verify(licenseService, times(1)).batchGetQuotaByModulePara(serviceContext.getUser(), moduleParaMap);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试 queryLicenseFeature 方法 - 空模块参数映射场景
   */
  @Test
  @DisplayName("queryLicenseFeature测试 - 空模块参数映射")
  void queryLicenseFeatureTest_EmptyModuleParaMap() {
    // 准备测试数据
    QueryLicenseFeature.Arg arg = new QueryLicenseFeature.Arg();
    arg.setItems(Sets.newHashSet("test_module"));
    Map<String, Set<String>> emptyModuleParaMap = Maps.newHashMap();
    arg.setModuleParaMap(emptyModuleParaMap);

    // 模拟功能查询结果
    Map<String, Boolean> featureMap = Maps.newHashMap();
    featureMap.put("test_module", true);

    // 配置Mock行为
    when(licenseService.existModule(tenantId, arg.getItems())).thenReturn(featureMap);
    when(licenseService.batchGetQuotaByModulePara(serviceContext.getUser(), emptyModuleParaMap))
        .thenReturn(Maps.newHashMap());

    // 执行被测试方法
    QueryLicenseFeature.Result result = licenseResourceService.queryLicenseFeature(arg, serviceContext);

    // 验证结果
    assertNotNull(result);
    assertEquals(featureMap, result.getFeature());
    assertNotNull(result.getQuota());
    assertTrue(result.getQuota().isEmpty());

    // 验证Mock交互
    verify(licenseService, times(1)).existModule(tenantId, arg.getItems());
    verify(licenseService, times(1)).batchGetQuotaByModulePara(serviceContext.getUser(), emptyModuleParaMap);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试 queryLicenseFeature 方法 - 单个模块单个参数场景
   */
  @Test
  @DisplayName("queryLicenseFeature测试 - 单个模块单个参数")
  void queryLicenseFeatureTest_SingleModuleSingleParam() {
    // 准备测试数据
    QueryLicenseFeature.Arg arg = new QueryLicenseFeature.Arg();
    arg.setItems(Sets.newHashSet("large_attachment_preview_app"));
    Map<String, Set<String>> moduleParaMap = Maps.newHashMap();
    moduleParaMap.put("large_attachment_preview_app", Sets.newHashSet("large_attachment_preview_limit"));
    arg.setModuleParaMap(moduleParaMap);

    // 模拟功能查询结果
    Map<String, Boolean> featureMap = Maps.newHashMap();
    featureMap.put("large_attachment_preview_app", true);

    // 模拟licenseService返回的配额信息
    Map<String, Map<String, QuotaInfo>> mockQuotaResult = Maps.newHashMap();
    Map<String, QuotaInfo> previewQuotas = Maps.newHashMap();
    previewQuotas.put("large_attachment_preview_limit", QuotaInfo.builder()
        .total(1000)
        .used(150)
        .available(850)
        .build());
    mockQuotaResult.put("large_attachment_preview_app", previewQuotas);

    // 配置Mock行为
    when(licenseService.existModule(tenantId, arg.getItems())).thenReturn(featureMap);
    when(licenseService.batchGetQuotaByModulePara(serviceContext.getUser(), moduleParaMap))
        .thenReturn(mockQuotaResult);

    // 执行被测试方法
    QueryLicenseFeature.Result result = licenseResourceService.queryLicenseFeature(arg, serviceContext);

    // 验证结果
    assertNotNull(result);
    assertEquals(featureMap, result.getFeature());
    assertNotNull(result.getQuota());
    assertEquals(1, result.getQuota().size());

    Map<String, QuotaInfo> previewResult = result.getQuota().get("large_attachment_preview_app");
    assertNotNull(previewResult);
    assertEquals(1, previewResult.size());

    QuotaInfo previewLimitInfo = previewResult.get("large_attachment_preview_limit");
    assertEquals(1000, previewLimitInfo.getTotal());
    assertEquals(150, previewLimitInfo.getUsed());
    assertEquals(850, previewLimitInfo.getAvailable());

    // 验证Mock交互
    verify(licenseService, times(1)).existModule(tenantId, arg.getItems());
    verify(licenseService, times(1)).batchGetQuotaByModulePara(serviceContext.getUser(), moduleParaMap);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试 queryLicenseFeature 方法 - 配额值为0的场景
   */
  @Test
  @DisplayName("queryLicenseFeature测试 - 配额值为0")
  void queryLicenseFeatureTest_ZeroQuotaValues() {
    // 准备测试数据
    QueryLicenseFeature.Arg arg = new QueryLicenseFeature.Arg();
    arg.setItems(Sets.newHashSet("test_module"));
    Map<String, Set<String>> moduleParaMap = Maps.newHashMap();
    moduleParaMap.put("test_module", Sets.newHashSet("test_limit"));
    arg.setModuleParaMap(moduleParaMap);

    // 模拟功能查询结果
    Map<String, Boolean> featureMap = Maps.newHashMap();
    featureMap.put("test_module", false);

    // 模拟licenseService返回的配额信息（配额为0）
    Map<String, Map<String, QuotaInfo>> mockQuotaResult = Maps.newHashMap();
    Map<String, QuotaInfo> testQuotas = Maps.newHashMap();
    testQuotas.put("test_limit",QuotaInfo.builder()
        .total(0)
        .used(0)
        .available(0)
        .build());
    mockQuotaResult.put("test_module", testQuotas);

    // 配置Mock行为
    when(licenseService.existModule(tenantId, arg.getItems())).thenReturn(featureMap);
    when(licenseService.batchGetQuotaByModulePara(serviceContext.getUser(), moduleParaMap))
        .thenReturn(mockQuotaResult);

    // 执行被测试方法
    QueryLicenseFeature.Result result = licenseResourceService.queryLicenseFeature(arg, serviceContext);

    // 验证结果
    assertNotNull(result);
    assertEquals(featureMap, result.getFeature());
    assertNotNull(result.getQuota());
    assertEquals(1, result.getQuota().size());

    Map<String, QuotaInfo> testResult = result.getQuota().get("test_module");
    assertNotNull(testResult);

    QuotaInfo testLimitInfo = testResult.get("test_limit");
    assertEquals(0, testLimitInfo.getTotal());
    assertEquals(0, testLimitInfo.getUsed());
    assertEquals(0, testLimitInfo.getAvailable());

    // 验证Mock交互
    verify(licenseService, times(1)).existModule(tenantId, arg.getItems());
    verify(licenseService, times(1)).batchGetQuotaByModulePara(serviceContext.getUser(), moduleParaMap);
  }
}
