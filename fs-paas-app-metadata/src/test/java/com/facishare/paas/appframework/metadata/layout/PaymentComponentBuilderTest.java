package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.metadata.impl.ui.layout.component.RelatedObjectList;
import com.facishare.paas.metadata.ui.layout.IRelatedObjectList;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class PaymentComponentBuilderTest {

  private PaymentComponentBuilder paymentComponentBuilder;

  @BeforeEach
  void setUp() {
    paymentComponentBuilder = new PaymentComponentBuilder();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试PaymentComponentBuilder构造函数正常场景
   */
  @Test
  @DisplayName("正常场景 - 构造PaymentComponentBuilder对象")
  void testPaymentComponentBuilderConstructor_Success() {
    // 执行被测试方法
    PaymentComponentBuilder builder = new PaymentComponentBuilder();

    // 验证结果
    assertNotNull(builder);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试build方法正常场景
   */
  @Test
  @DisplayName("正常场景 - 构建支付组件")
  void testBuild_Success() {
    // 执行被测试方法
    assertDoesNotThrow(() -> {
      IRelatedObjectList component = paymentComponentBuilder.build();
      
      // 验证结果
      assertNotNull(component);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getPaymentComponentWithoutFields方法
   */
  @Test
  @DisplayName("正常场景 - 获取不带字段的支付组件")
  void testGetPaymentComponentWithoutFields_Success() {
    // 执行被测试方法
    assertDoesNotThrow(() -> {
      RelatedObjectList component = paymentComponentBuilder.getPaymentComponentWithoutFields();
      
      // 验证结果
      assertNotNull(component);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试构建的组件包含正确的字段
   */
  @Test
  @DisplayName("正常场景 - 验证组件字段完整性")
  void testBuild_ComponentFieldsIntegrity() {
    // 执行被测试方法
    assertDoesNotThrow(() -> {
      IRelatedObjectList component = paymentComponentBuilder.build();
      
      // 验证结果
      assertNotNull(component);
      
      // 验证包含字段
      List<ITableColumn> includeFields = component.getIncludeFields();
      assertNotNull(includeFields);
      
      // 验证字段数量（应该包含8个字段：name, amount, fee, payStatus, payType, finishTime, transTime, remark, payEnterpriseName）
      assertTrue(includeFields.size() >= 8);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试多次调用build方法的一致性
   */
  @Test
  @DisplayName("正常场景 - 测试多次构建的一致性")
  void testBuild_MultipleCallsConsistency() {
    // 执行被测试方法
    assertDoesNotThrow(() -> {
      IRelatedObjectList component1 = paymentComponentBuilder.build();
      IRelatedObjectList component2 = paymentComponentBuilder.build();
      
      // 验证结果
      assertNotNull(component1);
      assertNotNull(component2);
      
      // 验证两次构建的组件都有相同的字段数量
      assertEquals(component1.getIncludeFields().size(), component2.getIncludeFields().size());
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试基本功能验证
   */
  @Test
  @DisplayName("正常场景 - 验证基本功能")
  void testPaymentComponentBuilder_BasicFunctionality() {
    // 验证基本功能
    assertNotNull(paymentComponentBuilder);
    assertDoesNotThrow(() -> {
      paymentComponentBuilder.toString();
    });
    assertDoesNotThrow(() -> {
      paymentComponentBuilder.hashCode();
    });
    assertDoesNotThrow(() -> {
      paymentComponentBuilder.equals(paymentComponentBuilder);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试对象状态一致性
   */
  @Test
  @DisplayName("正常场景 - 测试对象状态一致性")
  void testPaymentComponentBuilder_StateConsistency() {
    // 创建两个对象
    PaymentComponentBuilder builder1 = new PaymentComponentBuilder();
    PaymentComponentBuilder builder2 = new PaymentComponentBuilder();

    // 验证对象独立性
    assertNotNull(builder1);
    assertNotNull(builder2);
    assertNotSame(builder1, builder2);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试异常处理能力
   */
  @Test
  @DisplayName("异常场景 - 测试异常处理")
  void testPaymentComponentBuilder_ExceptionHandling() {
    // 测试在异常情况下的处理
    assertDoesNotThrow(() -> {
      PaymentComponentBuilder builder = new PaymentComponentBuilder();

      // 验证对象在异常情况下的稳定性
      assertNotNull(builder);
      
      // 多次调用build方法
      for (int i = 0; i < 5; i++) {
        IRelatedObjectList component = builder.build();
        assertNotNull(component);
      }
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试组件字段的基本属性
   */
  @Test
  @DisplayName("正常场景 - 验证组件字段基本属性")
  void testBuild_FieldBasicProperties() {
    // 执行被测试方法
    assertDoesNotThrow(() -> {
      IRelatedObjectList component = paymentComponentBuilder.build();
      
      // 验证结果
      assertNotNull(component);
      
      List<ITableColumn> includeFields = component.getIncludeFields();
      assertNotNull(includeFields);
      assertFalse(includeFields.isEmpty());
      
      // 验证每个字段都有基本属性
      for (ITableColumn field : includeFields) {
        assertNotNull(field);
        assertNotNull(field.getName());
        assertNotNull(field.getLabelName());
        assertNotNull(field.getRenderType());
      }
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试组件构建的稳定性
   */
  @Test
  @DisplayName("正常场景 - 测试组件构建稳定性")
  void testPaymentComponentBuilder_BuildStability() {
    // 测试多次构建的稳定性
    assertDoesNotThrow(() -> {
      for (int i = 0; i < 10; i++) {
        PaymentComponentBuilder builder = new PaymentComponentBuilder();
        IRelatedObjectList component = builder.build();
        assertNotNull(component);
        assertNotNull(component.getIncludeFields());
        assertFalse(component.getIncludeFields().isEmpty());
      }
    });
  }
}
