/**
 * Facishare PAAS Organization
 * This is PAAS organization service project
 *
 * OpenAPI spec version: v1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


package com.facishare.crm.privilege.swagger.model;

import java.util.Objects;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * BaseResult
 */
@javax.annotation.Generated(value = "class io.swagger.codegen.languages.JavaClientCodegen", date = "2017-07-25T12:04:51.098+08:00")
public class BaseResult   {
  @SerializedName("errCode")
  private Integer errCode = null;

  @SerializedName("errKey")
  private String errKey = null;

  @SerializedName("errMessage")
  private String errMessage = null;

  @SerializedName("errDescription")
  private String errDescription = null;

  @SerializedName("result")
  private Object result = null;

  @SerializedName("success")
  private Boolean success = false;

  public BaseResult errCode(Integer errCode) {
    this.errCode = errCode;
    return this;
  }

   /**
   * Get errCode
   * @return errCode
  **/
  @ApiModelProperty(example = "null", value = "")
  public Integer getErrCode() {
    return errCode;
  }

  public void setErrCode(Integer errCode) {
    this.errCode = errCode;
  }

  public BaseResult errKey(String errKey) {
    this.errKey = errKey;
    return this;
  }

   /**
   * Get errKey
   * @return errKey
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getErrKey() {
    return errKey;
  }

  public void setErrKey(String errKey) {
    this.errKey = errKey;
  }

  public BaseResult errMessage(String errMessage) {
    this.errMessage = errMessage;
    return this;
  }

   /**
   * Get errMessage
   * @return errMessage
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getErrMessage() {
    return errMessage;
  }

  public void setErrMessage(String errMessage) {
    this.errMessage = errMessage;
  }

  public BaseResult errDescription(String errDescription) {
    this.errDescription = errDescription;
    return this;
  }

   /**
   * Get errDescription
   * @return errDescription
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getErrDescription() {
    return errDescription;
  }

  public void setErrDescription(String errDescription) {
    this.errDescription = errDescription;
  }

  public BaseResult result(Object result) {
    this.result = result;
    return this;
  }

   /**
   * Get result
   * @return result
  **/
  @ApiModelProperty(example = "null", value = "")
  public Object getResult() {
    return result;
  }

  public void setResult(Object result) {
    this.result = result;
  }

  public BaseResult success(Boolean success) {
    this.success = success;
    return this;
  }

   /**
   * Get success
   * @return success
  **/
  @ApiModelProperty(example = "null", value = "")
  public Boolean getSuccess() {
    return success;
  }

  public void setSuccess(Boolean success) {
    this.success = success;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    BaseResult baseResult = (BaseResult) o;
    return Objects.equals(this.errCode, baseResult.errCode) &&
        Objects.equals(this.errKey, baseResult.errKey) &&
        Objects.equals(this.errMessage, baseResult.errMessage) &&
        Objects.equals(this.errDescription, baseResult.errDescription) &&
        Objects.equals(this.result, baseResult.result) &&
        Objects.equals(this.success, baseResult.success);
  }

  @Override
  public int hashCode() {
    return Objects.hash(errCode, errKey, errMessage, errDescription, result, success);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BaseResult {\n");
    
    sb.append("    errCode: ").append(toIndentedString(errCode)).append("\n");
    sb.append("    errKey: ").append(toIndentedString(errKey)).append("\n");
    sb.append("    errMessage: ").append(toIndentedString(errMessage)).append("\n");
    sb.append("    errDescription: ").append(toIndentedString(errDescription)).append("\n");
    sb.append("    result: ").append(toIndentedString(result)).append("\n");
    sb.append("    success: ").append(toIndentedString(success)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

