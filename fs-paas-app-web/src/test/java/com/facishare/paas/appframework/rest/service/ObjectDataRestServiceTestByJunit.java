package com.facishare.paas.appframework.rest.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.common.service.EmployeeService;
import com.facishare.paas.appframework.coordination.CrmService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.CustomSceneService;
import com.facishare.paas.appframework.metadata.DataSnapshotLogicService;
import com.facishare.paas.appframework.metadata.MetaDataActionService;
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverterManager;
import com.facishare.paas.appframework.privilege.DataPrivilegeService;
import com.facishare.paas.appframework.rest.dto.data.*;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.provider.Arguments;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2025/6/10
 * @Description : ObjectDataRestService的JUnit5单元测试类
 */
@ExtendWith(MockitoExtension.class)
class ObjectDataRestServiceTestByJunit {

    @InjectMocks
    private ObjectDataRestService objectDataRestService;

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private InfraServiceFacade infraServiceFacade;

    @Mock
    private FieldDataConverterManager fieldDataConverterManager;

    @Mock
    private DataPrivilegeService dataPrivilegeService;

    @Mock
    private CustomSceneService customSceneService;

    @Mock
    private CrmService crmService;

    @Mock
    private DataSnapshotLogicService dataSnapshotLogicService;

    @Mock
    private EmployeeService employeeService;


    // 测试数据
    private static final String TENANT_ID = "74255";
    private static final String USER_ID = "1000";
    private static final String DATA_ID = "test_data_id_123";
    private static final String DESCRIBE_API_NAME = "TestObject__c";
    private static final String DESCRIBE_ID = "describe_id_123";

    private User testUser;
    private RequestContext requestContext;
    private IObjectDescribe mockDescribe;
    private IObjectData mockObjectData;
    private IActionContext mockActionContext;

    @BeforeEach
    void setUp() {
        // 初始化测试用户
        testUser = User.builder()
                .tenantId(TENANT_ID)
                .userId(USER_ID)
                .build();

        // 初始化请求上下文
        requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(testUser)
                .peerName("Test")
                .build();

        // 初始化Mock对象描述
        mockDescribe = mock(IObjectDescribe.class);
        when(mockDescribe.getId()).thenReturn(DESCRIBE_ID);
        when(mockDescribe.getApiName()).thenReturn(DESCRIBE_API_NAME);
        when(mockDescribe.getPackage()).thenReturn("com.test");
        when(mockDescribe.containsField(anyString())).thenReturn(true);
        when(mockDescribe.getFieldDescribes()).thenReturn(Lists.newArrayList());

        // 初始化Mock对象数据
        mockObjectData = mock(IObjectData.class);
        when(mockObjectData.getId()).thenReturn(DATA_ID);
        when(mockObjectData.getDescribeApiName()).thenReturn(DESCRIBE_API_NAME);
        when(mockObjectData.getDescribeId()).thenReturn(DESCRIBE_ID);
        when(mockObjectData.getTenantId()).thenReturn(TENANT_ID);

        // 初始化Mock ActionContext
        mockActionContext = mock(IActionContext.class);
        when(mockActionContext.getUserId()).thenReturn(USER_ID);
        when(mockActionContext.getEnterpriseId()).thenReturn(TENANT_ID);
    }

    /**
     * 创建测试用的CreateData.Arg对象
     */
    private CreateData.Arg createTestCreateDataArg() {
        return CreateData.Arg.builder()
                .json("{\"name\":\"测试数据\"}")
                .descAPIName(DESCRIBE_API_NAME)
                .isTool(false)
                .isSpecifyTime(false)
                .triggerWorkFlow(false)
                .calculateDefaultValue(true)
                .build();
    }

    /**
     * 创建测试用的UpdateData.Arg对象
     */
    private UpdateData.Arg createTestUpdateDataArg() {
        return UpdateData.Arg.builder()
                .dataJson("{\"name\":\"更新后的测试数据\"}")
                .dataId(DATA_ID)
                .describeAPIName(DESCRIBE_API_NAME)
                .triggerFlow(false)
                .isTool(false)
                .isSpecifyTime(false)
                .notValidate(false)
                .skipImmutableFieldValidate(false)
                .incrementalUpdate(false)
                .skipModifyLog(false)
                .includeDescribe(true)
                .applyDataPrivilegeCheck(false)
                .applyValidationRule(false)
                .useSnapshotForApproval(false)
                .processDataConflicts(false)
                .build();
    }

    /**
     * 创建测试用的BulkCreateData.Arg对象
     */
    private BulkCreateData.Arg createTestBulkCreateDataArg() {
        JSONArray dataArray = new JSONArray();
        JSONObject data1 = new JSONObject();
        data1.put("name", "批量测试数据1");
        JSONObject data2 = new JSONObject();
        data2.put("name", "批量测试数据2");
        dataArray.add(data1);
        dataArray.add(data2);

        return BulkCreateData.Arg.builder()
                .dataListJson(dataArray.toJSONString())
                .describeApiName(DESCRIBE_API_NAME)
                .isTool(false)
                .triggerWorkFlow(false)
                .triggerApprovalFlow(false)
                .calculateDefaultValue(true)
                .build();
    }

    /**
     * 创建测试用的SearchTemplateQuery对象
     */
    private SearchTemplateQuery createTestSearchTemplateQuery() {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(10);
        query.setOffset(0);
        return query;
    }

    /**
     * 创建测试用的QueryResult对象
     */
    private QueryResult<IObjectData> createTestQueryResult() {
        QueryResult<IObjectData> result = new QueryResult<>();
        result.setTotalNumber(1);
        result.setData(Lists.newArrayList(mockObjectData));
        return result;
    }

    // ========== 以下是各个方法的测试用例 ==========

    /**
     * GenerateByAI
     * 测试内容描述：测试createObjectData方法在正常场景下的执行
     */
    @Test
    @DisplayName("正常场景 - 创建对象数据成功")
    void testCreateObjectData_Success() {
        // 准备测试数据
        CreateData.Arg arg = createTestCreateDataArg();

        // 配置Mock行为
        when(serviceFacade.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockDescribe);
        when(serviceFacade.saveObjectData(eq(testUser), any(IObjectData.class), any(MetaDataActionService.CreateAttributes.class))).thenReturn(mockObjectData);
        doNothing().when(serviceFacade).log(eq(testUser), any(), any(), eq(mockDescribe), anyList());

        // 执行被测试方法
        CreateData.Result result = objectDataRestService.createObjectData(arg, requestContext);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeDocument());
        assertNotNull(result.getObjectDataDocument());

        // 验证Mock交互
        verify(serviceFacade).findObject(TENANT_ID, DESCRIBE_API_NAME);
        verify(serviceFacade).saveObjectData(eq(testUser), any(IObjectData.class), any(MetaDataActionService.CreateAttributes.class));
        verify(serviceFacade).log(eq(testUser), any(), any(), eq(mockDescribe), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试createObjectData方法在权限检查失败时抛出异常
     */
    @Test
    @DisplayName("异常场景 - OpenAPI权限检查失败")
    void testCreateObjectDataThrowsValidateException_OpenAPIPrivilegeCheckFailed() {
        // 准备测试数据
        CreateData.Arg arg = createTestCreateDataArg();
        requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(testUser)
                .peerName("OpenAPI")
                .build();

        // 配置Mock行为 - 非管理员用户
        when(serviceFacade.isAdmin(testUser)).thenReturn(false);

        // 执行并验证异常
        Exception exception = assertThrows(ValidateException.class, () -> {
            objectDataRestService.createObjectData(arg, requestContext);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("Bad Request"));

        // 验证Mock交互
        verify(serviceFacade).isAdmin(testUser);
    }

    // TODO: 继续添加其他方法的测试用例...

    /**
     * GenerateByAI
     * 测试内容描述：测试batchCreateObjectData方法的正常场景
     */
    @Test
    @DisplayName("正常场景 - 批量创建对象数据成功")
    void testBatchCreateObjectData_Success() {
        // 准备测试数据
        BulkCreateData.Arg arg = createTestBulkCreateDataArg();
        List<IObjectData> savedDataList = Lists.newArrayList(mockObjectData);

        // 配置Mock行为
        when(serviceFacade.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockDescribe);
        when(serviceFacade.bulkSaveObjectData(anyList(), eq(testUser), any())).thenReturn(savedDataList);
        doNothing().when(serviceFacade).log(eq(testUser), any(), any(), eq(mockDescribe), eq(savedDataList));

        // 执行被测试方法
        BulkCreateData.Result result = objectDataRestService.batchCreateObjectData(arg, requestContext);

        // 验证结果
        assertNotNull(result);
        assertEquals(DESCRIBE_API_NAME, result.getDescribeApiName());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());

        // 验证Mock交互
        verify(serviceFacade).findObject(TENANT_ID, DESCRIBE_API_NAME);
        verify(serviceFacade).bulkSaveObjectData(anyList(), eq(testUser), any());
        verify(serviceFacade).log(eq(testUser), any(), any(), eq(mockDescribe), eq(savedDataList));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试updateObjectData方法的正常场景
     */
    @Test
    @DisplayName("正常场景 - 更新对象数据成功")
    void testUpdateObjectData_Success() {
        // 准备测试数据
        UpdateData.Arg arg = createTestUpdateDataArg();

        // 配置Mock行为
        when(serviceFacade.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockDescribe);
        when(serviceFacade.findObjectDataIgnoreAll(testUser, DATA_ID, DESCRIBE_API_NAME)).thenReturn(mockObjectData);
        when(serviceFacade.updateObjectData(eq(testUser), any(IObjectData.class), any(IActionContext.class))).thenReturn(mockObjectData);

        // 执行被测试方法
        UpdateData.Result result = objectDataRestService.updateObjectData(arg, requestContext);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getObjectDataDocument());
        assertNotNull(result.getDescribeDocument());

        // 验证Mock交互
        verify(serviceFacade).findObject(TENANT_ID, DESCRIBE_API_NAME);
        verify(serviceFacade).findObjectDataIgnoreAll(testUser, DATA_ID, DESCRIBE_API_NAME);
        verify(serviceFacade).updateObjectData(eq(testUser), any(IObjectData.class), any(IActionContext.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findBySearchTemplateQuery方法的正常场景
     */
    @Test
    @DisplayName("正常场景 - 通过搜索模板查询数据成功")
    void testFindBySearchTemplateQuery_Success() {
        // 准备测试数据
        FindBySearchTemplateQuery.Arg arg = FindBySearchTemplateQuery.Arg.builder()
                .describeAPIName(DESCRIBE_API_NAME)
                .searchTemplateQuery(createTestSearchTemplateQuery())
                .includeDescribe(true)
                .build();

        QueryResult<IObjectData> queryResult = createTestQueryResult();

        // 配置Mock行为
        when(serviceFacade.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockDescribe);
        when(serviceFacade.findBySearchQuery(any(IActionContext.class), eq(DESCRIBE_API_NAME), any(SearchTemplateQuery.class)))
                .thenReturn(queryResult);

        // 执行被测试方法
        FindBySearchTemplateQuery.Result result = objectDataRestService.findBySearchTemplateQuery(arg, requestContext);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeDocument());
        assertNotNull(result.getQueryResult());
        assertEquals(1, result.getQueryResult().getTotalNumber());
        assertEquals(1, result.getQueryResult().getData().size());

        // 验证Mock交互
        verify(serviceFacade).findObject(TENANT_ID, DESCRIBE_API_NAME);
        verify(serviceFacade).findBySearchQuery(any(IActionContext.class), eq(DESCRIBE_API_NAME), any(SearchTemplateQuery.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findDataById方法的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据ID查询数据成功")
    void testFindDataById_Success() {
        // 准备测试数据
        FindById.Arg arg = FindById.Arg.builder()
                .dataId(DATA_ID)
                .descAPIName(DESCRIBE_API_NAME)
                .includeDescribe(true)
                .includeLookup(false)
                .checkPrivilege(false)
                .includeDeleted(false)
                .includeInvalid(false)
                .keepAllMultiLangValue(false)
                .build();

        // 配置Mock行为
        when(serviceFacade.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockDescribe);
        when(serviceFacade.findObjectData(any(IActionContext.class), eq(DATA_ID), eq(mockDescribe)))
                .thenReturn(mockObjectData);

        // 执行被测试方法
        FindById.Result result = objectDataRestService.findDataById(arg, requestContext);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeDocument());
        assertNotNull(result.getObjectDataDocument());

        // 验证Mock交互
        verify(serviceFacade).findObject(TENANT_ID, DESCRIBE_API_NAME);
        verify(serviceFacade).findObjectData(any(IActionContext.class), eq(DATA_ID), eq(mockDescribe));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invalidByDataId方法的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据ID废弃数据成功")
    void testInvalidByDataId_Success() {
        // 准备测试数据
        DeletedById.Arg arg = DeletedById.Arg.builder()
                .dataId(DATA_ID)
                .describeAPIName(DESCRIBE_API_NAME)
                .includeDescribe(true)
                .build();

        List<IObjectDescribe> detailDescribes = Lists.newArrayList();

        // 配置Mock行为
        when(serviceFacade.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockDescribe);
        when(serviceFacade.findObjectDataIgnoreFormula(testUser, DATA_ID, DESCRIBE_API_NAME))
                .thenReturn(mockObjectData);
        when(serviceFacade.invalid(eq(mockObjectData), eq(testUser), any(IActionContext.class)))
                .thenReturn(mockObjectData);
        when(serviceFacade.findDetailDescribes(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(detailDescribes);
        doNothing().when(serviceFacade).masterDetailLog(eq(testUser), any(), any(), any(), anyList());

        // 执行被测试方法
        DeletedById.Result result = objectDataRestService.invalidByDataId(arg, requestContext);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeDocument());
        assertNotNull(result.getObjectDataDocument());

        // 验证Mock交互
        verify(serviceFacade).findObject(TENANT_ID, DESCRIBE_API_NAME);
        verify(serviceFacade).findObjectDataIgnoreFormula(testUser, DATA_ID, DESCRIBE_API_NAME);
        verify(serviceFacade).invalid(eq(mockObjectData), eq(testUser), any(IActionContext.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试delete方法的正常场景
     */
    @Test
    @DisplayName("正常场景 - 删除数据成功")
    void testDelete_Success() {
        // 准备测试数据
        DeletedById.Arg arg = DeletedById.Arg.builder()
                .dataId(DATA_ID)
                .describeAPIName(DESCRIBE_API_NAME)
                .includeDescribe(true)
                .build();

        List<IObjectData> deletedList = Lists.newArrayList(mockObjectData);

        // 配置Mock行为
        when(serviceFacade.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockDescribe);
        when(serviceFacade.bulkDelete(anyList(), eq(testUser))).thenReturn(deletedList);
        doNothing().when(serviceFacade).log(eq(testUser), any(), any(), eq(mockDescribe), eq(deletedList));

        // 执行被测试方法
        DeletedById.Result result = objectDataRestService.delete(arg, requestContext);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getObjectDataDocument());
        assertNotNull(result.getDescribeDocument());

        // 验证Mock交互
        verify(serviceFacade).findObject(TENANT_ID, DESCRIBE_API_NAME);
        verify(serviceFacade).bulkDelete(anyList(), eq(testUser));
        verify(serviceFacade).log(eq(testUser), any(), any(), eq(mockDescribe), eq(deletedList));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findByIds方法的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据ID列表查询数据成功")
    void testFindByIds_Success() {
        // 准备测试数据
        FindDataByIds.Arg arg = FindDataByIds.Arg.builder()
                .describeAPIName(DESCRIBE_API_NAME)
                .idList(Lists.newArrayList(DATA_ID))
                .build();

        List<IObjectData> objectDataList = Lists.newArrayList(mockObjectData);

        // 配置Mock行为
        when(serviceFacade.findObjectDataByIds(any(IActionContext.class), anyList(), eq(DESCRIBE_API_NAME)))
                .thenReturn(objectDataList);

        // 执行被测试方法
        FindDataByIds.Result result = objectDataRestService.findByIds(arg, requestContext);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDataList());
        assertEquals(1, result.getDataList().size());

        // 验证Mock交互
        verify(serviceFacade).findObjectDataByIds(any(IActionContext.class), anyList(), eq(DESCRIBE_API_NAME));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试batchUpdate方法的正常场景
     */
    @Test
    @DisplayName("正常场景 - 批量更新数据成功")
    void testBatchUpdate_Success() {
        // 准备测试数据
        JSONArray dataArray = new JSONArray();
        JSONObject updateData = new JSONObject();
        updateData.put("_id", DATA_ID);
        updateData.put("name", "更新的名称");
        dataArray.add(updateData);

        BatchUpdateData.Arg arg = BatchUpdateData.Arg.builder()
                .describeApiName(DESCRIBE_API_NAME)
                .dataListJson(dataArray.toJSONString())
                .build();

        List<IObjectData> dbDataList = Lists.newArrayList(mockObjectData);
        List<IObjectData> updatedDataList = Lists.newArrayList(mockObjectData);

        // 配置Mock行为
        when(serviceFacade.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockDescribe);
        when(serviceFacade.findObjectDataByIdsIgnoreAll(eq(TENANT_ID), anyList(), eq(DESCRIBE_API_NAME)))
                .thenReturn(dbDataList);
        doNothing().when(serviceFacade).calculateForBatchEditData(any(User.class), anyList(), anyList(), eq(mockDescribe));
        when(serviceFacade.batchUpdateByFields(any(IActionContext.class), anyList(), anyList()))
                .thenReturn(updatedDataList);

        // 执行被测试方法
        BatchUpdateData.Result result = objectDataRestService.batchUpdate(arg, requestContext);

        // 验证结果
        assertNotNull(result);
        assertEquals("OK", result.getResult());

        // 验证Mock交互
        verify(serviceFacade).findObject(TENANT_ID, DESCRIBE_API_NAME);
        verify(serviceFacade).findObjectDataByIdsIgnoreAll(eq(TENANT_ID), anyList(), eq(DESCRIBE_API_NAME));
        verify(serviceFacade).batchUpdateByFields(any(IActionContext.class), anyList(), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试updateObjectData方法在数据版本冲突时的处理
     */
    @Test
    @DisplayName("异常场景 - 数据版本冲突处理")
    void testUpdateObjectData_VersionConflict() {
        // 准备测试数据
        UpdateData.Arg arg = createTestUpdateDataArg();
        arg.setProcessDataConflicts(true);

        // 配置Mock行为
        when(serviceFacade.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockDescribe);
        when(serviceFacade.findObjectDataIgnoreAll(testUser, DATA_ID, DESCRIBE_API_NAME)).thenReturn(mockObjectData);

        // 执行被测试方法
        UpdateData.Result result = objectDataRestService.updateObjectData(arg, requestContext);

        // 验证结果
        assertNotNull(result);

        // 验证Mock交互
        verify(serviceFacade).findObject(TENANT_ID, DESCRIBE_API_NAME);
        verify(serviceFacade).findObjectDataIgnoreAll(testUser, DATA_ID, DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findNameByIds方法的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据ID列表查询名称成功")
    void testFindNameByIds_Success() {
        // 准备测试数据
        FindNameByIds.Arg arg = FindNameByIds.Arg.builder()
                .describeAPIName(DESCRIBE_API_NAME)
                .idList(Lists.newArrayList(DATA_ID))
                .namingConvention("default")
                .build();

        // 配置Mock行为
        List<com.facishare.paas.metadata.api.INameCache> nameList = Lists.newArrayList();
        when(serviceFacade.findRecordName(any(IActionContext.class), eq(DESCRIBE_API_NAME), anyList()))
                .thenReturn(nameList);

        // 执行被测试方法
        FindNameByIds.Result result = objectDataRestService.findNameByIds(arg, requestContext);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getQueryResult());

        // 验证Mock交互
        verify(serviceFacade).findRecordName(any(IActionContext.class), eq(DESCRIBE_API_NAME), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试recover方法的正常场景
     */
    @Test
    @DisplayName("正常场景 - 恢复数据成功")
    void testRecover_Success() {
        // 准备测试数据
        RecoverData.Arg arg = RecoverData.Arg.builder()
                .dataId(DATA_ID)
                .describeAPIName(DESCRIBE_API_NAME)
                .build();

        List<IObjectDescribe> detailDescribes = Lists.newArrayList();

        // 配置Mock行为
        when(serviceFacade.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockDescribe);
        when(serviceFacade.recover(any(IObjectData.class), any(IActionContext.class), eq(testUser)))
                .thenReturn(mockObjectData);
        when(serviceFacade.findDetailDescribes(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(detailDescribes);
        doNothing().when(serviceFacade).masterDetailLog(eq(testUser), any(), any(), any(), anyList());

        // 执行被测试方法
        RecoverData.Result result = objectDataRestService.recover(arg, requestContext);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getObjectDataDocument());
        assertNotNull(result.getDescribeDocument());

        // 验证Mock交互
        verify(serviceFacade).findObject(TENANT_ID, DESCRIBE_API_NAME);
        verify(serviceFacade).recover(any(IObjectData.class), any(IActionContext.class), eq(testUser));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试calculateCountValue方法的正常场景
     */
    @Test
    @DisplayName("正常场景 - 计算统计值成功")
    void testCalculateCountValue_Success() {
        // 准备测试数据
        CalculateCountValue.Arg arg = new CalculateCountValue.Arg();

        // 配置Mock行为
        when(serviceFacade.getCountValue(eq(TENANT_ID), any(), any())).thenReturn(100L);

        // 执行被测试方法
        CalculateCountValue.Result result = objectDataRestService.calculateCountValue(requestContext, arg);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());

        // 验证Mock交互
        verify(serviceFacade).getCountValue(eq(TENANT_ID), any(), any());
    }

    // TODO: 后续可以添加更多方法的测试用例...

    /**
     * GenerateByAI
     * 测试内容描述：测试batchCreateObjectData方法在空数据时的处理
     */
    @Test
    @DisplayName("边界场景 - 批量创建空数据")
    void testBatchCreateObjectData_EmptyData() {
        // 准备测试数据
        BulkCreateData.Arg arg = BulkCreateData.Arg.builder()
                .dataListJson("[]")
                .describeApiName(DESCRIBE_API_NAME)
                .isTool(false)
                .triggerWorkFlow(false)
                .triggerApprovalFlow(false)
                .calculateDefaultValue(true)
                .build();

        // 执行被测试方法
        BulkCreateData.Result result = objectDataRestService.batchCreateObjectData(arg, requestContext);

        // 验证结果
        assertNotNull(result);

        // 验证Mock交互 - 应该没有调用任何服务方法
        verify(serviceFacade, never()).findObject(anyString(), anyString());
        verify(serviceFacade, never()).bulkSaveObjectData(anyList(), any(User.class), any());
    }

    /**
     * 提供测试用例的参数化数据源
     */
    private static Stream<Arguments> provideTestDataForParameterizedTests() {
        return Stream.of(
                Arguments.of("TestObject1__c", "data_id_1", true),
                Arguments.of("TestObject2__c", "data_id_2", false),
                Arguments.of("TestObject3__c", "data_id_3", true)
        );
    }
}