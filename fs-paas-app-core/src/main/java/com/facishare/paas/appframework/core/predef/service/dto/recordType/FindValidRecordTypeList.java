package com.facishare.paas.appframework.core.predef.service.dto.recordType;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

public interface FindValidRecordTypeList {

    @Data
    class Arg{
        @JSONField(name = "M1")
        String describeApiName;
        @JSONField(name = "M2")
        @JsonProperty("is_only_active")
        boolean onlyActive;
        @JsonProperty("is_import")
        boolean forImport;

    }

    @Data
    class Result{
        @JSONField(name = "M8")
        private List record_list;
    }
}
