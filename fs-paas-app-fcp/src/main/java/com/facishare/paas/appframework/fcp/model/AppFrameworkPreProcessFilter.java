package com.facishare.paas.appframework.fcp.model;

import com.facishare.fcp.filter.PreProcessFilter;
import com.facishare.fcp.handler.FcpServiceContext;
import com.facishare.fcp.protocol.FcpHeader;
import com.facishare.fcp.protocol.FcpRequest;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.model.RequestContext.CLIENT_INFO;

/**
 * Created by zhouwr on 2017/11/11
 */
@Component
@Slf4j
public class AppFrameworkPreProcessFilter implements PreProcessFilter {
    @Override
    public void doFilter(FcpServiceContext fcpServiceContext) {
        RequestContext.ContentType contentType = getContentType(fcpServiceContext);
        String postId = fcpServiceContext.getStringFcpHeader(FcpHeader.FcpHeaderType.POST_ID);
        String tenantId = String.valueOf(fcpServiceContext.getAuthInfo().getEnterpriseId());
        String employeeId = String.valueOf(fcpServiceContext.getAuthInfo().getEmployeeId());
        String clientInfo = fcpServiceContext.getAuthInfo().getClientInfo();

        String outTenantId = fcpServiceContext.getLongFcpHeader(FcpHeader.FcpHeaderType.OUT_TENANT_ID) == 0 ? null : String.valueOf(fcpServiceContext.getLongFcpHeader(FcpHeader.FcpHeaderType.OUT_TENANT_ID));
        String outUserId = fcpServiceContext.getLongFcpHeader(FcpHeader.FcpHeaderType.OUT_USER_ID) == 0 ? null : String.valueOf(fcpServiceContext.getLongFcpHeader(FcpHeader.FcpHeaderType.OUT_USER_ID));

        String appId = fcpServiceContext.getStringFcpHeader(FcpHeader.FcpHeaderType.OUT_APP_ID);
        String localeInfo = fcpServiceContext.getStringFcpHeader(FcpHeader.FcpHeaderType.LANGUAGE_LOCALE);

        User user = User.builder()
                .tenantId(tenantId)
                .userId(employeeId)
                .outTenantId(outTenantId)
                .outUserId(outUserId)
                .build();

        String uri = getServiceMethod(fcpServiceContext.getFcpRequest());

        RequestContext requestContext = RequestContext.builder()
                .requestSource(RequestContext.RequestSource.CEP)
                .peerName(RequestContext.RequestSource.CEP.name())
                .contentType(contentType)
                .tenantId(tenantId)
                .user(user)
                .appId(appId)
                .postId(postId)
                .clientInfo(clientInfo)
                .lang(Lang.of(localeInfo))
                .requestUri(uri)
                .build();

        requestContext.setAttribute(CLIENT_INFO, fcpServiceContext.getStringFcpHeader(FcpHeader.FcpHeaderType.CLIENT_INFO));
        RequestContextManager.setContext(requestContext);

        log.info("FcpRequest URI:{} clientInfo:{}", uri, clientInfo);

        //设置埋点
//        try {
//            String operationId = fcpServiceContext.getFcpRequest().getHeader(FcpHeader.FcpHeaderType.QUERY_NAME)
//                    .getStringValue();
//            OperateReportUtil.postMessage(tenantId,user.getUserId(), ProductLine.CRM_SERVICE,
//                    "fs-crm","FCP", operationId);
//        }catch (Exception e){}
    }

    private RequestContext.ContentType getContentType(FcpServiceContext fcpContext) {
        int contentType = (int) fcpContext.getLongFcpHeader(FcpHeader.FcpHeaderType.CONTENT_TYPE);
        switch (contentType) {
            case 2:
                return RequestContext.ContentType.SIMPLE_JSON;
            case 3:
                return RequestContext.ContentType.FULL_JSON;
            default:
                return null;
        }
    }

    private String getServiceMethod(FcpRequest request) {
        FcpHeader header = request.getHeader(FcpHeader.FcpHeaderType.QUERY_NAME);
        if (header == null) {
            header = request.getHeader(FcpHeader.FcpHeaderType.NOTIFY_NAME);
        }

        if (header != null && header.getStringValue() != null) {
            String serviceMethod = header.getStringValue();
            List<String> methodFields = Splitter.on(".").splitToList(serviceMethod);
            if (methodFields.size() < 2) {
                methodFields = Splitter.on("/").omitEmptyStrings().splitToList(serviceMethod);
            }

            return methodFields.stream().collect(Collectors.joining("."));
        } else {
            return null;
        }
    }
}
