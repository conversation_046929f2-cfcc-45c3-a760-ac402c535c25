package com.facishare.paas.appframework.core.predef.service.dto.data;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface ValidateFunction {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        @SerializedName("describeApiName")
        private String describeApiName;
        @SerializedName("objectDataId")
        private String objectDataId;
        @SerializedName("buttonApiName")
        private String buttonApiName;
        @SerializedName("args")
        private Map<String,Object> args;
        @SerializedName("objectData")
        private ObjectDataDocument objectData;
        @SerializedName("details")
        Map<String, List<ObjectDataDocument>> details;
    }
    @Data
    @Builder
    class Result {
//        private ObjectDataDocument objectData;
//        private Map<String, List<ObjectDataDocument>> details;
//        private String targetDescribeApiName;
        private Boolean hasReturnValue;
        private Object returnValue;
    }
}
