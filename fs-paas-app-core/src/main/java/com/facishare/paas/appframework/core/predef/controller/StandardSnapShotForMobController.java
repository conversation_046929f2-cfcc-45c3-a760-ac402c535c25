package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.service.dto.log.GetSnapShotForMod;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.Map;
import java.util.Optional;

/**
 * @Desc
 * <AUTHOR>
 * @CreateTime 2019-09-24 16:05
 */
public class StandardSnapShotForMobController extends AbstractStandardSnapShotController<GetSnapShotForMod.Arg, GetSnapShotForMod.Result> {

    @Override
    protected Optional<IObjectDescribe> getDescribe(GetSnapShotForMod.Result result) {
        return Optional.ofNullable(result)
                .map(GetSnapShotForMod.Result::getDescribe)
                .filter(CollectionUtils::notEmpty)
                .map(ObjectDescribeExt::of);
    }

    @Override
    protected Optional<IObjectData> getObjectData(GetSnapShotForMod.Result result) {
        return Optional.ofNullable(result)
                .map(GetSnapShotForMod.Result::getData)
                .filter(CollectionUtils::notEmpty)
                .map(ObjectDataExt::of);
    }

    @Override
    protected GetSnapShotForMod.Result doService(GetSnapShotForMod.Arg arg) {
        LogInfo logInfo = serviceFacade.getLogById(arg.getApiName(), arg.getLogId(), controllerContext.getUser());
        IObjectDescribe objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), arg.getApiName());
        Map<String, Object> objData = logInfo.getSnapshot().getSnapshot();
        objData = decorateObjectData(controllerContext.getUser(), objData, objectDescribe);
        // 补充负责人主属部门字段
        fillOwnerDeptName(controllerContext.getUser(), objData);
        fillFieldInfo(controllerContext.getUser(), objectDescribe, objData);
        fillMaskFieldValue(objData, objectDescribe);
        fillDimensionFiledValue(objData, objectDescribe);
        GetSnapShotForMod.Result result = new GetSnapShotForMod.Result();
        result.setData(objData);
        result.setDescribe(objectDescribe == null ? null : ObjectDescribeExt.of(objectDescribe).toMap());
        result.setLayout(getSnapShotLayout(objData, objectDescribe, controllerContext));
        result.setCanRecover(false);
        result.setObjectDescribeExtra(getFieldExtra(objectDescribe));
        return result;
    }

}
