package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.metadata.button.GetSpecialButtons;
import com.facishare.paas.appframework.metadata.button.SpecialButtonManager;
import com.facishare.paas.appframework.metadata.button.SpecialButtonProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@ServiceModule("SpecialButtonProvider")
public class ObjectSpecialButtonProviderService {

    @Autowired
    private SpecialButtonManager providerManager;

    @ServiceMethod("getSpecialButtons")
    public GetSpecialButtons.Result getSpecialButtons(GetSpecialButtons.Arg arg, ServiceContext context) {
        SpecialButtonProvider provider = providerManager.getLocalProvider(arg.getDescribeApiName());
        return GetSpecialButtons.Result.of(provider.getSpecialButtons());
    }

}
