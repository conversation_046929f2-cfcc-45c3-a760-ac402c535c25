package com.facishare.paas.appframework.metadata.util;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2019/1/7 9:47 AM
 * 创建xml
 */
public class XmlUtil {

    /* xml根元素名称 */
    private final static String DEFAULT_ROOT_NAME = "Document";
    /* 表示目录的属性 */
    private final static String DIRECTORY = "D";
    /* 表示名称的属性 */
    private final static String NAME = "N";
    /* 表示文件的属性 */
    private final static String FILE = "F";
    /* 表示NPath的属性 */
    private final static String NP = "NP";
    /* 过滤作为目录名称的字符串中特殊字符*/
    private final static String SPECIAL_PATTERN = "[/\\\\:\"<>?|*\\.]";
    private final static Pattern pattern = Pattern.compile(SPECIAL_PATTERN);
    private final static Pattern spacePattern = Pattern.compile("\\u0003");
    private final static String SUFFIX_PATTERN = "^\\w+\\.\\w+\\.\\w+$";

    /**
     * 构建一级目录结构
     *
     * @param describe 对象名称作为一级目录
     * @param dataList 需要导出的数据
     * @return xml的字符串
     */
    public static String create1LayerXml(IObjectDescribe describe, List<IFieldDescribe> fieldsToExport, List<IObjectData> dataList) {
        return create1LayerXml(describe, fieldsToExport, dataList, false);
    }


    public static String create1LayerXml(IObjectDescribe describe, List<IFieldDescribe> fieldsToExport, List<IObjectData> dataList, boolean rename) {
        Document doc = DocumentHelper.createDocument();
        Element root = doc.addElement(DEFAULT_ROOT_NAME);
        // 以对象描述名称为名创建一级目录
        Element directory = root.addElement(DIRECTORY).addAttribute(NAME, filterName(describe.getDisplayName()));
        dataList.forEach(data -> {
            for (IFieldDescribe fieldDescribe : fieldsToExport) {
                if (Objects.nonNull(data.get(fieldDescribe.getApiName()))) {
                    // fullName形式为：filename.Npath
                    String[] fullNames = data.get(fieldDescribe.getApiName(), String.class).split("\\|");
                    for (String fullName : fullNames) {
                        if (StringUtils.isBlank(fullName)) {
                            continue;
                        }
                        String filename = getFileName(fullName);
                        if (rename) {
                            filename = renameFileName(data.getName(), filename);
                        }
                        String path = getPath(fullName);
                        //创建文件
                        if (!StringUtils.isEmpty(fullName)) {
                            directory.addElement(FILE)
                                    .addAttribute(NAME, filename)
                                    .addAttribute(NP, path);
                        }

                    }
                }
            }
        });
        return doc.asXML();
    }

    public static String createExportFileAndImage(Map<String, String> pathAndName, String fileName,
                                                  List<IObjectDescribe> describes,
                                                  Map<String, List<IFieldDescribe>> fieldsToExport,
                                                  Map<String, List<IObjectData>> dataListMap) {
        Document doc = DocumentHelper.createDocument();
        Element root = doc.addElement(DEFAULT_ROOT_NAME);
        root.addElement(FILE).addAttribute(NAME, fileName);
        if (CollectionUtils.notEmpty(pathAndName)) {
            pathAndName.forEach((path, name) -> {
                root.addElement(FILE)
                        .addAttribute(NAME, fillFileExt(name))
                        .addAttribute(NP, path);
            });
        }
        // 以对象描述名称为名创建一级目录
        describes.forEach(describe -> {
            Element first = root.addElement(DIRECTORY).addAttribute(NAME, filterName(describe.getDisplayName()));
            List<IFieldDescribe> fieldDescribes = fieldsToExport.get(describe.getApiName());
            if (CollectionUtils.empty(fieldDescribes)) {
                return;
            }
            List<IObjectData> dataList = dataListMap.get(describe.getApiName());
            if (CollectionUtils.empty(dataList)) {
                return;
            }
            // 以数据主属性为名建立二级目录
            dataList.forEach(data -> {
                Element second = first.addElement(DIRECTORY).addAttribute(NAME, filterName(data.getName()));
                // 以导出字段为名建立三级目录
                for (IFieldDescribe field : fieldDescribes) {
                    Element third = second.addElement(DIRECTORY).addAttribute(NAME, filterName(field.getLabel()));
                    //在第三级目录下创建文件
                    String names = data.get(field.getApiName(), String.class);
                    if (Objects.nonNull(names)) {
                        for (String fullName : names.split("\\|")) {
                            if (!StringUtils.isEmpty(fullName)) {
                                String filename = getFileName(fullName);
                                String path = getPath(fullName);
                                third.addElement(FILE).addAttribute(NAME, filename)
                                        .addAttribute(NP, path);
                            }
                        }
                    }
                }
            });
        });
        return doc.asXML();
    }

    public static String createExportFile(Map<String, String> pathAndName, String fileName) {
        Document doc = DocumentHelper.createDocument();
        Element root = doc.addElement(DEFAULT_ROOT_NAME);
        root.addElement(FILE).addAttribute(NAME, fileName);
        if (CollectionUtils.notEmpty(pathAndName)) {
            pathAndName.forEach((path, name) -> {
                root.addElement(FILE)
                        .addAttribute(NAME, name)
                        .addAttribute(NP, path);
            });
        }
        return doc.asXML();
    }

    private static String fillFileExt(String fileName) {
        return fileName + ".xlsx";
    }

    private static String renameFileName(String dataName, String filename) {
        String legalName = filterName(dataName);
        if (StringUtils.isNotBlank(legalName)) {
            return legalName + "_" + filename;
        }
        return filename;
    }


    /**
     * 去除后缀名重复的部分
     */
    private static String fixName(String fullName) {
        if (fullName.matches(SUFFIX_PATTERN)) {
            return fullName.substring(0, fullName.lastIndexOf('.'));
        }
        return fullName;
    }

    /**
     * 根据fullName获取图片/附件的文件名 fileName.ext#Npath
     */
    private static String getFileName(String fullName) {
        String nameAndExt = StringUtils.substringBeforeLast(fullName, "#");
        String name = StringUtils.substringBeforeLast(nameAndExt, ".");
        String ext = StringUtils.substringAfterLast(nameAndExt, ".");

        String realName = filterName(name);
        if (StringUtils.isBlank(realName)) {
            realName = I18NExt.getOrDefault(I18NKey.UNNAME, "Unnamed");
        }
        if (StringUtils.isNotBlank(ext)) {
            return realName + "." + ext;
        }
        return realName;
    }

    /**
     * 根据fullname获取图片/附件的NPath
     */
    private static String getPath(String fullName) {
        String path = StringUtils.substringAfterLast(fullName, "#");
        if (path.matches(SUFFIX_PATTERN)) {
            return StringUtils.substringBeforeLast(path, ".");
        }
        return path;
    }


    /**
     * 创建三级目录接口的xml
     *
     * @param describe       对象描述
     * @param fieldsToExport 需要导出的字段
     * @param dataList       数据列表
     * @return xml字符串
     */
    public static String create3LayerXml(IObjectDescribe describe, List<IFieldDescribe> fieldsToExport, List<IObjectData> dataList) {
        Document doc = DocumentHelper.createDocument();
        Element root = doc.addElement(DEFAULT_ROOT_NAME);
        // 以对象描述名称为名创建一级目录
        Element first = root.addElement(DIRECTORY).addAttribute(NAME, filterName(describe.getDisplayName()));
        // 以数据主属性为名建立二级目录
        for (IObjectData data : dataList) {
            Element second = first.addElement(DIRECTORY).addAttribute(NAME, filterName(data.getName()));
            // 以导出字段为名建立三级目录
            for (IFieldDescribe field : fieldsToExport) {
                Element third = second.addElement(DIRECTORY).addAttribute(NAME, filterName(field.getLabel()));
                //在第三级目录下创建文件
                String names = data.get(field.getApiName(), String.class);
                if (Objects.nonNull(names)) {
                    for (String fullName : names.split("\\|")) {
                        if (!StringUtils.isEmpty(fullName)) {
//                            fullName = fixName(fullName);
                            String filename = getFileName(fullName);
                            String path = getPath(fullName);
                            third.addElement(FILE).addAttribute(NAME, filename)
                                    .addAttribute(NP, path);
                        }
                    }
                }
            }
        }
        return doc.asXML();
    }

    /**
     * 判断生成的xml是否包含图片或者附件
     */
    public static boolean hasFile(String xml) throws DocumentException {
        Document doc = DocumentHelper.parseText(xml);
        Element root = doc.getRootElement();
        Element target = (Element) root.selectSingleNode("//" + FILE);
        return Objects.nonNull(target);
    }

    public static String filterName(String str) {
        if (StringUtils.isBlank(str)) {
            return I18NExt.getOrDefault(I18NKey.UNNAME, "Unnamed");
        }
        Matcher m = spacePattern.matcher(str);
        String spaceStr = m.replaceAll(" ").trim();
        String result = pattern.matcher(spaceStr).replaceAll("").trim();
        if (Strings.isNullOrEmpty(result)) {
            return I18NExt.getOrDefault(I18NKey.UNNAME, "Unnamed");
        }
        return result;
    }
}
