package com.facishare.paas.appframework.fcp.model;

import com.facishare.fcp.server.FcpServerContextListener;
import org.jboss.resteasy.plugins.spring.SpringContextLoaderSupport;
import org.jboss.resteasy.plugins.spring.i18n.Messages;
import org.springframework.web.context.ConfigurableWebApplicationContext;

import javax.servlet.ServletContext;
import javax.servlet.ServletContextEvent;

/**
 * Created by zhouwr on 2017/10/11
 */
public class FcpRestServerContextListener extends FcpServerContextListener {

    private SpringContextLoaderSupport springContextLoaderSupport = new SpringContextLoaderSupport();

    public FcpRestServerContextListener() {
    }

    public void contextInitialized(ServletContextEvent event) {
        boolean scanProviders = false;
        boolean scanResources = false;
        String sProviders = event.getServletContext().getInitParameter("resteasy.scan.providers");
        if (sProviders != null) {
            scanProviders = Boolean.valueOf(sProviders.trim()).booleanValue();
        }

        String scanAll = event.getServletContext().getInitParameter("resteasy.scan");
        if (scanAll != null) {
            boolean tmp = Boolean.valueOf(scanAll.trim()).booleanValue();
            scanProviders = tmp || scanProviders;
            scanResources = tmp || scanResources;
        }

        String sResources = event.getServletContext().getInitParameter("resteasy.scan.resources");
        if (sResources != null) {
            scanResources = Boolean.valueOf(sResources.trim()).booleanValue();
        }

        if (!scanProviders && !scanResources) {
            super.contextInitialized(event);
        } else {
            throw new RuntimeException(Messages.MESSAGES.cannotUseScanParameters());
        }
    }

    protected void customizeContext(ServletContext servletContext,
                                    ConfigurableWebApplicationContext configurableWebApplicationContext) {
        super.customizeContext(servletContext, configurableWebApplicationContext);
        this.springContextLoaderSupport.customizeContext(servletContext, configurableWebApplicationContext);
    }

}
