package com.facishare.paas.appframework.rest.service;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.metadata.TeamMemberInfoPoJo;
import com.facishare.paas.appframework.rest.dto.data.FindTeamMember;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 默认对象服务单元测试
 */
@ExtendWith(MockitoExtension.class)
class DefObjectRestServiceTest {

    @Mock
    private ServiceFacade serviceFacade;

    @InjectMocks
    private DefObjectRestService defObjectRestService;

    private RequestContext requestContext;
    private User user;

    @BeforeEach
    void setUp() {
        user = new User("74255", "1000");
        requestContext = RequestContext.builder()
                .user(user)
                .tenantId("74255")
                .build();
    }

    @Test
    void testGetTeamMember_Success() {
        // Given
        FindTeamMember.Arg arg = FindTeamMember.Arg.builder()
                .dataId("testDataId")
                .describeAPIName("testApiName")
                .build();

        IObjectData mockObjectData = mock(IObjectData.class);
        when(serviceFacade.findObjectDataIgnoreFormula(any(User.class), anyString(), anyString()))
                .thenReturn(mockObjectData);

        TeamMember mockTeamMember = mock(TeamMember.class);
        when(mockTeamMember.getEmployeeList()).thenReturn(Lists.newArrayList());
        when(mockTeamMember.getRoleCode()).thenReturn("testRole");
        when(mockTeamMember.getPermission()).thenReturn(TeamMember.Permission.READANDWRITE);
        when(mockTeamMember.getMemberType()).thenReturn(TeamMember.MemberType.EMPLOYEE);
        when(mockTeamMember.getSourceType()).thenReturn("testSource");
        when(mockTeamMember.getOutTenantId()).thenReturn("testOutTenantId");

        try (MockedStatic<ObjectDataExt> mockedStatic = mockStatic(ObjectDataExt.class)) {
            ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);
            mockedStatic.when(() -> ObjectDataExt.of(mockObjectData)).thenReturn(mockObjectDataExt);
            when(mockObjectDataExt.getTeamMembers()).thenReturn(Lists.newArrayList(mockTeamMember));

            // When
            FindTeamMember.Result result = defObjectRestService.getTeamMember(arg, requestContext);

            // Then
            assertNotNull(result);
            assertNotNull(result.getTeamMemberInfoPoJos());
            assertEquals(1, result.getTeamMemberInfoPoJos().size());

            TeamMemberInfoPoJo teamMemberInfo = result.getTeamMemberInfoPoJos().get(0);
            assertEquals("testRole", teamMemberInfo.getTeamMemberRole());
            assertEquals(TeamMember.Permission.READANDWRITE.getValue(), teamMemberInfo.getTeamMemberPermissionType());
            assertEquals(TeamMember.MemberType.EMPLOYEE.getValue(), teamMemberInfo.getTeamMemberType());
            assertEquals("testSource", teamMemberInfo.getSourceType());
            assertEquals("testOutTenantId", teamMemberInfo.getOutTenantId());
        }

        verify(serviceFacade, times(1)).findObjectDataIgnoreFormula(user, "testDataId", "testApiName");
    }

    @Test
    void testGetTeamMember_EmptyTeamMembers() {
        // Given
        FindTeamMember.Arg arg = FindTeamMember.Arg.builder()
                .dataId("testDataId")
                .describeAPIName("testApiName")
                .build();

        IObjectData mockObjectData = mock(IObjectData.class);
        when(serviceFacade.findObjectDataIgnoreFormula(any(User.class), anyString(), anyString()))
                .thenReturn(mockObjectData);

        try (MockedStatic<ObjectDataExt> mockedStatic = mockStatic(ObjectDataExt.class)) {
            ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);
            mockedStatic.when(() -> ObjectDataExt.of(mockObjectData)).thenReturn(mockObjectDataExt);
            when(mockObjectDataExt.getTeamMembers()).thenReturn(Collections.emptyList());

            // When
            FindTeamMember.Result result = defObjectRestService.getTeamMember(arg, requestContext);

            // Then
            assertNotNull(result);
            assertNotNull(result.getTeamMemberInfoPoJos());
            assertTrue(result.getTeamMemberInfoPoJos().isEmpty());
        }
    }

    @Test
    void testGetTeamMember_NullTeamMembers() {
        // Given
        FindTeamMember.Arg arg = FindTeamMember.Arg.builder()
                .dataId("testDataId")
                .describeAPIName("testApiName")
                .build();

        IObjectData mockObjectData = mock(IObjectData.class);
        when(serviceFacade.findObjectDataIgnoreFormula(any(User.class), anyString(), anyString()))
                .thenReturn(mockObjectData);

        try (MockedStatic<ObjectDataExt> mockedStatic = mockStatic(ObjectDataExt.class)) {
            ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);
            mockedStatic.when(() -> ObjectDataExt.of(mockObjectData)).thenReturn(mockObjectDataExt);
            when(mockObjectDataExt.getTeamMembers()).thenReturn(null);

            // When
            FindTeamMember.Result result = defObjectRestService.getTeamMember(arg, requestContext);

            // Then
            assertNotNull(result);
            assertNotNull(result.getTeamMemberInfoPoJos());
            assertTrue(result.getTeamMemberInfoPoJos().isEmpty());
        }
    }

    @ParameterizedTest
    @NullAndEmptySource
    @ValueSource(strings = {"   "})
    void testGetTeamMember_InvalidDataId(String dataId) {
        // Given
        FindTeamMember.Arg arg = FindTeamMember.Arg.builder()
                .dataId(dataId)
                .describeAPIName("testApiName")
                .build();

        IObjectData mockObjectData = mock(IObjectData.class);
        when(serviceFacade.findObjectDataIgnoreFormula(any(User.class), nullable(String.class), anyString()))
                .thenReturn(mockObjectData);

        try (MockedStatic<ObjectDataExt> mockedStatic = mockStatic(ObjectDataExt.class)) {
            ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);
            mockedStatic.when(() -> ObjectDataExt.of(mockObjectData)).thenReturn(mockObjectDataExt);
            when(mockObjectDataExt.getTeamMembers()).thenReturn(Collections.emptyList());

            // When
            FindTeamMember.Result result = defObjectRestService.getTeamMember(arg, requestContext);

            // Then
            assertNotNull(result);
            verify(serviceFacade, times(1)).findObjectDataIgnoreFormula(user, dataId, "testApiName");
        }
    }

    @ParameterizedTest
    @NullAndEmptySource
    @ValueSource(strings = {"   "})
    void testGetTeamMember_InvalidDescribeAPIName(String describeAPIName) {
        // Given
        FindTeamMember.Arg arg = FindTeamMember.Arg.builder()
                .dataId("testDataId")
                .describeAPIName(describeAPIName)
                .build();

        IObjectData mockObjectData = mock(IObjectData.class);
        when(serviceFacade.findObjectDataIgnoreFormula(any(User.class), anyString(), nullable(String.class)))
                .thenReturn(mockObjectData);

        try (MockedStatic<ObjectDataExt> mockedStatic = mockStatic(ObjectDataExt.class)) {
            ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);
            mockedStatic.when(() -> ObjectDataExt.of(mockObjectData)).thenReturn(mockObjectDataExt);
            when(mockObjectDataExt.getTeamMembers()).thenReturn(Collections.emptyList());

            // When
            FindTeamMember.Result result = defObjectRestService.getTeamMember(arg, requestContext);

            // Then
            assertNotNull(result);
            verify(serviceFacade, times(1)).findObjectDataIgnoreFormula(user, "testDataId", describeAPIName);
        }
    }

    @Test
    void testGetTeamMember_MultipleTeamMembers() {
        // Given
        FindTeamMember.Arg arg = FindTeamMember.Arg.builder()
                .dataId("testDataId")
                .describeAPIName("testApiName")
                .build();

        IObjectData mockObjectData = mock(IObjectData.class);
        when(serviceFacade.findObjectDataIgnoreFormula(any(User.class), anyString(), anyString()))
                .thenReturn(mockObjectData);

        TeamMember mockTeamMember1 = mock(TeamMember.class);
        when(mockTeamMember1.getEmployeeList()).thenReturn(Lists.newArrayList());
        when(mockTeamMember1.getRoleCode()).thenReturn("1");
        when(mockTeamMember1.getPermission()).thenReturn(TeamMember.Permission.READANDWRITE);
        when(mockTeamMember1.getMemberType()).thenReturn(TeamMember.MemberType.EMPLOYEE);

        TeamMember mockTeamMember2 = mock(TeamMember.class);
        when(mockTeamMember2.getEmployeeList()).thenReturn(Lists.newArrayList());
        when(mockTeamMember2.getRoleCode()).thenReturn("2");
        when(mockTeamMember2.getPermission()).thenReturn(TeamMember.Permission.READANDWRITE);
        when(mockTeamMember2.getMemberType()).thenReturn(TeamMember.MemberType.DEPARTMENT);

        try (MockedStatic<ObjectDataExt> mockedStatic = mockStatic(ObjectDataExt.class)) {
            ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);
            mockedStatic.when(() -> ObjectDataExt.of(mockObjectData)).thenReturn(mockObjectDataExt);
            when(mockObjectDataExt.getTeamMembers()).thenReturn(Lists.newArrayList(mockTeamMember1, mockTeamMember2));

            // When
            FindTeamMember.Result result = defObjectRestService.getTeamMember(arg, requestContext);

            // Then
            assertNotNull(result);
            assertNotNull(result.getTeamMemberInfoPoJos());
            assertEquals(2, result.getTeamMemberInfoPoJos().size());
        }
    }

    @Test
    void testGetTeamMember_ServiceFacadeReturnsNull() {
        // Given
        FindTeamMember.Arg arg = FindTeamMember.Arg.builder()
                .dataId("testDataId")
                .describeAPIName("testApiName")
                .build();

        when(serviceFacade.findObjectDataIgnoreFormula(any(User.class), anyString(), anyString()))
                .thenReturn(null);

        // When & Then
        assertThrows(NullPointerException.class, () -> {
            defObjectRestService.getTeamMember(arg, requestContext);
        });
    }

    @Test
    void testGetTeamMember_NullArg() {
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            defObjectRestService.getTeamMember(null, requestContext);
        });
    }

    @Test
    void testGetTeamMember_NullRequestContext() {
        // Given
        FindTeamMember.Arg arg = FindTeamMember.Arg.builder()
                .dataId("testDataId")
                .describeAPIName("testApiName")
                .build();

        // When & Then
        assertThrows(NullPointerException.class, () -> {
            defObjectRestService.getTeamMember(arg, null);
        });
    }
}
