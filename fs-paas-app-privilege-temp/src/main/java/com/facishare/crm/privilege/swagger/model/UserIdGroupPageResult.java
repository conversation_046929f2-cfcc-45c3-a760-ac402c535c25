package com.facishare.crm.privilege.swagger.model;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class UserIdGroupPageResult {
  @SerializedName("errCode")
  private Integer errCode = null;

  @SerializedName("errKey")
  private String errKey = null;

  @SerializedName("errMessage")
  private String errMessage = null;

  @SerializedName("errDescription")
  private String errDescription = null;

  @SerializedName("result")
  private List<UserIdGroupPojo> result = new ArrayList<UserIdGroupPojo>();

  @SerializedName("pageInfo")
  private PageInfo pageInfo = null;

  @SerializedName("success")
  private Boolean success = false;

  public UserIdGroupPageResult errCode(Integer errCode) {
    this.errCode = errCode;
    return this;
  }

  /**
   * Get errCode
   * @return errCode
   **/
  @ApiModelProperty(example = "null", value = "")
  public Integer getErrCode() {
    return errCode;
  }

  public void setErrCode(Integer errCode) {
    this.errCode = errCode;
  }

  public UserIdGroupPageResult errKey(String errKey) {
    this.errKey = errKey;
    return this;
  }

  /**
   * Get errKey
   * @return errKey
   **/
  @ApiModelProperty(example = "null", value = "")
  public String getErrKey() {
    return errKey;
  }

  public void setErrKey(String errKey) {
    this.errKey = errKey;
  }

  public UserIdGroupPageResult errMessage(String errMessage) {
    this.errMessage = errMessage;
    return this;
  }

  /**
   * Get errMessage
   * @return errMessage
   **/
  @ApiModelProperty(example = "null", value = "")
  public String getErrMessage() {
    return errMessage;
  }

  public void setErrMessage(String errMessage) {
    this.errMessage = errMessage;
  }

  public UserIdGroupPageResult errDescription(String errDescription) {
    this.errDescription = errDescription;
    return this;
  }

  /**
   * Get errDescription
   * @return errDescription
   **/
  @ApiModelProperty(example = "null", value = "")
  public String getErrDescription() {
    return errDescription;
  }

  public void setErrDescription(String errDescription) {
    this.errDescription = errDescription;
  }

  public UserIdGroupPageResult result(List<UserIdGroupPojo> result) {
    this.result = result;
    return this;
  }

  public UserIdGroupPageResult addResultItem(UserIdGroupPojo resultItem) {
    this.result.add(resultItem);
    return this;
  }

  /**
   * Get result
   * @return result
   **/
  @ApiModelProperty(example = "null", value = "")
  public List<UserIdGroupPojo> getResult() {
    return result;
  }

  public void setResult(List<UserIdGroupPojo> result) {
    this.result = result;
  }

  public UserIdGroupPageResult pageInfo(PageInfo pageInfo) {
    this.pageInfo = pageInfo;
    return this;
  }

  /**
   * Get pageInfo
   * @return pageInfo
   **/
  @ApiModelProperty(example = "null", value = "")
  public PageInfo getPageInfo() {
    return pageInfo;
  }

  public void setPageInfo(PageInfo pageInfo) {
    this.pageInfo = pageInfo;
  }

  public UserIdGroupPageResult success(Boolean success) {
    this.success = success;
    return this;
  }

  /**
   * Get success
   * @return success
   **/
  @ApiModelProperty(example = "null", value = "")
  public Boolean getSuccess() {
    return success;
  }

  public void setSuccess(Boolean success) {
    this.success = success;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    UserIdGroupPageResult userIdGroupPageResult = (UserIdGroupPageResult) o;
    return Objects.equals(this.errCode, userIdGroupPageResult.errCode) &&
      Objects.equals(this.errKey, userIdGroupPageResult.errKey) &&
      Objects.equals(this.errMessage, userIdGroupPageResult.errMessage) &&
      Objects.equals(this.errDescription, userIdGroupPageResult.errDescription) &&
      Objects.equals(this.result, userIdGroupPageResult.result) &&
      Objects.equals(this.pageInfo, userIdGroupPageResult.pageInfo) &&
      Objects.equals(this.success, userIdGroupPageResult.success);
  }

  @Override
  public int hashCode() {
    return Objects.hash(errCode, errKey, errMessage, errDescription, result, pageInfo, success);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class GroupPageResult {\n");

    sb.append("    errCode: ").append(toIndentedString(errCode)).append("\n");
    sb.append("    errKey: ").append(toIndentedString(errKey)).append("\n");
    sb.append("    errMessage: ").append(toIndentedString(errMessage)).append("\n");
    sb.append("    errDescription: ").append(toIndentedString(errDescription)).append("\n");
    sb.append("    result: ").append(toIndentedString(result)).append("\n");
    sb.append("    pageInfo: ").append(toIndentedString(pageInfo)).append("\n");
    sb.append("    success: ").append(toIndentedString(success)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
