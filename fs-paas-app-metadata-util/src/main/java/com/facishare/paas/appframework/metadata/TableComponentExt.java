package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.layout.component.NewTableComponentExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.TableColumn;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.*;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.experimental.Delegate;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2018/2/23
 */
public class TableComponentExt {

    private static final String IS_SHOW_TAG = "is_show_tag";
    private static final String SHOW_IMAGE = "show_image";
    private static final String AGENT_TYPE = "agent_type";

    public static final String FIELD_SECTION = "field_section";
    public static final String IS_NEW_LAYOUT = "is_new_layout";
    public static final String NEW_LAYOUT = "new_layout";

    public static final String COMPONENT_TYPE_TABLE = "table";

    @Getter
    @Delegate
    private ITableComponent tableComponent;

    public final static Set<String> UNSUPPORTED_QUOTE_FIELD_TYPE_IN_LIST = Sets.newHashSet(IFieldType.FILE_ATTACHMENT, IFieldType.SIGNATURE);

    private TableComponentExt(ITableComponent tableComponent) {
        this.tableComponent = tableComponent;
    }

    public static TableComponentExt of(ITableComponent tableComponent) {
        return new TableComponentExt(tableComponent);
    }

    public void removeFields(Set<String> toRemove) {
        if (enableNewLayout()) {
            getNewTableComponentExt()
                    .ifPresent(it -> it.removeByFieldNames(toRemove));
            return;
        }
        List<Map> fields = (List<Map>) convert().get(ITableComponent.INCLUDE_FIELDS);
        if (CollectionUtils.notEmpty(fields)) {
            fields.removeIf(x -> toRemove.contains(TableColumnExt.of(x).getApiName()));
        }
        List<IFieldSection> fieldSections = getFieldSections();
        if (CollectionUtils.notEmpty(fieldSections)) {
            fieldSections.forEach(x -> {
                List<IFormField> formFields = x.getFields().stream()
                        .filter(formField -> !toRemove.contains(formField.getFieldName()))
                        .collect(Collectors.toList());
                x.setFields(formFields);
            });
        }
    }

    public void removeFieldByTypes(ObjectDescribeExt describe, String... fieldTypes) {
        if (enableNewLayout()) {
            getNewTableComponentExt()
                    .ifPresent(it -> it.removeByTypes(describe, Sets.newHashSet(fieldTypes)));
            return;
        }
        Set<String> types = Sets.newHashSet(fieldTypes);
        List<Map> fields = (List<Map>) convert().get(ITableComponent.INCLUDE_FIELDS);
        if (CollectionUtils.empty(fields)) {
            return;
        }
        fields.removeIf(x -> isInTypes(describe, TableColumnExt.of(x).getTableColumn(), types));
    }

    private void adjustFieldRenderType(String objectApiName) {
        getFieldList().forEach(x -> x.setRenderType(LayoutExt.getRenderType(objectApiName,
                TableColumnExt.of(x).getApiName(), x.getRenderType())));
    }

    public void adjustFieldRenderType(IObjectDescribe describe) {
        if (enableNewLayout()) {
            getNewTableComponentExt()
                    .ifPresent(it -> it.adjustFieldRenderType(describe));
            return;
        }
        getFieldList().forEach(x -> {
            TableColumnExt tableColumnExt = TableColumnExt.of(x);
            String fieldApiName = tableColumnExt.getApiName();
            // layout 中可能没有保存 renderType, 需要取字段描述中的 type
            String type = ObjectDescribeExt.of(describe).getFieldDescribeSilently(fieldApiName)
                    .map(IFieldDescribe::getType).orElse(null);
            x.setRenderType(LayoutExt.getRenderType(describe.getApiName(), fieldApiName, type));
            tableColumnExt.remove(ITableColumn.LABEL_NAME);
        });
    }

    private boolean isInTypes(ObjectDescribeExt describe, ITableColumn formField, Set<String> fieldTypeSet) {
        return describe.getFieldDescribeSilently(TableColumnExt.of(formField).getApiName())
                .map(o -> fieldTypeSet.contains(o.getType())).orElse(false);
    }

    private List<ITableColumn> getFieldList() {
        List<Map> fields = (List<Map>) convert().get(ITableComponent.INCLUDE_FIELDS);
        return null != fields ? fields.stream().map(TableColumn::new).collect(Collectors.toList()) : Lists.newArrayList();
    }

    private Map convert() {
        return ((TableComponent) tableComponent).getContainerDocument();
    }

    public void correctLabel(ObjectDescribeExt describeExt) {
        if (enableNewLayout()) {
            getNewTableComponentExt().ifPresent(it -> it.correctLabel(describeExt));
            return;
        }
        getFieldList()
                //.filter(a -> !Strings.isNullOrEmpty(a.getName()))
                .forEach(x -> describeExt.getFieldDescribeSilently(TableColumnExt.of(x).getApiName())
                        .ifPresent(f -> x.setLabelName(f.getLabel())));
    }

    public void setDefaultFieldListIfEmpty() {
        if (enableNewLayout()) {
            // 高级布局不需要补充字段
            getNewTableComponentExt().ifPresent(NewTableComponentExt::setDefaultFieldListIfEmpty);
            return;
        }
        if (CollectionUtils.notEmpty(getFieldList())) {
            return;
        }
        setIncludeFields(Lists.newArrayList(TableColumnExt.buildNameColumn()));
    }

    /**
     * 新布局没有传统意义上的左右布局
     *
     * @return 开启左右布局，返回左半边布局的字段
     */
    public List<IFormField> getFieldsInFirstSection() {
        if (enableNewLayout()) {
            return Collections.emptyList();
        }
        if (CollectionUtils.empty(getFieldSections())) {
            return Collections.emptyList();
        }
        return getFieldSections().get(0).getFields();
    }

    public List<String> getFieldListAnyway() {
        if (enableNewLayout()) {
            return getNewTableComponentExt()
                    .map(NewTableComponentExt::getFieldList)
                    .orElseGet(Lists::newArrayList);
        }
        List<ITableColumn> fieldList = getFieldList();
        if (CollectionUtils.notEmpty(fieldList)) {
            return fieldList.stream().map(a -> TableColumnExt.of(a).getApiName()).collect(Collectors.toList());
        } else {
            List<String> fields = Lists.newArrayList();
            CollectionUtils.nullToEmpty(getFieldSections()).forEach(a -> {
                CollectionUtils.nullToEmpty(a.getFields())
                        .stream()
                        .filter(b -> !Strings.isNullOrEmpty(b.getFieldName()))
                        .forEach(b -> fields.add(b.getFieldName()));
            });
            return fields;
        }
    }

    private boolean containsKey(String key) {
        return ((TableComponent) tableComponent).containsKey(key);
    }

    public void initRenderShowImageAndTag() {
        if (!containsKey(IS_SHOW_TAG)) {
            removeIsShowTag();
        }
        if (!containsKey(SHOW_IMAGE)) {
            removeShowImage();
        }
    }

    private void removeIsShowTag() {
        if (enableNewLayout()) {
            return;
        }
        tableComponent.set(IS_SHOW_TAG, false);
    }

    public boolean isShowTag() {
        if (enableNewLayout()) {
            return getNewTableComponentExt()
                    .map(NewTableComponentExt::isShowTag)
                    .orElse(false);
        }
        return BooleanUtils.isTrue(tableComponent.get(IS_SHOW_TAG, Boolean.class));
    }

    public String getShowImage() {
        if (enableNewLayout()) {
            return getNewTableComponentExt()
                    .map(NewTableComponentExt::getShowImage)
                    .orElse(null);
        }
        return tableComponent.get(SHOW_IMAGE, String.class);
    }

    public void removeShowImage() {
        if (enableNewLayout()) {
            getNewTableComponentExt().ifPresent(NewTableComponentExt::removeShowImage);
            return;
        }
        tableComponent.set(SHOW_IMAGE, null);
    }

    private void setAgentTypeToComponent() {
        tableComponent.set(AGENT_TYPE, ILayout.AGENT_TYPE_MOBILE);
    }

    public void removeIfEmpty(Set<String> fields) {
        if (enableNewLayout()) {
            return;
        }
        if (CollectionUtils.empty(fields)) {
            return;
        }
        fields.forEach(this::remove);
    }

    private void remove(String field) {
        if (containsKey(field) && ObjectUtils.isEmpty(get(field))) {
            ((TableComponent) tableComponent).remove(field);
        }
    }

    public boolean enableNewLayout() {
        return BooleanUtils.isTrue(get(IS_NEW_LAYOUT, Boolean.class));
    }

    public Optional<NewTableComponentExt> getNewTableComponentExt() {
        Map newLayout = (Map) get(NEW_LAYOUT);
        if (Objects.isNull(newLayout)) {
            return Optional.empty();
        }
        return Optional.of(new NewTableComponentExt(newLayout));
    }

    public void replaceByNewTableComponent() {
        if (!enableNewLayout()) {
            return;
        }

        getNewTableComponentExt().ifPresent(it -> {
            tableComponent.set(SHOW_IMAGE, it.getShowImage());
            tableComponent.set(IS_SHOW_TAG, it.isShowTag());
            List<IFieldSection> fieldSections = this.getFieldSections();
            List<ITableColumn> tableColumns = it.convertToTableColumn();
            if (CollectionUtils.empty(fieldSections)) {
                // 没有开启左右布局、使用 includeFields
                tableComponent.setIncludeFields(tableColumns);
            } else {
                // 开启了左右布局、只替换 fieldSections 的左半部分
                List<IFormField> fields = tableColumns.stream()
                        .map(TableColumnExt::of)
                        .map(x -> new FormField(x.toMap()))
                        .collect(Collectors.toList());
                fieldSections.get(0).setFields(fields);
            }
        });

    }

    /**
     * 健壮性，即补充 table 组件 必要属性<br>
     * 缺少必要属性，设计器端和执行渲染的移动端无法正常使用该组件<br>
     * 缺少 api_name ：影响设计器配置组件<br>
     * 缺少 include_fields[*].api_name ：影响设计器配置组件<br>
     */
    public void robust() {
        String apiName = tableComponent.get(IComponent.NAME, String.class);
        List<ITableColumn> includes = tableComponent.getIncludeFields();
        List<IFieldSection> fieldSections = tableComponent.getFieldSections();
        if (!StringUtils.equals(ComponentExt.TABLE_COMPONENT, apiName)) {
            tableComponent.setName(ComponentExt.TABLE_COMPONENT);
        }

        if (CollectionUtils.notEmpty(fieldSections)) {
            fieldSections.forEach(section -> {
                List<IFormField> fields = section.getFields();
                if (CollectionUtils.empty(fields)) {
                    return;
                }
                fields.forEach(field -> {
                    if (Objects.isNull(field.get(IComponent.NAME, String.class))) {
                        field.set(IComponent.NAME, field.getFieldName());
                    }
                });
            });
        } else if (CollectionUtils.notEmpty(includes)) {
            includes.forEach(column -> {
                if (Objects.isNull(column.get(IComponent.NAME, String.class))) {
                    column.set(IComponent.NAME, column.getName());
                }
            });
        }
    }
}
