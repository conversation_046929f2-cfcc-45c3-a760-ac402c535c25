package com.facishare.paas.appframework.metadata.switchcache.provider;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.UniqueRuleLogicService;
import com.facishare.paas.appframework.metadata.switchcache.SwitchInfo;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.switchcache.provider.SwitchCacheProviderManager.UNIQUE_RULE;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/04/23
 */
@Component
public class UniqueRuleSwitchCacheProvider extends BaseSwitchCacheProvider {
    @Autowired
    @Qualifier("uniqueRuleLogicService")
    private UniqueRuleLogicService uniqueRuleLogicService;

    private static final Map<String, Function<IUniqueRule, Boolean>> funcMap;
    /**
     * 从外部导入 excel 时
     */
    public static final String USE_WHEN_IMPORT_EXCEL = "useWhenImportExcel";
    /**
     * 调用 OpenAPI 时
     */
    public static final String USE_WHEN_CALL_OPEN_API = "useWhenCallOpenApi";

    @Override
    public String getSwitchType() {
        return UNIQUE_RULE;
    }

    @Override
    public SwitchInfo getSwitchInfo(User user, String bindingObjectApiName, String switchName) {
        IUniqueRule uniqueRule = uniqueRuleLogicService.findByDescribeApiName(user.getTenantId(), bindingObjectApiName);
        return SwitchInfo.of(user.getTenantId(), getSwitchType(), switchName, bindingObjectApiName, getSwitchStatus(switchName, uniqueRule));
    }

    @Override
    protected Set<String> getSwitchNamesEmpty2All(Collection<String> switchNames) {
        return CollectionUtils.empty(switchNames) ? Sets.newHashSet(funcMap.keySet()) : Sets.newHashSet(switchNames);
    }

    @Override
    public List<SwitchInfo> getSwitchInfos(User user, String bindingObjectApiName, Collection<String> switchNames) {
        switchNames = getSwitchNamesEmpty2All(switchNames);
        if (CollectionUtils.empty(switchNames)) {
            return Collections.emptyList();
        }

        IUniqueRule uniqueRule = uniqueRuleLogicService.findByDescribeApiName(user.getTenantId(), bindingObjectApiName);
        return switchNames.stream()
                .map(switchName -> SwitchInfo.of(user.getTenantId(), getSwitchType(), switchName, bindingObjectApiName, getSwitchStatus(switchName, uniqueRule)))
                .collect(Collectors.toList());
    }

    private boolean getSwitchStatus(String switchName, IUniqueRule uniqueRule) {
        return Objects.isNull(uniqueRule) ? false : funcMap.get(switchName).apply(uniqueRule);
    }

    static {
        funcMap = Maps.newTreeMap(String.CASE_INSENSITIVE_ORDER);
        funcMap.putIfAbsent(USE_WHEN_IMPORT_EXCEL, IUniqueRule::isUseWhenImportExcel);
        funcMap.putIfAbsent(USE_WHEN_CALL_OPEN_API, IUniqueRule::isUseWhenCallOpenApi);
    }
}
