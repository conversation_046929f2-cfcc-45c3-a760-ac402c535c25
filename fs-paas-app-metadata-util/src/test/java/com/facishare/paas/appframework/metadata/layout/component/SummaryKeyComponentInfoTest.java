package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SummaryKeyComponentInfoTest {

  private SummaryKeyComponentInfo summaryKeyComponentInfo;
  
  @Mock
  private IFieldDescribe fieldDescribe;
  
  @Mock
  private IComponent component;

  @BeforeEach
  void setUp() {
    summaryKeyComponentInfo = new SummaryKeyComponentInfo();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试SummaryKeyComponentInfo的无参构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试无参构造函数")
  void testDefaultConstructor() {
    SummaryKeyComponentInfo info = new SummaryKeyComponentInfo();
    assertNotNull(info, "无参构造函数创建的对象不应为空");
    assertEquals("summary_info", info.getType(), "类型应为summary_info");
    assertNotNull(info.getButtons(), "按钮列表应初始化");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试SummaryKeyComponentInfo的Map构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = Maps.newHashMap();
    map.put("name", "test_summary");
    map.put("header", "测试汇总");

    SummaryKeyComponentInfo info = new SummaryKeyComponentInfo(map);
    assertNotNull(info, "Map构造函数创建的对象不应为空");
    assertEquals("summary_info", info.getType(), "类型应为summary_info");
    // 注意：SummaryKeyComponentInfo继承自AbstractComponent，属性访问可能有所不同
    // 这里主要验证对象创建成功和类型设置正确
    assertNotNull(info.getButtons(), "按钮列表应初始化");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFieldSections方法 - 空场景
   */
  @Test
  @DisplayName("正常场景 - 测试getFieldSections方法空场景")
  void testGetFieldSections_Empty() {
    List<IFormField> fieldSections = summaryKeyComponentInfo.getFieldSections();
    
    assertNotNull(fieldSections, "字段区域列表不应为空");
    assertTrue(fieldSections.isEmpty(), "初始字段区域列表应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setFieldSections和getFieldSections方法
   */
  @Test
  @DisplayName("正常场景 - 测试setFieldSections和getFieldSections方法")
  void testSetAndGetFieldSections() {
    // 创建测试字段
    Map<String, Object> fieldMap1 = Maps.newHashMap();
    fieldMap1.put("field_name", "field1");
    fieldMap1.put("label", "字段1");
    FormField field1 = new FormField(fieldMap1);
    
    Map<String, Object> fieldMap2 = Maps.newHashMap();
    fieldMap2.put("field_name", "field2");
    fieldMap2.put("label", "字段2");
    FormField field2 = new FormField(fieldMap2);
    
    List<IFormField> fieldSections = Lists.newArrayList(field1, field2);
    
    // 设置字段区域
    summaryKeyComponentInfo.setFieldSections(fieldSections);
    
    // 获取字段区域
    List<IFormField> retrievedSections = summaryKeyComponentInfo.getFieldSections();
    
    assertNotNull(retrievedSections, "获取的字段区域列表不应为空");
    assertEquals(2, retrievedSections.size(), "字段区域列表大小应为2");
    assertEquals("field1", retrievedSections.get(0).getFieldName(), "第一个字段名应正确");
    assertEquals("field2", retrievedSections.get(1).getFieldName(), "第二个字段名应正确");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试addFieldSection方法
   */
  @Test
  @DisplayName("正常场景 - 测试addFieldSection方法")
  void testAddFieldSection() {
    // 创建测试字段
    Map<String, Object> fieldMap = Maps.newHashMap();
    fieldMap.put("field_name", "test_field");
    fieldMap.put("label", "测试字段");
    FormField field = new FormField(fieldMap);
    
    // 添加字段区域
    summaryKeyComponentInfo.addFieldSection(field);
    
    // 验证添加结果
    List<IFormField> fieldSections = summaryKeyComponentInfo.getFieldSections();
    assertEquals(1, fieldSections.size(), "应添加1个字段区域");
    assertEquals("test_field", fieldSections.get(0).getFieldName(), "字段名应正确");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeFields方法 - 正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试removeFields方法")
  void testRemoveFields_NormalScenario() {
    // 先添加一些字段
    Map<String, Object> fieldMap1 = Maps.newHashMap();
    fieldMap1.put("field_name", "field1");
    FormField field1 = new FormField(fieldMap1);
    
    Map<String, Object> fieldMap2 = Maps.newHashMap();
    fieldMap2.put("field_name", "field2");
    FormField field2 = new FormField(fieldMap2);
    
    Map<String, Object> fieldMap3 = Maps.newHashMap();
    fieldMap3.put("field_name", "field3");
    FormField field3 = new FormField(fieldMap3);
    
    summaryKeyComponentInfo.setFieldSections(Lists.newArrayList(field1, field2, field3));
    
    // 移除指定字段
    Set<String> removeFieldNames = Sets.newHashSet("field1", "field3");
    summaryKeyComponentInfo.removeFields(removeFieldNames);
    
    // 验证移除结果
    List<IFormField> remainingFields = summaryKeyComponentInfo.getFieldSections();
    assertEquals(1, remainingFields.size(), "应剩余1个字段");
    assertEquals("field2", remainingFields.get(0).getFieldName(), "剩余字段应为field2");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeFields方法 - 空集合场景
   */
  @Test
  @DisplayName("边界场景 - 测试removeFields方法处理空集合")
  void testRemoveFields_EmptySet() {
    // 先添加一些字段
    Map<String, Object> fieldMap = Maps.newHashMap();
    fieldMap.put("field_name", "test_field");
    FormField field = new FormField(fieldMap);
    summaryKeyComponentInfo.setFieldSections(Lists.newArrayList(field));
    
    // 移除空集合
    summaryKeyComponentInfo.removeFields(Sets.newHashSet());
    
    // 验证字段未被移除
    List<IFormField> fieldSections = summaryKeyComponentInfo.getFieldSections();
    assertEquals(1, fieldSections.size(), "字段不应被移除");
    assertEquals("test_field", fieldSections.get(0).getFieldName(), "字段名应保持不变");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeFields方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试removeFields方法处理null值")
  void testRemoveFields_NullValue() {
    // 先添加一些字段
    Map<String, Object> fieldMap = Maps.newHashMap();
    fieldMap.put("field_name", "test_field");
    FormField field = new FormField(fieldMap);
    summaryKeyComponentInfo.setFieldSections(Lists.newArrayList(field));
    
    // 移除null值
    summaryKeyComponentInfo.removeFields(null);
    
    // 验证字段未被移除
    List<IFormField> fieldSections = summaryKeyComponentInfo.getFieldSections();
    assertEquals(1, fieldSections.size(), "字段不应被移除");
    assertEquals("test_field", fieldSections.get(0).getFieldName(), "字段名应保持不变");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试onFieldDelete方法 - 正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试onFieldDelete方法")
  void testOnFieldDelete_NormalScenario() {
    // 先添加一些字段
    Map<String, Object> fieldMap1 = Maps.newHashMap();
    fieldMap1.put("field_name", "field_to_delete");
    FormField field1 = new FormField(fieldMap1);
    
    Map<String, Object> fieldMap2 = Maps.newHashMap();
    fieldMap2.put("field_name", "field_to_keep");
    FormField field2 = new FormField(fieldMap2);
    
    summaryKeyComponentInfo.setFieldSections(Lists.newArrayList(field1, field2));
    
    // 配置Mock对象
    when(fieldDescribe.getApiName()).thenReturn("field_to_delete");
    
    // 删除字段
    summaryKeyComponentInfo.onFieldDelete(fieldDescribe);
    
    // 验证删除结果
    List<IFormField> remainingFields = summaryKeyComponentInfo.getFieldSections();
    assertEquals(1, remainingFields.size(), "应剩余1个字段");
    assertEquals("field_to_keep", remainingFields.get(0).getFieldName(), "剩余字段应为field_to_keep");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试onFieldDelete方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试onFieldDelete方法处理null值")
  void testOnFieldDelete_NullValue() {
    // 先添加一些字段
    Map<String, Object> fieldMap = Maps.newHashMap();
    fieldMap.put("field_name", "test_field");
    FormField field = new FormField(fieldMap);
    summaryKeyComponentInfo.setFieldSections(Lists.newArrayList(field));
    
    // 删除null字段
    summaryKeyComponentInfo.onFieldDelete(null);
    
    // 验证字段未被删除
    List<IFormField> fieldSections = summaryKeyComponentInfo.getFieldSections();
    assertEquals(1, fieldSections.size(), "字段不应被删除");
    assertEquals("test_field", fieldSections.get(0).getFieldName(), "字段名应保持不变");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试onFieldUpdate方法
   */
  @Test
  @DisplayName("正常场景 - 测试onFieldUpdate方法")
  void testOnFieldUpdate() {
    // 先添加一些字段
    Map<String, Object> fieldMap = Maps.newHashMap();
    fieldMap.put("field_name", "field_to_update");
    fieldMap.put("label", "原始标签");
    FormField field = new FormField(fieldMap);
    summaryKeyComponentInfo.setFieldSections(Lists.newArrayList(field));
    
    // 准备更新数据
    Map<String, Object> updatedField = Maps.newHashMap();
    updatedField.put("field_name", "field_to_update");
    updatedField.put("label", "更新后标签");
    
    // 更新字段
    summaryKeyComponentInfo.onFieldUpdate("field_to_update", updatedField);
    
    // 验证更新结果
    List<IFormField> fieldSections = summaryKeyComponentInfo.getFieldSections();
    assertEquals(1, fieldSections.size(), "字段数量应保持不变");
    // 注意：由于onFieldUpdate直接操作底层map，这里验证方法被调用即可
    assertNotNull(fieldSections.get(0), "字段应存在");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试containsField方法
   */
  @Test
  @DisplayName("正常场景 - 测试containsField方法")
  void testContainsField() {
    // 先添加一些字段
    Map<String, Object> fieldMap1 = Maps.newHashMap();
    fieldMap1.put("field_name", "existing_field");
    FormField field1 = new FormField(fieldMap1);

    Map<String, Object> fieldMap2 = Maps.newHashMap();
    fieldMap2.put("field_name", "another_field");
    FormField field2 = new FormField(fieldMap2);

    summaryKeyComponentInfo.setFieldSections(Lists.newArrayList(field1, field2));

    // 测试包含的字段
    assertTrue(summaryKeyComponentInfo.containsField("existing_field"), "应包含existing_field");
    assertTrue(summaryKeyComponentInfo.containsField("another_field"), "应包含another_field");

    // 测试不包含的字段
    assertFalse(summaryKeyComponentInfo.containsField("non_existing_field"), "不应包含non_existing_field");

    // 测试null字段 - 由于源码中的lambda表达式会抛出NPE，我们需要捕获异常
    assertThrows(NullPointerException.class, () -> {
      summaryKeyComponentInfo.containsField(null);
    }, "null字段应抛出NullPointerException");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试of静态工厂方法
   */
  @Test
  @DisplayName("正常场景 - 测试of静态工厂方法")
  void testOfStaticMethod() {
    // 创建一个具体的组件对象来测试
    Map<String, Object> testMap = Maps.newHashMap();
    testMap.put("name", "test_component");
    testMap.put("type", "original_type");

    // 创建一个AbstractComponent的实例来测试
    com.facishare.paas.metadata.impl.ui.layout.component.AbstractComponent testComponent =
        new com.facishare.paas.metadata.impl.ui.layout.component.AbstractComponent(testMap) {
          @Override
          public void onFieldUpdate(String fieldApiName, Map fieldMap) {
            // 空实现，仅用于测试
          }

          @Override
          public void onFieldDelete(com.facishare.paas.metadata.api.describe.IFieldDescribe deletedField) {
            // 空实现，仅用于测试
          }
        };

    // 使用静态工厂方法创建对象
    SummaryKeyComponentInfo info = SummaryKeyComponentInfo.of(testComponent);

    assertNotNull(info, "静态工厂方法创建的对象不应为空");
    assertEquals("summary_info", info.getType(), "类型应被重置为summary_info");
    // 注意：由于AbstractComponent的实现细节，这里主要验证对象创建成功
    assertNotNull(info.getButtons(), "按钮列表应初始化");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口实现
   */
  @Test
  @DisplayName("正常场景 - 测试接口实现")
  void testInterfaceImplementation() {
    // 验证实现了ISummaryKeyComponentInfo接口
    assertTrue(summaryKeyComponentInfo instanceof ISummaryKeyComponentInfo, 
               "应实现ISummaryKeyComponentInfo接口");
    
    // 验证继承自AbstractComponent
    assertTrue(summaryKeyComponentInfo instanceof com.facishare.paas.metadata.impl.ui.layout.component.AbstractComponent, 
               "应继承自AbstractComponent");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试复杂的字段操作场景
   */
  @Test
  @DisplayName("正常场景 - 测试复杂字段操作场景")
  void testComplexFieldOperations() {
    // 添加多个字段
    Map<String, Object> fieldMap1 = Maps.newHashMap();
    fieldMap1.put("field_name", "field1");
    fieldMap1.put("label", "字段1");
    FormField field1 = new FormField(fieldMap1);
    
    Map<String, Object> fieldMap2 = Maps.newHashMap();
    fieldMap2.put("field_name", "field2");
    fieldMap2.put("label", "字段2");
    FormField field2 = new FormField(fieldMap2);
    
    Map<String, Object> fieldMap3 = Maps.newHashMap();
    fieldMap3.put("field_name", "field3");
    fieldMap3.put("label", "字段3");
    FormField field3 = new FormField(fieldMap3);
    
    // 设置初始字段
    summaryKeyComponentInfo.setFieldSections(Lists.newArrayList(field1, field2, field3));
    assertEquals(3, summaryKeyComponentInfo.getFieldSections().size(), "初始应有3个字段");
    
    // 添加新字段
    Map<String, Object> fieldMap4 = Maps.newHashMap();
    fieldMap4.put("field_name", "field4");
    fieldMap4.put("label", "字段4");
    FormField field4 = new FormField(fieldMap4);
    summaryKeyComponentInfo.addFieldSection(field4);
    assertEquals(4, summaryKeyComponentInfo.getFieldSections().size(), "添加后应有4个字段");
    
    // 移除部分字段
    summaryKeyComponentInfo.removeFields(Sets.newHashSet("field1", "field3"));
    assertEquals(2, summaryKeyComponentInfo.getFieldSections().size(), "移除后应有2个字段");
    
    // 验证剩余字段
    assertTrue(summaryKeyComponentInfo.containsField("field2"), "应包含field2");
    assertTrue(summaryKeyComponentInfo.containsField("field4"), "应包含field4");
    assertFalse(summaryKeyComponentInfo.containsField("field1"), "不应包含field1");
    assertFalse(summaryKeyComponentInfo.containsField("field3"), "不应包含field3");
    
    // 删除字段
    when(fieldDescribe.getApiName()).thenReturn("field2");
    summaryKeyComponentInfo.onFieldDelete(fieldDescribe);
    assertEquals(1, summaryKeyComponentInfo.getFieldSections().size(), "删除后应有1个字段");
    assertTrue(summaryKeyComponentInfo.containsField("field4"), "应只剩field4");
  }
}
