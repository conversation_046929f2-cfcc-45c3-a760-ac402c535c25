package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.appframework.metadata.button.FilterButtonsManager;
import com.facishare.paas.appframework.metadata.button.FilterButtonsProvider;
import com.facishare.paas.appframework.metadata.button.FilterButtonsProviderProxy;
import com.facishare.paas.metadata.api.IUdefButton;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/12/26
 */
@Service
@ServiceModule("FilterButtonsProvider")
public class ObjectFilterButtonsProviderService {

    @Autowired
    private FilterButtonsManager findButtonsByDescribeApiNameManager;

    @ServiceMethod("findButtons")
    public FilterButtonsProviderProxy.Result findButtons(FilterButtonsProviderProxy.Arg arg, ServiceContext context) {
        FilterButtonsProvider localProvider = findButtonsByDescribeApiNameManager.getLocalProvider(arg.getDescribeApiName());
        List<IUdefButton> udefButtons = CollectionUtils.nullToEmpty(arg.getButtonList()).stream()
                .map(it -> ButtonExt.of(it).getButton())
                .collect(Collectors.toList());
        List<IUdefButton> buttons = localProvider.getButtons(context.getUser(), udefButtons);
        return FilterButtonsProviderProxy.Result.builder()
                .result(CollectionUtils.nullToEmpty(buttons).stream()
                        .map(it -> ButtonExt.of(it).toMap())
                        .collect(Collectors.toList()))
                .build();
    }
}
