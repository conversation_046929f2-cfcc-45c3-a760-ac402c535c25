package com.facishare.paas.appframework.rest.dto.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.metadata.api.ISearchQuery;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created By Yuanxl on 2018/5/4
 */
public interface FindByQuery {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
       ISearchQuery query;
       String describeAPIName;
       Boolean includeDescribe;
    }

    @Data
    class Result {

        @JSONField(name = "describe")
        @JsonProperty(value = "describe")
        @SerializedName("describe")
        ObjectDescribeDocument describeDocument;

        @JSONField(name = "queryResult")
        @JsonProperty(value = "queryResult")
        @SerializedName("queryResult")
        QueryResultInfo queryResult;
    }

    @Data
    class QueryResultInfo {
        List<ObjectDataDocument> data;
        Integer totalNumber;
    }


}
