package com.facishare.paas.appframework.flow;

import java.util.Map;

import com.facishare.paas.appframework.flow.dto.ExistAvailableDef;
import com.facishare.paas.appframework.flow.dto.QueryOneFlowDefinition;
import com.facishare.paas.appframework.flow.dto.TriggerFlow;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

@RestResource(value = "PAAS-FlowCommon", contentType = "application/json")
public interface FlowCommonProxy {
    @POST(value = "flow/def/existAvailableDef", desc = "获取是否存在可发起的业务流、阶段推进器流程定义接口")
    ExistAvailableDef.Result existAvailableDef(@Body ExistAvailableDef.Arg arg, @HeaderMap Map<String, String> header);

    /**
     * 触发服务编排流程
     *
     * @param arg 请求参数
     * @param header 请求头
     * @return 响应结果
     */
    @POST(value = "/one/trigger", desc = "触发工作流")
    TriggerFlow.Result triggerFlow(@Body TriggerFlow.Arg arg, @HeaderMap Map<String, String> header);

    /**
     * 批量查询OneFlow定义简单列表
     *
     * @param arg 请求参数
     * @param header 请求头
     * @return 响应结果
     */
    @POST(value = "/one/def/simpleList", desc = "批量查询OneFlow定义简单列表")
    QueryOneFlowDefinition.Result queryOneFlowDefinitionSimpleList(@Body QueryOneFlowDefinition.Arg arg, @HeaderMap Map<String, String> header);

}