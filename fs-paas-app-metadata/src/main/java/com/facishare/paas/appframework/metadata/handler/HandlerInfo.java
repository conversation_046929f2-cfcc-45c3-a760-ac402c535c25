package com.facishare.paas.appframework.metadata.handler;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.repository.model.HandlerDefinition;
import com.facishare.paas.appframework.metadata.repository.model.HandlerRuntimeConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2023/4/20.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HandlerInfo {
    private String apiName;
    private String label;
    private String description;
    private String handlerType;
    private String providerType;
    private Boolean active;
    private Integer executeOrder;
    private String aplApiName;

    public static HandlerInfo of(HandlerDefinition definition, HandlerRuntimeConfig config) {
        return HandlerInfo.builder()
                .apiName(definition.getApiName())
                .label(definition.i18nLabel())
                .description(definition.i18nDescription())
                .handlerType(definition.getHandlerType())
                .providerType(definition.getProviderType())
                .active(config.isActive())
                .executeOrder(config.getExecuteOrder())
                .aplApiName(definition.getAplApiName())
                .build();
    }

    public static void merge(List<HandlerInfo> newHandlerList, List<HandlerInfo> dbHandlerList) {
        Map<String, HandlerInfo> newHandlerMap = newHandlerList.stream().collect(Collectors.toMap(HandlerInfo::getApiName, x -> x, (a, b) -> a));
        Map<String, HandlerInfo> dbHandlerMap = dbHandlerList.stream().collect(Collectors.toMap(HandlerInfo::getApiName, x -> x, (a, b) -> a));

        //将数据库中的预置HandlerRuntimeConfig合并到页面上的HandlerInfo中
        newHandlerList.forEach(handlerInfo -> {
            HandlerInfo dbHandler = dbHandlerMap.get(handlerInfo.getApiName());
            if (Objects.isNull(dbHandler)) {
                return;
            }
            handlerInfo.setProviderType(dbHandler.getProviderType());
            if (!HandlerDefinition.provideByTenant(dbHandler.getProviderType())) {
                handlerInfo.setHandlerType(dbHandler.getHandlerType());
                handlerInfo.setExecuteOrder(dbHandler.getExecuteOrder());
            }
        });
        //过滤数据库中不存在的预置Handler
        newHandlerList.removeIf(x -> !HandlerDefinition.provideByTenant(x.getProviderType()) && !dbHandlerMap.containsKey(x.getApiName()));
        //将隐藏的预置Handler合并到提交的Handler集合中
        dbHandlerList.stream()
                .filter(x -> !HandlerDefinition.provideByTenant(x.getProviderType()))
                .filter(x -> !newHandlerMap.containsKey(x.getApiName())).forEach(x -> {
                    int index = dbHandlerList.indexOf(x);
                    for (int i = index - 1; i >= 0; i--) {
                        HandlerInfo handlerInfo = dbHandlerList.get(i);
                        if (Objects.equals(handlerInfo.getHandlerType(), x.getHandlerType())
                                && !HandlerDefinition.provideByTenant(handlerInfo.getProviderType())
                                && newHandlerMap.containsKey(handlerInfo.getApiName())) {
                            index = newHandlerList.indexOf(newHandlerMap.get(handlerInfo.getApiName())) + 1;
                            break;
                        }
                    }
                    index = Math.min(index, newHandlerList.size());
                    newHandlerList.add(index, x);
                    newHandlerMap.put(x.getApiName(), x);
                });
    }

    public HandlerDefinition toDefinition(String interfaceCode, String objectApiName) {
        return HandlerDefinition.builder()
                .interfaceCode(interfaceCode)
                .supportObjectApiName(objectApiName)
                .apiName(apiName)
                .label(label)
                .description(description)
                .handlerType(handlerType)
                .providerType(providerType)
                .aplApiName(aplApiName)
                .defaultOrder(executeOrder)
                .build();
    }

    public HandlerRuntimeConfig toConfig(String interfaceCode, String objectApiName) {
        return HandlerRuntimeConfig.builder()
                .interfaceCode(interfaceCode)
                .bindingObjectApiName(objectApiName)
                .handlerApiName(apiName)
                .active(!Boolean.FALSE.equals(active))
                .executeOrder(executeOrder)
                .build();
    }

}
