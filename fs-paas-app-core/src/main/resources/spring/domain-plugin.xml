<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="restServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-paas-appframework-rest" init-method="init"/>

    <bean id="addActionDomainPluginProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.core.predef.domain.AddActionDomainPluginProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="editActionDomainPluginProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.core.predef.domain.EditActionDomainPluginProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="incrementUpdateActionDomainPluginProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.core.predef.domain.IncrementUpdateActionDomainPluginProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="invalidActionDomainPluginProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.core.predef.domain.InvalidActionDomainPluginProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="bulkInvalidActionDomainPluginProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.core.predef.domain.BulkInvalidActionDomainPluginProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="flowCompletedActionDomainPluginProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.core.predef.domain.FlowCompletedActionDomainPluginProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="importTemplateActionDomainPluginProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.core.predef.domain.ImportTemplateActionDomainPluginProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="importVerifyActionDomainPluginProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.core.predef.domain.ImportVerifyActionDomainPluginProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="importDataActionDomainPluginProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.core.predef.domain.ImportDataActionDomainPluginProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="describeLayoutControllerDomainPluginProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.core.predef.domain.DescribeLayoutControllerDomainPluginProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="listControllerDomainPluginProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.core.predef.domain.ListControllerDomainPluginProxy"
          p:factory-ref="restServiceProxyFactory"/>

    <bean id="relatedListControllerDomainPluginProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.core.predef.domain.RelatedListControllerDomainPluginProxy"
          p:factory-ref="restServiceProxyFactory"/>

    <bean id="designerCreateLayoutActionDomainPluginProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.core.predef.domain.DesignerCreateLayoutActionDomainPluginProxy"
          p:factory-ref="restServiceProxyFactory"/>

    <bean id="designerUpdateLayoutActionDomainPluginProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.core.predef.domain.DesignerUpdateLayoutActionDomainPluginProxy"
          p:factory-ref="restServiceProxyFactory"/>

    <bean id="enterAccountActionDomainPluginProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.core.predef.domain.EnterAccountActionDomainPluginProxy"
          p:factory-ref="restServiceProxyFactory"/>

    <bean id="cancelEntryActionDomainPluginProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.core.predef.domain.CancelEntryActionDomainPluginProxy"
          p:factory-ref="restServiceProxyFactory"/>

</beans>