package com.facishare.paas.appframework.core.rest;

/**
 * Created by zhouwr on 2017/9/30
 */
public interface InnerHeaders {
    String TENANT_ID = "x-fs-ei";
    String USER_ID = "x-fs-userInfo";
    String PEER_HOST = "x-fs-peer-host";
    //老的peerName
    String PEER_NAME = "x-fs-peer-name";
    //以后统一使用的peerName
    String X_PEER_NAME = "x-peer-name";
    String EVENT_ID = "x-fs-eventId";

    String OUT_TENANT_ID = "x-out-tenant-id";
    String OUT_USER_ID = "x-out-user-id";
    String APP_ID = "x-app-id";
    String OUT_LINK_TYPE = "x-out-link-type";

    String LOCALE = "x-fs-locale";
    String BATCH = "x-fs-batch";
    String POST_ID = "x-fs-request-id";
    String PEER_REASON = "x-fs-peer-reason";
    String PEER_DISPLAY_NAME = "x-fs-peer-display-name";
    String PEER_DISPLAY_NAME_I18N_KEY = "x-fs-peer-display-name-i18n-key";
    String MODEL_NAME = "x-fs-model-name";
    String CLIENT_IP = "x-fs-client-ip";
    String ENTERPRISE_ACCOUNT = "x-fs-enterprise-account";
    String BIZ_ID = "x-fs-biz-id";
    String OTHER_BIZ_ID = "x-fs-other-biz-id";
    String UPSTREAM_OWNER_ID = "x-fs-upstream-owner-id";

    String OBJECT_API_NAME = "x-fs-object-api-name";
    String USE_LOCAL = "x-fs-use-local";

    String SERIALIZE_EMPTY = "x-fs-serialize-empty";
    String SESSION_ID = "x-fs-session-id";

    String PARAMS_IDEMPOTENT = "x-fs-params-idempotent";

    String OUT_IDENTITY_TYPE = "x-out-identity-type";

    String OBJECT_MODULE = "x-fs-module";

    String TIME_ZONE = "x-fs-timezone";

    String THIRD_USER_ID = "x-fs-thirduserid";
    String THIRD_APP_ID = "x-fs-thirdappid";
    String THIRD_TYPE = "x-fs-thirdtype";
    String UPDATE_ORIGIN_SOURCE = "x-fs-update-origin-source";
    String ORIGINAL_REQUEST_SOURCE = "x-fs-original-request-source";
    String CLIENT_INFO = "x-fs-client-info";
    String REGION = "x-fs-region";
}
