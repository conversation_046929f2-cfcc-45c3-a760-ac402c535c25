package com.facishare.paas.appframework.core.predef.service.dto;

import com.facishare.paas.appframework.common.service.model.ManageGroup;
import lombok.Data;

import java.util.Objects;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/7/17
 */
@Data
public class ManageGroupDTO {
    private final boolean all;
    private final String type;
    private final String parentApiName;
    private final Set<String> apiNames;

    private ManageGroupDTO(boolean all, String type, String parentApiName, Set<String> apiNames) {
        this.all = all;
        this.type = type;
        this.parentApiName = parentApiName;
        this.apiNames = apiNames;
    }

    public static ManageGroupDTO of(ManageGroup manageGroup) {
        if (Objects.isNull(manageGroup)) {
            return null;
        }
        Set<String> apiNames;
        if (manageGroup.isAllSupport()) {
            apiNames = manageGroup.getSupportApiNames();
        } else {
            apiNames = manageGroup.getApiNames();
        }
        return new ManageGroupDTO(manageGroup.isAllSupport(), manageGroup.getType().getType(), manageGroup.getParentApiName(), apiNames);
    }
}
