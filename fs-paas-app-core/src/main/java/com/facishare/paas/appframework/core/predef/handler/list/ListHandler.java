package com.facishare.paas.appframework.core.predef.handler.list;

import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Created by zhouwr on 2024/3/14.
 */
public interface ListHandler extends BaseListHandler<ListHandler.Arg, BaseListHandler.Result> {
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class Arg extends BaseListHandler.Arg<StandardListController.Arg> {
    }
}
