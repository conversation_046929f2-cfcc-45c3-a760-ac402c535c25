package com.facishare.paas.appframework.core.predef.service.dto.brushData;

import lombok.Builder;
import lombok.Data;

import java.util.Set;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/10/13
 */
public interface UpdateFunctionPrivilege {

    @Data
    class Arg {
        private Set<String> describeApiNames;
        private String ea;
        private String actionCode;
    }

    @Data
    class MultiCodeArg {    // 每次请求按照企业维度调用, 方便跨云请求
        private Set<String> describeApiNames;
        private String ei;
        private Set<String> actionCodes;
    }

    class Result {

    }

    @Data
    @Builder
    class ResultItem {
        String ei;
        @Builder.Default
        boolean success = false;
        String failMessage;
        Set<String> objApis;
    }
}
