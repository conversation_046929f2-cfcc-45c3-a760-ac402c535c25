package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.ObjectAction;

import java.util.List;
import java.util.stream.Collectors;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/10/12
 */
public class StandardAsyncBulkChangePartnerAction extends AbstractStandardAsyncBulkAction<StandardChangePartnerAction.Arg, StandardChangePartnerAction.Arg> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.ChangePartner.getFunPrivilegeCodes();
    }

    @Override
    protected String getDataIdByParam(StandardChangePartnerAction.Arg param) {
        return param.getDataIds().get(0);
    }

    @Override
    protected List<StandardChangePartnerAction.Arg> getButtonParams() {
        return arg.getDataIds().stream()
                .map(id -> StandardChangePartnerAction.Arg.of(id, arg.getPartnerId(), arg.getUpdateOutOwner()))
                .collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.CHANGE_PARTNER.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return ObjectAction.CHANGE_PARTNER.getActionCode();
    }
}
