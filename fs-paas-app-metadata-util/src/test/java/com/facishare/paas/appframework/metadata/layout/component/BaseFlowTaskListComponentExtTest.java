package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.metadata.api.search.IOrderBy;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BaseFlowTaskListComponentExtTest {

  @Mock
  private IComponent component;

  @Mock
  private IComponent documentBasedComponent;
  
  private TestableBaseFlowTaskListComponentExt baseFlowTaskListComponentExt;

  // 创建一个可测试的具体实现类
  private static class TestableBaseFlowTaskListComponentExt extends BaseFlowTaskListComponentExt {
    public TestableBaseFlowTaskListComponentExt(IComponent component) {
      super(component);
    }
  }

  @BeforeEach
  void setUp() {
    baseFlowTaskListComponentExt = new TestableBaseFlowTaskListComponentExt(component);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试常量定义
   */
  @Test
  @DisplayName("正常场景 - 测试常量定义")
  void testConstants() {
    assertEquals("task_list", BaseFlowTaskListComponentExt.COMPONENT_TYPE_TASK_LIST, 
                 "任务列表组件类型常量应正确");
    assertEquals("task_list_mobile", BaseFlowTaskListComponentExt.COMPONENT_TYPE_TASK_LIST_MOBILE, 
                 "移动端任务列表组件类型常量应正确");
    assertEquals("filter_info", BaseFlowTaskListComponentExt.FILTER_INFO, 
                 "过滤信息常量应正确");
    assertEquals("field_list", BaseFlowTaskListComponentExt.FIELD_LIST, 
                 "字段列表常量应正确");
    assertEquals("orders", BaseFlowTaskListComponentExt.ORDERS, 
                 "排序常量应正确");
    assertEquals("task_list_component", BaseFlowTaskListComponentExt.TASK_LIST_NAME, 
                 "任务列表名称常量应正确");
    assertEquals("task_list_mobile_component", BaseFlowTaskListComponentExt.TASK_LIST_MOBILE_NAME, 
                 "移动端任务列表名称常量应正确");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getComponent方法
   */
  @Test
  @DisplayName("正常场景 - 测试getComponent方法")
  void testGetComponent() {
    IComponent retrievedComponent = baseFlowTaskListComponentExt.getComponent();
    assertSame(component, retrievedComponent, "应返回构造函数中传入的组件");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setNameI18nKey方法
   */
  @Test
  @DisplayName("正常场景 - 测试setNameI18nKey方法")
  void testSetNameI18nKey() {
    String nameI18nKey = "test.name.i18n.key";
    
    baseFlowTaskListComponentExt.setNameI18nKey(nameI18nKey);
    
    verify(component).set(eq("nameI18nKey"), eq(nameI18nKey));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试resetOrders方法 - 正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试resetOrders方法")
  void testResetOrders_NormalScenario() {
    // 创建排序对象
    IOrderBy orderBy1 = new OrderBy();
    orderBy1.setFieldName("field1");
    orderBy1.setIsAsc(true);

    IOrderBy orderBy2 = new OrderBy();
    orderBy2.setFieldName("field2");
    orderBy2.setIsAsc(false);

    List<IOrderBy> orderBys = Lists.newArrayList(orderBy1, orderBy2);

    // 执行方法
    baseFlowTaskListComponentExt.resetOrders(orderBys);

    // 验证调用
    verify(component).set(eq(BaseFlowTaskListComponentExt.ORDERS), any(List.class));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试resetOrders方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试resetOrders方法处理null值")
  void testResetOrders_NullValue() {
    // 执行方法
    baseFlowTaskListComponentExt.resetOrders(null);
    
    // 验证没有调用set方法（因为null值直接返回）
    // 这里我们无法直接验证没有调用，但可以确保不抛出异常
    assertDoesNotThrow(() -> baseFlowTaskListComponentExt.resetOrders(null), 
                       "null值不应抛出异常");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getOrders方法 - 正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试getOrders方法")
  void testGetOrders_NormalScenario() {
    // 模拟返回的Map列表
    Map<String, Object> orderMap1 = Maps.newHashMap();
    orderMap1.put("fieldApiName", "field1");
    orderMap1.put("direction", "ASC");
    
    Map<String, Object> orderMap2 = Maps.newHashMap();
    orderMap2.put("fieldApiName", "field2");
    orderMap2.put("direction", "DESC");
    
    List<Map> orderMaps = Lists.newArrayList(orderMap1, orderMap2);
    when(component.get(BaseFlowTaskListComponentExt.ORDERS)).thenReturn(orderMaps);
    
    // 执行方法
    List<IOrderBy> orders = baseFlowTaskListComponentExt.getOrders();
    
    // 验证结果
    assertNotNull(orders, "排序列表不应为空");
    assertEquals(2, orders.size(), "应有2个排序对象");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getOrders方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试getOrders方法处理null值")
  void testGetOrders_NullValue() {
    when(component.get(BaseFlowTaskListComponentExt.ORDERS)).thenReturn(null);
    
    List<IOrderBy> orders = baseFlowTaskListComponentExt.getOrders();
    
    assertNotNull(orders, "null值时应返回空列表");
    assertTrue(orders.isEmpty(), "null值时应返回空列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试resetFilterInfo方法
   */
  @Test
  @DisplayName("正常场景 - 测试resetFilterInfo方法")
  void testResetFilterInfo() {
    // 创建过滤信息
    BaseFlowTaskListComponentExt.FilterInfo filterInfo = new BaseFlowTaskListComponentExt.FilterInfo();
    filterInfo.setFields(Lists.newArrayList("field1", "field2"));
    
    // 执行方法
    baseFlowTaskListComponentExt.resetFilterInfo(filterInfo);
    
    // 验证调用
    verify(component).set(eq(BaseFlowTaskListComponentExt.FILTER_INFO), any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFilterInfo方法 - 正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试getFilterInfo方法")
  void testGetFilterInfo_NormalScenario() {
    // 模拟返回的Map
    Map<String, Object> filterMap = Maps.newHashMap();
    filterMap.put("fields", Lists.newArrayList("field1", "field2"));
    when(component.get(BaseFlowTaskListComponentExt.FILTER_INFO)).thenReturn(filterMap);
    
    // 执行方法
    BaseFlowTaskListComponentExt.FilterInfo filterInfo = baseFlowTaskListComponentExt.getFilterInfo();
    
    // 验证结果
    assertNotNull(filterInfo, "过滤信息不应为空");
    assertNotNull(filterInfo.getFields(), "字段列表不应为空");
    assertEquals(2, filterInfo.getFields().size(), "应有2个字段");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试toMap方法 - DocumentBasedBean场景
   */
  @Test
  @DisplayName("正常场景 - 测试toMap方法DocumentBasedBean场景")
  void testToMap_DocumentBasedBean() {
    // 创建一个实现了DocumentBasedBean的组件
    TestableBaseFlowTaskListComponentExt extWithDocumentBean =
        new TestableBaseFlowTaskListComponentExt(documentBasedComponent);

    Map<String, Object> expectedMap = Maps.newHashMap();
    expectedMap.put("test", "value");

    // 模拟DocumentBasedBean行为
    when(documentBasedComponent.toJsonString()).thenReturn("{\"test\":\"value\"}");

    Map<String, Object> result = extWithDocumentBean.toMap();

    assertNotNull(result, "结果不应为空");
    assertEquals("value", result.get("test"), "应正确解析JSON字符串");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试toMap方法 - 普通组件场景
   */
  @Test
  @DisplayName("正常场景 - 测试toMap方法普通组件场景")
  void testToMap_RegularComponent() {
    String jsonString = "{\"name\":\"test\",\"type\":\"task_list\"}";
    when(component.toJsonString()).thenReturn(jsonString);
    
    Map<String, Object> result = baseFlowTaskListComponentExt.toMap();
    
    assertNotNull(result, "结果不应为空");
    assertEquals("test", result.get("name"), "应正确解析JSON字符串");
    assertEquals("task_list", result.get("type"), "应正确解析JSON字符串");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试FilterInfo内部类 - 基本功能
   */
  @Test
  @DisplayName("正常场景 - 测试FilterInfo基本功能")
  void testFilterInfo_BasicFunctionality() {
    BaseFlowTaskListComponentExt.FilterInfo filterInfo = new BaseFlowTaskListComponentExt.FilterInfo();
    
    // 测试设置和获取字段
    List<String> fields = Lists.newArrayList("field1", "field2", "field3");
    filterInfo.setFields(fields);
    
    assertEquals(fields, filterInfo.getFields(), "应正确设置和获取字段列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试FilterInfo的empty方法
   */
  @Test
  @DisplayName("正常场景 - 测试FilterInfo的empty方法")
  void testFilterInfo_Empty() {
    BaseFlowTaskListComponentExt.FilterInfo filterInfo = new BaseFlowTaskListComponentExt.FilterInfo();
    
    // 测试null字段
    assertTrue(filterInfo.empty(), "null字段应返回true");
    
    // 测试空列表
    filterInfo.setFields(Lists.newArrayList());
    assertTrue(filterInfo.empty(), "空列表应返回true");
    
    // 测试非空列表
    filterInfo.setFields(Lists.newArrayList("field1"));
    assertFalse(filterInfo.empty(), "非空列表应返回false");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试FilterInfo的JSON序列化和反序列化
   */
  @Test
  @DisplayName("正常场景 - 测试FilterInfo的JSON序列化")
  void testFilterInfo_JsonSerialization() {
    BaseFlowTaskListComponentExt.FilterInfo filterInfo = new BaseFlowTaskListComponentExt.FilterInfo();
    filterInfo.setFields(Lists.newArrayList("field1", "field2"));
    
    // 测试序列化
    String jsonString = filterInfo.toJsonString();
    assertNotNull(jsonString, "JSON字符串不应为空");
    assertTrue(jsonString.contains("field1"), "JSON应包含field1");
    assertTrue(jsonString.contains("field2"), "JSON应包含field2");
    
    // 测试反序列化
    BaseFlowTaskListComponentExt.FilterInfo deserializedInfo = 
        BaseFlowTaskListComponentExt.FilterInfo.fromJsonString(jsonString);
    assertNotNull(deserializedInfo, "反序列化对象不应为空");
    assertEquals(2, deserializedInfo.getFields().size(), "应有2个字段");
    assertTrue(deserializedInfo.getFields().contains("field1"), "应包含field1");
    assertTrue(deserializedInfo.getFields().contains("field2"), "应包含field2");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试空的排序列表场景
   */
  @Test
  @DisplayName("边界场景 - 测试空排序列表")
  void testResetOrders_EmptyList() {
    List<IOrderBy> emptyOrders = Lists.newArrayList();
    
    baseFlowTaskListComponentExt.resetOrders(emptyOrders);
    
    verify(component).set(eq(BaseFlowTaskListComponentExt.ORDERS), any(List.class));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试复杂的过滤信息场景
   */
  @Test
  @DisplayName("正常场景 - 测试复杂过滤信息")
  void testComplexFilterInfo() {
    BaseFlowTaskListComponentExt.FilterInfo filterInfo = new BaseFlowTaskListComponentExt.FilterInfo();
    List<String> complexFields = Lists.newArrayList(
        "field_with_underscore", "fieldWithCamelCase", "field123", "field_with_special_chars"
    );
    filterInfo.setFields(complexFields);
    
    // 测试序列化和反序列化
    String jsonString = filterInfo.toJsonString();
    BaseFlowTaskListComponentExt.FilterInfo deserializedInfo = 
        BaseFlowTaskListComponentExt.FilterInfo.fromJsonString(jsonString);
    
    assertEquals(4, deserializedInfo.getFields().size(), "应有4个复杂字段");
    assertTrue(deserializedInfo.getFields().containsAll(complexFields), "应包含所有复杂字段");
    assertFalse(deserializedInfo.empty(), "复杂字段列表不应为空");
  }
}
