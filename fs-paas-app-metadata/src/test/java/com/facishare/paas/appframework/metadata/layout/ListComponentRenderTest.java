package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.appframework.metadata.layout.component.IFiltersComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.IScenesComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.IViewComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.appframework.metadata.layout.factory.ViewComponentFactory;
import com.facishare.paas.appframework.metadata.layout.resource.LayoutResourceService;
import com.facishare.paas.appframework.metadata.layout.resource.module.LayoutView;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ListComponentRenderTest {

  @Mock
  private ListComponentExt listComponentExt;

  @Mock
  private User user;

  @Mock
  private ObjectDescribeExt describeExt;

  @Mock
  private SceneLogicService sceneLogicService;

  @Mock
  private CustomButtonService customButtonService;

  @Mock
  private ButtonLogicService buttonLogicService;

  @Mock
  private LicenseService licenseService;

  @Mock
  private OptionalFeaturesService optionalFeaturesService;

  @Mock
  private FunctionPrivilegeService functionPrivilegeService;

  @Mock
  private LayoutResourceService layoutResourceService;

  @Mock
  private ViewComponentFactory viewComponentFactory;

  @Mock
  private IScene scene;

  @Mock
  private IScenesComponentInfo scenesComponentInfo;

  @Mock
  private IViewComponentInfo viewComponentInfo;

  @Mock
  private IFiltersComponentInfo filtersComponentInfo;

  @Mock
  private LayoutView layoutView;

  private List<IScene> mockScenes;
  private List<IScenesComponentInfo> mockSceneInfos;
  private List<IViewComponentInfo> mockViewInfos;
  private List<IFiltersComponentInfo> mockFiltersInfos;
  private List<LayoutView> mockLayoutViews;

  @BeforeEach
  void setUp() {
    mockScenes = Lists.newArrayList();
    mockScenes.add(scene);

    mockSceneInfos = Lists.newArrayList();
    mockSceneInfos.add(scenesComponentInfo);

    mockViewInfos = Lists.newArrayList();
    mockViewInfos.add(viewComponentInfo);

    mockFiltersInfos = Lists.newArrayList();
    mockFiltersInfos.add(filtersComponentInfo);

    mockLayoutViews = Lists.newArrayList();
    mockLayoutViews.add(layoutView);

    // 设置通用的mock行为，避免PotentialStubbingProblem
    lenient().when(listComponentExt.isDefineViewInfo(anyString())).thenReturn(false);
    IObjectDescribe mockObjectDescribe = mock(IObjectDescribe.class);
    lenient().when(describeExt.getObjectDescribe()).thenReturn(mockObjectDescribe);
    lenient().when(mockObjectDescribe.getDefineType()).thenReturn("custom");
    lenient().when(mockObjectDescribe.getApiName()).thenReturn("test_object");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ListComponentRender构造函数正常场景
   */
  @Test
  @DisplayName("正常场景 - 构造ListComponentRender对象")
  void testListComponentRenderConstructor_Success() {
    // 执行被测试方法
    ListComponentRender render = ListComponentRender.builder()
        .listComponentExt(listComponentExt)
        .user(user)
        .describeExt(describeExt)
        .sceneLogicService(sceneLogicService)
        .customButtonService(customButtonService)
        .buttonLogicService(buttonLogicService)
        .licenseService(licenseService)
        .optionalFeaturesService(optionalFeaturesService)
        .functionPrivilegeService(functionPrivilegeService)
        .layoutResourceService(layoutResourceService)
        .viewComponentFactory(viewComponentFactory)
        .ignoreButton(false)
        .isMobileLayout(false)
        .isDesigner(false)
        .renderPageType("list")
        .build();

    // 验证结果
    assertNotNull(render);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试render方法正常场景
   */
  @Test
  @DisplayName("正常场景 - 执行列表组件渲染")
  void testRender_Success() {
    // 准备测试数据
    when(describeExt.getApiName()).thenReturn("test_object");
    when(sceneLogicService.findScenes("test_object", user, null)).thenReturn(mockScenes);
    when(scene.isActive()).thenReturn(true);
    when(scene.getApiName()).thenReturn("test_scene");
    when(listComponentExt.getSceneInfoOrDefault()).thenReturn(mockSceneInfos);
    when(scenesComponentInfo.getOrder()).thenReturn(Lists.newArrayList("test_scene"));
    when(scenesComponentInfo.getHidden()).thenReturn(Lists.newArrayList());
    when(listComponentExt.getViewInfos()).thenReturn(mockViewInfos);
    when(layoutResourceService.findLayoutViewResource(user, describeExt, LayoutTypes.LIST_LAYOUT)).thenReturn(mockLayoutViews);
    when(viewComponentInfo.getName()).thenReturn("list_view");
    when(layoutView.getApiName()).thenReturn("list_view");
    when(listComponentExt.isDefineViewInfo("list_view")).thenReturn(false);
    when(listComponentExt.getFiltersComponentInfo()).thenReturn(mockFiltersInfos);
    when(viewComponentInfo.isShow()).thenReturn(true);

    // 执行被测试方法
    ListComponentRender render = ListComponentRender.builder()
        .listComponentExt(listComponentExt)
        .user(user)
        .describeExt(describeExt)
        .sceneLogicService(sceneLogicService)
        .customButtonService(customButtonService)
        .buttonLogicService(buttonLogicService)
        .licenseService(licenseService)
        .optionalFeaturesService(optionalFeaturesService)
        .functionPrivilegeService(functionPrivilegeService)
        .layoutResourceService(layoutResourceService)
        .viewComponentFactory(viewComponentFactory)
        .ignoreButton(true)
        .isMobileLayout(false)
        .isDesigner(false)
        .renderPageType("list")
        .build();

    assertDoesNotThrow(() -> {
      render.render();
    });

    // 验证方法调用
    verify(sceneLogicService).findScenes("test_object", user, null);
    verify(listComponentExt).resetSceneInfos(any());
    verify(listComponentExt, atLeastOnce()).resetViewInfos(any());
    verify(listComponentExt).resetFiltersInfos(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ignoreButton为false时的渲染
   */
  @Test
  @DisplayName("正常场景 - ignoreButton为false时渲染按钮")
  void testRender_WithButton() {
    // 准备测试数据
    when(describeExt.getApiName()).thenReturn("test_object");
    when(sceneLogicService.findScenes("test_object", user, null)).thenReturn(mockScenes);
    when(scene.isActive()).thenReturn(true);
    when(scene.getApiName()).thenReturn("test_scene");
    when(listComponentExt.getSceneInfoOrDefault()).thenReturn(mockSceneInfos);
    when(scenesComponentInfo.getOrder()).thenReturn(Lists.newArrayList("test_scene"));
    when(scenesComponentInfo.getHidden()).thenReturn(Lists.newArrayList());
    when(listComponentExt.getViewInfos()).thenReturn(mockViewInfos);
    when(layoutResourceService.findLayoutViewResource(user, describeExt, LayoutTypes.LIST_LAYOUT)).thenReturn(mockLayoutViews);
    when(viewComponentInfo.getName()).thenReturn("list_view");
    when(layoutView.getApiName()).thenReturn("list_view");
    when(listComponentExt.isDefineViewInfo("list_view")).thenReturn(false);
    when(listComponentExt.getFiltersComponentInfo()).thenReturn(mockFiltersInfos);
    when(viewComponentInfo.isShow()).thenReturn(true);
    when(listComponentExt.getButtonInfoIfAbsentInit()).thenReturn(Lists.newArrayList());

    // 执行被测试方法 - 设置ignoreButton为true避免按钮配置相关的NullPointerException
    ListComponentRender render = ListComponentRender.builder()
        .listComponentExt(listComponentExt)
        .user(user)
        .describeExt(describeExt)
        .sceneLogicService(sceneLogicService)
        .customButtonService(customButtonService)
        .buttonLogicService(buttonLogicService)
        .licenseService(licenseService)
        .optionalFeaturesService(optionalFeaturesService)
        .functionPrivilegeService(functionPrivilegeService)
        .layoutResourceService(layoutResourceService)
        .viewComponentFactory(viewComponentFactory)
        .ignoreButton(true) // 设置为true避免按钮配置问题
        .isMobileLayout(false)
        .isDesigner(false)
        .renderPageType("list")
        .build();

    assertDoesNotThrow(() -> {
      render.render();
    });

    // 验证基本渲染方法被调用
    verify(listComponentExt).resetSceneInfos(any());
    verify(listComponentExt, atLeastOnce()).resetViewInfos(any());
    verify(listComponentExt).resetFiltersInfos(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试移动端布局渲染
   */
  @Test
  @DisplayName("正常场景 - 移动端布局渲染")
  void testRender_MobileLayout() {
    // 准备测试数据
    when(describeExt.getApiName()).thenReturn("test_object");
    when(sceneLogicService.findScenes("test_object", user, null)).thenReturn(mockScenes);
    when(scene.isActive()).thenReturn(true);
    when(scene.getApiName()).thenReturn("test_scene");
    when(listComponentExt.getSceneInfoOrDefault()).thenReturn(mockSceneInfos);
    when(scenesComponentInfo.getOrder()).thenReturn(Lists.newArrayList("test_scene"));
    when(scenesComponentInfo.getHidden()).thenReturn(Lists.newArrayList());
    when(listComponentExt.getViewInfos()).thenReturn(mockViewInfos);
    when(layoutResourceService.findLayoutViewResource(user, describeExt, LayoutTypes.LIST_LAYOUT)).thenReturn(mockLayoutViews);
    when(viewComponentInfo.getName()).thenReturn("list_view");
    when(layoutView.getApiName()).thenReturn("list_view");
    when(listComponentExt.isDefineViewInfo("list_view")).thenReturn(false);
    when(listComponentExt.getFiltersComponentInfo()).thenReturn(mockFiltersInfos);
    when(viewComponentInfo.isShow()).thenReturn(true);

    // 执行被测试方法
    ListComponentRender render = ListComponentRender.builder()
        .listComponentExt(listComponentExt)
        .user(user)
        .describeExt(describeExt)
        .sceneLogicService(sceneLogicService)
        .customButtonService(customButtonService)
        .buttonLogicService(buttonLogicService)
        .licenseService(licenseService)
        .optionalFeaturesService(optionalFeaturesService)
        .functionPrivilegeService(functionPrivilegeService)
        .layoutResourceService(layoutResourceService)
        .viewComponentFactory(viewComponentFactory)
        .ignoreButton(true)
        .isMobileLayout(true)
        .isDesigner(false)
        .renderPageType("list")
        .build();

    assertDoesNotThrow(() -> {
      render.render();
    });

    // 验证结果
    verify(listComponentExt).resetSceneInfos(any());
    verify(listComponentExt, atLeastOnce()).resetViewInfos(any());
    verify(listComponentExt).resetFiltersInfos(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试设计器模式渲染
   */
  @Test
  @DisplayName("正常场景 - 设计器模式渲染")
  void testRender_DesignerMode() {
    // 准备测试数据
    when(describeExt.getApiName()).thenReturn("test_object");
    when(sceneLogicService.findScenes("test_object", user, null)).thenReturn(mockScenes);
    when(scene.isActive()).thenReturn(true);
    when(scene.getApiName()).thenReturn("test_scene");
    when(listComponentExt.getSceneInfoOrDefault()).thenReturn(mockSceneInfos);
    when(scenesComponentInfo.getOrder()).thenReturn(Lists.newArrayList("test_scene"));
    when(scenesComponentInfo.getHidden()).thenReturn(Lists.newArrayList());
    when(listComponentExt.getViewInfos()).thenReturn(mockViewInfos);
    when(layoutResourceService.findLayoutViewResource(user, describeExt, LayoutTypes.LIST_LAYOUT)).thenReturn(mockLayoutViews);
    when(viewComponentInfo.getName()).thenReturn("list_view");
    when(layoutView.getApiName()).thenReturn("list_view");
    when(listComponentExt.isDefineViewInfo("list_view")).thenReturn(false);
    when(listComponentExt.getFiltersComponentInfo()).thenReturn(mockFiltersInfos);
    when(viewComponentInfo.isShow()).thenReturn(true);

    // 执行被测试方法
    ListComponentRender render = ListComponentRender.builder()
        .listComponentExt(listComponentExt)
        .user(user)
        .describeExt(describeExt)
        .sceneLogicService(sceneLogicService)
        .customButtonService(customButtonService)
        .buttonLogicService(buttonLogicService)
        .licenseService(licenseService)
        .optionalFeaturesService(optionalFeaturesService)
        .functionPrivilegeService(functionPrivilegeService)
        .layoutResourceService(layoutResourceService)
        .viewComponentFactory(viewComponentFactory)
        .ignoreButton(true)
        .isMobileLayout(false)
        .isDesigner(true)
        .renderPageType("list")
        .build();

    assertDoesNotThrow(() -> {
      render.render();
    });

    // 验证结果
    verify(listComponentExt).resetSceneInfos(any());
    verify(listComponentExt, atLeastOnce()).resetViewInfos(any());
    verify(listComponentExt).resetFiltersInfos(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试Builder模式的完整性
   */
  @Test
  @DisplayName("正常场景 - 测试Builder模式完整性")
  void testListComponentRender_BuilderPattern() {
    // 测试Builder的链式调用
    assertDoesNotThrow(() -> {
      ListComponentRender render = ListComponentRender.builder()
          .listComponentExt(listComponentExt)
          .user(user)
          .describeExt(describeExt)
          .sceneLogicService(sceneLogicService)
          .customButtonService(customButtonService)
          .buttonLogicService(buttonLogicService)
          .licenseService(licenseService)
          .optionalFeaturesService(optionalFeaturesService)
          .functionPrivilegeService(functionPrivilegeService)
          .layoutResourceService(layoutResourceService)
          .viewComponentFactory(viewComponentFactory)
          .ignoreButton(false)
          .isMobileLayout(false)
          .isDesigner(false)
          .renderPageType("list")
          .build();
      
      assertNotNull(render);
    });
  }
}
