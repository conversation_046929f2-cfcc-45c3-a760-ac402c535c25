package com.facishare.paas.appframework.core.predef.service.dto.search;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fxiaoke.functions.utils.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface ObjectSearchData {
    @Data
    class Arg {
        String keyword;
        String priorityObject;
        String scope;
        Integer size;
        String objectApiName;
        ObjectDataDocument objectData;
        boolean accurateQuery = false;
        Boolean fixedSort = Boolean.FALSE;
        List<String> searchObjList = Lists.newArrayList();

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        Integer totalCount;
        List<SearchResult> list;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class SearchResult {
        String apiName;
        String displayName;
        List<String> idList;
    }
}
