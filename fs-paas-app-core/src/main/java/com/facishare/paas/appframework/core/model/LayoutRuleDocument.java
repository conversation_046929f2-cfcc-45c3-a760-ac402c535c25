package com.facishare.paas.appframework.core.model;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.metadata.impl.LayoutRuleInfo;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class LayoutRuleDocument extends DocumentBaseEntity {

    public LayoutRuleDocument() {
    }

    public LayoutRuleDocument(Map<String, Object> map) {
        super(map);
        Object objId = data.get(LayoutRuleInfo.ID);
        if (Objects.nonNull(objId)) {
            data.put(LayoutRuleInfo.ID, objId);
        }
    }

    public static LayoutRuleDocument of(Map<String, Object> data) {
        return new LayoutRuleDocument(data);
    }

    public static LayoutRuleDocument of(LayoutRuleInfo data) {
        return new LayoutRuleDocument(data.getContainerDocument());
    }

    public static List<LayoutRuleDocument> mockData() {
        String json = "{\n" +
                "    \"api_name\": \"layout_rule_xfl23__c\",\n" +
                "    \"label\": \"测试\",\n" +
                "    \"description\": \"\",\n" +
                "    \"layout_api_name\": \"layout_69z5h__c\",\n" +
                "    \"describe_api_name\":\"object_2dH3t__c\",\n" +
                "    \"status\": 0,\n" +
                "    \"main_field\": \"field_sSZ05__c\",\n" +
                "    \"main_field_branches\": [\n" +
                "        {\n" +
                "            \"main_field_filter\": {\n" +
                "                \"value_type\": 0,\n" +
                "                \"operator\": \"EQ\",\n" +
                "                \"field_name\": \"field_sSZ05__c\",\n" +
                "                \"field_values\": [\n" +
                "                    \"option1\"\n" +
                "                ]\n" +
                "            },\n" +
                "            \"branches\": [\n" +
                "                {\n" +
                "                    \"conditions\": [\n" +
                "                        {\n" +
                "                            \"value_type\": 0,\n" +
                "                            \"operator\": \"EQ\",\n" +
                "                            \"field_name\": \"field_e41b5__c\",\n" +
                "                            \"field_values\": [\n" +
                "                                \"11\"\n" +
                "                            ]\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"value_type\": 0,\n" +
                "                            \"operator\": \"EQ\",\n" +
                "                            \"field_name\": \"field_mPdkI__c\",\n" +
                "                            \"field_values\": [\n" +
                "                                true\n" +
                "                            ]\n" +
                "                        }\n" +
                "                    ],\n" +
                "                    \"result\": {\n" +
                "                        \"show_field\": [\n" +
                "                            {\n" +
                "                                \"field_api_name\": \"field_4Pcjg__c\"\n" +
                "                            }\n" +
                "                        ],\n" +
                "                        \"required_field\": [\n" +
                "                            {\n" +
                "                                \"field_api_name\": \"field_4Pcjg__c\"\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    }\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        Map map = JSON.parseObject(json, Map.class);
        return Lists.newArrayList(LayoutRuleDocument.of(map));
    }

    public static List<LayoutRuleDocument> ofList(List<LayoutRuleInfo> layoutRuleList) {
        if (CollectionUtils.isEmpty(layoutRuleList)) {
            return Lists.newArrayList();
        }
        return layoutRuleList.stream().map(layout -> LayoutRuleDocument.of(layout)).collect(Collectors.toList());
    }
}