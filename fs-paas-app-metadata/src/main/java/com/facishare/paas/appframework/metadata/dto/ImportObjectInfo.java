package com.facishare.paas.appframework.metadata.dto;

import com.facishare.paas.appframework.core.rest.BaseAPIResult;
import com.facishare.paas.appframework.metadata.importobject.ImportObject;
import lombok.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/3/2
 */
public interface ImportObjectInfo {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        private String objectCode;
        private String objectApiName;
    }

    @Data
    @NoArgsConstructor
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    @AllArgsConstructor(staticName = "of")
    class RestResult extends BaseAPIResult {
        private Result data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor(staticName = "of")
    class Result {
        private ImportObject importObject;
    }
}
