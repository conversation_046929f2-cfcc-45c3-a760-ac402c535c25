package com.facishare.paas.appframework.core.predef.action

import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.util.ImportExportExt
import com.facishare.paas.appframework.metadata.ObjectDescribeExt
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IFieldType
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.describe.ObjectReferenceManyFieldDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe
import com.fxiaoke.api.IdGenerator
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.powermock.reflect.Whitebox
import spock.lang.Specification

import java.lang.reflect.Field

class StandardUnionInsertImportDataActionTest extends Specification {

    def objectApiName = "object_123__c"
    def actionCode = "UnionInsertImportData"
    def tenantId = "74255"
    def userId = "1000"
    def user = User.builder().tenantId(tenantId).userId(userId).build()
    def requestContext = RequestContext.builder().tenantId(tenantId).user(user).build()
    def actionContext = new ActionContext(requestContext, objectApiName, actionCode)

    ServiceFacade serviceFacade
    InfraServiceFacade infraServiceFacade
    IObjectDescribe objectDescribe
    SpringBeanHolder springBeanHolder

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def setup() {
        serviceFacade = Mock()
        infraServiceFacade = Mock()
        objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        objectDescribe.setIsActive(true)
        springBeanHolder = Mock(SpringBeanHolder)
        infraServiceFacade.getSpringBeanHolder() >> springBeanHolder
    }

    def "test convertFields"() {
        given:
        List<String> unionApiNameList = [objectApiName]
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(objectApiName)
        List<BaseImportAction.DetailInfo> detailInfoList = []
        String jobId = "65f2da41f30b060007f9577b"
        BaseImportAction.MasterInfo masterInfo = new BaseImportAction.MasterInfo()
        masterInfo.setApiName(objectApiName)
        List<ObjectDataDocument> rows = []
        List<BaseImportDataAction.ImportData> dataList = []
        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                unionApiNameList: unionApiNameList,
                jobId: jobId,
                importPreProcessing: false,
                detailInfo: detailInfoList,
                masterInfo: masterInfo,
                supportFieldMapping: supportFieldMapping,
                matchingType: matchingType,
                rows: rows
        )

        StandardUnionInsertImportDataAction action = new StandardUnionInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                unionApiNameList: unionApiNameList,
                dataList: dataList,
                arg: arg
        )
        when:
        action.convertFields(dataList)
        then:
        noExceptionThrown()
        where:
        supportFieldMapping | matchingType | _
        true                | 1            | _
        true                | 2            | _
        false               | 1            | _
        false               | 2            | _
    }

    def "test getAssociateObjectId"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(objectApiName)
        Map<String, List<BaseImportAction.FieldMapping>> fieldMappingMap = Maps.newHashMap()
        List<BaseImportAction.FieldMapping> fieldMappings = Lists.newArrayList()
        BaseImportAction.FieldMapping fieldMapping = new BaseImportAction.FieldMapping()
        fieldMapping.setApiName("md_field__c")
        fieldMapping.setColIndex("1")
        fieldMapping.setImportFieldMark(ImportExportExt.UNION_IMPORT_ID_MARK)
        fieldMappings.add(fieldMapping)
        fieldMappingMap.put(objectApiName, fieldMappings)
        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                supportFieldMapping: supportFieldMapping,
        )

        StandardUnionInsertImportDataAction action = new StandardUnionInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                arg: arg,
                fieldMappings: fieldMappingMap
        )
        when:
        action.getAssociateObjectId()
        then:
        noExceptionThrown()
        where:
        supportFieldMapping | _
        true                | _
        false               | _
    }


    def "test addFieldDescribeAndFillValue"() {
        given:
        BaseImportDataAction.ImportData importData = new BaseImportDataAction.ImportData()
        importData.setRowNo(1)
        importData.setData(new ObjectData())
        List<BaseImportDataAction.ImportData> dataList = [importData]
        Map<Integer, String> rowNoMasterIdMap = [1: "65f2da41f30b060007f9577b"]
        IFieldDescribe fieldDescribe = new MasterDetailFieldDescribe()
        fieldDescribe.setApiName("md_field__c")


        StandardUnionInsertImportDataAction action = new StandardUnionInsertImportDataAction(
                dataList: dataList
        )
        when:
        action.addFieldDescribeAndFillValue(dataList, rowNoMasterIdMap, fieldDescribe)
        then:
        noExceptionThrown()
    }

    def "test addMarkLabelField"() {
        given:

        IObjectData data = new ObjectData()
        Map.Entry<String, Object> dataMap = new MapEntry("1", "主从标识")

        Map<String, List<BaseImportAction.FieldMapping>> fieldMappingMap = Maps.newHashMap()
        List<BaseImportAction.FieldMapping> fieldMappings = Lists.newArrayList()
        BaseImportAction.FieldMapping fieldMapping = new BaseImportAction.FieldMapping()
        fieldMapping.setApiName("md_field__c")
        fieldMapping.setColIndex("1")
        fieldMapping.setImportFieldMark(ImportExportExt.EXT_INFO_RELATED_MARK)
        fieldMappings.add(fieldMapping)
        fieldMappingMap.put(objectApiName, fieldMappings)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                supportFieldMapping: supportFieldMapping,
        )

        StandardUnionInsertImportDataAction action = new StandardUnionInsertImportDataAction(
                arg: arg,
                fieldMappings: fieldMappingMap,
                objectDescribe: objectDescribe
        )
        when:
        action.addMarkLabelField(data, dataMap)
        then:
        noExceptionThrown()
        where:
        supportFieldMapping | _
        true                | _
        false               | _
    }


    def "test validateMasterDataMarkerUnique"() {
        given:

        ObjectDataDocument dataDocument = new ObjectDataDocument()
        dataDocument.put("rowNo", 1)
        dataDocument.put("1", "主从标识")

        ObjectDataDocument dataDocument1 = new ObjectDataDocument()
        dataDocument1.put("rowNo", 2)
        dataDocument1.put("主从标识", "标识1")

        Map<String, List<BaseImportAction.FieldMapping>> fieldMappingMap = Maps.newHashMap()
        List<BaseImportAction.FieldMapping> fieldMappings = Lists.newArrayList()
        BaseImportAction.FieldMapping fieldMapping = new BaseImportAction.FieldMapping()
        fieldMapping.setApiName("md_field__c")
        fieldMapping.setColIndex("1")
        fieldMapping.setImportFieldMark(ImportExportExt.EXT_INFO_RELATED_MARK)
        fieldMappings.add(fieldMapping)
        fieldMappingMap.put(objectApiName, fieldMappings)

        List<String> unionApiNameList = [objectApiName]

        List<BaseImportAction.DetailInfo> detailInfoList = []
        BaseImportAction.DetailInfo detailInfo = new BaseImportAction.DetailInfo()
        detailInfoList.add(detailInfo)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                supportFieldMapping: supportFieldMapping,
                rows: [dataDocument, dataDocument1],
                detailInfo: detailInfoList,
                unionApiNameList: unionApiNameList
        )

        StandardUnionInsertImportDataAction action = new StandardUnionInsertImportDataAction(
                arg: arg,
                fieldMappings: fieldMappingMap,
                objectDescribe: objectDescribe
        )
        when:
        action.validateMasterDataMarkerUnique()
        then:
        noExceptionThrown()
        where:
        supportFieldMapping | _
        true                | _
        false               | _
    }

    def "test doAct"() {
        given:


        ObjectDataDocument dataDocument = new ObjectDataDocument()
        dataDocument.put("rowNo", 1)
        dataDocument.put("1", "主从标识")

        ObjectDataDocument dataDocument1 = new ObjectDataDocument()
        dataDocument1.put("rowNo", 2)
        dataDocument1.put("主从标识", "标识1")

        List<BaseImportDataAction.ImportData> dataList = Lists.newArrayList()
        BaseImportDataAction.ImportData importData = new BaseImportDataAction.ImportData()
        importData.setRowNo(1)
        IObjectData data = new ObjectData()
        data.setId(IdGenerator.get())
        data.set("mark_label_virtual_api", "主从标识")
        importData.setData(data)
        dataList.add(importData)
        List<IObjectData> actualList = Lists.newArrayList()
        actualList.add(data)

        Map<String, List<BaseImportAction.FieldMapping>> fieldMappingMap = Maps.newHashMap()
        List<BaseImportAction.FieldMapping> fieldMappings = Lists.newArrayList()
        BaseImportAction.FieldMapping fieldMapping = new BaseImportAction.FieldMapping()
        fieldMapping.setApiName("md_field__c")
        fieldMapping.setColIndex("1")
        fieldMapping.setImportFieldMark(ImportExportExt.EXT_INFO_RELATED_MARK)
        fieldMappings.add(fieldMapping)
        fieldMappingMap.put(objectApiName, fieldMappings)

        List<String> unionApiNameList = [objectApiName]

        List<BaseImportAction.DetailInfo> detailInfoList = []
        BaseImportAction.DetailInfo detailInfo = new BaseImportAction.DetailInfo()
        detailInfoList.add(detailInfo)

        List<BaseImportAction.ImportError> allErrorList = Lists.newArrayList();
        BaseImportAction.ImportError error = new BaseImportAction.ImportError()
        error.setRowNo(2)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                supportFieldMapping: supportFieldMapping,
                importPreProcessing: importPreProcessing,
                rows: [dataDocument, dataDocument1],
                detailInfo: detailInfoList,
                unionApiNameList: unionApiNameList
        )

        StandardUnionInsertImportDataAction action = new StandardUnionInsertImportDataAction(
                arg: arg,
                fieldMappings: fieldMappingMap,
                objectDescribe: objectDescribe,
                actionContext: actionContext,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                serviceFacade: serviceFacade,
                actualList: actualList,
                allErrorList: allErrorList
        )
        when:
        action.doAct(arg)
        then:
        noExceptionThrown()
        where:
        importPreProcessing | supportFieldMapping
        false               | true
        false               | false
        true                | false
        true                | true
    }


    def "test fillCustomInfo2FunctionData"() {
        given:

        BaseImportDataAction.ImportData importData = new BaseImportDataAction.ImportData()
        IObjectData objectData = new ObjectData()
        objectData.set("mark_label_virtual_api", "1")
        importData.setRowNo(1)
        importData.setData(objectData)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                jobId: IdGenerator.get()
        )

        StandardUnionInsertImportDataAction action = new StandardUnionInsertImportDataAction(
                arg: arg,
                objectDescribe: objectDescribe
        )
        when:
        action.fillCustomInfo2FunctionData(importData)
        then:
        noExceptionThrown()
    }

    def "test validateOwner"() {
        given:

        List<BaseImportDataAction.ImportData> dataList = Lists.newArrayList()
        BaseImportDataAction.ImportData importData = new BaseImportDataAction.ImportData()
        importData.setRowNo(1)
        IObjectData data = new ObjectData()
        data.setId(IdGenerator.get())
        importData.setData(data)
        dataList.add(importData)

        Map<String, List<BaseImportAction.FieldMapping>> fieldMappingMap = Maps.newHashMap()
        List<BaseImportAction.FieldMapping> fieldMappings = Lists.newArrayList()
        BaseImportAction.FieldMapping fieldMapping = new BaseImportAction.FieldMapping()
        fieldMapping.setApiName("md_field__c")
        fieldMapping.setColIndex("1")
        fieldMapping.setImportFieldMark(ImportExportExt.EXT_INFO_RELATED_MARK)
        fieldMappings.add(fieldMapping)
        fieldMappingMap.put(objectApiName, fieldMappings)

        List<String> unionApiNameList = [objectApiName]

        List<BaseImportAction.DetailInfo> detailInfoList = []
        BaseImportAction.DetailInfo detailInfo = new BaseImportAction.DetailInfo()
        detailInfoList.add(detailInfo)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                supportFieldMapping: supportFieldMapping,
                importPreProcessing: importPreProcessing,
                detailInfo: detailInfoList,
                unionApiNameList: unionApiNameList
        )

        StandardUnionInsertImportDataAction action = new StandardUnionInsertImportDataAction(
                arg: arg,
                fieldMappings: fieldMappingMap,
                objectDescribe: objectDescribe
        )
        when:
        action.validateOwner(dataList)
        then:
        noExceptionThrown()
        where:
        importPreProcessing | supportFieldMapping
        false               | true
        false               | false
        true                | false
        true                | true
    }


    def "test generateResult"() {
        given:

        ObjectDataDocument dataDocument = new ObjectDataDocument()
        dataDocument.put("rowNo", 1)
        dataDocument.put("1", "主从标识")

        ObjectDataDocument dataDocument1 = new ObjectDataDocument()
        dataDocument1.put("rowNo", 2)
        dataDocument1.put("主从标识", "标识1")

        List<BaseImportDataAction.ImportData> dataList = Lists.newArrayList()
        BaseImportDataAction.ImportData importData = new BaseImportDataAction.ImportData()
        importData.setRowNo(1)
        IObjectData data = new ObjectData()
        data.setId(IdGenerator.get())
        data.set("mark_label_virtual_api", "主从标识")
        importData.setData(data)
        dataList.add(importData)
        List<IObjectData> actualList = Lists.newArrayList()
        actualList.add(data)

        Map<String, List<BaseImportAction.FieldMapping>> fieldMappingMap = Maps.newHashMap()
        List<BaseImportAction.FieldMapping> fieldMappings = Lists.newArrayList()
        BaseImportAction.FieldMapping fieldMapping = new BaseImportAction.FieldMapping()
        fieldMapping.setApiName("md_field__c")
        fieldMapping.setColIndex("1")
        fieldMapping.setImportFieldMark(ImportExportExt.EXT_INFO_RELATED_MARK)
        fieldMappings.add(fieldMapping)
        fieldMappingMap.put(objectApiName, fieldMappings)

        List<String> unionApiNameList = [objectApiName]

        List<BaseImportAction.DetailInfo> detailInfoList = []
        BaseImportAction.DetailInfo detailInfo = new BaseImportAction.DetailInfo()
        detailInfoList.add(detailInfo)

        List<BaseImportAction.ImportError> allErrorList = Lists.newArrayList();
        BaseImportAction.ImportError error = new BaseImportAction.ImportError()
        error.setRowNo(2)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                supportFieldMapping: supportFieldMapping,
                importPreProcessing: importPreProcessing,
                rows: [dataDocument, dataDocument1],
                detailInfo: detailInfoList,
                unionApiNameList: unionApiNameList
        )

        StandardUnionInsertImportDataAction action = new StandardUnionInsertImportDataAction(
                arg: arg,
                fieldMappings: fieldMappingMap,
                objectDescribe: objectDescribe,
                actionContext: actionContext,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                serviceFacade: serviceFacade,
                infraServiceFacade: infraServiceFacade,
                actualList: actualList,
                allErrorList: allErrorList
        )
        BaseImportAction.Result result = new BaseImportAction.Result()
        when:
        infraServiceFacade.findUnionInsertImportMark(_, _, _, _) >> Maps.newHashMap()
        action.generateResult(result)
        then:
        noExceptionThrown()
        where:
        importPreProcessing | supportFieldMapping
        false               | true
        false               | false
        true                | false
        true                | true
    }

    def "test validateRelatedObject"() {
        given: "A StandardUnionInsertImportDataAction instance and mock dependencies"
        def action = new StandardUnionInsertImportDataAction()
        def mockFieldDescribe = Mock(IFieldDescribe)
        def mockObjectReferenceFieldDescribe = Mock(ObjectReferenceFieldDescribe)
        def mockObjectReferenceManyFieldDescribe = Mock(ObjectReferenceManyFieldDescribe)

        and: "Mocking the behavior of field describes"
        mockFieldDescribe.getType() >> IFieldType.MASTER_DETAIL
        mockObjectReferenceFieldDescribe.getType() >> IFieldType.OBJECT_REFERENCE
        mockObjectReferenceManyFieldDescribe.getType() >> IFieldType.OBJECT_REFERENCE_MANY

        when: "validateRelatedObject is called with a specific matching type"
        action.validateRelatedObject(matchingType)

        then: "The expected behavior occurs based on the matching type"
        // Add assertions here based on the expected behavior

        where:
        matchingType << [1, 2, 3] // Replace with actual matching types used in your application
    }
}
