package com.facishare.paas.appframework.rest.dto.describe;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by zhouwr on 2022/4/28.
 */
public interface FindSimpleDetailDescribes {
    @Data
    class Arg {
        private String masterApiName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private List<SimpleDescribe> detailDescribeList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class SimpleDescribe {
        private String apiName;
        private String displayName;
        private String defineType;
        private Boolean active;
    }
}
