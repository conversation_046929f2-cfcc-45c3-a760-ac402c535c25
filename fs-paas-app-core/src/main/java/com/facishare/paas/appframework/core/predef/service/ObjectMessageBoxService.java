package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.service.MessageBoxService;
import com.facishare.paas.appframework.common.service.dto.FindTodoCount;
import com.facishare.paas.appframework.common.service.dto.FindTodoList;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.service.dto.messagebox.FindTodoMessageCount;
import com.facishare.paas.appframework.core.predef.service.dto.messagebox.FindTodoMessageList;
import com.facishare.paas.appframework.metadata.MetaDataMiscServiceImpl;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@ServiceModule("message_box")
@Component
public class ObjectMessageBoxService {
    @Autowired
    private MessageBoxService messageBoxService;
    @Autowired
    ServiceFacade serviceFacade;
    @Autowired
    private MetaDataMiscServiceImpl miscService;

    @ServiceMethod("todo_count")
    public FindTodoMessageCount.Result findTodoMessageCount(FindTodoMessageCount.Arg arg, ServiceContext serviceContext) {
        FindTodoCount.Arg serviceArg = FindTodoCount.Arg.builder()
                .tenantId(serviceContext.getTenantId())
                .userId(serviceContext.getUser().getUserId())
                .build();
        FindTodoCount.Result result = messageBoxService.findTodoCount(serviceContext.getUser(), serviceArg);
        return FindTodoMessageCount.Result.from(result);
    }

    @ServiceMethod("todo_list")
    public FindTodoMessageList.Result findTodoMessageList(FindTodoMessageList.Arg arg, ServiceContext serviceContext) {
        FindTodoList.Arg serviceArg = FindTodoList.Arg.builder()
                .tenantId(serviceContext.getTenantId())
                .userId(serviceContext.getUser().getUserId())
                .apiName(arg.getApiName())
                .pageSize(arg.getLimit())
                .offsetNum(arg.getOffset())
                .status(arg.getStatus())
                .type(arg.getType())
                .build();
        FindTodoList.Result result = messageBoxService.findTodoList(serviceContext.getUser(), serviceArg);

        //补充对象名称等显示信息
        fillDescribeInfo(serviceContext, result);

        //补充对象数据主属性
        fillName(serviceContext, result);

        //补充人员信息
        fillUserInfo(serviceContext, result);
        return FindTodoMessageList.Result.from(result);
    }

    private void fillName(ServiceContext serviceContext, FindTodoList.Result result) {
        Map<String, List<String>> idListMap = CollectionUtils.nullToEmpty(result.getMessageList())
                .stream()
                .filter(a -> !Strings.isNullOrEmpty(a.getObjectId()))
                .filter(a -> !Strings.isNullOrEmpty(a.getApiName()))
                .collect(Collectors.groupingBy(FindTodoList.MessageInfo::getApiName,
                        Collectors.mapping(FindTodoList.MessageInfo::getObjectId, Collectors.toList())));

        if (CollectionUtils.empty(idListMap)) {
            return;
        }

        Map<String, String> idNameMap = Maps.newConcurrentMap();
        miscService.fillNameFromNameCache("messageBoxService", serviceContext.getUser(), null, idListMap, idNameMap);

        CollectionUtils.nullToEmpty(result.getMessageList())
                .stream()
                .filter(a -> !Strings.isNullOrEmpty(a.getObjectId()))
                .filter(a -> !Strings.isNullOrEmpty(a.getApiName()))
                .forEach(a -> Optional.ofNullable(idNameMap.get(createNameCacheKey(a.getApiName(), a.getObjectId())))
                        .ifPresent(a::setObjectName));
    }


    private String createNameCacheKey(String describeApiName, String id) {
        return describeApiName + "_" + id;
    }

    private void fillDescribeInfo(ServiceContext serviceContext, FindTodoList.Result result) {
        List<String> apiNameList = CollectionUtils.nullToEmpty(result.getMessageList())
                .stream()
                .map(FindTodoList.MessageInfo::getApiName)
                .collect(Collectors.toList());

        Map<String, IObjectDescribe> map = serviceFacade.findObjects(serviceContext.getTenantId(), apiNameList);
        CollectionUtils.nullToEmpty(result.getMessageList())
                .forEach(a -> Optional.ofNullable(map.get(a.getApiName()))
                        .ifPresent(b -> a.setDescribeDisplayName(b.getDisplayName())));
    }

    private void fillUserInfo(ServiceContext serviceContext, FindTodoList.Result result) {
        List<String> userIdList = getExtendFieldStream(result)
                .map(a -> a.getExtendFieldObj().getCreateBy())
                .collect(Collectors.toList());

        List<UserInfo> userInfoList = serviceFacade.getUserNameByIds(
                serviceContext.getTenantId(), serviceContext.getUser().getUserId(), userIdList);

        Map<String, String> userMap = userInfoList
                .stream()
                .collect(Collectors.toMap(UserInfo::getId, UserInfo::getName));

        getExtendFieldStream(result)
                .forEach(a -> a.getExtendFieldObj().setCreateByName(userMap.get(a.getExtendFieldObj().getCreateBy())));
    }

    @NotNull
    private Stream<FindTodoList.MessageInfo> getExtendFieldStream(FindTodoList.Result result) {
        return CollectionUtils.nullToEmpty(result.getMessageList())
                .stream()
                .filter(a -> Objects.nonNull(a.getExtendFieldObj()) &&
                        !Strings.isNullOrEmpty(a.getExtendFieldObj().getCreateBy()));

    }
}
