package com.facishare.paas.appframework.core.model.handler;

import com.fxiaoke.transaction.tcc.api.context.BranchTransactionalContext;

/**
 * Created by zhouwr on 2023/7/10.
 */
public interface RollbackHandler<A extends Handler.Arg> {
    /**
     * 分布式业务处理器的回滚方法
     *
     * @param branchTransactionalContext 分布式事务上下文
     * @param context                    接口上下文
     * @param arg                        接口参数
     * @return 是否执行成功
     */
    boolean rollback(BranchTransactionalContext branchTransactionalContext, HandlerContext context, A arg);
}
