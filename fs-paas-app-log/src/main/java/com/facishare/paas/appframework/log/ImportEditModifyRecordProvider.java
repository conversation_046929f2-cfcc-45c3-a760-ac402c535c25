package com.facishare.paas.appframework.log;

import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import org.springframework.stereotype.Component;

/**
 * @Desc
 * <AUTHOR>
 * @CreateTime 2019-08-13 13:58
 */
@Component("importEditModifyRecordProvider")
public class ImportEditModifyRecordProvider extends EditModifyRecordProvider {
    @Override
    public String getApiName() {
        return ActionType.UpdateImport.getId();
    }

    @Override
    public ModifyRecord getModifyRecord(LogInfo logInfo) {
        return super.getModifyRecord(logInfo);
    }
}
