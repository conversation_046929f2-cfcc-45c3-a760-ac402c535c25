package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * SuspendedComponentInfoExt单元测试类
 */
@ExtendWith(MockitoExtension.class)
class SuspendedComponentInfoExtTest {

  @Mock
  private IComponent mockComponent;

  private SuspendedComponentInfoExt suspendedComponentInfoExt;

  @BeforeEach
  void setUp() {
    suspendedComponentInfoExt = new SuspendedComponentInfoExt(mockComponent);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试构造函数创建对象
   */
  @Test
  @DisplayName("测试构造函数")
  void testConstructor() {
    IComponent component = mock(IComponent.class);
    
    SuspendedComponentInfoExt result = new SuspendedComponentInfoExt(component);
    
    assertNotNull(result);
    assertEquals(component, result.getComponent());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试of静态方法创建对象
   */
  @Test
  @DisplayName("测试of静态方法")
  void testOfMethod() {
    IComponent component = mock(IComponent.class);
    
    SuspendedComponentInfoExt result = SuspendedComponentInfoExt.of(component);
    
    assertNotNull(result);
    assertEquals(component, result.getComponent());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeActions方法当actions为空时返回空列表
   */
  @Test
  @DisplayName("测试removeActions方法 - actions为空时返回空列表")
  void testRemoveActionsWhenEmpty() {
    // 准备测试数据
    Map<String, List<IRecordTypeOption>> validRecordTypeListMap = new HashMap<>();
    
    // Mock getActions返回空列表
    suspendedComponentInfoExt = spy(suspendedComponentInfoExt);
    doReturn(Lists.newArrayList()).when(suspendedComponentInfoExt).getActions();
    
    // 执行测试
    List<Integer> result = suspendedComponentInfoExt.removeActions(validRecordTypeListMap);
    
    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeActions方法移除无效的NEW_OBJECT类型action
   */
  @Test
  @DisplayName("测试removeActions方法 - 移除无效的NEW_OBJECT类型action")
  void testRemoveActionsRemoveInvalidNewObjectActions() {
    try (MockedStatic<StringUtils> mockedStringUtils = mockStatic(StringUtils.class)) {
      // 准备测试数据
      SuspendedActionInfo validAction = mock(SuspendedActionInfo.class);
      SuspendedActionInfo invalidAction = mock(SuspendedActionInfo.class);
      
      when(validAction.getType()).thenReturn(SuspendedActionInfo.NEW_OBJECT);
      when(validAction.getRelatedObjectApiName()).thenReturn("validObject");
      when(validAction.getRelatedObjectRecordType()).thenReturn("validRecordType");
      
      when(invalidAction.getType()).thenReturn(SuspendedActionInfo.NEW_OBJECT);
      when(invalidAction.getRelatedObjectApiName()).thenReturn("invalidObject");
      
      List<SuspendedActionInfo> actions = Lists.newArrayList(validAction, invalidAction);
      
      // Mock validRecordTypeListMap
      IRecordTypeOption recordTypeOption = mock(IRecordTypeOption.class);
      when(recordTypeOption.getApiName()).thenReturn("validRecordType");
      
      Map<String, List<IRecordTypeOption>> validRecordTypeListMap = new HashMap<>();
      validRecordTypeListMap.put("validObject", Lists.newArrayList(recordTypeOption));
      
      // Mock StringUtils - 只Mock实际使用的调用
      mockedStringUtils.when(() -> StringUtils.equals(anyString(), anyString())).thenReturn(true);
      mockedStringUtils.when(() -> StringUtils.isNotEmpty(anyString())).thenReturn(true);
      
      // Mock spy对象
      suspendedComponentInfoExt = spy(suspendedComponentInfoExt);
      doReturn(actions).when(suspendedComponentInfoExt).getActions();
      doNothing().when(suspendedComponentInfoExt).setActions(any());
      
      // 执行测试
      List<Integer> result = suspendedComponentInfoExt.removeActions(validRecordTypeListMap);
      
      // 验证结果
      assertNotNull(result);
      assertEquals(1, result.size());
      assertEquals(1, result.get(0).intValue()); // invalidAction的索引
      
      // 验证setActions被调用
      verify(suspendedComponentInfoExt).setActions(any());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeActions方法当recordType为空时不移除action
   */
  @Test
  @DisplayName("测试removeActions方法 - recordType为空时不移除action")
  void testRemoveActionsWhenRecordTypeIsEmpty() {
    try (MockedStatic<StringUtils> mockedStringUtils = mockStatic(StringUtils.class)) {
      // 准备测试数据
      SuspendedActionInfo action = mock(SuspendedActionInfo.class);
      when(action.getType()).thenReturn(SuspendedActionInfo.NEW_OBJECT);
      when(action.getRelatedObjectApiName()).thenReturn("testObject");
      when(action.getRelatedObjectRecordType()).thenReturn("");
      
      List<SuspendedActionInfo> actions = Lists.newArrayList(action);
      
      // Mock validRecordTypeListMap
      IRecordTypeOption recordTypeOption = mock(IRecordTypeOption.class);
      when(recordTypeOption.getApiName()).thenReturn("someRecordType");
      
      Map<String, List<IRecordTypeOption>> validRecordTypeListMap = new HashMap<>();
      validRecordTypeListMap.put("testObject", Lists.newArrayList(recordTypeOption));
      
      // Mock StringUtils
      mockedStringUtils.when(() -> StringUtils.equals(SuspendedActionInfo.NEW_OBJECT, SuspendedActionInfo.NEW_OBJECT))
          .thenReturn(true);
      mockedStringUtils.when(() -> StringUtils.isNotEmpty("")).thenReturn(false);
      
      // Mock spy对象
      suspendedComponentInfoExt = spy(suspendedComponentInfoExt);
      doReturn(actions).when(suspendedComponentInfoExt).getActions();
      doNothing().when(suspendedComponentInfoExt).setActions(any());
      
      // 执行测试
      List<Integer> result = suspendedComponentInfoExt.removeActions(validRecordTypeListMap);
      
      // 验证结果
      assertNotNull(result);
      assertTrue(result.isEmpty()); // 空recordType表示全部业务类型，不应该被移除
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeI18nPropsActions方法移除指定索引的actions
   */
  @Test
  @DisplayName("测试removeI18nPropsActions方法")
  void testRemoveI18nPropsActions() {
    // 准备测试数据
    Map<String, Object> action1 = new HashMap<>();
    action1.put("type", "action1");
    
    Map<String, Object> action2 = new HashMap<>();
    action2.put("type", "action2");
    
    Map<String, Object> action3 = new HashMap<>();
    action3.put("type", "action3");
    
    List<Map> actionsInI18nProps = Lists.newArrayList(action1, action2, action3);
    List<Integer> actionIndexList = Lists.newArrayList(0, 2); // 移除第0和第2个action
    
    // Mock spy对象
    suspendedComponentInfoExt = spy(suspendedComponentInfoExt);
    doReturn(actionsInI18nProps).when(suspendedComponentInfoExt).getActionsInI18nProps();
    
    // 执行测试
    suspendedComponentInfoExt.removeI18nPropsActions(actionIndexList);
    
    // 验证结果 - 应该只剩下action2
    assertEquals(1, actionsInI18nProps.size());
    assertEquals(action2, actionsInI18nProps.get(0));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeI18nPropsActions方法当actionIndexList为空时不移除任何action
   */
  @Test
  @DisplayName("测试removeI18nPropsActions方法 - actionIndexList为空时不移除任何action")
  void testRemoveI18nPropsActionsWhenIndexListEmpty() {
    // 准备测试数据
    Map<String, Object> action1 = new HashMap<>();
    action1.put("type", "action1");
    
    Map<String, Object> action2 = new HashMap<>();
    action2.put("type", "action2");
    
    List<Map> actionsInI18nProps = Lists.newArrayList(action1, action2);
    List<Integer> emptyIndexList = Lists.newArrayList();
    
    // Mock spy对象
    suspendedComponentInfoExt = spy(suspendedComponentInfoExt);
    doReturn(actionsInI18nProps).when(suspendedComponentInfoExt).getActionsInI18nProps();
    
    // 执行测试
    suspendedComponentInfoExt.removeI18nPropsActions(emptyIndexList);
    
    // 验证结果 - 所有action都应该保留
    assertEquals(2, actionsInI18nProps.size());
    assertEquals(action1, actionsInI18nProps.get(0));
    assertEquals(action2, actionsInI18nProps.get(1));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getActionsInI18nProps方法当actions是List类型时返回List
   */
  @Test
  @DisplayName("测试getActionsInI18nProps方法 - actions是List类型时返回List")
  void testGetActionsInI18nPropsWhenActionsList() {
    // 准备测试数据
    Map<String, Object> action1 = new HashMap<>();
    action1.put("type", "action1");
    
    List<Map> actionsList = Lists.newArrayList(action1);
    Map<String, Object> i18nProps = new HashMap<>();
    i18nProps.put(SuspendedComponentInfo.ACTIONS, actionsList);
    
    // Mock spy对象
    suspendedComponentInfoExt = spy(suspendedComponentInfoExt);
    doReturn(i18nProps).when(suspendedComponentInfoExt).getI18NProps();
    
    // 执行测试
    List<Map> result = suspendedComponentInfoExt.getActionsInI18nProps();
    
    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals(action1, result.get(0));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getActionsInI18nProps方法当actions不是List类型时返回空列表
   */
  @Test
  @DisplayName("测试getActionsInI18nProps方法 - actions不是List类型时返回空列表")
  void testGetActionsInI18nPropsWhenActionsNotList() {
    // 准备测试数据
    Map<String, Object> i18nProps = new HashMap<>();
    i18nProps.put(SuspendedComponentInfo.ACTIONS, "not a list");
    
    // Mock spy对象
    suspendedComponentInfoExt = spy(suspendedComponentInfoExt);
    doReturn(i18nProps).when(suspendedComponentInfoExt).getI18NProps();
    
    // 执行测试
    List<Map> result = suspendedComponentInfoExt.getActionsInI18nProps();
    
    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getActionsInI18nProps方法当actions为null时返回空列表
   */
  @Test
  @DisplayName("测试getActionsInI18nProps方法 - actions为null时返回空列表")
  void testGetActionsInI18nPropsWhenActionsNull() {
    // 准备测试数据
    Map<String, Object> i18nProps = new HashMap<>();
    i18nProps.put(SuspendedComponentInfo.ACTIONS, null);
    
    // Mock spy对象
    suspendedComponentInfoExt = spy(suspendedComponentInfoExt);
    doReturn(i18nProps).when(suspendedComponentInfoExt).getI18NProps();
    
    // 执行测试
    List<Map> result = suspendedComponentInfoExt.getActionsInI18nProps();
    
    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试Delegate注解的功能
   */
  @Test
  @DisplayName("测试Delegate注解功能")
  void testDelegateAnnotation() {
    // 测试通过Delegate注解可以直接调用IComponent的方法
    String testName = "testName";
    when(mockComponent.getName()).thenReturn(testName);
    
    String result = suspendedComponentInfoExt.getName();
    
    assertEquals(testName, result);
    verify(mockComponent).getName();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试继承自SuspendedComponentInfo的方法是否可用
   */
  @Test
  @DisplayName("测试继承的方法可用性")
  void testInheritedMethods() {
    // 测试getComponent方法
    IComponent component = suspendedComponentInfoExt.getComponent();
    assertEquals(mockComponent, component);
    
    // 测试可以调用父类的方法（通过spy验证）
    suspendedComponentInfoExt = spy(suspendedComponentInfoExt);
    doReturn(Lists.newArrayList()).when(suspendedComponentInfoExt).getActions();
    
    List<SuspendedActionInfo> actions = suspendedComponentInfoExt.getActions();
    assertNotNull(actions);
  }
}
