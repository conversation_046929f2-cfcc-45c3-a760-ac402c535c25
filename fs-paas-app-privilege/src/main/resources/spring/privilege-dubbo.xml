<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <!-- dubbo config -->
<!--    <dubbo:registry address="${dubbo.registry.address}" client="curator"/>-->

    <!--获取 深圳智能表单的外部联系人信息-->
<!--    <dubbo:reference id="permissionService"-->
<!--                     version="5.7"-->
<!--                     interface="com.facishare.organization.adapter.api.permission.service.PermissionService"/>-->
<!--    <dubbo:reference id="privilegeEmployeeService"-->
<!--                     interface="com.facishare.organization.adapter.api.service.EmployeeService"-->
<!--                     check="false" retries="0" timeout="50000"/>-->

<!--    <dubbo:reference id="privilegeDepartmentService"-->
<!--                     interface="com.facishare.organization.adapter.api.service.DepartmentService"-->
<!--                     check="false" retries="0" timeout="50000"/>-->
</beans>
