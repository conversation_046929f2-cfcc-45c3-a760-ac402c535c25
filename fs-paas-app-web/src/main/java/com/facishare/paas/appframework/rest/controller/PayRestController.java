package com.facishare.paas.appframework.rest.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.common.utils.AesException;
import com.facishare.open.common.utils.MsgEncryptor;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.jaxrs.annotation.RestAPI;
import com.facishare.paas.appframework.rest.dto.data.Pay;
import com.facishare.paas.appframework.rest.service.PayRestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.POST;
import javax.ws.rs.Path;

@RestAPI
@Controller
@Path("/v1/inner/rest/pay")
@Slf4j
public class PayRestController {

    @Autowired
    private PayRestService payRestService;

    @POST
    @Path("/callback")
    public Pay.Result payCallback(String content) {
        log.info("Enter payCallback in PayRestController, content:{}", content);
        String aesKey = "abcdefghijklmnopqrstuvwxyz0123456789ABCDEFG";
        JSONObject jsonObject = JSONObject.parseObject(content);
        String encyptStr = jsonObject.getString("content");
        Pay.Arg arg = null;
        try {
            JSONObject result = JSONObject.parseObject(MsgEncryptor.decrypt(encyptStr, aesKey));
            log.info("Decrypt result: {}", result);
            arg = JSONObject.toJavaObject(result, Pay.Arg.class);
            //业务处理
            RequestContext context = RequestContextManager.getContext();
            return payRestService.payCallback(arg, context);

        } catch (AesException e) {
            log.error("Decrypt wrong: {}", e.getMessage());
            throw new ValidateException("Decrypt Error");
        }
    }
}
