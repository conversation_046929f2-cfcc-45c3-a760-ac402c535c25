package com.facishare.paas.appframework.jaxrs.provider;

import com.facishare.paas.appframework.core.model.JSONSerializer;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.SerializerManager;
import org.apache.commons.io.Charsets;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.Consumes;
import javax.ws.rs.WebApplicationException;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.ext.MessageBodyReader;
import javax.ws.rs.ext.Provider;
import java.io.IOException;
import java.io.InputStream;
import java.lang.annotation.Annotation;
import java.lang.reflect.Type;
import java.util.Objects;

/**
 * Created by zhouwr on 2022/5/10.
 */
@Component
@Provider
@Consumes({"application/json", "application/simplejson"})
public class APIArgProvider<T> implements MessageBodyReader<T> {

    @Autowired
    private SerializerManager serializerManager;

    @Override
    public boolean isReadable(Class<?> type, Type genericType, Annotation[] annotations, MediaType mediaType) {
        return true;
    }

    @Override
    public T readFrom(Class<T> type,
                      Type genericType,
                      Annotation[] annotations,
                      MediaType mediaType,
                      MultivaluedMap<String, String> httpHeaders,
                      InputStream entityStream) throws IOException, WebApplicationException {
        String body = IOUtils.toString(entityStream, Charsets.UTF_8);
        if (String.class.equals(type)) {
            return (T) body;
        }
        RequestContext.ContentType contentType;
        if (Objects.equals("application/simplejson", mediaType.getType())) {
            contentType = RequestContext.ContentType.SIMPLE_JSON;
        } else {
            contentType = RequestContext.ContentType.FULL_JSON;
        }
        JSONSerializer serializer = serializerManager.getSerializer(contentType);
        return serializer.decode(type, body);
    }
}
