package com.facishare.paas.appframework.core.util;

import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

public class ConfigCenter {
    // TODO: 2022/4/28 全网去掉
    private static Set<String> tenantIdsOf805AccountAuth;

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-customeraccount", config -> {
            String tenantIdsOf805AccountAuthStr = config.get("tenantIdsOf805AccountAuth", "");
            tenantIdsOf805AccountAuth = Sets.newHashSet(Splitter.on(",").split(tenantIdsOf805AccountAuthStr));
        });
    }

    public static boolean is805AccountAuth(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return false;
        }

        //ALL 代表全网
        if (tenantIdsOf805AccountAuth.contains("ALL")) {
            return true;
        }
        return tenantIdsOf805AccountAuth.contains(tenantId);
    }
}