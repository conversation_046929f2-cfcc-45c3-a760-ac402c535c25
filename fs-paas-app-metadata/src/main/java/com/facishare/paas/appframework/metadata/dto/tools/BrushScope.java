package com.facishare.paas.appframework.metadata.dto.tools;

import lombok.Getter;

@Getter
public enum BrushScope {
    SPECIFIC_ENTERPRISE(0),
    SYSTEM_LIBRARY(1),
    ALL_ENTERPRISE(2);

    private int value;

    BrushScope(int value) {
        this.value = value;
    }

    public static BrushScope of(int value) {
        for (BrushScope brushScope : BrushScope.values()) {
            if (brushScope.getValue() == value) {
                return brushScope;
            }
        }
        return null;
    }
}
