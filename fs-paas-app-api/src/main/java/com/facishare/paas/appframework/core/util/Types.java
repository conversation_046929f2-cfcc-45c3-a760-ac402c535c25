package com.facishare.paas.appframework.core.util;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Map;

/**
 * Created by zhouwr on 2017/10/12
 */
@Slf4j
public class Types {

    private static final Class CLASS_NOT_FOUND = AppClassNotFound.class;

    private static final Map<Class, Class> argClassMap = Maps.newConcurrentMap();

    private static final LoadingCache<String, Class<?>> classCache = Caffeine.newBuilder()
            .maximumSize(10000)
            .initialCapacity(1000)
            .build(key -> {
                try {
                    return Class.forName(key);
                } catch (ClassNotFoundException e) {
                    return CLASS_NOT_FOUND;
                }
            });

    public static Class detectFirstGenericArgType(Class clazz) {
        Class argClass = argClassMap.get(clazz);
        if (argClass != null) {
            return argClass;
        }
        Type superType = clazz.getGenericSuperclass();
        argClass = detectFirstGenericArgType(superType);
        argClassMap.put(clazz, argClass);

        return argClass;
    }

    public static <T> Class<T> detectFirstGenericArgType(Type type) {
        while (type != null && !ParameterizedType.class.isAssignableFrom(type.getClass())) {
            type = ((Class) type).getGenericSuperclass();
        }
        if (type == null) {
            return null;
        }
        return (Class<T>) ((ParameterizedType) type).getActualTypeArguments()[0];
    }

    public static Type[] detectGenericTypes(Type type) {
        while (type != null && !ParameterizedType.class.isAssignableFrom(type.getClass())) {
            type = ((Class) type).getGenericSuperclass();
        }
        if (type == null) {
            return null;
        }
        return ((ParameterizedType) type).getActualTypeArguments();
    }

    public static Class<?> forName(String className) {
        if (Strings.isNullOrEmpty(className)) {
            return null;
        }
        Class clazz = classCache.get(className);
        if (clazz == CLASS_NOT_FOUND) {
            return null;
        }
        return clazz;
    }

    private static class AppClassNotFound {

    }

}
