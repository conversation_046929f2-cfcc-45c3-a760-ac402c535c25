package com.facishare.crm.privilege.swagger.api;

import com.facishare.crm.privilege.swagger.ApiClient;
import com.fxiaoke.common.IpUtil;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.UUID;

/**
 * Created by shun on 2020/2/5
 */
@Slf4j
public class ApiClientHeaderUtil {
    private static final String APP_NAME = ConfigHelper.getProcessInfo().getName();
    private static final String LOCAL_IP = IpUtil.getSiteLocalIp();

    public static void initHttpHeader(Map<String, String> localVarHeaderParams, ApiClient apiClient) {
        final String[] localVarAccepts = {
                "application/json"
        };
        final String localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) localVarHeaderParams.put("Accept", localVarAccept);

        final String[] localVarContentTypes = {
                "application/json"
        };
        final String localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);
        localVarHeaderParams.put("Content-Type", localVarContentType);

        try {
            refillRequest(localVarHeaderParams);
        } catch (Exception e) {
            log.warn(String.format("initHttpHeader error %s", e.getMessage()), e);
        }
    }

    /**
     * 重新填充（添加或替换） request header
     */
    private static void refillRequest(Map<String, String> localVarHeaderParams) {
        // 如果已经设置过，就不要再重复设置了
        TraceContext context = TraceContext.get();
        String locale = context.getLocale();
        if (locale != null) {
            localVarHeaderParams.put("Accept-Language", locale);
        }
        if (context.isColor()) {
            localVarHeaderParams.put("X-fs-Trace-Color", "1");
        }
        String traceId = context.getTraceId();
        if (traceId == null || traceId.length() == 0 || "null".equals(traceId)) {
            traceId = APP_NAME + '/' + UUID.randomUUID().toString().substring(0, 16);
            context.setTraceId(traceId);
        }
        localVarHeaderParams.put("X-fs-Trace-Id", traceId);
        String rpcId = context.getRpcId();
        if (rpcId != null && rpcId.length() > 0) {
            localVarHeaderParams.put("X-fs-RPC-Id", rpcId);
        }
        String ei = context.getEi();
        if (ei != null && ei.length() > 0) {
            localVarHeaderParams.put("X-fs-Enterprise-Id", ei);
            localVarHeaderParams.put("X-fs-ei", ei);
        }
        String employeeId = context.getEmployeeId();
        if (employeeId != null && employeeId.length() > 0) {
            localVarHeaderParams.put("X-fs-Employee-Id", employeeId);
        }
        String ea = context.getEa();
        if (ea != null && ea.length() > 0) {
            localVarHeaderParams.put("X-fs-Enterprise-Account", ea);
        }
        String uid = context.getUid();
        if (uid != null && uid.length() > 0) {
            localVarHeaderParams.put("X-fs-User-Info", uid);
        }
        localVarHeaderParams.put("x-peer-name", APP_NAME);
        localVarHeaderParams.put("x-real-ip", LOCAL_IP);
        String s = String.format("traceId=%s;userId=%s;color=%s;rpcId=%s", traceId, uid, context.isColor(), rpcId);
        localVarHeaderParams.put("x-trace", s);

        fillIstioTraceHeader(localVarHeaderParams);

    }

    private static void fillIstioTraceHeader(Map<String, String> builder) {
        TraceContext context = TraceContext.get();
        if (addHeaderIfNotEmpty(builder, "x-request-id", context.getxRequestId()) && addHeaderIfNotEmpty(builder, "x-b3-traceid", context.getxB3TraceId())) {
            addHeaderIfNotEmpty(builder, "x-b3-spanid", context.getxB3SpanId());
            addHeaderIfNotEmpty(builder, "x-b3-parentspanid", context.getxB3ParentSpanId());
            addHeaderIfNotEmpty(builder, "x-b3-sampled", context.getxB3Sampled());
            addHeaderIfNotEmpty(builder, "x-b3-flags", context.getxB3Flags());
        }
    }

    private static boolean addHeaderIfNotEmpty(Map<String, String> localVarHeaderParams, String key, String value) {
        if (Strings.isNullOrEmpty(value)) {
            return false;
        }
        localVarHeaderParams.put(key, value);
        return true;
    }

}
