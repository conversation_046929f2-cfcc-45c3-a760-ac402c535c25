package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试BigTextFieldDataConverter类的大文本字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class BigTextFieldDataConverterTest {

  @Mock
  private IObjectDataProxyService dataService;

  @Mock
  private SessionContext sessionContext;

  private BigTextFieldDataConverter converter;
  private IFieldDescribe fieldDescribe;
  private IObjectData objectData;

  @BeforeEach
  void setUp() {
    converter = new BigTextFieldDataConverter();
    fieldDescribe = createBigTextFieldDescribe("big_text_field");
    
    // 设置转换器的属性
    converter.setFieldDescribe(fieldDescribe);
    converter.setDataService(dataService);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法正常转换大文本
   */
  @Test
  @DisplayName("测试convertFieldData - 正常大文本转换")
  void testConvertFieldData_NormalBigTextConversion() throws Exception {
    // 准备测试数据
    String bigTextContent = "这是一段很长的文本内容，用于测试大文本字段的转换功能。" +
                           "它可能包含多行内容，以及各种特殊字符和格式。";
    objectData = createObjectData("big_text_field", bigTextContent);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals(bigTextContent, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理null值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null值")
  void testConvertFieldData_HandlesNullValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("big_text_field", null);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertNull(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理空字符串
   */
  @Test
  @DisplayName("测试convertFieldData - 处理空字符串")
  void testConvertFieldData_HandlesEmptyString() throws Exception {
    // 准备测试数据
    objectData = createObjectData("big_text_field", "");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理包含特殊字符的文本
   */
  @Test
  @DisplayName("测试convertFieldData - 包含特殊字符的文本")
  void testConvertFieldData_SpecialCharacters() throws Exception {
    // 准备测试数据 - 包含特殊字符的文本
    String specialText = "特殊字符测试：@#$%^&*()_+-={}[]|\\:;\"'<>,.?/~`\n换行符\t制表符";
    objectData = createObjectData("big_text_field", specialText);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 特殊字符应该被保留
    assertEquals(specialText, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理多行文本
   */
  @Test
  @DisplayName("测试convertFieldData - 多行文本")
  void testConvertFieldData_MultilineText() throws Exception {
    // 准备测试数据 - 多行文本
    String multilineText = "第一行文本\n第二行文本\n第三行文本\n\n空行测试\n最后一行";
    objectData = createObjectData("big_text_field", multilineText);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 多行格式应该被保留
    assertEquals(multilineText, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理超长文本
   */
  @Test
  @DisplayName("测试convertFieldData - 超长文本")
  void testConvertFieldData_VeryLongText() throws Exception {
    // 准备测试数据 - 生成超长文本
    StringBuilder longTextBuilder = new StringBuilder();
    for (int i = 0; i < 1000; i++) {
      longTextBuilder.append("这是第").append(i + 1).append("段重复的文本内容。");
    }
    String longText = longTextBuilder.toString();
    
    objectData = createObjectData("big_text_field", longText);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 超长文本应该完整保留
    assertEquals(longText, result);
    assertTrue(result.length() > 10000); // 确保文本确实很长
  }

  /**
   * 提供不同类型的大文本数据测试
   */
  private static Stream<Arguments> provideBigTextData() {
    return Stream.of(
        Arguments.of("普通文本", "普通文本"),
        Arguments.of("", ""),
        Arguments.of(null, null),
        Arguments.of("包含\n换行\t制表符的文本", "包含\n换行\t制表符的文本"),
        Arguments.of("Unicode字符：😀🎉🌟", "Unicode字符：😀🎉🌟"),
        Arguments.of("   前后有空格   ", "   前后有空格   ")
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理不同类型的大文本数据
   */
  @ParameterizedTest
  @MethodSource("provideBigTextData")
  @DisplayName("测试convertFieldData - 不同类型的大文本数据")
  void testConvertFieldData_DifferentBigTextData(String inputValue, String expectedResult) throws Exception {
    // 准备测试数据
    objectData = createObjectData("big_text_field", inputValue);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals(expectedResult, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理字段不存在的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 字段不存在")
  void testConvertFieldData_FieldNotExists() throws Exception {
    // 准备测试数据 - 创建不包含目标字段的数据
    objectData = createObjectData("other_field", "其他字段的内容");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 字段不存在时应该返回null
    assertNull(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理非字符串类型值
   */
  @Test
  @DisplayName("测试convertFieldData - 非字符串类型值")
  void testConvertFieldData_NonStringValue() throws Exception {
    // 准备测试数据 - 存储非字符串类型的值
    objectData = createObjectData("big_text_field", 12345);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 非字符串类型会被转换为字符串
    assertEquals("12345", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理包含HTML标签的文本
   */
  @Test
  @DisplayName("测试convertFieldData - 包含HTML标签的文本")
  void testConvertFieldData_HTMLContent() throws Exception {
    // 准备测试数据 - 包含HTML标签的文本
    String htmlContent = "<p>这是一个<strong>粗体</strong>文本，包含<em>斜体</em>和<a href='#'>链接</a>。</p>";
    objectData = createObjectData("big_text_field", htmlContent);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - HTML标签应该被保留（大文本字段不进行HTML解析）
    assertEquals(htmlContent, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试转换器的基本属性设置
   */
  @Test
  @DisplayName("测试转换器属性设置")
  void testConverterProperties() {
    // 验证属性设置
    assertEquals(fieldDescribe, converter.getFieldDescribe());
    assertEquals(dataService, converter.getDataService());
  }

  /**
   * 创建测试用的大文本字段描述
   */
  private IFieldDescribe createBigTextFieldDescribe(String apiName) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试大文本字段");
    fieldMap.put("type", IFieldType.BIG_TEXT);
    return FieldDescribeFactory.newInstance(fieldMap);
  }

  /**
   * 创建测试用的对象数据
   */
  private IObjectData createObjectData(String fieldName, Object value) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldName, value);
    return new ObjectData(dataMap);
  }
}
