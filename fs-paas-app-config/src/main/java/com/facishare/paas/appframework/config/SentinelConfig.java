package com.facishare.paas.appframework.config;

import com.facishare.paas.appframework.common.util.ChangeListenerHolder;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.release.GrayRule;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.base.Config;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/7/22
 */
@Slf4j
public class SentinelConfig {
    private final List<ResourceGray> resourceGrays;
    private final Map<String, ProfileGray> profileGray;


    private static final Config config = ConfigHelper.getApplicationConfig();
    private static final String GRAY_CONFIG_NAME = "fs.paas.sentinel.gray.file";
    private static final String DEFAULT_PROFILE = "default";

    @JsonCreator
    private SentinelConfig(@JsonProperty("resourceGrays") List<ResourceGray> resourceGrays,
                           @JsonProperty("profileGrays") List<ProfileGray> profileGrays) {
        this.resourceGrays = ImmutableList.copyOf(CollectionUtils.nullToEmpty(resourceGrays));
        this.profileGray = ImmutableMap.copyOf(CollectionUtils.nullToEmpty(profileGrays).stream()
                .filter(it -> !Strings.isNullOrEmpty(it.getEnableProfile()))
                .collect(Collectors.toMap(ProfileGray::getEnableProfile, Function.identity(), (x, y) -> y)));
    }


    public static SentinelConfig getInstance() {
        return Helper.instance;
    }

    public Set<String> getResourceNames(String path) {
        return this.resourceGrays.stream()
                .filter(it -> it.match(path))
                .map(ResourceGray::getResourceName)
                .collect(Collectors.toSet());
    }

    public boolean enableByProfile(String profile) {
        return profileGray.containsKey(profile) || profileGray.containsKey(DEFAULT_PROFILE);
    }

    /**
     * 终止指定应用程序和租户的请求灰度
     * 按灰度配置的顺从上到下，按appName匹配，找到第一个匹配的灰度应用程序和租户，终止请求灰度。
     *
     * @param profile  应用程序名称
     * @param tenantId 租户 ID
     * @return 如果找到了匹配的灰度应用程序和租户，则返回 true；否则返回 false
     */
    public boolean abortRequest(String profile, String tenantId) {
        ProfileGray appGray = Optional.ofNullable(profileGray.get(profile))
                .orElseGet(() -> profileGray.get(DEFAULT_PROFILE));
        return Objects.nonNull(appGray) && appGray.enableAbortTenant(tenantId);
    }

    private static class Helper {
        private static SentinelConfig instance;

        static {
            String grayConfigName = config.get(GRAY_CONFIG_NAME);
            if (Strings.isNullOrEmpty(grayConfigName)) {
                instance = buildEmpty();
            } else {
                ConfigFactory.getConfig(grayConfigName, ChangeListenerHolder.create(config -> {
                    log.warn("reload {}, content:{}", grayConfigName, config.getString());
                    instance = JacksonUtils.fromJson(config.getString(), SentinelConfig.class);
                    if (Objects.isNull(instance)) {
                        instance = buildEmpty();
                    }
                }));
            }
        }

        private static SentinelConfig buildEmpty() {
            return new SentinelConfig(Collections.emptyList(), Collections.emptyList());
        }
    }

    public static class ResourceGray {
        @Getter
        private final String resourceName;
        private final Pattern pattern;

        @JsonCreator
        private ResourceGray(@JsonProperty("resourceName") String resourceName,
                             @JsonProperty("pattern") String pattern) {
            this.resourceName = resourceName;
            this.pattern = Pattern.compile(pattern);
        }

        public boolean match(String str) {
            return this.pattern.matcher(str).matches();
        }
    }

    public static class ProfileGray {
        @Getter
        private final String enableProfile;
        private final GrayRule enableAbortTenantGray;

        @JsonCreator
        private ProfileGray(@JsonProperty("enableProfile") String enableProfile,
                            @JsonProperty("enableAbortTenant") String enableAbortTenant) {
            this.enableProfile = enableProfile;
            this.enableAbortTenantGray = new GrayRule(enableAbortTenant);
        }

        public boolean enableAbortTenant(String ei) {
            return enableAbortTenantGray.isAllow(ei);
        }
    }

}
