package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.internal.AsyncTaskMonitorObjectDataProvider;
import com.facishare.paas.appframework.metadata.internal.InternalObjectDataProvider;
import com.facishare.paas.appframework.metadata.repository.model.MtAsyncTaskMonitor;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.task.async.task.enums.TaskStatus;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class AsyncTaskMonitorObjListController extends BaseListController<AsyncTaskMonitorObjListController.Arg> {

    @Override
    protected String getSearchQueryInfo() {
        return arg.getSearchQueryInfo();
    }

    @Override
    protected String getSearchTemplateId() {
        return arg.getSearchTemplateId();
    }

    @Override
    protected String getSearchTemplateApiName() {
        return null;
    }

    @Override
    protected String getSearchTemplateType() {
        return arg.getSearchTemplateType();
    }

    @Override
    protected Map<String, Integer> getDescribeVersionMap() {
        return Collections.emptyMap();
    }

    @Override
    protected String getUsePageType() {
        return null;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected Boolean getFindExplicitTotalNum() {
        if (RequestUtil.isCepRequest()) {
            return Boolean.TRUE.equals(arg.getFindExplicitTotalNum());
        }
        return arg.getFindExplicitTotalNum();
    }

    @Override
    protected boolean needLayout(ISearchTemplateQuery query) {
        if (!arg.isIncludeLayout()) {
            return false;
        }
        return super.needLayout(query);
    }

    @Override
    protected boolean needDescribe(ISearchTemplateQuery query) {
        if (!arg.isIncludeDescribe()) {
            return false;
        }
        return super.needDescribe(query);
    }

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        super.beforeQueryData(query);
        IFilter createTimeFilter = new Filter();
        createTimeFilter.setFieldName(IObjectData.CREATE_TIME);
        int maxDayQueryLimit = AppFrameworkConfig.getAsyncTaskMaxDayQueryLimit(controllerContext.getTenantId());
        long createTime = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(maxDayQueryLimit);
        createTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(createTime)));
        createTimeFilter.setOperator(Operator.GTE);
        query.setFilters(Lists.newArrayList(createTimeFilter));
        OrderBy orderBy = new OrderBy(MtAsyncTaskMonitor.TASK_STATUS, true);
        List<String> valuesInOrder = Arrays.stream(TaskStatus.values())
                .sorted(Comparator.comparingInt(TaskStatus::getOrder))
                .map(TaskStatus::getValue)
                .collect(Collectors.toList());
        orderBy.setIsNullLast(true);
        orderBy.setOrderSequence(valuesInOrder);
        query.resetOrder(Lists.newArrayList(orderBy));
    }

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        QueryResult<IObjectData> queryResult = super.getQueryResult(query);
        InternalObjectDataProvider internalObjectDataProvider = serviceFacade.getBean(AsyncTaskMonitorObjectDataProvider.class);
        internalObjectDataProvider.fillObjectData(controllerContext.getUser(), objectDescribe, queryResult.getData());
        return queryResult;
    }

    @Override
    protected Result buildResult(List<ILayout> layouts, ISearchTemplateQuery query, QueryResult<IObjectData> queryResult) {
        Result result = super.buildResult(layouts, query, queryResult);
        if (!arg.includeDescribe) {
            result.setObjectDescribe(null);
        }
        if (!arg.includeLayout) {
            result.setListLayouts(null);
            result.setLayout(null);
        }
        return result;
    }

    @Data
    public static class Arg {
        @JsonProperty("object_describe_api_name")
        @SerializedName("object_describe_api_name")
        String objectDescribeApiName;
        @JsonProperty("search_template_id")
        @SerializedName("search_template_id")
        String searchTemplateId;
        @JsonProperty("search_template_type")
        @SerializedName("search_template_type")
        String searchTemplateType;
        @JsonProperty("find_explicit_total_num")
        @SerializedName("find_explicit_total_num")
        Boolean findExplicitTotalNum;
        @JsonProperty("include_describe")
        @SerializedName("include_describe")
        boolean includeDescribe = true;
        @JsonProperty("include_layout")
        @SerializedName("include_layout")
        boolean includeLayout = true;
        @JsonProperty("ignore_scene_record_type")
        @SerializedName("ignore_scene_record_type")
        boolean isIgnoreSceneRecordType;
        @JsonProperty("search_query_info")
        @SerializedName("search_query_info")
        String searchQueryInfo;
        // 是否将人员部门字段的扩展信息提取到外层
        private Boolean extractExtendInfo;
        // 是否序列化返回对象中的空值
        private Boolean serializeEmpty;

        public boolean serializeEmpty() {
            return !Boolean.FALSE.equals(serializeEmpty);
        }
    }

}

