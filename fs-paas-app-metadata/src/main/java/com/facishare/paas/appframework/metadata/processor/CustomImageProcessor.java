package com.facishare.paas.appframework.metadata.processor;

import com.facishare.paas.appframework.metadata.FileStoreService;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by zhouwr on 2017/8/24
 */
@Component
public class CustomImageProcessor extends AbstractFileFieldProcessor {

    @Resource
    private FileStoreService fileStoreService;

    @Override
    protected String getFinalFilePath(String tenantId, String userId, String tempFilePath, String fileName, String fileExt) {
        List<FileStoreService.PathPair> pathPairs = fileStoreService.saveImageFromTempFilesAndNames(tenantId, userId,
                Lists.newArrayList(FileStoreService.PathOriginNames.of(tempFilePath, fileName)), fileExt);
        return pathPairs.get(0).getFinalNPath();
//        return fileStoreService.saveImageFromTempFiles(tenantId, userId, Lists.newArrayList(tempFilePath), fileExt)
//                .get(0);
    }
}
