package com.facishare.paas.appframework.jaxrs.model;

import com.facishare.paas.appframework.core.rest.APIResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RestAPIResult {
    private static final int SUCCESS_CODE = 0;
    private static final String OK = "OK";

    private int code;
    private String message;
    private Object data;

    public static RestAPIResult success(Object result) {
        return RestAPIResult.builder().code(SUCCESS_CODE).message(OK).data(result).build();
    }

    public static RestAPIResult fail(int errCode, String errMessage) {
        return RestAPIResult.builder().code(errCode).message(errMessage).build();
    }

    public static RestAPIResult from(APIResult apiResult) {
        return RestAPIResult.builder()
                .code(apiResult.getCode())
                .message(apiResult.getMessage())
                .data(apiResult.getData())
                .build();
    }

    public static RestAPIResult from(InnerAPIResult innerAPIResult) {
        return RestAPIResult.builder()
                .code(innerAPIResult.getErrCode())
                .message(innerAPIResult.getErrMessage())
                .data(innerAPIResult.getResult())
                .build();
    }
}
