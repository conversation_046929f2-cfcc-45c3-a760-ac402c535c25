package com.facishare.paas.appframework.rest.dto.data;

import com.facishare.paas.appframework.common.service.dto.QueryUserInfoByIds;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface FindUserNameByIds {

    @Data
    @Builder
    class Arg {
        @SerializedName("idList")
        List<String> userIdList;
    }

    @Data
    class Result {
        List<UserInfo> userInfoList;
    }

}
