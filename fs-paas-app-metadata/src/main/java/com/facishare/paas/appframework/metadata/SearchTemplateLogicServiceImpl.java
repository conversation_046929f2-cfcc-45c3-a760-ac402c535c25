package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.CustomSceneService.AdjustOrderDTO;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.facishare.paas.appframework.metadata.config.ObjectConfigService;
import com.facishare.paas.appframework.metadata.config.ObjectFieldOrderConfig;
import com.facishare.paas.appframework.metadata.dto.scene.SceneDTO;
import com.facishare.paas.appframework.metadata.dto.scene.SceneExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.initscene.SceneInitManager;
import com.facishare.paas.appframework.metadata.scene.ValidatorUtil;
import com.facishare.paas.appframework.metadata.scene.validate.CustomSceneVD;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.SearchTemplateCode;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldListConfig;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.api.service.IDistributedLockService;
import com.facishare.paas.metadata.api.service.ISearchTemplateService;
import com.facishare.paas.metadata.common.MetadataContext;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.SearchTemplate;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.bizconf.arg.ConfigArg;
import com.fxiaoke.bizconf.bean.ValueType;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.facishare.crm.userdefobj.DefObjConstants.UDOBJ;
import static com.facishare.crm.userdefobj.DefObjConstants.invisibleFieldListFormObjectMap;
import static com.facishare.paas.appframework.metadata.CustomSceneConfig.getCustomSceneConfigKey;
import static com.facishare.paas.appframework.metadata.CustomSceneServiceImpl.OUT_USER_ALL_ID;
import static com.facishare.paas.appframework.metadata.CustomSceneServiceImpl.OUT_USER_IN_CHARGE_ID;
import static com.facishare.paas.appframework.metadata.SearchTemplateExt.DefaultScene.*;

/**
 * create by zhaoju on 2019/07/02
 */
@Slf4j
@Service
public class SearchTemplateLogicServiceImpl implements SearchTemplateLogicService {
    public static final String TEMPLATE_PRE = "TEMPLATE";

    @Autowired
    private ISearchTemplateService searchTemplateService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private SceneInitManager sceneInitManager;
    @Autowired
    private DataListHeaderConfigService dataListHeaderConfigService;
    @Autowired
    private LayoutLogicService layoutLogicService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private IDistributedLockService distributedLockService;
    @Autowired
    private RedissonService redissonService;
    @Autowired
    private OptionalFeaturesService optionalFeaturesService;
    @Autowired
    private ObjectConfigService objectConfigService;
    @Autowired
    private SearchTemplateLogicService searchTemplateLogicService;


    @Override
    public ISearchTemplate findByIdJoinTenant(String id, String describeApiName, String sceneType, User user) {
        ISearchTemplate searchTemplate;
        try {
            searchTemplate = findByIdAndType(id, describeApiName, sceneType, user);
        } catch (Exception e) {
            log.warn("findByIdJoinTenant error,user:{},describeApiName:{},id:{}", user, describeApiName, id, e);
            throw new MetaDataBusinessException(e.getMessage(), e);
        }

        if (Objects.isNull(searchTemplate)) {
            log.warn("templateId does not exist, templateId=>{},objectApiName=>{},user=>{}", id, describeApiName, user);
            throw new ValidateException(I18N.text(I18NKey.TEMPLATE_ID_NOT_EXIST));
        }
        SearchTemplateExt.of(searchTemplate).handleSearchTemplate();
        return searchTemplate;
    }

    private ISearchTemplate findByIdAndType(String id, String describeApiName, String sceneType, User user) {
        try {
            MetadataContext metadataContext = SceneExt.getOutTenantInfo(user).getMetadataContext();
            if (StringUtils.isNotBlank(sceneType)) {
                return findAndMerge(id, describeApiName, sceneType, metadataContext);
            }
            log.warn("sceneType is empty,id:{},apiName:{},user:{}", id, describeApiName, user);
            return findAndMergeNoSceneType(id, describeApiName, metadataContext);
        } catch (MetadataServiceException e) {
            log.warn("findByIdAndType error, templateId=>{},objectApiName=>{},user=>{}", id, describeApiName, user);
            throw new ValidateException(I18N.text(I18NKey.TEMPLATE_ID_NOT_EXIST));
        }
    }

    private ISearchTemplate findAndMergeNoSceneType(String id, String describeApiName, MetadataContext metadataContext) throws MetadataServiceException {
        // 调用元数据新方法1.先查系统 再查租户 再查个人
        ISearchTemplate searchTemplate = searchTemplateService.sequenceFindById(id, describeApiName, metadataContext);
        if (Objects.isNull(searchTemplate)) {
            log.warn("templateId does not exist, templateId=>{},objectApiName=>{},user=>{}", id, describeApiName, metadataContext.getUserId());
            throw new ValidateException(I18N.text(I18NKey.TEMPLATE_ID_NOT_EXIST));
        }
        // 通用merge
        return mergeBySceneType(searchTemplate, describeApiName, metadataContext);
    }

    @Override
    public ISearchTemplate findForTenantByApiName(User user, ISearchTemplate template, String describeApiName) {
        MetadataContext metadataContext = SceneExt.getOutTenantInfo(user).getMetadataContext();
        try {
            return searchTemplateService.findForTenantByApiName(user.getTenantId(), describeApiName,
                    template.getApiName(), template.getExtendAttribute(), metadataContext);
        } catch (MetadataServiceException e) {
            log.warn("findForTenantByApiName error, tenant=>{}, user=>{}, describeApiName=>{}, extendAttribute=>{}", user.getTenantId(), user.getUserId(), describeApiName, template.getExtendAttribute(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void wipeColumnWidth(User user, String objApiName, String extendAttribute) {
        dataListHeaderConfigService.wipeSettingsByObjApiNameAndUser(Sets.newHashSet(IFieldListConfig.FIELD_WIDTH), user, objApiName, extendAttribute);
    }

    private ISearchTemplate findAndMerge(String id, String describeApiName, String sceneType, MetadataContext context) throws MetadataServiceException {
        ISearchTemplate template = searchTemplateService.findByIdAndTypeNoMerge(id, sceneType, describeApiName, context);
        if (Objects.isNull(template)) {
            log.warn("not fount template by id and type, ei:{}, apiName{}, id:{}", context.getTenantId(), describeApiName, id);
            return searchTemplateService.findByIdAndType(id, sceneType, describeApiName, context);
        }
        return mergeBySceneType(template, describeApiName, context);
    }

    private ISearchTemplate mergeBySceneType(ISearchTemplate template, String describeApiName, MetadataContext context) throws MetadataServiceException {
        String sceneType = template.getType();
        if (SearchTemplateExt.DEFAULT_SCENE.equals(sceneType)) {
            ISearchTemplate byTenant = searchTemplateService.findForTenantByApiName(context.getTenantId(), describeApiName,
                    template.getApiName(), template.getExtendAttribute(), context);
            ISearchTemplate merged = merge(template, byTenant, "tenant");
            ISearchTemplate byCustom = searchTemplateService.findCustomTemplateByApiNameNoMerge(template.getApiName(),
                    context.getTenantId(), describeApiName, context.getUserId(), template.getExtendAttribute(), context);
            return merge(merged, byCustom, "user");
        }
        if (SearchTemplateExt.TENANT_SCENE.equals(sceneType)) {
            ISearchTemplate byCustom = searchTemplateService.findCustomTemplateByApiNameNoMerge(template.getApiName(),
                    context.getTenantId(), describeApiName, context.getUserId(), template.getExtendAttribute(), context);
            return merge(template, byCustom, "user");
        }
        return template;
    }

    private ISearchTemplate merge(ISearchTemplate tenantOrDefault, ISearchTemplate customOrTenant, String type) {
        if (Objects.isNull(customOrTenant)) {
            return tenantOrDefault;
        }
        SearchTemplate result = customOrTenant.copy();
        result.setId(tenantOrDefault.getId());
        result.setLabel(tenantOrDefault.getLabel());
        result.setRecordType(tenantOrDefault.getRecordType());
        result.setFilters(tenantOrDefault.getFilters());
        result.setWheres(tenantOrDefault.getWheres());
        result.setType(tenantOrDefault.getType());

        if ("user".equals(type)) {
            result.setOrders(tenantOrDefault.getOrders());
        }

        if (Objects.isNull(result.getFieldListType())) {
            result.setFieldListType(tenantOrDefault.getFieldListType());
        }
        if (Objects.isNull(result.getFieldList())) {
            result.setFieldList(tenantOrDefault.getFieldList());
        }
        if (Objects.isNull(result.getOrders())) {
            result.setOrders(tenantOrDefault.getOrders());
        }
        if (Objects.isNull(result.getDefaultScenePriority())) {
            result.setDefaultScenePriority(tenantOrDefault.getDefaultScenePriority());
        }
        if (Objects.isNull(result.getShowTag())) {
            result.setShowTag(tenantOrDefault.getShowTag());
        }
        return result;
    }

    @Override
    public ISearchTemplate findByIdWithHandelFieldList(String id, String describeApiName, String sceneType, User user) {
        ISearchTemplate searchTemplate = findByIdJoinTenant(id, describeApiName, sceneType, user);
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
        List<Map<String, Object>> fieldListConfig = dataListHeaderConfigService.findFieldListConfig(user, objectDescribe.getApiName(), searchTemplate.getExtendAttribute());
        List<LayoutRuleExt.FieldConfig> validFieldListConfig = fieldListConfig.stream()
                .map(LayoutRuleExt.FieldConfig::fromMap)
                .collect(Collectors.toList());

        List<String> fieldList = getFieldList(user, objectDescribe, RequestUtil.getAppId());
        Set<String> fieldSet = Sets.newLinkedHashSet(CollectionUtils.nullToEmpty(fieldList));
        handelSearchTemplate(objectDescribe, searchTemplate, fieldSet, validFieldListConfig);
        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), objectDescribe);
        if (!optionalFeaturesSwitch.getIsRelatedTeamEnabled()) {
            List<IFilter> filters = CollectionUtils.nullToEmpty(searchTemplate.getFilters());
            filters.removeIf(filter -> StringUtils.isNotEmpty(filter.getFieldName()) && filter.getFieldName().contains(ObjectDataExt.RELEVANT_TEAM));
            searchTemplate.setFilters(filters);
            if (StringUtils.equalsAny(searchTemplate.getBaseSceneApiName(), INVOLVED_WITH_ME.getApiName(), INVOLVED_WITH_SUB.getApiName())) {
                searchTemplate.setBaseSceneApiName(ALL.getApiName());
            }
        }
        return searchTemplate;
    }

    private List<String> getFieldList(User user, IObjectDescribe objectDescribe, String appId) {
        if (Utils.CRM_FEED_OBJ_API_NAME.equals(objectDescribe.getApiName())) {
            return Collections.emptyList();
        }
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(user, appId);
        ILayout layout = layoutLogicService.findObjectLayoutWithType(layoutContext, "", objectDescribe, ILayout.LIST_LAYOUT_TYPE, null);
        LayoutExt layoutExt = LayoutExt.of(layout);
        if (!ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            layoutExt.fillRelevantTeamField();
        }
        // 预置对象默认显示列顺序和字段权限中字段顺序一致
        List<String> orders = ObjectFieldOrderConfig.DEFAULT_ORDER.getFieldOrder(objectDescribe.getApiName());
        List<String> fieldList = layoutExt.getFieldList();
        ObjectDescribeExt.of(objectDescribe).addDateTimeRangeEndTime(fieldList);
        if (CollectionUtils.empty(orders)) {
            return fieldList;
        }
        return CollectionUtils.sortByGivenOrder(fieldList, orders, x -> x);
    }

    @Deprecated
    private ISearchTemplate getTemplateByOutId(String id, String describeApiName, User user) throws MetadataServiceException {
        Set<SearchTemplateCode> searchTemplateCodes = Sets.newHashSet();
        if (OUT_USER_ALL_ID.equals(id)) {
            searchTemplateCodes.add(SearchTemplateCode.ALL);
        } else if (OUT_USER_IN_CHARGE_ID.equals(id)) {
            searchTemplateCodes.add(SearchTemplateCode.IN_CHARGE);
        }
        List<ISearchTemplate> searchTemplates = searchTemplateService.findByObjectDescribeAPINameAndCode(user.getTenantId(), describeApiName, searchTemplateCodes);
        if (!CollectionUtils.empty(searchTemplates)) {
            return searchTemplates.get(0);
        }
        return null;
    }


    /**
     * 查询的结果包含排序，但不包含完整的field_list
     *
     * @param describeApiName
     * @param extendAttribute
     * @param user
     * @return
     */
    @Override
    public List<ISearchTemplate> findByDescribeApiNameAndExtendAttribute(String describeApiName, String extendAttribute, User user) {
       /* if (user.isOutUser() && !AppFrameworkConfig.isOutTemplateGrayTenant(user.getTenantId())) {
            return findOutSearchTemplate(describeApiName, user);
        }*/
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "#findByDescribeApiNameAndExtendAttribute");
        try {
            List<ISearchTemplate> templates = findAndInitSearchTemplate(describeApiName, extendAttribute, user);
            stopWatch.lap("findAndInitSearchTemplate");
            filterTemplate(describeApiName, user, templates);
            stopWatch.lap("filterTemplateByRelatedTeamSwitch");
            handleDefaultAndOrder(describeApiName, templates, user, extendAttribute);
            templates.forEach(searchTemplate -> SearchTemplateExt.of(searchTemplate).handleSearchTemplate());
            stopWatch.lap("handelSearchTemplate");
            filterTemplateByLicense(describeApiName, templates, user);
            stopWatch.lap("filterTemplateByLicense");
            return templates;
        } catch (MetadataServiceException e) {
            log.warn("findByDescribeApiNameAndExtendAttribute error, tenant=>{}, user=>{}, describeApiName=>{}, extendAttribute=>{}", user.getTenantId(), user.getUserId(), describeApiName, extendAttribute, e);
            throw new MetaDataBusinessException(e);
        } finally {
            stopWatch.logSlow(100);
        }
    }

    private void filterTemplate(String describeApiName, User user, List<ISearchTemplate> templates) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), describe);
        templates.removeIf(template -> !optionalFeaturesSwitch.getIsRelatedTeamEnabled() &&
                (StringUtils.equalsAny(template.getApiName(), INVOLVED_WITH_ME.getApiName(), INVOLVED_WITH_SUB.getApiName())));
        templates.forEach(template -> {
            List<Wheres> wheres = template.getWheres();
            wheres.forEach(where ->
                    CollectionUtils.nullToEmpty(where.getFilters()).
                            removeIf(filter -> !optionalFeaturesSwitch.getIsRelatedTeamEnabled() && filter.getFieldName().contains(ObjectDataExt.RELEVANT_TEAM))
            );
            template.setWheres(wheres);
        });
        if (describe.isBigObject()) {
            templates.removeIf(template -> StringUtils.equalsAny(template.getApiName(), SHARE_WITH_ME.getApiName(), FOLLOW.getApiName()));
        }
    }

    private void filterTemplateByLicense(String describeApiName, List<ISearchTemplate> templates, User user) {
        // 没有开通商机对象的企业，CRM 信息对象不需要下发 我负责商机的CRM信息 场景
        if (!Utils.CRM_FEED_OBJ_API_NAME.equals(describeApiName)) {
            return;
        }
        Set<String> describeApiNames = licenseService.queryAvailableObject(user.getTenantId());
        if (!describeApiNames.contains(Utils.OPPORTUNITY_API_NAME)) {
            templates.removeIf(x -> "in_charge_opportunity_crmfeed".equals(x.getApiName()));
        }
    }

    private void handleDefaultAndOrder(String describeApiName, List<ISearchTemplate> templates, User user, String extendAttribute) {
        CustomSceneConfig customSceneConfig = findCustomSceneConfig(user, getCustomSceneConfigKey(describeApiName, extendAttribute));
        try {
            ISearchTemplate sysDefaultScene = searchTemplateService.getDefaultSceneFromSysCache(describeApiName, extendAttribute,
                    SceneExt.getOutTenantInfo(user).getMetadataContext());
            String sysDefaultSceneApiName = Optional.ofNullable(sysDefaultScene).map(ISearchTemplate::getApiName).orElse(null);
            // 查询系统库中的默认场景
            SearchTemplateExt.handleTemplateConfig(templates, customSceneConfig, sysDefaultSceneApiName, user);
        } catch (MetadataServiceException e) {
            log.warn("handleDefaultAndOrder fail, apiName:{}, extendAttr:{}", describeApiName, extendAttribute, e);
        }
    }

    @Override
    @Deprecated
    public List<ISearchTemplate> findOutSearchTemplate(String describeApiName, User user) {
        List<ISearchTemplate> list;
        try {
            list = searchTemplateService.findByObjectDescribeAPINameAndCode(user.getTenantId(), describeApiName, Sets.newLinkedHashSet(
                    Lists.newArrayList(SearchTemplateCode.ALL, SearchTemplateCode.IN_CHARGE)));
        } catch (MetadataServiceException e) {
            log.warn("findOutSearchTemplate error,user:{},describeApiName:{}", user, describeApiName, e);
            throw new MetaDataBusinessException(e);
        }
        list.get(0).setId(OUT_USER_ALL_ID);
        list.get(1).setId(OUT_USER_IN_CHARGE_ID);
        return list;
    }

    @Override
    public List<ISearchTemplate> findOutSearchTemplate(String describeApiName, User user, String extendAttribute) {
        try {
            // 1.联合查询所有可见场景
            List<ISearchTemplate> templatesFromJoinTenant = querySearchTemplate(describeApiName, user, extendAttribute, true);
            // 按类型和创建时间排一下顺序
            return SearchTemplateExt.searchTemplatesOrderByTypeAndCreateTime(templatesFromJoinTenant);
        } catch (MetadataServiceException e) {
            log.warn("findOutSearchTemplate error, tenant=>{}, user=>{}, describeApiName=>{}, extendAttribute=>{}", user.getTenantId(), user.getUserId(), describeApiName, extendAttribute, e);
            throw new MetaDataBusinessException(e);
        }
    }

    /**
     * @param describeApiName
     * @param user
     * @param extendAttribute
     * @param isOuter         是否查询外部场景
     * @return
     * @throws MetadataServiceException
     */
    private List<ISearchTemplate> querySearchTemplate(String describeApiName, User user, String extendAttribute, boolean isOuter) throws MetadataServiceException {
        MetadataContext metadataContext = SceneExt.getOutTenantInfo(user, isOuter).getMetadataContext();
        return searchTemplateService.findForCustom(metadataContext, describeApiName, extendAttribute);
    }

    @Override
    public ISearchTemplate findDefaultOutTemplate(String describeApiName, User user) {
        try {
            List<ISearchTemplate> templates = querySearchTemplate(describeApiName, user, null, true);
            // 优先取 apiName 为 All 的场景
            return templates.stream()
                    .filter(template -> "All".equals(template.getApiName()))
                    .findFirst()
                    .orElseGet(() -> templates.stream()
                            .filter(template -> BooleanUtils.isTrue(template.getIsDefault()))
                            .findFirst()
                            .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.TEMPLATE_ID_NOT_EXIST))));
        } catch (MetadataServiceException e) {
            log.warn("findDefaultOutTemplate error, tenant=>{}, user=>{}, describeApiName=>{}", user.getTenantId(), user.getUserId(), describeApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<ISearchTemplate> findByDescribeApiNameWithHandelFieldList(String describeApiName, String extendAttribute, User user) {
        List<ISearchTemplate> searchTemplates = findByDescribeApiNameAndExtendAttribute(describeApiName, extendAttribute, user);
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
        List<Map<String, Object>> fieldListConfig = dataListHeaderConfigService.findFieldListConfig(user, objectDescribe.getApiName(), extendAttribute);
        List<LayoutRuleExt.FieldConfig> validFieldListConfig = fieldListConfig.stream()
                .map(LayoutRuleExt.FieldConfig::fromMap)
                .collect(Collectors.toList());

        List<String> fieldList = getFieldList(user, objectDescribe, RequestUtil.getAppId());
        return handelTemplateFieldList(searchTemplates, objectDescribe, fieldList, validFieldListConfig);
    }

    public List<ISearchTemplate> handelTemplateFieldList(List<ISearchTemplate> searchTemplates, IObjectDescribe objectDescribe,
                                                         List<String> fieldList, List<LayoutRuleExt.FieldConfig> validFieldListConfig) {
        if (CollectionUtils.empty(searchTemplates)) {
            return Lists.newArrayList();
        }
        Set<String> fieldSet = Sets.newLinkedHashSet(CollectionUtils.nullToEmpty(fieldList));
        return searchTemplates.stream()
                .peek(searchTemplate -> handelSearchTemplate(objectDescribe, searchTemplate, fieldSet, validFieldListConfig))
                .collect(Collectors.toList());
    }

    private void handelSearchTemplate(IObjectDescribe objectDescribe, ISearchTemplate searchTemplate, Set<String> fieldList, List<LayoutRuleExt.FieldConfig> validFieldListConfig) {
        SearchTemplateExt.of(searchTemplate).handleFiltersField(objectDescribe);
//        SearchTemplateExt.of(searchTemplate).initDefaultSceneFieldList(validFieldListConfig);
        SearchTemplateExt.of(searchTemplate).handleHeadFieldList(fieldList, validFieldListConfig, objectDescribe);
    }

    /**
     * 根据查询的结果，将只出现在tenant表中的insert到custom中
     *
     * @param describeApiName
     * @param extendAttribute
     * @param user
     * @return
     * @throws MetadataServiceException
     */
    private List<ISearchTemplate> findAndInitSearchTemplate(String describeApiName, String extendAttribute, User user) throws MetadataServiceException {
        // 1.联合查询所有可见场景
        List<ISearchTemplate> templatesFromJoinTenant = querySearchTemplate(describeApiName, user, extendAttribute, user.isOutUser());
        // 按类型和创建时间排一下顺序
        return SearchTemplateExt.searchTemplatesOrderByTypeAndCreateTime(templatesFromJoinTenant);
    }

    @Override
    public ISearchTemplate setDefaultScene(ISearchTemplate searchTemplate, User user) {
        String extendAttribute = searchTemplate.getExtendAttribute();
        if (StringUtils.contains(extendAttribute, ",")) {
            List<String> extendAttributes = Splitter.on(",").splitToList(extendAttribute);
            setDefaultSceneByPoolObject(searchTemplate.getObjectDescribeApiName(), extendAttributes, searchTemplate.getId(), user);
            return searchTemplate;
        }
        String customSceneConfigKey = getCustomSceneConfigKey(searchTemplate.getObjectDescribeApiName(), extendAttribute);
        // 用分布式锁，避免并发修改导致数据覆盖
        RLock rLock = redissonService.tryFairLock(10, 3, TimeUnit.SECONDS, customSceneConfigKey);
        try {
            CustomSceneConfig customSceneConfig = findCustomSceneConfig(user, customSceneConfigKey);
            if (Objects.nonNull(customSceneConfig)) {
                log.debug("setDefaultScene, updateConfig,ei:{},user:{},key:{},json:{}", user.getTenantId(),
                        user.getUserId(), customSceneConfigKey, searchTemplate.toJsonString());
                customSceneConfig.setDefaultScene(searchTemplate.getId());
                configService.upsertUserConfig(user, customSceneConfigKey, customSceneConfig.toJsonString(), ConfigValueType.JSON);
                return searchTemplate;
            }
            log.debug("setDefaultScene, createConfig,ei:{},user:{},key:{},json:{}", user.getTenantId(),
                    user.getUserId(), customSceneConfigKey, searchTemplate.toJsonString());
            CustomSceneConfig sceneConfig = CustomSceneConfig.buildByDefault(searchTemplate.getId());
            configService.createUserConfig(user, customSceneConfigKey, sceneConfig.toJsonString(), ConfigValueType.JSON);
            return searchTemplate;
        } finally {
            redissonService.unlock(rLock);
        }
    }

    /**
     * 对于线索池和公海对象,设置默认场景的时候,extendAttribute 会有多个值的情况,但查询场景的时候会指定其中一个extendAttribute
     * <p>
     * 处理方案
     * 1,设置默认场景时将多个 extendAttribute 拆开,保存多个配置信息
     * 2,查询场景的时候,根据指定的 extendAttribute 查询
     *
     * @param objectDescribeApiName
     * @param extendAttributes
     * @param defaultTemplateId
     * @param user
     */
    private void setDefaultSceneByPoolObject(String objectDescribeApiName, List<String> extendAttributes, String defaultTemplateId, User user) {
        String extend = String.join(",", Sets.newTreeSet(extendAttributes));
        String lockKey = getCustomSceneConfigKey(objectDescribeApiName, extend);
        RLock rLock = redissonService.tryFairLock(10, 3, TimeUnit.SECONDS, lockKey);
        try {
            Set<String> customSceneConfigKeys = CustomSceneConfig.getCustomSceneConfigKeys(objectDescribeApiName, extendAttributes);
            Map<String, CustomSceneConfig> sceneConfigMap = findCustomSceneConfigs(user, customSceneConfigKeys);

            List<ConfigArg> configArgs = Lists.newArrayList();
            sceneConfigMap.forEach((key, sceneConfig) -> {
                sceneConfig.setDefaultScene(defaultTemplateId);
                ConfigArg configArg = createConfigArg(user, key, sceneConfig);
                configArgs.add(configArg);
            });

            customSceneConfigKeys.removeAll(sceneConfigMap.keySet());
            customSceneConfigKeys.forEach(key -> {
                CustomSceneConfig sceneConfig = CustomSceneConfig.buildByDefault(defaultTemplateId);
                ConfigArg configArg = createConfigArg(user, key, sceneConfig);
                configArgs.add(configArg);
            });
            configService.batchUpsertUserConfig(user, configArgs);
        } finally {
            redissonService.unlock(rLock);
        }

    }

    private void cancelDefaultScene(ISearchTemplate searchTemplate, User user) {
        String extendAttribute = searchTemplate.getExtendAttribute();
        if (StringUtils.contains(extendAttribute, ",")) {
            List<String> extendAttributes = Splitter.on(",").splitToList(extendAttribute);
            cancelDefaultSceneByPoolObject(searchTemplate.getObjectDescribeApiName(), extendAttributes, searchTemplate.getId(), user);
            return;
        }
        String customSceneConfigKey = getCustomSceneConfigKey(searchTemplate.getObjectDescribeApiName(), extendAttribute);
        // 用分布式锁，避免并发修改导致数据覆盖
        RLock rLock = redissonService.tryFairLock(10, 3, TimeUnit.SECONDS, customSceneConfigKey);
        try {
            CustomSceneConfig customSceneConfig = findCustomSceneConfig(user, customSceneConfigKey);
            if (Objects.isNull(customSceneConfig) || !Objects.equals(searchTemplate.getId(), customSceneConfig.getDefaultScene())) {
                return;
            }
            customSceneConfig.setDefaultScene(null);
            configService.upsertUserConfig(user, customSceneConfigKey, customSceneConfig.toJsonString(), ConfigValueType.JSON);
        } finally {
            redissonService.unlock(rLock);
        }
    }

    private void cancelDefaultSceneByPoolObject(String objectDescribeApiName, List<String> extendAttributes, String defaultTemplateId, User user) {
        String extend = String.join(",", Sets.newTreeSet(extendAttributes));
        String lockKey = getCustomSceneConfigKey(objectDescribeApiName, extend);
        RLock rLock = redissonService.tryFairLock(10, 3, TimeUnit.SECONDS, lockKey);
        try {
            Set<String> customSceneConfigKeys = CustomSceneConfig.getCustomSceneConfigKeys(objectDescribeApiName, extendAttributes);
            Map<String, CustomSceneConfig> sceneConfigMap = findCustomSceneConfigs(user, customSceneConfigKeys);

            List<ConfigArg> configArgs = Lists.newArrayList();
            sceneConfigMap.forEach((key, sceneConfig) -> {
                if (!Objects.equals(defaultTemplateId, sceneConfig.getDefaultScene())) {
                    return;
                }
                sceneConfig.setDefaultScene(null);
                ConfigArg configArg = createConfigArg(user, key, sceneConfig);
                configArgs.add(configArg);
            });
            configService.batchUpsertUserConfig(user, configArgs);
        } finally {
            redissonService.unlock(rLock);
        }

    }

    private ConfigArg createConfigArg(User user, String key, CustomSceneConfig sceneConf) {
        return ConfigArg.builder()
                .tenantId(user.getTenantId())
                .key(key)
                .value(sceneConf.toJsonString())
                .valueType(ValueType.JSON)
                .build();
    }

    @Override
    public CustomSceneConfig getCustomSceneConfig(User user, String describeApiName, String extendAttribute) {
        String customSceneConfigKey = getCustomSceneConfigKey(describeApiName, extendAttribute);
        return findCustomSceneConfig(user, customSceneConfigKey);
    }

    private CustomSceneConfig findCustomSceneConfig(User user, String customSceneConfigKey) {
        Pair<Boolean, Optional<CustomSceneConfig>> customSceneConfigCache = ContextCacheUtil.getUserSceneConfigCache(user.getUserId(), customSceneConfigKey);
        if (Boolean.TRUE.equals(customSceneConfigCache.getKey())) {
            return customSceneConfigCache.getValue().orElse(null);
        }
        CustomSceneConfig customSceneConfig = null;
        String configValue = configService.findUserConfig(user, customSceneConfigKey);
        if (!Strings.isNullOrEmpty(configValue)) {
            customSceneConfig = CustomSceneConfig.fromJson(configValue);
        }
        ContextCacheUtil.cacheUserSceneConfig(user.getUserId(), customSceneConfigKey, customSceneConfig);
        return customSceneConfig;
    }

    private Map<String, CustomSceneConfig> findCustomSceneConfigs(User user, Collection<String> customSceneConfigKeys) {
        Map<String, String> queryUserConfigs = configService.queryUserConfigs(user, customSceneConfigKeys);
        Map<String, CustomSceneConfig> result = Maps.newHashMap();
        queryUserConfigs.forEach((key, configValue) -> {
            CustomSceneConfig customSceneConfig = CustomSceneConfig.fromJson(configValue);
            if (Objects.nonNull(customSceneConfig)) {
                result.put(key, customSceneConfig);
            }
        });
        return result;
    }

    @Override
    public ISearchTemplate createFieldListConfig(String describeApiName, String sceneId, String sceneType,
                                                 String extendAttribute, List<IHeadField> headFields, User user) {
        StopWatch stopWatch = StopWatch.create(getClass() + "#createFieldListConfig");
        RLock rLock = null;
        try {
            MetadataContext context = SceneExt.getOutTenantInfo(user).getMetadataContext();
            ISearchTemplate template = searchTemplateService.findByIdAndTypeNoMerge(sceneId, sceneType, describeApiName, context);
            stopWatch.lap("findByIdAndTypeNoMerge");
            if (Objects.isNull(template)) {
                log.warn("template not found ei:{}, describeApiName:{}, id:{}, extendAttribute:{}, userId:{}",
                        user.getTenantId(), describeApiName, sceneId, extendAttribute, user.getUserId());
                return null;
            }
            if (SearchTemplateExt.of(template).isCustomScene()) {
                ISearchTemplate result = doUpdate(headFields, template, context);
                stopWatch.lap("doUpdate");
                return result;
            }

            // 使用数据库的分布式锁，避免重复创建
            String templateKey = String.format("%s_%s_%s", TEMPLATE_PRE, describeApiName, user.getUserIdOrOutUserIdIfOutUser());
            rLock = redissonService.tryLock(user, describeApiName, templateKey);

            ISearchTemplate simpleTemplate = searchTemplateService.findCustomTemplateByApiNameNoMerge(template.getApiName(), user.getTenantId(),
                    describeApiName, user.getUserId(), extendAttribute, context);
            stopWatch.lap("findCustomTemplateByApiNameNoMerge");
            if (Objects.isNull(simpleTemplate)) {
                ISearchTemplate searchTemplate = SearchTemplateExt.of(template).copyToCustom(user);
                SearchTemplateExt.of(searchTemplate).setHeadField(headFields);
                ISearchTemplate result = searchTemplateService.create(searchTemplate, context);
                stopWatch.lap("create");
                return result;
            }

            ISearchTemplate result = doUpdate(headFields, simpleTemplate, context);
            stopWatch.lap("doUpdate");
            return result;
        } catch (MetadataServiceException e) {
            log.warn("createFieldListConfig error, ei:{}, apiName:{}, sceneId:{}", user.getTenantId(), describeApiName, sceneId, e);
            throw new MetaDataBusinessException(e);
        } finally {
            redissonService.unlock(rLock);
            stopWatch.logSlow(5000);
        }
    }

    private ISearchTemplate doUpdate(List<IHeadField> headFields, ISearchTemplate template, MetadataContext context) throws MetadataServiceException {
        SearchTemplateExt.of(template).setHeadField(headFields);
        return searchTemplateService.update(template, context);
    }

    @Override
    public void createOrUpdateFieldWidthConfig(String describeApiName, String extendAttribute, List<IHeadField> headFields, User user) {
        List<Tuple<String, Number>> fieldWidthConfig = dataListHeaderConfigService.findFieldWidthConfig(user, describeApiName, extendAttribute);

        Map<String, Number> fieldWidthMap = Maps.newLinkedHashMap();
        fieldWidthConfig.forEach(tuple -> fieldWidthMap.putIfAbsent(tuple.getKey(), tuple.getValue()));

        // 处理增量更新
        headFields.forEach(headField -> fieldWidthMap.compute(headField.getFieldName(), (key, oldValue) -> {
            if (Objects.isNull(headField.getWidth())) {
                return oldValue;
            }
            return headField.getWidth();
        }));

        List<Map<String, Object>> fieldWidth = fieldWidthMap.entrySet().stream()
                .filter(entry -> Objects.nonNull(entry.getKey()))
                .filter(entry -> Objects.nonNull(entry.getValue()))
                .map(entry -> {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put(entry.getKey(), entry.getValue());
                    return map;
                })
                .collect(Collectors.toList());
        dataListHeaderConfigService.createOrUpdateFieldWidthConfig(user, describeApiName, fieldWidth, extendAttribute, null);
    }

    @Override
    public void adjustOrder(List<AdjustOrderDTO> adjustOrders, String describeApiName, User user) {
        String extendAttribute = getExtendAttribute(adjustOrders, describeApiName, user);
        String configKey = getCustomSceneConfigKey(describeApiName, extendAttribute);
        // 用分布式锁，避免并发修改导致数据覆盖
        RLock rLock = redissonService.tryFairLock(10, 3, TimeUnit.SECONDS, configKey);
        try {
            CustomSceneConfig customSceneConfig = findCustomSceneConfig(user, configKey);
            if (Objects.nonNull(customSceneConfig)) {
                handleCustomSceneConfig(adjustOrders, customSceneConfig);
                configService.updateUserConfig(user, configKey, customSceneConfig.toJsonString(), ConfigValueType.STRING);
                return;
            }
            configService.createUserConfig(user, configKey, handleCustomSceneConfig(adjustOrders, null).toJsonString(), ConfigValueType.STRING);
        } finally {
            redissonService.unlock(rLock);
        }
    }

    private String getExtendAttribute(List<AdjustOrderDTO> adjustOrders, String describeApiName, User user) {
        AdjustOrderDTO adjustOrderDTO = adjustOrders.stream()
                .filter(x -> SearchTemplateExt.DEFAULT_SCENE.equals(x.getType()))
                .findFirst()
                .orElseGet(() -> {
                    log.warn("adjustOrder error, not fount searchTemplate. ei:{}, adjustOrders:{}, describeApiName:{}, user:{}",
                            user.getTenantId(), JSON.toJSONString(adjustOrders), describeApiName, user);
                    return adjustOrders.stream().findFirst().orElse(null);
                });
        if (adjustOrderDTO == null) {
            return null;
        }
        ISearchTemplate template = findByIdAndType(adjustOrderDTO.get_id(), describeApiName, adjustOrderDTO.getType(), user);
        return template.getExtendAttribute();
    }

    private CustomSceneConfig handleCustomSceneConfig(List<AdjustOrderDTO> adjustOrders, CustomSceneConfig customSceneConfig) {
        List<String> orderBy = Lists.newArrayList();
        List<String> hiddenScene = Lists.newArrayList();
        for (AdjustOrderDTO adjustOrder : adjustOrders) {
            orderBy.add(adjustOrder.get_id());
            if (Boolean.TRUE.equals(adjustOrder.getIs_hidden())) {
                hiddenScene.add(adjustOrder.get_id());
            }
        }

        if (Objects.isNull(customSceneConfig)) {
            return CustomSceneConfig.builder().hiddenScene(hiddenScene).orderBy(orderBy).build();
        }
        customSceneConfig.setHiddenScene(hiddenScene);
        customSceneConfig.setOrderBy(orderBy);
        return customSceneConfig;
    }

    @Override
    public ISearchTemplate create(ISearchTemplate template, User user) {
        // 校验场景是否合法
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), template.getObjectDescribeApiName());
        CustomSceneVD customSceneVD = CustomSceneVD.fromCreate(SceneDTO.fromSearchTemplate(template, describe), describe);
        if (!Objects.equals(Utils.CRM_FEED_OBJ_API_NAME, describe.getApiName())) {
            ValidatorUtil.validate(customSceneVD);
        }
        checkTemplateName(user, template);
        try {
            SearchTemplateExt.of(template).removeHiddenHeadField();
            ISearchTemplate searchTemplate = searchTemplateService.create(template, SceneExt.getOutTenantInfo(user).getMetadataContext());
            resetDefault(user, searchTemplate, template.getObjectDescribeApiName());
            return searchTemplate;
        } catch (MetadataServiceException e) {
            log.warn("create error,user:{},json:{}", user, template.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public ISearchTemplate update(ISearchTemplate template, User user) {
        ISearchTemplate searchTemplate = diffSearchTemplate(template, user);
        // 校验场景是否合法
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), searchTemplate.getObjectDescribeApiName());
        CustomSceneVD customSceneVD = CustomSceneVD.fromUpdate(SceneDTO.fromSearchTemplate(searchTemplate, describe), describe);
        ValidatorUtil.validate(customSceneVD);
        checkTemplateName(user, template);
        try {
            SearchTemplateExt.of(template).removeHiddenHeadField();
            resetDefault(user, searchTemplate, searchTemplate.getObjectDescribeApiName());
            return searchTemplateService.update(searchTemplate, SceneExt.getOutTenantInfo(user).getMetadataContext());
        } catch (MetadataServiceException e) {
            log.warn("update error,user:{},json:{}", user, searchTemplate.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    /**
     * 校验场景名称是否重复
     *
     * @param user
     * @param template
     */
    private void checkTemplateName(User user, ISearchTemplate template) {
        String label = template.getLabel();
        List<ISearchTemplate> templates = findByDescribeApiNameAndExtendAttribute(template.getObjectDescribeApiName(), template.getExtendAttribute(), user);
        for (ISearchTemplate searchTemplate : templates) {
            if (Objects.equals(label, searchTemplate.getLabel()) && !Objects.equals(searchTemplate.getId(), template.getId())) {
                throw new ValidateException(I18NExt.text(I18NKey.DUPLICATE_SCENE_NAME));
            }
        }
    }

    @Override
    public ISearchTemplate updateIsNewScene(String id, String describeApiName, User user) {
        try {
            ISearchTemplate searchTemplate = searchTemplateService.findById(id, describeApiName, user.getTenantId());
            if (Objects.isNull(searchTemplate) || !SearchTemplateExt.of(searchTemplate).isTenantScene()) {
                return null;
            }
            if (Boolean.FALSE.equals(searchTemplate.getIsNewScene())) {
                return null;
            }
            searchTemplate.setIsNewScene(false);
            return searchTemplateService.update(searchTemplate, SceneExt.getOutTenantInfo(user).getMetadataContext());
        } catch (MetadataServiceException e) {
            log.warn("updateIsNewScene error,ei:{},id:{},describeApiName:{},user:{}", user.getTenantId(), id, describeApiName, user, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<String> getAuthorizedFields(IObjectDescribe describe, User user) {
        Set<String> invisibleField = removeInvisibleField(describe, user);
        List<String> result = ObjectDescribeExt.of(describe).stream()
                .map(IFieldDescribe::getApiName)
                .filter(fieldApiName -> !invisibleField.contains(fieldApiName))
                .collect(Collectors.toList());
        return result;
    }

    private Set<String> removeInvisibleField(IObjectDescribe describe, User user) {
        Set<String> result = Sets.newHashSet();
        result.addAll(invisibleFieldListFormObjectMap.getOrDefault(UDOBJ, Collections.emptySet()));
        result.addAll(invisibleFieldListFormObjectMap.getOrDefault(ObjectDescribeExt.of(describe).getApiName(), Collections.emptySet()));
        result.addAll(functionPrivilegeService.getUnauthorizedFields(user, describe.getApiName()));
        return result;
    }

    private void resetDefault(User user, ISearchTemplate searchTemplate, String objectDescribeApiName) throws MetadataServiceException {
        if (!Boolean.TRUE.equals(searchTemplate.getIsDefault())) {
            cancelDefaultScene(searchTemplate, user);
            return;
        }
        setDefaultScene(searchTemplate, user);
        searchTemplate.setIsDefault(false);
    }

    private ISearchTemplate diffSearchTemplate(ISearchTemplate searchTemplate, User user) {
        ISearchTemplate templateInDb = findByIdJoinTenant(searchTemplate.getId(),
                searchTemplate.getObjectDescribeApiName(),
                searchTemplate.getType(), user);
        SearchTemplateExt.of(searchTemplate).handleFilterToWhere();
        return SearchTemplateExt.of(templateInDb).diff(searchTemplate);
    }
}
