package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.plugin.APLControllerPlugin;
import com.facishare.paas.appframework.core.model.plugin.Plugin;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController.Arg;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;

public class AbstractStandardWebDetailController<A extends Arg> extends AbstractStandardDetailController<A> {

    @Override
    protected ILayout getLayout() {
        ILayout layout;
        if (!Strings.isNullOrEmpty(arg.getLayoutApiName())) {
            layout = serviceFacade.getLayoutLogicService().getLayoutByApiNameWithComponents(controllerContext.getUser(), arg.getLayoutApiName(),
                    describe, data, relatedObjects, detailObjects, !needButton());
            return layout;
        }
        if (needButton()) {
            layout = serviceFacade.getLayoutLogicService().getLayoutWithComponents(buildLayoutContext(), data.getRecordType(),
                    describe, data, relatedObjects, detailObjects, getUnauthorizedFields(), PageType.WebDetail);
        } else {
            layout = serviceFacade.getLayoutLogicService().getLayoutWithComponents(buildLayoutContext(), data.getRecordType(),
                    describe, data, relatedObjects, detailObjects, getUnauthorizedFields(), PageType.WebDetail, arg.isFromRecycleBin(), true);
        }
        if (Objects.nonNull(layout)) {
            stopWatch.lap(String.format("getLayoutWithComponents;id:%s,api:%s,v:%s",
                    layout.getId(), layout.get(Layout.NAME), layout.getVersion()));
        }
        else {
            stopWatch.lap("getLayoutWithComponents");
        }

        //回收站详情页不展示自定义按钮
        if (arg.isFromRecycleBin()) {
            return layout;
        }
        if (defaultEnableQixinGroup) {
            boolean hiddenQiXinEntrance = infraServiceFacade.isHiddenQiXinEntrance(controllerContext.getUser());
            if (!hiddenQiXinEntrance) {
                LayoutExt.of(layout).getComponentByApiName(ComponentExt.TEAM_COMPONENT_NAME).ifPresent(y ->
                        ComponentExt.of(y).addButton(ButtonExt.generateQinxinGroupButton()));
            }
        }
        stopWatch.lap("generateQixinGroupButton");

        return layout;
    }

    private boolean needButton() {
        return !AppFrameworkConfig.unSupportButtonInternalObject(controllerContext.getObjectApiName());
    }

    @Override
    protected List<IObjectDescribe> findRelatedObjects() {
        List<IFieldDescribe> relatedFields = serviceFacade.findRelatedFields(controllerContext.getTenantId(),
                arg.getObjectDescribeApiName());
        if (CollectionUtils.notEmpty(relatedFields)) {
            Collections.sort(relatedFields, Comparator.comparingLong(o -> Optional.ofNullable(o.getCreateTime()).orElse(0L)));
        }
        return relatedFields.stream().map(this::buildSimpleDescribe).collect(Collectors.toList());
    }

    @Override
    protected void initRequestContext() {
        RequestContext context = RequestContextManager.getContext();
        context.setApiResource(controllerContext.getMethodName());
    }

    private IObjectDescribe buildSimpleDescribe(IFieldDescribe field) {
        IObjectDescribe describe = new ObjectDescribe();
        describe.setApiName(field.getDescribeApiName());
        describe.setFieldDescribes(Lists.newArrayList(field));
        describe.setTenantId(controllerContext.getTenantId());
        return describe;
    }

    @Override
    protected Plugin.Arg buildAPLPluginArg(String method) {
        APLControllerPlugin.TriggerInfo triggerInfo = buildTriggerInfo();
        AbstractStandardDetailController.Arg controllerArg = Objects.isNull(arg) ? null : arg.copy2Plugin();
        AbstractStandardDetailController.Result controllerResult = Objects.isNull(result) ? null : result.copy2Plugin();
        return new APLControllerPlugin.Arg(controllerContext.getObjectApiName(), controllerArg, controllerResult, triggerInfo);
    }

    @Override
    protected void mergeAPLPluginResult(APLControllerPlugin.Result aplPluginResult) {
        Result controllerResult = aplPluginResult.getControllerResult(Result.class);
        Optional.ofNullable(controllerResult)
                .map(Result::getData)
                .ifPresent(result::setData);
    }

}
