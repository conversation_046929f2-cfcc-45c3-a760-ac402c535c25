package com.facishare.paas.appframework.core.predef.action;

import com.facishare.organization.adapter.api.model.organizationwithouter.OrganizationEmployee;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.OutInfoChangeModel;
import com.facishare.paas.appframework.core.predef.service.PartnerRemindOutUserService;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.LogExtendInfo;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.enterpriserelation2.data.PublicEmployeeObjOwnDepOrOrgData;
import com.fxiaoke.enterpriserelation2.result.OuterAccountVo;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class StandardChangePartnerOwnerAction extends BaseUpdatePartnerAction<StandardChangePartnerOwnerAction.Arg, BaseUpdatePartnerAction.Result> {
    private static final int PAGE_SIZE = 1000;

    protected Result result = Result.builder().errorCode("0").message(I18N.text(I18NKey.CHANGE_PARTNER_OWNER_SUCCESS)).build();

    protected Tuple<Integer, Long> newOutInfo;
    protected PartnerRemindOutUserService partnerRemindOutUserService = SpringUtil.getContext().getBean(PartnerRemindOutUserService.class);
    private Boolean relatedTeamEnabled = Boolean.TRUE;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(StandardAction.ChangePartnerOwner.getFunPrivilegeCodes());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return arg.getDataIds();
    }

    public List<OutInfoChangeModel> outInfoChangeModelList = Lists.newArrayList();

    @Override
    protected void before(Arg arg) {
        log.info("StandardChangePartnerOwnerAction before arg {}", arg);
        super.before(arg);
        relatedTeamEnabled = infraServiceFacade.findOptionalFeaturesSwitch(actionContext.getTenantId(), objectDescribe).getIsRelatedTeamEnabled();

        validateArg(arg);
        initData(arg);
        objectDataList.forEach(data -> {
            validateData(data);
            OutInfoChangeModel model = OutInfoChangeModel
                    .builder()
                    .dataName(data.getName())
                    .dataId(data.getId())
                    .isPreDefineObj("package".equals(objectDescribe.getDefineType()))
                    .build();
            model.setOldOutEI(StringUtils.isBlank(data.getOutTenantId()) ? 0 : Integer.parseInt(data.getOutTenantId()));
            long outUserId = 0L;
            if (!data.getOutOwner().isEmpty()) {
                outUserId = StringUtils.isBlank(data.getOutOwner().get(0).toString()) ? 0L : Long.parseLong(data.getOutOwner().get(0).toString());
            }
            model.setOldOutUserId((int) outUserId);
            outInfoChangeModelList.add(model);
        });
    }

    private void validateData(IObjectData k) {
        ObjectDataExt objectExt = ObjectDataExt.of(k);
        if (objectExt.isLock()) {
            throw new ValidateException(objectExt.getName() + I18N.text(I18NKey.DATA_LOCKED));
        }
    }

    private void initData(Arg arg) {
        objectDataList = this.findByIdsIncludeLookUpName(this.actionContext.getUser(), this.objectDescribe.getApiName(), arg.getDataIds());
        if (CollectionUtils.isEmpty(objectDataList)) {
            throw new ValidateException(I18N.text(I18NKey.OBJECT_DATA_NOT_FOUND));
        }
    }

    private void validateArg(Arg arg) {
        // 从对象不支持更换单独外部负责人
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            throw new ValidateException(I18N.text(I18NKey.CAN_NOT_ONLY_CHANGE_DETAIL_OBJECT_OUT_OWNER));
        }

        if (CollectionUtils.isEmpty(arg.getDataIds())) {
            throw new ValidateException(I18N.text(I18NKey.DATA_ID_EMPTY));
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        if (!result.isSuccess()) {
            return result;
        }
        if (needTriggerApprovalFlow()) {
            Map<String, Object> data = Maps.newHashMap();
            Map<String, Map<String, Object>> dataMap = Maps.newHashMap();
            Map<String, Object> callbackData = Maps.newHashMap();
            Map<String, Map<String, Object>> callbackDataMap = Maps.newHashMap();
            dataList.forEach(x -> {
                List<String> outOwner = Optional.ofNullable(arg.getOwnerId()).map(Lists::newArrayList).orElseGet(Lists::newArrayList);
                data.put("out_owner", outOwner);
                dataMap.put(x.getId(), data);
                callbackData.put("ownerId", arg.getOwnerId());
                callbackData.put("outTenantId", arg.getOutTenantId());
                callbackData.put("oldOwnerStrategy", arg.getOldOwnerStrategy());
                callbackData.put("oldOwnerTeamMemberPermissionType", arg.getOldOwnerTeamMemberPermissionType());
                callbackData.put("isUpdateOutDataOwnDepartment", arg.getIsUpdateOutDataOwnDepartment());
                callbackData.put("isUpdateOutDataOwnOrganization", arg.getIsUpdateOutDataOwnOrganization());
                //将原状态放入callbackData，用于审批通过和驳回之后恢复原状态
                callbackData.put(ObjectLifeStatus.LAST_LIFE_STATUS_API_NAME, ObjectDataExt.of(x).getLifeStatusText());
                callbackDataMap.put(x.getId(), callbackData);
            });
            if (dataList.size() > 1) {
                startApprovalFlowAsynchronous(dataList, ApprovalFlowTriggerType.CHANGE_PARTNER_OWNER, dataMap, callbackDataMap);
                return Result.builder().errorCode("0").message(I18N.text(I18NKey.CHANGE_PARTNER_OWNER_SUCCESS)).build();
            } else {
                startApprovalFlow(dataList, ApprovalFlowTriggerType.CHANGE_PARTNER_OWNER, dataMap, callbackDataMap, null);
                if (isApprovalFlowStartSuccess(dataList.get(0).getId())) {
                    return Result.builder().errorCode("0").message(I18N.text(I18NKey.CHANGE_PARTNER_OWNER_SUCCESS)).build();
                }
            }
        }

        //更换外部负责人id
        changeOutOwner();
        outInfoChangeModelList.forEach(model -> {
            model.setNewOutEI(newOutInfo.getKey());
            model.setNewOutUserId(newOutInfo.getValue().intValue());
            model.setDisplayName(objectDescribe.getDisplayName());
            model.setIsPreDefineObj("package".equals(objectDescribe.getDefineType()));
        });
        return result;
    }

    private void changeOutOwner() {
        String outOwnerId = arg.getOwnerId();
        bulkChangeOutOwner(dataList, arg.getOutTenantId(), outOwnerId, arg.getOldOwnerStrategy(), arg.getOldOwnerTeamMemberPermissionType(), true);
    }

    @Override
    protected void dealDetail(List<IObjectData> masterDataList, String partnerId, Integer outTenantId, Long outOwnerId) {
        if (CollectionUtils.isEmpty(masterDataList)) {
            return;
        }
        List<String> masterDataIds = masterDataList.stream().map(IObjectData::getId).collect(Collectors.toList());
        List<IObjectDescribe> detailDescribes = serviceFacade.findDetailDescribes(actionContext.getTenantId(), objectDescribe.getApiName());

        for (IObjectDescribe detailDescribe : detailDescribes) {
            if (AppFrameworkConfig.isChangeOwnerDetailAsync(actionContext.getTenantId(), detailDescribe.getApiName())) {
                ParallelUtils.createParallelTask().submit(() -> doDetailChangeOutOwner(outTenantId, outOwnerId, detailDescribe, masterDataIds)).run();
            } else {
                doDetailChangeOutOwner(outTenantId, outOwnerId, detailDescribe, masterDataIds);
            }
        }
    }

    private void doDetailChangeOutOwner(Integer outTenantId, Long outOwnerId, IObjectDescribe detailDescribe, List<String> masterDataIds) {
        QueryResult<IObjectData> detailDataResult = serviceFacade.findDetailObjectDataBatchWithPage(actionContext.getUser(),
                objectDescribe.getApiName(), masterDataIds, detailDescribe, 1, PAGE_SIZE, null);
        stopWatch.lap("findDetailObjectDataBatchWithPage1");
        int totalPage = SearchTemplateQueryExt.calculateTotalPage(detailDataResult.getTotalNumber(), PAGE_SIZE);
        for (int pageNum = 1; pageNum <= totalPage; pageNum++) {
            try {
                if (pageNum > 1) {
                    detailDataResult = serviceFacade.findDetailObjectDataBatchWithPage(actionContext.getUser(),
                            objectDescribe.getApiName(), masterDataIds, detailDescribe, pageNum, PAGE_SIZE, null);
                    stopWatch.lap("findDetailObjectDataBatchWithPage" + pageNum);
                }
                if (CollectionUtils.isEmpty(detailDataResult.getData())) {
                    break;
                }
                bulkChangeOutOwner(detailDataResult.getData(), outTenantId.toString(), outOwnerId.toString(), arg.getOldOwnerStrategy(), arg.getOldOwnerTeamMemberPermissionType(), false);
                stopWatch.lap("lockDetailObjectData" + pageNum);
            } catch (MetaDataBusinessException e) {
                log.warn("dealWithDetail error,user:{},masterApiName:{},masterDataId:{},detailApiName:{}",
                        actionContext.getUser(), objectDescribe.getApiName(), masterDataIds, detailDescribe.getApiName(), e);
            } catch (Exception e) {
                log.error("dealWithDetail error,user:{},masterApiName:{},masterDataId:{},detailApiName:{}",
                        actionContext.getUser(), objectDescribe.getApiName(), masterDataIds, detailDescribe.getApiName(), e);
            }
        }
    }

    private void bulkChangeOutOwner(List<IObjectData> dataList, String outTenantId, String outOwnerId,
                                    String oldOwnerStrategy, String oldOwnerTeamMemberPermissionType,
                                    boolean recordingOutInfo) {
        if (Strings.isNullOrEmpty(outTenantId) && StringUtils.isNotEmpty(outOwnerId)) {
            List<OrganizationEmployee> organizationEmployees = infraServiceFacade.batchGetEmployee(actionContext.getTenantId(), Lists.newArrayList(outOwnerId));
            outTenantId = organizationEmployees.stream()
                    .filter(it -> Objects.equals(outOwnerId, it.getEmployeeId()))
                    .map(OrganizationEmployee::getMainDepartmentId)
                    .findFirst()
                    .orElse(null);
        }
        outTenantId = StringUtils.isNotEmpty(outTenantId) ? outTenantId : null;
        for (IObjectData data : dataList) {
            ObjectDataExt dataExt = ObjectDataExt.of(data);
            List<String> oldOwnerIds = dataExt.getOutOwner();
            String oldTenantId = dataExt.getOutTenantId();
            Optional<String> outOwnerIdOptional = Optional.ofNullable(StringUtils.isNotEmpty(outOwnerId) ? outOwnerId : null);
            List<String> outOwner = outOwnerIdOptional.map(Lists::newArrayList).orElseGet(Lists::newArrayList);
            dataExt.setOutOwner(outOwner);
            dataExt.setOutTenantId(outTenantId);
            dealOldOwner(data, oldTenantId, oldOwnerStrategy, oldOwnerIds, oldOwnerTeamMemberPermissionType);
            dataExt.synchronizeOutTeamMemberOwner(outTenantId, outOwnerId);
        }
        updateOutDataOwnDeptAndOrg(dataList);
        List<String> updateFields = Lists.newArrayList(ObjectDataExt.OUTER_OWNER, ObjectDataExt.RELEVANT_TEAM, ObjectDataExt.OUTER_TENANT);
        if (Boolean.TRUE.equals(arg.getIsUpdateOutDataOwnDepartment())) {
            updateFields.add(ObjectDataExt.OUT_DATA_OWN_DEPARTMENT);
        }
        if (Boolean.TRUE.equals(arg.getIsUpdateOutDataOwnOrganization())) {
            updateFields.add(ObjectDataExt.OUT_DATA_OWN_ORGANIZATION);
        }
        ActionContextExt actionContextExt = ActionContextExt.of(actionContext.getUser());
        if (StringUtils.isEmpty(outOwnerId)) {
            actionContextExt.setIsAllowDeleteAllTeamMembers(true);
        }
        serviceFacade.batchUpdateByFields(actionContextExt.getContext(), dataList, updateFields);
        if (recordingOutInfo) {
            newOutInfo = Tuple.of(StringUtils.isEmpty(outTenantId) ? 0 : Integer.parseInt(outTenantId), StringUtils.isEmpty(outOwnerId) ? 0L : Long.parseLong(outOwnerId));
        }
    }

    private void updateOutDataOwnDeptAndOrg(List<IObjectData> dataList) {
        boolean isUpdateOutDataOwnDepartment = Boolean.TRUE.equals(arg.getIsUpdateOutDataOwnDepartment());
        boolean isUpdateOutDataOwnOrganization = Boolean.TRUE.equals(arg.getIsUpdateOutDataOwnOrganization());
        if (!isUpdateOutDataOwnDepartment && !isUpdateOutDataOwnOrganization) {
            return;
        }
        List<OuterAccountVo> outerAccountVos = Lists.newArrayList();
        for (IObjectData data : dataList) {
            ObjectDataExt dataExt = ObjectDataExt.of(data);
            String outTenantId = dataExt.getOutTenantId();
            Optional<String> outOwnerId = dataExt.getOutOwnerId();
            if (!outOwnerId.isPresent() || StringUtils.isEmpty(outTenantId)) {
                continue;
            }
            OuterAccountVo outerAccountVo = new OuterAccountVo();
            outerAccountVo.setOuterUid(Long.parseLong(outOwnerId.get()));
            outerAccountVo.setOuterTenantId(Long.parseLong(outTenantId));
            outerAccountVos.add(outerAccountVo);
        }
        List<PublicEmployeeObjOwnDepOrOrgData> outerOwnDepOrOrg = infraServiceFacade.batchGetOuterOwnDepOrOrg(actionContext.getUser(), outerAccountVos);
        for (IObjectData data : dataList) {
            ObjectDataExt dataExt = ObjectDataExt.of(data);
            String outTenantId = dataExt.getOutTenantId();
            Optional<String> outOwnerId = dataExt.getOutOwnerId();
            if (!outOwnerId.isPresent() || StringUtils.isEmpty(outTenantId)) {
                continue;
            }
            Optional<PublicEmployeeObjOwnDepOrOrgData> depOrOrgData = outerOwnDepOrOrg.stream()
                    .filter(x -> Objects.equals(x.getOuterTenantId(), Long.parseLong(outTenantId))
                            && Objects.equals(x.getOuterUid(), Long.parseLong(outOwnerId.get())))
                    .findFirst();
            if (depOrOrgData.isPresent()) {
                if (isUpdateOutDataOwnDepartment) {
                    dataExt.setOutDataOwnDepartmentId(depOrOrgData.get().getErDepData().getId());
                }
                if (isUpdateOutDataOwnOrganization) {
                    dataExt.setOutDataOwnOrganizationId(depOrOrgData.get().getErOrgData().getId());
                }
            }
        }
    }

    //处理原外部负责人
    private void dealOldOwner(IObjectData objectData, String oldOutTenantId, String strategy, List<String> oldOwnerIds,
                              String oldOwnerTeamMemberPermissionType) {
        if (CollectionUtils.isEmpty(oldOwnerIds)) {
            return;
        }
        for (String oldOwnerId : oldOwnerIds)
            if (!Strings.isNullOrEmpty(oldOwnerId)
                    && UdobjConstants.CHANGE_OWNER_STRATEGY.SET_ORIGINAL_OWNER_TO_NORMAL_MEMBER.getValue().equals(strategy)
                    && !Strings.isNullOrEmpty(oldOwnerTeamMemberPermissionType)
                    && getRelatedTeamEnabled()) {
                ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
                TeamMember oldTeamMember = new TeamMember(oldOwnerId, TeamMember.Role.NORMAL_STAFF, TeamMember.Permission.of(oldOwnerTeamMemberPermissionType), oldOutTenantId);
                objectDataExt.addTeamMembers(Lists.newArrayList(oldTeamMember));
            }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if (!result.isSuccess()) {
            return result;
        }
        if (CollectionUtils.isEmpty(dataList)) {
            return result;
        }
        // 异步触发审批流或成功触发审批流不需要执行后续操作
        if (isApprovalFlowStartSuccessOrAsynchronous(arg.getDataIds().get(0))) {
            return result;
        }
        result = super.after(arg, result);
        String partnerId = null;
        if (CollectionUtils.isNotEmpty(dataList)) {
            partnerId = dataList.get(0).get(PrmConstant.FIELD_PARTNER_ID, String.class);
        }
        dealDetail(dataList, partnerId, newOutInfo.getKey(), newOutInfo.getValue());
        String apiName = actionContext.getObjectApiName();
        Integer remindRecordType = 0;
        if (PrmConstant.unSupportOldObject.contains(apiName)) {
            remindRecordType = partnerRemindOutUserService.getChangePartnerOwnerRemindRecordType(apiName);
        } else {
            remindRecordType = 92;
        }
        try {
            //先拷贝一下再给异步线程使用，防止其他线程报ConcurrentModificationException
            List<IObjectData> oldDataList = ObjectDataExt.copyList(objectDataList);
            List<IObjectData> newDataList = ObjectDataExt.copyList(dataList);
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            parallelTask.submit(() -> {
                serviceFacade.logByActionType(actionContext.getUser(), EventType.MODIFY, ActionType.CHANGE_PARTNER_OWNER, oldDataList, newDataList, objectDescribe);
                List<String> updateFields = Lists.newArrayList(ObjectDataExt.OUT_DATA_OWN_DEPARTMENT, ObjectDataExt.OUT_DATA_OWN_ORGANIZATION);
                LogExtendInfo logExtendInfo = LogExtendInfo.builder().peerReason(ActionType.CHANGE_PARTNER_OWNER.getName()).peerReasonI18NKey(ActionType.CHANGE_PARTNER_OWNER.getI18NKey()).build();
                serviceFacade.bulkRecordEditLog(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, objectDescribe,
                        dataList, oldDataList, updateFields, logExtendInfo);
            });
            parallelTask.run();
        } catch (Exception ex) {
            log.error("StandardChangePartnerOwnerAction execute logChangePartnerOwner error,arg {}", arg, ex);
        }
        partnerRemindOutUserService.remindOutUser(actionContext.getUser(), apiName, remindRecordType, outInfoChangeModelList);

        return result;
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.CHANGE_PARTNER_OWNER.getButtonApiName();
    }

    public Boolean getRelatedTeamEnabled() {
        return relatedTeamEnabled;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg {
        /**
         * 数据ID列表
         */
        private List<String> dataIds;

        /**
         * 外部负责人数据id
         */
        private String ownerId;
        /**
         * 外部企业 id
         */
        private String outTenantId;
        /**
         * 老外部负责人归属
         * 1.移出
         * 2.普通团队成员
         */
        private String oldOwnerStrategy;
        /**
         * 老外部负责人在团队中的权限
         */
        private String oldOwnerTeamMemberPermissionType;

        private List<String> relateObjectApiNames;
        /**
         * 更新外部归属部门
         */
        private Boolean isUpdateOutDataOwnDepartment = false;
        /**
         * 更新外部归属组织
         */
        private Boolean isUpdateOutDataOwnOrganization = false;

        public Arg(List<String> dataIds, String ownerId, String outTenantId) {
            this.dataIds = dataIds;
            this.ownerId = ownerId;
            this.outTenantId = outTenantId;
        }

        public static Arg of(Map<String, Object> callBackData, String dataId) {
            return new Arg(
                    Lists.newArrayList(dataId),
                    (String) callBackData.get("ownerId"),
                    (String) callBackData.get("outTenantId"),
                    (String) callBackData.get("oldOwnerStrategy"),
                    (String) callBackData.get("oldOwnerTeamMemberPermissionType"),
                    (List<String>) callBackData.get("relateObjectApiNames"),
                    (Boolean) callBackData.get("isUpdateOutDataOwnDepartment"),
                    (Boolean) callBackData.get("isUpdateOutDataOwnOrganization"));
        }

        public static Arg of(String dataId, String outTenantId, String newOwnerId,
                             String oldOwnerStrategy,
                             String oldOwnerTeamMemberPermissionType,
                             List<String> relateObjectApiNames,
                             Boolean isUpdateOutDataOwnDepartment,
                             Boolean isUpdateOutDataOwnOrganization) {
            return new Arg(
                    Lists.newArrayList(dataId), newOwnerId,
                    outTenantId, oldOwnerStrategy,
                    oldOwnerTeamMemberPermissionType, relateObjectApiNames,
                    isUpdateOutDataOwnDepartment, isUpdateOutDataOwnOrganization);
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OwnerInfo {
        private String newId;
        private String newName;
        private String oldId;
        private String oldName;
        private String outTenantName;
        private String oldOutTenantName;
    }

}
