package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.metadata.ui.layout.ITabSection;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.Delegate;
import com.google.common.base.Strings;

/**
 * Created by zhouwr on 2020/7/29
 */
public class TabSectionExt {

    @Delegate
    @Getter
    private ITabSection tabSection;

    private TabSectionExt(@NonNull ITabSection tabSection) {
        this.tabSection = tabSection;
    }

    public static TabSectionExt of(ITabSection tabSection) {
        return new TabSectionExt(tabSection);
    }

    public String getNameI18nKey() {
        return (String) get(ComponentExt.NAME_I18N_KEY);
    }

    public void setNameI18nKey(String nameI18nKey) {
        set(ComponentExt.NAME_I18N_KEY, nameI18nKey);
    }

    public void setHeaderByI18nKey(String i18nKey) {
        if (Strings.isNullOrEmpty(i18nKey)) {
            return;
        }
        setHeader(I18NExt.getOrDefault(i18nKey, getHeader()));
    }
}
