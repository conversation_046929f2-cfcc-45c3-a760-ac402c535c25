package com.facishare.paas.appframework.metadata;

import lombok.Getter;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Data : 2024/10/12
 * @Description :
 */
public enum UIEventTrigger {
    OTHER(0, ""),
    BLUR(1, "失去焦点"),
    ADD_LINE(2, "新增行(从对象)"),
    EDIT_LINE(3, "编辑行(从对象)"),
    DELETE_LINE(4, "删除行(从对象)"),
    PAGE_ONLOAD(5, "页面加载时"),
    PRICE_POLICY_AFTER(6, "价格政策执行后"),
    PRICE_POLICY_CANCEL_AFTER(7, "价格政策取消后"),
    REBATE_POLICY_AFTER(8, "返利政策执行后"),
    REBATE_POLICY_CANCEL_AFTER(9, "返利政策取消后");

    @Getter
    private int code;
    private String describe;

    UIEventTrigger(int code, String descirbe) {
        this.code = code;
        this.describe = descirbe;
    }
}
