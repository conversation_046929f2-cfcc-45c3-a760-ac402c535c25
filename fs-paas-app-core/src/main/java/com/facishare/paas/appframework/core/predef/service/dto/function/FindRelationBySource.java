package com.facishare.paas.appframework.core.predef.service.dto.function;

import com.facishare.paas.appframework.core.predef.service.dto.function.SaveFunctionRelation.ReferenceInfo;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/07/08
 */
public interface FindRelationBySource {

    @Data
    class Arg {
        private String type;
        private String value;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        private List<ReferenceInfo> items;
    }
}
