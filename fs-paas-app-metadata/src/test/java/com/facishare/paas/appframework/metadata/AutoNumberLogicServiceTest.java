package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.function.dto.IncrementNumberBatchExecute;
import com.facishare.paas.metadata.api.describe.AutoNumber;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AutoNumberLogicService单元测试 - JUnit5版本
 * create by zhaoju on 2019/11/11
 */
@ExtendWith(MockitoExtension.class)
class AutoNumberLogicServiceTest {

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private AutoNumberService autoNumberService;

    @InjectMocks
    private AutoNumberLogicService autoNumberLogicService;

    private IObjectDescribe describe;
    private IObjectDescribe personnelObj;

    private static final String PERSONNEL_JSON = "{\"fields\":{\"sex\":{\"describe_api_name\":\"PersonnelObj\",\"is_index\":true,\"is_active\":true,\"create_time\":1542613251916,\"is_unique\":false,\"description\":\"\",\"label\":\"性别\",\"type\":\"select_one\",\"is_abstract\":null,\"field_num\":null,\"is_required\":false,\"api_name\":\"sex\",\"options\":[{\"not_usable\":false,\"label\":\"男\",\"value\":\"M\"},{\"not_usable\":false,\"label\":\"女\",\"value\":\"F\"}],\"define_type\":\"package\",\"_id\":\"5d0dc03aa5083df3da572690\",\"is_index_field\":false,\"is_single\":false,\"label_r\":\"性别\",\"config\":{\"attrs\":{\"is_readonly\":0,\"is_required\":0}},\"index_name\":\"s_7\",\"status\":\"released\"},\"name\":{\"describe_api_name\":\"PersonnelObj\",\"is_index\":true,\"is_active\":true,\"create_time\":1527155144118,\"pattern\":\"\",\"description\":\"员工昵称，只在企业内唯一\",\"is_unique\":true,\"label\":\"员工昵称\",\"type\":\"text\",\"is_abstract\":null,\"field_num\":null,\"is_required\":true,\"api_name\":\"name\",\"define_type\":\"package\",\"_id\":\"5d0dc03aa5083df3da572699\",\"is_extend\":false,\"is_index_field\":false,\"is_single\":false,\"label_r\":\"系统名（昵称）\",\"config\":{\"edit\":0,\"enable\":0,\"attrs\":{\"label\":0}},\"index_name\":\"name\",\"max_length\":64,\"status\":\"released\"}},\"actions\":{},\"index_version\":1,\"_id\":\"5d0dc03aa5083df3da572677\",\"tenant_id\":\"78057\",\"is_udef\":true,\"api_name\":\"PersonnelObj\",\"created_by\":\"-1000\",\"last_modified_by\":\"1005\",\"display_name\":\"人员\",\"package\":\"CRM\",\"record_type\":null,\"is_active\":true,\"icon_path\":null,\"version\":4,\"release_version\":\"6.4\",\"plural_name\":null,\"define_type\":\"package\",\"is_deleted\":false,\"config\":{\"layout\":{\"add\":0},\"record_type\":{\"add\":0,\"assign\":0}},\"last_modified_time\":1570588980409,\"create_time\":1561182266303,\"store_table_name\":\"org_employee_user\",\"module\":null,\"icon_index\":null,\"description\":\"描述企业的员工基本信息\",\"visible_scope\":null,\"display_name_r\":\"人员\"}";

    private static final String DESCRIBE_JSON = "{\"fields\":{\"field_22751__c\":{\"describe_api_name\":\"object_ryjRs__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1573460343296,\"is_unique\":false,\"description\":\"\",\"default_value\":\"\",\"label\":\"单选1\",\"type\":\"select_one\",\"is_abstract\":null,\"field_num\":13,\"is_required\":false,\"api_name\":\"field_22751__c\",\"options\":[{\"not_usable\":false,\"label\":\"示例选项\",\"value\":\"option1\",\"config\":{\"edit\":1,\"enable\":1,\"remove\":1}},{\"not_usable\":true,\"label\":\"其他\",\"value\":\"other\",\"config\":{\"edit\":1,\"enable\":1,\"remove\":1}}],\"define_type\":\"custom\",\"_id\":\"5dc91977a5083dc313523c58\",\"is_index_field\":false,\"is_single\":false,\"config\":{\"add\":1,\"edit\":1,\"enable\":1,\"display\":1,\"remove\":1,\"attrs\":{\"api_name\":1,\"options\":1,\"is_unique\":1,\"default_value\":1,\"label\":1,\"help_text\":1}},\"index_name\":\"s_8\",\"help_text\":\"\",\"status\":\"new\"},\"field_417gh__c\":{\"describe_api_name\":\"object_ryjRs__c\",\"default_is_expression\":false,\"pattern\":\"\",\"description\":\"\",\"is_unique\":true,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"t_3\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1566289368668,\"default_value\":\"\",\"label\":\"单行文本-不能重复\",\"is_abstract\":null,\"field_num\":11,\"api_name\":\"field_417gh__c\",\"_id\":\"5d5badd8a5083d83035f4c6e\",\"is_index_field\":false,\"config\":{\"add\":1,\"edit\":1,\"enable\":1,\"display\":1,\"remove\":1,\"attrs\":{\"api_name\":1,\"is_unique\":1,\"default_value\":1,\"label\":1,\"help_text\":1}},\"help_text\":\"\",\"status\":\"new\"}},\"actions\":{},\"index_version\":1,\"_id\":\"5d5a0790a5083dd185da4a8b\",\"tenant_id\":\"78057\",\"is_udef\":true,\"api_name\":\"object_ryjRs__c\",\"created_by\":\"1000\",\"last_modified_by\":\"1000\",\"display_name\":\"zj-自定义对象lookup产品\",\"package\":\"CRM\",\"record_type\":null,\"is_active\":true,\"icon_path\":\"A_201907_04_25fcb9f2ca2147f6a41d36a52614341c.png\",\"version\":23,\"release_version\":\"6.4\",\"plural_name\":null,\"define_type\":\"custom\",\"is_deleted\":false,\"config\":{\"button\":{\"add\":1},\"layout\":{\"add\":1,\"assign\":1},\"layout_rule\":{\"add\":1},\"edit\":1,\"cascade\":{\"add\":1},\"rule\":{\"add\":1},\"fields\":{\"add\":1},\"record_type\":{\"add\":1,\"assign\":1}},\"last_modified_time\":1573460347405,\"create_time\":1566181264666,\"store_table_name\":null,\"module\":null,\"icon_index\":14,\"description\":\"\",\"visible_scope\":null}";

    @BeforeEach
    void setUp() {
        personnelObj = createDescribe(PERSONNEL_JSON);
        describe = createDescribe(DESCRIBE_JSON);
    }

    private IObjectDescribe createDescribe(String jsonStr) {
        IObjectDescribe describe = new ObjectDescribe();
        describe.fromJsonString(jsonStr);
        return describe;
    }

    /**
     * 测试validateFixField方法当字段类型为selectOne时的校验
     */
    @ParameterizedTest
    @MethodSource("provideValidSelectOneFieldTestData")
    @DisplayName("测试validateFixField - selectOne类型字段校验通过")
    void testValidateFixField_SelectOneType_ShouldPass(String fieldJson) {
        // 准备测试数据
        AutoNumber autoNumber = (AutoNumber) FieldDescribeFactory.newInstance(fieldJson);

        // 使用反射调用私有方法
        assertDoesNotThrow(() -> {
            Method method = AutoNumberLogicService.class.getDeclaredMethod("validateFixField", 
                IObjectDescribe.class, AutoNumber.class, IObjectDescribe.class);
            method.setAccessible(true);
            method.invoke(autoNumberLogicService, describe, autoNumber, personnelObj);
        });
    }

    /**
     * 提供有效的selectOne字段测试数据
     */
    private static Stream<Arguments> provideValidSelectOneFieldTestData() {
        return Stream.of(
            Arguments.of("{\"describe_api_name\":\"object_ryjRs__c\",\"prefix\":\"{yy}-{mm}\",\"description\":\"\",\"is_unique\":true,\"start_number\":1,\"type\":\"auto_number\",\"is_required\":false,\"define_type\":\"custom\",\"postfix\":\"$field_22751__c$\",\"is_single\":false,\"index_name\":\"s_7\",\"is_index\":true,\"is_active\":true,\"create_time\":1573443574943,\"default_value\":\"{yy}-{mm}$owner__r.sex$0001$field_22751__c$\",\"serial_number\":4,\"label\":\"自增编号\",\"is_abstract\":null,\"field_num\":12,\"condition\":\"NONE\",\"api_name\":\"field_fveT1__c\",\"_id\":\"5dc8d7f6a5083dc31350ab6e\",\"is_index_field\":false,\"config\":{\"add\":1,\"edit\":1,\"enable\":1,\"display\":1,\"remove\":1,\"attrs\":{\"condition\":1,\"prefix\":1,\"api_name\":1,\"text_preview\":1,\"start_number\":1,\"serial_number\":1,\"label\":1,\"postfix\":1,\"help_text\":1}},\"help_text\":\"\",\"status\":\"new\"}")
        );
    }

    /**
     * 测试validateFixField方法当字段类型不是selectOne时抛出异常
     */
    @ParameterizedTest
    @MethodSource("provideInvalidFieldTestData")
    @DisplayName("测试validateFixField - 非selectOne类型字段校验失败")
    void testValidateFixField_NonSelectOneType_ShouldThrowException(String fieldJson) {
        // 准备测试数据
        AutoNumber autoNumber = (AutoNumber) FieldDescribeFactory.newInstance(fieldJson);

        // 执行被测试方法并验证异常
        assertThrows(Exception.class, () -> {
            Method method = AutoNumberLogicService.class.getDeclaredMethod("validateFixField", 
                IObjectDescribe.class, AutoNumber.class, IObjectDescribe.class);
            method.setAccessible(true);
            method.invoke(autoNumberLogicService, describe, autoNumber, personnelObj);
        });
    }

    /**
     * 提供无效字段测试数据
     */
    private static Stream<Arguments> provideInvalidFieldTestData() {
        return Stream.of(
            Arguments.of("{\"describe_api_name\":\"object_ryjRs__c\",\"prefix\":\"{yy}-{mm}-$field_417gh__c$\",\"description\":\"\",\"is_unique\":true,\"start_number\":1,\"type\":\"auto_number\",\"is_required\":false,\"define_type\":\"custom\",\"postfix\":\"$field_22751__c$\",\"is_single\":false,\"index_name\":\"s_7\",\"is_index\":true,\"is_active\":true,\"create_time\":1573443574943,\"default_value\":\"{yy}-{mm}$owner__r.sex$0001$field_22751__c$\",\"serial_number\":4,\"label\":\"自增编号\",\"is_abstract\":null,\"field_num\":12,\"condition\":\"NONE\",\"api_name\":\"field_fveT1__c\",\"_id\":\"5dc8d7f6a5083dc31350ab6e\",\"is_index_field\":false,\"config\":{\"add\":1,\"edit\":1,\"enable\":1,\"display\":1,\"remove\":1,\"attrs\":{\"condition\":1,\"prefix\":1,\"api_name\":1,\"text_preview\":1,\"start_number\":1,\"serial_number\":1,\"label\":1,\"postfix\":1,\"help_text\":1}},\"help_text\":\"\",\"status\":\"new\"}")
        );
    }

    /**
     * 测试incrementNumber方法的编号增量逻辑
     */
    @Test
    @DisplayName("测试incrementNumber - 编号增量计算")
    void testIncrementNumber() {
        // 准备测试数据
        IObjectDescribe describe = mock(IObjectDescribe.class);
        AutoNumber autoNumber = mock(AutoNumber.class);
        IncrementNumberBatchExecute.IncrementNumber incrementNumber = mock(IncrementNumberBatchExecute.IncrementNumber.class);
        User user = mock(User.class);

        when(incrementNumber.getCondition()).thenReturn("NONE");
        when(incrementNumber.getCounter()).thenReturn("counter");
        when(incrementNumber.getSteppingNumber()).thenReturn(1);
        when(incrementNumber.getInitialValue()).thenReturn(1);

        Map<IncrementNumberBatchExecute.IncrementNumber, Set<String>> incrementNumberSetMap = 
            ImmutableMap.of(incrementNumber, Sets.newHashSet("1", "2"));

        when(autoNumberService.incrementNumber(eq(describe), eq(autoNumber), eq("counter"), eq(1), 
            any(), eq(user), eq(2), eq(1))).thenReturn(2L);

        // 执行被测试方法
        Map<IncrementNumberBatchExecute.IncrementNumber, java.util.concurrent.atomic.AtomicLong> result = 
            autoNumberLogicService.incrementNumber(describe, autoNumber, incrementNumberSetMap, user);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.containsKey(incrementNumber));
        assertEquals(2, result.get(incrementNumber).get());
    }

    /**
     * 测试calculateAutoNumberValue方法的自增编号计算
     */
    @Test
    @DisplayName("测试calculateAutoNumberValue - 自增编号值计算")
    void testCalculateAutoNumberValue() throws MetadataServiceException {
        // 准备测试数据
        String tenantId = "12321";
        Map<String, Object> dataMap = Collections.singletonMap("object_describe_api_name", "object_ryjRs__c");

        when(describeLogicService.findObjectWithoutCopyIfGray(tenantId, "object_ryjRs__c"))
            .thenReturn(describe);
        when(autoNumberService.generateAutoNumber(eq(describe), eq(1), anyLong()))
            .thenReturn(Collections.singletonMap("field_fveT1__c", 10));
        when(autoNumberService.replaceAutoNumber(anyList(), eq(describe), 
            eq(Collections.singletonMap("field_fveT1__c", 10)), anyLong()))
            .thenReturn(Collections.emptyList());

        // 执行被测试方法
        assertDoesNotThrow(() -> {
            autoNumberLogicService.calculateAutoNumberValue(tenantId, dataMap);
        });

        // 验证Mock交互
        verify(autoNumberService).generateAutoNumber(eq(describe), eq(1), anyLong());
        verify(autoNumberService).replaceAutoNumber(anyList(), eq(describe), 
            eq(Collections.singletonMap("field_fveT1__c", 10)), anyLong());
    }
} 