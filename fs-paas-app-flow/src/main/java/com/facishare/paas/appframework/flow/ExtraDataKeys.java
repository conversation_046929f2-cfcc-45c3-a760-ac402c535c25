package com.facishare.paas.appframework.flow;

/**
 * Created by zhouwr on 2019/5/29
 */
public interface ExtraDataKeys {
    String DETAIL_CHANGE = "detailChange";
    String TRIGGER_WORK_FLOW = "triggerWorkflow";
    String DETAIL_CREATE_TIME = "detailCreateTime";
    String USE_SNAPSHOT_FOR_APPROVAL = "useSnapshotForApproval";
    String SUPPORT_EDIT_DETAIL_SNAPSHOT = "supportEditDetailSnapshot";
    String OPTION_INFO = "optionInfo";
    String ARGS = "args";
    String BUTTON_API_NAME = "buttonApiName";
    String TRIGGER_FROM = "trigger_from";
    //触发审批成功以后的生命状态
    String NEW_LIFE_STATUS = "newLifeStatus";
    String TRIGGER_ACTION_CODE = "_triggerActionCode";
}
