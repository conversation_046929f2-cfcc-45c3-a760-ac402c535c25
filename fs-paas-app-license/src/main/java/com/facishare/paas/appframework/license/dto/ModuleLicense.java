package com.facishare.paas.appframework.license.dto;

import com.facishare.paas.license.pojo.ModuleInfoPojo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by zhouwr on 2017/10/13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModuleLicense {
    private String id;
    private String tenantId;
    private String productId;
    private String moduleName;
    private String moduleCode;
    private List<ModuleParaLicense> moduleParaPojos;

    public static ModuleLicense from(ModuleInfoPojo moduleInfoPojo) {
        return ModuleLicense.builder()
                .id(moduleInfoPojo.getId())
                .tenantId(moduleInfoPojo.getTenantId())
                .productId(moduleInfoPojo.getProductId())
                .moduleCode(moduleInfoPojo.getModuleCode())
                .moduleName(moduleInfoPojo.getModuleName())
                .build();
    }

}
