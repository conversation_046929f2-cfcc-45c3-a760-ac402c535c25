package com.facishare.paas.appframework.core.predef.service.dto.globaldata;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public interface BatchQueryLabels {
    @Data
    class Arg {
        private List<String> codes;
    }
    @Data
    @Builder
    class Result {
        List<ZoneInfo> zoneInfos;
    }

    @Data
    @Builder
    class ZoneInfo {
        String value;
        String label;

        public static List<ZoneInfo> from(Map<String, String> labelCodeMap) {
            return labelCodeMap.entrySet().stream()
                    .map(entry -> ZoneInfo.builder().value(entry.getKey()).label(entry.getValue()).build())
                    .collect(Collectors.toList());
        }
    }

}
