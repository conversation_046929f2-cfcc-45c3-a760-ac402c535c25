package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.metadata.ButtonUsePageType;
import com.facishare.paas.appframework.metadata.LayoutButtonExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ButtonOrderTest {

    @Mock
    private IUdefButton udefButton;
    @Mock
    private LayoutExt layoutExt;
    @Mock
    private ObjectDescribeExt describeExt;

    private List<IButton> targetButtons;
    private List<IButton> templateButtons;
    private List<IUdefButton> udefButtons;

    @BeforeEach
    void setUp() {
        targetButtons = Lists.newArrayList();
        templateButtons = Lists.newArrayList();
        udefButtons = Lists.newArrayList();

        // 创建测试按钮
        IButton targetButton = new Button();
        targetButton.setName("test_button");
        targetButton.setLabel("Test Button");
        targetButtons.add(targetButton);

        IButton templateButton = new Button();
        templateButton.setName("test_button");
        templateButton.set(ButtonOrder.DISPLAY_MODE, "icon");
        templateButtons.add(templateButton);

        // Mock UdefButton
        when(udefButton.isDeleted()).thenReturn(false);
        when(udefButton.getApiName()).thenReturn("custom_button__c");
        when(udefButton.getLabel()).thenReturn("Custom Button");
        when(udefButton.isActive()).thenReturn(true);
        udefButtons.add(udefButton);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试按钮排序功能，验证按模板排序的正确性
     */
    @Test
    @DisplayName("正常场景 - 按模板排序按钮")
    void testOrderingByTemplate_NormalCase() {
        // When
        List<IButton> result = ButtonOrder.orderingByTemplate(targetButtons, templateButtons);

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals("test_button", result.get(0).getName());
        assertEquals("icon", result.get(0).get(ButtonOrder.DISPLAY_MODE));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空列表的按钮排序
     */
    @Test
    @DisplayName("边界场景 - 空列表按钮排序")
    void testOrderingByTemplate_EmptyLists() {
        // Given
        List<IButton> emptyTarget = Lists.newArrayList();
        List<IButton> emptyTemplate = Lists.newArrayList();

        // When
        List<IButton> result = ButtonOrder.orderingByTemplate(emptyTarget, emptyTemplate);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试过滤自定义按钮功能
     */
    @Test
    @DisplayName("正常场景 - 过滤自定义按钮")
    void testFilteringUdefButton_NormalCase() {
        // When
        List<IButton> result = ButtonOrder.filteringUdefButton(udefButtons);

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals("custom_button__c", result.get(0).getName());
        assertEquals("Custom Button", result.get(0).getLabel());
        assertEquals(IButton.ACTION_TYPE_CUSTOM, LayoutButtonExt.of(result.get(0)).getActionType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试过滤已删除的自定义按钮
     */
    @Test
    @DisplayName("边界场景 - 过滤已删除的自定义按钮")
    void testFilteringUdefButton_DeletedButton() {
        // Given
        when(udefButton.isDeleted()).thenReturn(true);

        // When
        List<IButton> result = ButtonOrder.filteringUdefButton(udefButtons);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试过滤非自定义按钮（不以__c结尾）
     */
    @Test
    @DisplayName("边界场景 - 过滤非自定义按钮")
    void testFilteringUdefButton_NonCustomButton() {
        // Given
        when(udefButton.getApiName()).thenReturn("system_button");

        // When
        List<IButton> result = ButtonOrder.filteringUdefButton(udefButtons);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空的自定义按钮列表
     */
    @Test
    @DisplayName("边界场景 - 空的自定义按钮列表")
    void testFilteringUdefButton_EmptyList() {
        // Given
        List<IUdefButton> emptyList = Lists.newArrayList();

        // When
        List<IButton> result = ButtonOrder.filteringUdefButton(emptyList);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试null参数的自定义按钮过滤
     */
    @Test
    @DisplayName("边界场景 - null参数的自定义按钮过滤")
    void testFilteringUdefButton_NullList() {
        // When
        List<IButton> result = ButtonOrder.filteringUdefButton(null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新布局中自定义按钮的激活状态
     */
    @Test
    @DisplayName("正常场景 - 更新布局中自定义按钮状态")
    void testUpdateCustomButtonWithLayout_NormalCase() {
        // Given
        List<IButton> customButtons = Lists.newArrayList();
        IButton customButton = new Button();
        customButton.setName("custom_button__c");
        customButton.set("isActive", true);
        customButton.setLabel("Updated Label");
        customButtons.add(customButton);

        List<IButton> buttonOrder = Lists.newArrayList();
        IButton layoutButton = new Button();
        layoutButton.setName("custom_button__c");
        layoutButton.set("isActive", false);
        layoutButton.setLabel("Old Label");
        buttonOrder.add(layoutButton);

        when(layoutExt.getButtonOrder()).thenReturn(buttonOrder);
        when(layoutExt.getHiddenButtons()).thenReturn(Lists.newArrayList());

        // When
        ButtonOrder.updateCustomButtonWithLayout(customButtons, layoutExt);

        // Then
        verify(layoutExt).setButtonOrder(any(List.class));
        assertEquals(true, layoutButton.get("isActive", Boolean.class));
        assertEquals("Updated Label", layoutButton.getLabel());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空自定义按钮列表的更新
     */
    @Test
    @DisplayName("边界场景 - 空自定义按钮列表更新")
    void testUpdateCustomButtonWithLayout_EmptyCustomButtons() {
        // Given
        List<IButton> emptyCustomButtons = Lists.newArrayList();
        List<IButton> buttonOrder = Lists.newArrayList();
        
        IButton systemButton = new Button();
        LayoutButtonExt.of(systemButton).setActionType(IButton.ACTION_TYPE_DEFAULT);
        systemButton.setName("system_button");
        buttonOrder.add(systemButton);

        IButton customButton = new Button();
        customButton.setName("custom_button__c");
        buttonOrder.add(customButton);

        when(layoutExt.getButtonOrder()).thenReturn(buttonOrder);

        // When
        ButtonOrder.updateCustomButtonWithLayout(emptyCustomButtons, layoutExt);

        // Then
        verify(layoutExt).setButtonOrder(argThat(list -> 
            list.size() == 1 && "system_button".equals(list.get(0).getName())
        ));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据使用页面类型获取排序按钮列表
     */
    @Test
    @DisplayName("正常场景 - 根据Detail页面类型获取按钮")
    void testGetOrderButtonListByUsePage_DetailType() {
        // Given
        List<IButton> customButtons = Lists.newArrayList();
        IObjectDescribe objectDescribe = new ObjectDescribe();
        objectDescribe.setApiName("test_object");
        when(describeExt.getObjectDescribe()).thenReturn(objectDescribe);

        try (MockedStatic<LayoutButtons> layoutButtonsMock = mockStatic(LayoutButtons.class)) {
            LayoutButtons mockLayoutButtons = mock(LayoutButtons.class);
            when(mockLayoutButtons.getActionButtons()).thenReturn(Lists.newArrayList());
            when(mockLayoutButtons.getTailButtons()).thenReturn(Lists.newArrayList());
            layoutButtonsMock.when(() -> LayoutButtons.getInstance(describeExt)).thenReturn(mockLayoutButtons);

            // When
            List<IButton> result = ButtonOrder.getOrderButtonListByUsePage(customButtons, describeExt, ButtonUsePageType.Detail);

            // Then
            assertNotNull(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ListNormal页面类型获取按钮
     */
    @Test
    @DisplayName("正常场景 - 根据ListNormal页面类型获取按钮")
    void testGetOrderButtonListByUsePage_ListNormalType() {
        // Given
        List<IButton> customButtons = Lists.newArrayList();

        // When
        List<IButton> result = ButtonOrder.getOrderButtonListByUsePage(customButtons, describeExt, ButtonUsePageType.ListNormal);

        // Then
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试合并特殊按钮功能
     */
    @Test
    @DisplayName("正常场景 - 合并特殊按钮")
    void testCombineSpecialButtons_NormalCase() {
        // Given
        List<IButton> sourceButtons = Lists.newArrayList();
        IButton sourceButton = new Button();
        sourceButton.setName("source_button");
        sourceButtons.add(sourceButton);

        List<IButton> targetButtons = Lists.newArrayList();
        IButton targetButton = new Button();
        targetButton.setName("target_button");
        LayoutButtonExt.of(targetButton).setIsActive(true);
        targetButtons.add(targetButton);

        // When
        ButtonOrder.combineSpecialButtons(sourceButtons, targetButtons);

        // Then
        assertEquals(2, sourceButtons.size());
        assertTrue(sourceButtons.contains(sourceButton));
        assertTrue(sourceButtons.contains(targetButton));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试合并时过滤未激活的按钮
     */
    @Test
    @DisplayName("边界场景 - 合并时过滤未激活按钮")
    void testCombineSpecialButtons_FilterInactiveButtons() {
        // Given
        List<IButton> sourceButtons = Lists.newArrayList();
        List<IButton> targetButtons = Lists.newArrayList();
        
        IButton inactiveButton = new Button();
        inactiveButton.setName("inactive_button");
        LayoutButtonExt.of(inactiveButton).setIsActive(false);
        targetButtons.add(inactiveButton);

        // When
        ButtonOrder.combineSpecialButtons(sourceButtons, targetButtons);

        // Then
        assertTrue(sourceButtons.isEmpty());
    }
}
