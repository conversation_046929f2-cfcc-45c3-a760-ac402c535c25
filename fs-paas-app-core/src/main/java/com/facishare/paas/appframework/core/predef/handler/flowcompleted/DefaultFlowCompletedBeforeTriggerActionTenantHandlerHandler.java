package com.facishare.paas.appframework.core.predef.handler.flowcompleted;

import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.facade.FlowCompletedActionServiceFacade;
import com.facishare.paas.appframework.metadata.handler.HandlerType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by zhouwr on 2023/9/28.
 */
@Component
@HandlerProvider(name = "defaultFlowCompletedBeforeTriggerActionTenantHandlerHandler")
public class DefaultFlowCompletedBeforeTriggerActionTenantHandlerHandler implements FlowCompletedActionHandler {

    @Autowired
    private FlowCompletedActionServiceFacade flowCompletedActionServiceFacade;

    @Override
    public Result handle(HandlerContext context, Arg arg) {
        return flowCompletedActionServiceFacade.executeTriggerActionTenantHandler(context, arg,
                Lists.newArrayList(HandlerType.BEFORE, HandlerType.ACT));
    }
}
