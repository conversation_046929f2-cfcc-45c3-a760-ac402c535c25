package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.FindTodoCount;
import com.facishare.paas.appframework.common.service.dto.FindTodoList;
import com.facishare.paas.appframework.core.model.User;

public interface MessageBoxService {
    FindTodoCount.Result findTodoCount(User user, FindTodoCount.Arg arg);
    FindTodoList.Result findTodoList(User user, FindTodoList.Arg arg);
}
