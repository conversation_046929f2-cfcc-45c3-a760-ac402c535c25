package com.facishare.paas.appframework.core.predef.handler.edit;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.handler.edit.EditActionHandler;
import com.facishare.paas.metadata.api.IObjectData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * Created by zhouwr on 2023/2/14.
 */
@Component
@HandlerProvider(name = "_testEditHandler")
public class TestEditHandler implements EditActionHandler {
    @Override
    public Result handle(HandlerContext context, Arg arg) {
        IObjectData objectData = arg.objectData();
        if (StringUtils.contains(objectData.getName(), "test")) {
            throw new ValidateException("Blocked by test handler ^_^");
        }
        return new Result();
    }
}
