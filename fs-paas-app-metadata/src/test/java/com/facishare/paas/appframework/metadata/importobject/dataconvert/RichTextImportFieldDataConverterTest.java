package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.metadata.importobject.dataconvert.ConvertResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.describe.RichTextFieldDescribe;
import com.fxiaoke.release.FsGrayReleaseBiz;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.reflect.Whitebox;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * RichTextImportFieldDataConverter单元测试
 */
@ExtendWith(MockitoExtension.class)
class RichTextImportFieldDataConverterTest {

  @Mock
  private FsGrayReleaseBiz fsGrayReleaseBiz;

  private RichTextImportFieldDataConverter richTextImportFieldDataConverter;

  @BeforeEach
  void setUp() {
    richTextImportFieldDataConverter = new RichTextImportFieldDataConverter();
    
    // 设置灰度配置
    Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz);
    when(fsGrayReleaseBiz.isAllow(any(), any())).thenReturn(true);
  }

  @Test
  void testConvertFieldData() {
    // given
    IObjectDescribe objectDescribe = new ObjectDescribe();
    objectDescribe.setApiName("object_test__c");
    
    IObjectData objectData = new ObjectData();
    objectData.set("field_rich_text__c", "你说：\"你好，世界！\"");
    
    IFieldDescribe fieldDescribe = new RichTextFieldDescribe();
    fieldDescribe.setApiName("field_rich_text__c");
    
    User user = User.systemUser("74255");

    // when
    ConvertResult result = richTextImportFieldDataConverter.convertFieldData(objectData, objectDescribe, fieldDescribe, user);

    // then
    assertNotNull(result);
    assertDoesNotThrow(() -> {
      String jsonResult = JSON.toJSONString(result.getValue());
      assertTrue(jsonResult.contains("你说：\\\"你好，世界！\\\""));
      assertEquals("{\"__xt\":{\"__json\":{\"type\":\"doc\",\"content\":[{\"type\":\"paragraph\",\"content\":[{\"text\":\"你说：\\\"你好，世界！\\\"\",\"type\":\"text\"}]}]}},\"text\":\"你说：\\\"你好，世界！\\\"\"}",
                   jsonResult);
    });
  }
}
