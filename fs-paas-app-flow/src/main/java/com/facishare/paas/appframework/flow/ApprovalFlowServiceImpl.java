package com.facishare.paas.appframework.flow;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.flow.dto.*;
import com.facishare.paas.appframework.flow.exception.QueryApprovalFlowStatusException;
import com.facishare.paas.appframework.flow.exception.StartApprovalFlowException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by liyiguang on 2017/8/14.
 */
@Slf4j
@Service("approvalFlowService")
public class ApprovalFlowServiceImpl implements ApprovalFlowService {

    @Autowired
    ApprovalFlowServiceProxy proxy;
    @Autowired
    ApprovalFlowAssigneesProxy approvalFlowAssigneesProxy;

    @Override
    public ApprovalFlowStartResult startApprovalFlow(User user,
                                                     String objectAPIName,
                                                     String objectId,
                                                     ApprovalFlowTriggerType type,
                                                     Map<String, Object> data,
                                                     Map<String, Object> callbackData,
                                                     StartApprovalFlow.OriginInfo originInfo) {
        return startApprovalFlow(user, objectAPIName, objectId, type, data, callbackData, originInfo, null, null);
    }

    @Override
    public ApprovalFlowStartResult startApprovalFlow(User user,
                                                     String objectAPIName,
                                                     String objectId,
                                                     ApprovalFlowTriggerType type,
                                                     Map<String, Object> data,
                                                     Map<String, Object> callbackData,
                                                     StartApprovalFlow.OriginInfo originInfo,
                                                     Map<String, Object> extraData,
                                                     Map<String, Object> freeApprovalDef) {
        return startApprovalFlow(user, objectAPIName, objectId, type.getId(), data, callbackData, originInfo, extraData,
                null, freeApprovalDef);
    }

    private ApprovalFlowStartResult startApprovalFlow(User user,
                                                      String objectAPIName,
                                                      String objectId,
                                                      String type,
                                                      Map<String, Object> data,
                                                      Map<String, Object> callbackData,
                                                      StartApprovalFlow.OriginInfo originInfo,
                                                      Map<String, Object> extraData,
                                                      Map<String, Object> objectData,
                                                      Map<String, Object> freeApprovalDef) {
        StopWatch stopWatch = StopWatch.create("approvalFlowService.startApprovalFlow");

        AuthContext context = AuthContext.builder()
                .tenantId(user.getTenantId())
                .appId("CRM")
                .userId(user.getUserId()).build();

        StartApprovalFlow.Arg arg = StartApprovalFlow.Arg.builder()
                .context(context)
                .entityId(objectAPIName)
                .objectId(objectId)
                .triggerType(type)
                .data(objectData)
                .freeApprovalDef(freeApprovalDef)
                .triggerData(StartApprovalFlow.TriggerData.builder()
                        .data(data)
                        .callbackData(callbackData)
                        .originInfo(originInfo)
                        .extraData(buildExtraData(user.getTenantId(), type, extraData))
                        .build())
                .build();

        Map<String, String> headers = buildHeaders(user);
        stopWatch.lap("buildArg");

        StartApprovalFlow.Result result;
        if (grayPreMatchApproval()) {
            result = proxy.preMatchApproval(arg, headers);
            stopWatch.lap("proxy.preMatchApproval");
        } else {
            result = proxy.startApproval(arg, headers);
            stopWatch.lap("proxy.startApproval");
        }

        ApprovalFlowStartResult approvalFlowStartResult = ApprovalFlowStartResult.of(result.getCode());
        if (approvalFlowStartResult.isFailed()) {
            log.warn("startApprovalFlow failed,headers:{},arg:{},result:{}", headers, JsonUtil.toJsonWithNull(arg),
                    JsonUtil.toJsonWithNull(result));
            throw new StartApprovalFlowException(I18N.text(I18NKey.apprflow_trigger_abnormal, result.getMessage()), result.getCode());
        }
        //匹配审批流成功将流程实例id缓存起来
        if (approvalFlowStartResult == ApprovalFlowStartResult.SUCCESS) {
            RequestContext.BizInfo bizInfo = RequestUtil.getBizInfo();
            if (bizInfo == null) {
                bizInfo = RequestContext.BizInfo.builder()
                        .biz(RequestContext.Biz.ApprovalFlow.getCode())
                        .otherBizId(result.getData())
                        .build();
                RequestContextManager.getContext().setBizInfo(bizInfo);
            } else {
                bizInfo.setOtherBizId(result.getData());
            }
        }

        //为了配合新建数据的幂等逻辑，对于新建触发的审批，如果已经存在正在处理中的审批，后续当成触发成功来处理
        if (ApprovalFlowTriggerType.CREATE.getId().equals(type) && ApprovalFlowStartResult.ALREADY_EXIST == approvalFlowStartResult) {
            approvalFlowStartResult = ApprovalFlowStartResult.SUCCESS;
        }
        stopWatch.lap("processResult");

        stopWatch.logSlow(100);
        return approvalFlowStartResult;
    }

    private boolean grayPreMatchApproval() {
        return Optional.ofNullable(RequestContextManager.getContext())
                .map(it -> it.<Boolean>getAttribute(RequestContext.Attributes.PRE_MATCH_APPROVAL))
                .orElse(false);
    }

    private Map<String, Object> buildExtraData(String tenantId, String type, Map<String, Object> extraData) {
        Map<String, Object> newExtraData = Maps.newHashMap();
        //审批流扩展信息，比如：从对象变更记录、自由审批定义等
        if (extraData != null) {
            newExtraData.putAll(extraData);
        }
        //是否触发工作流
        ApprovalFlowTriggerType flowTriggerType = ApprovalFlowTriggerType.getType(type);
        if (Objects.nonNull(flowTriggerType) && flowTriggerType.supportWorkFlow()) {
            RequestContext context = RequestContextManager.getContext();
            Boolean triggerWorkflow = Objects.nonNull(context) ? context.getAttribute(RequestContext.Attributes.TRIGGER_WORK_FLOW) : null;
            if (Objects.nonNull(triggerWorkflow)) {
                newExtraData.put(ExtraDataKeys.TRIGGER_WORK_FLOW, triggerWorkflow);
            }
        }
        //审批流是否使用快照
        if (flowTriggerType == ApprovalFlowTriggerType.UPDATE) {
            newExtraData.put(ExtraDataKeys.USE_SNAPSHOT_FOR_APPROVAL, true);
            //只有820以上版本触发的审批才支持编辑从对象快照
            newExtraData.put(ExtraDataKeys.SUPPORT_EDIT_DETAIL_SNAPSHOT, true);
        }
        //触发审批流成功以后的生命状态
        RequestContext context = RequestContextManager.getContext();
        if (Objects.nonNull(context)) {
            String newLifeStatus = context.getAttribute(ExtraDataKeys.NEW_LIFE_STATUS);
            if (StringUtils.isNotBlank(newLifeStatus)) {
                newExtraData.put(ExtraDataKeys.NEW_LIFE_STATUS, newLifeStatus);
            }
        }
        return newExtraData;
    }

    @Override
    public Map<String, ApprovalFlowStartResult> batchStartApproval(User user, List<Tuple<String, String>> objects, ApprovalFlowTriggerType triggerType,
                                                                   Map<String, Map<String, Object>> dataMap, Map<String, Map<String, Object>> callbackDataMap,
                                                                   Map<String, Object> freeApprovalDef) {
        return batchStartApproval(user, objects, triggerType.getId(), dataMap, callbackDataMap, freeApprovalDef);
    }

    @Override
    public Map<String, ApprovalFlowStartResult> batchStartApproval(User user, List<Tuple<String, String>> objects, String triggerType,
                                                                   Map<String, Map<String, Object>> dataMap, Map<String, Map<String, Object>> callbackDataMap,
                                                                   Map<String, Object> freeApprovalDef) {

        Map<String, ApprovalFlowStartResult> ret = Maps.newHashMap();

        //TODO: 优化为批量操作
        for (Tuple<String, String> object : objects) {
            String objectId = object.getKey();
            String objectAPIName = object.getValue();

            ApprovalFlowStartResult result = startApprovalFlow(user, objectAPIName, objectId, triggerType,
                    dataMap.get(objectId), callbackDataMap.get(objectId), null, null, null, freeApprovalDef);
            ret.put(objectId, result);
        }
        return ret;
    }

    @Override
    public ApprovalFlowStartResult startApprovalWithObjectData(User user, Map<String, Object> objectData, String triggerType,
                                                               Map<String, Map<String, Object>> dataMap,
                                                               Map<String, Map<String, Object>> callbackDataMap,
                                                               Map<String, Object> freeApprovalDef) {
        String objectId = (String) objectData.get(IObjectData.ID);
        String objectAPIName = (String) objectData.get(IObjectData.DESCRIBE_API_NAME);

        ApprovalFlowStartResult result = startApprovalFlow(user, objectAPIName, objectId, triggerType,
                dataMap.get(objectId), callbackDataMap.get(objectId), null, null, objectData, freeApprovalDef);
        return result;
    }

    @Override
    public void batchStartApprovalAsynchronous(User user, List<Tuple<String, String>> objects, ApprovalFlowTriggerType triggerType,
                                               Map<String, Map<String, Object>> dataMap, Map<String, Map<String, Object>> callbackDataMap) {
        List<StartApprovalFlowAsynchronous.DataEntity> dataEntityList = Lists.newArrayList();
        for (Tuple<String, String> object : objects) {
            String objectId = object.getKey();
            String objectAPIName = object.getValue();
            Map<String, Object> data = dataMap.get(objectId);
            Map<String, Object> callbackData = callbackDataMap.get(objectId);

            dataEntityList.add(StartApprovalFlowAsynchronous.DataEntity.builder()
                    .dataId(objectId)
                    .entityId(objectAPIName)
                    .triggerData(StartApprovalFlowAsynchronous.TriggerData.builder()
                            .data(data)
                            .callbackData(callbackData)
                            .build())
                    .build());
        }

        startApprovalAsynchronous(user, triggerType, dataEntityList);
    }

    private void startApprovalAsynchronous(User user, ApprovalFlowTriggerType triggerType, List<StartApprovalFlowAsynchronous.DataEntity> dataEntityList) {
        AuthContext context = AuthContext.builder()
                .tenantId(user.getTenantId())
                .appId("CRM")
                .userId(user.getUserId()).build();

        StartApprovalFlowAsynchronous.Arg arg = StartApprovalFlowAsynchronous.Arg.builder()
                .context(context)
                .triggerType(triggerType.getId())
                .dataEntityList(dataEntityList)
                .build();

        Map<String, String> headers = buildHeaders(user);
        StartApprovalFlowAsynchronous.Result result = proxy.startApprovalFlowAsynchronous(arg, headers);
        if (!result.isSuccess()) {
            log.error("startApprovalAsynchronous error,headers:{},arg:{},result:{}", headers, JsonUtil.toJsonWithNull(arg),
                    JsonUtil.toJsonWithNull(result));
        }
    }

    @Override
    public ApprovalFlowStatus getApprovalStatusOfObject(User user, String objectId) {
        Map<String, ApprovalFlowStatus> map = batchGetApprovalStatusOfObject(user, Lists.newArrayList(objectId));
        return map.get(objectId);
    }

    @Override
    public Map<String, ApprovalFlowStatus> batchGetApprovalStatusOfObject(User user, List<String> objectIds) {
        if (CollectionUtils.empty(objectIds)) {
            return Maps.newHashMap();
        }

        AuthContext context = AuthContext.builder()
                .tenantId(user.getTenantId())
                .appId("CRM")
                .userId(user.getUserId()).build();
        QueryApprovalStatus.Arg arg = QueryApprovalStatus.Arg.builder()
                .context(context)
                .objectIds(objectIds).build();

        QueryApprovalStatus.Result result = proxy.queryApprovalStatus(arg, buildHeaders(user));
        if (!result.isSuccess()) {
            throw new QueryApprovalFlowStatusException(result.getMessage());
        }

        return result.getData().stream().map(x -> Tuple.of(x.getObjectId(), ApprovalFlowStatus.of(x.getStatus())))
                .collect(Collectors.toMap(x -> x.getKey(), x -> x.getValue()));
    }

    @Override
    public Map<String, Tuple<String, ApprovalFlowStatus>> batchGetApprovalTriggerType(User user, List<String> objectIds) {
        AuthContext context = AuthContext.builder()
                .tenantId(user.getTenantId())
                .appId("CRM")
                .userId(user.getUserId()).build();
        QueryApprovalStatus.Arg arg = QueryApprovalStatus.Arg.builder()
                .context(context)
                .objectIds(objectIds).build();

        QueryApprovalStatus.Result result = proxy.queryApprovalStatus(arg, buildHeaders(user));
        if (!result.isSuccess()) {
            throw new QueryApprovalFlowStatusException(result.getMessage());
        }
        List<QueryApprovalStatus.Status> statuses = result.getData();
        Map<String, Tuple<String, ApprovalFlowStatus>> triggerMap = Maps.newHashMap();
        for (QueryApprovalStatus.Status status : statuses) {
            Tuple<String, ApprovalFlowStatus> map = new Tuple<>(status.getTriggerType(), ApprovalFlowStatus.of(status.getStatus()));
            triggerMap.put(status.getObjectId(), map);
        }
        return triggerMap;
    }


    private Map<String, String> buildHeaders(User user) {
        Map<String, String> ret = RestUtils.buildHeaders(user);
        ret.put("x-tenant-id", String.valueOf(user.getTenantId()));
        //外部身份时，userId的值也是outUserId,触发审批流时需要用对应上游负责人的id
        ret.put("x-user-id", user.getUserIdWithFlowGray());
        return ret;
    }


    @Override
    public FindAssigneesModel.Result findApprovalFlowAssigneesExceptional(List<String> dataIdList, String objectDescribeApiName, User user) {
        //审批流接口会校验userId不能为空，因此userId为空时传个-10000，实际上这个参数没有用
        String userId = Strings.isNullOrEmpty(user.getUserId()) ? User.SUPPER_ADMIN_USER_ID : user.getUserId();
        AuthContext context = AuthContext.builder()
                .tenantId(user.getTenantId())
                .appId("CRM")
                .userId(userId).build();

        FindAssigneesModel.Arg arg = FindAssigneesModel.Arg.builder()
                .entityId(objectDescribeApiName)
                .objectIds(dataIdList)
                .context(context).build();

        try {
            return approvalFlowAssigneesProxy.findAssigneesModel(arg, buildHeaders(user));
        } catch (RuntimeException e) {
            log.warn("findApprovalFlowAssigneesExceptional fail:{}", e.getMessage(), e);
            FindAssigneesModel.Result result = new FindAssigneesModel.Result();
            result.setResult(Collections.emptyList());
            return result;
        }
    }

    @Override
    public Set<String> findApprovalFlowExceptionalAssignees(String id, String objectAPIName, User user) {
        FindAssigneesModel.Result ret = findApprovalFlowAssigneesExceptional(Lists.newArrayList(id), objectAPIName, user);
        return ret.getAllAssignees();
    }

    @Override
    public boolean matchRule(String id, String objectApiName, User user, String triggerType) {
        Map<String, String> headers = buildHeaders(user);
        MatchRule.Arg arg = MatchRule.Arg.builder()
                .entityId(objectApiName)
                .objectId(id)
                .triggerType(triggerType)
                .build();
        log.info("matchRule info, headers:{},arg:{}", JSON.toJSONString(headers), JSON.toJSONString(arg));
        MatchRule.Result result = proxy.matchRule(arg, headers);
        if (result.getCode() != 0) {
            log.error("matchRule error,headers:{},arg:{},result:{}", headers, JsonUtil.toJsonWithNull(arg),
                    JsonUtil.toJsonWithNull(result));
            throw new QueryApprovalFlowStatusException(result.getMessage());
        }
        return result.isData();
    }

    @Override
    public boolean hasApprovalFlowDefinitions(User user, String objectApiName) {
        Map<String, String> headers = buildHeaders(user);
        QueryDefinitions.Arg arg = QueryDefinitions.Arg.builder().entityId(objectApiName).build();
        QueryDefinitions.Result result = proxy.queryDefinitions(arg, headers);
        if (!result.isSuccess()) {
            log.warn("queryDefinitions failed,header:{},arg:{},result:{}", headers, arg, result);
            return false;
        }
        return CollectionUtils.notEmpty(result.getData());
    }

    @Override
    public boolean hasActiveApprovalFlowDefinitions(User user, String objectApiName, String triggerType) {
        Map<String, String> headers = buildHeaders(user);
        ApprovalFlowDefinition.Arg arg = ApprovalFlowDefinition.Arg.builder().entityId(objectApiName).triggerType(triggerType).build();
        ApprovalFlowDefinition.Result result = proxy.queryApprovalFlowDefinition(arg, headers);
        if (!result.isSuccess()) {
            log.warn("queryDefinitions failed,header:{},arg:{},result:{}", headers, arg, result);
            return true;
        }
        return result.isExist();
    }

    @Override
    public void freeApprovalDefValidate(User user, Map<String, Object> freeApprovalDef) {
        Map<String, String> headers = buildHeaders(user);
        FreeApprovalDefValidate.Arg arg = FreeApprovalDefValidate.Arg.builder().freeApprovalDef(freeApprovalDef).build();
        FreeApprovalDefValidate.Result result = proxy.freeApprovalDefValidate(arg, headers);
        if (result.failed()) {
            log.warn("freeApprovalDefValidate failed,header:{},arg:{},result:{}", headers, arg, result);
            throw new ValidateException(result.getMessage());
        }
    }
}
