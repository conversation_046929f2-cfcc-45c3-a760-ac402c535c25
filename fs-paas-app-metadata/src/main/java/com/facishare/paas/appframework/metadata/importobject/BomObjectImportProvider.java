package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.metadata.util.SfaGrayUtil;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

import static com.facishare.crm.openapi.Utils.BOM_API_NAME;

/**
 * <AUTHOR>
 * @date 2021/4/29 上午10:36
 * @illustration
 * @slogan:
 * @version:
 */
@Component
public class BomObjectImportProvider extends DefaultObjectImportProvider {


    @Override
    public String getObjectCode() {
        return BOM_API_NAME;
    }


    @Override
    protected String getObjectName(IObjectDescribe objectDescribe) {
        return super.getObjectName(objectDescribe);
    }

    @Override
    public Optional<ImportObject> getImportObject(IObjectDescribe describe, IUniqueRule uniqueRule) {
        if (!SfaGrayUtil.openCPQ(RequestContextManager.getContext().getTenantId())) {
            return Optional.empty();
        }
        return super.getImportObject(describe, uniqueRule);
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return ImportType.UNSUPPORT_INSERT_IMPORT;
    }

    @Override
    protected boolean supportImportId(IObjectDescribe objectDescribe) {
        return true;
    }

    @Override
    protected List<MatchingType> getMatchingTypesByUpdateWithUniqueRule(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return Lists.newArrayList(MatchingType.ID);
    }
}
