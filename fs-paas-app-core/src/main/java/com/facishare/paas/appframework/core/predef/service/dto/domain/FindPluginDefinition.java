package com.facishare.paas.appframework.core.predef.service.dto.domain;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.domain.DomainPluginDefinition;
import com.facishare.paas.appframework.metadata.domain.DomainPluginDefinition.ParamDefinition;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2022/9/20.
 */
public interface FindPluginDefinition {
    @Data
    class Arg {
        private String pluginApiName;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Result {
        private DomainPluginDefinitionDocument pluginDefinition;
    }

    @Data
    class DomainPluginDefinitionDocument {
        private String apiName;
        private String label;
        private String description;
        private List<ParamDefinition> paramDefinition;
        private Map<String, Object> managementConfig;

        public static DomainPluginDefinitionDocument of(DomainPluginDefinition definition) {
            DomainPluginDefinitionDocument document = new DomainPluginDefinitionDocument();
            document.setApiName(definition.getApiName());
            document.setLabel(definition.getLabel());
            document.setDescription(definition.getDescription());
            List<ParamDefinition> paramDefinitionDocuments = CollectionUtils.nullToEmpty(definition.getParamDefinition());
            document.setParamDefinition(paramDefinitionDocuments);
            document.setManagementConfig(definition.getManagementConfig());
            return document;
        }
    }
}
