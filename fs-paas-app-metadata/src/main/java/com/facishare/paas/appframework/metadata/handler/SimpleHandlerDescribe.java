package com.facishare.paas.appframework.metadata.handler;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.repository.model.HandlerDefinition;
import com.facishare.paas.appframework.metadata.repository.model.HandlerRuntimeConfig;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2023/2/6.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SimpleHandlerDescribe {
    private String apiName;
    private String handlerType;
    private String interfaceCode;
    private String providerType;
    private String industryCode;
    private String restApiUrl;
    private String aplApiName;
    private boolean enableDistributedTransaction;
    private int executeOrder;
    private boolean executeAsync;
    private boolean continueAfterApprovalSuccess;

    public static SimpleHandlerDescribe of(HandlerDefinition definition, HandlerRuntimeConfig config) {
        return SimpleHandlerDescribe.builder()
                .apiName(definition.getApiName())
                .handlerType(definition.getHandlerType())
                .interfaceCode(config.getInterfaceCode())
                .providerType(definition.getProviderType())
                .industryCode(definition.getIndustryCode())
                .restApiUrl(definition.getRestApiUrl())
                .aplApiName(definition.getAplApiName())
                .enableDistributedTransaction(definition.isEnableDistributedTransaction())
                .executeOrder(Optional.ofNullable(config.getExecuteOrder()).orElse(Integer.MAX_VALUE))
                .executeAsync(definition.isSupportAsync() && config.isExecuteAsync())
                .continueAfterApprovalSuccess(definition.continueAfterApprovalSuccess())
                .build();
    }

    public static List<SimpleHandlerDescribe> filterTenantHandlerAfterApprovalHandler(List<SimpleHandlerDescribe> handlerDescribes,
                                                                                      ObjectAction action,
                                                                                      ApprovalFlowTriggerType triggerType,
                                                                                      List<String> handlerTypes) {
        if (CollectionUtils.empty(handlerDescribes)) {
            return Collections.emptyList();
        }
        SimpleHandlerDescribe defaultTriggerApprovalHandler = findDefaultTriggerApprovalHandler(handlerDescribes, action, triggerType);
        return handlerDescribes.stream().filter(x -> {
            if (HandlerDefinition.isDefaultPostActionHandler(x.getApiName())) {
                return true;
            }
            if (!HandlerDefinition.provideByTenant(x.getProviderType())) {
                return false;
            }
            if (x.isContinueAfterApprovalSuccess()) {
                return false;
            }
            if (Objects.isNull(defaultTriggerApprovalHandler)) {
                return false;
            }
            if (!handlerTypes.contains(x.getHandlerType())) {
                return false;
            }
            return x.getExecuteOrder() > defaultTriggerApprovalHandler.getExecuteOrder();
        }).collect(Collectors.toList());
    }

    private static SimpleHandlerDescribe findDefaultTriggerApprovalHandler(List<SimpleHandlerDescribe> handlerDescribes,
                                                                           ObjectAction action, ApprovalFlowTriggerType triggerType) {
        String defaultTriggerApprovalHandlerApiName;
        switch (action) {
            case CREATE:
                defaultTriggerApprovalHandlerApiName = HandlerDefinition.DEFAULT_TRIGGER_CREATE_APPROVAL_FLOW_ADD_AFTER_HANDLER;
                break;
            case UPDATE:
                if (triggerType == ApprovalFlowTriggerType.CREATE) {
                    defaultTriggerApprovalHandlerApiName = HandlerDefinition.DEFAULT_TRIGGER_CREATE_APPROVAL_FLOW_EDIT_AFTER_HANDLER;
                } else {
                    defaultTriggerApprovalHandlerApiName = HandlerDefinition.DEFAULT_TRIGGER_UPDATE_APPROVAL_FLOW_EDIT_BEFORE_HANDLER;
                }
                break;
            case INVALID:
            case BULK_INVALID:
                defaultTriggerApprovalHandlerApiName = HandlerDefinition.DEFAULT_TRIGGER_APPROVAL_FLOW_INVALID_BEFORE_HANDLER;
                break;
            default:
                defaultTriggerApprovalHandlerApiName = null;
                break;
        }
        if (Strings.isNullOrEmpty(defaultTriggerApprovalHandlerApiName)) {
            return null;
        }
        return handlerDescribes.stream()
                .filter(x -> Objects.equals(defaultTriggerApprovalHandlerApiName, x.getApiName()))
                .findFirst()
                .orElse(null);
    }

    public boolean supportDistributedTransaction() {
        return HandlerType.ACT.getCode().equals(handlerType) && enableDistributedTransaction;
    }

}
