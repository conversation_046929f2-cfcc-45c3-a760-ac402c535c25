package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.impl.UdefButton;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by linqy on 2018/01/18
 */
public class ButtonDocument extends DocumentBaseEntity {

    public ButtonDocument() {
    }

    public ButtonDocument(Map<String, Object> data) {
        super(data);
    }

    public static List<ButtonDocument> fromButtons(List<IButton> bachButtons) {
        if (CollectionUtils.isEmpty(bachButtons)) {
            return Lists.newArrayList();
        }
        return bachButtons.stream()
                .map(ButtonDocument::fromButton)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static ButtonDocument fromButton(IButton button) {
        if (Objects.isNull(button)) {
            return null;
        }
        return of(((Button) button).getContainerDocument());
    }

    public static List<IButton> toLayoutButtonList(List<ButtonDocument> buttonDocuments) {
        if (CollectionUtils.isEmpty(buttonDocuments)) {
            return Lists.newArrayList();
        }
        return buttonDocuments.stream().map(ButtonDocument::toLayoutButton).collect(Collectors.toList());
    }

    public IButton toLayoutButton() {
        return new Button(new Document(data));
    }

    public IUdefButton toButton() {
        return new UdefButton(new Document(data));
    }

    public static ButtonDocument of(Map<String, Object> data) {
        if (data == null) {
            return null;
        }
        return new ButtonDocument(data);
    }

    public static ButtonDocument of(IUdefButton button) {
        if (button == null) {
            return null;
        }
        return new ButtonDocument(ButtonExt.of(button).toMap());
    }

    public static ButtonDocument of(IUdefButton button, boolean includeActionCode, boolean includeActionType) {
        ButtonDocument buttonDocument = ButtonDocument.of(button);
        if (Objects.nonNull(button) && Objects.nonNull(buttonDocument)) {
            if (includeActionCode) {
                buttonDocument.put("action", ObjectAction.getActionCodeByButtonApiName(button.getApiName()));
            }
            if (includeActionType) {
                buttonDocument.put(IButton.ACTION_TYPE, ButtonExt.of(button).getActionType());
            }
        }
        return buttonDocument;
    }

    public static ButtonDocument of(ButtonExt button) {
        if (button == null) {
            return null;
        }
        return new ButtonDocument(button.toMap());
    }

    public static List<ButtonDocument> ofList(Collection<IUdefButton> buttons) {
        if (CollectionUtils.isEmpty(buttons)) {
            return Lists.newArrayList();
        }
        return buttons.stream().map(ButtonDocument::of).collect(Collectors.toList());
    }

}
