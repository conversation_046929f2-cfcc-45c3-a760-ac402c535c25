package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.AppCaptchaService;
import com.facishare.paas.appframework.common.service.PhoneNumberService;
import com.facishare.paas.appframework.common.service.dto.*;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.gray.config.PhoneVerificationCodeFunctionConfig;
import com.facishare.paas.service.model.ObjectDataDocument;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.service.PhoneNumberServiceImpl.CRM_ACTION;

/**
 * 手机字段相关功能支持：只支持大陆手机号码
 */
@Slf4j
@Component
@ServiceModule("phone_number")
public class ObjectPhoneNumberService {

    @Autowired
    protected PhoneNumberService phoneNumberService;

    private AppCaptchaService appCaptchaService;

    @Autowired
    public void setCaptchaService(AppCaptchaService appCaptchaService) {
        this.appCaptchaService = appCaptchaService;
    }

    // 查询手机归属地
    @ServiceMethod("query_phone_number_info")
    public QueryPhoneNumberInformation.Result queryPhoneNumberInfo(QueryPhoneNumberInformation.Arg arg) {
        QueryPhoneNumberInformation.Result result = phoneNumberService.queryPhoneNumberInfo(arg.getMobile());
        return result;
    }

    // 批量查询手机归属地
    @ServiceMethod("batch_query_phone_number_info")
    public List<QueryPhoneNumberInformation.Result> batchQueryPhoneNumberInfo(QueryPhoneNumberInformation.Arg arg) {
        String[] split = arg.getMobile().split(",");
        Set<String> mobileSet = new HashSet<>(Arrays.asList(split));
        List<QueryPhoneNumberInformation.Result> results = phoneNumberService.batchQueryPhoneNumberInfo(mobileSet);
        return results;
    }

    private void sendCodeByAPL(User user, String describeApiName, String functionApiName, String methodName, List<String> requestBody) {

    }

    /**
     * 发送验证码
     * 新建编辑页填写手机字段：需要发送短信验证码才能保存
     * 1. 使用 APL 发送短信
     * 2. 使用 营销通 发送短信，有额度限制
     */
    @ServiceMethod("query_verification_code")
    public QueryVerificationCode.Result queryVerificationCode(QueryVerificationCode.Arg arg, ServiceContext context) {
        String result;

        PhoneVerificationCodeFunctionConfig instance = PhoneVerificationCodeFunctionConfig.getInstance();
        String functionApiName = instance.getFunctionApiName(context.getTenantId(), arg.getDescribeApiName(), arg.getFieldApiName());
        if (!Strings.isNullOrEmpty(functionApiName)) {
            int expireTime = instance.getCodeExpireTime(context.getTenantId(), arg.getDescribeApiName(), arg.getFieldApiName());
            GenerateVerificationCode.Result buildResult = phoneNumberService.generateVerificationCode(context.getUser(),
                    arg.getPhone(), context.getClientIp(), arg.getCaptchaCode(), arg.getCaptchaId(), expireTime);
            result = buildResult.getStatus();
            if (buildResult.success()) {
                //调用函数发短信
                GenerateVerificationCode.SendVerificationCodeArg verificationCodeArg = GenerateVerificationCode.SendVerificationCodeArg.builder()
                        .objectApiName(arg.getDescribeApiName())
                        .fieldApiName(arg.getFieldApiName())
                        .verificationCode(buildResult.getCode())
                        .objectData(arg.getObjectData())
                        .objectDetails(getObjetDetails(arg.getDetails()))
                        .build();
                phoneNumberService.sendCodeByAPL(context.getUser(), functionApiName, "sendVerificationCode", verificationCodeArg);
            }

        } else {
            // action: CRM
            result = phoneNumberService.sendSmsCode(context.getEa(), context.getUser(), context.getClientIp(), arg.getAreaCode(),
                    arg.getPhone(), arg.getCaptchaCode(), arg.getCaptchaId());
        }


        switch (result) {
            case "SUCCESS":
                return QueryVerificationCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.SUCCESS)
                        .build();
            case "NEED_IMAGE_CODE":
                return QueryVerificationCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.NEED_CAPTCHA)
                        .errorMessage(I18N.text(I18NKey.MOBILE_NEED_IMAGE_CODE))
                        .build();
            case "IMAGE_CODE_ERROR":
                return QueryVerificationCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.NEED_CAPTCHA)
                        .errorMessage(I18N.text(I18NKey.MOBILE_IMAGE_CODE_ERROR))
                        .build();
            case "IP_LIMITED":
                return QueryVerificationCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.ERROR)
                        .errorMessage(I18N.text(I18NKey.MOBILE_IP_LIMITED))
                        .build();
            case "SMS_SEND_ERROR":
                return QueryVerificationCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.ERROR)
                        .errorMessage(I18N.text(I18NKey.MOBILE_SMS_SEND_ERROR))
                        .build();
            case "SMS_TOO_OFTEN":
                return QueryVerificationCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.ERROR)
                        .errorMessage(I18N.text(I18NKey.MOBILE_SMS_TOO_OFTEN))
                        .build();
            case "AREACODE_NOT_SUPPORT":
                return QueryVerificationCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.ERROR)
                        .errorMessage(I18N.text(I18NKey.MOBILE_AREACODE_NOT_SUPPORT))
                        .build();
            case "Business_NOT_SUPPORT":
                return QueryVerificationCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.ERROR)
                        .errorMessage(I18N.text(I18NKey.MOBILE_BUSSINESS_NOT_SUPPORT))
                        .build();
            case "ERROR":
            default:
                log.warn("queryVerificationCode error tenantId :{} , result :{}", context.getTenantId(), result);
                return QueryVerificationCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.ERROR)
                        .errorMessage(I18N.text(I18NKey.SYSTEM_EXCEPTION))
                        .build();
        }
    }

    private Map<String, List<Map<String, Object>>> getObjetDetails(Map<String, List<ObjectDataDocument>> details) {
        if (CollectionUtils.empty(details)) {
            return Maps.newHashMap();
        }
        Map<String, List<Map<String, Object>>> result = Maps.newHashMap();
        details.forEach((describeApiName, dataList) -> {
            List<Map<String, Object>> list = dataList.stream()
                    .map(it -> (Map<String, Object>) it)
                    .collect(Collectors.toList());
            result.put(describeApiName, list);
        });
        return result;
    }

    /**
     * 校验短信验证码
     * 新建编辑页填写手机字段：需要发送短信验证码才能保存
     */
    @ServiceMethod("check_verification_code")
    public CheckVerificationCode.Result checkVerificationCode(CheckVerificationCode.Arg arg, ServiceContext context) {
        try {
            String result = phoneNumberService.verifySmsCode(context.getUser(), arg.getAreaCode(), arg.getPhone(), arg.getSmsCode());
            switch (result) {
                case "SUCCESS":
                    return CheckVerificationCode.Result.builder()
                            .success(true)
                            .build();
                case "SMS_CODE_ERROR":
                    return CheckVerificationCode.Result.builder()
                            .success(false)
                            .errorMessage(I18N.text(I18NKey.MOBILE_SMS_CODE_ERROR))
                            .build();
                case "VERIFY_CODE_EXCEED_LIMIT":
                    return CheckVerificationCode.Result.builder()
                            .success(false)
                            .errorMessage(I18N.text(I18NKey.MOBILE_VERIFY_CODE_EXCEED_LIMIT))
                            .build();
                case "ERROR":
                default:
                    log.error("checkVerificationCode error tenantId:{} , result:{}", context.getTenantId(), result);
                    return CheckVerificationCode.Result.builder()
                            .success(true)
                            .build();
            }
        } catch (Exception e) {
            log.error("checkVerificationCode error tenantId:{} , ", context.getTenantId(), e);
            return CheckVerificationCode.Result.builder()
                    .success(true)
                    .build();
        }
    }

    /**
     * 刷新图形验证码，需要通过图形验证码才可以发送短信验证码
     * 新建编辑页填写手机字段：需要发送短信验证码才能保存
     */
    @ServiceMethod("refresh_captcha")
    public RefreshCaptcha.Result refreshCaptcha() {
        Captcha.Result result = appCaptchaService.createCaptchaCode(null);
        return RefreshCaptcha.Result.builder()
                .image(result.getData())
                .captchaId(result.getEpxId())
                .build();
    }

    /**
     * 检测是否开通短信业务（fs-marketing）
     * @param context serviceContext
     * @return 是否开通短信业务
     */
    @ServiceMethod("check_sms_status")
    public SmsStatus.Result checkSmsStatus(ServiceContext context) {
        boolean result = phoneNumberService.checkSmsStatus(context.getTenantId());
        return new SmsStatus.Result(result);
    }
}
