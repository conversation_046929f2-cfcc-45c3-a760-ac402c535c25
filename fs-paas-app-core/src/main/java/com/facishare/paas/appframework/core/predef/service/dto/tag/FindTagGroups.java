package com.facishare.paas.appframework.core.predef.service.dto.tag;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.metadata.dto.scene.SceneDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/18 2:48 下午
 */
public interface FindTagGroups {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Arg {
        @Deprecated
        @JsonProperty("describe_api_name")
        @JSONField(name = "describe_api_name")
        String describeApiName;

        @JsonProperty("keyword")
        @JSONField(name = "keyword")
        @Deprecated
        String keyword;

        @JsonProperty("query_info")
        @JSONField(name = "query_info")
        TagQuery.QueryInfo queryInfo;

        @JsonProperty("sourceInfo")
        @JSONField(name = "sourceInfo")
        String sourceInfo;
    }

    @Data
    @Builder
    class Result {
        List<Group> groups;
    }

    @Data
    @Builder
    class Group {
        @JsonProperty("name")
        @JSONField(name = "name")
        String tagGroupName;

        @JsonProperty("id")
        @JSONField(name = "id")
        String tagGroupId;

        @JsonProperty("api_name")
        @JSONField(name = "api_name")
        String tagGroupApiName;

        @JsonProperty("tag_count")
        @JSONField(name = "tag_count")
        Long tagCount;

        @JsonProperty("define_type")
        @JSONField(name = "define_type")
        String defineType;

        @JsonProperty("is_applied_to_all")
        @JSONField(name = "is_applied_to_all")
        Boolean isAppliedToAll = false;

        @JsonProperty("target_describes")
        @JSONField(name = "target_describes")
        List<ObjectDescribeDocument> targetDescribes;

        @JsonProperty("ranges")
        @JSONField(name = "ranges")
        SceneDTO.Range ranges;

        @JsonProperty("is_active")
        @JSONField(name = "is_active")
        Boolean active;

        @JsonProperty("is_mutex")
        @JSONField(name = "is_mutex")
        Boolean mutex;

        @JsonProperty("is_all")
        @JSONField(name = "is_all")
        Boolean all;

        @JsonProperty("label_names")
        @JSONField(name = "label_names")
        List<CreateOrUpdateTag.Arg> labelNames;

        @JsonProperty("group_description")
        @JSONField(name = "group_description")
        String groupDescription;
    }
}
