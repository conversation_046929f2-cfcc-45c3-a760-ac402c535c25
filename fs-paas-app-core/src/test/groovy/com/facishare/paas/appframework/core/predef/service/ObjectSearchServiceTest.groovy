package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.core.predef.service.dto.search.ObjectSearchData
import com.google.common.collect.Lists
import spock.lang.Specification

import javax.naming.directory.SearchResult

class ObjectSearchServiceTest extends Specification {
    def "test sort list after search"() {
        given:
        def list = [new ObjectSearchData.SearchResult(idList: [1, 2]),
                    new ObjectSearchData.SearchResult(idList: [1, 2, 3, 4]),
                    new ObjectSearchData.SearchResult(idList: []),
                    new ObjectSearchData.SearchResult(idList: [2, 4, 5])]
        def service = new ObjectSearchService()
        when:
        service.sortList(list)
        then:
        list[0].idList.size() == 4
    }

}
