package com.facishare.crm.privilege.swagger.model;

import com.google.gson.annotations.SerializedName;

public class UserIdGroupPojo {
  @SerializedName("id")
  private String id;
  @SerializedName("userId")
  private String userId;
  @SerializedName("tenantId")
  private String tenantId;
  @SerializedName("name")
  private String name;
  @SerializedName("description")
  private String description;
  @SerializedName("isDeleted")
  private Integer isDeleted;
  @SerializedName("status")
  private Integer status;
  @SerializedName("type")
  private Integer type;
  @SerializedName("appId")
  private String appId;
  @SerializedName("createdBy")
  private String createdBy;
  @SerializedName("createTime")
  private Long createTime;
  @SerializedName("lastModifiedBy")
  private String lastModifiedBy;
  @SerializedName("lastModifiedTime")
  private Long lastModifiedTime;
  @SerializedName("delFlag")
  private Boolean delFlag = false;

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getUserId() {
    return userId;
  }

  public void setUserId(String userId) {
    this.userId = userId;
  }

  public String getTenantId() {
    return tenantId;
  }

  public void setTenantId(String tenantId) {
    this.tenantId = tenantId;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public Integer getIsDeleted() {
    return isDeleted;
  }

  public void setIsDeleted(Integer isDeleted) {
    this.isDeleted = isDeleted;
  }

  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public Integer getType() {
    return type;
  }

  public void setType(Integer type) {
    this.type = type;
  }

  public String getAppId() {
    return appId;
  }

  public void setAppId(String appId) {
    this.appId = appId;
  }

  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public Long getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Long createTime) {
    this.createTime = createTime;
  }

  public String getLastModifiedBy() {
    return lastModifiedBy;
  }

  public void setLastModifiedBy(String lastModifiedBy) {
    this.lastModifiedBy = lastModifiedBy;
  }

  public Long getLastModifiedTime() {
    return lastModifiedTime;
  }

  public void setLastModifiedTime(Long lastModifiedTime) {
    this.lastModifiedTime = lastModifiedTime;
  }

  public Boolean getDelFlag() {
    return delFlag;
  }

  public void setDelFlag(Boolean delFlag) {
    this.delFlag = delFlag;
  }
}
