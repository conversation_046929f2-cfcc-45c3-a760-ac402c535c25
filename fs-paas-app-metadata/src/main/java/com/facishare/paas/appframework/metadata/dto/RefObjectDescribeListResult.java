package com.facishare.paas.appframework.metadata.dto;

import com.facishare.paas.metadata.ui.layout.ILayout;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Created by zhaopx on 2017/10/27.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RefObjectDescribeListResult {
    // 对象apiName
    String describeApiName;
    //字段apiName
    String fieldApiName;
    //字段label
    String fieldLabel;
    //对象列表
    Map objectDescribe;
    //布局
    ILayout layout;
    //描述拓展
    Map objectDescribeExt;
}
