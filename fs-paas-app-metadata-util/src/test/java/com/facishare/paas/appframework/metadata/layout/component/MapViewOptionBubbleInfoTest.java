package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.metadata.api.ISelectOption;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MapViewOptionBubbleInfoTest {

  private MapViewOptionBubbleInfo mapViewOptionBubbleInfo;
  
  @Mock
  private ISelectOption selectOption;

  @BeforeEach
  void setUp() {
    mapViewOptionBubbleInfo = new MapViewOptionBubbleInfo();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试MapViewOptionBubbleInfo的无参构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试无参构造函数")
  void testDefaultConstructor() {
    MapViewOptionBubbleInfo info = new MapViewOptionBubbleInfo();
    assertNotNull(info, "无参构造函数创建的对象不应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试MapViewOptionBubbleInfo的Map构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = Maps.newHashMap();
    map.put(IMapViewOptionBubbleInfo.COLOR, "#FF0000");
    map.put(IMapViewOptionBubbleInfo.LABEL, "红色选项");
    map.put(IMapViewOptionBubbleInfo.VALUE, "red");
    
    MapViewOptionBubbleInfo info = new MapViewOptionBubbleInfo(map);
    assertNotNull(info, "Map构造函数创建的对象不应为空");
    assertEquals("#FF0000", info.getColor(), "应正确设置颜色");
    assertEquals("红色选项", info.getLabel(), "应正确设置标签");
    assertEquals("red", info.getValue(), "应正确设置值");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试buildBySelectOption静态工厂方法
   */
  @Test
  @DisplayName("正常场景 - 测试buildBySelectOption静态工厂方法")
  void testBuildBySelectOption() {
    // 配置Mock对象
    when(selectOption.getLabel()).thenReturn("测试选项");
    when(selectOption.getValue()).thenReturn("test_value");
    
    IMapViewOptionBubbleInfo info = MapViewOptionBubbleInfo.buildBySelectOption(selectOption);
    
    assertNotNull(info, "静态工厂方法创建的对象不应为空");
    assertEquals("测试选项", info.getLabel(), "应正确设置标签");
    assertEquals("test_value", info.getValue(), "应正确设置值");
    assertEquals(IMapViewOptionBubbleInfo.DEFAULT_COLOR, info.getColor(), "应设置默认颜色");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getColor和setColor方法
   */
  @Test
  @DisplayName("正常场景 - 测试Color属性")
  void testColor() {
    // 初始状态应为null
    assertNull(mapViewOptionBubbleInfo.getColor(), "初始颜色应为null");
    
    // 设置颜色
    mapViewOptionBubbleInfo.setColor("#00FF00");
    assertEquals("#00FF00", mapViewOptionBubbleInfo.getColor(), "应正确设置颜色");
    
    // 设置为null
    mapViewOptionBubbleInfo.setColor(null);
    assertNull(mapViewOptionBubbleInfo.getColor(), "应支持设置为null");
    
    // 设置为空字符串
    mapViewOptionBubbleInfo.setColor("");
    assertEquals("", mapViewOptionBubbleInfo.getColor(), "应支持设置为空字符串");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getLabel和setLabel方法
   */
  @Test
  @DisplayName("正常场景 - 测试Label属性")
  void testLabel() {
    // 初始状态应为null
    assertNull(mapViewOptionBubbleInfo.getLabel(), "初始标签应为null");
    
    // 设置标签
    mapViewOptionBubbleInfo.setLabel("蓝色选项");
    assertEquals("蓝色选项", mapViewOptionBubbleInfo.getLabel(), "应正确设置标签");
    
    // 设置为null
    mapViewOptionBubbleInfo.setLabel(null);
    assertNull(mapViewOptionBubbleInfo.getLabel(), "应支持设置为null");
    
    // 设置为空字符串
    mapViewOptionBubbleInfo.setLabel("");
    assertEquals("", mapViewOptionBubbleInfo.getLabel(), "应支持设置为空字符串");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getValue和setValue方法
   */
  @Test
  @DisplayName("正常场景 - 测试Value属性")
  void testValue() {
    // 初始状态应为null
    assertNull(mapViewOptionBubbleInfo.getValue(), "初始值应为null");
    
    // 设置值
    mapViewOptionBubbleInfo.setValue("blue");
    assertEquals("blue", mapViewOptionBubbleInfo.getValue(), "应正确设置值");
    
    // 设置为null
    mapViewOptionBubbleInfo.setValue(null);
    assertNull(mapViewOptionBubbleInfo.getValue(), "应支持设置为null");
    
    // 设置为空字符串
    mapViewOptionBubbleInfo.setValue("");
    assertEquals("", mapViewOptionBubbleInfo.getValue(), "应支持设置为空字符串");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试所有属性的综合设置
   */
  @Test
  @DisplayName("正常场景 - 测试所有属性综合设置")
  void testAllProperties() {
    // 设置所有属性
    String color = "#FFFF00";
    String label = "黄色选项";
    String value = "yellow";
    
    mapViewOptionBubbleInfo.setColor(color);
    mapViewOptionBubbleInfo.setLabel(label);
    mapViewOptionBubbleInfo.setValue(value);
    
    // 验证所有属性
    assertEquals(color, mapViewOptionBubbleInfo.getColor(), "颜色应正确设置");
    assertEquals(label, mapViewOptionBubbleInfo.getLabel(), "标签应正确设置");
    assertEquals(value, mapViewOptionBubbleInfo.getValue(), "值应正确设置");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口实现
   */
  @Test
  @DisplayName("正常场景 - 测试接口实现")
  void testInterfaceImplementation() {
    // 验证实现了IMapViewOptionBubbleInfo接口
    assertTrue(mapViewOptionBubbleInfo instanceof IMapViewOptionBubbleInfo, 
               "应实现IMapViewOptionBubbleInfo接口");
    
    // 验证实现了JsonCompatible接口
    assertTrue(mapViewOptionBubbleInfo instanceof com.facishare.paas.metadata.api.JsonCompatible, 
               "应实现JsonCompatible接口");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试序列化版本号
   */
  @Test
  @DisplayName("正常场景 - 测试序列化版本号")
  void testSerialVersionUID() {
    // 验证类有序列化版本号（通过反射检查）
    try {
      java.lang.reflect.Field field = MapViewOptionBubbleInfo.class.getDeclaredField("serialVersionUID");
      field.setAccessible(true);
      long serialVersionUID = field.getLong(null);
      assertEquals(3194801278411039501L, serialVersionUID, "序列化版本号应正确");
    } catch (Exception e) {
      fail("应该有serialVersionUID字段");
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试继承的AbstractComponentInfoDocument功能
   */
  @Test
  @DisplayName("正常场景 - 测试继承功能")
  void testInheritedFunctionality() {
    // 验证继承自AbstractComponentInfoDocument
    assertTrue(mapViewOptionBubbleInfo instanceof AbstractComponentInfoDocument, 
               "应继承自AbstractComponentInfoDocument");
    
    // 通过Map构造函数测试继承的功能
    Map<String, Object> map = Maps.newHashMap();
    map.put("custom_field", "custom_value");
    map.put(IMapViewOptionBubbleInfo.COLOR, "#000000");
    
    MapViewOptionBubbleInfo info = new MapViewOptionBubbleInfo(map);
    assertEquals("#000000", info.getColor(), "应正确处理颜色");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试常见颜色值
   */
  @Test
  @DisplayName("正常场景 - 测试常见颜色值")
  void testCommonColors() {
    String[] commonColors = {
        "#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF", "#00FFFF",
        "#000000", "#FFFFFF", "#808080", "#800000", "#008000", "#000080"
    };
    
    for (String color : commonColors) {
      mapViewOptionBubbleInfo.setColor(color);
      assertEquals(color, mapViewOptionBubbleInfo.getColor(), "应正确处理颜色: " + color);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试特殊字符处理
   */
  @Test
  @DisplayName("边界场景 - 测试特殊字符处理")
  void testSpecialCharacters() {
    // 测试包含特殊字符的标签和值
    String specialLabel = "选项<>&\"'测试";
    String specialValue = "value_@#$%^&*()";
    
    mapViewOptionBubbleInfo.setLabel(specialLabel);
    mapViewOptionBubbleInfo.setValue(specialValue);
    
    assertEquals(specialLabel, mapViewOptionBubbleInfo.getLabel(), "应正确处理特殊字符标签");
    assertEquals(specialValue, mapViewOptionBubbleInfo.getValue(), "应正确处理特殊字符值");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试长字符串处理
   */
  @Test
  @DisplayName("边界场景 - 测试长字符串处理")
  void testLongStrings() {
    // 创建长字符串
    StringBuilder longLabel = new StringBuilder();
    StringBuilder longValue = new StringBuilder();
    
    for (int i = 0; i < 1000; i++) {
      longLabel.append("标签");
      longValue.append("value");
    }
    
    String longLabelStr = longLabel.toString();
    String longValueStr = longValue.toString();
    
    mapViewOptionBubbleInfo.setLabel(longLabelStr);
    mapViewOptionBubbleInfo.setValue(longValueStr);
    
    assertEquals(longLabelStr, mapViewOptionBubbleInfo.getLabel(), "应正确处理长标签");
    assertEquals(longValueStr, mapViewOptionBubbleInfo.getValue(), "应正确处理长值");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试buildBySelectOption方法的边界情况
   */
  @Test
  @DisplayName("边界场景 - 测试buildBySelectOption方法边界情况")
  void testBuildBySelectOption_BoundaryConditions() {
    // 测试空标签和值
    when(selectOption.getLabel()).thenReturn("");
    when(selectOption.getValue()).thenReturn("");
    
    IMapViewOptionBubbleInfo info = MapViewOptionBubbleInfo.buildBySelectOption(selectOption);
    
    assertEquals("", info.getLabel(), "应正确处理空标签");
    assertEquals("", info.getValue(), "应正确处理空值");
    assertEquals(IMapViewOptionBubbleInfo.DEFAULT_COLOR, info.getColor(), "应设置默认颜色");
    
    // 测试null标签和值
    when(selectOption.getLabel()).thenReturn(null);
    when(selectOption.getValue()).thenReturn(null);
    
    IMapViewOptionBubbleInfo info2 = MapViewOptionBubbleInfo.buildBySelectOption(selectOption);
    
    assertNull(info2.getLabel(), "应正确处理null标签");
    assertNull(info2.getValue(), "应正确处理null值");
    assertEquals(IMapViewOptionBubbleInfo.DEFAULT_COLOR, info2.getColor(), "应设置默认颜色");
  }
}
