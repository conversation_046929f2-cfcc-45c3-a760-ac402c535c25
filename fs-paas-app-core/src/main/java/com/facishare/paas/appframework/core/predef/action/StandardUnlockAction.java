package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ObjectLockStatus;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.flow.ApprovalFlowStatus;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.common.util.UdobjConstants.LOCK_STATUS_VALUE_LOCK;

/**
 * 2017/10/26 zhenglei
 * 解锁
 */
@Slf4j
//@Idempotent(serializer = Serializer.Type.java)
public class StandardUnlockAction extends BaseObjectLockAction {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.Unlock.getFunPrivilegeCodes();
    }

    @Override
    protected boolean isLock() {
        return false;
    }

    @Override
    protected ActionType getActionType() {
        return ActionType.Unlock;
    }

    protected List<IObjectData> filterByLockStatus(List<IObjectData> objectDataList) {
        return objectDataList.stream().filter(it -> ObjectDataExt.of(it).isLock()).collect(Collectors.toList());
    }

    @Override
    protected List<IObjectData> filterMasterData(List<IObjectData> masterDataList) {
        dataList = filterByLockStatus(masterDataList);
        if (CollectionUtils.notEmpty(dataList)) {
            //如果allObjectDataList中有从对象，那么就判断他们的主对象是否被锁定了，过滤掉主对象锁定的从对象的数据
            dataList = filterByMasterLocked(dataList, false);
        }
        if (CollectionUtils.notEmpty(dataList)) {
            //判断allObjectDataList中是否存在着有进行中的编辑审批流存在，
            dataList = filterDataByFlowStatus(dataList, false);
        }
        return dataList;
    }

    @Override
    protected List<IObjectData> filterDetailData(List<IObjectData> detailDataList) {
        detailDataList = filterByLockStatus(detailDataList);
        return detailDataList;
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.UNLOCK.getButtonApiName();
    }


    /**
     * 需求:解锁D时判断M是否锁定,返回过滤后的数据
     */
    protected List<IObjectData> filterByMasterLocked(List<IObjectData> objectDataList, boolean isCascadeDealDetailObj) {
        //只校验用户从页面上的解锁操作
        if (!RequestUtil.isCepRequest() && !RequestUtil.isOriginalCepRequest()) {
            return objectDataList;
        }

        Set<String> masterIdList = Sets.newHashSet();
        Map<String, String> failMapForMasterLocked = Maps.newHashMap();
        List<IObjectData> failDetailDataList = Lists.newArrayList();
        //存放主对象id<K>,及从对象IObjectData<V>,方便获取failIds
        Map<String, List<IObjectData>> detailDataMapForMasterId = Maps.newHashMap();
        ObjectDescribeExt.of(objectDescribe).getMasterDetailFieldDescribe().ifPresent(masterFieldDescribe -> {
            for (IObjectData detailData : objectDataList) {
                String masterId = ObjectDataExt.of(detailData).get(masterFieldDescribe.getApiName(), String.class);
                if (StringUtils.isNotBlank(masterId)) {
                    masterIdList.add(masterId);
                    detailDataMapForMasterId.computeIfAbsent(masterId, k -> Lists.newArrayList()).add(detailData);
                }
            }

            List<String> fields = Lists.newArrayList(ObjectLockStatus.LOCK_STATUS_API_NAME, IObjectData.TENANT_ID,
                    IObjectData.ID, IObjectData.NAME, IObjectData.DISPLAY_NAME, IObjectData.DESCRIBE_API_NAME);
            MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                    .user(actionContext.getUser())
                    .isSimple(true)
                    .projectionFields(fields)
                    .skipRelevantTeam(true)
                    .build();

            List<IObjectData> masterObjectDataList = serviceFacade.findObjectDataByIdsWithQueryContext(queryContext,
                    Lists.newArrayList(masterIdList), masterFieldDescribe.getTargetApiName());

            int failCount = 0;
            for (IObjectData masterObjectData : masterObjectDataList) {
                //M已锁,D不能解锁
                if (Objects.equals(ObjectDataExt.of(masterObjectData).getLockStatus(), LOCK_STATUS_VALUE_LOCK)) {
                    List<IObjectData> fails = detailDataMapForMasterId.get(masterObjectData.getId());
                    failCount += fails.size();
                    failDetailDataList.addAll(fails);
                    failMapForMasterLocked.putAll(fails.stream().collect(Collectors.toMap(DBRecord::getId,
                            x -> ObjectDataExt.of(x).getDisplayNameOrName(null), (x, y) -> x)));
                }
            }

            if (!isCascadeDealDetailObj) {
                if (failCount > 0) {
                    String failReason = String.join(",", failMapForMasterLocked.values());
                    failIdsReason.add(I18N.text(I18NKey.MASTER_OBJECT_IS_LOCK_CAN_NOT_UNLOCK_OPERATION_DATA, failReason));
                }
            }
        });
        objectDataList.removeIf(x -> failMapForMasterLocked.containsKey(x.getId()));
        failIds.addAll(failDetailDataList);
        return objectDataList;

    }

    protected List<IObjectData> filterDataByFlowStatus(List<IObjectData> currentObjectData, boolean isCascadeDealDetailObj) {
        // 从对象不处理
        if (isCascadeDealDetailObj) {
            return currentObjectData;
        }
        Map<String, IObjectData> objectDataMapForId = currentObjectData.stream().collect(Collectors.toMap(IObjectData::getId, x -> x));
        List<String> dataIdList = Lists.newArrayList(objectDataMapForId.keySet());
        Map<String, Tuple<String, ApprovalFlowStatus>> flowStatusMapForDataId = serviceFacade.batchGetApprovalTriggerType(actionContext.getUser(), dataIdList);
        // 不是审批流触发的锁定不处理
        if (CollectionUtils.empty(flowStatusMapForDataId)) {
            return currentObjectData;
        }

        // 流程触发锁定的数据，按钮触发条件分组 阶段任务、编辑、自定义按钮
        Map<String, List<IObjectData>> dataMap = flowStatusMapForDataId.entrySet().stream()
                .filter(x -> ApprovalFlowStatus.isFlowLockAndIsInProgress(x.getValue()))
                .collect(Collectors.groupingBy(x -> getGroupByKey(x.getValue()),
                        Collectors.mapping(x -> objectDataMapForId.get(x.getKey()), Collectors.toList())));

        List<IObjectData> failDataListForFlowStatus = dataMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        failIds.addAll(failDataListForFlowStatus);

        Map<String, String> failMapForFlowStatus = failDataListForFlowStatus.stream()
                .collect(Collectors.toMap(DBRecord::getId, IObjectData::getName, (x, y) -> x));
        currentObjectData.removeIf(x -> failMapForFlowStatus.containsKey(x.getId()));
        // 多类型数据 按之前的提示信息提示
        if (dataMap.size() > 1) {
            String failReason = String.join(",", failMapForFlowStatus.values());
            failIdsReason.add(I18N.text(I18NKey.EDIT_OR_CUSTOM_BUTTON_APPROVAL_FLOW_CAN_NOT_OPERATION_DATA_SHOULD_UNLOCK_OPERATION, failReason));
            return currentObjectData;
        }
        // 单类型数据 给出具体的提示
        Optional<Tuple<String, List<IObjectData>>> optional = dataMap.entrySet().stream()
                .filter(x -> CollectionUtils.notEmpty(x.getValue()))
                .map(x -> Tuple.of(x.getKey(), x.getValue()))
                .findFirst();
        optional.ifPresent(x -> {
            String failReason = x.getValue().stream().map(IObjectData::getName).collect(Collectors.joining(","));
            failErrorMessage(x.getKey(), failReason);
        });
        return currentObjectData;
    }

    private void failErrorMessage(String key, String failReason) {
        if ("stageChange".equals(key)) {
            failIdsReason.add(I18N.text(I18NKey.STAGE_CHANGE_APPROVAL_FLOW_CAN_NOT_OPERATION_DATA_SHOULD_UNLOCK_OPERATION, failReason));
            return;
        }
        if ("Update".equals(key)) {
            failIdsReason.add(I18N.text(I18NKey.EDIT_APPROVAL_FLOW_CAN_NOT_OPERATION_DATA_SHOULD_UNLOCK_OPERATION, failReason));
            return;
        }
        failIdsReason.add(I18N.text(I18NKey.CUSTOM_BUTTON_APPROVAL_FLOW_CAN_NOT_OPERATION_DATA_SHOULD_UNLOCK_OPERATION, failReason));
    }

    private String getGroupByKey(Tuple<String, ApprovalFlowStatus> value) {
        return StringUtils.endsWith(value.getKey(), "__c") ? "CUSTOM_BUTTON" : value.getKey();
    }

}
