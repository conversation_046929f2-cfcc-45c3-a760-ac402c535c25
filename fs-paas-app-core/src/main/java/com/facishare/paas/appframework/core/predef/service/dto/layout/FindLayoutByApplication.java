package com.facishare.paas.appframework.core.predef.service.dto.layout;

import com.facishare.paas.appframework.core.model.LayoutDocument;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON>ju on 2024/5/27
 */
public interface FindLayoutByApplication {

    @Data
    class Arg {
        private String appId;
    }

    @Data
    @Builder
    class Result {
        private List<LayoutDocument> layouts;
    }
}
