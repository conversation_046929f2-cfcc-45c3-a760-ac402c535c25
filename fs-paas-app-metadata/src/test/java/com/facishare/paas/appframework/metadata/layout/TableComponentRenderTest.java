package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.TableComponentExt;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class TableComponentRenderTest {

  @Mock
  private User user;

  @Mock
  private ObjectDescribeExt describeExt;

  @Mock
  private TableComponentExt tableComponentExt;

  @Mock
  private FunctionPrivilegeService functionPrivilegeService;

  @Mock
  private ILayout layout;

  @Mock
  private IFieldDescribe fieldDescribe;

  @Mock
  private ITableColumn tableColumn;

  @Mock
  private IFormField formField;

  private List<ITableColumn> mockIncludeFields;
  private List<IFormField> mockFormFields;
  private Set<String> mockUnauthorizedFields;

  @BeforeEach
  void setUp() {
    mockIncludeFields = Lists.newArrayList();
    mockIncludeFields.add(tableColumn);

    mockFormFields = Lists.newArrayList();
    mockFormFields.add(formField);

    mockUnauthorizedFields = Sets.newHashSet("unauthorized_field");

    // 设置基本的mock行为
    when(user.getTenantId()).thenReturn("test_tenant");
    when(describeExt.getApiName()).thenReturn("test_object");
    when(tableComponentExt.getIncludeFields()).thenReturn(mockIncludeFields);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试TableComponentRender构造函数正常场景
   */
  @Test
  @DisplayName("正常场景 - 构造TableComponentRender对象")
  void testTableComponentRenderConstructor_Success() {
    // 执行被测试方法
    TableComponentRender render = TableComponentRender.builder()
        .user(user)
        .describeExt(describeExt)
        .tableComponentExt(tableComponentExt)
        .functionPrivilegeService(functionPrivilegeService)
        .skipCorrectLabel(false)
        .replaceIfNewTabComponent(true)
        .build();

    // 验证结果
    assertNotNull(render);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试render方法正常场景
   */
  @Test
  @DisplayName("正常场景 - 执行表格组件渲染")
  void testRender_Success() {
    // 准备测试数据
    when(functionPrivilegeService.getUnauthorizedFields(user, "test_object")).thenReturn(mockUnauthorizedFields);
    when(tableComponentExt.enableNewLayout()).thenReturn(false);
    when(describeExt.stream()).thenReturn(Lists.newArrayList(fieldDescribe).stream());
    when(fieldDescribe.isActive()).thenReturn(true);
    when(fieldDescribe.getApiName()).thenReturn("test_field");
    when(describeExt.containsField("test_field")).thenReturn(true);

    // 执行被测试方法
    TableComponentRender render = TableComponentRender.builder()
        .user(user)
        .describeExt(describeExt)
        .tableComponentExt(tableComponentExt)
        .functionPrivilegeService(functionPrivilegeService)
        .skipCorrectLabel(false)
        .replaceIfNewTabComponent(true)
        .build();

    assertDoesNotThrow(() -> {
      render.render();
    });

    // 验证方法调用
    verify(tableComponentExt).adjustFieldRenderType(describeExt);
    verify(tableComponentExt).setDefaultFieldListIfEmpty();
    verify(tableComponentExt).correctLabel(describeExt);
    verify(tableComponentExt).replaceByNewTableComponent();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试render方法跳过标签修正
   */
  @Test
  @DisplayName("正常场景 - 跳过标签修正的渲染")
  void testRender_SkipCorrectLabel() {
    // 准备测试数据
    when(functionPrivilegeService.getUnauthorizedFields(user, "test_object")).thenReturn(mockUnauthorizedFields);
    when(tableComponentExt.enableNewLayout()).thenReturn(false);
    when(describeExt.stream()).thenReturn(Lists.newArrayList(fieldDescribe).stream());
    when(fieldDescribe.isActive()).thenReturn(true);
    when(fieldDescribe.getApiName()).thenReturn("test_field");
    when(describeExt.containsField("test_field")).thenReturn(true);

    // 执行被测试方法
    TableComponentRender render = TableComponentRender.builder()
        .user(user)
        .describeExt(describeExt)
        .tableComponentExt(tableComponentExt)
        .functionPrivilegeService(functionPrivilegeService)
        .skipCorrectLabel(true)
        .replaceIfNewTabComponent(false)
        .build();

    assertDoesNotThrow(() -> {
      render.render();
    });

    // 验证跳过了标签修正
    verify(tableComponentExt, never()).correctLabel(describeExt);
    verify(tableComponentExt, never()).replaceByNewTableComponent();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试render方法带布局参数
   */
  @Test
  @DisplayName("正常场景 - 带布局参数的渲染")
  void testRender_WithLayout() {
    // 准备测试数据
    when(layout.isShowFieldname()).thenReturn(true);
    when(tableComponentExt.enableNewLayout()).thenReturn(false);
    when(functionPrivilegeService.getUnauthorizedFields(user, "test_object")).thenReturn(mockUnauthorizedFields);
    when(describeExt.stream()).thenReturn(Lists.newArrayList(fieldDescribe).stream());
    when(fieldDescribe.isActive()).thenReturn(true);
    when(fieldDescribe.getApiName()).thenReturn("test_field");
    when(describeExt.containsField("test_field")).thenReturn(true);

    // 执行被测试方法
    TableComponentRender render = TableComponentRender.builder()
        .user(user)
        .describeExt(describeExt)
        .tableComponentExt(tableComponentExt)
        .functionPrivilegeService(functionPrivilegeService)
        .skipCorrectLabel(false)
        .replaceIfNewTabComponent(true)
        .build();

    assertDoesNotThrow(() -> {
      render.render(layout);
    });

    // 验证方法调用
    verify(layout).isShowFieldname();
    verify(tableComponentExt).setIncludeFields(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试render方法带合并字段参数
   */
  @Test
  @DisplayName("正常场景 - 带合并字段参数的渲染")
  void testRender_WithMergeFields() {
    // 准备测试数据 - 避免触发copyFieldSectionToIncludeFields中的类型转换
    when(describeExt.isSFAObject()).thenReturn(false); // 设置为false避免类型转换
    when(tableComponentExt.enableNewLayout()).thenReturn(false);
    when(tableComponentExt.getFieldsInFirstSection()).thenReturn(Lists.newArrayList()); // 返回空列表
    when(functionPrivilegeService.getUnauthorizedFields(user, "test_object")).thenReturn(mockUnauthorizedFields);
    when(describeExt.stream()).thenReturn(Lists.newArrayList(fieldDescribe).stream());
    when(fieldDescribe.isActive()).thenReturn(true);
    when(fieldDescribe.getApiName()).thenReturn("test_field");
    when(describeExt.containsField("test_field")).thenReturn(true);

    // 执行被测试方法
    TableComponentRender render = TableComponentRender.builder()
        .user(user)
        .describeExt(describeExt)
        .tableComponentExt(tableComponentExt)
        .functionPrivilegeService(functionPrivilegeService)
        .skipCorrectLabel(false)
        .replaceIfNewTabComponent(true)
        .build();

    assertDoesNotThrow(() -> {
      render.render(true);
    });

    // 验证方法调用
    verify(tableComponentExt).adjustFieldRenderType(describeExt);
    verify(tableComponentExt).setDefaultFieldListIfEmpty();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeInactiveFields方法
   */
  @Test
  @DisplayName("正常场景 - 移除非活跃字段")
  void testRemoveInactiveFields_Success() {
    // 准备测试数据
    when(tableComponentExt.enableNewLayout()).thenReturn(false);
    when(describeExt.stream()).thenReturn(Lists.newArrayList(fieldDescribe).stream());
    when(fieldDescribe.isActive()).thenReturn(false);
    when(fieldDescribe.getApiName()).thenReturn("inactive_field");
    when(describeExt.containsField("test_field")).thenReturn(true);

    // 执行被测试方法
    TableComponentRender render = TableComponentRender.builder()
        .user(user)
        .describeExt(describeExt)
        .tableComponentExt(tableComponentExt)
        .functionPrivilegeService(functionPrivilegeService)
        .build();

    TableComponentRender result = render.removeInactiveFields();

    // 验证结果
    assertNotNull(result);
    assertSame(render, result);
    verify(tableComponentExt).removeFields(any(Set.class));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试renderShowImageAndTag方法
   */
  @Test
  @DisplayName("正常场景 - 渲染显示图片和标签")
  void testRenderShowImageAndTag_Success() {
    // 执行被测试方法
    TableComponentRender render = TableComponentRender.builder()
        .user(user)
        .describeExt(describeExt)
        .tableComponentExt(tableComponentExt)
        .functionPrivilegeService(functionPrivilegeService)
        .build();

    TableComponentRender result = render.renderShowImageAndTag();

    // 验证结果
    assertNotNull(result);
    assertSame(render, result);
    verify(tableComponentExt).initRenderShowImageAndTag();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeIfEmpty方法
   */
  @Test
  @DisplayName("正常场景 - 移除空字段")
  void testRemoveIfEmpty_Success() {
    // 执行被测试方法
    TableComponentRender render = TableComponentRender.builder()
        .user(user)
        .describeExt(describeExt)
        .tableComponentExt(tableComponentExt)
        .functionPrivilegeService(functionPrivilegeService)
        .build();

    TableComponentRender result = render.removeIfEmpty();

    // 验证结果
    assertNotNull(result);
    assertSame(render, result);
    verify(tableComponentExt).removeIfEmpty(any(Set.class));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setDefaultFieldListIfEmpty方法
   */
  @Test
  @DisplayName("正常场景 - 设置默认字段列表")
  void testSetDefaultFieldListIfEmpty_Success() {
    // 执行被测试方法
    TableComponentRender render = TableComponentRender.builder()
        .user(user)
        .describeExt(describeExt)
        .tableComponentExt(tableComponentExt)
        .functionPrivilegeService(functionPrivilegeService)
        .build();

    TableComponentRender result = render.setDefaultFieldListIfEmpty();

    // 验证结果
    assertNotNull(result);
    assertSame(render, result);
    verify(tableComponentExt).setDefaultFieldListIfEmpty();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试Builder模式的完整性
   */
  @Test
  @DisplayName("正常场景 - 测试Builder模式完整性")
  void testTableComponentRender_BuilderPattern() {
    // 测试Builder的链式调用
    assertDoesNotThrow(() -> {
      TableComponentRender render = TableComponentRender.builder()
          .user(user)
          .describeExt(describeExt)
          .tableComponentExt(tableComponentExt)
          .functionPrivilegeService(functionPrivilegeService)
          .skipCorrectLabel(false)
          .replaceIfNewTabComponent(true)
          .build();
      
      assertNotNull(render);
    });
  }
}
