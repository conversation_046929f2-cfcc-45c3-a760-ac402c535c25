package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.function.dto.FilterRecordType;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;


import java.util.List;
import java.util.Objects;

import static com.facishare.paas.appframework.common.util.AppFrameworkConfig.getRecordTypeFunc;

/**
 * <AUTHOR>
 * @create 2022-03-24-15:03
 */
public abstract class AbstractStandardController<A, R> extends PreDefineController<A, R> {


    @Override
    protected void before(A arg) {
        super.before(arg);
    }

}
