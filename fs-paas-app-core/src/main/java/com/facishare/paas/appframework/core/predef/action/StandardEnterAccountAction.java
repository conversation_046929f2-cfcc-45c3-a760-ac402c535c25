package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.FundAccountBaseService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.domain.EnterAccountActionDomainPlugin;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

public class StandardEnterAccountAction extends AbstractStandardAction<CustomButtonAction.Arg, CustomButtonAction.Result> {
    protected FundAccountBaseService fundAccountBaseService = SpringUtil.getContext().getBean(FundAccountBaseService.class);
    private PlatformTransactionManager tm = (PlatformTransactionManager) SpringUtil.getContext().getBean("paasMetadataTransactionManager");
    protected IObjectDescribe newCustomerAccountDescribe;
    protected IObjectDescribe accountTransactionFlowDescribe;
    protected IObjectData objectData;
    protected IObjectData dbObjectData;
    protected IObjectData argData;
    protected IObjectData fundAccountData;
    private Map<String, Object> updateFieldMap = Maps.newHashMap();
    private RLock enterAccountLock;
    private FundAccountBaseService.FundAuthConfigModel accessAuthModel;
    private String customerFieldName;
    private String enterAccountAmountFieldName;

    String fundAccountFieldName = FundAccountBaseService.Const.FUND_ACCOUNT;
    String enterIntoAccountFieldName = FundAccountBaseService.Const.ENTER_INTO_ACCOUNT;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.ENTER_ACCOUNT.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(CustomButtonAction.Arg arg) {
        String dataId = arg.getObjectDataId();
        return StringUtils.isEmpty(dataId) ? Lists.newArrayList() : Lists.newArrayList(dataId);
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.ENTER_ACCOUNT.getButtonApiName();
    }

    @Override
    protected Map<String, Object> getArgs() {
        return Objects.isNull(arg.getArgs()) ? Maps.newHashMap() : ObjectDataExt.of(arg.getArgs().toObjectData()).toMap();
    }

    @Override
    protected EnterAccountActionDomainPlugin.Arg buildDomainPluginArg(String method, List<String> recordTypeList) {
        return EnterAccountActionDomainPlugin.Arg.builder().dbObjectData(ObjectDataDocument.of(this.dbObjectData)).argData(ObjectDataDocument.of(this.argData))
                .fundAccountData(ObjectDataDocument.of(this.fundAccountData)).customerFieldName(this.customerFieldName).enterAccountAmountFieldName(this.enterAccountAmountFieldName).build();
    }

    @Override
    protected void before(CustomButtonAction.Arg arg) {
        super.before(arg);
        objectData = dataList.get(0);
        dbObjectData = ObjectDataExt.of(objectData).copy();
        this.argData = getArgData();
        String key = String.format("EnterCancel_%s_%s_%s", actionContext.getTenantId(), objectData.getDescribeApiName(), objectData.getId());
        enterAccountLock = infraServiceFacade.tryLock(0, 10, TimeUnit.SECONDS, key);
        if (Objects.isNull(enterAccountLock)) {
            log.warn("duplicate entry account key:{}", key);
            throw new ValidateException(I18N.text(FundAccountBaseService.Const.NOT_DUPLICATE_ENTRY, objectData.getName()));
        }
        this.accessAuthModel = fundAccountBaseService.getAccessAuthInfo(actionContext.getUser(), actionContext.getObjectApiName());
        this.customerFieldName = accessAuthModel.getCustomerFieldName();
        this.enterAccountAmountFieldName = accessAuthModel.getEnterAccountAmountFieldName();

        String fundAccountId = argData.get(fundAccountFieldName, String.class);
        this.fundAccountData = serviceFacade.findObjectData(actionContext.getUser(), fundAccountId, "FundAccountObj");

        updateFieldMap.put(fundAccountFieldName, fundAccountId);
        updateFieldMap.put(enterIntoAccountFieldName, true);

        check();
    }

    protected void check() {
        FundAccountBaseService.checkLifeStatus(objectData);
        String customerId = objectData.get(customerFieldName, String.class);
        if (StringUtils.isEmpty(customerId)) {
            throw new ValidateException(I18N.text(FundAccountBaseService.Const.NO_CUSTOMER_NOT_ENTRY));
        }
        //校验：fundAccountId是否在入账授权的授权明细里面
        String objectApiName = actionContext.getObjectApiName();
        String fundAccountId = argData.get(fundAccountFieldName, String.class);

        BigDecimal enterAccountAmount = objectData.get(enterAccountAmountFieldName, BigDecimal.class, BigDecimal.ZERO);
        boolean enterIntoAccount = objectData.get(enterIntoAccountFieldName, Boolean.class, Boolean.FALSE);
        if (enterAccountAmount.compareTo(BigDecimal.ZERO) == 0 || (enterAccountAmount.compareTo(BigDecimal.ZERO) < 0 && !enableNegativeEnterAccount())) {
            throw new ValidateException(I18N.text(FundAccountBaseService.Const.ENTER_AMOUNT_MUST_GT_ZERO));
        } else if (enterIntoAccount) {
            throw new ValidateException(I18N.text(FundAccountBaseService.Const.NOT_DUPLICATE_ENTRY, objectData.getName()));
        }
        fundAccountBaseService.checkFundAccountId(actionContext.getTenantId(), objectApiName, fundAccountId, fundAccountData.getName());
    }

    @Override
    protected IObjectData getPreObjectData() {
        return this.objectData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return this.objectData;
    }

    private boolean enableNegativeEnterAccount() {
        return FundAccountBaseService.enableNegativeEnterAccount(actionContext.getTenantId(), actionContext.getObjectApiName());
    }

    protected boolean isCreditFundAccount() {
        String accessModule = this.fundAccountData.get(FundAccountBaseService.Const.FIELD_ACCESS_MODULE, String.class);
        return "3".equals(accessModule);
    }

    @Override
    protected CustomButtonAction.Result doAct(CustomButtonAction.Arg arg) {
        CustomButtonAction.Result result = new CustomButtonAction.Result();
        String customerId = objectData.get(customerFieldName, String.class);
        BigDecimal enterAccountAmount = objectData.get(enterAccountAmountFieldName, BigDecimal.class, BigDecimal.ZERO);

        String fundAccountId = argData.get(fundAccountFieldName, String.class);
        Boolean isAutoEnterAccount = argData.get(FundAccountBaseService.Const.IS_AUTO_ENTER_ACCOUNT, Boolean.class, false);

        IObjectData customerAccountData = fundAccountBaseService.getOrCreateNewCustomerAccountData(actionContext.getUser(), fundAccountId, customerId);
        //更新objectData
        this.updateFieldMap.forEach(objectData::set);
        log.info("user:{},objectApiName:{},dataId:{},name:{},version:{}", actionContext.getUser(), objectData.getDescribeApiName(), objectData.getId(), objectData.getName(), objectData.getVersion());

        if (FundAccountBaseService.isReplenishmentQuantity(fundAccountData)) {
            serviceFacade.updateObjectData(actionContext.getUser(), objectData);
        } else {
            boolean enableCustomerAccountExceed = fundAccountBaseService.enableCustomerAccountExceed(actionContext.getUser());
            boolean accountCheckRuleEnable = fundAccountBaseService.accountCheckRuleEnable(actionContext.getUser());
            Map<String, Map<String, Object>> customerAccountHandleMap = FundAccountBaseService.buildCustomerUpdateMapAndCheck(customerAccountData, enterAccountAmount, isCreditFundAccount(), accountCheckRuleEnable, enableCustomerAccountExceed);
            List<String> customerAccountUpdateFields = Lists.newArrayList(customerAccountHandleMap.getOrDefault(customerAccountData.getId(), Maps.newHashMap()).keySet());
            IObjectData toAddIncomeFlowData = FundAccountBaseService.toIncomeFlowData(actionContext.getUser(), fundAccountData, customerId, customerAccountData, objectData, enterAccountAmount, accessAuthModel, isAutoEnterAccount);
            TransactionTemplate template = new TransactionTemplate(tm);
            List<IObjectData> updateNewCustomerAccountDataList = Lists.newArrayList();
            template.execute(new TransactionCallbackWithoutResult() {
                @Override
                protected void doInTransactionWithoutResult(TransactionStatus status) {
                    List<IObjectData> newCustomerAccountList = serviceFacade.batchUpdateByFields(actionContext.getUser(), Lists.newArrayList(customerAccountData), customerAccountUpdateFields, customerAccountHandleMap);
                    updateNewCustomerAccountDataList.addAll(newCustomerAccountList);
                    serviceFacade.updateObjectData(actionContext.getUser(), objectData);
                    serviceFacade.saveObjectData(actionContext.getUser(), toAddIncomeFlowData);
                }
            });

            //记录客户账户余额更新记录
            if (!updateNewCustomerAccountDataList.isEmpty()) {
                Map<String, Object> newCustomerAccountUpdateFieldMap = Maps.newHashMap();
                IObjectData updateNewCustomerAccountData = updateNewCustomerAccountDataList.get(0);
                customerAccountUpdateFields.forEach(k -> newCustomerAccountUpdateFieldMap.put(k, updateNewCustomerAccountData.get(k)));
                serviceFacade.log(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, newCustomerAccountDescribe, updateNewCustomerAccountData, newCustomerAccountUpdateFieldMap, customerAccountData);
            }
            serviceFacade.log(actionContext.getUser(), EventType.ADD, ActionType.Modify, accountTransactionFlowDescribe, toAddIncomeFlowData);
            //流水新建后置函数
            fundAccountBaseService.triggerFunctionByInnerAdd(actionContext.getUser(), Lists.newArrayList(toAddIncomeFlowData), "post");
        }
        //记录日志
        serviceFacade.log(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, objectDescribe, objectData, updateFieldMap, dbObjectData);
        return result;
    }

    @Override
    protected void findDescribe() {
        Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(actionContext.getTenantId(), Lists.newArrayList(actionContext.getObjectApiName(), FundAccountBaseService.Const.NEW_CUSTOMER_ACCOUNT_OBJ, FundAccountBaseService.Const.ACCOUNT_TRANSACTION_FLOW_OBJ));
        objectDescribe = describeMap.get(actionContext.getObjectApiName());
        newCustomerAccountDescribe = describeMap.get(FundAccountBaseService.Const.NEW_CUSTOMER_ACCOUNT_OBJ);
        accountTransactionFlowDescribe = describeMap.get(FundAccountBaseService.Const.ACCOUNT_TRANSACTION_FLOW_OBJ);
    }

    protected IObjectData getArgData() {
        ObjectDataDocument args = this.arg.getArgs();
        if (args == null) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        IObjectData argParamData = new ObjectData();
        argParamData.setTenantId(actionContext.getTenantId());
        argParamData.setDescribeApiName(actionContext.getObjectApiName());
        ObjectDataExt.of(args).toMap().forEach((k, v) -> {
            String fieldName = StringUtils.substringAfter(k, "form_");
            argParamData.set(fieldName, v);
        });
        return argParamData;
    }

    @Override
    protected void finallyDo() {
        if (Objects.nonNull(enterAccountLock)) {
            infraServiceFacade.unlock(enterAccountLock);
        }
        super.finallyDo();
    }
}
