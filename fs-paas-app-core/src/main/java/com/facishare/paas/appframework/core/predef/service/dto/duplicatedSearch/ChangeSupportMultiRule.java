package com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface ChangeSupportMultiRule {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        @JSONField(name = "use_multi_rule")
        @JsonProperty("use_multi_rule")
        private Boolean useMultiRule;

        @JsonProperty("object_describe_api_name")
        @JSONField(name = "object_describe_api_name")
        private String objectDescribeApiName;
    }

    class Result {

    }
}
