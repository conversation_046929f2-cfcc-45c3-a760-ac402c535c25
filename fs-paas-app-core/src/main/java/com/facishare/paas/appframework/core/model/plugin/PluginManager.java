package com.facishare.paas.appframework.core.model.plugin;

import com.facishare.paas.appframework.core.model.RequestType;

import java.util.List;

/**
 * Created by zhouwr on 2022/4/14.
 */
public interface PluginManager {
    void register(Plugin plugin);

    <T extends Plugin> T getLocalPlugin(String pluginApiName);

    <T extends Plugin> T getPlugin(RequestType requestType, String code, String pluginApiName);

    <T extends Plugin> List<T> getPluginList(RequestType requestType, String objectApiName, String code, String method, String tenantId,String agentType);
}
