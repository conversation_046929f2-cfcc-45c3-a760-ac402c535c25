package com.facishare.paas.appframework.core.predef.service.personalAuth;

import com.beust.jcommander.internal.Lists;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.GetAllPluginVo;
import com.facishare.paas.appframework.core.predef.service.dto.personalAuth.*;
import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.enums.PackagePluginAppType;
import com.facishare.paas.appframework.core.predef.service.onlinedoc.PackagePluginUtils;
import com.facishare.paas.appframework.metadata.FileStoreService;
import com.facishare.paas.appframework.metadata.onlinedoc.PackagePluginLogicService;
import com.facishare.paas.appframework.metadata.personalAuth.PersonalAuthLogicService;
import com.facishare.paas.appframework.metadata.personalAuth.model.ActionCallback;
import com.facishare.paas.appframework.metadata.personalAuth.model.GetPersonalAuthUrl;
import com.facishare.paas.appframework.metadata.repository.model.PackagePluginEntity;
import com.facishare.paas.appframework.metadata.repository.model.PersonalAuthEntity;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> create by liy on 2024/6/13
 */
@Service
@ServiceModule("personalAuth")
public class PersonalAuthService {

    @Autowired
    private PersonalAuthLogicService personalAuthLogicService;
    @Autowired
    private PackagePluginLogicService packagePluginLogicService;
    @Autowired
    private FileStoreService fileStoreService;

    /**
     * 获取自己所有授权记录
     * /EM1HNCRM/API/v1/object/personalAuth/service/getAllAuth
     * 1.查询白名单插件
     * 2.查询个人已授权记录
     * 3.查询已授权插件
     * 4.过滤出未授权插件
     * 5.合并信息
     */
    @ServiceMethod("getAllAuth")
    public GetAllAuthVo.Result getAllAuth(GetAllAuthVo.Arg arg, ServiceContext context) {
        GetAllAuthVo.Result result = new GetAllAuthVo.Result();
        //1
        List<String> appTypes = (List<String>) AppFrameworkConfig.getPluginConfig().getOrDefault("package_app_types", Lists.newArrayList());
        List<PackagePluginEntity> pluginListInAppTypes = CollectionUtils.empty(appTypes)
                ? Lists.newArrayList()
                : packagePluginLogicService.getPluginsByAppTypes(context.getUser(), appTypes);
        //2
        List<PersonalAuthEntity> authList = personalAuthLogicService.getAllAuth(context.getUser(),
                arg.getPageNumber(),
                arg.getPageSize() + 1);
        result.setHasMore(authList.size() > arg.getPageSize());
        List<PersonalAuthEntity> onePageAuthList = authList.subList(0, Math.min(authList.size(), arg.getPageSize()));
        List<String> hasAuthPluginApiNameList = onePageAuthList.stream().map(a -> a.getPluginApiName()).collect(Collectors.toList());
        //3
        Map<String, PackagePluginEntity> pluginMap = Maps.newHashMap();
        List<PackagePluginEntity> pluginList = CollectionUtils.empty(hasAuthPluginApiNameList)
                ? Lists.newArrayList()
                : packagePluginLogicService.getPluginsByApiNames(context.getUser(), hasAuthPluginApiNameList);
        pluginMap.putAll(pluginList.stream().collect(Collectors.toMap(PackagePluginEntity::getPluginApiName, x -> x)));
        //4
        List<PackagePluginEntity> unauthPluginList = pluginListInAppTypes.stream()
                .filter(x -> !hasAuthPluginApiNameList.contains(x.getPluginApiName()))
                .collect(Collectors.toList());
        //5
        result.setAuthList(onePageAuthList.stream().map(x -> {
            GetAllAuthVo.AuthItem item = new GetAllAuthVo.AuthItem();
            item.setAppType(x.getAppType());
            //规则：personal_auth.plugin.应用类型
            item.setAppName(I18NExt.getOrDefault(PackagePluginAppType.of(x.getAppType()).getNameKey(), x.getAppType()));
            item.setCreateTime(x.getCreateTime());
            item.setExpiredTime(x.getExpiredTime());
            item.setPluginApiName(x.getPluginApiName());
            //补充icon
            PackagePluginEntity pluginEntity = pluginMap.get(x.getPluginApiName());
            if (pluginEntity == null) {
                return null;
            }
            item.setIcon(pluginEntity.getIcon());
            item.setIconUrl(pluginEntity.isHttpIcon() ? pluginEntity.getIcon() : fileStoreService.generateCpathAccessUrl(context.getUser(), pluginEntity.getIcon(), pluginEntity.getIconSign()));
            item.setPluginName(PackagePluginUtils.name2I18n(pluginEntity.getPluginApiName(), pluginEntity.getDefineType(), pluginEntity.getName()));
            item.setPluginExtraInfo(pluginEntity.getExtraInfo());
            return item;
        }).filter(x -> x != null).collect(Collectors.toList()));
        result.setPluginList(unauthPluginList.stream().map(x -> {
            GetAllPluginVo.PluginItem item = new GetAllPluginVo.PluginItem();
            item.setDefineType(x.getDefineType());
            item.setPluginName(PackagePluginUtils.name2I18n(x.getPluginApiName(), x.getDefineType(), x.getName()));
            item.setIcon(x.getIcon());
            item.setAppType(x.getAppType());
            item.setAppName(I18NExt.getOrDefault(PackagePluginAppType.of(x.getAppType()).getNameKey(), x.getAppType()));
            item.setIconUrl(x.isHttpIcon() ? x.getIcon() : fileStoreService.generateCpathAccessUrl(context.getUser(), x.getIcon(), x.getIconSign()));
            item.setBinding(x.getBinding());
            item.setActive(x.getActive());
            item.setPluginApiName(x.getPluginApiName());
            item.setExtraInfo(x.getExtraInfo());
            return item;
        }).collect(Collectors.toList()));
        return result;
    }

    @ServiceMethod("checkAuth")
    public CheckAuthVo.Result checkAuth(CheckAuthVo.Arg arg, ServiceContext context) {
        PersonalAuthEntity entity = personalAuthLogicService.getCurrentPersonalAuth(context.getUser(),
                PackagePluginAppType.ONLINE_DOC.getType(),
                arg.getPluginApiName());
        if (entity == null
                || entity.getExpiredTime() <= 20 * 1000 + System.currentTimeMillis()) {
            GetPersonalAuthUrl.Result urlResult = personalAuthLogicService.getAuthUrl(context.getUser(),
                    PackagePluginAppType.ONLINE_DOC.getType(),
                    arg.getPluginApiName());
            CheckAuthVo.Result result = new CheckAuthVo.Result();
            result.setHasAuth(false);
            result.setAuthUrl(urlResult.getAuthUrl());
            return result;
        }
        CheckAuthVo.Result result = new CheckAuthVo.Result();
        result.setHasAuth(true);
        return result;
    }

    /**
     * 添加授权记录
     */
    @ServiceMethod("addAuth")
    public AddAuthVo.Result addAuth(AddAuthVo.Arg arg, ServiceContext context) {
        PersonalAuthEntity entity = personalAuthLogicService.addAuth(context.getUser(),
                arg.getAppType(),
                arg.getPluginApiName(),
                arg.getRuntimeData(),
                arg.getExpiredTime());
        AddAuthVo.Result result = new AddAuthVo.Result();
        result.setSuccess(true);
        return result;
    }

    /**
     * 删除授权记录
     */
    @ServiceMethod("deleteAuth")
    public DeleteAuthVo.Result deleteAuth(DeleteAuthVo.Arg arg, ServiceContext context) {
        PersonalAuthEntity entity = personalAuthLogicService.deleteAuth(context.getUser(),
                arg.getAppType(),
                arg.getPluginApiName());
        DeleteAuthVo.Result result = new DeleteAuthVo.Result();
        result.setSuccess(true);
        return result;
    }

    /**
     * 获取授权url
     */
    @ServiceMethod("getAuthUrl")
    public GetPersonalAuthUrlVo.Result getAuthUrl(GetPersonalAuthUrlVo.Arg arg, ServiceContext context) {
        GetPersonalAuthUrl.Result urlResult = personalAuthLogicService.getAuthUrl(context.getUser(), arg.getAppType(), arg.getPluginApiName());
        GetPersonalAuthUrlVo.Result result = new GetPersonalAuthUrlVo.Result();
        result.setAuthUrl(urlResult.getAuthUrl());
        return result;
    }

    /**
     * 获取授权token
     * 内部接口 /API/v1/inner/object/personalAuth/service/actionCallback
     */
    @ServiceMethod("actionCallback")
    public ActionCallbackVo.Result actionCallback(ActionCallbackVo.Arg arg, ServiceContext context) {
        ActionCallback.Result actionResult = personalAuthLogicService.actionCallback(
                context.getUser(),
                arg.getAppType(),
                arg.getPluginApiName(),
                arg.getCode());
        ActionCallbackVo.Result result = new ActionCallbackVo.Result();
        result.setErrorCode(actionResult.getErrorCode());
        result.setErrorMessage(actionResult.getErrorMessage());
        return result;
    }
}
