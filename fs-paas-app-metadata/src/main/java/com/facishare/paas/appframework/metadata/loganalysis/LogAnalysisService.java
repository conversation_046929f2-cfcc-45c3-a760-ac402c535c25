package com.facishare.paas.appframework.metadata.loganalysis;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.repository.model.EmployeeLoginUsage;
import com.facishare.paas.appframework.metadata.repository.model.EmployeeObjectUsage;

import java.util.List;

public interface LogAnalysisService {
    List<EmployeeLoginUsage> queryEmployeeLoginLogs();

    void insertEmployeeLoginLogs(User user, List<EmployeeLoginUsage> employeeLoginUsages);

    List<EmployeeObjectUsage> queryEmployObjectUsage();

    void insertEmployeeObjectUsage(User user, List<EmployeeObjectUsage> employeeObjectUsages);
}
