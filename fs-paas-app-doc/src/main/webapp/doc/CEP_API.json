{"swagger": "2.0", "info": {"title": "AppFrameWork CEP API", "description": "PaaS 应用框架CEP API", "version": "1.0"}, "host": "************:8234", "schemes": ["FCP"], "basePath": "/FHH/EM1HCRMUdobj", "produces": ["application/json"], "paths": {"/function/create": {"post": {"tags": ["Function API"], "summary": "create custom function", "description": "创建自定义函数", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/CreateFunctionArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/CreateFunctionResult"}}}}}, "/function/update": {"post": {"tags": ["Function API"], "summary": "update custom function", "description": "编辑自定义函数", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/CreateFunctionArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/CreateFunctionResult"}}}}}, "/function/find": {"post": {"tags": ["Function API"], "summary": "find custom function", "description": "查找自定义函数", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/FindFunctionArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/FindFunctionResult"}}}}}, "/function/query": {"post": {"tags": ["Function API"], "summary": "query custom function list", "description": "查询自定义函数列表", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/QueryFunctionArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/QueryFunctionResult"}}}}}, "/function/enable": {"post": {"tags": ["Function API"], "summary": "enable custom function", "description": "启用/禁用 自定义函数", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ActiveRuleArg"}}]}}, "/function/func_exist": {"post": {"tags": ["Function API"], "summary": "Is custom function exist", "description": "根据过滤条件判断自定义函数是否存在", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/FunctionExistArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/FunctionExistResult"}}}}}, "/function/compile_check": {"post": {"tags": ["Function API"], "summary": "compile custom function", "description": "编译自定义函数", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/CompileCheckArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/CompileCheckResult"}}}}}, "/function/delete": {"post": {"tags": ["Function API"], "summary": "delete custom function", "description": "删除自定义函数", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/DeleteFunctionArg"}}]}}, "/function/debug_function": {"post": {"tags": ["Function API"], "summary": "debug custom function", "description": "测试运行自定义函数", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/DebugRunFunctionArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/DebugRunFunctionResult"}}}}}, "/function/check_function_limit": {"post": {"tags": ["Function API"], "summary": "check custom function limit", "description": "检查自定义函数配额", "consumes": ["application/json"], "produces": ["application/json"]}}}, "definitions": {"FunctionInfo": {"type": "object", "properties": {"api_name": {"type": "string"}, "binding_object_api_name": {"type": "string"}, "function_name": {"type": "string"}, "is_active": {"type": "boolean"}, "create_time": {"type": "string", "format": "date"}, "create_by": {"type": "string"}, "return_type": {"type": "string"}, "status": {"type": "string"}, "remark": {"type": "string"}, "parameters": {"type": "array", "items": {"$ref": "#/definitions/FunctionInfoParameter"}}, "body": {"type": "string"}, "version": {"type": "integer"}, "application": {"type": "string"}, "bindingObjectLabel": {"type": "string"}}}, "FunctionInfoParameter": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "value": {"type": "object"}, "remark": {"type": "string"}}}, "CreateFunctionArg": {"type": "object", "properties": {"FunctionInfo": {"$ref": "#/definitions/FunctionInfo"}}}, "CreateFunctionResult": {"type": "object", "properties": {"FunctionInfo": {"$ref": "#/definitions/FunctionInfo"}}}, "UpdateFunctionArg": {"type": "object", "properties": {"FunctionInfo": {"$ref": "#/definitions/FunctionInfo"}}}, "UpdateFunctionResult": {"type": "object", "properties": {"FunctionInfo": {"$ref": "#/definitions/FunctionInfo"}}}, "FindFunctionArg": {"type": "object", "properties": {"api_name": {"type": "string"}, "binding_object_api_name": {"type": "string"}}}, "FindFunctionResult": {"type": "object", "properties": {"FunctionInfo": {"$ref": "#/definitions/FunctionInfo"}}}, "QueryFunctionArg": {"type": "object", "properties": {"api_name": {"type": "string"}, "binding_object_api_name": {"type": "string"}, "name_space": {"type": "array", "items": {"type": "string"}}, "is_active": {"type": "boolean"}, "pageNumber": {"type": "string"}, "pageSize": {"type": "string"}, "is_include_used": {"type": "boolean"}}}, "QueryFunctionResult": {"type": "object", "properties": {"function": {"type": "array", "items": {"$ref": "#/definitions/FunctionInfo"}}, "totalNumber": {"type": "integer"}}}, "ActiveRuleArg": {"type": "object", "properties": {"api_name": {"type": "string"}, "binding_object_api_name": {"type": "string"}, "is_active": {"type": "boolean"}}}, "FunctionExistArg": {"type": "object", "properties": {"binding_object_api_name": {"type": "string"}, "name_space": {"type": "array", "items": {"type": "string"}}, "return_Type": {"type": "string"}}}, "FunctionExistResult": {"type": "object", "properties": {"isExist": {"type": "boolean"}}}, "CompileCheckArg": {"type": "object", "properties": {"FunctionInfo": {"type": "object"}}}, "CompileCheckResult": {"type": "object", "properties": {"success": {"type": "boolean"}, "compileError": {"type": "string"}}}, "DeleteFunctionArg": {"type": "object", "properties": {"api_name": {"type": "string"}, "binding_object_api_name": {"type": "string"}}}, "DebugRunFunctionArg": {"type": "object", "properties": {"api_name": {"type": "string"}, "binding_object_api_name": {"type": "string"}, "input_data": {"type": "array", "items": {"$ref": "#/definitions/FunctionInfoParameter"}}, "FunctionInfo": {"$ref": "#/definitions/FunctionInfo"}}}, "DebugRunFunctionResult": {"type": "object", "properties": {"success": {"type": "boolean"}, "runResult": {"type": "object"}, "logInfo": {"type": "string"}, "error": {"type": "string"}}}}}