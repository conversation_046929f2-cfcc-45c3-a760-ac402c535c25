package com.facishare.paas.appframework.core.predef.service.dto.publicobject;

import com.facishare.paas.appframework.metadata.publicobject.dto.PublicObjectJobParamDTO;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicObjectJobResultDTO;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobResult;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobStatus;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobType;
import lombok.Builder;
import lombok.Data;

import java.util.Objects;
import java.util.Optional;

/**
 * Created by zhaooju on 2023/12/13
 */
public interface QueryPublicObjectJob {

    @Data
    class Arg {
        private String objectApiName;
        private String jobId;
    }

    @Data
    @Builder
    class Result {
        private PublicObjectJobParamDTO jobParam;
        private PublicObjectJobResultDTO jobResult;
        private String jobType;
        private String jobStatus;

        public static Result from(PublicObjectJobResult publicObjectJobResult) {
            if (Objects.isNull(publicObjectJobResult)) {
                return null;
            }
            String jobType = Optional.ofNullable(publicObjectJobResult.getJobType())
                    .map(PublicObjectJobType::getType)
                    .orElse(null);
            String jobStatus = Optional.ofNullable(publicObjectJobResult.getJobStatus())
                    .map(PublicObjectJobStatus::getStatus)
                    .orElse(null);
            return Result.builder()
                    .jobParam(publicObjectJobResult.getJobParam())
                    .jobResult(publicObjectJobResult.getJobResult())
                    .jobType(jobType)
                    .jobStatus(jobStatus)
                    .build();
        }
    }

}
