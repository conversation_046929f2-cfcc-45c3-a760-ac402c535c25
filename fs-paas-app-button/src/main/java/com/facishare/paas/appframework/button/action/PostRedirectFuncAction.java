package com.facishare.paas.appframework.button.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.exception.FunctionTimeoutException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.google.common.base.Strings;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Component
public class PostRedirectFuncAction extends UIActionFuncAction {
    @Override
    public ActionExecutorType getType() {
        return ActionExecutorType.POST_REDIRECT_FUNCTION;
    }

    @Override
    protected ButtonExecutor.Result handleResult(User user, ButtonExecutor.Arg arg, RunResult runResult) {
        Object ret = runResult.getFunctionResult();
        String returnType = runResult.getReturnType();

        ret = processResultValue(ret);

        if (ObjectUtils.isEmpty(ret)) {
            return null;
        }
        ButtonExecutor.Result result = ButtonExecutor.Result.builder()
                .hasReturnValue(!Objects.isNull(ret) && !Strings.isNullOrEmpty(String.valueOf(ret)))
                .returnType(returnType)
                .returnValue(ret)
                .build();

        return result;
    }

    private Object processResultValue(Object returnValue) {
        if (!(returnValue instanceof Map)) {
            return null;
        }

        // 返回所有返回值,不需要判断客户端类型
        if (needAllRedirectResult()) {
            return returnValue;
        }

        Map uiAction = (Map) returnValue;
        Object action = uiAction.get(UI_ACTION_TYPE);
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_740)) {
            throw new FunctionException(I18N.text(I18NKey.FUNCTION_UIACTION_CLIENT_UPGRADE));
        }

        /**
         * 移动端下发WebAction,Web端下发UIAction，返回结果为空，期望效果是不进行跳转。
         * 不阻塞保存，编辑的操作
         */
        if (isMobileRequest() && Objects.equals(WEB_ACTION, action)) {
            return null;
        }
        if (isWebRequest() && Objects.equals(APP_ACTION, action)) {
            return null;
        }

        return returnValue;
    }

    private boolean needAllRedirectResult() {
        return Optional.ofNullable(RequestContextManager.getContext())
                .map(it -> it.<Boolean>getAttribute("needAllRedirectResult"))
                .orElse(false);
    }

    @Override
    protected ButtonExecutor.Result handleException(RuntimeException e, IUdefFunction udefFunction) {
        ButtonExecutor.Result result = ButtonExecutor.Result.builder()
                .hasReturnValue(true)
                .returnType(ALERT_ACTION)
                .build();

        Map<String, Object> returnValue = null;
        if (e instanceof RestProxyBusinessException || e instanceof FunctionException) {
            returnValue = transformAlertAction(e.getMessage());
        } else {
            Throwable rootCause = ExceptionUtils.getRootCause(e);
            if (rootCause instanceof SocketTimeoutException || rootCause instanceof FunctionTimeoutException) {
                returnValue = transformAlertAction(e.getMessage());
            } else {
                returnValue = transformAlertAction(I18N.text(I18NKey.FUNC_FAIL));
            }
        }


        result.setReturnValue(returnValue);
        return result;
    }

    /**
     * 函数运行出现业务异常时，如果抛出业务异常，页面会停留在新建/编辑页面，此时数据已经保存入库了。
     * 所以后端会转成AlertAction，期望实现的效果是，出错了，弹出一个提示，所以转换成AlertAction
     *
     * @return
     */
    private Map<String, Object> transformAlertAction(String errorMessage) {
        Map<String, Object> result = new HashMap<>();
        result.put("action", ALERT_ACTION);
        result.put("data", new HashMap<>());
        result.put("text", errorMessage);
        result.put("type", "default");
        return result;
    }
}
