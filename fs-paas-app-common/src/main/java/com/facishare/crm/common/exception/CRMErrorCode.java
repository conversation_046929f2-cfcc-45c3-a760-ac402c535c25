package com.facishare.crm.common.exception;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;

/**
 * Created by <PERSON><PERSON> on 12/6/16.
 * 对应wiki：http://wiki.firstshare.cn/display/CRMSpace/CRM-JAVA+ErrorCode+document
 */
public enum CRMErrorCode {
    SUCCESS(0, I18NKey.ERRORCODE_SUCCESS),
    SYSTEM_ERROR(1, "system error"),
    PARAMETER_IS_WRONG(5, "parameter is wrong"),//参数为空
    PAAS_PRIVILEGE_FAILED(11, I18NKey.ERRORCODE_PAAS_PERMISSION_ERROR),
    AUTHENTICATION_ERROR(10, "authentication error"),//身份错误
    PAAS_ERROR(12, "paas error"),
    LOW_VERSION_ERROR(13, I18NKey.ERRORCODE_MINI_VERSION),
    WRONG_VERSION_ERROR(14, I18NKey.ERRORCODE_VERSION_ERROR),
    USER_DEFINED_ROLE_QUOTA_ERROR(15, I18NKey.ERRORCODE_MAX_ROLE),
    WRONG_VERSION_ERROR_FOR_INTELLI_FORM(14, I18NKey.ERRORCODE_BASIC_VERSION),

    //crm common
    FS_CRM_COMMON_RUNTIME_UNKOWN(304000001, I18NKey.ERRORCODE_UNKNOW_ERROR),
    FS_CRM_COMMON_RUNTIME_CONFIG_CENTER_FILE_NULL(304000002, I18NKey.ERRORCODE_CONFIG_FILE_FAIL),
    FS_CRM_COMMON_CHECKED_PARAMETER_INVALID(204000001, I18NKey.ERRORCODE_PARAM_WRONG),
    FS_CRM_COMMON_CHECKED_AUTHENTICATION_ERROR(204000002, I18NKey.CONSTANT_NO_PERMISSION),
    FS_CRM_COMMON_CHECKED_USERID_NULL(204000003, I18NKey.ERRORCODE_USER_ID_EMPTY),
    FS_CRM_COMMON_CHECKED_EI_NULL(204000004, I18NKey.ERRORCODE_EI_EMPTY),
    FS_CRM_COMMON_CHECKED_EA_NULL(204000005, I18NKey.ERRORCODE_EA_EMPTY),

    //openAPI
    FS_CRM_OPENAPI_UNSUPPORTED_OBJECT_TYPE(204100001, I18NKey.ERRORCODE_FAIL_CONVERT_APINAME),
    FS_CRM_OPENAPI_EXTERNAL_SERVICE_REST_EXCEPTION(204110002, I18NKey.ERRORCODE_REST_CALL_FAIL),
    FS_CRM_OPENAPI_EXTERNAL_SERVICE_REST_NULLRESPONSE_EXCEPTION(204110003, I18NKey.ERRORCODE_REST_CALL_FAIL),
    FS_CRM_OPENAPI_EXTERNAL_SERVICE_REST_CHANGESHELVES_EXCEPTION(204110004, I18NKey.ERRORCODE_ON_SALE_FAIL),
    FS_CRM_OPENAPI_EXTERNAL_SERVICE_REST_CHANGEOWNER_EXCEPTION(204110005, I18NKey.ERRORCODE_CHANGE_OWNER_FAIL),
    FS_CRM_OPENAPI_FIELD_NOT_ALLOWED_UPDATED(204110006, I18NKey.ERRORCODE_FIELD_UPDATE_FAIL),
    FS_CRM_OPENAPI_FIELD_NOT_ALLOWED_CREATED(204110007, I18NKey.ERRORCODE_DATA_CREATE_FAIL),
    FS_CRM_OPENAPI_EXTERNAL_SERVICE_REST_PARSE_RESULT_NULL(204110008, I18NKey.ERRORCODE_REST_RESULT_EMPTY),
    FS_CRM_OPENAPI_EXTERNAL_SERVICE_REST_PARSE_RESULT_ERROR(20411010, I18NKey.ERRORCODE_REST_RESULT_ERROR),
    FS_CRM_OPENAPI_EXTERNAL_SERVICE_REST_HTTPCODE_NOCONTENT(204110011, I18NKey.ERRORCODE_HTTP_204),
    FS_CRM_OPENAPI_EXTERNAL_SERVICE_REST_HTTPCODE_BAD_REQUEST(204110012, I18NKey.ERRORCODE_HTTP_400),
    FS_CRM_OPENAPI_EXTERNAL_SERVICE_REST_HTTPCODE_NOT_FOUND(204110013, I18NKey.ERRORCODE_HTTP_404),
    FS_CRM_OPENAPI_EXTERNAL_SERVICE_REST_HTTPCODE_NOTOK(204110014, I18NKey.ERRORCODE_REST_CALL_FAIL),
    FS_CRM_OPENAPI_EXTERNAL_SERVICE_REST_NOCONTENT(204110009, I18NKey.ERRORCODE_REST_RESULT_EMPTY),

    // defObj
    FS_CRM_DEFOBJ_CHECKED_NORELATION_BETWEEN_OBJECTS(204200001, I18NKey.ERRORCODE_NO_REFERENCE),
    FS_CRM_DEFOBJ_MDS_LAYOUT_INIT_ERROR(304200001, I18NKey.ERRORCODE_LAYOUT_NOT_INIT),
    FS_CRM_DEFOBJ_CHECKED_CANNOT_DISASSOCIATE(204200007, I18NKey.ERRORCODE_CAN_NOT_DISRELATE),
    FS_CRM_DEFOBJ_CHECKED_NORELATION_BETWEEN_OBJECT_DATAS(204200002, I18NKey.ERRORCODE_DATA_NO_REFERENCE),
    FS_CRM_DEFOBJ_CHECKED_NOT_EXIST_DATA(204200008, I18NKey.ERRORCODE_DATA_DELETED),
    FS_CRM_DEFOBJ_CHECKED_INDEX_NAME_NULL(204200003, I18NKey.ERRORCODE_INDEX_NAME_EMPTY),
    FS_CRM_DEFOBJ_CHECKED_CUSTOM_ACTION_PARAMETER_INVALID(204200004, I18NKey.ERRORCODE_PARAM_WRONG),
    FS_CRM_DEFOBJ_CHECKED_CUSTOM_ACTION_NOT_SUPPORT(204200005, I18NKey.ERRORCODE_UNSUPPORT_ACTION),
    FS_CRM_DEFOBJ_CHECKED_PAAS_SERVICE_ERROR(204200006, I18NKey.ERRORCODE_METADATA_ERROR),
    FS_CRM_DEFOBJ_CHECKED_LAYOUT_COMPONENT_ILLEGAL_ERROR(204200009, I18NKey.ERRORCODE_LAYOUT_COMPOENNT_ERROR),
    FS_CRM_DEFOBJ_EXTERNAL_SERVICE_REST_HTTPCODE_NOTOK(204200010, I18NKey.ERRORCODE_REST_CALL_FAIL),
    FS_CRM_DEFOBJ_CHECKED_CAN_NOT_LOOKUP_MYSELF(204200011, I18NKey.ERRORCODE_CAN_NOT_REF_SELF),
    FS_CRM_DEFOBJ_CHECKED_DUPLICATE_DISPLAY_NAME(204200012, I18NKey.ERRORCODE_OBJECT_NAME_DUPLICATE),
    FS_CRM_DEFOBJ_CHECKED_DUPLICATE_LAYOUT_NAME(204200019, I18NKey.ERRORCODE_LAYOUT_NAME_DUPLICATE),
    FS_CRM_DEFOBJ_EXTERNAL_SERVICE_REST_AUTH_HTTPCODE_NOTOK(204200013, I18NKey.ERRORCODE_AUTH_CAL_FAIL),
    FS_CRM_DEFOBJ_EXTERNAL_BEYOND_OBJ_MAX_LIMIT(204200014, I18NKey.ERRORCODE_MAX_OBJECT_COUNT),
    FS_CRM_DEFOBJ_TENANT_NOT_SUPPORT_UDOBJ(204200022, I18NKey.ERRORCODE_UNSUPPORT_UDOBJ),
    FS_CRM_DEFOBJ_EXTERNAL_BEYOND_LAYOUT_MAX_LIMIT(204200015, I18NKey.ERRORCODE_MAX_LAYOUT_COUNT),
    FS_CRM_DEFOBJ_EXTERNAL_BEYOND_RECORD_TYPE_MAX_LIMIT(204200016, I18NKey.ERRORCODE_MAX_RECORD_TYPE_COUNT),
    FS_CRM_DEFOBJ_EXTERNAL_WORKFLOW_IN_PROCESS(204200017, I18NKey.ERRORCODE_APPRFLOW_PROCESSIG),
    FS_CRM_DEFOBJ_EXTERNAL_FIELD_COUNT_LIMIT(204200018, I18NKey.ERRORCODE_MAX_FIELD_COUNT),
    FS_CRM_DEFOBJ_CHECKED_LAYOUT_IS_NULL(204200020, I18NKey.ERRORCODE_LAYOUT_NOT_EXIST),
    FS_CRM_DEFOBJ_CHECKED_LAYOUT_IS_DEFAULT(204200021, I18NKey.ERRORCODE_DEFAULT_LAYOUT_UNSUPPORT),
    FS_CRM_DEFOBJ_CHECKED_NAME_MAX_LENGTH(204200022, I18NKey.ERRORCODE_MAX_NAME_LENGTH),
    FS_CRM_DEFOBJ_NEW_PROJECT_EXCEPTION(204200100, I18NKey.ERRORCODE_UDOBJ_NEW_PROJECT_ERROR),

    // print template
    FS_CRM_PRINTTEMPLATE_COUNT_EXCEED_MAX(304511002, I18NKey.ERRORCODE_MAX_PRINT_TEMPLATE),
    FS_CRM_PRINTTEMPLATE_DUPLICATE_NAME(304511003, I18NKey.ERRORCODE_MAX_PRINT_TEMPLATE),
    FS_CRM_PRIVILEGE_PAAS_SQL_ERROR(304310000, I18NKey.ERRORCODE_PAAS_DB_ERROR),

    //权限相关 privilege
    FS_CRM_PRIVILEGE_PAAS_REST_ERROR(304310002, I18NKey.ERRORCODE_REST_CALL_FAIL),
    FS_CRM_PRIVILEGE_SYSTEM_UNKNOWN_ERROR(304310003, I18NKey.ERRORCODE_AUTH_CAL_FAIL),
    FS_CRM_PRIVILEGE_PAAS_MANGE(304310004, "no mange privilege"),//没有管理权限
    FS_CRM_CALL_CRM_METHOD_FAILED(304410000, I18NKey.ERRORCODE_REST_CALL_FAIL),
    FS_CRM_CALL_METADATA_METHOD_FAILED(304410001, I18NKey.ERRORCODE_METHOD_FAIL),
    PRIVILEGE_SYSTEM_ERROR(304300001, "system error"), //系统异常
    PRIVILEGE_PARAMETER_ERROR(304300002, "parameter error"), //参数错误
    PRIVILEGE_PAAS_ERROR(304300011, "paas error"),
    ROLE_REMOVE_USER_ERROR(304300012, "the user you delete contain yourself."),//不允许删除自己
    DATA_PRIVILEGE_NO_DATA_PRIVILEGE(304300013, I18NKey.ERRORCODE_NO_DATA_PERMISSION),
    FUNCTION_PRIVILEGE_NO_FUNCTION_PRIVILEGE(304300014, I18NKey.ERRORCODE_ACTION_NO_PERMISSION),
    ROLE_CODE_INVALID(30430040, I18NKey.ERRORCODE_ROLE_CODE_INVALID),
    PRIVILEGE_TOO_MANY(30430041, I18NKey.PRIVILEGE_TOO_MANY),

    CRM_ADMIN_MODIFY_ERROR(304300013, "crm administrator role can't modify"),//crm管理员不许进行更改
    QUOTA_NOT_ENOUGH_ERROR(304300014, "quota not enough,can't add user to role"),//crm配额不足
    DEFAULT_ROLE_COPY_ERROR(304300015, "default role can't be copy target"),//默认角色不能作为copy的目标
    WRONG_ROLE_TYPE_ERROR(304300016, "role type is wrong"),//默认角色不能作为copy的目标
    DEFAULT_ROLE_DELETE_ERROR(304300017, "default role can't be delete"),
    CRM_METHOD_FAILED(304300018, "crm method failed!"),//crm方法出错
    GET_FIELD_LIST_FAILED(304300019, "get field list failed!"),
    REMOVE_USER_CRM_VISIBLE_FAILED(304300020, "remove user crm visible failed."),
    DEFAULT_ROLE_NOT_EXIST_EXCEPTION(304300022, I18NKey.ERRORCODE_MAIN_ROLE_NOT_CONFIG),
    MASTER_DETAIL_OBJ_CAN_NOT_BE_UPDATED(304300023, I18NKey.ERRORCODE_RELEVANT_TEAM_UNSUPPORT),

    //用户组
    GROUP_SYSTEM_ERROR(304400001, "system error"), //系统异常
    GROUP_PARAMETER_ERROR(304400002, "parameter error"), //参数错误
    GROUP_PAAS_ERROR(304400011, "paas error"),


    //license服务
    LICENSE_VERSION_ERROR(304500001, "paas get version error"),
    LICENSE_QUOTA_ERROR(304500002, "paas get quota error"),
    LICENSE_ERROR(304500003, "paas license error");


    private String message;
    private int code;

    CRMErrorCode(int code, String message) {
        this.message = message;
        this.code = code;
    }

    public String getMessage() {
        return I18N.text(this.message);
    }

    public int getCode() {
        return this.code;
    }

    public String getStrCode() {
        return Integer.toString(this.code);
    }

    public static CRMErrorCode valueOf(int code) {
        for (CRMErrorCode errorCode : CRMErrorCode.values()) {
            if (errorCode.code == code) {
                return errorCode;
            }
        }
        throw new IllegalArgumentException("Invalid code value: " + code);
    }

}
