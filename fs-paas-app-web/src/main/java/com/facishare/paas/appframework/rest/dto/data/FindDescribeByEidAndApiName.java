package com.facishare.paas.appframework.rest.dto.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.metadata.dto.DetailObjectListResult;
import com.facishare.paas.appframework.metadata.dto.RefObjectDescribeListResult;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public interface FindDescribeByEidAndApiName {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        String apiName;
        boolean includeRefDesc;
        boolean includeDetailDesc;
        boolean includeStatistics;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends CommonResult.DescribeResult {
        @JSONField(name = "refObjectDescribeList")
        @JsonProperty(value = "refObjectDescribeList")
        @SerializedName("refObjectDescribeList")
        List<RefObjectDescribeListResult> refObjectDescribeList;

        @JSONField(name = "detailObjectDescribeList")
        @JsonProperty(value = "detailObjectDescribeList")
        @SerializedName("detailObjectDescribeList")
        List<DetailObjectListResult> detailObjectDescribeList;

    }
}
