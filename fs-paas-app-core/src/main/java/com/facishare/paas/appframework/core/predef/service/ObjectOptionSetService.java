package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.option.*;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.options.OptionReference;
import com.facishare.paas.appframework.metadata.options.OptionSetLogicService;
import com.facishare.paas.appframework.metadata.repository.model.MtOptionSet;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * create by zhaoju on 2021/12/14
 */
@Slf4j
@Service
@ServiceModule("options")
public class ObjectOptionSetService {
    @Autowired
    private OptionSetLogicService optionSetLogicService;

    @ServiceMethod("findAll")
    public FindAllOptionSet.Result findAllOptionSet(FindAllOptionSet.Arg arg, ServiceContext context) {
        List<MtOptionSet> options = optionSetLogicService.findAll(context.getUser());
        return FindAllOptionSet.Result.of(options);
    }

    @ServiceMethod("find")
    public FindOptionSet.Result findOptionSet(FindOptionSet.Arg arg, ServiceContext context) {
        Optional<MtOptionSet> mtOptionSet = optionSetLogicService.find(context.getUser(), arg.getOptionApiName());
        return FindOptionSet.Result.of(mtOptionSet.orElse(null));
    }

    @ServiceMethod("create")
    public SaveOptionSet.Result createOptionSet(SaveOptionSet.Arg arg, ServiceContext context) {
        MtOptionSet mtOptionSet = optionSetLogicService.create(context.getUser(), arg.getOption());
        return SaveOptionSet.Result.of(mtOptionSet);
    }

    @ServiceMethod("update")
    public SaveOptionSet.Result updateOptionSet(SaveOptionSet.Arg arg, ServiceContext context) {
        if (RequestUtil.isFromFunction() && !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OPTION_SET_CHANGE_GRAY_DISPATCHER, context.getTenantId())) {
            log.warn("unsupported updateOptionSet! ei:{}", context.getTenantId());
            throw new ValidateException(I18NExt.text(I18NKey.NOT_SUPPORTED_FUNCTION_UPDATE_OPTION_SET));
        }
        MtOptionSet mtOptionSet = optionSetLogicService.update(context.getUser(), arg.getOption(), arg.onlyUpdateOptions());
        return SaveOptionSet.Result.of(mtOptionSet);
    }

    @ServiceMethod("enable")
    public FindOptionSet.Result enableOptionSet(FindOptionSet.Arg arg, ServiceContext context) {
        MtOptionSet optionSet = optionSetLogicService.enable(context.getUser(), arg.getOptionApiName());
        return FindOptionSet.Result.of(optionSet);
    }

    @ServiceMethod("disable")
    public FindOptionSet.Result disableOptionSet(FindOptionSet.Arg arg, ServiceContext context) {
        MtOptionSet optionSet = optionSetLogicService.disable(context.getUser(), arg.getOptionApiName());
        return FindOptionSet.Result.of(optionSet);
    }

    @ServiceMethod("deleted")
    public FindOptionSet.Result deletedOptionSet(FindOptionSet.Arg arg, ServiceContext context) {
        optionSetLogicService.deleted(context.getUser(), arg.getOptionApiName());
        return FindOptionSet.Result.of(null);
    }

    @ServiceMethod("checkCount")
    public FindOptionSet.Result checkCount(ServiceContext context) {
        optionSetLogicService.checkCount(context.getUser());
        return FindOptionSet.Result.of(null);
    }

    @ServiceMethod("triggerCalculate")
    public TriggerCalculate.Result triggerCalculate(TriggerCalculate.Arg arg, ServiceContext context) {
        optionSetLogicService.asyncTriggerCalculate(context.getUser(), arg.getOptionApiNames(), BooleanUtils.isTrue(arg.getOnlyTouch()));
        return new TriggerCalculate.Result();
    }

    @ServiceMethod("findReference")
    public FindReference.Result findReference(FindReference.Arg arg, ServiceContext context) {
        List<OptionReference.SimpleReference> reference = optionSetLogicService.findReference(context.getUser(), arg.getOptionApiName())
                .getOptionReferences();
        return FindReference.Result.of(reference);
    }
}
