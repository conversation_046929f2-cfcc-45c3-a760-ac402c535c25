package com.facishare.paas.appframework.core.util;

/**
 * Created by zhouwr on 2019/11/14
 */
public interface GenericClass {

    class P<A, R> {

    }

    class P1<R extends P1.Result> extends P<P1.Arg, R> {

        class Arg {

        }

        class Result {

        }
    }

    class P2 extends P1<P2.Result> {

        class Result extends P1.Result {

        }
    }

    class P3 extends P<P3.Arg, P3.Result> {

        class Arg {

        }

        class Result {

        }
    }

}
