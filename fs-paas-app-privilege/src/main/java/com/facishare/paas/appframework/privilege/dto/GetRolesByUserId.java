package com.facishare.paas.appframework.privilege.dto;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Singular;

import java.util.List;

/**
 * Created by liyiguang on 2017/10/23.
 */
public interface GetRolesByUserId {

    @Data
    class Arg {
        private AuthContext authContext;
        private List<String> users;

        @Builder
        public Arg(AuthContext context, @Singular List<String> users) {
            this.authContext = context;
            this.users = users;
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {
        private List<UserRole> result;
    }

    @Data
    class UserRole {
        private String tenantId;
        private String appId;
        private String roleCode;
        private String roleName;
        private Integer orgType;
        private String orgId;
        private Boolean defaultRole;

        public boolean getDefaultRole() {
            return defaultRole == null ? false : defaultRole;
        }


    }
}
