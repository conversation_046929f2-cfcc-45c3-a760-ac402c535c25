package com.facishare.paas.appframework.log.dto;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

public class SearchFunctionEsLogDetail {

    @Data
    public static class Arg {
        private long tenantId;
        private String id;
        private String traceId;
        private String apiName;
        private long logLevel;
        private long startTime;
        private long endTime;
        private String keyWord;
        private int rows;
        private boolean prePage;
        private long createTime;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class Result extends BaseResult {
        private long totalCount;
        private boolean hasMore;
        private List<FunctionEsLogDetail> logs = Lists.newArrayList();
    }
}
