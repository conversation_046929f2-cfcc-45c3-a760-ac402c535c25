package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created by zhouwr on 2019/4/1
 */
@Slf4j
@Component
public class BatchFieldDataConverterManager implements ApplicationContextAware {

    private Map<String, BatchFieldDataConverter> batchDataConverterMap = Maps.newHashMap();

    @Autowired
    private BatchFieldDataConverterAdapter batchFieldDataConverterAdapter;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        try {
            Map<String, BatchFieldDataConverter> batchSpringMap = applicationContext.getBeansOfType(BatchFieldDataConverter.class);
            if (CollectionUtils.notEmpty(batchSpringMap)) {
                batchSpringMap.forEach((x, y) -> y.getSupportedFieldTypes().forEach(t -> batchDataConverterMap.put(t, y)));
            }
        } catch (BeansException e) {
            log.error("init BatchFieldDataConverter error", e);
        }
    }

    public BatchFieldDataConverter getBatchFieldDataConverter(String fieldType) {
        return batchDataConverterMap.getOrDefault(fieldType, batchFieldDataConverterAdapter);
    }

}
