package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

/**
 * 屏蔽属性值的更新导入
 */
@Component
public class AttributeValueObjectImportProvider extends DefaultObjectImportProvider {

    @Override
    public String getObjectCode() {
        return Utils.ATTRIBUTE_VALUE_OBJ_API_NAME;
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return ImportType.UNSUPPORT_UPDATE_IMPORT;
    }
}
