package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.OrganizationInfo;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds.MainDeptInfo;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.relation.CalculateFields;
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.UserDefinedButtonService;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Quote;
import com.facishare.paas.metadata.api.service.IObjectMappingRuleService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ActionEnum;
import com.facishare.paas.metadata.impl.ObjectMappingParams;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.ObjectMappingExt.MAPPING_ACTION_TYPE;
import static com.facishare.paas.metadata.api.DBRecord.OUT_OWNER;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("objectMappingService")
public class ObjectMappingServiceImpl implements ObjectMappingService {
    @Autowired
    private ObjectMappingService objectMappingService;

    @Autowired
    private IObjectMappingRuleService mappingRuleService;
    @Autowired
    private PostActionService postActionService;
    @Autowired
    private CustomButtonServiceImpl customButtonService;
    @Autowired
    private MetaDataComputeServiceImpl computeService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private QuoteValueService quoteValueService;
    @Autowired
    private MetaDataMiscServiceImpl metaDataMiscService;
    @Autowired
    private RecordTypeLogicService recordTypeLogicService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private UserDefinedButtonService userDefinedButtonService;
    @Autowired
    private FieldRelationCalculateService fieldRelationCalculateService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private LicenseService licenseService;


    @Override
    public List<IObjectMappingRuleInfo> createRule(User user, List<IObjectMappingRuleInfo> ruleList) {
        List<IObjectMappingRuleInfo> rules;
        try {
            rules = mappingRuleService.createObjectMappingRule(ruleList);
        } catch (MetadataServiceException e) {
            log.warn("Error in create MappingRule", e);
            throw new ValidateException(e.getMessage());
        }
        return rules;
    }

    @Override
    public List<IObjectMappingRuleInfo> updateRule(User user, List<IObjectMappingRuleInfo> ruleList) {
        List<IObjectMappingRuleInfo> rules;
        try {
            rules = mappingRuleService.setObjectMappingRule(ruleList);
        } catch (MetadataServiceException e) {
            log.warn("Error in update MappingRule", e);
            throw new ValidateException(e.getMessage());
        }
        return rules;
    }

    @Transactional
    @Override
    public void enableRule(User user, String ruleApiName, String describeApiName) {
        //校验目标对象是否存在
        List<IObjectMappingRuleInfo> rules = findByApiName(user, ruleApiName);
        String targetApiName = ObjectMappingExt.of(rules).getObjectMappingRuleInfo().getTargetApiName();
        IObjectDescribe target = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), targetApiName);
        if (Objects.isNull(target) || Objects.equals(target.isActive(), Boolean.FALSE)) {
            throw new ValidateException(I18N.text(I18NKey.TARGET_OBJECT_UNEXIST_OR_DISABLED));
        }

        String sourceApiName = ObjectMappingExt.of(rules).getObjectMappingRuleInfo().getSourceApiName();
        IObjectDescribe source = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), sourceApiName);
        if (Objects.isNull(source) || Objects.equals(source.isActive(), Boolean.FALSE)) {
            throw new ValidateException(I18N.text(I18NKey.SOURCE_OBJECT_UNEXIST_OR_DISABLED));
        }

        String errorMessage;
        try {
            errorMessage = mappingRuleService.validateObjectMappingRuleByRuleApiName(user.getTenantId(), ruleApiName);
        } catch (MetadataServiceException e) {
            log.warn("Error in enable mapping rule", e);
            throw new ValidateException(e.getMessage());
        }

        IUdefButton button = findButtonByRuleApiName(ruleApiName, describeApiName, user);
        if (!Objects.isNull(button)) {
            customButtonService.updateStatus(user, button.getApiName(), describeApiName, true);
        }

        if (!Strings.isNullOrEmpty(errorMessage)) {
            throw new ValidateException(errorMessage);
        }
    }

    @Transactional
    @Override
    public void disableRule(User user, String ruleApiName, String describeApiName) {
        String errorMessage;
        try {
            errorMessage = mappingRuleService.invalidObjectMappingRuleByRuleApiName(user.getTenantId(), ruleApiName);
        } catch (MetadataServiceException e) {
            log.warn("Error in disable mapping rule", e);
            throw new ValidateException(e.getMessage());
        }

        IUdefButton button = findButtonByRuleApiName(ruleApiName, describeApiName, user);
        if (!Objects.isNull(button)) {
            customButtonService.updateStatus(user, button.getApiName(), describeApiName, false);
        }

        if (!Strings.isNullOrEmpty(errorMessage)) {
            throw new ValidateException(errorMessage);
        }
    }

    /**
     * 主从映射规则从对象映射规则不禁用，防止从对象启用映射规则丢失。
     */
    @Override
    public void disableRuleByTargetDescribe(User user, String describeApiName) {
        List<IObjectMappingRuleInfo> ruleList = findRuleList(user, -1, null);
        List<IObjectMappingRuleInfo> actualList = ruleList.stream().filter(a ->
                Objects.equals(describeApiName, a.getTargetApiName()) &&
                        Objects.equals(a.getStatus(), 0) && ObjectMappingService.isObjectMappingRule(a)).collect(Collectors.toList());

        actualList.forEach(a -> objectMappingService.disableRule(user, a.getRuleApiName(), a.getSourceApiName()));
    }

    @Override
    public void disableRuleBySourceDescribe(User user, String describeApiName) {
        List<IObjectMappingRuleInfo> ruleList = findRuleList(user, -1, null);
        List<IObjectMappingRuleInfo> actualList = ruleList.stream().filter(a ->
                Objects.equals(describeApiName, a.getSourceApiName()) &&
                        Objects.equals(a.getStatus(), 0) && ObjectMappingService.isObjectMappingRule(a)).collect(Collectors.toList());

        actualList.forEach(a -> objectMappingService.disableRule(user, a.getRuleApiName(), a.getSourceApiName()));
    }

    @Override
    public void deleteRuleByTargetDescribe(User user, String describeApiName) {
        List<IObjectMappingRuleInfo> ruleInfoList = findRuleListWithDetailRule(user, -1, null);
        List<IObjectMappingRuleInfo> actualList = ruleInfoList.stream()
                .filter(x -> Objects.equals(describeApiName, x.getTargetApiName())
                        && Objects.equals(x.getStatus(), 1) && ObjectMappingService.isObjectMappingRule(x)).collect(Collectors.toList());
        actualList.forEach(x -> objectMappingService.deleteRule(user, x.getRuleApiName(), x.getSourceApiName()));
    }

    @Override
    public void deleteRuleBySourceDescribe(User user, String describeApiName) {
        List<IObjectMappingRuleInfo> ruleInfoList = findRuleListWithDetailRule(user, -1, null);
        List<IObjectMappingRuleInfo> actualList = ruleInfoList.stream()
                .filter(x -> Objects.equals(describeApiName, x.getSourceApiName())
                        && Objects.equals(x.getStatus(), 1) && ObjectMappingService.isObjectMappingRule(x)).collect(Collectors.toList());
        actualList.forEach(x -> objectMappingService.deleteRule(user, x.getRuleApiName(), x.getSourceApiName()));
    }


    @Override
    @Transactional
    public void deleteRule(User user, String ruleApiName, String describeApiName) {
        String errorMessage;
        try {
            errorMessage = mappingRuleService.deleteObjectMappingRuleByRuleApiName(user.getTenantId(), ruleApiName);
        } catch (MetadataServiceException e) {
            log.warn("Error in delete mapping rule", e);
            throw new ValidateException(e.getMessage());
        }

        IUdefButton button = findButtonByRuleApiName(ruleApiName, describeApiName, user);
        if (!Objects.isNull(button)) {
            customButtonService.deleteCustomButton(user, button.getApiName(), describeApiName);
            userDefinedButtonService.deleteUserDefinedButton(user, describeApiName, button.getApiName());
        }

        if (!Strings.isNullOrEmpty(errorMessage)) {
            throw new ValidateException(errorMessage);
        }
    }

    @Override
    public List<IObjectMappingRuleInfo> findByApiName(User user, String ruleApiName) {
        List<IObjectMappingRuleInfo> rules;
        try {
            rules = mappingRuleService.getObjectMappingRuleByRuleApiName(user.getTenantId(), ruleApiName);
        } catch (MetadataServiceException e) {
            log.warn("Error in findById MappingRule", e);
            throw new ValidateException(e.getMessage());
        }
        return rules;
    }

    @Override
    public List<IObjectMappingRuleInfo> findRuleList(User user, int status, String ruleName) {
        return findRuleList(user, status, ruleName, null);
    }

    @Override
    public List<IObjectMappingRuleInfo> findRuleList(User user, int status, String ruleName, String bizType) {
        List<IObjectMappingRuleInfo> rules;
        try {
            IActionContext actionContext = ActionContextExt.of(user).getContext();
            rules = mappingRuleService.getObjectMappingRule(user.getTenantId(), ruleName, status, bizType, actionContext);
            //根据可用预置对象过滤规则
            filterByAvailableObject(user.getTenantId(), rules);
            dealMappingRuleMultiLanguage(rules);
        } catch (MetadataServiceException e) {
            log.warn("Error in findRuleList MappingRule", e);
            throw new ValidateException(e.getMessage());
        }
        return rules;
    }

    private void dealMappingRuleMultiLanguage(List<IObjectMappingRuleInfo> rules) {
        CollectionUtils.nullToEmpty(rules).forEach(x -> {
            if (IObjectDescribe.DEFINE_TYPE_SYSTEM.equals(x.getDefineType())) {
                String key = getObjectMappingRuleMultiLanguageKey(x);
                String transValue = I18NExt.getOnlyText(key);
                if (!Strings.isNullOrEmpty(transValue)) {
                    x.setRuleName(transValue);
                }
            }
        });
    }

    private String getObjectMappingRuleMultiLanguageKey(IObjectMappingRuleInfo ruleInfo) {
        return ruleInfo.getSourceApiName() + "." + ruleInfo.getTargetApiName() + "." + ruleInfo.getRuleApiName() + ".rule";
    }

    @Override
    public List<IObjectMappingRuleInfo> findRuleListWithDetailRule(User user, int status, String ruleName) {
        List<IObjectMappingRuleInfo> rules;
        try {
            IActionContext context = ActionContextExt.of(user).getContext();
            rules = mappingRuleService.getObjectMappingRuleWithDetailObjMapping(user.getTenantId(), ruleName, status, context);
            //根据可用预置对象过滤规则
            filterByAvailableObject(user.getTenantId(), rules);
            dealMappingRuleMultiLanguage(rules);
        } catch (MetadataServiceException e) {
            log.warn("Error in findRuleList MappingRule", e);
            throw new ValidateException(e.getMessage());
        }
        return rules;
    }

    @Override
    public List<IObjectMappingRuleInfo> findRuleListBySourceApiNameAndTargetApiName(User user, int status, String sourceApiName, String targetApiName) {
        List<IObjectMappingRuleInfo> rules;
        try {
            IActionContext context = ActionContextExt.of(user).getContext();
            IObjectMappingParams params = new ObjectMappingParams();
            params.setSourceApiName(sourceApiName);
            params.setTargetApiName(targetApiName);
            rules = mappingRuleService.getObjectMappingRuleByRuleNameSourceTarget(user.getTenantId(), params, status, context);
            //根据可用预置对象过滤规则
            filterByAvailableObject(user.getTenantId(), rules);
        } catch (MetadataServiceException e) {
            log.warn("Error in findRuleListBySourceApiNameAndTargetApiName MappingRule", e);
            throw new ValidateException(e.getMessage());
        }
        return rules;
    }

    private void filterByAvailableObject(String tenantId, List<IObjectMappingRuleInfo> rules) {
        Set<String> availableObject = licenseService.queryAvailableObject(tenantId);
        availableObject.add(ObjectAPINameMapping.BizQuery.getApiName());
        availableObject.addAll(AppFrameworkConfig.getMappingRuleWhiteObjects());
        CollectionUtils.nullToEmpty(rules).removeIf(a ->
                (!ObjectDescribeExt.isCustomObject(a.getSourceApiName()) && !availableObject.contains(a.getSourceApiName())
                        ||
                        (!ObjectDescribeExt.isCustomObject(a.getTargetApiName()) && !availableObject.contains(a.getTargetApiName()))));
    }


    @Override
    public MappingDataResult mappingData(User user, MappingDataArg arg) {
        IObjectMappingParams param = arg.toMappingParams();
        IObjectMappingParams result;
        try {
            List<IObjectMappingRuleInfo> rules = findByApiName(user, arg.getRuleApiName());
            removeInvalidDetailObjectData(user, rules, param);
            removeFieldByFuncPrivilege(user, param);
            result = mappingRuleService.getObjectFieldMapping(user.getTenantId(), param, rules);
            processResult(user, result, rules, arg.getRuleApiName());
        } catch (MetadataServiceException e) {
            log.warn("Error in mappingData", e);
            throw new ValidateException(e.getMessage());
        }

        return MappingDataResult.parse(result);
    }

    /**
     * 移除无效的从对象数据
     * @param user 用户信息
     * @param rules 映射规则
     * @param param 映射参数
     */
    private void removeInvalidDetailObjectData(User user, List<IObjectMappingRuleInfo> rules, IObjectMappingParams param) {
        if (CollectionUtils.empty(rules)) {
            return;
        }
        
        // 获取所有从对象的目标对象
        Set<String> targetObjects = rules.stream()
            .filter(rule -> Objects.nonNull(rule.getMdType()) && rule.getMdType() == 1) // 过滤出从对象规则，处理mdType为空的情况
            .map(IObjectMappingRuleInfo::getTargetApiName) // 确保目标对象名不为空
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        
        if (!CollectionUtils.empty(targetObjects)) {
            // 查询目标对象的描述信息
            Map<String, IObjectDescribe> targetDescribes = describeLogicService.findObjectsWithoutCopy(user.getTenantId(), new ArrayList<>(targetObjects));
            Set<String> existingTargetObjects = targetDescribes.keySet();
            
            // 移除不存在目标对象对应的源对象数据
            Map<String, List<IObjectData>> detailsData = param.getDetailObjectData();
            if (Objects.nonNull(detailsData)) {
                rules.stream()
                    .filter(rule -> Objects.nonNull(rule.getMdType()) && rule.getMdType() == 1) // 只处理从对象规则，处理mdType为空的情况
                    .filter(rule -> Objects.nonNull(rule.getSourceApiName())) // 确保源对象名不为空
                    .forEach(rule -> {
                        if (!existingTargetObjects.contains(rule.getTargetApiName())) {
                            detailsData.remove(rule.getSourceApiName());
                            log.info("remove invalid detail object data: {}", rule.getSourceApiName());
                        }
                    });
            }
        }
    }

    @Override
    public MappingDataResult mappingDataByConvertRule(User user, MappingDataArg arg, BiConsumer<IObjectMappingParams, List<IObjectMappingRuleInfo>> consumer) {
        IObjectMappingParams param = arg.toMappingParams();
        IObjectMappingParams result;
        try {
            String ruleApiName = arg.getRuleApiName();
            List<IObjectMappingRuleInfo> rules = findConvertRuleByApiName(user, ruleApiName);
            consumer.accept(param, rules);
            removeFieldByFuncPrivilege(user, param);
            result = mappingRuleService.getObjectFieldMappingIgnoreDB(user.getTenantId(), param, rules);
            processResult(user, result, rules, arg.getRuleApiName());
        } catch (MetadataServiceException e) {
            log.warn("Error in mappingData", e);
            throw new ValidateException(e.getMessage());
        }

        return MappingDataResult.parse(result);
    }

    @Override
    public MappingDataResult mappingDataByRuleConfig(User user, MappingDataArg arg, List<IObjectMappingRuleInfo> rules, UnaryOperator<IObjectMappingParams> function) {
        IObjectMappingParams param = arg.toMappingParams();
        IObjectMappingParams result;
        try {
            removeFieldByFuncPrivilege(user, param);
            result = mappingRuleService.getObjectFieldMappingIgnoreDB(user.getTenantId(), param, rules);
            function.apply(result);
            processResult(user, result, rules, arg.getRuleApiName());
        } catch (MetadataServiceException e) {
            log.warn("Error in mappingData", e);
            throw new ValidateException(e.getMessage());
        }

        return MappingDataResult.parse(result);
    }

    @Override
    public List<IObjectMappingRuleInfo> findConvertRuleByApiName(User user, String ruleApiName) {
        List<IObjectMappingRuleInfo> rules;
        try {
            rules = mappingRuleService.getObjectMappingRuleByRuleApiNameAction(user.getTenantId(), ruleApiName, ActionEnum.CONVERSION_RULES, ActionContextExt.of(user).getContext());
        } catch (MetadataServiceException e) {
            log.warn("Error in findConvertRuleByApiName convert rule", e);
            throw new ValidateException(e.getMessage());
        }
        return rules;
    }

    private void processResult(User user, IObjectMappingParams result, List<IObjectMappingRuleInfo> rules, String ruleApiName) {
        //没有映射的字段计算默认值
        fillInfo(result, rules, user);
        String apiName = result.getObjectData().getDescribeApiName();
        Map<String, List<IObjectData>> detailObjectData = result.getDetailObjectData();
        //过滤没有新建功能权限的从对象数据
        if (AppFrameworkConfig.checkDetailObjectPrivilegeInMapping(ruleApiName)) {
            filterDetailsByFunctionPrivilege(user, detailObjectData);
        }
        Set<String> apiNames = Sets.newHashSet(detailObjectData.keySet());
        if (!Strings.isNullOrEmpty(apiName)) {
            apiNames.add(apiName);
        }
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjectsWithoutCopyIfGray(user.getTenantId(), apiNames);
        IObjectDescribe masterDescribe = describeMap.get(apiName);
        describeMap.remove(apiName);
        //主从一起映射的主从字段改成空
        clearMasterDetailFieldValue(detailObjectData, masterDescribe, describeMap);
        //过滤主从业务类型不匹配的从对象数据
        filterDetailsByUnmatchedRecordType(user, result.getObjectData(), detailObjectData, masterDescribe);
        //清空不可见的业务类型
        removeRecordType(user, result);
        //过滤业务类型为空的从对象数据
        if (AppFrameworkConfig.checkDetailObjectPrivilegeInMapping(ruleApiName)) {
            filterDetailsByInvalidRecordType(user, detailObjectData);
        }
        fillDataOwner(result, rules, user);
        fillDescribeId(result, describeMap, masterDescribe);
        // 填充主从对象的国家地区字段标签
        metaDataMiscService.fillMasterDetailCountryAreaLabels(user, masterDescribe, Lists.newArrayList(result.getObjectData()), 
                describeMap, result.getDetailObjectData());
        calculateDefaultValue(user, result, rules, masterDescribe, describeMap, detailObjectData);
        metaDataMiscService.fillPhoneNumberInformation(masterDescribe, result.getObjectData());
        fillLookupName(user, result);
        convertApiName(result);
        fillDeptAndOrg(result, rules, user, masterDescribe);
        fillUserInfo(user, Lists.newArrayList(result.getObjectData()), Lists.newArrayList(masterDescribe));
        List<IObjectData> detailDataList = result.getDetailObjectData().values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        fillUserInfo(user, detailDataList, Lists.newArrayList(describeMap.values()));
        fillRichTextImageInfo(user, masterDescribe, Lists.newArrayList(result.getObjectData()));
    }

    private void fillRichTextImageInfo(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        metaDataMiscService.fillRichTextImageInfo(objectDescribe, dataList, user);
    }

    private void filterDetailsByFunctionPrivilege(User user, Map<String, List<IObjectData>> detailObjectData) {
        if (User.SUPPER_ADMIN_USER_ID.equals(user.getUserId()) || CollectionUtils.empty(detailObjectData)) {
            return;
        }
        List<String> detailApiNames = Lists.newArrayList(detailObjectData.keySet());
        Map<String, Map<String, Boolean>> privilegeMap = functionPrivilegeService.batchFunPrivilegeCheck(user, detailApiNames,
                Lists.newArrayList(ObjectAction.CREATE.getActionCode()));
        detailApiNames.forEach(apiName -> {
            if (!Boolean.TRUE.equals(privilegeMap.getOrDefault(apiName, Collections.emptyMap()).get(ObjectAction.CREATE.getActionCode()))) {
                detailObjectData.remove(apiName);
            }
        });
    }

    private void filterDetailsByInvalidRecordType(User user, Map<String, List<IObjectData>> detailObjectData) {
        if (User.SUPPER_ADMIN_USER_ID.equals(user.getUserId()) || CollectionUtils.empty(detailObjectData)) {
            return;
        }
        detailObjectData.forEach((detailApiName, detailDataList) -> detailDataList.removeIf(x -> Strings.isNullOrEmpty(x.getRecordType())));
    }

    private void filterDetailsByUnmatchedRecordType(User user, IObjectData masterData, Map<String, List<IObjectData>> detailObjectData, IObjectDescribe masterDescribe) {
        //函数调用接口用户身份是-10000 数据都从数据库查询,无需过滤业务类型
        if (User.SUPPER_ADMIN_USER_ID.equals(user.getUserId()) || CollectionUtils.empty(detailObjectData)) {
            return;
        }
        String recordType = masterData.getRecordType();
        if (StringUtils.isNotEmpty(recordType)) {
            detailObjectData.forEach((apiName, dataList) -> {
                List<String> matchRecordTypes = recordTypeLogicService.findValidAndMatchRecordTypes(user, masterDescribe.getApiName(),
                        recordType, apiName);
                dataList.removeIf(x -> !matchRecordTypes.contains(x.getRecordType()));
            });
        }
    }

    private static void clearMasterDetailFieldValue(Map<String, List<IObjectData>> detailObjectData, IObjectDescribe masterDescribe,
                                                    Map<String, IObjectDescribe> detailDescribeMap) {
        detailObjectData.forEach((objectApiName, datalist) -> {
            if (CollectionUtils.empty(detailDescribeMap) || detailDescribeMap.get(objectApiName) == null) {
                return;
            }
            ObjectDescribeExt.of(detailDescribeMap.get(objectApiName)).getMasterDetailFieldName(masterDescribe.getApiName())
                    .ifPresent(masterDetailApiName -> datalist.forEach(data -> data.set(masterDetailApiName, null)));
        });
    }

    @Override
    public IObjectData mappingData(User user, IObjectMappingRuleInfo rule, IObjectData objectData) {
        IObjectMappingParams param = new ObjectMappingParams();
        param.setObjectData(objectData);
        try {
            IObjectMappingParams objectFieldMapping = mappingRuleService.getObjectFieldMapping(user.getTenantId(), param, Lists.newArrayList(rule));
            return objectFieldMapping.getObjectData();
        } catch (MetadataServiceException e) {
            log.warn("mappingData fail! ei:{}", user.getTenantId(), e);
            throw new ValidateException(e.getMessage());
        }
    }

    private void fillUserInfo(User user, List<IObjectData> dataList, List<IObjectDescribe> describeList) {
        describeList.forEach(describe -> {
            List<String> empFields = ObjectDescribeExt.of(describe)
                    .stream()
                    .filter(a -> Objects.equals(a.getType(), IFieldType.OUT_EMPLOYEE)
                            || (!ObjectDescribeExt.of(describe).isSlaveObject() && Objects.equals(a.getApiName(), OUT_OWNER)))
                    .map(IFieldDescribe::getApiName)
                    .collect(Collectors.toList());
            metaDataMiscService.fillUserInfo(describe, dataList, user, false, empFields);
        });
    }

    private void fillDataOwner(IObjectMappingParams result, List<IObjectMappingRuleInfo> rules, User user) {
        if (CollectionUtils.empty(rules)) {
            return;
        }
        Set<String> apiNames = rules.stream()
                .filter(rule -> rule.getFieldMapping().stream()
                        .noneMatch(fieldMapping -> IObjectData.OWNER.equals(fieldMapping.getTargetFieldName())))
                .map(IObjectMappingRuleInfo::getTargetApiName)
                .collect(Collectors.toSet());
        if (CollectionUtils.empty(apiNames)) {
            return;
        }
        // 处理主对象
        IObjectData objectData = result.getObjectData();
        String userId = user.getUpstreamOwnerIdOrUserId();
        if (Strings.isNullOrEmpty(userId)) {
            return;
        }
        if (apiNames.contains(objectData.getDescribeApiName())) {
            ObjectDataExt.setDataOwner(userId, Lists.newArrayList(objectData));
        }
        result.getDetailObjectData().forEach((apiName, dataList) -> {
            if (apiNames.contains(apiName)) {
                ObjectDataExt.setDataOwner(userId, dataList);
            }
        });

    }

    private void fillDeptAndOrg(IObjectMappingParams result, List<IObjectMappingRuleInfo> rules, User user, IObjectDescribe masterDescribe) {
        rules.stream().filter(a -> Strings.isNullOrEmpty(a.getMasterRuleApiName())).findFirst()
                .ifPresent(rule -> rule.getFieldMapping().stream()
                        .filter(fieldMapping -> Objects.equals(fieldMapping.getTargetFieldName(), ObjectDataExt.OWNER))
                        .forEach(x -> {
                            ObjectDataExt dataExt = ObjectDataExt.of(result.getObjectData());
                            dataExt.getOwnerId().ifPresent(ownerId -> {
                                Map<String, MainDeptInfo> deptInfoMap = orgService.getMainDeptInfo(user.getTenantId(), ownerId,
                                        Lists.newArrayList(ownerId));
                                // 处理查到的负责人主属部门id
                                MainDeptInfo mainDeptInfo = deptInfoMap.get(ownerId);
                                String deptId = Objects.nonNull(mainDeptInfo) ? mainDeptInfo.getDeptId() :
                                        (User.SUPPER_ADMIN_USER_ID.equals(ownerId) ? User.COMPANY_ID : "");
                                if (Strings.isNullOrEmpty(dataExt.getDataOwnDepartmentId())) {
                                    dataExt.setDataOwnDepartmentId(deptId);
                                }
                                result.getDetailObjectData().forEach((apiName, dataList) -> dataList.stream()
                                        .filter(data -> Strings.isNullOrEmpty(ObjectDataExt.of(data).getDataOwnDepartmentId()))
                                        .forEach(data -> ObjectDataExt.of(data).setDataOwnDepartmentId(deptId)));
                                if (ObjectDescribeExt.of(masterDescribe).isOpenOrganization()) {
                                    String userId = dataExt.getOwnerId().orElseGet(user::getUpstreamOwnerIdOrUserId);
                                    // 超级管理员使用 999999
                                    if (User.SUPPER_ADMIN_USER_ID.equals(userId) || Strings.isNullOrEmpty(userId)) {
                                        dataExt.setDataOwnOrganization(Lists.newArrayList(User.COMPANY_ID));
                                        dataExt.set(FieldDescribeExt.getLookupNameByFieldName(ObjectDataExt.DATA_OWN_ORGANIZATION), I18NExt.text(I18NKey.WHOLE_GROUP));
                                        return;
                                    }
                                    OrganizationInfo organizationInfo = orgService.findMainOrgAndDeptByUserId(user.getTenantId(), user.getUserId(), Lists.newArrayList(ownerId));
                                    if (CollectionUtils.empty(dataExt.getDataOwnOrganization())) {
                                        dataExt.setDataOwnOrgByDeptInfo(organizationInfo.getMainOrg(ownerId));
                                        dataExt.set(FieldDescribeExt.getLookupNameByFieldName(ObjectDataExt.DATA_OWN_ORGANIZATION), organizationInfo.getMainOrgName(userId));
                                    }
                                    result.getDetailObjectData().forEach((apiName, dataList) -> dataList.stream()
                                        .filter(data -> CollectionUtils.empty(ObjectDataExt.of(data).getDataOwnOrganization()))
                                        .forEach(data -> ObjectDataExt.of(data).setDataOwnOrgByDeptInfo(organizationInfo.getMainOrg(ownerId))));
                                }
                            });
                        }));
    }

    private void fillDescribeId(IObjectMappingParams result, Map<String, IObjectDescribe> detailDescribeMap, IObjectDescribe masterDescribe) {
        if (Objects.nonNull(masterDescribe)) {
            result.getObjectData().setDescribeId(masterDescribe.getId());
        }
        if (CollectionUtils.notEmpty(result.getDetailObjectData())) {
            result.getDetailObjectData().forEach((a, b) -> b.forEach(x -> x.setDescribeId(detailDescribeMap.get(a).getId())));
        }
    }

    private void fillInfo(IObjectMappingParams result, List<IObjectMappingRuleInfo> rules, User user) {
        rules.stream().filter(a -> Strings.isNullOrEmpty(a.getMasterRuleApiName())).findFirst()
                .ifPresent(rule -> {
                    result.getObjectData().setDescribeApiName(rule.getTargetApiName());
                    result.getObjectData().setCreatedBy(user.getUserId());
                });
        if (CollectionUtils.notEmpty(result.getDetailObjectData())) {
            result.getDetailObjectData().forEach((a, b) -> b.forEach(x -> {
                x.setDescribeApiName(a);
                x.set(ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectLifeStatus.NORMAL.getCode());
                x.setCreatedBy(user.getUserId());
            }));
        }
    }

    @Override
    public IUdefButton findButtonByRuleApiName(String ruleApiName, String describeApiName, User user) {
        List<IUdefButton> buttonList = customButtonService.findButtonsByDescribeApiName(user, describeApiName);
        IUdefButton button = null;
        List<IUdefButton> convertButton = buttonList.stream().
                filter(b -> Objects.equals(b.getButtonType(), MAPPING_ACTION_TYPE)).collect(Collectors.toList());

        for (IUdefButton uDefButton : convertButton) {
            List<IUdefAction> actionList = postActionService.findActionList(user, uDefButton, describeApiName);
            for (IUdefAction action : actionList) {
                ObjectMappingExt.MappingActionParam param = JSON.parseObject(action.getActionParamter(),
                        ObjectMappingExt.MappingActionParam.class);
                String objectMappingApiName = param.getObjectMappingApiName();
                if (Objects.equals(objectMappingApiName, ruleApiName)) {
                    button = uDefButton;
                    break;
                }
            }
        }

        return button;
    }

    @Override
    public void deleteDetailDescribeRule(User user, String detailApiName) {
        try {
            mappingRuleService.deleteObjectMappingRuleByDetailDescribeApiName(user.getTenantId(), detailApiName);
        } catch (MetadataServiceException e) {
            log.error("Error in delete detail describe rule, ei:{}, apiName:{}", user.getTenantId(), detailApiName, e);
            throw new MetaDataBusinessException(I18N.text(I18NKey.DELETE_MAPPING_RULE_EXCEPTION));
        }
    }

    @Override
    public void addFieldMappingRule(User user, Map map) {
        try {
            mappingRuleService.addFieldMappingRule(user.getTenantId(), map);
        } catch (MetadataServiceException e) {
            log.warn("Error in addFieldMappingRule", e);
        }
    }

    private void removeFieldByFuncPrivilege(User user, IObjectMappingParams param) {
        IObjectData objectData = param.getObjectData();
        List<String> apiNameList = getAllApiNames(param, objectData);
        Map<String, Set<String>> invisibleFields = functionPrivilegeService.getUnauthorizedFields(user, apiNameList);

        doRemoveFields(Lists.newArrayList(objectData), invisibleFields);
        if (!CollectionUtils.empty(param.getDetailObjectData())) {
            param.getDetailObjectData().forEach((k, v) -> doRemoveFields(v, invisibleFields));
        }
    }

    private void doRemoveFields(List<IObjectData> objectDataList, Map<String, Set<String>> invisibleFields) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }

        objectDataList.forEach(objectData -> {
            ObjectDataExt dataExt = ObjectDataExt.of(objectData);
            Set<String> fields = invisibleFields.get(dataExt.getDescribeApiName());
            if (!CollectionUtils.empty(fields)) {
                Map<String, Object> stringObjectMap = dataExt.toMap();
                fields.forEach(stringObjectMap::remove);
            }
        });
    }

    private void removeRecordType(User user, IObjectMappingParams result) {
        //函数调是系统用户,主从数据都是查询出来,无需校验业务类型
        if (User.SUPPER_ADMIN_USER_ID.equals(user.getUserId())) {
            return;
        }
        IObjectData objectData = result.getObjectData();
        List<String> apiNameList = getAllApiNames(result, objectData);
        //清掉不可用的业务类型
        Map<String, List<IRecordTypeOption>> recordTypeMap = recordTypeLogicService.findValidRecordTypeListMap
                (apiNameList, user);
        doRemoveRecordType(Lists.newArrayList(objectData), recordTypeMap);

        if (!CollectionUtils.empty(result.getDetailObjectData())) {
            result.getDetailObjectData().forEach((k, v) -> doRemoveRecordType(v, recordTypeMap));
        }
    }

    private List<String> getAllApiNames(IObjectMappingParams result, IObjectData objectData) {
        List<String> apiNameList = Lists.newArrayList();
        if (!Objects.isNull(objectData)) {
            apiNameList.add(objectData.getDescribeApiName());
        }

        if (!CollectionUtils.empty(result.getDetailObjectData())) {
            apiNameList.addAll(result.getDetailObjectData().keySet());
        }
        return apiNameList;
    }

    private void doRemoveRecordType(List<IObjectData> dataList, Map<String, List<IRecordTypeOption>> recordTypeMap) {
        dataList.forEach(objectData -> {
            ObjectDataExt dataExt = ObjectDataExt.of(objectData);
            List<IRecordTypeOption> options = recordTypeMap.get(dataExt.getDescribeApiName());
            String recordType = dataExt.getRecordType();
            if (!CollectionUtils.empty(options) && !Strings.isNullOrEmpty(recordType)) {
                Optional<IRecordTypeOption> first = options.stream().filter(a -> Objects.equals(a.getApiName(),
                        recordType)).findFirst();
                if (!first.isPresent()) {
                    dataExt.toMap().remove(MultiRecordType.RECORD_TYPE);
                }
            }
        });
    }


    private void fillLookupName(User user, IObjectMappingParams result) {
        //查询关联对象的数据（有引用字段的时候才执行）
        if (!Objects.isNull(result.getObjectData())) {
            doFillLookupName(user, Lists.newArrayList(result.getObjectData()), result.getObjectData().getDescribeApiName());
        }

        if (CollectionUtils.empty(result.getDetailObjectData())) {
            return;
        }

        result.getDetailObjectData().forEach((apiName, list) -> doFillLookupName(user, list, apiName, result.getObjectData()));
    }

    private void doFillLookupName(User user, List<IObjectData> dataList, String apiName) {
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), apiName);
        metaDataMiscService.fillObjectDataWithRefObject(objectDescribe, dataList, user, null, true);
        quoteValueService.fillQuoteFieldValue(user, dataList, objectDescribe, null, false);
    }

    private void doFillLookupName(User user, List<IObjectData> dataList, String apiName, IObjectData masterData) {
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), apiName);
        metaDataMiscService.fillObjectDataWithRefObject(objectDescribe, dataList, user, null, true);
        List<Quote> quoteList = ObjectDescribeExt.of(objectDescribe).getQuoteFieldDescribes();
        quoteValueService.fillQuoteFieldValue(user, dataList, objectDescribe, null, false, quoteList, masterData);
    }

    private void convertApiName(IObjectMappingParams result) {
        if (CollectionUtils.empty(result.getDetailObjectData())) {
            return;
        }
        Map<String, List<IObjectData>> newDetails = Maps.newHashMap();
        newDetails.putAll(result.getDetailObjectData());
        result.setDetailObjectData(newDetails);
    }

    private void calculateDefaultValue(User user, IObjectMappingParams result, List<IObjectMappingRuleInfo> rules,
                                       IObjectDescribe masterDescribe, Map<String, IObjectDescribe> detailDescribeMap,
                                       Map<String, List<IObjectData>> detailObjectData) {
        if (ignoreCalc(rules)) {
            return;
        }
        calcDefaultValueWithConstant(Lists.newArrayList(result.getObjectData()), rules, masterDescribe);
        detailDescribeMap.forEach((detailApiName, detailDescribe) ->
                calcDefaultValueWithConstant(detailObjectData.get(detailApiName), rules, detailDescribe));
        calcFormulaValue(user, result, rules, detailObjectData, masterDescribe, detailDescribeMap);
    }

    private void calcDefaultValueWithConstant(List<IObjectData> dataList, List<IObjectMappingRuleInfo> rules, IObjectDescribe describe) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        List<IFieldDescribe> fieldNeedToCalc = describeExt.getNeedCalculateConstantDefaultValue();

        List<String> ruleFields = Lists.newArrayList();
        rules.stream().filter(a -> Objects.equals(a.getTargetApiName(), describe.getApiName())).forEach(rule -> {
            List<IObjectMappingRuleDetailInfo> fieldMapping = rule.getFieldMapping();
            fieldMapping.forEach(f -> ruleFields.add(f.getTargetFieldName()));
        });

        List<IFieldDescribe> validFields = Lists.newArrayList();
        fieldNeedToCalc.forEach(a -> {
            if (!ruleFields.contains(a.getApiName())) {
                validFields.add(a);
            }
        });

        validFields.forEach(a -> dataList.forEach(data -> {
            if (!ObjectDataExt.of(data).toMap().containsKey(a.getApiName())) {
                data.set(a.getApiName(), a.getDefaultValue());
            }
        }));
    }

    private boolean ignoreCalc(List<IObjectMappingRuleInfo> rules) {
        return rules.stream().anyMatch(x -> ObjectAPINameMapping.BizQuery.getApiName().equals(x.getSourceApiName()));
    }

    private void calcFormulaValue(User user, IObjectMappingParams result, List<IObjectMappingRuleInfo> rules,
                                  Map<String, List<IObjectData>> detailObjectData, IObjectDescribe masterDescribe,
                                  Map<String, IObjectDescribe> detailDescribeMap) {
        List<IObjectDescribe> detailDescribes = CollectionUtils.notEmpty(detailDescribeMap) ?
                Lists.newArrayList(detailDescribeMap.values()) : Lists.newArrayList();
        CalculateFields calculateFields = fieldRelationCalculateService.computeCalculateFieldsForAddAction(masterDescribe,
                detailDescribes, false);

        //已经配置映射规则的字段不需要计算
        Lists.newArrayList(calculateFields.getCalculateFieldMap().keySet()).forEach(objectApiName -> {
            Set<String> ruleFields = Sets.newHashSet();
            rules.stream().filter(a -> Objects.equals(a.getTargetApiName(), objectApiName)).forEach(rule -> {
                List<IObjectMappingRuleDetailInfo> fieldMapping = rule.getFieldMapping();
                fieldMapping.forEach(f -> ruleFields.add(f.getTargetFieldName()));
            });
            calculateFields.getCalculateFieldMap().get(objectApiName).removeIf(x -> ruleFields.contains(x.getFieldName()));
        });
        computeService.batchCalculateBySortFields(user, result.getObjectData(), detailObjectData, calculateFields);
    }

    @Override
    public List<IObjectMappingRuleInfo> getObjectMappingRule(String tenantId, String ruleName, int status, String bizType) {
        List<IObjectMappingRuleInfo> rules;
        try {
            IActionContext actionContext = ActionContextExt.of(User.systemUser(tenantId)).getContext();
            rules = mappingRuleService.getObjectMappingRule(tenantId, ruleName, status, bizType, actionContext);
            // 根据可用预置对象过滤规则
            filterByAvailableObject(tenantId, rules);
        } catch (MetadataServiceException e) {
            log.warn("Error in getObjectMappingRule MappingRule", e);
            throw new ValidateException(e.getMessage());
        }
        return rules;
    }

}
