package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.core.model.ControllerListener;
import com.facishare.paas.appframework.core.predef.listener.prm.PrmDetailControllerListener;
import com.facishare.paas.appframework.core.predef.listener.prm.PrmNewDetailControllerListener;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.appframework.metadata.GroupComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IGroupComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2019/6/25
 */
public class StandardNewDetailController extends StandardDetailController {

    @Override
    protected ILayout getLayout() {
        ILayout layout = serviceFacade.getLayoutLogicService().getLayoutWithComponents(buildLayoutContext(), data.getRecordType(),
                describe, data, relatedObjects, detailObjects, getUnauthorizedFields(), PageType.NewDetail);
        stopWatch.lap("getLayoutWithComponents");

        //回收站详情页不展示自定义按钮
        if (arg.isFromRecycleBin()) {
            return layout;
        }

        if (defaultEnableQixinGroup) {
            LayoutExt.of(layout).getComponentByApiName(ComponentExt.TOP_COMPONENT_NAME).ifPresent(x ->
                    GroupComponentExt.of((IGroupComponent) x).getChildComponentByName(ComponentExt.TEAM_COMPONENT_NAME).ifPresent(y ->
                            ComponentExt.of(y).addButton(ButtonExt.generateQinxinGroupButton())));
        }
        stopWatch.lap("generateQixinGroupButton");

        return layout;
    }

    @Override
    public List<Class<? extends ControllerListener<Arg, Result>>> getControllerListenerClassList() {
        List<Class<? extends ControllerListener<Arg, Result>>> controllerListenerClassList = getSuperControllerListenerClassList();
        if (AppIdMapping.isPRM(controllerContext.getAppId())) {
            controllerListenerClassList.remove(PrmDetailControllerListener.class);
            controllerListenerClassList.add(PrmNewDetailControllerListener.class);
        }
        return controllerListenerClassList;
    }

    /**
     * 获取父类的ControllerListener列表
     * @return 父类的ControllerListener列表
     */
    protected List<Class<? extends ControllerListener<Arg, Result>>> getSuperControllerListenerClassList() {
        return super.getControllerListenerClassList();
    }

    @Override
    protected List<IObjectDescribe> findRelatedObjects() {
        List<IFieldDescribe> relatedFields = serviceFacade.findRelatedFields(controllerContext.getTenantId(),
                arg.getObjectDescribeApiName());
        return relatedFields.stream().map(x -> buildSimpleDescribe(x)).collect(Collectors.toList());
    }

    private IObjectDescribe buildSimpleDescribe(IFieldDescribe field) {
        IObjectDescribe describe = new ObjectDescribe();
        describe.setApiName(field.getDescribeApiName());
        describe.setFieldDescribes(Lists.newArrayList(field));
        return describe;
    }
}
