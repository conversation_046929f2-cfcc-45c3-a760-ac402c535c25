package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.metadata.dto.DetailResource;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class MasterSlaveDataModel {
    private List<String> fromMasterIds;
    private List<DetailResource> fromDetails;
    private IObjectData objectData;
    private Map<String, List<IObjectData>> details;

}
