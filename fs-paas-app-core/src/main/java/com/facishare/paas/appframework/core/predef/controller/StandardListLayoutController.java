package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.ListLayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.layout.LayoutAgentType;
import com.facishare.paas.appframework.metadata.layout.LayoutContext;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import de.lab4inf.math.util.Strings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.facishare.paas.appframework.core.predef.controller.StandardListLayoutController.Arg;
import static com.facishare.paas.appframework.core.predef.controller.StandardListLayoutController.Result;

/**
 * create by zhaoju on 2020/11/13
 */
public class StandardListLayoutController extends PreDefineController<Arg, Result> {
    protected ObjectDescribeExt objectDescribeExt;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        stopWatch.lap("doFunPrivilegeCheck");
        IObjectDescribe describe = findObjectDescribe();
        objectDescribeExt = ObjectDescribeExt.of(describe);
        initLayoutContext();
        stopWatch.lap("initLayoutContext");
    }

    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            LayoutContext.remove();
        }
    }

    private void initLayoutContext() {
        LayoutContext layoutContext = LayoutContext.get();
        layoutContext.setLayoutAgentType(LayoutAgentType.of(arg.getLayoutAgentType()));
    }

    private IObjectDescribe findObjectDescribe() {
        return serviceFacade.findObject(controllerContext.getTenantId(), controllerContext.getObjectApiName());
    }

    @Override
    protected Result doService(Arg arg) {
        ILayout listLayout = getListLayout();
        ListLayoutExt.of(listLayout).getFirstListComponent()
                .ifPresent(listComponentExt -> listComponentExt.fillAttributeFromLayout(listLayout));
        return buildResult(listLayout);
    }

    private ILayout getListLayout() {
        String listLayoutApiName = getListLayoutApiName();
        if (Strings.isNullOrEmpty(listLayoutApiName)) {
            return serviceFacade.getLayoutLogicService().getListLayoutWitchComponents(buildLayoutContext(),
                    objectDescribeExt, PageType.ListLayout, null, arg.getRecordType());
        }
        return serviceFacade.getLayoutLogicService().getListLayoutByApiNameWitchComponents(controllerContext.getUser(), listLayoutApiName,
                objectDescribeExt, PageType.ListLayout, null);
    }

    protected LayoutLogicService.LayoutContext buildLayoutContext() {
        return LayoutLogicService.LayoutContext.of(controllerContext.getUser(), controllerContext.getAppId());
    }

    protected String getListLayoutApiName() {
        return arg.getListLayoutApiName();
    }

    protected Result buildResult(ILayout listLayout) {
        Result result = Result.builder()
                .layout(LayoutDocument.of(listLayout))
                .build();

        if (includeDescribe()) {
            result.setDescribe(ObjectDescribeDocument.handleDescribeCache(arg.getDescribeVersionMap(), objectDescribeExt.getObjectDescribe()));
        }

        return result;
    }

    private boolean includeDescribe() {
        return !Boolean.FALSE.equals(arg.getIncludeDescribe());
    }

    @Data
    public static class Arg {
        private String describeApiName;
        /**
         * mobile 终端布局
         * web 网页端布局
         */
        private String layoutAgentType;
        private Boolean includeDescribe = true;
        private Map<String, Integer> describeVersionMap;
        private String recordType;
        /**
         * 指定列表页布局 apiName
         */
        private String listLayoutApiName;
        /**
         * 拓展参数
         */
        private Map<String, Object> extraParams;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private ObjectDescribeDocument describe;
        private LayoutDocument layout;

    }
}
