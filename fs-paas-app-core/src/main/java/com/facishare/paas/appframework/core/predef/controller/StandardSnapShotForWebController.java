package com.facishare.paas.appframework.core.predef.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.service.dto.log.GetSnapShotForWeb;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Optional;

/**
 * @Desc
 * <AUTHOR>
 * @CreateTime 2019-09-24 15:34
 */
public class StandardSnapShotForWebController extends AbstractStandardSnapShotController<GetSnapShotForWeb.Arg, GetSnapShotForWeb.Result> {

    @Override
    protected Optional<IObjectDescribe> getDescribe(GetSnapShotForWeb.Result result) {
        return Optional.ofNullable(result)
                .map(GetSnapShotForWeb.Result::getDescribe)
                .filter(CollectionUtils::notEmpty)
                .map(ObjectDescribeExt::of);
    }

    @Override
    protected Optional<IObjectData> getObjectData(GetSnapShotForWeb.Result result) {
        return Optional.ofNullable(result)
                .map(GetSnapShotForWeb.Result::getData)
                .filter(CollectionUtils::notEmpty)
                .map(ObjectDataExt::of);
    }

    @Override
    protected GetSnapShotForWeb.Result doService(GetSnapShotForWeb.Arg arg) {
        LogInfo logInfo = serviceFacade.getLogById(arg.getApiName(), arg.getLogId(), controllerContext.getUser());
        String sourceId = arg.getSourceId();
        String sourceApiName = arg.getSourceApiName();
        IObjectDescribe objectDescribe;
        Map<String, Object> objData;
        if (StringUtils.isNoneEmpty(sourceId, sourceApiName)) {
            objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), sourceApiName);
            objData = CollectionUtils.nullToEmpty(logInfo.getSnapshot().getSourceSnapshot())
                    .stream().filter(data -> sourceId.equals(data.get(IObjectData.ID)))
                    .findFirst().orElse(Maps.newHashMap());
        } else {
            objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), arg.getApiName());
            objData = logInfo.getSnapshot().getSnapshot();
        }
        //权限检查
        serviceFacade.doDataPrivilegeCheck(controllerContext.getUser(),
                Lists.newArrayList(ObjectDataExt.of(objData)),
                objectDescribe,
                ObjectAction.VIEW_DETAIL.getActionCode());
        handleBoolean(objData);
//        objData = appendReferenceObjInfo(objData, objectDescribe, context.getUser());
        objData = decorateObjectData(controllerContext.getUser(), objData, objectDescribe);
        // 补充负责人主属部门字段
        fillOwnerDeptName(controllerContext.getUser(), objData);
        fillFieldInfo(controllerContext.getUser(), objectDescribe, objData);
        fillMaskFieldValue(objData, objectDescribe);
        fillDimensionFiledValue(objData, objectDescribe);
        GetSnapShotForWeb.Result result = new GetSnapShotForWeb.Result();
        result.setSnapType(getSnapShotType(logInfo));
        result.setData(objData);
        result.setDescribe(objectDescribe == null ? null : ObjectDescribeExt.of(objectDescribe).toMap());
        result.setLayout(getSnapShotLayout(objData, objectDescribe, controllerContext));
        // 新修改记录去掉恢复
        result.setCanRecover(false);
        result.setObjectDescribeExt(getFieldExtra(objectDescribe));
        return result;
    }
}
