package com.facishare.paas.appframework.metadata.search.ast;

import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;

import java.util.Collections;
import java.util.List;

/**
 * 表达式解析器类，实现了递归下降解析算法
 * 支持解析以下语法结构:
 * - 逻辑运算: AND, OR
 * - 基本表达式: 数字, 括号表达式
 * <p>
 * 语法规则:
 * expression = orExpr
 * orExpr = andExpr ("OR" andExpr)*
 * andExpr = primary ("AND" primary)*
 * primary = NUMBER | "(" expression ")"
 *
 * <AUTHOR>
 * @since 2025/5/19
 */
public class Parser {
    public static final String FILTER_INDEX_FIELD_NAME = "__FILTER_INDEX__";
    /**
     * 待解析的token列表
     */
    private List<Token> tokens;
    /**
     * 当前解析位置
     */
    private int pos = 0;

    /**
     * 构造解析器
     *
     * @param tokens 待解析的token列表
     */
    public Parser(List<Token> tokens) {
        this.tokens = tokens;
    }

    /**
     * 解析表达式的入口方法
     *
     * @return 解析后的表达式语法树根节点
     */
    public SearchQuery parseExpression() {
        return parseOrExpr();
    }

    /**
     * 解析OR表达式
     * 语法规则: orExpr = andExpr ("OR" andExpr)*
     */
    private SearchQuery parseOrExpr() {
        SearchQuery left = parseAndExpr();
        while (match(TokenType.OR)) {
            SearchQuery right = parseAndExpr();
            left = left.or(right);
        }
        return left;
    }

    /**
     * 解析AND表达式
     * 语法规则: andExpr = primary ("AND" primary)*
     */
    private SearchQuery parseAndExpr() {
        SearchQuery left = parsePrimary();
        while (match(TokenType.AND)) {
            SearchQuery right = parsePrimary();
            left = left.and(right);
        }
        return left;
    }

    /**
     * 解析基本表达式
     * 语法规则: primary = NUMBER | "(" expression ")"
     */
    private SearchQuery parsePrimary() {
        if (match(TokenType.LPAREN)) {
            SearchQuery expr = parseExpression();
            expect(TokenType.RPAREN);
            return expr;
        } else if (match(TokenType.NUMBER)) {
            // 获取数字token的文本内容，这代表filters列表中的索引
            String filterIndex = previous().getText();
            
            // 创建一个临时的Filter来存储索引信息
            // 使用一个特殊的字段名来标识这是一个索引占位符
            IFilter tempFilter = new Filter();
            tempFilter.setFieldName(FILTER_INDEX_FIELD_NAME);
            tempFilter.setFieldValues(Collections.singletonList(filterIndex));
            
            return SearchQueryImpl.filter(tempFilter);
        }
        throw new RuntimeException("Unexpected token: " + peek());
    }

    /**
     * 匹配并消费指定类型的token
     *
     * @param type 期望的token类型
     * @return 如果当前token匹配则返回true并前进位置，否则返回false
     */
    private boolean match(TokenType type) {
        if (check(type)) {
            pos++;
            return true;
        }
        return false;
    }

    /**
     * 期望下一个token是指定类型，如果不是则抛出异常
     *
     * @param type 期望的token类型
     * @throws RuntimeException 当token类型不匹配时
     */
    private void expect(TokenType type) {
        if (!match(type)) {
            throw new RuntimeException("Expected token " + type + " but found " + peek());
        }
    }

    /**
     * 检查当前token是否为指定类型
     *
     * @param type 要检查的token类型
     * @return 如果当前token是指定类型则返回true
     */
    private boolean check(TokenType type) {
        return !isAtEnd() && tokens.get(pos).getType() == type;
    }

    /**
     * 获取前一个token
     */
    private Token previous() {
        return tokens.get(pos - 1);
    }

    /**
     * 获取当前token
     */
    private Token peek() {
        return tokens.get(pos);
    }

    /**
     * 检查是否已到达token列表末尾
     */
    private boolean isAtEnd() {
        return pos >= tokens.size();
    }
}
