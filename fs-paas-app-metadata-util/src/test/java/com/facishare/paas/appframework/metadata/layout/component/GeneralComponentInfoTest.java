package com.facishare.paas.appframework.metadata.layout.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class GeneralComponentInfoTest {

  private GeneralComponentInfo generalComponentInfo;

  @BeforeEach
  void setUp() {
    generalComponentInfo = new GeneralComponentInfo();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试GeneralComponentInfo的无参构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试无参构造函数")
  void testDefaultConstructor() {
    GeneralComponentInfo info = new GeneralComponentInfo();
    assertNotNull(info, "无参构造函数创建的对象不应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试GeneralComponentInfo的Map构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = Maps.newHashMap();
    map.put(IGeneralComponentInfo.PAGE_TYPE, "detail");
    map.put(IGeneralComponentInfo.HIDE_TYPE, Lists.newArrayList("mobile", "pc"));
    
    GeneralComponentInfo info = new GeneralComponentInfo(map);
    assertNotNull(info, "Map构造函数创建的对象不应为空");
    assertEquals("detail", info.getPageType(), "应正确设置页面类型");
    assertEquals(2, info.getHideType().size(), "应正确设置隐藏类型列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getRenderType方法
   */
  @Test
  @DisplayName("正常场景 - 测试getRenderType方法")
  void testGetRenderType() {
    // getRenderType方法返回null
    assertNull(generalComponentInfo.getRenderType(), "getRenderType应返回null");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getPageType方法
   */
  @Test
  @DisplayName("正常场景 - 测试getPageType方法")
  void testGetPageType() {
    // 初始状态应为null
    assertNull(generalComponentInfo.getPageType(), "初始页面类型应为null");
    
    // 通过Map构造函数设置页面类型
    Map<String, Object> map = Maps.newHashMap();
    map.put(IGeneralComponentInfo.PAGE_TYPE, "list");
    GeneralComponentInfo info = new GeneralComponentInfo(map);
    assertEquals("list", info.getPageType(), "应正确获取页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试copy方法
   */
  @Test
  @DisplayName("正常场景 - 测试copy方法")
  void testCopy() {
    // copy方法返回null
    assertNull(generalComponentInfo.copy(), "copy方法应返回null");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getHideType方法 - 正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试getHideType方法")
  void testGetHideType_NormalScenario() {
    // 设置隐藏类型
    List<String> hideTypes = Lists.newArrayList("mobile", "tablet");
    generalComponentInfo.setHideType(hideTypes);
    
    List<String> result = generalComponentInfo.getHideType();
    assertNotNull(result, "隐藏类型列表不应为空");
    assertEquals(2, result.size(), "隐藏类型列表大小应为2");
    assertTrue(result.contains("mobile"), "应包含mobile");
    assertTrue(result.contains("tablet"), "应包含tablet");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getHideType方法 - 空列表场景
   */
  @Test
  @DisplayName("边界场景 - 测试getHideType空列表")
  void testGetHideType_EmptyList() {
    // 设置空列表
    List<String> emptyList = Lists.newArrayList();
    generalComponentInfo.setHideType(emptyList);
    
    List<String> result = generalComponentInfo.getHideType();
    assertNotNull(result, "结果不应为空");
    assertTrue(result.isEmpty(), "应返回空列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getHideType方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试getHideType null值")
  void testGetHideType_NullValue() {
    // 设置null值
    generalComponentInfo.setHideType(null);
    
    List<String> result = generalComponentInfo.getHideType();
    assertNotNull(result, "结果不应为空");
    assertTrue(result.isEmpty(), "null值应返回空列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setHideType方法
   */
  @Test
  @DisplayName("正常场景 - 测试setHideType方法")
  void testSetHideType() {
    // 测试设置单个元素
    List<String> singleType = Lists.newArrayList("pc");
    generalComponentInfo.setHideType(singleType);
    
    List<String> result = generalComponentInfo.getHideType();
    assertEquals(1, result.size(), "应设置1个隐藏类型");
    assertEquals("pc", result.get(0), "应正确设置pc类型");
    
    // 测试设置多个元素
    List<String> multipleTypes = Lists.newArrayList("mobile", "tablet", "desktop");
    generalComponentInfo.setHideType(multipleTypes);
    
    result = generalComponentInfo.getHideType();
    assertEquals(3, result.size(), "应设置3个隐藏类型");
    assertTrue(result.contains("mobile"), "应包含mobile");
    assertTrue(result.contains("tablet"), "应包含tablet");
    assertTrue(result.contains("desktop"), "应包含desktop");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口实现
   */
  @Test
  @DisplayName("正常场景 - 测试接口实现")
  void testInterfaceImplementation() {
    // 验证实现了IGeneralComponentInfo接口
    assertTrue(generalComponentInfo instanceof IGeneralComponentInfo, 
               "应实现IGeneralComponentInfo接口");
    
    // 验证实现了IComponentInfo接口
    assertTrue(generalComponentInfo instanceof IComponentInfo, 
               "应实现IComponentInfo接口");
    
    // 验证实现了JsonCompatible接口
    assertTrue(generalComponentInfo instanceof com.facishare.paas.metadata.api.JsonCompatible, 
               "应实现JsonCompatible接口");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试序列化版本号
   */
  @Test
  @DisplayName("正常场景 - 测试序列化版本号")
  void testSerialVersionUID() {
    // 验证类有序列化版本号（通过反射检查）
    try {
      java.lang.reflect.Field field = GeneralComponentInfo.class.getDeclaredField("serialVersionUID");
      field.setAccessible(true);
      long serialVersionUID = field.getLong(null);
      assertEquals(2490474385619020130L, serialVersionUID, "序列化版本号应正确");
    } catch (Exception e) {
      fail("应该有serialVersionUID字段");
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试继承的AbstractComponentInfoDocument功能
   */
  @Test
  @DisplayName("正常场景 - 测试继承功能")
  void testInheritedFunctionality() {
    // 验证继承自AbstractComponentInfoDocument
    assertTrue(generalComponentInfo instanceof AbstractComponentInfoDocument, 
               "应继承自AbstractComponentInfoDocument");
    
    // 通过Map构造函数测试继承的功能
    Map<String, Object> map = Maps.newHashMap();
    map.put("custom_field", "custom_value");
    map.put(IGeneralComponentInfo.PAGE_TYPE, "edit");
    
    GeneralComponentInfo info = new GeneralComponentInfo(map);
    assertEquals("edit", info.getPageType(), "应正确处理页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试多次设置和获取操作
   */
  @Test
  @DisplayName("正常场景 - 测试多次设置和获取")
  void testMultipleSetAndGet() {
    // 第一次设置
    List<String> firstTypes = Lists.newArrayList("mobile");
    generalComponentInfo.setHideType(firstTypes);
    assertEquals(1, generalComponentInfo.getHideType().size(), "第一次设置应成功");
    
    // 第二次设置，覆盖之前的值
    List<String> secondTypes = Lists.newArrayList("pc", "tablet");
    generalComponentInfo.setHideType(secondTypes);
    assertEquals(2, generalComponentInfo.getHideType().size(), "第二次设置应覆盖之前的值");
    assertFalse(generalComponentInfo.getHideType().contains("mobile"), "不应包含之前的值");
    assertTrue(generalComponentInfo.getHideType().contains("pc"), "应包含新设置的值");
    assertTrue(generalComponentInfo.getHideType().contains("tablet"), "应包含新设置的值");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试特殊字符串处理
   */
  @Test
  @DisplayName("边界场景 - 测试特殊字符串处理")
  void testSpecialStrings() {
    // 测试包含特殊字符的隐藏类型
    List<String> specialTypes = Lists.newArrayList("mobile-v2", "pc_desktop", "tablet@2x");
    generalComponentInfo.setHideType(specialTypes);
    
    List<String> result = generalComponentInfo.getHideType();
    assertEquals(3, result.size(), "应正确处理特殊字符");
    assertTrue(result.contains("mobile-v2"), "应包含带连字符的值");
    assertTrue(result.contains("pc_desktop"), "应包含带下划线的值");
    assertTrue(result.contains("tablet@2x"), "应包含带@符号的值");
  }
}
