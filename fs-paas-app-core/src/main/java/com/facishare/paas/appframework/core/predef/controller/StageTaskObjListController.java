package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * create by zhangyixuan on 2019/07/31
 */
public class StageTaskObjListController extends StandardListController {

    @Override
    protected void doFunPrivilegeCheck() {
//        super.doFunPrivilegeCheck();
    }

    @Override
    protected BaseListController.Result after(Arg arg, BaseListController.Result result) {
        List<ObjectDataDocument> dataList = result.getDataList();
        List<String> objectApiNameList = dataList.stream().map(item -> String.valueOf(item.get("object_api_name"))).collect(Collectors.toList());
        Map<String, IObjectDescribe> objectDescMap = serviceFacade.findObjects(controllerContext.getTenantId(), objectApiNameList);

        if (!CollectionUtils.empty(dataList)) {
            dataList.forEach(data -> {
                String objectApiName = String.valueOf(data.get("object_api_name"));
                IObjectDescribe objectDesc = objectDescMap.get(objectApiName);
                SelectOneFieldDescribe fieldDescribe = (SelectOneFieldDescribe) objectDesc.getFieldDescribe(String.valueOf(data.get("stage_field_api_name")));
                Optional<ISelectOption> stageOption = fieldDescribe.getOption(String.valueOf(data.get("stage_id")));
                stageOption.ifPresent(item ->  data.put("stage_id", item.getLabel()));
                data.put("stage_field_api_name", fieldDescribe.getLabel());
            });
        }
        super.after(arg, result);
        return result;
    }

}
