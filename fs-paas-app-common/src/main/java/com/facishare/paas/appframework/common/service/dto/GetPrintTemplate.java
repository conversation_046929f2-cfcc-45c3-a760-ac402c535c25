package com.facishare.paas.appframework.common.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * 获取打印模板
 *
 * <AUTHOR>
 * @date 2021/09/23
 */
public interface GetPrintTemplate {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        String templateId;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        PrintTemplateVO printTemplateVO;
    }
    @Data
    class PrintTemplateVO {
        // TODO 去掉无用参数
        private String id;

        private String tenantId;//业务线id

        private String templateId;

        private Integer isToWord = 0;
    }
}
