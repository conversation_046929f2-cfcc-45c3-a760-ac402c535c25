package com.facishare.paas.appframework.metadata.dto.scene;

import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.MetadataContextExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.SearchTemplateExt;
import com.facishare.paas.appframework.metadata.config.IUdefConfig;
import com.facishare.paas.appframework.metadata.config.StandardConfig;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import lombok.Data;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Delegate;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import static com.facishare.paas.metadata.api.service.ISearchTemplateService.IS_OUT_TEMPLATE;

/**
 * create by zhaoju on 2019/05/25
 */
@Data
@RequiredArgsConstructor(staticName = "of")
public class SceneExt {
    @NonNull
    @Delegate
    private IScene scene;



    public static final String BASE_SCENE_LABEL_API = "base_scene_label";

    public static List<IUdefConfig> findButtonConfig(List<IScene> sceneList) {
        return sceneList.stream()
                .map(SceneExt::of)
                .map(SceneExt::generateButtonConfig)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private static IUdefConfig generateButtonConfig(SceneExt sceneExt) {
        if (!sceneExt.isDefaultScene()) {
            return null;
        }
        return StandardConfig.SCENE_CONFIG.getConfig(sceneExt.getObjectDescribeApiName(), sceneExt.getApiName());
    }

    /**
     * 场景字段支持配置字段值占宽度和是否自动折行<br>
     * 校验规则：有field_list校验field_list，没有field_list校验head_field
     * @return true校验通过
     */
    public boolean validateHeadField() {
        if (headFieldTypeIsIncludeLayout()) {
            return true;
        }

        return CollectionUtils.size(getFieldList()) > 1 || CollectionUtils.size(getHeadField()) > 1;
    }

    public boolean headFieldTypeIsAll() {
        return Objects.equals(getHeadFieldType(), SearchTemplateExt.USE_FIELD_LIST);
    }

    public boolean headFieldTypeIsIncludeLayout() {
        return Objects.equals(getHeadFieldType(), SearchTemplateExt.USE_LAYOUT_FIELD);
    }

    public boolean isTenantScene() {
        return SearchTemplateExt.TENANT_SCENE.equals(getType());
    }

    public boolean isDefaultScene() {
        return SearchTemplateExt.DEFAULT_SCENE.equals(getType());
    }

    public boolean isCustomScene() {
        return SearchTemplateExt.CUSTOM_SCENE.equals(getType());
    }

    public static MetadataContextExt getOutTenantInfo(User user) {
        return getOutTenantInfo(user, user.isOutUser());
    }

    public static MetadataContextExt getOutTenantInfo(User user, boolean isOuter) {
        MetadataContextExt contextExt = MetadataContextExt.of(user);
        RequestContext requestContext = RequestContextManager.getContext();
        if (ObjectUtils.isNotEmpty(requestContext)) {
            contextExt.setAppId(requestContext.getAppId());
        }
        if (isOuter) {
            contextExt.objectProperty(IS_OUT_TEMPLATE, true);
        }
        return contextExt;
    }

    public static <T extends IScene> List<T> convertDateFieldFilter2System(List<T> scenes, IObjectDescribe describe) {
        if (CollectionUtils.empty(scenes)) {
            return scenes;
        }
        return scenes.stream()
                .map(scene -> convertDateFieldFilter2System(scene, describe))
                .collect(Collectors.toList());
    }

    public static <T extends IScene> List<T> convertDateFieldFilter2Custom(List<T> scenes, IObjectDescribe describe) {
        if (CollectionUtils.empty(scenes)) {
            return scenes;
        }
        return scenes.stream()
                .map(scene -> convertDateFieldFilter2Custom(scene, describe))
                .collect(Collectors.toList());
    }

    public static <T extends IScene> T convertDateFieldFilter2System(T scene, IObjectDescribe describe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        return convertDateFieldFilter(scene, describeExt, FilterExt::convert2SystemZone);
    }

    public static <T extends IScene> T convertDateFieldFilter2Custom(T scene, IObjectDescribe describe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        return convertDateFieldFilter(scene, describeExt, FilterExt::convert2CustomZone);
    }

    private static <T extends IScene> T convertDateFieldFilter(T scene, ObjectDescribeExt describeExt,
                                                               BiConsumer<ObjectDescribeExt, IFilter> consumer) {
        List<SceneDTO.Where> wheres = scene.getWheres();
        if (CollectionUtils.empty(wheres)) {
            return scene;
        }
        List<SceneDTO.Where> whereList = wheres.stream()
                .map(SceneDTO.Where::toWheres)
                .peek(it -> it.getFilters().forEach(filter -> consumer.accept(describeExt, filter)))
                .map(SceneDTO.Where::fromWheres)
                .collect(Collectors.toList());
        ((SceneDTO) scene).setWheres(whereList);
        return scene;
    }

    /**
     * mobileField为null返回null而不是"null"
     * 此方法不用Jackson将null转为"null"的逻辑 而是直接返回null
     * @param value 一个对象
     * @return String
     */
    public static String toJson(Object value) {
        return value == null ? null : JacksonUtils.toJson(value);
    }
    /**
     * json为blank时返回 null
     * @param json 一个json的字符串
     * @return JSONObject
     */
    public static Map<String, Object> toMap(String json) {
        return StringUtils.isBlank(json) ? null : JacksonUtils.fromJson(json, JSONObject.class);
    }
}
