package com.facishare.paas.appframework.core.predef.handler.edit;

import com.facishare.paas.appframework.core.model.handler.HandlerAttributes;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.facade.AuditLogServiceFacade;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created by zhouwr on 2023/2/1.
 */
@Component
@HandlerProvider(name = "defaultRecordLogEditAfterHandler")
public class DefaultRecordLogEditAfterHandler extends AbstractEditAfterHandler {

    @Autowired
    private AuditLogServiceFacade auditLogServiceFacade;

    @Override
    protected Result doHandle(HandlerContext context, Arg arg) {
        recordEditLog(context, arg);
        return new Result();
    }

    private void recordEditLog(HandlerContext context, Arg arg) {
        if (Boolean.TRUE.equals(context.getAttribute(HandlerAttributes.SKIP_RECORD_LOG))) {
            return;
        }
        auditLogServiceFacade.recordEditLog(context.getUser(), arg, arg.masterAndDetailDescribes(), getLogExtendsInfo(context));
    }

    private Map<String, Object> getLogExtendsInfo(HandlerContext context) {
        Map<String, Object> map = Maps.newHashMap();
        map.put(LogInfo.TRIGGER_WORK_FLOW, context.getRequestContext().needTriggerWorkFlow());
        map.put(LogInfo.TRIGGER_APPROVAL_FLOW, !context.skipApprovalFlow());
        return map;
    }
}
