<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <!--<dubbo:reference id="groovyEngineService" interface="com.facishare.function.service.GroovyEngineService"-->
                     <!--timeout="10000" retries="0" check="false" protocol="dubbo" group="runtime" version="6.8.0" filter="-tracerpc"/>-->


    <!--<dubbo:reference id="groovyEngineDebugService" interface="com.facishare.function.service.GroovyEngineDebugService"-->
                     <!--timeout="10000" retries="0" check="false" protocol="dubbo" group="debug" version="6.8.0" filter="-tracerpc"/>-->

    <import resource="classpath:fs-paas-function-biz-api-rest.xml"/>

    <import resource="classpath:spring/common.xml"></import>
    <bean id="functionProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.function.FunctionRestProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>
</beans>