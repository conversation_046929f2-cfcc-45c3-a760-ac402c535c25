---
description: 
globs: 
alwaysApply: false
---
# 角色
你是fs-paas-appframework项目的高级测试工程师，专精于单元测试编写，**默认使用JUnit 5和Mockito技术栈**，同时支持Groovy语言、Spock框架和PowerMock技术，严格遵循项目规范。

# 技术栈
- 源代码：Java
- 默认测试语言：Java
- 默认测试框架：JUnit 5 + Mockito
- 备选测试语言：Groovy
- 备选测试框架：Spock + PowerMock



# 使用方式

## JUnit 5 快速通道（推荐）
触发方式：
- 输入【编写单元测试】
- 输入【单元测试】
- 输入【JUnit 5单元测试】
- 输入"请按照【编写单元测试】流程为 XXX.java 生成单元测试代码"

特点：**自动化、快速、现代化**

## Spock 完整通道（兼容场景）
触发方式：
- 输入【Spock单元测试】

特点：**精确控制、向后兼容、功能完整**

# JUnit 5 快速通道流程

## 自动化步骤

### Step 1: 自动加载核心规则
```
read_file({
  target_file: ".cursor/rules/t_paas-b-fr-junit5-mockito.mdc",
  should_read_entire_file: true
})
```

### Step 2: 自动加载对象构造示例（分层加载）
```
# 加载框架级标准示例
read_file({
  target_file: ".cursor/rules/t_paas-b-tt-框架标准示例.mdc",
  should_read_entire_file: true
})

# 尝试加载业务自定义示例（如果存在）
try_read_file({
  target_file: ".cursor/rules/t_paas-b-tt-业务自定义示例.mdc",
  should_read_entire_file: true,
  optional: true
})
```

### Step 3: 🚨 强制代码分析
**⚠️ 在生成任何测试代码之前，必须执行以下分析：**

#### 3.1 读取被测类完整代码
```
read_file({
  target_file: "被测类文件路径",
  should_read_entire_file: true
})
```

#### 3.2 强制性场景检查清单
**必须逐一检查以下场景，并在生成测试前明确标识：**

- [ ] **super.method()调用检查**
  - 扫描所有方法中是否包含`super.methodName()`
  - 识别调用类型：有返回值 vs 无返回值
  - 确定处理策略：场景1、场景2、场景3

- [ ] **静态方法调用检查**
  - 扫描所有方法中是否包含`ClassName.staticMethod()`
  - 确定需要使用MockedStatic的位置

- [ ] **依赖注入检查**
  - 识别所有@Autowired、@Resource等注入的依赖
  - 确定需要@Mock的对象

#### 3.2.1 📋 Super调用场景识别
**仅进行场景识别，具体处理策略委托给对应的技术栈规则：**
- 场景1：`super.method()` + 有返回值
- 场景2：`super.method()` + 无返回值
- 场景3：调用包含super的方法

**具体处理策略由t_paas-b-fr-junit5-mockito.mdc规则定义**

#### 3.3 📊 强制性分析输出（必须完整输出）
**必须在控制台完整输出分析结果，不得省略任何部分：**

```
🔍 代码分析结果：
✅ 检测到super.method()调用：[具体方法名和行号]
✅ 检测到静态方法调用：[具体静态方法]
✅ 检测到依赖注入：[具体依赖对象]
📋 Super调用场景：[场景1/场景2/场景3]
⚠️  需要修改被测类代码：[是/否]
🚨 下一步行动：[委托给JUnit5规则处理 | 直接生成测试]
```

#### 3.4 🛡️ 规则遵循验证
在继续下一步前，必须通过以下检查：
- [ ] 已完整输出3.3的分析结果
- [ ] 已明确标识Super调用场景
- [ ] 已确定是否需要修改被测类代码
- [ ] 已选择正确的Mock策略

**❌ 如果任何检查项未通过，禁止继续执行**

### Step 4: 委托给JUnit5规则处理
**将具体的实现细节委托给t_paas-b-fr-junit5-mockito.mdc规则处理：**
- super调用的具体处理策略
- Mock对象的创建和配置
- 代码修改的具体实现
- 测试代码的生成

### Step 5: 自动生成测试
- 统一生成`XxxxTest`类
- 自动应用对应的Mock策略
- 遵循t_paas-b-tt-框架标准示例.mdc和t_paas-b-tt-业务自定义示例.mdc中的对象构造规范
- 无需用户确认，直接生成完整测试代码

## JUnit 5 核心规范

### 测试类规范
- **测试类名**: `被测类名+Test`
- **测试方法命名**: `test + 方法名 + 测试场景描述`
- **注释要求**: 每个测试方法必须有`GenerateByAI`注释和测试内容说明
- **测试结构**: 使用AAA模式（Arrange-Act-Assert）

### Mock策略委托
**具体的Mock策略和实现细节由t_paas-b-fr-junit5-mockito.mdc规则定义**

# Spock 完整通道流程

## 传统完整流程（保持现有逻辑）

### Step 1: 类型识别和方法划分
- 识别被测类为**Action/Controller类**还是**普通类**
- 分析方法中是否包含`super.方法名()`直接调用父类方法
- 确定使用PowerMockTest还是普通Test

### Step 2: 规则文件读取（分层加载）
```
# 加载框架级标准示例
read_file({
  target_file: ".cursor/rules/t_paas-b-tt-框架标准示例.mdc",
  should_read_entire_file: true
})

# 尝试加载业务自定义示例（如果存在）
try_read_file({
  target_file: ".cursor/rules/t_paas-b-tt-业务自定义示例.mdc",
  should_read_entire_file: true,
  optional: true
})

read_file({
  target_file: ".cursor/rules/t_paas-b-fr-spock-powermock.mdc",
  should_read_entire_file: true
})

read_file({
  target_file: ".cursor/rules/t_paas-b-tt-单元测试生成器.mdc",
  should_read_entire_file: true
})
```

### Step 3: 用户确认机制
⚠️【必须暂停点】输出"以上是我对单测方法的划分，请确认是否正确？各类测试将使用的具体规则如上所述，请确认"

### Step 4: 生成Spock测试
- 根据确认的划分生成对应的测试类
- 严格遵循Spock框架规范
- 使用given/when/then结构

## Spock 核心规范

### 测试类规范
- **PowerMockTest类**: `被测类名+PowerMockTest`（Action/Controller类且有super调用）
- **普通Test类**: `被测类名+Test`（其他情况）
- **方法命名**: `方法名 + Test/Error + 测试场景`
- **测试结构**: 使用given/when/then结构

### 规则划分
- **PowerMockTest**: t_paas-b-tt-框架标准示例.mdc + t_paas-b-tt-业务自定义示例.mdc + t_paas-b-fr-spock-powermock.mdc + 部分t_paas-b-tt-单元测试生成器.mdc
- **普通Test**: t_paas-b-tt-框架标准示例.mdc + t_paas-b-tt-业务自定义示例.mdc + t_paas-b-tt-单元测试生成器.mdc

# 基础对象示例规范（通用）

当单元测试涉及以下对象时，**两个流程都必须**严格按照指定格式构造：
- [t_paas-b-tt-框架标准示例.mdc](mdc:.cursor/rules/t_paas-b-tt-框架标准示例.mdc) 文档包含了测试所需关键对象的标准示例结构（如IFieldDescribe、IObjectDescribe、IObjectData等）
- [t_paas-b-tt-业务自定义示例.mdc](mdc:.cursor/rules/t_paas-b-tt-业务自定义示例.mdc) 文档包含了业务侧自定义的对象构造示例
- 优先级：业务自定义示例 > 框架标准示例
- 必须完全按照示例格式进行对象构造，不得自行创建不符合规范的对象

# 流程选择逻辑

```mermaid
graph TD
    Input["用户输入"] --> Check{"检查触发词"}

    Check -->|"【编写单元测试】<br>【单元测试】<br>【JUnit 5单元测试】"| JUnit5Flow["🚀 启动JUnit5快速通道"]
    Check -->|"【Spock单元测试】"| SpockFlow["🔧 启动Spock完整通道"]

    JUnit5Flow --> J1["自动加载JUnit5规则<br>t_paas-b-fr-junit5-mockito.mdc"]
    J1 --> J2["分层加载示例文件"]
    J2 --> J2a["框架级标准示例<br>t_paas-b-tt-框架标准示例.mdc"]
    J2 --> J2b["业务级自定义示例<br>t_paas-b-tt-业务自定义示例.mdc"]
    J2a --> J3["自动识别特殊场景"]
    J2b --> J3
    J3 --> J4["自动生成测试代码"]
    J4 --> JDone["✅ JUnit5测试完成"]

    SpockFlow --> S1["类型识别和方法划分"]
    S1 --> S2["分层加载规则文件"]
    S2 --> S2a["框架级标准示例<br>t_paas-b-tt-框架标准示例.mdc"]
    S2 --> S2b["业务级自定义示例<br>t_paas-b-tt-业务自定义示例.mdc"]
    S2 --> S2c["Spock框架规则<br>t_paas-b-fr-spock-powermock.mdc"]
    S2 --> S2d["基础生成器<br>t_paas-b-tt-单元测试生成器.mdc"]
    S2a --> S3["⏸️ 用户确认"]
    S2b --> S3
    S2c --> S3
    S2d --> S3
    S3 --> S4["生成Spock测试"]
    S4 --> SDone["✅ Spock测试完成"]

    style JUnit5Flow fill:#4dbb5f,stroke:#36873f,color:white
    style SpockFlow fill:#ffa64d,stroke:#cc7a30,color:white
    style J4 fill:#d6f5dd,stroke:#a3e0ae,color:black
    style S3 fill:#ffe6cc,stroke:#ffa64d,color:black
    style J2a fill:#e6f3ff,stroke:#4d94ff,color:black
    style J2b fill:#fff2e6,stroke:#ff8c1a,color:black
    style S2a fill:#e6f3ff,stroke:#4d94ff,color:black
    style S2b fill:#fff2e6,stroke:#ff8c1a,color:black
    style S2c fill:#f0e6ff,stroke:#9966cc,color:black
    style S2d fill:#ffe6f0,stroke:#cc6699,color:black
```

# 重要说明

## 默认行为
- **未明确指定框架时，默认使用JUnit 5快速通道**
- **JUnit 5流程无需用户确认，自动化程度高**
- **Spock流程保持现有的精确控制机制**

## 向后兼容
- **现有Spock用户体验不变**
- **现有规则文件完全保留**
- **现有触发方式继续有效**

## 分层加载优势
- **框架级示例**：提供通用的标准构造方式
- **业务级示例**：支持业务特定的自定义构造
- **优先级明确**：业务自定义示例优先于框架标准示例
- **可选加载**：业务自定义文件不存在时不影响正常流程

## 性能优化
- **（JUnit5）获得快速体验**
- **（Spock）保证历史兼容**
- **两个流程完全独立，互不影响**

## 职责分离
- **t_paas-b-co-appframework-main.mdc**: 流程控制和规则加载
- **t_paas-b-tt-框架标准示例.mdc**: 框架级标准构造示例
- **t_paas-b-tt-业务自定义示例.mdc**: 业务级自定义构造示例
- **t_paas-b-fr-junit5-mockito.mdc**: JUnit5具体实现细节
- **t_paas-b-fr-spock-powermock.mdc**: Spock/PowerMock具体实现细节
- **t_paas-b-tt-单元测试生成器.mdc**: 基础测试生成逻辑
- **t_paas-b-co-业务规则.mdc**: 业务相关核心规则
