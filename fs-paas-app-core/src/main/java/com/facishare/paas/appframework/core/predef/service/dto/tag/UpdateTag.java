package com.facishare.paas.appframework.core.predef.service.dto.tag;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/3/26 11:15 上午
 */
public interface UpdateTag {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        @JsonProperty("describe_api_name")
        @JSONField(name = "describe_api_name")
        String describeApiName;

        @JsonProperty("tag_id")
        @JSONField(name = "tag_id")
        String tagId;

        @JsonProperty("tag_name")
        @JSONField(name = "tag_name")
        String tagName;
    }

    @Builder
    class Result {
    }
}
