package com.facishare.paas.appframework.core.predef.service.dto.log;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface GetManageLogList {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("QueryInfo")
        @SerializedName("QueryInfo")
        private CommonSearchInfo queryInfo;

        @JSONField(name = "M2")
        private int pageSize;

        @JSONField(name = "M3")
        private int pageNumber;
    }

    @Data
    class Result {
        @JSONField(name = "M1")
        private PageInfo pageInfo;

        @JSONField(name = "M2")
        private List<LogMsg> msgs;

        private String queryInterval;
    }

    @Data
    class CommonSearchInfo {
        @JSONField(name = "M1")
        @JsonProperty("FilterMainID")
        @SerializedName("FilterMainID")
        private String filterMainID;

        @JSONField(name = "M2")
        @JsonProperty("Conditions")
        @SerializedName("Conditions")
        private List<FilterConditionInfo> conditions;

        @JSONField(name = "M3")
        @JsonProperty("SortField")
        @SerializedName("SortField")
        private String sortField;

        @JSONField(name = "M4")
        @JsonProperty("SortType")
        @SerializedName("SortType")
        private int sortType;

    }

    @Data
    class FilterConditionInfo {

        @JSONField(name = "M1")
        @JsonProperty("FieldName")
        @SerializedName("FieldName")
        private String fieldName;

        @JSONField(name = "M2")
        @JsonProperty("Comparison")
        @SerializedName("Comparison")
        private int comparison;

        @JSONField(name = "M3")
        @JsonProperty("FilterValue")
        @SerializedName("FilterValue")
        private String filterValue;
    }

    @Data
    class LogMsg {

        @JSONField(name = "M1")
        @JsonProperty("OperTime")
        @SerializedName("OperTime")
        private long operTime;

        @JSONField(name = "M2")
        @JsonProperty("UserId")
        @SerializedName("UserId")
        private int userId;

        @JSONField(name = "M3")
        @JsonProperty("UserName")
        @SerializedName("UserName")
        private String userName;

        @JSONField(name = "M4")
        @JsonProperty("OperType")
        @SerializedName("OperType")
        private String operType;

        @JSONField(name = "M5")
        @JsonProperty("MsgList")
        @SerializedName("MsgList")
        private List<TextMsg> msgList;

        @JSONField(name = "M6")
        @JsonProperty("OperName")
        @SerializedName("OperName")
        private String operName;

        @JSONField(name = "M7")
        @JsonProperty("logId")
        @SerializedName("logId")
        private String logId;

        private String textMessage;
    }

    @Data
    class TextMsg {
        //不是链接
        public static int LINT_TYPE_NO_LINT = 0;
        //是链接,会在前端解析成跳转
        public static int LINT_TYPE_LINT = 1;

        Integer type = LINT_TYPE_NO_LINT;

        String text;

        String dataID = "";

        Integer objectType;

        String objectApiName;
    }
}
