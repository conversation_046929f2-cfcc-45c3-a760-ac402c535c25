package com.facishare.paas.appframework.metadata.options;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.OrderByExt;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.MtFieldDependence;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.facishare.paas.appframework.metadata.repository.model.MtFieldDependence.*;
import static com.facishare.paas.metadata.api.DBRecord.CREATE_TIME;
import static com.facishare.paas.metadata.api.Tenantable.TENANT_ID;

/**
 * Created by zhaooju on 2022/7/29
 */
@Slf4j
@Service
public class FieldDependenceLogicServiceImpl implements FieldDependenceLogicService {
    @Autowired
    private IRepository<MtFieldDependence> repository;

    @Override
    public List<MtFieldDependence> findAll(User user, String objectDescribeApiName) {
        SearchQuery searchQuery = SearchQueryImpl.filters(Lists.newArrayList(
                FilterExt.of(Operator.EQ, TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.EQ, DESCRIBE_API_NAME, objectDescribeApiName).getFilter()
        ));

        return queryData(user, searchQuery, 1000);
    }

    private List<MtFieldDependence> queryData(User user, SearchQuery searchQuery, int limit) {
        Query query = Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(CREATE_TIME, false)))
                .limit(limit)
                .offset(0)
                .build();
        return repository.findBy(user, query, MtFieldDependence.class);
    }

    @Override
    public Optional<MtFieldDependence> find(User user, String objectDescribeApiName, String fieldApiName, String childFieldName) {
        SearchQuery searchQuery = SearchQueryImpl.filters(Lists.newArrayList(
                FilterExt.of(Operator.EQ, TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.EQ, DESCRIBE_API_NAME, objectDescribeApiName).getFilter(),
                FilterExt.of(Operator.EQ, FIELD_API_NAME, fieldApiName).getFilter(),
                FilterExt.of(Operator.EQ, CHILD_FIELD_NAME, childFieldName).getFilter()
        ));

        return queryData(user, searchQuery, 1).stream().findFirst();
    }

    @Override
    public void create(User user, MtFieldDependence fieldDependence) {
        repository.create(user, fieldDependence);
    }

    @Override
    public void update(User user, MtFieldDependence fieldDependence) {
        MtFieldDependence mtFieldDependence = find(user, fieldDependence.getDescribeApiName(), fieldDependence.getFieldApiName(), fieldDependence.getChildFieldName())
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));

        mtFieldDependence.setDependence(fieldDependence.getDependence());
        repository.update(user, mtFieldDependence);
    }

    @Override
    public void deleted(User user, String objectDescribeApiName, String fieldApiName, String childFieldName) {
        MtFieldDependence fieldDependence = find(user, objectDescribeApiName, fieldApiName, childFieldName).orElse(null);
        if (Objects.isNull(fieldDependence)) {
            log.warn("fieldDependence not exit, user:{}, objectDescribeApiName:{}, fieldApiName:{}, childFieldName:{}", user, objectDescribeApiName, fieldApiName, childFieldName);
            return;
        }
        repository.bulkInvalidAndDelete(user, Lists.newArrayList(fieldDependence));
    }
}
