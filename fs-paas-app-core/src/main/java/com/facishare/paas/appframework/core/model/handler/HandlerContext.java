package com.facishare.paas.appframework.core.model.handler;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.util.Map;
import java.util.Objects;

/**
 * 业务处理器所属接口的上下文
 * Created by zhouwr on 2023/1/6.
 */
@ToString
public class HandlerContext {
    //上下文的属性集合
    @JsonProperty
    private final Map<String, Object> attributes = Maps.newHashMap();
    //本次请求的用户信息
    @Getter
    private final RequestContext requestContext;
    //接口Code，比如：Add、Edit、Detail、List等
    @Getter
    private final String interfaceCode;

    @Builder
    private HandlerContext(RequestContext requestContext, String interfaceCode) {
        this.requestContext = requestContext;
        this.interfaceCode = interfaceCode;
    }

    @JsonCreator
    private HandlerContext(@JsonProperty("requestContext") RequestContext requestContext,
                           @JsonProperty("interfaceCode") String interfaceCode,
                           @JsonProperty("attributes") Map<String, Object> attributes) {
        this.requestContext = requestContext;
        this.interfaceCode = interfaceCode;
        if (Objects.nonNull(attributes)) {
            this.attributes.putAll(attributes);
        }
    }

    public <V> V getAttribute(String key) {
        return (V) attributes.get(key);
    }

    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }

    public <V> V getRequestAttribute(String key) {
        return requestContext.getAttribute(key);
    }

    @JsonIgnore
    public String getTenantId() {
        return requestContext.getTenantId();
    }

    @JsonIgnore
    public User getUser() {
        return requestContext.getUser();
    }

    public boolean skipBaseValidate() {
        return requestContext.skipBaseValidate();
    }

    public boolean skipApprovalFlow() {
        if (!requestContext.needTriggerApprovalFlow()) {
            return true;
        }
        return Boolean.TRUE.equals(getAttribute(HandlerAttributes.SKIP_APPROVAL_FLOW));
    }

    public boolean skipWorkFlow() {
        return !requestContext.needTriggerWorkFlow();
    }
}
