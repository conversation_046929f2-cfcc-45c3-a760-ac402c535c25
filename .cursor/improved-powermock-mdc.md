---
description: Action/Controller类PowerMock单元测试规范-增强版
globs: 
alwaysApply: false
---

# PowerMock单元测试规范指南

## 重要说明 - 必须遵循
本文档定义了需要使用PowerMock的单元测试实现规范。仅当方法中包含父类方法调用且父类在外部jar包中时才使用PowerMock。每个PowerMock测试类必须：
1. 命名格式为`被测类名PowerMockTest`
2. 包含所有必需的PowerMock注解
3. 针对每个需要测试的方法，正确模拟父类方法调用

## 适用条件 - 何时使用PowerMock
以下情况必须使用PowerMock：
1. 被测试类名后缀为Action或Controller
2. 被测试方法中包含`super.方法名()`形式的直接父类方法调用
3. 父类位于外部jar包中（不在当前项目代码中）

## PowerMock测试类模板 - 参考使用
```groovy
package com.facishare.paas.appframework.example

// 基础导入
import spock.lang.Specification
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik

/**
 * TestedClass单元测试 - PowerMock版本
 * 用于测试TestedClass类中需要模拟父类方法调用的方法
 */
@PowerMockRunnerDelegate(Sputnik)
@RunWith(PowerMockRunner)
@PrepareForTest([TestedClass.class, ParentClass.class])
@PowerMockIgnore(value = ["org.slf4j.*", "com.sun.org.apache.xerces.*", "javax.xml.parsers.*", "javax.management.*", "sun.security.*"])
class TestedClassPowerMockTest extends Specification {

    /**
     * GenerateByAI
     * 测试内容描述：测试方法功能点
     */
    def "testMethodTest"() {
        given:
        // 创建被测试类的Spy对象
        TestedClass classUnderTestSpy = PowerMockito.spy(new TestedClass())
        
        // 模拟数据准备
        // ...
        
        // 模拟父类方法调用（选择以下方式之一）
        
        // 方式1：有返回值的super调用 - 模拟辅助方法
        PowerMockito.doReturn("预期返回值").when(classUnderTestSpy, "helperMethod", 任何参数)
        
        // 方式2：无返回值的super调用 - 直接抑制父类方法
        PowerMockito.suppress(PowerMockito.method(ParentClass.class, "parentMethod"))
        
        when:
        // 调用被测试方法
        def result = classUnderTestSpy.testMethod(参数)
        
        then:
        // 验证结果
        result == 预期结果
        noExceptionThrown()
    }
}
```

## 模拟父类方法调用的正确方式

### 情况1：有返回值的父类方法调用
当被测试方法中存在类似`super.methodName()`的调用且有返回值时，使用以下方式之一：

#### 方式1：优先使用 - 模拟辅助方法（Helper Method）
```groovy
// 在被测试类中定义辅助方法
protected String callParentMethod() {
    return super.methodName();
}

// 在测试中模拟辅助方法
PowerMockito.doReturn("预期返回值").when(spyObject, "callParentMethod");
```

#### 方式2：直接模拟方法调用
```groovy
PowerMockito.doReturn("预期返回值").when(spyObject, "methodName", 参数...);
```

### 情况2：无返回值的父类方法调用
当父类方法没有返回值时，使用以下方式：

```groovy
// 抑制父类方法执行
PowerMockito.suppress(PowerMockito.method(ParentClass.class, "methodName"));
```

## 完整示例 - ValidateFuncAction测试

```groovy
package com.facishare.paas.appframework.button.action

import com.facishare.paas.appframework.button.executor.ActionExecutorContext
import com.facishare.paas.appframework.button.dto.ButtonExecutor
import com.facishare.paas.appframework.function.dto.BatchDataExecuteFunction
import com.facishare.paas.appframework.function.dto.RunResult
import com.facishare.paas.appframework.function.FunctionLogicService
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.IUdefFunction
import com.facishare.paas.appframework.core.model.User
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification

/**
 * ValidateFuncAction单元测试 - PowerMock版本
 * 用于测试ValidateFuncAction类的executeFunction方法
 */
@PowerMockRunnerDelegate(Sputnik)
@RunWith(PowerMockRunner)
@PrepareForTest([ValidateFuncAction.class, ParentActionClass.class])
@PowerMockIgnore(value = ["org.slf4j.*", "com.sun.org.apache.xerces.*", "javax.xml.parsers.*", "javax.management.*", "sun.security.*"])
class ValidateFuncActionPowerMockTest extends Specification {

    /**
     * GenerateByAI
     * 测试内容描述：测试当isTriggerBatchFunc为false时，executeFunction方法的行为
     */
    def "executeFunctionTest_WhenIsTriggerBatchFuncIsFalse"() {
        given:
        // 创建测试对象的spy
        ValidateFuncAction validateFuncActionSpy = PowerMockito.spy(new ValidateFuncAction())
        
        // 构造测试数据
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        ActionExecutorContext context = Mock(ActionExecutorContext)
        IUdefFunction function = Mock(IUdefFunction)
        Map<String, Object> functionArgMap = [:]
        Map<String, List<IObjectData>> details = [:]
        
        // mock参数行为
        arg.isTriggerBatchFunc() >> false
        
        // 模拟父类方法返回的结果
        RunResult expectedResult = RunResult.builder()
                .success(true)
                .functionResult("父类方法返回结果")
                .build()
        
        // 模拟super.executeFunction调用
        PowerMockito.doReturn(expectedResult).when(validateFuncActionSpy, "executeFunction", 
                org.mockito.ArgumentMatchers.any(ButtonExecutor.Arg.class), 
                org.mockito.ArgumentMatchers.any(ActionExecutorContext.class), 
                org.mockito.ArgumentMatchers.any(IUdefFunction.class), 
                org.mockito.ArgumentMatchers.anyMap(), 
                org.mockito.ArgumentMatchers.anyMap())
        
        when:
        RunResult result = validateFuncActionSpy.executeFunction(arg, context, function, functionArgMap, details)
        
        then:
        result == expectedResult
        noExceptionThrown()
    }
}
```

## 决策流程图 - 何时使用PowerMock

1. 检查被测试类是否以Action或Controller结尾？
   - 是 -> 继续到步骤2
   - 否 -> 使用普通Spock测试（不使用PowerMock）

2. 检查被测试方法是否包含super.method()调用？
   - 是 -> 继续到步骤3
   - 否 -> 使用普通Spock测试（不使用PowerMock）

3. 检查父类是否在外部jar包中？
   - 是 -> 使用PowerMock测试，创建ClassName + PowerMockTest类
   - 否 -> 使用普通Spock测试（不使用PowerMock）

## 常见错误和解决方案

### 错误1：PowerMock注解不完整
必须添加以下所有注解：
```groovy
@PowerMockRunnerDelegate(Sputnik)
@RunWith(PowerMockRunner)
@PrepareForTest([TestedClass.class, ParentClass.class])
@PowerMockIgnore(value = ["org.slf4j.*", "com.sun.org.apache.xerces.*", "javax.xml.parsers.*", "javax.management.*", "sun.security.*"])
```

### 错误2：模拟方法调用参数不匹配
确保参数类型完全匹配：
```groovy
// 正确做法
PowerMockito.doReturn(result).when(spy, "method", 
    org.mockito.ArgumentMatchers.any(Type.class))

// 错误做法 - 参数类型不匹配
PowerMockito.doReturn(result).when(spy, "method", any())
```

### 错误3：未正确模拟父类方法
需要使用PowerMockito.doReturn或PowerMockito.suppress：
```groovy
// 正确做法
PowerMockito.doReturn("结果").when(spy, "parentMethod");

// 错误做法 - 不能直接使用Spock的when-then模式
spy.parentMethod() >> "结果"  // 这种方式不适用于PowerMock
``` 