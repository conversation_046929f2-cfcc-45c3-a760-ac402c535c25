package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GenerateByAI
 * 测试BatchFieldDataConverterAdapter类的批量转换适配器功能
 */
class BatchFieldDataConverterAdapterTest {

  private BatchFieldDataConverterAdapter adapter;

  @BeforeEach
  void setUp() {
    adapter = new BatchFieldDataConverterAdapter();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getSupportedFieldTypes方法返回空列表
   */
  @Test
  @DisplayName("测试getSupportedFieldTypes - 返回空列表")
  void testGetSupportedFieldTypes() {
    // 执行被测试方法
    List<String> supportedTypes = adapter.getSupportedFieldTypes();

    // 验证结果
    assertNotNull(supportedTypes);
    assertEquals(0, supportedTypes.size());
    assertTrue(supportedTypes.isEmpty());
  }
}
