package com.facishare.crm.privilege.rest.proxy.privilege.api.model;

import com.facishare.crm.privilege.rest.proxy.privilege.api.pojo.EntityOpennessPojo;
import com.facishare.crm.privilege.rest.proxy.privilege.model.BaseArgDataPrivilege;
import com.facishare.crm.privilege.rest.proxy.privilege.model.BaseResultDataPrivilege;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by yusb on 2017/4/24.
 */
public class CreateBaseDataPrivilegeRulesModel {

  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class Arg extends BaseArgDataPrivilege {
    private List<EntityOpennessPojo> entityOpenness;
  }

  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class Result extends BaseResultDataPrivilege {
  }
}
