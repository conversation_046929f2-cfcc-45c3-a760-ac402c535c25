package com.facishare.paas.appframework.metadata.layout.rule;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.AbstractDocumentBasedDBRecord;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.timezone.DateUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * create by z<PERSON><PERSON> on 2021/07/19
 */
public class LayoutRuleFilter extends AbstractDocumentBasedDBRecord implements ILayoutRuleFilter {
    private static final long serialVersionUID = 2183472398395847245L;

    public LayoutRuleFilter() {
    }

    public LayoutRuleFilter(Map map) {
        super(map);
    }

    @Override
    public String getFieldName() {
        return get(FIELD_NAME, String.class);
    }

    @Override
    public String getOperator() {
        return get(OPERATOR, String.class);
    }

    @Override
    public List<?> getFieldValues() {
        return get(FIELD_VALUES, List.class);
    }

    public void setFieldValues(List fieldValues) {
        set(FIELD_VALUES, fieldValues);
    }

    @Override
    public Integer getValueType() {
        return get(VALUE_TYPE, Integer.class);
    }

    @Override
    public void convertDateFieldFilter(ObjectDescribeExt describeExt, boolean toSystemZone) {
        if (Objects.nonNull(getValueType()) && 0 != getValueType()) {
            return;
        }
        Optional<IFieldDescribe> optional = describeExt.getFieldDescribeSilently(getFieldName())
                .filter(it -> FieldDescribeExt.of(it).realTypeIsDate());
        if (!optional.isPresent()) {
            return;
        }

        List<?> fieldValues = getFieldValues();
        if (CollectionUtils.empty(fieldValues)) {
            return;
        }
        Operator operator = Operator.valueOf(getOperator());
        if (Operator.IS.equals(operator) || Operator.ISN.equals(operator)) {
            return;
        }
        List values = Lists.newArrayList();
        if (Operator.BETWEEN == operator || Operator.NBETWEEN == operator) {
            long leftValue = new BigDecimal(fieldValues.get(0).toString()).longValue();
            long rightValue = new BigDecimal(fieldValues.get(1).toString()).longValue();
            Pair<Long, Long> pair;
            if (toSystemZone) {
                pair = DateUtils.getStartTimeAndEndTime2SystemZone(leftValue, rightValue);
            } else {
                pair = DateUtils.getStartTimeAndEndTime2CustomZone(leftValue, rightValue);
            }
            values.add(pair.getLeft());
            values.add(pair.getRight());
        } else {
            long value = new BigDecimal(fieldValues.get(0).toString()).longValue();
            long time = toSystemZone ? DateUtils.convertDateValueToSystem(value) :
                    DateUtils.convertDateValueToCustom(value);
            values.add(time);
        }

        setFieldValues(values);
    }


}
