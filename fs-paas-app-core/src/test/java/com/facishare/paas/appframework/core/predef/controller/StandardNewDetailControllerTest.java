package com.facishare.paas.appframework.core.predef.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ControllerListener;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController.Arg;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController.Result;
import com.facishare.paas.appframework.core.predef.listener.prm.PrmDetailControllerListener;
import com.facishare.paas.appframework.core.predef.listener.prm.PrmNewDetailControllerListener;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.appframework.metadata.RelatedObjectDescribeStructure;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IGroupComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.google.common.collect.Lists;

@ExtendWith(MockitoExtension.class)
class StandardNewDetailControllerTest {

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private InfraServiceFacade infraServiceFacade;

    @Mock
    private ControllerContext controllerContext;

    @Mock
    private LayoutLogicService layoutLogicService;

    @Mock
    private ILayout layout;

    @Mock
    private IComponent topComponent;

    @Mock
    private IGroupComponent teamComponent;

    @Mock
    private IButton button;

    private StandardNewDetailController controller;

    private Arg arg;
    private User user;

    @BeforeEach
    void setUp() {
        controller = new StandardNewDetailController();
        
        arg = new Arg();
        arg.setObjectDescribeApiName("TestObject__c");
        arg.setFromRecycleBin(false);
        
        user = new User("74255", "1000");
        
        // 手动设置所有必要的字段
        ReflectionTestUtils.setField(controller, "arg", arg);
        ReflectionTestUtils.setField(controller, "controllerContext", controllerContext);
        ReflectionTestUtils.setField(controller, "serviceFacade", serviceFacade);
        ReflectionTestUtils.setField(controller, "infraServiceFacade", infraServiceFacade);
    }
    
    /**
     * 设置getLayout测试所需的数据
     */
    private void setupLayoutTestData() {
        // 创建必要的对象
        IObjectDescribe describe = new ObjectDescribe();
        describe.setApiName("TestObject__c");
        
        IObjectData data = new ObjectData();
        data.setRecordType("default");
        
        StopWatch stopWatch = StopWatch.create("test");
        
        // 设置到controller中
        ReflectionTestUtils.setField(controller, "describe", describe);
        ReflectionTestUtils.setField(controller, "data", data);
        ReflectionTestUtils.setField(controller, "relatedObjects", new ArrayList<RelatedObjectDescribeStructure>());
        ReflectionTestUtils.setField(controller, "detailObjects", new ArrayList<RelatedObjectDescribeStructure>());
        ReflectionTestUtils.setField(controller, "stopWatch", stopWatch);
        ReflectionTestUtils.setField(controller, "defaultEnableQixinGroup", false);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLayout方法正常场景，验证完整的布局获取流程
     */
    @Test
    @DisplayName("正常场景 - getLayout方法获取布局并处理企信群组按钮")
    void testGetLayout_NormalScenario() {
        // 准备测试数据
        arg.setFromRecycleBin(false);
        
        // 设置必要的字段
        setupLayoutTestData();
        
        // Mock LayoutLogicService
        when(serviceFacade.getLayoutLogicService()).thenReturn(layoutLogicService);
        when(layoutLogicService.getLayoutWithComponents(
                any(LayoutLogicService.LayoutContext.class), 
                any(String.class), 
                any(IObjectDescribe.class), 
                any(IObjectData.class), 
                any(List.class), 
                any(List.class), 
                any(java.util.Collection.class), 
                any(PageType.class)))
                .thenReturn(layout);
        
        // Mock User和AppId
        when(controllerContext.getUser()).thenReturn(user);
        when(controllerContext.getAppId()).thenReturn("testApp");
        
        // 执行被测试方法
        ILayout result = controller.getLayout();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(layout, result);
        
        // 验证Mock交互
        verify(serviceFacade).getLayoutLogicService();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLayout方法回收站场景，验证回收站逻辑
     */
    @Test
    @DisplayName("回收站场景 - getLayout方法直接返回布局不处理按钮")
    void testGetLayout_RecycleBinScenario() {
        // 准备测试数据
        arg.setFromRecycleBin(true);
        
        // 设置必要的字段
        setupLayoutTestData();
        
        // Mock LayoutLogicService
        when(serviceFacade.getLayoutLogicService()).thenReturn(layoutLogicService);
        when(layoutLogicService.getLayoutWithComponents(
                any(LayoutLogicService.LayoutContext.class), 
                any(String.class), 
                any(IObjectDescribe.class), 
                any(IObjectData.class), 
                any(List.class), 
                any(List.class), 
                any(java.util.Collection.class), 
                any(PageType.class)))
                .thenReturn(layout);
        
        // Mock User和AppId
        when(controllerContext.getUser()).thenReturn(user);
        when(controllerContext.getAppId()).thenReturn("testApp");
        
        // 执行被测试方法
        ILayout result = controller.getLayout();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(layout, result);
        assertTrue(arg.isFromRecycleBin());
        
        // 验证Mock交互
        verify(serviceFacade).getLayoutLogicService();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getControllerListenerClassList方法PRM应用场景
     */
    @Test
    @DisplayName("PRM应用场景 - getControllerListenerClassList方法替换监听器")
    void testGetControllerListenerClassList_PRMApp() {
        // 准备测试数据
        List<Class<? extends ControllerListener<Arg, Result>>> superList = new ArrayList<>();
        superList.add(PrmDetailControllerListener.class);
        
        StandardNewDetailController spyController = spy(controller);
        doReturn(superList).when(spyController).getSuperControllerListenerClassList();
        
        when(controllerContext.getAppId()).thenReturn("prm");
        
        try (MockedStatic<AppIdMapping> appIdMappingMock = mockStatic(AppIdMapping.class)) {
            appIdMappingMock.when(() -> AppIdMapping.isPRM("prm")).thenReturn(true);
            
            // 执行被测试方法
            List<Class<? extends ControllerListener<Arg, Result>>> result = spyController.getControllerListenerClassList();
            
            // 验证结果
            assertNotNull(result);
            assertFalse(result.contains(PrmDetailControllerListener.class));
            assertTrue(result.contains(PrmNewDetailControllerListener.class));
            
            // 验证Mock交互
            verify(spyController).getSuperControllerListenerClassList();
            verify(controllerContext).getAppId();
            appIdMappingMock.verify(() -> AppIdMapping.isPRM("prm"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getControllerListenerClassList方法非PRM应用场景
     */
    @Test
    @DisplayName("非PRM应用场景 - getControllerListenerClassList方法不修改监听器")
    void testGetControllerListenerClassList_NonPRMApp() {
        // 准备测试数据
        List<Class<? extends ControllerListener<Arg, Result>>> superList = new ArrayList<>();
        superList.add(PrmDetailControllerListener.class);
        
        StandardNewDetailController spyController = spy(controller);
        doReturn(superList).when(spyController).getSuperControllerListenerClassList();
        
        when(controllerContext.getAppId()).thenReturn("crm");
        
        try (MockedStatic<AppIdMapping> appIdMappingMock = mockStatic(AppIdMapping.class)) {
            appIdMappingMock.when(() -> AppIdMapping.isPRM("crm")).thenReturn(false);
            
            // 执行被测试方法
            List<Class<? extends ControllerListener<Arg, Result>>> result = spyController.getControllerListenerClassList();
            
            // 验证结果
            assertNotNull(result);
            assertTrue(result.contains(PrmDetailControllerListener.class));
            assertFalse(result.contains(PrmNewDetailControllerListener.class));
            
            // 验证Mock交互
            verify(spyController).getSuperControllerListenerClassList();
            verify(controllerContext).getAppId();
            appIdMappingMock.verify(() -> AppIdMapping.isPRM("crm"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findRelatedObjects方法正常场景
     */
    @Test
    @DisplayName("正常场景 - findRelatedObjects方法返回相关对象列表")
    void testFindRelatedObjects_NormalScenario() {
        // 准备测试数据
        java.util.Map<String, Object> fieldMap1 = new java.util.HashMap<>();
        fieldMap1.put("api_name", "field1__c");
        fieldMap1.put("type", "text");
        IFieldDescribe fieldDescribe1 = FieldDescribeFactory.newInstance(fieldMap1);
        fieldDescribe1.setDescribeApiName("RelatedObject1__c");
        
        java.util.Map<String, Object> fieldMap2 = new java.util.HashMap<>();
        fieldMap2.put("api_name", "field2__c");
        fieldMap2.put("type", "text");
        IFieldDescribe fieldDescribe2 = FieldDescribeFactory.newInstance(fieldMap2);
        fieldDescribe2.setDescribeApiName("RelatedObject2__c");
        
        List<IFieldDescribe> relatedFields = Lists.newArrayList(fieldDescribe1, fieldDescribe2);
        
        when(controllerContext.getTenantId()).thenReturn("74255");
        when(serviceFacade.findRelatedFields("74255", "TestObject__c")).thenReturn(relatedFields);
        
        // 执行被测试方法
        List<IObjectDescribe> result = controller.findRelatedObjects();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        
        IObjectDescribe describe1 = result.get(0);
        assertEquals("RelatedObject1__c", describe1.getApiName());
        assertEquals(1, describe1.getFieldDescribes().size());
        assertEquals(fieldDescribe1, describe1.getFieldDescribes().get(0));
        
        IObjectDescribe describe2 = result.get(1);
        assertEquals("RelatedObject2__c", describe2.getApiName());
        assertEquals(1, describe2.getFieldDescribes().size());
        assertEquals(fieldDescribe2, describe2.getFieldDescribes().get(0));
        
        // 验证Mock交互
        verify(controllerContext).getTenantId();
        verify(serviceFacade).findRelatedFields("74255", "TestObject__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findRelatedObjects方法空列表场景
     */
    @Test
    @DisplayName("空列表场景 - findRelatedObjects方法返回空列表")
    void testFindRelatedObjects_EmptyList() {
        // 准备测试数据
        List<IFieldDescribe> emptyRelatedFields = new ArrayList<>();
        
        when(controllerContext.getTenantId()).thenReturn("74255");
        when(serviceFacade.findRelatedFields("74255", "TestObject__c")).thenReturn(emptyRelatedFields);
        
        // 执行被测试方法
        List<IObjectDescribe> result = controller.findRelatedObjects();
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证Mock交互
        verify(controllerContext).getTenantId();
        verify(serviceFacade).findRelatedFields("74255", "TestObject__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findRelatedObjects方法异常场景
     */
    @Test
    @DisplayName("异常场景 - findRelatedObjects方法处理服务异常")
    void testFindRelatedObjectsThrowsRuntimeException_ServiceException() {
        // 准备测试数据
        when(controllerContext.getTenantId()).thenReturn("74255");
        when(serviceFacade.findRelatedFields("74255", "TestObject__c"))
                .thenThrow(new RuntimeException("Service error"));
        
        // 执行并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            controller.findRelatedObjects();
        });
        
        // 验证异常信息
        assertTrue(exception.getMessage().contains("Service error"));
        
        // 验证Mock交互
        verify(controllerContext).getTenantId();
        verify(serviceFacade).findRelatedFields("74255", "TestObject__c");
    }
} 