package com.facishare.paas.appframework.core.predef.service.dto.dataMultiLang;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface GetEnableMultiLanguageField {

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    class Arg {
        private String describeApiName;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    class Result {
        List<MultiLanguageInfo> multiLanguageInfo;
        Integer limitFieldCount;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    class MultiLanguageInfo {
        private String describeApiName;
        private String displayName;
        private String type;
        private List<FieldInfo> fieldInfoList;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    class FieldInfo {
        private String fieldApiName;
        private String label;
    }

}
