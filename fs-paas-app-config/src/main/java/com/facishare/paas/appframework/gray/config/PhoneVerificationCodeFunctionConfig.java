package com.facishare.paas.appframework.gray.config;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by zhaooju on 2023/1/4
 */
public final class PhoneVerificationCodeFunctionConfig {
    private Map<String, PhoneVerificationCodeFunctionItem> map = Maps.newHashMap();

    private PhoneVerificationCodeFunctionConfig() {
        init();
    }

    public static PhoneVerificationCodeFunctionConfig getInstance() {
        return PhoneVerificationCodeFunctionConfigHelper.PHONE_VERIFICATION_CODE_FUNCTION_CONFIG;
    }

    private static String generateKey(String... keys) {
        return Stream.of(keys)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.joining("_"));
    }

    public String getFunctionApiName(String tenantId, String describeApiName, String fieldName) {
        String key = generateKey(tenantId, describeApiName, fieldName);
        PhoneVerificationCodeFunctionItem functionItem = map.get(key);
        if (Objects.isNull(functionItem)) {
            return null;
        }
        return functionItem.functionApiName;
    }

    public int getCodeExpireTime(String tenantId, String describeApiName, String fieldName) {
        String key = generateKey(tenantId, describeApiName, fieldName);
        return Optional.ofNullable(map.get(key))
                .map(it -> it.codeExpireTime)
                // 默认过期时间15分钟
                .orElse(15 * 60);
    }

    private void init() {
        ConfigFactory.getConfig("fs-paas-appframework-config", config -> {
            String functionPluginConf = config.get("phone_verification_code_function_conf");
            List<PhoneVerificationCodeFunctionInfo> functionPluginInfos = JacksonUtils.fromJson(functionPluginConf, new TypeReference<List<PhoneVerificationCodeFunctionInfo>>() {
            });
            loadPhoneVerificationCodeFunctionConfig(functionPluginInfos);
        });
    }

    private void loadPhoneVerificationCodeFunctionConfig(List<PhoneVerificationCodeFunctionInfo> functionPluginInfos) {
        Map<String, PhoneVerificationCodeFunctionItem> newMap = Maps.newHashMap();
        if (CollectionUtils.empty(functionPluginInfos)) {
            this.map = newMap;
            return;
        }
        for (PhoneVerificationCodeFunctionInfo functionPluginInfo : functionPluginInfos) {
            List<PhoneVerificationCodeFunctionItem> functionDefinitions = functionPluginInfo.functionDefinitions;
            if (CollectionUtils.empty(functionDefinitions)) {
                continue;
            }
            for (PhoneVerificationCodeFunctionItem functionDefinition : functionDefinitions) {
                String key = generateKey(functionPluginInfo.ei, functionPluginInfo.describeApiName, functionDefinition.fieldName);
                newMap.putIfAbsent(key, functionDefinition);
            }
        }
        this.map = newMap;
    }

    private static class PhoneVerificationCodeFunctionConfigHelper {
        private static final PhoneVerificationCodeFunctionConfig PHONE_VERIFICATION_CODE_FUNCTION_CONFIG = new PhoneVerificationCodeFunctionConfig();
    }

    @Data
    @ToString
    @EqualsAndHashCode
    private static class PhoneVerificationCodeFunctionInfo {
        private String describeApiName;
        private String ei;
        private List<PhoneVerificationCodeFunctionItem> functionDefinitions;
    }

    @Data
    @ToString
    @EqualsAndHashCode
    private static class PhoneVerificationCodeFunctionItem {
        private String fieldName;
        private String functionApiName;
        private Integer codeExpireTime;
    }
}
