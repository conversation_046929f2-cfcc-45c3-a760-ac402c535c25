package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.common.service.OrgService
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.service.dto.export.ExportDTO
import com.facishare.paas.appframework.core.predef.service.dto.objectImport.GetBatchPrintExportState
import com.facishare.paas.appframework.log.LogService
import com.facishare.paas.appframework.log.dto.AuditLog
import com.facishare.paas.appframework.log.dto.LoginLog
import com.facishare.paas.appframework.metadata.DescribeLogicService
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import spock.lang.Specification

/**
 * GenerateByAI
 * 测试内容描述：ObjectExportService对象导出服务的单元测试
 */
class ObjectExportServiceTest extends Specification {

    LogService logService
    ObjectExportService objectExportService
    DescribeLogicService describeLogicService
    OrgService orgService

    def tenant_id = '78057'
    ServiceContext context

    def setup() {
        RequestContext requestContext = RequestContext.builder().tenantId(tenant_id).user(User.systemUser(tenant_id)).build()
        RequestContextManager.setContext(requestContext)
        context = ContextManager.buildServiceContext("exportVerify", "")

        logService = Mock()
        describeLogicService = Mock()
        orgService = Mock()

        objectExportService = new ObjectExportService(
                logService: logService,
                describeLogicService: describeLogicService,
                orgService: orgService
        )
    }

    def "test exportApiVerify"() {
        given:
        def arg = ExportDTO.Arg.newInstance()
        arg.setSearchQuery("{\"operationTimeFrom\":1726122066611,\"operationTimeTo\":1726125666611,\"module\":\"UserDefineFunc\"}")
        arg.setExportBizType("auditLog")
        arg.setPageSize(0)
        when:
        def result = objectExportService.exportVerify(arg, context)
        then:
        println("exportApiVerify" + result)
    }

    def "test isSupportBatchPrintExport"() {
        given: "准备参数"
        def arg = new GetBatchPrintExportState.Arg()
        arg.objectApiName = "TestObject"

        when: "检查是否支持批量打印导出"
        def result = objectExportService.isSupportBatchPrintExport(arg, context)

        then: "应该返回结果"
        result != null
        result.supportPrintExport != null
    }

    def "test exportApiVerify with null arguments"() {
        given: "准备null参数"
        def arg = null

        when: "执行API导出验证"
        objectExportService.exportApiVerify(arg, context)

        then: "应该抛出ValidateException"
        thrown(com.facishare.paas.appframework.core.exception.ValidateException)
    }

    def "test exportApiVerify with valid arguments"() {
        given: "准备有效参数"
        def arg = new ExportDTO.Arg()
        arg.searchQuery = '{"describeApiNames":["TestObject"]}'

        and: "Mock服务调用"
        def mockDescribe = Mock(IObjectDescribe)
        def mockDescribeMap = ["TestObject": mockDescribe]
        mockDescribe.fieldDescribes >> []
        describeLogicService.findObjects(tenant_id, _) >> mockDescribeMap

        when: "执行API导出验证"
        def result = objectExportService.exportApiVerify(arg, context)

        then: "应该返回正确结果"
        result != null
        result.totalCount >= 0
    }

    def "test findApiExportHeader with null arguments"() {
        given: "准备null参数"
        def arg = null

        when: "查找API导出头"
        objectExportService.findApiExportHeader(arg, context)

        then: "应该抛出ValidateException"
        thrown(com.facishare.paas.appframework.core.exception.ValidateException)
    }

    def "test findApiExportHeader with valid arguments"() {
        given: "准备有效参数"
        def arg = new ExportDTO.Arg()
        arg.searchQuery = '{"describeApiNames":["TestObject"]}'

        and: "Mock服务调用"
        def mockDescribe = Mock(IObjectDescribe)
        mockDescribe.apiName >> "TestObject"
        mockDescribe.displayName >> "Test Object"
        describeLogicService.findDescribeListWithoutFields(tenant_id, _) >> [mockDescribe]

        when: "查找API导出头"
        def result = objectExportService.findApiExportHeader(arg, context)

        then: "应该返回正确结果"
        result != null
        result.groupHeaders != null
        result.totalCount == 1
    }

    def "test exportVerify with loginLog type"() {
        given: "准备登录日志验证参数"
        def arg = new ExportDTO.Arg()
        arg.exportBizType = "loginLog"
        arg.searchQuery = '{"startTime":"2023-01-01","endTime":"2023-12-31"}'

        and: "Mock服务调用"
        def mockLoginResult = Mock(LoginLog.Result)
        mockLoginResult.totalCount >> 100
        logService.getLoginLog(context.user, _) >> mockLoginResult

        when: "执行导出验证"
        def result = objectExportService.exportVerify(arg, context)

        then: "应该返回正确结果"
        result != null
        result.totalCount == 100
    }

    def "test exportVerify with auditLog type"() {
        given: "准备审计日志验证参数"
        def arg = new ExportDTO.Arg()
        arg.exportBizType = "auditLog"
        arg.searchQuery = '{"startTime":"2023-01-01","endTime":"2023-12-31"}'

        and: "Mock服务调用"
        def mockAuditResult = Mock(AuditLog.Result)
        mockAuditResult.totalCount >> 200
        logService.getAuditLogCount(context.user, _) >> mockAuditResult

        when: "执行导出验证"
        def result = objectExportService.exportVerify(arg, context)

        then: "应该返回正确结果"
        result != null
        result.totalCount == 200
    }

    def "test findExportHeader with loginLog type"() {
        given: "准备登录日志头部参数"
        def arg = new ExportDTO.Arg()
        arg.exportBizType = "loginLog"
        arg.searchQuery = '{"startTime":"2023-01-01","endTime":"2023-12-31"}'

        and: "Mock服务调用"
        def mockLoginResult = Mock(LoginLog.Result)
        mockLoginResult.totalCount >> 50
        logService.getLoginLog(context.user, _) >> mockLoginResult

        when: "查找导出头部"
        def result = objectExportService.findExportHeader(arg, context)

        then: "应该返回正确结果"
        result != null
        result.exportHeaders != null
        result.totalCount == 50
    }

    def "test findExportHeader with invalid type"() {
        given: "准备无效类型参数"
        def arg = new ExportDTO.Arg()
        arg.exportBizType = "invalidType"
        arg.searchQuery = '{"startTime":"2023-01-01","endTime":"2023-12-31"}'

        when: "查找导出头部"
        def result = objectExportService.findExportHeader(arg, context)

        then: "应该返回空结果"
        result != null
        result.exportHeaders == null
        result.totalCount == 0
    }

}
