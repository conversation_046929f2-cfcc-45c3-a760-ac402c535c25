package com.facishare.paas.appframework.core.predef.service.dto.bizconf;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.fxiaoke.bizconf.bean.ConfigPojo;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2020/7/28
 */
public interface SaveConfigData {

    @Data
    class Arg {
        String describeApiName;
        String bizType;
        List<ConfigData> configDataList;

        public List<ConfigPojo> toConfigPojoList(String pkg, String tenantId, String operator) {
            if (CollectionUtils.empty(configDataList)) {
                return Lists.newArrayList();
            }
            return configDataList.stream().map(x ->
                    ConfigPojo.builder()
                            .pkg(pkg)
                            .tenantId(tenantId)
                            .operator(operator)
                            .key(x.getConfigCode())
                            .assistantKey(x.getBizTypeValue())
                            .configValue(x.getConfigValue())
                            .build())
                    .collect(Collectors.toList());
        }
    }

    @Data
    @Builder
    class Result {

    }

}
