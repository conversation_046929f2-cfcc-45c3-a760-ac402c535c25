package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

import static com.facishare.crm.openapi.Utils.CUSTOMER_ACCOUNT_API_NAME;

/**
 * 客户账户
 * create by <PERSON><PERSON><PERSON> on 2019/03/31
 */
@Component
public class CustomerAccountObjectProvider extends DefaultObjectImportProvider {
    @Override
    public String getObjectCode() {
        return CUSTOMER_ACCOUNT_API_NAME;
    }

    @Override
    protected int getDuplicateJudgmentType(IObjectDescribe objectDescribe) {
        return UNSUPPORT_INSERT_IMPORT;
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return ImportType.UNSUPPORT_INSERT_IMPORT;
    }
}
