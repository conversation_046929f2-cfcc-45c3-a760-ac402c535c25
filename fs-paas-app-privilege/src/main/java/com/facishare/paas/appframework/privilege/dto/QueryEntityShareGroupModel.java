package com.facishare.paas.appframework.privilege.dto;

import lombok.*;
import lombok.experimental.Delegate;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class QueryEntityShareGroupModel {

  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode(callSuper = true)
  public static class Arg extends BasePrivilegeArg {
    private List<String> entityIds;
    private Integer permission;
    private Integer status;
    private BasePageInfoDataPrivilege page;
    private Map<Integer, Set<String>> shareIds;
    private Map<Integer, Set<String>> receiveIds;
    private Boolean outReceive;
    private Integer basedType;

    /**
     * 缺省时不用id查询
     */
    private List<String> ids;

    /**
     * 0来源方上游 1来源方下游
     *
     * NULL 查询全部
     */
    private Integer entityShareType;

    public static Arg createByIds(List<String> sharedRuleGroupIds, AuthContext buildAuthContext) {
      Arg arg = new Arg();
      arg.setIds(sharedRuleGroupIds);
      arg.setContext(buildAuthContext);
      return arg;
    }
  }

  @Data
  @EqualsAndHashCode(callSuper = true)
  public static class Result extends BasePrivilegeResult {
    @Delegate
    private BaseResultDataContent result;

    @Data
    public static class BaseResultDataContent {
      private List<EntityShareGroupPojo> content;
      private BasePageInfoDataPrivilege page;
    }
  }
}
