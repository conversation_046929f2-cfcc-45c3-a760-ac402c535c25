package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface DisableCustomField {
    @Data
    class Arg {

        @JSONField(name = "M1")
        private String describeAPIName;

        @JSONField(name = "M2")
        private String field_api_name;
    }

    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        Map objectDescribe;
    }
}
