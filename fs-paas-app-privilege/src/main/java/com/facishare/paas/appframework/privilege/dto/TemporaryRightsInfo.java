package com.facishare.paas.appframework.privilege.dto;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * create by z<PERSON><PERSON> on 2018/11/13
 */
@Data
@Builder
public class TemporaryRightsInfo {
    public static final String ENABLE = "enable";

    /**
     * 临时权限数据ID
     */
    String temporaryRightsId;
    /**
     * 数据所属对象
     */
    String objectDescribeApiName;
    String objectDescribeLabel;
    /**
     * 主属性
     */
    String objectDataId;
    String objectDataLabel;
    /**
     * 临时授权人员
     */
    String owner;
    String ownerName;
    /**
     * 来源（流程）
     */
    String scene;
    String sceneName;
    /**
     * 授权开始时间
     */
    Long startTime;
    /**
     * 授权结束时间
     */
    Long expiryTime;
    /**
     * 收回方式
     */
    String withdrawalWay;
    /**
     * 状态
     */
    String status;
    String sourceId;

    public static List<TemporaryRightsInfo> ofList(List<QueryTemporaryRightsList.TemporaryPermissionInfo> temporaryPermissionInfoList,
                                                   Map<String, Map<String, String>> objectApiNameForIdLabelMap,
                                                   Map<String, String> ownerLabelMap,
                                                   Map<String, IObjectDescribe> objectDescribeMap) {
        return temporaryPermissionInfoList.stream()
                .map(info -> TemporaryRightsInfo.builder()
                        .temporaryRightsId(info.getId())
                        .objectDescribeApiName(info.getEntityId())
                        .objectDescribeLabel(getDisplayName(objectDescribeMap, info.getEntityId()))
                        .objectDataId(info.getDataId())
                        .objectDataLabel(getObjectDataLabel(objectApiNameForIdLabelMap, info))
                        .owner(info.getOwner())
                        .ownerName(ownerLabelMap.get(info.getOwner()))
                        .sceneName(info.getSceneName())
                        .scene(info.getScene())
                        .startTime(info.getCreateTime())
                        .expiryTime(info.getExpiryTime())
                        .withdrawalWay(info.getWithdrawalWay())
                        .sourceId(info.getSourceId())
                        .status(Optional.ofNullable(info.getStatus()).orElse(ENABLE))
                        .build())
                .collect(Collectors.toList());
    }

    private static String getObjectDataLabel(Map<String, Map<String, String>> objectApiNameForIdLabelMap, QueryTemporaryRightsList.TemporaryPermissionInfo info) {
        Map<String, String> nameCache = objectApiNameForIdLabelMap.get(info.getEntityId());
        if (CollectionUtils.empty(nameCache)) {
            return info.getDataId();
        }
        return nameCache.get(info.getDataId());
    }

    private static String getDisplayName(Map<String, IObjectDescribe> objectDescribeMap, String entityId) {
        IObjectDescribe objectDescribe = objectDescribeMap.get(entityId);
        if (Objects.isNull(objectDescribe)) {
            return entityId;
        }
        return objectDescribe.getDisplayName();

    }
}
