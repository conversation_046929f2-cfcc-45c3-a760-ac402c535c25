package com.facishare.paas.appframework.metadata.component;

import com.facishare.paas.metadata.ui.layout.ICustomComponent;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class BatchQueryComponentDataTest {

  /**
   * GenerateByAI
   * 测试内容描述：测试BatchQueryComponentData.Arg的基本属性设置和获取
   */
  @Test
  @DisplayName("正常场景 - 测试Arg类的基本属性")
  void testArgBasicProperties() {
    // 创建测试数据
    List<String> apiNames = Lists.newArrayList("component1", "component2", "component3");
    
    // 使用Builder模式创建Arg对象
    BatchQueryComponentData.Arg arg = BatchQueryComponentData.Arg.builder()
        .apiNames(apiNames)
        .build();
    
    // 验证属性
    assertNotNull(arg, "Arg对象不应为空");
    assertEquals(apiNames, arg.getApiNames(), "API名称列表应该正确设置");
    assertEquals(3, arg.getApiNames().size(), "API名称列表大小应为3");
    assertTrue(arg.getApiNames().contains("component1"), "应包含component1");
    assertTrue(arg.getApiNames().contains("component2"), "应包含component2");
    assertTrue(arg.getApiNames().contains("component3"), "应包含component3");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试BatchQueryComponentData.Arg的空列表处理
   */
  @Test
  @DisplayName("边界场景 - 测试Arg类的空列表处理")
  void testArgEmptyList() {
    // 创建空列表
    List<String> emptyList = Lists.newArrayList();
    
    BatchQueryComponentData.Arg arg = BatchQueryComponentData.Arg.builder()
        .apiNames(emptyList)
        .build();
    
    // 验证空列表
    assertNotNull(arg.getApiNames(), "API名称列表不应为空");
    assertTrue(arg.getApiNames().isEmpty(), "API名称列表应为空");
    assertEquals(0, arg.getApiNames().size(), "API名称列表大小应为0");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试BatchQueryComponentData.Arg的null值处理
   */
  @Test
  @DisplayName("边界场景 - 测试Arg类的null值处理")
  void testArgNullValues() {
    // 创建包含null的Arg对象
    BatchQueryComponentData.Arg arg = BatchQueryComponentData.Arg.builder()
        .apiNames(null)
        .build();
    
    // 验证null值
    assertNull(arg.getApiNames(), "API名称列表可以为null");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试BatchQueryComponentData.Arg的Builder模式
   */
  @Test
  @DisplayName("正常场景 - 测试Arg类的Builder模式")
  void testArgBuilderPattern() {
    // 测试Builder模式的链式调用
    List<String> apiNames = Lists.newArrayList("test_component");
    
    BatchQueryComponentData.Arg arg = BatchQueryComponentData.Arg.builder()
        .apiNames(apiNames)
        .build();
    
    // 验证Builder构建的对象
    assertNotNull(arg, "Builder构建的对象不应为空");
    assertEquals(apiNames, arg.getApiNames(), "Builder设置的属性应正确");
    
    // 测试无参Builder
    BatchQueryComponentData.Arg emptyArg = BatchQueryComponentData.Arg.builder().build();
    assertNotNull(emptyArg, "无参Builder构建的对象不应为空");
    assertNull(emptyArg.getApiNames(), "无参Builder构建的对象属性应为null");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试BatchQueryComponentData.Arg的equals和hashCode方法
   */
  @Test
  @DisplayName("正常场景 - 测试Arg类的equals和hashCode方法")
  void testArgEqualsAndHashCode() {
    List<String> apiNames = Lists.newArrayList("component1", "component2");
    
    // 创建两个相同的Arg对象
    BatchQueryComponentData.Arg arg1 = BatchQueryComponentData.Arg.builder()
        .apiNames(apiNames)
        .build();
    
    BatchQueryComponentData.Arg arg2 = BatchQueryComponentData.Arg.builder()
        .apiNames(Lists.newArrayList("component1", "component2"))
        .build();
    
    // 验证equals
    assertEquals(arg1, arg2, "相同属性的Arg对象应该相等");
    assertEquals(arg1.hashCode(), arg2.hashCode(), "相同对象的hashCode应该相等");
    
    // 创建不同的Arg对象
    BatchQueryComponentData.Arg arg3 = BatchQueryComponentData.Arg.builder()
        .apiNames(Lists.newArrayList("different_component"))
        .build();
    
    assertNotEquals(arg1, arg3, "不同属性的Arg对象应该不相等");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试BatchQueryComponentData.Result类的基本功能
   */
  @Test
  @DisplayName("正常场景 - 测试Result类的基本功能")
  void testResultBasicFunctionality() {
    // 创建Result对象
    BatchQueryComponentData.Result result = new BatchQueryComponentData.Result();
    
    // 验证Result是ComponentResult的子类
    assertTrue(result instanceof ComponentResult, "Result应该是ComponentResult的子类");
    
    // 测试继承的方法
    result.setCode(0);
    result.setMessage("success");
    
    assertEquals(0, result.getCode(), "状态码应该正确设置");
    assertEquals("success", result.getMessage(), "消息应该正确设置");
    assertTrue(result.isSuccess(), "状态码为0时应该返回成功");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试BatchQueryComponentData.Result类的数据转换功能
   */
  @Test
  @DisplayName("正常场景 - 测试Result类的数据转换功能")
  void testResultDataConversion() {
    // 创建Result对象并设置数据
    BatchQueryComponentData.Result result = new BatchQueryComponentData.Result();
    
    ComponentData data = new ComponentData();
    data.setApiName("batch_component");
    data.setName("批量组件");
    data.setUrl("http://batch.com");
    
    result.setData(Lists.newArrayList(data));
    
    // 测试继承的toCustomComponents方法
    List<ICustomComponent> customComponents = result.toCustomComponents();
    assertNotNull(customComponents, "转换结果不应为空");
    assertEquals(1, customComponents.size(), "应该转换出1个组件");
    assertEquals("batch_component", customComponents.get(0).getName(), "组件名称应正确");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试BatchQueryComponentData.Arg的toString方法
   */
  @Test
  @DisplayName("正常场景 - 测试Arg类的toString方法")
  void testArgToString() {
    List<String> apiNames = Lists.newArrayList("component1", "component2");
    
    BatchQueryComponentData.Arg arg = BatchQueryComponentData.Arg.builder()
        .apiNames(apiNames)
        .build();
    
    String toString = arg.toString();
    
    // 验证toString包含属性信息
    assertNotNull(toString, "toString不应为空");
    assertTrue(toString.contains("component1") || toString.contains("apiNames"), 
               "toString应包含API名称信息");
  }
}
