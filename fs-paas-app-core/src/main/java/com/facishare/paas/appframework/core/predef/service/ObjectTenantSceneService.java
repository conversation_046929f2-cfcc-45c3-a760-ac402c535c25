package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.StandardConfigDocument;
import com.facishare.paas.appframework.core.predef.service.dto.scene.*;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.config.IUdefConfig;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.facishare.paas.appframework.metadata.dto.scene.ISystemScene;
import com.facishare.paas.appframework.metadata.dto.scene.ITenantScene;
import com.facishare.paas.appframework.metadata.dto.scene.SceneDTO;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.common.MetadataContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.lambda.tuple.Tuple;
import org.jooq.lambda.tuple.Tuple3;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;

import static com.facishare.paas.appframework.metadata.SearchTemplateExt.SceneType.TENANT_SCENE;

/**
 * create by zhaoju on 2019/04/30
 */
@Slf4j
@Component
@ServiceModule("tenant_scene")
public class ObjectTenantSceneService {

    @Autowired
    private SceneLogicService sceneLogicService;
    @Autowired
    private MetaDataService metaDataService;
    @Autowired
    private DescribeLogicService describeLogicService;

    @ServiceMethod("find_scene_list")
    public FindSceneList.Result findScenes(FindSceneList.Arg arg, ServiceContext context) {
        List<IScene> scenes = sceneLogicService.findScenes(arg.getDescribeApiName(), context.getUser(), arg.getExtendAttribute());
        List<IScene> baseScenes = sceneLogicService.findBaseScenes(arg.getDescribeApiName(), arg.getExtendAttribute(), context.getUser());
        List<IUdefConfig> sceneConfigList = sceneLogicService.findSceneConfigList(context.getUser(), arg.getDescribeApiName(), scenes);
        return FindSceneList.Result.of(scenes, baseScenes, StandardConfigDocument.ofList(sceneConfigList));
    }

    @ServiceMethod("find_scene")
    public FindSceneInfo.Result findScenes(FindSceneInfo.Arg arg, ServiceContext context) {
        IScene scene = sceneLogicService.findSceneByApiName(context.getUser(), arg.getDescribeApiName(), arg.getSceneApiName(),
                arg.getExtendAttribute(), arg.getAppId());
        return FindSceneInfo.Result.of(scene);
    }

    @ServiceMethod("create_scene")
    public ObjectTenantScene.Result createTenantScene(ObjectTenantScene.Arg arg, ServiceContext context) {
        SceneDTO scene = arg.getScene();
        if (Objects.isNull(scene)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        List<Tuple3<String, String, String>> api2Display2TypeForDefault = null;
        ITenantScene tenantScene = sceneLogicService.createTenantScene(scene, arg.getDescribeApiName(), scene.getExtendAttribute(), context.getUser());
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(context.getTenantId(), arg.getDescribeApiName());
        if (Boolean.TRUE.equals(scene.getClearCustomConfig())) {
            api2Display2TypeForDefault = Lists.newArrayList();
        }
        sceneLogicService.replaceOtherScenes(describe, scene, arg.getExtendAttribute(), api2Display2TypeForDefault, context.getUser());
        if (CollectionUtils.isNotEmpty(api2Display2TypeForDefault)) {
            sceneLogicService.ownCustomListConfigUserIdsToSend(scene.getObjectDescribeApiName(), describe.getDisplayName(), api2Display2TypeForDefault, arg.getExtendAttribute(), context.getUser());
        }
        return ObjectTenantScene.Result.of(tenantScene);
    }

    @ServiceMethod("update_scene")
    public ObjectTenantScene.Result updateTenantScene(ObjectTenantScene.Arg arg, ServiceContext context) {
        SceneDTO scene = arg.getScene();
        IScene resultScene = null;
        List<Tuple3<String, String, String>> api2Display2TypeForDefault = null;
        if (Boolean.TRUE.equals(scene.getClearCustomConfig())) {
            api2Display2TypeForDefault = Lists.newArrayList();
        }
        //租户场景校验和默认场景校验
        if (TENANT_SCENE.getType().equals(scene.getType())) {
            resultScene = sceneLogicService.updateTenantScene(scene, arg.getDescribeApiName(),
                    context.getUser());
        }
        else {    // DEFAULT_SCENE, CUSTOM_SCENE是员工自定义的, 不支持更新
            resultScene = sceneLogicService.updateSystemScene(scene, context.getUser());
            Tuple3<String, String, String> tuple = Tuple.tuple(scene.getApiName(), scene.getDisplayName(), scene.getType());
            if (api2Display2TypeForDefault != null) {
                api2Display2TypeForDefault.add(tuple);
            }
        }
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(context.getTenantId(), arg.getDescribeApiName());
        sceneLogicService.replaceOtherScenes(describe, scene, arg.getExtendAttribute(), api2Display2TypeForDefault, context.getUser());
        if (CollectionUtils.isNotEmpty(api2Display2TypeForDefault)) {
            sceneLogicService.ownCustomListConfigUserIdsToSend(scene.getObjectDescribeApiName(), describe.getDisplayName(),
                    api2Display2TypeForDefault, arg.getExtendAttribute(), context.getUser());
        }
        return ObjectTenantScene.Result.of(resultScene);
    }

    @ServiceMethod("updated_system_scene_orders")
    public DefaultSceneOrders.Result updatedDefaultOrders(DefaultSceneOrders.Arg arg, ServiceContext context) {
        ISystemScene systemScene = sceneLogicService.updatedDefaultOrders(arg.getOrders(), arg.getDescribeApiName(),
                arg.getSceneApiName(), arg.getExtendAttribute(), context.getUser());
        return DefaultSceneOrders.Result.builder().scene(systemScene).build();
    }

    @ServiceMethod("enable_scene")
    public ChangeSceneStatus.Result enableScene(ChangeSceneStatus.Arg arg, ServiceContext context) {
        sceneLogicService.enableScene(arg.getDescribeApiName(), arg.getSceneApiName(), arg.getType(), arg.getExtendAttribute(), context.getUser());
        return ChangeSceneStatus.Result.success();
    }

    @ServiceMethod("disable_scene")
    public ChangeSceneStatus.Result disableScene(ChangeSceneStatus.Arg arg, ServiceContext context) {
        sceneLogicService.disableScene(arg.getDescribeApiName(), arg.getSceneApiName(), arg.getType(), arg.getExtendAttribute(), context.getUser());
        return ChangeSceneStatus.Result.success();
    }

    @ServiceMethod("delete_scene")
    public ChangeSceneStatus.Result deleteScene(ChangeSceneStatus.Arg arg, ServiceContext context) {
        sceneLogicService.deleteTenantScene(arg.getDescribeApiName(), arg.getSceneApiName(), arg.getExtendAttribute(), context.getUser());
        return ChangeSceneStatus.Result.success();
    }

    @ServiceMethod("validate_tenant_scene_count")
    public ValidateTenantSceneCount.Result validateTenantSceneCount(ValidateTenantSceneCount.Arg arg,
                                                                    ServiceContext context) {
        sceneLogicService.validateTenantSceneSum(arg.getDescribeApiName(), context.getTenantId(), arg.getExtendAttribute());
        return ValidateTenantSceneCount.Result.builder().success(true).build();
    }

    @ServiceMethod("validate_scene_name")
    public ValidateSceneName.Result validateSceneName(ValidateSceneName.Arg arg, ServiceContext context) {
        SceneLogicServiceImpl.ValidateResult result = sceneLogicService.validateApiNameAndLabel(arg.getDescribeApiName(),
                arg.getSceneApiName(), arg.getDisplayName(), arg.getId(), context.getTenantId(), arg.getExtendAttribute());
        return ValidateSceneName.Result.builder().apiNameRepeat(result.isApiName()).displayNameRepeat(result.isLabel()).build();
    }

    @ServiceMethod("init_system_scene")
    public InitSystemScene.Result initSystemScene(InitSystemScene.Arg arg, ServiceContext context) {
        MetadataContext metadataContext = MetadataContextExt.of(context.getUser()).getMetadataContext();
        metadataContext.property("option", arg.getOption());
        metadataContext.property("objectType", arg.getObjectType());
        metadataContext.property("sceneApiName", arg.getSceneApiName());
        metadataContext.property("searchId", arg.getSearchId());
        metadataContext.setTenantId(arg.getTenantId());
        sceneLogicService.initSystemScene(arg.getDescribeApiName(), arg.getExtendAttribute(), context.getUser(), metadataContext);
        return InitSystemScene.Result.builder().build();
    }

    @ServiceMethod("set_default_scene_priority")
    public DefaultScenePriority.Result setDefaultScenePriority(DefaultScenePriority.Arg arg, ServiceContext context) {
        sceneLogicService.setUpDefaultScenePriority(arg.getDescribeApiName(), arg.getSceneApiNames(), arg.getExtendAttribute(), context.getUser());
        return DefaultScenePriority.Result.buildSuccess();
    }

    @ServiceMethod("find_scene_config")
    public FindSceneConfig.Result findSceneConfig(FindSceneConfig.Arg arg, ServiceContext context) {
        IUdefConfig sceneConfig = sceneLogicService.findSceneConfig(arg.getDescribeApiName(), arg.getSceneApiName(), context.getUser());
        return FindSceneConfig.Result.of(StandardConfigDocument.of(sceneConfig));
    }

    @ServiceMethod("support_or_filter")
    public SupportOrFilter.Result supportOrFilter(SupportOrFilter.Arg arg, ServiceContext context) {
        boolean supportOrFilter = metaDataService.isSupportOrFilter(context.getTenantId(), arg.getDescribeApiName());
        return SupportOrFilter.Result.builder().supportOrFilter(supportOrFilter).build();
    }

    private Supplier<IObjectDescribe> getObjectDescribeSupplier(ServiceContext context, String describeApiName) {
        return () -> describeLogicService.findObjectUseThreadLocalCache(context.getTenantId(), describeApiName);
    }

}
