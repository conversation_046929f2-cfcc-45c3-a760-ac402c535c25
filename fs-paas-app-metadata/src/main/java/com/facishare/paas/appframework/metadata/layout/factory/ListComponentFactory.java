package com.facishare.paas.appframework.metadata.layout.factory;

import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.layout.component.IViewComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentInfo;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.CommonComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.facishare.paas.appframework.metadata.layout.component.ListComponentExt.COMPONENT_TYPE_LIST;
import static com.facishare.paas.appframework.metadata.layout.component.ListComponentExt.LIST_COMPONENT;

/**
 * Created by zhaooju on 2022/6/27
 */
@Component
public class ListComponentFactory implements IComponentFactory {

    @Autowired
    private ViewComponentFactory viewComponentFactory;

    public ListComponentExt createDefaultComponent(User user, IObjectDescribe describe) {
        IComponent component = new CommonComponent();
        component.setType(COMPONENT_TYPE_LIST);
        ListComponentExt componentExt = ListComponentExt.of(component);
        componentExt.setName(LIST_COMPONENT);
        componentExt.setHeader(I18NExt.getOrDefault(I18NKey.LIST_PAGE, "列表页"));// ignoreI18n
        componentExt.setNameI18nKey(I18NKey.LIST_PAGE);
        // 视图
        List<IViewComponentInfo> viewComponentInfos = viewComponentFactory.create(user, describe);
        componentExt.resetViewInfos(viewComponentInfos);
        // 快速筛选
        componentExt.resetFiltersInfos(Lists.newArrayList());
        // 按钮
        componentExt.resetButtonInfos(Lists.newArrayList(ListComponentInfo.listBatch(),
                ListComponentInfo.listNormal(), ListComponentInfo.listSingle()));

        return componentExt;
    }

    @Override
    public String supportComponentType() {
        return ListComponentExt.COMPONENT_TYPE_LIST;
    }

    @Override
    public IComponent createDefaultComponent(ILayoutFactory.Context context, IObjectDescribe describe) {
        return createDefaultComponent(context.getUser(), describe).getComponent();
    }
}
