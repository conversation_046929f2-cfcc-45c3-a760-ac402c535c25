package com.facishare.paas.appframework.core.predef.service.dto.scene;

import lombok.Builder;
import lombok.Data;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/08/26
 */
public interface InitSystemScene {
    @Data
    class Arg {
        private String tenantId;
        private String describeApiName;
        private String searchId;
        private String extendAttribute;
        /**
         * option: insert-新增系统库,update-更新系统库,delete-删除系统库,传其他值不操作表只更新系统库缓存
         */
        private String option;
        /**
         * objectType: custom-自定义对象,其他值为预设对象
         */
        private String objectType;
        private String sceneApiName;
    }

    @Data
    @Builder
    class Result {
        @Builder.Default
        private boolean success = true;
    }
}
