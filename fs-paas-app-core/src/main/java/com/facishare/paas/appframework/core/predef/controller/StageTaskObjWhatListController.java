package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * create by zhangyixuan on 2019/07/30
 */
public class StageTaskObjWhatListController extends AbstractStandardWhatListController<AbstractStandardWhatListController.Arg> {
    @Override
    protected void doFunPrivilegeCheck() {
//        super.doFunPrivilegeCheck();
    }

    @Override
    protected BaseListController.Result after(Arg arg, BaseListController.Result result) {
        List<IObjectData> dataList = queryResult.getData();
        if (!CollectionUtils.empty(dataList)) {
            dataList.forEach(data -> {
                String objApiName = data.get("object_api_name", String.class);
                IObjectDescribe objectDesc = objects.get(objApiName);
                if (Objects.isNull(objectDesc)) {
                    return;
                }
                SelectOneFieldDescribe fieldDescribe = (SelectOneFieldDescribe) objectDesc.getFieldDescribe(data.get("stage_field_api_name", String.class));
                Optional<ISelectOption> stageOption = fieldDescribe.getOption(data.get("stage_id", String.class));
                stageOption.ifPresent(item -> data.set("stage_id", item.getLabel()));
                data.set("stage_field_api_name", fieldDescribe.getLabel());
            });
        }
        return result;
    }

}
