package com.facishare.paas.appframework.core.predef.service.dto.log;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface GetSnapShotForWeb {
    @Data
    class Arg {
        @JSONField(name = "M1")
        private String logId;

        @JSONField(name = "M2")
        private String apiName;

        private String sourceId;

        private String sourceApiName;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        @J<PERSON>NField(name = "M1")
        private Map data;

        @JSONField(name = "M2")
        private Map describe;

        @JSONField(name = "M3")
        private Map layout;

        @JSONField(name = "M4")
        private int snapType;

        @J<PERSON><PERSON>ield(name = "M5")
        private boolean canRecover;

        @J<PERSON>NField(name = "M6")
        private Map objectDescribeExt;
    }
}
