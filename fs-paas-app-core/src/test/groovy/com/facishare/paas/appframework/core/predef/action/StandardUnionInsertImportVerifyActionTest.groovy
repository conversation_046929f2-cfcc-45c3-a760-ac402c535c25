package com.facishare.paas.appframework.core.predef.action

import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.describe.ObjectReferenceManyFieldDescribe
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe
import com.fxiaoke.functions.utils.Lists
import com.google.common.collect.Maps
import spock.lang.Shared
import spock.lang.Specification

class StandardUnionInsertImportVerifyActionTest extends Specification {

    @Shared
    def objectApiName = "object_123__c"

    def actionCode = "UnionInsertImportData"

    def tenantId = "74255"

    def userId = "1000"

    def outTenantId = "200074255"

    def outUserId = "100018916"

    def user = User.builder().tenantId(tenantId).userId(userId).outTenantId(outTenantId).outUserId(outUserId).build()

    def requestContext = RequestContext.builder().tenantId(tenantId).user(user).build()

    def actionContext = new ActionContext(requestContext, objectApiName, actionCode)

    ServiceFacade serviceFacade
    InfraServiceFacade infraServiceFacade
    IObjectDescribe objectDescribe
    SpringBeanHolder springBeanHolder

    def setup() {
        serviceFacade = Mock()
        infraServiceFacade = Mock()
        objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        objectDescribe.setIsActive(true)
        springBeanHolder = Mock(SpringBeanHolder)
        infraServiceFacade.getSpringBeanHolder() >> springBeanHolder
    }

    def "test getValidImportFields"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        List<String> unionApiNames = [objectApiName]

        def apiName = "object_md__c"
        if (Objects.nonNull(fieldDescribe)) {
            fieldDescribe.setApiName("field_md__c")
            fieldDescribe.set("target_api_name", objectApiName)
            objectDescribe.setFieldDescribes(Lists.newArrayList(fieldDescribe))
            unionApiNames.add("object_md__c")
        }


        BaseImportAction.MasterInfo masterInfo = new BaseImportAction.MasterInfo()
        masterInfo.setApiName(objectApiName)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                supportFieldMapping: supportFieldMapping,
                masterInfo: masterInfo,
                unionApiNameList: unionApiNames,
                apiName: apiName
        )

        StandardUnionInsertImportVerifyAction action = new StandardUnionInsertImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                infraServiceFacade: infraServiceFacade,
                arg: arg
        )
        when:
        infraServiceFacade.getTemplateField(actionContext.getUser(), objectDescribe) >> objectDescribe.getFieldDescribes()
        action.getValidImportFields()
        then:
        noExceptionThrown()
        where:
        supportFieldMapping | fieldDescribe
        true                | new MasterDetailFieldDescribe()
        true                | new ObjectReferenceFieldDescribe()
        true                | new ObjectReferenceManyFieldDescribe()
        false               | new MasterDetailFieldDescribe()
        false               | new ObjectReferenceFieldDescribe()
        false               | new ObjectReferenceManyFieldDescribe()
    }

    def "test validateDuplicatedSearchRule"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)

        BaseImportAction.MasterInfo masterInfo = new BaseImportAction.MasterInfo()
        masterInfo.setApiName(objectApiName)


        List<BaseImportAction.DetailInfo> detailInfoList = Lists.newArrayList()
        BaseImportAction.DetailInfo detailInfo = new BaseImportAction.DetailInfo()
        detailInfo.setApiName("object_md__c")
        detailInfoList.add(detailInfo)

        List<String> unionApiNames = [objectApiName]

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                supportFieldMapping: supportFieldMapping,
                masterInfo: masterInfo,
                unionApiNameList: unionApiNames,
                detailInfo: detailInfoList
        )

        StandardUnionInsertImportVerifyAction action = new StandardUnionInsertImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                arg: arg
        )
        when:
        serviceFacade.findAllDuplicateSearchByApiNameAndType(*_) >> [objectApiName: []]
        serviceFacade.findDuplicatedSearchListByApiNamesAndType(*_) >> [objectApiName: []]
        action.validateDuplicatedSearchRule()
        then:
        noExceptionThrown()
        where:
        supportFieldMapping << [true, false]
    }

    def "test getNotValidFieldsByRuleField"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)

        IObjectDescribe detailDescribe = Spy(ObjectDescribe)
        detailDescribe.setApiName(detailApiName)
        MasterDetailFieldDescribe masterDetailFieldDescribe = new MasterDetailFieldDescribe()
        masterDetailFieldDescribe.setApiName("field_md__c")
        detailDescribe.setFieldDescribes(Lists.newArrayList(masterDetailFieldDescribe))

        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        String fieldApiName = "name"
        fieldDescribe.setApiName(fieldApiName)
        List<IFieldDescribe> validFieldList = Lists.newArrayList(fieldDescribe)

        List<String> ruleFieldName = Lists.newArrayList()
        ruleFieldName.add("name")

        BaseImportAction.MasterInfo masterInfo = new BaseImportAction.MasterInfo()
        masterInfo.setApiName(objectApiName)


        List<BaseImportAction.DetailInfo> detailInfoList = Lists.newArrayList()
        BaseImportAction.DetailInfo detailInfo = new BaseImportAction.DetailInfo()
        detailInfo.setApiName(detailApiName)
        detailInfoList.add(detailInfo)

        List<String> unionApiNames = [objectApiName, detailApiName]

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                supportFieldMapping: supportFieldMapping,
                masterInfo: masterInfo,
                unionApiNameList: unionApiNames,
                detailInfo: detailInfoList,
                apiName: detailApiName
        )

        StandardUnionInsertImportVerifyAction action = new StandardUnionInsertImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                arg: arg
        )
        when:
        serviceFacade.findObject(_, _) >> detailDescribe
        action.getNotValidFieldsByRuleField(validFieldList, ruleFieldName)
        then:
        noExceptionThrown()
        where:
        supportFieldMapping | detailApiName
        true                | "object_md__c"
        false               | "object_md__c"
        false               | objectApiName
    }

    def "test validateUniqueRule"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        String fieldApiName = "field_text__c"
        fieldDescribe.setApiName(fieldApiName)
        List<IFieldDescribe> validFieldList = Lists.newArrayList(fieldDescribe)

        Map<String, Object> data = Maps.newHashMap()
        data.put(fieldApiName, "test")

        StandardUnionInsertImportVerifyAction action = new StandardUnionInsertImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade
        )
        when:
        action.validateUniqueRule(validFieldList, data.entrySet())
        then:
        noExceptionThrown()
    }


    def "test getValidImportFieldList"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)

        IObjectDescribe detailDescribe = Spy(ObjectDescribe)
        detailDescribe.setApiName(detailApiName)
        MasterDetailFieldDescribe masterDetailFieldDescribe = new MasterDetailFieldDescribe()
        masterDetailFieldDescribe.setApiName("field_md__c")
        detailDescribe.setFieldDescribes(Lists.newArrayList(masterDetailFieldDescribe))

        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        String fieldApiName = "name"
        fieldDescribe.setApiName(fieldApiName)
        List<IFieldDescribe> validFieldList = Lists.newArrayList(fieldDescribe)

        List<String> ruleFieldName = Lists.newArrayList()
        ruleFieldName.add("name")

        BaseImportAction.MasterInfo masterInfo = new BaseImportAction.MasterInfo()
        masterInfo.setApiName(objectApiName)


        List<BaseImportAction.DetailInfo> detailInfoList = Lists.newArrayList()
        BaseImportAction.DetailInfo detailInfo = new BaseImportAction.DetailInfo()
        detailInfo.setApiName(detailApiName)
        detailInfoList.add(detailInfo)

        List<String> unionApiNames = [objectApiName, detailApiName]

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                supportFieldMapping: supportFieldMapping,
                masterInfo: masterInfo,
                unionApiNameList: unionApiNames,
                detailInfo: detailInfoList,
                apiName: detailApiName
        )

        StandardUnionInsertImportVerifyAction action = new StandardUnionInsertImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                arg: arg
        )
        when:
        serviceFacade.findObject(_, _) >> detailDescribe
        action.getValidImportFieldList(validFieldList, ruleFieldName)
        then:
        noExceptionThrown()
        where:
        supportFieldMapping | detailApiName
        true                | "object_md__c"
        false               | "object_md__c"
        false               | objectApiName
    }
}
