package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.privilege.dto.RestResult;
import com.facishare.paas.appframework.privilege.model.role.Role;

import java.util.List;

/**
 * Created by luxin on 2018/5/28.
 */
public interface RoleService {

    /**
     * 增加默认的角色
     *
     * @param tenantId
     * @param role
     * @throws PermissionError 如果失败,会抛出RuntimeException
     */
    void addPredefinedRole(String tenantId, Role role);


    List<RestResult.RoleInfoPojo> getOuterRoleInfoList(String tenantId, String appId);


    /**
     * 获取appId是crm的内部企业角色信息列表
     *
     * @param tenantId
     * @return
     */
    List<RestResult.RoleInfoPojo> getCrmRoleInfoList(String tenantId);

    /**
     * 判断角色是否存在
     *
     * @param tenantId
     * @param appId
     * @param roleCode
     * @return
     */
    Boolean checkRoleIsExist(String tenantId, String appId, String roleCode);

}
