package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.ui.layout.TableColumn;
import com.facishare.paas.metadata.impl.ui.layout.component.RelatedObjectList;
import com.facishare.paas.metadata.ui.layout.IRelatedObjectList;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * Created by liyiguang on 2017/11/14.
 */
public class PaymentComponentBuilder {

    public IRelatedObjectList build() {
        IRelatedObjectList component = getPaymentComponentWithoutFields();
        component.setIncludeFields(getTableColumnList());

        return component;
    }

    public RelatedObjectList getPaymentComponentWithoutFields() {
        return LayoutComponents.buildPaymentComponent();
    }

    private List<ITableColumn> getTableColumnList() {
        TableColumn orderNoField = new TableColumn();
        orderNoField.setRenderType(IFieldType.TEXT);
        orderNoField.setName("name");
        orderNoField.setLabelName(I18N.text(I18NKey.RECEIPT_ORDER_NO));
        orderNoField.set("api_name", "name");

        TableColumn amountField = new TableColumn();
        amountField.setRenderType(IFieldType.TEXT);
        amountField.setName("amount");
        amountField.setLabelName(I18N.text(I18NKey.RECEIPT_AMOUNT));
        amountField.set("api_name", "amount");

        List<ITableColumn> list = Lists.newArrayList(orderNoField, amountField);

        TableColumn feeField = new TableColumn();
        feeField.setRenderType(IFieldType.TEXT);
        feeField.setName("fee");
        feeField.setLabelName(I18N.text(I18NKey.SERVICE_FEE));
        feeField.set("api_name", "fee");
        list.add(feeField);


        TableColumn payStatusField = new TableColumn();
        payStatusField.setRenderType(IFieldType.TEXT);
        payStatusField.setName("payStatus");
        payStatusField.setLabelName(I18N.text(I18NKey.STATUS));
        payStatusField.set("api_name", "payStatus");
        list.add(payStatusField);


        TableColumn payTypeField = new TableColumn();
        payTypeField.setRenderType(IFieldType.TEXT);
        payTypeField.setName("payType");
        payTypeField.setLabelName(I18N.text(I18NKey.PAY_TYPE));
        payTypeField.set("api_name", "payType");
        list.add(payTypeField);


        TableColumn finishTimeField = new TableColumn();
        finishTimeField.setRenderType(IFieldType.TEXT);
        finishTimeField.setName("finishTime");
        finishTimeField.setLabelName(I18N.text(I18NKey.PAY_FINISH_TIME));
        finishTimeField.set("api_name", "finishTime");
        list.add(finishTimeField);


        TableColumn transTimeField = new TableColumn();
        transTimeField.setRenderType(IFieldType.TEXT);
        transTimeField.setName("transTime");
        transTimeField.setLabelName(I18N.text(I18NKey.TRANSACTION_TIME));
        transTimeField.set("api_name", "transTime");
        list.add(transTimeField);
        TableColumn remarkField = new TableColumn();
        remarkField.setRenderType(IFieldType.TEXT);
        remarkField.setName("remark");
        remarkField.setLabelName(I18N.text(I18NKey.REMARK));
        remarkField.set("api_name", "remark");
        list.add(remarkField);
        TableColumn payEnterpriseNameField = new TableColumn();
        payEnterpriseNameField.setRenderType(IFieldType.TEXT);
        payEnterpriseNameField.setName("payEnterpriseName");
        payEnterpriseNameField.setLabelName(I18N.text(I18NKey.PAY_ENTERPRISE_NAME));
        payEnterpriseNameField.set("api_name", "payEnterpriseName");
        list.add(payEnterpriseNameField);

        return list;
    }
}
