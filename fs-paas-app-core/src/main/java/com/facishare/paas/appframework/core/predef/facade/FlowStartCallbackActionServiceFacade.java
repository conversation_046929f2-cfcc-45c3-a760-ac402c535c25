package com.facishare.paas.appframework.core.predef.facade;

import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.predef.handler.flowstartcallback.FlowStartCallbackHandler;
import com.facishare.paas.appframework.metadata.handler.HandlerType;

import java.util.List;

/**
 * Created by zhouwr on 2023/10/12.
 */
public interface FlowStartCallbackActionServiceFacade {
    FlowStartCallbackHandler.Result executeTriggerActionTenantHandler(HandlerContext context,
                                                                      FlowStartCallbackHandler.Arg arg,
                                                                      List<HandlerType> handlerTypes);
}
