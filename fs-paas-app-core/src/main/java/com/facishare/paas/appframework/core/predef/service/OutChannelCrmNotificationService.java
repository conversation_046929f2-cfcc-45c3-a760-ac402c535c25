package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.model.ExtraChannel;
import com.facishare.paas.appframework.common.service.model.KeyValueItem;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.OutInfoChangeModel;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.prm.enums.ChannelNoticeBizType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import static com.facishare.paas.appframework.prm.model.OutNotificationConstant.*;

/**
 * <AUTHOR>
 * @time 2023-05-05 11:45
 * @Description
 */
@Service
@Slf4j
public class OutChannelCrmNotificationService {
    @Autowired
    private CRMNotificationServiceImpl crmNotificationService;
    @Autowired
    private WXOfficialAccountService wxOfficialAccountService;

    public void fillSendOutChannelInfo(@NotNull User user, @NotNull NewCrmNotification newCrmNotification,
                                       @NotNull ChannelNoticeBizType noticeBizType, Object data) {
        switch (noticeBizType) {
            case ALLOCATE_OBJECT:
                if (data != null) {
                    objectAllocation(user, newCrmNotification, (OutInfoChangeModel) data);
                }
                break;
            case CHANGE_OUT_TEAM:
                outTeamChange(user, newCrmNotification);
                break;
            default:
                log.warn("fillSendOutChannelInfo 当前操作未被实现, tenantId:{}, type:{}", user.getTenantId(), noticeBizType);
        }
        crmNotificationService.sendNewCrmNotification(user, newCrmNotification);
    }

    private void outTeamChange(User user, NewCrmNotification newCrmNotification) {
        IObjectData wxTemplateData = getOpenTemplateByNotificationType(ChannelNoticeBizType.CHANGE_OUT_TEAM, user);
        if (wxTemplateData == null) {
            return;
        }
        String outText = I18N.text(I18NKey.TEAM_MEMBER_CHANGE);
        Map<String, String> treeMap = new TreeMap<>();
        treeMap.put(BUSINESS_NAME, outText);
        treeMap.put(CURRENT_STAGE, newCrmNotification.getFullContent());
        buildOutChannelInfo(newCrmNotification, wxTemplateData, treeMap);
    }

    private IObjectData getOpenTemplateByNotificationType(ChannelNoticeBizType changeOutTeam, User user) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        List<IFilter> filters = query.getFilters();
        Filter actionFilter = new Filter();
        actionFilter.setFieldName(ACTION_MODULE);
        actionFilter.setOperator(Operator.EQ);
        actionFilter.setFieldValues(Lists.newArrayList(changeOutTeam.getAction()));
        Filter openStatusFilter = new Filter();
        openStatusFilter.setFieldName(OPEN_STATUS);
        openStatusFilter.setOperator(Operator.EQ);
        openStatusFilter.setFieldValues(Lists.newArrayList(Boolean.TRUE.toString()));
        filters.add(actionFilter);
        filters.add(openStatusFilter);
        List<IObjectData> wxTemplatedList = Lists.newArrayList();
        try {
            wxTemplatedList = wxOfficialAccountService.getlWXTemplatedList(user, query);
        } catch (Exception e) {
            log.error("getWXTemplatedList error, tenantId:{}", user.getTenantId(), e);
        }
        if (CollectionUtils.empty(wxTemplatedList)) {
            return null;
        } else {
            return wxTemplatedList.get(0);
        }
    }

    private void objectAllocation(User user, NewCrmNotification newCrmNotification, OutInfoChangeModel outInfo) {
        if (outInfo == null) {
            return;
        }
        IObjectData wxTemplateData = getOpenTemplateByNotificationType(ChannelNoticeBizType.ALLOCATE_OBJECT, user);
        if (wxTemplateData == null) {
            return;
        }
        String outText = outInfo.getDisplayName() + " " + outInfo.getDataName();
        Map<String, String> treeMap = new TreeMap<>();
        treeMap.put(BUSINESS_NAME, outText);
        treeMap.put(CURRENT_STAGE, newCrmNotification.getFullContent());
        buildOutChannelInfo(newCrmNotification, wxTemplateData, treeMap);
    }

    private static void buildOutChannelInfo(NewCrmNotification newCrmNotification, IObjectData data, Map<String, String> templateFieldMap) {
        String wxAppId = data.get(WX_APP_ID, String.class);
        String appId = newCrmNotification.getAppId();
        String templateId = data.get(TEMPLATE_ID, String.class);

        List<KeyValueItem> bodyForm = buildBodyForm(wxAppId, templateFieldMap);
        newCrmNotification.setBodyForm(bodyForm);

        List<String> extraChannelList = buildExtraChannel(appId);
        newCrmNotification.setExtraChannelList(extraChannelList);

        Map<String, Object> extraDataMap = buildExtraDataMap(templateId, wxAppId);
        newCrmNotification.setExtraDataMap(extraDataMap);

        Map<String, List<Map<String, String>>> templateIdKeyListMap = buildTemplateKeyListMap(wxAppId, templateId, templateFieldMap);
        newCrmNotification.setTemplateIdKeyListMap(templateIdKeyListMap);
    }

    private static Map<String, List<Map<String, String>>> buildTemplateKeyListMap(String wxAppId, String templateId, Map<String, String> templateFieldMap) {
        Map<String, List<Map<String, String>>> templateIdKeyListMap = new HashMap<>(1);
        List<Map<String, String>> templateFiledMaplist = Lists.newArrayList();
        templateFieldMap.forEach((key, value) -> {
            Map<String, String> map = new HashMap<>();
            map.put(key, wxAppId + "_" + key);
            templateFiledMaplist.add(map);
        });
        templateIdKeyListMap.put(templateId, templateFiledMaplist);
        return templateIdKeyListMap;
    }

    private static Map<String, Object> buildExtraDataMap(String templateId, String wxAppId) {
        Map<String, Object> extraDataMap = new HashMap<>(6);
        extraDataMap.put(WECHAT_TEMPLATE_ID, templateId);
        extraDataMap.put(FORWARD_TYPE, "0");
        extraDataMap.put(INNER_NO_USE_BODY_FORM, true);
        extraDataMap.put(TARGET_WX_APP_ID, wxAppId);
        return extraDataMap;
    }

    private static List<String> buildExtraChannel(@NotNull String appId) {
        List<String> extraChannelList = Lists.newArrayList();
        ExtraChannel extraChannel = new ExtraChannel();
        extraChannel.setOutChannelType(4);
        extraChannel.setReceiverChannelType(6);
        extraChannel.setAppId(appId);
        String extraChannelString = extraChannel.toString();
        extraChannelList.add(extraChannelString);
        return extraChannelList;
    }

    private static List<KeyValueItem> buildBodyForm(String wxAppId, Map<String, String> templateFieldMap) {
        List<KeyValueItem> bodyForm = Lists.newArrayList();
        templateFieldMap.forEach((key, value) -> {
            KeyValueItem keyValueItem = new KeyValueItem();
            KeyValueItem.TextElement textKeyElement = new KeyValueItem.TextElement();
            textKeyElement.setText(wxAppId + "_" + key);
            keyValueItem.setKeyElement(textKeyElement);

            KeyValueItem.TextElement textValueElement = new KeyValueItem.TextElement();
            textValueElement.setText(value);
            keyValueItem.setValueElement(textValueElement);
            bodyForm.add(keyValueItem);
        });
        return bodyForm;
    }
}
