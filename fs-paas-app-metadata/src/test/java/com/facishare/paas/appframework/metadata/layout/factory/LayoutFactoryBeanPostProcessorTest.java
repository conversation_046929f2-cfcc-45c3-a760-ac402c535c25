package com.facishare.paas.appframework.metadata.layout.factory;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.BeansException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LayoutFactoryBeanPostProcessorTest {

    @Mock
    private LayoutFactoryManager layoutFactoryManager;

    @Mock
    private IComponentFactoryManager componentFactoryManager;

    private LayoutFactoryBeanPostProcessor layoutFactoryBeanPostProcessor;

    @BeforeEach
    void setUp() {
        layoutFactoryBeanPostProcessor = new LayoutFactoryBeanPostProcessor(layoutFactoryManager, componentFactoryManager);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构造函数正常初始化
     */
    @Test
    @DisplayName("正常场景 - 构造函数初始化")
    void testConstructor_NormalCase() {
        // When
        LayoutFactoryBeanPostProcessor processor = new LayoutFactoryBeanPostProcessor(layoutFactoryManager, componentFactoryManager);

        // Then
        assertNotNull(processor);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试postProcessBeforeInitialization方法返回原始bean
     */
    @Test
    @DisplayName("正常场景 - postProcessBeforeInitialization返回原始bean")
    void testPostProcessBeforeInitialization_ReturnsOriginalBean() {
        // Given
        Object testBean = new Object();
        String beanName = "testBean";

        // When
        Object result = layoutFactoryBeanPostProcessor.postProcessBeforeInitialization(testBean, beanName);

        // Then
        assertSame(testBean, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试postProcessAfterInitialization方法调用所有manager的register方法
     */
    @Test
    @DisplayName("正常场景 - postProcessAfterInitialization调用register方法")
    void testPostProcessAfterInitialization_CallsRegisterOnAllManagers() {
        // Given
        Object testBean = new Object();
        String beanName = "testBean";

        // When
        Object result = layoutFactoryBeanPostProcessor.postProcessAfterInitialization(testBean, beanName);

        // Then
        assertSame(testBean, result);
        verify(layoutFactoryManager).register(testBean);
        verify(componentFactoryManager).register(testBean);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试postProcessAfterInitialization方法处理null bean
     */
    @Test
    @DisplayName("边界场景 - postProcessAfterInitialization处理null bean")
    void testPostProcessAfterInitialization_WithNullBean() {
        // Given
        Object testBean = null;
        String beanName = "testBean";

        // When
        Object result = layoutFactoryBeanPostProcessor.postProcessAfterInitialization(testBean, beanName);

        // Then
        assertNull(result);
        verify(layoutFactoryManager).register(null);
        verify(componentFactoryManager).register(null);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试postProcessAfterInitialization方法处理null beanName
     */
    @Test
    @DisplayName("边界场景 - postProcessAfterInitialization处理null beanName")
    void testPostProcessAfterInitialization_WithNullBeanName() {
        // Given
        Object testBean = new Object();
        String beanName = null;

        // When
        Object result = layoutFactoryBeanPostProcessor.postProcessAfterInitialization(testBean, beanName);

        // Then
        assertSame(testBean, result);
        verify(layoutFactoryManager).register(testBean);
        verify(componentFactoryManager).register(testBean);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试postProcessBeforeInitialization方法处理null bean
     */
    @Test
    @DisplayName("边界场景 - postProcessBeforeInitialization处理null bean")
    void testPostProcessBeforeInitialization_WithNullBean() {
        // Given
        Object testBean = null;
        String beanName = "testBean";

        // When
        Object result = layoutFactoryBeanPostProcessor.postProcessBeforeInitialization(testBean, beanName);

        // Then
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试postProcessBeforeInitialization方法处理null beanName
     */
    @Test
    @DisplayName("边界场景 - postProcessBeforeInitialization处理null beanName")
    void testPostProcessBeforeInitialization_WithNullBeanName() {
        // Given
        Object testBean = new Object();
        String beanName = null;

        // When
        Object result = layoutFactoryBeanPostProcessor.postProcessBeforeInitialization(testBean, beanName);

        // Then
        assertSame(testBean, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试postProcessAfterInitialization方法处理manager抛出异常的情况
     */
    @Test
    @DisplayName("异常场景 - manager register方法抛出异常")
    void testPostProcessAfterInitialization_ManagerThrowsException() {
        // Given
        Object testBean = new Object();
        String beanName = "testBean";
        doThrow(new RuntimeException("Test exception")).when(layoutFactoryManager).register(any());

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            layoutFactoryBeanPostProcessor.postProcessAfterInitialization(testBean, beanName);
        });

        verify(layoutFactoryManager).register(testBean);
        // componentFactoryManager.register 可能不会被调用，因为layoutFactoryManager抛出了异常
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构造函数处理null参数
     */
    @Test
    @DisplayName("边界场景 - 构造函数处理null参数")
    void testConstructor_WithNullParameters() {
        // When & Then
        assertDoesNotThrow(() -> {
            new LayoutFactoryBeanPostProcessor(null, null);
        });

        assertDoesNotThrow(() -> {
            new LayoutFactoryBeanPostProcessor(layoutFactoryManager, null);
        });

        assertDoesNotThrow(() -> {
            new LayoutFactoryBeanPostProcessor(null, componentFactoryManager);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多次调用postProcessAfterInitialization方法
     */
    @Test
    @DisplayName("正常场景 - 多次调用postProcessAfterInitialization")
    void testPostProcessAfterInitialization_MultipleCalls() {
        // Given
        Object testBean1 = new Object();
        Object testBean2 = new Object();
        String beanName1 = "testBean1";
        String beanName2 = "testBean2";

        // When
        Object result1 = layoutFactoryBeanPostProcessor.postProcessAfterInitialization(testBean1, beanName1);
        Object result2 = layoutFactoryBeanPostProcessor.postProcessAfterInitialization(testBean2, beanName2);

        // Then
        assertSame(testBean1, result1);
        assertSame(testBean2, result2);
        verify(layoutFactoryManager, times(2)).register(any());
        verify(componentFactoryManager, times(2)).register(any());
        verify(layoutFactoryManager).register(testBean1);
        verify(layoutFactoryManager).register(testBean2);
        verify(componentFactoryManager).register(testBean1);
        verify(componentFactoryManager).register(testBean2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试基本功能验证
     */
    @Test
    @DisplayName("正常场景 - 基本功能验证")
    void testBasicFunctionality() {
        // Then
        assertNotNull(layoutFactoryBeanPostProcessor);
        assertDoesNotThrow(() -> {
            layoutFactoryBeanPostProcessor.toString();
        });
        assertDoesNotThrow(() -> {
            layoutFactoryBeanPostProcessor.hashCode();
        });
        assertDoesNotThrow(() -> {
            layoutFactoryBeanPostProcessor.equals(layoutFactoryBeanPostProcessor);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试实现BeanPostProcessor接口的方法
     */
    @Test
    @DisplayName("正常场景 - 验证BeanPostProcessor接口实现")
    void testBeanPostProcessorInterface() {
        // Given
        Object testBean = new Object();
        String beanName = "testBean";

        // When & Then
        // 验证实现了BeanPostProcessor接口
        assertTrue(layoutFactoryBeanPostProcessor instanceof org.springframework.beans.factory.config.BeanPostProcessor);

        // 验证方法不抛出BeansException
        assertDoesNotThrow(() -> {
            layoutFactoryBeanPostProcessor.postProcessBeforeInitialization(testBean, beanName);
        });

        assertDoesNotThrow(() -> {
            layoutFactoryBeanPostProcessor.postProcessAfterInitialization(testBean, beanName);
        });
    }
}
