package com.facishare.paas.appframework.core.predef.domain;

import com.facishare.rest.core.annotation.*;

import java.util.Map;

/**
 * Created by zhouwr on 2024/05/21.
 */
@RestResource(
        value = "NCRM",
        desc = "列表页领域插件RPC代理类", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC"
)
public interface RelatedListControllerDomainPluginProxy {
    @POST(desc = "列表页页领域插件rest接口")
    RelatedListControllerDomainPlugin.RestResult post(@ServiceURLParam String url, @Body RelatedListControllerDomainPlugin.Arg arg, @HeaderMap Map<String, String> header);
}
