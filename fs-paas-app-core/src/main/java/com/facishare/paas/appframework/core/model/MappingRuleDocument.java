package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.metadata.api.IObjectArchiveRuleInfo;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.impl.ObjectMappingRuleInfo;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class MappingRuleDocument extends DocumentBaseEntity {
    public MappingRuleDocument() {
    }

    private MappingRuleDocument(Map<String, Object> data) {
        super(data);
    }

    public static MappingRuleDocument of(IObjectMappingRuleInfo objectMappingRuleInfo) {
        return new MappingRuleDocument(((ObjectMappingRuleInfo)objectMappingRuleInfo).getContainerDocument());
    }

    public static List<MappingRuleDocument> fromList(List<IObjectMappingRuleInfo> list) {
        if(CollectionUtils.empty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(MappingRuleDocument::of).collect(Collectors.toList());
    }

    public static List<MappingRuleDocument> fromArchiveList(List<IObjectArchiveRuleInfo> list) {
        if(CollectionUtils.empty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(MappingRuleDocument::of).collect(Collectors.toList());
    }
}
