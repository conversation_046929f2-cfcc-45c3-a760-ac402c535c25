package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/11/16
 */
public interface FindDescribeAndAbstractLayout {

    @Data
    class Arg {
        private String describeApiName;
        private String layoutApiName;
        private boolean noNeedReplaceI18n = false;
        private boolean removeI18n = false;
    }

    @Data
    @Builder
    class Result {
        private LayoutDocument layout;
        private ObjectDescribeDocument objectDescribe;
        private ObjectDescribeDocument describeExtra;
    }

    interface Support {
        @Data
        class Arg {
            private String describeApiName;
        }
        @Data
        @Builder
        class Result {
            private boolean gray;
        }
    }

    interface SupportFromObjApiNames {
        @Data
        class Arg {
            private List<String> describeApiNames;
        }
        @Data
        @Builder
        class Result {
            private List<Map<String, Object>> grayList;
        }
    }
}
