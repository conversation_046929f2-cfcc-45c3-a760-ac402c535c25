package com.facishare.paas.appframework.core.predef.service.dto.uniqueRule;

import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;

public interface GetSetting {

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Arg {
        private String apiName;
    }

    @Data
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Result {
        private IUniqueRule uniqueRule;
    }
}
