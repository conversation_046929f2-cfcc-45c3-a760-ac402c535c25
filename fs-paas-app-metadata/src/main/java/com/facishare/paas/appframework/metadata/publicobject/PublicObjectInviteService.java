package com.facishare.paas.appframework.metadata.publicobject;

import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobInfo;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobInvitationInfo;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/24
 */
public interface PublicObjectInviteService {
    void sendInvitationMessage(User user, PublicObjectJobInfo publicObjectJobInfo, String jobId);

    PublicObjectJobInvitationInfo findInvitationInfo(User user, IObjectDescribe describe, String token);

    InviteTokenInfo agreeInvitation(User user, IObjectDescribe describe, String token);

    InviteTokenInfo rejectInvitation(User user, IObjectDescribe describe, String token);

    @Data
    @NoArgsConstructor
    @AllArgsConstructor(staticName = "of")
    class InviteTokenInfo {
        private String upstreamTenantId;
        private String upstreamUserId;
        private String jobId;

        public static InviteTokenInfo fromJsonString(String json) {
            return JacksonUtils.fromJson(json, InviteTokenInfo.class);
        }

        public String toJsonString() {
            return JacksonUtils.toJson(this);
        }

        public User toUpstreamUser() {
            return new User(upstreamTenantId, upstreamUserId);
        }
    }
}
