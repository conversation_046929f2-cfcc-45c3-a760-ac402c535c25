package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.BatchGetEnterpriseCards;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

/**
 *
 * 深研服务
 *
 * <AUTHOR>
 * @date 2019/12/11 8:22 下午
 */
@RestResource(value = "ErBiz", desc = "深研服务", contentType = "application/json")
public interface ConnectionServiceProxy {
    @POST(value = "/outapi/enterpriseCard/batchGetEnterpriseCards", desc = "批量获取企业信息")
    BatchGetEnterpriseCards.Result batchGetEnterpriseCards(@HeaderParam("x-fs-ei") String tenantId,
                                                           @HeaderParam("x-eip-appid") String app,
                                                           @Body BatchGetEnterpriseCards.Arg arg);

}
