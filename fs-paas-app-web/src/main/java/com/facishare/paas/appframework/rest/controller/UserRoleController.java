package com.facishare.paas.appframework.rest.controller;

import com.facishare.crm.common.exception.CRMErrorCode;
import com.facishare.crm.common.exception.CrmCheckedException;
import com.facishare.crm.privilege.exception.PaasPrivilegeException;
import com.facishare.crm.privilege.service.ForbiddenUsersService;
import com.facishare.crm.privilege.service.FunctionPrivilegeOperateService;
import com.facishare.crm.privilege.swagger.ApiException;
import com.facishare.crm.privilege.util.ValidateUtil;
import com.facishare.crm.privilege.util.pb.ForbiddenResult;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.jaxrs.annotation.RestAPI;
import com.facishare.paas.appframework.metadata.exception.PrivilegeBusinessException;
import com.facishare.paas.appframework.privilege.UserRoleInfoProxy;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.appframework.privilege.dto.AuthContext;
import com.facishare.paas.appframework.privilege.dto.ForbiddenUsers;
import com.facishare.paas.appframework.privilege.dto.QueryRoleInfoListByUsersModel;
import com.facishare.paas.appframework.privilege.util.AppIdUtil;
import com.facishare.paas.appframework.rest.dto.data.*;
import com.facishare.paas.metadata.support.GDSHandler;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.*;
import java.util.Map;

import static com.facishare.paas.appframework.privilege.util.Headers.PAAS_PRIVILEGE_HEADDER;

/**
 * Created by luxin on 2018/8/7.
 */
@Controller
@Slf4j
@Path("/v1/inner/rest")
@RestAPI
public class UserRoleController {

    @Autowired
    private FunctionPrivilegeOperateService functionPrivilegeOperateService;
    @Autowired
    private ForbiddenUsersService forbiddenUsersService;
    @Autowired
    private UserRoleInfoProxy userRoleInfoProxy;
    @Autowired
    private GDSHandler gdsHandler;
    @Autowired
    private UserRoleInfoService userRoleInfoService;

    @Path("/user_role")
    @POST
    public Object batchAddUser2Role(@HeaderParam("x-fs-ea") String enterpriseAccount, UserRole.BatchAddUser2RoleArg arg) {
        RequestContext context = RequestContextManager.getContext();
        UserRole.BatchAddUser2RoleResult result = new UserRole.BatchAddUser2RoleResult();
        try {
            functionPrivilegeOperateService.addUser2Role(enterpriseAccount, context.getTenantId(), arg.getRoleCode(), arg.getUserIds());
            result.setCode(200);
            result.setMsg("success");
        } catch (CrmCheckedException e) {
            result.setCode(e.getIntErrorCode());
            result.setMsg(e.getMessage());
        } catch (ApiException e) {
            result.setCode(12);
            result.setMsg(e.getMessage());
        } catch (AppBusinessException e) {
            result.setCode(e.getErrorCode());
            result.setMsg(e.getMessage());
        }
        return result;
    }

    @POST
    @Path("/user_role/add_users")
    public UserRole.BatchAddUser2RoleResult addUser2Role(UserRole.BatchAddUser2RoleArg arg) {
        RequestContext context = RequestContextManager.getContext();
        String ea = gdsHandler.getEAByEI(context.getTenantId());
        try {
            functionPrivilegeOperateService.addUser2Role(ea, context.getTenantId(), arg.getRoleCode(), arg.getUserIds());
        } catch (ApiException e) {
            throw new PrivilegeBusinessException(e.getMessage(), CRMErrorCode.PAAS_ERROR.getCode());
        } catch (CrmCheckedException e) {
            throw new PrivilegeBusinessException(e.getMessage(), e.getIntErrorCode());
        }
        return new UserRole.BatchAddUser2RoleResult();
    }

    @POST
    @Path("/user_role/func")
    public AddFunc2UserRole.Result addFunc2UserDefinedRole(AddFunc2UserRole.Arg arg) {
        RequestContext context = RequestContextManager.getContext();
        if (!ValidateUtil.isValidParam(arg.getFuncCodes(), context.getTenantId(), arg.getRoleCode())) {
            throw new ValidateException(CRMErrorCode.PARAMETER_IS_WRONG.getMessage(), CRMErrorCode.PARAMETER_IS_WRONG.getCode());
        }

        try {
            functionPrivilegeOperateService.addDefaultFunc2Role(context.getTenantId(), arg.getRoleCode(), arg.getFuncCodes());
        } catch (ApiException e) {
            throw new PrivilegeBusinessException(e.getMessage(), CRMErrorCode.PAAS_ERROR.getCode());
        } catch (CrmCheckedException e) {
            throw new PrivilegeBusinessException(e.getMessage(), e.getIntErrorCode());
        }

        return new AddFunc2UserRole.Result();
    }

    @POST
    @Path("/user_role/create")
    public CreateUserDefinedRole.Result createUserDefinedRole(CreateUserDefinedRole.Arg arg) {
        RequestContext context = RequestContextManager.getContext();
        if (!ValidateUtil.isValidParam(context.getTenantId(), arg.getRoleName(), arg.getDescription())) {
            throw new ValidateException(CRMErrorCode.PARAMETER_IS_WRONG.getMessage(), CRMErrorCode.PARAMETER_IS_WRONG.getCode());
        }
        try {
            String roleCode = functionPrivilegeOperateService.createUserDefinedRole(context.getTenantId(), arg.getRoleName(), arg.getDescription());
            return CreateUserDefinedRole.Result.builder().roleCode(roleCode).build();
        } catch (PaasPrivilegeException e) {
            throw new PrivilegeBusinessException(e.getMessage(), e.getErrorCode().getCode());
        } catch (ApiException e) {
            throw new PrivilegeBusinessException(e.getMessage(), CRMErrorCode.PAAS_ERROR.getCode());
        } catch (CrmCheckedException e) {
            throw new PrivilegeBusinessException(e.getMessage(), e.getIntErrorCode());
        }
    }

    @POST
    @Path("/user_role/delete_users")
    public DeleteUserFromUserDefinedRole.Result deleteUserFromUserDefinedRole(DeleteUserFromUserDefinedRole.Arg arg) {
        RequestContext context = RequestContextManager.getContext();
        boolean isValidParam = ValidateUtil.isValidParam(arg.getUserIds(), context.getTenantId(), arg.getRoleCode());
        if (!isValidParam) {
            throw new ValidateException(CRMErrorCode.PARAMETER_IS_WRONG.getMessage(), CRMErrorCode.PARAMETER_IS_WRONG.getCode());
        }
        try {
            String ea = gdsHandler.getEAByEI(context.getTenantId());
            functionPrivilegeOperateService.deleteUserFromUserDefinedRole(ea, context.getTenantId(), arg.getRoleCode(), arg.getUserIds());
        } catch (ApiException e) {
            throw new PrivilegeBusinessException(e.getMessage(), CRMErrorCode.PAAS_ERROR.getCode());
        } catch (CrmCheckedException e) {
            throw new PrivilegeBusinessException(e.getMessage(), e.getIntErrorCode());
        }
        return new DeleteUserFromUserDefinedRole.Result();
    }

    @GET
    @Path("/user_role/exist_intelli_form")
    public ExistIntelliFormRole.Result existIntelliFormRole(@QueryParam("roleCode") String roleCode) {
        RequestContext context = RequestContextManager.getContext();
        if (!ValidateUtil.isValidParam(context.getTenantId())) {
            throw new ValidateException(CRMErrorCode.PARAMETER_IS_WRONG.getMessage(), CRMErrorCode.PARAMETER_IS_WRONG.getCode());
        }
        try {
            boolean existIntelliFormRole = functionPrivilegeOperateService.existRole(context.getTenantId(), roleCode);
            return ExistIntelliFormRole.Result.builder().existIntelliFormRole(existIntelliFormRole).build();
        } catch (ApiException e) {
            throw new PrivilegeBusinessException(e.getMessage(), CRMErrorCode.PAAS_ERROR.getCode());
        } catch (CrmCheckedException e) {
            throw new PrivilegeBusinessException(e.getMessage(), e.getIntErrorCode());
        }
    }


    @Path("/forbidden_user")
    @POST
    /**
     * 将员工添加到禁用员工名单里
     */
    public Object addUser2ForbiddenUserList(UserRole.BatchAddUser2ForbiddenUserListArg arg) {
        Map<String, Object> result = Maps.newHashMap();
        if (CollectionUtils.notEmpty(arg.getUserIds())) {
            RequestContext context = RequestContextManager.getContext();
            AuthContext authContext = getAuthContext(context);
            ForbiddenUsers.Arg arg1 = new ForbiddenUsers.Arg(authContext, arg.getUserIds(), true);
            ForbiddenResult<ForbiddenUsers.Result> resultForbiddenResult = forbiddenUsersService.updateForbiddenUsers(arg1);

            ForbiddenUsers.Result value = resultForbiddenResult.getValue();
            if (!value.getSuccess()) {
                result.put("success", false);
                result.put("errorMsg", value.getErrMessage());
                return result;
            }
        }
        result.put("success", true);
        return result;
    }

    private AuthContext getAuthContext(RequestContext context) {
        return AuthContext.builder().appId("CRM").tenantId(context.getTenantId()).userId(context.getUser().getUserId()).build();
    }


    @Path("/role_of_users")
    @POST
    public Object getRoleOfUsers(UserRole.RoleOfUsersArg arg) {
        RequestContext context = RequestContextManager.getContext();
        AuthContext authContext = buildAuthContext(context.getUser(), context.getAppId());

        QueryRoleInfoListByUsersModel.Arg queryRoleInfoListByUsersModelArg = new QueryRoleInfoListByUsersModel.Arg(authContext,
                arg.getUserIds(), Lists.newArrayListWithCapacity(0));
        QueryRoleInfoListByUsersModel.Result result =
                userRoleInfoProxy.queryRoleInfoListByUsers(queryRoleInfoListByUsersModelArg, PAAS_PRIVILEGE_HEADDER.defaultHeader());
        return result;
    }

    @Path("/is_admin")
    @GET
    public boolean isAdmin() {
        RequestContext context = RequestContextManager.getContext();
        boolean isAdmin = userRoleInfoService.isAdmin(context.getUser());
        return isAdmin;
    }

    private AuthContext buildAuthContext(User user, String appId) {
        return AuthContext.builder()
                .appId(AppIdUtil.getAppId(user, appId))
                .tenantId(user.getTenantId())
                .userId(user.getUserId())
                .outerTenantId(user.isOutUser() ? user.getOutTenantId() : null)
                .build();
    }


}
