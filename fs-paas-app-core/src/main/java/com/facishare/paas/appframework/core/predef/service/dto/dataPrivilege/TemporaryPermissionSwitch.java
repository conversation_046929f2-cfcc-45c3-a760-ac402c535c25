package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

public interface TemporaryPermissionSwitch {
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        @JSONField(name = "switch")
        @JsonProperty("switch")
        @SerializedName("switch")
        Boolean isActive;

        @JSONField(name = "level")
        @JsonProperty("level")
        @SerializedName("level")
        int level;

        @JSONField(name = "validity_term")
        @SerializedName(value = "validity_term")
        @JsonProperty(value = "validity_term")
        Integer validityTerm;

    }

    @Data
    @Builder
    class Result{
        private boolean success;
        private String errorInfo;
        private DataPrivilegeSwitchConfig.SwitchConfig configResult;
        private Boolean switchResult;
    }
}
