package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GenerateByAI
 * 测试BaseBatchFieldDataConverter抽象类的ID到名称映射逻辑
 */
class BaseBatchFieldDataConverterTest extends BaseDataConverterTest {

  private TestBaseBatchFieldDataConverter converter;

  @BeforeEach
  void setUp() {
    super.setUp();
    converter = new TestBaseBatchFieldDataConverter();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getId方法处理字符串类型的ID
   */
  @Test
  @DisplayName("测试getId - 字符串类型ID")
  void testGetId_StringValue() {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createEmployeeFieldDescribe("employee_field");
    IObjectData objectData = createObjectData("employee_field", "user123");

    // 执行被测试方法
    String result = converter.getId(objectData, fieldDescribe);

    // 验证结果
    assertEquals("user123", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getId方法处理数字类型的ID
   */
  @Test
  @DisplayName("测试getId - 数字类型ID")
  void testGetId_NumberValue() {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createEmployeeFieldDescribe("employee_field");
    IObjectData objectData = createObjectData("employee_field", 12345);

    // 执行被测试方法
    String result = converter.getId(objectData, fieldDescribe);

    // 验证结果
    assertEquals("12345", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getId方法处理List类型的ID（取第一个元素）
   */
  @ParameterizedTest
  @MethodSource("provideListValueTestData")
  @DisplayName("测试getId - List类型ID取第一个元素")
  void testGetId_ListValue(List<?> listValue, String expectedId) {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createEmployeeManyFieldDescribe("employee_many_field");
    IObjectData objectData = createObjectData("employee_many_field", listValue);

    // 执行被测试方法
    String result = converter.getId(objectData, fieldDescribe);

    // 验证结果
    assertEquals(expectedId, result);
  }

  /**
   * 提供List值测试数据
   */
  private static Stream<Arguments> provideListValueTestData() {
    return Stream.of(
        Arguments.of(Arrays.asList("user1", "user2", "user3"), "user1"),
        Arguments.of(Arrays.asList(123, 456, 789), "123"),
        Arguments.of(Arrays.asList("single_user"), "single_user")
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convert方法的完整流程
   */
  @Test
  @DisplayName("测试convert - 完整ID到名称映射流程")
  void testConvert_FullIdToNameMapping() {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createEmployeeFieldDescribe("employee_field");
    List<IObjectData> dataList = Arrays.asList(
        createObjectData("employee_field", "user1"),
        createObjectData("employee_field", "user2"),
        createObjectData("employee_field", "user1"), // 重复ID
        createObjectData("employee_field", "user3")
    );

    // 执行被测试方法
    converter.convert(dataList, fieldDescribe, context);

    // 验证结果
    assertEquals("张三", dataList.get(0).get("employee_field"));
    assertEquals("李四", dataList.get(1).get("employee_field"));
    assertEquals("张三", dataList.get(2).get("employee_field")); // 重复ID应该有相同的名称
    assertEquals("王五", dataList.get(3).get("employee_field"));

    // 验证getIdNameMap被调用时传入的参数
    List<String> receivedIdList = converter.getReceivedIdList();
    assertEquals(3, receivedIdList.size()); // 去重后应该只有3个ID
    assertTrue(receivedIdList.contains("user1"));
    assertTrue(receivedIdList.contains("user2"));
    assertTrue(receivedIdList.contains("user3"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convert方法处理List类型的员工字段
   */
  @Test
  @DisplayName("测试convert - List类型员工字段")
  void testConvert_ListTypeEmployeeField() {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createEmployeeManyFieldDescribe("employee_many_field");
    List<IObjectData> dataList = Arrays.asList(
        createObjectData("employee_many_field", Arrays.asList("user1", "user2")),
        createObjectData("employee_many_field", Arrays.asList("user3"))
    );

    // 执行被测试方法
    converter.convert(dataList, fieldDescribe, context);

    // 验证结果 - 注意：convert方法只处理第一个ID
    assertEquals("张三", dataList.get(0).get("employee_many_field"));
    assertEquals("王五", dataList.get(1).get("employee_many_field"));

    // 验证传入getIdNameMap的ID列表
    List<String> receivedIdList = converter.getReceivedIdList();
    assertEquals(2, receivedIdList.size());
    assertTrue(receivedIdList.contains("user1")); // 只取List的第一个元素
    assertTrue(receivedIdList.contains("user3"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convert方法处理空的ID名称映射
   */
  @Test
  @DisplayName("测试convert - 空的ID名称映射")
  void testConvert_EmptyIdNameMapping() {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createEmployeeFieldDescribe("employee_field");
    List<IObjectData> dataList = Arrays.asList(
        createObjectData("employee_field", "unknown_user")
    );

    // 设置转换器返回空映射
    converter.setReturnEmptyMapping(true);

    // 执行被测试方法
    converter.convert(dataList, fieldDescribe, context);

    // 验证结果 - 当映射为空时，应该设置为null
    assertNull(dataList.get(0).get("employee_field"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convert方法传递正确的用户信息
   */
  @Test
  @DisplayName("测试convert - 用户信息传递验证")
  void testConvert_UserParameterPassing() {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createEmployeeFieldDescribe("employee_field");
    List<IObjectData> dataList = Arrays.asList(createObjectData("employee_field", "user1"));

    // 执行被测试方法
    converter.convert(dataList, fieldDescribe, context);

    // 验证用户信息传递
    assertEquals(user, converter.getReceivedUser());
    assertEquals(fieldDescribe, converter.getReceivedFieldDescribe());
  }

  /**
   * 测试用的BaseBatchFieldDataConverter实现类
   */
  private static class TestBaseBatchFieldDataConverter extends BaseBatchFieldDataConverter {
    private List<String> receivedIdList;
    private IFieldDescribe receivedFieldDescribe;
    private User receivedUser;
    private boolean returnEmptyMapping = false;

    @Override
    protected Map<String, String> getIdNameMap(List<String> idList, IFieldDescribe fieldDescribe, User user) {
      this.receivedIdList = idList;
      this.receivedFieldDescribe = fieldDescribe;
      this.receivedUser = user;

      if (returnEmptyMapping) {
        return new HashMap<>();
      }

      // 模拟ID到名称的映射
      Map<String, String> mapping = new HashMap<>();
      mapping.put("user1", "张三");
      mapping.put("user2", "李四");
      mapping.put("user3", "王五");
      return mapping;
    }

    @Override
    public List<String> getSupportedFieldTypes() {
      return Arrays.asList(IFieldType.EMPLOYEE, IFieldType.EMPLOYEE_MANY);
    }

    public List<String> getReceivedIdList() {
      return receivedIdList;
    }

    public IFieldDescribe getReceivedFieldDescribe() {
      return receivedFieldDescribe;
    }

    public User getReceivedUser() {
      return receivedUser;
    }

    public void setReturnEmptyMapping(boolean returnEmptyMapping) {
      this.returnEmptyMapping = returnEmptyMapping;
    }
  }
}
