package com.facishare.paas.appframework.metadata.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonFilterField {
    @JSONField(name = "field_list")
    List<String> fieldList;

    @JSONField(name = "field_list_new")
    List<FilterField> fieldListNew;

    public static CommonFilterField fromMap(Map filterFields) {
        if (CollectionUtils.empty(filterFields)) {
            return CommonFilterField.builder().fieldList(Lists.newArrayList()).build();
        }

        return JSON.parseObject(JSON.toJSONString(filterFields), CommonFilterField.class);
    }

    public Map toMap() {

        return JSON.parseObject(JSON.toJSONString(this), Map.class);
    }

    @JSONField(serialize = false)
    public boolean isEmpty() {
        return CollectionUtils.empty(fieldList);
    }

    @JSONField(serialize = false)
    public boolean isFieldListNull() {
        return Objects.isNull(fieldList) && Objects.isNull(fieldListNew);
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FilterField {
        @JSONField(name = "field_name")
        @JsonProperty("field_name")
        @SerializedName("field_name")
        String fieldName;

        @JSONField(name = "is_show")
        @JsonProperty("is_show")
        @SerializedName("is_show")
        Boolean isShow;

        @JSONField(name = "enable_quick_filter")
        @JsonProperty("enable_quick_filter")
        @SerializedName("enable_quick_filter")
        Boolean enableQuickFilter;
    }

}
