package com.facishare.paas.appframework.license.quota;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.dto.ModuleParaLicense;
import com.facishare.paas.appframework.license.exception.LicenseException;
import com.facishare.paas.appframework.license.util.LicenseConstants;
import com.facishare.paas.appframework.license.util.ModulePara;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.impl.I18nServiceImpl;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;


import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TenantLicenseInfoTest {

    @Mock
    private LicenseService licenseService;
    
    @Mock
    private IObjectDescribe objectDescribe;
    
    @Mock
    private I18nClient i18nClient;
    
    @Mock
    private I18nServiceImpl i18nServiceImpl;
    
    private TenantLicenseInfo tenantLicenseInfo;
    private User user;

    @BeforeEach
    void setUp() {
        user = User.systemUser("123456");
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证模块解析功能能正确工作
     */
    @Test
    @DisplayName("正常场景 - 模块解析应该返回正确的模块映射")
    void testModuleParsing_ShouldReturnCorrectModuleMap() {
        // 准备测试数据
        Map<String, List<ModuleParaLicense>> moduleParaMap = createModuleParaMap();
        when(licenseService.batchGetModuleLicenses(any(User.class), any(Map.class))).thenReturn(moduleParaMap);
        
        // 创建测试对象
        tenantLicenseInfo = TenantLicenseInfo.builder()
            .user(user)
            .licenseService(licenseService)
            .build()
            .init(Sets.newHashSet(
                ModulePara.Module.CUSTOM_OBJECT.getModuleCode(),
                ModulePara.Module.BIG_OBJECT.getModuleCode(),
                ModulePara.Module.FIELD_INFO.getModuleCode(),
                ModulePara.ModuleBiz.MASTER_DETAIL.getBizCode()
            ));
        
        // 验证结果 - 测试对象创建成功
        assertNotNull(tenantLicenseInfo);
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证checkDescribeCount方法在数量未超过限制时不抛出异常
     */
    @Test
    @DisplayName("正常场景 - 检查描述数量未超过限制应该通过")
    void testCheckDescribeCount_WithinLimit_ShouldNotThrowException() {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class);
             MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            
            // 配置静态方法Mock
            mockedI18N.when(() -> I18N.text(anyString())).thenReturn("Error Message");
            mockedConfig.when(() -> AppFrameworkConfig.supportMoreFields(anyString())).thenReturn(true);
            
            // 准备测试数据
            Map<String, List<ModuleParaLicense>> moduleParaMap = createModuleParaMap();
            when(licenseService.batchGetModuleLicenses(any(User.class), any(Map.class))).thenReturn(moduleParaMap);
            
            // 创建测试对象
            tenantLicenseInfo = TenantLicenseInfo.builder()
                .user(user)
                .licenseService(licenseService)
                .build()
                .init(Sets.newHashSet(
                    ModulePara.Module.CUSTOM_OBJECT.getModuleCode(),
                    ModulePara.Module.BIG_OBJECT.getModuleCode(),
                    ModulePara.Module.FIELD_INFO.getModuleCode(),
                    ModulePara.ModuleBiz.MASTER_DETAIL.getBizCode()
                ));
            
            // 执行被测试方法 - 数量在限制内
            assertDoesNotThrow(() -> tenantLicenseInfo.checkDescribeCount(40, false, false));
            
            // 执行被测试方法 - 大对象数量在限制内
            assertDoesNotThrow(() -> tenantLicenseInfo.checkDescribeCount(15, true, false));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证checkDescribeCount方法在数量超过限制时抛出LicenseException
     */
    @Test
    @DisplayName("异常场景 - 检查描述数量超过限制应该抛出LicenseException")
    void testCheckDescribeCountThrowsLicenseException_WhenExceedsLimit() {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class);
             MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            
            // 配置静态方法Mock
            mockedI18N.when(() -> I18N.text(anyString())).thenReturn("Error Message");
            mockedConfig.when(() -> AppFrameworkConfig.supportMoreFields(anyString())).thenReturn(true);
            
            // 准备测试数据
            Map<String, List<ModuleParaLicense>> moduleParaMap = createModuleParaMap();
            when(licenseService.batchGetModuleLicenses(any(User.class), any(Map.class))).thenReturn(moduleParaMap);
            
            // 创建测试对象
            tenantLicenseInfo = TenantLicenseInfo.builder()
                .user(user)
                .licenseService(licenseService)
                .build()
                .init(Sets.newHashSet(
                    ModulePara.Module.CUSTOM_OBJECT.getModuleCode(),
                    ModulePara.Module.BIG_OBJECT.getModuleCode(),
                    ModulePara.Module.FIELD_INFO.getModuleCode(),
                    ModulePara.ModuleBiz.MASTER_DETAIL.getBizCode()
                ));
            
            // 执行并验证异常 - 普通对象数量超过限制
            assertThrows(LicenseException.class, () -> tenantLicenseInfo.checkDescribeCount(60, false, false));
            
            // 执行并验证异常 - 大对象数量超过限制
            assertThrows(LicenseException.class, () -> tenantLicenseInfo.checkDescribeCount(25, true, false));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证usableDescribeCount方法能正确计算可用的描述数量
     */
    @Test
    @DisplayName("正常场景 - 计算可用描述数量应该返回正确结果")
    void testUsableDescribeCount_ShouldReturnCorrectCount() {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class);
             MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            
            // 配置静态方法Mock
            mockedI18N.when(() -> I18N.text(anyString())).thenReturn("Error Message");
            mockedConfig.when(() -> AppFrameworkConfig.supportMoreFields(anyString())).thenReturn(true);
            
            // 准备测试数据
            Map<String, List<ModuleParaLicense>> moduleParaMap = createModuleParaMap();
            when(licenseService.batchGetModuleLicenses(any(User.class), any(Map.class))).thenReturn(moduleParaMap);
            
            // 创建测试对象
            tenantLicenseInfo = TenantLicenseInfo.builder()
                .user(user)
                .licenseService(licenseService)
                .build()
                .init(Sets.newHashSet(
                    ModulePara.Module.CUSTOM_OBJECT.getModuleCode(),
                    ModulePara.Module.BIG_OBJECT.getModuleCode(),
                    ModulePara.Module.FIELD_INFO.getModuleCode(),
                    ModulePara.ModuleBiz.MASTER_DETAIL.getBizCode()
                ));
            
            // 执行被测试方法
            int usableCount = tenantLicenseInfo.usableDescribeCount(20);
            
            // 验证结果 - 50 (max) - 20 (current) = 30
            assertEquals(30, usableCount);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证checkFieldCount方法在字段数量未超过限制时不抛出异常
     */
    @Test
    @DisplayName("正常场景 - 检查字段数量未超过限制应该通过")
    void testCheckFieldCount_WithinLimit_ShouldNotThrowException() {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class);
             MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            
            // 配置静态方法Mock
            mockedI18N.when(() -> I18N.text(anyString())).thenReturn("Error Message");
            mockedConfig.when(() -> AppFrameworkConfig.supportMoreFields(anyString())).thenReturn(true);
            
            // 准备测试数据
            Map<String, List<ModuleParaLicense>> moduleParaMap = createModuleParaMap();
            when(licenseService.batchGetModuleLicenses(any(User.class), any(Map.class))).thenReturn(moduleParaMap);
            
            // 创建测试对象
            tenantLicenseInfo = TenantLicenseInfo.builder()
                .user(user)
                .licenseService(licenseService)
                .build()
                .init(Sets.newHashSet(
                    ModulePara.Module.CUSTOM_OBJECT.getModuleCode(),
                    ModulePara.Module.BIG_OBJECT.getModuleCode(),
                    ModulePara.Module.FIELD_INFO.getModuleCode(),
                    ModulePara.ModuleBiz.MASTER_DETAIL.getBizCode()
                ));
            
            // 准备字段映射 - 数量在限制内
            Map<String, Integer> fieldMap = Maps.newHashMap();
            fieldMap.put(IFieldType.OBJECT_REFERENCE, 5); // 低于限制10
            
            // 执行被测试方法
            assertDoesNotThrow(() -> tenantLicenseInfo.checkFieldCount(fieldMap, objectDescribe));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证checkFieldCount方法在字段数量超过限制时抛出LicenseException
     */
    @Test
    @DisplayName("异常场景 - 检查字段数量超过限制应该抛出LicenseException")
    void testCheckFieldCountThrowsLicenseException_WhenExceedsLimit() {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class);
             MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            
            // 配置静态方法Mock
            mockedI18N.when(() -> I18N.text(anyString())).thenReturn("Error Message");
            mockedConfig.when(() -> AppFrameworkConfig.supportMoreFields(anyString())).thenReturn(true);
            
            // 准备测试数据
            Map<String, List<ModuleParaLicense>> moduleParaMap = createModuleParaMap();
            when(licenseService.batchGetModuleLicenses(any(User.class), any(Map.class))).thenReturn(moduleParaMap);
            
            // 创建测试对象
            tenantLicenseInfo = TenantLicenseInfo.builder()
                .user(user)
                .licenseService(licenseService)
                .build()
                .init(Sets.newHashSet(
                    ModulePara.Module.CUSTOM_OBJECT.getModuleCode(),
                    ModulePara.Module.BIG_OBJECT.getModuleCode(),
                    ModulePara.Module.FIELD_INFO.getModuleCode(),
                    ModulePara.ModuleBiz.MASTER_DETAIL.getBizCode()
                ));
            
            // 准备字段映射 - 数量超过限制
            Map<String, Integer> exceededFieldMap = Maps.newHashMap();
            exceededFieldMap.put(IFieldType.OBJECT_REFERENCE, 15); // 超过限制10
            
            // 执行并验证异常
            assertThrows(LicenseException.class, () -> tenantLicenseInfo.checkFieldCount(exceededFieldMap, objectDescribe));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证getNeedToCheckQuotaFieldType方法返回正确的需要检查配额的字段类型
     */
    @Test
    @DisplayName("正常场景 - 获取需要检查配额的字段类型应该返回正确列表")
    void testGetNeedToCheckQuotaFieldType_ShouldReturnCorrectFieldTypes() {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class);
             MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            
            // 配置静态方法Mock
            mockedI18N.when(() -> I18N.text(anyString())).thenReturn("Error Message");
            mockedConfig.when(() -> AppFrameworkConfig.supportMoreFields(anyString())).thenReturn(true);
            
            // 准备测试数据
            Map<String, List<ModuleParaLicense>> moduleParaMap = createModuleParaMap();
            when(licenseService.batchGetModuleLicenses(any(User.class), any(Map.class))).thenReturn(moduleParaMap);
            
            // 创建测试对象
            tenantLicenseInfo = TenantLicenseInfo.builder()
                .user(user)
                .licenseService(licenseService)
                .build()
                .init(Sets.newHashSet(
                    ModulePara.Module.CUSTOM_OBJECT.getModuleCode(),
                    ModulePara.Module.BIG_OBJECT.getModuleCode(),
                    ModulePara.Module.FIELD_INFO.getModuleCode(),
                    ModulePara.ModuleBiz.MASTER_DETAIL.getBizCode()
                ));
            
            // 执行被测试方法
            List<String> fieldTypes = tenantLicenseInfo.getNeedToCheckQuotaFieldType();
            
            // 验证结果
            assertTrue(fieldTypes.contains(IFieldType.OBJECT_REFERENCE));
            assertTrue(fieldTypes.contains(IFieldType.FORMULA));
            assertTrue(fieldTypes.contains(IFieldType.AUTO_NUMBER));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证getWithoutQuotaFieldType方法返回正确的无配额字段类型
     */
    @Test
    @DisplayName("正常场景 - 获取无配额字段类型应该返回正确列表")
    void testGetWithoutQuotaFieldType_ShouldReturnCorrectFieldTypes() {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class);
             MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            
            // 配置静态方法Mock
            mockedI18N.when(() -> I18N.text(anyString())).thenReturn("Error Message");
            mockedConfig.when(() -> AppFrameworkConfig.supportMoreFields(anyString())).thenReturn(true);
            
            // 准备测试数据
            Map<String, List<ModuleParaLicense>> moduleParaMap = createModuleParaMap();
            when(licenseService.batchGetModuleLicenses(any(User.class), any(Map.class))).thenReturn(moduleParaMap);
            
            // 创建测试对象
            tenantLicenseInfo = TenantLicenseInfo.builder()
                .user(user)
                .licenseService(licenseService)
                .build()
                .init(Sets.newHashSet(
                    ModulePara.Module.CUSTOM_OBJECT.getModuleCode(),
                    ModulePara.Module.BIG_OBJECT.getModuleCode(),
                    ModulePara.Module.FIELD_INFO.getModuleCode(),
                    ModulePara.ModuleBiz.MASTER_DETAIL.getBizCode()
                ));
            
            // 执行被测试方法
            List<String> fieldTypes = tenantLicenseInfo.getWithoutQuotaFieldType();
            
            // 验证结果
            assertTrue(fieldTypes.contains(IFieldType.MASTER_DETAIL));
            assertTrue(fieldTypes.contains(IFieldType.HTML_RICH_TEXT));
            assertTrue(fieldTypes.contains("group|sign_in"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证checkEntityShareCount方法在实体共享数量未超过限制时不抛出异常
     */
    @Test
    @DisplayName("正常场景 - 检查实体共享数量未超过限制应该通过")
    void testCheckEntityShareCount_WithinLimit_ShouldNotThrowException() {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class);
             MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            
            // 配置静态方法Mock
            mockedI18N.when(() -> I18N.text(anyString())).thenReturn("Error Message");
            mockedConfig.when(() -> AppFrameworkConfig.supportMoreFields(anyString())).thenReturn(true);
            
            // 准备数据角色模块参数许可证
            List<ModuleParaLicense> dataRoleLicenses = Arrays.asList(
                createModuleParaLicense(ModulePara.DATA_SHARE_RULES_LIMIT.getParaKey(), "50")
            );
            Map<String, List<ModuleParaLicense>> moduleParaMap = Maps.newHashMap();
            moduleParaMap.put(LicenseConstants.ModuleCode.DATA_ROLES, dataRoleLicenses);
            
            when(licenseService.batchGetModuleLicenses(any(User.class), any(Map.class))).thenReturn(moduleParaMap);
            
            // 创建测试对象
            tenantLicenseInfo = TenantLicenseInfo.builder()
                .user(user)
                .licenseService(licenseService)
                .build()
                .init(Sets.newHashSet(ModulePara.ModuleBiz.DATA_ROLE.getBizCode()));
            
            // 执行被测试方法 - 数量在限制内
            assertDoesNotThrow(() -> tenantLicenseInfo.checkEntityShareCount(40));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证checkEntityShareCount方法在实体共享数量超过限制时抛出LicenseException
     */
    @Test
    @DisplayName("异常场景 - 检查实体共享数量超过限制应该抛出LicenseException")
    void testCheckEntityShareCountThrowsLicenseException_WhenExceedsLimit() {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class);
             MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            
            // 配置静态方法Mock
            mockedI18N.when(() -> I18N.text(anyString())).thenReturn("Error Message");
            mockedConfig.when(() -> AppFrameworkConfig.supportMoreFields(anyString())).thenReturn(true);
            
            // 准备数据角色模块参数许可证
            List<ModuleParaLicense> dataRoleLicenses = Arrays.asList(
                createModuleParaLicense(ModulePara.DATA_SHARE_RULES_LIMIT.getParaKey(), "50")
            );
            Map<String, List<ModuleParaLicense>> moduleParaMap = Maps.newHashMap();
            moduleParaMap.put(LicenseConstants.ModuleCode.DATA_ROLES, dataRoleLicenses);
            
            when(licenseService.batchGetModuleLicenses(any(User.class), any(Map.class))).thenReturn(moduleParaMap);
            
            // 创建测试对象
            tenantLicenseInfo = TenantLicenseInfo.builder()
                .user(user)
                .licenseService(licenseService)
                .build()
                .init(Sets.newHashSet(ModulePara.ModuleBiz.DATA_ROLE.getBizCode()));
            
            // 执行并验证异常 - 数量超过限制
            assertThrows(LicenseException.class, () -> tenantLicenseInfo.checkEntityShareCount(60));
        }
    }

    /**
     * 创建模块参数映射的辅助方法
     */
    private Map<String, List<ModuleParaLicense>> createModuleParaMap() {
        Map<String, List<ModuleParaLicense>> moduleParaMap = Maps.newHashMap();
        
        // 设置自定义对象模块
        List<ModuleParaLicense> customObjectLicenses = Arrays.asList(
            createModuleParaLicense(ModulePara.CUSTOM_OBJECTS_LIMIT.getParaKey(), "50")
        );
        moduleParaMap.put(LicenseConstants.ModuleCode.CUSTOM_OBJECT, customObjectLicenses);
        
        // 设置大对象模块
        List<ModuleParaLicense> bigObjectLicenses = Arrays.asList(
            createModuleParaLicense(ModulePara.CUSTOM_BIG_OBJECTS_LIMIT.getParaKey(), "20")
        );
        moduleParaMap.put(LicenseConstants.ModuleCode.BIG_OBJECT_APP, bigObjectLicenses);
        
        // 设置字段信息模块
        List<ModuleParaLicense> fieldInfoLicenses = Arrays.asList(
            createModuleParaLicense(ModulePara.REFERENCE_FIELD_LIMIT.getParaKey(), "10"),
            createModuleParaLicense(ModulePara.QUOTE_FIELD_LIMIT.getParaKey(), "5")
        );
        moduleParaMap.put(LicenseConstants.ModuleCode.FIELD_INFO, fieldInfoLicenses);
        
        // 设置主从详情模块
        List<ModuleParaLicense> masterDetailLicenses = Arrays.asList(
            createModuleParaLicense(ModulePara.MASTER_DETAIL_OBJECT_LIMIT.getParaKey(), "5")
        );
        moduleParaMap.put(LicenseConstants.ModuleCode.MASTER_DETAIL_OBJECT, masterDetailLicenses);
        
        return moduleParaMap;
    }
    
    /**
     * 创建模块参数许可证的辅助方法
     */
    private ModuleParaLicense createModuleParaLicense(String paraKey, String paraValue) {
        ModuleParaLicense license = new ModuleParaLicense();
        license.setParaKey(paraKey);
        license.setParaValue(paraValue);
        return license;
    }
} 