package com.facishare.paas.appframework.core.timezone

import com.facishare.paas.appframework.core.timezone.annotation.ObjectDataField
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import spock.lang.Specification

import java.util.function.Function

/**
 * GenerateByAI
 * 测试内容描述：ObjectDataFieldConvert接口的单元测试
 */
class ObjectDataFieldConvertTest extends Specification {

    // 测试用的实现类
    static class TestObjectDataFieldConvert implements ObjectDataFieldConvert {
        private ObjectDataField.Type type
        private boolean systemZoneCalled = false
        private boolean customZoneCalled = false
        private Object lastValue
        private Function lastFunction

        TestObjectDataFieldConvert(ObjectDataField.Type type) {
            this.type = type
        }

        @Override
        ObjectDataField.Type getType() {
            return type
        }

        @Override
        def convert2SystemZone(value, Function function) {
            systemZoneCalled = true
            lastValue = value
            lastFunction = function
            return value
        }

        @Override
        def convert2CustomZone(value, Function function) {
            customZoneCalled = true
            lastValue = value
            lastFunction = function
            return value
        }

        // 测试辅助方法
        boolean isSystemZoneCalled() { return systemZoneCalled }
        boolean isCustomZoneCalled() { return customZoneCalled }
        Object getLastValue() { return lastValue }
        Function getLastFunction() { return lastFunction }
        void reset() {
            systemZoneCalled = false
            customZoneCalled = false
            lastValue = null
            lastFunction = null
        }
    }

    def "test interface contract - getType method"() {
        given: "创建测试实现"
        def converter = new TestObjectDataFieldConvert(ObjectDataField.Type.SINGLE)

        when: "调用getType方法"
        def type = converter.getType()

        then: "应该返回正确的类型"
        type == ObjectDataField.Type.SINGLE
    }

    def "test interface contract - convert2SystemZone method"() {
        given: "创建测试实现和测试数据"
        def converter = new TestObjectDataFieldConvert(ObjectDataField.Type.LIST)
        def testValue = "test_value"
        def mockFunction = Mock(Function)

        when: "调用convert2SystemZone方法"
        def result = converter.convert2SystemZone(testValue, mockFunction)

        then: "应该正确调用实现"
        converter.isSystemZoneCalled()
        !converter.isCustomZoneCalled()
        converter.getLastValue() == testValue
        converter.getLastFunction() == mockFunction
        result == testValue
    }

    def "test interface contract - convert2CustomZone method"() {
        given: "创建测试实现和测试数据"
        def converter = new TestObjectDataFieldConvert(ObjectDataField.Type.LIST_MAP)
        def testValue = ["key": "value"]
        def mockFunction = Mock(Function)

        when: "调用convert2CustomZone方法"
        def result = converter.convert2CustomZone(testValue, mockFunction)

        then: "应该正确调用实现"
        !converter.isSystemZoneCalled()
        converter.isCustomZoneCalled()
        converter.getLastValue() == testValue
        converter.getLastFunction() == mockFunction
        result == testValue
    }

    def "test interface with different types"() {
        given: "创建不同类型的转换器"
        def singleConverter = new TestObjectDataFieldConvert(ObjectDataField.Type.SINGLE)
        def listConverter = new TestObjectDataFieldConvert(ObjectDataField.Type.LIST)
        def listMapConverter = new TestObjectDataFieldConvert(ObjectDataField.Type.LIST_MAP)
        def mapMapConverter = new TestObjectDataFieldConvert(ObjectDataField.Type.MAP_MAP)

        expect: "每个转换器都应该返回正确的类型"
        singleConverter.getType() == ObjectDataField.Type.SINGLE
        listConverter.getType() == ObjectDataField.Type.LIST
        listMapConverter.getType() == ObjectDataField.Type.LIST_MAP
        mapMapConverter.getType() == ObjectDataField.Type.MAP_MAP
    }

    def "test interface with null values"() {
        given: "创建测试实现"
        def converter = new TestObjectDataFieldConvert(ObjectDataField.Type.SINGLE)
        def mockFunction = Mock(Function)

        when: "使用null值调用convert2SystemZone"
        def result1 = converter.convert2SystemZone(null, mockFunction)

        then: "应该正确处理null值"
        converter.isSystemZoneCalled()
        converter.getLastValue() == null
        result1 == null

        when: "重置并使用null值调用convert2CustomZone"
        converter.reset()
        def result2 = converter.convert2CustomZone(null, mockFunction)

        then: "应该正确处理null值"
        converter.isCustomZoneCalled()
        converter.getLastValue() == null
        result2 == null
    }

    def "test interface with different value types"() {
        given: "创建测试实现"
        def converter = new TestObjectDataFieldConvert(ObjectDataField.Type.SINGLE)
        def mockFunction = Mock(Function)

        when: "使用字符串值"
        def stringResult = converter.convert2SystemZone("test", mockFunction)

        then: "应该正确处理字符串"
        stringResult == "test"
        converter.getLastValue() == "test"

        when: "重置并使用Map值"
        converter.reset()
        def mapValue = ["key": "value"]
        def mapResult = converter.convert2SystemZone(mapValue, mockFunction)

        then: "应该正确处理Map"
        mapResult == mapValue
        converter.getLastValue() == mapValue

        when: "重置并使用List值"
        converter.reset()
        def listValue = ["item1", "item2"]
        def listResult = converter.convert2SystemZone(listValue, mockFunction)

        then: "应该正确处理List"
        listResult == listValue
        converter.getLastValue() == listValue
    }

    def "test interface method chaining"() {
        given: "创建测试实现"
        def converter = new TestObjectDataFieldConvert(ObjectDataField.Type.SINGLE)
        def mockFunction = Mock(Function)
        def testValue = "test"

        when: "链式调用转换方法"
        def result = converter.convert2SystemZone(
                converter.convert2CustomZone(testValue, mockFunction),
                mockFunction
        )

        then: "应该正确执行链式调用"
        result == testValue
        converter.isSystemZoneCalled()
        converter.isCustomZoneCalled()
    }

    def "test interface with generic types"() {
        given: "创建测试实现"
        def converter = new TestObjectDataFieldConvert(ObjectDataField.Type.SINGLE)
        def mockFunction = Mock(Function)

        when: "使用泛型类型"
        String stringResult = converter.convert2SystemZone("test", mockFunction)
        Map<String, String> mapResult = converter.convert2SystemZone(["key": "value"], mockFunction)
        List<String> listResult = converter.convert2SystemZone(["item"], mockFunction)

        then: "应该保持类型安全"
        stringResult instanceof String
        mapResult instanceof Map
        listResult instanceof List
        stringResult == "test"
        mapResult == ["key": "value"]
        listResult == ["item"]
    }

    def "test interface implementation consistency"() {
        given: "创建多个相同类型的转换器"
        def converter1 = new TestObjectDataFieldConvert(ObjectDataField.Type.SINGLE)
        def converter2 = new TestObjectDataFieldConvert(ObjectDataField.Type.SINGLE)
        def mockFunction = Mock(Function)
        def testValue = "test"

        when: "使用相同参数调用两个转换器"
        def result1 = converter1.convert2SystemZone(testValue, mockFunction)
        def result2 = converter2.convert2SystemZone(testValue, mockFunction)

        then: "应该产生一致的结果"
        result1 == result2
        converter1.getType() == converter2.getType()
    }

    def "test interface with complex objects"() {
        given: "创建测试实现和复杂对象"
        def converter = new TestObjectDataFieldConvert(ObjectDataField.Type.LIST_MAP)
        def mockFunction = Mock(Function)
        def complexObject = [
            "api1": [
                ["id": "1", "name": "item1"],
                ["id": "2", "name": "item2"]
            ],
            "api2": [
                ["id": "3", "name": "item3"]
            ]
        ]

        when: "转换复杂对象"
        def result = converter.convert2SystemZone(complexObject, mockFunction)

        then: "应该正确处理复杂对象"
        result == complexObject
        converter.getLastValue() == complexObject
        converter.isSystemZoneCalled()
    }

    def "test interface error handling"() {
        given: "创建会抛出异常的实现"
        def errorConverter = new ObjectDataFieldConvert() {
            @Override
            ObjectDataField.Type getType() {
                return ObjectDataField.Type.SINGLE
            }

            @Override
            def convert2SystemZone(value, Function function) {
                throw new RuntimeException("System zone conversion failed")
            }

            @Override
            def convert2CustomZone(value, Function function) {
                throw new IllegalArgumentException("Custom zone conversion failed")
            }
        }
        def mockFunction = Mock(Function)

        when: "调用convert2SystemZone"
        errorConverter.convert2SystemZone("test", mockFunction)

        then: "应该抛出RuntimeException"
        thrown(RuntimeException)

        when: "调用convert2CustomZone"
        errorConverter.convert2CustomZone("test", mockFunction)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test interface with concurrent access"() {
        given: "创建测试实现"
        def converter = new TestObjectDataFieldConvert(ObjectDataField.Type.SINGLE)
        def mockFunction = Mock(Function)
        def results = []

        when: "并发调用转换方法"
        def threads = (1..10).collect { i ->
            Thread.start {
                def result = converter.convert2SystemZone("test$i", mockFunction)
                synchronized (results) {
                    results << result
                }
            }
        }
        threads.each { it.join() }

        then: "应该正确处理并发访问"
        results.size() == 10
        results.every { it.startsWith("test") }
    }
}
