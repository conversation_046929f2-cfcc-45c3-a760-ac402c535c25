package com.facishare.paas.appframework.metadata.publicobject.dto;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.service.dto.QueryTenantGroupByIds;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.DataVisibilityRange;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/13
 */
@Data
public class ConnectedEnterpriseDTO {
    public static final String TYPE_TENANT = "tenant";
    public static final String TYPE_TENANT_GROUP = "tenant_group";
    public static final String TYPE_TENANT_GROUP_PREFIX = "tg_";


    /**
     * tenant 互联企业
     * tenant_group 互联企业组
     */
    private String type;
    private String id;

    public boolean tenant() {
        return TYPE_TENANT.equals(type);
    }

    public boolean tenantGroup() {
        return TYPE_TENANT_GROUP.equals(type);
    }

    @Data
    public static class Helper {
        private final String type;
        private final String id;
        private final String name;

        public static List<ConnectedEnterpriseDTO> fromData(IObjectData objectData, DataVisibilityRange dataVisibilityRange) {
            List<String> list = (List<String>) objectData.get(dataVisibilityRange.getApiName(), List.class);
            if (CollectionUtils.empty(list)) {
                return Collections.emptyList();
            }
            return list.stream()
                    .filter(StringUtils::isNotBlank)
                    .map(Helper::getConnectedEnterpriseDTO)
                    .distinct()
                    .collect(Collectors.toList());
        }

        public static List<Helper> fromEnterprise(List<ConnectedEnterpriseDTO> enterpriseList,
                                                  Map<String, SimpleEnterpriseData> enterpriseInfoMap,
                                                  Map<String, QueryTenantGroupByIds.TenantGroupInfo> tenantGroupInfoMap) {
            if (CollectionUtils.empty(enterpriseList)) {
                return Collections.emptyList();
            }
            return enterpriseList.stream()
                    .map(enterprise -> createHelper(enterpriseInfoMap, tenantGroupInfoMap, enterprise))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }

        private static Helper createHelper(Map<String, SimpleEnterpriseData> enterpriseInfoMap,
                                           Map<String, QueryTenantGroupByIds.TenantGroupInfo> tenantGroupInfoMap,
                                           ConnectedEnterpriseDTO enterprise) {
            if (enterprise.tenant()) {
                String enterpriseId = enterprise.getId();
                SimpleEnterpriseData info = enterpriseInfoMap.get(enterpriseId);
                if (Objects.isNull(info)) {
                    if (IObjectData.PUBLIC_DATA_DOWNSTREAM_TENANT_ID.equals(enterpriseId)) {
                        return new Helper(TYPE_TENANT, enterpriseId, I18NExt.text(I18NKey.PUBLIC_OBJECT_ALL_ENTERPRISES));
                    }
                    return null;
                }
                String enterpriseName = info.getEnterpriseName();
                return new Helper(TYPE_TENANT, enterpriseId, enterpriseName);
            }
            QueryTenantGroupByIds.TenantGroupInfo tenantGroupInfo = tenantGroupInfoMap.get(enterprise.getId());
            if (Objects.isNull(tenantGroupInfo)) {
                return null;
            }
            return new Helper(TYPE_TENANT_GROUP, TYPE_TENANT_GROUP_PREFIX + enterprise.getId(), tenantGroupInfo.getName());
        }

        private static ConnectedEnterpriseDTO getConnectedEnterpriseDTO(String id) {
            ConnectedEnterpriseDTO result = new ConnectedEnterpriseDTO();
            if (StringUtils.startsWith(id, TYPE_TENANT_GROUP_PREFIX)) {
                result.setId(StringUtils.substringAfterLast(id, TYPE_TENANT_GROUP_PREFIX));
                result.setType(TYPE_TENANT_GROUP);
                return result;
            }
            result.setId(id);
            result.setType(TYPE_TENANT);
            return result;
        }

        public Map<String, Object> toMap() {
            return (Map) JSON.toJSON(this);
        }
    }
}
