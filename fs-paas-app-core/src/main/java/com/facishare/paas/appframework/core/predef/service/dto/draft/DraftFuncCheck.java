package com.facishare.paas.appframework.core.predef.service.dto.draft;

import lombok.Data;


public interface DraftFuncCheck {
    @Data
    class Arg {
        private String draftId;

    }
    @Data

    class Result {
        private boolean success;

        public static Result buildResult(boolean success) {
            Result result = new Result();
            result.setSuccess(success);
            return result;
        }
    }
}
