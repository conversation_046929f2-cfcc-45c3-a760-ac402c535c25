package com.facishare.paas.appframework.metadata.dto.ai;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> <PERSON><PERSON>Hui
 * @Data : 2025/2/28
 * @Description :
 */
public class AIProxy {
	/*
	 * @param history 拼接了自定义信息的聊天上下文
	 * @param maxTokens 最大token数
	 * @param temperature 回答发散度,默认都应该填写0使得回答趋于稳定可复现
	 * @param model 模型名称  https://wiki.firstshare.cn/pages/viewpage.action?pageId=433128490
	 * @param sessionId 会话id, 用于统计生成的准确率
	 */
	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class ChatArg {
		List<AIDto.Message> messages;
		@Builder.Default
		private Integer maxTokens = 2048;
		@Builder.Default
		private Double temperature = 0.0;
		@Builder.Default
		private String model = "qwen-plus";
		String sessionId;
	}

	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class ChatResult {
		private String message;
		private String finish_reason;
		private AIDto.Usage usage;
	}

	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class TopNArg {
		private String userInput;
		@Builder.Default
		private int topN = 50;
		private Map<String, Set<String>> parameter; // parameter -> objectFilters -> [] 对象api列表

		public TopNArg putObjApis(Set<String> apis) {
			parameter = CollectionUtils.nullToEmpty(parameter);
			parameter.put("objectFilters", CollectionUtils.nullToEmpty(apis));
			return this;
		}
	}
}
