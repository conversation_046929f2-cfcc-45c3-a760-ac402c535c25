package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2025/3/10
 */
@Slf4j
public class StandardReChangeOrderAction extends PreDefineAction<StandardReChangeOrderAction.Arg, StandardReChangeOrderAction.Result> {
    private IObjectData objectData;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.ReChangeOrder.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected void initDataList() {
        callSuperInitDataList();
        objectData = dataList.get(0);
    }

    @Override
    protected void before(Arg arg) {
        callSuperBefore(arg);
        validateCanStartChangeOrder();
    }

    private void validateCanStartChangeOrder() {
        if (!infraServiceFacade.canStartChangeOrder(actionContext.getUser(), objectDescribe, objectData, true)) {
            log.warn("can not start change order, user: {}, describeApiName: {}, dataId: {}", actionContext.getUser(), objectDescribe.getApiName(), objectData.getId());
            throw new ValidateException(I18NExt.text(I18NKey.CAN_NOT_START_CHANGE_ORDER));
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        triggerChangeOrderAction(objectDataExt);
        return buildResult();
    }

    private Result buildResult() {
        return Result.builder()
                .originalApiName(objectDescribe.getOriginalDescribeApiName())
                .originalDataId(ObjectDataExt.of(objectData).getOriginalDataId())
                .build();
    }

    private void triggerChangeOrderAction(ObjectDataExt objectDataExt) {
        StandardChangeAction.Arg changeActionArg = StandardChangeAction.Arg.fromReChangeOrder(
                objectDataExt.getOriginalDataId(), objectDataExt.getChangeOrderRule(), objectDataExt.getId());
        ActionContext actionContext = new ActionContext(RequestContextManager.getContext(), objectDescribe.getOriginalDescribeApiName(), StandardAction.Change.toString());
        serviceFacade.triggerAction(actionContext, changeActionArg, StandardChangeAction.Result.class);
    }

    // 抽取super调用的辅助方法，用于测试Mock
    void callSuperInitDataList() {
        super.initDataList();
    }

    // 抽取super调用的辅助方法，用于测试Mock
    void callSuperBefore(Arg arg) {
        super.before(arg);
    }

    @Data
    public static class Arg {
        @JsonProperty("object_id")
        @JSONField(name = "object_id")
        private String objectDataId;
    }

    @Data
    @Builder
    public static class Result {
        private String originalApiName;
        private String originalDataId;
    }
}
