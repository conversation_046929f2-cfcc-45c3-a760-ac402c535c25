package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.appframework.core.model.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GenerateByAI
 * 测试DataConvertContext类的各种构造方法和转换功能
 */
class DataConvertContextTest {

  private User user;

  @BeforeEach
  void setUp() {
    user = User.systemUser("74255");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试基本的of方法创建DataConvertContext
   */
  @Test
  @DisplayName("测试of方法 - 基本创建")
  void testOf_Basic() {
    // 执行被测试方法
    DataConvertContext context = DataConvertContext.of(user);

    // 验证结果
    assertNotNull(context);
    assertEquals(user, context.getUser());
    assertFalse(context.isUseTenantTimezone());
    assertFalse(context.isUseISO());
    assertEquals("", context.getRegion());
    assertFalse(context.isFromQuoteField());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试带region参数的of方法
   */
  @Test
  @DisplayName("测试of方法 - 带region参数")
  void testOf_WithRegion() {
    // 准备测试数据
    String region = "zh_CN";

    // 执行被测试方法
    DataConvertContext context = DataConvertContext.of(user, region);

    // 验证结果
    assertNotNull(context);
    assertEquals(user, context.getUser());
    assertFalse(context.isUseTenantTimezone());
    assertFalse(context.isUseISO());
    assertEquals(region, context.getRegion());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试带useTenantTimezone参数的of方法
   */
  @Test
  @DisplayName("测试of方法 - 带useTenantTimezone参数")
  void testOf_WithUseTenantTimezone() {
    // 执行被测试方法
    DataConvertContext context = DataConvertContext.of(user, true);

    // 验证结果
    assertNotNull(context);
    assertEquals(user, context.getUser());
    assertTrue(context.isUseTenantTimezone());
    assertFalse(context.isUseISO());
    assertEquals("", context.getRegion());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试带useTenantTimezone和useISO参数的of方法
   */
  @Test
  @DisplayName("测试of方法 - 带useTenantTimezone和useISO参数")
  void testOf_WithUseTenantTimezoneAndUseISO() {
    // 执行被测试方法
    DataConvertContext context = DataConvertContext.of(user, true, true);

    // 验证结果
    assertNotNull(context);
    assertEquals(user, context.getUser());
    assertTrue(context.isUseTenantTimezone());
    assertTrue(context.isUseISO());
    assertEquals("", context.getRegion());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试完整参数的of方法
   */
  @ParameterizedTest
  @MethodSource("provideOfMethodTestData")
  @DisplayName("测试of方法 - 完整参数")
  void testOf_FullParameters(boolean useTenantTimezone, boolean useISO, String region) {
    // 执行被测试方法
    DataConvertContext context = DataConvertContext.of(user, useTenantTimezone, useISO, region);

    // 验证结果
    assertNotNull(context);
    assertEquals(user, context.getUser());
    assertEquals(useTenantTimezone, context.isUseTenantTimezone());
    assertEquals(useISO, context.isUseISO());
    assertEquals(region, context.getRegion());
  }

  /**
   * 提供of方法测试数据
   */
  private static Stream<Arguments> provideOfMethodTestData() {
    return Stream.of(
        Arguments.of(false, false, ""),
        Arguments.of(true, false, "zh_CN"),
        Arguments.of(false, true, "en_US"),
        Arguments.of(true, true, "ja_JP")
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试Builder构造方法
   */
  @Test
  @DisplayName("测试Builder构造方法 - 正常场景")
  void testBuilder_Normal() {
    // 执行被测试方法
    DataConvertContext context = DataConvertContext.builder()
        .user(user)
        .useTenantTimezone(true)
        .useISO(true)
        .region("zh_CN")
        .build();

    // 验证结果
    assertNotNull(context);
    assertEquals(user, context.getUser());
    assertTrue(context.isUseTenantTimezone());
    assertTrue(context.isUseISO());
    assertEquals("zh_CN", context.getRegion());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试Builder构造方法当user为null时抛出异常
   */
  @Test
  @DisplayName("测试Builder构造方法 - user为null抛出异常")
  void testBuilderThrowsNullPointerException_WhenUserIsNull() {
    // 执行并验证异常
    NullPointerException exception = assertThrows(NullPointerException.class, () -> {
      DataConvertContext.builder()
          .user(null)
          .useTenantTimezone(true)
          .useISO(true)
          .region("zh_CN")
          .build();
    });

    // 验证异常信息
    assertEquals("user", exception.getMessage());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试toSessionContext方法的转换功能
   */
  @Test
  @DisplayName("测试toSessionContext方法 - 正常转换")
  void testToSessionContext_Normal() {
    // 准备测试数据
    DataConvertContext context = DataConvertContext.builder()
        .user(user)
        .useTenantTimezone(true)
        .useISO(true)
        .region("zh_CN")
        .build();
    context.setFromQuoteField(true);

    // 执行被测试方法
    com.facishare.crm.valueobject.SessionContext sessionContext = context.toSessionContext();

    // 验证结果
    assertNotNull(sessionContext);
    assertEquals(Long.parseLong(user.getTenantId()), sessionContext.getEId());
    assertEquals(Integer.parseInt(user.getUserId()), sessionContext.getUserId());
    // 注意：SessionContext没有isUseTenantTimezone()方法，我们通过其他方式验证
    assertTrue(sessionContext.isDateFormatIncludeTimezoneInfo());
    assertEquals("zh_CN", sessionContext.getRegion());
    assertTrue(sessionContext.isFromQuoteField());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试toSessionContext方法的默认值转换
   */
  @Test
  @DisplayName("测试toSessionContext方法 - 默认值转换")
  void testToSessionContext_DefaultValues() {
    // 准备测试数据
    DataConvertContext context = DataConvertContext.of(user);

    // 执行被测试方法
    com.facishare.crm.valueobject.SessionContext sessionContext = context.toSessionContext();

    // 验证结果
    assertNotNull(sessionContext);
    assertEquals(Long.parseLong(user.getTenantId()), sessionContext.getEId());
    assertEquals(Integer.parseInt(user.getUserId()), sessionContext.getUserId());
    // 注意：SessionContext没有isUseTenantTimezone()方法，我们通过其他方式验证
    assertFalse(sessionContext.isDateFormatIncludeTimezoneInfo());
    assertEquals("", sessionContext.getRegion());
    assertFalse(sessionContext.isFromQuoteField());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setFromQuoteField方法
   */
  @Test
  @DisplayName("测试setFromQuoteField方法")
  void testSetFromQuoteField() {
    // 准备测试数据
    DataConvertContext context = DataConvertContext.of(user);

    // 执行被测试方法
    context.setFromQuoteField(true);

    // 验证结果
    assertTrue(context.isFromQuoteField());

    // 再次设置为false
    context.setFromQuoteField(false);
    assertFalse(context.isFromQuoteField());
  }
}
