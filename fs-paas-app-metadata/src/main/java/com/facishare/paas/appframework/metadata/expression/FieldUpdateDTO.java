package com.facishare.paas.appframework.metadata.expression;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * 字段更新数据传输对象
 * <p>
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/17.
 */
@Data
@Builder
@AllArgsConstructor
public class FieldUpdateDTO {
    //待更新字段
    private String field;
    //表达式
    private String expression;
    private boolean nullAsZero;
    private int decimalPlaces;
    //返回类型
    private String returnType;

    //返回结果
    private Object result;
}
