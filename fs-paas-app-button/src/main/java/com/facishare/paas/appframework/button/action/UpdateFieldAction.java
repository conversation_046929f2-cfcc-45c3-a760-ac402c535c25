package com.facishare.paas.appframework.button.action;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.button.dto.UpdatesPojo;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.FieldManyMaxConfig;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.expression.Expression;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.expression.ExpressionDTO;
import com.facishare.paas.appframework.metadata.expression.FieldUpdateDTO;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeServiceImpl;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * Created by linqy on 2018/01/11.
 * 更新字段
 */
@Slf4j
@Component
public class UpdateFieldAction implements ActionExecutor {

    @Autowired
    private MetaDataService metaDataService;
    @Autowired
    private ParseVarService parseVarService;
    @Autowired
    private LayoutLogicServiceImpl layoutLogicService;
    @Autowired
    private FunctionPrivilegeServiceImpl functionPrivilegeService;
    @Autowired
    private LogService logService;
    @Autowired
    private ExpressionCalculateLogicService expressionCalculateLogicService;
    @Autowired
    private UserRoleInfoService userRoleInfoService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private MaskFieldLogicService maskFieldLogicService;

    private static final String VARIABLE = "variable";
    private static final String FORMULA = "formula";
    private static final String CONSTANT = "constant";

    private static final List<String> LIST_VALUE_FILED = Lists.newArrayList(
            IFieldType.EMPLOYEE, IFieldType.DEPARTMENT, IFieldType.SELECT_MANY, IFieldType.DIMENSION
    );

    @Override
    public ActionExecutorType getType() {
        return ActionExecutorType.UPDATES;
    }

    @Override
    public ButtonExecutor.Result invoke(ButtonExecutor.Arg arg, ActionExecutorContext context) {
        Map<String, List<IObjectData>> details = arg.toDetails();
        IObjectData objectData = arg.getObjectData();
        return startCustomButton(objectData, details, context.getUser(), arg,
                context.getAction(), context.getButton(), context.getDescribe());
    }

    private ButtonExecutor.Result startCustomButton(IObjectData objectData,
                                                    Map<String, List<IObjectData>> details,
                                                    User user,
                                                    ButtonExecutor.Arg arg,
                                                    IUdefAction action,
                                                    IUdefButton button,
                                                    IObjectDescribe describe) {
        IObjectData oldData = ObjectDataExt.of(objectData).copy();
        Map<String, Object> map = getUpdateFieldMap(describe, objectData, button, action, arg, user);
        //更新字段
        metaDataService.batchUpdateWithMap(user, Lists.newArrayList(objectData), map);

        //记录审计日志
        Map<String, Object> updateMap = ObjectDataExt.of(oldData).diff(objectData, describe);
        //2021/12/21 paas.udobj.custom_button 一个key paas.udobj.button 按钮
        logService.log(user, EventType.MODIFY, ActionType.Modify, describe, objectData, updateMap, oldData,
                "custom_button", button.getLabel() + "," + button.getApiName(), getInterNationalPeerDisplayName(button, describe.getApiName()), null, null);
        //发送mq消息
        metaDataService.sendActionMq(user, Lists.newArrayList(objectData), ObjectAction.UPDATE);

        return null;
    }

    private InternationalItem getInterNationalPeerDisplayName(IUdefButton button, String describeApiName) {
        String buttonLabelKey = ButtonExt.of(button).getButtonI18NLabelKey(describeApiName);
        Map<String, String> defaultParameterValues = Maps.newHashMap();
        defaultParameterValues.put(buttonLabelKey, button.getLabel());
        defaultParameterValues.put(button.getApiName(), button.getApiName());
        return InternationalItem.builder()
                .internationalKey(I18NKey.BUTTON_PEER_DISPLAY_NAME)
                .defaultInternationalValue(button.getLabel() + "," + button.getApiName())
                .internationalParameters(Lists.newArrayList(buttonLabelKey, button.getApiName()))
                .defaultParameterValues(defaultParameterValues)
                .build();
    }


    public Map<String, Object> getUpdateFieldMap(IObjectDescribe describe, IObjectData objectData, IUdefButton button,
                                                 IUdefAction action, ButtonExecutor.Arg buttonArg, User user) {
        List<UpdatesPojo.Field> fieldList = getUpdatesPojo(user, buttonArg, action, (ObjectData) objectData, describe, button);
        Map<String, Object> updateFieldMap = Maps.newHashMap();
        fieldList.stream()
                .filter(field -> !ignoreField(user, describe, field))
                .forEach(field -> configValue(updateFieldMap, field));
        if (buttonArg.isSkipUpdateFieldActionValidation()) {
            return updateFieldMap;
        }
        validateLookup(updateFieldMap, describe, user);
        updateFieldMap.putAll(getMultiLangDataMap(updateFieldMap, describe, buttonArg.getArgs()));
        return updateFieldMap;
    }

    /**
     * 下游按钮更新动作，部门、人员字段更新需要特殊处理
     * 部门、人员、部门（多选）、人员（多选）字段不能更新为按钮入参 {@link #VARIABLE}
     * 部门、人员、部门（多选）、人员（多选）字段可以更新为常量 {@link #CONSTANT}
     *
     * @param user
     * @param describe
     * @param field
     * @return true 需要忽略的字段 false 需要保留的字段
     */
    private boolean ignoreField(User user, IObjectDescribe describe, UpdatesPojo.Field field) {
        if (!user.isOutUser()) {
            return false;
        }
        return ObjectDescribeExt.of(describe)
                .getFieldDescribeSilently(field.getField())
                .filter(it -> VARIABLE.equals(field.getVar_type()))
                .map(FieldDescribeExt::of)
                .map(fieldExt -> fieldExt.isEmployee() || fieldExt.isDepartmentField()
                        || fieldExt.isEmployeeManyField() || fieldExt.isDepartmentManyField())
                .orElse(false);
    }

    private void validateLookup(Map<String, Object> objectDataMap, IObjectDescribe describe, User user) {
        List<ObjectReferenceWrapper> fields = ObjectDescribeExt.of(describe).getActiveReferenceFieldDescribes().stream()
                .filter(it -> objectDataMap.containsKey(it.getApiName()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(fields)) {
            return;
        }
        IObjectData objectData = new ObjectData(objectDataMap);
        objectData.setDescribeApiName(describe.getApiName());

        fields.forEach(fieldDescribe -> metaDataService.validateLookupDataIgnorePolygonal(user, objectData, fieldDescribe));
    }

    private void configValue(Map<String, Object> map, UpdatesPojo.Field field) {
        Object rawValue = field.getValue();
        if (LIST_VALUE_FILED.contains(field.getReturn_type()) && !(rawValue instanceof List) && !Objects.isNull(rawValue)) {
            rawValue = Lists.newArrayList(String.valueOf(rawValue));
        }

        map.put(field.getField(), rawValue);
    }

    /**
     * 对需要更新的字段做转换
     */
    private List<UpdatesPojo.Field> getUpdatesPojo(User user, ButtonExecutor.Arg arg, IUdefAction
            action, ObjectData objectData, IObjectDescribe describe, IUdefButton button) {

        List<UpdatesPojo.Field> fields = getActionParameterField(action, describe);
        ILayout layout = null;
        ButtonParamRender buttonParamRender = null;
        if (!arg.isSkipUpdateFieldActionValidation()) {
            LayoutLogicService.LayoutContext layoutContext = bildLayoutContext(user);
            layout = layoutLogicService.findObjectLayoutWithType(layoutContext, objectData.getRecordType(), describe, LayoutTypes.EDIT, objectData);
            buttonParamRender = validateByButtonParam(user, action, objectData, describe, button, layout);
        }

        List<UpdatesPojo.Field> variableFieldList = fields.stream().filter(x -> VARIABLE.equals(x.getVar_type())).collect(Collectors.toList());
        //变量替换
        List<Variable> varValueList = parseVarService.getVarListByField(variableFieldList, arg.getArgs(), objectData, user, describe, button);

        Map<String, Object> varValueMap = varValueList.stream()
                .filter(x -> Objects.nonNull(x.getApiName()) && Objects.nonNull(x.getValue()))
                .collect(Collectors.toMap(Variable::getApiName, Variable::getValue));

        List<FieldUpdateDTO> fieldUpdateDTOList = Lists.newArrayList();

        fields.forEach(x -> {
            x.setReturn_type(describe.getFieldDescribe(x.getField()).getType());
            if (VARIABLE.equals(x.getVar_type())) {
                x.setValue(varValueMap.get(x.getField()));
            } else if (FORMULA.equals(x.getVar_type())) {
                fieldUpdateDTOList.add(getFieldUpdateDTO(x, describe));
            }
        });

        Map<String, ExpressionDTO.FormVariableDTO> formData = getFormData(button, arg);
        expressionCalculateLogicService.bulkCalculate(describe, fieldUpdateDTOList, objectData, formData);
        Map<String, FieldUpdateDTO> fieldUpdateDTOMap = Maps.newHashMap();
        fieldUpdateDTOList.forEach(x -> fieldUpdateDTOMap.put(x.getField(), x));
        fields.forEach(x -> {
            if (Objects.nonNull(fieldUpdateDTOMap.get(x.getField()))) {
                x.setValue(fieldUpdateDTOMap.get(x.getField()).getResult());
            }
        });
        addSelectOneOther(fields, objectData, arg, describe);
        addSelectManyOther(fields, objectData, arg, describe);
        if (arg.isSkipUpdateFieldActionValidation()) {
            return fields;
        }
        checkFieldIsRequired(fields, describe, LayoutExt.of(layout), buttonParamRender);
        String manyFieldMsg = outOfBoundsForManyField(fields, describe);
        if (StringUtils.isNotEmpty(manyFieldMsg)) {
            throw new ValidateException(I18NExt.text(I18NKey.EMPLOYEE_DEPARTMENT_MANY_FIELD_BEYOND_MAX_LIMIT_V3, manyFieldMsg));
        }
        return fields;
    }

    /**
     * 按钮执行动作只有前台按钮调用，这里从上下文中获取appId
     *
     * @param user
     * @return
     */
    private LayoutLogicService.LayoutContext bildLayoutContext(User user) {
        String appId = RequestUtil.getAppId();
        return LayoutLogicService.LayoutContext.of(user, appId);
    }


    public ButtonParamRender validateByButtonParam(User user, IUdefAction
            action, ObjectData objectData, IObjectDescribe describe, IUdefButton button, ILayout layout) {
        List<UpdatesPojo.Field> fields = getActionParameterField(action, describe);
        LayoutExt layoutExt = LayoutExt.of(layout);
        ButtonParamRender buttonParamRender = ButtonParamRender.builder()
                .data(objectData)
                .describe(describe)
                .user(user)
                .fields(fields)
                .orgService(orgService)
                .userRoleInfoService(userRoleInfoService)
                .maskFieldLogicService(maskFieldLogicService)
                .build()
                .initMaskFieldsWithUpdatesPojoFields();
        checkWaterMarkImage(fields, button, describe);
        // 校验数据归属部门字段
        checkDataOwnDeptAndOrg(fields, describe);
        checkFieldIsReadField(user, fields, describe, button, layoutExt, buttonParamRender);
        return buttonParamRender;
    }

    private List<UpdatesPojo.Field> getActionParameterField(IUdefAction action, IObjectDescribe objectDescribe) {
        String actionParameter = action.getActionParamter();
        UpdatesPojo pojo = JSON.parseObject(actionParameter, UpdatesPojo.class);
        List<UpdatesPojo.Field> fields = pojo.getFields();
        //按钮更新字段去掉禁用和删除的字段
        List<String> disableFieldDescribes = ObjectDescribeExt.of(objectDescribe).getDisableFieldDescribes().stream()
                .map(IFieldDescribe::getApiName).collect(Collectors.toList());
        fields.removeIf(x -> !objectDescribe.containsField(x.getField()) || disableFieldDescribes.contains(x.getField()));
        return fields;
    }

    private Map<String, Object> getMultiLangDataMap(Map<String, Object> updateFieldMap, IObjectDescribe objectDescribe, Map<String, Object> args) {
        if (CollectionUtils.empty(updateFieldMap) || Objects.isNull(objectDescribe)
                || CollectionUtils.empty(args)) {
            return Maps.newHashMap();
        }
        Map<String, Object> result = Maps.newHashMap();
        Map<String, IFieldDescribe> fieldDescribeMap = ObjectDescribeExt.of(objectDescribe).getFieldDescribeMap();
        for (String fieldApiName : updateFieldMap.keySet()) {
            IFieldDescribe fieldDescribe = fieldDescribeMap.get(fieldApiName);
            if (Objects.isNull(fieldDescribe)) {
                continue;
            }
            if (!FieldDescribeExt.of(fieldDescribe).isEnableDataMultiLang()) {
                continue;
            }
            String multiLangApiName = FieldDescribeExt.getMultiLangExtraFieldName(fieldApiName);
            Object multiLangData = args.get(getFormFieldApiName(multiLangApiName));
            if (Objects.nonNull(multiLangData)) {
                result.put(multiLangApiName, multiLangData);
            }
        }
        return result;
    }

    private String getFormFieldApiName(String fieldApiName) {
        return "form_" + fieldApiName;
    }

    public String outOfBoundsForManyField(List<UpdatesPojo.Field> fields, IObjectDescribe describe) {
        return fields.stream()
                .map(field -> {
                    Optional<IFieldDescribe> optional = ObjectDescribeExt.of(describe).getFieldDescribeSilently(field.getField());
                    if (!optional.isPresent()) {
                        return "";
                    }
                    IFieldDescribe fieldDescribe = describe.getFieldDescribe(field.getField());
                    return FieldManyMaxConfig.outBoundsForManyField(fieldDescribe.getType(), fieldDescribe.getLabel(), field.getValue(), describe);
                })
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.joining(","));
    }

    private void checkDataOwnDeptAndOrg(List<UpdatesPojo.Field> fields, IObjectDescribe describe) {
        if (CollectionUtils.empty(fields)) {
            return;
        }
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(describe);
        if (!objectDescribeExt.isSlaveObject()) {
            return;
        }
        List<String> labels = fields.stream()
                .filter(field -> ObjectDataExt.DATA_OWN_DEPARTMENT.equals(field.getField()) || ObjectDataExt.DATA_OWN_ORGANIZATION.equals(field.getField()))
                .map(field -> objectDescribeExt.getFieldDescribeSilently(field.getField()).map(IFieldDescribe::getLabel).orElse(""))
                .filter(label -> !Strings.isNullOrEmpty(label))
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(labels)) {
            throw new ValidateException(I18N.text(I18NKey.SLAVE_OBJECT_NOT_UPDATE_FIELD, String.join(", ", labels)));
        }
    }

    private void checkWaterMarkImage(List<UpdatesPojo.Field> fields, IUdefButton button, IObjectDescribe describe) {
        if (CollectionUtils.empty(fields) || CollectionUtils.empty(button.getParamForm())) {
            return;
        }
        List<IParamForm> paramFormList = ParamForm.fromList(button.getParamForm());
        Map<String, IParamForm> paramFormMap = paramFormList.stream().collect(Collectors.toMap(IParamForm::getApiName, it -> it));
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);

        for (UpdatesPojo.Field field : fields) {
            // 不是图片字段
            if (!describeExt.getFieldDescribeSilently(field.getField()).filter(fieldDescribe -> FieldDescribeExt.of(fieldDescribe).isImageField()).isPresent()) {
                continue;
            }
            // 不是变量值
            if (!UpdateFieldAction.VARIABLE.equals(field.getVar_type())) {
                continue;
            }
            Image sourceImage = (Image) describe.getFieldDescribe(field.getField());
            IParamForm paramForm = paramFormMap.get(Expression.of(String.valueOf(field.getValue())).parseVariableNames().get(0));
            String fieldApiName = paramForm.convertToFieldApiName();
            Image targetImage = (Image) describe.getFieldDescribe(fieldApiName);
            if (sourceImage.getIsWaterMark() && BooleanUtils.isFalse(targetImage.getIsWaterMark())) {
                throw new ValidateException(I18N.text(I18NKey.WATERMARK_IMAGE_CANNOT_UPDATED_TO_NON_WATERMARK_IMAGE, sourceImage.getLabel(), targetImage.getLabel()));
            }
        }
    }

    //校验待更新的字段是否必填
    private void checkFieldIsRequired(List<UpdatesPojo.Field> fields, IObjectDescribe describe, LayoutExt layoutExt, ButtonParamRender buttonParamRender) {
        if (CollectionUtils.empty(fields)) {
            return;
        }
        List<String> labels = Lists.newArrayList();
        fields.forEach(field -> {
            IFieldDescribe fieldDescribe = describe.getFieldDescribe(field.getField());
            if (Objects.isNull(fieldDescribe)) {
                return;
            }
            String label = fieldDescribe.getLabel();
            Optional<IFormField> formField = layoutExt.getField(field.getField());
            if (formField.isPresent() && formField.get().isRequired()) {
                if (ObjectDataExt.isValueEmpty(field.getValue()) && !buttonParamRender.isShowMask(field.getField())) {
                    labels.add(label);
                }
            }
        });
        if (CollectionUtils.notEmpty(labels)) {
            throw new ValidateException(I18N.text(I18NKey.FIELD_READ_REQUIRED_OPERATE, StringUtils.join(labels, ",")));
        }
    }

    private Map<String, ExpressionDTO.FormVariableDTO> getFormData(IUdefButton button, ButtonExecutor.Arg arg) {
        Map<String, ExpressionDTO.FormVariableDTO> formData = Maps.newHashMap();
        if (button.getParamForm().isEmpty()) {
            return formData;
        } else {
            button.getParamForm().forEach(x -> {
                String fileName = (String) x.get("api_name");
                String type = (String) x.get("type");
                Object value = getFieldValue(arg, fileName);
                ExpressionDTO.FormVariableDTO formVariableDTO = ExpressionDTO.FormVariableDTO.builder()
                        .fieldName(fileName)
                        .value(value)
                        .type(type)
                        .build();
                formData.put(fileName, formVariableDTO);
            });
            return formData;
        }
    }

    private Object getFieldValue(ButtonExecutor.Arg arg, String fileName) {
        Map<String, Object> args = arg.getArgs();
        if (CollectionUtils.empty(args)) {
            return null;
        }
        return args.get(fileName);
    }

    private FieldUpdateDTO getFieldUpdateDTO(UpdatesPojo.Field field, IObjectDescribe describe) {

        String value = String.valueOf(field.getValue());

        int decimalPlaces = Optional.of(describe.getFieldDescribe(field.getField()))
                .map(FieldDescribeExt::of)
                .map(FieldDescribeExt::getDecimalPlaces)
                .orElse(2);

        FieldUpdateDTO fieldUpdateDTO = FieldUpdateDTO.builder()
                .field(field.getField())
                .expression(value)
                .nullAsZero(field.isDefault_to_zero())
                .decimalPlaces(decimalPlaces)
                .returnType(field.getReturn_type())
                .build();

        return fieldUpdateDTO;
    }

    private void addSelectOneOther(List<UpdatesPojo.Field> fields, ObjectData objectData, ButtonExecutor.Arg arg, IObjectDescribe describe) {
        Map<String, Object> args = arg.getArgs();
        List<UpdatesPojo.Field> selectOneFieldList = fields.stream().filter(field -> IFieldType.SELECT_ONE.equals(field.getReturn_type())).collect(Collectors.toList());
        for (UpdatesPojo.Field field : selectOneFieldList) {
            if (SelectOne.OPTION_OTHER_VALUE.equals(field.getValue())) {
                UpdatesPojo.Field other = UpdatesPojo.Field.builder()
                        .field(field.getField() + "__o")
                        .value(getOtherValue(field, args, describe))
                        .build();
                fields.add(other);
            } else {
                if (Objects.nonNull(objectData.get(field + "__o"))) {
                    objectData.remove(field + "__o");
                }
            }
        }
    }

    private void addSelectManyOther(List<UpdatesPojo.Field> fields, ObjectData objectData, ButtonExecutor.Arg arg, IObjectDescribe describe) {
        Map<String, Object> args = arg.getArgs();
        fields.stream()
                .filter(field -> IFieldType.SELECT_MANY.equals(field.getReturn_type()))
                .collect(Collectors.toList())
                .forEach(field -> {
                    List<String> value = CollectionUtils.nullToEmpty((List) field.getValue());
                    if (value.contains(SelectMany.OPTION_OTHER_VALUE)) {
                        UpdatesPojo.Field other = UpdatesPojo.Field.builder()
                                .field(field.getField() + "__o")
                                .value(getOtherValue(field, args, describe))
                                .build();
                        fields.add(other);
                    } else {
                        if (!value.contains(SelectMany.OPTION_OTHER_VALUE)) {
                            objectData.remove(field + "__o");
                        }
                    }
                });
    }

    // 获取单选其他的value
    private Object getOtherValue(UpdatesPojo.Field field, Map<String, Object> args, IObjectDescribe describe) {
        // 判断在field中取值还是在变量中取值
        if (Objects.nonNull(field.getOther_value())) {
            return field.getOther_value();
        }
        if (CollectionUtils.notEmpty(args) && Objects.nonNull(args.get("form_" + field.getField() + "__o"))) {
            return args.get("form_" + field.getField() + "__o");
        }
        IFieldDescribe fieldDescribe = ObjectDescribeExt.of(describe).getFieldDescribe(field.getField());
        if (IFieldType.SELECT_ONE.equals(fieldDescribe.getType()) && ((SelectOne) fieldDescribe).getOptionOtherIsRequired()) {
            throw new ValidateException(I18N.text(I18NKey.SELECT_ONE_NOT_EMPTY, fieldDescribe.getLabel()));
        } else if (IFieldType.SELECT_MANY.equals(fieldDescribe.getType()) && ((SelectMany) fieldDescribe).getOptionOtherIsRequired()) {
            throw new ValidateException(I18N.text(I18NKey.SELECT_MANY_NOT_EMPTY, fieldDescribe.getLabel()));
        } else {
            return "";
        }
    }

    //校验待更新的字段是否是只读字段
    private void checkFieldIsReadField(User user, List<UpdatesPojo.Field> fields, IObjectDescribe describe, IUdefButton button, LayoutExt layoutExt, ButtonParamRender buttonParamRender) {
        Set<String> readonlyFields = functionPrivilegeService.getReadonlyFields(user, describe.getApiName());
        List<String> disableFieldDescribes = ObjectDescribeExt.of(describe).getDisableFieldDescribes().stream()
                .map(IFieldDescribe::getApiName).collect(Collectors.toList());
        for (UpdatesPojo.Field field : fields) {
            if (Objects.isNull(describe.getFieldDescribe(field.getField()))) {
                Map<Object, Object> labels = button.getParamForm().stream().collect(Collectors.toMap(x -> x.get("api_name"), x -> x.get("label")));
                String label = (String) labels.getOrDefault(String.format("form_%s", field.getField()), field.getField());
                throw new ValidateException(I18N.text(I18NKey.FIELD_DELETED, label));
            }

            if (disableFieldDescribes.contains(field.getField())) {
                continue;
            }

            String label = describe.getFieldDescribe(field.getField()).getLabel();

            if (readonlyFields.contains(field.getField())
                    && !ObjectDataExt.DATA_OWN_DEPARTMENT.equals(field.getField())
                    && !ObjectDataExt.DATA_OWN_ORGANIZATION.equals(field.getField())) {
                throw new ValidateException(I18N.text(I18NKey.FIELD_READ_ONLY_NOT_OPERATE, label));
            }
            Optional<FormFieldExt> formFieldExt = layoutExt.getField(field.getField()).map(FormFieldExt::of);

            if (formFieldExt.isPresent()
                    && formFieldExt.get().readOnly()
                    && !ObjectDataExt.DATA_OWN_DEPARTMENT.equals(formFieldExt.get().getFieldName())
                    && !ObjectDataExt.DATA_OWN_ORGANIZATION.equals(formFieldExt.get().getFieldName())
                    && !buttonParamRender.isShowMask(field.getField())) {

                throw new ValidateException(I18N.text(I18NKey.FIELD_READ_ONLY_NOT_OPERATE, label));
            }
        }
    }
}
