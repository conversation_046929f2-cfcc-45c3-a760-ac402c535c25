package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.describe.Image;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.experimental.Delegate;

import java.util.List;
import java.util.Map;

public class ImageExt {
    @Getter
    @Delegate
    private final Image image;

    public static final String SIGNATURE_URL = "signedUrl";

    public static final String SIGNATURE = "signature";
    public static final String PATH = "path";
    public static final String EXT = "ext";
    public static final String FILENAME = "filename";
    public static final String LINK_ID = "linkId";
    public static final String PREFIX_HTTP = "https://";
    public static final String PREFIX_HTTPS = "http://";
    public static final int EXPIRE_TIME = 60 * 60;

    private ImageExt(Image image) {
        this.image = image;
    }

    public static ImageExt of(Image image) {
        return new ImageExt(image);
    }

    public void setDefaultWaterMark() {
        List<Map<String, String>> waterMark = Lists.newArrayList();
        Map<String, String> currentUser = Maps.newHashMap();
        currentUser.put("value", "current_user");
        currentUser.put("type", "variable");
        waterMark.add(currentUser);
        Map<String, String> currentTime = Maps.newHashMap();
        currentTime.put("value", "current_time");
        currentTime.put("type", "variable");
        waterMark.add(currentTime);
        Map<String, String> currentAddress = Maps.newHashMap();
        currentAddress.put("value", "current_address");
        currentAddress.put("type", "variable");
        waterMark.add(currentAddress);
        setWatermark(waterMark);
    }
}
