package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.service.PrintTemplateServiceProxy;
import com.facishare.paas.appframework.common.service.dto.GetPrintTemplate;
import com.facishare.paas.appframework.common.service.dto.PrintTemplate;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 打印模板服务impl
 *
 * <AUTHOR>
 * @date 2021/09/22
 */
@Slf4j
@Service("printTemplateExportService")
public class PrintTemplateExportServiceImpl implements PrintTemplateExportService {
    @Autowired
    private PrintTemplateServiceProxy printTemplateServiceProxy;
    @Override
    public PrintTemplate.Result print(User user, PrintTemplate.Arg arg) {
        return printTemplateServiceProxy.print(user.getTenantId(), user.getUserId(), user.getUserId(), arg);
    }

    @Override
    public GetPrintTemplate.Result findPrintTemplate(User user, GetPrintTemplate.Arg arg) {
        return printTemplateServiceProxy.findPrintTemplate(user.getTenantId(), user.getUserId(), user.getUserId(), arg);
    }
}
