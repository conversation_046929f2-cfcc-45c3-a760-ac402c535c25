package com.facishare.paas.appframework.metadata.layout;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ComponentActions;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.util.LicenseConstants.ModuleCode;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.config.ButtonConfig;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Builder;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.util.ObjectAction.*;
import static com.facishare.paas.appframework.metadata.ButtonUsePageType.Detail;
import static com.facishare.paas.appframework.metadata.ButtonUsePageType.ListBatch;

/**
 * 生成各个组件上的button
 * <p>
 * Created by liyiguang on 2017/11/15.
 */
public class ButtonRender {

    private final IObjectDescribe describe;
    @NonNull
    private final CustomButtonService customButtonService;
    @NonNull
    private final ButtonLogicService buttonLogicService;
    @NonNull
    private final LicenseService licenseService;

    private final OptionalFeaturesService optionalFeaturesService;
    @NonNull
    private final User user;

    private final String layoutType;

    private List<IUdefButton> buttons;  // 当前租户全部可用按钮

    private boolean bigObject;

    @Builder
    public ButtonRender(IObjectDescribe describe,
                        CustomButtonService customButtonService,
                        ButtonLogicService buttonLogicService,
                        LicenseService licenseService,
                        OptionalFeaturesService optionalFeaturesService,
                        String layoutType,
                        User user,
                        boolean bigObject) {
        this.describe = describe;
        this.customButtonService = customButtonService;
        this.buttonLogicService = buttonLogicService;
        this.licenseService = licenseService;
        this.optionalFeaturesService = optionalFeaturesService;
        this.layoutType = layoutType;
        this.user = user;
        this.bigObject = bigObject;
    }

    public ButtonRender render() {
        if (describe == null) {
            buttons = Lists.newArrayList();
        } else {
            buttons = customButtonService.findButtonList(user, describe.getApiName());
        }
        return this;
    }

    public List<IButton> getButtonsByUsePage(ButtonUsePageType usePageType) {
        if (null == usePageType) {
            return Lists.newArrayList();
        }
        List<IButton> result;
        switch (usePageType) {
            case Detail:
                result = getDetailButtons();
                break;
            case ListComponent:
                result = getListComponentButtons();
                break;
            case ListNormal:
                result = getListNormalButtons();
                break;
            case DataList:
            case RelatedList:
                result = getListSingleButtons(usePageType);
                break;
            case ListBatch:
                result = getListBatchButtons();
                break;
            default:
                result = findCustomButtonsByUsePage(ObjectDescribeExt.of(describe), usePageType, user);
        }
        filterButtonWithOptionalFeaturesSwitch(usePageType, result);
        if (Objects.nonNull(describe)) {
            ButtonExt.filterButtonsByBlacklist(result, describe.getApiName());
        }
        ButtonExt.filterButtonsByBlacklist(result, bigObject);
        return handleTerminal(result);  // 设置设计器在哪个端显示, terminals 为空就是都显示
    }

    private void filterButtonWithOptionalFeaturesSwitch(ButtonUsePageType usePageType, List<IButton> buttonList) {
        if (Objects.isNull(describe) || Objects.isNull(optionalFeaturesService) || (usePageType != ListBatch && usePageType != Detail)) {
            return;
        }
        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), describe);
        buttonList.removeIf(button -> !optionalFeaturesSwitch.getIsRelatedTeamEnabled() && ButtonExt.TEAM_MEMBER_BUTTON_API_NAME.contains(button.getName())
                || !optionalFeaturesSwitch.getIsFollowUpDynamicEnabled() && StringUtils.equals(button.getName(), SALE_RECORD.getDefaultButtonApiName()));
    }

    public List<IButton> getDetailButtons() {
        if (describe == null) {
            List<IButton> buttons = LayoutButtons.UserDefinedObj.getActionButtons();
            //填入最后几个预定按钮
            buttons.addAll(LayoutButtons.UserDefinedObj.getTailButtons());
            return buttons;
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        List<IButton> customButtons;
        Set<String> buttonNames = AppFrameworkConfig.webDetailButtonGray(user.getTenantId(), describe.getApiName());    // 系统按钮的灰度配置
        if (CollectionUtils.notEmpty(buttonNames)) {
            //查找自定义按钮
            List<IUdefButton> buttons = customButtonService.filterButtonsForUsePageType(user, null, describe,
                    ButtonUsePageType.Detail.getId(), this.buttons, Collections.emptySet());
            customButtons = filterSystemButton(buttons, buttonNames);   // 要么是自定义的, 要么是系统的且命中灰度
        } else {
            customButtons = findCustomButtonsByUsePage(describeExt, ButtonUsePageType.Detail, user);    // 没有系统按钮灰度, 只返回自定义按钮
        }
        return ButtonOrder.getOrderButtonListByUsePage(customButtons, describeExt, Detail);
    }

    public List<IButton> getListNormalButtons() {
        List<IButton> results;
        if (describe == null) {
            results = ComponentActions.LIST_PAGE_HEADER.getActionButtons();
            return CollectionUtils.sortByGivenOrder(results, ButtonConfig.LIST_LAYOUT_LIST_NORMAL_BUTTONS, IButton::getName);
        }
        results = buttonLogicService.getButtonByComponentActions(user, ComponentActions.LIST_PAGE_HEADER, describe, null, false);
        Set<String> buttonNames = AppFrameworkConfig.listNormalButtonGray(user.getTenantId(), describe.getApiName());
        if (CollectionUtils.notEmpty(buttonNames)) {
            List<IUdefButton> buttons = customButtonService.filterButtonsForUsePageType(user, null, describe,
                    ButtonUsePageType.ListNormal.getId(), this.buttons, Collections.emptySet());
            List<IButton> customButtons = filterSystemButton(buttons, buttonNames);
            results.addAll(customButtons);
        } else {
            List<IButton> customButtons = findCustomButtonsByUsePage(describe, ButtonUsePageType.ListNormal, user);
            results.addAll(customButtons);
        }
        // 订单产品只保留「导出」按钮
        if (Utils.SALES_ORDER_PRODUCT_API_NAME.equals(describe.getApiName())) {
            results.removeIf(button -> LayoutButtonExt.of(button).isSystemButton()
                    && !StringUtils.equalsAny(button.getName(),ObjectAction.BATCH_EXPORT.getDefaultButtonApiName(), ObjectAction.EXPORT_FILE.getDefaultButtonApiName()));
        }
        return CollectionUtils.sortByGivenOrder(results, ButtonConfig.LIST_LAYOUT_LIST_NORMAL_BUTTONS, IButton::getName);
    }

    // 独立站点按钮: 通用按钮只能使用 [创建], 不包括 自定义的通用按钮
    public List<IButton> getListComponentButtons() {
        return ComponentActions.WEBSITE_LIST_PAGE_HEADER.getActionButtons();
    }

    private List<IButton> filterSystemButton(List<IUdefButton> buttons, Set<String> buttonNames) {
        if (CollectionUtils.empty(buttons)) {
            return Lists.newArrayList();
        }
        return buttons.stream().map(ButtonExt::of)
                .filter(ButtonExt::isActive)
                .filter(it -> !it.isSystemButton() || buttonNames.contains(it.getApiName()))
                .map(ButtonExt::toButton)
                .collect(Collectors.toList());
    }

    public List<IButton> getListSingleButtons(ButtonUsePageType usePageType) {
        if (describe == null) {
            return Lists.newArrayList();
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        List<IButton> resultButtons;
        Set<String> buttonNames = AppFrameworkConfig.listSingleButtonGray(user.getTenantId(), describeExt.getApiName());
        if (CollectionUtils.notEmpty(buttonNames)) {
            List<IUdefButton> buttons = customButtonService.filterButtonsForUsePageType(user, null, describe,
                    usePageType.getId(), this.buttons, Collections.emptySet());
            resultButtons = filterSystemButton(buttons, buttonNames);
        } else {
            resultButtons = findCustomButtonsByUsePage(describeExt, usePageType, user);
        }
        return ButtonOrder.getOrderButtonListByUsePage(resultButtons, describeExt, usePageType);
    }

    public List<IButton> getListBatchButtons() {
        if (describe == null) {
            List<IButton> buttons = ButtonConfig.generateButtonsByOrder(DefObjConstants.UDOBJ, user.getTenantId());
            return filterByPartner(buttons, describe);
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        List<IButton> customButtons = findCustomButtonsByUsePage(describeExt, ListBatch, user); // 自定义按钮(__c)
        List<IButton> buttonList = ButtonOrder.getOrderButtonListByUsePage(customButtons, describeExt, ListBatch);  // 添加配置文件中的预置系统按钮
        // 线索对象，没有开启 leads_deduplication_app， 不下发归集按钮
        if (Utils.LEADS_API_NAME.equals(describeExt.getApiName())) {
            Map<String, Boolean> map = licenseService.existModule(user.getTenantId(), Sets.newHashSet(ModuleCode.LEADS_DEDUPLICATION_APP));
            if (!map.getOrDefault(ModuleCode.LEADS_DEDUPLICATION_APP, false)) {
                buttonList.removeIf(it -> COLLECT_TO.getButtonApiName().equals(it.getName()));
            }
        }
        // 未开启了多组织，不下发「领取到本组织」、「分发到组织」
        if (Utils.ACCOUNT_API_NAME.equals(describeExt.getApiName()) && !describeExt.isOpenOrganization()) {
            buttonList.removeIf(it -> CHOOSE_MAIN_DATA.getButtonApiName().equals(it.getName())
                    || ALLOCATE_MAIN_DATA.getButtonApiName().equals(it.getName()));
        }
        // 产品对象列表布局设计器屏蔽「上架」、「下架」按钮
        if (Utils.PRODUCT_API_NAME.equals(describeExt.getApiName())) {
            buttonList.removeIf(it -> ObjectAction.STATUS_OFF.getDefaultButtonApiName().equals(it.getName())
                    || ObjectAction.STATUS_ON.getDefaultButtonApiName().equals(it.getName()));
        }
        // 订单产品只保留「导出」按钮
        if (Utils.SALES_ORDER_PRODUCT_API_NAME.equals(describeExt.getApiName())) {
            buttonList.removeIf(button -> LayoutButtonExt.of(button).isSystemButton()
                    && !StringUtils.equalsAny(button.getName(),ObjectAction.BATCH_EXPORT.getDefaultButtonApiName(), ObjectAction.EXPORT_FILE.getDefaultButtonApiName()));
        }
        return ButtonExt.filterLayoutButtons(describe, filterByPartner(buttonList, describe));
    }

    private List<IButton> filterByPartner(List<IButton> buttons, IObjectDescribe objectDescribe) {
        return buttonLogicService.filterPartnerButtonsWithDescribe(user, objectDescribe, buttons);
    }

    private List<IButton> findCustomButtonsByUsePage(IObjectDescribe describe, ButtonUsePageType usePageType, User user) {
        // 按钮列表的数据进行过滤, 按照页面类型, wheres, 以及对象类型
        List<IUdefButton> buttons = customButtonService.filterButtonsForUsePageType(user, null, describe,
                usePageType.getId(), this.buttons, Collections.emptySet());
        List<IUdefButton> iButtons = buttons.stream().filter(IUdefButton::isActive).collect(Collectors.toList());
        // 过滤出自定义按钮
        return ButtonOrder.filteringUdefButton(iButtons);
    }

    private List<IButton> handleTerminal(List<IButton> buttonList) {
        if (CollectionUtils.empty(buttonList)
                || !LayoutTypes.LIST_LAYOUT.equals(layoutType)) {
            return CollectionUtils.nullToEmpty(buttonList);
        }
        for (IButton button : buttonList) {
            LayoutButtonExt buttonExt = LayoutButtonExt.of(button);
            List<String> terminals = Lists.newArrayList();
            if (ButtonConfig.isListLayoutWebOnlyButtons(buttonExt.getName())
                || ButtonConfig.listLayoutButtonDisplayGray(buttonExt.getName(), LayoutButtonExt.TERMINAL_TYPE_WEB, user.getTenantId())) {
                terminals.add(LayoutButtonExt.TERMINAL_TYPE_WEB);
            } else if (ButtonConfig.listLayoutButtonDisplayGray(buttonExt.getName(), LayoutButtonExt.TERMINAL_TYPE_MOBILE, user.getTenantId())) {
                terminals.add(LayoutButtonExt.TERMINAL_TYPE_MOBILE);
            }
            if (CollectionUtils.notEmpty(terminals)) {
                buttonExt.setTerminal(terminals);
            }
            //按钮只在列表页显示，设置按钮显示位置：列表页通用
            if (!ObjectAction.CREATE.getButtonApiName().equals(buttonExt.getName())) {
                buttonExt.setRenderPageType(Lists.newArrayList(LayoutButtonExt.PAGE_TYPE_LIST));
            }
        }
        return buttonList;
    }

}