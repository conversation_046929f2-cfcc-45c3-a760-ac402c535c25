package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.enterpriserelation.GetRelationDownstreamInfo;
import com.facishare.paas.appframework.core.predef.service.dto.enterpriserelation.SupportEnterpriseRelation;
import com.facishare.paas.appframework.privilege.EnterpriseRelationLogicService;
import com.facishare.paas.appframework.privilege.dto.SupportEnterpriseRelationResult;
import com.google.common.collect.Sets;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * create by z<PERSON><PERSON> on 2021/08/17
 */
@Service
@ServiceModule("enterpriseRelation")
public class ObjectEnterpriseRelationService {
    @Autowired
    private EnterpriseRelationLogicService enterpriseRelationLogicService;

    @ServiceMethod("getRelationDownstreamInfo")
    public GetRelationDownstreamInfo.Result getOuterUserIdByObjectRelationId(GetRelationDownstreamInfo.Arg arg,
                                                                             ServiceContext context) {
        if (Strings.isNullOrEmpty(arg.getDataId())) {
            return GetRelationDownstreamInfo.Result.of(null);
        }
        Map<String, User> result = enterpriseRelationLogicService.getOwnerOutUserByDataIds(context.getUser(),
                arg.getDescribeApiName(), Sets.newHashSet(arg.getDataId()));
        User user = result.get(arg.getDataId());
        return GetRelationDownstreamInfo.Result.of(user);
    }

    @ServiceMethod("isSupport")
    public SupportEnterpriseRelation.Result isSupportEnterpriseRelation(ServiceContext context) {
        SupportEnterpriseRelationResult result = enterpriseRelationLogicService.isSupportEnterpriseRelation(context.getTenantId());
        return SupportEnterpriseRelation.Result.of(result);
    }
}
