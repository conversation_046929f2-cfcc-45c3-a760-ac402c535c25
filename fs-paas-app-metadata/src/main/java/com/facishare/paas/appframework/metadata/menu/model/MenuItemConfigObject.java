package com.facishare.paas.appframework.metadata.menu.model;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class MenuItemConfigObject {
    public MenuItemConfigObject(Boolean validatePrivilege, Boolean validateDescribe, String deviceType) {
        this.deviceType = deviceType;
        this.validatePrivilege = validatePrivilege;
        this.validateDescribe = validateDescribe;
    }

    @JSONField(name = "api_name")
    private String apiName;
    @JSONField(name = "display_name")
    private String displayName;
    @JSONField(name = "number")
    private Integer number;
    @JSONField(name = "icon_index")
    private Integer iconIndex;
    @JSONField(name = "icon_path_home")
    private String iconPathHome;
    @JSONField(name = "icon_path_menu")
    private String iconPathMenu;
    //适用终端类型，all代表所有，web代表只web端使用,mobile代表只终端使用
    @JSONField(name = "device_type")
    private String deviceType;
    //对象类型,predef_obj代表对象、not_obj代表不是对象
    @JSONField(name = "item_type")
    private String itemType;
    //validate_privilege，false代表验证功能权限
    @JSONField(name = "validate_privilege")
    private Boolean validatePrivilege;
    //validate_describe，false代表不验证describe
    @JSONField(name = "validate_describe")
    private Boolean validateDescribe;
    @JSONField(name = "url")
    private String url;
    @JSONField(name = "mobile_config")
    private MobileConfig mobileConfig;
    //是否使用默认url
    @JSONField(name = "useDefaultUrl")
    private Boolean useDefaultUrl;

    @Data
    public static class MobileConfig {
        @JSONField(name = "mobile_add_action")
        private String mobileAddAction;
        @JSONField(name = "mobile_list_action")
        private String mobileListAction;
    }
}
