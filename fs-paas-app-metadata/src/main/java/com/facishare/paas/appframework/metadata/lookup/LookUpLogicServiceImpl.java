package com.facishare.paas.appframework.metadata.lookup;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.FieldMapping;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("lookUpLogicService")
public class LookUpLogicServiceImpl implements LookUpLogicService {

    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private MetaDataFindService metaDataFindService;

    public void handleDataLookUpField(User user, IObjectDescribe describe, List<FieldMapping> fieldMappings, List<IObjectData> dataList) {
        if (noGrayLookUpFieldMapping(user)) {
            return;
        }
        if (CollectionUtils.isEmpty(fieldMappings) || CollectionUtils.isEmpty(dataList)) {
            return;
        }
        Set<FieldMapping> mappings = fieldMappings.stream()
                .filter(x -> !IObjectData.ID.equals(x.getFieldApiName()))
                .filter(x -> describe.containsField(x.getFieldApiName()))
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(mappings)) {
            return;
        }
        Set<String> objectApiNames = mappings.stream().map(FieldMapping::getObjectApiName).collect(Collectors.toSet());
        MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder().user(user).build();
        Map<String, IObjectDescribe> describes = describeLogicService.findObjectsWithoutCopy(queryContext.getUser().getTenantId(), objectApiNames);
        for (FieldMapping fieldMapping : mappings) {
            IObjectDescribe targetDescribe = describes.get(fieldMapping.getObjectApiName());
            validateField(fieldMapping, describe, targetDescribe, dataList);
        }
        for (FieldMapping fieldMapping : mappings) {
            IFieldDescribe lookupFieldDescribe = ObjectDescribeExt.of(describe).getActiveFieldDescribe(fieldMapping.getFieldApiName());
            processLookUpFieldToId(queryContext, describe, lookupFieldDescribe, fieldMapping, dataList);
        }
    }

    private void validateField(FieldMapping fieldMapping, IObjectDescribe objectDescribe, IObjectDescribe targetDescribe, List<IObjectData> dataList) {
        checkTargetField(fieldMapping, targetDescribe);
        checkDataList(fieldMapping, objectDescribe, dataList);
    }

    private void checkDataList(FieldMapping fieldMapping, IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        IFieldDescribe fieldDescribe = ObjectDescribeExt.of(objectDescribe).getActiveFieldDescribe(fieldMapping.getFieldApiName());
        String fieldApiName = FieldDescribeExt.getFieldMappingFieldName(fieldDescribe.getApiName());
        if (!(FieldDescribeExt.of(fieldDescribe).isObjectReferenceField() || FieldDescribeExt.of(fieldDescribe).isMasterDetailField())) {
            return;
        }
        for (IObjectData data : dataList) {
            Object originalValue = data.get(fieldApiName);
            if (originalValue instanceof List) {
                List<String> values = (List<String>) originalValue;
                if (FieldDescribeExt.of(fieldDescribe).isObjectReferenceField() || FieldDescribeExt.of(fieldDescribe).isMasterDetailField()) {
                    if (CollectionUtils.isNotEmpty(values) && values.size() != 1) {
                        log.warn("handleLookUpField validateField checkDataList error,apiName:{},field:{}", objectDescribe.getApiName(), fieldDescribe.getApiName());
                        throw new ValidateException(I18NExt.getOrDefault(I18NKey.OBJECT_FIELD_ONLY_ONE, "对象[{0}]的字段[{1}]的值{2}必须唯一",// ignoreI18n
                                objectDescribe.getDisplayName(), fieldDescribe.getLabel(), values));
                    }
                }
            }
        }
    }

    private void checkTargetField(FieldMapping fieldMapping, IObjectDescribe targetDescribe) {
        IFieldDescribe fieldDescribe = ObjectDescribeExt.of(targetDescribe).getActiveFieldDescribe(fieldMapping.getSpecifiedFieldApiName());
        if (!FieldDescribeExt.of(fieldDescribe).isUnique()) {
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.OBJECT_FIELD_IS_NOT_UNIQUE, "对象[{0}]的字段[{1}]必须是唯一的", targetDescribe.getDisplayName(), fieldDescribe.getLabel()));// ignoreI18n
        }
        if (!FieldDescribeExt.of(fieldDescribe).isIndex()) {
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.OBJECT_FIELD_IS_NOT_SELECTED, "对象[{0}]的字段[{1}]不支持筛选", targetDescribe.getDisplayName(), fieldDescribe.getLabel()));// ignoreI18n
        }
    }

    public List<IObjectData> queryDataBySpecifiedField(MetaDataFindService.QueryContext queryContext, IObjectDescribe describe, List<FieldMapping> fieldMappings, List<IObjectData> dataList) {
        if (noGrayLookUpFieldMapping(queryContext.getUser())) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(fieldMappings) || CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        Set<FieldMapping> mappings = fieldMappings.stream()
                .filter(x -> Objects.equals(x.getObjectApiName(), describe.getApiName()))
                .filter(x -> IObjectData.ID.equals(x.getFieldApiName()))
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(mappings)) {
            return Lists.newArrayList();
        }
        for (FieldMapping fieldMapping : mappings) {
            validateField(fieldMapping, describe, describe, dataList);
        }
        List<IObjectData> result = Lists.newArrayList();
        for (FieldMapping fieldMapping : mappings) {
            IFieldDescribe fieldDescribe = ObjectDescribeExt.of(describe).getActiveFieldDescribe(fieldMapping.getFieldApiName());
            List<IObjectData> objectDataList = processSpecifiedToId(queryContext, fieldDescribe, fieldMapping, dataList);
            if (CollectionUtils.isNotEmpty(objectDataList)) {
                result.addAll(objectDataList);
            }
        }
        return result;
    }

    private List<IObjectData> processSpecifiedToId(MetaDataFindService.QueryContext queryContext, IFieldDescribe fieldDescribe, FieldMapping fieldMapping, List<IObjectData> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(fieldDescribe, fieldMapping, dataList);
        QueryResult<IObjectData> queryResult = metaDataFindService.findBySearchQuery(queryContext, fieldMapping.getObjectApiName(), searchTemplateQuery);
        if (Objects.isNull(queryResult) || CollectionUtils.isEmpty(queryResult.getData())) {
            return Lists.newArrayList();
        }
        List<IObjectData> result = Lists.newArrayList();
        for (IObjectData data : dataList) {
            List<String> fieldValues = getSpecifiedValues(fieldDescribe, data);
            if (CollectionUtils.isEmpty(fieldValues)) {
                continue;
            }
            List<IObjectData> objectDataList = queryResult.getData().stream()
                    .filter(x -> CollectionUtils.containsAll(fieldValues, getFieldValues(x, fieldMapping.getSpecifiedFieldApiName())))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(objectDataList)) {
                result.addAll(objectDataList);
            }
        }
        return result;
    }


    private void processLookUpFieldToId(MetaDataFindService.QueryContext queryContext, IObjectDescribe describe, IFieldDescribe fieldDescribe, FieldMapping fieldMapping, List<IObjectData> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(fieldDescribe, fieldMapping, dataList);
        List<String> projectFields = Lists.newArrayList();
        projectFields.add(IObjectData.ID);
        projectFields.add(fieldMapping.getSpecifiedFieldApiName());
        queryContext.setProjectionFields(projectFields);
        QueryResult<IObjectData> queryResult = metaDataFindService.findBySearchQuery(queryContext, fieldMapping.getObjectApiName(), searchTemplateQuery);
        for (IObjectData data : dataList) {
            if (Objects.isNull(queryResult) || CollectionUtils.isEmpty(queryResult.getData())) {
                data.set(fieldDescribe.getApiName(), null);
                continue;
            }
            List<String> fieldValues = getSpecifiedValues(fieldDescribe, data);
            if (CollectionUtils.isEmpty(fieldValues)) {
                data.set(fieldDescribe.getApiName(), null);
                continue;
            }
            List<String> dataIds = queryResult.getData().stream()
                    .filter(x -> CollectionUtils.containsAll(fieldValues, getFieldValues(x, fieldMapping.getSpecifiedFieldApiName())))
                    .map(DBRecord::getId)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(dataIds)) {
                data.set(fieldDescribe.getApiName(), null);
                continue;
            }
            if (FieldDescribeExt.of(fieldDescribe).isObjectReferenceField() || FieldDescribeExt.of(fieldDescribe).isMasterDetailField()) {
                if (dataIds.size() != 1) {
                    log.warn("processLookUpFieldToId objectReferenceField error,apiName:{},field:{}", fieldDescribe.getDescribeApiName(), fieldDescribe.getApiName());
                    throw new ValidateException(I18NExt.getOrDefault(I18NKey.OBJECT_REFERENCE_FIELD_ONLY_ONE, "对象[{0}]的字段[{1}]的值{2}对应的数据必须唯一",// ignoreI18n
                            describe.getDisplayName(), fieldDescribe.getLabel(), dataIds));
                }
                data.set(fieldDescribe.getApiName(), dataIds.get(0));
                continue;
            }
            if (FieldDescribeExt.of(fieldDescribe).isObjectReferenceManyField()) {
                if (dataIds.size() != fieldValues.size()) {
                    List<String> notExistValues = fieldValues.stream()
                            .filter(x -> queryResult.getData().stream()
                                    .noneMatch(y -> Objects.equals(x, y.get(fieldMapping.getSpecifiedFieldApiName(), String.class))))
                            .collect(Collectors.toList());
                    log.warn("processLookUpFieldToId objectReferenceManyField error,apiName:{},field:{}", fieldDescribe.getDescribeApiName(), fieldDescribe.getApiName());
                    throw new ValidateException(I18NExt.getOrDefault(I18NKey.OBJECT_REFERENCE_MANY_FIELD_NOT_MATCH, "对象[{0}]的字段[{1}]的值{2}对应的数据不存在",// ignoreI18n
                            describe.getDisplayName(), fieldDescribe.getLabel(), notExistValues));
                }
                data.set(fieldDescribe.getApiName(), dataIds);
            }
        }
    }

    private List<String> getSpecifiedValues(IFieldDescribe fieldDescribe, IObjectData data) {
        String fieldName = FieldDescribeExt.getFieldMappingFieldName(fieldDescribe.getApiName());
        return getFieldValues(data, fieldName);
    }

    private List<String> getFieldValues(IObjectData data, String fieldApiName) {
        Object originalValue = data.get(fieldApiName);
        List<String> fieldValues = Lists.newArrayList();
        if (originalValue instanceof List) {
            List<String> targetValues = (List<String>) originalValue;
            if (CollectionUtils.isEmpty(targetValues)) {
                return fieldValues;
            }
            fieldValues.addAll(targetValues);
            return fieldValues;
        }
        if (originalValue instanceof String) {
            String targetValue = (String) originalValue;
            if (StringUtils.isBlank(targetValue)) {
                return fieldValues;
            }
            fieldValues.add(targetValue);
            return fieldValues;
        }
        return fieldValues;
    }

    private SearchTemplateQuery getSearchTemplateQuery(IFieldDescribe fieldDescribe, FieldMapping fieldMapping, List<IObjectData> dataList) {
        Set<String> fieldValues = Sets.newHashSet();
        for (IObjectData data : dataList) {
            List<String> specifiedValues = getSpecifiedValues(fieldDescribe, data);
            fieldValues.addAll(specifiedValues);
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        if (CollectionUtils.isNotEmpty(fieldValues)) {
            SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.IN, fieldMapping.getSpecifiedFieldApiName(), Lists.newArrayList(fieldValues));
        }
        return searchTemplateQuery;
    }

    public boolean containIdFieldMapping(User user, IObjectDescribe describe, List<FieldMapping> fieldMappings) {
        if (noGrayLookUpFieldMapping(user)) {
            return false;
        }
        if (CollectionUtils.isEmpty(fieldMappings)) {
            return false;
        }
        Optional<FieldMapping> fieldMapping = fieldMappings.stream()
                .filter(x -> Objects.equals(x.getObjectApiName(), describe.getApiName()))
                .filter(x -> IObjectData.ID.equals(x.getFieldApiName()))
                .findAny();
        return fieldMapping.isPresent();
    }

    private boolean noGrayLookUpFieldMapping(User user) {
        return !UdobjGrayConfig.isAllow("grayLookUpFieldMapping", user.getTenantId());
    }

    public boolean containLookUpFieldMapping(User user, IObjectDescribe describe, List<FieldMapping> fieldMappings) {
        if (noGrayLookUpFieldMapping(user)) {
            return false;
        }
        if (CollectionUtils.isEmpty(fieldMappings)) {
            return false;
        }
        Set<FieldMapping> mappings = fieldMappings.stream()
                .filter(x -> !IObjectData.ID.equals(x.getFieldApiName()))
                .filter(x -> describe.containsField(x.getFieldApiName()))
                .collect(Collectors.toSet());
        return !CollectionUtils.isEmpty(mappings);
    }
}
