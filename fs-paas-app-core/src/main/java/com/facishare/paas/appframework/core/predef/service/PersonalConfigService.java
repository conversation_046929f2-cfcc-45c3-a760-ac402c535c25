package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
@ServiceModule("personal_config")
public class PersonalConfigService {
    @Autowired
    private ConfigService configService;

    @ServiceMethod("save")
    public ConfigInfo saveConfig(ConfigInfo arg, ServiceContext context) {
        if (Objects.isNull(arg)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        configService.upsertUserConfig(context.getUser(), arg.getKey(), arg.getValue(), ConfigValueType.JSON);
        return arg;
    }

    @ServiceMethod("find")
    public ConfigInfo findConfig(ConfigInfo arg, ServiceContext context) {
        if (Objects.isNull(arg)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        String value = getConfigValue(arg, context);
        return ConfigInfo.builder().key(arg.getKey()).value(value).build();
    }

    private String getConfigValue(ConfigInfo arg, ServiceContext context) {
        return configService.findUserConfig(context.getUser(), arg.getKey());
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ConfigInfo {
        String key;
        String value;
    }

}
