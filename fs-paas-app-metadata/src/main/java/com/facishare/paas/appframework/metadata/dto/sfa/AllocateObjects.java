package com.facishare.paas.appframework.metadata.dto.sfa;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;

public interface AllocateObjects {
    @EqualsAndHashCode(callSuper = true)
    @Data
    class Arg extends ObjectPoolCommon.Arg{
        @SerializedName("OwnerID")
        @JSONField(name = "OwnerID")
        private Integer ownerId;
    }
}
