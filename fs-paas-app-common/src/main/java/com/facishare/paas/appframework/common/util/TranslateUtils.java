package com.facishare.paas.appframework.common.util;

public class TranslateUtils {
    private static final String TRANS_CONVERT_RULE_NAME = "trans_convert_rule_name.";
    private static final String TRANS_CONVERT_RULE_EXCESS_INSPECTION = "trans_convert_rule_excess_inspection.";
    private static final String TRANS_CONVERT_RULE_DATA_RANGE_PROMPT = "trans_convert_rule_data_range_prompt.";


    public static String getConvertRuleNameKey(String ruleApiName) {
        return TRANS_CONVERT_RULE_NAME + ruleApiName;
    }

    public static String getExcessInspectionPromptKey(String ruleApiName) {
        return TRANS_CONVERT_RULE_EXCESS_INSPECTION + ruleApiName;
    }

    public static String getDataRangePromptKey(String ruleApiName) {
        return TRANS_CONVERT_RULE_DATA_RANGE_PROMPT + ruleApiName;
    }

}
