package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.payment.dto.PaymentRecord;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.io.Serializable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Object data Document
 * <p>
 * Created by li<PERSON><PERSON><PERSON> on 2017/9/29.
 */
public class ObjectDataDocument extends DocumentBaseEntity {

    private static final long serialVersionUID = -5688139581783821859L;

    public ObjectDataDocument() {
    }

    private ObjectDataDocument(Map<String, Object> data) {
        super(data);
    }

    public static void projectField(List<ObjectDataDocument> dataList, List<String> fieldProjection) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }

        dataList.forEach(a -> a.projectField(fieldProjection));
    }

    public IObjectData toObjectData() {
        return ObjectDataExt.of(this.data).getObjectData();
    }

    public static ObjectDataDocument getByVersion(IObjectData objectData, Integer version) {
        if (Objects.isNull(objectData)) {
            return null;
        }
        if (Objects.isNull(version) || Objects.isNull(objectData.getVersion())) {
            return of(objectData);
        }
        return version < objectData.getVersion() ? of(objectData) : null;
    }

    public static ObjectDataDocument of(ObjectDataExt objectDataExt) {
        return new ObjectDataDocument(objectDataExt.toMap());
    }

    public static ObjectDataDocument of(IObjectData objectData) {
        if (objectData == null) {
            return null;
        }
        return new ObjectDataDocument(ObjectDataExt.toMap(objectData));
    }

    public static ObjectDataDocument of(Map<String, Object> data) {
        if (data == null) {
            return null;
        }
        return new ObjectDataDocument(data);
    }

    public static List<ObjectDataDocument> ofList(List<IObjectData> dataList) {
        if (dataList == null) {
            return null;
        }
        return dataList.stream().map(ObjectDataDocument::of).collect(Collectors.toList());
    }

    public static List<IObjectData> ofDataList(List<ObjectDataDocument> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return Lists.newArrayList();
        }
        return dataList.stream().map(x -> x.toObjectData()).collect(Collectors.toList());
    }

    public static Map<String, List<ObjectDataDocument>> ofMap(Map<String, List<IObjectData>> dataMap) {
        if (dataMap == null) {
            return null;
        }
        Map<String, List<ObjectDataDocument>> ret = Maps.newHashMap();
        dataMap.forEach((k, v) -> ret.put(k, ofList(v)));
        return ret;
    }

    public static Map<String, List<IObjectData>> ofDataMap(Map<String, List<ObjectDataDocument>> documentMap) {
        if (documentMap == null) {
            return null;
        }
        Map<String, List<IObjectData>> ret = Maps.newHashMap();
        documentMap.forEach((k, v) -> ret.put(k, ofDataList(v)));
        return ret;
    }

    public static ObjectDataDocument ofPayment(PaymentRecord paymentRecord) {
        IObjectData data = new ObjectData();
        data.setName(paymentRecord.getOrderNo());
        data.setId(paymentRecord.getOrderNo());
        data.set("amount", paymentRecord.getAmount());
        data.set("fee", paymentRecord.getFee());
        data.set("payEnterpriseName", paymentRecord.getPayEnterpriseName());
        data.set("remark", paymentRecord.getRemark());
        data.set("payType", paymentRecord.getPayType());
        data.set("finishTime", paymentRecord.getFinishTime());
        data.set("transTime", paymentRecord.getTransTime());
        data.set("relatedObject", paymentRecord.getRelatedObject());
        data.set("relatedObjectName", paymentRecord.getRelatedObjectName());
        data.set("payStatus", paymentRecord.getPayStatus());
        data.set("detailUrl", paymentRecord.getDetailUrl());

        return of(data);
    }

    public static List<ObjectDataDocument> ofPaymentList(List<PaymentRecord> paymentRecords) {
        if (CollectionUtils.empty(paymentRecords)) {
            return Lists.newArrayList();
        }
        return paymentRecords.stream().map(ObjectDataDocument::ofPayment).collect(Collectors.toList());
    }

    public String getId() {
        Object obj = data.get(IObjectData.ID);
        return Objects.isNull(obj) ? null : String.valueOf(obj);
    }



    public static List<IObjectData> fillOldData(List<IObjectData> updatedDataList, List<IObjectData> oldDataList) {
        if (CollectionUtils.empty(updatedDataList) || CollectionUtils.empty(oldDataList)) {
            return updatedDataList;
        }

        Map<String, ObjectDataDocument> map = oldDataList.stream().collect(Collectors.toMap(DBRecord::getId, ObjectDataDocument::of));
        List<IObjectData> list = Lists.newArrayList();
        updatedDataList
                .forEach(a -> {
                    Map<String, Object> data = Maps.newHashMap(ObjectDataDocument.of(a));
                    data.put("old_data", map.get(a.getId()));
                    list.add(ObjectDataExt.of(data).getObjectData());
                });
        return list;
    }
}
