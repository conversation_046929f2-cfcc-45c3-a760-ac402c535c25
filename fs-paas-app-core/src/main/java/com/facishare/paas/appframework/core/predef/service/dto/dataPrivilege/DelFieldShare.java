package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface DelFieldShare {
    @Data
    class Arg {
        @JsonProperty("ruleCodes")
        @SerializedName("ruleCodes")
        @JSONField(name = "M1")
        List<String> ruleCodes;

        @JsonProperty("describeApiName")
        @SerializedName("describeApiName")
        @JSONField(name = "M2")
        String describeApiName;

        @JsonProperty("status")
        @SerializedName("status")
        @J<PERSON><PERSON>ield(name = "M3")
        int status;
    }
    @Builder
    @Data
    class Result {
        @J<PERSON><PERSON>ield(name = "M1")
        boolean success;
    }
}
