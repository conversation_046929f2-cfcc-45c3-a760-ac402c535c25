package com.facishare.paas.appframework.license.quota.checker;

import lombok.AllArgsConstructor;

import java.util.Map;

@AllArgsConstructor
public abstract class BaseQuotaChecker implements QuotaChecker {
    protected Map<String, Integer> paraMap;

    @Override
    public boolean check(QuotaInfo quotaInfo) {
        return getMaxCount(quotaInfo) > quotaInfo.getActualCount();
    }

    @Override
    public abstract int getMaxCount(QuotaInfo quotaInfo);

}
