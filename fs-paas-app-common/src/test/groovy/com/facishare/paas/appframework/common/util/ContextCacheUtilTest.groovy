package com.facishare.paas.appframework.common.util

import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

/**
 * GenerateByAI
 * 测试内容描述：ContextCacheUtil工具类的单元测试
 */
class ContextCacheUtilTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def setup() {
        // 重置测试环境，避免测试间相互影响
        CacheContext.setContext(new CacheContext())
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试openContextCache和isOpenContextCache方法
     */
    def "测试缓存开关操作"() {
        given: "模拟RequestContext"
        def context = Mock(RequestContext)
        GroovyMock(RequestContextManager, global: true)
        
        when: "开启缓存"
        RequestContextManager.getContext() >> context
        ContextCacheUtil.openContextCache()
        
        then: "应设置缓存开启属性"
        1 * context.setAttribute(RequestContext.Attributes.OPEN_CONTEXT_CACHE, true)
        
        when: "检查是否开启缓存时，context中的属性为true"
        RequestContextManager.getContext() >> context
        context.getAttribute(RequestContext.Attributes.OPEN_CONTEXT_CACHE) >> true
        def result = ContextCacheUtil.isOpenContextCache()
        
        then: "应返回true"
        result == true
        
        when: "检查是否开启缓存时，context中的属性为false"
        RequestContextManager.getContext() >> context
        context.getAttribute(RequestContext.Attributes.OPEN_CONTEXT_CACHE) >> false
        result = ContextCacheUtil.isOpenContextCache()
        
        then: "应返回false"
        result == false
        
        when: "检查是否开启缓存时，context为null"
        RequestContextManager.getContext() >> null
        result = ContextCacheUtil.isOpenContextCache()
        
        then: "应返回false"
        result == false
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试缓存功能权限和获取缓存的功能权限
     */
    def "测试功能权限缓存操作"() {
        given: "模拟RequestContext和缓存上下文"
        def context = Mock(RequestContext)
        GroovyMock(RequestContextManager, global: true)
        RequestContextManager.getContext() >> context
        context.getAttribute(RequestContext.Attributes.OPEN_CONTEXT_CACHE) >> true
        
        and: "准备测试数据"
        def userId = "123"
        def functionPrivilege = [
            "func1": true,
            "func2": false
        ]
        def funcCodes = ["func1", "func2", "func3"]
        
        when: "缓存功能权限"
        ContextCacheUtil.cacheFunctionPrivilege(userId, functionPrivilege)
        
        and: "获取缓存的功能权限"
        def cachedPrivilege = ContextCacheUtil.getFunctionPrivilegeCache(userId, funcCodes)
        
        then: "应返回缓存的权限，没有缓存的不返回"
        cachedPrivilege.size() == 2
        cachedPrivilege["func1"] == true
        cachedPrivilege["func2"] == false
        !cachedPrivilege.containsKey("func3")
        
        when: "传入空的功能代码列表"
        cachedPrivilege = ContextCacheUtil.getFunctionPrivilegeCache(userId, [])
        
        then: "应返回空Map"
        cachedPrivilege.isEmpty()
        
        when: "CacheContext为null"
        CacheContext.setContext(null)
        cachedPrivilege = ContextCacheUtil.getFunctionPrivilegeCache(userId, funcCodes)
        
        then: "应返回空Map"
        cachedPrivilege.isEmpty()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试缓存数据权限和获取缓存的数据权限
     */
    def "测试数据权限缓存操作"() {
        given: "模拟RequestContext和缓存上下文"
        def context = Mock(RequestContext)
        GroovyMock(RequestContextManager, global: true)
        RequestContextManager.getContext() >> context
        context.getAttribute(RequestContext.Attributes.OPEN_CONTEXT_CACHE) >> true
        
        and: "准备测试数据"
        def userId = "123"
        def privilegeMap = [
            "data1": "edit",
            "data2": "view"
        ]
        def dataIdList = ["data1", "data2", "data3"]
        
        when: "缓存数据权限"
        ContextCacheUtil.cacheDataPrivilege(userId, privilegeMap)
        
        and: "获取单个数据权限缓存"
        def permission1 = ContextCacheUtil.getDataPrivilegeCache(userId, "data1")
        def permission2 = ContextCacheUtil.getDataPrivilegeCache(userId, "data2")
        def permission3 = ContextCacheUtil.getDataPrivilegeCache(userId, "data3")
        
        then: "应返回正确的权限"
        permission1 == "edit"
        permission2 == "view"
        permission3 == null
        
        when: "获取多个数据权限缓存"
        def permissions = ContextCacheUtil.getDataPrivilegesCache(userId, dataIdList)
        
        then: "应返回缓存的权限"
        permissions.size() == 2
        permissions["data1"] == "edit"
        permissions["data2"] == "view"
        !permissions.containsKey("data3")
        
        when: "传入空的数据ID列表"
        permissions = ContextCacheUtil.getDataPrivilegesCache(userId, [])
        
        then: "应返回空Map"
        permissions.isEmpty()
        
        when: "CacheContext为null"
        CacheContext.setContext(null)
        permissions = ContextCacheUtil.getDataPrivilegesCache(userId, dataIdList)
        
        then: "应返回空Map"
        permissions.isEmpty()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试字段权限缓存操作
     */
    def "测试字段权限缓存操作"() {
        given: "模拟RequestContext和缓存上下文"
        def context = Mock(RequestContext)
        GroovyMock(RequestContextManager, global: true)
        RequestContextManager.getContext() >> context
        context.getAttribute(RequestContext.Attributes.OPEN_CONTEXT_CACHE) >> true
        
        and: "准备测试数据"
        def userId = "123"
        def objectApiName = "Account"
        def permissionMap = [
            "name": 1,
            "phone": 2
        ]
        
        when: "缓存字段权限"
        ContextCacheUtil.cacheFieldPermission(userId, objectApiName, permissionMap)
        
        and: "获取字段权限缓存"
        def result = ContextCacheUtil.getFieldPermissionCache(userId, objectApiName)
        
        then: "应返回缓存的权限"
        result.key == true
        result.value.isPresent()
        result.value.get() == permissionMap
        
        when: "对于没有缓存的对象"
        result = ContextCacheUtil.getFieldPermissionCache(userId, "Contact")
        
        then: "应返回表示缓存不存在的结果"
        result.key == false
        !result.value.isPresent()
        
        when: "CacheContext为null"
        CacheContext.setContext(null)
        result = ContextCacheUtil.getFieldPermissionCache(userId, objectApiName)
        
        then: "应返回缓存不存在的结果"
        result.key == false
        !result.value.isPresent()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试哈希表操作相关方法
     */
    def "测试哈希表操作"() {
        given: "准备缓存上下文"
        def cacheContext = new CacheContext()
        CacheContext.setContext(cacheContext)
        
        when: "设置哈希表"
        def key = "hash_key"
        def hash = [
            "field1": "value1",
            "field2": "value2"
        ]
        ContextCacheUtil.hmset(key, hash)
        
        and: "获取哈希表中的字段"
        def field1Value = ContextCacheUtil.hget(key, "field1")
        def field2Value = ContextCacheUtil.hget(key, "field2")
        def field3Value = ContextCacheUtil.hget(key, "field3")
        
        then: "应返回正确的值"
        field1Value.isPresent()
        field1Value.get() == "value1"
        field2Value.isPresent()
        field2Value.get() == "value2"
        !field3Value.isPresent()
        
        when: "使用hset添加新字段"
        ContextCacheUtil.hset(key, "field3", "value3")
        field3Value = ContextCacheUtil.hget(key, "field3")
        
        then: "新字段应被添加"
        field3Value.isPresent()
        field3Value.get() == "value3"
        
        when: "使用hset修改现有字段"
        ContextCacheUtil.hset(key, "field1", "new_value1")
        field1Value = ContextCacheUtil.hget(key, "field1")
        
        then: "字段值应被更新"
        field1Value.isPresent()
        field1Value.get() == "new_value1"
        
        when: "对一个不存在的key使用hset"
        def newKey = "new_hash_key"
        ContextCacheUtil.hset(newKey, "field1", "value1")
        def newField1Value = ContextCacheUtil.hget(newKey, "field1")
        
        then: "应创建新的哈希表并添加字段"
        newField1Value.isPresent()
        newField1Value.get() == "value1"
        
        when: "CacheContext为null"
        CacheContext.setContext(null)
        def nullResult = ContextCacheUtil.hget(key, "field1")
        
        then: "应返回空Optional"
        !nullResult.isPresent()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试set和get方法
     */
    def "测试set和get方法"() {
        given: "准备缓存上下文"
        def cacheContext = new CacheContext()
        CacheContext.setContext(cacheContext)
        
        when: "设置缓存"
        def key = "simple_key"
        def value = "simple_value"
        ContextCacheUtil.set(key, value)
        
        and: "获取缓存"
        def cachedValue = ContextCacheUtil.get(key)
        
        then: "应返回缓存的值"
        cachedValue == value
        
        when: "设置null值"
        ContextCacheUtil.set(key, null)
        
        then: "缓存不应改变"
        ContextCacheUtil.get(key) == value
        
        when: "CacheContext为null"
        CacheContext.setContext(null)
        def nullResult = ContextCacheUtil.get(key)
        
        then: "应返回null"
        nullResult == null
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getOrElse方法
     */
    def "测试getOrElse方法"() {
        given: "准备缓存上下文和测试数据"
        def cacheContext = new CacheContext()
        CacheContext.setContext(cacheContext)
        def tenantId = "123"
        def describeApiName = "Account"
        def objectDescribe = Mock(IObjectDescribe)
        
        and: "准备supplier"
        def supplier = { -> objectDescribe } as java.util.function.Supplier
        
        when: "缓存中不存在数据时调用getOrElse"
        def result = ContextCacheUtil.getOrElse(tenantId, describeApiName, supplier)
        
        then: "应执行supplier并返回结果"
        result == objectDescribe
        
        when: "缓存中已存在数据时调用getOrElse"
        def key = "object_describe_${tenantId}_${describeApiName}"
        def cachedDescribe = Mock(IObjectDescribe)
        // 重新设置缓存上下文，因为getOrElse会修改缓存
        cacheContext = new CacheContext()
        CacheContext.setContext(cacheContext)
        cacheContext.setCache(key, cachedDescribe)
        result = ContextCacheUtil.getOrElse(tenantId, describeApiName, supplier)
        
        then: "应返回缓存的数据而不执行supplier"
        result == cachedDescribe
        result != objectDescribe
    }
} 