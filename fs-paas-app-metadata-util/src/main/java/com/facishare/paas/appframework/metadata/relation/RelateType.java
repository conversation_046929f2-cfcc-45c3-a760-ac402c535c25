package com.facishare.paas.appframework.metadata.relation;

/**
 * 对象级别的关联关系(用于字段依赖关系图的边)
 * <p>
 * Created by zhouwr on 2018/11/19
 */
public enum RelateType {
    S2S("self to self"),
    M2D("master to detail"),
    D2M("detail to master"),
    L2R("lookup to related"),
    R2L("related to lookup"),
    R2P("related to personnel"),
    R2O("related to department"),
    UNKNOWN("not sure what it is");

    RelateType(String description) {

    }
}
