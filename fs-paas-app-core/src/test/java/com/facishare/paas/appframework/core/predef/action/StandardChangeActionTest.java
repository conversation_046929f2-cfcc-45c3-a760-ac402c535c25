package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.button.action.ValidateFuncAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.SpringBeanHolder;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.domain.DomainPluginManager;
import com.facishare.paas.appframework.core.model.handler.HandlerManager;
import com.facishare.paas.appframework.core.model.plugin.PluginManager;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.filter.ObjectDataFilter;
import com.facishare.paas.appframework.metadata.handler.HandlerLogicService;
import com.facishare.paas.appframework.metadata.repository.model.MtChangeOrderRule;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.impl.I18nServiceImpl;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.reflect.Whitebox;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class StandardChangeActionTest {

    private StandardChangeAction standardChangeAction;

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private InfraServiceFacade infraServiceFacade;

    @Mock
    private ActionContext actionContext;

    @Mock
    private IObjectDescribe objectDescribe;

    @Mock
    private User user;

    @Mock
    private IObjectData objectData;

    @Mock
    private MtChangeOrderRule changeOrderRule;

    @Mock
    private ValidateFuncAction validateFuncAction;

    @Mock
    private SpringBeanHolder springBeanHolder;

    @Mock
    private HandlerManager handlerManager;

    @Mock
    private HandlerLogicService handlerLogicService;

    @Mock
    private PluginManager pluginManager;

    @Mock
    private DomainPluginManager domainPluginManager;

    @BeforeAll
    static void setUpBeforeClass() {
        // 创建 mock 实例
        I18nClient i18nClient = mock(I18nClient.class);
        I18nServiceImpl i18nServiceImpl = mock(I18nServiceImpl.class);

        // 给 mock 设置返回值 - 简化配置，避免具体方法调用
        lenient().when(i18nClient.getAllLanguage()).thenReturn(Lists.newArrayList());

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl);

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient.class, "SINGLETON", i18nClient);
    }

    @BeforeEach
    void setUp() {
        // 先配置Mock行为，避免setInfraServiceFacade时空指针异常
        lenient().when(infraServiceFacade.getSpringBeanHolder()).thenReturn(springBeanHolder);
        lenient().when(springBeanHolder.getHandlerManager()).thenReturn(handlerManager);
        lenient().when(springBeanHolder.getHandlerLogicService()).thenReturn(handlerLogicService);
        lenient().when(springBeanHolder.getPluginManager()).thenReturn(pluginManager);
        lenient().when(springBeanHolder.getDomainPluginManager()).thenReturn(domainPluginManager);

        // 创建被测试对象并手动注入依赖
        standardChangeAction = new StandardChangeAction();
        standardChangeAction.setServiceFacade(serviceFacade);
        standardChangeAction.setInfraServiceFacade(infraServiceFacade);
        
        // 设置其他字段
        ReflectionTestUtils.setField(standardChangeAction, "actionContext", actionContext);
        ReflectionTestUtils.setField(standardChangeAction, "objectDescribe", objectDescribe);
        ReflectionTestUtils.setField(standardChangeAction, "dataList", Lists.newArrayList(objectData));
        
        // 设置默认的arg对象避免空指针异常
        StandardChangeAction.Arg defaultArg = new StandardChangeAction.Arg();
        defaultArg.setObjectDataId("testDataId");
        ReflectionTestUtils.setField(standardChangeAction, "arg", defaultArg);

        lenient().when(actionContext.getUser()).thenReturn(user);
        lenient().when(actionContext.getObjectApiName()).thenReturn("testApiName");
        lenient().when(objectData.getId()).thenReturn("testDataId");
        lenient().when(changeOrderRule.getApiName()).thenReturn("testRuleApiName");
        
        // Mock数据查询方法避免ObjectDataNotFoundException
        lenient().when(serviceFacade.findObjectDataByIdsExcludeInvalid(any(), eq(Lists.newArrayList("testDataId")), eq("testApiName")))
                .thenReturn(Lists.newArrayList(objectData));
    }

    /**
     * 测试内容描述：测试initChangeOrderRule方法正常初始化变更规则
     */
    @Test
    @DisplayName("正常场景 - 初始化变更规则成功")
    void testInitChangeOrderRuleSuccess() {
        // 配置Mock行为
        when(infraServiceFacade.findByDescribeApiName(user, "testApiName"))
                .thenReturn(Lists.newArrayList(changeOrderRule));

        // 执行被测试方法
        assertDoesNotThrow(() -> {
            ReflectionTestUtils.invokeMethod(standardChangeAction, "initChangeOrderRule");
        });

        // 验证Mock交互
        verify(infraServiceFacade).findByDescribeApiName(user, "testApiName");

        // 验证字段被正确设置
        @SuppressWarnings("unchecked")
        List<MtChangeOrderRule> rules = (List<MtChangeOrderRule>) ReflectionTestUtils.getField(standardChangeAction, "changeOrderRules");
        assertNotNull(rules);
        assertEquals(1, rules.size());
        assertEquals(changeOrderRule, rules.get(0));
    }

    /**
     * 测试内容描述：测试initChangeOrderRule方法在没有可用变更规则时抛出ValidateException异常
     */
    @Test
    @DisplayName("异常场景 - 没有可用的变更规则")
    void testInitChangeOrderRuleThrowsValidateExceptionWhenNoRules() {
        // 配置Mock行为
        when(infraServiceFacade.findByDescribeApiName(user, "testApiName"))
                .thenReturn(Lists.newArrayList());

        try (MockedStatic<I18NExt> i18nExtMock = mockStatic(I18NExt.class)) {
            i18nExtMock.when(() -> I18NExt.text(I18NKey.NO_AVAILABLE_CHANGE_RULES))
                    .thenReturn("没有可用的变更规则");

            // 执行并验证异常
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                ReflectionTestUtils.invokeMethod(standardChangeAction, "initChangeOrderRule");
            });

            // 验证异常信息
            assertEquals("没有可用的变更规则", exception.getMessage());
        }

        // 验证Mock交互
        verify(infraServiceFacade).findByDescribeApiName(user, "testApiName");
    }

    /**
     * 测试内容描述：测试validateChangeOrderRuleExistence方法在变更规则不存在时抛出ValidateException异常
     */
    @Test
    @DisplayName("异常场景 - 变更规则不存在")
    void testValidateChangeOrderRuleExistenceThrowsValidateExceptionWhenRuleNotFound() {
        // 准备测试数据
        StandardChangeAction.Arg arg = new StandardChangeAction.Arg();
        arg.setChangeOrderRuleApiName("nonExistentRule");

        ReflectionTestUtils.setField(standardChangeAction, "changeOrderRules", Lists.newArrayList(changeOrderRule));

        try (MockedStatic<I18NExt> i18nExtMock = mockStatic(I18NExt.class)) {
            i18nExtMock.when(() -> I18NExt.text(I18NKey.CHANGE_ORDER_RULE_NOT_FOUND, "nonExistentRule"))
                    .thenReturn("变更规则不存在: nonExistentRule");

            // 执行并验证异常
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                ReflectionTestUtils.invokeMethod(standardChangeAction, "validateChangeOrderRuleExistence", arg);
            });

            // 验证异常信息
            assertEquals("变更规则不存在: nonExistentRule", exception.getMessage());
        }
    }

    /**
     * 测试内容描述：测试validateByLifeStatus方法在对象生命状态不正常时抛出ValidateException异常
     */
    @Test
    @DisplayName("异常场景 - 对象生命状态不正常")
    void testValidateByLifeStatusThrowsValidateExceptionWhenObjectStatusNotNormal() {
        ReflectionTestUtils.setField(standardChangeAction, "objectData", objectData);

        ObjectDataExt objectDataExt = mock(ObjectDataExt.class);

        // 配置Mock行为
        try (MockedStatic<ObjectDataExt> objectDataExtMock = mockStatic(ObjectDataExt.class);
             MockedStatic<I18NExt> i18nExtMock = mockStatic(I18NExt.class)) {

            objectDataExtMock.when(() -> ObjectDataExt.of(objectData)).thenReturn(objectDataExt);
            when(objectDataExt.isNormal()).thenReturn(false);
            i18nExtMock.when(() -> I18NExt.text(I18NKey.UNSUPPORTED_OPERATION))
                    .thenReturn("不支持的操作");

            // 执行并验证异常
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                ReflectionTestUtils.invokeMethod(standardChangeAction, "validateByLifeStatus");
            });

            // 验证异常信息
            assertEquals("不支持的操作", exception.getMessage());
        }
    }

    /**
     * 测试内容描述：测试getFuncPrivilegeCodes方法返回正确的权限代码列表
     */
    @Test
    @DisplayName("正常场景 - 获取功能权限代码")
    void testGetFuncPrivilegeCodesSuccess() {
        // 执行被测试方法
        List<String> result = standardChangeAction.getFuncPrivilegeCodes();

        // 验证结果
        assertNotNull(result);
        assertEquals(StandardAction.Change.getFunPrivilegeCodes(), result);
    }

    /**
     * 测试内容描述：测试getDataPrivilegeIds方法返回正确的数据权限ID列表
     */
    @Test
    @DisplayName("正常场景 - 获取数据权限ID")
    void testGetDataPrivilegeIdsSuccess() {
        // 准备测试数据
        StandardChangeAction.Arg arg = new StandardChangeAction.Arg();
        arg.setObjectDataId("testObjectId");

        // 执行被测试方法
        List<String> result = standardChangeAction.getDataPrivilegeIds(arg);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("testObjectId", result.get(0));
    }

    /**
     * 测试内容描述：测试Arg.fromReChangeOrder静态方法正常创建参数对象
     */
    @Test
    @DisplayName("正常场景 - 从ReChangeOrder创建参数")
    void testArgFromReChangeOrderSuccess() {
        // 执行被测试方法
        StandardChangeAction.Arg result = StandardChangeAction.Arg.fromReChangeOrder(
                "objectDataId", "ruleApiName", "changeDataId");

        // 验证结果
        assertNotNull(result);
        assertEquals("objectDataId", result.getObjectDataId());
        assertEquals("ruleApiName", result.getChangeOrderRuleApiName());
        assertEquals("changeDataId", result.getChangeDataId());
    }

    /**
     * 测试内容描述：测试Result.buildSuccess静态方法正常创建成功结果
     */
    @Test
    @DisplayName("正常场景 - 构建成功结果")
    void testResultBuildSuccessSuccess() {
        // 执行被测试方法
        StandardChangeAction.Result result = StandardChangeAction.Result.buildSuccess();

        // 验证结果
        assertNotNull(result);
    }
} 