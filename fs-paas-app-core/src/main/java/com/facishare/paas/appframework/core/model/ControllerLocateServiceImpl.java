package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.service.ControllerProxy;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.ControllerDefNotFoundError;
import com.facishare.paas.appframework.core.exception.ControllerNewInstanceException;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.predef.controller.StandardController;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * Controller locate service implements
 * <p>
 * Created by liyiguang on 2017/7/3.
 */
@Service
public class ControllerLocateServiceImpl implements ControllerLocateService {

    @Autowired
    private ControllerClassLoader controllerClassLoader;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private SerializerManager serializerManager;
    @Autowired
    private ControllerProxy controllerProxy;

    @Override
    public Controller locateController(ControllerContext context, Object payload) {
        return locateController(context, payload, null);
    }

    @Override
    public Controller locateController(ControllerContext context, Object payload, Class resultType) {
        StopWatch stopWatch = StopWatch.create("locateController");
        try {
            ControllerClassInfo classInfo = findControllerClassInfo(context, resultType == null);
            stopWatch.lap("findControllerClassInfo");
            //不是自定义对象且resultType不为空，本地没有自定义Controller则走rest接口调用
            if (classInfo == null && resultType != null) {
                return RemoteController.builder()
                        .context(context)
                        .arg(payload)
                        .resultType(resultType)
                        .proxy(controllerProxy)
                        .build();
            }
            if (Objects.nonNull(classInfo) && classInfo.isPreDefine()) {
                Class<PreDefineController> controllerClass = controllerClassLoader.loadControllerClass(classInfo);
                stopWatch.lap("loadControllerClass");
                PreDefineController controller = newControllerInstance(context, controllerClass, payload);
                stopWatch.lap("newControllerInstance");
                return controller;
            }
            //TODO: 不支持自定controller
            throw new UnsupportedOperationException("Unsupported custom controller:" + classInfo);
        } finally {
            stopWatch.logSlow(1000);
        }
    }

    PreDefineController newControllerInstance(
            ControllerContext context, Class<PreDefineController> clazz, Object payload) {
        StopWatch stopWatch = StopWatch.create("newControllerInstance");
        try {
            PreDefineController controller = clazz.newInstance();
            stopWatch.lap("newInstance");
            controller.setControllerContext(context);
            controller.setServiceFacade(serviceFacade);
            controller.setInfraServiceFacade(infraServiceFacade);
            if (payload instanceof String) {
                JSONSerializer jsonSerializer = serializerManager.getSerializer(context.getContentType());
                Object arg = jsonSerializer.decode(controller.getArgClass(), (String) payload);
                controller.setArg(arg);
                stopWatch.lap("decodeBody");
            } else {
                controller.setArg(payload);
            }
            List<Class> controllerListenerClassList = controller.getControllerListenerClassList();
            controllerListenerClassList.forEach(controllerListenerClass -> {
                ControllerListener listener = (ControllerListener) serviceFacade.getBean(controllerListenerClass);
                if (listener != null) {
                    controller.addControllerListener(listener);
                }
            });
            stopWatch.lap("getListener");

            return controller;
        } catch (InstantiationException | IllegalAccessException e) {
            throw new ControllerNewInstanceException(SystemErrorCode.CONTROLLER_INSTANTIATION_ERROR, e);
        } finally {
            stopWatch.logSlow(1000);
        }
    }

    /**
     * 1. 如果是预定义对象，加载预定对象Controller
     * 2. 如果是标准Controller 加载标准对象Controller
     *
     * @param context
     * @param isLocal
     * @return
     */
    ControllerClassInfo findControllerClassInfo(ControllerContext context, boolean isLocal) {
        StopWatch stopWatch = StopWatch.create("findControllerClassInfo");

        try {
            PreDefineObject preDefineObject = Strings.isNullOrEmpty(context.getAppId()) ?
                    PreDefineObjectRegistry.getPreDefineObject(context.getObjectApiName()) :
                    PreDefineObjectRegistry.getPreDefineObject(context.getAppId(), context.getObjectApiName());
            stopWatch.lap("getPreDefineObject");

            ControllerClassInfo classInfo = null;

            String destMethodName = getDestMethodName(context);
            if (Objects.nonNull(preDefineObject)) {
                classInfo = preDefineObject.getControllerClassInfo(destMethodName);
                stopWatch.lap("preDefineObject.getControllerClassInfo");
                boolean success = controllerClassLoader.check(classInfo);
                stopWatch.lap("preDefineObject.check");
                if (success) {
                    return classInfo;
                }
            }

            ModuleClassLocator moduleClassLocator = PreDefineObjectRegistry.getModuleClassLocator(context.getAppId());
            stopWatch.lap("getModuleClassLocator");
            if (Objects.nonNull(moduleClassLocator)) {
                classInfo = moduleClassLocator.findControllerClassInfo(destMethodName);
                stopWatch.lap("moduleClassLocator.findControllerClassInfo");
                boolean success = classInfo != null && controllerClassLoader.check(classInfo);
                stopWatch.lap("moduleClassLocator.check");
                if (success) {
                    return classInfo;
                }
            }

            //没有在本地注册，不是自定义对象，并且支持远程调用
            if (classInfo == null && !ObjectDescribeExt.isCustomObject(context.getObjectApiName()) && !isLocal) {
                return null;
            }

            StandardController standardController = StandardController.valueOfController(destMethodName);
            stopWatch.lap("StandardController.valueOfController");
            if (Objects.nonNull(standardController)) {
                return standardController.getControllerClassInfo();
            }

            throw new ControllerDefNotFoundError(SystemErrorCode.CONTROLLER_FOUND_ERROR);
        } finally {
            stopWatch.logSlow(1000);
        }
    }

    private String getDestMethodName(ControllerContext context) {
        return context.getMethodName();
    }
}
