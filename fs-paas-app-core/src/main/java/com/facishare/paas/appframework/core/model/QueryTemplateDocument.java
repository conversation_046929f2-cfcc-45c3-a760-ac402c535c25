package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.metadata.SearchTemplateExt;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.search.SearchTemplate;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by liyiguang on 2017/10/18.
 */
public class QueryTemplateDocument extends DocumentBaseEntity {

    public QueryTemplateDocument() {
    }

    public QueryTemplateDocument(Map<String, Object> data) {
        super(data);
    }

    public ISearchTemplate toSearchTemplate() {
        return new SearchTemplate(data);
    }

    public static List<ISearchTemplate> toSearchTemplateList(List<QueryTemplateDocument> queryTemplateDocuments) {
        if (CollectionUtils.isEmpty(queryTemplateDocuments)) {
            return Lists.newArrayList();
        }
        return queryTemplateDocuments.stream().map(QueryTemplateDocument::toSearchTemplate).collect(Collectors.toList());
    }

    public static QueryTemplateDocument of(ISearchTemplate searchTemplate) {
        if (searchTemplate == null) {
            return new QueryTemplateDocument();
        }
        return new QueryTemplateDocument(SearchTemplateExt.of(searchTemplate).toMap());
    }

    public static List<QueryTemplateDocument> ofList(List<ISearchTemplate> searchTemplates) {
        if (CollectionUtils.isEmpty(searchTemplates)) {
            return Lists.newArrayList();
        }
        return searchTemplates.stream().map(QueryTemplateDocument::of).collect(Collectors.toList());
    }

}
