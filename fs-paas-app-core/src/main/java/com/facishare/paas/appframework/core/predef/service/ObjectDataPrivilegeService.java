package com.facishare.paas.appframework.core.predef.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.common.exception.CRMErrorCode;
import com.facishare.crm.openapi.Utils;
import com.facishare.organization.adapter.api.service.OrganizationWithOuterService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.ConnectionServiceProxy;
import com.facishare.paas.appframework.common.service.ManageGroupService;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.OuterOrganizationService;
import com.facishare.paas.appframework.common.service.dto.*;
import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.common.service.model.ManageGroupType;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.config.ConfigException;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege.TemporaryRights;
import com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege.*;
import com.facishare.paas.appframework.core.util.ObjectUtils;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.util.LicenseConstants.ModuleCode;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.ObjectDescribeFinder;
import com.facishare.paas.appframework.metadata.er.ErOrgManagementControlService;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.metadata.filter.Comparator;
import com.facishare.paas.appframework.metadata.objects.ObjectListConfig;
import com.facishare.paas.appframework.privilege.*;
import com.facishare.paas.appframework.privilege.dto.*;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.TeamRoleInfo;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.enterpriserelation2.arg.ListDownstreamsPublicEmployeesByConditionArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.data.ErDepartmentSimpleData;
import com.fxiaoke.enterpriserelation2.result.ListOuterTenantDetailResult;
import com.fxiaoke.enterpriserelation2.result.data.OuterTenantDetailData;
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.crm.userdefobj.DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PRIVATE;
import static com.facishare.paas.metadata.api.describe.IObjectDescribe.DEFINE_TYPE_CUSTOM;
import static com.facishare.paas.metadata.api.describe.IObjectDescribe.DEFINE_TYPE_PACKAGE;

/**
 * Created by liyiguang on 2017/10/11.
 */
@ServiceModule("data_privilege")
@Component
@Slf4j
// TODO: 2017/10/24 shaobo
public class ObjectDataPrivilegeService {

    @Autowired
    DataPrivilegeService dataPrivilegeService;
    @Autowired
    DataPrivilegeCommonService dataPrivilegeCommonService;
    @Autowired
    ManageGroupService manageGroupService;
    @Autowired
    MetaDataService metaDataService;
    @Autowired
    DescribeLogicService describeLogicService;
    @Autowired
    LicenseService licenseService;
    @Autowired
    OptionalFeaturesService optionalFeaturesService;
    @Autowired
    FunctionPrivilegeServiceImpl functionPrivilegeService;
    @Autowired
    ServiceFacade serviceFacade;
    @Autowired
    ConfigService configService;
    @Autowired
    DataAuthService dataAuthService;
    //    @Autowired
//    UserService userService;
    @Autowired
    MetaDataMiscService metaDataMiscService;
    @Autowired
    LogService logService;
    @Autowired
    private ConnectionServiceProxy connectionServiceProxy;
    @Autowired
    PublicEmployeeService publicEmployeeService;
    @Autowired
    OrganizationWithOuterService organizationWithOuterService;
    @Autowired
    OuterOrganizationService outerOrganizationService;
    @Autowired
    OrgService orgService;
    @Autowired
    UserRoleInfoService userRoleInfoService;
    @Autowired
    TeamMemberRoleService teamMemberRoleService;
    @Autowired
    private ErOrgManagementControlService erOrgManagementControlService;

    private FsGrayReleaseBiz dataAuthGray = FsGrayRelease.getInstance("data-auth");

    /**
     * 修改创建和更新条件共享的验证条件
     *
     * @param tenantId
     * @return
     */
    private boolean enableUpdateFieldShareValidation(String tenantId) {
        return dataAuthGray.isAllow("enableUpdateFieldShareValidation", tenantId);
    }

    private boolean enableBigObjectCommonAndDepartmentPrivilege(String tenantId) {
        return dataAuthGray.isAllow("enableBigObjectCommonAndDepartmentPrivilege", tenantId);
    }

    private boolean enableEntityShareRuleSupportFunction(String tenantId) {
        return dataAuthGray.isAllow("enableEntityShareRuleSupportFunction", tenantId);
    }

    private boolean enableFieldShareRuleSupportFunction(String tenantId) {
        return dataAuthGray.isAllow("enableFieldShareRuleSupportFunction", tenantId);
    }

    private boolean enableDimensionRuleGroupSupportFunction(String tenantId) {
        return dataAuthGray.isAllow("enableDimensionRuleGroupSupportFunction", tenantId);
    }

    private boolean enableBatchDeleteTemporaryPrivilegeSupportFunction(String tenantId) {
        return dataAuthGray.isAllow("enableBatchDeleteTemporaryPrivilegeSupportFunction", tenantId);
    }

    @ServiceMethod("getCommonPrivilegeList")
    public GetCommonPrivilegeList.Result getCommonPrivilegeList(GetCommonPrivilegeList.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering getCommonPrivilegeList(tenantId={},userId={})", tenantId, context.getUser().getUserId());
        // 数据共享对象列表和自定义对象管理列表页使用相同的查询接口，此处不查老对象的描述
        // 权限相关的从对象不应该漏出来
        List<ObjectDataCommonPrivilegeInfo> privilegeList = getObjectDataCommonPrivilegeInfos(context);
        return GetCommonPrivilegeList.Result.builder().ObjectDataPermissionInfos(privilegeList).build();
    }

    @ServiceMethod("updateCommonPrivilegeList")
    public UpdateCommonPrivilegeList.Result updateCommonPrivilegeList(UpdateCommonPrivilegeList.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering updateCommonPrivilegeList(tenantId={},userId={})", tenantId, context.getUser().getUserId());

        dataPrivilegeService.updateCommonPrivilegeList(context.getUser(), arg.getObjectDataPermissionInfos());
        return UpdateCommonPrivilegeList.Result.builder().build();
    }

    @ServiceMethod("addCommonPrivilegeListResult")
    public UpdateCommonPrivilegeList.Result addCommonPrivilegeListResult(UpdateCommonPrivilegeList.Arg arg, ServiceContext context) {
        log.debug("Entering addCommonPrivilegeListResult(tenantId={},userId={})", context.getTenantId(), context.getUser().getUserId());

        dataPrivilegeService.addCommonPrivilegeListResult(context.getUser(), arg.getObjectDataPermissionInfos());
        return UpdateCommonPrivilegeList.Result.builder().build();
    }

    @ServiceMethod("initializeCommonPrivilege")
    public UpdateCommonPrivilegeList.Result initializeCommonPrivilege(ServiceContext context) {
        log.debug("initializeCommonPrivilege tenantId={},userId={}", context.getTenantId(), context.getUser().getUserId());
        List<ObjectDataPermissionInfo> privilegeList = getObjectDataPermissionInfos(context);
        privilegeList.forEach(x -> {
            String permissionType = AppFrameworkConfig.getDataPrivilegeMap().getOrDefault(x.getObjectDescribeApiName(), PRIVATE.getValue());
            x.setPermissionType(permissionType);
        });
        dataPrivilegeService.initCommonPrivilegeListResult(context.getUser(), privilegeList);
        return UpdateCommonPrivilegeList.Result.builder().build();
    }

    @ServiceMethod("getDescribeListByObjectType")
    public Map<String, Set<String>> getDescribeListByObjectType(Map<String, String> queryMap, ServiceContext context) {
        if (StringUtils.isBlank(context.getTenantId())) {
            log.warn("getDescribeListByObjectType tenantId is null");
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        String tenantId = context.getTenantId();

        Map<String, Set<String>> result = Maps.newHashMap();
        result.put("publicObject", Sets.newHashSet());

        ObjectDescribeFinder objectDescribeFinder = ObjectDescribeFinder
                .builder()
                .user(User.systemUser(tenantId))
                .visibleScope(Sets.newHashSet(IObjectDescribe.PUBLIC_OBJECT_VISIBLE_SCOPE))
                .onlyVisibleScope(true)
                .build();

        List<IObjectDescribe> publicObjectList = describeLogicService.findObjectsByTenantId(objectDescribeFinder);
        if (CollectionUtils.empty(publicObjectList)) {
            return result;
        }

        for (IObjectDescribe v : publicObjectList) {
            result.get("publicObject").add(v.getApiName());
        }

        return result;
    }

    @ServiceMethod("queryDimensionIntersectionStatus")
    public Map<String, Object> queryDimensionIntersectionStatus(Map<String, String> queryContent, ServiceContext context) {
        if (StringUtils.isBlank(context.getTenantId())) {
            log.warn("queryDimensionIntersectionStatus tenantId is null");
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        return dataPrivilegeService.queryDimensionIntersectionStatus(context.getUser(), queryContent);
    }

    @ServiceMethod("getAllEntityShareRuleList")
    public QueryAllEntityShareModel.Result getAllEntityShareRuleList(GetAllShareRules.Arg arg, ServiceContext context) {
        if (!enableEntityShareRuleSupportFunction(context.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        String tenantId = context.getTenantId();

        if (arg.getPageSize() == null || (arg.getPageSize() != 20 && arg.getPageSize() != 50 && arg.getPageSize() != 100)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        if (arg.getPageNumber() == null || arg.getPageNumber() < 1 || arg.getPageNumber() > 200) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        if (arg.getPermissionType() == null || (arg.getPermissionType() != -1 && arg.getPermissionType() != 1 && arg.getPermissionType() != 2)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        if (arg.getStatus() == null || (arg.getStatus() != -1 && arg.getStatus() != 0 && arg.getStatus() != 1)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        if (arg.getBasedType() == null || (arg.getBasedType() != 0 && arg.getBasedType() != 1 && arg.getBasedType() != 2)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        //API接口默认查询全部对象数据
        arg.setObjectDescribeApiName("-1");

        //暂时只支持内部共享规则查询
        arg.setOutReceive(false);
        arg.setEntityShareType(null);

        if (CollectionUtils.notEmpty(arg.getEntityShareIds())) {
            return dataPrivilegeService.getAllEntitySharePojoListByIds(arg, null, context.getUser());
        }

        // entices不为空时，查指定对象
        // -1 或者 -2查全部对象
        List<String> describeApiNameList = getAllApiNamesByCodeAndEntices(tenantId, arg.getEntices(), arg.getObjectDescribeApiName());

        return dataPrivilegeService.getAllEntitySharePojoList(arg, describeApiNameList, context.getUser());
    }

    @ServiceMethod("getAllShareRuleList")
    public GetAllShareRules.Result getAllShareRuleList(GetAllShareRules.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();

        // entices不为空时，查指定对象
        // -1 或者 -2查全部对象
        List<String> describeApiNameList = getAllApiNamesByCodeAndEntices(tenantId, arg.getEntices(), arg.getObjectDescribeApiName());

        if (context.getUser() != null && dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(tenantId) && !context.getUser().isOutUser()) {
            ManageGroup manageGroup = manageGroupService.queryManageGroup(context.getUser(), null, ManageGroupType.OBJECT, true);
            if (manageGroup == null) {
                GetAllShareRules.PageInfo emptyPageInfo = new GetAllShareRules.PageInfo();
                emptyPageInfo.setPageSize(NumberUtil.firstNonNull(arg.getPageSize(), NumberUtil.INTEGER_TWENTY));
                emptyPageInfo.setPageCount(NumberUtil.INTEGER_ZERO);
                emptyPageInfo.setPageNumber(NumberUtil.firstNonNull(arg.getPageNumber(), NumberUtil.INTEGER_ONE));
                emptyPageInfo.setTotalCount(NumberUtil.INTEGER_ZERO);
                return GetAllShareRules.Result.builder().page(emptyPageInfo).sharedObjectInfos(Lists.newArrayList()).build();
            }
            if (!manageGroup.isAllSupport()) {
                describeApiNameList = dataPrivilegeCommonService.checkEntityIdsByManageGroup(describeApiNameList, manageGroup);
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(describeApiNameList)) {
                    GetAllShareRules.PageInfo emptyPageInfo = new GetAllShareRules.PageInfo();
                    emptyPageInfo.setPageSize(NumberUtil.firstNonNull(arg.getPageSize(), NumberUtil.INTEGER_TWENTY));
                    emptyPageInfo.setPageCount(NumberUtil.INTEGER_ZERO);
                    emptyPageInfo.setPageNumber(NumberUtil.firstNonNull(arg.getPageNumber(), NumberUtil.INTEGER_ONE));
                    emptyPageInfo.setTotalCount(NumberUtil.INTEGER_ZERO);
                    return GetAllShareRules.Result.builder().page(emptyPageInfo).sharedObjectInfos(Lists.newArrayList()).build();
                }
            }
        }

        QueryAllEntityShareModel.Result restResult = dataPrivilegeService.getAllEntitySharePojoList(arg, describeApiNameList, context.getUser());

        QueryAllEntityShareModel.Result.BaseResultDataContent dataContent = restResult.getResult();
        List<EntitySharePojo> content = dataContent.getContent();
        BasePageInfoDataPrivilege dataPage = dataContent.getPage();

        Set<String> entityShareUserIds = Sets.newHashSet();

        for (EntitySharePojo entitySharePojo : content) {
            if (DataPrivilegeService.USER_TYPE.equals(entitySharePojo.getShareType()) && StringUtils.isNotBlank(entitySharePojo.getShareId())) {
                entityShareUserIds.add(entitySharePojo.getShareId());
            }
            if (DataPrivilegeService.USER_TYPE.equals(entitySharePojo.getReceiveType()) && StringUtils.isNotBlank(entitySharePojo.getReceiveId())) {
                entityShareUserIds.add(entitySharePojo.getReceiveId());
            }
            if (StringUtils.isNotBlank(entitySharePojo.getCreator())) {
                entityShareUserIds.add(entitySharePojo.getCreator());
            }
            if (StringUtils.isNotBlank(entitySharePojo.getModifier())) {
                entityShareUserIds.add(entitySharePojo.getModifier());
            }
        }

        Map<String, UserInfo> entityShareUserInfoMap = orgService.getUserInfoMapByIds(tenantId, context
                .getUser()
                .getUserId(), Lists.newArrayList(entityShareUserIds));

        if (entityShareUserInfoMap == null) {
            entityShareUserInfoMap = Maps.newHashMap();
        }

        List<GetAllShareRules.SharedRuleInfoPojo> sharedObjectInfoPojoList = Lists.newArrayList();

        for (EntitySharePojo entitySharePojo : content) {
            int targetType = specialDealForShareType(entitySharePojo.getReceiveType());
            int dataSourceType = specialDealForShareType(entitySharePojo.getShareType());
            IObjectDescribe objectDescribe = describeLogicService.findObject(tenantId, entitySharePojo.getEntityId());
            if (objectDescribe == null) {
                continue;
            }

            UserInfo creatorUserInfo = entityShareUserInfoMap.get(entitySharePojo.getCreator());
            UserInfo modifierUserInfo = entityShareUserInfoMap.get(entitySharePojo.getModifier());

            GetAllShareRules.SharedRuleInfoPojo pojo = GetAllShareRules.SharedRuleInfoPojo
                    .builder()
                    .dataSourceID(entitySharePojo.getShareId())
                    .dataSourceType(dataSourceType)
                    .targetID(entitySharePojo.getReceiveId())
                    .targetType(targetType)
                    .creator(entitySharePojo.getCreator())
                    .modifier(entitySharePojo.getModifier())
                    .createTime(entitySharePojo.getCreateTime())
                    .modifyTime(entitySharePojo.getModifyTime())
                    .objectDescribeApiName(entitySharePojo.getEntityId())
                    .permissionType(entitySharePojo.getPermission())
                    .status(entitySharePojo.getStatus())
                    .sharedEntity(entitySharePojo.getId())
                    .objectDescribeDisplayName(objectDescribe.getDisplayName())
                    .receiveDeptCascade(entitySharePojo.getReceiveDeptCascade())
                    .targetTenantId(entitySharePojo.getReceiveTenantId())
                    .basedType(entitySharePojo.getBasedType())
                    .build();

            if (DataPrivilegeService.USER_TYPE.equals(entitySharePojo.getShareType()) && entityShareUserInfoMap.get(entitySharePojo.getShareId()) != null) {
                UserInfo dataSourceUserInfo = entityShareUserInfoMap.get(entitySharePojo.getShareId());
                pojo.setDataSourceName(dataSourceUserInfo.getName());
                if (dataSourceUserInfo.getStatus() != null && UserInfo.NORMAL_STATUS != dataSourceUserInfo.getStatus()) {
                    pojo.setDataSourceStatus(DataPrivilegeService.DATA_SHARE_USER_STATUS_STOP);
                } else {
                    pojo.setDataSourceStatus(DataPrivilegeService.DATA_SHARE_USER_STATUS_NORMAL);
                }
            }

            if (DataPrivilegeService.USER_TYPE.equals(entitySharePojo.getReceiveType()) && entityShareUserInfoMap.get(entitySharePojo.getReceiveId()) != null) {
                UserInfo targetUserInfo = entityShareUserInfoMap.get(entitySharePojo.getReceiveId());
                pojo.setTargetName(targetUserInfo.getName());
                if (targetUserInfo.getStatus() != null && UserInfo.NORMAL_STATUS != targetUserInfo.getStatus()) {
                    pojo.setTargetStatus(DataPrivilegeService.DATA_SHARE_USER_STATUS_STOP);
                } else {
                    pojo.setTargetStatus(DataPrivilegeService.DATA_SHARE_USER_STATUS_NORMAL);
                }
            }

            if (creatorUserInfo != null) {
                pojo.setCreatorName(creatorUserInfo.getName());
                if (creatorUserInfo.getStatus() != null && UserInfo.NORMAL_STATUS != creatorUserInfo.getStatus()) {
                    pojo.setCreatorStatus(DataPrivilegeService.DATA_SHARE_USER_STATUS_STOP);
                } else {
                    pojo.setCreatorStatus(DataPrivilegeService.DATA_SHARE_USER_STATUS_NORMAL);
                }
            }

            if (modifierUserInfo != null) {
                pojo.setModifierName(modifierUserInfo.getName());
                if (modifierUserInfo.getStatus() != null && UserInfo.NORMAL_STATUS != modifierUserInfo.getStatus()) {
                    pojo.setModifierStatus(DataPrivilegeService.DATA_SHARE_USER_STATUS_STOP);
                } else {
                    pojo.setModifierStatus(DataPrivilegeService.DATA_SHARE_USER_STATUS_NORMAL);
                }
            }


            sharedObjectInfoPojoList.add(pojo);
        }

        GetAllShareRules.PageInfo pageInfo = new GetAllShareRules.PageInfo();
        pageInfo.setPageSize(dataPage.getPageSize());
        pageInfo.setPageCount(dataPage.getTotalPage());
        pageInfo.setPageNumber(dataPage.getCurrentPage());
        pageInfo.setTotalCount(dataPage.getTotal());

        GetAllShareRules.Result result = GetAllShareRules.Result.builder().page(pageInfo).sharedObjectInfos(sharedObjectInfoPojoList).build();
        result.setPage(pageInfo);
        return result;
    }

    @ServiceMethod("getShareRules")
    public GetShareRules.Result getShareRules(GetShareRules.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering getShareRules(tenantId={},userId={})", tenantId, context.getUser().getUserId());
        // entices不为空时，查指定对象
        List<String> describeApiNameList = handelApiNamesByCodeAndEntices(tenantId, arg.getEntices(), arg.getObjectDescribeApiName());

        if (context.getUser() != null && dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(tenantId) && !context.getUser().isOutUser()) {
            ManageGroup manageGroup = manageGroupService.queryManageGroup(context.getUser(), null, ManageGroupType.OBJECT, true);
            if (manageGroup == null) {
                GetShareRules.PageInfo emptyPageInfo = new GetShareRules.PageInfo();
                emptyPageInfo.setPageSize(NumberUtil.firstNonNull(arg.getPageSize(), NumberUtil.INTEGER_TWENTY));
                emptyPageInfo.setPageCount(NumberUtil.INTEGER_ZERO);
                emptyPageInfo.setPageNumber(NumberUtil.firstNonNull(arg.getPageNumber(), NumberUtil.INTEGER_ONE));
                emptyPageInfo.setTotalCount(NumberUtil.INTEGER_ZERO);
                return GetShareRules.Result.builder().page(emptyPageInfo).sharedObjectInfos(Lists.newArrayList()).build();
            }
            if (!manageGroup.isAllSupport()) {
                describeApiNameList = dataPrivilegeCommonService.checkEntityIdsByManageGroup(describeApiNameList, manageGroup);
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(describeApiNameList)) {
                    GetShareRules.PageInfo emptyPageInfo = new GetShareRules.PageInfo();
                    emptyPageInfo.setPageSize(NumberUtil.firstNonNull(arg.getPageSize(), NumberUtil.INTEGER_TWENTY));
                    emptyPageInfo.setPageCount(NumberUtil.INTEGER_ZERO);
                    emptyPageInfo.setPageNumber(NumberUtil.firstNonNull(arg.getPageNumber(), NumberUtil.INTEGER_ONE));
                    emptyPageInfo.setTotalCount(NumberUtil.INTEGER_ZERO);
                    return GetShareRules.Result.builder().page(emptyPageInfo).sharedObjectInfos(Lists.newArrayList()).build();
                }
            }
        }

        QueryEntityShareModel.Result restResult =
                dataPrivilegeService.getEntitySharePojoResult(arg, describeApiNameList, context.getUser());

        QueryEntityShareModel.Result.BaseResultDataContent dataContent = restResult.getResult();
        List<EntitySharePojo> content = dataContent.getContent();
        BasePageInfoDataPrivilege dataPage = dataContent.getPage();


        List<GetShareRules.SharedObjectInfoPojo> sharedObjectInfoPojoList = Lists.newArrayList();

        for (EntitySharePojo entitySharePojo : content) {
            int targetType = specialDealForShareType(entitySharePojo.getReceiveType());
            int dataSourceType = specialDealForShareType(entitySharePojo.getShareType());
            IObjectDescribe objectDescribe =
                    describeLogicService.findObject(tenantId, entitySharePojo.getEntityId());
            if (objectDescribe == null) {
                continue;
            }
            GetShareRules.SharedObjectInfoPojo pojo = GetShareRules.SharedObjectInfoPojo.builder()
                    .dataSourceID(entitySharePojo.getShareId())
                    .dataSourceType(dataSourceType)
                    .targetID(entitySharePojo.getReceiveId())
                    .targetType(targetType)
                    .objectDescribeApiName(entitySharePojo.getEntityId())
                    .permissionType(entitySharePojo.getPermission())
                    .status(entitySharePojo.getStatus())
                    .sharedEntity(entitySharePojo.getId())
                    .objectDescribeDisplayName(objectDescribe.getDisplayName())
                    .receiveDeptCascade(entitySharePojo.getReceiveDeptCascade())
                    .targetTenantId(entitySharePojo.getReceiveTenantId())
                    .basedType(entitySharePojo.getBasedType())
                    .build();

            sharedObjectInfoPojoList.add(pojo);
        }

        GetShareRules.PageInfo pageInfo = new GetShareRules.PageInfo();
        pageInfo.setPageSize(dataPage.getPageSize());
        pageInfo.setPageCount(dataPage.getTotalPage());
        pageInfo.setPageNumber(dataPage.getCurrentPage());
        pageInfo.setTotalCount(dataPage.getTotal());

        GetShareRules.Result result = GetShareRules.Result.builder()
                .page(pageInfo)
                .sharedObjectInfos(sharedObjectInfoPojoList).build();
        result.setPage(pageInfo);
        return result;
    }

    /**
     * 查询规则组列表
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("getShareRuleGroups")
    public GetShareRuleGroups.Result getShareRuleGroups(GetShareRuleGroups.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering getShareRuleGroups(tenantId={},userId={})", tenantId, context.getUser().getUserId());
        // entices不为空时，查指定对象
        List<String> describeApiNameList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(arg.getEntices())) {
            describeApiNameList.addAll(arg.getEntices());
        }

        QueryEntityShareGroupModel.Result restResult = dataPrivilegeService.getEntityShareGroupPojoResult(arg, describeApiNameList, context.getUser());

        QueryEntityShareGroupModel.Result.BaseResultDataContent dataContent = restResult.getResult();
        List<EntityShareGroupPojo> content = dataContent.getContent();
        BasePageInfoDataPrivilege dataPage = dataContent.getPage();

        Set<String> apiNames = Sets.newHashSet();
        content.forEach(v -> apiNames.addAll(v.getEntityIds()));
        Map<String, String> describeMap = describeLogicService.findDisplayNameByApiNames(tenantId, apiNames);

        List<GetShareRuleGroups.SharedGroupInfoPojo> sharedGroupInfoPojoList = Lists.newArrayList();

        for (EntityShareGroupPojo entityShareGroupPojo : content) {
            List<String> entityIds = entityShareGroupPojo.getEntityIds();
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(entityIds)) {
                continue;
            }

            Map<String, String> entityMap = Maps.newHashMap();
            entityIds.forEach(v -> entityMap.put(v, describeMap.getOrDefault(v, "")));

            GetShareRuleGroups.SharedGroupInfoPojo pojo = GetShareRuleGroups.SharedGroupInfoPojo.builder()
                    .groupId(entityShareGroupPojo.getId())
                    .entityIds(entityShareGroupPojo.getEntityIds())
                    .entityMap(entityMap)
                    .shareIds(entityShareGroupPojo.getShareIds())
                    .receiveIds(entityShareGroupPojo.getReceiveIds())
                    .permissionType(entityShareGroupPojo.getPermission())
                    .status(entityShareGroupPojo.getStatus())
                    .createTime(entityShareGroupPojo.getCreateTime())
                    .modifyTime(entityShareGroupPojo.getModifyTime())
                    .shareDeptCascade(entityShareGroupPojo.getShareDeptCascade())
                    .receiveDeptCascade(entityShareGroupPojo.getReceiveDeptCascade())
                    .entityShareGroupType(entityShareGroupPojo.getEntityShareGroupType())
                    .entityShareType(entityShareGroupPojo.getEntityShareType())
                    .basedType(entityShareGroupPojo.getBasedType())
                    .build();

            sharedGroupInfoPojoList.add(pojo);
        }

        GetShareRuleGroups.PageInfo pageInfo = new GetShareRuleGroups.PageInfo();
        pageInfo.setPageSize(dataPage.getPageSize());
        pageInfo.setPageCount(dataPage.getTotalPage());
        pageInfo.setPageNumber(dataPage.getCurrentPage());
        pageInfo.setTotalCount(dataPage.getTotal());

        GetShareRuleGroups.Result result = GetShareRuleGroups.Result.builder()
                .page(pageInfo)
                .sharedGroupInfos(sharedGroupInfoPojoList).build();
        result.setPage(pageInfo);

        return result;
    }

    @ServiceMethod("getAllShareRuleEntityId")
    public Set<String> getAllShareRuleEntityId(ServiceContext context) {
        String tenantId = context.getTenantId();

        // entices不为空时，查指定对象
        // -1 或者 -2查全部对象
        Set<String> queryAllEntityIds = getAllShareRuleEntityIdsByTenantId(tenantId);

        if (queryAllEntityIds == null) {
            return Sets.newHashSet();
        }

        return queryAllEntityIds;
    }

    @ServiceMethod("getAllShareRuleCount")
    public int getAllShareRuleCount(ServiceContext context) {
        String tenantId = context.getTenantId();
        return dataPrivilegeService.findEntityShareCount(context.getUser(), getAllShareRuleEntityIdsByTenantId(tenantId));
    }

    @ServiceMethod("addOrUpdateEntityShareRules")
    public AddOrUpdateShareRules.Result addOrUpdateEntityShareRules(AddOrUpdateShareRules.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();

        if (!enableEntityShareRuleSupportFunction(tenantId)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        log.debug("Entering addOrUpdateEntityShareRules(tenantId={},userId={})", tenantId, context.getUser().getUserId());

        DataSharing dataSharing = arg.toDataSharing(context.getUser());
        Set<String> queryAllEntityIds = getAllShareRuleEntityIdsByTenantId(tenantId);
        List<String> result = dataPrivilegeService.addOrUpdateEntityShareRules(context.getUser(), dataSharing, queryAllEntityIds, true);
        return AddOrUpdateShareRules.Result.builder().result(result).build();
    }

    @ServiceMethod("addOrUpdateShareRules")
    public AddOrUpdateShareRules.Result addOrUpdateShareRules(AddOrUpdateShareRules.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering addOrUpdateShareRules(tenantId={},userId={})", tenantId, context.getUser().getUserId());

        DataSharing dataSharing = arg.toDataSharing(context.getUser());
        Set<String> queryAllEntityIds = getAllShareRuleEntityIdsByTenantId(tenantId);
        List<String> result = dataPrivilegeService.addOrUpdateShareRules(context.getUser(), dataSharing, queryAllEntityIds);
        return AddOrUpdateShareRules.Result.builder().result(result).build();
    }

    @ServiceMethod("updateEntityShareRulePermission")
    public UpdateShareRulePermission.Result updateEntityShareRulePermission(UpdateShareRulePermission.Arg arg, ServiceContext context) {
        if (!enableEntityShareRuleSupportFunction(context.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (CollectionUtils.empty(arg.getEntityShareIds())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (arg.getPermission() == null) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (arg.getPermission() != 1 && arg.getPermission() != 2) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        String tenantId = context.getTenantId();
        log.debug("Entering updateEntityShareRulePermission(tenantId={},userId={})", tenantId, context.getUser().getUserId());

        boolean result = dataPrivilegeService.updateEntityShareRulePermission(context.getUser(), arg.getEntityShareIds(), arg.getPermission(), true);
        return UpdateShareRulePermission.Result.builder().result(result).build();
    }

    /**
     * 新增或更新规则组
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("addOrUpdateShareRuleGroups")
    public AddOrUpdateShareRuleGroups.Result addOrUpdateShareRuleGroups(AddOrUpdateShareRuleGroups.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering addOrUpdateShareRuleGroups(tenantId={},userId={})", tenantId, context.getUser().getUserId());

        DataShareRuleGroup dataShareRuleGroup = arg.toDataShareRuleGroup(arg);
        List<String> result = dataPrivilegeService.addOrUpdateShareRuleGroups(context.getUser(), dataShareRuleGroup);
        return AddOrUpdateShareRuleGroups.Result.builder().result(result).build();
    }

    /**
     * 删除规则组
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("delShareRuleGroups")
    public DelShareRuleGroups.Result delShareRuleGroups(DelShareRuleGroups.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering delShareRuleGroups(tenantId={},userId={})", tenantId, context.getUser().getUserId());
        boolean result = dataPrivilegeService.delShareRuleGroups(context.getUser(), arg.getSharedRuleGroupIds());
        return DelShareRuleGroups.Result.builder().result(result).build();
    }

    @ServiceMethod("delEntityShareRules")
    public DelShareRules.Result delEntityShareRules(DelShareRules.Arg arg, ServiceContext context) {
        if (!enableEntityShareRuleSupportFunction(context.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (CollectionUtils.empty(arg.getSharedRuleIds())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        return delShareRules(arg, context);
    }

    @ServiceMethod("delShareRules")
    public DelShareRules.Result delShareRules(DelShareRules.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering delShareRules(tenantId={},userId={})", tenantId, context.getUser().getUserId());
        boolean result = dataPrivilegeService.delShareRules(context.getUser(), arg.getSharedRuleIds());
        return DelShareRules.Result.builder().result(result).build();
    }

    @ServiceMethod("enableOrDisableEntityShareRule")
    public EnableOrDisableShareRule.Result enableOrDisableEntityShareRule(EnableOrDisableShareRule.Arg arg, ServiceContext context) {
        if (!enableEntityShareRuleSupportFunction(context.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (CollectionUtils.empty(arg.getSharedRuleIds())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (arg.getStatus() == null) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (arg.getStatus() != 0 && arg.getStatus() != 1) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        return enableOrDisableShareRule(arg, context);
    }

    @ServiceMethod("enableOrDisableShareRule")
    public EnableOrDisableShareRule.Result enableOrDisableShareRule(EnableOrDisableShareRule.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering enableOrDisableShareRule(tenantId={},userId={})", tenantId, context.getUser().getUserId());

        boolean result = dataPrivilegeService.enableOrDisableShareRule(context.getUser(), arg.getSharedRuleIds(), arg.getStatus());
        return EnableOrDisableShareRule.Result.builder().result(result).build();
    }

    /**
     * 停用或启用规则组
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("enableOrDisableShareRuleGroup")
    public EnableOrDisableShareRuleGroup.Result enableOrDisableShareRuleGroup(EnableOrDisableShareRuleGroup.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering enableOrDisableShareRuleGroup(tenantId={},userId={})", tenantId, context.getUser().getUserId());

        boolean result = dataPrivilegeService.enableOrDisableShareRuleGroup(context.getUser(), arg.getSharedRuleGroupIds(), arg.getStatus());

        return EnableOrDisableShareRuleGroup.Result.builder().result(result).build();
    }

    @ServiceMethod("createDimensionRuleGroup")
    public CreateDimensionRuleGroup.Result createDimensionRuleGroup(CreateDimensionRuleGroup.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering createDimensionRuleGroup(tenantId={},userId={})", tenantId, context.getUser().getUserId());

        CreateDimensionRuleGroupModel.Result result = dataPrivilegeService.createDimensionRuleGroup(context.getUser(), arg.getEntityId(), arg.getRuleParse(), arg.getRuleType(), arg.getPermission(), arg.getRemark(), arg.getRules(), arg.getReceives());
        return CreateDimensionRuleGroup.Result.builder().ruleCode(result.getResult()).success(result.isSuccess()).build();
    }

    @ServiceMethod("createDimensionRuleGroupInfo")
    public CreateDimensionRuleGroup.Result createDimensionRuleGroupInfo(CreateDimensionRuleGroup.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering createDimensionRuleGroup(tenantId={},userId={})", tenantId, context.getUser().getUserId());

        if (!enableDimensionRuleGroupSupportFunction(tenantId)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        Map<String, String> propertyMap = Maps.newHashMap();
        propertyMap.put("isApiRequest", "true");
        propertyMap.put("requestLang", queryRequestLang(context));

        CreateDimensionRuleGroupModel.Result result = dataPrivilegeService.createDimensionRuleGroupInfo(context.getUser(), arg.getEntityId(), arg.getRuleParse(), arg.getRuleType(), arg.getPermission(), arg.getRemark(), propertyMap, arg.getRules(), arg.getReceives());
        return CreateDimensionRuleGroup.Result.builder().ruleCode(result.getResult()).success(result.isSuccess()).build();
    }

    @ServiceMethod("updateDimensionRuleGroup")
    public UpdateDimensionRuleGroup.Result updateDimensionRuleGroup(UpdateDimensionRuleGroup.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering updateDimensionRuleGroup(tenantId={},userId={})", tenantId, context.getUser().getUserId());

        UpdateDimensionRuleGroupModel.Result result = dataPrivilegeService.updateDimensionRuleGroup(context.getUser(), arg.getRuleCode(), arg.getRuleParse(), arg.getPermission(), arg.getRemark(), arg.getRules(), arg.getReceives());
        return UpdateDimensionRuleGroup.Result.builder().ruleCode(result.getResult()).success(result.isSuccess()).build();
    }

    @ServiceMethod("updateDimensionRuleGroupInfo")
    public UpdateDimensionRuleGroup.Result updateDimensionRuleGroupInfo(UpdateDimensionRuleGroup.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering updateDimensionRuleGroup(tenantId={},userId={})", tenantId, context.getUser().getUserId());

        if (!enableDimensionRuleGroupSupportFunction(tenantId)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        Map<String, String> propertyMap = Maps.newHashMap();
        propertyMap.put("isApiRequest", "true");
        propertyMap.put("requestLang", queryRequestLang(context));

        UpdateDimensionRuleGroupModel.Result result = dataPrivilegeService.updateDimensionRuleGroupInfo(context.getUser(), arg.getRuleCode(), arg.getRuleParse(), arg.getPermission(), arg.getRemark(), propertyMap, arg.getRules(), arg.getReceives());
        return UpdateDimensionRuleGroup.Result.builder().ruleCode(result.getResult()).success(result.isSuccess()).build();
    }

    @ServiceMethod("queryDimensionRuleGroup")
    public QueryDimensionRuleGroup.Result queryDimensionRuleGroup(QueryDimensionRuleGroup.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering queryDimensionRuleGroup(tenantId={},userId={})", tenantId, context.getUser().getUserId());

        QueryDimensionRuleGroupModel.Result restResult = dataPrivilegeService.queryDimensionRuleGroup(context.getUser(), arg.getReceiveIds(), arg.getReceiveType(), arg.getReceiveTenantId(), arg.getPermissionType(), arg.getCreateTimeRange(), arg.getModifyTimeRange(), arg.getSortType(), arg.getSortOrder(), arg.getPageNumber(), arg.getPageSize());

        QueryDimensionRuleGroupModel.Result.QueryDimensionRuleGroupPojoResult dataContent = restResult.getResult();
        List<DimensionRuleGroupPojo> content = dataContent.getContent();

        PageInfo dataPage = dataContent.getPage();
        QueryDimensionRuleGroup.PageInfo pageInfo = new QueryDimensionRuleGroup.PageInfo();
        pageInfo.setPageSize(dataPage.getPageSize());
        pageInfo.setPageCount(dataPage.getTotalPage());
        pageInfo.setPageNumber(dataPage.getCurrentPage());
        pageInfo.setTotalCount(dataPage.getTotal());
        return QueryDimensionRuleGroup.Result.builder().page(pageInfo).dimensionRuleGroupList(content).build();
    }

    @ServiceMethod("queryDimensionRuleGroupInfo")
    public QueryDimensionRuleGroup.Result queryDimensionRuleGroupInfo(QueryDimensionRuleGroup.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering queryDimensionRuleGroup(tenantId={},userId={})", tenantId, context.getUser().getUserId());

        if (!enableDimensionRuleGroupSupportFunction(tenantId)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        Map<String, String> propertyMap = Maps.newHashMap();
        propertyMap.put("isApiRequest", "true");
        propertyMap.put("requestLang", queryRequestLang(context));

        QueryDimensionRuleGroupModel.Result restResult = dataPrivilegeService.queryDimensionRuleGroupInfo(context.getUser(), arg.getReceiveIds(), arg.getReceiveType(), arg.getReceiveTenantId(), arg.getPermissionType(), propertyMap, arg.getCreateTimeRange(), arg.getModifyTimeRange(), arg.getSortType(), arg.getSortOrder(), arg.getPageNumber(), arg.getPageSize());

        QueryDimensionRuleGroupModel.Result.QueryDimensionRuleGroupPojoResult dataContent = restResult.getResult();
        List<DimensionRuleGroupPojo> content = dataContent.getContent();

        PageInfo dataPage = dataContent.getPage();
        QueryDimensionRuleGroup.PageInfo pageInfo = new QueryDimensionRuleGroup.PageInfo();
        pageInfo.setPageSize(dataPage.getPageSize());
        pageInfo.setPageCount(dataPage.getTotalPage());
        pageInfo.setPageNumber(dataPage.getCurrentPage());
        pageInfo.setTotalCount(dataPage.getTotal());
        return QueryDimensionRuleGroup.Result.builder().page(pageInfo).dimensionRuleGroupList(content).build();
    }

    @ServiceMethod("queryDimensionRuleCodeList")
    public QueryDimensionRuleCodeList.Result queryDimensionRuleCodeList(QueryDimensionRuleCodeList.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering queryDimensionRuleCodeList(tenantId={},userId={})", tenantId, context.getUser().getUserId());

        if (CollectionUtils.empty(arg.getReceiveIds()) || arg.getReceiveType() == null) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        QueryDimensionRuleCodeListModel.Result result = dataPrivilegeService.queryDimensionRuleCodeList(context.getUser(), arg.getReceiveIds(), arg.getReceiveType(), arg.getReceiveTenantId());
        return QueryDimensionRuleCodeList.Result.builder().ruleCodeList(result.getResult()).success(result.isSuccess()).build();
    }

    @ServiceMethod("deleteDimensionRuleGroup")
    public DeleteDimensionRuleGroup.Result deleteDimensionRuleGroup(DeleteDimensionRuleGroup.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering deleteDimensionRuleGroup(tenantId={},userId={})", tenantId, context.getUser().getUserId());

        boolean result = dataPrivilegeService.deleteDimensionRuleGroup(context.getUser(), arg.getRuleCodes());
        return DeleteDimensionRuleGroup.Result.builder().success(result).build();
    }

    @ServiceMethod("deleteDimensionRuleGroupInfo")
    public DeleteDimensionRuleGroup.Result deleteDimensionRuleGroupInfo(DeleteDimensionRuleGroup.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering deleteDimensionRuleGroup(tenantId={},userId={})", tenantId, context.getUser().getUserId());

        if (!enableDimensionRuleGroupSupportFunction(tenantId)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        Map<String, String> propertyMap = Maps.newHashMap();
        propertyMap.put("isApiRequest", "true");
        propertyMap.put("requestLang", queryRequestLang(context));

        boolean result = dataPrivilegeService.deleteDimensionRuleGroupInfo(context.getUser(), arg.getRuleCodes(), propertyMap);
        return DeleteDimensionRuleGroup.Result.builder().success(result).build();
    }

    @ServiceMethod("getTeamMember")
    public GetTeamMember.Result getTeamMember(GetTeamMember.Arg arg, ServiceContext context) {
        StopWatch stopWatch = StopWatch.create("data_privilege.getTeamMember");

        String tenantId = context.getUser().getTenantId();
        String userId = context.getUser().getUserId();
        String dataID = arg.getDataID();
        String objectDescribeApiName = arg.getObjectDescribeApiName();
        log.debug("Entering getTeamMember(tenantId={},userId={},DataId={},objectDescribeApiName={})",
                tenantId, userId, dataID, objectDescribeApiName);
        if (Strings.isNullOrEmpty(dataID)) {
            log.warn("parameter is null in checkCountLimit,tenantId:{}, userId:{}, arg:{}",
                    context.getTenantId(), context.getUser().getUserId(), arg);
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        IObjectData objectData = metaDataService.findObjectDataIncludeDeletedIgnoreFormula(context.getUser(), dataID, objectDescribeApiName);
        stopWatch.lap("findData");
        List<TeamMemberInfoPoJo> relevantTeamFromObjectData = ObjectDataExt.of(objectData).getRelevantTeamFromObjectData();
        if (TeamMember.isTeamRoleGray(context.getTenantId())) {
            List<TeamRoleInfo> teamRoleInfos = teamMemberRoleService.queryTeamRoleInfo(context.getTenantId(), objectDescribeApiName);
            Map<String, String> teamRoleInfoMap = teamRoleInfos.stream()
                    .collect(Collectors.toMap(TeamRoleInfo::getRoleType, getTeamRoleNameFunction(), (x, y) -> x));
            relevantTeamFromObjectData.forEach(relevantTeam -> {
                List<String> roleNameList = CollectionUtils.nullToEmpty(relevantTeam.getTeamMemberRoleList()).stream()
                        .map(teamRoleInfoMap::get)
                        .collect(Collectors.toList());
                relevantTeam.setTeamMemberRoleNames(String.join(",", roleNameList));
            });
        }


        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_770)) {
            Set<String> notEmployeeTypes = Sets.newHashSet(TeamMember.MemberType.GROUP.getValue(),
                    TeamMember.MemberType.DEPARTMENT.getValue(),
                    TeamMember.MemberType.ROLE.getValue(),
                    TeamMember.MemberType.OUT_TENANT.getValue(),
                    TeamMember.MemberType.OUT_TENANT_GROUP.getValue());
            List<TeamMemberInfoPoJo> teamMemberInfoPoJos = relevantTeamFromObjectData.stream()
                    .filter(x -> notEmployeeTypes.contains(x.getTeamMemberType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.notEmpty(teamMemberInfoPoJos)) {
                throw new ValidateException(I18N.text(I18NKey.TEAM_MEMBER_UPGRADE));
            }
        }

        //校验当前用户是否具有编辑团队成员的权限
        boolean hasPermission = false;
        IObjectDescribe describe = describeLogicService.findObject(tenantId, objectDescribeApiName);
        stopWatch.lap("findObject");
        List<ObjectAction> actions = serviceFacade.filterAction(context.getUser(), describe, objectData, Lists.newArrayList(ObjectAction.EDIT_TEAM_MEMBER));
        stopWatch.lap("filterAction");
        if (CollectionUtils.notEmpty(actions)) {
            hasPermission = true;
        }
        if (RequestUtil.isMobileRequest() && Utils.PRODUCT_API_NAME.equals(arg.getObjectDescribeApiName())) {
            hasPermission = false;
        }
        if (!arg.isIncludeOutMember()) {
            CollectionUtils.nullToEmpty(relevantTeamFromObjectData).removeIf(TeamMemberInfoPoJo::isOutTeamMember);
        }
        // 补充团队成员信息
        fillTenantName(relevantTeamFromObjectData, context.getUser());
        stopWatch.lap("fillTenantName");

        List<IButton> buttons = getTeamMemberButtons(objectData, hasPermission, describe, context.getUser());
        stopWatch.lap("findButton");
        // 前端是否展示外部相关团队组件
        boolean isShowingOutTeamMember = isShowingOutTeamMember(objectData, tenantId);
        stopWatch.lap("isShowingOutTeamMember");

        stopWatch.logSlow(300);

        return GetTeamMember.Result.builder()
                .teamMemberInfos(relevantTeamFromObjectData)
                .hasEditPermission(hasPermission)
                .buttons(ButtonDocument.fromButtons(buttons))
                .isShowingOutTeamMember(isShowingOutTeamMember)
                .isShowingErDepartmentTeamMember(isShowingOutTeamMember && erOrgManagementControlService.interconnectDeptTeamMemberGray(context.getTenantId()))
                .disableSelectAll(AppFrameworkConfig.isTeamMemberDisableSelectAll(context.getTenantId()))
                .build();
    }

    @NotNull
    private static Function<TeamRoleInfo, String> getTeamRoleNameFunction() {
        return role -> role.getStatus() == 1 ? role.getRoleName() : I18NExt.text(I18NKey.DISABLED, role.getRoleName());
    }

    private boolean isShowingOutTeamMember(IObjectData objectData, String tenantId) {
        if (CollectionUtils.notEmpty(objectData.getOutOwner())) {
            return true;
        }
        if (AppFrameworkConfig.isShowingOutTeamMemberGray(tenantId)) {
            return serviceFacade.existModule(tenantId, Sets.newHashSet(ModuleCode.INTERCONNECT_APP_BASIC_APP))
                    .getOrDefault(ModuleCode.INTERCONNECT_APP_BASIC_APP, false);
        }
        return false;
    }

    // 补充相关团队成员name信息
    private void fillTenantName(List<TeamMemberInfoPoJo> relevantTeamFromObjectData, User user) {
        StopWatch stopWatch = StopWatch.create("fillOutTenantName");
        // 下游用户查看相关团队，只能看到同一个企业的人员和自己所属的企业
        if (user.isOutUser()) {
            relevantTeamFromObjectData.removeIf(it -> it.isOutTeamMember()
                    && !Objects.equals(user.getOutTenantId(), it.getOutTenantId())
                    && (TeamMember.MemberType.EMPLOYEE.getValue().equals(TeamMember.MemberType.of(it.getTeamMemberType()).getValue())
                    || TeamMember.MemberType.OUT_TENANT.getValue().equals(TeamMember.MemberType.of(it.getTeamMemberType()).getValue())));
        }

        Map<String, List<String>> teamMemberIds = getTeamMemberIds(relevantTeamFromObjectData);
        if (CollectionUtils.empty(teamMemberIds)) {
            return;
        }

        for (String teamMemberType : teamMemberIds.keySet()) {
            if (CollectionUtils.empty(teamMemberIds.get(teamMemberType))) {
                continue;
            }
            //获取相关团队成员id，去重
            List<String> uniqueMemberIds = teamMemberIds.get(teamMemberType).stream().distinct().collect(Collectors.toList());
            if (TeamMember.MemberType.EMPLOYEE.getValue().equals(teamMemberType)) {
                Map<String, UserInfo> userInfos = orgService.getUserNameByIds(user.getTenantId(), user.getUserId(), uniqueMemberIds)
                        .stream().collect(Collectors.toMap(UserInfo::getId, x -> x));
                if (CollectionUtils.empty(userInfos)) {
                    continue;
                }
                relevantTeamFromObjectData.stream()
                        .filter(x -> Objects.equals(x.getTeamMemberType(), teamMemberType))
                        .forEach(x -> {
                            List<String> teamMemberEmployee = x.getTeamMemberEmployee();
                            if (CollectionUtils.empty(teamMemberEmployee)) {
                                return;
                            }
                            UserInfo userInfo = userInfos.get(teamMemberEmployee.get(0));
                            if (Objects.isNull(userInfo)) {
                                return;
                            }
                            x.setTeamMemberName(userInfo.getNickname());
                            if (x.isOutTeamMember()) {
                                x.setOutUserName(userInfo.getNickname());
                                x.setOutUserProfile(userInfo.getPicAddr());
                            }
                        });
            }
            if (TeamMember.MemberType.GROUP.getValue().equals(teamMemberType)) {
                Map<String, String> groupInfos = orgService.getGroupInfoByIds(user.getTenantId(), user.getUserId(), uniqueMemberIds)
                        .stream().collect(Collectors.toMap(QueryGroupByIds.UserGroupInfo::getId, QueryGroupByIds.UserGroupInfo::getName));
                if (CollectionUtils.empty(groupInfos)) {
                    continue;
                }
                relevantTeamFromObjectData.stream()
                        .filter(x -> Objects.equals(x.getTeamMemberType(), teamMemberType))
                        .forEach(x -> {
                            List<String> teamMemberEmployee = x.getTeamMemberEmployee();
                            if (CollectionUtils.empty(teamMemberEmployee)) {
                                return;
                            }
                            String groupName = groupInfos.get(teamMemberEmployee.get(0));
                            if (StringUtils.isBlank(groupName)) {
                                return;
                            }
                            x.setTeamMemberName(groupName);
                        });
            }
            if (TeamMember.MemberType.DEPARTMENT.getValue().equals(teamMemberType)) {
                Map<String, String> deptInfos = orgService.getDeptInfoByIdsAndStatus(user.getTenantId(), uniqueMemberIds, QueryDeptInfoByDeptIds.DeptStatusEnum.ALL)
                        .stream().collect(Collectors.toMap(QueryDeptInfoByDeptIds.DeptInfo::getDeptId, QueryDeptInfoByDeptIds.DeptInfo::getDeptName));
                if (CollectionUtils.empty(deptInfos)) {
                    continue;
                }
                relevantTeamFromObjectData.stream()
                        .filter(x -> Objects.equals(x.getTeamMemberType(), teamMemberType))
                        .forEach(x -> {
                            List<String> teamMemberEmployee = x.getTeamMemberEmployee();
                            if (CollectionUtils.empty(teamMemberEmployee)) {
                                return;
                            }
                            String deptName = deptInfos.get(teamMemberEmployee.get(0));
                            if (StringUtils.isBlank(deptName)) {
                                return;
                            }
                            x.setTeamMemberName(deptName);
                        });

            }
            if (TeamMember.MemberType.ROLE.getValue().equals(teamMemberType)) {
                Map<String, String> roleInfos = userRoleInfoService.queryAllRoleInfoByCodes(user, uniqueMemberIds)
                        .stream().collect(Collectors.toMap(UserRoleInfo::getRoleCode, UserRoleInfo::getRoleName));
                if (CollectionUtils.empty(roleInfos)) {
                    continue;
                }
                relevantTeamFromObjectData.stream()
                        .filter(x -> Objects.equals(x.getTeamMemberType(), teamMemberType))
                        .forEach(x -> {
                            List<String> teamMemberEmployee = x.getTeamMemberEmployee();
                            if (CollectionUtils.empty(teamMemberEmployee)) {
                                return;
                            }
                            String roleName = roleInfos.get(teamMemberEmployee.get(0));
                            if (StringUtils.isBlank(roleName)) {
                                return;
                            }
                            x.setTeamMemberName(roleName);
                        });

            }
            if (TeamMember.MemberType.OUT_TENANT_GROUP.getValue().equals(teamMemberType)) {
                Map<String, String> tenantGroupInfos = orgService.getTenantGroupInfoByIds(user.getTenantId(), user.getUserIdOrOutUserIdIfOutUser(), uniqueMemberIds)
                        .stream().filter(Objects::nonNull).collect(Collectors.toMap(QueryTenantGroupByIds.TenantGroupInfo::getId, QueryTenantGroupByIds.TenantGroupInfo::getName));
                if (CollectionUtils.empty(tenantGroupInfos)) {
                    continue;
                }
                relevantTeamFromObjectData.stream()
                        .filter(x -> Objects.equals(x.getTeamMemberType(), teamMemberType))
                        .forEach(x -> {
                            List<String> teamMemberEmployee = x.getTeamMemberEmployee();
                            if (CollectionUtils.empty(teamMemberEmployee)) {
                                return;
                            }
                            String tenantGroupName = tenantGroupInfos.get(teamMemberEmployee.get(0));
                            if (StringUtils.isBlank(tenantGroupName)) {
                                return;
                            }
                            x.setTeamMemberName(tenantGroupName);
                        });
            }
            if (TeamMember.MemberType.INTERCONNECT_DEPARTMENT.getValue().equals(teamMemberType)) {
                fillErDepartmentTeamMemberDisplayNames(relevantTeamFromObjectData, user, uniqueMemberIds);
            }
        }

        List<String> outTenantIds = getOutTenantIdsFromTeamMembers(relevantTeamFromObjectData);

        if (CollectionUtils.notEmpty(outTenantIds)) {
            // 调用互联，批量查询外部企业信息
            BatchGetEnterpriseCards.Arg argForTenant = BatchGetEnterpriseCards.Arg.builder()
                    .outerTenantIds(outTenantIds.stream().map(Integer::valueOf).collect(Collectors.toList()))
                    .upstreamTenantId(Integer.valueOf(user.getTenantId()))
                    .build();
            BatchGetEnterpriseCards.Result result = connectionServiceProxy.batchGetEnterpriseCards(user.getTenantId(), "x_app_framework", argForTenant);
            stopWatch.lap("batchGetEnterpriseCards");
            if (result.isSuccess()) {
                for (TeamMemberInfoPoJo member : relevantTeamFromObjectData) {
                    // 补充企业信息
                    for (BatchGetEnterpriseCards.Card card : CollectionUtils.nullToEmpty(result.getData())) {
                        if (TeamMember.MemberType.OUT_TENANT.getValue().equals(member.getTeamMemberType())) {
                            if (CollectionUtils.empty(member.getTeamMemberEmployee())) {
                                continue;
                            }
                            if (StringUtils.equals(String.valueOf(card.getOuterTenantId()), member.getTeamMemberEmployee().get(0))) {
                                member.setOutTenantName(card.getEnterpriseName());
                                member.setTeamMemberName(card.getEnterpriseName());
                            }
                        }
                        if (TeamMember.MemberType.EMPLOYEE.getValue().equals(member.getTeamMemberType())
                                || TeamMember.MemberType.INTERCONNECT_DEPARTMENT.getValue().equals(member.getTeamMemberType())) {
                            if (StringUtils.equals(String.valueOf(card.getOuterTenantId()), member.getOutTenantId())) {
                                member.setOutTenantName(card.getEnterpriseName());
                            }
                        }
                    }
                }
            }
        }

        stopWatch.logSlow(100);
    }

    /**
     * 从团队成员信息中获取外部企业ID列表
     *
     * @param relevantTeamFromObjectData 团队成员信息列表
     * @return 外部企业ID列表
     */
    private List<String> getOutTenantIdsFromTeamMembers(List<TeamMemberInfoPoJo> relevantTeamFromObjectData) {
        if (CollectionUtils.empty(relevantTeamFromObjectData)) {
            return Collections.emptyList();
        }

        // 预估集合大小，减少扩容操作
        Set<String> outTenantIdSet = Sets.newHashSetWithExpectedSize(relevantTeamFromObjectData.size());
        for (TeamMemberInfoPoJo member : relevantTeamFromObjectData) {
            if (member == null || !member.isOutTeamMember()) {
                continue;
            }
            String memberType = member.getTeamMemberType();
            // 处理EMPLOYEE类型
            // 处理INTERCONNECT_DEPARTMENT类型
            if (TeamMember.MemberType.EMPLOYEE.getValue().equals(memberType)
                    || TeamMember.MemberType.INTERCONNECT_DEPARTMENT.getValue().equals(memberType)) {
                String outTenantId = member.getOutTenantId();
                if (NumberUtils.isDigits(outTenantId)) {
                    outTenantIdSet.add(outTenantId);
                }
            }
            // 处理OUT_TENANT类型
            else if (TeamMember.MemberType.OUT_TENANT.getValue().equals(memberType)) {
                List<String> employeeList = member.getTeamMemberEmployee();
                if (CollectionUtils.notEmpty(employeeList)) {
                    for (String employeeId : employeeList) {
                        if (NumberUtils.isDigits(employeeId)) {
                            outTenantIdSet.add(employeeId);
                        }
                    }
                }
            }
        }

        return Lists.newArrayList(outTenantIdSet);
    }

    /**
     * 补充团队中互联部门成员的显示名称
     * <p>
     * 处理团队中互联互通场景下的部门成员显示名称：
     * 1. 获取互联部门的成员信息
     * 2. 补充部门成员的显示名称
     * 3. 处理跨部门的名称展示格式
     *
     * @param teamMemberInfo 团队成员信息列表
     * @param user           当前用户信息
     * @param memberIds      用于存储显示名称的列表
     */
    private void fillErDepartmentTeamMemberDisplayNames(List<TeamMemberInfoPoJo> teamMemberInfo,
                                                        User user,
                                                        List<String> memberIds) {
        // 新增处理逻辑：调用outerOrganizationService获取互联部门名称
        List<ErDepartmentSimpleData> deptList = outerOrganizationService.batchGetOutDepartment(user, memberIds, QueryDeptInfoByDeptIds.DeptStatusEnum.ALL);
        if (CollectionUtils.empty(deptList)) {
            return;
        }
        Map<String, ErDepartmentSimpleData> deptNameMap = deptList.stream()
                .collect(Collectors.toMap(ErDepartmentSimpleData::getId, Function.identity(), (x, y) -> x));
        teamMemberInfo.stream()
                .filter(x -> Objects.equals(x.getTeamMemberType(), TeamMember.MemberType.INTERCONNECT_DEPARTMENT.getValue()))
                .forEach(x -> {
                    List<String> teamMemberEmployee = x.getTeamMemberEmployee();
                    if (CollectionUtils.empty(teamMemberEmployee)) {
                        return;
                    }
                    ErDepartmentSimpleData dept = deptNameMap.get(teamMemberEmployee.get(0));
                    String deptName = getDepartmentName(dept);
                    if (deptName != null) {
                        x.setTeamMemberName(deptName);
                    }
                });
    }

    private String getDepartmentName(ErDepartmentSimpleData dept) {
        if (Objects.isNull(dept)) {
            return null;
        }
        Integer status = dept.getStatus();
        String deptName = dept.getName();
        if (CollectionUtils.empty(dept.getOrgManageType())) {
            return I18N.text(I18NKey.STATUS_REMOVED, deptName);
        }
        if (!Objects.equals(status, 1)) {
            return I18N.text(I18NKey.DISABLED, deptName);
        }
        return deptName;
    }


    private Map<String, List<String>> getTeamMemberIds(List<TeamMemberInfoPoJo> relevantTeamFromObjectData) {
        Map<String, List<String>> teamMemberIds = Maps.newHashMap();
        relevantTeamFromObjectData.forEach(team -> {
            List<String> teamMemberEmployeeIds = Lists.newArrayList(team.getTeamMemberEmployee());
            String memberType = TeamMember.MemberType.of(team.getTeamMemberType()).getValue();
            List<String> existIds = teamMemberIds.get(memberType);
            if (CollectionUtils.empty(existIds)) {
                teamMemberIds.put(memberType, teamMemberEmployeeIds);
            } else {
                existIds.addAll(teamMemberEmployeeIds);
                teamMemberIds.put(memberType, existIds);
            }
        });
        return teamMemberIds;
    }

    private List<IButton> getTeamMemberButtons(IObjectData objectData, boolean hasPermission, IObjectDescribe describe, User user) {
        if (!hasPermission) {
            return Lists.newArrayList();
        }
        return serviceFacade.findTeamMemberButton(describe, objectData, user);
    }

    @ServiceMethod("getDataOwner")
    public GetDataOwner.Result getDataOwner(GetDataOwner.Arg arg, ServiceContext context) {
        if (CollectionUtils.empty(arg.getDataIdMap())) {
            return new GetDataOwner.Result(Maps.newHashMap());
        }
        List<IObjectData> dataList = Lists.newArrayList();
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        arg.getDataIdMap().forEach((x, y) -> {
            parallelTask.submit(() -> {
                List<IObjectData> tmpList = metaDataService.findObjectDataByIdsIncludeDeleted(context.getUser(), y, x);
                if (CollectionUtils.notEmpty(tmpList)) {
                    dataList.addAll(tmpList);
                }
            });
        });
        try {
            parallelTask.await(5000, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            log.error("getDataOwner error,arg:{}", arg, e);
        }

        Map<String, String> ownerIdMap = Maps.newHashMap();
        dataList.forEach(x -> ObjectDataExt.of(x).getOwnerId().ifPresent(y -> ownerIdMap.put(x.getId(), y)));

        return new GetDataOwner.Result(ownerIdMap);
    }


    private Integer specialDealForShareType(Integer shareType) {
        return DataPrivilegeService.USER_TYPE.equals(shareType) ? Integer.valueOf(shareType.intValue() + 1) : (DataPrivilegeService.USER_GROUP_TYPE.equals(shareType) ? Integer.valueOf(shareType.intValue() + 2) : shareType);
    }

    @ServiceMethod("delFieldShareRule")
    public DelFieldShare.Result delFieldShareRule(DelFieldShare.Arg arg, ServiceContext context) {
        if (!enableFieldShareRuleSupportFunction(context.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (CollectionUtils.empty(arg.getRuleCodes())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        return delFieldShare(arg, context);
    }

    @ServiceMethod("delFieldShare")
    public DelFieldShare.Result delFieldShare(DelFieldShare.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering delShareRules(tenantId={},userId={})", tenantId, context.getUser().getUserId());
        boolean result = dataPrivilegeService.delFieldShare(context.getUser(), arg.getRuleCodes(), arg.getDescribeApiName(), arg.getStatus());
        return DelFieldShare.Result.builder().success(result).build();
    }

    @ServiceMethod("changeFieldShareRuleStatus")
    public ChangeFieldShareStatus.Result changeFieldShareRuleStatus(ChangeFieldShareStatus.Arg arg, ServiceContext context) {
        if (!enableFieldShareRuleSupportFunction(context.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (CollectionUtils.empty(arg.getRuleCodes())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        return changeFieldShareStatus(arg, context);
    }

    @ServiceMethod("changeFieldShareStatus")
    public ChangeFieldShareStatus.Result changeFieldShareStatus(ChangeFieldShareStatus.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering enableOrDisableShareRule(tenantId={},userId={})", tenantId, context.getUser().getUserId());

        boolean result = dataPrivilegeService.changeFieldShareStatus(context.getUser(), arg.getRuleCodes(), arg.getStatus());
        return ChangeFieldShareStatus.Result.builder().success(result).build();
    }

    @ServiceMethod("addFieldShareRule")
    public AddFieldShare.Result addFieldShareRule(AddFieldShare.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();

        if (!enableFieldShareRuleSupportFunction(tenantId)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (arg == null) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (StringUtils.isAnyBlank(tenantId, arg.getRuleName(), arg.getRuleParse(), arg.getDescribeApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (CollectionUtils.empty(arg.getReceives())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        IObjectDescribe objectDescribe = describeLogicService.findObject(tenantId, arg.getDescribeApiName());
        if (objectDescribe == null) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (!checkFieldShareRuleInfo(tenantId, arg.getRuleParse(), arg.getRules(), objectDescribe)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        log.debug("Entering addFieldShareRule(tenantId={},userId={})", tenantId, context.getUser().getUserId());
        if (enableUpdateFieldShareValidation(tenantId)) {
            if (arg.getRules().stream().anyMatch(rule -> Comparator.valueOf(rule.getOperate()).needFilterValues() && CollectionUtils.empty(rule.getFieldValue()))) {
                log.error("addFieldShareRule failed, tenantId=>{}, userId=>{}, arg=>{}", tenantId, context.getUser().getUserId(), JSON.toJSONString(arg));
                throw new ValidateException(I18N.text(I18NKey.COMPLETE_FILTER_CRITERIA));
            }
        } else {
            if (arg.getRules().stream().anyMatch(rule -> !Comparator.valueOf(rule.getOperate()).needFilterValues() && CollectionUtils.empty(rule.getFieldValue()))) {
                log.error("addFieldShareRule failed, tenantId=>{}, userId=>{}, arg=>{}", tenantId, context.getUser().getUserId(), JSON.toJSONString(arg));
                throw new ValidateException(I18N.text(I18NKey.COMPLETE_FILTER_CRITERIA));
            }
        }
        CreateEntityFieldShareModel.Result result = dataPrivilegeService.addFieldShareRule(context.getUser(), arg.getDescribeApiName(), arg.getRuleName(), arg.getReceives(), arg.getRuleParse(), arg.getRules(), true);
        return AddFieldShare.Result.builder().ruleId(result.getResult()).success(result.isSuccess()).build();
    }

    @ServiceMethod("addFieldShare")
    public AddFieldShare.Result addFieldShare(AddFieldShare.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering addOrUpdateShareRules(tenantId={},userId={})", tenantId, context.getUser().getUserId());
        if (enableUpdateFieldShareValidation(tenantId)) {
            if (arg.getRules().stream()
                    .anyMatch(rule -> Comparator.valueOf(rule.getOperate()).needFilterValues() && CollectionUtils.empty(rule.getFieldValue()))) {
                log.error("addFieldShare filed, tenantId=>{}, userId=>{}, arg=>{}", context.getTenantId(),
                        context.getUser().getUserId(), JSON.toJSONString(arg));
                throw new ValidateException(I18N.text(I18NKey.COMPLETE_FILTER_CRITERIA));
            }
        } else {
            if (arg.getRules().stream()
                    .anyMatch(rule -> !Comparator.valueOf(rule.getOperate()).needFilterValues() && CollectionUtils.empty(rule.getFieldValue()))) {
                log.error("addFieldShare filed, tenantId=>{}, userId=>{}, arg=>{}", context.getTenantId(),
                        context.getUser().getUserId(), JSON.toJSONString(arg));
                throw new ValidateException(I18N.text(I18NKey.COMPLETE_FILTER_CRITERIA));
            }
        }
        CreateEntityFieldShareModel.Result result = dataPrivilegeService.addFieldShare(context.getUser(), arg.getDescribeApiName(), arg.getRuleName(), arg.getReceives(), arg.getRuleParse(), arg.getRules());
        return AddFieldShare.Result.builder()
                .ruleId(result.getResult())
                .success(result.isSuccess())
                .build();
    }

    @ServiceMethod("updateFieldShareRule")
    public UpdateFieldShare.Result updateFieldShareRule(UpdateFieldShare.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();

        if (!enableFieldShareRuleSupportFunction(tenantId)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (arg == null) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (StringUtils.isAnyBlank(tenantId, arg.getRuleCode(), arg.getRuleName(), arg.getRuleParse(), arg.getDescribeApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (CollectionUtils.empty(arg.getReceives())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        IObjectDescribe objectDescribe = describeLogicService.findObject(tenantId, arg.getDescribeApiName());
        if (objectDescribe == null) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (!checkFieldShareRuleInfo(tenantId, arg.getRuleParse(), arg.getRules(), objectDescribe)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        log.debug("Entering updateFieldShareRule(tenantId={},userId={})", tenantId, context.getUser().getUserId());
        if (enableUpdateFieldShareValidation(tenantId)) {
            if (arg.getRules().stream().anyMatch(rule -> Comparator.valueOf(rule.getOperate()).needFilterValues() && CollectionUtils.empty(rule.getFieldValue()))) {
                log.error("updateFieldShareRule failed, tenantId=>{}, userId=>{}, arg=>{}", tenantId, context.getUser().getUserId(), JSON.toJSONString(arg));
                throw new ValidateException(I18N.text(I18NKey.COMPLETE_FILTER_CRITERIA));
            }
        } else {
            if (arg.getRules().stream().anyMatch(rule -> !Comparator.valueOf(rule.getOperate()).needFilterValues() && CollectionUtils.empty(rule.getFieldValue()))) {
                log.error("updateFieldShareRule failed, tenantId=>{}, userId=>{}, arg=>{}", tenantId, context.getUser().getUserId(), JSON.toJSONString(arg));
                throw new ValidateException(I18N.text(I18NKey.COMPLETE_FILTER_CRITERIA));
            }
        }

        boolean result = dataPrivilegeService.updateFieldShareRule(context.getUser(), arg.getRuleCode(), arg.getDescribeApiName(), arg.getRuleName(), arg.getReceives(), arg.getRuleParse(), arg.getRules(), true);
        return UpdateFieldShare.Result.builder().success(result).build();
    }

    @ServiceMethod("updateFieldShare")
    public UpdateFieldShare.Result updateFieldShare(UpdateFieldShare.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        log.debug("Entering addOrUpdateShareRules(tenantId={},userId={})", tenantId, context.getUser().getUserId());
        if (enableUpdateFieldShareValidation(tenantId)) {
            if (arg.getRules().stream()
                    .anyMatch(rule -> Comparator.valueOf(rule.getOperate()).needFilterValues() && CollectionUtils.empty(rule.getFieldValue()))) {
                log.error("updateFieldShare filed, tenantId=>{}, userId=>{}, arg=>{}", context.getTenantId(),
                        context.getUser().getUserId(), JSON.toJSONString(arg));
                throw new ValidateException(I18N.text(I18NKey.COMPLETE_FILTER_CRITERIA));
            }
        } else {
            if (arg.getRules().stream()
                    .anyMatch(rule -> !Comparator.valueOf(rule.getOperate()).needFilterValues() && CollectionUtils.empty(rule.getFieldValue()))) {
                log.error("updateFieldShare filed, tenantId=>{}, userId=>{}, arg=>{}", context.getTenantId(),
                        context.getUser().getUserId(), JSON.toJSONString(arg));
                throw new ValidateException(I18N.text(I18NKey.COMPLETE_FILTER_CRITERIA));
            }
        }
        boolean result = dataPrivilegeService.updateFieldShare(context.getUser(), arg.getRuleCode(), arg.getDescribeApiName(), arg.getRuleName(), arg.getReceives(), arg.getRuleParse(), arg.getRules());
        return UpdateFieldShare.Result.builder().success(result).build();
    }

    @ServiceMethod("getAllFieldShareRuleList")
    public GetAllFieldShares.Result getAllFieldShareRuleList(GetAllFieldShares.Arg arg, ServiceContext context) {
        if (!enableFieldShareRuleSupportFunction(context.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        return getAllFieldShares(arg, context);
    }

    @ServiceMethod("getAllFieldShares")
    public GetAllFieldShares.Result getAllFieldShares(GetAllFieldShares.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        // entices不为空时，查指定对象
        // -1 查自定义对象 -2查预置对象
        List<String> describeApiNameList = getAllApiNamesByCodeAndEntices(tenantId, arg.getEntices(), arg.getDescribeApiName());

        if (context.getUser() != null && dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(tenantId) && !context.getUser().isOutUser()) {
            ManageGroup manageGroup = manageGroupService.queryManageGroup(context.getUser(), null, ManageGroupType.OBJECT, true);
            if (manageGroup == null) {
                GetAllFieldShares.PageInfo emptyPageInfo = new GetAllFieldShares.PageInfo();
                emptyPageInfo.setPageSize(NumberUtil.firstNonNull(arg.getPageSize(), NumberUtil.INTEGER_TWENTY));
                emptyPageInfo.setPageCount(NumberUtil.INTEGER_ZERO);
                emptyPageInfo.setPageNumber(NumberUtil.firstNonNull(arg.getPageNumber(), NumberUtil.INTEGER_ONE));
                emptyPageInfo.setTotalCount(NumberUtil.INTEGER_ZERO);
                return GetAllFieldShares.Result.builder().page(emptyPageInfo).shareRuleList(Lists.newArrayList()).build();
            }
            if (!manageGroup.isAllSupport()) {
                describeApiNameList = dataPrivilegeCommonService.checkEntityIdsByManageGroup(describeApiNameList, manageGroup);
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(describeApiNameList)) {
                    GetAllFieldShares.PageInfo emptyPageInfo = new GetAllFieldShares.PageInfo();
                    emptyPageInfo.setPageSize(NumberUtil.firstNonNull(arg.getPageSize(), NumberUtil.INTEGER_TWENTY));
                    emptyPageInfo.setPageCount(NumberUtil.INTEGER_ZERO);
                    emptyPageInfo.setPageNumber(NumberUtil.firstNonNull(arg.getPageNumber(), NumberUtil.INTEGER_ONE));
                    emptyPageInfo.setTotalCount(NumberUtil.INTEGER_ZERO);
                    return GetAllFieldShares.Result.builder().page(emptyPageInfo).shareRuleList(Lists.newArrayList()).build();
                }
            }
        }

        QueryAllEntityFieldShareModel.Result restResult = dataPrivilegeService.getAllFieldShareList(context.getUser(), arg.getRuleCodes(), arg.getStatus(), arg
                .getPermissionType(), arg.getReceives(), arg.getReceivesWithType(), arg.getCreateIds(), arg.getModifyIds(), arg.getCreateTimeRange(), arg.getModifyTimeRange(), arg
                .getRuleName(), arg.getPageNumber(), arg.getPageSize(), describeApiNameList, arg
                .getOutReceive());

        QueryAllEntityFieldShareModel.Result.QueryAllFieldShareResult dataContent = restResult.getResult();
        List<EntityFieldShareInfo> content = dataContent.getContent();

        PageInfo dataPage = dataContent.getPage();
        GetAllFieldShares.PageInfo pageInfo = new GetAllFieldShares.PageInfo();
        pageInfo.setPageSize(dataPage.getPageSize());
        pageInfo.setPageCount(dataPage.getTotalPage());
        pageInfo.setPageNumber(dataPage.getCurrentPage());
        pageInfo.setTotalCount(dataPage.getTotal());
        return GetAllFieldShares.Result.builder().page(pageInfo).shareRuleList(content).build();
    }

    @ServiceMethod("getFieldShares")
    public GetFieldShares.Result getFieldShares(GetFieldShares.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        // entices不为空时，查指定对象
        // -1 查自定义对象 -2查预置对象
        List<String> describeApiNameList = handelApiNamesByCodeAndEntices(tenantId, arg.getEntices(), arg.getDescribeApiName());

        if (context.getUser() != null && dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(tenantId) && !context.getUser().isOutUser()) {
            ManageGroup manageGroup = manageGroupService.queryManageGroup(context.getUser(), null, ManageGroupType.OBJECT, true);
            if (manageGroup == null) {
                GetShareRules.PageInfo emptyPageInfo = new GetShareRules.PageInfo();
                emptyPageInfo.setPageSize(NumberUtil.firstNonNull(arg.getPageSize(), NumberUtil.INTEGER_TWENTY));
                emptyPageInfo.setPageCount(NumberUtil.INTEGER_ZERO);
                emptyPageInfo.setPageNumber(NumberUtil.firstNonNull(arg.getPageNumber(), NumberUtil.INTEGER_ONE));
                emptyPageInfo.setTotalCount(NumberUtil.INTEGER_ZERO);
                return GetFieldShares.Result.builder().page(emptyPageInfo).shareRuleList(Lists.newArrayList()).build();
            }
            if (!manageGroup.isAllSupport()) {
                describeApiNameList = dataPrivilegeCommonService.checkEntityIdsByManageGroup(describeApiNameList, manageGroup);
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(describeApiNameList)) {
                    GetShareRules.PageInfo emptyPageInfo = new GetShareRules.PageInfo();
                    emptyPageInfo.setPageSize(NumberUtil.firstNonNull(arg.getPageSize(), NumberUtil.INTEGER_TWENTY));
                    emptyPageInfo.setPageCount(NumberUtil.INTEGER_ZERO);
                    emptyPageInfo.setPageNumber(NumberUtil.firstNonNull(arg.getPageNumber(), NumberUtil.INTEGER_ONE));
                    emptyPageInfo.setTotalCount(NumberUtil.INTEGER_ZERO);
                    return GetFieldShares.Result.builder().page(emptyPageInfo).shareRuleList(Lists.newArrayList()).build();
                }
            }
        }

        QueryEntityFieldShareModel.Result restResult = dataPrivilegeService.getFieldShares(context.getUser(),
                "",
                arg.getRuleCodes(),
                arg.getStatus(),
                arg.getPermissionType(),
                arg.getReceives(),
                arg.getReceivesWithType(),
                arg.getRuleName(),
                arg.getPageNumber(),
                arg.getPageSize(),
                describeApiNameList,
                arg.getOutReceive());

        QueryEntityFieldShareModel.Result.QueryFieldShareResult dataContent = restResult.getResult();
        List<FieldShareRule> content = dataContent.getContent();

        PageInfo dataPage = dataContent.getPage();
        GetShareRules.PageInfo pageInfo = new GetShareRules.PageInfo();
        pageInfo.setPageSize(dataPage.getPageSize());
        pageInfo.setPageCount(dataPage.getTotalPage());
        pageInfo.setPageNumber(dataPage.getCurrentPage());
        pageInfo.setTotalCount(dataPage.getTotal());
        return GetFieldShares.Result.builder().
                page(pageInfo).
                shareRuleList(content).
                build();
    }

    private Set<String> getAllShareRuleEntityIdsByTenantId(String tenantId) {
        List<String> result = getAllApiNamesByCodeAndEntices(tenantId, null, "-1");
        if (CollectionUtils.empty(result)) {
            return Sets.newHashSet();
        }
        return Sets.newHashSet(result);
    }

    private List<String> getAllApiNamesByCodeAndEntices(String tenantId, List<String> entices, String describeApiName) {
        List<String> describeApiNameList;
        if (CollectionUtils.notEmpty(entices)) {
            describeApiNameList = entices;
        } else if ("-1".equals(describeApiName) || "-2".equals(describeApiName)) {
            // 查看所有预置对象和全部自定义对象
            // 查看全部自定义对象共享规则时增加业务流程实例的共享规则

            List<IObjectDescribe> describes = Lists.newArrayList();

            List<IObjectDescribe> customDescribes = describeLogicService.findObjectsByTenantId(tenantId, DEFINE_TYPE_CUSTOM,
                    true, true, false, true, ObjectListConfig.DATA_PRIVILEGE);

            List<IObjectDescribe> packageDescribes = describeLogicService.findObjectsByTenantId(tenantId, DEFINE_TYPE_PACKAGE,
                    true, true, false, true, ObjectListConfig.DATA_PRIVILEGE);

            if (CollectionUtils.notEmpty(customDescribes)) {
                describes.addAll(customDescribes);
            }

            if (CollectionUtils.notEmpty(packageDescribes)) {
                describes.addAll(packageDescribes);
            }

            describeApiNameList = CollectionUtils.empty(describes) ? Lists.newArrayList() :
                    describes.stream().map(IObjectDescribe::getApiName).collect(Collectors.toList());

            describeApiNameList.add(Utils.BPM_INSTANCE_API_NAME);
        } else {
            describeApiNameList = Lists.newArrayList(describeApiName);
        }
        return describeApiNameList;
    }

    private List<String> handelApiNamesByCodeAndEntices(String tenantId, List<String> entices, String describeApiName) {
        List<String> describeApiNameList;
        if (CollectionUtils.notEmpty(entices)) {
            describeApiNameList = entices;
            // -1时查自定义对象
        } else if ("-1".equals(describeApiName)) {
            List<IObjectDescribe> describes = describeLogicService.findObjectsByTenantId(tenantId, DEFINE_TYPE_CUSTOM,
                    true, true, false, true, ObjectListConfig.DATA_PRIVILEGE);
            describeApiNameList = CollectionUtils.empty(describes) ? Lists.newArrayList() :
                    describes.stream().map(IObjectDescribe::getApiName).collect(Collectors.toList());
            // 查看全部自定义对象共享规则时增加业务流程实例的共享规则
            // -2时查所有预置对象
        } else if ("-2".equals(describeApiName)) {
            List<IObjectDescribe> describes = describeLogicService.findObjectsByTenantId(tenantId, DEFINE_TYPE_PACKAGE,
                    true, true, false, true, ObjectListConfig.DATA_PRIVILEGE);
            describeApiNameList = CollectionUtils.empty(describes) ? Lists.newArrayList() :
                    describes.stream().map(IObjectDescribe::getApiName).collect(Collectors.toList());
            describeApiNameList.add(Utils.BPM_INSTANCE_API_NAME);
        } else {
            describeApiNameList = Lists.newArrayList(describeApiName);
        }
        return describeApiNameList;
    }

    /**
     * 查看临时权限列表
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("queryTemporaryPrivilege")
    public QueryTemporaryPrivilege.Result queryTemporaryPrivilege(QueryTemporaryPrivilege.Arg arg, ServiceContext context) {
        log.info("queryTemporaryPrivilege, user={}, arg={}", arg, context);

        boolean isAllSupport = false;
        Set<String> supportApiNames = Sets.newHashSet();

        boolean isCheckManageGroup = false;
        if (context.getUser() != null && dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(context.getUser().getTenantId()) && !context.getUser().isOutUser()) {
            isCheckManageGroup = true;
        }

        //通过分管对象查询
        if (isCheckManageGroup) {
            ManageGroup manageGroup = manageGroupService.queryManageGroup(context.getUser(), null, ManageGroupType.OBJECT, true);
            if (manageGroup == null) {
                return QueryTemporaryPrivilege.Result.builder().success(true).ConfigInfoList(Lists.newArrayList()).build();
            }

            isAllSupport = manageGroup.isAllSupport();

            if (!isAllSupport && org.apache.commons.collections4.CollectionUtils.isNotEmpty(manageGroup.getSupportApiNames())) {
                supportApiNames = manageGroup.getSupportApiNames();
            }
        }

        if (Objects.nonNull(arg.getApiName()) && !Strings.isNullOrEmpty(arg.getApiName())) {
            if (isCheckManageGroup && !isAllSupport && !supportApiNames.contains(arg.getApiName())) {
                return QueryTemporaryPrivilege.Result.builder().success(true).ConfigInfoList(Lists.newArrayList()).build();
            }
            // 查询指定对象的临时权限配置
            String apiName = arg.getApiName();
            IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), apiName);
            String key = TemporaryPrivilegeSwitchConfig.KEY + apiName;
            String config = configService.findTenantConfig(context.getUser(), key, context.getAppId());
            if (Strings.isNullOrEmpty(config) || Objects.isNull(describe)) {
                log.debug("queryTemporaryPrivilege ");
                return QueryTemporaryPrivilege.Result.builder().success(true).build();
            }
            TemporaryPrivilegeSwitchConfig switchConfig = JSON.parseObject(config, TemporaryPrivilegeSwitchConfig.class);
            TemporaryPrivilege.ConfigInfo configInfo = TemporaryPrivilege.ConfigInfo.convert(switchConfig);
            configInfo.setApiName(apiName);
            configInfo.setDisplayName(describe.getDisplayName());
            return QueryTemporaryPrivilege.Result.builder().success(true).ConfigInfoList(Lists.newArrayList(configInfo)).build();
        }

        // 默认查询企业下所有对象的临时权限
        Map<String, String> displayNameMap = getDescribeDisplayNameMap(context.getUser());

        List<String> keyList = Lists.newArrayList();

        if (dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(context.getUser().getTenantId())) {
            for (String v : displayNameMap.keySet()) {
                if (isCheckManageGroup && !isAllSupport && !supportApiNames.contains(v)) {
                    continue;
                }
                keyList.add(TemporaryPrivilegeSwitchConfig.KEY + v);
            }
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(keyList)) {
                return QueryTemporaryPrivilege.Result.builder().success(true).ConfigInfoList(Lists.newArrayList()).build();
            }
        } else {
            keyList = displayNameMap.keySet().stream().map(key -> TemporaryPrivilegeSwitchConfig.KEY + key).collect(Collectors.toList());
        }

        Map<String, String> configMap;
        try {
            configMap = configService.queryTenantConfigs(context.getUser(), keyList, context.getAppId());
        } catch (ConfigException e) {
            log.warn("queryTenantConfigs error => {}, user => {}, keyList => {}", e.getMessage(), context.getUser(), keyList, e);
            return QueryTemporaryPrivilege.Result.builder().message(e.getMessage()).build();
        }
        if (CollectionUtils.empty(configMap)) {
            return QueryTemporaryPrivilege.Result.builder().build();
        }
        List<TemporaryPrivilege.ConfigInfo> configInfoList = Lists.newArrayList();
        configMap.forEach((key, value) -> {
            if (Strings.isNullOrEmpty(value)) {
                return;
            }
            String apiName = key.substring(key.lastIndexOf(".") + 1);
            TemporaryPrivilegeSwitchConfig switchConfig = JSON.parseObject(value, TemporaryPrivilegeSwitchConfig.class);
            TemporaryPrivilege.ConfigInfo configInfo = TemporaryPrivilege.ConfigInfo.convert(switchConfig);
            configInfo.setApiName(apiName);
            configInfo.setDisplayName(displayNameMap.get(apiName));
            configInfoList.add(configInfo);
        });
        return QueryTemporaryPrivilege.Result
                .builder()
                .success(true)
                .ConfigInfoList(configInfoList)
                .build();
    }

    @ServiceMethod("getAvailableObjectList")
    public TemporaryPrivilege.AvailableObjectResult getAvailableObjects(TemporaryPrivilege.AvailableObjectArg arg, ServiceContext context) {
        // 默认查询企业下所有对象的临时权限
        List<IObjectDescribe> describeList = describeLogicService.findObjectsByTenantId(context.getTenantId(),
                false, true, false, true, ObjectListConfig.DATA_PRIVILEGE);
        Map<String, String> displayNameMap = describeList.stream()
                .filter(describe -> !Utils.FLOW_OBJ_API_NAME.contains(describe.getApiName()))
                .collect(Collectors.toMap(IObjectDescribe::getApiName, IObjectDescribe::getDisplayName));

        List<String> keyList = displayNameMap.keySet()
                .stream()
                .map(key -> TemporaryPrivilegeSwitchConfig.KEY + key)
                .collect(Collectors.toList());

        Map<String, String> configMap;
        try {
            configMap = configService.queryTenantConfigs(context.getUser(), keyList, context.getAppId());
        } catch (ConfigException e) {
            log.warn("queryTenantConfigs error => {}, user => {}, keyList => {}", e.getMessage(), context.getUser(), keyList, e);
            return TemporaryPrivilege.AvailableObjectResult.builder().message(e.getMessage()).build();
        }
        Set<String> apiNameSet = configMap.keySet().stream()
                .map(key -> key.substring(key.lastIndexOf(".") + 1))
                .collect(Collectors.toSet());
        apiNameSet.addAll(Utils.FLOW_OBJ_API_NAME);

        String sourceInfo = Objects.isNull(arg) ? null : arg.getSourceInfo();
        ManageGroup objectManageGroup = describeLogicService.queryObjectManageGroup(context.getUser(), sourceInfo);
        describeList.removeIf(describe -> apiNameSet.contains(describe.getApiName()) || !ManageGroup.support(objectManageGroup, describe.getApiName()));

        return TemporaryPrivilege.AvailableObjectResult.builder()
                .success(true)
                .objectDescribeList(ObjectDescribeDocument.ofList(describeList))
                .total(describeList.size())
                .build();
    }

    /**
     * 新建临时权限
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("createTemporaryPrivilege")
    public TemporaryPrivilege.Result createTemporaryPrivilege(TemporaryPrivilege.Arg arg, ServiceContext context) {
        log.info("createTemporaryPrivilege, user ={}, arg ={}", arg, context);
        if (Strings.isNullOrEmpty(arg.getApiName())) {
            log.warn("createTenantConfig error, user => {}, arg => {}, 对象apiName不能为空", context.getUser(), arg);
            return TemporaryPrivilege.Result.builder().message(I18N.text(I18NKey.OBJECT_API_NAME_CAN_NOT_IS_NULL)).build();
        }
        String key = TemporaryPrivilegeSwitchConfig.KEY + arg.getApiName();
        TemporaryPrivilegeSwitchConfig config = arg.convertTo();
        String value = toJson(config, context.getUser(), false);
        try {
            configService.createTenantConfig(context.getUser(), key, value, ConfigValueType.JSON, context.getAppId());
            logService.logTemporaryRights(context.getUser(), EventType.ADD, ActionType.Add, arg.getApiName(), value);
        } catch (ConfigException e) {
            log.warn("createTenantConfig error, user => {}, arg => {}", context.getUser(), arg, e);
            return TemporaryPrivilege.Result.builder().message(e.getMessage()).build();
        }
        return TemporaryPrivilege.Result
                .builder()
                .success(true)
                .configResult(TemporaryPrivilege.ConfigInfo.convert(config))
                .build();
    }

    /**
     * 编辑临时权限
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("updateTemporaryPrivilege")
    public TemporaryPrivilege.Result updateTemporaryPrivilege(TemporaryPrivilege.Arg arg, ServiceContext context) {
        log.info("updateTemporaryPrivilege, user={}, arg={}", arg, context);
        if (Strings.isNullOrEmpty(arg.getApiName())) {
            log.warn("updateTemporaryPrivilege error, user => {}, arg => {}, 对象apiName不能为空", context.getUser(), arg);
            return TemporaryPrivilege.Result.builder().message(I18N.text(I18NKey.OBJECT_API_NAME_CAN_NOT_IS_NULL)).build();
        }
        String key = TemporaryPrivilegeSwitchConfig.KEY + arg.getApiName();
        TemporaryPrivilegeSwitchConfig config = arg.convertTo();
        String value = toJson(config, context.getUser(), true);

        // 查库中之前的数据
        String configDb = configService.findTenantConfig(context.getUser(), key, context.getAppId());
        if (Strings.isNullOrEmpty(configDb)) {
            log.warn("updateTemporaryPrivilege error, user => {}, arg => {}, 临时权限配置不存在或已被删除", context.getUser(), arg);
            return TemporaryPrivilege.Result.builder().message(I18N.text(I18NKey.UNEXIST_OR_DELETED)).build();
        }
        TemporaryPrivilegeSwitchConfig switchConfig = JSON.parseObject(configDb, TemporaryPrivilegeSwitchConfig.class);
        TemporaryPrivilege.ConfigInfo configInfo = TemporaryPrivilege.ConfigInfo.convert(switchConfig);

        try {
            configService.updateTenantConfig(context.getUser(), key, value, ConfigValueType.JSON, context.getAppId());
            // 异步更新数据
            dataPrivilegeService.asyncUpdateTemporaryRights(context.getUser(), arg.getApiName(),
                    configInfo.convertTo(), arg.convertToRuleConfig(), context.getAppId());
            // 原配置被禁用，补发启用配置的MQ
            if (!switchConfig.getConfSwitch()) {
                dataPrivilegeService.enableTemporaryRights(context.getUser(), arg.getApiName(),
                        TemporaryPrivilegeSwitch.ENABLE, context.getAppId());
            }
            logService.logTemporaryRights(context.getUser(), EventType.MODIFY, ActionType.UPDATE_TEMPORARY_PRIVILEGE, arg.getApiName(), value);
        } catch (ConfigException e) {
            log.warn("updateTemporaryPrivilege, user => {}, arg => {}", context.getUser(), arg, e);
            return TemporaryPrivilege.Result.builder().message(e.getMessage()).build();
        }
        return TemporaryPrivilege.Result
                .builder()
                .success(TemporaryPrivilegeSwitch.SUCCESS)
                .configResult(configInfo)
                .build();
    }

    @ServiceMethod("bulkCreateTemporaryPrivilege")
    public BulkTemporaryPrivilege.Result bulkCreateTemporaryPrivilege(BulkTemporaryPrivilege.Arg arg, ServiceContext context) {
        // 过滤掉 apiName 为空的数据
        Map<String, TemporaryPrivilege.Arg> privilegeMap = arg.getPrivileges()
                .stream()
                .filter(x -> !Strings.isNullOrEmpty(x.getApiName()))
                .distinct()
                .collect(Collectors.toMap(x -> TemporaryPrivilegeSwitchConfig.KEY + x.getApiName(), x -> x));

        Set<String> keySet = privilegeMap.keySet();
        // TODO: 2019/1/9 此时查到的配置信息不是最新的，导致后续的失败
        // 将待更新和待创建的配置分开
        Map<String, String> configMap = configService.queryTenantConfigs(context.getUser(),
                Lists.newArrayList(keySet), context.getAppId());
        List<String> toUpdateKeyList = Lists.newArrayList(configMap.keySet());
        List<String> toCreateKeyList =
                keySet.stream().filter(x -> !configMap.containsKey(x)).collect(Collectors.toList());

        // 更新
        Map<String, Object> resultMap = Maps.newHashMap();
        if (toUpdateKeyList.size() < ParallelUtils.MAX_PARALLEL_NUM) {
            resultMap.putAll(parallelTask(toUpdateKeyList, key -> toUpdate(context.getUser(), privilegeMap, configMap
                    , key, context.getAppId())));
        } else {
            // 当待更新的数据超过 MAX_PARALLEL_NUM 时, 以每次100条并行更新 config服务
            List<List<String>> partition = Lists.partition(toUpdateKeyList, 100);
            partition.forEach(x -> resultMap.putAll(parallelTask(x, key -> toUpdate(context.getUser(), privilegeMap,
                    configMap, key, context.getAppId()))));
        }
        // 创建
        if (toCreateKeyList.size() < ParallelUtils.MAX_PARALLEL_NUM) {
            resultMap.putAll(parallelTask(toCreateKeyList, key -> toCreate(context.getUser(), privilegeMap, key,
                    context.getAppId())));
        } else {
            List<List<String>> partition = Lists.partition(toCreateKeyList, 100);
            partition.forEach(x -> resultMap.putAll(parallelTask(x, key -> toCreate(context.getUser(), privilegeMap,
                    key, context.getAppId()))));
        }

        return BulkTemporaryPrivilege.Result.of(new DocumentBaseEntity(resultMap));
    }

    /**
     * 启用临时权限
     *
     * @param arg
     * @return
     */
    @ServiceMethod("enableTemporaryPrivilege")
    public TemporaryPrivilegeSwitch.Result enableTemporaryPrivilege(TemporaryPrivilegeSwitch.Arg arg, ServiceContext context) {
        log.info("enableTemporaryPrivilege, user={}, apiName={}", context.getUser(), arg.getApiName());
        if (Strings.isNullOrEmpty(arg.getApiName())) {
            log.warn("enableTemporaryPrivilege error, user => {}, arg => {}, 对象apiName不能为空", context.getUser(), arg);
            return TemporaryPrivilegeSwitch.Result.builder().message(I18N.text(I18NKey.OBJECT_API_NAME_CAN_NOT_IS_NULL)).build();
        }
        try {
            String result = temporaryPrivilegeSwitch(arg, context, TemporaryPrivilegeSwitch.ENABLE);
            logService.logTemporaryRights(context.getUser(), EventType.ENABLE, ActionType.ENABLE_TEMPORARY_PRIVILEGE, arg.getApiName(), result);
        } catch (ConfigException e) {
            log.warn("enableTemporaryPrivilege error, user => {}, apiName => {}", context.getUser(), arg.getApiName(), e);
            return TemporaryPrivilegeSwitch.Result.builder().message(e.getMessage()).build();
        }
        return TemporaryPrivilegeSwitch.Result.builder().success(TemporaryPrivilegeSwitch.SUCCESS).build();
    }

    /**
     * 禁用临时权限
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("disableTemporaryPrivilege")
    public TemporaryPrivilegeSwitch.Result disableTemporaryPrivilege(TemporaryPrivilegeSwitch.Arg arg, ServiceContext context) {
        log.info("disableTemporaryPrivilege, user={}, apiName={}", context.getUser(), arg.getApiName());
        if (Strings.isNullOrEmpty(arg.getApiName())) {
            log.warn("disableTemporaryPrivilege error, user => {}, arg => {}, 对象apiName不能为空", context.getUser(), arg);
            return TemporaryPrivilegeSwitch.Result.builder().message(I18N.text(I18NKey.OBJECT_API_NAME_CAN_NOT_IS_NULL)).build();
        }
        try {
            String result = temporaryPrivilegeSwitch(arg, context, TemporaryPrivilegeSwitch.DISABLE);
            logService.logTemporaryRights(context.getUser(), EventType.DISABLE, ActionType.DISABLE_TEMPORARY_PRIVILEGE, arg.getApiName(), result);
        } catch (ConfigException e) {
            log.warn("disableTemporaryPrivilege error, user => {}, apiName => {}", context.getUser(), arg.getApiName(), e);
            return TemporaryPrivilegeSwitch.Result.builder().message(e.getMessage()).build();
        }
        return TemporaryPrivilegeSwitch.Result.builder().success(TemporaryPrivilegeSwitch.SUCCESS).build();
    }

    /**
     * 删除临时权限
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("deleteTemporaryPrivilege")
    public TemporaryPrivilegeSwitch.Result deleteTemporaryPrivilege(TemporaryPrivilegeSwitch.Arg arg, ServiceContext context) {
        log.info("deleteTemporaryPrivilege, user={}, apiName={}", context.getUser(), arg.getApiName());
        if (Strings.isNullOrEmpty(arg.getApiName())) {
            log.warn("deleteTemporaryPrivilege error, user => {}, arg => {}, 对象apiName不能为空", context.getUser(), arg);
            return TemporaryPrivilegeSwitch.Result.builder().message(I18N.text(I18NKey.OBJECT_API_NAME_CAN_NOT_IS_NULL)).build();
        }
        String key = TemporaryPrivilegeSwitchConfig.KEY + arg.getApiName();
        String configValue = configService.findTenantConfig(context.getUser(), key, context.getAppId());
        if (Strings.isNullOrEmpty(configValue)) {
            log.warn("deleteTemporaryPrivilege temporary privilege, not exist or deleted, user => {}, arg => {}, key=>{}", context.getUser(), arg, key);
            return TemporaryPrivilegeSwitch.Result.builder().message(I18N.text(I18NKey.UNEXIST_OR_DELETED)).build();
        }
        TemporaryPrivilegeSwitchConfig config = JSON.parseObject(configValue, TemporaryPrivilegeSwitchConfig.class);
        if (Boolean.TRUE.equals(config.getConfSwitch())) {
            log.warn("启用状态的临时权限配置不能被删除, user => {}, apiName => {}, config => {}", context.getUser(), arg.getApiName(), config);
            return TemporaryPrivilegeSwitch.Result.builder().message(I18N.text(I18NKey.ENABLED_NOT_BE_DELETED)).build();
        }
        configService.deleteTenantConfig(context.getUser(), key, context.getAppId());
        // 异步删除数据
        dataPrivilegeService.asyncDeleteTemporaryRights(context.getUser(), arg.getApiName(), context.getAppId());
        logService.logTemporaryRights(context.getUser(), EventType.DELETE, ActionType.DELETE_TEMPORARY_PRIVILEGE, arg.getApiName(), configValue);
        return TemporaryPrivilegeSwitch.Result.builder().success(TemporaryPrivilegeSwitch.SUCCESS).build();
    }

    /**
     * 查看已授权列表
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("queryTemporaryRightsList")
    public QueryTemporaryRights.Result queryTemporaryRights(QueryTemporaryRights.Arg arg, ServiceContext context) {
        log.info("queryTemporaryRights, arg=>{}, user=>", arg, context.getUser());
        String describeApiName = Strings.isNullOrEmpty(arg.getObjectDescribeApiName()) ? null : arg.getObjectDescribeApiName();
        String dataId = Strings.isNullOrEmpty(arg.getObjectDataId()) ? null : arg.getObjectDataId();
        Integer pageSize = Optional.ofNullable(arg.getPageSize()).orElse(20);
        Integer pageNumber = Optional.ofNullable(arg.getPageNumber()).orElse(1);

        QueryTemporaryRightsList.Result resultList;

        if (dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(context.getUser().getTenantId())) {
            List<String> entityIdList = Lists.newArrayList();

            if (describeApiName != null) {
                entityIdList.add(describeApiName);
            }

            if (!context.getUser().isOutUser()) {
                ManageGroup manageGroup = manageGroupService.queryManageGroup(context.getUser(), null, ManageGroupType.OBJECT, true);
                if (manageGroup == null) {
                    return QueryTemporaryRights.Result.builder().totalNumber(0).pageSize(arg.getPageSize()).pageNumber(arg.getPageNumber()).build();
                }
                if (!manageGroup.isAllSupport()) {
                    entityIdList = dataPrivilegeCommonService.checkEntityIdsByManageGroup(entityIdList, manageGroup);
                    if (org.apache.commons.collections4.CollectionUtils.isEmpty(entityIdList)) {
                        return QueryTemporaryRights.Result.builder().totalNumber(0).pageSize(arg.getPageSize()).pageNumber(arg.getPageNumber()).build();
                    }
                }
            }

            resultList = dataPrivilegeService.queryTemporaryRights(context.getUser(), null, entityIdList, dataId, pageSize, pageNumber, arg.getUserId(), arg.getScene());
        } else {
            resultList = dataPrivilegeService.queryTemporaryRights(context.getUser(), describeApiName, Lists.newArrayList(), dataId, pageSize, pageNumber, arg.getUserId(), arg.getScene());
        }

        //存放对象下的dataIds
        Map<String, Set<String>> dataIdsMap = Maps.newHashMap();
        Set<String> ownerList = Sets.newHashSet();
        if (CollectionUtils.notEmpty(resultList.getResult().getContent())) {
            resultList.getResult().getContent().forEach(it -> {
                ownerList.add(it.getOwner());
                if (dataIdsMap.containsKey(it.getEntityId())) {
                    dataIdsMap.get(it.getEntityId()).add(it.getDataId());
                } else {
                    dataIdsMap.put(it.getEntityId(), Sets.newHashSet(it.getDataId()));
                }
            });
            Map<String, String> ownerLabelMap = serviceFacade.getUserNameMapByIds(context.getUser().getTenantId(), context.getUser().getUserId(), Lists.newArrayList(ownerList));
            if (ownerLabelMap.containsKey(User.SUPPER_ADMIN_USER_ID)) {
                ownerLabelMap.put(User.SUPPER_ADMIN_USER_ID, User.getSupperAdminUserName());
            }
            Map<String, IObjectDescribe> objectDescribeMap = describeLogicService.findObjects(context.getTenantId(), dataIdsMap.keySet());
            //key为对象apiName,value为对象数据dataId和label的map
            Map<String, Map<String, String>> objectApiNameForIdLabelMap = Maps.newConcurrentMap();
            // 异步查询
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            dataIdsMap.forEach((x, y) -> parallelTask.submit(() -> {
                List<INameCache> nameCaches = metaDataService.findRecordName(buildContext(context.getUser()), x, Lists.newArrayList(y));
                Map<String, String> ownersMap = nameCaches.stream()
                        .filter(cache -> Objects.nonNull(cache.getName()))
                        .collect(Collectors.toMap(INameCache::getId, INameCache::getName));
                objectApiNameForIdLabelMap.put(x, ownersMap);
            }));
            try {
                parallelTask.await(5, TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                throw new MetaDataException(e.getMessage());
            }

            return QueryTemporaryRights.Result.builder()
                    .rightsInfoList(TemporaryRightsInfo.ofList(resultList.getResult().getContent(), objectApiNameForIdLabelMap, ownerLabelMap, objectDescribeMap))
                    .totalNumber(resultList.getResult().getPage().getTotal())
                    .pageSize(arg.getPageSize())
                    .pageNumber(arg.getPageNumber())
                    .pageCount(getPageCount(resultList.getResult().getPage().getTotal(), arg.getPageSize()))
                    .build();
        } else {
            return QueryTemporaryRights.Result.builder()
                    .totalNumber(0)
                    .pageSize(arg.getPageSize())
                    .pageNumber(arg.getPageNumber())
                    .build();
        }
    }

    private Map<String, String> getDescribeDisplayNameMap(User user) {
        List<IObjectDescribe> describeList = describeLogicService.findObjectsByTenantId(user.getTenantId(),
                false, true, false, true, ObjectListConfig.DATA_PRIVILEGE);
        return describeList.stream()
                .filter(describe -> !Utils.FLOW_OBJ_API_NAME.contains(describe.getApiName()))
                .collect(Collectors.toMap(IObjectDescribe::getApiName, IObjectDescribe::getDisplayName));
    }

    /**
     * 删除数据
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("deleteTemporaryRights")
    public TemporaryRights.Result deleteTemporaryRights(TemporaryRights.Arg arg, ServiceContext context) {
        log.info("deleteTemporaryRights, arg=>{}, user=>{}", arg, context.getUser());
        DeleteTemporaryRights.Result result = dataPrivilegeService.deleteTemporaryRights(context.getUser(), arg.getSourceId(), arg.getDataId(), arg.getOwner(), arg.getApiName());
        return TemporaryRights.Result.builder().success(result.isSuccess()).message(result.getErrDescription()).build();
    }

    /**
     * 批量删除数据
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("batchDeleteTemporaryRights")
    public TemporaryRights.Result batchDeleteTemporaryRights(TemporaryRights.Arg arg, ServiceContext context) {
        log.info("batchDeleteTemporaryRights, arg=>{}, user=>{}", arg, context.getUser());
        DeleteTemporaryRights.Result result = dataPrivilegeService.batchDeleteTemporaryRights(context.getUser(), arg.getTemporaryRightsIds());
        return TemporaryRights.Result.builder().success(result.isSuccess()).message(result.getErrDescription()).build();
    }

    @ServiceMethod("batchDeleteTemporaryPrivilege")
    public BatchDeleteTemporaryPrivilege.Result batchDeleteTemporaryPrivilege(BatchDeleteTemporaryPrivilege.Arg arg, ServiceContext context) {
        log.info("batchDeleteTemporaryPrivilege, arg=>{}, user=>{}", arg, context.getUser());

        if (!enableBatchDeleteTemporaryPrivilegeSupportFunction(context.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        String lang = null;
        if (context.getLang() != null) {
            lang = context.getLang().getValue();
        }

        BatchDeleteTemporaryPrivilegeModel.Result result = dataPrivilegeService.batchDeleteTemporaryPrivilege(context.getUser(), arg, lang);
        return BatchDeleteTemporaryPrivilege.Result.builder().success(result.isSuccess()).message(result.getErrDescription()).build();
    }

    @ServiceMethod("calc_progress")
    public DataPrivilegeCalcProgress.Body calcProgress(ServiceContext context) {
        return dataAuthService.calcProgress(context.getTenantId());
    }

    @ServiceMethod("checkPrivilege")
    public CheckPrivilege.Result checkPrivilege(ServiceContext context, CheckPrivilege.Arg arg) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(context.getTenantId(), arg.getObjectDescribeApiName());
        IObjectData objectData = serviceFacade.findObjectDataIgnoreFormula(context.getUser(),
                arg.getId(), arg.getObjectDescribeApiName());

        Map<String, Permissions> permissionsMap = metaDataMiscService.checkPrivilege(context.getUser(),
                Lists.newArrayList(objectData), objectDescribe, arg.getActionCode());

        return CheckPrivilege.Result.builder()
                .value(permissionsMap.computeIfAbsent(arg.getId(), x -> Permissions.NO_PERMISSION).getValue())
                .build();
    }

    /**
     * 获取有权限的全部下游企业的全部对接人
     */
    @ServiceMethod("getOutTenantsAndUsers")
    public ConnectionEnterpriseUser.Result getOutUsers(ServiceContext context, ConnectionEnterpriseUser.Arg arg) {
        HeaderObj header = HeaderObj.newInstance(Integer.valueOf(context.getTenantId()));

        ListDownstreamsPublicEmployeesByConditionArg body = new ListDownstreamsPublicEmployeesByConditionArg();
        body.setUserId(getUserId(context.getUser()));
        body.setUpstreamTenantId(Integer.valueOf(context.getTenantId()));
        if (Objects.nonNull(arg.getOffset())) {
            body.setOffSet(arg.getOffset());
        }
        if (Objects.nonNull(arg.getLimit())) {
            body.setPageSize(arg.getLimit() > NumberUtil.INTEGER_ONE_THOUSAND ? NumberUtil.INTEGER_ONE_THOUSAND : arg.getLimit());
        } else {
            body.setPageSize(NumberUtil.INTEGER_ONE_THOUSAND);
        }
        log.warn("getOutTenantsAndUsers,tenantId:{},pageSize:{}", context.getTenantId(), body.getPageSize());
        if (Objects.nonNull(arg.getShortName())) {
            body.setSearchStr(arg.getShortName());
        }
        // 支持下游添加外部相关团队，如果当前操作者是下游，则过滤掉其它下游企业
        if (context.getUser().isOutUser() && !Strings.isNullOrEmpty(context.getUser().getOutTenantId())) {
            body.setDownstreamOuterTenantId(Long.parseLong(context.getUser().getOutTenantId()));
        }

        RestResult<ListOuterTenantDetailResult> restResult =
                publicEmployeeService.listDownstreamsPublicEmployeesByCondition(header, body);
        List<OuterTenantDetailData> outerTenants = null;
        if (restResult.isSuccess() && Objects.nonNull(restResult.getData())) {
            outerTenants = restResult.getData().getOuterTenants();
        } else {
            throw new ValidateException(I18N.text(I18NKey.GET_DOWNSTREAM_TENANT_ERROR, restResult.getErrMsg()));
        }

        List<ConnectionEnterpriseUser.ConnectionEnterpriseInfo> infos = Lists.newArrayList();
        if (Objects.nonNull(outerTenants)) {
            outerTenants.forEach(o -> {
                List<ConnectionEnterpriseUser.OutUser> outUsers = Lists.newArrayList();
                CollectionUtils.nullToEmpty(o.getOuterUsers()).forEach(u -> {
                    ConnectionEnterpriseUser.OutUser outUser = ConnectionEnterpriseUser.OutUser.builder()
                            .outUserId(String.valueOf(u.getOuterUid()))
                            .outUserName(u.getEmployeeName())
                            .gender(u.getGender())
                            .profile(u.getProfileImage())
                            .build();
                    outUsers.add(outUser);
                });

                ConnectionEnterpriseUser.ConnectionEnterpriseInfo info = ConnectionEnterpriseUser.ConnectionEnterpriseInfo.builder()
                        .outTenantId(String.valueOf(o.getOuterTenantId())).outTenantName(o.getEnterpriseName())
                        .outUsers(outUsers).build();
                infos.add(info);
            });
        }
        return ConnectionEnterpriseUser.Result.builder().infos(infos).build();
    }

    private int getUserId(User user) {
        if (user.isOutUser()) {
            List<User> users = outerOrganizationService.getUpstreamPartnerContact(user.getTenantId(), user.getOutTenantId());
            ObjectUtils.requireNotEmpty(users, CRMErrorCode.FS_CRM_COMMON_CHECKED_USERID_NULL::getMessage,
                    CRMErrorCode.FS_CRM_COMMON_CHECKED_USERID_NULL::getCode);
            return users.stream()
                    .mapToInt(User::getUserIdInt)
                    .findFirst()
                    .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.NO_CONTACT_PERSON)));
        }
        return user.getUserIdInt();
    }

    /**
     * 获取有权限的全部下游企业的全部对接人
     */
    @ServiceMethod("getOutUsers")
    public ConnectionEnterpriseUser.Result getOutUsers(ServiceContext context) {

        return ConnectionEnterpriseUser.Result.builder().build();
    }

    @ServiceMethod("obtainDataAuth")
    public Map<String, Object> obtainDataAuth(QueryDataAuth.Arg arg, ServiceContext context) {
        if (context != null && context.getLang() != null) {
            arg.setLang(context.getLang().getValue());
        }
        return dataPrivilegeService.obtainDataAuth(arg);
    }

    @ServiceMethod("queryAllGroupInfoList")
    public List<Map<String, Object>> queryAllGroupInfoList(QueryGroupInfoList.Arg arg, ServiceContext context) {
        List<Map<String, Object>> groupList = Lists.newArrayList();
        String tenantId = context.getTenantId();
        if (context.getUser() == null) {
            return groupList;
        }
        String userId = context.getUser().getUserId();
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(userId)) {
            return groupList;
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(arg.getGroupIds())) {
            List<QueryGroupByIds.UserGroupInfo> groupInfoList = orgService.getGroupInfoByIds(tenantId, userId, arg.getGroupIds());
            for (QueryGroupByIds.UserGroupInfo groupInfo : groupInfoList) {
                Map<String, Object> map = Maps.newHashMap();
                map.put("groupId", groupInfo.getId());
                map.put("groupName", groupInfo.getName());
                map.put("tenantId", groupInfo.getTenantId());
                map.put("status", groupInfo.getStatus());
                groupList.add(map);
            }
        } else {
            List<QueryGroupByGroupName.UserGroupInfo> groupInfoList = orgService.getGroupInfoByGroupName(tenantId, userId, arg.getKeyword(), arg.getStatus());
            for (QueryGroupByGroupName.UserGroupInfo groupInfo : groupInfoList) {
                Map<String, Object> map = Maps.newHashMap();
                map.put("groupId", groupInfo.getId());
                map.put("groupName", groupInfo.getName());
                map.put("tenantId", groupInfo.getTenantId());
                map.put("status", groupInfo.getStatus());
                groupList.add(map);
            }
        }
        return groupList;
    }

    @ServiceMethod("queryTeamRoleDescribeList")
    public Map<String, Set<String>> queryTeamRoleDescribeList(ServiceContext context) {
        Map<String, Set<String>> teamRoleDescribeList = dataPrivilegeService.queryTeamRoleDescribeList(context.getUser());

        Set<String> queryEntityIds = Sets.newHashSet();
        teamRoleDescribeList.values().forEach(v -> {
            if (CollectionUtils.notEmpty(v)) {
                queryEntityIds.addAll(v);
            }
        });
        queryEntityIds.remove("0");

        String tenantId = context.getUser().getTenantId();

        List<IObjectDescribe> describeList = describeLogicService.findDescribeListWithoutFields(tenantId, queryEntityIds);
        Map<String, OptionalFeaturesSwitchDTO> optionalFeaturesSwitch = optionalFeaturesService.batchQueryOptionalFeaturesSwitch(tenantId, describeList);

        if (MapUtils.isEmpty(optionalFeaturesSwitch)) {
            return teamRoleDescribeList;
        }

        Set<String> enableEntityIds = Sets.newHashSet();
        optionalFeaturesSwitch.forEach((x, y) -> {
            if (x == null || y == null) {
                return;
            }
            if (BooleanUtils.isTrue(y.getIsRelatedTeamEnabled())) {
                enableEntityIds.add(x);
            }
        });

        Map<String, Set<String>> enableTeamRoleDescribeList = Maps.newHashMap();
        for (String roleType : teamRoleDescribeList.keySet()) {
            Set<String> value = teamRoleDescribeList.getOrDefault(roleType, Sets.newHashSet());
            Set<String> filterValue = value.stream().filter(id -> {
                if ("0".equals(id) || enableEntityIds.contains(id)) {
                    return true;
                }
                return false;
            }).collect(Collectors.toSet());
            enableTeamRoleDescribeList.put(roleType, filterValue);
        }

        return enableTeamRoleDescribeList;
    }

    @ServiceMethod("queryTeamRoleMaxNumber")
    public Object queryTeamRoleMaxNumber(ServiceContext context) {
        return dataPrivilegeService.queryTeamRoleMaxNumber(context.getUser());
    }

    @ServiceMethod("createTeamRole")
    public CreateTeamRole.Result createTeamRole(CreateTeamRole.Arg arg, ServiceContext context) {
        return dataPrivilegeService.createTeamRole(context.getUser(), arg);
    }

    @ServiceMethod("queryTeamRole")
    public QueryTeamRole.Result queryTeamRole(QueryTeamRole.Arg arg, ServiceContext context) {
        if (arg == null) {
            arg = new QueryTeamRole.Arg();
        }

        String lang = null;
        if (context.getLang() != null) {
            lang = context.getLang().getValue();
        }

        QueryTeamRole.Result result = dataPrivilegeService.queryTeamRole(context.getUser(), arg, lang);

        String tenantId = context.getUser().getTenantId();

        Set<String> allEntityIds = Sets.newHashSet();
        for (TeamRolePojo v : result.getTeamRoleList()) {
            if (v == null || CollectionUtils.empty(v.getEntityIds())) {
                continue;
            }
            allEntityIds.addAll(v.getEntityIds());
        }
        allEntityIds.remove("0");


        List<IObjectDescribe> describeList = describeLogicService.findDescribeListWithoutFields(tenantId, allEntityIds);
        Set<String> licenseAvailableObject = licenseService.queryAvailableObject(tenantId);
        if (licenseAvailableObject == null) {
            licenseAvailableObject = Sets.newHashSet();
        }

        Map<String, String> allEntityIdMap = Maps.newHashMap();
        for (IObjectDescribe objectDescribe : describeList) {
            if (IObjectDescribe.DEFINE_TYPE_CUSTOM.equals(objectDescribe.getDefineType()) || licenseAvailableObject.contains(objectDescribe.getApiName())) {
                allEntityIdMap.put(objectDescribe.getApiName(), objectDescribe.getDisplayName());
            }
        }

        for (TeamRolePojo v : result.getTeamRoleList()) {
            if (v == null || CollectionUtils.empty(v.getEntityIds())) {
                continue;
            }
            Set<String> entityIds = Sets.newHashSet();
            Map<String, String> entityInfo = Maps.newHashMap();
            for (String id : v.getEntityIds()) {
                if ("0".equals(id)) {
                    entityIds.add(id);
                    entityInfo.put(id, I18N.text("data.auth.team.role.all.object.text"));
                    continue;
                }
                if (allEntityIdMap.containsKey(id)) {
                    entityIds.add(id);
                    entityInfo.put(id, allEntityIdMap.get(id));
                }
            }
            v.setEntityIds(entityIds);
            v.setEntityInfo(entityInfo);
        }

        return result;
    }

    @ServiceMethod("updateTeamRole")
    public UpdateTeamRole.Result updateTeamRole(UpdateTeamRole.Arg arg, ServiceContext context) {
        String lang = null;
        if (context.getLang() != null) {
            lang = context.getLang().getValue();
        }
        return dataPrivilegeService.updateTeamRole(context.getUser(), arg, lang);
    }

    @ServiceMethod("updateTeamRoleStatus")
    public UpdateTeamRoleStatus.Result updateTeamRoleStatus(UpdateTeamRoleStatus.Arg arg, ServiceContext context) {
        return dataPrivilegeService.updateTeamRoleStatus(context.getUser(), arg);
    }

    @ServiceMethod("deleteTeamRole")
    public UpdateTeamRoleStatus.Result deleteTeamRole(UpdateTeamRoleStatus.Arg arg, ServiceContext context) {
        return dataPrivilegeService.deleteTeamRole(context.getUser(), arg);
    }

    private String temporaryPrivilegeSwitch(TemporaryPrivilegeSwitch.Arg arg, ServiceContext context, boolean enable) {
        String key = TemporaryPrivilegeSwitchConfig.KEY + arg.getApiName();
        String configValue = configService.findTenantConfig(context.getUser(), key, context.getAppId());
        TemporaryPrivilegeSwitchConfig config = JSON.parseObject(configValue, TemporaryPrivilegeSwitchConfig.class);

        if (Objects.nonNull(config)) {
            if (config.getConfSwitch() == enable) {
                return null;
            }
            config.setConfSwitch(enable);
            String value = toJson(config, context.getUser(), true);
            // 禁用/启用临时权限
            configService.updateTenantConfig(context.getUser(), key, value, ConfigValueType.JSON, context.getAppId());
            // 异步禁用/启用数据
            dataPrivilegeService.enableTemporaryRights(context.getUser(), arg.getApiName(), enable, context.getAppId());
            return value;
        }
        // 临时权限配置已被删除或不存在
        throw new ConfigException(I18N.text(I18NKey.UNEXIST_OR_DELETED));
    }

    private String toJson(TemporaryPrivilegeSwitchConfig config, User user, boolean isUpdate) {
        if (isUpdate) {
            config.setLastModifiedBy(user.getUserId());
            config.setLastModifiedTime(System.currentTimeMillis());
            return JSON.toJSONString(config);
        }
        config.setCreatedBy(user.getUserId());
        config.setCreatedTime(System.currentTimeMillis());
        return JSON.toJSONString(config);
    }

    private IActionContext buildContext(User user) {
        return this.buildContext(user, false);
    }

    private IActionContext buildContext(User user, boolean allowUpdateInvalid) {
        return ActionContextExt.of(user).allowUpdateInvalid(allowUpdateInvalid).getContext();
    }

    private Integer getPageCount(Integer totalNumber, Integer pageSize) {
        if (totalNumber % pageSize != 0) {
            return totalNumber / pageSize + 1;
        } else {
            return totalNumber / pageSize;
        }
    }

    private String toCreate(User user, Map<String, TemporaryPrivilege.Arg> privilegeMap, String key, String pkg) {
        TemporaryPrivilege.Arg privilegeArg = privilegeMap.get(key);
        TemporaryPrivilegeSwitchConfig config = privilegeArg.convertTo();
        String value = toJson(config, user, false);
        try {
            configService.createTenantConfig(user, key, value, ConfigValueType.JSON, pkg);
        } catch (ConfigException e) {
            log.warn("toCreateTemporaryPrivilege error, user => {}, privilegeArg => {}", user,
                    JsonUtil.toJson(privilegeArg), e);
            return e.getMessage();
        }
        return "";
    }

    private String toUpdate(User user, Map<String, TemporaryPrivilege.Arg> privilegeMap,
                            Map<String, String> configMap, String key, String pkg) {
        TemporaryPrivilege.Arg privilegeArg = privilegeMap.get(key);
        TemporaryPrivilegeSwitchConfig config = privilegeArg.convertTo();
        String value = toJson(config, user, true);
        try {
            TemporaryPrivilegeSwitchConfig switchConfig = JSON.parseObject(configMap.get(key),
                    TemporaryPrivilegeSwitchConfig.class);
            TemporaryPrivilege.ConfigInfo configInfo = TemporaryPrivilege.ConfigInfo.convert(switchConfig);

            configService.updateTenantConfig(user, key, value, ConfigValueType.JSON, pkg);
            // 异步更新数据
            dataPrivilegeService.asyncUpdateTemporaryRights(user, privilegeArg.getApiName(), configInfo.convertTo(),
                    privilegeArg.convertToRuleConfig(), pkg);
        } catch (ConfigException e) {
            log.warn("toUpdateTemporaryPrivilege error, user => {}, privilegeArg => {}", user,
                    JsonUtil.toJson(privilegeArg), e);
            return e.getMessage();
        }
        return "";
    }

    private Map<String, String> parallelTask(Collection<String> toUpdateKeySet, Function<String, String> function) {
        Map<String, String> resultMap = Maps.newConcurrentMap();
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        toUpdateKeySet.forEach(key -> parallelTask.submit(() -> {
            String message = function.apply(key);
            if (!Strings.isNullOrEmpty(message)) {
                resultMap.put(key.substring(key.lastIndexOf('.' + 1)), message);
            }
        }));

        try {
            parallelTask.await(5, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("parallelTask error", e);
            throw new MetaDataBusinessException(e.getMessage());
        }
        return resultMap;
    }

    private List<ObjectDataCommonPrivilegeInfo> getObjectDataCommonPrivilegeInfos(ServiceContext context) {
        List<IObjectDescribe> objectDescribes;
        if (enableBigObjectCommonAndDepartmentPrivilege(context.getTenantId())) {
            objectDescribes = queryAllObjectDescribeByTenantId(context.getTenantId());
        } else {
            objectDescribes = describeLogicService.findObjectsByTenantId(context.getTenantId(), false, true, true, true, ObjectListConfig.DATA_PRIVILEGE);
        }

        List<ObjectDataPermissionInfo> list = dataPrivilegeService.getCommonPrivilegeListResult(context.getUser(), objectDescribes);

        if (CollectionUtils.empty(list)) {
            return Lists.newArrayList();
        }

        Map<String, Boolean> isBigObjectMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(objectDescribes)) {
            for (IObjectDescribe v : objectDescribes) {
                isBigObjectMap.put(v.getApiName(), v.isBigObject());
            }
        }

        List<ObjectDataCommonPrivilegeInfo> objectDataCommonPrivilegeInfoList = Lists.newArrayList();
        for (ObjectDataPermissionInfo info : list) {
            ObjectDataCommonPrivilegeInfo objectDataCommonPrivilegeInfo = new ObjectDataCommonPrivilegeInfo();
            objectDataCommonPrivilegeInfo.setObjectDescribeApiName(info.getObjectDescribeApiName());
            objectDataCommonPrivilegeInfo.setObjectDescribeDisplayName(info.getObjectDescribeDisplayName());
            objectDataCommonPrivilegeInfo.setPermissionType(info.getPermissionType());
            objectDataCommonPrivilegeInfo.setObjectType(BooleanUtils.isTrue(isBigObjectMap.get(info.getObjectDescribeApiName())) ? 1 : 0);
            objectDataCommonPrivilegeInfoList.add(objectDataCommonPrivilegeInfo);
        }

        return objectDataCommonPrivilegeInfoList;
    }

    private List<ObjectDataPermissionInfo> getObjectDataPermissionInfos(ServiceContext context) {
        List<IObjectDescribe> objectDescribes;
        if (enableBigObjectCommonAndDepartmentPrivilege(context.getTenantId())) {
            objectDescribes = queryAllObjectDescribeByTenantId(context.getTenantId());
        } else {
            objectDescribes = describeLogicService.findObjectsByTenantId(context.getTenantId(), false, true, true, true, ObjectListConfig.DATA_PRIVILEGE);
        }
        return dataPrivilegeService.getCommonPrivilegeListResult(context.getUser(), objectDescribes);
    }

    /**
     * 查询企业全部对象（包含大对象）
     *
     * @param tenantId
     * @return
     */
    private List<IObjectDescribe> queryAllObjectDescribeByTenantId(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return Lists.newArrayList();
        }

        ObjectDescribeFinder objectDescribeFinder = ObjectDescribeFinder
                .builder()
                .user(User.systemUser(tenantId))
                .isOnlyActivate(true)
                .isExcludeDetailObj(true)
                .isExcludeDetailWithMasterCreated(false)
                .isAsc(true)
                .sourceInfo(ObjectListConfig.DATA_PRIVILEGE)
                .visibleScope(Sets.newHashSet(IObjectDescribe.BIG_OBJECT_VISIBLE_SCOPE))
                .onlyVisibleScope(false)
                .build();

        List<IObjectDescribe> list = describeLogicService.findObjectsByTenantId(objectDescribeFinder);
        if (CollectionUtils.empty(list)) {
            return Lists.newArrayList();
        }

        return list;
    }

    private String queryRequestLang(ServiceContext context) {
        String lang = null;

        if (context == null) {
            return lang;
        }

        if (context.getLang() != null) {
            lang = context.getLang().getValue();
        }

        return lang;
    }

    private boolean checkFieldShareRuleInfo(String tenantId, String ruleParse, List<Rule> ruleList, IObjectDescribe objectDescribe) {
        if (StringUtils.isBlank(tenantId)) {
            return false;
        }
        if (CollectionUtils.empty(ruleList)) {
            return false;
        }

        if (StringUtils.isBlank(ruleParse)) {
            return false;
        }

        if (objectDescribe == null) {
            return false;
        }

        for (Rule rule : ruleList) {
            if (rule == null) {
                return false;
            }
            if (StringUtils.isAnyBlank(rule.getFieldName(), rule.getOperate())) {
                return false;
            }
            if (rule.getRuleOrder() == null || !ruleParse.contains(String.valueOf(rule.getRuleOrder()))) {
                return false;
            }
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(rule.getFieldName());
            if (fieldDescribe == null || StringUtils.isBlank(fieldDescribe.getType())) {
                return false;
            }
            rule.setFieldType(fieldDescribe.getType());
        }
        return true;
    }
}