package com.facishare.paas.appframework.metadata.util;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.metadata.HeadField;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplate;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * Created by luxin on 2018/5/11.
 */
public class SearchTemplateUtil {

    public static final Set<String> NEED_ADD_DEFAULT_SEARCH_TEMPLATE_API_NAMES = Collections
            .unmodifiableSet(Sets.newHashSet(Utils.CASES_API_NAME));


    private SearchTemplateUtil() {
    }

    public static IFilter getFilterByPara(Operator operator, String fieldName, List<String> list, boolean objectReference) {
        IFilter filter = new Filter();
        filter.setFieldnum(0);
        filter.setOperator(operator);
        filter.setConnector("AND");
        filter.setFieldName(fieldName);
        filter.setFieldValues(list);
        filter.setObjectReference(objectReference);
        return filter;
    }

    public static ISearchTemplate getSearchTemplate(String tenantId, String apiName, String label, boolean isDefault, List<IFilter> filters) {
        return getSearchTemplate(tenantId, apiName, label, isDefault, filters, null, null);
    }

    public static ISearchTemplate getSearchTemplate(String tenantId, String apiName, String label, boolean isDefault, List<IFilter> filters, List<String> filedList, Integer fieldListType) {
        ISearchTemplate searchTemplate = new SearchTemplate();
        searchTemplate.setTenantId(tenantId);
        searchTemplate.setIsHidden(false);
        searchTemplate.setPackage("CRM");
        searchTemplate.setLabel(label);
        searchTemplate.setIsDefault(isDefault);
        searchTemplate.setType("default");
        searchTemplate.setCreatedBy("system");
        searchTemplate.setObjectDescribeApiName(apiName);
        searchTemplate.setFilters(filters);
        if (Objects.nonNull(fieldListType)) {
            searchTemplate.setFieldListType(fieldListType);
        }
        if (CollectionUtils.isNotEmpty(filedList)) {
            searchTemplate.setFieldList(HeadField.fromFieldList(filedList));
        }
        return searchTemplate;
    }


}
