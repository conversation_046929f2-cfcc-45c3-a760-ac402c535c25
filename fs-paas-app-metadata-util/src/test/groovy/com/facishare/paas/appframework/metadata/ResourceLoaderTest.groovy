package com.facishare.paas.appframework.metadata

import com.github.mybatis.util.UnicodeFormatter
import spock.lang.Specification

/**
 * Created by liyiguang on 2017/11/29.
 */
class ResourceLoaderTest extends Specification {
    def "test loadMapping"() {

        when:
        def res = ResourceLoader.loadMapping("object_fields_mapping", "AccountObj")
        then:
        res != null
    }


    def "test SPACE_CHAR_REGX"() {
        when:
        str1 = UnicodeFormatter.decodeUnicodeString(str1)
        str2 = UnicodeFormatter.decodeUnicodeString(str2)
        println str1 == str2
        then:
        def s1 = ObjectDataExt.SPACE_CHAR_REGX.matcher(str1).replaceAll(" ")
        def s2 = ObjectDataExt.SPACE_CHAR_REGX.matcher(str2).replaceAll(" ")

        println UnicodeFormatter.toUnicodeString(s1)
        println UnicodeFormatter.toUnicodeString(s2)

        s1 == s2
        where:
        str1                                                                                                                                                                                                                                                                         | str2
        "\\u0043\\u0065\\u006e\\u0074\\u0072\\u0079\\u00a0\\u0049\\u006d\\u0061\\u0067\\u0065\\u006d\\u00a0\\u0041\\u0074\\u0061\\u0063\\u0061\\u0064\\u006f\\u00a0\\u0064\\u0065\\u00a0\\u0041\\u006e\\u0074\\u0065\\u006e\\u0061\\u0073\\u00a0\\u004c\\u0074\\u0064\\u0061\\u0034" | "\\u0043\\u0065\\u006e\\u0074\\u0072\\u0079\\u0020\\u0049\\u006d\\u0061\\u0067\\u0065\\u006d\\u0020\\u0041\\u0074\\u0061\\u0063\\u0061\\u0064\\u006f\\u0020\\u0064\\u0065\\u0020\\u0041\\u006e\\u0074\\u0065\\u006e\\u0061\\u0073\\u0020\\u004c\\u0074\\u0064\\u0061\\u0034"
    }

    def "test_config"() {
        expect:
        def str ="705020,706123,702706,700702,705188,706332,705761,688224,705783,700932,706089,706410,701823,704323,706416,687889,700376,691681,705056,706665,706197,706520,706641,706645,705759,706453,705635,705306,701539,706756,705705,705070,706876,706854,703301,688128,633969,705069,705092,680433,702462,705623,704523,705792,700266,706472,702454,707075,703289,706588,706054,705778,706013,704898,704810,705931,706673,705720,707036,705063,688339,701620,702516,703667,700609,704498,707337,701483,704727,706906,705649,705711,706495,704875,704668,703457,704254,706419,705752,705213,690637,704022,705266,705585,705194,706471,705990,704264,650946,706155,707064,706700,705139,705737,707065,660640,706722,700933,703843,706302,706601,706609,707180,705229,705831,690724,705784,702027,706763,704876,706786,705538,707324,706022,671982,706318,705579,662658,686911,705833,705865,703304,704467,704091,704720,705774,703769,707034,705147,706749,703591,704625,705942,706078,706146,706787,704029,706310,704772,707050,706305,704501,705818,706460,704478,702351,705064,707055,703273,706403,706548,701853,705546,703759,704963,704793,704557,686350,700135,705141,706995,701455,690833,678727,680969,706849,706956,705719,706844,704267,705638,702191,704743,706358,650943,702690,706516,701538,689519,706598,704077,701880,704802,704890,707110,707030,704766,706402,701536,687083,707031,615063,705779,704547,688763,700342,704706,706599,705374,678800,662495,706534,706817,701209,706467,706984,707151,703963,700197,705203,705535,704790,707421,704564,703602,704125,635896,684938,691647,703124,705285,684847,701210,704477,705016,704709,704265,703676,691162,700608,704951,664634,700685,705542,704270,707371,704616,705951,707323,704796,690862,700945,633072,523471,58517,649612,26640,592739,80663,583193,584982,127746,54216,585880,59572,59210,477319,552337,282489,75783,594431,617592,648203,653445,649811,652209,587597,653250,655535,657168,667687,647942,658251,658566,659774,660276,644328,69582,663652,663958,647596,672660,676173,679804,667535,633228,661753,607154,650052,671922,688000,684535,701605,184385,6671,633347,1"
        Set<String> tenatIds = AppFrameworkConfig.CONFIG_SPLITTER.split(str)
        println tenatIds
    }
}
