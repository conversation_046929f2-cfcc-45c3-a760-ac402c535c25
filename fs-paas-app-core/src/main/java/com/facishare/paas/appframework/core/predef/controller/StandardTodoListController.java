package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;


public class StandardTodoListController  extends AbstractStandardListController<StandardTodoListController.Arg>  {

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        if (arg.getUnProcessedStatus()==1) {
            buildUnProcessedQuery(query);
        }
        else {
            buildProcessedQuery(query);
        }
        return query;
    }

    protected void buildUnProcessedQuery(SearchTemplateQuery query) {

    }

    protected void buildProcessedQuery(SearchTemplateQuery query) {

    }

    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        SearchTemplateQuery query = super.customSearchTemplate(searchQuery);
        if (arg.getUnProcessedStatus()==1) {
            buildCustomUnProcessedQuery(query);
        }
        else {
            buildCustomProcessedQuery(query);
        }
        return query;
    }

    protected void buildCustomUnProcessedQuery(SearchTemplateQuery query) {

    }

    protected void buildCustomProcessedQuery(SearchTemplateQuery query) {

    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Arg extends StandardListController.Arg {
        @JSONField(name = "M17")
        @JsonProperty("un_processed_status")
        @SerializedName("un_processed_status")
        int unProcessedStatus;//1是待确认，2是已确认

        @JSONField(name = "M18")
        @JsonProperty("un_processed_num")
        @SerializedName("un_processed_num")
        int unProcessedNum;

        @JSONField(name = "M19")
        @JsonProperty("session_boc_item_key")
        @SerializedName("session_boc_item_key")
        int sessionBOCItemKey;
    }
}