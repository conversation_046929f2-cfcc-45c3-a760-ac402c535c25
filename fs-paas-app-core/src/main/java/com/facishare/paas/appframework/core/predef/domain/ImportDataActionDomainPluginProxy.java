package com.facishare.paas.appframework.core.predef.domain;

import com.facishare.rest.core.annotation.*;

import java.util.Map;

/**
 * Created by zhouwr on 2022/1/18.
 */
@RestResource(
        value = "NCRM",
        desc = "导入数据领域插件RPC代理类", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC"
)
public interface ImportDataActionDomainPluginProxy {
    @POST(desc = "导入数据领域插件rest接口")
    ImportDataActionDomainPlugin.RestResult post(@ServiceURLParam String url, @Body ImportDataActionDomainPlugin.Arg arg, @HeaderMap Map<String, String> header);
}
