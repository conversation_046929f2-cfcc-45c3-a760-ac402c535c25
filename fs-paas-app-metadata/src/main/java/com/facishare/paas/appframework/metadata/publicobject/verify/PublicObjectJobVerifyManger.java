package com.facishare.paas.appframework.metadata.publicobject.verify;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobType;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/26
 */
@Slf4j
@Component
public class PublicObjectJobVerifyManger implements ApplicationContextAware {
    private final Map<PublicObjectJobType, PublicObjectJobVerify> publicObjectJobVerifyMap = Maps.newHashMap();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        try {
            Map<String, PublicObjectJobVerify> batchSpringMap = applicationContext.getBeansOfType(PublicObjectJobVerify.class);
            if (CollectionUtils.notEmpty(batchSpringMap)) {
                batchSpringMap.forEach((x, y) -> y.getSupportedJobTypes().forEach(t -> publicObjectJobVerifyMap.put(t, y)));
            }
        } catch (BeansException e) {
            log.error("init PublicObjectFieldDescribeVerify error", e);
        }
    }

    public Optional<PublicObjectJobVerify> getPublicObjectJobVerify(PublicObjectJobType jobType) {
        return Optional.ofNullable(publicObjectJobVerifyMap.get(jobType));
    }
}
