package com.facishare.paas.appframework.log.dto;

import lombok.*;

import java.util.List;

public interface LoginLog {

    @Data
    class Arg {
        private LoginInfoCondition loginCondition;
        private String id;
        private Boolean isPrePage;
        private Boolean isChangePageSize;
        //每页的大小
        private int pageSize;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Result extends BaseResult {
        private List<LoginInfo> results;
        private int pageSize;
        private int totalCount;
        private Boolean hasMore;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class LoginInfoCondition {
        private String tenantId;
        //日志id，查询时为下一页的起始id
        private String id;
        //用户ID
        private List<String> userIds;
        //部门ID
        private List<String> deptIds;
        //设备归属人ID
        private List<String> ownerIds;
        //操作时间开始
        private Long operationTimeFrom;
        //操作时间结束
        private Long operationTimeTo;
        //登录时间
        private Long operationTime;
        private int pageSize;
        private Boolean isPrePage;
        private Boolean isChangePageSize;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class LoginInfo {
        //日志id，查询时为下一页的起始id
        private String id;
        //用户ID
        private String userId;
        //用户名
        private String userName;
        //部门ID
        private String deptId;
        //部门Name
        private String deptName;
        //登录ip
        private String loginIp;
        //登录方式
        private String loginType;
        //登录状态
        private String loginStatus;
        //设备归属人ID
        private String ownerId;
        //设备归属人
        private String ownerName;
        //设备型号
        private String loginBrowser;
        //异常信息
        private String exception;
        //登录时间
        private Long operationTime;
    }
}
