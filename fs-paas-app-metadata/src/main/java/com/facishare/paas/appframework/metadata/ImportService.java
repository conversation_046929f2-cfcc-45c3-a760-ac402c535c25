package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.ImportReferenceMapping;
import com.facishare.paas.appframework.metadata.dto.ImportTenantSetting;
import com.facishare.paas.appframework.metadata.importobject.ImportMarkInfo;
import com.facishare.paas.appframework.metadata.importobject.dataconvert.ImportBatchFieldDataConverter;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.TeamRoleInfo;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface ImportService {
    List<IFieldDescribe> getTemplateField(User user, IObjectDescribe objectDescribe);

    /**
     * 按照主角色的业务类型获取字段
     */
    List<IFieldDescribe> getTemplateField(User user, IObjectDescribe objectDescribe, String recordTypeApiName);

    List<IFieldDescribe> getUpdateImportTemplateField(User user, IObjectDescribe objectDescribe);

    /**
     * 按照主角色的业务类型获取字段
     */
    List<IFieldDescribe> getUpdateImportTemplateField(User user, IObjectDescribe objectDescribe, String recordTypeApiName);

    List<IFieldDescribe> getUpdateImportTemplateFieldIncludeFieldNames(User user, IObjectDescribe objectDescribe, List<String> fieldNames);

    boolean checkIsTitleForUpdateImport(IFieldDescribe fieldDescribe, IObjectDescribe describe);

    boolean checkIsTitle(IFieldDescribe fieldDescribe, IObjectDescribe describe);

    boolean isUniqueCheck(IObjectData data, IObjectDescribe describe, boolean isUpdate);

    String checkUnique(IObjectData data, IObjectDescribe describe, boolean isUpdate);

    List<IObjectData> generateAutoNumber(IObjectDescribe describe, List<IObjectData> objectDataList);

    Map<String, String> filterDataListByAutoNumberUniqueCheck(List<IObjectData> objectDataList, IObjectDescribe objectDescribe);

    ImportBatchFieldDataConverter getBatchFieldDataConverter(String fieldType);

    @Deprecated
    boolean saveUnionInsertImportMark(User user, String jobId, String describeApiName, Map<String, String> markIdMap);

    boolean saveUnionInsertImportMark(User user, String jobId, String describeApiName, List<ImportMarkInfo> importMarkInfos);

    Map<String, String> findUnionInsertImportMark(User user, String jobId, String objectCode, Collection<String> markLabel);

    ImportTenantSetting findImportTenantSetting(User user, String describeApiName);

    List<IFieldDescribe> generateTeamMemberField(User user, String describeApiName);

    List<TeamRoleInfo> findEnableTeamRoleInfosWithoutOwner(User user, String describeApiName);

    List<IFieldDescribe> generateTeamMemberByType(List<TeamRoleInfo> teamRoleInfos);

    ImportReferenceMapping findImportReferenceMapping(User user, String describeApiName);

    ImportReferenceMapping findImportReferenceMapping(User user, String describeApiName, boolean needLabel);
}
