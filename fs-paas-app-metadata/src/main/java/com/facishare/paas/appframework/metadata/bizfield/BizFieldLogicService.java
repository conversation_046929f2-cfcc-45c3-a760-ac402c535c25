package com.facishare.paas.appframework.metadata.bizfield;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;

/**
 * Created by zhouwr on 2023/9/15.
 */
public interface BizFieldLogicService {
    List<BizFieldDocument> getAllBizFieldsForManagement(String tenantId, String objectApiName);

    List<IFieldDescribe> getAllBizFields(String tenantId, IObjectDescribe describe);

    List<BizFieldDefinition> getAllValidBizFieldDefinitions(String tenantId, String objectApiName);
}
