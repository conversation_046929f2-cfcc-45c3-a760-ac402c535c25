package com.facishare.paas.appframework.rest.service;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.metadata.dto.DescribeDetailResult;
import com.facishare.paas.appframework.metadata.dto.DescribeResult;
import com.facishare.paas.appframework.metadata.menu.MenuCommonService;
import com.facishare.paas.appframework.rest.dto.data.IntelliFormDescribe;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by Yuanxl on 2018/5/8
 */
@Service
@Slf4j
public class IntelliFormDescribeService {

    @Autowired
    ServiceFacade serviceFacade;

    @Autowired
    MenuCommonService menuCommonService;

    @Autowired
    IObjectDescribeService objectDescribeService;

    public IntelliFormDescribe.AddFieldResult addField(IntelliFormDescribe.AddFieldArg arg,
                                                       RequestContext requestContext) {
        IntelliFormDescribe.AddFieldResult result = new IntelliFormDescribe.AddFieldResult();
        IObjectDescribe describe = serviceFacade.addDescribeCustomField(requestContext.getUser(),
                arg.getDescribeAPIName(), arg.getFieldDescribe(),null,null);
        if(Objects.isNull(describe)){
            return result;
        }
        IObjectDescribe describe2 = serviceFacade.findObject(requestContext.getTenantId(),
                arg.getDescribeAPIName());
        if(Objects.isNull(describe2)){
            return result;
        }

        result.setObjectDescribeDocument(ObjectDescribeDocument.of(describe2));
        return result;

    }

    public IntelliFormDescribe.CreateResult createDescribe(IntelliFormDescribe.CreateArg arg,
                                                           RequestContext requestContext) {
        IntelliFormDescribe.CreateResult result = new IntelliFormDescribe.CreateResult();
        DescribeResult describeResult = serviceFacade.createDescribe(requestContext.getUser(), arg.getJsonData()
                ,arg.getJsonLayout(), arg.getJsonListLayout(),true, arg.getIncludeLayout());
        ILayout layout = describeResult.getLayout();
        IObjectDescribe describe = describeResult.getObjectDescribe();

        result.setLayoutDocument(LayoutDocument.of(layout));
        result.setObjectDescribeDocument(ObjectDescribeDocument.of(describe));
        return result;
    }

    @SuppressWarnings("unchecked")
    public IntelliFormDescribe.DetailResult findDescribeDetail(IntelliFormDescribe.DetailArg arg,
                                                               RequestContext requestContext) {
        IntelliFormDescribe.DetailResult result = new IntelliFormDescribe.DetailResult();
        DescribeDetailResult describeDetailResult = serviceFacade.findDescribeByApiName(requestContext,
                arg.getDescribeAPIName(),
                true, ILayout.DETAIL_LAYOUT_TYPE, null, false, false, true, null);

        if(null == describeDetailResult){
            return result;
        }

        result.setLayoutDocument(LayoutDocument.of(describeDetailResult.getLayout()));

        if(!Boolean.TRUE.equals(arg.getIsExcludeDescribe())){
            result.setObjectDescribeDocument(ObjectDescribeDocument.of(describeDetailResult.getObjectDescribe()));
        }

        return result;
    }

    public IntelliFormDescribe.DisableResult disableDescribe(IntelliFormDescribe.DisableArg arg,
                                                             RequestContext requestContext) {
        IntelliFormDescribe.DisableResult result = new IntelliFormDescribe.DisableResult();

        IObjectDescribe disabledDescribe = serviceFacade.disableDescribe(requestContext.getUser(), arg.getDescribeAPIName());
        if(null == disabledDescribe){
            throw new ObjectDefNotFoundError(I18N.text(I18NKey.OBJECT_DESCRIBE_NOT_FOUND));
        }
        result.setObjectDescribeDocument(ObjectDescribeDocument.of(disabledDescribe));
        return result;
    }

    public IntelliFormDescribe.FindByPageResult findDescribeByPage(IntelliFormDescribe.FindByPageArg arg,
                                                                   RequestContext requestContext) {
        IntelliFormDescribe.FindByPageResult result = new IntelliFormDescribe.FindByPageResult();
        try {
            QueryResult<ObjectDescribe> queryResult = objectDescribeService.findByExampleWithFields(
                    requestContext.getTenantId(), arg.transToPGQueryInfo());

            List<ObjectDescribeDocument> describeDocumentList = Lists.newArrayList();
            if(!CollectionUtils.isEmpty(queryResult.getData())){
                describeDocumentList = queryResult.getData().stream().map(ObjectDescribeDocument::of)
                        .collect(Collectors.toList());
            }

            IntelliFormDescribe.PageInfo page = arg.getPage();
            page.setTotalCountAndTotalPage(queryResult.getTotalNumber() == null ? Integer.valueOf(0) : queryResult.getTotalNumber());

            result.setPage(page);
            result.setObjectDescribeDocumentList(describeDocumentList);
            return result;

        } catch (MetadataServiceException e) {
            throw new ValidateException(e.getMessage());
        }
    }

    public IntelliFormDescribe.ReplaceResult replaceDescribe(String arg,
                                                             RequestContext requestContext) {
        IntelliFormDescribe.ReplaceResult result = new IntelliFormDescribe.ReplaceResult();
        if(Strings.isNullOrEmpty(arg) || Strings.isNullOrEmpty(requestContext.getTenantId())
                || Objects.isNull(requestContext.getUser())){
           throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        IObjectDescribe objectDescribe = new ObjectDescribe(Document.parse(arg));
        objectDescribe.setTenantId(requestContext.getTenantId());

        IObjectDescribe describe = serviceFacade.findObject(requestContext.getTenantId(), objectDescribe.getApiName());
        if(null == describe){
            log.warn("describe not exists in intelliForm replace, tenantId:{}, apiName:{}",
                    requestContext.getTenantId(), objectDescribe.getApiName());
            throw new ValidateException(I18N.text(I18NKey.OBJECT_DESCRIBE_NOT_FOUND));
        }

        objectDescribe.setId(describe.getId());

        try {
            IObjectDescribe replaced = objectDescribeService.replace(objectDescribe, true);
            if(null != replaced){
                result.setObjectDescribeDocument(ObjectDescribeDocument.of(replaced));
            }
            return result;

        } catch (MetadataServiceException e) {
            throw new ValidateException(I18N.text(I18NKey.REPLACE_OBJECT_DESCRIBE_FAILED));
        }


    }
}
