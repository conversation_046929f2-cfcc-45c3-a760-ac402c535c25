package com.facishare.paas.appframework.metadata.mask;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2022/12/16
 */
@Service
public class MaskFieldLogicServiceImpl implements MaskFieldLogicService {
    @Autowired
    private UserRoleInfoService userRoleInfoService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private MaskFieldEncryptService maskFieldEncryptService;

    @Override
    public Map<String, List<IFieldDescribe>> getMaskFields(User user, Collection<IObjectDescribe> describeList, String ownerId) {
        if (user.isSupperAdmin()) {
            return Collections.emptyMap();
        }
        Map<String, List<IFieldDescribe>> maskFieldMap = Maps.newHashMap();
        for (IObjectDescribe describe : describeList) {
            List<IFieldDescribe> maskFields = ObjectDescribeExt.of(describe).getMaskFields();
            maskFieldMap.put(describe.getApiName(), maskFields);
        }
        // 下游请求暂不考虑去掩码的角色
        if (user.isOutUser()) {
            return maskFieldRoleFilter(user, maskFieldOutOwnerFilter(user, maskFieldMap, ownerId));
        }
        // 没有配置去掩码角色的字段,管理员角色默认去掩码
        if (userRoleInfoService.isAdmin(user)) {
            Map<String, List<IFieldDescribe>> roleConfigMaskFields = Maps.newHashMap();
            maskFieldMap.forEach((apiName, maskFieldDescribe) -> {
                List<IFieldDescribe> maskFieldList = CollectionUtils.nullToEmpty(maskFieldDescribe)
                        .stream()
                        .filter(field -> FieldDescribeExt.of(field).haveRolesConfig())
                        .collect(Collectors.toList());
                if (CollectionUtils.notEmpty(maskFieldList)) {
                    roleConfigMaskFields.put(apiName, maskFieldList);
                }
            });
            maskFieldMap = roleConfigMaskFields;
        }
        Map<String, List<IFieldDescribe>> result = maskFieldRoleFilter(user, maskFieldMap);
        return filterMaskFieldByOwner(user, result, ownerId);
    }

    private Map<String, List<IFieldDescribe>> filterMaskFieldByOwner(User user, Map<String, List<IFieldDescribe>> maskFieldMap, String ownerId) {
        if (Strings.isNullOrEmpty(ownerId)) {
            return maskFieldMap;
        }
        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfoMap = orgService.getMainDeptInfo(user.getTenantId(),
                user.getUserId(), Lists.newArrayList(ownerId));

        Map<String, List<IFieldDescribe>> result = Maps.newHashMap();
        maskFieldMap.forEach((objectApiName, fields) -> {
            List<IFieldDescribe> fieldDescribeList = Lists.newArrayList();
            for (IFieldDescribe field : fields) {
                // 配置了负责人去掩码
                FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(field);
                if (fieldDescribeExt.isRemoveMaskForOwner() && Objects.equals(user.getUserId(), ownerId)) {
                    continue;
                }
                if (CollectionUtils.empty(mainDeptInfoMap)) {
                    continue;
                }
                if (fieldDescribeExt.isRemoveMaskForDataOwnerMainDeptLeader()
                        && mainDeptInfoMap.containsKey(ownerId)) {
                    QueryDeptInfoByUserIds.MainDeptInfo deptInfo = mainDeptInfoMap.get(ownerId);
                    if (StringUtils.isNotEmpty(deptInfo.getLeaderId())
                            && StringUtils.equals(user.getUserId(), deptInfo.getLeaderId())) {
                        continue;
                    }
                }
                fieldDescribeList.add(field);
            }
            result.put(objectApiName, fieldDescribeList);
        });
        return result;
    }

    @Override
    public void processMaskFieldValue(User user, IObjectData masterData, Map<String, List<IObjectData>> detailDataMap, Map<String, IObjectDescribe> describeMap,
                                      MaskFieldConfig maskFieldConfig) {
        String ownerId;
        if (maskFieldConfig.isUseCurrentUser()) {
            ownerId = user.getUserIdOrOutUserIdIfOutUser();
        } else {
            ownerId = ObjectDataExt.of(masterData).getOwnerOrOutOwnerId(user);
        }
        Map<String, List<IFieldDescribe>> maskFieldMap = getMaskFields(user, describeMap.values(), ownerId);
        // 主对象
        fillMaskFieldValue(user, describeMap.get(masterData.getDescribeApiName()), Lists.newArrayList(masterData), maskFieldMap.get(masterData.getDescribeApiName()), maskFieldConfig);
        // 从对象
        detailDataMap.forEach((describeApiName, dataList) -> fillMaskFieldValue(user, describeMap.get(describeApiName), dataList, maskFieldMap.get(describeApiName), maskFieldConfig));
    }

    @Override
    public void fillMaskFieldValue(User user, IObjectDescribe describe, List<String> maskFieldApiNames, List<IObjectData> dataList) {
        if (CollectionUtils.empty(maskFieldApiNames)) {
            return;
        }
        if (!AppFrameworkConfig.maskFieldEncryptGray(user.getTenantId(), describe.getApiName())) {
            return;
        }
        List<IFieldDescribe> maskFields = ObjectDescribeExt.of(describe).getFieldByApiNames(maskFieldApiNames);
        if (CollectionUtils.empty(maskFields)) {
            return;
        }
        fillMaskFieldValue(user, describe, dataList, maskFields, MaskFieldLogicService.MaskFieldConfig.defaultMaskFieldConfig());
    }

    @Override
    public void fillMaskFieldValue(User user, IObjectDescribe describe, List<IObjectData> dataList, List<IFieldDescribe> encryptFields, MaskFieldConfig maskFieldConfig) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        List<IFieldDescribe> maskFields = Lists.newArrayList();
        if (maskFieldConfig.isNeedEncryptMaskFieldsWithDescribe()) {
            maskFields.addAll(ObjectDescribeExt.of(describe).getMaskFields());
        }
        if (CollectionUtils.empty(maskFields) && CollectionUtils.empty(encryptFields)) {
            return;
        }
        // encryptFields 中的字段一定要加密，不考虑是否配置了去掩码角色
        Set<String> encryptFieldNames = CollectionUtils.nullToEmpty(encryptFields).stream()
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toSet());
        maskFields.removeIf(field -> encryptFieldNames.contains(field.getApiName()));
        // 下游删除所有掩码字段，不需要考虑去掩码展示的角色
        if (user.isOutUser()) {
            List<IFieldDescribe> maskFieldsResult = maskFieldRoleFilter(user, maskFields);
            for (IObjectData objectData : dataList) {
                removeOrFillMaskFieldValue(objectData, encryptFields, maskFieldConfig);
                List<IFieldDescribe> filteredMaskField = maskFieldByOutOwner(user, ObjectDataExt.of(objectData).getOwnerOrOutOwnerId(user), maskFieldsResult);
                removeOrFillMaskFieldValue(objectData, filteredMaskField, maskFieldConfig);
            }
            if (!maskFieldConfig.isFill()) {
                dataList.forEach(data -> ObjectDataExt.of(data).remove(maskFields));
            } else {
                dataList.forEach(data -> ObjectDataExt.of(data).fillMaskValue(maskFields, maskFieldConfig.isRemoveOrigValue(), false));
            }
            return;
        }
        // 获取真正需要掩码的掩码字段
        List<IFieldDescribe> filteredMaskField = getFilteredMaskFields(user, maskFields);
        Set<String> ownerIds = dataList.stream()
                .map(x -> ObjectDataExt.of(x).getOwnerId().orElse(null))
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfoMap = orgService.getMainDeptInfo(user.getTenantId(),
                user.getUserId(), Lists.newArrayList(ownerIds));

        removeOrFillMaskFieldValue(user, dataList, filteredMaskField, encryptFields, mainDeptInfoMap, maskFieldConfig);
    }

    private void removeOrFillMaskFieldValue(User user, List<IObjectData> dataList, List<IFieldDescribe> maskFields,
                                            List<IFieldDescribe> encryptFields, Map<String, QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfoMap,
                                            MaskFieldConfig maskFieldConfig) {
        for (IObjectData data : dataList) {
            removeOrFillMaskFieldValue(data, encryptFields, maskFieldConfig);
            String ownerId = ObjectDataExt.of(data).getOwnerId().orElse(null);
            //数据没有负责人则统一展示掩码
            if (StringUtils.isEmpty(ownerId)) {
                removeOrFillMaskFieldValue(data, maskFields, maskFieldConfig);
                continue;
            }
            List<IFieldDescribe> maskFieldsResult = Lists.newArrayList();
            maskFields.forEach(field -> {
                //配置了负责人去掩码
                if (FieldDescribeExt.of(field).isRemoveMaskForOwner() && StringUtils.equals(user.getUserId(), ownerId)) {
                    return;
                }
                //配置了主属部门负责人去掩码
                if (FieldDescribeExt.of(field).isRemoveMaskForDataOwnerMainDeptLeader()
                        && CollectionUtils.notEmpty(mainDeptInfoMap)
                        && mainDeptInfoMap.containsKey(ownerId)) {
                    QueryDeptInfoByUserIds.MainDeptInfo deptInfo = mainDeptInfoMap.get(ownerId);
                    if (StringUtils.isNotEmpty(deptInfo.getLeaderId())
                            && StringUtils.equals(user.getUserId(), deptInfo.getLeaderId())) {
                        return;
                    }
                }
                maskFieldsResult.add(field);
            });
            removeOrFillMaskFieldValue(data, maskFieldsResult, maskFieldConfig);
        }
    }

    private void removeOrFillMaskFieldValue(IObjectData data, List<IFieldDescribe> maskFields, MaskFieldConfig maskFieldConfig) {
        if (maskFieldConfig.isEncrypt()) {
            maskFieldEncryptService.encode(data, maskFields);
        }
        if (maskFieldConfig.isFill()) {
            ObjectDataExt.of(data).fillMaskValue(maskFields, maskFieldConfig.isRemoveOrigValue(), false);
        } else {
            ObjectDataExt.of(data).remove(maskFields);
        }
    }

    private List<IFieldDescribe> getFilteredMaskFields(User user, List<IFieldDescribe> maskFields) {
        if (CollectionUtils.empty(maskFields)) {
            return Lists.newArrayList();
        }
        if (userRoleInfoService.isAdmin(user)) {
            List<IFieldDescribe> roleConfigMaskFiled = maskFields.stream().filter(field -> FieldDescribeExt.of(field).haveRolesConfig()).collect(Collectors.toList());
            if (CollectionUtils.empty(roleConfigMaskFiled)) {
                return Lists.newArrayList();
            }
            return maskFieldRoleFilter(user, roleConfigMaskFiled);
        }
        return maskFieldRoleFilter(user, maskFields);
    }

    @Override
    public void encodeDefaultValueByFieldDescribe(User user, IFieldDescribe fieldDescribe) {
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
        if (!fieldDescribeExt.getMaskFieldEncrypt()) {
            return;
        }
        // 有计算公式,或者默认值为空,不需要处理
        if (fieldDescribeExt.isCalculateField() || ObjectDataExt.isValueEmpty(fieldDescribeExt.getDefaultValue())) {
            return;
        }
        IObjectData data = new ObjectData();
        data.set(fieldDescribe.getApiName(), fieldDescribeExt.getDefaultValue());
        maskFieldEncryptService.encode(data, Lists.newArrayList(fieldDescribe));
        fieldDescribe.set("default_value_encrypt", data.get(FieldDescribeExt.getMaskEncryptFieldName(fieldDescribe.getApiName())));
    }

    @Override
    public void decodeMaskFieldEncryptValue(User user, List<IObjectData> dataList, IObjectDescribe describe) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }

        for (IObjectData objectData : dataList) {
            maskFieldEncryptService.decode(objectData, describe);
            // 移除 __s 字段
            removeMaskShowFieldName(objectData);
        }
    }

    @Override
    public List<String> getMaskFieldTypes(String tenantId, String describeApiName) {
        if (Strings.isNullOrEmpty(describeApiName)) {
            describeApiName = AppFrameworkConfig.UDOBJ;
        }
        List<String> result = Lists.newArrayList(IFieldType.CURRENCY, IFieldType.EMAIL, IFieldType.PHONE_NUMBER);
        if (AppFrameworkConfig.maskFieldEncryptGray(tenantId, describeApiName)) {
            result.addAll(Lists.newArrayList(IFieldType.NUMBER, IFieldType.TEXT, IFieldType.COUNT, IFieldType.FORMULA, IFieldType.QUOTE));
        }
        return ImmutableList.copyOf(result);
    }

    private void removeMaskShowFieldName(IObjectData objectData) {
        ObjectDataExt.of(objectData).toMap()
                .keySet()
                .removeIf(FieldDescribeExt::isMaskShowFieldName);
    }

    private Map<String, List<IFieldDescribe>> maskFieldRoleFilter(User user, Map<String, List<IFieldDescribe>> maskFieldsMap) {
        Map<String, List<IFieldDescribe>> maskFieldMap = Maps.newHashMap();
        maskFieldsMap.forEach((k, v) -> maskFieldMap.put(k, maskFieldRoleFilter(user, v)));
        return maskFieldMap;
    }

    private Map<String, List<IFieldDescribe>> maskFieldOutOwnerFilter(User user, Map<String, List<IFieldDescribe>> maskFieldsMap, String outOwnerId) {
        Map<String, List<IFieldDescribe>> maskFieldMap = Maps.newHashMap();
        maskFieldsMap.forEach((k, v) -> maskFieldMap.put(k, maskFieldByOutOwner(user, outOwnerId, v)));
        return maskFieldMap;
    }

    private List<IFieldDescribe> maskFieldByOutOwner(User user, String outOwnerId, List<IFieldDescribe> maskFields) {
        return maskFields.stream()
                .filter(fieldDescribe -> FieldDescribeExt.of(fieldDescribe).noOutOwnerMaskConfig(user, outOwnerId))
                .collect(Collectors.toList());
    }

    @Override
    public List<IFieldDescribe> maskFieldRoleFilter(User user, List<IFieldDescribe> maskFields) {
        List<IFieldDescribe> maskFieldsResult = Lists.newArrayList();
        List<String> userRoles = null;
        for (IFieldDescribe maskField : maskFields) {
            if (FieldDescribeExt.of(maskField).haveRolesConfig(user)) {
                if (Objects.isNull(userRoles)) {
                    userRoles = CollectionUtils.nullToEmpty(userRoleInfoService.getUserRole(user));
                }
                // 配置了去掩码角色并且用户在配置中则不将数据进行打码处理即返回false不处理此字段否则返回true
                List<String> maskRolesConfig = FieldDescribeExt.of(maskField).getMaskRolesConfig(user);
                if (userRoles.stream().anyMatch(maskRolesConfig::contains)) {
                    continue;
                }
            }
            maskFieldsResult.add(maskField);
        }
        return maskFieldsResult;
    }

}
