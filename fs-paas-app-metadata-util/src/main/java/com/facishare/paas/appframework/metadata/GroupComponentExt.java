package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IGroupComponent;
import lombok.Getter;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

/**
 * Created by zhouwr on 2018/1/31
 */
@Slf4j
public class GroupComponentExt {

    @Getter
    @Delegate
    private IGroupComponent groupComponent;

    private GroupComponentExt(IGroupComponent groupComponent) {
        this.groupComponent = groupComponent;
    }

    public static GroupComponentExt of(IGroupComponent groupComponent) {
        return new GroupComponentExt(groupComponent);
    }

    public List<IComponent> getChildComponentsSilently() {
        try {
            return CollectionUtils.nullToEmpty(getChildComponents());
        } catch (MetadataServiceException e) {
            log.error("getChildComponents error,groupComponent:{}", groupComponent, e);
            throw new MetaDataException(SystemErrorCode.METADATA_ERROR);
        }
    }

    public boolean containsChildComponent() {
        return CollectionUtils.notEmpty(getChildComponentsSilently());
    }

    public void removeButtonByActionsDeeply(List<String> actions) {
        List<IComponent> childComponents = getChildComponentsSilently();
        if (CollectionUtils.notEmpty(childComponents)) {
            childComponents.forEach(x -> ComponentExt.of(x).removeButtonByActionsDeeply(actions));
        }

        ComponentExt.of(groupComponent).removeButtonByActions(actions);
    }

    public Optional<IComponent> getChildComponentByName(String name) {
        return getChildComponentsSilently().stream().filter(x -> name.equals(x.getName())).findFirst();
    }

    public void removeChildComponentByName(String name) {
        List<IComponent> childComponents = getChildComponentsSilently();
        childComponents.removeIf(x -> name.equals(x.getName()));
        setChildComponents(childComponents);
    }

}
