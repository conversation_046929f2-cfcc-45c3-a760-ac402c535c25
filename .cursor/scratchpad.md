# MDC单元测试辅助工具项目

## 背景和动机
在Java项目开发过程中，编写高质量的单元测试是保障代码质量的重要手段。然而，创建测试数据通常是一个繁琐的过程，特别是对于复杂的数据结构。创建一个MDC(Mock Data Container)工具可以帮助开发者快速生成常见数据结构的测试数据，提高测试效率。基于现有的`301-unit-demo.mdc`，我们计划创建一个更全面、更易用的MDC工具，**采用基于JSON字符串反序列化的方式**，生成和管理测试数据，并自动根据被测试代码中使用的数据结构生成相应的测试数据。

## 关键挑战和分析
1. 整合现有`301-unit-demo.mdc`中的数据构造示例，确保新MDC兼容现有用法
2. 识别项目中其他常用的数据结构和模型，扩展MDC的覆盖范围
3. **设计基于JSON的数据生成方法，提供各种数据结构的JSON模板**
4. 确保生成的测试数据与项目现有测试框架(Spock)兼容
5. 提供足够的灵活性，允许开发者根据需求自定义测试数据
6. 为各种数据结构提供默认值和常用场景的预设配置
7. **实现智能匹配功能，自动识别被测试代码中使用的数据结构，并生成相应的JSON数据**
8. **支持更复杂的业务对象，如DuplicatedSearch等查重规则配置**

## 高层任务拆分
1. 分析`301-unit-demo.mdc`中的数据结构和构造方式，整理出核心数据类型
   - 用户相关: User
   - 对象描述相关: IObjectDescribe, IFieldDescribe
   - 对象数据相关: IObjectData
   - 上下文相关: ServiceContext, RequestContext
   - 查询条件相关: Wheres, Filter
   - **业务规则相关: DuplicatedSearch（查重规则）**
2. **设计基于JSON的数据生成框架**
   - **定义各种数据结构的JSON模板**
   - **设计JSON数据自定义和扩展机制**
   - **支持通过反序列化JSON生成对象**
3. 实现基础数据结构的JSON模板
   - 简单数据类型(字符串、数字、日期等)
   - 集合类型(List、Set、Map等)
4. 实现业务相关模型的JSON模板
   - 上下文类型(User, RequestContext, ServiceContext)
   - 元数据类型(IObjectDescribe, IFieldDescribe)
   - 数据类型(IObjectData)
   - 查询类型(Wheres, Filter)
   - **查重规则(DuplicatedSearch)**
5. **实现智能匹配功能**
   - 解析被测试代码，识别使用的数据结构
   - 自动生成所需的测试数据结构的JSON
   - 支持数据依赖关系的自动处理
6. 添加适当的文档和示例代码
7. 编写MDC自身的单元测试
8. 集成到项目中并进行测试

## 项目状态看板
- [x] 任务1: 分析`301-unit-demo.mdc`中的数据结构和构造方式
- [x] 任务2: 设计基于JSON的数据生成框架
- [x] 任务3: 实现基础数据结构的JSON模板
- [x] 任务4: 实现业务相关模型的JSON模板
- [ ] 任务4.1: 添加DuplicatedSearch查重规则的JSON模板
- [x] 任务5: 实现智能匹配功能
- [x] 任务6: 添加适当的文档和示例代码
- [ ] 任务7: 编写MDC自身的单元测试
- [ ] 任务8: 集成到项目中并进行测试

## 当前状态/进度跟踪
已完成初版基于JSON的MDC开发，创建了`.cursor/rules/302-test-data-factory.mdc`文件，实现了主要数据结构的JSON模板和智能匹配功能。现在需要根据新提供的DuplicatedSearch示例，添加对查重规则的支持。下一步也需要编写单元测试并将其集成到项目中。

## 执行者反馈或请求帮助
已收到用户提供的DuplicatedSearch示例，该示例展示了查重规则的JSON结构，包含了规则名称、查询条件、数据范围、有效规则等信息。我们将根据这个示例添加DuplicatedSearch的JSON模板到MDC中。

需要确认以下几点：
1. DuplicatedSearch对象是通过哪个类来表示的？
2. 是否需要支持其他类似的业务规则类型？
3. DuplicatedSearch中的一些子结构（如useable_rules）是否需要单独提供模板？

## 经验教训
1. 使用JSON模板可以有效简化复杂对象的创建过程，便于可视化和调整
2. 反序列化方式比链式调用更加直观，特别是对于复杂对象结构
3. 智能匹配功能需要考虑数据依赖关系，确保生成的JSON数据之间保持一致性
4. 实际项目中往往存在更复杂的业务对象（如DuplicatedSearch），需要特别关注这些对象的结构和关系 

# MDC单元测试辅助工具项目改进计划

## 背景和动机
当前项目已经有多个MDC文件用于辅助单元测试的编写：
- 300-generate-unit-test.mdc: Java代码生成Groovy单元测试的通用指南
- 301-appframework-base-unit-demo.mdc: 提供各种对象构造示例
- 301-appframework-base.mdc: 定义fs-paas-appframework项目的测试规范
- 301-appframework-bussiness.mdc: Action/Controller类的PowerMock单元测试规范

这些文档为依赖项目的服务编写单元测试提供了指导，但在实际使用过程中发现Cursor不能很好地按照规则执行，特别是在处理PowerMock单元测试时，Cursor未能正确添加所需的注解和实现相关代码结构。我们需要对这些MDC文件进行全面改进，解决已发现的问题，使其更加完善、一致和易于使用。

## 关键挑战和分析
1. **PowerMock规则执行问题**：Cursor不能很好理解PowerMock相关规则，没有正确生成PowerMock单元测试类和相关注解
2. **规则解析问题**：可能是由于规则描述不够明确或存在二义性，导致Cursor无法正确理解和执行
3. **文档一致性问题**：现有MDC文件之间可能存在内容重叠或冲突，需要统一规范
4. **示例不足**：缺少完整、规范的PowerMock测试示例，导致Cursor理解模糊
5. **决策判断逻辑不清晰**：如何判断何时使用PowerMock的规则可能不够明确
6. **MDC引用规范不统一**：各MDC文件之间的引用方式可能不一致，导致解析错误

## 高层任务拆分
1. **修复PowerMock单元测试生成问题**
   - 分析Cursor未能正确生成PowerMock单元测试的具体原因
   - 重新设计PowerMock测试规则描述方式，确保Cursor能正确理解
   - 提供更明确、完整的PowerMock测试示例和注解规范
   - 强化决策逻辑描述，明确什么情况下应使用PowerMock

2. **优化文档结构和组织**
   - 设计统一的文档结构模板，确保各MDC文件风格一致
   - 明确各文档的优先级和应用顺序
   - 消除文档间的冲突和重复内容
   - 标准化MDC文件间的引用方式

3. **增强规则表达清晰度**
   - 改进规则描述，使用更精确的指令语言
   - 为每条规则提供明确的用例和反例
   - 增加关键判断点的详细解释
   - 使用图表或流程图增强关键决策流程的可视化

4. **扩展示例代码库**
   - 增加更多真实场景的示例代码
   - 为每种测试类型提供完整的模板代码
   - 确保示例覆盖所有常见边界情况
   - 添加错误示例和正确示例的对比

5. **强化PowerMock使用指南**
   - 明确PowerMock的适用情况和使用限制
   - 提供完整的PowerMock注解和配置说明
   - 详细说明各种父类方法调用的处理技术
   - 增加PowerMock常见问题的解决方案

6. **提升文档可用性**
   - 简化文档语言，避免技术术语过于复杂
   - 增加更多注释和解释
   - 添加目录和索引，便于查找特定内容
   - 提供快速参考卡片式总结

7. **验证和测试改进效果**
   - 使用改进后的MDC文件测试多个业务场景
   - 收集实际使用反馈
   - 持续迭代优化规则内容

## 项目状态看板
- [x] 任务0: 初步分析问题原因
- [x] 任务1: 修复PowerMock单元测试生成问题
  - [x] 1.1: 分析Cursor未能正确生成PowerMock单元测试的具体原因
  - [x] 1.2: 重新设计PowerMock测试规则描述方式
  - [x] 1.3: 提供更明确、完整的PowerMock测试示例
  - [x] 1.4: 强化决策逻辑描述
- [x] 任务2: 优化文档结构和组织
  - [x] 2.1: 为301-appframework-bussiness.mdc添加明确的模板和示例
  - [x] 2.2: 重新组织301-appframework-base.mdc的决策流程
- [ ] 任务3: 增强规则表达清晰度
- [ ] 任务4: 扩展示例代码库
- [ ] 任务5: 强化PowerMock使用指南
- [ ] 任务6: 提升文档可用性
- [ ] 任务7: 验证和测试改进效果

## 当前状态/进度跟踪
已经完成对两个核心MDC文件的改进：

1. **301-appframework-bussiness.mdc**:
   - 添加了明确的"重要说明 - 必须遵循"部分
   - 增加了完整的PowerMock测试类模板示例
   - 改进了父类方法调用处理技术的描述和示例
   - 添加了决策流程图和常见错误解决方案
   - 增加了完整的工作流程示例

2. **301-appframework-base.mdc**:
   - 添加了清晰的核心规则
   - 设计了直观的决策流程图
   - 改进了关键识别点的描述
   - 添加了详细的场景示例
   - 增加了父类方法调用识别的细节说明

这些改进主要针对Cursor生成PowerMock单元测试时遇到的问题，通过更明确的指令、完整的示例代码和决策流程说明，使Cursor能更好地理解如何生成符合规范的PowerMock测试。

## 执行者反馈或请求帮助
需要验证改进后的MDC文件是否能够解决PowerMock测试生成的问题：
1. 是否可以提供一个待测试的Action或Controller类，让我们验证Cursor是否能够正确生成PowerMock测试？
2. 改进后的MDC文件是否还有其他需要优化的地方？
3. 在实际使用过程中，是否还有其他常见问题需要解决？

## 经验教训
1. AI模型理解复杂规则集时存在局限性，需要更明确、结构化的描述
2. MDC文件中的规则应使用明确的指令语句，避免使用含糊的表述
3. 决策流程需要使用可视化方式表示，如流程图或决策树
4. 提供多个完整的示例代码对于AI理解规则至关重要
5. 明确区分"必须遵循"的规则和一般性建议，优先级排序很重要
6. 针对常见错误提供具体的解决方案和对比示例
7. 文件结构应清晰有序，容易导航，重要信息应放在前面 