{"swagger": "2.0", "info": {"title": "AppFrameWork REST API", "description": "PaaS 应用框架REST API", "version": "1.0"}, "host": "[ceshi113]************:8014,[ceshi112]************:8009,[product]ncrmrest.nsvc.foneshare.cn", "schemes": ["http"], "basePath": "/API/v1/inner/rest", "produces": ["application/json"], "commonParams": {"includeDescribe": {"name": "includeDescribe", "in": "query", "description": "返回结果是否包含对象描述", "required": false, "type": "boolean", "default": false}, "userInfo": {"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, "eid": {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, "peerHost": {"name": "x-fs-peer-host", "in": "header", "description": "调用方Host地址", "required": false, "type": "string"}, "peerName": {"name": "x-fs-peer-name", "in": "header", "description": "调用方Name，例如FBS,OpenAPI,CRM等", "required": false, "type": "string"}, "eventID": {"name": "x-fs-eventId", "in": "header", "description": "防止工作流更新循环触发调用标识", "required": false, "type": "string"}, "apiName": {"name": "describeAPIName", "in": "path", "required": true, "type": "string", "description": "元数据对象描述的API name", "default": "customer"}, "isTool": {"name": "isTool", "in": "query", "required": false, "type": "boolean"}, "dataId": {"name": "dataId", "in": "path", "required": true, "type": "string", "description": "数据ID", "default": "1"}, "triggerWorkFlow": {"name": "triggerWorkFlow", "in": "query", "required": false, "type": "boolean", "description": "是否触发工作流", "default": "false"}}, "paths": {"/{describeAPIName}": {"post": {"summary": "create object data", "description": "创建自定义对象数据", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"$ref": "#/commonParams/eventID"}, {"$ref": "#/commonParams/apiName"}, {"$ref": "#/commonParams/isTool"}, {"$ref": "#/commonParams/triggerWorkFlow"}, {"name": "body", "in": "body", "required": true, "describe": "要新增的数据对象的json结构字符串", "schema": {"$ref": "#/definitions/DataJson"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DataAndDescribe"}}}}}, "/{describeAPIName}/{dataId}": {"put": {"summary": "update object data", "description": "更新自定义对象数据", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"$ref": "#/commonParams/eventID"}, {"$ref": "#/commonParams/apiName"}, {"$ref": "#/commonParams/dataId"}, {"$ref": "#/commonParams/isTool"}, {"name": "triggerFlow", "in": "query", "type": "string", "default": "true", "required": false, "describe": "是否触发流程（包含审批流、工作流"}, {"name": "incrementalUpdate", "in": "query", "type": "boolean", "default": false, "required": false, "describe": "是否是增量修改"}, {"name": "body", "in": "body", "required": true, "describe": "要更新的数据对象的json结构字符串", "schema": {"$ref": "#/definitions/DataJson"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DataAndDescribe"}}}}, "delete": {"summary": "delete object by id", "description": "根据id删除自定义对象", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"$ref": "#/commonParams/apiName"}, {"$ref": "#/commonParams/dataId"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DataAndDescribe"}}}}}, "/{describeAPIName}/invalid/{dataId}": {"delete": {"summary": "invalid object data", "description": "根据对象数据Id作废数据", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"$ref": "#/commonParams/eventID"}, {"$ref": "#/commonParams/apiName"}, {"$ref": "#/commonParams/dataId"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DataAndDescribe"}}}}}, "/{describeAPIName}/object_names": {"post": {"summary": "query by object data ID", "description": "根据数据对象Id列表查询", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"$ref": "#/commonParams/apiName"}, {"name": "body", "in": "body", "required": true, "describe": "要新增的数据对象的json结构字符串", "schema": {"$ref": "#/definitions/DataJson"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DataAndDescribe"}}}}}, "/{describeAPIName}/query": {"post": {"summary": "query by object data by searchQuery", "description": "根据查询条件（SearchQuery对象）来进行查询", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"$ref": "#/commonParams/apiName"}, {"#ref": "#/commonParams/includeDescribe"}, {"name": "body", "in": "body", "required": true, "describe": "查询条件对象(SearchQuery的Json格式字符串）", "schema": {"$ref": "#/definitions/QueryJson"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DataAndDescribe"}}}}}, "/{describeAPIName}/query_list_by_ids": {"post": {"summary": "query by object ID list", "description": "根据对象ID列表(Json格式字符串）来进行查询", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"$ref": "#/commonParams/apiName"}, {"name": "body", "in": "body", "required": true, "describe": "查询条件对象(SearchQuery的Json格式字符串）", "schema": {"$ref": "#/definitions/DataJson"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/QueryResult"}}}}}, "/{describeAPIName}/recover/{dataId}": {"get": {"summary": "recover object data by ID", "description": "根据id进行数据对象的恢复操作", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"$ref": "#/commonParams/apiName"}, {"$ref": "#/commonParams/dataId"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DataAndDescribe"}}}}}, "/{describeAPIName}/relatedquery/{dataId}": {"post": {"summary": "query by searchQuery", "description": "根据查询条件（SearchQuery对象）来进行查询", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"$ref": "#/commonParams/apiName"}, {"$ref": "#/commonParams/dataId"}, {"$ref": "#/commonParams/includeDescribe"}, {"name": "body", "in": "body", "required": true, "describe": "查询条件对象(SearchQuery的Json格式字符串）", "schema": {"$ref": "#/definitions/QueryJson"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/QueryRelateResult"}}}}}, "/{descAPIName}/{dataId}": {"get": {"summary": "query by object id", "description": "根据自定义对象id获取自定义对象", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"name": "descAPIName", "in": "path", "required": true, "type": "string", "description": "元数据对象描述的API name", "default": "customer"}, {"$ref": "#/commonParams/dataId"}, {"$ref": "#/commonParams/includeDescribe"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DataAndDescribe"}}}}}, "/object_describe/": {"get": {"summary": "query object describe list by tenant id", "description": "根据企业Id查询对象描述列表", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"name": "package", "in": "query", "type": "string", "description": "package name", "default": "CRM"}, {"name": "includeFieldsDesc", "in": "query", "type": "boolean", "describe": "是否包含字段描述", "default": true}, {"name": "includeUnActived", "in": "query", "type": "boolean", "describe": "是否包含未激活字段", "default": false}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ObjectDescribeList"}}}}}, "/object_describe/{apiName}": {"get": {"summary": "query object describe by tenant id and apiName", "description": "根据企业ID和对象的api name查询object describe对象", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"name": "include_ref_describe", "in": "query", "type": "string", "description": "是否包含关联对象的describe信息", "default": "false"}, {"name": "include_detail_describe", "in": "query", "type": "boolean", "describe": "是否包含详细描述"}, {"name": "apiName", "in": "path", "type": "string", "describe": "对象的apiName", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ObjectDescribe"}}}}}, "/object_describe/{apiName}/ref": {"get": {"summary": "query relative object", "description": "查询关联对象", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"name": "apiName", "in": "path", "type": "string", "describe": "对象的apiName", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ObjectDescribeList"}}}}}, "/object_describe/list": {"post": {"summary": "query object by api list ", "description": "根据api List 查询对象", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"name": "include_ref_describe", "in": "query", "type": "string", "description": "是否包含关联对象的describe信息", "default": "false"}, {"name": "body", "in": "body", "required": true, "describe": "对象api name列表", "schema": {"$ref": "#/definitions/apiList"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ObjectDescribeList"}}}}}, "/object_describe/draft": {"get": {"summary": "query object describe list by tenant id", "description": "根据企业ID获取自定义对象描述列表", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/userInfo"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ObjectDescribeList"}}}}}, "/object_describe/draft/{describeDraftId}": {"get": {"summary": "query object describe by id", "description": "根据id获取自定义对象描述", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/userInfo"}, {"name": "describeDraftId", "in": "path", "type": "string", "describe": "对象描述ID", "required": true, "default": "1"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ObjectDescribe"}}}}}, "/object_describe/{tenantId}": {"post": {"summary": "create object describe by tenantId", "description": "根据企业id创建自定义对象描述", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/userInfo"}, {"name": "tenantId", "in": "path", "type": "string", "describe": "企业ID", "required": true}, {"name": "isActivate", "in": "query", "type": "boolean", "description": "是否激活", "default": "true", "required": false}, {"name": "<PERSON><PERSON><PERSON>", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DescribeJson"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ObjectDescribe"}}}}}, "/object_describe/{tenantId}/{apiName}": {"post": {"summary": "update object describe", "description": "更新自定义对象描述", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/userInfo"}, {"name": "isActivate", "in": "query", "type": "boolean", "description": "是否激活", "default": "true", "required": false}, {"name": "tenantId", "in": "path", "type": "string", "describe": "企业ID", "required": true}, {"name": "apiName", "in": "path", "type": "string", "describe": "对象的apiName", "required": true}, {"name": "<PERSON><PERSON><PERSON>", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DescribeJson"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ObjectDescribe"}}}}}, "/object_describe/{tenantId}/{describeDraftId}": {"post": {"summary": "update object describe", "description": "更新自定义对象描述", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/userInfo"}, {"name": "tenantId", "in": "path", "type": "string", "describe": "企业ID", "required": true}, {"name": "describeDraftId", "in": "path", "type": "string", "describe": "对象描述ID", "required": true}, {"name": "<PERSON><PERSON><PERSON>", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DescribeJson"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ObjectDescribe"}}}}}}, "definitions": {"DescribeJson": {"type": "object"}, "DataAndDescribe": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"describe": {"type": "object"}, "object_data": {"type": "object"}}}}}, "DataJson": {"type": "object"}, "QueryJson": {"type": "object"}, "QueryResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object"}}}, "QueryRelateResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"related_list": {"type": "array", "items": {"type": "object"}}}}}}, "ObjectDescribeList": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"describe": {"type": "array", "items": {"type": "object"}}}}}}, "ObjectDescribe": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"describe": {"type": "object"}}}}}, "apiList": {"type": "array", "item": {"type": "string"}}}}