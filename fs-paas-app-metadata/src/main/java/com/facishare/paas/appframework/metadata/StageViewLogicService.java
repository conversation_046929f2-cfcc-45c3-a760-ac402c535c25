package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.StageViewInfo;

import java.util.List;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/02/05
 */
public interface StageViewLogicService {
    boolean saveStageView(String describeApiName, List<StageViewInfo> viewInfo, User user);

    List<StageViewInfo> findStageView(String describeApiName, User user);

    boolean deleteStageView(String describeApiName, User user);
}
