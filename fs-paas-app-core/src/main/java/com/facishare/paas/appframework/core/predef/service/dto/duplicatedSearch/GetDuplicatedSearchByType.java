package com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface GetDuplicatedSearchByType {
    @Data
    class Arg {
        @J<PERSON><PERSON>ield(name = "M1")
        private IDuplicatedSearch.Type duplicated_search_type;
        @J<PERSON><PERSON>ield(name = "M2")
        private boolean is_need_duplicate_search;
        @J<PERSON><PERSON>ield(name = "M3")
        private String master_object_type;
        @J<PERSON><PERSON>ield(name = "M4")
        private String slave_object_type;
        @JSONField(name = "M5")
        private String ignore_id;
        @J<PERSON><PERSON>ield(name = "M6")
        private String page_number;
        @JSONField(name = "M7")
        private String page_size;
        @J<PERSON><PERSON>ield(name = "M8")
        private Entity entity;
    }

    @Data
    @Builder
    class Entity {
        private String field_name;
        private Integer field_property;
        private Integer field_type;
        private Value value;
        private String user_defined_field_id;
    }

    @Data
    @Builder
    class Value {
        private String value;
    }

    @Data
    @Builder
    class Result {
        private List<duplicatedSearchObjectInfos> duplicated_search_object_infos;
    }

    @Data
    @Builder
    class duplicatedSearchObjectInfos {
        List<IFieldDescribe> duplicate_search_fields;
        Integer object_mode;
        Integer object_type;
        Page page;
    }

    @Data
    @Builder
    class Page {
        private int page_size;
        private int page_number;
        private int page_count;
        private int total_count;
    }
}
