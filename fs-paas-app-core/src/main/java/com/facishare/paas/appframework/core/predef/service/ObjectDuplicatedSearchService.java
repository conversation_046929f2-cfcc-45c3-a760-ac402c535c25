package com.facishare.paas.appframework.core.predef.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.common.exception.CrmCheckedException;
import com.facishare.crm.privilege.service.UserInfoService;
import com.facishare.crm.privilege.swagger.model.AuthContext;
import com.facishare.crm.privilege.util.Constant;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.DepartmentService;
import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.*;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.cache.RedisDao;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchDataInfo;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchOrderByType;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchResult;
import com.facishare.paas.appframework.metadata.expansion.DescribeExpansionRender;
import com.facishare.paas.appframework.metadata.objects.ObjectListConfig;
import com.facishare.paas.appframework.metadata.qixin.QiXinAppLogicService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.data.IDuplicatedSearchRefresh;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.api.IdGenerator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.cache.RedissonService.LimiterInfo;

@ServiceModule("duplicate_search")
@Service
@Slf4j
public class ObjectDuplicatedSearchService {

    @Autowired
    private DuplicatedSearchService duplicatedSearchService;

    @Autowired
    private DuplicatedSearchDataService duplicatedSearchDataService;

    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private RedisDao redisDao;

    @Autowired
    private MetaDataFindService metaDataFindService;

    @Autowired
    private MetaDataMiscService metaDataMiscService;

    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;

    @Autowired
    private UserRoleInfoService userRoleInfoService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private QuoteValueService quoteValueService;

    @Autowired
    private RedissonService redissonService;

    @Autowired
    private QiXinAppLogicService qiXinAppLogicService;

    @Autowired
    private DepartmentService departmentService;

    private String CACHE_LEVEL_UNIQUE_REQUEST = "uniqueRequest";

    /**
     * 保存查重规则
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("save")
    public SaveDuplicatedSearch.Result save(SaveDuplicatedSearch.Arg arg, ServiceContext context) {
        return createOrUpdateDuplicateRule(arg, context);
    }

    @ServiceMethod("create")
    public SaveDuplicatedSearch.Result create(SaveDuplicatedSearch.Arg arg, ServiceContext context) {
        IDuplicatedSearch iDuplicatedSearch = duplicatedSearchService.findDuplicatedSearchByRuleApiName(arg.getDuplicateSearch().getDescribeApiName(),
                arg.getDuplicateSearch().getRuleApiName(), context.getTenantId(), true);
        //如果规则为空，则代表是新建，需要校验规则是否
        if (Objects.nonNull(iDuplicatedSearch)) {
            throw new ValidateException(I18NExt.text(I18NKey.RULE_API_NAME_REPEAT, "查重规则重复"));// ignoreI18n
        }
        validateCount(arg.getDuplicateSearch().getDescribeApiName(), arg.getDuplicateSearch().getType(), context.getTenantId());
        return createOrUpdateDuplicateRule(arg, context);
    }

    @ServiceMethod("update")
    public SaveDuplicatedSearch.Result update(SaveDuplicatedSearch.Arg arg, ServiceContext context) {
        return createOrUpdateDuplicateRule(arg, context);
    }

    @ServiceMethod("changeSupportMultiRule")
    public ChangeSupportMultiRule.Result changeSupportMultiRule(ChangeSupportMultiRule.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getObjectDescribeApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_EMPTY));
        }
        boolean useMultiRule = BooleanUtils.isTrue(arg.getUseMultiRule());
        duplicatedSearchService.changeSupportMultiRule(context.getUser(), arg.getObjectDescribeApiName(), useMultiRule);
        return new ChangeSupportMultiRule.Result();
    }

    private SaveDuplicatedSearch.Result createOrUpdateDuplicateRule(SaveDuplicatedSearch.Arg arg, ServiceContext context) {
        IDuplicatedSearch duplicateSearchInfo = arg.getDuplicateSearch();
        duplicateSearchInfo.setTenantId(context.getTenantId());
        duplicateSearchInfo.setOperatorId(context.getUser().getUserId());
        duplicateSearchInfo.setEffective(false);
        IDuplicatedSearch duplicatedSearch = duplicatedSearchService.createOrUpdateDuplicatedSearch(context.getUser(), duplicateSearchInfo, false);
        return SaveDuplicatedSearch.Result.builder().duplicateSearch(duplicatedSearch).build();
    }


    /**
     * 查询查重规则和对象下所有的fieldDescribe
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("basic_setting")
    @Transactional
    public GetBasicSetting.Result getBasicSetting(GetBasicSetting.Arg arg, ServiceContext context) {

        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(context.getTenantId(), arg.getDescribeApiName())) {
            return GetBasicSetting.Result.builder().build();
        }
        IObjectDescribe objectDescribe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        List<String> fieldApiNameList = objectDescribe.getFieldDescribes().stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
        Map<IDuplicatedSearch.Type, IDuplicatedSearch> duplicatedSearchMap = duplicatedSearchService.findDuplicatedSearchByApiName(context.getUser(), arg.getDescribeApiName(), true);
        IDuplicatedSearch newDuplicate = duplicatedSearchMap.get(IDuplicatedSearch.Type.NEW);
        IDuplicatedSearch toolDuplicate = duplicatedSearchMap.get(IDuplicatedSearch.Type.TOOL);
        if (Objects.isNull(newDuplicate)) {
            newDuplicate = duplicatedSearchService.presetDuplicateRule(arg.getDescribeApiName(), IDuplicatedSearch.Type.NEW, context.getUser());
        }
        if (Objects.isNull(toolDuplicate)) {
            toolDuplicate = duplicatedSearchService.presetDuplicateRule(arg.getDescribeApiName(), IDuplicatedSearch.Type.TOOL, context.getUser());
        }
        GetBasicSetting.Rules.RulesBuilder newRules = GetBasicSetting.Rules.builder();
        String id = newDuplicate.getId();
        int version = newDuplicate.getVersion();
        newDuplicate.getPendingRules().getShowFields().removeIf(x -> !fieldApiNameList.contains(x));
        newRules.enable(newDuplicate.isEnable())
                .invalidNotDuplicateSearch(newDuplicate.isInvalidNotDuplicateSearch())
                .type(IDuplicatedSearch.Type.NEW)
                .supportImport(newDuplicate.isSupportImport())
                .pendingRules(newDuplicate.getPendingRules());

        GetBasicSetting.Rules.RulesBuilder toolRules = GetBasicSetting.Rules.builder();
        id = StringUtils.isNotEmpty(id) ? toolDuplicate.getId() : id;
        version = version == 0 ? toolDuplicate.getVersion() : version;
        toolDuplicate.getPendingRules().getShowFields().removeIf(x -> !fieldApiNameList.contains(x));
        // 查重工具不能配置'重复时允许继续新建'和'转换时按查重逻辑判断'
        filterButton(toolDuplicate);
        toolRules.enable(toolDuplicate.isEnable())
                .invalidNotDuplicateSearch(toolDuplicate.isInvalidNotDuplicateSearch())
                .type(IDuplicatedSearch.Type.TOOL)
                .pendingRules(toolDuplicate.getPendingRules());

        return GetBasicSetting.Result.builder()
                .newDuplicate(newRules.build())
                .toolDuplicate(toolRules.build())
                .id(id)
                .describeApiName(arg.getDescribeApiName())
                .version(version)
                .build();
    }

    //编辑时校验查重个规则的接口
    @ServiceMethod("validate")
    public Validate.Result validate(Validate.Arg arg, ServiceContext context) {
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(context.getTenantId(), arg.getDescribeApiName())) {
            return Validate.Result.builder().build();
        }
        IDuplicatedSearch duplicatedSearchByApiNameAndType = duplicatedSearchService.findDuplicatedSearchByApiNameAndType(context.getTenantId(),
                arg.getDescribeApiName(), arg.getType(), true);
        if (Objects.nonNull(duplicatedSearchByApiNameAndType) && !duplicatedSearchByApiNameAndType.isEffective()) {
            log.info("duplicate validate duplicate:{}, arg:{}", JSON.toJSONString(duplicatedSearchByApiNameAndType), JSON.toJSONString(arg));
            throw new ValidateException(I18N.text(I18NKey.DUPLICATE_SEARCH_RULE_ACTIVE));
        }
        filterByDuplicateSearchRule(context, arg.getDescribeApiName(), duplicatedSearchByApiNameAndType);
        return Validate.Result.builder().duplicateSearch(duplicatedSearchByApiNameAndType).build();
    }

    private IDuplicatedSearch filterByDuplicateSearchRule(ServiceContext context, String describeApiName, IDuplicatedSearch duplicatedSearch) {
        // 过滤禁用字段
        filterShowFields(describeApiName, context, duplicatedSearch);
        // 查重工具不能配置'重复时允许继续新建'和'转换时按查重逻辑判断'
        filterButton(duplicatedSearch);
        return duplicatedSearch;
    }

    private void filterButton(IDuplicatedSearch duplicatedSearchRule) {
        if (Objects.isNull(duplicatedSearchRule) || Objects.isNull(duplicatedSearchRule.getPendingRules())) {
            return;
        }
        if (IDuplicatedSearch.Type.TOOL.equals(duplicatedSearchRule.getType())) {
            CollectionUtils.nullToEmpty(duplicatedSearchRule.getPendingRules().getRelatedDescribes()).forEach(x -> x.setAllowCreateWhenDuplicated(null));
            if (duplicatedSearchRule.getPendingRules().getSpecialConfig() != null) {
                duplicatedSearchRule.getPendingRules().getSpecialConfig().setTransforms(null);
            }
        }
    }

    private void filterShowFields(String describeApiName, ServiceContext context, IDuplicatedSearch duplicatedSearchByApiNameAndType) {
        if (Objects.isNull(duplicatedSearchByApiNameAndType) || Objects.isNull(duplicatedSearchByApiNameAndType.getPendingRules())) {
            return;
        }
        IObjectDescribe objectDescribe = describeLogicService.findObject(context.getTenantId(), describeApiName);
        List<String> showFields = duplicatedSearchByApiNameAndType.getPendingRules().getShowFields().stream()
                .filter(x -> Objects.nonNull(ObjectDescribeExt.of(objectDescribe).getFieldDescribe(x)) &&
                        ObjectDescribeExt.of(objectDescribe).getFieldDescribe(x).isActive())
                .collect(Collectors.toList());
        duplicatedSearchByApiNameAndType.getPendingRules().setShowFields(showFields);
    }

    /**
     * 更新启用状态
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("update_status")
    public UpdateStatus.Result updateStatus(UpdateStatus.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getDescribeApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        IDuplicatedSearch duplicatedSearchRule = null;
        //灰度后，该接口只做启用禁用操作
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(context.getTenantId(), arg.getDescribeApiName())) {
            if (StringUtils.isBlank(arg.getDuplicateSearchRuleApiName())) {
                throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
            }
            duplicatedSearchRule = duplicatedSearchService.findDuplicatedSearchByRuleApiName(arg.getDescribeApiName()
                    , arg.getDuplicateSearchRuleApiName(), context.getTenantId(), true);
            if (Objects.isNull(duplicatedSearchRule)) {
                throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
            }
            if (Objects.nonNull(arg.getEnable())) {
                duplicatedSearchRule.setEnable(arg.getEnable());
                duplicatedSearchService.enableOrDisable(context.getUser(), arg.getDescribeApiName(), arg.getDuplicateSearchRuleApiName(), arg.getType(), arg.getEnable());
            } else {
                if (CollectionUtils.notEmpty(arg.getTransforms())) {
                    IDuplicatedSearch.SpecialConfig specialConfig = new IDuplicatedSearch.SpecialConfig(arg.getTransforms());
                    duplicatedSearchRule.getPendingRules().setSpecialConfig(specialConfig);
                }
                List<IDuplicatedSearch.RelatedDescribe> relatedDescribes = duplicatedSearchRule.getPendingRules().getRelatedDescribes();
                if (CollectionUtils.notEmpty(relatedDescribes)) {
                    updateRelatedDuplicate(relatedDescribes, arg.getRelatedEnables(), arg.getRelatedRepeatAllowCreateEnables());
                    duplicatedSearchRule.getPendingRules().setRelatedDescribes(relatedDescribes);
                }
            }
        } else {
            duplicatedSearchRule = duplicatedSearchService.findDuplicatedSearchByApiNameAndType(context.getTenantId(),
                    arg.getDescribeApiName(), arg.getType(), true);
            // 如果对象下没有查重规则，默认创建一个
            if (Objects.isNull(duplicatedSearchRule)) {
                duplicatedSearchRule = duplicatedSearchService.presetDuplicateRule(arg.getDescribeApiName(), arg.getType(), context.getUser());
            }
            if (Objects.nonNull(arg.getEnable())) {
                duplicatedSearchRule.setEnable(arg.getEnable());
            }
            if (CollectionUtils.notEmpty(arg.getTransforms())) {
                IDuplicatedSearch.SpecialConfig specialConfig = new IDuplicatedSearch.SpecialConfig(arg.getTransforms());
                duplicatedSearchRule.getPendingRules().setSpecialConfig(specialConfig);
            }
            // 导入按照查重规则查重（新建查重支持）
            if (IDuplicatedSearch.Type.NEW == arg.getType() && Objects.nonNull(arg.getSupportImport())) {
                duplicatedSearchRule.setSupportImport(arg.getSupportImport());
            }
            List<IDuplicatedSearch.RelatedDescribe> relatedDescribes = duplicatedSearchRule.getPendingRules().getRelatedDescribes();
            duplicatedSearchRule.setVersion(Objects.isNull(arg.getVersion()) ? 0 : arg.getVersion());
            duplicatedSearchRule.setOperatorId(context.getUser().getUserId());
            updateRelatedDuplicate(relatedDescribes, arg.getRelatedEnables(), arg.getRelatedRepeatAllowCreateEnables());
            duplicatedSearchRule.getPendingRules().setRelatedDescribes(relatedDescribes);
            duplicatedSearchRule = duplicatedSearchService.createOrUpdateDuplicatedSearch(context.getUser(), duplicatedSearchRule, true);
        }
        return UpdateStatus.Result.builder().duplicateSearch(duplicatedSearchRule).build();
    }

    @ServiceMethod("getDuplicateRuleByConfig")
    public GetPreDuplicateRuleByConfig.Result getDuplicateRuleByConfig(GetPreDuplicateRuleByConfig.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getDescribeApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        validateCount(arg.getDescribeApiName(), arg.getType(), context.getTenantId());
        IDuplicatedSearch iDuplicatedSearch = duplicatedSearchService.presetDuplicateRule(arg.getDescribeApiName(), arg.getType(), context.getUser());
        log.info("prefabricated duplicateSearch rule:{}", iDuplicatedSearch);
        return GetPreDuplicateRuleByConfig.Result.builder().duplicatedSearch(iDuplicatedSearch).build();
    }

    private void validateCount(String objectApiName, IDuplicatedSearch.Type type, String tenantId) {
        Integer count = duplicatedSearchService.getDuplicateSearchRuleCount(objectApiName, type, tenantId);
        if (count >= AppFrameworkConfig.getMultiDuplicateCountLimit()) {
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.DUPLICATE_SEARCH_RULES_COUNT_OVER_LIMIT, "查重规则超出数量:{0}", AppFrameworkConfig.getMultiDuplicateCountLimit()));// ignoreI18n
        }
    }


    @ServiceMethod("isGrayByObjectAndTenant")
    public Boolean isGrayByObjectAndTenant(IsGrayByObjectAndTenant.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getDescribeApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        return AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(context.getTenantId(), arg.getDescribeApiName());
    }


    @ServiceMethod("list")
    public DuplicateSearchRuleList.Result duplicateSearchRuleList(DuplicateSearchRuleList.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getDescribeApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_EMPTY));
        }
        List<IDuplicatedSearch> duplicatedSearchList;
        // 分管小组的请求，没有灰度查重多规则直接返回空
        if (ObjectListConfig.MANGE_GROUP.equals(arg.getSourceInfo())
                && !AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(context.getTenantId(), arg.getDescribeApiName())) {
            duplicatedSearchList = Lists.newArrayList();
        } else {
            if (arg.isPriorityByOrder()) {
                // 页面上调用有序规则列表只有设计器规则排序会调   // 管理后台查重规则 排序
                duplicatedSearchList = duplicatedSearchService.getDuplicateSearchRuleList(arg.getDescribeApiName(),
                        arg.getType(), context.getTenantId(), false, DuplicateSearchOrderByType.ORDER_BY_SORT, true); // 实时
            } else if (IDuplicatedSearch.Type.TOOL.equals(arg.getType())) { // 前台显示查重工具列表回调用，需要remove掉禁用的规则 // 前台使用查重工具
                duplicatedSearchList = duplicatedSearchService.getDuplicateSearchRuleList(arg.getDescribeApiName(),
                        arg.getType(), context.getTenantId(), false, DuplicateSearchOrderByType.ORDER_BY_CREATE_TIME, false);  // 非实时
                duplicatedSearchList.removeIf(x -> !x.isEnable());
            } else {    // 后台管理列表
                duplicatedSearchList = duplicatedSearchService.getDuplicateSearchRuleList(arg.getDescribeApiName(),
                        arg.getType(), context.getTenantId(), false, DuplicateSearchOrderByType.ORDER_BY_CREATE_TIME, true);  // 实时
            }
        }
        List<DuplicateSearchRuleList.DuplicateSearchSimpleInfo> simpleDuplicateSearchRuleList = Lists.newArrayList();
        duplicatedSearchList.forEach(x -> {
            DuplicateSearchRuleList.DuplicateSearchSimpleInfo searchSimpleInfo = new DuplicateSearchRuleList.DuplicateSearchSimpleInfo();
            searchSimpleInfo.setDuplicateRuleName(x.getName());
            searchSimpleInfo.setDuplicateRuleApiName(x.getRuleApiName());
            searchSimpleInfo.setType(x.getType());
            searchSimpleInfo.setOrder(x.getSort());
            searchSimpleInfo.setDescribeApiName(x.getDescribeApiName());
            searchSimpleInfo.setEnable(x.isEnable());
            searchSimpleInfo.setMark(x.getMark());
            searchSimpleInfo.setLastModifyBy(x.getLastModifiedBy());
            searchSimpleInfo.setLastModifyTime(x.getLastModifiedTime());
            searchSimpleInfo.setCreatedBy(x.getCreatedBy());
            searchSimpleInfo.setCreateTime(x.getCreateTime());
            simpleDuplicateSearchRuleList.add(searchSimpleInfo);
        });

        boolean useMultiRule = duplicatedSearchService.useMultiRule(context.getUser(), arg.getDescribeApiName());
        ManageGroup manageGroup = duplicatedSearchService.queryDuplicateSearchManageGroup(context.getUser(), arg.getDescribeApiName(), arg.getSourceInfo());
        return DuplicateSearchRuleList.Result.builder()
                .list(simpleDuplicateSearchRuleList)
                .useMultiRule(useMultiRule)
                .manageGroup(ManageGroupDTO.of(manageGroup))
                .build();
    }

    /**
     * 更新联合查重
     *
     * @param relatedDescribes              联合查重
     * @param relatedDuplicateEnableList    联合查重启用状态
     * @param allowCreateWhenDuplicatedList 重复时是否可以继续保存
     * @return
     */
    private void updateRelatedDuplicate(List<IDuplicatedSearch.RelatedDescribe> relatedDescribes,
                                        List<UpdateStatus.Entity> relatedDuplicateEnableList,
                                        List<UpdateStatus.Entity> allowCreateWhenDuplicatedList) {

        if (CollectionUtils.empty(relatedDuplicateEnableList) && CollectionUtils.empty(allowCreateWhenDuplicatedList)) {
            return;
        }
        if (CollectionUtils.empty(relatedDescribes)) {
            return;
        }
        Map<String, IDuplicatedSearch.RelatedDescribe> relatedDescribeMap = relatedDescribes.stream()
                .collect(Collectors.toMap(IDuplicatedSearch.RelatedDescribe::getDescribeApiName, x -> x));

        dealRelatedDuplicateEnableList(relatedDuplicateEnableList, relatedDescribeMap);
        dealAllowCreateWhenDuplicateList(allowCreateWhenDuplicatedList, relatedDescribeMap);
    }

    private void dealRelatedDuplicateEnableList(List<UpdateStatus.Entity> relatedDuplicateEnableList,
                                                Map<String, IDuplicatedSearch.RelatedDescribe> relatedDescribeMap) {
        if (CollectionUtils.empty(relatedDuplicateEnableList)) {
            return;
        }
        relatedDuplicateEnableList.forEach(entity -> {
            IDuplicatedSearch.RelatedDescribe relatedDescribe = relatedDescribeMap.get(entity.getRelatedApiName());
            if (Objects.isNull(relatedDescribe)) {
                return;
            }
            relatedDescribe.setEnable(entity.getValue());
        });
    }

    private void dealAllowCreateWhenDuplicateList(List<UpdateStatus.Entity> allowCreateWhenDuplicatedList,
                                                  Map<String, IDuplicatedSearch.RelatedDescribe> relatedDescribeMap) {
        if (CollectionUtils.empty(allowCreateWhenDuplicatedList)) {
            return;
        }
        allowCreateWhenDuplicatedList.forEach(entity -> {
            IDuplicatedSearch.RelatedDescribe relatedDescribe = relatedDescribeMap.get(entity.getRelatedApiName());
            if (Objects.isNull(relatedDescribe)) {
                return;
            }
            relatedDescribe.setAllowCreateWhenDuplicated(entity.getValue());
        });
    }


    /**
     * 依据查重类型获取所有对象下启用中的查重字段描述
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("get_search_tool_object")
    public GetSearchToolObject.Result getSearchToolObject(GetSearchToolObject.Arg arg, ServiceContext context) {
        List<String> objectApiNames = duplicatedSearchService.findAllObjectApiNameByType(context.getUser(), false, IDuplicatedSearch.Type.TOOL);
        //去除重复的对象
        objectApiNames = objectApiNames.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.empty(objectApiNames)) {
            return GetSearchToolObject.Result.builder().build();
        }
        List<String> haveFunPrivilegeApiNames = getHaveFunPrivilegeObjectApiNames(context.getUser(), objectApiNames, ObjectAction.VIEW_LIST.getActionCode());

        List<IObjectDescribe> describeListWithoutFields = describeLogicService.findDescribeListWithoutFields(context.getTenantId(), haveFunPrivilegeApiNames);
        //按objectApiNames排序
        Map<String, IObjectDescribe> collect = describeListWithoutFields.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, x -> x));
        describeListWithoutFields = objectApiNames.stream().filter(x -> collect.keySet().contains(x)).map(collect::get).collect(Collectors.toList());
        // 构造objectInfos
        List<GetSearchToolObject.ObjectInfo> objectInfos = Lists.newArrayList();
        describeListWithoutFields.forEach(objectDescribe -> {
            GetSearchToolObject.ObjectInfo objectInfo = GetSearchToolObject.ObjectInfo.builder()
                    .describeApiName(objectDescribe.getApiName())
                    .describeLabel(objectDescribe.getDisplayName())
                    .build();
            objectInfos.add(objectInfo);
        });

        return GetSearchToolObject.Result.builder().objectDescribes(objectInfos).build();

    }

    private List<String> getHaveFunPrivilegeObjectApiNames(User user, List<String> objectApiNames, String actionCode) {
        // 功能权限过滤
        Map<String, Map<String, Boolean>> funPrivilegeResult = functionPrivilegeService.batchFunPrivilegeCheck(user, objectApiNames,
                Lists.newArrayList(actionCode));
        return funPrivilegeResult.keySet().stream()
                .filter(x -> funPrivilegeResult.get(x).get(actionCode))
                .collect(Collectors.toList());
    }

    /**
     * 根据对象describeApiName返回查重工具中规则字段描述
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("get_search_tool_fields")
    public GetSearchToolFields.Result getSearchToolFields(GetSearchToolFields.Arg arg, ServiceContext context) {

        IDuplicatedSearch duplicateSearch;
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(context.getTenantId(), arg.getDescribeApiName())) {
            if (StringUtils.isBlank(arg.getDuplicateRuleApiName())) {
                List<IDuplicatedSearch> duplicatedSearchList = duplicatedSearchService.getEnableDuplicateSearchRuleList(arg.getDescribeApiName(),
                        IDuplicatedSearch.Type.TOOL, context.getTenantId(), false, DuplicateSearchOrderByType.ORDER_BY_SORT);
                duplicateSearch = duplicatedSearchList.stream().findFirst().orElse(null);
            } else {
                duplicateSearch = duplicatedSearchService.findDuplicatedSearchByRuleApiName(arg.getDescribeApiName(),
                        arg.getDuplicateRuleApiName(), context.getTenantId(), false);
            }
        } else {
            duplicateSearch = duplicatedSearchService.findDuplicatedSearchByApiNameAndType(context.getTenantId(),
                    arg.getDescribeApiName(), IDuplicatedSearch.Type.TOOL, false);
        }
        IObjectDescribe objectDescribe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());

        List<String> fieldNames = Lists.newArrayList();
        if (Objects.nonNull(duplicateSearch) && Objects.nonNull(duplicateSearch.getUseableRules())) {
            duplicateSearch.getUseableRules().getRules()
                    .forEach(rule -> rule.getConditions()
                            .forEach(condition -> fieldNames.add(condition.getFieldName())));
        }
        ObjectDescribeExt.of(objectDescribe)
                .getFieldByApiNames(fieldNames)
                .stream()
                .filter(x -> IFieldType.OBJECT_REFERENCE.equals(x.getType()))
                .forEach(x -> FieldDescribeExt.of(x).toMap().put("type", IFieldType.TEXT));
        List<Map> fieldDescribes = ObjectDescribeExt.of(objectDescribe).getFieldByApiNames(fieldNames).stream()
                // 查重过滤人员类型字段
                .filter(it -> !(FieldDescribeExt.of(it).isEmployee() && context.getUser().isOutUser()))
                .map(x -> (FieldDescribeExt.of(x).toMap()))
                .collect(Collectors.toList());
        return GetSearchToolFields.Result.builder().fieldDescribes(fieldDescribes).build();
    }

    /**
     * 获取查重结果列表表头 WEB端
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("get_result_header")
    public GetResultHeader.Result getResultHeader(GetResultHeader.Arg arg, ServiceContext context) {
        log.info("duplicateSearchGetResultHeader,arg:{},context:{}", arg.toString(), context.toString());
        IObjectDescribe objectDescribe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        IDuplicatedSearch duplicatedSearchByApiNameAndType = duplicatedSearchService.findDuplicatedSearchByApiNameAndType(context.getTenantId(), arg.getDescribeApiName(), arg.getType(), false);
        DuplicateSearchQuery duplicateSearchQuery = DuplicateSearchQuery.builder()
                .duplicatedSearchRule(duplicatedSearchByApiNameAndType)
                .build();
        List<String> showFields = duplicateSearchQuery.getShowFieldList(context.getUser(), functionPrivilegeService);
        return GetResultHeader.Result.builder().objectDescribe(ObjectDescribeDocument.of(objectDescribe)).includeFields(showFields).build();
    }

    /**
     * 查重结果
     *
     * @return
     */
    @ServiceMethod("get_result")
    public GetResult.Result getResult(GetResult.Arg arg, ServiceContext context, UnaryOperator<List<DuplicateSearchResult.DuplicateData>> handleDuplicateSearchResult) {
        log.info("duplicateSearchGetResult,arg:{},context:{}", arg.toString(), context.toString());
        //放的是本对象的apiName和联合查重对象的apiName
        Set<String> objectApiNames = Sets.newHashSet(arg.getDescribeApiName());
        String describeApiName = arg.getDescribeApiName();
        if (StringUtils.isNotBlank(arg.getRelatedApiName())) {
            objectApiNames.add(arg.getRelatedApiName());
            describeApiName = arg.getRelatedApiName();
        }
        IObjectData objectData = ObjectDataExt.of(arg.getObjectData()).getObjectData();

        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(context.getTenantId(), arg.getDescribeApiName())) { // 新的查重规则是列表，翻译只支持该类型
            List<IDuplicatedSearch> duplicatedSearchList = Lists.newArrayList();
            // 新建时用来确定上次调用查重接口查到重复数据的规则apiName，工具时来确定本次时那个规则做查重，用来拼redis的key值，查询数据
            String ruleApiNameIntercepted = arg.getRuleApiNameIntercepted();

            // 查询对象下的所有查重规则
            Map<String, List<IDuplicatedSearch>> duplicateSearchListMap =
                    duplicatedSearchService.findAllDuplicateSearchByApiNameAndType(context.getTenantId(), objectApiNames,
                            arg.getType(), false, DuplicateSearchOrderByType.ORDER_BY_SORT, false);

            if (CollectionUtils.empty(duplicateSearchListMap)) {
                return new GetResult.Result();
            }

            List<IDuplicatedSearch> duplicateSearchRuleList = duplicateSearchListMap.get(arg.getDescribeApiName());

            if (CollectionUtils.empty(duplicateSearchRuleList)) {
                return new GetResult.Result();
            }

            //如果是查重工具 查重 则兼容其没传ruleApiName的情况(默认取第一个)
            if (IDuplicatedSearch.Type.TOOL.equals(arg.getType())) {
                IDuplicatedSearch duplicatedSearch;
                if (StringUtils.isBlank(arg.getDuplicateRuleApiName())) {
                    duplicatedSearch = duplicateSearchRuleList.stream().findFirst().orElse(null);
                } else {
                    duplicatedSearch = duplicateSearchRuleList.stream()
                            .filter(x -> arg.getDuplicateRuleApiName().equals(x.getRuleApiName()))
                            .findFirst().orElse(null);
                }
                if (Objects.nonNull(duplicatedSearch)) {
                    duplicatedSearchList.add(duplicatedSearch);
                }
                if (StringUtils.isBlank(ruleApiNameIntercepted)) {
                    ruleApiNameIntercepted = arg.getDuplicateRuleApiName();
                }
            } else {
                //根据ruleApiName来截取查重规则列表（不是第一次查重，并且上次查重有查重规则，从该查重规则之后开始查重 ）
                if (StringUtils.isNotBlank(arg.getAssignDuplicateSearchApiName())) {
                    duplicateSearchRuleList.removeIf(x -> !arg.getAssignDuplicateSearchApiName().equals(x.getRuleApiName()));
                } else {
                    duplicateSearchRuleList = DuplicatedSearchExt.subListDuplicateRuleInfoByRuleApiName(arg.getDuplicateRuleApiName(), duplicateSearchRuleList);
                }
                duplicatedSearchList.addAll(duplicateSearchRuleList);
            }

            IObjectDescribe objectDescribe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());

            duplicatedSearchList = DuplicatedSearchExt.filterDuplicatedSearch(duplicatedSearchList, objectData, objectDescribe, departmentService);

            if (CollectionUtils.empty(duplicatedSearchList)) {
                return new GetResult.Result();
            }
            if (!duplicatedSearchService.useMultiRule(context.getUser(), arg.getDescribeApiName())
                    && CollectionUtils.notEmpty(duplicatedSearchList) && IDuplicatedSearch.Type.NEW.equals(arg.getType())) {
                duplicatedSearchList = duplicatedSearchList.subList(0, 1);
            }

            List<DuplicateSearchQuery> duplicateSearchQueryList = Lists.newArrayList();
            //取本对象和联合查重的apiName
            Set<String> apiNames = Sets.newHashSet();
            for (IDuplicatedSearch duplicatedSearch : duplicatedSearchList) {
                apiNames.addAll(getApiNames(duplicatedSearch));
            }
            Map<String, IObjectDescribe> objectDescribeMap = describeLogicService.findObjects(context.getTenantId(), apiNames);

            for (IDuplicatedSearch iDuplicatedSearch : duplicatedSearchList) {
                duplicateSearchQueryList.add(DuplicateSearchQuery.builder()
                        .duplicatedSearchRule(iDuplicatedSearch)
                        .duplicatedSearchMap(duplicateSearchListMap)
                        .relatedApiName(arg.getRelatedApiName())
                        .isNeedDuplicate(arg.getIsNeedDuplicate())
                        .pageNumber(arg.getPageNumber())
                        .pageSize(arg.getPageSize())
                        .build());
            }
            return doSearch(arg.getDescribeApiName(), describeApiName, objectDescribeMap, arg.getIncludeObjectDescribes(), arg.getType(),
                    context.getUser(), duplicateSearchQueryList, objectData, Lists.newArrayList(apiNames), handleDuplicateSearchResult,
                    ruleApiNameIntercepted, arg.getCacheLevel(), BooleanUtils.isTrue(arg.getReturnDuplicatedRule()));
        } else {
            //批量查询查重规则
            Map<String, List<IDuplicatedSearch>> duplicatedSearchMap = duplicatedSearchService.findDuplicatedSearchListByApiNamesAndType(context.getTenantId(),
                    Lists.newArrayList(objectApiNames), arg.getType(), false);
            if (CollectionUtils.empty(duplicatedSearchMap) || CollectionUtils.empty(duplicatedSearchMap.get(arg.getDescribeApiName()))) {
                return new GetResult.Result();
            }
            //取本对象查重规则
            List<IDuplicatedSearch> duplicatedSearchRuleList = duplicatedSearchMap.get(arg.getDescribeApiName());
            duplicatedSearchRuleList.removeIf(x -> !DuplicatedSearchExt.isEnableDuplicate(x));
            if (CollectionUtils.empty(duplicatedSearchRuleList)) {
                return new GetResult.Result();
            }
            //取本对象和联合查重的apiName
            Set<String> apiNames = Sets.newHashSet();
            duplicatedSearchRuleList.forEach(x -> apiNames.addAll(getApiNames(x)));
            Map<String, IObjectDescribe> objectDescribeMap = describeLogicService.findObjects(context.getTenantId(), apiNames);

            DuplicateSearchQuery duplicateSearchQuery = DuplicateSearchQuery.builder()
                    .duplicatedSearchRule(duplicatedSearchRuleList.get(0))
                    .duplicatedSearchMap(duplicatedSearchMap)
                    .relatedApiName(arg.getRelatedApiName())
                    .isNeedDuplicate(arg.getIsNeedDuplicate())
                    .pageNumber(arg.getPageNumber())
                    .pageSize(arg.getPageSize())
                    .build();

            return doSearch(arg.getDescribeApiName(), describeApiName, objectDescribeMap, arg.getIncludeObjectDescribes(),
                    arg.getType(), context.getUser(), Lists.newArrayList(duplicateSearchQuery), objectData, Lists.newArrayList(apiNames), handleDuplicateSearchResult,
                    "", arg.getCacheLevel(), BooleanUtils.isTrue(arg.getReturnDuplicatedRule()));
        }
    }

    private GetResult.Result doSearch(String apiName, String relatedApiName, Map<String, IObjectDescribe> objectDescribeMap,
                                      Boolean includeObjectDescribes, IDuplicatedSearch.Type type, User user,
                                      List<DuplicateSearchQuery> queryList, IObjectData objectData, List<String> apiNames,
                                      UnaryOperator<List<DuplicateSearchResult.DuplicateData>> handleDuplicateSearchResult,
                                      String ruleApiNameIntercepted, String cacheLevel, boolean returnDuplicateSearchRule) {
        if (CollectionUtils.empty(queryList)) {
            return GetResult.Result.builder().isKeepSave(true).build();
        }
        Map<String, List<String>> showFieldMap = getShowFields(user, queryList, objectDescribeMap, apiNames);
        checkShowField(showFieldMap, queryList);
        IObjectDescribe relatedObjectDescribe = objectDescribeMap.get(relatedApiName);
        if (Objects.isNull(relatedObjectDescribe)) {
            log.warn("duplicatedSearch param error:tenantId:{},apiName:{},relatedApiName:{},ruleList:{} objectDescribeMapKey:{}",
                    user.getTenantId(), apiName, relatedApiName, queryList, objectDescribeMap.keySet());
            throw new ValidateException(I18N.text(I18NKey.PARAM_EMPTY));
        }
        List<IDuplicatedSearch> duplicatedSearchRuleList = queryList.stream().map(DuplicateSearchQuery::getDuplicatedSearchRule).collect(Collectors.toList());

        String uniqueCacheId = "";
        if (StringUtils.isNotBlank(cacheLevel) && CACHE_LEVEL_UNIQUE_REQUEST.equals(cacheLevel)) {
            uniqueCacheId = IdGenerator.get();
        }

        // 查询查重的结果，并写入redis
        Map<String, DataIdsTotal> dataIdsTotalMap =
                getDuplicateDataIdsTotal(duplicatedSearchRuleList, queryList.get(0).isNeedDuplicate(), user, objectData,
                        objectDescribeMap.get(apiName), apiNames, type, handleDuplicateSearchResult, ruleApiNameIntercepted, uniqueCacheId);

        Map<String, DuplicateSearchQuery> ruleApiNameDuplicateSearchQuery = queryList.stream()
                .collect(Collectors.toMap(x -> x.getDuplicatedSearchRule().getRuleApiName(), it -> it, (x1, x2) -> x1));

        //relatedObjectDescribe是每次要被查重的对象，如果是本对象查重，则是本对象描述，要是关联对象查重，则是关联对象的描述
        DataIdsTotal dataIdsTotal = dataIdsTotalMap.get(relatedObjectDescribe.getApiName());

        DuplicateSearchQuery duplicateSearchQuery = null;
        if (Objects.isNull(dataIdsTotal)) {
            if (IDuplicatedSearch.Type.NEW.equals(type) && StringUtils.isNotBlank(ruleApiNameIntercepted)) {
                duplicateSearchQuery = ruleApiNameDuplicateSearchQuery.get(ruleApiNameIntercepted);
            }
        } else {
            //如果根据ruleApiName没拿到规则，则代表是为灰度过的企业，没有ruleApiName，并且规则只有一个，则取第一个规则
            duplicateSearchQuery = ruleApiNameDuplicateSearchQuery.get(dataIdsTotal.getRuleApiName());
        }
        if (Objects.isNull(duplicateSearchQuery)) {
            duplicateSearchQuery = queryList.get(0);
        }

        List<GetResult.RelatedSearchInfo> relatedSearchInfos = fillRelatedSearchInfos(apiName, objectDescribeMap, includeObjectDescribes, dataIdsTotalMap);

        long total = Objects.isNull(dataIdsTotal) ? 0 : dataIdsTotal.getTotal();

        String token = getToken(user, apiName, duplicateSearchQuery.getSearchType(), relatedObjectDescribe.getApiName(),
                DuplicatedSearchExt.of(duplicateSearchQuery.getDuplicatedSearchRule()).getRealRuleApiName(), uniqueCacheId);
        long showNum = duplicateSearchQuery.getShowNum();
        if (showNum != -1) {
            total = Math.min(total, showNum);
        }
        GetResult.DuplicateRuleInfo duplicateRuleInfo = null;
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(user.getTenantId(), apiName)) {
            IDuplicatedSearch duplicatedSearchRule = duplicateSearchQuery.getDuplicatedSearchRule();
            if (total > 0 || CollectionUtils.notEmpty(relatedSearchInfos) || returnDuplicateSearchRule) {
                duplicateRuleInfo = GetResult.DuplicateRuleInfo.builder()
                        .duplicateRuleName(duplicatedSearchRule.getName())
                        .describeApiName(duplicatedSearchRule.getDescribeApiName())
                        .enable(duplicatedSearchRule.isEnable())
                        .type(duplicatedSearchRule.getType())
                        .duplicateRuleApiName(duplicatedSearchRule.getRuleApiName())
                        .duplicatedSearch(returnDuplicateSearchRule ? duplicatedSearchRule : null)
                        .build();
            }
        }
        // 获取分页id
        List<String> ids = getDuplicateDataIdsWithPage(token, total, duplicateSearchQuery.getPageNumber(), duplicateSearchQuery.getPageSize());
        if (log.isDebugEnabled()) {
            log.debug("searchDuplicateData ids,tenantId:{},userId:{},object:{},token:{}, result:{}", user.getTenantId(), user.getUserId(), objectData.getDescribeApiName(), token, JSON.toJSONString(ids));
        }
        // 获取page信息
        GetResult.Page page = getPage(duplicateSearchQuery.getPageSize(), duplicateSearchQuery.getPageNumber(), total);
        List<IObjectData> chaosDataList = getObjectData(user, relatedObjectDescribe, ids);
        Map<String, IObjectData> dataMap = chaosDataList.stream().collect(Collectors.toMap(DBRecord::getId, x -> x));
        List<IObjectData> orderDataList = ids.stream()
                .filter(x -> Objects.nonNull(dataMap.get(x)))
                .map(dataMap::get)
                .collect(Collectors.toList());

        //将数据进行同步化处理，防止多线程修改数据导致字段丢失
        List<IObjectData> syncDataList = ObjectDataExt.synchronize(orderDataList);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            //处理引用字段
            quoteValueService.fillQuoteFieldValue(user, syncDataList, relatedObjectDescribe, null, false);
            // 补充掩码需要在补充引用字段之后执行,异步执行可能会导致引用字段没有掩码的情况
            metaDataMiscService.fillMaskFieldValue(user, syncDataList, relatedObjectDescribe, true);
        });
        parallelTask.submit(() -> {
            //添加lookup字段的主属性__r
            metaDataMiscService.fillObjectDataWithRefObject(relatedObjectDescribe, syncDataList, user, null);
        });
        parallelTask.submit(() -> {
            metaDataMiscService.fillUserInfo(relatedObjectDescribe, syncDataList, user);
            metaDataMiscService.fillDepartmentInfo(relatedObjectDescribe, syncDataList, user);
        });
        parallelTask.submit(() -> {
            // 公共对对象可见范围的__l
            metaDataMiscService.fillDataVisibilityRange(user, relatedObjectDescribe, syncDataList);
        });
        parallelTask.submit(() -> metaDataMiscService.fillCountryAreaLabel(relatedObjectDescribe, syncDataList, user));
        try {
            parallelTask.await(5000, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("process duplicate search data time out,tenantId:{},relatedApiName:{}", user.getTenantId(), relatedApiName, e);
        }
        List<String> showFields = getShowFields(duplicateSearchQuery, showFieldMap);
        GetResult.ButtonInfo buttonInfo = fillButtonInfo(user, orderDataList,
                showFields, relatedObjectDescribe, ids);
        // 判断是否有新建权限
        // 只有查重工具显示新建按钮，新建查重不需要显示
        boolean privilegeCheck = IDuplicatedSearch.Type.TOOL.equals(duplicateSearchQuery.getDuplicatedSearchRule().getType())
                && !ObjectDescribeExt.of(objectDescribeMap.get(apiName)).isSlaveObjectCreateWithMasterAndHiddenDetailButton()
                && functionPrivilegeService.funPrivilegeCheck(user, relatedObjectDescribe.getApiName(), ObjectAction.CREATE.getActionCode());

        // 获取企业下管理员ID
        List<String> adminUsers = getAdmins(user);

        // 判断是否可以继续保存
        boolean keepSave = duplicateSearchQuery.isKeepSave(dataIdsTotalMap.get(apiName));

        IDuplicatedSearch.Policy matchType = duplicateSearchQuery.matchType(dataIdsTotalMap.get(apiName));
        if (log.isDebugEnabled()) {
            log.debug("searchDuplicatedData result ,tenantId:{},userId:{},object:{}, result:{}", user.getTenantId(), user.getUserId(), objectData.getDescribeApiName(), orderDataList);
        }
        return GetResult.Result.builder()
                .objectDescribe(ObjectDescribeDocument.of(relatedObjectDescribe))
                .relatedSearchInfos(relatedSearchInfos)
                .describeExt(ObjectDescribeDocument.of(describeLogicService.findDescribeExtraByRenderType(user, relatedObjectDescribe,
                        Collections.emptyList(), DescribeExpansionRender.RenderType.Detail, true)))
                .dataList(ObjectDataDocument.ofList(orderDataList))
                .includeFields(showFields)
                .page(page)
                .buttonInfo(buttonInfo)
                .createPermission(privilegeCheck)
                .adminIds(adminUsers)
                .isKeepSave(keepSave)
                .matchType(matchType)
                .duplicateRuleInfo(duplicateRuleInfo)
                .duplicatedSearch(duplicateSearchQuery.getDuplicatedSearchRule())
                .build();
    }

    private List<String> getShowFields(DuplicateSearchQuery duplicateSearchQuery, Map<String, List<String>> showFieldMap) {
        String showFieldKey = getShowFieldKey(duplicateSearchQuery);
        return showFieldMap.get(showFieldKey);
    }

    private String getShowFieldKey(DuplicateSearchQuery duplicateSearchQuery) {
        IDuplicatedSearch iDuplicatedSearch = getDuplicateSearch(duplicateSearchQuery);
        if (Objects.isNull(iDuplicatedSearch)) {
            return duplicateSearchQuery.getRelatedApiName();
        }
        return iDuplicatedSearch.getDescribeApiName() + DuplicatedSearchExt.of(iDuplicatedSearch).getRealRuleApiName();
    }

    /**
     * 过滤无权限的字段，下游请求要过滤掉显示字段中的人员字段
     *
     * @param user
     * @param duplicateSearchQueryList
     * @param objectDescribeMap
     * @param apiNames
     * @return
     */
    private Map<String, List<String>> getShowFields(User user, List<DuplicateSearchQuery> duplicateSearchQueryList, Map<String, IObjectDescribe> objectDescribeMap, List<String> apiNames) {
        //获取每个规则下当前有权限的显示字段
        Map<String, List<String>> showFieldMap = filterUnauthorizedFields(user, duplicateSearchQueryList, apiNames);
        for (DuplicateSearchQuery query : duplicateSearchQueryList) {
            List<String> showFieldList = getShowFields(query, showFieldMap);
            if (!user.isOutUser() || CollectionUtils.empty(showFieldList)) {
                continue;
            }
            String relatedApiName = query.getRelatedApiName();
            IObjectDescribe describe;
            if (!Strings.isNullOrEmpty(relatedApiName)) {
                describe = objectDescribeMap.get(relatedApiName);
            } else {
                String describeApiName = query.getDuplicatedSearchRule().getDescribeApiName();
                describe = objectDescribeMap.get(describeApiName);
            }
            if (Objects.isNull(describe)) {
                log.warn("duplicated getShowFields, describe not exit, query:{}, user:{}", JSON.toJSONString(query), user);
                continue;
            }
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
            showFieldList
                    // 下游过滤人员字段
                    .removeIf(it -> describeExt.getFieldDescribeSilently(it)
                            .filter(field -> FieldDescribeExt.of(field).isEmployee() || FieldDescribeExt.of(field).isEmployeeManyField())
                            .isPresent());
        }
        return showFieldMap;

    }

    private Map<String, List<String>> filterUnauthorizedFields(User user, List<DuplicateSearchQuery> duplicateSearchQueryList, List<String> apiNames) {
        Map<String, Set<String>> unauthorizedFields = functionPrivilegeService.getUnauthorizedFields(user, apiNames);
        Map<String, List<String>> showFieldMap = Maps.newHashMap();
        for (DuplicateSearchQuery duplicateSearchQuery : duplicateSearchQueryList) {
            IDuplicatedSearch duplicatedSearch = getDuplicateSearch(duplicateSearchQuery);
            List<String> showFields = Lists.newArrayList();
            String showFieldKey = getShowFieldKey(duplicateSearchQuery);
            if (Objects.isNull(duplicatedSearch) || Objects.isNull(duplicatedSearch.getUseableRules()) || CollectionUtils.empty(duplicatedSearch.getUseableRules().getShowFields())) {
                showFields.add(IObjectData.NAME);
                showFieldMap.put(showFieldKey, showFields);
                continue;
            }
            showFields.addAll(duplicatedSearch.getUseableRules().getShowFields());
            Set<String> unauthorizedFieldList = unauthorizedFields.getOrDefault(duplicatedSearch.getDescribeApiName(), Sets.newHashSet());
            showFields.removeIf(unauthorizedFieldList::contains);

            showFieldMap.put(showFieldKey, showFields);
        }
        return showFieldMap;
    }

    private List<GetResult.RelatedSearchInfo> fillRelatedSearchInfos(String apiName, Map<String, IObjectDescribe> objectDescribeMap, Boolean includeObjectDescribes, Map<String, DataIdsTotal> dataIdsTotalMap) {
        if (!includeObjectDescribes) {
            return Lists.newArrayList();
        }
        List<String> relatedApiNames = Lists.newArrayList(dataIdsTotalMap.keySet());
        relatedApiNames.removeIf(x -> StringUtils.equals(x, apiName));

        return objectDescribeMap.values().stream()
                .filter(x -> relatedApiNames.contains(x.getApiName()))
                .map(x -> GetResult.RelatedSearchInfo.builder()
                        .objectDescribe(ObjectDescribeDocument.of(x))
                        .total(dataIdsTotalMap.get(x.getApiName()).getTotal())
                        .matchType(IDuplicatedSearch.Policy.FUZZY)
                        .build())
                .collect(Collectors.toList());
    }

    public IObjectData getSimpleSearchObjectData(String keyword, List<String> conditionFields, IObjectDescribe objectDescribe) {
        List<String> fields = ObjectDescribeExt.of(objectDescribe).getFieldByApiNames(conditionFields)
                .stream()
                .filter(a -> NumberUtils.isNumber(keyword) ? DuplicateSearchQuery.SIMPLE_SEARCH_NUMBER_FIELDS.contains(a.getType()) :
                        DuplicateSearchQuery.SIMPLE_SEARCH_NON_NUMBER_FIELDS.contains(a.getType()))
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toList());
        Map<String, Object> data = Maps.newHashMap();
        fields.forEach(a -> data.put(a, keyword));
        return ObjectDataExt.of(data).getObjectData();
    }

    @ServiceMethod("simple_search")
    public GetResult.Result simpleSearch(SimpleSearch.Arg arg, ServiceContext context) {
        log.info("simpleSearch,arg:{},context:{}", arg.toString(), context.toString());
        String describeApiName = arg.getDescribeApiName();
        List<String> objectApiNames = Lists.newArrayList(describeApiName);
        if (StringUtils.isNotBlank(arg.getRelatedApiName())) {
            objectApiNames.add(arg.getRelatedApiName());
        }
        Map<String, List<IDuplicatedSearch>> duplicatedSearchMap;
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(context.getTenantId(), arg.getDescribeApiName())) {
            duplicatedSearchMap = duplicatedSearchService.findAllDuplicateSearchByApiNameAndType(context.getTenantId(), Sets.newHashSet(objectApiNames), IDuplicatedSearch.Type.TOOL, false, DuplicateSearchOrderByType.ORDER_BY_SORT);
        } else {
            duplicatedSearchMap = duplicatedSearchService.findDuplicatedSearchListByApiNamesAndType(context.getTenantId(),
                    objectApiNames, IDuplicatedSearch.Type.TOOL, false);
        }
        if (CollectionUtils.empty(duplicatedSearchMap) || CollectionUtils.empty(duplicatedSearchMap.get(describeApiName))) {
            return new GetResult.Result();
        }
        List<IDuplicatedSearch> duplicatedSearchList = duplicatedSearchMap.get(describeApiName);
        IDuplicatedSearch iDuplicatedSearch = null;
        if (StringUtils.isNotBlank(arg.getDuplicateRuleApiName())) {
            iDuplicatedSearch = duplicatedSearchList.stream().filter(x -> arg.getDuplicateRuleApiName().equals(x.getRuleApiName())).findFirst().orElse(duplicatedSearchList.get(0));
        }
        if (Objects.isNull(iDuplicatedSearch)) {
            iDuplicatedSearch = duplicatedSearchList.get(0);
        }

        DuplicateSearchQuery duplicateSearchQuery = DuplicateSearchQuery.builder()
                .duplicatedSearchRule(iDuplicatedSearch)
                .duplicatedSearchMap(duplicatedSearchMap)
                .relatedApiName(arg.getRelatedApiName())
                .isNeedDuplicate(arg.getIsNeedDuplicate())
                .pageNumber(arg.getPageNumber())
                .pageSize(arg.getPageSize())
                .build();

        List<String> apiNames = getApiNames(iDuplicatedSearch);
        List<String> conditionFields = getConditionFields(iDuplicatedSearch);
        describeApiName = StringUtils.isBlank(arg.getRelatedApiName()) ? describeApiName : arg.getRelatedApiName();
        Map<String, IObjectDescribe> objectDescribeMap = describeLogicService.findObjects(context.getTenantId(), apiNames);

        IObjectData objectData = getSimpleSearchObjectData(arg.getKeyword(), conditionFields, objectDescribeMap.get(arg.getDescribeApiName()));
        return doSearch(arg.getDescribeApiName(), describeApiName, objectDescribeMap, arg.getIncludeObjectDescribes(), IDuplicatedSearch.Type.TOOL, context.getUser(),
                Lists.newArrayList(duplicateSearchQuery), objectData, apiNames, it -> it, "", "", false);

    }

    @ServiceMethod("set_rule_priority")
    public SetDuplicateRulePriority.Result setRulePriority(SetDuplicateRulePriority.Arg arg, ServiceContext context) {
        List<String> duplicateRuleApiNames = arg.getDuplicateRuleApiNames();
        String describeApiName = arg.getDescribeApiName();
        if (StringUtils.isBlank(describeApiName) || CollectionUtils.empty(duplicateRuleApiNames)) {
            return SetDuplicateRulePriority.Result.builder().result(true).build();
        }
        //获取参数中包含查重规则ApiName的所有查重规则
        List<IDuplicatedSearch> ruleInfoList = duplicatedSearchService
                .findDuplicatedSearchByRuleApiNames(describeApiName, duplicateRuleApiNames, context.getTenantId(), true);

        if (CollectionUtils.empty(ruleInfoList)) {
            log.warn("ruleInfoList not exists! describeApiName:{},duplicateRuleApiNames:{},tenantId:{}",
                    describeApiName, duplicateRuleApiNames, context.getTenantId());
            return SetDuplicateRulePriority.Result.builder().result(false).build();
        }

        //排序
        List<IDuplicatedSearch> duplicatedSearchList = CollectionUtils.sortByGivenOrder(ruleInfoList, duplicateRuleApiNames, IDuplicatedSearch::getRuleApiName);
        //根据参数中查重规则ApiName的顺序，给查重规则sort属性赋值
        AtomicInteger order = new AtomicInteger(1);
        HashMap<Integer, String> sortMap = Maps.newHashMap();
        duplicatedSearchList.forEach(x -> sortMap.put(order.getAndIncrement(), x.getId()));

        duplicatedSearchService.dealPriority(describeApiName, sortMap, context.getTenantId());

        return SetDuplicateRulePriority.Result.builder().result(true).build();
    }

    @ServiceMethod("delete_duplicate_rule")
    public FindAndDeleteDuplicateRule.Result deleteDuplicateRule(FindAndDeleteDuplicateRule.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getDuplicateRuleApiName()) || StringUtils.isBlank(arg.getDescribeApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        IDuplicatedSearch duplicatedSearch = duplicatedSearchService.findDuplicatedSearchByRuleApiName(arg.getDescribeApiName(),
                arg.getDuplicateRuleApiName(), context.getTenantId(), true);
        if (Objects.isNull(duplicatedSearch)) {
            return FindAndDeleteDuplicateRule.Result.builder().result(true).build();
        }
        if (duplicatedSearch.isEnable()) {
            throw new ValidateException(I18NExt.text(I18NKey.DUPLICATE_SEARCH_RULES_ENABLE_CANNOT_DELETE));
        }
        duplicatedSearchService.deleteByRuleApiName(arg.getDescribeApiName(), arg.getDuplicateRuleApiName(), context.getTenantId());
        return FindAndDeleteDuplicateRule.Result.builder().result(true).build();
    }

    @ServiceMethod("findDuplicateSearchByApiName")
    public SaveDuplicatedSearch.Result findDuplicateSearchByApiName(FindAndDeleteDuplicateRule.Arg
                                                                            arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getDuplicateRuleApiName()) || StringUtils.isBlank(arg.getDescribeApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }

        IDuplicatedSearch iDuplicatedSearch = duplicatedSearchService.findDuplicatedSearchByRuleApiName(arg.getDescribeApiName(),
                arg.getDuplicateRuleApiName(), context.getTenantId(), true, true);  // 后台编辑设计器, 走实时查询

        if (Objects.isNull(iDuplicatedSearch)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }

        iDuplicatedSearch = filterByDuplicateSearchRule(context, arg.getDescribeApiName(), iDuplicatedSearch);
        return SaveDuplicatedSearch.Result.builder().duplicateSearch(iDuplicatedSearch).build();
    }


    @ServiceMethod("refresh")
    public DuplicatedSearchRefresh.Result refresh(ServiceContext context, DuplicatedSearchRefresh.Arg arg) {
        return refresh(context.getTenantId(), arg);
    }


    @ServiceMethod("dataDuplicatedByRedis")
    public DataDuplicatedByRedis.Result dataDuplicatedByRedis(ServiceContext context, DataDuplicatedByRedis.Arg arg) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopy(context.getTenantId(), arg.getDescribeApiName());
        List<DuplicateSearchDataInfo> result = duplicatedSearchDataService.dataDuplicatedByRedis(context, describe, ObjectDataDocument.ofDataList(arg.getObjectDataList()));
        return DataDuplicatedByRedis.Result.builder().duplicatedInfos(result).build();
    }

    private DuplicatedSearchRefresh.Result refresh(String tenantId, DuplicatedSearchRefresh.Arg arg) {
        List<IDuplicatedSearch> allDuplicateSearchRule = duplicatedSearchService.findAll(tenantId, true);
        if (CollectionUtils.notEmpty(arg.getDescribeApiNameList())) {
            allDuplicateSearchRule.removeIf(x -> !arg.getDescribeApiNameList().contains(x.getDescribeApiName()));
        }
        List<String> failApiNameList = new ArrayList<>();
        List<String> allActiveObjectApiNameList = describeLogicService.findObjectsByTenantId(tenantId,
                false, false, false, true).stream().map(IObjectDescribe::getApiName).collect(Collectors.toList());
        allDuplicateSearchRule.removeIf(x -> AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterBlackObj(x.getDescribeApiName(), tenantId));
        allDuplicateSearchRule.removeIf(x -> !allActiveObjectApiNameList.contains(x.getDescribeApiName()));
        Map<String, List<IDuplicatedSearch>> objToDuplicateSearchRuleMap = allDuplicateSearchRule.stream().collect(Collectors.groupingBy(IDuplicatedSearch::getDescribeApiName));
        for (Map.Entry<String, List<IDuplicatedSearch>> objToDuplicateSearchRule : objToDuplicateSearchRuleMap.entrySet()) {
            String describeApiName = objToDuplicateSearchRule.getKey();
            List<IDuplicatedSearch> duplicatedSearchList = objToDuplicateSearchRuleMap.get(describeApiName);
            List<IDuplicatedSearchRefresh> duplicateRuleList = Lists.newArrayList();
            Set<String> ruleApiNames = Sets.newHashSet();
            for (IDuplicatedSearch duplicatedSearch : duplicatedSearchList) {
                if (Objects.isNull(DuplicatedSearchExt.of(duplicatedSearch).getPendingRules())
                        || CollectionUtils.empty(DuplicatedSearchExt.of(duplicatedSearch).getPendingRules().getRules())) {
                    continue;
                }
                String ruleApiName = "";
                do {
                    ruleApiName = DuplicatedSearchService.getRuleApiName();
                } while (!ruleApiNames.add(ruleApiName));

                duplicatedSearch.setRuleApiName(ruleApiName);
                duplicatedSearchService.fillRuleName(duplicatedSearch);
                duplicatedSearch.setEffective(true);
                duplicatedSearch.setVersion(0);
                if (StringUtils.isBlank(duplicatedSearch.getOperatorId())) {
                    duplicatedSearch.setOperatorId("-10000");
                }
                //最后修改时间为0不发crm消息
                duplicatedSearch.setLastModifiedTime(0L);
                duplicateRuleList.add(new IDuplicatedSearchRefresh(ruleApiName, duplicatedSearch));
            }
            try {
                if (CollectionUtils.notEmpty(duplicateRuleList)) {
                    duplicatedSearchService.deleteAndRefreshRule(tenantId, describeApiName, duplicateRuleList);
                } else {
                    log.warn("duplicate search rule isEmpty,tenantId:{},objectApiName:{}", tenantId, describeApiName);
                }
            } catch (Exception e) {
                failApiNameList.add(describeApiName);
                log.warn("duplicate search rule fail,tenantId:{}, objectApiName:{},info:{}", tenantId, describeApiName, e.getMessage());
            }
        }
        return DuplicatedSearchRefresh.Result.builder().failDescribeApiNameList(failApiNameList).build();

    }

    @ServiceMethod("batchRefresh")
    public DuplicatedSearchRefresh.BatchResult refresh(ServiceContext context, DuplicatedSearchRefresh.BatchArg arg) {
        Map<String, List<String>> finalTenant = Maps.newHashMap();
        Map<String, List<String>> grayTenant = arg.getGrayTenant();
        for (Map.Entry<String, List<String>> grayTenantMap : grayTenant.entrySet()) {
            String tenantId = grayTenantMap.getKey();
            DuplicatedSearchRefresh.Result result = refresh(tenantId, DuplicatedSearchRefresh.Arg.builder().describeApiNameList(grayTenant.get(tenantId)).build());
            finalTenant.put(tenantId, result.getFailDescribeApiNameList());
        }
        log.warn("duplicated refresh rule failTenantAndObjApiName:{}", finalTenant);
        return DuplicatedSearchRefresh.BatchResult.builder().finalTenant(finalTenant).build();
    }


    public List<String> getApiNames(IDuplicatedSearch duplicatedSearchRule) {
        if (Objects.isNull(duplicatedSearchRule)) {
            return Lists.newArrayList();
        }
        List<String> apiNames = Lists.newArrayList();
        apiNames.add(duplicatedSearchRule.getDescribeApiName());
        if (Objects.isNull(duplicatedSearchRule.getUseableRules()) || CollectionUtils.empty(duplicatedSearchRule.getUseableRules().getRelatedDescribes())) {
            return apiNames;
        }
        duplicatedSearchRule.getUseableRules().getRelatedDescribes().stream()
                .filter(IDuplicatedSearch.RelatedDescribe::isEnable)
                .forEach(x -> apiNames.add(x.getDescribeApiName()));
        return apiNames;
    }

    /**
     * 如果规则的显示字段为空，则代表当前用户无权权限使用该规则查重
     * 按查重规则顺序进行校验，如果没有当前用户没有其显示字段的查看权限就弹框
     *
     * @param showFieldMap
     * @param queryList
     */
    public void checkShowField(Map<String, List<String>> showFieldMap, List<DuplicateSearchQuery> queryList) {
        for (DuplicateSearchQuery duplicateSearchQuery : queryList) {
            List<String> showFields = getShowFields(duplicateSearchQuery, showFieldMap);
            if (CollectionUtils.empty(showFields)) {
                throw new ValidateException(I18N.text(I18NKey.NO_PERMISSION_VIEW));
            }
        }
    }

    private IDuplicatedSearch getDuplicateSearch(DuplicateSearchQuery query) {
        if (StringUtils.isNotBlank(query.getRelatedApiName()) || CollectionUtils.notEmpty(query.getDuplicatedSearchMap())) {
            List<IDuplicatedSearch> relatedDuplicateSearchList = query.getDuplicatedSearchMap().get(query.getRelatedApiName());
            if (CollectionUtils.notEmpty(relatedDuplicateSearchList)) {
                return relatedDuplicateSearchList.get(0);
            }
        }
        return query.getDuplicatedSearchRule();
    }

    public List<IObjectData> getObjectData(User user, IObjectDescribe objectDescribe, List<String> ids) {
        if (CollectionUtils.empty(ids)) {
            return Lists.newArrayList();
        }
        return metaDataFindService.findByIdsIncludeInvalid(ids, user.getTenantId(), objectDescribe.getApiName(),
                ActionContextExt.of(user).getContext());
    }

    public List<String> getDuplicateDataIdsWithPage(String token, long total, Integer pageNumber, Integer pageSize) {
        if (total == 0) {
            return Lists.newArrayList();
        }
        pageSize = Objects.isNull(pageSize) || pageSize < 1 ? Integer.valueOf(20) : pageSize;
        pageNumber = Objects.isNull(pageNumber) || pageNumber < 1 ? Integer.valueOf(1) : pageNumber;
        int start = (pageNumber - 1) * pageSize;
        int offset = total >= (start + pageSize) ? pageSize : (int) (total % pageSize);
        int end = (pageNumber - 1) * pageSize + offset - 1;

        List<String> ids = redisDao.zrangeByScore(token, start, end);
        return ids;
    }

    public GetResult.Page getPage(Integer pageSize, Integer pageNumber, long total) {
        pageNumber = Objects.isNull(pageNumber) || pageNumber < 1 ? Integer.valueOf(1) : pageNumber;
        pageSize = Objects.isNull(pageSize) || pageSize < 1 ? Integer.valueOf(20) : pageSize;
        GetResult.Page page = GetResult.Page.builder()
                .pageCount(1)
                .pageNumber(pageNumber)
                .pageSize(pageSize)
                .total(0)
                .build();
        if (total > 0) {
            page.setPageCount((int) (total % pageSize == 0 ? total / pageSize : total / pageSize + 1));
            page.setPageNumber(pageNumber);
            page.setPageSize(pageSize);
            page.setTotal((int) total);
        }
        return page;
    }

    public Map<String, DataIdsTotal> getDuplicateDataIdsTotal(List<IDuplicatedSearch> duplicatedSearchRuleList, boolean isNeedDuplicate, User user,
                                                              IObjectData objectData, IObjectDescribe objectDescribe, List<String> apiNames,
                                                              IDuplicatedSearch.Type type, UnaryOperator<List<DuplicateSearchResult.DuplicateData>> handleDuplicateSearchResult,
                                                              String ruleApiNameIntercepted, String uniqueCacheId) {
        log.info("getDuplicateDataIdsTotal info isNeedDuplicate:{}, user:{}, objectData:{}",
                isNeedDuplicate, JSON.toJSONString(user), objectData.toJsonString());
        //同一批规则 对象apiName，类型，是否查重都是一致的，所以只用取第一个的就可以
        IDuplicatedSearch duplicatedSearchRule = duplicatedSearchRuleList.get(0);
        String apiName = duplicatedSearchRule.getDescribeApiName();
        //去掉未启用的查重规则
        duplicatedSearchRuleList.removeIf(x -> !DuplicatedSearchExt.isEnableDuplicate(x));
        if (CollectionUtils.empty(duplicatedSearchRuleList)) {
            return Maps.newHashMap();
        }
        String finalRuleApiName = "";
        if (isNeedDuplicate) {
            // 下游企业校验查重次数
            checkLimitWithOuter(user, type);
            //获取本对象apiName和联合查重对象apiName(包含未开启联合查重的对象)
            Set<String> allApiNameList = Sets.newHashSet();
            duplicatedSearchRuleList.forEach(x -> allApiNameList.addAll(Optional.ofNullable(x.getUseableRules().getRelatedDescribes())
                    .orElse(Lists.newArrayList())
                    .stream()
                    .map(IDuplicatedSearch.RelatedDescribe::getDescribeApiName)
                    .collect(Collectors.toList())));
            allApiNameList.add(apiName);
            //删除上一次存的key(清空该对象下所有的查重规则key，为了重新查重)
            Set<String> tokenSet = Sets.newHashSet();
            allApiNameList.forEach(x -> {
                duplicatedSearchRuleList.forEach(duplicatedSearch -> {
                    tokenSet.add(getToken(user, apiName, type, x, DuplicatedSearchExt.of(duplicatedSearch).getRealRuleApiName(), uniqueCacheId));
                });
            });
            if (log.isDebugEnabled()) {
                log.debug("searchDuplicatedData batchDelKey,tenantId:{},userId:{},objectApiName:{},tokenList:{}", user.getTenantId(), user.getUserId(), objectData.getDescribeApiName(), tokenSet);
            }
            redisDao.batchDelKey(Lists.newArrayList(tokenSet));

            //1.查询重复数据 2.把查到的数据放到缓存中
            List<DuplicateSearchResult.DuplicateData> duplicateDataList = null;
            if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(user.getTenantId(), apiName)) {
                duplicateDataList = duplicatedSearchDataService.searchDuplicateDataByType(user, objectData, type, objectDescribe, duplicatedSearchRuleList);
            } else {
                duplicateDataList = duplicatedSearchDataService.searchDuplicateDataByType(user, objectData, type, objectDescribe);
            }
            if (Objects.nonNull(handleDuplicateSearchResult)) {
                duplicateDataList = handleDuplicateSearchResult.apply(duplicateDataList);
            }
            //拿到查到数据的查重规则ApiName
            if (!CollectionUtils.empty(duplicateDataList)) {
                finalRuleApiName = duplicateDataList.get(0).getRuleApiName();
            }
            //把匹配到重复数据的规则插入到redis中，超时时间五分钟
            List<String> tokenList = Lists.newArrayList();
            duplicateDataList.forEach(duplicateData -> {
                String token = getToken(user, apiName, type, duplicateData.getApiName(), duplicateData.getRuleApiName(), uniqueCacheId);
                if (duplicateData.getDataIds().size() > 0) {
                    // 不在使用 lpush，不需要翻转
//                    Collections.reverse(duplicateData.getDataIds());
                    List<String> ids = duplicateData.getDataIds();
                    tokenList.add(token);
                    redisDao.zadd(token, ids, 5 * 60);
                }
            });
            if (log.isDebugEnabled()) {
                log.debug("insert redis data info,tenantId:{},userId:{},object:{},tokenList:{}, result:{}", user.getTenantId(),
                        user.getUserId(), objectDescribe.getApiName(), JSON.toJSONString(tokenList), JSON.toJSONString(duplicateDataList));
            }
        }

        List<DataIdsTotal> dataIdsTotals = Lists.newArrayList();
        String finalTargetRuleApiName = StringUtils.isEmpty(ruleApiNameIntercepted) ? finalRuleApiName : ruleApiNameIntercepted;
        apiNames.forEach(x -> {
            String token = getToken(user, apiName, type, x, finalTargetRuleApiName, uniqueCacheId);
            long len = redisDao.zcard(token);
            if (len > 0) {
                DataIdsTotal total = DataIdsTotal.builder()
                        .apiName(x)
                        .token(token)
                        .total(len)
                        .build();
                dataIdsTotals.add(total);
            }
        });
        for (DataIdsTotal dataIdsTotal : dataIdsTotals) {
            dataIdsTotal.setRuleApiName(finalTargetRuleApiName);
        }
        return dataIdsTotals.stream().collect(Collectors.toMap(DataIdsTotal::getApiName, x -> x, (x1, x2) -> x1));
    }


    private void checkLimitWithOuter(User user, IDuplicatedSearch.Type type) {
        if (!AppFrameworkConfig.isDuplicateOuterGrayTenantId(user.getTenantId())) {
            return;
        }
        if (IDuplicatedSearch.Type.TOOL != type || !user.isOutUser()) {
            return;
        }
        String key = String.format("%s_%s_%s", "UDOBJ", "DUPLICATE_LIMIT", user.getTenantId());
        LimiterInfo limiterInfo = redissonService.incrementAndGet(key, 1, TimeUnit.DAYS, LimiterInfo::incrementAndGet, new LimiterInfo());
        if (limiterInfo == null) {
            throw new ValidateException(I18N.text(I18NKey.SERVICE_BUSY_TRY_AGAIN_LATER));
        }
        if (limiterInfo.getCurrentNumber() > AppFrameworkConfig.getDuplicateLimitGray(user.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKey.OUTER_DUPLICATED_LIMIT));
        }
    }

    private String getToken(User user, String apiName, IDuplicatedSearch.Type type, String relatedApiName, String uniqueId) {
        return String.format("%s_%s_%s%s_%s_%s_duplicateDataIds", user.getTenantId(), user.getUserId(), apiName, relatedApiName, type, uniqueId);
    }

    private String getToken(User user, String apiName, IDuplicatedSearch.Type type, String relatedApiName, String ruleApiName, String uniqueId) {
        if (StringUtils.isEmpty(ruleApiName)) {
            return getToken(user, apiName, type, relatedApiName, uniqueId);
        }
        return String.format("%s_%s_%s%s_%s_%s_%s_duplicateDataIds", user.getTenantId(), user.getUserId(), apiName, relatedApiName, ruleApiName, type, uniqueId);
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DataIdsTotal {
        private String apiName;
        private String ruleApiName;
        private String token;
        private Long total;
    }

    public List<String> getAdmins(User user) {
        if (user.isOutUser()) {
            return Lists.newArrayList();
        }
        AuthContext auth = new AuthContext();
        auth.setAppId(Constant.getAppId());
        auth.setUserId(user.getUserId());
        auth.setTenantId(user.getTenantId());
        List<String> adminUsers = Lists.newArrayList();
        try {
            adminUsers = userInfoService.getAdminUsers(auth, null);
        } catch (CrmCheckedException e) {
            log.error("getAdminUsers error: auth:{}", auth, e);
        }
        return adminUsers;
    }

    // 获取button信息
    public GetResult.ButtonInfo fillButtonInfo(User user, List<IObjectData> dataList, List<String> showFields, IObjectDescribe objectDescribe, List<String> ids) {
        if (CollectionUtils.empty(dataList)) {
            return new GetResult.ButtonInfo();
        }
        Map<String, Permissions> dataPrivilegeMap = metaDataMiscService.checkDataPrivilege(user, ids, objectDescribe);
        boolean isHiddenAdminBtn = qiXinAppLogicService.isHiddenQiXinEntrance(user);
        if ((user.isOutUser() && AppFrameworkConfig.isDuplicateOuterGrayTenantId(user.getTenantId())) || isHiddenAdminBtn) {
            return fillButtonInfoByOuter(user, dataPrivilegeMap, dataList);
        }

        boolean isAdmin = userRoleInfoService.isAdmin(user);
        Map<String, String> buttonMap = Maps.newHashMap();
        Map<String, List<String>> buttonMapList = Maps.newHashMap();
        dataList.forEach(x -> {
            if (Objects.equals(ObjectDataExt.of(x).getLifeStatus(), ObjectLifeStatus.INVALID)) {
                if (!isAdmin) {
                    buttonMap.put(x.getId(), GetResult.DuplicateButtonType.Admin.getApiName());
                    buttonMapList.put(x.getId(), Lists.newArrayList(GetResult.DuplicateButtonType.Admin.getApiName()));
                }
            } else {
                if (Objects.equals(Permissions.NO_PERMISSION, dataPrivilegeMap.get(x.getId()))) {
                    if (showFields.contains(ObjectDataExt.OWNER) && ObjectDataExt.of(x).hasOwner()) {
                        buttonMap.put(x.getId(), GetResult.DuplicateButtonType.Owner.getApiName());
                        buttonMapList.put(x.getId(), Lists.newArrayList(GetResult.DuplicateButtonType.Owner.getApiName()));
                    }
                } else {
                    buttonMap.put(x.getId(), GetResult.DuplicateButtonType.Detail.getApiName());
                    buttonMapList.put(x.getId(), Lists.newArrayList(GetResult.DuplicateButtonType.Detail.getApiName()));
                }
            }
        });

        List<GetResult.Button> buttons = Lists.newArrayList();
        buttons.add(GetResult.Button.builder().apiName(GetResult.DuplicateButtonType.Detail.getApiName()).label(I18N.text(GetResult.DuplicateButtonType.Detail.getLabel())).build());
        buttons.add(GetResult.Button.builder().apiName(GetResult.DuplicateButtonType.Admin.getApiName()).label(I18N.text(GetResult.DuplicateButtonType.Admin.getLabel())).build());
        buttons.add(GetResult.Button.builder().apiName(GetResult.DuplicateButtonType.Owner.getApiName()).label(I18N.text(GetResult.DuplicateButtonType.Owner.getLabel())).build());
        return GetResult.ButtonInfo.builder()
                .buttonMap(buttonMap)
                .buttonMapList(buttonMapList)
                .buttons(buttons)
                .build();

    }

    private GetResult.ButtonInfo fillButtonInfoByOuter(User user, Map<String, Permissions> dataPrivilegeMap, List<IObjectData> dataList) {
        Map<String, String> buttonMap = Maps.newHashMap();
        Map<String, List<String>> buttonMapList = Maps.newHashMap();
        dataList.stream()
                .filter(data -> !Objects.equals(ObjectDataExt.of(data).getLifeStatus(), ObjectLifeStatus.INVALID))
                .filter(data -> !Permissions.NO_PERMISSION.equals(dataPrivilegeMap.get(data.getId())))
                .forEach(data -> {
                    buttonMap.put(data.getId(), GetResult.DuplicateButtonType.Detail.getApiName());
                    buttonMapList.put(data.getId(), Lists.newArrayList(GetResult.DuplicateButtonType.Detail.getApiName()));
                });
        List<GetResult.Button> buttons = Lists.newArrayList();
        buttons.add(GetResult.Button.builder().apiName(GetResult.DuplicateButtonType.Detail.getApiName()).label(I18N.text(GetResult.DuplicateButtonType.Detail.getLabel())).build());
        return GetResult.ButtonInfo.builder()
                .buttonMap(buttonMap)
                .buttonMapList(buttonMapList)
                .buttons(buttons)
                .build();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DuplicateSearchQuery {
        private IDuplicatedSearch duplicatedSearchRule;
        private Map<String, List<IDuplicatedSearch>> duplicatedSearchMap;
        private String relatedApiName;
        private boolean isNeedDuplicate;
        private Integer pageSize;
        private Integer pageNumber;

        public Integer getPageSize() {
            return Objects.isNull(pageSize) || pageSize < 1 ? Integer.valueOf(20) : pageSize;
        }

        public Integer getPageNumber() {
            return Objects.isNull(pageNumber) || pageNumber < 1 ? Integer.valueOf(1) : pageNumber;
        }

        public static final List<String> SIMPLE_SEARCH_NUMBER_FIELDS = Lists.newArrayList(
                IFieldType.TEXT,
                IFieldType.LONG_TEXT,
                IFieldType.URL,
                IFieldType.EMAIL,
                IFieldType.PHONE_NUMBER
        );

        public static final List<String> SIMPLE_SEARCH_NON_NUMBER_FIELDS = Lists.newArrayList(
                IFieldType.TEXT,
                IFieldType.LONG_TEXT,
                IFieldType.URL,
                IFieldType.EMAIL,
                IFieldType.LOCATION
        );

        public IDuplicatedSearch.Type getSearchType() {
            return Objects.isNull(duplicatedSearchRule) ? IDuplicatedSearch.Type.NEW : duplicatedSearchRule.getType();
        }

        public List<String> getShowFieldList(User user, FunctionPrivilegeService functionPrivilegeService) {
            //取当前对象的查重规则
            IDuplicatedSearch duplicatedSearch = duplicatedSearchRule;
            //如果是联合的对象查重，取联合对象的查重规则，如果显示字段为空，需要预置显示字段
            if (StringUtils.isNotBlank(relatedApiName) && CollectionUtils.notEmpty(duplicatedSearchMap)) {
                List<IDuplicatedSearch> relateDuplicateSearchList = duplicatedSearchMap.get(relatedApiName);
                if (CollectionUtils.notEmpty(relateDuplicateSearchList)) {
                    duplicatedSearch = relateDuplicateSearchList.get(0);
                }
            }
            List<String> showFields = Lists.newArrayList();
            if (Objects.isNull(duplicatedSearch) || Objects.isNull(duplicatedSearch.getUseableRules()) || CollectionUtils.empty(duplicatedSearch.getUseableRules().getShowFields())) {
                showFields.add(IObjectData.NAME);
                return showFields;
            } else {
                showFields = duplicatedSearch.getUseableRules().getShowFields();
            }
            //过滤字段权限
            Set<String> unauthorizedFields = functionPrivilegeService.getUnauthorizedFields(user, duplicatedSearch.getDescribeApiName());
            showFields = showFields.stream().filter(x -> !unauthorizedFields.contains(x)).collect(Collectors.toList());
            return showFields;
        }

        // 获取显示条数
        public long getShowNum() {
            if (StringUtils.isBlank(relatedApiName) || Objects.isNull(duplicatedSearchRule)
                    || StringUtils.equals(relatedApiName, duplicatedSearchRule.getDescribeApiName())) {
                return Objects.isNull(duplicatedSearchRule) ? 0 : duplicatedSearchRule.getUseableRules().getShowNum();
            }

            List<IDuplicatedSearch.RelatedDescribe> relatedDescribes = duplicatedSearchRule.getUseableRules().getRelatedDescribes();
            Map<String, IDuplicatedSearch.RelatedDescribe> relatedDescribeMap = relatedDescribes.stream()
                    .collect(Collectors.toMap(IDuplicatedSearch.RelatedDescribe::getDescribeApiName, x -> x, (x1, x2) -> x1));
            IDuplicatedSearch.RelatedDescribe relatedDescribe = relatedDescribeMap.get(relatedApiName);

            return Objects.isNull(relatedDescribe) ? 0 : relatedDescribe.getRulesDef().getShowNum();
        }

        public boolean isKeepSave(DataIdsTotal dataIdsTotal) {
            if (Objects.isNull(dataIdsTotal) || dataIdsTotal.getTotal() < 1) {
                return true;
            }
            return Objects.nonNull(duplicatedSearchRule)
                    && Objects.equals(duplicatedSearchRule.getType(), IDuplicatedSearch.Type.NEW)
                    && duplicatedSearchRule.getUseableRules().getRules().stream()
                    .anyMatch(a -> a.getConditions().stream()
                            .anyMatch(b -> Objects.equals(IDuplicatedSearch.Policy.FUZZY, b.getFieldValue())));
        }

        private IDuplicatedSearch.Policy matchType(DataIdsTotal dataIdsTotal) {
            if (Objects.isNull(duplicatedSearchRule)) {
                return IDuplicatedSearch.Policy.PRECISE;
            }
            //ios用matchType == FUZZY && keepSave == true 判断是否可以继续保存(应该只按keepSave判断)，兼容这个bug，增加这段逻辑
            if (RequestUtil.isIOSRequest()) {
                if (Objects.isNull(dataIdsTotal) || dataIdsTotal.getTotal() < 1) {
                    return IDuplicatedSearch.Policy.FUZZY;
                }
            }

            boolean haveFuzzyType = duplicatedSearchRule.getUseableRules().getRules().stream()
                    .anyMatch(x -> x.getConditions().stream()
                            .anyMatch(y -> Objects.equals(IDuplicatedSearch.Policy.FUZZY, y.getFieldValue())));
            return haveFuzzyType ? IDuplicatedSearch.Policy.FUZZY : IDuplicatedSearch.Policy.PRECISE;
        }

    }

    public List<String> getConditionFields(IDuplicatedSearch duplicatedSearchRule) {
        if (Objects.isNull(duplicatedSearchRule) || Objects.isNull(duplicatedSearchRule.getUseableRules())) {
            return Lists.newArrayList();
        }

        Set<String> fields = Sets.newHashSet();
        duplicatedSearchRule.getUseableRules().getRules()
                .forEach(a -> a.getConditions()
                        .forEach(b -> fields.add(b.getFieldName())));
        return Lists.newArrayList(fields);
    }

}