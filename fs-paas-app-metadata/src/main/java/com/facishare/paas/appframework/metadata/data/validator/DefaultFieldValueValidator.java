package com.facishare.paas.appframework.metadata.data.validator;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Set;

@Component
public class DefaultFieldValueValidator implements IDataTypeValidator, IMaxLengthValidator {
    @Override
    public void validateDataType(String fieldApiName, IObjectData data, IObjectDescribe describe) {

    }

    @Override
    public void validateMaxLength(String fieldApiName, IObjectData data, IObjectDescribe describe) throws ValidateException {

    }

    @Override
    public Set<String> supportFieldTypes() {
        return Sets.newHashSet();
    }

}
