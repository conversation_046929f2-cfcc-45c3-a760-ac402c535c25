package com.facishare.paas.appframework.core.util;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Language;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@ToString
@EqualsAndHashCode
@Slf4j
public final class Lang {
    public static final Lang zh_CN;
    public static final Lang zh_TW;
    public static final Lang en;

    private final String value;

    private static final ConcurrentMap<String, Lang> all = Maps.newConcurrentMap();

    private static final String SIMPLIFIED_CHINESE = "zh-CN";
    private static final String TRADITIONAL_CHINESE = "zh-TW";
    private static final String ENGLISH = "en";
    
    private static final ThreadLocal<List<Lang>> LOCAL_LANG = new ThreadLocal<>();

    private Lang(String value) {
        this.value = value;
    }

    static {
        zh_CN = new Lang(SIMPLIFIED_CHINESE);
        zh_TW = new Lang(TRADITIONAL_CHINESE);
        en = new Lang(ENGLISH);
    }

    private static List<Lang> reloadLangByEi(String tenantId) {

        List<Lang> langs = LOCAL_LANG.get();
        if (CollectionUtils.isNotEmpty(langs)) {
            return langs;
        }
        
        List<Language> languages = Optional.ofNullable(tenantId)
                .map(LanguageClientUtil::getLanguages)
                .orElseGet(() -> I18nClient.getInstance().getAllLanguage());

        List<Lang> langList = languages.stream()
                .map(Language::getCode)
                .map(Lang::from)
                .collect(Collectors.toList());
        
        LOCAL_LANG.set(langList);
        
        return langList;
    }
    
    public static void clear() {
        LOCAL_LANG.remove();
    }
    
    private static List<Lang> reloadLang() {
        List<Language> languages = Optional.ofNullable(TraceContext.get())
                .map(TraceContext::getEi)
                .filter(NumberUtils::isCreatable)
                .map(Long::valueOf)
                .map(ei -> I18nClient.getInstance().getLanguage(ei))
                .orElseGet(() -> I18nClient.getInstance().getAllLanguage());

        return languages.stream()
                .map(Language::getCode)
                .map(Lang::from)
                .collect(Collectors.toList());
    }

    private static SortedMap<String, Lang> buildComparatorMap(List<Lang> supportLanguage) {
        return supportLanguage.stream().collect(Collectors.toMap(Lang::getValue, Function.identity(),
                (x, y) -> x, () -> Maps.newTreeMap(Lang.SPECIAL_STRING_COMPARATOR)));
    }

    public String getValue() {
        return value;
    }

    @JsonCreator
    public static Lang of(@JsonProperty("value") String value) {
        if (Strings.isNullOrEmpty(value)) {
            return defaultLang();
        }

        SortedMap<String, Lang> comparatorMap = buildComparatorMap(reloadLang());
        String langCode = parseLangCode(value);
        return comparatorMap.getOrDefault(langCode, defaultLang());
    }


    private static String parseLangCode(String value) {
        String langCode = null;
        try {
            langCode = I18nClient.getInstance().getCodeByAliasCode(value);
        } catch (Exception e) {
            log.warn("parseLangCode error, value:{}", value, e);
        }

        if (StringUtils.isNotBlank(langCode)) {
            return langCode;
        }

        if (StringUtils.startsWith(value, ENGLISH) && !Objects.equals(value, ENGLISH)) {
            return ENGLISH;
        }
        return value;
    }


    public static Lang defaultLang() {
        return Lang.zh_CN;
    }

    public static List<Lang> values() {
        return Optional.ofNullable(TraceContext.get())
                .map(TraceContext::getEi)
                .map(Lang::reloadLangByEi)
                .orElseGet(Lang::reloadLang);
    }

    private static Lang from(String languageCode) {
        if (Strings.isNullOrEmpty(languageCode)) {
            return defaultLang();
        }
        return all.computeIfAbsent(languageCode, code -> {
            switch (code) {
                case SIMPLIFIED_CHINESE:
                    return zh_CN;
                case TRADITIONAL_CHINESE:
                    return zh_TW;
                case ENGLISH:
                    return en;
                default:
                    return new Lang(code);
            }
        });
    }

    private static final SpecialStringComparator SPECIAL_STRING_COMPARATOR = new SpecialStringComparator();

    private static class SpecialStringComparator implements Comparator<String> {

        @Override
        public int compare(String s1, String s2) {
            int n1 = s1.length();
            int n2 = s2.length();
            int min = Math.min(n1, n2);
            for (int i = 0; i < min; i++) {
                char c1 = s1.charAt(i);
                char c2 = s2.charAt(i);
                if (c1 != c2) {
                    c1 = convert(c1);
                    c2 = convert(c2);
                    if (c1 != c2) {
                        return c1 - c2;
                    }
                }
            }
            return n1 - n2;
        }

        private char convert(char ch) {
            if ('_' == ch) {
                return '-';
            }
            return ch;
        }
    }
}
