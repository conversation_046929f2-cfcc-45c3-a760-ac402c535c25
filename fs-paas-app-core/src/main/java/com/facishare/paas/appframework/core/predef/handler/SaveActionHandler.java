package com.facishare.paas.appframework.core.predef.handler;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.handler.ActionHandler;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction.RelatedDataDocument;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.dto.ConvertRuleDataContainer;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData.RelatedObjectData;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2023/1/10.
 */
public interface SaveActionHandler<A extends SaveActionHandler.Arg, R extends SaveActionHandler.Result> extends ActionHandler<A, R> {

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(exclude = {"detailDescribeMap", "relatedDescribeMap", "uniqueRule"}, callSuper = true)
    class Arg extends ActionHandler.Arg<BaseObjectSaveAction.Arg> {
        //从对象描述集合
        @JsonIgnore
        private Map<String, IObjectDescribe> detailDescribeMap;
        //相关对象描述集合
        @JsonIgnore
        private Map<String, IObjectDescribe> relatedDescribeMap;
        //主对象数据
        private ObjectDataDocument objectData;
        //从对象数据
        private Map<String, List<ObjectDataDocument>> detailObjectData;
        //相关对象数据
        private Map<String, List<RelatedDataDocument>> relatedObjectData;
        //转换规则数据
        @JsonIgnore
        private ConvertRuleDataContainer convertRuleDataContainer;
        //唯一性规则
        @JsonIgnore
        private IUniqueRule uniqueRule;
        //是否审批例外人
        private boolean assigneesExceptional;

        private boolean fromChangeOrder;

        public boolean containsDetailDescribe(String detailApiName) {
            if (CollectionUtils.empty(detailDescribeMap)) {
                return false;
            }
            return detailDescribeMap.containsKey(detailApiName);
        }

        public IObjectDescribe getDetailDescribe(String apiName) {
            if (CollectionUtils.empty(detailDescribeMap)) {
                return null;
            }
            return detailDescribeMap.get(apiName);
        }

        public IObjectDescribe getRelatedDescribe(String apiName) {
            if (CollectionUtils.empty(relatedDescribeMap)) {
                return null;
            }
            return relatedDescribeMap.get(apiName);
        }

        public Map<String, IObjectDescribe> allDescribes() {
            Map<String, IObjectDescribe> result = Maps.newHashMap();
            result.put(getObjectDescribe().getApiName(), getObjectDescribe());
            if (CollectionUtils.notEmpty(detailDescribeMap)) {
                result.putAll(detailDescribeMap);
            }
            if (CollectionUtils.notEmpty(relatedDescribeMap)) {
                result.putAll(relatedDescribeMap);
            }
            return result;
        }

        public Map<String, IObjectDescribe> masterAndDetailDescribes() {
            Map<String, IObjectDescribe> result = Maps.newHashMap();
            result.put(getObjectDescribe().getApiName(), getObjectDescribe());
            if (CollectionUtils.notEmpty(detailDescribeMap)) {
                result.putAll(detailDescribeMap);
            }
            return result;
        }

        public IObjectData objectData() {
            return objectData.toObjectData();
        }

        public Map<String, List<IObjectData>> detailObjectData() {
            if (Objects.isNull(detailObjectData)) {
                return Maps.newHashMap();
            }
            return ObjectDataDocument.ofDataMap(detailObjectData);
        }

        public Map<String, List<RelatedObjectData>> relatedObjectData() {
            if (Objects.isNull(relatedObjectData)) {
                return Maps.newHashMap();
            }
            return RelatedDataDocument.toRelatedObjectDataMap(relatedObjectData);
        }

        public Map<String, List<IObjectData>> relatedDataMap() {
            Map<String, List<IObjectData>> relatedDataMap = Maps.newHashMap();
            Map<String, List<SaveMasterAndDetailData.RelatedObjectData>> relatedObjectData = relatedObjectData();
            if (CollectionUtils.empty(relatedObjectData)) {
                return relatedDataMap;
            }
            relatedObjectData.forEach((apiName, relatedData) -> {
                List<IObjectData> dataList = relatedData.stream()
                        .map(SaveMasterAndDetailData.RelatedObjectData::getDataList)
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList());
                relatedDataMap.put(apiName, dataList);
            });
            return relatedDataMap;
        }

        public List<IObjectData> masterAndDetailData() {
            List<IObjectData> result = Lists.newArrayList();
            if (CollectionUtils.notEmpty(detailObjectData)) {
                detailObjectData().forEach((k, v) -> result.addAll(v));
            }
            result.add(objectData());
            return result;
        }

        public boolean acceptNonBlockingResult() {
            if (RequestUtil.isCepRequest()) {
                return true;
            }
            return getInterfaceArg().acceptNonBlockingResult();
        }

        public boolean enableUniqueCheckResult() {
            return getInterfaceArg().enableUniqueCheckResult();
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class Result extends ActionHandler.Result<BaseObjectSaveAction.Result> {
        //主对象数据
        private ObjectDataDocument objectData;
        //从对象数据
        private Map<String, List<ObjectDataDocument>> detailObjectData;
        //是否跳过查重校验
        private Boolean skipDuplicateSearchCheck;
        //是否跳过前验证函数校验
        private Boolean skipValidationFunctionCheck;
        //是否跳过验证规则校验
        private Boolean skipValidationRuleCheck;
        //是否跳过唯一规则校验
        private Boolean skipUniqueRuleCheck;
    }
}
