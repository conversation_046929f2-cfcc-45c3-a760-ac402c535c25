package com.facishare.paas.appframework.metadata.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 跨对象筛选相关DTO
 * 
 * <AUTHOR>
 */
public interface CrossObjectFilter {
    /**
     * 国际化键值
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class I18nKey {
        private String key;
        private String defaultName;
    }

    /**
     * 对象和字段信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ObjectAndField {
        /**
         * 字段信息列表
         */
        private List<FieldInfo> fieldInfoList;
        
        /**
         * 节点名称
         */
        private String nodeName;
        
        /**
         * CRM对象名称
         */
        private String crmObjName;
        
        /**
         * 节点ID
         */
        private String nodeId;
    }
    
    /**
     * 字段信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class FieldInfo {
        /**
         * API名称
         */
        private String apiName;
        
        /**
         * CRM对象名称
         */
        private String crmObjName;
    }

    /**
     * 获取对象关系参数
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ObjRelationArg {
        /**
         * 业务对象列表
         */
        private List<Map<String, Object>> businessObjects;

        /**
         * 场景ID列表
         */
        private List<String> defaultFilterOptionIDs;

        /**
         * 分页页码
         */
        private Integer pageNumber;

        /**
         * 分页大小
         */
        private Integer pageSize;

        /**
         * paas筛选器
         */
        private String commonFilterList;
    }

    /**
     * 获取对象关系结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ObjRelationResult {
        /**
         * 对象和字段信息
         */
        private List<ObjectAndField> objectsAndFields;
        
    }

    /**
     * 查询报表数据参数
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class QueryReportArg {
        /**
         * 业务对象列表
         */
        private List<Map<String, Object>> businessObjects;
        
        /**
         * 场景ID列表
         */
        private List<String> defaultFilterOptionIDs;
        
        /**
         * 分页页码
         */
        private Integer pageNumber;
        
        /**
         * 分页大小
         */
        private Integer pageSize;
        
        /**
         * paas筛选器
         */
        private String commonFilterList;

        /**
         * 是否预览 0: 否 1: 是
         */
        private String isPreview;
    }

    /**
     * 查询报表数据结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class QueryReportResult {
        /**
         * 数据id列表
         */
        private List<String> id;
        
        /**
         * 数据id个数
         */
        private Integer totalNum;
    }

    /**
     * 通用过滤条件
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class CommonFilter {
        private String fieldName;
        private String operator;
        private String value;
    }
} 