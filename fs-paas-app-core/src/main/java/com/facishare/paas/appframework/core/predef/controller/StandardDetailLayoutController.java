package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ComponentActions;
import com.facishare.paas.appframework.common.util.ContextCacheUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.RelatedObjectDescribeStructure;
import com.facishare.paas.appframework.metadata.layout.LayoutAgentType;
import com.facishare.paas.appframework.metadata.layout.LayoutContext;
import com.facishare.paas.appframework.metadata.layout.LayoutVersion;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class StandardDetailLayoutController extends PreDefineController<StandardDetailLayoutController.Arg, StandardDetailLayoutController.Result> {

    protected IObjectDescribe describe;
    protected List<IObjectDescribe> related = Lists.newArrayList();
    protected List<RelatedObjectDescribeStructure> relatedObjects = Lists.newArrayList();
    protected List<RelatedObjectDescribeStructure> detailObjects = Lists.newArrayList();
    protected boolean supportTag;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardController.DetailLayout.getFuncPrivilegeCodes();
    }

    @Override
    protected void before(Arg arg) {
        initLayoutContext();
        stopWatch.lap("initLayoutContext");

        //获取对象描述
        describe = findObject();
        stopWatch.lap("findObject");
        //查询相关对象
        related = findRelatedObjects();
        stopWatch.lap("findRelatedObjects");

        //初始化context的缓存，可能包括功能权限、数据权限、当前用户是否CRM管理员
        initContextCache();
        stopWatch.lap("initContextCache");

        //根据List权限过滤相关对象
        filterRelatedObjects();
        stopWatch.lap("filterRelatedObjects");

        super.before(arg);
    }

    @Override
    protected Result doService(Arg arg) {
        //查layout，包括关联对象对应的component，相关对象的layout由pg元数据提供接口
        ILayout layout = getLayout();
        stopWatch.lap("getLayout");

        if (arg.getIncludeStatistics()) {
            infraServiceFacade.computeCalculateRelation(describe, null);
            stopWatch.lap("computeCalculateRelation");
        }

        supportTag = !describe.isBigObject() && infraServiceFacade.isSupportTag(arg.getObjectDescribeApiName(), controllerContext.getUser());
        stopWatch.lap("checkIfSupportTag");

        return buildResult(layout);
    }

    @Override
    protected final void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            LayoutContext.remove();
        }
    }

    protected ILayout getLayout() {
        ILayout layout = serviceFacade.getLayoutLogicService().getLayoutWithComponents(buildLayoutContext(), arg.getRecordType(),
                describe, null, relatedObjects, detailObjects, null, PageType.WebDetail,
                arg.getFromRecycleBin(), true);
        return layout;
    }

    protected LayoutLogicService.LayoutContext buildLayoutContext() {
        return LayoutLogicService.LayoutContext.of(controllerContext.getUser(), controllerContext.getAppId());
    }

    protected Result buildResult(ILayout layout) {
        Result result = Result.builder()
                .describe(arg.getIncludeDescribe() ?
                        ObjectDescribeDocument.handleDescribeCache(arg.getDescribeVersionMap(), describe) : null)
                .objectDescribeExt(buildDescribeExt())
                .supportTag(supportTag)
                .layout(LayoutDocument.of(layout))
                .build();
        return result;
    }

    protected ObjectDescribeDocument buildDescribeExt() {
        return new ObjectDescribeDocument();
    }

    private void initLayoutContext() {
        LayoutContext layoutContext = LayoutContext.get();
        layoutContext.setLayoutVersion(LayoutVersion.V3);
        layoutContext.setLayoutAgentType(LayoutAgentType.of(arg.getLayoutAgentType()));
    }

    private void initContextCache() {
        ContextCacheUtil.openContextCache();
        //-10000系统用户不需要初始化
        if (!controllerContext.getUser().isSupperAdmin()) {
            //功能权限
            serviceFacade.queryAndCacheFuncPrivilege(controllerContext.getUser(), controllerContext.getObjectApiName(), getObjectActionCodeMap());
            stopWatch.lap("queryAndCacheFuncPrivilege");
        }
    }

    //业务方自定义的需要校验功能权限的code
    protected Map<String, Set<String>> getCustomObjectActionCodeMap() {
        return Maps.newHashMap();
    }

    //需要初始化的功能权限列表
    private Map<String, Set<String>> getObjectActionCodeMap() {
        Map<String, Set<String>> codeMap = Maps.newHashMap();

        //本对象
//        Set<String> selfActionCodes = Sets.newHashSet(CollectionUtils.nullToEmpty(getFuncPrivilegeCodes()));
//        ComponentActions detailActions = getComponentActionsByApiName(describe.getApiName());
//        selfActionCodes.addAll(detailActions.getActionCodes());
//        if (!arg.isFromRecycleBin()) {
//            selfActionCodes.addAll(ComponentActions.TEAM.getActionCodes());
//        }
//        codeMap.put(describe.getApiName(), selfActionCodes);

        //相关对象
        related.forEach(relatedDescribe -> {
            Set<String> actions = Sets.newHashSet(ObjectAction.VIEW_LIST.getActionCode());
            if (!arg.getFromRecycleBin()) {
                actions.addAll(ComponentActions.RELATED_OBJECT.getActionCodes());
            }
            codeMap.put(relatedDescribe.getApiName(), actions);
        });

        //业务方自定义的需要校验功能权限的code
        Map<String, Set<String>> customObjectActionCodeMap = getCustomObjectActionCodeMap();
        if (CollectionUtils.notEmpty(customObjectActionCodeMap)) {
            customObjectActionCodeMap.forEach((k, v) -> {
                codeMap.putIfAbsent(k, Sets.newHashSet());
                codeMap.get(k).addAll(v);
            });
        }

        return codeMap;
    }

    private IObjectDescribe findObject() {
        return serviceFacade.findObjectIncludeMultiField(controllerContext.getTenantId(), arg.getObjectDescribeApiName());
    }

    private List<IObjectDescribe> findRelatedObjects() {
        List<IFieldDescribe> relatedFields = serviceFacade.findRelatedFields(controllerContext.getTenantId(),
                arg.getObjectDescribeApiName());
        return relatedFields.stream().map(x -> buildSimpleDescribe(x)).collect(Collectors.toList());
    }

    private IObjectDescribe buildSimpleDescribe(IFieldDescribe field) {
        IObjectDescribe describe = new ObjectDescribe();
        describe.setApiName(field.getDescribeApiName());
        describe.setFieldDescribes(Lists.newArrayList(field));
        return describe;
    }

    private void filterRelatedObjects() {
        related = serviceFacade.filterDescribesWithActionCode(controllerContext.getUser(), related, ObjectAction.VIEW_LIST.getActionCode());
        relatedObjects = getRelatedDescribes(related);
        detailObjects = getDetailDescribes(related);
    }

    private List<RelatedObjectDescribeStructure> getRelatedDescribes(List<IObjectDescribe> related) {
        return ObjectDescribeExt.of(describe).getRelatedObjectDescribeStructures(related);
    }

    private List<RelatedObjectDescribeStructure> getDetailDescribes(List<IObjectDescribe> related) {
        return ObjectDescribeExt.of(describe).getDetailObjectDescribeStructures(related);
    }

    @Data
    public static class Arg {
        private String recordType;
        //兼容数据格式
        private String objectDescribeApiName;
        //是否回收站的详情页请求
        private Boolean fromRecycleBin = false;
        private Map<String, Integer> describeVersionMap;
        //返回描述中是否包含计算统计信息
        private Boolean includeStatistics = false;
        private Boolean includeDescribe = false;
        //布局适用端类型(mobile-移动端；web-网页端，不传默认web)
        private String layoutAgentType;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private LayoutDocument layout;
        private ObjectDescribeDocument describe;
        private ObjectDescribeDocument objectDescribeExt;
        private Boolean supportTag;
    }
}
