package com.facishare.paas.appframework.metadata.util;

import org.apache.commons.lang.StringUtils;

import java.util.*;

/**
 * Created by l<PERSON><PERSON> on 2017/7/24.
 */
public class CompactTrie {
    private final Map<Character, Node> nodeMap = new HashMap<Character, Node>();

    private static class Node {
        private final Character c;
        private String postfix;
        private Map<Character, Node> map = new HashMap<Character, Node>();
        private boolean leaf = false;

        private Node(Character ch) {
            this.c = ch;
        }

        @Override
        public String toString() {
            return "Node{" +
                    "c=" + c +
                    ", postfix='" + postfix + '\'' +
                    ", map=" + map +
                    ", leaf=" + leaf +
                    '}';
        }
    }

    private void push(final Node node, final String w, final int index) {
        int len = w.length();
        if (index < len) {
            Character c = w.charAt(index);
            Node next = node.map.get(c);
            if (next == null) {
                next = new Node(c);
                node.map.put(c, next);
            }

            if (index == len - 1) {
                next.leaf = true;
            } else {
                push(next, w, index + 1);
            }
        }
    }

    public CompactTrie(Collection<String> words) {
        for (String w : words) {
            if (StringUtils.isEmpty(w)) {
                continue;
            }
            Character c = w.charAt(0);
            if (!nodeMap.containsKey(c)) {
                nodeMap.put(c, new Node(c));
            }

            Node node = nodeMap.get(c);
            if (w.length() == 1) {
                node.leaf = true;
            } else {
                push(node, w, 1);
            }
        }

        for (Map.Entry<Character, Node> e : nodeMap.entrySet()) {
            trim(e.getValue());
        }
    }

    private void trim(Node node) {
        int childrenNum = node.map.size();
        Node next = null;
        for (Map.Entry<Character, Node> e : node.map.entrySet()) {
            next = e.getValue();
            trim(next);
        }

        if (childrenNum == 1 && next != null && !node.leaf) {
            if (next.postfix == null) {
                node.postfix = String.valueOf(next.c);
            } else {
                node.postfix = next.c + next.postfix;
            }
            node.map = next.map;
            node.leaf = next.leaf;
        }
    }

    /**
     * 找到haystack字符串中包含的所有关键词，用于广告或者敏感词过滤
     *
     * @param haystack 待查找字符串
     * @return 找不到返回空set，否则返回找到的多个字符串
     */
    public Set<String> findAll(String haystack) {
        Set<String> words = new HashSet<String>();
        if (haystack != null) {
            for (int i = 0, len = haystack.length(); i < len; i++) {
                words.addAll(findAll(haystack, i, len));
            }
        }
        return words;
    }

    /**
     * 查找所有匹配的字符串
     *
     * @param str   字符串
     * @param start 开始位置
     * @param stop  结束为止
     * @return 找不到返回空set
     */
    public Set<String> findAll(final String str, final int start, int stop) {
        Set<String> found = new HashSet<String>();
        int i = start;
        Character c = str.charAt(i);
        Node current = nodeMap.get(c);
        if (current == null) {
            return found;
        }
        StringBuilder sb = new StringBuilder();
        for (; ; ) {
            sb.append(c);
            ++i;
            final String postfix = current.postfix;
            if (postfix != null) {
                if (!str.startsWith(postfix, i)) {
                    return found;
                }
                sb.append(postfix);
                i += postfix.length();
            }

            if (current.leaf) {
                found.add(sb.toString());
            }

            if (i >= stop) {
                break;
            }
            c = str.charAt(i);
            current = current.map.get(c);
            if (current == null) {
                return found;
            }
        }

        return found;
    }

    /**
     * 判断haystack中是否包含对应的脏词，并返回第一个匹配的脏词
     *
     * @param haystack 要查找的字符串
     * @return 返回第一个匹配的脏词
     */
    public String contains(String haystack) {
        if (haystack == null) {
            return null;
        }
        for (int i = 0, len = haystack.length(); i < len; i++) {
            String found = startsWith(haystack, i);
            if (found != null) {
                return found;
            }
        }
        return null;
    }

    /**
     * 判断字符串是否以多个字符串中的一个开头
     *
     * @param str 待判断字符串
     * @return 返回第一个匹配的字符串
     */
    public String startsWith(String str) {
        return startsWith(str, 0, str.length());
    }

    /**
     * 判断字符串是否以多个字符串中的一个开头
     *
     * @param str   待判断字符串
     * @param start 开始位置
     * @return 返回第一个匹配的字符串
     */
    public String startsWith(String str, int start) {
        return startsWith(str, start, str.length());
    }

    /**
     * 判断字符串是否以多个字符串中的一个开头
     *
     * @param str   待判断字符串
     * @param start 开始位置
     * @param stop  结束位置
     * @return 返回第一个匹配的字符串
     */
    public String startsWith(final String str, final int start, int stop) {
        int i = start;
        Character c = str.charAt(i);
        Node current = nodeMap.get(c);
        if (current == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (; ; ) {
            sb.append(c);
            ++i;
            final String postfix = current.postfix;
            if (postfix != null) {
                if (!str.startsWith(postfix, i)) {
                    return null;
                }
                sb.append(postfix);
                i += postfix.length();
            }

            if (current.leaf) {
                return sb.toString();
            }

            if (i >= stop) {
                break;
            }
            c = str.charAt(i);
            current = current.map.get(c);
            if (current == null) {
                return null;
            }
        }

        return null;
    }

    /**
     * 根据replaceMap中原始字符串和对应字符串的对应关系，替换haystack字符串中对应的pattern，返回替换后的字符串
     *
     * @param haystack
     * @param replaceMap
     * @return
     */
    public String replaceAll(String haystack, Map<String, String> replaceMap) {
        if (StringUtils.isBlank(haystack)) {
            return "";
        }
        //输入replace后字符串的StringBuilder
        StringBuilder resultBuilder = new StringBuilder(haystack.length());
        for (int i = 0, len = haystack.length(); i < len; ) {
            Character c = haystack.charAt(i);
            Node current = nodeMap.get(c);
            if (current == null) {
                resultBuilder.append(c);
                i++;
                continue;
            }
            StringBuilder sb = new StringBuilder(); //当前往前查找的临时变量
            boolean isMatched = false;
            for (; ; ) {
                sb.append(c);
                ++i;
                final String postfix = current.postfix;
                if (postfix != null) {
                    if (!haystack.startsWith(postfix, i)) {
                        break;
                        //return null;
                    }
                    sb.append(postfix);
                    i += postfix.length();
                }

                if (current.leaf) {
                    //return sb.toString();
                    //match found
                    isMatched = true;
                    break;
                }

                if (i >= len) {
                    break;
                }
                c = haystack.charAt(i);
                current = current.map.get(c);
                if (current == null) {
                    break;
                    //return null;
                }
            }
            String traversedStr = sb.toString();
            if (isMatched && replaceMap.containsKey(traversedStr)) {
                resultBuilder.append(replaceMap.get(traversedStr));
            } else {
                resultBuilder.append(traversedStr);
            }
        }
        return resultBuilder.toString();
    }

}