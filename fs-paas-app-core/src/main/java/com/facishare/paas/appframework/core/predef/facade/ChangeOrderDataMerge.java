package com.facishare.paas.appframework.core.predef.facade;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by zhaooju on 2025/3/11
 */
@Data
public class ChangeOrderDataMerge {
    private IObjectDescribe originalDescribe;
    private Map<String, IObjectDescribe> originalDetailDescribeMap;
    private IObjectData originalMasterData;
    private Map<String, List<IObjectData>> originalDetailsDataMap;


    private IObjectData mappingMasterObjectData;
    private Map<String, List<IObjectData>> mappingDetailsToAdd;
    private Map<String, List<IObjectData>> mappingDetailsToUpdate;
    private Map<String, List<IObjectData>> mappingDetailsToDelete;
    private IObjectData masterSnapshot;
    private Map<String, List<IObjectData>> detailsSnapshot;

    @Builder
    public ChangeOrderDataMerge(IObjectDescribe originalDescribe, Map<String, IObjectDescribe> originalDetailDescribeMap,
                                IObjectData originalMasterData, Map<String, List<IObjectData>> originalDetailsDataMap,
                                ObjectDataMapping.Result mappingResult) {
        this.originalDescribe = originalDescribe;
        this.originalDetailDescribeMap = originalDetailDescribeMap;
        this.originalMasterData = originalMasterData;
        this.originalDetailsDataMap = originalDetailsDataMap;
        this.mappingMasterObjectData = mappingResult.getMasterObjectData();
        this.mappingDetailsToAdd = CollectionUtils.nullToEmpty(mappingResult.getDetailsToAdd());
        this.mappingDetailsToUpdate = CollectionUtils.nullToEmpty(mappingResult.getDetailsToUpdate());
        this.mappingDetailsToDelete = CollectionUtils.nullToEmpty(mappingResult.getDetailsToDelete());
        this.masterSnapshot = mappingResult.getMasterSnapshot();
        this.detailsSnapshot = CollectionUtils.nullToEmpty(mappingResult.getDetailsSnapshot());
    }

    public MergeResult merge() {
        MergeAndDiffResult masterMergeResult = mergeUpdateData(originalDescribe, originalMasterData, mappingMasterObjectData, masterSnapshot);

        Map<String, List<IObjectData>> mergedDetailsDataMap = Maps.newHashMap();
        Map<String, List<IObjectData>> modifyDataMaps = Maps.newHashMap();
        Map<String, List<IObjectData>> addDataMaps = Maps.newHashMap();
        Map<String, List<IObjectData>> deleteDataMaps = Maps.newHashMap();

        for (IObjectDescribe originalDetailDescribe : originalDetailDescribeMap.values()) {
            String describeApiName = originalDetailDescribe.getApiName();
            List<IObjectData> dbDetailList = originalDetailsDataMap.getOrDefault(describeApiName, Lists.newArrayList());
            List<IObjectData> updateList = mappingDetailsToUpdate.getOrDefault(describeApiName, Lists.newArrayList());
            List<IObjectData> addList = mappingDetailsToAdd.getOrDefault(describeApiName, Lists.newArrayList());
            List<IObjectData> deleteList = mappingDetailsToDelete.getOrDefault(describeApiName, Lists.newArrayList());
            List<IObjectData> snapshotList = detailsSnapshot.getOrDefault(describeApiName, Lists.newArrayList());

            // 将数据库数据转换为Map，方便查找
            Map<String, IObjectData> dbDetailMap = dbDetailList.stream()
                    .collect(Collectors.toMap(IObjectData::getId, Function.identity()));
            Map<String, IObjectData> snapshotMap = snapshotList.stream()
                    .collect(Collectors.toMap(IObjectData::getId, Function.identity()));

            List<IObjectData> mergedList = Lists.newArrayList();
            List<IObjectData> modifyDataList = Lists.newArrayList();
            List<IObjectData> addDataList = Lists.newArrayList();
            List<IObjectData> deleteDataList = Lists.newArrayList();

            // 处理更新的数据
            for (IObjectData updateData : updateList) {
                String id = updateData.getId();
                IObjectData dbData = dbDetailMap.get(id);
                IObjectData snapshotData = snapshotMap.get(updateData.get(ObjectDataExt.CHANGED_DATA_ID));
                // 如果数据库中不存在该数据，则表示是新增的数据
                if (Objects.isNull(dbData)) {
                    IObjectData mergedData = ObjectDataExt.of(snapshotData).copy();
                    ObjectDataExt objectDataExt = ObjectDataExt.of(mergedData);
                    objectDataExt.merge(updateData);
                    deleteDataList.add(mergedData);
                    continue;
                }
                MergeAndDiffResult mergeAndDiffResult = mergeUpdateData(originalDetailDescribe, dbData, updateData, snapshotData);
                mergedList.add(mergeAndDiffResult.getObjectData());
                modifyDataList.add(mergeAndDiffResult.getChangedFieldsData());
            }

            // 处理新增的数据
            for (IObjectData addData : addList) {
                ObjectDataExt.of(addData).fillTemporaryId();
                mergedList.add(addData);
                addDataList.add(addData);
            }

            // 处理未被更新或删除的数据库数据
            deleteDataList.addAll(deleteList);
            Set<String> deleteIds = Stream.of(deleteDataList, mergedList)
                    .flatMap(List::stream)
                    .map(IObjectData::getId)
                    .collect(Collectors.toSet());
            dbDetailMap.values().stream()
                    .filter(data -> !deleteIds.contains(data.getId()))
                    .forEach(mergedList::add);

            if (CollectionUtils.notEmpty(mergedList)) {
                mergedDetailsDataMap.put(describeApiName, mergedList);
            }
            if (CollectionUtils.notEmpty(modifyDataList)) {
                modifyDataMaps.put(describeApiName, modifyDataList);
            }
            if (CollectionUtils.notEmpty(addDataList)) {
                addDataMaps.put(describeApiName, addDataList);
            }
            if (CollectionUtils.notEmpty(deleteList)) {
                deleteDataMaps.put(describeApiName, deleteList);
            }
        }

        return MergeResult.builder()
                .masterData(masterMergeResult.getObjectData())
                .masterWithOnlyChangedFields(masterMergeResult.getChangedFieldsData())
                .detailDataMap(mergedDetailsDataMap)
                .detailModifyDataMap(modifyDataMaps)
                .detailAddDataMap(addDataMaps)
                .detailDeleteDataMap(deleteDataMaps)
                .build();
    }

    /**
     * 合并待更新的数据
     * 该方法用于将变更单数据与原始数据进行合并，处理数据冲突
     *
     * @param describe     对象描述信息，包含字段定义等元数据
     * @param dbData       数据库中的原始数据
     * @param updateData   要更新的变更单数据
     * @param snapshotData 变更单创建时的数据快照
     * @return 合并结果，包含合并后的完整数据和只有变更字段的数据
     */
    private MergeAndDiffResult mergeUpdateData(IObjectDescribe describe, IObjectData dbData, IObjectData updateData, IObjectData snapshotData) {
        // 创建一个新的对象来存储合并结果，基于数据库当前数据
        ObjectDataExt objectDataExt = ObjectDataExt.of(dbData);
        IObjectData mergedData = objectDataExt.copy();

        // 创建只包含变更字段的数据
        IObjectData changedData = new ObjectData();
        changedData.setDescribeApiName(dbData.getDescribeApiName());
        changedData.setTenantId(dbData.getTenantId());
        changedData.setId(dbData.getId());

        // 比较快照数据和当前数据库数据的差异
        // 这一步是为了找出在变更单处理期间，数据库中发生变化的字段
        Map<String, Object> diff = ObjectDataExt.of(snapshotData).diff(dbData, describe);

        // 获取更新数据中的所有字段
        Map<String, Object> updateFields = ObjectDataExt.of(updateData).toMap();

        // 将更新数据中的字段合并到结果中
        // 但会忽略在变更单处理期间被其他操作修改过的字段(diff中的字段)
        updateFields.forEach((key, value) -> {
            if (!diff.containsKey(key)) {
                mergedData.set(key, value);
                changedData.set(key, value);
            }
        });

        String temporaryId = objectDataExt.getTemporaryId();
        if (!Strings.isNullOrEmpty(temporaryId)) {
            ObjectDataExt.of(mergedData).setTemporaryId(temporaryId);
            ObjectDataExt.of(changedData).setTemporaryId(temporaryId);
        }

        return MergeAndDiffResult.builder()
                .objectData(mergedData)
                .changedFieldsData(changedData)
                .build();
    }

    @Data
    @Builder
    public static class MergeAndDiffResult {
        private IObjectData objectData;
        private IObjectData changedFieldsData;
    }


    @Data
    @Builder
    public static class MergeResult {
        IObjectData masterData;                                             // 主对象数据
        IObjectData masterWithOnlyChangedFields;

        Map<String, List<IObjectData>> detailDataMap;                       // 从对象数据
        Map<String, List<IObjectData>> detailModifyDataMap;
        Map<String, List<IObjectData>> detailAddDataMap;
        Map<String, List<IObjectData>> detailDeleteDataMap;
    }
}
