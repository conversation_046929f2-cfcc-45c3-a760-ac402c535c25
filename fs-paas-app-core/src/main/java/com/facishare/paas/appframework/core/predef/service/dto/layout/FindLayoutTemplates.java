package com.facishare.paas.appframework.core.predef.service.dto.layout;

import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * Created by zhaooju on 2023/3/2
 */
public interface FindLayoutTemplates {

    @Data
    class Arg {
        private String describeApiName;
        private String business;
        private Integer cardStyle;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        private List<DocumentBaseEntity> templates;
    }
}
