package com.facishare.paas.appframework.core.predef.service;

import com.facishare.function.exception.FunctionCompileException;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.*;
import com.facishare.paas.appframework.common.service.dto.*;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.function.*;
import com.facishare.paas.appframework.core.predef.service.dto.function.SaveFunctionRelation.ReferenceInfo;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.function.FunctionVSCodeExt;
import com.facishare.paas.appframework.function.SecurityChecker;
import com.facishare.paas.appframework.function.UdefFunctionExt;
import com.facishare.paas.appframework.function.dto.Analyze;
import com.facishare.paas.appframework.function.dto.DebugFunction;
import com.facishare.paas.appframework.function.dto.DeveloperVerificationResult;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.QuoteValueService;
import com.facishare.paas.appframework.metadata.cache.RedisDao;
import com.facishare.paas.appframework.metadata.relation.SourceTypes;
import com.facishare.paas.appframework.metadata.relation.TargetTypes;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.PrivilegeResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.common.MapUtils;
import com.github.trace.TraceContext;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.net.SocketTimeoutException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.appframework.common.service.PhoneNumberService.LOGIN_DYNAMIC_PASSWORD;
import static com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode.VALIDATION_ERROR;


/**
 * Created by liyiguang on 2018/2/3.
 */

@ServiceModule("function")
@Service
@Slf4j
public class ObjectFunctionService {

    @Autowired
    private FunctionLogicService functionLogicService;

    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    @Qualifier("functionPrivilegeService")
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private SecurityChecker securityChecker;
    @Autowired
    private QuoteValueService quoteValueService;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private ReferenceServiceProxy referenceServiceProxy;

    @Autowired
    private RedisDao redisDao;

    private AppCaptchaService appCaptchaService;
    
    private SmsCodeService smsCodeService;

    @Autowired
    public void setCaptchaService(AppCaptchaService appCaptchaService) {
        this.appCaptchaService = appCaptchaService;
    }

    @Autowired
    public void setSmsCodeService(SmsCodeService smsCodeService) {
        this.smsCodeService = smsCodeService;
    }

    @ServiceMethod("create")
    public CreateFunction.Result create(CreateFunction.Arg arg, ServiceContext context) {
        IUdefFunction function = functionLogicService.createFunctionByRest(context.getUser(), arg.getFunction().toUDefFunction());
        return CreateFunction.Result.builder().function(FunctionInfo.of(function)).build();
    }

    @ServiceMethod("update")
    public UpdateFunction.Result update(UpdateFunction.Arg arg, ServiceContext context) {
        IUdefFunction function = functionLogicService.updateFunctionByRest(context.getUser(), arg.getFunction().toUDefFunction());
        return UpdateFunction.Result.builder().function(FunctionInfo.of(function)).build();
    }


    private String generateCacheKey(String tenantId) {
        return "FunctionClass_" + tenantId;
    }

    private void delFunctionClassCache(String tenantId) {
        int count = 4;
        while (--count > 0) {
            try {
                redisDao.delKey(generateCacheKey(tenantId));
                return;
            } catch (Exception e) {
                log.error("delFunctionClassCache error:{}", e.getMessage(), e);
            }
        }
    }


    private void checkFunctionStatus(User user, String bindingObjectApiName, String status, Map usedInfo) {
        if (StringUtils.isEmpty(status) || Objects.equals(status, UdefFunctionExt.NOT_USED)) {
            return;
        }

        if (MapUtils.isNullOrEmpty(usedInfo)) {
            return;
        }
    }

    @ServiceMethod("find")
    public FindFunction.Result find(FindFunction.Arg arg, ServiceContext context) {
        IUdefFunction function = functionLogicService.findUDefFunction(
                context.getUser(), arg.getApiName(), arg.getBindingObjectAPIName());

        FindFunction.Result ret = FindFunction.Result.builder().function(FunctionInfo.of(function)).build();
        return ret;
    }

    @ServiceMethod("compileCheck")
    public CompileCheck.Result compileCheck(CompileCheck.Arg arg, ServiceContext context) {
        IUdefFunction uDefFunction = arg.getFunction().toUDefFunction();
        uDefFunction.setTenantId(context.getTenantId());
        CompileCheck.Result ret = CompileCheck.Result.builder().success(true).build();
        try {
            functionLogicService.compile(context.getUser(), uDefFunction);
        } catch (FunctionCompileException e) {
            ret.setSuccess(false);
            ret.setCompileError(e.getMessage());
        }
        return ret;
    }

    @ServiceMethod("analyze")
    public Analyze.Result analyze(AnalyzeFunction.Arg arg, ServiceContext context) {
        IUdefFunction uDefFunction = arg.getFunction().toUDefFunction();
        uDefFunction.setTenantId(context.getTenantId());
        Analyze.Result result = functionLogicService.analyze(context.getUser(), uDefFunction);
        result.setLogInfo(TraceContext.get().getTraceId());
        return result;
    }

    @ServiceMethod("query")
    public QueryFunction.Result query(QueryFunction.Arg arg, ServiceContext context) {
        Map<String, Object> map = Maps.newHashMap();
        String functionName = Strings.isNullOrEmpty(arg.getFunctionName()) ? null : arg.getFunctionName();
        map.put(IUdefFunction.FUNCTION_NAME, functionName);
        List<String> nameSpace = CollectionUtils.empty(arg.getNameSpace()) ? null : arg.getNameSpace();
        map.put(IUdefFunction.NAME_SPACE, nameSpace);
        if (StringUtils.isNotEmpty(arg.getBindingObjectAPIName())) {
            map.put(IUdefFunction.BINDING_OBJECT_API_NAME, arg.getBindingObjectAPIName());
        }
        Integer limit = Integer.parseInt(arg.getPageSize());
        Integer offset = (Integer.parseInt(arg.getPageNumber()) - 1) * limit;
        map.put("limit", limit);
        map.put("offset", offset);
        String returnType = Strings.isNullOrEmpty(arg.getReturnType()) ? null : arg.getReturnType();
        map.put(IUdefFunction.RETURN_TYPE, returnType);
        if (CollectionUtils.notEmpty(arg.getReturnTypeList())) {
            map.remove(IUdefFunction.RETURN_TYPE);
            map.put(IUdefFunction.RETURN_TYPE_LIST, arg.getReturnTypeList());
        }
        Boolean isActive = Objects.isNull(arg.getIsActive()) ? null : arg.getIsActive();
        map.put(IUdefFunction.IS_ACTIVE, isActive);
        QueryResult result = functionLogicService.queryUDefFunction(context.getUser(), map);
        List<IUdefFunction> functions = result.getData();
        List<String> bingObjectApiNameList = Lists.newArrayList();
        functions.forEach(x -> bingObjectApiNameList.add(x.getBindingObjectApiName()));
        Multimap<String, ReferenceData> refMap = queryReferences(context.getUser(),
                functions.stream().map(x -> x.getApiName()).collect(Collectors.toList()));

        Map<String, IObjectDescribe> bindingObjectDescribeMap = describeLogicService.findObjects(context.getTenantId(), bingObjectApiNameList);
        functions.forEach(x -> {
            IObjectDescribe objectDescribe = Objects.isNull(bindingObjectDescribeMap
                    .get(x.getBindingObjectApiName())) ? null : bindingObjectDescribeMap.get(x.getBindingObjectApiName());
            if (Objects.nonNull(objectDescribe)) {
                x.setBindingObjectLabel(objectDescribe.getDisplayName());
            }
            if (!refMap.isEmpty() && CollectionUtils.notEmpty(refMap.get(x.getApiName()))) {
                List<ReferenceData> refList = (List<ReferenceData>) refMap.get(x.getApiName());
                String labels = mergeLabels(x, refList);
                Map info = ImmutableMap.of("api_name", refList.get(0).getSourceValue(), "label", labels, "type", x.getNameSpace());
                x.setUsedInfo(info);
            }

            if (CollectionUtils.empty(x.getUsedInfo())
                    || Objects.isNull(x.getUsedInfo().get("label"))
                    || Strings.isNullOrEmpty(x.getUsedInfo().get("label").toString())) {
                x.setStatus(IUdefFunction.NOT_USED);
            } else {
                x.setStatus(IUdefFunction.USED);
            }
        });
        QueryFunction.Result ret = QueryFunction.Result.builder()
                .totalNumber(result.getTotalNumber())
                .function(FunctionInfo.of(functions))
                .build();

        return ret;
    }

    private String mergeLabels(IUdefFunction udefFunction, List<ReferenceData> refList) {
        Map usedInfo = udefFunction.getUsedInfo();
        if (CollectionUtils.empty(usedInfo)) {
            Stream<String> sourceLabelStream = refList.stream()
                    .filter(ref -> Objects.nonNull(ref))
                    .map(t -> Strings.isNullOrEmpty(t.getSourceLabel()) ? StringUtils.EMPTY : t.getSourceLabel());
            if (SourceTypes.UI_EVENT.equals(udefFunction.getNameSpace())) {
                return Joiner.on(",").join(sourceLabelStream.distinct().collect(Collectors.toList()));
            }
            return Joiner.on(",").join(sourceLabelStream.collect(Collectors.toList()));
        }
        Object labelObj = usedInfo.get("label");
        String label = Objects.isNull(labelObj) ? StringUtils.EMPTY : labelObj.toString();
        if (CollectionUtils.empty(refList)) {
            return label;
        }
        Stream<String> sourceLabelStream = refList.stream().
                map(t -> t.getSourceLabel()).filter(t -> !t.equals(label));
        if (SourceTypes.UI_EVENT.equals(udefFunction.getNameSpace())) {
            sourceLabelStream = sourceLabelStream.distinct();
        }
        String mergeLabels = Joiner.on(",").join(sourceLabelStream.collect(Collectors.toList()));
        StringBuilder ret = new StringBuilder(label);
        return ret.append(",").append(mergeLabels).toString();
    }

    private Multimap<String, ReferenceData> queryReferences(User user, List<String> targetValues) {
        Multimap<String, ReferenceData> multiMap = ArrayListMultimap.create();
        if (CollectionUtils.empty(targetValues)) {
            return multiMap;
        }
        QueryByTargetList.Arg arg = QueryByTargetList.Arg.builder()
                .targetType(TargetTypes.FUNCTION)
                .targetValues(targetValues)
                .limit(200).build();
        QueryByTargetList.Result result = referenceServiceProxy.queryByTargetList(user.getTenantId(), arg);
        CollectionUtils.nullToEmpty(result.getValues()).stream().forEach(x -> multiMap.put(x.getTargetValue(), x));
        return multiMap;
    }

    @ServiceMethod("isActive")
    public ActiveRule.Result isActive(ActiveRule.Arg arg, ServiceContext context) {
        Boolean result = functionLogicService.setIsActive(context.getUser(), arg.getBindingObjectAPIName(), arg.getApiName(), arg.getIsActive());
        return new ActiveRule.Result(result);
    }

    @ServiceMethod("funExist")
    public FunctionExist.Result funExist(FunctionExist.Arg arg, ServiceContext context) {
        List<String> nameSpace = CollectionUtils.empty(arg.getNameSpace()) ? null : arg.getNameSpace();
        List<String> returnTypeList = null;
        if (!CollectionUtils.empty(arg.getReturnTypeList())) {
            returnTypeList = arg.getReturnTypeList();
        } else if (StringUtils.isEmpty(arg.getReturnType())) {
            returnTypeList = Arrays.asList(arg.getReturnType());
        }
        String bindingObjectApiName = Strings.isNullOrEmpty(arg.getBindingObjectApiName()) ? null : arg.getBindingObjectApiName();
        Boolean result = functionLogicService.funcIsExist(context.getUser(), nameSpace, returnTypeList, bindingObjectApiName);
        FunctionExist.Result ret = FunctionExist.Result.builder().isExist(result).build();

        return ret;
    }

    @ServiceMethod("delete")
    public DeleteFunction.Result delete(DeleteFunction.Arg arg, ServiceContext context) {
        Boolean result = functionLogicService.deleteUDefFunction(context.getUser(), arg.getBindingObjectAPIName(), arg.getApiName());
        return new DeleteFunction.Result(result);
    }

    @ServiceMethod("run")
    public RunFunction.Result runFunction(RunFunction.Arg arg, ServiceContext context) {
        log.debug("runFunction arg:{} context:{}", arg, context);

        try {
            RunResult runResult = functionLogicService.executeUDefIdempotent(context.getRequestContext(), arg.getApiName(),
                    arg.getBindingObjectAPIName(),
                    arg.getParameters() == null ? Lists.newArrayList() : arg.getParameters(),
                    arg.getBindingObjectDataId(),
                    arg.getObjectIds());


            if (runResult == null) {
                return RunFunction.Result.builder()
                        .status(RunFunction.Result.RUNNING)
                        .success(true).build();

            }

            return RunFunction.Result.builder()
                    .status(RunFunction.Result.END)
                    .success(runResult.isSuccess())
                    .functionResult(runResult.getFunctionResult())
                    .errorInfo(runResult.getErrorInfo())
                    .build();
        } catch (Exception e) {
            log.error("runFunction failed", e);
            return RunFunction.Result.builder()
                    .status(RunFunction.Result.END)
                    .success(false)
                    .errorInfo(e.getMessage())
                    .build();
        }
    }


    @ServiceMethod("controller")
    public RunFunction.Result controller(Controller.Arg arg, ServiceContext context) {
        try {
            RunResult runResult = functionLogicService.executeFunctionController(context.getRequestContext(), arg.getApiName(),
                    arg.getParameters() == null ? Lists.newArrayList() : arg.getParameters());

            return RunFunction.Result.builder()
                    .status(RunFunction.Result.END)
                    .success(runResult.isSuccess())
                    .functionResult(runResult.getFunctionResult())
                    .errorInfo(runResult.getErrorInfo())
                    .build();
        } catch (Exception e) {
            //超时
            if (e.getCause() instanceof SocketTimeoutException) {
                log.warn("runFunction failed", e);
                return RunFunction.Result.builder()
                        .status(RunFunction.Result.END)
                        .success(false)
                        .errorInfo(I18N.text(I18NKey.FUNC_TIMEOUT))
                        .build();
            }
            log.warn("runFunction failed", e);
            return RunFunction.Result.builder()
                    .status(RunFunction.Result.END)
                    .success(false)
                    .errorInfo(e.getMessage())
                    .build();
        }
    }

    @ServiceMethod("promotionRunFunction")
    public PromotionRunFunction.Result promotionRunFunction(PromotionRunFunction.Arg arg, ServiceContext context) {
        PromotionRunFunction.Result.ResultBuilder resultBuilder = PromotionRunFunction.Result.builder();
        try {
            IUdefFunction udefFunction = functionLogicService.findUDefFunction(
                    context.getUser(), arg.getApiName(), arg.getBindingObjectAPIName());
            if (udefFunction == null) {
                return resultBuilder.errorInfo("findUDefFunction error:result is null").success(false).build();
            }

            Map<String, List<Map<String, Object>>> argDetails = arg.getDetails();
            Map<String, List<IObjectData>> details = new HashMap<>(argDetails.size());
            argDetails.forEach((key, value) -> {
                details.put(key, value.stream()
                        .map(map -> ObjectDataExt.of(map).getObjectData())
                        .collect(Collectors.toList()));
            });
            IObjectData objectData = ObjectDataExt.of(arg.getObjectData()).getObjectData();
            quoteValueService.fillQuoteValueVirtualField(context.getUser(), objectData, details);

            RunResult result = functionLogicService.executeUDefFunction(context.getRequestContext().getUser(), udefFunction, Maps.newHashMap(),
                    objectData, details);

            return resultBuilder.success(true).functionResult(result.getFunctionResult()).build();
        } catch (Exception e) {
            log.warn("promotion run function error!", e);
            return resultBuilder.success(false)
                    .errorInfo(e.getMessage())
                    .build();
        }

    }

    @ServiceMethod("debugFunction")
    public DebugRunFunction.Result debugFunction(DebugRunFunction.Arg arg, ServiceContext context) {
        Map<String, Object> parameters = Maps.newHashMap();
        IUdefFunction uDefFunction = arg.getFunction().toUDefFunction();
        uDefFunction.setTenantId(context.getTenantId());

        arg.getInputData().forEach(x -> parameters.put(x.getName(), x.getValue()));

        String token = getToken(arg, context);
        DebugFunction.Result runResult = functionLogicService.debugRunUDefFunction(context.getUser(), uDefFunction, parameters, arg.getDataSource(), token);
        if (runResult == null) {
            return DebugRunFunction.Result.builder()
                    .success(false)
                    .token(token)
                    .build();

        }
        String error = runResult.getCompileError() != null ? runResult.getCompileError() : runResult.getRuntimeError();
        DebugRunFunction.Result ret = DebugRunFunction.Result.builder()
                .runResult(runResult.getResult())
                .success(runResult.isSuccess())
                .logInfo(runResult.getLog())
                .error(error)
                .build();

        return ret;
    }

    @ServiceMethod("checkFunctionCountLimit")
    public CheckFunctionCountLimit.Result checkFunctionCountLimit(ServiceContext context) {
        functionLogicService.checkFunctionCountLimit(context.getUser());
        return CheckFunctionCountLimit.Result.builder().build();
    }

    @ServiceMethod("checkObjectActionsPrivilege")
    // 获取登录人对对象 actions 权限
    public Object checkObjectActionsPrivilege(CheckObjectActionsPrivilege.Arg arg, ServiceContext context) {
        Map<String, Boolean> result;
        try {
            result = functionPrivilegeService.funPrivilegeCheck(context.getUser(), arg.getApiName(), arg.getActionCodes());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return result;
    }

    @ServiceMethod("batchObjectActionCodesPrivilegeCheck")
    public Object batchObjectActionCodesPrivilegeCheck(BatchObjectActionCodesPrivilegeCheck.Arg arg) {
        Map<String, List<String>> apiName2ActionCodes = arg.getApiName2ActionCodes();
        if (apiName2ActionCodes == null) {
            return PrivilegeResult.ofFail(VALIDATION_ERROR.getCode(), "apiName2ActionCodes is null");
        }
        return functionPrivilegeService.batchObjectFuncPrivilegeCheck(RequestContextManager.getContext(), apiName2ActionCodes);
    }

    @ServiceMethod("batchFuncCodePrivilegeCheck")
    public Object batchFuncCodePrivilegeCheck(BatchFuncCodePrivilegeCheck.Arg arg) {
        if (CollectionUtils.notEmpty(arg.getFuncCodes())) {
            return functionPrivilegeService.batchFuncCodePrivilegeCheck(RequestContextManager.getContext().getUser(), arg.getFuncCodes());
        } else {
            return new Object();
        }
    }

    @ServiceMethod("batchFunPrivilegeCheck")
    public Object batchFunPrivilegeCheck(BatchFunPrivilegeCheck.Arg arg) {
        if (CollectionUtils.notEmpty(arg.getActionCodes())) {
            return functionPrivilegeService.batchFunPrivilegeCheck(RequestContextManager.getContext().getUser(), arg.getObjectApiNames(), arg.getActionCodes());
        } else {
            return new Object();
        }
    }

    /**
     * 检查是否需要发送手机验证码
     * 编辑函数需要校验函数开发者
     * 查看个人函数模版需要验证码开发者
     * @param context serviceContext
     * @return 是否和手机号
     */
    @ServiceMethod("checkFunctionDevelopVerification")
    public DeveloperVerification.Result checkFunctionDevelopVerification(ServiceContext context) {
        RequestContext requestContext = context.getRequestContext();
        if (Objects.isNull(requestContext) || StringUtils.isEmpty(requestContext.getSessionId())) {
            log.warn("requestContext or sessionId is empty");
            return DeveloperVerification.Result.builder().needVerification(true).build();
        }

        DeveloperVerificationResult verification = functionLogicService.checkDeveloperVerification(context.getUser(), requestContext.getSessionId());
        return DeveloperVerification.Result.builder().needVerification(verification.isNeedVerification()).phone(verification.getPhone()).build();
    }

    /**
     * 验证手机号和验证码是否通过
     * 编辑函数需要校验函数开发者
     * @param arg arg
     * @param context serviceContext
     * @return 验证结果
     */
    @ServiceMethod("checkFunctionDevelopVerificationCode")
    public CheckVerificationCode.Result checkFunctionDevelopVerificationCode(CheckVerificationCode.Arg arg, ServiceContext context) {
        String result = functionLogicService.checkFunctionDevelopVerificationCode(context.getUser(), arg.getAreaCode(), arg.getPhone(), arg.getSmsCode());
        switch (result) {
            case "SUCCESS":
                return CheckVerificationCode.Result.builder()
                        .success(true)
                        .build();
            case "SMS_CODE_ERROR":
                return CheckVerificationCode.Result.builder()
                        .success(false)
                        .errorMessage(I18N.text(I18NKey.MOBILE_SMS_CODE_ERROR))
                        .build();
            case "VERIFY_CODE_EXCEED_LIMIT":
                return CheckVerificationCode.Result.builder()
                        .success(false)
                        .errorMessage(I18N.text(I18NKey.MOBILE_VERIFY_CODE_EXCEED_LIMIT))
                        .build();
            case "ERROR":
            default:
                log.warn("checkVerificationCode error tenantId:{} , result:{}", context.getTenantId(), result);
                return CheckVerificationCode.Result.builder()
                        .success(false)
                        .errorMessage(I18N.text(I18NKey.SYSTEM_EXCEPTION))
                        .build();
        }
    }

    /**
     * 是否开启函数认证接口
     * 编辑函数需要校验函数开发者
     * @param context serviceContext
     * @return 是否开启
     */
    @ServiceMethod("isEnableFunctionDevelopVerification")
    public IsEnableFunctionDevelopVerification.Result isEnableFunctionDevelopVerification(ServiceContext context) {
        boolean enable = functionLogicService.isEnableFunctionDevelopVerification(context.getUser());
        return new IsEnableFunctionDevelopVerification.Result(enable);
    }

    /**
     * 开启函数认证校验，只给 fs
     * 编辑函数需要校验函数开发者
     * @param arg arg
     * @param context serviceContext
     * @return 开启
     */
    @ServiceMethod("enableDeveloperVerification")
    public EnableDeveloperVerification.Result enableDeveloperVerification(EnableDeveloperVerification.Arg arg, ServiceContext context) {
        functionLogicService.enableDeveloperVerification(context.getUser(), arg.getTenantId());
        return new EnableDeveloperVerification.Result();
    }
    /**
     * 关闭函数认证校验，只给 fs
     * 编辑函数需要校验函数开发者
     * @param arg arg
     * @param context serviceContext
     * @return 关闭
     */
    @ServiceMethod("disableDeveloperVerification")
    public DisableDeveloperVerification.Result disableDeveloperVerification(DisableDeveloperVerification.Arg arg, ServiceContext context) {
        functionLogicService.disableDeveloperVerification(context.getUser(), arg.getTenantId());
        return new DisableDeveloperVerification.Result();
    }

    /**
     * 发送验证码
     * 编辑函数需要校验函数开发者
     * 查看个人函数模版需要验证码开发者
     * action: LoginDynamicPassword
     * @param arg arg
     * @param context serviceContext
     * @return 发送结果
     */
    @ServiceMethod("sendFunctionDevelopValidateCode")
    public SendFunctionDevelopValidateCode.Result sendFunctionDevelopValidateCode(SendFunctionDevelopValidateCode.Arg arg, ServiceContext context) {
        boolean allowDeveloper = securityChecker.isAllowDeveloper(arg.getPhone());
        if (!allowDeveloper) {
            return SendFunctionDevelopValidateCode.Result.builder()
                    .errorCode(QueryVerificationCode.Result.ERROR)
                    .errorMessage(I18N.text(I18NKey.FUNCTION_DEVELOPER_NOT_ALLOW))
                    .build();
        }

        String sendResult = smsCodeService.sendSmsCode(context.getEa(), context.getUser(), context.getClientIp(), arg.getAreaCode(),
                arg.getPhone(), arg.getCaptchaCode(), arg.getCaptchaId(), LOGIN_DYNAMIC_PASSWORD, I18NKey.DEFAULT_SM_LOGIN_I18N_KEY);

        switch (sendResult) {
            case "SUCCESS":
                return SendFunctionDevelopValidateCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.SUCCESS)
                        .build();
            case "NEED_IMAGE_CODE":
                return SendFunctionDevelopValidateCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.NEED_CAPTCHA)
                        .errorMessage(I18N.text(I18NKey.MOBILE_NEED_IMAGE_CODE))
                        .build();
            case "IMAGE_CODE_ERROR":
                return SendFunctionDevelopValidateCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.NEED_CAPTCHA)
                        .errorMessage(I18N.text(I18NKey.MOBILE_IMAGE_CODE_ERROR))
                        .build();
            case "IP_LIMITED":
                return SendFunctionDevelopValidateCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.ERROR)
                        .errorMessage(I18N.text(I18NKey.MOBILE_IP_LIMITED))
                        .build();
            case "SMS_SEND_ERROR":
                return SendFunctionDevelopValidateCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.ERROR)
                        .errorMessage(I18N.text(I18NKey.MOBILE_SMS_SEND_ERROR))
                        .build();
            case "SMS_TOO_OFTEN":
                return SendFunctionDevelopValidateCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.ERROR)
                        .errorMessage(I18N.text(I18NKey.MOBILE_SMS_TOO_OFTEN))
                        .build();
            case "AREACODE_NOT_SUPPORT":
                log.error("sendFunctionDevelopValidateCode AREACODE_NOT_SUPPORT");
                return SendFunctionDevelopValidateCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.ERROR)
                        .errorMessage(I18N.text(I18NKey.MOBILE_AREACODE_NOT_SUPPORT))
                        .build();
            case "Business_NOT_SUPPORT":
                log.error("sendFunctionDevelopValidateCode Business_NOT_SUPPORT");
                return SendFunctionDevelopValidateCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.ERROR)
                        .build();
            case "ERROR":
                log.error("sendFunctionDevelopValidateCode return ERROR");
                return SendFunctionDevelopValidateCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.SUCCESS)
                        .build();
            default:
                log.warn("queryVerificationCode error tenantId :{} , result :{}", context.getTenantId(), sendResult);
                return SendFunctionDevelopValidateCode.Result.builder()
                        .errorCode(QueryVerificationCode.Result.ERROR)
                        .errorMessage(I18N.text(I18NKey.SYSTEM_EXCEPTION))
                        .build();
        }
    }


    /**
     * 发送短信验证码前需要，获取图形验证码进行验证
     * 编辑函数需要校验函数开发者
     * 查看个人函数模版需要验证码开发者
     * @return base64
     */
    @ServiceMethod("getImageCode")
    public Captcha.Result getImageCode() {
        return appCaptchaService.createCaptchaCode(null);
    }

    /**
     * 验证图形验证码，以获得发送短信验证码的资格
     * 编辑函数需要校验函数开发者
     * 查看个人函数模版需要验证码开发者
     * @param arg arg 根据图形录入验证码
     * @param context serviceContext
     * @return 验证结果
     */
    @ServiceMethod("verifyCaptcha")
    public CaptchaVerify.Result verifyCaptcha(CaptchaVerify.Arg arg, ServiceContext context) {
        return appCaptchaService.verifyCaptchaCode(context.getUser(), arg.getCaptchaCode(), arg.getCaptchaId());
    }

    @ServiceMethod("findFunctionVersion")
    public FindFunctionVersion.Result findFunctionVersion(FindFunctionVersion.Arg arg, ServiceContext context) {
        IUdefFunction function = functionLogicService.findFunctionByVersion(
                context.getUser(), arg.getApiName(), arg.getBindingObjectAPIName(), arg.getVersion());

        FindFunctionVersion.Result ret = FindFunctionVersion.Result.builder().function(FunctionInfo.of(function)).build();
        return ret;
    }

    @ServiceMethod("queryVersions")
    public QueryVersions.Result queryVersions(QueryVersions.Arg arg, ServiceContext context) {
        List<IUdefFunction> functionList = functionLogicService.queryFunctionVersions(
                context.getUser(), arg.getApiName(), arg.getBindingObjectAPIName(), arg.getVersions(),
                arg.getUpdateUserId(), arg.getBeginTime(), arg.getEndTime(), arg.getLimit());

        Set<String> userSet = Sets.newHashSet();
        for (IUdefFunction function : functionList) {
            String userId = function.getLastModifiedBy() != null ? function.getLastModifiedBy() : function.getCreatedBy();
            function.setLastModifiedBy(userId);
            userSet.add(userId);
        }
        List<EmployeeDto> employeeList = employeeService.batchGetUserInfo(context.getTenantId(), userSet);
        Map<String, String> employeeMap = employeeList.stream().collect(Collectors.toMap(x -> String.valueOf(x.getEmployeeId()), x -> x.getName()));
        QueryVersions.Result ret = QueryVersions.Result.builder().functions(FunctionInfo.of(functionList, employeeMap)).build();
        return ret;
    }

    @ServiceMethod("diffFunction")
    public DiffFunction.Result diffFunction(DiffFunction.Arg arg, ServiceContext context) {
        String diffHtml = functionLogicService.diffFunction(context.getUser(), arg.getApiName(), arg.getBindingObjectAPIName(), arg.getVersions());
        return DiffFunction.Result.builder().diffHtml(diffHtml).build();
    }

    @ServiceMethod("saveRelation")
    public SaveFunctionRelation.Result saveRelation(SaveFunctionRelation.Arg arg, ServiceContext context) {
        List<ReferenceData> dataList = arg.getItems().stream().map(ReferenceInfo::toReferenceData).collect(Collectors.toList());
        functionLogicService.deleteAndCreateRelation(context.getUser(), dataList);
        return SaveFunctionRelation.Result.builder().code(200).message("success").build();
    }

    @ServiceMethod("deleteRelation")
    public DeleteFunctionRelation.Result deleteRelation(DeleteFunctionRelation.Arg arg, ServiceContext context) {
        functionLogicService.deleteRelation(context.getUser(), arg.getType(), arg.getValue(), arg.getFuncApiName());
        return DeleteFunctionRelation.Result.builder().code(200).message("success").build();
    }

    @ServiceMethod("findRelationBySource")
    public FindRelationBySource.Result findRelationBySource(FindRelationBySource.Arg arg, ServiceContext context) {
        List<ReferenceData> referenceData = functionLogicService.findRelationBySource(context.getUser(), arg.getType(), arg.getValue());
        List<ReferenceInfo> referenceInfos = CollectionUtils.nullToEmpty(referenceData).stream()
                .map(ReferenceInfo::of)
                .collect(Collectors.toList());
        return FindRelationBySource.Result.of(referenceInfos);
    }

    /**
     * 用于VSCode插件拉取函数列表
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("download")
    public DownloadFunction.Result download(DownloadFunction.Arg arg, ServiceContext context) {
        List<FunctionVSCodeExt> queryResult = functionLogicService.downloadFunctions(context.getUser(), arg.getApiName(),
                arg.getBindingObjectApiName(), arg.getType(), arg.getPageNumber(), arg.getPageSize());
        return DownloadFunction.Result.builder().list(queryResult).build();
    }

    /**
     * 用于VSCode插件上传函数
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("upload")
    public UploadFunction.Result upload(UploadFunction.Arg arg, ServiceContext context) {
        long updateTime = functionLogicService.uploadFunction(context.getUser(), arg.getApiName(), arg.getName(),
                arg.getContent(), arg.getMetaXml(), arg.getType(), arg.getUpdateTime(), arg.getNameSpace(),
                arg.getReturnType(), arg.getBindingObjectApiName(), arg.getDescription(), arg.getCommit());
        return UploadFunction.Result.builder().updateTime(updateTime).build();
    }

    /**
     * 用于VSCode插件拉取函数详情信息
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("getDetail")
    public GetDetail.Result getDetail(GetDetail.Arg arg, ServiceContext context) {
        String detail = functionLogicService.getDetailForVSCode(context.getUser(), arg.getApiName(), arg.getBindingObjectApiName());
        return GetDetail.Result.builder().detail(detail).build();
    }

    private String getToken(DebugRunFunction.Arg arg, ServiceContext context) {
        String token = arg.getToken();
        if (StringUtils.isNotEmpty(token)) {
            return token;
        }
        return context.getTenantId() + "-" + UUID.randomUUID().toString().replace("-", "");
    }

}


