package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.metadata.api.JsonCompatible;

import java.util.List;
import java.util.Map;

/**
 * 列表页组件，视图信息
 * create by <PERSON><PERSON><PERSON> on 2020/11/16
 */
public interface IViewComponentInfo extends JsonCompatible {
    String NAME = "name";
    String IS_SHOW = "is_show";
    String IS_DEFAULT = "is_default";
    String TIME_DIMENSION = "time_dimension";
    String DISPLAY_TYPE = "display_type";
    String FIELDS = "fields";
    String LOCATION_FIELD = "location_field";
    String BUBBLE_INFO = "bubble_info";

    String LIST_VIEW = "list_view";
    String SPLIT_VIEW = "split_view";
    String CALENDAR_VIEW = "calendar_view";
    String MAP_VIEW = "map_view";
    String CARD_VIEW = "card_view";

    String TREE_VIEW = "tree_view";

    String DISPLAY_TYPE_LIST = "list";
    String DISPLAY_TYPE_CALENDAR = "calendar";

    String getName();

    void setName(String name);

    boolean isShow();

    void setIsShow(boolean isShow);

    boolean isDefault();

    void setIsDefault(boolean isDefault);

    List<String> getTimeDimension();

    void setTimeDimension(List<String> timeDimension);

    String getDisplayType();

    void setDisplayType(String displayType);

    List<String> getFields();

    void setFields(List<String> fields);

    String getLocationField();

    void setLocationField(String locationField);

    IMapViewBubbleInfo getBubbleInfo();

    void setBubbleInfo(IMapViewBubbleInfo bubbleInfo);

}
