PicturePath:card
CustomerID:account_id
CustomerName:account_id__r
OpportunityID:opportunity_id
OpportunityName:opportunity_id__r
Name:name
NameSpell:account_pin_yin
Company:company
CompanySpell:company_pin_yin
Circles:owner_department
Post:job_title
IsKeyPerson:primary_contact
Tel:tel
Mobile:mobile
Email:email
Address:add
ProfileImagePath:image_path
YearOfBirth:year_of_birth
MonthOfBirth:month_of_birth
DayOfBirth:day_of_birth
BirthDay:date_of_birth
Gender:gender
Introducer:introducer
IntroducerName:introducer__r
Remark:remark
OwnerID:owner
Status:contact_status
CreatorID:created_by
CreateTime:create_time
UpdateTime:last_modified_time
UpdatorID:last_modified_by
IsDeleted:is_deleted
LockStatus:lock_status
Department:department
PartnerID:owned_partner_id
PartnerName:owned_partner_id__r
OutEI:out_tenant_id
OutOwnerID:out_owner
OutResources:out_resources
RecordType:record_type
Tel1:tel1
Tel2:tel2
Tel3:tel3
Tel4:tel4
Tel5:tel5
Mobile1:mobile1
Mobile2:mobile2
Mobile3:mobile3
Mobile4:mobile4
Mobile5:mobile5
EnablePartnerView:enable_partner_view
DataOwnDepartment:data_own_department
DataOwnDepartmentName:data_own_department__r