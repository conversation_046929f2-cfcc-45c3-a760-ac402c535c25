package com.facishare.paas.appframework.privilege;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.organization.adapter.api.permission.model.CheckFunctionCodeAndGetManageDepartments;
import com.facishare.organization.adapter.api.permission.service.PermissionService;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.employee.BatchGetEmployeeIdsByDepartmentId;
import com.facishare.organization.api.model.employee.arg.GetAllEmployeeIdsArg;
import com.facishare.organization.api.model.employee.result.GetAllEmployeeIdsResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.paas.appframework.privilege.dto.JudgeManageRangeInfo;
import com.facishare.paas.appframework.privilege.dto.JudgeMangePrivilegeResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static com.facishare.organization.adapter.api.permission.enums.functioncode.SystemFunctionCodeEnum.PERMISSION_BUSINESS_ROLE_QUERY;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试内容描述：测试JudgeMangePrivilegeServiceImpl类的方法
 */
@ExtendWith(MockitoExtension.class)
class JudgeMangePrivilegeServiceImplTest {

    @Mock
    private EmployeeProviderService employeeProviderService;

    @Mock
    private PermissionService permissionService;

    @InjectMocks
    private JudgeMangePrivilegeServiceImpl judgeMangePrivilegeService;

    private SessionContext testSessionContext;

    @BeforeEach
    void setUp() {
        testSessionContext = new SessionContext();
        testSessionContext.setEId(12345L);
        testSessionContext.setUserId(6789);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试judgeMangePrivilege方法正常场景，有管理权限
     */
    @ParameterizedTest
    @MethodSource("provideManagePrivilegeTestData")
    @DisplayName("正常场景 - judgeMangePrivilege方法有管理权限")
    void testJudgeMangePrivilegeWithPermission(boolean manageWholeCompany, List<Integer> departmentIds, 
                                               List<Integer> employeeIds, List<String> expectedEmployees) {
        // 准备测试数据
        CheckFunctionCodeAndGetManageDepartments.Result checkResult = mock(CheckFunctionCodeAndGetManageDepartments.Result.class);
        when(checkResult.getHasAbility()).thenReturn(true);
        when(checkResult.getIsManageWholeCompany()).thenReturn(manageWholeCompany);
        when(checkResult.getDepartmentIds()).thenReturn(departmentIds);

        BatchGetEmployeeIdsByDepartmentId.Result employeeResult = new BatchGetEmployeeIdsByDepartmentId.Result();
        employeeResult.setEmployeeIds(employeeIds);

        // 配置Mock行为
        when(permissionService.checkFunctionCodeAndGetManageDepartments(any(CheckFunctionCodeAndGetManageDepartments.Argument.class)))
                .thenReturn(checkResult);

        lenient().when(employeeProviderService.batchGetEmployeeIdsByDepartmentId(any(BatchGetEmployeeIdsByDepartmentId.Arg.class)))
                .thenReturn(employeeResult);

        // 执行被测试方法
        JudgeMangePrivilegeResult result = judgeMangePrivilegeService.judgeMangePrivilege(testSessionContext);

        // 验证Mock交互
        verify(permissionService, times(1)).checkFunctionCodeAndGetManageDepartments(argThat(arg -> {
            assertEquals(PERMISSION_BUSINESS_ROLE_QUERY.getFunctionCode(), arg.getFunctionCode());
            assertEquals("facishare-system", arg.getAppId());
            assertEquals(testSessionContext.getEId().intValue(), arg.getEnterpriseId());
            assertEquals(testSessionContext.getUserId(), arg.getCurrentEmployeeId());
            assertEquals(testSessionContext.getUserId(), arg.getEmployeeId());
            return true;
        }));

        if (departmentIds != null && !departmentIds.isEmpty()) {
            verify(employeeProviderService, times(1)).batchGetEmployeeIdsByDepartmentId(argThat(arg -> {
                assertEquals(testSessionContext.getEId().intValue(), arg.getEnterpriseId());
                assertEquals(departmentIds, arg.getDepartmentIds());
                assertEquals(RunStatus.ACTIVE, arg.getRunStatus());
                assertTrue(arg.isIncludeLowDepartment());
                return true;
            }));
        } else {
            verify(employeeProviderService, never()).batchGetEmployeeIdsByDepartmentId(any());
        }

        // 验证结果
        assertTrue(result.isHavePrivilege());
        assertEquals(manageWholeCompany, result.isManageWhole());
        assertEquals(departmentIds, result.getDeptIds());
        assertEquals(expectedEmployees, result.getEmployees());
    }

    static Stream<Arguments> provideManagePrivilegeTestData() {
        return Stream.of(
                Arguments.of(true, Arrays.asList(99999), Collections.emptyList(), Collections.emptyList()),
                Arguments.of(false, Arrays.asList(1001, 1002), Arrays.asList(2001, 2002), Arrays.asList("2001", "2002")),
                Arguments.of(false, Collections.emptyList(), Collections.emptyList(), Collections.emptyList())
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试judgeMangePrivilege方法异常场景，没有管理权限
     */
    @Test
    @DisplayName("异常场景 - judgeMangePrivilege方法没有管理权限")
    void testJudgeMangePrivilegeWithoutPermission() {
        // 准备测试数据
        CheckFunctionCodeAndGetManageDepartments.Result checkResult = mock(CheckFunctionCodeAndGetManageDepartments.Result.class);
        when(checkResult.getHasAbility()).thenReturn(false);

        // 配置Mock行为
        when(permissionService.checkFunctionCodeAndGetManageDepartments(any(CheckFunctionCodeAndGetManageDepartments.Argument.class)))
                .thenReturn(checkResult);

        // 执行被测试方法
        JudgeMangePrivilegeResult result = judgeMangePrivilegeService.judgeMangePrivilege(testSessionContext);

        // 验证Mock交互
        verify(permissionService, times(1)).checkFunctionCodeAndGetManageDepartments(any());
        verify(employeeProviderService, never()).batchGetEmployeeIdsByDepartmentId(any());

        // 验证结果
        assertFalse(result.isHavePrivilege());
        assertNull(result.getDeptIds());
        assertNull(result.getEmployees());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试judgeMangePrivilegeGray方法，管理全公司的情况
     */
    @Test
    @DisplayName("正常场景 - judgeMangePrivilegeGray方法管理全公司")
    void testJudgeMangePrivilegeGrayWithManageWhole() {
        // 准备测试数据
        CheckFunctionCodeAndGetManageDepartments.Result checkResult = mock(CheckFunctionCodeAndGetManageDepartments.Result.class);
        when(checkResult.getHasAbility()).thenReturn(true);
        when(checkResult.getIsManageWholeCompany()).thenReturn(true);
        when(checkResult.getDepartmentIds()).thenReturn(Arrays.asList(99999));

        // 配置Mock行为
        when(permissionService.checkFunctionCodeAndGetManageDepartments(any(CheckFunctionCodeAndGetManageDepartments.Argument.class)))
                .thenReturn(checkResult);

        // 执行被测试方法
        JudgeMangePrivilegeResult result = judgeMangePrivilegeService.judgeMangePrivilegeGray(testSessionContext);

        // 验证Mock交互
        verify(permissionService, times(1)).checkFunctionCodeAndGetManageDepartments(any());
        verify(employeeProviderService, never()).batchGetEmployeeIdsByDepartmentId(any());

        // 验证结果
        assertTrue(result.isHavePrivilege());
        assertTrue(result.isManageWhole());
        assertEquals(Arrays.asList(99999), result.getDeptIds());
        assertNull(result.getEmployees()); // 管理全公司时不需设置员工列表
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试judgeMangePrivilegeGray方法，管理部分部门的情况
     */
    @Test
    @DisplayName("正常场景 - judgeMangePrivilegeGray方法管理部分部门")
    void testJudgeMangePrivilegeGrayWithDepartments() {
        // 准备测试数据
        List<Integer> departmentIds = Arrays.asList(1001, 1002);
        List<Integer> employeeIds = Arrays.asList(2001, 2002);

        CheckFunctionCodeAndGetManageDepartments.Result checkResult = mock(CheckFunctionCodeAndGetManageDepartments.Result.class);
        when(checkResult.getHasAbility()).thenReturn(true);
        when(checkResult.getIsManageWholeCompany()).thenReturn(false);
        when(checkResult.getDepartmentIds()).thenReturn(departmentIds);

        BatchGetEmployeeIdsByDepartmentId.Result employeeResult = new BatchGetEmployeeIdsByDepartmentId.Result();
        employeeResult.setEmployeeIds(employeeIds);

        // 配置Mock行为
        when(permissionService.checkFunctionCodeAndGetManageDepartments(any(CheckFunctionCodeAndGetManageDepartments.Argument.class)))
                .thenReturn(checkResult);
        when(employeeProviderService.batchGetEmployeeIdsByDepartmentId(any(BatchGetEmployeeIdsByDepartmentId.Arg.class)))
                .thenReturn(employeeResult);

        // 执行被测试方法
        JudgeMangePrivilegeResult result = judgeMangePrivilegeService.judgeMangePrivilegeGray(testSessionContext);

        // 验证Mock交互
        verify(permissionService, times(1)).checkFunctionCodeAndGetManageDepartments(any());
        verify(employeeProviderService, times(1)).batchGetEmployeeIdsByDepartmentId(any());

        // 验证结果
        assertTrue(result.isHavePrivilege());
        assertFalse(result.isManageWhole());
        assertEquals(departmentIds, result.getDeptIds());
        assertEquals(Arrays.asList("2001", "2002"), result.getEmployees());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getEmployeeByDepartment方法正常场景
     */
    @Test
    @DisplayName("正常场景 - getEmployeeByDepartment方法查询部门员工")
    void testGetEmployeeByDepartment() {
        // 准备测试数据
        List<Integer> departmentIds = Arrays.asList(1001, 1002);
        List<Integer> employeeIds = Arrays.asList(2001, 2002);

        BatchGetEmployeeIdsByDepartmentId.Result employeeResult = new BatchGetEmployeeIdsByDepartmentId.Result();
        employeeResult.setEmployeeIds(employeeIds);

        // 配置Mock行为
        when(employeeProviderService.batchGetEmployeeIdsByDepartmentId(any(BatchGetEmployeeIdsByDepartmentId.Arg.class)))
                .thenReturn(employeeResult);

        // 执行被测试方法
        List<String> result = judgeMangePrivilegeService.getEmployeeByDepartment(departmentIds, testSessionContext);

        // 验证Mock交互
        verify(employeeProviderService, times(1)).batchGetEmployeeIdsByDepartmentId(argThat(arg -> {
            assertEquals(testSessionContext.getEId().intValue(), arg.getEnterpriseId());
            assertEquals(departmentIds, arg.getDepartmentIds());
            assertEquals(RunStatus.ACTIVE, arg.getRunStatus());
            assertTrue(arg.isIncludeLowDepartment());
            return true;
        }));

        // 验证结果
        assertEquals(Arrays.asList("2001", "2002"), result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getEmployeeByDepartment方法边界场景，部门列表为空
     */
    @Test
    @DisplayName("边界场景 - getEmployeeByDepartment方法部门列表为空")
    void testGetEmployeeByDepartmentWithEmptyDepartments() {
        // 准备测试数据
        List<Integer> departmentIds = Collections.emptyList();

        // 执行被测试方法
        List<String> result = judgeMangePrivilegeService.getEmployeeByDepartment(departmentIds, testSessionContext);

        // 验证Mock交互 - 部门为空不应调用员工查询接口
        verify(employeeProviderService, never()).batchGetEmployeeIdsByDepartmentId(any());

        // 验证结果
        assertEquals(0, result.size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkTargetFunction方法
     */
    @Test
    @DisplayName("正常场景 - checkTargetFunction方法检查目标功能权限")
    void testCheckTargetFunction() {
        // 准备测试数据
        String targetFunction = "test.function.code";

        CheckFunctionCodeAndGetManageDepartments.Result checkResult = mock(CheckFunctionCodeAndGetManageDepartments.Result.class);

        // 配置Mock行为
        when(permissionService.checkFunctionCodeAndGetManageDepartments(any(CheckFunctionCodeAndGetManageDepartments.Argument.class)))
                .thenReturn(checkResult);

        // 执行被测试方法
        CheckFunctionCodeAndGetManageDepartments.Result result = judgeMangePrivilegeService.checkTargetFunction(testSessionContext, targetFunction);

        // 验证Mock交互
        verify(permissionService, times(1)).checkFunctionCodeAndGetManageDepartments(argThat(arg -> {
            assertEquals(targetFunction, arg.getFunctionCode());
            assertEquals("facishare-system", arg.getAppId());
            assertEquals(testSessionContext.getEId().intValue(), arg.getEnterpriseId());
            assertEquals(testSessionContext.getUserId(), arg.getCurrentEmployeeId());
            assertEquals(testSessionContext.getUserId(), arg.getEmployeeId());
            return true;
        }));

        // 验证结果
        assertEquals(checkResult, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getUserMangeRangeInfo方法，管理全公司的情况
     */
    @Test
    @DisplayName("正常场景 - getUserMangeRangeInfo方法管理全公司")
    void testGetUserMangeRangeInfoWithManageWhole() {
        // 准备测试数据
        String targetFunction = "test.function.code";

        CheckFunctionCodeAndGetManageDepartments.Result checkResult = mock(CheckFunctionCodeAndGetManageDepartments.Result.class);
        when(checkResult.getHasAbility()).thenReturn(true);
        when(checkResult.getIsManageWholeCompany()).thenReturn(true);

        GetAllEmployeeIdsResult allEmployeeResult = new GetAllEmployeeIdsResult();
        allEmployeeResult.setEmployeeIds(Arrays.asList(1001, 1002, 1003));

        // 配置Mock行为
        when(permissionService.checkFunctionCodeAndGetManageDepartments(any(CheckFunctionCodeAndGetManageDepartments.Argument.class)))
                .thenReturn(checkResult);
        when(employeeProviderService.getAllEmployeeIds(any(GetAllEmployeeIdsArg.class)))
                .thenReturn(allEmployeeResult);

        // 执行被测试方法
        JudgeManageRangeInfo.Result result = judgeMangePrivilegeService.getUserMangeRangeInfo(testSessionContext, targetFunction);

        // 验证Mock交互
        verify(permissionService, times(1)).checkFunctionCodeAndGetManageDepartments(any());
        verify(employeeProviderService, times(1)).getAllEmployeeIds(argThat(arg -> {
            assertEquals(testSessionContext.getEId().intValue(), arg.getEnterpriseId());
            assertEquals(RunStatus.ACTIVE, arg.getRunStatus());
            return true;
        }));

        // 验证结果
        assertTrue(result.isHasAbility());
        assertTrue(result.isManageWholeCompany());
        assertEquals(Arrays.asList("1001", "1002", "1003"), result.getManageRangeUserIds());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getUserMangeRangeInfo方法，管理部分部门的情况
     */
    @Test
    @DisplayName("正常场景 - getUserMangeRangeInfo方法管理部分部门")
    void testGetUserMangeRangeInfoWithDepartments() {
        // 准备测试数据
        String targetFunction = "test.function.code";

        CheckFunctionCodeAndGetManageDepartments.Result checkResult = mock(CheckFunctionCodeAndGetManageDepartments.Result.class);
        when(checkResult.getHasAbility()).thenReturn(true);
        when(checkResult.getIsManageWholeCompany()).thenReturn(false);
        when(checkResult.getDepartmentIds()).thenReturn(Arrays.asList(1001, 1002));

        BatchGetEmployeeIdsByDepartmentId.Result employeeResult = new BatchGetEmployeeIdsByDepartmentId.Result();
        employeeResult.setEmployeeIds(Arrays.asList(2001, 2002));

        // 配置Mock行为
        when(permissionService.checkFunctionCodeAndGetManageDepartments(any(CheckFunctionCodeAndGetManageDepartments.Argument.class)))
                .thenReturn(checkResult);
        when(employeeProviderService.batchGetEmployeeIdsByDepartmentId(any(BatchGetEmployeeIdsByDepartmentId.Arg.class)))
                .thenReturn(employeeResult);

        // 执行被测试方法
        JudgeManageRangeInfo.Result result = judgeMangePrivilegeService.getUserMangeRangeInfo(testSessionContext, targetFunction);

        // 验证Mock交互
        verify(permissionService, times(1)).checkFunctionCodeAndGetManageDepartments(any());
        verify(employeeProviderService, times(1)).batchGetEmployeeIdsByDepartmentId(any());
        verify(employeeProviderService, never()).getAllEmployeeIds(any());

        // 验证结果
        assertTrue(result.isHasAbility());
        assertFalse(result.isManageWholeCompany());
        assertEquals(Arrays.asList("2001", "2002"), result.getManageRangeUserIds());
    }
} 