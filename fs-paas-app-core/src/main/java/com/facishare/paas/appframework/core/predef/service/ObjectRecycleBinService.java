package com.facishare.paas.appframework.core.predef.service;


import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkDeleteAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction;
import com.facishare.paas.appframework.core.predef.controller.StandardController;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.core.predef.service.dto.recycleBin.BulkDeleteData;
import com.facishare.paas.appframework.core.predef.service.dto.recycleBin.FindInvalidDataList;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IButton;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 回收站数据接口
 * <p>
 * Created by liyiguang on 2017/10/11.
 */
@Slf4j
@ServiceModule("recycle_bin")
@Component
public class ObjectRecycleBinService {

    @Autowired
    private ServiceFacade serviceFacade;

    @ServiceMethod("findInvalidDataList")
    public FindInvalidDataList.Result findInvalidDataList(FindInvalidDataList.Arg arg, ServiceContext context) {
        if (arg.getPageSize() <= 0 || arg.getPageSize() > AppFrameworkConfig.getMaxQueryLimit()) {
            log.warn("query limit:{} exceed max value:{}", arg.getPageSize(), AppFrameworkConfig.getMaxQueryLimit());
            arg.setPageSize(AppFrameworkConfig.getMaxQueryLimit());
        }

        QueryResult<IObjectData> queryResult = serviceFacade.findInvalidData(arg.toFindInvalidDataListArg(context.getTenantId()));

        FindInvalidDataList.Result result = FindInvalidDataList.Result.builder()
                .dataList(ObjectDataDocument.ofList(queryResult.getData()))
                .pageSize(arg.getPageSize())
                .totalCount(queryResult.getTotalNumber())
                .build();

        if (BooleanUtils.isNotTrue(arg.getFindExplicitTotalNum())) {
            IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), arg.getObjectDescribeAPIName());
            List<IButton> buttons = serviceFacade.findButtonsForRecycleBin(describe, context.getUser());
            result.setBulkButtons(ButtonDocument.fromButtons(buttons));
        }

        return result;
    }

    @ServiceMethod("bulkDeleteData")
    public BulkDeleteData.Result bulkDeleteData(BulkDeleteData.Arg arg, ServiceContext context) {
        StandardBulkDeleteAction.Arg actionArg = new StandardBulkDeleteAction.Arg();
        actionArg.setDescribeApiName(arg.getObjectDescribeAPIName());
        actionArg.setIdList(arg.getIdList());

        ActionContext actionContext = new ActionContext(context.getRequestContext(),
                arg.getObjectDescribeAPIName(), StandardAction.BulkDelete.name());

        StandardBulkDeleteAction.Result actionResult = serviceFacade.triggerAction(actionContext, actionArg, StandardBulkDeleteAction.Result.class);

        return BulkDeleteData.Result.builder().success(actionResult.getSuccess()).build();
    }

    @ServiceMethod("bulkRecover")
    public StandardBulkRecoverAction.Result bulkRecoverData(StandardBulkRecoverAction.Arg arg, ServiceContext context) {
        ActionContext actionContext = new ActionContext(context.getRequestContext(),
                arg.getObjectDescribeAPIName(), StandardAction.BulkRecover.name());

        return serviceFacade.triggerAction(actionContext, arg, StandardBulkRecoverAction.Result.class);
    }

    @ServiceMethod("objDetail")
    public StandardDetailController.Result objectDetail(StandardDetailController.Arg arg, ServiceContext context) {
        arg.setFromRecycleBin(true);
        ControllerContext controllerContext = new ControllerContext(context.getRequestContext(),
                arg.getObjectDescribeApiName(), StandardController.Detail.name());

        return serviceFacade.triggerController(controllerContext, arg, StandardDetailController.Result.class);
    }
}
