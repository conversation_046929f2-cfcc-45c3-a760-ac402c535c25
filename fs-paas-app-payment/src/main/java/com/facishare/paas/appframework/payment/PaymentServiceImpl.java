package com.facishare.paas.appframework.payment;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.payment.dto.PaymentRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service("paymentService")
public class PaymentServiceImpl implements PaymentService {

    @Autowired
    private EAPayOrderServiceProxy eaPayOrderQueryService;
    private String paymentUrl;
    private String detailUrl;
    private final static String BUSI_CODE = "1009";

    @PostConstruct
    private void init() {
        ConfigFactory.getInstance().getConfig("fs-crm-java-udobj-payment", config -> {
            try {
                paymentUrl = config.get("paymentUrl").trim();
                detailUrl = config.get("detailUrl").trim();
            } catch (Exception e) {
                log.warn("not exist fs-crm-java-udobj-payment config", e);
            }
        });
    }


    @Override
    public List<PaymentRecord> findPaymentList(User user, IObjectDescribe describe, IObjectData data) {
        if (Objects.isNull(describe) || Objects.isNull(data) || Objects.isNull(user)) {
            return Lists.newArrayList();
        }

        EAPayOrderServiceProxy.Arg arg = EAPayOrderServiceProxy.Arg.of(data.getId());
        EAPayOrderServiceProxy.Result result = eaPayOrderQueryService.queryEACollectPayOrder(arg);
        if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getResult())) {
            return Lists.newArrayList();
        }

        return result.getResult().stream()
                .map(a -> a.toPaymentRecord(describe, data, detailUrl))
                .collect(Collectors.toList());
    }


    @Override
    public String getPaymentUrl(GetPaymentUrlArg arg) {
        //不用多语
        return String.format("%s?busiNo=%s&busiCode=%s&orderName=%s&amount=%s&payerEnterpriseName=%s&alone=%s&apiName=%s",
                paymentUrl, arg.getDataId(), BUSI_CODE, arg.getOrderName(), arg.getAmount(), "客户", 1, arg.getApiName());// ignoreI18n
    }


}
