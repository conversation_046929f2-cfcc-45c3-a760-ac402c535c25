package com.facishare.paas.appframework.log;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 业务操作类型
 * <p>
 * Created by liyiguang on 2017/8/17.
 */
public enum ActionType {

    None("0", "None"),
    Add("1", I18NKey.ADD),
    Modify("2", I18NKey.MODIFY),
    Invalid("3", I18NKey.INVALID),
    Recovery("4", I18NKey.RECOVERY),
    Delete("5", I18NKey.DELETE),
    ChangeOwner("6", I18NKey.CHANGE_OWNER),
    AddSale("7", I18NKey.ADD_SALE),  //以前是销售团队
    RemoveSale("8", I18NKey.REMOVE_SALE),
    Assigned("9", I18NKey.ASSIGNED),
    TakeBack("10", I18NKey.TAKE_BACK),
    Receive("11", I18NKey.RECEIVE),
    ReturnBack("12", I18NKey.RETURN_BACK),
    Import("13", I18NKey.IMPORT),
    Reusing_Add("14", I18NKey.REUSING_ADD),
    RecoryByModify("15", I18NKey.RECOVERY_BY_MODIFY),
    Reusing("16", I18NKey.RESUING),
    AssistVisit("17", I18NKey.ASSIST_VISIT),
    Confirm("18", I18NKey.CONFIRM),
    Reject("19", I18NKey.REJECT),
    Recall("20", I18NKey.RECALL),
    MoveHSCustomer("21", I18NKey.MOVE_HSC_CUSTOMER),
    RemoveCustomerFromHS("22", I18NKey.REMOVE_CUSTOMER_FROM_HS),
    AddEmployee("23", I18NKey.ADD_EMPLOYEE),
    RemoveEmployee("24", I18NKey.REMOVE_EMPLOYEE),
    Enable("25", I18NKey.ENABLE),
    Disable("26", I18NKey.DISABLE),
    ModifySale("27", I18NKey.MODIFY_SALE),
    AddAddress("28", I18NKey.ADD_ADDRESS),
    ModifyAddress("29", I18NKey.MODIFY_ADDRESS),
    DeleteAddress("30", I18NKey.DELETE_ADDRESS),
    AddFinance("31", I18NKey.ADD_FINANCE),
    ModifyFinance("32", I18NKey.MODIFY_FINANCE),
    DeleteFinance("33", I18NKey.DELETE_FINANCE),
    AutoTakeBack("34", I18NKey.AUTO_TAKE_BACK),
    CancleAssistVisit("35", I18NKey.CANCEL_ASSIST_VISIT),
    Export("36", I18NKey.EXPORT),
    Handle("37", I18NKey.HANDLE),
    ConfirmDelivery("38", I18NKey.CONFIRM_DELIVERY),
    ConfirmReceive("39", I18NKey.CONFIRM_RECEIVE),
    Reset("40", I18NKey.RESET),
    SFA_WORK_FLOW_START("41", I18NKey.WORK_FLOW_START),
    SFA_WORK_FLOW_TACK_BACK("42", I18NKey.WORK_FLOW_TAKE_BACK),
    SFA_WORK_FLOW_CANCEL("43", I18NKey.WORK_FLOW_CANCEL),
    SFA_WORK_FLOW_COMPLETE("44", I18NKey.WORK_FLOW_COMPLETE),
    SFA_WORK_FLOW_REJECT("45", I18NKey.WORK_FLOW_REJECT),
    SFA_Lock("46", I18NKey.LOCK),
    SFA_UnLock("47", I18NKey.UNLOCK),
    SFA_AutoAssigned("48", I18NKey.AUTO_ASSIGNED),
    CreateLayout("create_layout", I18NKey.CREATE_LAYOUT),
    UpdateLayout("update_layout", I18NKey.UPDATE_LAYOUT),
    DeleteLayout("delete_layout", I18NKey.DELETE_LAYOUT),
    CREATE_OBJ("create_obj", I18NKey.CREATE_OBJECT),
    UPDATE_OBJ("update_obj", I18NKey.UPDATE_OBJECT),
    DELETE_OBJ("delete_obj", I18NKey.DELETE_OBJECT),
    DISABLE_OBJ("disable_obj", I18NKey.DISABLE_OBJECT),
    ENABLE_OBJ("enable_obj", I18NKey.ENABLE_OBJECT),
    CREATE_FIELD("create_field", I18NKey.CREATE_FIELD),
    UPDATE_FIELD("update_field", I18NKey.UPDATE_FIELD),
    DELETE_FIELD("delete_field", I18NKey.DELETE_FIELD),
    DISABLE_FIELD("disable_field", I18NKey.DISABLE_FIELD),
    ENABLE_FIELD("enable_field", I18NKey.ENABLE_FIELD),
    UPDATE_RECORD_TYPE("update_record_type", I18NKey.UPDATE_RECORD_TYPE),
    CREATE_RECORD_TYPE("create_record_type", I18NKey.CREATE_RECORD_TYPE),
    ENABLE_RECORD_TYPE("enable_record_type", I18NKey.ENABLE_RECORD_TYPE),
    DISABLE_RECORD_TYPE("disable_record_type", I18NKey.DIABLE_RECORD_TYPE),
    DELETE_RECORD_TYPE("delete_record_type", I18NKey.DELETE_RECORD_TYPE),
    Lock("Lock", I18NKey.LOCK),
    Unlock("unlock", I18NKey.UNLOCK),
    CREATE_BUTTON("create_utton", I18NKey.CREATE_BUTTION),
    UPDATE_BUTTON("update_button", I18NKey.UPDATE_BUTTON),
    DELETE_BUTTON("delete_button", I18NKey.DELETE_BUTTON),
    UpdateImport("update_import", I18NKey.UPDATE_IMPORT),
    CHANGE_BUTTON_STATUS("changeButtonStatus", I18NKey.CHANGE_BUTTON_STATUS),
    CHANGE_PARTNER("50", I18NKey.CHANGE_PARTNER),
    CHANGE_PARTNER_OWNER("51", I18NKey.CHANGE_PARTNER_OWNER),
    DELETE_PARTNER("52", I18NKey.DELETE_PARTNER),
    ASSOCIATE("associate", I18NKey.ASSOCIATE),
    DISASSOCIATE("disassociate", I18NKey.DIASSOCIATE),
    Empty("53", ""),
    CHANGEDEALSTATUS("54", I18NKey.CHANGE_DEAL_STATUS),
    TRANSFER_PROCESSING_TASK("transfer_processing_task", I18NKey.TRANSFER_PROCESSING_TASK),
    CREATE_OR_UPDATE_DUPLICATE_SEARCH("55", I18NKey.CREATE_OR_UPDATE_DUPLICATE_RULE),
    COLLECTED_TO("56", I18NKey.action_collect_to),
    MarkMQL("57", I18NKey.action_mark_mql),
    SET_MAIN("set_main", I18NKey.SET_MAIN),
    SET_DEFAULT("set_default", I18NKey.SET_DEFAULT),
    ALLOCATE("allocate", I18NKey.ASSIGNED),
    MOVE("move", I18NKey.action_move),
    REMOVE("remove", I18NKey.ACTION_REMOVE),
    RETURN("return", I18NKey.action_return),
    CHOOSE("choose", I18NKey.RECEIVE),
    CREATE_FUNCTION("create_function", I18NKey.CREATE_FUNCTION),
    UPDATE_FUNCTION("update_function", I18NKey.UPDATE_FUNCTION),
    DELETE_FUNCTION("delete_function", I18NKey.DELETE_FUNCTION),
    DISABLE_FUNCTION("disable_function", I18NKey.DISABLE_FUNCTION),
    ENABLE_FUNCTION("enable_function", I18NKey.ENABLE_FUNCTION),
    MODIFY("modify", I18NKey.CHANGE),
    INCREASE("add", I18NKey.INCREASE),
    CREATE_TEMPORARY_PRIVILEGE("create_temporary_privilege", I18NKey.CREATE_TEMPORARY_RIGHTS),
    UPDATE_TEMPORARY_PRIVILEGE("update_temporary_privilege", I18NKey.UPDATE_TEMPORARY_RIGHTS),
    ENABLE_TEMPORARY_PRIVILEGE("enable_temporary_privilege", I18NKey.ENABLE_TEMPORARY_RIGHTS),
    DISABLE_TEMPORARY_PRIVILEGE("disable_temporary_privilege", I18NKey.DISABLE_TEMPORARY_RIGHTS),
    DELETE_TEMPORARY_PRIVILEGE("delete_temporary_privilege", I18NKey.DELETE_TEMPORARY_RIGHTS),
    MERGE("merge", I18NKey.action_udobj_combine),
    UNLOAD("unload", I18NKey.UNLOAD),
    UPDATE_RELATED_TEAM_DATA_PERMISSION("update_related_team_data_permission", I18NKey.UPDATE_RELATED_TEAM_DATA_PERMISSION),
    EXTEND_EXPIRETIME("extend_expireTime", I18NKey.action_extend_expireTime),
    EDIT_ATLAS("edit_atlas", I18NKey.action_edit_atlas),
    REMOVE_OBJECT("remove_object", I18NKey.REMOVE_OBJECT),
    CHOOSE_MAIN_DATA("Choose", I18NKey.action_choose_main_data),
    ALLOCATE_MAIN_DATA("Allocate", I18NKey.action_allocate_main_data),
    CHANGE_STATUS("change_status", I18NKey.CHANGE_STATUS),
    CREATE_TENANT_SCENE("create_tenant_scene", I18NKey.CREATE_TENANT_SCENE),
    UPDATE_TENANT_SCENE("update_tenant_scene", I18NKey.UPDATE_TENANT_SCENE),
    DELETE_TENANT_SCENE("delete_tenant_scene", I18NKey.DELETE_TENANT_SCENE),
    DISABLE_TENANT_SCENE("disable_tenant_scene", I18NKey.DISABLE_TENANT_SCENE),
    ENABLE_TENANT_SCENE("enable_tenant_scene", I18NKey.ENABLE_TENANT_SCENE),
    DELETE_CUSTOM_SCENE("delete_custom_scene", I18NKey.DELETE_CUSTOM_SCENE),
    GDPR("gdpr", I18NKey.GDPR),
    OPEN_GDPR("open_gdpr", I18NKey.GDPR_OPEN_SUCCESS),
    CLOSE_GDPR("close_gdpr", I18NKey.GDPR_CLOSE_SUCCESS),
    ADD_GDPR_DETAIL("add_gdpr_detail", I18NKey.GDPR_ADD_LEGAL_BASE_DETAIL),
    DELETE_GDPR_DETAIL("delete_gdpr_detail", I18NKey.GDPR_DELETE_LEGAL_BASE_DETAIL),
    HISTORY_APPLY("historyApply", I18NKey.HISTORY_APPLY),
    CHANGE("change", I18NKey.ACTION_TYPE_CHANGE),
    ENTER_ACCOUNT("EnterAccount", I18NKey.ENTER_ACCOUNT),
    UPDATE_FUNCTION_REFERENCE("update_function_reference", I18NKey.UPDATE_FUNCTION_REFERENCE),
    REPORT_ANALYSIS("ReportAnalysis", I18NKey.REPORT_ANALYSIS),
    BATCH_TRANSFER_PROCUREMENT_ACCOUNT("batch_transfer_procurement_account", I18NKey.BATCH_TRANSFER_PROCUREMENT_ACCOUNT),
    BATCH_TRANSFER_PROCUREMENT_LEADS("batch_transfer_procurement_leads", I18NKey.BATCH_TRANSFER_PROCUREMENT_LEADS),
    TRANSFER_ALL_PROCUREMENT_ACCOUNT("transfer_all_procurement_account", I18NKey.TRANSFER_ALL_PROCUREMENT_ACCOUNT),
    TRANSFER_ALL_PROCUREMENT_LEADS("transfer_all_procurement_leads", I18NKey.TRANSFER_ALL_PROCUREMENT_LEADS),
    TRANSFER_PROCUREMENT_ACCOUNT("transfer_procurement_account", I18NKey.TRANSFER_PROCUREMENT_ACCOUNT),
    TRANSFER_PROCUREMENT_LEADS("transfer_procurement_leads", I18NKey.TRANSFER_PROCUREMENT_LEADS),
    SALES_ORDER_COST_ASSIGN("SalesOrderCostAssign", I18NKey.SALES_ORDER_COST_ASSIGN),
    AGREEMENT_STORE_CONFIRM("AgreementStoreConfirm", I18NKey.ACTION_AGREEMENT_STORE_CONFIRM),
    BUDGET_CLOSURE("BudgetClosure", I18NKey.BUDGET_CLOSURE),
    CHANGE_ACTION("Change", I18NKey.CHANGE_ACTION),
    IMPORT_ADD("import_add", I18NKey.IMPORT_ADD),
    PUBLIC_OBJECT_OPEN("public_object_open", I18NKey.PUBLIC_OBJECT_OPEN),
    PUBLIC_OBJECT_CLOSE("public_object_close", I18NKey.PUBLIC_OBJECT_CLOSE),
    CREATE_EXTENSION("create_extension", I18NKey.CREATE_EXTENSION),
    UPDATE_EXTENSION("update_extension", I18NKey.UPDATE_EXTENSION),
    DELETE_EXTENSION("delete_extension", I18NKey.DELETE_EXTENSION),
    DISABLE_EXTENSION("disable_extension", I18NKey.DISABLE_EXTENSION),
    ENABLE_EXTENSION("enable_extension", I18NKey.ENABLE_EXTENSION),
    ;

    String id;
    String name;

    ActionType(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return I18N.text(name);
    }

    public String getI18NKey() {
        return name;
    }

    public static String getNameById(String otherId) {
        for (ActionType actionType : ActionType.values()) {
            if (Objects.equals(actionType.getId(), otherId)) {
                return I18N.text(actionType.name);
            }
        }
        return "";
    }

    private static final List<ActionType> actionTypes = Lists.newArrayList(Empty, None, GDPR, OPEN_GDPR, CLOSE_GDPR, ADD_GDPR_DETAIL, DELETE_GDPR_DETAIL);

    public static Map<String, String> getActionTypes() {
        return Arrays.stream(ActionType.values()).filter(x -> !actionTypes.contains(x)).collect(Collectors.toMap(ActionType::getId, ActionType::getName));
    }
}
