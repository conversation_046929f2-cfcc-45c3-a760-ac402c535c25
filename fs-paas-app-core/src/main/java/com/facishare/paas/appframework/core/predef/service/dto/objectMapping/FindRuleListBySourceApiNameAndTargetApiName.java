package com.facishare.paas.appframework.core.predef.service.dto.objectMapping;

import com.facishare.paas.appframework.core.model.MappingRuleDocument;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface FindRuleListBySourceApiNameAndTargetApiName {

    @Data
    class Arg {
        private Integer status;
        private String ruleName;
        private String sourceApiName;
        private String targetApiName;


        /**
         * 0 启用 1禁用 -1全部
         *
         * @return
         */
        public Integer getStatus() {
            return status == null ? Integer.valueOf(0) : status;
        }
    }


    @Builder
    @Data
    class Result {
        private List<MappingRuleDocument> ruleList;
    }
}
