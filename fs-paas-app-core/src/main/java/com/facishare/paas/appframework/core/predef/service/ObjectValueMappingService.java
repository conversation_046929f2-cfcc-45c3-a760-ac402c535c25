package com.facishare.paas.appframework.core.predef.service;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.dto.objectMapping.*;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.util.LicenseConstants;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.privilege.UserDefinedButtonService;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectMappingRuleDetailInfo;
import com.facishare.paas.metadata.impl.ObjectMappingRuleInfo;
import com.facishare.paas.metadata.impl.UdefAction;
import com.facishare.paas.metadata.impl.UdefButton;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.ObjectMappingExt.*;
import static com.facishare.paas.metadata.api.describe.IObjectDescribe.DEFINE_TYPE_SYSTEM;

@ServiceModule("object_mapping")
@Service
public class ObjectValueMappingService {
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private PostActionService postActionService;
    @Autowired
    private CustomButtonServiceImpl buttonService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private UserDefinedButtonService userDefinedButtonService;
    @Autowired
    private ButtonLogicService buttonLogicService;
    @Autowired
    private LicenseService licenseService;

    @Transactional
    @ServiceMethod("createRule")
    public CreateRule.Result createRule(CreateRule.Arg arg, ServiceContext context) {
        //1 创建映射
        List<IObjectMappingRuleInfo> ruleList = arg.getRuleList().stream()
                .map(ObjectMappingRuleInfo::new).collect(Collectors.toList());
        ruleList.forEach(a -> {
            a.setTenantId(context.getTenantId());
            a.setCreatedBy(context.getUser().getUserId());
            a.setLastModifiedBy(context.getUser().getUserId());
        });
        List<IObjectMappingRuleInfo> rules = objectMappingService.createRule(context.getUser(), ruleList);
        Optional<IObjectMappingRuleInfo> master = rules.stream()
                .filter(a -> Strings.isNullOrEmpty(a.getMasterRuleApiName())).findAny();
        if (!master.isPresent()) {
            throw new ValidateException(I18N.text(I18NKey.CONVERSION_RULE_GENERATION_ERROR));
        }

        //2 创建Action
        IUdefButton button = new UdefButton(arg.getButton());
        IUdefAction action = new UdefAction();
        ObjectMappingActionParam actionParam = ObjectMappingActionParam.builder()
                .objectMappingApiName(master.get().getRuleApiName())
                .build();
        action.setActionType(MAPPING_ACTION_TYPE);
        action.setDescribeApiName(arg.getDescribeApiName());
        action.setLabel(String.format("%s-%s-%s", I18N.text(I18NKey.CONVERT), button.getLabel(), RandomStringUtils.random(4, true, true)));
        action.setActionParamter(JSON.toJSONString(actionParam));
        List<IUdefAction> actions = postActionService.bulkCreateAction(context.getUser(), Lists.newArrayList(action));

        //3 创建按钮
        List<String> actionIdList = actions.stream().map(a -> a.getId()).collect(Collectors.toList());
        button.setActions(actionIdList);
        IUdefButton customButton = buttonService.createCustomButton(context.getUser(), button);
        if (Objects.isNull(customButton)) {
            throw new ValidateException(I18N.text(I18NKey.CONVERSION_RULE_CORRESPONDING_BUTTON_GENERATION_ERROR));
        }

        //设置按钮角色button_convertOpportunity__c
        userDefinedButtonService.createUserDefinedButton(context.getUser(), customButton.getDescribeApiName(),
                customButton.getApiName(), customButton.getLabel(), arg.getRoles());

        List<MappingRuleDocument> result = MappingRuleDocument.fromList(rules);
        return CreateRule.Result.builder().ruleList(result).build();
    }

    @Transactional
    @ServiceMethod("updateRule")
    public UpdateRule.Result updateRule(UpdateRule.Arg arg, ServiceContext serviceContext) {
        //更新按钮基本信息，包括角色
        updateButtonInfo(arg.getButton(), serviceContext.getUser(), arg.getRoles());

        List<IObjectMappingRuleInfo> ruleList = arg.getRuleList().stream()
                .map(ObjectMappingRuleInfo::new).collect(Collectors.toList());
        List<IObjectMappingRuleInfo> resultRules = objectMappingService.updateRule(serviceContext.getUser(), ruleList);

        List<MappingRuleDocument> result = MappingRuleDocument.fromList(resultRules);
        return UpdateRule.Result.builder().ruleList(result).build();
    }

    private void updateButtonInfo(ButtonDocument buttonDocument, User user, List<String> newRoles) {
        if (Objects.isNull(buttonDocument)) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.BUTTON_INFO_NOT_EXIST));
        }

        //查找按钮
        IUdefButton newButton = new UdefButton(buttonDocument);
        if (Strings.isNullOrEmpty(newButton.getLabel())) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.BUTTON_NAME_CANNOT_EMPTY));
        }

        IUdefButton button = buttonService.findButtonByApiName(user, newButton.getApiName(), newButton.getDescribeApiName());
        if (Objects.isNull(button)) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.MAPPING_RULE_CORRESPONDING_BUTTON_DOES_NOT_EXIST));
        }

        //1 更新按钮对应角色
        //预置规则对应的按钮不可编辑
        if (isPredefineRuleButton(button.getApiName())) {
            return;
        }
        //2 更新按钮基本信息
        button.setLabel(newButton.getLabel());
        button.setDescription(newButton.getDescription());
        button.setWheres(newButton.getWheres());
        button.setLastModifiedBy(user.getUserId());
        button.setUsePages(newButton.getUsePages());

        buttonLogicService.updateButtonAndFunctionPrivilegePrivilege(user, button, newRoles);
    }

    @ServiceMethod("enableRule")
    public EnableRule.Result enableRule(EnableRule.Arg arg, ServiceContext serviceContext) {
        objectMappingService.enableRule(serviceContext.getUser(), arg.getRuleApiName(), arg.getDescribeApiName());
        return EnableRule.Result.builder().isSuccess(true).build();
    }

    @ServiceMethod("disableRule")
    public DisableRule.Result disableRule(DisableRule.Arg arg, ServiceContext serviceContext) {
        objectMappingService.disableRule(serviceContext.getUser(), arg.getRuleApiName(), arg.getDescribeApiName());
        return DisableRule.Result.builder().isSuccess(true).build();
    }


    @ServiceMethod("deleteRule")
    public DeleteRule.Result deleteRule(DeleteRule.Arg arg, ServiceContext serviceContext) {
        objectMappingService.deleteRule(serviceContext.getUser(), arg.getRuleApiName(), arg.getDescribeApiName());
        return DeleteRule.Result.builder().isSuccess(true).build();
    }


    @ServiceMethod("findByApiName")
    public FindRuleByApiName.Result findByApiName(FindRuleByApiName.Arg arg, ServiceContext serviceContext) {
        List<IObjectMappingRuleInfo> rules = objectMappingService.findByApiName(serviceContext.getUser(), arg.getRuleApiName());
        List<MappingRuleDocument> result = MappingRuleDocument.fromList(rules);

        if (CollectionUtils.empty(rules)) {
            return FindRuleByApiName.Result.builder()
                    .ruleList(null)
                    .roles(null)
                    .build();
        }
        ObjectMappingExt masterRule = ObjectMappingExt.of(rules);
        IUdefButton button = objectMappingService.findButtonByRuleApiName(arg.getRuleApiName(),
                masterRule.getSourceApiName(), serviceContext.getUser());
        List<String> roles = userDefinedButtonService.getHavePrivilegeRolesByUserDefinedButton(serviceContext.getUser(),
                button.getDescribeApiName(), button.getApiName());

        return FindRuleByApiName.Result.builder()
                .ruleList(result)
                .roles(roles)
                .button(ButtonDocument.of(button))
                .build();
    }

    @ServiceMethod("findRuleList")
    public FindRuleList.Result findRuleList(FindRuleList.Arg arg, ServiceContext serviceContext) {
        List<IObjectMappingRuleInfo> rules = objectMappingService.findRuleList(serviceContext.getUser(), arg.getStatus(), arg.getRuleName(), arg.getBizType());
        fillDisplayName(rules, serviceContext.getTenantId());
        fillButtonName(rules, serviceContext.getUser());
        List<MappingRuleDocument> result = MappingRuleDocument.fromList(rules);
        return FindRuleList.Result.builder().ruleList(result).build();
    }


    @ServiceMethod("findRuleListBySourceApiNameAndTargetApiName")
    public FindRuleListBySourceApiNameAndTargetApiName.Result findRuleListBySourceApiNameAndTargetApiName(
            FindRuleListBySourceApiNameAndTargetApiName.Arg arg, ServiceContext serviceContext) {
        List<IObjectMappingRuleInfo> rules = objectMappingService.findRuleListBySourceApiNameAndTargetApiName(
                serviceContext.getUser(), arg.getStatus(), arg.getSourceApiName(), arg.getTargetApiName());
        fillDisplayName(rules, serviceContext.getTenantId());
        fillButtonName(rules, serviceContext.getUser());
        List<MappingRuleDocument> result = MappingRuleDocument.fromList(rules);
        return FindRuleListBySourceApiNameAndTargetApiName.Result.builder().ruleList(result).build();
    }

    private void fillButtonName(List<IObjectMappingRuleInfo> rules, User user) {
        if (CollectionUtils.empty(rules)) {
            return;
        }

        List<String> list = rules.stream().map(IObjectMappingRuleInfo::getSourceApiName).collect(Collectors.toList());
        Map<String, List<IUdefButton>> buttonMap = buttonService.findButtonByApiNameListAndType(user, list, MAPPING_ACTION_TYPE);
        Map<String, List<IUdefAction>> actionMap = postActionService.findActionByApiNameListAndType(user, list, MAPPING_ACTION_TYPE);
        Map<String, IUdefButton> ruleButtonMap = ObjectMappingExt.parseRuleButtonMap(buttonMap, actionMap);
        // 判断是否购买多语资源包
        Map<String, Boolean> existModule = licenseService.existModule(user.getTenantId(), Sets.newHashSet(LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP));

        rules.forEach(rule -> {
            IUdefButton button = ruleButtonMap.get(rule.getRuleApiName());
            if (!Objects.isNull(button)) {
                if (existModule.get(LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP)) {
                    ButtonExt.of(button).fillButtonLabel(button.getDescribeApiName());
                }
                rule.setRuleActionName(button.getLabel());
            }
        });
    }

    @ServiceMethod("checkCount")
    public CheckCount.Result checkCount(CheckCount.Arg arg, ServiceContext serviceContext) {
        List<IUdefButton> buttonList = buttonService.findButtonList(serviceContext.getUser(), arg.getSourceApiName()).stream()
                .filter(button -> !ButtonExt.of(button).isSystemButton())
                .collect(Collectors.toList());

        buttonService.checkCustomButtonCountLimit(serviceContext.getUser(), arg.getSourceApiName(), buttonList.size());

        //以下代码兼容前段，前段未大改，故不动
        List<IObjectMappingRuleInfo> rules = objectMappingService.findRuleList(serviceContext.getUser(), -1, null);
        int ruleCount = 0;
        for (IObjectMappingRuleInfo rule : rules) {
            if (Objects.equals(rule.getSourceApiName(), arg.getSourceApiName())
                    && Objects.equals(rule.getTargetApiName(), arg.getTargetApiName())
                    && !Objects.equals(rule.getDefineType(), DEFINE_TYPE_SYSTEM)
                    && Objects.isNull(rule.getMasterRuleApiName())) {
                ruleCount++;
            }
        }
        return CheckCount.Result.builder()
                .customButtonCount(buttonList.size())
                .ruleCount(ruleCount)
                .build();
    }

    private void fillDisplayName(List<IObjectMappingRuleInfo> rules, String tenantId) {
        Set<String> apiNameSet = Sets.newHashSet();
        rules.forEach(a -> {
            apiNameSet.add(a.getSourceApiName());
            apiNameSet.add(a.getTargetApiName());
        });
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(tenantId, apiNameSet);
        rules.forEach(a -> {
            IObjectDescribe source = describeMap.get(a.getSourceApiName());
            a.setSourceDisplayName(Objects.isNull(source) ? null : source.getDisplayName());
            IObjectDescribe target = describeMap.get(a.getTargetApiName());
            a.setTargetDisplayName(Objects.isNull(target) ? null : target.getDisplayName());
        });

    }

    @ServiceMethod("addFieldMapping")
    public AddFieldMapping.Result addFieldMapping(AddFieldMapping.Arg arg, ServiceContext serviceContext) {
        Map<String, List<ObjectMappingRuleDetailInfo>> map = new HashMap<>();
        Iterator<Map.Entry<String, List<MappingRuleDetailDocument>>> iterator = arg.getMap().entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, List<MappingRuleDetailDocument>> entry = iterator.next();
            List<ObjectMappingRuleDetailInfo> ruleDetailInfos = entry.getValue().stream()
                    .map(ObjectMappingRuleDetailInfo::new).collect(Collectors.toList());
            map.put(entry.getKey(), ruleDetailInfos);
        }
        objectMappingService.addFieldMappingRule(serviceContext.getUser(), map);

        return AddFieldMapping.Result.builder()
                .rule(null)
                .build();
    }
}
