package com.facishare.paas.appframework.rest.dto.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-07-02 19:22
 */
public interface FindDetailDataList {
    @Data
    @Builder
    class Result {
        @JSONField(name = "masterData")
        @JsonProperty(value = "masterData")
        @SerializedName("masterData")
        ObjectDataDocument masterData;

        @JSONField(name = "detailData")
        @JsonProperty(value = "detailData")
        @SerializedName("detailData")
        Map<String, List<ObjectDataDocument>> detailData;
    }
}
