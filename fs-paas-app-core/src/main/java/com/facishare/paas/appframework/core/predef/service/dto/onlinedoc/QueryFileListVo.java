package com.facishare.paas.appframework.core.predef.service.dto.onlinedoc;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> create by liy on 2024/6/5
 */
public interface QueryFileListVo {
    @Data
    class Arg {
        /**
         * 插件apiName
         */
        private String pluginApiName;
        /**
         * 下一页
         */
        private boolean needNextPage;
        /**
         * 要查看的文件夹
         */
        private FileInfo folder;
        /**
         * 所属团队
         */
        private GroupInfo group;
        /**
         * 与厂商相关的信息
         * 默认为空map
         */
        private Map<String, Object> extraInfo;
    }

    @Data
    class Result {
        /**
         * 错误码
         */
        private int errorCode;
        /**
         * 错误提示语
         */
        private String errorMessage;
        /**
         * 文件清单
         */
        private List<FileInfo> fileList;
        /**
         * 团队清单
         */
        private List<GroupInfo> groupList;
        /**
         * 是否有更多
         */
        private boolean hasMore;
        /**
         * 与厂商相关的信息
         * 默认为空map
         */
        private Map<String, Object> extraInfo;
        /**
         * 待用户授权url
         */
        private String authUrl;
    }

    @Data
    class FileInfo {
        /**
         * 文件id，wps用
         */
        private String fileId;
        /**
         * 文件url
         */
        private String fileUrl;
        /**
         * 文件类型 OnlineDocFileType
         */
        private String fileType;
        /**
         * 文件名称
         */
        private String fileName;
        /**
         * 文件后缀
         */
        private String fileExt;
        /**
         * 文件大小
         */
        private int fileSize;
        /**
         * 创建时间，秒级
         */
        private long createTime;
        /**
         * 更新时间，秒级
         */
        private long updateTime;
        /**
         * 插件apiName
         */
        private String pluginApiName;
    }

    @Data
    class GroupInfo {
        /**
         * 团队id
         */
        private String id;
        /**
         * 团队名称
         */
        private String name;
        /**
         * 团队创建时间,秒级
         */
        private long createTime;
        /**
         * 团队修改时间,秒级
         */
        private long updateTime;
    }
}
