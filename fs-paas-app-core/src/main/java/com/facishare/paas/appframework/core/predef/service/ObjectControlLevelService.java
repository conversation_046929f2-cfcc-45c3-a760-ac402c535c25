package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.control.level.FindControlLevel;
import com.facishare.paas.appframework.metadata.config.ControlLevelResourceType;
import com.facishare.paas.appframework.metadata.config.ObjectControlLevelLogicService;
import com.google.common.base.Strings;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * Created by zhao<PERSON>ju on 2024/5/22
 */
@ServiceModule("controlLevel")
@Service
public class ObjectControlLevelService {

    private final ObjectControlLevelLogicService objectControlLevelLogicService;

    public ObjectControlLevelService(ObjectControlLevelLogicService objectControlLevelLogicService) {
        this.objectControlLevelLogicService = objectControlLevelLogicService;
    }

    @ServiceMethod("findControlLevel")
    public FindControlLevel.Result findControlLevel(ServiceContext serviceContext, FindControlLevel.Arg arg) {
        ControlLevelResourceType resourceType = ControlLevelResourceType.getByResourceType(arg.getResourceType()).orElse(null);
        if (Objects.isNull(resourceType)) {
            return FindControlLevel.Result.builder().build();
        }
        List<ObjectControlLevelLogicService.ObjectControlLevelInfo> objectControlLevelInfos =
                objectControlLevelLogicService.queryControlLevel(serviceContext.getUser(), arg.getParentFieldValue(), resourceType);
        if (!Strings.isNullOrEmpty(arg.getPrimaryKey())) {
            objectControlLevelInfos.removeIf(it -> !Objects.equals(it.getPrimaryKey(), arg.getPrimaryKey()));
        }
        return FindControlLevel.Result.builder()
                .controlLevelInfos(objectControlLevelInfos)
                .build();
    }
}
