package com.facishare.paas.appframework.core.predef.handler.list;

import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Created by zhouwr on 2024/3/14.
 */
public interface RelatedListHandler extends BaseListHandler<RelatedListHandler.Arg, BaseListHandler.Result> {
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class Arg extends BaseListHandler.Arg<StandardRelatedListController.Arg> {
    }
}
