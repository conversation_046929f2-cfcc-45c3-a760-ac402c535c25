package com.facishare.paas.appframework.core.predef.handler.edit;

import com.facishare.paas.appframework.core.model.handler.HandlerContext;

/**
 * Created by zhouwr on 2023/2/16.
 */
public abstract class AbstractEditAfterHandler implements EditActionHandler {
    protected abstract Result doHandle(HandlerContext context, Arg arg);

    @Override
    public Result handle(HandlerContext context, Arg arg) {
        if (arg.triggerUpdateApprovalFlowSuccess()) {
            return new Result();
        }
        return doHandle(context, arg);
    }
}
