package com.facishare.paas.appframework.core.predef.action

import com.facishare.paas.appframework.core.model.ActionLocateServiceImpl
import spock.lang.Specification

/**
 * Created by l<PERSON>yiguang on 2019/3/6.
 */

class ActionListenerTest extends Specification {

    ActionLocateServiceImpl actionLocateService

    def "test action listener"() {
        expect:
        1 == 1
        /*
        when:
        actionLocateService.locateAction()
        then:
        noExceptionThrown()
        */
    }


}