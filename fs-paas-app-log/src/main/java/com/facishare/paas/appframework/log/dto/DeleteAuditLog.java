package com.facishare.paas.appframework.log.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface DeleteAuditLog {
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        List<Parameter> parameters;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Parameter {
        String tenantId;
        String objectId;
        String objectAPIName;
        String bizId;
        String otherBizId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        boolean success;
        String errorMessage;
    }

}
