package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.StandardConfigDocument;
import com.facishare.paas.appframework.core.predef.service.dto.scene.*;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.SceneLogicService;
import com.facishare.paas.appframework.metadata.config.IUdefConfig;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.facishare.paas.appframework.metadata.dto.scene.ITenantScene;
import com.facishare.paas.appframework.metadata.dto.scene.SceneDTO;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.lambda.tuple.Tuple;
import org.jooq.lambda.tuple.Tuple3;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static com.facishare.paas.appframework.metadata.SceneLogicService.IS_OUTER;
import static com.facishare.paas.appframework.metadata.SearchTemplateExt.SceneType.TENANT_SCENE;

/**
 * create by zhaoju on 2020/10/28
 */
@Slf4j
@Component
@ServiceModule("outer_tenant_scene")
public class ObjectOuterTenantSceneService {
    @Autowired
    private SceneLogicService sceneLogicService;

    @Autowired
    private DescribeLogicService describeLogicService;

    @ServiceMethod("find_scene_list")
    public FindSceneList.Result findScenes(FindSceneList.Arg arg, ServiceContext context) {
        setOuterInContext(context, arg.getAppId());
        List<IScene> scenes = sceneLogicService.findScenes(arg.getDescribeApiName(), context.getUser(), arg.getExtendAttribute(), arg.getIsActive());
        List<IScene> baseScenes = sceneLogicService.findBaseScenes(arg.getDescribeApiName(), arg.getExtendAttribute(), context.getUser());
        List<IUdefConfig> sceneConfigList = sceneLogicService.findSceneConfigList(context.getUser(), arg.getDescribeApiName(), scenes);
        return FindSceneList.Result.of(scenes, baseScenes, StandardConfigDocument.ofList(sceneConfigList));
    }

    private void setOuterInContext(ServiceContext context, String appId) {
        context.setAttribute(IS_OUTER, true);
//        context.setAttribute(APP_ID,appId);
    }

    @ServiceMethod("find_scene")
    public FindSceneInfo.Result findScenes(FindSceneInfo.Arg arg, ServiceContext context) {
        setOuterInContext(context, arg.getAppId());
        IScene scene = sceneLogicService.findSceneByApiName(context.getUser(), arg.getDescribeApiName(), arg.getSceneApiName(),
                arg.getExtendAttribute(), arg.getAppId());
        return FindSceneInfo.Result.of(scene);
    }

    @ServiceMethod("create_scene")
    public ObjectTenantScene.Result createTenantScene(ObjectTenantScene.Arg arg, ServiceContext context) {
        SceneDTO scene = arg.getScene();
        if (Objects.isNull(scene)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        List<Tuple3<String, String, String>> api2Display2TypeForDefault = null;
        setOuterInContext(context, arg.getAppId());
        ITenantScene tenantScene = sceneLogicService.createTenantScene(arg.getScene(), arg.getDescribeApiName(), arg.getExtendAttribute(), context.getUser());
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(context.getTenantId(), arg.getDescribeApiName());
        if (Boolean.TRUE.equals(scene.getClearCustomConfig())) {
            api2Display2TypeForDefault = Lists.newArrayList();
        }
        sceneLogicService.replaceOtherScenes(describe, scene, arg.getExtendAttribute(), api2Display2TypeForDefault, context.getUser());
        if (CollectionUtils.isNotEmpty(api2Display2TypeForDefault)) {
            sceneLogicService.ownCustomListConfigUserIdsToSend(scene.getObjectDescribeApiName(), describe.getDisplayName(), api2Display2TypeForDefault, arg.getExtendAttribute(), context.getUser());
        }
        return ObjectTenantScene.Result.of(tenantScene);
    }

    @ServiceMethod("update_scene")
    public ObjectTenantScene.Result updateTenantScene(ObjectTenantScene.Arg arg, ServiceContext context) {
        setOuterInContext(context, arg.getAppId());
        SceneDTO scene = arg.getScene();
        IScene resultScene = null;
        List<Tuple3<String, String, String>> api2Display2TypeForDefault = null;
        if (Boolean.TRUE.equals(scene.getClearCustomConfig())) {
            api2Display2TypeForDefault = Lists.newArrayList();
        }
        if (TENANT_SCENE.getType().equals(scene.getType())) {
            resultScene = sceneLogicService.updateTenantScene(scene, arg.getDescribeApiName(),
                    context.getUser());
        } else {
            resultScene = sceneLogicService.updateSystemScene(scene, context.getUser());
            if (api2Display2TypeForDefault != null) {
                api2Display2TypeForDefault.add(Tuple.tuple(scene.getApiName(), scene.getDisplayName(), scene.getType()));
            }
        }
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(context.getTenantId(), arg.getDescribeApiName());
        sceneLogicService.replaceOtherScenes(describe, scene, arg.getExtendAttribute(), api2Display2TypeForDefault, context.getUser());
        if (CollectionUtils.isNotEmpty(api2Display2TypeForDefault)) {
            sceneLogicService.ownCustomListConfigUserIdsToSend(scene.getObjectDescribeApiName(), describe.getDisplayName(), api2Display2TypeForDefault, arg.getExtendAttribute(), context.getUser());
        }
        return ObjectTenantScene.Result.of(resultScene);
    }

    @ServiceMethod("enable_scene")
    public ChangeSceneStatus.Result enableScene(ChangeSceneStatus.Arg arg, ServiceContext context) {
        setOuterInContext(context, arg.getAppId());
        sceneLogicService.enableScene(arg.getDescribeApiName(), arg.getSceneApiName(), arg.getType(), arg.getExtendAttribute(), context.getUser());
        return ChangeSceneStatus.Result.success();
    }

    @ServiceMethod("disable_scene")
    public ChangeSceneStatus.Result disableScene(ChangeSceneStatus.Arg arg, ServiceContext context) {
        setOuterInContext(context, arg.getAppId());
        sceneLogicService.disableScene(arg.getDescribeApiName(), arg.getSceneApiName(), arg.getType(), arg.getExtendAttribute(), context.getUser());
        return ChangeSceneStatus.Result.success();
    }

    @ServiceMethod("delete_scene")
    public ChangeSceneStatus.Result deleteScene(ChangeSceneStatus.Arg arg, ServiceContext context) {
        setOuterInContext(context, arg.getAppId());
        sceneLogicService.deleteTenantScene(arg.getDescribeApiName(), arg.getSceneApiName(), arg.getExtendAttribute(), context.getUser());
        return ChangeSceneStatus.Result.success();
    }

    @ServiceMethod("validate_tenant_scene_count")
    public ValidateTenantSceneCount.Result validateTenantSceneCount(ValidateTenantSceneCount.Arg arg,
                                                                    ServiceContext context) {
        setOuterInContext(context, arg.getAppId());
        sceneLogicService.validateTenantSceneSum(arg.getDescribeApiName(), context.getUser());
        return ValidateTenantSceneCount.Result.builder().success(true).build();
    }

    @ServiceMethod("validate_scene_name")
    public ValidateSceneName.Result validateSceneName(ValidateSceneName.Arg arg, ServiceContext context) {
        setOuterInContext(context, arg.getAppId());
        boolean apiNameRepeat = sceneLogicService.validateSceneApiNameRepeat(arg.getSceneApiName(), arg.getDisplayName(), arg.getId(),
                arg.getExtendAttribute(), context.getUser());
        return ValidateSceneName.Result.builder().apiNameRepeat(apiNameRepeat).build();
    }

    @ServiceMethod("set_default_scene_priority")
    public DefaultScenePriority.Result setDefaultScenePriority(DefaultScenePriority.Arg arg, ServiceContext context) {
        setOuterInContext(context, arg.getAppId());
        sceneLogicService.setUpDefaultScenePriority(arg.getDescribeApiName(), arg.getSceneApiNames(), arg.getExtendAttribute(), context.getUser());
        return DefaultScenePriority.Result.buildSuccess();
    }
}
