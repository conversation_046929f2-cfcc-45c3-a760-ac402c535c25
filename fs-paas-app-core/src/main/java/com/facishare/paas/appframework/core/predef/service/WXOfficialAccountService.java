package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.facishare.paas.appframework.prm.model.OutNotificationConstant.WXTemplateObj;

/**
 * 微信公众号服务
 *
 * <AUTHOR>
 * @time 2023-05-05 15:04
 * @Description
 */
@Service
@Slf4j
public class WXOfficialAccountService {
    @Autowired
    private ServiceFacade serviceFacade;

    /**
     * 获取该企业下，绑定的微信公众号下的模版列表
     *
     * @param user
     * @return
     */
    public List<IObjectData> getAllWXTemplatedList(User user) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1000);
        return getlWXTemplatedList(user, query);
    }

    public List<IObjectData> getlWXTemplatedList(User user, SearchTemplateQuery query) {
        return serviceFacade.findBySearchQueryIgnoreAll(user, WXTemplateObj, query).getData();
    }


}
