package com.facishare.paas.appframework.metadata.mtresource;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.MtResource;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2021/03/05
 */
@Service
public class MtResourceServiceImpl implements IMtResourceService {

    @Autowired
    private IRepository<MtResource> repository;

    @Autowired
    private DescribeLogicService describeLogicService;

    @Override
    public List<MtResource> queryResource(String tenantId, String resourceParentValue, String resourceType, List<String> resourceValues) {
        return queryResource(tenantId, resourceParentValue, resourceType, resourceValues, null, null);
    }

    @Override
    public List<MtResource> queryResource(String tenantId, String resourceParentValue, String resourceType, List<String> resourceValues, String sourceType, String sourceValue) {
        Query query = buildSearchQuery(tenantId, resourceParentValue, resourceType, resourceValues, sourceType, sourceValue);
        return repository.findBy(User.systemUser(tenantId), query, MtResource.class);
    }

    @Override
    public List<MtResource> queryResourceByResourceTypes(String tenantId, String resourceParentValue, Collection<String> resourceTypes, String resourceValue) {
        Query query = buildSearchQuery(tenantId, resourceParentValue, null, resourceTypes, resourceValue, null, null, null);
        return repository.findBy(User.systemUser(tenantId), query, MtResource.class);
    }

    private Query buildSearchQuery(String tenantId, String resourceParentValue, String resourceType, List<String> resourceValues,
                                   String sourceType, String sourceValue) {
        return buildSearchQuery(tenantId, resourceParentValue, resourceType, null, null, resourceValues, sourceType, sourceValue);
    }

    private Query buildSearchQuery(String tenantId, String resourceParentValue, String resourceType, Collection<String> resourceTypes,
                                   String resourceValue, List<String> resourceValues, String sourceType, String sourceValue) {
        List<IFilter> filters = Lists.newArrayList();
        filters.add(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, tenantId).getFilter());
        filters.add(FilterExt.of(Operator.EQ, MtResource.RESOURCE_PARENT_VALUE, resourceParentValue).getFilter());
        if (!Strings.isNullOrEmpty(resourceType)) {
            filters.add(FilterExt.of(Operator.EQ, MtResource.RESOURCE_TYPE, resourceType).getFilter());
        } else if (CollectionUtils.notEmpty(resourceTypes)) {
            filters.add(FilterExt.of(Operator.IN, MtResource.RESOURCE_TYPE, Lists.newArrayList(resourceTypes)).getFilter());
        }
        filters.add(FilterExt.of(Operator.EQ, MtResource.STATUS, MtResource.STATUS_TYPE_ENABLE).getFilter());
        if (CollectionUtils.notEmpty(resourceValues)) {
            filters.add(FilterExt.of(Operator.IN, MtResource.RESOURCE_VALUE, resourceValues).getFilter());
        } else if (!Strings.isNullOrEmpty(resourceValue)) {
            filters.add(FilterExt.of(Operator.LIKE, MtResource.RESOURCE_VALUE, resourceValue).getFilter());
        }
        if (!Strings.isNullOrEmpty(sourceType)) {
            filters.add(FilterExt.of(Operator.EQ, MtResource.SOURCE_TYPE, sourceType).getFilter());
        }
        if (!Strings.isNullOrEmpty(sourceValue)) {
            filters.add(FilterExt.of(Operator.EQ, MtResource.SOURCE_VALUE, sourceValue).getFilter());
        }
        SearchQuery searchQuery = SearchQueryImpl.filters(filters);
        return Query.builder().limit(CollectionUtils.empty(resourceValues) ? AppFrameworkConfig.getMaxQueryLimit()
                : resourceValues.size()).searchQuery(searchQuery).build();

    }

    @Override
    public List<MtResource> modifyResource(String tenantId, String resourceParentValue, String resourceType, List<String> resourceValues,
                                           String controlLevel, String sourceType, String sourceValue) {
        if (CollectionUtils.empty(resourceValues)) {
            return Lists.newArrayList();
        }
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "#modifyResource" + tenantId);
        try {
            List<MtResource> mtResources = queryResource(tenantId, resourceParentValue, resourceType, resourceValues, sourceType, null);
            stopWatch.lap("queryResource");
            if (CollectionUtils.empty(mtResources)) {
                List<MtResource> mtResourceList = resourceValues.stream()
                        .map(resourceValue -> createMtResource(tenantId, resourceParentValue, resourceType, resourceValue, controlLevel,
                                sourceType, sourceValue))
                        .collect(Collectors.toList());
                List<MtResource> result = repository.bulkCreate(User.systemUser(tenantId), mtResourceList);
                stopWatch.lap("bulkCreate");
                return result;
            }
            // 已存在的 mtResources 修改 controlLevel
            Set<String> existResourceValues = mtResources.stream()
                    .peek(it -> {
                        it.setControlLevel(controlLevel);
                        it.setSourceValue(sourceValue);
                    })
                    .map(MtResource::getResourceValue)
                    .collect(Collectors.toSet());
            List<MtResource> mtResourceList = resourceValues.stream()
                    .filter(it -> !existResourceValues.contains(it))
                    .map(resourceValue -> createMtResource(tenantId, resourceParentValue, resourceType, resourceValue, controlLevel,
                            sourceType, sourceValue))
                    .collect(Collectors.toList());

            mtResourceList.addAll(mtResources);
            List<MtResource> result = repository.bulkUpsert(User.systemUser(tenantId), mtResourceList);
            stopWatch.lap("bulkUpsert");
            return result;
        } finally {
            stopWatch.logSlow(1000);
        }
    }

    private MtResource createMtResource(String tenantId, String describeApiName, String resourceType, String resourceValue,
                                        String controlLevel, String sourceType, String sourceValue) {
        MtResource mtResource = new MtResource();
        mtResource.setTenantId(tenantId);
        mtResource.setObjectDescribeApiName(MtResource.MT_RESOURCE_OBJ_API_NAME);
        mtResource.setResourceParentValue(describeApiName);
        mtResource.setResourceValue(resourceValue);
        mtResource.setResourceType(resourceType);
        mtResource.setControlLevel(controlLevel);
        mtResource.setSourceType(sourceType);
        mtResource.setSourceValue(sourceValue);
        mtResource.setStatus(MtResource.STATUS_TYPE_ENABLE);
        return mtResource;
    }

    @Override
    public void disableResource(String tenantId, String resourceParentValue, String resourceType, List<String> resourceValues, String sourceType) {
        List<MtResource> mtResources = queryResource(tenantId, resourceParentValue, resourceType, resourceValues, sourceType, null);
        if (CollectionUtils.empty(mtResources)) {
            return;
        }
        // 将 status 设置为 disable（1）
        for (MtResource mtResource : mtResources) {
            mtResource.setStatus(MtResource.STATUS_TYPE_DISABLE);
        }
        repository.bulkUpsert(User.systemUser(tenantId), mtResources);
    }

    @Override
    @Transactional
    public void updateDescribeAndModifyResource(String tenantId, IObjectDescribe objectDescribe,
                                                Collection<String> resourceValues,
                                                String sourceType, String sourceValue, String controlLevel) {
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "#updateDescribeAndModifyResource" + tenantId);
        try {
            describeLogicService.update(objectDescribe);
            stopWatch.lap("updateDescribe");
            modifyResource(tenantId, objectDescribe.getApiName(), MtResource.RESOURCE_TYPE_FILED, Lists.newArrayList(resourceValues),
                    controlLevel, sourceType, sourceValue);
            stopWatch.lap("modifyResource");
        } finally {
            stopWatch.logSlow(1000);
        }
    }

    @Override
    public Integer countByResourceType(User user, String resourceParentValue, String resourceType) {
        Query query = buildSearchQuery(user.getTenantId(), resourceParentValue, resourceType, null, null, null);
        return repository.findCountOnly(user, query, MtResource.class);
    }
}
