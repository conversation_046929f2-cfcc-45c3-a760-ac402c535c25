package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.domain.ActionDomainPlugin;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.predef.domain.IncrementUpdateActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.facade.IncrementUpdateActionServiceFacade;
import com.facishare.paas.appframework.core.predef.handler.incrementupdate.IncrementUpdateHandler;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.metadata.MetaDataActionService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDataMerger;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.DataConflictsResult;
import com.facishare.paas.appframework.metadata.dto.DataSnapshotResult;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchResult;
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.transaction.tcc.api.context.GlobalTransactionApplicationData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.*;

import static com.facishare.paas.appframework.core.model.RequestContext.Biz.ApprovalFlow;

/**
 * Created by zhouwr on 2019/4/4
 */
public class StandardIncrementUpdateAction extends PreDefineAction<StandardIncrementUpdateAction.Arg, StandardIncrementUpdateAction.Result> {

    protected IObjectData objectData;
    protected IObjectData dbObjectData;
    private DataSnapshotResult snapshotResult;
    private List<IDuplicatedSearch> duplicatedSearchList;
    private boolean skipDuplicationSearch = false;

    private IncrementUpdateActionServiceFacade incrementUpdateActionServiceFacade;

    protected boolean writeDB;

    @Override
    protected final boolean skipDomainPlugin(String method) {
        if (ActionDomainPlugin.AFTER.equals(method)) {
            return RequestUtil.isUseSnapshotForApproval();
        }
        return false;
    }

    @Override
    protected final List<String> getRecordTypes() {
        if (Objects.nonNull(dbObjectData)) {
            return Lists.newArrayList(dbObjectData.getRecordType());
        }
        return null;
    }

    @Override
    protected final IncrementUpdateActionDomainPlugin.Arg buildDomainPluginArg(String method, List<String> recordTypeList) {
        return IncrementUpdateActionDomainPlugin.Arg.builder()
                .objectData(ObjectDataDocument.of(objectData))
                .dbObjectData(ObjectDataDocument.of(dbObjectData))
                .useSnapshotForApproval(RequestUtil.isUseSnapshotForApproval())
                .build();
    }

    @Override
    protected final void processDomainPluginResult(String method, DomainPlugin.Arg pluginArg, DomainPlugin.Result pluginResult) {
        IncrementUpdateActionDomainPlugin.Result updatePluginResult = (IncrementUpdateActionDomainPlugin.Result) pluginResult;
        //合并插件返回结果
        ObjectDataMerger.builder()
                .origMasterData(objectData)
                .masterDataToUpdate(updatePluginResult.objectDataToUpdate())
                .build()
                .doMerge();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        if (applyDataPrivilegeCheck()) {
            return Lists.newArrayList(arg.getData().getId());
        }
        return null;
    }

    @Override
    protected void doDataPrivilegeCheck() {
        if (skipBaseValidate()) {
            return;
        }
        serviceFacade.doDataPrivilegeCheck(actionContext.getUser(), dataList, objectDescribe,
                StandardAction.Edit.getFunPrivilegeCodes().get(0));
    }

    private boolean applyDataPrivilegeCheck() {
        if (AppFrameworkConfig.isApplyDataPrivilegeCheckBlackDescribeApiName(actionContext.getObjectApiName())) {
            return false;
        }
        return BooleanUtils.isTrue(actionContext.<Boolean>getAttribute(RequestContext.Attributes.APPLY_DATA_PRIVILEGE_CHECK));
    }

    @Override
    protected void init() {
        super.init();

        if (ObjectDescribeExt.of(objectDescribe).isChangeOrderObject()) {
            throw new ValidateException(I18NExt.text(I18NKey.UNABLE_TO_UPDATE_CHANGE_ORDER_DATA));
        }

        incrementUpdateActionServiceFacade = serviceFacade.getBean(IncrementUpdateActionServiceFacade.class);

        objectData = parseData();
        stopWatch.lap("parseData");

        ObjectDataExt.correctValue(actionContext.getUser(), Lists.newArrayList(objectData), objectDescribe);
        stopWatch.lap("correctValue");

        //gdpr处理
        dealGdpr();
        stopWatch.lap("dealGdpr");

        //校验币种是否存在
        checkCurrencyOption();
        stopWatch.lap("checkCurrencyOption");

        dbObjectData = findData();
        stopWatch.lap("findData");

        //校验数据的版本号是不是最新的
        checkDataVersion();
        stopWatch.lap("checkDataVersion");

        extractAbstractOfRichText(objectData, objectDescribe);
        stopWatch.lap("extractAbstractOfRichText");

        calculateForIncrementUpdate();
        stopWatch.lap("calculateForIncrementUpdate");

        initDuplicatedSearch();
        stopWatch.lap("initDuplicatedSearch");

        //临时文件转正式文件
        serviceFacade.processData(objectDescribe, Lists.newArrayList(objectData));
        stopWatch.lap("processData");
    }

    @Override
    protected void beforeGlobalTransactionCompletion(GlobalTransactionApplicationData globalTransactionApplicationData) {
        super.beforeGlobalTransactionCompletion(globalTransactionApplicationData);
        globalTransactionApplicationData.setAttribute(GlobalTransactionConstant.OBJECT_SAVE_WRITE_DB_FLAG, writeDB);
    }

    private void initDuplicatedSearch() {
        if (!isDuplicateSearch()) {
            skipDuplicationSearch = true;
            return;
        }
        duplicatedSearchList = serviceFacade.getDuplicateSearchListByType(actionContext.getTenantId(), objectDescribe.getApiName(), IDuplicatedSearch.Type.NEW);
        if (CollectionUtils.empty(duplicatedSearchList)) {
            skipDuplicationSearch = true;
        }
    }

    private void calculateForIncrementUpdate() {
        serviceFacade.calculateForBatchEditData(actionContext.getUser(), Lists.newArrayList(objectData), Lists.newArrayList(dbObjectData), objectDescribe);
    }

    @Override
    protected void before(Arg arg) {
        if (CollectionUtils.empty(arg.getData())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        super.before(arg);
        stopWatch.lap("super.before");

        validateDataType();
        stopWatch.lap("validateDataType");

        // 校验日期范围字段值
        checkDateRangeFieldValue();
        stopWatch.lap("checkDateRangeFieldValue");
        //校验查重规则
        checkDuplicate();
        stopWatch.lap("checkDuplicate");

        //使用快照的更新，修改记录默认隐藏
        if (RequestUtil.isUseSnapshotForApproval()) {
            RequestUtil.setModifyLogHidden();
        }
    }

    private void checkDateRangeFieldValue() {
        List<String> errorMsg = ObjectDataExt.of(objectData).validateDateRangeField(objectDescribe);
        if (CollectionUtils.notEmpty(errorMsg)) {
            throw new ValidateException(errorMsg.get(0));
        }
    }

    protected void validateDataType() {
        //校验主对象
        serviceFacade.validateDataType(objectDescribe, Lists.newArrayList(objectData), actionContext.getUser());
    }

    private void dealGdpr() {
        if (RequestUtil.isOpenAPIRequest()) {
            if (infraServiceFacade.validateGdprCompliance(actionContext.getUser(),
                    RequestContext.OPENAPI_PEER_NAME, objectDescribe.getApiName(), objectData.getId())) {
                throw new ValidateException(I18N.text(I18NKey.GDPR_UNABLE_OPERATION, RequestContext.OPENAPI_PEER_NAME));
            }
            List<String> needFilterGdprFields = infraServiceFacade.needFilterGdprFields(actionContext.getUser(), objectDescribe.getApiName(), RequestContext.OPENAPI_PEER_NAME);
            if (CollectionUtils.notEmpty(needFilterGdprFields)) {
                ObjectDataExt.of(objectData).remove(Sets.newHashSet(needFilterGdprFields));

            }
        }
    }


    /**
     * 提取富文本的摘要信息
     */
    private void extractAbstractOfRichText(IObjectData objectData, IObjectDescribe describe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        if (describeExt.isSlaveObject()) {
            return;
        }

        ObjectDataExt.of(objectData).extractAbstractOfRichText(describeExt.getRichTextFields());
    }

    protected void checkDuplicate() {
        IncrementUpdateHandler.Arg handlerArg = (IncrementUpdateHandler.Arg) getHandlerArg(null);
        incrementUpdateActionServiceFacade.checkDuplicate(actionContext.getUser(), handlerArg);
    }

    private void checkCurrencyOption() {
        ObjectDataExt.of(objectData).checkCurrencyOption(objectDescribe);
    }

    protected void checkDataVersion() {
        processDataConflicts();
        ObjectDataExt.checkDataVersion(objectData, dbObjectData);
    }

    private void processDataConflicts() {
        if (!needProcessDataConflicts() || !ObjectDataExt.checkIfDataVersionConflict(objectData, dbObjectData)) {
            return;
        }

        DataConflictsResult dataConflictsResult = serviceFacade.processDataConflicts(actionContext.getUser(), objectDescribe, dbObjectData, objectData);
        if (Objects.isNull(dataConflictsResult)) {
            return;
        }

        BaseObjectSaveAction.DataConflicts conflicts = BaseObjectSaveAction.DataConflicts.builder()
                .fields(dataConflictsResult.getFields())
                .lastData(ObjectDataDocument.of(dataConflictsResult.getLastData()))
                .currentData(ObjectDataDocument.of(dataConflictsResult.getCurrentData()))
                .build();
        Result result = Result.builder()
                .dataConflicts(conflicts)
                .versionCheckBlocked(true)
                .build();
        throw new AcceptableValidateException(result);
    }

    private boolean needProcessDataConflicts() {
        return BooleanUtils.isTrue(arg.getProcessDataConflicts());
    }

    @Override
    protected Result doAct(Arg arg) {
        if (RequestUtil.isUseSnapshotForApproval()) {
            saveDataSnapshot();
            stopWatch.lap("saveDataSnapshot");
        } else {
            updateData();
            stopWatch.lap("updateData");
            updateDuplicateSearchData();
            stopWatch.lap("updateDuplicateSearchData");
        }
        return buildResult();
    }

    /**
     * 更新查询重相关的状态
     * <p>
     * 1 将数据写入redis
     * 2 直接解锁
     */
    private void updateDuplicateSearchData() {
        if (skipDuplicationSearch) {
            return;
        }

        try {
            IObjectData afterData = ObjectDataExt.of(ObjectDataExt.of(objectData).copy()).merge(dbObjectData).getObjectData();
            serviceFacade.handleDuplicateSearchRule(duplicatedSearchList, Collections.singletonList(afterData), objectDescribe, actionContext.getUser());
        } catch (Exception e) {
            log.error("updateDuplicateSearchData error ", e);
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        recordLog();
        stopWatch.lap("recordLog");

        return super.after(arg, result);
    }

    @Override
    protected void finallyDo() {
        infraServiceFacade.unlock(CacheContext.getContext().removeCache(ContextCacheKeys.DUPLICATE_SEARCH_LOCK));
        stopWatch.lap("unlockDuplicateSearch");
        super.finallyDo();
    }

    private Result buildResult() {
        Result result = Result.builder().success(true).writeDB(writeDB).build();
        if (snapshotResult != null) {
            //返回快照供审批流展示详情使用
            Map<String, Object> mergedSnapshot = snapshotResult.getMergedMasterSnapshot();
            result.setSnapshot(mergedSnapshot);
        }
        return result;
    }

    protected IObjectData parseData() {
        IObjectData objectData = arg.getData().toObjectData();
        objectData.setTenantId(actionContext.getTenantId());
        objectData.setDescribeApiName(objectDescribe.getApiName());

        //移除不支持编辑的字段
        ObjectDataExt.of(objectData).removeFieldsNotSupportEdit(objectDescribe);

        return objectData;
    }

    @Override
    protected boolean skipQueryRelateTeam() {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SKIP_QUERY_RELATE_TEAM_WHEN_UPDATE_EI, actionContext.getTenantId());
    }

    private IObjectData findData() {
        if (CollectionUtils.notEmpty(dataList)) {
            return dataList.get(0);
        }
        //不查相关团队
        if (skipQueryRelateTeam()) {
            return serviceFacade.findObjectDataIgnoreRelevantTeam(actionContext.getUser(), objectData.getId(), objectData.getDescribeApiName());
        }
        return serviceFacade.findObjectData(actionContext.getUser(), objectData.getId(), objectData.getDescribeApiName());
    }

    private void saveDataSnapshot() {
        RequestContext.BizInfo bizInfo = RequestContext.BizInfo.builder()
                .biz(ApprovalFlow.getCode())
                .otherBizId(RequestUtil.getOtherBizId())
                .bizId(RequestUtil.getBizId())
                .build();
        snapshotResult = infraServiceFacade.createSnapshotWithData(actionContext.getUser(), objectData,
                dbObjectData, objectDescribe, bizInfo);
    }

    protected void updateData() {
        IObjectData updateData = ObjectDataExt.of(objectData).copy();
        Map<String, Object> updateFieldMap = ObjectDataExt.of(updateData).toMap();
        log.debug("updateData fieldMap:{}", updateFieldMap);
        serviceFacade.updateWithMap(actionContext.getUser(), updateData, updateFieldMap,
                MetaDataActionService.UpdateAttributes.builder().actionType("incrementUpdateAction").build());
        writeDB = true;
    }

    protected void recordLog() {
        Map<String, Object> diffMap;
        IObjectData dbData = ObjectDataExt.of(dbObjectData).copy();
        IObjectData data = ObjectDataExt.of(objectData).copy();
        if (snapshotResult != null) {
            diffMap = snapshotResult.getDiffMap();
            //将快照合并到dbData中供修改记录使用
            ObjectDataExt.of(dbData).putAll(snapshotResult.getSnapshot().getMasterSnapshot());
        } else {
            diffMap = ObjectDataExt.of(dbData).diff(data, objectDescribe);
        }
        String bizId = RequestContextManager.getContext().getBizId();

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> serviceFacade.log(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, objectDescribe, data,
                diffMap, dbData, null, null, bizId, getLogExtendsInfo()));
        parallelTask.run();
    }


    private Map<String, Object> getLogExtendsInfo() {
        Map<String, Object> map = Maps.newHashMap();
        map.put(LogInfo.TRIGGER_WORK_FLOW, !Boolean.FALSE.equals(actionContext.getAttribute(RequestContext.Attributes.TRIGGER_WORK_FLOW)));
        map.put(LogInfo.TRIGGER_APPROVAL_FLOW, !Boolean.FALSE.equals(actionContext.getAttribute(RequestContext.Attributes.TRIGGER_FLOW)));
        return map;
    }


    // 查询重复数据
    private List<DuplicateSearchResult.DuplicateData> getDuplicateData(IObjectData objectData) {
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(actionContext.getTenantId(), objectDescribe.getApiName())) {
            return serviceFacade.searchDuplicateDataByType(actionContext.getUser(), objectData, IDuplicatedSearch.Type.NEW, objectDescribe, duplicatedSearchList);
        }
        return serviceFacade.searchDuplicateDataByType(actionContext.getUser(), objectData, IDuplicatedSearch.Type.NEW, objectDescribe);
    }


    // 是否查重
    protected boolean isDuplicateSearch() {
        BaseObjectSaveAction.OptionInfo optionInfo = arg.getOptionInfo();
        return ObjectUtils.isNotEmpty(optionInfo) && Objects.equals(optionInfo.getIsDuplicateSearch(), Boolean.TRUE);
    }

    @Override
    protected Handler.Arg<Arg> buildHandlerArg(SimpleHandlerDescribe handlerDescribe) {
        IncrementUpdateHandler.Arg handlerArg = new IncrementUpdateHandler.Arg();
        handlerArg.setObjectData(ObjectDataDocument.of(objectData));
        handlerArg.setDbObjectData(ObjectDataDocument.of(dbObjectData));
        handlerArg.setObjectDescribe(objectDescribe);
        handlerArg.setDuplicatedSearchList(duplicatedSearchList);
        handlerArg.setSkipDuplicateSearchCheck(skipDuplicationSearch);
        handlerArg.setSnapshotResult(snapshotResult);
        return handlerArg;
    }

    @Override
    protected void processHandlerResult(HandlerContext handlerContext, Handler.Arg<Arg> handlerArg, Handler.Result<Result> handlerResult) {
        super.processHandlerResult(handlerContext, handlerArg, handlerResult);
        if (handlerResult instanceof IncrementUpdateHandler.Result) {
            IncrementUpdateHandler.Result result = (IncrementUpdateHandler.Result) handlerResult;
            if (Objects.nonNull(result.getSkipDuplicateSearchCheck())) {
                skipDuplicationSearch = result.getSkipDuplicateSearchCheck();
            }
            if (Objects.nonNull(result.getDuplicatedSearchList())) {
                duplicatedSearchList = result.getDuplicatedSearchList();
            }
            if (Objects.nonNull(result.getSnapshotResult())) {
                snapshotResult = result.getSnapshotResult();
            }
            Optional.ofNullable(result.getInterfaceResult())
                    .map(Result::getWriteDB)
                    .ifPresent(it -> writeDB = it);
        }
    }

    @Data
    public static class Arg {
        private ObjectDataDocument data;
        private BaseObjectSaveAction.OptionInfo optionInfo;

        private Boolean processDataConflicts;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private boolean success;
        private Map<String, Object> snapshot;
        private Map<String, List<String>> duplicateDataMap;
        private Boolean writeDB;

        //是否被版本号校验拦截
        private Boolean versionCheckBlocked;
        // 冲突字段
        private BaseObjectSaveAction.DataConflicts dataConflicts;
    }
}
