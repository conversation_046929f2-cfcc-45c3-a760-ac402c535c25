package com.facishare.paas.appframework.core.predef.action

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.metadata.ObjectMappingService
import com.facishare.paas.appframework.metadata.repository.model.MtConvertRule
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.ObjectMappingRuleInfo
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.api.Language
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Shared
import spock.lang.Specification
import java.lang.reflect.Field

class StandardConvertActionTest extends Specification {
    StandardConvertAction standardConvertAction
    ServiceFacade serviceFacade = Mock(ServiceFacade)
    ActionContext actionContext = Mock(ActionContext)
    InfraServiceFacade infraServiceFacade = Mock(InfraServiceFacade)
    SpringBeanHolder springBeanHolder = Mock(SpringBeanHolder)
    @Shared
    String describeJson = '''{"fields":{"returned_goods_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"退货单金额(元)","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"ReturnedGoodsInvoiceObj","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["normal"]}]},{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["in_change"]}]}],"define_type":"package","is_single":false,"index_name":"d_11","field_api_name":"order_id","max_length":14,"is_index":true,"is_active":true,"create_time":1562060912729,"count_type":"sum","count_field_api_name":"returned_goods_inv_amount","label":"退货单金额(元)","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"returned_goods_amount","count_field_type":"currency","_id":"5d3abb7e319d19982fcc968b","is_index_field":false,"config":{"edit":1,"enable":0,"attrs":{"wheres":0,"is_required":1,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"},"refund_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"已退款金额（元）","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"RefundObj","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["normal"]}]},{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["in_change"]}]}],"define_type":"package","is_single":false,"index_name":"d_12","field_api_name":"order_id","max_length":14,"is_index":true,"is_active":true,"create_time":1562060912729,"count_type":"sum","count_field_api_name":"refunded_amount","label":"已退款金额(元)","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"refund_amount","count_field_type":"currency","_id":"5d3abb7e319d19982fcc968c","is_index_field":false,"config":{"edit":1,"enable":0,"attrs":{"wheres":0,"is_required":1,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"},"opportunity_id":{"describe_api_name":"SalesOrderObj","description":"商机名称","is_unique":false,"type":"object_reference","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":2,"operator":"EQ","field_name":"account_id","field_values":["$account_id$"]}]}],"define_type":"package","cascade_parent_api_name":"account_id","is_single":false,"index_name":"s_11","is_index":true,"is_active":false,"create_time":*************,"label":"商机名称","target_api_name":"OpportunityObj","target_related_list_name":"opportunity_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"api_name":"opportunity_id","_id":"5da70441319d19799edf068c","is_index_field":false,"config":{"edit":1,"enable":1,"display":0,"attrs":{"target_related_list_label":1,"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"status":"new"},"new_opportunity_id":{"describe_api_name":"SalesOrderObj","description":"商机2.0","is_unique":false,"type":"object_reference","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":2,"operator":"EQ","field_name":"account_id","field_values":["$account_id$"]}]}],"define_type":"package","cascade_parent_api_name":"account_id","is_single":false,"index_name":"s_8","is_index":true,"is_active":true,"create_time":*************,"label":"商机2.0","target_api_name":"NewOpportunityObj","target_related_list_name":"new_opportunity_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"api_name":"new_opportunity_id","_id":"5da70519319d19799edf068d","is_index_field":false,"config":{"edit":1,"enable":1,"attrs":{"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"status":"new"},"lock_rule":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"锁定规则","is_unique":false,"default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"5d1b2870319d19c15d3f9c6d","is_extend":false,"is_index_field":false,"is_single":false,"index_name":"s_1","help_text":"锁定规则","status":"new"},"settle_type":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":false,"create_time":1562060912676,"description":"结算方式","is_unique":false,"label":"结算方式","type":"select_one","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"settle_type","options":[{"resource_bundle_key":"","label":"预付","value":"1","config":{"edit":0,"enable":0,"remove":0}},{"resource_bundle_key":"","label":"现付","value":"2","config":{"edit":0,"enable":0,"remove":0}},{"resource_bundle_key":"","label":"赊销","value":"3","config":{"edit":0,"enable":0,"remove":0}}],"define_type":"package","option_id":"ce581138cb14e66193091162d8e2ecc8","_id":"5d1b2870319d19c15d3f9c6e","is_index_field":false,"is_single":false,"config":{"add":0,"edit":1,"enable":0,"display":0,"attrs":{"is_required":0,"options":0,"label":1,"help_text":1}},"index_name":"s_2","status":"released"},"current_level":{"describe_api_name":"SalesOrderObj","default_is_expression":false,"description":"已审批节点数量","is_unique":false,"type":"number","decimal_places":0,"default_to_zero":true,"is_required":false,"define_type":"system","is_single":false,"index_name":"d_1","max_length":14,"is_index":false,"is_active":false,"create_time":1562060912676,"length":14,"default_value":"0","label":"已审批节点数量","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"current_level","_id":"5d1b2870319d19c15d3f9c6f","is_index_field":false,"config":{"edit":0,"enable":0,"display":0,"attrs":{"is_required":0,"help_text":1}},"round_mode":4,"status":"released"},"discount":{"describe_api_name":"SalesOrderObj","default_is_expression":false,"is_index":false,"is_active":true,"create_time":1562060912676,"pattern":"","description":"整单折扣","is_unique":false,"default_value":"100","label":"整单折扣","type":"percentile","is_abstract":null,"field_num":null,"default_to_zero":true,"is_need_convert":false,"is_required":false,"api_name":"discount","define_type":"package","_id":"5d1b2870319d19c15d3f9c70","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1}},"index_name":"d_2","status":"released"},"order_time":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912676,"description":"下单日期","is_unique":false,"label":"下单日期","time_zone":"GMT+8","type":"date","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":true,"api_name":"order_time","define_type":"package","date_format":"yyyy-MM-dd","_id":"5d1b2870319d19c15d3f9c71","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":0,"attrs":{"is_required":0,"default_value":1,"label":1,"help_text":1}},"index_name":"l_1","status":"released"},"receivable_amount":{"describe_api_name":"SalesOrderObj","expression_type":"js","return_type":"currency","description":"待回款金额（元）","is_unique":false,"type":"formula","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"package","is_single":false,"index_name":"d_3","max_length":14,"is_index":true,"expression":"$order_amount$+$refund_amount$-$payment_amount$-$returned_goods_amount$","is_active":true,"create_time":1562060912676,"label":"待回款金额（元）","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"receivable_amount","_id":"5d1b2870319d19c15d3f9c72","is_index_field":false,"config":{"edit":1,"enable":0,"attrs":{"is_required":1,"formula":1,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"},"logistics_status":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"发货状态","is_unique":false,"label":"发货状态","type":"select_one","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"logistics_status","options":[{"resource_bundle_key":"","label":"待发货","value":"1","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"部分发货","value":"2","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已发货","value":"3","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"部分收货","value":"4","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已收货","value":"5","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"package","option_id":"a7ad9d6143f2ea3a984dc74c3dade3fc","_id":"5d1b2870319d19c15d3f9c73","is_index_field":false,"is_single":false,"config":{"edit":0},"index_name":"s_3","status":"released"},"ship_to_id":{"describe_api_name":"SalesOrderObj","description":"收货人","is_unique":false,"type":"object_reference","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":2,"operator":"EQ","field_name":"account_id","field_values":["$account_id$"]}]}],"define_type":"package","cascade_parent_api_name":"account_id","is_single":false,"index_name":"s_4","is_index":true,"is_active":true,"create_time":*************,"label":"收货人","target_api_name":"ContactObj","target_related_list_name":"ship_to_salesorder_list","is_abstract":null,"field_num":null,"target_related_list_label":"收货人订单列表","action_on_target_delete":"cascade_delete","is_need_convert":false,"api_name":"ship_to_id","_id":"5d1b2870319d19c15d3f9c74","is_index_field":false,"config":{"edit":1,"enable":1,"attrs":{"target_related_list_label":1,"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"status":"released"},"order_status":{"describe_api_name":"SalesOrderObj","is_use_value":true,"description":"状态（原）","is_unique":false,"type":"select_one","is_required":false,"options":[{"resource_bundle_key":"","label":"确认中","value":"6","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已确认","value":"7","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已驳回","value":"8","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已撤回","value":"9","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已发货","value":"10","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已收货","value":"11","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"作废","value":"99","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"system","option_id":"c5eec7d2d4e9a4a458a488e904ea0d84","is_single":false,"index_name":"s_5","is_index":true,"expression":"CASE($life_status$,'invalid','99','ineffective','8','under_review','6',CASE($logistics_status$,'3','10','5','11','7'))","is_active":true,"create_time":1562060912697,"label":"状态（原）","is_abstract":null,"field_num":null,"is_need_convert":false,"is_need_calculate":true,"api_name":"order_status","_id":"5d1b2870319d19c15d3f9c75","is_index_field":false,"config":{"edit":0,"enable":0,"display":1,"attrs":{"is_required":0,"help_text":1}},"status":"released"},"ship_to_add":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912698,"pattern":"","description":"收货地址","is_unique":false,"label":"收货地址","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"ship_to_add","define_type":"package","_id":"5d1b2870319d19c15d3f9c76","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1}},"index_name":"t_1","max_length":256,"status":"released"},"extend_obj_data_id":{"describe_api_name":"SalesOrderObj","default_is_expression":false,"pattern":"","description":"extend_obj_data_id","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"system","is_extend":false,"is_single":false,"index_name":"t_2","max_length":256,"is_index":false,"is_active":true,"create_time":1562060912698,"default_value":"","label":"extend_obj_data_id","is_abstract":null,"field_num":null,"api_name":"extend_obj_data_id","_id":"5d1b2870319d19c15d3f9c77","is_index_field":false,"help_text":"","status":"released"},"life_status_before_invalid":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912698,"pattern":"","description":"作废前生命状态","is_unique":false,"label":"作废前生命状态","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"life_status_before_invalid","define_type":"package","_id":"5d1b2870319d19c15d3f9c78","is_extend":false,"is_index_field":false,"is_single":false,"index_name":"t_3","help_text":"作废前生命状态","max_length":256,"status":"new"},"order_amount":{"describe_api_name":"SalesOrderObj","default_is_expression":true,"description":"销售订单金额(元)","is_unique":false,"type":"currency","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"package","is_single":false,"index_name":"d_4","max_length":14,"is_index":true,"is_active":true,"create_time":1562060912698,"length":18,"default_value":"$product_amount$*$discount$","label":"销售订单金额(元)","currency_unit":"￥","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"order_amount","_id":"5d1b2870319d19c15d3f9c79","is_index_field":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1,"max_length":1,"decimal_places":1}},"round_mode":4,"status":"released"},"owner_department":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912698,"pattern":"","description":"负责人主属部门","is_unique":false,"label":"负责人主属部门","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"owner_department","define_type":"package","_id":"5d1b2870319d19c15d3f9c7a","is_index_field":false,"is_single":false,"index_name":"owner_dept","max_length":100,"status":"released"},"signature_attachment":{"describe_api_name":"SalesOrderObj","file_amount_limit":10,"is_index":true,"is_active":true,"create_time":1562060912698,"description":"电子签章附件","is_unique":false,"label":"电子签章附件","type":"file_attachment","is_abstract":null,"field_num":null,"file_size_limit":10485760,"is_required":false,"api_name":"signature_attachment","define_type":"package","_id":"5d1b2870319d19c15d3f9c7b","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":1,"attrs":{"is_required":0,"help_text":1}},"index_name":"a_1","support_file_types":[],"status":"released"},"plan_payment_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"已计划回款金额","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"PaymentPlanObj","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["normal"]}]}],"define_type":"package","is_single":false,"index_name":"d_5","field_api_name":"order_id","max_length":14,"is_index":true,"is_active":true,"create_time":1562060912698,"count_type":"sum","count_field_api_name":"plan_payment_amount","label":"已计划回款金额","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"plan_payment_amount","count_field_type":"currency","_id":"5d1b2870319d19c15d3f9c7c","is_index_field":false,"config":{"edit":1,"enable":1,"attrs":{"wheres":1,"is_required":0,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"},"lock_status":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912706,"description":"锁定状态","is_unique":false,"default_value":"0","label":"锁定状态","type":"select_one","is_abstract":null,"field_num":null,"is_required":false,"api_name":"lock_status","options":[{"label":"未锁定","value":"0","config":{"edit":1,"enable":1,"remove":1}},{"label":"锁定","value":"1","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"package","option_id":"f8f6e3b07ca18b590c9a3cf86e014576","_id":"5d1b2870319d19c15d3f9c7d","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"s_6","status":"new"},"resource":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"来源","is_unique":false,"label":"来源","type":"select_one","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"resource","options":[{"resource_bundle_key":"","label":"CRM","value":"0","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"订货通","value":"1","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"package","option_id":"38d27afa1afa05bfa3f1fbf6b1c74b60","_id":"5d1b2870319d19c15d3f9c7e","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"display":1,"attrs":{"is_required":0,"help_text":1}},"index_name":"s_7","status":"released"},"submit_time":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"提交时间","is_unique":false,"label":"提交时间","time_zone":"GMT+8","type":"date_time","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"submit_time","define_type":"package","date_format":"yyyy-MM-dd HH:mm","_id":"5d1b2870319d19c15d3f9c7f","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"attrs":{"is_required":1,"label":1,"help_text":1}},"index_name":"l_2","status":"released"},"quote_id":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"报价单","is_unique":false,"label":"报价单","target_api_name":"QuoteObj","type":"object_reference","target_related_list_name":"quote_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"is_required":false,"api_name":"quote_id","define_type":"package","_id":"5d1b2870319d19c15d3f9c81","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"index_name":"s_9","status":"new"},"payment_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"已回款金额(元)","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"OrderPaymentObj","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["normal"]}]}],"define_type":"package","is_single":false,"index_name":"d_6","field_api_name":"order_id","is_index":true,"is_active":true,"create_time":*************,"count_type":"sum","length":14,"count_field_api_name":"payment_amount","label":"已回款金额(元)","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"payment_amount","count_field_type":"currency","_id":"5d1b2870319d19c15d3f9c82","is_index_field":false,"config":{"edit":1,"enable":0,"attrs":{"wheres":0,"is_required":1,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"},"delivery_comment":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"","description":"发货备注","is_unique":false,"label":"发货备注","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"delivery_comment","define_type":"package","_id":"5d1b2870319d19c15d3f9c83","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"t_4","max_length":1000,"status":"released"},"relevant_team":{"describe_api_name":"SalesOrderObj","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","is_unique":false,"define_type":"package","description":"成员员工","label":"成员员工","type":"employee","is_single":false,"index_name":"stringList_2","help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"is_unique":false,"define_type":"package","description":"成员角色","label":"成员角色","type":"select_one","index_name":"string_4","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"is_unique":false,"define_type":"package","description":"成员权限类型","label":"成员权限类型","type":"select_one","index_name":"string_5","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":*************,"is_unique":false,"description":"","label":"相关团队","type":"embedded_object_list","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"5d1b2870319d19c15d3f9c84","is_index_field":false,"is_single":false,"index_name":"s_10","help_text":"相关团队","status":"released"},"confirmed_receive_date":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"收货时间","is_unique":false,"label":"收货时间","time_zone":"GMT+8","type":"date_time","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"confirmed_receive_date","define_type":"package","date_format":"yyyy-MM-dd HH:mm","_id":"5d1b2870319d19c15d3f9c85","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"l_3","status":"released"},"delivery_date":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"交货日期","is_unique":false,"label":"交货日期","time_zone":"GMT+8","type":"date","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"delivery_date","define_type":"package","date_format":"yyyy-MM-dd","_id":"5d1b2870319d19c15d3f9c86","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1}},"index_name":"l_4","status":"released"},"price_book_id":{"describe_api_name":"SalesOrderObj","description":"价目表","is_unique":false,"type":"object_reference","is_required":false,"define_type":"package","cascade_parent_api_name":"account_id","is_single":false,"index_name":"s_12","is_index":true,"is_active":false,"create_time":*************,"label":"价目表","target_api_name":"PriceBookObj","target_related_list_name":"price_book_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"api_name":"price_book_id","_id":"5d1b2870319d19c15d3f9c88","is_index_field":false,"config":{"edit":1,"enable":0,"display":0,"attrs":{"target_related_list_label":1,"wheres":0,"is_required":1,"default_value":0,"label":1,"help_text":1}},"status":"new"},"name":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"prefix":"{yyyy}{mm}{dd}-","description":"销售订单编号","is_unique":true,"serial_number":6,"start_number":1,"label":"销售订单编号","type":"auto_number","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":true,"api_name":"name","define_type":"package","_id":"5b066c5f9e787b82577aebe4","postfix":"","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":0,"attrs":{"is_required":0,"label":1,"help_text":1}},"index_name":"name","status":"released"},"bill_money_to_confirm":{"describe_api_name":"SalesOrderObj","description":"待确认的开票金额","is_unique":false,"type":"currency","decimal_places":2,"is_required":false,"define_type":"system","is_single":false,"index_name":"d_7","max_length":14,"is_index":false,"is_active":true,"create_time":*************,"length":18,"label":"待确认的开票金额","currency_unit":"￥","is_abstract":true,"field_num":null,"is_need_convert":false,"api_name":"bill_money_to_confirm","_id":"5d1b2870319d19c15d3f9c89","is_index_field":false,"config":{"edit":0,"enable":0,"display":0,"attrs":{"is_required":0,"help_text":1}},"round_mode":4,"status":"released"},"delivered_amount_sum":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":false,"create_time":*************,"length":14,"description":"已发货金额","is_unique":false,"label":"已发货金额","type":"number","is_abstract":null,"decimal_places":2,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"delivered_amount_sum","define_type":"package","_id":"5d1b2870319d19c15d3f9c8a","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":0,"display":0,"attrs":{"is_required":0,"default_value":1,"label":1,"help_text":1}},"index_name":"d_8","round_mode":4,"status":"released"},"payment_money_to_confirm":{"describe_api_name":"SalesOrderObj","description":"待确认的回款金额","is_unique":false,"type":"currency","decimal_places":2,"is_required":false,"define_type":"system","is_single":false,"index_name":"d_9","max_length":14,"is_index":false,"is_active":true,"create_time":*************,"length":18,"label":"待确认的回款金额","currency_unit":"￥","is_abstract":true,"field_num":null,"is_need_convert":false,"api_name":"payment_money_to_confirm","_id":"5d1b2870319d19c15d3f9c8b","is_index_field":false,"config":{"edit":0,"enable":0,"display":0,"attrs":{"is_required":0,"help_text":1}},"round_mode":4,"status":"released"},"remark":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"","description":"备注","is_unique":false,"label":"备注","type":"long_text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"remark","define_type":"package","_id":"5d1b2870319d19c15d3f9c8c","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1}},"index_name":"t_5","max_length":2000,"status":"released"},"promotion_id":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":false,"create_time":*************,"description":"促销活动","is_unique":false,"label":"促销活动","target_api_name":"PromotionObj","type":"object_reference","target_related_list_name":"promotion_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"is_required":false,"api_name":"promotion_id","define_type":"package","_id":"5d1b2870319d19c15d3f9c8d","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":0,"display":0,"attrs":{"target_related_list_label":1,"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"index_name":"s_13","status":"new"},"lock_user":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912717,"description":"加锁人","is_unique":false,"label":"加锁人","type":"employee","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"5d1b2870319d19c15d3f9c8e","is_extend":false,"is_index_field":false,"is_single":true,"index_name":"a_2","help_text":"加锁人","status":"new"},"invoice_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"已开票金额(元)","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"InvoiceApplicationObj","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["normal"]}]},{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["in_change"]}]}],"define_type":"package","is_single":false,"index_name":"d_10","field_api_name":"order_id","is_index":true,"is_active":true,"create_time":1562060912717,"count_type":"sum","length":0,"count_field_api_name":"invoice_applied_amount","label":"已开票金额(元)","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"invoice_amount","count_field_type":"currency","_id":"5d1b2870319d19c15d3f9c8f","is_index_field":false,"config":{"edit":1,"enable":0,"attrs":{"wheres":0,"is_required":1,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"},"shipping_warehouse_id":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":false,"create_time":1562060912717,"description":"订货仓库","is_unique":false,"label":"订货仓库","target_api_name":"WarehouseObj","type":"object_reference","target_related_list_name":"shipping_warehouse_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"is_required":false,"api_name":"shipping_warehouse_id","define_type":"package","_id":"5d1b2870319d19c15d3f9c90","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":0,"display":0,"attrs":{"target_related_list_label":1,"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"index_name":"s_14","status":"new"},"partner_id":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":false,"create_time":1562060912717,"description":"合作伙伴","is_unique":false,"label":"合作伙伴","target_api_name":"PartnerObj","type":"object_reference","target_related_list_name":"partner_salesorder_list","is_abstract":null,"field_num":null,"target_related_list_label":"订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"is_required":false,"api_name":"partner_id","define_type":"package","_id":"5d1b2870319d19c15d3f9c91","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"display":0,"attrs":{"target_related_list_label":1,"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"index_name":"s_15","status":"new"},"receipt_type":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912729,"pattern":"","description":"收款类型","is_unique":false,"label":"收款类型","type":"select_one","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"receipt_type","options":[{"resource_bundle_key":"","label":"保证金","value":"1","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"服务费","value":"2","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"贷款","value":"3","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"package","option_id":"cec07bca47160cb2dac1d64e3fb04751","_id":"5d1b2870319d19c15d3f9c92","is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"attrs":{"is_required":1,"options":1,"label":1,"help_text":1}},"index_name":"s_16","status":"released"},"out_resources":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":false,"create_time":1562060912737,"description":"外部来源","is_unique":false,"label":"外部来源","type":"select_one","is_abstract":null,"field_num":null,"is_required":false,"api_name":"out_resources","options":[{"not_usable":false,"label":"PRM","value":"partner","config":{}}],"define_type":"package","option_id":"37e59fdcc9cdb736d4c89da45675ec08","_id":"5d1b2870319d19c15d3f9c95","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"display":0,"attrs":{"options":0,"help_text":1}},"index_name":"s_17","status":"new"},"product_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"产品合计","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"SalesOrderProductObj","is_required":false,"define_type":"package","is_single":false,"index_name":"d_13","field_api_name":"order_id","max_length":14,"is_index":true,"is_active":true,"create_time":1562060912737,"count_type":"sum","count_field_api_name":"subtotal","label":"产品合计","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"product_amount","count_field_type":"currency","_id":"5d1b2870319d19c15d3f9c96","is_index_field":false,"config":{"edit":1,"enable":1,"attrs":{"wheres":1,"is_required":0,"label":1,"help_text":1,"max_length":1,"decimal_places":1}},"round_mode":4,"status":"released"},"owner":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912737,"length":8,"description":"负责人","is_unique":false,"label":"负责人","type":"employee","is_abstract":null,"decimal_places":0,"field_num":null,"is_need_convert":true,"is_required":true,"api_name":"owner","define_type":"package","_id":"5d1b2870319d19c15d3f9c97","is_index_field":false,"is_single":true,"config":{"edit":0,"enable":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"owner","round_mode":4,"status":"released"},"life_status":{"describe_api_name":"SalesOrderObj","description":"生命状态","is_unique":false,"type":"select_one","is_required":false,"options":[{"label":"未生效","value":"ineffective","config":{"edit":1,"enable":1,"remove":1}},{"label":"审核中","value":"under_review","config":{"edit":1,"enable":1,"remove":1}},{"label":"正常","value":"normal","config":{"edit":1,"enable":1,"remove":1}},{"label":"变更中","value":"in_change","config":{"edit":1,"enable":1,"remove":1}},{"label":"作废","value":"invalid","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"package","option_id":"18de2c4eae8cf7a180ffb0f175010e6f","is_extend":false,"is_single":false,"index_name":"s_18","is_index":true,"is_active":true,"create_time":1562060912747,"default_value":"normal","label":"生命状态","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"life_status","_id":"5d1b2870319d19c15d3f9c98","is_index_field":false,"config":{},"help_text":"生命状态","status":"released"},"is_user_define_work_flow":{"describe_api_name":"SalesOrderObj","is_active":true,"create_time":1562060912748,"description":"是否是自定义的工作流","is_unique":false,"default_value":false,"label":"是否是自定义的工作流","type":"true_or_false","is_abstract":true,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"is_user_define_work_flow","define_type":"system","_id":"5d1b2870319d19c15d3f9c99","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"display":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"b_1","status":"released"},"ship_to_tel":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912748,"pattern":"^[0-9+\\\\-;,]{0,100}$","description":"收货人电话","is_unique":false,"label":"收货人电话","type":"phone_number","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"ship_to_tel","define_type":"package","_id":"5d1b2870319d19c15d3f9c9a","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1}},"index_name":"p_1","max_length":100,"status":"released"},"work_flow_id":{"describe_api_name":"SalesOrderObj","is_index":false,"is_active":false,"create_time":1562060912748,"pattern":"","description":"自由流程","is_unique":false,"label":"自由流程","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"work_flow_id","define_type":"system","_id":"5d1b2870319d19c15d3f9c9b","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"display":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"t_6","max_length":100,"status":"released"},"record_type":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"record_type","is_unique":false,"label":"业务类型","type":"record_type","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":true,"api_name":"record_type","options":[{"is_active":true,"api_name":"default__c","description":"预设业务类型","label":"预设业务类型"}],"define_type":"package","option_id":"504eae3cf17c3d7223be4a3e0e893ecb","_id":"5d1b2870319d19c15d3f9c9c","is_index_field":false,"is_single":false,"index_name":"r_type","config":{},"status":"released"},"account_id":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"客户名称","is_unique":false,"label":"客户名称","target_api_name":"AccountObj","type":"object_reference","target_related_list_name":"account_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"is_required":true,"api_name":"account_id","define_type":"package","_id":"5d1b2870319d19c15d3f9c9d","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":0,"attrs":{"target_related_list_label":1,"wheres":1,"is_required":0,"default_value":0,"label":1,"help_text":1}},"index_name":"s_19","status":"new"},"commision_info":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"","description":"提成信息","is_unique":false,"label":"提成信息","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"commision_info","define_type":"package","_id":"5d1b2870319d19c15d3f9c9e","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1}},"index_name":"t_7","max_length":100,"status":"released"},"confirmed_delivery_date":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"发货时间","is_unique":false,"label":"发货时间","time_zone":"GMT+8","type":"date_time","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"confirmed_delivery_date","define_type":"package","date_format":"yyyy-MM-dd HH:mm","_id":"5d1b2870319d19c15d3f9c9f","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"l_5","status":"released"},"_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"_id","api_name":"_id","description":"_id","status":"released","index_name":"_id","create_time":1527155146247},"created_by":{"type":"employee","define_type":"system","is_index":true,"is_need_convert":true,"is_required":false,"is_unique":false,"is_single":true,"api_name":"created_by","status":"released","label":"创建人","is_active":true,"index_name":"crt_by","create_time":1527155146247,"description":""},"last_modified_by":{"type":"employee","define_type":"system","is_index":true,"is_need_convert":true,"is_required":false,"is_unique":false,"is_single":true,"api_name":"last_modified_by","status":"released","is_active":true,"index_name":"md_by","label":"最后修改人","create_time":1527155146247,"description":""},"package":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"package","api_name":"package","description":"package","status":"released","create_time":1527155146247,"index_name":"pkg"},"tenant_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":true,"is_unique":false,"max_length":200,"pattern":"","label":"tenant_id","api_name":"tenant_id","description":"tenant_id","status":"released","create_time":1527155146247,"index_name":"ei"},"object_describe_api_name":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":true,"is_unique":false,"max_length":200,"pattern":"","label":"object_describe_api_name","api_name":"object_describe_api_name","description":"object_describe_api_name","status":"released","index_name":"api_name","create_time":1527155146247},"version":{"type":"number","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"round_mode":4,"length":8,"decimal_places":0,"label":"version","api_name":"version","description":"version","status":"released","index_name":"version","create_time":1527155146247},"object_describe_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"object_describe_id","api_name":"object_describe_id","description":"object_describe_id","status":"released","index_name":"object_describe_id","create_time":1527155146247},"create_time":{"type":"date_time","define_type":"system","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"time_zone":"","date_format":"yyyy-MM-dd HH:mm:ss","label":"创建时间","api_name":"create_time","description":"create_time","status":"released","index_name":"crt_time","create_time":1527155146247},"last_modified_time":{"type":"date_time","define_type":"system","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"time_zone":"","date_format":"yyyy-MM-dd HH:mm:ss","label":"最后修改时间","api_name":"last_modified_time","description":"last_modified_time","status":"released","index_name":"md_time","create_time":1527155146247},"is_deleted":{"type":"true_or_false","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"label":"is_deleted","api_name":"is_deleted","description":"is_deleted","default_value":false,"status":"released","index_name":"is_del","create_time":1527155146247},"out_tenant_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"外部企业","api_name":"out_tenant_id","description":"out_tenant_id","status":"released","index_name":"o_ei","create_time":1527155146247,"config":{"display":0}},"out_owner":{"type":"employee","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"is_single":true,"api_name":"out_owner","index_name":"o_owner","status":"released","label":"外部负责人","config":{"display":1},"create_time":1527155146247,"description":""},"data_own_department":{"type":"department","define_type":"package","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"is_single":true,"api_name":"data_own_department","status":"released","label":"归属部门","is_active":true,"index_name":"data_owner_dept_id","create_time":1527155146247,"description":""}},"actions":{"Lock":{"tenant_id":"-100","action_class":"LockAction","action_id":"5d1b2870319d19c15d3f9ca6","describe_id":"5b0689ca9e787b86896a1a24","action_code":"Lock","source_type":"java_spring","label":"加锁"},"ChangeOwner":{"tenant_id":"-100","action_class":"ChangeOwnerAction","action_id":"5d1b2870319d19c15d3f9ca5","describe_id":"5b0689ca9e787b86896a1a24","action_code":"ChangeOwner","source_type":"java_spring"},"Unlock":{"tenant_id":"-100","action_class":"UnlockAction","action_id":"5d1b2870319d19c15d3f9ca4","describe_id":"5b0689ca9e787b86896a1a24","action_code":"Unlock","source_type":"java_spring","label":"解锁"},"ConfirmDelivery":{"tenant_id":"-100","action_class":"ConfirmDeliveryAction","action_id":"5d1b2870319d19c15d3f9ca3","describe_id":"5b0689ca9e787b86896a1a24","action_code":"ConfirmDelivery","source_type":"java_spring"},"AddDeliveryNote":{"tenant_id":"-100","action_class":"AddDeliveryNoteAction","action_id":"5d1b2870319d19c15d3f9ca2","describe_id":"5b0689ca9e787b86896a1a24","action_code":"AddDeliveryNote","source_type":"java_spring","label":"创建发货单"},"AddTeamMember":{"tenant_id":"-100","action_class":"AddTeamMemberAction","action_id":"5d1b2870319d19c15d3f9ca1","describe_id":"5b0689ca9e787b86896a1a24","action_code":"AddTeamMember","source_type":"java_spring","label":"添加团队成员"},"ConfirmReceive":{"tenant_id":"-100","action_class":"ConfirmReceiveAction","action_id":"5d1b2870319d19c15d3f9ca0","describe_id":"5b0689ca9e787b86896a1a24","action_code":"ConfirmReceive","source_type":"java_spring"}},"index_version":1,"_id":"5b0689ca9e787b86896a1a24","tenant_id":"78586","is_udef":null,"api_name":"SalesOrderObj","created_by":"-1000","last_modified_by":"-1000","display_name":"销售订单","package":"CRM","record_type":null,"is_active":true,"icon_path":null,"version":11,"release_version":"6.4","plural_name":null,"define_type":"package","is_deleted":false,"last_modified_time":*************,"create_time":1527155146247,"store_table_name":"biz_sales_order","module":null,"icon_index":null,"description":"","visible_scope":null}'''
    @Shared
    def field1 = new MtConvertRule.Fields(objectApiName: "source__c", fieldApiName: "select_one__c")
    @Shared
    def field2 = new MtConvertRule.Fields(objectApiName: "detail__c", fieldApiName: "select_one__c")
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    def setup() {
        infraServiceFacade.springBeanHolder >> springBeanHolder
        standardConvertAction = new StandardConvertAction('serviceFacade': serviceFacade, 'actionContext': actionContext,
                'infraServiceFacade': infraServiceFacade)
    }

    def "test before throw validateException"() {
        given:
        def arg = new AbstractConvertAction.Arg()
        arg.setSourceId('123')
        arg.setRuleApiName('rule__c')
        arg.setSourceApiName('source__c')
        standardConvertAction.arg = arg
        IObjectDescribe objectDescribe = new ObjectDescribeDocument(JSON.parseObject(describeJson)).toObjectDescribe()
        serviceFacade.findObject(*_) >> objectDescribe
        serviceFacade.findObjectWithoutCopy(*_) >> objectDescribe
        when:
        actionContext.getUser() >> User.systemUser('74255')
        MtConvertRule rule = new MtConvertRule()
        rule.setApiName('rule__c')
        rule.setActive(true)
        serviceFacade.findObjectDataByIdsExcludeInvalid(*_) >> [new ObjectData([id: '123', name: 'name'])]
        serviceFacade.findObjectsWithoutCopy(*_) >> ['master__c': new ObjectDescribe()]
        infraServiceFacade.findConvertRuleInInternalObjByApiName(_, _, _) >> [rule]
        serviceFacade.findDisplayNameByApiNames(*_) >> ['master__c': '主对象']
        standardConvertAction.before(arg)
        then:
        thrown(ValidateException)

    }

    def 'test init throw ObjectDataNotFoundException'() {
        given:
        def arg = new AbstractConvertAction.Arg()
        arg.setSourceId('123')
        arg.setRuleApiName('rule__c')
        arg.setSourceApiName('source__c')
        standardConvertAction.arg = arg
        when:
        actionContext.getUser() >> User.systemUser('74255')
        standardConvertAction.init()
        then:
        thrown(ObjectDataNotFoundException)
    }

    def 'test init'() {
        given:
        def arg = new AbstractConvertAction.Arg()
        arg.setSourceId('123')
        arg.setRuleApiName('rule__c')
        arg.setSourceApiName('source__c')
        standardConvertAction.arg = arg
        when:

        actionContext.getUser() >> User.systemUser('74255')
        serviceFacade.findObjectDataByIdsExcludeInvalid(*_) >> [new ObjectData([id: '123', name: 'name'])]
        and:
        def describe = new ObjectDescribe()
        describe.setApiName('source__c')
        serviceFacade.findObjectWithoutCopy(*_) >> describe
        and:
        List<MtConvertRule> rules = [Mock(MtConvertRule)]
        Whitebox.setInternalState(standardConvertAction, "convertRuleList", rules)

        and:
        serviceFacade.findObjectsWithoutCopy(*_) >> ['master__c': new ObjectDescribe()]
        standardConvertAction.init()
        then:
        noExceptionThrown()
        1 * infraServiceFacade.doValidateRuleFieldConfig(*_)
    }

    def 'test doAct one to one'() {
        given:
        def arg = new AbstractConvertAction.Arg()
        arg.setSourceIds(['123'])
        arg.setRuleApiName('rule__c')
        arg.setSourceApiName('source__c')
        and:
        def describe = new ObjectDescribe()
        describe.setApiName('source__c')
        Whitebox.setInternalState(standardConvertAction, "sourceObjectDescribe", describe)
        Whitebox.setInternalState(standardConvertAction, "arg", arg)
        and:
        def mtConvertRule = new MtConvertRule()
        mtConvertRule.setApiName('master_rule__c')
        mtConvertRule.setSourceObjectDescribeApiName('master__c')
        def detailRule = new MtConvertRule()
        detailRule.setApiName('detail_rule__c')
        detailRule.setSourceObjectDescribeApiName('detail__c')
        List<MtConvertRule> rules = [mtConvertRule, detailRule]
        Whitebox.setInternalState(standardConvertAction, "convertRuleList", rules)
        Whitebox.setInternalState(standardConvertAction, "masterConvertRule", mtConvertRule)
        def master = new ObjectDescribe()
        master.setApiName('master__c')
        def detail = new ObjectDescribe()
        detail.setApiName('detail__c')
        def detailFieldDescribe = new MasterDetailFieldDescribe()
        detailFieldDescribe.setApiName('master_detail__c')
        detailFieldDescribe.setIsCreateWhenMasterCreate(true)
        detail.addFieldDescribe(detailFieldDescribe)
        Whitebox.setInternalState(standardConvertAction, "sourceDetailDescribes", [detail])
        when:
        actionContext.getUser() >> User.systemUser('74255')
        and:
        def mappingDataResult = ObjectMappingService.MappingDataResult.builder().build()
        infraServiceFacade.mappingDataByRuleConfig(*_) >> {
            mappingDataResult.setObjectData(new ObjectData([api_name: 'target__c']))
            return mappingDataResult
        }
        IObjectMappingRuleInfo ruleInfo = new ObjectMappingRuleInfo()
        ruleInfo.setRuleApiName('rule_test__c')
        ruleInfo.setMasterApiName('master__c')
        infraServiceFacade.findConvertRuleByApiName(*_) >> [ruleInfo]
        serviceFacade.findObjectsWithoutCopy(*_) >> ['master__c': new ObjectDescribe()]
        serviceFacade.buildDetailSearchTemplateQuery(*_) >> new SearchTemplateQuery()
        serviceFacade.findBySearchQuery(*_) >> {
            new QueryResult<IObjectData>(data: [new ObjectData([api_name: 'detail__c'])],
                    totalNumber: 1)
        }
        Whitebox.setInternalState(AppFrameworkConfig, "splitOrderMaxNumLimit", ['74255': 20])
        def data = new ObjectData([api_name: 'source__c', "_id": "1"])
        Whitebox.setInternalState(standardConvertAction, "sourceObjectDataList", [data])
        def result = standardConvertAction.doAct(arg)
        then:
        noExceptionThrown()
        result.getMasterSlaveDataList().size() == 1
    }

    def 'test doAct many to one'() {
        given:
        def arg = new AbstractConvertAction.Arg()
        arg.setSourceIds(['123'])
        arg.setRuleApiName('rule__c')
        arg.setSourceApiName('source__c')
        arg.setSpecifiedDetails(true)
        def sourceDetail = new AbstractConvertAction.SourceDetail()
        sourceDetail.setMasterId("123")
        sourceDetail.setDetails(["source__c": ["d123"]])
        arg.setSpecifiedSourceDetails([sourceDetail])
        and:
        def describe = new ObjectDescribe()
        describe.setApiName('source__c')
        Whitebox.setInternalState(standardConvertAction, "sourceObjectDescribe", describe)
        Whitebox.setInternalState(standardConvertAction, "arg", arg)
        and:
        def mtConvertRule = new MtConvertRule()
        mtConvertRule.setApiName('master_rule__c')
        mtConvertRule.setSourceObjectDescribeApiName('master__c')
        mtConvertRule.setSceneType(1)
        mtConvertRule.setStrategy(1)
        def detailRule = new MtConvertRule()
        detailRule.setApiName('detail_rule__c')
        detailRule.setSourceObjectDescribeApiName('detail__c')
        List<MtConvertRule> rules = [mtConvertRule, detailRule]
        Whitebox.setInternalState(standardConvertAction, "convertRuleList", rules)
        Whitebox.setInternalState(standardConvertAction, "masterConvertRule", mtConvertRule)
        def master = new ObjectDescribe()
        master.setApiName('master__c')
        def detail = new ObjectDescribe()
        detail.setApiName('detail__c')
        def detailFieldDescribe = new MasterDetailFieldDescribe()
        detailFieldDescribe.setApiName('master_detail__c')
        detailFieldDescribe.setIsCreateWhenMasterCreate(true)
        detail.addFieldDescribe(detailFieldDescribe)
        Whitebox.setInternalState(standardConvertAction, "sourceDetailDescribes", [detail])
        when:
        actionContext.getUser() >> User.systemUser('74255')
        and:
        def mappingDataResult = ObjectMappingService.MappingDataResult.builder().build()
        infraServiceFacade.mappingDataByRuleConfig(*_) >> {
            mappingDataResult.setObjectData(new ObjectData([api_name: 'target__c']))
            return mappingDataResult
        }
        IObjectMappingRuleInfo ruleInfo = new ObjectMappingRuleInfo()
        ruleInfo.setRuleApiName('rule_test__c')
        ruleInfo.setMasterApiName('master__c')
        infraServiceFacade.findConvertRuleByApiName(*_) >> [ruleInfo]
        serviceFacade.findObjectsWithoutCopy(*_) >> ['master__c': new ObjectDescribe()]
        serviceFacade.buildDetailSearchTemplateQuery(*_) >> new SearchTemplateQuery()
        serviceFacade.findObjectDataByIdsExcludeInvalid(*_) >> {
            [new ObjectData([api_name: 'detail__c'])]
        }
        Whitebox.setInternalState(AppFrameworkConfig, "splitOrderMaxNumLimit", ['74255': 20])
        def data = new ObjectData([api_name: 'master__c', "_id": "123"])
        def detail1 = new ObjectData([api_name: 'detail__c', "_id": "d123"])
        Whitebox.setInternalState(standardConvertAction, "sourceObjectDataList", [data, detail1])
        def result = standardConvertAction.doAct(arg)
        then:
        noExceptionThrown()
        result.getMasterSlaveDataList().size() == 1
    }

    def 'test doAct many to many'() {
        given:
        def arg = new AbstractConvertAction.Arg()
        arg.setSourceIds(['123'])
        arg.setRuleApiName('rule__c')
        arg.setSourceApiName('source__c')
        arg.setSpecifiedDetails(true)
        def sourceDetail = new AbstractConvertAction.SourceDetail()
        sourceDetail.setMasterId("123")
        sourceDetail.setDetails(["detail__c": ["d123", "d1234"]])
        arg.setSpecifiedSourceDetails([sourceDetail])
        and:
        def describe = new ObjectDescribe()
        describe.setApiName('source__c')
        Whitebox.setInternalState(standardConvertAction, "sourceObjectDescribe", describe)
        Whitebox.setInternalState(standardConvertAction, "arg", arg)
        and:
        def mtConvertRule = new MtConvertRule()
        mtConvertRule.setApiName('master_rule__c')
        mtConvertRule.setSourceObjectDescribeApiName('master__c')
        mtConvertRule.setSceneType(1)
        mtConvertRule.setStrategy(2)
        MtConvertRule.CombinedFields masterCombinedByFields = new MtConvertRule.CombinedFields()
        masterCombinedByFields.setFields(combinedFields)
        mtConvertRule.setCombinedByFields(masterCombinedByFields)
        def detailRule = new MtConvertRule()
        detailRule.setApiName('detail_rule__c')
        detailRule.setSourceObjectDescribeApiName('detail__c')
        List<MtConvertRule> rules = [mtConvertRule, detailRule]
        Whitebox.setInternalState(standardConvertAction, "convertRuleList", rules)
        Whitebox.setInternalState(standardConvertAction, "masterConvertRule", mtConvertRule)
        def master = new ObjectDescribe()
        master.setApiName('source__c')
        def detail = new ObjectDescribe()
        detail.setApiName('detail__c')
        def detailFieldDescribe = new MasterDetailFieldDescribe()
        detailFieldDescribe.setApiName('master_id__c')
        detailFieldDescribe.setIsCreateWhenMasterCreate(true)
        detailFieldDescribe.setTargetApiName("source__c")
        detail.addFieldDescribe(detailFieldDescribe)
        Whitebox.setInternalState(standardConvertAction, "sourceDetailDescribes", [detail])
        when:
        actionContext.getUser() >> User.systemUser('74255')
        and:
        def mappingDataResult = ObjectMappingService.MappingDataResult.builder().build()
        infraServiceFacade.mappingDataByRuleConfig(*_) >> {
            mappingDataResult.setObjectData(new ObjectData([api_name: 'target__c']))
            return mappingDataResult
        }
        IObjectMappingRuleInfo ruleInfo = new ObjectMappingRuleInfo()
        ruleInfo.setRuleApiName('rule_test__c')
        ruleInfo.setMasterApiName('master__c')
        infraServiceFacade.findConvertRuleByApiName(*_) >> [ruleInfo]
        serviceFacade.findObjectsWithoutCopy(*_) >> ['master__c': new ObjectDescribe()]
        serviceFacade.buildDetailSearchTemplateQuery(*_) >> new SearchTemplateQuery()
        serviceFacade.findObjectDataByIdsExcludeInvalid(*_) >> detailList
        serviceFacade.findBySearchQuery(*_) >> Whitebox.setInternalState(AppFrameworkConfig, "splitOrderMaxNumLimit", ['74255': 20])
        Whitebox.setInternalState(standardConvertAction, "sourceObjectDataList", sourceList)
        def result = standardConvertAction.doAct(arg)
        then:
        noExceptionThrown()
        resultSize == result.getMasterSlaveDataList().size()
        where:
        sourceList                                                                    | detailList                                                                                             | combinedFields   || resultSize
        [new ObjectData([api_name: 'source__c', "_id": "123", "select_one__c": '1'])] | [new ObjectData([api_name: 'detail__c', "_id": "d123", 'select_one__c': '1', 'master_id__c': '123']),
                                                                                         new ObjectData([api_name: 'detail__c', "_id": "d1234", 'select_one__c': '2', 'master_id__c': '123'])] | [field2]         || 2
        [new ObjectData([api_name: 'source__c', "_id": "123", "select_one__c": '1'])] | [new ObjectData([api_name: 'detail__c', "_id": "d123", 'select_one__c': '1', 'master_id__c': '123']),
                                                                                         new ObjectData([api_name: 'detail__c', "_id": "d1234", 'select_one__c': '2', 'master_id__c': '123']),
                                                                                         new ObjectData([api_name: 'detail__c', "_id": "d1235", 'select_one__c': '3', 'master_id__c': '123'])] | [field2]         || 3
        [new ObjectData([api_name: 'source__c', "_id": "123", "select_one__c": '1'])] | [new ObjectData([api_name: 'detail__c', "_id": "d123", 'select_one__c': '1', 'master_id__c': '123']),
                                                                                         new ObjectData([api_name: 'detail__c', "_id": "d1234", 'select_one__c': '2', 'master_id__c': '123'])] | [field1, field2] || 2
    }

}
