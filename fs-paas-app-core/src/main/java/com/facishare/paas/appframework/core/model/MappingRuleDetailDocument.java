package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.metadata.api.IObjectMappingRuleDetailInfo;
import com.facishare.paas.metadata.impl.ObjectMappingRuleDetailInfo;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class MappingRuleDetailDocument extends DocumentBaseEntity {
    public MappingRuleDetailDocument() {
    }

    private MappingRuleDetailDocument(Map<String, Object> data) {
        super(data);
    }

    public static MappingRuleDetailDocument of(IObjectMappingRuleDetailInfo objectMappingRuleDetailInfo) {
        return new MappingRuleDetailDocument(((ObjectMappingRuleDetailInfo)objectMappingRuleDetailInfo).getContainerDocument());
    }

    public static List<MappingRuleDetailDocument> fromList(List<IObjectMappingRuleDetailInfo> list) {
        if(CollectionUtils.empty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(MappingRuleDetailDocument::of).collect(Collectors.toList());
    }
}
