package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.UnionImportConfig;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.objectImport.GetImportObjectList.ImportObjectDTO;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.ImportReferenceMapping;
import com.facishare.paas.appframework.metadata.dto.ImportSetting;
import com.facishare.paas.appframework.metadata.dto.ImportTenantSetting;
import com.facishare.paas.appframework.metadata.importobject.*;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.predef.controller.StandardImportObjectController.Arg;
import static com.facishare.paas.appframework.core.predef.controller.StandardImportObjectController.Result;
import static com.facishare.paas.appframework.metadata.importobject.DefaultObjectImportProvider.INSERT_IMPORT;
import static com.facishare.paas.appframework.metadata.importobject.ObjectImportEnum.*;

/**
 * create by zhaoju on 2021/03/16
 */
public class StandardImportObjectController extends PreDefineController<Arg, Result> {
    protected ObjectDescribeExt objectDescribe;
    protected ObjectImportInitManager objectImportInitManager;

    private static final Set<String> UNION_IMPORT_OBJECT_CODE = Sets.newHashSet(MULTI_IMPORT_CUSTOMER_CONTACT.getObjectCode(),
            MULTI_IMPORT_CUSTOMER_ORDER_CUSTOMER_ORDER_PRODUCT.getObjectCode(),
            MULTI_IMPORT_RETURN_ORDER_RETURN_ORDER_PRODUCT.getObjectCode(),
            MULTIIMPORT_ACCOUNT_ACCOUNTFININFO.getObjectCode());

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.BATCH_IMPORT.getActionCode());
    }

    @Override
    protected void before(StandardImportObjectController.Arg arg) {
        objectDescribe = findObject();
        objectImportInitManager = serviceFacade.getBean(ObjectImportInitManager.class);
        super.before(arg);
    }

    private ObjectDescribeExt findObject() {
        IObjectDescribe objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), controllerContext.getObjectApiName());
        return ObjectDescribeExt.of(objectDescribe);
    }

    @Override
    protected Result doService(Arg arg) {
        ImportObjectDTO importObject = getImportObject(arg.getObjectCode());
        if (CollectionUtils.empty(arg.getDetailObjectApiNames())) {
            List<ObjectDescribeDocument> detailDescribes = null;
            if (Objects.nonNull(importObject)) {
                handleImportManagerSetting(importObject);
                handleImportReferenceMapping(importObject);
                // 填充唯一性规则信息
                fillRuleSettingRight(importObject.getImportUniquenessRule());
                // 查询从对象的描述
                detailDescribes = findDetailDescribes();
            }
            handleImportSettingWithDescribe(importObject, objectDescribe);
            return Result.builder()
                    .importObject(importObject)
                    .detailDescribes(detailDescribes)
                    .build();
        }
        List<String> unionImportApiNameList = Lists.newArrayList(arg.getObjectCode());
        unionImportApiNameList.addAll(arg.getDetailObjectApiNames());
        handleImportManagerSetting(importObject);
        handleImportReferenceMapping(importObject);
        ImportObjectDTO importObjectDTO = ImportObjectDTO.builder()
                .objectApiName(importObject.getObjectApiName())
                .objectCode(importObject.getObjectCode())
                .unionImportApiNameList(unionImportApiNameList)  // 主在前，从在后
                .isEnableUnionDuplicateChecking(false)
                .isNotSupportSaleEvent(Boolean.TRUE)
                .supportType(ImportType.UNSUPPORT_UPDATE_IMPORT)  // 不支持更新导入
                .isOpenWorkFlow(isOpenWorkFlow(importObject))
                .matchingTypes(getUnionMatchingTypes(importObject, Collections.emptyList()))
                .isApprovalFlow(isApprovalFlow(importObject, controllerContext.getUser()))
                .checkOutOwner(importObject.getCheckOutOwner())
                .removeOutTeamMember(importObject.getRemoveOutTeamMember())
                .updateOwner(importObject.getUpdateOwner())
                .insertImport(importObject.getInsertImport())
                .updateImport(importObject.getUpdateImport())
                .referenceFieldMapping(importObject.getReferenceFieldMapping())
                .build();
        handleImportSettingWithDescribe(importObjectDTO, objectDescribe);

        return Result.builder().importObject(importObjectDTO).build();
    }

    /**
     * 公共对象不支持导入触发审批流程/工作流
     *
     * @param importObject
     * @param objectDescribe
     */
    private void handleImportSettingWithDescribe(ImportObjectDTO importObject, ObjectDescribeExt objectDescribe) {
        if (!objectDescribe.isPublicObject()) {
            return;
        }
        importObject.setIsApprovalFlow(false);
        importObject.setIsOpenWorkFlow(false);
    }

    private void handleImportManagerSetting(ImportObjectDTO importObject) {
        if (objectDescribe.isPublicObject()) {
            return;
        }
        String importSettingKey = buildImportSettingKey(importObject.getObjectApiName());
        String tenantConfig = serviceFacade.findTenantConfig(controllerContext.getUser(), importSettingKey);
        ImportTenantSetting importTenantSetting = JSON.parseObject(tenantConfig, ImportTenantSetting.class);
        if (Objects.isNull(importTenantSetting)) {
            return;
        }
        handleInsertImportTenantSetting(importObject, importTenantSetting);
        handleUpdateImportTenantSetting(importObject, importTenantSetting);
    }

    private void handleImportReferenceMapping(ImportObjectDTO importObject) {
        if (objectDescribe.isPublicObject()) {
            return;
        }
        ImportReferenceMapping importReferenceMapping = infraServiceFacade.findImportReferenceMapping(controllerContext.getUser(), importObject.getObjectApiName(), true);
        if (BooleanUtils.isNotTrue(importReferenceMapping.getReferenceFieldMappingSwitch())) {
            return;
        }
        List<ImportReferenceMapping.ReferenceFieldMapping> referenceFieldMapping = importReferenceMapping.getReferenceFieldMapping();
        if (CollectionUtils.empty(referenceFieldMapping)) {
            return;
        }
        importObject.setReferenceFieldMapping(referenceFieldMapping);
    }

    private String buildImportSettingKey(String describeApiName) {
        return "importSetting_" + describeApiName;
    }

    private void handleUpdateImportTenantSetting(ImportObjectDTO importObject, ImportTenantSetting importTenantSetting) {
        //更新导入配置
        ImportSetting importSettingUpdateImport = importTenantSetting.getUpdateImport();
        ImportObjectDTO.ImportManagerSetting updateImport = new ImportObjectDTO.ImportManagerSetting();
        //更新工作流
        ImportObjectDTO.ImportAttribute updateTriggerWorkFlow = new ImportObjectDTO.ImportAttribute();
        updateTriggerWorkFlow.setReadOnly(BooleanUtils.isTrue(importSettingUpdateImport.getUpdateImportSwitch()));
        updateTriggerWorkFlow.setValue(BooleanUtils.isTrue(importSettingUpdateImport.getTriggerWorkFlow()));
        updateImport.setTriggerWorkFlow(updateTriggerWorkFlow);
        importObject.setUpdateImport(updateImport);
    }

    private void handleInsertImportTenantSetting(ImportObjectDTO importObject, ImportTenantSetting importTenantSetting) {
        //新建导入配置
        ImportSetting importSettingInsertImport = importTenantSetting.getInsertImport();
        boolean readOnly = BooleanUtils.isTrue(importSettingInsertImport.getInsertImportSwitch());
        ImportObjectDTO.ImportManagerSetting insertImport = new ImportObjectDTO.ImportManagerSetting();
        //新建审批流
        ImportObjectDTO.ImportAttribute triggerApprovalFlow = new ImportObjectDTO.ImportAttribute();
        triggerApprovalFlow.setReadOnly(readOnly);
        triggerApprovalFlow.setValue(BooleanUtils.isTrue(importSettingInsertImport.getTriggerApprovalFlow()));
        insertImport.setTriggerApprovalFlow(triggerApprovalFlow);
        insertImport.setImportMethod(importSettingInsertImport.getImportMethod());
        //新建工作流
        ImportObjectDTO.ImportAttribute triggerWorkFlow = new ImportObjectDTO.ImportAttribute();
        triggerWorkFlow.setReadOnly(readOnly);
        triggerWorkFlow.setValue(BooleanUtils.isTrue(importSettingInsertImport.getTriggerWorkFlow()));
        insertImport.setTriggerWorkFlow(triggerWorkFlow);
        importObject.setInsertImport(insertImport);
    }

    protected Boolean isOpenWorkFlow(ImportObjectDTO importObject) {
        return importObject.getIsOpenWorkFlow();
    }

    protected Boolean isApprovalFlow(ImportObjectDTO importObject, User user) {
        // 白名单企业不支持联合导入触发审批流程
        if (AppFrameworkConfig.isInMasterDetailApprovalWhiteList(controllerContext.getTenantId())) {
            return false;
        }
        return importObject.getIsApprovalFlow()
                && AppFrameworkConfig.isUnionInsertImportTriggerApprovalFlowGray(user.getTenantId(), importObject.getObjectCode());
    }

    protected ImportObjectDTO getImportObject(String objectCode) {
        ObjectImportInitProvider provider = objectImportInitManager.getProvider(objectCode);
        IUniqueRule uniqueRule = getUniqueRuleByDescribeApiName(objectDescribe, objectCode);
        Optional<ImportObject> importObjectOpt = provider.getImportObject(objectDescribe, uniqueRule);

        return importObjectOpt.map(it -> ImportObjectDTO.from(it, uniqueRule, objectDescribe)).orElse(null);
    }

    protected List<ObjectDescribeDocument> findDetailDescribes() {
        // 预置的联合导入不下发从对象描述
        if (!Objects.equals(arg.getObjectCode(), objectDescribe.getApiName())) {
            return Lists.newArrayList();
        }
        if (UnionImportConfig.canNotBeMaster(objectDescribe.getApiName())) {
            return Lists.newArrayList();
        }
        List<IObjectDescribe> describes = serviceFacade.findSimpleDetailDescribes(controllerContext.getTenantId(), objectDescribe.getApiName());
        describes = describes.stream()
                .filter(describe -> BooleanUtils.isNotFalse(describe.isActive()))
                .filter(describe -> !UnionImportConfig.canNotBeDetail(controllerContext.getTenantId(), describe.getApiName()))
                .collect(Collectors.toList());

        List<IObjectDescribe> validObjectList = serviceFacade.filterDescribesWithActionCode(controllerContext.getUser(),
                describes, ObjectAction.BATCH_IMPORT.getActionCode());
        return ObjectDescribeDocument.ofList(validObjectList);
    }

    /**
     * 获取联合导入所支持的匹配方式
     * 取主从都支持的识别方式且不支持唯一性规则
     */
    protected Map<String, List<MatchingType>> getUnionMatchingTypes(ImportObjectDTO masterImportObject,
                                                                    List<ImportObjectDTO> details) {
        Map<String, List<MatchingType>> result = Maps.newHashMap();
        // 主对象
        Map<String, List<MatchingType>> masterMap = masterImportObject.getMatchingTypes();
        List<MatchingType> masterTypes = masterMap.getOrDefault(INSERT_IMPORT, Lists.newArrayList(MatchingType.NAME));
        if (CollectionUtils.empty(details)) {
            result.put(INSERT_IMPORT, masterTypes);
            return result;
        }

        // 从对象
        for (ImportObjectDTO detailImportObject : details) {
            Map<String, List<MatchingType>> detailMap = detailImportObject.getMatchingTypes();
            List<MatchingType> detailTypes = detailMap.getOrDefault(INSERT_IMPORT, Lists.newArrayList(MatchingType.NAME));
            masterTypes.retainAll(detailTypes);
        }
        result.put(INSERT_IMPORT, masterTypes);
        return result;
    }

    protected IUniqueRule getUniqueRuleByDescribeApiName(IObjectDescribe describe, String objectCode) {
        if (UNION_IMPORT_OBJECT_CODE.contains(objectCode)) {
            return null;
        }
        return serviceFacade.findByDescribeApiName(controllerContext.getTenantId(), describe.getApiName());
    }

    protected void fillRuleSettingRight(ImportObject.ImportUniquenessRule uniquenessRule) {
        if (Objects.nonNull(uniquenessRule)) {
            uniquenessRule.setRuleSettingRight(serviceFacade.isAdmin(controllerContext.getUser()));
        }
    }

    @Data
    public static class Arg {
        private String objectCode;
        /**
         * 联合导入时使用
         */
        private List<String> detailObjectApiNames;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private ImportObjectDTO importObject;
        private List<ObjectDescribeDocument> detailDescribes;
    }
}
