{"swagger": "2.0", "info": {"description": "This is PAAS workflow service project ", "version": "v1", "title": "Facishare PAAS Workflow"}, "tags": [{"name": "PaaS-Workflow For CRM"}, {"name": "PaaS-Workflow For BPM"}, {"name": "PaaS-Workflow For AA"}, {"name": "PaaS-Workflow For XT"}], "schemes": ["http", "https"], "paths": {"/fix/data": {"post": {"tags": ["PaaS-Workflow For AA"], "summary": "修复数据", "description": "6.1-fixD<PERSON>", "operationId": "fixData", "consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/fix/data/batchUpdateCustomer": {"post": {"tags": ["PaaS-Workflow For AA"], "summary": "批量更新关联客户，用于后动作发送feed", "description": "6.2-batchUpdateCustomer", "operationId": "batchUpdateCustomer", "consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/afterAction/add": {"post": {"tags": ["PaaS-Workflow For AA"], "summary": "定义后动作", "description": "6.2", "operationId": "addAfterAction", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AfterActionDefinitionEntity"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/afterAction/add/mapping": {"post": {"tags": ["PaaS-Workflow For AA"], "summary": "添加后动作映射", "description": "6.2", "operationId": "addAfterActionMapping", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AfterActionMappingEntity"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/afterAction/create/feed/salesRecord": {"post": {"tags": ["PaaS-Workflow For AA"], "summary": "后动作创建销售记录", "description": "6.2", "operationId": "createSalesRecord", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/SalesRecordArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/afterAction/create/feed/schedule": {"post": {"tags": ["PaaS-Workflow For AA"], "summary": "后动作创建Feed日程", "description": "6.2", "operationId": "createFeedSchedule", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/FeedScheduleArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/afterAction/create/feed/task": {"post": {"tags": ["PaaS-Workflow For AA"], "summary": "后动作创建Feed任务", "description": "6.2", "operationId": "createFeedTask", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/FeedTaskArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/afterAction/list": {"post": {"tags": ["PaaS-Workflow For AA"], "summary": "查询后动作列表", "description": "6.2", "operationId": "findAfterAction", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AfterActionDefinitionEntity"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/afterAction/sendCRMNotice": {"post": {"tags": ["PaaS-Workflow For AA"], "summary": "测试后动作标准化，发送企信", "description": "6.2", "operationId": "sendCRMNotice", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/SendCRMNoticeArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/afterAction/sendEmail": {"post": {"tags": ["PaaS-Workflow For AA"], "summary": "发送邮件", "description": "6.2", "operationId": "sendEmail", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/SendEmailArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/afterAction/testCreateData": {"get": {"tags": ["PaaS-Workflow For AA"], "summary": "测试后动作标准化,创建自定义对象数据", "description": "6.2", "operationId": "testCreateData", "consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/afterAction/testSendEmail": {"get": {"tags": ["PaaS-Workflow For AA"], "summary": "测试后动作标准化，发送邮件", "description": "6.2", "operationId": "testSendEmail", "consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/approval/cancel": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "取消流程", "description": "5.5", "operationId": "cancel", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ApprovalCancelArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResult"}}}}}, "/paas/approval/changeTaskAssignee": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "更改异常的审批人，适用于task异常或找不到审批人的异常情况，返回workflowInstanceId", "description": "5.6", "operationId": "changeTaskAssignee", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ChangeTaskAssigneeArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/approval/complete": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "执行审批动作，actionType 同意:agree; 拒绝:reject", "description": "5.4", "operationId": "complete", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ApprovalCompleteArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResult"}}, "default": {"description": "success"}}}}, "/paas/approval/definition": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "查询审批流定义", "description": "6.1", "operationId": "getDefinition", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/GetWorkflowDefinitionArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResult"}}}}}, "/paas/approval/deploy": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "部署流程", "description": "5.4.2", "operationId": "deploy", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ApprovalDeployArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/approval/deployByRule": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "部署流程", "description": "5.4.3", "operationId": "deployByRule", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/DeployByRuleArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/approval/existsRule": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "查询是否存在触发条件", "description": "5.5", "operationId": "existsRule", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ExistsRuleArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/approval/getDefinition": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "查询流程定义Json", "description": "5.4", "operationId": "getDefinition", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ApprovalGetDefinitionArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/approval/getInstance": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "查询流程实例详情", "description": "5.5", "operationId": "getInstance", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ApprovalGetInstanceArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ApprovalFlowInstanceObject"}}}}}, "/paas/approval/getWholeDefinition": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "查询流程定义Json、触发规则Json", "description": "5.4.3", "operationId": "getWholeDefinition", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/WholeDefinitionArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/DefinitionPojo"}}}}}, "/paas/approval/instance/id": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "查询流程实例详情,包含网关节点", "description": "6.0", "operationId": "getXTInstance", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ApprovalGetInstanceArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/XTInstancePojo"}}}}}, "/paas/approval/merge/multi": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "6.0刷库专用，处理流程合并，未经许可不要调用", "description": "6.0", "operationId": "merge", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/MergeArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/approval/preview": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "预览流程，指定一个流程id", "description": "5.4", "operationId": "preview", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ApprovalPreviewArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "array", "items": {"$ref": "#/definitions/ApprovalTaskObject"}}}}}}, "/paas/approval/preview/id": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "预览流程，包含网关节点", "description": "6.0", "operationId": "previewXTFlow", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ApprovalPreviewArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "array", "items": {"$ref": "#/definitions/ActivityPojo"}}}}}}, "/paas/approval/preview4Admin": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "管理员预览流程", "description": "5.4", "operationId": "preview4Admin", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ApprovalPreview4AdminArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "array", "items": {"$ref": "#/definitions/ApprovalTaskObject"}}}}}}, "/paas/approval/previewByRule": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "预览流程，给出一个有顺序的流程id列表，根据规则条件预览", "description": "5.5", "operationId": "previewByRule", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/PreviewByRuleArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PreviewPojo"}}}}}, "/paas/approval/refreshTask": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "刷新task，即重新加载task，重新生成具体的审批人，适用于task异常的情况，返回workflowInstanceId", "description": "5.6", "operationId": "refreshTask", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/RefreshTaskArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/approval/start": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "启动流程-指定流程id", "description": "5.4", "operationId": "start", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ApprovalStartArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/approval/start/id": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "启动流程-指定流程id，支持条件网关", "description": "6.0", "operationId": "startXTFLow", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ApprovalStartArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/approval/startByRule": {"post": {"tags": ["PaaS-Workflow For XT"], "summary": "给出一个有顺序的流程id列表，根据规则条件启动", "description": "5.5", "operationId": "startByRule", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/StartByRuleArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/bpm/cancel/type": {"post": {"tags": ["PaaS-Workflow For BPM"], "summary": "取消实例", "description": "5.5", "operationId": "cancel", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BPMCancelArg"}}], "responses": {"default": {"description": "successful operation"}}}}, "/paas/bpm/change/candidates": {"post": {"tags": ["PaaS-Workflow For BPM"], "summary": "根据taskId更改task的审批人", "description": "5.6", "operationId": "changeCandidates", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ChangeCandidatesArg"}}], "responses": {"default": {"description": "successful operation"}}}}, "/paas/bpm/completeTask": {"post": {"tags": ["PaaS-Workflow For BPM"], "summary": "完成BPM Task", "description": "5.5", "operationId": "completeTask", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BpmCompleteTaskArg"}}], "responses": {"default": {"description": "successful operation"}}}}, "/paas/bpm/deploy": {"post": {"tags": ["PaaS-Workflow For BPM"], "summary": "部署BPM", "description": "5.5", "operationId": "deploy", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BpmDeployArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/bpm/deployByRule": {"post": {"tags": ["PaaS-Workflow For BPM"], "summary": "部署BPM，配置过滤规则", "description": "6.0", "operationId": "deployByRule", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BpmDeployArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/WorkflowPojo"}}}}}, "/paas/bpm/findInstances": {"post": {"tags": ["PaaS-Workflow For BPM"], "summary": "分页查询BPM实例列表-不指定sourceId则查询所有实例", "description": "5.5", "operationId": "findInstances", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BpmFindInstancesArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PageResult"}}}}}, "/paas/bpm/findTasks": {"post": {"tags": ["PaaS-Workflow For BPM"], "summary": "分页查询BPM任务列表", "description": "5.5", "operationId": "findTasks", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BpmFindTasksArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PageResult"}}}}}, "/paas/bpm/findTasks/assignee": {"post": {"tags": ["PaaS-Workflow For BPM"], "summary": "分页查询BPM待办任务和已办任务", "description": "5.5", "operationId": "findTasksByAs<PERSON><PERSON>", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BPMFindTasksByAssigneeArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PageResult"}}}}}, "/paas/bpm/instance/id": {"post": {"tags": ["PaaS-Workflow For BPM"], "summary": "根据instanceId查询BPM实例", "description": "5.5", "operationId": "findInstanceById", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BpmFindInstanceArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/InstancePojo"}}}}}, "/paas/bpm/start": {"post": {"tags": ["PaaS-Workflow For BPM"], "summary": "启动BPM", "description": "5.5", "operationId": "start", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BpmStartArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/bpm/startByRule": {"post": {"tags": ["PaaS-Workflow For BPM"], "summary": "启动BPM By Rule", "description": "5.7", "operationId": "start", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BpmStartByRuleArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/bpm/task/afterAction/retry": {"post": {"tags": ["PaaS-Workflow For BPM"], "summary": "审批节点后动作异常重试执行", "description": "6.2", "operationId": "taskAfterActionRetry", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BpmAfterActionRetryArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "array", "items": {"$ref": "#/definitions/WorkflowInfo"}}}}}}, "/paas/bpm/task/id": {"post": {"tags": ["PaaS-Workflow For BPM"], "summary": "根据taskId查询BPM任务", "description": "5.5", "operationId": "findTaskById", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BpmFindTaskWithTaskIdArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/TaskPojo"}}}}}, "/paas/bpm/workflowDefinition/id": {"post": {"tags": ["PaaS-Workflow For BPM"], "summary": "查询流程定义", "description": "5.5", "operationId": "findWorkflowDefinition", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BpmFindWorkflowDefinitionArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BpmWorkflowDefinitionObject"}}}}}, "/paas/crm/approval/Instance/delete": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "删除流程实例", "description": "5.6", "operationId": "updateInstanceState", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/DeleteInstanceArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResult"}}}}}, "/paas/crm/approval/assignee/change": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "更改异常的审批人，适用于task异常或找不到审批人的异常情况", "description": "6.0", "operationId": "changeAssignee", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ChangeTaskAssigneeArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResult"}}}}}, "/paas/crm/approval/assignees": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询单据关联的当前待办任务中所有的审批人,只查询审批例外人", "description": "6.0", "operationId": "findAssignees", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/FindAssigneesArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "array", "items": {"$ref": "#/definitions/CrmTaskAssigneePojo"}}}}}}, "/paas/crm/approval/batch/create": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "6.0线上刷固定审批流程定义专用，处理crm付款、退款、开票申请三个审批，未经许可不要调用", "description": "6.0", "operationId": "batchCreate", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/BatchCreateArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/crm/approval/cancel": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "撤回流程实例", "description": "5.6", "operationId": "cancelApprovalFlow", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/CancelArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object"}}}}}, "/paas/crm/approval/definitions": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询流程定义列表", "description": "5.6", "operationId": "getApprovalFlowDefinitions", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/EntityArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "array", "items": {"$ref": "#/definitions/WorkflowInfo"}}}}}}, "/paas/crm/approval/delete": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "删除审批流", "description": "5.6", "operationId": "deleteApprovalFlowDefinitions", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/FlowDefinitionIdArg"}}], "responses": {"200": {"description": "success", "schema": {"type": "object"}}}}}, "/paas/crm/approval/deploy": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "部署审批流定义", "description": "5.6", "operationId": "deployApprovalFlow", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AfDeployArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/crm/approval/detail": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询审批流定义详情", "description": "5.6", "operationId": "getApprovalFlowDetail", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/WorkflowIdArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/AfDefinitionObject"}}}}}, "/paas/crm/approval/detailAll": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询审批流定义详情, 包含已删除", "description": "6.0", "operationId": "getApprovalFlowDetailAll", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/WorkflowIdArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/AfDefinitionObject"}}}}}, "/paas/crm/approval/enable": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "启用/停用", "description": "5.6", "operationId": "enableApprovalFlow", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/WfEnableArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object"}}}}}, "/paas/crm/approval/entities": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询关联了审批流的实体ID集合", "description": "5.6", "operationId": "getApprovalFlowEntity", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/EntityCategoryArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "array", "items": {"type": "string"}}}}}}, "/paas/crm/approval/flowName": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "根据objectId获取此条数据触发的流程名称", "description": "5.6", "operationId": "getApprovalFlowName", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/WfObjectIdArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object"}}}}}, "/paas/crm/approval/instance/afterAction": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "审批实例后动作异常重试执行", "description": "6.2", "operationId": "instanceAfterActionRetry", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/InstanceAfterActionRetryArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "array", "items": {"$ref": "#/definitions/WorkflowInfo"}}}}}}, "/paas/crm/approval/instance/detail/id": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "根据id查询流程实例详情", "description": "5.6", "operationId": "getInstanceDetailById", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/InstanceDetailArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/CrmInstanceDetailPojo"}}}}}, "/paas/crm/approval/instance/state": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询审批实例的状态", "description": "5.6", "operationId": "getApprovalFlowInstanceState", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/WfObjectIdArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/FlowInstanceState"}}}}}, "/paas/crm/approval/instance/status": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "批量查询审批流实例的状态", "description": "5.6", "operationId": "getApprovalFlowInstanceStatus", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/InstanceStatusArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "array", "items": {"$ref": "#/definitions/InstanceStatusPojo"}}}}}}, "/paas/crm/approval/instanceProgress": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询流程实例详情/进展", "description": "5.6", "operationId": "getApprovalFlowInstanceProgress", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/WfObjectIdArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ApprovalFlowInstanceObject"}}}}}, "/paas/crm/approval/instances": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询提交的流程实例列表", "description": "5.6", "operationId": "findApprovalFlowInstances", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AfInstanceQueryArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/AfInstanceQueryResult"}}}}}, "/paas/crm/approval/instances/objectId": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询审批实例列表", "description": "6.0", "operationId": "getApprovalFlowInstanceByObjectId", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/FindInstancesByObjectIdArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "array", "items": {"$ref": "#/definitions/CrmInstancePojo"}}}}}}, "/paas/crm/approval/instances/updateTime": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "分页查询审批流实例列表-给定实例更新时间段", "description": "6.0", "operationId": "findApprovalInstancesByUpdateTime", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/FindInstancesByUpdateTimeArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PageResult"}}}}}, "/paas/crm/approval/number/check": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询审批流数量限制", "description": "5.6", "operationId": "checkTotalNumber", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/TotalNumberCheckArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/WorkflowLimitedNumberPojo"}}}}}, "/paas/crm/approval/priority": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "设置流程的优先级", "description": "5.6", "operationId": "updateApprovalFlowPriority", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/FlowPriorityArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object"}}}}}, "/paas/crm/approval/replace/assignees": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "将某一个人的所有待审批的任务替换成另外一个人", "description": "6.2", "operationId": "replaceAssignees", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ReplaceAssigneeArg"}}], "responses": {"default": {"description": "successful operation"}}}}, "/paas/crm/approval/start": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "启动审批流", "description": "5.6", "operationId": "startApprovalFlow", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/WfTriggerArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResult"}}}}}, "/paas/crm/approval/task/afterAction": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "审批节点后动作异常重试执行", "description": "6.2", "operationId": "taskAfterActionRetry", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/TaskAfterActionRetryArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "array", "items": {"$ref": "#/definitions/WorkflowInfo"}}}}}}, "/paas/crm/approval/task/detail/activityId": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询用户任务详情(未进行到的任务节点)", "description": "5.6", "operationId": "getTaskDetailByActivityId", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UserTaskActIdArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ApprovalTaskObject"}}}}}, "/paas/crm/approval/task/detail/taskId": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询用户任务详情(已审批，或当前进行中的任务节点)", "description": "5.6", "operationId": "getTaskDetailByTaskId", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UserTaskIdArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/ApprovalTaskObject"}}}}}, "/paas/crm/approval/task/state": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询任务状态", "description": "5.6", "operationId": "getTaskState", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UserTaskIdArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/UserTaskState"}}}}}, "/paas/crm/approval/tasks": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询用户任务,待我审批任务", "description": "5.6", "operationId": "findTasks", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UserTaskArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/TaskQueryResult"}}}}}, "/paas/crm/approval/unique/name": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询审批流的名称是否唯一", "description": "5.6", "operationId": "checkApprovalFlowName", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/FlowNameCheckArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResult"}}}}}, "/paas/crm/change/workflowRule/no_exist": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "刷库，处理工作流workflowRule为空的问题", "description": "6.2", "operationId": "changeNullWorkflowRule", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/ChangeNullWorkflowRuleArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/crm/find/task/info": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "获取task中的数据信息", "description": "6.0", "operationId": "getTaskInfo", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/DataTimeArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/crm/flow/rule": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询流程过滤器", "description": "6.2", "operationId": "findFlowRuleDefinitions", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/FlowRuleArg"}}], "responses": {"200": {"description": "success", "schema": {"type": "object"}}}}}, "/paas/crm/mail/template/apply/check": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询是否使用了邮箱模板，邮箱模板删除的时候使用", "description": "6.0", "operationId": "checkMailTemplateNumber", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/MailTemplateApplyCheckArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "boolean"}}}}}, "/paas/crm/workflow/definition": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询工作流定义", "description": "6.0", "operationId": "getWorkflowDefinition", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/GetWorkflowDefinitionArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/crm/workflow/delete": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "删除工作流定义", "description": "6.0", "operationId": "deleteWorkflow", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/DelWorkflowArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResult"}}}}}, "/paas/crm/workflow/deploy": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "部署工作流", "description": "6.0", "operationId": "deployWorkflow", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/DeployWorkflowArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/crm/workflow/enable": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "工作流启用/停用", "description": "6.0", "operationId": "enableWorkflow", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/EnableWorkflowArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResult"}}}}}, "/paas/crm/workflow/id/definition": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询工作流定义", "description": "6.2", "operationId": "getWorkflowDefinitionById", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/GetWorkflowDefinitionByIdArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "string"}}}}}, "/paas/crm/workflow/list": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询工作流定义列表", "description": "6.0", "operationId": "findWorkflowList", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/FindWorkflowListArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "array", "items": {"$ref": "#/definitions/CrmWorkflowPojo"}}}}}}, "/paas/crm/workflow/number/check": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "查询工作流数量限制", "description": "6.0", "operationId": "checkWorkflowTotalNumber", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/TotalNumberCheckArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/WorkflowLimitedNumberPojo"}}}}}, "/paas/crm/workflow/schedule/start": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "触发工作流", "description": "6.0", "operationId": "startScheduleWorkflow", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/StartScheduleWorkflowArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "array", "items": {"type": "string"}}}}}}, "/paas/crm/workflow/start": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "触发工作流", "description": "6.0", "operationId": "startWorkflow", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/StartWorkflowArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "array", "items": {"type": "string"}}}}}}, "/paas/crm/workflow/start/test": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "触发工作流,仅供测试使用", "description": "6.1", "operationId": "startWorkflowTest", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/StartWorkflowArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "array", "items": {"type": "string"}}}}}}, "/paas/crm/workflow/timer/start": {"post": {"tags": ["PaaS-Workflow For CRM"], "summary": "定时触发工作流", "description": "6.1", "operationId": "timerStartWorkflow", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/StartWorkflowArg"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResult"}}}}}}, "definitions": {"ActionParam": {"type": "object", "properties": {"fromField": {"type": "string"}, "toField": {"type": "string"}, "fieldFlag": {"type": "string"}, "fieldValue": {"type": "object"}}}, "ActivityInstancePojo": {"type": "object", "properties": {"id": {"type": "string"}, "activityId": {"type": "string"}, "calledWorkflowInstanceId": {"type": "string"}, "start": {"type": "integer", "format": "int64"}, "end": {"type": "integer", "format": "int64"}, "endState": {"type": "string"}, "duration": {"type": "integer", "format": "int64"}, "activityName": {"type": "string"}, "assignNextTask": {"type": "integer", "format": "int32"}, "properties": {"type": "object", "additionalProperties": {"type": "object"}}}}, "ActivityPojo": {"type": "object", "properties": {"activityId": {"type": "string"}, "activityName": {"type": "string"}, "activityType": {"type": "string"}, "taskId": {"type": "string"}, "taskType": {"type": "string"}, "taskState": {"type": "string"}, "assignee": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "opinions": {"type": "array", "items": {"$ref": "#/definitions/ApprovalOpinionObject"}}, "errMsg": {"type": "string"}, "completedConditionName": {"type": "string"}, "conditions": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "object"}}}, "sourceTransition": {"type": "object", "additionalProperties": {"type": "string"}}, "taskExecuteState": {"type": "string"}}}, "AfDefinitionObject": {"type": "object", "properties": {"workflowJson": {"type": "string"}, "workflowRuleJson": {"type": "string"}, "executionJson": {"type": "string"}}}, "AfDeployArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "update": {"type": "boolean", "default": false}, "workflowJson": {"type": "string"}, "ruleJson": {"type": "string"}, "executionJson": {"type": "string"}, "appId": {"type": "string"}}}, "AfInstanceQueryArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "entityId": {"type": "string"}, "state": {"type": "string"}, "pageInfo": {"$ref": "#/definitions/PageInfo"}}}, "AfInstanceQueryResult": {"type": "object", "properties": {"approvalInstanceObjects": {"type": "array", "items": {"$ref": "#/definitions/ApprovalInstanceObject"}}, "countAll": {"type": "integer", "format": "int32"}}}, "AfterActionDefinitionEntity": {"type": "object", "properties": {"id": {"$ref": "#/definitions/ObjectId"}, "tenantId": {"type": "string"}, "appId": {"type": "string"}, "name": {"type": "string"}, "url": {"type": "string"}, "method": {"type": "string"}, "type": {"type": "string"}, "headers": {"type": "array", "items": {"$ref": "#/definitions/Param"}}, "params": {"type": "array", "items": {"$ref": "#/definitions/Param"}}, "response": {"$ref": "#/definitions/Response"}, "createTime": {"type": "integer", "format": "int64"}, "creator": {"type": "string"}, "modifyTime": {"type": "integer", "format": "int64"}, "modifier": {"type": "string"}}}, "AfterActionMappingEntity": {"type": "object", "properties": {"id": {"$ref": "#/definitions/ObjectId"}, "tenantId": {"type": "string"}, "appId": {"type": "string"}, "sourceWorkflowId": {"type": "string"}, "afterActionId": {"type": "string"}, "headers": {"type": "array", "items": {"$ref": "#/definitions/Param"}}, "params": {"type": "array", "items": {"$ref": "#/definitions/Param"}}, "createTime": {"type": "integer", "format": "int64"}, "creator": {"type": "string"}, "modifyTime": {"type": "integer", "format": "int64"}, "modifier": {"type": "string"}}}, "ApprovalCancelArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "workflowInstanceId": {"type": "string"}}}, "ApprovalCompleteArg": {"type": "object", "required": ["actionType"], "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "taskId": {"type": "string"}, "actionType": {"type": "string", "description": "执行审批的结果", "enum": ["agree", "reject", "go_back(驳回上一个审批节点)"]}, "opinion": {"type": "string"}}, "description": "执行一个审批需要的参数"}, "ApprovalDeployArg": {"type": "object", "required": ["def<PERSON><PERSON>"], "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "defJson": {"type": "string", "example": "{\"type\": \"approvalflow\",\"sourceWorkflowId\": \"BPMServiceImplTestWHC\",\"name\": \"asdsdf试031\",\"description\": \"测试下审批流部署\",\"activities\": [{\"type\": \"startEvent\",\"id\": \"start\"},{\"type\": \"userTask\",\"taskType\": \"single\",\"assignee\": {\"person\": [\"1203\"], \"dept\": [\"1203\"], \"dept_leader\": [\"1203\"], \"role\": [\"1203\"], \"group\": [\"1203\"]},\"id\": \"1\"},{\"type\": \"exclusiveGateway\",\"id\": \"g1\"},{\"type\": \"userTask\",\"taskType\": \"single\",\"assignee\": {\"person\": [\"1203\"]},\"id\": \"2\"},{\"type\": \"exclusiveGateway\",\"id\": \"g2\"},{\"type\": \"userTask\",\"taskType\": \"single\",\"assignee\": {\"person\": [\"1203\"]},\"id\": \"3\"},{\"type\": \"exclusiveGateway\",\"id\": \"g3\"},{\"type\": \"endEvent\",\"id\": \"end\"}],\"transitions\": [{\"fromId\": \"start\",\"toId\": \"1\"},{\"fromId\": \"1\",\"toId\": \"g1\"},{\"condition\": {\"left\": {\"expression\": \"action\"},\"right\": {\"type\": {\"name\": \"text\"},\"value\": \"agree\"},\"type\": \"equals\"},\"fromId\": \"g1\",\"toId\": \"2\"},{\"condition\": {\"left\": {\"expression\": \"action\"},\"right\": {\"type\": {\"name\": \"text\"},\"value\": \"reject\"},\"type\": \"equals\"},\"fromId\": \"g1\",\"toId\": \"end\"},{\"condition\": {\"left\": {\"expression\": \"action\"},\"right\": {\"type\": {\"name\": \"text\"},\"value\": \"back\"},\"type\": \"equals\"},\"fromId\": \"g1\",\"toId\": \"1\"},{\"fromId\": \"2\",\"toId\": \"g2\"},{\"condition\": {\"left\": {\"expression\": \"action\"},\"right\": {\"type\": {\"name\": \"text\"},\"value\": \"agree\"},\"type\": \"equals\"},\"fromId\": \"g2\",\"toId\": \"3\"},{\"condition\": {\"left\": {\"expression\": \"action\"},\"right\": {\"type\": {\"name\": \"text\"},\"value\": \"reject\"},\"type\": \"equals\"},\"fromId\": \"g2\",\"toId\": \"end\"},{\"condition\": {\"left\": {\"expression\": \"action\"},\"right\": {\"type\": {\"name\": \"text\"},\"value\": \"back\"},\"type\": \"equals\"},\"fromId\": \"g2\",\"toId\": \"2\"},{\"fromId\": \"3\",\"toId\": \"g3\"},{\"condition\": {\"left\": {\"expression\": \"action\"},\"right\": {\"type\": {\"name\": \"text\"},\"value\": \"agree\"},\"type\": \"equals\"},\"fromId\": \"g3\",\"toId\": \"end\"},{\"condition\": {\"left\": {\"expression\": \"action\"},\"right\": {\"type\": {\"name\": \"text\"},\"value\": \"reject\"},\"type\": \"equals\"},\"fromId\": \"g3\",\"toId\": \"end\"},{\"condition\": {\"left\": {\"expression\": \"action\"},\"right\": {\"type\": {\"name\": \"text\"},\"value\": \"back\"},\"type\": \"equals\"},\"fromId\": \"g3\",\"toId\": \"3\"}],\"variables\": [{\"id\": \"action\",\"type\": {\"name\": \"text\"}}]}", "description": "流程定义"}}}, "ApprovalFlowInstanceObject": {"type": "object", "properties": {"id": {"type": "string"}, "tanentId": {"type": "string"}, "state": {"type": "string"}, "curTasks": {"type": "array", "items": {"$ref": "#/definitions/ApprovalTaskObject"}}, "allTasks": {"type": "array", "items": {"$ref": "#/definitions/ApprovalTaskObject"}}, "triggerType": {"type": "integer", "format": "int32"}, "applicantId": {"type": "string"}, "applyTime": {"type": "integer", "format": "int64"}, "workflowId": {"type": "string"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "execution": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/ExecutionPojo"}}}, "triggerData": {"$ref": "#/definitions/TriggerDataPojo"}, "version": {"type": "integer", "format": "int32"}}}, "ApprovalGetDefinitionArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "sourceWorkflowId": {"type": "string"}}}, "ApprovalGetInstanceArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "workflowInstanceId": {"type": "string"}}}, "ApprovalInstanceObject": {"type": "object", "properties": {"instanceId": {"type": "string"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "state": {"type": "string"}, "applicantId": {"type": "string"}, "triggerType": {"type": "integer", "format": "int32"}, "applyTime": {"type": "integer", "format": "int64"}, "modifyTime": {"type": "integer", "format": "int64"}, "endTime": {"type": "integer", "format": "int64"}, "flowName": {"type": "string"}, "version": {"type": "integer", "format": "int32"}}}, "ApprovalOpinionObject": {"type": "object", "properties": {"tenantId": {"type": "string"}, "userId": {"type": "string"}, "actionType": {"type": "string"}, "opinion": {"type": "string"}, "replyTime": {"type": "integer", "format": "int64"}}}, "ApprovalPreview4AdminArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "userId": {"type": "string"}, "defJson": {"type": "string"}}}, "ApprovalPreviewArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "sourceWorkflowId": {"type": "string"}}}, "ApprovalStartArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "sourceWorkflowId": {"type": "string"}, "objectId": {"type": "string"}, "entityId": {"type": "string"}}}, "ApprovalTaskInfo": {"type": "object", "properties": {"taskId": {"type": "string"}, "instanceId": {"type": "string"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "state": {"type": "string"}, "applicantId": {"type": "string"}, "replyTime": {"type": "integer", "format": "int64"}, "modifyTime": {"type": "integer", "format": "int64"}, "flowName": {"type": "string"}, "createTime": {"type": "integer", "format": "int64"}}}, "ApprovalTaskObject": {"type": "object", "properties": {"id": {"type": "string"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "instanceId": {"type": "string"}, "type": {"type": "string"}, "state": {"type": "string"}, "activityId": {"type": "string"}, "assignee": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "opinions": {"type": "array", "items": {"$ref": "#/definitions/ApprovalOpinionObject"}}, "approverModifyLog": {"type": "array", "items": {"$ref": "#/definitions/ApproverModifyLogObject"}}, "errMsg": {"type": "string"}, "sourceWorkflowId": {"type": "string"}, "sourceTransition": {"type": "object", "additionalProperties": {"type": "string"}}, "taskExecuteState": {"type": "string"}, "execution": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/ExecutionPojo"}}}, "version": {"type": "integer", "format": "int32"}}}, "ApproverModifyLogObject": {"type": "object", "properties": {"userId": {"type": "string"}, "beforeModifyPersons": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}, "afterModifyPersons": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}, "modifyOpinion": {"type": "string"}, "modifyTime": {"type": "integer", "format": "int64"}}}, "BPMCancelArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "workflowInstanceId": {"type": "string"}}}, "BPMFindTasksByAssigneeArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "pageInfo": {"$ref": "#/definitions/PageInfo"}, "assigneeId": {"type": "string"}, "name": {"type": "string"}, "completed": {"type": "boolean", "default": false}}}, "BaseResult": {"type": "object", "properties": {"errCode": {"type": "integer", "format": "int32"}, "errKey": {"type": "string"}, "errMessage": {"type": "string"}, "errDescription": {"type": "string"}, "result": {"type": "object"}, "success": {"type": "boolean", "default": false}}}, "BatchCreateArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "targets": {"type": "array", "items": {"type": "string"}}}}, "BpmAfterActionRetryArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "taskId": {"type": "string"}, "executeIndex": {"type": "integer", "format": "int32"}, "executeType": {"type": "integer", "format": "int32"}}}, "BpmCompleteTaskArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "taskId": {"type": "string"}, "actionType": {"type": "string"}, "conditionMap": {"type": "object", "additionalProperties": {"type": "object"}}, "opinion": {"type": "string"}, "nextTaskAssignee": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "addOrReplaceNextTaskAssignee": {"type": "integer", "format": "int32"}}}, "BpmDeployArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "update": {"type": "boolean", "default": false}, "workflowJson": {"type": "string"}, "ruleJson": {"type": "string"}}}, "BpmFindInstanceArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "instanceId": {"type": "string"}}}, "BpmFindInstancesArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "pageInfo": {"$ref": "#/definitions/PageInfo"}, "workflowId": {"type": "string"}, "workflowInstanceIdList": {"type": "array", "items": {"type": "string"}}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "state": {"type": "string"}, "workflowName": {"type": "string"}, "sourceWorkflowId": {"type": "string"}}}, "BpmFindTaskWithTaskIdArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "taskId": {"type": "string"}}}, "BpmFindTasksArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "pageInfo": {"$ref": "#/definitions/PageInfo"}, "instanceId": {"type": "string"}, "applicantId": {"type": "string"}, "assigneeId": {"type": "string"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "taskIdList": {"type": "array", "items": {"type": "string"}}, "activityInstanceIdList": {"type": "array", "items": {"type": "string"}}, "completed": {"type": "boolean", "default": false}, "state": {"type": "string"}, "sourceWorkflowId": {"type": "string"}, "name": {"type": "string"}}}, "BpmFindWorkflowDefinitionArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "workflowId": {"type": "string"}}}, "BpmStartArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "workflowSourceId": {"type": "string"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}}}, "BpmStartByRuleArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "workflowSourceId": {"type": "string"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "triggerSource": {"type": "string"}}}, "BpmWorkflowDefinitionObject": {"type": "object", "properties": {"workflowJson": {"type": "string"}, "ruleJson": {"type": "string"}}}, "CancelArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "objectId": {"type": "string"}, "reason": {"type": "string"}}}, "ChangeCandidatesArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "taskId": {"type": "string"}, "candidateIds": {"type": "array", "items": {"type": "string"}}}}, "ChangeNullWorkflowRuleArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "type": {"type": "string"}}}, "ChangeTaskAssigneeArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "taskId": {"type": "string"}, "personSet": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}, "modifyOpinion": {"type": "string"}}}, "CrmInstanceDetailPojo": {"type": "object", "properties": {"id": {"type": "string"}, "tenantId": {"type": "string"}, "state": {"type": "string"}, "triggerType": {"type": "integer", "format": "int32"}, "applicantId": {"type": "string"}, "applyTime": {"type": "integer", "format": "int64"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "modifyTime": {"type": "integer", "format": "int64"}, "endTime": {"type": "integer", "format": "int64"}, "flowName": {"type": "string"}, "sourceWorkflowId": {"type": "string"}, "workflowId": {"type": "string"}, "curTasks": {"type": "array", "items": {"$ref": "#/definitions/CrmTaskPojo"}}, "allTasks": {"type": "array", "items": {"$ref": "#/definitions/CrmTaskPojo"}}, "triggerData": {"$ref": "#/definitions/TriggerDataPojo"}, "version": {"type": "integer", "format": "int32"}, "execution": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/ExecutionPojo"}}}}}, "CrmInstancePojo": {"type": "object", "properties": {"tenantId": {"type": "string"}, "id": {"type": "string"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "triggerType": {"type": "string"}, "state": {"type": "string"}, "start": {"type": "integer", "format": "int64"}, "modifyTime": {"type": "integer", "format": "int64"}, "end": {"type": "integer", "format": "int64"}, "sourceWorkflowId": {"type": "string"}, "applicantId": {"type": "string"}, "workflowName": {"type": "string"}, "version": {"type": "integer", "format": "int32"}}}, "CrmTaskAssigneePojo": {"type": "object", "properties": {"tenantId": {"type": "string"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "assignees": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}}}, "CrmTaskPojo": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "state": {"type": "string"}, "activityId": {"type": "string"}, "assignee": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "opinions": {"type": "array", "items": {"$ref": "#/definitions/ApprovalOpinionObject"}}, "approverModifyLogs": {"type": "array", "items": {"$ref": "#/definitions/ApproverModifyLogObject"}}, "assigneeChangeLog": {"type": "array", "items": {"type": "array", "items": {"type": "string"}}}, "errMsg": {"type": "string"}, "sourceWorkflowId": {"type": "string"}, "createTime": {"type": "integer", "format": "int64"}, "modifyTime": {"type": "integer", "format": "int64"}, "execution": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/ExecutionPojo"}}}}}, "CrmWorkflowPojo": {"type": "object", "properties": {"sourceWorkflowId": {"type": "string"}, "workflowId": {"type": "string"}, "type": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "enable": {"type": "boolean", "default": false}, "entityId": {"type": "string"}, "triggerTypes": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "execution": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/ExecutionPojo"}}}, "creator": {"type": "string"}, "createTime": {"type": "integer", "format": "int64"}, "modifier": {"type": "string"}, "modifyTime": {"type": "integer", "format": "int64"}}}, "DataTimeArg": {"type": "object", "properties": {"startTime": {"type": "integer", "format": "int64"}, "endTime": {"type": "integer", "format": "int64"}, "state": {"type": "string"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "tenantId": {"type": "string"}}}, "DefinitionPojo": {"type": "object", "properties": {"workflowJson": {"type": "string"}, "ruleJson": {"type": "string"}}}, "DelWorkflowArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "sourceWorkflowId": {"type": "string"}, "appId": {"type": "string"}}}, "DeleteInstanceArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "objectId": {"type": "string"}}}, "DeployByRuleArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "defJson": {"type": "string"}, "ruleJson": {"type": "string"}}}, "DeployWorkflowArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "update": {"type": "boolean", "default": false}, "workflowJson": {"type": "string"}, "ruleJson": {"type": "string"}, "appId": {"type": "string"}}}, "EnableWorkflowArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "sourceWorkflowId": {"type": "string"}, "enable": {"type": "boolean", "default": false}, "appId": {"type": "string"}}}, "EntityArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "entityId": {"type": "string"}, "workflowName": {"type": "string"}, "enable": {"type": "boolean", "default": false}}}, "EntityCategoryArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}}}, "ExecutionPojo": {"type": "object", "properties": {"tenantId": {"type": "string"}, "appId": {"type": "string"}, "taskType": {"type": "string"}, "rowNo": {"type": "integer", "format": "int32"}, "executionState": {"type": "string"}, "actionErrorMsg": {"type": "string"}, "activityId": {"type": "string"}, "userId": {"type": "string"}, "modifyTime": {"type": "integer", "format": "int64"}, "sender": {"type": "string"}, "recipients": {"type": "object", "additionalProperties": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}}, "emailAddress": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}, "title": {"type": "string"}, "content": {"type": "string"}, "template": {"type": "string"}, "afterActionDefinitionId": {"type": "string"}, "afterActionMappingId": {"type": "string"}, "actionMapping": {"type": "object", "additionalProperties": {"type": "object"}}, "actionParams": {"type": "array", "items": {"$ref": "#/definitions/ActionParam"}}, "fieldMapping": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"type": "string"}}}, "updateFieldJson": {"type": "string"}, "updateFieldObject": {"type": "object"}, "triggerParam": {"type": "object", "additionalProperties": {"type": "object"}}, "workflowMap": {"type": "object", "additionalProperties": {"type": "string"}}}}, "ExistsRuleArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "sourceWorkflowId": {"type": "string"}}}, "FeedScheduleArg": {"type": "object", "properties": {"appId": {"type": "string"}, "tenantId": {"type": "string"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "type": {"type": "string"}, "applicantId": {"type": "string"}, "workflowInstanceId": {"type": "string"}, "workflowId": {"type": "string"}, "activityId": {"type": "string"}, "expressions": {"type": "array", "items": {"$ref": "#/definitions/expression"}}, "content": {"type": "string"}, "carbonCopyEmployeeIds": {"type": "object", "additionalProperties": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}}, "attenderEmployeeIds": {"type": "object", "additionalProperties": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}}, "allDay": {"type": "boolean", "default": false}, "beginTime": {"type": "string"}, "endTime": {"type": "string"}, "remindTypes": {"type": "array", "items": {"$ref": "#/definitions/RemindTypeModel"}}, "receipt": {"type": "boolean", "default": false}, "receiptEmployeeIds": {"type": "object", "additionalProperties": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}}}}, "FeedTaskArg": {"type": "object", "properties": {"appId": {"type": "string"}, "tenantId": {"type": "string"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "type": {"type": "string"}, "applicantId": {"type": "string"}, "workflowInstanceId": {"type": "string"}, "workflowId": {"type": "string"}, "activityId": {"type": "string"}, "expressions": {"type": "array", "items": {"$ref": "#/definitions/expression"}}, "title": {"type": "string"}, "content": {"type": "string"}, "carbonCopyEmployeeIds": {"type": "object", "additionalProperties": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}}, "attenderEmployeeIds": {"type": "object", "additionalProperties": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}}, "deadLine": {"type": "string"}}}, "FindAssigneesArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "entityId": {"type": "string"}, "objectIds": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}}}, "FindInstancesByObjectIdArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "objectId": {"type": "string"}, "sourceWorkflowId": {"type": "string"}}}, "FindInstancesByUpdateTimeArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "pageInfo": {"$ref": "#/definitions/PageInfo"}, "sourceWorkflowId": {"type": "string"}, "startTime": {"type": "integer", "format": "int64"}, "endTime": {"type": "integer", "format": "int64"}, "state": {"type": "string"}, "applicantId": {"type": "string"}, "entityId": {"type": "string"}}}, "FindWorkflowListArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "workflowName": {"type": "string"}, "entityId": {"type": "string"}}}, "FlowDefinitionIdArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "sourceWorkflowIds": {"type": "array", "items": {"type": "string"}}, "appId": {"type": "string"}}}, "FlowInstanceState": {"type": "object", "properties": {"state": {"type": "string"}, "applicantId": {"type": "string"}, "curTaskState": {"type": "string"}, "curTaskAssignerIds": {"type": "array", "items": {"type": "string"}}, "triggerType": {"type": "integer", "format": "int32"}, "version": {"type": "integer", "format": "int32"}}}, "FlowNameCheckArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "sourceWorkflowId": {"type": "string"}, "name": {"type": "string"}}}, "FlowPriority": {"type": "object", "properties": {"workflowId": {"type": "string"}, "priority": {"type": "integer", "format": "int32"}}}, "FlowPriorityArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "priorities": {"type": "array", "items": {"$ref": "#/definitions/FlowPriority"}}}}, "FlowRuleArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "entityId": {"type": "string"}, "sourceWorkflowId": {"type": "string"}, "ruleType": {"type": "string"}}}, "GetWorkflowDefinitionArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "sourceWorkflowId": {"type": "string"}}}, "GetWorkflowDefinitionByIdArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "workflowId": {"type": "string"}}}, "InstanceAfterActionRetryArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "instanceId": {"type": "string"}, "executeIndex": {"type": "integer", "format": "int32"}, "executeType": {"type": "integer", "format": "int32"}}}, "InstanceDetailArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "instanceId": {"type": "string"}}}, "InstancePojo": {"type": "object", "properties": {"workflowId": {"type": "string"}, "id": {"type": "string"}, "workflowName": {"type": "string"}, "workflowDescription": {"type": "string"}, "tenantId": {"type": "string"}, "start": {"type": "integer", "format": "int64"}, "end": {"type": "integer", "format": "int64"}, "variables": {"type": "object", "additionalProperties": {"type": "object"}}, "duration": {"type": "integer", "format": "int64"}, "activityInstances": {"type": "array", "items": {"$ref": "#/definitions/ActivityInstancePojo"}}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "sourceWorkflowId": {"type": "string"}, "state": {"type": "string"}, "applicantId": {"type": "string"}, "triggerSource": {"type": "string"}}}, "InstanceStatusArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "objectIds": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}}}, "InstanceStatusPojo": {"type": "object", "properties": {"tenantId": {"type": "string"}, "instanceId": {"type": "string"}, "objectId": {"type": "string"}, "status": {"type": "string"}, "triggerType": {"type": "integer", "format": "int32"}, "version": {"type": "integer", "format": "int32"}}}, "LeftSide": {"type": "object", "properties": {"fieldName": {"type": "string"}, "fieldType": {"type": "string"}, "fieldSrc": {"type": "string"}}}, "MailTemplateApplyCheckArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "templateId": {"type": "string"}}}, "MergeArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "entityId": {"type": "string"}, "sourceWorkflowIds": {"type": "array", "items": {"type": "string"}}, "flowName": {"type": "string"}}}, "ObjectId": {"type": "object", "properties": {"timestamp": {"type": "integer", "format": "int32"}, "machineIdentifier": {"type": "integer", "format": "int32"}, "processIdentifier": {"type": "integer", "format": "int32"}, "counter": {"type": "integer", "format": "int32"}, "time": {"type": "integer", "format": "int64"}, "date": {"type": "string", "format": "date-time"}, "timeSecond": {"type": "integer", "format": "int32"}}}, "OpinionPojo": {"type": "object", "properties": {"tenantId": {"type": "string"}, "userId": {"type": "string"}, "actionType": {"type": "string"}, "opinion": {"type": "string"}, "replyTime": {"type": "integer", "format": "int64"}}}, "PageInfo": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "orderBy": {"type": "string"}, "asc": {"type": "boolean", "default": false}}}, "PageResult": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "dataList": {"type": "array", "items": {"type": "object"}}}}, "Param": {"type": "object", "properties": {"fromField": {"type": "string"}, "toField": {"type": "string"}, "fieldValue": {"type": "object"}, "fieldFlag": {"type": "string"}, "strategy": {"type": "string"}}}, "PreviewByRuleArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "sourceWorkflowIds": {"type": "array", "items": {"type": "string"}}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "metadataInstanceId": {"type": "string"}, "metadataApiName": {"type": "string"}, "metadataVersion": {"type": "string"}}}, "PreviewPojo": {"type": "object", "properties": {"taskList": {"type": "array", "items": {"$ref": "#/definitions/ApprovalTaskObject"}}, "ruleJson": {"type": "string"}}}, "RefreshTaskArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "taskId": {"type": "string"}}}, "RemindTypeModel": {"type": "object", "properties": {"value": {"type": "integer", "format": "int32"}, "time": {"type": "integer", "format": "int64"}}}, "ReplaceAssigneeArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "oldPerson": {"type": "string"}, "newPerson": {"type": "string"}, "modifyOpinion": {"type": "string"}}}, "Response": {"type": "object", "properties": {"codeName": {"type": "string"}, "codeValue": {"type": "string"}, "messageName": {"type": "string"}}}, "RightSide": {"type": "object", "properties": {"value": {"type": "string"}}}, "SalesRecordArg": {"type": "object", "properties": {"appId": {"type": "string"}, "tenantId": {"type": "string"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "type": {"type": "string"}, "applicantId": {"type": "string"}, "workflowInstanceId": {"type": "string"}, "workflowId": {"type": "string"}, "activityId": {"type": "string"}, "expressions": {"type": "array", "items": {"$ref": "#/definitions/expression"}}, "content": {"type": "string"}, "tagId": {"type": "string"}, "carbonCopyEmployeeIds": {"type": "object", "additionalProperties": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}}}}, "SendCRMNoticeArg": {"type": "object", "properties": {"workflowContext": {"$ref": "#/definitions/WorkflowContext"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "recipients": {"type": "object", "additionalProperties": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}}, "title": {"type": "string"}, "content": {"type": "string"}, "template": {"type": "string"}, "id": {"type": "string"}, "type": {"type": "string"}, "applicantId": {"type": "string"}, "workflowInstanceStartTime": {"type": "integer", "format": "int64"}, "approvalStatus": {"type": "string"}, "approvalFlowName": {"type": "string"}, "workflowInstanceEndTime": {"type": "integer", "format": "int64"}}}, "SendEmailArg": {"type": "object", "properties": {"workflowContext": {"$ref": "#/definitions/WorkflowContext"}, "recipients": {"type": "object", "additionalProperties": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}}, "receiverEmailAddresses": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}, "templateId": {"type": "string"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "applicantId": {"type": "string"}, "taskType": {"type": "string"}, "instanceId": {"type": "string"}, "activityId": {"type": "string"}, "workflowId": {"type": "string"}}}, "StartByRuleArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "sourceWorkflowIds": {"type": "array", "items": {"type": "string"}}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "metadataInstanceId": {"type": "string"}, "metadataApiName": {"type": "string"}, "metadataVersion": {"type": "string"}}}, "StartScheduleWorkflowArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "triggerType": {"type": "integer", "format": "int32"}, "triggerData": {"type": "object", "additionalProperties": {"type": "object"}}, "eventId": {"type": "string"}, "sourceWorkflowId": {"type": "string"}}}, "StartWorkflowArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "triggerType": {"type": "integer", "format": "int32"}, "triggerData": {"type": "object", "additionalProperties": {"type": "object"}}, "eventId": {"type": "string"}, "startTime": {"type": "integer", "format": "int64"}}}, "StartupRuleCriteriaPojo": {"type": "object", "properties": {"rowNo": {"type": "integer", "format": "int32"}, "leftSide": {"$ref": "#/definitions/LeftSide"}, "operator": {"type": "string"}, "rightSide": {"$ref": "#/definitions/RightSide"}, "value": {"type": "string"}, "fieldType": {"type": "string"}, "fieldName": {"type": "string"}, "fieldSrc": {"type": "string"}}}, "StartupRulePojo": {"type": "object", "properties": {"ruleId": {"type": "string"}, "appId": {"type": "string"}, "entityId": {"type": "string"}, "ruleType": {"type": "string"}, "triggerTypes": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "conditionPattern": {"type": "string"}, "conditions": {"type": "array", "items": {"$ref": "#/definitions/StartupRuleCriteriaPojo"}}, "workflowSrcId": {"type": "string"}}}, "TaskAfterActionRetryArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "taskId": {"type": "string"}, "executeIndex": {"type": "integer", "format": "int32"}, "executeType": {"type": "integer", "format": "int32"}}}, "TaskPojo": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "applicantId": {"type": "string"}, "createTime": {"type": "integer", "format": "int64"}, "modifyTime": {"type": "integer", "format": "int64"}, "assigneeIds": {"type": "array", "items": {"type": "string"}}, "completed": {"type": "boolean", "default": false}, "canceled": {"type": "boolean", "default": false}, "activityId": {"type": "string"}, "activityInstanceId": {"type": "string"}, "workflowInstanceId": {"type": "string"}, "sourceWorkflowId": {"type": "string"}, "workflowId": {"type": "string"}, "tenantId": {"type": "string"}, "appId": {"type": "string"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "actionType": {"type": "string"}, "taskType": {"type": "string"}, "remindLatency": {"type": "integer", "format": "int32"}, "assignee": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "opinions": {"type": "array", "items": {"$ref": "#/definitions/OpinionPojo"}}, "candidateIds": {"type": "array", "items": {"type": "string"}}, "bpmExtension": {"type": "object", "additionalProperties": {"type": "object"}}, "workflowName": {"type": "string"}, "workflowDescription": {"type": "string"}, "description": {"type": "string"}, "state": {"type": "string"}, "execution": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/ExecutionPojo"}}}, "approverModifyLog": {"type": "array", "items": {"$ref": "#/definitions/ApproverModifyLogObject"}}, "rule": {"$ref": "#/definitions/StartupRulePojo"}, "assignNextTask": {"type": "integer", "format": "int32"}, "externalApplyTask": {"type": "integer", "format": "int32"}}}, "TaskQueryResult": {"type": "object", "properties": {"taskList": {"type": "array", "items": {"$ref": "#/definitions/ApprovalTaskInfo"}}, "countAll": {"type": "integer", "format": "int64"}}}, "TotalNumberCheckArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}}}, "TriggerDataArg": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "object"}}, "callbackData": {"type": "object", "additionalProperties": {"type": "object"}}}}, "TriggerDataPojo": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "object"}}, "callbackData": {"type": "object", "additionalProperties": {"type": "object"}}}}, "UserTaskActIdArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "instanceId": {"type": "string"}, "activityId": {"type": "string"}}}, "UserTaskArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "entityId": {"type": "string"}, "state": {"type": "string"}, "pageInfo": {"$ref": "#/definitions/PageInfo"}}}, "UserTaskIdArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "taskId": {"type": "string"}}}, "UserTaskState": {"type": "object", "properties": {"state": {"type": "string"}}}, "WfEnableArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "sourceWorkflowId": {"type": "string"}, "enable": {"type": "boolean", "default": false}, "appId": {"type": "string"}}}, "WfObjectIdArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "objectId": {"type": "string"}}}, "WfTriggerArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "entityId": {"type": "string"}, "objectId": {"type": "string"}, "operationType": {"type": "integer", "format": "int32"}, "triggerData": {"$ref": "#/definitions/TriggerDataArg"}}}, "WholeDefinitionArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "sourceWorkflowId": {"type": "string"}}}, "WorkflowContext": {"type": "object", "properties": {"tenantId": {"type": "string"}, "appId": {"type": "string"}, "userId": {"type": "string"}, "properties": {"type": "object", "additionalProperties": {"type": "string"}}, "objectProperties": {"type": "object", "additionalProperties": {"type": "object"}}}}, "WorkflowIdArg": {"type": "object", "properties": {"context": {"$ref": "#/definitions/WorkflowContext"}, "workflowId": {"type": "string"}}}, "WorkflowInfo": {"type": "object", "properties": {"workflowId": {"type": "string"}, "srcWorkflowId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "enable": {"type": "boolean", "default": false}, "creator": {"type": "string"}, "createTime": {"type": "integer", "format": "int64"}, "modifier": {"type": "string"}, "modifyTime": {"type": "integer", "format": "int64"}, "entityId": {"type": "string"}, "triggerTypes": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "priority": {"type": "integer", "format": "int32"}}}, "WorkflowLimitedNumberPojo": {"type": "object", "properties": {"allowable": {"type": "boolean", "default": false}, "tenantId": {"type": "string"}, "limitedNumber": {"type": "integer", "format": "int32"}, "currentNumber": {"type": "integer", "format": "int32"}}}, "WorkflowPojo": {"type": "object", "properties": {"sourceWorkflowId": {"type": "string"}, "workflowId": {"type": "string"}, "ruleId": {"type": "string"}}}, "XTInstancePojo": {"type": "object", "properties": {"id": {"type": "string"}, "state": {"type": "string"}, "applicantId": {"type": "string"}, "applyTime": {"type": "integer", "format": "int64"}, "curTasks": {"type": "array", "items": {"$ref": "#/definitions/ApprovalTaskObject"}}, "allActivities": {"type": "array", "items": {"$ref": "#/definitions/ActivityPojo"}}}}, "expression": {"type": "object", "properties": {"expression": {"type": "string"}, "returnType": {"type": "string"}}}}}