package com.facishare.paas.appframework.coordination;

import com.facishare.fsi.proxy.annotation.FsiService;
import com.facishare.fsi.proxy.annotation.FsiUri;
import com.facishare.paas.appframework.coordination.dto.GetSearchFilterList;
import com.facishare.paas.appframework.coordination.dto.GetTableConfigList;

@FsiService("CRMBasic")
public interface CrmBasicServiceProxy {

  @FsiUri("/EnterpriseBasic/UserFilter/GetUserFilterByTableName")
  GetSearchFilterList.Result getSearchFilterList(GetSearchFilterList.Arg arg, String ea);

  @FsiUri("/EnterpriseBasic/UserTable/GetTable")
  GetTableConfigList.Result getTableConfigList(GetTableConfigList.Arg arg, String ea);
}
