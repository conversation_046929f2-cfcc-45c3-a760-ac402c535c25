package com.facishare.paas.appframework.metadata.dto.auth;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

public interface FindRecordTypeModel {

    @EqualsAndHashCode(callSuper = true)
    @Data
    class Arg extends BaseAuthArg {
        private String entityId;
        private String roleCode;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    class Result extends BaseAuthResult {
        private List<RecordTypePojo> result;
    }

}
