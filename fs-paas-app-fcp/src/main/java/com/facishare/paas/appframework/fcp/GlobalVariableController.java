package com.facishare.paas.appframework.fcp;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.GlobalVariableService;
import com.facishare.paas.appframework.core.predef.service.dto.globalvariable.*;
import com.facishare.paas.appframework.core.model.ContextManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by zhouwr on 2017/11/1
 */
@Component
@FcpService("globalVariable")
public class GlobalVariableController {

    @Autowired
    private GlobalVariableService globalVariableService;

    @FcpMethod("create")
    public CreateGlobalVariable.Result createGlobalVarialbe(CreateGlobalVariable.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("globalVariable", "create");
        return globalVariableService.createGlobalVarialbe(arg, context);
    }

    @FcpMethod("update")
    public UpdateGlobalVariable.Result updateGloableVariable(UpdateGlobalVariable.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("globalVariable", "update");
        return globalVariableService.updateGlobalVariable(arg, context);
    }

    @FcpMethod("delete")
    public DeleteGlobalVariable.Result deleteGloableVarialbe(DeleteGlobalVariable.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("globalVariable", "delete");
        return globalVariableService.deleteGloableVarialbe(arg, context);
    }

    @FcpMethod("findGlobalVariableList")
    public GetGlobalVariableList.Result getGlobalVariableList(GetGlobalVariableList.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("globalVariable", "findGlobalVariableList");
        return globalVariableService.getGlobalVariableList(arg, context);
    }

    @FcpMethod("findGlobalVariableInfo")
    public GetGloableVariableDetail.Result getGlobalVariableDetail(GetGloableVariableDetail.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("globalVariable", "findGlobalVariableInfo");
        return globalVariableService.getGlobalVariableDetail(arg, context);
    }
}
