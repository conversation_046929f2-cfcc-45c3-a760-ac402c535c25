package com.facishare.paas.appframework.metadata.publicobject.module;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.repository.model.MtPublicObjectJobDetail;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2023/12/25
 */
@Data
public class EnterpriseRelationResult {
    public static String CONNECTED_EVENT = "connected";
    public static String UNCONNECTED_EVENT = "unconnected";

    private Integer totalNumber;
    private List<IObjectData> dataList;
    private IObjectDescribe describe;
    private List<OperateInfo> operateInfoList;

    public static EnterpriseRelationResult empty() {
        return new EnterpriseRelationResult();
    }

    public static EnterpriseRelationResult from(IObjectDescribe describe, List<IObjectData> enterpriseRelationDataList,
                                                Integer totalNumber) {
        EnterpriseRelationResult result = new EnterpriseRelationResult();
        result.setTotalNumber(totalNumber);
        result.setDescribe(describe);
        result.setDataList(enterpriseRelationDataList);
        return result;
    }

    public void fillOperateInfoList(List<MtPublicObjectJobDetail> jobDetailDataList, String event) {
        Map<String, MtPublicObjectJobDetail> jobDetailMap = CollectionUtils.nullToEmpty(jobDetailDataList).stream()
                .collect(Collectors.toMap(MtPublicObjectJobDetail::getEnterpriseRelationId, Function.identity()));

        List<OperateInfo> operateInfos = Lists.newArrayList();
        for (IObjectData objectData : dataList) {
            OperateInfo operateInfo = OperateInfo.from(objectData, getJobDetail(jobDetailMap, objectData.getId(), event), event);
            operateInfos.add(operateInfo);
        }
        this.operateInfoList = operateInfos;
    }

    private MtPublicObjectJobDetail getJobDetail(Map<String, MtPublicObjectJobDetail> jobDetailMap, String dataId, String event) {
        MtPublicObjectJobDetail objectJobDetail = jobDetailMap.get(dataId);
        if (Objects.isNull(objectJobDetail)) {
            return null;
        }
        PublicObjectJobType publicObjectJobType = PublicObjectJobType.fromType(objectJobDetail.getType());
        if (CONNECTED_EVENT.equals(event)
                && PublicObjectJobType.DISABLE_JOB != publicObjectJobType) {
            return null;
        }
        if (UNCONNECTED_EVENT.equals(event)
                && PublicObjectJobType.OPEN_JOB != publicObjectJobType
                && PublicObjectJobType.ENABLE_JOB != publicObjectJobType
                && PublicObjectJobType.INVITATION_JOB != publicObjectJobType) {
            return null;
        }
        return objectJobDetail;
    }

    @Data
    public static class OperateInfo {
        private String dataId;
        private String operate;
        private String status;
        private String message;

        public static OperateInfo from(IObjectData objectData, MtPublicObjectJobDetail jobDetail, String event) {
            OperateInfo operateInfo = new OperateInfo();
            operateInfo.setDataId(objectData.getId());
            if (Objects.isNull(jobDetail)) {
                operateInfo.setOperate(makeOperate(objectData, event));
                return operateInfo;
            }
            String status = jobDetail.getStatus();
            operateInfo.setStatus(status);
            // 补充errorMsg
            Optional.ofNullable(jobDetail.getResult())
                    .map(PublicObjectJobDetailResultInfo::fromJson)
                    .ifPresent(result -> operateInfo.setMessage(result.errorMessage()));
            PublicObjectJobStatus jobStatus = PublicObjectJobStatus.fromStatus(status);
            if (PublicObjectJobStatus.FAILED == jobStatus || PublicObjectJobStatus.CANCEL == jobStatus) {
                operateInfo.setOperate(makeOperate(objectData, event));
            }
            return operateInfo;
        }

        private static String makeOperate(IObjectData objectData, String event) {
            if (CONNECTED_EVENT.equals(event)) {
                return "disable";
            }
            if (UNCONNECTED_EVENT.equals(event)) {
                String manageMode = objectData.get(EnterpriseRelationSimpleInfo.MANAGE_MODE_API_NAME, String.class);
                if (EnterpriseRelationSimpleInfo.MANAGE_MODE_TYPE_SELF.equals(manageMode)) {
                    return "invite";
                }
                if (EnterpriseRelationSimpleInfo.MANAGE_MODE_TYPE_CUSTODY.equals(manageMode)) {
                    return "enable";
                }
            }
            return null;
        }
    }
}
