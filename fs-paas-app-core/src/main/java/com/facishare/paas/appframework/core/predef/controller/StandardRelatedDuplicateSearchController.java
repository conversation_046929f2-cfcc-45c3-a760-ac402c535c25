package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.service.DepartmentService;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.predef.service.ObjectDuplicatedSearchService;
import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.GetRelatedResults;
import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.GetResult;
import com.facishare.paas.appframework.metadata.DuplicatedSearchExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;

public class StandardRelatedDuplicateSearchController extends PreDefineController<GetRelatedResults.Arg, GetRelatedResults.Result> {

    private ObjectDuplicatedSearchService objectDuplicatedSearchService =
            SpringUtil.getContext().getBean("objectDuplicatedSearchService", ObjectDuplicatedSearchService.class);

    @Override
    protected GetRelatedResults.Result doService(GetRelatedResults.Arg arg) {
        IDuplicatedSearch duplicatedSearch = null;
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(controllerContext.getTenantId(), controllerContext.getObjectApiName())) {
            String ruleApiName = arg.getDuplicateRuleApiName();
            if (BooleanUtils.isFalse(arg.getIsNeedDuplicate()) && StringUtils.isNotBlank(arg.getRuleApiNameIntercepted())) {
                ruleApiName = arg.getRuleApiNameIntercepted();
            }
            if (StringUtils.isEmpty(ruleApiName)) {
                duplicatedSearch = findDuplicatedSearchRule();
            } else {
                duplicatedSearch = serviceFacade.findDuplicatedSearchByRuleApiName(controllerContext.getObjectApiName(),
                        ruleApiName, controllerContext.getTenantId(), false);
            }
        } else {
            duplicatedSearch = serviceFacade.findDuplicatedSearchByApiNameAndType(controllerContext.getTenantId(),
                    arg.getDescribeApiName(), arg.getType(), false);
        }
        if (Objects.isNull(duplicatedSearch)) {
            return GetRelatedResults.Result.builder().results(Lists.newArrayList()).build();
        }
        Set<String> apiNames = Sets.newHashSet();
        apiNames.addAll(objectDuplicatedSearchService.getApiNames(duplicatedSearch));
        apiNames.removeIf(x -> StringUtils.equals(x, arg.getDescribeApiName()));
        ControllerContext controllerContext = new ControllerContext(this.controllerContext.getRequestContext(), arg.getDescribeApiName(), "DuplicateSearch");
        List<GetResult.Result> results = Lists.newArrayList();
        String relatedDuplicateSearchRuleApiName = duplicatedSearch.getRuleApiName();
        apiNames.forEach(x -> {
            GetResult.Arg build = GetResult.Arg.builder()
                    .describeApiName(arg.getDescribeApiName())
                    .type(arg.getType())
                    .objectData(arg.getObjectData())
                    .isNeedDuplicate(arg.getIsNeedDuplicate())
                    .includeObjectDescribes(false)
                    .pageSize(arg.getPageSize())
                    .pageNumber(arg.getPageNumber())
                    .assignDuplicateSearchApiName(relatedDuplicateSearchRuleApiName)
                    .duplicateRuleApiName(Objects.equals(IDuplicatedSearch.Type.TOOL, arg.getType()) ? arg.getDuplicateRuleApiName() : "")
                    .ruleApiNameIntercepted(arg.getRuleApiNameIntercepted())
                    .relatedApiName(x)
                    .build();
            GetResult.Result result = serviceFacade.triggerController(controllerContext, build, GetResult.Result.class);
            if (Objects.isNull(result)) {
                return;
            }
            if (CollectionUtils.empty(result.getDataList())) {
                return;
            }
            results.add(result);
        });
        return GetRelatedResults.Result.builder().results(results).build();
    }

    private IDuplicatedSearch findDuplicatedSearchRule() {
        if (IDuplicatedSearch.Type.TOOL.equals(arg.getType())) {
            return null;
        }
        if (BooleanUtils.isNotTrue(arg.getUseFirstRule())) {
            return null;
        }
        List<IDuplicatedSearch> duplicatedSearchList = serviceFacade.findDuplicateSearchByApiNameAndType(controllerContext.getTenantId(), arg.getDescribeApiName(), arg.getType(), false);
        if (CollectionUtils.empty(duplicatedSearchList)) {
            return null;
        }
        IObjectDescribe describe = serviceFacade.findObjectWithoutCopy(controllerContext.getTenantId(), arg.getDescribeApiName());
        duplicatedSearchList = DuplicatedSearchExt.filterDuplicatedSearch(duplicatedSearchList, ObjectDataExt.of(arg.getObjectData()).getObjectData(), describe, serviceFacade.getService(DepartmentService.class));
        return duplicatedSearchList.stream().findFirst().orElse(null);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }
}
