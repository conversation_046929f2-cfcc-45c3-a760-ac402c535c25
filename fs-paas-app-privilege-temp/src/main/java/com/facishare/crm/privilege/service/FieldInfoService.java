package com.facishare.crm.privilege.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.privilege.model.FieldInfo;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.metadata.DescribeLogicServiceImpl;
import com.facishare.paas.metadata.api.GroupField;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.privilege.util.PrivilegeFieldsUtils.*;
import static com.facishare.crm.userdefobj.DefObjConstants.systemFieldListFormObjectMap;

/**
 * Created by luxin on 2017/10/31.
 */
@Service
@Deprecated
public class FieldInfoService {
    @Autowired
    private DescribeLogicServiceImpl objectDescribeService;

    public Map<String, Map<String, FieldInfo>> getFieldInfo(String descApiName, String tenantId) throws MetadataServiceException {
        //定义返回的结果集
        Map<String, Map<String, FieldInfo>> resultFieldInfoMap = Maps.newLinkedHashMap();

        Map<String, FieldInfo> tempInfo = Maps.newLinkedHashMap();
        IObjectDescribe objectDescribe = objectDescribeService.findObject(tenantId, descApiName);
        if (objectDescribe == null) {
            return resultFieldInfoMap;
        }
        Set<String> systemFiledNames = systemFieldListFormObjectMap.get(DefObjConstants.UDOBJ);

        List<IFieldDescribe> groupChildrenFields = getGroupChildrenFields(objectDescribe);
        List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes().stream().filter(field -> !groupChildrenFields.contains(field)).collect(Collectors.toList());

        for (IFieldDescribe fieldDescribe : fieldDescribes) {
            String fieldDescribeApiName = fieldDescribe.getApiName();
            if (!"system".equals(fieldDescribe.getDefineType())
                    && !"extend_obj_data_id".equals(fieldDescribeApiName)
                    || (SFA_OBJECT_API_NAMES.contains(descApiName) && "name".equals(fieldDescribeApiName))) {
                //如果在不可见或者系统字段的配置信息里面,或者被禁用,就不处理,直接continue。
                if ((invisibleFields.contains(fieldDescribeApiName) || systemFiledNames.contains(fieldDescribeApiName) || (fieldDescribe.isActive() != null && !fieldDescribe.isActive()))
                        && !(SFA_OBJECT_API_NAMES.contains(descApiName) && "name".equals(fieldDescribeApiName))) {
                    continue;
                }

                if (CONTACT_NOT_DISPLAY_FIELD_NAMES.contains(fieldDescribeApiName) && "ContactObj".equals(descApiName)) {
                    continue;
                }

                boolean isEditableOfInvisible = Boolean.TRUE;
                boolean isEditableOfReadOnly = Boolean.TRUE;
                if ("name".equals(fieldDescribeApiName) || "owner".equals(fieldDescribeApiName)) {
                    isEditableOfInvisible = Boolean.FALSE;
                    isEditableOfReadOnly = Boolean.FALSE;
                }

                if (Utils.SUBPRODUCT_API_NAME.equals(descApiName) && SUB_PRODUCT_INVISIBLE_NOT_EDITABLE_FIELDS.contains(fieldDescribeApiName)) {
                    isEditableOfInvisible = Boolean.FALSE;
                }

                //否则就放入resultMap中。
                FieldInfo fieldInfo = new FieldInfo();
                fieldInfo.setFieldCaption(fieldDescribe.getLabel());
                fieldInfo.setFieldName(fieldDescribeApiName);
                fieldInfo.setIsEditableOfInvisible(isEditableOfInvisible);
                fieldInfo.setIsEditableOfReadOnly(isEditableOfReadOnly);
                fieldInfo.setIsRequire(fieldDescribe.isRequired());
                fieldInfo.setStatus(2);
                fieldInfo.setIsEditable(true);

                if (Utils.PERSONNEL_OBJ_API_NAME.equals(descApiName)) {
                    if (IFieldType.MASTER_DETAIL.equals(fieldDescribe.getType()) || !"custom".equals(fieldDescribe.getDefineType())) {
                        fieldInfo.setIsEditable(false);
                        fieldInfo.setStatus(1);
                    }
                }
                tempInfo.put(fieldDescribeApiName, fieldInfo);
            }
        }
        resultFieldInfoMap.put(descApiName, tempInfo);
        return resultFieldInfoMap;
    }

    @NotNull
    private List<IFieldDescribe> getGroupChildrenFields(IObjectDescribe objectDescribe) throws MetadataServiceException {
        List<IFieldDescribe> childrenFields = Lists.newArrayList();

        for (IFieldDescribe fieldDescribe : objectDescribe.getFieldDescribes()) {
            //如果字段是group类型的,找出这个字段的子字段
            if (IFieldType.GROUP.equals(fieldDescribe.getType())) {
                //从属于group字段的子字段
                childrenFields.addAll(((GroupField) fieldDescribe).getFieldList(objectDescribe));
            }
        }
        return childrenFields;
    }

}
