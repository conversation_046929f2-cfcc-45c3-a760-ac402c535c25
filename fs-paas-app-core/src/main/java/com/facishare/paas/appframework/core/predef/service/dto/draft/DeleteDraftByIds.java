package com.facishare.paas.appframework.core.predef.service.dto.draft;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

public interface DeleteDraftByIds {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private boolean success;
    }

    @Data
    class Arg {
        @JSONField(name = "draft_id_list")
        @JsonProperty("draft_id_list")
        @SerializedName("draft_id_list")
        private Set<String> draftIdList;
    }
}
