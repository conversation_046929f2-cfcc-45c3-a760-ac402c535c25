package com.facishare.paas.appframework.common.service

import com.facishare.organization.adapter.api.model.organizationwithouter.BatchGetEmployee
import com.facishare.organization.adapter.api.service.OrganizationWithOuterService
import spock.lang.Specification

class OuterOrganizationServiceTest extends Specification {

    def organizationWithOuterService = Mock(OrganizationWithOuterService)
    def outerOrganizationService = new OuterOrganizationServiceImpl(
            "organizationWithOuterService": organizationWithOuterService
    )

    private OuterOrganizationService organizationService;

    def "test batchGetEmployee"() {
        given:
        BatchGetEmployee.Result batchGetEmployee = Mock();
        when:
        print("========")
        organizationWithOuterService.batchGetEmployee(_) >> batchGetEmployee;
        def result = outerOrganizationService.batchGetEmployee(tenantId, outUserIds)
        then:
        result == result1
        where:
        tenantId | outUserIds                         || result1
        "74255"  | ["0.00000"]                        || []
        "74255"  | ["2312312", "23213"]               || []
        "74255"  | ["21.2123123", "222222"]           || []
        "74255"  | ["2.112321", "1212222", "1212212"] || []
        "74255"  | ["122212", "123e122"]              || []
        "74255"  | ["122212", "123122"]               || []
    }


}
