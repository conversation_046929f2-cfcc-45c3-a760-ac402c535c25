package com.facishare.paas.appframework.privilege.util;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MasterSlaveFunction implements Serializable {
    private String masterApiName;
    private String slaveApiName;
    private Map<String, String> slave2MasterFunctionCodes = Maps.newHashMap();
}