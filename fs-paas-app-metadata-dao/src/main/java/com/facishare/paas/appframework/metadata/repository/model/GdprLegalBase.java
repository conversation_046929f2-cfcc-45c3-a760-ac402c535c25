package com.facishare.paas.appframework.metadata.repository.model;

import com.facishare.paas.appframework.metadata.repository.annotation.*;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/8 16:34
 */

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity(apiName = GdprLegalBase.GDPR_LEGAL_BASE_OBJ)
public class GdprLegalBase extends BaseEntity {
    public static final String GDPR_LEGAL_BASE_OBJ = "GdprLegalBaseObj";
    public static final String GDPR_LEGAL_BASE_DATA_ID = "data_id";
    public static final String GDPR_LEGAL_BASE_API_NAME = "api_name";
    public static final String GDPR_LEGAL_BASE = "legal_base";
    public static final String GDPR_LEGAL_BASE_STATUS = "legal_base_status";
    public static final String GDPR_LEGAL_BASE_LINK_ID = "link_id";
    public static final String GDPR_LEGAL_BASE_AGREE_PERSON = "agree_person";

    @TrueOrFalseField(field = @ObjectField(apiName = IObjectData.IS_DELETED, label = "is_deleted"))
    private boolean deleted;

    @TextField(field = @ObjectField(apiName = IObjectDescribe.API_NAME, label = "对象apiName"))
    private String apiName;

    @TextField(field = @ObjectField(apiName = "data_id", label = "数据id"))
    private String dataId;

    @TrueOrFalseField(field = @ObjectField(apiName = "open_status", label = "数据处理基础开启状态"))
    private Boolean openStatus;

    @SelectOneField(field = @ObjectField(apiName = "legal_base", label = "数据法律基础"))
    private String legalBase;

    @SelectOneField(field = @ObjectField(apiName = "legal_base_status", label = "数据法律基础状态"))
    private String legalBaseStatus;

    @TextField(field = @ObjectField(apiName = "remark", label = "备注"))
    private String remark;

    @DateTimeField(field = @ObjectField(apiName = "agree_date", label = "同意日期"))
    private Long agreeDate;

    @SelectOneField(field = @ObjectField(apiName = "contact_way", label = "通过方式"))
    private String contactWay;

    @TextField(field = @ObjectField(apiName = "agree_person", label = "同意明细更新人"))
    private String agreePerson;

    @SelectManyField(field = @ObjectField(apiName = "agree_first_option", label = "同意首选项"))
    private List<String> agreeFirstOption;

    @TextField(field = @ObjectField(apiName = "link_id", label = "链接加密后的字符串"))
    private String linkId;

    @DateTimeField(field = @ObjectField(apiName = "link_create_time", label = "链接生成时间"))
    private Long linkCreateTime;

    public enum LegalBase {
        UNTREATED("untreated", "全部"),// ignoreI18n
        AGREE("agree", "同意"),// ignoreI18n
        LEGAL_BASIS("legalBasis", "合法基础"),// ignoreI18n
        CONTRACT("contract", "合同"),// ignoreI18n
        LEGAL_OBLIGATION("legalObligation", "法律义务"),// ignoreI18n
        IMPORTANT_INTERESTS("importantInterests", "重要利益"),// ignoreI18n
        COMMON_INTEREST("commonInterest", "共同利益");// ignoreI18n

        private String value;
        private String label;

        LegalBase(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return value;
        }
    }

    public enum AgreeStatus {
        PENDING("pending", "待决"),// ignoreI18n
        OBTAIN("obtain", "获取"),// ignoreI18n
        WAITING("waiting", "等待中"),// ignoreI18n
        NO_REPLY("no_reply", "没有回复");// ignoreI18n

        private String value;
        private String label;

        AgreeStatus(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return value;
        }
    }
}
