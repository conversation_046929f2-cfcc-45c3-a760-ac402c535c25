package com.facishare.paas.appframework.license.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by zhouwr on 2017/10/13
 */
public interface GetModuleParaLicenses {
    @Data
    class Arg {
        private AuthContext context;
        private String moduleCode;
        private List<String> paraKeys;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    class Result extends BaseResult {
        private List<ModuleParaLicense> result;
    }
}
