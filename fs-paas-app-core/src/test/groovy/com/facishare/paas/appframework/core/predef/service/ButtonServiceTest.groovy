package com.facishare.paas.appframework.core.predef.service


import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.User
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Specification

class ButtonServiceTest extends Specification {

    static {
        System.setProperty("spring.profiles.active", "fstest")
    }

    def tenantId = "78586"
    @Autowired
    ButtonService buttonService


    def setup() {
        RequestContext requestContext = RequestContext.builder().user(Optional.of(User.systemUser(tenantId))).build()
        RequestContextManager.setContext(requestContext)
    }

    def json = "{\\\"func_args\\\":[],\\\"func_api_name\\\":\\\"func_8Dg99__c\\\"}"


    def "test updateButtonPostAction"() {
        expect:
        1 == 1
        /*
        given:
        ServiceContext context = ContextManager.buildServiceContext("button", "updateButtonPostAction")
        def udefAction = new UdefAction()
        udefAction.setBizKey("customCheck")
        udefAction.setActionType("custom_biz")
        udefAction.setDescribeApiName("object_p2oeJ__c")
        udefAction.setActionParamter("{}")
        udefAction.setStage("pre")
        def arg = UpdateButtonPostAction.Arg.builder()
                .buttonApiName("Add_button_default")
                .describeApiName("object_p2oeJ__c")
                .udefAction(Lists.newArrayList(udefAction)).build()
        when:
        buttonService.updateButtonPostAction(arg, context)
        then:
        print(1 == 1)
        */
    }

    def "test deleteButtonPostAction"() {
        expect:
        1 == 1
        /*
        given:
        ServiceContext context = ContextManager.buildServiceContext("button", "deleteButtonPostAction")
        def arg = new DeleteButtonPostAction.Arg()
        arg.setDescribeApiName("DeliveryNoteObj")
        arg.setActionIds(Lists.newArrayList("60c1ac2dfe145700010830c2"))
        arg.setButtonApiName("ConfirmReceipt_button_default")
        when:
        buttonService.deleteButtonPostAction(arg, context)
        then:
        print(1 == 1)
        */

    }
}
