package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhaopx on 2017/11/1.
 */
public class ObjectFieldDescribeDocument extends DocumentBaseEntity {

    public ObjectFieldDescribeDocument() {
    }

    public ObjectFieldDescribeDocument(Map<String, Object> data) {
        super(data);
    }

    public static ObjectFieldDescribeDocument of(IFieldDescribe fieldDescribe) {
        if (fieldDescribe == null) {
            return null;
        }
        return new ObjectFieldDescribeDocument(((DocumentBasedBean) fieldDescribe).getContainerDocument());
    }

    public static List<ObjectFieldDescribeDocument> ofList(List<IFieldDescribe> fields) {
        if (CollectionUtils.empty(fields)) {
            return Lists.newArrayList();
        }
        return fields.stream().map(ObjectFieldDescribeDocument::of).collect(Collectors.toList());
    }
}
