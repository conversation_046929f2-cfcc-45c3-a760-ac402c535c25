package com.facishare.paas.appframework.metadata.relation;

import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.expression.SimpleExpression;
import com.facishare.paas.appframework.metadata.relation.FieldRelationValidator.ValidateField;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IGlobalVariableDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.NonNull;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Supplier;

/**
 * Created by zhouwr on 2018/5/23
 */
public interface FieldRelationCalculateService {

    CalculateFields getCalculateFieldsForBatchCreate(IObjectDescribe describe, boolean excludeDefaultValue);

    CalculateFields getCalculateFieldsByChanges(IObjectDescribe masterDescribe, List<IObjectDescribe> detailDescribes,
                                                Map<String, Object> masterChange, Map<String, Map<String, Object>> detailChangeMap);

    FieldRelation computeCalculateRelation(IObjectDescribe masterDescribe, List<IObjectDescribe> detailDescribes);

    FieldRelation computeCalculateRelationWithExt(@NonNull IObjectDescribe masterDescribe, List<IObjectDescribe> detailDescribes,
                                                  Map<String, IObjectDescribe> describeExtMap);

    /**
     * 计算指定计算字段相关的字段(计算字段、引用字段、统计字段)
     *
     * @param calcFieldNames
     * @param describe
     * @return
     */
    CalculateFields computeCalculateFields(List<String> calcFieldNames, IObjectDescribe describe);

    CalculateFields computeCalculateFieldsForAddAction(IObjectDescribe masterDescribe, List<IObjectDescribe> detailDescribes, boolean excludeDefaultValue);

    CalculateFields computeCalculateFieldsForAddAction(@NonNull IObjectDescribe masterDescribe,
                                                       List<IObjectDescribe> detailDescribes,
                                                       boolean excludeDefaultValue,
                                                       boolean includeLookupObjects,
                                                       boolean excludeLookupRelateField);

    CalculateFields computeCalculateFieldsForEditData(EditCalculateParam editCalculateParam);

    CalculateFields computeCalculateFieldsForBatchEditData(BatchEditCalculateParam batchEditCalculateParam);

    CalculateFields convertToCalculateFields(String tenantId,
                                             Map<String, List<String>> fieldNameMap,
                                             Map<String, IObjectDescribe> describeMap);

    void validateFieldRelation(IObjectDescribe newDescribe, IObjectDescribe describeInDb, List<ValidateField> fields);

    List<IFieldDescribe> validateByField(IObjectDescribe describe, IFieldDescribe fieldDescribe);

    List<IFieldDescribe> validateByFields(IObjectDescribe describe, List<IFieldDescribe> iFieldDescribes);

    List<IFieldDescribe> validateByFields(IObjectDescribe describeInDb, List<IFieldDescribe> fieldDescribes, boolean updateReference);

    List<IFieldDescribe> validateByObjectDescribe(IObjectDescribe objectDescribe, IObjectDescribe describeInDb);

    void checkDecimalDigitChangeByDescribe(IObjectDescribe describe, IObjectDescribe describeInDb);

    void checkDecimalDigitChangeByFields(IObjectDescribe describe, List<IFieldDescribe> fieldDescribes, Boolean isFilter);

    List<IFieldDescribe> findUnStoredFormulaFields(IObjectDescribe objectDescribe, List<IFieldDescribe> formulaFields, List<String> includeType);

    Map<String, Set<String>> findUnReCalculateFields(IObjectDescribe objectDescribe, Map<String, IObjectDescribe> detailDescribeMap, FieldRelationGraph relationGraph);

    void deleteFormulaReferenceByDescribe(String tenantId, IObjectDescribe oldDescribe, IObjectDescribe newDescribe);

    void deleteFormulaReferenceByFields(String tenantId, String objectApiName, List<IFieldDescribe> fieldDescribes);

    void deleteFormulaReference(String tenantId, String objectApiName, List<String> formulaApiNames);

    List<ReferenceData> checkReferenceOfFormulaField(IObjectDescribe objectDescribe, List<IFieldDescribe> formulaFields, boolean isUpdateReferenceData);

    void findRelationFormulaOfGlobalVariable(IGlobalVariableDescribe globalVariable, IGlobalVariableDescribe dbGlobalVariable);

    void checkSelectOneChangeOfDescribe(IObjectDescribe newDescribe, IObjectDescribe describeInDb);

    void checkSelectOneChangeOfFields(IObjectDescribe newDescribe, IObjectDescribe describeInDb, List<IFieldDescribe> fieldDescribes, boolean isNeedDiff);

    List<IFieldDescribe> checkQuoteField(IObjectDescribe describe, IFieldDescribe fieldDescribe);

    List<IFieldDescribe> checkQuoteFieldsByObjectDescribe(IObjectDescribe objectDescribe, IObjectDescribe describeInDb);

    void checkObjectReferenceField(User user, IObjectDescribe describe, IFieldDescribe fieldDescribe);

    void cleanReferenceFieldRelation(User user, IObjectDescribe describe, IFieldDescribe fieldDescribe);

    void checkObjectReferenceFields(User user, IObjectDescribe describe, List<IFieldDescribe> fieldDescribes);

    List<ReferenceData> buildReferenceFieldRelation(User user, IFieldDescribe fieldDescribe);

    void checkObjectReferenceFieldsByObjectDescribe(User user, IObjectDescribe objectDescribe, IObjectDescribe describeInDb);

    void computeCalculateFieldsForMappingAction(User user, String buttonApiName, String originalObjectApiName, IObjectDescribe masterDescribe, List<IObjectDescribe> detailDescribes,
                                                IObjectData masterData, Map<String, List<IObjectData>> detailDataMap, List<String> filterDetail);

    void computeCalculateFieldsForConvertAction(User user, IObjectDescribe masterDescribe, List<IObjectDescribe> detailDescribes,
                                                IObjectData masterData, Map<String, List<IObjectData>> detailDataMap, List<String> filterDetail, Supplier<List<IObjectMappingRuleInfo>> supplier);

    void checkRecordLabelChange(IObjectDescribe oldDescribe, IRecordTypeOption option, boolean needDiff);

    List<IFieldDescribe> validateByFields(IObjectDescribe describeInDb, List<IFieldDescribe> fieldDescribes,
                                          boolean updateReference, List<SimpleExpression.VariableInfo> extVariables);

    List<IFieldDescribe> notEndJobForEach(User user, String objApiName, List<IFieldDescribe> fieldDescribes);
}
