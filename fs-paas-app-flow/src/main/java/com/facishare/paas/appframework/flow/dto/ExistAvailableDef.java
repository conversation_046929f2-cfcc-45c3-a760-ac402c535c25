package com.facishare.paas.appframework.flow.dto;

import lombok.Builder;
import lombok.Data;

public interface ExistAvailableDef {

    @Data
    @Builder
    class Arg {
        // 对象id
        private String entityId;
        // 数据id
        private String objectId;
    }

    @Data
    @Builder
    class Result {
        private int code;
        private String message;
        private ExistAvailable data;
    }

    @Data
    @Builder
    class ExistAvailable{
        private Boolean existAvailableBpmDef;
        private Boolean existAvailableStageDef;
    }
}
