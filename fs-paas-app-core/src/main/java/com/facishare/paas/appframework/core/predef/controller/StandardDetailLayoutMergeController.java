package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.layout.ComponentHeaderSetter;
import com.facishare.paas.appframework.metadata.layout.InitDetailLayoutBuilder;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.layout.LayoutVersion;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.functions.utils.Maps;
import com.fxiaoke.i18n.client.api.Localization;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

public class StandardDetailLayoutMergeController extends PreDefineController<StandardDetailLayoutMergeController.Arg, StandardDetailLayoutMergeController.Result> {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        validate();
    }

    private void validate() {
        if (StringUtils.isEmpty(arg.getAddEditLayoutApiName()) || CollectionUtils.empty(arg.getLayout())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
    }

    @Override
    protected Result doService(Arg arg) {
        ILayout editLayout = serviceFacade.getLayoutLogicService().findLayoutByApiName(controllerContext.getUser(), arg.getAddEditLayoutApiName(), controllerContext.getObjectApiName());
        if (Objects.isNull(editLayout)) {
            throw new ValidateException(I18NExt.text(I18NKey.ERRORCODE_LAYOUT_NOT_EXIST));
        }
        List<String> layoutTransKeys = LayoutExt.of(editLayout).getLayoutTransKeys();
        boolean supportMultiLanguage = serviceFacade.isSupportMultiLanguage(controllerContext.getTenantId());
        Map<String, Localization> localizationMap = Maps.newHashMap();
        if (supportMultiLanguage) {
            localizationMap = infraServiceFacade.getLocalization(layoutTransKeys, controllerContext.getTenantId(), false, true);
        }
        IObjectDescribe describeInDb = serviceFacade.findObjectWithoutCopyIfGray(controllerContext.getTenantId(), controllerContext.getObjectApiName());
        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = infraServiceFacade.findOptionalFeaturesSwitch(controllerContext.getTenantId(), describeInDb);
        LayoutExt detailLayout = LayoutExt.of(arg.getLayout());
        InitDetailLayoutBuilder.builder()
                .editLayout(editLayout)
                .detailLayout(detailLayout)
                .describe(describeInDb)
                .optionalFeaturesSwitch(optionalFeaturesSwitch)
                .describeLogicService(serviceFacade)
                .build()
                .mergeEditLayoutFieldConfigToDetailLayout();
        if (supportMultiLanguage) {
            LayoutExt layoutExt = LayoutExt.of(detailLayout);
            ComponentHeaderSetter.builder()
                    .tenantId(layoutExt.getTenantId())
                    .layoutType(LayoutTypes.DETAIL)
                    .layoutVersion(LayoutVersion.V3)
                    .components(layoutExt.getComponentsSilently())
                    .objectApiName(layoutExt.getRefObjectApiName())
                    .layoutApiName(layoutExt.getName())
                    .existMultiLanguage(supportMultiLanguage)
                    .componentPreKeyMap(serviceFacade.getLayoutLogicService().findComponentPreKeys(layoutExt.getComponentsSilently()))
                    .localizationMap(localizationMap)
                    .build()
                    .reset();
        }

        Result result = new Result();
        result.setLayout(LayoutDocument.of(detailLayout));
        return result;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Data
    public static class Arg {
        private LayoutDocument layout;
        private String addEditLayoutApiName;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private LayoutDocument layout;
    }
}
