package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.dataMultiLang.*;
import com.facishare.paas.appframework.metadata.DataLanguageService;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.ObjectType;
import com.facishare.paas.metadata.api.describe.DynamicDescribe;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.openapi.Utils.DEPARTMENT_OBJ_API_NAME;
import static com.facishare.crm.openapi.Utils.PERSONNEL_OBJ_API_NAME;
import static com.facishare.paas.appframework.common.util.AppFrameworkConfig.getEnableMultiLangFieldCountByDescribeApiName;
import static com.facishare.paas.appframework.metadata.objects.ObjectListConfig.OBJECT_MANAGEMENT;
import static com.facishare.paas.metadata.api.describe.IObjectDescribe.DEFINE_TYPE_CUSTOM;

@Slf4j
@Service
@ServiceModule("dataMultiLang")
public class ObjectDataMultiLangService {

    private static final String ALL = "ALL";
    private static final String UDOBJ = "customObj";

    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private DataLanguageService dataLanguageService;

    @ServiceMethod("create")
    public ChangeMultiLanguageField.Result create(ChangeMultiLanguageField.Arg arg, ServiceContext context) {
        List<ChangeMultiLanguageField.MultiLanguageField> enableMultiLanguageInfoList = arg.getEnableMultiLanguageInfo();
        if (CollectionUtils.empty(enableMultiLanguageInfoList)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        return addMultiLangObj(enableMultiLanguageInfoList, context);
    }

    private List<String> validateFieldCount(List<ChangeMultiLanguageField.MultiLanguageField> enableMultiLanguageInfoList, Map<String, IObjectDescribe> describeMap) {
        List<String> displayNameList = Lists.newArrayList();
        List<String> describeApiNameList = Lists.newArrayList();
        for (ChangeMultiLanguageField.MultiLanguageField multiLanguageField : enableMultiLanguageInfoList) {
            IObjectDescribe objectDescribe = describeMap.get(multiLanguageField.getDescribeApiName());
            if (Objects.isNull(objectDescribe)) {
                continue;
            }
            List<IFieldDescribe> fieldByApiNames = ObjectDescribeExt.of(objectDescribe).getFieldByApiNames(multiLanguageField.getEnableField());
            List<FieldDescribeExt> customFieldList = fieldByApiNames.stream()
                    .map(FieldDescribeExt::of)
                    .filter(FieldDescribeExt::isCustomField)
                    .collect(Collectors.toList());
            if (customFieldList.size() > getEnableMultiLangFieldCountByDescribeApiName(objectDescribe.getApiName())) {
                displayNameList.add(objectDescribe.getDisplayName());
                describeApiNameList.add(objectDescribe.getApiName());
            }
        }
        enableMultiLanguageInfoList.removeIf(x -> describeApiNameList.contains(x.getDescribeApiName()));
        return displayNameList;
    }

    @ServiceMethod("update")
    public ChangeMultiLanguageField.Result update(ChangeMultiLanguageField.Arg arg, ServiceContext context) {
        List<ChangeMultiLanguageField.MultiLanguageField> enableMultiLanguageInfoList = arg.getEnableMultiLanguageInfo();
        if (CollectionUtils.empty(enableMultiLanguageInfoList)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        return updateMultiLangObj(enableMultiLanguageInfoList, context);
    }

    private Map<String, Set<String>> changeMultiLangField(List<ChangeMultiLanguageField.MultiLanguageField> enableMultiLanguageInfoList, Map<String, IObjectDescribe> objectDescribeMap, ServiceContext context) {

        Map<String, List<String>> supportMultiLangObjField = AppFrameworkConfig.getSupportMultiLangObjField();
        //用diff过的数据转参数修改
        List<DynamicDescribe> dynamicDescribeList = diffChangeField(enableMultiLanguageInfoList, supportMultiLangObjField, objectDescribeMap);

        Map<String, Set<String>> failApiNameMap = Maps.newHashMap();
        describeLogicService.batchUpdateFieldDescribe(context.getUser(), dynamicDescribeList, failApiNameMap);
        return failApiNameMap;
    }


    private ChangeMultiLanguageField.Result updateMultiLangObj(List<ChangeMultiLanguageField.MultiLanguageField> enableMultiLanguageInfoList, ServiceContext context) {
        List<String> apiNameList = enableMultiLanguageInfoList.stream().map(ChangeMultiLanguageField.MultiLanguageField::getDescribeApiName).collect(Collectors.toList());
        Map<String, IObjectDescribe> objectDescribeMap = describeLogicService.findObjectsWithoutCopy(context.getTenantId(), apiNameList);
        //校验字段类型
        Map<String, Set<String>> unSupportMap = validateFieldType(context.getTenantId(), enableMultiLanguageInfoList, objectDescribeMap);
        if (CollectionUtils.notEmpty(unSupportMap)) {
            throw new ValidateException(I18NExt.text(I18NKey.ENABLE_FIELD_LANG_TYPE_ERROR, buildErrorInfo(unSupportMap, objectDescribeMap)));
        }
        //校验字段数量
        List<String> moreFieldCountDescribeApiNames = validateFieldCount(enableMultiLanguageInfoList, objectDescribeMap);
        if (CollectionUtils.notEmpty(moreFieldCountDescribeApiNames)) {
            throw new ValidateException(I18NExt.text(I18NKey.MULTI_LANG_FIELD_COUNT_ERROR, moreFieldCountDescribeApiNames, getEnableMultiLangFieldCountByDescribeApiName(ALL)));
        }
        Map<String, Set<String>> changeFailMap = changeMultiLangField(enableMultiLanguageInfoList, objectDescribeMap, context);
        if (CollectionUtils.notEmpty(changeFailMap)) {
            throw new ValidateException(I18NExt.text(I18NKey.ENABLE_FIELD_LANG_ERROR, buildErrorInfo(changeFailMap, objectDescribeMap)));
        }

        return ChangeMultiLanguageField.Result.builder().build();
    }

    private ChangeMultiLanguageField.Result addMultiLangObj(List<ChangeMultiLanguageField.MultiLanguageField> enableMultiLanguageInfoList, ServiceContext context) {
        List<String> apiNameList = enableMultiLanguageInfoList.stream().map(ChangeMultiLanguageField.MultiLanguageField::getDescribeApiName).distinct().collect(Collectors.toList());
        StringBuilder errInfo = new StringBuilder();
        List<List<String>> partition = Lists.partition(apiNameList, 10);
        List<String> moreFieldCountDescribeApiNames = Lists.newArrayList();
        for (List<String> subApiNameList : partition) {
            //拿到本次启用多语的对象和字段
            List<ChangeMultiLanguageField.MultiLanguageField> subMultiLanguageFields = enableMultiLanguageInfoList.stream()
                    .filter(x -> subApiNameList.contains(x.getDescribeApiName())).collect(Collectors.toList());
            Map<String, IObjectDescribe> objectDescribeMap = describeLogicService.findObjectsWithoutCopy(context.getTenantId(), subApiNameList);

            //校验字段类型
            errInfo.append(buildErrorInfo(validateFieldType(context.getTenantId(), subMultiLanguageFields, objectDescribeMap), objectDescribeMap));
            //校验字段数量
            moreFieldCountDescribeApiNames.addAll(validateFieldCount(subMultiLanguageFields, objectDescribeMap));

            Map<String, Set<String>> changeFailMap = changeMultiLangField(subMultiLanguageFields, objectDescribeMap, context);
            errInfo.append(buildErrorInfo(changeFailMap, objectDescribeMap));
        }
        if (StringUtils.isNotBlank(errInfo.toString())) {
            throw new ValidateException(I18NExt.text(I18NKey.ENABLE_FIELD_LANG_ERROR, errInfo));
        }
        if (CollectionUtils.notEmpty(moreFieldCountDescribeApiNames)) {
            throw new ValidateException(I18NExt.text(I18NKey.MULTI_LANG_FIELD_COUNT_ERROR, moreFieldCountDescribeApiNames, getEnableMultiLangFieldCountByDescribeApiName(ALL)));
        }

        return ChangeMultiLanguageField.Result.builder().build();
    }


    private String buildErrorInfo(Map<String, Set<String>> failApiNameMap, Map<String, IObjectDescribe> objectDescribeMap) {
        if (CollectionUtils.empty(failApiNameMap)) {
            return "";
        }
        StringBuilder errInfo = new StringBuilder();
        Set<Map.Entry<String, Set<String>>> entrySet = failApiNameMap.entrySet();
        for (Map.Entry<String, Set<String>> entry : entrySet) {
            String describeApiName = entry.getKey();
            Set<String> fieldApiNameList = entry.getValue();
            if (StringUtils.isBlank(describeApiName) || CollectionUtils.empty(fieldApiNameList)) {
                continue;
            }
            IObjectDescribe describe = objectDescribeMap.get(describeApiName);
            if (Objects.isNull(describe)) {
                errInfo.append(describeApiName).append(":");
                errInfo.append(fieldApiNameList).append(",");
            } else {
                errInfo.append(describe.getDisplayName()).append(":");
                Map<String, IFieldDescribe> fieldDescribeMap = describe.getFieldDescribeMap();
                List<String> labelList = fieldApiNameList.stream().map(fieldDescribeMap::get)
                        .filter(Objects::nonNull)
                        .map(IFieldDescribe::getLabel)
                        .collect(Collectors.toList());
                errInfo.append(labelList);
                errInfo.append(",");
            }
        }
        if (StringUtils.isBlank(errInfo.toString())) {
            return "";
        }
        return errInfo.delete(errInfo.lastIndexOf(","), errInfo.length()).toString();
    }

    private List<DynamicDescribe> diffChangeField(List<ChangeMultiLanguageField.MultiLanguageField> paramDataLanguageList,
                                                  Map<String, List<String>> supportDatLanguageMap, Map<String, IObjectDescribe> objectDescribeMap) {
        Map<String, ChangeMultiLanguageField.MultiLanguageField> paramDataLanguageMap = paramDataLanguageList.stream()
                .collect(Collectors.toMap(ChangeMultiLanguageField.MultiLanguageField::getDescribeApiName, it -> it, (x, y) -> x));

        List<DynamicDescribe> result = Lists.newArrayList();
        //循环参数中的每个对象及字段信息
        for (Map.Entry<String, ChangeMultiLanguageField.MultiLanguageField> paramEntry : paramDataLanguageMap.entrySet()) {
            String paramDescribeApiName = paramEntry.getKey();
            ChangeMultiLanguageField.MultiLanguageField paramDataLanguage = paramEntry.getValue();
            IObjectDescribe objectDescribe = objectDescribeMap.get(paramDescribeApiName);
            //拿到当前对象支持的预置字段
            List<String> preFieldApiNames = CollectionUtils.nullToEmpty(AppFrameworkConfig.getSupportMultiLangPreFieldByDescribeApiName(paramDescribeApiName));
            if (Objects.isNull(objectDescribe)) {
                continue;
            }
            List<IFieldDescribe> fieldByApiNames = ObjectDescribeExt.of(objectDescribe).getFieldByApiNames(paramDataLanguage.getEnableField());
            //过滤出参数中可以启用数据多语的字段
            List<String> enableFieldList = fieldByApiNames.stream()
                    .filter(x -> FieldDescribeExt.of(x).isTextType())
                    .filter(x -> FieldDescribeExt.of(x).isCustomField() || preFieldApiNames.contains(x.getApiName()))
                    .map(IFieldDescribe::getApiName)
                    .collect(Collectors.toList());
            //获取到已经启用的多语字段
            List<String> dbEnableFieldList = ObjectDescribeExt.of(objectDescribe).getEnableMultiLangField().stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
            //取两者差集获取到本次被禁用的字段
            List<String> deleteField = Lists.newArrayList(org.apache.commons.collections4.CollectionUtils.subtract(dbEnableFieldList, enableFieldList));
            if (CollectionUtils.empty(enableFieldList) && CollectionUtils.empty(deleteField)) {
                continue;
            }
            result.add(convertToDynamicDescribe(objectDescribe.getApiName(), enableFieldList, deleteField));
        }
        return result;
    }

    private DynamicDescribe convertToDynamicDescribe(String describeApiName, List<String> enableField, List<String> disableField) {
        List<Map<String, Object>> fieldAttributeField = Lists.newArrayList();
        for (String apiName : enableField) {
            Map<String, Object> attributeMap = buildDynamicDescribe(apiName, true);
            fieldAttributeField.add(attributeMap);
        }
        for (String apiName : disableField) {
            Map<String, Object> attributeMap = buildDynamicDescribe(apiName, false);
            fieldAttributeField.add(attributeMap);
        }
        return new DynamicDescribe(describeApiName, fieldAttributeField);

    }

    private Map<String, Object> buildDynamicDescribe(String apiName, boolean statue) {
        Map<String, Object> updateAttributeMap = Maps.newHashMap();
        updateAttributeMap.put("fieldApiName", apiName);
        updateAttributeMap.put("attributeKey", "enable_multi_lang");
        updateAttributeMap.put("attributeValue", statue);
        return updateAttributeMap;
    }


    @ServiceMethod("getSupportObject")
    public GetSupportObject.Result getSupportObject(GetSupportObject.Arg arg, ServiceContext context) {
        //从配置中获取数据
        Map<String, List<String>> supportMultiLangObjField = AppFrameworkConfig.getSupportMultiLangObjField();
        if (CollectionUtils.empty(supportMultiLangObjField)) {
            return GetSupportObject.Result.builder().describeList(Lists.newArrayList()).build();
        }
        //从配置中支持多语的字段并拿到对象相关描述
        List<IObjectDescribe> objectDescribeList = getSupportObjectList(supportMultiLangObjField.keySet(), context, arg.getIncludeEnable());
        if (CollectionUtils.empty(objectDescribeList)) {
            return GetSupportObject.Result.builder().describeList(Lists.newArrayList()).build();
        }
        //构建返回结果
        List<GetSupportObject.DescribeInfo> describeInfoList = objectDescribeList.stream()
                .map(x -> GetSupportObject.DescribeInfo.builder().apiName(x.getApiName()).displayName(x.getDisplayName()).build())
                .collect(Collectors.toList());

        return GetSupportObject.Result.builder().describeList(describeInfoList).build();

    }

    @ServiceMethod("getSupportFieldByObject")
    public GetSupportFieldByObject.Result getSupportFieldByObject(GetSupportFieldByObject.Arg arg, ServiceContext context) {
        String describeApiName = arg.getDescribeApiName();
        if (StringUtils.isBlank(describeApiName)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_EMPTY));
        }
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopy(context.getTenantId(), describeApiName);
        if (Objects.isNull(describe)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_EMPTY));
        }

        List<IFieldDescribe> fieldList = Lists.newArrayList();
        //拿配置中的预设字段
        List<String> fieldApiNameList = CollectionUtils.nullToEmpty(AppFrameworkConfig.getSupportMultiLangPreFieldByDescribeApiName(describeApiName));
        List<IFieldDescribe> preFieldDescribeList = ObjectDescribeExt.of(describe).getFieldByApiNames(fieldApiNameList.stream().distinct().collect(Collectors.toList()));
        fieldList.addAll(preFieldDescribeList.stream().filter(x -> FieldDescribeExt.of(x).isTextType()).collect(Collectors.toList()));

        //自定义对象和预设对象都支持自定义的单行文本和多行文本字段
        fieldList.addAll(ObjectDescribeExt.of(describe).getFieldDescribesSilently().stream()
                .filter(x -> !fieldApiNameList.contains(x.getApiName()))
                .filter(x -> FieldDescribeExt.of(x).isCustomField())
                .filter(x -> FieldDescribeExt.of(x).isTextType())
                .collect(Collectors.toList()));

        //构建返回结果
        List<GetAllMultiLanguageField.FieldInfo> fieldInfoList = fieldList.stream()
                .map(x -> GetAllMultiLanguageField.FieldInfo.builder().fieldApiName(x.getApiName()).label(x.getLabel()).build())
                .collect(Collectors.toList());
        GetSupportFieldByObject.MultiLangInfo langInfo = GetSupportFieldByObject.MultiLangInfo.builder()
                .describeApiName(describe.getApiName())
                .fieldInfoList(fieldInfoList)
                .displayName(describe.getDisplayName()).build();

        return GetSupportFieldByObject.Result.builder().multiLangInfo(langInfo).build();
    }

    private List<IObjectDescribe> getSupportObjectList(Set<String> preSupportMultiLangObj, ServiceContext context, boolean includeEnable) {
        List<IObjectDescribe> describeList = Lists.newArrayList();
        //包含udobj则代表获取所有自定义对象的所有文本字段
        if (preSupportMultiLangObj.contains(UDOBJ) && AppFrameworkConfig.objectMultiLangGray(context.getTenantId(), UDOBJ)) {
            describeList = describeLogicService.findObjectsByTenantId(context.getUser(),
                    DEFINE_TYPE_CUSTOM, false, false,
                    false, false, OBJECT_MANAGEMENT, ObjectType.ALL);
            describeList.removeIf(x -> preSupportMultiLangObj.contains(x.getApiName()));
        }
        List<String> preObjectList = preSupportMultiLangObj.stream()
                .filter(x -> !UDOBJ.equals(x))
                .filter(x -> AppFrameworkConfig.objectMultiLangGray(context.getTenantId(), x))
                .collect(Collectors.toList());

        //补上配置中配的预置对象
        List<IObjectDescribe> preObjectDescribeList = describeLogicService.findObjectList(context.getTenantId(), preObjectList);
        describeList.addAll(describeLogicService.queryAvailableObject(context.getUser(), preObjectDescribeList));
        List<String> describeApiNameList = describeList.stream().map(x -> x.getApiName()).collect(Collectors.toList());
        describeList.addAll(preObjectDescribeList.stream()
                .filter(x -> !describeApiNameList.contains(x.getApiName()))
                .filter(x -> AppFrameworkConfig.specialMultiLangDescribeApiName().contains(x.getApiName()))
                .collect(Collectors.toList()));

        //移除变更单相关对象（原单+变更单）  变更单920不支持数据级多语
        List<IObjectDescribe> changeOrderList = describeList.stream()
                .filter(x -> ObjectDescribeExt.of(x).isChangeOrderObject()).collect(Collectors.toList());
        List<String> changeOrderRelateApiNameList = changeOrderList.stream().map(IObjectDescribe::getOriginalDescribeApiName).collect(Collectors.toList());
        changeOrderRelateApiNameList.addAll(changeOrderList.stream().map(IObjectDescribe::getApiName).collect(Collectors.toList()));

        describeList.removeIf(x -> changeOrderRelateApiNameList.contains(x.getApiName()));

        if (!includeEnable) {
            List<String> enableDescribeList = getEnableFieldListAndFormat(context).stream().map(GetEnableMultiLanguageField.MultiLanguageInfo::getDescribeApiName).collect(Collectors.toList());
            describeList.removeIf(x -> enableDescribeList.contains(x.getApiName()));
        }
        return describeList;
    }

//    @ServiceMethod("getAllMultiLanguageField")
//    public GetAllMultiLanguageField.Result getAllMultiLanguageField(GetAllMultiLanguageField.Arg arg, ServiceContext context) {
//        //从配置中获取数据
//        // TODO: 895修改 点击某个对象的时候，才下发描述
//        Map<String, List<String>> supportMultiLangObjField = AppFrameworkConfig.getSupportMultiLangObjField();
//        if (CollectionUtils.empty(supportMultiLangObjField)) {
//            return GetAllMultiLanguageField.Result.builder().multiLangInfoList(Lists.newArrayList()).build();
//        }
//
//        Map<String, IObjectDescribe> objectDescribeMap = Maps.newHashMap();
//        //从配置中支持多语的字段并拿到对象相关描述
//        getSupportDataLangByConfig(objectDescribeMap, supportMultiLangObjField.keySet(), context, BooleanUtils.isFalse(arg.getIncludeEnable()));
//        if (CollectionUtils.empty(objectDescribeMap)) {
//            return GetAllMultiLanguageField.Result.builder().multiLangInfoList(Lists.newArrayList()).build();
//        }
//        List<GetAllMultiLanguageField.MultiLangInfo> multiLangInfos = buildAllMultiLanguageResult(objectDescribeMap);
//        return GetAllMultiLanguageField.Result.builder().multiLangInfoList(multiLangInfos).build();
//    }

//    private List<GetAllMultiLanguageField.MultiLangInfo> buildAllMultiLanguageResult(Map<String, IObjectDescribe> objectDescribeMap) {
//        List<GetAllMultiLanguageField.MultiLangInfo> multiLangInfos = Lists.newArrayList();
//
//        Set<Map.Entry<String, IObjectDescribe>> describeEntrySet = objectDescribeMap.entrySet();
//        for (Map.Entry<String, IObjectDescribe> describeEntry : describeEntrySet) {
//            IObjectDescribe describe = describeEntry.getValue();
//            List<IFieldDescribe> fieldDescribes = filterFieldByConfig(describeEntry.getValue());
//            GetAllMultiLanguageField.MultiLangInfo multiLangInfo = buildResult(fieldDescribes, describe);
//            if (Objects.nonNull(multiLangInfo)) {
//                multiLangInfos.add(multiLangInfo);
//            }
//        }
//        return multiLangInfos;
//    }


//    private List<IFieldDescribe> filterFieldByConfig(IObjectDescribe objectDescribe) {
//        Map<String, List<String>> configFieldInfoMap = AppFrameworkConfig.getSupportMultiLangObjField();
//        List<String> configFieldInfo = configFieldInfoMap.get(objectDescribe.getApiName());
//        List<IFieldDescribe> fieldDescribeList = ObjectDescribeExt.of(objectDescribe).getFieldDescribesSilently().stream()
//                .filter(x -> FieldDescribeExt.of(x).isTextType())
//                .collect(Collectors.toList());
//        if (CollectionUtils.empty(configFieldInfo)) {
//            if (configFieldInfoMap.containsKey(ALL)) {
//                configFieldInfo = configFieldInfoMap.get(ALL);
//            }
//            if (configFieldInfoMap.containsKey(UDOBJ) && ObjectDescribeExt.of(objectDescribe).isCustomObject()) {
//                configFieldInfo = configFieldInfoMap.get(UDOBJ);
//            }
//        }
//        if (CollectionUtils.empty(configFieldInfo)) {
//            return Lists.newArrayList();
//        }
//        if (configFieldInfo.contains("*")) {
//            return fieldDescribeList.stream()
//                    .filter(x -> FieldDescribeExt.of(x).isCustomField()
//                            || AppFrameworkConfig.getDataLangSupportPreFieldApiName().contains(x.getApiName()))
//                    .collect(Collectors.toList());
//        }
//        List<String> finalConfigFieldInfo = configFieldInfo;
//        return fieldDescribeList.stream().filter(x -> finalConfigFieldInfo.contains(x.getApiName())).collect(Collectors.toList());
//
//    }

    @ServiceMethod("getEnableMultiLanguageField")
    public GetEnableMultiLanguageField.Result getEnableMultiLanguageField(GetEnableMultiLanguageField.Arg arg, ServiceContext context) {
        if (StringUtils.isNotBlank(arg.getDescribeApiName())) {
            IObjectDescribe objectDescribe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
            return GetEnableMultiLanguageField.Result.builder().multiLanguageInfo(buildEnableMultiLanguageFieldResult(Lists.newArrayList(objectDescribe))).build();
        }
        return GetEnableMultiLanguageField.Result.builder().multiLanguageInfo(getEnableFieldListAndFormat(context))
                .limitFieldCount(AppFrameworkConfig.getEnableMultiLangFieldCountByDescribeApiName(ALL)).build();
    }

    private List<GetEnableMultiLanguageField.MultiLanguageInfo> buildEnableMultiLanguageFieldResult(List<IObjectDescribe> iObjectDescribes) {
        List<GetEnableMultiLanguageField.MultiLanguageInfo> multiLanguageInfos = Lists.newArrayList();
        for (IObjectDescribe describe : iObjectDescribes) {
            Set<IFieldDescribe> fieldDescribeList = ObjectDescribeExt.of(describe).getEnableMultiLangField();
            List<GetEnableMultiLanguageField.FieldInfo> fieldInfoList = fieldDescribeList.stream()
                    .map(x -> GetEnableMultiLanguageField.FieldInfo.builder()
                            .fieldApiName(x.getApiName())
                            .label(x.getLabel())
                            .build())
                    .collect(Collectors.toList());

            multiLanguageInfos.add(GetEnableMultiLanguageField.MultiLanguageInfo.builder()
                    .displayName(describe.getDisplayName())
                    .describeApiName(describe.getApiName())
                    .type(ObjectDescribeExt.of(describe).isBigObject() ? "bigObject" : describe.getDefineType())
                    .fieldInfoList(fieldInfoList)
                    .build());
        }
        return multiLanguageInfos;
    }

    private List<GetEnableMultiLanguageField.MultiLanguageInfo> getEnableFieldListAndFormat(ServiceContext context) {
        List<IObjectDescribe> dataLanguageInfoByTenant = dataLanguageService.getDataLanguageInfoByTenant(context);

        //部门，人员对象的多语信息不在数据多语设置面板漏出
        dataLanguageInfoByTenant.removeIf(x -> Lists.newArrayList(PERSONNEL_OBJ_API_NAME, DEPARTMENT_OBJ_API_NAME).contains(x.getApiName()));
        //不显示变更单对象
        dataLanguageInfoByTenant.removeIf(x -> ObjectDescribeExt.of(x).isChangeOrderObject());
        List<IObjectDescribe> result = sortObjectListByType(dataLanguageInfoByTenant);
        return buildEnableMultiLanguageFieldResult(result);
    }

    /**
     * 返回顺序 按 预设、自定义、大对象排序
     *
     * @param describeList
     * @return
     */
    private List<IObjectDescribe> sortObjectListByType(List<IObjectDescribe> describeList) {
        List<IObjectDescribe> bigDescribeList = Lists.newArrayList();
        List<IObjectDescribe> preDescribeList = Lists.newArrayList();
        List<IObjectDescribe> customerDescribeList = Lists.newArrayList();
        List<IObjectDescribe> resultList = Lists.newArrayList();
        for (IObjectDescribe objectDescribe : describeList) {
            if (ObjectDescribeExt.of(objectDescribe).isBigObject()) {
                bigDescribeList.add(objectDescribe);
            } else if (ObjectDescribeExt.of(objectDescribe).isCustomObject()) {
                customerDescribeList.add(objectDescribe);
            } else if (!ObjectDescribeExt.of(objectDescribe).isCustomObject()) {
                preDescribeList.add(objectDescribe);
            }
        }
        resultList.addAll(preDescribeList);
        resultList.addAll(customerDescribeList);
        resultList.addAll(bigDescribeList);
        return resultList;
    }

//
//    private void getSupportDataLangByConfig(Map<String, IObjectDescribe> objectDescribeMap, Set<String> supportDescribeApiName, ServiceContext context, boolean isRemoveEnable) {
//        //如果配置中有ALL则代表获取所有对象所有文本字段
//        if (supportDescribeApiName.contains(ALL) && AppFrameworkConfig.objectMultiLangGray(context.getTenantId(), ALL)) {
//            List<IObjectDescribe> describeList = describeLogicService.findObjectsByTenantId(context.getUser(),
//                    null, true, false,
//                    false, false, OBJECT_MANAGEMENT, ObjectType.ALL);
//            objectDescribeMap.putAll(describeList.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, it -> it, (x, y) -> x)));
//            return;
//        }
//        //包含udobj则代表获取所有自定义对象的所有文本字段
//        if (supportDescribeApiName.contains(UDOBJ) && AppFrameworkConfig.objectMultiLangGray(context.getTenantId(), UDOBJ)) {
//            List<IObjectDescribe> describeList = describeLogicService.findObjectsByTenantId(context.getUser(),
//                    DEFINE_TYPE_CUSTOM, true, false,
//                    false, false, OBJECT_MANAGEMENT, ObjectType.ALL);
//            describeList.removeIf(x -> supportDescribeApiName.contains(x.getApiName()));
//            objectDescribeMap.putAll(describeList.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, it -> it, (x, y) -> x)));
//        }
//        //补上配置中配的预置字段信息
//        objectDescribeMap.putAll(describeLogicService.findObjectsWithoutCopy(context.getTenantId(),
//                supportDescribeApiName.stream().filter(x -> !(ALL.equals(x) || UDOBJ.equals(x)))
//                        .filter(x -> AppFrameworkConfig.objectMultiLangGray(context.getTenantId(), x))
//                        .collect(Collectors.toList())));
//        if (isRemoveEnable) {
//            List<String> enableDescribeList = getEnableFieldListAndFormat(context).stream().map(GetEnableMultiLanguageField.MultiLanguageInfo::getDescribeApiName).collect(Collectors.toList());
//            enableDescribeList.forEach(objectDescribeMap::remove);
//        }
//    }

//    private GetAllMultiLanguageField.MultiLangInfo buildResult(List<IFieldDescribe> fieldDescribeList, IObjectDescribe describe) {
//        //只要该对象下有一个字段启用，则该对象都不下发
//        List<GetAllMultiLanguageField.FieldInfo> fieldList = fieldDescribeList.stream().map(x -> GetAllMultiLanguageField.FieldInfo.builder()
//                .fieldApiName(x.getApiName())
//                .label(x.getLabel())
//                .build()).collect(Collectors.toList());
//        if (CollectionUtils.empty(fieldList)) {
//            return null;
//        }
//        return GetAllMultiLanguageField.MultiLangInfo.builder()
//                .describeApiName(describe.getApiName())
//                .displayName(describe.getDisplayName())
//                .fieldInfoList(fieldList)
//                .build();
//    }

    private Map<String, Set<String>> validateFieldType(String tenantId, List<ChangeMultiLanguageField.MultiLanguageField> enableMultiLanguageInfoList, Map<String, IObjectDescribe> objectDescribeMap) {
        Map<String, Set<String>> unSupportFieldMap = Maps.newHashMap();
        for (ChangeMultiLanguageField.MultiLanguageField multiLanguageField : enableMultiLanguageInfoList) {
            IObjectDescribe describe = objectDescribeMap.get(multiLanguageField.getDescribeApiName());
            Set<String> unSupportField = Sets.newHashSet();
            List<String> enableField = multiLanguageField.getEnableField();
            if (Objects.isNull(describe) || !AppFrameworkConfig.objectMultiLangGray(tenantId, describe.getApiName())) {
                unSupportField.addAll(enableField);
                continue;
            }
            enableField.removeIf(x -> !describe.containsField(x));
            for (String apiName : enableField) {
                IFieldDescribe fieldDescribe = ObjectDescribeExt.of(describe).getFieldDescribe(apiName);
                if (!FieldDescribeExt.of(fieldDescribe).isTextType()) {
                    unSupportField.add(apiName);
                }
            }
            if (CollectionUtils.notEmpty(unSupportField)) {
                unSupportFieldMap.put(describe.getApiName(), unSupportField);
            }
        }
        enableMultiLanguageInfoList.removeIf(x -> unSupportFieldMap.containsKey(x.getDescribeApiName()));
        return unSupportFieldMap;
    }


}
