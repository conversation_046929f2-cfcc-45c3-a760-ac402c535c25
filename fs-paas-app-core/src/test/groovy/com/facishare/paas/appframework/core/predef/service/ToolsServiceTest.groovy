package com.facishare.paas.appframework.core.predef.service

import com.facishare.organization.api.model.employee.EmployeeDto
import com.facishare.organization.api.model.type.EmployeeEntityStatus
import com.facishare.paas.appframework.common.service.EmployeeService
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.dto.tools.FindPersonByMobile
import com.facishare.paas.appframework.privilege.UserRoleInfoService
import com.facishare.paas.appframework.privilege.dto.GetUserRoleInfo
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

class ToolsServiceTest extends Specification {
    ToolsService toolsService
    EmployeeService employeeService
    UserRoleInfoService userRoleInfoService

    def setup() {
        employeeService = Mock(EmployeeService)
        userRoleInfoService = Mock(UserRoleInfoService)
        toolsService = new ToolsService(
                employeeService: employeeService,
                userRoleInfoService: userRoleInfoService
        )
    }

    @Unroll
    def "findPersonByMobile参数校验测试 - #testCase"() {
        given: "准备测试参数"
        def arg = new FindPersonByMobile.Arg (
                ei: ei,
                phone: phone,
                includeMainRole: false,
                includeMainRoleName: false
        )
        def context = Mock(ServiceContext)

        when: "调用方法"
        toolsService.findPersonByMobile(context, arg)

        then: "验证异常"
        def e = thrown(ValidateException)
        e.message == "参数错误"

        where:
        testCase      | ei     | phone
        "企业ID为空"    | null   | "13800138000"
        "企业ID为空白"   | " "    | "13800138000"
        "手机号为空"     | "1"    | null
        "手机号为空白"    | "1"    | " "
    }

    def "findPersonByMobile查询不到员工测试"() {
        given: "准备测试参数"
        def arg = new FindPersonByMobile.Arg(
                ei: "1",
                phone: "13800138000",
                includeMainRole: false,
                includeMainRoleName: false
        )
        def context = Mock(ServiceContext)

        when: "调用方法"
        def result = toolsService.findPersonByMobile(context, arg)

        then: "验证结果"
        1 * employeeService.getEmployeeByMobile("1", "13800138000") >> null
        result.userId == null
        result.mainRoleCode == null
        result.mainRoleName == null
    }

    def "findPersonByMobile只查询员工信息测试"() {
        given: "准备测试参数"
        def arg = new FindPersonByMobile.Arg(
                ei: "1",
                phone: "13800138000",
                includeMainRole: false,
                includeMainRoleName: false
        )
        def context = Mock(ServiceContext)
        def employeeDto = new EmployeeDto(
                employeeId: 123,
                name: "测试用户",
                status: EmployeeEntityStatus.NORMAL
        )

        when: "调用方法"
        def result = toolsService.findPersonByMobile(context, arg)

        then: "验证结果"
        1 * employeeService.getEmployeeByMobile("1", "13800138000") >> employeeDto
        result.userId == "123"
        result.mainRoleCode == null
        result.mainRoleName == null
    }

    def "findPersonByMobile包含主角色信息测试"() {
        given: "准备测试参数"
        def arg = new FindPersonByMobile.Arg(
                ei: "1",
                phone: "13800138000",
                includeMainRole: true,
                includeMainRoleName: false
        )
        def context = Mock(ServiceContext)
        def employeeDto = new EmployeeDto(
                employeeId: 123,
                name: "测试用户",
                status: EmployeeEntityStatus.NORMAL
        )

        when: "调用方法"
        def result = toolsService.findPersonByMobile(context, arg)

        then: "验证结果"
        1 * employeeService.getEmployeeByMobile("1", "13800138000") >> employeeDto
        1 * userRoleInfoService.getDefaultRoleCode(_ as User) >> Optional.of("ROLE_CODE")
        result.userId == "123"
        result.mainRoleCode == "ROLE_CODE"
        result.mainRoleName == null
    }

    def "findPersonByMobile包含主角色名称测试"() {
        given: "准备测试参数"
        def arg = new FindPersonByMobile.Arg(
                ei: "1",
                phone: "13800138000",
                includeMainRole: true,
                includeMainRoleName: true
        )
        def context = Mock(ServiceContext)
        def employeeDto = new EmployeeDto(
                employeeId: 123,
                name: "测试用户",
                status: EmployeeEntityStatus.NORMAL
        )
        def roleInfo = new GetUserRoleInfo.RoleInfo(
                roleCode: "ROLE_CODE",
                roleName: "测试角色"
        )

        when: "调用方法"
        def result = toolsService.findPersonByMobile(context, arg)

        then: "验证结果"
        1 * employeeService.getEmployeeByMobile("1", "13800138000") >> employeeDto
        1 * userRoleInfoService.getDefaultRoleCode(_ as User) >> Optional.of("ROLE_CODE")
        1 * userRoleInfoService.queryRoleInfoByRoleCode(_ as User, Lists.newArrayList("ROLE_CODE")) >> Lists.newArrayList(roleInfo)
        result.userId == "123"
        result.mainRoleCode == "ROLE_CODE"
        result.mainRoleName == "测试角色"
    }
} 