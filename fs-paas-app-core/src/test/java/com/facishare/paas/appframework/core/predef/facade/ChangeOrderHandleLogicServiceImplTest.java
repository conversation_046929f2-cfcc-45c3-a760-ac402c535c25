package com.facishare.paas.appframework.core.predef.facade;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.facade.dto.CreateChangeOrder;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectMappingService;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderConfig;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderLogicService;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderRuleLogicService;
import com.facishare.paas.appframework.metadata.changeorder.OriginalAndChangeDescribes;
import com.facishare.paas.appframework.metadata.changeorder.MasterAndDetailDescribes;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.repository.model.MtChangeOrderRule;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class ChangeOrderHandleLogicServiceImplTest {

    @InjectMocks
    private ChangeOrderHandleLogicServiceImpl changeOrderHandleLogicService;

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private ChangeOrderLogicService changeOrderLogicService;

    @Mock
    private ChangeOrderRuleLogicService changeOrderRuleLogicService;

    @Mock
    private ExpressionCalculateLogicService expressionCalculateLogicService;

    @Mock
    private ObjectMappingService objectMappingService;

    @Mock
    private User user;

    @Mock
    private IObjectDescribe originalDescribe;

    @Mock
    private IObjectDescribe changeDescribe;

    @Mock
    private IObjectData masterData;

    @Mock
    private IObjectData dbMasterObjectData;

    @Mock
    private MtChangeOrderRule changeOrderRule;

    @Mock
    private OriginalAndChangeDescribes describes;

    @Mock
    private MasterAndDetailDescribes originalDescribes;

    @Mock
    private MasterAndDetailDescribes changeOrderDescribes;

    @BeforeEach
    void setUp() {
        lenient().when(user.getTenantId()).thenReturn("74255");
        lenient().when(originalDescribe.getApiName()).thenReturn("originalApiName");
        lenient().when(changeDescribe.getApiName()).thenReturn("changeApiName");
        lenient().when(masterData.getId()).thenReturn("masterDataId");
        lenient().when(dbMasterObjectData.getId()).thenReturn("dbMasterDataId");
        
        // 配置describes的Mock行为
        lenient().when(describes.getOriginalDescribes()).thenReturn(originalDescribes);
        lenient().when(describes.getChangeOrderDescribes()).thenReturn(changeOrderDescribes);
        
        // 配置originalDescribes的Mock行为
        Map<String, IObjectDescribe> originalDescribesMap = Maps.newHashMap();
        originalDescribesMap.put("originalApiName", originalDescribe);
        lenient().when(originalDescribes.getObjectDescribes()).thenReturn(originalDescribesMap);
        
        // 配置changeOrderDescribes的Mock行为
        lenient().when(changeOrderDescribes.getObjectDescribe()).thenReturn(changeDescribe);
        lenient().when(changeOrderDescribes.getDetailDescribes()).thenReturn(Maps.newHashMap());
        
        // 配置changeOrderRule的Mock行为
        lenient().when(changeOrderRule.getApiName()).thenReturn("testChangeOrderRule");
        lenient().when(changeOrderRule.toObjectFieldMapper(any())).thenReturn(mock(MtChangeOrderRule.ObjectFieldMapper.class));
        lenient().when(changeOrderRule.getFieldMapping()).thenReturn(Lists.newArrayList());
        
        // 配置ObjectMappingService的Mock行为
        lenient().when(objectMappingService.mappingData(any(User.class), any(), any(IObjectData.class)))
                .thenReturn(mock(IObjectData.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doCreateChangeOrder方法正常创建变更单
     */
    @Test
    @DisplayName("正常场景 - 创建变更单成功")
    void testDoCreateChangeOrderSuccess() {
        // 准备测试数据
        CreateChangeOrder.Arg arg = new CreateChangeOrder.Arg();
        arg.setDescribe(originalDescribe);
        arg.setMasterData(masterData);
        
        // 配置arg对象的必要数据
        IObjectData originalData = mock(IObjectData.class);
        when(originalData.getId()).thenReturn("originalDataId");
        arg.setOriginalData(originalData);
        arg.setOriginalDetails(Maps.newHashMap());
        arg.setDetailToAddMap(Maps.newHashMap());
        arg.setDetailToUpdateMap(Maps.newHashMap());
        arg.setDetailToDeleteMap(Maps.newHashMap());

        BaseObjectSaveAction.Result actionResult = mock(BaseObjectSaveAction.Result.class);
        ObjectDataDocument objectDataDocument = mock(ObjectDataDocument.class);
        IObjectData resultObjectData = new ObjectData();
        resultObjectData.setId("newChangeOrderId");

        // 配置Mock行为
        try (MockedStatic<ChangeOrderConfig> changeOrderConfigMock = mockStatic(ChangeOrderConfig.class);
             MockedStatic<RequestContextManager> requestContextManagerMock = mockStatic(RequestContextManager.class)) {

            changeOrderConfigMock.when(() -> ChangeOrderConfig.changeOrderDescribeGray(anyString(), anyString()))
                    .thenReturn(true);

            when(changeOrderLogicService.findChangeOrderDescribeByOriginalApiName(user, "originalApiName"))
                    .thenReturn(Optional.of(changeDescribe));

            when(serviceFacade.findObjectData(anyString(), anyString(), any(IObjectDescribe.class)))
                    .thenReturn(dbMasterObjectData);

            when(changeOrderRuleLogicService.findByDescribeApiName(user, "originalApiName"))
                    .thenReturn(Lists.newArrayList(changeOrderRule));

            when(changeOrderLogicService.findDescribesByOriginalApiName(user, "originalApiName"))
                    .thenReturn(describes);

            when(serviceFacade.triggerRemoteAction(any(ActionContext.class), any(BaseObjectSaveAction.Arg.class), eq(BaseObjectSaveAction.Result.class)))
                    .thenReturn(actionResult);

            when(actionResult.getObjectData()).thenReturn(objectDataDocument);
            when(objectDataDocument.toObjectData()).thenReturn(resultObjectData);

            requestContextManagerMock.when(RequestContextManager::getContext)
                    .thenReturn(mock(com.facishare.paas.appframework.core.model.RequestContext.class));

            // 执行被测试方法
            CreateChangeOrder.Result result = changeOrderHandleLogicService.doCreateChangeOrder(user, arg);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertEquals("changeApiName", result.getChangeOrderApiName());
            assertEquals("newChangeOrderId", result.getChangeOrderDataId());
        }

        // 验证Mock交互
        verify(changeOrderLogicService).findChangeOrderDescribeByOriginalApiName(user, "originalApiName");
        verify(serviceFacade).findObjectData(anyString(), anyString(), any(IObjectDescribe.class));
        verify(serviceFacade).triggerRemoteAction(any(ActionContext.class), any(BaseObjectSaveAction.Arg.class), eq(BaseObjectSaveAction.Result.class));
        verify(serviceFacade).updateWithMap(eq(user), eq(dbMasterObjectData), any(Map.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doCreateChangeOrder方法在不支持变更单时抛出ValidateException异常
     */
    @Test
    @DisplayName("异常场景 - 不支持创建变更单")
    void testDoCreateChangeOrderThrowsValidateExceptionWhenNotSupported() {
        // 准备测试数据
        CreateChangeOrder.Arg arg = new CreateChangeOrder.Arg();
        arg.setDescribe(originalDescribe);
        arg.setMasterData(masterData);

        // 配置Mock行为
        try (MockedStatic<ChangeOrderConfig> changeOrderConfigMock = mockStatic(ChangeOrderConfig.class);
             MockedStatic<I18NExt> i18nExtMock = mockStatic(I18NExt.class)) {

            changeOrderConfigMock.when(() -> ChangeOrderConfig.changeOrderDescribeGray(anyString(), anyString()))
                    .thenReturn(false);

            i18nExtMock.when(() -> I18NExt.text(I18NKey.UNSUPPORTED_CREATE_CHANGE_ORDER))
                    .thenReturn("不支持创建变更单");

            // 执行并验证异常
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                changeOrderHandleLogicService.doCreateChangeOrder(user, arg);
            });

            // 验证异常信息
            assertEquals("不支持创建变更单", exception.getMessage());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doCreateChangeOrder方法在变更单对象不存在时抛出ValidateException异常
     */
    @Test
    @DisplayName("异常场景 - 变更单对象不存在")
    void testDoCreateChangeOrderThrowsValidateExceptionWhenChangeOrderNotExist() {
        // 准备测试数据
        CreateChangeOrder.Arg arg = new CreateChangeOrder.Arg();
        arg.setDescribe(originalDescribe);
        arg.setMasterData(masterData);

        // 配置Mock行为
        try (MockedStatic<ChangeOrderConfig> changeOrderConfigMock = mockStatic(ChangeOrderConfig.class);
             MockedStatic<I18NExt> i18nExtMock = mockStatic(I18NExt.class)) {

            changeOrderConfigMock.when(() -> ChangeOrderConfig.changeOrderDescribeGray(anyString(), anyString()))
                    .thenReturn(true);

            when(changeOrderLogicService.findChangeOrderDescribeByOriginalApiName(user, "originalApiName"))
                    .thenReturn(Optional.empty());

            i18nExtMock.when(() -> I18NExt.text(I18NKey.CHANGE_ORDER_OBJECT_NOT_EXIST_OR_DELETED))
                    .thenReturn("变更单对象不存在或已删除");

            // 执行并验证异常
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                changeOrderHandleLogicService.doCreateChangeOrder(user, arg);
            });

            // 验证异常信息
            assertEquals("变更单对象不存在或已删除", exception.getMessage());
        }

        // 验证Mock交互
        verify(changeOrderLogicService).findChangeOrderDescribeByOriginalApiName(user, "originalApiName");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doModifyChangeOrder方法正常修改变更单
     */
    @Test
    @DisplayName("正常场景 - 修改变更单成功")
    void testDoModifyChangeOrderSuccess() {
        // 准备测试数据
        CreateChangeOrder.Arg arg = new CreateChangeOrder.Arg();
        arg.setDescribe(originalDescribe);
        arg.setMasterData(masterData);

        // 配置arg对象的必要数据
        IObjectData originalData = mock(IObjectData.class);
        when(originalData.getId()).thenReturn("originalDataId");
        arg.setOriginalData(originalData);
        arg.setOriginalDetails(Maps.newHashMap());
        arg.setDetailToAddMap(Maps.newHashMap());
        arg.setDetailToUpdateMap(Maps.newHashMap());
        arg.setDetailToDeleteMap(Maps.newHashMap());

        when(masterData.get(ObjectDataExt.CHANGED_DATA_ID, String.class)).thenReturn("changedDataId");

        BaseObjectSaveAction.Result actionResult = mock(BaseObjectSaveAction.Result.class);
        ObjectDataDocument objectDataDocument = mock(ObjectDataDocument.class);
        IObjectData resultObjectData = new ObjectData();
        resultObjectData.setId("modifiedChangeOrderId");

        // 配置Mock行为
        try (MockedStatic<ChangeOrderConfig> changeOrderConfigMock = mockStatic(ChangeOrderConfig.class);
             MockedStatic<RequestContextManager> requestContextManagerMock = mockStatic(RequestContextManager.class)) {

            changeOrderConfigMock.when(() -> ChangeOrderConfig.changeOrderDescribeGray(anyString(), anyString()))
                    .thenReturn(true);

            when(changeOrderLogicService.findChangeOrderDescribeByOriginalApiName(user, "originalApiName"))
                    .thenReturn(Optional.of(changeDescribe));

            when(serviceFacade.findObjectData(anyString(), anyString(), any(IObjectDescribe.class)))
                    .thenReturn(dbMasterObjectData);

            when(changeOrderRuleLogicService.findByDescribeApiName(user, "originalApiName"))
                    .thenReturn(Lists.newArrayList(changeOrderRule));

            when(changeOrderLogicService.findDescribesByOriginalApiName(user, "originalApiName"))
                    .thenReturn(describes);

            when(serviceFacade.triggerAction(any(ActionContext.class), any(BaseObjectSaveAction.Arg.class), eq(BaseObjectSaveAction.Result.class)))
                    .thenReturn(actionResult);

            when(actionResult.getObjectData()).thenReturn(objectDataDocument);
            when(objectDataDocument.toObjectData()).thenReturn(resultObjectData);

            requestContextManagerMock.when(RequestContextManager::getContext)
                    .thenReturn(mock(com.facishare.paas.appframework.core.model.RequestContext.class));

            // 执行被测试方法
            CreateChangeOrder.Result result = changeOrderHandleLogicService.doModifyChangeOrder(user, arg);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertEquals("changeApiName", result.getChangeOrderApiName());
            assertEquals("modifiedChangeOrderId", result.getChangeOrderDataId());
        }

        // 验证Mock交互
        verify(changeOrderLogicService).findChangeOrderDescribeByOriginalApiName(user, "originalApiName");
        verify(serviceFacade).findObjectData(anyString(), anyString(), any(IObjectDescribe.class));
        verify(serviceFacade).triggerAction(any(ActionContext.class), any(BaseObjectSaveAction.Arg.class), eq(BaseObjectSaveAction.Result.class));
        verify(serviceFacade).updateWithMap(eq(user), eq(dbMasterObjectData), any(Map.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doModifyChangeOrder方法在变更数据ID为空时抛出ValidateException异常
     */
    @Test
    @DisplayName("异常场景 - 变更数据ID为空")
    void testDoModifyChangeOrderThrowsValidateExceptionWhenChangedDataIdIsEmpty() {
        // 准备测试数据
        CreateChangeOrder.Arg arg = new CreateChangeOrder.Arg();
        arg.setDescribe(originalDescribe);
        arg.setMasterData(masterData);

        when(masterData.get(ObjectDataExt.CHANGED_DATA_ID, String.class)).thenReturn("");

        // 配置Mock行为
        try (MockedStatic<ChangeOrderConfig> changeOrderConfigMock = mockStatic(ChangeOrderConfig.class);
             MockedStatic<I18NExt> i18nExtMock = mockStatic(I18NExt.class)) {

            changeOrderConfigMock.when(() -> ChangeOrderConfig.changeOrderDescribeGray(anyString(), anyString()))
                    .thenReturn(true);

            i18nExtMock.when(() -> I18NExt.text(I18NKey.CHANGE_ORDER_MASTER_DATA_NOT_EXIST_OR_DELETED))
                    .thenReturn("变更单主数据不存在或已删除");

            // 执行并验证异常
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                changeOrderHandleLogicService.doModifyChangeOrder(user, arg);
            });

            // 验证异常信息
            assertEquals("变更单主数据不存在或已删除", exception.getMessage());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateByChangeStatus方法正常验证变更状态
     */
    @Test
    @DisplayName("正常场景 - 验证变更状态成功")
    void testValidateByChangeStatusSuccess() {
        // 准备测试数据
        IObjectData objectData = new ObjectData();

        // 配置Mock行为
        try (MockedStatic<ChangeOrderConfig> changeOrderConfigMock = mockStatic(ChangeOrderConfig.class)) {
            changeOrderConfigMock.when(() -> ChangeOrderConfig.changeOrderDescribeGray(anyString(), anyString()))
                    .thenReturn(true);

            // 执行被测试方法
            assertDoesNotThrow(() -> {
                changeOrderHandleLogicService.validateByChangeStatus(user, originalDescribe, objectData);
            });
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findByDescribeApiName方法返回变更规则列表
     */
    @Test
    @DisplayName("正常场景 - 根据对象API名称查找变更规则")
    void testFindByDescribeApiNameSuccess() {
        // 准备测试数据
        List<MtChangeOrderRule> expectedRules = Lists.newArrayList(changeOrderRule);

        // 配置Mock行为
        when(changeOrderRuleLogicService.findByDescribeApiName(user, "originalApiName"))
                .thenReturn(expectedRules);

        // 执行被测试方法
        List<MtChangeOrderRule> result = changeOrderHandleLogicService.findByDescribeApiName(user, "originalApiName");

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(changeOrderRule, result.get(0));

        // 验证Mock交互
        verify(changeOrderRuleLogicService).findByDescribeApiName(user, "originalApiName");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findAndMergeObjectDataWithOriginalData方法正常合并数据
     */
    @Test
    @DisplayName("正常场景 - 查找并合并对象数据与原始数据")
    void testFindAndMergeObjectDataWithOriginalDataSuccess() {
        // 准备测试数据
        List<IObjectData> dataList = Lists.newArrayList(masterData);
        List<IObjectData> expectedResult = Lists.newArrayList(masterData);

        // 配置Mock行为
        when(changeOrderLogicService.findAndMergeObjectDataWithOriginalData(user, originalDescribe, dataList))
                .thenReturn(expectedResult);

        // 执行被测试方法
        List<IObjectData> result = changeOrderHandleLogicService.findAndMergeObjectDataWithOriginalData(user, originalDescribe, dataList);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(masterData, result.get(0));

        // 验证Mock交互
        verify(changeOrderLogicService).findAndMergeObjectDataWithOriginalData(user, originalDescribe, dataList);
    }
} 