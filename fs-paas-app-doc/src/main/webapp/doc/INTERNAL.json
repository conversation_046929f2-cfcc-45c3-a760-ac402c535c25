{"swagger": "2.0", "info": {"title": "AppFrameWork REST API", "description": "PaaS 应用框架REST API", "version": "1.0"}, "host": "************:8234", "schemes": ["http"], "basePath": "/v1/inner/rest/internal", "produces": ["application/json"], "commonParams": {"includeDescribe": {"name": "includeDescribe", "in": "query", "description": "返回结果是否包含对象描述", "required": false, "type": "boolean", "default": false}, "includeLookup": {"name": "includeLookup", "in": "query", "description": "返回结果是否包含Lookup字段值", "required": false, "type": "boolean", "default": false}, "userInfo": {"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, "eid": {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, "peerHost": {"name": "x-fs-peer-host", "in": "header", "description": "调用方Host地址", "required": false, "type": "string"}, "peerName": {"name": "x-fs-peer-name", "in": "header", "description": "调用方Name，例如FBS,OpenAPI,CRM等", "required": false, "type": "string"}, "apiName": {"name": "descAPIName", "in": "path", "required": true, "type": "string", "description": "元数据对象描述的API name", "default": "customer"}, "dataId": {"name": "dataId", "in": "path", "required": true, "type": "string", "description": "数据ID", "default": "1"}}, "paths": {"/{descAPIName}/{dataId}": {"get": {"tags": ["Internal API"], "summary": "find record name", "description": "根据自定义对象id获取自定义对象", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"$ref": "#/commonParams/apiName"}, {"$ref": "#/commonParams/dataId"}, {"$ref": "#/commonParams/includeDescribe"}, {"$ref": "#/commonParams/includeLookup"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ObjectData"}}}}}, "/{descAPIName}/find_record_name": {"post": {"tags": ["Internal API"], "summary": "find object data by ID", "description": "根据自定义对象id获取自定义对象", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"$ref": "#/commonParams/apiName"}, {"$ref": "#/commonParams/includeDescribe"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/IDList"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ObjectDataList"}}}}}, "/{descAPIName}/find_by_ids": {"post": {"tags": ["Internal API"], "summary": "find object data by ID list", "description": "根据自定义对象id列表获取自定义对象", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"$ref": "#/commonParams/apiName"}, {"$ref": "#/commonParams/includeDescribe"}, {"$ref": "#/commonParams/includeLookup"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/IDList"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ObjectDataList"}}}}}, "/object_describe/{apiName}": {"get": {"tags": ["Internal API"], "summary": "find object describe by tenantId and apiName", "description": "根据企业ID和apiName查询对象描述", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"name": "apiName", "in": "path", "required": true, "type": "string", "description": "对象的API name"}, {"name": "include_ref_describe", "in": "query", "required": false, "type": "string", "default": "false", "describe": "是否包含关联对象的describe信息"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/FindDescribeByTenantAndApiResult"}}}}}, "/object_describe/check_cycle": {"post": {"tags": ["Internal API"], "summary": "check cycle", "description": "检查关联是否成环", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CheckCycleArg"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CheckCycleResult"}}}}}}, "definitions": {"FindDataByIdResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"describe": {"type": "object"}, "object_data": {"type": "object"}}}}}, "ObjectData": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"object_data": {"type": "object"}}}}}, "ObjectDataList": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"object_data": {"type": "array", "items": {"type": "object"}}}}}}, "IDList": {"type": "array", "items": {"type": "string"}}, "FindDescribeByTenantAndApiResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"describe": {"type": "object"}, "refObjectDescribeList": {"type": "array", "items": {"type": "object"}}}}}}, "CheckCycleArg": {"type": "object", "properties": {"sourceApiName": {"type": "string"}, "targetApiName": {"type": "string"}}}, "CheckCycleResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"success": {"type": "boolean"}}}}}}}