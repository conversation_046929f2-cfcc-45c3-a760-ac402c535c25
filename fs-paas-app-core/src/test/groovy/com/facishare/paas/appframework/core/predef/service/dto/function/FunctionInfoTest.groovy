package com.facishare.paas.appframework.core.predef.service.dto.function

import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.SerializerManager
import com.facishare.paas.appframework.core.model.SerializerManagerImpl
import com.google.common.collect.Lists
import com.google.gson.Gson
import spock.lang.Specification

/**
 * Created by l<PERSON><PERSON><PERSON><PERSON> on 2018/2/5.
 */
class FunctionInfoTest extends Specification {

    static serializer = new SerializerManagerImpl().getSerializer(RequestContext.ContentType.FULL_JSON)
    static Gson gson = new Gson()

    def "test serialize deserialize"() {
        when:
        def ret = serializer.decode(FunctionInfo.class, json)
        then:
        println json
        println ret
        ret == result
        where:
        json                              || result
        serializer.encode(functionInfo()) || functionInfo()
    }

    def functionInfo() {
        return FunctionInfo.builder()
                .bindingObjectApiName("AccountObj")
                .apiName("function_xxx__c")
                .functionName("func1")
                .returnType("String")
                .parameters(Lists.newArrayList(FunctionInfo.Parameter.of("""{"name": "a", "type": "String"}""")))
                .version(1)
                .application("CRM")
                .nameSpace("FUNCTIONS").build()

    }


    def "test gson serialize deserialize"() {
        when:
        def ret = gson.fromJson(json, FunctionInfo.class)
        then:
        println json
        println ret
        ret == result
        where:
        json                        || result
        gson.toJson(functionInfo()) || functionInfo()
    }


}
