package com.facishare.paas.appframework.metadata.reference;

import com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.model.ref.RefMessage.Refs;
import com.facishare.paas.appframework.metadata.reference.WhereUsedDisplayConf.ConfItem;
import com.facishare.paas.appframework.metadata.reference.WhereUsedDisplayConf.I18nConf;
import com.facishare.paas.appframework.metadata.reference.WhereUsedDisplayConf.MenuConf;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.PostConstruct;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class RefFieldServiceImpl implements RefFieldService {

    private final AppDefaultRocketMQProducer recordReferenceProducer;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("entity-reference-display-where-used", iConfig -> {
            String content = new String(iConfig.getContent(), Charset.defaultCharset());
            WhereUsedDisplayConf.Conf conf = JacksonUtils.fromJson(content, WhereUsedDisplayConf.Conf.class);
            if (Objects.nonNull(conf) && CollectionUtils.notEmpty(conf.getConfItems())) {
                for (ConfItem item : conf.getConfItems()) {
                    Optional<String> labelOpt = Optional.ofNullable(item.getI18nConf()).map(I18nConf::getDefaultVal);
                    if (StringUtils.isBlank(item.getInvokerType()) || CollectionUtils.empty(item.getEntity())
                            || !labelOpt.isPresent()) {
                        continue;
                    }
                    TYPE_TO_CONF_ITEM.put(item.getInvokerType(), item.getEntity());
                    ARCHIVED_TYPE_MAP.put(item.getInvokerType(), item.getEntity().keySet());
                    MenuConf menuConf = MenuConf.builder()
                            .invokerType(item.getInvokerType())
                            .labelI18n(item.getI18nConf())
                            .build();
                    INVOKER_LIST.add(menuConf);


                }
            }
        });
    }


    public RefFieldServiceImpl(AppDefaultRocketMQProducer recordReferenceProducer) {
        this.recordReferenceProducer = recordReferenceProducer;
    }

    private void sendFieldRefsMessage(Refs refs) {
        try {
            recordReferenceProducer.sendMessage(JacksonUtils.toJson(refs).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("sendFieldRefsMessage error:{}", e.getMessage(), e);
        }
    }

    @Override
    public void sendRefs(Refs refs) {
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                public void afterCommit() {
                    sendFieldRefsMessage(refs);
                }
            });
        } else {
            sendFieldRefsMessage(refs);
        }
    }
}
