package com.facishare.paas.appframework.core.predef.facade;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.facade.dto.CreateChangeOrder;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderChangedStatus;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderLogicService;
import com.facishare.paas.appframework.metadata.changeorder.MasterAndDetailDescribes;
import com.facishare.paas.appframework.metadata.changeorder.OriginalAndChangeDescribes;
import com.facishare.paas.appframework.metadata.repository.model.MtChangeOrderRule;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.impl.ObjectMappingRuleDetailInfo;
import com.facishare.paas.metadata.impl.ObjectMappingRuleEnumInfo;
import com.facishare.paas.metadata.impl.ObjectMappingRuleInfo;
import com.facishare.paas.metadata.impl.describe.AbstractFieldDescribe;
import com.fxiaoke.api.IdGenerator;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by zhaooju on 2023/4/25
 */
@Slf4j
public class ObjectDataMapping {
    private final User user;
    private final MtChangeOrderRule changeRule;
    private final OriginalAndChangeDescribes describes;
    private final ChangeOrderLogicService changeOrderLogicService;
    private final ObjectMappingService objectMappingService;
    private final boolean onlyChangeField;
    private final boolean isEdit;
    private final boolean needSnapshot;

    /**
     * 计算、统计、引用和自增编号类型的字段在开启变更单时会修改字段类型，这里不能作为映射规则的目标字段
     */
    private static final Set<String> UN_SUPPORT_MAPPING_TARGET_FIELD_TYPE = ImmutableSet.of(IFieldType.QUOTE, IFieldType.FORMULA,
            IFieldType.COUNT, IFieldType.AUTO_NUMBER, IFieldType.MASTER_DETAIL);

    @Builder
    private ObjectDataMapping(User user, MtChangeOrderRule changeRule, OriginalAndChangeDescribes describes,
                              ChangeOrderLogicService changeOrderLogicService, ObjectMappingService objectMappingService,
                              boolean onlyChangeField, boolean isEdit, boolean needSnapshot) {
        this.user = user;
        this.changeRule = changeRule;
        this.describes = describes;
        this.changeOrderLogicService = changeOrderLogicService;
        this.objectMappingService = objectMappingService;
        this.onlyChangeField = onlyChangeField;
        this.isEdit = isEdit;
        this.needSnapshot = needSnapshot;
    }

    public Mapping2ChangeOrderDataResult mapping2ChangeOrderData(CreateChangeOrder.Arg arg) {
        IObjectDescribe[] objectDescribes = describes.getOriginalDescribes().getObjectDescribes().values().toArray(new IObjectDescribe[0]);
        MtChangeOrderRule.ObjectFieldMapper objectFieldMapper = changeRule.toObjectFieldMapper(objectDescribes);
        MasterAndDetailDescribes changeOrderDescribes = describes.getChangeOrderDescribes();
        List<IObjectMappingRuleInfo> mappingRuleInfos = convertToObjectMappingRules(changeRule, describes, false, true);
        Map<String, IObjectMappingRuleInfo> ruleInfoMap = mappingRuleInfos.stream()
                .collect(Collectors.toMap(IObjectMappingRuleInfo::getSourceApiName, Function.identity()));
        Map<String, IObjectDescribe> describesMap = describes.getOriginalDescribes().getObjectDescribes();
        Table<String, String, IObjectData> dataTable = arg.toOriginalDataTable();

        BiFunction<String, IObjectData, ObjectDataMapping.MappingData> triMappingFunction = (describeApiName, objectData) -> {
            IObjectMappingRuleInfo ruleInfo = ruleInfoMap.get(describeApiName);
            Map<String, IObjectData> objectDataMap = dataTable.row(describeApiName);
            MappingData mappingData = mappingData(user, describesMap.get(describeApiName), objectDataMap, ruleInfo, objectData);

            if (isEdit) {
                String id = objectData.get(ObjectDataExt.CHANGED_DATA_ID, String.class);
                if (!Strings.isNullOrEmpty(id)) {
                    mappingData.getObjectData().setId(id);
                }
            }
            return mappingData;
        };

        IObjectData masterData = arg.getMasterData();
        CreateChangeOrder.ChangeOrderDataTuple changeOrderDataTuple = CreateChangeOrder.ChangeOrderDataTuple.buildChangeObjectDataByRule(objectFieldMapper,
                changeOrderDescribes.getObjectDescribe(), masterData, arg.getOriginalData(), null, triMappingFunction);

        Mapping2ChangeOrderDataResult result = buildDetails(changeOrderDescribes.getDetailDescribes(), arg, objectFieldMapper, triMappingFunction, ruleInfoMap);
        IObjectData objectData = changeOrderDataTuple.getChangeObjectData();
        result.setMasterObjectData(objectData);
        IObjectData originalObjectData = changeOrderDataTuple.getOriginalObjectData();
        result.setOriginalObjectData(originalObjectData);

        // 记录变更规则apiName
        objectData.set(ObjectDataExt.CHANGE_ORDER_RULE, changeRule.getApiName());
        // 变更原因字段
        Object changedReason = masterData.get(ObjectDataExt.CHANGED_REASON);
        objectData.set(ObjectDataExt.CHANGED_REASON, changedReason);

        originalObjectData.set("_CallBackData", arg.getCallBackData());
        return result;
    }

    private Mapping2ChangeOrderDataResult buildDetails(Map<String, IObjectDescribe> detailMap, CreateChangeOrder.Arg arg,
                                                       MtChangeOrderRule.ObjectFieldMapper objectFieldMapper,
                                                       BiFunction<String, IObjectData, ObjectDataMapping.MappingData> triMappingFunction, Map<String, IObjectMappingRuleInfo> ruleInfoMap) {
        if (CollectionUtils.empty(detailMap)) {
            return Mapping2ChangeOrderDataResult.builder().build();
        }
        Map<String, List<IObjectData>> details = Maps.newHashMap();
        Map<String, List<IObjectData>> originalDataDetails = Maps.newHashMap();
        Map<String, List<IObjectData>> originalDetails = arg.getOriginalDetails();
        Map<String, List<IObjectData>> detailToAddMap = arg.getDetailToAddMap();
        Map<String, List<IObjectData>> detailToUpdateMap = arg.getDetailToUpdateMap();
        Map<String, List<IObjectData>> detailToDeleteMap = arg.getDetailToDeleteMap();

        for (IObjectDescribe objectDescribe : detailMap.values()) {
            if (!originalDetails.containsKey(objectDescribe.getOriginalDescribeApiName()) || Objects.isNull(ruleInfoMap.get(objectDescribe.getOriginalDescribeApiName()))) {
                continue;
            }
            Map<String, IObjectData> originalDetailMap = originalDetails.get(objectDescribe.getOriginalDescribeApiName()).stream()
                    .collect(Collectors.toMap(IObjectData::getId, Function.identity(), (x, y) -> x));
            List<IObjectData> dataList = Lists.newArrayList();
            List<IObjectData> originalDataList = Lists.newArrayList();

            List<CreateChangeOrder.ChangeOrderDataTuple> changeOrderDataTuples = Lists.newArrayList();
            List<CreateChangeOrder.ChangeOrderDataTuple> addList = buildChangeOrderDataTuple(objectFieldMapper, objectDescribe, originalDetailMap,
                    detailToAddMap.get(objectDescribe.getOriginalDescribeApiName()), CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_ADD, triMappingFunction);
            changeOrderDataTuples.addAll(addList);
            List<CreateChangeOrder.ChangeOrderDataTuple> updateList = buildChangeOrderDataTuple(objectFieldMapper, objectDescribe, originalDetailMap,
                    detailToUpdateMap.get(objectDescribe.getOriginalDescribeApiName()), CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_UPDATE, triMappingFunction);
            changeOrderDataTuples.addAll(updateList);
            List<CreateChangeOrder.ChangeOrderDataTuple> deletedList = buildChangeOrderDataTuple(objectFieldMapper, objectDescribe, originalDetailMap,
                    detailToDeleteMap.get(objectDescribe.getOriginalDescribeApiName()), CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_DELETED, triMappingFunction);
            changeOrderDataTuples.addAll(deletedList);

            for (CreateChangeOrder.ChangeOrderDataTuple changeOrderDataTuple : changeOrderDataTuples) {
                dataList.add(changeOrderDataTuple.getChangeObjectData());
                Optional.ofNullable(changeOrderDataTuple.getOriginalObjectData())
                        .ifPresent(originalDataList::add);
            }
            // 这里按 OrderBy 排一下顺序，让从对象的变更单对象顺序和页面提交的一致
            ObjectDataExt.sortByOrderBy(dataList);
            details.put(objectDescribe.getApiName(), Lists.newArrayList(dataList));
            originalDataDetails.put(objectDescribe.getApiName(), Lists.newArrayList(originalDataList));
        }
        return Mapping2ChangeOrderDataResult.builder()
                .details(details)
                .originalDataDetails(originalDataDetails)
                .build();
    }

    private List<CreateChangeOrder.ChangeOrderDataTuple> buildChangeOrderDataTuple(MtChangeOrderRule.ObjectFieldMapper objectFieldMapper, IObjectDescribe describe,
                                                                                   Map<String, IObjectData> originalDetailMap, List<IObjectData> objectDataList,
                                                                                   String changedType, BiFunction<String, IObjectData, ObjectDataMapping.MappingData> biFunction) {
        List<CreateChangeOrder.ChangeOrderDataTuple> result = Lists.newArrayList();
        if (CollectionUtils.notEmpty(objectDataList)) {
            for (IObjectData changeData : objectDataList) {
                IObjectData originalObjectData = originalDetailMap.get(changeData.getId());
                CreateChangeOrder.ChangeOrderDataTuple dataTuple = CreateChangeOrder.ChangeOrderDataTuple.buildChangeObjectDataByRule(objectFieldMapper,
                        describe, changeData, originalObjectData, changedType, biFunction);
                result.add(dataTuple);
            }
        }
        return result;
    }

    public Result mapping2OriginalData(IObjectData masterData, Map<String, List<IObjectData>> detailsDataMap) {
        MasterAndDetailDescribes changeOrderDescribes = describes.getChangeOrderDescribes();
        IObjectDescribe changeOrderDescribe = changeOrderDescribes.getObjectDescribe();

        // 获取主对象数据和快照
        MappingObjectListResult masterMappingResult = mappingObjectList(user, changeOrderDescribe, describes.getOriginalDescribe(changeOrderDescribe.getOriginalDescribeApiName()),
                changeRule, Lists.newArrayList(masterData));
        IObjectData masterObjectData = masterMappingResult.getUpdateList().get(0);
        masterObjectData.setDescribeApiName(describes.getOriginalDescribe(changeOrderDescribe.getOriginalDescribeApiName()).getApiName());
        masterObjectData.setTenantId(user.getTenantId());

        Map<String, List<IObjectData>> detailsToAdd = Maps.newHashMap();
        Map<String, List<IObjectData>> detailsToUpdate = Maps.newHashMap();
        Map<String, List<IObjectData>> detailsToDelete = Maps.newHashMap();
        Map<String, List<IObjectData>> detailsSnapshot = Maps.newHashMap();

        changeOrderDescribes.getDetailDescribes().forEach((apiName, describe) -> {
            String originalDescribeApiName = describe.getOriginalDescribeApiName();
            List<IObjectData> dataList = detailsDataMap.get(apiName);
            if (CollectionUtils.empty(dataList)) {
                return;
            }
            Optional<String> masterField = ObjectDescribeExt.of(describes.getOriginalDescribe(describe.getOriginalDescribeApiName())).getMasterDetailFieldDescribe()
                    .map(AbstractFieldDescribe::getApiName);
            MappingObjectListResult mappingResult = mappingObjectList(user, describe, describes.getOriginalDescribe(originalDescribeApiName),
                    changeRule, dataList);
            String masterObjectDataId = masterObjectData.getId();
            mappingResult.forEachData(data -> {
                masterField.ifPresent(fieldName -> data.set(fieldName, masterObjectDataId));
                data.setDescribeApiName(originalDescribeApiName);
                data.setTenantId(user.getTenantId());
            });
            buildChangeOrderDataTuple(detailsToAdd, originalDescribeApiName, mappingResult.getAddList());
            buildChangeOrderDataTuple(detailsToUpdate, originalDescribeApiName, mappingResult.getUpdateList());
            buildChangeOrderDataTuple(detailsToDelete, originalDescribeApiName, mappingResult.getDeleteList());

            // 保存明细对象的快照数据
            if (CollectionUtils.notEmpty(mappingResult.getChangeOrderSnapshot())) {
                detailsSnapshot.put(originalDescribeApiName, mappingResult.getChangeOrderSnapshot());
            }
        });

        IObjectData masterSnapshot = CollectionUtils.notEmpty(masterMappingResult.getChangeOrderSnapshot()) ?
                masterMappingResult.getChangeOrderSnapshot().get(0) : null;
        return Result.builder()
                .masterObjectData(masterObjectData)
                .masterSnapshot(masterSnapshot)
                .detailsToAdd(detailsToAdd)
                .detailsToUpdate(detailsToUpdate)
                .detailsToDelete(detailsToDelete)
                .detailsSnapshot(detailsSnapshot)
                .build();
    }

    private void buildChangeOrderDataTuple(Map<String, List<IObjectData>> detailsToAdd, String describeApiName, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        detailsToAdd.computeIfAbsent(describeApiName, x -> Lists.newArrayList()).addAll(dataList);
    }

    private IObjectData mappingMasterData(IObjectData objectData, IObjectDescribe changeOrderDescribe) {
        IObjectDescribe originalDescribe = describes.getOriginalDescribe(changeOrderDescribe.getOriginalDescribeApiName());
        MappingObjectListResult mappingResult = mappingObjectList(user, changeOrderDescribe, originalDescribe,
                changeRule, Lists.newArrayList(objectData));
        IObjectData masterObjectData = mappingResult.getUpdateList().get(0);
        masterObjectData.setDescribeApiName(originalDescribe.getApiName());
        masterObjectData.setTenantId(user.getTenantId());
        return masterObjectData;
    }

    private MappingObjectListResult mappingObjectList(User user, IObjectDescribe changeDescribe, IObjectDescribe originalDescribe,
                                                      MtChangeOrderRule changeRule, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return MappingObjectListResult.builder().build();
        }
        List<String> ids = dataList.stream().map(IObjectData::getId).collect(Collectors.toList());

        List<IObjectData> changeOrderSnapshot = changeOrderLogicService.findChangeOrderSnapshot(user, changeDescribe, ids);
        Map<String, IObjectData> snapshotDataMap = changeOrderSnapshot.stream()
                .map(data -> ObjectDataExt.of(data).copy())
                .collect(Collectors.toMap(IObjectData::getId, Function.identity()));

        ObjectDescribeExt changeObjectDescribeExt = ObjectDescribeExt.of(changeDescribe);
        String originalDataField = changeObjectDescribeExt.isSlaveObject() ? ObjectDataExt.ORIGINAL_DETAIL_DATA : ObjectDataExt.ORIGINAL_DATA;

        IObjectMappingRuleInfo objectMappingRuleInfo = convertToObjectMappingRule(changeRule, originalDescribe, changeDescribe);
        MappingObjectListResult.MappingObjectListResultBuilder resultBuilder = MappingObjectListResult.builder();
        List<IObjectData> addList = Lists.newArrayList();
        List<IObjectData> updateList = Lists.newArrayList();
        List<IObjectData> deleteList = Lists.newArrayList();

        for (IObjectData objectData : dataList) {
            String originalDataId = objectData.get(originalDataField, String.class);
            String changeType = getChangeType(objectData);
            IObjectData snapshotData = snapshotDataMap.get(objectData.getId());
            if (Objects.nonNull(snapshotData)) {
                ObjectDataExt.of(snapshotData).merge(objectData);
            }
            MappingData mappingData = mappingData(user, changeDescribe, snapshotDataMap, objectMappingRuleInfo, objectData);
            IObjectData data = mappingData.getObjectData();
            if (changeObjectDescribeExt.isSlaveObject() && needSnapshot) {
                data.setOrderBy(objectData.getOrderBy());
            }
            if (!Strings.isNullOrEmpty(originalDataId)) {
                data.setId(originalDataId);
            } else if (CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_ADD.equals(changeType)) {
                data.setId(IdGenerator.get());
            }
            // 作废的数据需要补充原单的主属性
            if (CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_DELETED.equals(changeType)) {
                data.setName(objectData.getName());
            }
            // 补充原单id
            data.set(ObjectDataExt.CHANGED_DATA_ID, objectData.getId());
            // 原单上记录变更单的信息
            data.set(ObjectDataExt.CHANGED_BY, objectData.get(ObjectDataExt.CHANGED_BY));
            data.set(ObjectDataExt.CHANGED_TIME, objectData.get(ObjectDataExt.CHANGED_TIME));
            data.set(ObjectDataExt.CHANGED_REASON, objectData.get(ObjectDataExt.CHANGED_REASON));
            data.set(ObjectDataExt.VERSION_NUMBER, objectData.get(ObjectDataExt.VERSION_NUMBER));
            data.set(ObjectDataExt.CHANGED_STATUS, ChangeOrderChangedStatus.NORMAL.getCode());

            switch (changeType) {
                case CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_ADD:
                    addList.add(data);
                    break;
                case CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_UPDATE:
                    updateList.add(data);
                    break;
                case CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_DELETED:
                    deleteList.add(data);
                    break;
            }
        }

        List<IObjectData> originalDataSnapshot = Lists.newArrayList();
        if (needSnapshot) {
            for (IObjectData snapshotData : changeOrderSnapshot) {
                MappingData snapshotmappingData = mappingData(user, changeDescribe, snapshotDataMap, objectMappingRuleInfo, snapshotData, false);
                IObjectData objectData = snapshotmappingData.getObjectData();
                objectData.setId(snapshotData.getId());
                objectData.setDescribeApiName(changeDescribe.getOriginalDescribeApiName());
                originalDataSnapshot.add(objectData);
            }
        }
        return resultBuilder
                .addList(addList)
                .updateList(updateList)
                .deleteList(deleteList)
                .changeOrderSnapshot(originalDataSnapshot)
                .build();
    }

    private String getChangeType(IObjectData objectData) {
        String changeType = ObjectDataExt.of(objectData).getChangedType();
        if (Strings.isNullOrEmpty(changeType)) {
            return CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_UPDATE;
        }
        return changeType;
    }

    private MappingData mappingData(User user, IObjectDescribe changeDescribe, Map<String, IObjectData> objectDataMap,
                                    IObjectMappingRuleInfo objectMappingRuleInfo, IObjectData objectData) {
        return mappingData(user, changeDescribe, objectDataMap, objectMappingRuleInfo, objectData, onlyChangeField);
    }

    /**
     * 处理对象数据映射
     * 将原始数据映射为变更单数据，或将变更单数据映射回原始数据
     *
     * @param user                  当前用户
     * @param changeDescribe        变更描述对象
     * @param objectDataMap         数据Map,key为数据ID
     * @param objectMappingRuleInfo 映射规则
     * @param objectData            待映射的数据对象
     * @param onlyChangeField       是否只映射变更字段
     * @return 映射结果
     */
    private MappingData mappingData(User user, IObjectDescribe changeDescribe, Map<String, IObjectData> objectDataMap,
                                    IObjectMappingRuleInfo objectMappingRuleInfo, IObjectData objectData, boolean onlyChangeField) {
        if (log.isDebugEnabled()) {
            log.debug("Start mapping data for object: {}, onlyChangeField: {}", objectData.getDescribeApiName(), onlyChangeField);
        }

        // 获取快照数据进行差异比较
        IObjectData snapshotData = objectDataMap.get(objectData.getId());
        IObjectData diffData = getDiffData(changeDescribe, objectData, snapshotData);

        // 执行数据映射
        IObjectData data = objectMappingService.mappingData(user, objectMappingRuleInfo, objectData);
        ObjectDataExt dataExt = ObjectDataExt.of(data);
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(changeDescribe);
        AtomicBoolean isChange = new AtomicBoolean(false);

        // 遍历处理每个字段的映射
        for (IObjectMappingRuleDetailInfo ruleDetailInfo : objectMappingRuleInfo.getFieldMapping()) {
            String targetFieldName = ruleDetailInfo.getTargetFieldName();
            String sourceFieldName = ruleDetailInfo.getSourceFieldName();

            // 检查字段是否发生变更
            if (!isChange.get()) {
                isChange.compareAndSet(false, diffData.containsField(sourceFieldName));
            }

            // 处理字段映射逻辑
            if (onlyChangeField) {
                // 只映射变更字段
                if (!diffData.containsField(sourceFieldName)) {
                    dataExt.remove(targetFieldName);
                }
            } else if (Objects.nonNull(snapshotData)) {
                // 映射所有字段
                if (objectData.containsField(sourceFieldName)) {
                    dataExt.setIfAbsent(targetFieldName, snapshotData.get(sourceFieldName));
                } else {
                    dataExt.set(targetFieldName, snapshotData.get(sourceFieldName));
                }
            }

            // 获取字段描述信息
            IFieldDescribe fieldDescribe = describeExt.getFieldDescribeSilently(sourceFieldName).orElse(null);
            if (Objects.isNull(fieldDescribe)) {
                continue;
            }

            // 处理单选多选字段的其他选项
            if (FieldDescribeExt.isSelectField(fieldDescribe.getType())) {
                handleSelectField(diffData, dataExt, fieldDescribe, sourceFieldName, targetFieldName, isChange);
            }

            // 处理富文本字段
            if (RichTextExt.isProcessableRichText(fieldDescribe)) {
                handleRichTextField(objectData, dataExt, fieldDescribe, targetFieldName);
            }
        }

        if (log.isDebugEnabled()) {
            log.debug("Finished mapping data for object: {}, hasChanges: {}", objectData.getDescribeApiName(), isChange.get());
        }
        return new MappingData(data, isChange.get());
    }

    /**
     * 处理选择类型字段的映射
     */
    private void handleSelectField(IObjectData diffData, ObjectDataExt dataExt, IFieldDescribe fieldDescribe,
                                   String sourceFieldName, String targetFieldName, AtomicBoolean isChange) {
        // 处理其他选项值
        if (ObjectDataExt.hasOtherValue(diffData.get(sourceFieldName))) {
            String selectOther = FieldDescribeExt.getSelectOther(fieldDescribe.getApiName());
            dataExt.set(FieldDescribeExt.getSelectOther(targetFieldName), diffData.get(selectOther));
            if (!isChange.get()) {
                isChange.compareAndSet(false, diffData.containsField(selectOther));
            }
        }
    }

    /**
     * 处理富文本字段的映射
     */
    private void handleRichTextField(IObjectData objectData, ObjectDataExt dataExt,
                                     IFieldDescribe fieldDescribe, String targetFieldName) {
        String richTextAbstractName = RichTextExt.getRichTextAbstractName(fieldDescribe.getApiName());
        dataExt.set(RichTextExt.getRichTextAbstractName(targetFieldName), objectData.get(richTextAbstractName));
    }

    private IObjectData getDiffData(IObjectDescribe changeDescribe, IObjectData objectData, IObjectData snapshotData) {
        if (Objects.isNull(snapshotData)) {
            return objectData;
        }
        Map<String, Object> diff = ObjectDataExt.of(snapshotData).diff(objectData, changeDescribe);
        ObjectDataExt dataExt = ObjectDataExt.of(diff);
        dataExt.setTenantId(objectData.getTenantId());
        dataExt.setDescribeApiName(objectData.getDescribeApiName());
        return dataExt.getObjectData();
    }

    /**
     * 从变更单还原原单的数据
     *
     * @param changeOrderRule
     * @param originalDescribe
     * @param changeDescribe
     * @return
     */
    private IObjectMappingRuleInfo convertToObjectMappingRule(MtChangeOrderRule changeOrderRule, IObjectDescribe originalDescribe,
                                                              IObjectDescribe changeDescribe) {
        OriginalAndChangeDescribes describes = OriginalAndChangeDescribes.buildByDescribe(originalDescribe, changeDescribe);
        List<IObjectMappingRuleInfo> ruleInfos = convertToObjectMappingRules(changeOrderRule, describes, true, false);
        if (CollectionUtils.empty(ruleInfos)) {
            return null;
        }
        return ruleInfos.get(0);
    }

    private List<IObjectMappingRuleInfo> convertToObjectMappingRules(MtChangeOrderRule changeOrderRule, OriginalAndChangeDescribes describes,
                                                                     boolean excludeDetailObj, boolean toChange) {
        List<IObjectMappingRuleInfo> result = Lists.newArrayList();
        for (MtChangeOrderRule.ObjectMapping objectMapping : changeOrderRule.getFieldMapping()) {
            String sourceApiName = objectMapping.getSourceApiName();
            if (excludeDetailObj && !describes.isMasterDescribe(sourceApiName)) {
                continue;
            }

            String targetApiName = objectMapping.getTargetApiName();
            IObjectMappingRuleInfo objectMappingRuleInfo = new ObjectMappingRuleInfo();
            objectMappingRuleInfo.setMdType(2);
            if (toChange) {
                objectMappingRuleInfo.setSourceApiName(sourceApiName);
                objectMappingRuleInfo.setTargetApiName(targetApiName);
            } else {
                objectMappingRuleInfo.setSourceApiName(targetApiName);
                objectMappingRuleInfo.setTargetApiName(sourceApiName);
            }

            IObjectDescribe targetDescribe = getTargetDescribe(describes, toChange, sourceApiName, targetApiName);

            Map<String, IFieldDescribe> changeFieldMapping = describes.getChangeFieldMapping(sourceApiName, targetApiName);
            List<MtChangeOrderRule.FieldMapping> fieldMappings = objectMapping.getFieldMappings();
            Set<String> fieldset = fieldMappings.stream()
                    .map(MtChangeOrderRule.FieldMapping::getSourceFieldApiName)
                    .collect(Collectors.toSet());

            changeFieldMapping.entrySet().stream()
                    // 移除已经配置的字段
                    .filter(it -> !fieldset.contains(it.getKey()))
                    .map(it -> new MtChangeOrderRule.FieldMapping(it.getKey(), it.getValue().getApiName(), null, null))
                    .forEach(fieldMappings::add);

            List<IObjectMappingRuleDetailInfo> objectMappingRuleDetailInfos = Lists.newArrayList();
            for (MtChangeOrderRule.FieldMapping fieldMapping : fieldMappings) {
                IObjectMappingRuleDetailInfo ruleDetailInfo = new ObjectMappingRuleDetailInfo();
                if (toChange) {
                    ruleDetailInfo.setSourceFieldName(fieldMapping.getSourceFieldApiName());
                    ruleDetailInfo.setTargetFieldName(fieldMapping.getTargetFieldApiName());
                } else {
                    ruleDetailInfo.setSourceFieldName(fieldMapping.getTargetFieldApiName());
                    ruleDetailInfo.setTargetFieldName(fieldMapping.getSourceFieldApiName());
                }

                String targetFieldName = ruleDetailInfo.getTargetFieldName();
                if (unSupportMappingTargetField(targetDescribe, targetFieldName)) {
                    continue;
                }

                List<IObjectMappingRuleEnumInfo> optionMapping = buildOptionMapping(changeFieldMapping.get(fieldMapping.getSourceFieldApiName()));
                if (CollectionUtils.notEmpty(optionMapping)) {
                    ruleDetailInfo.setOptionMapping(optionMapping);
                }

                objectMappingRuleDetailInfos.add(ruleDetailInfo);
            }
            objectMappingRuleInfo.setFieldMapping(objectMappingRuleDetailInfos);
            result.add(objectMappingRuleInfo);
        }
        return result;
    }

    private boolean unSupportMappingTargetField(IObjectDescribe targetDescribe, String targetFieldName) {
        return ObjectDescribeExt.of(targetDescribe).getFieldDescribeSilently(targetFieldName)
                .map(IFieldDescribe::getType)
                .filter(UN_SUPPORT_MAPPING_TARGET_FIELD_TYPE::contains)
                .isPresent();
    }

    private IObjectDescribe getTargetDescribe(OriginalAndChangeDescribes describes, boolean toChange, String sourceApiName, String targetApiName) {
        if (toChange) {
            return describes.getChangeOrderDescribe(targetApiName);
        }
        return describes.getOriginalDescribe(sourceApiName);
    }

    private List<IObjectMappingRuleEnumInfo> buildOptionMapping(IFieldDescribe fieldDescribe) {
        List<IObjectMappingRuleEnumInfo> result = Lists.newArrayList();
        if (Objects.isNull(fieldDescribe)) {
            return result;
        }
        if (!FieldDescribeExt.isSelectField(fieldDescribe.getType())) {
            return result;
        }
        for (ISelectOption selectOption : ((SelectOne) fieldDescribe).getSelectOptions()) {
            ObjectMappingRuleEnumInfo objectMappingRuleEnumInfo = new ObjectMappingRuleEnumInfo();
            objectMappingRuleEnumInfo.setSourceEnumCode(selectOption.getValue());
            objectMappingRuleEnumInfo.setTargetEnumCode(selectOption.getValue());
            result.add(objectMappingRuleEnumInfo);
        }
        return result;
    }

    @Data
    @Builder
    public static class Result {
        private IObjectData masterObjectData;
        private Map<String, List<IObjectData>> detailsToAdd;
        private Map<String, List<IObjectData>> detailsToUpdate;
        private Map<String, List<IObjectData>> detailsToDelete;
        private IObjectData masterSnapshot;
        private Map<String, List<IObjectData>> detailsSnapshot;
    }

    @Data
    @Builder
    public static class Mapping2ChangeOrderDataResult {
        private IObjectData masterObjectData;
        private Map<String, List<IObjectData>> details;

        private IObjectData originalObjectData;
        private Map<String, List<IObjectData>> originalDataDetails;
    }

    @Data
    public static class MappingData {
        private final IObjectData objectData;
        private final boolean change;

        private MappingData(IObjectData objectData, boolean change) {
            this.objectData = objectData;
            this.change = change;
        }
    }

    @Data
    @Builder
    public static class MappingObjectListResult {
        private List<IObjectData> addList;
        private List<IObjectData> updateList;
        private List<IObjectData> deleteList;
        private List<IObjectData> changeOrderSnapshot;

        public void forEachData(Consumer<IObjectData> consumer) {
            Stream.of(addList, updateList, deleteList)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .forEach(consumer);
        }
    }

}
