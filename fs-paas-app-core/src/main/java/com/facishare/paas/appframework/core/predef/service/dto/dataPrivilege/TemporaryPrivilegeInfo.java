package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.privilege.dto.TemporaryPermissionInfo;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@Slf4j
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TemporaryPrivilegeInfo {
    @JSONField(name = "object_describe_apiName")
    @SerializedName(value = "object_describe_apiName")
    @JsonProperty(value = "object_describe_apiName")
    String objectDescribeApiName;

    @JSONField(name = "object_describe_label")
    @SerializedName(value = "object_describe_label")
    @JsonProperty(value = "object_describe_label")
    String objectDescribeLabel;

    @JSONField(name = "object_data_id")
    @SerializedName(value = "object_data_id")
    @JsonProperty(value = "object_data_id")
    String objectDataId;

    @JSONField(name = "object_data_label")
    @SerializedName(value = "object_data_label")
    @JsonProperty(value = "object_data_label")
    String objectDataLabel;

    @JSONField(name = "owners")
    @SerializedName(value = "owners")
    @JsonProperty(value = "owners")
    Map<String, String> owners;

    @JSONField(name = "scene_name")
    @SerializedName(value = "scene_name")
    @JsonProperty(value = "scene_name")
    String sceneName;

    @JSONField(name = "scene")
    @SerializedName(value = "scene")
    @JsonProperty(value = "scene")
    String scene;


    public static Map<String, String> getOwnerMap(Map<String, String> ownerLabelMap, List<String> ownerList) {
        Map<String, String> ownerMap = Maps.newHashMap();
        if (CollectionUtils.empty(ownerList)) {
            return Collections.emptyMap();
        } else {
            ownerList.forEach(x -> {
                ownerMap.put(x, ownerLabelMap.get(x));
            });
        }
        return ownerMap;
    }

    /*
     * 给前端返回列表中的每条数据，包含对象label和data的label，以及每条数据包含的userId和userName
     * */
    public static List<TemporaryPrivilegeInfo> of(List<TemporaryPermissionInfo> temporaryPermissionInfoList,
                                                  Map<String, Map<String, String>> objectApiNameForIdLabelMap,
                                                  Map<String, String> ownerLabelMap,
                                                  Map<String, IObjectDescribe> objectDescribeMap) {

        List<TemporaryPrivilegeInfo> temporaryPrivilegeInfoList = temporaryPermissionInfoList.stream()
                .filter(x -> objectApiNameForIdLabelMap.keySet().contains(x.getEntityId()) &&
                        objectDescribeMap.containsKey(x.getEntityId()))
                .map(x -> TemporaryPrivilegeInfo.builder()
                        .objectDescribeApiName(x.getEntityId())
                        .objectDescribeLabel(objectDescribeMap.get(x.getEntityId()).getDisplayName())
                        .objectDataId(x.getDataId())
                        .objectDataLabel(objectApiNameForIdLabelMap.get(x.getEntityId()).get(x.getDataId()))
                        .owners(getOwnerMap(ownerLabelMap, x.getOwners()))
                        .sceneName(x.getSceneName())
                        .scene(x.getScene())
                        .build()
        ).collect(Collectors.toList());
        return temporaryPrivilegeInfoList;
    }


}
