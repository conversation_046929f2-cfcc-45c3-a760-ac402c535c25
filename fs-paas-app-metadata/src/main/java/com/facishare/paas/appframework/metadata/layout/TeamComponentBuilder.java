package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ComponentActions;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.ButtonLogicService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.describe.EmbeddedObjectListFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.EmbeddedField;
import com.facishare.paas.metadata.impl.ui.layout.component.UserFiled;
import com.facishare.paas.metadata.impl.ui.layout.component.UserListComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IEmbeddedField;
import com.facishare.paas.metadata.ui.layout.IUserField;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

/**
 * 把创建相关团队组件的逻辑封装起来
 * <p>
 * Created by liyiguang on 2017/11/14.
 */
@Slf4j
@Builder
public class TeamComponentBuilder {

    public static final String NAME = "name";
    public static final String POST = "post";
    public static final String OUT_TENANT = "outTenant";
    private User user;

    private ObjectDescribeExt objectDescribeExt;

    private IObjectData objectData;

    private ButtonLogicService buttonLogicService;

    private LicenseService licenseService;

    public UserListComponent build() {
        UserListComponent component = buildUserListComponentWithoutFieldsAndButtons();

        fillIncludedFields(component);

        List<IButton> allButtons = getButtons();
        component.setButtons(allButtons);

        return component;
    }

    private void fillIncludedFields(UserListComponent component) {
        Optional<EmbeddedObjectListFieldDescribe> fieldDescribe =
                objectDescribeExt.getFieldDescribeSilently(UdobjConstants.RELEVANT_TEAM_API_NAME)
                        .map(x -> (EmbeddedObjectListFieldDescribe) x);

        if (!fieldDescribe.isPresent()) {
            return;
        }

        //如果describe中有相关团队字段，则下发字段信息
        UserFiled userFiled = new UserFiled();
        userFiled.setRenderType(IFieldType.EMBEDDED_OBJECT_LIST);
        userFiled.setName(ObjectDataExt.RELEVANT_TEAM);
        List<IEmbeddedField> embeddedFields = Lists.newArrayList();

        for (IFieldDescribe iFieldDescribe : getFieldDescribes(fieldDescribe.get())) {
            if (iFieldDescribe.getType().equals(IFieldType.EMPLOYEE)) {
                List<String> RELEVANT_FIELDS_EMPLOYEE_API_NAME_LIST = Lists.newArrayList(NAME, POST);
                List<String> RELEVANT_FIELDS_EMPLOYEE_LABEL_LIST = Lists.newArrayList(I18N.text(I18NKey.NAME), I18N.text(I18NKey.POSITION));
                List<String> RELEVANT_FIELDS_EMPLOYEE_RENDER_TYPE_LIST = Lists.newArrayList(IFieldType.TEXT, IFieldType.TEXT);
                for (int i = 0; i < RELEVANT_FIELDS_EMPLOYEE_API_NAME_LIST.size(); i++) {
                    EmbeddedField embeddedField = new EmbeddedField();
                    embeddedField.setName(iFieldDescribe.getApiName());
                    embeddedField.setLabelName(RELEVANT_FIELDS_EMPLOYEE_LABEL_LIST.get(i));
                    embeddedField.setRenderType(UdobjConstants.RENDER_TYPE_EMPLOYEE_NEST);
                    embeddedField.setEmployeeRenderType(RELEVANT_FIELDS_EMPLOYEE_RENDER_TYPE_LIST.get(i));
                    embeddedField.setEmployeeRenderFields(RELEVANT_FIELDS_EMPLOYEE_API_NAME_LIST.get(i));
                    embeddedFields.add(embeddedField);
                }
            } else {
                EmbeddedField embeddedField = new EmbeddedField();
                embeddedField.setName(iFieldDescribe.getApiName());
                embeddedField.setLabelName(iFieldDescribe.getLabel());
                embeddedField.setRenderType(iFieldDescribe.getType());
                embeddedFields.add(embeddedField);
            }
        }

        userFiled.setEmbeddedFileds(embeddedFields);
        List<IUserField> userFiledList = Lists.newArrayList();
        //下游企业不展示上游的相关团队
        if (!user.isOutUser()) {
            userFiledList.add(userFiled);
        }
        //老终端不显示外部相关团队组件
        if (!RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_720)) {
            UserFiled outUserField = createOutTeamMemberField(userFiled);
            userFiledList.add(outUserField);
        }
        component.setIncludeFields(userFiledList);
    }

    private UserFiled createOutTeamMemberField(UserFiled userFiled) {
        UserFiled newFiled = new UserFiled();
        newFiled.setRenderType(IFieldType.EMBEDDED_OBJECT_LIST);
        newFiled.setName(ObjectDataExt.OUT_RELEVANT_TEAM);
        List<IEmbeddedField> embeddedFields = Lists.newArrayList();
        for (IEmbeddedField embeddedField : userFiled.getEmbeddedFileds()) {
            EmbeddedField newEmbeddedFiled = new EmbeddedField();
            if (POST.equals(embeddedField.getEmployeeRenderFields())) {
                // 外部相关团队的职位改为"互联企业"
                newEmbeddedFiled.setName(embeddedField.getName());
                newEmbeddedFiled.setLabelName(I18N.text(I18NKey.CONNECTED_ENTERPRISE));
                newEmbeddedFiled.setRenderType(embeddedField.getRenderType());
                newEmbeddedFiled.setEmployeeRenderType(embeddedField.getEmployeeRenderType());
                newEmbeddedFiled.setEmployeeRenderFields(OUT_TENANT);
                embeddedFields.add(newEmbeddedFiled);
            } else {
                newEmbeddedFiled.setName(embeddedField.getName());
                newEmbeddedFiled.setLabelName(embeddedField.getLabelName());
                newEmbeddedFiled.setRenderType(embeddedField.getRenderType());
                newEmbeddedFiled.setEmployeeRenderType(embeddedField.getEmployeeRenderType());
                newEmbeddedFiled.setEmployeeRenderFields(embeddedField.getEmployeeRenderFields());
                embeddedFields.add(newEmbeddedFiled);
            }
        }
        newFiled.setEmbeddedFileds(embeddedFields);
        return newFiled;
    }

    public UserListComponent buildUserListComponentWithoutFieldsAndButtons() {
        return LayoutComponents.buildUserListComponent();
    }

    private List<IButton> getButtons() {
        if (objectData == null || ObjectDataExt.of(objectData).isInvalid()) {
            return Lists.newArrayList();
        }
        return buttonLogicService.getButtonByComponentActions(user, ComponentActions.TEAM,
                objectDescribeExt.getObjectDescribe(), objectData, false);
    }

    private List<IFieldDescribe> getFieldDescribes(EmbeddedObjectListFieldDescribe describe) {
        return describe.getFieldDescribes();
    }

}
