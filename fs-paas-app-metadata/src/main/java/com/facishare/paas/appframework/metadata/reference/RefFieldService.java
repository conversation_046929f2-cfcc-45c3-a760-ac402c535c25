package com.facishare.paas.appframework.metadata.reference;

import com.facishare.paas.appframework.core.model.ref.RefMessage.Refs;
import com.facishare.paas.appframework.metadata.reference.WhereUsedDisplayConf.ConfItem;
import com.facishare.paas.appframework.metadata.reference.WhereUsedDisplayConf.EntityConf;
import com.facishare.paas.appframework.metadata.reference.WhereUsedDisplayConf.MenuConf;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface RefFieldService {

    Map<String, Map<String, EntityConf>> TYPE_TO_CONF_ITEM = Maps.newHashMap();

    List<MenuConf> INVOKER_LIST = Lists.newArrayList();

    Map<String, Set<String>> ARCHIVED_TYPE_MAP = new HashMap<>();

    void sendRefs(Refs refs);
}
