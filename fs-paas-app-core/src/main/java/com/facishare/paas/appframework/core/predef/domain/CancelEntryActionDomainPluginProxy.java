package com.facishare.paas.appframework.core.predef.domain;

import com.facishare.rest.core.annotation.*;

import java.util.Map;

@RestResource(
        value = "NCRM",
        desc = "取消入账领域插件RPC代理类", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC"
)
public interface CancelEntryActionDomainPluginProxy {
    @POST(desc = "入账领域插件rest接口")
    CancelEntryActionDomainPlugin.RestResult post(@ServiceURLParam String url, @Body CancelEntryActionDomainPlugin.Arg arg, @HeaderMap Map<String, String> header);

}
