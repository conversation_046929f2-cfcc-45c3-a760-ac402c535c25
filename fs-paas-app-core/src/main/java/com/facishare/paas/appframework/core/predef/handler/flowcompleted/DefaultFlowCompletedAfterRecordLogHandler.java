package com.facishare.paas.appframework.core.predef.handler.flowcompleted;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.facade.AuditLogServiceFacade;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created by zhouwr on 2023/10/7.
 */
@Component
@HandlerProvider(name = "defaultFlowCompletedAfterRecordLogHandler")
public class DefaultFlowCompletedAfterRecordLogHandler extends AbstractFlowCompletedAfterHandler {

    @Autowired
    private AuditLogServiceFacade auditLogServiceFacade;

    @Override
    protected Result doHandle(HandlerContext context, Arg arg) {
        ApprovalFlowTriggerType triggerType = arg.getInterfaceArg().approvalFlowTriggerType();
        switch (triggerType) {
            case UPDATE:
                recordEditLog(context, arg);
                break;
            case INVALID:
                recordInvalidLog(context, arg);
                break;
            default:
                break;
        }
        return new Result();
    }

    private void recordEditLog(HandlerContext context, Arg arg) {
        Map<String, Map<String, Object>> detailChangeMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(arg.getDetailChange())) {
            arg.getDetailChange().forEach((k, v) -> detailChangeMap.put(k, (Map<String, Object>) v));
        }
        auditLogServiceFacade.recordEditLog(context.getUser(), arg.data(), arg.dbData(),
                arg.getEditActionInfo().getUpdatedFieldMap().get(arg.getObjectApiName()).get(arg.data().getId()),
                arg.dbDetailDataMap(), ObjectDataDocument.ofDataMap(arg.getEditActionInfo().getDetailsToAdd()),
                ObjectDataDocument.ofDataMap(arg.getEditActionInfo().getDetailsToUpdate()),
                ObjectDataDocument.ofDataMap(arg.getEditActionInfo().getDetailsToDelete()),
                ObjectDataDocument.ofDataMap(arg.getEditActionInfo().getDetailsToInvalid()), detailChangeMap,
                arg.getDescribeMap(), Maps.newHashMap(), arg.getConvertRuleDataContainer());
    }

    private void recordInvalidLog(HandlerContext context, Arg arg) {
        auditLogServiceFacade.recordInvalidLog(context.getUser(), Lists.newArrayList(arg.data()), arg.detailDataMap(),
                arg.getDescribeMap());
    }
}
