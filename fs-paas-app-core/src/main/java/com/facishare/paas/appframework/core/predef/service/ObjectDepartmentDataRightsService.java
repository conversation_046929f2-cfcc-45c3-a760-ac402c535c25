package com.facishare.paas.appframework.core.predef.service;


import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.departmentDataPrivilege.*;
import com.facishare.paas.appframework.privilege.DepartmentDataRightsLogicService;
import com.facishare.paas.appframework.privilege.dto.DepartmentDataRights;
import com.facishare.paas.appframework.privilege.dto.DepartmentRightsResult;
import com.facishare.paas.appframework.privilege.dto.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 部门内数据权限
 * Created by fengjy in 2020/11/18 16:41
 */
@ServiceModule("departmentDataPrivilege")
@Component
@Slf4j
public class ObjectDepartmentDataRightsService {

    @Autowired
    DepartmentDataRightsLogicService departmentDataRightsLogicService;

    @ServiceMethod("upsert")
    public UpsertDepartmentDataRights.Result upsert(ServiceContext context, UpsertDepartmentDataRights.Arg arg) {
        Integer type = null;
        if (ObjectUtils.isEmpty(arg.getType())) {
            type = arg.isIncludeSub() ? 0 : 1;
        } else {
            type = arg.getType();
        }
        departmentDataRightsLogicService.upsert(context.getUser(), arg.getEntityIds(), arg.getDeptIds(), arg.getScene(), type);
        return new UpsertDepartmentDataRights.Result();
    }

    @ServiceMethod("query")
    public QueryDepartmentDataRights.Result query(ServiceContext context, QueryDepartmentDataRights.Arg arg) {
        Tuple<PageInfo, List<DepartmentDataRights>> query = departmentDataRightsLogicService.query(context.getUser(),
                arg.getEntityIds(),
                arg.getDepartmentIds(),
                arg.getScene(),
                arg.getPage(),
                arg.getSize());

        List<DepartmentRightsResult> dataList = query.getValue()
                .stream().map(DepartmentRightsResult::of)
                .collect(Collectors.toList());

        QueryDepartmentDataRights.Result result = QueryDepartmentDataRights.Result.builder()
                .pageInfo(query.getKey())
                .result(dataList)
                .build();
        return result;
    }

    @ServiceMethod("find")
    public FindDepartmentDataRights.Result find(ServiceContext context, FindDepartmentDataRights.Arg arg) {
        List<DepartmentDataRights> result = departmentDataRightsLogicService.findByIds(context.getUser(), arg.getScene(), arg.getIds());
        List<DepartmentRightsResult> dataList = result.stream()
                .map(DepartmentRightsResult::of)
                .collect(Collectors.toList());

        return FindDepartmentDataRights
                .Result.builder()
                .result(dataList)
                .build();
    }

    @ServiceMethod("disable")
    public DisableDepartmentDataRights.Result disable(ServiceContext context, DisableDepartmentDataRights.Arg arg) {
        departmentDataRightsLogicService.disableByIds(context.getUser(), arg.getScene(), arg.getIds());
        return new DisableDepartmentDataRights.Result();
    }

    @ServiceMethod("enable")
    public EnableDepartmentDataRights.Result enable(ServiceContext context, EnableDepartmentDataRights.Arg arg) {
        departmentDataRightsLogicService.enable(context.getUser(), arg.getScene(), arg.getIds());
        return new EnableDepartmentDataRights.Result();
    }


    @ServiceMethod("delete")
    public DeleteDepartmentDataRights.Result delete(ServiceContext context, DeleteDepartmentDataRights.Arg arg) {
        departmentDataRightsLogicService.deleteByIds(context.getUser(), arg.getScene(), arg.getIds());
        return new DeleteDepartmentDataRights.Result();
    }


}
