package com.facishare.paas.appframework.metadata.layout.resource;

import com.facishare.paas.appframework.common.util.TenantUtil;
import com.facishare.paas.appframework.core.model.User;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.lang.reflect.Method;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class LayoutViewResourceProxyImplTest {

    @Mock
    private User user;

    private LayoutViewResourceProxyImpl layoutViewResourceProxy;

    @BeforeEach
    void setUp() {
        layoutViewResourceProxy = new LayoutViewResourceProxyImpl();
        lenient().when(user.getTenantId()).thenReturn("74255");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找布局视图资源的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找布局视图资源")
    void testFindLayoutViewResource_NormalCase() {
        // Given
        String describeApiName = "test_object";

        try (MockedStatic<TenantUtil> tenantUtilMock = mockStatic(TenantUtil.class)) {
            tenantUtilMock.when(() -> TenantUtil.judgmentGrayByConfigKey(anyString(), anyString()))
                    .thenReturn(true);

            // When
            List<LayoutViewResourceProxyImpl.LayoutViewDTO> result = 
                    layoutViewResourceProxy.findLayoutViewResource(user, describeApiName);

            // Then
            assertNotNull(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找不存在的对象的布局视图资源
     */
    @Test
    @DisplayName("边界场景 - 查找不存在对象的布局视图资源")
    void testFindLayoutViewResource_NonExistentObject() {
        // Given
        String describeApiName = "non_existent_object";

        try (MockedStatic<TenantUtil> tenantUtilMock = mockStatic(TenantUtil.class)) {
            tenantUtilMock.when(() -> TenantUtil.judgmentGrayByConfigKey(anyString(), anyString()))
                    .thenReturn(true);

            // When
            List<LayoutViewResourceProxyImpl.LayoutViewDTO> result = 
                    layoutViewResourceProxy.findLayoutViewResource(user, describeApiName);

            // Then
            assertNotNull(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找布局视图资源时传入null参数
     */
    @Test
    @DisplayName("边界场景 - 传入null参数")
    void testFindLayoutViewResource_NullParameters() {
        // Given
        String describeApiName = null;

        try (MockedStatic<TenantUtil> tenantUtilMock = mockStatic(TenantUtil.class)) {
            tenantUtilMock.when(() -> TenantUtil.judgmentGrayByConfigKey(anyString(), anyString()))
                    .thenReturn(true);

            // When
            List<LayoutViewResourceProxyImpl.LayoutViewDTO> result = 
                    layoutViewResourceProxy.findLayoutViewResource(user, describeApiName);

            // Then
            assertNotNull(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找布局视图资源时用户为null
     */
    @Test
    @DisplayName("异常场景 - 用户为null")
    void testFindLayoutViewResource_NullUser() {
        // Given
        String describeApiName = "test_object";

        // When
        List<LayoutViewResourceProxyImpl.LayoutViewDTO> result =
                layoutViewResourceProxy.findLayoutViewResource(null, describeApiName);

        // Then
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找UDOBJ类型的布局视图资源
     */
    @Test
    @DisplayName("正常场景 - 查找UDOBJ类型布局视图资源")
    void testFindLayoutViewResource_UdObjType() {
        // Given
        String describeApiName = "custom_object__c";

        try (MockedStatic<TenantUtil> tenantUtilMock = mockStatic(TenantUtil.class)) {
            tenantUtilMock.when(() -> TenantUtil.judgmentGrayByConfigKey(anyString(), anyString()))
                    .thenReturn(true);

            // When
            List<LayoutViewResourceProxyImpl.LayoutViewDTO> result = 
                    layoutViewResourceProxy.findLayoutViewResource(user, describeApiName);

            // Then
            assertNotNull(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试初始化方法
     */
    @Test
    @DisplayName("正常场景 - 测试初始化方法")
    void testInit_NormalCase() {
        // Given
        try (MockedStatic<ConfigFactory> configFactoryMock = mockStatic(ConfigFactory.class)) {
            configFactoryMock.when(() -> ConfigFactory.getConfig(anyString(), any()))
                    .thenAnswer(invocation -> {
                        // 模拟配置回调
                        return null;
                    });

            // When & Then
            assertDoesNotThrow(() -> {
                layoutViewResourceProxy.init();
            });
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试LayoutViewDTO构造函数
     */
    @Test
    @DisplayName("正常场景 - 测试LayoutViewDTO构造")
    void testLayoutViewDTO_Constructor() {
        // Given
        String apiName = "test_view";
        String label = "Test View";
        String labelI18NKey = "test.view.label";
        Boolean defaultShow = true;
        Boolean defaultView = false;
        String grayKey = "test_gray_key";

        // When
        LayoutViewResourceProxyImpl.LayoutViewDTO dto = 
                new LayoutViewResourceProxyImpl.LayoutViewDTO(
                        apiName, label, labelI18NKey, defaultShow, defaultView, grayKey);

        // Then
        assertNotNull(dto);
        assertEquals(apiName, dto.getApiName());
        assertEquals(defaultShow, dto.getDefaultShow());
        assertEquals(defaultView, dto.getDefaultView());
        assertEquals(grayKey, dto.getGrayKey());
        assertNotNull(dto.getLabel());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试LayoutViewDTO的equals和hashCode方法
     */
    @Test
    @DisplayName("正常场景 - 测试LayoutViewDTO的equals和hashCode")
    void testLayoutViewDTO_EqualsAndHashCode() {
        // Given
        String apiName = "test_view";
        LayoutViewResourceProxyImpl.LayoutViewDTO dto1 = 
                new LayoutViewResourceProxyImpl.LayoutViewDTO(
                        apiName, "Label1", "key1", true, false, "gray1");
        LayoutViewResourceProxyImpl.LayoutViewDTO dto2 = 
                new LayoutViewResourceProxyImpl.LayoutViewDTO(
                        apiName, "Label2", "key2", false, true, "gray2");
        LayoutViewResourceProxyImpl.LayoutViewDTO dto3 = 
                new LayoutViewResourceProxyImpl.LayoutViewDTO(
                        "different_api", "Label1", "key1", true, false, "gray1");

        // When & Then
        assertEquals(dto1, dto2); // 相同的apiName应该相等
        assertNotEquals(dto1, dto3); // 不同的apiName应该不相等
        assertEquals(dto1.hashCode(), dto2.hashCode()); // 相同的apiName应该有相同的hashCode
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试LayoutViewDTO的toString方法
     */
    @Test
    @DisplayName("正常场景 - 测试LayoutViewDTO的toString")
    void testLayoutViewDTO_ToString() {
        // Given
        LayoutViewResourceProxyImpl.LayoutViewDTO dto = 
                new LayoutViewResourceProxyImpl.LayoutViewDTO(
                        "test_view", "Test View", "test.view.label", true, false, "test_gray");

        // When
        String result = dto.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("test_view"));
        assertTrue(result.contains("Test View"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试LayoutViewDTO的getLabel方法
     */
    @Test
    @DisplayName("正常场景 - 测试LayoutViewDTO的getLabel")
    void testLayoutViewDTO_GetLabel() {
        // Given
        String label = "Test View";
        String labelI18NKey = "test.view.label";
        LayoutViewResourceProxyImpl.LayoutViewDTO dto = 
                new LayoutViewResourceProxyImpl.LayoutViewDTO(
                        "test_view", label, labelI18NKey, true, false, "test_gray");

        // When
        String result = dto.getLabel();

        // Then
        assertNotNull(result);
        // 由于I18NExt.getOrDefault的行为，这里只验证返回值不为null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试基本功能验证
     */
    @Test
    @DisplayName("正常场景 - 基本功能验证")
    void testLayoutViewResourceProxyImpl_BasicFunctionality() {
        // Then
        assertNotNull(layoutViewResourceProxy);
        assertDoesNotThrow(() -> {
            layoutViewResourceProxy.toString();
        });
        assertDoesNotThrow(() -> {
            layoutViewResourceProxy.hashCode();
        });
        assertDoesNotThrow(() -> {
            layoutViewResourceProxy.equals(layoutViewResourceProxy);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试常量值
     */
    @Test
    @DisplayName("正常场景 - 测试常量值")
    void testConstants() {
        // Then
        assertEquals("udobj", LayoutViewResourceProxyImpl.UDOBJ);
        assertEquals("udobj_extra", LayoutViewResourceProxyImpl.UDOBJ_EXTRA);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找布局视图资源时灰度配置为false
     */
    @Test
    @DisplayName("边界场景 - 灰度配置为false")
    void testFindLayoutViewResource_GrayConfigFalse() {
        // Given
        String describeApiName = "test_object";

        try (MockedStatic<TenantUtil> tenantUtilMock = mockStatic(TenantUtil.class)) {
            tenantUtilMock.when(() -> TenantUtil.judgmentGrayByConfigKey(anyString(), anyString()))
                    .thenReturn(false);

            // When
            List<LayoutViewResourceProxyImpl.LayoutViewDTO> result = 
                    layoutViewResourceProxy.findLayoutViewResource(user, describeApiName);

            // Then
            assertNotNull(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找布局视图资源时不同租户ID
     */
    @Test
    @DisplayName("正常场景 - 不同租户ID")
    void testFindLayoutViewResource_DifferentTenantId() {
        // Given
        String describeApiName = "test_object";
        lenient().when(user.getTenantId()).thenReturn("78057");

        try (MockedStatic<TenantUtil> tenantUtilMock = mockStatic(TenantUtil.class)) {
            tenantUtilMock.when(() -> TenantUtil.judgmentGrayByConfigKey(anyString(), anyString()))
                    .thenReturn(true);

            // When
            List<LayoutViewResourceProxyImpl.LayoutViewDTO> result = 
                    layoutViewResourceProxy.findLayoutViewResource(user, describeApiName);

            // Then
            assertNotNull(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试loadConf方法的不同场景
     */
    @Test
    @DisplayName("测试loadConf方法 - 空配置")
    void testLoadConf_EmptyConfig() throws Exception {
        // Given
        LayoutViewResourceProxyImpl proxy = new LayoutViewResourceProxyImpl();
        Method loadConfMethod = LayoutViewResourceProxyImpl.class.getDeclaredMethod("loadConf", String.class);
        loadConfMethod.setAccessible(true);

        // When
        loadConfMethod.invoke(proxy, "[]");

        // Then
        List<LayoutViewResourceProxyImpl.LayoutViewDTO> result = proxy.findLayoutViewResource(user, "TestObj");
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试loadConf方法 - null配置
     */
    @Test
    @DisplayName("测试loadConf方法 - null配置")
    void testLoadConf_NullConfig() throws Exception {
        // Given
        LayoutViewResourceProxyImpl proxy = new LayoutViewResourceProxyImpl();
        Method loadConfMethod = LayoutViewResourceProxyImpl.class.getDeclaredMethod("loadConf", String.class);
        loadConfMethod.setAccessible(true);

        // When
        loadConfMethod.invoke(proxy, (String) null);

        // Then
        List<LayoutViewResourceProxyImpl.LayoutViewDTO> result = proxy.findLayoutViewResource(user, "TestObj");
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试LayoutViewDTO的getter方法
     */
    @Test
    @DisplayName("测试LayoutViewDTO的getter方法")
    void testLayoutViewDTO_Getters() {
        // Given
        String apiName = "test_api";
        String label = "Test Label";
        String labelI18NKey = "test.label";
        Boolean defaultShow = true;
        Boolean defaultView = false;
        String grayKey = "test_gray";

        // When
        LayoutViewResourceProxyImpl.LayoutViewDTO dto = new LayoutViewResourceProxyImpl.LayoutViewDTO(
                apiName, label, labelI18NKey, defaultShow, defaultView, grayKey);

        // Then
        assertEquals(apiName, dto.getApiName());
        assertEquals(label, dto.getLabel()); // 由于I18N可能返回原值
        assertEquals(labelI18NKey, dto.getLabelI18NKey());
        assertEquals(defaultShow, dto.getDefaultShow());
        assertEquals(defaultView, dto.getDefaultView());
        assertEquals(grayKey, dto.getGrayKey());
    }



    /**
     * GenerateByAI
     * 测试内容描述：测试findLayoutViewResource方法的边界情况
     */
    @Test
    @DisplayName("测试findLayoutViewResource - 边界情况")
    void testFindLayoutViewResource_EdgeCases() {
        // Given
        LayoutViewResourceProxyImpl proxy = new LayoutViewResourceProxyImpl();

        // When & Then
        // 测试null describeApiName
        List<LayoutViewResourceProxyImpl.LayoutViewDTO> result1 = proxy.findLayoutViewResource(user, null);
        assertNotNull(result1);

        // 测试空字符串 describeApiName
        List<LayoutViewResourceProxyImpl.LayoutViewDTO> result2 = proxy.findLayoutViewResource(user, "");
        assertNotNull(result2);

        // 测试不存在的 describeApiName
        List<LayoutViewResourceProxyImpl.LayoutViewDTO> result3 = proxy.findLayoutViewResource(user, "NonExistentObj");
        assertNotNull(result3);
    }



    /**
     * GenerateByAI
     * 测试内容描述：测试LayoutViewDTO构造函数的null值处理
     */
    @Test
    @DisplayName("测试LayoutViewDTO构造函数 - null值处理")
    void testLayoutViewDTO_NullValues() {
        // When
        LayoutViewResourceProxyImpl.LayoutViewDTO dto = new LayoutViewResourceProxyImpl.LayoutViewDTO(
                null, null, null, null, null, null);

        // Then
        assertNull(dto.getApiName());
        // I18NExt.getOrDefault可能返回null，所以不强制要求非null
        assertNull(dto.getLabelI18NKey());
        assertNull(dto.getDefaultShow());
        assertNull(dto.getDefaultView());
        assertNull(dto.getGrayKey());
    }
}
