# 1.创建插件
curl --request POST \
--url http://172.0.0.1/API/v1/inner/object/packagePlugin/service/createPlugin \
--header 'content-type: application/json' \
--header 'x-fs-ei: 90238' \
--header 'x-fs-userInfo: 1000' \
--data '{
"name": "插件-001",
"icon": "C_202406_27_e96db9f32f5042f18f06df7a6d4a6ef0",
"pluginApiName":"Plugin001",
"functionApiName":"OnlineDocWpsPersonal__c",
"pwcApiName":"component_PrD5M__c"
}'

# 2.获取所有插件
curl --request POST \
--url http://172.0.0.1/API/v1/inner/object/packagePlugin/service/getAllPlugin \
--header 'content-type: application/json' \
--header 'x-fs-ei: 90238' \
--header 'x-fs-userInfo: 1000' \
--data '{
"pageNumber": 0,
"pageSize": 20
}'

# n.标记在线文档插件为wps个人应用插件类型
curl --request POST \
--url http://172.0.0.1/API/v1/inner/object/packagePlugin/service/mark2WpsPersonalType \
--header 'content-type: application/json' \
--header 'x-fs-ei: 90238' \
--header 'x-fs-userInfo: 1000' \
--data '{
"pluginApiName":"Plugin001",
"pageSize": 20
}'