package com.facishare.paas.appframework.common.util;

import com.facishare.paas.appframework.core.exception.JsonSerializeError;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import com.google.common.base.Strings;

import java.io.IOException;
import java.lang.reflect.Type;

/**
 * create by z<PERSON><PERSON> on 2019/09/25
 */
@Slf4j
public class JacksonUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private static final ObjectMapper nonnullObjectMapper = new ObjectMapper();

    static {
        objectMapper.setDefaultPropertyInclusion(JsonInclude.Value.construct(JsonInclude.Include.NON_NULL, JsonInclude.Include.USE_DEFAULTS));
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.enable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN);

        nonnullObjectMapper.setDefaultPropertyInclusion(JsonInclude.Value.construct(JsonInclude.Include.NON_NULL, JsonInclude.Include.NON_NULL));
        nonnullObjectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        nonnullObjectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        nonnullObjectMapper.enable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN);
    }

    public static String toJson(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (IOException e) {
            log.warn("encode error,object:{}", object, e);
            throw new JsonSerializeError(e);
        }
    }

    public static String toJsonExcludeNullValue(Object object) {
        try {
            return nonnullObjectMapper.writeValueAsString(object);
        } catch (IOException e) {
            log.warn("toJsonExcludeNullValue error,object:{}", object, e);
            throw new JsonSerializeError(e);
        }
    }

    public static <T> T fromJson(String json, Class<T> clazz) {
        if (Strings.isNullOrEmpty(json)) {
            return null;
        }
        try {
            return objectMapper.readValue(json, clazz);
        } catch (IOException e) {
            log.warn("decode error,clazz:{},json:{}", clazz, json, e);
            throw new JsonSerializeError(e);
        }
    }

    public static <T> T fromJson(String json, TypeReference<T> typeReference) {
        if (Strings.isNullOrEmpty(json)) {
            return null;
        }
        try {
            return objectMapper.readValue(json, typeReference);
        } catch (IOException e) {
            log.warn("decode error,type:{},json:{}", typeReference.getType(), json, e);
            throw new JsonSerializeError(e);
        }
    }

    public static <T> T fromJson(String json, Class<?> rawClass, Class<?>... parameterClasses) {
        if (Strings.isNullOrEmpty(json)) {
            return null;
        }
        try {
            return objectMapper.readValue(json, objectMapper.getTypeFactory().constructParametricType(rawClass, parameterClasses));
        } catch (IOException e) {
            log.warn("decode error,rawClass:{},parameterClasses:{},json:{}", rawClass, parameterClasses, json, e);
            throw new JsonSerializeError(e);
        }
    }

    public static JsonNode readTree(String json) {
        if (Strings.isNullOrEmpty(json)) {
            return null;
        }
        try {
            return objectMapper.readTree(json);
        } catch (IOException e) {
            log.warn("readTree error, content:{}", json, e);
        }
        return null;
    }

    public static <T> T convertValue(Object fromValue, Class<T> clazz) {
        return objectMapper.convertValue(fromValue, clazz);
    }

    public static <T> TypeReference<T> getTypeReference(Type type) {
        return new TypeReference<T>() {
            @Override
            public Type getType() {
                return type;
            }
        };
    }

    @Data
    static class Arg {
        private String id;
        private String name;
    }
}
