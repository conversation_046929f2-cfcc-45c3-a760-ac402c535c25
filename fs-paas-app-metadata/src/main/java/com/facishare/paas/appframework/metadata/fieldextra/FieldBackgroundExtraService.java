package com.facishare.paas.appframework.metadata.fieldextra;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.repository.model.MtFieldBackgroundExtra;

import java.util.List;

public interface FieldBackgroundExtraService {

    List<MtFieldBackgroundExtra> findAll(User user, String describeApiName);

    List<MtFieldBackgroundExtra> find(User user, String describeApiName, List<String> fieldApiNames);

    void upsert(User user, MtFieldBackgroundExtra fieldBackgroundExtra);

    void bulkUpsert(User user, List<MtFieldBackgroundExtra> fieldBackgroundExtras);

    void delete(User user, String describeApiName, List<String> fieldApiNames);

    void bulkDelete(User user, String describeApiName);

}
