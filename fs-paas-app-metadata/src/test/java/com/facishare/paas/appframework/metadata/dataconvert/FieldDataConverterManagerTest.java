package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.metadata.api.describe.IFieldType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试FieldDataConverterManager类的转换器管理功能
 */
@ExtendWith(MockitoExtension.class)
class FieldDataConverterManagerTest {

  @Mock
  private ApplicationContext applicationContext;

  @Mock
  private FieldDataConverterAdapter fieldDataConverterAdapter;

  @InjectMocks
  private FieldDataConverterManager manager;

  private TestFieldDataConverter testConverter1;
  private TestFieldDataConverter testConverter2;

  @BeforeEach
  void setUp() {
    testConverter1 = new TestFieldDataConverter(Arrays.asList(IFieldType.TEXT, IFieldType.LONG_TEXT));
    testConverter2 = new TestFieldDataConverter(Arrays.asList(IFieldType.NUMBER, IFieldType.CURRENCY));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setApplicationContext方法正常初始化转换器映射
   */
  @Test
  @DisplayName("测试setApplicationContext - 正常初始化转换器映射")
  void testSetApplicationContext_NormalInitialization() {
    // 准备测试数据
    Map<String, FieldDataConverter> springMap = new HashMap<>();
    springMap.put("converter1", testConverter1);
    springMap.put("converter2", testConverter2);

    when(applicationContext.getBeansOfType(FieldDataConverter.class)).thenReturn(springMap);

    // 执行被测试方法
    manager.setApplicationContext(applicationContext);

    // 验证结果
    assertEquals(testConverter1, manager.getFieldDataConverter(IFieldType.TEXT));
    assertEquals(testConverter1, manager.getFieldDataConverter(IFieldType.LONG_TEXT));
    assertEquals(testConverter2, manager.getFieldDataConverter(IFieldType.NUMBER));
    assertEquals(testConverter2, manager.getFieldDataConverter(IFieldType.CURRENCY));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setApplicationContext方法处理空的转换器映射
   */
  @Test
  @DisplayName("测试setApplicationContext - 空的转换器映射")
  void testSetApplicationContext_EmptyConverterMap() {
    // 准备测试数据
    Map<String, FieldDataConverter> emptyMap = new HashMap<>();
    when(applicationContext.getBeansOfType(FieldDataConverter.class)).thenReturn(emptyMap);

    // 执行被测试方法
    manager.setApplicationContext(applicationContext);

    // 验证结果 - 应该返回默认的适配器
    assertEquals(fieldDataConverterAdapter, manager.getFieldDataConverter(IFieldType.TEXT));
    assertEquals(fieldDataConverterAdapter, manager.getFieldDataConverter(IFieldType.NUMBER));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setApplicationContext方法处理null的转换器映射
   */
  @Test
  @DisplayName("测试setApplicationContext - null转换器映射")
  void testSetApplicationContext_NullConverterMap() {
    // 准备测试数据
    when(applicationContext.getBeansOfType(FieldDataConverter.class)).thenReturn(null);

    // 执行被测试方法
    manager.setApplicationContext(applicationContext);

    // 验证结果 - 应该返回默认的适配器
    assertEquals(fieldDataConverterAdapter, manager.getFieldDataConverter(IFieldType.TEXT));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setApplicationContext方法处理BeansException异常
   */
  @Test
  @DisplayName("测试setApplicationContext - BeansException异常处理")
  void testSetApplicationContext_BeansException() {
    // 准备测试数据 - 抛出BeansException而不是RuntimeException
    when(applicationContext.getBeansOfType(FieldDataConverter.class))
        .thenThrow(new org.springframework.beans.BeansException("Bean creation failed") {});

    // 执行被测试方法 - 不应该抛出异常
    assertDoesNotThrow(() -> manager.setApplicationContext(applicationContext));

    // 验证结果 - 应该返回默认的适配器
    assertEquals(fieldDataConverterAdapter, manager.getFieldDataConverter(IFieldType.TEXT));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFieldDataConverter方法返回已注册的转换器
   */
  @Test
  @DisplayName("测试getFieldDataConverter - 返回已注册的转换器")
  void testGetFieldDataConverter_RegisteredConverter() {
    // 准备测试数据
    Map<String, FieldDataConverter> springMap = new HashMap<>();
    springMap.put("converter1", testConverter1);
    when(applicationContext.getBeansOfType(FieldDataConverter.class)).thenReturn(springMap);

    // 初始化管理器
    manager.setApplicationContext(applicationContext);

    // 执行被测试方法
    FieldDataConverter result = manager.getFieldDataConverter(IFieldType.TEXT);

    // 验证结果
    assertEquals(testConverter1, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFieldDataConverter方法返回默认适配器
   */
  @Test
  @DisplayName("测试getFieldDataConverter - 返回默认适配器")
  void testGetFieldDataConverter_DefaultAdapter() {
    // 准备测试数据 - 不初始化任何转换器

    // 执行被测试方法
    FieldDataConverter result = manager.getFieldDataConverter("unknown_field_type");

    // 验证结果
    assertEquals(fieldDataConverterAdapter, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试转换器支持多个字段类型的映射
   */
  @Test
  @DisplayName("测试转换器支持多个字段类型的映射")
  void testMultipleFieldTypesMapping() {
    // 准备测试数据
    Map<String, FieldDataConverter> springMap = new HashMap<>();
    springMap.put("multiConverter", testConverter1);
    when(applicationContext.getBeansOfType(FieldDataConverter.class)).thenReturn(springMap);

    // 初始化管理器
    manager.setApplicationContext(applicationContext);

    // 验证结果 - 同一个转换器应该支持多个字段类型
    assertEquals(testConverter1, manager.getFieldDataConverter(IFieldType.TEXT));
    assertEquals(testConverter1, manager.getFieldDataConverter(IFieldType.LONG_TEXT));
    assertSame(manager.getFieldDataConverter(IFieldType.TEXT), 
               manager.getFieldDataConverter(IFieldType.LONG_TEXT));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试后注册的转换器覆盖先注册的转换器
   */
  @Test
  @DisplayName("测试后注册的转换器覆盖先注册的转换器")
  void testConverterOverride() {
    // 准备测试数据 - 两个转换器都支持TEXT类型
    TestFieldDataConverter overrideConverter = new TestFieldDataConverter(Arrays.asList(IFieldType.TEXT));
    
    Map<String, FieldDataConverter> springMap = new HashMap<>();
    springMap.put("converter1", testConverter1); // 支持TEXT
    springMap.put("overrideConverter", overrideConverter); // 也支持TEXT
    
    when(applicationContext.getBeansOfType(FieldDataConverter.class)).thenReturn(springMap);

    // 初始化管理器
    manager.setApplicationContext(applicationContext);

    // 验证结果 - 应该是后注册的转换器（具体哪个取决于Map的迭代顺序）
    FieldDataConverter result = manager.getFieldDataConverter(IFieldType.TEXT);
    assertTrue(result == testConverter1 || result == overrideConverter);
  }

  /**
   * 测试用的FieldDataConverter实现类
   */
  private static class TestFieldDataConverter implements FieldDataConverter {
    private final List<String> supportedTypes;

    public TestFieldDataConverter(List<String> supportedTypes) {
      this.supportedTypes = supportedTypes;
    }

    @Override
    public List<String> getSupportedFieldTypes() {
      return supportedTypes;
    }

    @Override
    public String convertFieldData(com.facishare.paas.metadata.api.IObjectData objectData, 
                                   com.facishare.paas.metadata.api.describe.IFieldDescribe fieldDescribe, 
                                   DataConvertContext context) {
      return "test_converted_value";
    }
  }
}
