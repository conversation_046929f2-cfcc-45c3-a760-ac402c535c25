package com.facishare.paas.appframework.core.predef.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.UdobjGrayKey;
import com.facishare.paas.appframework.common.util.UdobjGrayUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.predef.controller.StandardListController.Arg;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.SecurityEventTrackingDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.trace.TraceContext;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 标准列表页接口
 * <p>
 * Created by liyiguang on 2017/6/17.
 */
public class StandardListController extends AbstractStandardListController<Arg> {

    /**
     * 放到这里是为了兼容
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg {
        @JSONField(name = "M1")
        @JsonProperty("include_layout")
        @SerializedName("include_layout")
        boolean includeLayout = true;

        @JSONField(name = "M2")
        boolean custom;

        @JSONField(name = "M3")
        @JsonProperty("search_query_info")
        @SerializedName("search_query_info")
        String searchQueryInfo;

        @JSONField(name = "M4")
        @JsonProperty("object_describe_api_name")
        @SerializedName("object_describe_api_name")
        String objectDescribeApiName;

        @JSONField(name = "M5")
        @JsonProperty("search_template_id")
        @SerializedName("search_template_id")
        String searchTemplateId;

        /**
         * 用于辅助查询默认场景的拓展信息
         */
        @JSONField(name = "params_of_default_template")
        @JsonProperty("params_of_default_template")
        @SerializedName("params_of_default_template")
        ParamsOfDefaultTemplate paramsOfDefaultTemplate;

        // 返回值是否包含组织机构信息
        @JSONField(name = "M6")
        @JsonProperty("include_org_info")
        @SerializedName("include_org_info")
        Boolean includeOrgInfo = false;

        @JSONField(name = "M7")
        @JsonProperty("keyword")
        @SerializedName("keyword")
        String keyword;

        @JSONField(name = "M8")
        Map<String, Integer> describeVersionMap;

        @JSONField(name = "M9")
        @JsonProperty("id_list")
        @SerializedName("id_list")
        List<String> idList;

        @JSONField(name = "M10")
        @JsonProperty("field_projection")
        @SerializedName("field_projection")
        List<String> fieldProjection;

        @JSONField(name = "M11")
        @JsonProperty("ignore_scene_filter")
        @SerializedName("ignore_scene_filter")
        Boolean isIgnoreSceneFilter;

        @JSONField(name = "M12")
        @JsonProperty("find_explicit_total_num")
        @SerializedName("find_explicit_total_num")
        Boolean findExplicitTotalNum;

        @JSONField(name = "M13")
        @JsonProperty("replace_option_value")
        @SerializedName("replace_option_value")
        boolean replaceOptionValue;

        @JsonProperty("extract_time_format_value")
        @SerializedName("extract_time_format_value")
        Boolean extractTimeFormatValue;

        @JSONField(name = "M14")
        @JsonProperty("check_permission")
        @SerializedName("check_permission")
        boolean checkPermission = false;

        @JSONField(name = "M15")
        @JsonProperty("get_data_only")
        @SerializedName("get_data_only")
        boolean getDataOnly = false;

        @JSONField(name = "M16")
        @JsonProperty("include_invalid_data")
        @SerializedName("include_invalid_data")
        boolean includeInvalidData = false;

        @JSONField(name = "M20")
        @JsonProperty("include_describe")
        @SerializedName("include_describe")
        boolean includeDescribe = true;

        @JSONField(name = "M21")
        @JsonProperty("include_button")
        @SerializedName("include_button")
        boolean includeButtonInfo = true;

        @JSONField(name = "M22")
        @JsonProperty("search_template_type")
        @SerializedName("search_template_type")
        String searchTemplateType;

        @JSONField(name = "M23")
        @JsonProperty("tag_operator")
        @SerializedName("tag_operator")
        String tagOperator;

        @JSONField(name = "M24")
        @JsonProperty("tags")
        @SerializedName("tags")
        List<String> tags;

        @JSONField(name = "template_api_name")
        @JsonProperty("template_api_name")
        @SerializedName("template_api_name")
        String templateApiName;

        @JSONField(name = "extend_attribute")
        @JsonProperty("extend_attribute")
        @SerializedName("extend_attribute")
        // 老对象列表迁移支持，公海对象查询时一部分数据是客户数据，增加
        // 标识区分
        private String extendAttribute;

        @JSONField(name = "ignore_scene_record_type")
        @JsonProperty("ignore_scene_record_type")
        @SerializedName("ignore_scene_record_type")
        boolean isIgnoreSceneRecordType;

        @JSONField(name = "need_return_count_num")
        @JsonProperty("need_return_count_num")
        @SerializedName("need_return_count_num")
        boolean needReturnCountNum = true;

        @JSONField(name = "filter_product_ids")
        @JsonProperty("filter_product_ids")
        @SerializedName("filter_product_ids")
        private List<String> filterProductIds;

        /**
         * mobile 终端布局
         * web 网页端布局
         */
        @JSONField(name = "need_tag")
        @JsonProperty("need_tag")
        @SerializedName("need_tag")
        private boolean needTag;

        @JSONField(name = "need_summary_data")
        @JsonProperty("need_summary_data")
        @SerializedName("need_summary_data")
        private Boolean needSummaryData;

        @JSONField(name = "list_component")
        @JsonProperty("list_component")
        @SerializedName("list_component")
        private Map<String, Object> listComponent;

        /**
         * 指定列表页布局 apiName
         */
        @JSONField(name = "list_layout_api_name")
        @JsonProperty("list_layout_api_name")
        @SerializedName("list_layout_api_name")
        private String listLayoutApiName;

        /**
         * 查询富文本
         */
        @JSONField(name = "search_rich_text_extra")
        @JsonProperty("search_rich_text_extra")
        @SerializedName("search_rich_text_extra")
        private Boolean searchRichTextExtra;

        // 是否序列化返回对象中的空值
        private Boolean serializeEmpty;
        // 是否将人员部门字段的扩展信息提取到外层
        private Boolean extractExtendInfo;

        @JSONField(name = "record_type_mapping")
        @JsonProperty("record_type_mapping")
        @SerializedName("record_type_mapping")
        private Boolean recordTypeMapping;

        /**
         * 拓展参数
         */
        @JSONField(name = "extra_params")
        @JsonProperty("extra_params")
        @SerializedName("extra_params")
        private Map<String, Object> extraParams;

        private List<Map<String, Object>> businessObjects;

        public boolean serializeEmpty() {
            return !Boolean.FALSE.equals(serializeEmpty);
        }

        public boolean extractExtendInfo() {
            return Boolean.TRUE.equals(extractExtendInfo);
        }

        public Arg copy2Plugin() {
            Arg result = new Arg();
            result.setSearchQueryInfo(searchQueryInfo);
            result.setObjectDescribeApiName(objectDescribeApiName);
            return result;
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        // 列表事件安全埋点
        try {
            if (RequestUtil.isCepRequest() && UdobjGrayUtil.isObjectAndTenantGray(UdobjGrayKey.SECURITY_INCIDENTS_LIST,
                    controllerContext.getTenantId(), controllerContext.getObjectApiName())) {
                SecurityEventTrackingDTO trackingDTO = SecurityEventTrackingDTO.builder()
                        .eventId(UUID.randomUUID().toString())
                        .traceId(TraceContext.get().getTraceId())
                        .eventTime(System.currentTimeMillis())
                        .tenantId(controllerContext.getTenantId())
                        .userId(controllerContext.getUser().getUserId())
                        .sourceIp(Objects.isNull(RequestContextManager.getContext()) ? "" : RequestContextManager.getContext().getClientIp())
                        .deviceId(RequestUtil.getCustomHeader(RequestContext.FS_DEVICE_ID))
                        .deviceType(RequestUtil.getCustomHeader(RequestContext.FS_DEVICE_TYPE))
                        .objects(controllerContext.getObjectApiName())
                        .operation("ListView")
                        .actionType("UIListViewEvent")
                        .parameters(JSON.toJSONString(arg))
                        .query(query.toJsonString())
                        .records(CollectionUtil.emptyIfNull(result.getDataList()).stream()
                                .map(ObjectDataDocument::getId).collect(Collectors.toList()))
                        .build();
                BizLogClient.send("biz-log-security-event-tracking", Pojo2Protobuf.toMessage(trackingDTO, com.fxiaoke.log.SecurityEventTrackingDTO.class).toByteArray());
            }
        } catch (Exception e) {
            log.warn("send message error ,exception: ", e);
        }
        return super.after(arg, result);
    }

    @Data
    public static class ParamsOfDefaultTemplate {
        @JSONField(name = "extend_attribute")
        @JsonProperty("extend_attribute")
        @SerializedName("extend_attribute")
        private String extendAttribute;

        @JSONField(name = "thirdapp_record_type")
        @JsonProperty("thirdapp_record_type")
        @SerializedName("thirdapp_record_type")
        private String thirdRecordType;
    }
}
