package com.facishare.paas.appframework.metadata.publicobject.module;

import com.facishare.paas.appframework.metadata.repository.model.MtPublicObjectJobDetail;
import com.google.common.base.Converter;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/17
 */
public class PublicObjectJobDetailConverter extends Converter<PublicObjectJobDetailInfo, MtPublicObjectJobDetail> {
    public static PublicObjectJobDetailConverter getInstance() {
        return SingletonHolder.INSTANCE;
    }

    @Override
    protected MtPublicObjectJobDetail doForward(PublicObjectJobDetailInfo jobDetailInfo) {
        MtPublicObjectJobDetail result = new MtPublicObjectJobDetail();
        result.setId(jobDetailInfo.getId());
        result.setObjectApiName(jobDetailInfo.getObjectApiName());
        result.setJobId(jobDetailInfo.getPublicObjectJobId());
        result.setStatus(jobDetailInfo.getStatus());
        result.setResult(jobDetailInfo.getJobResult().toJson());
        return result;
    }

    @Override
    protected PublicObjectJobDetailInfo doBackward(MtPublicObjectJobDetail jobDetail) {
        PublicObjectJobDetailInfo result = new PublicObjectJobDetailInfo();
        result.setId(jobDetail.getId());
        result.setObjectApiName(jobDetail.getObjectApiName());
        result.setDownstreamTenantId(jobDetail.getDownstreamTenantId());
        result.setPublicObjectJobId(jobDetail.getJobId());
        result.setStatus(jobDetail.getStatus());
        result.setJobResult(PublicObjectJobDetailResultInfo.fromJson(jobDetail.getResult()));
        result.setLastModifiedBy(jobDetail.getLastModifiedBy());
        return result;
    }

    private static class SingletonHolder {
        private static final PublicObjectJobDetailConverter INSTANCE = new PublicObjectJobDetailConverter();
    }
}
