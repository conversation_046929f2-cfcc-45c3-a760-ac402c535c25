package com.facishare.paas.appframework.metadata.options.bo;

import com.facishare.paas.appframework.metadata.repository.model.MtFieldDependence;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/7/29
 */
@Data
public class OptionDependence {
    private final String value;
    private final List<String> childOptions;

    public OptionDependence(String value, List<String> childOptions) {
        this.value = value;
        this.childOptions = childOptions;
    }

    public static OptionDependence from(MtFieldDependence.FieldDependence fieldDependence) {
        if (Objects.isNull(fieldDependence)) {
            return null;
        }
        return new OptionDependence(fieldDependence.getCode(), fieldDependence.getChildCodes());
    }

    public MtFieldDependence.FieldDependence convert() {
        MtFieldDependence.FieldDependence fieldDependence = new MtFieldDependence.FieldDependence();
        fieldDependence.setCode(value);
        fieldDependence.setChildCodes(childOptions);
        return fieldDependence;
    }
}
