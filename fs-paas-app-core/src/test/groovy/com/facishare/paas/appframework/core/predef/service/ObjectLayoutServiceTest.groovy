package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.predef.service.dto.layout.FindLayoutsByDescribeApiName
import com.facishare.paas.appframework.core.predef.service.dto.layout.FindLayoutById
import com.facishare.paas.appframework.core.predef.service.dto.layout.CreateOrUpdateLayout
import com.facishare.paas.appframework.core.predef.service.dto.layout.DeleteLayout
import com.facishare.paas.appframework.core.predef.service.dto.layout.CopyLayout
import com.facishare.paas.appframework.core.predef.service.dto.layout.SetDefaultLayout
import com.facishare.paas.appframework.metadata.DescribeLogicService
import com.facishare.paas.appframework.metadata.LayoutLogicService
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.rest.api.model.newmetadataothers.FindLayoutById
import spock.lang.Specification

/**
 * GenerateByAI
 * 测试内容描述：ObjectLayoutService对象布局服务的单元测试
 */
class ObjectLayoutServiceTest extends Specification {

    def service = new ObjectLayoutService()
    def mockLayoutLogicService = Mock(LayoutLogicService)
    def mockDescribeLogicService = Mock(DescribeLogicService)
    def mockUser = Mock(User)
    def mockLayoutDescribe = Mock(ILayoutDescribe)
    def mockObjectDescribe = Mock(IObjectDescribe)
    def serviceContext = new ServiceContext()

    def setup() {
        service.layoutLogicService = mockLayoutLogicService
        service.describeLogicService = mockDescribeLogicService

        serviceContext.tenantId = "test-tenant"
        serviceContext.user = mockUser

        mockUser.userId >> "test-user"
        mockUser.tenantId >> "test-tenant"

        mockLayoutDescribe.id >> "layout-1"
        mockLayoutDescribe.name >> "Test Layout"
        mockLayoutDescribe.describeApiName >> "TestObject"

        mockObjectDescribe.apiName >> "TestObject"
        mockObjectDescribe.displayName >> "Test Object"
    }

    def "test findLayoutsByDescribeApiName with valid arguments"() {
        given: "准备查询参数"
        def arg =  FindLayoutsByDescribeApiName
        arg.describeApiName = "TestObject"

        and: "Mock服务调用"
        mockLayoutLogicService.findLayoutsByDescribeApiName("test-tenant", "TestObject") >> [mockLayoutDescribe]

        when: "根据对象API名称查找布局"
        def result = service.findLayoutsByDescribeApiName(arg, serviceContext)

        then: "应该返回正确结果"
        result != null
        result.layouts != null
        1 * mockLayoutLogicService.findLayoutsByDescribeApiName("test-tenant", "TestObject")
    }

    def "test findLayoutsByDescribeApiName with empty result"() {
        given: "准备查询参数"
        def arg = new Object()
        arg.describeApiName = "NonExistentObject"

        and: "Mock服务调用返回空列表"
        mockLayoutLogicService.findLayoutsByDescribeApiName("test-tenant", "NonExistentObject") >> []

        when: "根据对象API名称查找布局"
        def result = service.findLayoutsByDescribeApiName(arg, serviceContext)

        then: "应该返回空结果"
        result != null
        result.layouts == null || result.layouts.isEmpty()
    }

    def "test findLayoutById with valid arguments"() {
        given: "准备查询参数"
        def arg = new FindLayoutById.FindLayoutByIdResultDetail()
        arg.layoutId = "layout-1"

        and: "Mock服务调用"
        mockLayoutLogicService.findLayoutById("test-tenant", "layout-1") >> mockLayoutDescribe

        when: "根据ID查找布局"
        def result = service.findLayoutById(arg, serviceContext)

        then: "应该返回正确结果"
        result != null
        result.layout != null
        1 * mockLayoutLogicService.findLayoutById("test-tenant", "layout-1")
    }

    def "test findLayoutById with non-existent layout"() {
        given: "准备查询参数"
        def arg = new FindLayoutById.Arg()
        arg.layoutId = "non-existent-layout"

        and: "Mock服务调用返回null"
        mockLayoutLogicService.findLayoutById("test-tenant", "non-existent-layout") >> null

        when: "根据ID查找布局"
        def result = service.findLayoutById(arg, serviceContext)

        then: "应该返回空结果"
        result != null
        result.layout == null
    }

    def "test createLayout with valid arguments"() {
        given: "准备创建参数"
        def arg = new CreateOrUpdateLayout.Arg()
        arg.name = "New Layout"
        arg.describeApiName = "TestObject"
        arg.layoutType = "detail"
        arg.content = '{"sections":[]}'

        and: "Mock服务调用"
        mockDescribeLogicService.findObject("test-tenant", "TestObject") >> mockObjectDescribe
        mockLayoutLogicService.createLayout(_, _) >> mockLayoutDescribe

        when: "创建布局"
        def result = service.createLayout(arg, serviceContext)

        then: "应该返回正确结果"
        result != null
        result.layout != null
        1 * mockDescribeLogicService.findObject("test-tenant", "TestObject")
        1 * mockLayoutLogicService.createLayout(_, _)
    }

    def "test createLayout with invalid describeApiName"() {
        given: "准备无效参数"
        def arg = new CreateOrUpdateLayout.Arg()
        arg.name = "New Layout"
        arg.describeApiName = "InvalidObject"
        arg.layoutType = "detail"

        and: "Mock服务调用返回null"
        mockDescribeLogicService.findObject("test-tenant", "InvalidObject") >> null

        when: "创建布局"
        service.createLayout(arg, serviceContext)

        then: "应该抛出ValidateException"
        thrown(com.facishare.paas.appframework.core.exception.ValidateException)
    }

    def "test createLayout with blank name"() {
        given: "准备空白名称参数"
        def arg = new CreateOrUpdateLayout.Arg()
        arg.name = ""
        arg.describeApiName = "TestObject"
        arg.layoutType = "detail"

        when: "创建布局"
        service.createLayout(arg, serviceContext)

        then: "应该抛出ValidateException"
        thrown(com.facishare.paas.appframework.core.exception.ValidateException)
    }

    def "test createLayout with long name"() {
        given: "准备过长名称参数"
        def arg = new CreateOrUpdateLayout.Arg()
        arg.name = "This is a very long layout name that exceeds the maximum allowed length"
        arg.describeApiName = "TestObject"
        arg.layoutType = "detail"

        when: "创建布局"
        service.createLayout(arg, serviceContext)

        then: "应该抛出ValidateException"
        thrown(com.facishare.paas.appframework.core.exception.ValidateException)
    }

    def "test updateLayout with valid arguments"() {
        given: "准备更新参数"
        def arg = new CreateOrUpdateLayout.Arg()
        arg.layoutId = "layout-1"
        arg.name = "Updated Layout"
        arg.describeApiName = "TestObject"
        arg.layoutType = "detail"
        arg.content = '{"sections":[{"title":"Updated Section"}]}'

        and: "Mock服务调用"
        mockLayoutLogicService.findLayoutById("test-tenant", "layout-1") >> mockLayoutDescribe
        mockLayoutLogicService.updateLayout(_, _) >> mockLayoutDescribe

        when: "更新布局"
        def result = service.updateLayout(arg, serviceContext)

        then: "应该返回正确结果"
        result != null
        result.layout != null
        1 * mockLayoutLogicService.findLayoutById("test-tenant", "layout-1")
        1 * mockLayoutLogicService.updateLayout(_, _)
    }

    def "test updateLayout with non-existent layout"() {
        given: "准备更新参数"
        def arg = new CreateOrUpdateLayout.Arg()
        arg.layoutId = "non-existent-layout"
        arg.name = "Updated Layout"

        and: "Mock服务调用返回null"
        mockLayoutLogicService.findLayoutById("test-tenant", "non-existent-layout") >> null

        when: "更新布局"
        service.updateLayout(arg, serviceContext)

        then: "应该抛出ValidateException"
        thrown(com.facishare.paas.appframework.core.exception.ValidateException)
    }

    def "test deleteLayout with valid arguments"() {
        given: "准备删除参数"
        def arg = new DeleteLayout.Arg()
        arg.layoutId = "layout-1"

        and: "Mock服务调用"
        mockLayoutLogicService.findLayoutById("test-tenant", "layout-1") >> mockLayoutDescribe

        when: "删除布局"
        def result = service.deleteLayout(arg, serviceContext)

        then: "应该返回正确结果"
        result != null
        result.success == true
        1 * mockLayoutLogicService.findLayoutById("test-tenant", "layout-1")
        1 * mockLayoutLogicService.deleteLayout("test-tenant", "layout-1", mockUser)
    }

    def "test deleteLayout with non-existent layout"() {
        given: "准备删除参数"
        def arg = new DeleteLayout.Arg()
        arg.layoutId = "non-existent-layout"

        and: "Mock服务调用返回null"
        mockLayoutLogicService.findLayoutById("test-tenant", "non-existent-layout") >> null

        when: "删除布局"
        service.deleteLayout(arg, serviceContext)

        then: "应该抛出ValidateException"
        thrown(com.facishare.paas.appframework.core.exception.ValidateException)
    }

    def "test copyLayout with valid arguments"() {
        given: "准备复制参数"
        def arg = new CopyLayout.Arg()
        arg.layoutId = "layout-1"
        arg.newLayoutName = "Copied Layout"

        and: "Mock服务调用"
        mockLayoutLogicService.findLayoutById("test-tenant", "layout-1") >> mockLayoutDescribe
        mockLayoutLogicService.copyLayout(_, _, _) >> mockLayoutDescribe

        when: "复制布局"
        def result = service.copyLayout(arg, serviceContext)

        then: "应该返回正确结果"
        result != null
        result.layout != null
        1 * mockLayoutLogicService.findLayoutById("test-tenant", "layout-1")
        1 * mockLayoutLogicService.copyLayout("test-tenant", "layout-1", "Copied Layout")
    }

    def "test copyLayout with non-existent layout"() {
        given: "准备复制参数"
        def arg = new CopyLayout.Arg()
        arg.layoutId = "non-existent-layout"
        arg.newLayoutName = "Copied Layout"

        and: "Mock服务调用返回null"
        mockLayoutLogicService.findLayoutById("test-tenant", "non-existent-layout") >> null

        when: "复制布局"
        service.copyLayout(arg, serviceContext)

        then: "应该抛出ValidateException"
        thrown(com.facishare.paas.appframework.core.exception.ValidateException)
    }

    def "test setDefaultLayout with valid arguments"() {
        given: "准备设置默认布局参数"
        def arg = new SetDefaultLayout.Arg()
        arg.layoutId = "layout-1"
        arg.describeApiName = "TestObject"
        arg.layoutType = "detail"

        and: "Mock服务调用"
        mockLayoutLogicService.findLayoutById("test-tenant", "layout-1") >> mockLayoutDescribe

        when: "设置默认布局"
        def result = service.setDefaultLayout(arg, serviceContext)

        then: "应该返回正确结果"
        result != null
        result.success == true
        1 * mockLayoutLogicService.findLayoutById("test-tenant", "layout-1")
        1 * mockLayoutLogicService.setDefaultLayout("test-tenant", "TestObject", "detail", "layout-1")
    }

    def "test service with null serviceContext"() {
        given: "准备参数"
        def arg = new FindLayoutsByDescribeApiName.Arg()
        arg.describeApiName = "TestObject"

        when: "使用null context查找布局"
        service.findLayoutsByDescribeApiName(arg, null)

        then: "应该抛出异常"
        thrown(NullPointerException)
    }

    def "test service with null arguments"() {
        when: "使用null参数查找布局"
        service.findLayoutsByDescribeApiName(null, serviceContext)

        then: "应该抛出异常"
        thrown(Exception)
    }

    def "test createLayout with different layout types"() {
        given: "准备不同布局类型的参数"
        def arg = new CreateOrUpdateLayout.Arg()
        arg.name = "Test Layout"
        arg.describeApiName = "TestObject"
        arg.layoutType = layoutType
        arg.content = '{"sections":[]}'

        and: "Mock服务调用"
        mockDescribeLogicService.findObject("test-tenant", "TestObject") >> mockObjectDescribe
        mockLayoutLogicService.createLayout(_, _) >> mockLayoutDescribe

        when: "创建不同类型的布局"
        def result = service.createLayout(arg, serviceContext)

        then: "应该返回正确结果"
        result != null
        result.layout != null

        where:
        layoutType << ["detail", "list", "edit", "create"]
    }
}
