package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试TextFieldDataConverter类的文本字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class TextFieldDataConverterTest {

  @Mock
  private IObjectDataProxyService dataService;

  @Mock
  private SessionContext sessionContext;

  private TextFieldDataConverter converter;
  private IFieldDescribe fieldDescribe;
  private IObjectData objectData;

  @BeforeEach
  void setUp() {
    converter = new TextFieldDataConverter();
    fieldDescribe = createTextFieldDescribe("text_field");
    objectData = createObjectData("text_field", "test_value");
    
    // 设置转换器的属性
    converter.setFieldDescribe(fieldDescribe);
    converter.setObjectData(objectData);
    converter.setDataService(dataService);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法正常转换文本值
   */
  @Test
  @DisplayName("测试convertFieldData - 正常文本转换")
  void testConvertFieldData_NormalTextConversion() throws Exception {
    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("test_value", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理null值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null值")
  void testConvertFieldData_HandlesNullValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("text_field", null);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertNull(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理空字符串
   */
  @Test
  @DisplayName("测试convertFieldData - 处理空字符串")
  void testConvertFieldData_HandlesEmptyString() throws Exception {
    // 准备测试数据
    objectData = createObjectData("text_field", "");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理长文本
   */
  @Test
  @DisplayName("测试convertFieldData - 处理长文本")
  void testConvertFieldData_HandlesLongText() throws Exception {
    // 准备测试数据
    String longText = "这是一个很长的文本内容，包含了多种字符：中文、English、数字123、特殊符号!@#$%^&*()";
    objectData = createObjectData("text_field", longText);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals(longText, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理包含换行符的文本
   */
  @Test
  @DisplayName("测试convertFieldData - 处理包含换行符的文本")
  void testConvertFieldData_HandlesTextWithNewlines() throws Exception {
    // 准备测试数据
    String textWithNewlines = "第一行\n第二行\r\n第三行\t制表符";
    objectData = createObjectData("text_field", textWithNewlines);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals(textWithNewlines, result);
  }

  /**
   * 提供不同类型的文本值测试数据
   */
  private static Stream<Arguments> provideTextValues() {
    return Stream.of(
        Arguments.of("普通文本", "普通文本"),
        Arguments.of("123456", "123456"),
        Arguments.of("special@#$%", "special@#$%"),
        Arguments.of("   空格文本   ", "   空格文本   "),
        Arguments.of("单个字", "单个字"),
        Arguments.of("Mixed中英文123", "Mixed中英文123")
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理不同类型的文本值
   */
  @ParameterizedTest
  @MethodSource("provideTextValues")
  @DisplayName("测试convertFieldData - 不同类型的文本值")
  void testConvertFieldData_DifferentTextValues(String inputValue, String expectedResult) throws Exception {
    // 准备测试数据
    objectData = createObjectData("text_field", inputValue);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals(expectedResult, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理字段不存在的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 字段不存在")
  void testConvertFieldData_FieldNotExists() throws Exception {
    // 准备测试数据 - 创建不包含目标字段的数据
    objectData = createObjectData("other_field", "other_value");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 字段不存在时应该返回null
    assertNull(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理非字符串类型的值
   */
  @Test
  @DisplayName("测试convertFieldData - 非字符串类型值的类型转换")
  void testConvertFieldData_NonStringValueTypeConversion() throws Exception {
    // 准备测试数据 - 存储Integer类型的值
    objectData = createObjectData("text_field", 12345);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - ObjectData.get(String, String.class)会进行类型转换
    assertEquals("12345", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试转换器的基本属性设置
   */
  @Test
  @DisplayName("测试转换器属性设置")
  void testConverterProperties() {
    // 验证属性设置
    assertEquals(fieldDescribe, converter.getFieldDescribe());
    assertEquals(objectData, converter.getObjectData());
    assertEquals(dataService, converter.getDataService());
  }

  /**
   * 创建测试用的文本字段描述
   */
  private IFieldDescribe createTextFieldDescribe(String apiName) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试文本字段");
    fieldMap.put("type", IFieldType.TEXT);
    return FieldDescribeFactory.newInstance(fieldMap);
  }

  /**
   * 创建测试用的对象数据
   */
  private IObjectData createObjectData(String fieldName, Object value) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldName, value);
    return new ObjectData(dataMap);
  }
}
