package com.facishare.paas.appframework.flow;

import com.facishare.paas.appframework.core.model.User;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface StageThrusterService {
    /**
     * 批量获取数据可以跳转的阶段
     *
     * @param user
     * @param apiName
     * @param ids
     * @return
     */
    Map<String, List<String>> canMoveToStagesOfObjects(User user, String apiName, Set<String> ids);
}
