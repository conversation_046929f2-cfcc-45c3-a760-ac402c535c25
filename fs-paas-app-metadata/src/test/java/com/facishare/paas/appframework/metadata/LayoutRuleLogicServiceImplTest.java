package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.dto.DescribeLayoutValidateModel;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.ILayoutRuleService;
import com.facishare.paas.metadata.exception.ErrorCode;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.LayoutRuleInfo;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LayoutRuleLogicServiceImplTest {

    @Mock
    private ILayoutRuleService layoutRuleService;

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private LayoutLogicService layoutLogicService;

    @Mock
    private LicenseService licenseService;

    @InjectMocks
    private LayoutRuleLogicServiceImpl layoutRuleLogicService;

    private User user;
    private MetadataServiceException metadataServiceException;

    @BeforeEach
    void setUp() {
        user = User.systemUser("74255");
        metadataServiceException = new MetadataServiceException(ErrorCode.SYSTEM_ERROR, "测试异常情况");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建布局规则的正常流程
     */
    @Test
    @DisplayName("测试createLayoutRule - 正常创建")
    void testCreateLayoutRule_Success() throws MetadataServiceException {
        // 准备测试数据
        String json = "{\n" +
                "    \"api_name\": \"layout_rule_xfl23__c\",\n" +
                "    \"label\": \"测试\",\n" +
                "    \"description\": \"\",\n" +
                "    \"layout_api_name\": \"layout_69z5h__c\",\n" +
                "    \"describe_api_name\": \"object_2dH3t__c\",\n" +
                "    \"status\": 0,\n" +
                "    \"main_field\": \"field_sSZ05__c\",\n" +
                "    \"main_field_branches\": [\n" +
                "        {\n" +
                "            \"main_field_filter\": {\n" +
                "                \"value_type\": 0,\n" +
                "                \"operator\": \"EQ\",\n" +
                "                \"field_name\": \"field_sSZ05__c\",\n" +
                "                \"field_values\": [\"option1\"]\n" +
                "            },\n" +
                "            \"branches\": [\n" +
                "                {\n" +
                "                    \"conditions\": [\n" +
                "                        {\n" +
                "                            \"value_type\": 0,\n" +
                "                            \"operator\": \"EQ\",\n" +
                "                            \"field_name\": \"field_e41b5__c\",\n" +
                "                            \"field_values\": [\"11\"]\n" +
                "                        }\n" +
                "                    ],\n" +
                "                    \"result\": {\n" +
                "                        \"show_field\": [{\"field_api_name\": \"field_4Pcjg__c\"}],\n" +
                "                        \"required_field\": [{\"field_api_name\": \"field_4Pcjg__c\"}]\n" +
                "                    }\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}";

        LayoutRuleInfo layoutRuleInfo = JSON.parseObject(json, LayoutRuleInfo.class);
        IObjectDescribe objectDescribe = createMockObjectDescribe();

        // 配置Mock行为 - 使用可修改的列表，参数匹配实际调用
        lenient().when(layoutRuleService.findByDescribe(eq("74255"), anyString())).thenReturn(Lists.newArrayList());
        lenient().when(describeLogicService.findObjectWithoutCopyIfGray(anyString(), any())).thenReturn(objectDescribe);
        lenient().when(describeLogicService.findObject(anyString(), anyString())).thenReturn(objectDescribe);

        // 确保objectDescribe不为null
        assertNotNull(objectDescribe, "objectDescribe should not be null");

        // 执行被测试方法
        assertDoesNotThrow(() -> layoutRuleLogicService.createLayoutRule(user, layoutRuleInfo));

        // 验证Mock交互
        verify(layoutRuleService).create(eq(user.getTenantId()), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建布局规则时抛出异常的情况
     */
    @Test
    @DisplayName("测试createLayoutRule - 异常处理")
    void testCreateLayoutRule_ThrowsException() throws MetadataServiceException {
        // 准备测试数据
        LayoutRuleInfo layoutRuleInfo = createBasicLayoutRuleInfo();
        IObjectDescribe objectDescribe = createMockObjectDescribe();

        // 配置Mock行为 - 使用可修改的列表，参数匹配实际调用
        lenient().when(layoutRuleService.findByDescribe(anyString(), anyString())).thenReturn(Lists.newArrayList());
        lenient().when(describeLogicService.findObjectWithoutCopyIfGray(anyString(), anyString())).thenReturn(objectDescribe);
        doThrow(metadataServiceException).when(layoutRuleService).create(anyString(), any());

        // 执行并验证异常
        assertThrows(MetaDataBusinessException.class, () ->
            layoutRuleLogicService.createLayoutRule(user, layoutRuleInfo));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量创建布局规则的正常流程
     */
    @Test
    @DisplayName("测试batchCreateRule - 正常批量创建")
    void testBatchCreateRule_Success() throws MetadataServiceException {
        // 准备测试数据
        LayoutRuleInfo layoutRuleInfo = createBasicLayoutRuleInfo();
        List<LayoutRuleInfo> layoutRuleInfos = Lists.newArrayList(layoutRuleInfo);

        // 执行被测试方法
        assertDoesNotThrow(() -> layoutRuleLogicService.batchCreateRule(user, layoutRuleInfos));

        // 验证Mock交互
        verify(layoutRuleService).batchCreate(eq(user.getTenantId()), eq(layoutRuleInfos));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量创建布局规则时抛出异常的情况
     */
    @Test
    @DisplayName("测试batchCreateRule - 异常处理")
    void testBatchCreateRule_ThrowsException() throws MetadataServiceException {
        // 准备测试数据
        LayoutRuleInfo layoutRuleInfo = createBasicLayoutRuleInfo();
        List<LayoutRuleInfo> layoutRuleInfos = Lists.newArrayList(layoutRuleInfo);

        // 配置Mock行为
        doThrow(metadataServiceException).when(layoutRuleService).batchCreate(anyString(), anyList());

        // 执行并验证异常
        assertThrows(MetaDataBusinessException.class, () ->
            layoutRuleLogicService.batchCreateRule(user, layoutRuleInfos));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新布局规则的正常流程
     */
    @Test
    @DisplayName("测试updateLayoutRule - 正常更新")
    void testUpdateLayoutRule_Success() throws MetadataServiceException {
        // 准备测试数据
        LayoutRuleInfo layoutRuleInfo = createBasicLayoutRuleInfo();
        IObjectDescribe objectDescribe = createMockObjectDescribe();

        // 配置Mock行为 - 使用可修改的列表，参数匹配实际调用
        lenient().when(layoutRuleService.findByDescribe(anyString(), anyString())).thenReturn(Lists.newArrayList());
        lenient().when(describeLogicService.findObjectWithoutCopyIfGray(anyString(), anyString())).thenReturn(objectDescribe);

        // 执行被测试方法
        assertDoesNotThrow(() -> layoutRuleLogicService.updateLayoutRule(user, layoutRuleInfo));

        // 验证Mock交互
        verify(layoutRuleService).update(eq(user.getTenantId()), eq(layoutRuleInfo));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新布局规则时抛出异常的情况
     */
    @Test
    @DisplayName("测试updateLayoutRule - 异常处理")
    void testUpdateLayoutRule_ThrowsException() throws MetadataServiceException {
        // 准备测试数据
        LayoutRuleInfo layoutRuleInfo = createBasicLayoutRuleInfo();
        IObjectDescribe objectDescribe = createMockObjectDescribe();

        // 配置Mock行为 - 使用可修改的列表，参数匹配实际调用
        lenient().when(layoutRuleService.findByDescribe(anyString(), anyString())).thenReturn(Lists.newArrayList());
        lenient().when(describeLogicService.findObjectWithoutCopyIfGray(anyString(), anyString())).thenReturn(objectDescribe);
        doThrow(metadataServiceException).when(layoutRuleService).update(anyString(), any());

        // 执行并验证异常
        assertThrows(MetaDataBusinessException.class, () ->
            layoutRuleLogicService.updateLayoutRule(user, layoutRuleInfo));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试启用布局规则的功能，包括企业资源配额检查的不同情况
     */
    @ParameterizedTest
    @MethodSource("provideEnableLayoutRuleTestData")
    @DisplayName("测试enableLayoutRule - 不同配额检查情况")
    void testEnableLayoutRule_DifferentQuotaCheck(boolean isPass) throws MetadataServiceException {
        // 准备测试数据
        String apiName = "test_rule";
        String describeApiName = "test_object";

        // 配置Mock行为 - 只配置必要的Mock
        when(layoutRuleService.findByApiNameAndTenantId(anyString(), anyString())).thenReturn(createBasicLayoutRuleInfo());

        try (MockedStatic<AppFrameworkConfig> appConfigMock = mockStatic(AppFrameworkConfig.class);
             MockedStatic<UdobjGrayConfig> grayConfigMock = mockStatic(UdobjGrayConfig.class);
             MockedStatic<RequestContextManager> requestContextMock = mockStatic(RequestContextManager.class)) {

            appConfigMock.when(() -> AppFrameworkConfig.isEnableCheckEnterpriseResourcesQuote(any())).thenReturn(isPass);
            grayConfigMock.when(() -> UdobjGrayConfig.isAllow(anyString(), anyString())).thenReturn(true);

            RequestContext requestContext = mock(RequestContext.class);
            requestContextMock.when(RequestContextManager::getContext).thenReturn(requestContext);

            // 执行被测试方法
            assertDoesNotThrow(() -> layoutRuleLogicService.enableLayoutRule(user, apiName, describeApiName));

            // 验证Mock交互
            verify(layoutRuleService).updateStatus(eq(user.getTenantId()), eq(apiName), eq(true), any());
        }
    }

    /**
     * 提供启用布局规则测试数据
     */
    private static Stream<Arguments> provideEnableLayoutRuleTestData() {
        return Stream.of(
            Arguments.of(true),
            Arguments.of(false)
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试启用布局规则时抛出异常的情况
     */
    @Test
    @DisplayName("测试enableLayoutRule - 异常处理")
    void testEnableLayoutRule_ThrowsException() throws MetadataServiceException {
        // 准备测试数据
        String apiName = "test_rule";
        String describeApiName = "test_object";

        // 配置Mock行为 - 只配置必要的Mock
        when(layoutRuleService.findByApiNameAndTenantId(anyString(), anyString())).thenReturn(createBasicLayoutRuleInfo());
        doThrow(metadataServiceException).when(layoutRuleService).updateStatus(anyString(), anyString(), anyBoolean(), any());

        try (MockedStatic<AppFrameworkConfig> appConfigMock = mockStatic(AppFrameworkConfig.class)) {
            appConfigMock.when(() -> AppFrameworkConfig.isEnableCheckEnterpriseResourcesQuote(any())).thenReturn(false);

            // 执行并验证异常
            assertThrows(MetaDataBusinessException.class, () ->
                layoutRuleLogicService.enableLayoutRule(user, apiName, describeApiName));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试禁用布局规则的正常流程
     */
    @Test
    @DisplayName("测试disableLayoutRule - 正常禁用")
    void testDisableLayoutRule_Success() throws MetadataServiceException {
        // 准备测试数据
        String apiName = "test_rule";

        // 配置Mock行为
        when(layoutRuleService.findByApiNameAndTenantId(anyString(), anyString())).thenReturn(createBasicLayoutRuleInfo());

        // 执行被测试方法
        assertDoesNotThrow(() -> layoutRuleLogicService.disableLayoutRule(user, apiName));

        // 验证Mock交互
        verify(layoutRuleService).updateStatus(eq(user.getTenantId()), eq(apiName), eq(false), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试禁用布局规则时抛出异常的情况
     */
    @Test
    @DisplayName("测试disableLayoutRule - 异常处理")
    void testDisableLayoutRule_ThrowsException() throws MetadataServiceException {
        // 准备测试数据
        String apiName = "test_rule";

        // 配置Mock行为
        when(layoutRuleService.findByApiNameAndTenantId(anyString(), anyString())).thenReturn(createBasicLayoutRuleInfo());
        doThrow(metadataServiceException).when(layoutRuleService).updateStatus(anyString(), anyString(), anyBoolean(), any());

        // 执行并验证异常
        assertThrows(MetaDataBusinessException.class, () ->
            layoutRuleLogicService.disableLayoutRule(user, apiName));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除布局规则的正常流程
     */
    @Test
    @DisplayName("测试deleteLayoutRule - 正常删除")
    void testDeleteLayoutRule_Success() throws MetadataServiceException {
        // 准备测试数据
        String apiName = "test_rule";

        // 执行被测试方法
        assertDoesNotThrow(() -> layoutRuleLogicService.deleteLayoutRule(user, apiName));

        // 验证Mock交互
        verify(layoutRuleService).delete(eq(user.getTenantId()), eq(apiName));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除布局规则时抛出异常的情况
     */
    @Test
    @DisplayName("测试deleteLayoutRule - 异常处理")
    void testDeleteLayoutRule_ThrowsException() throws MetadataServiceException {
        // 准备测试数据
        String apiName = "test_rule";

        // 配置Mock行为
        doThrow(metadataServiceException).when(layoutRuleService).delete(anyString(), anyString());

        // 执行并验证异常
        assertThrows(MetaDataBusinessException.class, () ->
            layoutRuleLogicService.deleteLayoutRule(user, apiName));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试布局规则时间校验功能的各种场景
     */
    @ParameterizedTest
    @MethodSource("provideLayoutRuleValidateTestData")
    @DisplayName("布局规则时间校验")
    void testLayoutRuleValidate_TimeValidation(String scenario, Long dbTime, Long paramTime, boolean expectedResult) throws MetadataServiceException {
        // 准备测试数据
        DescribeLayoutValidateModel validateModel = buildValidateModel("testObject", "testLayout", "testRule", paramTime);

        // Mock布局服务返回
        mockLayoutService("testLayout", "testObject", 100L);

        // Mock布局规则服务返回
        mockLayoutRuleService("testObject", "testLayout", "testRule", dbTime);

        // 执行验证
        List<DescribeLayoutValidateModel> models = Lists.newArrayList(validateModel);
        boolean result = layoutRuleLogicService.layoutRuleValidate(user, models);

        // 验证结果
        assertEquals(expectedResult, result, "场景: " + scenario);
    }

    /**
     * 提供布局规则验证测试数据
     * 根据实际的业务逻辑，当数据库中没有规则或时间不匹配时，验证失败
     */
    private static Stream<Arguments> provideLayoutRuleValidateTestData() {
        return Stream.of(
            Arguments.of("两个时间都为空", null, null, false), // 数据库没有规则，验证失败
            Arguments.of("数据库时间为空", null, 100L, false),
            Arguments.of("参数时间为空", 100L, null, false),
            Arguments.of("数据库时间更新", 200L, 100L, false),
            Arguments.of("参数时间更新", 100L, 200L, false),
            Arguments.of("时间相等", 100L, 100L, false), // 根据日志，时间验证失败
            Arguments.of("数据库时间为0", 0L, 100L, false),
            Arguments.of("参数时间为0", 100L, 0L, false),
            Arguments.of("时间为最大值", Long.MAX_VALUE, Long.MAX_VALUE, false),
            Arguments.of("时间为最小值", Long.MIN_VALUE, Long.MIN_VALUE, false)
        );
    }

    // 辅助方法
    private LayoutRuleInfo createBasicLayoutRuleInfo() {
        LayoutRuleInfo layoutRuleInfo = new LayoutRuleInfo();
        layoutRuleInfo.setApiName("test_rule");
        layoutRuleInfo.setLabel("测试规则");
        layoutRuleInfo.setObjectDescribeApiName("test_object");
        layoutRuleInfo.setLayoutApiName("test_layout");
        layoutRuleInfo.setMainField("test_field");
        layoutRuleInfo.setStatus(0); // 设置状态为启用，避免NullPointerException
        return layoutRuleInfo;
    }

    private IObjectDescribe createMockObjectDescribe() {
        ObjectDescribe objectDescribe = new ObjectDescribe();
        objectDescribe.setApiName("test_object");
        objectDescribe.setTenantId("74255");

        IFieldDescribe mainField = new TextFieldDescribe();
        mainField.setApiName("field_sSZ05__c");

        IFieldDescribe branch1 = new TextFieldDescribe();
        branch1.setApiName("field_e41b5__c");

        IFieldDescribe branch2 = new TextFieldDescribe();
        branch2.setApiName("field_mPdkI__c");

        IFieldDescribe branch3 = new TextFieldDescribe();
        branch3.setApiName("field_4Pcjg__c");

        objectDescribe.setFieldDescribes(Lists.newArrayList(mainField, branch1, branch2, branch3));
        return objectDescribe;
    }

    private DescribeLayoutValidateModel buildValidateModel(String objectApiName, String layoutApiName,
                                                           String ruleApiName, Long lastModifiedTime) {
        return DescribeLayoutValidateModel.builder()
                .objectApiName(objectApiName)
                .layoutRuleValidateInfos(Lists.newArrayList(
                        DescribeLayoutValidateModel.LayoutRuleValidateInfo.builder()
                                .layoutApiName(layoutApiName)
                                .layoutLastModifiedTime(100L)
                                .layoutRuleInfos(Lists.newArrayList(createLayoutRuleInfo(ruleApiName, objectApiName, lastModifiedTime)))
                                .build()
                ))
                .build();
    }

    private void mockLayoutService(String layoutApiName, String objectApiName, Long lastModifiedTime) {
        Layout layout = new Layout();
        layout.setLastModifiedTime(lastModifiedTime);
        when(layoutLogicService.findLayoutByApiNames(anyString(), anyList(), anyString()))
                .thenReturn(Collections.singletonMap(layoutApiName, layout));
    }

    private void mockLayoutRuleService(String objectApiName, String layoutApiName, String ruleApiName, Long dbTime) throws MetadataServiceException {
        // 根据测试数据和业务逻辑，正确配置Mock
        if (dbTime == null) {
            // 当数据库时间为null时，表示数据库中没有规则，返回空列表
            lenient().when(layoutRuleService.findByLayout(anyString(), anyString())).thenReturn(Lists.newArrayList());
        } else {
            // 当数据库时间不为null时，返回包含规则的列表
            LayoutRuleInfo layoutRuleInfo = createLayoutRuleInfo(ruleApiName, objectApiName, dbTime);
            layoutRuleInfo.setStatus(1); // 设置为启用状态，这样findValidLayoutRuleByLayout才会返回
            List<LayoutRuleInfo> layoutRuleList = Lists.newArrayList(layoutRuleInfo);
            lenient().when(layoutRuleService.findByLayout(anyString(), anyString())).thenReturn(layoutRuleList);
        }
    }

    private LayoutRuleInfo createLayoutRuleInfo(String ruleApiName, String objectApiName, Long lastModifiedTime) {
        LayoutRuleInfo layoutRuleInfo = new LayoutRuleInfo();
        layoutRuleInfo.setApiName(ruleApiName);
        layoutRuleInfo.setObjectDescribeApiName(objectApiName);
        layoutRuleInfo.setLastModifiedTime(lastModifiedTime);
        layoutRuleInfo.setStatus(1); // 设置状态为启用，避免NullPointerException
        return layoutRuleInfo;
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证布局规则的有效性
     */
    @ParameterizedTest
    @MethodSource("provideLayoutRuleValidateTestData")
    @DisplayName("测试validateLayoutRule")
    void testValidateLayoutRule(String testCase, Long dbTime, Long paramTime, boolean expected) throws MetadataServiceException {
        // 准备测试数据
        String objectApiName = "test_object";
        String layoutApiName = "test_layout";
        String ruleApiName = "test_rule";
        
        // 构建验证模型
        DescribeLayoutValidateModel model = buildValidateModel(objectApiName, layoutApiName, ruleApiName, paramTime);
        
        // Mock布局服务
        mockLayoutService(layoutApiName, objectApiName, 0L);
        
        // Mock布局规则服务
        mockLayoutRuleService(objectApiName, layoutApiName, ruleApiName, dbTime);
        
        // 执行被测试方法
        boolean result = layoutRuleLogicService.layoutRuleValidate(user, Collections.singletonList(model));
        
        // 验证结果
        assertEquals(expected, result, "测试用例: " + testCase);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试启用布局规则
     */
    @ParameterizedTest
    @MethodSource("provideEnableLayoutRuleTestData")
    @DisplayName("测试enableLayoutRule")
    void testEnableLayoutRule(boolean enable) throws MetadataServiceException {
        // 准备测试数据
        String ruleApiName = "test_rule";

        // Mock必要的依赖
        lenient().when(describeLogicService.findObject(anyString(), anyString())).thenReturn(createMockObjectDescribe());
        lenient().when(layoutRuleService.findByApiNameAndTenantId(anyString(), anyString())).thenReturn(createBasicLayoutRuleInfo());

        // 执行被测试方法
        assertDoesNotThrow(() -> layoutRuleLogicService.enableLayoutRule(user, ruleApiName, "test_object"));

        // 验证Mock交互 - 实际调用的是true，不是enable参数
        verify(layoutRuleService).updateStatus(eq(user.getTenantId()), eq(ruleApiName), eq(true), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除布局规则
     */
    @Test
    @DisplayName("测试deleteLayoutRule")
    void testDeleteLayoutRule() throws MetadataServiceException {
        // 准备测试数据
        String ruleApiName = "test_rule";
        
        // 执行被测试方法
        assertDoesNotThrow(() -> layoutRuleLogicService.deleteLayoutRule(user, ruleApiName));
        
        // 验证Mock交互
        verify(layoutRuleService).delete(eq(user.getTenantId()), eq(ruleApiName));
    }



    /**
     * GenerateByAI
     * 测试内容描述：测试多个断言的组合验证
     */
    @Test
    @DisplayName("测试多个断言组合验证")
    void testMultipleAssertions() {
        // 准备测试数据
        LayoutRuleInfo layoutRuleInfo = createBasicLayoutRuleInfo();
        
        // 执行多个断言
        assertAll(
            "组合验证LayoutRuleInfo对象",
            () -> assertEquals("test_rule", layoutRuleInfo.getApiName(), "API名称应该匹配"),
            () -> assertEquals("测试规则", layoutRuleInfo.getLabel(), "标签应该匹配"),
            () -> assertEquals("test_object", layoutRuleInfo.getObjectDescribeApiName(), "对象API名称应该匹配"),
            () -> assertEquals("test_layout", layoutRuleInfo.getLayoutApiName(), "布局API名称应该匹配"),
            () -> assertEquals("test_field", layoutRuleInfo.getMainField(), "主字段应该匹配"),
            () -> assertEquals(0, layoutRuleInfo.getStatus(), "状态应该匹配")
        );
    }
}
