package com.facishare.paas.appframework.rest.dto.data;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * create by z<PERSON><PERSON> on 2020/02/04
 */
public interface FindSimpleDataByIds {
    @Data
    class Arg {
        private String describeApiName;
        private List<String> ids;
        private List<String> fieldList;
    }

    @Data
    @Builder
    class Result {
        List<ObjectDataDocument> dataList;
    }
}
