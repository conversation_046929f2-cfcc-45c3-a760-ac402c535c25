package com.facishare.paas.appframework.metadata.relation


import com.facishare.paas.metadata.api.describe.*
import spock.lang.Specification

/**
 * create by <PERSON><PERSON><PERSON> on 2020/08/10
 */
class FieldRelationGraphBuilderTest extends Specification {
    def "test buildGraphByCalculate"() {
        given:
        IObjectDescribe describe = Mock(IObjectDescribe)
        describe.getApiName() >> "customObj"

        IFieldDescribe text = Mock(Text)
        text.getApiName() >> "text__c"
        text.isActive() >> true
        text.getType() >> IFieldType.TEXT

        IFieldDescribe lookup = Mock(IObjectReferenceField)
        lookup.getApiName() >> "lookup__c"
        lookup.isActive() >> true
        lookup.getTargetApiName() >> "customObj"
        lookup.getType() >> IFieldType.OBJECT_REFERENCE

        IFieldDescribe formula = Mock(Formula)
        formula.getApiName() >> "formula__c"
        formula.isActive() >> true
        formula.getExpression() >> '$text__c$'
        formula.getType() >> IFieldType.FORMULA

        describe.getFieldDescribes() >> [text, lookup, formula]
        describe.getFieldDescribe("text__c") >> text
        when:
        def graphBuilder = FieldRelationGraphBuilder.builder().graphLayers([]).build()
        graphBuilder.buildGraphByCalculate(describe)
        then:
        println graphBuilder.graph.graph
        1 == 1
    }
}
