package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.common.util.UdobjGrayKey;
import com.facishare.paas.appframework.common.util.UdobjGrayUtil;
import com.facishare.paas.appframework.core.model.ControllerListener;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController.Arg;
import com.facishare.paas.appframework.core.predef.listener.prm.PrmDetailControllerListener;
import com.facishare.paas.appframework.core.predef.listener.prm.PrmNewDetailControllerListener;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.SecurityEventTrackingDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.trace.TraceContext;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * Created by zhouwr on 2019/6/25
 */
public class StandardWebDetailController extends AbstractStandardWebDetailController<Arg> {

    @Override
    public List<Class<? extends ControllerListener<Arg, Result>>> getControllerListenerClassList() {
        List<Class<? extends ControllerListener<Arg, Result>>> controllerListenerClassList = super.getControllerListenerClassList();
        if (AppIdMapping.isPRM(controllerContext.getAppId())) {
            controllerListenerClassList.remove(PrmDetailControllerListener.class);
            controllerListenerClassList.add(PrmNewDetailControllerListener.class);
        }
        return controllerListenerClassList;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        // 详情页事件安全埋点
        try {
            if (RequestUtil.isCepRequest() && UdobjGrayUtil.isObjectAndTenantGray(UdobjGrayKey.SECURITY_INCIDENTS_DETAIL,
                    controllerContext.getTenantId(), controllerContext.getObjectApiName())) {
                SecurityEventTrackingDTO trackingDTO = SecurityEventTrackingDTO.builder()
                        .eventId(UUID.randomUUID().toString())
                        .traceId(TraceContext.get().getTraceId())
                        .eventTime(System.currentTimeMillis())
                        .tenantId(controllerContext.getTenantId())
                        .userId(controllerContext.getUser().getUserId())
                        .sourceIp(Objects.isNull(RequestContextManager.getContext()) ? "" : RequestContextManager.getContext().getClientIp())
                        .deviceId(RequestUtil.getCustomHeader(RequestContext.FS_DEVICE_ID))
                        .deviceType(RequestUtil.getCustomHeader(RequestContext.FS_DEVICE_TYPE))
                        .objects(controllerContext.getObjectApiName())
                        .operation("DetailView")
                        .actionType("UIDetailViewEvent")
                        .parameters(JSON.toJSONString(arg))
                        .objectIds(arg.getObjectDataId())
                        .build();
                BizLogClient.send("biz-log-security-event-tracking", Pojo2Protobuf.toMessage(trackingDTO, com.fxiaoke.log.SecurityEventTrackingDTO.class).toByteArray());
            }
        } catch (Exception e) {
            log.warn("send message error ,exception: ", e);
        }
        return super.after(arg, result);
    }
}
