package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.privilege.dto.ObjectsPermission;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * create by <PERSON><PERSON><PERSON> on 2018/12/13
 */
@RestResource(
        value = "PAAS-DATA-AUTH-SERVICE",
        desc = "PAAS新数据权限服务", // ignoreI18n
        contentType = "application/json"
)
public interface DataAuthServiceProxy {

    @POST(value = "/datarights/objectspermission", desc = "用户的记录权限查询")
    ObjectsPermission.Result objectsPermission(@HeaderMap Map<String, String> header, @Body ObjectsPermission.Arg arg);
}
