package com.facishare.paas.appframework.core.predef.service.dto.function;

import com.alibaba.dubbo.rpc.RpcException;
import com.google.gson.annotations.SerializedName;

import com.alibaba.fastjson.annotation.JSONField;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;

/**
 * Created by liyiguang on 2018/4/2.
 */
public interface RunFunction {

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        @JSONField(name = "api_name")
        @SerializedName(value = "api_name")
        @JsonProperty(value = "api_name")
        String apiName;

        @JSONField(name = "binding_object_api_name")
        @SerializedName(value = "binding_object_api_name")
        @JsonProperty(value = "binding_object_api_name")
        String bindingObjectAPIName;

        List<FunctionInfo.Parameter> parameters;

        @JSONField(name = "binding_object_data_id")
        @SerializedName(value = "binding_object_data_id")
        @JsonProperty(value = "binding_object_data_id")
        String bindingObjectDataId;

        List<String> objectIds;
    }

    @Data
    @Builder
    @Jacksonized
    class Result {
        private boolean success;
        private Object functionResult;
        private String errorInfo;
        private int status;

        public static final int RUNNING = 1;
        public static final int END = 2;

    }
}
