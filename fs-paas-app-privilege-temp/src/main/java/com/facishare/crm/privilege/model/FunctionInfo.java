package com.facishare.crm.privilege.model;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * 一个funcCode项的权限信息
 * Created by luxin on 2016/12/1.
 */
@Data
@AllArgsConstructor
@Builder
public class FunctionInfo {
    private String displayName;
    private Boolean enabled;
    private String functionNumber;
    private Boolean isEditable;
    private Boolean defaultStatus;


    private Boolean isIntelligentForm;
    private Boolean isFiledReadOnlyRequired;
    private Boolean isAdd;

    private Boolean isClone;

    private static FunctionInfo duplicateCheckObjFunctionInfo;

    public FunctionInfo(String displayName, Boolean isEditable, String functionNumber, boolean enabled, boolean defaultStatus) {
        this.displayName = displayName;
        this.functionNumber = functionNumber;
        this.isEditable = isEditable;
        this.enabled = enabled;
        this.defaultStatus = defaultStatus;
    }

    public FunctionInfo() {

    }

    public void setEnabledOrDefault(Boolean enabled) {
        if (enabled == null) {
            this.enabled = false;
        } else {
            this.enabled = enabled;
        }
    }

    public static FunctionInfo getDuplicateCheckObjFunctionInfo() {
        if (duplicateCheckObjFunctionInfo == null) {
            duplicateCheckObjFunctionInfo = new FunctionInfo(I18N.text(I18NKey.ALLOW_REPEAT_TOOL), true, "DuplicateCheckObj", false, false);
            return duplicateCheckObjFunctionInfo;
        } else {
            return duplicateCheckObjFunctionInfo;
        }
    }

}
