package com.facishare.paas.appframework.metadata.switchcache.provider;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DuplicatedSearchService;
import com.facishare.paas.appframework.metadata.switchcache.SwitchInfo;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiFunction;

import static com.facishare.paas.appframework.metadata.switchcache.provider.SwitchCacheProviderManager.DUPLICATED_SEARCH;

/**
 * create by z<PERSON><PERSON> on 2021/04/23
 */
@Component
public class DuplicatedSearchSwitchCacheProvider extends BaseSwitchCacheProvider {
    @Autowired
    @Qualifier("duplicatedSearchService")
    private DuplicatedSearchService duplicatedSearchService;

    private static final Map<String, BiFunction<User, String, Boolean>> funcMap = Maps.newTreeMap(String::compareToIgnoreCase);

    private static final String NEW_ENABLE = getFuncKey(IDuplicatedSearch.Type.NEW, "enable");
    private static final String NEW_SUPPORT_IMPORT = getFuncKey(IDuplicatedSearch.Type.NEW, "support_import");
    private static final String TOOL_ENABLE = getFuncKey(IDuplicatedSearch.Type.TOOL, "enable");

    @Override
    public String getSwitchType() {
        return DUPLICATED_SEARCH;
    }

    @Override
    public SwitchInfo getSwitchInfo(User user, String bindingObjectApiName, String switchName) {
        Boolean switchStatus = funcMap.get(switchName).apply(user, bindingObjectApiName);
        return SwitchInfo.of(user.getTenantId(), getSwitchType(), switchName, bindingObjectApiName, switchStatus);
    }

    @PostConstruct
    private void init() {
        funcMap.putIfAbsent(NEW_ENABLE, (user, objectApiName) -> {
            IDuplicatedSearch duplicatedSearch = duplicatedSearchService.findDuplicatedSearchByApiNameAndType(user.getTenantId(), objectApiName, IDuplicatedSearch.Type.NEW, true);
            return Objects.nonNull(duplicatedSearch) && duplicatedSearch.isEnable();
        });

        funcMap.putIfAbsent(NEW_SUPPORT_IMPORT, (user, objectApiName) -> {
            IDuplicatedSearch duplicatedSearch = duplicatedSearchService.findDuplicatedSearchByApiNameAndType(user.getTenantId(), objectApiName, IDuplicatedSearch.Type.NEW, true);
            return Objects.nonNull(duplicatedSearch) && duplicatedSearch.isSupportImport();
        });

        funcMap.putIfAbsent(TOOL_ENABLE, (user, objectApiName) -> {
            IDuplicatedSearch duplicatedSearch = duplicatedSearchService.findDuplicatedSearchByApiNameAndType(user.getTenantId(), objectApiName, IDuplicatedSearch.Type.TOOL, true);
            return Objects.nonNull(duplicatedSearch) && duplicatedSearch.isEnable();
        });
    }

    private static String getFuncKey(IDuplicatedSearch.Type type, String key) {
        return String.format("%s_%s_%s", DUPLICATED_SEARCH, type, key);
    }

    @Override
    protected Set<String> getSwitchNamesEmpty2All(Collection<String> switchNames) {
        return CollectionUtils.empty(switchNames) ? Sets.newHashSet(funcMap.keySet()) : Sets.newHashSet(switchNames);
    }
}
