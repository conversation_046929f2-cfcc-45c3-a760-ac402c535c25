package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface CheckDisableFields {
    @Data
    class Arg {
        @JSONField(name = "M1")
        private String describeApiName;
        @JSONField(name = "M2")
        private List<String> fieldList;
    }

    @Data
    @Builder
    class Result {
    }
}
