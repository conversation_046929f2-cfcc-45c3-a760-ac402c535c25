package com.facishare.crm.privilege.util;


import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by lei on 12/15/16.
 */
public class LogUtil {
    private static String appId = "CRM";
    //    private static String  corpId="";
//    private static String objectName = "41";
    private static Integer objectType = 0;
//    private static String module = objectName;
    private static String memo = "0";
    private static String NULL_STR = "";
    public static Integer bizOperationName_Add = 1;
    public static Integer bizOperationName_Modify = 2;
    public static Integer bizOperationName_Invalid = 3;
    public static Integer bizOperationName_Recovery = 4;
    public static Integer bizOperationName_Delete = 5;

    public static Map<String, Object> buildLOG(String corpId, Integer bizOperationName, String userName, String userId, String textMessage, String objectName) {

        String module = objectName;
        Map<String, Object> textMsg = new HashMap(4);
        textMsg.put("type", 0);
        textMsg.put("text", textMessage);
        textMsg.put("dataID", "");
        textMsg.put("objectType", objectType);

        Map<String, Object> jsonMessage = new HashMap(3);
        jsonMessage.put("textMsg", Collections.singletonList(textMsg));
        jsonMessage.put("snapshot", new HashMap(0));
        jsonMessage.put("SaleDynamicID", NULL_STR);

        Map<String, Object> log = new HashMap(10);
        log.put("appId", appId);
        log.put("corpId", corpId);
        log.put("objectId", NULL_STR);
        log.put("objectName", objectName);
        log.put("module", module);
        log.put("bizOperationName", bizOperationName);
        log.put("userId", userId);
        log.put("userName", userName);
        log.put("textMessage", textMessage);
        log.put("jsonMessage", jsonMessage);
        log.put("memo", memo);
        Map<String, Object> m = new HashMap(1);
        m.put("log", Collections.singletonList(log));
        return m;
    }

   /* public static void main(String[] args) {
        Map m = build("c", LogUtil.bizOperationName_Add, "zhang", "", "");
        System.out.println(JsonUtil.toJson(m));
    }*/


}
