package com.facishare.paas.appframework.rest.dto.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by Yuanxl on 2018/5/7
 */
public interface RecoverData {

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    class Arg {
        String describeAPIName;
        String dataId;
    }

    class Result extends CommonResult.ObjectDataResult {}
}
