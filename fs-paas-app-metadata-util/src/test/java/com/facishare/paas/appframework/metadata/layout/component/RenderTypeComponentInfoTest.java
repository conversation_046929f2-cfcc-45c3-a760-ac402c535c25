package com.facishare.paas.appframework.metadata.layout.component;

import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class RenderTypeComponentInfoTest {

  private RenderTypeComponentInfo renderTypeComponentInfo;

  @BeforeEach
  void setUp() {
    renderTypeComponentInfo = new RenderTypeComponentInfo();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试RenderTypeComponentInfo的无参构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试无参构造函数")
  void testDefaultConstructor() {
    RenderTypeComponentInfo info = new RenderTypeComponentInfo();
    assertNotNull(info, "无参构造函数创建的对象不应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试RenderTypeComponentInfo的Map构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = Maps.newHashMap();
    map.put(IRenderTypeComponentInfo.NAME, "table");
    map.put(IRenderTypeComponentInfo.PAGE_TYPE, "list");
    
    RenderTypeComponentInfo info = new RenderTypeComponentInfo(map);
    assertNotNull(info, "Map构造函数创建的对象不应为空");
    assertEquals("table", info.getName(), "应正确设置名称");
    assertEquals("list", info.getPageType(), "应正确设置页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试defaultRenderTypeInfo静态工厂方法
   */
  @Test
  @DisplayName("正常场景 - 测试defaultRenderTypeInfo静态工厂方法")
  void testDefaultRenderTypeInfo() {
    RenderTypeComponentInfo info = RenderTypeComponentInfo.defaultRenderTypeInfo();
    
    assertNotNull(info, "静态工厂方法创建的对象不应为空");
    assertEquals(IRenderTypeComponentInfo.RENDER_TYPE_CARD, info.getName(), "应设置默认渲染类型为card");
    assertEquals(IComponentInfo.PAGE_TYPE_LIST, info.getPageType(), "应设置默认页面类型为list");
    assertEquals(IRenderTypeComponentInfo.RENDER_TYPE_CARD, info.getRenderType(), "getRenderType应返回card");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getName和setName方法
   */
  @Test
  @DisplayName("正常场景 - 测试Name属性")
  void testName() {
    // 初始状态应为null
    assertNull(renderTypeComponentInfo.getName(), "初始名称应为null");
    
    // 设置名称
    renderTypeComponentInfo.setName("grid");
    assertEquals("grid", renderTypeComponentInfo.getName(), "应正确设置名称");
    
    // 设置为null
    renderTypeComponentInfo.setName(null);
    assertNull(renderTypeComponentInfo.getName(), "应支持设置为null");
    
    // 设置为空字符串
    renderTypeComponentInfo.setName("");
    assertEquals("", renderTypeComponentInfo.getName(), "应支持设置为空字符串");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getRenderType和setRenderType方法
   */
  @Test
  @DisplayName("正常场景 - 测试RenderType属性")
  void testRenderType() {
    // 初始状态应为null
    assertNull(renderTypeComponentInfo.getRenderType(), "初始渲染类型应为null");
    
    // 设置渲染类型
    renderTypeComponentInfo.setRenderType("chart");
    assertEquals("chart", renderTypeComponentInfo.getRenderType(), "应正确设置渲染类型");
    assertEquals("chart", renderTypeComponentInfo.getName(), "setRenderType应同时设置name");
    
    // 通过setName设置，getRenderType应返回相同值
    renderTypeComponentInfo.setName("dashboard");
    assertEquals("dashboard", renderTypeComponentInfo.getRenderType(), "getRenderType应返回name的值");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getPageType方法
   */
  @Test
  @DisplayName("正常场景 - 测试getPageType方法")
  void testGetPageType() {
    // 初始状态应为null
    assertNull(renderTypeComponentInfo.getPageType(), "初始页面类型应为null");
    
    // 通过Map构造函数设置页面类型
    Map<String, Object> map = Maps.newHashMap();
    map.put(IRenderTypeComponentInfo.PAGE_TYPE, "detail");
    RenderTypeComponentInfo info = new RenderTypeComponentInfo(map);
    assertEquals("detail", info.getPageType(), "应正确获取页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试copy方法
   */
  @Test
  @DisplayName("正常场景 - 测试copy方法")
  void testCopy() {
    // 设置一些属性
    Map<String, Object> map = Maps.newHashMap();
    map.put(IRenderTypeComponentInfo.NAME, "kanban");
    map.put(IRenderTypeComponentInfo.PAGE_TYPE, "project");
    
    RenderTypeComponentInfo original = new RenderTypeComponentInfo(map);
    IComponentInfo copied = original.copy();
    
    assertNotNull(copied, "复制的对象不应为空");
    assertNotSame(original, copied, "复制的对象应是不同的实例");
    assertTrue(copied instanceof RenderTypeComponentInfo, "复制的对象应是RenderTypeComponentInfo类型");
    
    RenderTypeComponentInfo copiedInfo = (RenderTypeComponentInfo) copied;
    assertEquals(original.getName(), copiedInfo.getName(), "复制的对象应有相同的名称");
    assertEquals(original.getPageType(), copiedInfo.getPageType(), "复制的对象应有相同的页面类型");
    assertEquals(original.getRenderType(), copiedInfo.getRenderType(), "复制的对象应有相同的渲染类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口实现
   */
  @Test
  @DisplayName("正常场景 - 测试接口实现")
  void testInterfaceImplementation() {
    // 验证实现了IRenderTypeComponentInfo接口
    assertTrue(renderTypeComponentInfo instanceof IRenderTypeComponentInfo, 
               "应实现IRenderTypeComponentInfo接口");
    
    // 验证实现了IComponentInfo接口
    assertTrue(renderTypeComponentInfo instanceof IComponentInfo, 
               "应实现IComponentInfo接口");
    
    // 验证实现了JsonCompatible接口
    assertTrue(renderTypeComponentInfo instanceof com.facishare.paas.metadata.api.JsonCompatible, 
               "应实现JsonCompatible接口");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试序列化版本号
   */
  @Test
  @DisplayName("正常场景 - 测试序列化版本号")
  void testSerialVersionUID() {
    // 验证类有序列化版本号（通过反射检查）
    try {
      java.lang.reflect.Field field = RenderTypeComponentInfo.class.getDeclaredField("serialVersionUID");
      field.setAccessible(true);
      long serialVersionUID = field.getLong(null);
      assertEquals(-2178432304391598593L, serialVersionUID, "序列化版本号应正确");
    } catch (Exception e) {
      fail("应该有serialVersionUID字段");
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试继承的AbstractComponentInfoDocument功能
   */
  @Test
  @DisplayName("正常场景 - 测试继承功能")
  void testInheritedFunctionality() {
    // 验证继承自AbstractComponentInfoDocument
    assertTrue(renderTypeComponentInfo instanceof AbstractComponentInfoDocument, 
               "应继承自AbstractComponentInfoDocument");
    
    // 通过Map构造函数测试继承的功能
    Map<String, Object> map = Maps.newHashMap();
    map.put("custom_field", "custom_value");
    map.put(IRenderTypeComponentInfo.NAME, "timeline");
    
    RenderTypeComponentInfo info = new RenderTypeComponentInfo(map);
    assertEquals("timeline", info.getName(), "应正确处理名称");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试常见的渲染类型
   */
  @Test
  @DisplayName("正常场景 - 测试常见渲染类型")
  void testCommonRenderTypes() {
    String[] renderTypes = {
        IRenderTypeComponentInfo.RENDER_TYPE_CARD,
        "table", "grid", "list", "chart", "dashboard", "kanban", "timeline", "calendar"
    };
    
    for (String renderType : renderTypes) {
      renderTypeComponentInfo.setRenderType(renderType);
      assertEquals(renderType, renderTypeComponentInfo.getRenderType(), "应正确设置渲染类型: " + renderType);
      assertEquals(renderType, renderTypeComponentInfo.getName(), "name应与渲染类型一致: " + renderType);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试常见的页面类型
   */
  @Test
  @DisplayName("正常场景 - 测试常见页面类型")
  void testCommonPageTypes() {
    String[] pageTypes = {
        IComponentInfo.PAGE_TYPE_LIST,
        "detail", "edit", "create", "dashboard", "report"
    };
    
    for (String pageType : pageTypes) {
      Map<String, Object> map = Maps.newHashMap();
      map.put(IRenderTypeComponentInfo.PAGE_TYPE, pageType);
      
      RenderTypeComponentInfo info = new RenderTypeComponentInfo(map);
      assertEquals(pageType, info.getPageType(), "应正确设置页面类型: " + pageType);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试Name和RenderType的一致性
   */
  @Test
  @DisplayName("正常场景 - 测试Name和RenderType一致性")
  void testNameAndRenderTypeConsistency() {
    // 通过setName设置
    renderTypeComponentInfo.setName("custom_render");
    assertEquals("custom_render", renderTypeComponentInfo.getName(), "name应正确设置");
    assertEquals("custom_render", renderTypeComponentInfo.getRenderType(), "renderType应与name一致");
    
    // 通过setRenderType设置
    renderTypeComponentInfo.setRenderType("another_render");
    assertEquals("another_render", renderTypeComponentInfo.getName(), "name应与renderType一致");
    assertEquals("another_render", renderTypeComponentInfo.getRenderType(), "renderType应正确设置");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试特殊字符处理
   */
  @Test
  @DisplayName("边界场景 - 测试特殊字符处理")
  void testSpecialCharacters() {
    // 测试包含特殊字符的渲染类型名称
    String specialName = "render_type-with.special@chars";
    
    renderTypeComponentInfo.setName(specialName);
    assertEquals(specialName, renderTypeComponentInfo.getName(), "应正确处理特殊字符名称");
    assertEquals(specialName, renderTypeComponentInfo.getRenderType(), "renderType应正确处理特殊字符");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试copy方法的深度复制
   */
  @Test
  @DisplayName("正常场景 - 测试copy方法深度复制")
  void testCopyDeepCopy() {
    // 创建包含多个属性的原始对象
    Map<String, Object> map = Maps.newHashMap();
    map.put(IRenderTypeComponentInfo.NAME, "original_render");
    map.put(IRenderTypeComponentInfo.PAGE_TYPE, "original_page");
    map.put("custom_property", "custom_value");
    
    RenderTypeComponentInfo original = new RenderTypeComponentInfo(map);
    RenderTypeComponentInfo copied = (RenderTypeComponentInfo) original.copy();
    
    // 验证复制的对象
    assertNotNull(copied, "复制的对象不应为空");
    assertEquals(original.getName(), copied.getName(), "名称应相同");
    assertEquals(original.getPageType(), copied.getPageType(), "页面类型应相同");
    assertEquals(original.getRenderType(), copied.getRenderType(), "渲染类型应相同");
    
    // 验证是不同的实例
    assertNotSame(original, copied, "应是不同的实例");
    
    // 修改原始对象，验证复制对象不受影响
    original.setName("modified_render");
    assertEquals("original_render", copied.getName(), "复制对象不应受原始对象修改影响");
  }
}
