package com.facishare.paas.appframework.core.predef.service.dto.fieldshowname;

import com.facishare.paas.appframework.metadata.repository.model.FieldShowName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface UpsertFieldShowName {

    @Data
    class Arg {
        private List<FieldShowName> fieldShowNames;
        private String describeApiName;
        private String recordApiName;
    }

    @Data
    @Builder
    class Result {
    }
}
