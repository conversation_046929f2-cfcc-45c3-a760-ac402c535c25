package com.facishare.paas.appframework.rest.service;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeProxy;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.RoleService;
import com.facishare.paas.appframework.privilege.dto.RestResult;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 外部角色服务单元测试
 */
@ExtendWith(MockitoExtension.class)
@Timeout(3)
class OuterRoleServiceTest {

    @Mock
    private RoleService roleService;

    @Mock
    private com.facishare.crm.privilege.service.RoleService businessRoleService;

    @Mock
    private FunctionPrivilegeProxy functionPrivilegeProxy;

    @Mock
    private FunctionPrivilegeService functionPrivilegeService;

    @Mock
    private DescribeLogicService describeLogicService;

    @InjectMocks
    private OuterRoleService outerRoleService;

    private RequestContext requestContext;
    private User user;

    @BeforeEach
    void setUp() {
        user = new User("74255", "1000");
        requestContext = RequestContext.builder()
                .user(user)
                .tenantId("74255")
                .appId("testAppId")
                .build();
    }

    @Test
    void testGetOutRoleInfo_Success() {
        // Given
        List<RestResult.RoleInfoPojo> mockRoleInfoList = Lists.newArrayList();

        RestResult.RoleInfoPojo roleInfo1 = new RestResult.RoleInfoPojo();
        roleInfo1.setRoleCode("ROLE_001");
        roleInfo1.setRoleName("管理员");
        mockRoleInfoList.add(roleInfo1);

        RestResult.RoleInfoPojo roleInfo2 = new RestResult.RoleInfoPojo();
        roleInfo2.setRoleCode("ROLE_002");
        roleInfo2.setRoleName("普通用户");
        mockRoleInfoList.add(roleInfo2);

        when(roleService.getOuterRoleInfoList("74255", "testAppId"))
                .thenReturn(mockRoleInfoList);

        // When
        Map<String, String> result = outerRoleService.getOutRoleInfo(requestContext);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("管理员", result.get("ROLE_001"));
        assertEquals("普通用户", result.get("ROLE_002"));
        verify(roleService, times(1)).getOuterRoleInfoList("74255", "testAppId");
    }

    @Test
    void testGetOutRoleInfo_EmptyRoleList() {
        // Given
        when(roleService.getOuterRoleInfoList("74255", "testAppId"))
                .thenReturn(Lists.newArrayList());

        // When
        Map<String, String> result = outerRoleService.getOutRoleInfo(requestContext);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetOutRoleInfo_NullRoleList() {
        // Given
        when(roleService.getOuterRoleInfoList("74255", "testAppId"))
                .thenReturn(null);

        // When & Then
        assertThrows(NullPointerException.class, () -> {
            outerRoleService.getOutRoleInfo(requestContext);
        });
    }

    @Test
    void testGetOutRoleInfo_SingleRole() {
        // Given
        List<RestResult.RoleInfoPojo> mockRoleInfoList = Lists.newArrayList();

        RestResult.RoleInfoPojo roleInfo = new RestResult.RoleInfoPojo();
        roleInfo.setRoleCode("SINGLE_ROLE");
        roleInfo.setRoleName("单一角色");
        mockRoleInfoList.add(roleInfo);

        when(roleService.getOuterRoleInfoList("74255", "testAppId"))
                .thenReturn(mockRoleInfoList);

        // When
        Map<String, String> result = outerRoleService.getOutRoleInfo(requestContext);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("单一角色", result.get("SINGLE_ROLE"));
    }

    @Test
    void testGetOutRoleInfo_RoleWithNullCode() {
        // Given
        List<RestResult.RoleInfoPojo> mockRoleInfoList = Lists.newArrayList();

        RestResult.RoleInfoPojo roleInfo1 = new RestResult.RoleInfoPojo();
        roleInfo1.setRoleCode("VALID_ROLE");
        roleInfo1.setRoleName("有效角色");
        mockRoleInfoList.add(roleInfo1);

        RestResult.RoleInfoPojo roleInfo2 = new RestResult.RoleInfoPojo();
        roleInfo2.setRoleCode(null);
        roleInfo2.setRoleName("无效角色");
        mockRoleInfoList.add(roleInfo2);

        when(roleService.getOuterRoleInfoList("74255", "testAppId"))
                .thenReturn(mockRoleInfoList);

        // When
        Map<String, String> result = outerRoleService.getOutRoleInfo(requestContext);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("有效角色", result.get("VALID_ROLE"));
        assertEquals("无效角色", result.get(null));
    }

    @Test
    void testGetOutRoleInfo_RoleWithNullName() {
        // Given
        List<RestResult.RoleInfoPojo> mockRoleInfoList = Lists.newArrayList();

        RestResult.RoleInfoPojo roleInfo = new RestResult.RoleInfoPojo();
        roleInfo.setRoleCode("ROLE_WITH_NULL_NAME");
        roleInfo.setRoleName(null);
        mockRoleInfoList.add(roleInfo);

        when(roleService.getOuterRoleInfoList("74255", "testAppId"))
                .thenReturn(mockRoleInfoList);

        // When
        Map<String, String> result = outerRoleService.getOutRoleInfo(requestContext);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNull(result.get("ROLE_WITH_NULL_NAME"));
    }

    @Test
    void testGetOutRoleInfo_DuplicateRoleCodes() {
        // Given
        List<RestResult.RoleInfoPojo> mockRoleInfoList = Lists.newArrayList();

        RestResult.RoleInfoPojo roleInfo1 = new RestResult.RoleInfoPojo();
        roleInfo1.setRoleCode("DUPLICATE_ROLE");
        roleInfo1.setRoleName("第一个角色");
        mockRoleInfoList.add(roleInfo1);

        RestResult.RoleInfoPojo roleInfo2 = new RestResult.RoleInfoPojo();
        roleInfo2.setRoleCode("DUPLICATE_ROLE");
        roleInfo2.setRoleName("第二个角色");
        mockRoleInfoList.add(roleInfo2);

        when(roleService.getOuterRoleInfoList("74255", "testAppId"))
                .thenReturn(mockRoleInfoList);

        // When
        Map<String, String> result = outerRoleService.getOutRoleInfo(requestContext);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        // 后面的值会覆盖前面的值
        assertEquals("第二个角色", result.get("DUPLICATE_ROLE"));
    }

    @Test
    void testGetOutRoleInfo_NullRequestContext() {
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            outerRoleService.getOutRoleInfo(null);
        });
    }

    @Test
    void testGetOutRoleInfo_RoleServiceThrowsException() {
        // Given
        when(roleService.getOuterRoleInfoList("74255", "testAppId"))
                .thenThrow(new RuntimeException("Role service error"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            outerRoleService.getOutRoleInfo(requestContext);
        });
    }

    @Test
    void testGetOutRoleInfo_LargeRoleList() {
        // Given
        List<RestResult.RoleInfoPojo> mockRoleInfoList = Lists.newArrayList();

        for (int i = 0; i < 100; i++) {
            RestResult.RoleInfoPojo roleInfo = new RestResult.RoleInfoPojo();
            roleInfo.setRoleCode("ROLE_" + i);
            roleInfo.setRoleName("角色 " + i);
            mockRoleInfoList.add(roleInfo);
        }

        when(roleService.getOuterRoleInfoList("74255", "testAppId"))
                .thenReturn(mockRoleInfoList);

        // When
        Map<String, String> result = outerRoleService.getOutRoleInfo(requestContext);

        // Then
        assertNotNull(result);
        assertEquals(100, result.size());
        assertEquals("角色 0", result.get("ROLE_0"));
        assertEquals("角色 99", result.get("ROLE_99"));
    }

    @Test
    void testGetOutRoleInfo_VerifyServiceParameters() {
        // Given
        when(roleService.getOuterRoleInfoList("74255", "testAppId"))
                .thenReturn(Lists.newArrayList());

        // When
        outerRoleService.getOutRoleInfo(requestContext);

        // Then
        verify(roleService, times(1)).getOuterRoleInfoList(
                eq("74255"),  // tenantId from requestContext
                eq("testAppId")      // appId from requestContext
        );
    }

    @Test
    void testGetOutRoleInfo_EmptyStrings() {
        // Given
        List<RestResult.RoleInfoPojo> mockRoleInfoList = Lists.newArrayList();

        RestResult.RoleInfoPojo roleInfo1 = new RestResult.RoleInfoPojo();
        roleInfo1.setRoleCode("");
        roleInfo1.setRoleName("空代码角色");
        mockRoleInfoList.add(roleInfo1);

        RestResult.RoleInfoPojo roleInfo2 = new RestResult.RoleInfoPojo();
        roleInfo2.setRoleCode("EMPTY_NAME_ROLE");
        roleInfo2.setRoleName("");
        mockRoleInfoList.add(roleInfo2);

        when(roleService.getOuterRoleInfoList("74255", "testAppId"))
                .thenReturn(mockRoleInfoList);

        // When
        Map<String, String> result = outerRoleService.getOutRoleInfo(requestContext);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("空代码角色", result.get(""));
        assertEquals("", result.get("EMPTY_NAME_ROLE"));
    }
}
