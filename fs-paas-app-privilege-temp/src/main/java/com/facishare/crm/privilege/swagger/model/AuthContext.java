/**
 * Facishare PAAS Auth
 * This is PAAS auth service project
 *
 * OpenAPI spec version: v1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


package com.facishare.crm.privilege.swagger.model;

import java.util.Objects;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * AuthContext
 */
@javax.annotation.Generated(value = "class io.swagger.codegen.languages.JavaClientCodegen", date = "2017-05-16T15:01:04.279+08:00")
public class AuthContext   {
  @SerializedName("tenantId")
  private String tenantId = null;

  @SerializedName("appId")
  private String appId = null;

  @SerializedName("userId")
  private String userId = null;

  @SerializedName("language")
  private String language = null;

  @SerializedName("outerTenantId")
  private String outerTenantId;

  @SerializedName("outerUserId")
  private String outerUserId;

  @SerializedName("properties")
  private Map<String, String> properties = new HashMap<String, String>();

  @SerializedName("objectProperties")
  private Map<String, Object> objectProperties = new HashMap<String, Object>();

  public AuthContext tenantId(String tenantId) {
    this.tenantId = tenantId;
    return this;
  }

   /**
   * Get tenantId
   * @return tenantId
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getTenantId() {
    return tenantId;
  }

  public void setTenantId(String tenantId) {
    this.tenantId = tenantId;
  }

  public AuthContext appId(String appId) {
    this.appId = appId;
    return this;
  }

   /**
   * Get appId
   * @return appId
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getAppId() {
    return appId;
  }

  public void setAppId(String appId) {
    this.appId = appId;
  }

  public AuthContext userId(String userId) {
    this.userId = userId;
    return this;
  }

   /**
   * Get userId
   * @return userId
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getUserId() {
    return userId;
  }

  public void setUserId(String userId) {
    this.userId = userId;
  }

  public AuthContext properties(Map<String, String> properties) {
    this.properties = properties;
    return this;
  }

  public AuthContext putPropertiesItem(String key, String propertiesItem) {
    this.properties.put(key, propertiesItem);
    return this;
  }
  /**
   * Get languageLocale
   * @return languageLocale
   **/
  @ApiModelProperty(example = "null", value = "")
  public String getLanguageLocale() {
    return language;
  }

  public String getOuterUserId() {
    return outerUserId;
  }

  public void setOuterUserId(String outerUserId) {
    this.outerUserId = outerUserId;
  }

  public void setLanguageLocale(String languageLocale) {
    this.language = languageLocale;
  }

  public AuthContext languageLocale(String languageLocale) {
    this.language = languageLocale;
    return this;
  }
   /**
   * Get properties
   * @return properties
  **/
  @ApiModelProperty(example = "null", value = "")
  public Map<String, String> getProperties() {
    return properties;
  }

  public void setProperties(Map<String, String> properties) {
    this.properties = properties;
  }

  public AuthContext objectProperties(Map<String, Object> objectProperties) {
    this.objectProperties = objectProperties;
    return this;
  }

  public AuthContext putObjectPropertiesItem(String key, Object objectPropertiesItem) {
    this.objectProperties.put(key, objectPropertiesItem);
    return this;
  }

   /**
   * Get objectProperties
   * @return objectProperties
  **/
  @ApiModelProperty(example = "null", value = "")
  public Map<String, Object> getObjectProperties() {
    return objectProperties;
  }

  public void setObjectProperties(Map<String, Object> objectProperties) {
    this.objectProperties = objectProperties;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AuthContext authContext = (AuthContext) o;
    return Objects.equals(this.tenantId, authContext.tenantId) &&
        Objects.equals(this.appId, authContext.appId) &&
        Objects.equals(this.userId, authContext.userId) &&
        Objects.equals(this.properties, authContext.properties) &&
        Objects.equals(this.objectProperties, authContext.objectProperties);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tenantId, appId, userId, properties, objectProperties);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AuthContext {\n");
    
    sb.append("    tenantId: ").append(toIndentedString(tenantId)).append("\n");
    sb.append("    appId: ").append(toIndentedString(appId)).append("\n");
    sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
    sb.append("    properties: ").append(toIndentedString(properties)).append("\n");
    sb.append("    objectProperties: ").append(toIndentedString(objectProperties)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  public String getOuterTenantId() {
    return outerTenantId;
  }

  public void setOuterTenantId(String outerTenantId) {
    this.outerTenantId = outerTenantId;
  }
}

