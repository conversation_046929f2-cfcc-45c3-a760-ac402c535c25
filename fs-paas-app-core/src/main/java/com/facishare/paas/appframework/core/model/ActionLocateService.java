package com.facishare.paas.appframework.core.model;

/**
 * action locate service interface
 * <p>
 * Created by liyiguang on 2017/6/17.
 */
public interface ActionLocateService {
    Action locateAction(ActionContext context, String payload);

    Action locateAction(ActionContext context, Object arg);

    /**
     * @param context
     * @param arg
     * @param resultType Action的返回值类型，如果此参数为null，则只会调用本地的Action，否则可能走RPC调用
     * @return
     */
    Action locateAction(ActionContext context, Object arg, Class resultType);

    Action locateRemoteAction(ActionContext context, Object arg, Class resultType);
}
