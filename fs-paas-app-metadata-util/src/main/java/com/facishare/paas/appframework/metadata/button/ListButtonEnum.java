package com.facishare.paas.appframework.metadata.button;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.button.DefaultButtonAction.*;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/09/23
 */
public enum ListButtonEnum {
    DEFAULT_STANDARD_BUTTON(CREATE, UPDATE, CHANGE_OWNER, INVALID, ADD_TEAM_MEMBER, DELETE_TEAM_MEMBER, LOCK, UNLOCK);

    ListButtonEnum(DefaultButtonAction... objectActions) {
        this.objectActions = Lists.newArrayList(objectActions);
    }

    private List<DefaultButtonAction> objectActions;

    public List<String> getButtonApiNames() {
        return objectActions.stream()
                .map(DefaultButtonAction::getObjectAction)
                .map(ObjectAction::getButtonApiName)
                .collect(Collectors.toList());
    }

    public List<IUdefButton> getButton() {
        return objectActions.stream()
                .map(DefaultButtonAction::getButton)
                .collect(Collectors.toList());
    }
}
