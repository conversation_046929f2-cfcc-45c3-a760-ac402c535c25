package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.util.LicenseConstants;
import com.facishare.paas.appframework.metadata.ComponentDefineType;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.RelatedObjectDescribeStructure;
import com.facishare.paas.appframework.metadata.layout.LayoutComponents;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.layout.MasterDetailGroupComponentBuilder;
import com.facishare.paas.appframework.metadata.layout.RelatedObjectGroupComponentBuilder;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.MasterDetail;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.predef.controller.StandardDesignerLayoutBusinessComponentController.Arg;
import static com.facishare.paas.appframework.core.predef.controller.StandardDesignerLayoutBusinessComponentController.Result;
import static com.facishare.paas.metadata.api.describe.IObjectDescribe.ORIGINAL_DESCRIBE_API_NAME;
import static java.util.stream.Collectors.toList;

public class StandardDesignerLayoutBusinessComponentController extends PreDefineController<Arg, Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    protected List<IComponent> getSpecialDetailPageBusinessComponents(IObjectDescribe describe) {
        String tenantId = controllerContext.getTenantId();
        String objectApiName = describe.getApiName();
        List<IComponent> components = Lists.newArrayList();
        //摘要卡片
        if (LayoutComponents.SUPPORT_SUMMERY_CARD_OBJECTS.contains(objectApiName)) {
            components.add(LayoutComponents.buildSummaryCardComponent());
        }
        //附件组件
        if (LayoutComponents.SUPPORT_ATTACH_OBJECTS.contains(objectApiName)) {
            components.add(LayoutComponents.buildAttachComponent());
        }

        if (ObjectAPINameMapping.Account.getApiName().equals(objectApiName)) {
            //客户层级关系
            //需要购买资源包
            Set<String> keys = Sets.newHashSet(LicenseConstants.ModuleCode.ACCOUNT_HIERARCHY_APP, LicenseConstants.ModuleCode.CONTACT_RELATION_APP);
            Map<String, Boolean> existModule = serviceFacade.existModule(tenantId, keys);
            if (existModule.getOrDefault(LicenseConstants.ModuleCode.ACCOUNT_HIERARCHY_APP, false)) {
                components.add(LayoutComponents.buildAccountHierarchyComponent());
            }
            if (existModule.getOrDefault(LicenseConstants.ModuleCode.CONTACT_RELATION_APP, false)) {
                components.add(LayoutComponents.buildContactRelationComponent());
            }
            //作战地图
            if (AppFrameworkConfig.isSupportAccountOperationMap(tenantId)) {
                components.add(LayoutComponents.buildAccountOperationMapComponent());
            }
        }
        //产品属性
        if (Utils.PRODUCT_API_NAME.equals(objectApiName)) {
            User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
            String moduleConfig = serviceFacade.findTenantConfig(user, "is_open_attribute");
            if (moduleConfig != null && Objects.equals(moduleConfig, "1")) {
                components.add(LayoutComponents.buildProductAttributeComponent());
            }
        }

        //市场活动层级
        if (Utils.MARKETING_EVENT_API_NAME.equals(objectApiName)) {
            components.add(LayoutComponents.buildMarketingEventPathComponent());
        }
        return components;
    }

    protected List<IComponent> filterDetailPageComponents(List<IComponent> components, IObjectDescribe describe) {
        return components;
    }

    protected List<IComponent> filterEditPageComponents(List<IComponent> components, IObjectDescribe describe) {
        return components;
    }

    @Override
    protected Result doService(Arg arg) {
        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), arg.getObjectApiName());
        List<IComponent> components;
        //流程布局的业务组件
        if (Strings.isNullOrEmpty(arg.getLayoutType())) {
            components = getFlowBusinessComponents(describe);
        } else {
            switch (arg.getLayoutType()) {
                case LayoutTypes.DETAIL:
                    components = getDetailPageBusinessComponents(describe);
                    components = filterDetailPageComponents(components, describe);
                    break;
                case LayoutTypes.EDIT:
                    components = getEditPageBusinessComponents(describe);
                    components = filterEditPageComponents(components, describe);
                    break;
                default:
                    components = Lists.newArrayList();
                    break;
            }
        }
        List<Map> maps = components.stream().map(x -> ComponentExt.of(x).toMap()).collect(Collectors.toList());
        return Result.builder().components(maps).build();
    }

    private List<IComponent> getDetailPageBusinessComponents(IObjectDescribe describe) {
        String objectApiName = describe.getApiName();
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);

        List<IComponent> components = Lists.newArrayList();
        List<IComponent> specialBusinessComponents = getSpecialDetailPageBusinessComponents(describe);
        if (CollectionUtils.notEmpty(specialBusinessComponents)) {
            components.addAll(getSpecialDetailPageBusinessComponents(describe));
        }

        List<IObjectDescribe> relatedDescribes = serviceFacade.findRelatedDescribes(controllerContext.getTenantId(), objectApiName);
        List<RelatedObjectDescribeStructure> detailObjects = describeExt.getDetailObjectDescribeStructures(relatedDescribes);
        List<RelatedObjectDescribeStructure> lookupObjects = describeExt.getRelatedObjectDescribeStructures(relatedDescribes);
        //根据配置中心的配置去掉不展示的关联对象
        lookupObjects.removeIf(x -> DefObjConstants.isReferenceObjectInvisible(objectApiName, x.getRelatedObjectDescribe().getApiName()));

        //从对象
        components.addAll(MasterDetailGroupComponentBuilder.builder().detailObjectsDescribeList(detailObjects).build().getComponentListForNewDesigner());
        //关联对象
        components.addAll(RelatedObjectGroupComponentBuilder.builder().objectDescribe(describeExt)
                .user(RequestContextManager.getContext().getUser())
                .relatedObjectDescribeList(lookupObjects).build().getComponentListForNewDesigner());
        components.forEach(component -> component.setDefineType(ComponentDefineType.BUSINESS.getType()));

        //根据配置过滤组件
        components.removeIf(x -> DefObjConstants.isComponentInvisible(objectApiName, x.getName()));

        return components;
    }

    private List<IComponent> getEditPageBusinessComponents(IObjectDescribe describe) {
        List<IObjectDescribe> detailDescribes = serviceFacade.findDetailDescribesCreateWithMaster(controllerContext.getTenantId(), describe.getApiName());
        Map<String, String> changeToOriginalApiNameMap = CollectionUtils.nullToEmpty(infraServiceFacade.batchFindOriginalApiNameByChangOrderList(controllerContext.getUser(),
                detailDescribes.stream().map(IObjectDescribe::getApiName).collect(toList())));
        List<IComponent> businessComponents = detailDescribes.stream().map(x -> {
            IComponent detailObjComponent = LayoutComponents.buildDetailObjectComponent(x);
            if (StringUtils.isNotEmpty(changeToOriginalApiNameMap.get(x.getApiName()))) {
                detailObjComponent.set(ORIGINAL_DESCRIBE_API_NAME, changeToOriginalApiNameMap.get(x.getApiName()));
            }
            //主从一起新建是必填的从对象组件不能删除
            MasterDetail masterDetail = ObjectDescribeExt.of(x).getMasterDetailField().get();
            ComponentExt.of(detailObjComponent).setUnDeletable(Boolean.TRUE.equals(masterDetail.getIsRequiredWhenMasterCreate()));
            return detailObjComponent;
        }).collect(toList());
        return businessComponents;
    }

    private List<IComponent> getFlowBusinessComponents(IObjectDescribe describe) {
        List<IObjectDescribe> detailDescribes = serviceFacade.findDetailDescribesCreateWithMaster(controllerContext.getTenantId(), describe.getApiName());
        Map<String, String> changeToOriginalApiNameMap = CollectionUtils.nullToEmpty(infraServiceFacade.batchFindOriginalApiNameByChangOrderList(controllerContext.getUser(),
                detailDescribes.stream().map(IObjectDescribe::getApiName).collect(toList())));
        List<IComponent> businessComponents = detailDescribes.stream().map(x -> {
            IComponent detailObjComponent = LayoutComponents.buildDetailObjectComponent(x);
            if (StringUtils.isNotEmpty(changeToOriginalApiNameMap.get(x.getApiName()))) {
                detailObjComponent.set(ORIGINAL_DESCRIBE_API_NAME, changeToOriginalApiNameMap.get(x.getApiName()));
            }
            return detailObjComponent;
        }).collect(toList());
        return businessComponents;
    }

    @Data
    public static class Arg {
        private String objectApiName;
        private String layoutType;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private List<Map> components;
    }
}
