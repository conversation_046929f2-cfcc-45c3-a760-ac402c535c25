package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.metadata.button.FindAndFilterButtonsByDataManager;
import com.facishare.paas.appframework.metadata.button.FindAndFilterButtonsByDataProvider;
import com.facishare.paas.appframework.metadata.button.FindAndFilterButtonsByDataProxy;
import com.facishare.paas.metadata.api.IUdefButton;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by zhaooju on 2022/10/14
 */
@Service
@ServiceModule("FindAndFilterButtonsByDataProvider")
public class ObjectFindAndFilterButtonsByDataProviderService {
    @Autowired
    private FindAndFilterButtonsByDataManager manager;

    @ServiceMethod("findAndFilterButtonsByData")
    public FindAndFilterButtonsByDataProxy.Result findAndFilterButtonsByData(FindAndFilterButtonsByDataProxy.Arg arg, ServiceContext context) {
        FindAndFilterButtonsByDataProvider provider = manager.getLocalProvider(arg.getDescribeApiName());
        List<IUdefButton> buttons = provider.findAndFilterButtonsByData(context.getUser(), arg.convertTo());
        return FindAndFilterButtonsByDataProxy.Result.of(buttons);
    }
}
