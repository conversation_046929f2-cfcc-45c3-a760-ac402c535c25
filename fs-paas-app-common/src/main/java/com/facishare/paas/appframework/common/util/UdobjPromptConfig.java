package com.facishare.paas.appframework.common.util;

import com.facishare.paas.appframework.common.service.model.AIFormulaConfigDTO;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>ui
 * @Data : 2025/3/18
 * @Description : 读取提示词
 */
@Slf4j
public class UdobjPromptConfig {
	private static final String AI_FORMULA_PROMPT_FILE = "fs-udobj-ai-formula-prompt";
	private static String AI_FORMULA_PROMPT = AIFormulaConfigDTO.GENERATE_EXPRESSION;

	public static String getFormulaPrompt() {
		return AI_FORMULA_PROMPT;
	}

	static {
		ConfigFactory.getConfig(AI_FORMULA_PROMPT_FILE, (IConfig config) -> {
			try {
				AI_FORMULA_PROMPT = config.getString();
				log.info("prompt load {} success, new date: {}", AI_FORMULA_PROMPT_FILE, AI_FORMULA_PROMPT);
			} catch (Exception e) {
				log.error("prompt load {} failed, old date: {}", AI_FORMULA_PROMPT_FILE, AI_FORMULA_PROMPT, e);
			}
		});
	}
}
