package com.facishare.paas.appframework.metadata.layout.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class AggregateComponentInfoTest {

  private AggregateComponentInfo aggregateComponentInfo;

  @BeforeEach
  void setUp() {
    aggregateComponentInfo = new AggregateComponentInfo();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试AggregateComponentInfo的无参构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试无参构造函数")
  void testDefaultConstructor() {
    AggregateComponentInfo info = new AggregateComponentInfo();
    assertNotNull(info, "无参构造函数创建的对象不应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试AggregateComponentInfo的Map构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = Maps.newHashMap();
    map.put(IAggregateComponentInfo.RENDER_TYPE, "chart");
    map.put(IAggregateComponentInfo.PAGE_TYPE, "dashboard");
    
    // 创建汇总字段数据
    Map<String, Object> summaryField = Maps.newHashMap();
    summaryField.put("field_name", "total_amount");
    summaryField.put("aggregate_type", "sum");
    List<Map<String, Object>> summaryFields = Lists.newArrayList(summaryField);
    map.put(IAggregateComponentInfo.SUMMARY_FIELDS, summaryFields);
    
    AggregateComponentInfo info = new AggregateComponentInfo(map);
    assertNotNull(info, "Map构造函数创建的对象不应为空");
    assertEquals("chart", info.getRenderType(), "应正确设置渲染类型");
    assertEquals("dashboard", info.getPageType(), "应正确设置页面类型");
    assertEquals(1, info.getSummaryFields().size(), "应正确设置汇总字段");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getRenderType方法
   */
  @Test
  @DisplayName("正常场景 - 测试getRenderType方法")
  void testGetRenderType() {
    // 初始状态应为null
    assertNull(aggregateComponentInfo.getRenderType(), "初始渲染类型应为null");
    
    // 通过Map构造函数设置渲染类型
    Map<String, Object> map = Maps.newHashMap();
    map.put(IAggregateComponentInfo.RENDER_TYPE, "table");
    AggregateComponentInfo info = new AggregateComponentInfo(map);
    assertEquals("table", info.getRenderType(), "应正确获取渲染类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getPageType方法
   */
  @Test
  @DisplayName("正常场景 - 测试getPageType方法")
  void testGetPageType() {
    // 初始状态应为null
    assertNull(aggregateComponentInfo.getPageType(), "初始页面类型应为null");
    
    // 通过Map构造函数设置页面类型
    Map<String, Object> map = Maps.newHashMap();
    map.put(IAggregateComponentInfo.PAGE_TYPE, "report");
    AggregateComponentInfo info = new AggregateComponentInfo(map);
    assertEquals("report", info.getPageType(), "应正确获取页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试copy方法
   */
  @Test
  @DisplayName("正常场景 - 测试copy方法")
  void testCopy() {
    // 设置一些属性
    Map<String, Object> map = Maps.newHashMap();
    map.put(IAggregateComponentInfo.RENDER_TYPE, "chart");
    map.put(IAggregateComponentInfo.PAGE_TYPE, "dashboard");
    
    AggregateComponentInfo original = new AggregateComponentInfo(map);
    IAggregateComponentInfo copied = original.copy();
    
    assertNotNull(copied, "复制的对象不应为空");
    assertNotSame(original, copied, "复制的对象应是不同的实例");
    assertEquals(original.getRenderType(), copied.getRenderType(), "复制的对象应有相同的渲染类型");
    assertEquals(original.getPageType(), copied.getPageType(), "复制的对象应有相同的页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getSummaryFields方法 - 正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试getSummaryFields方法")
  void testGetSummaryFields_NormalScenario() {
    // 创建汇总字段数据
    Map<String, Object> field1 = Maps.newHashMap();
    field1.put("field_name", "amount");
    field1.put("aggregate_type", "sum");
    
    Map<String, Object> field2 = Maps.newHashMap();
    field2.put("field_name", "count");
    field2.put("aggregate_type", "count");
    
    List<Map<String, Object>> summaryFieldsData = Lists.newArrayList(field1, field2);
    
    Map<String, Object> map = Maps.newHashMap();
    map.put(IAggregateComponentInfo.SUMMARY_FIELDS, summaryFieldsData);
    
    AggregateComponentInfo info = new AggregateComponentInfo(map);
    List<ISummaryComponentInfo> summaryFields = info.getSummaryFields();
    
    assertNotNull(summaryFields, "汇总字段列表不应为空");
    assertEquals(2, summaryFields.size(), "应有2个汇总字段");
    assertTrue(summaryFields.get(0) instanceof SummaryComponentInfo, "应是SummaryComponentInfo实例");
    assertTrue(summaryFields.get(1) instanceof SummaryComponentInfo, "应是SummaryComponentInfo实例");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getSummaryFields方法 - 空列表场景
   */
  @Test
  @DisplayName("边界场景 - 测试getSummaryFields空列表")
  void testGetSummaryFields_EmptyList() {
    Map<String, Object> map = Maps.newHashMap();
    map.put(IAggregateComponentInfo.SUMMARY_FIELDS, Lists.newArrayList());
    
    AggregateComponentInfo info = new AggregateComponentInfo(map);
    List<ISummaryComponentInfo> summaryFields = info.getSummaryFields();
    
    assertNotNull(summaryFields, "结果不应为空");
    assertTrue(summaryFields.isEmpty(), "应返回空列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getSummaryFields方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试getSummaryFields null值")
  void testGetSummaryFields_NullValue() {
    Map<String, Object> map = Maps.newHashMap();
    map.put(IAggregateComponentInfo.SUMMARY_FIELDS, null);
    
    AggregateComponentInfo info = new AggregateComponentInfo(map);
    List<ISummaryComponentInfo> summaryFields = info.getSummaryFields();
    
    assertNotNull(summaryFields, "结果不应为空");
    assertTrue(summaryFields.isEmpty(), "null值应返回空列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getSummaryFields方法 - 未设置字段场景
   */
  @Test
  @DisplayName("边界场景 - 测试getSummaryFields未设置字段")
  void testGetSummaryFields_NoField() {
    // 不设置SUMMARY_FIELDS字段
    AggregateComponentInfo info = new AggregateComponentInfo();
    List<ISummaryComponentInfo> summaryFields = info.getSummaryFields();
    
    assertNotNull(summaryFields, "结果不应为空");
    assertTrue(summaryFields.isEmpty(), "未设置字段应返回空列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口实现
   */
  @Test
  @DisplayName("正常场景 - 测试接口实现")
  void testInterfaceImplementation() {
    // 验证实现了IAggregateComponentInfo接口
    assertTrue(aggregateComponentInfo instanceof IAggregateComponentInfo, 
               "应实现IAggregateComponentInfo接口");
    
    // 验证实现了JsonCompatible接口
    assertTrue(aggregateComponentInfo instanceof com.facishare.paas.metadata.api.JsonCompatible, 
               "应实现JsonCompatible接口");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试继承的AbstractComponentInfoDocument功能
   */
  @Test
  @DisplayName("正常场景 - 测试继承功能")
  void testInheritedFunctionality() {
    // 验证继承自AbstractComponentInfoDocument
    assertTrue(aggregateComponentInfo instanceof AbstractComponentInfoDocument, 
               "应继承自AbstractComponentInfoDocument");
    
    // 通过Map构造函数测试继承的功能
    Map<String, Object> map = Maps.newHashMap();
    map.put("custom_field", "custom_value");
    map.put(IAggregateComponentInfo.RENDER_TYPE, "pie_chart");
    
    AggregateComponentInfo info = new AggregateComponentInfo(map);
    assertEquals("pie_chart", info.getRenderType(), "应正确处理渲染类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试复杂的汇总字段数据
   */
  @Test
  @DisplayName("正常场景 - 测试复杂汇总字段数据")
  void testComplexSummaryFields() {
    // 创建复杂的汇总字段数据
    Map<String, Object> field1 = Maps.newHashMap();
    field1.put("field_name", "revenue");
    field1.put("aggregate_type", "sum");
    field1.put("display_name", "总收入");
    field1.put("format", "currency");
    
    Map<String, Object> field2 = Maps.newHashMap();
    field2.put("field_name", "order_count");
    field2.put("aggregate_type", "count");
    field2.put("display_name", "订单数量");
    
    Map<String, Object> field3 = Maps.newHashMap();
    field3.put("field_name", "avg_amount");
    field3.put("aggregate_type", "avg");
    field3.put("display_name", "平均金额");
    field3.put("precision", 2);
    
    List<Map<String, Object>> summaryFieldsData = Lists.newArrayList(field1, field2, field3);
    
    Map<String, Object> map = Maps.newHashMap();
    map.put(IAggregateComponentInfo.SUMMARY_FIELDS, summaryFieldsData);
    map.put(IAggregateComponentInfo.RENDER_TYPE, "dashboard");
    map.put(IAggregateComponentInfo.PAGE_TYPE, "analytics");
    
    AggregateComponentInfo info = new AggregateComponentInfo(map);
    
    // 验证基本属性
    assertEquals("dashboard", info.getRenderType(), "渲染类型应正确");
    assertEquals("analytics", info.getPageType(), "页面类型应正确");
    
    // 验证汇总字段
    List<ISummaryComponentInfo> summaryFields = info.getSummaryFields();
    assertEquals(3, summaryFields.size(), "应有3个汇总字段");
    
    // 验证每个字段都是正确的类型
    for (ISummaryComponentInfo field : summaryFields) {
      assertTrue(field instanceof SummaryComponentInfo, "每个字段都应是SummaryComponentInfo实例");
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试copy方法的深度复制
   */
  @Test
  @DisplayName("正常场景 - 测试copy方法深度复制")
  void testCopyDeepCopy() {
    // 创建包含汇总字段的原始对象
    Map<String, Object> summaryField = Maps.newHashMap();
    summaryField.put("field_name", "total");
    summaryField.put("aggregate_type", "sum");
    
    Map<String, Object> map = Maps.newHashMap();
    map.put(IAggregateComponentInfo.RENDER_TYPE, "chart");
    map.put(IAggregateComponentInfo.SUMMARY_FIELDS, Lists.newArrayList(summaryField));
    
    AggregateComponentInfo original = new AggregateComponentInfo(map);
    IAggregateComponentInfo copied = original.copy();
    
    // 验证复制的对象
    assertNotNull(copied, "复制的对象不应为空");
    assertEquals(original.getRenderType(), copied.getRenderType(), "渲染类型应相同");
    assertEquals(original.getSummaryFields().size(), copied.getSummaryFields().size(), "汇总字段数量应相同");
    
    // 验证是不同的实例
    assertNotSame(original, copied, "应是不同的实例");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试多种渲染类型
   */
  @Test
  @DisplayName("正常场景 - 测试多种渲染类型")
  void testVariousRenderTypes() {
    String[] renderTypes = {"chart", "table", "card", "list", "grid", "dashboard"};
    
    for (String renderType : renderTypes) {
      Map<String, Object> map = Maps.newHashMap();
      map.put(IAggregateComponentInfo.RENDER_TYPE, renderType);
      
      AggregateComponentInfo info = new AggregateComponentInfo(map);
      assertEquals(renderType, info.getRenderType(), "应正确设置渲染类型: " + renderType);
    }
  }
}
