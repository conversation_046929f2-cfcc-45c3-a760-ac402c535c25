package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectImportConfig;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.cache.RedisDao;
import com.facishare.paas.appframework.metadata.query.SearchQueryContext;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IDataRightsParameter;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Charsets;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.common.hash.Hashing;

import java.text.DecimalFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * create by zhaoju on 2021/02/19
 */
public abstract class BaseExportVerifyAction extends PreDefineAction<StandardExportVerifyAction.Arg, StandardExportVerifyAction.Result> {
    /**
     * searchQuery， verify中根据用户选择查询导出数量
     */
    protected SearchTemplateQuery searchQuery;
    /**
     * 当前导出数量
     */
    protected int totalCount = 0;
    protected Map<String, IObjectDescribe> detailDescribeMap = Maps.newHashMap();

    protected abstract int getExportRowsThrottle();

    @Override
    protected List<String> getDataPrivilegeIds(StandardExportVerifyAction.Arg arg) {
        return null;
    }

    @Override
    protected void before(StandardExportVerifyAction.Arg arg) {
        initDetailDescribes();
        super.before(arg);
        this.searchQuery = generateSearchQuery(actionContext.getUser(), arg.getSearchTemplateId(), arg.getSearchQueryInfo(), arg.getDataIdList());
    }

    private void initDetailDescribes() {
        //初始化从对象描述
        if (isExportDetailObjects()) {
            Map<String, IObjectDescribe> detailDescribes = serviceFacade.findObjects(actionContext.getTenantId(), arg.getDetailArg().getDetailObjectApiNames());
            detailDescribes.forEach((detailApiName, detailDescribe) -> {
                if (ObjectDescribeExt.of(detailDescribe).isSlaveObject()) {
                    detailDescribeMap.put(detailApiName, detailDescribe);
                }
            });
        }
    }

    private SearchTemplateQuery generateSearchQuery(User user, String searchTemplateId,
                                                    String searchQueryInfo, List<String> dataIdList) {
        if (serviceFacade.isSupportOrFilter(actionContext.getTenantId(), actionContext.getObjectApiName())) {
            Query query = defineQuery(user, searchTemplateId, searchQueryInfo, dataIdList);
            query.handlePlainContentFilter();
            return (SearchTemplateQuery) query.toSearchTemplateQuery();
        }
        SearchTemplateQuery searchTemplateQuery = serviceFacade.getSearchTemplateQueryWithIgnoreSceneFilter(user, ObjectDescribeExt.of(objectDescribe),
                searchTemplateId, arg.getSearchTemplateType(), searchQueryInfo, false, arg.isIgnoreSceneFilter(), arg.isIgnoreSceneRecordType());
        //忽略掉前端传过来的offset
        searchTemplateQuery.setOffset(0);
        if (CollectionUtils.notEmpty(dataIdList)) {
            SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.IN, IObjectData.ID, dataIdList);
        }
        FilterExt.handlePlainContentFilter(searchTemplateQuery.getFilters());
        return searchTemplateQuery;
    }

    protected final boolean isPlainContentSearch() {
        return searchQuery.getFilters().stream().anyMatch(x -> Objects.equals(FilterExt.FilterValueTypes.PLAIN_CONTENT, x.getValueType()));
    }

    private Query defineQuery(User user, String searchTemplateId, String searchQueryInfo, List<String> dataIdList) {
        SearchQueryContext queryContext = SearchQueryContext.builder()
                .searchTemplateType(arg.getSearchTemplateType())
                .templateId(searchTemplateId)
                .isIgnoreSceneFilter(arg.isIgnoreSceneFilter())
                .isIgnoreSceneRecordType(arg.isIgnoreSceneRecordType())
                .isRelatedPage(false)
                .build();
        Query query = serviceFacade.findSearchQuery(user, objectDescribe, searchQueryInfo, queryContext);
        if (CollectionUtils.notEmpty(dataIdList)) {
            SearchQuery searchQuery = query.getSearchQuery()
                    .map(it -> it.and(FilterExt.of(Operator.IN, IObjectData.ID, dataIdList).getFilter()))
                    .orElse(SearchQueryImpl.filter(FilterExt.of(Operator.IN, IObjectData.ID, dataIdList).getFilter()));
            query.setSearchQuery(searchQuery);
        }
        // 忽略掉前端传过来的offset
        query.setOffset(0);
        // 查询精确总数，不返回数据
        query.setFindExplicitTotalNum(true);
        return query;
    }

    @Override
    protected StandardExportVerifyAction.Result doAct(StandardExportVerifyAction.Arg arg) {
        //校验数量,不能超过10万条
        validateCount();
        customValidate();
        return StandardExportVerifyAction.Result.builder().success(true).build();
    }

    protected void customValidate() {
    }

    protected void validateCount() {
        totalCount = validateThrottle();
        if (isExportLimitExceeded(totalCount)) {
            log.warn("Export validate Error, TenantId:{}, UserId:{}, arg:{}.", actionContext.getTenantId(),
                    actionContext.getUser().getUserId(), arg);
            throw new ValidateException(buildThrottleExceedMessage());
        }
    }

    private boolean isExportLimitExceeded(int count) {
        return count > getExportRowsThrottle();
    }

    protected boolean isExportVip() {
        return AppFrameworkConfig.getExportVipTenantIds().contains(actionContext.getTenantId());
    }

    protected int validateThrottle() {
        int totalCount = 0;
        ISearchTemplateQuery searchTemplateQuery = SearchTemplateQueryExt.of(searchQuery).copy();
        searchTemplateQuery.setLimit(1);
        searchTemplateQuery.setOffset(0);
        // 查询精确总数，不返回数据
        searchTemplateQuery.setFindExplicitTotalNum(true);
        QueryResult<IObjectData> data = findObjectByQuery(actionContext.getUser(), objectDescribe, (SearchTemplateQuery) searchTemplateQuery);
        totalCount += data.getTotalNumber();
        if (isExportLimitExceeded(totalCount)) {
            return totalCount;
        }
        if (!isMdExportLimit() && isExportDetailObjects() && CollectionUtils.notEmpty(detailDescribeMap)) {
            for (IObjectDescribe detailObjectDescribe : detailDescribeMap.values()) {
                ISearchTemplateQuery detailSearchQuery = SearchTemplateQueryExt.of(searchQuery).copy();
                CollectionUtils.nullToEmpty(detailSearchQuery.getFilters()).forEach(x -> x.setIsMasterField(true));
                IDataRightsParameter dataRightsParameter = detailSearchQuery.getDataRightsParameter();
                ObjectDescribeExt.of(detailObjectDescribe).getMasterDetailFieldDescribe().ifPresent(it -> {
                    if (Objects.nonNull(dataRightsParameter)) {
                        dataRightsParameter.setIsDetailObject(true);
                        dataRightsParameter.setMasterIdFieldApiName(it.getApiName());
                        dataRightsParameter.setMasterObjectApiName(it.getTargetApiName());
                    }
                });
                detailSearchQuery.setDataRightsParameter(dataRightsParameter);
                QueryResult<IObjectData> detailData = findObjectByQuery(actionContext.getUser(), detailObjectDescribe, (SearchTemplateQuery) detailSearchQuery);
                totalCount += detailData.getTotalNumber();
                if (isExportLimitExceeded(totalCount)) {
                    return totalCount;
                }
            }
        }
        if (ObjectImportConfig.isGrayExportTotalCount(actionContext.getTenantId())) {
            RedisDao redisDao = serviceFacade.getBean(RedisDao.class);
            redisDao.set(getRedisKey(), String.valueOf(totalCount), 30);
        }
        return totalCount;
    }

    protected boolean isMdExportLimit() {
        return UdobjGrayConfig.isAllow("md_export_limit", actionContext.getTenantId()) || isPlainContentSearch();
    }


    private String getRedisKey() {
        String md5Key = Hashing.sha256().newHasher()
                .putString(Strings.nullToEmpty(arg.getSearchTemplateId()), Charsets.UTF_8)
                .putString(Strings.nullToEmpty(arg.getSearchQueryInfo()), Charsets.UTF_8)
                .putString(Strings.nullToEmpty(arg.getSearchTemplateType()), Charsets.UTF_8)
                .putString(Objects.toString(arg.getDataIdList()), Charsets.UTF_8)
                .putString(actionContext.getTenantId(), Charsets.UTF_8)
                .putString(actionContext.getObjectApiName(), Charsets.UTF_8)
                .hash()
                .toString();
        return String.format("%s_%s_%s_%s", actionContext.getTenantId(), actionContext.getUser().getUserIdOrOutUserIdIfOutUser(),
                actionContext.getObjectApiName(), md5Key);
    }

    protected String buildThrottleExceedMessage() {
        return I18N.text(I18NKey.EXPORT_EXCEED_THROTTLE, new DecimalFormat(",###").format(getExportRowsThrottle()));
    }

    private QueryResult<IObjectData> findObjectByQuery(User user, IObjectDescribe describe, SearchTemplateQuery query) {
        return serviceFacade.findBySearchQuery(user, describe, describe.getApiName(), query);
    }

    public boolean isExportDetailObjects() {
        return Objects.nonNull(arg.getDetailArg()) && CollectionUtils.notEmpty(arg.getDetailArg().getDetailObjectApiNames());
    }

}