/**
 * Facishare PAAS Organization
 * This is PAAS organization service project
 *
 * OpenAPI spec version: v1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


package com.facishare.crm.privilege.swagger.api;

import com.facishare.crm.privilege.swagger.ApiCallback;
import com.facishare.crm.privilege.swagger.ApiClient;
import com.facishare.crm.privilege.swagger.ApiException;
import com.facishare.crm.privilege.swagger.ApiResponse;
import com.facishare.crm.privilege.swagger.Configuration;
import com.facishare.crm.privilege.swagger.Pair;
import com.facishare.crm.privilege.swagger.ProgressRequestBody;
import com.facishare.crm.privilege.swagger.ProgressResponseBody;
import com.facishare.crm.privilege.swagger.model.BaseResult;
import com.facishare.crm.privilege.swagger.model.PojoArg;
import com.facishare.crm.privilege.swagger.model.StatusArg;
import com.facishare.crm.privilege.swagger.model.StatusBatchArg;
import com.facishare.crm.privilege.swagger.model.UserArg;
import com.google.gson.reflect.TypeToken;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UpdateApi {
    private ApiClient apiClient;

    public UpdateApi() {
        this(Configuration.getDefaultApiClient());
    }

    public UpdateApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /* Build call for batchChgStatus */
    private okhttp3.Call batchChgStatusCall(StatusBatchArg statusBatchArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = statusBatchArg;
        
        // verify the required parameter 'statusBatchArg' is set
        if (statusBatchArg == null) {
            throw new ApiException("Missing the required parameter 'statusBatchArg' when calling batchChgStatus(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/group/batch/status".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * batchChgStatus
     * batch  change  some  groups&#39;  status,stop  or  start
     * @param statusBatchArg  (required)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult batchChgStatus(StatusBatchArg statusBatchArg) throws ApiException {
        ApiResponse<BaseResult> resp = batchChgStatusWithHttpInfo(statusBatchArg);
        return resp.getData();
    }

    /**
     * batchChgStatus
     * batch  change  some  groups&#39;  status,stop  or  start
     * @param statusBatchArg  (required)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> batchChgStatusWithHttpInfo(StatusBatchArg statusBatchArg) throws ApiException {
        okhttp3.Call call = batchChgStatusCall(statusBatchArg, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * batchChgStatus (asynchronously)
     * batch  change  some  groups&#39;  status,stop  or  start
     * @param statusBatchArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call batchChgStatusAsync(StatusBatchArg statusBatchArg, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = batchChgStatusCall(statusBatchArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for chgStatus */
    private okhttp3.Call chgStatusCall(StatusArg statusArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = statusArg;
        
        // verify the required parameter 'statusArg' is set
        if (statusArg == null) {
            throw new ApiException("Missing the required parameter 'statusArg' when calling chgStatus(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/group/status".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * chgStatus
     * change  one  group&#39;s  status,stop  or  start
     * @param statusArg  (required)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult chgStatus(StatusArg statusArg) throws ApiException {
        ApiResponse<BaseResult> resp = chgStatusWithHttpInfo(statusArg);
        return resp.getData();
    }

    /**
     * chgStatus
     * change  one  group&#39;s  status,stop  or  start
     * @param statusArg  (required)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> chgStatusWithHttpInfo(StatusArg statusArg) throws ApiException {
        okhttp3.Call call = chgStatusCall(statusArg, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * chgStatus (asynchronously)
     * change  one  group&#39;s  status,stop  or  start
     * @param statusArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call chgStatusAsync(StatusArg statusArg, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = chgStatusCall(statusArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for update */
    private okhttp3.Call updateCall(PojoArg pojoArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = pojoArg;
        
        // verify the required parameter 'pojoArg' is set
        if (pojoArg == null) {
            throw new ApiException("Missing the required parameter 'pojoArg' when calling update(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/group/update".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * update
     * update  a  group
     * @param pojoArg  (required)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult update(PojoArg pojoArg) throws ApiException {
        ApiResponse<BaseResult> resp = updateWithHttpInfo(pojoArg);
        return resp.getData();
    }

    /**
     * update
     * update  a  group
     * @param pojoArg  (required)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> updateWithHttpInfo(PojoArg pojoArg) throws ApiException {
        okhttp3.Call call = updateCall(pojoArg, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * update (asynchronously)
     * update  a  group
     * @param pojoArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call updateAsync(PojoArg pojoArg, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = updateCall(pojoArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for updateGroupMem */
    private okhttp3.Call updateGroupMemCall(UserArg userArg, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = userArg;
        
        // verify the required parameter 'userArg' is set
        if (userArg == null) {
            throw new ApiException("Missing the required parameter 'userArg' when calling updateGroupMem(Async)");
        }
        

        // create path and map variables
        String localVarPath = "/org/group/member/update".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * updateGroupMem
     * update  a  group&#39;s  members
     * @param userArg  (required)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult updateGroupMem(UserArg userArg) throws ApiException {
        ApiResponse<BaseResult> resp = updateGroupMemWithHttpInfo(userArg);
        return resp.getData();
    }

    /**
     * updateGroupMem
     * update  a  group&#39;s  members
     * @param userArg  (required)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> updateGroupMemWithHttpInfo(UserArg userArg) throws ApiException {
        okhttp3.Call call = updateGroupMemCall(userArg, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * updateGroupMem (asynchronously)
     * update  a  group&#39;s  members
     * @param userArg  (required)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call updateGroupMemAsync(UserArg userArg, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = updateGroupMemCall(userArg, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
}
