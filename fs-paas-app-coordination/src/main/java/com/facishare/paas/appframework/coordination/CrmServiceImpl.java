package com.facishare.paas.appframework.coordination;

import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.coordination.dto.GetFieldDependencyList;
import com.facishare.paas.appframework.coordination.dto.GetOptionDependencyList;
import com.facishare.paas.appframework.coordination.dto.GetSearchFilterList.Arg;
import com.facishare.paas.appframework.coordination.dto.GetSearchFilterList.Result;
import com.facishare.paas.appframework.coordination.dto.GetTableConfigList;
import com.facishare.paas.appframework.core.model.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by zhouwr on 2017/10/23
 */
@Service
public class CrmServiceImpl implements CrmService {

    @Autowired
    private CrmServiceProxy proxy;
    @Autowired
    private CrmBasicServiceProxy basicServiceProxy;
    @Autowired
    private ConfigService configService;


    @Override
    public String getConfigValue(String tenantId, int key) {
        String rst = configService.findTenantConfig(User.systemUser(tenantId),String.valueOf(key));
        return rst;
    }

    @Override
    public boolean canUpdateAccountName(String tenantId) {
        String configValue = getConfigValue(tenantId, 11);
        return "1".equals(configValue);
    }

    @Override
    public boolean getIsSubCascadeConfig(String tenantId) {
        return !SubCascadeConfig.isInBlackList(tenantId);
    }

    @Override
    public GetFieldDependencyList.Result getFieldDependencyList(String tenantId, GetFieldDependencyList.Arg arg) {
        return proxy.getFieldDependencyList(arg, tenantId);
    }

    @Override
    public GetOptionDependencyList.Result getOptionDependencyList(String tenantId,
                                                                  GetOptionDependencyList.Arg arg) {
        return proxy.getOptionDependencyList(arg, tenantId);
    }

    @Override
    public Result getSearchFilterList(String tenantId, Arg arg) {
        return basicServiceProxy.getSearchFilterList(arg, tenantId);
    }

    @Override
    public GetTableConfigList.Result getTableConfigList(String tenantId,
                                                        GetTableConfigList.Arg arg) {
        return basicServiceProxy.getTableConfigList(arg, tenantId);
    }
}
