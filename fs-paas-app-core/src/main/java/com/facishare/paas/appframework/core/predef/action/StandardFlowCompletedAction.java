package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.Serializer;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.predef.domain.FlowCompletedActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.facade.AuditLogServiceFacade;
import com.facishare.paas.appframework.core.predef.facade.FlowCompletedActionServiceFacade;
import com.facishare.paas.appframework.core.predef.facade.SaveActionServiceFacade;
import com.facishare.paas.appframework.core.predef.handler.flowcompleted.FlowCompletedActionHandler;
import com.facishare.paas.appframework.core.util.HandlerGrayConfig;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.flow.ExtraDataKeys;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataDiffer;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.dto.ConvertRuleDataContainer;
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot;
import com.facishare.paas.appframework.metadata.dto.UpdateMasterAndDetailData;
import com.facishare.paas.appframework.metadata.handler.HandlerLogicService;
import com.facishare.paas.appframework.metadata.handler.HandlerType;
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;
import com.facishare.paas.appframework.metadata.relation.EditCalculateParam;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.transaction.tcc.api.context.GlobalTransactionApplicationData;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.redisson.api.RLock;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.model.RequestContext.Attributes.TRIGGER_WORK_FLOW;
import static com.facishare.paas.appframework.core.model.RequestContext.Biz.ApprovalFlow;
import static com.facishare.paas.appframework.metadata.ButtonExt.ACTION_SOURCE_FLOW;


/**
 * 审批流完成回调动作
 * <p>
 * Created by wansong on 2017/10/30.
 */
@Slf4j
@Idempotent(serializer = Serializer.Type.java, waitTime = 15)
public class StandardFlowCompletedAction extends PreDefineAction<StandardFlowCompletedAction.Arg, StandardFlowCompletedAction.Result> {

    public static final Set<ApprovalFlowTriggerType> SUPPORT_HANDLER_TRIGGER_TYPES = ImmutableSet.of(ApprovalFlowTriggerType.CREATE,
            ApprovalFlowTriggerType.UPDATE, ApprovalFlowTriggerType.INVALID);

    protected IObjectData data;

    protected IObjectData dbData;

    protected Map<String, List<IObjectData>> detailObjectDataMap;
    protected Map<String, List<IObjectData>> dbDetailObjectDataMap;
    private final Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
    private final Map<String, IObjectDescribe> detailDescribeMap = Maps.newHashMap();

    //最终要触发工作流的数据
    protected Map<String, List<IObjectData>> dataToTriggerCreateWorkFlow = Maps.newLinkedHashMap();
    protected Map<String, List<IObjectData>> dataToTriggerInvalidWorkFlow = Maps.newLinkedHashMap();

    private ObjectDataSnapshot dataSnapshot;
    protected Map<String, Object> callbackData;
    protected Map<String, Object> detailChange;

    private List<IObjectData> fillOldData;

    //处理编辑审批的临时变量
    protected Map<String, List<IObjectData>> detailsToAdd = Maps.newHashMap();
    protected Map<String, List<IObjectData>> detailsToUpdate = Maps.newHashMap();
    protected Map<String, List<IObjectData>> detailsToInvalid = Maps.newHashMap();
    protected Map<String, List<IObjectData>> detailsToDelete = Maps.newHashMap();
    protected Map<String, Map<String, Map<String, Object>>> updatedFieldMap = Maps.newHashMap();

    private Boolean triggerWorkFlow;
    private String lastLifeStatus;
    private boolean useSnapshot;

    private List<SimpleHandlerDescribe> triggerActionHandlerDescribes;

    private FlowCompletedActionServiceFacade flowCompletedActionServiceFacade;
    private AuditLogServiceFacade auditLogServiceFacade;

    protected boolean writeDB;

    private ConvertRuleDataContainer convertRuleDataContainer;

    @Override
    protected final boolean customSkipHandler() {
        ApprovalFlowTriggerType triggerType = arg.approvalFlowTriggerType();
        return !SUPPORT_HANDLER_TRIGGER_TYPES.contains(triggerType);
    }

    @Override
    protected final Handler.Arg<Arg> buildHandlerArg(SimpleHandlerDescribe handlerDescribe) {
        return FlowCompletedActionHandler.Arg.builder()
                .describeMap(describeMap)
                .data(ObjectDataDocument.of(data))
                .dbData(ObjectDataDocument.of(dbData))
                .detailDataMap(ObjectDataDocument.ofMap(detailObjectDataMap))
                .dbDetailDataMap(ObjectDataDocument.ofMap(dbDetailObjectDataMap))
                .callbackData(callbackData)
                .detailChange(detailChange)
                .lastLifeStatus(lastLifeStatus)
                .skipWorkFlow(!needTriggerWorkFlow())
                .triggerActionHandlerDescribes(triggerActionHandlerDescribes)
                .convertRuleDataContainer(convertRuleDataContainer)
                .editActionInfo(FlowCompletedActionHandler.EditActionInfo.builder()
                        .detailsToAdd(ObjectDataDocument.ofMap(detailsToAdd))
                        .detailsToUpdate(ObjectDataDocument.ofMap(detailsToUpdate))
                        .detailsToDelete(ObjectDataDocument.ofMap(detailsToDelete))
                        .detailsToInvalid(ObjectDataDocument.ofMap(detailsToInvalid))
                        .updatedFieldMap(updatedFieldMap)
                        .fillOldData(ObjectDataDocument.ofList(fillOldData))
                        .build())
                .build();
    }

    @Override
    protected final void processHandlerResult(HandlerContext handlerContext, Handler.Arg<Arg> handlerArg, Handler.Result<Result> handlerResult) {
        if (handlerResult instanceof FlowCompletedActionHandler.Result) {
            FlowCompletedActionHandler.Result actionResult = (FlowCompletedActionHandler.Result) handlerResult;
            if (Objects.nonNull(actionResult.getData())) {
                ObjectDataExt.of(this.data).putAll(actionResult.getData());
                if (isUpdateTriggerType()) {
                    processMasterDataEdit();
                }
            }
            if (Objects.nonNull(actionResult.getDetailDataMap())) {
                if (Objects.isNull(detailObjectDataMap)) {
                    detailObjectDataMap = Maps.newHashMap();
                }
                actionResult.getDetailDataMap().forEach((k, v) -> {
                    if (describeMap.containsKey(k) && !objectDescribe.getApiName().equals(k)) {
                        detailObjectDataMap.put(k, ObjectDataDocument.ofDataList(v));
                    }
                });
                if (isUpdateTriggerType()) {
                    ObjectDataDiffer.DiffResult diffResult = ObjectDataDiffer.builder()
                            .oldDataMap(this.dbDetailObjectDataMap)
                            .newDataMap(this.detailObjectDataMap)
                            .describeMap(this.describeMap)
                            .filterNotSupportUpdateFields(true)
                            .build()
                            .diff();
                    this.detailChange = Maps.newHashMap(diffResult.getDiffMap());
                    processDetailDataEdit();
                }
            }
            Optional.ofNullable(actionResult.getInterfaceResult())
                    .map(Result::getWriteDB)
                    .ifPresent(it -> writeDB = it);
        }
    }

    @Override
    protected final List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected final List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    private void checkDuplicate() {
        flowCompletedActionServiceFacade.checkDuplicate(actionContext.getUser(), objectDescribe, data, callbackData, arg);
    }

    @Override
    protected void init() {
        if (Objects.isNull(arg.approvalFlowTriggerType())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        flowCompletedActionServiceFacade = serviceFacade.getBean(FlowCompletedActionServiceFacade.class);
        auditLogServiceFacade = serviceFacade.getBean(AuditLogServiceFacade.class);

        RequestContextManager.getContext().setBatch(false);
        initObjectData();
        stopWatch.lap("initObjectData");

        //更新生命状态
        boolean skipProcess = !processLifeStatusByTriggerResult();
        stopWatch.lap("resetLifeStatusByTriggerResult");

        //数据生命状态不是审核中或变更中，直接返回
        if (skipProcess) {
            throw new AcceptableValidateException(new Result(true, writeDB));
        }

        initDetailData();
        stopWatch.lap("initDetailData");

        processDataForEditAction();
        stopWatch.lap("processDataForEditAction");

        builderConvertRuleContainerFromCallBackData(callbackData);

        //初始化业务链
        initTriggerActionHandlerDescribes(arg.approvalFlowTriggerType());
        stopWatch.lap("initTriggerActionHandlerDescribes");
    }

    @Override
    protected void beforeGlobalTransactionCompletion(GlobalTransactionApplicationData globalTransactionApplicationData) {
        super.beforeGlobalTransactionCompletion(globalTransactionApplicationData);
        globalTransactionApplicationData.setAttribute(GlobalTransactionConstant.OBJECT_SAVE_WRITE_DB_FLAG, writeDB);
    }

    /**
     * 1. 取数据信息
     * 2. 根据审批结果更新数据的状态，其实就是通过状态时，就更新为已生效状态 或  作废 状态
     * 3. 如果作废成功,则执行作废。如果作废的是主对象,则也要作废其下没有审批流的从对象
     * 5. 记录日志信息
     * 6. 触发工作流
     */
    @Override
    protected Result doAct(Arg arg) {
        log.debug("Entering StandardFlowCompletedAction,arg:{}", arg);

        //checkDuplicate
        checkDuplicate();
        stopWatch.lap("checkDuplicate");

        if (!arg.isPass()) {
            updateDataLifeStatus(data);
            stopWatch.lap("updateDataLifeStatus");
            recordLifeStatusModifyLog();
            stopWatch.lap("recordLifeStatusModifyLog");
            //只有编辑后才删除 日志
            if (Objects.equals(ApprovalFlowTriggerType.UPDATE, arg.approvalFlowTriggerType())) {
                deleteDataSnapshot();
                stopWatch.lap("deleteDataSnapshot");
            }
            updateChangeOrderStatus();
            stopWatch.lap("updateChangeOrderStatus");
            return new Result(true, writeDB);
        }

        boolean needUpdateLifeStatus = doActionWithTriggerType();
        stopWatch.lap("doActionWithTriggerType");

        // 更新变更单状态
        updateChangeOrderStatus();
        stopWatch.lap("updateChangeOrderStatus");

        if (needUpdateLifeStatus) {
            updateDataLifeStatus(data);
            stopWatch.lap("updateDataLifeStatus");
            recordLifeStatusModifyLog();
            stopWatch.lap("recordLifeStatusModifyLog");
        }

        if (needTriggerWorkFlow()) {
            startWorkFlow();
            stopWatch.lap("startWorkFlow");
        }
        return new Result(true, writeDB);
    }

    private void updateChangeOrderStatus() {
        flowCompletedActionServiceFacade.updateChangeOrderStatus(actionContext.getUser(), objectDescribe, data, arg);
    }

    @Override
    protected void finallyDo() {
        RLock duplicateSearchLock = CacheContext.getContext().removeCache(ContextCacheKeys.DUPLICATE_SEARCH_LOCK);
        infraServiceFacade.unlock(duplicateSearchLock);
        stopWatch.lap("unlockDuplicateSearch");
        super.finallyDo();
    }

    private void initObjectData() {
        String dataId = arg.getDataId();
        String apiName = arg.getDescribeApiName();
        describeMap.put(apiName, objectDescribe);
        dbData = serviceFacade.findObjectData(actionContext.getUser(), dataId, apiName);
        stopWatch.lap("findObjectData");
        data = ObjectDataExt.of(dbData).copy();
        parseCustomAttribute();
        if (useSnapshot) {
            dataSnapshot = findDataSnapshot();
            stopWatch.lap("findDataSnapshot");
        }
        callbackData = parseCallbackData();
        detailChange = parseDetailChange();
    }

    private void initDetailData() {
        if (needDetailData()) {
            dbDetailObjectDataMap = getDataDetails();
            detailObjectDataMap = ObjectDataExt.copyMap(dbDetailObjectDataMap);
        }
    }

    private void processDataForEditAction() {
        if (isUpdateTriggerType()) {
            fillOldData = fillOldData(data, callbackData);

            //合并主对象和从对象的数据
            ObjectDataExt.of(data).putAll(callbackData);
            if (CollectionUtils.notEmpty(detailChange)) {
                Map<String, Map<String, Object>> mergedDetailChange = Maps.newHashMap();
                detailChange.forEach((k, v) -> mergedDetailChange.put(k, (Map<String, Object>) v));
                ObjectDataSnapshot.builder().detailSnapshot(mergedDetailChange).build().mergeIntoDetailData(detailObjectDataMap);
            }

            //预处理主对象的数据
            processMasterDataEdit();
            //预处理从对象的数据
            processDetailDataEdit();
            // 同步更新计算字段逻辑
            syncUpdateCalculateFields();
        }
    }

    private void syncUpdateCalculateFields() {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FLOW_COMPLETED_SUPPORT_SYNC_CALCULATE_EI, actionContext.getTenantId())) {
            return;
        }
        try {
            Map<String, List<IObjectData>> beforeDetailsToAdd = ObjectDataExt.copyMap(detailsToAdd);
            Map<String, List<IObjectData>> beforeDetailsToUpdate = ObjectDataExt.copyMap(detailsToUpdate);
            ObjectDataExt.mergeWithDbData(beforeDetailsToUpdate, dbDetailObjectDataMap);
            Map<String, List<IObjectData>> detailObjectDataCopy = ObjectDataExt.copyMap(detailObjectDataMap);
            EditCalculateParam param = buildEditCalculateParam(detailObjectDataCopy);
            serviceFacade.calculateForEditData(actionContext.getUser(), param);
            Map<String, Object> updateCalculateFields = ObjectDataExt.of(dbData).diff(data, objectDescribe);
            ObjectDataExt.of(updateCalculateFields).retainCalculateField(objectDescribe);
            Map<String, Map<String, Object>> masterDataMap = updatedFieldMap.get(objectDescribe.getApiName());
            masterDataMap.get(data.getId()).putAll(updateCalculateFields);
            ObjectDataExt.mergeWithDbData(detailsToAdd, detailObjectDataCopy);
            Map<String, List<IObjectData>> diffDetailToAdd = ObjectDataExt.diffDetail(beforeDetailsToAdd, detailsToAdd, describeMap, false);
            Map<String, List<IObjectData>> diffDetailToUpdate = ObjectDataExt.diffDetail(beforeDetailsToUpdate, detailObjectDataCopy, describeMap, true);
            diffDetailToUpdate.forEach((apiName, dataList) -> {
                IObjectDescribe detailDescribe = detailDescribeMap.get(apiName);
                CollectionUtils.nullToEmpty(dataList).forEach(objectData -> {
                    String id = objectData.getId();
                    ObjectDataExt.of(objectData).retainCalculateField(detailDescribe);
                    objectData.setId(id);
                });
            });
            ObjectDataExt.mergeWithDbData(detailsToUpdate, diffDetailToUpdate);
            log.info("syncUpdateCalculateFields, tenantId:{},master apiName:{},dataId:{},updateCalculateFields:{},detailToAdd diff:{},detailsToUpdate diff:{}",
                    actionContext.getTenantId(), objectDescribe.getApiName(), data.getId(), updateCalculateFields,
                    diffDetailToAdd, diffDetailToUpdate);
        } catch (Exception e) {
            log.warn("syncUpdateCalculateFields fail!, ei:{}, describeApiName:{}", actionContext.getTenantId(), actionContext.getObjectApiName(), e);
        }
    }

    private EditCalculateParam buildEditCalculateParam(Map<String, List<IObjectData>> detailObjectDataCopy) {
        Map<String, List<IObjectData>> detailDeleteDataMap = Maps.newHashMap();
        detailDeleteDataMap.putAll(detailsToDelete);
        detailDeleteDataMap.putAll(detailsToInvalid);
        return EditCalculateParam.builder()
                .masterData(data)
                .oldMasterData(dbData)
                .detailDataMap(detailObjectDataCopy)
                .oldDetailDataMap(dbDetailObjectDataMap)
                .masterModifyData(updatedFieldMap.get(objectDescribe.getApiName()).get(data.getId()))
                .detailAddDataMap(detailsToAdd)
                .detailDeleteDataMap(detailDeleteDataMap)
                .detailModifyDataMap(detailsToUpdate)
                .masterDescribe(objectDescribe)
                .detailDescribeMap(detailDescribeMap)
                .excludeDefaultValue(true)
                .includeQuoteField(false)
                .excludeLookupRelateField(false)
                .getDataIndex(objectData -> ObjectDataExt.of(objectData).getId())
                .build();
    }

    private void parseCustomAttribute() {
        if (arg.getCallbackData() != null && (arg.getCallbackData() instanceof Map)) {
            Map<String, Object> data = (Map) arg.getCallbackData();
            lastLifeStatus = (String) data.remove(ObjectLifeStatus.LAST_LIFE_STATUS_API_NAME);
        }
        if (arg.getExtraData() != null) {
            triggerWorkFlow = (Boolean) data.get(ExtraDataKeys.TRIGGER_WORK_FLOW);
            if (triggerWorkFlow != null) {
                RequestContextManager.getContext().setAttribute(RequestContext.Attributes.TRIGGER_WORK_FLOW, triggerWorkFlow);
            }
            Boolean useSnapshotForApproval = (Boolean) arg.getExtraData().get(ExtraDataKeys.USE_SNAPSHOT_FOR_APPROVAL);
            useSnapshot = Boolean.TRUE.equals(useSnapshotForApproval);
            if (useSnapshotForApproval != null) {
                RequestContextManager.getContext().setAttribute(RequestContext.Attributes.USE_SNAPSHOT_FOR_APPROVAL, useSnapshot);
            }
        }
    }

    private ObjectDataSnapshot findDataSnapshot() {
        return infraServiceFacade.findAndMergeSnapshot(actionContext.getTenantId(), arg.getDescribeApiName(), arg.getDataId(),
                ApprovalFlow.getCode(), RequestUtil.getOtherBizId());
    }

    private void deleteDataSnapshot() {
        infraServiceFacade.deleteSnapshot(actionContext.getUser(), arg.getDescribeApiName(), arg.getDataId(),
                ApprovalFlow.getCode(), RequestUtil.getOtherBizId());
    }

    protected Map<String, Object> parseCallbackData() {
        Map<String, Object> masterCallbackData = Maps.newHashMap(CollectionUtils.nullToEmpty((Map) arg.getCallbackData()));
        if (dataSnapshot != null) {
            masterCallbackData.putAll(dataSnapshot.getMasterSnapshot());
        }
        if (CollectionUtils.notEmpty(masterCallbackData)) {
            ObjectDataExt.of(masterCallbackData).removeCalculateField(objectDescribe);
        }
        return masterCallbackData;
    }

    protected Map<String, Object> parseDetailChange() {
        Map<String, Object> detailChange = Maps.newHashMap();
        if (dataSnapshot != null) {
            detailChange.putAll(dataSnapshot.getDetailSnapshot());
        } else if (CollectionUtils.notEmpty(arg.getExtraData())) {
            Map bakDetailChange = (Map<String, Object>) arg.getExtraData().get(ExtraDataKeys.DETAIL_CHANGE);
            detailChange.putAll(CollectionUtils.nullToEmpty(bakDetailChange));
        }
        return detailChange;
    }

    private boolean processLifeStatusByTriggerResult() {
        boolean ret = ObjectDataExt.of(data).getLifeStatus().completeCallBack(data,
                arg.approvalFlowTriggerType(), arg.isPass());

        if (!Strings.isNullOrEmpty(lastLifeStatus)) {
            ObjectDataExt.of(data).setLifeStatus(lastLifeStatus);
        }

        return ret;
    }

    protected boolean doActionWithTriggerType() {
        boolean ret = true;
        ApprovalFlowTriggerType triggerType = arg.approvalFlowTriggerType();
        //执行before类型的业务链
        executeTriggerActionTenantHandler(Lists.newArrayList(HandlerType.BEFORE, HandlerType.ACT));
        switch (triggerType) {
            case CREATE:
                doCreateAction();
                break;
            case UPDATE:
                doEditAction();
                ret = false;
                break;
            case INVALID:
                doInvalidAction();
                ret = false;
                break;
            case CHANGE_OWNER:
                doChangeOwnerAction();
                break;
            case CUSTOM_BUTTON:
                ret = doCustomButtonAction();
                break;
            case CHANGE_PARTNER:
                doChangePartnerAction();
                break;
            case DELETE_PARTNER:
                doDeletePartnerAction();
                break;
            case CHANGE_PARTNER_OWNER:
                doChangePartnerOwnerAction();
                break;
            default:
                doOtherAction();
                break;
        }
        writeDB = true;
        return ret;
    }

    private boolean needDetailData() {
        ApprovalFlowTriggerType triggerType = arg.approvalFlowTriggerType();
        return ApprovalFlowTriggerType.CREATE == triggerType
                || ApprovalFlowTriggerType.UPDATE == triggerType
                || ApprovalFlowTriggerType.INVALID == triggerType;
    }

    private boolean isUpdateTriggerType() {
        return arg.approvalFlowTriggerType() == ApprovalFlowTriggerType.UPDATE;
    }

    private ObjectAction getActionByTriggerType(ApprovalFlowTriggerType triggerType) {
        return ObjectAction.of(triggerType.getActionCode());
    }

    private void initTriggerActionHandlerDescribes(ApprovalFlowTriggerType triggerType) {
        if (!arg.isPass()) {
            return;
        }
        ObjectAction action = getActionByTriggerType(triggerType);
        String interfaceCode = action.getInterfaceCode();
        if (!HandlerGrayConfig.supportManagement(actionContext.getTenantId(), interfaceCode, actionContext.getObjectApiName())) {
            return;
        }
        HandlerLogicService handlerLogicService = infraServiceFacade.getSpringBeanHolder().getHandlerLogicService();
        this.triggerActionHandlerDescribes = handlerLogicService.findHandlerDescribeByInterfaceCode(actionContext.getTenantId(),
                actionContext.getObjectApiName(), interfaceCode);
    }

    private void executeTriggerActionTenantHandler(List<HandlerType> handlerTypes) {
        if (CollectionUtils.empty(triggerActionHandlerDescribes)) {
            return;
        }
        HandlerContext handlerContext = HandlerContext.builder()
                .requestContext(actionContext.getRequestContext())
                .interfaceCode(StandardAction.FlowCompleted.name())
                .build();
        FlowCompletedActionHandler.Arg handlerArg = (FlowCompletedActionHandler.Arg) getHandlerArg(null);
        FlowCompletedActionHandler.Result tenantHandlerResult = flowCompletedActionServiceFacade
                .executeTriggerActionTenantHandler(handlerContext, handlerArg, handlerTypes);
        processHandlerResult(handlerContext, handlerArg, tenantHandlerResult);
    }

    private void doPostAction(ObjectAction action, IObjectData objectData, Map<String, Object> actionParam,
                              Map<String, List<IObjectData>> detailObjectData) {
        flowCompletedActionServiceFacade.doPostAction(actionContext.getRequestContext(), objectDescribe.getApiName(),
                objectData, detailObjectData, callbackData, action, actionParam);
    }

    protected void doCreateAction() {
        dataToTriggerCreateWorkFlow.put(arg.getDescribeApiName(), Lists.newArrayList(data));
        if (CollectionUtils.notEmpty(triggerActionHandlerDescribes)) {
            //执行after类型的业务链
            executeTriggerActionTenantHandler(Lists.newArrayList(HandlerType.ACT, HandlerType.AFTER));
            stopWatch.lap("executeTriggerActionTenantHandler");
        } else {
            doPostAction(ObjectAction.CREATE, data, Maps.newHashMap(), getDataDetails());
            stopWatch.lap("doPostAction");
        }
    }

    private void triggerWorkFlowForDetailsInCreateAction() {
        if (CollectionUtils.empty(callbackData) || !callbackData.containsKey(ExtraDataKeys.DETAIL_CREATE_TIME)) {
            return;
        }
        // 主从审批的白名单企业,主从同时新建的从,工作流已经在新建时触发了
        if (AppFrameworkConfig.isInMasterDetailApprovalWhiteList(actionContext.getTenantId())) {
            return;
        }
        if (CollectionUtils.empty(detailObjectDataMap)) {
            return;
        }
        long detailCreateTime = NumberUtils.toLong(String.valueOf(callbackData.get(ExtraDataKeys.DETAIL_CREATE_TIME)), dbData.getCreateTime());
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> {
            List<IObjectDescribe> detailDescribes = describeMap.values().stream()
                    .filter(x -> !Objects.equals(objectDescribe.getApiName(), x.getApiName()))
                    .filter(x -> ObjectDescribeExt.of(x).isCreateWithMaster())
                    .collect(Collectors.toList());
            detailDescribes.forEach(detailDescribe -> {
                try {
                    List<IObjectData> queryDetailData = detailObjectDataMap.getOrDefault(detailDescribe.getApiName(), Collections.emptyList()).stream()
                            .filter(x -> x.getCreateTime() >= detailCreateTime)
                            .collect(Collectors.toList());
                    if (CollectionUtils.empty(queryDetailData)) {
                        return;
                    }
                    Map<String, List<IObjectData>> dataMap = Maps.newHashMap();
                    dataMap.put(detailDescribe.getApiName(), queryDetailData);
                    triggerWorkFlow(dataMap, ApprovalFlowTriggerType.CREATE);
                } catch (Exception e) {
                    log.error("processDetailsInCreateAction error,tenantId:{},apiName:{}", actionContext.getTenantId(),
                            detailDescribe.getApiName(), e);
                }
            });
        });
        parallelTask.run();
        stopWatch.lap("triggerWorkFlowForDetailsInCreateAction");
    }

    private void recordLifeStatusModifyLog() {
        auditLogServiceFacade.recordLifeStatusModifyLog(actionContext.getTenantId(), data, objectDescribe, ObjectDataExt.of(dbData).getLifeStatus());
    }

    protected void doEditAction() {
        //主和从放到一个事务里更新
        updateMasterAndDetailData();
        stopWatch.lap("updateMasterAndDetailData");
        //同步更新从对象的生命状态
        updateDetailObjectDataLifeStatus(data);
        stopWatch.lap("updateDetailObjectDataLifeStatus");
        //记录修改日志
        recordEditLog();
        stopWatch.lap("recordEditLog");
        //发送mq消息
        sendEditMq(fillOldData);
        stopWatch.lap("sendEditMq");
        if (CollectionUtils.notEmpty(triggerActionHandlerDescribes)) {
            //执行after类型的业务链
            executeTriggerActionTenantHandler(Lists.newArrayList(HandlerType.ACT, HandlerType.AFTER));
            stopWatch.lap("executeTriggerActionTenantHandler");
        } else {
            //执行后置函数
            doPostAction(ObjectAction.UPDATE, data, Maps.newHashMap(), getDataDetails());
            stopWatch.lap("doPostAction");
        }
    }

    private void processMasterDataEdit() {
        updatedFieldMap.remove(objectDescribe.getApiName());

        Map<String, Object> toUpdateMap = ObjectDataExt.of(dbData).diff(data, objectDescribe);
        ObjectDataExt.of(toUpdateMap).removeFieldsNotSupportEdit(objectDescribe);
        toUpdateMap.put(ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectDataExt.of(data).getLifeStatusText());
        Map<String, Map<String, Object>> updateMap = Maps.newHashMap();
        updateMap.put(data.getId(), toUpdateMap);
        updatedFieldMap.put(objectDescribe.getApiName(), updateMap);
    }

    private void processDetailDataEdit() {
        detailsToAdd.clear();
        detailsToUpdate.clear();
        detailsToInvalid.clear();
        detailsToDelete.clear();
        describeMap.keySet().stream().filter(x -> !objectDescribe.getApiName().equals(x)).forEach(x -> {
            updatedFieldMap.remove(x);
            dataToTriggerCreateWorkFlow.remove(x);
            dataToTriggerInvalidWorkFlow.remove(x);
        });

        if (CollectionUtils.empty(detailChange)) {
            return;
        }
        detailChange.forEach((apiName, v) -> {
            Map<String, Object> changes = (Map<String, Object>) v;
            detailDataAdd(apiName, changes);
            detailDataUpdate(apiName, changes);
            detailDataInvalid(apiName, changes);
            detailDataDelete(apiName, changes);
        });
    }

    protected void updateMasterAndDetailData() {
        UpdateMasterAndDetailData.Arg updateArg = UpdateMasterAndDetailData.Arg.builder()
                .masterObjectData(data)
                .incrementUpdate(true)
                .toUpdateMap(updatedFieldMap.get(objectDescribe.getApiName()).get(data.getId()))
                .detailsToAdd(detailsToAdd)
                .detailsToUpdate(detailsToUpdate)
                .detailsToInvalid(detailsToInvalid)
                .detailsToDelete(detailsToDelete)
                .convertRuleDataContainer(convertRuleDataContainer)
                .incrementUpdateDetail(true)
                .updateIgnoreNotExistsData(true)
                .actionType("flowCompletedAction")
                .build();
        serviceFacade.updateMasterAndDetailData(actionContext.getUser(), updateArg);
    }

    private void builderConvertRuleContainerFromCallBackData(Map<String, Object> callbackData) {
        Object o = CollectionUtils.nullToEmpty(callbackData).get(ExtraDataKeys.OPTION_INFO);
        BaseObjectSaveAction.OptionInfo optionInfo = JSON.parseObject(JSON.toJSONString(o), BaseObjectSaveAction.OptionInfo.class);
        if (Objects.nonNull(optionInfo) && optionInfo.isFromReferenceCreate()) {
            SaveActionServiceFacade saveActionServiceFacade = serviceFacade.getBean(SaveActionServiceFacade.class);
            convertRuleDataContainer = saveActionServiceFacade.buildConvertRuleDataContainer(actionContext.getUser(), optionInfo);
        }
    }

    private void recordEditLog() {
        Map<String, Map<String, Object>> detailChangeMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(detailChange)) {
            detailChange.forEach((k, v) -> detailChangeMap.put(k, (Map<String, Object>) v));
        }
        auditLogServiceFacade.recordEditLog(actionContext.getUser(), data, dbData,
                updatedFieldMap.get(objectDescribe.getApiName()).get(data.getId()), dbDetailObjectDataMap, detailsToAdd,
                detailsToUpdate, detailsToDelete, detailsToInvalid, detailChangeMap, describeMap, Maps.newHashMap(), convertRuleDataContainer);
    }

    private void sendEditMq(List<IObjectData> fillOldData) {
        //先拷贝一下数据，防止并发修改报错
        List<IObjectData> cpOldData = ObjectDataExt.copyList(fillOldData);
        Map<String, List<IObjectData>> cpUpdateDetails = ObjectDataExt.copyMap(detailsToUpdate);
        Map<String, List<IObjectData>> cpInvalidDetails = ObjectDataExt.copyMap(detailsToInvalid);
        Map<String, List<IObjectData>> cpDeleteDetails = ObjectDataExt.copyMap(detailsToDelete);
        Map<String, List<IObjectData>> cpAddDetails = ObjectDataExt.copyMap(detailsToAdd);

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> {
            serviceFacade.sendActionMq(actionContext.getUser(), cpOldData, ObjectAction.UPDATE);
            cpUpdateDetails.forEach((k, v) ->
                    serviceFacade.sendActionMq(actionContext.getUser(), v, ObjectAction.UPDATE));
            cpInvalidDetails.forEach((k, v) ->
                    serviceFacade.sendActionMq(actionContext.getUser(), v, ObjectAction.INVALID));
            cpDeleteDetails.forEach((k, v) ->
                    serviceFacade.sendActionMq(actionContext.getUser(), v, ObjectAction.DELETE));
            cpAddDetails.forEach((k, v) ->
                    serviceFacade.sendActionMq(actionContext.getUser(), v, ObjectAction.CREATE));
        });
        parallelTask.run();
    }

    protected final void detailDataAdd(String objectApiName, Map<String, Object> changes) {
        detailsToAdd.remove(objectApiName);
        dataToTriggerCreateWorkFlow.remove(objectApiName);

        List<Map<String, Object>> addList = (List<Map<String, Object>>) changes.get(ObjectAction.CREATE.getActionCode());
        if (CollectionUtils.empty(addList)) {
            return;
        }
        List<IObjectData> dataList = addList.stream().map(x -> ObjectDataExt.of(x).getObjectData()).collect(Collectors.toList());
        dataList.forEach(x -> ObjectDataExt.of(x).setLifeStatus(ObjectDataExt.of(data).getLifeStatus()));
        detailsToAdd.put(objectApiName, dataList);
        dataToTriggerCreateWorkFlow.put(objectApiName, dataList);
    }

    protected final void detailDataUpdate(String objectApiName, Map<String, Object> changes) {
        detailsToUpdate.remove(objectApiName);
        updatedFieldMap.remove(objectApiName);

        Map<String, Object> updateList = (Map<String, Object>) changes.get(ObjectAction.UPDATE.getActionCode());
        if (CollectionUtils.empty(updateList)) {
            return;
        }
        //查询一下被编辑的从对象的数据，给后面保存修改记录使用
        List<IObjectData> dbDetailData = dbDetailObjectDataMap.getOrDefault(objectApiName, Collections.emptyList()).stream()
                .filter(x -> updateList.containsKey(x.getId())).collect(Collectors.toList());
        if (CollectionUtils.empty(dbDetailData)) {
            return;
        }
        Set<String> dbDetailIds = dbDetailData.stream().map(DBRecord::getId).collect(Collectors.toSet());
        List<IObjectData> dataList = Lists.newArrayList();
        Map<String, Map<String, Object>> updateField = Maps.newHashMap();
        updateList.forEach((k, v) -> {
            //数据库里查不到的数据不处理
            if (!dbDetailIds.contains(k)) {
                return;
            }
            ObjectDataExt dataExt = ObjectDataExt.of(Maps.newHashMap((Map<String, Object>) v));
            IObjectDescribe detailDescribe = getDescribe(objectApiName);
            //移除计算字段和统计字段
            dataExt.removeCalculateField(detailDescribe);
            dataExt.setLifeStatus(ObjectDataExt.of(data).getLifeStatus());
            updateField.put(k, Maps.newHashMap(dataExt.toMap()));
            dataExt.setTenantId(actionContext.getTenantId());
            dataExt.setDescribeApiName(objectApiName);
            dataExt.setId(k);
            //设置主从关系字段
            ObjectDescribeExt.of(detailDescribe).getMasterDetailFieldName(objectDescribe.getApiName())
                    .ifPresent(x -> dataExt.set(x, data.getId()));
            dataList.add(dataExt.getObjectData());
        });
        if (CollectionUtils.notEmpty(dataList)) {
            detailsToUpdate.put(objectApiName, dataList);
        }
        if (CollectionUtils.notEmpty(updateField)) {
            updatedFieldMap.put(objectApiName, updateField);
        }
    }

    protected final void detailDataInvalid(String objectApiName, Map<String, Object> changes) {
        detailsToInvalid.remove(objectApiName);
        dataToTriggerInvalidWorkFlow.remove(objectApiName);

        List<String> invalidIds = (List<String>) changes.get(ObjectAction.INVALID.getActionCode());
        if (CollectionUtils.empty(invalidIds)) {
            return;
        }
        List<IObjectData> dataList = dbDetailObjectDataMap.getOrDefault(objectApiName, Collections.emptyList()).stream()
                .filter(x -> invalidIds.contains(x.getId())).collect(Collectors.toList());
        if (CollectionUtils.notEmpty(dataList)) {
            List<IObjectData> cpDataList = ObjectDataExt.copyList(dataList);
            detailsToInvalid.put(objectApiName, cpDataList);
            dataToTriggerInvalidWorkFlow.put(objectApiName, cpDataList);
        }
    }

    protected final void detailDataDelete(String objectApiName, Map<String, Object> changes) {
        detailsToDelete.remove(objectApiName);

        List<String> deleteIds = (List<String>) changes.get(ObjectAction.DELETE.getActionCode());
        if (CollectionUtils.empty(deleteIds)) {
            return;
        }
        List<IObjectData> dataList = dbDetailObjectDataMap.getOrDefault(objectApiName, Collections.emptyList()).stream()
                .filter(x -> deleteIds.contains(x.getId())).collect(Collectors.toList());
        if (CollectionUtils.notEmpty(dataList)) {
            List<IObjectData> cpDataList = ObjectDataExt.copyList(dataList);
            detailsToDelete.put(objectApiName, cpDataList);
        }
    }

    private IObjectDescribe getDescribe(String apiName) {
        IObjectDescribe describe = describeMap.get(apiName);
        if (Objects.isNull(describe)) {
            describe = serviceFacade.findObject(actionContext.getTenantId(), apiName);
            describeMap.put(apiName, describe);
        }
        return describe;
    }

    protected void doInvalidAction() {
        invalidDataAndDetailData(data);
        if (CollectionUtils.notEmpty(triggerActionHandlerDescribes)) {
            //执行after类型的业务链
            executeTriggerActionTenantHandler(Lists.newArrayList(HandlerType.ACT, HandlerType.AFTER));
            stopWatch.lap("executeTriggerActionTenantHandler");
        } else {
            doPostAction(ObjectAction.INVALID, data, Maps.newHashMap(), detailObjectDataMap);
            stopWatch.lap("doPostAction");
        }
    }

    protected void doChangeOwnerAction() {
        StandardChangeOwnerAction.Arg args = StandardChangeOwnerAction.Arg.of(callbackData, arg.getDataId());
        ActionContext newActionContext = buildNewActionContext(StandardAction.ChangeOwner.name());
        serviceFacade.triggerAction(newActionContext, args, StandardChangeOwnerAction.Result.class);
    }

    protected void doChangePartnerAction() {
        StandardChangePartnerAction.Arg args = StandardChangePartnerAction.Arg.of(callbackData, arg.getDataId());
        ActionContext newActionContext = buildNewActionContext(StandardAction.ChangePartner.name());
        serviceFacade.triggerAction(newActionContext, args, BaseUpdatePartnerAction.Result.class);
    }

    protected void doDeletePartnerAction() {
        StandardDeletePartnerAction.Arg args = StandardDeletePartnerAction.Arg.of(arg.getDataId());
        ActionContext newActionContext = buildNewActionContext(StandardAction.DeletePartner.name());
        serviceFacade.triggerAction(newActionContext, args, BaseUpdatePartnerAction.Result.class);
    }

    protected void doChangePartnerOwnerAction() {
        StandardChangePartnerOwnerAction.Arg args = StandardChangePartnerOwnerAction.Arg.of(callbackData, arg.getDataId());
        ActionContext newActionContext = buildNewActionContext(StandardAction.ChangePartnerOwner.name());
        serviceFacade.triggerAction(newActionContext, args, BaseUpdatePartnerAction.Result.class);
    }

    protected boolean doCustomButtonAction() {
        CustomButtonAction.Arg args = CustomButtonAction.Arg.builder()
                .args(ObjectDataDocument.of((Map<String, Object>) callbackData.get(ExtraDataKeys.ARGS)))
                .objectDataId(arg.getDataId())
                .skipTriggerApprovalFlow(true)
                .skipPreFunction(true)
                .skipCheckButtonWheres(true)
                .skipLockValidate(true)
                .actionSource(ACTION_SOURCE_FLOW)
                .build();
        ActionContext newActionContext = buildNewActionContext((String) callbackData.get(ExtraDataKeys.BUTTON_API_NAME));
        serviceFacade.triggerAction(newActionContext, args, CustomButtonAction.Result.class);

        //有的自定义按钮会在APL里作废数据，这种情况下不需要再更新数据的生命状态了
        List<IObjectData> newData = serviceFacade.findObjectDataByIdsIgnoreAll(actionContext.getTenantId(),
                Lists.newArrayList(arg.getDataId()), objectDescribe.getApiName());
        return CollectionUtils.notEmpty(newData) && !ObjectDataExt.of(newData.get(0)).isInvalid();
    }

    protected void doOtherAction() {

    }

    protected ActionContext buildNewActionContext(String actionName) {
        ActionContext context = new ActionContext(actionContext.getRequestContext(),
                objectDescribe.getApiName(), actionName);
        //回调不需要进行基础校验(功能权限、数据权限、生命状态、锁定状态等)
        context.setAttribute(RequestContext.Attributes.SKIP_BASE_VALIDATE, Boolean.TRUE);
        //回调不需要再次触发审批流
        context.setAttribute(RequestContext.Attributes.TRIGGER_FLOW, Boolean.FALSE);
        //回调后函数支持幂等
        context.setAttribute(RequestContext.Attributes.FUNCTION_IDEMPOTENT, Boolean.TRUE);
        if (triggerWorkFlow != null) {
            context.setAttribute(TRIGGER_WORK_FLOW, triggerWorkFlow);
        }

        return context;
    }

    protected final void startWorkFlow() {
        triggerInvalidWorkFlow();
        stopWatch.lap("triggerInvalidWorkFlow");
        triggerCreateWorkFlow();
        stopWatch.lap("triggerCreateWorkFlow");
    }

    private void triggerCreateWorkFlow() {
        triggerWorkFlow(dataToTriggerCreateWorkFlow, ApprovalFlowTriggerType.CREATE);

        //查询需要触发工作流的从对象并触发新建工作流
        if (ApprovalFlowTriggerType.CREATE.equals(arg.approvalFlowTriggerType())) {
            triggerWorkFlowForDetailsInCreateAction();
        }
    }

    private void triggerWorkFlow(Map<String, List<IObjectData>> dataToTriggerWorkFlow, ApprovalFlowTriggerType approvalFlowTriggerType) {
        if (CollectionUtils.empty(dataToTriggerWorkFlow)) {
            return;
        }
        dataToTriggerWorkFlow.forEach((apiName, dataList) -> dataList.forEach(objectData -> {
            try {
                infraServiceFacade.startWorkFlow(objectData.getId(), apiName, approvalFlowTriggerType.getId(),
                        actionContext.getUser(),
                        Collections.emptyMap(), actionContext.getEventId());
            } catch (Exception e) {
                log.error("startWorkFlow error,tenantId:{},triggerType:{},apiName:{},dataId:{}", actionContext.getTenantId(),
                        approvalFlowTriggerType.getId(), apiName, objectData.getId(), e);
            }
        }));
    }

    private void triggerInvalidWorkFlow() {
        triggerWorkFlow(dataToTriggerInvalidWorkFlow, ApprovalFlowTriggerType.INVALID);
    }


    //todo 看能否迁到fs-crm中
    private List<IObjectData> fillOldData(IObjectData updateData, Map<String, Object> callbackData) {
        IObjectData ret = ObjectDataExt.of(updateData).copy();
        ret.set("eventId", actionContext.getEventId());
        ObjectDataExt.of(ret).putAll(callbackData);
        if (!Utils.NEW_OPPORTUNITY_API_NAME.equals(objectDescribe.getApiName())) {
            return Lists.newArrayList(ret);
        }

        // copy一份ObjectData，防止后续updateData的变更，对old_data造成影响
        IObjectData oldData = ObjectDataExt.of(updateData).copy();
        // fillOldData 在处理时updateData使用了新的map。old_data和oldData公用同一个map
        // 后续操作对updateData的变动不会影响到返回的结果，对oldData的变动会影响到old_data的值
        return ObjectDataDocument.fillOldData(Lists.newArrayList(ret), Lists.newArrayList(oldData));
    }

    protected final void updateDataLifeStatus(IObjectData data) {
        //驳回或撤回作废审批把last_life_status_before_invalid清空
        if (ApprovalFlowTriggerType.INVALID == arg.approvalFlowTriggerType() && !arg.isPass()) {
            serviceFacade.batchUpdateByFields(actionContext.getUser(), Lists.newArrayList(data),
                    ObjectLifeStatus.LIFE_STATUS_AND_LAST_LIFE_STATUS_BEFORE_INVALID_FIELD);
        } else {
            serviceFacade.batchUpdateByFields(actionContext.getUser(), Lists.newArrayList(data), ObjectLifeStatus.LIFE_STATUS_FIELD);
        }
        //同步更新从对象的生命状态
        updateDetailObjectDataLifeStatus(data);
    }

    private void updateDetailObjectDataLifeStatus(IObjectData data) {
        flowCompletedActionServiceFacade.updateDetailObjectDataLifeStatus(arg.approvalFlowTriggerType(), actionContext.getUser(),
                data, dbData, detailObjectDataMap, detailDescribeMap);
    }

    protected final void invalidDataAndDetailData(IObjectData data) {
        List<IObjectData> invalidDataList = Lists.newArrayList();

        // 作废数据
        User user = actionContext.getUser();
        List<IObjectData> detailDataList = Lists.newArrayList();

        //作废主对象
        invalidDataList.addAll(serviceFacade.bulkInvalid(Lists.newArrayList(data), user));
        //2021/12/17 作废后执行按钮上的current动作
        RuntimeException ex = null;
        try {
            infraServiceFacade.doCurrentAction(callbackData, actionContext, objectDescribe, data, detailObjectDataMap);
        } catch (RuntimeException e) {
            log.warn("doCurrentAction fail!", e);
            ex = e;
        }
        //批量作废从对象
        if (CollectionUtils.notEmpty(detailObjectDataMap)) {
            detailObjectDataMap.forEach((x, y) -> detailDataList.addAll(serviceFacade.bulkInvalid(y, user)));
        }
        dealGdpr(Lists.newArrayList(data));
        if (Objects.nonNull(ex)) {
            throw ex;
        }
        invalidDataList.addAll(detailDataList);

        dataToTriggerInvalidWorkFlow.put(arg.getDescribeApiName(), Lists.newArrayList(data));
        detailDataList.stream().collect(Collectors.groupingBy(IObjectData::getDescribeApiName))
                .forEach((apiName, detailList) -> dataToTriggerInvalidWorkFlow.put(apiName, detailList));

        //记录日志
        serviceFacade.masterDetailLog(user, EventType.MODIFY, ActionType.Invalid, describeMap, invalidDataList);
        //发送mq消息
        serviceFacade.sendActionMq(actionContext.getUser(), invalidDataList, ObjectAction.INVALID);
    }

    private void dealGdpr(List<IObjectData> dataList) {
        List<String> ids = dataList.stream().map(DBRecord::getId).collect(Collectors.toList());
        ParallelUtils.createBackgroundTask()
                .submit(() -> infraServiceFacade.bulkInvalidGdprLegalBase(actionContext.getUser(), objectDescribe.getApiName(), ids))
                .run();
    }

    protected final Map<String, List<IObjectData>> getDataDetails() {
        if (dbDetailObjectDataMap != null) {
            return dbDetailObjectDataMap;
        }
        User user = actionContext.getUser();
        if (serviceFacade.isMasterObject(user.getTenantId(), objectDescribe.getApiName())) {
            //获取所有的从describe
            List<IObjectDescribe> detailDescribes = serviceFacade.findDetailDescribes(user.getTenantId(), objectDescribe.getApiName());
            detailDescribes.forEach(it -> {
                this.describeMap.put(it.getApiName(), it);
                this.detailDescribeMap.put(it.getApiName(), it);
            });
            //获取所有的从数据
            dbDetailObjectDataMap = serviceFacade.findDetailObjectDataList(detailDescribes, data, user);
        } else {
            dbDetailObjectDataMap = Maps.newHashMap();
        }
        return dbDetailObjectDataMap;
    }

    protected boolean needTriggerWorkFlow() {
        return !Boolean.FALSE.equals(triggerWorkFlow) && isNotPublicObject();
    }

    protected boolean isNotPublicObject() {
        return Objects.isNull(objectDescribe) || !objectDescribe.isPublicObject();
    }

    @Override
    protected final List<String> getRecordTypes() {
        if (Objects.nonNull(dbData)) {
            return Lists.newArrayList(dbData.getRecordType());
        }
        return null;
    }

    @Override
    protected FlowCompletedActionDomainPlugin.Arg buildDomainPluginArg(String method, List<String> recordTypeList) {
        return FlowCompletedActionDomainPlugin.Arg.builder()
                .triggerType(arg.getTriggerType())
                .status(arg.getStatus())
                .callbackData(callbackData)
                .detailChange(detailChange)
                .objectData(ObjectDataDocument.of(data))
                .detailObjectData(ObjectDataDocument.ofMap(getDataDetails()))
                .dbData(ObjectDataDocument.of(dbData))
                .build();
    }

    @Data
    @ToString
    public static class Arg {
        public final static String PASS = "pass";
        public final static String REJECT = "reject";
        public final static String CANCEL = "cancel";

        private String tenantId;
        private String describeApiName;
        private String dataId;
        private String userId;
        private String triggerType;
        private Object callbackData;
        private String status;
        private String requestId;
        private Map<String, Object> extraData;

        public boolean isPass() {
            return status.equals(PASS);
        }

        public boolean isReject() {
            return REJECT.equals(status);
        }

        public boolean isCancel() {
            return CANCEL.equals(status);
        }

        public ApprovalFlowTriggerType approvalFlowTriggerType() {
            return ApprovalFlowTriggerType.getType(triggerType);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result implements Serializable {
        private static final long serialVersionUID = -6617339206793912901L;
        private Boolean success;
        // 数据是否入库
        private Boolean writeDB;
    }

}
