package com.facishare.crm.privilege.util.pb;

import io.protostuff.Tag;

import java.util.List;

/**
 * Created by lei on 11/30/16.
 */
public class RemoveUsersArg {
    //    message RemoveUsersArg {
//        required string fsEa = 1; //企业账号
//        repeated int32 userIds = 2; //用户id
//    }
    @Tag(1)
    private String fsEa;
    @Tag(2)
    private List<Integer> userIds;


    public String getFsEa() {
        return fsEa;
    }

    public void setFsEa(String fsEa) {
        this.fsEa = fsEa;
    }

    public List<Integer> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<Integer> userIds) {
        this.userIds = userIds;
    }

    @Override
    public String toString() {
        return "RemoveUsersArg{" +
                "fsEa='" + fsEa + '\'' +
                ", userIds=" + userIds +
                '}';
    }
}
