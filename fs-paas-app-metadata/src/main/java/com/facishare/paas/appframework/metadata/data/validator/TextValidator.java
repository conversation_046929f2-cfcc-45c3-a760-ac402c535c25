package com.facishare.paas.appframework.metadata.data.validator;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import static com.facishare.paas.appframework.core.i18n.I18NKey.FIELD_DATA_OVER_SIZE;
import static com.facishare.paas.appframework.core.i18n.I18NKey.FIELD_DATA_TYPE_WRONG;

@Component
public class TextValidator implements IDataTypeValidator, IMaxLengthValidator {
    @Override
    public Set<String> supportFieldTypes() {
        return Sets.newHashSet(IFieldType.TEXT, IFieldType.OBJECT_REFERENCE,
                IFieldType.AUTO_NUMBER, IFieldType.COUNTRY, IFieldType.PROVINCE, IFieldType.CITY, IFieldType.DISTRICT,
                IFieldType.PHONE_NUMBER, IFieldType.URL, IFieldType.RECORD_TYPE,
                IFieldType.MASTER_DETAIL, IFieldType.LOCATION, IFieldType.HTML_RICH_TEXT, IFieldType.EMAIL);
    }

    @Override
    public void validateDataType(String fieldApiName, IObjectData data, IObjectDescribe describe) {
        if (checkParamError(fieldApiName, data, describe)) {
            return;
        }

        Object fieldValue = data.get(fieldApiName);
        if (Objects.isNull(fieldValue) || fieldValue instanceof String) {
            return;
        }

        throw new ValidateException(I18NExt.getOrDefault(FIELD_DATA_TYPE_WRONG, FIELD_DATA_TYPE_WRONG, getField(fieldApiName, describe).getLabel()+fieldApiName, fieldValue));
    }

    @Override
    public void validateMaxLength(String fieldApiName, IObjectData data, IObjectDescribe describe) {
        //先不校验
//        if (checkParamError(fieldApiName, data, describe)) {
//            return;
//        }
//
//        Object fieldValue = data.get(fieldApiName);
//        if (Objects.isNull(fieldValue)) {
//            return;
//        }
//
//        int dataLength = String.valueOf(fieldValue).length();
//        IFieldDescribe field = getField(fieldApiName, describe);
//        Object rawLength = field.get("max_length");
//        if(ObjectUtils.isEmpty(rawLength)) {
//            if(FieldDescribeExt.of(field).isAutoNumber() && AUTO_NUMBER_MAX_LENGTH < dataLength) {
//                throw new ValidateException(I18NExt.getOrDefault(FIELD_DATA_OVER_SIZE, FIELD_DATA_OVER_SIZE,
//                        field.getLabel()+fieldApiName, fieldValue, AUTO_NUMBER_MAX_LENGTH));
//            }
//            return;
//        }
//
//        if(!(rawLength instanceof Integer)) {
//            return;
//        }
//
//        Integer maxLength = (Integer) rawLength;
//        if(maxLength >= dataLength) {
//            return;
//        }
//
//        throw new ValidateException(I18NExt.getOrDefault(FIELD_DATA_OVER_SIZE, FIELD_DATA_OVER_SIZE,
//                field.getLabel()+fieldApiName, fieldValue, maxLength));
    }
}
