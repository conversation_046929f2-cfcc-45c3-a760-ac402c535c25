package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDraftDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IObjectDataDraft;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/11/11 7:41 下午
 */
public class StandardDraftDetailController extends PreDefineController<
        StandardDraftDetailController.Arg, StandardDraftDetailController.Result> {
    private MaskFieldLogicService maskFieldLogicService;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected Result doService(Arg arg) {
        checkArg();
        maskFieldLogicService = serviceFacade.getBean(MaskFieldLogicService.class);
        Optional<IObjectDataDraft> draft = infraServiceFacade.findDraftById(controllerContext.getTenantId(), arg.getDraftId());
        if (!draft.isPresent()) {
            throw new ValidateException(I18N.text(I18NKey.draft_data_deleted));
        }
        if (Objects.isNull(draft.get().getMasterDraftData())) {
            return Result.builder().build();
        }

        if (Objects.isNull(arg.getCalculateOption())) {
            return Result.builder().draft(ObjectDataDraftDocument.of(draft.get())).build();
        }

        IObjectDataDraft dataDraft = draft.get();
        Map<String, List<IObjectData>> detailDataMap = CollectionUtils.nullToEmpty(dataDraft.getSlaveDraftData());
        IObjectData masterDraftData = dataDraft.getMasterDraftData();
        IObjectDescribe objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), masterDraftData.getDescribeApiName());
        Map<String, IObjectDescribe> detailDescribes = serviceFacade.findObjects(controllerContext.getTenantId(), detailDataMap.keySet());

        // 解码加密的掩码字段
        decodeMaskFieldEncryptValue(controllerContext.getUser(), objectDescribe, detailDescribes, masterDraftData, detailDataMap);
        serviceFacade.calculateForDrafts(controllerContext.getUser(), objectDescribe, masterDraftData,
                Lists.newArrayList(detailDescribes.values()),
                detailDataMap, arg.getCalculateOption().getSkipCalculateFields(), arg.getCalculateOption().getSkipCalculateDVField());

        fillMaskFieldValue(controllerContext.getUser(), objectDescribe, detailDescribes, masterDraftData, detailDataMap);

        dealNPathSign(objectDescribe, Lists.newArrayList(masterDraftData));

        dealDetailsNPathSign(detailDescribes, detailDataMap);

        return Result.builder().draft(ObjectDataDraftDocument.of(draft.get())).build();
    }

    private void decodeMaskFieldEncryptValue(User user, IObjectDescribe objectDescribe, Map<String, IObjectDescribe> detailDescribes,
                                             IObjectData masterData, Map<String, List<IObjectData>> detailDataMap) {
        if (!AppFrameworkConfig.maskFieldEncryptGray(user.getTenantId(), objectDescribe.getApiName())) {
            return;
        }
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap(detailDescribes);
        describeMap.put(objectDescribe.getApiName(), objectDescribe);

        Map<String, List<IObjectData>> objectDataMap = Maps.newHashMap(detailDataMap);
        objectDataMap.put(masterData.getDescribeApiName(), Lists.newArrayList(masterData));
        objectDataMap.forEach((objectApiName, dataList) -> {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(describeMap.get(objectApiName));
            maskFieldLogicService.decodeMaskFieldEncryptValue(user, dataList, describeExt);
        });
    }

    private void fillMaskFieldValue(User user, IObjectDescribe objectDescribe, Map<String, IObjectDescribe> detailDescribes,
                                    IObjectData masterData, Map<String, List<IObjectData>> detailDataMap) {
        if (!AppFrameworkConfig.maskFieldEncryptGray(user.getTenantId(), objectDescribe.getApiName())) {
            return;
        }
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(objectDescribe.getApiName(), objectDescribe);
        describeMap.putAll(detailDescribes);
        MaskFieldLogicService.MaskFieldConfig maskFieldConfig = MaskFieldLogicService.MaskFieldConfig.defaultMaskFieldConfig();
        maskFieldConfig.setUseCurrentUser(true);
        maskFieldLogicService.processMaskFieldValue(user, masterData, detailDataMap, describeMap, maskFieldConfig);
    }

    private void checkArg() {
        if (Objects.isNull(arg.getDraftId())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
    }

    private void dealDetailsNPathSign(Map<String, IObjectDescribe> detailDescribes, Map<String, List<IObjectData>> detailDataMap) {
        if (MapUtils.isEmpty(detailDataMap)) {
            return;
        }
        detailDataMap.forEach((objectApiName, dataList) -> {
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(dataList)) {
                return;
            }
            IObjectDescribe detailDesc =  detailDescribes.get(objectApiName);
            dealNPathSign(detailDesc, dataList);
        });
    }

    private void dealNPathSign(IObjectDescribe objDesc, List<IObjectData> dataList) {
        IActionContext ctx = ActionContextExt.of(controllerContext.getUser()).getContext();
        infraServiceFacade.getFileStoreService().generateNPathSignedUrl(ctx,
                AppFrameworkConfig.signedUrlFieldTypes(objDesc.getTenantId()), objDesc, dataList);
    }

    @Data
    public static class Arg {
        @JSONField(name = "draft_id")
        @JsonProperty("draft_id")
        @SerializedName("draft_id")
        private String draftId;

        @JSONField(name = "calculate_option")
        @JsonProperty(value = "calculate_option")
        @SerializedName("calculate_option")
        private CalculateOption calculateOption;
    }

    @Data
    public static class CalculateOption {
        @JSONField(name = "skip_calculate_fields")
        @JsonProperty(value = "skip_calculate_fields")
        @SerializedName("skip_calculate_fields")
        private Map<String, List<String>> skipCalculateFields;

        @JSONField(name = "skip")
        @JsonProperty(value = "skip")
        @SerializedName("skip")
        private Boolean skipCalculateDVField;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        @JSONField(name = "draft")
        @JsonProperty("draft")
        @SerializedName("draft")
        private ObjectDataDraftDocument draft;
    }
}
