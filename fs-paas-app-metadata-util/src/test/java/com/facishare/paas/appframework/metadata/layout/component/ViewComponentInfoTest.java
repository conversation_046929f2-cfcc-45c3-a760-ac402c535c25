package com.facishare.paas.appframework.metadata.layout.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class ViewComponentInfoTest {

  private ViewComponentInfo viewComponentInfo;

  @BeforeEach
  void setUp() {
    viewComponentInfo = new ViewComponentInfo();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ViewComponentInfo的无参构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试无参构造函数")
  void testDefaultConstructor() {
    ViewComponentInfo info = new ViewComponentInfo();
    assertNotNull(info, "无参构造函数创建的对象不应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ViewComponentInfo的Map构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = Maps.newHashMap();
    map.put(IViewComponentInfo.NAME, "test_view");
    map.put(IViewComponentInfo.IS_SHOW, true);
    map.put(IViewComponentInfo.IS_DEFAULT, false);
    map.put(IViewComponentInfo.DISPLAY_TYPE, "table");
    
    ViewComponentInfo info = new ViewComponentInfo(map);
    assertNotNull(info, "Map构造函数创建的对象不应为空");
    assertEquals("test_view", info.getName(), "应正确设置名称");
    assertTrue(info.isShow(), "应正确设置显示状态");
    assertFalse(info.isDefault(), "应正确设置默认状态");
    assertEquals("table", info.getDisplayType(), "应正确设置显示类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试treeView静态工厂方法
   */
  @Test
  @DisplayName("正常场景 - 测试treeView静态工厂方法")
  void testTreeView() {
    IViewComponentInfo info = ViewComponentInfo.treeView();
    
    assertNotNull(info, "静态工厂方法创建的对象不应为空");
    assertEquals(IViewComponentInfo.TREE_VIEW, info.getName(), "应设置为树形视图");
    assertFalse(info.isDefault(), "应设置为非默认视图");
    assertTrue(info.isShow(), "应设置为显示状态");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getName和setName方法
   */
  @Test
  @DisplayName("正常场景 - 测试Name属性")
  void testName() {
    // 初始状态应为null
    assertNull(viewComponentInfo.getName(), "初始名称应为null");
    
    // 设置名称
    viewComponentInfo.setName("list_view");
    assertEquals("list_view", viewComponentInfo.getName(), "应正确设置名称");
    
    // 设置为null
    viewComponentInfo.setName(null);
    assertNull(viewComponentInfo.getName(), "应支持设置为null");
    
    // 设置为空字符串
    viewComponentInfo.setName("");
    assertEquals("", viewComponentInfo.getName(), "应支持设置为空字符串");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isShow和setIsShow方法
   */
  @Test
  @DisplayName("正常场景 - 测试IsShow属性")
  void testIsShow() {
    // 初始状态应为true（BooleanUtils.isNotFalse的默认行为）
    assertTrue(viewComponentInfo.isShow(), "初始显示状态应为true");
    
    // 设置为false
    viewComponentInfo.setIsShow(false);
    assertFalse(viewComponentInfo.isShow(), "应正确设置为false");
    
    // 设置为true
    viewComponentInfo.setIsShow(true);
    assertTrue(viewComponentInfo.isShow(), "应正确设置为true");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isDefault和setIsDefault方法
   */
  @Test
  @DisplayName("正常场景 - 测试IsDefault属性")
  void testIsDefault() {
    // 初始状态应为false
    assertFalse(viewComponentInfo.isDefault(), "初始默认状态应为false");
    
    // 设置为true
    viewComponentInfo.setIsDefault(true);
    assertTrue(viewComponentInfo.isDefault(), "应正确设置为true");
    
    // 设置为false
    viewComponentInfo.setIsDefault(false);
    assertFalse(viewComponentInfo.isDefault(), "应正确设置为false");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getTimeDimension和setTimeDimension方法
   */
  @Test
  @DisplayName("正常场景 - 测试TimeDimension属性")
  void testTimeDimension() {
    // 初始状态应为空列表
    List<String> initialTimeDimension = viewComponentInfo.getTimeDimension();
    assertNotNull(initialTimeDimension, "初始时间维度列表不应为空");
    assertTrue(initialTimeDimension.isEmpty(), "初始时间维度列表应为空");
    
    // 设置时间维度
    List<String> timeDimensions = Lists.newArrayList("year", "month", "day");
    viewComponentInfo.setTimeDimension(timeDimensions);
    
    List<String> retrievedTimeDimensions = viewComponentInfo.getTimeDimension();
    assertNotNull(retrievedTimeDimensions, "获取的时间维度列表不应为空");
    assertEquals(3, retrievedTimeDimensions.size(), "时间维度列表大小应为3");
    assertTrue(retrievedTimeDimensions.contains("year"), "应包含year");
    assertTrue(retrievedTimeDimensions.contains("month"), "应包含month");
    assertTrue(retrievedTimeDimensions.contains("day"), "应包含day");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getDisplayType和setDisplayType方法
   */
  @Test
  @DisplayName("正常场景 - 测试DisplayType属性")
  void testDisplayType() {
    // 初始状态应为null
    assertNull(viewComponentInfo.getDisplayType(), "初始显示类型应为null");
    
    // 设置显示类型
    viewComponentInfo.setDisplayType("chart");
    assertEquals("chart", viewComponentInfo.getDisplayType(), "应正确设置显示类型");
    
    // 设置为null
    viewComponentInfo.setDisplayType(null);
    assertNull(viewComponentInfo.getDisplayType(), "应支持设置为null");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFields和setFields方法
   */
  @Test
  @DisplayName("正常场景 - 测试Fields属性")
  void testFields() {
    // 初始状态应为空列表
    List<String> initialFields = viewComponentInfo.getFields();
    assertNotNull(initialFields, "初始字段列表不应为空");
    assertTrue(initialFields.isEmpty(), "初始字段列表应为空");
    
    // 设置字段列表
    List<String> fields = Lists.newArrayList("field1", "field2", "field3");
    viewComponentInfo.setFields(fields);
    
    List<String> retrievedFields = viewComponentInfo.getFields();
    assertNotNull(retrievedFields, "获取的字段列表不应为空");
    assertEquals(3, retrievedFields.size(), "字段列表大小应为3");
    assertTrue(retrievedFields.contains("field1"), "应包含field1");
    assertTrue(retrievedFields.contains("field2"), "应包含field2");
    assertTrue(retrievedFields.contains("field3"), "应包含field3");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getLocationField和setLocationField方法
   */
  @Test
  @DisplayName("正常场景 - 测试LocationField属性")
  void testLocationField() {
    // 初始状态应为null
    assertNull(viewComponentInfo.getLocationField(), "初始位置字段应为null");
    
    // 设置位置字段
    viewComponentInfo.setLocationField("address_field");
    assertEquals("address_field", viewComponentInfo.getLocationField(), "应正确设置位置字段");
    
    // 设置为null
    viewComponentInfo.setLocationField(null);
    assertNull(viewComponentInfo.getLocationField(), "应支持设置为null");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getBubbleInfo和setBubbleInfo方法 - 正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试BubbleInfo属性")
  void testBubbleInfo_NormalScenario() {
    // 初始状态应为null
    assertNull(viewComponentInfo.getBubbleInfo(), "初始气泡信息应为null");
    
    // 创建气泡信息
    Map<String, Object> bubbleMap = Maps.newHashMap();
    bubbleMap.put(IMapViewBubbleInfo.FIELD, "status_field");
    MapViewBubbleInfo bubbleInfo = new MapViewBubbleInfo(bubbleMap);
    
    // 设置气泡信息
    viewComponentInfo.setBubbleInfo(bubbleInfo);
    
    // 获取气泡信息
    IMapViewBubbleInfo retrievedBubbleInfo = viewComponentInfo.getBubbleInfo();
    assertNotNull(retrievedBubbleInfo, "获取的气泡信息不应为空");
    assertEquals("status_field", retrievedBubbleInfo.getField(), "气泡信息字段应正确");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setBubbleInfo方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试setBubbleInfo方法处理null值")
  void testSetBubbleInfo_NullValue() {
    // 先设置一个气泡信息
    Map<String, Object> bubbleMap = Maps.newHashMap();
    bubbleMap.put(IMapViewBubbleInfo.FIELD, "test_field");
    MapViewBubbleInfo bubbleInfo = new MapViewBubbleInfo(bubbleMap);
    viewComponentInfo.setBubbleInfo(bubbleInfo);
    
    // 设置为null（应该不会改变原有值）
    viewComponentInfo.setBubbleInfo(null);
    
    // 验证原有值保持不变
    IMapViewBubbleInfo retrievedBubbleInfo = viewComponentInfo.getBubbleInfo();
    assertNotNull(retrievedBubbleInfo, "设置null不应改变原有值");
    assertEquals("test_field", retrievedBubbleInfo.getField(), "原有气泡信息应保持不变");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口实现
   */
  @Test
  @DisplayName("正常场景 - 测试接口实现")
  void testInterfaceImplementation() {
    // 验证实现了IViewComponentInfo接口
    assertTrue(viewComponentInfo instanceof IViewComponentInfo, 
               "应实现IViewComponentInfo接口");
    
    // 验证实现了JsonCompatible接口
    assertTrue(viewComponentInfo instanceof com.facishare.paas.metadata.api.JsonCompatible, 
               "应实现JsonCompatible接口");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试序列化版本号
   */
  @Test
  @DisplayName("正常场景 - 测试序列化版本号")
  void testSerialVersionUID() {
    // 验证类有序列化版本号（通过反射检查）
    try {
      java.lang.reflect.Field field = ViewComponentInfo.class.getDeclaredField("serialVersionUID");
      field.setAccessible(true);
      long serialVersionUID = field.getLong(null);
      assertEquals(1640838484577233334L, serialVersionUID, "序列化版本号应正确");
    } catch (Exception e) {
      fail("应该有serialVersionUID字段");
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试继承的AbstractComponentInfoDocument功能
   */
  @Test
  @DisplayName("正常场景 - 测试继承功能")
  void testInheritedFunctionality() {
    // 验证继承自AbstractComponentInfoDocument
    assertTrue(viewComponentInfo instanceof AbstractComponentInfoDocument, 
               "应继承自AbstractComponentInfoDocument");
    
    // 通过Map构造函数测试继承的功能
    Map<String, Object> map = Maps.newHashMap();
    map.put("custom_field", "custom_value");
    map.put(IViewComponentInfo.NAME, "custom_view");
    
    ViewComponentInfo info = new ViewComponentInfo(map);
    assertEquals("custom_view", info.getName(), "应正确处理名称");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试完整的视图组件配置
   */
  @Test
  @DisplayName("正常场景 - 测试完整视图组件配置")
  void testCompleteViewConfiguration() {
    // 设置完整的视图配置
    viewComponentInfo.setName("dashboard_view");
    viewComponentInfo.setIsShow(true);
    viewComponentInfo.setIsDefault(true);
    viewComponentInfo.setDisplayType("dashboard");
    viewComponentInfo.setTimeDimension(Lists.newArrayList("year", "quarter", "month"));
    viewComponentInfo.setFields(Lists.newArrayList("revenue", "profit", "orders"));
    viewComponentInfo.setLocationField("office_location");
    
    // 创建气泡信息
    Map<String, Object> bubbleMap = Maps.newHashMap();
    bubbleMap.put(IMapViewBubbleInfo.FIELD, "priority");
    MapViewBubbleInfo bubbleInfo = new MapViewBubbleInfo(bubbleMap);
    viewComponentInfo.setBubbleInfo(bubbleInfo);
    
    // 验证完整配置
    assertEquals("dashboard_view", viewComponentInfo.getName(), "名称应正确");
    assertTrue(viewComponentInfo.isShow(), "应显示");
    assertTrue(viewComponentInfo.isDefault(), "应为默认视图");
    assertEquals("dashboard", viewComponentInfo.getDisplayType(), "显示类型应正确");
    assertEquals(3, viewComponentInfo.getTimeDimension().size(), "时间维度应有3个");
    assertEquals(3, viewComponentInfo.getFields().size(), "字段应有3个");
    assertEquals("office_location", viewComponentInfo.getLocationField(), "位置字段应正确");
    assertNotNull(viewComponentInfo.getBubbleInfo(), "气泡信息不应为空");
    assertEquals("priority", viewComponentInfo.getBubbleInfo().getField(), "气泡信息字段应正确");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试多种显示类型
   */
  @Test
  @DisplayName("正常场景 - 测试多种显示类型")
  void testVariousDisplayTypes() {
    String[] displayTypes = {"table", "chart", "card", "map", "calendar", "kanban", "timeline"};
    
    for (String displayType : displayTypes) {
      viewComponentInfo.setDisplayType(displayType);
      assertEquals(displayType, viewComponentInfo.getDisplayType(), "应正确设置显示类型: " + displayType);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试特殊字符处理
   */
  @Test
  @DisplayName("边界场景 - 测试特殊字符处理")
  void testSpecialCharacters() {
    // 测试包含特殊字符的名称和字段
    String specialName = "view_with_@#$%_chars";
    List<String> specialFields = Lists.newArrayList("field_with_underscore", "field-with-dash", "field.with.dot");
    
    viewComponentInfo.setName(specialName);
    viewComponentInfo.setFields(specialFields);
    
    assertEquals(specialName, viewComponentInfo.getName(), "应正确处理特殊字符名称");
    assertEquals(3, viewComponentInfo.getFields().size(), "应正确处理特殊字符字段");
    
    for (String field : specialFields) {
      assertTrue(viewComponentInfo.getFields().contains(field), "应包含特殊字符字段: " + field);
    }
  }
}
