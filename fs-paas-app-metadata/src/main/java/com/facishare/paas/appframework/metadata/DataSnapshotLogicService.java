package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.DataSnapshotResult;
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot;
import com.facishare.paas.metadata.api.IDataSnapshot;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

public interface DataSnapshotLogicService {

    DataSnapshotResult createSnapshotWithData(User user, IObjectData newData, IObjectData oldData,
                                              IObjectDescribe describe, RequestContext.BizInfo bizInfo);

    void createSnapshot(User user, String describeApiName, String dataId, ObjectDataSnapshot snapshot);

    ObjectDataSnapshot findAndMergeSnapshot(String tenantId, String describeApiName, String dataId, String biz, String bizId);

    void deleteSnapshot(User user, String describeApiName, String dataId, String biz, String bizId);

    void updateSnapshot(User user, IDataSnapshot dataSnapshot);
}
