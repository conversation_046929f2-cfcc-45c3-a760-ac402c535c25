package com.facishare.paas.appframework.common.service.impl

import com.facishare.paas.appframework.common.service.GeoAddressProxy
import com.facishare.paas.appframework.common.service.GeoAddressService
import com.facishare.paas.appframework.common.service.dto.GeoAddressDTO
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.RestUtils
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class GeoAddressServiceImplTest extends Specification {
    GeoAddressServiceImpl geoAddressService
    GeoAddressProxy geoAddressProxy = Mock(GeoAddressProxy)

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def setup() {
        geoAddressService = new GeoAddressServiceImpl()
        geoAddressService.geoAddressProxy = geoAddressProxy
    }

    def "测试getAddressByGeo方法 - 成功场景"() {
        given:
        def user = Mock(User)
        def request = new GeoAddressDTO.ReverseGeoRequest(
                location: "39.984154,116.307490",
                coordType: 1,
                radius: 1000,
                extensions: "all"
        )
        def headers = ["X-FS-TENANT-ID": "12345", "X-FS-USER-ID": "67890"]
        def geoResult = new GeoAddressDTO.GeoResult(
                address: "北京市海淀区",
                country: "中国",
                province: "北京市",
                city: "北京市",
                district: "海淀区",
                township: "中关村",
                street: "丹棱街",
                streetNumber: "5号"
        )
        def restResult = new GeoAddressDTO.RestResult(
                success: true,
                data: geoResult
        )

        when:
        def result = geoAddressService.getAddressByGeo(user, request)

        then:
        1 * user.getTenantId() >> "12345"
        1 * user.getUserId() >> "67890"
        1 * RestUtils.buildHeaders(user) >> headers
        1 * geoAddressProxy.getAddressByGeo(headers, request) >> restResult
        
        result == geoResult
        result.address == "北京市海淀区"
        result.country == "中国"
        result.province == "北京市"
        result.city == "北京市"
        result.district == "海淀区"
    }

    def "测试getAddressByGeo方法 - 失败场景"() {
        given:
        def user = Mock(User)
        def request = new GeoAddressDTO.ReverseGeoRequest(
                location: "39.984154,116.307490",
                coordType: 1,
                radius: 1000,
                extensions: "all"
        )
        def headers = ["X-FS-TENANT-ID": "12345", "X-FS-USER-ID": "67890"]
        def restResult = new GeoAddressDTO.RestResult(
                success: false,
                data: null
        )

        when:
        def result = geoAddressService.getAddressByGeo(user, request)

        then:
        1 * user.getTenantId() >> "12345"
        1 * user.getUserId() >> "67890"
        1 * RestUtils.buildHeaders(user) >> headers
        1 * geoAddressProxy.getAddressByGeo(headers, request) >> restResult
        
        result == null
    }

    def "测试getAddressByGeo方法 - 返回null场景"() {
        given:
        def user = Mock(User)
        def request = new GeoAddressDTO.ReverseGeoRequest(
                location: "39.984154,116.307490",
                coordType: 1,
                radius: 1000,
                extensions: "all"
        )
        def headers = ["X-FS-TENANT-ID": "12345", "X-FS-USER-ID": "67890"]

        when:
        def result = geoAddressService.getAddressByGeo(user, request)

        then:
        1 * user.getTenantId() >> "12345"
        1 * user.getUserId() >> "67890"
        1 * RestUtils.buildHeaders(user) >> headers
        1 * geoAddressProxy.getAddressByGeo(headers, request) >> null
        
        result == null
    }

    def "测试getAddressByGeo方法 - 各种边界值"() {
        given:
        def user = Mock(User)
        def request = new GeoAddressDTO.ReverseGeoRequest(
                location: inputLocation,
                coordType: inputCoordType,
                radius: inputRadius,
                extensions: inputExtensions
        )
        def headers = ["X-FS-TENANT-ID": "12345", "X-FS-USER-ID": "67890"]
        def geoResult = new GeoAddressDTO.GeoResult(
                address: "测试地址"
        )
        def restResult = new GeoAddressDTO.RestResult(
                success: true,
                data: geoResult
        )

        when:
        def result = geoAddressService.getAddressByGeo(user, request)

        then:
        1 * user.getTenantId() >> "12345"
        1 * user.getUserId() >> "67890"
        1 * RestUtils.buildHeaders(user) >> headers
        1 * geoAddressProxy.getAddressByGeo(headers, request) >> restResult
        
        result == geoResult
        result.address == "测试地址"

        where:
        inputLocation           | inputCoordType | inputRadius | inputExtensions
        "0,0"                   | 1              | 0           | "all"
        "90,180"                | 2              | 1           | "base"
        "-90,-180"              | 3              | 1000        | ""
        "39.984154,116.307490"  | 1              | Integer.MAX_VALUE | null
        ""                      | 0              | -1          | "all"
    }
} 