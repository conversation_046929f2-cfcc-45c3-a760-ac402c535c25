package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.handler.EnableOrDisableHandler;
import com.facishare.paas.appframework.core.predef.service.dto.handler.UpsertHandlerDefinitionAndRuntimeConfig;
import com.facishare.paas.appframework.metadata.handler.HandlerLogicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by zhouwr on 2023/2/23.
 */
@Service
@ServiceModule("handler")
public class ObjectHandlerService {
    @Autowired
    private HandlerLogicService handlerLogicService;

    @ServiceMethod("upsert_handler_definition_and_runtime_config")
    public UpsertHandlerDefinitionAndRuntimeConfig.Result upsertHandlerDefinitionAndRuntimeConfig(UpsertHandlerDefinitionAndRuntimeConfig.Arg arg,
                                                                                                  ServiceContext context) {
        handlerLogicService.batchUpsertHandlerDefinitionAndRuntimeConfig(context.getUser(), arg.getDefinitions(), arg.getRuntimeConfigs());
        return new UpsertHandlerDefinitionAndRuntimeConfig.Result();
    }

    @ServiceMethod("enable_handler")
    public EnableOrDisableHandler.Result enableHandler(EnableOrDisableHandler.Arg arg, ServiceContext context) {
        handlerLogicService.enableHandler(context.getUser(), arg.getUniqueKey());
        return new EnableOrDisableHandler.Result();
    }

    @ServiceMethod("disable_handler")
    public EnableOrDisableHandler.Result disableHandler(EnableOrDisableHandler.Arg arg, ServiceContext context) {
        handlerLogicService.disableHandler(context.getUser(), arg.getUniqueKey());
        return new EnableOrDisableHandler.Result();
    }
}
