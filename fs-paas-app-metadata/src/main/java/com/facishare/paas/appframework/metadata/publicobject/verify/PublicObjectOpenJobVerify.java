package com.facishare.paas.appframework.metadata.publicobject.verify;

import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.publicobject.module.DesignerResourceResult;
import com.facishare.paas.appframework.metadata.publicobject.module.InternationalVerifyMessage;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobParamVerifyInfo;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/26
 */
@Slf4j
@Component
public class PublicObjectOpenJobVerify extends AbstractPublicObjectJobVerify {

    @Override
    public List<PublicObjectJobType> getSupportedJobTypes() {
        return ImmutableList.of(PublicObjectJobType.OPEN_JOB, PublicObjectJobType.VERIFY_JOB);
    }

    @Override
    protected VerifyResult verifyDescribeWithUpstream(User user, IObjectDescribe describe) {
        VerifyResult verifyResult = VerifyResult.buildEmpty();
        if (describe.isPublicObject()) {
            log.warn("verifyByUpstream already is public object, ei:{}, apiName:{}", user.getTenantId(), describe.getApiName());
            InternationalVerifyMessage verifyMessage = InternationalVerifyMessage.of(
                    I18NKey.ALREADY_ENABLED_PUBLIC_OBJECT,
                    I18NExt.text(I18NKey.ALREADY_ENABLED_PUBLIC_OBJECT, describe.getDisplayName()),
                    InternationalVerifyMessage.buildDescribeDisplayName(describe));
            verifyResult.append(verifyMessage);
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        if (describeExt.isChangeOrderObject() || describeExt.enabledChangeOrder()) {
            InternationalVerifyMessage verifyMessage = InternationalVerifyMessage.of(
                    I18NKey.PUBLIC_OBJECT_CANNOT_OPEN_CHANGE_ORDER,
                    I18NExt.text(I18NKey.PUBLIC_OBJECT_CANNOT_OPEN_CHANGE_ORDER, describe.getDisplayName()),
                    InternationalVerifyMessage.buildDescribeDisplayName(describe));
            verifyResult.append(verifyMessage);
        }
        return verifyResult;
    }

    @Override
    protected IObjectDescribe modifyVerifyDescribeByUpstream(IObjectDescribe upstreamDescribe, IObjectDescribe downstreamDescribe, PublicObjectJobParamVerifyInfo jobParam) {
        // 校验任务，1企业的描述并没有修改，这里需要加工描述
        if (jobParam.getJobType() == PublicObjectJobType.VERIFY_JOB) {
            DesignerResourceResult designerResource = DesignerResourceResult.fromDescribe(upstreamDescribe);
            modifyDescribePublicFlag(User.systemUser(upstreamDescribe.getTenantId()), jobParam, upstreamDescribe, designerResource);
        }
        return super.modifyVerifyDescribeByUpstream(upstreamDescribe, downstreamDescribe, jobParam);
    }

    @Override
    protected VerifyResult initAndVerify(User user, IObjectDescribe describe, PublicObjectJobParamVerifyInfo jobParam) {
        VerifyResult verifyResult = VerifyResult.buildEmpty();
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        verifyResult.merge(verifyByMasterDetail(user, describeExt));
        if (!verifyResult.success()) {
            return verifyResult;
        }
        // 根据配置文件补充预设字段的配置
        DesignerResourceResult designerResource = DesignerResourceResult.fromDescribe(describeExt);
        // 修改描述上的公共对象标记
        verifyResult.merge(modifyDescribePublicFlag(user, jobParam, describeExt, designerResource));
        // 对象间的依赖关系校验
        verifyResult.merge(verifyByObjectRelation(user, describeExt, jobParam.getUpstreamTenantId()));
        // 校验字段依赖关系
        verifyResult.merge(verifyByDesignerResourceResult(user, describeExt, designerResource));
        // 校验字段必填和公共字段标记的关系
        verifyResult.merge(verifyByPublicFields(user, describe));
        return verifyResult;
    }

    private VerifyResult modifyDescribePublicFlag(User user, PublicObjectJobParamVerifyInfo jobParam,
                                                  IObjectDescribe describe, DesignerResourceResult designerResource) {
        VerifyResult verifyResult = VerifyResult.buildEmpty();
        jobParam.fillPublicFieldInfoByDesignerResource(designerResource);
        List<InternationalVerifyMessage> errorMessage = jobParam.setPublicObjectFlag(user, describe);
        verifyResult.appends(errorMessage);
        return verifyResult;
    }

}
