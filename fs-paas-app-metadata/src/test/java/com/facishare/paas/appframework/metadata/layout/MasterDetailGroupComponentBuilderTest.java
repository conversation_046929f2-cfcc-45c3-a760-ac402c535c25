package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderLogicService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class MasterDetailGroupComponentBuilderTest {

  @Mock
  private RelatedObjectDescribeStructure detailObjectDescribeStructure;

  @Mock
  private ObjectDescribeExt objectDescribeExt;

  @Mock
  private IObjectData objectData;

  @Mock
  private LayoutExt layoutExt;

  @Mock
  private User user;

  @Mock
  private ILayout listLayout;

  @Mock
  private IRecordTypeOption recordTypeOption;

  @Mock
  private ButtonLogicService buttonLogicService;

  @Mock
  private ChangeOrderLogicService changeOrderLogicService;

  @Mock
  private IObjectDescribe detailObjectDescribe;

  private List<RelatedObjectDescribeStructure> detailObjectsDescribeList;
  private Map<String, ILayout> listLayoutMap;
  private Map<String, List<IRecordTypeOption>> recordTypeOptionMap;
  private Map<String, List<String>> matchRecordTypeMap;
  private Map<String, List<String>> hasDataRecordTypeMap;
  private Map<String, Object> relatedListComponent;

  @BeforeEach
  void setUp() {
    detailObjectsDescribeList = Lists.newArrayList();
    detailObjectsDescribeList.add(detailObjectDescribeStructure);

    listLayoutMap = Maps.newHashMap();
    listLayoutMap.put("detail_object", listLayout);

    recordTypeOptionMap = Maps.newHashMap();
    recordTypeOptionMap.put("detail_object", Lists.newArrayList(recordTypeOption));

    matchRecordTypeMap = Maps.newHashMap();
    matchRecordTypeMap.put("detail_object", Lists.newArrayList("record_type_1"));

    hasDataRecordTypeMap = Maps.newHashMap();
    hasDataRecordTypeMap.put("detail_object", Lists.newArrayList("record_type_1"));

    relatedListComponent = Maps.newHashMap();

    // 设置基本的mock行为
    when(detailObjectDescribeStructure.getRelatedObjectDescribe()).thenReturn(detailObjectDescribe);
    when(detailObjectDescribe.getApiName()).thenReturn("detail_object");
    when(detailObjectDescribe.getId()).thenReturn("detail_id");
    when(detailObjectDescribeStructure.getRelatedListLabel()).thenReturn("Detail List");
    when(recordTypeOption.getApiName()).thenReturn("record_type_1");
    when(objectDescribeExt.getApiName()).thenReturn("master_object");
    when(objectDescribeExt.isChangeOrderObject()).thenReturn(false);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试MasterDetailGroupComponentBuilder构造函数正常场景
   */
  @Test
  @DisplayName("正常场景 - 构造MasterDetailGroupComponentBuilder对象")
  void testMasterDetailGroupComponentBuilderConstructor_Success() {
    // 执行被测试方法
    MasterDetailGroupComponentBuilder builder = MasterDetailGroupComponentBuilder.builder()
        .detailObjectsDescribeList(detailObjectsDescribeList)
        .objectDescribeExt(objectDescribeExt)
        .objectData(objectData)
        .layoutExt(layoutExt)
        .user(user)
        .listLayoutMap(listLayoutMap)
        .recordTypeOptionMap(recordTypeOptionMap)
        .matchRecordTypeMap(matchRecordTypeMap)
        .hasDataRecordTypeMap(hasDataRecordTypeMap)
        .relatedListComponent(relatedListComponent)
        .buttonLogicService(buttonLogicService)
        .changeOrderLogicService(changeOrderLogicService)
        .build();

    // 验证结果
    assertNotNull(builder);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试空detailObjectsDescribeList的场景
   */
  @Test
  @DisplayName("边界场景 - 空detailObjectsDescribeList")
  void testMasterDetailGroupComponentBuilder_EmptyDetailObjectsList() {
    // 执行被测试方法
    MasterDetailGroupComponentBuilder builder = MasterDetailGroupComponentBuilder.builder()
        .detailObjectsDescribeList(Lists.newArrayList())
        .objectDescribeExt(objectDescribeExt)
        .layoutExt(layoutExt)
        .user(user)
        .build();

    // 验证结果
    assertNotNull(builder);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试null参数的场景
   */
  @Test
  @DisplayName("边界场景 - null参数处理")
  void testMasterDetailGroupComponentBuilder_NullParameters() {
    // 执行被测试方法
    MasterDetailGroupComponentBuilder builder = MasterDetailGroupComponentBuilder.builder()
        .detailObjectsDescribeList(null)
        .objectDescribeExt(null)
        .objectData(null)
        .layoutExt(null)
        .user(null)
        .listLayoutMap(null)
        .recordTypeOptionMap(null)
        .matchRecordTypeMap(null)
        .hasDataRecordTypeMap(null)
        .relatedListComponent(null)
        .buttonLogicService(null)
        .changeOrderLogicService(null)
        .build();

    // 验证结果
    assertNotNull(builder);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试变更单对象场景
   */
  @Test
  @DisplayName("正常场景 - 变更单对象处理")
  void testMasterDetailGroupComponentBuilder_ChangeOrderObject() {
    // 准备测试数据
    when(objectDescribeExt.isChangeOrderObject()).thenReturn(true);
    when(changeOrderLogicService.findOriginalApiNameByChangeOrder(user, "detail_object")).thenReturn("original_detail_object");

    // 执行被测试方法
    MasterDetailGroupComponentBuilder builder = MasterDetailGroupComponentBuilder.builder()
        .detailObjectsDescribeList(detailObjectsDescribeList)
        .objectDescribeExt(objectDescribeExt)
        .layoutExt(layoutExt)
        .user(user)
        .changeOrderLogicService(changeOrderLogicService)
        .build();

    // 验证结果
    assertNotNull(builder);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试Builder模式的完整性
   */
  @Test
  @DisplayName("正常场景 - 测试Builder模式完整性")
  void testMasterDetailGroupComponentBuilder_BuilderPattern() {
    // 测试Builder的链式调用
    assertDoesNotThrow(() -> {
      MasterDetailGroupComponentBuilder builder = MasterDetailGroupComponentBuilder.builder()
          .detailObjectsDescribeList(detailObjectsDescribeList)
          .objectDescribeExt(objectDescribeExt)
          .objectData(objectData)
          .layoutExt(layoutExt)
          .user(user)
          .listLayoutMap(listLayoutMap)
          .recordTypeOptionMap(recordTypeOptionMap)
          .matchRecordTypeMap(matchRecordTypeMap)
          .hasDataRecordTypeMap(hasDataRecordTypeMap)
          .relatedListComponent(relatedListComponent)
          .buttonLogicService(buttonLogicService)
          .changeOrderLogicService(changeOrderLogicService)
          .build();
      
      assertNotNull(builder);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试基本功能验证
   */
  @Test
  @DisplayName("正常场景 - 验证基本功能")
  void testMasterDetailGroupComponentBuilder_BasicFunctionality() {
    // 执行被测试方法
    MasterDetailGroupComponentBuilder builder = MasterDetailGroupComponentBuilder.builder()
        .detailObjectsDescribeList(detailObjectsDescribeList)
        .objectDescribeExt(objectDescribeExt)
        .objectData(objectData)
        .layoutExt(layoutExt)
        .user(user)
        .listLayoutMap(listLayoutMap)
        .recordTypeOptionMap(recordTypeOptionMap)
        .matchRecordTypeMap(matchRecordTypeMap)
        .hasDataRecordTypeMap(hasDataRecordTypeMap)
        .relatedListComponent(relatedListComponent)
        .buttonLogicService(buttonLogicService)
        .changeOrderLogicService(changeOrderLogicService)
        .build();

    // 验证基本功能
    assertNotNull(builder);
    assertDoesNotThrow(() -> {
      builder.toString();
    });
    assertDoesNotThrow(() -> {
      builder.hashCode();
    });
    assertDoesNotThrow(() -> {
      builder.equals(builder);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试对象状态一致性
   */
  @Test
  @DisplayName("正常场景 - 测试对象状态一致性")
  void testMasterDetailGroupComponentBuilder_StateConsistency() {
    // 创建两个相同配置的对象
    MasterDetailGroupComponentBuilder builder1 = MasterDetailGroupComponentBuilder.builder()
        .detailObjectsDescribeList(detailObjectsDescribeList)
        .objectDescribeExt(objectDescribeExt)
        .objectData(objectData)
        .layoutExt(layoutExt)
        .user(user)
        .listLayoutMap(listLayoutMap)
        .recordTypeOptionMap(recordTypeOptionMap)
        .matchRecordTypeMap(matchRecordTypeMap)
        .hasDataRecordTypeMap(hasDataRecordTypeMap)
        .relatedListComponent(relatedListComponent)
        .buttonLogicService(buttonLogicService)
        .changeOrderLogicService(changeOrderLogicService)
        .build();

    MasterDetailGroupComponentBuilder builder2 = MasterDetailGroupComponentBuilder.builder()
        .detailObjectsDescribeList(detailObjectsDescribeList)
        .objectDescribeExt(objectDescribeExt)
        .objectData(objectData)
        .layoutExt(layoutExt)
        .user(user)
        .listLayoutMap(listLayoutMap)
        .recordTypeOptionMap(recordTypeOptionMap)
        .matchRecordTypeMap(matchRecordTypeMap)
        .hasDataRecordTypeMap(hasDataRecordTypeMap)
        .relatedListComponent(relatedListComponent)
        .buttonLogicService(buttonLogicService)
        .changeOrderLogicService(changeOrderLogicService)
        .build();

    // 验证对象独立性
    assertNotNull(builder1);
    assertNotNull(builder2);
    assertNotSame(builder1, builder2);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试异常处理能力
   */
  @Test
  @DisplayName("异常场景 - 测试异常处理")
  void testMasterDetailGroupComponentBuilder_ExceptionHandling() {
    // 测试在异常情况下的处理
    assertDoesNotThrow(() -> {
      MasterDetailGroupComponentBuilder builder = MasterDetailGroupComponentBuilder.builder()
          .detailObjectsDescribeList(detailObjectsDescribeList)
          .objectDescribeExt(objectDescribeExt)
          .objectData(objectData)
          .layoutExt(layoutExt)
          .user(user)
          .listLayoutMap(listLayoutMap)
          .recordTypeOptionMap(recordTypeOptionMap)
          .matchRecordTypeMap(matchRecordTypeMap)
          .hasDataRecordTypeMap(hasDataRecordTypeMap)
          .relatedListComponent(relatedListComponent)
          .buttonLogicService(buttonLogicService)
          .changeOrderLogicService(changeOrderLogicService)
          .build();

      // 验证对象在异常情况下的稳定性
      assertNotNull(builder);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试最小参数集合
   */
  @Test
  @DisplayName("正常场景 - 测试最小参数集合")
  void testMasterDetailGroupComponentBuilder_MinimalParameters() {
    // 测试最小参数集合
    MasterDetailGroupComponentBuilder builder = MasterDetailGroupComponentBuilder.builder()
        .detailObjectsDescribeList(detailObjectsDescribeList)
        .build();

    assertNotNull(builder);
  }
}
