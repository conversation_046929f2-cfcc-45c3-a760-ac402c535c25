package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.metadata.layout.component.NewTableComponentExt
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IFieldType
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ui.layout.FormField
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent
import com.facishare.paas.metadata.ui.layout.IComponent
import com.facishare.paas.metadata.ui.layout.IFieldSection
import com.facishare.paas.metadata.ui.layout.IFormField
import com.facishare.paas.metadata.ui.layout.ITableColumn
import com.facishare.paas.metadata.ui.layout.ITableComponent
import com.google.common.collect.Sets
import spock.lang.Specification
import spock.lang.Unroll

class TableComponentExtTest extends Specification {

    def "test of factory method"() {
        given: "一个ITableComponent对象"
        def tableComponent = Mock(ITableComponent) {
            getName() >> "test_table"
            getType() >> "table"
        }

        when: "使用of方法创建TableComponentExt"
        def tableComponentExt = TableComponentExt.of(tableComponent)

        then: "应该正确包装表格组件"
        tableComponentExt != null
        tableComponentExt.getTableComponent() == tableComponent
        tableComponentExt.getName() == "test_table"
        tableComponentExt.getType() == "table"
    }

    def "test removeFields without new layout"() {
        given: "包含字段的表格组件"
        def fieldMaps = [
                ["name": "field1", "api_name": "field1"],
                ["name": "field2", "api_name": "field2"],
                ["name": "field3", "api_name": "field3"]
        ]
        def fieldSection = Mock(IFieldSection) {
            getFields() >> [
                    Mock(IFormField) { getFieldName() >> "field1" },
                    Mock(IFormField) { getFieldName() >> "field2" }
            ]
            setFields(_) >> null
        }
        
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> false
            get(ITableComponent.INCLUDE_FIELDS) >> fieldMaps
            getFieldSections() >> [fieldSection]
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "移除字段"
        tableComponentExt.removeFields(Sets.newHashSet("field1", "field3"))

        then: "应该从include_fields和field_sections中移除字段"
        fieldMaps.size() == 1
        fieldMaps[0]["name"] == "field2"
        1 * fieldSection.setFields(_)
    }

    def "test removeFields with new layout"() {
        given: "新布局表格组件"
        def newTableComponentExt = Mock(NewTableComponentExt) {
            removeByFieldNames(_) >> null
        }
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> true
            get("new_layout") >> [:]
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)
        
        GroovyMock(TableComponentExt, global: true)
        tableComponentExt.enableNewLayout() >> true
        tableComponentExt.getNewTableComponentExt() >> Optional.of(newTableComponentExt)

        when: "移除字段"
        tableComponentExt.removeFields(Sets.newHashSet("field1"))

        then: "应该调用新布局组件的移除方法"
        1 * newTableComponentExt.removeByFieldNames(Sets.newHashSet("field1"))
    }

    def "test removeFieldByTypes without new layout"() {
        given: "包含不同类型字段的表格组件"
        def fieldMaps = [
                ["name": "text_field", "render_type": "text"],
                ["name": "file_field", "render_type": "file_attachment"],
                ["name": "number_field", "render_type": "number"]
        ]
        def describe = Mock(ObjectDescribeExt) {
            getFieldDescribeSilently("text_field") >> Optional.of(Mock(IFieldDescribe) { getType() >> "text" })
            getFieldDescribeSilently("file_field") >> Optional.of(Mock(IFieldDescribe) { getType() >> "file_attachment" })
            getFieldDescribeSilently("number_field") >> Optional.of(Mock(IFieldDescribe) { getType() >> "number" })
        }
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> false
            get(ITableComponent.INCLUDE_FIELDS) >> fieldMaps
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "按类型移除字段"
        tableComponentExt.removeFieldByTypes(describe, "file_attachment", "signature")

        then: "应该移除指定类型的字段"
        fieldMaps.size() == 2
        fieldMaps.find { it["name"] == "file_field" } == null
    }

    def "test removeFieldByTypes with new layout"() {
        given: "新布局表格组件"
        def newTableComponentExt = Mock(NewTableComponentExt) {
            removeByTypes(_, _) >> null
        }
        def describe = Mock(ObjectDescribeExt)
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> true
            get("new_layout") >> [:]
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)
        
        GroovyMock(TableComponentExt, global: true)
        tableComponentExt.enableNewLayout() >> true
        tableComponentExt.getNewTableComponentExt() >> Optional.of(newTableComponentExt)

        when: "按类型移除字段"
        tableComponentExt.removeFieldByTypes(describe, "file_attachment")

        then: "应该调用新布局组件的移除方法"
        1 * newTableComponentExt.removeByTypes(describe, Sets.newHashSet("file_attachment"))
    }

    def "test adjustFieldRenderType without new layout"() {
        given: "表格组件和对象描述"
        def fieldMaps = [
                ["name": "field1", "render_type": "text", "label_name": "Field 1"],
                ["name": "field2", "render_type": "number"]
        ]
        def describe = Mock(IObjectDescribe) {
            getApiName() >> "test_object"
        }
        def objectDescribeExt = Mock(ObjectDescribeExt) {
            getFieldDescribeSilently("field1") >> Optional.of(Mock(IFieldDescribe) { getType() >> "text" })
            getFieldDescribeSilently("field2") >> Optional.of(Mock(IFieldDescribe) { getType() >> "currency" })
        }
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> false
            get(ITableComponent.INCLUDE_FIELDS) >> fieldMaps
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)
        
        GroovyMock(ObjectDescribeExt, global: true)
        ObjectDescribeExt.of(describe) >> objectDescribeExt
        
        GroovyMock(LayoutExt, global: true)
        LayoutExt.getRenderType("test_object", "field1", "text") >> "text"
        LayoutExt.getRenderType("test_object", "field2", "currency") >> "currency"

        when: "调整字段渲染类型"
        tableComponentExt.adjustFieldRenderType(describe)

        then: "应该更新字段的渲染类型并移除label_name"
        fieldMaps[0]["render_type"] == "text"
        fieldMaps[1]["render_type"] == "currency"
        !fieldMaps[0].containsKey("label_name")
    }

    def "test adjustFieldRenderType with new layout"() {
        given: "新布局表格组件"
        def newTableComponentExt = Mock(NewTableComponentExt) {
            adjustFieldRenderType(_) >> null
        }
        def describe = Mock(IObjectDescribe)
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> true
            get("new_layout") >> [:]
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)
        
        GroovyMock(TableComponentExt, global: true)
        tableComponentExt.enableNewLayout() >> true
        tableComponentExt.getNewTableComponentExt() >> Optional.of(newTableComponentExt)

        when: "调整字段渲染类型"
        tableComponentExt.adjustFieldRenderType(describe)

        then: "应该调用新布局组件的调整方法"
        1 * newTableComponentExt.adjustFieldRenderType(describe)
    }

    def "test correctLabel without new layout"() {
        given: "表格组件和对象描述"
        def fieldMaps = [
                ["name": "field1"],
                ["name": "field2"]
        ]
        def describeExt = Mock(ObjectDescribeExt) {
            getFieldDescribeSilently("field1") >> Optional.of(Mock(IFieldDescribe) { getLabel() >> "Updated Field 1" })
            getFieldDescribeSilently("field2") >> Optional.of(Mock(IFieldDescribe) { getLabel() >> "Updated Field 2" })
        }
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> false
            get(ITableComponent.INCLUDE_FIELDS) >> fieldMaps
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "修正标签"
        tableComponentExt.correctLabel(describeExt)

        then: "应该更新字段标签"
        fieldMaps[0]["label_name"] == "Updated Field 1"
        fieldMaps[1]["label_name"] == "Updated Field 2"
    }

    def "test correctLabel with new layout"() {
        given: "新布局表格组件"
        def newTableComponentExt = Mock(NewTableComponentExt) {
            correctLabel(_) >> null
        }
        def describeExt = Mock(ObjectDescribeExt)
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> true
            get("new_layout") >> [:]
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)
        
        GroovyMock(TableComponentExt, global: true)
        tableComponentExt.enableNewLayout() >> true
        tableComponentExt.getNewTableComponentExt() >> Optional.of(newTableComponentExt)

        when: "修正标签"
        tableComponentExt.correctLabel(describeExt)

        then: "应该调用新布局组件的修正方法"
        1 * newTableComponentExt.correctLabel(describeExt)
    }

    def "test setDefaultFieldListIfEmpty without new layout"() {
        given: "没有字段的表格组件"
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> false
            get(ITableComponent.INCLUDE_FIELDS) >> []
            setIncludeFields(_) >> null
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "设置默认字段列表"
        tableComponentExt.setDefaultFieldListIfEmpty()

        then: "应该设置默认的name字段"
        1 * tableComponent.setIncludeFields(_)
    }

    def "test setDefaultFieldListIfEmpty with existing fields"() {
        given: "已有字段的表格组件"
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> false
            get(ITableComponent.INCLUDE_FIELDS) >> [["name": "existing_field"]]
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "设置默认字段列表"
        tableComponentExt.setDefaultFieldListIfEmpty()

        then: "不应该修改现有字段"
        0 * tableComponent.setIncludeFields(_)
    }

    def "test setDefaultFieldListIfEmpty with new layout"() {
        given: "新布局表格组件"
        def newTableComponentExt = Mock(NewTableComponentExt) {
            setDefaultFieldListIfEmpty() >> null
        }
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> true
            get("new_layout") >> [:]
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)
        
        GroovyMock(TableComponentExt, global: true)
        tableComponentExt.enableNewLayout() >> true
        tableComponentExt.getNewTableComponentExt() >> Optional.of(newTableComponentExt)

        when: "设置默认字段列表"
        tableComponentExt.setDefaultFieldListIfEmpty()

        then: "应该调用新布局组件的设置方法"
        1 * newTableComponentExt.setDefaultFieldListIfEmpty()
    }

    def "test getFieldsInFirstSection without new layout"() {
        given: "有字段节的表格组件"
        def formFields = [
                Mock(IFormField) { getFieldName() >> "field1" },
                Mock(IFormField) { getFieldName() >> "field2" }
        ]
        def fieldSection = Mock(IFieldSection) {
            getFields() >> formFields
        }
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> false
            getFieldSections() >> [fieldSection]
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "获取第一节的字段"
        def fields = tableComponentExt.getFieldsInFirstSection()

        then: "应该返回第一节的字段"
        fields == formFields
    }

    def "test getFieldsInFirstSection with new layout"() {
        given: "新布局表格组件"
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> true
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "获取第一节的字段"
        def fields = tableComponentExt.getFieldsInFirstSection()

        then: "应该返回空列表"
        fields.isEmpty()
    }

    def "test getFieldsInFirstSection with empty sections"() {
        given: "没有字段节的表格组件"
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> false
            getFieldSections() >> []
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "获取第一节的字段"
        def fields = tableComponentExt.getFieldsInFirstSection()

        then: "应该返回空列表"
        fields.isEmpty()
    }

    def "test getFieldListAnyway with new layout"() {
        given: "新布局表格组件"
        def newTableComponentExt = Mock(NewTableComponentExt) {
            getFieldList() >> ["field1", "field2"]
        }
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> true
            get("new_layout") >> [:]
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)
        
        GroovyMock(TableComponentExt, global: true)
        tableComponentExt.enableNewLayout() >> true
        tableComponentExt.getNewTableComponentExt() >> Optional.of(newTableComponentExt)

        when: "获取字段列表"
        def fieldList = tableComponentExt.getFieldListAnyway()

        then: "应该返回新布局的字段列表"
        fieldList == ["field1", "field2"]
    }

    def "test getFieldListAnyway without new layout with include fields"() {
        given: "有include_fields的表格组件"
        def fieldMaps = [
                ["name": "field1"],
                ["name": "field2"]
        ]
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> false
            get(ITableComponent.INCLUDE_FIELDS) >> fieldMaps
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "获取字段列表"
        def fieldList = tableComponentExt.getFieldListAnyway()

        then: "应该返回include_fields的字段名"
        fieldList == ["field1", "field2"]
    }

    def "test getFieldListAnyway without new layout with field sections"() {
        given: "有字段节的表格组件"
        def formFields = [
                Mock(IFormField) { getFieldName() >> "field1" },
                Mock(IFormField) { getFieldName() >> "field2" },
                Mock(IFormField) { getFieldName() >> "" } // 空字段名应该被过滤
        ]
        def fieldSection = Mock(IFieldSection) {
            getFields() >> formFields
        }
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> false
            get(ITableComponent.INCLUDE_FIELDS) >> []
            getFieldSections() >> [fieldSection]
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "获取字段列表"
        def fieldList = tableComponentExt.getFieldListAnyway()

        then: "应该返回字段节的有效字段名"
        fieldList == ["field1", "field2"]
    }

    def "test initRenderShowImageAndTag"() {
        given: "表格组件"
        def tableComponent = Mock(ITableComponent) {
            set(_, _) >> null
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)
        
        // Mock containsKey behavior
        GroovyMock(TableComponentExt, global: true)
        tableComponentExt.enableNewLayout() >> false

        when: "初始化渲染显示图片和标签"
        tableComponentExt.initRenderShowImageAndTag()

        then: "应该设置默认值"
        noExceptionThrown()
    }

    def "test isShowTag without new layout"() {
        given: "表格组件"
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> false
            get("is_show_tag", Boolean.class) >> true
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "检查是否显示标签"
        def isShowTag = tableComponentExt.isShowTag()

        then: "应该返回正确值"
        isShowTag == true
    }

    def "test isShowTag with new layout"() {
        given: "新布局表格组件"
        def newTableComponentExt = Mock(NewTableComponentExt) {
            isShowTag() >> false
        }
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> true
            get("new_layout") >> [:]
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)
        
        GroovyMock(TableComponentExt, global: true)
        tableComponentExt.enableNewLayout() >> true
        tableComponentExt.getNewTableComponentExt() >> Optional.of(newTableComponentExt)

        when: "检查是否显示标签"
        def isShowTag = tableComponentExt.isShowTag()

        then: "应该调用新布局组件的方法"
        isShowTag == false
    }

    def "test getShowImage without new layout"() {
        given: "表格组件"
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> false
            get("show_image", String.class) >> "avatar"
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "获取显示图片"
        def showImage = tableComponentExt.getShowImage()

        then: "应该返回正确值"
        showImage == "avatar"
    }

    def "test getShowImage with new layout"() {
        given: "新布局表格组件"
        def newTableComponentExt = Mock(NewTableComponentExt) {
            getShowImage() >> "profile"
        }
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> true
            get("new_layout") >> [:]
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)
        
        GroovyMock(TableComponentExt, global: true)
        tableComponentExt.enableNewLayout() >> true
        tableComponentExt.getNewTableComponentExt() >> Optional.of(newTableComponentExt)

        when: "获取显示图片"
        def showImage = tableComponentExt.getShowImage()

        then: "应该调用新布局组件的方法"
        showImage == "profile"
    }

    def "test removeShowImage without new layout"() {
        given: "表格组件"
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> false
            set(_, _) >> null
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "移除显示图片"
        tableComponentExt.removeShowImage()

        then: "应该设置为null"
        1 * tableComponent.set("show_image", null)
    }

    def "test removeShowImage with new layout"() {
        given: "新布局表格组件"
        def newTableComponentExt = Mock(NewTableComponentExt) {
            removeShowImage() >> null
        }
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> true
            get("new_layout") >> [:]
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)
        
        GroovyMock(TableComponentExt, global: true)
        tableComponentExt.enableNewLayout() >> true
        tableComponentExt.getNewTableComponentExt() >> Optional.of(newTableComponentExt)

        when: "移除显示图片"
        tableComponentExt.removeShowImage()

        then: "应该调用新布局组件的方法"
        1 * newTableComponentExt.removeShowImage()
    }

    def "test enableNewLayout"() {
        given: "表格组件"
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> true
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "检查是否启用新布局"
        def enabled = tableComponentExt.enableNewLayout()

        then: "应该返回正确值"
        enabled == true
    }

    def "test getNewTableComponentExt"() {
        given: "有新布局的表格组件"
        def newLayoutMap = ["field_list": ["field1", "field2"]]
        def tableComponent = Mock(ITableComponent) {
            get("new_layout") >> newLayoutMap
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "获取新表格组件扩展"
        def newComponentExt = tableComponentExt.getNewTableComponentExt()

        then: "应该返回NewTableComponentExt实例"
        newComponentExt.isPresent()
        newComponentExt.get() instanceof NewTableComponentExt
    }

    def "test getNewTableComponentExt with null new layout"() {
        given: "没有新布局的表格组件"
        def tableComponent = Mock(ITableComponent) {
            get("new_layout") >> null
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "获取新表格组件扩展"
        def newComponentExt = tableComponentExt.getNewTableComponentExt()

        then: "应该返回空Optional"
        !newComponentExt.isPresent()
    }

    def "test replaceByNewTableComponent without new layout"() {
        given: "非新布局表格组件"
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> false
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "替换为新表格组件"
        tableComponentExt.replaceByNewTableComponent()

        then: "应该不做任何操作"
        noExceptionThrown()
    }

    def "test replaceByNewTableComponent with new layout"() {
        given: "新布局表格组件"
        def tableColumns = [Mock(ITableColumn)]
        def newTableComponentExt = Mock(NewTableComponentExt) {
            getShowImage() >> "avatar"
            isShowTag() >> true
            convertToTableColumn() >> tableColumns
        }
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> true
            get("new_layout") >> [:]
            getFieldSections() >> []
            set(_, _) >> null
            setIncludeFields(_) >> null
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)
        
        GroovyMock(TableComponentExt, global: true)
        tableComponentExt.enableNewLayout() >> true
        tableComponentExt.getNewTableComponentExt() >> Optional.of(newTableComponentExt)

        when: "替换为新表格组件"
        tableComponentExt.replaceByNewTableComponent()

        then: "应该设置新布局的属性"
        1 * tableComponent.set("show_image", "avatar")
        1 * tableComponent.set("is_show_tag", true)
        1 * tableComponent.setIncludeFields(tableColumns)
    }

    def "test replaceByNewTableComponent with field sections"() {
        given: "有字段节的新布局表格组件"
        def tableColumns = [Mock(ITableColumn)]
        def formFields = [Mock(IFormField)]
        def fieldSection = Mock(IFieldSection) {
            setFields(_) >> null
        }
        def newTableComponentExt = Mock(NewTableComponentExt) {
            getShowImage() >> "avatar"
            isShowTag() >> true
            convertToTableColumn() >> tableColumns
        }
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> true
            get("new_layout") >> [:]
            getFieldSections() >> [fieldSection]
            set(_, _) >> null
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)
        
        GroovyMock(TableComponentExt, global: true)
        tableComponentExt.enableNewLayout() >> true
        tableComponentExt.getNewTableComponentExt() >> Optional.of(newTableComponentExt)

        when: "替换为新表格组件"
        tableComponentExt.replaceByNewTableComponent()

        then: "应该设置字段节的字段"
        1 * fieldSection.setFields(_)
    }

    def "test robust method"() {
        given: "需要健壮性处理的表格组件"
        def includeField1 = Mock(ITableColumn) {
            get(IComponent.NAME, String.class) >> null
            getName() >> "field1"
            set(_, _) >> null
        }
        def includeField2 = Mock(ITableColumn) {
            get(IComponent.NAME, String.class) >> "field2"
        }
        def formField1 = Mock(IFormField) {
            get(IComponent.NAME, String.class) >> null
            getFieldName() >> "form_field1"
            set(_, _) >> null
        }
        def formField2 = Mock(IFormField) {
            get(IComponent.NAME, String.class) >> "form_field2"
        }
        def fieldSection = Mock(IFieldSection) {
            getFields() >> [formField1, formField2]
        }
        
        def tableComponent = Mock(ITableComponent) {
            get(IComponent.NAME, String.class) >> "wrong_name"
            setName(_) >> null
            getIncludeFields() >> [includeField1, includeField2]
            getFieldSections() >> [fieldSection]
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "进行健壮性处理"
        tableComponentExt.robust()

        then: "应该修正组件名称和缺失的name属性"
        1 * tableComponent.setName(ComponentExt.TABLE_COMPONENT)
        1 * includeField1.set(IComponent.NAME, "field1")
        1 * formField1.set(IComponent.NAME, "form_field1")
    }

    def "test robust method with correct name"() {
        given: "名称正确的表格组件"
        def tableComponent = Mock(ITableComponent) {
            get(IComponent.NAME, String.class) >> ComponentExt.TABLE_COMPONENT
            getIncludeFields() >> []
            getFieldSections() >> []
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "进行健壮性处理"
        tableComponentExt.robust()

        then: "不应该修改名称"
        0 * tableComponent.setName(_)
    }

    def "test robust method with empty sections and fields"() {
        given: "空字段和节的表格组件"
        def emptyFieldSection = Mock(IFieldSection) {
            getFields() >> []
        }
        def tableComponent = Mock(ITableComponent) {
            get(IComponent.NAME, String.class) >> ComponentExt.TABLE_COMPONENT
            getIncludeFields() >> []
            getFieldSections() >> [emptyFieldSection]
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "进行健壮性处理"
        tableComponentExt.robust()

        then: "应该正常完成"
        noExceptionThrown()
    }

    def "test constants"() {
        expect: "常量值正确"
        TableComponentExt.IS_SHOW_TAG == "is_show_tag"
        TableComponentExt.SHOW_IMAGE == "show_image"
        TableComponentExt.AGENT_TYPE == "agent_type"
        TableComponentExt.FIELD_SECTION == "field_section"
        TableComponentExt.IS_NEW_LAYOUT == "is_new_layout"
        TableComponentExt.NEW_LAYOUT == "new_layout"
        TableComponentExt.COMPONENT_TYPE_TABLE == "table"
        TableComponentExt.UNSUPPORTED_QUOTE_FIELD_TYPE_IN_LIST.contains(IFieldType.FILE_ATTACHMENT)
        TableComponentExt.UNSUPPORTED_QUOTE_FIELD_TYPE_IN_LIST.contains(IFieldType.SIGNATURE)
    }

    def "test removeIfEmpty"() {
        given: "表格组件"
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> false
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "移除空字段"
        tableComponentExt.removeIfEmpty(Sets.newHashSet("field1", "field2"))

        then: "应该正常完成"
        noExceptionThrown()
    }

    def "test complex scenario"() {
        given: "复杂的表格组件场景"
        def fieldMaps = [
                ["name": "text_field", "render_type": "text"],
                ["name": "file_field", "render_type": "file_attachment"]
        ]
        def describe = Mock(ObjectDescribeExt) {
            getFieldDescribeSilently("text_field") >> Optional.of(Mock(IFieldDescribe) { 
                getType() >> "text"
                getLabel() >> "Text Field"
            })
            getFieldDescribeSilently("file_field") >> Optional.of(Mock(IFieldDescribe) { 
                getType() >> "file_attachment"
                getLabel() >> "File Field"
            })
        }
        def tableComponent = Mock(ITableComponent) {
            get("is_new_layout", Boolean.class) >> false
            get(ITableComponent.INCLUDE_FIELDS) >> fieldMaps
            get("is_show_tag", Boolean.class) >> false
            get("show_image", String.class) >> null
            getFieldSections() >> []
        }
        def tableComponentExt = TableComponentExt.of(tableComponent)

        when: "执行复杂操作序列"
        tableComponentExt.removeFieldByTypes(describe, "file_attachment")
        tableComponentExt.correctLabel(describe)
        tableComponentExt.initRenderShowImageAndTag()

        then: "应该正确处理所有操作"
        fieldMaps.size() == 1
        fieldMaps[0]["name"] == "text_field"
        fieldMaps[0]["label_name"] == "Text Field"
        tableComponentExt.isShowTag() == false
        tableComponentExt.getShowImage() == null
    }
} 