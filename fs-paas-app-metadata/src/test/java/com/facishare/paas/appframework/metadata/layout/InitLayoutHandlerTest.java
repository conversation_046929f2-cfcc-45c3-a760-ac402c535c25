package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.appframework.metadata.FormTable;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.TabsComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InitLayoutHandlerTest {

    @Mock
    private ILayout mockLayout;
    @Mock
    private LayoutExt mockLayoutExt;

    private InitLayoutHandler initLayoutHandler;
    private List<IComponent> components;

    @BeforeEach
    void setUp() {
        initLayoutHandler = new InitLayoutHandler();
        components = Lists.newArrayList();

        // 创建测试组件
        FormComponent formComponent = new FormComponent();
        formComponent.setName("form_component");
        formComponent.setHeader("Form Component");
        components.add(formComponent);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试复制布局到详情页类型的正常场景
     */
    @Test
    @DisplayName("正常场景 - 复制布局到详情页")
    void testCopyLayout_ToDetailLayout() {
        // Given
        Layout sourceLayout = new Layout();
        sourceLayout.setLayoutType(LayoutTypes.EDIT);
        sourceLayout.setName("test_layout");
        sourceLayout.setDisplayName("Test Layout");

        try (MockedStatic<LayoutExt> layoutExtMock = mockStatic(LayoutExt.class)) {
            LayoutExt mockSourceLayoutExt = mock(LayoutExt.class);
            LayoutExt mockNewLayoutExt = mock(LayoutExt.class);
            Layout newLayout = new Layout();

            layoutExtMock.when(() -> LayoutExt.of(sourceLayout)).thenReturn(mockSourceLayoutExt);
            when(mockSourceLayoutExt.copy()).thenReturn(newLayout);
            when(mockSourceLayoutExt.getName()).thenReturn("test_layout");

            layoutExtMock.when(() -> LayoutExt.of(newLayout)).thenReturn(mockNewLayoutExt);
            when(mockNewLayoutExt.toMap()).thenReturn(new java.util.HashMap<>());

            // When
            ILayout result = initLayoutHandler.copyLayout(sourceLayout, LayoutTypes.DETAIL);

            // Then
            assertNotNull(result);
            assertEquals(LayoutTypes.DETAIL, result.getLayoutType());
            assertEquals("test_layout", result.getName());
            assertEquals("Test Layout", result.getDisplayName());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试复制布局到编辑页类型的正常场景
     */
    @Test
    @DisplayName("正常场景 - 复制布局到编辑页")
    void testCopyLayout_ToEditLayout() {
        // Given
        Layout sourceLayout = new Layout();
        sourceLayout.setLayoutType(LayoutTypes.DETAIL);
        sourceLayout.setName("test_layout");
        sourceLayout.setDisplayName("Test Layout");

        try (MockedStatic<LayoutExt> layoutExtMock = mockStatic(LayoutExt.class)) {
            LayoutExt mockSourceLayoutExt = mock(LayoutExt.class);
            LayoutExt mockNewLayoutExt = mock(LayoutExt.class);
            Layout newLayout = new Layout();

            layoutExtMock.when(() -> LayoutExt.of(sourceLayout)).thenReturn(mockSourceLayoutExt);
            when(mockSourceLayoutExt.copy()).thenReturn(newLayout);
            when(mockSourceLayoutExt.getName()).thenReturn("test_layout");
            when(mockSourceLayoutExt.getDefaultEditLayoutName()).thenReturn("test_layout_edit");

            layoutExtMock.when(() -> LayoutExt.of(newLayout)).thenReturn(mockNewLayoutExt);
            when(mockNewLayoutExt.toMap()).thenReturn(new java.util.HashMap<>());

            // When
            ILayout result = initLayoutHandler.copyLayout(sourceLayout, LayoutTypes.EDIT);

            // Then
            assertNotNull(result);
            assertEquals(LayoutTypes.EDIT, result.getLayoutType());
            assertEquals("test_layout_edit", result.getName());
            assertTrue(result.getDisplayName().contains("Test Layout"));
            verify(mockNewLayoutExt).retainComponentsWithField();
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建页签组件的正常场景
     */
    @Test
    @DisplayName("正常场景 - 构建页签组件")
    void testBuildTabComponent_NormalCase() {
        // Given
        List<IComponent> copyComponents = Lists.newArrayList(components);

        try (MockedStatic<LayoutComponents> layoutComponentsMock = mockStatic(LayoutComponents.class);
             MockedStatic<ComponentExt> componentExtMock = mockStatic(ComponentExt.class)) {

            TabsComponent mockTabsComponent = mock(TabsComponent.class);
            layoutComponentsMock.when(LayoutComponents::buildTabsComponent).thenReturn(mockTabsComponent);

            ComponentExt mockComponentExt = mock(ComponentExt.class);
            componentExtMock.when(() -> ComponentExt.of(any())).thenReturn(mockComponentExt);
            when(mockComponentExt.isFormType()).thenReturn(true);
            when(mockComponentExt.getNameI18nKey()).thenReturn("test.i18n.key");

            // When
            IComponent result = initLayoutHandler.buildTabComponent(components, false, copyComponents);

            // Then
            assertNotNull(result);
            verify(mockTabsComponent).setComponents(any(List.class));
            verify(mockTabsComponent).setTabs(any(List.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建详情页页签组件的场景
     */
    @Test
    @DisplayName("正常场景 - 构建详情页页签组件")
    void testBuildTabComponent_ToDetailPage() {
        // Given
        List<IComponent> copyComponents = Lists.newArrayList(components);

        try (MockedStatic<LayoutComponents> layoutComponentsMock = mockStatic(LayoutComponents.class);
             MockedStatic<ComponentExt> componentExtMock = mockStatic(ComponentExt.class)) {

            TabsComponent mockTabsComponent = mock(TabsComponent.class);
            layoutComponentsMock.when(LayoutComponents::buildTabsComponent).thenReturn(mockTabsComponent);

            ComponentExt mockComponentExt = mock(ComponentExt.class);
            componentExtMock.when(() -> ComponentExt.of(any())).thenReturn(mockComponentExt);
            when(mockComponentExt.isFormType()).thenReturn(true);
            when(mockComponentExt.isRelatedList()).thenReturn(false);
            when(mockComponentExt.isMasterDetailComponent()).thenReturn(false);
            when(mockComponentExt.isOperationLog()).thenReturn(false);
            when(mockComponentExt.getNameI18nKey()).thenReturn("test.i18n.key");

            // When
            IComponent result = initLayoutHandler.buildTabComponent(components, true, copyComponents);

            // Then
            assertNotNull(result);
            verify(mockTabsComponent).setComponents(any(List.class));
            verify(mockTabsComponent).setTabs(any(List.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试排除表格组件中文本组件的功能
     */
    @Test
    @DisplayName("正常场景 - 排除表格组件中的文本组件")
    void testGetExcludeFormTableTextComponents_WithFormTable() {
        // Given
        FormComponent formTableComponent = new FormComponent();
        formTableComponent.setName("form_table");
        formTableComponent.setType(ComponentExt.TYPE_FORM_TABLE);
        components.add(formTableComponent);

        FormComponent textComponent = new FormComponent();
        textComponent.setName("text_component");
        textComponent.setType(ComponentExt.TYPE_TEXT_COMPONENT);
        components.add(textComponent);

        try (MockedStatic<ComponentExt> componentExtMock = mockStatic(ComponentExt.class);
             MockedStatic<FormTable> formTableMock = mockStatic(FormTable.class)) {

            ComponentExt mockComponentExt = mock(ComponentExt.class);
            componentExtMock.when(() -> ComponentExt.of(any())).thenReturn(mockComponentExt);
            when(mockComponentExt.isFormTable()).thenReturn(true, false, false);

            FormTable mockFormTable = mock(FormTable.class);
            formTableMock.when(() -> FormTable.of(any())).thenReturn(mockFormTable);
            when(mockFormTable.getTextComponentApiNameList()).thenReturn(Lists.newArrayList("text_component"));

            // When
            List<IComponent> result = initLayoutHandler.getExcludeFormTableTextComponents(components);

            // Then
            assertNotNull(result);
            // 验证文本组件被排除
            assertTrue(result.stream().noneMatch(c -> "text_component".equals(c.getName()) && 
                ComponentExt.TYPE_TEXT_COMPONENT.equals(c.getType())));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试没有表格组件时的排除功能
     */
    @Test
    @DisplayName("正常场景 - 没有表格组件时返回原列表")
    void testGetExcludeFormTableTextComponents_NoFormTable() {
        // Given
        try (MockedStatic<ComponentExt> componentExtMock = mockStatic(ComponentExt.class)) {
            ComponentExt mockComponentExt = mock(ComponentExt.class);
            componentExtMock.when(() -> ComponentExt.of(any())).thenReturn(mockComponentExt);
            when(mockComponentExt.isFormTable()).thenReturn(false);

            // When
            List<IComponent> result = initLayoutHandler.getExcludeFormTableTextComponents(components);

            // Then
            assertNotNull(result);
            assertEquals(components.size(), result.size());
            assertEquals(components, result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空组件列表的处理
     */
    @Test
    @DisplayName("边界场景 - 空组件列表")
    void testGetExcludeFormTableTextComponents_EmptyList() {
        // Given
        List<IComponent> emptyComponents = Lists.newArrayList();

        // When
        List<IComponent> result = initLayoutHandler.getExcludeFormTableTextComponents(emptyComponents);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建页签组件时的空组件列表
     */
    @Test
    @DisplayName("边界场景 - 构建页签组件时传入空列表")
    void testBuildTabComponent_EmptyComponents() {
        // Given
        List<IComponent> emptyComponents = Lists.newArrayList();
        List<IComponent> emptyCopyComponents = Lists.newArrayList();

        try (MockedStatic<LayoutComponents> layoutComponentsMock = mockStatic(LayoutComponents.class)) {
            TabsComponent mockTabsComponent = mock(TabsComponent.class);
            layoutComponentsMock.when(LayoutComponents::buildTabsComponent).thenReturn(mockTabsComponent);

            // When
            IComponent result = initLayoutHandler.buildTabComponent(emptyComponents, false, emptyCopyComponents);

            // Then
            assertNotNull(result);
            verify(mockTabsComponent).setComponents(any(List.class));
            verify(mockTabsComponent).setTabs(any(List.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试复制布局时的null参数处理
     */
    @Test
    @DisplayName("异常场景 - 复制布局时传入null参数")
    void testCopyLayout_NullParameters() {
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            initLayoutHandler.copyLayout(null, LayoutTypes.DETAIL);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试基本功能验证
     */
    @Test
    @DisplayName("正常场景 - 基本功能验证")
    void testInitLayoutHandler_BasicFunctionality() {
        // Then
        assertNotNull(initLayoutHandler);
        assertDoesNotThrow(() -> {
            initLayoutHandler.toString();
        });
        assertDoesNotThrow(() -> {
            initLayoutHandler.hashCode();
        });
        assertDoesNotThrow(() -> {
            initLayoutHandler.equals(initLayoutHandler);
        });
    }
}
