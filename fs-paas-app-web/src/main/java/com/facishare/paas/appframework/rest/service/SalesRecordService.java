package com.facishare.paas.appframework.rest.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.coordination.CrmService;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.privilege.DataAuthServiceProxy;
import com.facishare.paas.appframework.privilege.DataPrivilegeProxy;
import com.facishare.paas.appframework.privilege.dto.AuthContext;
import com.facishare.paas.appframework.privilege.dto.ObjectsPermission;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.appframework.privilege.util.AppIdUtil;
import com.facishare.paas.appframework.rest.dto.data.CheckPermission;
import com.facishare.paas.appframework.rest.dto.data.FeedDataByJson;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.rest.InnerHeaders.TENANT_ID;

@Slf4j
@Service
public class SalesRecordService {

    private static final int NO_PERMISSION = 0;
    private static final int READONLY = 1;
    private static final int READ_WRITE = 2;
    @Autowired
    DataAuthServiceProxy dataAuthServiceProxy;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private CrmService crmService;
    @Autowired
    private DataPrivilegeProxy proxy;

    public FeedDataByJson.Result feedData(FeedDataByJson.Arg arg, RequestContext requestContext) {
        FeedDataByJson.Result result = new FeedDataByJson.Result();
        List<FeedDataByJson.FeedData> feedDataList = arg.getDataList();

        if (CollectionUtils.isEmpty(feedDataList)) {
            return result;
        }


        //按类型分组，分别查询
        Map<String, List<String>> map = Maps.newHashMap();
        feedDataList.forEach(x -> {
            if (map.containsKey(x.getDataType())) {
                map.get(x.getDataType()).add(x.getDataId());
            } else {
                map.put(x.getDataType(), Lists.newArrayList(x.getDataId()));
            }
        });

        IActionContext context = ActionContextExt.of(requestContext.getUser(),
                requestContext).getContext();

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        Set<Map.Entry<String, List<String>>> entries = map.entrySet();
        for (Map.Entry<String, List<String>> entry : entries) {
            parallelTask.submit(() -> {
                List<String> list = entry.getValue().stream().filter(
                        a -> !Strings.isNullOrEmpty(a)).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(list)) {
                    return;
                }
                String apiName = entry.getKey();
                IObjectDescribe describe = serviceFacade.findObject(requestContext.getTenantId(), apiName);
                if (null == describe) {
                    return;
                }
                String displayName = describe.getDisplayName();
                List<INameCache> nameCaches = serviceFacade.findRecordName(context, apiName, list);
                for (INameCache data : nameCaches) {
                    FeedDataByJson.FeedData one = findFeedData(feedDataList, data.getId());
                    if (one != null) {
                        one.setData(data.getName());
                        one.setDisplayName(displayName);
                    }
                }

                //处理数据权限
                Map<String, String> permissionResult = checkDataDataPrivilege(requestContext, list, apiName);
                for (FeedDataByJson.FeedData data : feedDataList) {
                    String permission = permissionResult.get(data.getDataId());
                    if (Objects.equals(permission, String.valueOf(NO_PERMISSION))) {
                        data.setData(getMaskName(data.getData()));
                    }
                }

            });

        }

        try {
            parallelTask.await(5000, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            log.error("time out when execute query data in feedData", e);
        }

        result.setFeedDataList(feedDataList);
        return result;
    }

    private String getMaskName(String name) {
        if (com.google.common.base.Strings.isNullOrEmpty(name)) {
            return name;
        }

        int length = name.length();
        if (length > 4) {
            return String.format("%s********%s", name.substring(0, 2), name.substring(name.length() - 2));
        } else {
            return "****";
        }
    }

    private Map<String, String> checkDataDataPrivilege(RequestContext requestContext, List<String> idList,
                                                       String apiName) {
        if (CollectionUtils.isEmpty(idList)) {
            return Maps.newConcurrentMap();
        }
        Boolean isAdmin = serviceFacade.isAdmin(requestContext.getUser());
        if (isAdmin) {
            return idList.stream()
                    .collect(Collectors.toMap(
                            x -> x, value -> String.valueOf(READ_WRITE)));
        }

        boolean isSubCascadeConfig = crmService.getIsSubCascadeConfig(requestContext.getTenantId());
        ObjectsPermission.Arg arg = new ObjectsPermission.Arg();
        arg.setContext(buildAuthContext(requestContext.getUser()));
        arg.setObjects(idList);
        arg.setEntityId(apiName);
        arg.setCascadeDept(isSubCascadeConfig);
        arg.setCascadeSubordinates(isSubCascadeConfig);
        arg.setRoleType(DefObjConstants.DATA_PRIVILEGE_ROLE_TYPE.OWNER.getValue());

        Map<String, String> header = Maps.newHashMap();
        header.put(TENANT_ID, requestContext.getTenantId());
        ObjectsPermission.Result permission = dataAuthServiceProxy.objectsPermission(header, arg);

        if (!permission.isSuccess()) {
            log.error("objectsPermission failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(permission));
            throw new PermissionError(I18NExt.text(I18NKey.FAILED_TO_GET_DATA_PERMISSION));
        }
        Map<String, String> objectsPermission = permission.getResult();
        Map<String, String> result = idList.stream().collect(Collectors.toMap(
                x -> x, value -> Objects.isNull(objectsPermission.get(value)) ?
                        String.valueOf(NO_PERMISSION) : objectsPermission.get(value)));
        return result;
    }

    private AuthContext buildAuthContext(User user) {
        return AuthContext.builder()
                .appId(AppIdUtil.getAppId(user))
                .tenantId(user.getTenantId())
                .userId(user.getUserId())
                .outerTenantId(user.isOutUser() ? user.getOutTenantId() : null)
                .build();
    }

    /**
     * 从feedDataList中找出dataId为 id的元素
     *
     * @param feedDataList feedData元素列表
     * @param id           dataId
     * @return FeedData
     */
    private FeedDataByJson.FeedData findFeedData(List<FeedDataByJson.FeedData> feedDataList, String id) {
        for (FeedDataByJson.FeedData data : feedDataList) {
            if (id.equals(data.getDataId())) {
                return data;
            }
        }
        return null;
    }

    public CheckPermission.Result checkPermission(CheckPermission.Arg arg, RequestContext requestContext) {
        CheckPermission.Result result = new CheckPermission.Result();
        result.setPermissionResult(Integer.valueOf(Permissions.NO_PERMISSION.getValue()));
        List<CheckPermission.CheckPermissionData> checkList = arg.getPermissionDataList();

        if (CollectionUtils.isEmpty(checkList)) {
            return result;
        }

        IActionContext context = ActionContextExt.of(requestContext.getUser(),
                requestContext).getContext();
        List<String> idList = Lists.newArrayList();
        checkList.forEach(x -> {
            if (x.getDataId() != null)
                idList.add(x.getDataId());
        });

        List<IObjectData> dataList = serviceFacade.findObjectDataByIds(context, idList,
                checkList.get(0).getDataType());

        if (CollectionUtils.isEmpty(dataList)) {
            return result;
        }

        // 查询数据权限，是每个dataId对应一个权限，还是一个User对应一个权限
        checkList.forEach(x -> {
            IObjectDescribe describe = serviceFacade.findObject(requestContext.getTenantId(), x.getDataType());
            Map<String, Permissions> privMap = serviceFacade.checkDataPrivilege(requestContext.getUser(),
                    idList, describe);
            privMap.forEach((k, v) -> {
                if (v == Permissions.READ_ONLY || v == Permissions.READ_WRITE) {
                    result.setPermissionResult(Integer.valueOf(Permissions.READ_ONLY.getValue()));
                    return;
                }
            });
        });

        return result;
    }
}
