package com.facishare.crm.privilege.rest.proxy.privilege.api.model;


import com.facishare.crm.privilege.rest.proxy.privilege.model.BaseArgDataPrivilege;
import com.facishare.crm.privilege.rest.proxy.privilege.model.BaseResultDataPrivilege;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by yusb on 2017/5/11.
 */
public class UserResponsibleDeptUsersModel {
  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class Arg extends BaseArgDataPrivilege {
    private Boolean deptCascade;
  }

  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class Result extends BaseResultDataPrivilege {
    private List<String> result;
  }
}
