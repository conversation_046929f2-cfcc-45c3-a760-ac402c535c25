package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.appframework.metadata.ButtonUsePageType;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ListComponentInfoTest {

  private ListComponentInfo listComponentInfo;
  
  @Mock
  private IButton button1;
  
  @Mock
  private IButton button2;
  
  @Mock
  private IButton button3;

  @BeforeEach
  void setUp() {
    listComponentInfo = new ListComponentInfo();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ListComponentInfo的无参构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试无参构造函数")
  void testDefaultConstructor() {
    ListComponentInfo info = new ListComponentInfo();
    assertNotNull(info, "无参构造函数创建的对象不应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试ListComponentInfo的Map构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = Maps.newHashMap();
    map.put(IListComponentInfo.RENDER_TYPE, "list_normal");
    map.put(IListComponentInfo.PAGE_TYPE, "list");
    map.put(IListComponentInfo.ORDER, Lists.newArrayList("btn1", "btn2"));
    map.put(IListComponentInfo.HIDDEN, Lists.newArrayList("btn3"));
    
    ListComponentInfo info = new ListComponentInfo(map);
    assertNotNull(info, "Map构造函数创建的对象不应为空");
    assertEquals("list_normal", info.getRenderType(), "应正确设置渲染类型");
    assertEquals("list", info.getPageType(), "应正确设置页面类型");
    assertEquals(2, info.getOrder().size(), "应正确设置顺序列表");
    assertEquals(1, info.getHidden().size(), "应正确设置隐藏列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试listNormal静态工厂方法
   */
  @Test
  @DisplayName("正常场景 - 测试listNormal静态工厂方法")
  void testListNormal() {
    IListComponentInfo info = ListComponentInfo.listNormal();
    
    assertNotNull(info, "静态工厂方法创建的对象不应为空");
    assertEquals(ListComponentInfo.ButtonRenderType.LIST_NORMAL.getType(), info.getRenderType(), 
                 "应设置为list_normal渲染类型");
    assertEquals(ButtonUsePageType.DataList.getId(), info.getPageType(), "应设置默认页面类型");
    assertNotNull(info.getOrder(), "顺序列表不应为空");
    assertNotNull(info.getHidden(), "隐藏列表不应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试listNormal带参数静态工厂方法
   */
  @Test
  @DisplayName("正常场景 - 测试listNormal带参数静态工厂方法")
  void testListNormalWithPageType() {
    IListComponentInfo info = ListComponentInfo.listNormal(ButtonUsePageType.Edit);
    
    assertNotNull(info, "静态工厂方法创建的对象不应为空");
    assertEquals(ListComponentInfo.ButtonRenderType.LIST_NORMAL.getType(), info.getRenderType(), 
                 "应设置为list_normal渲染类型");
    assertEquals(ButtonUsePageType.Edit.getId(), info.getPageType(), "应设置指定的页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试listBatch静态工厂方法
   */
  @Test
  @DisplayName("正常场景 - 测试listBatch静态工厂方法")
  void testListBatch() {
    IListComponentInfo info = ListComponentInfo.listBatch();
    
    assertNotNull(info, "静态工厂方法创建的对象不应为空");
    assertEquals(ListComponentInfo.ButtonRenderType.LIST_BATCH.getType(), info.getRenderType(), 
                 "应设置为list_batch渲染类型");
    assertEquals(ButtonUsePageType.DataList.getId(), info.getPageType(), "应设置默认页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试listBatch带参数静态工厂方法
   */
  @Test
  @DisplayName("正常场景 - 测试listBatch带参数静态工厂方法")
  void testListBatchWithPageType() {
    IListComponentInfo info = ListComponentInfo.listBatch(ButtonUsePageType.Create);
    
    assertNotNull(info, "静态工厂方法创建的对象不应为空");
    assertEquals(ListComponentInfo.ButtonRenderType.LIST_BATCH.getType(), info.getRenderType(), 
                 "应设置为list_batch渲染类型");
    assertEquals(ButtonUsePageType.Create.getId(), info.getPageType(), "应设置指定的页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试listSingle静态工厂方法
   */
  @Test
  @DisplayName("正常场景 - 测试listSingle静态工厂方法")
  void testListSingle() {
    IListComponentInfo info = ListComponentInfo.listSingle();
    
    assertNotNull(info, "静态工厂方法创建的对象不应为空");
    assertEquals(ListComponentInfo.ButtonRenderType.LIST_SINGLE.getType(), info.getRenderType(), 
                 "应设置为list_single渲染类型");
    assertEquals(ButtonUsePageType.DataList.getId(), info.getPageType(), "应设置默认页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试listSingle带参数静态工厂方法
   */
  @Test
  @DisplayName("正常场景 - 测试listSingle带参数静态工厂方法")
  void testListSingleWithPageType() {
    IListComponentInfo info = ListComponentInfo.listSingle(ButtonUsePageType.ListNormal);
    
    assertNotNull(info, "静态工厂方法创建的对象不应为空");
    assertEquals(ListComponentInfo.ButtonRenderType.LIST_SINGLE.getType(), info.getRenderType(), 
                 "应设置为list_single渲染类型");
    assertEquals(ButtonUsePageType.ListNormal.getId(), info.getPageType(), "应设置指定的页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试normal静态工厂方法
   */
  @Test
  @DisplayName("正常场景 - 测试normal静态工厂方法")
  void testNormal() {
    IListComponentInfo info = ListComponentInfo.normal(ButtonUsePageType.Edit);
    
    assertNotNull(info, "静态工厂方法创建的对象不应为空");
    assertEquals(ListComponentInfo.ButtonRenderType.NORMAL.getType(), info.getRenderType(), 
                 "应设置为normal渲染类型");
    assertEquals(ButtonUsePageType.Edit.getId(), info.getPageType(), "应设置指定的页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试defaultSceneInfo静态工厂方法
   */
  @Test
  @DisplayName("正常场景 - 测试defaultSceneInfo静态工厂方法")
  void testDefaultSceneInfo() {
    IListComponentInfo info = ListComponentInfo.defaultSceneInfo();
    
    assertNotNull(info, "静态工厂方法创建的对象不应为空");
    assertEquals(IListComponentInfo.RENDER_TYPE_DROP_DOWN, info.getRenderType(), 
                 "应设置为下拉框渲染类型");
    assertEquals(ButtonUsePageType.DataList.getId(), info.getPageType(), "应设置默认页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试buildComponentInfo静态工厂方法
   */
  @Test
  @DisplayName("正常场景 - 测试buildComponentInfo静态工厂方法")
  void testBuildComponentInfo() {
    IListComponentInfo info = ListComponentInfo.buildComponentInfo("custom_type", ButtonUsePageType.Create);
    
    assertNotNull(info, "静态工厂方法创建的对象不应为空");
    assertEquals("custom_type", info.getRenderType(), "应设置指定的渲染类型");
    assertEquals(ButtonUsePageType.Create.getId(), info.getPageType(), "应设置指定的页面类型");
    assertNotNull(info.getOrder(), "顺序列表不应为空");
    assertNotNull(info.getHidden(), "隐藏列表不应为空");
    assertTrue(info.getOrder().isEmpty(), "顺序列表应为空");
    assertTrue(info.getHidden().isEmpty(), "隐藏列表应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试buildComponentInfo静态工厂方法 - null页面类型
   */
  @Test
  @DisplayName("边界场景 - 测试buildComponentInfo静态工厂方法处理null页面类型")
  void testBuildComponentInfo_NullPageType() {
    IListComponentInfo info = ListComponentInfo.buildComponentInfo("test_type", null);
    
    assertNotNull(info, "静态工厂方法创建的对象不应为空");
    assertEquals("test_type", info.getRenderType(), "应设置指定的渲染类型");
    assertNull(info.getPageType(), "页面类型应为null");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getOrder和setOrder方法
   */
  @Test
  @DisplayName("正常场景 - 测试Order属性")
  void testOrder() {
    // 初始状态应为空列表
    List<String> initialOrder = listComponentInfo.getOrder();
    assertNotNull(initialOrder, "初始顺序列表不应为空");
    assertTrue(initialOrder.isEmpty(), "初始顺序列表应为空");
    
    // 设置顺序列表
    List<String> orderList = Lists.newArrayList("button1", "button2", "button3");
    listComponentInfo.setOrder(orderList);
    
    List<String> retrievedOrder = listComponentInfo.getOrder();
    assertNotNull(retrievedOrder, "获取的顺序列表不应为空");
    assertEquals(3, retrievedOrder.size(), "顺序列表大小应为3");
    assertEquals("button1", retrievedOrder.get(0), "第一个按钮应为button1");
    assertEquals("button2", retrievedOrder.get(1), "第二个按钮应为button2");
    assertEquals("button3", retrievedOrder.get(2), "第三个按钮应为button3");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getHidden和setHidden方法
   */
  @Test
  @DisplayName("正常场景 - 测试Hidden属性")
  void testHidden() {
    // 初始状态应为空列表
    List<String> initialHidden = listComponentInfo.getHidden();
    assertNotNull(initialHidden, "初始隐藏列表不应为空");
    assertTrue(initialHidden.isEmpty(), "初始隐藏列表应为空");
    
    // 设置隐藏列表
    List<String> hiddenList = Lists.newArrayList("hidden1", "hidden2");
    listComponentInfo.setHidden(hiddenList);
    
    List<String> retrievedHidden = listComponentInfo.getHidden();
    assertNotNull(retrievedHidden, "获取的隐藏列表不应为空");
    assertEquals(2, retrievedHidden.size(), "隐藏列表大小应为2");
    assertTrue(retrievedHidden.contains("hidden1"), "应包含hidden1");
    assertTrue(retrievedHidden.contains("hidden2"), "应包含hidden2");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getRenderType方法
   */
  @Test
  @DisplayName("正常场景 - 测试getRenderType方法")
  void testGetRenderType() {
    // 初始状态应为null
    assertNull(listComponentInfo.getRenderType(), "初始渲染类型应为null");
    
    // 通过Map构造函数设置渲染类型
    Map<String, Object> map = Maps.newHashMap();
    map.put(IListComponentInfo.RENDER_TYPE, "list_normal");
    ListComponentInfo info = new ListComponentInfo(map);
    assertEquals("list_normal", info.getRenderType(), "应正确获取渲染类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getPageType和setPageType方法
   */
  @Test
  @DisplayName("正常场景 - 测试PageType属性")
  void testPageType() {
    // 初始状态应为null
    assertNull(listComponentInfo.getPageType(), "初始页面类型应为null");
    
    // 设置页面类型
    listComponentInfo.setPageType("edit");
    assertEquals("edit", listComponentInfo.getPageType(), "应正确设置页面类型");
    
    // 设置为null
    listComponentInfo.setPageType(null);
    assertNull(listComponentInfo.getPageType(), "应支持设置为null");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getExposedButton和setExposedButton方法
   */
  @Test
  @DisplayName("正常场景 - 测试ExposedButton属性")
  void testExposedButton() {
    // 初始状态应为null
    assertNull(listComponentInfo.getExposedButton(), "初始暴露按钮数应为null");
    
    // 设置暴露按钮数
    listComponentInfo.setExposedButton(3);
    assertEquals(3, listComponentInfo.getExposedButton(), "应正确设置暴露按钮数");
    
    // 设置为null
    listComponentInfo.setExposedButton(null);
    assertNull(listComponentInfo.getExposedButton(), "应支持设置为null");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getExposedButton方法 - BigDecimal转换
   */
  @Test
  @DisplayName("正常场景 - 测试getExposedButton方法BigDecimal转换")
  void testGetExposedButton_BigDecimalConversion() {
    // 通过Map设置BigDecimal值
    Map<String, Object> map = Maps.newHashMap();
    map.put(IListComponentInfo.EXPOSED_BUTTON, new BigDecimal("5"));
    ListComponentInfo info = new ListComponentInfo(map);

    assertEquals(5, info.getExposedButton(), "应正确转换BigDecimal为Integer");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试useForAddPage方法
   */
  @Test
  @DisplayName("正常场景 - 测试useForAddPage方法")
  void testUseForAddPage() {
    // 设置为Create页面类型
    listComponentInfo.setPageType(ButtonUsePageType.Create.getId());
    assertTrue(listComponentInfo.useForAddPage(), "Create页面类型应返回true");

    // 设置为其他页面类型
    listComponentInfo.setPageType(ButtonUsePageType.Edit.getId());
    assertFalse(listComponentInfo.useForAddPage(), "非Create页面类型应返回false");

    // 设置为null
    listComponentInfo.setPageType(null);
    assertFalse(listComponentInfo.useForAddPage(), "null页面类型应返回false");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试useForEditPage方法
   */
  @Test
  @DisplayName("正常场景 - 测试useForEditPage方法")
  void testUseForEditPage() {
    // 设置为Edit页面类型
    listComponentInfo.setPageType(ButtonUsePageType.Edit.getId());
    assertTrue(listComponentInfo.useForEditPage(), "Edit页面类型应返回true");

    // 设置为其他页面类型
    listComponentInfo.setPageType(ButtonUsePageType.Create.getId());
    assertFalse(listComponentInfo.useForEditPage(), "非Edit页面类型应返回false");

    // 设置为null
    listComponentInfo.setPageType(null);
    assertFalse(listComponentInfo.useForEditPage(), "null页面类型应返回false");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试renderInListNormal方法
   */
  @Test
  @DisplayName("正常场景 - 测试renderInListNormal方法")
  void testRenderInListNormal() {
    // 通过Map设置渲染类型
    Map<String, Object> map = Maps.newHashMap();
    map.put(IListComponentInfo.RENDER_TYPE, ListComponentInfo.ButtonRenderType.LIST_NORMAL.getType());
    ListComponentInfo info = new ListComponentInfo(map);

    assertTrue(info.renderInListNormal(), "list_normal渲染类型应返回true");

    // 设置为其他渲染类型
    map.put(IListComponentInfo.RENDER_TYPE, ListComponentInfo.ButtonRenderType.LIST_BATCH.getType());
    ListComponentInfo info2 = new ListComponentInfo(map);
    assertFalse(info2.renderInListNormal(), "非list_normal渲染类型应返回false");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试renderInListBatch方法
   */
  @Test
  @DisplayName("正常场景 - 测试renderInListBatch方法")
  void testRenderInListBatch() {
    // 通过Map设置渲染类型
    Map<String, Object> map = Maps.newHashMap();
    map.put(IListComponentInfo.RENDER_TYPE, ListComponentInfo.ButtonRenderType.LIST_BATCH.getType());
    ListComponentInfo info = new ListComponentInfo(map);

    assertTrue(info.renderInListBatch(), "list_batch渲染类型应返回true");

    // 设置为其他渲染类型
    map.put(IListComponentInfo.RENDER_TYPE, ListComponentInfo.ButtonRenderType.LIST_SINGLE.getType());
    ListComponentInfo info2 = new ListComponentInfo(map);
    assertFalse(info2.renderInListBatch(), "非list_batch渲染类型应返回false");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试renderInListSingle方法
   */
  @Test
  @DisplayName("正常场景 - 测试renderInListSingle方法")
  void testRenderInListSingle() {
    // 通过Map设置渲染类型
    Map<String, Object> map = Maps.newHashMap();
    map.put(IListComponentInfo.RENDER_TYPE, ListComponentInfo.ButtonRenderType.LIST_SINGLE.getType());
    ListComponentInfo info = new ListComponentInfo(map);

    assertTrue(info.renderInListSingle(), "list_single渲染类型应返回true");

    // 设置为其他渲染类型
    map.put(IListComponentInfo.RENDER_TYPE, ListComponentInfo.ButtonRenderType.NORMAL.getType());
    ListComponentInfo info2 = new ListComponentInfo(map);
    assertFalse(info2.renderInListSingle(), "非list_single渲染类型应返回false");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeButtons方法 - 正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试removeButtons方法")
  void testRemoveButtons_NormalScenario() {
    // 设置初始按钮
    List<String> order = Lists.newArrayList("btn1", "btn2", "btn3", "btn4");
    List<String> hidden = Lists.newArrayList("btn5", "btn6");
    listComponentInfo.setOrder(order);
    listComponentInfo.setHidden(hidden);

    // 移除部分按钮
    List<String> toRemove = Lists.newArrayList("btn2", "btn5");
    listComponentInfo.removeButtons(toRemove);

    // 验证移除结果
    List<String> remainingOrder = listComponentInfo.getOrder();
    List<String> remainingHidden = listComponentInfo.getHidden();

    assertEquals(3, remainingOrder.size(), "顺序列表应剩余3个按钮");
    assertFalse(remainingOrder.contains("btn2"), "btn2应被移除");
    assertTrue(remainingOrder.contains("btn1"), "btn1应保留");
    assertTrue(remainingOrder.contains("btn3"), "btn3应保留");
    assertTrue(remainingOrder.contains("btn4"), "btn4应保留");

    assertEquals(1, remainingHidden.size(), "隐藏列表应剩余1个按钮");
    assertFalse(remainingHidden.contains("btn5"), "btn5应被移除");
    assertTrue(remainingHidden.contains("btn6"), "btn6应保留");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeButtons方法 - 空列表场景
   */
  @Test
  @DisplayName("边界场景 - 测试removeButtons方法处理空列表")
  void testRemoveButtons_EmptyList() {
    // 设置初始按钮
    List<String> order = Lists.newArrayList("btn1", "btn2");
    listComponentInfo.setOrder(order);

    // 移除空列表
    listComponentInfo.removeButtons(Lists.newArrayList());

    // 验证没有变化
    assertEquals(2, listComponentInfo.getOrder().size(), "空列表移除不应影响原有按钮");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeButtons方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试removeButtons方法处理null值")
  void testRemoveButtons_NullValue() {
    // 设置初始按钮
    List<String> order = Lists.newArrayList("btn1", "btn2");
    listComponentInfo.setOrder(order);

    // 移除null值
    listComponentInfo.removeButtons(null);

    // 验证没有变化
    assertEquals(2, listComponentInfo.getOrder().size(), "null值移除不应影响原有按钮");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试mergeWithButtons方法 - 正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试mergeWithButtons方法")
  void testMergeWithButtons_NormalScenario() {
    // 配置Mock按钮
    when(button1.getName()).thenReturn("btn1");
    when(button2.getName()).thenReturn("btn2");
    when(button3.getName()).thenReturn("existing_btn"); // 包含一个已存在的按钮

    List<IButton> buttons = Lists.newArrayList(button1, button2, button3);

    // 设置初始状态
    listComponentInfo.setOrder(Lists.newArrayList("existing_btn"));
    listComponentInfo.setHidden(Lists.newArrayList());

    // 合并按钮
    listComponentInfo.mergeWithButtons(buttons);

    // 验证合并结果 - 根据实际逻辑，existing_btn会被保留，新按钮会被添加
    List<String> order = listComponentInfo.getOrder();
    assertTrue(order.contains("existing_btn"), "应保留原有按钮");
    assertTrue(order.contains("btn1"), "应添加新按钮btn1");
    assertTrue(order.contains("btn2"), "应添加新按钮btn2");
    // existing_btn不会重复添加，因为已经在order中了
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试mergeWithButtons方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试mergeWithButtons方法处理null值")
  void testMergeWithButtons_NullValue() {
    // 设置初始状态
    List<String> originalOrder = Lists.newArrayList("btn1", "btn2");
    listComponentInfo.setOrder(originalOrder);

    // 合并null按钮列表
    listComponentInfo.mergeWithButtons(null);

    // 验证没有变化
    assertEquals(2, listComponentInfo.getOrder().size(), "null按钮列表不应影响原有按钮");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试mergeWithButtonNames方法 - 正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试mergeWithButtonNames方法")
  void testMergeWithButtonNames_NormalScenario() {
    // 设置初始状态
    listComponentInfo.setOrder(Lists.newArrayList("existing_btn"));
    listComponentInfo.setHidden(Lists.newArrayList("hidden_btn"));

    // 合并按钮名称
    List<String> newButtonNames = Lists.newArrayList("new_btn1", "new_btn2", "existing_btn");
    listComponentInfo.mergeWithButtonNames(newButtonNames);

    // 验证合并结果
    List<String> order = listComponentInfo.getOrder();
    assertTrue(order.contains("existing_btn"), "应保留原有按钮");
    assertTrue(order.contains("new_btn1"), "应添加新按钮new_btn1");
    assertTrue(order.contains("new_btn2"), "应添加新按钮new_btn2");

    // existing_btn不应重复添加
    long count = order.stream().filter(name -> "existing_btn".equals(name)).count();
    assertEquals(1, count, "existing_btn不应重复添加");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试mergeWithButtonNames方法 - Excel导入按钮优先级
   */
  @Test
  @DisplayName("正常场景 - 测试mergeWithButtonNames方法Excel导入按钮优先级")
  void testMergeWithButtonNames_ImportExcelPriority() {
    // 设置初始状态
    listComponentInfo.setOrder(Lists.newArrayList("btn1"));
    listComponentInfo.setHidden(Lists.newArrayList());

    // 包含Excel导入按钮的新按钮列表 - 使用正确的按钮API名称
    List<String> newButtonNames = Lists.newArrayList("btn1", "btn2", "Import_Excel_button_default", "btn3");
    listComponentInfo.mergeWithButtonNames(newButtonNames);

    // 验证Excel导入按钮在第一位
    List<String> order = listComponentInfo.getOrder();
    assertEquals("Import_Excel_button_default", order.get(0), "Excel导入按钮应在第一位");
    assertTrue(order.contains("btn1"), "应保留原有按钮");
    assertTrue(order.contains("btn2"), "应添加新按钮btn2");
    assertTrue(order.contains("btn3"), "应添加新按钮btn3");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试mergeWithButtonNames方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试mergeWithButtonNames方法处理null值")
  void testMergeWithButtonNames_NullValue() {
    // 设置初始状态
    List<String> originalOrder = Lists.newArrayList("btn1", "btn2");
    listComponentInfo.setOrder(originalOrder);

    // 合并null按钮名称列表
    listComponentInfo.mergeWithButtonNames(null);

    // 验证没有变化
    assertEquals(2, listComponentInfo.getOrder().size(), "null按钮名称列表不应影响原有按钮");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试filterAndSortButtons方法 - 正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试filterAndSortButtons方法")
  void testFilterAndSortButtons_NormalScenario() {
    // 配置Mock按钮
    when(button1.getName()).thenReturn("btn1");
    when(button2.getName()).thenReturn("btn2");
    when(button3.getName()).thenReturn("btn3");

    List<IButton> buttons = Lists.newArrayList(button3, button1, button2); // 乱序

    // 设置顺序和隐藏
    listComponentInfo.setOrder(Lists.newArrayList("btn1", "btn2", "btn3"));
    listComponentInfo.setHidden(Lists.newArrayList("btn2")); // btn2被隐藏

    // 过滤和排序
    List<IButton> result = listComponentInfo.filterAndSortButtons(buttons);

    // 验证结果
    assertEquals(2, result.size(), "应过滤掉隐藏的按钮");
    assertEquals("btn1", result.get(0).getName(), "第一个应为btn1");
    assertEquals("btn3", result.get(1).getName(), "第二个应为btn3");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试filterAndSortButtons方法 - 空列表场景
   */
  @Test
  @DisplayName("边界场景 - 测试filterAndSortButtons方法处理空列表")
  void testFilterAndSortButtons_EmptyList() {
    List<IButton> emptyButtons = Lists.newArrayList();

    List<IButton> result = listComponentInfo.filterAndSortButtons(emptyButtons);

    assertNotNull(result, "结果不应为空");
    assertTrue(result.isEmpty(), "空列表应返回空列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试filterAndSortButtons方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试filterAndSortButtons方法处理null值")
  void testFilterAndSortButtons_NullValue() {
    List<IButton> result = listComponentInfo.filterAndSortButtons(null);

    assertNull(result, "null输入应返回null");
  }
}
