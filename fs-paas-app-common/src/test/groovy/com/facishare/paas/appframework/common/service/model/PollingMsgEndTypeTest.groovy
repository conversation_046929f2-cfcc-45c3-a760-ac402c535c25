package com.facishare.paas.appframework.common.service.model

import com.facishare.polling.api.enums.PollingOSType
import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：PollingMsgEndType枚举类的单元测试
 */
class PollingMsgEndTypeTest extends Specification {

    def "test all enum values exist"() {
        expect: "验证所有枚举值都存在"
        PollingMsgEndType.EMPTY != null
        PollingMsgEndType.IOS != null
        PollingMsgEndType.ANDROID != null
        PollingMsgEndType.WEB != null
        PollingMsgEndType.H5 != null
        PollingMsgEndType.MOBILE != null
        PollingMsgEndType.ALL != null
    }

    def "test enum code values"() {
        expect: "验证枚举的code值正确"
        PollingMsgEndType.EMPTY.code == "empty"
        PollingMsgEndType.IOS.code == "iOS"
        PollingMsgEndType.ANDROID.code == "android"
        PollingMsgEndType.WEB.code == "web"
        PollingMsgEndType.H5.code == "h5"
        PollingMsgEndType.MOBILE.code == "mobile"
        PollingMsgEndType.ALL.code == "all"
    }

    @Unroll
    def "test toPollingOSType conversion for #endType"() {
        when: "调用toPollingOSType方法"
        def result = endType.toPollingOSType()

        then: "应该返回正确的PollingOSType"
        result == expectedOSType

        where:
        endType                  | expectedOSType
        PollingMsgEndType.EMPTY  | null
        PollingMsgEndType.IOS    | PollingOSType.IOS
        PollingMsgEndType.ANDROID| PollingOSType.ANDROID
        PollingMsgEndType.WEB    | PollingOSType.WEB
        PollingMsgEndType.H5     | PollingOSType.WEB
        PollingMsgEndType.MOBILE | PollingOSType.APP
        PollingMsgEndType.ALL    | PollingOSType.WEBANDAPP
    }

    def "test enum values method returns all values"() {
        when: "调用values方法"
        def values = PollingMsgEndType.values()

        then: "应该返回所有7个枚举值"
        values.length == 7
        values.contains(PollingMsgEndType.EMPTY)
        values.contains(PollingMsgEndType.IOS)
        values.contains(PollingMsgEndType.ANDROID)
        values.contains(PollingMsgEndType.WEB)
        values.contains(PollingMsgEndType.H5)
        values.contains(PollingMsgEndType.MOBILE)
        values.contains(PollingMsgEndType.ALL)
    }

    @Unroll
    def "test valueOf method returns correct enum for #enumName"() {
        when: "使用valueOf方法"
        def result = PollingMsgEndType.valueOf(enumName)

        then: "应该返回正确的枚举值"
        result == expectedEnum

        where:
        enumName  | expectedEnum
        "EMPTY"   | PollingMsgEndType.EMPTY
        "IOS"     | PollingMsgEndType.IOS
        "ANDROID" | PollingMsgEndType.ANDROID
        "WEB"     | PollingMsgEndType.WEB
        "H5"      | PollingMsgEndType.H5
        "MOBILE"  | PollingMsgEndType.MOBILE
        "ALL"     | PollingMsgEndType.ALL
    }

    def "test valueOf with invalid name throws exception"() {
        when: "使用无效的枚举名称"
        PollingMsgEndType.valueOf("INVALID_TYPE")

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test enum name method returns correct name"() {
        expect: "验证name方法返回正确的枚举名称"
        PollingMsgEndType.EMPTY.name() == "EMPTY"
        PollingMsgEndType.IOS.name() == "IOS"
        PollingMsgEndType.ANDROID.name() == "ANDROID"
        PollingMsgEndType.WEB.name() == "WEB"
        PollingMsgEndType.H5.name() == "H5"
        PollingMsgEndType.MOBILE.name() == "MOBILE"
        PollingMsgEndType.ALL.name() == "ALL"
    }

    def "test enum toString method"() {
        expect: "验证toString方法返回枚举名称"
        PollingMsgEndType.EMPTY.toString() == "EMPTY"
        PollingMsgEndType.IOS.toString() == "IOS"
        PollingMsgEndType.ANDROID.toString() == "ANDROID"
        PollingMsgEndType.WEB.toString() == "WEB"
        PollingMsgEndType.H5.toString() == "H5"
        PollingMsgEndType.MOBILE.toString() == "MOBILE"
        PollingMsgEndType.ALL.toString() == "ALL"
    }

    def "test web-related types both map to WEB"() {
        expect: "验证WEB和H5都映射到PollingOSType.WEB"
        PollingMsgEndType.WEB.toPollingOSType() == PollingOSType.WEB
        PollingMsgEndType.H5.toPollingOSType() == PollingOSType.WEB
    }

    def "test mobile and app mapping"() {
        expect: "验证MOBILE映射到PollingOSType.APP"
        PollingMsgEndType.MOBILE.toPollingOSType() == PollingOSType.APP
    }

    def "test all platforms mapping"() {
        expect: "验证ALL映射到PollingOSType.WEBANDAPP"
        PollingMsgEndType.ALL.toPollingOSType() == PollingOSType.WEBANDAPP
    }

    def "test empty type returns null"() {
        expect: "验证EMPTY类型返回null"
        PollingMsgEndType.EMPTY.toPollingOSType() == null
    }
}
