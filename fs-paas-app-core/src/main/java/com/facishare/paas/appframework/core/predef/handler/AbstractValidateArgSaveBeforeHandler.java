package com.facishare.paas.appframework.core.predef.handler;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.FieldManyMaxConfig;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.predef.facade.ApprovalFlowServiceFacade;
import com.facishare.paas.appframework.license.quota.TenantLicenseInfo;
import com.facishare.paas.appframework.license.util.ModulePara;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData.RelatedObjectData;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2023/1/10.
 */
public abstract class AbstractValidateArgSaveBeforeHandler<A extends SaveActionHandler.Arg, R extends SaveActionHandler.Result> implements SaveActionHandler<A, R> {

    @Autowired
    protected ServiceFacade serviceFacade;
    @Autowired
    private ApprovalFlowServiceFacade approvalFlowServiceFacade;
    @Autowired
    private InfraServiceFacade infraServiceFacade;

    protected abstract R buildResult(HandlerContext context, A arg);

    @Override
    public R handle(HandlerContext context, A arg) {
        //校验主对象参数
        validateMasterArg(context, arg);
        //根据字段类型校验数据格式
        validateDataType(context, arg);
        //校验数据状态
        validateDataStatus(context, arg);
        //校验币种信息
        checkCurrency(context, arg);
        //校验从对象参数
        validateDetailArg(context, arg);
        //校验相关对象参数
        validateRelatedArg(context, arg);
        //校验自由审批参数
        validateFreeApprovalDef(context, arg);
        // 校验变更规则的校验条件
        validateChangeRuleWithChangeData(context, arg);
        // 推拉单超额检查
        validateConvertRulesExcessCheck(context, arg);
        // 校验数据的合法性
        validateDataList(context, arg);
        //构建返回值
        return buildResult(context, arg);
    }

    protected void validateDataList(HandlerContext context, A arg) {
    }

    protected void validateConvertRulesExcessCheck(HandlerContext context, A arg) {
    }

    protected void validateChangeRuleWithChangeData(HandlerContext context, A arg) {

    }

    private void validateMasterArg(HandlerContext context, A arg) {
        IObjectDescribe objectDescribe = arg.getObjectDescribe();
        IObjectData objectData = arg.objectData();
        ObjectDataExt.of(objectData).validate(objectDescribe);
        //校验what_list字段
        validateWhatListCount(context, objectDescribe, objectData);
        //校验富文本字段
        validateRichTextValue(objectDescribe, objectData);
        // 校验归属部门类型是部门
        validateTypeForDataOwnDept(context, objectDescribe, objectData);
        //校验人员部门字段梳理
        checkDepartmentEmployeeManyMaxNum(objectDescribe, objectData);
        //查找关联多选，校验字段个数
        checkObjectReferenceManyMaxNum(objectDescribe, objectData);
        //校验查找关联字段的过滤条件
        if (!AppFrameworkConfig.isSkipValidateLookupGrayTenant(context.getTenantId())) {
            validateLookupData(context, arg, objectData, objectDescribe);
        }
        // 校验日期范围字段值
        checkDateRangeFieldValue(objectDescribe, objectData);
    }

    private void checkDateRangeFieldValue(IObjectDescribe objectDescribe, IObjectData objectData) {
        List<String> errorMsg = ObjectDataExt.of(objectData).validateDateRangeField(objectDescribe);
        if (CollectionUtils.notEmpty(errorMsg)) {
            throw new ValidateException(errorMsg.get(0));
        }
    }

    private void checkCurrency(HandlerContext context, A arg) {
        checkMasterAndDetailCurrency(context, arg);
        processRelatedObjectData(arg, (apiName, dataList) -> {
            IObjectDescribe objectDescribe = arg.getRelatedDescribe(apiName);
            if (Objects.isNull(objectDescribe) || !ObjectDescribeExt.of(objectDescribe).containsMultiCurrencyField()) {
                return;
            }
            dataList.stream()
                    .map(ObjectDataExt::of)
                    //校验币种是否存在
                    .peek(data -> data.checkCurrencyOption(objectDescribe))
                    //校验汇率有值，币种为空的场景
                    .forEach(ObjectDataExt::checkCurrency);
        });
    }

    private void checkMasterAndDetailCurrency(HandlerContext context, A arg) {
        IObjectDescribe objectDescribe = arg.getObjectDescribe();
        if (!ObjectDescribeExt.of(objectDescribe).containsMultiCurrencyField()) {
            return;
        }
        IObjectData objectData = arg.getObjectData().toObjectData();
        Map<String, List<IObjectData>> detailObjectData = arg.detailObjectData();
        //校验币种是否存在
        ObjectDataExt.of(objectData).checkCurrencyOption(objectDescribe);
        //校验汇率有值，币种为空的场景
        ObjectDataExt.of(objectData).checkCurrency();
        CollectionUtils.nullToEmpty(detailObjectData).forEach((apiName, dataList) ->
                dataList.forEach(data -> ObjectDataExt.of(data).checkCurrency()));
        //校验主和从的币种是否一致
        checkDetailCurrency(context, arg, detailObjectData, objectData);
    }

    protected void checkDetailCurrency(HandlerContext context, Arg arg, Map<String, List<IObjectData>> detailObjectData, IObjectData objectData) {
        CollectionUtils.nullToEmpty(detailObjectData).forEach((apiName, dataList) ->
                dataList.forEach(data -> ObjectDataExt.of(data).checkDetailCurrency(objectData)));
    }

    private void validateDetailArg(HandlerContext context, A arg) {
        if (CollectionUtils.empty(arg.getDetailDescribeMap())) {
            return;
        }
        Map<String, List<IObjectData>> detailObjectData = CollectionUtils.nullToEmpty(arg.detailObjectData());
        arg.getDetailDescribeMap().forEach((apiName, describe) -> {
            List<IObjectData> detailDataList = detailObjectData.get(apiName);
            validateDetail(context, arg, describe, detailDataList);
        });
    }

    protected void validateDetail(HandlerContext context, A arg, IObjectDescribe detailDescribe, List<IObjectData> detailDataList) {
        String detailApiName = detailDescribe.getApiName();
        //根据资源包校验从对象数据的条数
        if (!AppFrameworkConfig.isNotCheckCountDetailObject(detailApiName)) {
            TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo.builder()
                    .licenseService(serviceFacade)
                    .user(context.getUser())
                    .build()
                    .init(Sets.newHashSet(ModulePara.ModuleBiz.MASTER_DETAIL.getBizCode()));
            tenantLicenseInfo.checkDetailObjectCount(CollectionUtils.size(detailDataList), detailApiName);
        }

        //TODO 前端没有传apiName的只能从库里取数据，这个校验会影响性能，最好能把主从关系的IsRequiredWhenMasterCreate选项去掉。
        Optional<MasterDetailFieldDescribe> mdFieldDescribe = ObjectDescribeExt.of(detailDescribe).getMasterDetailFieldDescribe();
        MasterDetailFieldDescribe masterDetailField = mdFieldDescribe.orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));
        boolean isDetailDataInArg = CollectionUtils.nullToEmpty(arg.getDetailObjectData()).containsKey(detailApiName);
        if (masterDetailField.getIsRequiredWhenMasterCreate()) {
            if (!isDetailDataInArg) {
                detailDataList = serviceFacade.findDetailObjectDataWithPage(context.getUser(), arg.getObjectApiName(), arg.objectData().getId(),
                        detailDescribe, 1, 1, null).getData();
            }
            if (CollectionUtils.empty(detailDataList)) {
                throw new ValidateException(I18N.text(I18NKey.CREAT_MASTER_DATA_DETAIL_DATA_CAN_NOT_IS_NULL, detailDescribe.getDisplayName()));
            }
        }
        if (CollectionUtils.notEmpty(detailDataList) && isDetailDataInArg) {
            detailDataList.forEach(data -> {
                ObjectDataExt.of(data).validate(detailDescribe);
                checkDepartmentEmployeeManyMaxNum(detailDescribe, data);
                checkObjectReferenceManyMaxNum(detailDescribe, data);
                checkDateRangeFieldValue(detailDescribe, data);
            });
        }
    }

    private void checkObjectReferenceManyMaxNum(IObjectDescribe objectDescribe, IObjectData objectData) {
        List<IFieldDescribe> objectRefManyFields = ObjectDescribeExt.of(objectDescribe).getAllObjectRefManyFieldDescribes();
        if (CollectionUtils.empty(objectRefManyFields) || Objects.isNull(objectData)) {
            return;
        }
        for (IFieldDescribe objectRefManyField : objectRefManyFields) {
            List<String> fieldValues = ObjectDataExt.of(objectData).getManyField(objectRefManyField.getApiName());
            int maxNum = FieldManyMaxConfig.getObjectReferenceManyMaxLimit(objectDescribe.getTenantId(), objectDescribe.getApiName());
            if (fieldValues.size() > maxNum) {
                throw new ValidateException(I18N.text(I18NKey.OBJECT_REFERENCE_MANY_FIELD_BEYOND_MAX_LIMIT, maxNum, objectRefManyField.getLabel()));
            }
        }
    }

    private void checkDepartmentEmployeeManyMaxNum(IObjectDescribe objectDescribe, IObjectData objectData) {
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(objectDescribe);
        List<IFieldDescribe> manyFields = objectDescribeExt.getFieldDescribes().stream()
                .filter(field ->
                        field.getType().equals(IFieldType.EMPLOYEE_MANY) || field.getType().equals(IFieldType.DEPARTMENT_MANY)
                ).collect(Collectors.toList());
        if (CollectionUtils.empty(manyFields)) {
            return;
        }
        manyFields.stream()
                .filter(a -> Objects.nonNull(objectData.get(a.getApiName()))).forEach(fieldDescribe -> {
                    List<String> fieldValues = ObjectDataExt.of(objectData).getManyField(fieldDescribe.getApiName());
                    int maxNum = FieldDescribeExt.getMaxNum(objectDescribe.getTenantId(), objectDescribe.getApiName());
                    if (fieldValues.size() > maxNum) {
                        throw new ValidateException(I18NExt.getOrDefault(I18NKey.EMPLOYEE_DEPARTMENT_MANY_FIELD_BEYOND_MAX_LIMIT_V2, I18NKey.EMPLOYEE_DEPARTMENT_MANY_FIELD_BEYOND_MAX_LIMIT_V2, maxNum, fieldDescribe.getLabel()));
                    }
                });
    }

    private void validateWhatListCount(HandlerContext context, IObjectDescribe objectDescribe, IObjectData objectData) {
        List<WhatList> whatListFields = ObjectDescribeExt.of(objectDescribe).getWhatListFields();
        int maxCount = AppFrameworkConfig.getMaxWhatListDataCount(context.getTenantId());
        CollectionUtils.nullToEmpty(whatListFields).forEach(whatList -> {
            List<ObjectDataExt.WhatListData> list = ObjectDataExt.of(objectData).parseWhatListData(whatList);
            if (list.size() > maxCount) {
                throw new ValidateException(I18NExt.getOrDefault(I18NKey.WHAT_LIST_DATA_EXCEED_MAX_COUNT,
                        I18NKey.WHAT_LIST_DATA_EXCEED_MAX_COUNT, whatList.getLabel(), maxCount));
            }
        });
    }

    private void validateRichTextValue(IObjectDescribe objectDescribe, IObjectData objectData) {
        List<HtmlRichText> richTextFields = ObjectDescribeExt.of(objectDescribe).getRichTextFields();
        if (CollectionUtils.empty(richTextFields)) {
            return;
        }
        richTextFields.stream()
                .filter(a -> Objects.nonNull(objectData.get(a.getApiName())))
                .forEach(a -> RichTextExt.validateHtml(String.valueOf(objectData.get(a.getApiName()))));
    }

    private void validateDataType(HandlerContext context, A arg) {
        IObjectDescribe objectDescribe = arg.getObjectDescribe();
        IObjectData objectData = arg.objectData();
        //校验主对象
        serviceFacade.validateDataType(objectDescribe, Lists.newArrayList(objectData), context.getUser());
        Map<String, List<IObjectData>> detailObjectData = arg.detailObjectData();
        //校验从对象
        CollectionUtils.nullToEmpty(detailObjectData).entrySet()
                .stream()
                .filter(entry -> arg.containsDetailDescribe(entry.getKey()))
                .forEach(entry -> serviceFacade.validateDataType(arg.getDetailDescribe(entry.getKey()),
                        Lists.newArrayList(entry.getValue()), context.getUser()));

        // 校验相关对象
        processRelatedObjectData(arg, (apiName, dataList) -> {
            IObjectDescribe relatedDescribe = arg.getRelatedDescribe(apiName);
            serviceFacade.validateDataType(relatedDescribe, dataList, context.getUser());
        });
    }

    protected void validateTypeForDataOwnDept(HandlerContext context, IObjectDescribe objectDescribe, IObjectData objectData) {
        infraServiceFacade.validateTypeForDataOwnDept(context.getUser(), objectDescribe, objectData);
    }

    protected void validateDataStatus(HandlerContext context, A arg) {
        IObjectDescribe objectDescribe = arg.getObjectDescribe();
        if (!ObjectDescribeExt.of(objectDescribe).needCheckMasterStatus()) {
            return;
        }
        //主对象的审批例外人不校验锁定状态和生命状态
        if (arg.isAssigneesExceptional()) {
            return;
        }
        IObjectData objectData = arg.objectData();
        IObjectData realMasterData = approvalFlowServiceFacade.getRealMasterObjectData(context.getUser(), objectDescribe, objectData);
        if (realMasterData == null) {
            throw new ValidateException(I18N.text(I18NKey.MASTER_DATA_INVALID_OR_DELETED));
        }
        if (ObjectDataExt.of(realMasterData).isLock()) {
            throw new ValidateException(I18N.text(I18NKey.CANNOT_OPERATE_BECAUSE_OF_MASTER_LOCKED));
        }
        if (ObjectDataExt.of(realMasterData).isInChange()) {
            throw new ValidateException(I18N.text(I18NKey.CANNOT_OPERATE_BECAUSE_OF_MASTER_IN_CHANGE));
        }
    }

    private void validateRelatedArg(HandlerContext context, A arg) {
        validateRelatedListFormComponentCount(context, arg);
        processRelatedObjectData(arg, (apiName, dataList) -> {
            IObjectDescribe objectDescribe = arg.getRelatedDescribe(apiName);
            dataList.forEach(objectData -> {
                ObjectDataExt.of(objectData).validate(objectDescribe);
                // 校验部门,人员多选
                checkDepartmentEmployeeManyMaxNum(objectDescribe, objectData);
                checkObjectReferenceManyMaxNum(objectDescribe, objectData);
            });
        });
    }

    private void validateRelatedListFormComponentCount(HandlerContext context, A arg) {
        Map<String, List<RelatedObjectData>> relatedObjectData = arg.relatedObjectData();
        if (CollectionUtils.empty(relatedObjectData)) {
            return;
        }
        TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo.builder()
                .licenseService(serviceFacade)
                .user(context.getUser())
                .build()
                .init(Sets.newHashSet(ModulePara.ModuleBiz.MASTER_RELATED.getBizCode()));
        // 校验对象组件的个数
        int relatedObjectCount = relatedObjectData.values().stream().mapToInt(List::size).sum();
        tenantLicenseInfo.validateRelatedListFormComponentCount(relatedObjectCount, arg.getObjectApiName());

        // 提交数据,校验每个组件单独包含的数据条数
        relatedObjectData.forEach((relatedApiName, relatedObjectDataList) -> {
            relatedObjectDataList.forEach(it -> {
                String name = String.format("%s_%s", relatedApiName, it.getRelatedFieldName());
                tenantLicenseInfo.checkRelatedObjectCount(it.size(), name);
            });
        });
    }

    private void processRelatedObjectData(A arg, BiConsumer<String, List<IObjectData>> action) {
        Map<String, List<RelatedObjectData>> relatedObjectData = arg.relatedObjectData();
        if (CollectionUtils.empty(relatedObjectData)) {
            return;
        }
        relatedObjectData.forEach((apiName, relatedDataList) -> {
            List<IObjectData> dataList = relatedDataList.stream()
                    .map(SaveMasterAndDetailData.RelatedObjectData::getDataList)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            action.accept(apiName, dataList);
        });
    }

    private void validateFreeApprovalDef(HandlerContext context, A arg) {
        if (CollectionUtils.empty(arg.getInterfaceArg().getFreeApprovalDef())) {
            return;
        }
        serviceFacade.freeApprovalDefValidate(context.getUser(), arg.getInterfaceArg().getFreeApprovalDef());
    }

    protected void validateLookupData(HandlerContext context, A arg, IObjectData objectData, IObjectDescribe describe) {
        List<ObjectReferenceWrapper> fields = ObjectDescribeExt.of(describe).getActiveReferenceFieldDescribes();
        doValidateLookupData(context, objectData, describe, fields);
    }

    protected final void doValidateLookupData(HandlerContext context, IObjectData objectData, IObjectDescribe describe, List<ObjectReferenceWrapper> fields) {
        if (CollectionUtils.empty(fields)) {
            return;
        }

        for (ObjectReferenceWrapper fieldDescribe : fields) {
            serviceFacade.validateLookupData(context.getUser(), objectData, fieldDescribe);
        }
    }


}
