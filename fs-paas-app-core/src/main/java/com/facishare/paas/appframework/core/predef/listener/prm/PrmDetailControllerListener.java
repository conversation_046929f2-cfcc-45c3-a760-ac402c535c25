package com.facishare.paas.appframework.core.predef.listener.prm;

import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ControllerListener;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.prm.PrmLayoutRender;
import com.facishare.paas.appframework.prm.enums.PrmPageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class PrmDetailControllerListener implements ControllerListener<StandardDetailController.Arg, StandardDetailController.Result> {
    @Autowired
    private ServiceFacade serviceFacade;
    @Override
    public void before(ControllerContext context, StandardDetailController.Arg arg) {

    }

    @Override
    public void after(ControllerContext context, StandardDetailController.Arg arg, StandardDetailController.Result result) {
        if (result.getLayout() != null) {
            PrmLayoutRender.builder()
                    .pageType(PrmPageType.Detail)
                    .describe(result.getDescribe() != null ? result.getDescribe().toObjectDescribe() : serviceFacade.findObjectIncludeMultiField(context.getTenantId(), arg.getObjectDescribeApiName()))
                    .layoutExt(LayoutExt.of(result.getLayout()))
                    .build()
                    .render();
        }
    }
}
