package com.facishare.crm.privilege.service;

import com.facishare.crm.userdefobj.CrmActionEnum;
import com.facishare.rest.core.util.JsonUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by luxin on 2017/11/6.
 */
@Slf4j
@Service
public class NewPredefinedObjectService {
    private static final String API_NAME_KEY = "apiName";
    private static final String DISPLAY_NAME = "displayName";
    private static final String ICON_PATH = "iconPath";
    private static final String ICON_INDEX = "iconIndex";


    @Autowired
    private PrivilegeService commonPrivilegeService;

    private List<Map<String, Object>> predefinedObjectInfoList;
    private List<String> predefinedObjectApiNames;

    @PostConstruct
    private void init() {
        ConfigFactory.getConfig("new-predefined-object", config -> {
            String newPredefinedObjectsJsonStr = config.get("newPredefinedObjectsJsonStr");
            log.info("reload newPredefinedObjectsJsonStr:{}", newPredefinedObjectsJsonStr);
            predefinedObjectInfoList = JsonUtil.fromJson(newPredefinedObjectsJsonStr, List.class);
            predefinedObjectApiNames = predefinedObjectInfoList.stream().map(info -> ((String) info.get("apiName"))).collect(Collectors.toList());
        });
    }

    public List<Map<String, Object>> getPredefinedObjectInfoForMenu(String tenantId, String userId) {
        List<Map<String, Object>> predefinedObjectInfoForMenuList = Lists.newArrayList();
        Map<String, Map<String, Boolean>> PredefinedObjectPrivilegeInfo = commonPrivilegeService.
                getObjectsFunctionPrivilege(tenantId, userId, getPredefinedObjectRequestFuncInfo(predefinedObjectApiNames));

        predefinedObjectInfoList.forEach(predefinedObjectInfo -> {
            String apiName = predefinedObjectInfo.get(API_NAME_KEY) == null ? null : predefinedObjectInfo.get(API_NAME_KEY).toString();
            if (apiName == null) {
                return;
            }

            Map<String, Boolean> objectPrivilegeMap = PredefinedObjectPrivilegeInfo.get(apiName);

            if (objectPrivilegeMap == null) {
                return;
            } else if (objectPrivilegeMap.get(CrmActionEnum.VIEW_LIST.getActionCode())) {
                List<String> privilegeActions = Lists.newArrayList(CrmActionEnum.VIEW_LIST.getActionCode());

                if (objectPrivilegeMap.get(CrmActionEnum.CREATE.getActionCode())) {
                    privilegeActions.add(CrmActionEnum.CREATE.getActionCode());
                }
                predefinedObjectInfo.put("privilegeAction", privilegeActions);

                Integer index = predefinedObjectInfo.get(ICON_INDEX) == null ? null : Integer.valueOf(predefinedObjectInfo.get(ICON_INDEX).toString());
                if (index != null) {
                    predefinedObjectInfoForMenuList.add(index, predefinedObjectInfo);
                }
            }
        });
        return predefinedObjectInfoForMenuList;
    }

    private Map<String, List<String>> getPredefinedObjectRequestFuncInfo(List<String> predefinedObjectApiNames) {
        Map<String, List<String>> predefinedObjectRequestFuncInfo = Maps.newLinkedHashMap();
        predefinedObjectApiNames.forEach(apiName -> {
            predefinedObjectRequestFuncInfo.put(apiName, Lists.newArrayList(CrmActionEnum.VIEW_LIST.getActionCode(), CrmActionEnum.CREATE.getActionCode()));
        });
        return predefinedObjectRequestFuncInfo;
    }


}
