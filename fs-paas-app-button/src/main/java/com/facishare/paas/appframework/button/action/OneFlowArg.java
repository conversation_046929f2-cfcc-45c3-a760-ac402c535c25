package com.facishare.paas.appframework.button.action;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * OneFlow参数模型类
 */
public class OneFlowArg implements BaseActionArg {
  private String id;  // OneFlow参数ID
  private String type;  // 参数类型
  private String value;  // 参数值表达式
  private Object realValue;  // 实际参数值
  private String elementType;  // 元素类型（用于列表类型）
  private String elementObjectApiName;  // 元素对象API名称
  private String objectApiName;  // 对象API名称


  @Override
  @JSONField(name = "id")
  public String getName() {
    return id;
  }

  @Override
  @JSONField(name = "id")
  public void setName(String name) {
    this.id = name;
  }

  @Override
  public String getType() {
    return type;
  }

  @Override
  public void setType(String type) {
    this.type = type;
  }

  @Override
  public String getValue() {
    return value;
  }

  @Override
  public void setValue(String value) {
    this.value = value;
  }

  @Override
  public Object getRealValue() {
    return realValue;
  }

  @Override
  public void setRealValue(Object value) {
    this.realValue = value;
  }

  public String getElementType() {
    return elementType;
  }

  public void setElementType(String elementType) {
    this.elementType = elementType;
  }

  public String getElementObjectApiName() {
    return elementObjectApiName;
  }

  public void setElementObjectApiName(String elementObjectApiName) {
    this.elementObjectApiName = elementObjectApiName;
  }

  public String getObjectApiName() {
    return objectApiName;
  }

  public void setObjectApiName(String objectApiName) {
    this.objectApiName = objectApiName;
  }
}
