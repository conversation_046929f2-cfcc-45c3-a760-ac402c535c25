package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

public interface DeleteTemporaryRights {
    @Data
    @EqualsAndHashCode(callSuper = true)
    class Arg extends BasePrivilegeArg {
        private String scene;
        private String entityId;
        private Set<String> dataIds;
        private Set<String> owners;
        private String sourceId;

        // deleteTemporaryRightsData 接口支持的参数
        private String dataId;
        private String owner;
        private Integer permission;
        private String status;
        private String withdrawalWay;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {
        DeleteTemporaryRights.DeleteResult result;
    }

    @Data
    class DeleteResult {

    }
}
