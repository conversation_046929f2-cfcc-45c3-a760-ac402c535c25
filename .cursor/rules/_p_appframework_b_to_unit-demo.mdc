---
description: 单元测试示例
globs: 
alwaysApply: false
---
# 要求按照下面示例的规范生成单测
- 为每个没有使用@RunWith(PowerMockRunner)注解的单测类添加 setupSpec 方法。（已添加过的忽略）
```groovy
    import com.fxiaoke.i18n.client.I18nClient
    import com.fxiaoke.i18n.client.impl.I18nServiceImpl
    import org.powermock.reflect.Whitebox

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }
```
- 单测中依赖注入时示例
```groovy
    MetaDataComputeService metaDataComputeService
    ObjectDataProxy dataProxy = Mock(ObjectDataProxy)
    def setup() {
        metaDataComputeService = new MetaDataComputeServiceImpl(dataProxy: dataProxy)
    }
```
- 当需要验证UdobjGrayConfig.isAllow方法时，需要参考下面代码编写单测。注意isAllow使用通配符即可 *_ 。
```groovy
    import com.fxiaoke.release.FsGrayReleaseBiz
    import com.facishare.paas.appframework.core.util.UdobjGrayConfig

    def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
    Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
    fsGrayReleaseBiz.isAllow(*_) >> trueOrFalse
```
- 当遇到单测中使用 [AppFrameworkConfig.java](mdc:fs-paas-app-common/src/main/java/com/facishare/paas/appframework/common/util/AppFrameworkConfig.java) 中方法时，需要读取 @AppFrameworkConfig.java 文件，根据文件中该方法中使用的变量定义，使用 Whitebox.setInternalState 模拟。
- 示例：
```groovy
    import com.facishare.paas.appframework.common.util.AppFrameworkConfig

    Whitebox.setInternalState(AppFrameworkConfig, "maxQueryLimit", 1000)
    
    Whitebox.setInternalState(AppFrameworkConfig, "mobileSupportUiPaasButtonGrayEi", ['74255','78057'] as Set)
```

- 被代码中遇到 IObjectDescribe 时构造示例
```groovy
    // 必需导入：
    import com.facishare.paas.metadata.api.describe.IObjectDescribe
    import com.facishare.paas.metadata.impl.describe.ObjectDescribe
    import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory
    import com.facishare.paas.metadata.impl.describe.FieldDescribe

    // 构造示例：
    IObjectDescribe describe = new ObjectDescribe()
    describe.setApiName("AccountObj")
    IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance([
        "api_name": "employeeField", 
        "type": "employee"
    ])
    describe.setFieldDescribes([fieldDescribe])
```

- 被代码中遇到 IFieldDescribe 构造
- 字段类型请参考 com.facishare.paas.metadata.api.describe.IFieldType 中的定义
```groovy
// 必需导入：
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IFieldType
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory

// 构造示例：
IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance([
    "api_name": "employeeField", 
    "type": IFieldType.EMPLOYEE
])
```
- 被代码中遇到 User 构造
```groovy
    def tenantId = '74255'
    User user = User.systemUser(tenantId) 
```

- 被代码中遇到 IObjectData 构造
- ObjectData构造时key为IFieldDescribe构造中的api_name。
```groovy
// 构造示例一：
IObjectData objectData = new ObjectData(['_id':'2','name':'产品2', 'object_describe_api_name': 'master__c'])
// 构造示例二：
IObjectData objectData = new ObjectData()
objectData.fromJsonString('''{"name":"test-1000","owner":"1000","mc_currency":"CNY","object_describe_api_name":"master__c"}''')

```
- ServiceContext、RequestContext和RequestContextManager构造时参考下面代码
```groovy
        def user = User.systemUser('74255')
        def requestContext = RequestContext.builder()
                .tenantId('74255')
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .build()
        RequestContextManager.setContext(requestContext)
        def context = new ServiceContext(requestContext, "cacheValidate", "cacheValidate")
```
- 当被测试类中使用 [RequestUtil.java](mdc:fs-paas-app-api/src/main/java/com/facishare/paas/appframework/core/util/RequestUtil.java) 的相关方法时，一定读取其相关类 [RequestContext.java](mdc:fs-paas-app-api/src/main/java/com/facishare/paas/appframework/core/model/RequestContext.java) [RequestContextManager.java](mdc:fs-paas-app-api/src/main/java/com/facishare/paas/appframework/core/model/RequestContextManager.java) 的具体实现来为 [RequestContext.java](mdc:fs-paas-app-api/src/main/java/com/facishare/paas/appframework/core/model/RequestContext.java) 设置具体的值来达到 Mock 的效果