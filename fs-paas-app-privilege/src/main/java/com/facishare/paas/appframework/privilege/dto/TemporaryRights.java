package com.facishare.paas.appframework.privilege.dto;

import lombok.Builder;
import lombok.Data;

/**
 * create by <PERSON><PERSON><PERSON> on 2018/11/21
 */
public interface TemporaryRights {
    String UPDATE = "update";
    String ENABLE = "enable";
    String DELETE = "delete";

    @Data
    @Builder
    class UpdateTemporaryRights {
        private String tenantId;
        private String appId;
        private String userId;
        private String entityId;
        private RuleConfig beforeRule;
        private RuleConfig afterRule;
    }

    @Data
    @Builder
    class EnableTemporaryRights {
        private String tenantId;
        private String appId;
        private String userId;
        private String entityId;
        private boolean enable;
    }

    @Data
    @Builder
    class DeleteTemporaryRights {
        private String tenantId;
        private String appId;
        private String userId;
        private String entityId;
    }

    @Data
    @Builder
    class RuleConfig {
        private Integer level;
        private String withdrawalWay;
        private Integer validityTerm;
    }

}
