package com.facishare.paas.appframework.button.action;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.button.dto.SendCrmRemindPojo;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.expression.Expression;
import com.facishare.paas.appframework.privilege.util.AppIdUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by linqy on 2018/01/18.
 * 发crm提醒
 */
@Slf4j
@Component
public class SendCRMRemindAction implements ActionExecutor {

    @Autowired
    private CRMNotificationService proxy;
    @Autowired
    private GetEmployeeManager getEmployeeManager;

    @Autowired
    private ParseVarService parseVarService;

    @Override
    public ActionExecutorType getType() {
        return ActionExecutorType.SEND_CRM_REMIND;
    }

    @Override
    public ButtonExecutor.Result invoke(ButtonExecutor.Arg arg, ActionExecutorContext context) {
        IObjectData objectData = arg.getObjectData();
        Map<String, List<IObjectData>> details = arg.toDetails();
        return startCustomButton(objectData, details, context.getUser(), arg, context.getAction(), context.getButton(),
                context.getDescribe());
    }

    private ButtonExecutor.Result startCustomButton(IObjectData objectData,
                                                    Map<String, List<IObjectData>> details,
                                                    User user, ButtonExecutor.Arg arg,
                                                    IUdefAction action,
                                                    IUdefButton button,
                                                    IObjectDescribe describe) {
        SendCrmRemindPojo sendCrmRemindPojo = getSendCrmRemindPojo(action);
        Set<Integer> receiverIds = getReceiveIds(user, sendCrmRemindPojo, arg, objectData, describe, button);
        String content = replaceVars(user, sendCrmRemindPojo.getContent(), arg, objectData, describe, button);
        String title = replaceVars(user, sendCrmRemindPojo.getTitle(), arg, objectData, describe, button);
        //CRM提醒标题长度截取20位字符
        // 去掉 CRM提醒 标题长度 20 个字符的限制 https://www.tapd.cn/54330609/bugtrace/bugs/view?bug_id=1154330609001179655&url_cache_key=d5ac1984d405de0361430fb7e4b9c322
/*
        if(!Strings.isNullOrEmpty(title) && title.length()>20){
            title = title.substring(0,20);
        }
*/
       // log.debug("发送crm提醒。接收人：{}，用户id：{}，发送内容：{}", receiverIds, user.getUserId(), content);
//        CRMNotification sendArg = CRMNotification.builder()
//                .sender(user.getUserId())
//                .content(content)
//                .title(title)
//                .remindRecordType(92)
//                .content2Id(user.getUserId())
//                .dataId("")
//                .receiverIds(receiverIds)
//                .build();
//        proxy.sendCRMNotification(user, sendArg);

        //用户自定义不支持多语
        NewCrmNotification newSendArg = NewCrmNotification.builder()
                .senderId(user.getUserId())
                .remindSender(true)
                .fullContent(content)
                .title(title)
                .type(92)
                .receiverIDs(receiverIds)
                .appId(AppIdUtil.getAppId(user))
                .build();
        proxy.sendNewCrmNotification(user, newSendArg);
        return null;
    }

    private String replaceVars(User user, String content, ButtonExecutor.Arg arg, IObjectData objectData, IObjectDescribe describe, IUdefButton button) {
        List<String> apiNameList = Expression.of(content).parseVariableNames();
        for (String apiName : apiNameList) {
            Object data = parseVarService.getDisplayData(apiName, arg.getArgs(), objectData, user, describe, button);
            if (data == null) {
                data = "";
            }
            content = content.replace("$" + apiName + "$", String.valueOf(data));
        }
        return content;
    }

    private Set<Integer> getReceiveIds(User user, SendCrmRemindPojo pojo, ButtonExecutor.Arg arg, IObjectData objectData, IObjectDescribe describe, IUdefButton button) {
        Map<String, List<String>> recipients = pojo.getRecipients();
        log.debug("发送crm提醒。接收人：{}，用户id：{}", recipients, user.getUserId());
        Set<String> keySet = recipients.keySet();
        if (keySet.contains("vars")) {
            List<String> var = recipients.get("vars");
            List<Variable> varList = parseVarService.getVarList(var, arg.getArgs(), objectData, user, describe, button);
            recipients.remove("vars");//去掉变量，变量做特殊处理
            varList.forEach(x -> {
                String fieldType = x.getFieldType();
                if ((IFieldType.EMPLOYEE.equals(fieldType) || IFieldType.EMPLOYEE_MANY.equals(fieldType)) && x.getValue() != null) {
                    List<String> person = recipients.get("person");
                    if (CollectionUtils.empty(person)) {
                        recipients.put("person", (ArrayList<String>) x.getValue());
                    } else {
                        person.addAll((ArrayList<String>) x.getValue());
                    }
                }
                if ((IFieldType.DEPARTMENT.equals(fieldType) || IFieldType.DEPARTMENT_MANY.equals(fieldType)) && x.getValue() != null) {
                    List<String> dept = recipients.get("dept");
                    if (CollectionUtils.empty(dept)) {
                        recipients.put("dept", (ArrayList<String>) x.getValue());
                    } else {
                        dept.addAll((ArrayList<String>) x.getValue());
                    }
                }
            });
        }
        log.debug("发提醒接受人：{}", recipients);
        Set<String> ret = getEmployeeManager.getReceives(user, describe.getApiName(), objectData.getId(), recipients);
        return ret.stream().map(x -> Integer.valueOf(x)).collect(Collectors.toSet());
    }

    private SendCrmRemindPojo getSendCrmRemindPojo(IUdefAction action) {
        String actionParameter = action.getActionParamter();
        SendCrmRemindPojo pojo = JSON.parseObject(actionParameter, SendCrmRemindPojo.class);
        return pojo;
    }

}
