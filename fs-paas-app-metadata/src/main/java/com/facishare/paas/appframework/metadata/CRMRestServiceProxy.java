package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.metadata.dto.sfa.*;
import com.facishare.paas.appframework.privilege.dto.FindFeedRolePermission;
import com.facishare.paas.appframework.privilege.dto.SetDataPermissionItems;
import com.facishare.rest.core.annotation.*;

import java.util.Map;

@RestResource(value = "CRM_SFA", desc = "crm服务",
        contentType = "application/json", codec = "com.facishare.paas.appframework.metadata.util.CRMRestServiceCodec")
public interface CRMRestServiceProxy {
    @GET(value = "/crm/Import/ObjectList/{userId}")
    GetObjectList.Result getObjectList(@HeaderMap Map<String, String> headers, @PathParams Map<String, String>
            pathParams);

    @GET(value = "crm/{objectName}/getlayout/{id}")
    CheckIsShowRelatedObj.Result checkIsShowRelatedObj(@HeaderMap Map<String, String> headers, @PathParams
            Map<String, String> pathParams);

    @GET(value = "/crm/duplicatesearch/isduplicatesearchtoolenabled", desc = "查询老对象查重工具是否启用")
    DuplicateToolEnabled.Result isDuplicateSearchToolEnabled(@HeaderMap Map<String, String> headers, @QueryParamsMap
            Map<String, String> params);

    @POST(value = "/common/getfunctionrights")
    GetFunctionRights.Result getFunctionRight(@HeaderMap Map<String, String> headers, @Body GetFunctionRights.Arg arg);

    @POST(value = "/common/associate")
    Associate.Result associate(@HeaderMap Map<String, String> headers, @Body Associate.Arg arg);

    @POST(value = "/common/disAssociate")
    Associate.Result disAssociate(@HeaderMap Map<String, String> headers, @Body Associate.Arg arg);

    @POST(value = "/common/lock")
    Lock.Result lock(@HeaderMap Map<String, String> headers, @Body Lock.Arg arg);

    @POST(value = "/common/recover")
    RestCommon.Result recover(@HeaderMap Map<String, String> headers, @Body RestCommon.Arg arg);

    @POST(value = "/common/delete")
    RestCommon.Result delete(@HeaderMap Map<String, String> headers, @Body RestCommon.Arg arg);

    @POST(value = "/common/changeowner")
    ChangeOwner.Result changeOwner(@HeaderMap Map<String, String> headers, @Body ChangeOwner.Arg arg);

    @POST(value = "/common/changeoutowner")
    BulkChangePartner.Result changeOutOwner(@HeaderMap Map<String, String> headers, @Body BulkChangePartner.Arg arg);

    @POST(value = "/common/changeenablepartnerview")
    BulkChangeEnablePartnerView.Result changeEnablePartnerView(@HeaderMap Map<String, String> headers, @Body BulkChangeEnablePartnerView.Arg arg);

    @POST(value = "crm/common/getplainobjectsbynames")
    GetObjectByNames.Result getObjectsByNames(@Body GetObjectByNames.Arg arg, @HeaderMap Map<String, String> headers);

    @POST(value = "crm/common/batchcalculatecountfields")
    BatchCalculateCountFields.Result batchCalculateCountFields(@HeaderMap Map<String, String> headers, @Body BatchCalculateCountFields.Arg arg);

    @GET(value = "crm/common/getobjectcountbyrecordtype/{objectType}/{recordType}")
    GetObjectCountByRecordType.Result getObjectCountByRecordType(@PathParams Map arg, @HeaderMap Map<String, String> headers);

    @POST(value = "/teammember/batchaddteammembers")
    BatchAddTeamMember.Result batchAddTeamMember(@HeaderMap Map<String, String> headers, @Body BatchAddTeamMember.Arg arg);

    @POST(value = "/teammember/query")
    GetTeamMembers.Result getTeamMembers(@Body GetTeamMembers.Arg arg, @HeaderMap Map<String, String> headers);

    @POST(value = "/teammember/modifyteammembers")
    ModifyTeamMember.Result modifyTeamMember(@HeaderMap Map<String, String> headers, @Body ModifyTeamMember.Arg arg);

    @POST(value = "/teammember/delete")
    BatchDeletedTeamMember.Result batchDeletedTeamMember(@HeaderMap Map<String, String> headers, @Body BatchDeletedTeamMember.Arg arg);

    @POST(value = "/crm/{objectName}/batchmove")
    ObjectPoolCommon.Result move(@HeaderMap Map<String, String> headers, @PathParams Map<String, String>
            pathParams, @Body ObjectPoolCommon.Arg arg);

    @POST(value = "/crm/{objectName}/batchreturn")
    ObjectPoolCommon.Result returnBack(@HeaderMap Map<String, String> headers, @PathParams Map<String, String>
            pathParams, @Body MoveOrReturn.Arg arg);

    @POST(value = "/crm/{objectName}/choose")
    ObjectPoolCommon.Result choose(@HeaderMap Map<String, String> headers, @PathParams Map<String, String>
            pathParams, @Body ObjectPoolCommon.Arg arg);

    @POST(value = "/crm/{objectName}/allocate")
    ObjectPoolCommon.Result allocate(@HeaderMap Map<String, String> headers, @PathParams Map<String, String>
            pathParams, @Body AllocateObjects.Arg arg);

    @POST(value = "/crm/customer/setdealstatus")
    RestCommon.Result setDealStatus(@HeaderMap Map<String, String> headers, @Body SetDealStatus.Arg arg);

    @POST(value = "/crm/paasworkflow/getpaastriggerresult")
    PaasTriggerResult.Result getPaasTriggerResult(@HeaderMap Map<String, String> headers, @Body PaasTriggerResult.Arg arg);

    @POST(value = "/crm/paasworkflow/complete")
    Complete.Result complete(@HeaderMap Map<String, String> headers, @Body Complete.Arg arg);

    @POST(value = "/crm/common/batchcheckfunctionright")
    BatchCheckFunctionRight.Result batchCheckFunctionRight(@HeaderMap Map<String, String> headers, @Body BatchCheckFunctionRight.Arg arg);

    @POST(value = "/crm/customer/getcustomerlimit")
    CustomerLimit.Result getCustomerLimit(@HeaderMap Map<String, String> headers, @QueryParam("employeeID") String employeeID, @Body String body);

    @POST(value = "/crm/customer/checkcustomerclaimtime")
    CustomerLimit.CheckCustomerClaimTimeResult checkCustomerClaimTime(@HeaderMap Map<String, String> headers, @Body CustomerLimit.CheckCustomerClaimTimeArg arg);


    @POST(value = "/crm/datapermission/getdatapermissiongroups")
    FindFeedRolePermission.Result getDataPermissionGroups(@HeaderMap Map<String, String> headers, @Body FindFeedRolePermission.Arg arg);

    @POST(value = "/crm/datapermission/setdatapermissionitems")
    SetDataPermissionItems.Result setDataPermissionGroups(@HeaderMap Map<String, String> headers, @Body SetDataPermissionItems.Arg arg);
}
