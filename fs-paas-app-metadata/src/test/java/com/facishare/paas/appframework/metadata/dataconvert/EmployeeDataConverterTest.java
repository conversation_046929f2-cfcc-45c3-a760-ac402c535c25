package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试EmployeeDataConverter类的员工字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class EmployeeDataConverterTest {

  @Mock
  private OrgService orgService;

  @Mock
  private DataConvertContext context;

  @Mock
  private User user;

  @InjectMocks
  private EmployeeDataConverter converter;

  private IFieldDescribe employeeFieldDescribe;
  private IFieldDescribe employeeManyFieldDescribe;
  private IObjectData objectData;

  @BeforeEach
  void setUp() {
    employeeFieldDescribe = createEmployeeFieldDescribe("employee_field", IFieldType.EMPLOYEE);
    employeeManyFieldDescribe = createEmployeeFieldDescribe("employee_many_field", IFieldType.EMPLOYEE_MANY);
    objectData = createObjectData("employee_field", Arrays.asList("user1", "user2"));
    
    // 设置mock行为
    lenient().when(context.getUser()).thenReturn(user);
    lenient().when(user.getTenantId()).thenReturn("tenant123");
    lenient().when(user.getUserId()).thenReturn("currentUser");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法正常转换单个员工
   */
  @Test
  @DisplayName("测试convertFieldData - 单个员工转换")
  void testConvertFieldData_SingleEmployeeConversion() {
    // 准备测试数据
    List<String> employeeIds = Arrays.asList("user1");
    Map<String, String> userNameMap = new HashMap<>();
    userNameMap.put("user1", "张三");
    
    objectData = createObjectData("employee_field", employeeIds);
    when(orgService.getUserNameMapByIds("tenant123", "currentUser", employeeIds))
        .thenReturn(userNameMap);

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, employeeFieldDescribe, context);

    // 验证结果
    assertEquals("张三", result);
    verify(orgService).getUserNameMapByIds("tenant123", "currentUser", employeeIds);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法转换多个员工
   */
  @Test
  @DisplayName("测试convertFieldData - 多个员工转换")
  void testConvertFieldData_MultipleEmployeeConversion() {
    // 准备测试数据
    List<String> employeeIds = Arrays.asList("user1", "user2", "user3");
    Map<String, String> userNameMap = new HashMap<>();
    userNameMap.put("user1", "张三");
    userNameMap.put("user2", "李四");
    userNameMap.put("user3", "王五");
    
    objectData = createObjectData("employee_many_field", employeeIds);
    when(orgService.getUserNameMapByIds("tenant123", "currentUser", employeeIds))
        .thenReturn(userNameMap);

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, employeeManyFieldDescribe, context);

    // 验证结果
    assertEquals("张三|李四|王五", result);
    verify(orgService).getUserNameMapByIds("tenant123", "currentUser", employeeIds);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理null值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null值")
  void testConvertFieldData_HandlesNullValue() {
    // 准备测试数据
    objectData = createObjectData("employee_field", null);

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, employeeFieldDescribe, context);

    // 验证结果
    assertNull(result);
    verify(orgService, never()).getUserNameMapByIds(anyString(), anyString(), anyList());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理空列表
   */
  @Test
  @DisplayName("测试convertFieldData - 处理空列表")
  void testConvertFieldData_HandlesEmptyList() {
    // 准备测试数据
    objectData = createObjectData("employee_field", Collections.emptyList());

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, employeeFieldDescribe, context);

    // 验证结果
    assertNull(result);
    verify(orgService, never()).getUserNameMapByIds(anyString(), anyString(), anyList());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理非List类型值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理非List类型值")
  void testConvertFieldData_HandlesNonListValue() {
    // 准备测试数据
    objectData = createObjectData("employee_field", "not_a_list");

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, employeeFieldDescribe, context);

    // 验证结果
    assertNull(result);
    verify(orgService, never()).getUserNameMapByIds(anyString(), anyString(), anyList());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理部分用户不存在的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 部分用户不存在")
  void testConvertFieldData_PartialUsersNotFound() {
    // 准备测试数据
    List<String> employeeIds = Arrays.asList("user1", "user2", "user3");
    Map<String, String> userNameMap = new HashMap<>();
    userNameMap.put("user1", "张三");
    userNameMap.put("user3", "王五");
    // user2不存在于map中
    
    objectData = createObjectData("employee_many_field", employeeIds);
    when(orgService.getUserNameMapByIds("tenant123", "currentUser", employeeIds))
        .thenReturn(userNameMap);

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, employeeManyFieldDescribe, context);

    // 验证结果 - 不存在的用户会返回null，join时会包含null
    assertEquals("张三|null|王五", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理字段不存在的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 字段不存在")
  void testConvertFieldData_FieldNotExists() {
    // 准备测试数据 - 创建不包含目标字段的数据
    objectData = createObjectData("other_field", Arrays.asList("user1"));

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, employeeFieldDescribe, context);

    // 验证结果
    assertNull(result);
    verify(orgService, never()).getUserNameMapByIds(anyString(), anyString(), anyList());
  }

  /**
   * 提供不同类型的员工数据测试
   */
  private static Stream<Arguments> provideEmployeeData() {
    return Stream.of(
        Arguments.of(Arrays.asList("user1"), IFieldType.EMPLOYEE, "张三"),
        Arguments.of(Arrays.asList("user1", "user2"), IFieldType.EMPLOYEE, "张三"), // 单选只取第一个
        Arguments.of(Arrays.asList("user1"), IFieldType.EMPLOYEE_MANY, "张三"),
        Arguments.of(Arrays.asList("user1", "user2"), IFieldType.EMPLOYEE_MANY, "张三|李四")
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理不同类型的员工字段
   */
  @ParameterizedTest
  @MethodSource("provideEmployeeData")
  @DisplayName("测试convertFieldData - 不同类型的员工字段")
  void testConvertFieldData_DifferentEmployeeFieldTypes(List<String> employeeIds, String fieldType, String expectedResult) {
    // 准备测试数据
    IFieldDescribe fieldDescribe = createEmployeeFieldDescribe("test_field", fieldType);
    Map<String, String> userNameMap = new HashMap<>();
    userNameMap.put("user1", "张三");
    userNameMap.put("user2", "李四");
    
    objectData = createObjectData("test_field", employeeIds);
    when(orgService.getUserNameMapByIds("tenant123", "currentUser", employeeIds))
        .thenReturn(userNameMap);

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, fieldDescribe, context);

    // 验证结果
    assertEquals(expectedResult, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getSupportedFieldTypes方法
   */
  @Test
  @DisplayName("测试getSupportedFieldTypes")
  void testGetSupportedFieldTypes() {
    // 执行被测试方法
    List<String> supportedTypes = converter.getSupportedFieldTypes();

    // 验证结果
    assertNotNull(supportedTypes);
    assertEquals(2, supportedTypes.size());
    assertTrue(supportedTypes.contains(IFieldType.EMPLOYEE));
    assertTrue(supportedTypes.contains(IFieldType.EMPLOYEE_MANY));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convert方法的直接调用
   */
  @Test
  @DisplayName("测试convert方法")
  void testConvert() {
    // 准备测试数据
    List<String> employeeIds = Arrays.asList("user1");
    Map<String, String> userNameMap = new HashMap<>();
    userNameMap.put("user1", "张三");
    
    objectData = createObjectData("employee_field", employeeIds);
    when(orgService.getUserNameMapByIds("tenant123", "currentUser", employeeIds))
        .thenReturn(userNameMap);

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, employeeFieldDescribe, context);

    // 验证结果
    assertEquals("张三", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试处理不支持的字段类型
   */
  @Test
  @DisplayName("测试不支持的字段类型")
  void testUnsupportedFieldType() {
    // 准备测试数据 - 使用不支持的字段类型
    IFieldDescribe unsupportedField = createEmployeeFieldDescribe("test_field", IFieldType.TEXT);
    objectData = createObjectData("test_field", Arrays.asList("user1"));

    // 执行被测试方法
    String result = converter.convertFieldData(objectData, unsupportedField, context);

    // 验证结果 - 不支持的字段类型应该返回null
    assertNull(result);
  }

  /**
   * 创建测试用的员工字段描述
   */
  private IFieldDescribe createEmployeeFieldDescribe(String apiName, String fieldType) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试员工字段");
    fieldMap.put("type", fieldType);
    return FieldDescribeFactory.newInstance(fieldMap);
  }

  /**
   * 创建测试用的对象数据
   */
  private IObjectData createObjectData(String fieldName, Object value) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldName, value);
    return new ObjectData(dataMap);
  }
}
