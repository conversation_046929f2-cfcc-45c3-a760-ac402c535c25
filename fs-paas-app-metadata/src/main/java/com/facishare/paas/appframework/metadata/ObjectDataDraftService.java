package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectDataDraft;
import com.facishare.paas.metadata.api.QueryResult;

import java.util.List;
import java.util.Optional;

/**
 * 草稿箱接口
 *
 * <AUTHOR>
 * @date 2019/11/11 4:12 下午
 */
public interface ObjectDataDraftService {

    /**
     * 创建草稿
     *
     * @param draft 草稿
     * @param user  用户信息
     * @return 草稿
     */
    IObjectDataDraft createDraft(IObjectDataDraft draft, User user);

    /**
     * 编辑草稿
     *
     * @param draft 草稿
     * @return 草稿
     */
    IObjectDataDraft updateDraft(IObjectDataDraft draft, User user);

    /**
     * 删除草稿
     *
     * @param tenantId 租户ID
     * @param id       草稿ID
     */
    void deleteDraftById(String tenantId, String id);

    /**
     * 批量删除草稿
     *
     * @param tenantId 企业ID
     * @param idList   草稿ID列表
     */
    void deleteDraftByIds(String tenantId, List<String> idList);

    /**
     * 查询指定用户下的所有草稿
     *
     * @param user 用户信息
     * @return 草稿列表
     */
    List<IObjectDataDraft> findDraftByUser(User user);

    QueryResult<IObjectDataDraft> findDraftByTemplate(User user, String objectApiName, String recordType, List<String> dataSources, int limit, int offset);

    /**
     * 根据租户ID和草稿ID查询草稿
     *
     * @param tenantId 租户ID
     * @param draftId  草稿ID
     * @return 草稿
     */
    Optional<IObjectDataDraft> findDraftById(String tenantId, String draftId);

    boolean deleteDraftByUser(User user);

    IObjectDataDraft findByBizTypeAndBizId(User user, String bizType, String bizId);

    void deleteByBizTypeAndBizIds(String tenantId, String bizType, List<String> bizIds);

    boolean existPreviousDraft(User user, long timestamp);

    boolean deleteDraftByTimestamp(User user, long timestamp);
}
