package com.facishare.paas.appframework.metadata;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.dto.FunctionPrivilege;
import com.facishare.paas.appframework.metadata.dto.sfa.*;
import com.facishare.paas.appframework.metadata.restdriver.ObjectActionConverter;
import com.facishare.paas.appframework.metadata.restdriver.ObjectActionConverterManager;
import com.facishare.paas.appframework.metadata.util.SFAHeaderUtil;
import com.facishare.paas.appframework.privilege.dto.FindFeedRolePermission;
import com.facishare.paas.appframework.privilege.dto.SetDataPermissionItems;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service("crmRestService")
public class CRMRestServiceImpl implements CRMRestService {
    @Autowired
    private CRMRestServiceProxy crmRestServiceProxy;

    @Autowired
    private NewCRMRestServiceProxy newCRMRestServiceProxy;

    @Autowired
    private ObjectActionConverterManager objectActionConverterManager;

    @Override
    public List<GetObjectList.ImportObject> getImportObjectList(User user) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);

        Map<String, String> param = Maps.newHashMap();
        param.put("userId", user.getUserId());

        GetObjectList.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.getObjectList(header, param);
        } else {
            result = newCRMRestServiceProxy.getObjectList(header, param);
        }

        result.logAndThrowExceptionIfFailed(log, "getImportObjectModule error,user:{}", user);

        return result.getValue().getImportObjectList();
    }

    @Override
    public CheckIsShowRelatedObj.CheckIsShowRelatedObjResult checkIsShowRelatedObj(User user, String objectName,
                                                                                   String id) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);

        Map<String, String> param = Maps.newHashMap();
        param.put("objectName", objectName);
        param.put("id", id);

        CheckIsShowRelatedObj.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.checkIsShowRelatedObj(header, param);
        } else {
            result = newCRMRestServiceProxy.checkIsShowRelatedObj(header, param);
        }

        result.logAndThrowExceptionIfFailed(log, "checkIsShowRelatedObj error,user:{},param:{}", user, param);

        return result.getValue();
    }

    @Override
    public List<FunctionPrivilege> getFunctionPrivileges(User user, String objectApiName, String dataId) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);

        String masterApiName;
        //客户地址、客户财务信息、附件需要查询客户的权限
        if (ObjectAPINameMapping.isAccountDetailObject(objectApiName)) {
            masterApiName = ObjectAPINameMapping.Account.getApiName();
        } else {
            masterApiName = objectApiName;
        }

        GetFunctionRights.Arg arg = GetFunctionRights.Arg.builder()
                .objectType(ObjectAPINameMapping.toObjectType(masterApiName))
                .objectId(dataId)
                .build();

        GetFunctionRights.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.getFunctionRight(header, arg);
        } else {
            result = newCRMRestServiceProxy.getFunctionRight(header, arg);
        }

        result.logAndThrowExceptionIfFailed(log, "getFunctionPrivileges error,user:{},arg:{}", user, arg);

        List<FunctionPrivilege> functionPrivileges = result.getValue().stream().map(x ->
                FunctionPrivilege.builder()
                        .hasPrivilege(x.getRight() == 1)
                        .functionCode(String.valueOf(x.getFunctionCode()))
                        .build())
                .collect(Collectors.toList());

        if (ObjectAPINameMapping.Account.getApiName().equals(objectApiName)) {
            processAccountDetailFunctionPrivileges(functionPrivileges);
        }

        functionPrivileges.forEach(x -> {
            fillActionInfo(x, objectApiName);
        });

        return functionPrivileges;
    }

    private void processAccountDetailFunctionPrivileges(List<FunctionPrivilege> functionPrivileges) {
        functionPrivileges.stream().forEach(x -> {
            List<ObjectAPINameMapping> detailObjects = ObjectAPINameMapping.getAccountDetailObjects();
            for (ObjectAPINameMapping apiNameMapping : detailObjects) {
                if (fillActionInfo(x, apiNameMapping.getApiName())) {
                    break;
                }
            }
        });
    }

    private boolean fillActionInfo(FunctionPrivilege functionPrivilege, String apiName) {
        if (Objects.nonNull(functionPrivilege.getActionCode())) {
            return true;
        }
        ObjectActionConverter actionConverter = objectActionConverterManager.getObjectActionConverter(apiName);
        if (Objects.isNull(actionConverter)) {
            return false;
        }
        String actionCode = actionConverter.toActionCode(String.valueOf(functionPrivilege.getFunctionCode()));
        if (Objects.isNull(actionCode)) {
            return false;
        }
        functionPrivilege.setActionCode(actionCode);
        functionPrivilege.setActionLabel(actionConverter.getLabelByActionCode(functionPrivilege.getActionCode()));
        functionPrivilege.setObjectApiName(apiName);
        return true;
    }

    @Override
    public void associate(User user, String masterDataId, String masterApiName, List<String>
            relatedDataIds,
                          String relatedApiName, String relatedListName) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);

        Associate.Arg arg = getAssociateArg(masterDataId, masterApiName,
                relatedDataIds, relatedApiName, relatedListName);
        Associate.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.associate(header, arg);
        } else {
            result = newCRMRestServiceProxy.associate(header, arg);
        }

        result.logAndThrowExceptionIfFailed(log, "associate error,user:{},arg:{}", user, arg);
    }

    @Override
    public void disAssociate(User user, String masterDataId, String masterApiName, List<String>
            relatedDataIds,
                             String relatedApiName, String relatedListName) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);

        Associate.Arg arg = getAssociateArg(masterDataId, masterApiName,
                relatedDataIds, relatedApiName, relatedListName);
        Associate.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.disAssociate(header, arg);
        } else {
            result = newCRMRestServiceProxy.disAssociate(header, arg);
        }

        result.logAndThrowExceptionIfFailed(log, "disAssociate error,user:{},arg:{}", user, arg);
    }

    @Override
    public void lock(User user, int objectType, List<String> objectIds, boolean isLocked) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);

        Lock.Arg arg = getLockArg(objectType, objectIds, isLocked);
        Lock.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.lock(header, arg);
        } else {
            result = newCRMRestServiceProxy.lock(header, arg);
        }

        result.logAndThrowExceptionIfFailed(log, "lock error,user:{},arg:{}", user, arg);
    }

    @Override
    public void recover(User user, int objectType, List<String> objectIds) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);

        RestCommon.Arg arg = getRestCommonArg(objectType, objectIds);
        RestCommon.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.recover(header, arg);
        } else {
            result = newCRMRestServiceProxy.recover(header, arg);
        }

        result.logAndThrowExceptionIfFailed(log, "recover error,user:{},arg:{}", user, arg);
    }

    @Override
    public void delete(User user, int objectType, List<String> objectIds) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);

        RestCommon.Arg arg = getRestCommonArg(objectType, objectIds);
        RestCommon.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.delete(header, arg);
        } else {
            result = newCRMRestServiceProxy.delete(header, arg);
        }

        result.logAndThrowExceptionIfFailed(log, "delete error,user:{},arg:{}", user, arg);
    }

    @Override
    public void changeOwner(IActionContext context, String apiName, String id, String owner) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(context);

        ChangeOwner.Arg arg = ChangeOwner.Arg.builder()
                .objectId(id)
                .objectType(ObjectAPINameMapping.toObjectType(apiName))
                .ownerId(owner).build();

        ChangeOwner.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.changeOwner(header, arg);
        } else {
            result = newCRMRestServiceProxy.changeOwner(header, arg);
        }

        result.logAndThrowExceptionIfFailed(log, "changeOwner error,user:{},arg:{}", context, arg);
    }

    @Override
    public List<GetObjectByNames.NameIDPojo> getObjectByNames(User user, String apiName, List<String> nameList) {
        GetObjectByNames.Arg arg = new GetObjectByNames.Arg();
        arg.setObjectNames(nameList);
        String objectType = Utils.convertApinameToObjType(apiName);
        arg.setObjectType(objectType);
        RequestContext context = RequestContextManager.getContext();
        if (Objects.nonNull(context)) {
            Boolean needRepeated = context.getAttribute(GetObjectByNames.NEED_REPEATED_DATA);
            arg.setNeedRepeatedData(Objects.nonNull(needRepeated) ? needRepeated : false);
        }
        Map<String, String> header = Maps.newHashMap();
        header.put("x-fs-userInfo", user.getUserId());
        header.put("x-fs-ei", user.getTenantId());
        GetObjectByNames.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.getObjectsByNames(arg, header);
        } else {
            result = newCRMRestServiceProxy.getObjectsByNames(arg, header);
        }

        if (null == result || CollectionUtils.isEmpty(result.getValue())) {
            return null;
        }

        result.logAndThrowExceptionIfFailed(log, "getObjectByNames error,headers:{},arg:{}", header, arg);

        List<GetObjectByNames.NameIDPojo> dataList = result.getValue();
        return dataList;
    }

    @Override
    public List<GetTeamMembers.TeamMemberPojo> getTeamMembers(User user, String apiName, String dataId) {
        GetTeamMembers.Arg arg = new GetTeamMembers.Arg();
        arg.setObject_id(dataId);
        String objectType = Utils.convertApinameToObjType(apiName);
        arg.setObject_type(objectType);
        Map<String, String> header = Maps.newHashMap();
        header.put("x-fs-userInfo", user.getUserId());
        header.put("x-fs-ei", user.getTenantId());
        GetTeamMembers.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.getTeamMembers(arg, header);
        } else {
            result = newCRMRestServiceProxy.getTeamMembers(arg, header);
        }

        /*if (null == result || CollectionUtils.isEmpty(result.getValue())) {
            return null;
        }*/

        result.logAndThrowExceptionIfFailed(log, "getTeamMembers error,headers:{},arg:{}", header, arg);

        List<GetTeamMembers.TeamMemberPojo> dataList = result.getValue();
        return dataList;
    }

    @Override
    public Map<String, Object> batchCalculateCountFields(String tenantId, IObjectData masterObjectData, List<Count> countFields) {
        Map<String, String> headers = new HashMap<>();
        headers.put("x-fs-ei", tenantId);
        headers.put("x-fs-userInfo", User.SUPPER_ADMIN_USER_ID);

        List<BatchCalculateCountFields.CountField> countFieldList = Lists.newArrayList();
        for (Count count : countFields) {
            SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(count.getWheres());
            queryExt.handleWheresFilter(masterObjectData);
            countFieldList.add(BatchCalculateCountFields.CountField.of(count, queryExt.getWheres()));
        }

        BatchCalculateCountFields.Arg arg = BatchCalculateCountFields.Arg.builder()
                .objectApiName(masterObjectData.getDescribeApiName())
                .objectDataId(masterObjectData.getId())
                .countFieldList(countFieldList)
                .build();

        BatchCalculateCountFields.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.batchCalculateCountFields(headers, arg);
        } else {
            result = newCRMRestServiceProxy.batchCalculateCountFields(headers, arg);
        }

        result.logAndThrowExceptionIfFailed(log, "batchCalculateCountFields error,headers:{},arg:{}", headers, arg);

        return result.getValue().getCountFieldValues();
    }

    @Override
    public void batchAddTeamMember(User user, int objectType, List<String> employeeIds, List<String> objectIds, List<String> teamMemberTypes, int permissionType) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);

        BatchAddTeamMember.Arg arg = getBatchAddTeamMemberArg(objectType, employeeIds, objectIds, teamMemberTypes, permissionType);
        BatchAddTeamMember.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.batchAddTeamMember(header, arg);
        } else {
            result = newCRMRestServiceProxy.batchAddTeamMember(header, arg);
        }

        result.logAndThrowExceptionIfFailed(log, "batchAddTeamMember error,user:{},arg:{}", user, arg);
    }

    @Override
    public void modifyTeamMember(User user, int objectType, String objectId, List<ModifyTeamMember.TeamMemberInfoPojo> teamMemberInfos) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);

        ModifyTeamMember.Arg arg = getModifyTeamMemberArg(objectType, objectId, teamMemberInfos);
        ModifyTeamMember.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.modifyTeamMember(header, arg);
        } else {
            result = newCRMRestServiceProxy.modifyTeamMember(header, arg);
        }

        result.logAndThrowExceptionIfFailed(log, "modifyTeamMember error,user:{},arg:{}", user, arg);
    }

    @Override
    public void batchDeletedTeamMember(User user, int objectType, List<String> employeeIds, List<String> objectIds) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);

        BatchDeletedTeamMember.Arg arg = getBatchDeletedTeamMemberArg(objectType, employeeIds, objectIds);
        BatchDeletedTeamMember.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.batchDeletedTeamMember(header, arg);
        } else {
            result = newCRMRestServiceProxy.batchDeletedTeamMember(header, arg);
        }

        result.logAndThrowExceptionIfFailed(log, "batchDeletedTeamMember error,user:{},arg:{}", user, arg);
    }

    private ModifyTeamMember.Arg getModifyTeamMemberArg(Integer object_type, String object_id, List<ModifyTeamMember.TeamMemberInfoPojo> infos) {
        return ModifyTeamMember.Arg.builder()
                .object_type(object_type)
                .object_id(object_id)
                .teamMemberInfos(infos)
                .build();
    }

    private BatchAddTeamMember.Arg getBatchAddTeamMemberArg(Integer object_type, List<String> employee_ids, List<String> object_ids, List<String> teammeber_types, Integer permission_type) {
        return BatchAddTeamMember.Arg.builder()
                .object_type(object_type)
                .object_ids(object_ids)
                .employee_ids(employee_ids)
                .teammeber_types(teammeber_types)
                .permission_type(permission_type)
                .build();
    }

    private BatchDeletedTeamMember.Arg getBatchDeletedTeamMemberArg(Integer object_type, List<String> employee_ids, List<String> object_ids) {
        return BatchDeletedTeamMember.Arg.builder()
                .object_type(object_type)
                .object_ids(object_ids)
                .employee_ids(employee_ids)
                .build();
    }

    private Lock.Arg getLockArg(Integer object_type, List<String> object_ids, Boolean is_locked) {
        return Lock.Arg.builder()
                .object_type(object_type)
                .object_ids(object_ids)
                .is_locked(is_locked)
                .build();
    }

    private RestCommon.Arg getRestCommonArg(Integer object_type, List<String> object_ids) {
        return RestCommon.Arg.builder()
                .object_type(object_type)
                .object_ids(object_ids)
                .build();
    }

    private Associate.Arg getAssociateArg(String associateObjId, String associateObjApiName, List<String>
            associatedObjIds,
                                          String associatedObjApiName, String associatedObjRelatedListName) {
        return Associate.Arg.builder()
                .associateObjApiName(associateObjApiName)
                .associateObjId(associateObjId)
                .associatedObjApiName(associatedObjApiName)
                .associatedObjIds(associatedObjIds)
                .associatedObjRelatedListName(associatedObjRelatedListName)
                .build();
    }

    @Override
    public void bulkChangePartner(User user, String apiName, BulkChangePartner.Pojo pojo) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);
        BulkChangePartner.Arg arg = BulkChangePartner.Arg.builder()
                .objectIds(pojo.getObjectIds())
                .objectType(Integer.valueOf(Utils.convertApinameToObjType(apiName)))
                .outEI(pojo.getOutTenantId())
                .outOwnerID(pojo.getOutOwner())
                .partnerID(pojo.getPartnerId())
                .build();

        BulkChangePartner.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.changeOutOwner(header, arg);
        } else {
            result = newCRMRestServiceProxy.changeOutOwner(header, arg);
        }

        result.logAndThrowExceptionIfFailed(log, "bulkChangePartner error,user:{},arg:{}", user, arg);
    }

    @Override
    public void bulkChangeEnablePartnerView(User user, String apiName, Set<String> objectIds, boolean enablePartnerView) {
        if (!apiName.equals("ContactObj")) {
            throw new ValidateException(I18N.text(I18NKey.UNSUPPORT_INVOKING));
        }
        BulkChangeEnablePartnerView.Arg arg = BulkChangeEnablePartnerView.Arg
                .builder()
                .objectType(Integer.valueOf(Utils.convertApinameToObjType(apiName)))
                .dataIds(objectIds)
                .enablePartnerView(enablePartnerView)
                .build();
        BulkChangeEnablePartnerView.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.changeEnablePartnerView(SFAHeaderUtil.getHeaders(user), arg);
        } else {
            result = newCRMRestServiceProxy.changeEnablePartnerView(SFAHeaderUtil.getHeaders(user), arg);
        }

        result.logAndThrowExceptionIfFailed(log, "bulkChangeEnablePartnerView error,user:{},arg:{}", user, arg);
    }

    @Override
    public ObjectPoolCommon.Result move(User user, String apiName, String objectPoolId, List<String> objectIds) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);
        Map<String, String> param = getObjectNamePathMap(apiName);

        ObjectPoolCommon.Arg arg = getObjectPoolCommonArg(objectPoolId, objectIds);
        ObjectPoolCommon.Result result = crmRestServiceProxy.move(header, param, arg);
        result.logAndThrowExceptionIfFailed(log, "move error,user:{},arg:{}", user, arg);
        return result;
    }

    @Override
    public ObjectPoolCommon.Result returnBack(User user, String apiName, String objectPoolId, List<String> objectIds, String backReason, Integer operationType) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);
        Map<String, String> param = getObjectNamePathMap(apiName);

        MoveOrReturn.Arg arg = new MoveOrReturn.Arg();
        arg.setBackReason(backReason);
        arg.setObjectIDs(objectIds);
        arg.setObjectPoolId(objectPoolId);
        arg.setOperationType(operationType);
        ObjectPoolCommon.Result result = crmRestServiceProxy.returnBack(header, param, arg);
        result.logAndThrowExceptionIfFailed(log, "move error,user:{},arg:{}", user, arg);
        return result;
    }


    @Override
    public void moveSingle(IActionContext context, String apiName, String requestParam) {
        //入参检查
        if (null == requestParam) {
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
        Map<String, String> header = SFAHeaderUtil.getHeaders(context);
        MoveOrReturnSingle.Arg arg = JsonUtil.fromJson(requestParam, MoveOrReturnSingle.Arg.class);
        String objectPoolId = "";
        if (Utils.LEADS_API_NAME.equals(apiName)) {
            objectPoolId = arg.getLeadsPoolId();
        } else if (Utils.ACCOUNT_API_NAME.equals(apiName)) {
            objectPoolId = arg.getHighSeasId();
        }
        List<String> objectIds = Lists.newArrayList(arg.getObjectID());
        Map<String, String> param = getObjectNamePathMap(apiName);

        ObjectPoolCommon.Arg rstArg = getObjectPoolCommonArg(objectPoolId, objectIds);
        ObjectPoolCommon.Result rstResult;
        if (RequestUtil.isCepRequest()) {
            rstResult = crmRestServiceProxy.move(header, param, rstArg);
        } else {
            rstResult = newCRMRestServiceProxy.move(header, param, rstArg);
        }

        RestCommon.Result result = new RestCommon.Result();
        result.setErrorCode(rstResult.getErrorCode());
        result.setMessage(rstResult.getMessage());
        result.setSuccess(rstResult.isSuccess());
        result.logAndThrowExceptionIfFailed(log, "move error,user:{},arg:{}", context, requestParam);
    }

    @Override
    public void returnBackSingle(IActionContext context, String apiName, String requestParam) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(context);
        //入参检查
        if (null == requestParam) {
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
        MoveOrReturnSingle.Arg arg = JsonUtil.fromJson(requestParam, MoveOrReturnSingle.Arg.class);
        String objectPoolId = "";
        if (Utils.LEADS_API_NAME.equals(apiName)) {
            objectPoolId = arg.getLeadsPoolId();
        } else if (Utils.ACCOUNT_API_NAME.equals(apiName)) {
            objectPoolId = arg.getHighSeasId();
        }
        List<String> objectIds = Lists.newArrayList(arg.getObjectID());
        Map<String, String> param = getObjectNamePathMap(apiName);

        MoveOrReturn.Arg rstArg = new MoveOrReturn.Arg();
        rstArg.setBackReason(arg.getBackReason());
        rstArg.setObjectIDs(objectIds);
        rstArg.setObjectPoolId(objectPoolId);
        if (Utils.LEADS_API_NAME.equals(apiName)) {
            rstArg.setOperationType(2);
        } else if (Utils.ACCOUNT_API_NAME.equals(apiName)) {
            rstArg.setOperationType(1);
        }
        ObjectPoolCommon.Result rstResult;
        if (RequestUtil.isCepRequest()) {
            rstResult = crmRestServiceProxy.returnBack(header, param, rstArg);
        } else {
            rstResult = newCRMRestServiceProxy.returnBack(header, param, rstArg);
        }

        RestCommon.Result result = new RestCommon.Result();
        result.setErrorCode(rstResult.getErrorCode());
        result.setMessage(rstResult.getMessage());
        result.setSuccess(rstResult.isSuccess());
        result.logAndThrowExceptionIfFailed(log, "move error,user:{},arg:{}", context, requestParam);
    }

    @Override
    public ObjectPoolCommon.Result choose(User user, String apiName, List<String> objectIds, String objectPoolId) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);
        Map<String, String> param = getObjectNamePathMap(apiName);

        ObjectPoolCommon.Arg arg = getObjectPoolCommonArg(objectPoolId, objectIds);
        ObjectPoolCommon.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.choose(header, param, arg);
        } else {
            result = newCRMRestServiceProxy.choose(header, param, arg);
        }
        result.logAndThrowExceptionIfFailed(log, "choose error,user:{},arg:{}", user, arg);
        return result;
    }

    @NotNull
    private Map<String, String> getObjectNamePathMap(String apiName) {
        String objectName = ObjectAPINameMapping.toOldAPIName(apiName);
        Map<String, String> param = Maps.newHashMap();
        param.put("objectName", objectName);
        return param;
    }

    private ObjectPoolCommon.Arg getObjectPoolCommonArg(String objectPoolId, List<String> object_ids) {
        ObjectPoolCommon.Arg arg = new ObjectPoolCommon.Arg();
        arg.setObjectIDs(object_ids);
        arg.setObjectPoolId(objectPoolId);
        return arg;
    }

    @Override
    public ObjectPoolCommon.Result allocate(User user, String apiName, List<String> objectIds, String objectPoolId, Integer ownerId) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);
        Map<String, String> param = getObjectNamePathMap(apiName);

        AllocateObjects.Arg arg = new AllocateObjects.Arg();
        arg.setOwnerId(ownerId);
        arg.setObjectIDs(objectIds);
        arg.setObjectPoolId(objectPoolId);
        ObjectPoolCommon.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.allocate(header, param, arg);
        } else {
            result = newCRMRestServiceProxy.allocate(header, param, arg);
        }
        result.logAndThrowExceptionIfFailed(log, "allocate error,user:{},arg:{}", user, arg);
        return result;
    }

    @Override
    public void setDealStatus(User user, String accountId, int dealStatus) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);

        if (Strings.isNullOrEmpty(accountId)) {
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }

        SetDealStatus.Arg arg = SetDealStatus.Arg
                .builder()
                .accountId(accountId)
                .dealStatus(dealStatus)
                .build();
        RestCommon.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.setDealStatus(header, arg);
        } else {
            result = newCRMRestServiceProxy.setDealStatus(header, arg);
        }

        result.logAndThrowExceptionIfFailed(log, "setDealStatus error,user:{},arg:{}", user, arg);
    }

    @Override
    public PaasTriggerResult.Result getPaasTriggerResult(User user, PaasTriggerResult.Arg arg) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);
        PaasTriggerResult.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.getPaasTriggerResult(header, arg);
        } else {
            result = newCRMRestServiceProxy.getPaasTriggerResult(header, arg);
        }

        return result;
    }

    @Override
    public Complete.Result complete(User user, Complete.Arg arg) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);
        Complete.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.complete(header, arg);
        } else {
            result = newCRMRestServiceProxy.complete(header, arg);
        }

        return result;
    }

    @Override
    public BatchCheckFunctionRight.Result batchCheckFunctionRight(User user, BatchCheckFunctionRight.Arg arg) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);
        BatchCheckFunctionRight.Result result;
        if (RequestUtil.isCepRequest()) {
            result = crmRestServiceProxy.batchCheckFunctionRight(header, arg);
        } else {
            result = newCRMRestServiceProxy.batchCheckFunctionRight(header, arg);
        }

        return result;
    }

    @Override
    public CustomerLimit.Result getCustomerLimit(User user, Integer employeeId) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);
        CustomerLimit.Result result = crmRestServiceProxy.getCustomerLimit(header, employeeId.toString(), "");
        return result;
    }

    @Override
    public CustomerLimit.CheckCustomerClaimTimeResult checkCustomerClaimTime(User user, Integer employeeId, String poolId, List<String> objectIds) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);
        CustomerLimit.CheckCustomerClaimTimeArg arg = CustomerLimit.CheckCustomerClaimTimeArg.builder()
                .owner(String.valueOf(employeeId)).poolId(poolId).objectIds(objectIds).build();
        CustomerLimit.CheckCustomerClaimTimeResult result = crmRestServiceProxy.checkCustomerClaimTime(header, arg);
        return result;
    }

    @Override
    public FindFeedRolePermission.Result getDataPermissionGroups(User user, List<String> dataRoleIDs) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);
        FindFeedRolePermission.Arg arg = FindFeedRolePermission.Arg.builder()
                .currentEmployeeID(user.getUserId())
                .dataRoleIDs(dataRoleIDs)
                .build();
        return crmRestServiceProxy.getDataPermissionGroups(header, arg);
    }

    @Override
    public SetDataPermissionItems.Result setDataPermissionGroups(User user, List<FindFeedRolePermission.DataPermissionItem> dataPermissionItems) {
        Map<String, String> header = SFAHeaderUtil.getHeaders(user);
        SetDataPermissionItems.Arg arg = SetDataPermissionItems.Arg.builder()
                .currentEmployeeID(user.getUserId())
                .dataPermissionItems(dataPermissionItems)
                .build();
        return crmRestServiceProxy.setDataPermissionGroups(header, arg);
    }
}
