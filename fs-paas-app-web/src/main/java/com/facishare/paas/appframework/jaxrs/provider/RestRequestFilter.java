package com.facishare.paas.appframework.jaxrs.provider;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.jaxrs.annotation.RestAPI;
import org.springframework.stereotype.Component;

import javax.annotation.Priority;
import javax.ws.rs.ext.Provider;

@Priority(100)
@Provider
@Component
@RestAPI
public class RestRequestFilter extends AbstractInnerRequestFilter {
    @Override
    protected RequestContext.RequestSource getRequestSource() {
        return RequestContext.RequestSource.REST;
    }
}
