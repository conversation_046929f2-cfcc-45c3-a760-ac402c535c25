package com.facishare.paas.appframework.function.dto;

import com.google.common.base.Strings;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public interface IncrementNumberBatchExecute {
    @Data
    @Builder
    class Arg {
        String functionAPIName;
        String bindingObjectAPIName;
        List<ObjectInfo> objectInfos;
    }

    @Data
    @Builder
    class ObjectInfo {
        String id;  //自增编号虚拟id,返回时,需要根据这个合并返回结果
        Map<String, Object> data;
    }

    @Data
    class Result {
        boolean success;
        String errorMessage;
        /**
         * key 虚拟的数据id
         * value 函数自增编号规则
         */
        Map<String, IncrementNumber> result;
    }

    @Data
    class IncrementNumber {
        /**
         * 计数器,一个字符串,如果计数器相同则在计数器规则上开始编号自增.
         * 如果计数器不同则编号会从零开始递增,
         * 如果号码符合某种规则开始递增,需要保证计数器的字符串相同
         */
        private String counter;
        /**
         * condition：重计规则分为四种「YEAR、MONTH、DAY、NONE」
         */
        private String condition;
        /**
         * 指定后缀
         */
        private String postfix;
        /**
         * 指定前缀
         */
        private String prefix;

        /**
         * 编号位数。默认为2位
         */
        private Integer serialNumber = 2;
        /**
         * 自增编号的类型分为两种  1:直接返回字符串,直接使用
         * 0: 返回规则,需要上面三个不为空
         */
        private int type;
        private String value;

        /**
         * 编号起始值不能小于 0，且默认为 1
         */
        private Integer initialValue;
        /**
         * 步进器默认为1，且不能小于1
         */
        private Integer steppingNumber;

        public boolean isRulesType() {
            return type == 0;
        }

        public String getAutoNumber(long l) {
            if (!isRulesType()) {
                return getValue();
            }
            String numberPattern = "%s%0" + getSerialNumber() + "d%s";
            return String.format(numberPattern, getPrefix(), l, getPostfix());
        }

        private String getPostfix() {
            return Strings.nullToEmpty(postfix);
        }

        private String getPrefix() {
            return Strings.nullToEmpty(prefix);
        }

        private int getSerialNumber() {
            if (null == serialNumber || serialNumber < 0) {
                return 2;
            }
            return serialNumber;
        }

        public Integer getInitialValue() {
            if (null == initialValue || initialValue < 0) {
                return 1;
            }
            return initialValue;
        }

        public Integer getSteppingNumber() {
            if (null == steppingNumber || steppingNumber < 1) {
                return 1;
            }
            return steppingNumber;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            IncrementNumber that = (IncrementNumber) o;
            return Objects.equals(getCounter(), that.getCounter()) &&
                    Objects.equals(getCondition(), that.getCondition());
        }

        @Override
        public int hashCode() {
            return Objects.hash(getCondition(), getCounter());
        }
    }
}
