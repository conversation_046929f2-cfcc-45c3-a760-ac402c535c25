package com.facishare.paas.appframework.core.predef.handler.flowcompleted;

import com.facishare.paas.appframework.core.model.handler.HandlerContext;

/**
 * Created by zhouwr on 2023/11/1.
 */
public abstract class AbstractFlowCompletedAfterHandler implements FlowCompletedActionHandler {

    protected abstract Result doHandle(HandlerContext context, Arg arg);

    @Override
    public Result handle(HandlerContext context, Arg arg) {
        if (!arg.getInterfaceArg().isPass()) {
            return new Result();
        }
        return doHandle(context, arg);
    }
}
