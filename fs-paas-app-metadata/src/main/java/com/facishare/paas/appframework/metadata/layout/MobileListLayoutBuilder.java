package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ButtonUsePageType;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ListLayoutExt;
import com.facishare.paas.appframework.metadata.layout.component.*;
import com.facishare.paas.appframework.metadata.layout.factory.ListComponentFactory;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.ButtonExt.IMPORT_EXPORT_BUTTON_NAME;
import static com.facishare.paas.appframework.metadata.layout.component.ListComponentInfo.ButtonRenderType.LIST_SINGLE;

/**
 * create by zhaoju on 2020/11/23
 */
public class MobileListLayoutBuilder {
    private ListLayoutExt listLayout;
    private PageType pageType;
    private ListComponentFactory listComponentFactory;

    @Builder
    public MobileListLayoutBuilder(LayoutExt listLayout, PageType pageType, ListComponentFactory listComponentFactory) {
        this.listLayout = ListLayoutExt.of(listLayout);
        this.pageType = pageType;
        this.listComponentFactory = listComponentFactory;
    }

    //移动端通用列表配置同步Web端通用列表配置
    public LayoutExt getMobileListLayout() {
        LayoutExt mobileListLayout;
        boolean enableMobileLayout = listLayout.isEnableMobileLayout();
        if (enableMobileLayout) {
            mobileListLayout = LayoutExt.of(listLayout.toLayoutExt().getMobileLayout());
            // 补充场景信息
            ListLayoutExt.of(mobileListLayout).getFirstListComponent()
                    .ifPresent(mobileListComponent -> {
                        syncSceneInfo(mobileListComponent);
                        syncListSingleButtonInfo(mobileListComponent);
                    });
        } else {
            mobileListLayout = LayoutExt.of(Maps.newHashMap());
            mobileListLayout.setComponents(getComponents());
            mobileListLayout.setLayoutStructure(getLayoutStructure());
            mobileListLayout.setRefObjectApiName(listLayout.getRefObjectApiName());
        }
        // 移动端布局部分属性需要使用web的配置
        ListLayoutExt.of(mobileListLayout).getFirstListComponent()
                .ifPresent(mobileListComponent -> {
                    syncViewInfo(mobileListComponent);
                    syncSelectListButton(mobileListComponent);
                    if (Objects.equals(pageType, PageType.ListHeader)) {//只有ListHeader接口请求才补充指定字段汇总
                        syncTotalsInfo(mobileListComponent);
                    }
                });
        syncLayoutPlugin(mobileListLayout);
        // 设置当前 layout 有没有开起独立布局
        LayoutContext.get().setEnableMobileLayout(enableMobileLayout);
        return mobileListLayout;
    }

    private void syncLayoutPlugin(LayoutExt mobileListLayout) {
        LayoutStructure.syncLayoutPlugins(listLayout.toLayoutExt(), mobileListLayout);
    }

    private void syncListSingleButtonInfo(ListComponentExt mobileListComponent) {
        if (isDesigner()) {
            return;
        }
        Optional<IListComponentInfo> listComponentInfoOpt = listLayout.getFirstListComponent()
                .flatMap(webListComponent -> webListComponent.getButtonInfoByUsePageAndRenderPageType(ButtonUsePageType.DataList,
                        IComponentInfo.PAGE_TYPE_LIST));
        List<IListComponentInfo> buttonInfo = mobileListComponent.getButtonInfo();
        buttonInfo.removeIf(it -> ButtonUsePageType.DataList.getId().equals(it.getRenderType()) && IComponentInfo.PAGE_TYPE_LIST.equals(it.getPageType()));
        listComponentInfoOpt
                .ifPresent(buttonInfo::add);
        mobileListComponent.resetButtonInfos(buttonInfo);
    }

    private boolean isDesigner() {
        return pageType == PageType.Designer;
    }

    /**
     * 移动端选数据列表按钮配置，需要同步web列表的数据
     *
     * @param mobileListComponent
     */
    private void syncSelectListButton(ListComponentExt mobileListComponent) {
        if (mobileListComponent.isEnableSelectedLayout()) {
            return;
        }
        Optional<IListComponentInfo> listComponentInfoOpt = listLayout.getFirstListComponent()
                .flatMap(webListComponent -> webListComponent.getButtonInfoByUsePageAndRenderPageType(ButtonUsePageType.ListNormal,
                        IComponentInfo.PAGE_TYPE_SELECTED));
        List<IListComponentInfo> buttonInfo = mobileListComponent.getButtonInfo();
        buttonInfo.removeIf(it -> ButtonUsePageType.ListNormal.getId().equals(it.getRenderType()) && IComponentInfo.PAGE_TYPE_SELECTED.equals(it.getPageType()));
        listComponentInfoOpt
                .ifPresent(buttonInfo::add);
        mobileListComponent.resetButtonInfos(buttonInfo);
    }

    private void syncSceneInfo(ListComponentExt mobileListComponent) {
        if (CollectionUtils.empty(mobileListComponent.getSceneInfo())) {
            List<IScenesComponentInfo> mobileSceneInfo = getMobileSceneInfo(listLayout.getFirstListComponent().orElse(null));
            mobileListComponent.resetSceneInfos(mobileSceneInfo);
        }
        resetRenderTypeInfos(mobileListComponent);
    }

    private void syncViewInfo(ListComponentExt mobileListComponent) {
        listLayout.getFirstListComponent()
                .ifPresent(webListLayoutComponent -> {
                    List<IViewComponentInfo> viewInfos = webListLayoutComponent.getViewInfos();
                    mobileListComponent.resetViewInfos(viewInfos);
                });
    }

    /**
     * 移动端汇总字段配置同步（来源于Web端）
     * 移动端开启“独立配置布局”后，汇总字段依然可以沿用web端布局配置
     * 移动端可以配置 显示/关闭 全部数据汇总字段
     * 显示：只同步全部数据汇总字段配置
     *
     * @param mobile
     */
    private void syncTotalsInfo(ListComponentExt mobile) {
        Optional<ListComponentExt> web = listLayout.getFirstListComponent();
        web.ifPresent(i -> {
            if (!mobile.isDisplayTotalsFromMobile() && !mobile.isDisplayTotalsFromSelected()) {
                mobile.setAllPageSummaryComponentInfo(Collections.emptyList());
                mobile.resetAggregateInfos(Collections.emptyList());
                return;
            }
            if (i.enableAggregate()) {
                List<IAggregateComponentInfo> aggregateInfos;
                if (i.isEnableSelectedLayout()) {
                    aggregateInfos = i.getAggregateInfos();
                } else {
                    aggregateInfos = i.getAggregateInfosByPageType(IComponentInfo.PAGE_TYPE_LIST);
                }
                mobile.resetAggregateInfos(CollectionUtils.nullToEmpty(aggregateInfos));
                return;
            }
            List<ISummaryComponentInfo> allPageSummaryComponentInfo = i.getAllPageSummaryComponentInfoByPageType(IComponentInfo.PAGE_TYPE_LIST);
            //Web端有配置汇总字段，且移动端未设置不显示
            mobile.setAllPageSummaryComponentInfo(CollectionUtils.nullToEmpty(allPageSummaryComponentInfo));
        });
    }

    private Map<String, Object> getLayoutStructure() {
        Map<String, Object> layoutStructure = Maps.newLinkedHashMap();

        Map<String, Object> row1 = Maps.newLinkedHashMap();
        row1.put(LayoutStructure.COLUMNS, Collections.singletonList(ImmutableMap.of("width", "100%")));
        List<List<String>> components = Lists.newArrayList();
        components.add(Lists.newArrayList(ListComponentExt.LIST_COMPONENT));
        row1.put(LayoutStructure.COMPONENTS, components);

        layoutStructure.put(LayoutStructure.LAYOUT, Lists.newArrayList(row1));
        return layoutStructure;
    }

    private List<IComponent> getComponents() {
        ListComponentExt listComponent = listLayout.getFirstListComponent().map(listComponentExt -> {
            viewInfos(listComponentExt)
                    .buttonInfos(listComponentExt)
                    .sceneInfos(listComponentExt)
                    .filtersInfos(listComponentExt)
                    .initInfos(listComponentExt);
            return listComponentExt;
        }).orElseGet(() -> {
            IObjectDescribe describe = new ObjectDescribe();
            describe.setApiName(listLayout.getRefObjectApiName());
            return listComponentFactory.createDefaultComponent(User.systemUser(listLayout.getTenantId()), describe);
        });
        List<IComponent> components = Lists.newArrayList(listComponent.getComponent());
        List<IComponent> listComponents = listLayout.getAllComponents().stream()
                .filter(component -> AppFrameworkConfig.getListLayoutSupportComponent().contains(component.getType()))
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(listComponents)) {
            components.addAll(listComponents);
        }
        return components;
    }

    private void initInfos(ListComponentExt listComponentExt) {
        listComponentExt.resetSummaryComponentInfo(Collections.emptyList());
        resetRenderTypeInfos(listComponentExt);
        if (isDesigner()) {
            listComponentExt.setEnableSelectedLayout(null);
        }
        // web端的配置中有移动端才会有的标记，这里需要移除这两个标记，避免脏数据
        listComponentExt.removeDisplayTotals();
    }

    private MobileListLayoutBuilder sceneInfos(ListComponentExt listComponentExt) {
        List<IScenesComponentInfo> sceneInfos = getMobileSceneInfo(listComponentExt);
        listComponentExt.resetSceneInfos(sceneInfos);
        return this;
    }

    private List<IScenesComponentInfo> getMobileSceneInfo(ListComponentExt listComponentExt) {
        if (Objects.isNull(listComponentExt)) {
            return Lists.newArrayList();
        }
        return listComponentExt.getSceneInfo().stream()
                .peek(it -> it.setRenderType(IListComponentInfo.RENDER_TYPE_DROP_DOWN))
                .collect(Collectors.toList());
    }

    private MobileListLayoutBuilder filtersInfos(ListComponentExt listComponentExt) {
        if (isDesigner()) {
            listComponentExt.resetFiltersInfos(Collections.emptyList());
        }
        return this;
    }

    private MobileListLayoutBuilder buttonInfos(ListComponentExt listComponentExt) {
        List<IListComponentInfo> buttonComponent = listComponentExt.getButtonInfo().stream()
                .filter(it -> !(isDesigner() && LIST_SINGLE.getType().equals(it.getRenderType())))
                .peek(this::filterButtonInMobile)
                .collect(Collectors.toList());
        listComponentExt.resetButtonInfos(buttonComponent);
        return this;
    }

    private void filterButtonInMobile(IListComponentInfo listComponentInfo) {
        List<String> order = listComponentInfo.getOrder();
        if (CollectionUtils.empty(order)) {
            return;
        }
        List<String> newOrder = order.stream().filter(it -> !IMPORT_EXPORT_BUTTON_NAME.contains(it)).collect(Collectors.toList());
        listComponentInfo.setOrder(newOrder);
    }

    private MobileListLayoutBuilder viewInfos(ListComponentExt listComponentExt) {
        if (isDesigner()) {
            listComponentExt.resetViewInfos(Collections.emptyList());
        }
        return this;
    }

    private void resetRenderTypeInfos(ListComponentExt listComponentExt) {
        if (CollectionUtils.notEmpty(listComponentExt.getRenderTypeInfo())) {
            return;
        }
        listComponentExt.resetRenderTypeInfos(Lists.newArrayList(RenderTypeComponentInfo.defaultRenderTypeInfo()));
    }
}
