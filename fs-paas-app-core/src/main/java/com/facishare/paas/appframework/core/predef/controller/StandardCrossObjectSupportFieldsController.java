package com.facishare.paas.appframework.core.predef.controller;

import cn.hutool.core.exceptions.ValidateException;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.dto.CrossObjectFilter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 标准跨对象筛选支持字段控制器
 * 
 * <AUTHOR>
 */
@Slf4j
public class StandardCrossObjectSupportFieldsController extends AbstractStandardController<StandardCrossObjectSupportFieldsController.Arg, StandardCrossObjectSupportFieldsController.Result> {


    @Override
    protected Result doService(Arg arg) {
        try {            
            // 检查参数
            if (CollectionUtils.empty(arg.getBusinessObjects())) {
                log.warn("Business object list is empty");
                throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
            }
            // 构建请求参数
            CrossObjectFilter.ObjRelationArg objRelationArg = CrossObjectFilter.ObjRelationArg.builder()
                    .businessObjects(arg.getBusinessObjects())
                    .build();
            stopWatch.lap("buildArg");
            // 调用BI服务获取对象关系结果
            CrossObjectFilter.ObjRelationResult objRelationResult = infraServiceFacade.getObjRelationResult(controllerContext.getUser(), objRelationArg);
            stopWatch.lap("getObjRelationResult");
            // 构建返回结果
            Result result = new Result();
            if (Objects.nonNull(objRelationResult)) {
                result.setObjectsAndFields(objRelationResult.getObjectsAndFields());
            }
            return result;
        } catch (Exception e) {
            log.error("Exception occurred while getting cross-object filter supported fields", e);
            throw new ValidateException(I18NExt.text(I18NKey.BI_SERVICE_ERROR));
        }
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    /**
     * 请求参数
     */
    @Data
    public static class Arg {
        /**
         * 业务对象列表
         */
        private List<Map<String, Object>> businessObjects;
    }

    /**
     * 返回结果
     */
    @Data
    public static class Result {
        /**
         * 对象和字段信息
         */
        private List<CrossObjectFilter.ObjectAndField> objectsAndFields;
        
    }
} 