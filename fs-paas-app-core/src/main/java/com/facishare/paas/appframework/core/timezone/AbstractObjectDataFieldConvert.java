package com.facishare.paas.appframework.core.timezone;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/06/17
 */
public abstract class AbstractObjectDataFieldConvert implements ObjectDataFieldConvert {

    public static final String CONVERT_MARK = "_convert_mark_";

    protected IObjectData toObjectData(Object value) {
        Objects.requireNonNull(value);
        if (value instanceof ObjectDataDocument) {
            return ((ObjectDataDocument) value).toObjectData();
        }
        if (value instanceof Map) {
            return ObjectDataExt.of((Map) value);
        }
        if (value instanceof String) {
            IObjectData objectData = new ObjectData();
            objectData.fromJsonString((String) value);
            return objectData;
        }
        throw new IllegalArgumentException();
    }

    protected List<IObjectData> toDataList(Object value) {
        if (value instanceof List) {
            List<?> dataList = (List<?>) value;
            if (CollectionUtils.empty(dataList)) {
                return Lists.newArrayList();
            }
            return dataList.stream()
                    .map(this::toObjectData)
                    .collect(Collectors.toList());
        }
        throw new IllegalArgumentException();
    }

    protected Map<String, List<IObjectData>> toDetailData(Object value) {
        Objects.requireNonNull(value);
        if (value instanceof Map) {
            Map<String, List<IObjectData>> result = Maps.newHashMap();
            ((Map<String, List<?>>) value).forEach((apiName, dataList) -> {
                List<IObjectData> objectDataList = toDataList(dataList);
                result.put(apiName, objectDataList);
            });
            return result;
        }
        throw new IllegalArgumentException();
    }

    protected Map<String, List<IObjectData>> toCalculateDetailData(Object value) {
        Objects.requireNonNull(value);
        if (value instanceof Map) {
            Map<String, List<IObjectData>> result = Maps.newHashMap();
            ((Map<String, Map<String, ?>>) value).forEach((apiName, values) -> {
                List<IObjectData> dataList = values.entrySet().stream()
                        .map(it -> {
                            IObjectData data = toObjectData(it.getValue());
                            data.set(CONVERT_MARK, it.getKey());
                            return data;
                        }).collect(Collectors.toList());
                result.put(apiName, dataList);
            });
            return result;
        }
        throw new IllegalArgumentException();
    }

    protected Map<String, Map<String, IObjectData>> toCalculateDetail(Object value) {
        Objects.requireNonNull(value);
        if (value instanceof Map) {
            Map<String, Map<String, IObjectData>> result = Maps.newHashMap();
            ((Map<String, Map<String, ?>>) value).forEach((apiName, values) -> {
                Map<String, IObjectData> dataMap = values.entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, it -> toObjectData(it.getValue())));
                result.put(apiName, dataMap);
            });
            return result;
        }
        throw new IllegalArgumentException();
    }

    protected <T> T toEntity(IObjectData objectData, T entity) {
        Objects.requireNonNull(objectData);
        if (entity instanceof ObjectDataDocument) {
            return (T) ObjectDataDocument.of(objectData);
        }
        if (entity instanceof Map) {
            return (T) ObjectDataExt.of(objectData).toMap();
        }
        if (entity instanceof String) {
            return (T) objectData.toJsonString();
        }
        throw new IllegalArgumentException();
    }

    private <T> List<T> toEntityList(List<IObjectData> dataList, List<T> entity) {
        if (CollectionUtils.empty(entity)) {
            return entity;
        }
        T o = entity.get(0);
        return dataList.stream()
                .map(objectData -> toEntity(objectData, o))
                .collect(Collectors.toList());
    }

    protected <T> T toListEntity(List<IObjectData> dataList, T entity) {
        Objects.requireNonNull(entity);
        if (entity instanceof List) {
            List<?> entityList = (List<?>) entity;
            return (T) toEntityList(dataList, entityList);
        }
        throw new IllegalArgumentException();
    }

    protected <T> T toDetailEntity(Map<String, List<IObjectData>> detail, T entity) {
        Objects.requireNonNull(entity);
        if (entity instanceof Map) {
            Map<String, List<?>> result = Maps.newHashMap();
            ((Map<String, List<?>>) entity).forEach((apiName, values) -> {
                List<IObjectData> dataList = detail.get(apiName);
                List<?> entityList = toEntityList(dataList, values);
                result.put(apiName, entityList);
            });
            return (T) result;
        }
        throw new IllegalArgumentException();
    }

    protected <T> T toCalculateEntity(Map<String, Map<String, IObjectData>> calculateDetail, T entity) {
        Objects.requireNonNull(entity);
        if (entity instanceof Map) {
            Map<String, Map<String, ?>> result = Maps.newHashMap();
            ((Map<String, Map<String, ?>>) entity).forEach((apiName, values) -> {
                if (CollectionUtils.empty(values)) {
                    result.put(apiName, values);
                    return;
                }
                Object o = Lists.newArrayList(values.values()).get(0);
                Map<String, IObjectData> objectDataMap = calculateDetail.get(apiName);
                Map<String, Object> objectMap = objectDataMap.entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, it -> toEntity(it.getValue(), o)));
                result.put(apiName, objectMap);
            });
            return (T) result;
        }
        throw new IllegalArgumentException();
    }

}
