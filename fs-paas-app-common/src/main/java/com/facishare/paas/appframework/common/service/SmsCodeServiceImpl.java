package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.MobileInfo;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.userlogin.api.model.validatecode.BuildValidateCode;
import com.facishare.userlogin.api.model.validatecode.InnerSmsMessage;
import com.facishare.userlogin.api.model.validatecode.SmsMessage;
import com.facishare.userlogin.api.model.validatecode.VerifyValidateCode;
import com.facishare.userlogin.api.service.ValidateCodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service("smsCodeService")
public class SmsCodeServiceImpl implements SmsCodeService {

    private ValidateCodeService validateCodeService;

    @Autowired // from META-INF/fs-user-login-api-dubbo-rest-client.xml
    public void setValidateCodeService(ValidateCodeService validateCodeService) {
        this.validateCodeService = validateCodeService;
    }

    @Override
    public String sendSmsCode(String ea, User user, String ip, String areaCode, String mobile, String captchaCode, String captchaId, String biz, String i18nKey) {
        // 1. 验证是否发送动态验证码并生成动态验证码
        BuildValidateCode.Result smsCodeRes = createSmsCode(user, ip, areaCode, mobile, captchaCode, captchaId, 180);

        if (Objects.isNull(smsCodeRes)) {
            // 接口返回错误
            return BuildValidateCode.SendValidateCodeEnum.ERROR.name();
        }
        if (!Objects.equals(BuildValidateCode.SendValidateCodeEnum.SUCCESS, smsCodeRes.getResult())) {
            // 生成验证码失败
            log.warn("userlogin build captchaCode is fail, tenantId: {}, mobile: {}, captchaId: {}", user.getTenantId(), mobile, captchaId);
            return smsCodeRes.getResult().name();
        }
        if (StringUtils.isBlank(smsCodeRes.getCode())) {
            // 生成验证码为空
            log.warn("userlogin build captchaCode is empty, tenantId: {}, mobile: {}, captchaId: {}", user.getTenantId(), mobile, captchaId);
            return BuildValidateCode.SendValidateCodeEnum.ERROR.name();
        }
        // 2. 制作短信内容
        String code = smsCodeRes.getCode();
        String content = I18NExt.text(I18NKey.DEFAULT_SM_LOGIN_I18N_KEY, code);
        content = content.replace("{}", code);
        if (StringUtils.isBlank(content)) {
            // 生成短信内容异常
            log.warn("i18n build message content is empty, tenantId: {}, mobile: {}, captchaId: {}", user.getTenantId(), mobile, captchaId);
            return BuildValidateCode.SendValidateCodeEnum.ERROR.name();
        }
        // 3. 发送短信
        boolean success = sendSMS(ea, user, areaCode, mobile, biz, content);
        // 成功是指成功调用发送短信接口，并不一定能被手机接收到
        return success ? BuildValidateCode.SendValidateCodeEnum.SUCCESS.name() : BuildValidateCode.SendValidateCodeEnum.SMS_SEND_ERROR.name();
    }

    @Override
    public BuildValidateCode.Result createSmsCode(User user, String ip, String areaCode, String mobile, String captchaCode, String captchaId, int expireTime) {
        MobileInfo mobileInfo = new MobileInfo(areaCode, mobile);
        BuildValidateCode.Argument validAndGetCodeBody = new BuildValidateCode.Argument();
        validAndGetCodeBody.setKey(mobileInfo.smsMobile());
        validAndGetCodeBody.setIp(ip);
        validAndGetCodeBody.setImgCode(captchaCode);
        validAndGetCodeBody.setEpxId(captchaCode);
        validAndGetCodeBody.setCodeExpireTime(expireTime);
        return validateCodeService.buildCode(validAndGetCodeBody);
    }

    @Override
    public String verifySmsCode(User user, String areaCode, String mobile, String smsCode) {
        MobileInfo mobileInfo = new MobileInfo(areaCode, mobile);
        VerifyValidateCode.Argument body = new VerifyValidateCode.Argument();
        body.setCode(smsCode);
        // 验证时的 key 保持一致要和生成验证码的 key
        // 都使用了 国际冠码（00）-国际电话区号-封闭电话号码
        body.setKey(mobileInfo.smsMobile());
        VerifyValidateCode.Result result = validateCodeService.verifyCode(body);
        return result.getResult().name();
    }

    @Override
    public boolean sendSMS(String ea, User user, String areaCode, String mobile, String biz, String content) {
        boolean result;
        if (StringUtils.equals(biz, "CRM")) {
            SmsMessage.Argument sendSmsBody = new SmsMessage.Argument();
            sendSmsBody.setAreaCode(areaCode);
            sendSmsBody.setMobile(mobile);
            sendSmsBody.setEmployeeId(user.getUserIdInt());
            sendSmsBody.setEnterpriseAccount(ea);
            sendSmsBody.setMessage(content);
            result = validateCodeService.sendMessage(sendSmsBody).isSucceed();
        } else {
            InnerSmsMessage.Argument sendSmsBody = new InnerSmsMessage.Argument();
            sendSmsBody.setMobile(mobile);
            sendSmsBody.setContent(content);
            validateCodeService.sendInnerSms(sendSmsBody);
            result = true;
        }
       
        return result;
    }
    
    
}
