package com.facishare.paas.appframework.metadata.menu;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.google.common.collect.Lists;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataActionService;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.menu.model.MenuItemConfigObject;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class MenuCommonServiceImpl implements MenuCommonService {
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private MetaDataFindService metaDataFindService;
    @Autowired
    private MetaDataActionService metaDataActionService;

    @Override
    public IObjectData createMenuItem(User user, String apiName) {
        IObjectDescribe menuItemDescribe = describeLogicService.findObject(user.getTenantId(), MenuConstants.MENU_ITEM_API_NAME);

        IObjectData systemCrm = this.findDefaultCrmMenu(user);
        String menuId = systemCrm.getId();
        List<IObjectData> menuObjectList = this.findMenuItemByApiName(user, menuId, Lists.newArrayList(apiName));
        if (CollectionUtils.isNotEmpty(menuObjectList)) {
            log.warn("生成失败，当前对象已生成菜单项");
            return menuObjectList.get(0);
        }
        IObjectData lastMenuItem = this.findLastCrmMenuItem(user, menuId);
        Integer lastOrder = lastMenuItem.get(MenuConstants.MenuItemField.NUMBER.getApiName(), Integer.class);
        IObjectData newMenuItem = this.buildMenuItem(user, menuId, menuItemDescribe, apiName);
        newMenuItem.set(MenuConstants.MenuItemField.NUMBER.getApiName(), lastOrder + 5);
        metaDataActionService.saveObjectData(user, newMenuItem);
        return newMenuItem;
    }

    @Override
    public IObjectData createOrUpdateMenuItemWithOrder(User user, String apiName, Integer order) {
        if (order == null) {
            throw new ValidateException(I18N.text(I18NKey.SORT_NUMBER_CANNOT_BE_NULL));
        }
        IObjectDescribe menuItemDescribe = describeLogicService.findObject(user.getTenantId(), MenuConstants.MENU_ITEM_API_NAME);

        IObjectData systemCrm = this.findDefaultCrmMenu(user);
        String menuId = systemCrm.getId();
        List<IObjectData> menuObjectList = this.findMenuItemByApiName(user, menuId, Lists.newArrayList(apiName));
        IObjectData newMenuItem;
        if (CollectionUtils.isNotEmpty(menuObjectList)) {
            newMenuItem = menuObjectList.get(0);
            newMenuItem.set(MenuConstants.MenuItemField.NUMBER.getApiName(), order);
            metaDataActionService.updateObjectData(user, newMenuItem);
        } else {
            newMenuItem = this.buildMenuItem(user, menuId, menuItemDescribe, apiName);
            newMenuItem.set(MenuConstants.MenuItemField.NUMBER.getApiName(), order);
            metaDataActionService.saveObjectData(user, newMenuItem);
        }
        return newMenuItem;
    }

    /**
     * 获取系统默认的CRM菜单
     */
    @Override
    public IObjectData findDefaultCrmMenu(User user) {
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        searchTemplateQueryExt.setLimit(1);
        searchTemplateQueryExt.addFilter(Operator.EQ, MenuConstants.MenuField.ISSTYTEM.getApiName(), "true");
        QueryResult<IObjectData> dataQueryResult = metaDataFindService.findBySearchQuery(user, MenuConstants.MENU_API_NAME, (SearchTemplateQuery) searchTemplateQueryExt.getQuery());
        return CollectionUtils.isNotEmpty(dataQueryResult.getData()) ? dataQueryResult.getData().get(0) : null;
    }

    /**
     * 根据对象apiname获取当前的菜单项
     */
    @Override
    public List<IObjectData> findMenuItemByApiName(User user, String menuId, List<String> apiNames) {
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        searchTemplateQueryExt.setLimit(apiNames.size());
        searchTemplateQueryExt.addFilter(Operator.EQ, MenuConstants.MenuItemField.MENUID.getApiName(), menuId);
        searchTemplateQueryExt.addFilter(Operator.IN, MenuConstants.MenuItemField.REFERENCEAPINAME.getApiName(), apiNames);
        QueryResult<IObjectData> dataQueryResult = metaDataFindService.findBySearchQuery(user, MenuConstants.MENU_ITEM_API_NAME, (SearchTemplateQuery) searchTemplateQueryExt.getQuery());
        return dataQueryResult.getData();
    }

    /**
     * 获取最后一次插入的CRM菜单项
     */
    @Override
    public IObjectData findLastCrmMenuItem(User user, String menuId) {
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        searchTemplateQueryExt.setLimit(1);
        searchTemplateQueryExt.addFilter(Operator.EQ, MenuConstants.MenuItemField.MENUID.getApiName(), menuId);
        searchTemplateQueryExt.setOrders(Lists.newArrayList(new OrderBy(MenuConstants.MenuItemField.NUMBER.getApiName(), false)));
        QueryResult<IObjectData> dataQueryResult = metaDataFindService.findBySearchQuery(user, MenuConstants.MENU_ITEM_API_NAME, (SearchTemplateQuery) searchTemplateQueryExt.getQuery());
        return CollectionUtils.isNotEmpty(dataQueryResult.getData()) ? dataQueryResult.getData().get(0) : null;
    }

    @Override
    public IObjectData buildMenuItem(User user, String menuId, IObjectDescribe menuItemDescribe, String objectApiName) {
        ObjectData objectData = new ObjectData();
        objectData.set(MenuConstants.MenuItemField.DEFINETYPE.getApiName(), MenuConstants.MenuItemField.DEFINETYPE_STYSTEM.getApiName());
        objectData.set(MenuConstants.MenuItemField.REFERENCEAPINAME.getApiName(), objectApiName);
        objectData.set(MenuConstants.MenuItemField.MENUID.getApiName(), menuId);
        objectData.set(MenuConstants.MenuItemField.TYPE.getApiName(),MenuConstants.MenuItemField.TYPE_MENU.getApiName());
        objectData.set(MenuConstants.MenuItemField.ISHIDDEN.getApiName(),Boolean.FALSE);
        MenuItemConfigObject menuConfig = MenuConstants.configMenuItemMap.get(objectApiName);
        if (menuConfig != null) {
            //配置中菜单对象的类型
            objectData.set(MenuConstants.MenuItemField.ITEMTYPE.getApiName(), menuConfig.getItemType());
        } else {
            objectData.set(MenuConstants.MenuItemField.ITEMTYPE.getApiName(), MenuConstants.MenuItemField.ITEMTYPE_UDOBJ.getApiName());
        }
        objectData.setTenantId(user.getTenantId());
        objectData.setDescribeId(menuItemDescribe.getId());
        objectData.setDescribeApiName(menuItemDescribe.getApiName());
        return objectData;
    }
}
