package com.facishare.paas.appframework.core.predef.action

import com.facishare.paas.appframework.common.util.CollectionUtils
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.metadata.dto.ImportReferenceMapping
import com.facishare.paas.metadata.api.data.IUniqueRule
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.describe.ObjectReferenceManyFieldDescribe
import com.facishare.paas.metadata.impl.describe.*
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.release.FsGrayReleaseBiz
import com.google.common.collect.Lists
import org.powermock.reflect.Whitebox
import spock.lang.Shared
import spock.lang.Specification
import java.lang.reflect.Field

class BaseImportTemplateActionTest extends Specification {
    @Shared
    def objectApiName = "object_123__c"
    def actionCode = "BaseImportTemplate"
    def tenantId = "74255"
    def userId = "1000"
    def outTenantId = "200074255"
    def outUserId = "100018916"
    def user = User.builder().tenantId(tenantId).userId(userId).outTenantId(outTenantId).outUserId(outUserId).build()
    def requestContext = RequestContext.builder().tenantId(tenantId).user(user).build()
    def actionContext = new ActionContext(requestContext, objectApiName, actionCode)

    ServiceFacade serviceFacade
    IObjectDescribe objectDescribe
    InfraServiceFacade infraServiceFacade
    SpringBeanHolder springBeanHolder
    List<IFieldDescribe> fieldDescribeList = Lists.newArrayList()

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def setup() {
        serviceFacade = Mock(ServiceFacade)
        infraServiceFacade = Mock(InfraServiceFacade)
        springBeanHolder = Mock(SpringBeanHolder)
        infraServiceFacade.getSpringBeanHolder() >> springBeanHolder
        objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        objectDescribe.setIsActive(true)

        TextFieldDescribe textFieldDescribe = new TextFieldDescribe()
        textFieldDescribe.setApiName("name")
        fieldDescribeList.add(textFieldDescribe)

        MasterDetailFieldDescribe masterDetailFieldDescribe = new MasterDetailFieldDescribe()
        masterDetailFieldDescribe.setApiName("field_md__c")
        fieldDescribeList.add(masterDetailFieldDescribe)

        RecordTypeFieldDescribe recordTypeFieldDescribe = new RecordTypeFieldDescribe()
        recordTypeFieldDescribe.setApiName("record_type")
        fieldDescribeList.add(recordTypeFieldDescribe)

        SelectOneFieldDescribe selectOneFieldDescribe = new SelectOneFieldDescribe()
        selectOneFieldDescribe.setApiName("select_one")
        selectOneFieldDescribe.setDefineType("package")
        fieldDescribeList.add(selectOneFieldDescribe)

        PercentileFieldDescribe percentileFieldDescribe = new PercentileFieldDescribe()
        percentileFieldDescribe.setApiName("percentile__c")
        fieldDescribeList.add(percentileFieldDescribe)

        ObjectReferenceFieldDescribe objectReferenceFieldDescribe = new ObjectReferenceFieldDescribe()
        objectReferenceFieldDescribe.setApiName("field_reference__c")
        objectReferenceFieldDescribe.setTargetApiName(objectApiName)
        fieldDescribeList.add(objectReferenceFieldDescribe)

        ObjectReferenceManyFieldDescribe objectReferenceManyFieldDescribe = new ObjectReferenceManyFieldDescribe()
        objectReferenceManyFieldDescribe.setApiName("field_reference_many__c")
        objectReferenceManyFieldDescribe.setTargetApiName(objectApiName)
        fieldDescribeList.add(objectReferenceManyFieldDescribe)

        IFieldDescribe imageFieldDescribe = new ImageFieldDescribe()
        imageFieldDescribe.setApiName("field_image__c")
        imageFieldDescribe.setIsWaterMark(true)
        fieldDescribeList.add(imageFieldDescribe)

        IFieldDescribe fileAttachmentFieldDescribe = new FileAttachmentFieldDescribe()
        fileAttachmentFieldDescribe.setApiName("field_file__c")
        fieldDescribeList.add(fileAttachmentFieldDescribe)

        IFieldDescribe numberFieldDescribe = new NumberFieldDescribe()
        numberFieldDescribe.setApiName("field_number__c")
        fieldDescribeList.add(numberFieldDescribe)

        IFieldDescribe currencyFieldDescribe = new CurrencyFieldDescribe()
        currencyFieldDescribe.setApiName("field_currency__c")
        fieldDescribeList.add(currencyFieldDescribe)

        IFieldDescribe dateFieldDescribe = new DateFieldDescribe()
        dateFieldDescribe.setApiName("field_date__c")
        fieldDescribeList.add(dateFieldDescribe)

        IFieldDescribe dateTimeFieldDescribe = new DateTimeFieldDescribe()
        dateTimeFieldDescribe.setApiName("field_dateTime__c")
        fieldDescribeList.add(dateTimeFieldDescribe)

        IFieldDescribe timeFieldDescribe = new TimeFieldDescribe()
        timeFieldDescribe.setApiName("field_time__c")
        fieldDescribeList.add(timeFieldDescribe)

        IFieldDescribe autoNumberFieldDescribe = new AutoNumberFieldDescribe()
        autoNumberFieldDescribe.setApiName("field_auto__c")
        fieldDescribeList.add(autoNumberFieldDescribe)

        IFieldDescribe longTextFieldDescribe = new LongTextFieldDescribe()
        longTextFieldDescribe.setApiName("field_longtext__c")
        fieldDescribeList.add(longTextFieldDescribe)

        IFieldDescribe selectManyFieldDescribe = new SelectManyFieldDescribe()
        selectManyFieldDescribe.setApiName("field_selectMany__c")
        fieldDescribeList.add(selectManyFieldDescribe)

        IFieldDescribe phoneNumberFieldDescribe = new PhoneNumberFieldDescribe()
        phoneNumberFieldDescribe.setApiName("field_phoneNumber__c")
        fieldDescribeList.add(phoneNumberFieldDescribe)

        IFieldDescribe employeeFieldDescribe = new EmployeeFieldDescribe()
        employeeFieldDescribe.setApiName("field_employee__c")
        fieldDescribeList.add(employeeFieldDescribe)

        IFieldDescribe employeeManyFieldDescribe = new EmployeeManyFieldDescribe()
        employeeManyFieldDescribe.setApiName("field_employeeMany__c")
        fieldDescribeList.add(employeeManyFieldDescribe)

        IFieldDescribe departmentFieldDescribe = new DepartmentFieldDescribe()
        departmentFieldDescribe.setApiName("field_dept__c")
        fieldDescribeList.add(departmentFieldDescribe)

        IFieldDescribe departmentManyFieldDescribe = new DepartmentManyFieldDescribe()
        departmentManyFieldDescribe.setApiName("field_employeeMany__c")
        fieldDescribeList.add(departmentManyFieldDescribe)

        IFieldDescribe emailFieldDescribe = new EmailFieldDescribe()
        emailFieldDescribe.setApiName("field_email__c")
        fieldDescribeList.add(emailFieldDescribe)

        IFieldDescribe countryFieldDescribe = new CountryFieldDescribe()
        countryFieldDescribe.setApiName("field_country__c")
        fieldDescribeList.add(countryFieldDescribe)

        IFieldDescribe provinceFieldDescribe = new ProvinceFieldDescribe()
        provinceFieldDescribe.setApiName("field_province__c")
        fieldDescribeList.add(provinceFieldDescribe)

        IFieldDescribe cityFiledDescribe = new CityFiledDescribe()
        cityFiledDescribe.setApiName("field_city__c")
        fieldDescribeList.add(cityFiledDescribe)

        IFieldDescribe districtFieldDescribe = new DistrictFieldDescribe()
        districtFieldDescribe.setApiName("field_district__c")
        fieldDescribeList.add(districtFieldDescribe)

        IFieldDescribe townFieldDescribe = new TownFieldDescribe()
        townFieldDescribe.setApiName("field_town__c")
        fieldDescribeList.add(townFieldDescribe)

        IFieldDescribe villageFieldDescribe = new VillageFieldDescribe()
        villageFieldDescribe.setApiName("field_village__c")
        fieldDescribeList.add(villageFieldDescribe)

        IFieldDescribe dimensionFieldDescribe = new DimensionFieldDescribe()
        dimensionFieldDescribe.setApiName("field_dimension__c")
        fieldDescribeList.add(dimensionFieldDescribe)

        IFieldDescribe booleanFieldDescribe = new BooleanFieldDescribe()
        booleanFieldDescribe.setApiName("field_boolean__c")
        fieldDescribeList.add(booleanFieldDescribe)

        IFieldDescribe uRLFieldDescribe = new URLFieldDescribe()
        uRLFieldDescribe.setApiName("field_url__c")
        fieldDescribeList.add(uRLFieldDescribe)

        IFieldDescribe locationFieldDescribe = new LocationFieldDescribe()
        locationFieldDescribe.setApiName("field_location__c")
        fieldDescribeList.add(locationFieldDescribe)

        IFieldDescribe htmlRichTextFieldDescribe = new HtmlRichTextFieldDescribe()
        htmlRichTextFieldDescribe.setApiName("field_richtext__c")
        fieldDescribeList.add(htmlRichTextFieldDescribe)

        objectDescribe.setFieldDescribes(fieldDescribeList)
    }

    def "StandardInsertImportTemplateAction getImportTemplateField"() {
        given:
        BaseImportTemplateAction.Arg arg = new BaseImportTemplateAction.Arg()

        StandardInsertImportTemplateAction action = new StandardInsertImportTemplateAction(
                serviceFacade: serviceFacade,
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade
        )
        when:
        action.getImportTemplateField(arg)
        then:
        noExceptionThrown()
    }


    def "test BaseImportTemplateAction doAct"() {
        given:
        BaseImportTemplateAction.Arg arg = new BaseImportTemplateAction.Arg(
                matchingType: matchType,
                detailArg: []
        )
        StandardInsertImportTemplateAction action = new StandardInsertImportTemplateAction(
                serviceFacade: serviceFacade,
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                arg: arg
        )
        when:
        infraServiceFacade.getTemplateField(actionContext.getUser(), objectDescribe, arg.getRecordType()) >> fieldDescribeList
        serviceFacade.findValidRecordTypeList(objectDescribe.getApiName(), actionContext.getUser()) >> []
        serviceFacade.findObject(actionContext.getTenantId(), actionContext.getObjectApiName()) >> objectDescribe
        infraServiceFacade.findImportReferenceMapping(actionContext.getUser(), objectDescribe.getApiName()) >> new ImportReferenceMapping()
        action.doAct(arg)
        then:
        noExceptionThrown()
        where:
        matchType | _
        1         | _
        2         | _
    }


    def "test StandardUpdateImportTemplateAction before"() {
        given:
        BaseImportTemplateAction.Arg arg = new BaseImportTemplateAction.Arg(
                matchingType: matchType,
                detailArg: []
        )
        StandardUpdateImportTemplateAction action = new StandardUpdateImportTemplateAction(
                serviceFacade: serviceFacade,
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                arg: arg
        )
        when:
        serviceFacade.findObject(actionContext.getTenantId(), actionContext.getObjectApiName()) >> objectDescribe
        infraServiceFacade.findImportReferenceMapping(actionContext.getUser(), objectDescribe.getApiName()) >> new ImportReferenceMapping()
        action.before(arg)
        then:
        noExceptionThrown()
        where:
        matchType | _
        3         | _
        2         | _
    }

    def "test StandardUpdateImportTemplateAction getImportTemplateField"() {
        given:

        BaseImportTemplateAction.Arg arg = new BaseImportTemplateAction.Arg()

        StandardUpdateImportTemplateAction action = new StandardUpdateImportTemplateAction(
                serviceFacade: serviceFacade,
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade
        )
        when:
        action.getImportTemplateField(arg)
        then:
        noExceptionThrown()
    }

    def "test StandardUpdateImportTemplateAction handelUniqueRuleFields"() {
        given:
        BaseImportTemplateAction.Arg arg = new BaseImportTemplateAction.Arg(
                matchingType: matchType
        )

        StandardUpdateImportTemplateAction action = new StandardUpdateImportTemplateAction(
                serviceFacade: serviceFacade,
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                arg: arg
        )
        Whitebox.setInternalState(action, "uniqueRule", new IUniqueRule())
        when:
        action.handelUniqueRuleFields(fieldDescribeList)
        then:
        noExceptionThrown()
        where:
        matchType | _
        3         | _
        2         | _
    }


    def "test BaseImportTemplateAction sortHeader"() {
        given:
        List<BaseImportTemplateAction.DetailArg> detailInfoList = Lists.newArrayList()
        BaseImportTemplateAction.DetailArg detailArg = new BaseImportTemplateAction.DetailArg()
        detailInfoList.add(detailArg)

        BaseImportTemplateAction.Arg arg = new BaseImportTemplateAction.Arg(
                recordType: recordType,
                detailArg: detailInfoList
        )

        StandardInsertImportTemplateAction action = new StandardInsertImportTemplateAction(
                serviceFacade: serviceFacade,
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                arg: arg
        )
        when:
        CollectionUtils.nullToEmpty(fieldDescribeList).get(0).setDescribeApiName(describeApiName)
        action.sortHeader(fieldDescribeList)
        then:
        noExceptionThrown()
        where:
        recordType   | describeApiName
        "default__c" | objectApiName
        ""          | "123"
        ""          | "456"
    }
}
