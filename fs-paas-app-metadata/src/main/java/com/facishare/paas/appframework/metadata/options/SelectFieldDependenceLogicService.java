package com.facishare.paas.appframework.metadata.options;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.options.bo.SelectFieldDependence;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;

import java.util.List;
import java.util.Optional;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/7/29
 */
public interface SelectFieldDependenceLogicService {
    /**
     * 字段依赖关系主要从描述上获取,不需要单独查询依赖关系表
     *
     * @param user
     * @param objectDescribeApiName
     * @return
     */
    List<SelectFieldDependence> findFieldDependenceWitchObjectApiName(User user, String objectDescribeApiName);

    /**
     * 选项的字段依赖关系主要保存在两个地方
     * 1,父子字段没有使用选项集,依赖关系保存在描述上
     * 2,父子字段有使用选项集,依赖关系保存在依赖关系表中
     *
     * @param user
     * @param objectDescribeApiName
     * @param fieldApiName
     * @param childFieldName
     * @return
     */
    Optional<SelectFieldDependence> find(User user, String objectDescribeApiName, String fieldApiName, String childFieldName);

    void create(User user, SelectFieldDependence selectFieldDependence);

    void copy(User user, IObjectDescribe objectDescribe, SelectFieldDependence selectFieldDependence);

    void update(User user, SelectFieldDependence selectFieldDependence);

    void deleted(User user, String objectDescribeApiName, String fieldApiName, String childFieldName);

    List<SelectOne> findChildFields(User user, String objectDescribeApiName, String fieldApiName);

    List<SelectFieldDependence> findAll(User user, String objectDescribeApiName);
}
