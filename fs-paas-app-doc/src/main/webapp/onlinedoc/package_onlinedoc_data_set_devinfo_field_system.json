{"basicFilterCondition": [{"fieldName": "is_deleted", "operator": "IN", "order": 2, "values": ["0"]}], "deleteOriginalData": false, "fieldDescribe": [{"fieldBizType": "TenantId", "fieldType": "String", "name": "tenant_id", "order": 1}, {"fieldBizType": "ApiName", "fieldType": "String", "name": "plugin_api_name", "order": 2}, {"fieldType": "String", "name": "define_type", "order": 3}, {"fieldType": "String", "name": "app_type", "order": 4}, {"fieldBizType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldBizValue": "{\"value\": \"false\"}", "fieldType": "Boolean", "name": "is_binding", "order": 2147483647}, {"fieldBizType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldBizValue": "{\"value\": \"false\"}", "fieldType": "Boolean", "name": "is_active", "order": 2147483647}, {"fieldBizType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldBizValue": "{\"value\": \"{}\"}", "fieldType": "String", "name": "dev_info", "order": 2147483647}, {"fieldType": "String", "name": "id", "order": 2147483647}, {"fieldType": "String", "name": "is_deleted", "order": 2147483647}], "inBoundCheck": {"carefulChecks": [], "checkTableDescriptors": [], "dependencyTableDescriptors": [], "unchangeableField": [], "uniqueIndices": []}, "logicDeleteFieldValue": {}, "order": 5, "principalIds": [308, 5396, 7618], "processorDescribe": {"inBoundTag": "PG", "outBoundTag": "PG", "selectHandlerType": {"configName": "dev_package_plugin", "handlerType": "PG"}}, "slaveTableDescribe": [], "tableId": "dev_onlinedoc_plugin_devinfo_field_system", "tableName": "dev_package_plugin", "uniqueKeys": ["tenant_id", "plugin_api_name"]}