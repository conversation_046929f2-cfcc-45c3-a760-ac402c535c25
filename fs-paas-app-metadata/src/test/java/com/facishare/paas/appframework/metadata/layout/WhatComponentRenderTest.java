package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.WhatComponentExt;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class WhatComponentRenderTest {

  @Mock
  private User user;

  @Mock
  private ObjectDescribeExt describeExt;

  @Mock
  private ObjectDescribeExt whatDescribeExt;

  @Mock
  private WhatComponentExt whatComponentExt;

  @Mock
  private FunctionPrivilegeService functionPrivilegeService;

  @Mock
  private IFieldDescribe fieldDescribe;

  @Mock
  private ITableColumn tableColumn;

  private List<IFieldDescribe> mockFieldDescribes;
  private List<ITableColumn> mockIncludeFields;
  private Set<String> mockUnauthorizedFields;

  @BeforeEach
  void setUp() {
    mockFieldDescribes = Lists.newArrayList();
    mockFieldDescribes.add(fieldDescribe);

    mockIncludeFields = Lists.newArrayList();
    mockIncludeFields.add(tableColumn);

    mockUnauthorizedFields = Sets.newHashSet("unauthorized_field");

    // 设置基本的mock行为
    when(user.getTenantId()).thenReturn("test_tenant");
    when(describeExt.getApiName()).thenReturn("test_object");
    when(whatDescribeExt.getApiName()).thenReturn("what_object");
    when(whatComponentExt.getIncludeFields()).thenReturn(mockIncludeFields);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试WhatComponentRender构造函数正常场景
   */
  @Test
  @DisplayName("正常场景 - 构造WhatComponentRender对象")
  void testWhatComponentRenderConstructor_Success() {
    // 执行被测试方法
    WhatComponentRender render = WhatComponentRender.builder()
        .user(user)
        .describeExt(describeExt)
        .whatDescribeExt(whatDescribeExt)
        .whatComponentExt(whatComponentExt)
        .functionPrivilegeService(functionPrivilegeService)
        .build();

    // 验证结果
    assertNotNull(render);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试render方法正常场景
   */
  @Test
  @DisplayName("正常场景 - 执行What组件渲染")
  void testRender_Success() {
    // 准备测试数据
    when(describeExt.stream()).thenReturn(mockFieldDescribes.stream());
    when(whatDescribeExt.stream()).thenReturn(mockFieldDescribes.stream());
    when(fieldDescribe.isActive()).thenReturn(true);
    when(fieldDescribe.getApiName()).thenReturn("test_field");
    when(fieldDescribe.isAbstract()).thenReturn(false);
    when(describeExt.containsField("test_field")).thenReturn(true);
    when(whatDescribeExt.containsField("test_field")).thenReturn(true);
    when(describeExt.isSFAObject()).thenReturn(false);
    when(whatDescribeExt.isSFAObject()).thenReturn(false);
    when(functionPrivilegeService.getUnauthorizedFields(user, "test_object")).thenReturn(mockUnauthorizedFields);
    when(functionPrivilegeService.getUnauthorizedFields(user, "what_object")).thenReturn(mockUnauthorizedFields);

    // 执行被测试方法
    WhatComponentRender render = WhatComponentRender.builder()
        .user(user)
        .describeExt(describeExt)
        .whatDescribeExt(whatDescribeExt)
        .whatComponentExt(whatComponentExt)
        .functionPrivilegeService(functionPrivilegeService)
        .build();

    assertDoesNotThrow(() -> {
      render.render();
    });

    // 验证方法调用
    verify(whatComponentExt).adjustFieldRenderType("test_object");
    verify(whatComponentExt).adjustFieldRenderType("what_object");
    verify(whatComponentExt).setDefaultFieldListIfEmpty();
    verify(whatComponentExt).correctLabel(describeExt, whatDescribeExt);
    verify(whatComponentExt, atLeastOnce()).removeFields(any(Set.class));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试filterUnauthorizedFieldsByRole方法
   */
  @Test
  @DisplayName("正常场景 - 按角色过滤未授权字段")
  void testFilterUnauthorizedFieldsByRole_Success() {
    // 准备测试数据
    String roleCode = "test_role";
    when(functionPrivilegeService.getUnauthorizedFieldsByRole(user, roleCode, "test_object")).thenReturn(mockUnauthorizedFields);

    // 执行被测试方法
    WhatComponentRender render = WhatComponentRender.builder()
        .user(user)
        .describeExt(describeExt)
        .whatDescribeExt(whatDescribeExt)
        .whatComponentExt(whatComponentExt)
        .functionPrivilegeService(functionPrivilegeService)
        .build();

    assertDoesNotThrow(() -> {
      render.filterUnauthorizedFieldsByRole(roleCode);
    });

    // 验证方法调用
    verify(functionPrivilegeService).getUnauthorizedFieldsByRole(user, roleCode, "test_object");
    verify(whatComponentExt).removeFields(mockUnauthorizedFields);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeInactiveFields方法
   */
  @Test
  @DisplayName("正常场景 - 移除非活跃字段")
  void testRemoveInactiveFields_Success() {
    // 准备测试数据
    when(describeExt.stream()).thenReturn(mockFieldDescribes.stream());
    when(whatDescribeExt.stream()).thenReturn(mockFieldDescribes.stream());
    when(fieldDescribe.isActive()).thenReturn(false);
    when(fieldDescribe.getApiName()).thenReturn("inactive_field");
    when(fieldDescribe.isAbstract()).thenReturn(false);
    when(describeExt.containsField("inactive_field")).thenReturn(false);
    when(whatDescribeExt.containsField("inactive_field")).thenReturn(false);

    // 执行被测试方法
    WhatComponentRender render = WhatComponentRender.builder()
        .user(user)
        .describeExt(describeExt)
        .whatDescribeExt(whatDescribeExt)
        .whatComponentExt(whatComponentExt)
        .functionPrivilegeService(functionPrivilegeService)
        .build();

    assertDoesNotThrow(() -> {
      render.removeInactiveFields();
    });

    // 验证方法调用
    verify(whatComponentExt).removeFields(any(Set.class));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试处理抽象字段
   */
  @Test
  @DisplayName("正常场景 - 处理抽象字段")
  void testRender_WithAbstractFields() {
    // 准备测试数据
    when(describeExt.stream()).thenReturn(mockFieldDescribes.stream());
    when(whatDescribeExt.stream()).thenReturn(mockFieldDescribes.stream());
    when(fieldDescribe.isActive()).thenReturn(true);
    when(fieldDescribe.getApiName()).thenReturn("abstract_field");
    when(fieldDescribe.isAbstract()).thenReturn(true);
    when(describeExt.containsField("abstract_field")).thenReturn(true);
    when(whatDescribeExt.containsField("abstract_field")).thenReturn(true);
    when(describeExt.isSFAObject()).thenReturn(false);
    when(whatDescribeExt.isSFAObject()).thenReturn(false);
    when(functionPrivilegeService.getUnauthorizedFields(user, "test_object")).thenReturn(Sets.newHashSet());
    when(functionPrivilegeService.getUnauthorizedFields(user, "what_object")).thenReturn(Sets.newHashSet());

    // 执行被测试方法
    WhatComponentRender render = WhatComponentRender.builder()
        .user(user)
        .describeExt(describeExt)
        .whatDescribeExt(whatDescribeExt)
        .whatComponentExt(whatComponentExt)
        .functionPrivilegeService(functionPrivilegeService)
        .build();

    assertDoesNotThrow(() -> {
      render.render();
    });

    // 验证方法调用
    verify(whatComponentExt, atLeastOnce()).removeFields(any(Set.class));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试SFA对象处理
   */
  @Test
  @DisplayName("正常场景 - SFA对象处理")
  void testRender_SFAObject() {
    // 准备测试数据
    when(describeExt.stream()).thenReturn(mockFieldDescribes.stream());
    when(whatDescribeExt.stream()).thenReturn(mockFieldDescribes.stream());
    when(fieldDescribe.isActive()).thenReturn(true);
    when(fieldDescribe.getApiName()).thenReturn("test_field");
    when(fieldDescribe.isAbstract()).thenReturn(false);
    when(describeExt.containsField("test_field")).thenReturn(true);
    when(whatDescribeExt.containsField("test_field")).thenReturn(true);
    when(describeExt.isSFAObject()).thenReturn(true);
    when(whatDescribeExt.isSFAObject()).thenReturn(true);
    when(functionPrivilegeService.getUnauthorizedFields(user, "test_object")).thenReturn(Sets.newHashSet());
    when(functionPrivilegeService.getUnauthorizedFields(user, "what_object")).thenReturn(Sets.newHashSet());

    // 执行被测试方法
    WhatComponentRender render = WhatComponentRender.builder()
        .user(user)
        .describeExt(describeExt)
        .whatDescribeExt(whatDescribeExt)
        .whatComponentExt(whatComponentExt)
        .functionPrivilegeService(functionPrivilegeService)
        .build();

    assertDoesNotThrow(() -> {
      render.render();
    });

    // 验证方法调用
    verify(describeExt).isSFAObject();
    verify(whatDescribeExt).isSFAObject();
    verify(whatComponentExt, atLeastOnce()).removeFields(any(Set.class));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试无权限服务的场景
   */
  @Test
  @DisplayName("边界场景 - 无权限服务")
  void testRender_NoFunctionPrivilegeService() {
    // 准备测试数据
    when(describeExt.stream()).thenReturn(mockFieldDescribes.stream());
    when(whatDescribeExt.stream()).thenReturn(mockFieldDescribes.stream());
    when(fieldDescribe.isActive()).thenReturn(true);
    when(fieldDescribe.getApiName()).thenReturn("test_field");
    when(fieldDescribe.isAbstract()).thenReturn(false);
    when(describeExt.containsField("test_field")).thenReturn(true);
    when(whatDescribeExt.containsField("test_field")).thenReturn(true);
    when(describeExt.isSFAObject()).thenReturn(false);
    when(whatDescribeExt.isSFAObject()).thenReturn(false);

    // 执行被测试方法
    WhatComponentRender render = WhatComponentRender.builder()
        .user(user)
        .describeExt(describeExt)
        .whatDescribeExt(whatDescribeExt)
        .whatComponentExt(whatComponentExt)
        .functionPrivilegeService(null)
        .build();

    assertDoesNotThrow(() -> {
      render.render();
    });

    // 验证方法调用
    verify(whatComponentExt).adjustFieldRenderType("test_object");
    verify(whatComponentExt).adjustFieldRenderType("what_object");
    verify(whatComponentExt).setDefaultFieldListIfEmpty();
    verify(whatComponentExt).correctLabel(describeExt, whatDescribeExt);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试Builder模式的完整性
   */
  @Test
  @DisplayName("正常场景 - 测试Builder模式完整性")
  void testWhatComponentRender_BuilderPattern() {
    // 测试Builder的链式调用
    assertDoesNotThrow(() -> {
      WhatComponentRender render = WhatComponentRender.builder()
          .user(user)
          .describeExt(describeExt)
          .whatDescribeExt(whatDescribeExt)
          .whatComponentExt(whatComponentExt)
          .functionPrivilegeService(functionPrivilegeService)
          .build();
      
      assertNotNull(render);
    });
  }
}
