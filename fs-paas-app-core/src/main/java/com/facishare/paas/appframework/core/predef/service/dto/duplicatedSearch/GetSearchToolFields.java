package com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface GetSearchToolFields {
    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Arg {
        String describeApiName;
        String duplicateRuleApiName;
    }

    @Data
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Result {
        private List<Map> fieldDescribes;
    }
}
