/**
 * <AUTHOR>
 * @codeName OnlineDocWpsEnterprise
 * @description 新建
 * @createTime 2024-08-05
 */

import java.text.SimpleDateFormat

class OnlineDocWpsEnterprise implements OnlineDocPersonalPlugin {

    private static String CONTENT_TYPE = "application/json;charset=utf-8"

    //
    private static String HOST = "https://openapi.wps.cn"
    //获取company_token
    private static String GET_COMPANY_TOKEN_URL = "/oauthapi/v3/inner/company/token?"
    //获取普通团队列表
    private static String QUERY_GROUP_URL = "/drive/v1/corpgroups?"
    //用户授权url
    private static String AUTH_CODE_URL = "/oauthapi/v2/authorize?response_type=code&autologin=false&scope=corp_files_synerg_mgr,admin_corp_groups.all"
    //获取token url
    private static String QUERY_ACCESS_TOKEN_URL = "/oauthapi/v2/token?"
    //刷新token url
    private static String REFRESH_ACCESS_TOKEN_URL = "/oauthapi/v2/token/refresh?"
    //获取文件列表url
    private static String QUERY_FILE_LIST_URL = "/drive/v2/corpgroups/{group_id}/files?"
    //获取预览url
    private static String PREVIEW_URL = "/drive/v2/corpgroups/{group_id}/files/{file_id}/share_link?"
    //授权回调地址后缀
    private static String CALLBACK_URL_SUFFIX = "/wps/authRedirect"


    //保存token信息到本地缓存,wps默认24小时
    private void saveAccessToken2Cache(String tenantId, String userId, String pluginApiName, Map<String, Object> runtimeData, int expiresSecond) {
        Map tokenMap = [:]
        tokenMap.putAll(runtimeData)
        tokenMap.put("token_expires_millis", DateTime.now().toTimestamp() + (expiresSecond - 30) * 1000)

        String key = "OnlineDocWpsEnAccess_token_" + userId + "_" + pluginApiName
        Cache cache = Fx.cache.getDefaultCache()
        String value = Fx.json.toJson(tokenMap)
        cache.put(key, value, expiresSecond)
        log.info("saveAccessToken2Cache key:" + key)
    }

    //获取token信息从本地缓存中
    private String getAccessTokenFromCache(String tenantId, String userId, String pluginApiName) {
        String key = "OnlineDocWpsEnAccess_token_" + userId + "_" + pluginApiName
        Cache cache = Fx.cache.getDefaultCache()
        String value = cache.get(key) as String
        if (value == null) {
            log.info("getAccessTokenFromCache key:" + key + " value:null")
            return null
        }
        Map tokenMap = Fx.json.parse(value)
        String token = tokenMap["access_token"] as String
        Long expired = tokenMap["token_expires_millis"] as Long
        if (expired <= DateTime.now().toTimestamp()) {
            log.info("getAccessTokenFromCache key:" + key + " value:expired")
            return null
        }
        log.info("getAccessTokenFromCache key:" + key)
        return token
    }

    //保存companyToken到缓存
    private void saveCompanyToken2Cache(String pluginApiName, Map<String, Object> runtimeData, int expiresSecond) {
        Map tokenMap = [:]
        tokenMap.putAll(runtimeData)
        tokenMap.put("token_expires_millis", DateTime.now().toTimestamp() + (expiresSecond - 30) * 1000)

        String key = "OnlineDocWpsEnCom_token_" + pluginApiName
        Cache cache = Fx.cache.getDefaultCache()
        String value = Fx.json.toJson(tokenMap)
        cache.put(key, value, expiresSecond)
        log.info("saveCompanyToken2Cache key:" + key)
    }

    //清空companyToken到缓存
    private void cleanCompanyTokenFromCache(String pluginApiName) {
        String key = "OnlineDocWpsEnCom_token_" + pluginApiName
        Fx.cache.getDefaultCache().remove(key)
        log.info("cleanCompanyTokenFromCache key:" + key)
    }

    //查询companyToken信息从本地缓存中
    private String getCompanyTokenFromCache(String pluginApiName) {
        String key = "OnlineDocWpsEnCom_token_" + pluginApiName
        Cache cache = Fx.cache.getDefaultCache()
        String value = cache.get(key) as String
        if (value == null) {
            log.info("getCompanyTokenFromCache key:" + key + " value:null")
            return null
        }
        Map tokenMap = Fx.json.parse(value)
        String token = tokenMap["company_token"] as String
        Long expired = tokenMap["token_expires_millis"] as Long
        if (expired <= DateTime.now().toTimestamp()) {
            log.info("getCompanyTokenFromCache key:" + key + " value:expired")
            return null
        }
        log.info("getCompanyTokenFromCache key:" + key)
        return token
    }

    private Map getOrRefreshCompanyToken(String pluginApiName, String appId, String appKey) {
        String companyToken = getCompanyTokenFromCache(pluginApiName)
        if (companyToken != null) {
            Map tokenMap = [:]
            tokenMap.put("errorCode", 0)
            tokenMap.put("companyToken", companyToken)
            return tokenMap
        }
        //重新获取companyToken
        Map tokenMap = queryCompanyToken(pluginApiName, appId, appKey)
        return tokenMap
    }

    //获取runtimeData
    private Map getOrRefreshAccessToken(String pluginApiName, Map devInfo, Map authRuntimeData, FunctionContext context) {
        String accessTokenInCache = getAccessTokenFromCache(context.getTenantId(), context.getUserId(), pluginApiName)
        if (accessTokenInCache != null) {
            Map<String, Object> refreshMap = [:]
            refreshMap.put("errorCode", 0)
            refreshMap.put("accessToken", accessTokenInCache)
            return refreshMap
        }
        //刷新accessToken
        Map<String, Object> refreshMap = refreshAccessToken(pluginApiName, devInfo, authRuntimeData, context)
        return refreshMap
    }

    //构建header
    private Map buildHeader(String appId, String appKey, String uri, Map body) {
        SimpleDateFormat sdf = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US)
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"))
        //date  RFC1123格式 "Wed, 03 Nov 2024 20:00:00 GMT";
        String rfc1123Date = sdf.format(new java.util.Date())
        String contentMd5 = Fx.crypto.MD5.encode((body == null || body.isEmpty()) ? "" : Fx.json.toJson(body)).toLowerCase()
        String sign = Fx.crypto.SHA1.hex(appKey + contentMd5 + uri + CONTENT_TYPE + rfc1123Date)
        String Auth = "WPS-3:" + appId + ":" + sign.toLowerCase()
        //
        Map header = [:]
        header["Content-Type"] = CONTENT_TYPE
        header["Content-Md5"] = contentMd5
        header["Date"] = rfc1123Date
        header["X-Auth"] = Auth
        return header
    }

    //企业认证 暂时未用到
    public EnterpriseAuth.Result enterpriseAuth(EnterpriseAuth.Arg arg, FunctionContext context) {
        log.info("enterpriseAuth arg:" + arg)
        String appId = arg.getDevInfo().get("appId") as String
        String appKey = arg.getDevInfo().get("appKey") as String
        String pluginApiName = arg.getPluginApiName()
        //获取company token
        Map tokenMap = queryCompanyToken(pluginApiName, appId, appKey)
        String companyToken = tokenMap["companyToken"] as String
        int errorCode = tokenMap["errorCode"] as Integer
        String errorMessage = tokenMap["errorMessage"] as String
        if (errorCode != 0) {
            Fx.message.throwException(errorMessage)
        }
        EnterpriseAuth.Result result = new EnterpriseAuth.Result()
        //result.setErrorCode(0)
        //result.setErrorMessage(errorMessage)
        log.info("enterpriseAuth result:" + result)
        return result
    }

    //请求access token
    private Map queryCompanyToken(String pluginApiName, String appId, String appKey) {
        log.info("queryCompanyToken arg:" + pluginApiName + " appId:" + appId)
        String bizUrl = GET_COMPANY_TOKEN_URL + "app_id=" + appId
        String url = HOST + bizUrl
        Map headers = buildHeader(appId, appKey, bizUrl, null)
        log.info("queryCompanyToken request begin:" + url)
        def (Boolean error, HttpResult httpResult, String errorMessage) = Fx.http.get(url, headers, 10000, false, 0)
        log.info("queryCompanyToken request finish:" + httpResult)
        //检查请求错误
        if (error || httpResult == null) {
            log.info("queryAccessToken request null error :" + errorMessage)
            Map resultMap = [:]
            resultMap.put("errorCode", -1)
            resultMap.put("errorMessage", errorMessage)
            return resultMap
        }
        Map contentMap = null
        if (httpResult.statusCode == 200) {
            contentMap = httpResult.content as Map
        } else {
            contentMap = Fx.json.parse(httpResult.content as String)
        }
        Integer bizErrorCode = contentMap.get("result") as Integer
        String bizErrorMessage = contentMap.get("msg") as String
        //检查请求业务错误
        if (bizErrorCode != 0) {
            Map resultMap = [:]
            resultMap.put("errorCode", bizErrorCode)
            resultMap.put("errorMessage", bizErrorMessage)
            log.info("queryCompanyToken request biz failed error:" + resultMap)
            return resultMap
        }
        //补充过期时间戳
        Map tokenMap = contentMap
        Integer expiresSecond = contentMap["expires_in"] as Integer
        String companyToken = contentMap["company_token"] as String

        //保存到本地缓存
        saveCompanyToken2Cache(pluginApiName, tokenMap, expiresSecond)
        //返回结果
        Map resultMap = [:]
        resultMap.put("errorCode", 0)
        resultMap.put("companyToken", companyToken)
        log.info("queryCompanyToken result success:" + resultMap.size())
        return resultMap
    }

    //刷新access token
    private Map<String, Object> refreshAccessToken(String pluginApiName, Map<String, String> devInfo, Map<String, Object> authRuntimeData, FunctionContext context) {
        log.info("refreshAccessToken arg:" + pluginApiName)
        Map authMap = authRuntimeData
        if (authMap.isEmpty()) {
            //从未授权，模拟金山错误码，下发授权url
            Map<String, Object> resultMap = [:]
            resultMap.put("errorCode", 100050)
            resultMap.put("errorMessage", "never to auth")
            log.info("refreshAccessToken failed resultMap:" + resultMap)
            return resultMap
        }
        String appId = devInfo.get("appId")
        String appKey = devInfo.get("appKey")
        String refreshToken = authMap.get("refresh_token")
        String queryRefreshTokenUrl = HOST + REFRESH_ACCESS_TOKEN_URL + "appid=" + appId + "&appkey=" + appKey + "&refresh_token=" + refreshToken
        Map headers = ["Content-Type": CONTENT_TYPE]
        Map requestBody = [:]
        log.info("refreshAccessToken request begin:" + queryRefreshTokenUrl)
        def (Boolean error, HttpResult httpResult, String errorMessage) = Fx.http.post(queryRefreshTokenUrl, headers, requestBody, 10000, false, 0)
        log.info("refreshAccessToken request finish:" + httpResult)
        //检查请求错误
        if (error || httpResult == null) {
            log.info("refreshAccessToken request null error :" + errorMessage)
            Map<String, Object> resultMap = [:]
            resultMap.put("errorCode", -1)
            resultMap.put("errorMessage", "request error:" + errorMessage)
            return resultMap
        }
        Map contentMap = null
        if (httpResult.statusCode == 200) {
            contentMap = httpResult.content as Map
        } else {
            contentMap = Fx.json.parse(httpResult.content as String)
        }
        Integer bizErrorCode = contentMap.get("result") as Integer
        String bizErrorMessage = contentMap.get("msg") as String
        //检查请求业务错误
        if (bizErrorCode != 0) {
            Map<String, Object> resultMap = [:]
            resultMap.put("errorCode", bizErrorCode)
            resultMap.put("errorMessage", bizErrorMessage)
            log.info("refreshAccessToken request biz failed error:" + contentMap)
            return resultMap
        }
        //补充过期时间戳
        Map tokenMap = contentMap["token"] as Map
        String accessToken = tokenMap["access_token"] as String
        Integer expiresSecond = tokenMap["expires_in"] as Integer
        //保存到本地缓存
        saveAccessToken2Cache(context.getTenantId(), context.getUserId(), pluginApiName, tokenMap, expiresSecond)
        //返回结果
        Map<String, Object> resultMap = [:]
        resultMap.put("errorCode", 0)
        resultMap.put("accessToken", accessToken)
        log.info("refreshAccessToken finish success:" + resultMap.size())
        return resultMap
    }

    //请求access token
    private Map<String, Object> queryAccessToken(String pluginApiName,
                                                 String appId,
                                                 String appKey,
                                                 String code,
                                                 FunctionContext context) {
        String queryTokenUrl = HOST + QUERY_ACCESS_TOKEN_URL + "appid=" + appId + "&appkey=" + appKey + "&code=" + code
        Map headers = ["Content-Type": CONTENT_TYPE]
        log.info("queryAccessToken request begin:" + queryTokenUrl)
        def (Boolean error, HttpResult httpResult, String errorMessage) = Fx.http.get(queryTokenUrl, headers, 10000, false, 0)
        log.info("queryAccessToken request finish:" + httpResult)
        //检查请求错误
        if (error || httpResult == null) {
            log.info("queryAccessToken request null error :" + errorMessage)
            Map resultMap = [:]
            resultMap["errorCode"] = -1
            resultMap["errorMessage"] = "request error:" + errorMessage
            return resultMap
        }
        Map contentMap = null
        if (httpResult.statusCode == 200) {
            contentMap = httpResult.content as Map
        } else {
            contentMap = Fx.json.parse(httpResult.content as String)
        }
        Integer bizErrorCode = contentMap.get("result") as Integer
        String bizErrorMessage = contentMap.get("msg") as String
        //检查请求业务错误
        if (bizErrorCode != 0) {
            log.info("queryAccessToken request biz failed error:" + contentMap)
            Map resultMap = [:]
            resultMap["errorCode"] = bizErrorCode
            resultMap["errorMessage"] = bizErrorMessage
            return resultMap
        }
        //补充过期时间戳
        Map tokenMap = contentMap["token"] as Map
        Integer expiresSecond = tokenMap["expires_in"] as Integer
        //保存到本地缓存
        saveAccessToken2Cache(context.getTenantId(), context.getUserId(), pluginApiName, tokenMap, expiresSecond)
        //返回结果
        Map resultMap = [:]
        resultMap["errorCode"] = 0
        resultMap["runtimeData"] = tokenMap
        resultMap["expiredTime"] = DateTime.now().toTimestamp() + 90 * 24 * 3600 * 1000L
        return resultMap
    }

    //获取个人授权url
    public GetPersonalAuthUrl.Result getPersonalAuthUrl(GetPersonalAuthUrl.Arg arg, FunctionContext context) {
        //标准日志，请勿删除
        log.info("getPersonalAuthUrl arg:" + arg)

        /*此处开始加业务*/
        String appId = arg.getDevInfo().get("appId")
        String callbackUrl = arg.getDevInfo().get("callbackUrl") + CALLBACK_URL_SUFFIX

        //拼装纷享业务参数
        Map<String, String> authMap = [:]
        authMap.put("pluginApiName", arg.getPluginApiName())
        authMap.put("enterpriseId", context.getTenantId())
        authMap.put("employeeId", context.getUserId())
        //拼接如state中
        String state = Fx.crypto.base64.encode(Fx.json.toJson(authMap) as byte[])
        String authUrl = HOST + AUTH_CODE_URL + "&appid=" + appId + "&redirect_uri=" + callbackUrl + "&state=" + state

        GetPersonalAuthUrl.Result result = new GetPersonalAuthUrl.Result()
        result.setErrorCode(0)
        result.setAuthUrl(authUrl)
        //标准日志，请勿删除
        log.info("getPersonalAuthUrl result:" + result)
        return result
    }

    //处理callback回来的数据
    public ActionCallback.Result actionCallback(ActionCallback.Arg arg, FunctionContext context) {
        //标准日志，请勿删除
        log.info("actionCallback arg:" + arg)

        /*此处开始加业务*/
        String pluginApiName = arg.getPluginApiName()
        String code = arg.getCode()
        String appId = arg.getDevInfo().get("appId")
        String appKey = arg.getDevInfo().get("appKey")
        //获取用户授权token
        Map accessTokenResultMap = queryAccessToken(pluginApiName, appId, appKey, code, context)
        Integer errorCode = accessTokenResultMap["errorCode"] as Integer
        String errorMessage = accessTokenResultMap["errorMessage"] as String
        if (errorCode != 0) {
            ActionCallback.Result result = new ActionCallback.Result()
            result.setErrorCode(errorCode)
            result.setErrorMessage(errorMessage)
            return result
        }
        ActionCallback.Result result = new ActionCallback.Result()
        result.setErrorCode(errorCode)
        result.setRuntimeData(accessTokenResultMap["runtimeData"] as Map)
        result.setExpiredTime(accessTokenResultMap["expiredTime"] as Long)
        //标准日志，请勿删除
        log.info("actionCallback result:" + result)
        return result
    }

    //请求文件列表
    public QueryFileList.Result queryFileList(QueryFileList.Arg arg, FunctionContext context) {
        //标准日志，请勿删除
        log.info("queryFileList arg:" + arg)
        String pluginApiName = arg.getPluginApiName()
        String appId = arg.getDevInfo().get("appId") as String
        String appKey = arg.getDevInfo().get("appKey") as String
        Map authRuntimeData = arg.getAuthRuntimeData()
        Map pluginRuntimeData = arg.getPluginRuntimeData()
        Integer errorCode = 0
        String errorMessage = null
        //获取or更新companyToken
        Map companyTokenMap = getOrRefreshCompanyToken(pluginApiName, appId, appKey)
        errorCode = companyTokenMap["errorCode"] as Integer
        errorMessage = companyTokenMap["errorMessage"] as String
        String companyToken = companyTokenMap["companyToken"] as String
        if (errorCode != 0) {
            //返回失败结果
            return buildQueryFileListFailedResult(pluginApiName, errorCode, errorMessage, arg.getDevInfo(), context)
        }
        //获取or更新accessToken
        Map accessTokenMap = getOrRefreshAccessToken(pluginApiName, arg.getDevInfo(), authRuntimeData, context)
        errorCode = accessTokenMap["errorCode"] as Integer
        errorMessage = accessTokenMap["errorMessage"] as String
        String accessToken = accessTokenMap["accessToken"] as String
        if (errorCode != 0) {
            //返回失败结果
            return buildQueryFileListFailedResult(arg.getPluginApiName(), errorCode, errorMessage, arg.getDevInfo(), context)
        }
        //查询团队列表
        Map<String, Object> extraInfo = (arg.getExtraInfo() == null) ? [:] : arg.getExtraInfo()
        //未选择团队，则视为在查团队
        if (arg.getGroup() == null) {
            Integer pageNumber = extraInfo.getOrDefault("groupPageNumber", 0) as Integer
            //加载下一页
            if (arg.isNeedNextPage()) {
                pageNumber += 1
            }
            Map groupListMap = queryGroups(appId, appKey, companyToken, accessToken, pageNumber)
            errorCode = groupListMap["errorCode"] as Integer
            errorMessage = groupListMap["errorMessage"] as String
            List groupList = groupListMap["groupList"] as List
            if (errorCode != 0) {
                //返回失败结果
                return buildQueryFileListFailedResult(pluginApiName, errorCode, errorMessage, arg.getDevInfo(), context)
            }
            extraInfo["groupPageNumber"] = pageNumber
            //返回结果
            QueryFileList.Result result = new QueryFileList.Result()
            result.setErrorCode(errorCode)
            result.setErrorMessage(errorMessage)
            result.setGroupList(groupList)
            result.setHasMore(groupListMap["hasMore"] as boolean)
            result.setExtraInfo(extraInfo)
            //标准日志，请勿删除
            log.info("queryFileList groups result:" + result)
            return result
        }
        //查询文件
        String groupId = arg.getGroup().getId()
        String parentId = null
        Integer offset = extraInfo.getOrDefault("next_offset", 0) as Integer
        Integer pageNumber = extraInfo.getOrDefault("pageNumber", 0) as Integer
        if (arg.isNeedNextPage()) {
            pageNumber += 1
        }
        //进入选择的文件夹
        if (arg.getFolder() != null) {
            parentId = arg.getFolder().getFileId()
        }
        //请求文件列表清单
        Map fileListMap = queryFiles(arg.getPluginApiName(), appId, appKey, companyToken, accessToken, parentId, groupId, offset, pageNumber)
        errorCode = fileListMap.get("errorCode") as Integer
        errorMessage = fileListMap.get("errorMessage") as String
        List<Map> fileList = fileListMap.get("fileList") as List
        extraInfo["next_offset"] = fileListMap["next_offset"] as Integer
        extraInfo["pageNumber"] = pageNumber
        if (errorCode != 0) {
            //返回失败结果
            return buildQueryFileListFailedResult(arg.getPluginApiName(), errorCode, errorMessage, arg.getDevInfo(), context)
        }
        //返回结果
        QueryFileList.Result result = new QueryFileList.Result()
        result.setErrorCode(errorCode)
        result.setErrorMessage(errorMessage)
        result.setFileList(fileList)
        result.setHasMore(fileListMap["hasMore"] as boolean)
        result.setExtraInfo(extraInfo)
        //标准日志，请勿删除
        log.info("queryFileList result:" + result)
        return result
    }

    private QueryFileList.Result buildQueryFileListFailedResult(String pluginApiName,
                                                                int errorCode,
                                                                String errorMessage,
                                                                Map devInfo,
                                                                FunctionContext context) {
        //以下错误需要用户重新授权
        //{"result":100013,"msg":"InvalidAccessToken"}
        //{"result":100050,"msg":"InvalidRefreshToken"}
        //{"result":10102019,"msg":"InvalidAccessToken"}
        if (errorCode == 100050 || errorCode == 100013 || errorCode == 10102019) {
            //检查是否需要授权
            GetPersonalAuthUrl.Arg authUrlArg = new GetPersonalAuthUrl.Arg()
            authUrlArg.setPluginApiName(pluginApiName)
            authUrlArg.setDevInfo(devInfo)
            GetPersonalAuthUrl.Result authUrlResult = getPersonalAuthUrl(authUrlArg, context)
            //
            QueryFileList.Result result = new QueryFileList.Result()
            result.setErrorCode(errorCode)
            result.setErrorMessage(errorMessage)
            result.setAuthUrl(authUrlResult.getAuthUrl())
            return result
        }
        //以下错误需要清空缓存
        //{"result":10102025,"msg":"InvalidCompanyToken"}
        if (errorCode == 10102025) {
            //清空companyToken
            cleanCompanyTokenFromCache(pluginApiName)
            Fx.message.throwException(errorMessage + " 请稍后重试")
        }
        Fx.message.throwException(errorMessage)
    }

    //获取预览url，wps使用
    public GetPreviewUrl.Result getPreviewUrl(GetPreviewUrl.Arg arg, FunctionContext context) {
        //标准日志，请勿删除
        log.info("getPreviewUrl arg:" + arg)
        String pluginApiName = arg.getPluginApiName()
        String appId = arg.getDevInfo().get("appId") as String
        String appKey = arg.getDevInfo().get("appKey") as String
        String groupId = arg.getGroup().getId()
        String fileId = arg.getFile().getFileId()
        /*此处开始加业务*/
        String companyToken = null
        String accessToken = null
        Integer errorCode = 0
        String errorMessage = null
        //更新companyToken
        Map companyTokenMap = getOrRefreshCompanyToken(pluginApiName, appId, appKey)
        errorCode = companyTokenMap["errorCode"] as Integer
        errorMessage = companyTokenMap["errorMessage"] as String
        companyToken = companyTokenMap["companyToken"] as String
        if (errorCode != 0) {
            Fx.message.throwException(errorMessage)
        }
        Map runtimeDataMap = getOrRefreshAccessToken(arg.getPluginApiName(), arg.getDevInfo(), arg.getAuthRuntimeData(), context)
        errorCode = runtimeDataMap.get("errorCode") as Integer
        errorMessage = runtimeDataMap.get("errorMessage") as String
        accessToken = runtimeDataMap.get("accessToken") as String
        if (errorCode != 0) {
            Fx.message.throwException(errorMessage)
        }
        String bizUrl = PREVIEW_URL.replace("{group_id}", groupId).replace("{file_id}", fileId)
        bizUrl += "company_token=" + companyToken + "&access_token=" + accessToken + "&permission=company_read"
        String getPreviewUrl = HOST + bizUrl
        Map headers = buildHeader(appId, appKey, bizUrl, null)
        log.info("getPreviewUrl request beign:" + getPreviewUrl)
        def (Boolean error, HttpResult httpResult, String httpErrorMessage) = Fx.http.get(getPreviewUrl, headers, 10000, false, 0)
        log.info("getPreviewUrl request finish:" + httpResult)
        //检查请求错误
        if (error || httpResult == null) {
            log.info("getPreviewUrl request null error :" + httpErrorMessage)
            Fx.message.throwException(httpErrorMessage)
        }
        Map contentMap = null
        if (httpResult.statusCode == 200) {
            contentMap = httpResult.content as Map
        } else {
            contentMap = Fx.json.parse(httpResult.content as String)
        }
        Integer bizErrorCode = contentMap.get("result") as Integer
        String bizErrorMessage = contentMap.get("msg") as String
        //检查请求业务错误
        if (bizErrorCode != 0) {
            log.info("getPreviewUrl request biz failed error:" + contentMap)
            Map msgMap = Fx.json.parse(bizErrorMessage)
            String msg = msgMap["msg"]
            Fx.message.throwException((msg != null) ? msg : bizErrorMessage)
        }
        //返回结果
        GetPreviewUrl.Result result = new GetPreviewUrl.Result()
        result.setErrorCode(0)
        result.setFileUrl(contentMap.get("share_link") as String)
        //标准日志，请勿删除
        log.info("getPreviewUrl result:" + result)
        return result
    }

    //查找普通团队, pageNumber从0开始
    private Map queryGroups(String appId, String appKey, String companyToken, String accessToken, int pageNumber) {
        int pageSize = 20
        String bizUrl = QUERY_GROUP_URL + "company_token=" + companyToken + "&access_token=" + accessToken + "&count=" + pageSize + "&offset=" + pageNumber * pageSize
        String url = HOST + bizUrl
        Map headers = buildHeader(appId, appKey, bizUrl, null)
        log.info("queryGroups request begin:" + url)
        def (Boolean error, HttpResult httpResult, String errorMessage) = Fx.http.get(url, headers, 10000, false, 0)
        log.info("queryGroups request finish:" + httpResult)
        //检查请求错误
        if (error || httpResult == null) {
            log.info("queryGroups request null error :" + errorMessage)
            Map resultMap = [:]
            resultMap.put("errorCode", -1)
            resultMap.put("errorMessage", errorMessage)
            return resultMap
        }
        Map contentMap = null
        if (httpResult.statusCode == 200) {
            contentMap = httpResult.content as Map
        } else {
            contentMap = Fx.json.parse(httpResult.content as String)
        }
        Integer bizErrorCode = contentMap.get("result") as Integer
        String bizErrorMessage = contentMap.get("msg") as String
        //检查请求业务错误
        if (bizErrorCode != 0) {
            log.info("queryGroups request biz failed error:" + contentMap)
            Map resultMap = [:]
            resultMap.put("errorCode", bizErrorCode)
            resultMap.put("errorMessage", bizErrorMessage)
            return resultMap
        }
        List<Map> groupList = contentMap["groups"] as List
        int nextOffset = contentMap["next_offset"] as int

        Map<String, Object> resultMap = [:]
        resultMap.put("errorCode", 0)
        resultMap.put("groupList", convert2GroupInfo(groupList))
        resultMap.put("hasMore", nextOffset > 0)
        return resultMap
    }

    //查询文件列表 pageNumber从0开始
    private Map<String, Object> queryFiles(String pluginApiName, String appId, String appKey,
                                           String companyToken, String accessToken,
                                           String parentId, String groupId,
                                           int offset, int pageNumber) {
        int pageSize = 20
        int tmpOffset = pageNumber > 0 ? pageNumber * pageSize + 1 : 0
        String bizUrl = QUERY_FILE_LIST_URL.replace("{group_id}", groupId)
        bizUrl += "access_token=" + accessToken + "&company_token=" + companyToken + "&offset=" + tmpOffset + "&count=" + pageSize
        if (parentId != null) {
            bizUrl += "&parent_id=" + parentId
        }
        String url = HOST + bizUrl
        Map headers = buildHeader(appId, appKey, bizUrl, null)
        log.info("queryFiles request begin:" + url)
        def (Boolean error, HttpResult httpResult, String errorMessage) = Fx.http.get(url, headers, 10000, false, 0)
        log.info("queryFiles request finish:" + httpResult)
        //检查请求错误
        if (error || httpResult == null) {
            log.info("queryFiles request null error :" + errorMessage)
            Map<String, Object> resultMap = [:]
            resultMap.put("errorCode", -1)
            resultMap.put("errorMessage", "request error:" + errorMessage)
            return resultMap
        }
        Map contentMap = null
        if (httpResult.statusCode == 200) {
            contentMap = httpResult.content as Map
        } else {
            contentMap = Fx.json.parse(httpResult.content as String)
        }
        Integer bizErrorCode = contentMap.get("result") as Integer
        String bizErrorMessage = contentMap.get("msg") as String
        //检查请求业务错误
        if (bizErrorCode != 0) {
            log.info("queryFiles request biz failed error:" + contentMap)
            Map<String, Object> resultMap = [:]
            resultMap.put("errorCode", bizErrorCode)
            resultMap.put("errorMessage", bizErrorMessage)
            return resultMap
        }
        List<Map> fileList = contentMap["files"] as List
        int nextOffset = contentMap["next_offset"] as int

        Map<String, Object> resultMap = [:]
        resultMap.put("errorCode", 0)
        resultMap.put("fileList", convert2FileInfo(pluginApiName, fileList))
        resultMap.put("hasMore", nextOffset > 0)
        resultMap.put("next_offset", nextOffset)
        return resultMap
    }

    //转换成规范结构
    private List<Map> convert2FileInfo(String pluginApiName, List<Map> fileList) {
        List<Map> fileInfoList = []
        fileList.each() { item ->
            Map fileInfo = [:]
            fileInfo.put("fileId", item.get("file_id"))
            fileInfo.put("fileName", item.get("file_name"))
            fileInfo.put("fileExt", findFileExtension(item.get("file_name") as String))
            fileInfo.put("fileType", convertFileType(item.get("file_type") as String))
            fileInfo.put("fileSize", item.get("file_size"))
            fileInfo.put("createTime", 1000 * (item.get("ctime") as long))
            fileInfo.put("updateTime", 1000 * (item.get("mtime") as long))
            fileInfo.put("pluginApiName", pluginApiName)
            fileInfoList.add(fileInfo)
        }
        return fileInfoList
    }

    //转换成规范结构
    private List<Map> convert2GroupInfo(List<Map> groupList) {
        List<Map> groupInfoList = []
        groupList.each() { item ->
            Map groupInfo = [:]
            groupInfo.put("id", item.get("group_id"))
            groupInfo.put("name", item.get("group_name"))
            groupInfo.put("createTime", 1000 * (item.get("ctime") as long))
            groupInfo.put("updateTime", 1000 * (item.get("mtime") as long))
            groupInfoList.add(groupInfo)
        }
        return groupInfoList
    }

    String findFileExtension(String fileName) {
        def parts = fileName.split("\\.")
        if (parts.size() > 1) {
            return parts[parts.size() - 1]
        }
        return null
    }

    //检查文件类型，只支持文件夹、文件、未知
    private String convertFileType(String fileType) {
        switch (fileType) {
            case "file":
            case "sharefile":
                return "file"
            case "folder":
            case "linkfolder":
                return "folder"
            default:
                return "unknown"
        }
    }

    static void main(String[] args) {

    }
}