package com.facishare.paas.appframework.metadata.layout.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class AbstractComponentInfoDocumentTest {

  // 创建一个具体的实现类用于测试抽象类
  private static class TestComponentInfoDocument extends AbstractComponentInfoDocument {
    public TestComponentInfoDocument() {
      super();
    }
    
    public TestComponentInfoDocument(Map map) {
      super(map);
    }
  }

  private TestComponentInfoDocument testDocument;

  @BeforeEach
  void setUp() {
    testDocument = new TestComponentInfoDocument();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试AbstractComponentInfoDocument的无参构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试无参构造函数")
  void testDefaultConstructor() {
    TestComponentInfoDocument document = new TestComponentInfoDocument();
    assertNotNull(document, "无参构造函数创建的对象不应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试AbstractComponentInfoDocument的Map构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = Maps.newHashMap();
    map.put("test_key", "test_value");
    map.put("list_key", Lists.newArrayList("item1", "item2"));
    
    TestComponentInfoDocument document = new TestComponentInfoDocument(map);
    assertNotNull(document, "Map构造函数创建的对象不应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getList方法 - 正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试getList方法")
  void testGetList_NormalScenario() {
    // 准备测试数据
    List<String> originalList = Lists.newArrayList("item1", "item2", "item3");
    Map<String, Object> map = Maps.newHashMap();
    map.put("string_list", originalList);
    
    TestComponentInfoDocument document = new TestComponentInfoDocument(map);
    
    // 测试getList方法
    List<String> result = document.getList("string_list");
    
    assertNotNull(result, "结果不应为空");
    assertEquals(3, result.size(), "列表大小应为3");
    assertTrue(result.contains("item1"), "应包含item1");
    assertTrue(result.contains("item2"), "应包含item2");
    assertTrue(result.contains("item3"), "应包含item3");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getList方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试getList方法处理null值")
  void testGetList_NullValue() {
    Map<String, Object> map = Maps.newHashMap();
    map.put("null_key", null);
    
    TestComponentInfoDocument document = new TestComponentInfoDocument(map);
    
    // 测试null值
    List<Object> result = document.getList("null_key");
    assertNotNull(result, "结果不应为空");
    assertTrue(result.isEmpty(), "null值应返回空列表");
    
    // 测试不存在的key
    List<Object> nonExistentResult = document.getList("non_existent_key");
    assertNotNull(nonExistentResult, "结果不应为空");
    assertTrue(nonExistentResult.isEmpty(), "不存在的key应返回空列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getList方法 - 字符串值场景
   */
  @Test
  @DisplayName("正常场景 - 测试getList方法处理字符串值")
  void testGetList_StringValue() {
    // 准备JSON字符串格式的列表数据
    String jsonString = "[\"string1\", \"string2\", \"string3\"]";
    Map<String, Object> map = Maps.newHashMap();
    map.put("json_string", jsonString);
    
    TestComponentInfoDocument document = new TestComponentInfoDocument(map);
    
    // 测试字符串值
    List<String> result = document.getList("json_string");
    
    assertNotNull(result, "结果不应为空");
    assertEquals(3, result.size(), "列表大小应为3");
    assertTrue(result.contains("string1"), "应包含string1");
    assertTrue(result.contains("string2"), "应包含string2");
    assertTrue(result.contains("string3"), "应包含string3");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getList方法 - 复杂对象场景
   */
  @Test
  @DisplayName("正常场景 - 测试getList方法处理复杂对象")
  void testGetList_ComplexObject() {
    // 准备复杂对象列表
    Map<String, Object> obj1 = Maps.newHashMap();
    obj1.put("id", 1);
    obj1.put("name", "object1");
    
    Map<String, Object> obj2 = Maps.newHashMap();
    obj2.put("id", 2);
    obj2.put("name", "object2");
    
    List<Map<String, Object>> originalList = Lists.newArrayList(obj1, obj2);
    
    Map<String, Object> map = Maps.newHashMap();
    map.put("object_list", originalList);
    
    TestComponentInfoDocument document = new TestComponentInfoDocument(map);
    
    // 测试复杂对象列表
    List<Map> result = document.getList("object_list");
    
    assertNotNull(result, "结果不应为空");
    assertEquals(2, result.size(), "列表大小应为2");
    
    // 验证第一个对象
    Map firstObj = result.get(0);
    assertNotNull(firstObj, "第一个对象不应为空");
    
    // 验证第二个对象
    Map secondObj = result.get(1);
    assertNotNull(secondObj, "第二个对象不应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getList方法 - 非字符串非列表对象场景
   */
  @Test
  @DisplayName("异常场景 - 测试getList方法处理非字符串非列表对象")
  void testGetList_NonStringNonListObject() {
    // 准备非字符串非列表的对象
    Map<String, Object> complexObject = Maps.newHashMap();
    complexObject.put("field1", "value1");
    complexObject.put("field2", Lists.newArrayList("item1", "item2"));

    Map<String, Object> map = Maps.newHashMap();
    map.put("complex_object", complexObject);

    TestComponentInfoDocument document = new TestComponentInfoDocument(map);

    // 测试非字符串非列表对象，预期会抛出异常
    assertThrows(Exception.class, () -> {
      document.getList("complex_object");
    }, "非列表对象转换为列表时应抛出异常");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getList方法 - 空列表场景
   */
  @Test
  @DisplayName("边界场景 - 测试getList方法处理空列表")
  void testGetList_EmptyList() {
    Map<String, Object> map = Maps.newHashMap();
    map.put("empty_list", Lists.newArrayList());
    
    TestComponentInfoDocument document = new TestComponentInfoDocument(map);
    
    List<Object> result = document.getList("empty_list");
    
    assertNotNull(result, "结果不应为空");
    assertTrue(result.isEmpty(), "空列表应返回空列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getList方法 - 空JSON字符串场景
   */
  @Test
  @DisplayName("边界场景 - 测试getList方法处理空JSON字符串")
  void testGetList_EmptyJsonString() {
    Map<String, Object> map = Maps.newHashMap();
    map.put("empty_json", "[]");
    
    TestComponentInfoDocument document = new TestComponentInfoDocument(map);
    
    List<Object> result = document.getList("empty_json");
    
    assertNotNull(result, "结果不应为空");
    assertTrue(result.isEmpty(), "空JSON数组应返回空列表");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口实现
   */
  @Test
  @DisplayName("正常场景 - 测试接口实现")
  void testInterfaceImplementation() {
    // 验证实现了JsonCompatible接口
    assertTrue(testDocument instanceof com.facishare.paas.metadata.api.JsonCompatible, 
               "应实现JsonCompatible接口");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试继承的DocumentBasedBean功能
   */
  @Test
  @DisplayName("正常场景 - 测试继承功能")
  void testInheritedFunctionality() {
    // 验证继承自DocumentBasedBean
    assertTrue(testDocument instanceof com.facishare.paas.metadata.impl.DocumentBasedBean, 
               "应继承自DocumentBasedBean");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试序列化版本号
   */
  @Test
  @DisplayName("正常场景 - 测试序列化版本号")
  void testSerialVersionUID() {
    // 验证类有序列化版本号（通过反射检查）
    try {
      java.lang.reflect.Field field = AbstractComponentInfoDocument.class.getDeclaredField("serialVersionUID");
      field.setAccessible(true);
      long serialVersionUID = field.getLong(null);
      assertEquals(-8305805134339058242L, serialVersionUID, "序列化版本号应正确");
    } catch (Exception e) {
      fail("应该有serialVersionUID字段");
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getList方法的类型安全性
   */
  @Test
  @DisplayName("正常场景 - 测试getList方法类型安全性")
  void testGetList_TypeSafety() {
    // 准备混合类型的列表
    List<Object> mixedList = Lists.newArrayList("string", 123, true, null);
    Map<String, Object> map = Maps.newHashMap();
    map.put("mixed_list", mixedList);
    
    TestComponentInfoDocument document = new TestComponentInfoDocument(map);
    
    // 测试混合类型列表
    List<Object> result = document.getList("mixed_list");
    
    assertNotNull(result, "结果不应为空");
    assertEquals(4, result.size(), "列表大小应为4");
    
    // 验证各种类型的元素都能正确处理
    assertTrue(result.contains("string"), "应包含字符串元素");
    assertTrue(result.contains(123), "应包含数字元素");
    assertTrue(result.contains(true), "应包含布尔元素");
    assertTrue(result.contains(null), "应包含null元素");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getList方法的多次调用
   */
  @Test
  @DisplayName("正常场景 - 测试getList方法多次调用")
  void testGetList_MultipleCalls() {
    List<String> originalList = Lists.newArrayList("item1", "item2");
    Map<String, Object> map = Maps.newHashMap();
    map.put("test_list", originalList);
    
    TestComponentInfoDocument document = new TestComponentInfoDocument(map);
    
    // 多次调用getList方法
    List<String> result1 = document.getList("test_list");
    List<String> result2 = document.getList("test_list");
    
    assertNotNull(result1, "第一次调用结果不应为空");
    assertNotNull(result2, "第二次调用结果不应为空");
    assertEquals(result1.size(), result2.size(), "多次调用应返回相同大小的列表");
    assertEquals(result1, result2, "多次调用应返回相同的内容");
  }
}
