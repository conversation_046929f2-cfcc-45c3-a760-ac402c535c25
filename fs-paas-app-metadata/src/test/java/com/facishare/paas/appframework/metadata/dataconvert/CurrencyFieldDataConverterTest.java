package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GenerateByAI
 * 测试CurrencyFieldDataConverter类的货币字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class CurrencyFieldDataConverterTest {

  @Mock
  private IObjectDataProxyService dataService;

  @Mock
  private SessionContext sessionContext;

  private CurrencyFieldDataConverter converter;
  private IFieldDescribe fieldDescribe;
  private IObjectData objectData;

  @BeforeEach
  void setUp() {
    converter = new CurrencyFieldDataConverter();
    fieldDescribe = createCurrencyFieldDescribe("currency_field");
    objectData = createObjectData("currency_field", new BigDecimal("1234.56"));
    
    // 设置转换器的属性
    converter.setFieldDescribe(fieldDescribe);
    converter.setObjectData(objectData);
    converter.setDataService(dataService);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法正常转换BigDecimal货币值
   */
  @Test
  @DisplayName("测试convertFieldData - BigDecimal货币转换")
  void testConvertFieldData_BigDecimalCurrencyConversion() throws Exception {
    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("1234.56", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理null值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null值")
  void testConvertFieldData_HandlesNullValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("currency_field", null);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理整数值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理整数值")
  void testConvertFieldData_HandlesIntegerValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("currency_field", 1000);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("1000", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理Double值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理Double值")
  void testConvertFieldData_HandlesDoubleValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("currency_field", 999.99);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("999.99", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理Long值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理Long值")
  void testConvertFieldData_HandlesLongValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("currency_field", 1234567890L);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("1234567890", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理BigDecimal的科学计数法
   */
  @Test
  @DisplayName("测试convertFieldData - BigDecimal科学计数法转换")
  void testConvertFieldData_BigDecimalScientificNotation() throws Exception {
    // 准备测试数据 - 使用科学计数法的BigDecimal
    BigDecimal scientificNumber = new BigDecimal("1.23E+5");
    objectData = createObjectData("currency_field", scientificNumber);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - toPlainString()应该返回非科学计数法的字符串
    assertEquals("123000", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理零值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理零值")
  void testConvertFieldData_HandlesZeroValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("currency_field", BigDecimal.ZERO);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("0", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理负数货币值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理负数货币值")
  void testConvertFieldData_HandlesNegativeValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("currency_field", new BigDecimal("-1500.75"));
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("-1500.75", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理高精度小数
   */
  @Test
  @DisplayName("测试convertFieldData - 处理高精度小数")
  void testConvertFieldData_HandlesHighPrecisionDecimal() throws Exception {
    // 准备测试数据
    objectData = createObjectData("currency_field", new BigDecimal("123.123456789"));
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("123.123456789", result);
  }

  /**
   * 提供不同类型的货币值测试数据
   */
  private static Stream<Arguments> provideCurrencyValues() {
    return Stream.of(
        Arguments.of(new BigDecimal("100.00"), "100.00"),
        Arguments.of(new BigDecimal("0.01"), "0.01"),
        Arguments.of(new BigDecimal("999999.99"), "999999.99"),
        Arguments.of(new BigDecimal("-50.25"), "-50.25"),
        Arguments.of(100, "100"),
        Arguments.of(0, "0"),
        Arguments.of(-25, "-25"),
        Arguments.of(99.99, "99.99"),
        Arguments.of("123.45", "123.45") // 字符串类型的货币值
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理不同类型的货币值
   */
  @ParameterizedTest
  @MethodSource("provideCurrencyValues")
  @DisplayName("测试convertFieldData - 不同类型的货币值")
  void testConvertFieldData_DifferentCurrencyValues(Object inputValue, String expectedResult) throws Exception {
    // 准备测试数据
    objectData = createObjectData("currency_field", inputValue);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals(expectedResult, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理字段不存在的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 字段不存在")
  void testConvertFieldData_FieldNotExists() throws Exception {
    // 准备测试数据 - 创建不包含目标字段的数据
    objectData = createObjectData("other_field", new BigDecimal("100.00"));
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 字段不存在时应该返回空字符串
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理非数字类型的值
   */
  @Test
  @DisplayName("测试convertFieldData - 非数字类型值")
  void testConvertFieldData_NonNumericValue() throws Exception {
    // 准备测试数据 - 存储布尔类型的值
    objectData = createObjectData("currency_field", true);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 非null值会被转换为字符串
    assertEquals("true", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试转换器的基本属性设置
   */
  @Test
  @DisplayName("测试转换器属性设置")
  void testConverterProperties() {
    // 验证属性设置
    assertEquals(fieldDescribe, converter.getFieldDescribe());
    assertEquals(objectData, converter.getObjectData());
    assertEquals(dataService, converter.getDataService());
  }

  /**
   * 创建测试用的货币字段描述
   */
  private IFieldDescribe createCurrencyFieldDescribe(String apiName) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试货币字段");
    fieldMap.put("type", IFieldType.CURRENCY);
    return FieldDescribeFactory.newInstance(fieldMap);
  }

  /**
   * 创建测试用的对象数据
   */
  private IObjectData createObjectData(String fieldName, Object value) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldName, value);
    return new ObjectData(dataMap);
  }
}
