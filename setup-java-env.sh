#!/bin/bash

# Java开发环境配置脚本
# 使用方法: source setup-java-env.sh [java版本]
# 例如: source setup-java-env.sh 17 (默认使用JDK 17)

# 设置默认Java版本
JAVA_VERSION=${1:-17}

case $JAVA_VERSION in
    8)
        export JAVA_HOME="/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home"
        echo "已切换到 JDK 8"
        ;;
    17)
        export JAVA_HOME="/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.11/Contents/Home"
        echo "已切换到 JDK 17"
        ;;
    *)
        echo "不支持的Java版本: $JAVA_VERSION"
        echo "支持的版本: 8, 17"
        return 1
        ;;
esac

# 更新PATH
export PATH="$JAVA_HOME/bin:$PATH"

# 显示当前Java版本
echo "当前Java版本:"
java -version

echo "当前Maven版本:"
mvn -version

echo ""
echo "环境变量:"
echo "JAVA_HOME=$JAVA_HOME"
echo ""
echo "开发环境配置完成！"
