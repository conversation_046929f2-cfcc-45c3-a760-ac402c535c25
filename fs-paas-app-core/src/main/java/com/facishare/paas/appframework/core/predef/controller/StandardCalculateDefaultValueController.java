package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2022/2/9
 */
public class StandardCalculateDefaultValueController extends PreDefineController<StandardCalculateDefaultValueController.Arg, StandardCalculateDefaultValueController.Result> {

    protected IObjectDescribe describe;
    private MaskFieldLogicService maskFieldLogicService;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        describe = serviceFacade.findObject(controllerContext.getTenantId(), arg.getDescribeApiName());
        maskFieldLogicService = serviceFacade.getBean(MaskFieldLogicService.class);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected Result doService(Arg arg) {
        IObjectData data = calculateForCreateData(arg);
        fillMaskFieldValue(controllerContext.getUser(), describe, Lists.newArrayList(data), arg.getMaskFieldApiNames());
        return Result.builder().objectData(ObjectDataDocument.of(data)).build();
    }

    private IObjectData calculateForCreateData(Arg arg) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEFAULT_VALUE_SUPPORT_OWNER_GRAY, controllerContext.getTenantId())) {
            return serviceFacade.calculateExpressionForCreateData(controllerContext.getUser(), arg.getRecordType(), describe, objectData -> {
                modifyDataBeforeCalculateWithCreate(controllerContext.getUser(), describe, objectData);
            });
        }
        IObjectData data = serviceFacade.calculateExpressionForCreateData(controllerContext.getUser(), arg.getRecordType(), describe);
        if (needRemoveOwnerField(controllerContext.getUser(), describe)) {
            ObjectDataExt.of(data).remove(Sets.newHashSet(IObjectData.OWNER, IObjectData.OWNER + "__r"));
        }
        ObjectDataExt.of(data).removeDefaultValueIfInvalid(describe);
        ObjectDataExt.of(data).removeEmptyMultiCurrencyFields();
        return data;
    }

    protected void modifyDataBeforeCalculateWithCreate(User user, IObjectDescribe describe, IObjectData objectData) {
        if (needRemoveOwnerField(user, describe)) {
            ObjectDataExt.of(objectData).remove(Sets.newHashSet(IObjectData.OWNER, IObjectData.OWNER + "__r"));
        }
        ObjectDataExt.of(objectData).removeDefaultValueIfInvalid(describe);
        ObjectDataExt.of(objectData).removeEmptyMultiCurrencyFields();
    }

    private boolean needRemoveOwnerField(User user, IObjectDescribe describe) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEFAULT_VALUE_SUPPORT_OWNER_GRAY, user.getTenantId()) && user.isOutUser()) {
            return false;
        }
        Map<String, IFormField> formFields = arg.toFormFieldMap();
        if (CollectionUtils.empty(formFields)) {
            return false;
        }

        Optional<IFieldDescribe> ownerField = ObjectDescribeExt.of(describe).getOwnerField();
        return !formFields.containsKey(IObjectData.OWNER)
                && ownerField.isPresent()
                && !ownerField.get().isRequired();
    }

    private void fillMaskFieldValue(User user, IObjectDescribe describe, List<IObjectData> objectDataList, Map<String, List<String>> maskFieldApiNames) {
        if (CollectionUtils.empty(maskFieldApiNames)) {
            return;
        }
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(describe);
        List<IFieldDescribe> maskFields = objectDescribeExt.getFieldByApiNames(maskFieldApiNames.get(describe.getApiName()));
        maskFieldLogicService.fillMaskFieldValue(user, describe, objectDataList, maskFields, MaskFieldLogicService.MaskFieldConfig.defaultMaskFieldConfig());
    }

    @Data
    public static class Arg {
        private String describeApiName;
        private String recordType;
        /**
         * 需要掩码加密的字段
         */
        private Map<String, List<String>> maskFieldApiNames;

        private List<DocumentBaseEntity> formFields;


        public Map<String, IFormField> toFormFieldMap() {
            if (CollectionUtils.empty(formFields)) {
                return Collections.emptyMap();
            }
            return formFields.stream()
                    .map(FormField::new)
                    .collect(Collectors.toMap(FormField::getFieldName, Function.identity(), (x, y) -> y));
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private ObjectDataDocument objectData;
    }
}
