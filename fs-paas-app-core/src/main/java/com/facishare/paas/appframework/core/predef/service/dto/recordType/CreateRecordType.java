package com.facishare.paas.appframework.core.predef.service.dto.recordType;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.metadata.layout.I18nInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

public interface CreateRecordType {

    @Data
    class Arg{
        @JSONField(name = "M1")
        String describeApiName;
        @JSONField(name = "M2")
        String record_type;
        String remark;
        @JsonProperty("i18nInfoList")
        List<I18nInfo> i18nInfoList;
    }

    @Data
    class Result{
        @JSONField(name = "M2")
        private boolean success;
    }
}
