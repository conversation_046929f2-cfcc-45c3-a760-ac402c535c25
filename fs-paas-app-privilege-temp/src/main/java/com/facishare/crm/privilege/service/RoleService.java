package com.facishare.crm.privilege.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.common.exception.CRMErrorCode;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.privilege.controller.arg.*;
import com.facishare.crm.privilege.model.AuditLog;
import com.facishare.crm.privilege.model.FunctionInfo;
import com.facishare.crm.privilege.model.ObjectPrivilegeInfo;
import com.facishare.crm.privilege.model.valueobject.CrmException;
import com.facishare.crm.privilege.model.valueobject.CrmResult;
import com.facishare.crm.privilege.swagger.ApiClient;
import com.facishare.crm.privilege.swagger.ApiException;
import com.facishare.crm.privilege.swagger.SwaggerClientUtil;
import com.facishare.crm.privilege.swagger.api.FieldPermissionApi;
import com.facishare.crm.privilege.swagger.api.FuncPermissionApi;
import com.facishare.crm.privilege.swagger.api.PaasAuthApiApi;
import com.facishare.crm.privilege.swagger.api.UserRoleApi;
import com.facishare.crm.privilege.swagger.model.*;
import com.facishare.crm.privilege.util.*;
import com.facishare.crm.valueobject.SessionContext;
import com.facishare.fcp.model.BaseFcpServiceResult;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.RecordTypeAuthProxy;
import com.facishare.paas.appframework.metadata.RecordTypeLogicServiceImpl;
import com.facishare.paas.appframework.metadata.dto.auth.*;
import com.facishare.paas.appframework.metadata.objects.ObjectListConfig;
import com.facishare.paas.appframework.metadata.util.ProductUtil;
import com.facishare.paas.appframework.privilege.MasterSlaveFunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.ForbiddenUsers;
import com.facishare.paas.appframework.privilege.model.DefaultFunctionPrivilegeProvider;
import com.facishare.paas.appframework.privilege.model.FunctionCodeBuilder;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProvider;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProviderManager;
import com.facishare.paas.appframework.privilege.model.role.AccountTransactionOperatorRoleProvider;
import com.facishare.paas.appframework.privilege.model.role.ChannelManagerRoleProvider;
import com.facishare.paas.appframework.privilege.model.role.Role;
import com.facishare.paas.appframework.privilege.util.FunctionPrivillegeConfig;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.metadata.api.GroupField;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.facishare.crm.privilege.util.GetRoleUtils.apiName2NotEditableActionCodes;
import static com.facishare.crm.privilege.util.PrivilegeFieldsUtils.SFA_OBJECT_API_NAMES;
import static com.facishare.crm.privilege.util.VersionConstants.VERSION_ENTERPRISE;
import static com.facishare.paas.appframework.common.util.ObjectAction.UNKNOWN_ACTION;
import static com.facishare.paas.appframework.core.i18n.I18NKey.PRIVIELGE_USE_VERSION;
import static com.facishare.paas.appframework.privilege.util.Headers.PAAS_PRIVILEGE_HEADDER;


@Slf4j
@Service
@Deprecated
public class RoleService {
    private static final int USER_DEFINED_ROLE_FLAG = 2;
    private final IChangeableConfig props = ConfigFactory.getConfig(Constant.getFS_CRM_PRIVILEGE());
    private final int openAuditLogFlag = props.getInt("audit_log");
    Set<String> doNotHaveFieldPrivilegeApiNames = Sets.newHashSet("CheckinsObj", "CheckinsImgObj", "GoalValueObj", "ActiveRecordObj", "LeadsTransferLogObj");
    @Autowired
    private PushSessionService pushSessionService;
    @Autowired
    private SyncCrmVisibleService syncCrmVisibleService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private ChannelManagerRoleProvider channelManagerRoleProvider;
    @Autowired
    private AccountTransactionOperatorRoleProvider accountTransactionOperatorRoleProvider;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private FunctionPrivilegeProviderManager functionPrivilegeProviderManager;
    @Autowired
    private ForbiddenUsersService forbiddenUsersService;
    @Autowired
    private MasterSlaveFunctionPrivilegeService masterSlaveFunctionPrivilegeService;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private RecordTypeAuthProxy recordTypeAuthProxy;
    @Autowired
    private RecordTypeLogicServiceImpl recordTypeLogicService;
    private UserRoleApi userRoleApi;
    private FuncPermissionApi funcPermissionApi;
    private FieldPermissionApi fieldPermissionApi;
    private PaasAuthApiApi paasAuthApiApi;
    private int customRoleSize;
    private Set<String> customRoleEaSet = Sets.newHashSet();

    private String dhtRoleCode = "00000000000000000000000000000021";

    private Set<String> productObjEditFunctionCode = Sets.newHashSet();

    private Set<String> spuProductChainReactFuncCodes = Sets.newHashSet();

    private Set<String> roleCodeCheckEIs = Sets.newHashSet();

    private Map<String, List<String>> resetRoleFuncMap = Maps.newHashMap();


    @PostConstruct
    private void init() {
        userRoleApi = SwaggerApiUtil.getUserRoleApi();
        funcPermissionApi = SwaggerApiUtil.getFuncPermissionApi();
        fieldPermissionApi = SwaggerApiUtil.getFieldPermissionApi();
        paasAuthApiApi = SwaggerApiUtil.getPaasAuthApiApi();

        ConfigFactory.getInstance().getConfig("fs-privilege-config", (config) -> {
            customRoleSize = config.getInt("customRoleSize", 250);
            String temp = config.get("customRoleSizeEaString");
            if (StringUtils.isNotBlank(temp)) {
                customRoleEaSet = Arrays.stream(temp.split(",")).collect(Collectors.toSet());
            }

            productObjEditFunctionCode = Arrays.stream(config.get("productObjEditFunctionCode", "ProductObj||ChangeBPMApprover,ProductObj||StartBPM,ProductObj||StopBPM,ProductObj||ViewEntireBPM,ProductObj||UploadDeleteAttach,ProductObj||ViewAttach,ProductObj||Relate,ProductObj||ViewAttach,ProductObj||PictureAnnexDownload,ProductObj||ViewBPMInstanceLo,ProductObj||AddToPriceBook").split(",")).collect(Collectors.toSet());

            spuProductChainReactFuncCodes = Arrays.stream(config.get("spuProductChainReactFuncCodes", "SPUObj,SPUObj||Add,SPUObj||View,SPUObj||Edit,SPUObj||Abolish,SPUObj||Lock,SPUObj||Unlock,SPUObj||ChangeOwner,SPUObj||EditTeamMember,SPUObj||Print,SPUObj||Export,SPUObj||Delete,SPUObj||Recover,SPUObj||Import").split(",")).collect(Collectors.toSet());

            roleCodeCheckEIs = Arrays.stream(config.get("roleCodeCheckEIs", "").split(",")).collect(Collectors.toSet());

            resetRoleFuncMap = JSON.parseObject(config.get("resetRoleFuncMap"), new TypeReference<Map<String, List<String>>>() {
            });

        });
    }


    public BaseFcpServiceResult getRolePrivilege(GetRolePrivilegeArg arg, SessionContext sessionContext) {
        String roleCode = arg.getRoleCode();
        if (StringUtils.isBlank(roleCode)) {
            return parameterIsNullOrBlank();
        }

        BaseFcpServiceResult rolePrivilegeResult = getRolePrivilegeResult(arg, sessionContext);
        List privilegeInfo = (List) rolePrivilegeResult.getResult();
        for (Object objectPrivilegeInfo : privilegeInfo) {
            ObjectPrivilegeInfo privilege = (ObjectPrivilegeInfo) objectPrivilegeInfo;
            final String descApiName = privilege.getDescApiName();

            if ("SpecificationObj".equals(descApiName) || "SpecificationValueObj".equals(descApiName)) {
                List<FunctionInfo> roleFunctionInfos = privilege.getRoleFunctionInfos();
                for (FunctionInfo functionList : roleFunctionInfos) {
                    final String functionCode = functionList.getFunctionNumber();
                    if ("SpecificationObj||Import".equals(functionCode) || "SpecificationValueObj||Import".equals(functionCode)) {
                        functionList.setIsEditable(false);
                    }
                }
            }
            if ("ProductObj".equals(descApiName)) {
                if (ProductUtil.isSpuOpen(String.valueOf(sessionContext.getEId()))) {
                    List<FunctionInfo> roleFunctionInfos = privilege.getRoleFunctionInfos();
                    for (FunctionInfo functionList : roleFunctionInfos) {
                        final String functionCode = functionList.getFunctionNumber();
                        if (!"ProductObj||UploadDeleteAttach".equals(functionCode)
                                && !"ProductObj||ViewAttach".equals(functionCode)
                                && !"ProductObj||Relate".equals(functionCode)) {
                            functionList.setIsEditable(false);
                        }
                    }
                }
            }
        }

        rolePrivilegeResult.setResult(privilegeInfo);
        return rolePrivilegeResult;
    }

    private BaseFcpServiceResult getRolePrivilegeResult(GetRolePrivilegeArg arg, SessionContext sessionContext) {
        String objectId2crmRequestCode = GetRoleUtils.getObjectId2crmRequestCodeByVersion(VERSION_ENTERPRISE);
        return getRolePrivilege(arg, sessionContext, objectId2crmRequestCode);
    }

    private BaseFcpServiceResult getRolePrivilege(GetRolePrivilegeArg arg, SessionContext sessionContext, String objectId2crmRequestCode) {
        AuthContext authContext = getAuthContext(sessionContext);
        authContext.setLanguageLocale(sessionContext.getLanguageLocale());
        BaseFcpServiceResult result = new BaseFcpServiceResult();
        RoleFuncPermissionArg roleFuncPermissionArg = new RoleFuncPermissionArg();

        roleFuncPermissionArg.setAuthContext(authContext);
        String roleCode = arg.getRoleCode();
        roleFuncPermissionArg.setRoleCode(roleCode);

        if (StringUtils.isBlank(roleCode)) {
            return parameterIsNullOrBlank();
        }

        FuncPermissResponse baseResult;

        List<ObjectPrivilegeInfo> privilegeInfoList = Lists.newArrayList();
        try {
            baseResult = funcPermissionApi.roleFuncPermiss(roleFuncPermissionArg);
            List<FunctionPojo> resultObject = baseResult.getResult();
            Map<String, FunctionPojo> funcCode2FuncInfo = getFuncCode2FuncInfo(resultObject);

            // 增加查重工具
            processDuplicateCheckObjPrivilegeInfo(privilegeInfoList, arg.getRoleCode(), funcCode2FuncInfo);

            Set<String> supportObjApiNames = getSupportObjApiNames(authContext);
            // 添加自定义对象
            Set<String> slaveHasNoFunctionCodes = masterSlaveFunctionPrivilegeService.getSlaveHasNoFunctionCodes(authContext.getTenantId());
            List<ObjectPrivilegeInfo> userDefObjectPrivileges = queryUserDefObjectPrivilege(roleCode, sessionContext, funcCode2FuncInfo, supportObjApiNames, slaveHasNoFunctionCodes);

            privilegeInfoList.addAll(userDefObjectPrivileges);

            List<GetRoleUtils.FuncCodeInfo> codeInfoList = getFuncCodeInfos(roleCode);
            codeInfoList.forEach(funcCodeInfo -> {
                if (funcCode2FuncInfo.get(funcCodeInfo.getFuncCode()) != null) {
                    funcCodeInfo.setDisplayName(funcCode2FuncInfo.get(funcCodeInfo.getFuncCode()).getFuncName());
                }
            });

            List<String[]> objectOrder = GetRoleUtils.getObjectOrder(objectId2crmRequestCode);
            objectOrder.add(GetRoleUtils.INVENTORY_OBJECT_INFO);
            for (String[] objectInfo : objectOrder) {
                // 根据分版逻辑判断对象是否下发
                String apiName = objectInfo[2];
                if (!supportObjApiNames.contains(apiName) && !GetRoleUtils.BI_OBJECTS.contains(apiName) && "CRM".equals(sessionContext.getAppId())) {
                    continue;
                }

                ObjectPrivilegeInfo privilegeInfo = initPrivilegeInfo(roleCode, objectInfo, apiName);
                //将crm这边的信息传递到了对象中了
                for (GetRoleUtils.FuncCodeInfo funcCodeInfo : codeInfoList) {
                    String descApiName = funcCodeInfo.getDescApiName();
                    if (descApiName == null || !Objects.equals(descApiName, apiName)) {
                        continue;
                    }
                    String functionNumber = funcCodeInfo.getFuncCode();
                    if (funcCode2FuncInfo.get(functionNumber) == null) {
                        continue;
                    }
                    if (slaveHasNoFunctionCodes.contains(functionNumber)) {
                        continue;
                    }
                    Boolean enabled = funcCode2FuncInfo.get(functionNumber).getIsEnabled();

                    //标记是否是BI的权限
                    if (privilegeInfo.getIsBIObject()) {
                        if (GetRoleUtils.BI_VIEW_LIST_PRIVILEGE.equals(funcCodeInfo.getOperationName())) {
                            privilegeInfo.setViewListFuncCode(funcCodeInfo.getFuncCode());
                            privilegeInfo.setIsHaveViewListPermiss(enabled);
                        }
                    } else {
                        //标记是否是 查看列表权限
                        if (GetRoleUtils.VIEW_LIST_PRIVILEGE.equals(funcCodeInfo.getOperationName())) {
                            privilegeInfo.setViewListFuncCode(funcCodeInfo.getFuncCode());
                            privilegeInfo.setIsHaveViewListPermiss(enabled);
                        }
                    }
                    FunctionInfo functionInfo = initFunctionInfo(funcCodeInfo, functionNumber, enabled);
                    privilegeInfo.getRoleFunctionInfos().add(functionInfo);
                }
                // 如果查询不到对象的功能权限,就不展示对象
                if (CollectionUtils.isNotEmpty(privilegeInfo.getRoleFunctionInfos())) {
                    addPrivilegeInfo2List(privilegeInfoList, privilegeInfo);
                }
            }

            if (Objects.equals(roleCode, GetRoleUtils.PERSONNEL_ROLE_CODE)) {
                Iterator<ObjectPrivilegeInfo> iterator = privilegeInfoList.iterator();
                while (iterator.hasNext()) {
                    ObjectPrivilegeInfo next = iterator.next();
                    String descApiName = next.getDescApiName();
                    if (!GetRoleUtils.PERSONNEL_ROLE_SHOW_APINAME.contains(descApiName)) {
                        iterator.remove();
                    } else {
                        next.getRoleFunctionInfos()
                                .stream()
                                .filter(e -> isPersonnelNotEditable(e.getFunctionNumber(), sessionContext.getEId()))
                                .forEach(e -> e.setIsEditable(false));
                    }
                }
            }

            Integer errorCode = baseResult.getErrCode();
            result.setErrorCode(errorCode);
            result.setErrorMessage(baseResult.getErrMessage());
            result.setResult(privilegeInfoList);
            return result;
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "roleFuncPermiss", JSONObject.toJSON(arg), e);
            return errorResponse(result, e.getMessage());
        }
    }

    private boolean isPersonnelNotEditable(String functionNumber, Long enterpriseId) {
        if (!FunctionPrivillegeConfig.getPersonnelRoleEditableFunctionCodes().contains(functionNumber)) {
            return true;
        }
        if (FunctionPrivillegeConfig.openPersonnelRoleEditableFunctionCodesGray(enterpriseId.toString())) {
            return false;
        }
        return true;
    }

    private FunctionInfo initFunctionInfo(GetRoleUtils.FuncCodeInfo funcCodeInfo, String functionNumber, Boolean enabled) {
        FunctionInfo functionInfo = new FunctionInfo(funcCodeInfo.getDisplayName(), funcCodeInfo.getIsEditable(), functionNumber, enabled, true);

        if (ObjectAction.INTELLIGENTFORM.getActionCode().equals(funcCodeInfo.getOperationName())) {
            functionInfo.setIsIntelligentForm(Boolean.TRUE);
        }

        if (ObjectAction.CREATE.getActionCode().equals(funcCodeInfo.getOperationName())) {
            functionInfo.setIsAdd(Boolean.TRUE);
        }
        return functionInfo;
    }

    @NotNull
    private ObjectPrivilegeInfo initPrivilegeInfo(String roleCode, String[] objectInfo, String apiName) {
        ObjectPrivilegeInfo privilegeInfo = new ObjectPrivilegeInfo();
        privilegeInfo.setDescApiName(apiName);
        privilegeInfo.setDescApiDisplayName(I18N.text(objectInfo[1]));
        privilegeInfo.setIsHaveFieldPrivilege(Boolean.valueOf(objectInfo[0]));
        privilegeInfo.setIsEditable(isEditable4Object(roleCode, apiName));

        if (GetRoleUtils.BI_OBJECTS.contains(apiName)) {
            privilegeInfo.setIsBIObject(Boolean.TRUE);
        } else {
            privilegeInfo.setIsBIObject(Boolean.FALSE);
        }
        return privilegeInfo;
    }

    @NotNull
    private Set<String> getSupportObjApiNames(AuthContext authContext) {
        Set<String> tmpSupportObjApiNames = licenseService.getObjectApiNames(authContext.getTenantId());
        Set<String> supportObjApiNames = Sets.newHashSet();

        if (CollectionUtils.isNotEmpty(tmpSupportObjApiNames)) {
            tmpSupportObjApiNames.forEach(o -> {
                String oldApiName = VersionConstants.NEW_OLD_OBJECT_MAPPING.get(o);
                if (oldApiName != null) {
                    supportObjApiNames.add(oldApiName);
                }
                supportObjApiNames.add(o);
            });
        } else {
            log.warn("tenantLicense is blank. tenantId {}", authContext.getTenantId());
        }
        return supportObjApiNames;
    }

    private List<GetRoleUtils.FuncCodeInfo> getFuncCodeInfos(String roleCode) {
        GetRoleUtils.Role role;
        //找到目标的角色
        if (PrivilegeConstants.CHECKINS_ROLE_CODE.equals(roleCode) || GetRoleUtils.PARTNER_ROLE_CODE.equals(roleCode)) {
            role = GetRoleUtils.getOneDefaultRule();
        } else {
            role = GetRoleUtils.getRoleByRoleCode(roleCode);
        }
        return role != null ? role.getFuncCodeInfoList() : Lists.newArrayList();
    }

    private void processDuplicateCheckObjPrivilegeInfo(List<ObjectPrivilegeInfo> privilegeInfoList, String roleCode, Map<String, FunctionPojo> funcCode2FuncInfo) {
        FunctionInfo duplicateCheckObjFunctionInfo = FunctionInfo.getDuplicateCheckObjFunctionInfo();
        FunctionPojo functionPojo = funcCode2FuncInfo.get(duplicateCheckObjFunctionInfo.getFunctionNumber());

        if (functionPojo != null) {
            duplicateCheckObjFunctionInfo.setEnabledOrDefault(functionPojo.getIsEnabled());
            duplicateCheckObjFunctionInfo.setDisplayName(getMultilingualFuncDisplayName(functionPojo));

            ObjectPrivilegeInfo objectPrivilegeInfo;
            if ("00000000000000000000000000000006".equals(roleCode)) {
                duplicateCheckObjFunctionInfo.setIsEditable(false);
                objectPrivilegeInfo = ObjectPrivilegeInfo.builder()
                        .descApiName("DuplicateCheckObj")
                        .descApiDisplayName(I18N.text(I18NKey.REPETITION_CHECKER))
                        .viewListFuncCode("DuplicateCheckObj").roleFunctionInfos(Lists.newArrayList(duplicateCheckObjFunctionInfo)).build();
            } else {
                duplicateCheckObjFunctionInfo.setIsEditable(true);
                objectPrivilegeInfo = ObjectPrivilegeInfo.builder()
                        .descApiName("DuplicateCheckObj")
                        .descApiDisplayName(I18N.text(I18NKey.REPETITION_CHECKER))
                        .viewListFuncCode("DuplicateCheckObj").roleFunctionInfos(Lists.newArrayList(duplicateCheckObjFunctionInfo))
                        .isEditable(true).build();
            }
            privilegeInfoList.add(objectPrivilegeInfo);
        }
    }

    private boolean isEditable4Object(String roleCode, String objectApiName) {
        boolean isEditable = true;
        if (roleCode.equals(GetRoleUtils.getAdminRoleCode()) && !"Visit".equals(objectApiName)) {
            isEditable = false;
        }
        return isEditable;
    }


    /**
     * 获取自定义对象的权限
     */
    private List<ObjectPrivilegeInfo> queryUserDefObjectPrivilege(String roleCode, SessionContext sessionContext, Map<String, FunctionPojo> funcCode2FuncInfo, Set<String> supportObjectApiNames, Set<String> slaveHasNoFunctionCodes) {

        List<IObjectDescribe> objectDescribes = describeLogicService.findObjectsByTenantId(sessionContext.getEId().toString(), false, Boolean.TRUE, Boolean.FALSE, Boolean.FALSE, ObjectListConfig.FUNCTION_PRIVILEGE);

        List<String> describeApiNames = objectDescribes.stream().map(IObjectDescribe::getApiName).collect(Collectors.toList());

        FuncPermissionCheckArg arg = new FuncPermissionCheckArg();
        arg.setAuthContext(ContextUtils.getAuthContext(sessionContext));
        arg.setFuncCodeList(describeApiNames);

        Map<String, String> apiNameAndDisplayMapping = Maps.newLinkedHashMap();

        objectDescribes.stream().filter(o -> ((supportObjectApiNames.contains(o.getApiName()) || o.getApiName().endsWith("__c"))
                || !"CRM".equals(sessionContext.getAppId()))
        ).forEach(o -> apiNameAndDisplayMapping.put(o.getApiName(), o.getDisplayName()));

        apiNameAndDisplayMapping.put("GoalValueObj", I18N.text("goal.value.object"));
        return getUserDefObjectPrivilegeInfo(roleCode, funcCode2FuncInfo, apiNameAndDisplayMapping, slaveHasNoFunctionCodes, sessionContext.getEId());
    }

    @NotNull
    private List<ObjectPrivilegeInfo> getUserDefObjectPrivilegeInfo(String roleCode, Map<String, FunctionPojo> funcCode2FuncInfo, Map<String, String> apiNameAndDisplayMapping, Set<String> slaveHasNoFunctionCodes, Long enterpriseId) {
        List<ObjectPrivilegeInfo> resultList = Lists.newArrayList();
        for (Map.Entry<String, String> entry : apiNameAndDisplayMapping.entrySet()) {
            String apiName = entry.getKey();
            String displayName = entry.getValue();
            ObjectPrivilegeInfo privilegeInfo = new ObjectPrivilegeInfo();
            privilegeInfo.setDescApiName(apiName);
            privilegeInfo.setViewListFuncCode(apiName);
            privilegeInfo.setDescApiDisplayName(displayName);
            privilegeInfo.setIsHaveFieldPrivilege(isHaveFieldPrivilege(apiName));
            privilegeInfo.setIsEditable(Boolean.TRUE);
            privilegeInfo.setIsBIObject(Boolean.FALSE);

            FunctionPrivilegeProvider privilegeProvider = getProvider(apiName);
            List<String> actionCodes = privilegeProvider.getSupportedActionCodes();

            if (funcCode2FuncInfo.get(apiName) != null) {
                Set<String> funcCodes = Sets.newHashSet();
                for (String actionCode : actionCodes) {
                    String functionNumber = FunctionCodeBuilder.build(apiName, actionCode);
                    funcCodes.add(functionNumber);
                    if (slaveHasNoFunctionCodes.contains(functionNumber)) {
                        continue;
                    }

                    Boolean enabled = getFuncCodeEnableStatus(funcCode2FuncInfo, functionNumber);

                    if (enabled == null) {
                        continue;
                    }
                    if (ObjectAction.VIEW_LIST.getActionCode().equals(actionCode)) {
                        privilegeInfo.setIsHaveViewListPermiss(enabled);
                    }

                    boolean isEditable = getFuncCodeEditableStatus(roleCode, apiName, actionCode, enterpriseId);
                    FunctionInfo functionInfo = new FunctionInfo(ObjectAction.of(actionCode).getActionLabel(), isEditable, functionNumber, enabled, true);
                    handleFunctionInfo(apiName, functionInfo, actionCode);
                    privilegeInfo.getRoleFunctionInfos().add(functionInfo);
                }

                Set<String> specialUserDefinedFuncCodes = getSpecialUserDefinedFuncCodes(apiName, funcCode2FuncInfo, funcCodes);
                for (String funcCode : specialUserDefinedFuncCodes) {
                    if (slaveHasNoFunctionCodes.contains(funcCode)) {
                        continue;
                    }

                    FunctionPojo functionPojo = funcCode2FuncInfo.get(funcCode);
                    String funcCodeDisplayName = getFuncDisplayName(funcCode, functionPojo);
                    FunctionInfo functionInfo = new FunctionInfo(funcCodeDisplayName, getFuncCodeEditableStatus(roleCode, apiName), funcCode, functionPojo.getIsEnabled(), false);
                    privilegeInfo.getRoleFunctionInfos().add(functionInfo);
                }
                resultList.add(privilegeInfo);
            }

        }
        return resultList;
    }

    private String getFuncDisplayName(String funcCode, FunctionPojo functionPojo) {
        String actionCode = funcCode.substring((funcCode.indexOf("||") == -1) ? 0 : funcCode.indexOf("||") + 2);

        return getFuncDisplayNameByActionCode(actionCode, functionPojo);
    }

    private String getMultilingualFuncDisplayName(FunctionPojo functionPojo) {
        String MultilingualFuncDisplayName = I18N.text(functionPojo.getFuncName());
        if (StringUtils.isBlank(MultilingualFuncDisplayName)) {
            MultilingualFuncDisplayName = functionPojo.getFuncName();
        }
        return MultilingualFuncDisplayName;
    }

    private String getFuncDisplayNameByActionCode(String actionCode, FunctionPojo functionPojo) {
        ObjectAction action = ObjectAction.of(actionCode);
        return action.equals(UNKNOWN_ACTION) ? functionPojo.getFuncName() : action.getActionLabel();
    }

    private Boolean isHaveFieldPrivilege(String apiName) {
        return !doNotHaveFieldPrivilegeApiNames.contains(apiName);
    }


    private Set<String> getSpecialUserDefinedFuncCodes(String objectApiName, Map<String, FunctionPojo> funcCode2FuncInfo, Set<String> prefuncCodes) {
        Set<Map.Entry<String, FunctionPojo>> funcCodeEntrySet = funcCode2FuncInfo.entrySet();
        Set<String> specialUserDefinedFuncCodes = Sets.newLinkedHashSet();
        for (Map.Entry<String, FunctionPojo> funcCodeEntry : funcCodeEntrySet) {
            if (funcCodeEntry.getKey().startsWith(objectApiName) && funcCode2FuncInfo.get(funcCodeEntry.getKey()) != null
                    && !prefuncCodes.contains(funcCodeEntry.getKey()) && (Objects.equals(funcCodeEntry.getKey().split("\\|\\|")[0], objectApiName))) {
                specialUserDefinedFuncCodes.add(funcCodeEntry.getKey());
            }
        }
        return specialUserDefinedFuncCodes;
    }


    private boolean getFuncCodeEditableStatus(String roleCode, String objectApiName) {
        boolean isEditable = Boolean.TRUE;
        if ((GetRoleUtils.getAdminRoleCode().equals(roleCode)
                || "OrderPaymentObj".equals(objectApiName))
                || (PrivilegeConstants.CHECKINS_ROLE_CODE.equals(roleCode) && ("CheckinsImgObj".equals(objectApiName) || "CheckinsObj".equals(objectApiName) || "CheckinsImgDetailObj".equals(objectApiName)))) {
            isEditable = Boolean.FALSE;
        }
        return isEditable;
    }

    private boolean getFuncCodeEditableStatus(String roleCode, String objectApiName, String actionCode, Long enterpriseId) {
        if (GetRoleUtils.getAdminRoleCode().equals(roleCode)) {
            return false;
        }
        if (GetRoleUtils.PERSONNEL_ROLE_CODE.equals(roleCode)) {
            if (isPersonnelNotEditable(objectApiName + "||" + actionCode, enterpriseId)) {
                return false;
            }
            return true;
        }
        if ("OrderPaymentObj".equals(objectApiName)) {
            return false;
        }

        //外勤人员的三个对象不允许编辑
        if (PrivilegeConstants.CHECKINS_ROLE_CODE.equals(roleCode) && ("CheckinsImgObj".equals(objectApiName) || "CheckinsObj".equals(objectApiName) || "CheckinsImgDetailObj".equals(objectApiName))) {
            return false;
        }

        if (apiName2NotEditableActionCodes.get(objectApiName) != null && apiName2NotEditableActionCodes.get(objectApiName).contains(actionCode)) {
            return false;
        }
        return true;
    }

    private Boolean getFuncCodeEnableStatus(Map<String, FunctionPojo> funcCode2FuncInfo, String functionNumber) {
        if (funcCode2FuncInfo.get(functionNumber) == null) {
            return null;
        }

        Boolean enabled = funcCode2FuncInfo.get(functionNumber).getIsEnabled();
        //如果没找到,就用默认的
        if (enabled == null) {
            enabled = Boolean.FALSE;
        }
        return enabled;
    }


    private void handleFunctionInfo(String apiName, FunctionInfo functionInfo, String actionCode) {
        if (ObjectAction.INTELLIGENTFORM.getActionCode().equals(actionCode)) {
            functionInfo.setIsIntelligentForm(Boolean.TRUE);
        }

        if (ObjectAction.CREATE.getActionCode().equals(actionCode)) {
            functionInfo.setIsAdd(Boolean.TRUE);
        }

        if (ObjectAction.CLONE.getActionCode().equals(actionCode)) {
            functionInfo.setIsClone(Boolean.TRUE);
        }

        if (Utils.SALES_ORDER_API_NAME.equals(apiName)) {
            if (GetRoleUtils.ADD_PRIVILEGE.equals(actionCode)
                    || GetRoleUtils.IMPORT_PRIVILEGE.equals(actionCode)
                    || GetRoleUtils.EDIT_PRIVILEGE.equals(actionCode)) {
                functionInfo.setIsFiledReadOnlyRequired(Boolean.TRUE);
            }
        } else if (GetRoleUtils.ADD_PRIVILEGE.equals(actionCode) || GetRoleUtils.IMPORT_PRIVILEGE.equals(actionCode)) {
            functionInfo.setIsFiledReadOnlyRequired(Boolean.TRUE);
        }
    }


    private FunctionPrivilegeProvider getProvider(String apiName) {
        return functionPrivilegeProviderManager.getProvider(apiName);
    }


    private void addPrivilegeInfo2List(List<ObjectPrivilegeInfo> privilegeInfoList, ObjectPrivilegeInfo privilegeInfo) {
        if (privilegeInfo.getRoleFunctionInfos().size() > 0) {
            privilegeInfoList.add(privilegeInfo);
        }
    }


    private Map<String, FunctionPojo> getFuncCode2FuncInfo(List<FunctionPojo> resultObject) {
        Map<String, FunctionPojo> funcCode2FuncInfo = Maps.newHashMap();
        for (FunctionPojo functionPojo : resultObject) {
            String funcCode = functionPojo.getFuncCode();


            funcCode2FuncInfo.put(funcCode, functionPojo);
        }
        return funcCode2FuncInfo;
    }


    private BaseFcpServiceResult parameterIsNullOrBlank() {
        BaseFcpServiceResult result = new BaseFcpServiceResult();
        result.setErrorCode(CRMErrorCode.PARAMETER_IS_WRONG.getCode());
        result.setErrorMessage(CRMErrorCode.PARAMETER_IS_WRONG.getMessage());
        return result;

    }

    private BaseFcpServiceResult privilegeParameterError() {
        BaseFcpServiceResult result = new BaseFcpServiceResult();
        result.setErrorCode(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getCode());
        result.setErrorMessage(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getMessage());
        return result;
    }

    private BaseFcpServiceResult paasError(String errorMessage) {
        BaseFcpServiceResult result = new BaseFcpServiceResult();
        result.setErrorCode(CRMErrorCode.PAAS_ERROR.getCode());
        result.setErrorMessage(errorMessage);
        return result;

    }

    private BaseFcpServiceResult userDefinedRoleQuotaError(int quota) {
        BaseFcpServiceResult result = new BaseFcpServiceResult();
        result.setErrorCode(CRMErrorCode.USER_DEFINED_ROLE_QUOTA_ERROR.getCode());
        String message = I18N.text(I18NKey.USER_DEFINED_ROLE_QUOTA_ERROR, PRIVIELGE_USE_VERSION, quota);
        result.setErrorMessage(message);
        return result;
    }


    @NotNull
    private Map<String, Integer> getGroupChildrenField2Status(String tenantId, Map<String, Integer> fieldName2Status, String descApiName) {
        IObjectDescribe objectDescribe = describeLogicService.findObject(tenantId, descApiName);
        Map<String, Integer> childrenFieldName2Status = Maps.newHashMap();
        try {
            for (IFieldDescribe fieldDescribe : objectDescribe.getFieldDescribes()) {
                //如果字段是group类型的,找出这个字段的子字段
                if (IFieldType.GROUP.equals(fieldDescribe.getType())) {
                    String fieldApiName = fieldDescribe.getApiName();

                    Integer status = fieldName2Status.get(fieldApiName);
                    Set<String> childrenFieldApiNames = ((GroupField) fieldDescribe).getFieldList(objectDescribe).stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet());
                    if (status != null && CollectionUtils.isNotEmpty(childrenFieldApiNames)) {
                        childrenFieldApiNames.forEach(fileName -> childrenFieldName2Status.put(fileName, status));
                    }
                }
            }
        } catch (MetadataServiceException e) {
            log.error("IFieldDescribe cast to GroupField error,userDefinedObject error.", e);
            throw new CrmException(CRMErrorCode.SYSTEM_ERROR.getCode(), CRMErrorCode.SYSTEM_ERROR.getMessage());
        }
        return childrenFieldName2Status;
    }


    public BaseFcpServiceResult updateRolePrivilege(UpdateRolePrivilegeArg arg, SessionContext sessionContext) {
        BaseFcpServiceResult result = new BaseFcpServiceResult();
        String roleCode = arg.getRoleCode();
        if (StringUtils.isBlank(roleCode) || arg.getFuncCode() == null) {
            return parameterIsNullOrBlank();
        }
        //第一次查询A角色权限返回慢于第二次查询B角色，导致B角色保存是A角色的权限；进行校验
        if (roleCodeCheckEIs.contains(sessionContext.getEId().toString()) || roleCodeCheckEIs.contains("*")) {
            if (!Objects.equals(roleCode, arg.getReturnedRoleCode())) {
                return privilegeParameterError();
            }
        }
        BaseResult baseResult;
        try {
            AuthContext authContext = getAuthContext(sessionContext);
            List<String> argFunctionCodeList = arg.getFuncCode();

            List<IObjectDescribe> supportObjectDescribes = describeLogicService.findObjectsByTenantId(authContext.getTenantId(),
                    false, false, false, false, ObjectListConfig.FUNCTION_PRIVILEGE);

            Set<String> inActiveObjectAPiNames = supportObjectDescribes.stream().filter(o -> !o.isActive() && !o.isDeleted()).map(IObjectDescribe::getApiName).collect(Collectors.toSet());

            List<FunctionPojo> oldFunctionPojoList = getRoleFunctionInfoList(roleCode, authContext);

            Map<String, FunctionPojo> funcCode2FunctionPojo = Maps.newHashMap();
            oldFunctionPojoList.forEach(functionPojo -> {
                String funcCode = functionPojo.getFuncCode();
                funcCode2FunctionPojo.put(funcCode, functionPojo);

                String objectApiName = getApiNameByFunctionCode(funcCode);
                // 将禁用的有权限的funcCode增加上
                if (inActiveObjectAPiNames.contains(objectApiName) && (functionPojo.getIsEnabled() || GetRoleUtils.getAdminRoleCode().equals(arg.getRoleCode()))) {
                    argFunctionCodeList.add(funcCode);
                }
            });

            if (ProductUtil.isSpuOpen(authContext.getTenantId())) {
                processSPUProductFuncCodeChainReact(argFunctionCodeList);
            }

            baseResult = updateRoleFunctionCode(roleCode, authContext, argFunctionCodeList);

            if (baseResult.getSuccess()) {
                taskExecutor.submit(
                        MonitorTaskWrapper.wrap(() -> {
                                    try {
                                        addCRMAuditLog(arg, sessionContext, roleCode, funcCode2FunctionPojo);

                                        pushSessionByRoleFunctionChange(arg.getUpdatedViewListFuncCode(), sessionContext, roleCode);
                                        //计算diff 新的和老的有什么不同
                                        updateRoleBindFunctionCodeChangeRecordType(sessionContext, arg.getRoleCode(), oldFunctionPojoList, argFunctionCodeList);

                                    } catch (Exception e) {
                                        log.error("updateRolePrivilege", e);
                                    }
                                }
                        )
                );
            }
            return response(result, baseResult);
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "updateRoleFuncPermiss", JSONObject.toJSON(arg), e);
            return errorResponse(result, e.getMessage());
        }
    }

    private void pushSessionByRoleFunctionChange(List<String> viewListFunctionCode, SessionContext sessionContext, String roleCode) throws ApiException {
        if (CollectionUtils.isEmpty(viewListFunctionCode)) {
            return;
        }
        //获取角色下的人
        List<String> userIds = getRoleUsers(roleCode, sessionContext);
        List<Integer> userIdsInt = Lists.newArrayList();
        userIds.forEach(
                userId -> userIdsInt.add(Integer.valueOf(userId))
        );
        if (CollectionUtils.isEmpty(userIdsInt)) {
            return;
        }
        //推送企信消息,通知终端 对象改变了
        pushSessionService.pushSessionByFieldPrivilegeUpdate(userIdsInt, sessionContext.getEa(), Lists.newArrayList("1", "2"));
    }

    private void addCRMAuditLog(UpdateRolePrivilegeArg arg, SessionContext sessionContext, String roleCode, Map<String, FunctionPojo> funcCode2FunctionPojo) {
        Set<String> updatedFuncCodes = Sets.newHashSet();
        for (String funcCode : arg.getFuncCode()) {
            FunctionPojo tempPojo = funcCode2FunctionPojo.get(funcCode);
            if (tempPojo == null || (!tempPojo.getIsEnabled())) {
                funcCode2FunctionPojo.remove(funcCode);
                updatedFuncCodes.add(funcCode);
            }
        }
        funcCode2FunctionPojo.forEach((funCode, pojo) -> {
            if (pojo.getIsEnabled()) {
                updatedFuncCodes.add(funCode);
            }
        });
        if (CollectionUtils.isNotEmpty(updatedFuncCodes)) {
            List<String> textMessages = Lists.newArrayList();
            Set<String> objectNames = GetRoleUtils.getObjectName(updatedFuncCodes);
            String roleName = getRoleName(roleCode, sessionContext);
            objectNames.forEach(objectName -> textMessages.add(I18N.text(I18NKey.ROLE_FUNC_PRIVILEGE, roleName, objectName)));
            if (textMessages.size() > 0) {
                AuditLogUtil.createCrmAuditLog(textMessages, AuditLog.MODIFY, sessionContext.getUserIdString(), sessionContext.getEId().toString());
            }
        }
    }

    private BaseResult updateRoleFunctionCode(String roleCode, AuthContext authContext, List<String> argFunctionCodeList) throws ApiException {
        BaseResult baseResult;
        UpdateRoleFuncPermissionArg permissionArg = new UpdateRoleFuncPermissionArg();
        permissionArg.setAuthContext(authContext);
        permissionArg.setRoleCode(roleCode);
        permissionArg.setFuncCode(argFunctionCodeList);
        baseResult = funcPermissionApi.updateRoleFuncPermiss(permissionArg);
        return baseResult;
    }

    private String getApiNameByFunctionCode(String funcCode) {
        String[] tmpObjectApiNames = funcCode.split("\\|\\|");
        return tmpObjectApiNames[0];
    }

    private void updateRoleBindFunctionCodeChangeRecordType(SessionContext sessionContext, String roleCode, List<FunctionPojo> oldFunctionPojo, List<String> argFuncCodeList) {

        Set<String> argFuncCodeSet = Sets.newHashSet();

        Set<String> toAddFunctionCodeSet = Sets.newHashSet();
        Set<String> toRemoveFunctionCodeSet = Sets.newHashSet();

        Set<String> oldFunctionCodeSet = oldFunctionPojo.stream().filter(e -> Objects.equals(e.getIsEnabled(), Boolean.TRUE)).map(FunctionPojo::getFuncCode).collect(Collectors.toSet());

        if (CollectionUtils.isNotEmpty(argFuncCodeList)) {
            argFuncCodeSet = Sets.newHashSet(argFuncCodeList);
            toAddFunctionCodeSet.addAll(argFuncCodeList);
            toAddFunctionCodeSet.removeAll(oldFunctionCodeSet);
        }

        if (CollectionUtils.isNotEmpty(oldFunctionCodeSet)) {
            toRemoveFunctionCodeSet.addAll(oldFunctionCodeSet);
            toRemoveFunctionCodeSet.removeAll(argFuncCodeList);
        }

        addRoleRecordType(sessionContext, roleCode, argFuncCodeSet, toAddFunctionCodeSet, oldFunctionCodeSet);

        removeRoleRecordType(sessionContext, roleCode, argFuncCodeSet, toRemoveFunctionCodeSet, oldFunctionCodeSet);
    }

    private void removeRoleRecordType(SessionContext sessionContext, String roleCode, Set<String> argFuncCodeList, Set<String> toRemoveFunctionCodeSet, Set<String> oldFunctionCodeSet) {
        Set<String> toRemoveApiName = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(toRemoveFunctionCodeSet)) {
            for (String functionCode : toRemoveFunctionCodeSet) {
                final String apiName = getApiNameByFunctionCode(functionCode);
                final String createActionCode = getFunctionCodeByActionCode(apiName, ObjectAction.CREATE);
                final String importActionCode = getFunctionCodeByActionCode(apiName, ObjectAction.BATCH_IMPORT);
                final String modifyActionCode = getFunctionCodeByActionCode(apiName, ObjectAction.UPDATE);
                //保存前 （新建 = Yes）或（导入 = Yes）
                if (oldFunctionCodeSet.contains(createActionCode) || oldFunctionCodeSet.contains(importActionCode) || oldFunctionCodeSet.contains(modifyActionCode)) {
                    //保存后（新建 = No） 且 （导入 = No）
                    if (!argFuncCodeList.contains(createActionCode) && !argFuncCodeList.contains(importActionCode) && !argFuncCodeList.contains(modifyActionCode)) {
                        toRemoveApiName.add(apiName);
                    }
                }
            }
        }
        removeObjectsRoleRecordType(sessionContext, roleCode, toRemoveApiName);
    }

    private void addRoleRecordType(SessionContext sessionContext, String roleCode, Set<String> argFuncCodeList, Set<String> toAddFunctionCodeSet, Set<String> oldFunctionCodeSet) {

        Set<String> toAddApiName = Sets.newHashSet();

        if (CollectionUtils.isNotEmpty(toAddFunctionCodeSet)) {
            for (String functionCode : toAddFunctionCodeSet) {
                final String apiName = getApiNameByFunctionCode(functionCode);
                final String createActionCode = getFunctionCodeByActionCode(apiName, ObjectAction.CREATE);
                final String importActionCode = getFunctionCodeByActionCode(apiName, ObjectAction.BATCH_IMPORT);
                final String modifyActionCode = getFunctionCodeByActionCode(apiName, ObjectAction.UPDATE);
                //保存前（新建 = No） 且 （导入 = No）
                if (!oldFunctionCodeSet.contains(createActionCode) && !oldFunctionCodeSet.contains(importActionCode) && !oldFunctionCodeSet.contains(modifyActionCode)) {
                    //保存后（新建 = Yes）或（导入 = Yes）
                    if (argFuncCodeList.contains(createActionCode) || argFuncCodeList.contains(importActionCode) || argFuncCodeList.contains(modifyActionCode)) {
                        toAddApiName.add(apiName);
                    }
                }
            }
        }
        addObjectsRoleRecordType(sessionContext, roleCode, toAddApiName);
    }

    private void removeObjectsRoleRecordType(SessionContext sessionContext, String roleCode, Set<String> toRemoveApiName) {
        for (String apiName : toRemoveApiName) {
            final UpdateRoleRecordTypeModel.Arg arg = new UpdateRoleRecordTypeModel.Arg();
            arg.setRecordTypePojos(Lists.newArrayList());
            arg.setEntityId(apiName);
            arg.setAuthContext(sessionContext);
            final BatchDeleteRoleEntityRelation.Arg batchDeleteRoleEntityRelation = new BatchDeleteRoleEntityRelation.Arg();
            batchDeleteRoleEntityRelation.setEntityId(apiName);
            batchDeleteRoleEntityRelation.setRoleCodes(Lists.newArrayList(roleCode));
            batchDeleteRoleEntityRelation.setAuthContext(sessionContext);
            recordTypeAuthProxy.batchDeleteRoleEntityRelation(batchDeleteRoleEntityRelation,
                    PAAS_PRIVILEGE_HEADDER.buildHeader(String.valueOf(sessionContext.getEId())));
        }
    }

    /**
     * 处理商品产品功能权限联动逻辑
     *
     * @param funcCodes
     */
    private void processSPUProductFuncCodeChainReact(List<String> funcCodes) {

        Set<String> productChainReactFuncCodes = Sets.newHashSet();
        funcCodes.forEach(funcCode -> {
            if (spuProductChainReactFuncCodes.contains(funcCode)) {
                productChainReactFuncCodes.add(funcCode.replace("SPUObj", "ProductObj"));
            }
        });
        funcCodes.addAll(productChainReactFuncCodes);
    }

    private List<FunctionPojo> getRoleFunctionInfoList(String roleCode, AuthContext authContext) throws ApiException {
        RoleFuncPermissionArg roleFuncPermissionArg = new RoleFuncPermissionArg();
        roleFuncPermissionArg.setAuthContext(authContext);
        roleFuncPermissionArg.setRoleCode(roleCode);

        FuncPermissResponse baseResult = funcPermissionApi.roleFuncPermiss(roleFuncPermissionArg);
        return baseResult.getResult();
    }


    public BaseFcpServiceResult updateRoleObjectFieldPrivilege(UpdateRoleObjectFieldPrivilegeArg arg, SessionContext context) {
        BaseFcpServiceResult result = new BaseFcpServiceResult();
        String roleCode = arg.getRoleCode();

        if (StringUtils.isBlank(roleCode) || arg.getFieldPermission() == null) {
            return parameterIsNullOrBlank();
        }

        //对管理员进行限制,不许更改
        if (roleCode.equals(GetRoleUtils.getAdminRoleCode())) {
            result.setErrorCode(CRMErrorCode.CRM_ADMIN_MODIFY_ERROR.getCode());
            result.setErrorMessage(CRMErrorCode.CRM_ADMIN_MODIFY_ERROR.getMessage());
            return result;
        }
        AuthContext authContext = getAuthContext(context);
        if (StringUtils.isBlank(roleCode)) {
            throw new IllegalArgumentException("request roleCode is wrong! roleCode -" + roleCode);
        }
        UpdateRoleFieldPermissionArg updateRoleFieldPermissionArg = new UpdateRoleFieldPermissionArg();
        updateRoleFieldPermissionArg.setAuthContext(authContext);
        updateRoleFieldPermissionArg.setRoleCode(roleCode);
        String descApiName = arg.getDescApiName();
        Integer entityId = PrivilegeFieldsUtils.getCrmRequestCode(descApiName);

        Map<String, Integer> fieldName2Status = arg.getFieldPermission();
        if ("SPUObj".equals(descApiName)) {
            UpdateRoleFieldPermissionArg productFieldPermissionArg = new UpdateRoleFieldPermissionArg();
            productFieldPermissionArg.setAuthContext(authContext);
            productFieldPermissionArg.setRoleCode(roleCode);
            productFieldPermissionArg.setEntityId("ProductObj");
            Map<String, Integer> fieldPermissionMap = Maps.newHashMap();
            fieldName2Status.forEach((field, permission) -> {
                if ("standard_price".equals(field)) {
                    fieldPermissionMap.put("price", permission);
                }
                if ("product_line".equals(field)) {
                    fieldPermissionMap.put("product_line", permission);
                }
                if ("unit".equals(field)) {
                    fieldPermissionMap.put("unit", permission);
                }
                if ("category".equals(field)) {
                    fieldPermissionMap.put("category", permission);
                }
                if ("batch_sn".equals(field)) {
                    fieldPermissionMap.put("batch_sn", permission);
                }
            });
            productFieldPermissionArg.setFieldPermission(fieldPermissionMap);
            try {
                fieldPermissionApi.updateFieldPermiss(productFieldPermissionArg);
            } catch (ApiException e) {
                log.error("updateProductObjObjectFieldPrivilege", e);
            }
        }
        if (fieldName2Status == null) {
            result.setErrorCode(CRMErrorCode.PARAMETER_IS_WRONG.getCode());
            result.setErrorMessage(CRMErrorCode.PARAMETER_IS_WRONG.getMessage());
            return result;
        }

        BaseResult baseResult;
        if (entityId == null) {
            updateRoleFieldPermissionArg.setEntityId(descApiName);
            //处理group类型的字段的子字段的字段权限
            fieldName2Status.putAll(getGroupChildrenField2Status(authContext.getTenantId(), arg.getFieldPermission(), arg.getDescApiName()));
        } else {
            updateRoleFieldPermissionArg.setEntityId(String.valueOf(entityId));
        }

        updateRoleFieldPermissionArg.setFieldPermission(fieldName2Status);
        try {
            baseResult = fieldPermissionApi.updateFieldPermiss(updateRoleFieldPermissionArg);
            if (baseResult.getSuccess()) {
                try {
                    String roleName = getRoleName(roleCode, context);
                    String objectDisplayName = GetRoleUtils.getObjectDisplayName(roleCode, descApiName);
                    if (objectDisplayName == null) {
                        objectDisplayName = "";
                    }
                    String text = I18N.text(I18NKey.ROLE_FIELD_PRIVILEGE, roleName, objectDisplayName);
                    AuditLogUtil.createCrmAuditLog(Lists.newArrayList(text), AuditLog.MODIFY, context.getUserIdString(), context.getEId().toString());
                    //获取角色下的人
                    List<String> userIds = getRoleUsers(roleCode, context);
                    List<Integer> userIdsInt = Lists.newArrayList();
                    userIds.forEach(
                            userId -> userIdsInt.add(Integer.valueOf(userId))
                    );
                    //推送企信消息
                    if (!userIdsInt.isEmpty()) {
                        if (entityId == null) {
                            pushSessionService.pushSessionByFieldPrivilegeUpdate(userIdsInt, context.getEa(), Lists.newArrayList(descApiName));
                        } else {
                            pushSessionService.pushSessionByFieldPrivilegeUpdate(userIdsInt, context.getEa(), Lists.newArrayList(entityId.toString()));
                        }
                    }
                } catch (Exception e) {
                    log.error("updateRoleObjectFieldPrivilege", e);
                }
            }
            return response(result, baseResult);
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "updateFieldPermiss", JSONObject.toJSON(arg), e);
            return errorResponse(result, e.getMessage());
        }
    }


    public BaseFcpServiceResult roleAddUsers(RoleAddUsersArg arg, SessionContext sessionContext) {
        AuthContext authContext = getAuthContext(sessionContext);
        String roleCode = arg.getRoleCode();
        if (StringUtils.isBlank(roleCode) || CollectionUtils.isEmpty(arg.getUsers())) {
            return parameterIsNullOrBlank();
        }
        BaseFcpServiceResult result = new BaseFcpServiceResult();
        if (roleCode.equals(GetRoleUtils.ORDER_GOODS_ROLE_CODE)) {
            return result;
        }

        List<String> userIdsAdd2Role = arg.getUsers();

        BaseResult baseResult;
        AddRoleUserArg addArg = new AddRoleUserArg();
        addArg.setAuthContext(authContext);
        addArg.setUsers(userIdsAdd2Role);
        addArg.setRoles(Lists.newArrayList(roleCode));
        addArg.setUpdateDefaultRole(Boolean.FALSE);
        addArg.setDefaultRole(roleCode);

        Map<String, Set<String>> users2RoleCodes;
        try {
            users2RoleCodes = getUsersRoleCodesWithoutPersonnelRole(sessionContext, Sets.newHashSet(arg.getUsers()));
        } catch (ApiException e) {
            return errorResponse(result, e.getMessage());
        }
        //获得那些人 需要开通可见范围
        List<Integer> needOpenCrmVisibleUsers = getNeedOpenCrmVisibleUsers(users2RoleCodes);

        //开通crm可见范围
        CrmResult crmResult;
        if (needOpenCrmVisibleUsers.isEmpty()) {
            crmResult = new CrmResult();
            crmResult.setCode(CrmResult.SUCCESS);
        } else {
            crmResult = syncCrmVisibleService.syncOpenCrmVisible(needOpenCrmVisibleUsers, sessionContext.getEa());
        }
        if (crmResult.getCode() == CrmResult.SUCCESS) {
            //将需要开通crm可见范围的用户移除禁用名单
            com.facishare.paas.appframework.privilege.dto.AuthContext forbiddenUsersAuthContext =
                    com.facishare.paas.appframework.privilege.dto.AuthContext.builder()
                            .userId(authContext.getUserId())
                            .appId(authContext.getAppId())
                            .tenantId(authContext.getTenantId())
                            .build();
            ForbiddenUsers.Arg forbiddenUsersArg = new ForbiddenUsers.Arg(forbiddenUsersAuthContext, arg.getUsers(), false);
            forbiddenUsersService.updateForbiddenUsers(forbiddenUsersArg);

            try {
                baseResult = paasAuthApiApi.addRoleToUser(addArg);
                if (baseResult.getSuccess()) {
                    try {
                        //获取实际被增加角色的人
                        List<Integer> realAddRoleUsers = getRealAddRoleUsers(users2RoleCodes, roleCode);
                        if (!realAddRoleUsers.isEmpty()) {
                            //通知用户角色被增加的CRM角色
                            String roleName = getRoleName(roleCode, sessionContext);
                            //crm通知
                            AddRemindRecordUtil.callRemindRecordMethod(sessionContext, realAddRoleUsers, roleName, AddRemindRecordUtil.getAddDec());
                            //管理日志
                            AuditLogUtil.createCrmAuditLog(roleName, realAddRoleUsers, AuditLog.ADD, sessionContext.getUserIdString(), sessionContext.getEId().toString());
                            //发送企信通知
                            pushSessionService.pushSessionByFieldPrivilegeUpdate(realAddRoleUsers, sessionContext.getEa(), null);
                        }
                    } catch (Exception e) {
                        log.error("roleAddUsers", e);
                    }
                }
                return response(result, baseResult);
            } catch (ApiException e) {
                log.error(Constant.paas_error_log + " m={} p={}", "addRoleToUser", JSONObject.toJSON(arg), e);
                return errorResponse(result, e.getMessage());
            }
        } else {
            result.setErrorCode(crmResult.getCode());
            result.setErrorMessage(crmResult.getMsg());
            result.setResult(crmResult.getMsg());
            return result;
        }
    }


    private List<Integer> getRealAddRoleUsers(Map<String, Set<String>> users2RoleCodes, String roleCode) {
        List<Integer> realUpdateRoleUsers = Lists.newArrayList();
        users2RoleCodes.forEach((userId, roleCodes) -> {
            if (!roleCodes.contains(roleCode)) {
                realUpdateRoleUsers.add(Integer.valueOf(userId));
            }
        });
        return realUpdateRoleUsers;
    }


    private List<Integer> getNeedOpenCrmVisibleUsers(Map<String, Set<String>> users2RoleCodes) {
        List<Integer> needOpenCrmVisibleUsers = Lists.newArrayList();

        users2RoleCodes.forEach((userId, roles) -> {
            if (roles.isEmpty()) {
                needOpenCrmVisibleUsers.add(Integer.valueOf(userId));
            }
        });
        return needOpenCrmVisibleUsers;
    }


    private Map<String, Set<String>> getUsersRoleCodesWithoutPersonnelRole(SessionContext sessionContext, Set<String> userIds) throws ApiException {
        Map<String, Set<String>> userId_roles = Maps.newHashMap();

        ApiClient apiClient = SwaggerClientUtil.getApiClient(Constant.BASE_URL_AUTH_KEY, props);
        UserRoleApi api = new UserRoleApi(apiClient);
        QueryUserRoleCodesByUsersArg arg = new QueryUserRoleCodesByUsersArg();
        AuthContext authContext = getAuthContext(sessionContext);
        arg.setAuthContext(authContext);
        arg.setUsers(Lists.newArrayList(userIds));
        try {
            Map<String, List<String>> result = api.queryUserRoleCodesByUsers(arg).getResult();
            for (Map.Entry<String, List<String>> e : result.entrySet()) {
                userId_roles.put(e.getKey(), getRoleCodesWithoutPersonnelRole(e.getValue()));
            }
            return userId_roles;
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "queryUserRoleCodesByUsers", JSONObject.toJSON(arg), e);
            throw e;
        }
    }

    private HashSet<String> getRoleCodesWithoutPersonnelRole(List<String> value) {
        if (CollectionUtils.isEmpty(value)) {
            return Sets.newHashSet();
        }
        value.remove(GetRoleUtils.PERSONNEL_ROLE_CODE);
        return Sets.newHashSet(value);
    }


    public BaseFcpServiceResult roleRemoveUsers(RoleRemoveUsersArg arg, SessionContext sessionContext) {
        AuthContext authContext = getAuthContext(sessionContext);
        BaseFcpServiceResult result = new BaseFcpServiceResult();
        String roleCode = arg.getRoleCode();
        List<String> userIds = arg.getUsers();

        if (StringUtils.isBlank(roleCode) || CollectionUtils.isEmpty(userIds)) {
            return parameterIsNullOrBlank();
        }
        //禁止管理员删除自己的限定
        if (roleCode.equals(GetRoleUtils.getAdminRoleCode())) {
            String currentUserId = sessionContext.getUserIdString();
            if (userIds.contains(currentUserId)) {
                result.setErrorMessage(CRMErrorCode.ROLE_REMOVE_USER_ERROR.getMessage());
                result.setErrorCode(CRMErrorCode.ROLE_REMOVE_USER_ERROR.getCode());
                result.setResult(CRMErrorCode.ROLE_REMOVE_USER_ERROR.getMessage());
                return result;
            }
        }

        Map<String, Set<String>> users2RoleCodes;
        try {
            users2RoleCodes = getUsersRoleCodesWithoutPersonnelRole(sessionContext, Sets.newHashSet(arg.getUsers()));
        } catch (ApiException e) {
            return errorResponse(result, e.getMessage());
        }
        //需要被删除角色的员工
        List<Integer> needDeleteRoleUsers = getRealRemoveRoleUsers(users2RoleCodes, roleCode);

        if (needDeleteRoleUsers.isEmpty()) {
            result.setResult(0);
            return result;
        }
        BaseResult baseResult = new BaseResult();

        try {
            List<String> onlyOneRoleUserIds = findOnlyOneRoleUserIds(roleCode, needDeleteRoleUsers.stream().map(String::valueOf).collect(Collectors.toList()), sessionContext);
            if (CollectionUtils.isNotEmpty(onlyOneRoleUserIds)) {
                final UpdateRoleUserArg body = new UpdateRoleUserArg();
                body.setAuthContext(authContext);
                body.setUsers(Lists.newArrayList(onlyOneRoleUserIds));
                body.setRoles(Lists.newArrayList());
//                body.setDefaultRole("");
                baseResult = paasAuthApiApi.updateRoleToUser(body);
                Integer errorCode = baseResult.getErrCode();
                if (errorCode != 0) {
                    log.error("failed errorCode-{},errorMessage-{}", errorCode, baseResult.getErrMessage());
                    return errorResponse(result, baseResult.getErrMessage());
                }
            }

            List<String> mulRoleUserIds = Lists.newArrayList(needDeleteRoleUsers.stream().map(String::valueOf).collect(Collectors.toList()));
            mulRoleUserIds.removeAll(onlyOneRoleUserIds);

            if (CollectionUtils.isNotEmpty(mulRoleUserIds)) {
                DelUserFromRoleArg delUserFromRoleArg = new DelUserFromRoleArg();
                delUserFromRoleArg.setAuthContext(authContext);
                delUserFromRoleArg.setRoleCode(roleCode);
                delUserFromRoleArg.setUsers(mulRoleUserIds);
                baseResult = paasAuthApiApi.delRoleUser(delUserFromRoleArg);
                Integer errorCode = baseResult.getErrCode();
                if (errorCode != 0) {
                    log.error("failed errorCode-{},errorMessage-{}", errorCode, baseResult.getErrMessage());
                    return errorResponse(result, baseResult.getErrMessage());
                }
            }

            String roleName = getRoleName(roleCode, sessionContext);
            //通知用户角色被删除的CRM通知
            AddRemindRecordUtil.callRemindRecordMethod(sessionContext, needDeleteRoleUsers, roleName, AddRemindRecordUtil.getDeleteDec());
            //添加管理日志
            AuditLogUtil.createCrmAuditLog(roleName, needDeleteRoleUsers, AuditLog.DELETE, sessionContext.getUserIdString(), sessionContext.getEId().toString());
            //企信推送session
            pushSessionService.pushSessionByFieldPrivilegeUpdate(needDeleteRoleUsers, sessionContext.getEa(), null);
            //找出没有角色的员工
            List<Integer> deleteRoleUsers = getNotHaveRoleUsers(users2RoleCodes, needDeleteRoleUsers);
            //移除crm可见范围
            if (CollectionUtils.isNotEmpty(deleteRoleUsers)) {
                CrmResult crmResult = syncCrmVisibleService.syncCloseCrmVisible(deleteRoleUsers, sessionContext.getEa());
                if (crmResult.getCode() != 200) {
                    return errorResult(result, crmResult);
                }
            }
            return response(result, baseResult);
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "delUserFromRole", JSONObject.toJSON(arg), e);
            return errorResponse(result, e.getMessage());
        }

    }

    private BaseFcpServiceResult errorResult(BaseFcpServiceResult result, CrmResult crmResult) {
        result.setErrorCode(crmResult.getCode());
        result.setResult(crmResult.getMsg());
        result.setErrorMessage(crmResult.getMsg());
        return result;
    }

    private List<String> findOnlyOneRoleUserIds(String roleCode, List<String> userIds, SessionContext sessionContext) {
        try {
            QueryUserRolesByUsersArg usersArg = new QueryUserRolesByUsersArg();
            usersArg.setAuthContext(getAuthContext(sessionContext));
            usersArg.setUsers(userIds);
            usersArg.setExcludeRoles(Lists.newArrayList(dhtRoleCode));
            QueryRoleInfoListByUsersResponse response;
            response = paasAuthApiApi.queryRoleInfoListByUsers(usersArg);
            final List<String> multOrAdminRoleUserIds = response.getResult()
                    .getUserRoles().entrySet().stream()
                    .filter(entry -> {
                        if (entry.getValue().stream().anyMatch(e -> Objects.equals(e.getRoleCode(), GetRoleUtils.getAdminRoleCode()))) {
                            return true;
                        }
                        final Set<String> collect = entry.getValue().stream()
                                .filter(po -> !Objects.equals(po.getRoleCode(), GetRoleUtils.PERSONNEL_ROLE_CODE))
                                .filter(po -> !Objects.equals(po.getRoleCode(), roleCode))
                                .map(UserRoleInfoPojo::getRoleCode).collect(Collectors.toSet());
                        if (collect.size() > 0) {
                            return true;
                        }
                        return false;
                    }).map(Map.Entry::getKey).collect(Collectors.toList());
            List<String> onlyOneRoleUserIds = Lists.newArrayList(userIds);
            onlyOneRoleUserIds.removeAll(multOrAdminRoleUserIds);
            return onlyOneRoleUserIds;
        } catch (ApiException e) {
            log.error(e.getMessage(), e);
        }
        return Lists.newArrayList();
    }

    private List<Integer> getRealRemoveRoleUsers(Map<String, Set<String>> users2RoleCodes, String roleCode) {
        List<Integer> realUpdateRoleUsers = Lists.newArrayList();
        users2RoleCodes.forEach((userId, roleCodes) -> {
            if (roleCodes.contains(roleCode)) {
                realUpdateRoleUsers.add(Integer.valueOf(userId));
            }
        });
        return realUpdateRoleUsers;
    }

    private List<Integer> getNotHaveRoleUsers(Map<String, Set<String>> users2RoleCodes, List<Integer> needDeleteRoleUsers) {
        List<Integer> notHaveRoleUsers = Lists.newArrayList();
        needDeleteRoleUsers.forEach(
                userId -> {
                    if (users2RoleCodes.get(userId.toString()).size() == 1) {
                        notHaveRoleUsers.add(userId);
                    }
                });
        return notHaveRoleUsers;
    }


    private BaseFcpServiceResult errorResponse(BaseFcpServiceResult result, String errorMsg) {
        result.setErrorMessage(errorMsg);
        result.setErrorCode(CRMErrorCode.PAAS_PRIVILEGE_FAILED.getCode());
        result.setResult(errorMsg);
        return result;
    }


    public BaseFcpServiceResult addRole(AddRoleArg arg, SessionContext context) {
        if (StringUtils.isBlank(arg.getRoleName())) {
            return parameterIsNullOrBlank();
        }

        AuthContext authContext = getAuthContext(context);
        RoleInfoArg roleInfoArg = new RoleInfoArg();
        roleInfoArg.setAuthContext(authContext);
        roleInfoArg.setRoleType(USER_DEFINED_ROLE_FLAG);
        BaseFcpServiceResult result = new BaseFcpServiceResult();

        int userDefinedRoleCount;

        RoleResponse response;
        try {
            response = userRoleApi.roleInfo(roleInfoArg);
            if (!response.getSuccess()) {
                result.setErrorCode(response.getErrCode());
                result.setErrorMessage(response.getErrMessage());
                return result;
            }
            userDefinedRoleCount = response.getResult().getRoles().size();
        } catch (ApiException e) {
            log.error("[addRole] roleInfo error. arg- {}", roleInfoArg, e);
            return paasError(e.getMessage());
        }

        int userDefinedRoleQuota = getUserDefinedRoleQuota(context.getEa());

        if (userDefinedRoleCount >= userDefinedRoleQuota) {
            return userDefinedRoleQuotaError(userDefinedRoleQuota);
        }

        CreateRoleArg createRoleArg = new CreateRoleArg();
        createRoleArg.setAuthContext(authContext);
        createRoleArg.setRoleName(arg.getRoleName());
        createRoleArg.setDescription(arg.getDescription());
        createRoleArg.setRoleType(arg.getRoleType() == null ? Integer.valueOf(2) : arg.getRoleType());

        AddRoleResponse baseResult;
        try {
            baseResult = userRoleApi.addRole(createRoleArg);
            Integer errorCode = baseResult.getErrCode();
            result.setErrorMessage(baseResult.getErrMessage());
            result.setErrorCode(errorCode);
            String roleCodeTemp = null;
            if (baseResult.getResult() == null) {
                result.setResult("");
            } else {
                roleCodeTemp = baseResult.getResult();
                result.setResult(roleCodeTemp);
            }
            boolean isSuccess = baseResult.getSuccess();
            if (isSuccess && StringUtils.isNotBlank(roleCodeTemp)) {
                //添加管理日志
                String roleName = getRoleName(roleCodeTemp, context);
                String textMessage = I18N.text(I18NKey.ROLE_NAME, roleName);
                AuditLogUtil.createCrmAuditLog(Lists.newArrayList(textMessage), AuditLog.ADD, context.getUserIdString(), context.getEId().toString());
            }
            return result;
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "addRole", JSONObject.toJSON(arg), e);
            return errorResponse(result, e.getMessage());
        }
    }

    private int getUserDefinedRoleQuota(String ea) {
        if (customRoleEaSet.contains(ea)) {
            return customRoleSize;
        }
        return 100;
    }

    public void addObjectsRoleRecordType(SessionContext context, String roleCode, Set<String> addApiNameSet) {
        if (CollectionUtils.isEmpty(addApiNameSet)) {
            return;
        }
        final String tenantId = context.getEId().toString();

        final Set<String> objectDescribes = findActiveObjectApiNamesByTenantId(tenantId);

        Set<String> toAddApiNameSet = addApiNameSet.stream().filter(objectDescribes::contains).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(toAddApiNameSet)) {
            return;
        }

        for (String apiName : toAddApiNameSet) {
            //1、查询
            final List record_list = recordTypeLogicService.findRecordTypeList(tenantId, apiName, true).getRecord_list();
            record_list.forEach(recordType -> {
                final Map<String, Object> iRecordTypeOption = (Map<String, Object>) recordType;

                final AddRoleRecordTypeModel.Arg arg = new AddRoleRecordTypeModel.Arg();
                final RecordTypePojo recordTypePojo = new RecordTypePojo();
                recordTypePojo.setTenantId(tenantId);
                recordTypePojo.setAppId(context.getAppId());
                recordTypePojo.setRoleCode(roleCode);
                recordTypePojo.setEntityId(apiName);
                final String recordTypeId = iRecordTypeOption.get("api_name").toString();
                recordTypePojo.setRecordTypeId(recordTypeId);
                recordTypePojo.setDefaultType(Objects.equals(recordTypeId, "default__c"));
                arg.setRecordTypePojos(Lists.newArrayList(recordTypePojo));
                arg.setEntityId(apiName);
                arg.setRecordTypeId(recordTypeId);
                arg.setAuthContext(context);
                recordTypeAuthProxy.addRoleRecordType(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(tenantId));
            });
        }
    }

    private String getFunctionCodeByActionCode(String apiName, ObjectAction create) {
        return apiName + "||" + create.getActionCode();
    }

    private String getRoleName(String roleCode, SessionContext sessionContext) {
        AuthContext authContext = getAuthContext(sessionContext);
        RoleInfoArg arg = new RoleInfoArg();
        arg.setAuthContext(authContext);
        arg.setRoleCode(roleCode);
        try {
            RoleResponse result = userRoleApi.roleInfo(arg);
            List<RolePojo> rolePojoList = result.getResult().getRoles();
            if (CollectionUtils.isEmpty(rolePojoList)) {
                log.error("paas roleInfo method wrong! request arg -{}", JSON.toJSONString(arg));
                return "";
            }
            RolePojo rolePojo = rolePojoList.get(0);
            return rolePojo.getRoleName();
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "roleInfo", JSONObject.toJSON(arg), e);
        }
        return null;
    }


    private BaseFcpServiceResult defaultRoleDelete() {
        BaseFcpServiceResult result = new BaseFcpServiceResult();
        result.setErrorCode(CRMErrorCode.DEFAULT_ROLE_DELETE_ERROR.getCode());
        result.setErrorMessage(CRMErrorCode.DEFAULT_ROLE_DELETE_ERROR.getMessage());
        return result;
    }

    public BaseFcpServiceResult deleteRole(DeleteRoleArg arg, SessionContext context) {
        BaseFcpServiceResult result = new BaseFcpServiceResult();
        String roleCode = arg.getRoleCode();
        //对管理员进行限制,不许更改
        if (roleCode.equals(GetRoleUtils.getAdminRoleCode())) {
            result.setErrorCode(CRMErrorCode.CRM_ADMIN_MODIFY_ERROR.getCode());
            result.setErrorMessage(CRMErrorCode.CRM_ADMIN_MODIFY_ERROR.getMessage());
            return result;
        }
        if (StringUtils.isBlank(roleCode)) {
            return parameterIsNullOrBlank();
        }
        Set<String> defaultRoleCodeSet = GetRoleUtils.getDefaultRoleCodeSet();
        if (defaultRoleCodeSet.contains(roleCode)) {
            return defaultRoleDelete();
        }
        AuthContext authContext = getAuthContext(context);

        DelRoleArg deleteArg = new DelRoleArg();
        deleteArg.setAuthContext(authContext);
        deleteArg.setRoleCode(roleCode);
        BaseResult baseResult;
        try {
            //角色被删除前,角色下的用户
            List<String> userIds = getRoleUsers(roleCode, context);
            String roleName = getRoleName(roleCode, context);
            baseResult = paasAuthApiApi.delDefinedRole(deleteArg);

            if (baseResult.getSuccess() && !userIds.isEmpty()) {
                try {
                    if (CollectionUtils.isNotEmpty(userIds)) {
                        List<Integer> userIdIntegers = Lists.newArrayList();
                        userIds.forEach(userId -> userIdIntegers.add(Integer.valueOf(userId)));

                        //通知用户角色被删除的CRM通知的代码
                        AddRemindRecordUtil.callRemindRecordMethod(context, userIdIntegers, roleName, AddRemindRecordUtil.getDeleteDec());
                        //企信发session
                        pushSessionService.pushSessionByFieldPrivilegeUpdate(userIdIntegers, context.getEa(), null);
                    }
                    List<Integer> userIdsInt;
                    //添加管理日志
                    if (openAuditLogFlag == 1) {
                        String textMessage = I18N.text(I18NKey.ROLE_NAME, roleName);
                        AuditLogUtil.createCrmAuditLog(Lists.newArrayList(textMessage), AuditLog.DELETE, context.getUserIdString(), context.getEId().toString());

                        userIdsInt = Lists.newArrayList();
                        userIdsInt.addAll(userIds.stream().map(Integer::valueOf).collect(Collectors.toList()));

                        //添加员工被删除角色的管理日志
                        if (!userIdsInt.isEmpty()) {
                            AuditLogUtil.createCrmAuditLog(roleName, userIdsInt, AuditLog.DELETE, context.getUserIdString(), context.getEId().toString());
                        }
                    }

                    Map<String, Set<String>> users2RoleCodes;
                    try {
                        users2RoleCodes = getUsersRoleCodesWithoutPersonnelRole(context, Sets.newHashSet(userIds));
                    } catch (ApiException e) {
                        return errorResponse(result, e.getMessage());
                    }

                    //同步crm可见范围
                    List<Integer> needCloseCrmVisibleUserIds = Lists.newArrayList();
                    users2RoleCodes.forEach((userId, roleCodes) -> {

                        if (CollectionUtils.isEmpty(roleCodes)) {
                            needCloseCrmVisibleUserIds.add(Integer.parseInt(userId));
                        }

                    });
                    if (CollectionUtils.isNotEmpty(needCloseCrmVisibleUserIds)) {
                        syncCrmVisibleService.syncCloseCrmVisible(needCloseCrmVisibleUserIds, context.getEa());
                    }
                } catch (Exception e) {
                    log.error("[deleteRole]", e);
                }
            }
            return response(result, baseResult);
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "delRole", JSONObject.toJSON(arg), e);
            return errorResponse(result, e.getMessage());
        }
    }

    public BaseFcpServiceResult updateRole(UpdateRoleInfoArg arg, SessionContext context) {
        BaseFcpServiceResult result = new BaseFcpServiceResult();
        String roleCode = arg.getRoleCode();

        if (StringUtils.isBlank(roleCode) || StringUtils.isBlank(arg.getRoleName())) {
            return parameterIsNullOrBlank();
        }

        //对管理员进行限制,不许更改
        if (roleCode.equals(GetRoleUtils.getAdminRoleCode())) {
            result.setErrorCode(CRMErrorCode.CRM_ADMIN_MODIFY_ERROR.getCode());
            result.setErrorMessage(CRMErrorCode.CRM_ADMIN_MODIFY_ERROR.getMessage());
            return result;
        }
        AuthContext authContext = getAuthContext(context);

        if (StringUtils.isBlank(roleCode)) {
            throw new IllegalArgumentException("request roleCode is wrong! roleCode -" + roleCode);
        }
        UpdateRoleArg updateRoleArg = new UpdateRoleArg();
        updateRoleArg.setAuthContext(authContext);
        RolePojo rolePojo = new RolePojo();
        rolePojo.setRoleName(arg.getRoleName());
        rolePojo.setDescription(arg.getDescription());
        rolePojo.setRoleCode(roleCode);

        updateRoleArg.setRolePojo(rolePojo);
        BaseResult baseResult;
        try {
            baseResult = userRoleApi.updateRole(updateRoleArg);
            //添加管理日志
            if (baseResult.getSuccess() && openAuditLogFlag == 1) {
                String roleName = getRoleName(roleCode, context);
                String textMessage = I18N.text(I18NKey.ROLE_NAME, roleName);
                AuditLogUtil.createCrmAuditLog(Lists.newArrayList(textMessage), AuditLog.MODIFY, context.getUserIdString(), context.getEId().toString());
            }
            return response(result, baseResult);
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "updateRole", JSONObject.toJSON(arg), e);
            return errorResponse(result, e.getMessage());
        }
    }

    public BaseFcpServiceResult copyRoleAndRecordType(CopyRoleArg arg, SessionContext sessionContext) {
        final String destRoleCode = arg.getDestRoleCode();
        final String sourceRoleCode = arg.getSourceRoleCode();
        if (StringUtils.isBlank(destRoleCode) || StringUtils.isBlank(sourceRoleCode)) {
            return parameterIsNullOrBlank();
        }

        BaseFcpServiceResult result = new BaseFcpServiceResult();
        Set<String> defaultRoleCodeSet = GetRoleUtils.getDefaultRoleCodeSet();
        if (defaultRoleCodeSet.contains(destRoleCode)) {
            result.setErrorCode(CRMErrorCode.DEFAULT_ROLE_COPY_ERROR.getCode());
            result.setErrorMessage(CRMErrorCode.DEFAULT_ROLE_COPY_ERROR.getMessage());
            return result;
        }
        BaseResult baseResult;
        try {
            AuthContext authContext = getAuthContext(sessionContext);

            baseResult = copyRole(destRoleCode, sourceRoleCode, authContext);

            taskExecutor.submit(
                    MonitorTaskWrapper.wrap(() -> copyRecordType(sessionContext, sourceRoleCode, destRoleCode, authContext.getTenantId()))
            );

            return response(result, baseResult);
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "rolePermissCopy", JSONObject.toJSON(arg), e);
            return errorResponse(result, e.getMessage());
        }
    }

    private BaseResult copyRole(String destRoleCode, String sourceRoleCode, AuthContext authContext) throws ApiException {
        BaseResult baseResult;
        RolePermissCopyArg rolePermissCopyArg = new RolePermissCopyArg();
        rolePermissCopyArg.setAuthContext(authContext);
        rolePermissCopyArg.setSourceRoleCode(sourceRoleCode);
        rolePermissCopyArg.setDestRoleCode(destRoleCode);
        baseResult = userRoleApi.rolePermissCopy(rolePermissCopyArg);
        return baseResult;
    }

    private void copyRecordType(SessionContext sessionContext, String sourceRoleCode, String destRoleCode, String tenantId) {
        final Set<String> apiNameList = findActiveObjectApiNamesByTenantId(tenantId);
        //1、查询来源角色的业务类型，所有对象的绑定的业务类型
        final BatchFindRecordType.Arg findArg = BatchFindRecordType.Arg.builder().build();
        findArg.setEntityIds(Lists.newArrayList(apiNameList));
        findArg.setRoleCodes(Lists.newArrayList(sourceRoleCode));
        findArg.setAuthContext(sessionContext);
        final List<RecordTypePojo> recordTypePojoList = recordTypeAuthProxy.batchFindRecordType(findArg, PAAS_PRIVILEGE_HEADDER.buildHeader(tenantId)).getResult();

        for (RecordTypePojo recordTypePojo : recordTypePojoList) {
            AddRoleRecordTypeModel.Arg addArg = new AddRoleRecordTypeModel.Arg();
            final List<RecordTypePojo> recordTypePojos = Lists.newArrayList();
            final RecordTypePojo typePojo = new RecordTypePojo();
            typePojo.setTenantId(recordTypePojo.getTenantId());
            typePojo.setAppId(recordTypePojo.getAppId());
            typePojo.setRoleCode(destRoleCode);
            typePojo.setEntityId(recordTypePojo.getEntityId());
            typePojo.setRecordTypeId(recordTypePojo.getRecordTypeId());
            typePojo.setDefaultType(recordTypePojo.getDefaultType());
            recordTypePojos.add(typePojo);
            addArg.setRecordTypePojos(recordTypePojos);
            addArg.setAuthContext(sessionContext);
            addArg.setEntityId(recordTypePojo.getEntityId());
            addArg.setRecordTypeId(recordTypePojo.getRecordTypeId());
            recordTypeAuthProxy.addRoleRecordType(addArg, PAAS_PRIVILEGE_HEADDER.buildHeader(tenantId));
        }
    }

    private Set<String> findActiveObjectApiNamesByTenantId(String tenantId) {
        List<IObjectDescribe> supportObjectDescribes = describeLogicService.findObjectsByTenantId(tenantId, false,
                true, false, false, ObjectListConfig.FUNCTION_PRIVILEGE);
        return supportObjectDescribes.stream().map(IObjectDescribe::getApiName).collect(Collectors.toSet());
    }


    private BaseFcpServiceResult response(BaseFcpServiceResult result, BaseResult baseResult) {
        Integer errorCode = baseResult.getErrCode();
        if (errorCode == 0) {
            result.setErrorCode(errorCode);
            result.setErrorMessage(baseResult.getErrMessage());
            if (baseResult.getResult() == null) {
                result.setResult("");
            } else {
                result.setResult(baseResult.getResult());
            }
            return result;
        } else {
            log.error("failed errorCode-{},errorMessage-{}", errorCode, baseResult.getErrMessage());
            return errorResponse(result, baseResult.getErrMessage());
        }
    }

    private AuthContext getAuthContext(SessionContext sessionContext) {
        AuthContext auth = new AuthContext();
        auth.setAppId(sessionContext.getAppId() == null ? Constant.getAppId() : sessionContext.getAppId());
        auth.setUserId(sessionContext.getUserIdString());
        auth.setTenantId(sessionContext.getEId().toString());
        auth.setOuterUserId(sessionContext.getOutUserId());
        auth.setOuterTenantId(sessionContext.getOuterTenantId());
        auth.setLanguageLocale(sessionContext.getLanguageLocale());
        return auth;
    }

    private List<String> getRoleUsers(String roleCode, SessionContext sessionContext) throws ApiException {
        RoleUserInfoArg arg = new RoleUserInfoArg();
        arg.setRoleCode(roleCode);
        arg.setAuthContext(getAuthContext(sessionContext));
        RoleUserResponse roleUserResponse = userRoleApi.roleUser(arg);
        RoleUserPageInfoReponse temp = roleUserResponse.getResult();
        List<String> userIds = temp.getUsers();
        if (userIds == null) {
            return Lists.newArrayList();
        }
        return userIds;
    }

    public BaseFcpServiceResult resetRole(ResetRoleArg arg, SessionContext context) {
        BaseFcpServiceResult result = new BaseFcpServiceResult();
        String roleCode = arg.getRoleCode();

        if (StringUtils.isBlank(roleCode)) {
            return parameterIsNullOrBlank();
        }
        //对管理员进行限制,不许更改
        if (roleCode.equals(GetRoleUtils.PERSONNEL_ROLE_CODE)) {
            result.setErrorCode(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getCode());
            result.setErrorMessage(CRMErrorCode.PRIVILEGE_PARAMETER_ERROR.getMessage());
            return result;
        }

        if (roleCode.equals(GetRoleUtils.getAdminRoleCode())) {
            result.setErrorCode(CRMErrorCode.CRM_ADMIN_MODIFY_ERROR.getCode());
            result.setErrorMessage(CRMErrorCode.CRM_ADMIN_MODIFY_ERROR.getMessage());
            return result;
        }
        AuthContext authContext = getAuthContext(context);

        DelRoleFuncPermissArg delRoleFuncPermissArg = new DelRoleFuncPermissArg();
        delRoleFuncPermissArg.setAuthContext(authContext);
        delRoleFuncPermissArg.setRoleCode(roleCode);

        DelRoleFieldPermissArg delRoleFieldPermissArg = new DelRoleFieldPermissArg();
        delRoleFieldPermissArg.setRoleCode(roleCode);
        delRoleFieldPermissArg.setAuthContext(authContext);
        BaseResult baseResult;
        try {
            funcPermissionApi.delRoleFuncPermiss(delRoleFuncPermissArg);
            baseResult = fieldPermissionApi.delRoleFieldPermiss(delRoleFieldPermissArg);


            //将默认角色的
            Set<String> defaultRoleCodeSet = GetRoleUtils.getDefaultRoleCodeSet();
            if (Role.ACCOUNT_TRANSACTION_OPERATOR.getRoleCode().equals(roleCode)) {
                UpdateRoleFuncPermiss(context, roleCode, Lists.newArrayList(accountTransactionOperatorRoleProvider.getHavePermissFuncCodes()));
            } else if (defaultRoleCodeSet.contains(roleCode) && !GetRoleUtils.PARTNER_ROLE_CODE.equals(roleCode)) {

                List<String> funcCodes = getNeedSetCheckedFuncCodes4PredefinedObject(roleCode);
                UpdateRoleFuncPermiss(context, roleCode, funcCodes);
            } else if (GetRoleUtils.PARTNER_ROLE_CODE.equals(roleCode)) {
                UpdateRoleFuncPermiss(context, roleCode, Lists.newArrayList(channelManagerRoleProvider.getHavePermissFuncCodes()));
            }

            if (baseResult.getSuccess()) {
                //添加管理日志
                String roleName = getRoleName(roleCode, context);
                String textMessage = I18N.text(I18NKey.ROLE_NAME, roleName);
                AuditLogUtil.createCrmAuditLog(Lists.newArrayList(textMessage), AuditLog.RESET, context.getUserIdString(), context.getEId().toString());
            }
            return response(result, baseResult);
        } catch (ApiException e) {
            log.error(Constant.paas_error_log + " m={} p={}", "resetRole", JSONObject.toJSON(arg), e);
            return errorResponse(result, e.getMessage());
        }
    }

    @NotNull
    /**
     * 获取预制对象需要默认设置为true的权限码
     */
    private List<String> getNeedSetCheckedFuncCodes4PredefinedObject(String roleCode) {
        List<String> funcCodes = Lists.newArrayList();
        SFA_OBJECT_API_NAMES.forEach(apiName -> {
            FunctionPrivilegeProvider provider = functionPrivilegeProviderManager.getProvider(apiName);
            if (!(provider.getClass().equals(DefaultFunctionPrivilegeProvider.class))) {
                List<String> actionCodes = provider.getCustomInitRoleActionCodes().get(roleCode);
                if (actionCodes != null) {
                    List<String> tmpFuncCodes = actionCodes.stream()
                            .map(actionCode -> FunctionCodeBuilder.build(apiName, actionCode)).collect(Collectors.toList());

                    funcCodes.addAll(tmpFuncCodes);
                }
            }
        });
        supportRoleFunc(funcCodes, roleCode);
        return funcCodes;
    }

    private void supportRoleFunc(List<String> funcCodes, String roleCode) {

        List<String> roleFuncs = resetRoleFuncMap.getOrDefault(roleCode, null);

        if (CollectionUtils.isEmpty(roleFuncs)) {
            return;
        }

        funcCodes.addAll(roleFuncs);
    }

    private void UpdateRoleFuncPermiss(SessionContext context, String roleCode, List<String> funcCodes) throws ApiException {
        UpdateRoleFuncPermissionArg updateRoleFuncPermissionArg = new UpdateRoleFuncPermissionArg();
        updateRoleFuncPermissionArg.setFuncCode(funcCodes);
        updateRoleFuncPermissionArg.setAuthContext(getAuthContext(context));
        updateRoleFuncPermissionArg.setRoleCode(roleCode);
        funcPermissionApi.updateRoleFuncPermiss(updateRoleFuncPermissionArg);
    }


    public BaseFcpServiceResult updateUserDefinedPrivilege(UpdateUserDefinedPrivilegeArg arg, SessionContext sessionContext) {
        if (arg.getFuncCode2StatusMap().isEmpty()) {
            return new BaseFcpServiceResult();
        }
        final String roleCode = arg.getRoleCode();
        if (StringUtils.isBlank(arg.getDescApiName()) || StringUtils.isBlank(roleCode)) {
            return parameterIsNullOrBlank();
        }

        if (GetRoleUtils.getAdminRoleCode().equals(roleCode)) {
            BaseFcpServiceResult result = new BaseFcpServiceResult();
            result.setErrorCode(CRMErrorCode.AUTHENTICATION_ERROR.getCode());
            result.setErrorMessage(I18N.text(I18NKey.NOT_CRM_ADMIN));
            return result;
        }

        final List<String> selectedFuncCodes = Lists.newArrayList();
        final List<String> unselectedFuncCodes = Lists.newArrayList();
        Map<String, Boolean> funcCode2StatusMap = arg.getFuncCode2StatusMap();
        funcCode2StatusMap.forEach((funcCode, status) -> {
                    if (status) {
                        selectedFuncCodes.add(funcCode);
                    } else {
                        unselectedFuncCodes.add(funcCode);
                    }
                }
        );

        if (ProductUtil.isSpuOpen(String.valueOf(sessionContext.getEId()))) {
            handleLinkageFunctionCode(selectedFuncCodes, unselectedFuncCodes);
        }

        BaseFcpServiceResult result = new BaseFcpServiceResult();
        try {
            AuthContext authContext = getAuthContext(sessionContext);
            List<FunctionPojo> oldFunctionPojoList = getRoleFunctionInfoList(roleCode, authContext);
            Set<String> oldFunctionCodeSet = oldFunctionPojoList.stream().filter(e -> Objects.equals(e.getIsEnabled(), Boolean.TRUE)).map(FunctionPojo::getFuncCode).collect(Collectors.toSet());

            BaseResult baseResult = updateRoleModifiedFuncPermission(sessionContext, roleCode, selectedFuncCodes, unselectedFuncCodes);
            if (!baseResult.getSuccess()) {
                log.error(CRMErrorCode.PAAS_ERROR + " m={} p={},response -{}", "updateUserDefinedPrivilege", JSONObject.toJSON(arg), baseResult);
                result.setErrorCode(baseResult.getErrCode());
                result.setErrorMessage(baseResult.getErrMessage());
                return result;
            }
            taskExecutor.submit(
                    MonitorTaskWrapper.wrap(() -> {
                        final Set<String> selectedFuncCodeSet = Sets.newHashSet(selectedFuncCodes);
                        addRoleRecordType(sessionContext, roleCode, selectedFuncCodeSet, selectedFuncCodeSet, oldFunctionCodeSet);
                        removeRoleRecordType(sessionContext, roleCode, selectedFuncCodeSet, Sets.newHashSet(unselectedFuncCodes), oldFunctionCodeSet);
                    })
            );

            result.setErrorCode(0);
            result.setErrorMessage("success");
            return result;
        } catch (ApiException e) {
            log.error(CRMErrorCode.PAAS_ERROR + " m={} p={}", "[updateUserDefinedPrivilege] updateRoleModifiedFuncPermission", JSONObject.toJSON(arg), e);
            result.setErrorCode(CRMErrorCode.PAAS_ERROR.getCode());
            result.setErrorMessage(e.getMessage());
            return result;
        }
    }

    private void handleLinkageFunctionCode(List<String> selectedFuncCodes, List<String> unselectedFuncCodes) {
        for (int i = 0; i < selectedFuncCodes.size(); i++) {
            if (selectedFuncCodes.get(i).contains("SPUObj||Import") || selectedFuncCodes.get(i).contains("SPUObj") || selectedFuncCodes.get(i).contains("SPUObj||Add") || selectedFuncCodes.get(i).contains("SPUObj||View")
                    || selectedFuncCodes.get(i).contains("SPUObj||Edit") || selectedFuncCodes.get(i).contains("SPUObj||Abolish") || selectedFuncCodes.get(i).contains("SPUObj||Lock")
                    || selectedFuncCodes.get(i).contains("SPUObj||Unlock") || selectedFuncCodes.get(i).contains("SPUObj||ChangeOwner") || selectedFuncCodes.get(i).contains("SPUObj||EditTeamMember")
                    || selectedFuncCodes.get(i).contains("SPUObj||Print") || selectedFuncCodes.get(i).contains("SPUObj||Export") || selectedFuncCodes.get(i).contains("SPUObj||Delete")
                    || selectedFuncCodes.get(i).contains("SPUObj||Recover")) {

                if (selectedFuncCodes.get(i).equals("SPUObj||Add")) {
                    selectedFuncCodes.add("ProductObj||Add");
                }
                if (selectedFuncCodes.get(i).equals("SPUObj")) {
                    selectedFuncCodes.add("ProductObj");
                }
                if (selectedFuncCodes.get(i).equals("SPUObj||View")) {
                    selectedFuncCodes.add("ProductObj||View");
                }
                if (selectedFuncCodes.get(i).equals("SPUObj||Edit")) {
                    selectedFuncCodes.add("ProductObj||Edit");
                }
                if (selectedFuncCodes.get(i).equals("SPUObj||Abolish")) {
                    selectedFuncCodes.add("ProductObj||Abolish");
                }
                if (selectedFuncCodes.get(i).equals("SPUObj||Lock")) {
                    selectedFuncCodes.add("ProductObj||Lock");
                }
                if (selectedFuncCodes.get(i).equals("SPUObj||Unlock")) {
                    selectedFuncCodes.add("ProductObj||Unlock");
                }
                if (selectedFuncCodes.get(i).equals("SPUObj||ChangeOwner")) {
                    selectedFuncCodes.add("ProductObj||ChangeOwner");
                }
                if (selectedFuncCodes.get(i).equals("SPUObj||EditTeamMember")) {
                    selectedFuncCodes.add("ProductObj||EditTeamMember");
                }
                if (selectedFuncCodes.get(i).equals("SPUObj||Print")) {
                    selectedFuncCodes.add("ProductObj||Print");
                }
                if (selectedFuncCodes.get(i).equals("SPUObj||Import")) {
                    selectedFuncCodes.add("ProductObj||Import");
                }
                if (selectedFuncCodes.get(i).equals("SPUObj||Export")) {
                    selectedFuncCodes.add("ProductObj||Export");
                }
                if (selectedFuncCodes.get(i).equals("SPUObj||Delete")) {
                    selectedFuncCodes.add("ProductObj||Delete");
                }
                if (selectedFuncCodes.get(i).equals("SPUObj||Recover")) {
                    selectedFuncCodes.add("ProductObj||Recover");
                }

            }
        }

        for (int i = 0; i < unselectedFuncCodes.size(); i++) {
            if (unselectedFuncCodes.get(i).contains("SPUObj||Import") || unselectedFuncCodes.get(i).contains("SPUObj") || unselectedFuncCodes.get(i).contains("SPUObj||Add") || unselectedFuncCodes.get(i).contains("SPUObj||View")
                    || unselectedFuncCodes.get(i).contains("SPUObj||Edit") || unselectedFuncCodes.get(i).contains("SPUObj||Abolish") || unselectedFuncCodes.get(i).contains("SPUObj||Lock")
                    || unselectedFuncCodes.get(i).contains("SPUObj||Unlock") || unselectedFuncCodes.get(i).contains("SPUObj||ChangeOwner") || unselectedFuncCodes.get(i).contains("SPUObj||EditTeamMember")
                    || unselectedFuncCodes.get(i).contains("SPUObj||Print") || unselectedFuncCodes.get(i).contains("SPUObj||Export") || unselectedFuncCodes.get(i).contains("SPUObj||Delete")
                    || unselectedFuncCodes.get(i).contains("SPUObj||Recover")) {

                if (unselectedFuncCodes.get(i).equals("SPUObj||Add")) {
                    unselectedFuncCodes.add("ProductObj||Add");
                }
                if (unselectedFuncCodes.get(i).equals("SPUObj")) {
                    unselectedFuncCodes.add("ProductObj");
                }
                if (unselectedFuncCodes.get(i).equals("SPUObj||View")) {
                    unselectedFuncCodes.add("ProductObj||View");
                }
                if (unselectedFuncCodes.get(i).equals("SPUObj||Edit")) {
                    unselectedFuncCodes.add("ProductObj||Edit");
                }
                if (unselectedFuncCodes.get(i).equals("SPUObj||Abolish")) {
                    unselectedFuncCodes.add("ProductObj||Abolish");
                }
                if (unselectedFuncCodes.get(i).equals("SPUObj||Lock")) {
                    unselectedFuncCodes.add("ProductObj||Lock");
                }
                if (unselectedFuncCodes.get(i).equals("SPUObj||Unlock")) {
                    unselectedFuncCodes.add("ProductObj||Unlock");
                }
                if (unselectedFuncCodes.get(i).equals("SPUObj||ChangeOwner")) {
                    unselectedFuncCodes.add("ProductObj||ChangeOwner");
                }
                if (unselectedFuncCodes.get(i).equals("SPUObj||EditTeamMember")) {
                    unselectedFuncCodes.add("ProductObj||EditTeamMember");
                }
                if (unselectedFuncCodes.get(i).equals("SPUObj||Print")) {
                    unselectedFuncCodes.add("ProductObj||Print");
                }
                if (unselectedFuncCodes.get(i).equals("SPUObj||Import")) {
                    unselectedFuncCodes.add("ProductObj||Import");
                }
                if (unselectedFuncCodes.get(i).equals("SPUObj||Export")) {
                    unselectedFuncCodes.add("ProductObj||Export");
                }
                if (unselectedFuncCodes.get(i).equals("SPUObj||Delete")) {
                    unselectedFuncCodes.add("ProductObj||Delete");
                }
                if (unselectedFuncCodes.get(i).equals("SPUObj||Recover")) {
                    unselectedFuncCodes.add("ProductObj||Recover");
                }

            }
        }
    }

    private BaseResult updateRoleModifiedFuncPermission(SessionContext sessionContext, String roleCode, List<String> selectedFuncCodes, List<String> unselectedFuncCodes) throws ApiException {
        UpdateRoleModifiedFuncPermissionArg requestArg = new UpdateRoleModifiedFuncPermissionArg();
        requestArg.setRoleCode(roleCode);
        requestArg.setAuthContext(getAuthContext(sessionContext));
        requestArg.setAddFuncCode(selectedFuncCodes);
        requestArg.setDelFuncCode(unselectedFuncCodes);
        return funcPermissionApi.updateRoleModifiedFuncPermission(requestArg);
    }

}

