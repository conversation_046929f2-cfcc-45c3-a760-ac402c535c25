package com.facishare.paas.appframework.core.predef.handler.listheader;

import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.QueryTemplateDocument;
import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.core.model.handler.ControllerHandler;
import lombok.*;

import java.util.List;

/**
 * Created by zhouwr on 2024/3/12.
 */
public interface ListHeaderHandler extends ControllerHandler<ListHeaderHandler.Arg, ListHeaderHandler.Result> {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @EqualsAndHashCode(callSuper = true)
    class Arg extends ControllerHandler.Arg<StandardListHeaderController.Arg, StandardListHeaderController.Result> {
        private List<String> showFields;
        private LayoutDocument layout;
        private LayoutDocument listLayout;
        private List<QueryTemplateDocument> templates;
        private List<QueryTemplateDocument> baseScenes;
        private List<ButtonDocument> bulkButtonList;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @EqualsAndHashCode(callSuper = true)
    class Result extends Handler.Result<StandardListHeaderController.Result> {
        private List<String> showFields;
        private LayoutDocument layout;
        private LayoutDocument listLayout;
        private List<QueryTemplateDocument> templates;
        private List<QueryTemplateDocument> baseScenes;
        private List<ButtonDocument> bulkButtonList;
    }

}
