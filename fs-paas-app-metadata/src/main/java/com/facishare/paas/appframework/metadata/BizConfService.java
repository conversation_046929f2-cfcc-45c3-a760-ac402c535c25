package com.facishare.paas.appframework.metadata;

import com.fxiaoke.bizconf.bean.ConfigDefPojo;
import com.fxiaoke.bizconf.bean.ConfigPojo;

import java.util.List;
import java.util.Map;

/**
 * create by z<PERSON><PERSON> on 2020/09/04
 */
public interface BizConfService {
    List<ConfigDefPojo> queryConfigDef(String tenantId, String describeApiName, String bizType);

    List<ConfigPojo> queryConfigData(String tenantId, String configCode, List<String> bizTypeValueList);

    Map<String, ConfigPojo> queryConfigDataIncludeDefault(String tenantId, String describeApiName, String bizType,
                                                          String configCode, List<String> bizTypeValueList);
}
