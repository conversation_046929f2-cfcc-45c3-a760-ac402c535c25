package com.facishare.paas.appframework.metadata.mask;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.Collection;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/12/13
 */
public interface MaskFieldEncryptService {

    /**
     * 加密
     *
     * @param value 待加密数据
     * @return 加密后的数据
     */
    String encode(String value);

    void encode(IObjectData data, Collection<IFieldDescribe> fieldNames);

    /**
     * 解密
     *
     * @param value 待解密数据
     * @return 解密后的数据
     */
    String decode(String value);

    void decode(IObjectData data, Collection<IFieldDescribe> fieldNames);

    void decode(IObjectData data, IObjectDescribe describe);
}
