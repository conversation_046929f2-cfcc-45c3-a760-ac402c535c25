package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.SelectManyFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SelectManyImportFieldDataConverter单元测试
 */
class SelectManyImportFieldDataConverterTest {

  @Test
  void testSelectManyOptionValid() {
    // given
    String str = "乐蔓莓果啤酒20L K";
    
    // when
    String[] array = str.split("\\|");
    
    // then
    assertEquals(1, array.length);
    assertEquals("乐蔓莓果啤酒20L K", array[0]);
  }

  @Test
  void testConvertFieldData() {
    // given
    SelectManyImportFieldDataConverter converter = new SelectManyImportFieldDataConverter();
    
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put("a", "乐蔓莓果啤酒20L K");
    ObjectData data = new ObjectData(dataMap);
    
    SelectManyFieldDescribe field = new SelectManyFieldDescribe();
    field.setApiName("a");
    
    SelectOption option = new SelectOption();
    option.setLabel("乐蔓莓果啤酒20L K");
    option.setValue("111");
    field.addSelectOption(option);
    
    User user = new User("1", "-10000");

    // when & then
    assertDoesNotThrow(() -> {
      converter.convertFieldData(data, field, user);
    });
  }
}
