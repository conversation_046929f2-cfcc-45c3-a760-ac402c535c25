package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.es.abstrace.SearchSourceByQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class StandardSearchListController extends StandardListController {
    private List<String> currentIdList;

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery searchTemplateQuery = super.buildSearchTemplateQuery();
        SearchTemplateQueryExt templateQueryExt = SearchTemplateQueryExt.of(searchTemplateQuery);
        templateQueryExt.getFilters().clear();
        templateQueryExt.getWheres().clear();
        templateQueryExt.setPermissionType(arg.isCheckPermission() ? 1 : 0);
        templateQueryExt.addFilter(Operator.EQ, IObjectData.DESCRIBE_API_NAME, objectDescribe.getApiName());

        if (CollectionUtils.empty(arg.getIdList()) || searchTemplateQuery.getOffset() >= arg.getIdList().size()) {
            templateQueryExt.addFilter(Operator.IN, IObjectData.ID, Lists.newArrayList("-1"));
            return searchTemplateQuery;
        }

        int toIndex = Math.min(searchTemplateQuery.getOffset() + searchTemplateQuery.getLimit(), arg.getIdList().size());
        currentIdList = arg.getIdList().subList(searchTemplateQuery.getOffset(), toIndex);
        templateQueryExt.addFilter(Operator.IN, IObjectData.ID, currentIdList);
        templateQueryExt.setOffset(0);
        return searchTemplateQuery;
    }

    @Override
    protected Query defineQuery() {
        Query query = super.defineQuery();
        query.setPermissionType(arg.isCheckPermission() ? 1 : 0);

        SearchQuery searchQuery = SearchQueryImpl.filter(FilterExt.of(Operator.EQ, IObjectData.DESCRIBE_API_NAME, objectDescribe.getApiName()).getFilter());
        if (CollectionUtils.empty(arg.getIdList()) || query.getOffset() >= arg.getIdList().size()) {
            query.setSearchQuery(searchQuery
                    .and(FilterExt.of(Operator.IN, IObjectData.ID, Lists.newArrayList("-1")).getFilter()));
            return query;
        }

        int toIndex = Math.min(query.getOffset() + query.getLimit(), arg.getIdList().size());
        currentIdList = arg.getIdList().subList(query.getOffset(), toIndex);
        query.setSearchQuery(searchQuery
                .and(FilterExt.of(Operator.IN, IObjectData.ID, currentIdList).getFilter()));
        query.setOffset(0);
        return query;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);

        //全局搜索时，结果按参数中idList顺序排序
        if (CollectionUtils.empty(currentIdList)) {
            return result;
        }
        Map<String, ObjectDataDocument> map = Maps.newLinkedHashMap();
        currentIdList.forEach(a -> map.put(a, null));

        Map<String, ObjectDataDocument> collect = result.getDataList()
                .stream()
                .collect(Collectors.toMap(ObjectDataDocument::getId, a -> a));
        collect.forEach(map::put);
        List<ObjectDataDocument> sortedList = map.values().stream().filter(Objects::nonNull).collect(Collectors.toList());
        result.setDataList(sortedList);
        result.setTotal(CollectionUtils.nullToEmpty(arg.getIdList()).size());
        result.setSupportFullFieldSearchOnListPage(isSupportFullFieldSearchOnListPage(buildDescribeExt(null)));
        return result;
    }

    private Boolean isSupportFullFieldSearchOnListPage(ObjectDescribeDocument describeExt) {
        Object o = describeExt.get(OptionalFeaturesService.GLOBAL_SEARCH_SWITCH);
        if (Boolean.TRUE.equals(o)) {
            SearchSourceByQuery abstractSearchSource = serviceFacade.getBean(SearchSourceByQuery.class);
            boolean grayES = abstractSearchSource.checkGrayES(controllerContext.getTenantId(), objectDescribe.getApiName());
            if (grayES) {
                return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.LIST_DATA_GRAY_EI, controllerContext.getTenantId());
            }
        }
        return false;
    }

}
