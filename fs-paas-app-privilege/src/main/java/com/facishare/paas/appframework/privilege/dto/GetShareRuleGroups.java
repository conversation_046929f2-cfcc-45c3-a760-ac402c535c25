package com.facishare.paas.appframework.privilege.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface GetShareRuleGroups {

  @Data
  class Arg {
    Integer permissionType;

    Integer status;

    Integer pageNumber;

    Integer pageSize;

    private Map<Integer, Set<String>> sources;

    private Map<Integer, Set<String>> receives;

    private List<String> entices;

    //null 查询所有的
    //true 只查询共享給外部的
    //false 只查询共享給内部的
    private Boolean outReceive;

    private Integer basedType;

    /**
     * 0来源方上游 1来源方下游
     *
     * NULL 查询全部
     */
    private Integer entityShareType;
  }

  @Builder
  @Data
  class Result {
    @JsonProperty("SharedGroupInfos")
    @SerializedName("SharedGroupInfos")
    @JSONField(name = "M1")
    private List<SharedGroupInfoPojo> sharedGroupInfos;

    @JsonProperty("Page")
    @SerializedName("Page")
    @JSONField(name = "M2")
    private PageInfo page;
  }

  @Data
  @Builder
  class SharedGroupInfoPojo {
    private String groupId;
    private List<String> entityIds;
    private Map<String,String> entityMap;
    private List<String> shareIds;
    private List<String> receiveIds;
    private Integer permissionType;
    private Integer status;
    private Long createTime;
    private Long modifyTime;
    private Integer shareDeptCascade;
    private Integer receiveDeptCascade;
    private Integer entityShareGroupType;
    private Integer entityShareType;
    private Integer basedType;
  }

  @Data
  class PageInfo {
    @JsonProperty("PageCount")
    @SerializedName("PageCount")
    private Integer pageCount;
    @JsonProperty("PageNumber")
    @SerializedName("PageNumber")
    private Integer pageNumber;
    @JsonProperty("PageSize")
    @SerializedName("PageSize")
    private Integer pageSize;
    @JsonProperty("TotalCount")
    @SerializedName("TotalCount")
    private Integer totalCount;
  }
}
