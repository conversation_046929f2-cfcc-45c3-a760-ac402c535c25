<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans" xmlns:context="http://www.springframework.org/schema/context"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
  <!-- 向Spring容器注册基本服务 -->
  <context:annotation-config/>
<!--  &lt;!&ndash; 指定配置扫描包路径 &ndash;&gt;
  <context:component-scan
      base-package="com.facishare.crm.privilege.fsi,com.facishare.paas.auth"/>
  <import resource="classpath:spring/metadata-dubbo.xml"/>
  <import resource="classpath:spring/metadata-autoconfig.xml"/>


  &lt;!&ndash;rest-proxy的api配置&ndash;&gt;
  <bean id="restServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory" p:configName="fs-crm-java-rest-proxy" init-method="init" />
  <bean id="DefObjDataPrivilegeApi" class="com.facishare.rest.core.RestServiceProxyFactoryBean" p:type="com.facishare.crm.utils.rest.privilege.api.DefObjDataPrivilegeApi ">
    <property name="factory" ref="restServiceProxyFactory"></property>
  </bean>-->
  <bean id="commonPrivilegeService" class="com.facishare.crm.privilege.service.impl.PrivilegeServiceImpl"/>

  <!--<bean id="userService" class="com.facishare.crm.privilege.service.UserService"/>
  <bean id="openService" class="com.facishare.crm.privilege.service.OpenService"/>
  <bean id="userInfoService" class="com.facishare.crm.privilege.service.UserInfoService"/>
  <bean id="pushSessionService" class="com.facishare.crm.privilege.service.PushSessionService"/>-->


</beans>