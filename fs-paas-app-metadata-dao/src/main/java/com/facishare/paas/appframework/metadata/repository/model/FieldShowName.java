package com.facishare.paas.appframework.metadata.repository.model;

import com.facishare.paas.appframework.metadata.repository.annotation.Entity;
import com.facishare.paas.appframework.metadata.repository.annotation.ObjectField;
import com.facishare.paas.appframework.metadata.repository.annotation.TextField;
import com.facishare.paas.appframework.metadata.repository.annotation.TrueOrFalseField;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity(apiName = FieldShowName.FIELD_SHOW_NAME_OBJ)
public class FieldShowName extends BaseEntity {
    public static final String FIELD_SHOW_NAME_OBJ = "FieldShowNameObj";
    public static final String NAME = "name";
    public static final String OBJECT_API_NAME = "object_api_name";
    public static final String FIELD_API_NAME = "field_api_name";
    public static final String RECORD_API_NAME = "record_api_name";

    @TrueOrFalseField(field = @ObjectField(apiName = IObjectData.IS_DELETED, label = "is_deleted"))
    private boolean deleted;

    @TextField(field = @ObjectField(apiName = NAME, label = "对象name"))
    private String name;

    @TextField(field = @ObjectField(apiName = OBJECT_API_NAME, label = "对象apiName"))
    private String objectApiName;

    @TextField(field = @ObjectField(apiName = RECORD_API_NAME, label = "业务类型apiName"))
    private String recordApiName;

    @TextField(field = @ObjectField(apiName = FIELD_API_NAME, label = "字段apiName"))
    private String fieldApiName;
}
