package com.facishare.paas.appframework.core.predef.service;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.service.PlatServiceProxy;
import com.facishare.paas.appframework.common.service.dto.OrganizationStatus;
import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.button.FunctionAction;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.multicurrency.RefreshInternational;
import com.facishare.paas.appframework.metadata.sandbox.ChangeSetFindByQuery;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.para.MultiOrgOpen;
import com.facishare.paas.metadata.api.service.IMetadataMultiOrgService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.UdefButton;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@ServiceModule("sandbox")
@Slf4j
@Component
public class ObjectSandboxService {

    @Autowired
    private MultiCurrencyLogicService multiCurrencyLogicService;
    @Autowired
    private IMetadataMultiOrgService metadataMultiOrgService;
    @Autowired
    private PlatServiceProxy platServiceProxy;
    @Autowired
    private CustomButtonService customButtonService;
    @Autowired
    private PostActionService postActionService;
    @Autowired
    private ReferenceLogicService referenceLogicService;

    @ServiceMethod("refresh_multi_currency")
    public RefreshInternational.Result refreshMultiCurrency(RefreshInternational.Arg arg, ServiceContext context) {
        multiCurrencyLogicService.refreshMultiCurrency(arg, context.getUser());
        return new RefreshInternational.Result();
    }

    @ServiceMethod("refresh_multi_organization")
    public RefreshInternational.Result refreshMultiOrganization(RefreshInternational.Arg arg, ServiceContext context) {
        if (CollectionUtils.empty(arg.getObjectApiNames())) {
            return new RefreshInternational.Result();
        }
        OrganizationStatus.Arg openArg = new OrganizationStatus.Arg();
        openArg.setTenantId(context.getTenantId());
        OrganizationStatus.Result result = platServiceProxy.openOrganization(openArg);
        if (!result.isOpenOrganization()) {
            return new RefreshInternational.Result();
        }
        List<String> organizationBlackObjectList = AppFrameworkConfig.getOrganizationBlackObjectList();
        List<String> describeApiNames = arg.getObjectApiNames().stream()
                .filter(apiName -> !organizationBlackObjectList.contains(apiName))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(describeApiNames)) {
            log.error("openMultiOrganization failed,describeApiNames is null");
            return new RefreshInternational.Result();
        }
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> {
            MultiOrgOpen.Arg multiOrgArg = new MultiOrgOpen.Arg();
            multiOrgArg.setTenantId(context.getTenantId());
            multiOrgArg.setDescribeApiNames(describeApiNames);
            try {
                log.warn("openMultiOrganization ei:{},multiOrgArg:{}", context.getTenantId(), JSON.toJSONString(multiOrgArg));
                metadataMultiOrgService.openMultiOrg(multiOrgArg);
            } catch (MetadataServiceException e) {
                log.error("openMultiOrganization failed!ei:{}", context.getTenantId(), e);
            }
        });
        parallelTask.run();
        return new RefreshInternational.Result();
    }

    @ServiceMethod("button_reference")
    public ChangeSetFindByQuery.Result buttonReference(ChangeSetFindByQuery.Arg arg, ServiceContext context) {
        try {
            // 1. 参数校验：检查入参 arg 和 searchQueryInfo 是否为空
            if (Objects.isNull(arg) || Objects.isNull(arg.getSearchQueryInfo())) {
                log.warn("Invalid input parameters: arg or searchQueryInfo is null");
                return buildEmptyResult();
            }

            List<ChangeSetFindByQuery.Filter> filters = arg.getSearchQueryInfo().getFilters();
            // 2. 从过滤器中获取关键参数值
            String apiName = getFilterValue(filters, "api_name");
            String describeApiName = getFilterValue(filters, "describe_api_name");
            String tenantId = Objects.isNull(arg.getEnterpriseId()) ? null : String.valueOf(arg.getEnterpriseId());

            // 3. 关键参数判空，如果为空则直接返回空结果
            if (StringUtils.isAnyEmpty(apiName, describeApiName, tenantId)) {
                log.warn("Missing required parameters - apiName: {}, describeApiName: {}, tenantId: {}", 
                    apiName, describeApiName, tenantId);
                return buildEmptyResult();
            }

            // 4. 记录按钮引用查询日志，方便追踪问题
            log.info("Button reference query - apiName: {}, describeApiName: {}, tenantId: {}",
                    apiName, describeApiName, tenantId);

            // 5. 调用 buttonService 获取按钮定义
            IUdefButton button = customButtonService.findButtonByApiNameForDesigner(context.getUser(), apiName, describeApiName);
            if (button == null) {
                log.warn("Button not found for apiName: {}, describeApiName: {}", apiName, describeApiName);
            }

            // 6. 调用 actionService 获取按钮关联的 Action 列表
            List<IUdefAction> actionList = findActionListForDesigner(context.getUser(), button, describeApiName);
            List<String> functionApiNames = Lists.newArrayList();

            // 7. 遍历 Action 列表，提取 FunctionApiName
            actionList.forEach(action -> {
                try {
                    FunctionAction.ActionParameter actionParameter = FunctionAction.ActionParameter.of(action.getActionParamter());
                    if (Objects.isNull(actionParameter)) {
                        return;
                    }
                    String functionApiName = actionParameter.getFunctionApiName();
                    if (StringUtils.isEmpty(functionApiName)) {
                        return;
                    }
                    functionApiNames.add(functionApiName);
                } catch (Exception e) {
                    log.error("Error processing action parameter for action: {}", action, e);
                }
            });

            // 8. 调用 referenceLogicService 查询引用数据
            List<ReferenceData> referenceDataList = referenceLogicService.queryByTargetList(tenantId, functionApiNames);
            if (CollectionUtils.empty(referenceDataList)) {
                log.info("No reference data found for functionApiNames: {}", functionApiNames);
                return buildEmptyResult();
            }

            // 9. 直接获取 sourceValue 等于 apiName 的引用数据
            List<Map<String, Object>> dataList = referenceDataList.stream()
                    .filter(data -> apiName.equals(data.getSourceValue()))
                    .map(data -> {
                        Map<String, Object> map = Maps.newHashMap();
                        map.put("source_type", data.getSourceType());
                        map.put("source_label", data.getSourceLabel());
                        map.put("source_value", data.getSourceValue());
                        map.put("target_type", data.getTargetType());
                        map.put("target_value", data.getTargetValue());
                        map.put("tenant_id", tenantId);
                        return map;
                    })
                    .collect(Collectors.toList());

            // 10. 构建并返回结果
            return ChangeSetFindByQuery.Result.builder()
                    .count(dataList.size())
                    .dataList(dataList)
                    .build();

        } catch (Exception e) {
            log.error("Error in buttonReference method - tenantId: {}, error: {}", 
                arg != null ? arg.getEnterpriseId() : "null", e.getMessage(), e);
            return buildEmptyResult();
        }
    }

    private ChangeSetFindByQuery.Result buildEmptyResult() {
        return ChangeSetFindByQuery.Result.builder()
                .count(0)
                .dataList(Lists.newArrayList())
                .build();
    }

    private String getFilterValue(List<ChangeSetFindByQuery.Filter> filters, String fieldName) {
        return getFilterValue(filters, fieldName, null);
    }

    private String getFilterValue(List<ChangeSetFindByQuery.Filter> filters, String fieldName, String defaultValue) {
        return filters.stream()
                .filter(f -> fieldName.equals(f.getFieldName()))
                .findFirst()
                .map(ChangeSetFindByQuery.Filter::getValues)
                .filter(v -> !v.isEmpty())
                .map(v -> v.get(0))
                .orElse(defaultValue);
    }

    private List<IUdefAction> findActionListForDesigner(User user, IUdefButton button, String describeApiName) {
        List<IUdefAction> actions = postActionService.findActionListForDesigner(
                user,
                button != null ? button : new UdefButton(),
                describeApiName
        );
        return actions.stream()
                .map(UdefActionExt::of)
                .peek(x -> x.computeActionStage(button))
                .map(UdefActionExt::getUdefAction)
                .collect(Collectors.toList());
    }

}
