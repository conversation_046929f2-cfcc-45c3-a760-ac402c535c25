package com.facishare.crm.openapi;

import com.alibaba.fastjson.JSON;
import com.facishare.rest.core.util.JsonUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * Created by <PERSON><PERSON> on 12/13/16.
 */

@Slf4j
public class Utils {
    public static final String ACCOUNT_API_NAME = "AccountObj"; // 客户
    public static final String ENTERPRISE_INFO_API_NAME = "EnterpriseInfoObj"; // 企业库
    public static final String PRODUCT_API_NAME = "ProductObj"; //产品
    public static final String BOM_API_NAME = "BOMObj"; //bom
    public static final String BOM_CORE_API_NAME = "BomCoreObj"; //产品组合
    public static final String BOM_PATH_API_NAME = "BOMPathObj";
    public static final String PRODUCT_GROUP_API_NAME = "ProductGroupObj"; //产品分组

    public static final String PRODUCT_CONSTRAINT_API_NAME = "ProductConstraintObj";// 约束条件
    public static final String PRODUCT_CONSTRAINT_LINES_API_NAME = "ProductConstraintLinesObj";// 约束条件明细
    public static final String SALE_CONTRACT_API_NAME = "SaleContractObj";// 销售合同
    public static final String SALE_CONTRACT_LINE_API_NAME = "SaleContractLineObj";// 销售合同产品
    public static final String SALES_ORDER_API_NAME = "SalesOrderObj"; //销售订单
    public static final String SALES_ORDER_PRODUCT_API_NAME = "SalesOrderProductObj"; //订单关联产品
    public static final String CONTACT_API_NAME = "ContactObj"; //联系人
    public static final String LEADS_API_NAME = "LeadsObj"; //线索
    //    public static final String PAYMENT_API_NAME = "PaymentObj"; //回款
    public static final String ACCOUNT_ADDR_API_NAME = "AccountAddrObj"; //客户地址
    public static final String ACCOUNT_ATT_API_NAME = "AccountAttObj"; //客户附件
    public static final String ACCOUNT_FIN_INFO_API_NAME = "AccountFinInfoObj"; //客户财务信息
    public static final String INVOICE_APPLICATION_API_NAME = "InvoiceApplicationObj";//开票信息
    public static final String OPPORTUNITY_API_NAME = "OpportunityObj"; //商机
    public static final String REFUND_API_NAME = "RefundObj"; //退款
    public static final String CONTRACT_API_NAME = "ContractObj"; //合同
    public static final String VISITING_API_NAME = "VisitingObj"; //拜访
    public static final String RETURN_GOODS_INVOICE_API_NAME = "ReturnedGoodsInvoiceObj"; //退货单
    public static final String RETURN_GOODS_INVOICE_Product_API_NAME = "ReturnedGoodsInvoiceProductObj"; //退货单产品
    public static final String MARKETING_EVENT_API_NAME = "MarketingEventObj"; //市场活动
    public static final String LEADS_POOL_API_NAME = "LeadsPoolObj"; //线索池
    public static final String SALE_ACTION_API_NAME = "SaleActionObj";
    public static final String SALE_ACTION_STAGE_API_NAME = "SaleActionStageObj";
    public static final String HIGHSEAS_API_NAME = "HighSeasObj";
    public static final String CUSTOMER_ACCOUNT_API_NAME = "CustomerAccountObj";  //客户账户对象
    public static final String PREPAY_DETAIL_API_NAME = "PrepayDetailObj";    //预付对象
    public static final String REBATE_INCOME_DETAIL_API_NAME = "RebateIncomeDetailObj";  //返利收入主对象
    public static final String REBATE_OUTCOME_DETAIL_API_NAME = "RebateOutcomeDetailObj";  //返利收入从对象
    public static final String PRICE_BOOK_API_NAME = "PriceBookObj";  //价目表
    public static final String PRICE_BOOK_PRODUCT_API_NAME = "PriceBookProductObj";  //价目表产品
    public static final String CHECKINS_API_NAME = "CheckinsObj";//高级外勤对象
    public static final String CHECKINS_IMG_API_NAME = "CheckinsImgObj";//高级外勤对象
    public static final String QUOTE_API_NAME = "QuoteObj";  //报价单
    public static final String QUOTE_LINES_API_NAME = "QuoteLinesObj";  //报价单明细
    public static final String PROMOTION_API_NAME = "PromotionObj";  //促销
    public static final String PROMOTION_PRODUCT_API_NAME = "PromotionProductObj";  //促销产品
    public static final String PROMOTION_RULE_API_NAME = "PromotionRuleObj";  //促销规则
    public static final String DELIVERY_NOTE_API_NAME = "DeliveryNoteObj";  //发货单
    public static final String DELIVERY_NOTE_PRODUCT_API_NAME = "DeliveryNoteProductObj";  //发货单产品
    public static final String STOCK_API_NAME = "StockObj";  //发货单产品
    public static final String GOODS_RECEIVED_NOTE_API_NAME = "GoodsReceivedNoteObj";  // 入库单
    public static final String WAREHOUSE_API_NAME = "WarehouseObj";  //发货单产品
    public static final String GOODS_RECEIVED_NOTE_PRODUCT_API_NAME = "GoodsReceivedNoteProductObj";  //入库单产品
    public static final String PAYMENT_PLAN_API_NAME = "PaymentPlanObj";
    public static final String CUSTOMER_PAYMENT_API_NAME = "PaymentObj";
    public static final String ORDER_PAYMENT_API_NAME = "OrderPaymentObj";
    public static final String PARTNER_API_NAME = "PartnerObj"; //合作伙伴
    public static final String CASES_API_NAME = "CasesObj"; //工单
    public static final String BPM_TASK_API_NAME = "BpmTask"; //bpm任务
    public static final String BPM_INSTANCE_API_NAME = "BpmInstance"; //bpm实例
    public static final String BPM_INSTANCE_DISPLAY_NAME = "业务流程实例"; //bpm实例 // ignoreI18n

    public static final String OUTBOUND_DELIVER_NOTE_API_NAME = "OutboundDeliveryNoteObj"; //出库单
    public static final String OUTBOUND_DELIVER_NOTE_PRODUCT_API_NAME = "OutboundDeliveryNoteProductObj"; //出库单产品
    public static final String REQUISTITION_NOTE_API_NAME = "RequisitionNoteObj";//调拨单
    public static final String REQUISTITION_NOTE_PRODUCT_API_NAME = "RequisitionNoteProductObj"; //调拨单产品

    public static final String REBATE_USE_RULE_OBJ = "RebateUseRuleObj"; //返利使用规则
    public static final String ADVERTISEMENT_OBJ = "AdvertisementObj"; //广告

    public static final String VISIT_ROUTE_OBJ = "VisitRouteObj"; //路线

    /**
     * 组织架构对象
     */
    @Deprecated
    public static final String ORG_USER = "PersonnelObj";
    public static final String PERSONNEL_OBJ_API_NAME = "PersonnelObj";
    public static final String DEPARTMENT_OBJ_API_NAME = "DepartmentObj";//部门
    public static final String DIMENSION_OBJ_API_NAME = "DimensionObj";

    public static final String GOAL_VALUE_API_NAME = "GoalValueObj";//目标值
    public static final String GOAL_RULE_API_NAME = "GoalRuleObj";//目标规则

    public static final String ERP_STOCK_API_NAME = "ErpStockObj";
    public static final String ERP_WAREHOUSE_API_NAME = "ErpWarehouseObj";

    public static final String SIGN_TENANT_CERTIFY_API_NAME = "InternalSignCertifyObj";// 租户认证管理
    public static final String SIGN_USER_CERTIFY_API_NAME = "AccountSignCertifyObj";// 用户认证管理
    public static final String SIGN_RECORD_API_NAME = "SignRecordObj";// 签章使用记录
    public static final String SIGNER_API_NAME = "SignerObj";// 签署方
    public static final String WECHAT_FAN_API_NAME = "WechatFanObj";// 微信粉丝

    //深圳设备等对象的apiname
    public static final String DEVICE_API_NAME = "DeviceObj"; //设备
    public static final String DEVICE_PART_API_NAME = "DevicePartObj"; //设备配件规格关系
    public static final String DEVICE_CHECK_RECORD_API_NAME = "CheckRecordObj"; //设备巡检

    //public static final String ORDER_MESSAGE_API_NAME = "OrderMessageObj"; //订单留言

    public static final String STOCK_CHECK_NOTE_API_NAME = "StockCheckNoteObj"; //盘点单
    public static final String STOCK_CHECK_NOTE_PRODUCT_API_NAME = "StockCheckNoteProductObj"; //盘点单产品
    public static final String STOCK_DETAILS_API_NAME = "StockDetailsObj"; //出入库明细

    public static final String NEW_OPPORTUNITY_API_NAME = "NewOpportunityObj";  //商机2.0
    public static final String NEW_OPPORTUNITY_LINES_API_NAME = "NewOpportunityLinesObj";  //商机2.0明细
    public static final String NEW_OPPORTUNITY_CONTACTS_API_NAME = "NewOpportunityContactsObj";  //商机联系人

    public static final String CREDIT_FILE_API_NAME = "CreditFileObj"; //信用档案

    public static final String APPROVAL_INSTANCE_API_NAME = "ApprovalInstanceObj";
    public static final String APPROVAL_TASK_API_NAME = "ApprovalTaskObj";
    public static final String APPROVAL_INSTANCE_DISPLAY_NAME = "审批流程实例"; // ignoreI18n

    public static final String STAGE_TASK_API_NAME = "StageTaskObj";   //阶段推进器任务
    public static final String STAGE_INSTANCE_API_NAME = "StageInstanceObj";    //阶段推进器实例

    public static final String STATEMENT = "StatementObj";
    public static final String STATEMENT_DETAIL = "StatementDetailObj";

    public static final String INDUSTRY_PRICE_BOOK_API_NAME = "IndustryPriceBookObj"; //行业价目表
    public static final String INDUSTRY_PRICE_BOOK_PRODUCT_API_NAME = "IndustryPriceBookProductObj"; //行业价目表明细

    public static final String SALES_SCOPE_API_NAME = "SalesScopeObj"; //销售范围
    public static final String SALES_SCOPE_PRODUCT_API_NAME = "SalesScopeProductObj"; //销售范围明细

    public static final String SCHEDULE_API_NAME = "ScheduleObj"; //日程

    public static final String SERVICE_RECORD_API_NAME = "ServiceRecordObj"; //服务记录

    public static final String BATCH_API_NAME = "BatchObj"; //服务记录
    public static final String SERIAL_NUMBER_API_NAME = "SerialNumberObj"; //服务记录

    public static final String SPECIFICATION_API_NAME = "SpecificationObj"; //规格
    public static final String SPECIFICATION_VALUE_API_NAME = "SpecificationValueObj"; //规格值
    public static final String SPU_API_NAME = "SPUObj"; //商品

    public static final String SUBPRODUCTCATALOG_API_NAME = "SubProductCatalogObj"; //子产品明细
    public static final String SUBPRODUCT_API_NAME = "SubProductObj"; //子产品明细
    public static final String UNIT_INFO_API_NAME = "UnitInfoObj"; //单位

    public static final String FEE_DETAIL_API_NAME = "FeeDetailObj"; //费用明细
    public static final String FEE_SETTLEMENT_BILL_API_NAME = "FeeSettlementBillObj"; //费用结算单
    public static final String FEE_SETTLEMENT_BILL_DETAIL_API_NAME = "FeeSettlementDetailObj"; //费用结算明细

    public static final String CHARGE_APPROVE_API_NAME = "ChargeApproveObj"; //普通报销对象

    public static final String MEMBER_API_NAME = "MemberObj";  //会员
    public static final String MEMBER_EQUITY_API_NAME = "MemberEquitiesObj"; //会员权益
    public static final String MEMBER_GRADE_API_NAME = "MemberGradeObj"; //会员等级
    public static final String MEMBER_GRADE_EQUITY_API_NAME = "MemberGradeEquitiesRuleObj";  //会员等级权益规则
    public static final String MEMBER_GROWTH_VALUE_DETAIL_API_NAME = "MemberGrowthValueDetailObj";   //会员成长值明细
    public static final String MEMBER_INTEGRAL_DETAIL_API_NAME = "MemberIntegralDetailObj";   //会员积分明细

    public static final String AI_MAIN_API_NAME = "AIMainObj";  //AI主对象
    public static final String AI_REF_API_NAME = "AIRefObj";  //AI从对象

    public static final String APPRAISE_API_NAME = "AppraiseObj";

    public static final String CASES_ACCESSORY_USE_INFO_API_NAME = "CasesAccessoryUseInfoObj"; //工单配件使用产品
    public static final String RECEIVE_MATERIAL_BILL_API_NAME = "ReceiveMaterialBillObj"; //领料单
    public static final String RECEIVE_MATERIAL_BILL_PRODUCT_API_NAME = "ReceiveMaterialBillProductObj"; //领料单产品
    public static final String REFUND_MATERIAL_BILL_API_NAME = "RefundMaterialBillObj"; //退料单
    public static final String REFUND_MATERIAL_BILL_PRODUCT_API_NAME = "RefundMaterialBillProductObj"; //退料单产品
    public static final String EMPLOYEE_WAREHOUSE_API_NAME = "EmployeeWarehouseObj"; //员工个人库

    public static final String PURCHASE_ORDER_API_NAME = "PurchaseOrderObj"; //采购订单
    public static final String PURCHASE_ORDER_PRODUCT_API_NAME = "PurchaseOrderProductObj"; //采购订单产品
    public static final String SUPPLIER_API_NAME = "SupplierObj"; //供应商

    public static final String MARKET_ACTIVITY_API_NAME = "MarketingActivityObj";
    public static final String DEALER_STOCK_API_NAME = "DealerStockObj"; //经销商库存
    public static final String STORE_STOCK_API_NAME = "StoreStockObj"; //门店库存
    public static final String CUSTOMER_RECEIVING_NOTE_API_NAME = "CustomerReceivingNoteObj"; //客户收货单
    public static final String CUSTOMER_RECEIVING_NOTE_PRODUCT_API_NAME = "CustomerReceivingNoteProductObj"; //客户收货单产品

    public static final String BATCH_STOCK_API_NAME = "BatchStockObj"; //批次库存

    public static final String DEALER_ORDER_API_NAME = "DealerOrderObj";

    public static final String DEALER_ORDER_PRODUCT_API_NAME = "DealerOrderProductObj";
    public static final String DEALER_DELIVERY_NOTE_API_NAME = "DealerDeliveryNoteObj";
    public static final String DEALER_DELIVERY_NOTE_PRODUCT_API_NAME = "DealerDeliveryNoteProductObj";
    public static final String DEALER_RECEIVED_NOTE_API_NAME = "StoreReceivedNoteObj";
    public static final String DEALER_RECEIVED_NOTE_PRODUCT_API_NAME = "StoreReceivedNoteProductObj";
    public static final String DEALER_RETURN_ORDER_API_NAME = "DealerReturnOrderObj";
    public static final String DEALER_RETURN_ORDER_PRODUCT_API_NAME = "DealerReturnOrderProductObj";
    public static final String STORE_SALES_VOLUME_API_NAME = "StoreSalesVolumeObj";
    public static final String STORE_SALES_VOLUME_PRODUCT_API_NAME = "StoreSalesVolumeProductObj";

    public static final String CUSTOMER_RECEIVED_NOTE_API_NAME = "CustomerReceivedNoteObj"; //客户收货单
    public static final String CUSTOMER_RECEIVED_NOTE_PRODUCT_API_NAME = "CustomerReceivedNoteProductObj"; //客户收货单产品

    public static final String SALE_EVENT_API_NAME = "SaleEventObj";// 销售记录
    public static final String LEADS_TRANSFER_LOG_OBJ = "LeadsTransferLogObj";//线索转换日志\
    public static final String LEADS_FLOW_RECORD_OBJ = "LeadsFlowRecordObj";//线索跟进流转记录\

    public static final String MULTI_UNIT_RELATED_API_NAME = "MultiUnitRelatedObj";//多单位和商品产品的关联对象

    public static final String TIERED_PRICE_BOOK_API_NAME = "TieredPriceBookObj";//阶梯价目表
    public static final String TIERED_PRICE_BOOK_PRODUCT_API_NAME = "TieredPriceBookProductObj";//阶梯价目表明细
    public static final String TIERED_PRICE_BOOK_RULE_API_NAME = "TieredPriceBookRuleObj";//阶梯价目表规则

    public static final String CRM_FEED_OBJ_API_NAME = "CRMFeedObj"; // CRM信息

    public static final String ACTIVE_RECORD_API_NAME = "ActiveRecordObj"; // 销售记录对象

    public static final String INVOICE_APPLICATION_LINES_API_NAME = "InvoiceApplicationLinesObj"; // 开票明细对象

    public static final String BEHAVIOR_INTEGRAL_DETAIL_API_NAME = "BehaviorIntegralDetailObj";//行为积分明细
    public static final String BIZ_QUERY_OBJ_API_NAME = "BizQueryObj";//工商查询

    public static final String ATTRIBUTE_GROUP_OBJ_API_NAME = "AttributeGroupObj";//属性分组
    public static final String ATTRIBUTE_OBJ_API_NAME = "AttributeObj";//属性
    public static final String ATTRIBUTE_VALUE_OBJ_API_NAME = "AttributeValueObj";//属性值
    public static final String NONSTANDARD_ATTRIBUTE_OBJ_API_NAME = "NonstandardAttributeObj";//非标属性
    public static final String PRODUCT_ATTRIBUTE_OBJ_API_NAME = "ProductAttributeObj";//属性产品
    public static final String PRODUCT_CATEGORY_ATTRIBUTE_OBJ_API_NAME = "ProductCategoryAttributeObj";//属性产品分类
    public static final String ATTRIBUTE_USER_RELATION_OBJ_API_NAME = "AttributeUserRelationObj";//属性产品分类

    public static final String AVAILABLE_RANGE_OBJ_API_NAME = "AvailableRangeObj"; //可售范围

    public static final String CAMPAIGN_MEMBERS_OBJ_API_NAME = "CampaignMembersObj"; //活动成员
    public static final String KEYWORD_SERVING_PLAN_OBJ_API_NAME = "KeywordServingPlanObj"; //关键词投放计划
    public static final String MARKETING_KEYWORD_OBJ_API_NAME = "MarketingKeywordObj"; //营销关键词管理
    public static final String TERM_SERVING_LINES_OBJ_API_NAME = "TermServingLinesObj"; //关键词投放明细
    public static final String LANDING_PAGE_OBJ_API_NAME = "LandingPageObj"; //落地页
    public static final String LANDING_PAGE_VISITOR_DETAIL_OBJ_API_NAME = "LandingPageVisitorDetailObj"; //落地页访问明细
    public static final String ATTRIBUTE_PRICE_BOOK_OBJ_API_NAME = "AttributePriceBookObj";
    public static final String ATTRIBUTE_APPLICABLE_PRICE_BOOK_OBJ_API_NAME = "AttributeApplicablePriceBookObj";
    public static final String ATTRIBUTE_PRICE_BOOK_LINES_OBJ_API_NAME = "AttributePriceBookLinesObj";

    public static final String PRICE_POLICY_API_NAME = "PricePolicyObj";
    public static final String PRICE_POLICY_ACCOUNT_API_NAME = "PricePolicyAccountObj";
    public static final String PRICE_POLICY_RULE_API_NAME = "PricePolicyRuleObj";
    public static final String PRICE_POLICY_PRODUCT_API_NAME = "PricePolicyProductObj";
    public static final String AGGREGATE_RULE_API_NAME = "AggregateRuleObj"; //聚合规则
    public static final String AGGREGATE_VALUE_OBJ_API_NAME = "AggregateValueObj"; //聚合值
    public static final String ORDER_OCCUPY_API_NAME = "OrderOccupyObj"; // 订单占用
    public static final String POLICY_OCCUPY_API_NAME = "PolicyOccupyObj"; // 价格政策占用量

    public static final String PRICE_POLICY_LIMIT_ACCOUNT_API_NAME = "PricePolicyLimitAccountObj"; // 价格政策明细限量

    public static final String AMORTIZE_INFO_API_NAME = "AmortizeInfoObj"; // 分摊明细

    public static final String BEHAVIOR_RECORD_OBJ_API_NAME = "BehaviorRecordObj"; //行为记录

    public static final String PROJECT_API_NAME = "ProjectObj"; //项目
    public static final String PROJECT_STAGE_API_NAME = "ProjectStageObj"; //阶段
    public static final String PROJECT_TASK_API_NAME = "ProjectTaskObj"; //任务
    public static final String DEPENDENCIES_API_NAME = "DependenciesObj"; //依赖关系

    //特殊处理的一个东西.........
    public static final String ACCOUNT_COST_API_NAME = "AccountCostObj"; //客户费用
    //CRM的老对象的老ApiName
    public static final String OLD_APINAME_ACCOUNT_API_NAME = "Customer"; // 客户
    public static final String OLD_APINAME_PRODUCT_API_NAME = "Product"; //产品
    public static final String OLD_APINAME_SALES_ORDER_API_NAME = "Trade"; //销售订单
    public static final String OLD_APINAME_CONTACT_API_NAME = "Contact"; //联系人
    public static final String OLD_APINAME_LEADS_API_NAME = "SalesClue"; //线索
    public static final String OLD_APINAME_PAYMENT_API_NAME = "Payment"; //回款
    public static final String OLD_APINAME_INVOICE_APPLICATION_API_NAME = "Bill";//开票信息
    public static final String OLD_APINAME_OPPORTUNITY_API_NAME = "Opportunity"; //商机
    public static final String OLD_APINAME_REFUND_API_NAME = "Refund"; //退款
    public static final String OLD_APINAME_CONTRACT_API_NAME = "Contract"; //合同
    public static final String OLD_APINAME_VISITING_API_NAME = "Visit"; //拜访
    public static final String OLD_APINAME_RETURN_GOODS_INVOICE_API_NAME = "ReturnOrder";
    public static final String OLD_APINAME_MARKETING_EVENT_API_NAME = "MarketingEvent";

    public static final String EMPLOYEE_LOGIN_USAGE_API_NAME = "EmployeeLoginUsageObj";//员工登录情况
    public static final String EMPLOYEE_OBJECT_USAGE_API_NAME = "EmployeeObjectUsageObj";//员工使用情况
    public static final String FORECAST_RULE_API_NAME = "ForecastRuleObj";//预测规则
    public static final String FORECAST_TASK_API_NAME = "ForecastTaskObj";//预测任务
    public static final String FORECAST_TASK_DETAIL_API_NAME = "ForecastTaskDetailObj";//预测任务明细

    public static final String FAULT_POSITION_API_NAME = "FaultPositionObj";
    public static final String EQUITY_RELATIONSHIP_API_NAME = "EquityRelationshipObj";

    public static final String ADVANCED_FORMULA_API_NAME = "AdvancedFormulaObj";
    public static final String ADVANCED_FORMULA_LINE_API_NAME = "AdvancedFormulaLineObj";

    // 互联企业
    public static final String ENTERPRISE_RELATION_OBJ_API_NAME = "EnterpriseRelationObj";
    public static final String PUBLIC_EMPLOYEE_OBJ_API_NAME = "PublicEmployeeObj";

    public static final String ER_DEPARTMENT_OBJ_OBJ_API_NAME = "ErDepartmentObj";


    //老对象中的客户地址，客户附件，财务信息，订单关联产品四个对象是特殊对象。将来会考虑统一作为Master-detail来处理
    public static final Set<String> OLD_MASTER_DETAIL_OBJ_API_NAMES =
            Sets.newHashSet(ACCOUNT_ADDR_API_NAME, ACCOUNT_ATT_API_NAME, ACCOUNT_FIN_INFO_API_NAME);
    // 流程对象列表
    public static final List<String> FLOW_OBJ_API_NAME = Lists.newArrayList(APPROVAL_INSTANCE_API_NAME, APPROVAL_TASK_API_NAME,
            BPM_TASK_API_NAME, BPM_INSTANCE_API_NAME);
    public static final Set<String> FLOW_TASK_OBJ_API_NAME = ImmutableSet.of(APPROVAL_TASK_API_NAME, BPM_TASK_API_NAME, STAGE_TASK_API_NAME);
    // 支持「转换新建」按钮 的对象
    public static final Set<String> TRANSFER_ADD_OBJECT = ImmutableSet.of(ACCOUNT_API_NAME, CONTACT_API_NAME, OPPORTUNITY_API_NAME, NEW_OPPORTUNITY_API_NAME);
    public static String objTypeToNewApiNameMapping;
    public static String newApiNameToObjTypeMapping;
    public static String newApiNameToSFAOldApiNameMapping;

    //所有的老对象apiName列表
//    public static final Set<String> OLD_OBJ_API_NAMES =
//            Sets.newHashSet(ACCOUNT_API_NAME, PRODUCT_API_NAME, SALES_ORDER_API_NAME, SALES_ORDER_PRODUCT_API_NAME,
//                    CONTACT_API_NAME, LEADS_API_NAME, ACCOUNT_ADDR_API_NAME, ACCOUNT_ATT_API_NAME, ACCOUNT_FIN_INFO_API_NAME,
//                    INVOICE_APPLICATION_API_NAME, REFUND_API_NAME, CONTRACT_API_NAME, VISITING_API_NAME, RETURN_GOODS_INVOICE_API_NAME,
//                    RETURN_GOODS_INVOICE_Product_API_NAME, HIGHSEAS_API_NAME,
//                    OPPORTUNITY_API_NAME, MARKETING_EVENT_API_NAME, LEADS_POOL_API_NAME, SALE_ACTION_API_NAME, SALE_ACTION_STAGE_API_NAME);
    public static Map<String, String> apiName2DisplayNameMap = Maps.newHashMap();

    //老对象在lookup中或者MasterDetail的场景下的显示顺序
//    public static final List<String> UDOBJ_SUPPORT_OLD_OBJ_LIST_AND_ORDERS = Lists.newArrayList(
//            MARKETING_EVENT_API_NAME,
//            LEADS_API_NAME,
//            ACCOUNT_API_NAME,
//            ACCOUNT_ADDR_API_NAME,
//            ACCOUNT_FIN_INFO_API_NAME,
//            CONTACT_API_NAME,
//            OPPORTUNITY_API_NAME,
//            NEW_OPPORTUNITY_API_NAME, NEW_OPPORTUNITY_LINES_API_NAME, NEW_OPPORTUNITY_CONTACTS_API_NAME,
//            SALES_ORDER_API_NAME, SALES_ORDER_PRODUCT_API_NAME,
//            PRODUCT_API_NAME,
//            SPU_API_NAME,
//            SPECIFICATION_API_NAME, SPECIFICATION_VALUE_API_NAME,
//            PRICE_BOOK_API_NAME, PRICE_BOOK_PRODUCT_API_NAME,
//            QUOTE_API_NAME, QUOTE_LINES_API_NAME,
//            CONTRACT_API_NAME,
//            INVOICE_APPLICATION_API_NAME,
//            RETURN_GOODS_INVOICE_API_NAME, RETURN_GOODS_INVOICE_Product_API_NAME,
//            REFUND_API_NAME,
//            CUSTOMER_PAYMENT_API_NAME, ORDER_PAYMENT_API_NAME, PAYMENT_PLAN_API_NAME,
//            PARTNER_API_NAME,
//            CHECKINS_API_NAME, CHECKINS_IMG_API_NAME,
//            CASES_API_NAME,
//            CUSTOMER_ACCOUNT_API_NAME,
//
//
//
//
//            VISITING_API_NAME
//            , PREPAY_DETAIL_API_NAME, REBATE_INCOME_DETAIL_API_NAME
//    );

    //老对象的显示顺序
//    public static final List<String> OLD_OBJ_LIST_AND_ORDERS = Lists.newArrayList(LEADS_API_NAME, ACCOUNT_API_NAME,
//            OPPORTUNITY_API_NAME, CONTACT_API_NAME, CONTRACT_API_NAME, SALES_ORDER_API_NAME, SALES_ORDER_PRODUCT_API_NAME,
//            RETURN_GOODS_INVOICE_API_NAME, RETURN_GOODS_INVOICE_Product_API_NAME, CUSTOMER_PAYMENT_API_NAME, REFUND_API_NAME, PRODUCT_API_NAME,
//            INVOICE_APPLICATION_API_NAME, MARKETING_EVENT_API_NAME, VISITING_API_NAME, CUSTOMER_ACCOUNT_API_NAME,
//            PREPAY_DETAIL_API_NAME, REBATE_INCOME_DETAIL_API_NAME, PRICE_BOOK_API_NAME, PRICE_BOOK_PRODUCT_API_NAME
//    );
    public static Map<String, List<String>> supportFieldsMap;
    private static Map<String, String> apiName2StoreTableNameMap = Maps.newHashMap();

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-default-obj-config", config -> {
            objTypeToNewApiNameMapping = config.get("ObjTypeToNewApiNameMapping");
            newApiNameToObjTypeMapping = config.get("NewApiNameToObjTypeMapping");
            newApiNameToSFAOldApiNameMapping = config.get("NewApiNameToSFAOldApiNameMapping");
        });

        apiName2DisplayNameMap.put(ACCOUNT_API_NAME, "");
        apiName2DisplayNameMap.put(PRODUCT_API_NAME, "");
        apiName2DisplayNameMap.put(SALES_ORDER_API_NAME, "");
        apiName2DisplayNameMap.put(CONTACT_API_NAME, "");
        apiName2DisplayNameMap.put(LEADS_API_NAME, "");
//        apiName2DisplayNameMap.put(PAYMENT_API_NAME, "回款");
        apiName2DisplayNameMap.put(ACCOUNT_API_NAME, "");
        apiName2DisplayNameMap.put(REFUND_API_NAME, "");
        apiName2DisplayNameMap.put(CONTRACT_API_NAME, "");
        apiName2DisplayNameMap.put(OPPORTUNITY_API_NAME, "");

        apiName2StoreTableNameMap.put(ACCOUNT_API_NAME, "customer");
        apiName2StoreTableNameMap.put(PRODUCT_API_NAME, "product");
        apiName2StoreTableNameMap.put(SALES_ORDER_API_NAME, "customer_trade");
        apiName2StoreTableNameMap.put(SALES_ORDER_PRODUCT_API_NAME, "trade_product");
        apiName2StoreTableNameMap.put(CONTACT_API_NAME, "contact");
        apiName2StoreTableNameMap.put(LEADS_API_NAME, "sales_clue");
//        apiName2StoreTableNameMap.put(PAYMENT_API_NAME, "trade_payment");
        apiName2StoreTableNameMap.put(REFUND_API_NAME, "trade_refund");
        apiName2StoreTableNameMap.put(INVOICE_APPLICATION_API_NAME, "trade_bill");
        apiName2StoreTableNameMap.put(CONTRACT_API_NAME, "contract");
        apiName2StoreTableNameMap.put(RETURN_GOODS_INVOICE_API_NAME, "return_order");
        apiName2StoreTableNameMap.put(RETURN_GOODS_INVOICE_Product_API_NAME, "return_order_product");
        apiName2StoreTableNameMap.put(OPPORTUNITY_API_NAME, "opportunity");
        apiName2StoreTableNameMap.put(VISITING_API_NAME, "visit");
        apiName2StoreTableNameMap.put(MARKETING_EVENT_API_NAME, "marketing_event");
        apiName2StoreTableNameMap.put(LEADS_POOL_API_NAME, "sales_clue_pool");
        apiName2StoreTableNameMap.put(HIGHSEAS_API_NAME, "high_seas");
        apiName2StoreTableNameMap.put(SALE_ACTION_API_NAME, "sale_action");
        apiName2StoreTableNameMap.put(SALE_ACTION_STAGE_API_NAME, "sale_action_stage");
        apiName2StoreTableNameMap.put(PRICE_BOOK_API_NAME, "price_book");
        apiName2StoreTableNameMap.put(PRICE_BOOK_PRODUCT_API_NAME, "price_book_product");
        apiName2StoreTableNameMap.put(CUSTOMER_ACCOUNT_API_NAME, "customer_account");
    }

    static {
        ConfigFactory.getConfig("fs-crm-sys-variable", iConfig -> {
            supportFieldsMap = JsonUtil.fromJson(iConfig.get("referenceObjDisplayColumns"), Map.class);
        });
    }

    public static String convertApinameToObjType(String apiName) {
        Map<String, String> apiNameToObjTypeMap = (Map) JSON.parse(newApiNameToObjTypeMapping);
        return apiNameToObjTypeMap.get(apiName);

    }

    public static String convertObjTypeToApiname(String objType) {
        Map<String, String> objtypeToApiNameMap = (Map) JSON.parse(objTypeToNewApiNameMapping);
        return objtypeToApiNameMap.get(objType);
    }

//    public static String convertApinameToModuleCode(String apiName) {
//        if ("AccountObj".equals(apiName)) {
//            return "account";
//        }
//        if ("ContactObj".equals(apiName)) {
//            return "contact";
//        }
//        if ("ProductObj".equals(apiName)) {
//            return "product";
//        }
//        if ("SalesOrderObj".equals(apiName)) {
//            return "sales_order";
//        }
////        if ("PaymentObj".equals(apiName)) {
////            return "payment";
////        }
//        if ("LeadsObj".equals(apiName)) {
//            return "leads";
//        }
//        if ("InvoiceApplicationObj".equals(apiName)) {
//            return "invoice_application";
//        }
//        if ("ContractObj".equals(apiName)) {
//            return "contract";
//        }
//        if ("OpportunityObj".equals(apiName)) {
//            return "opportunity";
//        }
//        if ("RefundObj".equals(apiName)) {
//            return "refund";
//        }
//        if ("VisitingObj".equals(apiName)) {
//            return "visiting";
//        }
//        if ("LeadsPoolObj".equals(apiName)) {
//            return "leads_pool";
//        }
//        if ("ReturnedGoodsInvoiceObj".equals(apiName)) {
//            return "returned_goods_invoice";
//        }
//        return null;
//    }

    /**
     * 665之后,写迁移完成 将不会有老对象
     * 这个转换是专门给自定义对象过滤功能权限所使用的. 转换成的oldapiname是权限模块对应的apiname。和openapi对接无关。
     * 线索SalesClue
     * 客户Customer
     * 联系人Contact
     * 商机Opportunity
     * 产品Product
     * 销售订单Trade
     * 合同Contract
     * 退货单ReturnOrder
     * 回款Payment
     * 退款Refund
     * 开票申请Bill
     * 拜访Visit
     * 市场活动MarketingEvent
     * 盘点Inventory
     * 退货单ReturnOrder
     */
    public static String convertNewApiNameToOldApiName(String newApiName) {
        return "";
    }

    /*
    public static void main(String[] args) {
        String mapjson = "{\"1\":\"LeadsObj\",\"2\":\"AccountObj\",\"3\":\"ContactObj\",\"4\":\"ProductObj\",\"5\":\"PaymentObj\",\"11\":\"SalesOrderObj\",\"39\":\"AccountAddrObj\",\"40\":\"AccountFinInfoObj\",\"24\":\"AccountAttObj\",\"9\":\"InvoiceApplicationObj\",\"8\":\"OpportunityObj\",\"6\":\"RefundObj\",\"16\":\"ContractObj\",\"13\":\"VisitingObj\",\"28\":\"SalesOrderProductObj\",\"12\":\"ReturnedGoodsInvoiceObj\",\"20\":\"MarketingEventObj\"}";
        Map stringMap = (Map) JSON.parse(mapjson);
        Map newmap = Maps.newHashMap();
        Set keySet = stringMap.keySet();
        for (Object key : keySet) {
            String value = stringMap.get(key).toString();
            newmap.put(value, key);
        }
        System.out.println(JSON.toJSONString(newmap));
    }
     */

}
