package com.facishare.paas.appframework.metadata.search.ast

import com.facishare.paas.appframework.metadata.search.SearchQuery
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl
import com.facishare.paas.metadata.api.search.IFilter
import com.facishare.paas.metadata.impl.search.Filter
import spock.lang.Specification
import spock.lang.Unroll

import java.util.Collections

/**
 * FilterGroupValidator的Spock单元测试
 * 
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/5/19
 */
@Unroll
class FilterGroupValidatorTest extends Specification {
    
    FilterGroupValidator validator = new FilterGroupValidator()
    
    private SearchQuery createFilterNode(String index) {
        IFilter tempFilter = new Filter()
        tempFilter.setFieldName("__FILTER_INDEX__")
        tempFilter.setFieldValues(Collections.singletonList(index))
        return SearchQueryImpl.filter(tempFilter)
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validate方法在传入null节点时的行为
     */
    def "validateTest_空节点"() {
        when: "传入null节点进行验证"
        def result = validator.validate(null)
        
        then: "结果应该是无效的，且错误原因是'表达式为空'"
        !result.valid
        result.reason == "表达式为空"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validate方法在传入简单过滤器节点时的行为
     */
    def "validateTest_简单值节点"() {
        given: "创建一个简单过滤器节点"
        SearchQuery node = createFilterNode("1")
        
        when: "验证该节点"
        def result = validator.validate(node)
        
        then: "结果应该是有效的"
        result.valid
        result.reason == null
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validate方法在传入简单AND节点时的行为
     */
    def "validateTest_简单AND节点"() {
        given: "创建一个简单AND节点 (1 AND 2)"
        SearchQuery node1 = createFilterNode("1")
        SearchQuery node2 = createFilterNode("2")
        SearchQuery andNode = node1.and(node2)
        
        when: "验证该节点"
        def result = validator.validate(andNode)
        
        then: "结果应该是有效的"
        result.valid
        result.reason == null
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validate方法在传入简单OR节点时的行为
     */
    def "validateTest_简单OR节点"() {
        given: "创建一个简单OR节点 (1 OR 2)"
        SearchQuery node1 = createFilterNode("1")
        SearchQuery node2 = createFilterNode("2")
        SearchQuery orNode = node1.or(node2)
        
        when: "验证该节点"
        def result = validator.validate(orNode)
        
        then: "结果应该是有效的"
        result.valid
        result.reason == null
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validate方法在传入复杂结构1 (1 AND 2) OR 3 时的行为
     */
    def "validateTest_复杂结构1"() {
        given: "创建复杂结构 (1 AND 2) OR 3"
        SearchQuery node1 = createFilterNode("1")
        SearchQuery node2 = createFilterNode("2")
        SearchQuery node3 = createFilterNode("3")
        
        SearchQuery andNode = node1.and(node2)
        SearchQuery orNode = andNode.or(node3)
        
        when: "验证该节点"
        def result = validator.validate(orNode)
        
        then: "结果应该是有效的"
        result.valid
        result.reason == null
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validate方法在传入复杂结构2 ((1 AND 2) OR (3 AND 4)) AND 5 时的行为
     */
    def "validateTest_复杂结构2"() {
        given: "创建复杂结构 ((1 AND 2) OR (3 AND 4)) AND 5"
        SearchQuery node1 = createFilterNode("1")
        SearchQuery node2 = createFilterNode("2")
        SearchQuery node3 = createFilterNode("3")
        SearchQuery node4 = createFilterNode("4")
        SearchQuery node5 = createFilterNode("5")
        
        SearchQuery and1Node = node1.and(node2)
        SearchQuery and2Node = node3.and(node4)
        SearchQuery orNode = and1Node.or(and2Node)
        SearchQuery rootNode = orNode.and(node5)
        
        when: "验证该节点"
        def result = validator.validate(rootNode)
        
        then: "结果应该是有效的"
        result.valid
        result.reason == null
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validate方法在传入无效结构1 ((1 OR 2) AND 3) 时的行为
     */
    def "validateError_无效结构1"() {
        given: "创建无效结构 ((1 OR 2) AND 3) - AND节点的子节点不能包含OR操作符"
        SearchQuery node1 = createFilterNode("1")
        SearchQuery node2 = createFilterNode("2")
        SearchQuery node3 = createFilterNode("3")
        
        SearchQuery orNode = node1.or(node2)
        SearchQuery andNode = orNode.and(node3)
        
        when: "验证该节点"
        def result = validator.validate(andNode)
        
        then: "结果应该是无效的"
        !result.valid
        result.reason == "AND节点的子节点不能包含OR操作符"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validate方法在传入无效结构2 (1 OR (2 OR 3)) 时的行为
     */
    def "validateError_无效结构2"() {
        given: "创建无效结构 (1 OR (2 OR 3)) - OR节点的子节点必须是AND操作符或值节点"
        SearchQuery node1 = createFilterNode("1")
        SearchQuery node2 = createFilterNode("2")
        SearchQuery node3 = createFilterNode("3")
        
        SearchQuery innerOrNode = node2.or(node3)
        SearchQuery outerOrNode = node1.or(innerOrNode)
        
        when: "验证该节点"
        def result = validator.validate(outerOrNode)
        
        then: "结果应该是无效的"
        !result.valid
        result.reason == "OR节点的子节点必须是AND操作符或值节点"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试estimateFilterGroupCount方法在处理简单结构时的行为
     */
    def "estimateFilterGroupCountTest_简单结构"() {
        given: "创建测试节点"
        SearchQuery valueNode = createFilterNode("1")
        
        SearchQuery node1 = createFilterNode("1")
        SearchQuery node2 = createFilterNode("2")
        SearchQuery andNode = node1.and(node2)
        
        SearchQuery node3 = createFilterNode("1")
        SearchQuery node4 = createFilterNode("2")
        SearchQuery orNode = node3.or(node4)
        
        when: "估算filterGroup数量"
        def valueNodeCount = validator.estimateFilterGroupCount(valueNode)
        def andNodeCount = validator.estimateFilterGroupCount(andNode)
        def orNodeCount = validator.estimateFilterGroupCount(orNode)
        
        then: "结果符合预期"
        valueNodeCount == 0  // 值节点不需要filterGroup
        andNodeCount == 0    // AND节点不需要filterGroup
        orNodeCount == 2     // OR节点需要2个filterGroup
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试estimateFilterGroupCount方法在处理复杂结构时的行为
     */
    def "estimateFilterGroupCountTest_复杂结构"() {
        given: "创建测试节点"
        // 创建结构 (1 AND 2) OR 3
        SearchQuery node1 = createFilterNode("1")
        SearchQuery node2 = createFilterNode("2")
        SearchQuery node3 = createFilterNode("3")
        
        SearchQuery andNode1 = node1.and(node2)
        SearchQuery orNode1 = andNode1.or(node3)
        
        // 创建结构 ((1 AND 2) OR (3 AND 4))
        SearchQuery node4 = createFilterNode("1")
        SearchQuery node5 = createFilterNode("2")
        SearchQuery node6 = createFilterNode("3")
        SearchQuery node7 = createFilterNode("4")
        
        SearchQuery andNode2 = node4.and(node5)
        SearchQuery andNode3 = node6.and(node7)
        SearchQuery orNode2 = andNode2.or(andNode3)
        
        when: "估算filterGroup数量"
        def count1 = validator.estimateFilterGroupCount(orNode1)
        def count2 = validator.estimateFilterGroupCount(orNode2)
        
        then: "结果符合预期"
        count1 == 2  // (1 AND 2) OR 3 需要2个filterGroup
        count2 == 2  // ((1 AND 2) OR (3 AND 4)) 需要2个filterGroup
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：使用参数化测试estimateFilterGroupCount方法
     */
    def "estimateFilterGroupCountTest_参数化测试"() {
        when: "根据不同的节点结构估算filterGroup数量"
        def result = validator.estimateFilterGroupCount(node)
        
        then: "结果符合预期"
        result == expectedCount
        
        where: "不同的节点结构及其预期结果"
        node                             | expectedCount
        null                             | 0
        createFilterNode("1")            | 0
        createFilterNode("1").and(createFilterNode("2")) | 0
        createFilterNode("1").or(createFilterNode("2"))  | 2
        createFilterNode("1").and(createFilterNode("2")).or(createFilterNode("3")) | 2
        createFilterNode("1").and(createFilterNode("2")).or(createFilterNode("3").and(createFilterNode("4"))) | 2
    }
    
    // 辅助方法，用于创建测试节点
    private SearchQuery createSimpleAndNode() {
        return createFilterNode("1").and(createFilterNode("2"))
    }
    
    private SearchQuery createSimpleOrNode() {
        return createFilterNode("1").or(createFilterNode("2"))
    }
    
    private SearchQuery createComplex1() {
        // (1 AND 2) OR 3
        return createFilterNode("1").and(createFilterNode("2")).or(createFilterNode("3"))
    }
    
    private SearchQuery createComplex2() {
        // ((1 AND 2) OR (3 AND 4))
        return createFilterNode("1").and(createFilterNode("2")).or(createFilterNode("3").and(createFilterNode("4")))
    }
} 