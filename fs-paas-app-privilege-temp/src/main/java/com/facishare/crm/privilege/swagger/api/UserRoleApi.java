/**
 * Facishare PAAS Auth
 * This is PAAS auth service project
 *
 * OpenAPI spec version: v1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


package com.facishare.crm.privilege.swagger.api;

import com.facishare.crm.privilege.swagger.*;
import com.facishare.crm.privilege.swagger.model.*;
import com.google.gson.reflect.TypeToken;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UserRoleApi {
    private ApiClient apiClient;

    public UserRoleApi() {
        this(Configuration.getDefaultApiClient());
    }

    public UserRoleApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /* Build call for addRole */
    private okhttp3.Call addRoleCall(CreateRoleArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/addRole".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 角色添加
     * 角色添加
     * @param body  (optional)
     * @return AddRoleResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public AddRoleResponse addRole(CreateRoleArg body) throws ApiException {
        ApiResponse<AddRoleResponse> resp = addRoleWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 角色添加
     * 角色添加
     * @param body  (optional)
     * @return ApiResponse&lt;AddRoleResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<AddRoleResponse> addRoleWithHttpInfo(CreateRoleArg body) throws ApiException {
        okhttp3.Call call = addRoleCall(body, null, null);
        Type localVarReturnType = new TypeToken<AddRoleResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 角色添加 (asynchronously)
     * 角色添加
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call addRoleAsync(CreateRoleArg body, final ApiCallback<AddRoleResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = addRoleCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<AddRoleResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for addUserToRole */
    private okhttp3.Call addUserToRoleCall(AddUserToRoleArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/addUserToRole".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 角色中添加用户
     * 角色中添加用户
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult addUserToRole(AddUserToRoleArg body) throws ApiException {
        ApiResponse<BaseResult> resp = addUserToRoleWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 角色中添加用户
     * 角色中添加用户
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> addUserToRoleWithHttpInfo(AddUserToRoleArg body) throws ApiException {
        okhttp3.Call call = addUserToRoleCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 角色中添加用户 (asynchronously)
     * 角色中添加用户
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call addUserToRoleAsync(AddUserToRoleArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = addUserToRoleCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for batchAddUsersToRoles */
    private okhttp3.Call batchAddUsersToRolesCall(BatchAddUsersToRolesArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/batchAddUsersToRoles".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 批量添加用户到多个角色
     * 批量添加用户到多个角色
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult batchAddUsersToRoles(BatchAddUsersToRolesArg body) throws ApiException {
        ApiResponse<BaseResult> resp = batchAddUsersToRolesWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 批量添加用户到多个角色
     * 批量添加用户到多个角色
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> batchAddUsersToRolesWithHttpInfo(BatchAddUsersToRolesArg body) throws ApiException {
        okhttp3.Call call = batchAddUsersToRolesCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 批量添加用户到多个角色 (asynchronously)
     * 批量添加用户到多个角色
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call batchAddUsersToRolesAsync(BatchAddUsersToRolesArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = batchAddUsersToRolesCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for delRole */
    private okhttp3.Call delRoleCall(DelRoleArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/delRole".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 角色删除
     * 角色删除
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult delRole(DelRoleArg body) throws ApiException {
        ApiResponse<BaseResult> resp = delRoleWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 角色删除
     * 角色删除
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> delRoleWithHttpInfo(DelRoleArg body) throws ApiException {
        okhttp3.Call call = delRoleCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 角色删除 (asynchronously)
     * 角色删除
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call delRoleAsync(DelRoleArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = delRoleCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for delUserFromRole */
    private okhttp3.Call delUserFromRoleCall(DelUserFromRoleArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/delUserFromRole".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 角色中移除用户
     * 角色中移除用户
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult delUserFromRole(DelUserFromRoleArg body) throws ApiException {
        ApiResponse<BaseResult> resp = delUserFromRoleWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 角色中移除用户
     * 角色中移除用户
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> delUserFromRoleWithHttpInfo(DelUserFromRoleArg body) throws ApiException {
        okhttp3.Call call = delUserFromRoleCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 角色中移除用户 (asynchronously)
     * 角色中移除用户
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call delUserFromRoleAsync(DelUserFromRoleArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = delUserFromRoleCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for getAllUserRoleRelationEntities */
    private okhttp3.Call getAllUserRoleRelationEntitiesCall(BaseArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/getAllEmployeeRoleRelationEntities".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 获取所有用户角色关系
     * 获取所有用户角色关系
     * @param body  (optional)
     * @return QueryUserRoleEntityResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public QueryUserRoleEntityResponse getAllUserRoleRelationEntities(BaseArg body) throws ApiException {
        ApiResponse<QueryUserRoleEntityResponse> resp = getAllUserRoleRelationEntitiesWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 获取所有用户角色关系
     * 获取所有用户角色关系
     * @param body  (optional)
     * @return ApiResponse&lt;QueryUserRoleEntityResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<QueryUserRoleEntityResponse> getAllUserRoleRelationEntitiesWithHttpInfo(BaseArg body) throws ApiException {
        okhttp3.Call call = getAllUserRoleRelationEntitiesCall(body, null, null);
        Type localVarReturnType = new TypeToken<QueryUserRoleEntityResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 获取所有用户角色关系 (asynchronously)
     * 获取所有用户角色关系
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call getAllUserRoleRelationEntitiesAsync(BaseArg body, final ApiCallback<QueryUserRoleEntityResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = getAllUserRoleRelationEntitiesCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<QueryUserRoleEntityResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for getEmployeeRoleRelationEntitiesByRoleIDs */
    private okhttp3.Call getEmployeeRoleRelationEntitiesByRoleIDsCall(UserRoleRelationEntitiesByRolesArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/getEmployeeRoleRelationEntitiesByRoleIDs".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 根据角色列表获取所有用户角色关系
     * 根据角色列表获取所有用户角色关系
     * @param body  (optional)
     * @return QueryUserRoleEntityResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public QueryUserRoleEntityResponse getEmployeeRoleRelationEntitiesByRoleIDs(UserRoleRelationEntitiesByRolesArg body) throws ApiException {
        ApiResponse<QueryUserRoleEntityResponse> resp = getEmployeeRoleRelationEntitiesByRoleIDsWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 根据角色列表获取所有用户角色关系
     * 根据角色列表获取所有用户角色关系
     * @param body  (optional)
     * @return ApiResponse&lt;QueryUserRoleEntityResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<QueryUserRoleEntityResponse> getEmployeeRoleRelationEntitiesByRoleIDsWithHttpInfo(UserRoleRelationEntitiesByRolesArg body) throws ApiException {
        okhttp3.Call call = getEmployeeRoleRelationEntitiesByRoleIDsCall(body, null, null);
        Type localVarReturnType = new TypeToken<QueryUserRoleEntityResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 根据角色列表获取所有用户角色关系 (asynchronously)
     * 根据角色列表获取所有用户角色关系
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call getEmployeeRoleRelationEntitiesByRoleIDsAsync(UserRoleRelationEntitiesByRolesArg body, final ApiCallback<QueryUserRoleEntityResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = getEmployeeRoleRelationEntitiesByRoleIDsCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<QueryUserRoleEntityResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for getMultiEmployeeRoleRelationEntitiesByEmployeeIDs */
    private okhttp3.Call getMultiEmployeeRoleRelationEntitiesByEmployeeIDsCall(UserRoleRelationEntitiesByUsersArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/getMultiEmployeeRoleRelationEntitiesByEmployeeIDs".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 根据用户列表获取所有用户角色关系
     * 根据用户列表获取所有用户角色关系
     * @param body  (optional)
     * @return QueryUserRoleEntityResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public QueryUserRoleEntityResponse getMultiEmployeeRoleRelationEntitiesByEmployeeIDs(UserRoleRelationEntitiesByUsersArg body) throws ApiException {
        ApiResponse<QueryUserRoleEntityResponse> resp = getMultiEmployeeRoleRelationEntitiesByEmployeeIDsWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 根据用户列表获取所有用户角色关系
     * 根据用户列表获取所有用户角色关系
     * @param body  (optional)
     * @return ApiResponse&lt;QueryUserRoleEntityResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<QueryUserRoleEntityResponse> getMultiEmployeeRoleRelationEntitiesByEmployeeIDsWithHttpInfo(UserRoleRelationEntitiesByUsersArg body) throws ApiException {
        okhttp3.Call call = getMultiEmployeeRoleRelationEntitiesByEmployeeIDsCall(body, null, null);
        Type localVarReturnType = new TypeToken<QueryUserRoleEntityResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 根据用户列表获取所有用户角色关系 (asynchronously)
     * 根据用户列表获取所有用户角色关系
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call getMultiEmployeeRoleRelationEntitiesByEmployeeIDsAsync(UserRoleRelationEntitiesByUsersArg body, final ApiCallback<QueryUserRoleEntityResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = getMultiEmployeeRoleRelationEntitiesByEmployeeIDsCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<QueryUserRoleEntityResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryRoleCodeListByUserId */
    private okhttp3.Call queryRoleCodeListByUserIdCall(BaseArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/queryRoleCodeListByUserId".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 查询用户分配的角色
     * 查询用户分配的角色
     * @param body  (optional)
     * @return QueryRoleCodeListByUserIdResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public QueryRoleCodeListByUserIdResponse queryRoleCodeListByUserId(BaseArg body) throws ApiException {
        ApiResponse<QueryRoleCodeListByUserIdResponse> resp = queryRoleCodeListByUserIdWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 查询用户分配的角色
     * 查询用户分配的角色
     * @param body  (optional)
     * @return ApiResponse&lt;QueryRoleCodeListByUserIdResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<QueryRoleCodeListByUserIdResponse> queryRoleCodeListByUserIdWithHttpInfo(BaseArg body) throws ApiException {
        okhttp3.Call call = queryRoleCodeListByUserIdCall(body, null, null);
        Type localVarReturnType = new TypeToken<QueryRoleCodeListByUserIdResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 查询用户分配的角色 (asynchronously)
     * 查询用户分配的角色
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryRoleCodeListByUserIdAsync(BaseArg body, final ApiCallback<QueryRoleCodeListByUserIdResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryRoleCodeListByUserIdCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<QueryRoleCodeListByUserIdResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryRoleInfoWithCodes */
    private okhttp3.Call queryRoleInfoWithCodesCall(QueryRoleInfoWithRoleCodesArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/queryRoleInfoWithCodes".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 角色查询(支持多roleCode查询)
     * 角色查询(支持多roleCode查询)
     * @param body  (optional)
     * @return RoleResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public RoleResponse queryRoleInfoWithCodes(QueryRoleInfoWithRoleCodesArg body) throws ApiException {
        ApiResponse<RoleResponse> resp = queryRoleInfoWithCodesWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 角色查询(支持多roleCode查询)
     * 角色查询(支持多roleCode查询)
     * @param body  (optional)
     * @return ApiResponse&lt;RoleResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<RoleResponse> queryRoleInfoWithCodesWithHttpInfo(QueryRoleInfoWithRoleCodesArg body) throws ApiException {
        okhttp3.Call call = queryRoleInfoWithCodesCall(body, null, null);
        Type localVarReturnType = new TypeToken<RoleResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 角色查询(支持多roleCode查询) (asynchronously)
     * 角色查询(支持多roleCode查询)
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryRoleInfoWithCodesAsync(QueryRoleInfoWithRoleCodesArg body, final ApiCallback<RoleResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryRoleInfoWithCodesCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<RoleResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryRoleUsersByRoles */
    private okhttp3.Call queryRoleUsersByRolesCall(QueryRoleUsersByRolesArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/queryRoleUsersByRoles".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 批量查询角色下的用户
     * 批量查询角色下的用户
     * @param body  (optional)
     * @return QueryRoleUsersByRolesResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public QueryRoleUsersByRolesResponse queryRoleUsersByRoles(QueryRoleUsersByRolesArg body) throws ApiException {
        ApiResponse<QueryRoleUsersByRolesResponse> resp = queryRoleUsersByRolesWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 批量查询角色下的用户
     * 批量查询角色下的用户
     * @param body  (optional)
     * @return ApiResponse&lt;QueryRoleUsersByRolesResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<QueryRoleUsersByRolesResponse> queryRoleUsersByRolesWithHttpInfo(QueryRoleUsersByRolesArg body) throws ApiException {
        okhttp3.Call call = queryRoleUsersByRolesCall(body, null, null);
        Type localVarReturnType = new TypeToken<QueryRoleUsersByRolesResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 批量查询角色下的用户 (asynchronously)
     * 批量查询角色下的用户
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryRoleUsersByRolesAsync(QueryRoleUsersByRolesArg body, final ApiCallback<QueryRoleUsersByRolesResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryRoleUsersByRolesCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<QueryRoleUsersByRolesResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryUserRoleCodesByUsers */
    private okhttp3.Call queryUserRoleCodesByUsersCall(QueryUserRoleCodesByUsersArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/queryUserRoleCodesByUsers".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 批量查询用户分配的角色
     * 批量查询用户分配的角色
     * @param body  (optional)
     * @return QueryUserRoleCodesByUsersResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public QueryUserRoleCodesByUsersResponse queryUserRoleCodesByUsers(QueryUserRoleCodesByUsersArg body) throws ApiException {
        ApiResponse<QueryUserRoleCodesByUsersResponse> resp = queryUserRoleCodesByUsersWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 批量查询用户分配的角色
     * 批量查询用户分配的角色
     * @param body  (optional)
     * @return ApiResponse&lt;QueryUserRoleCodesByUsersResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<QueryUserRoleCodesByUsersResponse> queryUserRoleCodesByUsersWithHttpInfo(QueryUserRoleCodesByUsersArg body) throws ApiException {
        okhttp3.Call call = queryUserRoleCodesByUsersCall(body, null, null);
        Type localVarReturnType = new TypeToken<QueryUserRoleCodesByUsersResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 批量查询用户分配的角色 (asynchronously)
     * 批量查询用户分配的角色
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryUserRoleCodesByUsersAsync(QueryUserRoleCodesByUsersArg body, final ApiCallback<QueryUserRoleCodesByUsersResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryUserRoleCodesByUsersCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<QueryUserRoleCodesByUsersResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryUserRolesByUsers */
    private okhttp3.Call queryUserRolesByUsersCall(QueryUserRolesByUsersArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/queryUserRolesByUsers".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 批量查询用户的角色
     * 批量查询用户的角色
     * @param body  (optional)
     * @return QueryUserRolesByUsersResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public QueryUserRolesByUsersResponse queryUserRolesByUsers(QueryUserRolesByUsersArg body) throws ApiException {
        ApiResponse<QueryUserRolesByUsersResponse> resp = queryUserRolesByUsersWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 批量查询用户的角色
     * 批量查询用户的角色
     * @param body  (optional)
     * @return ApiResponse&lt;QueryUserRolesByUsersResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<QueryUserRolesByUsersResponse> queryUserRolesByUsersWithHttpInfo(QueryUserRolesByUsersArg body) throws ApiException {
        okhttp3.Call call = queryUserRolesByUsersCall(body, null, null);
        Type localVarReturnType = new TypeToken<QueryUserRolesByUsersResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 批量查询用户的角色 (asynchronously)
     * 批量查询用户的角色
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryUserRolesByUsersAsync(QueryUserRolesByUsersArg body, final ApiCallback<QueryUserRolesByUsersResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryUserRolesByUsersCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<QueryUserRolesByUsersResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for queryUsers */
    private okhttp3.Call queryUsersCall(QueryUsersArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/queryUsers".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 查询用户
     * 查询用户
     * @param body  (optional)
     * @return QueryUsersResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public QueryUsersResponse queryUsers(QueryUsersArg body) throws ApiException {
        ApiResponse<QueryUsersResponse> resp = queryUsersWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 查询用户
     * 查询用户
     * @param body  (optional)
     * @return ApiResponse&lt;QueryUsersResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<QueryUsersResponse> queryUsersWithHttpInfo(QueryUsersArg body) throws ApiException {
        okhttp3.Call call = queryUsersCall(body, null, null);
        Type localVarReturnType = new TypeToken<QueryUsersResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 查询用户 (asynchronously)
     * 查询用户
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call queryUsersAsync(QueryUsersArg body, final ApiCallback<QueryUsersResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = queryUsersCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<QueryUsersResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for roleInfo */
    private okhttp3.Call roleInfoCall(RoleInfoArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/roleInfo".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 角色查询
     * 角色查询
     * @param body  (optional)
     * @return RoleResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public RoleResponse roleInfo(RoleInfoArg body) throws ApiException {
        ApiResponse<RoleResponse> resp = roleInfoWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 角色查询
     * 角色查询
     * @param body  (optional)
     * @return ApiResponse&lt;RoleResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<RoleResponse> roleInfoWithHttpInfo(RoleInfoArg body) throws ApiException {
        okhttp3.Call call = roleInfoCall(body, null, null);
        Type localVarReturnType = new TypeToken<RoleResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 角色查询 (asynchronously)
     * 角色查询
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call roleInfoAsync(RoleInfoArg body, final ApiCallback<RoleResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = roleInfoCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<RoleResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for rolePermissCopy */
    private okhttp3.Call rolePermissCopyCall(RolePermissCopyArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/rolePermissCopy".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 角色权限复制
     * 角色权限复制
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult rolePermissCopy(RolePermissCopyArg body) throws ApiException {
        ApiResponse<BaseResult> resp = rolePermissCopyWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 角色权限复制
     * 角色权限复制
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> rolePermissCopyWithHttpInfo(RolePermissCopyArg body) throws ApiException {
        okhttp3.Call call = rolePermissCopyCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 角色权限复制 (asynchronously)
     * 角色权限复制
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call rolePermissCopyAsync(RolePermissCopyArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = rolePermissCopyCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for roleUser */
    private okhttp3.Call roleUserCall(RoleUserInfoArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/roleUser".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 角色下绑定的用户查询
     * 角色下绑定的用户查询
     * @param body  (optional)
     * @return RoleUserResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public RoleUserResponse roleUser(RoleUserInfoArg body) throws ApiException {
        ApiResponse<RoleUserResponse> resp = roleUserWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 角色下绑定的用户查询
     * 角色下绑定的用户查询
     * @param body  (optional)
     * @return ApiResponse&lt;RoleUserResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<RoleUserResponse> roleUserWithHttpInfo(RoleUserInfoArg body) throws ApiException {
        okhttp3.Call call = roleUserCall(body, null, null);
        Type localVarReturnType = new TypeToken<RoleUserResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 角色下绑定的用户查询 (asynchronously)
     * 角色下绑定的用户查询
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call roleUserAsync(RoleUserInfoArg body, final ApiCallback<RoleUserResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = roleUserCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<RoleUserResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for updateRole */
    private okhttp3.Call updateRoleCall(UpdateRoleArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/updateRole".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 角色信息更新
     * 角色信息更新
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult updateRole(UpdateRoleArg body) throws ApiException {
        ApiResponse<BaseResult> resp = updateRoleWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 角色信息更新
     * 角色信息更新
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> updateRoleWithHttpInfo(UpdateRoleArg body) throws ApiException {
        okhttp3.Call call = updateRoleCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 角色信息更新 (asynchronously)
     * 角色信息更新
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call updateRoleAsync(UpdateRoleArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = updateRoleCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for updateUserRole */
    private okhttp3.Call updateUserRoleCall(UpdateUserRoleArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/updateUserRole".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 用户绑定的角色更新
     * 用户绑定的角色更新
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult updateUserRole(UpdateUserRoleArg body) throws ApiException {
        ApiResponse<BaseResult> resp = updateUserRoleWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 用户绑定的角色更新
     * 用户绑定的角色更新
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> updateUserRoleWithHttpInfo(UpdateUserRoleArg body) throws ApiException {
        okhttp3.Call call = updateUserRoleCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 用户绑定的角色更新 (asynchronously)
     * 用户绑定的角色更新
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call updateUserRoleAsync(UpdateUserRoleArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = updateUserRoleCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for updateUserRoles */
    private okhttp3.Call updateUserRolesCall(UpdateUserRolesArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/updateUserRoles".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 批量更新用户角色
     * 批量更新用户角色
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult updateUserRoles(UpdateUserRolesArg body) throws ApiException {
        ApiResponse<BaseResult> resp = updateUserRolesWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 批量更新用户角色
     * 批量更新用户角色
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> updateUserRolesWithHttpInfo(UpdateUserRolesArg body) throws ApiException {
        okhttp3.Call call = updateUserRolesCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 批量更新用户角色 (asynchronously)
     * 批量更新用户角色
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call updateUserRolesAsync(UpdateUserRolesArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = updateUserRolesCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for userRoleInfo */
    private okhttp3.Call userRoleInfoCall(UserRoleInfoArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/userRoleInfo".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 用户分配的角色查询
     * 用户分配的角色查询
     * @param body  (optional)
     * @return UserRoleInfoResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public UserRoleInfoResponse userRoleInfo(UserRoleInfoArg body) throws ApiException {
        ApiResponse<UserRoleInfoResponse> resp = userRoleInfoWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 用户分配的角色查询
     * 用户分配的角色查询
     * @param body  (optional)
     * @return ApiResponse&lt;UserRoleInfoResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<UserRoleInfoResponse> userRoleInfoWithHttpInfo(UserRoleInfoArg body) throws ApiException {
        okhttp3.Call call = userRoleInfoCall(body, null, null);
        Type localVarReturnType = new TypeToken<UserRoleInfoResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 用户分配的角色查询 (asynchronously)
     * 用户分配的角色查询
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call userRoleInfoAsync(UserRoleInfoArg body, final ApiCallback<UserRoleInfoResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = userRoleInfoCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<UserRoleInfoResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
}
