package com.facishare.paas.appframework.core.predef.service;

import com.alibaba.fastjson.JSON;
import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.RequestId;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18N<PERSON>ey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.uievent.UIEventProcess;
import com.facishare.paas.appframework.core.predef.facade.CalculateServiceFacade;
import com.facishare.paas.appframework.core.predef.service.calculate.CalculateWithUIActionCallbackContainer;
import com.facishare.paas.appframework.core.predef.service.dto.calculate.*;
import com.facishare.paas.appframework.core.predef.service.dto.calculate.FindCalculationJobInfo.CalculationJobInfo;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.facishare.paas.appframework.metadata.dto.CheckFieldsForCalc;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.expression.Expression;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.expression.ExpressionDTO;
import com.facishare.paas.appframework.metadata.expression.ExpressionFactory;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.metadata.relation.*;
import com.facishare.paas.appframework.metadata.relation.CalculateRelation.RelateField;
import com.facishare.paas.appframework.metadata.repository.model.MtAsyncTaskMonitor;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.appframework.metadata.task.AsyncTaskService;
import com.facishare.paas.expression.exception.ExpressionCompileException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Quote;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.task.async.task.enums.TaskStatus;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.appframework.core.predef.service.dto.calculate.FindCalculateFieldsWithUnCompletedJob.CalculateFieldInfo;

/**
 * Created by linqiuying on 17/10/17.
 */
@ServiceModule("calculate")
@Component
@Slf4j
public class CalculateService {

    private static final long THIRTY_DAYS_MILLISECONDS = 30 * 24 * 60 * 60 * 1000L;

    @Autowired
    private MetaDataService metaDataService;

    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private ExpressionCalculateLogicService expressionCalculateLogicService;

    @Autowired
    private FieldRelationCalculateService fieldRelationCalculateService;

    @Autowired
    private FieldRelationGraphService fieldRelationGraphService;

    @Autowired
    private JobScheduleService jobScheduleService;

    @Autowired
    private RecordTypeLogicService recordTypeLogicService;

    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private InfraServiceFacade infraServiceFacade;

    @Autowired
    private QuoteValueService quoteValueService;

    @Autowired
    private MaskFieldLogicService maskFieldLogicService;

    @Autowired
    private RedissonService redissonService;

    @Autowired
    private CalculateServiceFacade calculateServiceFacade;
    @Autowired
    private AsyncTaskService asyncTaskService;

    @ServiceMethod("expressionCheck")
    public ExpressionCheck.Result expressionCheck(ExpressionCheck.Arg arg, ServiceContext context) {
        ExpressionDTO expressionDTO = JSON.parseObject(arg.getJson_data(), ExpressionDTO.class);
        expressionDTO.setExtFields(arg.getExtFields());
        expressionDTO.setOnlySupportGrounded(Boolean.TRUE.equals(arg.getOnlySupportGrounded()));
        String errMsg;
        try {
            expressionCalculateLogicService.compileCheck(context.getTenantId(), expressionDTO);
            return new ExpressionCheck.Result();
        } catch (StackOverflowError e) {
            log.warn("expression stack overflow,context:{},arg:{} ", context, arg, e);
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.EXPRESSION_STACK_OVERFLOW, "计算公式中嵌套条件太多，请减少条件判断数重试"));// ignoreI18n
        } catch (ObjectDefNotFoundError e) {
            throw new ValidateException(I18N.text(I18NKey.VERIFY_OBJECT_EXIST_AND_SAVE_SECCESS_END_CASE_ADD_CALC_FORMULA));
        } catch (ExpressionCompileException e) {
            log.warn("expression compile error,context:{},arg:{} ", context, arg, e);
            String syntaxError = I18N.text(I18NKey.FORMULA_SYNTAX_ERROR);
            if (BooleanUtils.isTrue(arg.getErrorReminder())
                    && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.EXPRESSION_CHECK_DETAIL_REMIND_GRAY, context.getTenantId())) {
                errMsg = e.getMessage();
                if (StringUtils.isNotEmpty(errMsg)) {
                    errMsg = errMsg.replace("\n", "<br>").replace(" ", "&nbsp;");
                }
                return ExpressionCheck.Result.builder()
                        .code(400)
                        .value(errMsg)
                        .build();
            } else {
                throw new MetaDataBusinessException(syntaxError);
            }
        }
    }

    @ServiceMethod("expressionDebug")
    public ExpressionDebug.Result expressionDebug(ExpressionDebug.Arg arg, ServiceContext context) {
        try {
            String result = expressionCalculateLogicService.expressionDebug(context.getTenantId(), arg.getExpressionObject(),
                    arg.getDataId());
            return ExpressionDebug.Result.builder().success(true).result(result).build();
        } catch (Exception e) {
            log.warn("expressionDebug failed,ei:{},arg:{}", context.getTenantId(), arg, e);
            return ExpressionDebug.Result.builder().success(false).errorMessage(e.getMessage()).build();
        }
    }

    @ServiceMethod("expressionCalculate")
    public ExpressionCalculate.Result expressionCalculate(ExpressionCalculate.Arg arg, ServiceContext context) {
        if (Strings.isNullOrEmpty(arg.getObjectDescribeApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR_OBJECT_API_NAME_IS_NULL));
        }
        IObjectData objectData = new ObjectData();
        objectData.fromJsonString(arg.getObject_data());
        objectData.setTenantId(context.getTenantId());
        objectData.setDescribeApiName(arg.getObjectDescribeApiName());

        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getObjectDescribeApiName());
        List<String> resultFieldNames = metaDataService.batchCalculate(context.getUser(), Lists.newArrayList(objectData),
                describe, Lists.newArrayList(arg.getApi_name()), arg.isCalculateFormulaOnly(), RequestUtil.isCepRequest());

        ExpressionCalculate.Result result = new ExpressionCalculate.Result();
        result.setValue_list(ObjectDataExt.of(objectData).toMap(resultFieldNames));

        return result;
    }

    @ServiceMethod("batchExpressionCalculate")
    public BatchExpressionCalculate.Result batchExpressionCalculate(BatchExpressionCalculate.Arg arg, ServiceContext context) {
        if (Strings.isNullOrEmpty(arg.getObjectDescribeApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR_OBJECT_API_NAME_IS_NULL));
        }
        IObjectData objectData = new ObjectData();
        objectData.fromJsonString(arg.getObject_data());
        objectData.setTenantId(context.getTenantId());
        objectData.setDescribeApiName(arg.getObjectDescribeApiName());

        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getObjectDescribeApiName());
        List<String> resultFieldNames = metaDataService.batchCalculate(context.getUser(), Lists.newArrayList(objectData),
                describe, arg.getApi_name_list(),
                arg.isCalculateFormulaOnly(), RequestUtil.isCepRequest());

        BatchExpressionCalculate.Result result = new BatchExpressionCalculate.Result();
        result.setValue_list(ObjectDataExt.of(objectData).toMap(resultFieldNames));

        return result;
    }

    @ServiceMethod("batchDataExpressionCalculate")
    public BatchDataExpressionCalculate.Result batchDataExpressionCalculate(BatchDataExpressionCalculate.Arg arg, ServiceContext context) {
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + ".batchDataExpressionCalculate");

        List<DataCalculatePojo> dataCalculatePojoList = JSON.parseArray(arg.getCalculate_data_list(), DataCalculatePojo.class);
        DataCalculatePojo pojo = dataCalculatePojoList.get(0);

        if (CollectionUtils.empty(pojo.getObjectDataList())) {
            return new BatchDataExpressionCalculate.Result();
        }
        List<CalculateObjectData> dataList = Lists.newArrayList();
        pojo.getObjectDataList().forEach((k, v) -> dataList.add(CalculateObjectData.of(k, new ObjectData(v))));
        CalculateObjectData.toList(dataList).forEach(x -> {
            x.setTenantId(context.getTenantId());
            x.setDescribeApiName(pojo.getObjectDescribeApiName());
        });
        stopWatch.lap("parseArg");

        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), pojo.getObjectDescribeApiName());
        stopWatch.lap("findObject");

        List<String> resultFieldNames = metaDataService.batchCalculate(context.getUser(), CalculateObjectData.toList(dataList),
                describe, pojo.getApi_name_list(), pojo.isCalculateFormulaOnly(), RequestUtil.isCepRequest());
        stopWatch.lap("batchCalculate");

        BatchDataExpressionCalculate.Result result = new BatchDataExpressionCalculate.Result();
        Map<String, Map<String, Object>> data = Maps.newHashMap();
        dataList.forEach(x -> data.put(x.getIndex(), ObjectDataExt.of(x.getObjectData()).toMap(resultFieldNames)));
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put(pojo.getObjectDescribeApiName(), data);
        result.setValue_list(resultMap);
        stopWatch.lap("buildResult");

        stopWatch.logSlow(3000);

        return result;
    }

    /**
     * 批量计算同一对象下的多条数据
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("calculateBatchData")
    public CalculateBatchData.Result calculateBatch(CalculateBatchData.Arg arg, ServiceContext context) {
        if (CollectionUtils.empty(arg.getCalculateFieldApiNames()) && CollectionUtils.empty(arg.getCalculateFields())) {
            return new CalculateBatchData.Result();
        }
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getObjectApiName());
        Map<String, List<String>> calculateFieldNameMap = Maps.newHashMap();
        Map<String, List<RelateField>> calculateFieldMap = Maps.newHashMap();
        FieldRelationGraph graph;
        if (CollectionUtils.empty(arg.getCalculateFields())) {
            CalculateFields calculateFields = fieldRelationCalculateService.computeCalculateFields(arg.getCalculateFieldApiNames(), describe);
            calculateFieldMap.putAll(calculateFields.getCalculateFieldMap());
            graph = calculateFields.getGraph();
        } else {
            calculateFieldMap.put(describe.getApiName(), arg.getCalculateFields());
            graph = fieldRelationGraphService.buildReverseDependencyGraph(Lists.newArrayList(describe),
                    false, true, true);
        }
        calculateFieldMap.forEach((k, v) -> calculateFieldNameMap.put(k, v.stream().map(RelateField::getFieldName).collect(Collectors.toList())));
        removeInvalidCalculateFields(calculateFieldMap, graph);

        Map<String, IObjectData> dataMap = arg.getDataMap().entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, it -> it.getValue().toObjectData()));
        // 移除没有指定需要计算数据
        dataMap.keySet().removeIf(it -> !CollectionUtils.nullToEmpty(arg.getModifiedDataIndexList()).contains(it));
        Map<String, IObjectDescribe> describeMap = Stream.of(describe)
                .collect(Collectors.toMap(IObjectDescribe::getApiName, it -> it));
        metaDataService.batchCalculateBySortFields(context.getUser(), Lists.newArrayList(dataMap.values()), describeMap, calculateFieldMap);

        CalculateBatchData.Result result = new CalculateBatchData.Result();
        calculateFieldNameMap.values().forEach(fieldNames ->
                dataMap.forEach((index, data) -> result.addData(index, data, fieldNames)));
        return result;
    }

    /**
     * 移除没有查到的描述
     * 移除不存在的字段以及不是计算字段、默认值、统计字段、引用字段的字段
     *
     * @param calculateFieldMap
     * @param graph
     */
    private void removeInvalidCalculateFields(Map<String, List<RelateField>> calculateFieldMap, FieldRelationGraph graph) {
        Map<String, IObjectDescribe> describeMap = graph.getDescribeMap();
        //移除没有查询到describe的字段
        Lists.newArrayList(calculateFieldMap.keySet()).stream().filter(x -> !describeMap.containsKey(x)).forEach(calculateFieldMap::remove);
        //移除不存在的字段以及不是计算字段、默认值、统计字段、引用字段的字段
        Lists.newArrayList(calculateFieldMap.keySet()).forEach(x -> {
            List<RelateField> fields = calculateFieldMap.get(x);
            fields.removeIf(y -> {
                Optional<FieldNode> fieldNodeOpt = graph.getNode(x, y.getFieldName());
                if (!fieldNodeOpt.isPresent()) {
                    return true;
                }
                FieldNode node = fieldNodeOpt.get();
                return !node.isCalculateField() && !node.isCountField() && !node.isQuoteField();
            });
        });
    }

    @Idempotent(expireTime = 60, waitTime = 10)
    @ServiceMethod("batchCalculate")
    public BatchCalculate.Result batchCalculate(BatchCalculate.Arg arg, @RequestId(propertyName = "idempotentKey") ServiceContext context) {
        if (Objects.isNull(arg)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_EMPTY));
        }

        //使用seriesId加锁，防止同一批次的请求并发执行
        if (!arg.skipConcurrentCheck()
                && StringUtils.isNotBlank(arg.getSeriesId())
                && UdobjGrayUtil.isObjectAndTenantGray(UdobjGrayKey.LOCK_BY_SERIES_ID_IN_CALCULATE_AND_UI_EVENT_AND_ADD_EDIT_REQUEST, context.getTenantId(), arg.getMasterObjectApiName())) {
            String lockKey = RequestUtil.buildSeriesKey(arg.getSeriesId(), context.getTenantId());
            RLock lock = redissonService.tryLockWithErrorMsg(0, 30, TimeUnit.SECONDS, lockKey,
                    I18NExt.text(I18NKey.PREVIOUS_REQUEST_PROCESSING_ALERT), AppFrameworkErrorCode.PREVIOUS_REQUEST_PROCESSING_ALERT.getCode());
            try {
                return doBatchCalculate(arg, context);
            } finally {
                redissonService.unlock(lock);
            }
        }

        return doBatchCalculate(arg, context);
    }

    private BatchCalculate.Result doBatchCalculate(BatchCalculate.Arg arg, ServiceContext context) {
        if ((CollectionUtils.empty(arg.getCalculateFieldApiNames()) && CollectionUtils.empty(arg.getCalculateFields()))) {
            return new BatchCalculate.Result();
        }
        boolean isGray = UdobjGrayUtil.isObjectAndTenantGray(UdobjGrayKey.BATCH_CALCULATE_RESULT_CHECK_MODIFY, context.getTenantId(), arg.getMasterObjectApiName());
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + ".batchCalculate");
        context.setApiResource(context.getServiceMethod());
        Set<String> describeApiNames = Sets.newHashSet(arg.getMasterObjectApiName());
        if (CollectionUtils.empty(arg.getCalculateFields())) {
            describeApiNames.addAll(arg.getCalculateFieldApiNames().keySet());
        } else {
            describeApiNames.addAll(arg.getCalculateFields().keySet());
        }
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(context.getTenantId(), describeApiNames);
        stopWatch.lap("findObjects");

        IObjectDescribe masterDescribe = describeMap.get(arg.getMasterObjectApiName());
        List<IObjectDescribe> detailDescribes = describeLogicService.findDetailDescribesCreateWithMaster(context.getTenantId(), masterDescribe.getApiName());
        Map<String, IObjectDescribe> detailDescribeMap = detailDescribes.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, x -> x));
        describeMap.putAll(detailDescribeMap);
        stopWatch.lap("findDetailDescribesCreateWithMaster");

        boolean applyOptimize = arg.applyOptimize(context.getTenantId(), detailDescribeMap.keySet());
        decodeMaskFieldEncryptValueByBatchCalculateArg(context.getUser(), arg, describeMap, applyOptimize);
        stopWatch.lap("processArgByMaskFieldEncryptData");

        IObjectData masterData = arg.getMasterData().toObjectData();
        masterData.setTenantId(context.getTenantId());
        masterData.setDescribeApiName(arg.getMasterObjectApiName());
        boolean isAddAction = !ObjectDataExt.of(masterData).hasId();

        Map<String, List<String>> calculateFieldNames = Maps.newHashMap();
        Map<String, List<RelateField>> calculateFieldMap;
        FieldRelationGraph graph;
        if (CollectionUtils.empty(arg.getCalculateFields())) {
            CalculateFields calculateFields = fieldRelationCalculateService.convertToCalculateFields(context.getTenantId(),
                    arg.getCalculateFieldApiNames(), describeMap);
            calculateFieldMap = calculateFields.getCalculateFieldMap();
            graph = calculateFields.getGraph();
            stopWatch.lap("convertToCalculateFields");
        } else {
            calculateFieldMap = arg.getCalculateFields();
            graph = fieldRelationGraphService.buildReverseDependencyGraph(Lists.newArrayList(describeMap.values()),
                    false, true, true);
        }
        if (!isGray) {
            calculateFieldMap.forEach((k, v) -> calculateFieldNames.put(k, v.stream().map(RelateField::getFieldName).filter(StringUtils::isNotBlank).collect(Collectors.toList())));
        }

        processCalculateFields(calculateFieldMap, masterDescribe, detailDescribeMap, graph, arg);
        stopWatch.lap("processCalculateFields");

        List<String> toCalculateDetailApiNames = calculateFieldMap.keySet().stream()
                .filter(detailDescribeMap::containsKey).collect(Collectors.toList());
        if (CollectionUtils.notEmpty(toCalculateDetailApiNames) && CollectionUtils.empty(arg.getDetailDataMap())) {
            arg.setDetailDataMap(Maps.newHashMap());
            toCalculateDetailApiNames.forEach(x -> arg.getDetailDataMap().put(x, null));
        }

        if (!isAddAction) {
            //和数据库里的数据进行合并，防止计算字段引用的字段因为布局中被隐藏而取不到值
            metaDataService.mergeWithDbData(context.getTenantId(), arg.getMasterObjectApiName(), Lists.newArrayList(masterData));
            stopWatch.lap("mergeMasterWithDbData");
        }

        BatchCalculate.Result result;
        Map<String, Map<String, IObjectData>> dataMap = Maps.newHashMap();
        dataMap.put(masterData.getDescribeApiName(), ImmutableMap.of("0", masterData));
        Map<String, List<CalculateObjectData>> detailCalculateDataMap = Maps.newHashMap();
        CalculateFields calculateFields = null;
        if (CollectionUtils.notEmpty(arg.getDetailDataMap())) {
            arg.getDetailDataMap().forEach((detailApiName, detailMap) -> {
                if (Objects.nonNull(detailMap)) {
                    detailCalculateDataMap.putIfAbsent(detailApiName, Lists.newArrayList());
                    detailMap.forEach((index, data) -> {
                        IObjectData detailData = data.toObjectData();
                        detailData.setTenantId(context.getTenantId());
                        detailData.setDescribeApiName(detailApiName);
                        boolean isModified = arg.detailDataModified(detailApiName, index);
                        detailCalculateDataMap.get(detailApiName).add(CalculateObjectData.of(index, detailData, isModified));
                    });
                }
            });

            if (!isAddAction) {
                //和数据库里的数据进行合并，防止计算字段引用的字段因为布局中被隐藏而取不到值
                detailCalculateDataMap.forEach((k, v) -> metaDataService.mergeWithDbData(context.getTenantId(), k, CalculateObjectData.toList(v)));
                stopWatch.lap("mergeDetailWithDbData");

                //前端没有传过来的detail直接使用数据库的数据
                toCalculateDetailApiNames.stream().filter(x -> Objects.isNull(detailCalculateDataMap.get(x))).filter(describeMap::containsKey).forEach(x -> {
                    List<IObjectData> detailDataList = metaDataService.findDetailObjectDataListIgnoreFormula(describeMap.get(x), masterData, context.getUser());
                    detailCalculateDataMap.put(x, CalculateObjectData.ofList(detailDataList, false));
                    stopWatch.lap("findDetailObjectDataListIgnoreFormula");
                });
            }

            Map<String, List<IObjectData>> detailDataMap = CalculateObjectData.toListMap(detailCalculateDataMap);
            if (applyOptimize) {
                log.info("applyOptimize=true");
                IObjectData oldMasterData = arg.oldMasterData();
                Map<String, List<IObjectData>> oldDetailDataMap = arg.oldDetailDataMap();
                //合并数据
                if (!isAddAction && arg.actByDeleteDetail()) {
                    oldDetailDataMap.forEach((k, v) -> metaDataService.mergeWithDbData(context.getTenantId(), k, v));
                    stopWatch.lap("mergeOldDetailWithDbData");
                }
                //构造主从计算参数
                EditCalculateParam editCalculateParam = EditCalculateParam.builder()
                        .masterData(masterData)
                        .oldMasterData(oldMasterData)
                        .detailDataMap(detailDataMap)
                        .oldDetailDataMap(oldDetailDataMap)
                        .masterModifyData(arg.masterModifyData())
                        .detailAddDataMap(arg.detailAddDataMap(detailCalculateDataMap))
                        .detailDeleteDataMap(arg.detailDeleteDataMap())
                        .detailModifyDataMap(arg.detailModifyDataMap())
                        .masterDescribe(masterDescribe)
                        .detailDescribeMap(detailDescribeMap)
                        .excludeDefaultValue(false)
                        .includeQuoteField(true)
                        .excludeLookupRelateField(false)
                        .getDataIndex(data -> ObjectDataExt.of(data).getTemporaryId())
                        .build();

                //计算字段依赖关系
                calculateFields = fieldRelationCalculateService.computeCalculateFieldsForEditData(editCalculateParam);
                //过滤不需要计算的字段
                if (arg.actByCopyDetail()) {
                    String modifiedObjectApiName = arg.getModifiedObjectApiName();
                    Map<String, List<RelateField>> lookupOrMasterCalculateFields = Maps.newHashMap();
                    calculateFields.getCalculateFieldMap().forEach((k, v) -> {
                        if (!detailDescribeMap.containsKey(k)) {
                            lookupOrMasterCalculateFields.put(k, v);
                        }
                    });
                    Set<RelateField> toRemoveCalculateFields = Sets.newHashSet();
                    calculateFields.getCalculateFieldMap().forEach((k, v) -> {
                        if (Objects.equals(modifiedObjectApiName, k)) {
                            v.forEach(relateField -> {
                                if (!arg.needCalculateAllData(k, relateField.getFieldName(), lookupOrMasterCalculateFields, graph)) {
                                    toRemoveCalculateFields.add(relateField);
                                }
                            });
                        }
                    });
                    //过滤不依赖主对象或关联对象的统计字段的默认值和计算字段
                    calculateFields.getCalculateFieldMap().getOrDefault(modifiedObjectApiName, Lists.newArrayList())
                            .removeAll(toRemoveCalculateFields);
                    calculateFields.getCalculateDataMap().getOrDefault(modifiedObjectApiName, Lists.newArrayList()).forEach(x ->
                            x.removeCalculateFields(toRemoveCalculateFields));
                }
                //过滤掉参数中没有的字段
                calculateFields.retainCalculateFields(calculateFieldMap);
                log.info("newCalculateFields:{},oldCalculateFields:{}", JacksonUtils.toJson(calculateFields.getCalculateFieldMap()),
                        JacksonUtils.toJson(calculateFieldMap));
                stopWatch.lap("computeCalculateFieldsForEditData");

                //过滤掉__r字段，防止__r字段返回老的值，特别是金额字段
                Set<String> extendFieldNames = calculateFields.getCalculateFieldMap().getOrDefault(masterDescribe.getApiName(), Collections.emptyList())
                        .stream().map(x -> FieldDescribeExt.getLookupNameByFieldName(x.getFieldName())).collect(Collectors.toSet());
                ObjectDataExt.of(masterData).remove(extendFieldNames);
                calculateFields.getCalculateDataMap().forEach((detailApiName, calculateDataList) -> {
                    calculateDataList.stream().filter(x -> CollectionUtils.notEmpty(x.getCalculateFields())).forEach(calculateData -> {
                        Set<String> detailExtendFieldNames = calculateData.getCalculateFields()
                                .stream().map(x -> FieldDescribeExt.getLookupNameByFieldName(x.getFieldName())).collect(Collectors.toSet());
                        ObjectDataExt.of(calculateData.getObjectData()).remove(detailExtendFieldNames);
                        //过滤特殊指定不需要计算的字段
                        if (CollectionUtils.notEmpty(arg.getExcludedDetailCalculateFields())) {
                            calculateData.removeCalculateFields(arg.getExcludedDetailCalculateFields().getOrDefault(detailApiName, Collections.emptyMap()).get(calculateData.getIndex()));
                        }
                    });
                });
                stopWatch.lap("removeExtendFields");
                //主从一起计算
                metaDataService.batchCalculateBySortFields(context.getUser(), masterData, detailDataMap, calculateFields);
                stopWatch.lap("batchCalculateBySortFieldsWithDetailData-optimize");
            } else if (CollectionUtils.notEmpty(arg.getExcludedDetailCalculateFields())) {
                calculateFields = CalculateFields.of(graph, calculateFieldMap, null, detailCalculateDataMap);
                detailCalculateDataMap.forEach((detailApiName, calculateDataList) -> {
                    calculateDataList.forEach(calculateData -> {
                        //本对象默认需要计算的字段
                        List<RelateField> selfCalculateFields = calculateFieldMap.getOrDefault(detailApiName, Collections.emptyList()).stream()
                                .filter(x -> x.isCalculateAllData() || calculateData.isModified()).collect(Collectors.toList());
                        calculateData.addCalculateFields(selfCalculateFields);
                        //过滤特殊指定不需要计算的字段
                        calculateData.removeCalculateFields(arg.getExcludedDetailCalculateFields().getOrDefault(detailApiName, Collections.emptyMap()).get(calculateData.getIndex()));
                        //查找关联对象上需要计算的字段
                        calculateFieldMap.forEach((apiName, relateFields) -> {
                            if (!masterDescribe.getApiName().equals(apiName) && !detailDescribeMap.containsKey(apiName)) {
                                calculateData.addRelateFields(apiName, relateFields);
                            }
                        });
                    });
                });
                //主从一起计算
                metaDataService.batchCalculateBySortFields(context.getUser(), masterData, detailDataMap, calculateFields);
                stopWatch.lap("batchCalculateBySortFieldsWithDetailData-excluded");
            } else {
                metaDataService.batchCalculateBySortFieldsWithDetailData(context.getUser(), masterData, detailCalculateDataMap, describeMap, calculateFieldMap);
                stopWatch.lap("batchCalculateBySortFieldsWithDetailData");
            }

            if (!isGray) {
                dataMap.putAll(CalculateObjectData.getCalculateDataMap(detailCalculateDataMap));
            }
        } else {
            metaDataService.batchCalculateBySortFields(context.getUser(), Lists.newArrayList(masterData), describeMap, calculateFieldMap);
            stopWatch.lap("batchCalculateBySortFields");
        }
        if (Objects.nonNull(calculateFields)) {
            result = buildOptimiseResult(masterData, calculateFields.getCalculateDataMap(), calculateFields.getCalculateFieldMap().get(masterDescribe.getApiName()));
        } else if (isGray) {
            result = buildResult(masterData, detailCalculateDataMap, calculateFieldMap);
        } else {
            result = buildDefaultResult(calculateFieldNames, dataMap);
        }
        stopWatch.lap("buildResult");
        fillInfo(context.getUser(), result, describeMap);
        stopWatch.lap("fillInfo");
        fillMaskFieldValueByBatchCalculateResult(context.getUser(), arg.getMasterObjectApiName(), result, describeMap, arg.getMaskFieldApiNames());
        stopWatch.lap("processResultByMaskFieldEncryptData");
        stopWatch.logSlow(3000);

        return result;
    }

    private void fillInfo(User user, BatchCalculate.Result result, Map<String, IObjectDescribe> describeMap) {
        removeCountAndFormulaExtraFiled(result, describeMap);
        fillUserInfo(user, result, describeMap);
    }

    private void fillUserInfo(User user, BatchCalculate.Result result, Map<String, IObjectDescribe> describeMap) {
        if (Objects.isNull(result) || CollectionUtils.empty(describeMap)) {
            return;
        }
        CollectionUtils.nullToEmpty(result.getCalculateResult()).forEach((objectApiName, documentMap) -> {
            IObjectDescribe objectDescribe = describeMap.get(objectApiName);
            if (Objects.isNull(objectDescribe)) {
                return;
            }
            List<IObjectData> dataList = documentMap.values().stream()
                    .map(ObjectDataDocument::toObjectData)
                    .collect(Collectors.toList());
            metaDataService.fillUserInfo(objectDescribe, dataList, user);
            metaDataService.fillDepartmentInfo(objectDescribe, dataList, user);
        });
    }

    private void removeCountAndFormulaExtraFiled(BatchCalculate.Result result, Map<String, IObjectDescribe> describeMap) {
        if (Objects.isNull(result) || CollectionUtils.empty(describeMap)) {
            return;
        }
        CollectionUtils.nullToEmpty(result.getCalculateResult()).forEach((objectApiName, documentMap) -> {
            IObjectDescribe objectDescribe = describeMap.get(objectApiName);
            if (Objects.isNull(objectDescribe)) {
                return;
            }
            ObjectDescribeExt.of(objectDescribe).getCountAndFormulaFields().forEach(filed ->
                    documentMap.values().forEach(x -> x.remove(FieldDescribeExt.getLookupNameByFieldName(filed.getApiName()))));
        });
    }

    private static BatchCalculate.Result buildDefaultResult(Map<String, List<String>> calculateFieldNames, Map<String, Map<String, IObjectData>> dataMap) {
        BatchCalculate.Result result = new BatchCalculate.Result();
        calculateFieldNames.forEach((describeApiName, fieldNames) -> {
            if (dataMap.containsKey(describeApiName)) {
                dataMap.get(describeApiName).forEach((index, data) -> result.addData(describeApiName, index, data, fieldNames));
            }
        });
        return result;
    }

    private BatchCalculate.Result buildResult(IObjectData masterData, Map<String, List<CalculateObjectData>> detailDataMap, Map<String, List<RelateField>> calculateFieldMap) {
        CalculateObjectData calculateObjectData = CalculateObjectData.of("0", masterData, true);
        Map<String, List<CalculateObjectData>> calculateObjectDataMap = Maps.newHashMap();
        calculateObjectDataMap.put(masterData.getDescribeApiName(), Lists.newArrayList(calculateObjectData));
        if (CollectionUtils.notEmpty(detailDataMap)) {
            calculateObjectDataMap.putAll(detailDataMap);
        }
        Map<String, Map<String, IObjectData>> calculateResult = CalculateObjectData.getCalculateDataMapWithRelatedField(calculateObjectDataMap, calculateFieldMap);
        BatchCalculate.Result result = new BatchCalculate.Result();
        result.addAll(calculateResult);
        return result;
    }

    private BatchCalculate.Result buildOptimiseResult(IObjectData masterData,
                                                      Map<String, List<CalculateObjectData>> detailCalculateDataMap,
                                                      List<RelateField> masterCalculateFields) {
        Map<String, Map<String, IObjectData>> dataResult = CalculateObjectData.getDataMapByCalculateDataMap(detailCalculateDataMap);
        if (CollectionUtils.notEmpty(masterCalculateFields)) {
            IObjectData masterResult = CalculateObjectData.getObjectDataWithRelateFields(masterData, masterCalculateFields);
            dataResult.put(masterData.getDescribeApiName(), ImmutableMap.of("0", masterResult));
        }
        BatchCalculate.Result result = new BatchCalculate.Result();
        result.addAll(dataResult);
        return result;
    }

    private void fillMaskFieldValueByBatchCalculateResult(User user, String masterObjectApiName, BatchCalculate.Result result, Map<String, IObjectDescribe> describeMap, Map<String, List<String>> maskFieldApiNames) {
        if (!AppFrameworkConfig.maskFieldEncryptGray(user.getTenantId(), masterObjectApiName)) {
            return;
        }
        if (CollectionUtils.empty(maskFieldApiNames)) {
            return;
        }

        CollectionUtils.nullToEmpty(result.getCalculateResult()).forEach((objectApiName, documentMap) -> {
            List<IObjectData> dataList = Lists.newArrayList(documentMap.values()).stream()
                    .map(ObjectDataDocument::toObjectData)
                    .collect(Collectors.toList());
            fillMaskFieldValue(user, describeMap.get(objectApiName), dataList, maskFieldApiNames);
        });
    }

    private void fillMaskFieldValue(User user, IObjectDescribe describe, List<IObjectData> objectDataList, Map<String, List<String>> maskFieldApiNames) {
        if (CollectionUtils.empty(maskFieldApiNames)) {
            return;
        }
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(describe);
        List<IFieldDescribe> maskFields = objectDescribeExt.getFieldByApiNames(maskFieldApiNames.get(describe.getApiName()));
        maskFieldLogicService.fillMaskFieldValue(user, describe, objectDataList, maskFields, MaskFieldLogicService.MaskFieldConfig.defaultMaskFieldConfig());
    }

    private void decodeMaskFieldEncryptValueByBatchCalculateArg(User user, BatchCalculate.Arg arg, Map<String, IObjectDescribe> describeMap, boolean applyOptimize) {
        if (!AppFrameworkConfig.maskFieldEncryptGray(user.getTenantId(), arg.getMasterObjectApiName())) {
            return;
        }
        Map<String, List<IObjectData>> dataDocumentMap = Maps.newHashMap();
        dataDocumentMap.put(arg.getMasterObjectApiName(), Lists.newArrayList(arg.getMasterData().toObjectData()));
        CollectionUtils.nullToEmpty(arg.getDetailDataMap()).forEach((objectApiName, documentMap) -> {
            List<IObjectData> dataList = Lists.newArrayList(documentMap.values()).stream()
                    .map(ObjectDataDocument::toObjectData)
                    .collect(Collectors.toList());
            dataDocumentMap.put(objectApiName, dataList);
        });
        decodeMaskFieldEncryptValue(user, describeMap, dataDocumentMap);
        //旧数据去掩码
        if (applyOptimize) {
            Map<String, List<IObjectData>> oldDataMap = Maps.newHashMap();
            if (CollectionUtils.notEmpty(arg.getOldMasterData())) {
                oldDataMap.put(arg.getMasterObjectApiName(), Lists.newArrayList(arg.getOldMasterData().toObjectData()));
            }
            if (CollectionUtils.notEmpty(arg.getOldDetailDataMap())) {
                arg.getOldDetailDataMap().forEach((detailApiName, oldDetailDataMap) -> {
                    List<IObjectData> oldDataList = ObjectDataDocument.ofDataList(Lists.newArrayList(oldDetailDataMap.values()));
                    if (CollectionUtils.notEmpty(oldDataList)) {
                        oldDataMap.put(detailApiName, oldDataList);
                    }
                });
            }
            decodeMaskFieldEncryptValue(user, describeMap, oldDataMap);
        }
    }

    private void decodeMaskFieldEncryptValue(User user, Map<String, IObjectDescribe> describeMap, Map<String, List<IObjectData>> dataDocumentMap) {
        dataDocumentMap.forEach((objectApiName, dataList) -> {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(describeMap.get(objectApiName));
            maskFieldLogicService.decodeMaskFieldEncryptValue(user, dataList, describeExt);
        });
    }

    private void processCalculateFields(Map<String, List<RelateField>> calculateFieldMap,
                                        IObjectDescribe masterDescribe,
                                        Map<String, IObjectDescribe> detailDescribeMap,
                                        FieldRelationGraph graph,
                                        BatchCalculate.Arg arg) {
        removeInvalidCalculateFields(calculateFieldMap, graph);
        Map<String, IObjectDescribe> describeMap = graph.getDescribeMap();

        //删除数据不需要计算只使用了常量和全局变量的默认值和计算字段
        if (arg.actByDeleteDetail()) {
            Lists.newArrayList(calculateFieldMap.keySet()).stream().filter(detailDescribeMap::containsKey).forEach(x -> {
                List<String> calculateFieldList = calculateFieldMap.get(x).stream()
                        .map(RelateField::getFieldName)
                        .collect(Collectors.toList());
                List<String> detailFieldsToRemove = ObjectDescribeExt.of(describeMap.get(x)).getFieldByApiNames(calculateFieldList).stream()
                        .filter(y -> FieldDescribeExt.of(y).isCalculateField()).filter(y -> {
                            Expression expression = ExpressionFactory.createExpression(describeMap.get(x), y);
                            return expression.noFieldInExpression();
                        }).map(IFieldDescribe::getApiName).collect(Collectors.toList());
                calculateFieldMap.get(x).removeIf(f -> detailFieldsToRemove.contains(f.getFieldName()));
            });
        }

        //判断从对象的字段是否需要计算该对象的所有数据
        List<RelateField> masterCountFields = calculateFieldMap.getOrDefault(masterDescribe.getApiName(), Collections.emptyList()).stream()
                .filter(x -> FieldDescribeExt.of(masterDescribe.getFieldDescribe(x.getFieldName())).isCountField())
                .collect(Collectors.toList());
        Map<String, List<RelateField>> lookupAndMasterCountFields = Maps.newHashMap();
        calculateFieldMap.forEach((k, v) -> {
            if (k.equals(masterDescribe.getApiName()) || detailDescribeMap.containsKey(k)) {
                return;
            }
            lookupAndMasterCountFields.put(k, v);
        });
        if (CollectionUtils.notEmpty(masterCountFields)) {
            lookupAndMasterCountFields.put(masterDescribe.getApiName(), masterCountFields);
        }
        calculateFieldMap.forEach((k, v) -> {
            if (detailDescribeMap.containsKey(k)) {
                v.forEach(relateField -> relateField.setCalculateAllData(arg.needCalculateAllData(k,
                        relateField.getFieldName(), lookupAndMasterCountFields, graph)));
            }
        });
    }

    @ServiceMethod("calculateWithExpression")
    public CalculateWithExpression.Result calculateWithExpression(CalculateWithExpression.Arg arg, ServiceContext context) {
        if (Strings.isNullOrEmpty(arg.getObjectApiName())) {
            throw new ValidateException(I18N.text(I18NKey.OBJECT_API_NAME_CAN_NOT_IS_NULL));
        }
        if (CollectionUtils.empty(arg.getExpressionList())) {
            return new CalculateWithExpression.Result(Maps.newHashMap());
        }
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getObjectApiName());
        IObjectData objectData = Objects.isNull(arg.getObjectData()) ? new ObjectData() : arg.getObjectData().toObjectData();
        objectData.setTenantId(context.getTenantId());
        objectData.setDescribeApiName(describe.getApiName());
        Map<String, Object> resultMap = expressionCalculateLogicService.calculateWithExpression(describe, objectData,
                arg.getExtData(), arg.getExpressionList());
        return new CalculateWithExpression.Result(resultMap);
    }

    @ServiceMethod("bulkDataCalculateWithExpression")
    public BulkDataCalculateWithExpression.Result bulkDataCalculateWithExpression(BulkDataCalculateWithExpression.Arg arg, ServiceContext context) {
        if (Strings.isNullOrEmpty(arg.getObjectApiName())) {
            throw new ValidateException(I18N.text(I18NKey.OBJECT_API_NAME_CAN_NOT_IS_NULL));
        }
        if (CollectionUtils.empty(arg.getExpressionList())) {
            return new BulkDataCalculateWithExpression.Result(Maps.newHashMap());
        }
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getObjectApiName());
        Map<String, Map<String, Object>> calcResult = expressionCalculateLogicService.bulkDataCalculateWithExpression(describe, arg.getDataCtx(), arg.getExpressionList());
        return new BulkDataCalculateWithExpression.Result(calcResult);
    }

    @ServiceMethod("batchCalculateCountFields")
    public BatchCalculateCountFields.Result batchCalculateCountFields(BatchCalculateCountFields.Arg arg, ServiceContext context) {
        Map<String, Object> values = metaDataService.calculateCountField(context.getUser(), arg.getObjectApiName(),
                arg.getObjectDataId(), arg.getCountFieldDescribeList());
        return BatchCalculateCountFields.Result.builder().countFieldValues(ObjectDataDocument.of(values)).build();
    }

    @ServiceMethod("calculateAndUpdateFormulaFields")
    public CalculateAndUpdateFormulaFields.Result calculateAndUpdateFormulaFields(CalculateAndUpdateFormulaFields.Arg arg, ServiceContext context) {
        if (Strings.isNullOrEmpty(arg.getObjectApiName())) {
            throw new ValidateException(I18N.text(I18NKey.OBJECT_API_NAME_CAN_NOT_IS_NULL));
        }

        if (CollectionUtils.empty(arg.getDataIds())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        if (CollectionUtils.empty(arg.getFieldApiNames())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        Map<String, Map<String, Object>> result = metaDataService.calculateAndUpdateFormulaFields(context.getUser(),
                arg.getObjectApiName(), arg.getDataIds(), arg.getFieldApiNames(), arg.calcRelateField());
        if (!RequestUtil.isCepRequest()) {
            return CalculateAndUpdateFormulaFields.Result.builder().result(result).build();
        }
        return CalculateAndUpdateFormulaFields.Result.builder().build();
    }

    @ServiceMethod("calculateCountOrFormulaField")
    public CalculateCountOrFormulaField.Result calculateCountOrFormulaField(CalculateCountOrFormulaField.Arg arg, ServiceContext context) {
        if (Strings.isNullOrEmpty(arg.getObjectApiName())) {
            throw new ValidateException(I18N.text(I18NKey.OBJECT_API_NAME_CAN_NOT_IS_NULL));
        }

        if (Strings.isNullOrEmpty(arg.getFieldApiName()) || Strings.isNullOrEmpty(arg.getDataId())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        IObjectDescribe objectDescribe = describeLogicService.findObject(context.getTenantId(), arg.getObjectApiName());
        IFieldDescribe field = ObjectDescribeExt.of(objectDescribe).getActiveFieldDescribe(arg.getFieldApiName());
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(field);

        IObjectData data = metaDataService.findObjectData(context.getTenantId(), arg.getDataId(), objectDescribe);

        Map<String, Object> result = Maps.newHashMap();
        if (fieldDescribeExt.isQuoteField()) {
            quoteValueService.fillQuoteFieldValue(context.getUser(), Lists.newArrayList(data), objectDescribe, null,
                    true, Lists.newArrayList((Quote) field), null, false);
            String valueFieldName = FieldDescribeExt.getQuotedValueNameByFieldName(field.getApiName());
            String otherFieldName = FieldDescribeExt.getQuotedValueNameByFieldName(field.getApiName());
            String displayFieldName = FieldDescribeExt.getLookupNameByFieldName(field.getApiName());
            if (ObjectDataExt.of(data).containsField(valueFieldName)) {
                result.put(valueFieldName, data.get(valueFieldName));
            }
            if (ObjectDataExt.of(data).containsField(otherFieldName)) {
                result.put(otherFieldName, data.get(otherFieldName));
            }
            if (ObjectDataExt.of(data).containsField(displayFieldName)) {
                result.put(displayFieldName, data.get(displayFieldName));
            }
        } else if (fieldDescribeExt.isCountField()) {
            IObjectDescribe detailDescribe = describeLogicService.findObject(context.getTenantId(), ((Count) field).getSubObjectDescribeApiName());
            metaDataService.calculateCountField(data, objectDescribe, detailDescribe, Lists.newArrayList((Count) field));
        } else {
            expressionCalculateLogicService.bulkCalculate(objectDescribe, Lists.newArrayList(data), Lists.newArrayList(field));
        }
        result.put(arg.getFieldApiName(), data.get(arg.getFieldApiName()));

        return CalculateCountOrFormulaField.Result.builder().result(ObjectDataDocument.of(result)).build();
    }

    @ServiceMethod("submit_calculate_job")
    public SubmitCalculateJob.Result submitCalculateJob(SubmitCalculateJob.Arg arg, ServiceContext context) {
        if (!arg.isManual()) {
            if (CollectionUtils.empty(arg.getCalculateJobList())) {
                throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
            }
            arg.getCalculateJobList().forEach(x -> {
                if (Strings.isNullOrEmpty(x.getObjectApiName()) || CollectionUtils.empty(x.getFieldApiNameList())) {
                    throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
                }
            });
        }
        List<IObjectDescribe> describeList;
        boolean submitAllDescribe = false;
        if (CollectionUtils.notEmpty(arg.getCalculateJobList())) {
            List<String> objectApiNameList = arg.getCalculateJobList().stream().map(SubmitCalculateJob.CalculateJob::getObjectApiName).distinct().collect(Collectors.toList());
            describeList = Lists.newArrayList(describeLogicService.findObjects(context.getTenantId(), objectApiNameList).values());
        } else {
            describeList = describeLogicService.findDescribeListWithFields(context.getUser());
            //只处理指定时间以后编辑过的对象
            if (arg.getLastModifiedTime() != null) {
                describeList = describeList.stream()
                        .filter(x -> x.getLastModifiedTime() != null && x.getLastModifiedTime() > arg.getLastModifiedTime())
                        .collect(Collectors.toList());
            }
            describeList = sortDescribeList(describeList);
            submitAllDescribe = true;
        }
        Map<String, List<String>> calculateFieldMap = Maps.newHashMap();
        CollectionUtils.nullToEmpty(arg.getCalculateJobList()).forEach(x -> calculateFieldMap.put(x.getObjectApiName(), x.getFieldApiNameList()));

        for (IObjectDescribe describe : describeList) {
            if (!describe.isActive()) {
                continue;
            }
            List<String> fieldNameList = null;
            try {
                if (calculateFieldMap.containsKey(describe.getApiName())) {
                    if (CollectionUtils.notEmpty(calculateFieldMap.get(describe.getApiName()))) {
                        fieldNameList = calculateFieldMap.get(describe.getApiName());
                        //只允许提交统计字段、计算字段、特殊状态字段的计算任务
                        Set<String> allCalculateFields = ObjectDescribeExt.of(describe).getActiveFieldDescribes().stream()
                                .filter(x -> FieldDescribeExt.of(x).isCalculateFieldsNeedStoreInDB())
                                .map(IFieldDescribe::getApiName)
                                .collect(Collectors.toSet());
                        fieldNameList.removeIf(x -> !allCalculateFields.contains(x));
                    } else {
                        fieldNameList = ObjectDescribeExt.of(describe).getActiveFieldDescribes().stream()
                                .filter(x -> FieldDescribeExt.of(x).isCalculateFieldsNeedStoreInDB())
                                .map(IFieldDescribe::getApiName)
                                .collect(Collectors.toList());
                    }
                } else {
                    if (submitAllDescribe) {
                        describe = describeLogicService.findObject(context.getTenantId(), describe.getApiName());
                    }
                    fieldNameList = ObjectDescribeExt.of(describe).getActiveFieldDescribes().stream()
                            .filter(x -> FieldDescribeExt.of(x).isCalculateFieldsNeedStoreInDB())
                            .map(IFieldDescribe::getApiName)
                            .collect(Collectors.toList());
                }
                calculateFieldMap.put(describe.getApiName(), fieldNameList);
                if (CollectionUtils.notEmpty(fieldNameList)) {
                    jobScheduleService.submitCalculateJob(context.getUser(), fieldNameList, describe.getApiName(), arg.isManual());
                    log.warn("submit calculate job success,tenantId:{},objectApiName:{},fieldNameList:{},manual:{}",
                            context.getTenantId(), describe.getApiName(), fieldNameList, arg.isManual());
                } else {
                    log.warn("no field need calculate");
                }
            } catch (MetaDataBusinessException e) {
                log.warn("submit calculate job failed,tenantId:{},objectApiName:{},fieldNameList:{},errMsg:{}",
                        context.getTenantId(), describe.getApiName(), fieldNameList, e.getMessage());
            } catch (Exception e) {
                log.error("submit calculate job failed,tenantId:{},objectApiName:{},fieldNameList:{},errMsg:{}",
                        context.getTenantId(), describe.getApiName(), fieldNameList, e.getMessage(), e);
            }
        }

        return new SubmitCalculateJob.Result(calculateFieldMap);
    }

    private List<IObjectDescribe> sortDescribeList(List<IObjectDescribe> describeList) {
        if (CollectionUtils.empty(describeList)) {
            return describeList;
        }
        List<IObjectDescribe> result = describeList.stream().filter(x -> IObjectDescribe.DEFINE_TYPE_CUSTOM.equals(x.getDefineType())).collect(Collectors.toList());
        result.addAll(describeList.stream().filter(x -> !result.contains(x)).collect(Collectors.toList()));

        return result;
    }

    @ServiceMethod("check_calculate_fields")
    public CheckCalculateFields.Result checkCalculateFields(CheckCalculateFields.Arg arg, ServiceContext context) {
        if (Strings.isNullOrEmpty(arg.getObjectApiName()) || CollectionUtils.empty(arg.getFieldList())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getObjectApiName());
        List<IFieldDescribe> fieldDescribes = arg.getFieldList().stream()
                .map(x -> (IFieldDescribe) FieldDescribeExt.of(x).getFieldDescribe()).collect(Collectors.toList());
        fieldDescribes.forEach(x -> x.setDescribeApiName(arg.getObjectApiName()));

        fieldRelationCalculateService.validateByFields(describe, fieldDescribes, false);

        return new CheckCalculateFields.Result(true);
    }

    @ServiceMethod("batchCalculateOfRecordType")
    public BatchCalculateOfRecordType.Result batchCalculateOfRecordType(BatchCalculateOfRecordType.Arg arg, ServiceContext context) {
        IObjectData masterData = arg.getMasterData().toObjectData();
        masterData.setRecordType(arg.getRecordType());
        Map<String, List<IObjectData>> detailDataMap = Maps.newHashMap();
        arg.getDetailDataMap().forEach((apiName, dataList) -> {
            List<IObjectData> objectDataList = dataList.stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
            detailDataMap.put(apiName, objectDataList);
        });
        BatchCalculateOfRecordType.Result result = BatchCalculateOfRecordType.Result.builder()
                .objectData(arg.getMasterData())
                .detail(arg.getDetailDataMap())
                .build();
        if (Strings.isNullOrEmpty(arg.getRecordType()) || CollectionUtils.empty(detailDataMap)) {
            return result;
        }
        Map<String, List<IRecordTypeOption>> validRecordTypeListMap = recordTypeLogicService.findValidRecordTypeListMap(Lists.newArrayList(detailDataMap.keySet()), context.getUser());
        validRecordTypeListMap = recordTypeLogicService.filterUnMatchRecordTypes(context.getUser().getTenantId(), validRecordTypeListMap, arg.getMasterObjectApiName(), arg.getRecordType());

        List<String> filterDetail = Lists.newArrayList();
        for (Map.Entry<String, List<IObjectData>> detailData : detailDataMap.entrySet()) {
            List<IObjectData> detailDataList = detailData.getValue();
            List<String> validRecordType = validRecordTypeListMap.getOrDefault(detailData.getKey(), Lists.newArrayList()).stream().map(IRecordTypeOption::getApiName).collect(Collectors.toList());
            boolean present = detailDataList.stream().anyMatch(x -> !validRecordType.contains(x.getRecordType()));
            if (!present) {
                continue;
            }
            filterDetail.add(detailData.getKey());
            detailDataList.removeIf(x -> !validRecordType.contains(x.getRecordType()));
        }

        if (CollectionUtils.notEmpty(filterDetail)) {
            List<String> apiNameList = Lists.newArrayList(detailDataMap.keySet());
            apiNameList.add(arg.getMasterObjectApiName());
            Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(context.getTenantId(), apiNameList);
            IObjectDescribe masterDescribe = describeMap.get(arg.getMasterObjectApiName());
            describeMap.remove(arg.getMasterObjectApiName());
            if (StringUtils.isNotEmpty(arg.getRuleApiName())) {
                fieldRelationCalculateService.computeCalculateFieldsForConvertAction(context.getUser(), masterDescribe, Lists.newArrayList(describeMap.values()), masterData, detailDataMap, filterDetail, () -> infraServiceFacade.findConvertRuleByApiName(context.getUser(), arg.getRuleApiName()));
            } else {
                fieldRelationCalculateService.computeCalculateFieldsForMappingAction(context.getUser(), arg.getButtonApiName(), arg.getOriginalObjectApiName(), masterDescribe, Lists.newArrayList(describeMap.values()), masterData, detailDataMap, filterDetail);
            }
        }

        result.setObjectData(ObjectDataDocument.of(masterData));
        result.setDetail(ObjectDataDocument.ofMap(detailDataMap));

        return result;
    }

    @ServiceMethod("calculate_with_drafts")
    public CalculateWithDrafts.Result calculateWithDrafts(CalculateWithDrafts.Arg arg, ServiceContext context) {
        IObjectData masterData = arg.getObjectData().toObjectData();
        Map<String, List<IObjectData>> detailDataMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(arg.getDetailDataMap())) {
            arg.getDetailDataMap().forEach((apiName, dataList) -> {
                List<IObjectData> objectDataList = dataList.stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
                detailDataMap.put(apiName, objectDataList);
            });
        }
        IObjectDescribe objectDescribe = describeLogicService.findObject(context.getTenantId(), masterData.getDescribeApiName());
        Map<String, IObjectDescribe> detailDescribes = describeLogicService.findObjects(context.getTenantId(), detailDataMap.keySet());
        decodeMaskFieldEncryptValueByCalculateWithDraftsArg(context.getUser(), objectDescribe, detailDescribes, masterData, detailDataMap);

        metaDataService.calculateForDrafts(context.getUser(), objectDescribe, masterData, Lists.newArrayList(detailDescribes.values()),
                detailDataMap, arg.getSkipCalculateFields(), arg.getSkipCalculateDVField());

        fillMaskFieldValueByCalculateWithDraftsResult(context.getUser(), objectDescribe, detailDescribes, masterData, detailDataMap);
        return CalculateWithDrafts.Result.builder()
                .objectData(ObjectDataDocument.of(masterData))
                .detailDataMap(ObjectDataDocument.ofMap(detailDataMap))
                .build();
    }

    private void decodeMaskFieldEncryptValueByCalculateWithDraftsArg(User user, IObjectDescribe objectDescribe, Map<String, IObjectDescribe> detailDescribes,
                                                                     IObjectData masterData, Map<String, List<IObjectData>> detailDataMap) {
        if (!AppFrameworkConfig.maskFieldEncryptGray(user.getTenantId(), objectDescribe.getApiName())) {
            return;
        }
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap(detailDescribes);
        describeMap.put(objectDescribe.getApiName(), objectDescribe);

        Map<String, List<IObjectData>> objectDataMap = Maps.newHashMap(detailDataMap);
        objectDataMap.put(masterData.getDescribeApiName(), Lists.newArrayList(masterData));
        decodeMaskFieldEncryptValue(user, describeMap, objectDataMap);
    }

    private void fillMaskFieldValueByCalculateWithDraftsResult(User user, IObjectDescribe objectDescribe, Map<String, IObjectDescribe> detailDescribes,
                                                               IObjectData masterData, Map<String, List<IObjectData>> detailDataMap) {
        if (!AppFrameworkConfig.maskFieldEncryptGray(user.getTenantId(), objectDescribe.getApiName())) {
            return;
        }
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(objectDescribe.getApiName(), objectDescribe);
        describeMap.putAll(detailDescribes);
        MaskFieldLogicService.MaskFieldConfig maskFieldConfig = MaskFieldLogicService.MaskFieldConfig.defaultMaskFieldConfig();
        maskFieldConfig.setUseCurrentUser(true);
        maskFieldLogicService.processMaskFieldValue(user, masterData, detailDataMap, describeMap, maskFieldConfig);
    }

    @Deprecated
    @ServiceMethod("calculateWithUIActionCallback")
    public CalculateWithEditInfo.Result calculateWithUIActionCallback(CalculateWithEditInfo.Arg arg,
                                                                      ServiceContext context) {
        return calculateWithEditInfo(arg, context);
    }

    @ServiceMethod("calculateWithEditInfo")
    public CalculateWithEditInfo.Result calculateWithEditInfo(CalculateWithEditInfo.Arg arg,
                                                              ServiceContext context) {
        if (Objects.isNull(arg)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_EMPTY));
        }
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        CalculateWithUIActionCallbackContainer container = CalculateWithUIActionCallbackContainer.builder()
                .serviceFacade(serviceFacade)
                .infraServiceFacade(infraServiceFacade)
                .requestContext(context.getRequestContext())
                .objectDescribe(describe)
                .objectData(arg.getBeforeDataInfo().getMasterData())
                .details(arg.getBeforeDataInfo().getDetailData())
                .build()
                .initProcessor();
        UIEventProcess.ProcessRequest request = calculateServiceFacade.calculateWithUIActionCallback(container,
                arg.getAfterDataInfo().getMasterData(), arg.getAfterDataInfo().getDetailData());
        return CalculateWithEditInfo.Result.of(request.getMasterData(), request.getDetailWithOnlyChangedFields());
    }

    @ServiceMethod("checkFieldsForCalc")
    public CheckFieldsForCalc.Result checkFields(CheckFieldsForCalc.Arg arg, ServiceContext context) {
        List<IFieldDescribe> fieldDescribes = arg.getFieldDescribes().stream().map(FieldDescribeFactory::newInstance).collect(Collectors.toList());
        return serviceFacade.checkFieldsForCalc(context.getUser(), arg.getDescribeAPIName(), arg.getFactor(), arg.isFieldInfo(), fieldDescribes);
    }

    @ServiceMethod("calculateAndUpdateByOriginalData")
    public CalculateAndUpdateByOriginalData.Result calculateAndUpdateByOriginalData(CalculateAndUpdateByOriginalData.Arg arg, ServiceContext context) {
        if (Strings.isNullOrEmpty(arg.getObjectApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_EMPTY) + ":objectApiName");
        }
        if (Strings.isNullOrEmpty(arg.getDataId())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_EMPTY) + ":dataId");
        }
        metaDataService.calculateAndUpdateByOriginalData(context.getUser(), arg.getObjectApiName(), arg.getDataId(), arg.getBeforeData());
        return new CalculateAndUpdateByOriginalData.Result();
    }

    @ServiceMethod("findCalculateFieldsWithUnCompletedJob")
    public FindCalculateFieldsWithUnCompletedJob.Result findCalculateFieldsWithUnCompletedJob(FindCalculateFieldsWithUnCompletedJob.Arg arg, ServiceContext context) {
        if (Strings.isNullOrEmpty(arg.getObjectApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_EMPTY) + ":objectApiName");
        }
        FindCalculateFieldsWithUnCompletedJob.Result result = new FindCalculateFieldsWithUnCompletedJob.Result();
        result.setSupportCalculationProgress(true);
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopy(context.getTenantId(), arg.getObjectApiName());
        long defaultStartTime = System.currentTimeMillis() - THIRTY_DAYS_MILLISECONDS;
        long startTime = Objects.isNull(describe.getCreateTime()) ? defaultStartTime : Math.max(describe.getCreateTime(), defaultStartTime);
        SearchQuery searchQuery = SearchQueryImpl.filters(Lists.newArrayList(FilterExt.of(Operator.GTE, IObjectData.CREATE_TIME, String.valueOf(startTime)).getFilter(),
                FilterExt.of(Operator.IN, MtAsyncTaskMonitor.TASK_STATUS, Lists.newArrayList(TaskStatus.BE_QUEUING.getValue(), TaskStatus.IN_PROCESS.getValue())).getFilter(),
                FilterExt.of(Operator.EQ, MtAsyncTaskMonitor.DESCRIBE_API_NAME, arg.getObjectApiName()).getFilter()));
        List<MtAsyncTaskMonitor> jobList = asyncTaskService.findBySearchQuery(context.getUser(), searchQuery);
        if (CollectionUtils.notEmpty(jobList)) {
            List<CalculateFieldInfo> calculateFields = jobList.stream()
                    .filter(jobInfo -> !jobInfo.completed())
                    .flatMap(jobInfo -> CalculateFieldInfo.of(jobInfo, describe).stream())
                    .collect(Collectors.toList());
            result.setCalculateFields(calculateFields);
        }
        return result;
    }

    @ServiceMethod("findCalculationJobInfo")
    public FindCalculationJobInfo.Result findCalculationJobInfo(FindCalculationJobInfo.Arg arg, ServiceContext context) {
        if (Strings.isNullOrEmpty(arg.getJobId())) {
            if (Strings.isNullOrEmpty(arg.getObjectApiName())) {
                throw new ValidateException(I18NExt.text(I18NKey.PARAM_EMPTY) + ":objectApiName");
            }
            if (Strings.isNullOrEmpty(arg.getFieldApiName())) {
                throw new ValidateException(I18NExt.text(I18NKey.PARAM_EMPTY) + ":fieldApiName");
            }
        }
        FindCalculationJobInfo.Result result = new FindCalculationJobInfo.Result();
        CalculationJobInfo jobInfo;
        if (!Strings.isNullOrEmpty(arg.getJobId())) {
            jobInfo = findJobInfoById(context.getUser(), arg.getJobId());
        } else {
            jobInfo = findJobInfoByField(context.getUser(), arg.getObjectApiName(), arg.getFieldApiName());
        }
        result.setJobInfo(jobInfo);
        return result;
    }

    private CalculationJobInfo findJobInfoById(User user, String jobId) {
        MtAsyncTaskMonitor jobInfo = asyncTaskService.find(user, jobId);
        if (Objects.isNull(jobInfo)) {
            return null;
        }
        return CalculationJobInfo.of(jobInfo);
    }

    private CalculationJobInfo findJobInfoByField(User user, String objectApiName, String fieldApiName) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopy(user.getTenantId(), objectApiName);
        IFieldDescribe field = describe.getFieldDescribe(fieldApiName);
        if (Objects.isNull(field) || !FieldDescribeExt.of(field).isCalculateFieldsNeedStoreInDB()) {
            log.warn("not calculate field,tenantId:{},objectApiName:{},fieldApiName:{}", user.getTenantId(), objectApiName,
                    fieldApiName);
            return null;
        }
        long defaultStartTime = System.currentTimeMillis() - THIRTY_DAYS_MILLISECONDS;
        long startTime = Objects.isNull(describe.getCreateTime()) ? defaultStartTime : Math.max(describe.getCreateTime(), defaultStartTime);
        SearchQuery searchQuery = SearchQueryImpl.filters(Lists.newArrayList(FilterExt.of(Operator.GTE, IObjectData.CREATE_TIME, String.valueOf(startTime)).getFilter(),
                FilterExt.of(Operator.IN, MtAsyncTaskMonitor.TASK_STATUS, Lists.newArrayList(TaskStatus.BE_QUEUING.getValue(), TaskStatus.IN_PROCESS.getValue())).getFilter(),
                FilterExt.of(Operator.EQ, MtAsyncTaskMonitor.DESCRIBE_API_NAME, objectApiName).getFilter()));
        List<MtAsyncTaskMonitor> jobList = asyncTaskService.findBySearchQuery(user, searchQuery);
        if (CollectionUtils.empty(jobList)) {
            return null;
        }
        return jobList.stream()
                .filter(job -> {
                    List<String> params = Lists.newArrayList(StringUtils.split(job.getBizApiName(), ","));
                    return params.contains(fieldApiName);
                })
                .findFirst()
                .map(CalculationJobInfo::of)
                .orElse(null);
    }

}
