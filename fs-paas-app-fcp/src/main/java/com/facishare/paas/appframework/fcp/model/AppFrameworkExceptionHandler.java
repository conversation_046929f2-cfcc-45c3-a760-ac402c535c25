package com.facishare.paas.appframework.fcp.model;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.fcp.exception.InvalidFcpRequestException;
import com.facishare.fcp.handler.ExceptionHandler;
import com.facishare.fcp.protocol.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.metadata.exception.MetadataValidateException;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;

/**
 * AppFramework Fcp biz server exception handler
 */
@Slf4j
@Component
public class AppFrameworkExceptionHandler implements ExceptionHandler {
    @Override
    public void handle(FcpRequest request, FcpResponse response, Throwable throwable) {

        if (throwable instanceof AppBusinessException) {
            AppBusinessException businessException = (AppBusinessException) throwable;
            response.setMessageType(FcpMessageType.FAILURE);
            response.addFcpBody(FcpBody.createFcpBody(businessException.getMessage()));
            response.addFcpHeader(FcpHeader.createFcpHeaderBuilder(FcpHeader.FcpHeaderType.FAIL_CODE)
                    .setValue(businessException.getErrorCode())
                    .build());
        } else if (throwable instanceof RestProxyBusinessException) {
            RestProxyBusinessException businessException = (RestProxyBusinessException) throwable;
            response.setMessageType(FcpMessageType.FAILURE);
            response.addFcpBody(FcpBody.createFcpBody(businessException.getMessage()));
            response.addFcpHeader(FcpHeader.createFcpHeaderBuilder(FcpHeader.FcpHeaderType.FAIL_CODE)
                    .setValue(businessException.getCode())
                    .build());
        } else if (throwable instanceof MetadataValidateException) {
            MetadataValidateException validateException = (MetadataValidateException) throwable;
            response.setMessageType(FcpMessageType.FAILURE);
            response.addFcpBody(FcpBody.createFcpBody(validateException.getMessage()));
            response.addFcpHeader(FcpHeader.createFcpHeaderBuilder(FcpHeader.FcpHeaderType.FAIL_CODE)
                    .setValue(validateException.getErrorCode().getCode())
                    .build());
        } else if (throwable instanceof InvalidFcpRequestException) {
            response.setMessageType(FcpMessageType.FAILURE);
            response.addFcpBody(FcpBody.createFcpBody(throwable.getMessage()));
            response.addFcpHeader(FcpHeader.createFcpHeaderBuilder(FcpHeader.FcpHeaderType.FAIL_CODE)
                    .setValue(AppFrameworkErrorCode.REQUEST_RESOURCE_NOT_FOUND.getCode())
                    .build());
        } else if (throwable instanceof APPException) {
            log.error("", throwable);
            APPException appException = (APPException) throwable;
            response.setMessageType(FcpMessageType.ERROR);
            response.addFcpHeader(FcpHeader.createFcpHeaderBuilder(FcpHeader.FcpHeaderType.I18N_ERROR_CODE)
                    .setValue(appException.getErrorCode().getCode()).build());
            response.addFcpHeader(FcpHeader.createFcpHeaderBuilder(FcpHeader.FcpHeaderType.I18N_ERROR_MESSAGE)
                    .setValue(appException.getErrorCode().getDescription()).build());
            String[] placeholders = appException.getSupplements();
            if (Objects.nonNull(placeholders) && placeholders.length > 0) {
                response.addFcpHeader(FcpHeader.createFcpHeaderBuilder(FcpHeader.FcpHeaderType.I18N_ERROR_MESSAGE_PARAMS)
                        .setValue(Arrays.toString(placeholders)).build());
            }
            if (request.getLongHeader(FcpHeader.FcpHeaderType.TRACE_COLOR) == 1) {
                response.addFcpBody(FcpBody.createFcpBody(ExceptionUtils.getStackTrace(throwable)));
            } else {
                response.addFcpBody(FcpBody.createFcpBody(I18N.text(I18NKey.SYSTEM_ERROR)));
            }
        } else {
            log.error("", throwable);
            response.setMessageType(FcpMessageType.ERROR);
            if (request.getLongHeader(FcpHeader.FcpHeaderType.TRACE_COLOR) == 1) {
                response.addFcpBody(FcpBody.createFcpBody(ExceptionUtils.getStackTrace(throwable)));
            } else {
                response.addFcpBody(FcpBody.createFcpBody(I18N.text(I18NKey.SYSTEM_ERROR)));
            }
        }

        log.debug("handle exception, response:{}", response);
    }

    @Override
    public boolean traceError(Throwable throwable) {
        if (throwable instanceof AppBusinessException
                || throwable instanceof RestProxyBusinessException
                || throwable instanceof MetadataValidateException
                || throwable instanceof InvalidFcpRequestException) {
            return false;
        }
        return true;
    }
}
