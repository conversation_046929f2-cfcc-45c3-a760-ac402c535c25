package com.facishare.paas.appframework.common.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 外部部门信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OuterDepartmentInfo {
    /**
     * 外部部门ID
     */
    private String outerDepartmentId;

    /**
     * 外部部门名称
     */
    private String outerDepartmentName;

    /**
     * 外部租户ID
     */
    private String outerTenantId;

    /**
     * 外部租户名称
     */
    private String outerTenantName;

    /**
     * 部门状态
     * 1: 正常
     * 2: 已删除
     */
    private Integer status;
} 