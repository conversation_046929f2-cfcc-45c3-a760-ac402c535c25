package com.facishare.paas.appframework.privilege.dto;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import lombok.*;

import java.util.List;
import java.util.Objects;
import java.util.Set;

public interface QueryTemporaryPrivilegeList {
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    class Arg extends BasePrivilegeArg {
        private String entityId;
        private Set<String> dataIds;
        private Set<String> owners;
        private Integer permission;
        private String scene;
        private String sourceId;
        private PageInfo pageInfo;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {
        QueryResult result;

        public void fillSceneName() {
            if (Objects.isNull(result) || CollectionUtils.empty(result.getContent())) {
                return;
            }

            result.getContent().forEach(a -> a.setSceneName(calcSceneName(a.getScene())));
        }

        private String calcSceneName(String scene) {
            switch (scene) {
                case "approval":
                    return I18N.text(I18NKey.APPROVAL_FLOW);
                case "bpm":
                    return I18N.text(I18NKey.BUSINESS_FLOW);
                case "workflow":
                    return I18N.text(I18NKey.WORK_FLOW);
                default:
                    return null;
            }
        }
    }

    @Data
    class QueryResult {
        List<TemporaryPermissionInfo> content;
        PageInfo page;
    }
}
