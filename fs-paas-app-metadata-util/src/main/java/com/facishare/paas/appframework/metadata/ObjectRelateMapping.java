package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;

import java.io.InputStream;
import java.util.Map;
import java.util.Optional;

/**
 * Created by liyiguang on 2017/11/13.
 */
public abstract class ObjectRelateMapping {

    private static Map<String, Map<String, Integer>> map;

    static {
        init();
    }

    public static Optional<Integer> getRelate(String relateObjectAPIName, String relatedObjectAPIName) {
        Map<String, Integer> relatedMap = map.get(relateObjectAPIName);
        if (relatedMap == null) {
            return Optional.empty();
        }
        Integer ret = relatedMap.get(relatedObjectAPIName);
        return Optional.ofNullable(ret);
    }

    private static void init() {
        try {
            InputStream inputStream = ObjectRelateMapping.class.getResourceAsStream("/object_relate_mapping.json");
            map = JSON.parseObject(inputStream, Map.class);
        } catch (Exception e) {
            throw new RuntimeException("ObjectRelateMapping init failed", e);
        }
    }

    public static void main(String[] args) {
        System.out.println(getRelate("AccountObj", "LeadsObj"));
        System.out.println(getRelate("LeadsObj", "AccountObj"));
    }
}
