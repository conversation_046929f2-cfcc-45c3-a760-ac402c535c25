package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.dto.FuncCodePrivilege;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试内容描述：测试DeliveryNotePrivilegeService类的方法
 */
@ExtendWith(MockitoExtension.class)
class DeliveryNotePrivilegeServiceTest {

    @Mock
    private FunctionPrivilegeService functionPrivilegeService;

    @Mock
    private FunctionPrivilegeProxy functionPrivilegeProxy;

    @InjectMocks
    private DeliveryNotePrivilegeService deliveryNotePrivilegeService;

    private String testTenantId;
    private String testUserId;

    @BeforeEach
    void setUp() {
        testTenantId = "123456";
        testUserId = "78910";
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deliveryNoteAddFuncAccess方法正常场景，成功获取权限并为发货单对象增加权限
     */
    @Test
    @DisplayName("正常场景 - deliveryNoteAddFuncAccess方法成功获取权限并增加权限")
    void testDeliveryNoteAddFuncAccess_Success() {
        // 准备测试数据
        Map<String, List<String>> resultMap = new HashMap<>();
        resultMap.put(PrivilegeConstants.SALES_ORDER_CONFIRM_DELIVERY_FUNC_CODE, Arrays.asList("role1", "role2"));
        resultMap.put(PrivilegeConstants.SALES_ORDER_CONFIRM_RECEIPT_FUNC_CODE, Arrays.asList("role3", "role4"));
        
        FuncCodePrivilege.Result result = new FuncCodePrivilege.Result();
        result.setSuccess(true);
        result.setResult(resultMap);

        // 配置Mock行为
        when(functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(any(FuncCodePrivilege.Arg.class), anyMap()))
                .thenReturn(result);

        // 执行被测试方法
        deliveryNotePrivilegeService.deliveryNoteAddFuncAccess(testTenantId, testUserId);

        // 验证Mock交互
        verify(functionPrivilegeProxy, times(1)).getHaveFuncCodesPrivilegeRoles(
                argThat(arg -> {
                    return arg.getAuthContext().getTenantId().equals(testTenantId) &&
                           arg.getAuthContext().getUserId().equals(testUserId) &&
                           arg.getAuthContext().getAppId().equals(PrivilegeConstants.APP_ID) &&
                           arg.getFuncCodes().size() == 2 &&
                           arg.getFuncCodes().contains(PrivilegeConstants.SALES_ORDER_CONFIRM_DELIVERY_FUNC_CODE) &&
                           arg.getFuncCodes().contains(PrivilegeConstants.SALES_ORDER_CONFIRM_RECEIPT_FUNC_CODE);
                }),
                anyMap()
        );

        // 验证第一种权限的调用
        verify(functionPrivilegeService, times(1)).rolesAddFuncAccess(
                argThat(user -> user.getTenantId().equals(testTenantId) && user.getUserId().equals(testUserId)),
                eq("DeliveryNoteObj"),
                eq("Add"),
                eq(Arrays.asList("role1", "role2"))
        );

        // 验证第二种权限的调用
        verify(functionPrivilegeService, times(1)).rolesAddFuncAccess(
                argThat(user -> user.getTenantId().equals(testTenantId) && user.getUserId().equals(testUserId)),
                eq("DeliveryNoteObj"),
                eq("ConfirmReceipt"),
                eq(Arrays.asList("role3", "role4"))
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deliveryNoteAddFuncAccess方法异常场景，权限查询失败
     */
    @Test
    @DisplayName("异常场景 - deliveryNoteAddFuncAccess方法权限查询失败")
    void testDeliveryNoteAddFuncAccess_QueryFailed() {
        // 准备测试数据
        FuncCodePrivilege.Result result = new FuncCodePrivilege.Result();
        result.setSuccess(false);
        result.setErrCode(100);
        result.setErrMessage("权限查询失败");

        // 配置Mock行为
        when(functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(any(FuncCodePrivilege.Arg.class), anyMap()))
                .thenReturn(result);

        // 执行被测试方法
        deliveryNotePrivilegeService.deliveryNoteAddFuncAccess(testTenantId, testUserId);

        // 验证Mock交互 - 权限调用失败应不执行后续操作
        verify(functionPrivilegeProxy, times(1)).getHaveFuncCodesPrivilegeRoles(any(FuncCodePrivilege.Arg.class), anyMap());
        verify(functionPrivilegeService, never()).rolesAddFuncAccess(any(User.class), anyString(), anyString(), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deliveryNoteAddFuncAccess方法边界场景，角色列表为空
     */
    @Test
    @DisplayName("边界场景 - deliveryNoteAddFuncAccess方法角色列表为空")
    void testDeliveryNoteAddFuncAccess_WithEmptyRoles() {
        // 准备测试数据
        Map<String, List<String>> resultMap = new HashMap<>();
        resultMap.put(PrivilegeConstants.SALES_ORDER_CONFIRM_DELIVERY_FUNC_CODE, Arrays.asList());
        resultMap.put(PrivilegeConstants.SALES_ORDER_CONFIRM_RECEIPT_FUNC_CODE, null);
        
        FuncCodePrivilege.Result result = new FuncCodePrivilege.Result();
        result.setSuccess(true);
        result.setResult(resultMap);

        // 配置Mock行为
        when(functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(any(FuncCodePrivilege.Arg.class), anyMap()))
                .thenReturn(result);

        // 执行被测试方法
        deliveryNotePrivilegeService.deliveryNoteAddFuncAccess(testTenantId, testUserId);

        // 验证Mock交互 - 空角色列表应不执行添加权限操作
        verify(functionPrivilegeProxy, times(1)).getHaveFuncCodesPrivilegeRoles(any(FuncCodePrivilege.Arg.class), anyMap());
        verify(functionPrivilegeService, never()).rolesAddFuncAccess(any(User.class), anyString(), anyString(), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deliveryNoteAddFuncAccess方法异常场景，权限分配过程中发生异常
     */
    @Test
    @DisplayName("异常场景 - deliveryNoteAddFuncAccess方法权限分配过程中发生异常")
    void testDeliveryNoteAddFuncAccess_WithException() {
        // 准备测试数据
        Map<String, List<String>> resultMap = new HashMap<>();
        resultMap.put(PrivilegeConstants.SALES_ORDER_CONFIRM_DELIVERY_FUNC_CODE, Arrays.asList("role1", "role2"));
        
        FuncCodePrivilege.Result result = new FuncCodePrivilege.Result();
        result.setSuccess(true);
        result.setResult(resultMap);

        // 配置Mock行为
        when(functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(any(FuncCodePrivilege.Arg.class), anyMap()))
                .thenReturn(result);
        doThrow(new RuntimeException("模拟异常"))
                .when(functionPrivilegeService).rolesAddFuncAccess(any(User.class), anyString(), anyString(), anyList());

        // 执行被测试方法 - 方法不应抛出异常
        assertDoesNotThrow(() -> {
            deliveryNotePrivilegeService.deliveryNoteAddFuncAccess(testTenantId, testUserId);
        });

        // 验证Mock交互
        verify(functionPrivilegeProxy, times(1)).getHaveFuncCodesPrivilegeRoles(any(FuncCodePrivilege.Arg.class), anyMap());
        verify(functionPrivilegeService, times(1)).rolesAddFuncAccess(any(User.class), anyString(), anyString(), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deliveryNoteAddFuncAccess方法边界场景，传入null参数
     */
    @Test
    @DisplayName("边界场景 - deliveryNoteAddFuncAccess方法传入null参数")
    void testDeliveryNoteAddFuncAccess_WithNullParams() {
        // 配置Mock行为 - 当传入null参数时返回null
        when(functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(any(), anyMap()))
                .thenReturn(null);

        // 执行被测试方法 - 应该抛出NullPointerException，因为代码没有处理null result的情况
        assertThrows(NullPointerException.class, () -> {
            deliveryNotePrivilegeService.deliveryNoteAddFuncAccess(null, null);
        });

        // 验证Mock交互
        verify(functionPrivilegeProxy, times(1)).getHaveFuncCodesPrivilegeRoles(any(), anyMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deliveryNoteAddFuncAccess方法正常场景，只有一种权限有角色
     */
    @Test
    @DisplayName("正常场景 - deliveryNoteAddFuncAccess方法只有一种权限有角色")
    void testDeliveryNoteAddFuncAccess_OnlyOnePrivilegeHasRoles() {
        // 准备测试数据
        Map<String, List<String>> resultMap = new HashMap<>();
        resultMap.put(PrivilegeConstants.SALES_ORDER_CONFIRM_DELIVERY_FUNC_CODE, Arrays.asList("role1", "role2"));
        resultMap.put(PrivilegeConstants.SALES_ORDER_CONFIRM_RECEIPT_FUNC_CODE, Arrays.asList());
        
        FuncCodePrivilege.Result result = new FuncCodePrivilege.Result();
        result.setSuccess(true);
        result.setResult(resultMap);

        // 配置Mock行为
        when(functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(any(FuncCodePrivilege.Arg.class), anyMap()))
                .thenReturn(result);

        // 执行被测试方法
        deliveryNotePrivilegeService.deliveryNoteAddFuncAccess(testTenantId, testUserId);

        // 验证Mock交互 - 只应该调用一次权限分配
        verify(functionPrivilegeProxy, times(1)).getHaveFuncCodesPrivilegeRoles(any(FuncCodePrivilege.Arg.class), anyMap());
        verify(functionPrivilegeService, times(1)).rolesAddFuncAccess(
                any(User.class),
                eq("DeliveryNoteObj"),
                eq("Add"),
                eq(Arrays.asList("role1", "role2"))
        );
    }
} 