package com.facishare.paas.appframework.metadata.dto;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IDataSnapshot;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ObjectDataSnapshot {
    private Map<String, Object> masterSnapshot;
    private Map<String, Map<String, Object>> detailSnapshot;
    private String biz;
    private String bizId;
    private String subBizId;

    public Map<String, Object> getDetailSnapshotById(String detailApiName, String detailDataId) {
        if (CollectionUtils.empty(detailSnapshot)) {
            return Maps.newHashMap();
        }
        Map<String, Object> changes = detailSnapshot.getOrDefault(detailApiName, Maps.newHashMap());
        List<Map<String, Object>> addList = (List<Map<String, Object>>) changes.getOrDefault(ObjectAction.CREATE.getActionCode(),
                Lists.newArrayList());
        Map<String, Map<String, Object>> updateList = (Map<String, Map<String, Object>>) changes.getOrDefault(ObjectAction.UPDATE.getActionCode(),
                Maps.newHashMap());
        for (Map<String, Object> addData : addList) {
            if (Objects.equals(detailDataId, ObjectDataExt.of(addData).getId())) {
                return addData;
            }
        }
        return updateList.getOrDefault(detailDataId, Maps.newHashMap());
    }

    public void mergeFrom(IDataSnapshot snapshot) {
        masterSnapshot.putAll(snapshot.getMasterSnapshot());
        merge(snapshot.getSlaveSnapshot(), (Map) detailSnapshot);
    }

    private void merge(Map<String, Object> fromDetailSnapshot, Map<String, Object> toDetailSnapshot) {
        if (CollectionUtils.empty(fromDetailSnapshot)) {
            return;
        }
        fromDetailSnapshot.keySet().forEach(detailApiName -> {
            if (!toDetailSnapshot.containsKey(detailApiName)) {
                toDetailSnapshot.put(detailApiName, fromDetailSnapshot.get(detailApiName));
            } else {
                Map<String, Object> changes = (Map<String, Object>) fromDetailSnapshot.getOrDefault(detailApiName, Maps.newHashMap());
                List<Map<String, Object>> addList = (List<Map<String, Object>>) changes.getOrDefault(ObjectAction.CREATE.getActionCode(),
                        Lists.newArrayList());
                Map<String, Map<String, Object>> updateList = (Map<String, Map<String, Object>>) changes.getOrDefault(ObjectAction.UPDATE.getActionCode(),
                        Maps.newHashMap());
                List<String> invalidIds = (List<String>) changes.getOrDefault(ObjectAction.INVALID.getActionCode(), Lists.newArrayList());
                List<String> deleteIds = (List<String>) changes.getOrDefault(ObjectAction.DELETE.getActionCode(), Lists.newArrayList());

                Map<String, Object> mergeChanges = (Map<String, Object>) toDetailSnapshot.get(detailApiName);
                List<Map<String, Object>> mergeAddList = (List<Map<String, Object>>) mergeChanges.computeIfAbsent(ObjectAction.CREATE.getActionCode(),
                        k -> Lists.newArrayList());
                Set<String> mergeAddIds = mergeAddList.stream().map(m -> ObjectDataExt.of(m).getId()).collect(Collectors.toSet());
                Map<String, Map<String, Object>> mergeUpdateList = (Map<String, Map<String, Object>>) mergeChanges.computeIfAbsent(ObjectAction.UPDATE.getActionCode(),
                        k -> Maps.newHashMap());
                List<String> mergeInvalidIds = (List<String>) mergeChanges.computeIfAbsent(ObjectAction.INVALID.getActionCode(), k -> Lists.newArrayList());
                List<String> mergeDeleteIds = (List<String>) mergeChanges.computeIfAbsent(ObjectAction.DELETE.getActionCode(), k -> Lists.newArrayList());

                mergeAddList.stream().map(m -> ObjectDataExt.of(m)).filter(m -> updateList.containsKey(m.getId())).forEach(m -> m.putAll(updateList.get(m.getId())));
                mergeAddList.removeIf(m -> invalidIds.contains(ObjectDataExt.of(m).getId()));
                mergeAddList.removeIf(m -> deleteIds.contains(ObjectDataExt.of(m).getId()));
                mergeAddList.addAll(addList.stream().filter(x -> !mergeAddIds.contains(ObjectDataExt.of(x).getId())).collect(Collectors.toList()));
                updateList.keySet().stream().filter(detailDataId -> !mergeAddIds.contains(detailDataId)).forEach(detailDataId ->
                        mergeUpdateList.computeIfAbsent(detailDataId, k -> Maps.newHashMap()).putAll(updateList.get(detailDataId)));
                mergeInvalidIds.addAll(invalidIds.stream().filter(x -> !mergeInvalidIds.contains(x) && !mergeAddIds.contains(x)).collect(Collectors.toList()));
                mergeDeleteIds.addAll(deleteIds.stream().filter(x -> !mergeDeleteIds.contains(x) && !mergeAddIds.contains(x)).collect(Collectors.toList()));

                if (CollectionUtils.empty(mergeAddList)) {
                    mergeChanges.remove(ObjectAction.CREATE.getActionCode());
                }
                if (CollectionUtils.empty(mergeUpdateList)) {
                    mergeChanges.remove(ObjectAction.UPDATE.getActionCode());
                }
                if (CollectionUtils.empty(mergeInvalidIds)) {
                    mergeChanges.remove(ObjectAction.INVALID.getActionCode());
                }
                if (CollectionUtils.empty(mergeDeleteIds)) {
                    mergeChanges.remove(ObjectAction.DELETE.getActionCode());
                }
            }
        });
        toDetailSnapshot.entrySet().removeIf(it -> ObjectDataExt.isValueEmpty(it.getValue()));
    }

    public void mergeIntoDetailChange(Map<String, Object> detailChange) {
        merge((Map) detailSnapshot, detailChange);
    }

    public void mergeIntoMasterData(IObjectData masterData) {
        if (CollectionUtils.empty(masterSnapshot)) {
            return;
        }
        ObjectDataExt.of(masterData).putAll(masterSnapshot);
    }

    public void mergeIntoDetailData(Map<String, List<IObjectData>> detailDataMap) {
        if (CollectionUtils.empty(detailSnapshot) || CollectionUtils.empty(detailDataMap)) {
            return;
        }
        detailDataMap.keySet().forEach(detailApiName -> {
            List<IObjectData> detailDataList = detailDataMap.get(detailApiName);

            Map<String, Object> changes = detailSnapshot.getOrDefault(detailApiName, Maps.newHashMap());
            List<Map<String, Object>> addList = (List<Map<String, Object>>) changes.getOrDefault(ObjectAction.CREATE.getActionCode(), Lists.newArrayList());
            Map<String, Map<String, Object>> updateList = (Map<String, Map<String, Object>>) changes.getOrDefault(ObjectAction.UPDATE.getActionCode(), Maps.newHashMap());
            List<String> invalidIds = (List<String>) changes.getOrDefault(ObjectAction.INVALID.getActionCode(), Lists.newArrayList());
            List<String> deleteIds = (List<String>) changes.getOrDefault(ObjectAction.DELETE.getActionCode(), Lists.newArrayList());

            detailDataList.stream().filter(x -> updateList.containsKey(x.getId())).forEach(x -> ObjectDataExt.of(x).putAll(updateList.get(x.getId())));
            detailDataList.addAll(addList.stream().map(x -> ObjectDataExt.of(x).getObjectData()).collect(Collectors.toList()));
            detailDataList.removeIf(x -> invalidIds.contains(x.getId()) || deleteIds.contains(x.getId()));
        });
    }

    public void mergeInto(IObjectData masterData, Map<String, List<IObjectData>> detailDataMap) {
        mergeIntoMasterData(masterData);
        mergeIntoDetailData(detailDataMap);
    }
}
