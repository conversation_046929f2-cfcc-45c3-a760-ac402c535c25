package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.button.ButtonActionExecutor;
import com.facishare.paas.appframework.button.CustomButtonExecutor;
import com.facishare.paas.appframework.button.CustomButtonMiscService;
import com.facishare.paas.appframework.common.service.GeoAddressService;
import com.facishare.paas.appframework.common.service.NetworkDiskService;
import com.facishare.paas.appframework.common.service.OuterOrganizationService;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.predef.facade.ChangeOrderHandlerLogicService;
import com.facishare.paas.appframework.flow.WorkFlowService;
import com.facishare.paas.appframework.function.ValidRecordTypePlugin;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.bi.BIService;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderLogicService;
import com.facishare.paas.appframework.metadata.config.ObjectConfigService;
import com.facishare.paas.appframework.metadata.dimension.DimensionLogicService;
import com.facishare.paas.appframework.metadata.domain.DomainPluginLogicService;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.fieldalign.GlobalFieldAlignService;
import com.facishare.paas.appframework.metadata.fieldextra.FieldBackgroundExtraLogicService;
import com.facishare.paas.appframework.metadata.gdpr.GdprService;
import com.facishare.paas.appframework.metadata.lookup.LookUpLogicService;
import com.facishare.paas.appframework.metadata.publicobject.PublicObjectLogicService;
import com.facishare.paas.appframework.metadata.qixin.QiXinAppLogicService;
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService;
import com.facishare.paas.appframework.metadata.richtext.RichTextService;
import com.facishare.paas.appframework.metadata.switchcache.SwitchCacheService;
import com.facishare.paas.appframework.privilege.EnterpriseRelationLogicService;
import com.facishare.paas.appframework.privilege.EnterpriseRelationServiceProxy;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.function.Supplier;

public interface InfraServiceFacade extends SwitchCacheService, DomainPluginLogicService, RedissonService,
        I18nSettingService, PentagonRelationService, OptionalFeaturesService, PrintTemplateExportService,
        ValidRecordTypePlugin, GlobalFieldAlignService, GdprService, SceneLogicService, DataSnapshotLogicService,
        DimensionLogicService, ObjectMappingService, IOcrLogicService, TagLogicService, UIEventLogicService,
        ObjectDataDraftService, BizConfService, OuterOrganizationService, EnterpriseRelationLogicService,
        ObjectConfigService, ChangeOrderHandlerLogicService, ChangeOrderLogicService, ButtonActionExecutor, CustomButtonExecutor,
        FieldRelationCalculateService, ExpressionCalculateLogicService, WorkFlowService,
        QuoteValueService, QixinGroupService, NetworkDiskService, ObjectConvertRuleService, ConvertRuleLogicService,
        RichTextService, QiXinAppLogicService, LookUpLogicService, FieldBackgroundExtraLogicService, PublicObjectLogicService,
        DataListHeaderConfigService, EnterpriseRelationServiceProxy, ValidateRuleService, FieldDataConvertService,
        CustomButtonMiscService, ImportService, LayoutRuleLogicService, GeoAddressService, BIService {

    @Transactional
    void runWithTransaction(Runnable runnable);

    @Transactional
    <T> T callWithTransaction(Supplier<T> supplier);

    /**
     * 以全局事务的方式执行任务
     *
     * @param name                 任务名称
     * @param timeout              全局事务超时时间
     * @param tenantId             企业id
     * @param rollbackExceptions   需要发起回滚的异常
     * @param noRollbackExceptions 不需要发起回滚的异常
     * @param supplier             需要执行的任务
     * @param <T>
     * @return supplier的返回值
     */
    <T> T callWithGlobalTransaction(String name,
                                    int timeout,
                                    String tenantId,
                                    List<Class<? extends Throwable>> rollbackExceptions,
                                    List<Class<? extends Throwable>> noRollbackExceptions,
                                    Supplier<T> supplier);

    SpringBeanHolder getSpringBeanHolder();

    void validateTypeForDataOwnDept(User user, IObjectDescribe objectDescribe, IObjectData objectData);

    FileStoreService getFileStoreService();
}
