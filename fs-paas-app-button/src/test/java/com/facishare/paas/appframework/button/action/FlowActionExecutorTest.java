package com.facishare.paas.appframework.button.action;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.FunctionTimeoutException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.flow.FlowCommonService;
import com.facishare.paas.appframework.flow.dto.OneFlowExecuteRequest;
import com.facishare.paas.appframework.flow.dto.OneFlowExecuteResponse;
import com.facishare.paas.appframework.metadata.IAcionType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.net.SocketTimeoutException;
import java.util.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * FlowActionExecutor单元测试类
 */
@ExtendWith(MockitoExtension.class)
class FlowActionExecutorTest {

  @Mock
  private FlowCommonService flowCommonService;

  @Mock
  private ArgumentProcessorService argumentProcessorService;

  @InjectMocks
  private FlowActionExecutor flowActionExecutor;

  private User testUser;
  private ButtonExecutor.Arg testArg;
  private ActionExecutorContext testContext;
  private IUdefAction testAction;
  private IObjectData testObjectData;
  private IObjectDescribe testDescribe;
  private IUdefButton testButton;

  @BeforeEach
  void setUp() {
    testUser = User.builder()
        .tenantId("74255")
        .userId("1000")
        .build();
    testUser.setUserName("testUser");

    testObjectData = new ObjectData();
    testObjectData.set("_id", "test123");
    testObjectData.set("name", "测试数据");

    testDescribe = mock(IObjectDescribe.class);
    testButton = mock(IUdefButton.class);
    testAction = mock(IUdefAction.class);
    testArg = mock(ButtonExecutor.Arg.class);
    testContext = mock(ActionExecutorContext.class);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getType方法返回正确的ActionExecutorType
   */
  @Test
  @DisplayName("正常场景 - getType返回CURRENT_ONE_FLOW")
  void testGetType() {
    // 执行被测试方法
    ActionExecutorType result = flowActionExecutor.getType();

    // 验证结果
    assertEquals(ActionExecutorType.CURRENT_ONE_FLOW, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试invoke方法正常场景，同步执行成功
   */
  @Test
  @DisplayName("正常场景 - invoke同步执行成功")
  void testInvoke_SyncSuccess() {
    // 准备测试数据
    List<BaseActionArg> processedArgs = Arrays.asList(
        createOneFlowArg("param1", "value1"),
        createOneFlowArg("param2", "value2")
    );

    OneFlowExecuteResponse executeResponse = new OneFlowExecuteResponse();
    executeResponse.setWorkflowInstanceId("instance123");

    // 配置Mock行为
    when(testAction.getActionParamter()).thenReturn(
        "{\n" +
        "  \"oneflow_api_name\": \"testFlow\",\n" +
        "  \"oneflow_args\": [\n" +
        "    {\"name\": \"param1\", \"value\": \"value1\"}\n" +
        "  ],\n" +
        "  \"async\": false\n" +
        "}");
    when(testArg.getObjectData()).thenReturn(testObjectData);
    when(testArg.getArgs()).thenReturn(new HashMap<>());
    when(testContext.getAction()).thenReturn(testAction);
    when(testContext.getUser()).thenReturn(testUser);
    when(testContext.getDescribe()).thenReturn(testDescribe);
    when(testContext.getButton()).thenReturn(testButton);

    try (MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class)) {
      mockedCollectionUtils.when(() -> CollectionUtils.notEmpty(processedArgs)).thenReturn(true);

      when(argumentProcessorService.processOneFlowArguments(any(), any(), any(), any(), any(), any()))
          .thenReturn(processedArgs);
      when(flowCommonService.executeFlow(eq(testUser), any(OneFlowExecuteRequest.class)))
          .thenReturn(executeResponse);

      // 执行被测试方法
      ButtonExecutor.Result result = flowActionExecutor.invoke(testArg, testContext);

      // 验证结果
      assertNotNull(result);
      assertTrue(result.isHasReturnValue());
      assertEquals(IAcionType.ONE_FLOW, result.getReturnType());
      assertEquals("instance123", result.getReturnValue());

      // 验证Mock交互
      verify(argumentProcessorService).processOneFlowArguments(any(), any(), any(), any(), any(), any());
      verify(flowCommonService).executeFlow(eq(testUser), any(OneFlowExecuteRequest.class));
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试invoke方法正常场景，异步执行成功
   */
  @Test
  @DisplayName("正常场景 - invoke异步执行成功")
  void testInvoke_AsyncSuccess() {
    // 准备测试数据
    List<BaseActionArg> processedArgs = Collections.emptyList();
    OneFlowExecuteResponse executeResponse = new OneFlowExecuteResponse();
    executeResponse.setWorkflowInstanceId("instance456");

    // 配置Mock行为
    when(testAction.getActionParamter()).thenReturn(
        "{\n" +
        "  \"oneflow_api_name\": \"testFlow\",\n" +
        "  \"oneflow_args\": [],\n" +
        "  \"async\": true\n" +
        "}");
    when(testArg.getObjectData()).thenReturn(testObjectData);
    when(testArg.getArgs()).thenReturn(new HashMap<>());
    when(testContext.getAction()).thenReturn(testAction);
    when(testContext.getUser()).thenReturn(testUser);
    when(testContext.getDescribe()).thenReturn(testDescribe);
    when(testContext.getButton()).thenReturn(testButton);

    try (MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class)) {
      mockedCollectionUtils.when(() -> CollectionUtils.notEmpty(processedArgs)).thenReturn(false);

      when(argumentProcessorService.processOneFlowArguments(any(), any(), any(), any(), any(), any()))
          .thenReturn(processedArgs);
      when(flowCommonService.executeFlow(eq(testUser), any(OneFlowExecuteRequest.class)))
          .thenReturn(executeResponse);

      // 执行被测试方法
      ButtonExecutor.Result result = flowActionExecutor.invoke(testArg, testContext);

      // 验证结果
      assertNotNull(result);
      assertTrue(result.isHasReturnValue());
      assertEquals(IAcionType.ONE_FLOW, result.getReturnType());
      assertEquals("instance456", result.getReturnValue());

      // 验证Mock交互
      verify(argumentProcessorService).processOneFlowArguments(any(), any(), any(), any(), any(), any());
      verify(flowCommonService).executeFlow(eq(testUser), any(OneFlowExecuteRequest.class));
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试invoke方法异常场景，ValidateException直接抛出
   */
  @Test
  @DisplayName("异常场景 - invoke抛出ValidateException")
  void testInvokeThrowsValidateException() {
    // 准备测试数据
    ValidateException validateException = new ValidateException("验证失败");

    // 配置Mock行为
    when(testAction.getActionParamter()).thenReturn(
        "{\n" +
        "  \"oneflow_api_name\": \"testFlow\",\n" +
        "  \"oneflow_args\": [],\n" +
        "  \"async\": false\n" +
        "}");
    when(testArg.getObjectData()).thenReturn(testObjectData);
    when(testArg.getArgs()).thenReturn(new HashMap<>());
    when(testContext.getAction()).thenReturn(testAction);
    when(testContext.getUser()).thenReturn(testUser);
    when(testContext.getDescribe()).thenReturn(testDescribe);
    when(testContext.getButton()).thenReturn(testButton);
    when(argumentProcessorService.processOneFlowArguments(any(), any(), any(), any(), any(), any()))
        .thenThrow(validateException);

    // 执行并验证异常
    ValidateException exception = assertThrows(ValidateException.class, () -> {
      flowActionExecutor.invoke(testArg, testContext);
    });

    // 验证异常信息
    assertEquals("验证失败", exception.getMessage());

    // 验证Mock交互
    verify(argumentProcessorService).processOneFlowArguments(any(), any(), any(), any(), any(), any());
    verify(flowCommonService, never()).executeFlow(any(), any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试invoke方法异常场景，RestProxyBusinessException转换为ValidateException
   */
  @Test
  @DisplayName("异常场景 - invoke处理RestProxyBusinessException")
  void testInvokeThrowsRestProxyBusinessException() {
    // 准备测试数据
    RestProxyBusinessException businessException = new RestProxyBusinessException(1001, "业务异常");

    // 配置Mock行为
    when(testAction.getActionParamter()).thenReturn(
        "{\n" +
        "  \"oneflow_api_name\": \"testFlow\",\n" +
        "  \"oneflow_args\": [],\n" +
        "  \"async\": false\n" +
        "}");
    when(testArg.getObjectData()).thenReturn(testObjectData);
    when(testArg.getArgs()).thenReturn(new HashMap<>());
    when(testContext.getAction()).thenReturn(testAction);
    when(testContext.getUser()).thenReturn(testUser);
    when(testContext.getDescribe()).thenReturn(testDescribe);
    when(testContext.getButton()).thenReturn(testButton);
    when(argumentProcessorService.processOneFlowArguments(any(), any(), any(), any(), any(), any()))
        .thenThrow(businessException);

    // 执行并验证异常
    ValidateException exception = assertThrows(ValidateException.class, () -> {
      flowActionExecutor.invoke(testArg, testContext);
    });

    // 验证异常信息
    assertEquals("业务异常", exception.getMessage());

    // 验证Mock交互
    verify(argumentProcessorService).processOneFlowArguments(any(), any(), any(), any(), any(), any());
    verify(flowCommonService, never()).executeFlow(any(), any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试invoke方法异常场景，SocketTimeoutException转换为超时异常
   */
  @Test
  @DisplayName("异常场景 - invoke处理SocketTimeoutException")
  void testInvokeThrowsSocketTimeoutException() {
    // 准备测试数据
    RuntimeException timeoutException = new RuntimeException(new SocketTimeoutException("连接超时"));

    // 配置Mock行为
    when(testAction.getActionParamter()).thenReturn(
        "{\n" +
        "  \"oneflow_api_name\": \"testFlow\",\n" +
        "  \"oneflow_args\": [],\n" +
        "  \"async\": false\n" +
        "}");
    when(testArg.getObjectData()).thenReturn(testObjectData);
    when(testArg.getArgs()).thenReturn(new HashMap<>());
    when(testContext.getAction()).thenReturn(testAction);
    when(testContext.getUser()).thenReturn(testUser);
    when(testContext.getDescribe()).thenReturn(testDescribe);
    when(testContext.getButton()).thenReturn(testButton);

    try (MockedStatic<ExceptionUtils> mockedExceptionUtils = mockStatic(ExceptionUtils.class);
         MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {

      mockedExceptionUtils.when(() -> ExceptionUtils.getRootCause(timeoutException))
          .thenReturn(new SocketTimeoutException("连接超时"));
      mockedI18N.when(() -> I18N.text(I18NKey.ONE_FLOW_TIMEOUT)).thenReturn("流程执行超时");

      when(argumentProcessorService.processOneFlowArguments(any(), any(), any(), any(), any(), any()))
          .thenThrow(timeoutException);

      // 执行并验证异常
      ValidateException exception = assertThrows(ValidateException.class, () -> {
        flowActionExecutor.invoke(testArg, testContext);
      });

      // 验证异常信息
      assertTrue(exception.getMessage().contains("testFlow"));
      assertTrue(exception.getMessage().contains("流程执行超时"));

      // 验证Mock交互
      verify(argumentProcessorService).processOneFlowArguments(any(), any(), any(), any(), any(), any());
      verify(flowCommonService, never()).executeFlow(any(), any());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试invoke方法异常场景，FunctionTimeoutException转换为超时异常
   */
  @Test
  @DisplayName("异常场景 - invoke处理FunctionTimeoutException")
  void testInvokeThrowsFunctionTimeoutException() {
    // 准备测试数据
    RuntimeException timeoutException = new RuntimeException(new FunctionTimeoutException("函数执行超时"));

    // 配置Mock行为
    when(testAction.getActionParamter()).thenReturn(
        "{\n" +
        "  \"oneflow_api_name\": \"testFlow\",\n" +
        "  \"oneflow_args\": [],\n" +
        "  \"async\": false\n" +
        "}");
    when(testArg.getObjectData()).thenReturn(testObjectData);
    when(testArg.getArgs()).thenReturn(new HashMap<>());
    when(testContext.getAction()).thenReturn(testAction);
    when(testContext.getUser()).thenReturn(testUser);
    when(testContext.getDescribe()).thenReturn(testDescribe);
    when(testContext.getButton()).thenReturn(testButton);

    try (MockedStatic<ExceptionUtils> mockedExceptionUtils = mockStatic(ExceptionUtils.class);
         MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {

      mockedExceptionUtils.when(() -> ExceptionUtils.getRootCause(timeoutException))
          .thenReturn(new FunctionTimeoutException("函数执行超时"));
      mockedI18N.when(() -> I18N.text(I18NKey.ONE_FLOW_TIMEOUT)).thenReturn("流程执行超时");

      when(argumentProcessorService.processOneFlowArguments(any(), any(), any(), any(), any(), any()))
          .thenThrow(timeoutException);

      // 执行并验证异常
      ValidateException exception = assertThrows(ValidateException.class, () -> {
        flowActionExecutor.invoke(testArg, testContext);
      });

      // 验证异常信息
      assertTrue(exception.getMessage().contains("testFlow"));
      assertTrue(exception.getMessage().contains("流程执行超时"));

      // 验证Mock交互
      verify(argumentProcessorService).processOneFlowArguments(any(), any(), any(), any(), any(), any());
      verify(flowCommonService, never()).executeFlow(any(), any());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试invoke方法异常场景，其他RuntimeException转换为通用失败异常
   */
  @Test
  @DisplayName("异常场景 - invoke处理其他RuntimeException")
  void testInvokeThrowsOtherRuntimeException() {
    // 准备测试数据
    RuntimeException otherException = new RuntimeException("其他异常");

    // 配置Mock行为
    when(testAction.getActionParamter()).thenReturn(
        "{\n" +
        "  \"oneflow_api_name\": \"testFlow\",\n" +
        "  \"oneflow_args\": [],\n" +
        "  \"async\": false\n" +
        "}");
    when(testArg.getObjectData()).thenReturn(testObjectData);
    when(testArg.getArgs()).thenReturn(new HashMap<>());
    when(testContext.getAction()).thenReturn(testAction);
    when(testContext.getUser()).thenReturn(testUser);
    when(testContext.getDescribe()).thenReturn(testDescribe);
    when(testContext.getButton()).thenReturn(testButton);

    try (MockedStatic<ExceptionUtils> mockedExceptionUtils = mockStatic(ExceptionUtils.class);
         MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {

      mockedExceptionUtils.when(() -> ExceptionUtils.getRootCause(otherException))
          .thenReturn(new RuntimeException("其他异常"));
      mockedI18N.when(() -> I18N.text(I18NKey.ONE_FLOW_FAIL)).thenReturn("OneFlow执行失败");

      when(argumentProcessorService.processOneFlowArguments(any(), any(), any(), any(), any(), any()))
          .thenThrow(otherException);

      // 执行并验证异常
      ValidateException exception = assertThrows(ValidateException.class, () -> {
        flowActionExecutor.invoke(testArg, testContext);
      });

      // 验证异常信息
      assertTrue(exception.getMessage().contains("OneFlow执行失败"));

      // 验证Mock交互
      verify(argumentProcessorService).processOneFlowArguments(any(), any(), any(), any(), any(), any());
      verify(flowCommonService, never()).executeFlow(any(), any());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试FlowActionParameter.fromJson方法正常场景
   */
  @Test
  @DisplayName("正常场景 - FlowActionParameter.fromJson解析成功")
  void testFlowActionParameterFromJson_Success() {
    // 准备测试数据
    String jsonStr = "{\n" +
        "  \"oneflow_api_name\": \"testFlow\",\n" +
        "  \"oneflow_args\": [\n" +
        "    {\"name\": \"param1\", \"value\": \"value1\"}\n" +
        "  ],\n" +
        "  \"async\": true\n" +
        "}";

    // 配置Mock行为
    try (MockedStatic<JSON> mockedJSON = mockStatic(JSON.class)) {
      FlowActionExecutor.FlowActionParameter expectedParameter = new FlowActionExecutor.FlowActionParameter();
      expectedParameter.setFlowApiName("testFlow");
      expectedParameter.setFlowArgList(Arrays.asList(createOneFlowArg("param1", "value1")));
      expectedParameter.setAsync(true);

      mockedJSON.when(() -> JSON.parseObject(jsonStr, FlowActionExecutor.FlowActionParameter.class))
          .thenReturn(expectedParameter);

      // 执行被测试方法
      FlowActionExecutor.FlowActionParameter result = FlowActionExecutor.FlowActionParameter.fromJson(jsonStr);

      // 验证结果
      assertNotNull(result);
      assertEquals("testFlow", result.getFlowApiName());
      assertTrue(result.isAsync());
      assertNotNull(result.getFlowArgList());
      assertEquals(1, result.getFlowArgList().size());

      // 验证Mock交互
      mockedJSON.verify(() -> JSON.parseObject(jsonStr, FlowActionExecutor.FlowActionParameter.class));
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试FlowActionParameter.fromJson方法空参数列表场景
   */
  @Test
  @DisplayName("边界场景 - FlowActionParameter.fromJson处理空参数列表")
  void testFlowActionParameterFromJson_NullArgList() {
    // 准备测试数据
    String jsonStr = "{\n" +
        "  \"oneflow_api_name\": \"testFlow\",\n" +
        "  \"async\": false\n" +
        "}";

    // 配置Mock行为
    try (MockedStatic<JSON> mockedJSON = mockStatic(JSON.class)) {
      FlowActionExecutor.FlowActionParameter expectedParameter = new FlowActionExecutor.FlowActionParameter();
      expectedParameter.setFlowApiName("testFlow");
      expectedParameter.setFlowArgList(null);
      expectedParameter.setAsync(false);

      mockedJSON.when(() -> JSON.parseObject(jsonStr, FlowActionExecutor.FlowActionParameter.class))
          .thenReturn(expectedParameter);

      // 执行被测试方法
      FlowActionExecutor.FlowActionParameter result = FlowActionExecutor.FlowActionParameter.fromJson(jsonStr);

      // 验证结果
      assertNotNull(result);
      assertEquals("testFlow", result.getFlowApiName());
      assertFalse(result.isAsync());
      assertNotNull(result.getFlowArgList());
      assertTrue(result.getFlowArgList().isEmpty());

      // 验证Mock交互
      mockedJSON.verify(() -> JSON.parseObject(jsonStr, FlowActionExecutor.FlowActionParameter.class));
    }
  }

  /**
   * 提供超时异常测试的测试数据
   */
  private static Stream<Arguments> provideTimeoutExceptions() {
    return Stream.of(
        Arguments.of(new SocketTimeoutException("Socket timeout"), "Socket timeout"),
        Arguments.of(new FunctionTimeoutException("Function timeout"), "Function timeout")
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：参数化测试超时异常处理
   */
  @ParameterizedTest
  @MethodSource("provideTimeoutExceptions")
  @DisplayName("参数化测试 - 超时异常处理")
  void testInvokeTimeoutExceptions(Throwable timeoutCause, String expectedMessage) {
    // 准备测试数据
    RuntimeException timeoutException = new RuntimeException(timeoutCause);

    // 配置Mock行为
    when(testAction.getActionParamter()).thenReturn(
        "{\n" +
        "  \"oneflow_api_name\": \"testFlow\",\n" +
        "  \"oneflow_args\": [],\n" +
        "  \"async\": false\n" +
        "}");
    when(testArg.getObjectData()).thenReturn(testObjectData);
    when(testArg.getArgs()).thenReturn(new HashMap<>());
    when(testContext.getAction()).thenReturn(testAction);
    when(testContext.getUser()).thenReturn(testUser);
    when(testContext.getDescribe()).thenReturn(testDescribe);
    when(testContext.getButton()).thenReturn(testButton);

    try (MockedStatic<ExceptionUtils> mockedExceptionUtils = mockStatic(ExceptionUtils.class);
         MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {

      mockedExceptionUtils.when(() -> ExceptionUtils.getRootCause(timeoutException))
          .thenReturn(timeoutCause);
      mockedI18N.when(() -> I18N.text(I18NKey.ONE_FLOW_TIMEOUT)).thenReturn("流程执行超时");

      when(argumentProcessorService.processOneFlowArguments(any(), any(), any(), any(), any(), any()))
          .thenThrow(timeoutException);

      // 执行并验证异常
      ValidateException exception = assertThrows(ValidateException.class, () -> {
        flowActionExecutor.invoke(testArg, testContext);
      });

      // 验证异常信息
      assertTrue(exception.getMessage().contains("testFlow"));
      assertTrue(exception.getMessage().contains("流程执行超时"));

      // 验证Mock交互
      verify(argumentProcessorService).processOneFlowArguments(any(), any(), any(), any(), any(), any());
      verify(flowCommonService, never()).executeFlow(any(), any());
    }
  }

  /**
   * 创建OneFlowArg测试对象的辅助方法
   */
  private OneFlowArg createOneFlowArg(String name, Object value) {
    OneFlowArg arg = new OneFlowArg();
    arg.setName(name);
    arg.setRealValue(value);
    return arg;
  }
}
