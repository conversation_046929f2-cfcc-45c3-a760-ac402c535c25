package com.facishare.paas.appframework.core.predef;

import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObject;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;

/**
 * 内部预定义对象枚举类
 * 用于定义和管理系统内部的预定义对象
 */
public enum InternalPreDefineObject implements PreDefineObject {

    // 异步任务监控对象
    AsyncTaskMonitorObj("AsyncTaskMonitorObj");

    private String apiName;
    InternalPreDefineObject(String apiName) {
        this.apiName = apiName;
    }

    public static void init() {
        for (InternalPreDefineObject object : InternalPreDefineObject.values()) {
            PreDefineObjectRegistry.register(object);
        }
    }

    private static final String PACKAGE_NAME = InternalPreDefineObject.class.getPackage().getName();

    @Override
    public String getApiName() {
        return apiName;
    }

    @Override
    public String getPackageName() {
        return PACKAGE_NAME;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String actionCode) {
        String className = PACKAGE_NAME + ".action." + this.apiName + actionCode + "Action";
        return new ActionClassInfo(className);
    }

    @Override
    public ControllerClassInfo getControllerClassInfo(String methodName) {
        String className = PACKAGE_NAME + ".controller." + this.apiName + methodName + "Controller";
        return new ControllerClassInfo(className);
    }
}
