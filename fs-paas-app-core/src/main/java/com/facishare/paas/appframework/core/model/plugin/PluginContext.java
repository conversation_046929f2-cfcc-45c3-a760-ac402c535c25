package com.facishare.paas.appframework.core.model.plugin;

import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.RequestContext;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.Delegate;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/6/28
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class PluginContext {
    @Delegate
    private final RequestContext requestContext;
    private final String objectApiName;
    private final String moduleCode;
    @Setter
    private String moduleType;

    public static PluginContext fromActionContext(ActionContext context) {
        return new PluginContext(context.getRequestContext(), context.getObjectApiName(), context.getActionCode());
    }

    public static PluginContext fromActionContext(ActionContext context, String describeApiName) {
        return new PluginContext(context.getRequestContext(), describeApiName, context.getActionCode());
    }

    public static PluginContext fromControllerContext(ControllerContext context) {
        return new PluginContext(context.getRequestContext(), context.getObjectApiName(), context.getMethodName());
    }


}
