package com.facishare.paas.appframework.log.dto;

import lombok.Data;

import java.util.Date;

/**
 * 自定义函数日志
 * Created by fengjy in 2019/1/9 11:11
 */
@Data
public class FunctionLogInfo {
    private String id;
    private String tenantId;
    private String functionApiName;
    private String parameters;
    private String returnType;
    private Date operationTime;
    private String FunctionApiName;
    private String exception;
    private String returnValue;
    private Long durationTime;
    private String traceId;
    private String triggerType;
    private String name;
    private boolean success;
    private String objectId;
    private String module;
    private String bindingApiName;
    private String bindingObjName;
    private long createTime;
}
