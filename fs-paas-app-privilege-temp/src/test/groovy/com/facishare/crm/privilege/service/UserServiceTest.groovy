package com.facishare.crm.privilege.service

import com.facishare.crm.privilege.controller.arg.CopyUserRoleToUserArg
import com.facishare.crm.privilege.util.Constant
import com.facishare.crm.privilege.util.GetRoleUtils
import com.facishare.crm.valueobject.SessionContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Shared
import spock.lang.Specification

/**
 * Created by luxin on 2017/5/8.
 */
//@ContextConfiguration(locations = "classpath:privilege-config.xml")
class UserServiceTest extends Specification {


  @Autowired
  UserService userService

  @Shared
  def authContext = new SessionContext()

  @Shared
  def user_id = "112232"

  @Shared
  def crmAdminCode = GetRoleUtils.getAdminRoleCode()

  def setup() {
    authContext.setEa(2 + "")
    authContext.setEId(2L)
    authContext.setUserId(1000)
    authContext.setUserAccount("E.2.1000")
    authContext.setDeviceID("6770f0c4-ac8a-14ed-2e28-5158f2bb993b")
    authContext.setClientVersion("Firefox")
    authContext.setTraceID("215b581f97e304722e8042a81555d348")
    authContext.setPostID("")
  }








  def "BatchAddUsersToRoles"() {

  }

  def "UpdateUserRole"() {

  }

  def "CrmUserList"() {

  }



  def "RoleUsers"() {

  }


  def "CopyUserRoleToUsers"() {


    expect:
    userService.copyUserRoleToUsers(arg, authContext).getCode() == resultCode
    where:
    arg                                                             || resultCode
    new CopyUserRoleToUserArg(userIdOld: "110", userIds: "113,114",majorRoleCode:"00000000000000000000000000000015") || 0



  }



  def "RemoveCrmUser"() {

  }





  def "UpdateUsersMajorRole"() {

  }

  def "UpdateUserMajorRoleRespectively"() {

  }

  def "CheckUserDisconnectRole"() {

  }
}
