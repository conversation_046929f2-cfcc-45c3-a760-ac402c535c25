package com.facishare.paas.appframework.core.predef.facade;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.facade.dto.CreateChangeOrder;
import com.facishare.paas.appframework.core.predef.facade.dto.DiffDetailData;
import com.facishare.paas.appframework.core.predef.facade.dto.EffectiveAndUpdateData;
import com.facishare.paas.appframework.core.predef.facade.dto.MergeChangeOrderData;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.appframework.metadata.dto.UpdateMasterAndDetailData;
import com.facishare.paas.appframework.metadata.repository.model.MtChangeOrderRule;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/4/6
 */
public interface ChangeOrderHandlerLogicService {

    CreateChangeOrder.Result doCreateChangeOrder(User user, CreateChangeOrder.Arg arg);

    CreateChangeOrder.Result doModifyChangeOrder(User user, CreateChangeOrder.Arg arg);

    void saveChangeOrderOriginalData(User user, SaveMasterAndDetailData.Arg saveArg, IObjectData objectData, Map<String, List<IObjectData>> detailData);

    void modifyChangeOrderOriginalData(User user, UpdateMasterAndDetailData.Arg updateArg, IObjectData objectData, Map<String, List<IObjectData>> detailData);

    void validateChangeRuleWithChangeData(User user, IObjectDescribe changeDescribe, IObjectData changeObjectData, MtChangeOrderRule.CalibrationType type);

    List<MtChangeOrderRule> findByDescribeApiName(User user, String describeApiName);

    List<IObjectData> findAndMergeObjectDataWithOriginalData(User user, IObjectDescribe describe, List<IObjectData> dataList);

    EffectiveAndUpdateData.Result effectiveAndUpdateData(User user, EffectiveAndUpdateData.Arg arg);

    /**
     * 校验变更状态
     * @param user 用户
     * @param describe 对象描述
     * @param objectData 对象数据
     */
    void validateByChangeStatus(User user, IObjectDescribe describe, IObjectData objectData);

    /**
     * 校验变更状态（支持传入changeDataId和是否需要审批流校验）
     * @param user 用户
     * @param describe 对象描述
     * @param objectData 对象数据
     * @param changeDataId 变更单数据ID（可选）
     * @param needApprovalFlowCheck 是否需要审批流校验
     */
    void validateByChangeStatus(User user, IObjectDescribe describe, IObjectData objectData, String changeDataId, boolean needApprovalFlowCheck);

    void updateChangeOrderStatusWithFlowReject(User user, IObjectDescribe describe, IObjectData objectData);

    void updateChangeOrderStatusWithFlowPass(User user, IObjectDescribe describe, IObjectData objectData);

    DiffDetailData.Result diffDetailData(User user, DiffDetailData.Arg arg);

    MergeChangeOrderData.Result mergeChangeOrderData(User user, MergeChangeOrderData.Arg arg);
}
