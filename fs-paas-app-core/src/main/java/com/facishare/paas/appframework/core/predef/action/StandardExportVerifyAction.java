package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ImportConfig;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.util.AppIdMapping.DISPATCH_ORDERS;

/**
 * <AUTHOR>
 * @date 2019-06-10 16:53
 */
public class StandardExportVerifyAction extends BaseExportVerifyAction {


    // ---------------------------------------- Constants

    /**
     * 导出数量超限, 默认10万条。
     */
    private static final int CODE_EXCEED_COUNT = 10;
    /**
     * 系统错误。
     */
    private static final int CODE_SYSTEM_ERROR = -1;
    /**
     * 默认最大允许导出数量。
     */
    protected final int exportRowsThrottle;
    protected final int exportRowsThrottleVip;
    protected static int exportFileAttachmentThrottle;
    protected static int exportFileAttachmentThrottleVip;
    protected static int exportMDThrottle;

    public StandardExportVerifyAction() {
        exportRowsThrottle = ImportConfig.getExportRowsThrottle();
        exportRowsThrottleVip = ImportConfig.getExportRowsThrottleVip();
        exportFileAttachmentThrottle = ImportConfig.getExportFileAttachmentThrottle();
        exportFileAttachmentThrottleVip = ImportConfig.getExportFileAttachmentThrottleVip();
        exportMDThrottle = ImportConfig.getExportMDThrottle();
    }

    @Override
    protected void doFunPrivilegeCheck() {
        super.doFunPrivilegeCheck();
        if (isExportMasterFileAttachment()) {
            serviceFacade.doFunPrivilegeCheck(actionContext.getUser(), actionContext.getObjectApiName(), StandardAction.ExportFileAttachment.getFunPrivilegeCodes());
        }
        if (isExportDetailFileAttachment()) {
            List<String> detailApiNames = arg.getDetailArg().getDetailInfo().stream()
                    .map(StandardExportAction.DetailInfo::getApiName).collect(Collectors.toList());
            if (CollectionUtils.empty(detailApiNames)) {
                return;
            }
            List<String> funcPrivilegeCodes = StandardAction.ExportFileAttachment.getFunPrivilegeCodes();
            Map<String, Map<String, Boolean>> batchFunPrivilegeCheck = serviceFacade.batchFunPrivilegeCheck(actionContext.getUser(), detailApiNames, funcPrivilegeCodes);
            log.warn("No operation function permissions,user:{},objectApiName:{},actionCodes:{}", actionContext.getUser(), detailApiNames, funcPrivilegeCodes);
            if (CollectionUtils.empty(batchFunPrivilegeCheck)) {
                String displayName = CollectionUtils.nullToEmpty(detailDescribeMap).values().stream()
                        .map(IObjectDescribe::getDisplayName).collect(Collectors.joining(","));
                throw new PermissionError(I18N.text(I18NKey.OBJECT_FUNCTION_PERMISSIONS, displayName,
                        funcPrivilegeCodes.stream().map(actionCode -> I18N.text(ObjectAction.of(actionCode).getI18NKey())).collect(Collectors.joining(","))));
            }
            for (String detailApiName : detailApiNames) {
                Map<String, Boolean> funPrivilegeCheck = batchFunPrivilegeCheck.get(detailApiName);
                IObjectDescribe describe = detailDescribeMap.get(detailApiName);
                if (CollectionUtils.empty(funPrivilegeCheck)) {
                    String displayName = Objects.isNull(describe) ? "" : describe.getDisplayName();
                    throw new PermissionError(I18N.text(I18NKey.OBJECT_FUNCTION_PERMISSIONS, displayName,
                            funcPrivilegeCodes.stream().map(actionCode -> I18N.text(ObjectAction.of(actionCode).getI18NKey())).collect(Collectors.joining(","))));
                }
                for (String actionCode : funcPrivilegeCodes) {
                    Boolean hasFunction = funPrivilegeCheck.get(actionCode);
                    if (BooleanUtils.isNotTrue(hasFunction)) {
                        String displayName = Objects.isNull(describe) ? "" : describe.getDisplayName();
                        throw new PermissionError(I18N.text(I18NKey.OBJECT_FUNCTION_PERMISSIONS, displayName,
                                I18N.text(ObjectAction.of(actionCode).getI18NKey())));
                    }
                }
            }
        }
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        // 派工单下游导出暂不支持功能权限
        if (DISPATCH_ORDERS.equals(AppIdMapping.getNamedAppId(actionContext.getAppId())) && actionContext.getUser().isOutUser()) {
            return null;
        }
        return StandardAction.Export.getFunPrivilegeCodes();
    }

    protected int getExportRowsThrottle() {
        if (isMdExportLimit() && isExportDetailObjects()) {
            return exportMDThrottle;
        }
        if (isExportFileAttachment()) {
            return getExportFileAttachmentThrottle();
        }
        if (isExportVip()) {
            return exportRowsThrottleVip;
        }
        return exportRowsThrottle;
    }

    private int getExportFileAttachmentThrottle() {
        if (isExportVip()) {
            return exportFileAttachmentThrottleVip;
        }
        return exportFileAttachmentThrottle;
    }

    public boolean isExportFileAttachment() {
        return isExportMasterFileAttachment() || isExportDetailFileAttachment();
    }

    public boolean isExportMasterFileAttachment() {
        return CollectionUtils.notEmpty(arg.getFileFields());
    }

    public boolean isExportDetailFileAttachment() {
        if (Objects.nonNull(arg.getDetailArg()) && CollectionUtils.notEmpty(arg.getDetailArg().getDetailInfo())) {
            return arg.getDetailArg().getDetailInfo().stream().anyMatch(x -> CollectionUtils.notEmpty(x.getFileFields()));
        }
        return false;
    }

    // ---------------------------------------- Nested Classes.

    @Data
    public static class Arg {

        @JsonProperty("search_template_id")
        @SerializedName("search_template_id")
        String searchTemplateId;

        @JsonProperty("search_query_info")
        @SerializedName("search_query_info")
        String searchQueryInfo;

        @JsonProperty("ignore_scene_filter")
        @SerializedName("ignore_scene_filter")
        boolean isIgnoreSceneFilter;

        @JsonProperty("dataIdList")
        @SerializedName("dataIdList")
        List<String> dataIdList;

        @JsonProperty("search_template_api_name")
        @SerializedName("search_template_api_name")
        String searchTemplateApiName;

        @JsonProperty("ignore_scene_record_type")
        @SerializedName("ignore_scene_record_type")
        boolean isIgnoreSceneRecordType;

        @JsonProperty("search_template_type")
        @SerializedName("search_template_type")
        String searchTemplateType;

        @JsonProperty("other_name_list")
        @SerializedName("other_name_list")
        List<String> otherNameList;

        @JsonProperty("recordType_apiName")
        @SerializedName("recordType_apiName")
        String recordType;

        @JsonProperty("detail_arg")
        @SerializedName("detail_arg")
        DetailArg detailArg;

        @JsonProperty(value = "print_template_id")
        @SerializedName("print_template_id")
        private String printTemplateId;

        @JSONField(name = "field_api_name_list")
        @JsonProperty(value = "field_api_name_list")
        @SerializedName(value = "field_api_name_list")
        List<String> fieldApiNames;

        @JsonProperty("export_fileAttachment")
        @SerializedName("export_fileAttachment")
        boolean exportFileAttachment;

        @JsonProperty("file_fields")
        @SerializedName("file_fields")
        List<String> fileFields;
    }

    @Data
    static class DetailArg {

        @JsonProperty("detail_object_api_names")
        @SerializedName("detail_object_api_names")
        List<String> detailObjectApiNames;

        @JsonProperty("include_id")
        @SerializedName("include_id")
        boolean includeId;

        @JsonProperty("export_fileAttachment")
        @SerializedName("export_fileAttachment")
        boolean exportFileAttachment;


        @JsonProperty("detail_info")
        @SerializedName("detail_info")
        List<StandardExportAction.DetailInfo> detailInfo;
    }


    @Data
    public static class DetailInfo {
        String apiName;
        @JsonProperty("file_fields")
        @SerializedName("file_fields")
        List<String> fileFields;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        boolean success;
    }

}

