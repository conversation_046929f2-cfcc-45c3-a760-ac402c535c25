package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/03/16
 */
public interface PentagonRelationService {

    void handleChainCycle(User user, ISearchTemplateQuery query, IObjectData masterObjectData, IObjectDescribe masterDescribe);

    void handleChainCycle(User user, Query query, IObjectData masterObjectData, IObjectDescribe masterDescribe);
}
