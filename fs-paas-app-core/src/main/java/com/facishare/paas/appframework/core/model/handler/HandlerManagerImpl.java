package com.facishare.paas.appframework.core.model.handler;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.support.AopUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * Handler管理器，用于注册和查找Handler
 * <p/>
 * Created by zhouwr on 2023/1/6.
 */
@Slf4j
@Component
public class HandlerManagerImpl implements HandlerManager {

    private final Map<String, Handler> handlerMap = Maps.newHashMap();

    @Override
    public void register(Handler handler) {
        Class<?> clazz = AopUtils.getTargetClass(handler);
        HandlerProvider provider = clazz.getAnnotation(HandlerProvider.class);
        if (provider == null || Strings.isNullOrEmpty(provider.name())) {
            log.warn("ignore handler which has no provider:{}", clazz);
            return;
        }
        String name = provider.name();
        if (handlerMap.containsKey(name)) {
            throw new ValidateException("Handler:" + name + " already exists!");
        }
        handlerMap.put(name, handler);
        log.info("register handler:{}-{}", name, clazz);
    }

    @Override
    public <T extends Handler> T getHandler(SimpleHandlerDescribe handlerDescribe) {
        //优先返回本地的Handler
        T handler = (T) handlerMap.get(handlerDescribe.getApiName());
        if (Objects.nonNull(handler)) {
            return handler;
        }
        //如果restApiUrl不为空，则返回远程Handler
        if (!Strings.isNullOrEmpty(handlerDescribe.getRestApiUrl())) {
            handler = (T) handlerMap.get(getRemoteHandlerApiName(handlerDescribe));
            if (Objects.isNull(handler)) {
                handler = (T) handlerMap.get(getDefaultRemoteHandlerApiName());
            }
            return handler;
        }
        //如果aplApiName不为空，则返回AplHandler
        if (!Strings.isNullOrEmpty(handlerDescribe.getAplApiName())) {
            handler = (T) handlerMap.get(getAPLHandlerApiName(handlerDescribe));
            if (Objects.isNull(handler)) {
                handler = (T) handlerMap.get(getDefaultAPLHandlerApiName(handlerDescribe));
            }
            return handler;
        }
        return null;
    }

    private String getRemoteHandlerApiName(SimpleHandlerDescribe handlerDescribe) {
        return "remoteHandler_" + handlerDescribe.getInterfaceCode();
    }

    private String getDefaultRemoteHandlerApiName() {
        return "defaultRemoteHandler";
    }

    private String getAPLHandlerApiName(SimpleHandlerDescribe handlerDescribe) {
        if (handlerDescribe.supportDistributedTransaction()) {
            return "APLTCCActionHandler_" + handlerDescribe.getInterfaceCode();
        }
        return "APLHandler_" + handlerDescribe.getInterfaceCode();
    }

    private String getDefaultAPLHandlerApiName(SimpleHandlerDescribe handlerDescribe) {
        if (handlerDescribe.supportDistributedTransaction()) {
            return "defaultAPLTCCActionHandler";
        }
        return "defaultAPLHandler";
    }
}
