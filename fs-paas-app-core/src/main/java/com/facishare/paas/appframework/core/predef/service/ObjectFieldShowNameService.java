package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.fieldshowname.FindFieldShowName;
import com.facishare.paas.appframework.core.predef.service.dto.fieldshowname.FindRecordFieldMapping;
import com.facishare.paas.appframework.core.predef.service.dto.fieldshowname.UpsertFieldShowName;
import com.facishare.paas.appframework.metadata.FieldShowNameLogicService;
import com.facishare.paas.appframework.metadata.repository.model.FieldShowName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@ServiceModule("field_show_name")
@Service
public class ObjectFieldShowNameService {


    @Autowired
    private FieldShowNameLogicService fieldShowNameLogicService;

    @ServiceMethod("find_field_show_names")
    public FindFieldShowName.Result findFieldShowName(ServiceContext context, FindFieldShowName.Arg arg) {
        List<FieldShowName> fieldShowNames = fieldShowNameLogicService.findFieldShowNamesByApiName(context.getUser(), arg.getDescribeApiName());
        return FindFieldShowName.Result.builder()
                .fieldShowNames(fieldShowNames)
                .build();
    }

    @ServiceMethod("upsert_field_show_names")
    public UpsertFieldShowName.Result upsertFieldShowName(ServiceContext context, UpsertFieldShowName.Arg arg) {
        fieldShowNameLogicService.upsertFieldShowName(context.getUser(), arg.getDescribeApiName(), arg.getRecordApiName(), arg.getFieldShowNames());
        return UpsertFieldShowName.Result.builder().build();
    }


    @ServiceMethod("find_record_field_mapping")
    public FindRecordFieldMapping.Result findRecordFieldMapping(ServiceContext context, FindRecordFieldMapping.Arg arg) {
        Map<String, Object> recordFieldMapping = fieldShowNameLogicService.findRecordFieldMapping(context.getUser(), arg.getRecordFieldList());
        return FindRecordFieldMapping.Result.builder()
                .fieldMapping(recordFieldMapping)
                .build();
    }

}
