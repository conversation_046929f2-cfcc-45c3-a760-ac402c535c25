package com.facishare.paas.appframework.core.predef.action


import com.facishare.paas.appframework.core.model.User

/**
 * create by <PERSON><PERSON><PERSON> on 2020/09/06
 */
class StandardAddUIActionTest extends BaseActionTest {
    def "test_AddUIAction"() {
        expect:
        1 == 1
        /*
        given: "init user"
        initUser(tenantId, userId)
        and: "init RequestContext"
        initRequestContext()
        and: "init arg"
        def arg = JacksonUtils.fromJson(json, StandardAddUIAction.Arg)
        println arg
        when:
        def result = execute(apiName, StandardAction.AddUI.toString(), arg)
        then:
        println result
        noExceptionThrown()
        where:
        tenantId | userId | apiName           | json                     || _
        "74255"  | "1000" | "object_2mmga__c" | '''{"object_data":{}}''' || _
        */
    }


    def initUser(String tenantId, String userId) {
        user = new User(tenantId, userId)
    }

    def initRequestContext() {
        requestContext = buildRequestContext(user.getTenantId(), user.getUserId())
    }
}
