package com.facishare.paas.appframework.metadata.repository.fieldhandler;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.google.common.collect.Lists;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

public class <PERSON><PERSON><PERSON>ee<PERSON><PERSON><PERSON> implements FieldHandler{
    @Override
    public Object parseFromObjectDataToEntity(Object objectDataValue, FieldInfo fieldInfo) {
        if(Objects.isNull(objectDataValue)) {
            return null;
        }

        Class<?> fieldType = fieldInfo.getFieldType().getRawType();
        if(Collection.class.isAssignableFrom(fieldType)) {
            if (objectDataValue instanceof List) {
                if (CollectionUtils.empty((List) objectDataValue)) {
                    return null;
                }
                return objectDataValue;
            }
            return Lists.newArrayList(objectDataValue);
        } else if(Objects.equals(fieldType, String.class)) {
            if (objectDataValue instanceof List) {
                if (CollectionUtils.empty((List) objectDataValue)) {
                    return null;
                }
                return String.valueOf(((List) objectDataValue).get(0));
            }
            return String.valueOf(objectDataValue);
        } else {
            throw new ValidateException("parseFromObjectDataToEntity fail, field type not right");
        }
    }

    @Override
    public Object parseFromEntityToObjectData(Object entityValue, FieldInfo fieldInfo) {
        if(Objects.isNull(entityValue)) {
            return null;
        }

        Class<?> fieldType = fieldInfo.getFieldType().getRawType();
        if(Collection.class.isAssignableFrom(fieldType)) {
            return Lists.newArrayList((Collection)entityValue);
        } else if(Objects.equals(fieldType, String.class)) {
            return Lists.newArrayList(entityValue.toString());
        } else {
            throw new ValidateException("parseFromEntityToObjectData fail, Field type not right");
        }
    }
}
