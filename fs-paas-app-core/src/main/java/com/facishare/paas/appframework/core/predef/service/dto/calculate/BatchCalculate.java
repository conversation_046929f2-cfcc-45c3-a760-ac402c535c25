package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.UdobjGrayKey;
import com.facishare.paas.appframework.common.util.UdobjGrayUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.metadata.CalculateObjectData;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.relation.CalculateRelation.RelateField;
import com.facishare.paas.appframework.metadata.relation.FieldNode;
import com.facishare.paas.appframework.metadata.relation.FieldRelationGraph;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2018/4/27
 */
public interface BatchCalculate {

    String EDIT = "edit";
    String ADD = "add";
    String DEL = "del";
    String COPY = "copy";

    @Data
    class Arg {
        //主对象的apiName
        @JSONField(name = "M1")
        private String masterObjectApiName;
        //主对象数据
        @JSONField(name = "M2")
        private ObjectDataDocument masterData;
        //从对象数据(第一级的key是从对象的apiName，第二级的key是数据的编号)
        @JSONField(name = "M3")
        private Map<String, Map<String, ObjectDataDocument>> detailDataMap;
        //修改数据的对象的apiName（编辑、添加、删除从时该字段必须传值）
        @JSONField(name = "M4")
        private String modifiedObjectApiName;
        //修改数据的编号（编辑从的字段或添加从时该字段必须传值）
        @JSONField(name = "M5")
        private List<String> modifiedDataIndexList;
        //需要计算的字段的apiName(包括计算字段和统计字段，Map的key是对象的apiName)
        @JSONField(name = "M6")
        private Map<String, List<String>> calculateFieldApiNames;
        //是否计算传过来的所有字段（因为大部分情况下只有新建的或被编辑的从对象才会计算默认值，特殊情况下可已使用这个参数）
        @JSONField(name = "M7")
        private boolean calculateAllFields;

        //带着计算顺序的计算字段（用于替换calculateFieldApiNames）
        @JSONField(name = "M8")
        private Map<String, List<RelateField>> calculateFields;

        //按照从对象数据index指定不需要计算的字段（第1级key是从对象的apiName，第2级key是dataIndex，第2级value是不需要计算的字段）
        private Map<String, Map<String, List<RelateField>>> excludedDetailCalculateFields;

        /**
         * 需要掩码加密的字段
         */
        private Map<String, List<String>> maskFieldApiNames;

        //页面id，对于同一个新建/编辑页调用的请求，都用同一个id
        private String seriesId;

        //修改的类型（edit：编辑 ，add：添加，del：删除，copy：复制）
        private String modifiedType;
        //edit场景下，变更的字段
        private List<String> modifiedFields;
        //修改前的主对象数据（修改主对象时才需要传）
        private ObjectDataDocument oldMasterData;
        //修改前的从对象数据（modifiedObjectApiName是从对象时才需要传，当modifiedType等于add或copy时不用传该参数，modifiedType等于edit时传被修改的dataIndex对应的从对象全字段数据，modifiedType等于del的时候传被删除的dataIndex对应的从对象全字段数据）
        private Map<String, Map<String, ObjectDataDocument>> oldDetailDataMap;
        //是否跳过并发检查
        private Boolean skipConcurrentCheck;

        public boolean skipConcurrentCheck() {
            return Boolean.TRUE.equals(skipConcurrentCheck);
        }

        public boolean applyOptimize(String tenantId, Set<String> detailApiNames) {
            //没有灰度则不支持
            if (!UdobjGrayUtil.isObjectAndTenantGray(UdobjGrayKey.OPTIMIZE_DETAIL_OBJECT_CALCULATE_IN_BATCH_CALCULATE, tenantId, masterObjectApiName)) {
                return false;
            }
            //没有从对象则不支持
            if (CollectionUtils.empty(detailApiNames)) {
                return false;
            }
            //指定计算所有字段则不支持
            if (calculateAllFields) {
                return false;
            }
            //编辑主对象触发计算则不支持
            if (masterDataModified()) {
                return false;
            }
            //没有使用带order的计算字段参数则不支持
            if (CollectionUtils.empty(calculateFields)) {
                return false;
            }
            //没有查找关联对象的统计字段需要计算则不支持
            if (calculateFields.keySet().stream().noneMatch(x -> !Objects.equals(masterObjectApiName, x) && !detailApiNames.contains(x))) {
                return false;
            }
            //没有新参数则不支持
            if (Strings.isNullOrEmpty(modifiedType)) {
                return false;
            }
            //参数不合法则不支持
            if (!EDIT.equals(modifiedType) && !ADD.equals(modifiedType) && !DEL.equals(modifiedType) && !COPY.equals(modifiedType)) {
                return false;
            }

            if (EDIT.equals(modifiedType)) {
                //过滤掉__r字段
                modifiedFields = CollectionUtils.nullToEmpty(modifiedFields).stream()
                        .filter(x -> !StringUtils.endsWith(x, FieldDescribeExt.LOOKUP_NAME_SUFFIX)).collect(Collectors.toList());
                if (masterDataModified()) {
                    return CollectionUtils.notEmpty(modifiedFields);
                }
                return CollectionUtils.notEmpty(modifiedDataIndexList)
                        && CollectionUtils.notEmpty(modifiedFields)
                        && CollectionUtils.notEmpty(oldDetailDataMap)
                        && CollectionUtils.notEmpty(oldDetailDataMap.get(modifiedObjectApiName))
                        && CollectionUtils.isEqual(oldDetailDataMap.get(modifiedObjectApiName).keySet(), modifiedDataIndexList)
                        && oldDetailDataMap.get(modifiedObjectApiName).keySet().stream()
                        .filter(x -> modifiedDataIndexList.contains(x))
                        .allMatch(x -> CollectionUtils.notEmpty(oldDetailDataMap.get(modifiedObjectApiName).get(x))
                                && oldDetailDataMap.get(modifiedObjectApiName).get(x).keySet().containsAll(modifiedFields))
                        && CollectionUtils.notEmpty(detailDataMap)
                        && CollectionUtils.notEmpty(detailDataMap.get(modifiedObjectApiName))
                        && detailDataMap.get(modifiedObjectApiName).keySet().containsAll(modifiedDataIndexList)
                        && detailDataMap.get(modifiedObjectApiName).keySet().stream()
                        .filter(x -> modifiedDataIndexList.contains(x))
                        .allMatch(x -> CollectionUtils.notEmpty(detailDataMap.get(modifiedObjectApiName).get(x))
                                && detailDataMap.get(modifiedObjectApiName).get(x).keySet().containsAll(modifiedFields));
            }
            if (ADD.equals(modifiedType) || COPY.equals(modifiedType)) {
                return CollectionUtils.notEmpty(modifiedDataIndexList)
                        && CollectionUtils.notEmpty(detailDataMap)
                        && CollectionUtils.notEmpty(detailDataMap.get(modifiedObjectApiName))
                        && detailDataMap.get(modifiedObjectApiName).keySet().containsAll(modifiedDataIndexList);
            }
            return CollectionUtils.notEmpty(oldDetailDataMap)
                    && CollectionUtils.notEmpty(oldDetailDataMap.get(modifiedObjectApiName))
                    && oldDetailDataMap.get(modifiedObjectApiName).keySet().stream()
                    .allMatch(x -> CollectionUtils.empty(detailDataMap)
                            || CollectionUtils.empty(detailDataMap.get(modifiedObjectApiName))
                            || !detailDataMap.get(modifiedObjectApiName).containsKey(x));
        }

        public IObjectData oldMasterData() {
            if (CollectionUtils.empty(oldMasterData)) {
                return new ObjectData();
            }
            return oldMasterData.toObjectData();
        }

        public Map<String, Object> masterModifyData() {
            if (!EDIT.equals(modifiedType) || !masterDataModified()) {
                return Maps.newHashMap();
            }
            return ObjectDataExt.of(masterData).toMap(modifiedFields);
        }

        public Map<String, List<IObjectData>> oldDetailDataMap() {
            if (CollectionUtils.empty(oldDetailDataMap)) {
                return Maps.newHashMap();
            }
            Map<String, List<IObjectData>> result = Maps.newHashMap();
            oldDetailDataMap.forEach((apiName, dataMap) -> {
                if (CollectionUtils.empty(dataMap)) {
                    return;
                }
                List<IObjectData> dataList = Lists.newArrayList();
                dataMap.forEach((index, document) -> {
                    IObjectData data = document.toObjectData();
                    ObjectDataExt.of(data).setTemporaryId(index);
                    dataList.add(data);
                });
                result.put(apiName, dataList);
            });
            return result;
        }

        public Map<String, List<IObjectData>> detailModifyDataMap() {
            if (!EDIT.equals(modifiedType) || masterDataModified()) {
                return Maps.newHashMap();
            }
            Map<String, List<IObjectData>> result = Maps.newHashMap();
            List<IObjectData> dataList = Lists.newArrayList();
            detailDataMap.getOrDefault(modifiedObjectApiName, Collections.emptyMap()).forEach((index, document) -> {
                if (!modifiedDataIndexList.contains(index)) {
                    return;
                }
                IObjectData data = ObjectDataExt.of(ObjectDataExt.of(document).toMap(modifiedFields)).getObjectData();
                ObjectDataExt.of(data).setTemporaryId(index);
                dataList.add(data);
            });
            if (CollectionUtils.notEmpty(dataList)) {
                result.put(modifiedObjectApiName, dataList);
            }
            return result;
        }

        public Map<String, List<IObjectData>> detailDeleteDataMap() {
            if (!DEL.equals(modifiedType)) {
                return Maps.newHashMap();
            }
            Map<String, List<IObjectData>> result = Maps.newHashMap();
            oldDetailDataMap.forEach((apiName, dataMap) -> {
                if (CollectionUtils.empty(dataMap)) {
                    return;
                }
                List<IObjectData> dataList = Lists.newArrayList();
                dataMap.forEach((index, document) -> {
                    if (CollectionUtils.nullToEmpty(detailDataMap).getOrDefault(apiName, Collections.emptyMap()).containsKey(index)) {
                        return;
                    }
                    IObjectData data = document.toObjectData();
                    ObjectDataExt.of(data).setTemporaryId(index);
                    dataList.add(data);
                });
                if (CollectionUtils.notEmpty(dataList)) {
                    result.put(apiName, dataList);
                }
            });
            return result;
        }

        public Map<String, List<IObjectData>> detailAddDataMap(Map<String, List<CalculateObjectData>> detailCalculateDataMap) {
            if (!ADD.equals(modifiedType) && !COPY.equals(modifiedType)) {
                return Maps.newHashMap();
            }
            Map<String, List<IObjectData>> result = Maps.newHashMap();
            List<IObjectData> dataList = Lists.newArrayList();
            detailCalculateDataMap.getOrDefault(modifiedObjectApiName, Collections.emptyList()).forEach((calculateData -> {
                if (!modifiedDataIndexList.contains(calculateData.getIndex())) {
                    return;
                }
                IObjectData data = calculateData.getObjectData();
                ObjectDataExt.of(data).setTemporaryId(calculateData.getIndex());
                dataList.add(data);
            }));
            if (CollectionUtils.notEmpty(dataList)) {
                result.put(modifiedObjectApiName, dataList);
            }
            return result;
        }

        /**
         * 判断从对象的计算字段或默认值是否需要计算该从对象的所有数据
         *
         * @param detailApiName
         * @param fieldApiName
         * @param lookupAndMasterCountFields
         * @param graph
         * @return
         */
        public boolean needCalculateAllData(String detailApiName,
                                            String fieldApiName,
                                            Map<String, List<RelateField>> lookupAndMasterCountFields,
                                            FieldRelationGraph graph) {
            if (calculateAllFields) {
                return true;
            }
            //编辑主或其他从需要计算所有数据
            if (!detailApiName.equals(modifiedObjectApiName)) {
                return true;
            }
            Optional<FieldNode> detailNode = graph.getNode(detailApiName, fieldApiName);
            //引用字段只需要计算被编辑的数据
            if (!detailNode.isPresent()) {
                return false;
            }
            //引用主或lookup对象的统计字段的需要计算所有数据
            for (Map.Entry<String, List<RelateField>> lookupAndMasterCountField : lookupAndMasterCountFields.entrySet()) {
                String k = lookupAndMasterCountField.getKey();
                List<RelateField> v = lookupAndMasterCountField.getValue();
                if (v.stream().map(x -> graph.getNode(k, x.getFieldName())).filter(x -> x.isPresent()).anyMatch(x ->
                        graph.hasRoute(x.get(), detailNode.get()))) {
                    return true;
                }
            }

            return false;
        }

        public boolean detailDataModified(String detailApiName, String index) {
            if (!detailApiName.equals(modifiedObjectApiName)) {
                return false;
            }
            if (CollectionUtils.empty(modifiedDataIndexList)) {
                return false;
            }
            return modifiedDataIndexList.contains(index);
        }

        public boolean masterDataModified() {
            return Strings.isNullOrEmpty(modifiedObjectApiName) || Objects.equals(masterObjectApiName, modifiedObjectApiName);
        }

        /**
         * 判断是否是删除数据触发的计算
         *
         * @return
         */
        public boolean actByDeleteDetail() {
            if (Strings.isNullOrEmpty(modifiedObjectApiName) || masterObjectApiName.equals(modifiedObjectApiName)) {
                return false;
            }
            if (calculateAllFields) {
                return false;
            }
            return CollectionUtils.empty(modifiedDataIndexList) || DEL.equals(modifiedType);
        }

        public boolean actByCopyDetail() {
            if (Strings.isNullOrEmpty(modifiedObjectApiName) || masterObjectApiName.equals(modifiedObjectApiName)) {
                return false;
            }
            return COPY.equals(modifiedType);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        //计算结果(第一级的key是对象的apiName，第二级的key是数据的编号，主对象数据的编号是0)
        @JSONField(name = "M1")
        private Map<String, Map<String, ObjectDataDocument>> calculateResult = Maps.newHashMap();

        public void addData(String objectApiName, String index, IObjectData data, List<String> fieldNames) {
            calculateResult.putIfAbsent(objectApiName, Maps.newHashMap());
            calculateResult.get(objectApiName).putIfAbsent(index, ObjectDataDocument.of(Maps.newHashMap()));
            calculateResult.get(objectApiName).get(index).putAll(ObjectDataExt.of(data).toMap(fieldNames));

            //补充lookup字段的__r和引用字段的__v
            Map<String, Object> dataMap = ObjectDataExt.of(data).toMap();
            fieldNames.forEach(x -> {
                String lookupName = FieldDescribeExt.getLookupNameByFieldName(x);
                if (dataMap.containsKey(lookupName)) {
                    calculateResult.get(objectApiName).get(index).put(lookupName, dataMap.get(lookupName));
                }

                String quotedValueName = FieldDescribeExt.getQuotedValueNameByFieldName(x);
                if (dataMap.containsKey(quotedValueName)) {
                    calculateResult.get(objectApiName).get(index).put(quotedValueName, dataMap.get(quotedValueName));
                }
            });
        }

        public void addAll(Map<String, Map<String, IObjectData>> calculateData) {
            calculateData.forEach((objectApiName, dataMap) -> {
                calculateResult.putIfAbsent(objectApiName, Maps.newHashMap());
                dataMap.forEach((index, objectData) -> calculateResult.get(objectApiName).putIfAbsent(index, ObjectDataDocument.of(objectData)));
            });
        }
    }

}
