package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction.Arg;
import static com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction.Result;

/**
 * Created by liyiguang on 2017/11/21.
 */
@Slf4j
public class StandardBulkRecoverAction extends AbstractStandardAction<Arg, Result> {

    protected static final int PAGE_SIZE = 1000;

    protected List<IObjectData> allHandledDataList = Lists.newArrayList();

    protected Map<String, IObjectDescribe> describeMap = Maps.newHashMap();

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.RECOVER_FUN_PRIVILEGE_GRAY, actionContext.getTenantId())) {
            return StandardAction.BulkRecover.getFunPrivilegeCodes();
        }
        return Collections.emptyList();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        if (AppFrameworkConfig.isGrayRecoverButton(actionContext.getTenantId(), actionContext.getObjectApiName())
                || UdobjGrayConfig.isAllow(UdobjGrayConfigKey.RECOVER_FUN_PRIVILEGE_GRAY, actionContext.getTenantId())) {
            return arg.getIdList();
        }
        return Collections.emptyList();
    }

    @Override
    protected boolean needInvalidData() {
        return true;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result doAct(Arg arg) {
        //参数校验
        if (CollectionUtils.empty(arg.getIdList())) {
            return Result.builder().build();
        }

        //恢复主对象
        List<IObjectData> recovered = dealMasterData(arg);

        //批量恢复从对象
        dealWithDetail(recovered);

        // 记录修改日志
        recordLog();

        return Result.builder().success(true).build();
    }

    private void recordLog() {
        serviceFacade.masterDetailLog(actionContext.getUser(), EventType.MODIFY, ActionType.Recovery, describeMap, allHandledDataList);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        serviceFacade.sendActionMq(actionContext.getUser(), allHandledDataList, ObjectAction.RECOVER);
        return super.after(arg, result);
    }

    protected List<IObjectData> dealMasterData(Arg arg) {
        //灰度企业的主从一起新建的从不允许单独恢复
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObjectCreateWithMasterAndInGrayList()
                && !actionContext.isFromOpenAPI()) {
            throw new ValidateException(I18N.text(I18NKey.CANNOT_RECOVER_DETAIL_CREATE_WITH_MASTER));
        }

        //D对象可单独作废，判断D数据所对象的M数据是否已作废，如果已作废提示：“当前数据所属主对象已作废，请先恢复主对象”。
        ObjectDescribeExt.of(objectDescribe).getMasterDetailFieldDescribe().ifPresent(it -> {
            List<IObjectData> listToRecover = serviceFacade.findObjectDataByIdsIncludeDeletedIgnoreFormula(actionContext.getUser(), arg.idList,
                    objectDescribe.getApiName());
            List<String> ids = listToRecover.stream()
                    .map(x -> x.get(it.getApiName(), String.class))
                    .filter(x -> !Strings.isNullOrEmpty(x)).collect(Collectors.toList());
            if (CollectionUtils.notEmpty(ids)) {
                List<IObjectData> referIdListToRecover =
                        serviceFacade.findObjectDataByIdsIncludeDeletedIgnoreFormula(actionContext.getUser(), ids, it.getTargetApiName());
                for (IObjectData objectData : referIdListToRecover) {

                    if (objectData.isDeleted())
                        throw new ValidateException(I18N.text(I18NKey.MASTER_IS_INVALID_PLEASE_RECOVER_MASTER_OBJECT));
                }
            }
        });

        //找到当前arg数据ids的主对象ids。
        //查询主对象objectData。
        //判断ObjectData的is_deleted值是否是true。
        //如果是,throw,否,就继续走
        return recoverObjects(arg.buildRecoverObjectDataParameter(actionContext), objectDescribe);
    }

    protected void dealWithDetail(List<IObjectData> masterDataList) {
        if (CollectionUtils.empty(masterDataList)) {
            return;
        }

        //查从对象
        List<IObjectDescribe> detailDescribes =
                serviceFacade.findDetailDescribes(actionContext.getTenantId(), objectDescribe.getApiName());

        if (CollectionUtils.empty(detailDescribes)) {
            return;
        }

        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        detailDescribes.forEach(detailDescribe -> task.submit(() -> dealOneDetail(detailDescribe, masterDataList)));
        try {
            task.await(5000, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.MD_RECOVER_DETAIL_DATA_TIME_OUT));
        }
    }

    protected void dealOneDetail(IObjectDescribe detailDescribe, List<IObjectData> masterDataList) {
        // 恢复时，查询所有状态为 0 和 1 的从对象
        List<IFilter> filters = Lists.newArrayList(FilterExt.of(Operator.EQ, IObjectData.IS_DELETED,
                Lists.newArrayList(String.valueOf(DELETE_STATUS.INVALID.getValue()), String.valueOf(DELETE_STATUS.NORMAL.getValue()))).getFilter());
        List<String> masterDataIds = masterDataList.stream().map(x -> x.getId()).collect(Collectors.toList());
        try {
            QueryResult<IObjectData> detailDataResult = serviceFacade.findDetailObjectDataBatchWithPageIncludeInvalid(actionContext.getUser(),
                    objectDescribe.getApiName(), masterDataIds, detailDescribe, 1, PAGE_SIZE, filters);

            int totalPage = SearchTemplateQueryExt.calculateTotalPage(detailDataResult.getTotalNumber(), PAGE_SIZE);
            for (int pageNum = 1; pageNum <= totalPage; pageNum++) {
                if (pageNum > 1) {
                    detailDataResult = serviceFacade.findDetailObjectDataBatchWithPageIncludeInvalid(actionContext.getUser(),
                            objectDescribe.getApiName(), masterDataIds, detailDescribe, pageNum, PAGE_SIZE, filters);
                }

                // 恢复 作废状态的从
                List<IObjectData> dataList = CollectionUtils.nullToEmpty(detailDataResult.getData()).stream()
                        .filter(data -> ObjectDataExt.of(data).isInvalid())
                        .collect(Collectors.toList());
                if (CollectionUtils.empty(dataList)) {
                    continue;
                }
                // 增加日志排查问题
                log.warn("recover detailDataResult ids:{}", dataList.stream().map(DBRecord::getId).collect(Collectors.joining(",")));

                recoverObjects(dataList, detailDescribe);
            }
        } catch (MetaDataBusinessException e) {
            log.warn("dealWithDetail error,user:{},masterApiName:{},masterDataId:{},detailApiName:{}",
                    actionContext.getUser(), objectDescribe.getApiName(), masterDataIds, detailDescribe.getApiName(), e);
        } catch (Exception e) {
            log.error("dealWithDetail error,user:{},masterApiName:{},masterDataId:{},detailApiName:{}",
                    actionContext.getUser(), objectDescribe.getApiName(), masterDataIds, detailDescribe.getApiName(), e);
        }
    }

    protected List<IObjectData> recoverObjects(List<IObjectData> dataList, IObjectDescribe describe) {
        if (CollectionUtils.empty(dataList)) {
            return dataList;
        }

        List<IObjectData> recovered = serviceFacade.bulkRecover(dataList, actionContext.getUser());
        allHandledDataList.addAll(recovered);
        describeMap.put(describe.getApiName(), describe);

        return recovered;
    }

    @Override
    protected IObjectData getPreObjectData() {
        if (CollectionUtils.empty(dataList)) {
            return null;
        }
        return dataList.get(0);
    }

    @Override
    protected IObjectData getPostObjectData() {
        if (CollectionUtils.empty(arg.getIdList()) || isBatchAction()) {
            return null;
        }
        return serviceFacade.findObjectData(actionContext.getUser(), arg.getIdList().get(0), objectDescribe);
    }

    @Override
    protected String getButtonApiName() {
        if (AppFrameworkConfig.isGrayRecoverButton(actionContext.getTenantId(), actionContext.getObjectApiName())) {
            return ObjectAction.RECOVER.getButtonApiName();
        }
        return null;
    }

    @Data
    public static class Arg {
        @JSONField(name = "M1")
        @JsonProperty("describe_api_name")
        @SerializedName("describe_api_name")
        String objectDescribeAPIName;

        @JSONField(name = "M2")
        List<String> idList;

        public static Arg of(String describeApiName, String dataId) {
            Arg arg = new Arg();
            arg.setIdList(Lists.newArrayList(dataId));
            arg.setObjectDescribeAPIName(describeApiName);
            return arg;
        }

        public List<IObjectData> buildRecoverObjectDataParameter(ActionContext context) {
            return idList.stream().map(x -> buildObjectData(context.getTenantId(), objectDescribeAPIName, x))
                    .collect(Collectors.toList());
        }

        private IObjectData buildObjectData(String tenantId, String objectDescribeAPIName, String id) {
            IObjectData data = new ObjectData();
            data.setId(id);
            data.setTenantId(tenantId);
            data.setDescribeApiName(objectDescribeAPIName);
            return data;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        @JSONField(name = "M1")
        private List<ObjectDataDocument> dataList;
        @JSONField(name = "M2")
        private Boolean success;
        @JSONField(name = "M3")
        private Integer pageCount;
        @JSONField(name = "M4")
        private Integer pageNumber;
        @JSONField(name = "M5")
        private Integer pageSize;
        @JSONField(name = "M6")
        private Integer totalCount;
    }

}
