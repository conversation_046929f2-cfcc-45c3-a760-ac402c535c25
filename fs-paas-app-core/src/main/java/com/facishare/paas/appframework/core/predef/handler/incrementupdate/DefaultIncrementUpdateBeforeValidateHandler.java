package com.facishare.paas.appframework.core.predef.handler.incrementupdate;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@HandlerProvider(name = "defaultIncrementUpdateBeforeValidateHandler")
public class DefaultIncrementUpdateBeforeValidateHandler implements IncrementUpdateHandler {

    @Autowired
    private ServiceFacade serviceFacade;

    @Override
    public Result handle(HandlerContext context, Arg arg) {
        validateDataType(context, arg);
        checkDateRangeFieldValue(arg);
        return new Result();
    }


    private void validateDataType(HandlerContext context, Arg arg) {
        //校验主对象
        serviceFacade.validateDataType(arg.getObjectDescribe(), Lists.newArrayList(arg.getObjectData().toObjectData()), context.getUser());
    }

    private void checkDateRangeFieldValue(Arg arg) {
        List<String> errorMsg = ObjectDataExt.of(arg.getObjectData().toObjectData()).validateDateRangeField(arg.getObjectDescribe());
        if (CollectionUtils.notEmpty(errorMsg)) {
            throw new ValidateException(errorMsg.get(0));
        }
    }
}
