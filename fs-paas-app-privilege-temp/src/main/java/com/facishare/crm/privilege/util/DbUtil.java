package com.facishare.crm.privilege.util;


import com.github.autoconf.api.IConfig;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;

/**
 * Created by luxin on 2016/12/28.
 */
@Slf4j
public class DbUtil {


    public static DataSource buildTomcatJdbcDataSource(String key, String url, IConfig config) {
        org.apache.tomcat.jdbc.pool.DataSource ds = new org.apache.tomcat.jdbc.pool.DataSource();
        ds.setName("db-" + key);
        ds.setUrl(url);
        // TODO
        ds.setDriverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
//        ds.setDriverClassName("net.sourceforge.jtds.jdbc.Driver");
        ds.setUsername(config.get("username"));
        try {
            ds.setPassword(config.get("password"));
        } catch (Exception e) {
            log.error("Password decode error : {}", config.get("password"), e);

        }

        ds.setMaxActive(config.getInt("tomcat-jdbc.maxActive", 100));
        ds.setInitialSize(config.getInt("tomcat-jdbc.initialSize", 10));
        ds.setMinIdle(config.getInt("tomcat-jdbc.minIdle", 10));
        ds.setMaxWait(config.getInt("tomcat-jdbc.maxWait", 60000));
        ds.setTimeBetweenEvictionRunsMillis(config.getInt("tomcat-jdbc.timeBetweenEvictionRunsMillis", 3000));
        ds.setMinEvictableIdleTimeMillis(config.getInt("tomcat-jdbc.minEvictableIdleTimeMillis", 300000));
        ds.setValidationQuery(config.get("tomcat-jdbc.validationQuery", "SELECT 'x'"));
        ds.setTestWhileIdle(config.getBool("tomcat-jdbc.testWhileIdle", true));
        ds.setTestOnReturn(config.getBool("tomcat-jdbc.testOnReturn", false));
        ds.setTestOnBorrow(config.getBool("tomcat-jdbc.testOnBorrow", true));

//        ds.setRemoveAbandoned(config.getBool("tomcat-jdbc.removeAbandoned", true));
//        ds.setRemoveAbandonedTimeout(config.getInt("tomcat-jdbc.removeAbandonedTimeout", 3600));
//        ds.setLogAbandoned(config.getBool("tomcat-jdbc.logAbandoned", true));

        return ds;
    }


}