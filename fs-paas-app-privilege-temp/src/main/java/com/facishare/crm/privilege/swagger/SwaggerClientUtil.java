package com.facishare.crm.privilege.swagger;

import com.facishare.crm.privilege.util.Constant;
import com.github.autoconf.api.IChangeableConfig;
import com.facishare.crm.privilege.swagger.ApiClient;

/**
 * Created by lei on 12/8/16.
 */
public class SwaggerClientUtil {
  private static ApiClient orgApiClient = new ApiClient();
  private static ApiClient authApiClient = new ApiClient();

  public static ApiClient getApiClient(String key, IChangeableConfig props) {
    if (Constant.BASE_URL_AUTH_KEY.equals(key)) {
      authApiClient.setBasePath(props.get(key, Constant.getBaseUrlAuth()));
      return authApiClient;
    }
    if (Constant.BASE_URL_ORG_KEY.equals(key)) {
      orgApiClient.setBasePath(props.get(key, Constant.getBaseUrlOrg()));
      return orgApiClient;
    }
    return null;
  }
}
