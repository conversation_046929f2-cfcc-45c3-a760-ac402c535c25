package com.facishare.paas.appframework.common.service

import com.facishare.paas.appframework.common.service.model.PollingKeys
import com.facishare.paas.appframework.common.service.model.PollingMsgEndType
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.support.GDSHandler
import com.facishare.polling.api.arg.UpdatePollingDataArg
import com.facishare.polling.api.enums.PollingOSType
import com.facishare.polling.api.util.PollingMessageProducer
import com.facishare.polling.api.util.RangeBuilder
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class MessagePollingServiceImplTest extends Specification {
    MessagePollingServiceImpl messagePollingService
    GDSHandler gdsHandler = Mock(GDSHandler)
    PollingMessageProducer pollingMessageProducer = Mock(PollingMessageProducer)
    
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    
    def setup() {
        messagePollingService = new MessagePollingServiceImpl()
        messagePollingService.gdsHandler = gdsHandler
        messagePollingService.pollingMessageProducer = pollingMessageProducer
    }
    
    def "测试sendPollingMessage方法"() {
        given:
        def tenantId = "12345"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        def pollingKey = "test_polling_key"
        def pollingMsgEndType = PollingMsgEndType.MOBILE
        def isRealTime = true
        def ea = "test_ea"
        
        when:
        messagePollingService.sendPollingMessage(user, pollingKey, pollingMsgEndType, isRealTime)
        
        then:
        1 * gdsHandler.getEAByEI(tenantId) >> ea
        1 * pollingMessageProducer.sendMessage(_) >> { args ->
            UpdatePollingDataArg arg = args[0]
            assert arg.key == pollingKey
            assert arg.osType == PollingOSType.APP
            assert arg.realTime == isRealTime
            assert arg.range != null
        }
    }
    
    def "测试sendWholeNetworkPollingMessage方法"() {
        given:
        def tenantId = "12345"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        def pollingKey = "test_polling_key"
        def pollingMsgEndType = PollingMsgEndType.MOBILE
        def isRealTime = true
        
        when:
        messagePollingService.sendWholeNetworkPollingMessage(user, pollingKey, pollingMsgEndType, isRealTime)
        
        then:
        1 * pollingMessageProducer.sendMessage(_) >> { args ->
            UpdatePollingDataArg arg = args[0]
            assert arg.key == pollingKey
            assert arg.osType == PollingOSType.APP
            assert arg.realTime == isRealTime
            assert arg.range != null
        }
    }
    
    def "测试notifyDescribeLayoutChange方法 - 全网络"() {
        given:
        def tenantId = "12345"
        def isWholeNetwork = true
        
        when:
        messagePollingService.notifyDescribeLayoutChange(tenantId, isWholeNetwork)
        
        then:
        1 * pollingMessageProducer.sendMessage({ UpdatePollingDataArg arg ->
            assert arg.key == PollingKeys.DESCRIBE_LAYOUT_CHANGE_ALL
            assert arg.osType == PollingOSType.APP
            assert arg.realTime == true
            true
        })
    }
    
    def "测试notifyDescribeLayoutChange方法 - 非全网络"() {
        given:
        def tenantId = "12345"
        def isWholeNetwork = false
        def ea = "test_ea"
        
        when:
        messagePollingService.notifyDescribeLayoutChange(tenantId, isWholeNetwork)
        
        then:
        1 * gdsHandler.getEAByEI(tenantId) >> ea
        1 * pollingMessageProducer.sendMessage({ UpdatePollingDataArg arg ->
            assert arg.key == PollingKeys.DESCRIBE_LAYOUT_CHANGE
            assert arg.osType == PollingOSType.APP
            assert arg.realTime == true
            true
        })
    }
    
    def "测试doSendPollingMessage方法异常处理"() {
        given:
        def tenantId = "12345"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        user.getUserId() >> "67890"
        def pollingKey = "test_polling_key"
        def pollingOSType = PollingOSType.APP
        def isRealTime = true
        def isWholeNetwork = false
        def ea = "test_ea"
        
        when:
        // 利用反射调用私有方法
        Whitebox.invokeMethod(messagePollingService, "doSendPollingMessage", user, pollingKey, pollingOSType, isRealTime, isWholeNetwork)
        
        then:
        1 * gdsHandler.getEAByEI(tenantId) >> ea
        1 * pollingMessageProducer.sendMessage(_) >> { throw new RuntimeException("测试异常") }
        noExceptionThrown() // 方法内部应该捕获异常并记录日志
    }
    
    def "测试不同终端类型的polling消息"() {
        given:
        def tenantId = "12345"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        def pollingKey = "test_polling_key"
        def ea = "test_ea"
        
        when:
        messagePollingService.sendPollingMessage(user, pollingKey, endType, true)
        
        then:
        1 * gdsHandler.getEAByEI(tenantId) >> ea
        1 * pollingMessageProducer.sendMessage({ UpdatePollingDataArg arg ->
            assert arg.osType == expectedOSType
            true
        })
        
        where:
        endType                  | expectedOSType
        PollingMsgEndType.IOS    | PollingOSType.IOS
        PollingMsgEndType.ANDROID| PollingOSType.ANDROID
        PollingMsgEndType.WEB    | PollingOSType.WEB
        PollingMsgEndType.H5     | PollingOSType.WEB
        PollingMsgEndType.MOBILE | PollingOSType.APP
        PollingMsgEndType.ALL    | PollingOSType.WEBANDAPP
    }
} 