package com.facishare.paas.appframework.core.predef.service.dto.brushData;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by zhaooju on 2025/4/24
 */
public interface CalculateForAdd {

    @Data
    class Arg {
        private String describeApiName;
        private ObjectDataDocument objectData;
        private Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();
    }

    @Data
    @Builder
    class Result {
        private ObjectDataDocument objectData;
        private Map<String, List<ObjectDataDocument>> details;
    }
}
