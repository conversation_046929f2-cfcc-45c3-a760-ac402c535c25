package com.facishare.paas.appframework.flow;

import com.facishare.paas.appframework.flow.dto.InitializeWorkFlow;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import java.util.Map;

@RestResource(value = "PAAS-WORKFLOW", desc = "工作流服务", contentType = "application/json")
public interface WorkFlowRestProxy {

  @POST(value = "fs-workflow/workflow/initDeploy", desc = "初始化工作流")
  InitializeWorkFlow.Result initializeWorkFlow(@Body InitializeWorkFlow.Arg arg, @HeaderMap Map<String, String> header);
}
