package com.facishare.paas.appframework.metadata.copy;

import com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.config.ApplicationLayeredGrayService;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.options.SelectFieldDependenceLogicService;
import com.facishare.paas.appframework.metadata.options.bo.OptionDependence;
import com.facishare.paas.appframework.metadata.options.bo.SelectFieldDependence;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CopyObjectServiceImplTest {

    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private ConfigService configService;
    
    @Mock
    private LayoutLogicService layoutLogicService;
    
    @Mock
    private OptionalFeaturesService optionalFeaturesService;
    
    @Mock
    private AppDefaultRocketMQProducer copyObjectProducer;
    
    @Mock
    private SelectFieldDependenceLogicService dependenceLogicService;
    
    @Mock
    private ApplicationLayeredGrayService applicationLayeredGrayService;

    @InjectMocks
    private CopyObjectServiceImpl copyObjectService;

    private User testUser;
    private IObjectDescribe sourceObjectDescribe;
    private ObjectDescribeExt sourceObjectDescribeExt;

    @BeforeEach
    void setUp() {
        testUser = new User("74255", "1000");

        // 创建测试用的IObjectDescribe
        sourceObjectDescribe = new ObjectDescribe();
        sourceObjectDescribe.setApiName("sourceObject");
        sourceObjectDescribe.setDisplayName("源对象");
        
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put(IFieldDescribe.API_NAME, "testField");
        fieldMap.put(IFieldDescribe.TYPE, IFieldType.TEXT);
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        sourceObjectDescribe.setFieldDescribes(Lists.newArrayList(fieldDescribe));
        
        sourceObjectDescribeExt = ObjectDescribeExt.of(sourceObjectDescribe);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试正常复制对象的完整流程
     */


    @Test
    @DisplayName("正常场景 - 复制对象成功")
    void testCopyObject_Success() {
        // 准备测试数据
        String sourceDescribeApiName = "sourceObject";
        String objectApiName = "targetObject";
        String describeLabel = "目标对象";
        
        // 配置Mock行为
        when(describeLogicService.findObject(testUser.getTenantId(), sourceDescribeApiName))
            .thenReturn(sourceObjectDescribe);
        
        // Mock AppFrameworkConfig静态方法
        try (MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            mockedConfig.when(() -> AppFrameworkConfig.getMaxDetailObjectCount(testUser.getTenantId()))
                .thenReturn(5);
            mockedConfig.when(() -> AppFrameworkConfig.getMaxDetailObjectCountPreObject(anyString()))
                .thenReturn(3);
            
            // Mock System.currentTimeMillis
            try (MockedStatic<System> mockedSystem = mockStatic(System.class)) {
                mockedSystem.when(System::currentTimeMillis).thenReturn(1234567890L);
                
                // Mock其他依赖
                when(dependenceLogicService.findAll(testUser, sourceDescribeApiName))
                    .thenReturn(Lists.newArrayList());
                when(layoutLogicService.findByTypesIncludeFlowLayout(any(LayoutLogicService.LayoutContext.class), eq(sourceDescribeApiName), any()))
                    .thenReturn(Lists.newArrayList());
                
                OptionalFeaturesSwitchDTO optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO();
                optionalFeaturesSwitch.setIsRelatedTeamEnabled(true);
                optionalFeaturesSwitch.setIsFollowUpDynamicEnabled(true);
                optionalFeaturesSwitch.setIsModifyRecordEnabled(true);
                optionalFeaturesSwitch.setIsGlobalSearchEnabled(true);
                optionalFeaturesSwitch.setMultiFieldSort(false);
                when(optionalFeaturesService.findOptionalFeaturesSwitch(testUser.getTenantId(), any()))
                    .thenReturn(optionalFeaturesSwitch);
                
                // 执行被测试方法
                copyObjectService.copyObject(testUser, sourceDescribeApiName, objectApiName, describeLabel);
                
                // 验证结果
                verify(describeLogicService).findObject(testUser.getTenantId(), sourceDescribeApiName);
                verify(describeLogicService).createDescribe(eq(testUser), any(ObjectDescribeExt.class), isNull(), isNull(), eq(false));
                verify(copyObjectProducer).sendMessage(any(), anyInt());
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当源对象不存在时抛出异常
     */


    @Test
    @DisplayName("异常场景 - 源对象不存在")
    void testCopyObjectThrowsException_SourceObjectNotFound() {
        // 准备测试数据
        String sourceDescribeApiName = "nonExistentObject";
        String objectApiName = "targetObject";
        String describeLabel = "目标对象";
        
        // 配置Mock行为
        when(describeLogicService.findObject(testUser.getTenantId(), sourceDescribeApiName))
            .thenReturn(null);
        
        // 执行并验证异常
        assertThrows(NullPointerException.class, () -> {
            copyObjectService.copyObject(testUser, sourceDescribeApiName, objectApiName, describeLabel);
        });
        
        // 验证Mock交互
        verify(describeLogicService).findObject(testUser.getTenantId(), sourceDescribeApiName);
        verify(describeLogicService, never()).createDescribe(any(), any(), any(), any(), anyBoolean());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试复制字段依赖关系的正常场景
     */


    @Test
    @DisplayName("正常场景 - 复制字段依赖关系")
    void testCopyFieldDependence_Success() {
        // 准备测试数据
        String sourceDescribeApiName = "sourceObject";
        List<OptionDependence> dependenceList = Lists.newArrayList(
            new OptionDependence("value1", Lists.newArrayList("child1")),
            new OptionDependence("value2", Lists.newArrayList("child2"))
        );
        List<SelectFieldDependence> sourceSelectFieldDependence = Lists.newArrayList(
            new SelectFieldDependence(sourceDescribeApiName, "field1", "childField1", dependenceList),
            new SelectFieldDependence(sourceDescribeApiName, "field2", "childField2", dependenceList)
        );
        
        // 配置Mock行为
        when(dependenceLogicService.findAll(testUser, sourceDescribeApiName))
            .thenReturn(sourceSelectFieldDependence);
        
        // 使用反射调用私有方法
        ReflectionTestUtils.invokeMethod(copyObjectService, "copyFieldDependence", 
            testUser, sourceDescribeApiName, sourceObjectDescribe);
        
        // 验证结果
        verify(dependenceLogicService).findAll(testUser, sourceDescribeApiName);
        verify(dependenceLogicService, times(2)).copy(eq(testUser), eq(sourceObjectDescribe), any(SelectFieldDependence.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当没有字段依赖关系时的场景
     */


    @Test
    @DisplayName("正常场景 - 无字段依赖关系")
    void testCopyFieldDependence_EmptyDependence() {
        // 准备测试数据
        String sourceDescribeApiName = "sourceObject";
        
        // 配置Mock行为
        when(dependenceLogicService.findAll(testUser, sourceDescribeApiName))
            .thenReturn(Lists.newArrayList());
        
        // 使用反射调用私有方法
        ReflectionTestUtils.invokeMethod(copyObjectService, "copyFieldDependence", 
            testUser, sourceDescribeApiName, sourceObjectDescribe);
        
        // 验证结果
        verify(dependenceLogicService).findAll(testUser, sourceDescribeApiName);
        verify(dependenceLogicService, never()).copy(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理对象描述的正常场景
     */


    @Test
    @DisplayName("正常场景 - 处理对象描述")
    void testProcessObjectDescribe_Success() {
        // 准备测试数据
        String objectApiName = "targetObject";
        String describeLabel = "目标对象";
        List<String> needToDelFieldApiNames = Lists.newArrayList();
        
        // Mock System.currentTimeMillis
        try (MockedStatic<System> mockedSystem = mockStatic(System.class)) {
            mockedSystem.when(System::currentTimeMillis).thenReturn(1234567890L);
            
            // 使用反射调用私有方法
            ObjectDescribeExt result = ReflectionTestUtils.invokeMethod(copyObjectService, "processObjectDescribe", 
                testUser, objectApiName, describeLabel, sourceObjectDescribe, needToDelFieldApiNames);
            
            // 验证结果
            assertNotNull(result);
            assertEquals(objectApiName, result.getApiName());
            assertEquals(describeLabel, result.getDisplayName());
            assertEquals(testUser.getUserId(), result.getCreatedBy());
            assertEquals(testUser.getUserId(), result.getLastModifiedBy());
            assertEquals(1234567890L, result.getCreateTime());
            assertEquals(1234567890L, result.getLastModifiedTime());
            assertFalse(result.isActive());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试发送复制对象消息的正常场景
     */


    @Test
    @DisplayName("正常场景 - 发送复制对象消息")
    void testSendCopyObjectMessage_Success() {
        // 准备测试数据
        String sourceDescribeApiName = "sourceObject";
        String objectApiName = "targetObject";
        String describeLabel = "目标对象";
        
        // 使用反射调用私有方法
        ReflectionTestUtils.invokeMethod(copyObjectService, "sendCopyObjectMessage", 
            testUser, sourceDescribeApiName, objectApiName, describeLabel);
        
        // 验证结果
        verify(copyObjectProducer).sendMessage(any(), anyInt());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试复制布局的正常场景
     */


    @Test
    @DisplayName("正常场景 - 复制布局")
    void testCopyLayouts_Success() {
        // 准备测试数据
        String targetObjectApiName = "sourceObject";
        String objectApiName = "targetObject";
        List<String> needToDelFieldApiNames = Lists.newArrayList();
        
        // 创建Mock布局
        ILayout mockLayout = mock(ILayout.class);
        when(mockLayout.getRefObjectApiName()).thenReturn(targetObjectApiName);
        List<ILayout> layouts = Lists.newArrayList(mockLayout);
        
        // 配置Mock行为
        when(layoutLogicService.findByTypesIncludeFlowLayout(any(LayoutLogicService.LayoutContext.class), eq(targetObjectApiName), any()))
            .thenReturn(layouts);
        
        // 使用反射调用私有方法
        ReflectionTestUtils.invokeMethod(copyObjectService, "copyLayouts", 
            testUser, targetObjectApiName, objectApiName, needToDelFieldApiNames);
        
        // 验证结果
        verify(layoutLogicService).findByTypesIncludeFlowLayout(any(LayoutLogicService.LayoutContext.class), eq(targetObjectApiName), any());
        verify(layoutLogicService).bulkCreate(layouts);
        verify(mockLayout).setRefObjectApiName(objectApiName);
        verify(mockLayout).setId(null);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试复制可选功能开关的正常场景
     */


    @Test
    @DisplayName("正常场景 - 复制可选功能开关")
    void testCopyOptionalFeatureSwitch_Success() {
        // 准备测试数据
        String objectApiName = "targetObject";
        
        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO();
        optionalFeaturesSwitch.setIsRelatedTeamEnabled(false);
        optionalFeaturesSwitch.setIsFollowUpDynamicEnabled(true);
        optionalFeaturesSwitch.setIsModifyRecordEnabled(true);
        optionalFeaturesSwitch.setIsGlobalSearchEnabled(true);
        optionalFeaturesSwitch.setMultiFieldSort(true);
        
        // 配置Mock行为
        when(optionalFeaturesService.findOptionalFeaturesSwitch(testUser.getTenantId(), sourceObjectDescribe))
            .thenReturn(optionalFeaturesSwitch);
        
        // 使用反射调用私有方法
        ReflectionTestUtils.invokeMethod(copyObjectService, "copyOptionalFeatureSwitch", 
            testUser, objectApiName, sourceObjectDescribe);
        
        // 验证结果
        verify(optionalFeaturesService).findOptionalFeaturesSwitch(testUser.getTenantId(), sourceObjectDescribe);
        verify(configService).createTenantConfig(eq(testUser), 
            eq(objectApiName + "|" + OptionalFeaturesService.OPTIONAL_FEATURES), 
            anyString(), eq(ConfigValueType.JSON));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当所有可选功能都启用且无多字段排序时不创建配置
     */


    @Test
    @DisplayName("正常场景 - 所有功能启用时不创建配置")
    void testCopyOptionalFeatureSwitch_AllFeaturesEnabled() {
        // 准备测试数据
        String objectApiName = "targetObject";
        
        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO();
        optionalFeaturesSwitch.setIsRelatedTeamEnabled(true);
        optionalFeaturesSwitch.setIsFollowUpDynamicEnabled(true);
        optionalFeaturesSwitch.setIsModifyRecordEnabled(true);
        optionalFeaturesSwitch.setIsGlobalSearchEnabled(true);
        optionalFeaturesSwitch.setMultiFieldSort(false);
        
        // 配置Mock行为
        when(optionalFeaturesService.findOptionalFeaturesSwitch(testUser.getTenantId(), sourceObjectDescribe))
            .thenReturn(optionalFeaturesSwitch);
        
        // 使用反射调用私有方法
        ReflectionTestUtils.invokeMethod(copyObjectService, "copyOptionalFeatureSwitch", 
            testUser, objectApiName, sourceObjectDescribe);
        
        // 验证结果
        verify(optionalFeaturesService).findOptionalFeaturesSwitch(testUser.getTenantId(), sourceObjectDescribe);
        verify(configService, never()).createTenantConfig(any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取应用ID的正常场景
     */


    @Test
    @DisplayName("正常场景 - 获取应用ID支持分层")
    void testGetAppId_SupportLayered() {
        // 准备测试数据
        String objectApiName = "testObject";
        String expectedAppId = "testApp";
        
        LayoutLogicService.LayoutContext context = mock(LayoutLogicService.LayoutContext.class);
        when(context.getAppIdByLayoutType(LayoutTypes.EDIT)).thenReturn(expectedAppId);
        when(context.getUser()).thenReturn(testUser);
        
        // 配置Mock行为
        when(applicationLayeredGrayService.supportAppLayered(testUser, expectedAppId, objectApiName))
            .thenReturn(true);
        
        // 使用反射调用私有方法
        String result = ReflectionTestUtils.invokeMethod(copyObjectService, "getAppId", 
            context, objectApiName);
        
        // 验证结果
        assertEquals(expectedAppId, result);
        verify(applicationLayeredGrayService).supportAppLayered(testUser, expectedAppId, objectApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取应用ID不支持分层时返回null
     */


    @Test
    @DisplayName("正常场景 - 获取应用ID不支持分层")
    void testGetAppId_NotSupportLayered() {
        // 准备测试数据
        String objectApiName = "testObject";
        String appId = "testApp";
        
        LayoutLogicService.LayoutContext context = mock(LayoutLogicService.LayoutContext.class);
        when(context.getAppIdByLayoutType(LayoutTypes.EDIT)).thenReturn(appId);
        when(context.getUser()).thenReturn(testUser);
        
        // 配置Mock行为
        when(applicationLayeredGrayService.supportAppLayered(testUser, appId, objectApiName))
            .thenReturn(false);
        
        // 使用反射调用私有方法
        String result = ReflectionTestUtils.invokeMethod(copyObjectService, "getAppId", 
            context, objectApiName);
        
        // 验证结果
        assertNull(result);
        verify(applicationLayeredGrayService).supportAppLayered(testUser, appId, objectApiName);
    }
}
