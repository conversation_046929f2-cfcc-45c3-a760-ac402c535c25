package com.facishare.paas.appframework.core.predef.action

import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.describe.ObjectReferenceManyFieldDescribe
import com.facishare.paas.metadata.impl.describe.*
import com.fxiaoke.functions.utils.Lists
import spock.lang.Specification

class StandardUnionInsertImportTemplateActionTest extends Specification {
    def objectApiName = "object_123__c"
    def detailApiName = "object_md__c"
    def actionCode = "UnionInsertImportTemplate"
    def tenantId = "74255"
    def userId = "1000"
    def outTenantId = "200074255"
    def outUserId = "100018916"
    def user = User.builder().tenantId(tenantId).userId(userId).outTenantId(outTenantId).outUserId(outUserId).build()
    def requestContext = RequestContext.builder().tenantId(tenantId).user(user).build()
    def actionContext = new ActionContext(requestContext, objectApiName, actionCode)

    ServiceFacade serviceFacade
    InfraServiceFacade infraServiceFacade
    IObjectDescribe objectDescribe
    IObjectDescribe detailDescribe
    SpringBeanHolder springBeanHolder
    List<IFieldDescribe> fieldDescribeList = Lists.newArrayList()

    def setup() {
        serviceFacade = Mock()
        infraServiceFacade = Mock()
        objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        objectDescribe.setIsActive(true)
        springBeanHolder = Mock(SpringBeanHolder)
        infraServiceFacade.getSpringBeanHolder() >> springBeanHolder


        detailDescribe = Spy(ObjectDescribe)
        detailDescribe.setApiName(detailApiName)
        MasterDetailFieldDescribe masterDetailFieldDescribe = new MasterDetailFieldDescribe()
        masterDetailFieldDescribe.setApiName("field_md__c")
        fieldDescribeList.add(masterDetailFieldDescribe)
        RecordTypeFieldDescribe recordTypeFieldDescribe = new RecordTypeFieldDescribe()
        recordTypeFieldDescribe.setApiName("record_type")
        fieldDescribeList.add(recordTypeFieldDescribe)
        PercentileFieldDescribe percentileFieldDescribe = new PercentileFieldDescribe()
        percentileFieldDescribe.setApiName("percentile__c")
        fieldDescribeList.add(percentileFieldDescribe)
        ObjectReferenceFieldDescribe objectReferenceFieldDescribe = new ObjectReferenceFieldDescribe()
        objectReferenceFieldDescribe.setApiName("field_reference__c")
        objectReferenceFieldDescribe.setTargetApiName(objectApiName)
        fieldDescribeList.add(objectReferenceFieldDescribe)
        ObjectReferenceManyFieldDescribe objectReferenceManyFieldDescribe = new ObjectReferenceManyFieldDescribe()
        objectReferenceManyFieldDescribe.setApiName("field_reference_many__c")
        objectReferenceManyFieldDescribe.setTargetApiName(objectApiName)
        fieldDescribeList.add(objectReferenceManyFieldDescribe)
        detailDescribe.setFieldDescribes(fieldDescribeList)
    }

    def "test before"() {
        given:
        List<String> unionApiNames = [objectApiName, detailApiName]

        List<BaseImportTemplateAction.DetailArg> detailInfoList = Lists.newArrayList()
        if (Objects.nonNull(detailArg)) {
            detailArg.setDetailApiName(detailApiName)
            detailInfoList.add(detailArg)
        }

        BaseImportTemplateAction.Arg arg = new BaseImportTemplateAction.Arg(
                unionImportApiNameList: unionApiNames,
                detailArg: detailInfoList
        )

        StandardUnionInsertImportTemplateAction action = new StandardUnionInsertImportTemplateAction(
                arg: arg,
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade
        )

        when:
        serviceFacade.findObject(actionContext.getTenantId(), actionContext.getObjectApiName()) >> objectDescribe
        serviceFacade.findObject(actionContext.getTenantId(), detailApiName) >> detailDescribe
        serviceFacade.findObjects(actionContext.getTenantId(), [detailApiName]) >> [detailApiName: detailDescribe]
        action.before(arg)
        then:
        noExceptionThrown()
        where:
        detailArg                                | _
        new BaseImportTemplateAction.DetailArg() | _
        null                                     | _
    }


    def "test doAct"() {
        given:
        List<String> unionApiNames = [objectApiName, detailApiName]

        List<BaseImportTemplateAction.DetailArg> detailInfoList = Lists.newArrayList()
        if (Objects.nonNull(detailArg)) {
            detailArg.setDetailApiName(detailApiName)
            detailInfoList.add(detailArg)
        }

        BaseImportTemplateAction.Arg arg = new BaseImportTemplateAction.Arg(
                unionImportApiNameList: unionApiNames,
                detailArg: detailInfoList,
                importType: importType,
                matchingType: matchType
        )

        StandardUnionInsertImportTemplateAction action = new StandardUnionInsertImportTemplateAction(
                arg: arg,
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                masterDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                detailDescribeList: [detailDescribe]
        )

        when:
        infraServiceFacade.getTemplateField(_, _, _) >> fieldDescribeList
        infraServiceFacade.getUpdateImportTemplateField(_, _, _) >> fieldDescribeList
        serviceFacade.findValidRecordTypeList(objectDescribe.getApiName(), actionContext.getUser()) >> []
        action.doAct(arg)
        then:
        noExceptionThrown()
        where:
        detailArg                                | importType | matchType
        new BaseImportTemplateAction.DetailArg() | 0          | 1
        null                                     | 1          | 2
    }
}
