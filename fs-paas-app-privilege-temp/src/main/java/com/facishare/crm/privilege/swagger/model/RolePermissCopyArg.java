/**
 * Facishare PAAS Auth
 * This is PAAS auth service project
 *
 * OpenAPI spec version: v1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


package com.facishare.crm.privilege.swagger.model;

import java.util.Objects;
import com.facishare.crm.privilege.swagger.model.AuthContext;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * RolePermissCopyArg
 */
@javax.annotation.Generated(value = "class io.swagger.codegen.languages.JavaClientCodegen", date = "2017-05-16T15:01:04.279+08:00")
public class RolePermissCopyArg   {
  @SerializedName("authContext")
  private AuthContext authContext = null;

  @SerializedName("sourceRoleCode")
  private String sourceRoleCode = null;

  @SerializedName("destRoleCode")
  private String destRoleCode = null;

  public RolePermissCopyArg authContext(AuthContext authContext) {
    this.authContext = authContext;
    return this;
  }

   /**
   * Get authContext
   * @return authContext
  **/
  @ApiModelProperty(example = "null", value = "")
  public AuthContext getAuthContext() {
    return authContext;
  }

  public void setAuthContext(AuthContext authContext) {
    this.authContext = authContext;
  }

  public RolePermissCopyArg sourceRoleCode(String sourceRoleCode) {
    this.sourceRoleCode = sourceRoleCode;
    return this;
  }

   /**
   * Get sourceRoleCode
   * @return sourceRoleCode
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getSourceRoleCode() {
    return sourceRoleCode;
  }

  public void setSourceRoleCode(String sourceRoleCode) {
    this.sourceRoleCode = sourceRoleCode;
  }

  public RolePermissCopyArg destRoleCode(String destRoleCode) {
    this.destRoleCode = destRoleCode;
    return this;
  }

   /**
   * Get destRoleCode
   * @return destRoleCode
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getDestRoleCode() {
    return destRoleCode;
  }

  public void setDestRoleCode(String destRoleCode) {
    this.destRoleCode = destRoleCode;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RolePermissCopyArg rolePermissCopyArg = (RolePermissCopyArg) o;
    return Objects.equals(this.authContext, rolePermissCopyArg.authContext) &&
        Objects.equals(this.sourceRoleCode, rolePermissCopyArg.sourceRoleCode) &&
        Objects.equals(this.destRoleCode, rolePermissCopyArg.destRoleCode);
  }

  @Override
  public int hashCode() {
    return Objects.hash(authContext, sourceRoleCode, destRoleCode);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RolePermissCopyArg {\n");
    
    sb.append("    authContext: ").append(toIndentedString(authContext)).append("\n");
    sb.append("    sourceRoleCode: ").append(toIndentedString(sourceRoleCode)).append("\n");
    sb.append("    destRoleCode: ").append(toIndentedString(destRoleCode)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

