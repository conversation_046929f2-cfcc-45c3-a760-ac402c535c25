package com.facishare.paas.appframework.metadata


import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.Count
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.ObjectReference
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class AggregateRuleTest extends Specification {


    /**
     * GenerateByAI
     * 测试内容描述：测试AggregateRule的of静态工厂方法能够正确创建实例
     */
    def "ofTestValid"() {
        given:
        IObjectData data = new ObjectData([
            'aggregate_object': 'TestObj',
            'aggregate_field': 'amount',
            'aggregate_way': 'sum',
            'date_field': 'create_time'
        ])

        when:
        AggregateRule rule = AggregateRule.of(data)

        then:
        rule != null
        rule.getData() == data
        rule.getAggregateObject() == 'TestObj'
        rule.getAggregateField() == 'amount'
        rule.getAggregateWay() == 'sum'
        rule.getDateField() == 'create_time'
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of方法传入null参数的异常情况
     */
    def "ofErrorNull"() {
        when:
        def of = AggregateRule.of(null)
        of.getAggregateObject()
        then:
        thrown(NullPointerException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMaxOrMinWay静态方法能够正确判断最大值或最小值聚合方式
     */
    def "isMaxOrMinWayTestValid"() {
        when:
        boolean result = AggregateRule.isMaxOrMinWay(aggregateWay)

        then:
        result == expectedResult

        where:
        aggregateWay      | expectedResult
        Count.TYPE_MAX    | true
        Count.TYPE_MIN    | true
        Count.TYPE_SUM    | false
        Count.TYPE_COUNT  | false
        "avg"             | false
        "unknown"         | false
        null              | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isAvgWay静态方法能够正确判断平均值聚合方式
     */
    def "isAvgWayTestValid"() {
        when:
        boolean result = AggregateRule.isAvgWay(aggregateWay)

        then:
        result == expectedResult

        where:
        aggregateWay                       | expectedResult
        AggregateRule.AGGREGATE_WAY_AVG    | true
        Count.TYPE_SUM                     | false
        Count.TYPE_MAX                     | false
        Count.TYPE_MIN                     | false
        Count.TYPE_COUNT                   | false
        "unknown"                          | false
        null                               | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildAggFieldName静态方法能够正确构建聚合字段名
     */
    def "buildAggFieldNameTestValid"() {
        when:
        String result = AggregateRule.buildAggFieldName(aggFunction, fieldName)

        then:
        result == expectedResult

        where:
        aggFunction                        | fieldName | expectedResult
        Count.TYPE_COUNT                   | "amount"  | AggregateRule.COUNT_VALUE_KEY
        Count.TYPE_SUM                     | "amount"  | "sum_amount"
        Count.TYPE_MAX                     | "amount"  | "max_amount"
        Count.TYPE_MIN                     | "amount"  | "min_amount"
        AggregateRule.AGGREGATE_WAY_AVG    | "amount"  | "sum_amount"
        "custom"                           | "price"   | "custom_price"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试实例方法buildAggFieldName能够正确构建聚合字段名
     */
    def "buildAggFieldNameInstanceTestValid"() {
        given:
        IObjectData data = new ObjectData([
            'aggregate_field': fieldName,
            'aggregate_way': aggregateWay
        ])
        AggregateRule rule = AggregateRule.of(data)

        when:
        String result = rule.buildAggFieldName()

        then:
        result == expectedResult

        where:
        aggregateWay                       | fieldName | expectedResult
        Count.TYPE_COUNT                   | "amount"  | AggregateRule.COUNT_VALUE_KEY
        Count.TYPE_SUM                     | "amount"  | "sum_amount"
        AggregateRule.AGGREGATE_WAY_AVG    | "amount"  | "sum_amount"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMaxOrMinWay实例方法能够正确判断最大值或最小值聚合方式
     */
    def "isMaxOrMinWayInstanceTestValid"() {
        given:
        IObjectData data = new ObjectData(['aggregate_way': aggregateWay])
        AggregateRule rule = AggregateRule.of(data)

        when:
        boolean result = rule.isMaxOrMinWay()

        then:
        result == expectedResult

        where:
        aggregateWay      | expectedResult
        Count.TYPE_MAX    | true
        Count.TYPE_MIN    | true
        Count.TYPE_SUM    | false
        "unknown"         | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isAvgWay实例方法能够正确判断平均值聚合方式
     */
    def "isAvgWayInstanceTestValid"() {
        given:
        IObjectData data = new ObjectData(['aggregate_way': aggregateWay])
        AggregateRule rule = AggregateRule.of(data)

        when:
        boolean result = rule.isAvgWay()

        then:
        result == expectedResult

        where:
        aggregateWay                       | expectedResult
        AggregateRule.AGGREGATE_WAY_AVG    | true
        Count.TYPE_SUM                     | false
        "unknown"                          | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDateRangeWithTimeMillis方法在自定义日期范围时的正确性
     */
    def "getDateRangeWithTimeMillisTestCustomRange"() {
        given:
        def dateRange = [
            type: "custom",
            value: [1640966400000L, 1641052800000L] // 2022-01-01 to 2022-01-02
        ]
        IObjectData data = new ObjectData(['date_range': dateRange])
        AggregateRule rule = AggregateRule.of(data)

        when:
        List<Long> result = rule.getDateRangeWithTimeMillis()

        then:
        result != null
        result.size() >= 1
        result.contains(1640966400000L)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDateRangeWithTimeMillis方法在预定义日期范围时的正确性
     */
    def "getDateRangeWithTimeMillisTestPredefinedRange"() {
        given:
        def dateRange = [type: rangeType]
        IObjectData data = new ObjectData(['date_range': dateRange])
        AggregateRule rule = AggregateRule.of(data)

        when:
        List<Long> result = rule.getDateRangeWithTimeMillis()

        then:
        result != null

        where:
        rangeType << ["this_quarter", "last_quarter", "this_month", "last_month", "this_week", "last_week"]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDateRangeWithTimeMillis方法在没有日期范围时返回空列表
     */
    def "getDateRangeWithTimeMillisTestEmpty"() {
        given:
        IObjectData data = new ObjectData([:])
        AggregateRule rule = AggregateRule.of(data)

        when:
        List<Long> result = rule.getDateRangeWithTimeMillis()

        then:
        result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getCondition方法能够正确解析查询条件
     */
    def "getConditionTestValid"() {
        given:
        String conditionJson = '[{"field_name":"status","operator":"EQ","field_values":["1"]}]'
        IObjectData data = new ObjectData(['condition': conditionJson])
        AggregateRule rule = AggregateRule.of(data)

        when:
        List<Map<String, Object>> result = rule.getCondition()

        then:
        result != null
        result.size() == 1
        result.get(0).field_name == "status"
        result.get(0).operator == "EQ"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getCondition方法在没有条件时返回空列表
     */
    def "getConditionTestEmpty"() {
        given:
        IObjectData data = new ObjectData([:])
        AggregateRule rule = AggregateRule.of(data)

        when:
        List<Map<String, Object>> result = rule.getCondition()

        then:
        result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDimension方法能够正确获取维度信息
     */
    def "getDimensionTestValid"() {
        given:
        IObjectData data = new ObjectData(['dimension': dimension])
        AggregateRule rule = AggregateRule.of(data)

        when:
        String result = rule.getDimension()

        then:
        result == dimension

        where:
        dimension << ["simple_field", "lookup_field.target_field", null, ""]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isDimensionInLookup方法能够正确判断维度是否在关联对象中
     */
    def "isDimensionInLookupTestValid"() {
        given:
        IObjectData data = new ObjectData(['dimension': dimension])
        AggregateRule rule = AggregateRule.of(data)

        when:
        boolean result = rule.isDimensionInLookup()

        then:
        result == expectedResult

        where:
        dimension                  | expectedResult
        "simple_field"             | false
        "lookup_field.target_field"| true
        ""                         | false
        null                       | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试aggregateWayForCalculate方法能够正确转换聚合方式用于计算
     */
    def "aggregateWayForCalculateTestValid"() {
        given:
        IObjectData data = new ObjectData(['aggregate_way': aggregateWay])
        AggregateRule rule = AggregateRule.of(data)

        when:
        String result = rule.aggregateWayForCalculate()

        then:
        result == expectedResult

        where:
        aggregateWay                       | expectedResult
        AggregateRule.AGGREGATE_WAY_AVG    | Count.TYPE_SUM
        Count.TYPE_SUM                     | Count.TYPE_SUM
        Count.TYPE_MAX                     | Count.TYPE_MAX
        Count.TYPE_MIN                     | Count.TYPE_MIN
        Count.TYPE_COUNT                   | Count.TYPE_COUNT
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setCalculateStatus方法能够正确设置计算状态
     */
    def "setCalculateStatusTestValid"() {
        given:
        IObjectData data = new ObjectData([:])
        AggregateRule rule = AggregateRule.of(data)

        when:
        rule.setCalculateStatus(status)

        then:
        rule.get(AggregateRule.CALCULATE_STATUS) == status

        where:
        status << [
            AggregateRule.CalculateStatus.PROCESSING.getCode(),
            AggregateRule.CalculateStatus.COMPLETED.getCode(),
            "custom_status"
        ]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRelateFieldsMap方法能够正确构建关联字段映射
     */
    def "getRelateFieldsMapTestValid"() {
        given:
        IObjectData data = new ObjectData([
            'aggregate_object': 'TestObj',
            'aggregate_field': 'amount',
            'date_field': 'create_time',
            'dimension': 'status',
            'condition': '[]'
        ])
        AggregateRule rule = AggregateRule.of(data)
        
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName('TestObj')

        when:
        Map<String, Set<String>> result = rule.getRelateFieldsMap(describe)

        then:
        result != null
        result.containsKey('TestObj')
        result.get('TestObj').contains('create_time')
        result.get('TestObj').contains('amount')
        result.get('TestObj').contains('status')
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRelateFieldsMap方法处理关联字段的情况
     */
    def "getRelateFieldsMapTestWithLookup"() {
        given:
        IObjectData data = new ObjectData([
            'aggregate_object': 'TestObj',
            'aggregate_field': 'amount',
            'date_field': 'create_time',
            'dimension': 'account.name',
            'condition': '[]'
        ])
        AggregateRule rule = AggregateRule.of(data)
        
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName('TestObj')
        
        IFieldDescribe lookupField = FieldDescribeFactory.newInstance([
            "api_name": "account",
            "type": "object_reference"
        ])
        lookupField.set(ObjectReference.TARGET_API_NAME, "AccountObj")
        describe.setFieldDescribes([lookupField])

        when:
        Map<String, Set<String>> result = rule.getRelateFieldsMap(describe)

        then:
        result != null
        result.containsKey('TestObj')
        result.containsKey('AccountObj')
        result.get('TestObj').contains('account')
        result.get('AccountObj').contains('name')
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseDimension方法能够正确解析维度字段
     */
    @Unroll
    def "parseDimensionTestValid"() {
        given:
        IObjectData data = new ObjectData(['dimension': dimension])
        AggregateRule rule = AggregateRule.of(data)

        when:
        String[] result = rule.parseDimension()

        then:
        result == expectedResult

        where:
        dimension                  | expectedResult
        "simple_field"             | ["simple_field"] as String[]
        "lookup_field.target_field"| ["lookup_field", "target_field"] as String[]
        " "                         | [" "] as String[]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isDateFieldInRange方法能够正确判断日期是否在范围内
     */
    def "isDateFieldInRangeTestValid"() {
        given:
        def dateRange = [
            type: "custom",
            value: [1640966400000L, 1641052800000L] // 2022-01-01 to 2022-01-02
        ]
        IObjectData data = new ObjectData(['date_range': dateRange])
        AggregateRule rule = AggregateRule.of(data)

        when:
        boolean result = rule.isDateFieldInRange(value)

        then:
        result == expectedResult

        where:
        value              | expectedResult
        1640966400000L     | true   // 在范围内
        1641139200000L     | false  // 超出范围
        null               | false  // null值
        ""                 | false  // 空值
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isDateFieldInRange方法在没有日期范围时返回false
     */
    def "isDateFieldInRangeTestNoRange"() {
        given:
        IObjectData data = new ObjectData([:])
        AggregateRule rule = AggregateRule.of(data)

        when:
        boolean result = rule.isDateFieldInRange(1640966400000L)

        then:
        result == false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLookupFields方法能够正确获取关联对象的关联字段
     */
    def "getLookupFieldsTestValid"() {
        given:
        IObjectData data = new ObjectData([
            'dimension': 'account.name',
            'condition': '[{"filters":[{"field_name":"account.status","operator":"EQ","field_values":["1"]}]}]'
        ])
        AggregateRule rule = AggregateRule.of(data)
        
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName('TestObj')
        
        IFieldDescribe lookupField = FieldDescribeFactory.newInstance([
            "api_name": "account",
            "type": "object_reference"
        ])
        lookupField.set(ObjectReference.TARGET_API_NAME, "AccountObj")
        describe.setFieldDescribes([lookupField])

        when:
        Set<String> result = rule.getLookupFields(describe, "AccountObj")

        then:
        result != null
        result.contains("account")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试CalculateStatus枚举的正确性
     */
    def "calculateStatusEnumTestValid"() {
        when:
        String processingCode = AggregateRule.CalculateStatus.PROCESSING.getCode()
        String completedCode = AggregateRule.CalculateStatus.COMPLETED.getCode()

        then:
        processingCode == "0"
        completedCode == "1"
    }
} 