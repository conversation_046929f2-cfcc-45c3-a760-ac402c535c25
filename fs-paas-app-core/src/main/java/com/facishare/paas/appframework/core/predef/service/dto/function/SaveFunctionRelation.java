package com.facishare.paas.appframework.core.predef.service.dto.function;

import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.metadata.relation.TargetTypes;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;

import java.util.List;

public interface SaveFunctionRelation {

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        List<ReferenceInfo> items;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class ReferenceInfo {
        String type;
        String name;
        String value;
        String funcApiName;

        public static ReferenceInfo of(ReferenceData referenceData) {
            return new ReferenceInfo(referenceData.getSourceType(), referenceData.getSourceLabel(),
                    referenceData.getSourceValue(), referenceData.getTargetValue());
        }

        public ReferenceData toReferenceData() {
            return ReferenceData.builder()
                    .sourceType(getType())
                    .sourceLabel(getName())
                    .sourceValue(getValue())
                    .targetType(TargetTypes.FUNCTION)
                    .targetValue(getFuncApiName())
                    .build();
        }
    }

    @Data
    @Builder
    @Jacksonized
    class Result {
        Integer code;
        String message;
    }

    static ReferenceData of(ReferenceInfo info) {
        return info.toReferenceData();
    }
}
