package com.facishare.paas.appframework.core.predef.service.dto.changeorder;

import com.facishare.paas.appframework.metadata.changeorder.OpenChangeOrderResult;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/3/23
 */
public interface OpenChangeOrder {
    @Data
    class Arg {
        private String describeApiName;
    }

    @Data
    class Result {
        private boolean success;
        private OpenChangeOrderResult openChangeOrderResult;

        public static Result buildSuccess() {
            Result result = new Result();
            result.setSuccess(true);
            return result;
        }

        public static Result buildResult(OpenChangeOrderResult openChangeOrderResult) {
            Result result = new Result();
            boolean success = openChangeOrderResult.success();
            result.setSuccess(success);
            if (!success) {
                result.setOpenChangeOrderResult(openChangeOrderResult);
            }
            return result;
        }
    }
}
