package com.facishare.paas.appframework.core.model

import com.facishare.crm.predefine.CRMPreDefineObject
import com.facishare.crm.predefine.action.AccountAddAction
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction
import com.facishare.paas.appframework.core.predef.action.StandardAddAction
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import spock.lang.Specification

/**
 * Created by liyiguang on 2017/6/21.
 */
class ActionLocateServiceImplTest extends Specification {

    ActionLocateServiceImpl actionLocateService

    def setup() {
        actionLocateService = new ActionLocateServiceImpl()
        actionLocateService.setSerializerManager(new SerializerManagerImpl())
        actionLocateService.setActionClassLoader(new ActionClassLoaderImpl())

        CRMPreDefineObject.init()
    }

    def "test locateAction"() {
        expect:
        1 == 1
        /*
        given:
        def facadeService = Mock(ServiceFacade)
        facadeService.findObject(tenantId, apiName) >> object
        actionLocateService.setServiceFacade(facadeService)
        when:
        def action = actionLocateService.locateAction(context, payload)
        then:
        action == expectedAction
        where:
        tenantId | apiName       | object     | context    | payload                     || expectedAction
        "1"      | "CustomerObj" | objects1() | context1() | "{\"object_data\":\"abc\"}" || expectAction1()
        "2"      | "AccountObj"  | objects2() | context2() | "{\"object_data\":\"abc\"}" || expectAction2()
        */
    }

    def expectAction1() {
        def action = new StandardAddAction()
        def arg = new BaseObjectSaveAction.Arg()
        arg.setObject_data("abc")
        action.setArg(arg)
        return action
    }

    def context1() {
        def requestContext = new RequestContext()
        requestContext.setTenantId("1")
        requestContext.setUser(new User("1", "1000"))
        requestContext.setContentType(RequestContext.ContentType.FULL_JSON)
        def actionContext = new ActionContext(requestContext, "CustomerObj", "Add")
        return actionContext
    }

    def objects1() {
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("CustomerObj")
        return objectDescribe
    }


    def expectAction2() {
        def action = new AccountAddAction()
        def arg = new AccountAddAction.Arg()
        arg.setObject_data("abc")
        action.setArg(arg)
        return action
    }

    def context2() {
        def requestContext = new RequestContext()
        requestContext.setTenantId("2")
        requestContext.setUser(new User("2", "1000"))
        requestContext.setContentType(RequestContext.ContentType.FULL_JSON)
        def actionContext = new ActionContext(requestContext, "AccountObj", "Add")
        return actionContext
    }

    def objects2() {
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")
        return objectDescribe
    }

}
