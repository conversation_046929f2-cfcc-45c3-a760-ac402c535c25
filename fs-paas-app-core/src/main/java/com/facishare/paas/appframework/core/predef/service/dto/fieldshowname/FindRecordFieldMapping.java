package com.facishare.paas.appframework.core.predef.service.dto.fieldshowname;

import com.facishare.paas.appframework.metadata.dto.FieldRecordShowName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface FindRecordFieldMapping {

    @Data
    class Arg {
        private List<FieldRecordShowName> recordFieldList;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private Map<String, Object> fieldMapping;
    }
}
