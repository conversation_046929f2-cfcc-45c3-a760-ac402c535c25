package com.facishare.paas.appframework.core.predef.service.dto.brushData;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/12/30
 */
public interface MaskFieldEncrypt {
    @Data
    class Arg {
        private String describeApiName;
        private ObjectDataDocument objectData;

        /**
         * 需要掩码加密的字段
         */
        private Map<String, List<String>> maskFieldApiNames;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        private ObjectDataDocument objectData;
    }
}
