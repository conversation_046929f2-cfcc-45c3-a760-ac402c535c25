package com.facishare.paas.appframework.metadata.duplicated;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.DuplicatedSearchExt;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchDataInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.google.common.base.Strings;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import org.apache.commons.collections4.ListUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.DuplicatedSearchExt.DUPLICATED_SEARCH_DATA;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

/**
 * create by zhaoju on 2020/10/21
 */

public class DuplicatedSearchDataContainer {
    private Set<DuplicatedSearchContainer> containers = Sets.newHashSet();
    private IDuplicatedSearch duplicatedSearch;

    private final Table<String, String, DuplicatedSearchEntry> nodeTable = HashBasedTable.create();

    private DuplicatedSearchDataContainer(IDuplicatedSearch duplicatedSearch) {
        this.duplicatedSearch = duplicatedSearch;
    }

    public static DuplicatedSearchDataContainer of(IDuplicatedSearch duplicatedSearch,
                                                   Collection<IObjectData> dataCollection) {
        return of(duplicatedSearch, dataCollection, (container) -> Sets.newHashSet(container.getSourceId()));
    }

    public static DuplicatedSearchDataContainer of(IDuplicatedSearch duplicatedSearch,
                                                   Collection<IObjectData> dataCollection,
                                                   Function<DuplicatedSearchContainer, Collection<String>> function) {
        DuplicatedSearchDataContainer duplicatedSearchDataContainer = new DuplicatedSearchDataContainer(duplicatedSearch);
        duplicatedSearchDataContainer.buildAndInit(dataCollection, function);
        return duplicatedSearchDataContainer;
    }

    public List<DuplicateSearchDataInfo> searchDuplicatedData() {
        if (CollectionUtils.empty(containers)) {
            return Collections.emptyList();
        }

        Set<List<String>> ruleFieldNameByPrecise = DuplicatedSearchExt.of(duplicatedSearch).getRuleFieldNameByPrecise();
        List<DuplicateSearchDataInfo> result = containers.stream()
                .map(container -> {
                    Map<String, String> fieldNameSearchKeyMap = container.getFieldNameSearchKeyMap();
                    return ruleFieldNameByPrecise.stream()
                            .map(fieldNames -> buildSearchDataInfo(container, fieldNameSearchKeyMap, fieldNames))
                            .reduce(DuplicateSearchDataInfo::merge);
                }).filter(Optional::isPresent)
                .map(Optional::get)
                // 保留重复的结果
                .filter(DuplicateSearchDataInfo::isDuplicated)
                .collect(Collectors.toList());
        return result;
    }

    private DuplicateSearchDataInfo buildSearchDataInfo(DuplicatedSearchContainer container, Map<String, String> fieldNameSearchKeyMap, List<String> fieldNames) {
        List<String> objIds = fieldNameSearchKeyMap.entrySet().stream()
                .filter(entry -> fieldNames.contains(entry.getKey()))
                .map(entry -> nodeTable.get(entry.getKey(), entry.getValue()))
                .filter(Objects::nonNull)
                .map(it -> (List<String>) Lists.newArrayList(it.getObjIds()))
                // 合并逻辑，取交集
                .reduce(ListUtils::retainAll)
                .orElse(Collections.emptyList());
        return DuplicateSearchDataInfo.of(duplicatedSearch.getDescribeApiName(), container.sourceId, objIds);
    }

    private void buildAndInit(Collection<IObjectData> dataCollection, Function<DuplicatedSearchContainer, Collection<String>> function) {
        if (CollectionUtils.empty(dataCollection)) {
            return;
        }
        this.containers = dataCollection.stream()
                .map(objectData -> buildContainer(objectData, duplicatedSearch))
                .collect(toSet());
        for (DuplicatedSearchContainer container : containers) {
            Collection<String> ids = function.apply(container);
            container.duplicatedSearchEntries.forEach(it -> it.appendId(ids.toArray(new String[0])));
        }
    }

    private DuplicatedSearchEntry getDuplicatedSearchEntry(String fieldName, String searchKey) {
        DuplicatedSearchEntry searchEntry = nodeTable.get(fieldName, searchKey);
        if (Objects.isNull(searchEntry)) {
            searchEntry = DuplicatedSearchEntry.of(searchKey, fieldName);
            nodeTable.put(fieldName, searchKey, searchEntry);
        }
        return searchEntry;
    }

    private DuplicatedSearchContainer buildContainer(IObjectData objectData, IDuplicatedSearch duplicatedSearch) {
        Set<String> fieldNames = DuplicatedSearchExt.of(duplicatedSearch).getRuleFieldNameByPrecise().stream()
                .flatMap(Collection::stream).collect(toSet());
        Set<DuplicatedSearchEntry> duplicatedSearchEntries = fieldNames.stream()
                .map(fieldName -> {
                    // 值为空不参与查重
                    String searchKey = DuplicatedSearchExt.getSearchKey(objectData, duplicatedSearch.getTenantId(),
                            duplicatedSearch.getDescribeApiName(), fieldName, duplicatedSearch.getVersion());
                    if (Strings.isNullOrEmpty(searchKey)) {
                        return null;
                    }
                    // 维护同一份 DuplicatedSearchEntry
                    DuplicatedSearchEntry result = getDuplicatedSearchEntry(fieldName, searchKey);
                    return result;
                }).filter(Objects::nonNull).collect(toSet());

        return new DuplicatedSearchContainer(duplicatedSearchEntries, duplicatedSearch, objectData.getId());
    }

    @EqualsAndHashCode(of = {"duplicatedSearch", "sourceId"})
    @ToString
    public static class DuplicatedSearchContainer {
        @NonNull
        private final Set<DuplicatedSearchEntry> duplicatedSearchEntries;
        @NonNull
        private final IDuplicatedSearch duplicatedSearch;
        @Getter
        private final String sourceId;

        DuplicatedSearchContainer(Set<DuplicatedSearchEntry> duplicatedSearchEntries,
                                  IDuplicatedSearch duplicatedSearch, String sourceId) {
            this.duplicatedSearchEntries = duplicatedSearchEntries;
            this.duplicatedSearch = duplicatedSearch;
            this.sourceId = sourceId;
        }

        public Set<List<String>> getRuleFieldNameByPrecise() {
            return DuplicatedSearchExt.of(duplicatedSearch).getRuleFieldNameByPrecise();
        }

        public Map<String, String> getFieldNameSearchKeyMap() {
            return duplicatedSearchEntries.stream()
                    .collect(toMap(DuplicatedSearchEntry::getFieldName, DuplicatedSearchEntry::getSearchKey));
        }

        public String getRedisTempKey() {
            return String.format("%s_%s_%s", DUPLICATED_SEARCH_DATA, duplicatedSearch.getTenantId(),
                    duplicatedSearch.getDescribeApiName());
        }
    }

}
