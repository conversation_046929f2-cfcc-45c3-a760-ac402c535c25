package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.google.common.collect.Lists;
import org.jooq.lambda.tuple.Tuple;

import java.util.List;

import static com.facishare.paas.appframework.metadata.SearchTemplateExt.SceneType.TENANT_SCENE;

public class StandardEditOuterSceneAction extends BaseOuterSceneAction {
    @Override
    protected Result doAct(Arg arg) {
        IScene resultScene;
        if (Boolean.TRUE.equals(scene.getClearCustomConfig())) {
            api2Display2TypeForDefault = Lists.newArrayList();
        }
        if (TENANT_SCENE.getType().equals(scene.getType())) {
            resultScene = infraServiceFacade.updateTenantScene(scene, arg.getDescribeApiName(),
                    actionContext.getUser());
        } else {
            resultScene = infraServiceFacade.updateSystemScene(scene, actionContext.getUser());
            if (api2Display2TypeForDefault != null) {
                api2Display2TypeForDefault.add(Tuple.tuple(scene.getApiName(), scene.getDisplayName(), scene.getType()));
            }
        }
        extendSceneConfig();
        return new Result(resultScene);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }
}
