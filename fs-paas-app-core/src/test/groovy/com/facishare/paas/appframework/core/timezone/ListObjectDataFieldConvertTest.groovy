package com.facishare.paas.appframework.core.timezone

import com.facishare.paas.appframework.core.timezone.annotation.ObjectDataField
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import spock.lang.Specification

import java.util.function.Function

/**
 * GenerateByAI
 * 测试内容描述：ListObjectDataFieldConvert列表对象数据字段转换器的单元测试
 */
class ListObjectDataFieldConvertTest extends Specification {

    def converter = ListObjectDataFieldConvert.getInstance()
    def mockDescribe = Mock(IObjectDescribe)
    def mockFunction = Mock(Function)

    def setup() {
        mockFunction.apply(_) >> mockDescribe

        // Mock ObjectDataExt静态方法
        ObjectDataExt.metaClass.static.convertDateFieldValueToSystemZone = { IObjectDescribe describe, List dataList ->
            // 模拟系统时区转换
            return dataList.collect { data ->
                def result = new ObjectData()
                result.setDescribeApiName(data.getDescribeApiName())
                result.set("converted_to_system", true)
                result.set("id", data.get("id"))
                return result
            }
        }

        ObjectDataExt.metaClass.static.convertDateFieldValueToCustomZone = { IObjectDescribe describe, List dataList ->
            // 模拟自定义时区转换
            return dataList.collect { data ->
                def result = new ObjectData()
                result.setDescribeApiName(data.getDescribeApiName())
                result.set("converted_to_custom", true)
                result.set("id", data.get("id"))
                return result
            }
        }

        ObjectDataExt.metaClass.static.of = { Map map ->
            def objectData = new ObjectData()
            objectData.setDescribeApiName("TestApi")
            map.each { k, v -> objectData.set(k, v) }
            return objectData
        }
    }

    def cleanup() {
        // 清理元类修改
        ObjectDataExt.metaClass = null
    }

    def "test getInstance returns singleton"() {
        when: "多次获取实例"
        def instance1 = ListObjectDataFieldConvert.getInstance()
        def instance2 = ListObjectDataFieldConvert.getInstance()

        then: "应该返回相同的实例"
        instance1.is(instance2)
        instance1 == instance2
    }

    def "test getType returns LIST"() {
        when: "获取类型"
        def type = converter.getType()

        then: "应该返回LIST类型"
        type == ObjectDataField.Type.LIST
    }

    def "test convert2SystemZone with null value"() {
        when: "转换null值到系统时区"
        def result = converter.convert2SystemZone(null, mockFunction)

        then: "应该返回null"
        result == null
        0 * mockFunction.apply(_)
    }

    def "test convert2SystemZone with empty list"() {
        given: "准备空列表"
        def emptyList = []

        when: "转换空列表到系统时区"
        def result = converter.convert2SystemZone(emptyList, mockFunction)

        then: "应该返回原列表"
        result == emptyList
        0 * mockFunction.apply(_)
    }

    def "test convert2SystemZone with valid list"() {
        given: "准备有效的Map列表"
        def testList = [
            ["id": "1", "name": "item1"],
            ["id": "2", "name": "item2"]
        ]

        when: "转换列表到系统时区"
        def result = converter.convert2SystemZone(testList, mockFunction)

        then: "应该正确转换"
        result instanceof List
        result.size() == 2
        1 * mockFunction.apply("TestApi") >> mockDescribe
    }

    def "test convert2SystemZone with JSON string list"() {
        given: "准备JSON字符串列表"
        def jsonList = [
            '{"id":"1","name":"item1"}',
            '{"id":"2","name":"item2"}'
        ]

        when: "转换JSON字符串列表到系统时区"
        def result = converter.convert2SystemZone(jsonList, mockFunction)

        then: "应该正确转换"
        result instanceof List
        result.size() == 2
        1 * mockFunction.apply("TestApi") >> mockDescribe
    }

    def "test convert2CustomZone with null value"() {
        when: "转换null值到自定义时区"
        def result = converter.convert2CustomZone(null, mockFunction)

        then: "应该返回null"
        result == null
        0 * mockFunction.apply(_)
    }

    def "test convert2CustomZone with empty list"() {
        given: "准备空列表"
        def emptyList = []

        when: "转换空列表到自定义时区"
        def result = converter.convert2CustomZone(emptyList, mockFunction)

        then: "应该返回原列表"
        result == emptyList
        0 * mockFunction.apply(_)
    }

    def "test convert2CustomZone with valid list"() {
        given: "准备有效的Map列表"
        def testList = [
            ["id": "1", "name": "item1"],
            ["id": "2", "name": "item2"]
        ]

        when: "转换列表到自定义时区"
        def result = converter.convert2CustomZone(testList, mockFunction)

        then: "应该正确转换"
        result instanceof List
        result.size() == 2
        1 * mockFunction.apply("TestApi") >> mockDescribe
    }

    def "test convert2SystemZone with mixed content list"() {
        given: "准备混合内容列表"
        def mixedList = [
            ["id": "1", "name": "item1"],
            '{"id":"2","name":"item2"}'
        ]

        when: "转换混合列表到系统时区"
        def result = converter.convert2SystemZone(mixedList, mockFunction)

        then: "应该正确转换"
        result instanceof List
        result.size() == 2
        1 * mockFunction.apply("TestApi") >> mockDescribe
    }

    def "test convert2SystemZone uses first item's describe api name"() {
        given: "准备测试列表"
        def testList = [
            ["id": "1", "describeApiName": "FirstApi"],
            ["id": "2", "describeApiName": "SecondApi"]
        ]

        when: "转换列表"
        converter.convert2SystemZone(testList, mockFunction)

        then: "应该使用第一个元素的API名称"
        1 * mockFunction.apply("FirstApi") >> mockDescribe
    }

    def "test convert2CustomZone uses first item's describe api name"() {
        given: "准备测试列表"
        def testList = [
            ["id": "1", "describeApiName": "FirstApi"],
            ["id": "2", "describeApiName": "SecondApi"]
        ]

        when: "转换列表"
        converter.convert2CustomZone(testList, mockFunction)

        then: "应该使用第一个元素的API名称"
        1 * mockFunction.apply("FirstApi") >> mockDescribe
    }

    def "test convert2SystemZone with non-list throws exception"() {
        given: "准备非列表类型"
        def nonList = "not a list"

        when: "转换非列表类型"
        converter.convert2SystemZone(nonList, mockFunction)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test convert2CustomZone with non-list throws exception"() {
        given: "准备非列表类型"
        def nonList = "not a list"

        when: "转换非列表类型"
        converter.convert2CustomZone(nonList, mockFunction)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test convert2SystemZone with list containing unsupported types"() {
        given: "准备包含不支持类型的列表"
        def invalidList = [123, 456]

        when: "转换包含不支持类型的列表"
        converter.convert2SystemZone(invalidList, mockFunction)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test convert2CustomZone with list containing unsupported types"() {
        given: "准备包含不支持类型的列表"
        def invalidList = [123, 456]

        when: "转换包含不支持类型的列表"
        converter.convert2CustomZone(invalidList, mockFunction)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test convert2SystemZone preserves list type"() {
        given: "准备ArrayList"
        def arrayList = new ArrayList([["id": "1"]])

        when: "转换ArrayList"
        def result = converter.convert2SystemZone(arrayList, mockFunction)

        then: "应该保持List类型"
        result instanceof List
    }

    def "test convert2CustomZone preserves list type"() {
        given: "准备LinkedList"
        def linkedList = new LinkedList([["id": "1"]])

        when: "转换LinkedList"
        def result = converter.convert2CustomZone(linkedList, mockFunction)

        then: "应该保持List类型"
        result instanceof List
    }

    def "test conversion with large list"() {
        given: "准备大列表"
        def largeList = (1..1000).collect { i ->
            ["id": i.toString(), "name": "item$i"]
        }

        when: "转换大列表"
        def result = converter.convert2SystemZone(largeList, mockFunction)

        then: "应该正确处理大列表"
        result instanceof List
        result.size() == 1000
        1 * mockFunction.apply("TestApi") >> mockDescribe
    }

    def "test conversion with nested structures"() {
        given: "准备包含嵌套结构的列表"
        def nestedList = [
            [
                "id": "1",
                "nested": ["field1": "value1"],
                "list": ["item1", "item2"]
            ],
            [
                "id": "2",
                "nested": ["field2": "value2"],
                "list": ["item3", "item4"]
            ]
        ]

        when: "转换嵌套结构列表"
        def result = converter.convert2SystemZone(nestedList, mockFunction)

        then: "应该正确处理嵌套结构"
        result instanceof List
        result.size() == 2
        1 * mockFunction.apply("TestApi") >> mockDescribe
    }

    def "test thread safety of singleton"() {
        given: "准备并发测试"
        def instances = []

        when: "并发获取实例"
        def threads = (1..10).collect { i ->
            Thread.start {
                def instance = ListObjectDataFieldConvert.getInstance()
                synchronized (instances) {
                    instances << instance
                }
            }
        }
        threads.each { it.join() }

        then: "所有实例应该相同"
        instances.size() == 10
        instances.every { it.is(instances[0]) }
    }

    def "test conversion performance"() {
        given: "准备测试数据"
        def testList = [["id": "1"], ["id": "2"]]

        when: "多次转换"
        def startTime = System.currentTimeMillis()
        1000.times {
            converter.convert2SystemZone(testList, mockFunction)
        }
        def endTime = System.currentTimeMillis()

        then: "性能应该可接受"
        (endTime - startTime) < 5000 // 5秒内完成1000次转换
    }

    def "test inheritance from AbstractObjectDataFieldConvert"() {
        expect: "应该继承自AbstractObjectDataFieldConvert"
        converter instanceof AbstractObjectDataFieldConvert
        converter instanceof ObjectDataFieldConvert
    }

    def "test Helper inner class"() {
        expect: "Helper类应该正确实现单例模式"
        // 通过反射验证Helper类的存在
        def helperClass = ListObjectDataFieldConvert.getDeclaredClasses().find { it.simpleName == "Helper" }
        helperClass != null
        helperClass.getDeclaredFields().any { it.name == "INSTANCE" }
    }

    def "test convert with null function throws exception"() {
        given: "准备测试数据"
        def testList = [["id": "1"]]

        when: "使用null函数转换"
        converter.convert2SystemZone(testList, null)

        then: "应该抛出异常"
        thrown(Exception)
    }

    def "test convert with malformed JSON in list"() {
        given: "准备包含格式错误JSON的列表"
        def invalidJsonList = ['{"id":"1","name":']

        when: "转换包含格式错误JSON的列表"
        converter.convert2SystemZone(invalidJsonList, mockFunction)

        then: "应该抛出异常"
        thrown(Exception)
    }
}
