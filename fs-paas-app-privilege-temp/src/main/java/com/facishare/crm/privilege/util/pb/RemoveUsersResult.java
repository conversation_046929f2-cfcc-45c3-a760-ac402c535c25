package com.facishare.crm.privilege.util.pb;

import io.protostuff.Tag;

/**
 * Created by lei on 11/30/16.
 */
public class RemoveUsersResult {
    //    message RemoveUsersResult {
//        required int32 code = 1; //200成功，另外就是其他错误
//        required string msg = 2; //OK成功，其他则为失败
//    }
    @Tag(1)
    private int code;
    @Tag(2)
    private String msg;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    @Override
    public String toString() {
        return "RemoveUsersResult{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                '}';
    }
}
