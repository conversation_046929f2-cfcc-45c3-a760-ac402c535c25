package com.facishare.paas.appframework.common.service.dto;

import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/7/26
 */
public interface FindObjectPageComponentList {

    @Data
    class Arg {
        private String bizId;
        private String templeType;
    }

    @Data
    class Result {
        private List<ObjectPageComponent> objectPageComponents;
    }

    @Data
    class ObjectPageComponent {
        private String id;
        private String title;
        private String type;
        private String apiName;
        private String functionCode;
        private String nameI18nKey;

        private String configBizId;
    }
}
