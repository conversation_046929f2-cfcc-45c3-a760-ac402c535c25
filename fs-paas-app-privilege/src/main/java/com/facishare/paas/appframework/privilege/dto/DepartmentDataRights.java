package com.facishare.paas.appframework.privilege.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DepartmentDataRights {
    String id;
    String entityId;
    String deptId;
    long createdTime;
    long lastModifiedTime;
    String createdBy;
    String lastModifiedBy;
    boolean includeSub;
    Integer status;
    Integer scene;
    Integer type;
}
