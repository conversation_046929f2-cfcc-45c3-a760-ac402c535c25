package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

import static com.facishare.crm.openapi.Utils.REBATE_INCOME_DETAIL_API_NAME;

/**
 * 返利
 * create by <PERSON><PERSON><PERSON> on 2019/03/31
 */
@Component
public class RebateIncomeDetailObjectProvider extends DefaultObjectImportProvider {
    @Override
    public String getObjectCode() {
        return REBATE_INCOME_DETAIL_API_NAME;
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return ImportType.UNSUPPORT_UPDATE_IMPORT;
    }

    @Override
    protected int getDuplicateJudgmentType(IObjectDescribe objectDescribe) {
        return UNSUPPORT_UPDATE_IMPORT;
    }
}
