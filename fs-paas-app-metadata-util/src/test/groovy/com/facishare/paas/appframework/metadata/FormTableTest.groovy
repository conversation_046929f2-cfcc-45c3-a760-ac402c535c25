package com.facishare.paas.appframework.metadata

import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.ui.layout.IComponent
import com.facishare.paas.metadata.ui.layout.IFormField
import spock.lang.Specification
import spock.lang.Unroll

class FormTableTest extends Specification {

    def "test default constructor"() {
        when: "使用默认构造方法创建FormTable"
        def formTable = new FormTable()

        then: "应该正确初始化"
        formTable != null
        formTable.getType() == ComponentExt.TYPE_FORM_TABLE
    }

    def "test constructor with map"() {
        given: "一个包含数据的Map"
        def dataMap = [
                "rows": [
                        [
                                "cells": [
                                        [
                                                "components": [
                                                        [
                                                                "type"       : FormTable.TYPE_FIELD,
                                                                "field_name" : "test_field",
                                                                "render_type": "text"
                                                        ]
                                                ]
                                        ]
                                ]
                        ]
                ]
        ]

        when: "使用Map构造FormTable"
        def formTable = new FormTable(dataMap)

        then: "应该正确初始化数据"
        formTable != null
        formTable.getType() == ComponentExt.TYPE_FORM_TABLE
        formTable.getFieldNameList().contains("test_field")
    }

    def "test of factory method"() {
        given: "一个IComponent实现和ComponentExt Mock"
        def mockComponent = new FormTable()

        when: "使用of方法创建FormTable"
        def formTable = FormTable.of(mockComponent)

        then: "应该正确创建FormTable"
        formTable != null
        formTable.getType() == ComponentExt.TYPE_FORM_TABLE
    }

    def "test getFields with default parameter"() {
        given: "包含字段的FormTable"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "field1", render_type: "text"],
                [type: FormTable.TYPE_FIELD, field_name: "field2", render_type: "number"]
        ])

        when: "获取字段列表"
        def fields = formTable.getFields()

        then: "应该返回所有字段"
        fields.size() == 2
        fields.any { it.getFieldName() == "field1" }
        fields.any { it.getFieldName() == "field2" }
    }

    def "test getFields with includeFieldType parameter"() {
        given: "包含字段的FormTable"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "field1", render_type: "text"]
        ])

        when: "获取字段列表并排除类型"
        def fieldsWithoutType = formTable.getFields(false)
        def fieldsWithType = formTable.getFields(true)

        then: "应该根据参数返回不同结果"
        fieldsWithoutType.size() == 1
        fieldsWithType.size() == 1
        // 验证字段内容
        fieldsWithoutType[0].getFieldName() == "field1"
        fieldsWithType[0].getFieldName() == "field1"
    }

    def "test getFields with empty rows"() {
        given: "没有行数据的FormTable"
        def formTable = new FormTable()

        when: "获取字段列表"
        def fields = formTable.getFields()

        then: "应该返回空列表"
        fields.isEmpty()
    }

    def "test getFields with empty cells"() {
        given: "包含空cells的FormTable"
        def formTable = new FormTable([
                "rows": [
                        ["cells": []]
                ]
        ])

        when: "获取字段列表"
        def fields = formTable.getFields()

        then: "应该返回空列表"
        fields.isEmpty()
    }

    def "test getFields with empty components"() {
        given: "包含空components的FormTable"
        def formTable = new FormTable([
                "rows": [
                        [
                                "cells": [
                                        ["components": []]
                                ]
                        ]
                ]
        ])

        when: "获取字段列表"
        def fields = formTable.getFields()

        then: "应该返回空列表"
        fields.isEmpty()
    }

    def "test getFields filters non-field components"() {
        given: "包含不同类型组件的FormTable"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "field1", render_type: "text"],
                [type: "text_component", (IComponent.NAME): "text1"],
                [type: FormTable.TYPE_FIELD, field_name: "field2", render_type: "number"]
        ])

        when: "获取字段列表"
        def fields = formTable.getFields()

        then: "应该只返回字段类型的组件"
        fields.size() == 2
        fields.every { it instanceof IFormField }
    }

    def "test getComponentApiNameByType"() {
        given: "包含不同类型组件的FormTable"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, (IComponent.NAME): "field_component", field_name: "field1"],
                [type: "text_component", (IComponent.NAME): "text_component1"],
                [type: "text_component", (IComponent.NAME): "text_component2"],
                [type: "other_type", (IComponent.NAME): "other_component"]
        ])

        when: "根据类型获取组件API名"
        def textComponents = formTable.getComponentApiNameByType("text_component")
        def fieldComponents = formTable.getComponentApiNameByType(FormTable.TYPE_FIELD)

        then: "应该返回正确的组件名列表"
        textComponents.size() == 2
        textComponents.contains("text_component1")
        textComponents.contains("text_component2")
        fieldComponents.size() == 1
        fieldComponents.contains("field_component")
    }

    def "test getComponentApiNameByType with empty type"() {
        given: "包含组件的FormTable"
        def formTable = createFormTableWithFields([
                [type: "text_component", (IComponent.NAME): "text1"]
        ])

        when: "使用空类型获取组件"
        def result = formTable.getComponentApiNameByType("")

        then: "应该返回空列表"
        result.isEmpty()
    }

    def "test getComponentApiNameByType with null type"() {
        given: "包含组件的FormTable"
        def formTable = createFormTableWithFields([
                [type: "text_component", (IComponent.NAME): "text1"]
        ])

        when: "使用null类型获取组件"
        def result = formTable.getComponentApiNameByType(null)

        then: "应该返回空列表"
        result.isEmpty()
    }

    def "test getFieldNameList"() {
        given: "包含字段的FormTable"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "field1", render_type: "text"],
                [type: FormTable.TYPE_FIELD, field_name: "field2", render_type: "number"]
        ])

        when: "获取字段名列表"
        def fieldNames = formTable.getFieldNameList()

        then: "应该返回所有字段名"
        fieldNames.size() == 2
        fieldNames.contains("field1")
        fieldNames.contains("field2")
    }

    def "test getField when field exists"() {
        given: "包含字段的FormTable"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "target_field", render_type: "text"]
        ])

        when: "获取指定字段"
        def field = formTable.getField("target_field")

        then: "应该返回该字段"
        field.isPresent()
        field.get().getFieldName() == "target_field"
    }

    def "test getField when field not exists"() {
        given: "包含字段的FormTable"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "other_field", render_type: "text"]
        ])

        when: "获取不存在的字段"
        def field = formTable.getField("target_field")

        then: "应该返回空Optional"
        !field.isPresent()
    }

    def "test containsField"() {
        given: "包含字段的FormTable"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "existing_field", render_type: "text"]
        ])

        expect: "应该正确判断字段是否存在"
        formTable.containsField("existing_field") == true
        formTable.containsField("non_existing_field") == false
    }

    def "test hideFields"() {
        given: "包含字段的FormTable"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "required_field", render_type: "text", is_required: true, is_readonly: false],
                [type: FormTable.TYPE_FIELD, field_name: IObjectData.OWNER, render_type: "text", is_required: true, is_readonly: false],
                [type: FormTable.TYPE_FIELD, field_name: "normal_field", render_type: "text", is_required: false, is_readonly: false]
        ])

        when: "隐藏字段"
        formTable.hideFields(["required_field", IObjectData.OWNER, "normal_field"] as Set)

        then: "应该保留必填字段但移除owner字段"
        def remainingFields = formTable.getFieldNameList()
        remainingFields.contains("required_field") // 必填字段应该保留
        !remainingFields.contains(IObjectData.OWNER) // owner字段应该被移除
        !remainingFields.contains("normal_field") // 普通字段应该被移除
    }

    def "test removeFields"() {
        given: "包含字段的FormTable"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "field1", render_type: "text"],
                [type: FormTable.TYPE_FIELD, field_name: "field2", render_type: "text"],
                [type: FormTable.TYPE_FIELD, field_name: "field3", render_type: "text"]
        ])

        when: "移除指定字段"
        formTable.removeFields(["field1", "field3"] as Set)

        then: "应该只保留未被移除的字段"
        def remainingFields = formTable.getFieldNameList()
        remainingFields.size() == 1
        remainingFields.contains("field2")
        !remainingFields.contains("field1")
        !remainingFields.contains("field3")
    }

    def "test removeFieldByTypes"() {
        given: "包含不同渲染类型字段的FormTable"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "text_field", render_type: "text"],
                [type: FormTable.TYPE_FIELD, field_name: "number_field", render_type: "number"],
                [type: FormTable.TYPE_FIELD, field_name: "date_field", render_type: "date"]
        ])

        when: "根据渲染类型移除字段"
        formTable.removeFieldByTypes("text", "number")

        then: "应该移除指定类型的字段"
        def remainingFields = formTable.getFieldNameList()
        remainingFields.size() == 1
        remainingFields.contains("date_field")
        !remainingFields.contains("text_field")
        !remainingFields.contains("number_field")
    }

    def "test removeFieldByTypesExceptApiNameList"() {
        given: "包含字段的FormTable"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "field1", render_type: "text"],
                [type: FormTable.TYPE_FIELD, field_name: "field2", render_type: "text"],
                [type: FormTable.TYPE_FIELD, field_name: "field3", render_type: "number"]
        ])

        when: "根据类型移除字段但排除指定字段"
        formTable.removeFieldByTypesExceptApiNameList(["field1"], "text")

        then: "应该保留排除列表中的字段"
        def remainingFields = formTable.getFieldNameList()
        remainingFields.contains("field1") // 被排除，应该保留
        remainingFields.contains("field3") // 不是text类型，应该保留
        !remainingFields.contains("field2") // text类型且不在排除列表，应该被移除
    }

    def "test removeFieldByTypes with ObjectDescribeExt"() {
        given: "FormTable和对象描述"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "text_field", render_type: "text"],
                [type: FormTable.TYPE_FIELD, field_name: "number_field", render_type: "number"]
        ])

        def textField = Mock(IFieldDescribe) {
            getType() >> "text"
        }
        def numberField = Mock(IFieldDescribe) {
            getType() >> "number"
        }
        def describe = Mock(ObjectDescribeExt) {
            getFieldDescribeSilently("text_field") >> Optional.of(textField)
            getFieldDescribeSilently("number_field") >> Optional.of(numberField)
        }

        when: "根据对象描述类型移除字段"
        formTable.removeFieldByTypes(describe, "text")

        then: "应该移除指定类型的字段"
        def remainingFields = formTable.getFieldNameList()
        remainingFields.size() == 1
        remainingFields.contains("number_field")
        !remainingFields.contains("text_field")
    }

    def "test adjustFieldRenderType"() {
        given: "包含字段的FormTable"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "test_field", render_type: "old_type", is_required: false, is_readonly: false]
        ])

        when: "调整字段渲染类型"
        formTable.adjustFieldRenderType("test_object")

        then: "字段应该存在"
        def field = formTable.getField("test_field")
        field.isPresent()
        field.get().getFieldName() == "test_field"
        // 注意：由于LayoutExt.getRenderType是静态方法且没有Mock，实际的render_type可能不会改变
        // 这里只验证方法调用不会抛出异常
    }

    def "test setRequired"() {
        given: "FormTable和对象描述"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "test_field", render_type: "text", is_required: false, is_readonly: false]
        ])

        def requiredField = Mock(IFieldDescribe) {
            isRequired() >> true
        }
        def describe = Mock(ObjectDescribeExt) {
            getFieldDescribeSilently("test_field") >> Optional.of(requiredField)
        }

        when: "设置必填属性"
        formTable.setRequired(describe)

        then: "应该设置字段为必填"
        def field = formTable.getField("test_field")
        field.isPresent()
        field.get().isRequired() == true
    }

    def "test setReadOnly"() {
        given: "包含字段的FormTable"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "field1", render_type: "text", is_required: false, is_readonly: false],
                [type: FormTable.TYPE_FIELD, field_name: "field2", render_type: "text", is_required: false, is_readonly: false]
        ])

        when: "设置只读属性"
        formTable.setReadOnly(["field1"] as Set, true)

        then: "应该只设置指定字段为只读"
        def field1 = formTable.getField("field1")
        def field2 = formTable.getField("field2")
        field1.isPresent()
        field2.isPresent()
        field1.get().isReadOnly() == true
        field2.get().isReadOnly() == false
    }

    def "test onFieldDelete"() {
        given: "包含字段的FormTable"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "field_to_delete", render_type: "text"],
                [type: FormTable.TYPE_FIELD, field_name: "field_to_keep", render_type: "text"]
        ])

        def deletedField = Mock(IFieldDescribe) {
            getApiName() >> "field_to_delete"
        }

        when: "字段删除回调"
        formTable.onFieldDelete(deletedField)

        then: "应该移除被删除的字段"
        def remainingFields = formTable.getFieldNameList()
        remainingFields.size() == 1
        remainingFields.contains("field_to_keep")
        !remainingFields.contains("field_to_delete")
    }

    def "test onFieldUpdate"() {
        given: "包含字段的FormTable"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "test_field", render_type: "text", is_required: false, is_readonly: false]
        ])

        def updateFieldData = [
                is_required    : true,
                is_readonly: true
        ]

        when: "字段更新回调"
        formTable.onFieldUpdate("test_field", updateFieldData)

        then: "应该更新字段属性"
        def field = formTable.getField("test_field")
        field.isPresent()
        // 由于onFieldUpdate的实现可能比较复杂，这里只验证方法调用不会抛出异常
        field.get().getFieldName() == "test_field"
    }

    def "test removeDuplicatedFormFieldByFormTableComponent"() {
        given: "包含重复字段的FormTable"
        def formTable = new FormTable([
                "rows": [
                        [
                                "cells": [
                                        [
                                                "components": [
                                                        [type: FormTable.TYPE_FIELD, field_name: "field1", render_type: "text"],
                                                        [type: FormTable.TYPE_FIELD, field_name: "field1", render_type: "text"], // 重复字段
                                                        [type: FormTable.TYPE_FIELD, field_name: "field2", render_type: "text"],
                                                        [type: "text_component", (IComponent.NAME): "text1"] // 非字段组件
                                                ]
                                        ]
                                ]
                        ]
                ]
        ])

        when: "移除重复字段"
        formTable.removeDuplicatedFormFieldByFormTableComponent([] as Set)

        then: "应该只保留第一个出现的字段"
        def remainingFields = formTable.getFieldNameList()
        remainingFields.count { it == "field1" } == 1 // 只有一个field1
        remainingFields.contains("field2")

        // 验证非字段组件被保留 - 应该检查rows中的原始数据
        def rows = formTable.getRows()
        def components = rows[0].cells[0].components
        def nonFieldComponents = components.findAll { it.type != FormTable.TYPE_FIELD }
        nonFieldComponents.size() == 1
        nonFieldComponents[0][(IComponent.NAME)] == "text1"
    }

    def "test getTextComponentApiNameList"() {
        given: "包含文本组件的FormTable"
        def formTable = createFormTableWithFields([
                [type: ComponentExt.TYPE_TEXT_COMPONENT, api_name: "text1"],
                [type: ComponentExt.TYPE_TEXT_COMPONENT, api_name: "text2"],
                [type: FormTable.TYPE_FIELD, field_name: "field1"]
        ])

        when: "获取文本组件API名列表"
        def textComponentNames = formTable.getTextComponentApiNameList()

        then: "应该返回所有文本组件的API名"
        textComponentNames.size() == 2
        textComponentNames.contains("text1")
        textComponentNames.contains("text2")
    }

    def "test getRows when rows exist"() {
        given: "包含行数据的FormTable"
        def rowsData = [
                ["cells": [["components": []]]],
                ["cells": [["components": []]]]
        ]
        def formTable = new FormTable(["rows": rowsData])

        when: "获取行数据"
        def rows = formTable.getRows()

        then: "应该返回行数据"
        rows.size() == 2
        rows == rowsData
    }

    def "test getRows when rows empty"() {
        given: "没有行数据的FormTable"
        def formTable = new FormTable()

        when: "获取行数据"
        def rows = formTable.getRows()

        then: "应该返回空列表"
        rows.isEmpty()
    }

    def "test setRows"() {
        given: "FormTable"
        def formTable = new FormTable()
        def newRows = [
                ["cells": [["components": []]]]
        ]

        when: "设置行数据"
        formTable.setRows(newRows)

        then: "应该更新行数据"
        formTable.getRows() == newRows
    }

    @Unroll
    def "test removeFields with empty collections - #scenario"() {
        given: "包含字段的FormTable"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "field1", render_type: "text"]
        ])

        when: "使用空集合移除字段"
        formTable.removeFields(removeSet)

        then: "应该不移除任何字段"
        formTable.getFieldNameList().contains("field1")

        where:
        scenario    | removeSet
        "empty set" | [] as Set
        "null set"  | null as Set
    }

    def "test hideFields with empty removeFieldNames"() {
        given: "包含字段的FormTable"
        def formTable = createFormTableWithFields([
                [type: FormTable.TYPE_FIELD, field_name: "field1", render_type: "text"]
        ])

        when: "使用空集合隐藏字段"
        formTable.hideFields([] as Set)

        then: "应该不移除任何字段"
        formTable.getFieldNameList().contains("field1")
    }

    private FormTable createFormTableWithFields(List<Map> componentsList) {
        def rows = [
                [
                        "cells": [
                                [
                                        "components": componentsList
                                ]
                        ]
                ]
        ]
        return new FormTable(["rows": rows])
    }
} 