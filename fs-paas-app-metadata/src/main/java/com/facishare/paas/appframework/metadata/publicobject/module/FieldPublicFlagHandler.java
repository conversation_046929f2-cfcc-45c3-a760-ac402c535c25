package com.facishare.paas.appframework.metadata.publicobject.module;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.publicobject.PublicObjectGrayConfig;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2025/2/19
 */
@Component
public class FieldPublicFlagHandler {

    /**
     * 设置新字段的公开标记
     *
     * @param user        用户信息
     * @param describeExt 对象描述扩展
     * @param consumer    处理回调
     */
    public void setupNewFieldsPublicFlag(User user, ObjectDescribeExt describeExt, Consumer<ObjectDescribeExt> consumer) {
        if (!describeExt.isPublicObject() || !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SET_FIELD_PUBLIC_FLAG_BEFORE_ADD, describeExt.getUpstreamTenantId())) {
            consumer.accept(describeExt);
            return;
        }
        Set<String> fieldNames = describeExt.stream()
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toSet());
        consumer.accept(describeExt);
        Set<String> newFieldNames = describeExt.stream()
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toSet());
        newFieldNames.removeAll(fieldNames);
        if (CollectionUtils.empty(newFieldNames)) {
            return;
        }
        List<IFieldDescribe> newFields = describeExt.getFieldByApiNames(newFieldNames);
        for (IFieldDescribe fieldDescribe : newFields) {
            String tenantId = describeExt.isDownstreamTenantWithPublicObject() ? describeExt.getUpstreamTenantId() : describeExt.getTenantId();
            PublicFieldType publicFieldType = PublicObjectGrayConfig.getPublicFieldType(describeExt.getApiName(),
                            fieldDescribe.getApiName(), fieldDescribe.getType(), tenantId)
                    .orElseGet(() -> FieldDescribeExt.of(fieldDescribe).isCustomField() ? PublicFieldType.PRIVATE_FIELD : PublicFieldType.PUBLIC_FIELD_PUBLIC_DATA);
            fieldDescribe.setBelongTenant(publicFieldType.belongTenant(user.getTenantId()));
        }
    }

    public void initNewFieldPublicFieldFlag(User user, IObjectDescribe describe, IObjectDescribe describeInDB) {
        if (!describe.isPublicObject() || Objects.isNull(describeInDB)) {
            return;
        }
        List<IFieldDescribe> fieldDescribes = ObjectDescribeExt.of(describe).stream()
                .filter(it -> shouldSetupPublicFlag(describeInDB, it))
                .collect(Collectors.toList());
        String tenantId = ObjectDescribeExt.of(describe).isDownstreamTenantWithPublicObject() ? describe.getUpstreamTenantId() : describe.getTenantId();
        for (IFieldDescribe fieldDescribe : fieldDescribes) {
            boolean customField = FieldDescribeExt.of(fieldDescribe).isCustomField();
            if (customField && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FORCE_CUSTOM_FIELD_TO_PRIVATE, tenantId)) {
                fieldDescribe.setBelongTenant(PublicFieldType.PRIVATE_FIELD.belongTenant(user.getTenantId()));
                continue;
            }
            PublicFieldType publicFieldType = PublicObjectGrayConfig.getPublicFieldType(describe.getApiName(),
                            fieldDescribe.getApiName(), fieldDescribe.getType(), tenantId)
                    .orElseGet(() -> customField ? PublicFieldType.PRIVATE_FIELD : PublicFieldType.PUBLIC_FIELD_PUBLIC_DATA);
            fieldDescribe.setBelongTenant(publicFieldType.belongTenant(user.getTenantId()));
        }
    }

    private boolean shouldSetupPublicFlag(IObjectDescribe describeInDB, IFieldDescribe fieldDescribe) {
        return Strings.isNullOrEmpty(fieldDescribe.getId())
                && Strings.isNullOrEmpty(fieldDescribe.getBelongTenant())
                && !describeInDB.containsField(fieldDescribe.getApiName());
    }
}
