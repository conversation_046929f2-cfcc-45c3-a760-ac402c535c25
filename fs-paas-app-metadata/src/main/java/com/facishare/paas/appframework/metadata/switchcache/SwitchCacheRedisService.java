package com.facishare.paas.appframework.metadata.switchcache;

import java.util.Collection;
import java.util.Optional;
import java.util.function.BooleanSupplier;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/04/16
 */
public interface SwitchCacheRedisService {
    boolean cacheExit(String tenantId, String switchType, String objectApiName);

    Optional<Boolean> getIfPresent(String tenantId, String switchType, String switchName, String objectApiName);

    boolean get(String tenantId, String switchType, String switchName, String objectApiName, BooleanSupplier supplier);

    void put(String tenantId, String switchType, String switchName, String objectApiName, boolean switchStatus);

    void put(SwitchInfo switchInfo);

    void putAll(Collection<SwitchInfo> switchInfos);

    void delete(String tenantId, String switchType, String objectApiName);
}
