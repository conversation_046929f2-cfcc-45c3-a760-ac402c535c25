package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface CheckCountLimit {
    @Data
    class Arg {
        @JSONField(name = "M2")
        String objectDescribeApiName;
        @JSONField(name = "M1")
        String checkType;
    }
    @Data
    class Result {
        @JSONField(name = "M1")
        String value;
    }
}
