package com.facishare.paas.appframework.core.predef.service.dto.domain;

import com.facishare.paas.appframework.metadata.repository.model.DomainPluginInstance;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by zhouwr on 2022/9/20.
 */
public interface FindPluginInstanceDetail {
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        private String id;
        private String pluginApiName;
        private String objectApiName;
        private String filedApiName;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Result {
        private DomainPluginInstance pluginInstance;
    }
}
