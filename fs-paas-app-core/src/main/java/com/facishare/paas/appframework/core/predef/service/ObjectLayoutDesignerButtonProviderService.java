package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.metadata.LayoutButtonExt;
import com.facishare.paas.appframework.metadata.button.GetLayoutDesignerButton;
import com.facishare.paas.appframework.metadata.button.LayoutDesignerButtonManager;
import com.facishare.paas.appframework.metadata.button.LayoutDesignerButtonProvider;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
@ServiceModule("LayoutDesignerButtonProvider")
public class ObjectLayoutDesignerButtonProviderService {
    @Autowired
    private LayoutDesignerButtonManager layoutDesignerButtonManager;

    @ServiceMethod("getButtons")
    public GetLayoutDesignerButton.Result getLayoutDesignerButton(GetLayoutDesignerButton.Arg arg, ServiceContext context) {
        LayoutDesignerButtonProvider provider = layoutDesignerButtonManager.getLocalProvider(arg.getDescribeApiName());
        List<IButton> buttonsList = CollectionUtils.nullToEmpty(arg.getButtonList())
                .stream()
                .map(Button::new)
                .collect(Collectors.toList());
        List<IButton> resultButtons = provider.getButtons(buttonsList, context.getUser());
        List<Map> buttonDocumentList = CollectionUtils.nullToEmpty(resultButtons)
                .stream()
                .map(x -> LayoutButtonExt.of(x).toMap())
                .collect(Collectors.toList());

        return GetLayoutDesignerButton.Result.builder().result(buttonDocumentList).build();
    }
}
