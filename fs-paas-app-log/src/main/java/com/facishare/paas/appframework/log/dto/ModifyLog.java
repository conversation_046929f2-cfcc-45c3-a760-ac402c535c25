package com.facishare.paas.appframework.log.dto;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

public interface ModifyLog {
    @Data
    class Arg {
        private AuditLogInfo condition;
        private int pageSize;
    }

    @Data
    class Result {
        private List<ModifyLogInfo> modifyLogInfos = Lists.newArrayList();
        private int totalCount;
        private int pageSize;
        private Boolean hasMore;
    }

    @Data
    class ModifyLogInfo {
        private List<LogInfo.DiffObjectData> objectData;
        private String dataId;
        private String objectApiName;
        private String logId;
        private String bizOperationName;
        private List<Object> searchAfter;
        private Long operationTime;
    }
}
