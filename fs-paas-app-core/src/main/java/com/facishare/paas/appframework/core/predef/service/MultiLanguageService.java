package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.service.dto.language.Edit;
import com.facishare.paas.appframework.core.predef.service.dto.language.GetList;
import com.facishare.paas.appframework.core.predef.service.dto.language.LanguageInfo;
import com.facishare.paas.appframework.core.predef.service.dto.language.Update;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.objects.ObjectListConfig;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Lists;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

@ServiceModule("language")
@Slf4j
@Service
public class MultiLanguageService {

    @Autowired
    private ServiceFacade serviceFacade;
    /**
     * 国际化平台key的tag， 老对象、自定义对象、字段
     */
    private static final String TAG_PRE = "pre_object";
    private static final String TAG_CUSTOM = "custom_object";
    private static final String TAG_METADATA_OPTIONS = "metadata_options";
    @Autowired
    private ObjectDescribeServiceImpl objectDescribeService;

    /**
     * 暂时需要过滤某些对象
     */
    private List<String> toBeFilteringList = Arrays.asList("ApprovalTaskObj", "ApprovalInstanceObj", "VisitingObj");

    @PostConstruct
    public void init() {
        I18nClient.getInstance().realTime(true);
        I18nClient.getInstance().initWithTags(TAG_PRE, TAG_CUSTOM, TAG_METADATA_OPTIONS);
    }

    /**
     * 初始化标签页列表
     */
    @ServiceMethod("list")
    public GetList.Result getObjectList(GetList.Arg arg, ServiceContext context) {
        //请求语言环境
        String local = context.getRequestContext().getLang().getValue();
        //查询该租户下所有对象描述
//        List<IObjectDescribe> describes =  serviceFacade.findObjectsByTenantId(
//                context.getTenantId(), false, false, true, false);

        List<IObjectDescribe> describes = serviceFacade.findObjectsByTenantId(context.getTenantId(),
                false, false, false, false, ObjectListConfig.I18N);
        //过滤对象
        describes.removeIf(d -> toBeFilteringList.contains(d.getApiName()));

        Map<String, IObjectDescribe> keyMap = new LinkedHashMap<>();
        //老对象keys
        List<String> preKeys = Lists.newArrayList();
        for (IObjectDescribe d : describes) {
            keyMap.put(GetI18nKeyUtil.getDescribeDisplayNameKey(d.getApiName()), d);
            //老对象keys
            if (ObjectDescribeExt.isSFAObject(d.getApiName()) || ObjectDescribeExt.isPredefineCustomObject(d.getApiName())) {
                preKeys.add(GetI18nKeyUtil.getDescribeDisplayNameKey(d.getApiName()));
            }
        }
        List<String> keys = Lists.newArrayList(keyMap.keySet());
        //根据对象key， 批量获取对象显示名称的中英繁
        Map<String, Localization> multiLanguageMap = I18nClient.getInstance().get(keys, Long.parseLong(context.getTenantId()));

        //从国际化平台查询老对象的预制名称
        Map<String, Localization> preDefineMap = I18nClient.getInstance().getCommon(preKeys);

        //组装结果
        List<LanguageInfo> infoList = Lists.newArrayList();
        keyMap.forEach((key, describe) -> {
            Localization loc = multiLanguageMap.get(key);
            String simple = null;
            String tradition = null;
            String english = null;
            if (Objects.nonNull(loc)) {
                simple = getZhCN(loc);
                tradition = getZhTW(loc);
                english = getEn(loc);
            }
            //如果国际化平台三种语言都为空，则把simple设置为该对象名称
            if (Objects.isNull(simple) && Objects.isNull(tradition) && Objects.isNull(english)) {
                simple = describe.getDisplayName();
            }
            //获取label
            String label = describe.getDisplayName();
            if (ObjectDescribeExt.isSFAObject(describe.getApiName())) {
                label = getLabel(key, local, preDefineMap);
            }
            LanguageInfo info = LanguageInfo.builder().apiName(describe.getApiName())
                    .label(label)
                    .defineType(describe.getDefineType())
                    .simpleChinese(simple)
                    .english(english)
                    .traditional(tradition).build();
            // 老对象需要返回预制名称
            if (ObjectDescribeExt.isSFAObject(describe.getApiName())) {
                Localization lc = preDefineMap.get(key);
                if (Objects.nonNull(lc)) {
                    info.setPreSimpleChinese(getZhCN(preDefineMap.get(key)));
                    info.setPreEnglish(getEn(preDefineMap.get(key)));
                    info.setPreTraditional(getZhTW(preDefineMap.get(key)));
                }
            }
            infoList.add(info);
        });

        return GetList.Result.builder().describes(infoList).build();
    }

    private String getEn(Localization loc) {
        return loc.get(Lang.en.getValue());
    }

    private String getZhTW(Localization loc) {
        return loc.get(Lang.zh_TW.getValue());
    }

    private String getZhCN(Localization loc) {
        return loc.get(Lang.zh_CN.getValue());
    }

    /**
     * @param arg
     * @param serviceContext
     * @return
     */
    @ServiceMethod("edit")
    public Edit.Result edit(Edit.Arg arg, ServiceContext serviceContext) {
        //请求语言环境
        String local = serviceContext.getRequestContext().getLang().getValue();
        /* 获取对象描述的语言配置信息 */
        IObjectDescribe describe = serviceFacade.findObject(serviceContext.getTenantId(), arg.getDescribeApiName());
        Localization localization = I18nClient.getInstance().get(GetI18nKeyUtil.getDescribeDisplayNameKey(
                        arg.getDescribeApiName()),
                Long.parseLong(serviceContext.getTenantId()));

        String objectChinese = null;
        String objectEnglish = null;
        String objectTradition = null;
        if (Objects.nonNull(localization)) {
            objectChinese = getZhCN(localization);
            objectEnglish = getEn(localization);
            objectTradition = getZhTW(localization);
        }
        //如果国际化平台三种语言都为空，则把simple设置为该对象名称
        if (Objects.isNull(objectChinese) && Objects.isNull(objectEnglish) && Objects.isNull(objectTradition)) {
            log.warn("invoke i18nClient error, localization is empty, tenantId:{}, userId:{}, describe:{}",
                    serviceContext.getTenantId(), serviceContext.getUser().getUserId(), describe);
            objectChinese = describe.getDisplayName();
        }
        String preObjectChinese = null;
        String preObjectEnglish = null;
        String preObjectTradition = null;
        String label = describe.getDisplayName();
        if (ObjectDescribeExt.isSFAObject(arg.getDescribeApiName()) || ObjectDescribeExt.isPredefineCustomObject(
                arg.getDescribeApiName())) {
            Localization lc = I18nClient.getInstance().getCommon(GetI18nKeyUtil
                    .getDescribeDisplayNameKey(arg.getDescribeApiName()));
            if (Objects.nonNull(lc)) {
                preObjectChinese = getZhCN(lc);
                preObjectEnglish = getEn(lc);
                preObjectTradition = getZhTW(lc);
                if (ObjectDescribeExt.isSFAObject(arg.getDescribeApiName())) {
                    label = getLabelByPrior(lc, local);
                }
            }
        }
        LanguageInfo describeInfo = LanguageInfo.builder().apiName(arg.getDescribeApiName())
                .sfaObject(ifPredefineDescribe(describe))
                .label(label)
                .defineType(describe.getDefineType())
                .simpleChinese(objectChinese)
                .english(objectEnglish)
                .traditional(objectTradition)
                .preSimpleChinese(preObjectChinese)
                .preEnglish(preObjectEnglish)
                .preTraditional(preObjectTradition).build();

        /* 获取字段 */
        List<IFieldDescribe> fields = describe.getFieldDescribes();
        Map<String, IFieldDescribe> keyMap = new LinkedHashMap<>();
        List<String> preFieldKeys = Lists.newArrayList();
        for (IFieldDescribe field : fields) {
            String key = GetI18nKeyUtil.getFieldLabelKey(arg.getDescribeApiName(), field.getApiName());
            keyMap.put(key, field);
            if (!IFieldDescribe.DEFINE_TYPE_CUSTOM.equals(field.getDefineType())) {
                preFieldKeys.add(key);
            }
        }
        List<String> keyList = Lists.newArrayList(keyMap.keySet());
        Map<String, Localization> multiLanguageMap = I18nClient.getInstance().get(keyList, Long.parseLong(
                serviceContext.getTenantId()));

        //获取预设字段的预设名称
        Map<String, Localization> preDefineMap = I18nClient.getInstance().getCommon(preFieldKeys);

        //组装结果
        List<LanguageInfo> infoList = Lists.newArrayList();
        keyMap.forEach((key, field) -> {
            Localization lc = multiLanguageMap.get(key);
            String simpleFieldName = null;
            String traditionFieldName = null;
            String englishFieldName = null;
            String fieldLabel = field.getLabel();
            if (Objects.nonNull(lc)) {
                simpleFieldName = getZhCN(lc);
                traditionFieldName = getZhTW(lc);
                englishFieldName = getEn(lc);
            }
            //如果国际化平台三种语言都为空，则把simple设置为该对象名称
            if (Objects.isNull(simpleFieldName) && Objects.isNull(traditionFieldName) && Objects.isNull(englishFieldName)) {
                log.warn("invoke i18nClient error, localization is empty, tenantId:{}, userId:{}, key:{}, fields:{}",
                        serviceContext.getTenantId(), serviceContext.getUser().getUserId(), key, fields);
                simpleFieldName = field.getLabel();
            }
            if (!IFieldDescribe.DEFINE_TYPE_CUSTOM.equals(field.getDefineType())) {
                String l = getLabel(key, local, preDefineMap);
                if (Objects.nonNull(l)) {
                    fieldLabel = l;
                }
            }
            LanguageInfo info = LanguageInfo.builder().apiName(field.getApiName())
                    .label(fieldLabel)
                    .sfaObject(ifPredefineField(field, describe))
                    .defineType(field.getDefineType())
                    .type(field.getType())
                    .simpleChinese(simpleFieldName)
                    .english(englishFieldName)
                    .config(field.getConfig())
                    .traditional(traditionFieldName).build();
            // 预设字段需要返回预设名称
            if (IFieldDescribe.DEFINE_TYPE_SYSTEM.equals(field.getDefineType()) ||
                    IFieldDescribe.DEFINE_TYPE_PACKAGE.equals(field.getDefineType())) {
                if (Objects.nonNull(preDefineMap.get(key))) {
                    Localization preLc = preDefineMap.get(key);
                    info.setPreSimpleChinese(getZhCN(preLc));
                    info.setPreEnglish(getEn(preLc));
                    info.setPreTraditional(getZhTW(preLc));
                }
            }
            infoList.add(info);
        });

        Edit.Result result = new Edit.Result();
        result.setDescribe(describeInfo);
        result.setFields(infoList);
        return result;
    }

    /**
     * 字段是否为预定义字段
     *
     * @param field 字段描述
     * @return 是否为预定义字段
     */
    private boolean ifPredefineField(IFieldDescribe field, IObjectDescribe describe) {
        if (IObjectDescribe.DEFINE_TYPE_CUSTOM.equals(describe.getDefineType())) {
            return false;
        }
        return !IFieldDescribe.DEFINE_TYPE_CUSTOM.equals(field.getDefineType());

    }

    /**
     * 对象是否为预设对象
     */
    private boolean ifPredefineDescribe(IObjectDescribe describe) {
        return !IObjectDescribe.DEFINE_TYPE_CUSTOM.equals(describe.getDefineType());
    }

    /**
     * 更新语言
     */
    @ServiceMethod("save")
    public Update.Result update(Update.Arg arg, ServiceContext context) {
        //获取User
        LanguageInfo describeInfo = arg.getDescribe();
        List<LanguageInfo> fieldInfoList = arg.getFields();
        //保存对象
        String describeTag = getDescribeTag(describeInfo);
        I18nClient i18nClient = I18nClient.getInstance();
        Localization objectLocal = Localization.builder()
                .key(GetI18nKeyUtil.getDescribeDisplayNameKey(describeInfo.getApiName()))
                .tags(Lists.newArrayList(describeTag))
                .tenantId(Long.parseLong(context.getTenantId()))
                .build();
        i18nClient.build(objectLocal, Lang.zh_CN.getValue(), describeInfo.getPreSimpleChinese());
        i18nClient.build(objectLocal, Lang.zh_TW.getValue(), describeInfo.getTraditional());
        i18nClient.build(objectLocal, Lang.en.getValue(), describeInfo.getEnglish());
        //保存字段
        List<Localization> lcs = Lists.newArrayList();
        fieldInfoList.forEach(f -> {
            String fieldTag = getFieldTag(describeInfo, f);
            Localization lc = Localization.builder()
                    .key(GetI18nKeyUtil.getFieldLabelKey(describeInfo.getApiName(), f.getApiName()))
                    .tags(Lists.newArrayList(fieldTag))
                    .tenantId(Long.parseLong(context.getTenantId()))
                    .build();
            i18nClient.build(lc, Lang.zh_CN.getValue(), f.getPreSimpleChinese());
            i18nClient.build(lc, Lang.zh_TW.getValue(), f.getTraditional());
            i18nClient.build(lc, Lang.en.getValue(), f.getEnglish());
            lcs.add(lc);
        });
        lcs.add(objectLocal);
        log.info("Save localization, tenantId:{}, userId:{}", context.getTenantId(), context.getUser().getUserId());
        i18nClient.save(Long.parseLong(context.getTenantId()), lcs, true);
        try {
            IObjectDescribe describeToUpdate = objectDescribeService.findByTenantIdAndDescribeApiName(context.getTenantId(), arg.getDescribe().getApiName());
            IActionContext actionContext = new ActionContext();
            actionContext.setEnterpriseId(context.getTenantId());
            actionContext.setUserId(context.getUser().getUserId());
            objectDescribeService.touchDescribe(describeToUpdate, actionContext);
        } catch (MetadataServiceException e) {
            log.error("MetadataServiceException:", e);
        }
        return Update.Result.builder().success(true).build();
    }


    private String getDescribeTag(LanguageInfo describeInfo) {
        return IObjectDescribe.DEFINE_TYPE_CUSTOM.equals(describeInfo.getDefineType()) ? TAG_CUSTOM :
                TAG_PRE;
    }

    /**
     * 对象是自定义对象，字段也都是custom_tag，不管是不是预置字段
     * 预置对象看字段的define_type区分
     *
     * @param describeInfo
     * @param f
     * @return
     */
    private String getFieldTag(LanguageInfo describeInfo, LanguageInfo f) {
        return Objects.equals(getDescribeTag(describeInfo), TAG_CUSTOM) ? TAG_CUSTOM :
                IFieldDescribe.DEFINE_TYPE_CUSTOM.equals(f.getDefineType()) ? TAG_CUSTOM : TAG_PRE;

    }

    private String getLabel(String key, String local, Map<String, Localization> keyMap) {
        String result;
        Localization lc = keyMap.get(key);
        if (Objects.isNull(lc)) {
            return null;
        }
        return getLabelByPrior(lc, local);
    }

    private String getLabelByPrior(@NonNull Localization lc, String local) {
        String result;
        if (Lang.en.getValue().equals(local)) {
            result = getEn(lc) == null ? (getZhCN(lc) == null ? getZhTW(lc) : getZhCN(lc)) : getEn(lc);
        } else if (Lang.zh_TW.getValue().equals(local)) {
            result = getZhTW(lc) == null ? (getZhCN(lc) == null ? getEn(lc) : getZhCN(lc)) : getZhTW(lc);
        } else {
            result = getZhCN(lc) == null ? (getEn(lc) == null ? getZhTW(lc) : getEn(lc)) : getZhCN(lc);
        }
        return result;
    }

}
