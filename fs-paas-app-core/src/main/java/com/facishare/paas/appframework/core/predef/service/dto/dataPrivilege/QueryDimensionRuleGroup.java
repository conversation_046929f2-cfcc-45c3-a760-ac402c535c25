package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.facishare.paas.appframework.privilege.dto.DimensionRuleGroupPojo;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface QueryDimensionRuleGroup {

  @Data
  class Arg {

    Set<String> receiveIds;

    Integer receiveType;

    String receiveTenantId;

    int permissionType;

    Map<String, Long> createTimeRange;

    Map<String, Long> modifyTimeRange;

    int sortType;

    int sortOrder;

    int pageNumber;

    int pageSize;

  }


  @Builder
  @Data
  class Result {

    private List<DimensionRuleGroupPojo> dimensionRuleGroupList;

    private PageInfo page;

  }


  @Data
  class PageInfo {

    private Integer pageCount;

    private Integer pageNumber;

    private Integer pageSize;

    private Integer totalCount;

  }

}
