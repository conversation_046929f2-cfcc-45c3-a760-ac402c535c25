package com.facishare.paas.appframework.license.quota.checker;

import lombok.Builder;

import java.util.Map;

public class ScoreQuotaChecker extends BaseQuotaChecker {
    @Builder
    public ScoreQuotaChecker(Map<String, Integer> paraMap) {
        super(paraMap);
    }

    @Override
    public int getMaxCount(QuotaInfo quotaInfo) {
        return paraMap.getOrDefault(quotaInfo.getParaKey(), 0);
    }
}
