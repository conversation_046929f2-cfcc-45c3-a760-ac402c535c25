package com.facishare.paas.appframework.core.predef.service.dto.globaldata;

import com.facishare.paas.appframework.metadata.CountryAreaManager;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON>hao<PERSON>ju on 2022/7/19
 */
public interface GetCountryAreaFieldLabelByCode {

    @Data
    class Arg {
        private List<String> codes;
        private String type;
    }

    @Data
    @Builder
    class Result {
        private List<CountryAreaManager.Node> value;
    }
}
