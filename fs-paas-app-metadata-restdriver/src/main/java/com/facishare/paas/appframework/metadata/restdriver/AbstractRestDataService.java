package com.facishare.paas.appframework.metadata.restdriver;

import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IRelatedListQuery;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

import java.util.Collections;
import java.util.List;

/**
 * Abstract data service implement by REST
 *
 * Created by liyiguang on 2017/7/17.
 */
public abstract class AbstractRestDataService implements IObjectDataService {
    @Override
    public IObjectData create(IObjectData iObjectData, IActionContext context) throws MetadataServiceException {
        return null;
    }

    @Override
    public List<IObjectData> bulkCreate(List<IObjectData> list, Boolean aBoolean, IActionContext context) throws MetadataServiceException {
        return null;
    }

    @Override
    public IObjectData invalid(IObjectData iObjectData, IActionContext context) throws MetadataServiceException {
        return null;
    }

    @Override
    public IObjectData recover(IObjectData iObjectData, IActionContext context) throws MetadataServiceException {
        return null;
    }

    @Override
    public IObjectData delete(IObjectData iObjectData, IActionContext context) throws MetadataServiceException {
        return null;
    }

    @Override
    public IObjectData update(IObjectData iObjectData, IActionContext context) throws MetadataServiceException {
        return null;
    }

    @Override
    public IObjectData findById(String id, String s, IActionContext context, String s1) throws MetadataServiceException {
        return null;
    }

    @Override
    public List<IObjectData> findByIds(List<String> list, String tenantId, String objectApiName, IActionContext context) throws MetadataServiceException {
        return Collections.emptyList();
    }

    @Override
    public List<IObjectData> findByIds(List<String> list, ISearchQuery iSearchQuery, String tenantId, String objectApiName, IActionContext context) throws MetadataServiceException {
        return Collections.emptyList();
    }

    @Override
    public IObjectData findById(String id, String tenantId, String objectApiName, IDataProjection iDataProjection, IActionContext context) throws MetadataServiceException {
        return null;
    }

    @Override
    public List<IObjectData> findByTenantId(String tenantId, String objectApiName, IActionContext context) throws MetadataServiceException {
        return null;
    }

    @Override
    public QueryResult<IObjectData> findBySearchQuery(ISearchQuery iSearchQuery, String tenantId, String objectApiName, IActionContext context) throws MetadataServiceException {
        return null;
    }

    @Override
    public QueryResult<IObjectData> findBySearchQuery(String tenantId, String objectApiName, String s2, IActionContext context) throws MetadataServiceException {
        return null;
    }

    @Override
    public IObjectData findInvalidDataById(String id, String s, IActionContext context, String s1) throws MetadataServiceException {
        return null;
    }

    @Override
    public QueryResult<IObjectData> findBySearchQuery(String tenantId, String objectApiName, SearchTemplateQuery searchTemplateQuery, IActionContext context) throws MetadataServiceException {
        return null;
    }

    @Override
    public IRelatedListQuery.QueryResult<IObjectData> findRelatedList(IRelatedListQuery relatedListQuery, IActionContext context) throws MetadataServiceException {
        return null;
    }

    @Override
    public List<IObjectData> bulkInvalid(List<IObjectData> list, IActionContext context) throws MetadataServiceException {
        return null;
    }

    @Override
    public List<IObjectData> bulkRecover(List<IObjectData> list, IActionContext context) throws MetadataServiceException {
        return null;
    }

    @Override
    public List<IObjectData> bulkDelete(List<IObjectData> list, IActionContext context) throws MetadataServiceException {
        return null;
    }

    @Override
    public List<INameCache> findRecordName(IActionContext context, String s, List<String> list) throws MetadataServiceException {
        return null;
    }
}
