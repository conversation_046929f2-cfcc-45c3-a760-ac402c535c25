package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ContextManager;
import com.google.common.collect.Lists;

import java.util.List;

public class StandardGdprProjectRequestInvalidAction extends BaseGdprProjectRequestAction {
    @Override
    protected Result doAct(Arg arg) {
        StandardBulkInvalidAction.ArgHelper argHelper = new StandardBulkInvalidAction.ArgHelper();
        argHelper.setId(arg.getDataId());
        argHelper.setObjectDescribeApiName(arg.getApiName());
        StandardBulkInvalidAction.Arg args = StandardBulkInvalidAction.Arg.fromArgHelpers(Lists.newArrayList(argHelper));
        ActionContext actionContext = ContextManager.buildActionContext(arg.getApiName(), StandardAction.BulkInvalid.name());
        serviceFacade.triggerAction(actionContext, args, StandardBulkInvalidAction.Result.class);
        infraServiceFacade.updateGdprProjectRequestByType(actionContext.getUser(), arg.getId(), "close");
        return Result.builder().build();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.BulkInvalid.getFunPrivilegeCodes();
    }
}
