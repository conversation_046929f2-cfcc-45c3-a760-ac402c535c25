package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试NumberFieldDataConverter类的数字字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class NumberFieldDataConverterTest {

  @Mock
  private IObjectDataProxyService dataService;

  @Mock
  private SessionContext sessionContext;

  private NumberFieldDataConverter converter;
  private IFieldDescribe fieldDescribe;
  private IObjectData objectData;

  @BeforeEach
  void setUp() {
    converter = new NumberFieldDataConverter();
    fieldDescribe = createNumberFieldDescribe("number_field");
    objectData = createObjectData("number_field", new BigDecimal("123.45"));
    
    // 设置转换器的属性
    converter.setFieldDescribe(fieldDescribe);
    converter.setObjectData(objectData);
    converter.setDataService(dataService);
    
    // 设置SessionContext的默认行为
    lenient().when(sessionContext.getRegion()).thenReturn("");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法正常转换BigDecimal值
   */
  @Test
  @DisplayName("测试convertFieldData - BigDecimal值转换")
  void testConvertFieldData_BigDecimalConversion() throws Exception {
    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("123.45", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理null值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null值")
  void testConvertFieldData_HandlesNullValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("number_field", null);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理整数值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理整数值")
  void testConvertFieldData_HandlesIntegerValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("number_field", 100);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("100", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理Double值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理Double值")
  void testConvertFieldData_HandlesDoubleValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("number_field", 99.99);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("99.99", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理Long值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理Long值")
  void testConvertFieldData_HandlesLongValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("number_field", 1234567890L);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("1234567890", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理BigDecimal的科学计数法
   */
  @Test
  @DisplayName("测试convertFieldData - BigDecimal科学计数法转换")
  void testConvertFieldData_BigDecimalScientificNotation() throws Exception {
    // 准备测试数据 - 使用科学计数法的BigDecimal
    BigDecimal scientificNumber = new BigDecimal("1.23E+10");
    objectData = createObjectData("number_field", scientificNumber);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - toPlainString()应该返回非科学计数法的字符串
    assertEquals("12300000000", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理零值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理零值")
  void testConvertFieldData_HandlesZeroValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("number_field", BigDecimal.ZERO);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("0", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理负数
   */
  @Test
  @DisplayName("测试convertFieldData - 处理负数")
  void testConvertFieldData_HandlesNegativeValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("number_field", new BigDecimal("-456.78"));
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("-456.78", result);
  }

  /**
   * 提供不同类型的数字值测试数据
   */
  private static Stream<Arguments> provideNumberValues() {
    return Stream.of(
        Arguments.of(new BigDecimal("123.456"), "123.456"),
        Arguments.of(new BigDecimal("0.001"), "0.001"),
        Arguments.of(new BigDecimal("1000000"), "1000000"),
        Arguments.of(new BigDecimal("-0.5"), "-0.5"),
        Arguments.of(123, "123"),
        Arguments.of(0, "0"),
        Arguments.of(-999, "-999"),
        Arguments.of(3.14159, "3.14159")
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理不同类型的数字值
   */
  @ParameterizedTest
  @MethodSource("provideNumberValues")
  @DisplayName("测试convertFieldData - 不同类型的数字值")
  void testConvertFieldData_DifferentNumberValues(Object inputValue, String expectedResult) throws Exception {
    // 准备测试数据
    objectData = createObjectData("number_field", inputValue);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals(expectedResult, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理多区域格式化（当region不为空时）
   */
  @Test
  @DisplayName("测试convertFieldData - 多区域格式化")
  void testConvertFieldData_MultiRegionFormatting() throws Exception {
    // 准备测试数据
    when(sessionContext.getRegion()).thenReturn("US");
    objectData = createObjectData("number_field", new BigDecimal("1234.56"));
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - MultiRegionNumberFormatUtils.formatForRegion可能返回null，
    // 根据实际实现，如果formatForRegion返回null，整个方法也会返回null
    // 这里我们只验证方法能正常执行，不抛出异常
    // 结果可能是null（如果formatForRegion返回null）或格式化后的字符串
    assertTrue(result == null || result.length() > 0);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理字段不存在的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 字段不存在")
  void testConvertFieldData_FieldNotExists() throws Exception {
    // 准备测试数据 - 创建不包含目标字段的数据
    objectData = createObjectData("other_field", 123);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 字段不存在时应该返回空字符串
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试转换器的基本属性设置
   */
  @Test
  @DisplayName("测试转换器属性设置")
  void testConverterProperties() {
    // 验证属性设置
    assertEquals(fieldDescribe, converter.getFieldDescribe());
    assertEquals(objectData, converter.getObjectData());
    assertEquals(dataService, converter.getDataService());
  }

  /**
   * 创建测试用的数字字段描述
   */
  private IFieldDescribe createNumberFieldDescribe(String apiName) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试数字字段");
    fieldMap.put("type", IFieldType.NUMBER);
    return FieldDescribeFactory.newInstance(fieldMap);
  }

  /**
   * 创建测试用的对象数据
   */
  private IObjectData createObjectData(String fieldName, Object value) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldName, value);
    return new ObjectData(dataMap);
  }
}
