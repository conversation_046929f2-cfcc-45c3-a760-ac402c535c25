package com.facishare.paas.appframework.core.predef.controller;


import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.plugin.dto.ListHeaderControllerPluginDTO;
import com.facishare.paas.appframework.core.predef.listener.prm.PrmListHeaderControllerListener;
import com.facishare.paas.appframework.core.predef.service.ObjectQuerySceneService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.CommonFilterField;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController.Arg;
import static com.facishare.paas.appframework.metadata.layout.component.ListComponentExt.SCENE_API_NAME;

/**
 * 列表页头
 * <p>
 * Created by liyiguang on 2017/10/18.
 */
public class StandardListHeaderController extends AbstractStandardListHeaderController<Arg> {

    @Override
    public List<Class<? extends ControllerListener<Arg, Result>>> getControllerListenerClassList() {
        List<Class<? extends ControllerListener<Arg, Result>>> controllerListenerClassList = super.getControllerListenerClassList();
        if (AppIdMapping.isPRM(controllerContext.getAppId())) {
            controllerListenerClassList.add(PrmListHeaderControllerListener.class);
        }
        return controllerListenerClassList;
    }

    /**
     * @param layout 移动端摘要布局
     * @return
     * @See {@link ObjectQuerySceneService#findSavedFilterFields}
     */
    @Override
    protected List<CommonFilterField.FilterField> getFilterFields(ILayout layout) {
        if (Utils.FLOW_TASK_OBJ_API_NAME.contains(objectDescribeExt.getApiName()) && fromWhatList()) {
            return infraServiceFacade.findFilterFields(controllerContext.getUser(), objectDescribeExt, null, null, null);
        }
        return super.getFilterFields(layout);
    }

    private boolean fromWhatList() {
        Map<String, Object> extraParams = arg.getExtraParams();
        if (CollectionUtils.empty(extraParams)) {
            return false;
        }
        return "what_list".equals(extraParams.get("business_page"));
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg {

        @JSONField(name = "apiname")
        @JsonProperty("apiname")
        @SerializedName("apiname")
        private String apiName;

        @JSONField(name = "include_layout")
        @JsonProperty("include_layout")
        @SerializedName("include_layout")
        private Boolean includeLayout;

        @JSONField(name = "layout_type")
        @JsonProperty("layout_type")
        @SerializedName("layout_type")
        private String layoutType;

        @JSONField(name = "recordType_apiName")
        @JsonProperty("recordType_apiName")
        @SerializedName("recordType_apiName")
        private String recordTypeAPIName;

        @JSONField(name = "include_ref_describe")
        @JsonProperty("include_ref_describe")
        @SerializedName("include_ref_describe")
        @Builder.Default
        private boolean includeRefDescribe = false;

        @JSONField(name = "goal_rule_id")
        @JsonProperty("goal_rule_id")
        @SerializedName("goal_rule_id")
        //目标管理特殊用参数,后去去掉fcp协议后考虑迁移至fs-crm
        private String goalRuleId;

        @JSONField(name = "goal_rule_detail_id")
        @JsonProperty("goal_rule_detail_id")
        @SerializedName("goal_rule_detail_id")
        //目标管理特殊用参数,去掉fcp协议后考虑迁移至fs-crm
        private String goalRuleDetailId;

        @JSONField(name = "fiscal_year")
        @JsonProperty("fiscal_year")
        @SerializedName("fiscal_year")
        //目标管理特殊用参数,去掉fcp协议后考虑迁移至fs-crm
        private String fiscalYear;

        @JSONField(name = "extend_attribute")
        @JsonProperty("extend_attribute")
        @SerializedName("extend_attribute")
        //老对象列表迁移支持，公海对象查询时一部分数据是客户数据，增加
        // 标识区分
        private String extendAttribute;

        @JSONField(name = "layout_by_template")
        @JsonProperty("layout_by_template")
        @SerializedName("layout_by_template")
        private boolean findLayoutByTemplate;

        @JSONField(name = "template_type")
        @JsonProperty("template_type")
        @SerializedName("template_type")
        private String templateType;

        @JSONField(name = "only_template")
        @JsonProperty("only_template")
        @SerializedName("only_template")
        private boolean onlyTemplate;

        @JSONField(name = "thirdpp_record_type", alternateNames = "thirdapp_record_type")
        @JsonProperty("thirdpp_record_type")
        @JsonAlias("thirdapp_record_type")
        @SerializedName(value = "thirdpp_record_type", alternate = "thirdapp_record_type")
        private String thirdRecordType;

        /**
         * list 大列表
         * related 相关列表
         * selected 选择列表
         */
        @JSONField(name = "list_type")
        @JsonProperty("list_type")
        @SerializedName("list_type")
        private String listType;

        @JSONField(name = "lookup_data_id")
        @JsonProperty("lookup_data_id")
        @SerializedName("lookup_data_id")
        private String targetObjectDataId;

        @JSONField(name = "lookup_describe_api_name")
        @JsonProperty("lookup_describe_api_name")
        @SerializedName("lookup_describe_api_name")
        private String targetObjectApiName;

        @JSONField(name = "field_related_api_name")
        @JsonProperty("field_related_api_name")
        @SerializedName("field_related_api_name")
        private String objectReferenceApiName;

        @JSONField(name = "check_edit_permission")
        @JsonProperty("check_edit_permission")
        @SerializedName("check_edit_permission")
        private Boolean checkEditPermission;

        /**
         * mobile 终端布局
         * web 网页端布局
         */
        @JSONField(name = "layout_agent_type")
        @JsonProperty("layout_agent_type")
        @SerializedName("layout_agent_type")
        private String layoutAgentType;

        @Builder.Default
        private Boolean includeDescribe = true;
        private Map<String, Integer> describeVersionMap;

        //Detail接口返回的布局中的相关对象组件(用于加工场景和按钮)
        @JSONField(name = "related_list_component")
        @JsonProperty("related_list_component")
        @SerializedName("related_list_component")
        private Map<String, Object> relatedListComponent;

        @JSONField(name = "list_component")
        @JsonProperty("list_component")
        @SerializedName("list_component")
        private Map<String, Object> listComponent;

        /**
         * 指定列表页布局 apiName
         */
        @JSONField(name = "list_layout_api_name")
        @JsonProperty("list_layout_api_name")
        @SerializedName("list_layout_api_name")
        private String listLayoutApiName;

        @JSONField(name = "only_default_template")
        @JsonProperty("only_default_template")
        @SerializedName("only_default_template")
        private Boolean onlyDefaultTemplate;

        //是否序列化返回对象中的空值
        private Boolean serializeEmpty;

        /**
         * 拓展参数
         */
        @JSONField(name = "extra_params")
        @JsonProperty("extra_params")
        @SerializedName("extra_params")
        private Map<String, Object> extraParams;

        @JSONField(name = "template_api_name")
        @JsonProperty("template_api_name")
        @SerializedName("template_api_name")
        private String templateApiName;

        /**
         * 是否为跨对象筛选场景
         */
        @JSONField(name = "cross_object_filter")
        @JsonProperty("cross_object_filter")
        @SerializedName("cross_object_filter")
        private Boolean crossObjectFilter;

        @JSONField(name = "include_describe_ext")
        @JsonProperty("include_describe_ext")
        @SerializedName("include_describe_ext")
        private Boolean includeDescribeExt;
        
        @JSONField(name = "include_abstract_layout_list")
        @JsonProperty("include_abstract_layout_list")
        @SerializedName("include_abstract_layout_list")
        private Boolean includeAbstractLayoutList;

        public boolean serializeEmpty() {
            return !Boolean.FALSE.equals(serializeEmpty);
        }

        public boolean checkEditPermission() {
            return Boolean.TRUE.equals(checkEditPermission);
        }

        public boolean includeDescribe() {
            return !Boolean.FALSE.equals(includeDescribe);
        }

        public boolean includeLayout() {
            return !Boolean.FALSE.equals(includeLayout);
        }

        public boolean onlyDefaultTemplate() {
            return Boolean.TRUE.equals(onlyDefaultTemplate);
        }

        public boolean isCrossObjectFilter() {
            return Boolean.TRUE.equals(crossObjectFilter);
        }

        public boolean includeDescribeExt() {
            return !Boolean.FALSE.equals(includeDescribeExt);
        }
        
        public boolean includeAbstractLayoutList() {
            return !Boolean.FALSE.equals(includeAbstractLayoutList);
        }

        public ListHeaderControllerPluginDTO.Arg copy2Plugin() {
            return ListHeaderControllerPluginDTO.Arg.builder()
                    .describeApiName(apiName)
                    .layoutAgentType(layoutAgentType)
                    .listType(listType)
                    .recordType(recordTypeAPIName)
                    .build();
        }

        // true: listHeader请求来自独立站点发布后的页面, 需要一些补充逻辑
        public boolean fromWebSite() {
            return CollectionUtils.notEmpty(this.getListComponent())
                    && BooleanUtils.isTrue((Boolean) this.getListComponent().get(ListComponentExt.COMPONENT_FLAG));
        }

        // 互联站点的极简模式会指定场景apiName, 存储在list_component参数中
        public String fetchWebSiteTemplateApi(ListComponentExt listComponentExt) {
            if (Objects.isNull(listComponentExt) || !fromWebSite()) {
                return "";
            }
            return listComponentExt.get(SCENE_API_NAME, String.class, "");
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    public static class Result extends PreDefineResult {
        private LayoutDocument layout;
        private ObjectDescribeDocument objectDescribe;
        private List<RefObjectDescribe> refObjectDescribeList;
        private List<QueryTemplateDocument> templates;
        private List<QueryTemplateDocument> baseScenes;
        private List<DetailObject> detailObjectList;
        private ObjectDataDocument objectData;
        private List<String> generalInfoHideType;
        private List<String> componentHideType;
        /**
         * 670启用租户场景之后，不在使用当前字段控制显示列
         * 前端根据字段templates.field_list来控制表头展示的字段根据visibleFields和visibleFieldsWidth来控制字段的可选范围，
         * 建议通过重写getAuthorizedFields() 方法来实现
         * <p>
         * 租户场景新添加一致列显示类型 根据布局展示，前端会根据布局中的字段来展示表头
         * 建议在布局layout中将不需要展示的业务字段屏蔽
         */
        @Deprecated
        private List<DocumentBaseEntity> fieldList;
        private List<DetailObject> allDetailObjectList;
        private Boolean isChildObj;
        private ObjectDescribeDocument objectDescribeExt;
        //是否主从审批的白名单企业，用于前端区分锁定的提示信息
        private Boolean supportTag;
        /**
         * 745 新增是否支持或过滤条件
         */
        private Boolean supportOrFilter;
        /**
         * 840 新增场景筛选条件支持「当前登录人」变量
         */
        private Boolean supportFilterVariable;
        /**
         * 计划671移除、只保留visibleFieldsWidth
         */
        private List<String> visibleFields;
        private List<DocumentBaseEntity> visibleFieldsWidth;
        /**
         * 批量按钮
         */
        private List<ButtonDocument> buttons;

        //是否主从审批的白名单企业，用于前端区分锁定的提示信息
        private Boolean isInApprovalWhiteList;
        //是否有编辑权限
        private Boolean hasEditPermission;

        /**
         * 移动端保存的筛选字段
         */
        private List<CommonFilterField.FilterField> filterFields;
        private Boolean supportGeoQuery;

        /**
         * 移动端 根据业务类型下发主角色分配布局
         */
        private List<LayoutDocument> abstractLayoutList;
        private Map<String, String> recordLayoutMapping;

        // 快速筛选字段
        private List<String> quickFilterField;
        // 视图信息
        private List<DocumentBaseEntity> viewInfo;
        private List<DocumentBaseEntity> renderTypeInfo;
        /**
         * 当前页的汇总字段
         */
        private List<DocumentBaseEntity> summaryInfo;

        /**
         * 列表页单条需要外漏的按钮个数
         */
        private Integer listSingleExposed;
        /**
         * 场景渲染方式
         */
        private String sceneRenderType;

        /**
         * 所有页的汇总字段
         */
        private List<DocumentBaseEntity> allPageSummaryInfo;
        /**
         * 选中数据的汇总字段
         */
        private List<DocumentBaseEntity> selectedDataSummaryInfo;

        /**
         * 字段对齐方式
         */
        private String fieldAlign;

        /**
         * 侧栏可筛选元素显示到顶部
         */
        private Boolean convertTopListFilter;

        /**
         * 是否支持全字段搜索
         */
        private Boolean supportFullFieldSearch;

        /**
         * 列表页支持全字段搜索
         */
        private Boolean supportFullFieldSearchOnListPage;

        /**
         * 支持 AI 筛选
         */
        private Boolean supportAiSearch;

        public IFieldDescribe copyFieldToDescribeExt(String fieldApiName) {
            if (Objects.isNull(objectDescribe) || !objectDescribe.toObjectDescribe().containsField(fieldApiName)) {
                return null;
            }
            if (Objects.isNull(objectDescribeExt)) {
                objectDescribeExt = ObjectDescribeDocument.of(new ObjectDescribe());
            }
            IFieldDescribe fieldDescribe = objectDescribe.toObjectDescribe().getFieldDescribe(fieldApiName);
            return ObjectDescribeExt.of(objectDescribeExt).getOrCopyField(fieldDescribe);
        }
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RefObjectDescribe {
        //字段apiName
        String fieldApiName;
        //字段label
        String fieldLabel;
        //对象列表
        Map objectDescribe;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DetailObject {
        //字段apiName
        String fieldApiName;
        //字段label
        String fieldLabel;
        //对象描述
        ObjectDescribeDocument objectDescribe;
        String related_list_name;
        String related_list_label;
        List<DetailLayout> layoutList;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DetailLayout {
        LayoutDocument detail_layout;
        LayoutDocument list_layout;
        String record_type;
    }

}
