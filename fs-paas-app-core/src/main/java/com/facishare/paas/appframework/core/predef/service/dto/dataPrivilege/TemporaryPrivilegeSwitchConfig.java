package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * create by z<PERSON><PERSON> on 2018/11/01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TemporaryPrivilegeSwitchConfig {
    public static final String KEY = "temporary.permission.";
    public static final String TIME_LIMIT = "timeLimit";
    public static final String HANDLING_TASK = "handlingTask";
    public static final int MAX_VALIDITY_TERM = 9999;

    @JSONField(name = "temporary.permission.switch")
    @JsonProperty("temporary.permission.switch")
    @SerializedName("temporary.permission.switch")
    private Boolean confSwitch;
    @JSONField(name = "temporary.permission.level")
    @JsonProperty("temporary.permission.level")
    @SerializedName("temporary.permission.level")
    private Integer level;
    @JSONField(name = "temporary.permission.validity.term")
    @JsonProperty("temporary.permission.validity.term")
    @SerializedName("temporary.permission.validity.term")
    private Integer validityTerm;
    @JSONField(name = "temporary.permission.validity.create")
    @JsonProperty("temporary.permission.validity.create")
    @SerializedName("temporary.permission.validity.create")
    private String createdBy;
    @JSONField(name = "temporary.permission.validity.createTime")
    @JsonProperty("temporary.permission.validity.createTime")
    @SerializedName("temporary.permission.validity.createTime")
    private Long createdTime;
    @JSONField(name = "temporary.permission.validity.modifier")
    @JsonProperty("temporary.permission.validity.modifier")
    @SerializedName("temporary.permission.validity.modifier")
    private String lastModifiedBy;
    @JSONField(name = "temporary.permission.validity.modifyTime")
    @JsonProperty("temporary.permission.validity.modifyTime")
    @SerializedName("temporary.permission.validity.modifyTime")
    private Long lastModifiedTime;
    @JSONField(name = "temporary.permission.withdrawal.way")
    @JsonProperty("temporary.permission.withdrawal.way")
    @SerializedName("temporary.permission.withdrawal.way")
    private String recyclingWay;

    public static TemporaryPrivilegeSwitchConfig convert(DataPrivilegeSwitchConfig.SwitchConfig config) {
        // 当validityTerm大于最大值时，使用最大值
        if (Objects.nonNull(config.getValidityTerm()) && config.getValidityTerm() > MAX_VALIDITY_TERM) {
            config.setValidityTerm(MAX_VALIDITY_TERM);
        }
        return TemporaryPrivilegeSwitchConfig.builder()
                .confSwitch(config.getConfSwitch())
                .level(config.getLevel())
                .validityTerm(config.getValidityTerm())
                .recyclingWay(TIME_LIMIT)
                .createdTime(System.currentTimeMillis())
                .lastModifiedTime(System.currentTimeMillis())
                .build();
    }

}
