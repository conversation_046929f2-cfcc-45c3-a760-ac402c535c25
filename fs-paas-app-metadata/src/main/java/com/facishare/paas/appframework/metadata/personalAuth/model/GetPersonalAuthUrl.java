package com.facishare.paas.appframework.metadata.personalAuth.model;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR> create by liy on 2024/6/18
 */
public interface GetPersonalAuthUrl {

    @Data
    class Arg {
        /**
         * 开发信息
         */
        private Map<String, String> devInfo;
        /**
         * 应用类型
         */
        private String appType;
        /**
         * 插件apiName
         */
        private String pluginApiName;
    }

    @Data
    class Result {
        /**
         * 错误码
         */
        private int errorCode;
        /**
         * 错误提示语
         */
        private String errorMessage;
        /**
         * 授权url
         */
        private String authUrl;
    }
}
