package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ButtonUsePageType;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.RelatedObjectDescribeStructure;
import com.facishare.paas.appframework.metadata.layout.MasterDetailGroupComponentBuilder;
import com.facishare.paas.appframework.metadata.layout.RelatedObjectGroupComponentBuilder;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.base.Strings;
import lombok.Data;

import java.util.List;

/**
 * Created by zhouweirong on 2021/9/27.
 */
public class StandardDesignerRelatedLayoutResourceController extends AbstractDesignerListLayoutResourceController<StandardDesignerRelatedLayoutResourceController.Arg> {

    private IObjectDescribe lookupDescribe;
    private IFieldDescribe lookupField;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected void before(Arg arg) {
        if (Strings.isNullOrEmpty(arg.getLookupObjectApiName()) || Strings.isNullOrEmpty(arg.getLookupFieldApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_EMPTY));
        }
        super.before(arg);
        describe = serviceFacade.findObject(controllerContext.getTenantId(), controllerContext.getObjectApiName());
        lookupField = ObjectDescribeExt.of(describe).getActiveFieldDescribe(arg.getLookupFieldApiName());
        lookupDescribe = serviceFacade.findObject(controllerContext.getTenantId(), arg.getLookupObjectApiName());
    }

    protected List<IButton> getNormalButtons() {
        if (FieldDescribeExt.of(lookupField).isMasterDetailField()) {
            return getDetailNormalButtons();
        } else {
            return getRelatedNormalButtons();
        }
    }

    protected List<IButton> getRelatedNormalButtons() {
        RelatedObjectDescribeStructure relatedStructure = RelatedObjectDescribeStructure.builder()
                .relatedObjectDescribe(describe)
                .fieldApiName(arg.getLookupFieldApiName())
                .build();

        List<IButton> buttonList = RelatedObjectGroupComponentBuilder.builder()
                .functionPrivilegeService(serviceFacade)
                .buttonLogicService(serviceFacade)
                .user(User.systemUser(controllerContext.getTenantId()))
                .objectDescribe(ObjectDescribeExt.of(lookupDescribe))
                .build()
                .getButtons(relatedStructure);

        return buttonList;
    }

    protected List<IButton> getDetailNormalButtons() {
        List<IButton> buttonList = MasterDetailGroupComponentBuilder.builder()
                .buttonLogicService(serviceFacade)
                .user(User.systemUser(controllerContext.getTenantId()))
                .objectDescribeExt(ObjectDescribeExt.of(lookupDescribe))
                .build()
                .getButtons(describe);

        return buttonList;
    }

    protected List<IButton> getSingleButtons() {
        List<IButton> batchButtons = findButtonsByUsePage(ButtonUsePageType.RelatedList);
        return batchButtons;
    }

    @Data
    public static class Arg {
        private String lookupObjectApiName;
        private String lookupFieldApiName;
    }
}
