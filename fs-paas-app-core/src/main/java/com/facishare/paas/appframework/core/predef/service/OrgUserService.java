package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.orguser.FindByUserIds;
import com.facishare.paas.appframework.core.predef.service.dto.orguser.FindMainDeptInfoByUserId;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.appframework.metadata.ObjectDataFormatter;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 组织架构用户服务
 * <p>
 * Created by liyiguang on 2018/4/13.
 */
@ServiceModule("user")
@Component
@Slf4j
public class OrgUserService {

    @Autowired
    private MetaDataService metaDataService;

    @Autowired
    private OrgService orgService;
    @Autowired
    private DescribeLogicService describeLogicService;

    @ServiceMethod("find_by_user_ids")
    public FindByUserIds.Result findUserByUserId(FindByUserIds.Arg arg, ServiceContext context) {

        if (CollectionUtils.empty(arg.getUserIds())) {
            return FindByUserIds.Result.builder().objectDataList(Collections.emptyList()).build();
        }

        List<IObjectData> userList = metaDataService.findEmployeeInfoByUserIds(context.getTenantId(), arg.getUserIds());

        if (arg.formatData()) {
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(context.getTenantId(), ObjectDescribeExt.PERSONNEL_OBJ_API_NAME);
            ObjectDataFormatter.builder()
                    .describe(objectDescribe)
                    .dataList(userList)
                    .build()
                    .format();
        }

        FindByUserIds.Result ret = FindByUserIds.Result.builder()
                .objectDataList(userList.stream().map(ObjectDataDocument::of).collect(Collectors.toList()))
                .build();

        return ret;
    }


    @ServiceMethod("find_main_dept_info_by_user_id")
    public FindMainDeptInfoByUserId.Result findMainDeptInfoByUserId(FindMainDeptInfoByUserId.Arg arg, ServiceContext context) {
        if (CollectionUtils.empty(arg.getUserIds())) {
            return FindMainDeptInfoByUserId.Result.builder().mainDeptInfoList(Collections.emptyList()).build();
        }

        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfo = orgService.getMainDeptInfo(context.getTenantId(), context.getUser().getUserId(), Lists.newArrayList(arg.getUserIds()));
        if (mainDeptInfo.values().isEmpty()) {
            return FindMainDeptInfoByUserId.Result.builder().mainDeptInfoList(Collections.emptyList()).build();
        }
        List<FindMainDeptInfoByUserId.MainDeptInfo> mainDeptInfoList = mainDeptInfo.values().stream().map(it -> FindMainDeptInfoByUserId.MainDeptInfo.builder().
                userId(it.getUserId()).
                userName(it.getUserName()).
                deptId(it.getDeptId()).
                deptName(it.getDeptName()).
                leaderId(it.getLeaderId()).
                leaderName(it.getLeaderName()).build()).collect(Collectors.toList());
        return FindMainDeptInfoByUserId.Result.builder().mainDeptInfoList(mainDeptInfoList).build();
    }

}
