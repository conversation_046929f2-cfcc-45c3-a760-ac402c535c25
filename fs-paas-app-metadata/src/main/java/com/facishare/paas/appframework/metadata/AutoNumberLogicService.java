package com.facishare.paas.appframework.metadata;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.function.dto.IncrementNumberBatchExecute;
import com.facishare.paas.appframework.metadata.autonumber.AutoNumberConditionInfo;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.expression.Expression;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.AutoNumber;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.appframework.metadata.AutoNumberExt.ConditionEnum;

/**
 * create by zhaoju on 2018/09/26
 */
@Slf4j
@Service
public class AutoNumberLogicService {
    @Autowired
    private AutoNumberService autoNumberService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private FunctionAutoNumber functionAutoNumber;

    /**
     * objectData和tenantId计算自增编码字段的值
     * 注意目前使用rest接口调用此方法可以正常工作，使用Action调用时请重写
     *
     * @param tenantId
     * @param dataMap
     * @return
     */
    public IObjectData calculateAutoNumberValue(String tenantId, Map<String, Object> dataMap) {
        IObjectData data = new ObjectData(dataMap);
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(tenantId, data.getDescribeApiName());
        try {
            calculateAutoNumberValue(describe, Lists.newArrayList(data));
        } catch (MetadataServiceException e) {
            log.warn("saveObjectData error,tenantId:{},data:{}", tenantId, data, e);
            throw new MetaDataBusinessException(e);
        }
        return data;
    }

    /**
     * 计算自增编号
     *
     * @param describe
     * @param dataList
     * @return
     * @throws MetadataServiceException
     */
    public List<IObjectData> calculateAutoNumberValue(IObjectDescribe describe, List<IObjectData> dataList) throws MetadataServiceException {
        if (CollectionUtils.empty(dataList)) {
            return dataList;
        }
        StopWatch sw = StopWatch.create("calculateAutoNumberValue");
        try {
            long currentTimeMillis = System.currentTimeMillis();
            Map<String, Integer> stringIntegerMap = autoNumberService.generateAutoNumber(describe, dataList.size(), currentTimeMillis);
            sw.lap("generateAutoNumber");
            List<IObjectData> resultDataList = autoNumberService.replaceAutoNumber(dataList, describe, stringIntegerMap, currentTimeMillis);
            sw.lap("replaceAutoNumber");
            ObjectDescribeExt.of(describe).getFunctionAutoNumber().ifPresent(autoNumber -> {
                User user = Optional.ofNullable(RequestContextManager.getContext())
                        .map(RequestContext::getUser)
                        .orElseGet(() -> User.systemUser(describe.getTenantId()));
                functionAutoNumber.incrementNumberByFunction(describe, autoNumber, dataList, user);
                sw.lap("incrementNumberByFunction");
            });
            return resultDataList;
        } finally {
            sw.logSlow(100);
        }
    }

    /**
     * 创建或更新自增编号字段
     *
     * @param objectDescribe 新的对象描述
     * @param oldDescribe    旧的对象描述
     * @throws MetadataServiceException
     */
    @Transactional
    public void diffAutoNumberField(IObjectDescribe objectDescribe, IObjectDescribe oldDescribe) throws MetadataServiceException {
        autoNumberService.diffAutoNumberField(objectDescribe, oldDescribe);
    }

    /**
     * 创建新字段时，创建对应的自增编码重计规则
     *
     * @param objectDescribe
     * @param fieldDescribes
     * @throws MetadataServiceException
     */
    @Transactional
    public void createCondition(IObjectDescribe objectDescribe, List<IFieldDescribe> fieldDescribes) throws MetadataServiceException {
        autoNumberService.createCondition(objectDescribe, fieldDescribes);
        autoNumberService.changeAutoNumberFunctionStatus(objectDescribe, fieldDescribes);
    }

    /**
     * 切换重计条件
     *
     * @param objectDescribe
     * @param fieldDescribes
     * @throws MetadataServiceException
     */
    @Transactional
    public void updateCondition(IObjectDescribe objectDescribe, List<IFieldDescribe> fieldDescribes) throws MetadataServiceException {
        autoNumberService.updateCondition(objectDescribe, fieldDescribes);
        autoNumberService.changeAutoNumberFunctionStatus(objectDescribe, fieldDescribes);
    }

    public void autoNumberValidateByObjectDescribe(IObjectDescribe objectDescribe, User user) {
        autoNumberValidate(objectDescribe, ObjectDescribeExt.of(objectDescribe).getAutoNumberFields(), user);
    }

    public void autoNumberValidateByObjectDescribe(IObjectDescribe objectDescribe, IObjectDescribe describeInDb, User user) {
        List<AutoNumber> autoNumbers = ObjectDescribeExt.of(objectDescribe).diffAutoNumberFields(describeInDb);
        autoNumberValidate(objectDescribe, autoNumbers, user);
    }

    public void autoNumberValidate(IObjectDescribe objectDescribe, List<AutoNumber> autoNumberFields, User user) {
        if (CollectionUtils.empty(autoNumberFields)) {
            return;
        }
        AutoNumberExt.autoNumberValidate(autoNumberFields);
        validateFixFields(objectDescribe, autoNumberFields, user);
    }

    private void validateFixFields(IObjectDescribe objectDescribe, List<AutoNumber> autoNumberFields, User user) {
        IObjectDescribe personnelObj = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), Utils.PERSONNEL_OBJ_API_NAME);
        autoNumberFields.forEach(autoNumber -> validateFixField(objectDescribe, autoNumber, personnelObj));
    }

    private void validateFixField(IObjectDescribe objectDescribe, AutoNumber autoNumber, IObjectDescribe personnelObj) {
        List<String> preFieldName = Expression.of(autoNumber.getPrefix()).parseVariableNames();
        List<String> postFieldName = Expression.of(autoNumber.getPostfix()).parseVariableNames();
        Set<String> fieldNameSets = Stream.of(preFieldName, postFieldName).flatMap(Collection::stream).collect(Collectors.toSet());
        if (CollectionUtils.empty(fieldNameSets)) {
            return;
        }
        if (isValidated(fieldNameSets, ObjectDescribeExt.of(objectDescribe), personnelObj)) {
            throw new ValidateException(I18N.text(I18NKey.AUTO_NUMBER_VALIDATE_MESSAGE, autoNumber.getLabel()));
        }
    }

    private boolean isValidated(Set<String> fieldNameSets, ObjectDescribeExt describeExt, IObjectDescribe personnelObj) {
        return fieldNameSets.stream()
                .anyMatch(fieldName -> !(isSelectOne(describeExt, fieldName) || isEmployeeSelectOne(describeExt, fieldName, personnelObj)));
    }

    private boolean isEmployeeSelectOne(ObjectDescribeExt describeExt, String fieldName, IObjectDescribe personnelObj) {
        if (!StringUtils.contains(fieldName, ".")) {
            return false;
        }
        Optional<IFieldDescribe> fieldOptional = describeExt.getFieldDescribeSilently(StringUtils.replace(StringUtils.substringBefore(fieldName, "."), "__r", ""))
                .filter(fieldDescribe -> IFieldType.EMPLOYEE.equals(fieldDescribe.getType()));
        if (!fieldOptional.isPresent()) {
            return false;
        }
        return ObjectDescribeExt.of(personnelObj).getFieldDescribeSilently(StringUtils.substringAfter(fieldName, "."))
                .filter(fieldDescribe -> IFieldType.SELECT_ONE.equals(fieldDescribe.getType())).isPresent();
    }

    private boolean isSelectOne(ObjectDescribeExt describeExt, String fieldName) {
        return describeExt.getFieldDescribeSilently(fieldName)
                .filter(fieldDescribe -> IFieldType.SELECT_ONE.equals(fieldDescribe.getType())).isPresent();
    }

    /**
     * @param describe
     * @param autoNumber
     * @param incrementNumberSetMap
     * @param user
     * @return key 为同类规则 （equals 相同的规则），
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Map<IncrementNumberBatchExecute.IncrementNumber, AtomicLong> incrementNumber(IObjectDescribe describe,
                                                                                        AutoNumber autoNumber,
                                                                                        Map<IncrementNumberBatchExecute.IncrementNumber, Set<String>> incrementNumberSetMap,
                                                                                        User user) {

        Map<IncrementNumberBatchExecute.IncrementNumber, AtomicLong> resultMap = Maps.newHashMap();
        incrementNumberSetMap.forEach((incrementNumber, value) -> {
            ConditionEnum conditionEnum = ConditionEnum.of(incrementNumber.getCondition(), false);
            validate(conditionEnum, incrementNumber, user);
            Long number = autoNumberService.incrementNumber(describe, autoNumber, incrementNumber.getCounter(),
                    incrementNumber.getInitialValue(), conditionEnum, user, value.size(), incrementNumber.getSteppingNumber());
            resultMap.put(incrementNumber, new AtomicLong(number));
        });
        return resultMap;
    }

    private void validate(ConditionEnum conditionEnum, IncrementNumberBatchExecute.IncrementNumber incrementNumber, User user) {
        if (null == conditionEnum) {
            throw new ValidateException(I18N.text(I18NKey.FUNC_INCREMENT_NUMBER_CONDITION_NOT_EXIST));
        }

        String counter = incrementNumber.getCounter();
        if (Strings.isNullOrEmpty(counter) || counter.length() > 50) {
            log.warn("validate fail, ei:{}, incrementNumber:{}, user:{}", user.getTenantId(), incrementNumber, user);
            throw new ValidateException(I18N.text(I18NKey.FUNC_INCREMENT_NUMBER_FAIL));
        }
    }

    public void resetAutoNumber(User user, String describeApiName, String fieldApiName, String condition, String counter, Integer startNumber) {
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), describeApiName);
        AutoNumber autoNumber = ObjectDescribeExt.of(describe)
                .stream()
                .map(FieldDescribeExt::of)
                .filter(it -> Objects.equals(fieldApiName, it.getApiName()))
                .filter(FieldDescribeExt::isAutoNumber)
                .map(it -> it.<AutoNumber>getFieldDescribe())
                .findFirst()
                .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.FIELD_NOT_EXIST_OR_NOT_AUTO_INCREMENT)));
        AutoNumberExt autoNumberExt = AutoNumberExt.of(autoNumber);
        if (!autoNumberExt.isFunctionAutoNumber()) {
            if (Strings.isNullOrEmpty(condition)) {
                condition = autoNumberExt.getCondition();
            }
            if (Objects.isNull(startNumber)) {
                startNumber = autoNumberExt.getStartNumber();
            }
            counter = null;
        }
        AutoNumberConditionInfo conditionInfo = AutoNumberConditionInfo.builder()
                .tenantId(user.getTenantId())
                .objectApiName(describeApiName)
                .fieldApiName(fieldApiName)
                .condition(ConditionEnum.of(condition))
                .startNumber(startNumber)
                .counter(counter)
                .build();

        try {
            autoNumberService.setNAutoNumber(conditionInfo, System.currentTimeMillis());
        } catch (MetadataServiceException e) {
            log.warn("setNAutoNumber fail, ei:{}, conditionInfo:{}", user.getTenantId(), JacksonUtils.toJson(conditionInfo), e);
        }

    }

}
