package com.facishare.paas.appframework.privilege.util;

import com.facishare.paas.appframework.privilege.model.ObjectPrivilegeInfo;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;

import java.util.List;
import java.util.Map;

/**
 * Created by luxin on 2018/7/14.
 */
public class FunctionInfoConstants {

    public static Map<String, ObjectPrivilegeInfo> apiNameObjectPrivilegeInfoMapping = Maps.newHashMap();

    static {
        ConfigFactory.getConfig("fs-crm-object-privilege_info-mapping", iConfig -> {
            Gson gson = new GsonBuilder().create();
            List<ObjectPrivilegeInfo> ObjectPrivilegeInfoList = gson.fromJson(
                    iConfig.get("apiNameObjectPrivilegeInfoMapping"), new TypeToken<List<ObjectPrivilegeInfo>>(){}.getType());

            for (ObjectPrivilegeInfo objectPrivilegeInfo : ObjectPrivilegeInfoList) {
                apiNameObjectPrivilegeInfoMapping.put(objectPrivilegeInfo.getApiName(), objectPrivilegeInfo);
            }
        });
    }

}
