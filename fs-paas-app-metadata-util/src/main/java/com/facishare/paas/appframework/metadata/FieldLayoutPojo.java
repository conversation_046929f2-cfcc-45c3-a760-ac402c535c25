package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by luohl on 2017/10/30.
 */
@Data
public class FieldLayoutPojo {
    @JSONField(name = "api_name")
    @SerializedName("api_name")
    @JsonProperty("api_name")
    private String apiName;

    private String label;

    @JSONField(name = "render_type")
    @SerializedName("render_type")
    @JsonProperty("render_type")
    private String renderType;

    @JSONField(name = "is_readonly")
    @SerializedName("is_readonly")
    @JsonProperty("is_readonly")
    private boolean readonly;

    @JSONField(name = "is_required")
    @SerializedName("is_required")
    @JsonProperty("is_required")
    private boolean required;

    @J<PERSON>NField(name = "is_show")
    @SerializedName("is_show")
    @JsonProperty("is_show")
    private boolean show;

    @JSONField(name = "layout_type")
    @SerializedName("layout_type")
    @JsonProperty("layout_type")
    private String layoutType;

    private String namespace;
    @JSONField(name = "reference_field_config")
    @SerializedName("reference_field_config")
    @JsonProperty("reference_field_config")
    private String referenceFieldConfig;
}
