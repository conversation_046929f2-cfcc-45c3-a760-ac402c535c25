package com.facishare.paas.appframework.privilege.dto;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2018/7/12 14:03
 */
public interface QueryRoleInfoListByForbiddenUsers {
    @Data
    @AllArgsConstructor
    class Arg {
        private AuthContext authContext;
        private Boolean forbiddenFlag;
        private List<String> users;
        private Map<String, Integer> pageInfo;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errMessage;
        private UserRolesAndPageInfo result;
        private boolean success;
    }

    @Data
    class UserRolesAndPageInfo {
        private Map<String, List<QueryRoleInfoListByUsersModel.UserRolePojo>> userRoles;
        private Map<String, Integer> pageInfo;
    }

    @Data
    @AllArgsConstructor
    class RoleInfo {
        private String roleCode;
        private String roleName;
    }

    @Data
    class AllRoleInfo {
        private String defualtRole;
        private List<RoleInfo> roleInfoList = Lists.newArrayList();
    }

}
