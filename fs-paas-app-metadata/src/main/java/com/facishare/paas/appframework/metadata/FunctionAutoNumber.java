package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.AutoNumber;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/04/22
 */
public interface FunctionAutoNumber {
    List<IObjectData> incrementNumberByFunction(IObjectDescribe describe, AutoNumber autoNumber,
                                                List<IObjectData> dataList, User user);
}
