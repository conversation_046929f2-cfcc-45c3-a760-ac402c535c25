package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/02/18
 */
public interface FindCrmObjectList {
    @Data
    class Arg {

        private boolean isIncludeSystemObj = true;

        private boolean isIncludeFieldDescribe = false;

        private String packageName;

        private boolean isAsc = false;

        private boolean isIncludeUnActived = false;

        private String sourceInfo;
    }

    @Data
    @Builder
    class Result {
        private List<ObjectInfo> objectList;
        private ManageGroupDTO manageGroup;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class ObjectInfo {
        private String apiName;
        private String displayName;
    }
}
