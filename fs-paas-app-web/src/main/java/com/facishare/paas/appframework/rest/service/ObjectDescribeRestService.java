package com.facishare.paas.appframework.rest.service;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.ref.RefMessage.ActionType;
import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.*;
import com.facishare.paas.appframework.metadata.objects.ObjectListConfig;
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService;
import com.facishare.paas.appframework.rest.dto.data.*;
import com.facishare.paas.appframework.rest.dto.describe.FindSimpleDetailDescribes;
import com.facishare.paas.appframework.rest.dto.describe.FindSimpleDetailDescribes.SimpleDescribe;
import com.facishare.paas.appframework.rest.dto.describe.QueryDisplayNameByApiNames;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.DynamicDescribe;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribeExtra;
import com.facishare.paas.metadata.api.service.IObjectDescribeExtService;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.metadata.api.describe.IObjectDescribe.DEFINE_TYPE_CUSTOM;

@Service
@Slf4j
public class ObjectDescribeRestService {

    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private FieldRelationCalculateService fieldRelationCalculateService;

    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private IObjectDescribeExtService describeExtService;

    /**
     * 根据企业获取自定义对象描述列表
     *
     * @param arg     封装必要参数
     * @param context 业务上下文环境
     * @return 对象表述列表
     */
    public FindDescribeByEid.Result findByTenantId(FindDescribeByEid.Arg arg, RequestContext context) {
        FindDescribeByEid.Result result = new FindDescribeByEid.Result();
        ObjectDescribeFinder objectDescribeFinder = ObjectDescribeFinder.builder()
                .user(context.getUser())
                .describeDefineType(null)
                .isOnlyActivate(arg.isIncludeUnActived())
                .isExcludeDetailObj(arg.isExcludeDetailObj())
                .isExcludeDetailWithMasterCreated(arg.isExcludeDetailWithMasterCreated())
                .isAsc(false)
                .includeChangeOrderObject(arg.getIncludeChangeOrderObj())
                .sourceInfo(arg.getSourceInfo())
                .build();
        List<IObjectDescribe> describeList = describeLogicService.findObjectsByTenantId(objectDescribeFinder);
        ManageGroup manageGroup = describeLogicService.queryObjectManageGroup(context.getUser(), arg.getSourceInfo());
        result.setObjectDescribe(ObjectDescribeDocument.ofList(describeList));
        result.setManageGroup(ManageGroupDTO.of(manageGroup));
        return result;
    }

    /**
     * 根据apiName和企业Id查找单一对象描述
     *
     * @param arg            封装必要参数
     * @param requestContext 业务上下文环境
     * @return 对象描述
     */
    @SuppressWarnings("unchecked")
    public FindDescribeByEidAndApiName.Result findByTenantIdAndDescribeAPIName(FindDescribeByEidAndApiName.Arg arg,
                                                                               RequestContext requestContext) {
        FindDescribeByEidAndApiName.Result result = new FindDescribeByEidAndApiName.Result();
        DescribeDetailResult describeDetailResult = describeLogicService.findDescribeByApiName(requestContext, arg.getApiName(), Boolean.FALSE,
                "", "", arg.isIncludeRefDesc(), arg.isIncludeDetailDesc(), false, null);
        if (null == describeDetailResult) {
            throw new ObjectDefNotFoundError(I18N.text(I18NKey.OBJECT_DESCRIBE_NOT_FOUND));
        }

        //计算普通字段和统计字段、计算字段的依赖关系
        if (arg.isIncludeStatistics()) {
            IObjectDescribe masterDescribe = new ObjectDescribe(describeDetailResult.getObjectDescribe());
            List<IObjectDescribe> detailDescribes = CollectionUtils.nullToEmpty(
                            describeDetailResult.getDetailObjectList()).stream()
                    .map(x -> new ObjectDescribe(x.getObjectDescribe())).collect(Collectors.toList());
            fieldRelationCalculateService.computeCalculateRelation(masterDescribe, detailDescribes);
        }

        ObjectDescribeDocument objectDescribeDocument =
                ObjectDescribeDocument.of(describeDetailResult.getObjectDescribe());
        // 当前对象是否开启变更单对象
        objectDescribeDocument.put("enabled_change_order", ObjectDescribeExt.of(objectDescribeDocument.toObjectDescribe()).enabledChangeOrder());
        List<RefObjectDescribeListResult> refObjectDescribeListResults = describeDetailResult.getRefObjectDescribeList();
        List<DetailObjectListResult> detailObjectListResults = describeDetailResult.getDetailObjectList();
        result.setObjectDescribeDocument(objectDescribeDocument);
        result.setRefObjectDescribeList(refObjectDescribeListResults);
        result.setDetailObjectDescribeList(detailObjectListResults);
        return result;
    }

    public FindReferenceDescribes.Result findByReferenceObjects(FindReferenceDescribes.Arg arg, RequestContext context) {
        FindReferenceDescribes.Result result = new FindReferenceDescribes.Result();
        List<IObjectDescribe> crm = describeLogicService.findLookupDescribes(context.getTenantId(), arg.getApiName(), false);
        List<IObjectDescribe> details = describeLogicService.findDetailDescribes(context.getTenantId(), arg.getApiName());
        //只返回启用的数据
        crm = crm.stream().filter(IObjectDescribe::isActive).collect(Collectors.toList());
        details = details.stream().filter(IObjectDescribe::isActive).collect(Collectors.toList());
        crm.addAll(details);
        result.setObjectDescribe(ObjectDescribeDocument.ofList(Lists.newArrayList(crm)));
        return result;
    }

    /**
     * 根据api列表获取对象描述
     *
     * @param arg     封装参数
     * @param context 请求上下文
     * @return 对象描述列表
     */
    public FindDescribeByApiNameList.Result findByApiNameList(FindDescribeByApiNameList.Arg arg,
                                                              RequestContext context) {
        //查询描述不做拷贝
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI, context.getTenantId())) {
            context.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        }
        FindDescribeByApiNameList.Result result = new FindDescribeByApiNameList.Result();
        List<String> apiNameList = arg.getNameList();
        List<IObjectDescribe> describeList;
        if (arg.isIncludeFields()) {
            Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(context.getTenantId(),
                    apiNameList);
            //map -> list
            describeList = Lists.newArrayList(describeMap.values());
        } else {
            describeList = describeLogicService.findDescribeListWithoutFields(context.getTenantId(), apiNameList);
        }
        result.setObjectDescribe(ObjectDescribeDocument.ofList(describeList));
        return result;
    }

    public FindByEidAndDescribeId.Result findByDescribeId(FindByEidAndDescribeId.Arg arg,
                                                          RequestContext context) {
        FindByEidAndDescribeId.Result result = new FindByEidAndDescribeId.Result();
        IObjectDescribe describe = describeLogicService.findObjectById(context.getTenantId(), arg.getDescribeId());
        result.setObjectDescribeDocument(ObjectDescribeDocument.of(describe));
        return result;
    }

    public UpdateObjectDescribe.Result updateObjectDescribe(UpdateObjectDescribe.Arg arg,
                                                            RequestContext context) {
        UpdateObjectDescribe.Result result = new UpdateObjectDescribe.Result();
        DescribeResult updated = describeLogicService.updateDescribe(context.getUser(), arg.getDescribeJson(),
                null, true, false);
        result.setObjectDescribeDocument(ObjectDescribeDocument.of(updated.getObjectDescribe()));
        return result;
    }

    public DeleteObjectDescribe.Result deleteObjectDescribe(DeleteObjectDescribe.Arg arg,
                                                            RequestContext context) {
        DeleteObjectDescribe.Result result = new DeleteObjectDescribe.Result();
        IObjectDescribe describe = describeLogicService.findObjectById(context.getTenantId(), arg.getDescribeId());
        IObjectDescribe deleted = describeLogicService.deleteDescribe(context.getUser(), describe.getApiName());
        result.setObjectDescribeDocument(ObjectDescribeDocument.of(deleted));
        return result;
    }

    public List<FindDisplayNames.Result> findDisplayNames(FindDisplayNames.Arg arg, RequestContext context) {
        List<FindDisplayNames.Result> resultList = Lists.newArrayList();
        Set<String> objectVisibleScopes = arg.isIncludeBigObject() ? IObjectDescribe.BIG_OBJECT_VISIBLE_SCOPE : null;
        ObjectDescribeFinder objectDescribeFinder = ObjectDescribeFinder.builder()
                .user(context.getUser())
                .describeDefineType(arg.isOnlyIncludeCustomObj() ? DEFINE_TYPE_CUSTOM : null)
                .isOnlyActivate(!arg.isOnlyActivate())
                .includeChangeOrderObject(arg.getIncludeChangeOrderObj())
                .isExcludeDetailObj(arg.isExcludeDetailObj())
                .isExcludeDetailWithMasterCreated(arg.isExcludeDetailWithMasterCreated())
                .isAsc(false)
                .sourceInfo(ObjectListConfig.REST)
                .visibleScope(objectVisibleScopes)
                .build();
        List<IObjectDescribe> describeList = serviceFacade.findObjectsByTenantId(objectDescribeFinder);

        List<String> hideButtonApiNames = Lists.newArrayList();
        if (arg.isCheckDetailObjectButton()) {
            hideButtonApiNames = describeLogicService.findDetailApiNamesCreateWithMasterAndHiddenButton(context.getTenantId());
        }
        ManageGroup objectManageGroup = describeLogicService.queryObjectManageGroup(context.getUser(), arg.getSourceInfo());

        List<String> describeApiNames = describeList.stream().map(IObjectDescribe::getApiName).collect(Collectors.toList());
        IActionContext metaDataCtx = ActionContextExt.of(objectDescribeFinder.getUser()).getContext();
        Map<String, IObjectDescribeExtra> describeExtMap = describeExtService.findDescribeExtByDescribeApiName(describeApiNames, metaDataCtx);
        boolean presetObjChangeIcon = UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CHANGE_ICON_PRESET_OBJECT_GRAY, context.getTenantId());
        for (IObjectDescribe describe : describeList) {
            if (CollectionUtils.notEmpty(arg.getApiNameList()) && !arg.getApiNameList().contains(describe.getApiName())) {
                continue;
            }
            FindDisplayNames.Result cell = new FindDisplayNames.Result();
            cell.setDescribeApiName(describe.getApiName());
            cell.setDescribeDisplayName(describe.getDisplayName());
            cell.setDefineType(describe.getDefineType());
            cell.setIsActive(describe.isActive());
            cell.setIconPath(describe.getIconPath());
            cell.setIconIndex(describe.getIconIndex());
            cell.setHideButton(hideButtonApiNames.contains(describe.getApiName()));
            cell.setOriginalDescribeApiName(describe.getOriginalDescribeApiName());
            cell.setPublicObject(describe.isPublicObject());
            if (Objects.nonNull(objectManageGroup)) {
                cell.setManageGroupSupport(objectManageGroup.support(describe.getApiName()));
            }
            if (presetObjChangeIcon || ObjectDescribeExt.isCustomObject(describe.getApiName())) {
                Integer iconSlot = Optional.ofNullable(describeExtMap)
                        .map(x -> x.get(describe.getApiName()))
                        .map(IObjectDescribeExtra::getIconIndex)
                        .orElse(null);
                cell.setIconSlot(iconSlot);
            }
            resultList.add(cell);
        }

        return resultList;

    }

    /**
     * 更新指定字段
     */
    public UpdateCustomFieldDescribe.Result updateCustomField(UpdateCustomFieldDescribe.Arg arg, RequestContext context) {
        IObjectDescribe objectDescribe = describeLogicService.updateCustomFieldDescribe(context.getUser(),
                arg.getDescribeApiName(), arg.getFieldDescribeJson(), null, null);
        UpdateCustomFieldDescribe.Result result = new UpdateCustomFieldDescribe.Result();
        result.setObjectDescribeDocument(ObjectDescribeDocument.of(objectDescribe));
        return result;
    }

    /**
     * 查询字段描述
     */
    public FindFieldDescribe.Result findFieldDescribe(FindFieldDescribe.Arg arg, RequestContext context) {
        FieldResult fieldResult = describeLogicService.findCustomFieldDescribe(context.getTenantId(), arg.getDescribeApiName(),
                arg.getFieldApiName());
        IFieldDescribe fieldDescribe = fieldResult.getField();
        FindFieldDescribe.Result result = new FindFieldDescribe.Result();
        result.setFieldDescribeDocument(ObjectFieldDescribeDocument.of(fieldDescribe));
        return result;
    }

    public FindBySelectFields.Result findBySelectFields(FindBySelectFields.Arg arg, RequestContext context) {
        Map<String, DynamicDescribe> dynamicDescribeMap = describeLogicService.findBySelectFields(context.getTenantId(),
                arg.getDescribeAndFields(), arg.getDescribeSelects(), arg.getFieldSelects());
        Map<String, ObjectDescribeDocument> describeMap = Maps.newHashMap();
        dynamicDescribeMap.forEach((k, v) -> describeMap.put(k, ObjectDescribeDocument.of(v, arg.getDescribeSelects(), arg.getFieldSelects())));

        return FindBySelectFields.Result.builder().describeMap(describeMap).build();
    }

    public FindDescribeApiNames.Result findAllDescribeApiNames(RequestContext context) {
        List<IObjectDescribe> allDescribeApiNames = describeLogicService.findAllObjectsByTenantId(context.getTenantId(),
                "", false, false,
                false, false, "");
        List<String> describeApiNames = allDescribeApiNames.stream().map(IObjectDescribe::getApiName).collect(Collectors.toList());
        return FindDescribeApiNames.Result.builder().describeApiNames(describeApiNames).build();
    }

    public QueryDisplayNameByApiNames.Result queryDisplayNameByApiNames(QueryDisplayNameByApiNames.Arg arg, RequestContext context) {
        Map<String, String> displayNames = describeLogicService.queryDisplayNameByApiNames(context.getTenantId(), arg.getApiNames(), arg.getIncludeInvalid());
        return QueryDisplayNameByApiNames.Result.builder().displayNames(displayNames).build();
    }

    public FindSimpleDetailDescribes.Result findSimpleDetailDescribes(FindSimpleDetailDescribes.Arg arg, RequestContext context) {
        List<IObjectDescribe> detailDescribes = describeLogicService.findSimpleDetailDescribes(context.getTenantId(), arg.getMasterApiName());
        List<SimpleDescribe> simpleDetailDescribes = detailDescribes.stream()
                .map(x -> SimpleDescribe.builder()
                        .apiName(x.getApiName())
                        .displayName(x.getDisplayName())
                        .defineType(x.getDefineType())
                        .active(!Boolean.FALSE.equals(x.isActive()))
                        .build())
                .collect(Collectors.toList());
        return FindSimpleDetailDescribes.Result.builder().detailDescribeList(simpleDetailDescribes).build();
    }

    public RefRecord.Result recordRef(RefRecord.Arg arg, RequestContext context) {

        if (CollectionUtils.empty(arg.getTenantIdList())) {
            return RefRecord.Result.builder().msg("no tenant need to record ref by where_used")
                    .successTenantIdList(null)
                    .build();
        }

        List<String> noGrayTenantIdList = Lists.newArrayList();
        List<String> successTenantIdList = Lists.newArrayList();
        List<String> failTenantIdList = Lists.newArrayList();
        for (String tenantId : arg.getTenantIdList()) {
            if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FIND_WHERE_A_FIELD_IS_USED_GRAY_KEY, tenantId)) {
                noGrayTenantIdList.add(tenantId);
                continue;
            }
            try {
                // 查所有对象
                ObjectDescribeFinder objectDescribeFinder = ObjectDescribeFinder.builder()
                        .user(User.systemUser(tenantId))
                        .isExcludeDetailObj(false)
                        .isExcludeDetailWithMasterCreated(false)
                        .sourceInfo(ObjectListConfig.OBJECT_MANAGEMENT)
                        .build();
                List<IObjectDescribe> describeList = describeLogicService.findObjectsByTenantId(objectDescribeFinder);
                // 遍历所有对象的 字段 布局
                for (IObjectDescribe describe : describeList) {
                    if (CollectionUtils.notEmpty(arg.getObjApiList()) && !arg.getObjApiList().contains(describe.getApiName())) {
                        continue;
                    }
                    DescribeAndLayoutList.Result result = describeLogicService.findDescribeAndLayoutList(tenantId, describe.getApiName());
                    IObjectDescribe descWithFields = result.getDescribe();

                    List<ILayout> layoutList = result.getLayoutList();
                    if (CollectionUtils.notEmpty(arg.getLayoutApiList())) {
                        layoutList = layoutList.stream().filter(layout -> arg.getLayoutApiList().contains(layout.getName())).collect(Collectors.toList());
                    }
                    List<IFieldDescribe> fieldList = descWithFields.getFieldDescribes();
                    if (CollectionUtils.notEmpty(arg.getFieldApiList())) {
                        fieldList = fieldList.stream().filter(field -> arg.getFieldApiList().contains(field.getApiName())).collect(Collectors.toList());
                    }

                    describeLogicService.executeReferenceByField(ActionType.DELETE_AND_CREATE, fieldList, descWithFields);

                    serviceFacade.getLayoutLogicService().executeReferenceByLayout(ActionType.DELETE_AND_CREATE, descWithFields, layoutList);
                }

                successTenantIdList.add(tenantId);
            } catch (Exception e) {
                failTenantIdList.add(tenantId);
            }
        }
        return RefRecord.Result.builder()
                .noGrayTenantIdList(noGrayTenantIdList)
                .successTenantIdList(successTenantIdList)
                .failTenantIdList(failTenantIdList)
                .build();
    }

}

