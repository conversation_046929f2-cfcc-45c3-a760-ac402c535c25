package com.facishare.paas.appframework.core.timezone;

import com.facishare.paas.appframework.core.timezone.annotation.ObjectDataField;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.function.Function;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/06/17
 */
public interface ObjectDataFieldConvert {

    ObjectDataField.Type getType();

    <T> T convert2SystemZone(T value, Function<String, IObjectDescribe> function);

    <T> T convert2CustomZone(T value, Function<String, IObjectDescribe> function);
}
