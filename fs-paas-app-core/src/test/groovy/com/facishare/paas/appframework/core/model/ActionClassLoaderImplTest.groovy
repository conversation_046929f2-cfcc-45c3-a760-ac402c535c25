package com.facishare.paas.appframework.core.model

import com.facishare.paas.appframework.core.predef.action.StandardAction
import com.facishare.paas.appframework.core.predef.action.StandardAddAction
import spock.lang.Specification

/**
 * Created by liyiguang on 2017/6/21.
 */
class ActionClassLoaderImplTest extends Specification {

    def "test loadActionClass"() {
        given:
        def classLoader = new ActionClassLoaderImpl()
        when:
        def clazz = classLoader.loadActionClass(actionCLassInfo)
        then:
        clazz == expectedClass
        where:
        actionCLassInfo                                                          || expectedClass
        new ActionClassInfo(StandardAction.Add.defaultActionClassInfo.className) || StandardAddAction.class
    }

    def "test loadPreDefineActionClass"() {
        given:
        def classLoader = new ActionClassLoaderImpl()
        when:
        def clazz = classLoader.loadPreDefineActionClass(actionCLassInfo)
        then:
        clazz == expectedClass
        where:
        actionCLassInfo                                                          || expectedClass
        new ActionClassInfo(StandardAction.Add.defaultActionClassInfo.className) || StandardAddAction.class
    }

    def "test loadCustomerAction"() {

    }
}
