package com.facishare.paas.appframework.metadata.autonumber;

import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.AutoNumberInfo;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/23
 */

@FunctionalInterface
public interface UpsertAutoNumberInfo {

    AutoNumberInfo upsert() throws MetadataServiceException;

    /**
     * upsert autoNumber 获取锁的key
     *
     * @param tenantId
     * @param describeApiName
     * @param fieldName
     * @return
     */
    static String getAutoNumberUpsertLockKey(String tenantId, String describeApiName, String fieldName) {
        return "autoNumberUpsertLock_" + tenantId + "_" + describeApiName + "_" + fieldName;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        private boolean create;
        private AutoNumberInfo autoNumberInfo;
    }
}
