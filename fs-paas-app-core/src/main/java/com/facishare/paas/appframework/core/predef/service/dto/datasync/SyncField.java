package com.facishare.paas.appframework.core.predef.service.dto.datasync;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.Delegate;

import java.util.List;
import java.util.Set;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/03/04
 */
public interface SyncField {
    @Data
    class Arg {
        /**
         * 对象api_name
         */
        @JsonProperty("describe_api_name")
        private String describeApiName;
        /**
         * 需要同步的字段apiname
         */
        @JsonProperty("field_api_names")
        private Set<String> fieldApiNames;
        /**
         * 下游的企业id
         */
        @JsonProperty("tenant_ids")
        private Set<String> tenantIds;
        /**
         * 是否锁定下游配置
         */
        @JsonProperty("is_lock_downstram")
        private boolean lockDownstram;
    }

    @Data
    @Builder
    class Result {
        protected List<SyncErrorInfo> errorInfo;
    }

    @ToString
    @EqualsAndHashCode
    class SyncErrorInfo {
        @Getter
        private String tenantId;
        @Delegate
        @JsonIgnore
        private SyncError syncError;

        private SyncErrorInfo(String tenantId, SyncError syncError) {
            this.tenantId = tenantId;
            this.syncError = syncError;
        }

        public static SyncErrorInfo of(String tenantId, SyncError syncError) {
            return new SyncErrorInfo(tenantId, syncError);
        }
    }

    @Getter
    enum SyncError {
        TENANT_NOT_EXIT("4001", "tenant_not_exit"),
        OBJECT_NOT_EXIT("4002", "object_not_exit"),
        UPDATE_OBJECT_FIL("5003", "update_object_fil"),
        APP_BUSINESS_ERROR("403", "app_business_error"),
        SYSTEM_ERROR("500", "system_error"),
        ;

        SyncError(String code, String message) {
            this.code = code;
            this.message = message;
        }

        private String code;
        private String message;
    }
}
