package com.facishare.paas.appframework.privilege;

import com.facishare.organization.adapter.api.model.organizationwithouter.OrganizationEmployee;
import com.facishare.paas.appframework.common.service.OuterOrganizationService;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.util.LicenseConstants.ModuleCode;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.privilege.dto.SupportEnterpriseRelationResult;
import com.facishare.paas.appframework.privilege.model.EnterpriseRelationTeamMemberStrategy;
import com.facishare.paas.appframework.privilege.model.EnterpriseRelationTeamMemberStrategy.TeamMemberStrategy;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.fxiaoke.enterpriserelation2.result.OuterAccountVo;
import com.fxiaoke.enterpriserelation2.result.RelationDownstreamResult;
import com.fxiaoke.enterpriserelation2.result.data.RelationEmployeeIdInfoData;
import com.google.common.collect.*;
import lombok.*;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2021/08/25
 */
@Service("enterpriseRelationLogicService")
public class EnterpriseRelationLogicServiceImpl implements EnterpriseRelationLogicService {
    @Autowired
    private EnterpriseRelationServiceProxy enterpriseRelationService;

    @Autowired
    private OuterOrganizationService outerOrganizationService;

    @Autowired
    private LicenseService licenseService;

    @Autowired
    private OptionalFeaturesService optionalFeaturesService;

    @Override
    public void fillOutOwner(User user, IObjectDescribe describe, Collection<IObjectData> dataList) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        if (describeExt.isSlaveObject()) {
            return;
        }
        for (IObjectReferenceField objectReferenceField : describeExt.getSupportRelationOuterOwnerFields()) {
            List<IObjectData> objectDataList = dataList.stream()
                    .filter(objectData -> CollectionUtils.empty(objectData.getOutOwner()))
                    .collect(Collectors.toList());
            if (CollectionUtils.empty(objectDataList)) {
                return;
            }
            String fieldApiName = objectReferenceField.getApiName();
            Set<String> referenceObjectDataIds = objectDataList.stream()
                    .map(data -> data.get(fieldApiName, String.class))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());

            if (CollectionUtils.empty(referenceObjectDataIds)) {
                continue;
            }
            // 根据『客户』、『联系人』查询对应的下游主负责人
            String targetApiName = objectReferenceField.getTargetApiName();
            Map<String, OutUserInfo> resultMap = getOwnerOutUserByDataIds(user, targetApiName, referenceObjectDataIds, false);
            for (IObjectData data : objectDataList) {
                String partnerId = data.get(fieldApiName, String.class);
                Optional.ofNullable(resultMap.get(partnerId)).ifPresent(it -> {
                    String outTenantId = it.getOutTenantId();
                    String outUserId = it.getOutUserId();
                    data.setOutOwner(Lists.newArrayList(outUserId));
                    data.setOutTenantId(outTenantId);
                    // 同步外部相关团队负责人
                    ObjectDataExt.of(data).synchronizeOutTeamMemberOwner(outTenantId, outUserId);
                });
            }
        }
    }

    @Override
    public void fillOutTeamMember(User user, IObjectDescribe describe, Collection<IObjectData> dataList) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ENTERPRISE_RELATION_SUPPORT_TEAM_MEMBER, user.getTenantId())) {
            return;
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        // 从对象不需要处理相关团队
        if (describeExt.isSlaveObject()) {
            return;
        }
        if (user.isOutGuestUser()) {
            return;
        }
        // 判断对象有没有开启相关团队的开关
        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), describe);
        if (BooleanUtils.isFalse(optionalFeaturesSwitch.getIsRelatedTeamEnabled())) {
            return;
        }
        List<IObjectReferenceField> relationFields = describeExt.getSupportRelationFields();
        if (CollectionUtils.empty(relationFields)) {
            return;
        }
        ReferenceDataContainer referenceDataContainer = new ReferenceDataContainer();
        for (IObjectReferenceField objectReferenceField : relationFields) {
            String fieldApiName = objectReferenceField.getApiName();
            Set<String> referenceObjectDataIds = dataList.stream()
                    .map(data -> data.get(fieldApiName, String.class))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());
            String targetApiName = objectReferenceField.getTargetApiName();
            referenceDataContainer.add(targetApiName, referenceObjectDataIds, FieldDescribeExt.of(objectReferenceField).isSupportRelationNeedUserInfo());
        }
        // 根据数据id查询下游企业和下游对接人
        Table<String, String, OutUserInfo> userTable = findOwnerOutUserByDataIds(user, referenceDataContainer);

        for (IObjectReferenceField objectReferenceField : relationFields) {
            String targetApiName = objectReferenceField.getTargetApiName();
            String fieldApiName = objectReferenceField.getApiName();
            for (IObjectData data : dataList) {
                String referenceId = data.get(fieldApiName, String.class);
                Optional.ofNullable(userTable.get(targetApiName, referenceId)).ifPresent(it -> {
                    // 根据不同的策略合并相关团队成员
                    TeamMemberStrategy strategy = EnterpriseRelationTeamMemberStrategy.getStrategy(objectReferenceField.getRelationOuterDataPrivilege());
                    TeamMember teamMember = strategy.getTeamMember(it.getOutTenantId(), it.getOutUserId());
                    if (Objects.nonNull(teamMember)) {
                        ObjectDataExt.of(data).addOuterTeamMembers(Lists.newArrayList(teamMember));
                    }
                });
            }
        }
    }

    @Override
    public void syncOutTenantIdFromOutUser(User user, IObjectDescribe describe, Collection<IObjectData> objectDataList) {
        List<String> outOwners = objectDataList.stream().map(ObjectDataExt::of)
                .map(it -> it.getOutOwnerId().orElse(null))
                .filter(StringUtils::isNotBlank)
                .filter(NumberUtils::isCreatable)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.empty(outOwners)) {
            return;
        }
        // 查询下游人员所属的下游企业
        List<OrganizationEmployee> organizationEmployees = outerOrganizationService.batchGetEmployee(user.getTenantId(), outOwners);
        Map<String, String> userId2TenantIdMap = organizationEmployees.stream()
                .collect(Collectors.toMap(OrganizationEmployee::getEmployeeId, OrganizationEmployee::getMainDepartmentId));

        for (IObjectData data : objectDataList) {
            ObjectDataExt dataExt = ObjectDataExt.of(data);
            dataExt.getOutOwnerId().ifPresent(outOwnerId -> {
                String outTenantId = userId2TenantIdMap.get(outOwnerId);
                if (StringUtils.isNotBlank(outTenantId)) {
                    dataExt.setOutTenantId(outTenantId);
                    dataExt.synchronizeOutTeamMemberOwner(outTenantId, outOwnerId);
                }
            });
        }
    }


    @Override
    public Map<String, User> getOwnerOutUserByDataIds(User user, String objectApiName, Set<String> referenceObjectDataIds) {
        Map<String, OutUserInfo> userInfoMap = getOwnerOutUserByDataIds(user, objectApiName, referenceObjectDataIds, true);
        Map<String, User> result = Maps.newHashMap();
        if (CollectionUtils.empty(userInfoMap)) {
            return result;
        }
        userInfoMap.forEach((key, outUserInfo) -> result.put(key, outUserInfo.toUser()));
        return result;
    }

    @Override
    public boolean supportInterconnectBaseAppLicense(String tenantId) {
        Map<String, Boolean> licenseResult = licenseService.existModule(tenantId, Sets.newHashSet(ModuleCode.INTERCONNECT_APP_BASIC_APP));
        return licenseResult.getOrDefault(ModuleCode.INTERCONNECT_APP_BASIC_APP, false);
    }

    @Override
    public SupportEnterpriseRelationResult isSupportEnterpriseRelation(String tenantId) {
        if (!supportInterconnectBaseAppLicense(tenantId)) {
            return SupportEnterpriseRelationResult.of(false, false);
        }
        boolean support = UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ENTERPRISE_RELATION_SUPPORT_TEAM_MEMBER, tenantId);
        return SupportEnterpriseRelationResult.of(true, support);
    }

    @Override
    public List<UserInfo> listOuterInfo(User user, Collection<String> names) {
        Map<String, List<OuterAccountVo>> listMap = outerOrganizationService.listOuterOwnerByNames(user, names);
        if (CollectionUtils.empty(listMap)) {
            return Lists.newArrayList();
        }

        List<UserInfo> resultList = Lists.newArrayList();
        listMap.forEach((name, accounts) -> {
            accounts.forEach(it -> {
                UserInfo userInfo = UserInfo.builder()
                        .id(String.valueOf(it.getOuterUid()))
                        .tenantId(String.valueOf(it.getOuterTenantId()))
                        .name(name)
                        .nickname(name)
                        .build();
                resultList.add(userInfo);
            });
        });
        return resultList;
    }


    @Override
    public List<UserInfo> getOutUserInfoByEnterpriseAndNames(User user, Collection<String> names) {
        List<RelationEmployeeIdInfoData> outUserInfos = outerOrganizationService.batchGetOutUserInfoByEnterpriseAndNames(user, names);
        if (CollectionUtils.empty(outUserInfos)) {
            return Lists.newArrayList();
        }

        List<UserInfo> resultList = Lists.newArrayList();
        for (RelationEmployeeIdInfoData outUserInfo : outUserInfos) {
            UserInfo userInfo = UserInfo.builder()
                    .id(String.valueOf(outUserInfo.getOuterUserId()))
                    .tenantId(String.valueOf(outUserInfo.getOuterTenantId()))
                    .name(outUserInfo.getEnterpriseName() + "." + outUserInfo.getEmployeeName())
                    .nickname(outUserInfo.getEnterpriseName() + "." + outUserInfo.getEmployeeName())
                    .build();
            resultList.add(userInfo);
        }
        return resultList;
    }

    /**
     * 1、先按合作伙伴id 查询对应的下游企业
     * 2、根据下游企业查询主对接人
     * 3、查询主对接人的人员信息
     *
     * @param user
     * @param objectApiName
     * @param referenceObjectDataIds
     * @param needUserName
     * @return
     */
    private Map<String, OutUserInfo> getOwnerOutUserByDataIds(User user, String objectApiName,
                                                              Set<String> referenceObjectDataIds,
                                                              boolean needUserName) {
        Map<String, String> tenantMap = getOutTenantByDataIds(user, objectApiName, referenceObjectDataIds);
        if (CollectionUtils.empty(tenantMap)) {
            return Collections.emptyMap();
        }

        Map<String, OutUserInfo> result = Maps.newHashMap();
        Map<String, OutUserInfo> ownerOutUserCache = Maps.newHashMap();
        tenantMap.forEach((dataId, outTenantId) -> {
            OutUserInfo outUser = ownerOutUserCache.get(outTenantId);
            if (Objects.nonNull(outUser)) {
                result.put(dataId, outUser);
                return;
            }
            outerOrganizationService.getOwnerOutUserByOutTenant(user, outTenantId).ifPresent(it -> {
                OutUserInfo outUserInfo = OutUserInfo.fromUser(it);
                ownerOutUserCache.put(outTenantId, outUserInfo);
                result.put(dataId, outUserInfo);
            });
        });
        if (!needUserName) {
            return result;
        }
        Set<String> outUserIds = result.values().stream()
                .map(OutUserInfo::getOutUserId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        List<OrganizationEmployee> organizationEmployees = outerOrganizationService.batchGetEmployee(user.getTenantId(), outUserIds);
        Map<String, OrganizationEmployee> employeeMap = organizationEmployees.stream().collect(Collectors.toMap(OrganizationEmployee::getEmployeeId,
                Function.identity()));
        result.forEach((key, value) -> {
            OrganizationEmployee employee = employeeMap.get(value.getOutUserId());
            if (Objects.nonNull(employee)) {
                value.setUserName(employee.getName());
            }
        });

        return result;
    }

    private Map<String, String> getOutTenantByDataIds(User user, String objectApiName,
                                                      Set<String> referenceObjectDataIds) {
        if (CollectionUtils.empty(referenceObjectDataIds)) {
            return Collections.emptyMap();
        }
        Map<String, RelationDownstreamResult> relationDownstreamResultMap = enterpriseRelationService.getRelationDownstreamInfo(
                user.getTenantId(), objectApiName, referenceObjectDataIds);
        if (CollectionUtils.empty(relationDownstreamResultMap)) {
            return Collections.emptyMap();
        }

        Map<String, String> result = Maps.newHashMap();
        relationDownstreamResultMap.forEach((dataId, relationDownstreamResult) -> {
            // 优先从查询的结果中取
            String outTenantId = relationDownstreamResult.getDownstreamOuterTenantId().toString();
            result.put(dataId, outTenantId);
        });
        return result;
    }

    private Table<String, String, OutUserInfo> findOwnerOutUserByDataIds(User user, ReferenceDataContainer referenceDataContainer) {

        Table<String, String, OutUserInfo> result = HashBasedTable.create();
        Map<String, Set<String>> needUser = referenceDataContainer.getNeedUser();
        needUser.forEach((objectApiName, dataIds) -> {
            Map<String, OutUserInfo> userMap = getOwnerOutUserByDataIds(user, objectApiName, dataIds, false);
            userMap.forEach((id, userInfo) -> result.put(objectApiName, id, userInfo));
        });

        Map<String, Set<String>> onlyTenant = referenceDataContainer.getOnlyTenant();
        onlyTenant.forEach((objectApiName, dataIds) -> {
            Map<String, String> tenantMap = getOutTenantByDataIds(user, objectApiName, dataIds);
            tenantMap.forEach((id, outTenantId) -> {
                OutUserInfo outUserInfo = new OutUserInfo(outTenantId, null);
                result.put(objectApiName, id, outUserInfo);
            });
        });
        return result;
    }

    private static class ReferenceDataContainer {

        private final Map<String, ReferenceDataItem> map = Maps.newHashMap();

        public void add(String describeApiName, Set<String> dataIds, boolean needUser) {
            if (CollectionUtils.empty(dataIds)) {
                return;
            }
            dataIds.stream()
                    .map(id -> new ReferenceDataItem(describeApiName, id, needUser))
                    .forEach(this::add);

        }

        public void add(ReferenceDataItem item) {
            if (Objects.isNull(item)) {
                return;
            }
            String matchKey = item.getMatchKey();
            map.compute(matchKey, (key, oldValue) -> {
                if (Objects.isNull(oldValue)) {
                    return item;
                }
                return oldValue.merge(item);
            });
        }

        public Map<String, Set<String>> getNeedUser() {
            return map.values().stream()
                    .filter(ReferenceDataItem::isNeedUser)
                    .collect(Collectors.groupingBy(ReferenceDataItem::getDescribeApiName,
                            Collectors.mapping(ReferenceDataItem::getDataId, Collectors.toSet())));
        }

        public Map<String, Set<String>> getOnlyTenant() {
            return map.values().stream()
                    .filter(it -> !it.isNeedUser())
                    .collect(Collectors.groupingBy(ReferenceDataItem::getDescribeApiName,
                            Collectors.mapping(ReferenceDataItem::getDataId, Collectors.toSet())));
        }
    }

    @Getter
    @ToString
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    private static class ReferenceDataItem {
        private String describeApiName;
        private String dataId;
        private boolean needUser;

        public static boolean match(ReferenceDataItem item1, ReferenceDataItem item2) {
            if (Objects.isNull(item1) || Objects.isNull(item2)) {
                return false;
            }
            return Objects.equals(item1.getMatchKey(), item2.getMatchKey());
        }

        public String getMatchKey() {
            return String.format("%s__%s", describeApiName, dataId);
        }

        public ReferenceDataItem merge(ReferenceDataItem item) {
            if (!match(this, item)) {
                return null;
            }
            return new ReferenceDataItem(describeApiName, dataId, isNeedUser() || item.isNeedUser());
        }
    }

    @Data
    private static class OutUserInfo {
        private final String outTenantId;
        private final String outUserId;
        private String userName;

        public OutUserInfo(String outTenantId, String outUserId) {
            this.outTenantId = outTenantId;
            this.outUserId = outUserId;
        }

        public static OutUserInfo fromUser(User user) {
            return new OutUserInfo(user.getOutTenantId(), user.getOutUserId());
        }

        public User toUser() {
            User user = User.builder()
                    .outTenantId(outTenantId)
                    .outUserId(outUserId)
                    .build();
            user.setUserName(userName);
            return user;
        }
    }
}

