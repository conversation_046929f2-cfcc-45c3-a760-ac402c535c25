package com.facishare.paas.appframework.core.predef.service.dto.gdpr;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface BatchUpdateGdprLegalBase {

    @Data
    class Arg {
        private String apiName;
        private List<String> dataIds;
        private String legalBase;
    }

    @Data
    @Builder
    class Result {
        //private boolean success;
    }
}
