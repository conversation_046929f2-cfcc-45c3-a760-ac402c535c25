package com.facishare.paas.appframework.core.predef.handler;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.handler.*;
import com.facishare.paas.appframework.function.plugin.FunctionPluginService;
import com.fxiaoke.transaction.tcc.annotation.TccTransactional;
import com.fxiaoke.transaction.tcc.api.context.BranchTransactionalContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * Created by zhouwr on 2023/4/21.
 */
@Slf4j
@Component
@HandlerProvider(name = "defaultAPLTCCActionHandler")
public class DefaultAPLTCCActionHandler implements TCCActionHandler<Handler.Arg, Handler.Result> {

    @Autowired
    private FunctionPluginService functionPluginService;

    @Override
    @TccTransactional(name = "handle", confirmMethod = "commit", cancelMethod = "rollback")
    public Result handle(HandlerContext context, Arg arg) {
        BranchTransactionalContext branchTransactionalContext = BranchTransactionalContext.getCurrent();
        Result result = executeFunction(branchTransactionalContext, context, arg, HANDLE);
        //合并contextData到分布式事务的上下文中
        if (Objects.nonNull(result) && CollectionUtils.notEmpty(result.getContextData())) {
            branchTransactionalContext.setActionContext(result.getContextData());
        }
        return result;
    }

    @Override
    public boolean commit(BranchTransactionalContext branchTransactionalContext, HandlerContext context, Arg arg) {
        return executeFunction(branchTransactionalContext, context, arg, COMMIT);
    }

    @Override
    public boolean rollback(BranchTransactionalContext branchTransactionalContext, HandlerContext context, Arg arg) {
        return executeFunction(branchTransactionalContext, context, arg, ROLLBACK);
    }

    private <T> T executeFunction(BranchTransactionalContext branchTransactionalContext, HandlerContext context, Arg arg, String method) {
        FunctionPluginService.FunctionPluginContext functionPluginContext = FunctionPluginService.FunctionPluginContext.builder()
                .user(context.getUser())
                .argProcess(() -> buildArg(branchTransactionalContext, arg))
                .resultProcess(jsonString -> {
                    APLResult aplResult = buildResult(arg, jsonString);
                    if (COMMIT.equals(method) || ROLLBACK.equals(method)) {
                        return aplResult.success();
                    }
                    return aplResult.getResult();
                })
                .build();
        return (T) functionPluginService.executeFuncMethod(functionPluginContext, arg.getHandlerDescribe().getAplApiName(), method);
    }

    private List<String> buildArg(BranchTransactionalContext branchTransactionalContext, Arg arg) {
        return Lists.newArrayList(JacksonUtils.toJson(branchTransactionalContext), JacksonUtils.toJson(arg));
    }

    protected Class<? extends Result> getResultType(Arg arg) {
        return HandlerResultTypeMappings.getHandlerResultType(arg.getHandlerDescribe().getInterfaceCode());
    }

    private APLResult buildResult(Arg arg, String jsonString) {
        if (Strings.isNullOrEmpty(jsonString)) {
            log.warn("APL handler return empty result,aplApiName:{}", arg.getHandlerDescribe().getAplApiName());
            return new APLResult();
        }
        Class<? extends Result> resultType = getResultType(arg);
        if (Objects.isNull(resultType)) {
            throw new ValidateException("Cannot find result type for interfaceCode: " + arg.getHandlerDescribe().getInterfaceCode());
        }
        APLResult aplResult = JacksonUtils.fromJson(jsonString, APLResult.class, resultType);
        if (!aplResult.success()) {
            log.warn("execute APL handler failed,aplApiName:{},result:{}", arg.getHandlerDescribe().getAplApiName(), jsonString);
            throw new ValidateException(aplResult.getErrorMessage());
        }
        return aplResult;
    }

}
