package com.facishare.crm.common.exception

import spock.lang.Specification

/**
 * GenerateByAI
 * 测试内容描述：MasterObjectDataNotExistException异常类的单元测试
 */
class MasterObjectDataNotExistExceptionTest extends Specification {

    def "test constructor with CRMErrorCode and message"() {
        given: "准备错误码和消息"
        def errorCode = CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NOT_EXIST_DATA
        def message = "Master object data does not exist"

        when: "创建异常实例"
        def exception = new MasterObjectDataNotExistException(errorCode, message)

        then: "验证异常属性"
        exception.getErrorCode() == errorCode
        exception.getMessage() == message
        exception.getCause() == null
    }

    def "test constructor with CRMErrorCode, message and cause"() {
        given: "准备错误码、消息和原因"
        def errorCode = CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NOT_EXIST_DATA
        def message = "Master object data not found"
        def cause = new RuntimeException("Database connection failed")

        when: "创建异常实例"
        def exception = new MasterObjectDataNotExistException(errorCode, message, cause)

        then: "验证异常属性"
        exception.getErrorCode() == errorCode
        exception.getMessage() == message
        exception.getCause() == cause
    }

    def "test getIntErrorCode method"() {
        given: "创建异常实例"
        def errorCode = CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NOT_EXIST_DATA
        def exception = new MasterObjectDataNotExistException(errorCode, "Data not exist")

        when: "调用getIntErrorCode方法"
        def intErrorCode = exception.getIntErrorCode()

        then: "应该返回null（因为errorCode字段未设置）"
        intErrorCode == null
    }



    def "test with null message"() {
        given: "使用null消息"
        def errorCode = CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NOT_EXIST_DATA

        when: "创建异常实例"
        def exception = new MasterObjectDataNotExistException(errorCode, null)

        then: "验证异常属性"
        exception.getErrorCode() == errorCode
        exception.getMessage() == null
    }

    def "test with null cause"() {
        given: "使用null原因"
        def errorCode = CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NOT_EXIST_DATA
        def message = "Test with null cause"

        when: "创建异常实例"
        def exception = new MasterObjectDataNotExistException(errorCode, message, null)

        then: "验证异常属性"
        exception.getErrorCode() == errorCode
        exception.getMessage() == message
        exception.getCause() == null
    }

    def "test exception inheritance"() {
        given: "创建异常实例"
        def exception = new MasterObjectDataNotExistException(CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NOT_EXIST_DATA, "Test")

        expect: "验证继承关系"
        exception instanceof Exception
        exception instanceof CrmErrorInter
    }

    def "test CrmErrorInter interface implementation"() {
        given: "创建异常实例"
        def errorCode = CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NOT_EXIST_DATA
        def exception = new MasterObjectDataNotExistException(errorCode, "Master data not found")

        when: "通过接口调用方法"
        CrmErrorInter errorInter = exception
        def result = errorInter.getErrorCode()

        then: "应该返回正确的错误码"
        result == errorCode
    }

    def "test with different CRMErrorCode values"() {
        given: "不同的错误码"
        def testCases = [
            CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NOT_EXIST_DATA,
            CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NORELATION_BETWEEN_OBJECTS,
            CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NORELATION_BETWEEN_OBJECT_DATAS,
            CRMErrorCode.FS_CRM_COMMON_CHECKED_PARAMETER_INVALID,
            CRMErrorCode.SYSTEM_ERROR
        ]

        expect: "所有错误码都能正确设置"
        testCases.each { errorCode ->
            def exception = new MasterObjectDataNotExistException(errorCode, "Test message")
            assert exception.getErrorCode() == errorCode
        }
    }

    def "test exception message handling"() {
        given: "不同类型的消息"
        def errorCode = CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NOT_EXIST_DATA
        def testMessages = [
            "Simple message",
            "Message with special characters: !@#\$%^&*()",
            "中文消息测试",
            "",
            "Very long message that contains multiple sentences and should be handled properly by the exception class without any issues."
        ]

        expect: "所有消息都能正确处理"
        testMessages.each { message ->
            def exception = new MasterObjectDataNotExistException(errorCode, message)
            assert exception.getMessage() == message
        }
    }

    def "test exception cause chain"() {
        given: "创建异常链"
        def rootCause = new IllegalArgumentException("Root cause")
        def middleCause = new RuntimeException("Middle cause", rootCause)
        def errorCode = CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NOT_EXIST_DATA
        def message = "Master object data not exist"

        when: "创建异常实例"
        def exception = new MasterObjectDataNotExistException(errorCode, message, middleCause)

        then: "验证异常链"
        exception.getCause() == middleCause
        exception.getCause().getCause() == rootCause
        exception.getCause().getMessage() == "Middle cause"
        exception.getCause().getCause().getMessage() == "Root cause"
    }

    def "test toString method"() {
        given: "创建异常实例"
        def errorCode = CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NOT_EXIST_DATA
        def message = "Master object data not found"
        def exception = new MasterObjectDataNotExistException(errorCode, message)

        when: "调用toString方法"
        def result = exception.toString()

        then: "应该包含异常信息"
        result != null
        result.contains("MasterObjectDataNotExistException")
        result.contains(message)
    }

    def "test stack trace functionality"() {
        given: "创建异常实例"
        def errorCode = CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NOT_EXIST_DATA
        def message = "Master object data does not exist"

        when: "创建异常"
        def exception = new MasterObjectDataNotExistException(errorCode, message)

        then: "应该有堆栈跟踪信息"
        exception.getStackTrace() != null
        exception.getStackTrace().length > 0
    }

    def "test serialization compatibility"() {
        given: "创建异常实例"
        def errorCode = CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NOT_EXIST_DATA
        def message = "Master object data not exist"
        def exception = new MasterObjectDataNotExistException(errorCode, message)

        expect: "异常应该是可序列化的（Exception实现了Serializable）"
        exception instanceof java.io.Serializable
    }

    def "test specific error scenarios"() {
        expect: "验证特定错误场景"
        // 主对象数据不存在
        def notExistException = new MasterObjectDataNotExistException(
            CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NOT_EXIST_DATA, 
            "Master object with ID 12345 does not exist"
        )
        notExistException.getErrorCode() == CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NOT_EXIST_DATA

        // 对象间无关联关系
        def noRelationException = new MasterObjectDataNotExistException(
            CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NORELATION_BETWEEN_OBJECTS,
            "No relation between master and detail objects"
        )
        noRelationException.getErrorCode() == CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NORELATION_BETWEEN_OBJECTS
    }

    def "test exception with empty and whitespace messages"() {
        given: "创建异常实例"
        def errorCode = CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_NOT_EXIST_DATA

        expect: "验证空消息和空白字符消息的处理"
        def emptyException = new MasterObjectDataNotExistException(errorCode, "")
        emptyException.getMessage() == ""

        def whitespaceException = new MasterObjectDataNotExistException(errorCode, "   ")
        whitespaceException.getMessage() == "   "

        def tabException = new MasterObjectDataNotExistException(errorCode, "\t\n")
        tabException.getMessage() == "\t\n"
    }
}
