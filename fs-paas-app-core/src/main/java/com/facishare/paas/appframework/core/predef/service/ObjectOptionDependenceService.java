package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.option.dependence.*;
import com.facishare.paas.appframework.metadata.options.SelectFieldDependenceLogicService;
import com.facishare.paas.appframework.metadata.options.bo.SelectFieldDependence;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/8/1
 */
@Service
@ServiceModule("option_dependence")
public class ObjectOptionDependenceService {

    @Autowired
    private SelectFieldDependenceLogicService dependenceLogicService;

    @ServiceMethod("findAll")
    public FindAllOptionDependence.Result findAllOptionDependence(ServiceContext context, FindAllOptionDependence.Arg arg) {
        if (StringUtils.isAnyEmpty(arg.getDescribeApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        List<SelectFieldDependence> dependenceList = dependenceLogicService.findFieldDependenceWitchObjectApiName(context.getUser(), arg.getDescribeApiName());
        return FindAllOptionDependence.Result.of(FieldDependenceDTO.fromList(dependenceList));
    }

    @ServiceMethod("find")
    public FindOptionDependence.Result findOptionDependence(ServiceContext context, FindOptionDependence.Arg arg) {
        if (StringUtils.isAnyEmpty(arg.getDescribeApiName(), arg.getFieldApiName(), arg.getChildFieldName())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        Optional<SelectFieldDependence> selectFieldDependence = dependenceLogicService.find(context.getUser(), arg.getDescribeApiName(), arg.getFieldApiName(), arg.getChildFieldName());
        FieldDependenceDTO dependenceDTO = selectFieldDependence.map(FieldDependenceDTO::from).orElse(null);
        return FindOptionDependence.Result.of(dependenceDTO);
    }

    @ServiceMethod("create")
    public CreateOptionDependence.Result createOptionDependence(ServiceContext context, CreateOptionDependence.Arg arg) {
        FieldDependenceDTO fieldDependence = arg.getFieldDependence();
        if (Objects.isNull(fieldDependence)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        dependenceLogicService.create(context.getUser(), fieldDependence.convert());
        return new CreateOptionDependence.Result();
    }

    @ServiceMethod("update")
    public CreateOptionDependence.Result updateOptionDependence(ServiceContext context, CreateOptionDependence.Arg arg) {
        FieldDependenceDTO fieldDependence = arg.getFieldDependence();
        if (Objects.isNull(fieldDependence)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        dependenceLogicService.update(context.getUser(), fieldDependence.convert());
        return new CreateOptionDependence.Result();
    }

    @ServiceMethod("deleted")
    public DeletedOptionDependence.Result deletedOptionDependence(ServiceContext context, DeletedOptionDependence.Arg arg) {
        if (StringUtils.isAnyEmpty(arg.getDescribeApiName(), arg.getFieldApiName(), arg.getChildFieldName())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        dependenceLogicService.deleted(context.getUser(), arg.getDescribeApiName(), arg.getFieldApiName(), arg.getChildFieldName());
        return new DeletedOptionDependence.Result();
    }
}
