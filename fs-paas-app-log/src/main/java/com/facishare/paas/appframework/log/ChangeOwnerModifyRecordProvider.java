package com.facishare.paas.appframework.log;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.TeamMemberInfo;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.TeamRoleInfo;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component("changeOwnerModifyRecordProvider")
public class ChangeOwnerModifyRecordProvider extends TeamMemberModifyRecordProvider {
    @Override
    public String getApiName() {
        return ActionType.ChangeOwner.getId();
    }

    @Override
    protected List<TeamMemberInfo.Msg> parseTeamMemberMsgs(List<TeamMemberInfo.Msg> msgs) {
        CollectionUtils.nullToEmpty(msgs)
                .forEach(x -> {
                    x.setOperationLabel(ActionType.getNameById(x.getOperationType()));
                    if (Objects.nonNull(x.getMsgMap()) && Objects.nonNull(x.getMsgMap().getOperation())) {
                        if (StringUtils.isNotBlank(x.getMsgMap().getRole().convertToI18NValue())) {
                            x.getMsgMap().setOperation(I18N.text(x.getMsgMap().getOperation(), x.getMsgMap().getRole().convertToI18NValue()));
                        } else {
                            x.getMsgMap().setOperation(I18N.text(x.getMsgMap().getOperation(), I18N.text(I18NKey.constant_normal_staff)));
                        }
                    }
                    handleTeamMemberLang(x);
                });
        return msgs;
    }

    @Override
    public void logTeamMember(IObjectData oldData,
                              IObjectData data,
                              IObjectDescribe objectDescribe,
                              Map<String, List<TeamMemberInfo.Member>> memberInfos,
                              List<TeamMemberInfo.Msg> msgList,
                              List<LogInfo.LintMessage> textMsg,
                              List<TeamRoleInfo> teamRoleInfos) {
        String dataOwnerId = "";
        if (CollectionUtils.notEmpty(data.getOwner())) {
            dataOwnerId = data.getOwner().get(0);
        }
        String oldOwnerId;
        if (CollectionUtils.notEmpty(oldData.getOwner())) {
            oldOwnerId = oldData.getOwner().get(0);
        } else {
            oldOwnerId = "";
        }
        if (!StringUtils.equals(oldOwnerId, dataOwnerId)) {
            List<TeamMember> teamMembers = ObjectDataExt.of(data).getTeamMembers();
            List<TeamMember> oldOwnerMembers = teamMembers.stream()
                    .filter(x -> Objects.equals(x.getEmployee(), oldOwnerId))
                    .filter(x -> TeamMember.MemberType.EMPLOYEE.equals(x.getMemberType()))
                    .collect(Collectors.toList());

            String newPermissonKey = "";
            List<String> newRoleKeyList = Lists.newArrayList();
            String oldStrategyStr;
            String operation;
            String oldMemberName = getMemberName(memberInfos.get(TeamMember.MemberType.EMPLOYEE.getValue()), oldOwnerId);
            String newMemberName = getMemberName(memberInfos.get(TeamMember.MemberType.EMPLOYEE.getValue()), dataOwnerId);

            //用于后台CRM日志的显示
            textMsg.add(new LogInfo.LintMessage(objectDescribe.getDisplayName(), "", ""));
            textMsg.add(new LogInfo.LintMessage(data.getName(), data.getId(), objectDescribe.getApiName()));


            if (CollectionUtils.notEmpty(oldOwnerMembers)) {
                newPermissonKey = oldOwnerMembers.get(0).getPermission().getLabelKey();
                Map<String, String> defaultParameterValues = Maps.newHashMap();
                defaultParameterValues.put(newPermissonKey, I18N.text(newPermissonKey));
                String text = I18N.text(I18NKey.EDIT_OWNER_LOG_CHANGE_OWNER_2, oldMemberName, I18N.text(newPermissonKey), newMemberName);
                textMsg.add(new LogInfo.LintMessage(text, "", "",
                        InternationalItem.builder()
                                .internationalKey(I18NKey.EDIT_OWNER_LOG_CHANGE_OWNER_2)
                                .defaultInternationalValue(text)
                                .internationalParameters(Lists.newArrayList(oldMemberName, newPermissonKey, newMemberName))
                                .defaultParameterValues(defaultParameterValues)
                                .build()
                ));

                newRoleKeyList = oldOwnerMembers.stream().map(x -> getRoleNameKey(teamRoleInfos, x)).collect(Collectors.toList());
                operation = I18NKey.CONVERT_NORMAL_MEMBER;
            } else {
                oldStrategyStr = I18NKey.EDIT_OWNER_LOG_REMOVE_TEAM_MEMBER;
                Map<String, String> defaultParameterValues = Maps.newHashMap();
                defaultParameterValues.put(oldStrategyStr, I18N.text(oldStrategyStr));
                String text = I18N.text(I18NKey.EDIT_OWNER_LOG_CHANGE_OWNER, oldMemberName, I18N.text(oldStrategyStr), newMemberName);
                textMsg.add(new LogInfo.LintMessage(text, "", "",
                        InternationalItem.builder()
                                .internationalKey(I18NKey.EDIT_OWNER_LOG_CHANGE_OWNER)
                                .defaultInternationalValue(text)
                                .internationalParameters(Lists.newArrayList(oldMemberName, oldStrategyStr, newMemberName))
                                .defaultParameterValues(defaultParameterValues)
                                .build()
                ));
                operation = I18NKey.REMOVED_RELATED_TEAM;
            }

            //用于详情页的修改记录
            msgList.add(
                    getTeamMemberInfoMsg(
                            ActionType.ChangeOwner,
                            dataOwnerId,
                            oldOwnerId,
                            memberInfos.get(TeamMember.MemberType.EMPLOYEE.getValue()),
                            newRoleKeyList,
                            DefObjConstants.DATA_PRIVILEGE_ROLE_TYPE.OWNER.getI18NLabel(),
                            newPermissonKey,
                            DefObjConstants.DATA_PRIVILEGE_PERMISSION.READANDWRITE.getI18NLabel(),
                            operation
                    ));

        }
    }
}
