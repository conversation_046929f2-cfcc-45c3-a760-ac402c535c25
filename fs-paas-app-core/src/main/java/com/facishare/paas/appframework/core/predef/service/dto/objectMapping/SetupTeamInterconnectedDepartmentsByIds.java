package com.facishare.paas.appframework.core.predef.service.dto.objectMapping;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2025/3/21
 */
public interface SetupTeamInterconnectedDepartmentsByIds {

    @Data
    class Arg {
        private String describeApiName;
        private List<String> dataIds;
        private Boolean updatedTeam;

        public boolean needUpdateTeam() {
            return BooleanUtils.isTrue(updatedTeam);
        }
    }

    @Data
    @Builder
    class Result {
        private List<ObjectDataDocument> objectDataList;
    }
}
