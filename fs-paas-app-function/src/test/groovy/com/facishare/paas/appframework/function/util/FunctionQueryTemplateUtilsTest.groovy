package com.facishare.paas.appframework.function.util

import com.facishare.paas.appframework.common.util.Tuple
import com.facishare.paas.appframework.function.dto.RunResult
import com.facishare.paas.appframework.metadata.FilterExt
import com.facishare.paas.appframework.metadata.search.Query
import com.facishare.paas.appframework.metadata.search.SearchQuery
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.search.Wheres
import com.facishare.paas.metadata.impl.search.Filter
import spock.lang.Specification
import spock.lang.Unroll

class FunctionQueryTemplateUtilsTest extends Specification {

    /**
     * GenerateByAI
     * 测试内容描述：测试转换查询模版到Filter列表
     */
    def "transfer2FilterListTest"() {
        given: "准备查询模版数据"
        def filterJson = "{\"field_name\":\"status\",\"operator\":\"EQ\",\"field_values\":[\"1\"],\"value_type\":0}"
        def filter = new Filter()
        filter.fromJsonString(filterJson)
        
        def functionSearchQuery = [
            "type": "AND",
            "filter": filterJson,
            "templates": [] 
        ]
        
        def functionSearchQueryNested = [
            "type": "AND",
            "templates": [
                [
                    "type": "AND",
                    "filter": filterJson,
                    "templates": []
                ]
            ]
        ]
        
        def filters = []

        when: "执行转换单级查询模版"
        FunctionQueryTemplateUtils.transfer2FilterList(functionSearchQuery, filters)

        then: "验证结果"
        filters.size() == 1
        filters[0].fieldName == "status"
        filters[0].operator.toString() == "EQ"
        filters[0].fieldValues == ["1"]

        when: "执行转换嵌套查询模版"
        def nestedFilters = []
        FunctionQueryTemplateUtils.transfer2FilterList(functionSearchQueryNested, nestedFilters)

        then: "验证嵌套结果"
        nestedFilters.size() == 1
        nestedFilters[0].fieldName == "status"
        nestedFilters[0].operator.toString() == "EQ"
        nestedFilters[0].fieldValues == ["1"]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试转换查询模版到SearchQuery
     */
    def "transfer2SearchQueryTest"() {
        given: "准备查询模版数据"
        def filterJson = "{\"field_name\":\"status\",\"operator\":\"EQ\",\"field_values\":[\"1\"],\"value_type\":0}"
        def filter = new Filter()
        filter.fromJsonString(filterJson)
        
        def andSearchQuery = [
            "type": "AND",
            "filter": filterJson,
            "templates": [] 
        ]
        
        def orSearchQuery = [
            "type": "OR",
            "filter": filterJson,
            "templates": [] 
        ]
        
        def nestedSearchQuery = [
            "type": "AND",
            "templates": [
                [
                    "type": "AND",
                    "filter": filterJson,
                    "templates": []
                ],
                [
                    "type": "OR",
                    "filter": filterJson,
                    "templates": []
                ]
            ]
        ]

        when: "执行转换AND查询模版"
        def result1 = FunctionQueryTemplateUtils.transfer2SearchQuery(andSearchQuery)

        then: "验证AND查询模版结果"
        result1 != null
        result1.isFilterNode()
        result1.connector == SearchQuery.Connector.AND
        result1.filter.fieldName == "status"
        result1.filter.operator.toString() == "EQ"
        result1.filter.fieldValues == ["1"]

        when: "执行转换OR查询模版"
        def result2 = FunctionQueryTemplateUtils.transfer2SearchQuery(orSearchQuery)

        then: "验证OR查询模版结果"
        result2 != null
        result2.isFilterNode()
        result2.filter.fieldName == "status"
        result2.filter.operator.toString() == "EQ"
        result2.filter.fieldValues == ["1"]

        when: "执行转换嵌套查询模版"
        def result3 = FunctionQueryTemplateUtils.transfer2SearchQuery(nestedSearchQuery)

        then: "验证嵌套查询模版结果"
        result3 != null
        !result3.isFilterNode()
        result3.connector == SearchQuery.Connector.AND
        result3.searchQueryContainer.size() == 2
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取函数API名称
     */
    def "getFunctionApiNameByWheresTest"() {
        given: "准备Wheres数据"
        def wheresJson = "{\"filters\":[{\"value_type\":9,\"operator\":\"EQ\",\"field_name\":\"test_field\",\"field_values\":[\"test_function_api\"]}],\"connector\":\"AND\"}"
        def where = new Wheres()
        where.fromJsonString(wheresJson)
        
        List<Wheres> wheres = [where]

        when: "执行获取函数API名称"
        def result = FunctionQueryTemplateUtils.getFunctionApiNameByWheres(wheres)

        then: "验证结果"
        result == "test_function_api"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据值类型获取Where过滤器
     */
    def "getWhereFilterByValueTypeTest"() {
        given: "准备Wheres数据"
        def wheresJson = "{\"filters\":[{\"value_type\":9,\"operator\":\"EQ\",\"field_name\":\"test_field\",\"field_values\":[\"test_value\"]}],\"connector\":\"AND\"}"
        def where = new Wheres()
        where.fromJsonString(wheresJson)
        
        List<Wheres> wheres = [where]

        when: "执行获取Where过滤器"
        def result = FunctionQueryTemplateUtils.getWhereFilterByValueType(wheres, FilterExt.FilterValueTypes.FUNCTION_VARIABLE)

        then: "验证结果"
        result != null
        result.key == where
        result.value == 0
        
        when: "使用不存在的值类型"
        def result2 = FunctionQueryTemplateUtils.getWhereFilterByValueType(wheres, 999)

        then: "验证结果为空"
        result2 == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取过滤器值
     */
    def "getFilterValueTest"() {
        given: "准备WhereFilter数据"
        def wheresJson = "{\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"test_field\",\"field_values\":[\"test_value\"]}],\"connector\":\"AND\"}"
        def where = new Wheres()
        where.fromJsonString(wheresJson)
        
        def whereFilter = Tuple.of(where, 0)

        when: "执行获取过滤器值"
        def result = FunctionQueryTemplateUtils.getFilterValue(whereFilter)

        then: "验证结果"
        result == "test_value"
        
        when: "输入null"
        def result2 = FunctionQueryTemplateUtils.getFilterValue(null)

        then: "验证结果为null"
        result2 == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理函数结果 - 空结果情况
     */
    def "handleFunctionResultEmptyTest"() {
        given: "准备空的函数结果数据"
        def tenantId = "test_tenant"
        def wheresJson = "{\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"id\",\"field_values\":[\"\"]}],\"connector\":\"AND\"}"
        def wheres = new Wheres()
        wheres.fromJsonString(wheresJson)
        
        def filterIndex = 0
        
        def runResult = RunResult.builder()
            .functionResult(null)
            .build()

        when: "处理空的函数结果"
        def result = FunctionQueryTemplateUtils.handleFunctionResult(tenantId, wheres, filterIndex, runResult)

        then: "验证结果"
        result != null
        result.isEmpty()
        wheres.filters[0].fieldName == IFieldDescribe.ID
        wheres.filters[0].fieldValues == [""]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理函数结果 - 列表结果情况
     */
    def "handleFunctionResultListTest"() {
        given: "准备列表函数结果数据"
        def tenantId = "test_tenant"
        def wheresJson = "{\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"id\",\"field_values\":[\"\"]}],\"connector\":\"AND\"}"
        def wheres = new Wheres()
        wheres.fromJsonString(wheresJson)
        
        def filterIndex = 0
        
        def functionResult = ["1", "2", "3"]
        def runResult = RunResult.builder()
            .functionResult(functionResult)
            .build()

        when: "处理列表函数结果"
        def result = FunctionQueryTemplateUtils.handleFunctionResult(tenantId, wheres, filterIndex, runResult)

        then: "验证结果"
        result != null
        result.isEmpty()
        wheres.filters[0].fieldName == IFieldDescribe.ID
        wheres.filters[0].valueType == FilterExt.FilterValueTypes.CONSTANT
        wheres.filters[0].fieldValues == ["1", "2", "3"]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理函数结果 - RangeRule类型结果情况
     */
    def "handleFunctionResultRangeRuleTest"() {
        given: "准备RangeRule类型函数结果数据"
        def tenantId = "test_tenant"
        def wheresJson = "{\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"id\",\"field_values\":[\"\"]}],\"connector\":\"AND\"}"
        def wheres = new Wheres()
        wheres.fromJsonString(wheresJson)
        
        def filterIndex = 0
        
        def queryTemplate = [
            "type": "AND",
            "filter": "{\"field_name\":\"status\",\"operator\":\"EQ\",\"field_values\":[\"1\"],\"value_type\":0}"
        ]
        
        def relatedObjectData = [
            "field1": "value1",
            "field2": "value2"
        ]
        
        def functionResult = [
            "queryTemplate": queryTemplate,
            "relatedObjectDataSpecified": relatedObjectData
        ]
        
        def runResult = RunResult.builder()
            .functionResult(functionResult)
            .returnType("RangeRule")
            .build()

        when: "处理RangeRule类型函数结果"
        def result = FunctionQueryTemplateUtils.handleFunctionResult(tenantId, wheres, filterIndex, runResult)

        then: "验证结果"
        result != null
        result == relatedObjectData
        wheres.filters.size() > 0
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理函数结果 - Query情况
     */
    @Unroll
    def "handleFunctionResultQueryTest"() {
        given: "准备查询函数结果数据"
        def tenantId = "test_tenant"
        def query = Query.builder().build()
        def filter = new Filter()
        filter.setFieldName("id")
        
        when: "处理查询函数结果"
        def result = FunctionQueryTemplateUtils.handleFunctionResult(tenantId, query, filter, returnType, functionResult)

        then: "验证结果"
        result == expectedResult
        
        where:
        returnType     | functionResult                                           | expectedResult
        null           | null                                                     | null
        null           | []                                                       | null
        null           | ["1", "2", "3"]                                          | null
        "RangeRule"    | ["queryTemplate": [:], "relatedObjectDataSpecified": ["field1": "value1"]] | ["field1": "value1"]
    }
} 