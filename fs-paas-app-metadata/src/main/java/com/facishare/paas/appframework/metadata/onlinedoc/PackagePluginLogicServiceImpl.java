package com.facishare.paas.appframework.metadata.onlinedoc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.drew.lang.Charsets;
import com.facishare.converter.EIEAConverter;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NSaveFileFromTempFile;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.function.biz.api.service.FunctionService;
import com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FileStoreService;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.OrderByExt;
import com.facishare.paas.appframework.metadata.onlinedoc.model.EnterpriseAuth;
import com.facishare.paas.appframework.metadata.onlinedoc.model.OnlineDocAuthChangeInfo;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.PackagePluginEntity;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> create by liy on 2024/6/5
 */
@Slf4j
@Service
public class PackagePluginLogicServiceImpl implements PackagePluginLogicService {

    private static final String BUSINESS = "PackagePlugin";

    @Autowired
    private IRepository<PackagePluginEntity> repository;
    @Resource
    private NFileStorageService nFileStorageService;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private AppDefaultRocketMQProducer onlineDocProducer;
    @Autowired
    private FileStoreService fileStoreService;
    @Autowired
    @Qualifier("bizFunctionService")
    private FunctionService functionService;

    @Override
    public PackagePluginEntity createPlugin(User user, PackagePluginEntity entity) {
        tc2cAndMarkSign(user, entity.getIcon(), entity);
        entity.setObjectDescribeApiName(PackagePluginEntity.OBJECT_API_NAME);
        entity.setLastModifiedBy(user.getUserId());
        entity.setLastModifiedTime(System.currentTimeMillis());
        PackagePluginEntity dbEntity = repository.create(user, entity);
        return dbEntity;
    }

    @Override
    public PackagePluginEntity updatePlugin(User user, PackagePluginEntity entity, List<String> fields) {
        if (StringUtils.isNotBlank(entity.getIcon())) {
            boolean hasChange = tc2cAndMarkSign(user, entity.getIcon(), entity);
            if (hasChange) {
                fields.add(PackagePluginEntity.PLUGIN_ENTITY_FILED_ICON_SIGN);
            }
        }
        entity.setObjectDescribeApiName(PackagePluginEntity.OBJECT_API_NAME);
        entity.setLastModifiedBy(user.getUserId());
        entity.setLastModifiedTime(System.currentTimeMillis());
        List<PackagePluginEntity> pluginEntities = repository.bulkUpdateByFields(user, Lists.newArrayList(entity), fields);
        PackagePluginEntity updatedEntity = CollectionUtils.isEmpty(pluginEntities) ? null : pluginEntities.get(0);
        return updatedEntity;
    }

    @Override
    public PackagePluginEntity deletePlugin(User user, PackagePluginEntity entity) {
        entity.setObjectDescribeApiName(PackagePluginEntity.OBJECT_API_NAME);
        entity.setLastModifiedBy(user.getUserId());
        entity.setLastModifiedTime(System.currentTimeMillis());
        List<PackagePluginEntity> pluginEntities = repository.bulkDelete(user, Lists.newArrayList(entity));
        PackagePluginEntity updatedEntity = CollectionUtils.isEmpty(pluginEntities) ? null : pluginEntities.get(0);
        return updatedEntity;
    }

    @Override
    public PackagePluginEntity findPluginByApiName(User user, String pluginApiName) {
        SearchQuery searchQuery = SearchQueryImpl.filters(
                Lists.newArrayList(
                        FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter(),
                        FilterExt.of(Operator.EQ, PackagePluginEntity.PLUGIN_ENTITY_FILED_PLUGIN_API_NAME, pluginApiName).getFilter(),
                        FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, "0").getFilter()),
                false);
        Query query = Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, false)))
                .limit(1)
                .offset(0)
                .build();
        List<PackagePluginEntity> pluginEntities = repository.findBy(user, query, PackagePluginEntity.class);
        return CollectionUtils.isEmpty(pluginEntities) ? null : pluginEntities.get(0);
    }

    @Override
    public List<PackagePluginEntity> getPluginList(User user, String appType, int pageNumber, int pageSize) {
        List<IFilter> filters = Lists.newArrayList(
                FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, "0").getFilter());
        if (StringUtils.isNotBlank(appType)) {
            filters.add(FilterExt.of(Operator.EQ, PackagePluginEntity.PLUGIN_ENTITY_FILED_APP_TYPE, appType).getFilter());
        }
        SearchQuery searchQuery = SearchQueryImpl.filters(filters, false);
        Query query = Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, false)))
                .limit(pageSize)
                .offset(pageNumber * pageSize)
                .build();

        List<PackagePluginEntity> pluginEntities = repository.findBy(user, query, PackagePluginEntity.class);
        return pluginEntities;
    }

    @Override
    public List<PackagePluginEntity> getUsingPluginList(User user, String appType, int pageNumber, int pageSize) {
        List<IFilter> filters = Lists.newArrayList(
                FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.EQ, PackagePluginEntity.PLUGIN_ENTITY_FILED_IS_ACTIVE, Boolean.TRUE.toString()).getFilter(),
                FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, "0").getFilter());
        if (StringUtils.isNotBlank(appType)) {
            filters.add(FilterExt.of(Operator.EQ, PackagePluginEntity.PLUGIN_ENTITY_FILED_APP_TYPE, appType).getFilter());
        }
        SearchQuery searchQuery = SearchQueryImpl.filters(filters, false);
        Query query = Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, false)))
                .limit(pageSize)
                .offset(pageNumber * pageSize)
                .build();

        List<PackagePluginEntity> pluginEntities = repository.findBy(user, query, PackagePluginEntity.class);
        return pluginEntities;
    }

    @Override
    public List<PackagePluginEntity> getPluginsByApiNames(User user, List<String> apiNames) {
        List<IFilter> filters = Lists.newArrayList(
                FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.IN, PackagePluginEntity.PLUGIN_ENTITY_FILED_PLUGIN_API_NAME, apiNames).getFilter(),
                FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, "0").getFilter());
        SearchQuery searchQuery = SearchQueryImpl.filters(filters, false);
        Query query = Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, false)))
                .limit(apiNames.size())
                .offset(0)
                .build();
        List<PackagePluginEntity> pluginEntities = repository.findBy(user, query, PackagePluginEntity.class);
        return pluginEntities;
    }

    @Override
    public List<PackagePluginEntity> getPluginsByAppTypes(User user, List<String> appTypes) {
        List<IFilter> filters = Lists.newArrayList(
                FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.EQ, PackagePluginEntity.PLUGIN_ENTITY_FILED_IS_ACTIVE, Boolean.TRUE.toString()).getFilter(),
                FilterExt.of(Operator.IN, PackagePluginEntity.PLUGIN_ENTITY_FILED_APP_TYPE, appTypes).getFilter(),
                FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, "0").getFilter());
        SearchQuery searchQuery = SearchQueryImpl.filters(filters, false);
        Query query = Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, false)))
                .limit(20)
                .offset(0)
                .build();
        List<PackagePluginEntity> pluginEntities = repository.findBy(user, query, PackagePluginEntity.class);
        return pluginEntities;
    }

    /**
     * 临时转正式
     */
    private boolean tc2cAndMarkSign(User user, String tcPath, PackagePluginEntity entity) {
        if (tcPath.startsWith("http://") || tcPath.startsWith("https://")) {
            entity.setIcon(tcPath);
            entity.setIconSign(tcPath);
            return true;
        }
        if (!tcPath.startsWith("TC_")) {
            return false;
        }
        String enterpriseAccount = eieaConverter.enterpriseIdToAccount(Integer.parseInt(user.getTenantId()));
        NSaveFileFromTempFile.Arg nFileArg = new NSaveFileFromTempFile.Arg();
        nFileArg.setBusiness(BUSINESS);
        nFileArg.setEa(enterpriseAccount);
        nFileArg.setTempFileName(tcPath);
        NSaveFileFromTempFile.Result result = nFileStorageService.nSaveFileFromTempFile(nFileArg, enterpriseAccount);
        entity.setIcon(result.getFinalNPath());
        entity.setIconSign(fileStoreService.generateCpathSignature(user, result.getFinalNPath()));
        return true;
    }

    /**
     * 通知清理个人授权记录
     */
    public void notifyRemovePersonalAuth(User user, String appType, String pluginApiName) {
        if (StringUtils.isBlank(pluginApiName)) {
            log.warn("notifyRemovePersonalAuth failed missing pluginApiName:{}", pluginApiName);
            return;
        }
        OnlineDocAuthChangeInfo info = new OnlineDocAuthChangeInfo();
        info.setTenantId(Integer.parseInt(user.getTenantId()));
        info.setAppType(appType);
        info.setPluginApiName(pluginApiName);
        byte[] bytes = JSON.toJSONString(info).getBytes(Charsets.UTF_8);
        SendResult sendResult = onlineDocProducer.sendMessage(bytes);
        log.info("notifyRemovePersonalAuth finish sendResult:{}, info:{}", sendResult, info);
    }

    @Override
    @Deprecated
    public Map<String, Object> tryEnterpriseAuth(User user, String pluginApiName, String functionApiName, Map<String, String> devInfo) {
        EnterpriseAuth.Arg arg = new EnterpriseAuth.Arg();
        arg.setPluginApiName(pluginApiName);
        arg.setDevInfo(devInfo);
        try {
            String responseResult = functionService.executeFuncMethod(
                    user.getTenantId(),
                    user.getUserId(),
                    functionApiName,
                    "enterpriseAuth",
                    Lists.newArrayList(JSONObject.toJSONString(arg)));
            EnterpriseAuth.Result result = JSONObject.parseObject(responseResult, EnterpriseAuth.Result.class);
            return result.getRuntimeData();
        } catch (Exception e) {
            log.error("tryEnterpriseAuth failed user:{}, arg:{}", user, arg, e);
            throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_FUNCTION_ERROR, e.getMessage()));
        }
    }
}
