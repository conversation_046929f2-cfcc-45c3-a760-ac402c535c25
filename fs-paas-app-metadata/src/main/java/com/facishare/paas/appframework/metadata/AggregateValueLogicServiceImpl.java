package com.facishare.paas.appframework.metadata;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.search.AggFunctionArg;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

@Service("aggregateValueLogicService")
public class AggregateValueLogicServiceImpl implements AggregateValueLogicService {

    @Autowired
    private MetaDataFindService metaDataFindService;

    @Override
    public AggregateRule findAggregateRuleById(String tenantId, String ruleId) {
        IObjectData data = metaDataFindService.findObjectDataIgnoreAll(User.systemUser(tenantId), ruleId, Utils.AGGREGATE_RULE_API_NAME);
        return AggregateRule.of(data);
    }

    @Override
    public List<AggregateRule> findAggregateRuleByIds(String tenantId, List<String> ruleIds) {
        List<IObjectData> dataList = metaDataFindService.findObjectDataByIdsIgnoreAll(tenantId, ruleIds, Utils.AGGREGATE_RULE_API_NAME);
        return dataList.stream().map(AggregateRule::of).collect(Collectors.toList());
    }

    @Override
    public BigDecimal getAggregateValue(String tenantId, String ruleId, String dimension, long startDate, long endDate,
                                        String aggregateWay, int scale, RoundingMode roundingMode) {
        if (Strings.isNullOrEmpty(dimension)) {
            return null;
        }
        SearchTemplateQuery query = SearchTemplateQueryExt.of(new SearchTemplateQuery())
                .addFilter(Operator.EQ, AggregateValue.AGGREGATE_RULE_ID, ruleId)
                .addFilter(Operator.EQ, AggregateValue.AGGREGATE_DIMENSION, dimension)
                .addFilter(Operator.GTE, AggregateValue.AGGREGATE_DATE, String.valueOf(startDate))
                .addFilter(Operator.LTE, AggregateValue.AGGREGATE_DATE, String.valueOf(endDate))
                .toSearchTemplateQuery();
        List<AggFunctionArg> aggFunctionArgs = Lists.newArrayList();
        if (AggregateRule.isMaxOrMinWay(aggregateWay)) {
            aggFunctionArgs.add(AggFunctionArg.builder().aggFunction(aggregateWay).aggField(AggregateValue.AGGREGATE_VALUE).build());
        } else {
            aggFunctionArgs.add(AggFunctionArg.builder().aggFunction(Count.TYPE_SUM).aggField(AggregateValue.AGGREGATE_VALUE).build());
            if (AggregateRule.isAvgWay(aggregateWay)) {
                aggFunctionArgs.add(AggFunctionArg.builder().aggFunction(Count.TYPE_SUM).aggField(AggregateValue.AGGREGATE_COUNT_VALUE).build());
            }
        }
        List<IObjectData> dataList = metaDataFindService.aggregateFindBySearchQuery(User.systemUser(tenantId), query,
                Utils.AGGREGATE_VALUE_OBJ_API_NAME, null, aggFunctionArgs);
        if (CollectionUtils.empty(dataList)) {
            return null;
        }
        Object value = dataList.get(0).get(AggregateRule.buildAggFieldName(Count.TYPE_SUM, AggregateValue.AGGREGATE_VALUE));
        if (ObjectDataExt.isValueEmpty(value)) {
            return null;
        }
        BigDecimal valueDecimal = new BigDecimal(value.toString());
        if (valueDecimal.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.valueOf(0);
        }
        if (AggregateRule.isAvgWay(aggregateWay)) {
            Object countValue = dataList.get(0).get(AggregateRule.buildAggFieldName(Count.TYPE_SUM, AggregateValue.AGGREGATE_COUNT_VALUE));
            if (ObjectDataExt.isValueEmpty(countValue)) {
                return BigDecimal.valueOf(0);
            }
            return valueDecimal.divide(new BigDecimal(countValue.toString()), scale, roundingMode);
        } else {
            return new BigDecimal(value.toString()).setScale(scale, roundingMode);
        }
    }
}
