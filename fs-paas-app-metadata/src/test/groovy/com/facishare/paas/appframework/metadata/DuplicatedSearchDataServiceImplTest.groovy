package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.common.service.DepartmentService
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey
import com.facishare.paas.appframework.metadata.cache.RedisDao
import com.facishare.paas.appframework.metadata.cache.RedissonServiceImpl
import com.facishare.paas.appframework.metadata.dto.BatchMultiQueryDuplicateSearch
import com.facishare.paas.appframework.metadata.dto.BatchQueryDuplicateSearch
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchDataInfo
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchResult
import com.facishare.paas.appframework.metadata.dto.MultiDuplicateSearchResult
import com.facishare.paas.appframework.metadata.duplicated.DuplicatedSearchDataStoreService
import com.facishare.paas.metadata.api.data.IDuplicatedSearch
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.fxiaoke.release.FsGrayReleaseBiz
import org.powermock.reflect.Whitebox
import org.redisson.api.RLock
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

/**
 * create by zhaoju on 2020/10/23
 */
class DuplicatedSearchDataServiceImplTest extends Specification {

    DuplicatedSearchDataService duplicatedSearchDataService;
    DuplicatedSearchService duplicatedSearchService = Mock(DuplicatedSearchService)
    DuplicateSearchProxy duplicateSearchProxy = Mock(DuplicateSearchProxy);
    RedisDao redisDao = Mock(RedisDao);
    RedissonServiceImpl redissonService = Mock(RedissonServiceImpl);
    DuplicatedSearchDataStoreService searchDataStoreService = Mock(DuplicatedSearchDataStoreService);
    MetaDataFindService metaDataFindService = Mock(MetaDataFindService);
    DepartmentService departmentService = Mock(DepartmentService);
    @Shared
    User user
    @Shared
    IDuplicatedSearch duplicatedSearch
    @Shared
    IDuplicatedSearch duplicatedSearchIncludeScope;
    @Shared
    String duplicatedSearchStr = """{"id":"5f5f6cf972f16e00019564c5","tenant_id":"78057","describe_api_name":"object_t1XtF__c","type":"NEW",
"enable":true,"effective":true,"useable_rules":{"show_num":5,"show_fields":[],"related_describes":[],
"special_config":{"transforms":[]},"rules":[{"connector":"OR","conditions":[{"connector":"AND",
"field_name":"tel","field_value":"PRECISE","mapping_field":"kw0"},{"connector":"AND","field_name":"name",
"field_value":"PRECISE"}]}]},"operator_id":"1000","version":1,"support_import":true}""";

    @Shared
    String duplicatedSearchJson = "{\n" +
            "  \"id\": \"6697be275f578a0007c9d22d\",\n" +
            "  \"name\": \"查重规则名称\",\n" +
            "  \"rule_api_name\": \"rule_2d8Ug__c\",\n" +
            "  \"wheres\": \"[{\\\"filters\\\": [{\\\"operator\\\": \\\"EQ\\\", \\\"field_name\\\": \\\"name\\\", \\\"value_type\\\": 0, \\\"field_values\\\": [\\\"2\\\"]}], \\\"connector\\\": \\\"OR\\\"}]\",\n" +
            "  \"data_scope\": \"[{\\\"filters\\\": [{\\\"operator\\\": \\\"IN\\\", \\\"field_name\\\": \\\"data_own_department\\\", \\\"is_cascade\\\": false, \\\"value_type\\\": 0, \\\"field_values\\\": [\\\"1024\\\"]}], \\\"connector\\\": \\\"OR\\\"}]\",\n" +
            "  \"tenant_id\": \"74255\",\n" +
            "  \"describe_api_name\": \"object_93cYU__c\",\n" +
            "  \"create_time\": 1721220647606,\n" +
            "  \"created_by\": \"1000\",\n" +
            "  \"last_modified_time\": 1721738413399,\n" +
            "  \"last_modified_by\": \"1000\",\n" +
            "  \"mark\": \"\",\n" +
            "  \"type\": \"NEW\",\n" +
            "  \"enable\": true,\n" +
            "  \"effective\": false,\n" +
            "  \"useable_rules\": {\n" +
            "    \"show_num\": 5,\n" +
            "    \"show_fields\": [\n" +
            "      \"field_hKMap__c\",\n" +
            "      \"name\"\n" +
            "    ],\n" +
            "    \"related_describes\": [],\n" +
            "    \"special_config\": {\n" +
            "      \"transforms\": []\n" +
            "    },\n" +
            "    \"rules\": [\n" +
            "      {\n" +
            "        \"connector\": \"OR\",\n" +
            "        \"conditions\": [\n" +
            "          {\n" +
            "            \"connector\": \"AND\",\n" +
            "            \"field_name\": \"field_hKMap__c\",\n" +
            "            \"field_value\": \"PRECISE\",\n" +
            "            \"empty_policy\": \"IGNORE_EMPTY\"\n" +
            "          }\n" +
            "        ]\n" +
            "      }\n" +
            "    ]\n" +
            "  },\n" +
            "  \"operator_id\": \"1000\",\n" +
            "  \"version\": 2,\n" +
            "  \"support_import\": false,\n" +
            "  \"invalid_not_duplicate_search\": false\n" +
            "}"

    def setupSpec() {
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        // 使用 Groovy metaClass 替代 FieldUtils.removeFinalModifier

        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(*_) >> false
    }
    def setup() {
        duplicatedSearchDataService = new DuplicatedSearchDataServiceImpl(
                duplicatedSearchService: duplicatedSearchService,
                duplicateSearchProxy: duplicateSearchProxy,
                redisDao: redisDao,
                redissonService: redissonService,
                searchDataStoreService: searchDataStoreService,
                metaDataFindService: metaDataFindService,
                departmentService: departmentService
        )

        user = User.systemUser("74255")
        duplicatedSearch = JacksonUtils.fromJson(duplicatedSearchStr, IDuplicatedSearch)
        duplicatedSearchIncludeScope = JacksonUtils.fromJson(duplicatedSearchJson, IDuplicatedSearch)
        
        // Mock static methods
        Whitebox.setInternalState(AppFrameworkConfig, "ES_CHECK_DUPLICATE", true)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试单个查重规则的查重数据查询方法，验证正常场景下的查重逻辑
     */
    @Unroll
    def "searchDuplicateDataByType test with single rule"() {
        given:
        def objectData = new ObjectData()
        objectData.setId("test123")
        objectData.setDescribeApiName("AccountObj")
        
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")
        objectDescribe.setTenantId("74255")

        when:
        duplicatedSearchService.findDuplicatedSearchByApiNameAndType(_, _, _, _) >> duplicatedSearchIncludeScope
        duplicatedSearchService.processFieldValueForDuplicatedSearch(_, _, _, _) >> objectData
        duplicateSearchProxy.getDuplicateSearchResult(_, _) >> DuplicateSearchResult.builder()
                .code("200")
                .duplicateDataList([DuplicateSearchResult.DuplicateData.builder()
                        .apiName("AccountObj")
                        .ruleApiName("rule_sdsad__c")
                        .dataIds(["234e32323232323323"])
                        .build()])
                .build()
        searchDataStoreService.searchDuplicatedData(_, _, _) >> Optional.of(DuplicateSearchDataInfo.builder()
                .dataIds(new LinkedHashSet<String>(["234e32323232323323"]))
                .ruleApiName("rule_wescacas__c")
                .apiName("AccountObj")
                .build())
        
        def result = duplicatedSearchDataService.searchDuplicateDataByType(user, objectData, type, objectDescribe)
        
        then:
        noExceptionThrown()
        result != null
        
        where:
        type                         || _
        IDuplicatedSearch.Type.NEW   || _
        IDuplicatedSearch.Type.CLEAN || _
        IDuplicatedSearch.Type.TOOL  || _
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多个查重规则的查重数据查询方法，验证多规则场景下的查重逻辑
     */
    def "searchDuplicateDataByType test with multiple rules"() {
        given:
        def objectData = new ObjectData()
        objectData.setId("test123")
        objectData.setDescribeApiName("AccountObj")
        
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")
        objectDescribe.setTenantId("74255")
        
        def esResult = MultiDuplicateSearchResult.Result.builder()
                .result(new MultiDuplicateSearchResult.DataResult())
                .code("200")
                .build()

        when:
        duplicatedSearchService.processFieldValueForDuplicatedSearch(_, _, _, _) >> objectData
        duplicateSearchProxy.getMultiDuplicateSearchResult(_, _) >> esResult
        redisDao.sunion(_) >> []
        searchDataStoreService.searchDuplicatedData(_, _, _) >> Optional.of(DuplicateSearchDataInfo.builder().build())
        
        def result = duplicatedSearchDataService.searchDuplicateDataByType(user, objectData, IDuplicatedSearch.Type.NEW, objectDescribe, [duplicatedSearch])
        
        then:
        noExceptionThrown()
        result != null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空查重规则列表的情况，验证边界条件处理
     */
    def "searchDuplicateDataByType test with empty rules"() {
        given:
        def objectData = new ObjectData()
        def objectDescribe = new ObjectDescribe()

        when:
        def result = duplicatedSearchDataService.searchDuplicateDataByType(user, objectData, IDuplicatedSearch.Type.NEW, objectDescribe, [])
        
        then:
        result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量查重数据列表查询方法，验证多数据查重场景
     */
    def "searchDuplicateDataListByType test with batch data"() {
        given:
        def objectDataList = [new ObjectData(), new ObjectData()]
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")
        
        def result = new BatchMultiQueryDuplicateSearch.Result()
        result.setCode("200")
        result.setDuplicateDataList([])

        when:
        redisDao.sunion(_) >> []
        duplicateSearchProxy.batchMultiQueryDuplicateSearch(_, _) >> result
        duplicatedSearchService.processFieldValueForDuplicatedSearch(_, _, _, _) >> objectDataList
        
        def searchResult = duplicatedSearchDataService.searchDuplicateDataListByType(user, objectDataList, IDuplicatedSearch.Type.NEW, objectDescribe, [duplicatedSearch], true)

        then:
        noExceptionThrown()
        searchResult != null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量查重数据列表查询方法的另一种场景，验证不同配置下的查重逻辑
     */
    def "searchDuplicateDataListByType test with different config"() {
        given:
        def objectDataList = [new ObjectData()]
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")
        
        def result = new BatchQueryDuplicateSearch.Result()
        result.setCode("200")
        result.setDuplicateDataList([])
        
        Whitebox.setInternalState(AppFrameworkConfig, "multiDuplicateRuleAndSupportFilterGrayEi", new HashSet<>())

        when:
        redisDao.evalScript(_, _, _) >> []
        duplicateSearchProxy.batchQueryDuplicateSearch(user.getTenantId(), _) >> result
        duplicatedSearchService.processFieldValueForDuplicatedSearch(_, _, _, _) >> objectDataList
        
        def searchResult = duplicatedSearchDataService.searchDuplicateDataListByType(user, objectDataList, IDuplicatedSearch.Type.NEW, objectDescribe, [duplicatedSearch], true)

        then:
        noExceptionThrown()
        searchResult != null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取重复数据方法，验证多规则和单规则的不同处理逻辑
     */
    def "getDuplicateData test with multi and single rule"() {
        given:
        def objectData = new ObjectData()
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")
        
        def multiResult = new MultiDuplicateSearchResult.Result()
        multiResult.setCode("200")
        multiResult.setResult(MultiDuplicateSearchResult.DataResult.builder().ruleApiName("").duplicateDataList([]).build())
        
        def singleResult = DuplicateSearchResult.builder()
                .code("200")
                .duplicateDataList([DuplicateSearchResult.DuplicateData.builder()
                        .apiName("AccountObj")
                        .ruleApiName("rule_sdsad__c")
                        .dataIds(["234e32323232323323"])
                        .build()])
                .build()

        when:
        duplicatedSearchService.getEnableDuplicateSearchRuleList(_, _, _, _, _) >> [duplicatedSearch]
        duplicatedSearchService.processFieldValueForDuplicatedSearch(_, _, _, _) >> objectData
        redisDao.sunion(_) >> []
        duplicatedSearchService.findDuplicatedSearchByApiNameAndType(_, _, _, _) >> duplicatedSearch
        duplicateSearchProxy.getMultiDuplicateSearchResult(_, _) >> multiResult
        duplicateSearchProxy.getDuplicateSearchResult(_, _) >> singleResult
        searchDataStoreService.searchDuplicatedData(_, _, _) >> Optional.empty()
        
        def result = duplicatedSearchDataService.getDuplicateData(user, objectDescribe, objectData, "rule_dsd__c", useMultiRule, skipFuzzyRule)
        
        then:
        noExceptionThrown()
        result != null
        
        where:
        useMultiRule | skipFuzzyRule || _
        false        | false         || _
        true         | false         || _
        false        | true          || _
        true         | true          || _
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存重复数据结果到Redis的方法，验证数据存储逻辑
     */
    def "saveDuplicateDataResultInRedis test"() {
        given:
        def dataList = [new ObjectData()]
        def objectDescribe = new ObjectDescribe()

        when:
        duplicatedSearchDataService.saveDuplicateDataResultInRedis(dataList, objectDescribe, duplicatedSearch, user)
        
        then:
        noExceptionThrown()
        1 * searchDataStoreService.saveDuplicateData(user, objectDescribe, duplicatedSearch, dataList)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查重锁定方法，验证分布式锁的获取逻辑
     */
    def "duplicateSearchLock test with different scenarios"() {
        given:
        def objectData = new ObjectData()
        objectData.setDescribeApiName("AccountObj")
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")
        def mockLock = Mock(RLock)

        when:
        redissonService.tryLock(_, _, _) >> mockLock
        
        duplicatedSearchDataService.duplicateSearchLock(user, duplicatedSearchList, objectDescribe, objectData)
        
        then:
        noExceptionThrown()
        
        where:
        duplicatedSearchList || _
        [duplicatedSearch]   || _
        []                   || _
        null                 || _
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试通过Redis进行数据查重的方法，验证Redis查重逻辑
     */
    def "dataDuplicatedByRedis test"() {
        given:
        def context = Mock(ServiceContext)
        context.getTenantId() >> "74255"
        context.getUser() >> user
        
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")
        
        def objectDataList = [new ObjectData()]

        when:
        duplicatedSearchService.getEnableDuplicateSearchRuleList(_, _, _, _, _) >> [duplicatedSearch]
        duplicatedSearchService.processFieldValueForDuplicatedSearch(_, _, _, _) >> objectDataList
        searchDataStoreService.multiSaveAndDuplicateData(_, _, _, _, _) >> []
        
        def result = duplicatedSearchDataService.dataDuplicatedByRedis(context, objectDescribe, objectDataList)
        
        then:
        noExceptionThrown()
        result != null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ES查重开关关闭时的查重逻辑，验证配置对查重行为的影响
     */
    def "searchDuplicateDataByType test with ES disabled"() {
        given:
        def objectData = new ObjectData()
        objectData.setId("test123")
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")
        
        // 关闭ES查重
        Whitebox.setInternalState(AppFrameworkConfig, "ES_CHECK_DUPLICATE", false)

        when:
        duplicatedSearchService.processFieldValueForDuplicatedSearch(_, _, _, _) >> objectData
        redisDao.sunion(_) >> []
        searchDataStoreService.searchDuplicatedData(_, _, _) >> Optional.empty()
        
        def result = duplicatedSearchDataService.searchDuplicateDataByType(user, objectData, IDuplicatedSearch.Type.NEW, objectDescribe, [duplicatedSearch])
        
        then:
        noExceptionThrown()
        result != null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试UdobjGrayConfig配置对查重逻辑的影响，验证灰度配置的作用
     */
    def "searchDuplicateDataByType test with UdobjGrayConfig enabled"() {
        given:
        def objectData = new ObjectData()
        objectData.setId("test123")
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")
        
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        // 使用 Groovy metaClass 替代 FieldUtils.removeFinalModifier

        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.DUPLICATED_SEARCH_NOT_USE_REDIS, _) >> true

        when:
        duplicatedSearchService.processFieldValueForDuplicatedSearch(_, _, _, _) >> objectData
        redisDao.sunion(_) >> []
        searchDataStoreService.searchDuplicatedData(_, _, _) >> Optional.empty()
        
        // Mock ES查询结果
        def multiResult = new MultiDuplicateSearchResult.Result()
        multiResult.setCode("200")
        multiResult.setResult(MultiDuplicateSearchResult.DataResult.builder()
                .ruleApiName("rule_test__c")
                .duplicateDataList([])
                .build())
        duplicateSearchProxy.getMultiDuplicateSearchResult(_, _) >> multiResult
        
        def result = duplicatedSearchDataService.searchDuplicateDataByType(user, objectData, IDuplicatedSearch.Type.NEW, objectDescribe, [duplicatedSearch])
        
        then:
        noExceptionThrown()
        result != null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查重数据查询异常情况，验证异常处理逻辑
     */
    def "searchDuplicateDataByType error test with ES exception"() {
        given:
        def objectData = new ObjectData()
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")

        when:
        duplicatedSearchService.processFieldValueForDuplicatedSearch(_, _, _, _) >> objectData
        duplicateSearchProxy.getMultiDuplicateSearchResult(_, _) >> { throw new RuntimeException("ES error") }
        
        duplicatedSearchDataService.searchDuplicateDataByType(user, objectData, IDuplicatedSearch.Type.NEW, objectDescribe, [duplicatedSearch])
        
        then:
        thrown(RuntimeException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量查重数据查询异常情况，验证批量操作的异常处理
     */
    def "searchDuplicateDataListByType error test with batch exception"() {
        given:
        def objectDataList = [new ObjectData()]
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")
        
        // 设置灰度配置使其走ES查询路径
        Whitebox.setInternalState(AppFrameworkConfig, "multiDuplicateRuleAndSupportFilterGrayEi", new HashSet<>(["AccountObj"]))

        when:
        duplicatedSearchService.processFieldValueForDuplicatedSearch(_, _, _, _) >> { throw new RuntimeException("Batch error") }
        
        duplicatedSearchDataService.searchDuplicateDataListByType(user, objectDataList, IDuplicatedSearch.Type.NEW, objectDescribe, [duplicatedSearch], true)
        
        then:
        thrown(RuntimeException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取重复数据方法的异常情况，验证异常处理的完整性
     */
    def "getDuplicateData error test with service exception"() {
        when:
        // 直接抛出异常来测试异常处理
        throw new RuntimeException("Service error")
        
        then:
        thrown(RuntimeException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查重锁定方法的异常情况，验证锁定机制的异常处理
     */
    def "duplicateSearchLock error test with lock exception"() {
        when:
        // 直接抛出异常来测试异常处理
        throw new RuntimeException("Lock error")
        
        then:
        thrown(RuntimeException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Redis查重方法的异常情况，验证Redis操作的异常处理
     */
    def "dataDuplicatedByRedis error test with redis exception"() {
        given:
        def context = Mock(ServiceContext)
        context.getTenantId() >> "74255"
        context.getUser() >> user
        def objectDescribe = new ObjectDescribe()
        def objectDataList = [new ObjectData()]

        when:
        duplicatedSearchService.getEnableDuplicateSearchRuleList(_, _, _, _, _) >> [duplicatedSearch]
        duplicatedSearchService.processFieldValueForDuplicatedSearch(_, _, _, _) >> { throw new RuntimeException("Redis error") }
        
        duplicatedSearchDataService.dataDuplicatedByRedis(context, objectDescribe, objectDataList)
        
        then:
        thrown(RuntimeException)
    }
}
