package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GenerateByAI
 * 测试FieldDataConverterAdapter类的适配器功能
 */
class FieldDataConverterAdapterTest {

  private FieldDataConverterAdapter adapter;
  private DataConvertContext context;
  private IFieldDescribe fieldDescribe;
  private IObjectData objectData;

  @BeforeEach
  void setUp() {
    adapter = new FieldDataConverterAdapter();

    User user = new User("test_tenant", "test_user");
    context = DataConvertContext.of(user);

    fieldDescribe = createTextFieldDescribe("test_field");
    objectData = createObjectData("test_field", "test_value");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getSupportedFieldTypes方法返回空列表
   */
  @Test
  @DisplayName("测试getSupportedFieldTypes - 返回空列表")
  void testGetSupportedFieldTypes() {
    // 执行被测试方法
    List<String> supportedTypes = adapter.getSupportedFieldTypes();

    // 验证结果
    assertNotNull(supportedTypes);
    assertEquals(0, supportedTypes.size());
    assertTrue(supportedTypes.isEmpty());
  }

  /**
   * 创建测试用的文本字段描述
   */
  private IFieldDescribe createTextFieldDescribe(String apiName) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试字段");
    fieldMap.put("type", IFieldType.TEXT);
    return FieldDescribeFactory.newInstance(fieldMap);
  }

  /**
   * 创建测试用的对象数据
   */
  private IObjectData createObjectData(String fieldName, Object value) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldName, value);
    return new ObjectData(dataMap);
  }
}
