package com.facishare.paas.appframework.metadata.dto;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Maps;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * create by z<PERSON><PERSON> on 2020/05/07
 */
public class Filter extends DocumentBasedBean {
    private static final long serialVersionUID = -4599545645035917724L;

    private Filter(Map filter) {
        super(filter);
    }

    public static Filter of(Map filter) {
        return new Filter(filter);
    }

    public static Filter of(IFilter filter) {
        Filter result = new Filter(Maps.newHashMap());
        result.fromJsonString(filter.toJsonString());
        return result;
    }

    public static <T extends Map> List<Filter> ofList(List<T> filters) {
        if (CollectionUtils.empty(filters)) {
            return Collections.emptyList();
        }
        return filters.stream().map(Filter::new).collect(Collectors.toList());
    }

    public Integer getValueType() {
        return get("value_type", Integer.class);
    }

    public Operator getOperator() {
        return Operator.valueOf(get("operator", String.class));
    }

    public List<String> getFieldValues() {
        return get("field_values", List.class);
    }

    public String getFieldName() {
        return get("field_name", String.class);
    }

    /**
     * 四角、五角关系
     *
     * @return
     */
    public boolean hasRelatedChainObjectVariableValueType() {
        return getValueType() != null && FilterExt.FilterValueTypes.RELATED_CHAIN_OBJECT_VARIABLE == getValueType();
    }

    /**
     * 三角关系
     *
     * @return
     */
    public boolean hasRefObjectVariableValueType() {
        return getValueType() != null && FilterExt.FilterValueTypes.REF_OBJECT_VARIABLE == getValueType();
    }

    /**
     * lookup 函数过滤条件
     *
     * @return
     */
    public boolean hasFunctionVariableValueType() {
        return getValueType() != null && FilterExt.FilterValueTypes.FUNCTION_VARIABLE == getValueType();
    }

    public IFilter toIFilter() {
        IFilter filter = new com.facishare.paas.metadata.impl.search.Filter();
        filter.fromJsonString(toJsonString());
        return filter;
    }
}
