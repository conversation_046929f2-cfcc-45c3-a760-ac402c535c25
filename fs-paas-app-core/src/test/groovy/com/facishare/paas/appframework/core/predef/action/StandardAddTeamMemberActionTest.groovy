package com.facishare.paas.appframework.core.predef.action

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.core.model.ActionContext
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.TeamMemberInfoPoJo
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.util.SpringContextUtil
import org.powermock.reflect.Whitebox
import org.springframework.context.ApplicationContext
import spock.lang.Specification
import java.lang.reflect.Field

class StandardAddTeamMemberActionTest extends Specification {

    def objectApiName = "object_123__c"
    def actionCode = "AddTeamMember"
    def tenantId = "590064"
    def userId = "1000"
    def user = User.builder().tenantId(tenantId).userId(userId).build()
    def requestContext = RequestContext.builder().tenantId(tenantId).user(user).build()
    def actionContext = new ActionContext(requestContext, objectApiName, actionCode)

    def setup() {
        ApplicationContext CONTEXT = Mock(ApplicationContext.class)
        Whitebox.setInternalState(SpringContextUtil, "CONTEXT", CONTEXT)
    }

    def arg = '''{"teamMemberInfos":[{"teamMemberEmployee":["1078","1095","1034"],"teamMemberPermissionType":"1","teamMemberRole":"","teamMemberRoleList":["4"],"teamMemberType":0,"outTeamMemberEmployee":[]},{"teamMemberEmployee":["1160"],"teamMemberPermissionType":"1","teamMemberRole":"","teamMemberRoleList":["4","1"],"teamMemberType":0,"outTeamMemberEmployee":[]},{"teamMemberEmployee":[],"teamMemberPermissionType":"1","teamMemberRole":"","teamMemberRoleList":["4","2"],"teamMemberType":0,"outTeamMemberEmployee":[{"userId":"100148709","outTenantId":"200158833"}]}],"otherObjects":[],"dataIDs":["65a9147f75961b0001bfee81"]}'''

    def data = '''{"field_C2H9o__c":"示111例文本","field_fhm3V__c__r":"北京市","field_247l3__c":"示例文本","mc_exchange_rate":"123.000000","field_Rj6uF__c":1705544460000,"owner_department_id":"1123","field_33igZ__c__r":"中国","data_own_department__r":{"deptName":"第三方平台主属部门","deptId":"1123","deptType":"dept","parentId":"999999","status":0},"version":"9","data_own_department__l":[{"parentId":"999999","deptId":"1123","deptName":"第三方平台主属部门","status":0,"deptType":"dept"}],"field_fhm3V__c":"283","tenant_id":"590064","field_cNmsG__c":"示例文本","data_own_organization":["999999"],"field_Z6h36__c":"1.00","field_tW638__c":"249","data_own_organization__l":[{"deptId":"999999","deptName":"基础业务集成测试087(590064)","status":0,"deptType":"org"}],"currency_field_22wm3__c":{"prefix":"￥","suffix":"元","fieldApiName":"currency_field_22wm3__c","objectApiName":"object_m50x5__c"},"field_vL3au__c":"13935","field_9tzp7__c":"0#%$0#%$定位信息","field_vL3au__c__r":"中关村街道","field_DBJo1__c":"PD5qjvdlX","field_22wm3__c__r":"￥ 123.00 元","data_own_organization__r":{"deptName":"基础业务集成测试087(590064)","deptId":"999999","deptType":"org","status":0},"field_g4kYw__c":["1002"],"field_nrmpN__c":"PD5qjvdlX","last_modified_time":1721029395706,"life_status":"normal","field_8Z844__c":8460000,"out_tenant_id":"301875149","field_33igZ__c":"248","field_Fb8qy__c__p":{"mobile":"13900000000","province":"新疆维吾尔自治区","city":"乌鲁木齐市","code":"0991","operator":"移动","mobilePath":"新疆维吾尔自治区乌鲁木齐市 移动","i18n":{"province":"新疆维吾尔自治区","city":"乌鲁木齐市","operator":"移动"}},"field_2QiUE__c":"635","field_1Sbpb__c":"示例文本","out_owner__r":{"dept":"301875149","name":"zeng","nickname":"zeng","tenantId":"590064","id":"311044105","enterpriseName":"yxtest1.ui","status":0},"field_F7s1A__c":[],"out_owner__l":[{"id":"311044105","tenantId":"590064","enterpriseName":"yxtest1.ui","name":"zeng","nickname":"zeng","status":0,"dept":"301875149"}],"created_by__r":{"picAddr":"N_202206_24_adaa32ccd4d4449ab61b35fa501215ff","mobile":"12289607222","description":"","dept":"1123","empNum":"1234","modifyTime":1719823563828,"post":"测试","createTime":1527665433600,"phone":"43567890","name":"lihh李","nickname":"lihh李","tenantId":"590064","id":"1002","email":"<EMAIL>","status":0},"field_wEUzb__c":"123.00","field_6G683__c":[],"owner_department":"第三方平台主属部门","field_g4kYw__c__r":{"picAddr":"N_202206_24_adaa32ccd4d4449ab61b35fa501215ff","mobile":"12289607222","description":"","dept":"1123","empNum":"1234","modifyTime":1719823563828,"post":"测试","createTime":1527665433600,"phone":"43567890","name":"lihh李","nickname":"lihh李","tenantId":"590064","id":"1002","email":"<EMAIL>","status":0},"field_g4kYw__c__l":[{"id":"1002","tenantId":"590064","name":"lihh李","picAddr":"N_202206_24_adaa32ccd4d4449ab61b35fa501215ff","email":"<EMAIL>","nickname":"lihh李","phone":"43567890","description":"","status":0,"createTime":1527665433600,"modifyTime":1719823563828,"dept":"1123","post":"测试","empNum":"1234"}],"field_7kvdy__c":1705507200000,"lock_status":"0","package":"CRM","create_time":1705579647553,"field_x6aP4__c":"<p>富文本信息ww</p>","field_x6aP4__c__o":"富文本信息ww","created_by":["1002"],"relevant_team":[{"innerDepartmentMember":false,"deptCascaded":false,"innerEmployeeMember":true,"outTeamMember":false,"teamMemberEmployee":["1160"],"teamMemberRole":"1","teamMemberRoleList":["1"],"teamMemberPermissionType":"2","outTenantId":"","sourceType":"","teamMemberType":"0","teamMemberDeptCascade":"0"},{"innerDepartmentMember":false,"deptCascaded":false,"innerEmployeeMember":true,"outTeamMember":true,"teamMemberEmployee":["311044105"],"teamMemberRole":"1","teamMemberRoleList":["1"],"teamMemberPermissionType":"2","outTenantId":"301875149","outUserName":"zeng","sourceType":"2","teamMemberType":"0","teamMemberDeptCascade":"0"}],"field_drb1A__c":"dsdsd111","field_mxc04__c":"<EMAIL>","data_own_department":["1123"],"field_2ou6A__c":"123.00","name":"311121","field_32rRI__c":"示例文本","_id":"65a9147f75961b0001bfee81","field_tW638__c__r":"北京市","is_deleted":false,"field_8l27O__c":"14","field_fd5cR__c":"示例文本\\n23232\\n343","object_describe_api_name":"object_m50x5__c","owner__l":[{"id":"1160","tenantId":"590064","name":"韩萌","picAddr":"","email":"","nickname":"韩萌","phone":"","description":"","status":0,"createTime":1715255222567,"modifyTime":1718953080632,"dept":"1123","post":"","empNum":"韩萌"}],"out_owner":["311044105"],"field_Fb8qy__c":"13900000000","field_8Daco__c":"1","owner__r":{"picAddr":"","mobile":"17082237758","description":"","dept":"1123","empNum":"韩萌","modifyTime":1718953080632,"post":"","createTime":1715255222567,"phone":"","name":"韩萌","nickname":"韩萌","tenantId":"590064","id":"1160","email":"","status":0},"owner":["1160"],"field_wGfey__c":1705544460000,"field_46n95__c":"sHb7f772n","last_modified_by__l":[{"id":"1002","tenantId":"590064","name":"lihh李","picAddr":"N_202206_24_adaa32ccd4d4449ab61b35fa501215ff","email":"<EMAIL>","nickname":"lihh李","phone":"43567890","description":"","status":0,"createTime":1527665433600,"modifyTime":1719823563828,"dept":"1123","post":"测试","empNum":"1234"}],"created_by__l":[{"id":"1002","tenantId":"590064","name":"lihh李","picAddr":"N_202206_24_adaa32ccd4d4449ab61b35fa501215ff","email":"<EMAIL>","nickname":"lihh李","phone":"43567890","description":"","status":0,"createTime":1527665433600,"modifyTime":1719823563828,"dept":"1123","post":"测试","empNum":"1234"}],"last_modified_by":["1002"],"field_2QiUE__c__r":"海淀区","mc_currency":"CNY","record_type":"default__c","last_modified_by__r":{"picAddr":"N_202206_24_adaa32ccd4d4449ab61b35fa501215ff","mobile":"12289607222","description":"","dept":"1123","empNum":"1234","modifyTime":1719823563828,"post":"测试","createTime":1527665433600,"phone":"43567890","name":"lihh李","nickname":"lihh李","tenantId":"590064","id":"1002","email":"<EMAIL>","status":0},"field_22wm3__c":"123.00","field_0jpRl__c":1705544460000}'''

    def "updateObjectDataByAddMemberTest"() {
        given:
        IObjectData objectData = new ObjectData()
        objectData.fromJsonString(data)
        StandardAddTeamMemberAction.Arg actonArg = JSON.parseObject(arg, StandardAddTeamMemberAction.Arg)
        List<TeamMemberInfoPoJo> teamMemberInfos = actonArg.getTeamMemberInfos()
        actonArg.setAddTeamMemberRole(true)


        StandardAddTeamMemberAction action = new StandardAddTeamMemberAction(
                actionContext: actionContext,
                arg: actonArg
        )
        when:
        action.updateObjectDataByAddMember(teamMemberInfos, objectData)
        then:
        noExceptionThrown()
    }
}
