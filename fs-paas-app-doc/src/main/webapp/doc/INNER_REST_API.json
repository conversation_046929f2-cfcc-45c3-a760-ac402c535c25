{"swagger": "2.0", "info": {"title": "AppFrameWork REST API", "description": "PaaS 应用框架REST API", "version": "1.0"}, "host": "************:8234", "schemes": ["http"], "basePath": "/API/v1/inner/object", "produces": ["application/json"], "paths": {"/describe/service/findDescribeByApiName": {"post": {"tags": ["Describe API"], "summary": "find describe by api name", "description": "根据对象APIName查询对象描述", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": false, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": false, "type": "string"}, {"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/DescribeFindDescribeByApiNameArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/DescribeFindDescribeByApiNameResult"}}}}}, "/describe/service/createDescribe": {"post": {"tags": ["Describe API"], "summary": "create describe", "description": "创建描述", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": false, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": false, "type": "string"}, {"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/CreateDescribeArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/CreateDescribeResult"}}}}}, "/describe/service/updateDescribe": {"post": {"tags": ["Describe API"], "summary": "update describe", "description": "更新描述", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": false, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": false, "type": "string"}, {"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/UpdateDescribeArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/UpdateDescribeResult"}}}}}, "/describe/service/disableDescribeCustomField": {"post": {"tags": ["Describe API"], "summary": "disable describe field", "description": "禁用对象描述字段", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": false, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": false, "type": "string"}, {"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/DisableDescribeFieldArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/DisableDescribeFieldResult"}}}}}, "/describe/service/enableDescribeCustomField": {"post": {"tags": ["Describe API"], "summary": "enable describe field", "description": "更新描述", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": false, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": false, "type": "string"}, {"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/EnableDescribeFieldArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/EnableDescribeFieldResult"}}}}}, "/describe/service/addDescribeCustomField": {"post": {"tags": ["Describe API"], "summary": "add describe field", "description": "添加对象描述字段", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": false, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": false, "type": "string"}, {"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/AddDescribeFieldArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/AddDescribeFieldResult"}}}}}, "/layout/service/createLayout": {"post": {"tags": ["Layout API"], "summary": "create layout", "description": "创建布局", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": false, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": false, "type": "string"}, {"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/CreateLayoutArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/CreateLayoutResult"}}}}}, "/layout/service/findLayoutByRecordTypeAndRole": {"post": {"tags": ["Layout API"], "summary": "find describe by api name", "description": "根据对象APIName查询对象描述", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": false, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": false, "type": "string"}, {"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/FindLayoutByRecordTypeAndRoleArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/FindLayoutByRecordTypeAndRoleResult"}}}}}, "/function/service/run": {"post": {"tags": ["Function API"], "summary": "run function", "description": "run function", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": false, "type": "string"}, {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": false, "type": "string"}, {"in": "body", "name": "body", "required": false, "schema": {"$ref": "#/definitions/FunctionRunArg"}}], "responses": {"200": {"description": "successful", "schema": {"$ref": "#/definitions/FunctionRunResult"}}}}}}, "definitions": {"DescribeFindDescribeByApiNameArg": {"type": "object", "properties": {"describe_apiName": {"type": "string"}, "include_layout": {"type": "boolean"}, "layoutType": {"type": "string"}, "includeRelatedList": {"type": "boolean"}}}, "DescribeFindDescribeByApiNameResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"objectDescribe": {"type": "object"}, "layout": {"type": "object"}, "relatedList": {"type": "array", "items": {"type": "object"}}}}}}, "CreateLayoutArg": {"type": "object", "properties": {"layout_data": {"type": "string"}}}, "CreateLayoutResult": {"type": "object", "properties": {"layout": {"type": "object"}}}, "FindLayoutByRecordTypeAndRoleArg": {"type": "object", "properties": {"describeApiName": {"type": "string"}, "role": {"type": "boolean"}, "recordType": {"type": "string"}}}, "FindLayoutByRecordTypeAndRoleResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"layout": {"type": "object"}}}}}, "CreateDescribeArg": {"type": "object", "properties": {"json_data": {"type": "string"}, "json_layout": {"type": "string"}, "include_layout": {"type": "boolean"}, "active": {"type": "boolean"}, "json_list_layout": {"type": "string"}}}, "CreateDescribeResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"layout": {"type": "object"}, "objectDescribeDraft": {"type": "object"}, "objectDescribe": {"type": "object"}}}}}, "UpdateDescribeArg": {"type": "object", "properties": {"json_data": {"type": "string"}, "json_layout": {"type": "string"}, "include_layout": {"type": "boolean"}, "active": {"type": "boolean"}}}, "UpdateDescribeResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"layout": {"type": "object"}, "objectDescribeDraft": {"type": "object"}, "objectDescribe": {"type": "object"}}}}}, "DisableDescribeFieldArg": {"type": "object", "properties": {"describeAPIName": {"type": "string"}, "field_api_name": {"type": "string"}}}, "DisableDescribeFieldResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"objectDescribeDraft": {"type": "object"}, "objectDescribe": {"type": "object"}}}}}, "EnableDescribeFieldArg": {"type": "object", "properties": {"describeAPIName": {"type": "string"}, "field_api_name": {"type": "string"}}}, "EnableDescribeFieldResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"objectDescribeDraft": {"type": "object"}, "objectDescribe": {"type": "object"}}}}}, "AddDescribeFieldArg": {"type": "object", "properties": {"describeAPIName": {"type": "string"}, "field_describe": {"type": "string"}, "layout_list": {"type": "string"}, "group_fields": {"type": "string"}}}, "AddDescribeFieldResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"objectDescribeDraft": {"type": "object"}, "objectDescribe": {"type": "object"}}}}}, "FunctionRunArg": {"type": "object", "properties": {"api_name": {"type": "string"}, "binding_object_api_name": {"type": "string"}, "parameters": {"type": "array", "items": {"$ref": "#/definitions/FunctionParameter"}}}}, "FunctionParameter": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "object"}}}, "FunctionRunResult": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"success": {"type": "boolean"}, "functionResult": {"type": "object"}, "errorInfo": {"type": "string"}}}}}}}