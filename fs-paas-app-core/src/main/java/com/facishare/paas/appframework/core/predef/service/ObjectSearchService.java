package com.facishare.paas.appframework.core.predef.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.DataSearchService;
import com.facishare.paas.appframework.common.service.SearchDataByName;
import com.facishare.paas.appframework.common.service.dto.SearchData;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.search.FindSearchObjectList;
import com.facishare.paas.appframework.core.predef.service.dto.search.ObjectSearchData;
import com.facishare.paas.appframework.core.predef.service.dto.search.SearchByName;
import com.facishare.paas.appframework.core.predef.service.dto.search.SearchByNameWithGroup;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.objects.ObjectListConfig;
import com.facishare.paas.appframework.metadata.wishFul.RecordingService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.WhatList;
import com.fxiaoke.functions.model.RelatedObject;
import com.fxiaoke.functions.model.RelatedObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.util.UdobjGrayConfigKey.EXCLUDE_DETAIL_IN_SEARCH;

@Slf4j
@Component
@ServiceModule("search")
public class ObjectSearchService {
    @Autowired
    private MetaDataService metaDataService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private FunctionLogicService functionLogicService;
    @Autowired
    private DataSearchService dataSearchService;
    @Autowired
    private OptionalFeaturesService optionalFeaturesService;
    @Autowired
    private QuoteValueService quoteValueService;
    @Autowired
    private RecordingService recordingService;

    private static List<String> UN_SUPPORT_OBJECT_LIST = Lists.newArrayList(
            ObjectAPINameMapping.AccountAtt.getApiName(),
            ObjectAPINameMapping.AccountCost.getApiName(),
//            ObjectAPINameMapping.HighSeas.getApiName(),
//            ObjectAPINameMapping.LeadsPool.getApiName(),
            ObjectAPINameMapping.ReturnedGoodsInvoiceProduct.getApiName(),
            ObjectAPINameMapping.SaleAction.getApiName(),
            ObjectAPINameMapping.SaleActionStage.getApiName(),
            ObjectAPINameMapping.SalesOrderProduct.getApiName(),
            ObjectAPINameMapping.Visiting.getApiName(),
            ObjectAPINameMapping.BizQuery.getApiName(),
            Utils.APPROVAL_TASK_API_NAME,
            Utils.APPROVAL_INSTANCE_API_NAME,
            Utils.CHECKINS_API_NAME,
            Utils.CHECKINS_IMG_API_NAME
    );

    @ServiceMethod("search_data")
    public ObjectSearchData.Result search(ObjectSearchData.Arg arg, ServiceContext context) {
        Map<String, String> objectMap = getObjectMap(arg, context, false);
        SearchData.Arg searchDataArg = buildSearchArg(arg, context, objectMap);
        SearchData.Result result = dataSearchService.searchData(searchDataArg, context.getUser());
        List<ObjectSearchData.SearchResult> list = getSearchResults(objectMap, result.getApiIdsMap());
        if (arg.getFixedSort() && CollectionUtils.notEmpty(arg.getSearchObjList()) && CollectionUtils.notEmpty(objectMap)) {
            log.debug("sortByObjectList param: list:{},searchObjList:{},objectMap:{}", JSON.toJSONString(list), JSON.toJSONString(arg.getSearchObjList()), JSON.toJSONString(objectMap));
            list = sortByObjectList(list, arg.getSearchObjList(), objectMap);
            log.debug("sortByObjectList result: list:{}", JSON.toJSONString(list));
        } else {
            //按照数据量倒序
            list = sortList(list);
        }

        return ObjectSearchData.Result.builder().list(list).totalCount(result.getTotalSize()).build();
    }

    private List<ObjectSearchData.SearchResult> sortByObjectList(List<ObjectSearchData.SearchResult> list, List<String> searchObjList, Map<String, String> objectMap) {
        Map<String, ObjectSearchData.SearchResult> resultMap = list.stream().collect(Collectors.toMap(ObjectSearchData.SearchResult::getApiName, x -> x, (x1, x2) -> x1));
        List<ObjectSearchData.SearchResult> resultList = Lists.newArrayList();
        for (String objectApiName : searchObjList) {
            ObjectSearchData.SearchResult searchResult = resultMap.get(objectApiName);
            if (Objects.isNull(searchResult)) {
                //objectMap中也没拿到，代表该对象没有权限
                if (StringUtils.isNotEmpty(objectMap.get(objectApiName))) {
                    searchResult = ObjectSearchData.SearchResult.builder().apiName(objectApiName).displayName(objectMap.get(objectApiName)).idList(Lists.newArrayList()).build();
                } else {
                    continue;
                }
            }
            resultList.add(searchResult);
        }
        return resultList;
    }

    @ServiceMethod("search_data_by_name")
    public SearchByName.Result searchByName(ObjectSearchData.Arg arg, ServiceContext context) {
        Map<String, String> objectMap = getObjectMap(arg, context, false);
        SearchData.Arg searchDataArg = buildSearchArg(arg, context, objectMap);
        searchDataArg.setIncludeApiIdsMap(false);
        searchDataArg.setIncludeNameResults(true);
        searchDataArg.setAccurateQuery(arg.isAccurateQuery());
        SearchDataByName.Result result = dataSearchService.searchDataByName(searchDataArg, context.getUser());

        List<SearchByName.DataInfo> list = CollectionUtils.nullToEmpty(result.getNameResults()).stream()
                .filter(a -> !Strings.isNullOrEmpty(a.getDataId()))
                .map((a ->
                        SearchByName.DataInfo.builder()
                                .apiName(a.getApiName())
                                .id(a.getDataId())
                                .displayName(objectMap.get(a.getApiName()))
                                .build()
                )).collect(Collectors.toList());

        Map<String, List<SearchByName.DataInfo>> map = list.stream().collect(Collectors.groupingBy(SearchByName.DataInfo::getApiName));

        IActionContext actionContext = ActionContextExt.of(context.getUser()).getContext();
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        map.forEach((apiName, dataInfoList) -> {
            parallelTask.submit(() -> {
                List<String> idList = CollectionUtils.nullToEmpty(dataInfoList).stream()
                        .map(SearchByName.DataInfo::getId)
                        .collect(Collectors.toList());
                List<INameCache> nameCacheList = metaDataService.findRecordName(actionContext, apiName, idList);
                Map<String, String> idNameMap = CollectionUtils.nullToEmpty(nameCacheList).stream()
                        .collect(Collectors.toMap(INameCache::getId, INameCache::getName));
                dataInfoList.forEach(a -> a.setName(idNameMap.get(a.getId())));
            });
        });
        try {
            parallelTask.await(5, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("Error in searchByName, time out,", e);
        }

        return SearchByName.Result.builder().list(list).build();
    }

    @ServiceMethod("search_data_by_name_with_group")
    public SearchByNameWithGroup.Result searchByNameWithGroup(ObjectSearchData.Arg arg, ServiceContext context) {
        String functionAPIName = getFunctionAPIName(context.getUser().getTenantId(), arg.getObjectApiName());
        Map<String, String> objectMap;
        if (StringUtils.isNotEmpty(functionAPIName)) {
            objectMap = getObjectMapByFunctionFilter(arg, context, functionAPIName);
        } else {
            objectMap = getObjectMap(arg, context, true);
        }

        SearchData.Arg searchDataArg = buildSearchArg(arg, context, objectMap);
        searchDataArg.setIncludeApiIdsMap(true);
        searchDataArg.setIncludeNameResults(false);
        searchDataArg.setAccurateQuery(arg.isAccurateQuery());
        SearchDataByName.Result result = dataSearchService.searchDataByName(searchDataArg, context.getUser());

        List<ObjectSearchData.SearchResult> list = getSearchResults(objectMap, result.getApiIdsMap());
        //按照数据量倒序
        list = sortList(list);


        List<SearchByNameWithGroup.SearchData> resultList = CollectionUtils.nullToEmpty(list)
                .stream()
                .map(a -> {
                    List<SearchByNameWithGroup.DataInfo> dataInfoList = CollectionUtils.nullToEmpty(a.getIdList())
                            .stream()
                            .map(b -> SearchByNameWithGroup.DataInfo.builder().id(b).build()).collect(Collectors.toList());
                    return SearchByNameWithGroup.SearchData.builder()
                            .apiName(a.getApiName())
                            .displayName(objectMap.get(a.getApiName()))
                            .dataList(dataInfoList)
                            .build();
                })
                .collect(Collectors.toList());

        IActionContext actionContext = ActionContextExt.of(context.getUser()).getContext();
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        resultList.forEach(searchData -> {
            parallelTask.submit(() -> {
                List<String> idList = CollectionUtils.nullToEmpty(result.getApiIdsMap()).getOrDefault(searchData.getApiName(), Lists.newArrayList());
                List<INameCache> nameCacheList = metaDataService.findRecordName(actionContext, searchData.getApiName(), idList);
                Map<String, String> idNameMap = CollectionUtils.nullToEmpty(nameCacheList).stream()
                        .filter(it -> Objects.nonNull(it.getName()))
                        .collect(Collectors.toMap(INameCache::getId, INameCache::getName));
                searchData.getDataList().forEach(a -> a.setName(idNameMap.get(a.getId())));
            });
        });

        try {
            parallelTask.await(5, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("Error in searchByNameWithGroup, time out.", e);
        }

        return SearchByNameWithGroup.Result.builder().dataList(resultList).build();
    }

    private SearchData.Arg buildSearchArg(ObjectSearchData.Arg arg, ServiceContext context, Map<String, String> objectMap) {
        Set<String> searchScope = objectMap.keySet();

        return SearchData.Arg.builder()
                .keyword(arg.getKeyword())
                .searchApiNames(searchScope)
                .weightApiName(Strings.isNullOrEmpty(arg.getPriorityObject()) ? null
                        : Sets.newHashSet(arg.getPriorityObject()))
                .tenantId(context.getTenantId())
                .userId(context.getUser().getUpstreamOwnerIdOrUserId())
                .size(Objects.isNull(arg.getSize()) ? 1000 : arg.getSize())
                .outTenantId(context.getUser().getOutTenantId())
                .outUserId(context.getUser().getOutUserId())
                .accurateQuery(arg.isAccurateQuery())
                .optimizeBatchQuery(CollectionUtils.notEmpty(arg.getSearchObjList()))
                .build();
    }

    @ServiceMethod("find_search_object_list")
    public FindSearchObjectList.Result findSearchObjectList(FindSearchObjectList.Arg arg, ServiceContext context) {
        return FindSearchObjectList.Result.builder().objectDescribeList(ObjectDescribeDocument.ofList(findDescribeList(context))).build();
    }

    @ServiceMethod("find_feature_enabled_object_list")
    public FindSearchObjectList.ResultV3 findFeatureEnabledObjectList(FindSearchObjectList.Arg arg, ServiceContext context) {
        List<IObjectDescribe> describeList = describeLogicService.findDescribeByPrivilegeAndModule(context.getUser(),
                ObjectAction.VIEW_LIST.getActionCode(), false,
                true, UdobjGrayConfig.isAllow(EXCLUDE_DETAIL_IN_SEARCH, context.getTenantId()), true,
                ObjectListConfig.OBJECT_MANAGEMENT);
        Map<String, OptionalFeaturesSwitchDTO> featuresSwitchDTOMap = optionalFeaturesService.batchQueryOptionalFeaturesSwitch(context.getTenantId(), describeList);

        // 确定要使用的开关key
        String enabledSwitchKey = StringUtils.isNotEmpty(arg.getEnabledSwitchKey()) ?
                arg.getEnabledSwitchKey() : OptionalFeaturesService.GLOBAL_SEARCH_SWITCH;

        List<IObjectDescribe> filteredDescribeList = describeList.stream()
                .filter(describe -> {
                    OptionalFeaturesSwitchDTO optionalFeaturesSwitchDTO = featuresSwitchDTOMap.get(describe.getApiName());
                    // 使用OptionalFeaturesService接口中的方法判断开关状态
                    return optionalFeaturesService.isSwitchEnabled(enabledSwitchKey, optionalFeaturesSwitchDTO);
                })
                .collect(Collectors.toList());

        List<String> describeApiNameList = filteredDescribeList.stream().map(IObjectDescribe::getApiName).collect(Collectors.toList());
        return FindSearchObjectList.ResultV3.builder().describeApiNameList(describeApiNameList).build();
    }

    private List<IObjectDescribe> findDescribeList(ServiceContext context) {
        List<IObjectDescribe> describeList = describeLogicService.findDescribeByPrivilegeAndModule(context.getUser(),
                ObjectAction.VIEW_LIST.getActionCode(), false,
                true, UdobjGrayConfig.isAllow(EXCLUDE_DETAIL_IN_SEARCH, context.getTenantId()), true,
                ObjectListConfig.OBJECT_MANAGEMENT);
        List<String> sortedList = ObjectDescribeExt.getObjectOrderList();
        describeList = CollectionUtils.sortByGivenOrder(describeList, sortedList, IObjectDescribe::getApiName);
        Set<String> searchDataBlackObject = AppFrameworkConfig.getSearchDataBlackObject();
        describeList.removeIf(a -> searchDataBlackObject.contains(a.getApiName()));
        filterByGlobalSearchSwitch(context.getTenantId(), describeList, false);
        return describeList;
    }


    @ServiceMethod("find_search_describe_list")
    public FindSearchObjectList.ResultV2 findSearchDescribeIncludeHistoryList(ServiceContext context) {

        List<String> historyList = recordingService.queryHistoryViewObjByCurrentUser(context.getUser(), 10);

        //查询所有对象
        List<IObjectDescribe> searchObjectList = findDescribeList(context);
        Map<String, IObjectDescribe> allDescribeApiNameMap = searchObjectList.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, v -> v, (x1, x2) -> x1));

        List<FindSearchObjectList.ObjectInfo> result = Lists.newArrayList();

        //移除历史对象中没有权限的对象,并构建返回结果
        historyList.stream()
                .filter(allDescribeApiNameMap::containsKey)
                .map(allDescribeApiNameMap::get)
                .forEach(x -> result.add(new FindSearchObjectList.ObjectInfo(x.getDisplayName(), x.getApiName())));

        //移除已经存在于历史对象中的对象，并构建返回结果
        searchObjectList.stream()
                .filter(x -> !historyList.contains(x.getApiName()))
                .forEach(x -> result.add(new FindSearchObjectList.ObjectInfo(x.getDisplayName(), x.getApiName())));
        return FindSearchObjectList.ResultV2.builder().objectDescribeList(result).build();
    }


    private void filterByGlobalSearchSwitch(String tenantId, List<IObjectDescribe> describeList, boolean excludeCloseFollowUpDynamicSwitchObj) {
        if (AppFrameworkConfig.isOptionalFeaturesSupport(tenantId)) {
            Map<String, OptionalFeaturesSwitchDTO> featuresSwitchDTOMap = optionalFeaturesService.batchQueryOptionalFeaturesSwitch(tenantId, describeList);
            describeList.removeIf(describe -> {
                OptionalFeaturesSwitchDTO optionalFeaturesSwitchDTO = featuresSwitchDTOMap.get(describe.getApiName());
                return Objects.nonNull(optionalFeaturesSwitchDTO)
                        && (!optionalFeaturesSwitchDTO.getIsGlobalSearchEnabled() || (excludeCloseFollowUpDynamicSwitchObj && !optionalFeaturesSwitchDTO.getIsFollowUpDynamicEnabled()));
            });
        }
    }

    private List<ObjectSearchData.SearchResult> sortList(List<ObjectSearchData.SearchResult> list) {
        //按查出的数据量倒序排
        list.sort(Comparator.comparingInt(a -> CollectionUtils.nullToEmpty(a.getIdList()).size() * -1));
        return list;
    }

    @NotNull
    private List<ObjectSearchData.SearchResult> getSearchResults(Map<String, String> objectMap, Map<String, List<String>> map) {
        List<ObjectSearchData.SearchResult> list = Lists.newArrayList();
        CollectionUtils.nullToEmpty(map).forEach((k, v) -> {
            ObjectSearchData.SearchResult item = ObjectSearchData.SearchResult.builder()
                    .apiName(k)
                    .displayName(objectMap.get(k))
                    .idList(v)
                    .build();
            list.add(item);
        });
        return list;
    }

    private Map<String, String> getObjectMap(ObjectSearchData.Arg arg, ServiceContext context, boolean excludeCloseFollowUpDynamicSwitchObj) {
        Map<String, String> objectMap;
        List<String> searchObjList = Lists.newArrayList();
        searchObjList.addAll(arg.getSearchObjList());
        if (StringUtils.isNotEmpty(arg.getScope()) && !searchObjList.contains(arg.getScope())) {
            searchObjList.add(arg.getScope());
        }
        List<IObjectDescribe> describeList;
        if (CollectionUtils.empty(searchObjList)) {
            describeList = describeLogicService.findDescribeByPrivilegeAndModule(context.getUser(),
                    ObjectAction.VIEW_LIST.getActionCode(), false, true,
                    UdobjGrayConfig.isAllow(EXCLUDE_DETAIL_IN_SEARCH, context.getTenantId()), true);
        } else {
            Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(context.getTenantId(), searchObjList);
            List<String> hasPermissionObj = functionPrivilegeService.funPrivilegeCheck(context.getUser(), searchObjList, ObjectAction.VIEW_LIST.getActionCode());
            describeList = describeMap.values().stream().filter(x -> hasPermissionObj.contains(x.getApiName())).collect(Collectors.toList());
        }
        Set<String> searchDataBlackObject = AppFrameworkConfig.getSearchDataBlackObject();
        describeList.removeIf(a -> searchDataBlackObject.contains(a.getApiName()));
        filterByGlobalSearchSwitch(context.getTenantId(), describeList, excludeCloseFollowUpDynamicSwitchObj);
        objectMap = describeList.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, IObjectDescribe::getDisplayName));

        return objectMap;
    }

    private Map<String, String> getObjectMapByFunctionFilter(ObjectSearchData.Arg arg, ServiceContext context, String functionAPIName) {
        Map<String, String> objectMap = Maps.newHashMap();
        List<String> apiNames = getObjectApiNamesByFunction(context.getUser(), arg.getObjectApiName(), functionAPIName, arg.getObjectData());
        if (Strings.isNullOrEmpty(arg.getScope())) {
            boolean excludeDetail = UdobjGrayConfig.isAllow(EXCLUDE_DETAIL_IN_SEARCH, context.getTenantId());
            List<IObjectDescribe> describes;
            if (excludeDetail) {
                describes = Lists.newArrayList(describeLogicService.findObjectsWithoutCopyIfGray(context.getUser().getTenantId(), apiNames).values());
            } else {
                describes = describeLogicService.findDescribeListWithoutFields(context.getTenantId(), apiNames);
            }
            List<IObjectDescribe> describeListFiltered = describeLogicService.filterDescribesWithActionCode(context.getUser(), describes, ObjectAction.VIEW_LIST.getActionCode());
            List<IObjectDescribe> describeList = describeListFiltered.stream()
                    .filter(describe -> BooleanUtils.isTrue(describe.isActive()))
                    .sorted(Comparator.comparingInt(describe -> apiNames.indexOf(describe.getApiName())))
                    .collect(Collectors.toList());
            Set<String> searchDataBlackObject = AppFrameworkConfig.getSearchDataBlackObject();
            describeList.removeIf(a -> searchDataBlackObject.contains(a.getApiName()));
            if (excludeDetail) {
                Set<String> detailApiNames = describes.stream()
                        .filter(x -> ObjectDescribeExt.of(x).isSlaveObject())
                        .map(IObjectDescribe::getApiName)
                        .collect(Collectors.toSet());
                describeList.removeIf(a -> detailApiNames.contains(a.getApiName()));
            }
            filterByGlobalSearchSwitch(context.getTenantId(), describeList, true);
            objectMap = describeList.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, IObjectDescribe::getDisplayName));
        } else {
            if (apiNames.contains(arg.getScope())) {
                IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getScope());
                boolean hasPermission = functionPrivilegeService.funPrivilegeCheck(context.getUser(), arg.getScope(), ObjectAction.VIEW_LIST.getActionCode());
                if (hasPermission) {
                    objectMap.put(arg.getScope(), describe.getApiName());
                }
            }
        }
        return objectMap;
    }

    private String getFunctionAPIName(String tenantId, String objectApiName) {
        if (StringUtils.isEmpty(objectApiName)) {
            return null;
        }
        IObjectDescribe objectDescribe = describeLogicService.findObject(tenantId, objectApiName);
        List<WhatList> whatListFields = ObjectDescribeExt.of(objectDescribe).getWhatListFields();
        String funcApiName = null;
        for (WhatList whatList : CollectionUtils.nullToEmpty(whatListFields)) {
            funcApiName = FieldDescribeExt.of(whatList).getFuncApiNameFromWhatListField();
            if (StringUtils.isNotBlank(funcApiName)) {
                break;
            }
        }
        return funcApiName;
    }

    private List<String> getObjectApiNamesByFunction(User user, String objectApiName, String funcApiName, ObjectDataDocument objectData) {
        IUdefFunction function = functionLogicService.findUDefFunction(user, funcApiName, objectApiName);
        if (Objects.isNull(function)) {
            throw new ValidateException(I18N.text(I18NKey.NOT_FIND_FUNCTION, funcApiName));
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        quoteValueService.fillQuoteValueVirtualField(user, objectDataExt, Maps.newHashMap());
        RunResult runResult = functionLogicService.executeUDefFunction(user, function, null, objectDataExt, Maps.newHashMap());
        if (!runResult.isSuccess()) {
            throw new FunctionException(runResult.getErrorInfo());
        }
        Object functionResult = runResult.getFunctionResult();
        if ("RelatedObject".equals(runResult.getReturnType()) && Objects.nonNull(functionResult)) {
            RelatedObject relatedObject = JSON.parseObject(JSON.toJSONString(functionResult), RelatedObject.class);
            List<RelatedObjectData> objectList = relatedObject.getObjectList();
            return CollectionUtils.nullToEmpty(objectList).stream()
                    .map(RelatedObjectData::getApiName)
                    .distinct()
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

}
