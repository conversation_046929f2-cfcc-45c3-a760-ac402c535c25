package com.facishare.paas.appframework.core.predef.listener.prm;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ControllerListener;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.dto.CommonFilterField;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class PrmListHeaderControllerListener implements ControllerListener<StandardListHeaderController.Arg, StandardListHeaderController.Result> {
    @Override
    public void before(ControllerContext context, StandardListHeaderController.Arg arg) {

    }

    @Override
    public void after(ControllerContext context, StandardListHeaderController.Arg arg, StandardListHeaderController.Result result) {
        handelPrmIndexFields(result);
        removeNotSupportFilterFields(result);
    }

    private void removeNotSupportFilterFields(StandardListHeaderController.Result result) {
        if (!CollectionUtils.empty(result.getFilterFields())) {
            IObjectDescribe objectDescribe = result.getObjectDescribe().toObjectDescribe();
            Map<String, IFieldDescribe> fieldDescribeMap = objectDescribe.getFieldDescribeMap();
            List<CommonFilterField.FilterField> filterFields = result.getFilterFields();
            List<CommonFilterField.FilterField> newFilterFields = filterFields.stream().filter(filterField -> {
                IFieldDescribe iFieldDescribe = fieldDescribeMap.get(filterField.getFieldName());
                if (iFieldDescribe != null && (iFieldDescribe.getType().equals(IFieldType.DEPARTMENT_MANY)
                        || iFieldDescribe.getType().equals(IFieldType.EMPLOYEE_MANY))) {
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
            result.setFilterFields(newFilterFields);
        }
    }

    private void handelPrmIndexFields(StandardListHeaderController.Result result) {
        if (result.getObjectDescribe() != null) {
            IObjectDescribe objectDescribe = result.getObjectDescribe().toObjectDescribe();
            if (CollectionUtils.notEmpty(objectDescribe.getFieldDescribes())
                    && CollectionUtils.notEmpty(PrmConstant.unSupportIndexFields)) {
                objectDescribe.getFieldDescribes().stream().forEach(iFieldDescribe -> {
                    if (iFieldDescribe != null) {
                        if (PrmConstant.unSupportIndexFields.contains(iFieldDescribe.getApiName()) ||
                                PrmConstant.unSupportIndexFields.contains(iFieldDescribe.getType())) {
                            IFieldDescribe fieldExt = result.copyFieldToDescribeExt(iFieldDescribe.getApiName());
                            fieldExt.setIndex(false);
                        }
                    }
                });
            }
        }
    }
}
