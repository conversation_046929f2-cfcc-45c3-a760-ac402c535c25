package com.facishare.paas.appframework.metadata.layout.component;

import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class SummaryComponentInfoTest {

  private SummaryComponentInfo summaryComponentInfo;

  @BeforeEach
  void setUp() {
    summaryComponentInfo = new SummaryComponentInfo();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试SummaryComponentInfo的无参构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试无参构造函数")
  void testDefaultConstructor() {
    SummaryComponentInfo info = new SummaryComponentInfo();
    assertNotNull(info, "无参构造函数创建的对象不应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试SummaryComponentInfo的Map构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = Maps.newHashMap();
    map.put(ISummaryComponentInfo.API_NAME, "total_amount");
    map.put(ISummaryComponentInfo.TYPE, "sum");
    map.put(ISummaryComponentInfo.FIELD_NAME, "amount");
    map.put(ISummaryComponentInfo.RENDER_TYPE, "number");
    map.put(ISummaryComponentInfo.PAGE_TYPE, "dashboard");
    
    SummaryComponentInfo info = new SummaryComponentInfo(map);
    assertNotNull(info, "Map构造函数创建的对象不应为空");
    assertEquals("total_amount", info.getApiName(), "应正确设置API名称");
    assertEquals("sum", info.getType(), "应正确设置聚合类型");
    assertEquals("amount", info.getFieldName(), "应正确设置字段名称");
    assertEquals("number", info.getRenderType(), "应正确设置渲染类型");
    assertEquals("dashboard", info.getPageType(), "应正确设置页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getApiName和setApiName方法
   */
  @Test
  @DisplayName("正常场景 - 测试ApiName属性")
  void testApiName() {
    // 初始状态应为null
    assertNull(summaryComponentInfo.getApiName(), "初始API名称应为null");
    
    // 设置API名称
    summaryComponentInfo.setApiName("revenue_summary");
    assertEquals("revenue_summary", summaryComponentInfo.getApiName(), "应正确设置API名称");
    
    // 设置为null
    summaryComponentInfo.setApiName(null);
    assertNull(summaryComponentInfo.getApiName(), "应支持设置为null");
    
    // 设置为空字符串
    summaryComponentInfo.setApiName("");
    assertEquals("", summaryComponentInfo.getApiName(), "应支持设置为空字符串");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getType和setType方法
   */
  @Test
  @DisplayName("正常场景 - 测试Type属性")
  void testType() {
    // 初始状态应为null
    assertNull(summaryComponentInfo.getType(), "初始聚合类型应为null");
    
    // 测试常见的聚合类型
    String[] aggregateTypes = {"sum", "count", "avg", "max", "min"};
    
    for (String type : aggregateTypes) {
      summaryComponentInfo.setType(type);
      assertEquals(type, summaryComponentInfo.getType(), "应正确设置聚合类型: " + type);
    }
    
    // 设置为null
    summaryComponentInfo.setType(null);
    assertNull(summaryComponentInfo.getType(), "应支持设置为null");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFieldName和setFieldName方法
   */
  @Test
  @DisplayName("正常场景 - 测试FieldName属性")
  void testFieldName() {
    // 初始状态应为null
    assertNull(summaryComponentInfo.getFieldName(), "初始字段名称应为null");
    
    // 设置字段名称
    summaryComponentInfo.setFieldName("order_amount");
    assertEquals("order_amount", summaryComponentInfo.getFieldName(), "应正确设置字段名称");
    
    // 测试包含特殊字符的字段名
    summaryComponentInfo.setFieldName("field_name__c");
    assertEquals("field_name__c", summaryComponentInfo.getFieldName(), "应支持自定义字段名");
    
    // 设置为null
    summaryComponentInfo.setFieldName(null);
    assertNull(summaryComponentInfo.getFieldName(), "应支持设置为null");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getRenderType方法
   */
  @Test
  @DisplayName("正常场景 - 测试getRenderType方法")
  void testGetRenderType() {
    // 初始状态应为null
    assertNull(summaryComponentInfo.getRenderType(), "初始渲染类型应为null");
    
    // 通过Map构造函数设置渲染类型
    Map<String, Object> map = Maps.newHashMap();
    map.put(ISummaryComponentInfo.RENDER_TYPE, "chart");
    SummaryComponentInfo info = new SummaryComponentInfo(map);
    assertEquals("chart", info.getRenderType(), "应正确获取渲染类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getPageType方法
   */
  @Test
  @DisplayName("正常场景 - 测试getPageType方法")
  void testGetPageType() {
    // 初始状态应为null
    assertNull(summaryComponentInfo.getPageType(), "初始页面类型应为null");
    
    // 通过Map构造函数设置页面类型
    Map<String, Object> map = Maps.newHashMap();
    map.put(ISummaryComponentInfo.PAGE_TYPE, "report");
    SummaryComponentInfo info = new SummaryComponentInfo(map);
    assertEquals("report", info.getPageType(), "应正确获取页面类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试copy方法
   */
  @Test
  @DisplayName("正常场景 - 测试copy方法")
  void testCopy() {
    // 设置一些属性
    summaryComponentInfo.setApiName("test_summary");
    summaryComponentInfo.setType("sum");
    summaryComponentInfo.setFieldName("amount");
    
    ISummaryComponentInfo copied = summaryComponentInfo.copy();
    
    assertNotNull(copied, "复制的对象不应为空");
    assertNotSame(summaryComponentInfo, copied, "复制的对象应是不同的实例");
    assertEquals(summaryComponentInfo.getApiName(), copied.getApiName(), "复制的对象应有相同的API名称");
    assertEquals(summaryComponentInfo.getType(), copied.getType(), "复制的对象应有相同的聚合类型");
    assertEquals(summaryComponentInfo.getFieldName(), copied.getFieldName(), "复制的对象应有相同的字段名称");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试toMap方法
   */
  @Test
  @DisplayName("正常场景 - 测试toMap方法")
  void testToMap() {
    // 设置一些属性
    summaryComponentInfo.setApiName("revenue_total");
    summaryComponentInfo.setType("sum");
    summaryComponentInfo.setFieldName("revenue");
    
    Map<String, Object> map = summaryComponentInfo.toMap();
    
    assertNotNull(map, "toMap结果不应为空");
    assertEquals("revenue_total", map.get(ISummaryComponentInfo.API_NAME), "Map应包含API名称");
    assertEquals("sum", map.get(ISummaryComponentInfo.TYPE), "Map应包含聚合类型");
    assertEquals("revenue", map.get(ISummaryComponentInfo.FIELD_NAME), "Map应包含字段名称");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口实现
   */
  @Test
  @DisplayName("正常场景 - 测试接口实现")
  void testInterfaceImplementation() {
    // 验证实现了ISummaryComponentInfo接口
    assertTrue(summaryComponentInfo instanceof ISummaryComponentInfo, 
               "应实现ISummaryComponentInfo接口");
    
    // 验证实现了JsonCompatible接口
    assertTrue(summaryComponentInfo instanceof com.facishare.paas.metadata.api.JsonCompatible, 
               "应实现JsonCompatible接口");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试序列化版本号
   */
  @Test
  @DisplayName("正常场景 - 测试序列化版本号")
  void testSerialVersionUID() {
    // 验证类有序列化版本号（通过反射检查）
    try {
      java.lang.reflect.Field field = SummaryComponentInfo.class.getDeclaredField("serialVersionUID");
      field.setAccessible(true);
      long serialVersionUID = field.getLong(null);
      assertEquals(-7583742308956510342L, serialVersionUID, "序列化版本号应正确");
    } catch (Exception e) {
      fail("应该有serialVersionUID字段");
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试继承的DocumentBasedBean功能
   */
  @Test
  @DisplayName("正常场景 - 测试继承功能")
  void testInheritedFunctionality() {
    // 验证继承自DocumentBasedBean
    assertTrue(summaryComponentInfo instanceof com.facishare.paas.metadata.impl.DocumentBasedBean, 
               "应继承自DocumentBasedBean");
    
    // 通过Map构造函数测试继承的功能
    Map<String, Object> map = Maps.newHashMap();
    map.put("custom_field", "custom_value");
    map.put(ISummaryComponentInfo.API_NAME, "test_api");
    
    SummaryComponentInfo info = new SummaryComponentInfo(map);
    assertEquals("test_api", info.getApiName(), "应正确处理API名称");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试完整的汇总组件信息设置
   */
  @Test
  @DisplayName("正常场景 - 测试完整汇总组件信息")
  void testCompleteComponentInfo() {
    // 设置完整的汇总组件信息
    summaryComponentInfo.setApiName("monthly_revenue");
    summaryComponentInfo.setType("sum");
    summaryComponentInfo.setFieldName("order_amount");
    
    // 通过Map设置其他属性
    Map<String, Object> map = Maps.newHashMap();
    map.put(ISummaryComponentInfo.API_NAME, summaryComponentInfo.getApiName());
    map.put(ISummaryComponentInfo.TYPE, summaryComponentInfo.getType());
    map.put(ISummaryComponentInfo.FIELD_NAME, summaryComponentInfo.getFieldName());
    map.put(ISummaryComponentInfo.RENDER_TYPE, "currency");
    map.put(ISummaryComponentInfo.PAGE_TYPE, "dashboard");
    
    SummaryComponentInfo completeInfo = new SummaryComponentInfo(map);
    
    // 验证所有属性
    assertEquals("monthly_revenue", completeInfo.getApiName(), "API名称应正确");
    assertEquals("sum", completeInfo.getType(), "聚合类型应正确");
    assertEquals("order_amount", completeInfo.getFieldName(), "字段名称应正确");
    assertEquals("currency", completeInfo.getRenderType(), "渲染类型应正确");
    assertEquals("dashboard", completeInfo.getPageType(), "页面类型应正确");
    
    // 测试toMap方法
    Map<String, Object> resultMap = completeInfo.toMap();
    assertEquals(5, resultMap.size(), "Map应包含5个属性");
    
    // 测试copy方法
    ISummaryComponentInfo copiedInfo = completeInfo.copy();
    assertEquals(completeInfo.getApiName(), copiedInfo.getApiName(), "复制的API名称应相同");
    assertEquals(completeInfo.getType(), copiedInfo.getType(), "复制的聚合类型应相同");
    assertEquals(completeInfo.getFieldName(), copiedInfo.getFieldName(), "复制的字段名称应相同");
    assertEquals(completeInfo.getRenderType(), copiedInfo.getRenderType(), "复制的渲染类型应相同");
    assertEquals(completeInfo.getPageType(), copiedInfo.getPageType(), "复制的页面类型应相同");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试特殊字符和边界值
   */
  @Test
  @DisplayName("边界场景 - 测试特殊字符和边界值")
  void testSpecialCharactersAndBoundaryValues() {
    // 测试包含特殊字符的值
    summaryComponentInfo.setApiName("field_with_@#$%_chars");
    summaryComponentInfo.setType("custom_aggregate_type");
    summaryComponentInfo.setFieldName("field__c_with_underscores");
    
    assertEquals("field_with_@#$%_chars", summaryComponentInfo.getApiName(), "应支持特殊字符API名称");
    assertEquals("custom_aggregate_type", summaryComponentInfo.getType(), "应支持自定义聚合类型");
    assertEquals("field__c_with_underscores", summaryComponentInfo.getFieldName(), "应支持下划线字段名");
    
    // 测试空字符串
    summaryComponentInfo.setApiName("");
    summaryComponentInfo.setType("");
    summaryComponentInfo.setFieldName("");
    
    assertEquals("", summaryComponentInfo.getApiName(), "应支持空字符串API名称");
    assertEquals("", summaryComponentInfo.getType(), "应支持空字符串聚合类型");
    assertEquals("", summaryComponentInfo.getFieldName(), "应支持空字符串字段名");
  }
}
