package com.facishare.paas.appframework.metadata.personalAuth.model;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR> create by liy on 2024/6/20
 */
public interface ActionCallback {

    @Data
    class Arg {
        /**
         * 临时码
         */
        private String code;
        /**
         * 插件apiName
         */
        private String pluginApiName;
        /**
         * 开发信息
         */
        private Map<String, String> devInfo;
    }

    @Data
    class Result {
        /**
         * 是否成功
         */
        private int errorCode;
        /**
         * 错误提示语
         */
        private String errorMessage;
        /**
         * 运行时数据，用于保存用户授权token信息
         */
        private Map<String, Object> runtimeData;
        /**
         * 授权过期时间戳，毫秒
         */
        private long expiredTime;
    }
}
