package com.facishare.paas.appframework.core.predef;

import org.springframework.context.support.ApplicationObjectSupport;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * 初始化服务
 * <p>
 * Created by l<PERSON>yi<PERSON>ng on 2017/7/12.
 */
@Service
public class FlowInitService extends ApplicationObjectSupport {

    @PostConstruct
    public void init() {
        StageTaskPreDefineObject.init();
        InternalPreDefineObject.init();
    }
}