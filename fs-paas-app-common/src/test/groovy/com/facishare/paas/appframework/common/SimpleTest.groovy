package com.facishare.paas.appframework.common

import spock.lang.Specification

/**
 * 简单测试验证Groovy语法
 */
class SimpleTest extends Specification {

    def "test basic functionality"() {
        given: "准备测试数据"
        def value = "test"

        when: "执行操作"
        def result = value.toUpperCase()

        then: "验证结果"
        result == "TEST"
    }

    def "test exception handling"() {
        when: "执行会抛出异常的操作"
        throw new RuntimeException("test exception")

        then: "验证异常"
        thrown(RuntimeException)
    }

    def "test no exception"() {
        when: "执行正常操作"
        def result = 1 + 1

        then: "验证结果"
        result == 2
        noExceptionThrown()
    }
}
