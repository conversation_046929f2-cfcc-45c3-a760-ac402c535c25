package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.HeadField;
import com.facishare.paas.appframework.metadata.IHeadField;
import com.facishare.paas.appframework.metadata.LayoutRuleExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.layout.component.BaseFlowTaskListComponentExt;
import com.facishare.paas.appframework.metadata.layout.component.FlowTaskListComponentExt;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.Builder;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/3/8
 */
public class FlowTaskListComponentRender extends BaseFlowTaskListMobileComponentExtRender {
    private final FlowTaskListComponentExt flowTaskListComponentExt;

    @Builder
    private FlowTaskListComponentRender(FlowTaskListComponentExt flowTaskListComponentExt, ObjectDescribeExt describeExt,
                                        ObjectDescribeExt whatDescribeExt, User user, ILayout objectLayout, PageType pageType) {
        super(describeExt, whatDescribeExt, user, objectLayout, pageType);
        this.flowTaskListComponentExt = flowTaskListComponentExt;
    }

    @Override
    protected void renderFieldList() {
        List<IHeadField> headFields = flowTaskListComponentExt.getHeadFields();
        if (!isDesigner()) {
            List<String> supportFields = getSupportFields();
            headFields.removeIf(it -> !supportFields.contains(it.getFieldName()));
        }
        if (CollectionUtils.empty(headFields)) {
            headFields = buildHeadFieldList();
        }
        flowTaskListComponentExt.resetHeadField(headFields);
    }

    private List<IHeadField> buildHeadFieldList() {
        List<LayoutRuleExt.FieldConfig> fieldConfigList = objectLayout.getFieldConfigList(Lists.newArrayList(), true);
        return HeadField.fromFieldConfigList(fieldConfigList);
    }

    @Override
    protected BaseFlowTaskListComponentExt getFlowTaskListComponentExt() {
        return flowTaskListComponentExt;
    }
}
