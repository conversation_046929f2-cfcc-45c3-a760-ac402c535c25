package com.facishare.paas.appframework.core.predef.service.dto.convertRule;

import com.facishare.paas.appframework.core.model.MappingRuleDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by zhouwr on 2023/2/23.
 */
public interface FindByDescribeApiName {
    @Data
    class Arg {
        private String targetApiName;
        private String sourceApiName;
        private String sourceId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private List<MappingRuleDocument> ruleList;
    }
}
