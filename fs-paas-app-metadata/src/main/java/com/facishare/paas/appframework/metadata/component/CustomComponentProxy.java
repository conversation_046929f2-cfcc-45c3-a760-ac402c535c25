package com.facishare.paas.appframework.metadata.component;

import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(value = "CUSTOM_COMPONENT", desc = "自定义组件服务", contentType = "application/json")
public interface CustomComponentProxy {
    @POST(value = "/queryComponentList")
    QueryComponentList.Result queryComponentList(@Body QueryComponentList.Arg arg, @HeaderMap Map<String, String> headers);

    @POST(value = "/batchQueryComponentData")
    BatchQueryComponentData.Result batchQueryComponentData(@Body BatchQueryComponentData.Arg arg, @HeaderMap Map<String, String> headers);
}
