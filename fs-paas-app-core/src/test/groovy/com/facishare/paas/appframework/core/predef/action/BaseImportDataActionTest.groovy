package com.facishare.paas.appframework.core.predef.action

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.button.action.ValidateFuncAction
import com.facishare.paas.appframework.common.service.dto.OrganizationInfo
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.exception.AcceptableValidateException
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.util.ImportExportExt
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey
import com.facishare.paas.appframework.function.FunctionLogicService
import com.facishare.paas.appframework.function.dto.RunResult
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO
import com.facishare.paas.appframework.metadata.ObjectDescribeExt
import com.facishare.paas.appframework.metadata.QuoteValueService
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchDataInfo
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchOrderByType
import com.facishare.paas.appframework.metadata.dto.ImportReferenceMapping
import com.facishare.paas.appframework.metadata.duplicated.DuplicatedSearchDataStoreService
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.IUdefFunction
import com.facishare.paas.metadata.api.action.IActionContext
import com.facishare.paas.metadata.api.data.IDuplicatedSearch
import com.facishare.paas.metadata.api.data.IUniqueRule
import com.facishare.paas.metadata.api.describe.Employee
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.describe.ObjectReferenceManyFieldDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.UdefFunction
import com.facishare.paas.metadata.impl.describe.*
import com.fxiaoke.api.IdGenerator
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.release.FsGrayReleaseBiz
import com.fxiaoke.release.GrayRule
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.powermock.reflect.Whitebox
import spock.lang.Shared
import spock.lang.Specification
import java.lang.reflect.Field

class BaseImportDataActionTest extends Specification {
    def objectApiName = "object_123__c"
    def targetApiName = "object_target__123__c"
    def actionCode = "BaseImportDataBaseImportDataAction"
    def tenantId = "74255"
    def userId = "1000"
    def deptId = "1000"
    def deptOrg = "1000"
    def outTenantId = "200074255"
    def outUserId = "100018916"
    def user = User.builder().tenantId(tenantId).userId(userId).build()
    def requestContext = RequestContext.builder().tenantId(tenantId).user(user).build()
    def actionContext = new ActionContext(requestContext, objectApiName, actionCode)
    def ruleApiName = "rule_test__c"

    ServiceFacade serviceFacade
    IObjectDescribe objectDescribe
    IObjectDescribe targetDescribe
    InfraServiceFacade infraServiceFacade
    SpringBeanHolder springBeanHolder
    IUniqueRule uniqueRule
    OptionalFeaturesSwitchDTO optionalFeaturesSwitchDTO
    ImportLogMessage.ImportMessageBuilder importLogMessageBuilder
    DuplicatedSearchDataStoreService duplicatedSearchDataStoreService
    FunctionLogicService functionLogicService

    List<IFieldDescribe> fieldDescribeList = Lists.newArrayList()

    def textFieldApiName = "field_text__c"
    def recordFieldApiName = "recordType"
    def ownerFieldApiName = "owner"
    def percentFieldApiName = "percentile__c"
    def normal_read_only = "normal_read_only"
    def normal_read_write = "normal_read_write"
    def field_country__c = "field_country__c"
    def field_province__c = "field_province__c"
    def field_city__c = "field_city__c"
    def field_district__c = "field_district__c"
    def field_town__c = "field_town__c"
    def field_village__c = "field_village__c"
    def field_reference_many__c = "field_reference_many__c"
    def field_reference__c = "field_reference__c"

    def setup() {
        serviceFacade = Mock()
        infraServiceFacade = Mock()
        springBeanHolder = Spy(SpringBeanHolder)
        infraServiceFacade.getSpringBeanHolder() >> springBeanHolder
        uniqueRule = Mock(IUniqueRule)
        optionalFeaturesSwitchDTO = Mock(OptionalFeaturesSwitchDTO)
        importLogMessageBuilder = Mock(ImportLogMessage.ImportMessageBuilder)
        duplicatedSearchDataStoreService = Mock()
        functionLogicService = Mock()

        // Mock importReferenceMapping
        def importReferenceMapping = ImportReferenceMapping.builder()
                .importReferenceMappingGray(false)
                .referenceFieldMappingSwitch(false)
                .referenceFieldMapping([])
                .objectApiName(objectApiName)
                .build()
        infraServiceFacade.findImportReferenceMapping(_, _) >> importReferenceMapping

        // Mock objectDescribe
        objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        objectDescribe.setIsActive(true)

        // Mock targetDescribe
        targetDescribe = Spy(ObjectDescribe)
        targetDescribe.setApiName(targetApiName)
        targetDescribe.setIsActive(true)

        TextFieldDescribe nameField = new TextFieldDescribe()
        nameField.setApiName(IObjectData.NAME)
        nameField.setLabel(IObjectData.NAME)
        nameField.setDescribeApiName(objectApiName)
        nameField.setEnableMultiLang(true)
        nameField.setUnique(true)
        fieldDescribeList.add(nameField)

        Employee employee = new EmployeeFieldDescribe()
        employee.setApiName(ownerFieldApiName)
        employee.setLabel(ownerFieldApiName)
        fieldDescribeList.add(employee)

        DepartmentFieldDescribe dataOwnDept = new DepartmentFieldDescribe()
        dataOwnDept.setApiName(IObjectData.DATA_OWN_DEPARTMENT)
        dataOwnDept.setLabel(IObjectData.DATA_OWN_DEPARTMENT)
        dataOwnDept.setDescribeApiName(objectApiName)
        fieldDescribeList.add(dataOwnDept)

        DepartmentFieldDescribe dataOwnOrg = new DepartmentFieldDescribe()
        dataOwnOrg.setApiName(IObjectData.DATA_OWN_ORGANIZATION)
        dataOwnOrg.setLabel(IObjectData.DATA_OWN_ORGANIZATION)
        dataOwnOrg.setDescribeApiName(objectApiName)
        fieldDescribeList.add(dataOwnOrg)

        TextFieldDescribe textFieldDescribe = new TextFieldDescribe()
        textFieldDescribe.setApiName(textFieldApiName)
        textFieldDescribe.setLabel(textFieldApiName)
        textFieldDescribe.setDescribeApiName(objectApiName)
        textFieldDescribe.setEnableMultiLang(true)
        textFieldDescribe.setUnique(true)
        fieldDescribeList.add(textFieldDescribe)

        RecordTypeFieldDescribe recordTypeFieldDescribe = new RecordTypeFieldDescribe()
        recordTypeFieldDescribe.setApiName("record_type")
        fieldDescribeList.add(recordTypeFieldDescribe)

        PercentileFieldDescribe percentileFieldDescribe = new PercentileFieldDescribe()
        percentileFieldDescribe.setApiName(percentFieldApiName)
        fieldDescribeList.add(percentileFieldDescribe)

        ObjectReferenceFieldDescribe objectReferenceFieldDescribe = new ObjectReferenceFieldDescribe()
        objectReferenceFieldDescribe.setApiName("out_owner")
        objectReferenceFieldDescribe.setLabel("out_owner")
        objectReferenceFieldDescribe.setTargetApiName(targetApiName)
        objectReferenceFieldDescribe.setRelationOuterDataPrivilege("outer_owner")
        fieldDescribeList.add(objectReferenceFieldDescribe)

        ObjectReferenceFieldDescribe referenceFieldDescribe = new ObjectReferenceFieldDescribe()
        referenceFieldDescribe.setApiName(field_reference__c)
        referenceFieldDescribe.setLabel(field_reference__c)
        referenceFieldDescribe.setTargetApiName(targetApiName)
        fieldDescribeList.add(referenceFieldDescribe)

        ObjectReferenceManyFieldDescribe objectReferenceManyFieldDescribe = new ObjectReferenceManyFieldDescribe()
        objectReferenceManyFieldDescribe.setApiName(field_reference_many__c)
        objectReferenceManyFieldDescribe.setTargetApiName(targetApiName)
        objectReferenceManyFieldDescribe.setLabel(field_reference_many__c)
        fieldDescribeList.add(objectReferenceManyFieldDescribe)

        IFieldDescribe imageFieldDescribe = new ImageFieldDescribe()
        imageFieldDescribe.setApiName("field_image__c")
        imageFieldDescribe.setIsWaterMark(true)
        fieldDescribeList.add(imageFieldDescribe)


        IFieldDescribe areaFieldDescribe = new AreaFieldDescribe()
        areaFieldDescribe.setApiName("field_area__c")
        IFieldDescribe countryFieldDescribe = new CountryFieldDescribe()
        countryFieldDescribe.setApiName(field_country__c)
        areaFieldDescribe.setAreaCountryFieldApiName(field_country__c)
        IFieldDescribe provinceFieldDescribe = new ProvinceFieldDescribe()
        provinceFieldDescribe.setApiName(field_province__c)
        areaFieldDescribe.setAreaProvinceFieldApiName(field_province__c)
        IFieldDescribe cityFiledDescribe = new CityFiledDescribe()
        cityFiledDescribe.setApiName(field_city__c)
        areaFieldDescribe.setAreaCityFieldApiName(field_city__c)
        IFieldDescribe districtFieldDescribe = new DistrictFieldDescribe()
        districtFieldDescribe.setApiName(field_district__c)
        areaFieldDescribe.setAreaDistrictFieldApiName(field_district__c)
        IFieldDescribe townFieldDescribe = new TownFieldDescribe()
        townFieldDescribe.setApiName(field_town__c)
        areaFieldDescribe.setAreaTownFieldApiName(field_town__c)
        IFieldDescribe villageFieldDescribe = new VillageFieldDescribe()
        villageFieldDescribe.setApiName(field_village__c)
        areaFieldDescribe.setAreaVillageFieldApiName(field_village__c)
        fieldDescribeList.add(countryFieldDescribe)
        fieldDescribeList.add(provinceFieldDescribe)
        fieldDescribeList.add(cityFiledDescribe)
        fieldDescribeList.add(districtFieldDescribe)
        fieldDescribeList.add(townFieldDescribe)
        fieldDescribeList.add(villageFieldDescribe)
        fieldDescribeList.add(areaFieldDescribe)

        TextFieldDescribe normalReadOnly = new TextFieldDescribe()
        normalReadOnly.setApiName(normal_read_only)
        normalReadOnly.setLabel(normal_read_only)
        fieldDescribeList.add(normalReadOnly)

        TextFieldDescribe normalReadWrite = new TextFieldDescribe()
        normalReadWrite.setApiName(normal_read_write)
        normalReadWrite.setLabel(normal_read_write)
        fieldDescribeList.add(normalReadWrite)

        objectDescribe.setFieldDescribes(fieldDescribeList)
        targetDescribe.setFieldDescribes(fieldDescribeList)
    }

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
        }

    def "test removeOldOutTeamMember"() {
        given:
        List<BaseImportDataAction.ImportData> dataList = Lists.newArrayList()
        BaseImportDataAction.ImportData importData = new BaseImportDataAction.ImportData()
        importData.setRowNo(1)
        importData.setData(new ObjectData())
        dataList.add(importData)
        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                removeOutTeamMember: removeOutTeamMember
        )
        StandardUpdateImportDataAction action = new StandardUpdateImportDataAction(
                objectDescribe: objectDescribe,
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                arg: arg
        )
        when:
        action.removeOldOutTeamMember(dataList)
        then:
        noExceptionThrown()
        where:
        removeOutTeamMember | _
        true                | _
        false               | _
    }


    def "test checkOutOwner"() {
        given:
        List<BaseImportDataAction.ImportData> dataList = Lists.newArrayList()
        BaseImportDataAction.ImportData importData = new BaseImportDataAction.ImportData()
        importData.setRowNo(1)

        IObjectData data = new ObjectData()
        data.setId(IdGenerator.get())
        data.setOutOwner(Lists.newArrayList(outUserId))
        importData.setData(data)
        dataList.add(importData)

        ObjectReferenceFieldDescribe objectReferenceFieldDescribe = new ObjectReferenceFieldDescribe()
        objectReferenceFieldDescribe.setApiName("out_owner")
        objectReferenceFieldDescribe.setTargetApiName(objectApiName)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                checkOutOwner: checkOutOwner
        )
        StandardUpdateImportDataAction action = new StandardUpdateImportDataAction(
                objectDescribe: objectDescribe,
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                infraServiceFacade: infraServiceFacade,
                arg: arg,
                dataList: dataList
        )
        when:
        infraServiceFacade.isOuterUsersByTenantId(_, _) >> [:]
        infraServiceFacade.getRelationDownstreamInfo(_, _, _) >> [:]
        infraServiceFacade.batchGetOutUsersByOutTenants(_, _) >> []
        action.checkOutOwner(dataList, Lists.newArrayList(objectReferenceFieldDescribe))
        then:
        noExceptionThrown()
        where:
        checkOutOwner | _
        true          | _
        false         | _
    }


    def "test fillOutOwner"() {
        given:
        List<BaseImportDataAction.ImportData> dataList = Lists.newArrayList()
        BaseImportDataAction.ImportData importData = new BaseImportDataAction.ImportData()
        importData.setRowNo(1)
        importData.setData(new ObjectData())
        dataList.add(importData)

        ObjectReferenceFieldDescribe objectReferenceFieldDescribe = new ObjectReferenceFieldDescribe()
        objectReferenceFieldDescribe.setApiName("field_reference__c")
        objectReferenceFieldDescribe.setTargetApiName(objectApiName)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                isEmptyValueToUpdate: isEmptyValueToUpdate
        )
        StandardUpdateImportDataAction action = new StandardUpdateImportDataAction(
                objectDescribe: objectDescribe,
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                arg: arg,
                dataList: dataList,
                infraServiceFacade: infraServiceFacade
        )
        when:
        action.fillOutOwner(dataList)
        then:
        noExceptionThrown()
        where:
        isEmptyValueToUpdate | _
        true                 | _
        false                | _
    }

    def "test StandardInsertImportDataAction dealOuterOwner"() {
        given:
        List<BaseImportDataAction.ImportData> dataList = Lists.newArrayList()
        BaseImportDataAction.ImportData importData = new BaseImportDataAction.ImportData()
        importData.setRowNo(1)
        def data = new ObjectData()
        data.setId(IdGenerator.get())
        data.setOutOwner([outUserId])
        importData.setData(data)
        dataList.add(importData)

        BaseImportDataAction.ImportData importData2 = new BaseImportDataAction.ImportData()
        importData2.setRowNo(2)
        def data2 = new ObjectData()
        data2.setId(IdGenerator.get())
        data2.setOutOwner(Lists.newArrayList())
        importData2.setData(data2)
        dataList.add(importData2)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                isEmptyValueToUpdate: isEmptyValueToUpdate,
                removeOutTeamMember: removeOutTeamMember,
                checkOutOwner: checkOutOwner
        )
        StandardInsertImportDataAction action = new StandardInsertImportDataAction(
                objectDescribe: objectDescribe,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                arg: arg,
                dataList: dataList,
                infraServiceFacade: infraServiceFacade
        )
        when:
        infraServiceFacade.isOuterUsersByTenantId(_, _) >> [:]
        infraServiceFacade.getRelationDownstreamInfo(_, _, _) >> [:]
        infraServiceFacade.batchGetOutUsersByOutTenants(_, _) >> []
        action.dealOuterOwner(dataList)
        then:
        noExceptionThrown()
        where:
        isEmptyValueToUpdate | removeOutTeamMember | checkOutOwner
        true                 | true                | true
        true                 | true                | false
        true                 | false               | false
        true                 | false               | true
        false                | true                | true
        false                | true                | false
        false                | false               | true
        false                | false               | false
    }


    def "test StandardUpdateImportDataAction dealOuterOwner"() {
        given:
        List<BaseImportDataAction.ImportData> dataList = Lists.newArrayList()
        BaseImportDataAction.ImportData importData = new BaseImportDataAction.ImportData()
        importData.setRowNo(1)
        def data = new ObjectData()
        data.setId(IdGenerator.get())
        data.setOutOwner([outUserId])
        importData.setData(data)
        dataList.add(importData)

        BaseImportDataAction.ImportData importData2 = new BaseImportDataAction.ImportData()
        importData2.setRowNo(2)
        def data2 = new ObjectData()
        data2.setId(IdGenerator.get())
        data2.setOutOwner(Lists.newArrayList())
        importData2.setData(data2)
        dataList.add(importData2)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                isEmptyValueToUpdate: isEmptyValueToUpdate,
                removeOutTeamMember: removeOutTeamMember,
                checkOutOwner: checkOutOwner
        )
        StandardUpdateImportDataAction action = new StandardUpdateImportDataAction(
                objectDescribe: objectDescribe,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                arg: arg,
                dataList: dataList,
                infraServiceFacade: infraServiceFacade
        )
        when:
        infraServiceFacade.isOuterUsersByTenantId(_, _) >> [:]
        infraServiceFacade.getRelationDownstreamInfo(_, _, _) >> [:]
        infraServiceFacade.batchGetOutUsersByOutTenants(_, _) >> []
        action.dealOuterOwner(dataList)
        then:
        noExceptionThrown()
        where:
        isEmptyValueToUpdate | removeOutTeamMember | checkOutOwner
        true                 | true                | true
        true                 | true                | false
        true                 | false               | false
        true                 | false               | true
        false                | true                | true
        false                | true                | false
        false                | false               | true
        false                | false               | false
    }

    def "test StandardInsertImportDataAction before"() {
        given:
        def objectDescribe = createObjectDescribe()
        def sourceDataList = createDataDocumentList()
        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                tenantId: tenantId,
                userId: userId,
                apiName: objectApiName,
                importType: 0,
                matchingType: matchType,
                isEmptyValueToUpdate: false,
                rows: sourceDataList)
        StandardInsertImportDataAction standardInsertImportDataAction = new StandardInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                arg: arg)
        when:
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        // 使用 Groovy metaClass 替代 FieldUtils.removeFinalModifier

        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(_, _) >> true
        serviceFacade.findObject(_, _) >> objectDescribe
        infraServiceFacade.findBySwitchCache(_, _, _, _, _) >> Optional.of(uniqueRule)
        infraServiceFacade.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitchDTO
        optionalFeaturesSwitchDTO.getIsRelatedTeamEnabled() >> true
        infraServiceFacade.findEnableTeamRoleInfosWithoutOwner(_, _) >> []
        serviceFacade.getEnableDuplicateSearchRuleList(_, IDuplicatedSearch.Type.NEW, _, false, DuplicateSearchOrderByType.ORDER_BY_SORT) >> []
        serviceFacade.findDuplicatedSearchByApiNameAndType(_, _, IDuplicatedSearch.Type.NEW, false) >> []
        infraServiceFacade.generateTeamMemberField(actionContext.getUser(), objectDescribe.getApiName()) >> []
        standardInsertImportDataAction.before(arg)
        then:
        noExceptionThrown()
        where:
        matchType << [1, 2]
    }

    def "test doAct"() {
        given:
        QuoteValueService quoteValueService = Mock(QuoteValueService)
        ValidateFuncAction validateFuncAction = new ValidateFuncAction(
                functionLogicService: functionLogicService,
                quoteValueService: quoteValueService
        )

        List<BaseImportAction.ImportError> allErrorList = Lists.newArrayList()

        def dataList = createDataList()

        def sourceDataList = createDataDocumentList()
        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                isApprovalFlowEnabled: isApprovalFlowEnabled,
                importPreProcessing: importPreProcessing,
                tenantId: tenantId,
                userId: userId,
                apiName: objectApiName,
                importType: 0,
                matchingType: matchType,
                isEmptyValueToUpdate: false,
                rows: sourceDataList)

        StandardInsertImportDataAction standardInsertImportDataAction = new StandardInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                importLogMessageBuilder: importLogMessageBuilder,
                validateFuncAction: validateFuncAction,
                dataList: dataList,
                allErrorList: allErrorList,
                arg: arg)
        when:
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        // 使用 Groovy metaClass 替代 FieldUtils.removeFinalModifier

        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(_, _) >> true
        objectDescribe.isActive() >> true
        serviceFacade.findObject(_, _) >> objectDescribe
        infraServiceFacade.findBySwitchCache(_, _, _, _, _) >> Optional.of(uniqueRule)
        infraServiceFacade.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitchDTO
        optionalFeaturesSwitchDTO.getIsRelatedTeamEnabled() >> true
        infraServiceFacade.findEnableTeamRoleInfosWithoutOwner(_, _) >> []
        serviceFacade.getEnableDuplicateSearchRuleList(_, IDuplicatedSearch.Type.NEW, _, false, DuplicateSearchOrderByType.ORDER_BY_SORT) >> []
        serviceFacade.findDuplicatedSearchByApiNameAndType(_, _, IDuplicatedSearch.Type.NEW, false) >> []
        serviceFacade.fillDataOwnDeptAndOrgByOutUser(_, _, _) >> fillData
        infraServiceFacade.getTemplateField(actionContext.getUser(), objectDescribe) >> ObjectDescribeExt.of(objectDescribe).getFieldDescribes()
        serviceFacade.getFunctionLogicService() >> functionLogicService

        IUdefFunction function = new UdefFunction()
        function.setApiName("function_text__c")
        functionLogicService.findFunctionByFuncRelationKey(*_) >> Optional.of(function)
        infraServiceFacade.isUniqueCheck(*_) >> true
        functionLogicService.findUDefFunction(*_) >> function

        RunResult result = new RunResult()
        result.setSuccess(true)
        result.setReturnType("ValidateResult")
        ValidateFuncAction.ValidateResult validateResult = new ValidateFuncAction.ValidateResult()
        validateResult.setSuccess(true)
        result.setFunctionResult(validateResult)
        functionLogicService.executeUDefFunction(*_) >> result
        standardInsertImportDataAction.doAct(arg)
        then:
        noExceptionThrown()
        where:
        matchType | fillData | importPreProcessing | isApprovalFlowEnabled
        1         | true     | false               | true
        1         | false    | false               | false
        2         | true     | false               | true
        2         | false    | false               | false
    }

    def "test doActException"() {
        given:
        List<BaseImportDataAction.ImportData> importDataList = Lists.newArrayList()
        BaseImportDataAction.ImportData importData = new BaseImportDataAction.ImportData();
        importData.setData(new ObjectData())
        importData.setRowNo(1)
        importDataList.add(importData)
        List<BaseImportAction.ImportError> importErrorList = Lists.newArrayList()
        BaseImportAction.ImportError importError = new BaseImportAction.ImportError()
        importErrorList.add(importError)

        def sourceDataList = createDataDocumentList()
        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                importPreProcessing: importPreProcessing,
                tenantId: tenantId,
                userId: userId,
                apiName: objectApiName,
                importType: 0,
                matchingType: matchType,
                isEmptyValueToUpdate: false,
                rows: sourceDataList)
        StandardInsertImportDataAction standardInsertImportDataAction = new StandardInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                importLogMessageBuilder: importLogMessageBuilder,
                dataList: importDataList,
                allErrorList: importErrorList,
                arg: arg)
        when:
        objectDescribe.isActive() >> true
        serviceFacade.findObject(_, _) >> objectDescribe
        infraServiceFacade.findBySwitchCache(_, _, _, _, _) >> Optional.of(uniqueRule)
        infraServiceFacade.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitchDTO
        optionalFeaturesSwitchDTO.getIsRelatedTeamEnabled() >> true
        infraServiceFacade.findEnableTeamRoleInfosWithoutOwner(_, _) >> []
        serviceFacade.getEnableDuplicateSearchRuleList(_, IDuplicatedSearch.Type.NEW, _, false, DuplicateSearchOrderByType.ORDER_BY_SORT) >> []
        serviceFacade.findDuplicatedSearchByApiNameAndType(_, _, IDuplicatedSearch.Type.NEW, false) >> []
        serviceFacade.fillDataOwnDeptAndOrgByOutUser(_, _, _) >> fillData
        infraServiceFacade.getTemplateField(actionContext.getUser(), objectDescribe) >> ObjectDescribeExt.of(objectDescribe).getFieldDescribes()
        serviceFacade.getFunctionLogicService() >> functionLogicService
        functionLogicService.findFunctionByFuncRelationKey(*_) >> Optional.of(new UdefFunction())
        serviceFacade.findMainOrgAndDeptByUserId(_, _, _) >> OrganizationInfo.of(Collections.emptyList())
        standardInsertImportDataAction.doAct(arg)
        then:
        thrown(AcceptableValidateException.class)
        where:
        matchType | fillData | importPreProcessing
        1         | true     | true
        1         | false    | true
        2         | true     | true
        2         | false    | true
    }


    def "test after"() {
        given:
        List<IObjectData> actualList = Lists.newArrayList();
        IObjectData data = new ObjectData()
        data.setId(IdGenerator.get())
        actualList.add(data)
        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                isApprovalFlowEnabled: true,
                isWorkFlowEnabled: true)
        StandardInsertImportDataAction standardInsertImportDataAction = new StandardInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                actualList: actualList,
                arg: arg)
        BaseImportAction.Result result = new BaseImportAction.Result()
        when:
        standardInsertImportDataAction.after(arg, result)
        then:
        noExceptionThrown()
    }


    def "test validDuplicateSearch"() {
        given:

        List<BaseImportDataAction.ImportData> dataList = Lists.newArrayList()
        BaseImportDataAction.ImportData importData = new BaseImportDataAction.ImportData()
        importData.setRowNo(1)
        def data = new ObjectData()
        def id1 = IdGenerator.get()
        data.setId(id1)
        data.set(textFieldApiName, "test1")
        data.set(percentFieldApiName, "1")
        data.setOutOwner([outUserId])
        importData.setData(data)
        dataList.add(importData)

        BaseImportDataAction.ImportData importData2 = new BaseImportDataAction.ImportData()
        importData2.setRowNo(2)
        def data2 = new ObjectData()
        def id2 = IdGenerator.get()
        data2.setId(id2)
        data2.set(textFieldApiName, "test2")
        data2.setOutOwner(Lists.newArrayList())
        importData2.setData(data2)
        dataList.add(importData2)


        IDuplicatedSearch duplicatedSearch = new IDuplicatedSearch()
        duplicatedSearch.setSupportImport(true)
        duplicatedSearch.setEnable(true)
        duplicatedSearch.setRuleApiName(ruleApiName)
        duplicatedSearch.setDescribeApiName(objectApiName)
        duplicatedSearch.setName("查重")
        IDuplicatedSearch.RulesDef rulesDef = new IDuplicatedSearch.RulesDef()
        IDuplicatedSearch.Rule rule = new IDuplicatedSearch.Rule()
        rule.setConditions(Lists.newArrayList())
        rulesDef.setRules(Lists.newArrayList(rule))
        duplicatedSearch.setUseableRules(rulesDef)


        BaseImportAction.Arg arg = new BaseImportAction.Arg()
        StandardInsertImportDataAction standardInsertImportDataAction = new StandardInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                dataList: dataList,
                arg: arg,
                duplicatedSearchDataStoreService: duplicatedSearchDataStoreService,
                duplicatedSearchList: [duplicatedSearch]
        )


        DuplicateSearchDataInfo duplicateSearchDataInfo = DuplicateSearchDataInfo.builder()
                .sourceDataId(id1)
                .apiName(objectApiName)
                .ruleApiName(ruleApiName)
                .dataIds(new LinkedHashSet(id2 as Collection))
                .build()
        when:
        duplicatedSearchDataStoreService.multiSaveAndDuplicateData(_, _, _, _, _) >> [duplicateSearchDataInfo]
        serviceFacade.searchDuplicateDataListByType(*_) >> [duplicateSearchDataInfo]
        standardInsertImportDataAction.validDuplicateSearch()
        then:
        noExceptionThrown()
    }


    def "test validUniquenessRule"() {
        given:
        List<BaseImportDataAction.ImportData> dataList = Lists.newArrayList()
        BaseImportDataAction.ImportData importData = new BaseImportDataAction.ImportData()
        importData.setRowNo(1)
        def data = new ObjectData()
        def id1 = IdGenerator.get()
        data.setId(id1)
        data.set(textFieldApiName, "test1")
        data.set(percentFieldApiName, "1")
        data.setOutOwner([outUserId])
        importData.setData(data)
        dataList.add(importData)

        BaseImportDataAction.ImportData importData2 = new BaseImportDataAction.ImportData()
        importData2.setRowNo(2)
        def data2 = new ObjectData()
        def id2 = IdGenerator.get()
        data2.setId(id2)
        data2.set(textFieldApiName, "test2")
        data2.setOutOwner(Lists.newArrayList())
        importData2.setData(data2)
        dataList.add(importData2)

        IUniqueRule uniqueRule = new IUniqueRule()
        uniqueRule.setEffective(true)
        uniqueRule.setUseWhenImportExcel(true)
        IUniqueRule.Rule rule = new IUniqueRule.Rule()
        IUniqueRule.Condition condition = new IUniqueRule.Condition()
        condition.setFieldName(textFieldApiName)
        IUniqueRule.Condition condition2 = new IUniqueRule.Condition()
        condition.setFieldName(percentFieldApiName)
        List<IUniqueRule.Condition> conditions = Lists.newArrayList()
        conditions.add(condition)
        if (multUniqueField) {
            conditions.add(condition2)
        }
        rule.setConditions(conditions)
        List<IUniqueRule.Rule> rules = Lists.newArrayList(rule)
        IUniqueRule.VersionedRules versionedRules = IUniqueRule.VersionedRules.builder().rules(rules).version(1).build()
        uniqueRule.setUseableRules(versionedRules)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                matchingType: matchingType,
                specifiedField: specifiedField)
        StandardInsertImportDataAction action = new StandardInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                arg: arg,
                uniqueRule: uniqueRule,
                dataList: dataList)

        when:
        serviceFacade.findDuplicateData(_, _, _) >> []
        serviceFacade.findObjectDataByIds(_ as IActionContext, _ as List<String>, _ as String) >> []
        action.validUniquenessRule()
        then:
        noExceptionThrown()
        where:
        matchingType | specifiedField | multUniqueField
        1            | ""             | true
        3            | ""             | false
        4            | "name"         | true
    }


    def "test customConvertLabelToApiName"() {
        given:
        def dataList = createDataDocumentList()

        BaseImportAction.MasterInfo masterInfo = new BaseImportAction.MasterInfo()
        masterInfo.setApiName(objectApiName)

        def fieldMappings = createFieldMapping()

        Map<String, String> teamMemberLabelMap = Maps.newHashMap()
        teamMemberLabelMap.put(normal_read_only, "0_4_1")
        teamMemberLabelMap.put(normal_read_write, "0_4_2")

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                masterInfo: masterInfo,
                supportFieldMapping: supportFieldMapping,
                matchingType: matchingType
        )

        StandardInsertImportDataAction action = new StandardInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                arg: arg,
                validFieldDescribeList: fieldDescribeList,
                fieldMappings: fieldMappings,
                teamMemberLabelMap: teamMemberLabelMap
        )
        when:
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        // 使用 Groovy metaClass 替代 FieldUtils.removeFinalModifier

        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.CUSTOM_TEAM_TYPE_GRAY_EI, _ as String) >> customTeamTypeGray
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.CUSTOM_TEAM_ROLE_GRAY_EI, _ as String) >> customTeamRoleGray

        def grayRule = Mock(GrayRule)
        Map<String, GrayRule> grayRuleMap = [(objectApiName): grayRule]
        Whitebox.setInternalState(AppFrameworkConfig, "objectMultiLangGray", grayRuleMap)
        grayRule.isAllow(_) >> true
        action.customConvertLabelToApiName(dataList, objectDescribe)
        then:
        noExceptionThrown()
        where:
        matchingType | supportFieldMapping | customTeamRoleGray | customTeamTypeGray
        1            | false               | true               | true
        1            | false               | true               | false
        1            | false               | false              | true
        1            | false               | false              | false
        1            | true                | true               | true
        1            | true                | true               | false
        1            | true                | false              | true
        1            | true                | false              | false
    }

    def "test convertFields"() {
        given:
        def dataList = createDataList()

        BaseImportAction.MasterInfo masterInfo = new BaseImportAction.MasterInfo()
        masterInfo.setApiName(objectApiName)

        def fieldMappings = createFieldMapping()


        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                rows: createDataDocumentList(),
                masterInfo: masterInfo,
                supportFieldMapping: supportFieldMapping,
                matchingType: matchingType
        )

        StandardInsertImportDataAction action = new StandardInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                arg: arg,
                validFieldDescribeList: fieldDescribeList,
                fieldMappings: fieldMappings,
                dataList: dataList
        )
        when:
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        // 使用 Groovy metaClass 替代 FieldUtils.removeFinalModifier

        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(_, _) >> true
        serviceFacade.findObject(actionContext.getTenantId(), targetApiName) >> targetDescribe
        serviceFacade.findObjectDataByIdsIgnoreFormula(_, _, _) >> []
        action.convertFields(dataList)
        then:
        noExceptionThrown()
        where:
        matchingType | supportFieldMapping
        1            | false
        2            | false
        1            | true
        2            | true
    }

    def "test validateUniqueDataInExcel"() {
        given:
        def dataList = createDataList()

        BaseImportAction.MasterInfo masterInfo = new BaseImportAction.MasterInfo()
        masterInfo.setApiName(objectApiName)

        def fieldMappings = createFieldMapping()


        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                rows: createDataDocumentList(),
                masterInfo: masterInfo,
                supportFieldMapping: supportFieldMapping,
                matchingType: matchingType
        )

        StandardInsertImportDataAction action = new StandardInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                arg: arg,
                validFieldDescribeList: fieldDescribeList,
                fieldMappings: fieldMappings,
                dataList: dataList
        )
        when:
        def grayRule = Mock(GrayRule)
        Map<String, GrayRule> grayRuleMap = [(objectApiName): grayRule]
        Whitebox.setInternalState(AppFrameworkConfig, "objectMultiLangGray", grayRuleMap)
        grayRule.isAllow(_) >> true
        action.validateUniqueDataInExcel()
        then:
        noExceptionThrown()
        where:
        matchingType | supportFieldMapping
        1            | false
        2            | false
        1            | true
        2            | true
    }


    def "test validateMultiLang"() {
        given:
        def dataList = createDataList()

        BaseImportAction.MasterInfo masterInfo = new BaseImportAction.MasterInfo()
        masterInfo.setApiName(objectApiName)

        def fieldMappings = createFieldMapping()


        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                rows: createDataDocumentList(),
                masterInfo: masterInfo,
                supportFieldMapping: supportFieldMapping,
                matchingType: matchingType
        )

        StandardInsertImportDataAction action = new StandardInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                arg: arg,
                validFieldDescribeList: fieldDescribeList,
                fieldMappings: fieldMappings,
                dataList: dataList
        )
        when:
        def grayRule = Mock(GrayRule)
        Map<String, GrayRule> grayRuleMap = [(objectApiName): grayRule]
        Whitebox.setInternalState(AppFrameworkConfig, "objectMultiLangGray", grayRuleMap)
        grayRule.isAllow(_) >> true
        action.validateMultiLang(dataList, objectDescribe)
        then:
        noExceptionThrown()
        where:
        matchingType | supportFieldMapping
        1            | false
        2            | false
        1            | true
        2            | true
    }


    def createDataList() {
        List<BaseImportDataAction.ImportData> result = Lists.newArrayList()
        for (int i = 0; i < 5; i++) {
            BaseImportDataAction.ImportData importData = new BaseImportDataAction.ImportData()
            IObjectData objectData = new ObjectData()
            objectData.set("rowNo", i)
            objectData.setName("name" + i)
            objectData.set(textFieldApiName, "导入" + i)
            objectData.set(textFieldApiName + "__lang_zh-CN", "导入lang_zh" + i)
            objectData.set(ownerFieldApiName, "admin01")
            objectData.set(recordFieldApiName, "预设业务类型")
            objectData.set(field_country__c, "中国")
            objectData.set(field_province__c, "山西")
            objectData.set(field_city__c, "阳泉")
            objectData.set(field_district__c, "盂县")
            objectData.set(field_town__c, "上社镇")
            objectData.set(field_village__c, "上社村")
            objectData.set(normal_read_only, "张三")
            objectData.set(normal_read_write, "李四")
            objectData.set(field_reference_many__c, "查找关联多选")
            objectData.set(field_reference__c, "查找关联单选")
            objectData.setDataOwnDepartment(Lists.newArrayList(deptId))
            objectData.setDataOwnOrganization(Lists.newArrayList(deptOrg))
            objectData.set(IObjectData.IS_DELETED, false)
            importData.setRowNo(i)
            importData.setData(objectData)
            result.add(importData)
        }
        return result
    }


    def createDataDocumentList() {
        List<ObjectDataDocument> result = Lists.newArrayList()
        for (int i = 0; i < 5; i++) {
            Map<String, Object> map = Maps.newLinkedHashMap()
            map.put("rowNo", i)
            map.put(IObjectData.NAME, "主属性" + i)
            map.put(textFieldApiName, "导入" + i)
            map.put(textFieldApiName + "__lang_zh-CN", "导入lang_zh" + i)
            map.put(ownerFieldApiName, "admin01")
            map.put(recordFieldApiName, "预设业务类型")
            map.put(field_country__c, "中国")
            map.put(field_province__c, "山西")
            map.put(field_city__c, "阳泉")
            map.put(field_district__c, "盂县")
            map.put(field_town__c, "上社镇")
            map.put(field_village__c, "上社村")
            map.put(normal_read_only, "张三")
            map.put(normal_read_write, "李四")
            map.put(field_reference_many__c, "查找关联多选")
            map.put(field_reference__c, "查找关联单选")
            map.put(IObjectData.DATA_OWN_DEPARTMENT, deptId)
            map.put(IObjectData.DATA_OWN_ORGANIZATION, deptOrg)
            map.put(IObjectData.IS_DELETED, false)
            ObjectDataDocument document = ObjectDataDocument.of(map)
            result.add(document)
        }
        return result
    }

    def createFieldMapping() {
        Map<String, List<BaseImportAction.FieldMapping>> fieldMappingMap = Maps.newHashMap()
        List<BaseImportAction.FieldMapping> fieldMappings = Lists.newArrayList()

        BaseImportAction.FieldMapping nameFieldMapping = new BaseImportAction.FieldMapping()
        nameFieldMapping.setApiName(IObjectData.NAME)
        nameFieldMapping.setColIndex(IObjectData.NAME)
        fieldMappings.add(nameFieldMapping)

        BaseImportAction.FieldMapping dept = new BaseImportAction.FieldMapping()
        dept.setApiName(IObjectData.DATA_OWN_DEPARTMENT)
        dept.setColIndex(IObjectData.DATA_OWN_DEPARTMENT)
        fieldMappings.add(dept)

        BaseImportAction.FieldMapping org = new BaseImportAction.FieldMapping()
        org.setApiName(IObjectData.DATA_OWN_ORGANIZATION)
        org.setColIndex(IObjectData.DATA_OWN_ORGANIZATION)
        fieldMappings.add(org)

        BaseImportAction.FieldMapping textFieldMapping = new BaseImportAction.FieldMapping()
        textFieldMapping.setApiName(textFieldApiName)
        textFieldMapping.setColIndex(textFieldApiName)
        fieldMappings.add(textFieldMapping)

        BaseImportAction.FieldMapping fieldMappingLang = new BaseImportAction.FieldMapping()
        fieldMappingLang.setApiName(textFieldApiName + "__lang_zh-CN")
        fieldMappingLang.setColIndex(textFieldApiName + "__lang_zh-CN")
        fieldMappings.add(fieldMappingLang)


        BaseImportAction.FieldMapping ownerFieldMapping = new BaseImportAction.FieldMapping()
        ownerFieldMapping.setApiName(ownerFieldApiName)
        ownerFieldMapping.setColIndex(ownerFieldApiName)
        fieldMappings.add(ownerFieldMapping)

        BaseImportAction.FieldMapping recordFieldMapping = new BaseImportAction.FieldMapping()
        recordFieldMapping.setApiName(textFieldApiName)
        recordFieldMapping.setColIndex(textFieldApiName)
        fieldMappings.add(recordFieldMapping)

        BaseImportAction.FieldMapping country = new BaseImportAction.FieldMapping()
        country.setApiName(field_country__c)
        country.setColIndex(field_country__c)
        fieldMappings.add(country)

        BaseImportAction.FieldMapping province = new BaseImportAction.FieldMapping()
        province.setApiName(field_province__c)
        province.setColIndex(field_province__c)
        fieldMappings.add(province)

        BaseImportAction.FieldMapping city = new BaseImportAction.FieldMapping()
        city.setApiName(field_city__c)
        city.setColIndex(field_city__c)
        fieldMappings.add(city)

        BaseImportAction.FieldMapping district = new BaseImportAction.FieldMapping()
        district.setApiName(field_district__c)
        district.setColIndex(field_district__c)
        fieldMappings.add(district)

        BaseImportAction.FieldMapping town = new BaseImportAction.FieldMapping()
        town.setApiName(field_town__c)
        town.setColIndex(field_town__c)
        fieldMappings.add(town)

        BaseImportAction.FieldMapping village = new BaseImportAction.FieldMapping()
        village.setApiName(field_village__c)
        village.setColIndex(field_village__c)
        fieldMappings.add(village)

        BaseImportAction.FieldMapping normalReadOnly = new BaseImportAction.FieldMapping()
        normalReadOnly.setApiName(normal_read_only)
        normalReadOnly.setColIndex(normal_read_only)
        normalReadOnly.setImportFieldMark(ImportExportExt.TEAM_MEMBER_MARK)
        fieldMappings.add(normalReadOnly)

        BaseImportAction.FieldMapping normalReadWrite = new BaseImportAction.FieldMapping()
        normalReadWrite.setApiName(normal_read_write)
        normalReadWrite.setColIndex(normal_read_write)
        normalReadWrite.setImportFieldMark(ImportExportExt.TEAM_MEMBER_MARK)
        fieldMappings.add(normalReadWrite)


        fieldMappingMap.put(objectApiName, fieldMappings)
        return fieldMappingMap
    }

    def createObjectDescribe() {
        IObjectDescribe describe = new ObjectDescribe(JSON.parseObject(describeJson, Map.class))
        return describe
    }
    @Shared
    def describeJson = '''{"fields":{"field_m0KDi__c":{"describe_api_name":"object_z87LW__c","default_is_expression":false,"description":"","is_unique":false,"type":"number","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_1","max_length":14,"is_index":true,"is_active":true,"create_time":1568702145131,"length":12,"default_value":"","label":"数字","field_num":7,"api_name":"field_m0KDi__c","_id":"5d807ec1a5083d69323353b4","is_index_field":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"is_unique":1,"default_value":1,"label":1,"help_text":1,"max_length":1,"decimal_places":1}},"round_mode":4,"help_text":"","status":"new"},"field_o10qI__c":{"describe_api_name":"object_z87LW__c","default_is_expression":false,"is_index":true,"is_active":true,"create_time":1573699472718,"is_unique":false,"default_value":"","label":"日期","time_zone":"GMT+8","type":"date","field_num":19,"default_to_zero":false,"is_required":false,"api_name":"field_o10qI__c","define_type":"custom","date_format":"yyyy-MM-dd","_id":"5dccbf90a5083d9644cd13e4","is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"is_unique":1,"default_value":1,"label":1,"help_text":1}}},"life_status":{"describe_api_name":"object_z87LW__c","is_index":true,"is_active":true,"create_time":1566181304572,"description":"","is_unique":false,"default_value":"normal","label":"生命状态","type":"select_one","field_num":3,"is_need_convert":false,"is_required":false,"api_name":"life_status","options":[{"label":"未生效","value":"ineffective","config":{"edit":0,"enable":0,"remove":0}},{"label":"审核中","value":"under_review","config":{"edit":0,"enable":0,"remove":0}},{"label":"正常","value":"normal","config":{"edit":0,"enable":0,"remove":0}},{"label":"变更中","value":"in_change","config":{"edit":0,"enable":0,"remove":0}},{"label":"作废","value":"invalid","config":{"edit":0,"enable":0,"remove":0}}],"define_type":"package","_id":"5d5a07b8a5083dd185da53ac","is_index_field":false,"is_single":false,"config":{"add":0,"edit":0,"enable":0,"display":1,"remove":0,"attrs":{"is_readonly":0,"is_required":0,"api_name":0,"options":0,"is_unique":0,"default_value":0,"label":0,"help_text":0}},"index_name":"s_3","help_text":"","status":"new"},"life_status_before_invalid":{"describe_api_name":"object_z87LW__c","is_index":false,"is_active":true,"create_time":1571192856477,"pattern":"","description":"作废前生命状态","is_unique":false,"label":"作废前生命状态","type":"text","field_num":4,"is_need_convert":false,"is_required":false,"api_name":"life_status_before_invalid","define_type":"package","_id":"5da68018a5083d686f162409","is_index_field":false,"is_single":false,"index_name":"t_1","max_length":256,"status":"new"},"lock_rule":{"describe_api_name":"object_z87LW__c","is_index":false,"is_active":true,"create_time":1571192856477,"description":"锁定规则","is_unique":false,"default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","field_num":1,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"5da68018a5083d686f162406","is_index_field":false,"is_single":false,"index_name":"s_1","status":"new"},"lock_status":{"describe_api_name":"object_z87LW__c","is_index":true,"is_active":true,"create_time":1566181304559,"description":"锁定状态","is_unique":false,"default_value":"0","label":"锁定状态","type":"select_one","field_num":2,"is_need_convert":false,"is_required":false,"api_name":"lock_status","options":[{"label":"未锁定","value":"0","config":{"edit":0,"enable":0,"remove":0}},{"label":"锁定","value":"1","config":{"edit":0,"enable":0,"remove":0}}],"define_type":"package","_id":"5d5a07b8a5083dd185da53ab","is_index_field":false,"is_single":false,"config":{"add":0,"edit":0,"enable":0,"display":1,"remove":0,"attrs":{"is_readonly":0,"is_required":0,"api_name":0,"options":0,"is_unique":0,"default_value":0,"label":0,"help_text":0}},"index_name":"s_2","help_text":"","status":"new"},"lock_user":{"describe_api_name":"object_z87LW__c","is_index":false,"is_active":true,"create_time":1571192856477,"description":"加锁人","is_unique":false,"label":"加锁人","type":"employee","field_num":5,"is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"5da68018a5083d686f162407","is_index_field":false,"is_single":true,"index_name":"a_1","status":"new"},"name":{"describe_api_name":"object_z87LW__c","default_is_expression":false,"pattern":"","description":"name","is_unique":true,"type":"text","default_to_zero":false,"is_required":true,"define_type":"system","is_single":false,"index_name":"name","max_length":100,"is_index":true,"is_active":true,"create_time":1566181304604,"default_value":"","label":"主属性","api_name":"name","_id":"5d5a07b8a5083dd185da53ae","is_index_field":false,"config":{"add":0,"edit":1,"enable":0,"display":1,"remove":0,"attrs":{"api_name":1,"is_unique":1,"label":1,"type":1,"help_text":1}},"help_text":"","status":"new"},"owner":{"describe_api_name":"object_z87LW__c","is_index":true,"is_active":true,"create_time":1566181304569,"description":"","is_unique":false,"label":"负责人","type":"employee","is_need_convert":false,"is_required":true,"api_name":"owner","define_type":"package","_id":"5d5a07b8a5083dd185da53a9","is_index_field":false,"is_single":true,"config":{"add":0,"edit":0,"enable":0,"display":1,"remove":0,"attrs":{"api_name":0,"label":0}},"index_name":"owner","help_text":"","status":"new"},"owner_department":{"describe_api_name":"object_z87LW__c","default_is_expression":false,"pattern":"","description":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"package","is_single":true,"index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1566181304570,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"5d5a07b8a5083dd185da53af","is_index_field":false,"config":{"add":0,"edit":0,"enable":0,"display":1,"remove":0,"attrs":{"api_name":0,"label":0}},"help_text":"","status":"new"},"record_type":{"describe_api_name":"object_z87LW__c","is_index":true,"is_active":true,"create_time":1566181304571,"description":"record_type","is_unique":false,"label":"业务类型","type":"record_type","is_need_convert":false,"is_required":true,"api_name":"record_type","options":[{"is_active":true,"api_name":"default__c","description":"预设业务类型","label":"预设业务类型","config":{"edit":1,"enable":0,"remove":0}}],"define_type":"package","_id":"5d5a07b8a5083dd185da53b1","is_index_field":false,"is_single":false,"config":{"add":0,"edit":0,"enable":0,"display":1,"remove":0,"attrs":{"api_name":0,"label":0}},"index_name":"r_type","help_text":"","status":"released"},"relevant_team":{"describe_api_name":"object_z87LW__c","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","is_unique":false,"define_type":"package","description":"成员员工","label":"成员员工","type":"employee","is_single":true,"help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"is_unique":false,"define_type":"package","description":"成员角色","label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"is_unique":false,"define_type":"package","description":"成员权限类型","label":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1571192856589,"is_unique":false,"label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"5da68018a5083d686f162408","is_index_field":false,"is_single":false,"index_name":"a_team","help_text":"相关团队","status":"new"},"_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"_id","api_name":"_id","description":"_id","status":"released","index_name":"_id","create_time":1566181304573},"created_by":{"type":"employee","define_type":"system","is_index":true,"is_need_convert":true,"is_required":false,"is_unique":false,"is_single":true,"api_name":"created_by","status":"released","label":"创建人","is_active":true,"index_name":"crt_by","create_time":1566181304573},"last_modified_by":{"type":"employee","define_type":"system","is_index":true,"is_need_convert":true,"is_required":false,"is_unique":false,"is_single":true,"api_name":"last_modified_by","status":"released","is_active":true,"index_name":"md_by","label":"最后修改人","create_time":1566181304573},"package":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"package","api_name":"package","description":"package","status":"released","create_time":1566181304573,"index_name":"pkg"},"tenant_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":true,"is_unique":false,"max_length":200,"pattern":"","label":"tenant_id","api_name":"tenant_id","description":"tenant_id","status":"released","create_time":1566181304573,"index_name":"ei"},"object_describe_api_name":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":true,"is_unique":false,"max_length":200,"pattern":"","label":"object_describe_api_name","api_name":"object_describe_api_name","description":"object_describe_api_name","status":"released","index_name":"api_name","create_time":1566181304573},"version":{"type":"number","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"round_mode":4,"length":8,"decimal_places":0,"label":"version","api_name":"version","description":"version","status":"released","index_name":"version","create_time":1566181304573},"object_describe_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"object_describe_id","api_name":"object_describe_id","description":"object_describe_id","status":"released","index_name":"object_describe_id","create_time":1566181304573},"create_time":{"type":"date_time","define_type":"system","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"time_zone":"","date_format":"yyyy-MM-dd HH:mm:ss","label":"创建时间","api_name":"create_time","description":"create_time","status":"released","index_name":"crt_time","create_time":1566181304573},"last_modified_time":{"type":"date_time","define_type":"system","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"time_zone":"","date_format":"yyyy-MM-dd HH:mm:ss","label":"最后修改时间","api_name":"last_modified_time","description":"last_modified_time","status":"released","index_name":"md_time","create_time":1566181304573},"is_deleted":{"type":"true_or_false","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"label":"is_deleted","api_name":"is_deleted","description":"is_deleted","default_value":false,"status":"released","index_name":"is_del","create_time":1566181304573},"out_tenant_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"外部企业","api_name":"out_tenant_id","description":"out_tenant_id","status":"released","index_name":"o_ei","create_time":1566181304573,"config":{"display":0}},"out_owner":{"type":"employee","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"is_single":true,"api_name":"out_owner","index_name":"o_owner","status":"released","label":"外部负责人","config":{"display":1},"create_time":1566181304573},"data_own_department":{"type":"department","define_type":"package","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"is_single":true,"api_name":"data_own_department","status":"released","label":"归属部门","is_active":true,"index_name":"data_owner_dept_id","create_time":1566181304573},"order_by":{"type":"number","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"round_mode":4,"length":8,"decimal_places":0,"label":"order_by","api_name":"order_by","description":"order_by","status":"released","index_name":"o_by","create_time":1566181304573}},"actions":{},"index_version":1,"_id":"5d5a07b8a5083dd185da53a8","tenant_id":"78057","is_udef":true,"api_name":"object_z87LW__c","created_by":"1000","last_modified_by":"1000","display_name":"yuan","package":"CRM","is_active":true,"icon_path":"A_201705_11_e4e168dc5ce84c2c84abe7767827bbf0.png","version":36,"release_version":"6.4","define_type":"custom","is_deleted":false,"config":{"button":{"add":1},"layout":{"add":1,"assign":1},"layout_rule":{"add":1},"edit":1,"cascade":{"add":1},"rule":{"add":1},"fields":{"add":1},"record_type":{"add":1,"assign":1}},"last_modified_time":1574059100620,"create_time":1566181304573,"icon_index":0,"description":""}'''
}
