package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectFieldExtra;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * create by zhaoju on 2020/04/17
 */
public class StandardButtonLayoutController extends PreDefineController<StandardButtonLayoutController.Arg, StandardButtonLayoutController.Result> {

    protected IObjectDescribe describe;
    protected IObjectData objectData;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        ObjectAction objectAction = ObjectAction.getByButtonApiName(arg.getButtonApiName());
        if (ObjectAction.UNKNOWN_ACTION == objectAction) {
            return Lists.newArrayList(arg.getButtonApiName());
        }
        return Lists.newArrayList(objectAction.getPrivilegeCode(ObjectAPINameMapping.isSFAObject(controllerContext.getObjectApiName())));
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        stopWatch.lap("doFunPrivilegeCheck");
        //获取对象描述
        describe = findObject(arg);
        stopWatch.lap("findObject");
        //查询objectData
        objectData = findObjectData(arg);
        stopWatch.lap("findObjectData");
    }

    private IObjectData findObjectData(Arg arg) {
        return serviceFacade.findObjectData(controllerContext.getUser(), arg.getObjectDataId(), describe);
    }

    private IObjectDescribe findObject(Arg arg) {
        return serviceFacade.findObjectIncludeMultiField(controllerContext.getTenantId(), controllerContext.getObjectApiName());
    }

    @Override
    protected Result doService(Arg arg) {
        IUdefButton button = findButton();
        return buildResult(button);
    }

    protected IUdefButton findButton() {
        IUdefButton button = serviceFacade.findButtonByApiName(controllerContext.getUser(), arg.getButtonApiName(), controllerContext.getObjectApiName());
        if (button == null) {
            return null;
        }
        ButtonExt buttonExt = ButtonExt.of(button);
        Set<String> readonlyFields = serviceFacade.getReadonlyFields(controllerContext.getUser(), describe.getApiName());
        Map<String, IObjectFieldExtra> extraHashMap = getDescribeExtra();
        buttonExt.handleButtonParam(controllerContext.getUser(), objectData, describe, extraHashMap, readonlyFields);
        return button;
    }

    private Map<String, IObjectFieldExtra> getDescribeExtra() {
        DescribeExtra describeExtra = serviceFacade.findDescribeExtra(controllerContext.getUser(), describe);
        return Optional.ofNullable(describeExtra)
                .map(it -> it.getDescribeExtra(describe.getApiName()))
                .orElseGet(Maps::newHashMap);
    }

    private Result buildResult(IUdefButton button) {
        return Result.builder()
                .button(ButtonDocument.of(button, true, true))
                .objectData(ObjectDataDocument.of(objectData))
                .build();
    }


    @Data
    public static class Arg {
        private String buttonApiName;
        private String objectDataId;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private ButtonDocument button;
        private ObjectDataDocument objectData;
    }
}
