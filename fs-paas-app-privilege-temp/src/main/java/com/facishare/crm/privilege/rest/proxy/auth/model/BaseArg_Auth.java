package com.facishare.crm.privilege.rest.proxy.auth.model;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.crm.valueobject.SessionContext;
import com.facishare.rest.core.util.JsonUtil;
import lombok.Data;

import java.util.Map;

/**
 * 底层REST服务的BaseArg。
 * Type1类型:海波团队的REST基础参数。
 * Created by yusb on 17/4/13.
 */
@Data
public class BaseArg_Auth {

    private BaseAuthContext authContext = new BaseAuthContext();

    public void setAuthContext(SessionContext cxt) {
        authContext.setTenantId(cxt.getEId().toString());
        authContext.setAppId(DefObjConstants.PACKAGE_NAME_CRM);
        authContext.setUserId(cxt.getUserIdString());
    }

    public void setObjectProperties(Map<String, Object> objectProperties) {
        this.authContext.setObjectProperties(objectProperties);
    }

    public void setProperties(Map<String, Object> properties) {
        this.authContext.setProperties(properties);
    }

    @Override
    public String toString() {
        return JsonUtil.toJson(this);
    }
}
