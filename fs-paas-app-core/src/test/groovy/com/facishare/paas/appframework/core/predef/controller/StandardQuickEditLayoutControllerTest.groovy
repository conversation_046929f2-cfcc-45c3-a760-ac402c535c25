package com.facishare.paas.appframework.core.predef.controller


import com.facishare.paas.appframework.core.model.*
import spock.lang.Specification

/**
 * Created by zhouwr on 2020/1/3
 */
class StandardQuickEditLayoutControllerTest extends Specification {

    StandardQuickEditLayoutController controller
    ServiceFacade serviceFacade

    def setup() {
        System.setProperty("process.profile", "ceshi113");

        def requestContext = RequestContext.builder()
                .tenantId("55732")
                .user(new User("55732", "1000"))
                .build()
        RequestContextManager.setContext(requestContext)
        def controllerContext = new ControllerContext(requestContext, "object_7oz5u__c", "QuickEditLayout")

        serviceFacade = Mock(ServiceFacade)
//        serviceFacade.isEnableQixinGroup(_, _) >> true

        def arg = new StandardQuickEditLayoutController.Arg()
        arg.setDataId("5df9fe72bab09c020652fe04")
        arg.setFieldApiName("field_m2qTs__c")
        controller = new StandardQuickEditLayoutController()
        controller.controllerContext = controllerContext
        controller.arg = arg
        controller.serviceFacade = serviceFacade
    }

    def "test validateByConfig"() {
        expect:
        1 == 1
        /*
        when:
        controller.validateByConfig()
        then:
        noExceptionThrown()
        */
    }

    def "test validateByConfig exception"() {
        expect:
        1 == 1
        /*
        given:
        controller.arg.setFieldApiName("life_status")
        when:
        controller.validateByConfig()
        then:
        thrown(ValidateException)
        */
    }

}
