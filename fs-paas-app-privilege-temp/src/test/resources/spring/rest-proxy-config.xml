<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

  <!-- swagger的api配置 -->
  <bean id="roleApi" class="com.facishare.crm.privilege.controller.RoleController"/>
  <bean id="groupApi" class="com.facishare.crm.privilege.controller.GroupController"/>
  <bean id="userApi" class="com.facishare.crm.privilege.controller.UserController"/>
  <bean id="groupShareApi" class="com.facishare.crm.privilege.controller.share.GroupShareController"/>
  <bean id="roleShareApi" class="com.facishare.crm.privilege.controller.share.RoleShareController"/>

</beans>