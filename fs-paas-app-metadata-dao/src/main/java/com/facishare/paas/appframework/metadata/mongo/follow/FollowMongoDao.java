package com.facishare.paas.appframework.metadata.mongo.follow;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.mongo.follow.bean.BaseFollowInfo;
import com.facishare.paas.appframework.metadata.mongo.follow.bean.MongoConvert;
import com.facishare.paas.appframework.metadata.mongo.follow.bean.QueryCondition;
import com.fxiaoke.api.IdGenerator;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.mongodb.ErrorCategory;
import com.mongodb.MongoBulkWriteException;
import com.mongodb.WriteError;
import com.mongodb.bulk.BulkWriteError;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.BulkWriteOptions;
import com.mongodb.client.model.InsertOneModel;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.mongo.follow.bean.BaseFollowInfo.TENANT_ID;


@Slf4j
public class FollowMongoDao<T extends BaseFollowInfo> {

    public static final String DB_NAME = "paas_follow";

    @Setter
    private DatastoreExt followDataStore;

    public void bulkCreate(User user, List<T> followInfoList) {
        long count = countByCondition(user, new QueryCondition());
        if (count > 10000) {
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.FOLLOW_LIMIT, "系统中最多关注上限10000条对象数据，请移除不需要的关注数据再进行关注", 10000));// ignoreI18n
        }
        List<InsertOneModel<Document>> documentList = Lists.newArrayList();
        for (T followInfo : followInfoList) {
            long currentTime = System.currentTimeMillis();
            followInfo.setId(IdGenerator.get());
            followInfo.setFollowTime(new Date(currentTime));
            followInfo.setTenantId(user.getTenantId());
            if (user.isOutUser()) {
                followInfo.setOutTenantId(user.getOutTenantId());
                followInfo.setOutUserId(user.getOutUserId());
            } else {
                followInfo.setUserId(user.getUserId());
            }
            documentList.add(new InsertOneModel<>(followInfo.toDocument()));
        }

        try {
            MongoCollection<Document> mongoCollection = getMongoCollection(user);
            mongoCollection.bulkWrite(documentList, new BulkWriteOptions().ordered(false));
        } catch (MongoBulkWriteException e) {
            if (CollectionUtils.notEmpty(e.getWriteErrors())) {
                List<BulkWriteError> writeErrors = e.getWriteErrors();
                Set<String> failMessages = writeErrors.stream()
                        .filter(x -> ErrorCategory.DUPLICATE_KEY == ErrorCategory.fromErrorCode(x.getCode()))
                        .map(WriteError::getMessage)
                        .collect(Collectors.toSet());
                if (CollectionUtils.notEmpty(failMessages)) {
                    log.warn("bulkCreate follow data error,user:{},duplicate key failMessages:{}", user, failMessages, e);
                }
                Set<String> otherFailMessages = writeErrors.stream()
                        .filter(x -> !(ErrorCategory.DUPLICATE_KEY == ErrorCategory.fromErrorCode(x.getCode())))
                        .map(WriteError::getMessage)
                        .collect(Collectors.toSet());
                if (CollectionUtils.notEmpty(otherFailMessages)) {
                    log.warn("bulkCreate follow data error,user:{},otherFailMessages:{}", user, otherFailMessages, e);
                }
            }
        } catch (Exception e) {
            log.error("bulkCreate follow data error,user:{}", user, e);
            throw new MetaDataBusinessException(I18NExt.getOrDefault(I18NKey.FOLLOW_FAILED, "关注失败"));// ignoreI18n
        }

    }

    private MongoCollection<Document> getMongoCollection(User user) {
        return followDataStore
                .setTenantId(user.getTenantId())
                .getMongo()
                .getDatabase(DB_NAME)
                .getCollection(getFollowCollectionName(user.getTenantId()));
    }

    public void bulkDeleteByCondition(User user, QueryCondition condition) {
        MongoCollection<Document> mongoCollection = getMongoCollection(user);
        mongoCollection.deleteMany(buildCondition(user, condition));
    }

    public List<T> findByCondition(User user, QueryCondition condition, MongoConvert<T> mongoConvert) {
        MongoCollection<Document> mongoCollection = getMongoCollection(user);
        FindIterable<Document> documents = mongoCollection
                .find(buildCondition(user, condition))
                .limit(condition.getLimit());
        List<T> followInfoList = Lists.newArrayList();
        for (Document document : documents) {
            T followInfo = mongoConvert.convert(document);
            followInfoList.add(followInfo);
        }
        return followInfoList;
    }

    private Document buildCondition(User user, QueryCondition condition) {
        Document document = new Document();
        document.put(TENANT_ID, user.getTenantId());
        Map<String, Object> equal = condition.getEqual();
        if (CollectionUtils.notEmpty(equal)) {
            document.putAll(equal);
        }
        Map<String, List<Object>> in = condition.getIn();
        if (CollectionUtils.notEmpty(in)) {
            in.forEach((key, value) -> document.put(key, new Document("$in", value)));
        }
        return document;
    }

    public long countByCondition(User user, QueryCondition condition) {
        MongoCollection<Document> mongoCollection = getMongoCollection(user);
        Document filter = buildCondition(user, condition);
        return mongoCollection.countDocuments(filter);
    }

    private String getFollowCollectionName(String tenantId) {
        return DB_NAME + "_" + Math.abs(tenantId.hashCode() & 0x7fffffff) % 100;
    }
}
