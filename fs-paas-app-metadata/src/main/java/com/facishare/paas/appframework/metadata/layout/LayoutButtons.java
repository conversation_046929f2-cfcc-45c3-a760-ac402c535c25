package com.facishare.paas.appframework.metadata.layout;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.ui.layout.IButton;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.util.ObjectAction.*;

/**
 * 设计器中各对象按钮枚举类型
 * 不同对象有不同的默认按钮排序
 *
 * <AUTHOR>
 */
public enum LayoutButtons {

    /**
     * 设计器中不同对象按钮默认排序
     */
    //公共部分
    CommonTail(SEND_MAIL, DISCUSS, REMIND, SCHEDULE, PRINT),
    //需要给SFA对象补充的按钮，不走权限
    Supplements(SEND_MAIL, DISCUSS, REMIND, SCHEDULE),
    //产品对象特殊补充按钮
    SupplementsForProduct(SEND_MAIL, DISCUSS, REMIND, SCHEDULE, DIAL),
    //只有Web端显示的按钮
    WebOnlyButtons(ADD_SPEC),
    //客户主数据
    AccountMainDataObj(UPDATE, ALLOCATE_MAIN_DATA, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, DELETE, LOCK, UNLOCK,
            CLONE, ADD_TEAM_MEMBER, EDIT_TEAM_MEMBER, DELETE_TEAM_MEMBER),
    //客户
    AccountObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, DELETE, RECOVER, START_STAGE_PROPELLOR, LOCK, UNLOCK,
            CLONE, CHOOSE, ALLOCATE, RETURN, TAKE_BACK, MOVE, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER,
            UPDATE_DEAL_STATUS, VIEW_FEED_CARD, AUDIT, CHANGE_AUDITOR, ADD_TEAM_MEMBER, EDIT_TEAM_MEMBER,
            DELETE_TEAM_MEMBER, EXTEND_EXPIRETIME, CREATE_CHECKINS),
    //            BLACKLIST_MARK, BLACKLIST_CANCEL, RISK_MONITOR_ENABLE, RISK_MONITOR_DISABLE, RISK_PORTRAIT_ENABLE),
    //联系人
    ContactObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, DELETE, RECOVER, LOCK, UNLOCK,
            CLONE, SAVE_TO_PHONE, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER, ADD_TEAM_MEMBER, EDIT_TEAM_MEMBER, DELETE_TEAM_MEMBER),
    //商机
    OpportunityObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, DELETE, RECOVER, LOCK, UNLOCK,
            CLONE, CHANGE_SALE_ACTION, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER, VIEW_BEFORE_SALE_ACTION, VIEW_AFTER_SALE_ACTION, ADD_TEAM_MEMBER, EDIT_TEAM_MEMBER, DELETE_TEAM_MEMBER),
    NewOpportunityObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, START_STAGE_PROPELLOR, LOCK, UNLOCK,
            CLONE, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER, CREATE_CHECKINS),
    //销售订单
    SalesOrderObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, DELETE, RECOVER, START_STAGE_PROPELLOR, LOCK, UNLOCK,
            CLONE, COLLECT, RECALL, CONFIRM_DELIVERY, CONFIRM_RECEIPT, ADD_DELIVERY_NOTE, CHANGE_PARTNER,
            CHANGE_PARTNER_OWNER, DELETE_PARTNER, CONFIRM, REJECT, CHANGE_CONFIRMOR, ADD_TEAM_MEMBER, EDIT_TEAM_MEMBER,
            DELETE_TEAM_MEMBER, PAY_INSTANTLY, CONFIRM_RECEIPT2, BUY_AGAIN, ONE_MORE_ORDER, TRANSFER_ORDER),
    //销售线索
    LeadsObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, DELETE, RECOVER, START_STAGE_PROPELLOR, LOCK, UNLOCK,
            CLONE, TRANSFER, RETURN, CHOOSE, ALLOCATE, MOVE, TAKE_BACK, CLOSE, FOLLOW_UP, CHANGE_PARTNER, CHANGE_PARTNER_OWNER,
            DELETE_PARTNER, VIEW_FEED_CARD, ADD_TEAM_MEMBER, EDIT_TEAM_MEMBER, DELETE_TEAM_MEMBER, EXTEND_EXPIRETIME,
            MarkMQL, COLLECT_TO, TRANSFER_PARTNER, PROCESS_LEADS, CREATE_CHECKINS, REVISIT_VIRTUAL_PHONE),
    //退货单
    ReturnedGoodsInvoiceObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, DELETE, RECOVER, LOCK, UNLOCK,
            CLONE, RECALL, CONFIRM, REJECT, CHANGE_CONFIRMOR, ADD_TEAM_MEMBER, EDIT_TEAM_MEMBER, DELETE_TEAM_MEMBER,
            ENTER_ACCOUNT, CANCEL_ENTRY),
    //回款
    PaymentObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, DELETE, RECOVER, LOCK, UNLOCK,
            CLONE, ENTER_ACCOUNT, CANCEL_ENTRY),
    //发货单
    DeliveryNoteObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, VIEW_LOGISTICS, CONFIRM_RECEIPT,
            LOCK, UNLOCK, CLONE, VIRTUAL_STOCK_IN),
    //调拨单
    RequisitionNoteObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, LOCK, UNLOCK, CLONE),
    //产品
    ProductObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, INVALID, LOCK, UNLOCK, CLONE),
    //销售订单产品
    SalesOrderProductObj(SALE_RECORD, DIAL),
    //退货单产品
    ReturnedGoodsInvoiceProductObj(SALE_RECORD, DIAL),
    //工单
    //CasesObj(UPDATE, SERVICE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, LOCK, UNLOCK, CLONE, CANCEL_CASES_BPM, CASES_MULTIPLE_CHECKINS, CASES_RELATED_INFO, CASES_KNOWLEDGE_RECOMMEND, VIEW_CASES_ENGINEER_BUSY_IDLE, RECEIVE_CASES),
    CasesObj(UPDATE, SERVICE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, LOCK, UNLOCK, CLONE),
    //退款
    RefundObj(UPDATE, INVALID, DELETE, RECOVER, LOCK, UNLOCK, CLONE, CHANGE_OWNER, CONFIRM, SALE_RECORD, REJECT, DIAL,
            ADD_TEAM_MEMBER, EDIT_TEAM_MEMBER, DELETE_TEAM_MEMBER, ENTER_ACCOUNT, CANCEL_ENTRY),
    //开票申请
    InvoiceApplicationObj(UPDATE, INVALID, DELETE, RECOVER, LOCK, UNLOCK, CLONE, CHANGE_OWNER, CONFIRM, SALE_RECORD, REJECT, DIAL, ADD_TEAM_MEMBER, EDIT_TEAM_MEMBER, DELETE_TEAM_MEMBER),
    //市场活动
    MarketingEventObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, START_STAGE_PROPELLOR, LOCK, UNLOCK, CLONE, CHANGE_STATES, ADD_TEAM_MEMBER, EDIT_TEAM_MEMBER, DELETE_TEAM_MEMBER),
    //市场活动贡献
    MarketingEventInfluenceObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, LOCK, UNLOCK, CLONE, CHANGE_STATES, ADD_TEAM_MEMBER, EDIT_TEAM_MEMBER, DELETE_TEAM_MEMBER, RECALCULATE),
    //项目管理
    ProjectObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, START_STAGE_PROPELLOR, LOCK, UNLOCK, CLONE, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER, ADD_TEAM_MEMBER, EDIT_TEAM_MEMBER, DELETE_TEAM_MEMBER, GANTT_VIEW, KANBAN_VIEW, RESOURCE_VIEW, MEMBER_WORKING_HOURS_FILL),
    //活动成员
    CampaignMembersObj(UPDATE, CHANGE_STATUS, REMOVE_OBJECT, SALE_RECORD, DIAL),
    //合同
    ContractObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, LOCK, UNLOCK, CLONE, ADD_TEAM_MEMBER, EDIT_TEAM_MEMBER, DELETE_TEAM_MEMBER),
    EnterpriseRelationObj(UPDATE, CHANGE_OWNER, START_BPM, INVALID, DELETE, RECOVER, LOCK, UNLOCK, CLONE, ADD_TEAM_MEMBER, EDIT_TEAM_MEMBER, DELETE_TEAM_MEMBER, START_ENTERPRISE_RELATION, STOP_ENTERPRISE_RELATION),
    PublicEmployeeObj(UPDATE, CHANGE_OWNER, START_BPM, INVALID, DELETE, RECOVER, LOCK, UNLOCK, CLONE, ADD_TEAM_MEMBER, EDIT_TEAM_MEMBER, DELETE_TEAM_MEMBER, START_PUBLIC_EMPLOYEE, STOP_PUBLIC_EMPLOYEE, RESET_PUBLIC_EMPLOYEE_PASSWORD),
    PartnerObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, START_STAGE_PROPELLOR, LOCK, UNLOCK,
            CLONE, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER, CREATE_CHECKINS),
    PartnerAgreementDetailObj(UPDATE, CHANGE_OWNER, INVALID, LOCK, UNLOCK, INITIATE_RENEWAL, RENEW),
    FAccountAuthorizationObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, START_STAGE_PROPELLOR, LOCK, UNLOCK,
            CLONE, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER, Init),
    //    SalesStatementsObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, START_STAGE_PROPELLOR, LOCK, UNLOCK,
//            CLONE, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER, Print_Receipt, Conformance_Statements),
    //自定义对象(包括报价单、入库单、价目表等)
    UserDefinedObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, START_STAGE_PROPELLOR, LOCK, UNLOCK,
            CLONE, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER, ENTER_ACCOUNT, CANCEL_ENTRY),

    // 设备计划
    /*DevicePlanObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, START_STAGE_PROPELLOR, LOCK, UNLOCK,
            CLONE, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER, CANCEL_DEVICE_PLAN_PUBLISH, DEVICE_PLAN_PUBLISH),
    // 设备计划明细
    DevicePlanDetailObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, START_STAGE_PROPELLOR, LOCK, UNLOCK,
            CLONE, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER, DEVICE_PLAN_DETAIL_CREATE_CASES),
    // 维保计划
    PreventiveMaintenanceObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, START_STAGE_PROPELLOR, LOCK, UNLOCK,
            CLONE, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER, CANCEL_PREVENTIVE_MAINTENANCE_PUBLISH, PREVENTIVE_MAINTENANCE_PUBLISH),
    // 维保计划明细
    PreventiveMaintenanceDetailObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, START_STAGE_PROPELLOR, LOCK, UNLOCK,
            CLONE, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER, PREVENTIVE_MAINTENANCE_CREATE_CASES),
    // 费用结算单
    FeeSettlementBillObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, START_STAGE_PROPELLOR, LOCK, UNLOCK,
            CLONE, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER, COMPLETE_FEE_SETTLEMENT_BILL),
    // 领料单
    ReceiveMaterialBillObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, START_STAGE_PROPELLOR, LOCK, UNLOCK,
            CLONE, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER, REJECT_RECEIVE_MATERIAL_APPLY, CONFIRM_RECEIVE_MATERIAL_APPLY, SUBMIT_RECEIVE_MATERIAL_APPLY),
    // 领料单
    RefundMaterialBillObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, START_STAGE_PROPELLOR, LOCK, UNLOCK,
            CLONE, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER, REJECT_REFUND_MATERIAL_APPLY, CONFIRM_REFUND_MATERIAL_APPLY, SUBMIT_REFUND_MATERIAL_APPLY),
    // 服务知识记录
    ServiceKnowledgeObj(UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, START_STAGE_PROPELLOR, LOCK, UNLOCK,
            CLONE, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER, PREVIEW_KNOWLEDGE, UPDATE_KNOWLEDGE_CATEGORY),*/

    ElectronicSignerObj(SIGN_FILE),

    TransactionStatementObj(INITIATE_RECONCILIATION, CONFIRM_RECONCILIATION, UPDATE, SALE_RECORD, DIAL, CHANGE_OWNER, START_BPM, INVALID, START_STAGE_PROPELLOR, LOCK, UNLOCK, CLONE, CHANGE_PARTNER_OWNER, SEND_MAIL, DISCUSS, REMIND, SCHEDULE, PRINT),


    //课程开发演示用
    ZLCarObj(UPDATE, TEST_CAR_SELL, DIAL, CHANGE_OWNER, START_BPM, INVALID, START_STAGE_PROPELLOR, LOCK, UNLOCK, CLONE, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER),

    /*
    WechatFriendsRecordObj(FRIEND_INHERIT, UPDATE, CHANGE_OWNER, INVALID, LOCK, CLONE, SEND_MAIL, DISCUSS, REMIND, SCHEDULE, PRINT),
    WechatGroupObj(GROUP_INHERIT, UPDATE, CHANGE_OWNER, INVALID, LOCK, CLONE, SEND_MAIL, DISCUSS, REMIND, SCHEDULE, PRINT),
    */;

    /**
     * 枚举常量列表
     */
    static List<String> nameList;

    static {
        nameList = new ArrayList<>();
        LayoutButtons[] arr = LayoutButtons.values();
        for (LayoutButtons a : arr) {
            nameList.add(a.name());
        }
    }

    private List<ObjectAction> actions;

    LayoutButtons(ObjectAction... actions) {
        this.actions = Arrays.asList(actions);
    }

    public static LayoutButtons getInstance(ObjectDescribeExt objectDescribeExt) {
        if (nameList.contains(objectDescribeExt.getApiName())) {
            return LayoutButtons.valueOf(objectDescribeExt.getApiName());
        }
        return UserDefinedObj;
    }

    public List<IButton> getActionButtons() {
        //自定义对象，用"actionCode_button_default"方式命名预定义按钮
        return actions.stream().map(this::createButtons).collect(Collectors.toList());
    }

    /**
     * 生成自定义对象的预定义按钮
     *
     * @param action 动作名称
     * @return 按钮
     */
    private IButton createButtons(ObjectAction action) {
        return action.createButton();
    }

    public List<IButton> getTailButtons() {
        //销售订单产品和退货单产品没有【打印】按钮
        if (Utils.RETURN_GOODS_INVOICE_Product_API_NAME.equals(name()) ||
                Utils.SALES_ORDER_PRODUCT_API_NAME.equals(name())) {
            return Supplements.actions.stream().map(this::createButtons).collect(Collectors.toList());
        }
        return CommonTail.actions.stream().map(this::createButtons).collect(Collectors.toList());
    }
}
