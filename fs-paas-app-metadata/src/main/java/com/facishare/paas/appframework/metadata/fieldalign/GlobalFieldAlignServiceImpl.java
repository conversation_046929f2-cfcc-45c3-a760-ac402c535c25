package com.facishare.paas.appframework.metadata.fieldalign;

import com.facishare.paas.appframework.common.service.MessagePollingService;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.LayoutStructure;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

/**
 * Created by zhaooju on 2022/5/13
 */
@Service("fieldAlignService")
public class GlobalFieldAlignServiceImpl implements GlobalFieldAlignService {

    public static final String GLOBAL_LAYOUT_CONF_KEY = "pass_app_global_layout_conf";

    @Autowired
    private ConfigService configService;

    @Autowired
    private MessagePollingService messagePollingService;

    @Override
    public GlobalFieldAlign getGlobalFieldAlign(String tenantId) {
        String jsonStr = configService.findTenantConfig(User.systemUser(tenantId), GLOBAL_LAYOUT_CONF_KEY);
        GlobalFieldAlign fieldAlign = JacksonUtils.fromJson(jsonStr, GlobalFieldAlign.class);
        if (Objects.isNull(fieldAlign)) {
            return GlobalFieldAlign.of("", "");
        }
        return fieldAlign;
    }

    @Override
    public void updateGlobalFieldAlign(String tenantId, GlobalFieldAlign fieldAlign) {
        String jsonStr = JacksonUtils.toJson(fieldAlign);
        configService.upsertTenantConfig(User.systemUser(tenantId), GLOBAL_LAYOUT_CONF_KEY, jsonStr, ConfigValueType.JSON);
        GlobalFieldAlign globalFieldAlign = getGlobalFieldAlign(tenantId);
        if (!Objects.equals(globalFieldAlign, fieldAlign)) {
            messagePollingService.notifyDescribeLayoutChange(tenantId, false);
        }
    }

    @Override
    public GlobalFieldAlign.FieldAlign getFieldAlignByLayout(String tenantId, ILayout layout) {
        return Optional.ofNullable(layout)
                .map(LayoutExt::of)
                .map(this::getFieldAlignByLayout)
                .orElseGet(() -> getGlobalFieldAlign(tenantId).getFieldAlign());
    }

    private GlobalFieldAlign.FieldAlign getFieldAlignByLayout(LayoutExt layoutExt) {
        String fieldAlign = LayoutStructure.getFieldAlign(layoutExt);
        return GlobalFieldAlign.FieldAlign.of(fieldAlign);
    }

    @Override
    public void handleLayoutWithGlobalFieldAlign(String tenantId, IObjectDescribe describe, ILayout layout) {
        if (!isGrayFieldAlign(tenantId, describe.getApiName())) {
            return;
        }
        LayoutExt layoutExt = LayoutExt.of(layout);

        GlobalFieldAlign globalFieldAlign = getGlobalFieldAlign(tenantId);
        GlobalFieldAlign.FieldAlign fieldAlign = Optional.ofNullable(getFieldAlignByLayout(layoutExt))
                .orElseGet(globalFieldAlign::getFieldAlign);
        layoutExt.set(LayoutStructure.FIELD_ALIGN, fieldAlign.getType());
        layoutExt.set(LayoutStructure.DETAIL_FORM_LAYOUT, globalFieldAlign.getDetailFormLayout());
    }

    @Override
    public boolean isGrayFieldAlign(String tenantId, String describeApiName) {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FIELD_ALIGN, tenantId);
    }
}
