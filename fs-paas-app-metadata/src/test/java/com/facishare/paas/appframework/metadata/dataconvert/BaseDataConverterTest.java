package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import org.junit.jupiter.api.BeforeEach;

import java.util.Arrays;
import java.util.List;

/**
 * GenerateByAI
 * 数据转换器测试基类，提供通用的测试数据构造方法和Mock对象
 */
public abstract class BaseDataConverterTest {

  protected User user;
  protected DataConvertContext context;

  @BeforeEach
  void setUp() {
    user = User.systemUser("74255");
    context = DataConvertContext.of(user);
  }

  /**
   * 创建测试用的字段描述
   */
  protected IFieldDescribe createFieldDescribe(String apiName, String fieldType) {
    String jsonTemplate = "{\n" +
        "  \"api_name\": \"%s\",\n" +
        "  \"type\": \"%s\",\n" +
        "  \"label\": \"测试字段\",\n" +
        "  \"is_required\": false,\n" +
        "  \"is_active\": true\n" +
        "}";
    return FieldDescribeFactory.newInstance(String.format(jsonTemplate, apiName, fieldType));
  }

  /**
   * 创建测试用的对象描述
   */
  protected IObjectDescribe createObjectDescribe(String apiName) {
    ObjectDescribe describe = new ObjectDescribe();
    describe.setApiName(apiName);
    describe.setTenantId("74255");
    return describe;
  }

  /**
   * 创建测试用的对象数据
   */
  protected IObjectData createObjectData(String fieldApiName, Object value) {
    ObjectData data = new ObjectData();
    data.set(fieldApiName, value);
    data.set("object_describe_api_name", "test_object__c");
    return data;
  }

  /**
   * 创建包含多个字段的对象数据
   */
  protected IObjectData createObjectDataWithFields(String... fieldValuePairs) {
    ObjectData data = new ObjectData();
    for (int i = 0; i < fieldValuePairs.length; i += 2) {
      if (i + 1 < fieldValuePairs.length) {
        data.set(fieldValuePairs[i], fieldValuePairs[i + 1]);
      }
    }
    data.set("object_describe_api_name", "test_object__c");
    return data;
  }

  /**
   * 创建对象数据列表
   */
  protected List<IObjectData> createObjectDataList(IObjectData... dataArray) {
    return Arrays.asList(dataArray);
  }

  /**
   * 创建员工字段描述
   */
  protected IFieldDescribe createEmployeeFieldDescribe(String apiName) {
    return createFieldDescribe(apiName, IFieldType.EMPLOYEE);
  }

  /**
   * 创建多选员工字段描述
   */
  protected IFieldDescribe createEmployeeManyFieldDescribe(String apiName) {
    return createFieldDescribe(apiName, IFieldType.EMPLOYEE_MANY);
  }

  /**
   * 创建文本字段描述
   */
  protected IFieldDescribe createTextFieldDescribe(String apiName) {
    return createFieldDescribe(apiName, IFieldType.TEXT);
  }

  /**
   * 创建数字字段描述
   */
  protected IFieldDescribe createNumberFieldDescribe(String apiName) {
    return createFieldDescribe(apiName, IFieldType.NUMBER);
  }

  /**
   * 创建日期字段描述
   */
  protected IFieldDescribe createDateFieldDescribe(String apiName) {
    return createFieldDescribe(apiName, IFieldType.DATE);
  }

  /**
   * 创建日期时间字段描述
   */
  protected IFieldDescribe createDateTimeFieldDescribe(String apiName) {
    return createFieldDescribe(apiName, IFieldType.DATE_TIME);
  }

  /**
   * 创建单选字段描述
   */
  protected IFieldDescribe createSelectOneFieldDescribe(String apiName) {
    return createFieldDescribe(apiName, IFieldType.SELECT_ONE);
  }

  /**
   * 创建多选字段描述
   */
  protected IFieldDescribe createSelectManyFieldDescribe(String apiName) {
    return createFieldDescribe(apiName, IFieldType.SELECT_MANY);
  }

  /**
   * 创建布尔字段描述
   */
  protected IFieldDescribe createBooleanFieldDescribe(String apiName) {
    return createFieldDescribe(apiName, IFieldType.TRUE_OR_FALSE);
  }
}
