package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.metadata.layout.I18nInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface FindCustomFieldDescribe {
    @Data
    class Arg {
        @JSONField(name = "M1")
        private String describeAPIName;

        @JSONField(name = "M2")
        private String fieldApiName;

        private boolean includeDescribeExtra;

        private boolean includeFieldsExtra;

        private boolean noNeedReplaceI18n = false;
    }

    @Data
    class Result {
        @JSONField(name = "M1")
        private List layout_list;

        @J<PERSON><PERSON>ield(name = "M2")
        private Map field;

        private ObjectDescribeDocument describeExtra;

        private List<Map<String, Object>> fieldsExtra;

        @JsonProperty("i18nInfoList")
        private List<I18nInfo> i18nInfoList;
    }
}
