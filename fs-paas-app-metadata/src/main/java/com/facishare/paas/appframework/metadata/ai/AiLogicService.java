package com.facishare.paas.appframework.metadata.ai;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.dto.ChatComplete;


/**
 * <AUTHOR> create by liy on 2024/5/14
 */
public interface AiLogicService {
    /**
     * ai识别
     */
    ChatComplete.Result chatComplete(String tenantId, String userId, String agentType, String content);

    ChatComplete.TranslateResult chatTranslate(String tenantId, String userId, ChatComplete.AITransDTO arg) throws ValidateException;
}
