package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.common.exception.CrmDefObjCheckedException;
import com.facishare.crm.valueobject.SessionContext;

/**
 * Created by linqiuying on 17/6/9.
 */
public class PhoneNumberFieldDataConverter extends AbstractFieldDataConverter {
  @Override
  public String convertFieldData(SessionContext sessionContext) throws CrmDefObjCheckedException {
    return getObjectData().get(getFieldDescribe().getApiName(),String.class);
  }
}
