package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.metadata.cache.RedisDao;
import com.facishare.paas.appframework.metadata.dto.MasterApprovalResult;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * Created by zhouwr on 2019/11/11
 */
@Slf4j
@Service("objectDataFlowService")
public class ObjectDataFlowServiceImpl implements ObjectDataFlowService {

    private static final int MASTER_APPROVAL_RESULT_EXPIRE_TIME_MILLI_SECONDS = 60 * 1000;

    @Autowired
    private RedisDao redisDao;

    @Override
    public void saveMasterDataApprovalResult(IObjectData detailData, MasterApprovalResult masterApprovalResult) {
        if (Objects.isNull(masterApprovalResult)) {
            return;
        }
        String key = buildMasterApprovalKey(detailData.getTenantId(), detailData.getDescribeApiName(), detailData.getId());
        try {
            redisDao.set(key, JSON.toJSONString(masterApprovalResult), MASTER_APPROVAL_RESULT_EXPIRE_TIME_MILLI_SECONDS);
        } catch (Exception e) {
            log.error("saveMasterDataApprovalResult error,key:{},result:{}", key, masterApprovalResult, e);
        }
    }

    @Override
    public MasterApprovalResult getMasterDataApprovalResult(String tenantId, String objectApiName, String dataId) {
        String key = buildMasterApprovalKey(tenantId, objectApiName, dataId);
        try {
            String result = redisDao.getStrCache(key);
            if (Strings.isNullOrEmpty(result)) {
                return null;
            }
            return JSON.parseObject(result, MasterApprovalResult.class);
        } catch (Exception e) {
            log.error("getMasterDataApprovalResult error,key:{}", key, e);
            return null;
        }
    }

    private String buildMasterApprovalKey(String tenantId, String objectApiName, String dataId) {
        return tenantId + "|" + objectApiName + "|" + dataId + "-mar";
    }
}
