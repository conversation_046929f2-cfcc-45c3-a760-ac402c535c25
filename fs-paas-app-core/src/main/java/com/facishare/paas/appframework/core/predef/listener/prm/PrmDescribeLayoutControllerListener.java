package com.facishare.paas.appframework.core.predef.listener.prm;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ControllerListener;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.prm.PrmLayoutRender;
import com.facishare.paas.appframework.prm.enums.PrmPageType;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import org.springframework.stereotype.Component;

/**
 * prm加载describe and layout 监听类
 * <AUTHOR>
 */
@Component
public class PrmDescribeLayoutControllerListener implements ControllerListener<StandardDescribeLayoutController.Arg,StandardDescribeLayoutController.Result> {

    @Override
    public void before(ControllerContext context, StandardDescribeLayoutController.Arg arg) {

    }

    @Override
    public void after(ControllerContext context, StandardDescribeLayoutController.Arg arg, StandardDescribeLayoutController.Result result) {
        PrmLayoutRender.builder()
                .pageType(PrmPageType.AddOrEdit)
                .describe(result.getObjectDescribe().toObjectDescribe())
                .layoutExt(LayoutExt.of(result.getLayout()))
                .build()
                .render();

        if(CollectionUtils.notEmpty(result.getDetailObjectList()) ) {
            //过滤不支持的从对象
            result.getDetailObjectList().removeIf(d -> PrmConstant.unSupportDetailApiNames
                    .contains(d.getObjectDescribe().get("api_name").toString()));

            result.getDetailObjectList().forEach(detailObject ->
                    detailObject.getLayoutList().forEach(layout -> {
                        if (layout.getDetail_layout() != null) {
                            PrmLayoutRender.builder()
                                    .pageType(PrmPageType.AddOrEdit)
                                    .describe(ObjectDescribeDocument.of(detailObject.getObjectDescribe()).toObjectDescribe())
                                    .layoutExt(LayoutExt.of(layout.getDetail_layout()))
                                    .build()
                                    .render();
                        }
                    })
            );
        }
    }
}
