package com.facishare.paas.appframework.common.mq;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.fxiaoke.rocketmq.acl.AutoConfAclClientRPCHook;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.UUID;

/**
 * RocketMQ 消息处理器
 * <p>
 * Created by liyiguang on 2018/1/23.
 */
@Slf4j
@Deprecated
public class RocketMQMessageProcessor {

    private RocketMQMessageListener rocketMQMessageListener;
    private volatile boolean shutdown = true;
    private DefaultMQPushConsumer defaultMQPushConsumer;
    private String configName;

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public void setRocketMQMessageListener(RocketMQMessageListener rocketMQMessageListener) {
        this.rocketMQMessageListener = rocketMQMessageListener;
    }

    @PostConstruct
    public void init() {
        ConfigFactory.getInstance().getConfig(configName, config -> {
            try {
                reload(config);
            } catch (Exception e) {
                throw new RocketMQException(SystemErrorCode.MQ_INIT_ERROR, e);
            }
        });
    }

    private void reload(IConfig conf) {
        String content = new String(conf.getContent());
        if (Strings.isNullOrEmpty(content)) {
            log.error("{} config content is empty", configName);
            throw new RocketMQException("config error! key=" + configName);
        }
        Config config = JSON.parseObject(content, Config.class);
        log.info("reload config:{}", config);

        if (defaultMQPushConsumer == null) {
            createConsumer(config);
        } else {
            shutdown();
            createConsumer(config);
        }
    }

    private void createConsumer(Config config) {
        try {
            defaultMQPushConsumer = new DefaultMQPushConsumer(config.getConsumeGroup(), new AutoConfAclClientRPCHook("rocketmq-acl"));
            defaultMQPushConsumer.setNamesrvAddr(config.getNameServer());
            defaultMQPushConsumer.subscribe(config.getTopic(), config.getTags());
            defaultMQPushConsumer.setInstanceName(UUID.randomUUID().toString());
            defaultMQPushConsumer.setConsumeFromWhere(config.getConsumeFromWhere());
            defaultMQPushConsumer.setConsumeMessageBatchMaxSize(config.getConsumeBatchSize());

            if (config.isOrdered()) {
                registerOrderedMessageListener();
            } else {
                registerConcurrentMessageListener();
            }
            defaultMQPushConsumer.start();
            shutdown = false;
        } catch (Exception e) {
            log.error("create RocketMQ defaultMQPushConsumer failed!", e);
        }

    }

    private void registerOrderedMessageListener() {
        defaultMQPushConsumer.registerMessageListener((MessageListenerOrderly) (messages, context) -> {

            if (shutdown) {
                log.warn("consumer is shutdown,suspend queue a moment!");
                return ConsumeOrderlyStatus.SUSPEND_CURRENT_QUEUE_A_MOMENT;
            }

            try {
                rocketMQMessageListener.consumeMessage(messages);
                return ConsumeOrderlyStatus.SUCCESS;
            } catch (Exception e) {
                log.error("consume message failed!", e);
            }
            return ConsumeOrderlyStatus.SUSPEND_CURRENT_QUEUE_A_MOMENT;
        });
    }

    private void registerConcurrentMessageListener() {
        defaultMQPushConsumer.registerMessageListener((MessageListenerConcurrently) (messages, context) -> {
            if (shutdown) {
                log.warn("consumer is shutdown,reConsume later!");
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }

            try {
                rocketMQMessageListener.consumeMessage(messages);
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("consume message failed!", e);
            }
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        });
    }


    private void shutdown() {
        shutdown = true;
        if (defaultMQPushConsumer != null) {
            try {
                this.defaultMQPushConsumer.shutdown();
                this.defaultMQPushConsumer = null;
            } catch (Exception e) {
                log.error("shutdown RocketMQ consumer failed!", e);
            }
        }
    }

    @Data
    @Slf4j
    public static class Config {
        String nameServer;
        String consumeGroup;    //这个值决定了消费集群
        String topic;           //一个Processor 处理一个Topic
        List<String> tags;      //消费那些Tag
        int consumeBatchSize;   //每次消费的消息的数量
        String consumeFromWhere;
        boolean ordered = false;

        public int getConsumeBatchSize() {
            if (consumeBatchSize > 0) {
                return consumeBatchSize;
            }
            return 1;
        }

        public ConsumeFromWhere getConsumeFromWhere() {
            try {
                ConsumeFromWhere from = ConsumeFromWhere.valueOf(consumeFromWhere);
                return from;
            } catch (Exception e) {
                log.warn("Unsupported ConsumeFromWhere enum:" + consumeFromWhere);
            }
            return ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET;
        }

        public String getTags() {
            if (CollectionUtils.notEmpty(tags)) {
                return Joiner.on("||").skipNulls().join(tags);
            }
            return "*";
        }
    }


}
