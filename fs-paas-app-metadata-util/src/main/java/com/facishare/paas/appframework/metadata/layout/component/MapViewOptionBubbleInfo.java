package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.metadata.api.ISelectOption;

import java.util.Map;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/07/30
 */
public class MapViewOptionBubbleInfo extends AbstractComponentInfoDocument implements IMapViewOptionBubbleInfo {
    private static final long serialVersionUID = 3194801278411039501L;

    public MapViewOptionBubbleInfo() {
    }

    public MapViewOptionBubbleInfo(Map map) {
        super(map);
    }

    public static IMapViewOptionBubbleInfo buildBySelectOption(ISelectOption option) {
        IMapViewOptionBubbleInfo optionBubbleInfo = new MapViewOptionBubbleInfo();
        optionBubbleInfo.setColor(DEFAULT_COLOR);
        optionBubbleInfo.setLabel(option.getLabel());
        optionBubbleInfo.setValue(option.getValue());
        return optionBubbleInfo;
    }

    @Override
    public String getColor() {
        return get(COLOR, String.class);
    }

    @Override
    public void setColor(String color) {
        set(COLOR, color);
    }

    @Override
    public String getLabel() {
        return get(LABEL, String.class);
    }

    @Override
    public void setLabel(String label) {
        set(LABEL, label);
    }

    @Override
    public String getValue() {
        return get(VALUE, String.class);
    }

    @Override
    public void setValue(String value) {
        set(VALUE, value);
    }
}
