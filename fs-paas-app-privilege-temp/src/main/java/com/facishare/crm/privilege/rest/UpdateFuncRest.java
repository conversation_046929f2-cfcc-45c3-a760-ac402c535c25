package com.facishare.crm.privilege.rest;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.privilege.service.FuncService;
import com.facishare.crm.privilege.util.GetRoleUtils;
import com.facishare.rest.core.util.JsonUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Response;
import java.util.List;
import java.util.Map;

/**
 * Created by luxin on 2017/5/20.
 */
@Component
@Slf4j
@Path("/updatefuncrest")
public class UpdateFuncRest {
    @Autowired
    FuncService funcService;

    private Map<String, String> funcCode2DescMap;
    private Map<String, String> userDefinedFuncCode2DescMap;
    private int funcType;

    @PostConstruct
    private void init() {
        ConfigFactory.getConfig("fs-crm-add-function-code", config -> {
            String addFuncCode = config.get("addFuncCode");
            String addUserDefinedFuncCode = config.get("addUserDefinedFuncCode");
            funcType = config.getInt("funcType");
            log.info("reload addFuncCode:{},addUserDefinedFuncCode:{},funcType{}", addFuncCode, addUserDefinedFuncCode, funcType);

            this.funcCode2DescMap = (Map<String, String>) JSON.parse(addFuncCode);
            this.userDefinedFuncCode2DescMap = (Map<String, String>) JSON.parse(addUserDefinedFuncCode);
        });
    }

    @POST
    @Path("/addfunc")
    @Produces({"application/json"})
    public Response addFunc(String body) {
        if (StringUtils.isBlank(body)) return Response.ok().entity("body is blank.").build();

        List<String> tenantIds = Lists.newArrayList(body.trim().split(","));

        for (String tenantId : tenantIds) {
            try {
                funcService.addFuncCode(tenantId, funcCode2DescMap, funcType);
            } catch (Exception e) {
                log.error("addFuncCode error. tenantId {}", tenantId, e);
            }
        }
        return Response.ok().entity("ok").build();
    }


    @POST
    @Path("/addfuncbyfuncinfo")
    @Produces({"application/json"})
    public Response addFuncByFuncInfo(String body) {
        if (StringUtils.isBlank(body)) return Response.ok().entity("body is blank.").build();
        Map<String, Object> bodyInfo = JsonUtil.fromJson(body, Map.class);

        List<String> tenantIds = (List<String>) bodyInfo.get("tenantIds");

        Map<String, String> funcCode2DescMap = (Map<String, String>) bodyInfo.get("funcCode2DescMap");

        for (String tenantId : tenantIds) {
            try {
                funcService.addFuncCode(tenantId, funcCode2DescMap, funcType);
            } catch (Exception e) {
                log.error("addFuncCode error. tenantId {}", tenantId, e);
            }
        }
        return Response.ok().entity("ok").build();
    }


    @POST
    @Path("/adduserdefinedfunc")
    @Produces({"application/json"})
    public Response addUserDefinedFunc(String body) {
        if (StringUtils.isBlank(body)) return Response.ok().entity("body is blank.").build();
        List<String> tenantIds = Lists.newArrayList(body.trim().split(","));

        for (String tenantId : tenantIds) {
            try {
                funcService.addUserDefinedFuncCode(tenantId, userDefinedFuncCode2DescMap);
            } catch (Exception e) {
                log.error("addUserDefinedFuncCode  error. tenantId {}", tenantId, e);
            }
        }
        return Response.ok().entity("ok").build();
    }


    @POST
    @Path("/addassociatedrelationfuncCode")
    @Produces({"application/json"})
    public Response addAssociatedRelationFuncCode(String body) {
        if (StringUtils.isBlank(body)) return Response.ok().entity("body is blank.").build();
        List<String> tenantIds = Lists.newArrayList(body.trim().split(","));

        long startTime = System.currentTimeMillis();
        for (String tenantId : tenantIds) {
            try {
                funcService.addAssociatedRelationFuncCode(tenantId, funcCode2DescMap, GetRoleUtils.USER_DEFINED_FUNC_TYPE);
            } catch (Exception e) {
                log.error("addAssociatedRelationFuncCode error. tenantId {}", tenantId, e);
            }
        }

        log.info("addAssociatedRelationFuncCode use time {}", System.currentTimeMillis() - startTime);
        return Response.ok().entity("ok").build();
    }


    @POST
    @Path("/addfuncaccess")
    @Produces({"application/json"})
    public Response addFuncAccess(String body) {
        if (StringUtils.isBlank(body)) return Response.ok().entity("body is blank.").build();

        Map<String, Object> bodyInfo = JsonUtil.fromJson(body, Map.class);

        List<String> tenantIds = (List<String>) bodyInfo.get("tenantIds");
        List<String> funcCodes = (List<String>) bodyInfo.get("funcCodes");
        String roleCode = (String) bodyInfo.get("roleCode");

        List<String> failedTenantIds = Lists.newArrayList();

        for (String tenantId : tenantIds) {
            try {
                funcService.addFuncAccess(tenantId, funcCodes, roleCode);
            } catch (Exception e) {
                log.error("addFuncAccess error. tenantId {}", tenantId, e);
                failedTenantIds.add(tenantId);
            }
        }

        if (failedTenantIds.isEmpty()) {
            return Response.ok().entity("ok").build();
        } else {
            return Response.ok().entity("failedTenantIds: " + failedTenantIds).build();
        }

    }


}