package com.facishare.paas.appframework.core.predef;

import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObject;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;

/**
 * create by zhangyixuan on 2019/07/31
 */
public enum StageTaskPreDefineObject implements PreDefineObject {

    StageTaskObj("StageTaskObj");

    private String apiName;
    StageTaskPreDefineObject(String apiName) {
        this.apiName = apiName;
    }

    public static void init() {
        for (StageTaskPreDefineObject object : StageTaskPreDefineObject.values()) {
            PreDefineObjectRegistry.register(object);
        }
    }

    private static final String PACKAGE_NAME = StageTaskPreDefineObject.class.getPackage().getName();

    @Override
    public String getApiName() {
        return apiName;
    }

    @Override
    public String getPackageName() {
        return PACKAGE_NAME;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String actionCode) {
        String className = PACKAGE_NAME + ".action." + this.apiName + actionCode + "Action";
        return new ActionClassInfo(className);
    }

    @Override
    public ControllerClassInfo getControllerClassInfo(String methodName) {
        String className = PACKAGE_NAME + ".controller." + this.apiName + methodName + "Controller";
        return new ControllerClassInfo(className);
    }
}
