package com.facishare.paas.appframework.metadata.domain;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginInstance;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2022/9/21.
 */
@Builder
public class DomainPluginDefinitionFilter {

    private static final String UDOBJ = "udobj";

    public static final String STATUS_TYPE_RUN_TIME = "run_time";

    //=======输入参数======
    private List<DomainPluginDefinition> pluginDefinitionList;
    private List<DomainPluginInstance> pluginInstanceList;
    private String tenantId;
    private IObjectDescribe objectDescribe;
    private LicenseService licenseService;
    private ConfigService configService;

    private String status;

    //=======输出参数======
    private List<DomainPluginDefinition> validPluginDefinitionList;

    public FilterResult doFilter() {
        prepare();
        filterByPluginInstance();
        filterByObjectApiName();//作用对象
        filterByLicenseVersion();// 主版本
        filterByLicenseModule();// 模块
        filterByBizConfig();//业务开关
        filterByManagementConfig();// 是否支持从对象
        return buildResult();
    }

    private void prepare() {
        validPluginDefinitionList = Lists.newArrayList(pluginDefinitionList);
    }

    private void filterByPluginInstance() {
        if (CollectionUtils.empty(validPluginDefinitionList)) {
            return;
        }
        if (CollectionUtils.empty(pluginInstanceList)) {
            return;
        }
        //不需要过滤字段插件
        Set<String> pluginApiNames = pluginInstanceList.stream().map(x -> x.getPluginApiName())
                .filter(x -> DomainPluginType.Field != DomainPluginDefinitionHolder.getPluginType(x))
                .collect(Collectors.toSet());
        validPluginDefinitionList = validPluginDefinitionList.stream()
                .filter(x -> !pluginApiNames.contains(x.getApiName()))
                .collect(Collectors.toList());
    }

    private void filterByObjectApiName() {
        if (CollectionUtils.empty(validPluginDefinitionList)) {
            return;
        }
        String formatObjectApiName = formatObjectApiName(objectDescribe.getApiName());
        validPluginDefinitionList = validPluginDefinitionList.stream()
                .filter(x -> filterObjectApiNameAndPrivilegeConfig(formatObjectApiName, x))
                .collect(Collectors.toList());
    }

    private boolean filterObjectApiNameAndPrivilegeConfig(String objectApiName, DomainPluginDefinition pluginDefinition) {
        DomainPluginDefinition.PrivilegeConfig privilegeConfig = pluginDefinition.getPrivilegeConfig();
        if (Objects.isNull(privilegeConfig) || CollectionUtils.empty(privilegeConfig.getSupportObjectApiNames())) {
            return true;
        }
        if (Objects.equals(STATUS_TYPE_RUN_TIME, status) && BooleanUtils.isNotTrue(privilegeConfig.getCheckOnRunTime())) {
            return true;
        }
        return privilegeConfig.getSupportObjectApiNames().contains(objectApiName);
    }

    private void filterByLicenseVersion() {
        if (CollectionUtils.empty(validPluginDefinitionList)) {
            return;
        }
        List<DomainPluginDefinition> definitionsToCheck = validPluginDefinitionList.stream()
                .filter(this::filterByPrivilegeConfig)
                .filter(x -> CollectionUtils.notEmpty(x.getPrivilegeConfig().getLicenseVersions()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(definitionsToCheck)) {
            return;
        }
        List<String> versionsAndPackages = licenseService.getVersionAndPackages(tenantId);
        definitionsToCheck = definitionsToCheck.stream()
                .filter(x -> !org.apache.commons.collections4.CollectionUtils.containsAny(versionsAndPackages,
                        x.getPrivilegeConfig().getLicenseVersions()))
                .collect(Collectors.toList());
        validPluginDefinitionList.removeAll(definitionsToCheck);
    }

    private void filterByLicenseModule() {
        if (CollectionUtils.empty(validPluginDefinitionList)) {
            return;
        }
        List<DomainPluginDefinition> definitionsToCheck = validPluginDefinitionList.stream()
                .filter(this::filterByPrivilegeConfig)
                .filter(x -> CollectionUtils.notEmpty(x.getPrivilegeConfig().getLicenseModules()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(definitionsToCheck)) {
            return;
        }
        Set<String> modules = definitionsToCheck.stream().flatMap(x -> x.getPrivilegeConfig().getLicenseModules().stream())
                .collect(Collectors.toSet());
        Map<String, Boolean> moduleStatus = licenseService.existModule(tenantId, modules);
        definitionsToCheck = definitionsToCheck.stream()
                .filter(x -> x.getPrivilegeConfig().getLicenseModules().stream()
                        .allMatch(y -> !BooleanUtils.isTrue(moduleStatus.get(y))))
                .collect(Collectors.toList());
        validPluginDefinitionList.removeAll(definitionsToCheck);
    }

    private void filterByBizConfig() {
        if (CollectionUtils.empty(validPluginDefinitionList)) {
            return;
        }
        List<DomainPluginDefinition> definitionsToCheck = validPluginDefinitionList.stream()
                .filter(this::filterByPrivilegeConfig)
                .filter(x -> CollectionUtils.notEmpty(x.getPrivilegeConfig().getBizConfig()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(definitionsToCheck)) {
            return;
        }
        List<String> configKeys = definitionsToCheck.stream()
                .flatMap(x -> x.getPrivilegeConfig().getBizConfig().stream().map(y -> y.getKey()))
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> configValueMap = configService.queryTenantConfigs(User.systemUser(tenantId), configKeys);
        definitionsToCheck = definitionsToCheck.stream()
                .filter(x -> x.getPrivilegeConfig().getBizConfig().stream()
                        .allMatch(y -> !Objects.equals(y.getValue(), configValueMap.get(y.getKey()))))
                .collect(Collectors.toList());
        validPluginDefinitionList.removeAll(definitionsToCheck);
    }

    private boolean filterByPrivilegeConfig(DomainPluginDefinition pluginDefinition) {
        DomainPluginDefinition.PrivilegeConfig privilegeConfig = pluginDefinition.getPrivilegeConfig();
        if (Objects.isNull(privilegeConfig)) {
            return false;
        }
        if (!Objects.equals(STATUS_TYPE_RUN_TIME, status)) {
            return true;
        }
        return BooleanUtils.isTrue(privilegeConfig.getCheckOnRunTime());
    }

    private void filterByManagementConfig() {
        if (CollectionUtils.empty(validPluginDefinitionList) || Objects.equals(STATUS_TYPE_RUN_TIME, status)) {
            return;
        }
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            validPluginDefinitionList.removeIf(x -> !x.supportDetailObj());
        }
    }

    private static String formatObjectApiName(String objectApiName) {
        if (ObjectDescribeExt.isCustomObject(objectApiName)) {
            return UDOBJ;
        }
        return objectApiName;
    }

    private FilterResult buildResult() {
        return FilterResult.builder().validPluginDefinitionList(validPluginDefinitionList).build();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class FilterResult {
        private List<DomainPluginDefinition> validPluginDefinitionList;
    }
}
