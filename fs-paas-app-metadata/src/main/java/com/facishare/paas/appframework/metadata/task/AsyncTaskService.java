package com.facishare.paas.appframework.metadata.task;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.repository.model.MtAsyncTaskMonitor;
import com.facishare.paas.appframework.metadata.search.SearchQuery;

import java.util.List;
import java.util.Map;

public interface AsyncTaskService {

    void create(User user, MtAsyncTaskMonitor mtAsyncTaskMonitor);

    void batchCreate(User user, List<MtAsyncTaskMonitor> mtAsyncTaskMonitors);

    void update(User user, MtAsyncTaskMonitor mtAsyncTaskMonitor, List<String> updateFieldList);

    void batchUpdate(User user, List<MtAsyncTaskMonitor> mtAsyncTaskMonitors, List<String> updateFieldList);

    void updateWithMap(User user, String taskId, Map<String, Object> fieldMap);

    MtAsyncTaskMonitor find(User user, String taskId);

    List<MtAsyncTaskMonitor> findBySearchQuery(User user, SearchQuery searchQuery);

}
