package com.facishare.crm.privilege.rest.proxy.model;


import com.facishare.crm.privilege.util.Constant;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * Created by luxin on 2017/5/4.
 */
@Data
@NoArgsConstructor
public class BaseContext {

  private String tenantId;
  private String appId = Constant.APP_ID;
  private String userId = Constant.USER_ID;

  private Map<String, Object> objectProperties;
  private Map<String, Object> properties;

  public BaseContext(String tenantId, String appId, String userId) {
    this.tenantId = tenantId;
    this.appId = appId;
    this.userId = userId;

  }
  public BaseContext(String tenantId) {
    this.tenantId = tenantId;
  }

  public boolean isValid() {
    return StringUtils.isNotBlank(tenantId) && StringUtils.isNotBlank(appId) && StringUtils.isNotBlank(userId);
  }


}
