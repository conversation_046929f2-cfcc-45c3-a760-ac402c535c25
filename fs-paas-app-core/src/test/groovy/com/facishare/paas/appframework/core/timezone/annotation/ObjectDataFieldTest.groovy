package com.facishare.paas.appframework.core.timezone.annotation

import spock.lang.Specification
import spock.lang.Unroll

import java.lang.reflect.Field

/**
 * GenerateByAI
 * 测试内容描述：ObjectDataField注解的单元测试
 */
class ObjectDataFieldTest extends Specification {

    // 测试用的类
    static class TestClass {
        @ObjectDataField
        private String defaultField

        @ObjectDataField(ObjectDataField.Type.SINGLE)
        private String singleField

        @ObjectDataField(ObjectDataField.Type.LIST)
        private List<String> listField

        @ObjectDataField(ObjectDataField.Type.LIST_MAP)
        private Map<String, List<String>> listMapField

        @ObjectDataField(ObjectDataField.Type.MAP_MAP)
        private Map<String, Map<String, String>> mapMapField

        private String noAnnotationField
    }

    def "test annotation default value"() {
        given: "获取带默认注解的字段"
        Field field = TestClass.getDeclaredField("defaultField")
        ObjectDataField annotation = field.getAnnotation(ObjectDataField.class)

        when: "检查注解的默认值"
        def type = annotation.value()

        then: "应该是SINGLE类型"
        type == ObjectDataField.Type.SINGLE
    }

    @Unroll
    def "test annotation with specific type: #fieldName should have type #expectedType"() {
        given: "获取指定字段"
        Field field = TestClass.getDeclaredField(fieldName)
        ObjectDataField annotation = field.getAnnotation(ObjectDataField.class)

        when: "检查注解类型"
        def actualType = annotation.value()

        then: "应该匹配期望的类型"
        actualType == expectedType

        where:
        fieldName      | expectedType
        "singleField"  | ObjectDataField.Type.SINGLE
        "listField"    | ObjectDataField.Type.LIST
        "listMapField" | ObjectDataField.Type.LIST_MAP
        "mapMapField"  | ObjectDataField.Type.MAP_MAP
    }

    def "test field without annotation"() {
        given: "获取没有注解的字段"
        Field field = TestClass.getDeclaredField("noAnnotationField")

        when: "检查注解"
        ObjectDataField annotation = field.getAnnotation(ObjectDataField.class)

        then: "应该为null"
        annotation == null
    }

    def "test annotation presence"() {
        given: "获取带注解的字段"
        Field field = TestClass.getDeclaredField("defaultField")

        when: "检查注解是否存在"
        boolean hasAnnotation = field.isAnnotationPresent(ObjectDataField.class)

        then: "应该存在注解"
        hasAnnotation == true
    }

    def "test annotation retention policy"() {
        given: "获取注解类"
        Class<ObjectDataField> annotationClass = ObjectDataField.class

        when: "检查注解的保留策略"
        def retention = annotationClass.getAnnotation(java.lang.annotation.Retention.class)

        then: "应该是RUNTIME保留策略"
        retention.value() == java.lang.annotation.RetentionPolicy.RUNTIME
    }

    def "test annotation target"() {
        given: "获取注解类"
        Class<ObjectDataField> annotationClass = ObjectDataField.class

        when: "检查注解的目标"
        def target = annotationClass.getAnnotation(java.lang.annotation.Target.class)

        then: "应该只能用于字段"
        target.value() == [java.lang.annotation.ElementType.FIELD] as java.lang.annotation.ElementType[]
    }

    def "test annotation is documented"() {
        given: "获取注解类"
        Class<ObjectDataField> annotationClass = ObjectDataField.class

        when: "检查是否有Documented注解"
        boolean isDocumented = annotationClass.isAnnotationPresent(java.lang.annotation.Documented.class)

        then: "应该有Documented注解"
        isDocumented == true
    }

    def "test Type enum values"() {
        when: "获取所有枚举值"
        def types = ObjectDataField.Type.values()

        then: "应该包含所有预期的类型"
        types.length == 4
        types.contains(ObjectDataField.Type.SINGLE)
        types.contains(ObjectDataField.Type.LIST)
        types.contains(ObjectDataField.Type.LIST_MAP)
        types.contains(ObjectDataField.Type.MAP_MAP)
    }

    def "test Type enum valueOf"() {
        expect: "valueOf方法应该正确工作"
        ObjectDataField.Type.valueOf("SINGLE") == ObjectDataField.Type.SINGLE
        ObjectDataField.Type.valueOf("LIST") == ObjectDataField.Type.LIST
        ObjectDataField.Type.valueOf("LIST_MAP") == ObjectDataField.Type.LIST_MAP
        ObjectDataField.Type.valueOf("MAP_MAP") == ObjectDataField.Type.MAP_MAP
    }

    def "test Type enum valueOf with invalid value throws exception"() {
        when: "使用无效值调用valueOf"
        ObjectDataField.Type.valueOf("INVALID")

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test Type enum ordinal values"() {
        expect: "枚举的序号应该正确"
        ObjectDataField.Type.SINGLE.ordinal() == 0
        ObjectDataField.Type.LIST.ordinal() == 1
        ObjectDataField.Type.LIST_MAP.ordinal() == 2
        ObjectDataField.Type.MAP_MAP.ordinal() == 3
    }

    def "test Type enum name values"() {
        expect: "枚举的名称应该正确"
        ObjectDataField.Type.SINGLE.name() == "SINGLE"
        ObjectDataField.Type.LIST.name() == "LIST"
        ObjectDataField.Type.LIST_MAP.name() == "LIST_MAP"
        ObjectDataField.Type.MAP_MAP.name() == "MAP_MAP"
    }

    def "test annotation on inherited fields"() {
        given: "创建继承类"

        when: "检查子类字段的注解"
        Field childField = ChildClass.getDeclaredField("childField")
        ObjectDataField childAnnotation = childField.getAnnotation(ObjectDataField.class)

        and: "检查父类字段的注解"
        Field parentField = ParentClass.getDeclaredField("parentField")
        ObjectDataField parentAnnotation = parentField.getAnnotation(ObjectDataField.class)

        then: "两个注解都应该正确"
        childAnnotation.value() == ObjectDataField.Type.SINGLE
        parentAnnotation.value() == ObjectDataField.Type.LIST
    }

    def "test annotation with different access modifiers"() {
        given: "创建包含不同访问修饰符字段的类"

        when: "检查所有字段的注解"
        def publicAnnotation = AccessModifierTest.getDeclaredField("publicField").getAnnotation(ObjectDataField.class)
        def protectedAnnotation = AccessModifierTest.getDeclaredField("protectedField").getAnnotation(ObjectDataField.class)
        def packageAnnotation = AccessModifierTest.getDeclaredField("packageField").getAnnotation(ObjectDataField.class)
        def privateAnnotation = AccessModifierTest.getDeclaredField("privateField").getAnnotation(ObjectDataField.class)

        then: "所有注解都应该正确"
        publicAnnotation.value() == ObjectDataField.Type.SINGLE
        protectedAnnotation.value() == ObjectDataField.Type.LIST
        packageAnnotation.value() == ObjectDataField.Type.LIST_MAP
        privateAnnotation.value() == ObjectDataField.Type.MAP_MAP
    }

    def "test annotation with static and final fields"() {
        given: "创建包含静态和final字段的类"

        when: "检查所有字段的注解"
        def staticAnnotation = StaticFinalTest.getDeclaredField("staticField").getAnnotation(ObjectDataField.class)
        def finalAnnotation = StaticFinalTest.getDeclaredField("finalField").getAnnotation(ObjectDataField.class)
        def staticFinalAnnotation = StaticFinalTest.getDeclaredField("staticFinalField").getAnnotation(ObjectDataField.class)

        then: "所有注解都应该正确"
        staticAnnotation.value() == ObjectDataField.Type.SINGLE
        finalAnnotation.value() == ObjectDataField.Type.LIST
        staticFinalAnnotation.value() == ObjectDataField.Type.LIST_MAP
    }

    def "test annotation reflection performance"() {
        given: "创建测试类实例"
        def testInstance = new TestClass()
        def fields = TestClass.getDeclaredFields()

        when: "多次获取注解"
        def startTime = System.currentTimeMillis()
        1000.times {
            fields.each { field ->
                field.getAnnotation(ObjectDataField.class)
            }
        }
        def endTime = System.currentTimeMillis()

        then: "性能应该可接受（小于1秒）"
        (endTime - startTime) < 1000
    }
}
