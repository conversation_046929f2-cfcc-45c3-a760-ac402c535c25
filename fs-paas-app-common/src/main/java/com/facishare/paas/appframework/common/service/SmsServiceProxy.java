package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.CheckSmsStatus;
import com.facishare.paas.appframework.common.service.dto.SendSms;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.rest.core.codec.IRestCodeC;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * fs-marketing/fs-marketing-web
 * Created by fengjy in 2020/2/10 22:57
 * 接口文档：https://wiki.firstshare.cn/pages/viewpage.action?pageId=110352530
 */
@RestResource(
        value = "PAAS-SMS",
        desc = "深研（营销通）短信服务", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.SmsServiceProxy$Codec")
public interface SmsServiceProxy {
    
    @POST(value = "/apply/checkSmsStatus", desc = "检查租户是否开通了短信服务")
    CheckSmsStatus.Result checkSmsStatus(@Body CheckSmsStatus.Arg arg);
    
    @POST(value = "/send/sendSms", desc = "发送短信")
    SendSms.Result sendSms(@Body SendSms.Arg arg);

    class Codec implements IRestCodeC {

        private static final ObjectMapper objectMapper = new ObjectMapper();

        static {
            objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
            objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        }

        @Override
        public <T> byte[] encodeArg(T obj) {
            try {
                return objectMapper.writeValueAsBytes(obj);
            } catch (IOException e) {
                throw new RuntimeException("encode error", e);
            }
        }

        @Override
        public <T> T decodeResult(int statusCode, Map<String, List<String>> map, byte[] bytes, Class<T> aClass) {

            if (statusCode != 200) {
                throw new RuntimeException("decode error,body:" + (bytes != null ? new String(bytes) : ""));
            }
            try {
                JsonNode root = objectMapper.readTree(bytes);
                int errCode = root.get("errCode").asInt();
                if (errCode != 0) {
                    throw new RestProxyBusinessException(errCode, root.get("errMsg").asText());
                }
                return objectMapper.readValue(root.get("data").toString(), aClass);
            } catch (IOException e) {
                throw new RuntimeException("decode error", e);
            }
        }
    }
}
