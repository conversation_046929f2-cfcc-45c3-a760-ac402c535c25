package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by zhouwr on 2019/12/13
 */
@Slf4j
public class QuickEditConfig {

    private static final Set<String> DEFAULT_FIELD_TYPE_CANNOT_EDIT = Sets.newHashSet(IFieldType.AUTO_NUMBER, IFieldType.COUNT,
            IFieldType.FORMULA, IFieldType.QUOTE, IFieldType.GROUP, IFieldType.MASTER_DETAIL);

    private static Config config;

    static {
        ConfigFactory.getConfig("fs-paas-app-quickedit-config", iConfig -> {
            log.warn("reload fs-paas-app-quickedit-config,content:{}", iConfig.getString());
            config = JSON.parseObject(iConfig.getString(), Config.class);
        });
    }

    public static boolean isFieldCannotEdit(String objectApiName, String fieldName) {
        if (config.fieldsCannotEdit.getOrDefault(objectApiName, Collections.emptyList()).contains(fieldName)) {
            return true;
        }
        return config.fieldsCannotEdit.getOrDefault("default", Collections.emptyList()).contains(fieldName);
    }

    public static boolean isFieldTypeCannotEdit(String fieldType) {
        if (DEFAULT_FIELD_TYPE_CANNOT_EDIT.contains(fieldType)) {
            return true;
        }
        return config.fieldTypeCannotEdit.contains(fieldType);
    }

    @Data
    private static class Config {
        private Map<String, List<String>> fieldsCannotEdit = Maps.newHashMap();
        private Set<String> fieldTypeCannotEdit = Sets.newHashSet();
    }
}
