package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.common.exception.CrmDefObjCheckedException;
import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.RichTextExt;

/**
 * Created by linqiuying on 17/6/9.
 */
public class RichTextFieldDataConverter extends AbstractFieldDataConverter {
    @Override
    public String convertFieldData(SessionContext sessionContext) throws CrmDefObjCheckedException {
        if (RichTextExt.isProcessableRichText(getFieldDescribe()) || (AppFrameworkConfig.cooperativeRichTextGray(String.valueOf(sessionContext.getEId()), getFieldDescribe().getDescribeApiName())
                && RichTextExt.isProcessableCooperativeRichText(getFieldDescribe()))) {
            //__o 显示的是纯文本内容
            return ObjectDataExt.of(getObjectData()).get(RichTextExt.getRichTextAbstractName(getFieldDescribe().getApiName()), String.class);
        }
        return getObjectData().get(getFieldDescribe().getApiName(), String.class);
    }
}
