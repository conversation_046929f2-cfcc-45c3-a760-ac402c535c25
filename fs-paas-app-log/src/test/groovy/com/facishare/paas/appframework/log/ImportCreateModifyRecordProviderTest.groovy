package com.facishare.paas.appframework.log

import com.facishare.paas.appframework.log.dto.LogInfo
import com.facishare.paas.appframework.log.dto.ModifyRecord
import spock.lang.Specification

/**
 * GenerateByAI
 * Test class for ImportCreateModifyRecordProvider to verify its functionality for handling import create modify records
 */
class ImportCreateModifyRecordProviderTest extends Specification {
    ImportCreateModifyRecordProvider provider

    void setup() {
        provider = new ImportCreateModifyRecordProvider()
    }

    /**
     * GenerateByAI
     * Test content: Verify that getApiName returns the correct action type for import operations
     */
    def "getApiNameTest"() {
        when: "Getting the API name"
        def result = provider.getApiName()

        then: "Should return Import action type ID"
        result == ActionType.Import.getId()
    }

    /**
     * GenerateByAI
     * Test content: Verify that getModifyRecord correctly processes log info and returns expected modify record
     */
    def "getModifyRecordTest"() {
        given: "A LogInfo object with necessary data"
        def logInfo = LogInfo.builder()
            .logId("testLogId")
            .objectName("testObject")
            .operation(ActionType.Import.getId())
            .build()

        when: "Getting modify record from log info"
        def result = provider.getModifyRecord(logInfo)

        then: "Should return a non-null ModifyRecord"
        result != null
        result instanceof ModifyRecord
        result.operationType == ActionType.Import.getId()
    }

    /**
     * GenerateByAI
     * Test content: Verify that getModifyRecord handles null log info gracefully
     */
    def "getModifyRecordErrorNullLogInfo"() {
        when: "Getting modify record with null log info"
        provider.getModifyRecord(null)

        then: "Should throw NullPointerException"
        thrown(NullPointerException)
    }
}
