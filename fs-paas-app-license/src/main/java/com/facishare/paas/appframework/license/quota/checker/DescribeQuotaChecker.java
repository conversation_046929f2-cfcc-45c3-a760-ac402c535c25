package com.facishare.paas.appframework.license.quota.checker;

import com.facishare.paas.appframework.license.util.ModulePara;
import lombok.Builder;

import java.util.Map;

public class DescribeQuota<PERSON>hecker extends BaseQuotaChecker{
    @Builder
    public DescribeQuotaChecker(Map<String, Integer> paraMap) {
        super(paraMap);
    }

    @Override
    public int getMaxCount(QuotaChecker.QuotaInfo quotaInfo) {
        return paraMap.getOrDefault(ModulePara.CUSTOM_OBJECTS_LIMIT.getParaKey(), 0);
    }
}
