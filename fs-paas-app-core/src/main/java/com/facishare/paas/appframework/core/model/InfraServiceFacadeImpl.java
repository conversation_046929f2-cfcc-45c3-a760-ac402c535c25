package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.button.ButtonActionExecutor;
import com.facishare.paas.appframework.button.CustomButtonExecutor;
import com.facishare.paas.appframework.button.CustomButtonMiscService;
import com.facishare.paas.appframework.common.service.*;
import com.facishare.paas.appframework.common.service.dto.OrganizationStatus;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds.DeptInfo;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.predef.facade.ChangeOrderHandlerLogicService;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.flow.WorkFlowService;
import com.facishare.paas.appframework.function.ValidRecordTypePlugin;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.bi.BIService;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderLogicService;
import com.facishare.paas.appframework.metadata.config.ObjectConfigService;
import com.facishare.paas.appframework.metadata.dimension.DimensionLogicService;
import com.facishare.paas.appframework.metadata.domain.DomainPluginLogicService;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.fieldalign.GlobalFieldAlignService;
import com.facishare.paas.appframework.metadata.fieldextra.FieldBackgroundExtraLogicService;
import com.facishare.paas.appframework.metadata.gdpr.GdprService;
import com.facishare.paas.appframework.metadata.lookup.LookUpLogicService;
import com.facishare.paas.appframework.metadata.publicobject.PublicObjectLogicService;
import com.facishare.paas.appframework.metadata.qixin.QiXinAppLogicService;
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService;
import com.facishare.paas.appframework.metadata.richtext.RichTextService;
import com.facishare.paas.appframework.metadata.switchcache.SwitchCacheService;
import com.facishare.paas.appframework.privilege.EnterpriseRelationLogicService;
import com.facishare.paas.appframework.privilege.EnterpriseRelationServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.transaction.tcc.api.GlobalTransactionInfo;
import com.fxiaoke.transaction.tcc.api.GlobalTransactionTemplate;
import com.fxiaoke.transaction.tcc.api.GlobalTransactionalHolder;
import lombok.Getter;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;

@Service("infraServiceFacade")
@Slf4j
public class InfraServiceFacadeImpl implements InfraServiceFacade {
    @Autowired
    private SpringBeanHolder springBeanHolder;

    @Delegate
    @Autowired
    private SwitchCacheService switchCacheService;

    @Autowired
    @Delegate
    private I18nSettingService i18nSettingService;

    @Delegate
    @Autowired
    private DomainPluginLogicService domainPluginLogicService;

    @Delegate
    @Autowired
    private RedissonService redissonService;

    @Autowired
    @Delegate
    @Qualifier("pentagonRelationService")
    private PentagonRelationService pentagonRelationService;

    @Autowired
    @Delegate
    private OptionalFeaturesService optionalFeaturesService;

    @Delegate
    @Autowired
    private PrintTemplateExportService printTemplateExportService;

    @Delegate
    @Autowired
    @Qualifier("validRecordTypePlugin")
    private ValidRecordTypePlugin validRecordTypePlugin;

    @Delegate
    @Autowired
    private GlobalFieldAlignService fieldAlignService;

    @Delegate
    @Autowired
    private GdprService gdprService;

    @Delegate
    @Autowired
    private SceneLogicService sceneLogicService;

    @Autowired
    @Delegate
    private DataSnapshotLogicService dataSnapshotLogicService;

    @Delegate
    @Autowired
    private DimensionLogicService dimensionLogicService;

    @Autowired
    @Delegate
    private ObjectMappingService objectMappingService;

    @Delegate
    @Autowired
    private IOcrLogicService ocrLogicService;

    @Autowired
    @Delegate
    private TagLogicService tagLogicService;

    @Autowired
    @Delegate
    private UIEventLogicService eventLogicService;

    @Autowired
    @Delegate
    private ObjectDataDraftService objectDataDraftService;

    @Autowired
    @Delegate
    private BizConfService bizConfService;

    @Delegate
    @Autowired
    private OuterOrganizationService outerOrganizationService;

    @Delegate
    @Autowired
    private EnterpriseRelationLogicService enterpriseRelationLogicService;

    @Delegate
    @Autowired
    private ObjectConfigService objectConfigService;

    @Delegate
    @Autowired
    private ChangeOrderHandlerLogicService changeOrderHandlerLogicService;

    @Delegate
    @Autowired
    private ChangeOrderLogicService changeOrderLogicService;

    @Autowired
    @Delegate
    private ButtonActionExecutor buttonActionExecutor;

    @Autowired
    @Delegate
    private CustomButtonExecutor customButtonExecutor;

    @Autowired
    @Delegate
    private FieldRelationCalculateService fieldRelationCalculateService;

    @Autowired
    @Delegate
    private ExpressionCalculateLogicService expressionCalculateLogicService;

    @Autowired
    @Delegate
    private WorkFlowService workFlowService;

    @Autowired
    @Delegate
    private QuoteValueService quoteValueService;

    @Autowired
    @Delegate
    private QixinGroupService qixinGroupService;

    @Autowired
    @Delegate
    private NetworkDiskService networkDiskService;

    @Autowired
    @Delegate
    private GeoAddressService geoAddressService;

    @Autowired
    @Delegate
    private BIService biService;

    @Autowired
    @Delegate
    private ObjectConvertRuleService objectConvertRuleService;

    @Autowired
    @Delegate
    private ConvertRuleLogicService convertRuleLogicService;

    @Autowired
    private GlobalTransactionTemplate globalTransactionTemplate;

    @Autowired
    @Delegate
    private RichTextService richTextService;

    @Autowired
    @Delegate
    private QiXinAppLogicService qiXinAppLogicService;

    @Autowired
    @Delegate
    private LookUpLogicService lookUpLogicService;

    @Autowired
    @Delegate
    private FieldBackgroundExtraLogicService fieldBackgroundExtraLogicService;

    @Autowired
    @Delegate
    private PublicObjectLogicService publicObjectLogicService;

    @Autowired
    @Getter
    private FileStoreService fileStoreService;

    @Autowired
    @Delegate
    private DataListHeaderConfigService dataListHeaderConfigService;

    @Autowired
    @Delegate
    private EnterpriseRelationServiceImpl enterpriseRelationService;

    @Autowired
    @Delegate
    private ValidateRuleServiceImpl validateRuleService;

    @Autowired
    @Delegate
    private FieldDataConvertService fieldDataConvertService;

    @Autowired
    @Delegate
    private CustomButtonMiscService customButtonMiscService;

    @Delegate
    @Autowired
    private ImportService importService;

    @Delegate
    @Autowired
    private LayoutRuleLogicService layoutRuleLogicService;

    @Transactional
    @Override
    public void runWithTransaction(Runnable runnable) {
        runnable.run();
    }

    @Transactional
    @Override
    public <T> T callWithTransaction(Supplier<T> supplier) {
        return supplier.get();
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T callWithGlobalTransaction(String name,
                                           int timeout,
                                           String tenantId,
                                           List<Class<? extends Throwable>> rollbackExceptions,
                                           List<Class<? extends Throwable>> noRollbackExceptions,
                                           Supplier<T> supplier) {

        log.info("callWithGlobalTransaction ei:{}, name:{}", tenantId, name);
        try {
            return (T) globalTransactionTemplate.execute(new GlobalTransactionalHolder() {
                @Override
                public Object execute() {
                    return supplier.get();
                }

                @Override
                public GlobalTransactionInfo getTransactionInfo() {
                    return GlobalTransactionInfo.builder()
                            .name(name)
                            .timeout(timeout)
                            .tenantId(tenantId)
                            .rollbackFor(rollbackExceptions)
                            .noRollbackFor(noRollbackExceptions)
                            .build();
                }
            });
        } catch (RuntimeException e) {
            throw e;
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public SpringBeanHolder getSpringBeanHolder() {
        return springBeanHolder;
    }

    @Override
    public void validateTypeForDataOwnDept(User user, IObjectDescribe objectDescribe, IObjectData objectData) {
        List<String> dataOwnDepartment = objectData.getDataOwnDepartment();
        if (CollectionUtils.empty(dataOwnDepartment)) {
            // 值为空 返回
            return;
        }
        if (RequestUtil.isCepRequest()) {
            // CEP请求 返回
            return;
        }
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DATA_OWN_DEPARTMENT_FILTER_BY_DEPT_TYPE, user.getTenantId())) {
            // 没有灰度 返回
            return;
        }
        OrganizationStatus.Arg arg = new OrganizationStatus.Arg();
        arg.setTenantId(user.getTenantId());
        PlatServiceProxy platServiceProxy = SpringUtil.getContext().getBean(PlatServiceProxy.class);
        OrganizationStatus.Result result = platServiceProxy.openOrganization(arg);
        // 没有开组织 返回
        if (!result.isOpenOrganization()) {
            return;
        }

        OrgService orgService = SpringUtil.getContext().getBean(OrgService.class);
        List<DeptInfo> deptList = orgService.getDeptInfoNameByIds(user.getTenantId(), user.getUserId(), dataOwnDepartment);
        if (!CollectionUtils.empty(deptList)) {
            DeptInfo dataOwnDept = deptList.get(0);
            if (Objects.equals(dataOwnDept.getDeptType(), QueryDeptInfoByDeptIds.TYPE_DEPT)
                    || Objects.equals(User.COMPANY_ID, dataOwnDept.getDeptId())) {
                // 归属部门类型为部门 返回
                return;
            }
            // 校验拦截：{0}的{1}类型不是部门，请完善{0}值再提交
            IFieldDescribe dataOwnDeptField = objectDescribe.getFieldDescribe(ObjectDataExt.DATA_OWN_DEPARTMENT);
            throw new ValidateException(I18NExt.text(I18NKey.DEPT_TYPE_VALIDATE, dataOwnDeptField.getLabel(), dataOwnDept.getDeptName(), dataOwnDeptField.getLabel()));
        }
    }
}
