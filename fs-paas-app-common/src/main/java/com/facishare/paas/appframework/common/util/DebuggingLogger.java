package com.facishare.paas.appframework.common.util;

import lombok.experimental.Delegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/12/20
 */
public enum DebuggingLogger {
    DEBUGGING_LOGGER(),
    ;
    @Delegate
    private final Logger LOG;

    DebuggingLogger() {
        LOG = LoggerFactory.getLogger(DebuggingLogger.class);
    }

}
