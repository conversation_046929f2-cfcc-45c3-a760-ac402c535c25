package com.facishare.paas.appframework.common.service

import com.facishare.organization.api.model.RunStatus
import com.facishare.organization.api.model.department.DepartmentDto
import com.facishare.organization.api.model.department.arg.*
import com.facishare.organization.api.model.department.result.*
import com.facishare.organization.api.service.DepartmentProviderService
import com.facishare.organization.api.service.OrganizationTreeService
import com.facishare.paas.I18N
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds.DeptStatusEnum
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import spock.lang.Specification
import spock.lang.Unroll
import org.powermock.reflect.Whitebox

/**
 * GenerateByAI
 * 测试内容描述：DepartmentServiceImpl服务类的单元测试
 */
class DepartmentServiceImplTest extends Specification {

    def departmentService = new DepartmentServiceImpl()
    def departmentProviderService = Mock(DepartmentProviderService)
    def organizationTreeService = Mock(OrganizationTreeService)

    def setup() {
        departmentService.departmentProviderService = departmentProviderService
        departmentService.organizationTreeService = organizationTreeService
    }

    def "test batchGetDepartment with status"() {
        given: "准备测试数据"
        def tenantId = "12345"
        def deptIds = ["1001", "1002"]
        def status = DeptStatusEnum.ENABLE
        
        def mockDept1 = new DepartmentDto()
        mockDept1.setDepartmentId(1001)
        mockDept1.setName("部门1")
        
        def mockDept2 = new DepartmentDto()
        mockDept2.setDepartmentId(1002)
        mockDept2.setName("部门2")
        
        def mockResult = new BatchGetDepartmentDtoResult()
        mockResult.setDepartments([mockDept1, mockDept2])

        when: "调用batchGetDepartment方法"
        def result = departmentService.batchGetDepartment(tenantId, deptIds, status)

        then: "应该调用departmentProviderService并返回正确结果"
        1 * departmentProviderService.batchGetDepartmentDto(_) >> { BatchGetDepartmentDtoArg arg ->
            assert arg.getEnterpriseId() == 12345
            assert arg.getDepartmentIds() == [1001, 1002]
            assert arg.getRunStatus() == RunStatus.ACTIVE
            return mockResult
        }
        result.size() == 2
        result[0].getDepartmentId() == 1001
        result[1].getDepartmentId() == 1002
    }

    def "test batchGetDepartment without status"() {
        given: "准备测试数据"
        def tenantId = "12345"
        def deptIds = ["1001"]
        
        def mockDept = new DepartmentDto()
        mockDept.setDepartmentId(1001)
        
        def mockResult = new BatchGetDepartmentDtoResult()
        mockResult.setDepartments([mockDept])

        when: "调用batchGetDepartment方法（不指定状态）"
        def result = departmentService.batchGetDepartment(tenantId, deptIds)

        then: "应该使用默认状态ALL"
        1 * departmentProviderService.batchGetDepartmentDto(_) >> { BatchGetDepartmentDtoArg arg ->
            assert arg.getRunStatus() == RunStatus.ALL
            return mockResult
        }
        result.size() == 1
    }

    def "test batchGetDepartment with empty deptIds"() {
        when: "使用空的部门ID列表"
        def result = departmentService.batchGetDepartment("12345", [])

        then: "应该返回空列表且不调用服务"
        0 * departmentProviderService._
        result.isEmpty()
    }

    def "test batchGetDepartment with exception"() {
        given: "准备会抛出异常的场景"
        def tenantId = "12345"
        def deptIds = ["1001"]

        when: "调用batchGetDepartment方法"
        departmentService.batchGetDepartment(tenantId, deptIds)

        then: "应该捕获异常并重新抛出RuntimeException"
        1 * departmentProviderService.batchGetDepartmentDto(_) >> { throw new Exception("Service error") }
        thrown(RuntimeException)
    }

    def "test getLowDepartment"() {
        given: "准备测试数据"
        def tenantId = "12345"
        def deptId = "1001"
        def status = DeptStatusEnum.ENABLE
        
        def mockDept = new DepartmentDto()
        mockDept.setDepartmentId(1002)
        mockDept.setName("子部门")
        
        def mockResult = new GetLowDepartmentsDtoResult()
        mockResult.setDepartmentDtos([mockDept])

        when: "调用getLowDepartment方法"
        def result = departmentService.getLowDepartment(tenantId, deptId, status)

        then: "应该调用departmentProviderService并返回正确结果"
        1 * departmentProviderService.getLowDepartmentsDto(_) >> { GetLowDepartmentsDtoArg arg ->
            assert arg.getDepartmentId() == 1001
            assert arg.getEnterpriseId() == 12345
            assert arg.getSelf() == true
            assert arg.getRunStatus() == RunStatus.ACTIVE
            return mockResult
        }
        result.size() == 1
        result[0].getDepartmentId() == 1002
    }

    def "test batchGetDepartmentByNames without multi-language"() {
        given: "准备测试数据"
        def tenantId = "12345"
        def deptNames = ["销售部", "技术部"]
        def status = DeptStatusEnum.ENABLE
        
        def mockDept = new DepartmentDto()
        mockDept.setDepartmentId(1001)
        mockDept.setName("销售部")
        
        def mockResult = new GetDepartmentByNamesResult()
        mockResult.setDepartments([mockDept])

        // Mock AppFrameworkConfig
        AppFrameworkConfig.metaClass.static.objectMultiLangGray = { String tid, String objName -> false }

        when: "调用batchGetDepartmentByNames方法"
        def result = departmentService.batchGetDepartmentByNames(tenantId, deptNames, status)

        then: "应该调用普通的getDepartmentByNames方法"
        1 * departmentProviderService.getDepartmentByNames(_) >> { GetDepartmentByNamesArg arg ->
            assert arg.getEnterpriseId() == 12345
            assert arg.getNames() == deptNames
            assert arg.getRunStatus() == RunStatus.ACTIVE
            return mockResult
        }
        result.size() == 1
        result[0].getName() == "销售部"
    }

    def "test batchGetDepartmentByNames with multi-language"() {
        given: "准备测试数据"
        def tenantId = "12345"
        def deptNames = ["销售部", "技术部"]
        def status = DeptStatusEnum.ENABLE
        
        def mockDept = new DepartmentDto()
        mockDept.setDepartmentId(1001)
        mockDept.setName("销售部")
        
        def mockResult = new GetDepartmentByNamesResult()
        mockResult.setDepartments([mockDept])

        // Mock AppFrameworkConfig and I18N
        AppFrameworkConfig.metaClass.static.objectMultiLangGray = { String tid, String objName -> true }
        def mockI18NContext = Mock()
        mockI18NContext.getLanguage() >> "zh_CN"
        I18N.metaClass.static.getContext = { -> mockI18NContext }

        when: "调用batchGetDepartmentByNames方法"
        def result = departmentService.batchGetDepartmentByNames(tenantId, deptNames, status)

        then: "应该调用多语言的getDepartmentByNamesMultiLanguage方法"
        1 * departmentProviderService.getDepartmentByNamesMultiLanguage(_) >> { GetDepartmentByNamesMultiLanguageArg arg ->
            assert arg.getEnterpriseId() == 12345
            assert arg.getNames() == deptNames
            assert arg.getRunStatus() == RunStatus.ACTIVE
            assert arg.getLanguageType() == "zh_CN"
            return mockResult
        }
        result.size() == 1
    }

    def "test batchGetDepartmentByPrincipal"() {
        given: "准备测试数据"
        def tenantId = "12345"
        def manageIdList = ["2001", "2002"]
        def status = DeptStatusEnum.ENABLE
        
        def mockDept = new DepartmentDto()
        mockDept.setDepartmentId(1001)
        mockDept.setManagerId(2001)
        
        def mockResult = new BatchGetDepartmentByPrincipalResult()
        mockResult.setDepartments([mockDept])

        when: "调用batchGetDepartmentByPrincipal方法"
        def result = departmentService.batchGetDepartmentByPrincipal(tenantId, manageIdList, status)

        then: "应该调用departmentProviderService并返回正确结果"
        1 * departmentProviderService.batchGetDepartmentByPrincipal(_) >> { BatchGetDepartmentByPrincipalArg arg ->
            assert arg.getEnterpriseId() == 12345
            assert arg.getPrincipalIds() == [2001, 2002]
            assert arg.getRunStatus() == RunStatus.ACTIVE
            return mockResult
        }
        result.size() == 1
        result[0].getManagerId() == 2001
    }

    def "test batchGetDepartmentByPrincipal with empty manageIdList"() {
        when: "使用空的管理者ID列表"
        def result = departmentService.batchGetDepartmentByPrincipal("12345", [], DeptStatusEnum.ENABLE)

        then: "应该返回空列表且不调用服务"
        0 * departmentProviderService._
        result.isEmpty()
    }

    def "test getDepartmentByEmployeeId"() {
        given: "准备测试数据"
        def tenantId = "12345"
        def userId = "3001"
        
        def mockDept = new DepartmentDto()
        mockDept.setDepartmentId(1001)
        
        def mockResult = new GetDepartmentDtoByEmployeeIdResult()
        mockResult.setDepartments([mockDept])

        when: "调用getDepartmentByEmployeeId方法"
        def result = departmentService.getDepartmentByEmployeeId(tenantId, userId)

        then: "应该调用departmentProviderService并返回正确结果"
        1 * departmentProviderService.getDepartmentDtoByEmployeeId(_) >> { GetDepartmentDtoByEmployeeIdArg arg ->
            assert arg.getEnterpriseId() == 12345
            assert arg.getEmployeeId() == 3001
            return mockResult
        }
        result.size() == 1
        result[0].getDepartmentId() == 1001
    }

    def "test getDepartmentByEmployeeId with blank userId"() {
        when: "使用空白的用户ID"
        def result = departmentService.getDepartmentByEmployeeId("12345", "")

        then: "应该返回空列表且不调用服务"
        0 * departmentProviderService._
        result.isEmpty()
    }

    @Unroll
    def "test convertDeptStatusToRunStatus with #status"() {
        given: "创建DepartmentServiceImpl实例"
        def service = new DepartmentServiceImpl()

        when: "调用私有方法convertDeptStatusToRunStatus"
        def result = Whitebox.invokeMethod(service, "convertDeptStatusToRunStatus", status)

        then: "应该返回正确的RunStatus"
        result == expectedRunStatus

        where:
        status                | expectedRunStatus
        DeptStatusEnum.ALL    | RunStatus.ALL
        DeptStatusEnum.ENABLE | RunStatus.ACTIVE
        DeptStatusEnum.DISABLE| RunStatus.STOP
    }

    @Unroll
    def "test convertRunStatusToDeptStatus with #runStatus"() {
        given: "创建DepartmentServiceImpl实例"
        def service = new DepartmentServiceImpl()

        when: "调用私有方法convertRunStatusToDeptStatus"
        def result = Whitebox.invokeMethod(service, "convertRunStatusToDeptStatus", runStatus)

        then: "应该返回正确的DeptStatusEnum"
        result == expectedDeptStatus

        where:
        runStatus        | expectedDeptStatus
        RunStatus.ALL    | DeptStatusEnum.ALL
        RunStatus.ACTIVE | DeptStatusEnum.ENABLE
        RunStatus.STOP   | DeptStatusEnum.DISABLE
    }

    def cleanup() {
        // 清理元类修改
        AppFrameworkConfig.metaClass = null
        I18N.metaClass = null
    }
}
