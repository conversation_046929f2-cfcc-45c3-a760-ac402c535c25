package com.facishare.paas.appframework.metadata.data.validator;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MultiValueValidator 单元测试
 */
class MultiValueValidatorTest {

  private MultiValueValidator validator;

  @BeforeEach
  void setUp() {
    validator = new MultiValueValidator();
  }

  @Test
  void testSupportFieldTypes() {
    Set<String> supportedTypes = validator.supportFieldTypes();
    
    assertEquals(3, supportedTypes.size());
    assertTrue(supportedTypes.contains(IFieldType.OBJECT_REFERENCE_MANY));
    assertTrue(supportedTypes.contains(IFieldType.SELECT_MANY));
    assertTrue(supportedTypes.contains(IFieldType.DIMENSION));
  }

  @Test
  void testValidateDataType_WithNullValue_ShouldPass() {
    // Given
    String fieldApiName = "test_field";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.SELECT_MANY);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, null);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithListValue_ShouldPass() {
    // Given
    String fieldApiName = "test_field";
    List<String> fieldValue = Lists.newArrayList("value1", "value2");
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.SELECT_MANY);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithSetValue_ShouldPass() {
    // Given
    String fieldApiName = "test_field";
    Set<String> fieldValue = Sets.newHashSet("value1", "value2");
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.OBJECT_REFERENCE_MANY);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithEmptyListValue_ShouldPass() {
    // Given
    String fieldApiName = "test_field";
    List<String> fieldValue = Lists.newArrayList();
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.DIMENSION);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithStringValue_ShouldThrowException() {
    // Given
    String fieldApiName = "test_field";
    String fieldValue = "single_value";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.SELECT_MANY);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    ValidateException exception = assertThrows(ValidateException.class,
        () -> validator.validateDataType(fieldApiName, objectData, describe));
    
    assertNotNull(exception.getMessage());
  }

  @Test
  void testValidateDataType_WithIntegerValue_ShouldThrowException() {
    // Given
    String fieldApiName = "test_field";
    Integer fieldValue = 123;
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.OBJECT_REFERENCE_MANY);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    ValidateException exception = assertThrows(ValidateException.class,
        () -> validator.validateDataType(fieldApiName, objectData, describe));
    
    assertNotNull(exception.getMessage());
  }

  @Test
  void testValidateDataType_WithBooleanValue_ShouldThrowException() {
    // Given
    String fieldApiName = "test_field";
    Boolean fieldValue = Boolean.TRUE;
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.DIMENSION);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, fieldValue);
    IObjectData objectData = new ObjectData(dataMap);

    // When & Then
    ValidateException exception = assertThrows(ValidateException.class,
        () -> validator.validateDataType(fieldApiName, objectData, describe));
    
    assertNotNull(exception.getMessage());
  }

  @Test
  void testValidateDataType_WithNullFieldApiName_ShouldReturn() {
    // Given
    String fieldApiName = null;
    IObjectDescribe describe = createObjectDescribe("test_field", IFieldType.SELECT_MANY);
    IObjectData objectData = new ObjectData(Collections.emptyMap());

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithEmptyFieldApiName_ShouldReturn() {
    // Given
    String fieldApiName = "";
    IObjectDescribe describe = createObjectDescribe("test_field", IFieldType.SELECT_MANY);
    IObjectData objectData = new ObjectData(Collections.emptyMap());

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithNullData_ShouldReturn() {
    // Given
    String fieldApiName = "test_field";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.SELECT_MANY);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, null, describe));
  }

  @Test
  void testValidateDataType_WithNullDescribe_ShouldReturn() {
    // Given
    String fieldApiName = "test_field";
    IObjectData objectData = new ObjectData(Collections.emptyMap());

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, null));
  }

  /**
   * 创建测试用的 IObjectDescribe 对象
   */
  private IObjectDescribe createObjectDescribe(String fieldApiName, String fieldType) {
    IObjectDescribe describe = new ObjectDescribe();
    describe.setApiName("TestObj");
    
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", fieldApiName);
    fieldMap.put("type", fieldType);
    fieldMap.put("label", "测试字段");
    
    IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
    
    describe.setFieldDescribes(Arrays.asList(fieldDescribe));
    return describe;
  }
}
