package com.facishare.paas.appframework.metadata.publicobject;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicEmployeeSimpleInfo;
import com.facishare.paas.appframework.metadata.publicobject.module.EnterpriseRelationQueryParam;
import com.facishare.paas.appframework.metadata.publicobject.module.EnterpriseRelationResult;
import com.facishare.paas.appframework.metadata.publicobject.module.EnterpriseRelationSimpleInfo;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * Created by zhaooju on 2023/12/25
 */
public interface PublicObjectEnterpriseRelationService {

    /**
     * 查询已经开启公共对象的互联企业
     *
     * @param user
     * @param objectApiName
     */
    EnterpriseRelationResult findConnectedEnterpriseRelation(User user, String objectApiName, EnterpriseRelationQueryParam queryParam);

    EnterpriseRelationResult findUnconnectedEnterpriseRelation(User user, String objectApiName, EnterpriseRelationQueryParam queryParam);

    Optional<EnterpriseRelationSimpleInfo> findEnterpriseRelationByDownstreamTenantId(User user, String downstreamTenantId);

    /**
     * 根据企业id，查询互联企业信息
     *
     * @param user
     * @param downstreamTenantIds
     * @return
     */
    List<EnterpriseRelationSimpleInfo> findEnterpriseRelationByDownstreamTenantIds(User user, Collection<String> downstreamTenantIds);

    /**
     * 根据企业组id，查询代管的互联企业信息
     *
     * @param user
     * @param tenantGroupIds
     * @return
     */
    List<EnterpriseRelationSimpleInfo> findCustodyMangeEnterpriseRelationByTenantGroup(User user, Collection<String> tenantGroupIds);

    List<PublicEmployeeSimpleInfo> findEnterpriseRelationAdminByDownstreamTenantId(User user, String downstreamTenantId);
}
