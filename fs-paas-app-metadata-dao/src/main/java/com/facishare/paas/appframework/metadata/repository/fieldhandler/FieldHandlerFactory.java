package com.facishare.paas.appframework.metadata.repository.fieldhandler;

import com.facishare.paas.appframework.metadata.repository.annotation.FieldType;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;

import java.lang.annotation.Annotation;
import java.util.Map;
import java.util.Objects;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/03/29
 */
public enum FieldHandlerFactory {
    INSTANCE,
    ;

    public static final String JSON_FIELD = "json";

    private final Map<String, FieldHandler> fieldHandlerMap;

    private final FieldHandler DEFAULT_FIELD_HANDLER = new DefaultFieldHandler();

    FieldHandlerFactory() {
        fieldHandlerMap = ImmutableMap.copyOf(init());
    }

    private Map<String, FieldHandler> init() {
        Map<String, FieldHandler> map = Maps.newHashMap();
        map.put(IFieldType.EMPLOYEE, new EmployeeHandler());
        map.put(IFieldType.DEPARTMENT, new EmployeeHandler());
        map.put(IFieldType.NUMBER, new NumberHandler());
        map.put(IFieldType.CURRENCY, new NumberHandler());
        map.put(IFieldType.PERCENTILE, new NumberHandler());
        map.put(IFieldType.TEXT, new TextFieldHandler());
        map.put(IFieldType.LONG_TEXT, new TextFieldHandler());
        map.put(IFieldType.TRUE_OR_FALSE, new TrueOrFalseFieldHandler());
        map.put(JSON_FIELD, new JsonFieldHandler());
        return map;
    }

    public FieldHandler getFieldHandler(FieldInfo fieldInfo) {
        if (Objects.isNull(fieldInfo) || Objects.isNull(fieldInfo.getAnnotation())) {
            return DEFAULT_FIELD_HANDLER;
        }
        Annotation annotation = fieldInfo.getAnnotation();
        Class<? extends Annotation> annotationType = annotation.annotationType();
        if (annotationType.isAnnotationPresent(FieldType.class)) {
            FieldType fieldType = annotationType.getAnnotation(FieldType.class);
            return fieldHandlerMap.getOrDefault(fieldType.type(), DEFAULT_FIELD_HANDLER);
        }
        return DEFAULT_FIELD_HANDLER;
    }
}
