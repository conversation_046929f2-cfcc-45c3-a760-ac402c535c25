package com.facishare.paas.appframework.rest.service;

import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.action.StandardChangeOwnerAction;
import com.facishare.paas.appframework.core.predef.controller.StandardController;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.rest.dto.data.IntelliFormData;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.ObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Yuanxl on 2018/5/10
 */
@Service
@Slf4j
public class IntelliFormDataService {

    @Autowired
    ServiceFacade serviceFacade;

    @Autowired
    private ActionLocateService actionLocateService;

    @Autowired
    private ControllerLocateService controllerLocateService;

    public Object changeOwner(IntelliFormData.ChangeOwnerArg arg){
        //转换成Action适配的格式
        StandardChangeOwnerAction.Arg actArg = convertChangeOwnerArg(arg);

        ActionContext context = ContextManager.buildActionContext(arg.getDescribe_api_name(),
                StandardAction.ChangeOwner.toString());
        Action action  =  actionLocateService.locateAction(context, actArg);

        return action.act(action.getArg());
    }

    public IntelliFormData.CreateDataResult createObjectData(IntelliFormData.CreateDataArg arg) {
        //arg转换成Action适配的格式
        BaseObjectSaveAction.Arg actArg = converToActionAddArg(arg);
        ActionContext context = ContextManager.buildActionContext(
                actArg.getObjectData().toObjectData().getDescribeApiName(), StandardAction.Add.toString());
        Action action = actionLocateService.locateAction(context, actArg);

        @SuppressWarnings("unchecked")
        BaseObjectSaveAction.Result tempResult = (BaseObjectSaveAction.Result) action.act(action.getArg());

        IntelliFormData.CreateDataResult result = new IntelliFormData.CreateDataResult();
        result.setObjectDataDocument(ObjectDataDocument.of(tempResult.getObjectData()));

        return result;
    }

    public IntelliFormData.FindDataListResult findDataList(IntelliFormData.FindDataListArg arg,
                                                           RequestContext requestContext) {
        IntelliFormData.FindDataListResult result = new IntelliFormData.FindDataListResult();
        //arg转换为StandardListController.Arg
        StandardListController.Arg controllerArg = convertToListControllerArg(arg, requestContext);
        //使用StandardListController处理业务逻辑
        ControllerContext context = ContextManager.buildControllerContext(arg.getDescribeAPIName(),
                StandardController.List.toString());
        Controller controller = controllerLocateService.locateController(context, controllerArg);

        @SuppressWarnings("unchecked")
        StandardListController.Result tempResult =
                (StandardListController.Result)controller.service(controller.getArg());
        result.setDataDocumentList(tempResult.getDataList());
        return result;
    }

    public IntelliFormData.FindByIdResult findObjectDataById(IntelliFormData.FindByIdArg arg) {
        //arg转换为StandardDetailController.Arg格式
        StandardDetailController.Arg controllerArg = convertToDetailControllerArg(arg);

        ControllerContext context = ContextManager.buildControllerContext(arg.getDescribeAPIName(),
                StandardController.Detail.toString());
        Controller controller = controllerLocateService.locateController(context, controllerArg);
        @SuppressWarnings("unchecked")
        StandardDetailController.Result tempResult = (StandardDetailController.Result) controller.service(
                controller.getArg());

        IntelliFormData.FindByIdResult result = new IntelliFormData.FindByIdResult();
        result.setLayoutDocument(tempResult.getLayout());
        result.setObjectDataDocument(tempResult.getData());
        result.setObjectDescribeDocument(tempResult.getDescribe());

        return result;
    }

    private StandardListController.Arg convertToListControllerArg(IntelliFormData.FindDataListArg arg,
                                                                  RequestContext requestContext) {
        StandardListController.Arg result = new StandardListController.Arg();
        //拼装search query
        String query = "{\"limit\":" + arg.getPageSize() +
                ",\"offset\":0,\"filters\":[{\"field_name\":\"create_time\",\"field_values\":[" + arg.getLastTime() +
                "],\"operator\":\"LTE\"}],\"orders\":[{\"fieldName\":\"create_time\",\"isAsc\":false}]}";

        result.setIncludeLayout(false);
        result.setObjectDescribeApiName(arg.getDescribeAPIName());
        result.setSearchQueryInfo(query);
        //设置templateId
        List<ISearchTemplate> templateList =
                serviceFacade.findByDescribeApiNameAndExtendAttribute(arg.getDescribeAPIName(),null, requestContext.getUser());
        if(CollectionUtils.isNotEmpty(templateList)){
            for (ISearchTemplate iSearchTemplate : templateList) {
                // TODO: 2024/9/20 I18n
                if("全部".equals(iSearchTemplate.getLabel())){ // ignoreI18n
                    result.setSearchTemplateId(iSearchTemplate.getId());
                    break;
                }
            }
        }

        return result;
    }

    private StandardChangeOwnerAction.Arg convertChangeOwnerArg(IntelliFormData.ChangeOwnerArg arg) {
        StandardChangeOwnerAction.Arg result = new StandardChangeOwnerAction.Arg();
        result.setOldOwnerStrategy(arg.getOld_owner_strategy());
        result.setOldOwnerTeamMemberPermissionType(arg.getOld_owner_team_member_permission_type());
        result.setOldOwnerTeamMemberRole(arg.getOld_owner_team_member_role());

        List<IntelliFormData.ChangeOwnerData> dataList = arg.getData();
        List<StandardChangeOwnerAction.ChangeOwnerData> resultList = new ArrayList<>();
        dataList.forEach(x -> {
            StandardChangeOwnerAction.ChangeOwnerData data = new StandardChangeOwnerAction.ChangeOwnerData();
            data.setOwnerId(x.getOwner_id());
            data.setObjectDataId(x.getObject_data_id());
            resultList.add(data);
        });

        result.setData(resultList);
        return result;
    }

    private BaseObjectSaveAction.Arg converToActionAddArg(IntelliFormData.CreateDataArg arg) {
        BaseObjectSaveAction.Arg result = new BaseObjectSaveAction.Arg();
        IObjectData objectData = new ObjectData();
        objectData.fromJsonString(arg.getData());
        result.setObjectData(ObjectDataDocument.of(objectData));

        return result;
    }

    private StandardDetailController.Arg convertToDetailControllerArg(IntelliFormData.FindByIdArg arg) {
        StandardDetailController.Arg result = new StandardDetailController.Arg();
        result.setObjectDataId(arg.getDataId());
        result.setObjectDescribeApiName(arg.getDescribeAPIName());
        return result;
    }

}
