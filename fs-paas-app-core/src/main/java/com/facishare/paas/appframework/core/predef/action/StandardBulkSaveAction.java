package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.timezone.annotation.ObjectDataField;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class StandardBulkSaveAction extends PreDefineAction<StandardBulkSaveAction.Arg, StandardBulkSaveAction.Result> {

    protected List<IObjectData> objectDatas;

    private List<IObjectData> toAddList = Lists.newArrayList();
    private List<IObjectData> toUpdateList = Lists.newArrayList();

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.Edit.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    @Override
    protected void init() {
        super.init();
        this.objectDatas = arg.objectDatas.stream()
                .map(x -> x.toObjectData())
                .collect(Collectors.toList());
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result doAct(Arg arg) {
        if (CollectionUtils.empty(objectDatas)) {
            return Result.builder()
                    .objectDatas(Lists.newArrayList())
                    .build();
        }

        for (IObjectData objectData : objectDatas) {
            if (StringUtils.isEmpty(objectData.getId())) {
                toAddList.add(objectData);
            } else {
                toUpdateList.add(objectData);
            }
        }

        List<IObjectData> result = Lists.newArrayList();

        if (CollectionUtils.notEmpty(toAddList)) {
            result.addAll(saveValue(actionContext.getUser(), toAddList));
        }

        if (CollectionUtils.notEmpty(toUpdateList)) {
            result.addAll(updateValue(actionContext.getUser(), toUpdateList));
        }

        List<ObjectDataDocument> resultDocument = result.stream().map(data -> ObjectDataDocument.of(data))
                .collect(Collectors.toList());

        return Result.builder()
                .objectDatas(resultDocument)
                .build();
    }

    protected List<IObjectData> saveValue(User user, List<IObjectData> objectDataList) {
        List<IObjectData> result = serviceFacade.bulkSaveObjectData(objectDataList, user);
        return result;
    }

    protected List<IObjectData> updateValue(User user, List<IObjectData> objectDataList) {
        List<IObjectData> result = serviceFacade.batchUpdate(objectDataList, user);
        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        recordLog();


        return result;
    }

    protected void recordLog() {

    }

    @Data
    public static class Arg {
        @JSONField(name = "M1")
        @JsonProperty("object_datas")
        List<ObjectDataDocument> objectDatas = Lists.newArrayList();

		/**
		 * true：手动导入（默认）
		 * false：自动导入
		 */
		private Boolean autoImport = false;


		/**
		 * 是否首次升级高阶功能
		 */
		private Boolean firstUpgrade = false;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        @JSONField(name = "M1")
        @JsonProperty("object_datas")
        List<ObjectDataDocument> objectDatas;
    }
}
