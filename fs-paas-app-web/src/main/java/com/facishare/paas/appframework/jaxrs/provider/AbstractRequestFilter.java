package com.facishare.paas.appframework.jaxrs.provider;

import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.rest.InnerHeaders;
import org.apache.commons.lang3.StringUtils;
import org.jboss.resteasy.spi.ResteasyProviderFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;

/**
 * Created by zhouwr on 2017/9/30
 */
public abstract class AbstractRequestFilter implements ContainerRequestFilter {

    protected Logger log = LoggerFactory.getLogger(getClass());

    @Override
    public void filter(ContainerRequestContext containerRequestContext) {
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName());
        RequestContext context = getRequestContext(containerRequestContext);
        stopWatch.lap("getRequestContext");
        ResteasyProviderFactory.pushContext(RequestContext.class, context);
        stopWatch.lap("pushContext");
        RequestContextManager.setContext(context);
        stopWatch.lap("setContext");
        context.getUser().validate();
        stopWatch.lap("validate");
        stopWatch.logSlow(1000);
    }

    protected abstract RequestContext getRequestContext(ContainerRequestContext context);

    protected boolean isBatch(ContainerRequestContext containerRequestContext) {
        String batch = containerRequestContext.getHeaderString(InnerHeaders.BATCH);
        if ((batch != null) && batch.equalsIgnoreCase("false")) {
            return false;
        }
        return true;
    }

    protected boolean isParamsIdempotent(ContainerRequestContext containerRequestContext) {
        String idempotent = containerRequestContext.getHeaderString(InnerHeaders.PARAMS_IDEMPOTENT);
        if (idempotent != null && idempotent.equalsIgnoreCase("true")) {
            return true;
        }
        return false;
    }

    protected boolean isUpdateOriginSource(ContainerRequestContext containerRequestContext) {
        String updateOriginSource = containerRequestContext.getHeaderString(InnerHeaders.UPDATE_ORIGIN_SOURCE);
        if (StringUtils.equalsIgnoreCase(updateOriginSource, "true")) {
            return true;
        }
        return false;
    }
}
