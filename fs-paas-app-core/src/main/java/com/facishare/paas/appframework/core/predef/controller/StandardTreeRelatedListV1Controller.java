package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.timezone.annotation.ObjectDataField;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.RecursiveParameter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.facishare.paas.metadata.ui.layout.ILayout.DETAIL_LAYOUT_TYPE;

public class StandardTreeRelatedListV1Controller extends BaseListController<StandardTreeRelatedListV1Controller.Arg> {

    protected ObjectDescribeExt extendObjectDescribe;
    protected Boolean hasData = Boolean.TRUE;
    protected List<IObjectData> mainObjectDataList;

    @Override
    protected void init() {
        if (CollectionUtils.empty(arg.getRootProductIds())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_EMPTY));
        }
        super.init();
        extendObjectDescribe = findObject(arg.getExtendObjDescApiName());
    }

    protected SearchTemplateQuery buildTreeSearchTemplateQuery(ObjectDescribeExt objectDescribeExt, String searchQueryInfo) {
        SearchTemplateQuery query = serviceFacade.getSearchTemplateQuery(controllerContext.getUser(), objectDescribeExt,
                getSearchTemplateId(), searchQueryInfo, true);
        //递归查找
        RecursiveParameter recursiveParameter = new RecursiveParameter();
        IFilter filter = new Filter();
        filter.setFieldName(arg.getRecursiveFieldName());
        filter.setOperator(Operator.IN);
        filter.setFieldValues(Lists.newArrayList(arg.getRootProductIds()));
        recursiveParameter.setFilters(Lists.newArrayList(filter));
        recursiveParameter.setExcludeRoot(true);
        recursiveParameter.setIdFieldName(arg.getRecursiveFieldName());
        recursiveParameter.setParentIdFieldName(arg.getRecursiveParentFieldName());
        recursiveParameter.setFindParent(false);
        query.setRecursiveParameter(recursiveParameter);

        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, "life_status", "normal");
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, "is_deleted", "0");
        return query;
    }

    @Override
    protected String getSearchQueryInfo() {
        return arg.getChildSearchQueryInfo();
    }

    @Override
    protected String getSearchTemplateId() {
        return null;
    }

    @Override
    protected String getSearchTemplateApiName() {
        return null;
    }

    @Override
    protected String getSearchTemplateType() {
        return null;
    }

    @Override
    protected Map<String, Integer> getDescribeVersionMap() {
        return null;
    }

    @Override
    protected String getUsePageType() {
        return null;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected BaseListController.Result doService(Arg arg) {
        SearchTemplateQuery queryChild = buildTreeSearchTemplateQuery(objectDescribe, arg.getChildSearchQueryInfo());
        mainObjectDataList = Lists.newArrayList();
        if (hasData) {
            QueryResult<IObjectData> queryChildResult = getQueryResult(queryChild);
            mainObjectDataList = queryChildResult.getData();
        }
        return buildResult(getMobileLayout(), getDetailLayout(), mainObjectDataList);
    }

    protected Result buildResult(List<ILayout> mobileLayouts, List<ILayout> detailLayouts, List<IObjectData> queryChildList) {
        ObjectData childObjectData = new ObjectData(objectDescribe.getApiName(), ObjectDataDocument.ofList(queryChildList));

        Result result = new Result();
        result.setDataMapList(Lists.newArrayList(childObjectData));
        if (arg.getIncludeDesc()) {
            result.setObjectDescribes(Lists.newArrayList(ObjectDescribeDocument.ofList(Lists.newArrayList(objectDescribe, extendObjectDescribe))));
            result.setListLayoutList(LayoutDocument.ofList(mobileLayouts));
            result.setDetailLayoutList(LayoutDocument.ofList(detailLayouts));
        }

        return result;
    }

    protected ILayout getChildDetailLayout() {
        return serviceFacade.getLayoutLogicService().findDefaultLayout(buildLayoutContext(), DETAIL_LAYOUT_TYPE, objectDescribe.getApiName());
    }

    protected ILayout getExtendDetailLayout() {
        return serviceFacade.getLayoutLogicService().findDefaultLayout(buildLayoutContext(), DETAIL_LAYOUT_TYPE, arg.getExtendObjDescApiName());
    }

    protected List<ILayout> getDetailLayout() {
        return Lists.newArrayList(
                getChildDetailLayout()
                , getExtendDetailLayout());
    }

    protected List<ILayout> getMobileLayout() {
        List<ILayout> rst = Lists.newArrayList();
        rst.addAll(findMobileLayouts(objectDescribe));
        rst.addAll(findMobileLayouts(extendObjectDescribe));
        return rst;
    }

    @Data
    public static class Arg {

        /**
         * 查询富文本
         */
        @JSONField(name = "search_rich_text_extra")
        @JsonProperty("search_rich_text_extra")
        @SerializedName("search_rich_text_extra")
        private Boolean searchRichTextExtra;

        @JSONField(name = "object_data")
        @JsonProperty("object_data")
        @SerializedName("object_data")
        private ObjectDataDocument objectData;

        @JSONField(name = "details")
        @JsonProperty("details")
        @SerializedName("details")
        private Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();

        @JSONField(name = "root_product_ids")
        @JsonProperty("root_product_ids")
        @SerializedName("root_product_ids")
        private Set<String> rootProductIds;

        @JSONField(name = "recursive_field_name")
        @JsonProperty("recursive_field_name")
        @SerializedName("recursive_field_name")
        private String recursiveFieldName;

        @JSONField(name = "recursive_parent_field_name")
        @JsonProperty("recursive_parent_field_name")
        @SerializedName("recursive_parent_field_name")
        private String recursiveParentFieldName;

        @JSONField(name = "extend_obj_desc_api_name")
        @JsonProperty("extend_obj_desc_api_name")
        @SerializedName("extend_obj_desc_api_name")
        private String extendObjDescApiName;

        @JSONField(name = "child_search_query_info")
        @JsonProperty("child_search_query_info")
        @SerializedName("child_search_query_info")
        private String childSearchQueryInfo;

        @JSONField(name = "price_book_id")
        @JsonProperty("price_book_id")
        @SerializedName("price_book_id")
        private String priceBookId;

        @JSONField(name = "include_desc")
        @JsonProperty("include_desc")
        @SerializedName("include_desc")
        private Boolean includeDesc;

        @JSONField(name = "account_id")
        @JsonProperty("account_id")
        @SerializedName("account_id")
        private String accountId;

        @JSONField(name = "partner_id")
        @JsonProperty("partner_id")
        @SerializedName("partner_id")
        private String partnerId;

        @JSONField(name = "mc_currency")
        @JsonProperty("mc_currency")
        @SerializedName("mc_currency")
        private String mcCurrency;

        @JSONField(name = "bom_list")
        @JsonProperty("bom_list")
        @SerializedName("bom_list")
        private List<ObjectDataDocument> bomList;

        @JSONField(name = "include_constraint")
        @JsonProperty("include_constraint")
        @SerializedName("include_constraint")
        private Boolean includeConstraint;

        @JSONField(name = "root_product_list")
        @JsonProperty("root_product_list")
        @SerializedName("root_product_list")
        private List<ObjectDataDocument> rootProductList;

        @JSONField(name = "query_child")
        @JsonProperty("query_child")
        @SerializedName("query_child")
        private boolean queryChild;

        @JSONField(name = "query_first_level_child")
        @JsonProperty("query_first_level_child")
        @SerializedName("query_first_level_child")
        private boolean queryFirstLevelChild;

        @JSONField(name = "page_flag")
        @JsonProperty("page_flag")
        @SerializedName("page_flag")
        private boolean pageFlag;

        @JSONField(name = "filter_empty_groups")
        @JsonProperty("filter_empty_groups")
        @SerializedName("filter_empty_groups")
        private Boolean filterEmptyGroups;

        //每次只允许请求一个core bom,以下三个字段是针对取core bom使用的参数
        @JSONField(name = "bom_core_id")
        @JsonProperty("bom_core_id")
        @SerializedName("bom_core_id")
        private String rootBomCoreId;
        //复用bom在当前bomcore下的完整路径，供子节点拼接bomPath使用
        @JSONField(name = "new_bom_path")
        @JsonProperty("new_bom_path")
        @SerializedName("new_bom_path")
        private String newBomPath;
        //复用bom在当前bomcore下的节点ID
        @JSONField(name = "new_bom_id")
        @JsonProperty("new_bom_id")
        @SerializedName("new_bom_id")
        private String newBomId;

        @JSONField(name = "include_all_sub_core_id")
        @JsonProperty("include_all_sub_core_id")
        @SerializedName("include_all_sub_core_id")
        private boolean includeAllSubCoreId;

        @JSONField(name = "bom_id")
        @JsonProperty("bom_id")
        @SerializedName("bom_id")
        private String bomId;

        //扩展属性，用于传递业务参数
        @JSONField(name = "extraData")
        @JsonProperty("extraData")
        @SerializedName("extraData")
        private Map<String, Object> extraData;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Result extends BaseListController.Result {
        private List<ObjectDescribeDocument> objectDescribes;

        private List<LayoutDocument> listLayoutList;

        private List<LayoutDocument> detailLayoutList;

        private List<ObjectData> dataMapList;

        private List<Map<String, String>> allSubBomList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ObjectData {
        private String describeApiName;
        private List<ObjectDataDocument> dataList;

    }

}
