package com.facishare.paas.appframework.rest.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.controller.StandardController;
import com.facishare.paas.appframework.core.predef.service.dto.objectImport.FindImportInfosByNames;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.appframework.rest.dto.data.BulkImport;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@ServiceModule("rest_bulk_import")
@Service
@Slf4j
public class BulkImportRestService {

    @Autowired
    private ServiceFacade serviceFacade;

    public List<BulkImport.PrimaryFieldInfo> findPrimaryFieldList(RequestContext requestContext) {
        try {
            return findFieldFiledList(requestContext);
        } catch (Exception e) {
            log.error(I18N.text(I18NKey.QUERY_NAME_FAILED));
            throw new ValidateException(I18N.text(I18NKey.QUERY_NAME_FAILED));
        }
    }

    /**
     * 通过serviceFacade对象查找主属性列表
     * @param requestContext 上下文环境
     * @return 主属性列表
     */
    private List<BulkImport.PrimaryFieldInfo> findFieldFiledList(RequestContext requestContext) {
        List<BulkImport.PrimaryFieldInfo> result = null;
        List<IObjectDescribe> objectDescribeList = serviceFacade.
                findDescribeByPrivilegeAndModule(requestContext.getUser(),
                StandardController.List.name(), false, true, false, true);
        if(null != objectDescribeList){
            result = new ArrayList<>(objectDescribeList.size());
            for (IObjectDescribe describe : objectDescribeList) {
                result.add(new BulkImport.PrimaryFieldInfo(describe));
            }
        }

        return result;
    }

    public List<BulkImport.ImportRowInfo> findImportInfoListByNames(BulkImport.Arg arg, RequestContext requestContext) {
        List<FindImportInfosByNames.CellStruct> cellStructList = arg.getCellStructList();
        return findImportRowInfoByNames(cellStructList, requestContext);
    }

    /**
     * 根据名称列表查找ImportRowInfo
     * @param cellStructList 数据列表
     * @param requestContext 业务环境上下文
     * @return 每行数据列表
     */
    private List<BulkImport.ImportRowInfo> findImportRowInfoByNames(
            List<FindImportInfosByNames.CellStruct> cellStructList, RequestContext requestContext) {
        if(CollectionUtils.isEmpty(cellStructList)){
            return Lists.newArrayList();
        }

        Map<String, Set<String>> group = Maps.newHashMap();
        cellStructList.forEach(data -> {
            if(group.containsKey(data.getDescribeApiName())){
                group.get(data.getDescribeApiName()).add(data.getPrimaryFieldValue());
            }else{
                group.put(data.getDescribeApiName(), Sets.newHashSet(data.getPrimaryFieldValue()));
            }
        });

        List<FindImportInfosByNames.ImportCellInfo> allDataList = Lists.newArrayList();
        group.forEach((apiName , nameSet) -> {
            List<FindImportInfosByNames.ImportCellInfo> dataInfoList = getImportInfoByRowData(
                    Lists.newArrayList(nameSet), requestContext, apiName);
            if(!CollectionUtils.isEmpty(dataInfoList)){
                allDataList.addAll(dataInfoList);
            }
        });

        Map<Integer, BulkImport.ImportRowInfo> importRowInfoMap = Maps.newHashMap();
        cellStructList.forEach(data -> {
            BulkImport.ImportRowInfo info = importRowInfoMap.get(data.getRowNo());
            if(null == info){
                info = new BulkImport.ImportRowInfo();
                info.setRowNo(data.getRowNo());
                info.setCellInfoList(Lists.newArrayList());
                importRowInfoMap.put(data.getRowNo(), info);
            }
            FindImportInfosByNames.ImportCellInfo cellInfo = getCellInfo(allDataList, data);
            info.getCellInfoList().add(cellInfo);
        });

        return Lists.newArrayList(importRowInfoMap.values());
    }

    /**
     * 从现有数据中查找指定数据结构的信息
     * @param allDataList 现有数据
     * @param data 指定数据结构
     * @return 指定数据结构的信息
     */
    private FindImportInfosByNames.ImportCellInfo getCellInfo(List<FindImportInfosByNames.ImportCellInfo> allDataList,
                                                              FindImportInfosByNames.CellStruct data) {
        Optional<FindImportInfosByNames.ImportCellInfo> first = allDataList.stream().filter(
                a -> Objects.equals(a.getDescribeApiName(), data.getDescribeApiName())
            && Objects.equals(a.getPrimaryFieldValue(), data.getPrimaryFieldValue())).findFirst();
        if(first.isPresent()){
            first.get().setRowNo(data.getRowNo());
            return first.get();
        }

        FindImportInfosByNames.ImportCellInfo nullCell = new FindImportInfosByNames.ImportCellInfo();
        nullCell.setRowNo(data.getRowNo());
        nullCell.setAmount(0);
        nullCell.setPrimaryFieldValue(data.getPrimaryFieldValue());
        nullCell.setDescribeApiName(data.getDescribeApiName());
        return nullCell;
    }

    /**
     * 查找导入数据信息列表
     * @param nameList 主属性列表
     * @param requestContext 业务环境上下文
     * @param apiName API名称
     * @return 每行数据的列表
     */
    private List<FindImportInfosByNames.ImportCellInfo> getImportInfoByRowData(
            ArrayList<String> nameList, RequestContext requestContext, String apiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(2000);
        query.setOffset(0);
        if(!Objects.equals(Utils.PRICE_BOOK_API_NAME, apiName)){
            query.setPermissionType(Permissions.READ_ONLY.intValue()); //走数据权限
        }

        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName(IObjectData.IS_DELETED);
        filter.setFieldDefineType(IObjectDescribe.DEFINE_TYPE_SYSTEM);
        filter.setOperator(Operator.EQ);
        filter.setIndexName(IObjectData.IS_DELETED);
        filter.setFieldValues(Lists.newArrayList("0"));
        filters.add(filter);

        filter = new Filter();
        filter.setFieldName(IObjectData.DESCRIBE_API_NAME);
        filter.setFieldDefineType(IObjectDescribe.DEFINE_TYPE_SYSTEM);
        filter.setOperator(Operator.EQ);
        filter.setIndexName(IObjectData.DESCRIBE_API_NAME);
        filter.setFieldValues(Lists.newArrayList(apiName));
        filters.add(filter);

        filter = new Filter();
        filter.setFieldName(IObjectData.NAME);
        filter.setFieldDefineType(IObjectDescribe.DEFINE_TYPE_SYSTEM);
        filter.setFieldValueType("string");
        filter.setOperator(Operator.IN);
        filter.setIndexName(IObjectData.NAME);
        filter.setFieldValues(nameList);
        filters.add(filter);

        query.setFilters(filters);

        IActionContext context = ActionContextExt.of(requestContext.getUser(),
                requestContext).getContext();

        QueryResult<IObjectData> resultList = serviceFacade.findBySearchQuery(context, apiName, query);
        if(Objects.isNull(resultList) || CollectionUtils.isEmpty(resultList.getData())){
            return null;
        }

        //根据主属性分组
        Map<String, List<IObjectData>> group = Maps.newHashMap();
        resultList.getData().forEach(data -> {
            if(group.containsKey(data.getName())){
                group.get(data.getName()).add(data);
            }else{
                group.put(data.getName(), Lists.newArrayList(data));
            }
        });

        List<FindImportInfosByNames.ImportCellInfo> list = Lists.newArrayList();
        group.forEach((name, dataList) -> {
            FindImportInfosByNames.ImportCellInfo info = new FindImportInfosByNames.ImportCellInfo();
            info.setDescribeApiName(apiName);
            info.setAmount(dataList.size());
            info.setPrimaryFieldValue(name);
            info.setFirstDataId(dataList.get(0).getId());
            list.add(info);
        });

        return list;

    }

}
