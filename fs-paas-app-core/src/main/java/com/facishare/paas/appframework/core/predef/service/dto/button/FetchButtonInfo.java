package com.facishare.paas.appframework.core.predef.service.dto.button;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.button.ButtonURL;
import com.facishare.paas.metadata.api.IUdefButton;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;
import java.util.stream.Collectors;


public interface FetchButtonInfo {
    @Data
    class Arg {
        private Long lastFetchTime;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private List<ButtonInfo> buttonList;

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ButtonInfo {
        String actionCode;
        String describeApiName;
        String url;
        String customAction;
        Set<String> recordType;
        String sourceDescribeApiName;
        String sourceFieldApiName;
        NaviUIAction uiAction;

        public static List<ButtonInfo> from(IUdefButton button, boolean isMobileClient) {
            if(Objects.isNull(button)) {
                return Lists.newArrayList();
            }

            ButtonURL buttonURL = ButtonURL.of(button.getUrl());
            return CollectionUtils.nullToEmpty(buttonURL.getUrlInfos(isMobileClient))
                    .stream()
                    .map(a->ButtonInfo.builder()
                            .actionCode(ObjectAction.getByButtonApiName(button.getApiName()).getActionCode())
                            .url(a.getUrl())
                            .describeApiName(button.getDescribeApiName())
                            .customAction(a.getCustomAction())
                            .recordType(a.getRecordType())
                            .build())
                    .collect(Collectors.toList());
        }

        public static List<ButtonInfo> from(List<IUdefButton> buttonList, boolean isMobileClient) {
            return CollectionUtils.nullToEmpty(buttonList)
                    .stream()
                    .flatMap(a -> ButtonInfo.from(a, isMobileClient).stream())
                    .collect(Collectors.toList());
        }

        public static ButtonInfo from(Map<String, String> map) {
            return ButtonInfo.builder()
                    .actionCode(String.valueOf(map.getOrDefault("actionCode", "")))
                    .describeApiName(String.valueOf(map.getOrDefault("describeApiName", "")))
                    .url(String.valueOf(map.getOrDefault("url", "")))
                    .sourceDescribeApiName(String.valueOf(map.getOrDefault("sourceDescribeApiName", "")))
                    .sourceFieldApiName(String.valueOf(map.getOrDefault("sourceFieldApiName", "")))
                    .build();
        }

        public static ButtonInfo from(NaviUIAction action) {
            return ButtonInfo.builder()
                    .actionCode(action.getActionCode())
                    .describeApiName(action.getDescribeApiName())
                    .uiAction(action)
                    .build();
        }

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class NaviUIAction {
        String actionCode;
        String describeApiName;
        private NaviUIComponent component;
        private Map<String, Object> userData;
        private String type;
        private String action;

        public static NaviUIAction of(Map<String, String> map) {
            return NaviUIAction.builder()
                    .actionCode(map.getOrDefault("actionCode", ""))
                    .describeApiName(map.getOrDefault("describeApiName", ""))
                    .type(map.getOrDefault("type", ""))
                    .component(NaviUIComponent.from(String.valueOf(map.getOrDefault("component", "{}"))))
                    .action(map.getOrDefault("action", ""))
                    .build();
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class NaviUIComponent {
        private String apiName;
        private String url;

        public static NaviUIComponent from(String json) {
            return JSON.parseObject(json, NaviUIComponent.class);
        }
    }

}
