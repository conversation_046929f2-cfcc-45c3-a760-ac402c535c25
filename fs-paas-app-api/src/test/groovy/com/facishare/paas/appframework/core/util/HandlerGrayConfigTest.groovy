package com.facishare.paas.appframework.core.util

import com.alibaba.fastjson.JSON
import com.github.autoconf.ConfigFactory
import com.github.autoconf.api.IConfig
import com.fxiaoke.release.GrayRule
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

class HandlerGrayConfigTest extends Specification {

    @Shared
    def configData = [
            interfaceGrayRules         : [
                    'Add': [
                            'udobj'        : 'white:74255|78057',
                            'SalesOrderObj': 'white:74255'
                    ]
            ],
            handlerGrayRules           : [
                    'testAddHandler': [
                            'default'      : 'white:74255|78057',
                            'udobj'        : 'white:78057',
                            'SalesOrderObj': 'white:74255'
                    ]
            ],
            managementGrayRules        : [
                    'Add': [
                            'udobj': 'white:74255'
                    ]
            ],
            globalTransactionGrayRules : [
                    'Delete': [
                            'udobj': 'white:74255'
                    ]
            ],
            preMatchApprovalGrayRules  : [
                    'Approve': [
                            'udobj': 'white:74255'
                    ]
            ],
            handlerConfigSystemGrayRule: 'white:74255'
    ]

    def setup() {
        // 在每个测试方法执行前重新初始化配置
        initializeConfig()
    }

    def cleanup() {
        // 清理静态字段
        Whitebox.setInternalState(HandlerGrayConfig.class, "interfaceGrayRules", null)
        Whitebox.setInternalState(HandlerGrayConfig.class, "handlerGrayRules", null)
        Whitebox.setInternalState(HandlerGrayConfig.class, "managementGrayRules", null)
        Whitebox.setInternalState(HandlerGrayConfig.class, "globalTransactionGrayRules", null)
        Whitebox.setInternalState(HandlerGrayConfig.class, "preMatchApprovalGrayRules", null)
        Whitebox.setInternalState(HandlerGrayConfig.class, "handlerConfigSystemGrayRule", null)
    }

    private void initializeConfig() {
        try {
            // 解析配置数据并设置到静态字段
            def parsedInterfaceGrayRules = UdobjGrayConfig.parseGrayRules(configData.interfaceGrayRules)
            def parsedHandlerGrayRules = UdobjGrayConfig.parseGrayRules(configData.handlerGrayRules)
            def parsedManagementGrayRules = UdobjGrayConfig.parseGrayRules(configData.managementGrayRules)
            def parsedGlobalTransactionGrayRules = UdobjGrayConfig.parseGrayRules(configData.globalTransactionGrayRules)
            def parsedPreMatchApprovalGrayRules = UdobjGrayConfig.parseGrayRules(configData.preMatchApprovalGrayRules)
            def handlerConfigSystemGrayRule = new GrayRule(configData.handlerConfigSystemGrayRule)

            Whitebox.setInternalState(HandlerGrayConfig.class, "interfaceGrayRules", parsedInterfaceGrayRules)
            Whitebox.setInternalState(HandlerGrayConfig.class, "handlerGrayRules", parsedHandlerGrayRules)
            Whitebox.setInternalState(HandlerGrayConfig.class, "managementGrayRules", parsedManagementGrayRules)
            Whitebox.setInternalState(HandlerGrayConfig.class, "globalTransactionGrayRules", parsedGlobalTransactionGrayRules)
            Whitebox.setInternalState(HandlerGrayConfig.class, "preMatchApprovalGrayRules", parsedPreMatchApprovalGrayRules)
            Whitebox.setInternalState(HandlerGrayConfig.class, "handlerConfigSystemGrayRule", handlerConfigSystemGrayRule)
        } catch (Exception e) {
            // 如果初始化失败，设置默认值避免NullPointerException
            Whitebox.setInternalState(HandlerGrayConfig.class, "interfaceGrayRules", [:])
            Whitebox.setInternalState(HandlerGrayConfig.class, "handlerGrayRules", [:])
            Whitebox.setInternalState(HandlerGrayConfig.class, "managementGrayRules", [:])
            Whitebox.setInternalState(HandlerGrayConfig.class, "globalTransactionGrayRules", [:])
            Whitebox.setInternalState(HandlerGrayConfig.class, "preMatchApprovalGrayRules", [:])
            Whitebox.setInternalState(HandlerGrayConfig.class, "handlerConfigSystemGrayRule", new GrayRule("deny"))
            println "Failed to initialize config, using defaults: ${e.message}"
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试配置加载是否正常
     */
    def "testConfigLoad"() {
        when:
        def result = HandlerGrayConfig.supportHandler("74255", "Add", "udobj")

        then:
        noExceptionThrown()
        result == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试supportHandler方法在不同场景下的灰度规则判断
     */
    @Unroll
    def "supportHandlerTest"() {
        expect:
        HandlerGrayConfig.supportHandler(tenantId, interfaceCode, objectApiName) == expected

        where:
        tenantId | interfaceCode | objectApiName   | expected
        '74255'  | 'Add'         | 'udobj'         | true
        '78057'  | 'Add'         | 'udobj'         | true
        '74255'  | 'Add'         | 'SalesOrderObj' | true
        '78057'  | 'Add'         | 'SalesOrderObj' | false
        '99999'  | 'Add'         | 'udobj'         | false
        '74255'  | 'Delete'      | 'udobj'         | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isHandlerInWhiteList方法在不同场景下的白名单判断
     * 注意：isHandlerInWhiteList方法supportDefault=true，会回退到default规则
     */
    @Unroll
    def "isHandlerInWhiteListTest"() {
        expect:
        HandlerGrayConfig.isHandlerInWhiteList(tenantId, handlerApiName, objectApiName) == expected

        where:
        tenantId | handlerApiName   | objectApiName   | expected
        '74255'  | 'testAddHandler' | 'udobj'         | false  // 匹配udobj规则：white:78057，74255不在白名单
        '78057'  | 'testAddHandler' | 'udobj'         | true   // 匹配udobj规则：white:78057
        '74255'  | 'testAddHandler' | 'SalesOrderObj' | true   // 匹配SalesOrderObj规则：white:74255
        '78057'  | 'testAddHandler' | 'SalesOrderObj' | false  // 匹配SalesOrderObj规则：white:74255，78057不在白名单
        '99999'  | 'testAddHandler' | 'udobj'         | false  // udobj规则不匹配，回退到default也不匹配
        '74255'  | 'unknownHandler' | 'udobj'         | false  // 找不到handler规则
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试supportManagement方法在不同场景下的管理权限判断
     * 注意：supportManagement需要同时满足supportHandler和managementGrayRules
     */
    @Unroll
    def "supportManagementTest"() {
        expect:
        HandlerGrayConfig.supportManagement(tenantId, interfaceCode, objectApiName) == expected

        where:
        tenantId | interfaceCode | objectApiName | expected
        '74255'  | 'Add'         | 'udobj'       | true   // supportHandler=true && managementGrayRules匹配
        '78057'  | 'Add'         | 'udobj'       | false  // supportHandler=true但managementGrayRules不匹配
        '99999'  | 'Add'         | 'udobj'       | false  // supportHandler=false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试supportGlobalTransaction方法在不同场景下的全局事务支持判断
     */
    @Unroll
    def "supportGlobalTransactionTest"() {
        expect:
        HandlerGrayConfig.supportGlobalTransaction(tenantId, interfaceCode, objectApiName) == expected

        where:
        tenantId | interfaceCode | objectApiName | expected
        '74255'  | 'Delete'      | 'udobj'       | true
        '78057'  | 'Delete'      | 'udobj'       | false
        '99999'  | 'Delete'      | 'udobj'       | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试supportPreMatchApproval方法在不同场景下的预匹配审批支持判断
     */
    @Unroll
    def "supportPreMatchApprovalTest"() {
        expect:
        HandlerGrayConfig.supportPreMatchApproval(tenantId, interfaceCode, objectApiName) == expected

        where:
        tenantId | interfaceCode | objectApiName | expected
        '74255'  | 'Approve'     | 'udobj'       | true
        '78057'  | 'Approve'     | 'udobj'       | false
        '99999'  | 'Approve'     | 'udobj'       | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isHandlerConfigSystemGrayRule方法在不同场景下的系统灰度规则判断
     */
    @Unroll
    def "isHandlerConfigSystemGrayRuleTest"() {
        expect:
        HandlerGrayConfig.isHandlerConfigSystemGrayRule(tenantId) == expected

        where:
        tenantId | expected
        '74255'  | true
        '78057'  | false
        '99999'  | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getSystemTenantId方法在不同场景下的系统租户ID获取
     */
    @Unroll
    def "getSystemTenantIdTest"() {
        expect:
        HandlerGrayConfig.getSystemTenantId(tenantId) == expected

        where:
        tenantId | expected
        '74255'  | '-101'
        '78057'  | '-100'
        '99999'  | '-100'
    }
} 