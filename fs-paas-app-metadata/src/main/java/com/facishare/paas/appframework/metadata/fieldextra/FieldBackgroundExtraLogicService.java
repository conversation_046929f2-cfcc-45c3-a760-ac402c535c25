package com.facishare.paas.appframework.metadata.fieldextra;

import com.facishare.paas.appframework.core.model.User;

import java.util.List;
import java.util.Map;

public interface FieldBackgroundExtraLogicService {

    List<Map<String, Object>> findFieldsExtra(User user, String describeApiName);

    List<Map<String, Object>> findFieldsExtraByFieldApiNames(User user, String describeApiName, List<String> fieldApiNames);

    List<Map<String, Object>> findOptionRemarksByField(User user, String describeApiName, String fieldApiName);

    void upsertOptionRemark(User user, String describeApiName, String fieldApiName, List<Map<String, Object>> optionRemarks);

    void bulkUpsertFieldsExtra(User user, String describeApiName, List<Map<String, Object>> fieldsExtraList);
}
