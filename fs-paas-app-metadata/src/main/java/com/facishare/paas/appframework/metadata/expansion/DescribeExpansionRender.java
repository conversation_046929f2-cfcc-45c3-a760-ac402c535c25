package com.facishare.paas.appframework.metadata.expansion;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.metadata.options.SelectFieldDependenceLogicService;
import com.facishare.paas.appframework.metadata.options.bo.SelectFieldDependence;
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectFieldExtra;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.appframework.metadata.MtExchangeRate.FUNCTIONAL_CURRENCY;

/**
 * Created by zhaoju on 2022/2/21
 */
public class DescribeExpansionRender {

    private boolean fillParentLookup;
    private boolean fillCascadeDetailLookup;
    /**
     * 一些拓展属性
     */
    private boolean addExtProperty;
    /**
     * 替换邮箱字段的 pattern
     */
    private boolean handleEmailFields;
    /**
     * 水印
     */
    private boolean processWaterMarkField;
    /**
     * 查找关联、主从字段增加是否支持显示字段
     */
    private boolean addDisplayNameSwitch;
    /**
     * 计算普通字段和统计字段、计算字段的依赖关系
     */
    private boolean computeCalculateRelation;

    /**
     * 引用字段的选项值
     */
    private boolean fillQuoteFieldOption;

    /**
     * true 开启描述缓存、不能直接修改描述
     */
    @Builder.Default
    private boolean describeCacheable = true;

    private boolean addTreeViewSwitch;

    private boolean fieldDependence;

    private boolean fillBigObjectFlag;
    private boolean fillOptionalFeatures;

    private IObjectData objectData;

    private boolean maskEncryptFields;

    private User user;

    private DescribeLogicService describeLogicService;

    private FieldRelationCalculateService fieldRelationCalculateService;

    private SelectFieldDependenceLogicService selectFieldDependenceLogicService;

    private OptionalFeaturesService optionalFeaturesService;

    private MaskFieldLogicService maskFieldLogicService;

    private MultiCurrencyLogicService multiCurrencyLogicService;

    @Builder
    public DescribeExpansionRender(Boolean fillParentLookup, Boolean fillCascadeDetailLookup,
                                   Boolean addExtProperty, Boolean handleEmailFields, Boolean processWaterMarkField, Boolean addDisplayNameSwitch,
                                   Boolean addTreeViewSwitch, Boolean computeCalculateRelation, Boolean fillQuoteFieldOption,
                                   Boolean describeCacheable, Boolean fieldDependence, Boolean fillBigObjectFlag, Boolean fillOptionalFeatures, User user, RenderType renderType,
                                   DescribeLogicService describeLogicService, FieldRelationCalculateService fieldRelationCalculateService,
                                   SelectFieldDependenceLogicService selectFieldDependenceLogicService, OptionalFeaturesService optionalFeaturesService,
                                   IObjectData objectData, Boolean maskEncryptFields, MaskFieldLogicService maskFieldLogicService, MultiCurrencyLogicService multiCurrencyLogicService) {
        this.fillParentLookup = null2Empty(fillParentLookup, () -> renderType == RenderType.DescribeLayout);
        this.fillCascadeDetailLookup = null2Empty(fillCascadeDetailLookup, () -> renderType == RenderType.DescribeLayout);
        this.addExtProperty = null2Empty(addExtProperty, () -> renderType == RenderType.DescribeLayout);
        this.handleEmailFields = null2Empty(handleEmailFields, () -> renderType == RenderType.DescribeLayout);
        this.processWaterMarkField = null2Empty(processWaterMarkField, () -> renderType == RenderType.DescribeLayout);
        this.addDisplayNameSwitch = null2Empty(addDisplayNameSwitch, () -> true);
        this.addTreeViewSwitch = null2Empty(addTreeViewSwitch, () -> true);
        this.computeCalculateRelation = null2Empty(computeCalculateRelation, () -> renderType == RenderType.DescribeLayout);
        this.fillQuoteFieldOption = null2Empty(fillQuoteFieldOption, () -> renderType == RenderType.DescribeLayout || renderType == RenderType.Detail);
        this.fieldDependence = null2Empty(fieldDependence, () -> renderType == RenderType.DescribeLayout || renderType == RenderType.Detail);
        this.fillBigObjectFlag = !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DESCRIBE_EXPANSION_FILL_BIG_OBJECT_FLAG_GRAY, user.getTenantId())
                || null2Empty(fillBigObjectFlag, () -> renderType == RenderType.Designer);
        this.fillOptionalFeatures = null2Empty(fillOptionalFeatures, () -> true);
        this.describeCacheable = describeCacheable;
        this.user = user;
        this.describeLogicService = describeLogicService;
        this.fieldRelationCalculateService = fieldRelationCalculateService;
        this.selectFieldDependenceLogicService = selectFieldDependenceLogicService;
        this.optionalFeaturesService = optionalFeaturesService;
        this.maskEncryptFields = null2Empty(maskEncryptFields, () -> renderType == RenderType.DescribeLayout);
        this.objectData = objectData;
        this.maskFieldLogicService = maskFieldLogicService;
        this.multiCurrencyLogicService = multiCurrencyLogicService;
    }

    private boolean null2Empty(Boolean bool, Supplier<Boolean> supplier) {
        return Optional.ofNullable(bool).orElseGet(supplier);
    }

    public DescribeExtra render(IObjectDescribe describe, List<IObjectDescribe> detailDescribes) {
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "#render");
        Map<String, IObjectDescribe> describeExtMap;
        try {
            describeExtMap = getDescribeExtMap(Maps.newHashMap());
            fillExpansion(describe, describeExtMap);
            stopWatch.lap("fillExpansion");
            fillMaskEncryptFields(describe, detailDescribes, describeExtMap);
            stopWatch.lap("fillMaskEncryptFields");
            if (computeCalculateRelation) {
                List<IObjectDescribe> detailDescribesCreateWithMaster = detailDescribes.stream()
                        .filter(x -> ObjectDescribeExt.of(x).isCreateWithMaster())
                        .collect(Collectors.toList());
                fieldRelationCalculateService.computeCalculateRelationWithExt(describe, detailDescribesCreateWithMaster, describeExtMap);
                stopWatch.lap("computeCalculateRelation");
            }
            if (fillCascadeDetailLookup) {
                ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
                describeExt.fillCascadeDetailLookupWithExt(describeExtMap);
                describeExt.fillCascadeDetailLookupWithExt(detailDescribes, describeExtMap);
                stopWatch.lap("fillCascadeDetailLookup");
            }
            if (CollectionUtils.notEmpty(detailDescribes)) {
                detailDescribes.forEach(objectDescribe -> fillExpansion(objectDescribe, describeExtMap));
                stopWatch.lap("fillDetailExpansion");
            }
            return DescribeExtra.of(describe.getApiName(), describeExtMap);
        } finally {
            stopWatch.logSlow(500);
        }
    }

    public DescribeExtra render(List<IObjectDescribe> relatedObjectDescribes) {
        Map<String, IObjectDescribe> describeExtMap = getDescribeExtMap(Maps.newHashMap());

        if (CollectionUtils.notEmpty(relatedObjectDescribes)) {
            relatedObjectDescribes.forEach(objectDescribe -> fillExpansion(objectDescribe, describeExtMap));
        }
        return DescribeExtra.of(null, describeExtMap);
    }

    private void fillMaskEncryptFields(IObjectDescribe describe, List<IObjectDescribe> detailDescribes, Map<String, IObjectDescribe> describeExtMap) {
        if (!AppFrameworkConfig.maskFieldEncryptGray(user.getTenantId(), describe.getApiName())) {
            return;
        }
        if (!maskEncryptFields) {
            return;
        }

        Map<String, IObjectDescribe> describeMap = detailDescribes.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, Function.identity()));
        describeMap.put(describe.getApiName(), describe);
        String ownerId = Optional.ofNullable(objectData)
                .map(ObjectDataExt::of)
                .flatMap(x -> x.getOwnerOrOutOwnerIdOptional(user))
                .orElse(null);

        // 灰度的企业，在新建编辑页面，所有字段都不支持掩码加密
        Map<String, List<IFieldDescribe>> maskFieldMap = Maps.newHashMap();
        if (!AppFrameworkConfig.maskFieldEncryptObjectPagesGray(user.getTenantId(), describe.getApiName())) {
            Map<String, List<IFieldDescribe>> map = maskFieldLogicService.getMaskFields(user, Lists.newArrayList(describeMap.values()), ownerId);
            maskFieldMap.putAll(map);
        }

        describeMap.forEach((objectApiName, objectDescribe) -> {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
            List<IFieldDescribe> maskFields = describeExt.getMaskFields();
            if (CollectionUtils.empty(maskFields)) {
                return;
            }
            Set<String> maskFieldNames = CollectionUtils.nullToEmpty(maskFieldMap.get(objectApiName)).stream()
                    .map(IFieldDescribe::getApiName)
                    .collect(Collectors.toSet());
            for (IFieldDescribe maskField : maskFields) {
                FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(FieldDescribeExt.of(maskField).copy());
                boolean maskFieldEncrypt = maskFieldNames.contains(maskField.getApiName());
                fieldDescribeExt.setMaskFieldEncrypt(maskFieldEncrypt);
                if (maskFieldEncrypt) {
                    maskFieldLogicService.encodeDefaultValueByFieldDescribe(user, fieldDescribeExt.getFieldDescribe());
                }
                mergeField(describeExt, describeExtMap, fieldDescribeExt);
            }
        });
    }

    private void fillRecordTypeField(IObjectDescribe describe, Map<String, IObjectDescribe> describeExtMap) {
        ObjectDescribeExt.of(describe).getRecordTypeField().ifPresent(recordTypeField -> {
            if (describeExtMap == null) {
                ObjectDescribeExt.of(describe).fillValueToRecordType();
                return;
            }
            ObjectDescribeExt.of(describe).copyField4Ext(describeExtMap, recordTypeField);
            //处理业务类型中每个option增加value属性
            describeExtMap.values().forEach(it -> ObjectDescribeExt.of(it).fillValueToRecordType());
        });
    }

    private void processFieldIsIndex(IObjectDescribe describe, Map<String, IObjectDescribe> describeExtMap) {
        if (!user.isOutUser()) {
            return;
        }
        if (describeExtMap == null) {
            ObjectDescribeExt.of(describe).changeIsIndexWithOuter(user);
            return;
        }
        ObjectDescribeExt.of(describe).copy4Ext(describeExtMap);
        describeExtMap.values().forEach(it -> ObjectDescribeExt.of(it).changeIsIndexWithOuter(user));
    }

    private void fillExpansion(IObjectDescribe describe, Map<String, IObjectDescribe> describeExtMap) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "#fillExpansion." + describe.getApiName());

        try {
            fillDescribeExtra(describeExt, describeExtMap);
            stopWatch.lap("fillDescribeExtra");
            if (fillParentLookup) {
                describeExt.fillParentLookupWithExt(describeExtMap);
                stopWatch.lap("fillParentLookup");
            }
            if (addExtProperty) {
                describeExt.addExtPropertyWithExt(describeExtMap);
                stopWatch.lap("addExtProperty");
            }
            if (handleEmailFields) {
                describeExt.handleEmailFieldsWithExt(describeExtMap);
                stopWatch.lap("handleEmailFields");
            }
            if (processWaterMarkField) {
                describeLogicService.processWaterMarkFieldWithExt(describe, user, describeExtMap);
                stopWatch.lap("processWaterMarkField");
            }
            if (addDisplayNameSwitch) {
                addDisplayNameSwitch(describeExt, describeExtMap);
                stopWatch.lap("addDisplayNameSwitch");
            }
            if (addTreeViewSwitch) {
                addTreeViewSwitch(describeExt, describeExtMap);
                stopWatch.lap("addTreeViewSwitch");
            }
            if (fillQuoteFieldOption) {
                fillQuoteFieldOption(describeExt, describeExtMap);
                stopWatch.lap("fillQuoteFieldOption");
            }
            if (fieldDependence) {
                fillFieldDependence(describeExt, describeExtMap);
                stopWatch.lap("fieldDependence");
            }
            if (describeExt.getExpectEmployeeAllocateRuleByGray(user, true)) {
                fillOwnerField(describeExt, describeExtMap);
                stopWatch.lap("fillOwnerField");
            }
            if (fillBigObjectFlag) {
                fillBigObjectFlag(describeExt, describeExtMap);
                stopWatch.lap("fillBigObjectFlag");
            }
            fillMultiLang(describeExt, describeExtMap);
            stopWatch.lap("fillMultiLang");
            fillRecordTypeField(describe, describeExtMap);
            stopWatch.lap("fillRecordTypeField");
            processFieldIsIndex(describe, describeExtMap);
            stopWatch.lap("processFieldIsIndex");
        } finally {
            stopWatch.logSlow(200);
        }
    }

    private void fillBigObjectFlag(ObjectDescribeExt describeExt, Map<String, IObjectDescribe> describeExtMap) {
        List<String> lookupDescribeApiNames = describeExt.getActiveLookupFieldDescribes().stream()
                .map(refFieldDescribe -> ObjectReferenceWrapper.of(refFieldDescribe).getTargetApiName())
                .distinct()
                .collect(Collectors.toList());
        Map<String, IObjectDescribe> lookupDescribes = describeLogicService.findDescribeListWithoutFields(user.getTenantId(), lookupDescribeApiNames)
                .stream().collect(Collectors.toMap(IObjectDescribe::getApiName, Function.identity()));
        describeExt.getActiveLookupFieldDescribes().forEach(lookupFieldDescribe -> {
            String targetApiName = ObjectReferenceWrapper.of(lookupFieldDescribe).getTargetApiName();
            IObjectDescribe lookupDescribe = lookupDescribes.get(targetApiName);
            if (Objects.nonNull(lookupDescribe) && lookupDescribe.isBigObject()) {
                if (describeExtMap == null) {
                    FieldDescribeExt field = FieldDescribeExt.of(lookupFieldDescribe);
                    field.addBigObjectFlag(lookupDescribe.getVisibleScope());
                } else {
                    FieldDescribeExt.of(describeExt.copyField4Ext(describeExtMap, lookupFieldDescribe))
                            .addBigObjectFlag(lookupDescribe.getVisibleScope());
                }
            }
        });

    }

    private void fillOwnerField(ObjectDescribeExt describeExt, Map<String, IObjectDescribe> describeExtMap) {
        describeExt.getFieldDescribeSilently(ObjectDataExt.OWNER)
                .ifPresent(ownerField -> mergeField(describeExt, describeExtMap, FieldDescribeExt.of(ownerField)));
    }

    private void fillMultiLang(ObjectDescribeExt describeExt, Map<String, IObjectDescribe> describeExtMap) {
        Set<IFieldDescribe> multiLangField = describeExt.getEnableMultiLangField();
        if (CollectionUtils.empty(multiLangField) || !AppFrameworkConfig.objectMultiLangGray(describeExt.getTenantId(), describeExt.getApiName())
                || describeExt.isChangeOrderOrOriginalObj()) {
            return;
        }
        List<Lang> langs = Lang.values();
        for (IFieldDescribe fieldDescribe : multiLangField) {
            List<Map<String, String>> values = AppFrameworkConfig.multiLangSupport(langs, describeExt.getApiName(), fieldDescribe.getApiName()).stream()
                    .map(lang -> ImmutableMap.of("code", lang.getValue(), "label", I18NExt.text(I18NExt.getLangI18NKey(lang))))
                    .collect(Collectors.toList());
            if (describeExtMap == null) {
                fieldDescribe.set(FieldDescribeExt.SUPPORT_LANGUAGES, values);
                continue;
            }
            IFieldDescribe field4Ext = describeExt.copyField4Ext(describeExtMap, fieldDescribe);
            field4Ext.set(FieldDescribeExt.SUPPORT_LANGUAGES, values);
            mergeField(describeExt, describeExtMap, FieldDescribeExt.of(field4Ext));
        }
    }

    private void fillFieldDependence(ObjectDescribeExt describeExt, Map<String, IObjectDescribe> describeExtMap) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OPTION_DEPENDENCE_GRAY_EI, user.getTenantId())) {
            return;
        }
        if (!checkHasFieldDependence(describeExt)) {
            return;
        }
        List<SelectFieldDependence> fieldDependenceList = selectFieldDependenceLogicService.findAll(user, describeExt.getApiName());
        if (CollectionUtils.empty(fieldDependenceList)) {
            return;
        }
        IObjectDescribe describe = describeExt.copy();
        for (SelectFieldDependence selectFieldDependence : fieldDependenceList) {
            Map<String, Object> field = selectFieldDependence.fillOption(describe);
            mergeField(describeExt, describeExtMap, FieldDescribeExt.of(field));
        }
    }

    private boolean checkHasFieldDependence(ObjectDescribeExt describeExt) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CHECK_HAS_FIELD_DEPENDENCE_GRAY, user.getTenantId())) {
            return true;
        }
        return describeExt.stream()
                .filter(it -> FieldDescribeExt.of(it).isCascadeChildField())
                .map(it -> (SelectOne) it)
                .anyMatch(it -> StringUtils.isNotBlank(it.getCascadeParentApiName()));
    }

    private Map<String, IObjectDescribe> getDescribeExtMap(Map<String, IObjectDescribe> describeExtMap) {
        return describeCacheable ? describeExtMap : null;
    }

    private void fillDescribeExtra(ObjectDescribeExt describeExt, Map<String, IObjectDescribe> describeExtMap) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DESCRIBE_EXTRA_GRAY_EI, user.getTenantId())) {
            return;
        }
        Map<String, List<IObjectFieldExtra>> describeExtra = describeLogicService.findDescribeExtra(user, Lists.newArrayList(describeExt.getApiName()));
        DescribeExtra extra = DescribeExtra.of(describeExt, describeExtra.getOrDefault(describeExt.getApiName(), Collections.emptyList()));
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(extra.getDescribeExpansion());

        for (IFieldDescribe field : objectDescribeExt.getFieldDescribes()) {
            mergeField(describeExt, describeExtMap, FieldDescribeExt.of(field));
        }
        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = null;
        if (fillOptionalFeatures) {
            optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), describeExt);
        }
        IObjectDescribe describe;
        if (describeExtMap == null) {
            describe = describeExt;
        } else {
            describe = describeExt.copy4Ext(describeExtMap);
            describeExtMap.put(describe.getApiName(), describe);
        }
        //移动端获取不到本币信息，所以需要下发本币 默认币种>个人币种>缓存>本币
        describe.set(FUNCTIONAL_CURRENCY, getFunctionalCurrencyCode().orElse(null));

        if (Objects.isNull(optionalFeaturesSwitch)) {
            return;
        }
        describe.set(OptionalFeaturesService.RELATED_TEAM_SWITCH, optionalFeaturesSwitch.getIsRelatedTeamEnabled());
        describe.set(OptionalFeaturesService.GLOBAL_SEARCH_SWITCH, optionalFeaturesSwitch.getIsGlobalSearchEnabled());
        describe.set(OptionalFeaturesService.FOLLOW_UP_DYNAMIC_SWITCH, optionalFeaturesSwitch.getIsFollowUpDynamicEnabled());
        describe.set(OptionalFeaturesService.MODIFY_RECORD_SWITCH, optionalFeaturesSwitch.getIsModifyRecordEnabled());
        describe.set(OptionalFeaturesService.MULTI_FIELD_SORT, optionalFeaturesSwitch.getMultiFieldSort());
        describe.set(OptionalFeaturesService.CROSS_OBJECT_FILTER_BUTTON, optionalFeaturesSwitch.getCrossObjectFilterButton());
    }

    private Optional<String> getFunctionalCurrencyCode() {
        if (Objects.isNull(multiCurrencyLogicService)) {
            return Optional.empty();
        }
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.GET_FUNCTIONAL_CURRENCY_CODE_LOCAL_CACHE_GRAY, user.getTenantId())) {
            MtCurrency functionalCurrency = multiCurrencyLogicService.findFunctionalCurrency(user);
            return Optional.ofNullable(functionalCurrency).map(MtCurrency::getCurrencyCode);
        }
        return multiCurrencyLogicService.findFunctionalCurrencyCode(user);
    }

    private void fillQuoteFieldOption(ObjectDescribeExt describeExt, Map<String, IObjectDescribe> describeExtMap) {
        Map<String, Map<String, Object>> fieldMap = describeLogicService.fillQuoteFieldOption(describeExt);

        fieldMap.forEach((fieldApiName, field) -> mergeField(describeExt, describeExtMap, FieldDescribeExt.of(field)));
    }

    private void mergeField(ObjectDescribeExt describeExt, Map<String, IObjectDescribe> describeExtMap, FieldDescribeExt fieldDescribeExt) {
        if (describeExtMap == null) {
            IFieldDescribe fieldDescribe = describeExt.getFieldDescribe(fieldDescribeExt.getApiName());
            FieldDescribeExt.of(fieldDescribe).mergeFrom(fieldDescribeExt.copy());
            return;
        }
        IObjectDescribe describe = describeExt.copy4Ext(describeExtMap);
        if (!describe.containsField(fieldDescribeExt.getApiName())) {
            describe.addFieldDescribe(fieldDescribeExt.copy());
        } else {
            IFieldDescribe fieldDescribe = describeExtMap.get(describeExt.getApiName()).getFieldDescribe(fieldDescribeExt.getApiName());
            FieldDescribeExt.of(fieldDescribe).mergeFrom(fieldDescribeExt.copy());
        }
    }

    private void addDisplayNameSwitch(ObjectDescribeExt describeExt, Map<String, IObjectDescribe> describeExtMap) {
        if (!AppFrameworkConfig.isSupportDisplayNameFieldEnterprise(user.getTenantId())) {
            return;
        }
        describeExt.getAllActiveRefFieldDescribesExcludeWhatField().forEach(refFieldDescribe -> {
            String refObjTargetApiName = FieldDescribeExt.of(refFieldDescribe).getRefObjTargetApiName();
            if (!AppFrameworkConfig.isSupportDisplayNameField(refObjTargetApiName)) {
                return;
            }
            IObjectDescribe refDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), refObjTargetApiName);
            ObjectDescribeExt refDescribeExt = ObjectDescribeExt.of(refDescribe);
            if (refDescribeExt.isSupportDisplayName()) {
                if (describeExtMap == null) {
                    FieldDescribeExt field = FieldDescribeExt.of(refFieldDescribe);
                    field.addDisplayNameSwitch(refDescribeExt.isOpenDisplayName());
                } else {
                    FieldDescribeExt.of(describeExt.copyField4Ext(describeExtMap, refFieldDescribe))
                            .addDisplayNameSwitch(refDescribeExt.isOpenDisplayName());
                }
            }
        });
    }

    private void addTreeViewSwitch(ObjectDescribeExt describeExt, Map<String, IObjectDescribe> describeExtMap) {
        describeExt.getActiveLookupFieldDescribes().forEach(refFieldDescribe -> {
            String targetApiName = ObjectReferenceWrapper.of(refFieldDescribe).getTargetApiName();
            if (!AppFrameworkConfig.supportTreeView(user.getTenantId(), targetApiName)) {
                return;
            }
            IObjectDescribe refDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), targetApiName);
            ObjectDescribeExt refDescribeExt = ObjectDescribeExt.of(refDescribe);
            if (refDescribeExt.isSupportTreeViewObject()) {
                if (describeExtMap == null) {
                    FieldDescribeExt field = FieldDescribeExt.of(refFieldDescribe);
                    field.addTreeViewSwitch(refDescribeExt.isSupportTreeView());
                    field.addBigTreeFlag(refDescribeExt.getTreeType());
                } else {
                    FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(describeExt.copyField4Ext(describeExtMap, refFieldDescribe));
                    fieldDescribeExt.addTreeViewSwitch(refDescribeExt.isSupportTreeView());
                    fieldDescribeExt.addBigTreeFlag(refDescribeExt.getTreeType());
                }
            }
        });
    }

    public enum RenderType {
        /**
         * 设计器
         */
        Designer(),
        /**
         * 新建编辑页
         */
        DescribeLayout(),
        /**
         * 详情、列表页
         */
        Detail(),
        ;

        static final Map<String, RenderType> map;

        static {
            map = Stream.of(values()).collect(Collectors.toMap(Enum::toString, Function.identity()));
        }

        public static RenderType of(String name) {
            return map.getOrDefault(name, Detail);
        }
    }
}
