package com.facishare.paas.appframework.rest.dto.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface RefRecord {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        private List<String> tenantIdList;
        private List<String> objApiList;
        private List<String> fieldApiList;
        private List<String> layoutApiList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private List<String> noGrayTenantIdList;
        private List<String> successTenantIdList;
        private List<String> failTenantIdList;
        private String msg;
    }
}
