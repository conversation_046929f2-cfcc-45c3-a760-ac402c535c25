package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribeExtra;
import com.facishare.paas.metadata.api.service.IObjectDescribeExtService;
import com.facishare.paas.metadata.impl.describe.ObjectDescribeExtra;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.facishare.paas.appframework.metadata.ObjectDescribeExt.ICON_SLOT;

@Service("extraDescribeLogicService")
public class ExtraDescribeLogicServiceImpl implements ExtraDescribeLogicService {
    
    private IObjectDescribeExtService objExtService;

    @Autowired
    public void setObjExtService(IObjectDescribeExtService objExtService) {
        this.objExtService = objExtService;
    }


    @Override
    public List<IObjectDescribeExtra> save(String tenantId, IObjectDescribe objDesc, Map<String, Object> objExtra) {
        ObjectDescribeExtraExt extra = ObjectDescribeExtraExt.of(objDesc.getApiName(), objDesc, objExtra);
        IActionContext metadataContext = ActionContextExt.of(tenantId).getContext();
        return objExtService.upsert(metadataContext, Lists.newArrayList(extra.getObjectDescribeExtra()));
    }

    @Override
    public List<IObjectDescribeExtra> save(String tenantId, String objApi, Map<String, Object> objExtra) {
        ObjectDescribeExtra extra = new ObjectDescribeExtra();
        extra.setDescribeApiName(objApi);
        extra.setIconIndex((Integer) objExtra.get(ICON_SLOT));
        IActionContext metadataContext = ActionContextExt.of(tenantId).getContext();
        return objExtService.upsert(metadataContext, Lists.newArrayList(extra));
    }

    @Override
    public Map<String, IObjectDescribeExtra> find(String tenantId, List<String> objApis) {
        IActionContext metadataContext = ActionContextExt.of(tenantId).getContext();
        return objExtService.findDescribeExtByDescribeApiName(objApis, metadataContext);
    }
}
