package com.facishare.paas.appframework.rest.dto.data;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

/**
 * Created by luxin on 2019-02-14.
 */
public interface UserObjectsFuncCodesCheck {
    @Data
    class Arg {
        @NotEmpty(message = "apiNames is empty")
        List<String> apiNames;
        /**
         * ObjectAction.actionCode
         */
        @NotEmpty(message = "actionCode is blank")
        String actionCode;
    }
}
