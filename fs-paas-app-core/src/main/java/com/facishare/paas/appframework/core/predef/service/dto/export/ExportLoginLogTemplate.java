package com.facishare.paas.appframework.core.predef.service.dto.export;

import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.log.dto.LoginLog;
import com.facishare.paas.timezone.DateTimeFormat;
import com.facishare.paas.timezone.DateTimeFormatUtils;
import com.facishare.paas.timezone.TimeZoneContextHolder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@Data
public class ExportLoginLogTemplate {
    //操作人
    String userName;
    //部门
    String deptName;
    //登录终端型号
    String loginBrowser;
    //登录IP
    String loginIp;
    //登录状态
    String loginStatus;
    //登录方式
    String loginType;
    //设备归属人
    String ownerName;
    //异常现象
    String exception;
    //操作时间
    String operationTime;


    //操作人
    public static final String USER_NAME = "userName";
    //部门
    public static final String DEPT_NAME = "deptName";
    //登录终端型号
    public static final String LOGIN_BROWSER = "loginBrowser";
    //登录IP
    public static final String LOGIN_IP = "loginIp";
    //登录状态
    public static final String LOGIN_STATUS = "loginStatus";
    //登录方式
    public static final String LOGIN_TYPE = "loginType";
    //设备归属人
    public static final String OWNER_NAME = "ownerName";
    //异常现象
    public static final String EXCEPTION = "exception";
    //操作时间
    public static final String OPERATION_TIME = "operationTime";

    public static List<ExportDTO.Header> getLoginLogTemplate() {
        List<ExportDTO.Header> loginLogTemplate = Lists.newArrayList();
        Map<String, String> loginLogHeaders = Maps.newLinkedHashMap();
        loginLogHeaders.put(USER_NAME, I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_HEADER_PERSON, "登录人（昵称）"));// ignoreI18n
        loginLogHeaders.put(DEPT_NAME, I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_HEADER_DEPARTMENT, "部门"));// ignoreI18n
        loginLogHeaders.put(LOGIN_BROWSER, I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_HEADER_BROWSER, "登录终端设备"));// ignoreI18n
        loginLogHeaders.put(LOGIN_IP, "IP");
        loginLogHeaders.put(OWNER_NAME, I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_HEADER_OWNER, "设备归属人"));// ignoreI18n
        loginLogHeaders.put(OPERATION_TIME, I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_HEADER_TIME, "登录时间"));// ignoreI18n
        loginLogHeaders.put(LOGIN_TYPE, I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_HEADER_TYPE, "登录方式"));// ignoreI18n
        loginLogHeaders.put(LOGIN_STATUS, I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_HEADER_STATUS, "登录状态"));// ignoreI18n
        loginLogHeaders.put(EXCEPTION, I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_HEADER_EXCEPTION, "异常现象"));// ignoreI18n
        loginLogHeaders.forEach((key, value) -> {
            ExportDTO.Header exportHeader = new ExportDTO.Header();
            exportHeader.setKey(key);
            exportHeader.setName(value);
            loginLogTemplate.add(exportHeader);
        });
        return loginLogTemplate;
    }

    public static ExportLoginLogTemplate convertToLoginLogTemplate(LoginLog.LoginInfo loginInfo) {
        ExportLoginLogTemplate loginTemplate = new ExportLoginLogTemplate();
        loginTemplate.setUserName(loginInfo.getUserName());
        loginTemplate.setLoginIp(loginInfo.getLoginIp());
        loginTemplate.setLoginBrowser(loginInfo.getLoginBrowser());
        loginTemplate.setDeptName(loginInfo.getDeptName());
        loginTemplate.setOwnerName(loginInfo.getOwnerName());
        loginTemplate.setLoginStatus(parseLoginStatus(loginInfo.getLoginStatus()));
        loginTemplate.setLoginType(parseLoginType(loginInfo.getLoginType()));
        loginTemplate.setException(parseException(loginInfo.getException(), loginInfo.getLoginStatus()));
        loginTemplate.setOperationTime(DateTimeFormatUtils.formatWithTimezoneInfo(loginInfo.getOperationTime(),
                TimeZoneContextHolder.getUserTimeZone(), DateTimeFormat.DATE_TIME.getType()));
        return loginTemplate;
    }

    private static String parseException(String exception, String loginStatus) {
        if (StringUtils.equals(loginStatus, "2")) {
            switch (exception) {
                case "1":
                    return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_EXCEPTION_1, "密码错误");// ignoreI18n
                case "2":
                    return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_EXCEPTION_2, "尝试授权失败");// ignoreI18n
                default:
                    return exception;
            }
        }
        return exception;
    }

    private static String parseLoginStatus(String loginStatus) {
        switch (loginStatus) {
            case "1":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_STATUS_1, "登录成功");// ignoreI18n
            case "0":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_STATUS_0, "登录失败");// ignoreI18n
            default:
                return loginStatus;
        }
    }

    private static String parseLoginType(String loginType) {
        switch (loginType) {
            case "0":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_0, "账号密码登录");// ignoreI18n
            case "1":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_1, "扫码登录");// ignoreI18n
            case "2":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_2, "体验账号登录");// ignoreI18n
            case "3":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_3, "手机授权登录");// ignoreI18n
            case "4":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_4, "安全登录");// ignoreI18n
            case "5":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_5, "单点登录");// ignoreI18n
            case "6":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_6, "动态密码登录");// ignoreI18n
            case "7":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_7, "纷享客服登录");// ignoreI18n
            case "8":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_8, "邮箱密码登录");// ignoreI18n
            case "9":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_9, "邮箱验证码登录");// ignoreI18n
            case "10":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_10, "互联手机号密码登录");// ignoreI18n
            case "11":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_11, "互联手机号验证码登录");// ignoreI18n
            case "12":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_12, "互联邮箱密码登录");// ignoreI18n
            case "13":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_13, "互联邮箱验证码登录");// ignoreI18n
            case "14":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_14, "SAML2登录");// ignoreI18n
            case "15":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_15, "oauth2登录");// ignoreI18n
            case "16":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_16, "微信登录");// ignoreI18n
            case "17":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_17, "云之家登录");// ignoreI18n
            case "18":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_18, "钉钉登录");// ignoreI18n
            case "19":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_19, "沙盒登录");// ignoreI18n
            case "20":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_20, "企业帐号登录");// ignoreI18n
            case "21":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_21, "通过切换企业登录");// ignoreI18n
            case "22":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_22, "飞书登录");// ignoreI18n
            case "fxiaoke":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_FXIAOKE, "纷享登录");// ignoreI18n
            case "wechat":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_WECHAT, "微信登录");// ignoreI18n
            case "mini_program":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_MINI_PROGRAM, "小程序登录");// ignoreI18n
            case "third_party":
                return I18NExt.getOrDefault(I18NKey.EXPORT_LOGIN_TYPE_THIRD_PARTY, "第三方登录");// ignoreI18n
            default:
                return loginType;
        }
    }
}
