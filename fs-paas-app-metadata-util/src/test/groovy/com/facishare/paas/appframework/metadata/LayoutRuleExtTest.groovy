package com.facishare.paas.appframework.metadata


import com.facishare.paas.appframework.common.util.Tuple
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.layout.EditLayout
import com.facishare.paas.appframework.metadata.layout.LayoutTypes
import com.facishare.paas.metadata.api.search.IFilter
import com.facishare.paas.metadata.impl.LayoutRuleInfo
import com.facishare.paas.metadata.impl.search.Operator
import spock.lang.Specification
import spock.lang.Unroll

class LayoutRuleExtTest extends Specification {

    def "test of factory method with LayoutRuleInfo"() {
        given: "一个LayoutRuleInfo对象"
        def ruleInfo = Mock(LayoutRuleInfo) {
            getApiName() >> "test_rule"
            getLabel() >> "Test Rule"
        }

        when: "使用of方法创建LayoutRuleExt"
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        then: "应该正确包装规则信息"
        layoutRuleExt != null
        layoutRuleExt.getApiName() == "test_rule"
        layoutRuleExt.getLabel() == "Test Rule"
        layoutRuleExt.getRule() == ruleInfo
    }

    def "test of factory method with Map"() {
        given: "一个包含规则数据的Map"
        def ruleMap = [
                "api_name": "test_rule",
                "label"   : "Test Rule",
                "type"    : "field"
        ]

        when: "使用of方法创建LayoutRuleExt"
        def layoutRuleExt = LayoutRuleExt.of(ruleMap)

        then: "应该正确创建规则"
        layoutRuleExt != null
        layoutRuleExt.getRuleInfo() != null
    }

    def "test getPageBranch"() {
        given: "包含页面分支的规则"
        def pageBranches = [
                "type"          : "hide",
                "hide_field"    : ["field1", "field2"],
                "readonly_field": ["field3"]
        ]
        def ruleInfo = Mock(LayoutRuleInfo) {
            getPageBranches() >> pageBranches
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        when: "获取页面分支"
        def pageBranch = layoutRuleExt.getPageBranch()

        then: "应该返回正确的页面分支"
        pageBranch != null
        pageBranch.getType() == "hide"
        pageBranch.getHideField().contains("field1")
        pageBranch.getHideField().contains("field2")
        pageBranch.getReadonlyField().contains("field3")
    }

    def "test getDefaultEditPageApiName"() {
        given: "一个规则"
        def ruleInfo = Mock(LayoutRuleInfo) {
            getApiName() >> "test_rule"
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        when: "获取默认编辑页面API名"
        def defaultPageApiName = layoutRuleExt.getDefaultEditPageApiName()

        then: "应该返回正确格式的API名"
        defaultPageApiName == LayoutTypes.EDIT + "_test_rule"
    }

    def "test getDefaultEditPageLabel"() {
        given: "一个规则"
        def ruleInfo = Mock(LayoutRuleInfo) {
            getLabel() >> "Test Rule"
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        when: "获取默认编辑页面标签"
        def defaultPageLabel = layoutRuleExt.getDefaultEditPageLabel()

        then: "应该返回正确格式的标签"
        defaultPageLabel.startsWith("Test Rule")
        defaultPageLabel.contains(EditLayout.EDIT_PAGE_LABEL)
    }

    def "test fillSystemInfo"() {
        given: "用户和规则"
        def user = Mock(User) {
            getUserId() >> "user123"
        }
        def ruleInfo = Mock(LayoutRuleInfo) {
            setCreatedBy("user123") >> null
            setLastModifiedBy("user123") >> null
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        when: "填充系统信息"
        layoutRuleExt.fillSystemInfo(user)

        then: "应该设置创建人和修改人"
        1 * ruleInfo.setCreatedBy("user123")
        1 * ruleInfo.setLastModifiedBy("user123")
    }

    def "test enableRule"() {
        given: "一个规则"
        def ruleInfo = Mock(LayoutRuleInfo) {
            setStatus(0) >> null
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        when: "启用规则"
        layoutRuleExt.enableRule()

        then: "应该设置状态为0"
        1 * ruleInfo.setStatus(0)
    }

    def "test diableRule"() {
        given: "一个规则"
        def ruleInfo = Mock(LayoutRuleInfo) {
            setStatus(1) >> null
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        when: "禁用规则"
        layoutRuleExt.diableRule()

        then: "应该设置状态为1"
        1 * ruleInfo.setStatus(1)
    }

    @Unroll
    def "test isPageTypeRule - type: #type, expected: #expected"() {
        given: "不同类型的规则"
        def ruleInfo = Mock(LayoutRuleInfo) {
            getType() >> type
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        expect: "应该正确判断是否为页面类型规则"
        layoutRuleExt.isPageTypeRule() == expected

        where:
        type    | expected
        "page"  | true
        "field" | false
        null    | false
    }

    def "test isEnabled with status 0"() {
        given: "状态为0的规则"
        def ruleInfo = Mock(LayoutRuleInfo) {
            getStatus() >> 0
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        expect: "应该返回true"
        layoutRuleExt.isEnabled() == true
    }

    def "test isEnabled with status 1"() {
        given: "状态为1的规则"
        def ruleInfo = Mock(LayoutRuleInfo) {
            getStatus() >> 1
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        expect: "应该返回false"
        layoutRuleExt.isEnabled() == false
    }

    def "test isEnabled with null status using real object"() {
        given: "使用真实对象创建规则，状态为null"
        def ruleInfo = Mock(LayoutRuleInfo) {
            getStatus() >> status
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        when: "调用isEnabled方法"
        def result = layoutRuleExt.isEnabled()

        then: "应该返回false"
        layoutRuleExt.isEnabled() == expected
        where:
        status || expected
        0      || true
        1      || false
        3      || false
    }

    def "test existCycle with no cycle"() {
        given: "没有循环的规则列表"
        def rule1 = createFieldRule("field1", "field2", [])
        def rule2 = createFieldRule("field2", "field3", [])
        def ruleList = [rule1, rule2]

        when: "检查是否存在循环"
        def hasCycle = LayoutRuleExt.existCycle(ruleList)

        then: "应该返回false"
        hasCycle == false
    }

    def "test existCycle with cycle"() {
        given: "存在循环的规则列表"
        def rule1 = createFieldRule("field1", "field2", [])
        def rule2 = createFieldRule("field2", "field1", [])
        def ruleList = [rule1, rule2]

        when: "检查是否存在循环"
        def hasCycle = LayoutRuleExt.existCycle(ruleList)

        then: "应该返回true"
        hasCycle == true
    }

    def "test existCycle with page type rules"() {
        given: "包含页面类型规则的列表"
        def pageRule = Mock(LayoutRuleInfo) {
            getType() >> "page"
        }
        def fieldRule = createFieldRule("field1", "field2", [])
        def ruleList = [pageRule, fieldRule]

        when: "检查是否存在循环"
        def hasCycle = LayoutRuleExt.existCycle(ruleList)

        then: "应该忽略页面规则"
        hasCycle == false
    }

    def "test existCycle with empty rules"() {
        when: "检查空规则列表"
        def hasCycle = LayoutRuleExt.existCycle([])

        then: "应该返回false"
        hasCycle == false
    }

    def "test getMainBranches"() {
        given: "包含主分支的规则"
        def mainFieldBranches = [
                [
                        "main_field_filter": [
                                "field_name"  : "test_field",
                                "operator"    : "EQ",
                                "field_values": ["value1"]
                        ],
                        "branches"         : []
                ]
        ]
        def ruleInfo = Mock(LayoutRuleInfo) {
            getMainFieldBranches() >> mainFieldBranches
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        when: "获取主分支"
        def mainBranches = layoutRuleExt.getMainBranches()

        then: "应该返回正确的主分支列表"
        mainBranches.size() == 1
        mainBranches[0].getMainFieldFilter().getFieldName() == "test_field"
        mainBranches[0].getMainFieldFilter().getOperator() == "EQ"
    }

    def "test resetMainBranches"() {
        given: "规则和新的主分支"
        def ruleInfo = Mock(LayoutRuleInfo) {
            setMainFieldBranches(_) >> null
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)
        def newBranches = [
                new LayoutRuleExt.FieldBranch(
                        mainFieldFilter: new LayoutRuleExt.Filter(
                                fieldName: "new_field",
                                operator: "IN",
                                fieldValues: ["val1", "val2"]
                        ),
                        branches: []
                )
        ]

        when: "重置主分支"
        layoutRuleExt.resetMainBranches(newBranches)

        then: "应该设置新的主分支"
        1 * ruleInfo.setMainFieldBranches(_)
    }

    def "test getFieldBranches"() {
        given: "包含字段分支的规则"
        def mainFieldBranches = [
                [
                        "main_field_filter": [
                                "field_name"  : "test_field",
                                "operator"    : "EQ",
                                "field_values": ["value1"]
                        ],
                        "branches"         : []
                ]
        ]
        def ruleInfo = Mock(LayoutRuleInfo) {
            getMainFieldBranches() >> mainFieldBranches
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        when: "获取字段分支"
        def fieldBranches = layoutRuleExt.getFieldBranches()

        then: "应该返回IFieldBranch列表"
        fieldBranches.size() == 1
        fieldBranches[0] != null
    }

    def "test getFieldBranches with empty branches"() {
        given: "没有分支的规则"
        def ruleInfo = Mock(LayoutRuleInfo) {
            getMainFieldBranches() >> []
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        when: "获取字段分支"
        def fieldBranches = layoutRuleExt.getFieldBranches()

        then: "应该返回空列表"
        fieldBranches.isEmpty()
    }

    def "test validatePageTypeRuleByLayout with valid rule"() {
        given: "页面类型规则和布局规则列表"
        def ruleInfo = Mock(LayoutRuleInfo) {
            getType() >> "page"
            getPageTriggerMode() >> "create"
            getLayoutApiName() >> "layout1"
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        def otherRule = Mock(LayoutRuleInfo) {
            getPageTriggerMode() >> "edit"
            getLayoutApiName() >> "layout2"
        }

        when: "验证页面类型规则"
        layoutRuleExt.validatePageTypeRuleByLayout([otherRule])

        then: "应该验证通过，不抛异常"
        noExceptionThrown()
    }

    def "test validatePageTypeRuleByLayout with duplicate rule"() {
        given: "重复的页面类型规则"
        def ruleInfo = Mock(LayoutRuleInfo) {
            getType() >> "page"
            getPageTriggerMode() >> "create"
            getLayoutApiName() >> "layout1"
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        def duplicateRule = Mock(LayoutRuleInfo) {
            getPageTriggerMode() >> "create"
            getLayoutApiName() >> "layout1"
        }

        when: "验证重复的页面类型规则"
        layoutRuleExt.validatePageTypeRuleByLayout([duplicateRule])

        then: "应该抛出验证异常"
        thrown(ValidateException)
    }

    def "test validatePageTypeRuleByLayout with non-page rule"() {
        given: "非页面类型规则"
        def ruleInfo = Mock(LayoutRuleInfo) {
            getType() >> "field"
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        when: "验证非页面类型规则"
        layoutRuleExt.validatePageTypeRuleByLayout([])

        then: "应该直接返回，不做验证"
        noExceptionThrown()
    }

    def "test isFieldInRule with main field"() {
        given: "规则"
        def ruleInfo = Mock(LayoutRuleInfo) {
            getMainField() >> "main_field"
            getMainFieldBranches() >> []
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        expect: "应该正确判断字段是否在规则中"
        layoutRuleExt.isFieldInRule("main_field") == true
        layoutRuleExt.isFieldInRule("other_field") == false
    }

    def "test isFieldInRule with field in branches"() {
        given: "包含分支的规则"
        def mainFieldBranches = [
                [
                        "main_field_filter": [
                                "field_name"  : "trigger_field",
                                "operator"    : "EQ",
                                "field_values": ["value1"]
                        ],
                        "branches"         : [
                                [
                                        "conditions": [
                                                [
                                                        "field_name"  : "condition_field",
                                                        "operator"    : "NE",
                                                        "field_values": ["value2"]
                                                ]
                                        ],
                                        "result"    : [
                                                "show_field"    : [
                                                        ["field_api_name": "show_field"]
                                                ],
                                                "required_field": [
                                                        ["field_api_name": "required_field"]
                                                ],
                                                "readonly_field": [
                                                        ["field_api_name": "readonly_field"]
                                                ]
                                        ]
                                ]
                        ]
                ]
        ]
        def ruleInfo = Mock(LayoutRuleInfo) {
            getMainField() >> "main_field"
            getMainFieldBranches() >> mainFieldBranches
        }
        def layoutRuleExt = LayoutRuleExt.of(ruleInfo)

        expect: "应该正确判断字段是否在分支中"
        layoutRuleExt.isFieldInRule("trigger_field") == true
        layoutRuleExt.isFieldInRule("condition_field") == true
        layoutRuleExt.isFieldInRule("show_field") == true
        layoutRuleExt.isFieldInRule("required_field") == true
        layoutRuleExt.isFieldInRule("readonly_field") == true
        layoutRuleExt.isFieldInRule("not_exist_field") == false
    }

    def "test Filter fromIFilter"() {
        given: "一个IFilter对象"
        def iFilter = Mock(IFilter) {
            getFieldName() >> "test_field"
            getOperator() >> Operator.EQ
            getValueType() >> 1
            getFieldValues() >> ["value1", "value2"]
        }

        when: "从IFilter创建Filter"
        def filter = LayoutRuleExt.Filter.fromIFilter(iFilter)

        then: "应该正确转换"
        filter.getFieldName() == "test_field"
        filter.getOperator() == "EQ"
        filter.getValueType() == 1
        filter.getFieldValues() == ["value1", "value2"]
    }

    def "test Filter toIFilter"() {
        given: "一个Filter对象"
        def filter = new LayoutRuleExt.Filter(
                fieldName: "test_field",
                operator: "EQ",
                valueType: 1,
                fieldValues: ["value1", "value2"]
        )

        when: "转换为IFilter"
        def iFilter = filter.toIFilter()

        then: "应该正确转换并返回IFilter对象"
        iFilter != null
        // 验证转换后的字段名
        iFilter.getFieldName() == "test_field"
        iFilter.getOperator() == Operator.EQ
        iFilter.getFieldValues() == ["value1", "value2"]
    }

    def "test FieldConfig fromMap"() {
        given: "包含字段配置的Map"
        def configMap = [
                "required": true,
                "width"   : 100
        ]

        when: "从Map创建FieldConfig"
        def fieldConfig = LayoutRuleExt.FieldConfig.fromMap(configMap)

        then: "应该正确解析配置"
        fieldConfig.getTuple().getKey() == "required"
        fieldConfig.getTuple().getValue() == true
        fieldConfig.getWidth() == 100
    }

    def "test FieldConfig fromMap with multiple boolean values"() {
        given: "包含多个布尔值的Map"
        def configMap = [
                "required": true,
                "readonly": false,
                "width"   : 150
        ]

        when: "从Map创建FieldConfig"
        def fieldConfig = LayoutRuleExt.FieldConfig.fromMap(configMap)

        then: "应该解析第一个布尔值"
        fieldConfig.getTuple() != null
        fieldConfig.getWidth() == 150
        // 注意：由于Map的迭代顺序不确定，这里只验证基本结构
    }

    def "test FieldConfig fieldConfigToMap"() {
        given: "一个FieldConfig对象"
        def tuple = Tuple.of("required", true)
        def fieldConfig = LayoutRuleExt.FieldConfig.of(tuple, 120)

        when: "转换为Map"
        def documentEntity = fieldConfig.fieldConfigToMap()

        then: "应该包含正确的配置"
        documentEntity.get("required") == true
        documentEntity.get("width") == 120
    }

    def "test FieldConfig of static method"() {
        given: "字段配置参数"
        def tuple = Tuple.of("readonly", false)
        def width = 200

        when: "使用of方法创建FieldConfig"
        def fieldConfig = LayoutRuleExt.FieldConfig.of(tuple, width)

        then: "应该正确创建配置"
        fieldConfig.getTuple() == tuple
        fieldConfig.getWidth() == width
    }

    private LayoutRuleInfo createFieldRule(String mainField, String showField, List<String> conditions) {
        def mainFieldBranches = [
                [
                        "main_field_filter": [
                                "field_name"  : mainField,
                                "operator"    : "EQ",
                                "field_values": ["value1"]
                        ],
                        "branches"         : [
                                [
                                        "conditions": conditions.collect { fieldName ->
                                            [
                                                    "field_name"  : fieldName,
                                                    "operator"    : "EQ",
                                                    "field_values": ["value"]
                                            ]
                                        },
                                        "result"    : [
                                                "show_field": [
                                                        ["field_api_name": showField]
                                                ]
                                        ]
                                ]
                        ]
                ]
        ]

        return Mock(LayoutRuleInfo) {
            getType() >> "field"
            getMainFieldBranches() >> mainFieldBranches
        }
    }

    def "test complex rule structure"() {
        given: "复杂的规则结构"
        def ruleData = [
                "api_name"           : "complex_rule",
                "label"              : "Complex Rule",
                "type"               : "field",
                "main_field"         : "status__c",
                "main_field_branches": [
                        [
                                "main_field_filter": [
                                        "field_name"  : "status__c",
                                        "operator"    : "EQ",
                                        "field_values": ["active"]
                                ],
                                "branches"         : [
                                        [
                                                "conditions": [
                                                        [
                                                                "field_name"  : "priority__c",
                                                                "operator"    : "IN",
                                                                "field_values": ["high", "urgent"]
                                                        ]
                                                ],
                                                "result"    : [
                                                        "show_field"    : [
                                                                ["field_api_name": "escalation_reason__c"]
                                                        ],
                                                        "required_field": [
                                                                ["field_api_name": "manager_approval__c"]
                                                        ],
                                                        "readonly_field": [
                                                                ["field_api_name": "creation_date__c"]
                                                        ]
                                                ]
                                        ]
                                ]
                        ]
                ]
        ]

        when: "创建复杂规则"
        def layoutRuleExt = LayoutRuleExt.of(ruleData)

        then: "应该正确处理复杂结构"
        layoutRuleExt != null

        def mainBranches = layoutRuleExt.getMainBranches()
        mainBranches.size() == 1

        def firstBranch = mainBranches[0]
        firstBranch.getMainFieldFilter().getFieldName() == "status__c"
        firstBranch.getBranches().size() == 1

        def branch = firstBranch.getBranches()[0]
        branch.getConditions().size() == 1
        branch.getConditions()[0].getFieldName() == "priority__c"

        def result = branch.getResult()
        result.getShowFieldList().size() == 1
        result.getRequiredFieldList().size() == 1
        result.getReadonlyFieldList().size() == 1
    }

    def "test nested field detection"() {
        given: "包含嵌套字段的规则"
        def layoutRuleExt = LayoutRuleExt.of([
                "main_field"         : "parent_field",
                "main_field_branches": [
                        [
                                "main_field_filter": [
                                        "field_name"  : "trigger_field",
                                        "operator"    : "EQ",
                                        "field_values": ["value"]
                                ],
                                "branches"         : [
                                        [
                                                "conditions": [
                                                        [
                                                                "field_name"  : "condition_field",
                                                                "operator"    : "NE",
                                                                "field_values": ["other"]
                                                        ]
                                                ],
                                                "result"    : [
                                                        "show_field": [
                                                                ["field_api_name": "result_field"]
                                                        ]
                                                ]
                                        ]
                                ]
                        ]
                ]
        ])

        expect: "应该正确检测所有相关字段"
        layoutRuleExt.isFieldInRule("parent_field") == true
        layoutRuleExt.isFieldInRule("trigger_field") == true
        layoutRuleExt.isFieldInRule("condition_field") == true
        layoutRuleExt.isFieldInRule("result_field") == true
        layoutRuleExt.isFieldInRule("unrelated_field") == false
    }
}