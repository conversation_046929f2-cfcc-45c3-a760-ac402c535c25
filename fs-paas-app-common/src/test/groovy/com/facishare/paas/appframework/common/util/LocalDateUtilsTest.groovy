package com.facishare.paas.appframework.common.util

import spock.lang.Specification
import spock.lang.Unroll

import java.time.DayOfWeek
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId

class LocalDateUtilsTest extends Specification {

    def "test transDateByFormat"() {
        when:
        def r = LocalDateUtils.transDateByFormat(dateFormat, time as Long);
        println(r)
        then:
        result == r
        where:
        time            | dateFormat            || result
        "1657769141774" | "yyyy"                || "2022"
        "1657769141774" | "yyyy-QQQ"            || "2022-Q3"
        "1657769141774" | "yyyy-MM"             || "2022-07"
        "1657769141774" | "yyyy-MM-dd"          || "2022-07-14"
        "1657769141774" | "yyyy-QQQ"            || "2022-Q3"
        "1662466288909" | "yyyy-MM-dd HH:mm:ss" || "2022-09-06 20:11:28"
        null            | "yyyy-MM-dd HH:mm:ss" || null
        "1662466288909" | "yyyy-MM-dd HH:mm"    || "2022-09-06 20:11"
        "1662466288909" | "HH:mm"               || "20:11"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试convertTimeMillisToDate方法将时间戳转换为LocalDate
     */
    def "convertTimeMillisToDate test"() {
        when:
        def result = LocalDateUtils.convertTimeMillisToDate(1657769141774L)
        
        then:
        result.year == 2022
        result.monthValue == 7
        result.dayOfMonth == 14
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试convertTimeMillisToDateTime方法将时间戳转换为LocalDateTime
     */
    def "convertTimeMillisToDateTime test"() {
        when:
        def result = LocalDateUtils.convertTimeMillisToDateTime(1657769141774L)
        
        then:
        result.year == 2022
        result.monthValue == 7
        result.dayOfMonth == 14
        result.hour >= 0 // 由于时区问题，不进行精确断言
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试convertDateToTimeMillis方法将LocalDate转换为时间戳
     */
    def "convertDateToTimeMillis test"() {
        given:
        def date = LocalDate.of(2022, 7, 14)
        
        when:
        def result = LocalDateUtils.convertDateToTimeMillis(date)
        
        then:
        // 转换回LocalDate，验证日期相同
        LocalDateUtils.convertTimeMillisToDate(result) == date
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试convertDateTimeToTimeMillis方法将LocalDateTime转换为时间戳
     */
    def "convertDateTimeToTimeMillis test"() {
        given:
        def dateTime = LocalDateTime.of(2022, 7, 14, 10, 30, 15)
        
        when:
        def result = LocalDateUtils.convertDateTimeToTimeMillis(dateTime)
        
        then:
        // 转换回LocalDateTime，验证日期和时间相同
        def convertedDateTime = LocalDateUtils.convertTimeMillisToDateTime(result)
        convertedDateTime.year == dateTime.year
        convertedDateTime.monthValue == dateTime.monthValue
        convertedDateTime.dayOfMonth == dateTime.dayOfMonth
        convertedDateTime.hour == dateTime.hour
        convertedDateTime.minute == dateTime.minute
        convertedDateTime.second == dateTime.second
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试truncateWithDay方法将时间戳截断到天
     */
    @Unroll
    def "truncateWithDay test with #type"() {
        when:
        def result = LocalDateUtils.truncateWithDay(input)
        
        then:
        // 转换为LocalDateTime，验证时分秒都为0
        def dateTime = Instant.ofEpochMilli(result)
            .atZone(ZoneId.systemDefault())
            .toLocalDateTime()
        dateTime.hour == 0
        dateTime.minute == 0
        dateTime.second == 0
        dateTime.nano == 0
        
        where:
        type      | input
        "Long"    | 1657769141774L
        "String"  | "1657769141774"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试plusOneDay方法将时间戳加一天
     */
    def "plusOneDay test"() {
        given:
        def date = LocalDate.of(2022, 7, 14)
        def timestamp = LocalDateUtils.convertDateToTimeMillis(date)
        
        when:
        def result = LocalDateUtils.plusOneDay(timestamp)
        
        then:
        def resultDate = LocalDateUtils.convertTimeMillisToDate(result)
        resultDate == date.plusDays(1)
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getDateListBetweenDates方法获取两个日期之间的所有日期列表
     */
    def "getDateListBetweenDates test"() {
        given:
        def startDate = LocalDate.of(2022, 7, 14)
        def endDate = LocalDate.of(2022, 7, 17)
        
        when:
        def result = LocalDateUtils.getDateListBetweenDates(startDate, endDate)
        
        then:
        result.size() == 4
        result[0] == LocalDate.of(2022, 7, 14)
        result[1] == LocalDate.of(2022, 7, 15)
        result[2] == LocalDate.of(2022, 7, 16)
        result[3] == LocalDate.of(2022, 7, 17)
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getDateListBetweenDates方法在开始日期大于结束日期时的边界情况
     */
    def "getDateListBetweenDates edge case test"() {
        given:
        def startDate = LocalDate.of(2022, 7, 17)
        def endDate = LocalDate.of(2022, 7, 14)
        
        when:
        def result = LocalDateUtils.getDateListBetweenDates(startDate, endDate)
        
        then:
        result.isEmpty()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getDateListBetweenTimeMillis方法获取两个时间戳之间的所有日期列表
     */
    def "getDateListBetweenTimeMillis test"() {
        given:
        def startDate = LocalDate.of(2022, 7, 14)
        def endDate = LocalDate.of(2022, 7, 17)
        def startTimestamp = LocalDateUtils.convertDateToTimeMillis(startDate)
        def endTimestamp = LocalDateUtils.convertDateToTimeMillis(endDate)
        
        when:
        def result = LocalDateUtils.getDateListBetweenTimeMillis(startTimestamp, endTimestamp)
        
        then:
        result.size() == 4
        result[0] == LocalDate.of(2022, 7, 14)
        result[1] == LocalDate.of(2022, 7, 15)
        result[2] == LocalDate.of(2022, 7, 16)
        result[3] == LocalDate.of(2022, 7, 17)
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getDateListOfThisWeek方法获取本周所有日期
     */
    def "getDateListOfThisWeek test"() {
        when:
        def result = LocalDateUtils.getDateListOfThisWeek()
        
        then:
        result.size() == 7
        result[0].dayOfWeek == DayOfWeek.MONDAY
        result[6].dayOfWeek == DayOfWeek.SUNDAY
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getDateListOfLastWeek方法获取上周所有日期
     */
    def "getDateListOfLastWeek test"() {
        when:
        def result = LocalDateUtils.getDateListOfLastWeek()
        
        then:
        result.size() == 7
        result[0].dayOfWeek == DayOfWeek.MONDAY
        result[6].dayOfWeek == DayOfWeek.SUNDAY
        
        and: "上周的周一应该是本周周一减去7天"
        def thisWeekMonday = LocalDate.now().with(DayOfWeek.MONDAY)
        result[0] == thisWeekMonday.minusWeeks(1)
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getDateListOfThisMonth方法获取本月所有日期
     */
    def "getDateListOfThisMonth test"() {
        when:
        def result = LocalDateUtils.getDateListOfThisMonth()
        def now = LocalDate.now()
        
        then:
        result.size() > 0
        result[0].dayOfMonth == 1
        result[0].month == now.month
        result[0].year == now.year
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getDateListOfLastMonth方法获取上月所有日期
     */
    def "getDateListOfLastMonth test"() {
        when:
        def result = LocalDateUtils.getDateListOfLastMonth()
        def lastMonth = LocalDate.now().minusMonths(1)
        
        then:
        result.size() > 0
        result[0].dayOfMonth == 1
        result[0].month == lastMonth.month
        result[0].year == lastMonth.year
    }
}
