package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.TrueOrFalse;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

/**
 * Created by linqiuying on 17/6/9.
 */
@Slf4j
public class TrueOrFalseDataConverter extends AbstractFieldDataConverter {
    @Override
    public String convertFieldData(SessionContext sessionContext) {
        Boolean value = null;
        try {
            value = getObjectData().get(getFieldDescribe().getApiName(), Boolean.class);
        } catch (Exception e) {
            log.warn("Can not covert to Boolean", e);
        }

        if (value == null) {
            return "";
        }

        //获取布尔字段的label
        Optional<ISelectOption> option = ((TrueOrFalse) getFieldDescribe()).getOption(value);
        if (option.isPresent()) {
            return option.get().getLabel();
        }

        return value ? I18N.text(I18NKey.YES) : I18N.text(I18NKey.NO);
    }
}
