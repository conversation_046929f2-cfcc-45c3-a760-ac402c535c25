package com.facishare.paas.appframework.metadata.component;

import com.facishare.paas.metadata.ui.layout.ICustomComponent;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class QueryComponentListTest {

  /**
   * GenerateByAI
   * 测试内容描述：测试QueryComponentList.Arg的基本属性设置和获取
   */
  @Test
  @DisplayName("正常场景 - 测试Arg类的基本属性")
  void testArgBasicProperties() {
    // 创建测试数据
    String targetScope = "ObjectDetailPage";
    String objectApiName = "TestObj";
    
    // 使用Builder模式创建Arg对象
    QueryComponentList.Arg arg = QueryComponentList.Arg.builder()
        .targetScope(targetScope)
        .objectApiName(objectApiName)
        .build();
    
    // 验证属性
    assertNotNull(arg, "Arg对象不应为空");
    assertEquals(targetScope, arg.getTargetScope(), "目标范围应该正确设置");
    assertEquals(objectApiName, arg.getObjectApiName(), "对象API名称应该正确设置");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试QueryComponentList.Arg的null值处理
   */
  @Test
  @DisplayName("边界场景 - 测试Arg类的null值处理")
  void testArgNullValues() {
    // 创建包含null的Arg对象
    QueryComponentList.Arg arg = QueryComponentList.Arg.builder()
        .targetScope(null)
        .objectApiName(null)
        .build();
    
    // 验证null值
    assertNull(arg.getTargetScope(), "目标范围可以为null");
    assertNull(arg.getObjectApiName(), "对象API名称可以为null");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试QueryComponentList.Arg的空字符串处理
   */
  @Test
  @DisplayName("边界场景 - 测试Arg类的空字符串处理")
  void testArgEmptyStrings() {
    // 创建包含空字符串的Arg对象
    QueryComponentList.Arg arg = QueryComponentList.Arg.builder()
        .targetScope("")
        .objectApiName("")
        .build();
    
    // 验证空字符串
    assertEquals("", arg.getTargetScope(), "目标范围可以为空字符串");
    assertEquals("", arg.getObjectApiName(), "对象API名称可以为空字符串");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试QueryComponentList.Arg的Builder模式
   */
  @Test
  @DisplayName("正常场景 - 测试Arg类的Builder模式")
  void testArgBuilderPattern() {
    // 测试Builder模式的链式调用
    QueryComponentList.Arg arg = QueryComponentList.Arg.builder()
        .targetScope("ObjectEditPage")
        .objectApiName("EditObj")
        .build();
    
    // 验证Builder构建的对象
    assertNotNull(arg, "Builder构建的对象不应为空");
    assertEquals("ObjectEditPage", arg.getTargetScope(), "Builder设置的目标范围应正确");
    assertEquals("EditObj", arg.getObjectApiName(), "Builder设置的对象API名称应正确");
    
    // 测试无参Builder
    QueryComponentList.Arg emptyArg = QueryComponentList.Arg.builder().build();
    assertNotNull(emptyArg, "无参Builder构建的对象不应为空");
    assertNull(emptyArg.getTargetScope(), "无参Builder构建的对象目标范围应为null");
    assertNull(emptyArg.getObjectApiName(), "无参Builder构建的对象API名称应为null");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试QueryComponentList.Arg的equals和hashCode方法
   */
  @Test
  @DisplayName("正常场景 - 测试Arg类的equals和hashCode方法")
  void testArgEqualsAndHashCode() {
    // 创建两个相同的Arg对象
    QueryComponentList.Arg arg1 = QueryComponentList.Arg.builder()
        .targetScope("ObjectDetailPage")
        .objectApiName("TestObj")
        .build();
    
    QueryComponentList.Arg arg2 = QueryComponentList.Arg.builder()
        .targetScope("ObjectDetailPage")
        .objectApiName("TestObj")
        .build();
    
    // 验证equals
    assertEquals(arg1, arg2, "相同属性的Arg对象应该相等");
    assertEquals(arg1.hashCode(), arg2.hashCode(), "相同对象的hashCode应该相等");
    
    // 创建不同的Arg对象
    QueryComponentList.Arg arg3 = QueryComponentList.Arg.builder()
        .targetScope("ObjectEditPage")
        .objectApiName("TestObj")
        .build();
    
    assertNotEquals(arg1, arg3, "不同属性的Arg对象应该不相等");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试QueryComponentList.Result类的基本功能
   */
  @Test
  @DisplayName("正常场景 - 测试Result类的基本功能")
  void testResultBasicFunctionality() {
    // 创建Result对象
    QueryComponentList.Result result = new QueryComponentList.Result();
    
    // 验证Result是ComponentResult的子类
    assertTrue(result instanceof ComponentResult, "Result应该是ComponentResult的子类");
    
    // 测试继承的方法
    result.setCode(0);
    result.setMessage("success");
    
    assertEquals(0, result.getCode(), "状态码应该正确设置");
    assertEquals("success", result.getMessage(), "消息应该正确设置");
    assertTrue(result.isSuccess(), "状态码为0时应该返回成功");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试QueryComponentList.Result类的数据转换功能
   */
  @Test
  @DisplayName("正常场景 - 测试Result类的数据转换功能")
  void testResultDataConversion() {
    // 创建Result对象并设置数据
    QueryComponentList.Result result = new QueryComponentList.Result();
    
    ComponentData data = new ComponentData();
    data.setApiName("query_component");
    data.setName("查询组件");
    data.setUrl("http://query.com");
    
    result.setData(Lists.newArrayList(data));
    
    // 测试继承的toCustomComponents方法
    List<ICustomComponent> customComponents = result.toCustomComponents();
    assertNotNull(customComponents, "转换结果不应为空");
    assertEquals(1, customComponents.size(), "应该转换出1个组件");
    assertEquals("query_component", customComponents.get(0).getName(), "组件名称应正确");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试QueryComponentList.TargetScope枚举的所有值
   */
  @Test
  @DisplayName("正常场景 - 测试TargetScope枚举的所有值")
  void testTargetScopeEnum() {
    // 验证所有枚举值存在
    assertNotNull(QueryComponentList.TargetScope.ObjectDetailPage, "ObjectDetailPage枚举值应存在");
    assertNotNull(QueryComponentList.TargetScope.ObjectEditPage, "ObjectEditPage枚举值应存在");
    assertNotNull(QueryComponentList.TargetScope.ObjectListPage, "ObjectListPage枚举值应存在");
    assertNotNull(QueryComponentList.TargetScope.BigObjectDetailPage, "BigObjectDetailPage枚举值应存在");
    assertNotNull(QueryComponentList.TargetScope.BigObjectEditPage, "BigObjectEditPage枚举值应存在");
    assertNotNull(QueryComponentList.TargetScope.BigObjectListPage, "BigObjectListPage枚举值应存在");
    assertNotNull(QueryComponentList.TargetScope.PortalPage, "PortalPage枚举值应存在");
    
    // 验证枚举值的名称
    assertEquals("ObjectDetailPage", QueryComponentList.TargetScope.ObjectDetailPage.name());
    assertEquals("ObjectEditPage", QueryComponentList.TargetScope.ObjectEditPage.name());
    assertEquals("ObjectListPage", QueryComponentList.TargetScope.ObjectListPage.name());
    assertEquals("BigObjectDetailPage", QueryComponentList.TargetScope.BigObjectDetailPage.name());
    assertEquals("BigObjectEditPage", QueryComponentList.TargetScope.BigObjectEditPage.name());
    assertEquals("BigObjectListPage", QueryComponentList.TargetScope.BigObjectListPage.name());
    assertEquals("PortalPage", QueryComponentList.TargetScope.PortalPage.name());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试QueryComponentList.TargetScope枚举的values方法
   */
  @Test
  @DisplayName("正常场景 - 测试TargetScope枚举的values方法")
  void testTargetScopeValues() {
    QueryComponentList.TargetScope[] values = QueryComponentList.TargetScope.values();
    
    // 验证枚举值数量
    assertEquals(7, values.length, "TargetScope枚举应有7个值");
    
    // 验证包含所有预期的枚举值
    boolean hasObjectDetailPage = false;
    boolean hasObjectEditPage = false;
    boolean hasObjectListPage = false;
    boolean hasBigObjectDetailPage = false;
    boolean hasBigObjectEditPage = false;
    boolean hasBigObjectListPage = false;
    boolean hasPortalPage = false;
    
    for (QueryComponentList.TargetScope scope : values) {
      switch (scope) {
        case ObjectDetailPage:
          hasObjectDetailPage = true;
          break;
        case ObjectEditPage:
          hasObjectEditPage = true;
          break;
        case ObjectListPage:
          hasObjectListPage = true;
          break;
        case BigObjectDetailPage:
          hasBigObjectDetailPage = true;
          break;
        case BigObjectEditPage:
          hasBigObjectEditPage = true;
          break;
        case BigObjectListPage:
          hasBigObjectListPage = true;
          break;
        case PortalPage:
          hasPortalPage = true;
          break;
      }
    }
    
    assertTrue(hasObjectDetailPage, "应包含ObjectDetailPage");
    assertTrue(hasObjectEditPage, "应包含ObjectEditPage");
    assertTrue(hasObjectListPage, "应包含ObjectListPage");
    assertTrue(hasBigObjectDetailPage, "应包含BigObjectDetailPage");
    assertTrue(hasBigObjectEditPage, "应包含BigObjectEditPage");
    assertTrue(hasBigObjectListPage, "应包含BigObjectListPage");
    assertTrue(hasPortalPage, "应包含PortalPage");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试QueryComponentList.Arg的toString方法
   */
  @Test
  @DisplayName("正常场景 - 测试Arg类的toString方法")
  void testArgToString() {
    QueryComponentList.Arg arg = QueryComponentList.Arg.builder()
        .targetScope("ObjectDetailPage")
        .objectApiName("TestObj")
        .build();
    
    String toString = arg.toString();
    
    // 验证toString包含属性信息
    assertNotNull(toString, "toString不应为空");
    assertTrue(toString.contains("ObjectDetailPage") || toString.contains("targetScope"), 
               "toString应包含目标范围信息");
    assertTrue(toString.contains("TestObj") || toString.contains("objectApiName"), 
               "toString应包含对象API名称信息");
  }
}
