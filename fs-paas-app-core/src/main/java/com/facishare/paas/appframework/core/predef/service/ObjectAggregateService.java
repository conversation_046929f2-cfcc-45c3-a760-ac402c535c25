package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.aggregate.GetAggregateValues;
import com.facishare.paas.appframework.metadata.AggregateRule;
import com.facishare.paas.appframework.metadata.AggregateValueLogicService;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@ServiceModule("aggregate")
public class ObjectAggregateService {

    @Autowired
    private AggregateValueLogicService aggregateValueLogicService;
    @Autowired
    private DescribeLogicService describeLogicService;

    @ServiceMethod("getAggregateValues")
    public GetAggregateValues.Result getAggregateValues(GetAggregateValues.Arg arg, ServiceContext context) {
        List<AggregateRule> ruleList = aggregateValueLogicService.findAggregateRuleByIds(context.getTenantId(), arg.getAggregateRuleIds());
        Set<String> objectApiNames = ruleList.stream().map(AggregateRule::getAggregateObject).collect(Collectors.toSet());
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjectsWithoutCopy(context.getTenantId(), objectApiNames);
        ObjectDataDocument aggregateValues = new ObjectDataDocument();
        ruleList.forEach(rule -> {
            List<Long> dateRange = rule.getDateRangeWithTimeMillis();
            String dimension = getDimensionValue(arg, rule);
            IObjectDescribe describe = describeMap.get(rule.getAggregateObject());
            IFieldDescribe fieldDescribe = Objects.isNull(describe) || Strings.isNullOrEmpty(rule.getAggregateField()) ? null :
                    describe.getFieldDescribe(rule.getAggregateField());
            int scale = Objects.isNull(fieldDescribe) ? 2 : FieldDescribeExt.of(fieldDescribe).getDecimalPlaces();
            RoundingMode roundingMode = Objects.isNull(fieldDescribe) ? RoundingMode.HALF_UP : FieldDescribeExt.of(fieldDescribe).getRoundingMode();
            BigDecimal aggregateValue = aggregateValueLogicService.getAggregateValue(context.getTenantId(), rule.getId(),
                    dimension, dateRange.get(0), dateRange.get(dateRange.size() - 1), rule.getAggregateWay(),
                    scale, roundingMode);
            String strValue;
            if (Objects.isNull(aggregateValue)) {
                strValue = rule.isMaxOrMinWay() ? null : "0";
            } else {
                strValue = aggregateValue.toPlainString();
            }
            aggregateValues.put(rule.getId(), strValue);
        });
        return GetAggregateValues.Result.builder().aggregateValues(aggregateValues).build();
    }

    private String getDimensionValue(GetAggregateValues.Arg arg, AggregateRule rule) {
        String[] fieldNames = rule.parseDimension();
        String fieldName = fieldNames.length > 1 ? fieldNames[1] : fieldNames[0];
        return (String) arg.getData().get(fieldName);
    }

}
