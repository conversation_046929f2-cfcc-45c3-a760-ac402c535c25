package com.facishare.paas.appframework.metadata.dto.sfa;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by zhaopx on 2017/11/21.
 */
public interface ModifyTeamMember {
    @Data
    @Builder
    class Arg {
        @SerializedName("ObjectType")
        @JSONField(name = "ObjectType")
        private Integer object_type;

        @SerializedName("DataID")
        @JSONField(name = "DataID")
        private String object_id;

        @SerializedName("TeamMemberInfos")
        @JSONField(name = "TeamMemberInfos")
        private List<TeamMemberInfoPojo> teamMemberInfos;
    }

    @Getter
    @Setter
    class Result extends BaseResult {

    }

    @Data
    class TeamMemberInfoPojo {
        @SerializedName("EmployeeID")
        @JSONField(name = "EmployeeID")
        private Integer employeeID;

        @SerializedName("PermissionType")
        @JSONField(name = "PermissionType")
        private Integer permissionType;

        @SerializedName("TeamMemberTypes")
        @JSONField(name = "TeamMemberTypes")
        private List<Integer> teamMemberTypeList;
    }
}
