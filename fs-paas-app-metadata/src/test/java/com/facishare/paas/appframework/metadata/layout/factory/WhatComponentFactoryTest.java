package com.facishare.paas.appframework.metadata.layout.factory;

import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.TableColumnExt;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.WhatComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class WhatComponentFactoryTest {

    @Mock
    private ILayoutFactory.Context context;

    @Mock
    private IObjectDescribe describe;

    @Mock
    private IObjectDescribe whatDescribe;

    private WhatComponentFactory whatComponentFactory;

    @BeforeEach
    void setUp() {
        whatComponentFactory = new WhatComponentFactory();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试支持的组件类型返回正确值
     */
    @Test
    @DisplayName("正常场景 - 支持的组件类型")
    void testSupportComponentType_ReturnsCorrectType() {
        // When
        String result = whatComponentFactory.supportComponentType();

        // Then
        assertEquals(IComponent.TYPE_WHAT, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建默认组件的正常场景
     */
    @Test
    @DisplayName("正常场景 - 创建默认What组件")
    void testCreateDefaultComponent_NormalCase() {
        // Given
        when(context.getWhatDescribe()).thenReturn(whatDescribe);
        
        ObjectDescribe objectDescribe = new ObjectDescribe();
        objectDescribe.setApiName("test_object");
        
        ObjectDescribe whatObjectDescribe = new ObjectDescribe();
        whatObjectDescribe.setApiName("what_object");

        List<ITableColumn> mockColumns = Lists.newArrayList();

        try (MockedStatic<ObjectDescribeExt> objectDescribeExtMock = mockStatic(ObjectDescribeExt.class);
             MockedStatic<TableColumnExt> tableColumnExtMock = mockStatic(TableColumnExt.class)) {

            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            ObjectDescribeExt mockWhatDescribeExt = mock(ObjectDescribeExt.class);

            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(describe)).thenReturn(mockDescribeExt);
            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(whatDescribe)).thenReturn(mockWhatDescribeExt);
            tableColumnExtMock.when(() -> TableColumnExt.buildWhatListDefaultColumns(mockDescribeExt, mockWhatDescribeExt))
                    .thenReturn(mockColumns);

            // When
            IComponent result = whatComponentFactory.createDefaultComponent(context, describe);

            // Then
            assertNotNull(result);
            assertTrue(result instanceof WhatComponent);
            WhatComponent whatComponent = (WhatComponent) result;
            assertEquals("what_component", whatComponent.getName());
            assertEquals(mockColumns, whatComponent.getIncludeFields());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建默认组件时context为null的情况
     */
    @Test
    @DisplayName("异常场景 - context为null")
    void testCreateDefaultComponent_NullContext() {
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            whatComponentFactory.createDefaultComponent(null, describe);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建默认组件时describe为null的情况
     */
    @Test
    @DisplayName("异常场景 - describe为null")
    void testCreateDefaultComponent_NullDescribe() {
        // Given
        when(context.getWhatDescribe()).thenReturn(whatDescribe);

        try (MockedStatic<ObjectDescribeExt> objectDescribeExtMock = mockStatic(ObjectDescribeExt.class)) {
            // When & Then
            assertThrows(NullPointerException.class, () -> {
                whatComponentFactory.createDefaultComponent(context, null);
            });
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建默认组件时whatDescribe为null的情况
     */
    @Test
    @DisplayName("异常场景 - whatDescribe为null")
    void testCreateDefaultComponent_NullWhatDescribe() {
        // Given
        when(context.getWhatDescribe()).thenReturn(null);

        try (MockedStatic<ObjectDescribeExt> objectDescribeExtMock = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(describe)).thenReturn(mockDescribeExt);

            // When & Then
            assertThrows(NullPointerException.class, () -> {
                whatComponentFactory.createDefaultComponent(context, describe);
            });
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建默认组件时TableColumnExt返回空列表
     */
    @Test
    @DisplayName("边界场景 - TableColumnExt返回空列表")
    void testCreateDefaultComponent_EmptyColumns() {
        // Given
        when(context.getWhatDescribe()).thenReturn(whatDescribe);
        List<ITableColumn> emptyColumns = Lists.newArrayList();

        try (MockedStatic<ObjectDescribeExt> objectDescribeExtMock = mockStatic(ObjectDescribeExt.class);
             MockedStatic<TableColumnExt> tableColumnExtMock = mockStatic(TableColumnExt.class)) {

            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            ObjectDescribeExt mockWhatDescribeExt = mock(ObjectDescribeExt.class);

            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(describe)).thenReturn(mockDescribeExt);
            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(whatDescribe)).thenReturn(mockWhatDescribeExt);
            tableColumnExtMock.when(() -> TableColumnExt.buildWhatListDefaultColumns(mockDescribeExt, mockWhatDescribeExt))
                    .thenReturn(emptyColumns);

            // When
            IComponent result = whatComponentFactory.createDefaultComponent(context, describe);

            // Then
            assertNotNull(result);
            assertTrue(result instanceof WhatComponent);
            WhatComponent whatComponent = (WhatComponent) result;
            assertEquals("what_component", whatComponent.getName());
            assertEquals(emptyColumns, whatComponent.getIncludeFields());
            assertTrue(whatComponent.getIncludeFields().isEmpty());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建默认组件时TableColumnExt返回null
     */
    @Test
    @DisplayName("边界场景 - TableColumnExt返回null")
    void testCreateDefaultComponent_NullColumns() {
        // Given
        when(context.getWhatDescribe()).thenReturn(whatDescribe);

        try (MockedStatic<ObjectDescribeExt> objectDescribeExtMock = mockStatic(ObjectDescribeExt.class);
             MockedStatic<TableColumnExt> tableColumnExtMock = mockStatic(TableColumnExt.class)) {

            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            ObjectDescribeExt mockWhatDescribeExt = mock(ObjectDescribeExt.class);

            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(describe)).thenReturn(mockDescribeExt);
            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(whatDescribe)).thenReturn(mockWhatDescribeExt);
            tableColumnExtMock.when(() -> TableColumnExt.buildWhatListDefaultColumns(mockDescribeExt, mockWhatDescribeExt))
                    .thenReturn(null);

            // When
            IComponent result = whatComponentFactory.createDefaultComponent(context, describe);

            // Then
            assertNotNull(result);
            assertTrue(result instanceof WhatComponent);
            WhatComponent whatComponent = (WhatComponent) result;
            assertEquals("what_component", whatComponent.getName());
            assertNull(whatComponent.getIncludeFields());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试实现IComponentFactory接口
     */
    @Test
    @DisplayName("正常场景 - 验证实现IComponentFactory接口")
    void testImplementsIComponentFactory() {
        // Then
        assertTrue(whatComponentFactory instanceof IComponentFactory);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试组件名称的一致性
     */
    @Test
    @DisplayName("正常场景 - 验证组件名称一致性")
    void testComponentNameConsistency() {
        // Given
        when(context.getWhatDescribe()).thenReturn(whatDescribe);

        try (MockedStatic<ObjectDescribeExt> objectDescribeExtMock = mockStatic(ObjectDescribeExt.class);
             MockedStatic<TableColumnExt> tableColumnExtMock = mockStatic(TableColumnExt.class)) {

            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            ObjectDescribeExt mockWhatDescribeExt = mock(ObjectDescribeExt.class);

            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(describe)).thenReturn(mockDescribeExt);
            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(whatDescribe)).thenReturn(mockWhatDescribeExt);
            tableColumnExtMock.when(() -> TableColumnExt.buildWhatListDefaultColumns(mockDescribeExt, mockWhatDescribeExt))
                    .thenReturn(Lists.newArrayList());

            // When
            IComponent result1 = whatComponentFactory.createDefaultComponent(context, describe);
            IComponent result2 = whatComponentFactory.createDefaultComponent(context, describe);

            // Then
            assertEquals(result1.getName(), result2.getName());
            assertEquals("what_component", result1.getName());
            assertEquals("what_component", result2.getName());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试基本功能验证
     */
    @Test
    @DisplayName("正常场景 - 基本功能验证")
    void testBasicFunctionality() {
        // Then
        assertNotNull(whatComponentFactory);
        assertDoesNotThrow(() -> {
            whatComponentFactory.toString();
        });
        assertDoesNotThrow(() -> {
            whatComponentFactory.hashCode();
        });
        assertDoesNotThrow(() -> {
            whatComponentFactory.equals(whatComponentFactory);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试支持的组件类型常量值
     */
    @Test
    @DisplayName("正常场景 - 验证支持的组件类型常量")
    void testSupportComponentTypeConstant() {
        // When
        String supportedType = whatComponentFactory.supportComponentType();

        // Then
        assertNotNull(supportedType);
        assertFalse(supportedType.isEmpty());
        assertEquals(IComponent.TYPE_WHAT, supportedType);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建的组件类型正确性
     */
    @Test
    @DisplayName("正常场景 - 验证创建的组件类型")
    void testCreatedComponentType() {
        // Given
        when(context.getWhatDescribe()).thenReturn(whatDescribe);

        try (MockedStatic<ObjectDescribeExt> objectDescribeExtMock = mockStatic(ObjectDescribeExt.class);
             MockedStatic<TableColumnExt> tableColumnExtMock = mockStatic(TableColumnExt.class)) {

            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            ObjectDescribeExt mockWhatDescribeExt = mock(ObjectDescribeExt.class);

            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(describe)).thenReturn(mockDescribeExt);
            objectDescribeExtMock.when(() -> ObjectDescribeExt.of(whatDescribe)).thenReturn(mockWhatDescribeExt);
            tableColumnExtMock.when(() -> TableColumnExt.buildWhatListDefaultColumns(mockDescribeExt, mockWhatDescribeExt))
                    .thenReturn(Lists.newArrayList());

            // When
            IComponent result = whatComponentFactory.createDefaultComponent(context, describe);

            // Then
            assertNotNull(result);
            assertTrue(result instanceof WhatComponent);
            assertEquals(IComponent.TYPE_WHAT, result.getType());
        }
    }
}
