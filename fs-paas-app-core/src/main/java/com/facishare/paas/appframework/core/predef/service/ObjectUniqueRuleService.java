package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ObjectFieldDescribeDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.uniqueRule.FieldDescribeList;
import com.facishare.paas.appframework.core.predef.service.dto.uniqueRule.GetSetting;
import com.facishare.paas.appframework.core.predef.service.dto.uniqueRule.SaveUniqueRule;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.UniqueRuleLogicService;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IUniqueRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@ServiceModule("unique_rule")
@Service
@Slf4j
public class ObjectUniqueRuleService {

    @Autowired
    private IUniqueRuleService uniqueRuleService;
    @Autowired
    private UniqueRuleLogicService uniqueRuleLogicService;
    @Autowired
    private DescribeLogicService describeLogicService;

    @ServiceMethod("save")
    public SaveUniqueRule.Result save(SaveUniqueRule.Arg arg, ServiceContext context) {
        IObjectDescribe objectDescribe = describeLogicService.findObject(context.getTenantId(), arg.getUniqueRule().getDescribeApiName());
        //过滤禁用字段
        arg.getUniqueRule().getPendingRules().getRules().forEach(x ->
                x.setConditions(x.getConditions().stream().filter(y ->
                        Objects.nonNull(objectDescribe.getFieldDescribe(y.getFieldName())) && objectDescribe.getFieldDescribe(y.getFieldName()).isActive())
                        .collect(Collectors.toList())));
        IUniqueRule uniqueRule = uniqueRuleLogicService.createOrUpdate(context.getTenantId(), arg.getUniqueRule());
        return SaveUniqueRule.Result.builder().uniqueRule(uniqueRule).build();
    }

    @ServiceMethod("field_describe_list")
    public FieldDescribeList.Result fieldDescribeList(FieldDescribeList.Arg arg, ServiceContext context) {
        IObjectDescribe objectDescribe = describeLogicService.findObject(context.getTenantId(), arg.getApiName());
        List<IFieldDescribe> fieldDescribes = uniqueRuleLogicService.filterField(objectDescribe);
        return FieldDescribeList.Result.builder()
                .fieldDescribes(fieldDescribes.stream()
                        .map(ObjectFieldDescribeDocument::of)
                        .collect(Collectors.toList()))
                .build();
    }

    @ServiceMethod("get_setting")
    public GetSetting.Result getSetting(GetSetting.Arg arg, ServiceContext context) {
        IUniqueRule uniqueRule = uniqueRuleLogicService.findByDescribeApiName(context.getTenantId(), arg.getApiName());
        return GetSetting.Result.builder().uniqueRule(uniqueRule).build();
    }

}
