package com.facishare.paas.appframework.metadata.ai;

import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.metadata.dto.ChatComplete;
import com.facishare.paas.appframework.metadata.dto.ai.AIDto;
import com.facishare.paas.appframework.metadata.dto.ai.AIProxy;
import com.facishare.paas.appframework.metadata.dto.ai.DataQuery;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.List;

/**
 * <AUTHOR> create by liy on 2024/5/13
 */
@RestResource(
        value = "AI_REST_PROXY",
        desc = "ai服务", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.metadata.util.CRMRestServiceCodec")
public interface AiRestServiceProxy {

    @POST(value = "/v1/openai/chatComplete", desc = "AI对话")
    ChatComplete.Result chatComplete(@HeaderParam("x-fs-ei") String tenantId,
                                     @HeaderParam("x-fs-userinfo") String employeeId,
                                     @HeaderParam("x-fs-business-name") String bizName,
                                     @Body ChatComplete.Arg arg);

    @POST(value = "/v1/translatorWorkstation/translate", desc = "AI翻译")
    ChatComplete.TranslateResult chatTranslate(@HeaderParam("x-fs-ei") String tenantId,
                                               @HeaderParam("x-fs-userinfo") String employeeId,
                                               @HeaderParam("x-fs-business-name") String bizName,
                                               @Body ChatComplete.AITransDTO arg);

    @POST(value = "/v1/openai/chatComplete", desc = "AI对话", socketReadTimeoutSecond = 120)
    ChatComplete.Result chatCompleteLongTime(@HeaderParam("x-fs-ei") String tenantId,
                                             @HeaderParam("x-fs-userinfo") String employeeId,
                                             @HeaderParam("x-fs-business-name") String bizName,
                                             @Body AIProxy.ChatArg arg);

    @POST(value = "/v1/inner/queryFields", desc = "计算关联度TOP N的字段描述")
    AIDto.Ret<List<JSONObject>> queryFields(@HeaderParam("x-fs-ei") String tenantId,
                                            @HeaderParam("x-fs-userinfo") String employeeId,
                                            @HeaderParam("x-fs-business-name") String bizName,
                                            @Body AIProxy.TopNArg arg);

    @POST(value = "/v1/action/n2sql/object/execute", desc = "自然语言转SearchQuery", codec = "com.facishare.paas.appframework.common.service.codec.AppDefaultCodeC")
    DataQuery.Result generateSearchQuery(@HeaderParam("x-fs-ei") String tenantId,
                                         @HeaderParam("x-fs-userinfo") String employeeId,
                                         @Body DataQuery.Arg arg);
}
