package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试BatchEmployeeDataConverter类的批量员工字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class BatchEmployeeDataConverterTest {

  @Mock
  private OrgService orgService;

  @Mock
  private User user;

  @InjectMocks
  private BatchEmployeeDataConverter converter;

  private IFieldDescribe employeeFieldDescribe;

  @BeforeEach
  void setUp() {
    employeeFieldDescribe = createEmployeeFieldDescribe("employee_field", IFieldType.EMPLOYEE);
    
    // 设置mock行为
    lenient().when(user.getTenantId()).thenReturn("tenant123");
    lenient().when(user.getUserId()).thenReturn("currentUser");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法正常获取用户名映射
   */
  @Test
  @DisplayName("测试getIdNameMap - 正常获取用户名映射")
  void testGetIdNameMap_NormalUserNameMapping() {
    // 准备测试数据
    List<String> userIds = Arrays.asList("user1", "user2", "user3");
    Map<String, String> expectedMap = new HashMap<>();
    expectedMap.put("user1", "张三");
    expectedMap.put("user2", "李四");
    expectedMap.put("user3", "王五");
    
    when(orgService.getUserNameMapByIds("tenant123", "currentUser", userIds))
        .thenReturn(expectedMap);

    // 执行被测试方法
    Map<String, String> result = converter.getIdNameMap(userIds, employeeFieldDescribe, user);

    // 验证结果
    assertNotNull(result);
    assertEquals(3, result.size());
    assertEquals("张三", result.get("user1"));
    assertEquals("李四", result.get("user2"));
    assertEquals("王五", result.get("user3"));
    
    verify(orgService).getUserNameMapByIds("tenant123", "currentUser", userIds);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法处理空列表
   */
  @Test
  @DisplayName("测试getIdNameMap - 处理空列表")
  void testGetIdNameMap_EmptyList() {
    // 准备测试数据
    List<String> userIds = Collections.emptyList();
    Map<String, String> expectedMap = new HashMap<>();
    
    when(orgService.getUserNameMapByIds("tenant123", "currentUser", userIds))
        .thenReturn(expectedMap);

    // 执行被测试方法
    Map<String, String> result = converter.getIdNameMap(userIds, employeeFieldDescribe, user);

    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());
    
    verify(orgService).getUserNameMapByIds("tenant123", "currentUser", userIds);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法处理部分用户不存在的情况
   */
  @Test
  @DisplayName("测试getIdNameMap - 部分用户不存在")
  void testGetIdNameMap_PartialUsersNotFound() {
    // 准备测试数据
    List<String> userIds = Arrays.asList("user1", "user2", "user3");
    Map<String, String> expectedMap = new HashMap<>();
    expectedMap.put("user1", "张三");
    expectedMap.put("user3", "王五");
    // user2不存在于返回的map中
    
    when(orgService.getUserNameMapByIds("tenant123", "currentUser", userIds))
        .thenReturn(expectedMap);

    // 执行被测试方法
    Map<String, String> result = converter.getIdNameMap(userIds, employeeFieldDescribe, user);

    // 验证结果
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals("张三", result.get("user1"));
    assertEquals("王五", result.get("user3"));
    assertNull(result.get("user2"));
    
    verify(orgService).getUserNameMapByIds("tenant123", "currentUser", userIds);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法处理null用户
   */
  @Test
  @DisplayName("测试getIdNameMap - null用户")
  void testGetIdNameMap_NullUser() {
    // 准备测试数据
    List<String> userIds = Arrays.asList("user1");
    
    // 执行被测试方法 - 传入null用户应该抛出异常或返回空map
    assertThrows(NullPointerException.class, () -> {
      converter.getIdNameMap(userIds, employeeFieldDescribe, null);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法处理OrgService返回null的情况
   */
  @Test
  @DisplayName("测试getIdNameMap - OrgService返回null")
  void testGetIdNameMap_OrgServiceReturnsNull() {
    // 准备测试数据
    List<String> userIds = Arrays.asList("user1", "user2");
    
    when(orgService.getUserNameMapByIds("tenant123", "currentUser", userIds))
        .thenReturn(null);

    // 执行被测试方法
    Map<String, String> result = converter.getIdNameMap(userIds, employeeFieldDescribe, user);

    // 验证结果
    assertNull(result);
    
    verify(orgService).getUserNameMapByIds("tenant123", "currentUser", userIds);
  }

  /**
   * 提供不同大小的用户ID列表测试数据
   */
  private static Stream<Arguments> provideUserIdLists() {
    return Stream.of(
        Arguments.of(Arrays.asList("user1"), 1),
        Arguments.of(Arrays.asList("user1", "user2"), 2),
        Arguments.of(Arrays.asList("user1", "user2", "user3", "user4", "user5"), 5),
        Arguments.of(Collections.emptyList(), 0)
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法处理不同大小的用户ID列表
   */
  @ParameterizedTest
  @MethodSource("provideUserIdLists")
  @DisplayName("测试getIdNameMap - 不同大小的用户ID列表")
  void testGetIdNameMap_DifferentSizedUserIdLists(List<String> userIds, int expectedSize) {
    // 准备测试数据
    Map<String, String> expectedMap = new HashMap<>();
    for (int i = 0; i < userIds.size(); i++) {
      expectedMap.put(userIds.get(i), "用户" + (i + 1));
    }
    
    when(orgService.getUserNameMapByIds("tenant123", "currentUser", userIds))
        .thenReturn(expectedMap);

    // 执行被测试方法
    Map<String, String> result = converter.getIdNameMap(userIds, employeeFieldDescribe, user);

    // 验证结果
    assertNotNull(result);
    assertEquals(expectedSize, result.size());
    
    verify(orgService).getUserNameMapByIds("tenant123", "currentUser", userIds);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getSupportedFieldTypes方法
   */
  @Test
  @DisplayName("测试getSupportedFieldTypes")
  void testGetSupportedFieldTypes() {
    // 执行被测试方法
    List<String> supportedTypes = converter.getSupportedFieldTypes();

    // 验证结果
    assertNotNull(supportedTypes);
    assertEquals(1, supportedTypes.size());
    assertTrue(supportedTypes.contains(IFieldType.EMPLOYEE));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法处理重复用户ID的情况
   */
  @Test
  @DisplayName("测试getIdNameMap - 重复用户ID")
  void testGetIdNameMap_DuplicateUserIds() {
    // 准备测试数据 - 包含重复的用户ID
    List<String> userIds = Arrays.asList("user1", "user2", "user1", "user3");
    Map<String, String> expectedMap = new HashMap<>();
    expectedMap.put("user1", "张三");
    expectedMap.put("user2", "李四");
    expectedMap.put("user3", "王五");
    
    when(orgService.getUserNameMapByIds("tenant123", "currentUser", userIds))
        .thenReturn(expectedMap);

    // 执行被测试方法
    Map<String, String> result = converter.getIdNameMap(userIds, employeeFieldDescribe, user);

    // 验证结果
    assertNotNull(result);
    assertEquals(3, result.size()); // 去重后应该只有3个用户
    assertEquals("张三", result.get("user1"));
    assertEquals("李四", result.get("user2"));
    assertEquals("王五", result.get("user3"));
    
    verify(orgService).getUserNameMapByIds("tenant123", "currentUser", userIds);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法处理特殊字符用户ID的情况
   */
  @Test
  @DisplayName("测试getIdNameMap - 特殊字符用户ID")
  void testGetIdNameMap_SpecialCharacterUserIds() {
    // 准备测试数据 - 包含特殊字符的用户ID
    List<String> userIds = Arrays.asList("user-1", "user_2", "user@3", "user.4");
    Map<String, String> expectedMap = new HashMap<>();
    expectedMap.put("user-1", "用户1");
    expectedMap.put("user_2", "用户2");
    expectedMap.put("user@3", "用户3");
    expectedMap.put("user.4", "用户4");
    
    when(orgService.getUserNameMapByIds("tenant123", "currentUser", userIds))
        .thenReturn(expectedMap);

    // 执行被测试方法
    Map<String, String> result = converter.getIdNameMap(userIds, employeeFieldDescribe, user);

    // 验证结果
    assertNotNull(result);
    assertEquals(4, result.size());
    assertEquals("用户1", result.get("user-1"));
    assertEquals("用户2", result.get("user_2"));
    assertEquals("用户3", result.get("user@3"));
    assertEquals("用户4", result.get("user.4"));
    
    verify(orgService).getUserNameMapByIds("tenant123", "currentUser", userIds);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getIdNameMap方法处理OrgService抛出异常的情况
   */
  @Test
  @DisplayName("测试getIdNameMap - OrgService抛出异常")
  void testGetIdNameMap_OrgServiceThrowsException() {
    // 准备测试数据
    List<String> userIds = Arrays.asList("user1", "user2");
    
    when(orgService.getUserNameMapByIds("tenant123", "currentUser", userIds))
        .thenThrow(new RuntimeException("Service error"));

    // 执行被测试方法 - 应该抛出异常
    assertThrows(RuntimeException.class, () -> {
      converter.getIdNameMap(userIds, employeeFieldDescribe, user);
    });
    
    verify(orgService).getUserNameMapByIds("tenant123", "currentUser", userIds);
  }

  /**
   * 创建测试用的员工字段描述
   */
  private IFieldDescribe createEmployeeFieldDescribe(String apiName, String fieldType) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试员工字段");
    fieldMap.put("type", fieldType);
    return FieldDescribeFactory.newInstance(fieldMap);
  }
}
