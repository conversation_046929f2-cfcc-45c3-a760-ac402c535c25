package com.facishare.paas.appframework.core.util


import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.rest.InnerHeaders
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> create by liy on 2025/5/14
 */
class RequestUtilTest extends Specification {

    void setup() {
        RequestContextManager.removeContext()
    }

    void cleanup() {
        RequestContextManager.removeContext()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试系统租户判断功能
     */
    @Unroll
    def "isSystemTenantTest"() {
        expect:
        RequestUtil.isSystemTenant(tenantId) == expected

        where:
        tenantId | expected
        "-100"   | true
        "-101"   | true
        "123"    | false
        null     | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取当前语言设置，包含context为null和context存在两种情况
     */
    def "getCurrentLangTest"() {
        given:
        def context = RequestContext.builder().build()

        when: "context为null的情况"
        then:
        RequestUtil.getCurrentLang() == Lang.zh_CN

        when: "context存在但lang为null的情况"
        RequestContextManager.setContext(context)
        then:
        RequestUtil.getCurrentLang() == Lang.zh_CN

        when: "context存在且lang有值的情况"
        context = RequestContext.builder()
                .lang(Lang.en)
                .build()
        RequestContextManager.setContext(context)
        then:
        RequestUtil.getCurrentLang() == Lang.en
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取语言标签
     */
    def "getLanguageTagTest"() {
        given:
        def context = RequestContext.builder()
                .lang(lang)
                .build()

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.getLanguageTag() == expected

        where:
        lang       | expected
        Lang.zh_CN | "zh-CN"
        Lang.en    | "en"
        null       | "zh-CN"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试内部请求判断功能
     */
    def "isInnerRequestTest"() {
        given:
        def context = RequestContext.builder()
                .requestSource(requestSource)
                .build()

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.isInnerRequest() == expected

        where:
        requestSource                      | expected
        RequestContext.RequestSource.INNER | true
        RequestContext.RequestSource.CEP   | false
        null                               | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试URL编码和解码功能
     */
    def "urlEncodeAndDecodeTest"() {
        expect:
        RequestUtil.encode(input) == encoded
        RequestUtil.decode(encoded, "UTF-8") == decoded

        where:
        input  | encoded              | decoded
        "test" | "test"               | "test"
        "测试" | "%E6%B5%8B%E8%AF%95" | "测试"
        null   | null                 | null
        ""     | ""                   | ""
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取自定义请求头功能
     */
    def "getCustomHeaderTest"() {
        given:
        def context = RequestContext.builder().build()
        context.setAttribute("X-Custom-Header", "test-value")

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.getCustomHeader("X-Custom-Header") == "test-value"
        RequestUtil.getCustomHeader(null) == ""
        RequestUtil.getCustomHeader("") == ""
        RequestUtil.getCustomHeader("Non-Existent-Header") == ""
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试移动端请求判断功能
     */
    def "isMobileRequestTest"() {
        given:
        def context = RequestContext.builder()
                .clientInfo(clientInfo)
                .build()

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.isMobileRequest() == expected

        where:
        clientInfo       | expected
        "Android.123456" | true
        "iOS.123456"     | true
        "Web.123456"     | false
        null             | false
        ""               | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取应用版本号功能
     */
    def "getAppVersionTest"() {
        given:
        def context = RequestContext.builder()
                .clientInfo(clientInfo)
                .build()

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.getAppVersion() == expected

        where:
        clientInfo       | expected
        "Android.123456" | "123456"
        "iOS.100000001"  | "1"
        "Web.123456"     | null
        null             | null
        ""               | null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试CEP请求判断功能
     */
    def "isCepRequestTest"() {
        given:
        def context = RequestContext.builder()
                .requestSource(requestSource)
                .build()

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.isCepRequest() == expected

        where:
        requestSource                      | expected
        RequestContext.RequestSource.CEP   | true
        RequestContext.RequestSource.INNER | false
        null                               | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试微信小程序请求判断功能
     */
    def "isWXMiniProgramTest"() {
        given:
        def context = RequestContext.builder()
                .clientInfo(clientInfo)
                .build()

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.isWXMiniProgram() == expected

        where:
        clientInfo                         | expected
        RequestContext.WEB_WX_MINI_PROGRAM | true
        "WEB.WXMiniProgram"                | true
        "Android.123456"                   | false
        null                               | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试微信Web请求判断功能
     */
    def "isWXWebRequestTest"() {
        given:
        def context = RequestContext.builder()
                .clientInfo(clientInfo)
                .build()
        if (deviceType) {
            context.setAttribute(RequestContext.FS_DEVICE_TYPE, deviceType)
        }

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.isWXWebRequest() == expected

        where:
        clientInfo       | deviceType                           | expected
        "WX.123456"      | null                                 | true
        "WX.123456"      | RequestContext.FS_DEVICE_TYPE_MOBILE | false
        "Android.123456" | null                                 | false
        null             | null                                 | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试企业微信移动端请求判断功能
     */
    def "isMobileWXWorkRequestTest"() {
        given:
        def context = RequestContext.builder()
                .clientInfo(clientInfo)
                .build()
        if (deviceType) {
            context.setAttribute(RequestContext.FS_DEVICE_TYPE, deviceType)
        }

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.isMobileWXWorkRequest() == expected

        where:
        clientInfo       | deviceType                           | expected
        "WXwork.123456"  | RequestContext.FS_DEVICE_TYPE_MOBILE | true
        "WXwork.123456"  | null                                 | false
        "Android.123456" | RequestContext.FS_DEVICE_TYPE_MOBILE | false
        null             | RequestContext.FS_DEVICE_TYPE_MOBILE | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量处理标志功能
     */
    def "isBatchTest"() {
        given:
        def context = RequestContext.builder()
                .batch(batch)
                .build()

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.isBatch() == expected

        where:
        batch | expected
        true  | true
        false | false
        null  | true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试OpenAPI请求判断功能
     */
    def "isOpenAPIRequestTest"() {
        given:
        def context = RequestContext.builder()
                .peerName(peerName)
                .build()

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.isOpenAPIRequest() == expected

        where:
        peerName                             | expected
        RequestContext.OPENAPI_PEER_NAME     | true
        RequestContext.OPENAPI_PEER_NAME_TWO | true
        "OtherPeerName"                      | false
        null                                 | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Web请求判断功能
     */
    def "isWebRequestTest"() {
        given:
        def context = RequestContext.builder()
                .clientInfo(clientInfo)
                .build()

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.isWebRequest() == expected

        where:
        clientInfo       | expected
        "Web.123456"     | true
        "Android.123456" | false
        "iOS.123456"     | false
        null             | true
        ""               | true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Function来源判断功能
     */
    def "isFromFunctionTest"() {
        given:
        def context = RequestContext.builder()
                .peerName(peerName)
                .build()

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.isFromFunction() == expected

        where:
        peerName                          | expected
        RequestContext.FUNCTION_PEER_NAME | true
        "OtherPeerName"                   | false
        null                              | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试原始CEP请求判断功能
     */
    def "isOriginalCepRequestTest"() {
        given:
        def context = RequestContext.builder()
                .requestSource(RequestContext.RequestSource.CEP)
                .build()
        if (originalRequestSource) {
            context.setAttribute(InnerHeaders.ORIGINAL_REQUEST_SOURCE, originalRequestSource)
        }

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.isOriginalCepRequest() == expected

        where:
        originalRequestSource                   | expected
        RequestContext.RequestSource.CEP.name() | true
        "OTHER"                                 | false
        null                                    | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试参数幂等性判断功能
     */
    def "needParamsIdempotentTest"() {
        given:
        def context = RequestContext.builder()
                .requestSource(requestSource)
                .paramsIdempotent(paramsIdempotent)
                .build()

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.needParamsIdempotent() == expected

        where:
        requestSource                      | paramsIdempotent | expected
        RequestContext.RequestSource.CEP   | false            | true
        RequestContext.RequestSource.CEP   | true             | true
        RequestContext.RequestSource.INNER | true             | true
        RequestContext.RequestSource.INNER | false            | false
        null                               | false            | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试计算日志启用判断功能
     */
    def "isCalculateLogEnableTest"() {
        given:
        def context = RequestContext.builder()
                .build()
        if (recordCalculateLog != null) {
            context.setAttribute(RequestContext.Attributes.RECORD_CALCULATE_LOG, recordCalculateLog)
        }
        if (debug) {
            context.setAttribute(RequestContext.Attributes.DEBUG, true)
        }

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.isCalculateLogEnable() == expected

        where:
        recordCalculateLog | debug | expected
        true               | false | true
        false              | false | false
        null               | false | true
        false              | true  | true
        null               | true  | true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试调试模式判断功能
     */
    def "isDebugModeTest"() {
        given:
        def context = RequestContext.builder()
                .build()
        if (debug != null) {
            context.setAttribute(RequestContext.Attributes.DEBUG, debug)
        }

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.isDebugMode() == expected

        where:
        debug | expected
        true  | true
        false | false
        null  | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取业务信息功能
     */
    def "getBizInfoTest"() {
        given:
        def bizInfo = new RequestContext.BizInfo(biz: "testBiz")
        def context = RequestContext.builder()
                .build()
        if (hasBizInfo) {
            context.setBizInfo(bizInfo)
        }

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.getBizInfo() == expected

        where:
        hasBizInfo | expected
        true       | new RequestContext.BizInfo(biz: "testBiz")
        false      | null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取业务ID功能
     */
    def "getBizIdTest"() {
        given:
        def bizInfo = new RequestContext.BizInfo(bizId: "testBizId")
        def context = RequestContext.builder()
                .bizId(directBizId)
                .build()
        if (hasBizInfo) {
            context.setBizInfo(bizInfo)
        }

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.getBizId() == expected

        where:
        hasBizInfo | directBizId | expected
        true       | null        | "testBizId"
        false      | "directId"  | "directId"
        false      | null        | null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取其他业务ID功能
     */
    def "getOtherBizIdTest"() {
        given:
        def bizInfo = new RequestContext.BizInfo(otherBizId: "testOtherBizId")
        def context = RequestContext.builder()
                .build()
        if (hasBizInfo) {
            context.setBizInfo(bizInfo)
        }

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.getOtherBizId() == expected

        where:
        hasBizInfo | expected
        true       | "testOtherBizId"
        false      | null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取PeerName功能
     */
    def "getPeerNameTest"() {
        given:
        def bizInfo = new RequestContext.BizInfo(biz: "testBiz")
        def context = RequestContext.builder()
                .peerName(peerName)
                .build()
        if (hasBizInfo) {
            context.setBizInfo(bizInfo)
        }

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.getPeerName() == expected

        where:
        peerName   | hasBizInfo | expected
        "testPeer" | false      | "testPeer"
        null       | true       | "testBiz"
        null       | false      | null
        "testPeer" | true       | "testPeer"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试设置和获取计算日志记录功能
     */
    def "setRecordCalculateLogTest"() {
        given:
        def context = RequestContext.builder()
                .build()
        if (oldValue != null) {
            context.setAttribute(RequestContext.Attributes.RECORD_CALCULATE_LOG, oldValue)
        }

        when:
        RequestContextManager.setContext(context)
        def result = RequestUtil.setRecordCalculateLog(newValue)

        then:
        result == oldValue
        RequestUtil.isCalculateLogEnable() == expected

        where:
        oldValue | newValue | expected
        true     | false    | false
        false    | true     | true
        null     | true     | true
        true     | null     | true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试移动端版本比较功能
     */
    def "isMobileRequestBeforeVersionTest"() {
        given:
        def context = RequestContext.builder()
                .clientInfo(clientInfo)
                .build()

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.isMobileRequestBeforeVersion(version) == expected

        where:
        clientInfo       | version  | expected
        "Android.123456" | "123457" | true
        "Android.123456" | "123456" | false
        "Android.123456" | "123455" | false
        "Web.123456"     | "123457" | false
        null             | "123457" | false
        "Android.123"    | "123456" | true
        "Android.123456" | null     | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建系列键功能
     */
    def "buildSeriesKeyTest"() {
        expect:
        RequestUtil.buildSeriesKey(seriesId, tenantId) == expected

        where:
        seriesId | tenantId | expected
        "1"      | "100"    | "series|1|100"
        null     | "100"    | "series|null|100"
        "1"      | null     | "series|1|null"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取应用ID功能
     */
    def "getAppIdTest"() {
        given:
        def context = RequestContext.builder()
                .appId(appId)
                .build()

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.getAppId() == expected

        where:
        appId     | expected
        "testApp" | "testApp"
        null      | null
        ""        | ""
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取事件ID功能
     */
    def "getEventIdTest"() {
        given:
        def context = RequestContext.builder()
                .eventId(eventId)
                .build()

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.getEventId() == expected

        where:
        eventId    | expected
        "event123" | "event123"
        null       | null
        ""         | ""
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试修改日志隐藏状态的设置和获取功能
     */
    def "modifyLogHiddenTest"() {
        given:
        def context = RequestContext.builder().build()

        when: "context为null的情况"
        then:
        !RequestUtil.isModifyLogHidden()

        when: "设置context但未设置修改日志隐藏状态"
        RequestContextManager.setContext(context)
        then:
        !RequestUtil.isModifyLogHidden()

        when: "设置修改日志为隐藏状态"
        RequestContextManager.setContext(context)
        RequestUtil.setModifyLogHidden()
        then:
        RequestUtil.isModifyLogHidden()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试序列化空值设置功能
     */
    def "setSerializeEmptyFalseTest"() {
        given:
        def context = RequestContext.builder()
                .contentType(contentType)
                .build()

        when:
        RequestContextManager.setContext(context)
        RequestUtil.setSerializeEmptyFalse()

        then:
        context.getContentType() == expected

        where:
        contentType                                    | expected
        RequestContext.ContentType.SIMPLE_JSON         | RequestContext.ContentType.NONNULL_SIMPLE_JSON
        RequestContext.ContentType.FULL_JSON           | RequestContext.ContentType.NONNULL_FULL_JSON
        RequestContext.ContentType.NONNULL_SIMPLE_JSON | RequestContext.ContentType.NONNULL_SIMPLE_JSON
        RequestContext.ContentType.NONNULL_FULL_JSON   | RequestContext.ContentType.NONNULL_FULL_JSON
        null                                           | null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取外部身份类型功能
     */
    def "getOutIdentityTypeTest"() {
        given:
        def context = RequestContext.builder()
                .outIdentityType(outIdentityType)
                .build()

        when:
        RequestContextManager.setContext(context)

        then:
        RequestUtil.getOutIdentityType() == expected

        where:
        outIdentityType | expected
        "type1"         | "type1"
        null            | null
        ""              | ""
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试设置ES最近更新标记功能
     */
    def "setEsRecentUpdateTest"() {
        given:
        def context = RequestContext.builder().build()

        when: "context为null的情况"
        RequestUtil.setEsRecentUpdate()
        then:
        true

        when: "设置context并调用setEsRecentUpdate"
        RequestContextManager.setContext(context)
        RequestUtil.setEsRecentUpdate()
        then:
        context.getAttribute(RequestContext.Attributes.ES_RECENT_UPDATE) == true
    }
}
