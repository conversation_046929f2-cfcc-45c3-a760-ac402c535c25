package com.facishare.paas.appframework.function.dto;

import com.facishare.function.Function;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by fengjy in 2020/4/13 10:30
 */
public interface ControllerFunction {
    @Data
    @Builder
    class Arg{
        @JsonProperty(value = "api_name")
        String apiName;

        List<?> parameters;
    }
}
