package com.facishare.paas.appframework.core.predef.handler.add;

import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.RequestId;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.handler.AbstractActTCCActionHandler;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.facade.ChangeOrderHandlerLogicService;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.exception.MetadataDataDuplicateBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Quote;
import com.fxiaoke.transaction.tcc.annotation.TccTransactional;
import com.fxiaoke.transaction.tcc.api.context.BranchTransactionalContext;
import com.google.common.base.Charsets;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.hash.Hashing;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2023/1/29.
 */
@Slf4j
@Component
@HandlerProvider(name = "defaultSaveDataAddActHandler")
public class DefaultSaveDataAddActHandler extends AbstractActTCCActionHandler<AddActionHandler.Arg, AddActionHandler.Result> {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ChangeOrderHandlerLogicService changeOrderHandlerLogicService;

    @Override
    @TccTransactional(name = "handle", confirmMethod = "commit", cancelMethod = "rollback")
    public AddActionHandler.Result handle(HandlerContext context, AddActionHandler.Arg arg) {
        handleQuoteValue(arg);
        SaveMasterAndDetailData.Result saveResult = doSaveData(context, arg);
        return buildResult(arg, saveResult);
    }

    private AddActionHandler.Result buildResult(AddActionHandler.Arg arg, SaveMasterAndDetailData.Result saveResult) {
        BaseObjectSaveAction.Result actionResult = BaseObjectSaveAction.Result.builder()
                .writeDB(true)
                .isDuplicate(false)
                .objectData(ObjectDataDocument.of(saveResult.getMasterObjectData()))
                .details(ObjectDataDocument.ofMap(saveResult.getDetailObjectData()))
                .relatedDataList(arg.getRelatedObjectData())
                .build();
        AddActionHandler.Result result = new AddActionHandler.Result();
        result.setInterfaceResult(actionResult);
        return result;
    }

    private void handleQuoteValue(List<IObjectData> dataList, IObjectDescribe describe) {
        List<Quote> quotes = ObjectDescribeExt.of(describe).getQuoteFieldDescribes();
        if (CollectionUtils.empty(quotes) || CollectionUtils.empty(dataList)) {
            return;
        }
        Set<String> quoteFieldApiName = quotes.stream().map(Quote::getApiName).collect(Collectors.toSet());
        dataList.stream().map(ObjectDataExt::of).forEach(dataExt -> dataExt.remove(quoteFieldApiName));
    }

    private void handleQuoteValue(AddActionHandler.Arg arg) {
        IObjectData objectData = arg.objectData();
        IObjectDescribe objectDescribe = arg.getObjectDescribe();
        handleQuoteValue(Lists.newArrayList(objectData), objectDescribe);

        // 处理从对象
        Map<String, List<IObjectData>> detailObjectData = arg.detailObjectData();
        if (CollectionUtils.notEmpty(detailObjectData)) {
            detailObjectData.forEach((apiName, objectDataList) -> handleQuoteValue(objectDataList, arg.getDetailDescribe(apiName)));
        }

        // 处理相关对象
        if (CollectionUtils.notEmpty(arg.getRelatedObjectData())) {
            arg.relatedDataMap().forEach((apiName, objectDataList) -> handleQuoteValue(objectDataList, arg.getRelatedDescribe(apiName)));
        }
    }

    private SaveMasterAndDetailData.Result doSaveData(HandlerContext context, AddActionHandler.Arg arg) {
        //正式更新对象和从对象
        SaveMasterAndDetailData.Arg saveArg = SaveMasterAndDetailData.Arg.builder()
                .idempotentKey(buildIdempotentKey(context, arg))
                .masterObjectData(copyAndRemoveSpecialFields(arg.getObjectDescribe(), arg.objectData()))
                .detailObjectData(copyAndRemoveSpecialFields(arg.detailObjectData(), arg))
                .relatedObjectData(arg.relatedObjectData())
                .objectDescribes(arg.allDescribes())
                .actionType("addHandler")
                .enableRealTimeCalculateDataAuth(arg.getInterfaceArg().enableRealTimeCalculateDataAuth())
                .enableUniqueCheckResult(arg.getInterfaceArg().enableUniqueCheckResult())
                .realTimeCalculateDetailAuth(arg.getInterfaceArg().realTimeCalculateDetailAuth())
                .build();
        SaveMasterAndDetailData.Result saveResult = null;
        try {
            saveResult = serviceFacade.saveMasterAndDetailData(context.getUser(), saveArg, arg.getConvertRuleDataContainer());
        } catch (MetadataDataDuplicateBusinessException e) {
            log.warn("saveMasterAndDetailData fail with MetadataDataDuplicateBusinessException", e);
            MetadataDataDuplicateBusinessException.DuplicateMessage duplicateMessage = e.getDuplicateMessage();
            BaseObjectSaveAction.DuplicateDataVerificationResult duplicateDataVerificationResult = BaseObjectSaveAction.DuplicateDataVerificationResult.fromFieldDuplicateMessage(e.getMessage(), duplicateMessage);
            BaseObjectSaveAction.Result actionResult = BaseObjectSaveAction.Result.builder()
                    .writeDB(false)
                    .duplicateDataVerificationResult(duplicateDataVerificationResult)
                    .build();
            throw new AcceptableValidateException(actionResult);
        }
        //被幂等组件拦截抛异常提示
        if (saveResult == null) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.DO_NOT_SUBMIT_REPEATEDLY));
        }
        saveOriginalDataByChangeOrder(context.getUser(), arg.getObjectDescribe(), saveArg, arg.getInterfaceArg());
        return saveResult;
    }

    private IObjectData copyAndRemoveSpecialFields(IObjectDescribe describe, IObjectData data) {
        if (Objects.isNull(describe)) {
            return data;
        }
        Set<String> fieldApiNamesToRemove = ObjectDescribeExt.of(describe).getFieldDescribes().stream()
                .filter(x -> FieldDescribeExt.of(x).isQuoteField())
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toSet());
        if (CollectionUtils.empty(fieldApiNamesToRemove)) {
            return data;
        }
        IObjectData cpData = ObjectDataExt.of(data).copy();
        return ObjectDataExt.of(cpData).remove(fieldApiNamesToRemove).getObjectData();
    }

    private Map<String, List<IObjectData>> copyAndRemoveSpecialFields(Map<String, List<IObjectData>> dataMap, AddActionHandler.Arg arg) {
        if (CollectionUtils.empty(dataMap)) {
            return dataMap;
        }
        return dataMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                        .map(x -> copyAndRemoveSpecialFields(arg.getDetailDescribe(entry.getKey()), x))
                        .collect(Collectors.toList())));
    }

    private void saveOriginalDataByChangeOrder(User user, IObjectDescribe describe, SaveMasterAndDetailData.Arg saveArg,
                                               BaseObjectSaveAction.Arg interfaceArg) {
        if (!ObjectDescribeExt.of(describe).isChangeOrderObject()) {
            return;
        }
        ObjectDataDocument originalData = interfaceArg.getOriginalData();
        if (Objects.isNull(originalData)) {
            return;
        }
        changeOrderHandlerLogicService.saveChangeOrderOriginalData(user, saveArg,
                originalData.toObjectData(), ObjectDataDocument.ofDataMap(interfaceArg.getOriginalDetails()));
    }

    private String buildIdempotentKey(HandlerContext context, Arg arg) {
        String postId = context.getRequestContext().getPostId();
        if (Strings.isNullOrEmpty(postId)) {
            return null;
        }
        return Hashing.sha256().newHasher()
                .putString("saveMasterAndDetailData", Charsets.UTF_8)
                .putString(postId, Charsets.UTF_8)
                .putString(context.getTenantId(), Charsets.UTF_8)
                .putString(arg.getObjectApiName() + "/" + context.getInterfaceCode(), Charsets.UTF_8)
                .hash().toString();
    }

    @Override
    public boolean commit(BranchTransactionalContext branchTransactionalContext, HandlerContext context, AddActionHandler.Arg arg) {
        return super.commit(branchTransactionalContext, context, arg);
    }

    @Override
    @Idempotent
    @Transactional
    public boolean rollback(@RequestId(propertyName = "branchId") BranchTransactionalContext branchTransactionalContext, HandlerContext context, AddActionHandler.Arg arg) {
        return RequestContextManager.runWithContext(context.getRequestContext(), () -> {
            IObjectData objectData = arg.objectData();
            Map<String, List<IObjectData>> detailObjectData = arg.detailObjectData();
            bulkDeleteData(context.getUser(), objectData, detailObjectData);
            return super.rollback(branchTransactionalContext, context, arg);
        });
    }

    /**
     * 事务回滚，需要删除新建主和从对象数据
     *
     * @param user
     * @param objectData
     * @param detailObjectData
     */
    private void bulkDeleteData(User user, IObjectData objectData, Map<String, List<IObjectData>> detailObjectData) {
        serviceFacade.bulkDeleteDirect(Lists.newArrayList(objectData), user);
        if (CollectionUtils.empty(detailObjectData)) {
            return;
        }
        detailObjectData.forEach((apiName, dataList) -> serviceFacade.bulkDeleteDirect(dataList, user));
    }
}
