package com.facishare.paas.appframework.core.timezone

import com.facishare.paas.appframework.core.model.ObjectDataDocument
import com.facishare.paas.metadata.api.describe.Date
import com.facishare.paas.metadata.api.describe.IFieldType
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.timezone.TimeZoneContext
import com.facishare.paas.timezone.TimeZoneContextHolder
import com.facishare.paas.timezone.config.TimeZoneConfig
import spock.lang.Specification

import java.time.ZoneId

/**
 * create by z<PERSON><PERSON> on 2021/06/17
 */
class SingleObjectDataFieldConvertTest extends Specification {

    def setup() {
        TimeZoneConfig.INSTANCE.TIME_ZONE_GRAY_ENTERPRISE_IDS.add("78057")
    }

    def "test convertDateValue2System"() {
        given:
        TimeZoneContextHolder.setTimezoneContext(TimeZoneContext.of("78057", ZoneId.of(timeZone), ZoneId.of("Atlantic/Cape_Verde")))

        ObjectDataFieldConvert convert = new SingleObjectDataFieldConvert()
        IObjectDescribe describe = Stub(IObjectDescribe)
        Date date = Stub(Date)
        when:
        describe.getFieldDescribe(fieldApiName) >> date
        describe.getFieldDescribes() >> [date]
        date.getApiName() >> fieldApiName
        date.getType() >> IFieldType.DATE
        then:
        def result = convert.convert2SystemZone(value) { describe }
        result == expect
        where:
        fieldApiName | value                                              | timeZone     | expect
        "date__c"    | ["date__c": 1623855600000L]                        | "Asia/Tokyo" | ["date__c": 1623859200000L]
        "date__c"    | ObjectDataDocument.of(["date__c": 1623855600000L]) | "Asia/Tokyo" | ObjectDataDocument.of(["date__c": 1623859200000L])
        "date__c"    | """{"date__c":1623855600000}"""                    | "Asia/Tokyo" | """{"date__c":1623859200000}"""
    }

    def "test convertDateValue2Custom"() {
        given:
        TimeZoneContextHolder.setTimezoneContext(TimeZoneContext.of("78057", ZoneId.of(timeZone), ZoneId.of("Atlantic/Cape_Verde")))

        ObjectDataFieldConvert convert = new SingleObjectDataFieldConvert()
        IObjectDescribe describe = Stub(IObjectDescribe)
        Date date = Stub(Date)
        when:
        describe.getFieldDescribe(fieldApiName) >> date
        describe.getFieldDescribes() >> [date]
        date.getApiName() >> fieldApiName
        date.getType() >> IFieldType.DATE
        then:
        def result = convert.convert2CustomZone(value) { describe }
        result == expect
        where:
        fieldApiName | value                                              | timeZone     | expect
        "date__c"    | ["date__c": 1623859200000L]                        | "Asia/Tokyo" | ["date__c": 1623855600000L]
        "date__c"    | ObjectDataDocument.of(["date__c": 1623859200000L]) | "Asia/Tokyo" | ObjectDataDocument.of(["date__c": 1623855600000L])
        "date__c"    | """{"date__c":1623859200000}"""                    | "Asia/Tokyo" | """{"date__c":1623855600000}"""
    }
}
