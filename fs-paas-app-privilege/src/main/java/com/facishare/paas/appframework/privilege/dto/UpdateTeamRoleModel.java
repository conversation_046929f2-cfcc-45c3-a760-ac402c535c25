package com.facishare.paas.appframework.privilege.dto;

import lombok.*;

import java.util.Set;

public class UpdateTeamRoleModel {

  @EqualsAndHashCode(callSuper = true)
  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Arg extends BasePrivilegeArg {

    private String roleName;
    private String roleType;
    private Set<String> entityIds;
    private String description;
    private String lang;

  }


  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class Result extends BasePrivilegeResult {

  }
}
