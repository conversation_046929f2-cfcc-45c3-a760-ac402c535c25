package com.facishare.paas.appframework.function.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.codehaus.jackson.annotate.JsonProperty;

import java.util.List;
import java.util.Map;

/**
 * Created by fengjy in 2020/2/25 10:45
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RunResult {
    private boolean success;
    // 暂时只有200和400，400代表业务抛出了异常
    private int code;
    private Object functionResult;
    private String errorInfo;
    private String returnType;
    private Map<String, Object> diffData;
    @JsonAlias("details")
    @JsonProperty("diffDetails")
    private Map<String, List<Map<String, Object>>> diffDetails;
}
