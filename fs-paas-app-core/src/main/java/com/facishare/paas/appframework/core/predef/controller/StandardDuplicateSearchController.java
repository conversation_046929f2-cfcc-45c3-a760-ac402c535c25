package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.plugin.APLControllerPlugin;
import com.facishare.paas.appframework.core.model.plugin.Plugin;
import com.facishare.paas.appframework.core.predef.service.ObjectDuplicatedSearchService;
import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.GetResult;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.DuplicatedSearchExt;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchResult;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.SecurityEventTrackingDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.trace.TraceContext;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;
import java.util.function.UnaryOperator;

public class StandardDuplicateSearchController extends PreDefineController<GetResult.Arg, GetResult.Result> {
    private ObjectDuplicatedSearchService objectDuplicatedSearchService =
            SpringUtil.getContext().getBean("objectDuplicatedSearchService", ObjectDuplicatedSearchService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected GetResult.Result doService(GetResult.Arg arg) {
        ServiceContext serviceContext = new ServiceContext(controllerContext.getRequestContext(), "duplicate_search", "get_result");
        return objectDuplicatedSearchService.getResult(arg, serviceContext, handleDuplicateSearchResult()); // 前台拦截非实时翻译查重名称
    }

    @Override
    protected GetResult.Result after(GetResult.Arg arg, GetResult.Result result) {
        postMessage(arg.getObjectData(), result.getDuplicatedSearch());
        return super.after(arg, result);
    }

    private void postMessage(ObjectDataDocument objectData, IDuplicatedSearch duplicatedSearch) {
        if (Objects.isNull(duplicatedSearch)
                || !DuplicatedSearchExt.of(duplicatedSearch).isTool()
                || !AppFrameworkConfig.isPostMessage(duplicatedSearch.getDescribeApiName())
                || Objects.isNull(arg.getObjectData())
                || !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.REPORT_DUPLICATE_GRAY_EI,controllerContext.getTenantId())
                || BooleanUtils.isTrue(arg.isFromSimpleSearch())) {
            return;
        }
        Map<Object, Object> objectMap = Maps.newHashMap();
        Set<String> duplicatedSearchFieldSet = Sets.newHashSet();
        DuplicatedSearchExt.of(duplicatedSearch).getRuleFieldNames().forEach(duplicatedSearchFieldSet::addAll);
        duplicatedSearchFieldSet.forEach(x -> objectMap.put(x, objectData.get(x)));

        SecurityEventTrackingDTO trackingDTO = SecurityEventTrackingDTO.builder()
                .eventId(UUID.randomUUID().toString())
                .traceId(TraceContext.get().getTraceId())
                .eventTime(System.currentTimeMillis())
                .tenantId(controllerContext.getTenantId())
                .userId(controllerContext.getUser().getUserId())
                .sourceIp(Objects.isNull(RequestContextManager.getContext()) ? "" : RequestContextManager.getContext().getClientIp())
                .deviceId(Objects.isNull(RequestContextManager.getContext()) ? "" : RequestContextManager.getContext().getAttribute(RequestContext.FS_DEVICE_ID))
                .deviceType(Objects.isNull(RequestContextManager.getContext()) ? "" : RequestContextManager.getContext().getAttribute(RequestContext.FS_DEVICE_TYPE))
                .objects(controllerContext.getObjectApiName())
                .operation("duplicationCheck")
                .actionType("UIDuplicationCheckEvent")
                .query(JSON.toJSONString(objectMap)).build();

        BizLogClient.send("biz-log-security-event-tracking", Pojo2Protobuf.toMessage(trackingDTO, com.fxiaoke.log.SecurityEventTrackingDTO.class).toByteArray());
    }

    /**
     * 对查重结果的处理
     *
     * @return
     */
    protected UnaryOperator<List<DuplicateSearchResult.DuplicateData>> handleDuplicateSearchResult() {
        return it -> it;
    }

    @Override
    protected Plugin.Arg buildAPLPluginArg(String method) {
        APLControllerPlugin.TriggerInfo triggerInfo = buildTriggerInfo();
        GetResult.Arg controllerArg = Objects.isNull(arg) ? null : arg.toPluginArg();
        GetResult.Result controllerResult = Objects.isNull(result) ? null : result.toPluginResult();
        return new APLControllerPlugin.Arg(controllerContext.getObjectApiName(), controllerArg, controllerResult, triggerInfo);
    }

    @Override
    protected void mergeAPLPluginResult(APLControllerPlugin.Result aplPluginResult) {
        GetResult.Result controllerResult = aplPluginResult.getControllerResult(GetResult.Result.class);
        Optional.ofNullable(controllerResult)
                .map(GetResult.Result::getDataList)
                .ifPresent(result::setDataList);
    }

}
