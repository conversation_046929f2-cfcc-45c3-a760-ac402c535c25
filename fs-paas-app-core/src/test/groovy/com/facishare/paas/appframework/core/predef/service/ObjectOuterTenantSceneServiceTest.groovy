package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.predef.service.dto.scene.*
import com.facishare.paas.appframework.metadata.DescribeLogicService
import com.facishare.paas.appframework.metadata.SceneLogicService
import com.facishare.paas.appframework.metadata.config.SceneConfig
import com.facishare.paas.appframework.metadata.dto.scene.IScene
import com.facishare.paas.appframework.metadata.dto.scene.ISystemScene
import com.facishare.paas.appframework.metadata.dto.scene.ITenantScene
import com.facishare.paas.appframework.metadata.dto.scene.SceneDTO
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

class ObjectOuterTenantSceneServiceTest extends Specification {

    ObjectOuterTenantSceneService objectOuterTenantSceneService
    SceneLogicService sceneLogicService = Mock(SceneLogicService)
    DescribeLogicService describeLogicService = Mock(DescribeLogicService)
    ServiceContext context = Mock(ServiceContext)

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def setup() {
        objectOuterTenantSceneService = new ObjectOuterTenantSceneService(
                sceneLogicService: sceneLogicService,
                describeLogicService: describeLogicService
        )
        context.getTenantId() >> "12345"
        context.getUser() >> null
    }

    @Unroll
    def "测试findScenes方法 - 查找场景列表"() {
        given:
        def arg = new FindSceneList.Arg(
                describeApiName: describeApiName,
                extendAttribute: extendAttribute,
                appId: appId
        )
        def scenes = [Mock(IScene)]
        def baseScenes = [Mock(IScene)]
        def sceneConfigList = [new SceneConfig()]

        when:
        def result = objectOuterTenantSceneService.findScenes(arg, context)

        then:
        1 * context.setAttribute("is_outer", true)
        1 * sceneLogicService.findScenes(describeApiName, _, extendAttribute) >> scenes
        1 * sceneLogicService.findBaseScenes(describeApiName, extendAttribute, _) >> baseScenes
        1 * sceneLogicService.findSceneConfigList(_, describeApiName, scenes) >> sceneConfigList
        result.getScenes() == scenes
        result.getBaseScenes() == baseScenes
        result.getSceneConfigs() != null

        where:
        describeApiName | extendAttribute | appId
        "AccountObj"    | "attr1"         | "app1"
        "ContactObj"    | "attr2"         | "app2"
    }

    @Unroll
    def "测试findScenes方法 - 查找单个场景"() {
        given:
        def arg = new FindSceneInfo.Arg(
                describeApiName: describeApiName,
                sceneApiName: sceneApiName,
                extendAttribute: extendAttribute,
                appId: appId
        )
        def scene = Mock(IScene)

        when:
        def result = objectOuterTenantSceneService.findScenes(arg, context)

        then:
        1 * context.setAttribute("is_outer", true)
        1 * sceneLogicService.findSceneByApiName(_, describeApiName, sceneApiName, extendAttribute, appId) >> scene
        result.getScene() == scene

        where:
        describeApiName | sceneApiName | extendAttribute | appId
        "AccountObj"    | "scene1"     | "attr1"         | "app1"
        "ContactObj"    | "scene2"     | "attr2"         | "app2"
    }

    @Unroll
    def "测试createTenantScene方法 - 成功场景"() {
        given:
        def sceneDTO = new SceneDTO()
        sceneDTO.setObjectDescribeApiName(describeApiName)
        sceneDTO.setDisplayName("测试场景")
        sceneDTO.setClearCustomConfig(clearCustomConfig)

        def arg = new ObjectTenantScene.Arg(
                describeApiName: describeApiName,
                scene: sceneDTO,
                extendAttribute: extendAttribute,
                appId: appId
        )

        def tenantScene = Mock(ITenantScene)
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setDisplayName("测试对象")

        when:
        def result = objectOuterTenantSceneService.createTenantScene(arg, context)

        then:
        1 * context.setAttribute("is_outer", true)
        1 * sceneLogicService.createTenantScene(sceneDTO, describeApiName, extendAttribute, _) >> tenantScene
        1 * describeLogicService.findObjectWithoutCopyIfGray(_, describeApiName) >> objectDescribe
        1 * sceneLogicService.replaceOtherScenes(objectDescribe, sceneDTO, extendAttribute, _, _) >> { args ->
            def arg3 = args[3] as List
            if (Objects.nonNull(arg3)) {
                arg3.add(["1", "2", "3"])
            }
            true
        }

        if (clearCustomConfig) {
            1 * sceneLogicService.ownCustomListConfigUserIdsToSend(describeApiName, objectDescribe.getDisplayName(), _, extendAttribute, _)
        }

        result.getScene() == tenantScene

        where:
        describeApiName | extendAttribute | appId  | clearCustomConfig
        "AccountObj"    | "attr1"         | "app1" | true
        "ContactObj"    | "attr2"         | "app2" | false
    }

    def "测试createTenantScene方法 - 场景为空时抛出异常"() {
        given:
        def arg = new ObjectTenantScene.Arg(
                describeApiName: "AccountObj",
                scene: null,
                extendAttribute: "attr1",
                appId: "app1"
        )

        when:
        objectOuterTenantSceneService.createTenantScene(arg, context)

        then:
        thrown(ValidateException)
    }

    @Unroll
    def "测试updateTenantScene方法 - 租户场景"() {
        given:
        def sceneDTO = new SceneDTO()
        sceneDTO.setObjectDescribeApiName(describeApiName)
        sceneDTO.setDisplayName("测试场景")
        sceneDTO.setType("tenant")
        sceneDTO.setClearCustomConfig(clearCustomConfig)

        def arg = new ObjectTenantScene.Arg(
                describeApiName: describeApiName,
                scene: sceneDTO,
                extendAttribute: extendAttribute,
                appId: appId
        )

        def updatedScene = new SceneDTO(type: "tenant")
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setDisplayName("测试对象")

        when:
        def result = objectOuterTenantSceneService.updateTenantScene(arg, context)

        then:
        1 * context.setAttribute("is_outer", true)
        1 * sceneLogicService.updateTenantScene(sceneDTO, describeApiName, _) >> updatedScene
        1 * describeLogicService.findObjectWithoutCopyIfGray(_, describeApiName) >> objectDescribe
        1 * sceneLogicService.replaceOtherScenes(objectDescribe, sceneDTO, extendAttribute, _, _) >> { args ->
            def arg3 = args[3] as List
            if (Objects.nonNull(arg3)) {
                arg3.add(["1", "2", "3"])
            }
            true
        }

        if (clearCustomConfig) {
            1 * sceneLogicService.ownCustomListConfigUserIdsToSend(describeApiName, objectDescribe.getDisplayName(), _, extendAttribute, _)
        }

        result.getScene() == updatedScene

        where:
        describeApiName | extendAttribute | appId  | clearCustomConfig
        "AccountObj"    | "attr1"         | "app1" | true
        "ContactObj"    | "attr2"         | "app2" | false
    }

    @Unroll
    def "测试updateTenantScene方法 - 系统场景"() {
        given:
        def sceneDTO = new SceneDTO()
        sceneDTO.setObjectDescribeApiName(describeApiName)
        sceneDTO.setDisplayName("测试场景")
        sceneDTO.setApiName("test_scene")
        sceneDTO.setType("system_scene")
        sceneDTO.setClearCustomConfig(clearCustomConfig)

        def arg = new ObjectTenantScene.Arg(
                describeApiName: describeApiName,
                scene: sceneDTO,
                extendAttribute: extendAttribute,
                appId: appId
        )

        def updatedScene = Mock(ISystemScene)
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setDisplayName("测试对象")

        when:
        def result = objectOuterTenantSceneService.updateTenantScene(arg, context)

        then:
        1 * context.setAttribute("is_outer", true)
        1 * sceneLogicService.updateSystemScene(sceneDTO, _) >> updatedScene
        1 * describeLogicService.findObjectWithoutCopyIfGray(_, describeApiName) >> objectDescribe
        1 * sceneLogicService.replaceOtherScenes(objectDescribe, sceneDTO, extendAttribute, _, _) >> { args ->
            def arg3 = args[3] as List
            if (Objects.nonNull(arg3)) {
                arg3.add(["1", "2", "3"])
            }
            true
        }

        if (clearCustomConfig) {
            1 * sceneLogicService.ownCustomListConfigUserIdsToSend(describeApiName, objectDescribe.getDisplayName(), _, extendAttribute, _)
        }

        result.getScene() == updatedScene

        where:
        describeApiName | extendAttribute | appId  | clearCustomConfig
        "AccountObj"    | "attr1"         | "app1" | true
        "ContactObj"    | "attr2"         | "app2" | false
    }

    @Unroll
    def "测试enableScene方法"() {
        given:
        def arg = new ChangeSceneStatus.Arg(
                describeApiName: describeApiName,
                sceneApiName: sceneApiName,
                type: type,
                extendAttribute: extendAttribute,
                appId: appId
        )

        when:
        def result = objectOuterTenantSceneService.enableScene(arg, context)

        then:
        1 * context.setAttribute("is_outer", true)
        1 * sceneLogicService.enableScene(describeApiName, sceneApiName, type, extendAttribute, _)
        result.isSuccess()

        where:
        describeApiName | sceneApiName | type           | extendAttribute | appId
        "AccountObj"    | "scene1"     | "tenant_scene" | "attr1"         | "app1"
        "ContactObj"    | "scene2"     | "system_scene" | "attr2"         | "app2"
    }

    @Unroll
    def "测试disableScene方法"() {
        given:
        def arg = new ChangeSceneStatus.Arg(
                describeApiName: describeApiName,
                sceneApiName: sceneApiName,
                type: type,
                extendAttribute: extendAttribute,
                appId: appId
        )

        when:
        def result = objectOuterTenantSceneService.disableScene(arg, context)

        then:
        1 * context.setAttribute("is_outer", true)
        1 * sceneLogicService.disableScene(describeApiName, sceneApiName, type, extendAttribute, _)
        result.isSuccess()

        where:
        describeApiName | sceneApiName | type           | extendAttribute | appId
        "AccountObj"    | "scene1"     | "tenant_scene" | "attr1"         | "app1"
        "ContactObj"    | "scene2"     | "system_scene" | "attr2"         | "app2"
    }

    @Unroll
    def "测试deleteScene方法"() {
        given:
        def arg = new ChangeSceneStatus.Arg(
                describeApiName: describeApiName,
                sceneApiName: sceneApiName,
                extendAttribute: extendAttribute,
                appId: appId
        )

        when:
        def result = objectOuterTenantSceneService.deleteScene(arg, context)

        then:
        1 * context.setAttribute("is_outer", true)
        1 * sceneLogicService.deleteTenantScene(describeApiName, sceneApiName, extendAttribute, _)
        result.isSuccess()

        where:
        describeApiName | sceneApiName | extendAttribute | appId
        "AccountObj"    | "scene1"     | "attr1"         | "app1"
        "ContactObj"    | "scene2"     | "attr2"         | "app2"
    }

    @Unroll
    def "测试validateTenantSceneCount方法"() {
        given:
        def arg = new ValidateTenantSceneCount.Arg(
                describeApiName: describeApiName,
                appId: appId
        )

        when:
        def result = objectOuterTenantSceneService.validateTenantSceneCount(arg, context)

        then:
        1 * context.setAttribute("is_outer", true)
        1 * sceneLogicService.validateTenantSceneSum(describeApiName, _)
        result.isSuccess()

        where:
        describeApiName | appId
        "AccountObj"    | "app1"
        "ContactObj"    | "app2"
    }

    @Unroll
    def "测试validateSceneName方法"() {
        given:
        def arg = new ValidateSceneName.Arg(
                sceneApiName: sceneApiName,
                displayName: displayName,
                id: id,
                extendAttribute: extendAttribute,
                appId: appId
        )

        when:
        def result = objectOuterTenantSceneService.validateSceneName(arg, context)

        then:
        1 * context.setAttribute("is_outer", true)
        1 * sceneLogicService.validateSceneApiNameRepeat(sceneApiName, displayName, id, extendAttribute, _) >> apiNameRepeat
        result.getApiNameRepeat() == apiNameRepeat

        where:
        sceneApiName | displayName | id    | extendAttribute | appId  | apiNameRepeat
        "scene1"     | "场景1"     | "id1" | "attr1"         | "app1" | true
        "scene2"     | "场景2"     | "id2" | "attr2"         | "app2" | false
    }

    @Unroll
    def "测试setDefaultScenePriority方法"() {
        given:
        def arg = new DefaultScenePriority.Arg(
                describeApiName: describeApiName,
                sceneApiNames: sceneApiNames,
                extendAttribute: extendAttribute,
                appId: appId
        )

        when:
        def result = objectOuterTenantSceneService.setDefaultScenePriority(arg, context)

        then:
        1 * context.setAttribute("is_outer", true)
        1 * sceneLogicService.setUpDefaultScenePriority(describeApiName, sceneApiNames, extendAttribute, _)
        result.isSuccess()

        where:
        describeApiName | sceneApiNames        | extendAttribute | appId
        "AccountObj"    | ["scene1", "scene2"] | "attr1"         | "app1"
        "ContactObj"    | ["scene3", "scene4"] | "attr2"         | "app2"
    }
} 