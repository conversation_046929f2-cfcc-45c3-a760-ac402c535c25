package com.facishare.paas.appframework.metadata.dto.scene;

import com.facishare.paas.metadata.impl.DocumentBasedBean;

import java.util.Map;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/06/03
 */
public class Order extends DocumentBasedBean implements IOrder {
    public Order() {
    }

    public Order(Map map) {
        super(map);
    }

    @Override
    public String getFieldName() {
        return get(FIELD_NAME, String.class);
    }

    @Override
    public void setFieldName(String fieldName) {
        set(FIELD_NAME, fieldName);
    }

    @Override
    public boolean isAsc() {
        return Boolean.TRUE.equals(get(IS_ASC, Boolean.class));
    }

    @Override
    public void setIsAsc(boolean isAsc) {
        set(IS_ASC, isAsc);
    }
}
