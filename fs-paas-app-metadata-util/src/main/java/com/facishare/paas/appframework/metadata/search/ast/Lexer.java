package com.facishare.paas.appframework.metadata.search.ast;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2025/5/19
 */
public class Lexer {
    private static final Pattern TOKEN_PATTERN = Pattern.compile(
            "\\s*(\\d+|AND|OR|\\(|\\))\\s*");

    public static List<Token> tokenize(String input) {
        List<Token> tokens = Lists.newArrayList();
        Matcher matcher = TOKEN_PATTERN.matcher(input);

        while (matcher.find()) {
            String value = matcher.group(1);
            if (value.matches("\\d+")) {
                tokens.add(new Token(TokenType.NUMBER, value));
            } else if (value.equals("AND")) {
                tokens.add(new Token(TokenType.AND, value));
            } else if (value.equals("OR")) {
                tokens.add(new Token(TokenType.OR, value));
            } else if (value.equals("(")) {
                tokens.add(new Token(TokenType.LPAREN, value));
            } else if (value.equals(")")) {
                tokens.add(new Token(TokenType.RPAREN, value));
            }
        }

        return tokens;
    }
}
