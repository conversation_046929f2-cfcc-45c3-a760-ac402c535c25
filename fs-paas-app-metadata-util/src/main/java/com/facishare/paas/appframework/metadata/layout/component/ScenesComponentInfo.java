package com.facishare.paas.appframework.metadata.layout.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/02/20
 */
public class ScenesComponentInfo extends AbstractComponentInfoDocument implements IScenesComponentInfo {
    private static final long serialVersionUID = 2490474085619020130L;

    public ScenesComponentInfo() {
    }

    public ScenesComponentInfo(Map map) {
        super(map);
    }

    public static IScenesComponentInfo defaultSceneInfo() {
        Map<String, Object> map = Maps.newHashMap();
        map.put(RENDER_TYPE, IListComponentInfo.RENDER_TYPE_DROP_DOWN);
        map.put(PAGE_TYPE, IComponentInfo.PAGE_TYPE_LIST);
        IScenesComponentInfo result = new ScenesComponentInfo(map);
        result.setOrder(Lists.newArrayList());
        result.setHidden(Lists.newArrayList());
        return result;
    }

    @Override
    public List<String> getOrder() {
        return getList(ORDER);
    }

    @Override
    public void setOrder(List<String> orders) {
        set(ORDER, orders);
    }

    @Override
    public List<String> getHidden() {
        return getList(HIDDEN);
    }

    @Override
    public void setHidden(List<String> hidden) {
        set(HIDDEN, hidden);
    }

    /**
     * @return 渲染类型
     */
    @Override
    public String getRenderType() {
        return get(RENDER_TYPE, String.class);
    }

    /**
     * @return 所属页面
     */
    @Override
    public String getPageType() {
        return get(PAGE_TYPE, String.class);
    }

    @Override
    public IScenesComponentInfo copy() {
        return new ScenesComponentInfo((Map) copy(map));
    }
}
