package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;

import java.util.List;
import java.util.Map;

/**
 * Created by linqy on 2018/01/11
 */
public interface PostActionService {

    /**
     * 创建后动作
     *
     * @param user
     * @param action
     * @return
     */
    IUdefAction createAction(User user, IUdefAction action);

    /**
     * 批量创建后动作
     *
     * @param user
     * @param actionList
     * @return
     */
    List<IUdefAction> bulkCreateAction(User user, List<IUdefAction> actionList);

    /**
     * 批量更新后动作
     *
     * @param user
     * @param actionList
     * @return
     */
    List<IUdefAction> bulkUpdateAction(User user, List<IUdefAction> actionList);

    /**
     * 查询后动作列表，不做任何处理，发现问题可联系相关负责人换接口
     *
     * @param user
     * @param describeApiName
     * @return
     */
    List<IUdefAction> findActionList(User user, List<String> idList, String describeApiName);

    /**
     * 查询后动作列表，只处理乡镇字段，不处理禁用和删除的字段信息
     *
     * @param user
     * @param button
     * @param describeApiName
     * @return
     */
    List<IUdefAction> findActionListForDesigner(User user, IUdefButton button, String describeApiName);

    /**
     * 查询后动作列表，处理乡镇字段和以及禁用和删除的字段信息
     *
     * @param user
     * @param button
     * @param describeApiName
     * @return
     */
    List<IUdefAction> findActionList(User user, IUdefButton button, String describeApiName);

    boolean bulkDeleteAction(User user, List<String> idList, String describeApiName);

    Map<String, List<IUdefAction>> findActionByApiNameListAndType(User user, List<String> apiNameList, String actionType);

    List<IUdefAction> findActionListByStage(User user, IUdefButton button, String describeApiName, String actionStage);
}
