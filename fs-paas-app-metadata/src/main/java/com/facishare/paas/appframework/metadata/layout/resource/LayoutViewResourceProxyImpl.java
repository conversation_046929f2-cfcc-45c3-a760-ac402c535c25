package com.facishare.paas.appframework.metadata.layout.resource;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.common.util.TenantUtil;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.model.User;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.release.GrayRule;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2022/6/27
 */
@Service
public class LayoutViewResourceProxyImpl implements LayoutViewResourceProxy {

    public static final String UDOBJ = "udobj";
    public static final String UDOBJ_EXTRA = "udobj_extra";
    private Map<String, List<LayoutViewConf>> LayoutViewConfMap = Maps.newHashMap();


    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-paas-appframework-config", config -> {
            String layoutViewsConf = config.get("layout_views_conf");
            loadConf(layoutViewsConf);
        });
    }

    @Override
    public List<LayoutViewDTO> findLayoutViewResource(User user, String describeApiName) {
        List<LayoutViewConf> layoutViewConfs = Lists.newArrayList(CollectionUtils.nullToEmpty(LayoutViewConfMap.get(describeApiName)));
        if (CollectionUtils.empty(layoutViewConfs)) {
            layoutViewConfs.addAll(CollectionUtils.nullToEmpty(LayoutViewConfMap.get(UDOBJ)));
            layoutViewConfs.addAll(CollectionUtils.nullToEmpty(LayoutViewConfMap.get(UDOBJ_EXTRA)));
        }
        if (CollectionUtils.empty(layoutViewConfs)) {
            return Collections.emptyList();
        }
        List<LayoutViewDTO> viewList = Lists.newArrayList();
        // 如果同一对象下存在多个匹配的结果,只返回第一个配置
        for (LayoutViewConf layoutViewConf : layoutViewConfs) {
            if (layoutViewConf.isAllow(user.getTenantId())) {
                viewList.addAll(layoutViewConf.getViews().stream()
                        .filter(x -> TenantUtil.judgmentGrayByConfigKey(user.getTenantId(), x.getGrayKey()))
                        .collect(Collectors.toList()));
            }
        }
        viewList = viewList.stream().distinct().collect(Collectors.toList());
        return viewList;
    }

    private void loadConf(String layoutViewsConf) {
        List<LayoutViewConf> layoutViewConfs = JacksonUtils.fromJson(layoutViewsConf, new TypeReference<List<LayoutViewConf>>() {
        });

        if (CollectionUtils.empty(layoutViewConfs)) {
            return;
        }
        Map<String, List<LayoutViewConf>> map = Maps.newHashMap();
        for (LayoutViewConf layoutViewConf : layoutViewConfs) {
            String objectApiName = layoutViewConf.getObjectApiName();
            map.computeIfAbsent(objectApiName, key -> Lists.newArrayList()).add(layoutViewConf);
        }
        LayoutViewConfMap = map;
    }

    @Getter
    @ToString
    @EqualsAndHashCode(of = {"apiName"})
    public static class LayoutViewDTO {
        private final String apiName;
        private final String label;
        private final String labelI18NKey;

        private final Boolean defaultShow;
        private final Boolean defaultView;
        private final String grayKey;

        @JsonCreator
        public LayoutViewDTO(@JsonProperty("apiName") String apiName,
                             @JsonProperty("label") String label,
                             @JsonProperty("labelI18NKey") String labelI18NKey,
                             @JsonProperty("defaultShow") Boolean defaultShow,
                             @JsonProperty("defaultView") Boolean defaultView,
                             @JsonProperty("grayKey") String grayKey) {
            this.apiName = apiName;
            this.label = label;
            this.labelI18NKey = labelI18NKey;
            this.defaultShow = defaultShow;
            this.defaultView = defaultView;
            this.grayKey = grayKey;
        }

        public String getLabel() {
            return I18NExt.getOrDefault(labelI18NKey, label);
        }
    }


    @ToString
    @EqualsAndHashCode
    private static class LayoutViewConf {
        @Getter
        private final String objectApiName;
        private final GrayRule grayRule;
        @Getter
        private final List<LayoutViewDTO> views;

        public boolean isAllow(String euid) {
            return grayRule.isAllow(euid);
        }

        private LayoutViewConf(String objectApiName, GrayRule grayRule, List<LayoutViewDTO> views) {
            this.objectApiName = objectApiName;
            this.grayRule = grayRule;
            this.views = ImmutableList.copyOf(views);
        }

        @JsonCreator
        public static LayoutViewConf of(@JsonProperty("objectApiName") String objectApiName,
                                        @JsonProperty("gray") String gray,
                                        @JsonProperty("views") List<LayoutViewDTO> views) {
            GrayRule grayRule = new GrayRule(gray);
            return new LayoutViewConf(objectApiName, grayRule, views);
        }
    }
}
