package com.facishare.paas.appframework.core.predef.action

import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.data.IUniqueRule
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe
import com.fxiaoke.api.IdGenerator
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class BaseImportVerifyActionTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def objectApiName = "object_123__c"
    def actionCode = "BaseImportVerify"
    def tenantId = "74255"
    def userId = "1000"
    def outTenantId = "200074255"
    def outUserId = "100018916"
    def user = User.builder().tenantId(tenantId).userId(userId).outTenantId(outTenantId).outUserId(outUserId).build()
    def requestContext = RequestContext.builder().tenantId(tenantId).user(user).build()
    def actionContext = new ActionContext(requestContext, objectApiName, actionCode)

    ServiceFacade serviceFacade
    InfraServiceFacade infraServiceFacade
    IObjectDescribe objectDescribe
    SpringBeanHolder springBeanHolder
    List<IFieldDescribe> fieldDescribeList = Lists.newArrayList()

    def textFieldApiName = "field_text__c"

    def setup() {
        serviceFacade = Mock()
        infraServiceFacade = Mock()
        objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(objectApiName)
        objectDescribe.setIsActive(true)
        springBeanHolder = Mock(SpringBeanHolder)
        infraServiceFacade.getSpringBeanHolder() >> springBeanHolder

        TextFieldDescribe textFieldDescribe = new TextFieldDescribe()
        textFieldDescribe.setApiName(textFieldApiName)
        fieldDescribeList.add(textFieldDescribe)
        RecordTypeFieldDescribe recordTypeFieldDescribe = new RecordTypeFieldDescribe()
        recordTypeFieldDescribe.setApiName("record_type")
        fieldDescribeList.add(recordTypeFieldDescribe)

        objectDescribe.setFieldDescribes(fieldDescribeList)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试BaseImportVerifyAction的doAct方法，验证不同匹配类型、导入类型和字段映射支持的组合场景，包括异常场景
     */
    def "test BaseImportVerifyAction doAct"() {
        given:
        ObjectDataDocument dataDocument = new ObjectDataDocument()
        if (value != null) {
            dataDocument.put(textFieldApiName, value)
        }

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                rows: [dataDocument],
                matchingType: matchingType,
                importType: importType,
                specifiedField: textFieldApiName,
                supportFieldMapping: supportMapping
        )

        Map<String, List<BaseImportAction.FieldMapping>> fieldMappingMap = Maps.newHashMap()
        List<BaseImportAction.FieldMapping> fieldMappings = Lists.newArrayList()
        BaseImportAction.FieldMapping fieldMapping = new BaseImportAction.FieldMapping()
        fieldMapping.setApiName(textFieldApiName)
        fieldMapping.setColIndex("1")
        fieldMappings.add(fieldMapping)
        fieldMappingMap.put(objectApiName, fieldMappings)

        // 创建必填字段描述
        TextFieldDescribe fieldDescribe = new TextFieldDescribe()
        fieldDescribe.setApiName(textFieldApiName)
        fieldDescribe.setRequired(true)
        fieldDescribe.setLabel("测试字段")
        fieldDescribeList.clear()
        fieldDescribeList.add(fieldDescribe)

        StandardInsertImportVerifyAction action = new StandardInsertImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                infraServiceFacade: infraServiceFacade,
                arg: arg,
                fieldMappings: fieldMappingMap
        )

        when:
        Whitebox.setInternalState(action, "isFuncPrivilegePass", isFuncPrivilegePass)
        if (!isFuncPrivilegePass) {
            serviceFacade.findObject(actionContext.getTenantId(), actionContext.getObjectApiName()) >> objectDescribe
            Whitebox.setInternalState(action, "permissionErrorMessage", "No permission to import")
        }

        // 设置必要的 mock
        objectDescribe.getFieldDescribes() >> fieldDescribeList
        objectDescribe.getFieldDescribe(textFieldApiName) >> fieldDescribe
        objectDescribe.isActive() >> true
        objectDescribe.getDisplayName() >> "测试对象"

        // 确保 infraServiceFacade 返回正确的字段列表
        infraServiceFacade.getTemplateField(actionContext.getUser(), objectDescribe) >> fieldDescribeList

        // 设置 validFieldList
        Whitebox.setInternalState(action, "validFieldList", fieldDescribeList)

        def result = action.doAct(arg)
        println "Test result: success=${result.success}, message=${result.message}, value=${value}, isFuncPrivilegePass=${isFuncPrivilegePass}"

        then:
        noExceptionThrown()
        // 只有在权限检查失败时，才期望 success 为 false
        if (!isFuncPrivilegePass) {
            assert !result.success
            assert result.message == "No permission to import"
        } else {
            // 所有其他情况都期望成功，且没有错误消息
            assert result.success
            assert result.message == null
        }

        where:
        matchingType | importType | supportMapping | isFuncPrivilegePass | value  | expectSuccess
        1            | 1          | true           | true                | "test" | true
        2            | 1          | true           | true                | "test" | true
        3            | 1          | true           | true                | "test" | true
        4            | 1          | true           | true                | "test" | true
        1            | 1          | false          | true                | "test" | true
        2            | 1          | false          | true                | "test" | true
        3            | 1          | false          | true                | "test" | true
        4            | 1          | false          | true                | "test" | true
        1            | 1          | false          | false               | "test" | false  // 只有权限检查失败时才期望失败
        1            | 1          | true           | true                | null   | true   // 空值也应该成功
        1            | 1          | true           | true                | ""     | true   // 空字符串也应该成功
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试BaseImportVerifyAction的verifyUniqueRule方法，验证唯一性规则校验功能，包括有效和无效的规则场景
     */
    def "test BaseImportVerifyAction verifyUniqueRule"() {
        given:
        IUniqueRule uniqueRule = new IUniqueRule()
        uniqueRule.setEffective(isEffective)
        uniqueRule.setUseWhenImportExcel(useWhenImportExcel)
        IUniqueRule.Rule rule = new IUniqueRule.Rule()
        IUniqueRule.Condition condition = new IUniqueRule.Condition()
        condition.setFieldName(textFieldApiName)
        List<IUniqueRule.Condition> conditions = Lists.newArrayList()
        conditions.add(condition)
        rule.setConditions(conditions)
        List<IUniqueRule.Rule> rules = Lists.newArrayList(rule)
        IUniqueRule.VersionedRules versionedRules = IUniqueRule.VersionedRules.builder().rules(rules).version(1).build()
        uniqueRule.setUseableRules(versionedRules)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                matchingType: matchingType,
                importType: importType,
                specifiedField: textFieldApiName,
                supportFieldMapping: supportMapping
        )

        StandardInsertImportVerifyAction action = new StandardInsertImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                infraServiceFacade: infraServiceFacade,
                arg: arg,
                uniqueRule: uniqueRule
        )

        IObjectData objectData = new ObjectData()
        if (value != null) {
            objectData.set(textFieldApiName, value)
        }

        when:
        def result = action.verifyUniqueRule(fieldDescribeList, objectData)
        println "verifyUniqueRule result: ${result}"

        then:
        noExceptionThrown()
        if (expectSuccess) {
            assert result == null
        } else {
            assert result != null
        }

        where:
        matchingType | importType | supportMapping | isEffective | useWhenImportExcel | value  | expectSuccess
        1            | 1          | true           | true        | true               | "test" | true
        3            | 1          | true           | true        | true               | "test" | true
        1            | 1          | true           | false       | true               | "test" | true
        1            | 1          | true           | true        | false              | "test" | false
        1            | 1          | true           | true        | true               | null   | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试BaseImportVerifyAction的verifyFieldsByUniqueRule方法，验证基于唯一性规则的字段校验功能，包括有效和无效的数据场景
     */
    def "test BaseImportVerifyAction verifyFieldsByUniqueRule"() {
        given:
        IObjectData objectData = new ObjectData()
        objectData.setId(IdGenerator.get())
        if (hasValue) {
            objectData.set(textFieldApiName, value)
        }

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                matchingType: matchingType,
                importType: importType,
                specifiedField: "field_text__c",
                supportFieldMapping: supportMapping
        )

        Map<String, List<BaseImportAction.FieldMapping>> fieldMappingMap = Maps.newHashMap()
        List<BaseImportAction.FieldMapping> fieldMappings = Lists.newArrayList()
        BaseImportAction.FieldMapping fieldMapping = new BaseImportAction.FieldMapping()
        fieldMapping.setApiName(textFieldApiName)
        fieldMapping.setColIndex("1")
        fieldMappings.add(fieldMapping)
        fieldMappingMap.put(objectApiName, fieldMappings)

        StandardUpdateImportVerifyAction action = new StandardUpdateImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                infraServiceFacade: infraServiceFacade,
                arg: arg,
                fieldMappings: fieldMappingMap
        )

        when:
        def result = action.verifyFieldsByUniqueRule(fieldDescribeList, objectData)

        then:
        noExceptionThrown()
        if (expectSuccess) {
            assert result == null
        } else {
            assert result != null
        }

        where:
        matchingType | importType | supportMapping | hasValue | value  | expectSuccess
        1            | 1          | true           | true     | "test" | true
        2            | 1          | true           | true     | "test" | true
        3            | 1          | true           | true     | "test" | true
        4            | 1          | true           | true     | "test" | true
        1            | 1          | false          | true     | "test" | true
        1            | 1          | true           | false    | null   | false
        1            | 1          | true           | true     | ""     | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试BaseImportVerifyAction的validateFieldsBySpecifiedField方法，验证指定字段的校验功能，包括必填和唯一性校验
     */
    def "test BaseImportVerifyAction validateFieldsBySpecifiedField"() {
        given:
        ObjectDataDocument document = new ObjectDataDocument()
        if (hasValue) {
            document.put(specifiedField, value)
        }

        IObjectDescribe describe = new ObjectDescribe()
        List<IFieldDescribe> fieldDescribeList = Lists.newArrayList()
        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        fieldDescribe.setUnique(unique)
        fieldDescribe.setLabel("ceshi")
        fieldDescribe.setApiName(specifiedField)
        fieldDescribe.setRequired(required)
        fieldDescribeList.add(fieldDescribe)

        describe.setFieldDescribes(fieldDescribeList)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                specifiedField: specifiedField,
                importTitles: ["测试字段"]  // 添加必要的导入标题
        )

        StandardUpdateImportVerifyAction action = new StandardUpdateImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: describe,
                serviceFacade: serviceFacade,
                infraServiceFacade: infraServiceFacade,
                arg: arg
        )

        when:
        def result = action.validateFieldsBySpecifiedField(fieldDescribeList, document.entrySet())
        println "validateFieldsBySpecifiedField result: ${result}"

        then:
        noExceptionThrown()
        if (expectSuccess) {
            assert result == ""
        } else {
            assert result != ""
        }

        where:
        specifiedField  | unique | required | hasValue | value  | expectSuccess
        "field_text__c" | true   | true     | true     | "test" | true
        "field_text__c" | false  | true     | true     | "test" | true
        "field_text__c" | true   | true     | false    | null   | false
        "field_text__c" | true   | true     | true     | ""     | false
        "field_text__c" | true   | false    | false    | null   | true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试BaseImportVerifyAction的validateFieldsByUniqueRule方法，验证基于唯一性规则的字段验证功能，包括不同的数据场景
     */
    def "test BaseImportVerifyAction validateFieldsByUniqueRule"() {
        given:
        ObjectDataDocument document = new ObjectDataDocument()
        if (hasValue) {
            document.put(textFieldApiName, value)
        }

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                matchingType: matchingType,
                importType: importType,
                specifiedField: textFieldApiName,
                supportFieldMapping: supportMapping,
                importTitles: ["测试字段"]  // 添加必要的导入标题
        )

        StandardUpdateImportVerifyAction action = new StandardUpdateImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                infraServiceFacade: infraServiceFacade,
                arg: arg
        )

        when:
        def result = action.validateFieldsByUniqueRule(fieldDescribeList, document.entrySet())
        println "validateFieldsByUniqueRule result: ${result}"

        then:
        noExceptionThrown()
        if (expectSuccess) {
            assert result == ""
        } else {
            assert result != ""
        }

        where:
        matchingType | importType | supportMapping | hasValue | value  | expectSuccess
        1            | 1          | true           | true     | "test" | true
        2            | 1          | true           | true     | "test" | true
        3            | 1          | true           | true     | "test" | true
        4            | 1          | true           | true     | "test" | true
        1            | 1          | true           | false    | null   | false
        1            | 1          | true           | true     | ""     | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试BaseImportVerifyAction的validateUniqueRule方法，验证唯一性规则的验证功能，包括不同的规则场景
     */
    def "test BaseImportVerifyAction validateUniqueRule"() {
        given:
        ObjectDataDocument document = new ObjectDataDocument()
        if (hasValue) {
            document.put(textFieldApiName, value)
        }

        IUniqueRule uniqueRule = new IUniqueRule()
        uniqueRule.setEffective(isEffective)
        uniqueRule.setUseWhenImportExcel(useWhenImportExcel)
        IUniqueRule.Rule rule = new IUniqueRule.Rule()
        IUniqueRule.Condition condition = new IUniqueRule.Condition()
        condition.setFieldName(textFieldApiName)
        List<IUniqueRule.Condition> conditions = Lists.newArrayList()
        conditions.add(condition)
        rule.setConditions(conditions)
        List<IUniqueRule.Rule> rules = Lists.newArrayList(rule)
        IUniqueRule.VersionedRules versionedRules = IUniqueRule.VersionedRules.builder().rules(rules).version(1).build()
        uniqueRule.setUseableRules(versionedRules)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                matchingType: matchingType,
                importType: importType,
                specifiedField: textFieldApiName,
                supportFieldMapping: supportMapping,
                importTitles: ["测试字段"]  // 添加必要的导入标题
        )

        StandardInsertImportVerifyAction action = new StandardInsertImportVerifyAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                infraServiceFacade: infraServiceFacade,
                arg: arg,
                uniqueRule: uniqueRule
        )

        when:
        def result = action.validateUniqueRule(fieldDescribeList, document.entrySet())
        println "validateUniqueRule result: ${result}"

        then:
        noExceptionThrown()
        if (expectSuccess) {
            assert result == ""
        } else {
            assert result != ""
        }

        where:
        matchingType | importType | supportMapping | isEffective | useWhenImportExcel | hasValue | value  | expectSuccess
        1            | 1          | true           | true        | true               | true     | "test" | true
        3            | 1          | true           | true        | true               | true     | "test" | true
        1            | 1          | true           | false       | true               | true     | "test" | true
        1            | 1          | true           | true        | false              | true     | "test" | false
        1            | 1          | true           | true        | true               | false    | null   | false
        1            | 1          | true           | true        | true               | true     | ""     | false
    }

}
