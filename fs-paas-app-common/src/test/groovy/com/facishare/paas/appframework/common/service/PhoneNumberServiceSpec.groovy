package com.facishare.paas.appframework.common.service

import com.facishare.paas.appframework.common.service.dto.Captcha
import com.facishare.paas.appframework.common.service.dto.CheckSmsStatus
import com.facishare.paas.appframework.common.service.dto.GetImageCode
import com.facishare.paas.appframework.common.service.dto.VerifyValidateCode
import com.facishare.paas.appframework.core.model.User
import com.facishare.userlogin.api.model.captcha.BuildCodeDto
import com.facishare.userlogin.api.service.ImageCodeService
import com.facishare.userlogin.api.service.ValidateCodeService
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class PhoneNumberServiceSpec extends Specification {
	String tenantId = "74255"
	def smsProxy = Mock(SmsServiceProxy)
	def validateCodeService = Mock(ValidateCodeService)
	def imageCodeService = Mock(ImageCodeService)
	def appCaptchaService = Mock(AppCaptchaService)
	
	PhoneNumberServiceImpl phoneNumberService
	
	def setupSpec() {
        def i18nClient = Mock(com.fxiaoke.i18n.client.I18nClient)
        def i18nServiceImpl = Mock(com.fxiaoke.i18n.client.impl.I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(com.fxiaoke.i18n.client.I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

	def setup() {
		phoneNumberService = new PhoneNumberServiceImpl()
		Whitebox.setInternalState(phoneNumberService, "smsServiceProxy", smsProxy)
		phoneNumberService.setCaptchaService(appCaptchaService)
		
		// 确保appCaptchaService已正确设置且不为null
		assert phoneNumberService.appCaptchaService != null
	}

	def "test checkSmsStatus"() {
		given:
		smsProxy.checkSmsStatus(_ as CheckSmsStatus.Arg) >> smsStatus

		expect:
		result == phoneNumberService.checkSmsStatus(ei)

		where:
		ei  | smsStatus                              || result
		"1" | new CheckSmsStatus.Result("status": 1) || false
		"2" | new CheckSmsStatus.Result("status": 0) || true
	}

	def "test refreshCaptcha"() {
		given: "模拟AppCaptchaService.createCaptchaCode返回完整的Captcha.Result对象"
		def captchaResult = Captcha.Result.builder()
				.epxId("test-captcha-id")
				.data("test-captcha-data".bytes)
				.build()
		appCaptchaService.createCaptchaCode(null) >> captchaResult

		when: "调用refreshCaptcha方法"
		def result = phoneNumberService.refreshCaptcha()

		then: "验证正确调用了AppCaptchaService.createCaptchaCode方法"
		1 * appCaptchaService.createCaptchaCode(null)
		
		and: "验证返回值包含了正确的数据"
		result != null
		result.epxId == "test-captcha-id"
		result.data != null
	}
}
