package com.facishare.paas.appframework.core.predef.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.privilege.EnterpriseRelationServiceImpl;
import com.facishare.paas.appframework.prm.PartnerCoreService;
import com.facishare.paas.appframework.prm.PartnerCoreServiceImpl;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.enterpriserelation2.result.RelationDownstreamResult;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public abstract class BaseUpdatePartnerAction<A, Result> extends BaseObjectApprovalAction<A, Result> {

    protected List<IObjectData> objectDataList = null;
    protected EnterpriseRelationServiceImpl enterpriseRelationService = SpringUtil.getContext().getBean(EnterpriseRelationServiceImpl.class);
    protected PartnerCoreService partnerCoreService = SpringUtil.getContext().getBean(PartnerCoreServiceImpl.class);

    @Override
    protected void before(A arg) {
        super.before(arg);
    }

    protected String getPartnerName(IObjectData objectData) {
        String partnerName = objectData.get(PrmConstant.FIELD_PARTNER_ID + "__r", String.class);
        if (Strings.isNullOrEmpty(partnerName)) {
            String partnerId = objectData.get(PrmConstant.FIELD_PARTNER_ID, String.class);
            if (!Strings.isNullOrEmpty(partnerId)) {
                IActionContext context = ActionContextExt.of(actionContext.getUser(),
                        actionContext.getRequestContext()).getContext();
                List<INameCache> recordName = serviceFacade.findRecordName(context, Utils.PARTNER_API_NAME, Lists.newArrayList(partnerId));
                if (CollectionUtils.notEmpty(recordName)) {
                    return recordName.get(0).getName();
                }
            }
        }
        return "";
    }


    protected List<IObjectData> findByIdsIncludeLookUpName(User user, String apiName, List<String> ids) {
        return ids.stream().map(id -> serviceFacade.findObjectData(user, id, apiName)).collect(Collectors.toList());
    }


    protected Tuple<Integer, Long> changePartnerAndOwner(User user, List<IObjectData> dataList, String partnerId) {
        return this.changePartnerAndOwner(user, dataList, partnerId, 0l);
    }


    protected Tuple<Integer, Long> changePartnerAndOwner(User user, List<IObjectData> dataList, String partnerId, Long outOwnerId) {
        Integer outTenantId = 0;
        if (!StringUtils.isEmpty(partnerId)) {
            Map<String, RelationDownstreamResult> downstreamMap = enterpriseRelationService.getRelationDownstreamInfo(user.getTenantId(), Sets.newHashSet(partnerId));
            RelationDownstreamResult downstream = downstreamMap.get(partnerId);
            if (downstream != null) {
                //设置外部企业和外部负责人
                outTenantId = downstream.getDownstreamOuterTenantId();
                if (outOwnerId == 0L) {
                    outOwnerId = downstream.getRelationOwnerOuterUid();
                    // 2019/4/11 兼容深研接口通过扫码创建下游，但是无外部负责人的清理，导致relationOwnerOuterId为null
                    if (outOwnerId == null) {
                        outOwnerId = 0L;
                    }
                }
            } else {
                log.warn("get downstream relation data is null, tenant:{}", user.getTenantId());
                outOwnerId = 0L;
            }
        }
        for (IObjectData objectData : dataList) {
            objectData.set(ObjectDataExt.PARTNER_ID, partnerId);
            String outTenant = outTenantId == 0 ? null : outTenantId.toString();
            objectData.setOutTenantId(outTenant);
            objectData.setOutOwner(outOwnerId == 0 ? null : Lists.newArrayList(outOwnerId.toString()));
            String outOwner = outOwnerId == 0 ? null : outOwnerId.toString();
            updateOwnerToTeamMember(objectData, outOwner, outTenant);
        }
        List<String> updateFields = Lists.newArrayList(ObjectDataExt.OUTER_OWNER, ObjectDataExt.PARTNER_ID,
                ObjectDataExt.RELEVANT_TEAM, ObjectDataExt.OUTER_TENANT);

        serviceFacade.batchUpdateByFields(user, dataList, updateFields);
        return Tuple.of(outTenantId, outOwnerId);
    }

    protected void updateOwnerToTeamMember(IObjectData objectData, String outOwner, String outTenantId) {
        //如果是从对象,则不需要处理相关团队,因为从对象的相关团队是绑定在主上的。
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            return;
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
        //从原相关团队中获取原外部负责人
        Optional<TeamMember> oldOwnerTeamMemberOpt =
                teamMembers.stream().filter(f -> f.getRole() == TeamMember.Role.OWNER
                        && f.isOutMember()).findFirst();
        if (oldOwnerTeamMemberOpt.isPresent()) {
            //移除原外部负责人
            teamMembers.remove(oldOwnerTeamMemberOpt.get());
        }
        //从原相关团队中获取新外部负责人,如果能找到,也移除,避免在后面被二次添加
        teamMembers = teamMembers.stream()
                .filter(it -> !it.getEmployee().equals(outOwner)).collect(Collectors.toList());

        //添加新外部负责人
        if (!Strings.isNullOrEmpty(outOwner)) {
            teamMembers.add(new TeamMember(outOwner, TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE, outTenantId));
        }

        objectDataExt.setTeamMembers(teamMembers);
    }

    protected void dealDetail(List<IObjectData> masterDataList, String partnerId, Integer outTenantId, Long outOwnerId) {
        if (CollectionUtils.empty(masterDataList)) {
            return;
        }
        List<IObjectDescribe> relatedDescribes = serviceFacade.findDetailDescribes(actionContext.getTenantId(),
                objectDescribe.getApiName());
        if (CollectionUtils.empty(relatedDescribes)) {
            return;
        }

        for (IObjectDescribe item : relatedDescribes) {
            for (IObjectData masterObjectData : masterDataList) {
                try {
                    List<IObjectData> detailDataList = serviceFacade.findDetailObjectDataList(item, masterObjectData,
                            actionContext.getUser());
                    if (CollectionUtils.empty(detailDataList)) {
                        continue;
                    }
                    String dataOutTenantId = masterObjectData.getOutTenantId();
                    List outOwner = masterObjectData.getOutOwner();
                    for (IObjectData objectData : detailDataList) {
                        objectData.set(ObjectDataExt.PARTNER_ID, partnerId);
                        objectData.setOutTenantId(dataOutTenantId);
                        objectData.setOutOwner(outOwner);
                    }
                    List<String> updateFields = Lists.newArrayList(ObjectDataExt.OUTER_OWNER, ObjectDataExt.OUTER_TENANT,
                            ObjectDataExt.PARTNER_ID);
                    serviceFacade.batchUpdateByFields(actionContext.getUser(), detailDataList, updateFields);
                } catch (Exception e) {
                    log.error("cascade dealDetail error,tenantId:{},masterApiName:{},masterDataId:{}," +
                                    "detailApiName:{}",
                            actionContext.getTenantId(), objectDescribe.getApiName(), masterObjectData.getId(),
                            item.getApiName(), e);
                }
            }
        }
    }

    @Override
    protected IObjectData getPreObjectData() {
        if (CollectionUtils.empty(objectDataList)) {
            return null;
        }
        return objectDataList.get(0);
    }

    @Override
    protected IObjectData getPostObjectData() {
        if (CollectionUtils.empty(objectDataList)) {
            return null;
        }
        return objectDataList.get(0);
    }

    @Override
    protected Map<String, List<IObjectData>> getPostObjectDetails() {
        Map<String, List<IObjectData>> detailDataMap = new HashMap<>();
        User user = actionContext.getUser();
        if (serviceFacade.isMasterObject(user.getTenantId(), objectDescribe.getApiName())) {
            //获取所有的从describe
            List<IObjectDescribe> detailDescribes = serviceFacade.findDetailDescribes(user.getTenantId(), objectDescribe.getApiName());
            //获取所有的从数据
            detailDataMap = serviceFacade.findDetailObjectDataList(detailDescribes, objectDataList.get(0), user);
        }
        return detailDataMap;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private String errorDetail;
        private String errorCode;
        private String message;

        public boolean isSuccess() {
            if ("0".equals(errorCode)) {
                return true;
            }
            return false;
        }
    }
}
