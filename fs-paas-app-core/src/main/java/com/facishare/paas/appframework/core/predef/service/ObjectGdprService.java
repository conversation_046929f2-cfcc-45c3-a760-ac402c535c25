package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.UdobjGrayKey;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.gdpr.*;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.gdpr.GdprFormData;
import com.facishare.paas.appframework.metadata.gdpr.GdprService;
import com.facishare.paas.appframework.metadata.repository.model.GdprCompliance;
import com.facishare.paas.appframework.metadata.repository.model.GdprLegalBase;
import com.facishare.paas.appframework.metadata.repository.model.GdprProjectRequest;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.AggFunctionArg;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.gdpr.GdprServiceImpl.GDPR_AGREE_PERSON;

/**
 * <AUTHOR>
 * @date 2021/5/8 11:53
 */
@Slf4j
@Service
@ServiceModule("gdpr")
public class ObjectGdprService {

    @Autowired
    private GdprService gdprService;
    @Autowired
    private MetaDataFindService metaDataFindService;
    @Autowired
    private DescribeLogicService describeLogicService;

    //全部
    public static final String UNTREATED = "untreated";

    //------------------------------------------ gdpr 后台配置相关接口 start----------------------------------------------
    @ServiceMethod("open_compliance")
    public OpenGdprCompliance.Result openGdprCompliance(OpenGdprCompliance.Arg arg, ServiceContext context) {
        try {
            GdprCompliance gdprCompliance = buildGdprCompliance(arg);
            gdprService.openGdprCompliance(context.getUser(), gdprCompliance);
        } catch (Exception e) {
            log.warn("openGdprCompliance failed! user:{}", context.getUser(), e);
            throw new MetaDataBusinessException(I18NExt.text(I18NKey.ENABLE_GDPR_FAILURE_CONTACT_FENXIANG_CUSTOMER_SERVICE));
        }
        return OpenGdprCompliance.Result.builder().build();
    }

    private GdprCompliance buildGdprCompliance(OpenGdprCompliance.Arg arg) {
        GdprCompliance gdprCompliance = new GdprCompliance();
        gdprCompliance.setApiNames(arg.getApiNames());
        gdprCompliance.setForbidOpenApi(arg.getForbidOpenApi());
        gdprCompliance.setForbidExport(arg.getForbidExport());
        gdprCompliance.setUnusableOperation(arg.getUnusableOperation());
        gdprCompliance.setPeriod(arg.getPeriod());
        gdprCompliance.setPeriodType(arg.getPeriodType());
        return gdprCompliance;
    }

    @ServiceMethod("close_compliance")
    public CloseGdprCompliance.Result closeGdprCompliance(CloseGdprCompliance.Arg arg, ServiceContext context) {
        try {
            gdprService.closeGdprCompliance(context.getUser());
        } catch (Exception e) {
            log.warn("closeGdprCompliance failed! user:{}", context.getUser(), e);
            throw new MetaDataBusinessException(I18NExt.text(I18NKey.ENABLE_GDPR_FAILURE_CONTACT_FENXIANG_CUSTOMER_SERVICE));
        }
        return CloseGdprCompliance.Result.builder().build();
    }

    @ServiceMethod("find_compliance_status")
    public FindGdprComplianceStatus.Result findGdprComplianceStatus(FindGdprComplianceStatus.Arg arg, ServiceContext context) {
        boolean status = gdprService.findGdprComplianceStatusByCache(context.getUser(), arg.getApiName());
        // 判断是否是灰度企业，即是否已迁移到字段扩展表
        boolean migrated = UdobjGrayConfig.isAllow(UdobjGrayKey.FIELD_COMPLIANCE_SETTING_GRAY, context.getUser().getTenantId());
        return FindGdprComplianceStatus.Result.builder()
                .success(status)
                .migrated(migrated)
                .build();
    }

    @ServiceMethod("find_compliance_data")
    public FindGdprComplianceData.Result findGdprComplianceData(FindGdprComplianceData.Arg arg, ServiceContext context) {
        Optional<GdprCompliance> compliance = gdprService.findGdprComplianceData(context.getUser());
        if (compliance.isPresent()) {
            FindGdprComplianceData.GdprComplianceData result = FindGdprComplianceData.GdprComplianceData.builder().build();
            result.setUnusableOperation(compliance.get().getUnusableOperation());
            result.setForbidExport(compliance.get().getForbidExport());
            result.setForbidOpenApi(compliance.get().getForbidOpenApi());
            result.setApiNames(compliance.get().getApiNames());
            result.setPeriod(compliance.get().getPeriod());
            result.setPeriodType(compliance.get().getPeriodType());
            return FindGdprComplianceData.Result.builder().result(result).build();
        }
        return FindGdprComplianceData.Result.builder().build();
    }

    @ServiceMethod("find_personal_field")
    public FindGdprPersonalField.Result findGdprPersonalField(FindGdprPersonalField.Arg arg, ServiceContext context) {
        FindGdprPersonalField.Result result = FindGdprPersonalField.Result.builder().build();
        List<GdprCompliance> personalField = gdprService.findGdprCompliance(context.getUser(), arg.getApiName());
        personalField.stream().filter(x -> Objects.equals(x.getApiName(), arg.getApiName()))
                .findFirst()
                .ifPresent(x -> {
                    result.setOrdinaryFields(x.getOrdinaryFields());
                    result.setSensitiveFields(x.getSensitiveFields());
                });
        return result;
    }

    @ServiceMethod("update_personal_field")
    public UpdateGdprPersonalField.Result updateGdprPersonalField(UpdateGdprPersonalField.Arg arg, ServiceContext context) {
        gdprService.updateGdprPersonalField(context.getUser(), arg.getApiName(), arg.getSensitiveFields(), arg.getOrdinaryFields());
        return UpdateGdprPersonalField.Result.builder().build();
    }

    @ServiceMethod("find_legal_base_view")
    public FindGdprLegalBaseView.Result findGdprLegalBaseView(FindGdprLegalBaseView.Arg arg, ServiceContext context) {
        FindGdprLegalBaseView.LegalBase legalBaseResult = FindGdprLegalBaseView.LegalBase.builder().build();
        FindGdprLegalBaseView.Agree agreeResult = new FindGdprLegalBaseView.Agree();
        if (context == null || arg == null) {
            log.warn("findGdprLegalBaseView param is null, arg:{},context:{}", arg, context);
            return FindGdprLegalBaseView.Result.builder().legalBase(legalBaseResult).agree(agreeResult).build();
        }
        //1.用于查询全部场景的数据总量，只需要精确总数
        Query query = Query.builder().limit(1).findExplicitTotalNum(true).build();
        query.and(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, context.getTenantId()).getFilter());
        query.and(FilterExt.of(Operator.EQ, IObjectData.DESCRIBE_API_NAME, arg.getApiName()).getFilter());
        query.and(FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter());
        QueryResult<IObjectData> dataQueryResult = metaDataFindService.findBySearchQuery(context.getUser(), arg.getApiName(),
                (SearchTemplateQuery) query.toSearchTemplateQuery());
        if (Objects.isNull(dataQueryResult) || dataQueryResult.getTotalNumber() == 0) {
            return FindGdprLegalBaseView.Result.builder().legalBase(legalBaseResult).agree(agreeResult).build();
        }
        //设置全部场景的totalCount
        legalBaseResult.setUntreated(dataQueryResult.getTotalNumber());


        List<IObjectData> aggregateResult = aggregateLegalBaseData(arg, context.getUser());
        if (CollectionUtils.empty(aggregateResult)) {
            return FindGdprLegalBaseView.Result.builder().legalBase(legalBaseResult).agree(agreeResult).build();
        }

        buildResult(legalBaseResult, agreeResult, aggregateResult);
        return FindGdprLegalBaseView.Result.builder().legalBase(legalBaseResult).agree(agreeResult).build();
    }

    private List<IObjectData> aggregateLegalBaseData(FindGdprLegalBaseView.Arg arg, User user) {
        //用于联表查询gdpr_legal_base与mt_data或者其他数据专表的数据。
        Query searchQuery = Query.builder().limit(1).findExplicitTotalNum(true).build();
        searchQuery.and(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter());
        searchQuery.and(FilterExt.of(Operator.EQ, GdprLegalBase.GDPR_LEGAL_BASE_API_NAME, arg.getApiName()).getFilter());
        searchQuery.and(FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter());

        //构造聚合条件
        AggFunctionArg functionArg = AggFunctionArg.builder().aggFunction("count").aggField("id").build();

        List<IObjectData> aggregateResult = metaDataFindService.aggregateFindBySearchQueryWithGroupFields(user,
                (SearchTemplateQuery) searchQuery.toSearchTemplateQuery(),
                GdprLegalBase.GDPR_LEGAL_BASE_OBJ,
                Lists.newArrayList(GdprLegalBase.GDPR_LEGAL_BASE, GdprLegalBase.GDPR_LEGAL_BASE_STATUS),
                Lists.newArrayList(functionArg));
        if (CollectionUtils.empty(aggregateResult)) {
            return Lists.newArrayList();
        }
        return aggregateResult;
    }

    /**
     * @param legalBaseResult 法律基础结果
     * @param agreeResult     同意情况下状态结果
     * @param aggregateResult 聚合结果
     */
    private void buildResult(FindGdprLegalBaseView.LegalBase legalBaseResult, FindGdprLegalBaseView.Agree agreeResult, List<IObjectData> aggregateResult) {
        for (IObjectData legalBaseData : aggregateResult) {
            String legalBase = legalBaseData.get(GdprLegalBase.GDPR_LEGAL_BASE, String.class);
            if (StringUtils.isBlank(legalBase)) {
                continue;
            }
            Integer count = legalBaseData.get(GdprService.GDPR_GROUP_BY_COUNT, Integer.class);
            if (GdprLegalBase.LegalBase.AGREE.getValue().equals(legalBase)) {
                int agreeCount = legalBaseResult.getAgree();
                String legalBaseStatus = legalBaseData.get(GdprLegalBase.GDPR_LEGAL_BASE_STATUS, String.class);
                if (StringUtils.isNotBlank(legalBaseStatus)) {
                    if (GdprLegalBase.AgreeStatus.PENDING.getValue().equals(legalBaseStatus)) {
                        agreeResult.setPending(count);
                    }
                    if (GdprLegalBase.AgreeStatus.OBTAIN.getValue().equals(legalBaseStatus)) {
                        agreeResult.setObtain(count);
                    }
                    if (GdprLegalBase.AgreeStatus.WAITING.getValue().equals(legalBaseStatus)) {
                        agreeResult.setWaiting(count);
                    }
                    legalBaseResult.setAgree(agreeCount + count);
                }
                continue;
            }
            if (GdprLegalBase.LegalBase.LEGAL_BASIS.getValue().equals(legalBase)) {
                legalBaseResult.setLegalBasis(count);
                continue;
            }
            if (GdprLegalBase.LegalBase.CONTRACT.getValue().equals(legalBase)) {
                legalBaseResult.setContract(count);
                continue;
            }
            if (GdprLegalBase.LegalBase.LEGAL_OBLIGATION.getValue().equals(legalBase)) {
                legalBaseResult.setLegalObligation(count);
                continue;
            }
            if (GdprLegalBase.LegalBase.COMMON_INTEREST.getValue().equals(legalBase)) {
                legalBaseResult.setCommonInterest(count);
                continue;
            }
            if (GdprLegalBase.LegalBase.IMPORTANT_INTERESTS.getValue().equals(legalBase)) {
                legalBaseResult.setImportantInterests(count);
            }
        }
    }

    @ServiceMethod("query_legal_base_data")
    public QueryGdprLegalBaseData.Result queryGdprLegalBaseData(QueryGdprLegalBaseData.Arg arg, ServiceContext context) {
        QueryGdprLegalBaseData.Result result = QueryGdprLegalBaseData.Result.builder().build();
        if (context == null || arg == null || StringUtils.isBlank(arg.getLegalBase())) {
            log.warn("queryGdprLegalBaseData param is null, arg:{},context:{}", arg, context);
            return result;
        }
        //构造总的query,用于聚合查询数据库
        Query query = Query.builder().limit(arg.getPageSize())
                .offset(SearchTemplateQueryExt.calculateOffset(arg.getPageNumber(), arg.getPageSize())).build();
        //查指定的法律基础,过滤指定的数据id
        if (!UNTREATED.equals(arg.getLegalBase())) {
            Query legalBaseQuery = Query.builder()
                    .limit(arg.getPageSize())
                    .offset(SearchTemplateQueryExt.calculateOffset(arg.getPageNumber(), arg.getPageSize()))
                    .build();
            legalBaseQuery.and(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, context.getTenantId()).getFilter());
            legalBaseQuery.and(FilterExt.of(Operator.EQ, IObjectData.DESCRIBE_API_NAME, GdprLegalBase.GDPR_LEGAL_BASE_OBJ).getFilter());
            legalBaseQuery.and(FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter());
            legalBaseQuery.and(FilterExt.of(Operator.EQ, GdprLegalBase.GDPR_LEGAL_BASE, arg.getLegalBase()).getFilter());
            if (StringUtils.isNotBlank(arg.getLegalBaseStatus())) {
                legalBaseQuery.and(FilterExt.of(Operator.EQ, GdprLegalBase.GDPR_LEGAL_BASE_STATUS, arg.getLegalBaseStatus()).getFilter());
            }
            QueryResult<IObjectData> legalBaseList = metaDataFindService.findBySearchQuery(context.getUser(), GdprLegalBase.GDPR_LEGAL_BASE_OBJ,
                    (SearchTemplateQuery) legalBaseQuery.toSearchTemplateQuery());
            if (legalBaseList == null || CollectionUtils.empty(legalBaseList.getData())) {
                return result;
            }
            //过滤所有的数据id
            List<String> dataIds = legalBaseList.getData().stream().map(x -> x.get(GdprLegalBase.GDPR_LEGAL_BASE_DATA_ID, String.class)).collect(Collectors.toList());
            query.and(FilterExt.of(Operator.IN, IObjectData.ID, dataIds).getFilter());
        }
        query.and(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, context.getTenantId()).getFilter());
        query.and(FilterExt.of(Operator.EQ, IObjectData.DESCRIBE_API_NAME, arg.getApiName()).getFilter());
        query.and(FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter());
        //用于[全部]场景情况下的筛选
        if (UNTREATED.equals(arg.getLegalBase()) && StringUtils.isNotBlank(arg.getName())) {
            query.and(FilterExt.of(Operator.LIKE, IObjectData.NAME, arg.getName()).getFilter());
        }
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getApiName());
        QueryResult<IObjectData> queryResult = metaDataFindService.findBySearchQueryIfFillExtendFieldInfo(context.getUser(), describe,
                (SearchTemplateQuery) query.toSearchTemplateQuery(), true, true,
                true, true);
        if (queryResult == null || CollectionUtils.empty(queryResult.getData())) {
            return result;
        }
        return QueryGdprLegalBaseData.Result.builder()
                .objectData(ObjectDataDocument.ofList(queryResult.getData()))
                .totalCount(queryResult.getTotalNumber())
                .pageCount(SearchTemplateQueryExt.calculateTotalPage(arg.getPageNumber(), arg.getPageSize()))
                .pageSize(arg.getPageSize())
                .pageNumber(arg.getPageNumber())
                .build();
    }
    //-------------------------------------gdpr 后台配置相关接口 end------------------------------------------------------


    //--------------------------------------------gdpr 详情页 start------------------------------------------------------
    @ServiceMethod("open_legal_base")
    public OpenGdprLegalBase.Result openGdprLegalBase(OpenGdprLegalBase.Arg arg, ServiceContext context) {
        try {
            gdprService.openGdprLegalBase(context.getUser(), arg.getApiName(), arg.getDataId());
        } catch (Exception e) {
            log.warn("openGdprLegalBase failed! user:{}", context.getUser(), e);
            throw new MetaDataBusinessException("open gdpr legal base failed! please contact fx");
        }
        return OpenGdprLegalBase.Result.builder().build();
    }

    @ServiceMethod("close_legal_base")
    public CloseGdprLegalBase.Result closeGdprLegalBase(CloseGdprLegalBase.Arg arg, ServiceContext context) {
        try {
            gdprService.closeGdprLegalBase(context.getUser(), arg.getApiName(), arg.getDataId());
        } catch (Exception e) {
            log.warn("closeGdprLegalBase failed! user:{}", context.getUser(), e);
            throw new MetaDataBusinessException(I18NExt.text(I18NKey.DISABLE_GDPR_FAILURE_CONTACT_FENXIANG_CUSTOMER_SERVICE));
        }
        return CloseGdprLegalBase.Result.builder().build();
    }

//    @ServiceMethod("add_legal_base")
//    public AddGdprLegalBase.Result addGdprLegalBase(AddGdprLegalBase.Arg arg, ServiceContext context) {
//        //  AddGdprLegalBase.Result result = AddGdprLegalBase.Result.builder().build();
//        GdprLegalBase gdprLegalBase = buildGdprLegalBase(arg);
//        gdprService.updateGdprLegalBase(context.getUser(), gdprLegalBase);
//        return AddGdprLegalBase.Result.builder().build();
//    }


    @ServiceMethod("update_legal_base")
    public UpdateGdprLegalBase.Result updateGdprLegalBase(UpdateGdprLegalBase.Arg arg, ServiceContext context) {
        gdprService.updateGdprLegalBase(context.getUser(), arg.getApiName(), arg.getDataId(), arg.getLegalBase(), arg.getRemark());
        return UpdateGdprLegalBase.Result.builder().build();
    }


    @ServiceMethod("find_legal_base_detail")
    public FindGdprLegalBaseDetail.Result findGdprLegalBaseDetail(FindGdprLegalBaseDetail.Arg arg, ServiceContext context) {
        FindGdprLegalBaseDetail.Result result = FindGdprLegalBaseDetail.Result.builder().build();
        if (arg == null) {
            log.warn("findGdprLegalBaseDetail param error! user:{}", context.getUser());
            return result;
        }
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), GdprLegalBase.GDPR_LEGAL_BASE_OBJ);
        Optional<GdprLegalBase> gdprLegalBase = gdprService.findGdprLegalBaseByApiNameAndDataId(context.getUser(), arg.getApiName(), arg.getDataId());
        if (gdprLegalBase.isPresent()
                && GdprLegalBase.LegalBase.AGREE.getValue().equals(gdprLegalBase.get().getLegalBase())
                && GdprLegalBase.AgreeStatus.WAITING.getValue().equals(gdprLegalBase.get().getLegalBaseStatus())
        ) {
            boolean isLinkExpire = gdprService.isLinkExpire(context.getUser(), gdprLegalBase.get());
            if (isLinkExpire) {
                result.setLegalBaseStatus(GdprLegalBase.AgreeStatus.NO_REPLY.getValue());
            }
        }
        gdprLegalBase.ifPresent(legalBase -> buildResult(result, legalBase));
        //个人字段信息，查库
        List<GdprCompliance> gdprCompliance = gdprService.findGdprCompliance(context.getUser(), arg.getApiName());
        Optional<GdprCompliance> first = gdprCompliance.stream()
                .filter(x -> Objects.equals(x.getApiName(), arg.getApiName()) && x.isOpenStatus())
                .findFirst();
        if (first.isPresent()) {
            GdprCompliance data = first.get();
            result.setOrdinaryFields(data.getOrdinaryFields());
            result.setSensitiveFields(data.getSensitiveFields());
        }
        result.setDescribeDocument(ObjectDescribeDocument.of(describe));
        return result;
    }

    private void buildResult(FindGdprLegalBaseDetail.Result result, GdprLegalBase legalBase) {
        result.setId(legalBase.getId());
        result.setLegalBase(legalBase.getLegalBase());
        result.setLegalBaseStatus(legalBase.getLegalBaseStatus());
        result.setOpenStatus(legalBase.getOpenStatus());
        result.setRemark(legalBase.getRemark());
        result.setAgreeDate(legalBase.getAgreeDate());
        result.setAgreePerson(StringUtils.equals(legalBase.getAgreePerson(), GDPR_AGREE_PERSON) ?
                I18NExt.getOrDefault(I18NKey.GDPR_AGREE_PERSON_ACCOUNT, "客户") : legalBase.getAgreePerson());// ignoreI18n
        result.setContactWay(legalBase.getContactWay());
        result.setDataId(legalBase.getDataId());
        result.setApiName(legalBase.getApiName());
        result.setAgreeFirstOption(legalBase.getAgreeFirstOption());
        if (GdprLegalBase.LegalBase.AGREE.getValue().equals(legalBase.getLegalBase())
                && GdprLegalBase.AgreeStatus.WAITING.getValue().equals(legalBase.getLegalBaseStatus())) {
            result.setLink(buildGdprLink(legalBase.getLinkId()));
            result.setLinkCreateTime(legalBase.getLinkCreateTime());
        }
    }

    private String buildGdprLink(String linkId) {
        return AppFrameworkConfig.getGdprUrl() + "?linkId=" + linkId;
    }

    @ServiceMethod("create_legal_base_link")
    public CreateLegalBaseLink.Result createLegalBaseLink(CreateLegalBaseLink.Arg arg, ServiceContext context) {
        gdprService.createLegalBaseLink(context.getUser(), arg.getDataId(), arg.getApiName());
        return CreateLegalBaseLink.Result.builder().build();
    }

    @ServiceMethod("save_gdpr_form_lang")
    public SaveGdprFormLang.Result saveGdprFormLang(SaveGdprFormLang.Arg arg, ServiceContext context) {
        gdprService.saveGdprFormLang(context.getUser(), arg.getLang());
        return SaveGdprFormLang.Result.builder().build();
    }

    @ServiceMethod("find_gdpr_form_data")
    public FindGdprFormData.Result findGdprFormData(FindGdprFormData.Arg arg, ServiceContext context) {
        GdprFormData gdprFormData = gdprService.findGdprFormData(context.getUser());
        return FindGdprFormData.Result.builder()
                .enterpriseName(gdprFormData.getEnterpriseName())
                .lang(gdprFormData.getLang())
                .build();
    }

    @ServiceMethod("submit_gdpr_form_data")
    public SubmitGdprFormData.Result submitGdprFormData(SubmitGdprFormData.Arg arg, ServiceContext context) {
        gdprService.submitGdprFormData(context.getUser(), arg.getId(), arg.getAgreeFirstOption(), arg.getRemark());
        return SubmitGdprFormData.Result.builder().build();
    }


    @ServiceMethod("add_legal_base_detail")
    public AddGdprLegalBaseDetail.Result addGdprLegalBaseDetail(AddGdprLegalBaseDetail.Arg arg, ServiceContext context) {
        AddGdprLegalBaseDetail.Result result = AddGdprLegalBaseDetail.Result.builder().build();
        if (!GdprLegalBase.LegalBase.AGREE.getValue().equals(arg.getLegalBase())) {
            log.warn("addGdprLegalBaseDetail legalBase param error，legal base must be agree!");
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.GDPR_LEGAL_BASE_DETAIL_AGREE_UPDATE, "法律基础为同意才可更新同意明细"));// ignoreI18n
        }

        GdprLegalBase gdprLegalBase = GdprLegalBase.builder()
                .dataId(arg.getDataId())
                .apiName(arg.getApiName())
                .remark(arg.getRemark())
                .contactWay(arg.getContactWay())
                .agreeDate(arg.getAgreeDate())
                .agreeFirstOption(arg.getAgreeFirstOption())
                .build();
        gdprService.addGdprLegalBaseDetail(context.getUser(), gdprLegalBase);
        return result;
    }


    @ServiceMethod("update_legal_base_detail")
    public UpdateGdprLegalBaseDetail.Result updateGdprLegalBaseDetail(UpdateGdprLegalBaseDetail.Arg arg, ServiceContext context) {
        UpdateGdprLegalBaseDetail.Result result = UpdateGdprLegalBaseDetail.Result.builder().build();
        if (!GdprLegalBase.LegalBase.AGREE.getValue().equals(arg.getLegalBase())) {
            log.warn("updateGdprLegalBaseDetail legalBase param error,legal base must be agree!");
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.GDPR_LEGAL_BASE_DETAIL_AGREE_UPDATE, "法律基础为同意才可更新同意明细"));// ignoreI18n
        }
        GdprLegalBase gdprLegalBase = GdprLegalBase.builder()
                .dataId(arg.getDataId())
                .apiName(arg.getApiName())
                .remark(arg.getRemark())
                .contactWay(arg.getContactWay())
                .agreeDate(arg.getAgreeDate())
                .agreeFirstOption(arg.getAgreeFirstOption())
                .build();
        gdprService.updateGdprLegalBaseDetail(context.getUser(), gdprLegalBase);
        return result;
    }

    @ServiceMethod("delete_legal_base_detail")
    public DeleteGdprLegalBaseDetail.Result deleteGdprLegalBaseDetail(DeleteGdprLegalBaseDetail.Arg arg, ServiceContext context) {
        gdprService.deleteGdprLegalBaseDetail(context.getUser(), arg.getId());
        return DeleteGdprLegalBaseDetail.Result.builder().build();
    }
    //--------------------------------------------gdpr 详情页 end------------------------------------------------------


    //--------------------------------------------gdpr 数据项目请求 start------------------------------------------------
    @ServiceMethod("find_project_request_view")
    public FindGdprProjectRequestView.Result findGdprProjectRequestView(FindGdprProjectRequestView.Arg arg, ServiceContext context) {
        FindGdprProjectRequestView.Result result = FindGdprProjectRequestView.Result.builder().build();
        if (context == null) {
            log.warn("findGdprProjectRequestView context is null! ");
            return result;
        }
        Query query = Query.builder().limit(1).build();
        query.and(FilterExt.of(Operator.EQ, IObjectDescribe.TENANT_ID, context.getTenantId()).getFilter());
        query.and(FilterExt.of(Operator.EQ, IObjectDescribe.API_NAME, arg.getApiName()).getFilter());
        query.and(FilterExt.of(Operator.EQ, GdprProjectRequest.GDPR_PROJECT_REQUEST_STATUS, GdprProjectRequest.GDPR_PROJECT_REQUEST_TYPE_STATUS_OPEN).getFilter());
        query.and(FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter());
        query.setFindExplicitTotalNum(true);
        AggFunctionArg functionArg = AggFunctionArg.builder().aggFunction("count").aggField("id").build();
        List<IObjectData> projectRequestList = metaDataFindService.aggregateFindBySearchQuery(context.getUser(),
                (SearchTemplateQuery) query.toSearchTemplateQuery(), GdprProjectRequest.GDPR_PROJECT_REQUEST_OBJ,
                GdprProjectRequest.GDPR_PROJECT_REQUEST_TYPE, Lists.newArrayList(functionArg));
        if (CollectionUtils.empty(projectRequestList)) {
            return result;
        }
        FindGdprProjectRequestView.ProjectRequest projectRequest = FindGdprProjectRequestView.ProjectRequest.builder().build();
        for (IObjectData data : projectRequestList) {
            String type = data.get(GdprProjectRequest.GDPR_PROJECT_REQUEST_TYPE, String.class);
            Integer count = data.get(GdprService.GDPR_GROUP_BY_COUNT, Integer.class);
            if (GdprProjectRequest.GDPR_PROJECT_REQUEST_TYPE_QUERY.equals(type)) {
                projectRequest.setQuery(count);
                continue;
            }
            if (GdprProjectRequest.GDPR_PROJECT_REQUEST_TYPE_UPDATE.equals(type)) {
                projectRequest.setUpdate(count);
                continue;
            }
            if (GdprProjectRequest.GDPR_PROJECT_REQUEST_TYPE_EXPORT.equals(type)) {
                projectRequest.setExport(count);
                continue;
            }
            if (GdprProjectRequest.GDPR_PROJECT_REQUEST_TYPE_DELETE.equals(type)) {
                projectRequest.setDelete(count);
                continue;
            }
            if (GdprProjectRequest.GDPR_PROJECT_REQUEST_TYPE_STOP.equals(type)) {
                projectRequest.setStop(count);
            }
        }
        return FindGdprProjectRequestView.Result.builder().result(projectRequest).build();
    }

    @ServiceMethod("query_project_request_data")
    public QueryGdprProjectRequestData.Result queryGdprProjectRequestData(QueryGdprProjectRequestData.Arg arg, ServiceContext context) {
        QueryGdprProjectRequestData.Result result = QueryGdprProjectRequestData.Result.builder().build();
        if (context == null || arg == null) {
            log.warn("queryGdprProjectRequestData param is null, arg:{},context:{}", arg, context);
            return result;
        }
        //先分页查请求表
        Query query = Query.builder()
                .limit(arg.getPageSize())
                .offset(SearchTemplateQueryExt.calculateOffset(arg.getPageNumber(), arg.getPageSize()))
                .build();
        query.and(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, context.getTenantId()).getFilter());
        query.and(FilterExt.of(Operator.EQ, GdprProjectRequest.GDPR_PROJECT_REQUEST_API_NAME, arg.getApiName()).getFilter());
        query.and(FilterExt.of(Operator.EQ, GdprProjectRequest.GDPR_PROJECT_REQUEST_TYPE, arg.getType()).getFilter());
        QueryResult<IObjectData> projectRequestList = metaDataFindService.findBySearchQuery(context.getUser(),
                GdprProjectRequest.GDPR_PROJECT_REQUEST_OBJ, (SearchTemplateQuery) query.toSearchTemplateQuery());
        if (Objects.isNull(projectRequestList) || CollectionUtils.empty(projectRequestList.getData())) {
            return result;
        }
        //查询真正的数据
        List<String> dataIds = projectRequestList.getData().stream()
                .map(x -> x.get(GdprProjectRequest.GDPR_PROJECT_REQUEST_DATA_ID, String.class))
                .collect(Collectors.toList());
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getApiName());
        Query searchQuery = Query.builder()
                .limit(arg.getPageSize())
                .offset(SearchTemplateQueryExt.calculateOffset(arg.getPageNumber(), arg.getPageSize()))
                .build();
        searchQuery.and(FilterExt.of(Operator.EQ, IObjectData.DESCRIBE_API_NAME, arg.getApiName()).getFilter());
        searchQuery.and(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, context.getTenantId()).getFilter());
        searchQuery.and(FilterExt.of(Operator.IN, IObjectData.ID, dataIds).getFilter());
        searchQuery.and(FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter());
        QueryResult<IObjectData> queryResult = metaDataFindService.findBySearchQueryIfFillExtendFieldInfo(context.getUser(), describe,
                (SearchTemplateQuery) searchQuery.toSearchTemplateQuery(), true, true,
                true, true);
        if (queryResult == null || CollectionUtils.empty(queryResult.getData())) {
            return result;
        }
        return QueryGdprProjectRequestData.Result.builder()
                .objectData(ObjectDataDocument.ofList(queryResult.getData()))
                .totalCount(queryResult.getTotalNumber())
                .pageCount(SearchTemplateQueryExt.calculateTotalPage(arg.getPageNumber(), arg.getPageSize()))
                .pageSize(arg.getPageSize())
                .pageNumber(arg.getPageNumber())
                .build();
    }

    @ServiceMethod("find_project_request_data")
    public FindGdprProjectRequestData.Result findGdprProjectRequestData(FindGdprProjectRequestData.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getDataId()) || StringUtils.isBlank(arg.getApiName())) {
            log.warn("findGdprProjectRequestData arg error! user:{}", context.getUser());
            return FindGdprProjectRequestData.Result.builder().build();
        }
        List<GdprProjectRequest> data = gdprService.findGdprProjectRequest(context.getUser(), arg.getApiName(), arg.getDataId());
        return FindGdprProjectRequestData.Result.builder().result(data).build();
    }

    @ServiceMethod("add_project_request_data")
    public AddGdprProjectRequestData.Result addGdprProjectRequestData(AddGdprProjectRequestData.Arg arg, ServiceContext context) {
        if (context == null || arg == null) {
            log.warn("queryGdprProjectRequestData param is null, arg:{},context:{}", arg, context);
            return AddGdprProjectRequestData.Result.builder().build();
        }
        gdprService.addGdprProjectRequest(context.getUser(), arg.getApiName(), arg.getDataId(), arg.getType());
        return AddGdprProjectRequestData.Result.builder().build();
    }

    @ServiceMethod("update_project_request_data")
    public UpdateGdprProjectRequestData.Result updateGdprProjectRequestData(UpdateGdprProjectRequestData.Arg arg, ServiceContext context) {
        try {
            gdprService.updateGdprProjectRequest(context.getUser(), arg.getId());
        } catch (Exception e) {
            log.warn("updateGdprProjectRequestData failed! user:{}", context.getUser(), e);
            throw new MetaDataBusinessException("update gdpr project request data failed! please contact fxiaoke");
        }
        return UpdateGdprProjectRequestData.Result.builder().build();
    }

    @ServiceMethod("delete_project_request_data")
    public DeleteGdprProjectRequestData.Result deleteGdprProjectRequestData(DeleteGdprProjectRequestData.Arg arg, ServiceContext context) {
        gdprService.deleteGdprProjectRequest(context.getUser(), arg.getId());
        return DeleteGdprProjectRequestData.Result.builder().build();
    }
    //--------------------------------------------gdpr 数据项目请求 end  ------------------------------------------------
}
