package com.facishare.paas.appframework.core.predef.action

import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.appframework.core.BaseTest
import com.facishare.paas.appframework.core.model.Action
import com.facishare.paas.appframework.core.model.ActionContext
import com.facishare.paas.appframework.core.model.ActionLocateService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired

/**
 * create by <PERSON><PERSON><PERSON> on 2018/12/10
 */
@Slf4j
class BaseActionTest extends BaseTest {
    @Autowired
    protected ActionLocateService actionLocateService


    def buildActionContext(String apiName, String actionCode) {
        new ActionContext(requestContext, apiName, actionCode)
    }

    def <T> Object execute(String apiName, String actionCode, T arg) {
        ActionContext actionContext = buildActionContext(apiName, actionCode)
        String payload = JacksonUtils.toJson(arg)
        Action action = actionLocateService.locateAction(actionContext, payload)
        action.act(arg)
    }
}
