package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.dto.GetRolesByUserId;
import com.facishare.paas.appframework.privilege.dto.GetUserRoleInfo;
import com.facishare.paas.appframework.privilege.dto.QueryRoleCodeByNames;
import com.facishare.paas.appframework.privilege.dto.UserRoleInfo;
import com.facishare.paas.auth.model.RoleViewPojo;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * Created by zhouwr on 2017/10/16
 */
public interface UserRoleInfoService {

    List<String> getUsersByRole(User user, String roleCode);

    boolean isAdmin(User user);

    List<String> getUserRole(User user);

    Optional<String> getDefaultRoleCode(User user);

    List<GetRolesByUserId.UserRole> getRoleInfoByUser(User user);

    Optional<String> getMainRoleLayoutAPIName(User user, String objectAPIName, String recordType);

    Optional<String> getMainRoleLayoutAPIName(User user, String objectAPIName, String recordType, String viewType);

    List<RoleViewPojo> getMainRoleLayoutByRecordType(User user, String objectAPIName, String viewType);

    List<String> queryRoleUsersByRoles(User user, List<String> roles);

    List<String> queryUsers(User user, String roleCode, Set<String> userIdSet);

    Map<String, String> queryRoleNameByRoleCode(User user, List<String> roleIds);

    List<QueryRoleCodeByNames.RoleInfo> queryRoleCodeByNames(User user, List<String> roleNames);

    List<GetUserRoleInfo.RoleInfo> queryRoleInfoByRoleCode(User user, List<String> roleIds);

    List<UserRoleInfo> queryAllRoleInfoByCodes(User user, List<String> roleCodes);
}
