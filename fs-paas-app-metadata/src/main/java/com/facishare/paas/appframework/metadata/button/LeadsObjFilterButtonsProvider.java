package com.facishare.paas.appframework.metadata.button;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.util.LicenseConstants;
import com.facishare.paas.metadata.api.IUdefButton;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.facishare.paas.appframework.common.util.ObjectAction.COLLECT_TO;
import static com.facishare.paas.appframework.common.util.ObjectAction.REVISIT_VIRTUAL_PHONE;

/**
 * Created by zhaooju on 2022/12/26
 */
@Component
@Slf4j
public class LeadsObjFilterButtonsProvider implements FilterButtonsProvider {
    @Autowired
    private LicenseService licenseService;

    @Autowired
    private ConfigService configService;

    @Override
    public String getDescribeApiName() {
        return Utils.LEADS_API_NAME;
    }

    @Override
    public List<IUdefButton> getButtons(User user, List<IUdefButton> buttons) {
        if (CollectionUtils.empty(buttons)) {
            return buttons;
        }
        // 线索对象，没有开启 leads_deduplication_app， 不下发归集按钮
        Map<String, Boolean> map = licenseService.existModule(user.getTenantId(), Sets.newHashSet(LicenseConstants.ModuleCode.LEADS_DEDUPLICATION_APP));
        if (BooleanUtils.isNotTrue(map.get(LicenseConstants.ModuleCode.LEADS_DEDUPLICATION_APP))) {
            buttons.removeIf(it -> COLLECT_TO.getButtonApiName().equals(it.getApiName()));
        }
        String invalidRebateStatus = configService.findTenantConfig(user, "marketing_open_invalid_rebate_status");
        if (StringUtil.isBlank(invalidRebateStatus) || "0".equals(invalidRebateStatus)) {
            buttons.removeIf(it -> REVISIT_VIRTUAL_PHONE.getButtonApiName().equals(it.getApiName()));
        }
        return buttons;
    }
}
