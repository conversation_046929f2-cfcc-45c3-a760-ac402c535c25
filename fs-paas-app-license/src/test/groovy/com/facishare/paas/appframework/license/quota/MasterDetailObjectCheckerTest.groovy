package com.facishare.paas.appframework.license.quota

import com.facishare.paas.appframework.license.quota.checker.MasterDetailObjectChecker
import com.facishare.paas.appframework.license.quota.checker.QuotaChecker
import com.facishare.paas.appframework.license.util.ModulePara
import com.google.common.collect.Maps
import spock.lang.Specification

class MasterDetailObjectCheckerTest extends Specification {

    def "test getMaxCount should return mapped value if available"() {
        given:
        def paraKey = ModulePara.MASTER_DETAIL_OBJECT_LIMIT.getParaKey()
        def paraMap = [(paraKey): 20]
        def masterDetailObjectChecker = new MasterDetailObjectChecker(paraMap)
        def quotaInfo = QuotaChecker.QuotaInfo.builder()
                .tenantId("12345")
                .build()

        when:
        def result = masterDetailObjectChecker.getMaxCount(quotaInfo)

        then:
        result == 20
    }

    def "test getMaxCount should return default value if not mapped"() {
        given:
        def paraMap = Maps.newHashMap()
        def masterDetailObjectChecker = new MasterDetailObjectChecker(paraMap)
        def quotaInfo = QuotaChecker.QuotaInfo.builder()
                .tenantId("12345")
                .build()

        when:
        def result = masterDetailObjectChecker.getMaxCount(quotaInfo)

        then:
        result == ModulePara.MASTER_DETAIL_OBJECT_LIMIT.getDefaultValue("12345")
    }

    def "test check method should return true when max count >= actual count"() {
        given:
        def paraKey = ModulePara.MASTER_DETAIL_OBJECT_LIMIT.getParaKey()
        def paraMap = [(paraKey): 20]
        def masterDetailObjectChecker = new MasterDetailObjectChecker(paraMap)
        def quotaInfo = QuotaChecker.QuotaInfo.builder()
                .tenantId("12345")
                .actualCount(15)
                .build()

        when:
        def result = masterDetailObjectChecker.check(quotaInfo)

        then:
        result == true
    }

    def "test check method should return false when max count < actual count"() {
        given:
        def paraKey = ModulePara.MASTER_DETAIL_OBJECT_LIMIT.getParaKey()
        def paraMap = [(paraKey): 10]
        def masterDetailObjectChecker = new MasterDetailObjectChecker(paraMap)
        def quotaInfo = QuotaChecker.QuotaInfo.builder()
                .tenantId("12345")
                .actualCount(15)
                .build()

        when:
        def result = masterDetailObjectChecker.check(quotaInfo)

        then:
        result == false
    }
} 