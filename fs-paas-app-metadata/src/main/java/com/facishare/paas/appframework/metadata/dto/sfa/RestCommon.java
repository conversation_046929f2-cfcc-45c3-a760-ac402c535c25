package com.facishare.paas.appframework.metadata.dto.sfa;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by zhaopx on 2017/12/4.
 */
public interface RestCommon {
    @Data
    @Builder
    class Arg {
        @SerializedName("ObjectType")
        @JSONField(name = "ObjectType")
        private Integer object_type;

        @SerializedName("ObjectIDs")
        @JSONField(name = "ObjectIDs")
        private List<String> object_ids;
    }

    @Getter
    @Setter
    class Result extends BaseResult {

    }
}
