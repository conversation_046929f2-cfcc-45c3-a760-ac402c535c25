{"swagger": "2.0", "info": {"title": "AppFrameWork REST API", "description": "PaaS 应用框架REST API", "version": "1.0"}, "host": "************:8234", "schemes": ["http"], "basePath": "/v1/inner/rest/customAction", "produces": ["application/json"], "commonParams": {"userInfo": {"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, "eid": {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}}, "paths": {"/{objectApiName}/{actionCode}": {"post": {"summary": "do custom action by action code", "description": "根据action code执行动作", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"name": "objectApiName", "in": "path", "required": true, "describe": "对象apiname", "type": "string"}, {"name": "actionCode", "in": "path", "required": true, "describe": "动作名称", "type": "string"}, {"name": "requestParam", "in": "body", "required": true, "schema": {"$ref": "#/definitions/requestParam"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/response"}}}}}}, "definitions": {"requestParam": {"type": "object"}, "response": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}}}}}