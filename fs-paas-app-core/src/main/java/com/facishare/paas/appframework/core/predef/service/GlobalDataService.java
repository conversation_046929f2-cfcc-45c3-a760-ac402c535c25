package com.facishare.paas.appframework.core.predef.service;

import com.facishare.crm.operate.report.OperateReportUtil;
import com.facishare.crm.operate.report.model.ProductLine;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.globaldata.*;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.CountryAreaManager;
import com.facishare.paas.appframework.metadata.CountryAreaManager.Node;
import com.facishare.paas.appframework.metadata.MetaDataGlobalService;
import com.facishare.paas.appframework.metadata.dto.TownInfo;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.support.CountryAreaService;
import com.facishare.paas.metadata.support.CountryAreaService.AreaInfo;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.util.UdobjGrayConfigKey.IGNORE_GOOGLE_MAP_LICENSE_GRAY;

/**
 * 全局数据服务
 * Created by liyiguang on 2017/10/11.
 */
@ServiceModule("global_data")
@Component
@Slf4j
public class GlobalDataService {

    @Resource
    private CountryAreaService countryAreaService;

    @Autowired
    private MetaDataGlobalService metaDataGlobalService;

    @Autowired
    private LicenseService licenseService;

    @ServiceMethod("country_area_field_options")
    public String getCountryAreaFieldOptions(GetCountryAreaFieldOptions.Arg arg, ServiceContext context) {
        return countryAreaService.getCountryCascadeJsonString(context.getLang().getValue(), context.getTenantId());
    }

    @ServiceMethod("country_area_field_options_include_deleted")
    public String getCountryAreaFieldOptionsIncludedDeleted(GetCountryAreaFieldOptions.Arg arg, ServiceContext context) {
        return countryAreaService.getCountryCascadeJsonStringIncludeDeleted(context.getLang().getValue(), context.getTenantId());
    }

    @ServiceMethod("country_area_tree")
    public GetCountryAreaTree.Result getCountryAreaTree(GetCountryAreaTree.Arg arg, ServiceContext context) {
        List<Node> nodeList = CountryAreaManager.getNodeList(context.getTenantId());
        return GetCountryAreaTree.Result.builder().value(nodeList).build();
    }

    @ServiceMethod("get_town_options")
    public GetTownOptions.Result getTownOptions(GetTownOptions.Arg arg, ServiceContext context) {
        List<TownInfo> townOptions = metaDataGlobalService.getTownOptions(arg.getType(), Sets.newHashSet(arg.getCode()), context.getUser());
        return GetTownOptions.Result.builder().town(townOptions).build();
    }

    @ServiceMethod("get_country_area_field_label_by_code")
    public GetCountryAreaFieldLabelByCode.Result getCountryAreaFieldLabelByCode(GetCountryAreaFieldLabelByCode.Arg arg, ServiceContext context) {
        List<String> codes = arg.getCodes();
        if (CollectionUtils.empty(codes)) {
            return GetCountryAreaFieldLabelByCode.Result.builder().build();
        }
        String type = arg.getType();
        List<Node> result = codes.stream()
                .map(code -> Node.of(code, CountryAreaManager.getLabelByCode(context.getTenantId(), code, type)))
                .collect(Collectors.toList());
        return GetCountryAreaFieldLabelByCode.Result.builder().value(result).build();
    }

    @ServiceMethod("batch_query_location_info")
    public BatchQueryLocationInfos.Result batchQueryLocationInfo(BatchQueryLocationInfos.Arg arg, ServiceContext context) {
        List<Map<String, String>> locationInfos = metaDataGlobalService.batchQueryAreaNamesByCode(context.getUser(), arg.getCodes(), arg.getType());
        return BatchQueryLocationInfos.Result.builder().locationInfos(locationInfos).build();
    }

    @ServiceMethod("get_zoning_code")
    public GetZoningCode.Result getZoningCode(GetZoningCode.Arg arg, ServiceContext context) {
        AreaInfo areaInfo = CountryAreaManager.getZoningCodeByLabel(context.getTenantId(), arg.getLabel(), arg.getType());
        return GetZoningCode.Result.from(areaInfo);
    }

    @ServiceMethod("get_map_quota_left")
    @Deprecated
    public GetMapQuotaLeft.Result getMapQuotaLeft(GetMapQuotaLeft.Arg arg, ServiceContext context) {
        long quota = 0;
        boolean openQuota = false;
        if (UdobjGrayConfig.isAllow("google_map_ei", context.getTenantId())) {
            quota = 1000;
            openQuota = true;
        }
        return GetMapQuotaLeft.Result.builder().quota(quota).openQuota(openQuota).build();
    }

    @ServiceMethod("consume_map_quota")
    @Deprecated
    public ConsumeMapLeftQuota.Result consumeMapQuota(ConsumeMapLeftQuota.Arg arg, ServiceContext context) {
        CollectionUtils.nullToEmpty(arg.getConsumptionList()).forEach(a -> {
            for (int i = 0; i < a.getCount(); i++) {
                OperateReportUtil.postMessage(context.getTenantId(), context.getUser().getUserId(),
                        ProductLine.CRM_SERVICE, "map_quota", "mapQuotaConsumption", a.getApi());
            }
        });
        return ConsumeMapLeftQuota.Result.builder().success(true).build();
    }


    @ServiceMethod("get_zone_by_parent")
    public GetZoneByParent.Result getZoneByParent(GetZoneByParent.Arg arg, ServiceContext context) {
        CountryAreaService.BreadcrumbType breadcrumbType = CountryAreaService.BreadcrumbType.CODE;
        if (StringUtils.equalsIgnoreCase(arg.getValueType(), "label")) {
            breadcrumbType = CountryAreaService.BreadcrumbType.NAME;
        }

        String path = arg.getParentPath();
        if (StringUtils.isEmpty(path)) {
            path = "ROOT";
        }


        boolean cascadeChild = BooleanUtils.isTrue(arg.getCascadeChildren());
        // TODO: 2024/9/20 I18n
        List<String> allTypes = Lists.newArrayList("国家", "省", "市", "区", "乡镇"); // ignoreI18n

        List<String> types = Lists.newArrayList();
        if (cascadeChild) {
            int index;
            if (StringUtils.isBlank(arg.getParentPath())) {
                index = 0;
            } else {
                String[] strings = arg.getParentPath().split("/");
                index = strings.length - 1;
            }

            int cascadeLevel = allTypes.size();
            if (Objects.nonNull(arg.getCascadeLevel()) && arg.getCascadeLevel() > 0 && arg.getCascadeLevel() <= 3) {
                cascadeLevel = arg.getCascadeLevel();
            }

            types = allTypes.subList(index + 1, index == 0 ? Math.min(allTypes.size() - 1, cascadeLevel + index + 1) :
                    Math.min(allTypes.size(), cascadeLevel + index + 1));
        }
        List<CountryAreaService.CountryAreaDTO> list = countryAreaService.findChildrenAreasByBreadCrumbAndTypeLimit(context.getTenantId(), breadcrumbType,
                path, cascadeChild, types, 100000);

        return GetZoneByParent.Result.builder().optionList(GetZoneByParent.ZoneInfo.fromCountryArea(list)).build();
    }

    @ServiceMethod("get_parent_zone")
    public GetParentZone.Result getParentZone(GetParentZone.Arg arg, ServiceContext context) {
        List<CountryAreaService.CountryAreaDTO> parentAreaList = countryAreaService.findParentAreaList(context.getTenantId(), arg.getValue());
        return GetParentZone.Result.builder().parentList(GetZoneByParent.ZoneInfo.fromCountryArea(parentAreaList)).build();
    }

    @ServiceMethod("consume_and_return_map_quota")
    public ConsumeAndReturnMapQuota.Result consumeAndReturnMapQuotaLeft(ConsumeAndReturnMapQuota.Arg arg, ServiceContext context) {
        if (arg.getConsumeCount() <= 0 || StringUtils.isBlank(arg.getModuleKey()) || StringUtils.isBlank(arg.getParaKey())
                || StringUtils.isBlank(arg.getBizSource()) || StringUtils.isBlank(arg.getBizKey())) {
            throw new MetaDataBusinessException(I18NExt.getOrDefault(I18NKey.PARAM_ERROR, "参数错误"));// ignoreI18n
        }

        //使用企业自己googleMap的key，不用校验license
        if(UdobjGrayConfig.isAllow(IGNORE_GOOGLE_MAP_LICENSE_GRAY, context.getTenantId())) {
            return ConsumeAndReturnMapQuota.Result.builder().consumeSuccess(true).build();
        }

        boolean success = licenseService.acquireUsedValue(context.getTenantId(), arg.getModuleKey(), arg.getParaKey(), arg.getConsumeCount());
        if (success) {
            // 记录埋点到神策
            for (int i = 0; i < arg.getConsumeCount(); i++) {
                OperateReportUtil.postMessage(context.getTenantId(), context.getUser().getUserIdOrOutUserIdIfOutUser(),
                        ProductLine.CRM_SERVICE, arg.getBizSource(), "mapQuotaConsumptionBiz", arg.getBizKey()
                        , arg.getApiName(), arg.getDataId(), null);
            }
        }
        return ConsumeAndReturnMapQuota.Result.builder().consumeSuccess(success).build();
    }

    @ServiceMethod("check_map_license")
    public CheckMapLicense.Result checkMapLicense(CheckMapLicense.Arg arg, ServiceContext context) {
        if (Objects.isNull(arg) || StringUtils.isBlank(arg.getModuleKey())) {
            return CheckMapLicense.Result.builder().open(false).build();
        }
        //使用企业自己googleMap的key，不用校验license
        if(UdobjGrayConfig.isAllow(IGNORE_GOOGLE_MAP_LICENSE_GRAY, context.getTenantId())) {
            return CheckMapLicense.Result.builder().open(true).build();
        }

        Map<String, Boolean> map = licenseService.existModule(context.getTenantId(), Sets.newHashSet(arg.getModuleKey()));
        Boolean open = CollectionUtils.nullToEmpty(map).getOrDefault(arg.getModuleKey(), false);

        return CheckMapLicense.Result.builder().open(BooleanUtils.isTrue(open)).build();
    }

    @ServiceMethod("get_map_quota_left_by_license")
    public GetMapQuotaLeftByLicense.Result getMapQuotaLeftByLicense(GetMapQuotaLeftByLicense.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getModuleKey()) || StringUtils.isBlank(arg.getParaKey())) {
            return GetMapQuotaLeftByLicense.Result.builder().quota(0L).build();
        }

        //使用企业自己googleMap的key，不用校验license
        if(UdobjGrayConfig.isAllow(IGNORE_GOOGLE_MAP_LICENSE_GRAY, context.getTenantId())) {
            return GetMapQuotaLeftByLicense.Result.builder().quota(100L).build();
        }


        Map<String, String> map = licenseService.acquireUsedValue(context.getTenantId(), Sets.newHashSet(arg.getParaKey()));
        String valueStr = map.getOrDefault(arg.getParaKey(), "0");
        long usedValue = 0;
        if (NumberUtils.isCreatable(valueStr)) {
            usedValue = new BigDecimal(valueStr).longValue();
        }

        int quota = licenseService.getQuotaByModule(context.getTenantId(), arg.getParaKey());


        return GetMapQuotaLeftByLicense.Result.builder().quota(quota - usedValue).build();
    }

    @ServiceMethod("get_area_by_codes")
    public GetZoneByCodes.Result getAreaByCodes(GetZoneByCodes.Arg arg, ServiceContext context) {
        List<String> codes = arg.getCodes();
        if (CollectionUtils.empty(codes)) {
            return GetZoneByCodes.Result.builder().optionList(Lists.newArrayList()).build();
        }
        if (codes.size() > AppFrameworkConfig.getMaxQueryAreaLimit()) {
            codes = codes.subList(0, AppFrameworkConfig.getMaxQueryAreaLimit());
        }
        List<CountryAreaService.CountryAreaDTO> countryAreaList = countryAreaService.findAreaByStandardCodes(context.getLang().getValue(), context.getTenantId(), codes);
        return GetZoneByCodes.Result.builder().optionList(GetZoneByCodes.ZoneInfo.fromCountryArea(countryAreaList)).build();
    }

    @ServiceMethod("query_area_info")
    public QueryAreaInfo.Result queryAreaInfo(QueryAreaInfo.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getKeyword())) {
            return QueryAreaInfo.Result.builder().optionList(Lists.newArrayList()).build();
        }
        // 默认查询四级 即：国家 省 市 区
        int level = Objects.isNull(arg.getLevel()) ? 4 : arg.getLevel();
        List<CountryAreaService.CountryAreaDTO> countryAreaList = countryAreaService.findAreaBySearchQueryAndParentCode(context.getTenantId(), context.getLang().getValue(),
                arg.getKeyword(), trimStartEndSlash(arg.getParentPath()), level);
        return QueryAreaInfo.Result.builder().optionList(QueryAreaInfo.AreaInfo.fromCountryArea(countryAreaList)).build();
    }

    private String trimStartEndSlash(String parentPath) {
        if (StringUtils.isNotEmpty(parentPath) && (parentPath.startsWith("/") || parentPath.endsWith("/"))) {
            parentPath = StringUtils.removeStart(parentPath, "/");
            parentPath = StringUtils.removeEnd(parentPath, "/");
        }
        return parentPath;
    }

    @ServiceMethod("get_area_by_fs_unique_codes")
    public GetZoneByFsUniqueCodes.Result getAreaByFsUniqueCodes(GetZoneByFsUniqueCodes.Arg arg, ServiceContext context) {
        List<String> fsUniqueCodes = arg.getFsUniqueCodes();
        if (CollectionUtils.empty(fsUniqueCodes)) {
            return GetZoneByFsUniqueCodes.Result.builder().optionList(Lists.newArrayList()).build();
        }
        if (fsUniqueCodes.size() > AppFrameworkConfig.getMaxQueryAreaLimit()) {
            fsUniqueCodes = fsUniqueCodes.subList(0, AppFrameworkConfig.getMaxQueryAreaLimit());
        }
        List<CountryAreaService.CountryAreaDTO> countryAreaList = countryAreaService.findStandardCodesByCode(context.getTenantId(), fsUniqueCodes);
        return GetZoneByFsUniqueCodes.Result.builder().optionList(GetZoneByFsUniqueCodes.ZoneInfo.fromCountryArea(countryAreaList)).build();
    }

    @ServiceMethod("get_all_area_by_label")
    public GetZoningCode.AllResult getAllAreaByLabel(GetZoningCode.Arg arg, ServiceContext context) {
        if (Strings.isNullOrEmpty(arg.getLabel()) || Strings.isNullOrEmpty(arg.getType())) {
            return GetZoningCode.AllResult.builder().build();
        }

        return GetZoningCode.AllResult.from(countryAreaService.getAllZoningCodeAreaInfoByLabel(
                context.getLang().getValue(), context.getTenantId(), arg.getLabel(), arg.getType()));
    }

    @ServiceMethod("batch_query_labels")
    public BatchQueryLabels.Result batchQueryLabels(BatchQueryLabels.Arg arg, ServiceContext context) {
        if (CollectionUtils.empty(arg.getCodes())) {
            return BatchQueryLabels.Result.builder().build();
        }
        Map<String, String> labelCodeMap = countryAreaService.batchQueryLabelsByCodes(context.getLang().getValue(), context.getTenantId(), Sets.newHashSet(arg.getCodes()));
        return BatchQueryLabels.Result.builder().zoneInfos(BatchQueryLabels.ZoneInfo.from(labelCodeMap)).build();
    }
}
