package com.facishare.paas.appframework.log.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * Created by fengjy in 2019/1/8 20:26
 */
public class SearchFunctionDetailModel {
    @Data
    public static class Arg {
        private String logId;
        private String tenantId;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class Result extends BaseResult {
        private String exception;
        private String name;
        private String parameters;
        private String returnValue;
        private Boolean success;
        private Date operationTime;
        private Long durationTime;
        private String functionApiName;
        private List<FunctionLogDetail> results;
    }
}
