package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.query.SearchQueryContext;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fasterxml.jackson.annotation.JsonProperty;
import joptsimple.internal.Strings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import static com.facishare.paas.appframework.core.predef.controller.StandardListCountController.Arg;
import static com.facishare.paas.appframework.core.predef.controller.StandardListCountController.Result;

/**
 * 根据场景id 获取当前场景的数据条数
 * create by zhaoju on 2021/01/05
 */
public class StandardListCountController extends PreDefineController<Arg, Result> {
    public static final String DEFAULT_SEARCH_QUERY_INFO = "{\"filters\":[],\"wheres\":[],\"orders\":[]}";
    public static final String SEARCH_QUERY_WITH_RECORD_TYPE_FILTER = "{\"filters\":[{\"field_name\":\"record_type\",\"field_values\":[\"%s\"],\"operator\":\"EQ\"}],\",wheres\":[],\"orders\":[]}";
    protected ObjectDescribeExt objectDescribe;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardController.ListCount.getFuncPrivilegeCodes();
    }

    @Override
    protected void before(Arg arg) {
        // 只查询模糊数量
        objectDescribe = findObject();
        stopWatch.lap("findObject");
        super.before(arg);
    }

    private ObjectDescribeExt findObject() {
        IObjectDescribe objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), arg.getObjectDescribeApiName());
        return ObjectDescribeExt.of(objectDescribe);
    }

    @Override
    protected Result doService(Arg arg) {
        SearchTemplateQuery query = getSearchTemplateQuery();
        stopWatch.lap("buildSearchTemplateQuery");
        QueryResult<IObjectData> queryResult = getQueryResult(query);
        stopWatch.lap("getQueryResult");
        return Result.builder()
                .total(queryResult.getTotalNumber())
                .build();
    }

    private QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        IActionContext context = buildContext();
        return serviceFacade.findBySearchQuery(context, objectDescribe.getApiName(), query);
    }

    protected IActionContext buildContext() {
        IActionContext context = ActionContextExt.of(controllerContext.getUser())
                .skipRelevantTeam()
                .disableDeepQuote()
                .getContext();
        context.setDoCalculate(false);
        context.openReturnExplainCountOnly();
        return context;
    }

    private SearchTemplateQuery getSearchTemplateQuery() {
        if (serviceFacade.isSupportOrFilter(controllerContext.getTenantId(), controllerContext.getObjectApiName())) {
            SearchTemplateQuery query = (SearchTemplateQuery) defineQuery().toSearchTemplateQuery();
            if (log.isDebugEnabled()) {
                log.debug("defineQuery ending, SearchTemplateQuery:{}", query.toJsonString());
            }
            validateQuery(query);
            return customSearchTemplate(query);
        }
        SearchTemplateQuery searchQuery = buildSearchTemplateQuery();
        return customSearchTemplate(searchQuery);
    }

    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        return searchQuery;
    }

    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery searchTemplateQuery = serviceFacade.getSearchTemplateQueryWithIgnoreSceneFilter(controllerContext.getUser(), objectDescribe,
                getSearchTemplateId(), getSearchTemplateType(), getSearchQueryInfo(), false, false,
                false);
//        searchTemplateQuery.setFindExplicitTotalNum(true);
        return searchTemplateQuery;
    }

    protected Query defineQuery() {
        SearchQueryContext queryContext = SearchQueryContext.builder()
                .searchTemplateType(getSearchTemplateType())
                .templateId(getSearchTemplateId())
                .isIgnoreSceneFilter(false)
                .isIgnoreSceneRecordType(false)
                .isRelatedPage(false)
                .build();
        Query searchQuery = serviceFacade.findSearchQuery(controllerContext.getUser(), objectDescribe, getSearchQueryInfo(), queryContext);
//        searchQuery.setFindExplicitTotalNum(true);
        return searchQuery;
    }

    private String getSearchQueryInfo() {
        if (Strings.isNullOrEmpty(arg.getRecordType())) {
            return DEFAULT_SEARCH_QUERY_INFO;
        }
        return String.format(SEARCH_QUERY_WITH_RECORD_TYPE_FILTER, arg.getRecordType());
    }

    protected String getSearchTemplateId() {
        return arg.getSearchTemplateId();
    }

    protected String getSearchTemplateType() {
        return arg.getSearchTemplateType();
    }

    private void validateQuery(SearchTemplateQuery query) {
        String pattern = query.getPattern();
        if (Strings.isNullOrEmpty(pattern)) {
            return;
        }
        int num = pattern.toLowerCase().replace("or", "and").split("and").length;
        if (num != query.getFilters().size()) {
            log.warn("defineQuery fail, ei:{}, objectApiName:{}, pattern size:{}, filters size:{}, SearchTemplateQuery:{}",
                    controllerContext.getTenantId(), controllerContext.getObjectApiName(), num, query.getFilters().size(), query.toJsonString());
        }
    }

    @Data
    public static class Arg {
        @JsonProperty("object_describe_api_name")
        private String objectDescribeApiName;

        @JsonProperty("search_template_id")
        private String searchTemplateId;

        @JsonProperty("search_template_type")
        private String searchTemplateType;

        @JsonProperty("record_type")
        private String recordType;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private Integer total;
    }
}
