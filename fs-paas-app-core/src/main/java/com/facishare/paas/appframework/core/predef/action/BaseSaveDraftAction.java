package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDraftDocument;
import com.facishare.paas.appframework.license.quota.TenantLicenseInfo;
import com.facishare.paas.appframework.license.util.ModulePara;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IObjectDataDraft;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Sets;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/11/11 3:06 下午
 */
public abstract class BaseSaveDraftAction extends AbstractStandardAction<BaseSaveDraftAction.Arg,
        BaseSaveDraftAction.Result> {
    private IObjectDataDraft draft;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        initDraft();
        validateDetail();
    }

    @Override
    protected Result doAct(Arg arg) {
        handleDraft(draft);
        IObjectDataDraft savedDraft = save(draft);
        return Result.builder().draft(ObjectDataDraftDocument.of(savedDraft)).build();
    }

    protected void handleDraft(IObjectDataDraft draft) {
        // 补充标题
//        String title = getTitle();
//        draft.setTitle(title);

        // 补充tenantId
        if (StringUtils.isBlank(draft.getTenantId())) {
            draft.setTenantId(actionContext.getTenantId());
        }
    }

    protected abstract IObjectDataDraft save(IObjectDataDraft draft);

    /*
     * 初始化draft，更新和新建的共同逻辑
     */
    private void initDraft() {
        ObjectDataDraftDocument dataDraftDocument = arg.getDraft();
        draft = dataDraftDocument.toObjectDataDraft();
        String title = draft.getTitle();
        if (StringUtils.isNotEmpty(title) && title.length() > 200) {
            draft.setTitle(title.substring(0, 200));
        }
        initDraftObjData(draft);
    }

    private void validateDetail() {
        Map<String, List<IObjectData>> slaveDraftData = draft.getSlaveDraftData();
        if (CollectionUtils.notEmpty(slaveDraftData)) {
            for (Map.Entry<String, List<IObjectData>> entry : slaveDraftData.entrySet()) {
                String detailApiName = entry.getKey();
                List<IObjectData> detailDataList = entry.getValue();
                validateDetailCount(detailApiName, detailDataList);
            }
        }
    }


    private void validateDetailCount(String detailApiName, List<IObjectData> detailDataList) {
        // 根据资源包校验从对象数据的条数
        if (!AppFrameworkConfig.isNotCheckCountDetailObject(detailApiName)) {
            TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo.builder()
                    .licenseService(serviceFacade)
                    .user(actionContext.getUser())
                    .build()
                    .init(Sets.newHashSet(ModulePara.ModuleBiz.MASTER_DETAIL.getBizCode()));
            tenantLicenseInfo.checkDetailObjectCount(detailDataList.size(), detailApiName);
        }
    }

    /*
     * 获取title信息
     *
     * 取主数据的主属性，如果为null，则采用"对象名称+无标题+创建时间"命名
     */
    private String getTitle() {
        return StringUtils.isNotBlank(draft.getMasterDraftData().getName()) ? draft.getMasterDraftData().getName() :
                Objects.nonNull(draft.getCreateTime()) ?
                        String.format("%s+%s+%s", objectDescribe.getDisplayName(), I18N.text(
                                I18NKey.NO_TITLE), draft.getCreatedBy()) :
                        String.format("%s+%s+%s", objectDescribe.getDisplayName(), I18N.text(I18NKey.NO_TITLE), Instant.now().toEpochMilli());
    }

    @SuppressWarnings("unchecked")
    protected void initDraftObjData(IObjectDataDraft draft) {
        IObjectDescribe master = objectDescribe;
        IObjectData masterDraft = draft.getMasterDraftData();
        initMaster(master, masterDraft);

        // 主对象原始数据，编辑数据时获取到的，端上用于多人同时编辑 diff
        Object originalMaster = masterDraft.get("originalData");
        if (originalMaster instanceof Map) {
            initMaster(master, masterDraft);
        }

        Map<String, List<IObjectData>> detailsDraft = draft.getSlaveDraftData();
        if (CollectionUtils.empty(detailsDraft)) {
            return;
        }
        List<IObjectDescribe> detailDescList = serviceFacade.findDetailDescribes(actionContext.getTenantId(), objectDescribe.getApiName());
        Map<String, IObjectDescribe> detailDescMap = detailDescList.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, Function.identity()));
        detailsDraft.forEach((detailApi, detailsDrafts) -> {
            IObjectDescribe detail = detailDescMap.get(detailApi);
            initDetails(detail, detailsDrafts);
        });

        // 从对象原始数据，编辑数据时获取到的，端上用于多人同时编辑 diff
        Object originalDetails = masterDraft.get("originalDetails");
        if (originalDetails instanceof Map) {
            if (CollectionUtils.empty((Map<?, ?>) originalDetails)) {
                return;
            }
            ((Map<?, ?>) originalDetails).forEach((detailApi, detailsDrafts) -> {
                IObjectDescribe detail = detailDescMap.get((String) detailApi);
                initDetails(detail, (List) detailsDrafts);
            });
        }
    }

    protected void initMaster(IObjectDescribe master, IObjectData masterDraft) {
        ObjectDataExt.of(masterDraft).removeSignedUrl(master);
    }

    @SuppressWarnings("unchecked")
    protected void initDetails(IObjectDescribe detail, List detailDrafts) {
        if (CollectionUtils.empty(detailDrafts)) {
            return;
        }

        for (Object detailDraft : detailDrafts) {
            if (detailDraft instanceof Map) {
                detailDraft = new ObjectData(((Map<String, Object>) detailDraft));
            }
            ObjectDataExt.of((IObjectData) detailDraft).removeSignedUrl(detail);
        }
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        @JSONField(name = "draft")
        @JsonProperty(value = "draft")
        @SerializedName(value = "draft")
        private ObjectDataDraftDocument draft;

//        @JSONField(name = "object_data")
//        @JsonProperty(value = "object_data")
//        @SerializedName(value = "object_data")
//        private ObjectDataDocument objectData;
//        @JSONField(name = "details")
//        @JsonProperty(value = "details")
//        @SerializedName(value = "details")
//        private Map<String, List<ObjectDataDocument>> details;
    }

    @Data
    public static class Arg {
        @JSONField(name = "draft")
        @JsonProperty(value = "draft")
        @SerializedName(value = "draft")
        private ObjectDataDraftDocument draft;
//        @JSONField(name = "object_data")
//        @JsonProperty(value = "object_data")
//        @SerializedName(value = "object_data")
//        private ObjectDataDocument objectData;
//        @JSONField(name = "details")
//        @JsonProperty(value = "details")
//        @SerializedName(value = "details")
//        private Map<String, List<ObjectDataDocument>> details;
    }
}
