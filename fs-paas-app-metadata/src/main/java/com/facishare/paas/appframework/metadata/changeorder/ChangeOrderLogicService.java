package com.facishare.paas.appframework.metadata.changeorder;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/3/20
 */
public interface ChangeOrderLogicService {

    List<DescribeInfo> findSupportChangeOrderDescribes(String tenantId);

    OriginalAndChangeDescribes findDescribesByOriginalApiName(User user, String originalDescribe);

    Optional<IObjectDescribe> findChangeOrderDescribeByOriginalApiName(User user, String originalDescribe);

    OpenChangeOrderResult openChangeOrder(User user, String originalDescribeApiName);

    void openDetailChangeOrder(User user, IObjectDescribe describe);

    void closeChangeOrder(User user, String originalDescribeApiName);

    void syncChangeOrderDescribe(User user, String originalDescribeApiName);

    void syncChangeOrderDescribeWithOriginalDescribe(User user, String originalDescribeApiName);

    void syncFieldI18n(User user, IObjectDescribe describe, List<String> changeFieldNames);

    List<IObjectData> findChangeOrderOriginalData(User user, String describeApiName, Collection<String> dataIds);

    void saveChangeOrderOriginalData(User user, String describeApiName, Collection<IObjectData> dataList, boolean isUpdate);

    List<IObjectData> findAndMergeObjectDataWithOriginalData(User user, IObjectDescribe describe, List<IObjectData> dataList);

    List<IObjectData> findChangeOrderSnapshot(User user, IObjectDescribe describe, Collection<String> ids);

    ILayout findLayoutWithChangeOrder(User user, String describeApiName, String layoutType, Function<String, ILayout> function);

    Map<String, Layout> findDetailLayoutsWithChangeOrder(User user, IObjectDescribe describe, Function<String, Map<String, Layout>> function);

    boolean isOpenChangeOrder(User user, String describeApiName);

    void addFieldForOriginalDescribe(User user, IObjectDescribe describe);

    String findOriginalApiNameByChangeOrder(User user, String changeOrderApiName);

    Map<String, String> batchFindOriginalApiNameByChangOrderList(User user, List<String> changeOrderApiNameList);

    boolean canStartChangeOrder(User user, IObjectDescribe describe, IObjectData objectData);

    boolean canStartChangeOrder(User user, IObjectDescribe describe, IObjectData objectData, boolean allowUnderReview);

}
