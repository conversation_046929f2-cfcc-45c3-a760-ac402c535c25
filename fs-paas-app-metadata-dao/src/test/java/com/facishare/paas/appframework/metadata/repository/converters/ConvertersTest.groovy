package com.facishare.paas.appframework.metadata.repository.converters

import com.facishare.paas.appframework.common.util.JacksonUtils
import spock.lang.Specification

/**
 * create by <PERSON><PERSON><PERSON> on 2021/04/20
 */
class ConvertersTest extends Specification {

    def "test get"() {
        when:
        def converter = Converters.INSTANCE.get(type)
        then:
        converter.class == expect
        where:
        type       || expect
        boolean    || BooleanConvert
        Boolean    || BooleanConvert

        BigDecimal || BigDecimalConvert

        Byte       || ByteConvert
        byte       || ByteConvert

        char       || CharacterConvert
        Character  || CharacterConvert

        double     || DoubleConvert
        Double     || DoubleConvert

        float      || FloatConvert
        Float      || FloatConvert

        int        || IntegerConvert
        Integer    || IntegerConvert

        Long       || LongConvert
        long       || LongConvert

        short      || ShortConvert
        Short      || ShortConvert
    }

    def "test decode bool"() {
        when:
        def converter = Converters.INSTANCE.get(type)
        then:
        converter.decode(value) == expect
        where:
        type    | value   || expect
        boolean | true    || true
        boolean | false   || false
        boolean | null    || null
        boolean | "true"  || true
        boolean | "false" || false
        boolean | "123"   || false
        boolean | 0       || false
        boolean | 1       || true
        boolean | -1      || true

        Boolean | true    || true
        Boolean | false   || false
        Boolean | null    || null
        Boolean | "true"  || true
        Boolean | "false" || false
        Boolean | "123"   || false
        Boolean | 0       || false
        Boolean | 1       || true
        Boolean | -1      || true
    }

    def "test"() {

        when:
        def value = JacksonUtils.toJson(json)
        then:
        println(json)
        println(value)
        noExceptionThrown()
        where:
        json | _
        """{"name":"zhangsan"}"""|_
    }
}
