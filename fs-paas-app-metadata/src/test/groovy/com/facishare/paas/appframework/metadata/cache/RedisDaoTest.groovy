package com.facishare.paas.appframework.metadata.cache

import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.appframework.metadata.DuplicatedSearchDataServiceImpl
import com.github.jedis.support.MergeJedisCmd
import com.google.common.collect.Lists
import org.apache.commons.codec.digest.DigestUtils
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification
import spock.lang.Unroll

import javax.annotation.Resource
import java.util.function.Consumer

/**
 * create by zhaoju on 2020/10/16
 */
//@ContextConfiguration(value = "classpath:applicationContext.xml")
class RedisDaoTest extends Specification {


    RedisDao redisDao
    MergeJedisCmd mergeJedisCmd

    def setup() {
        mergeJedisCmd = Mock(MergeJedisCmd)
        redisDao = new RedisDao('redisCache': mergeJedisCmd)
    }

    @Unroll
    def "sinter should handle #scenario correctly"() {
        when:
        def result = redisDao.sinter(keys)

        then:
        result == expectedResult
        redisCalls * mergeJedisCmd.sinter(_ as String[]) >> mockResult

        where:
        scenario           | keys              | expectedResult             | redisCalls | mockResult
        "empty input"      | []                | Collections.emptySet()     | 0          | null
        "null input"       | null              | Collections.emptySet()     | 0          | null
        "single key"       | ["key1"]          | ["value1", "value2"].toSet() | 1        | ["value1", "value2"].toSet()
        "multiple keys"    | ["key1", "key2"]  | ["commonValue"].toSet()    | 1          | ["commonValue"].toSet()
    }

    def "sinter should pass correct array to redisCache"() {
        given:
        def keys = ["key1", "key2", "key3"]

        when:
        redisDao.sinter(keys)

        then:
        1 * mergeJedisCmd.sinter({ String[] args ->
            args.length == 3 &&
                    args[0] == "key1" &&
                    args[1] == "key2" &&
                    args[2] == "key3"
        }) >> ["result"].toSet()
    }




    @Unroll
    def "evalScript should handle #scenario correctly"() {
        given:
        def script = "return KEYS[1] .. ARGV[1]"
        def sha256Hex = DigestUtils.sha256Hex(script)

        when:
        def result = redisDao.evalScript(script, keys as List, args as String[])

        then:
        1 * mergeJedisCmd.scriptExists(sha256Hex) >> scriptExists
        scriptLoadCalls * mergeJedisCmd.scriptLoad(script) >> sha256Hex
        1 * mergeJedisCmd.evalsha(sha256Hex, keys, Lists.newArrayList(args)) >> expectedResult
        result == expectedResult

        where:
        scenario               | keys     | args     | scriptExists | scriptLoadCalls | expectedResult
        "existing script"      | ["key1"] | ["arg1"] | true         | 0               | "key1arg1"
        "non-existing script"  | ["key2"] | ["arg2"] | false        | 1               | "key2arg2"
        "multiple keys/args"   | ["k1", "k2"] | ["a1", "a2"] | true | 0               | "result"
    }

    def "evalScript should handle exceptions"() {
        given:
        def script = "return redis.call('INVALID_COMMAND')"
        def sha256Hex = DigestUtils.sha256Hex(script)
        def keys = ["key"]
        def args = ["arg"]

        when:
        redisDao.evalScript(script, keys as List, args as String[])

        then:
        1 * mergeJedisCmd.scriptExists(sha256Hex) >> true
        1 * mergeJedisCmd.evalsha(sha256Hex, keys, Lists.newArrayList(args)) >> { throw new RuntimeException("Redis error") }

        thrown(RuntimeException)
    }

    def "evalScript should pass correct parameters to evalsha"() {
        given:
        def script = "return KEYS[1] .. ARGV[1] .. ARGV[2]"
        def sha256Hex = DigestUtils.sha256Hex(script)
        def keys = ["testKey"]
        def args = ["arg1", "arg2"]

        when:
        redisDao.evalScript(script, keys as List, args as String[])

        then:
        1 * mergeJedisCmd.scriptExists(sha256Hex) >> true
        1 * mergeJedisCmd.evalsha(sha256Hex, keys, { List argList ->
            argList == Lists.newArrayList(args)
        }) >> "testKeyarg1arg2"
    }

    def "test set and get methods"() {
        given:
        mergeJedisCmd.setex(_, _, _) >> { String key, int expire, String value -> }
        mergeJedisCmd.get(_) >> expectedValue

        when:
        redisDao.set(key, value, expire)
        def result = redisDao.getStrCache(key)

        then:
        result == expectedValue

        where:
        key       | value           | expire || expectedValue
        "testKey" | "testValue"     | 60     || "testValue"
        "key1"    | "anotherValue"  | 120    || "anotherValue"
    }

    def "test zadd method"() {
        given:
        mergeJedisCmd.zadd(_, _) >> expectedResult
        mergeJedisCmd.expire(_, _) >> 1

        when:
        def result = redisDao.zadd(key, content, timeout)

        then:
        result == expectedResult

        where:
        key        | content                   | timeout || expectedResult
        "zsetKey1" | ["a", "b", "c"]           | 60      || 3L
        "zsetKey2" | ["x", "y"]                | 120     || 2L
        "zsetKey3" | Collections.emptyList()   | 30      || -1L
    }

    def "test mget method"() {
        given:
        mergeJedisCmd.mget(_) >> expectedValues

        when:
        def result = redisDao.mget(keys as String[])

        then:
        result == expectedValues

        where:
        keys                   || expectedValues
        ["key1", "key2"]       || ["value1", "value2"]
        ["key3", "key4", "key5"] || ["value3", "value4", "value5"]
    }

    def "test delKey method"() {
        given:
        mergeJedisCmd.del(_) >> expectedResult

        when:
        def result = redisDao.delKey(key)

        then:
        result == expectedResult

        where:
        key         || expectedResult
        "testKey1"  || 1L
        "testKey2"  || 0L
    }

    def "test pipelineSet method"() {
        given:
        mergeJedisCmd.pipeline(_) >> { Consumer<MergeJedisCmd> pipelineConsumer -> pipelineConsumer.accept(mergeJedisCmd) }
        mergeJedisCmd.set(_, _) >> "OK"
        mergeJedisCmd.expire(_, _) >> 1

        when:
        redisDao.pipelineSet(keyMap, seconds)

        then:
        1 * mergeJedisCmd.pipeline(_)

        where:
        keyMap                              | seconds
        ["key1": "value1", "key2": "value2"] | 60
        ["key3": "value3"]                   | 120
    }

    /*def "test evalScript"() {
        when:
        def strings = ["""["key1","key2"]""", """["key3","key2"]""", """["key3","key4"]"""]
        redisDao.pipelinesadd(arg, 3)
        then:
        def result = redisDao.evalScript(script, strings, "temp_key")
        println "===========" + result
        println result.class
        where:
        script                                     | arg                                                                                                     || _
        DuplicatedSearchDataServiceImpl.LUA_SCRIPT | ["key1": ["111", "123", "3333"], "key2": ["222", "111", "3333"], "key3": ["222", "123", "111", "3333"]] || _

    }

    def "test evalScript1"() {
        when:
        def strings = ["""["key1","key2"]""", """["key3","key2"]"""]
        redisDao.pipelinesadd(arg, 3)
        then:
        def result = redisDao.evalScript(script, strings)
        println result
        println result.class
        where:
        script                                                                | arg                                                                             || _
        """
            local arg = ARGV[1]
            local temp_keys = {}
            for i, value in ipairs(KEYS) do
                local body = cjson.decode(value)
                local temp_key = arg.."_"..tostring(i)
                redis.call("sinterstore", temp_key, unpack(body))
                table.insert(temp_keys, temp_key)
            end
            local result = redis.call("sunion", unpack(temp_keys))
            redis.call("del", unpack(temp_keys))
            return result

        """                                                        | ["key1": ["111", "123"], "key2": ["222", "111"], "key3": ["222", "123", "111"]] || _
    }

    def "test evalScript2"() {
        when:
        def strings = ["key1", "key2", "key3", "key4"]
        redisDao.pipelinesadd(arg, 3)
        then:
        def result = redisDao.evalScript(script, strings, String.valueOf(5000), JacksonUtils.toJson(arg))
        println result
        println(redisDao.sinter(arg.keySet()))
        println(redisDao.getExpireTime("key1"))
        println(redisDao.smembers("key1"))
        println(redisDao.smembers("key"))
        println(redisDao.smembers("key4"))
        1 == 1
        where:
        script                                               | arg                                                                             || _
        """
            local time = tonumber(ARGV[1])
            local value = ARGV[2]
            local map = cjson.decode(value)
            for i, key in ipairs(KEYS) do
                redis.call("sadd", key, map[key])
                redis.call("expire", key, time)
            end
            return 1
        """                                       | ["key1": ["111", "123"], "key2": ["222", "111"], "key3": ["222", "123", "111"]] || _
    }*/
}
