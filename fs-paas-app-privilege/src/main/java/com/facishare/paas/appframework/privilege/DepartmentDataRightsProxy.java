package com.facishare.paas.appframework.privilege;


import com.facishare.paas.appframework.privilege.dto.*;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.rest.core.codec.IRestCodeC;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestResource(
        value = "DEPARTMENT-DATA-RIGHTS",
        desc = "部门内数据权限", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.privilege.DepartmentDataRightsProxy$Codec"
)
public interface DepartmentDataRightsProxy {

    @POST(value = "/deptRights/queryAll", desc = "查询所有")
    DepartmentDataRightsQueryAll.Result queryAll(@HeaderMap Map<String, String> header, @Body DepartmentDataRightsQueryAll.Arg arg);

    @POST(value = "/deptRights/upSert", desc = "插入更新")
    void upsert(@HeaderMap Map<String, String> header, @Body DepartmentDataRightsUpsert.Arg arg);

    @POST(value = "/deptRights/delete", desc = "删除")
    void delete(@HeaderMap Map<String, String> header, @Body DepartmentDataRightsDelete.Arg arg);

    @POST(value = "/deptRights/queryByIds", desc = "根据ID查询")
    List<DepartmentDataRights> queryByIds(@HeaderMap Map<String, String> header, @Body DepartmentDataRightsQueryByIds.Arg arg);


    class Codec implements IRestCodeC {

        private static final ObjectMapper objectMapper = new ObjectMapper();

        static {
            objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
            objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        }

        @Override
        public <T> byte[] encodeArg(T obj) {
            try {
                return objectMapper.writeValueAsBytes(obj);
            } catch (IOException e) {
                throw new RuntimeException("encode error", e);
            }
        }

        @Override
        @SuppressWarnings("unchecked")
        public <T> T decodeResult(int statusCode, Map<String, List<String>> map, byte[] bytes, Class<T> aClass) {

            if (statusCode != 200) {
                throw new RuntimeException("decode error,body:" + (bytes != null ? new String(bytes) : ""));
            }
            try {
                JsonNode root = objectMapper.readTree(bytes);
                int errCode = root.get("errCode").asInt();
                if (errCode != 0) {
                    throw new RestProxyBusinessException(errCode, root.get("errMessage").asText());
                }
                if (Objects.equals(aClass.getSimpleName(), "void")) {
                    return null;
                }
                new TypeReference<List<DepartmentDataRights>>() {};
                if (Objects.equals(aClass.getSimpleName(),"List")){
                    return (T)objectMapper.readValue(root.get("result").toString(),new TypeReference<List<DepartmentDataRights>>() {});
                }
                return objectMapper.readValue(root.get("result").toString(), aClass);
            } catch (IOException e) {
                throw new RuntimeException("decode error", e);
            }
        }
    }
}
