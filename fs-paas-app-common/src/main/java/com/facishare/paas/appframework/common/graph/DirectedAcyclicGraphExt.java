package com.facishare.paas.appframework.common.graph;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Deque;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * create by <PERSON><PERSON><PERSON> on 2018/09/04
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DirectedAcyclicGraphExt {
    static final String GRAPH_HAS_CYCLE = "graph %s has cycle.";

    public static boolean isEmpty(BaseGraph graph) {
        return graph == null || graph.nodes().size() == 0;
    }

    public static boolean notEmpty(BaseGraph graph) {
        return graph != null && graph.nodes().size() > 0;
    }

    /**
     * 通过深度优先遍历，查找两个节点在图中是否强链接
     *
     * @param graph
     * @param nodeU
     * @param nodeV
     * @param <N>
     * @return
     */
    public static <N> boolean hasRoute(BaseGraph<N> graph, N nodeU, N nodeV) {
        Objects.requireNonNull(nodeU, "nodeU");
        Objects.requireNonNull(nodeV, "nodeV");
        Set<N> visited = Sets.newHashSetWithExpectedSize(graph.nodes().size());
        return dfs(graph, nodeU, nodeV, visited);
    }

    private static <N> boolean dfs(BaseGraph<N> graph, N nodeU, N nodeV, Set<N> visited) {
        if (nodeU.equals(nodeV)) {
            return true;
        }

        visited.add(nodeU);
        Set<N> nodeList = graph.successors(nodeU);

        if (nodeList.size() > 0) {
            for (N node : nodeList) {
                if (visited.contains(node)) {
                    continue;
                }
                if (dfs(graph, node, nodeV, visited)) {
                    return true;
                }
            }
        }

        return false;
    }

    public static <N> Map<N, List<List<N>>> getRoute(BaseGraph<N> graph, N node) {
        Deque<N> stack = Lists.newLinkedList();
        Map<N, List<List<N>>> resultMap = Maps.newHashMap();
        Set<N> successorsNodes = graph.successors(node);
        successorsNodes.forEach(successorsNode -> {
            stack.push(successorsNode);
            resultMap.put(successorsNode, dfs(graph, successorsNodes.stream().filter(n -> !successorsNode.equals(n)).collect(Collectors.toSet()), stack));
        });
        return resultMap;
    }

    private static <N> List<List<N>> dfs(BaseGraph<N> graph, Set<N> successorsNodes, Deque<N> stack) {
        List<List<N>> resultList = Lists.newArrayList();
        Deque<N> routeStack = Lists.newLinkedList();
        while (!stack.isEmpty()) {
            N node = stack.poll();
            routeStack.offer(node);
            // 与目标节点间包含通路,记录路径的栈中保存的路径节点，并将当前节点从路径栈中删除
            if (successorsNodes.contains(node)) {
                resultList.add((List) routeStack);
                routeStack.pollLast();
                continue;
            }

            Set<N> successors = graph.successors(node);
            successors.forEach(stack::push);
            // 当前节点没有后继节点，从路径栈中删掉该节点
            if (CollectionUtils.empty(successors)) {
                routeStack.poll();
            }
        }
        return resultList;
    }

}
