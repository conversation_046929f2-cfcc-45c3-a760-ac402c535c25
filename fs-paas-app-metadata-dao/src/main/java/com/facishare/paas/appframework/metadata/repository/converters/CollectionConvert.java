package com.facishare.paas.appframework.metadata.repository.converters;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.util.Types;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.reflect.TypeToken;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * Created by zhouwr on 2021/12/7.
 */
public class CollectionConvert<E> implements Converter<Collection<E>> {

    /**
     * JSON 反序列化指定的类型
     */
    private TypeReference<Collection<E>> typeReference;
    /**
     * 获取字段的定义类型和运行时类型
     */
    private TypeToken<Collection<E>> typeToken;

    public static CollectionConvertFactory getConvertFactory() {
        return CollectionConvertFactory.COLLECTION_CONVERT_FACTORY;
    }
    private CollectionConvert(TypeToken<Collection<E>> typeToken) {
        this.typeToken=typeToken;
        this.typeReference=JacksonUtils.getTypeReference(typeToken.getType());
    }

    @Override
    public Collection<E> convert(Object value) {
        if (Objects.isNull(value)){
            return null;
        }
        if (value instanceof String) {
            return JacksonUtils.fromJson(((String) value), typeReference);
        }

        if (typeToken.getRawType().isAssignableFrom(value.getClass())) {
            Collection<?> list = (Collection<?>) value;
            if (CollectionUtils.empty(list)){
                return (Collection<E>) list;
            }
            Object next = list.iterator().next();
            if (Types.detectFirstGenericArgType(typeReference.getType()).isAssignableFrom(next.getClass())) {
                return (Collection<E>) list;
            }
        }
        return JacksonUtils.fromJson(JacksonUtils.toJson(value), typeReference);
    }

    public static class CollectionConvertFactory implements ConverterFactory {

        private static CollectionConvertFactory COLLECTION_CONVERT_FACTORY = new CollectionConvertFactory();

        @Override
        public <T> CollectionConvert create(TypeToken<T> typeToken) {
            Class<? super T> rawType = typeToken.getRawType();
            if (!Collection.class.isAssignableFrom(rawType)) {
                return null;
            }
            return new CollectionConvert(typeToken);
        }
    }
}
