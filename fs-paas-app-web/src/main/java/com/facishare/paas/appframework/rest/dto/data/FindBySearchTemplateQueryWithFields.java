package com.facishare.paas.appframework.rest.dto.data;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * create by z<PERSON><PERSON> on 2019/04/15
 */
public interface FindBySearchTemplateQueryWithFields {

    @Data
    class Arg {
        @JSONField(name = "field_list")
        private List<String> fieldList;
        @JSONField(name = "search_query_info")
        private String searchTemplateQuery;
        @JSONField(name = "keep_all_multi_lang_value")
        private Boolean keepAllMultiLangValue;
    }
}
