package com.facishare.paas.appframework.log;

import com.facishare.paas.appframework.common.service.dto.TenantInfo;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.dto.*;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 审计日志服务
 * <p>
 * Created by liyiguang on 2017/6/29.
 */
public interface LogService {

    void logInternational(User user, EventType eventType, ActionType actionType, String objectApiName, String textMessage, InternationalItem internationalTextMessage);

    void logInternational(User user, EventType eventType, ActionType actionType, String objectApiName, String module, String textMessage, InternationalItem internationalTextMessage);

    void logInternational(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, String module, String dataId, String textMessage, InternationalItem internationalTextMessage);

    void logWithInternationalCustomMessage(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, IObjectData data, String customMessage, InternationalItem customMessageInternational);

    void logDataWithInternationalCustomMessage(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, IObjectData data, String customMessage, InternationalItem customMessageInternational);

    void masterDetailLog(User user, EventType eventType, ActionType actionType, Map<String, IObjectDescribe> objectDescribes, List<IObjectData> data);

    void masterDetailLog(User user, EventType eventType, ActionType actionType, Map<String, IObjectDescribe> objectDescribes, List<IObjectData> data, String peerName, String peerDisplayName);

    void masterDetailLog(User user, EventType eventType, ActionType actionType, Map<String, IObjectDescribe> objectDescribes, List<IObjectData> data,
                         String peerName, String peerDisplayName, Map<String, Object> extendsInfo);

    void masterDetailLog(User user, EventType eventType, ActionType actionType, Map<String, IObjectDescribe> objectDescribes, List<IObjectData> data,
                         String peerName, String peerDisplayName, Map<String, Object> extendsInfo, ConvertSourceContainer convertSourceContainer);

    String masterModifyLog(User user, Map<String, IObjectDescribe> objectDescribes, IObjectData masterData,
                           Map<String, Map<String, Map<String, Object>>> updatedFieldMap, IObjectData dbMasterData, Map<String, Object> detailChangeMap);

    String masterModifyLog(User user, Map<String, IObjectDescribe> objectDescribes, IObjectData masterModifyData,
                           Map<String, Map<String, Map<String, Object>>> updatedFieldMap, IObjectData dbMasterData, List<String> changeDetailApiName);

    void detailModifyLog(User user, IObjectDescribe objectDescribe, List<IObjectData> modifyDataList,
                         Map<String, Map<String, Object>> updatedFieldMap, List<IObjectData> dbDetailDataList, String masterLogId);

    void detailModifyLog(User user, ActionType actionType, IObjectDescribe objectDescribe, List<IObjectData> modifyDataList,
                         Map<String, Map<String, Object>> updatedFieldMap, List<IObjectData> dbDetailDataList, String masterLogId);

    void logWithCustomMessage(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe, List<IObjectData> dataList,
                              Map<String, Map<String, Object>> updatedFieldMap, List<IObjectData> dbDataList, String peerName, String peerDisplayName, String customMessage);

    void log(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe, IObjectData modifyData,
             Map<String, Object> updatedFieldMap, IObjectData dbData, String peerName, String peerDisplayName,
             InternationalItem internationalPeerDisplayName, String bizId, Map<String, Object> extendsInfo);

    void sendLog(List<LogInfo> logList);

    MasterLogInfo fillMasterModifyLog(User user, Map<String, IObjectDescribe> objectDescribes, IObjectData masterData,
                                      Map<String, Map<String, Map<String, Object>>> updatedFieldMap, IObjectData dbMasterData,
                                      Map<String, Object> detailChangeMap);

    MasterLogInfo fillMasterModifyLog(User user, Map<String, IObjectDescribe> objectDescribes, IObjectData masterData,
                                      Map<String, Map<String, Map<String, Object>>> updatedFieldMap, IObjectData dbMasterData,
                                      Map<String, Object> detailChangeMap, Map<String, Object> extendsInfo);

    MasterLogInfo fillMasterModifyLog(User user, Map<String, IObjectDescribe> objectDescribes, IObjectData masterData,
                                      Map<String, Map<String, Map<String, Object>>> updatedFieldMap, IObjectData dbMasterData,
                                      Map<String, Object> detailChangeMap, Map<String, Object> extendsInfo, ConvertSourceContainer convertSourceContainer);

    MasterLogInfo fillMasterModifyLog(User user, ActionType actionType, Map<String, IObjectDescribe> objectDescribes, IObjectData masterData,
                                      Map<String, Map<String, Map<String, Object>>> updatedFieldMap, IObjectData dbMasterData,
                                      Map<String, Object> detailChangeMap, Map<String, Object> extendsInfo);


    MasterLogInfo fillMasterModifyLog(User user, Map<String, IObjectDescribe> objectDescribes, IObjectData masterData,
                                      Map<String, Map<String, Map<String, Object>>> updatedFieldMap, IObjectData dbMasterData,
                                      List<String> changeDetailApiName);

    MasterLogInfo fillMasterModifyLog(User user, Map<String, IObjectDescribe> objectDescribes, IObjectData masterData,
                                      Map<String, Map<String, Map<String, Object>>> updatedFieldMap, IObjectData dbMasterData,
                                      List<String> changeDetailApiName, Map<String, Object> extendsInfo);

    MasterLogInfo fillMasterModifyLog(User user, ActionType actionType, Map<String, IObjectDescribe> objectDescribes, IObjectData masterData,
                                      Map<String, Map<String, Map<String, Object>>> updatedFieldMap, IObjectData dbMasterData,
                                      List<String> changeDetailApiName, Map<String, Object> extendsInfo);

    MasterLogInfo fillMasterModifyLog(User user, ActionType actionType, Map<String, IObjectDescribe> objectDescribes, IObjectData masterData,
                                      Map<String, Map<String, Map<String, Object>>> updatedFieldMap, IObjectData dbMasterData,
                                      List<String> changeDetailApiName, Map<String, Object> extendsInfo, ConvertSourceContainer convertSourceContainer);

    void updateImportLog(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe, List<IObjectData> dataList, Map<String, IObjectData> dbDataMap);

    void bulkRecordEditLog(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe, List<IObjectData> dataList, List<IObjectData> dbDataList);

    void updateImportLog(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe, List<IObjectData> dataList, Map<String, IObjectData> dbDataMap, Map<String, Object> extendsInfo);

    void logByActionType(User user, EventType eventType, ActionType actionType, List<IObjectData> oldDataList, List<IObjectData> dataList, IObjectDescribe objectDescribe);

    void logWithUpdateLayout(User user, EventType eventType, ActionType actionType, ILayout before, ILayout after, String objectApiName, String textMessage, InternationalItem internationalTextMessage);

    //Mob端查询某apiName的某id的全部修改记录
    MobSearchResult mobSearchModifyRecord(String apiName, String dataId, int limit, long operationTime, User user);

    MobSearchResult mobSearchModifyRecord(String apiName, String dataId, String operationType, int limit, long operationTime, User user, List<String> bizId, List<String> otherBizIds);

    MobSearchResult mobSearchModifyRecord(User user, LogCondition condition);

    //Mob端查询某masterId的某从对象的全部修改记录
    MobSearchResult mobSearchModifyRecordForMaster(String masterId, String detailApiName, int limit, long operationTime, User user);

    MobSearchResult mobSearchModifyRecordForMaster(String masterId, String detailApiName, String operationType, int limit, long operationTime, User user);

    MobSearchResult mobSearchModifyRecordForMaster(String masterId, String detailApiName, String masterLogId, String operationType, User user, List<String> bizId, List<String> otherBizIds, int limit, long operationTime);

    MobSearchResult mobSearchModifyRecordForMaster(User user, LogCondition condition);

    //Mob端查询某masterId的全部修改记录,其中的不同的从对象的修改记录放在MobSearchResult放在richResult中
    MobSearchRichResult mobSearchModifyRecordForMaster(String masterId, String masterApiName, List<String> detailApiNameList, int limit, long operationTime, User user);

    //Web端查询某apiName的某id的全部修改记录
    WebSearchResult webSearchModifyRecord(String apiName, String dataId, int limit, int pageNumber, User user);

    //apiName的objectId 业务id 查询修改记录
    WebSearchResult webSearchModifyRecord(String apiName, String dataId, String operationalType, int limit, int pageNumber, User user, List<String> bizId, List<String> otherBizIds);

    /**
     * web端对象修改记录查询
     *
     * @param user      用户
     * @param condition 查询条件
     * @return 修改记录内容
     */
    WebSearchResult webSearchModifyRecord(User user, LogCondition condition);

    //Web端查询某masterId的某从对象的全部修改记录
    WebSearchResult webSearchModifyRecordForMaster(String masterId, String detailApiName, int limit, int pageNumber, User user);

    //Web端查询某masterId的某从对象的全部修改记录
    WebSearchResult webSearchModifyRecordForMaster(String masterId, String detailApiName, String operationalType, int limit, int pageNumber, User user);

    //主从修改记录查询,包含BizId进行过滤
    WebSearchResult webSearchModifyRecordForMaster(String masterId, String detailApiName, String masterLogId, String operationalType, User user, List<String> bizId, List<String> otherBizIds, int limit, int pageNumber);

    /**
     * web端查询从对象修改记录
     *
     * @param user      用户
     * @param condition 查询条件
     * @return 修改记录内容
     */
    WebSearchResult webSearchModifyRecordForMaster(User user, LogCondition condition);

    LogInfo getLogById(String apiName, String logId, User user);

    GetLogByModuleResult getLogByModule(String module, int pageSize, int pageNumber, User user);

    GetManageLogListResult getManageLogList(User user, String module, int pageSize, int pageNumber, List<Filter> filters, String sortField, int sortType);

    //查询自定义函数记录
    SearchFunctionModel.Result getFunctionLog(User user, String logId, String functionApiName, Date operationTimeFrom, Boolean success, Date operationTimeTo, Map<String, Boolean> sorts, String traceId, String name, int pageNumber, int pageSize);

    //查询自定义函数记录
    SearchFunctionLogV2.Result getFunctionLogV2(User user, String logId, String functionApiName, String traceId, Date operationTimeFrom, Boolean success, Date operationTimeTo, Map<String, Boolean> sorts, String name, int pageSize, boolean nextPage);

    //查询自定义函数运行时详细日志 走mongo
    SearchFunctionDetailModel.Result getFunctionLogDetail(User user, String traceId, String logId);

    //走es
    SearchFunctionDetailModel.Result getNewFunctionLogDetail(User user, String traceId, String logId);

    SearchFunctionEsLogDetail.Result searchFunctionEsLogDetail(User user, SearchFunctionEsLogDetail.Arg arg);

    SearchFunctionEsLog.Result searchFunctionEsLog(User user, SearchFunctionEsLog.Arg arg);

    void logUdefFunction(User user, EventType eventType, ActionType actionType, String objectApiName, String apiName, IUdefFunction function);

    void logTemporaryRights(User user, EventType eventType, ActionType actionType, String objectApiName, String json);

    void logDataPermission(User user, EventType eventType, ActionType actionType, List<LogInfo.ObjectSnapshot> snapshots);

    void logRelatedTeamDataPermission(User user, IObjectDescribe objectDescribe, Map<String, Set<String>> addPermissionRecords, Map<String, Set<String>> removePermissionRecords);

    void logRuleSetting(User user, EventType eventType, ActionType actionType, String objectApiName, String textMessage);

    Set<TenantInfo> getTenantInfos(Set<String> tenantIds, User user);

    void deleteAuditLog(String tenantId, String describeApiName, String dataId, String otherBizId);

    /**
     * 删除审计日志
     */
    void deleteLog(User user, DeleteLog.Arg arg);

    QueryFunctionVerificationLog.Result queryFunctionValidationLog(User user, String sessionId);

    InsertFunctionVerificationLog.Result insertFunctionValidationLog(User user, List<FunctionValidationLog> logs);

    void logByCustomized(User user, EventType eventType, ActionType actionType, String apiName, String dataID,
                         List<LogInfo.LintMessage> textMsg, List<TeamMemberInfo.Msg> msgs);

    LoginLog.Result getLoginLog(User user, LoginLog.Arg arg);

    AuditLog.Result getAuditLog(User user, AuditLog.Arg arg);

    AuditLog.Result getAuditLogCount(User user, AuditLog.Arg arg);

    ModifyLog.Result getModifyLog(User user, ModifyLog.Arg arg);

    void bulkRecordEditLog(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe,
                           List<IObjectData> dataList, List<IObjectData> dbDataList,
                           List<String> updateFields, LogExtendInfo logExtendInfo);

    @Data
    @Builder
    class MasterLogInfo {
        String masterLogId;
        List<LogInfo> logList;
    }

    // 以下是被@Deprecated标记的方法
    @Deprecated
    void log(User user, EventType eventType, ActionType actionType, String objectApiName, String textMessage);

    @Deprecated
    void log(User user, EventType eventType, ActionType actionType, String objectApiName, Map<String, Object> objectSnapshot, String textMessage);

    @Deprecated
    void log(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, IObjectData data);

    @Deprecated
    void log(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, IObjectData data, Map<String, Object> extendsInfo);

    @Deprecated
    void logWithCustomMessage(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, IObjectData data, String customMessage);

    @Deprecated
    void logWithCustomMessage(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, IObjectData data, String customMessage, List<TeamMemberInfo.Msg> msgs);

    @Deprecated
    void logWithCustomMessage(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, List<IObjectData> dataList, String customMessage);

    @Deprecated
    void logCustomMessageOnly(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, IObjectData data, String customMessage);

    @Deprecated
    void log(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, List<IObjectData> data);

    @Deprecated
    void log(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, List<IObjectData> data, Map<String, Object> extendsInfo);

    @Deprecated
    void log(User user, EventType eventType, ActionType actionType, Map<String, IObjectDescribe> objectDescribes, List<IObjectData> data);

    @Deprecated
    void log(User user, EventType eventType, ActionType actionType, Map<String, IObjectDescribe> objectDescribes, List<IObjectData> data, String masterLogId);

    @Deprecated
    void log(User user, EventType eventType, ActionType actionType, Map<String, IObjectDescribe> objectDescribes, List<IObjectData> data, String masterLogId, ConvertSourceContainer convertSourceContainer);

    @Deprecated
    void log(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe, IObjectData data,
             Map<String, Object> updatedFieldMap, IObjectData dbData);

    @Deprecated
    void log(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe, IObjectData data,
             Map<String, Object> updatedFieldMap, IObjectData dbData, String bizId);

    @Deprecated
    void log(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe, IObjectData data,
             Map<String, Object> updatedFieldMap, IObjectData dbData, String peerName, String peerDisplayName, String bizId);

    @Deprecated
    void log(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe, IObjectData modifyData,
             Map<String, Object> updatedFieldMap, IObjectData dbData, String peerName, String peerDisplayName, String bizId, Map<String, Object> extendsInfo);

    @Deprecated
    void logWithUpdateLayout(User user, EventType eventType, ActionType actionType, ILayout before, ILayout after, String objectApiName, String textMessage);

    @Deprecated
    MobSearchResult mobSearchModifyRecordForMaster(String masterId, String detailApiName, String masterLogId, String operationType, User user, List<String> bizId, List<String> otherBizIds);

    @Deprecated
    WebSearchResult webSearchModifyRecordForMaster(String masterId, String detailApiName, String masterLogId, String operationalType, User user, List<String> bizId, List<String> otherBizIds);
}