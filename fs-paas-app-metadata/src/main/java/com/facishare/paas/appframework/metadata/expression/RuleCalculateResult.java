package com.facishare.paas.appframework.metadata.expression;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.IRule;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2018/5/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RuleCalculateResult {
    private boolean match;
    private IRule rule;
    private IObjectData data;
    private List<RuleMatchResult> matchResults = Lists.newArrayList();

    public void copy(RuleCalculateResult source) {
        this.match = source.isMatch();
        this.rule = source.getRule();
        this.data = source.getData();
        this.matchResults = source.getMatchResults();
    }

    public boolean hasEnableBlock() {
        return matchResults.stream()
                .anyMatch(matchResults -> Optional.ofNullable(matchResults.getRule().getEnableBlocking()).orElse(true));
    }

    public List<RuleMatchResult> getEnableBlockMatchResults() {
        return matchResults.stream()
                // 默认启用验证规则
                .filter(matchResult -> Optional.ofNullable(matchResult.getRule().getEnableBlocking()).orElse(true))
                .collect(Collectors.toList());
    }

    @Data
    @Builder
    public static class RuleMatchResult {
        private boolean match;
        private IRule rule;
    }
}
