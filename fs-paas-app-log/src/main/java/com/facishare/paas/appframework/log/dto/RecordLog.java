package com.facishare.paas.appframework.log.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.*;

import java.util.List;

/**
 * Created by liyiguang on 2017/8/16.
 */
public interface RecordLog {

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    class Arg{
        @Singular
        @SerializedName("log")
        @JSONField(name = "log")
        List<LogInfo> logs;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Result{
        int code;
        String errorMessage;

        public boolean isSuccess(){
            return code == 200;
        }
    }
}
