package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.FindTodoCount;
import com.facishare.paas.appframework.common.service.dto.FindTodoList;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(value = "PAAS-MESSAGE-BOX", desc = "代办消息服务", contentType = "application/json")
public interface MessageBoxServiceProxy {
    @POST(value = "/todo/red-num", desc = "H5待办消息接口")
    FindTodoCount.Result findTodoCount(@HeaderMap Map<String, String> header, @Body FindTodoCount.Arg arg);

    @POST(value = "/todo/list", desc = "待办消息列表")
    FindTodoList.Result findTodoList(@HeaderMap Map<String, String> header, @Body FindTodoList.Arg arg);
}
