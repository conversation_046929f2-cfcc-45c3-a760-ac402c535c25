package com.facishare.paas.appframework.metadata.mask;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.EncryptUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.cache.KryoUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * Created by zhaooju on 2022/12/13
 */
@Service
public class MaskFieldEncryptServiceImpl implements MaskFieldEncryptService {
    @Override
    public String encode(String value) {
        return EncryptUtil.encode(value);
    }

    @Override
    public void encode(IObjectData data, Collection<IFieldDescribe> fieldNames) {
        if (CollectionUtils.empty(fieldNames)) {
            return;
        }
        for (IFieldDescribe fieldDescribe : fieldNames) {
            String fieldName = fieldDescribe.getApiName();
            if (!data.containsField(fieldName)) {
                continue;
            }
            Object value = data.get(fieldName);
            if (ObjectDataExt.isValueEmpty(value)) {
                data.set(FieldDescribeExt.getMaskEncryptFieldName(fieldName), "");
                continue;
            }

            EncryptData encryptData = EncryptData.of(value, fieldDescribe.getType());
            String encodeValue = encode(encryptData.serialize());
            data.set(FieldDescribeExt.getMaskEncryptFieldName(fieldName), encodeValue);
        }
    }

    @Override
    public String decode(String value) {
        return EncryptUtil.decode(value);
    }

    @Override
    public void decode(IObjectData data, Collection<IFieldDescribe> fieldNames) {
        if (CollectionUtils.empty(fieldNames)) {
            return;
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(data);
        for (IFieldDescribe fieldDescribe : fieldNames) {
            String fieldName = fieldDescribe.getApiName();
            String fieldApiName = FieldDescribeExt.getMaskEncryptFieldName(fieldName);

            Object value = data.get(fieldApiName);
            objectDataExt.remove(fieldApiName);
            if (ObjectDataExt.isValueEmpty(value)) {
                // __encrypt 为空，且data中没有原字段的key，认为是用户手动清空字段的行为
                if (!data.containsField(fieldName)) {
                    data.set(fieldName, null);
                }
                continue;
            }
            String decodeValue = decode(value.toString());
            EncryptData encryptData = EncryptData.deserialize(decodeValue);
            Object formatValue = encryptData.formatValue();
            if (ObjectDataExt.isValueEmpty(formatValue)) {
                data.set(fieldName, null);
                continue;
            }
            data.set(fieldName, formatValue);
        }
    }

    @Override
    public void decode(IObjectData objectData, IObjectDescribe describe) {
        List<String> fieldNames = Lists.newArrayList();
        for (String key : ObjectDataExt.of(objectData).toMap().keySet()) {
            if (FieldDescribeExt.isMaskEncryptField(key)) {
                String fieldName = FieldDescribeExt.getFieldNameFromMaskEncryptFieldName(key);
                fieldNames.add(fieldName);
            }
        }
        List<IFieldDescribe> maskFields = ObjectDescribeExt.of(describe).getFieldByApiNames(fieldNames);
        decode(objectData, maskFields);
    }

    @Slf4j
    private static class EncryptData {
        private Object value;
        private String fieldType;

        public EncryptData() {
        }

        private EncryptData(Object value, String fieldType) {
            this.value = value;
            this.fieldType = fieldType;
        }

        public static EncryptData of(Object value, String fieldType) {
            return new EncryptData(value, fieldType);
        }

        public static EncryptData deserialize(String value) {
            try {
                return KryoUtils.deserialize(Hex.decodeHex(value), EncryptData.class);
            } catch (Exception e) {
                log.warn("deserialize fail, value:{}", value, e);
                throw new ValidateException(I18NExt.text(I18NKey.DECODING_FAILURE));
            }
        }

        public String serialize() {
            return Hex.encodeHexString(KryoUtils.serialize(this));
        }

        public Object formatValue() {
            return value;
        }
    }
}
