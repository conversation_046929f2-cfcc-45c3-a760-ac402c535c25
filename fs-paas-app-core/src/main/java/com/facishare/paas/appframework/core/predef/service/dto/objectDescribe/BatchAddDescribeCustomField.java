package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.ObjectFieldDescribeDocument;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface BatchAddDescribeCustomField {
    @Data
    class Arg {
        private String describeAPIName;

        private List<ObjectFieldDescribeDocument> fields;

    }

    @Builder
    @Data
    class Result {
        private Map objectDescribe;
    }
}
