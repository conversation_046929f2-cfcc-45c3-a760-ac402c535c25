package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ContextCacheUtil;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.metadata.AggregateRule;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.layout.component.ISummaryComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.SummaryComponentInfo;
import com.facishare.paas.appframework.metadata.query.SearchQueryContext;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.AggRule;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;
import java.util.stream.Collectors;

public class StandardSummaryFieldController extends PreDefineController<StandardSummaryFieldController.Arg, StandardSummaryFieldController.Result> {

    protected ObjectDescribeExt objectDescribe;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected void before(Arg arg) {
        ContextCacheUtil.openContextCache();
        objectDescribe = findDescribe();
        stopWatch.lap("findDescribe");
        super.before(arg);
    }

    private ObjectDescribeExt findDescribe() {
        IObjectDescribe describe = serviceFacade.findObjectUseThreadLocalCache(controllerContext.getTenantId(), controllerContext.getObjectApiName());
        return ObjectDescribeExt.of(describe);
    }

    @Override
    protected Result doService(Arg arg) {
        ISearchTemplateQuery query = getSearchTemplateQuery();
        stopWatch.lap("getSearchTemplateQuery");

        Set<String> unauthorizedFields = getUnauthorizedFields();
        stopWatch.lap("getUnauthorizedFields");

        Map<String, String> summaryData = querySummaryData(query, unauthorizedFields);
        stopWatch.lap("querySummaryData");

        return buildResult(summaryData);
    }

    private Result buildResult(Map<String, String> summaryData) {
        return Result.builder()
                .summaryData(summaryData)
                .build();
    }

    private Map<String, String> querySummaryData(ISearchTemplateQuery query, Set<String> unauthorizedFields) {
        if (CollectionUtils.empty(arg.getSummaryFieldList())) {
            return Maps.newHashMap();
        }
        List<ISummaryComponentInfo> summaryInfos = arg.getSummaryFieldList().stream()
                .map(x -> new SummaryComponentInfo(x))
                .filter(x -> !Strings.isNullOrEmpty(x.getApiName()) && !Strings.isNullOrEmpty(x.getFieldName()) && !Strings.isNullOrEmpty(x.getType()))
                .filter(x -> ISummaryComponentInfo.TYPES.contains(x.getType()))
                .filter(x -> ObjectDescribeExt.of(objectDescribe).isFieldActive(x.getFieldName())
                        && FieldDescribeExt.of(objectDescribe.getFieldDescribe(x.getFieldName())).isNumberTypeField())
                .filter(x -> !unauthorizedFields.contains(x.getFieldName()))
                .peek(x -> {
                    if (Strings.isNullOrEmpty(x.getApiName())) {
                        x.setApiName(AggregateRule.buildAggFieldName(x.getType(), x.getFieldName()));
                    }
                })
                .collect(Collectors.toList());

        Map<String, String> summaryData = serviceFacade.querySummaryData(controllerContext.getUser(), controllerContext.getObjectApiName(),
                (SearchTemplateQuery) query, summaryInfos);

        //加工掩码字段
        List<ISummaryComponentInfo> maskSummaryInfos = summaryInfos.stream()
                .filter(x -> FieldDescribeExt.of(objectDescribe.getFieldDescribe(x.getFieldName())).isShowMask())
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(maskSummaryInfos)) {
            IObjectData data = new ObjectData();
            List<String> maskFieldNames = maskSummaryInfos.stream().map(x -> x.getFieldName()).distinct().collect(Collectors.toList());
            List<IFieldDescribe> maskFields = ObjectDescribeExt.of(objectDescribe).getFieldByApiNames(maskFieldNames);
            maskSummaryInfos.forEach(x -> data.set(x.getFieldName(), "0"));
            serviceFacade.fillMaskFieldValue(controllerContext.getUser(), Lists.newArrayList(data), maskFields, false);
            maskSummaryInfos.stream()
                    .filter(x -> data.containsField(FieldDescribeExt.getShowFieldName(x.getFieldName())))
                    .forEach(x -> summaryData.put(x.getApiName(), (String) data.get(FieldDescribeExt.getShowFieldName(x.getFieldName()))));
        }

        return summaryData;
    }

    private ISearchTemplateQuery getSearchTemplateQuery() {
        Query query = defineQuery();
        query = customizeQuery(query);
        ISearchTemplateQuery templateQuery = query.toSearchTemplateQuery();
        //聚合查询不需要order_by
        SearchTemplateQueryExt.of(templateQuery).resetOrderBy(Lists.newArrayList());
        if (log.isDebugEnabled()) {
            log.debug("defineQuery ending, SearchTemplateQuery:{}", templateQuery.toJsonString());
        }
        validateQuery(templateQuery);
        return templateQuery;
    }

    protected Query customizeQuery(Query query) {
        return query;
    }

    private void validateQuery(ISearchTemplateQuery query) {
        if (!SearchTemplateQueryExt.validateQueryPattern(query)) {
            log.warn("defineQuery fail, ei:{}, objectApiName:{}, SearchTemplateQuery:{}",
                    controllerContext.getTenantId(), controllerContext.getObjectApiName(), query.toJsonString());
        }
    }

    private Query defineQuery() {
        SearchQueryContext queryContext = SearchQueryContext.builder()
                .searchTemplateType(arg.getSearchTemplateType())
                .templateId(arg.getSearchTemplateId())
                .isIgnoreSceneFilter(isIgnoreSceneFilter())
                .isIgnoreSceneRecordType(isIgnoreSceneRecordType())
                .build();
        Query searchQuery = serviceFacade.findSearchQuery(controllerContext.getUser(), objectDescribe, arg.getSearchQueryInfo(), queryContext);
        searchQuery.resetOrders(Lists.newArrayList());
        return searchQuery;
    }

    private Set<String> getUnauthorizedFields() {
        Set<String> unauthorizedFields = serviceFacade.getUnauthorizedFields(controllerContext.getUser(), objectDescribe.getApiName());
        return CollectionUtils.empty(unauthorizedFields) ? Collections.emptySet() : unauthorizedFields;
    }

    private boolean isIgnoreSceneFilter() {
        return Objects.equals(arg.getIsIgnoreSceneFilter(), Boolean.TRUE);
    }

    private boolean isIgnoreSceneRecordType() {
        return Objects.equals(arg.getIsIgnoreSceneRecordType(), Boolean.TRUE);
    }

    @Data
    public static class Arg {
        @JSONField(name = "search_query_info")
        @JsonProperty("search_query_info")
        @SerializedName("search_query_info")
        private String searchQueryInfo;

        @JSONField(name = "search_template_id")
        @JsonProperty("search_template_id")
        @SerializedName("search_template_id")
        private String searchTemplateId;

        @JSONField(name = "search_template_type")
        @JsonProperty("search_template_type")
        @SerializedName("search_template_type")
        private String searchTemplateType;

        @JSONField(name = "summary_field_list")
        @JsonProperty("summary_field_list")
        @SerializedName("summary_field_list")
        private List<Map<String, Object>> summaryFieldList;

        @JSONField(name = "ignore_scene_filter")
        @JsonProperty("ignore_scene_filter")
        @SerializedName("ignore_scene_filter")
        private Boolean isIgnoreSceneFilter;

        @JSONField(name = "ignore_scene_record_type")
        @JsonProperty("ignore_scene_record_type")
        @SerializedName("ignore_scene_record_type")
        private Boolean isIgnoreSceneRecordType;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private Map<String, String> summaryData;
    }

}
