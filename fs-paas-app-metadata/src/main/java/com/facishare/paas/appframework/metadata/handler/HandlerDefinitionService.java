package com.facishare.paas.appframework.metadata.handler;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.repository.model.HandlerDefinition;

import java.util.List;

/**
 * Created by zhouwr on 2023/2/6.
 */
public interface HandlerDefinitionService {
    void batchCreate(User user, List<HandlerDefinition> handlerDefinitions);

    void batchUpdate(User user, List<HandlerDefinition> handlerDefinitions);

    void batchUpsert(User user, List<HandlerDefinition> handlerDefinitions);

    void batchDelete(User user, List<HandlerDefinition> definitionList);

    List<HandlerDefinition> findByApiNames(String tenantId, List<String> handlerApiNames, boolean doMerge, boolean includeDisabled);

    HandlerDefinition findByApiName(String tenantId, String handlerApiName, boolean doMerge, boolean includeDisabled);
}
