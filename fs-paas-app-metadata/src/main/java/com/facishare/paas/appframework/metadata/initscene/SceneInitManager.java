package com.facishare.paas.appframework.metadata.initscene;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class SceneInitManager implements ApplicationContextAware {
    private Map<String, SceneInitProvider> providerMap = Maps.newHashMap();

    @Autowired
    @Qualifier("defaultSceneProvider")
    private DefaultSceneProvider sceneProvider;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        initFunctionPrivilegeProviderMap(applicationContext);
    }

    private void initFunctionPrivilegeProviderMap(ApplicationContext applicationContext) {
        Map<String, SceneInitProvider> springBeanMap = applicationContext.getBeansOfType(SceneInitProvider.class);
        springBeanMap.values().forEach(provider -> {
            if (StringUtils.isNotEmpty(provider.getApiName())) {
                providerMap.put(provider.getApiName(), provider);
            }
        });
    }

    public SceneInitProvider getProvider(String apiName) {
        return providerMap.getOrDefault(apiName, sceneProvider);
    }
}
