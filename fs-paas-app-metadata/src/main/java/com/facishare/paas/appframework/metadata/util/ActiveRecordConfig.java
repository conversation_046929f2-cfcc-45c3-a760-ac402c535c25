package com.facishare.paas.appframework.metadata.util;

import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by shun on 2020/3/8
 */
public class ActiveRecordConfig {

    private static Set<String> grayEnterpriseIdSet = Sets.newHashSet();
    private static Set<String> grayEnterpriseIdSet700 = Sets.newHashSet();
    private static Set<String> grayEnterpriseIdSetImport = Sets.newHashSet();


    static {
        ConfigFactory.getInstance().getConfig("fs-social-active-record-config", iConfig -> {
            String grayEnterpriseIds = iConfig.get("grayEnterpriseIds");
            if (StringUtils.isNotBlank(grayEnterpriseIds)) {
                grayEnterpriseIdSet = Arrays.stream(grayEnterpriseIds.split(",")).collect(Collectors.toSet());
            }

            String grayEnterpriseIds700 = iConfig.get("grayEnterpriseIds700");
            if (StringUtils.isNotBlank(grayEnterpriseIds700)) {
                grayEnterpriseIdSet700 = Arrays.stream(grayEnterpriseIds700.split(",")).collect(Collectors.toSet());
            }

            String grayEnterpriseIdsImport = iConfig.get("grayEnterpriseIdsImport");
            if (StringUtils.isNotBlank(grayEnterpriseIdsImport)) {
                grayEnterpriseIdSetImport = Arrays.stream(grayEnterpriseIdsImport.split(",")).collect(Collectors.toSet());
            }

        });
    }

    public static boolean isInActiveRecordGray(String enterpriseStr) {
        if (grayEnterpriseIdSet.contains("*")) {
            return true;
        }
        if (grayEnterpriseIdSet.contains(enterpriseStr)) {
            return true;
        }
        return false;
    }

    public static boolean isInActiveRecordGray700(String enterpriseStr) {
        if (grayEnterpriseIdSet700.contains("*")) {
            return true;
        }
        if (grayEnterpriseIdSet700.contains(enterpriseStr)) {
            return true;
        }
        return false;
    }

    public static boolean isInActiveRecordImportGray(String enterpriseStr) {
        if (grayEnterpriseIdSetImport.contains("*")) {
            return true;
        }
        if (grayEnterpriseIdSetImport.contains(enterpriseStr)) {
            return true;
        }
        return false;
    }

}
