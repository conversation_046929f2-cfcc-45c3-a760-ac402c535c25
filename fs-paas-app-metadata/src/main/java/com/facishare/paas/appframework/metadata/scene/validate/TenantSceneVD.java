package com.facishare.paas.appframework.metadata.scene.validate;

import com.facishare.paas.appframework.metadata.dto.scene.ITenantScene;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NonNull;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/05/25
 */
@Data
@AllArgsConstructor
public class TenantSceneVD {
    @NonNull
    private ITenantScene tenantScene;
    @NonNull
    private IObjectDescribe objectDescribe;
    private boolean isUpdate;

    public static TenantSceneVD fromCreate(ITenantScene tenantScene, IObjectDescribe describe) {
        tenantScene.setId(null);
        return new TenantSceneVD(tenantScene, describe, false);
    }

    public static TenantSceneVD fromUpdate(ITenantScene tenantScene, IObjectDescribe describe) {
        return new TenantSceneVD(tenantScene, describe, true);
    }

}
