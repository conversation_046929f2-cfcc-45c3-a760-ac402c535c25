package com.facishare.paas.appframework.metadata.copy;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.config.ApplicationLayeredGrayService;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.layout.EditLayout;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.options.SelectFieldDependenceLogicService;
import com.facishare.paas.appframework.metadata.options.bo.SelectFieldDependence;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.facishare.paas.appframework.metadata.ObjectDescribeExt.of;
import static com.facishare.paas.metadata.impl.describe.EmployeeManyFieldDescribe.WHERES;
import static com.facishare.paas.metadata.impl.describe.EmployeeManyFieldDescribe.WHERE_TYPE;

@Slf4j
@Service("copyObjectService")
public class CopyObjectServiceImpl implements CopyObjectService {
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private LayoutLogicService layoutLogicService;
    @Autowired
    private OptionalFeaturesService optionalFeaturesService;
    @Autowired
    @Qualifier("copyObjectProducer")
    private AppDefaultRocketMQProducer copyObjectProducer;
    @Autowired
    private SelectFieldDependenceLogicService dependenceLogicService;
    @Autowired
    private ApplicationLayeredGrayService applicationLayeredGrayService;


    @Override
    @Transactional
    public void copyObject(User user, String sourceDescribeApiName, String objectApiName, String describeLabel) {
        IObjectDescribe sourceObjectDescribe = describeLogicService.findObject(user.getTenantId(), sourceDescribeApiName);
        List<String> needToDelFieldApiNames = ObjectDescribeExt.of(sourceObjectDescribe).getCopyObjectNeedToDelFieldApiNames();
        StopWatch stopWatch = StopWatch.create("copyObject");
        ObjectDescribeExt targetDescribe = processObjectDescribe(user, objectApiName, describeLabel, sourceObjectDescribe, needToDelFieldApiNames);
        stopWatch.lap("processObjectDescribe");
        describeLogicService.createDescribe(user, targetDescribe, null, null, false);
        stopWatch.lap("createDescribe");
        copyLayouts(user, sourceDescribeApiName, objectApiName, needToDelFieldApiNames);
        stopWatch.lap("copyLayouts");
        copyOptionalFeatureSwitch(user, objectApiName, sourceObjectDescribe);
        stopWatch.lap("copyOptionalFeatureSwitch");
        copyFieldDependence(user, sourceDescribeApiName, targetDescribe);
        stopWatch.lap("copyFieldDependence");
        sendCopyObjectMessage(user, sourceDescribeApiName, objectApiName, describeLabel);
        stopWatch.lap("sendCopyObjectMessage");
        stopWatch.logSlow(2000);
    }

    private void copyFieldDependence(User user, String sourceDescribeApiName, IObjectDescribe objectDescribe) {
        List<SelectFieldDependence> sourceSelectFieldDependence = dependenceLogicService.findAll(user, sourceDescribeApiName);
        if (CollectionUtils.empty(sourceSelectFieldDependence)) {
            return;
        }
        sourceSelectFieldDependence.forEach(dependence -> {
            SelectFieldDependence selectFieldDependence = new SelectFieldDependence(objectDescribe.getApiName(), dependence.getFieldApiName(), dependence.getChildFieldName(), dependence.getDependence());
            dependenceLogicService.copy(user, objectDescribe, selectFieldDependence);
        });
    }

    private ObjectDescribeExt processObjectDescribe(User user, String objectApiName, String describeLabel, IObjectDescribe sourceObjectDescribe, List<String> needToDelFieldApiNames) {
        ObjectDescribeExt targetDescribe = of(sourceObjectDescribe.copy());
        targetDescribe.setApiName(objectApiName);
        targetDescribe.setDisplayName(describeLabel);
        targetDescribe.setCreatedBy(user.getUserId());
        targetDescribe.setCreateTime(System.currentTimeMillis());
        targetDescribe.setLastModifiedBy(user.getUserId());
        targetDescribe.setLastModifiedTime(System.currentTimeMillis());
        targetDescribe.getFieldDescribes().forEach(field -> {
            FieldDescribeExt.of(field).remove(IFieldDescribe.ID);
            field.setCreateTime(System.currentTimeMillis());
        });
        targetDescribe.setIsActive(false);

        // 自增编号函数清空
        targetDescribe.getFunctionAutoNumber().ifPresent(autoNumber -> {
            autoNumber.setAutoNumberType(null);
            autoNumber.setFuncApiName(null);
        });
        // 主从字段判断
        targetDescribe.getMasterDetailFieldDescribe().ifPresent(masterDetailFieldDescribe -> {
            String masterApiName = masterDetailFieldDescribe.getTargetApiName();
            List<IObjectDescribe> detailDescribes = describeLogicService.findSimpleDetailDescribes(user.getTenantId(), masterApiName);
            //一个Master对象的Detail个数限制：5个。
            int maxDetailCountByTenant = AppFrameworkConfig.getMaxDetailObjectCount(user.getTenantId());
            int maxDetailCountPreObject = AppFrameworkConfig.getMaxDetailObjectCountPreObject(masterApiName);
            int maxDetailCount = Math.max(maxDetailCountByTenant, maxDetailCountPreObject);
            if (detailDescribes.size() >= maxDetailCount) {
                needToDelFieldApiNames.add(masterDetailFieldDescribe.getApiName());
            }
        });
        // 人员部门 函数处理
        targetDescribe.stream()
                .filter(x -> FieldDescribeExt.of(x).isEmployee()
                        || FieldDescribeExt.of(x).isEmployeeManyField()
                        || FieldDescribeExt.of(x).isDepartmentField()
                        || FieldDescribeExt.of(x).isDepartmentManyField())
                .forEach(fieldDescribe -> {
                    if (FilterExt.FUNCTION_WHERE_TYPE.equals(fieldDescribe.get(WHERE_TYPE, String.class))) {
                        fieldDescribe.set(WHERES, Lists.newArrayList());
                        fieldDescribe.set(WHERE_TYPE, FilterExt.FIELD_TYPE);
                    }
                });
        // 查找关联字段
        targetDescribe.getReferenceFieldDescribes().forEach(field -> {
            field.setTargetRelatedListLabel(describeLabel);
            field.setTargetRelatedListName(FieldDescribeExt.generateTargetRelatedListName());
            if (FilterExt.FUNCTION_WHERE_TYPE.equals(field.getWhereType())) {
                field.setWheres(Lists.newArrayList());
                field.setWhereType(FilterExt.FIELD_TYPE);
            }
            if (FilterExt.FUNCTION_WHERE_TYPE.equals(field.getRelatedWhereType())) {
                field.setRelatedWheres(Lists.newArrayList());
                field.setRelatedWhereType(FilterExt.FIELD_TYPE);
            }
            if (!ObjectDataExt.isValueEmpty(field.getDefaultValue())) {
                Object defaultValue = field.getDefaultValue();
                if (defaultValue instanceof String) {
                    String defaultValueStr = defaultValue.toString();
                    needToDelFieldApiNames.forEach(apiName -> {
                        if (defaultValueStr.contains(apiName)) {
                            field.setDefaultValue(StringUtils.EMPTY);
                            field.setDefaultIsExpression(false);
                        }
                    });
                }
            }
        });
        targetDescribe.removeFieldDescribeList(needToDelFieldApiNames);
        // 计算字段、引用字段禁用
        targetDescribe.getFormulaFields().forEach(x -> {
            if (StringUtils.equals(x.getApiName(), FieldDescribeExt.DISPLAY_NAME)) {
                return;
            }
            x.setActive(false);
            needToDelFieldApiNames.add(x.getApiName());
        });
        targetDescribe.getQuoteFieldDescribes().forEach(x -> {
            x.setActive(false);
            needToDelFieldApiNames.add(x.getApiName());
        });
        // 移除描述中store_table_name属性
        targetDescribe.setStoreTableName(null);
        return targetDescribe;
    }

    private void sendCopyObjectMessage(User user, String sourceDescribeApiName, String objectApiName, String describeLabel) {
        // 发送消息
        CopyObjectMessage cpyObjectMessage = CopyObjectMessage.of(sourceDescribeApiName,
                objectApiName, describeLabel, user.getTenantId(), user.getUserId());
        int selectorHash = String.format("%s|%s", user.getTenantId(), objectApiName).hashCode() & 0x7fffffff;
        copyObjectProducer.sendMessage(cpyObjectMessage.toMessageData(), selectorHash);
    }

    private void copyLayouts(User user, String targetObjectApiName, String objectApiName, List<String> needToDelFieldApiNames) {
        // copy 布局的时候只处理crm应用下的布局
        LayoutLogicService.LayoutContext context = LayoutLogicService.LayoutContext.of(user);
        // 复制布局
        List<ILayout> layouts = layoutLogicService.findByTypesIncludeFlowLayout(context, targetObjectApiName, Lists.newArrayList(LayoutTypes.DETAIL, LayoutTypes.EDIT, LayoutTypes.LIST, LayoutTypes.LIST_LAYOUT));
        layouts.forEach(layout -> {
            LayoutExt layoutExt = LayoutExt.of(layout);
            layout.setRefObjectApiName(objectApiName);
            layout.setId(null);
            layoutExt.setUiEventIds(Lists.newArrayList());
            layoutExt.removeFields(needToDelFieldApiNames);
            layoutExt.getFormComponents().forEach(FormComponentExt::removeEmptySection);
            layoutExt.removeTopInfoComponentFields(needToDelFieldApiNames);
            layoutExt.removeRelatedListComponents();
        });
        layoutLogicService.bulkCreate(layouts);
        if (layouts.stream().anyMatch(x -> LayoutExt.of(x).isEditLayout())) {
            String appId = getAppId(context, objectApiName);
            String key = EditLayout.buildStatusKey(objectApiName, appId);
            configService.upsertTenantConfig(user, key, String.valueOf(EditLayout.EditLayoutStatus.ENABLE.getCode()), ConfigValueType.STRING);
        }
    }

    private String getAppId(LayoutLogicService.LayoutContext context, String objectApiName) {
        String appId = context.getAppIdByLayoutType(LayoutTypes.EDIT);
        if (applicationLayeredGrayService.supportAppLayered(context.getUser(), appId, objectApiName)) {
            return appId;
        }
        return null;
    }

    private void copyOptionalFeatureSwitch(User user, String objectApiName, IObjectDescribe targetObjectDescribe) {
        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), targetObjectDescribe);
        if (!(optionalFeaturesSwitch.getIsRelatedTeamEnabled() && optionalFeaturesSwitch.getIsFollowUpDynamicEnabled()
                && optionalFeaturesSwitch.getIsModifyRecordEnabled() && optionalFeaturesSwitch.getIsGlobalSearchEnabled()) || optionalFeaturesSwitch.getMultiFieldSort()) {
            configService.createTenantConfig(user, objectApiName + "|" + OptionalFeaturesService.OPTIONAL_FEATURES, JSON.toJSONString(optionalFeaturesSwitch), ConfigValueType.JSON);
        }
    }
}
