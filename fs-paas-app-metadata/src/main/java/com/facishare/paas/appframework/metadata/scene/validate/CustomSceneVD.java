package com.facishare.paas.appframework.metadata.scene.validate;

import com.facishare.paas.appframework.metadata.dto.scene.MutableCustomScene;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NonNull;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/06/02
 */
@Data
@AllArgsConstructor
public class CustomSceneVD {
    @NonNull
    private MutableCustomScene customScene;
    @NonNull
    private IObjectDescribe objectDescribe;
    private boolean isUpdate;

    public static CustomSceneVD fromCreate(MutableCustomScene customScene, IObjectDescribe describe) {
        return new CustomSceneVD(customScene, describe, false);
    }

    public static CustomSceneVD fromUpdate(MutableCustomScene customScene, IObjectDescribe describe) {
        return new CustomSceneVD(customScene, describe, true);
    }
}
