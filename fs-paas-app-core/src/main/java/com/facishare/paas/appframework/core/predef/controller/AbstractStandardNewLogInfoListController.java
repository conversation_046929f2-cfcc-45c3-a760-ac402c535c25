package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.common.service.dto.QueryGroupByIds;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.AuditLogConfig;
import com.facishare.paas.appframework.log.dto.*;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.domain.DomainPluginLogicService;
import com.facishare.paas.appframework.metadata.expansion.DescribeExpansionRender;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.appframework.privilege.dto.GetUserRoleInfo;
import com.facishare.paas.metadata.api.GroupField;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.fxiaoke.stone.commons.domain.constant.AuthModel;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Desc
 * <AUTHOR>
 * @CreateTime 2019-09-24 16:40
 */
public abstract class AbstractStandardNewLogInfoListController<A, R> extends PreDefineController<A, R> {

    protected IObjectDescribe objectDescribe;

    public final static List<String> DETAIL_MERGE_DATA_OPERATION_LIST = Lists.newArrayList(
            ActionType.Add.getId(), ActionType.Lock.getId(), ActionType.Unlock.getId(),
            ActionType.Invalid.getId(), ActionType.Recovery.getId(), ActionType.Delete.getId());

    public final static List<String> TEAM_MEMBER_ACTION_LIST = Lists.newArrayList(ActionType.AddEmployee.getId(),
            ActionType.AddSale.getId(), ActionType.RemoveEmployee.getId(), ActionType.RemoveSale.getId(),
            ActionType.ModifySale.getId(), ActionType.ChangeOwner.getId(), ActionType.CHANGE_PARTNER_OWNER.getId());

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected void before(A arg) {
        super.before(arg);
        objectDescribe = findDescribe();
    }

    protected abstract IObjectDescribe findDescribe();

    protected abstract List<LogRecord> getModifyRecordList(R result);

    @Override
    protected R doService(A arg) {
        return null;
    }

    public boolean isGrayCHRead() {
        return AuditLogConfig.isGrayAuditLogChRead(controllerContext.getTenantId());
    }

    protected final void handleEmployeeMultiLang(User user, List<LogRecord> logRecordList) {
        Set<String> allUserIds = Sets.newHashSet();
        List<String> userIds = logRecordList.stream()
                .filter(x -> Objects.nonNull(x.getOwner()))
                .map(x -> x.getOwner().getEmployeeID())
                .map(String::valueOf)
                .distinct()
                .collect(Collectors.toList());
        allUserIds.addAll(userIds);
        List<String> approvalUserIds = logRecordList.stream()
                .filter(x -> Objects.nonNull(x.getApprovalFlowInfo()))
                .filter(x -> Objects.nonNull(x.getApprovalFlowInfo().getOwner()))
                .map(x -> x.getApprovalFlowInfo().getOwner().getEmployeeID())
                .map(String::valueOf)
                .distinct()
                .collect(Collectors.toList());
        allUserIds.addAll(approvalUserIds);
        if (CollectionUtils.empty(allUserIds)) {
            return;
        }
        List<UserInfo> userInfos = serviceFacade.getUserNameByIds(user.getTenantId(), user.getUserId(), Lists.newArrayList(allUserIds));
        if (CollectionUtils.empty(userInfos)) {
            return;
        }
        Map<String, String> userInfoMap = userInfos.stream().collect(Collectors.toMap(UserInfo::getId, UserInfo::getName, (x, y) -> x));
        for (LogRecord logRecord : logRecordList) {
            EmpSimpleInfo owner = logRecord.getOwner();
            if (Objects.nonNull(owner)) {
                int employeeID = owner.getEmployeeID();
                String userName = userInfoMap.get(String.valueOf(employeeID));
                if (StringUtils.isNotBlank(userName)) {
                    owner.setName(userName);
                }
            }
            LogInfo.ApprovalFlowInfo approvalFlowInfo = logRecord.getApprovalFlowInfo();
            if (Objects.nonNull(approvalFlowInfo)) {
                LogInfo.EmployeeInfo approvalFlowInfoOwner = approvalFlowInfo.getOwner();
                if (Objects.nonNull(approvalFlowInfoOwner)) {
                    int employeeID = approvalFlowInfoOwner.getEmployeeID();
                    String userName = userInfoMap.get(String.valueOf(employeeID));
                    if (StringUtils.isNotBlank(userName)) {
                        approvalFlowInfoOwner.setName(userName);
                    }
                }
            }
        }
    }

    protected Map<String, Object> getFieldExtra() {
        DescribeExtra describeExtra = serviceFacade.findDescribeExtraByRenderType(controllerContext.getUser(), objectDescribe,
                Collections.emptyList(), DescribeExpansionRender.RenderType.Detail, true);
        return ObjectDescribeDocument.of(describeExtra);
    }

    protected void dealModifyDatas(IObjectDescribe objectDescribe, List<LogRecord> logRecordList) {
        if (Objects.isNull(objectDescribe) || CollectionUtils.empty(logRecordList)) {
            return;
        }
        // 获取组件内所有字段
        Map<String, List<String>> groupFieldMap = objectDescribe.getFieldDescribes().stream()
                .filter(x -> IFieldType.GROUP.equals(x.getType()))
                .collect(Collectors.toMap(IFieldDescribe::getApiName, x -> {
                    try {
                        return ((GroupField) x).getFieldList(objectDescribe)
                                .stream()
                                .map(IFieldDescribe::getApiName)
                                .collect(Collectors.toList());
                    } catch (Exception e) {
                        log.warn("get group field error", e);
                    }
                    return Lists.newArrayList();
                }));

        // 获取编辑类型的修改记录数据
        List<LogRecord> modifyList = logRecordList.stream()
                .filter(x -> Objects.nonNull(x)
                        && Objects.nonNull(x.getObjectData())
                        && ActionType.Modify.getId().equals(x.getOperationType()))
                .collect(Collectors.toList());

        Map<String, Map<String, LogInfo.DiffObjectData>> diffObjectDataMap = modifyList.stream()
                .collect(Collectors.toMap(LogRecord::getLogID,
                        logRecord -> logRecord.getObjectData().stream()
                                .collect(Collectors.toMap(LogInfo.DiffObjectData::getFieldApiName,
                                        diffObjectData -> diffObjectData, (x, y) -> y))));

        // 获取组件的数据
        Map<String, List<LogInfo.DiffObjectData>> groupFieldDataMap = Maps.newHashMap();
        groupFieldMap.forEach((k, v) -> {
            if (Objects.isNull(v)) {
                return;
            }
            v.stream().filter(Objects::nonNull).forEach(fieldApiName ->
                    diffObjectDataMap.forEach((logId, diffObjectDatas) ->
                            getGroupFieldData(groupFieldDataMap, k, logId, fieldApiName, diffObjectDatas)));
        });

        // 排序
        modifyList.forEach(x -> groupFieldMap.forEach((k, v) -> {
            x.getObjectData().removeIf(y -> v.contains(y.getFieldApiName()));
            x.getObjectData().addAll(CollectionUtils.nullToEmpty(groupFieldDataMap.get(getKey(x.getLogID(), k))));
        }));

    }

    protected void handleRichTextFieldValue(IObjectDescribe objectDescribe, List<LogRecord> logRecordList) {
        if (CollectionUtils.empty(logRecordList)) {
            return;
        }

        List<IObjectData> list = Lists.newArrayList();
        logRecordList.stream()
                .filter(a -> CollectionUtils.notEmpty(a.getObjectData()))
                .forEach(a -> a.getObjectData().forEach(b -> {
                    if (CollectionUtils.notEmpty(b.getOldValue())) {
                        list.add(new ObjectData(b.getOldValue()));
                    }

                    if (CollectionUtils.notEmpty(b.getValue())) {
                        list.add(new ObjectData(b.getValue()));
                    }
                }));
        serviceFacade.fillRichTextImageInfo(objectDescribe, list, controllerContext.getUser());
    }

    protected void fillObjectReferenceManyFieldValue(IObjectDescribe objectDescribe, List<LogRecord> logRecordList) {
        if (CollectionUtils.empty(logRecordList)) {
            return;
        }
        List<IObjectData> fillDataReq = Lists.newArrayList();
        logRecordList.stream()
                .filter(a -> CollectionUtils.notEmpty(a.getObjectData()))
                .forEach(a -> a.getObjectData().forEach(b -> {
                    if (CollectionUtils.notEmpty(b.getOldValue())) {
                        fillDataReq.add(new ObjectData(b.getOldValue()));
                    }

                    if (CollectionUtils.notEmpty(b.getValue())) {
                        fillDataReq.add(new ObjectData(b.getValue()));
                    }
                }));
        serviceFacade.fillObjectDataObjectManyWithRefObject(objectDescribe, fillDataReq, controllerContext.getUser());

    }

    public void removeOutDimensionRecords(List<LogRecord> logRecordList) {
        if (CollectionUtils.empty(logRecordList)) {
            return;
        }
        if (controllerContext.getUser().isOutUser()) {
            logRecordList.removeIf(x -> ActionType.Modify.getId().equals(x.getOperationType()) &&
                    CollectionUtils.notEmpty(x.getObjectData()) &&
                    x.getObjectData().stream()
                            .filter(Objects::nonNull)
                            .map(LogInfo.DiffObjectData::getRenderType)
                            .collect(Collectors.toList())
                            .contains(IFieldType.DIMENSION));
        }
    }

    protected final void removeFieldsByDomainPlugin(List<LogRecord> logRecordList) {
        try {
            if (CollectionUtils.empty(logRecordList)) {
                return;
            }
            DomainPluginLogicService.FieldConfig fieldConfig = infraServiceFacade.findFieldConfigByDomainPlugin(controllerContext.getTenantId(),
                    objectDescribe.getApiName(), null, getRecordTypes(), PageType.ModifyLog.name());
            Set<String> hiddenFields = fieldConfig.getHiddenFields(objectDescribe.getApiName());
            if (CollectionUtils.empty(hiddenFields)) {
                return;
            }
            logRecordList.forEach(x -> {
                List<LogInfo.DiffObjectData> objectDataList = x.getObjectData();
                if (CollectionUtils.empty(objectDataList)) {
                    return;
                }
                objectDataList.removeIf(o -> hiddenFields.contains(o.getFieldApiName()));
            });
        } catch (Exception e) {
            log.warn("removeFieldsByDomainPlugin failed", e);
        }
    }

    protected String getKey(String logId, String fieldApiName) {
        return logId + "_" + fieldApiName;
    }


    /**
     * @param groupFieldDataMap 组件里的字段修改记录数据
     * @param groupApiName      组件字段的apiName(如：地区组件字段)
     * @param logId             修改记录的ID
     * @param fieldApiName      组件里的字段apiName(如：国家、省、市、区等字段)
     * @param diffObjectDataMap 修改记录的数据
     */
    protected void getGroupFieldData(Map<String, List<LogInfo.DiffObjectData>> groupFieldDataMap,
                                     String groupApiName, String logId, String fieldApiName,
                                     Map<String, LogInfo.DiffObjectData> diffObjectDataMap) {

        LogInfo.DiffObjectData diffObjectData = diffObjectDataMap.get(fieldApiName);
        if (Objects.isNull(diffObjectData)) {
            return;
        }
        String key = getKey(logId, groupApiName);
        groupFieldDataMap.putIfAbsent(key, Lists.newArrayList());
        groupFieldDataMap.get(key).add(diffObjectData);
    }


    protected void dealDetail(String detailApiName, String masterLogId, List<LogRecord> logRecordList) {
        if (Strings.isNullOrEmpty(detailApiName) || Strings.isNullOrEmpty(masterLogId) || CollectionUtils.empty(logRecordList)) {
            return;
        }
        List<LogRecord> records = Lists.newArrayList();
        // 按类型过滤，把需要合并的数据合到一起
        logRecordList.stream().filter(x -> DETAIL_MERGE_DATA_OPERATION_LIST.contains(x.getOperationType()))
                .collect(Collectors.groupingBy(LogRecord::getOperationType)).forEach((k, v) -> {
                    List<ObjectInfo.ObjectData> objectDatas = Lists.newArrayList();
                    ObjectInfo objectInfo = ObjectInfo.builder()
                            .objectDatas(objectDatas)
                            .build();
                    v.forEach(x -> {
                        if (Objects.isNull(x.getObjectInfo()) || CollectionUtils.empty(x.getObjectInfo().getObjectDatas())) {
                            return;
                        }
                        x.getObjectInfo().getObjectDatas().forEach(y -> y.setLogID(x.getLogID()));
                        objectDatas.addAll(x.getObjectInfo().getObjectDatas());
                        objectInfo.setObjectApiName(x.getObjectInfo().getObjectApiName());
                        objectInfo.setObjectLabel(x.getObjectInfo().getObjectLabel());
                    });
                    if (Objects.isNull(v.get(0))) {
                        return;
                    }
                    v.get(0).setLogID(null);
                    v.get(0).setObjectInfo(objectInfo);
                    records.add(v.get(0));
                });
        // 按新建、删除、编辑排序
        logRecordList.removeIf(x -> DETAIL_MERGE_DATA_OPERATION_LIST.contains(x.getOperationType()));
        Map<String, List<LogRecord>> logRecordMap = records.stream().collect(Collectors.groupingBy(LogRecord::getOperationType));
        List<LogRecord> orderLogRecordList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(logRecordMap.get(ActionType.Add.getId()))) {
            orderLogRecordList.addAll(logRecordMap.get(ActionType.Add.getId()));
            logRecordMap.remove(ActionType.Add.getId());
        }
        if (CollectionUtils.notEmpty(logRecordMap.get(ActionType.Delete.getId()))) {
            orderLogRecordList.addAll(logRecordMap.get(ActionType.Delete.getId()));
            logRecordMap.remove(ActionType.Delete.getId());
        }
        logRecordMap.values().forEach(orderLogRecordList::addAll);
        logRecordList.addAll(0, orderLogRecordList);
    }

    protected LogRecord modifyRecordToLogRecord(ModifyRecord record) {
        return LogRecord.builder()
                .logID(record.getLogID())
                .logMsg(record.getLogMsg())
                .operationTime(record.getOperationTime())
                .operationType(record.getOperationType())
                .owner(record.getOwner())
                .snapShotType(record.getSnapShotType())
                .peerName(record.getPeerName())
                .operationLabel(record.getOperationLabel())
                .objectInfo(record.getObjectInfo())
                .msgList(record.getMsgList())
                .objectData(record.getObjectData())
                .peerReason(record.getPeerReason())
                .detailInfos(record.getDetailInfos())
                .masterLogId(record.getMasterLogId())
                .dataName(record.getDataName())
                .approvalFlowInfo(record.getApprovalFlowInfo())
                .configMsg(record.getConfigMsg())
                .traceId(record.getTraceId())
                .convertEventId(record.getConvertEventId())
                .sourceObjectInfo(record.getSourceObjectInfo())
                .sourceDetailInfos(record.getSourceDetailInfos())
                .build();
    }

    protected void filterSourceDetailSnapshot(List<ModifyRecord> modifyRecordList, String sourceId, String masterLogId) {
        if (CollectionUtils.empty(modifyRecordList)) {
            return;
        }
        if (Strings.isNullOrEmpty(sourceId) && !Strings.isNullOrEmpty(masterLogId)) {
            modifyRecordList.forEach(modifyRecord -> {
                if (StringUtils.isNotEmpty(modifyRecord.getConvertEventId())) {
                    modifyRecord.setConvertEventId(null);
                }
            });
        }
        if (Strings.isNullOrEmpty(sourceId)) {
            return;
        }
        Iterator<ModifyRecord> iterator = modifyRecordList.iterator();
        while (iterator.hasNext()) {
            ModifyRecord modifyRecord = iterator.next();
            ObjectInfo sourceObjectInfo = modifyRecord.getSourceObjectInfo();
            if (Objects.nonNull(sourceObjectInfo)) {
                List<ObjectInfo.ObjectData> objectDataList = CollectionUtils.nullToEmpty(sourceObjectInfo.getObjectDatas()).stream()
                        .filter(data -> sourceId.equals(data.getMasterId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.empty(objectDataList)) {
                    iterator.remove();
                } else {
                    sourceObjectInfo.setObjectDatas(objectDataList);
                }
            }
        }
    }

    protected void filterFieldPermission(List<ModifyRecord> modifyRecordList, Set<String> unauthorizedFields) {
        if (CollectionUtils.empty(modifyRecordList) || CollectionUtils.empty(unauthorizedFields)) {
            return;
        }
        modifyRecordList.forEach(x -> {
            List<LogInfo.DiffObjectData> objectData = CollectionUtils.nullToEmpty(x.getObjectData()).stream()
                    .filter(y -> !unauthorizedFields.contains(y.getFieldApiName())).collect(Collectors.toList());
            x.setObjectData(objectData);
        });
    }

    protected void fillEmployeeAndDepartmentFieldValue(List<LogRecord> logRecordList, IObjectDescribe describe,
                                                       String masterDataId, String masterApiName) {
        List<LogRecord> modifyRecords = CollectionUtils.nullToEmpty(logRecordList).stream()
                .filter(Objects::nonNull)
                .filter(x -> ActionType.Modify.getId().equals(x.getOperationType()))
                .filter(x -> CollectionUtils.notEmpty(x.getObjectData()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(modifyRecords)) {
            return;
        }
        List<IObjectData> oldDataList = Lists.newArrayList();
        List<IObjectData> newDataList = Lists.newArrayList();
        Set<String> fieldNameList = Sets.newHashSet();
        modifyRecords.forEach(x -> {
            IObjectData oldData = new ObjectData();
            IObjectData newData = new ObjectData();
            x.getObjectData().forEach(y -> {
                oldData.set(y.getFieldApiName(), y.getOldValue().get(y.getFieldApiName()));
                newData.set(y.getFieldApiName(), y.getValue().get(y.getFieldApiName()));
                fieldNameList.add(y.getFieldApiName());
            });
            oldDataList.add(oldData);
            newDataList.add(newData);
        });

        List<IFieldDescribe> employeeFields = fieldNameList.stream()
                .filter(describe::containsField)
                .map(describe::getFieldDescribe)
                .filter(x -> FieldDescribeExt.of(x).isOutEmployee()
                        || x.getType().equals(IFieldType.EMPLOYEE)
                        || x.getType().equals(IFieldType.EMPLOYEE_MANY))
                .collect(Collectors.toList());

        List<IFieldDescribe> departmentFields = fieldNameList.stream()
                .filter(describe::containsField)
                .map(describe::getFieldDescribe)
                .filter(x -> FieldDescribeExt.of(x).isOutEmployee()
                        || x.getType().equals(IFieldType.DEPARTMENT)
                        || x.getType().equals(IFieldType.DEPARTMENT_MANY)
                        || x.getType().equals(IFieldType.OUT_DEPARTMENT))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(employeeFields) && CollectionUtils.empty(departmentFields)) {
            return;
        }
        List<IObjectData> dataList = Lists.newArrayList();
        dataList.addAll(oldDataList);
        dataList.addAll(newDataList);
        IObjectData dbData = serviceFacade.findObjectDataIncludeDeleted(controllerContext.getUser(), masterDataId, masterApiName);
        dataList.forEach(x -> {
            x.setTenantId(controllerContext.getTenantId());
            x.setDescribeApiName(describe.getApiName());
            x.setOwner(dbData.getOwner());
        });
        if (!CollectionUtils.empty(employeeFields)) {
            serviceFacade.fillUserInfo(describe, dataList, controllerContext.getUser());
        }
        if (!CollectionUtils.empty(departmentFields)) {
            serviceFacade.fillDepartmentInfo(describe, dataList, controllerContext.getUser());
        }
        for (int i = 0; i < modifyRecords.size(); i++) {
            IObjectData oldData = oldDataList.get(i);
            IObjectData newData = newDataList.get(i);
            modifyRecords.get(i).getObjectData().forEach(x -> {
                if (x.getRenderType().equals(IFieldType.OUT_EMPLOYEE) ||
                        x.getRenderType().equals(IFieldType.EMPLOYEE) ||
                        x.getRenderType().equals(IFieldType.EMPLOYEE_MANY) ||
                        x.getRenderType().equals(IFieldType.DEPARTMENT) ||
                        x.getRenderType().equals(IFieldType.DEPARTMENT_MANY) ||
                        x.getRenderType().equals(IFieldType.OUT_DEPARTMENT)) {
                    String employeeApiName = FieldDescribeExt.getEmployeeNameByFieldName(x.getFieldApiName());
                    x.getOldValue().put(x.getFieldApiName(), oldData.get(x.getFieldApiName()));
                    x.getOldValue().put(employeeApiName, oldData.get(employeeApiName));
                    x.getValue().put(x.getFieldApiName(), newData.get(x.getFieldApiName()));
                    x.getValue().put(employeeApiName, newData.get(employeeApiName));
                }
            });
        }
    }

    protected void fillDataVisibilityRangeFieldValue(List<LogRecord> logRecordList, IObjectDescribe describe,
                                                     String masterDataId, String masterApiName) {
        if (!describe.isPublicObject()) {
            return;
        }
        List<LogRecord> modifyRecords = CollectionUtils.nullToEmpty(logRecordList).stream()
                .filter(Objects::nonNull)
                .filter(x -> ActionType.Modify.getId().equals(x.getOperationType()))
                .filter(x -> CollectionUtils.notEmpty(x.getObjectData()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(modifyRecords)) {
            return;
        }
        List<IObjectData> oldDataList = Lists.newArrayList();
        List<IObjectData> newDataList = Lists.newArrayList();
        Set<String> fieldNameList = Sets.newHashSet();
        modifyRecords.forEach(x -> {
            IObjectData oldData = new ObjectData();
            IObjectData newData = new ObjectData();
            x.getObjectData().forEach(y -> {
                oldData.set(y.getFieldApiName(), y.getOldValue().get(y.getFieldApiName()));
                newData.set(y.getFieldApiName(), y.getValue().get(y.getFieldApiName()));
                fieldNameList.add(y.getFieldApiName());
            });
            oldDataList.add(oldData);
            newDataList.add(newData);
        });
        if (!fieldNameList.contains(IObjectData.DOWNSTREAM_TENANT_ID)) {
            return;
        }
        List<IObjectData> dataList = Lists.newArrayList();
        dataList.addAll(oldDataList);
        dataList.addAll(newDataList);
        IObjectData dbData = serviceFacade.findObjectDataIncludeDeleted(controllerContext.getUser(), masterDataId, masterApiName);
        dataList.forEach(x -> {
            x.setTenantId(controllerContext.getTenantId());
            x.setDescribeApiName(describe.getApiName());
            x.setOwner(dbData.getOwner());
        });
        serviceFacade.fillDataVisibilityRange(controllerContext.getUser(), describe, dataList);
        for (int i = 0; i < modifyRecords.size(); i++) {
            IObjectData oldData = oldDataList.get(i);
            IObjectData newData = newDataList.get(i);
            modifyRecords.get(i).getObjectData().forEach(x -> {
                if (IObjectData.DOWNSTREAM_TENANT_ID.equals(x.getFieldApiName())) {
                    String fieldKey = x.getFieldApiName() + FieldDescribeExt.EMPLOYEE_NAME_SUFFIX;
                    x.getOldValue().put(x.getFieldApiName(), oldData.get(x.getFieldApiName()));
                    x.getOldValue().put(fieldKey, oldData.get(fieldKey));
                    x.getValue().put(x.getFieldApiName(), newData.get(x.getFieldApiName()));
                    x.getValue().put(fieldKey, newData.get(fieldKey));
                }
            });
        }
    }

    protected void handleTeamMember(User user, List<LogRecord> logRecordList) {
        if (!infraServiceFacade.findOptionalFeaturesSwitch(user.getTenantId(), objectDescribe).getIsRelatedTeamEnabled()) {
            List<LogRecord> changeOwnerModifyRecords = CollectionUtils.nullToEmpty(logRecordList).stream()
                    .filter(Objects::nonNull)
                    .filter(x -> ActionType.ChangeOwner.getId().equals(x.getOperationType()) || ActionType.CHANGE_PARTNER_OWNER.getId().equals(x.getOperationType()))
                    .filter(x -> CollectionUtils.notEmpty(x.getMsgList()))
                    .collect(Collectors.toList());
            for (LogRecord modifyRecord : changeOwnerModifyRecords) {
                for (TeamMemberInfo.Msg msg : modifyRecord.getMsgList()) {
                    msg.setMsgMap(new TeamMemberInfo.MsgMap());
                }
            }
        }
        // 相关团队相关的修改记录
        List<LogRecord> modifyRecords = CollectionUtils.nullToEmpty(logRecordList).stream()
                .filter(Objects::nonNull)
                .filter(x -> TEAM_MEMBER_ACTION_LIST.contains(x.getOperationType()))
                .filter(x -> CollectionUtils.notEmpty(x.getMsgList()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(modifyRecords)) {
            return;
        }
        Set<String> userIds = Sets.newHashSet();
        Set<String> groupIds = Sets.newHashSet();
        Set<String> roleCodes = Sets.newHashSet();
        Set<String> departmentIds = Sets.newHashSet();
        List<TeamMemberInfo.Msg> msgList = Lists.newArrayList();
        for (LogRecord modifyRecord : modifyRecords) {
            for (TeamMemberInfo.Msg msg : modifyRecord.getMsgList()) {
                if (Objects.isNull(msg.getMsgMap())) {
                    continue;
                }
                if (msg.getMsgMap().matchMemberType(TeamMember.MemberType.DEPARTMENT)) {
                    Set<String> ids = msg.getMember().allIds();
                    departmentIds.addAll(ids);
                    msgList.add(msg);
                }
                if (msg.getMsgMap().matchMemberType(TeamMember.MemberType.ROLE)) {
                    Set<String> ids = msg.getMember().allIds();
                    roleCodes.addAll(ids);
                    msgList.add(msg);
                }
                if (msg.getMsgMap().matchMemberType(TeamMember.MemberType.EMPLOYEE)) {
                    Set<String> ids = msg.getMember().allIds();
                    userIds.addAll(ids);
                    msgList.add(msg);
                }
                if (msg.getMsgMap().matchMemberType(TeamMember.MemberType.GROUP)) {
                    Set<String> ids = msg.getMember().allIds();
                    groupIds.addAll(ids);
                    msgList.add(msg);
                }
            }
        }
        Map<String, String> deptNameMap = Maps.newHashMap();
        Map<String, String> roleInfoMap = Maps.newHashMap();
        Map<String, String> userInfoMap = Maps.newHashMap();
        Map<String, String> groupInfoMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(departmentIds)) {
            List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = serviceFacade.getDeptInfoNameByIdsAndStatus(user.getTenantId(),
                    user.getUserId(), Lists.newArrayList(departmentIds), QueryDeptInfoByDeptIds.DeptStatusEnum.ALL);
            deptNameMap = deptInfos.stream().collect(Collectors.toMap(QueryDeptInfoByDeptIds.DeptInfo::getDeptId, QueryDeptInfoByDeptIds.DeptInfo::getDeptName, (x, y) -> x));
        }
        if (CollectionUtils.notEmpty(roleCodes)) {
            List<GetUserRoleInfo.RoleInfo> roleInfos = serviceFacade.queryRoleInfoByRoleCode(user, Lists.newArrayList(roleCodes));
            roleInfoMap = roleInfos.stream().collect(Collectors.toMap(GetUserRoleInfo.RoleInfo::getRoleCode, GetUserRoleInfo.RoleInfo::getRoleName, (x, y) -> x));
        }
        if (CollectionUtils.notEmpty(userIds)) {
            List<UserInfo> roleInfos = serviceFacade.getUserNameByIds(user.getTenantId(), user.getUserId(), Lists.newArrayList(userIds));
            userInfoMap = roleInfos.stream().collect(Collectors.toMap(UserInfo::getId, UserInfo::getName, (x, y) -> x));
        }
        if (CollectionUtils.notEmpty(groupIds)) {
            List<QueryGroupByIds.UserGroupInfo> roleInfos = serviceFacade.getGroupInfoByIds(user.getTenantId(), user.getUserId(), Lists.newArrayList(groupIds));
            groupInfoMap = roleInfos.stream().collect(Collectors.toMap(QueryGroupByIds.UserGroupInfo::getId, QueryGroupByIds.UserGroupInfo::getName, (x, y) -> x));
        }


        for (TeamMemberInfo.Msg msg : msgList) {
            TeamMemberInfo.Member member = msg.getMember();
            String name = "";
            String oldName = "";
            if (msg.getMsgMap().matchMemberType(TeamMember.MemberType.DEPARTMENT)) {
                name = deptNameMap.get(member.getId());
                oldName = deptNameMap.get(member.getOldId());
            } else if (msg.getMsgMap().matchMemberType(TeamMember.MemberType.ROLE)) {
                name = roleInfoMap.get(member.getId());
                oldName = roleInfoMap.get(member.getOldId());
            } else if (msg.getMsgMap().matchMemberType(TeamMember.MemberType.EMPLOYEE)) {
                name = userInfoMap.get(member.getId());
                oldName = userInfoMap.get(member.getOldId());
            } else if (msg.getMsgMap().matchMemberType(TeamMember.MemberType.GROUP)) {
                name = groupInfoMap.get(member.getId());
                oldName = groupInfoMap.get(member.getOldId());
            }
            if (StringUtils.isNotBlank(name)) {
                member.setName(name);
            }
            if (StringUtils.isNotBlank(oldName)) {
                member.setOldName(oldName);
            }
        }
    }

    protected void fillDimensionFieldValue(List<LogRecord> logRecordList, IObjectDescribe describe, String masterDataId, String masterApiName) {
        List<LogRecord> dimensionRecords = CollectionUtils.nullToEmpty(logRecordList).stream()
                .filter(Objects::nonNull)
                .filter(x -> ActionType.Modify.getId().equals(x.getOperationType()))
                .filter(x -> CollectionUtils.notEmpty(x.getObjectData()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(dimensionRecords)) {
            return;
        }
        List<IObjectData> oldDataList = Lists.newArrayList();
        List<IObjectData> newDataList = Lists.newArrayList();
        Set<String> fieldNameList = Sets.newHashSet();
        dimensionRecords.forEach(x -> {
            IObjectData oldData = new ObjectData();
            IObjectData newData = new ObjectData();
            x.getObjectData().forEach(y -> {
                oldData.set(y.getFieldApiName(), y.getOldValue().get(y.getFieldApiName()));
                newData.set(y.getFieldApiName(), y.getValue().get(y.getFieldApiName()));
                fieldNameList.add(y.getFieldApiName());
            });
            oldDataList.add(oldData);
            newDataList.add(newData);
        });

        List<IFieldDescribe> dimensionFields = fieldNameList.stream()
                .filter(describe::containsField)
                .map(describe::getFieldDescribe)
                .filter(x -> FieldDescribeExt.of(x).isDimension())
                .collect(Collectors.toList());
        if (CollectionUtils.empty(dimensionFields)) {
            return;
        }
        List<IObjectData> dataList = Lists.newArrayList();
        dataList.addAll(oldDataList);
        dataList.addAll(newDataList);
        IObjectData dbData = serviceFacade.findObjectDataIncludeDeleted(controllerContext.getUser(), masterDataId, masterApiName);
        dataList.forEach(x -> {
            x.setTenantId(controllerContext.getTenantId());
            x.setDescribeApiName(describe.getApiName());
            x.setOwner(dbData.getOwner());
        });
        serviceFacade.fillDimensionFieldValue(controllerContext.getUser(), describe, dataList);
        for (int i = 0; i < dimensionRecords.size(); i++) {
            IObjectData oldData = oldDataList.get(i);
            IObjectData newData = newDataList.get(i);
            dimensionRecords.get(i).getObjectData().forEach(x -> {
                if (x.getRenderType().equals(IFieldType.DIMENSION)) {
                    String dimensionApiName = FieldDescribeExt.getLookupNameByFieldName(x.getFieldApiName());
                    x.getOldValue().put(x.getFieldApiName(), oldData.get(x.getFieldApiName()));
                    x.getOldValue().put(dimensionApiName, oldData.get(dimensionApiName));
                    x.getValue().put(x.getFieldApiName(), newData.get(x.getFieldApiName()));
                    x.getValue().put(dimensionApiName, newData.get(dimensionApiName));
                }
            });
        }
    }

    protected void fillMaskFieldValue(List<LogRecord> logRecordList, IObjectDescribe describe, String masterDataId, String masterApiName) {
        if (!needFillMaskFieldValue()) {
            return;
        }
        List<LogRecord> modifyRecords = CollectionUtils.nullToEmpty(logRecordList).stream()
                .filter(Objects::nonNull)
                .filter(x -> ActionType.Modify.getId().equals(x.getOperationType()))
                .filter(x -> CollectionUtils.notEmpty(x.getObjectData()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(modifyRecords)) {
            return;
        }
        List<IObjectData> oldDataList = Lists.newArrayList();
        List<IObjectData> newDataList = Lists.newArrayList();
        Set<String> fieldNameList = Sets.newHashSet();
        modifyRecords.forEach(x -> {
            IObjectData oldData = new ObjectData();
            IObjectData newData = new ObjectData();
            x.getObjectData().forEach(y -> {
                oldData.set(y.getFieldApiName(), y.getOldValue().get(y.getFieldApiName()));
                newData.set(y.getFieldApiName(), y.getValue().get(y.getFieldApiName()));
                fieldNameList.add(y.getFieldApiName());
            });
            oldDataList.add(oldData);
            newDataList.add(newData);
        });

        List<IFieldDescribe> maskFields = fieldNameList.stream()
                .filter(x -> describe.containsField(x))
                .map(x -> describe.getFieldDescribe(x))
                .filter(x -> FieldDescribeExt.of(x).isShowMask())
                .collect(Collectors.toList());
        if (CollectionUtils.empty(maskFields)) {
            return;
        }
        List<IObjectData> dataList = Lists.newArrayList();
        dataList.addAll(oldDataList);
        dataList.addAll(newDataList);
        IObjectData dbData = serviceFacade.findObjectDataIncludeDeleted(controllerContext.getUser(), masterDataId, masterApiName);
        dataList.forEach(x -> {
            x.setTenantId(controllerContext.getTenantId());
            x.setDescribeApiName(describe.getApiName());
            x.setOwner(dbData.getOwner());
            x.setOutOwner(dbData.getOutOwner());
        });

        serviceFacade.fillMaskFieldValue(controllerContext.getUser(), dataList, maskFields, false);
        for (int i = 0; i < modifyRecords.size(); i++) {
            IObjectData oldData = oldDataList.get(i);
            IObjectData newData = newDataList.get(i);
            modifyRecords.get(i).getObjectData().forEach(x -> {
                String showApiName = FieldDescribeExt.getShowFieldName(x.getFieldApiName());
                if (ObjectDataExt.of(oldData).containsField(showApiName)) {
                    x.getOldValue().put(x.getFieldApiName(), oldData.get(showApiName));
                    x.getOldValue().put(showApiName, oldData.get(showApiName));
                    x.getValue().put(x.getFieldApiName(), newData.get(showApiName));
                    x.getValue().put(showApiName, newData.get(showApiName));
                }
            });
        }
    }

    protected boolean needFillMaskFieldValue() {
        return RequestUtil.isCepRequest();
    }

    /**
     * 本方法只支持图片字段，处理附件和图片请使用
     *
     * @see #dealNPathSign(List, String...)
     */
    @Deprecated
    protected void dealImgSign(List<LogRecord> logRecordList) {
        dealNPathSign(logRecordList, IFieldType.IMAGE);
    }

    protected void dealNPathSign(List<LogRecord> logRecordList, String... fieldTypes) {
        IActionContext ctx = ActionContextExt.of(controllerContext.getUser()).getContext();
        Set<String> signedUrlTypes = AppFrameworkConfig.signedUrlFieldTypes(ctx.getEnterpriseId());
        Set<String> enabledTypes = Arrays.stream(fieldTypes)
                .filter(signedUrlTypes::contains)
                .collect(Collectors.toSet());
        if (CollectionUtils.empty(logRecordList) || enabledTypes.isEmpty()) {
            return;
        }
        for (LogRecord logRecord : logRecordList) {
            List<LogInfo.DiffObjectData> objectData = logRecord.getObjectData();
            if (CollectionUtils.empty(objectData)) {
                continue;
            }
            objectData.forEach(o -> {
                if (!enabledTypes.contains(o.getRenderType())) {
                    return;
                }
                if (ObjectUtils.isEmpty(o)) {
                    return;
                }

                @SuppressWarnings("unchecked")
                List<Map<String, Object>> o1 = (List<Map<String, Object>>) Optional.ofNullable(o.getOldValue())
                        .map(map -> map.get(o.getFieldApiName()))
                        .filter(List.class::isInstance).orElse(null);
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> o2 = (List<Map<String, Object>>) Optional.ofNullable(o.getValue())
                        .map(map -> map.get(o.getFieldApiName()))
                        .filter(List.class::isInstance).orElse(null);

                List<Map<String, Object>> list = CollectionUtils.concatAndNotContainEmpty(o1, o2);

                AuthModel authModel = infraServiceFacade.getFileStoreService().getAuthModel(o.getRenderType());

                infraServiceFacade.getFileStoreService().generateNPathSignedUrl(ctx, authModel, list);
            });
        }
    }

    protected final void dealMultiLangData(IObjectDescribe objectDescribe, List<LogRecord> logRecordList) {
        Set<String> enableMultiLangField = ObjectDescribeExt.of(objectDescribe).getEnableMultiLangField().stream()
                .map(IFieldDescribe::getApiName).collect(Collectors.toSet());
        if (CollectionUtils.empty(enableMultiLangField) || CollectionUtils.empty(logRecordList)) {
            return;
        }
        for (LogRecord logRecord : logRecordList) {
            List<LogInfo.DiffObjectData> objectData = logRecord.getObjectData();
            if (CollectionUtils.empty(objectData)) {
                continue;
            }
            Iterator<LogInfo.DiffObjectData> iterator = objectData.iterator();
            while (iterator.hasNext()) {
                LogInfo.DiffObjectData diffObjectData = iterator.next();
                String fieldApiName = diffObjectData.getFieldApiName();
                if (!enableMultiLangField.contains(diffObjectData.getFieldApiName())) {
                    continue;
                }
                removeEmptyMultiLang(diffObjectData.getOldValue(), fieldApiName);
                removeEmptyMultiLang(diffObjectData.getValue(), fieldApiName);
                if (CollectionUtils.empty(diffObjectData.getOldValue()) && CollectionUtils.empty(diffObjectData.getValue())) {
                    iterator.remove();
                }
            }
        }
    }

    private void removeEmptyMultiLang(Map<String, Object> recordValueMap, String fieldApiName) {
        Object multiLangValue = recordValueMap.get(FieldDescribeExt.getMultiLangExtraFieldName(fieldApiName));
        if (Objects.isNull(multiLangValue)) {
            return;
        }
        if (multiLangValue instanceof Map) {
            Map<String, Object> valueMap = (Map) multiLangValue;
            valueMap.entrySet().removeIf(x -> Objects.isNull(x.getValue())
                    || (x.getValue() instanceof String && StringUtils.isBlank((String) x.getValue())));
            if (CollectionUtils.empty(valueMap)) {
                recordValueMap.remove(FieldDescribeExt.getMultiLangExtraFieldName(fieldApiName));
            }
        }
    }

}
