package com.facishare.paas.appframework.core.predef.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.facishare.crm.privilege.service.RoleService;
import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import com.facishare.paas.appframework.core.predef.service.dto.recordType.*;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.I18nSettingService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.RecordTypeLogicServiceImpl;
import com.facishare.paas.appframework.metadata.dto.RecordTypeMatchInfo;
import com.facishare.paas.appframework.metadata.dto.RecordTypeResult;
import com.facishare.paas.appframework.metadata.dto.auth.RecordTypeRoleViewPojo;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.fieldextra.FieldBackgroundExtraLogicService;
import com.facishare.paas.appframework.metadata.layout.I18nInfo;
import com.facishare.paas.appframework.metadata.repository.model.MtFieldBackgroundExtra;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectRelationMatch;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectRelationMatch;
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe;
import com.facishare.paas.metadata.impl.describe.RecordTypeOption;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.log4j.Log4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 对象描述服务接口
 * <p>
 * Created by liyiguang on 2017/6/17.
 */
@Log4j
@ServiceModule("record_type")
@Component
public class ObjectRecordTypeService {

    @Autowired
    private RecordTypeLogicServiceImpl recordTypeService;

    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private FieldBackgroundExtraLogicService fieldBackgroundExtraLogicService;

    @Resource
    private I18nSettingService i18nSettingService;


    /**
     * 创建对象业务类型
     *
     * @param arg
     * @return
     */
    @ServiceMethod("create")
    public CreateRecordType.Result createRecordType(CreateRecordType.Arg arg, ServiceContext context) {
        CreateRecordType.Result result = new CreateRecordType.Result();
        if (StringUtils.isBlank(arg.getRecord_type()) || StringUtils.isBlank(arg.getDescribeApiName())) {
            return result;
        }
        RecordTypeRoleViewPojo pojo = JSON.parseObject(arg.getRecord_type(), RecordTypeRoleViewPojo.class);
        RecordTypeResult recordTypeResult = recordTypeService.createRecordType(context.getTenantId(), arg.getDescribeApiName(), pojo, getUser(context));
        List<I18nInfo> i18nInfoList = arg.getI18nInfoList();
        i18nSettingService.synTranslateToMeta(context.getTenantId(), arg.getDescribeApiName(), i18nInfoList);
        boolean success = recordTypeResult.isSuccess();
        result.setSuccess(success);
        if (success) {
            upsertOptionRemark(context.getUser(), arg.getDescribeApiName(), pojo.getApi_name(), arg.getRemark());
        }
        return result;
    }

    private void upsertOptionRemark(User user, String describeApiName, String recordType, String remark) {
        Map<String, Object> optionRemarkMap = MtFieldBackgroundExtra.buildOptionRemarkMap(recordType, StringUtils.defaultString(remark));
        fieldBackgroundExtraLogicService.upsertOptionRemark(user, describeApiName, IFieldType.RECORD_TYPE, Lists.newArrayList(optionRemarkMap));
    }

    @ServiceMethod("update")
    public UpdateRecordType.Result updateRecordType(UpdateRecordType.Arg arg, ServiceContext context) {
        RecordTypeResult recordTypeResult = recordTypeService.updateRecordType(context.getUser(), arg.getDescribeApiName(), arg.getRecord_type());

        List<I18nInfo> i18nInfoList = arg.getI18nInfoList();
        i18nSettingService.synTranslateToMeta(context.getTenantId(), arg.getDescribeApiName(), i18nInfoList);

        UpdateRecordType.Result result = new UpdateRecordType.Result();
        result.setSuccess(recordTypeResult.isSuccess());
        boolean success = recordTypeResult.isSuccess();
        if (success) {
            IRecordTypeOption option = new RecordTypeOption();
            option.fromJsonString(arg.getRecord_type());
            upsertOptionRemark(context.getUser(), arg.getDescribeApiName(), option.getApiName(), arg.getRemark());
        }
        return result;
    }

    @ServiceMethod("delete")
    public DeleteRecordType.Result deleteRecordType(DeleteRecordType.Arg arg, ServiceContext context) {
        DeleteRecordType.Result result = new DeleteRecordType.Result();
        RecordTypeResult recordTypeResult = recordTypeService.deleteRecordType(context.getUser(), arg.getDescribeApiName(), arg.getRecordApiName());
        result.setSuccess(recordTypeResult.isSuccess());
        if (!recordTypeResult.isSuccess()) {
            throw new MetaDataBusinessException(recordTypeResult.getFailMessage());
        }
        return result;
    }

    @ServiceMethod("enableRecordType")
    public EnableRecordType.Result enableRecordType(EnableRecordType.Arg arg, ServiceContext context) {
        EnableRecordType.Result result = new EnableRecordType.Result();
        RecordTypeResult recordTypeResult = recordTypeService.enableRecordType(context.getUser(), arg.getDescribeApiName(), arg.getRecordApiName());
        result.setSuccess(recordTypeResult.isSuccess());
        if (!recordTypeResult.isSuccess()) {
            throw new MetaDataBusinessException(recordTypeResult.getFailMessage());
        }
        return result;
    }

    @ServiceMethod("disableRecordType")
    public DisableRecordType.Result disableRecordType(DisableRecordType.Arg arg, ServiceContext context) {
        DisableRecordType.Result result = new DisableRecordType.Result();
        RecordTypeResult recordTypeResult = recordTypeService.disableRecordType(context.getUser(), arg.getDescribeApiName(), arg.getRecordApiName());
        result.setSuccess(recordTypeResult.isSuccess());
        if (!recordTypeResult.isSuccess()) {
            throw new MetaDataBusinessException(recordTypeResult.getFailMessage());
        }

        //如果角色没有可用类型，则赋予预置类型
        RecordTypeResult roleAndRecordType = recordTypeService.findRoleAndRecordType(context.getTenantId(), arg.getDescribeApiName(), context.getUser());
        if (CollectionUtils.notEmpty(roleAndRecordType.getRole_list())) {
            List roleList = roleAndRecordType.getRole_list();
            boolean isNeedSave = false;
            for (Object a : roleList) {
                Map<String, Object> doc = (Map) a;
                Object records = doc.get("records");
                if (!Objects.isNull(records) && records instanceof List) {
                    List<String> typeList = (List) records;
                    List<String> validTypeList = getValidTypeList(context.getTenantId(), arg.getDescribeApiName(), typeList);
                    if (Objects.equals(validTypeList.size(), 1) && Objects.equals(validTypeList.get(0), arg.getRecordApiName())) {
                        typeList.add(MultiRecordType.RECORD_TYPE_DEFAULT);
                        isNeedSave = true;
                    }
                }
            }

            if (isNeedSave) {
                recordTypeService.assignRecord(context.getTenantId(), arg.getDescribeApiName(), JSON.toJSONString(roleList), getUser(context));
            }
        }
        return result;
    }

    @ServiceMethod("sortRecordTypeOption")
    public SortRecordTypeOption.Result sortRecordTypeOption(SortRecordTypeOption.Arg arg, ServiceContext context) {
        recordTypeService.sortRecordTypeOption(context.getUser(), arg.getDescribeApiName(), arg.getSortedRecordTypeOption());
        return new SortRecordTypeOption.Result();
    }

    private List<String> getValidTypeList(String tenantId, String describeApiName, List<String> toCheckList) {
        IObjectDescribe describe = describeLogicService.findObject(tenantId, describeApiName);
        Optional<RecordTypeFieldDescribe> recordTypeField = ObjectDescribeExt.of(describe).getRecordTypeField();
        if (!recordTypeField.isPresent() || CollectionUtils.empty(toCheckList)) {
            return Lists.newArrayList();
        }

        List<IRecordTypeOption> recordTypeOptions = recordTypeField.get().getRecordTypeOptions();
        if (CollectionUtils.empty(recordTypeOptions)) {
            return Lists.newArrayList();
        }

        List<String> resultList = Lists.newArrayList();
        toCheckList.forEach(a -> {
            if (recordTypeField.get().isOptionExist(a)) {
                resultList.add(a);
            }
        });

        return resultList;
    }

    @ServiceMethod("findRoleInfoList")
    public FindRoleInfoList.Result findRoleInfoList(FindRoleInfoList.Arg arg, ServiceContext context) {
        FindRoleInfoList.Result result = new FindRoleInfoList.Result();
        RecordTypeResult recordTypeResult = recordTypeService.findRoleInfoList(getUser(context));
        result.setRole_list(recordTypeResult.getRole_list());
        return result;
    }

    @ServiceMethod("findRecordInfo")
    public FindRecordInfo.Result findRecordInfo(FindRecordInfo.Arg arg, ServiceContext context) {
        FindRecordInfo.Result result = new FindRecordInfo.Result();
        User user = context.getUser();
        boolean defaultLang = arg.isNoNeedReplaceI18n()
                && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FIELD_NAME_RECORD_TYPE_FIELD_PACKET,
                user.getTenantId());
        RecordTypeResult recordTypeResult = recordTypeService.findRecordInfo(user, arg.getDescribeApiName(),
                arg.getRecordApiName(), defaultLang);
        result.setRecordTypeOption(recordTypeResult.getRecordTypeOption());
        if (arg.isIncludeRemark()) {
            List<Map<String, Object>> optionRemarksByField = fieldBackgroundExtraLogicService.findOptionRemarksByField(user, arg.getDescribeApiName(), IFieldType.RECORD_TYPE);
            optionRemarksByField.stream()
                    .filter(optionRemark -> StringUtils.equals((String) optionRemark.get(MtFieldBackgroundExtra.OPTION_VALUE), arg.getRecordApiName()))
                    .findFirst()
                    .ifPresent(x -> result.setRemark((String) x.get(MtFieldBackgroundExtra.REMARK)));
        }
        List<I18nInfo> i18nInfos = i18nSettingService.queryTranslation(context.getTenantId(),
                arg.getDescribeApiName(),
                Lists.newArrayList(I18nInfo.builder()
                        .apiName(arg.getRecordApiName())
                        .type(I18nInfo.Type.RECORD_TYPE_LABEL.getFrontType())
                        .build()));
        result.setI18nInfoList(i18nInfos);
        return result;
    }

    @ServiceMethod("findRoleAndLayout")
    public FindRoleAndLayout.Result findRoleAndLayout(FindRoleAndLayout.Arg arg, ServiceContext context) {
        FindRoleAndLayout.Result result = new FindRoleAndLayout.Result();
        RecordTypeResult recordTypeResult = recordTypeService.findRoleAndLayout(context.getTenantId(), arg.getDescribeApiName(), getUser(context));
        result.setRole_list(recordTypeResult.getRole_list());
        result.setLayout_list(recordTypeResult.getLayout_list());
        return result;
    }

    @ServiceMethod("findRoleAndRecordType")
    public FindRoleAndRecordType.Result findRoleAndRecordType(FindRoleAndRecordType.Arg arg, ServiceContext context) {
        FindRoleAndRecordType.Result result = new FindRoleAndRecordType.Result();
        RecordTypeResult recordTypeResult = recordTypeService.findRoleAndRecordType(context.getTenantId(), arg.getDescribeApiName(),
                getUser(context), arg.getSourceInfo());
        result.setRole_list(recordTypeResult.getRole_list());
        result.setRecord_list(recordTypeResult.getRecord_list());
        return result;
    }

    @ServiceMethod("assignRecord")
    public AssignRecord.Result assignRecord(AssignRecord.Arg arg, ServiceContext context) {
        AssignRecord.Result result = new AssignRecord.Result();
        RecordTypeResult recordTypeResult = recordTypeService.assignRecord(context.getTenantId(), arg.getDescribeApiName(),
                arg.getRole_list(), getUser(context), arg.getSourceInfo());
        result.setSuccess(recordTypeResult.isSuccess());
        return result;
    }

    @ServiceMethod("findRecordTypeList")
    public FindRecordTypeList.Result findRecordTypeList(FindRecordTypeList.Arg arg, ServiceContext context) {
        FindRecordTypeList.Result result = new FindRecordTypeList.Result();
        RecordTypeResult recordTypeResult = recordTypeService.findRecordTypeList(context.getTenantId(), arg.getDescribeApiName(), arg.getIs_only_active());
        result.setRecord_list(recordTypeResult.getRecord_list());
        if (arg.isIncludeRemark()) {
            result.setOptionRemark(fieldBackgroundExtraLogicService.findOptionRemarksByField(context.getUser(), arg.getDescribeApiName(), IFieldType.RECORD_TYPE));
        }
        return result;
    }

    @ServiceMethod("findLayoutByRecordType")
    public FindLayoutByRecordType.Result findLayoutByRecordType(FindLayoutByRecordType.Arg arg, ServiceContext context) {
        FindLayoutByRecordType.Result result = new FindLayoutByRecordType.Result();
        IObjectDescribe objectDescribe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        RecordTypeResult recordTypeResult = recordTypeService.findLayoutByRecordType(objectDescribe, arg.getRecordApiName(), getUser(context), arg.getLayoutType());
        result.setLayout(recordTypeResult.getLayout());
        result.setObjectDescribe(recordTypeResult.getObjectDescribe());
        return result;
    }

    @ServiceMethod("findAssignedLayout")
    public FindAssignedLayout.Result findAssignedLayout(FindAssignedLayout.Arg arg, ServiceContext context) {
        FindAssignedLayout.Result result = new FindAssignedLayout.Result();
        RecordTypeResult recordTypeResult = recordTypeService.findAssignedLayout(getUser(context), arg.getDescribeApiName(),
                arg.getWhatDescribeApiName(), arg.getLayoutType(), arg.getSourceInfo(), arg.getAppId());
        result.setRole_list(recordTypeResult.getRole_list());
        result.setLayout_list(recordTypeResult.getLayout_list());
        result.setRecord_list(recordTypeResult.getRecord_list());
        result.setLayoutManageGroup(ManageGroupDTO.of(recordTypeResult.getLayoutManageGroup()));
        return result;
    }

    @ServiceMethod("saveLayoutAssign")
    public SaveLayoutAssign.Result saveLayoutAssign(SaveLayoutAssign.Arg arg, ServiceContext context) {
        SaveLayoutAssign.Result result = new SaveLayoutAssign.Result();
        RecordTypeResult recordTypeResult = recordTypeService.saveLayoutAssign(arg.getLayoutType(), arg.getDescribeApiName(),
                arg.getWhatDescribeApiName(), arg.getRole_list(), getUser(context), arg.getSourceInfo(), arg.getAppId());
        result.setSuccess(recordTypeResult.isSuccess());
        return result;
    }

    @ServiceMethod("findValidRecordTypeList")
    public FindValidRecordTypeList.Result findValidRecordTypeList(FindValidRecordTypeList.Arg arg, ServiceContext context) {
        FindValidRecordTypeList.Result result = new FindValidRecordTypeList.Result();
        if (arg.isForImport()) {
            if (ObjectDescribeExt.UnsupportedImportRecordTypeDescribes.contains(arg.getDescribeApiName())) {
                return result;
            }
        }
        List<IRecordTypeOption> recordTypeList = recordTypeService.findValidRecordTypeList(arg.getDescribeApiName(), context.getUser());
        List<Map> dataList = recordTypeList.stream()
                .map(recordType -> (((DocumentBasedBean) recordType).getContainerDocument()))
                .collect(Collectors.toList());
        result.setRecord_list(dataList);
        return result;
    }

    @ServiceMethod("bulkFindRecordTypeList")
    public BulkFindRecordTypeList.Result bulkFindRecordTypeList(BulkFindRecordTypeList.Arg arg, ServiceContext context) {
        BulkFindRecordTypeList.Result result = new BulkFindRecordTypeList.Result();
        List<BulkFindRecordTypeList.Item> recordTypeMap = Lists.newArrayList();
        List<IObjectDescribe> describeList = Lists.newArrayList();
        if (CollectionUtils.empty(arg.getDescribeApiNameList())) {
            describeList = describeLogicService.findDescribeListWithFields(context.getUser());
        } else {
            Map<String, IObjectDescribe> map = describeLogicService.findObjects(context.getTenantId(), arg.getDescribeApiNameList());
            describeList = Lists.newArrayList(map.values());
        }

        //暂不支持订单产品和退货单产品
        describeList.removeIf(x -> ObjectAPINameMapping.of(x.getApiName()) == ObjectAPINameMapping.SalesOrderProduct);
        describeList.removeIf(x -> ObjectAPINameMapping.of(x.getApiName()) == ObjectAPINameMapping.ReturnedGoodsInvoiceProduct);

        describeList.forEach(v -> {
            Optional<RecordTypeFieldDescribe> recordTypeField = ObjectDescribeExt.of(v).getRecordTypeField();
            recordTypeField.ifPresent(r -> {
                List<IRecordTypeOption> recordTypeOptions = r.getRecordTypeOptions();
                List<Map> collect = recordTypeOptions.stream().map(o -> ((DocumentBasedBean) o).getContainerDocument()).collect(Collectors.toList());
                BulkFindRecordTypeList.Item item = BulkFindRecordTypeList.Item.builder()
                        .apiName(v.getApiName())
                        .displayName(v.getDisplayName())
                        .recordType(collect)
                        .build();
                recordTypeMap.add(item);
            });
        });
        result.setRecordTypeMap(recordTypeMap);
        return result;
    }

    @ServiceMethod("bulkFindValidRecordTypeList")
    public BulkFindRecordTypeList.Result bulkFindValidRecordTypeList(BulkFindRecordTypeList.Arg arg, ServiceContext context) {
        BulkFindRecordTypeList.Result result = new BulkFindRecordTypeList.Result();
        List<BulkFindRecordTypeList.Item> recordTypeMap = Lists.newArrayList();
        Map<String, IObjectDescribe> map = describeLogicService.findObjects(context.getTenantId(), arg.getDescribeApiNameList());
        List<IObjectDescribe> describeList = Lists.newArrayList(map.values());

        //暂不支持订单产品和退货单产品
        describeList.removeIf(x -> ObjectAPINameMapping.of(x.getApiName()) == ObjectAPINameMapping.SalesOrderProduct);
        describeList.removeIf(x -> ObjectAPINameMapping.of(x.getApiName()) == ObjectAPINameMapping.ReturnedGoodsInvoiceProduct);

        Map<String, List<IRecordTypeOption>> objectMap;
        // 兼容 705 版本之前的 iOS 请求
        if (RequestUtil.isIOSRequest() && RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_705)) {
            objectMap = recordTypeService.bulkFindValidRecordTypeListWithTransfer(describeList, context.getUser());
        } else {
            objectMap = recordTypeService.bulkFindValidRecordTypeList(describeList, context.getUser());
        }
        objectMap.forEach((k, v) -> {
            if (Objects.isNull(map.get(k))) {
                return;
            }

            List<Map> collect = v.stream().map(o -> ((DocumentBasedBean) o).getContainerDocument()).collect(Collectors.toList());
            BulkFindRecordTypeList.Item item = BulkFindRecordTypeList.Item.builder()
                    .apiName(map.get(k).getApiName())
                    .displayName(map.get(k).getDisplayName())
                    .recordType(collect)
                    .build();
            recordTypeMap.add(item);
        });
        result.setRecordTypeMap(recordTypeMap);
        return result;
    }

    protected User getUser(ServiceContext context) {
        return context.getUser();
    }

    @ServiceMethod("findRecordTypeMatchList")
    public FindRecordTypeMatchList.Result findRecordTypeMatchList(ServiceContext context, FindRecordTypeMatchList.Arg arg) {
        if (StringUtils.isEmpty(arg.getDescribeApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR_OBJECT_API_NAME_IS_NULL));
        }
        FindRecordTypeMatchList.Result result;
        IObjectDescribe detailDescribe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        MasterDetailFieldDescribe masterDetailFieldDescribe = ObjectDescribeExt.of(detailDescribe).getMasterDetailFieldDescribe().orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));
        IObjectDescribe masterDescribe = describeLogicService.findObject(context.getTenantId(), masterDetailFieldDescribe.getTargetApiName());

        RecordTypeFieldDescribe recordTypeFieldDescribe = ObjectDescribeExt.of(masterDescribe).getRecordTypeField().orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));
        List<IRecordTypeOption> masterRecordTypeOptions = recordTypeFieldDescribe.getRecordTypeOptions();
        List<String> masterRecords = masterRecordTypeOptions.stream().map(x -> x.getApiName()).collect(Collectors.toList());

        RecordTypeFieldDescribe detailRecordTypeField = ObjectDescribeExt.of(detailDescribe).getRecordTypeField().orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));
        List<IRecordTypeOption> detailRecordTypeOptions = detailRecordTypeField.getRecordTypeOptions();
        List<String> records = detailRecordTypeOptions.stream().map(x -> x.getApiName()).collect(Collectors.toList());
        List<RecordTypeMatchInfo> detailRecordList = detailRecordTypeOptions.stream()
                .map(x -> RecordTypeMatchInfo.builder().apiName(x.getApiName()).label(x.getLabel()).build())
                .collect(Collectors.toList());

        //查库 查映射关系
        List<IObjectRelationMatch> relationMatchList = recordTypeService.findMatchRecordTypeRelation(context.getTenantId(), masterDetailFieldDescribe.getTargetApiName(), "", arg.getDescribeApiName());

        List<RecordTypeMatchInfo> masterRecordList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(relationMatchList)) {
            Map<String, String> recordLabelMap = Maps.newHashMap();
            masterRecordTypeOptions.forEach(x -> recordLabelMap.put(x.getApiName(), x.getLabel()));
            relationMatchList.stream().filter(x -> masterRecords.contains(x.getSourceValue())).forEach(x -> {
                String targetValue = x.getTargetValue();
                List<String> detailRecords = Lists.newArrayList(records);
                List<RecordTypeMatchInfo.RecordInfo> recordInfos = JSONArray.parseArray(targetValue, RecordTypeMatchInfo.RecordInfo.class);
                List<String> excludeDetailType = recordInfos.stream().filter(y -> !y.isMatch()).map(y -> y.getApiName()).collect(Collectors.toList());
                detailRecords.removeAll(excludeDetailType);
                RecordTypeMatchInfo matchInfo = RecordTypeMatchInfo.builder().id(x.getId()).apiName(x.getSourceValue()).label(recordLabelMap.get(x.getSourceValue())).records(detailRecords).build();
                masterRecordList.add(matchInfo);
            });
            List<String> dbMasterRecords = relationMatchList.stream().map(x -> x.getSourceValue()).collect(Collectors.toList());
            masterRecords.removeAll(dbMasterRecords);
        }
        //没有查询到结果（之前未编辑过,全部匹配展示）
        List<RecordTypeMatchInfo> otherMasterRecordType = masterRecordTypeOptions.stream()
                .filter(x -> masterRecords.contains(x.getApiName()))
                .map(x -> RecordTypeMatchInfo.builder().id("").apiName(x.getApiName()).label(x.getLabel()).records(records).build())
                .collect(Collectors.toList());
        masterRecordList.addAll(otherMasterRecordType);
        List<RecordTypeMatchInfo> masterRecordTypeMatchInfos = sortMatchInfo(masterRecordList, masterRecordTypeOptions);
        result = FindRecordTypeMatchList.Result.builder().masterLabel(masterDescribe.getDisplayName()).masterRecordList(masterRecordTypeMatchInfos).detailLabel(detailDescribe.getDisplayName()).detailRecordList(detailRecordList).build();

        return result;
    }

    @ServiceMethod("modifyRecordTypeMatchList")
    public ModifyRecordTypeMatchList.Result modifyRecordTypeMatchList(ServiceContext context, ModifyRecordTypeMatchList.Arg arg) {
        ModifyRecordTypeMatchList.Result result = new ModifyRecordTypeMatchList.Result();
        IObjectDescribe detailDescribe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        String masterApiName = ObjectDescribeExt.of(detailDescribe).getMasterDetailFieldDescribe().map(MasterDetailFieldDescribe::getTargetApiName).orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));
        RecordTypeFieldDescribe detailRecordTypeField = ObjectDescribeExt.of(detailDescribe).getRecordTypeField().orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));
        List<IRecordTypeOption> detailRecordTypeOptions = detailRecordTypeField.getRecordTypeOptions();

        List<String> detailRecords = detailRecordTypeOptions.stream().map(x -> x.getApiName()).collect(Collectors.toList());
        List<IObjectRelationMatch> relationMatchs = Lists.newArrayList();
        arg.getRecordList().forEach(x -> {
            List<String> records = x.getRecords();
            List<RecordTypeMatchInfo.RecordInfo> handleRecords = detailRecords.stream()
                    .map(y -> RecordTypeMatchInfo.RecordInfo.builder()
                            .apiName(y)
                            .isMatch(records.contains(y))
                            .build()).collect(Collectors.toList());

            IObjectRelationMatch relationMatch = new ObjectRelationMatch();
            relationMatch.setId(x.getId());
            // TODO: 2019/8/14 底层元数据添加 企业id->context
            relationMatch.setTenantId(context.getTenantId());
            relationMatch.setSourceApiName(masterApiName);
            relationMatch.setSourceValue(x.getApiName());
            relationMatch.setMatchType(IFieldType.RECORD_TYPE);
            relationMatch.setTargetApiName(arg.getDescribeApiName());
            relationMatch.setTargetValue(JSON.toJSONString(handleRecords));
            relationMatchs.add(relationMatch);
        });

        recordTypeService.createOrUpdateRecordTypeRelation(context, relationMatchs);
        return result;
    }

    @ServiceMethod("initObjectRecordType")
    public InitObjectRecordType.Result initObjectRecordType(ServiceContext context, InitObjectRecordType.Arg arg) {
        recordTypeService.recordTypeInit(context.getUser(), arg.getLayoutApiName(), context.getTenantId(), arg.getDescribeApiName());
        return InitObjectRecordType.Result.builder().build();
    }

    @ServiceMethod("initRoleAndRecordTypeView")
    public InitRoleAndRecordTypeView.Result initRoleAndRecordTypeView(InitRoleAndRecordTypeView.Arg arg, ServiceContext context) {
        Set<String> roleCodes = recordTypeService.findRoleCodesWithInitRecordType(arg.getDescribeApiName(), context.getUser());
        roleCodes.forEach(roleCode ->
                roleService.addObjectsRoleRecordType(SessionContext.of(context.getUser()), roleCode, Sets.newHashSet(arg.getDescribeApiName())));
        return new InitRoleAndRecordTypeView.Result();
    }

    private List<RecordTypeMatchInfo> sortMatchInfo(List<RecordTypeMatchInfo> masterRecordList, List<IRecordTypeOption> masterRecordTypeOptions) {
        List<RecordTypeMatchInfo> recordTypeMatchInfoList = Lists.newArrayList();
        Map<String, RecordTypeMatchInfo> matchInfoMap = masterRecordList.stream().collect(Collectors.toMap(x -> x.getApiName(), x -> x));
        for (IRecordTypeOption recordTypeOption : masterRecordTypeOptions) {
            recordTypeMatchInfoList.add(matchInfoMap.get(recordTypeOption.getApiName()));
        }
        return recordTypeMatchInfoList;
    }
}
