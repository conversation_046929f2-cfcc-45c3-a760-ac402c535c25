package com.facishare.paas.appframework.privilege.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Set;

public interface UpdateTemporaryRights {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    class Arg extends BasePrivilegeArg {
        private String scene;
        private String entityId;
        private String dataId;
        private Set<String> owners;
        private String sourceId;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {
        UpdateTemporaryRights.UpdateResult result;
    }

    @Data
    class UpdateResult {

    }
}
