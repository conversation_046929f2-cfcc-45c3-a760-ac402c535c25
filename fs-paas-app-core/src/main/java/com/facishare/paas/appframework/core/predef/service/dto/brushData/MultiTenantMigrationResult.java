package com.facishare.paas.appframework.core.predef.service.dto.brushData;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 多企业迁移数据的结果类，用于记录多个企业的迁移结果
 */
@Data
public class MultiTenantMigrationResult implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 每个企业的迁移结果
     */
    private Map<String, MigrationResult> tenantResults = new HashMap<>();
    
    /**
     * 总企业数
     */
    private int totalTenants;
    
    /**
     * 迁移成功的企业数
     */
    private int successTenants;
    
    /**
     * 迁移失败的企业数
     */
    private int failedTenants;
    
    /**
     * 添加一个企业的迁移结果
     */
    public void addTenantResult(String tenantId, MigrationResult result) {
        tenantResults.put(tenantId, result);
        totalTenants++;
        
        // 如果企业迁移有错误信息，则认为该企业迁移失败
        if (result.getErrorMessage() != null && !result.getErrorMessage().isEmpty()) {
            failedTenants++;
        } else {
            successTenants++;
        }
    }
    
    /**
     * 获取总体迁移完成率
     */
    public double getOverallCompletionRate() {
        if (totalTenants == 0) {
            return 0;
        }
        
        double totalRate = 0;
        for (MigrationResult result : tenantResults.values()) {
            totalRate += result.getCompletionRate();
        }
        
        return totalRate / totalTenants;
    }
} 