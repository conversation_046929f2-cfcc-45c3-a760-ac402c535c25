{"swagger": "2.0", "info": {"title": "AppFrameWork REST API", "description": "PaaS 应用框架权限相关REST API", "version": "1.0"}, "host": "************:8234", "schemes": ["http"], "basePath": "/v1/inner/rest", "produces": ["application/json"], "commonParams": {"userInfo": {"name": "x-fs-userInfo", "in": "header", "description": "Header中的当前操作人的ID", "required": true, "type": "string"}, "eid": {"name": "x-fs-ei", "in": "header", "description": "Header中的当前企业ID", "required": true, "type": "string"}, "peerHost": {"name": "x-fs-peer-host", "in": "header", "description": "调用方Host地址", "required": false, "type": "string"}, "peerName": {"name": "x-fs-peer-name", "in": "header", "description": "调用方Name，例如FBS,OpenAPI,CRM等", "required": false, "type": "string"}, "dataID": {"name": "body", "in": "body", "required": true, "type": "string", "describe": "数据ID"}}, "paths": {"/object_data/{ObjectDescribeApiName}/privilege/getTeamMember": {"post": {"summary": "query team member of a object", "description": "查询某数据的相关团队", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"$ref": "#/commonParams/userInfo"}, {"$ref": "#/commonParams/eid"}, {"$ref": "#/commonParams/peerHost"}, {"$ref": "#/commonParams/peerName"}, {"name": "ObjectDescribeApiName", "in": "path", "required": true, "type": "string", "describe": "是否包含关联对象的describe信息"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/commonParams/dataID"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TeamMemberInfos"}}}}}}, "definitions": {"TeamMemberInfos": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"TeamMemberInfos": {"type": "array", "items": {"type": "object"}}}}}}}}