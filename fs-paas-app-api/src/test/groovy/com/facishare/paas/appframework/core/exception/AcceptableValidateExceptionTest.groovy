package com.facishare.paas.appframework.core.exception

import spock.lang.Specification
import spock.lang.Unroll

class AcceptableValidateExceptionTest extends Specification {

    /**
     * GenerateByAI
     * 测试内容描述：验证异常构造函数的正确性，包括验证结果的存储和获取，以及父类属性的正确设置
     */
    @Unroll
    def "构造函数Test不同类型参数"() {
        given: "准备测试数据"
        def validateResult = testValue

        when: "创建AcceptableValidateException实例"
        def exception = new AcceptableValidateException(validateResult)

        then: "验证异常属性是否正确设置"
        exception.validateResult == testValue
        exception.message == "It's ok"
        exception.errorCode == 200

        where: "测试不同类型的验证结果"
        testValue << [
            "test string",
            123,
            new HashMap<String, String>(),
            null
        ]
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证getValidateResult方法的正确性
     */
    @Unroll
    def "getValidateResultTest"() {
        given: "创建带有验证结果的异常实例"
        def validateResult = "test result"
        def exception = new AcceptableValidateException(validateResult)

        when: "获取验证结果"
        def result = exception.getValidateResult()

        then: "验证返回结果是否正确"
        result == validateResult
    }
} 