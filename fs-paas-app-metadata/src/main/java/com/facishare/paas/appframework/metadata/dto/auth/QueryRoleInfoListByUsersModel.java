package com.facishare.paas.appframework.metadata.dto.auth;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

public interface QueryRoleInfoListByUsersModel {
    @EqualsAndHashCode(callSuper = true)
    @Data
    class Arg extends BaseAuthArg {
        private List<String> users;
        private List<String> excludeRoles;
        private String roleCode;
        private PageInfo pageInfo;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    class Result extends BaseAuthResult {
        private ResultModel result;
    }

    @Data
    class ResultModel {
        private PageInfo pageInfo;
        private Map<String, List<UserRolePojo>> userRoles;
    }
}
