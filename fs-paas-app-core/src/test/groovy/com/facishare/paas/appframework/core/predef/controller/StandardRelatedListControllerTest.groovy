package com.facishare.paas.appframework.core.predef.controller

import com.facishare.paas.appframework.common.util.CollectionUtils
import com.facishare.paas.appframework.metadata.search.Query
import com.facishare.paas.appframework.metadata.search.SearchQuery
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import spock.lang.Specification

/**
 * <AUTHOR> @date 2019-11-06 17:59
 *
 */
class StandardRelatedListControllerTest extends Specification {
    def action = new StandardRelatedListController()

    def "order detail list"() {
        expect:
        1 == 1
        /*
        given:
        def query = makeQuery()
        def arg = Mock(StandardRelatedListController.Arg) {
            isOrdered() >> Boolean.TRUE
            getSearchQueryInfo() >>> """{"limit":2000,"offset":0}"""
            getSearchTemplateId() >>> null
        }
        action.setArg(arg)

        when:
        action.handleOrderBys(query)

        then:
        noExceptionThrown()
        query.getOrders().get(0).getFieldName() == IObjectData.ORDER_BY
        */
    }


    def makeQuery() {
        def queryJson = """{"limit":2000,"offset":0,"filters":[{"field_name":"object_describe_api_name","field_values":["object_az2en__c"],"operator":"EQ","connector":"AND","fieldNum":0,"isObjectReference":false,"isIndex":false},{"field_name":"field_zWmmm__c","field_values":["5d8ae1bda5083db82de11078"],"operator":"EQ","connector":"AND","fieldNum":0,"isObjectReference":false,"isIndex":false}],"wheres":[],"orders":[{"fieldName":"name","isAsc":true,"isReference":false}],"whatFieldApiNames":[],"permissionType":0}"""
        return SearchTemplateQuery.fromJsonString(queryJson)
    }

    def "test_handle_orderBys"() {
        given:
        StandardRelatedListController.Arg arg = makeArg(isOrdered, searchQueryInfo, searchTemplateId)
        action.setArg(arg)
        SearchTemplateQuery query = makeSearchTemplate(searchTemplate)
        when:
        action.handleOrderBys(query)
        then:
        noExceptionThrown()
        CollectionUtils.notEmpty(query.getOrders())
        query.getOrders().first().getFieldName() == fieldName
        where:
        isOrdered | searchQueryInfo                 | searchTemplateId | fieldName  | searchTemplate
        true      | '''{"limit":2000,"offset":0}''' | null             | "order_by" | '''{"limit":2000,"offset":0,
"filters":[{"field_name":"object_describe_api_name","field_values":["object_az2en__c"],"operator":"EQ","connector":"AND","fieldNum":0,"isObjectReference":false,"isIndex":false},{"field_name":"field_zWmmm__c","field_values":["5d8ae1bda5083db82de11078"],"operator":"EQ","connector":"AND","fieldNum":0,"isObjectReference":false,"isIndex":false}],"wheres":[],"orders":[{"fieldName":"name","isAsc":true,"isReference":false}],"whatFieldApiNames":[],"permissionType":0}'''
        false     | '''{"limit":2000,"offset":0}''' | null             | "name"     | '''{"limit":2000,"offset":0,
"filters":[{"field_name":"object_describe_api_name","field_values":["object_az2en__c"],"operator":"EQ","connector":"AND","fieldNum":0,"isObjectReference":false,"isIndex":false},{"field_name":"field_zWmmm__c","field_values":["5d8ae1bda5083db82de11078"],"operator":"EQ","connector":"AND","fieldNum":0,"isObjectReference":false,"isIndex":false}],"wheres":[],"orders":[{"fieldName":"name","isAsc":true,"isReference":false}],"whatFieldApiNames":[],"permissionType":0}'''
    }

    def makeSearchTemplate(String queryJson) {
        return SearchTemplateQuery.fromJsonString(queryJson)
    }

    def makeArg(isOrdered, searchQueryInfo, id, targetObjectApiName = "WhatEver", targetObjectDataId = "WhatEver") {
        StandardRelatedListController.Arg arg = new StandardRelatedListController.Arg()
        arg.setOrdered(isOrdered)
        arg.setSearchQueryInfo(searchQueryInfo)
        arg.setSearchTemplateId(id)
        arg.setTargetObjectApiName(targetObjectApiName)
        arg.setTargetObjectDataId(targetObjectDataId)
        return arg
    }

    def "test_handle_orderBys_list_page"() {
        given:
        StandardRelatedListController.Arg arg = makeArg(isOrdered, searchQueryInfo, searchTemplateId, null, null)
        action.setArg(arg)
        SearchTemplateQuery query = makeSearchTemplate(searchTemplate)
        when:
        action.handleOrderBys(query)
        then:
        noExceptionThrown()
        CollectionUtils.empty(query.getOrders())
        where:
        isOrdered | searchQueryInfo                 | searchTemplateId | fieldName | searchTemplate
        false     | '''{"limit":2000,"offset":0}''' | null             | "name"    | '''{"limit":2000,"offset":0,
"filters":[{"field_name":"object_describe_api_name","field_values":["object_az2en__c"],"operator":"EQ","connector":"AND","fieldNum":0,"isObjectReference":false,"isIndex":false},{"field_name":"field_zWmmm__c","field_values":["5d8ae1bda5083db82de11078"],"operator":"EQ","connector":"AND","fieldNum":0,"isObjectReference":false,"isIndex":false}],"wheres":[],"orders":[{"fieldName":"name","isAsc":true,"isReference":false}],"whatFieldApiNames":[],"permissionType":0}'''

    }


    def "remove"() {
        given:
        def str = "{\"limit\":20,\"offset\":0,\"filters\":[],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}],\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"field_cK3mi__c\",\"field_values\":[\"257\"]},{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"data_own_organization\",\"field_values\":[\"999999\"]},{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"field_nSfLb__c\",\"field_values\":[\"11112\"]},{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"field_Tw5pK__c\",\"field_values\":[\"other\"]}]},{\"connector\":\"OR\",\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"field_cK3mi__c\",\"field_values\":[\"257\"]},{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"field_Tw5pK__c\",\"field_values\":[\"option1\"]},{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"field_1ZNwf__c\",\"field_values\":[\"V34799Ks5\"]}]},{\"connector\":\"OR\",\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"field_DNog4__c\",\"field_values\":[\"gka2gZ0U7\"]},{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"field_Tw5pK__c\",\"field_values\":[\"option1\"]}]},{\"connector\":\"OR\",\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"field_2uf6s__c\",\"field_values\":[\"248\"]}]}]}"
        Query query = Query.fromSearchTemplateQuery(makeSearchTemplate(str))
        SearchQuery searchQuery = query.getSearchQuery().get();
        when:

        StandardRelatedListController.removeQueryFilter(searchQuery, ["0.field_cK3mi__c.257"] as Set<String>)
        then:
        noExceptionThrown()
    }
}
