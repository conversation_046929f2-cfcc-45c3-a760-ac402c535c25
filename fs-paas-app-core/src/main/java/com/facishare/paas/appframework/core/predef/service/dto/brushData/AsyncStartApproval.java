package com.facishare.paas.appframework.core.predef.service.dto.brushData;

import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import lombok.Data;

import java.util.Set;

/**
 * Created by zhaooju on 2022/9/22
 */
public interface AsyncStartApproval {

    @Data
    class Arg {
        private String describeApiName;
        private String type;
        private Boolean triggerWorkflow;
        private Set<String> dataIds;

        public ApprovalFlowTriggerType getApprovalFlowTriggerType() {
            return ApprovalFlowTriggerType.getType(type);
        }
    }

    class Result {

    }
}
