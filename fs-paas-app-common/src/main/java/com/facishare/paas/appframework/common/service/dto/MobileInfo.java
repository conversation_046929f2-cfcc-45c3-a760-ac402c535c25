package com.facishare.paas.appframework.common.service.dto;

import org.apache.commons.lang3.StringUtils;

/**
 * Copy from fs-user-login/fs-user-login-provider/src/main/java/com/facishare/userlogin/provider/service/ValidateCodeServiceImpl.java
 */
public class MobileInfo {
    /**
     * 是否是大陆手机号
     */
    private boolean chineseMobile;
    /**
     * 国际电话区号
     */
    private final String areaCode;
    /**
     * 移动手机号码
     */
    private final String mobile;

    public MobileInfo(String areaCode, String mobile) {
        this.areaCode = StringUtils.isBlank(areaCode) ? "86" : areaCode.replace("00", "").replace("+", "").trim();
        if ("86".equals(this.areaCode)) {
            chineseMobile = true;
        }
        this.mobile = mobile;
    }

    public String smsMobile() {
        if (chineseMobile) {
            return mobile;
        }

        return String.format("00%s%s", areaCode, mobile);
    }
}
