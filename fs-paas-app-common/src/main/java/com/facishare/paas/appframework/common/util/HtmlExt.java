package com.facishare.paas.appframework.common.util;

import com.facishare.paas.appframework.core.util.RequestUtil;
import com.fxiaoke.common.Guard;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class HtmlExt {
    private static final String TEMP_FILE_NAME = "TempFileName";
    private static final String FILE = "File";
    private Document doc;

    private HtmlExt(String html) {
        doc = Jsoup.parseBodyFragment(Objects.nonNull(html) ? html : "");
    }

    public static HtmlExt of(String html) {
        return new HtmlExt(html);
    }

    private List<Element> parseValidImgElements() {
        Elements imgTags = doc.select("img[src]");
        List<Element> list = Lists.newArrayList();
        for (Element element : imgTags) {
            String url = element.attr("src");
            if (Strings.isNullOrEmpty(url) || url.contains("fxui-dist")) {
                continue;
            }
            list.add(element);
        }
        return list;
    }

    public Map<String, List<HtmlElementExt>> getAllTempImageSrc() {
        Map<String, List<HtmlElementExt>> map = Maps.newHashMap();
        List<Element> imgTags = parseValidImgElements();
        for (Element element : imgTags) {
            String url = element.attr("src");
            url = url.replace("https://", "");
            String[] parts = url.split("/");
            if (parts.length < 4) {
                continue;
            }

            String path = parts[3];
            if (StringUtils.equalsIgnoreCase(path, FILE)) {
                Map<String, String> params = UriComponentsBuilder.fromUriString(url)
                        .build()
                        .getQueryParams()
                        .toSingleValueMap();
                if (params.containsKey(TEMP_FILE_NAME)) {
                    path = params.get(TEMP_FILE_NAME);
                }
            }
            if (!Strings.isNullOrEmpty(path) && path.startsWith("TN")) {
                if (map.containsKey(path)) {
                    map.get(path).add(HtmlElementExt.of(element));
                } else {
                    map.put(path, Lists.newArrayList(HtmlElementExt.of(element)));
                }
            }
        }
        return map;
    }

    public List<HtmlElementExt> getAllImageElement() {
        List<Element> imgTags = parseValidImgElements();
        List<HtmlElementExt> list = Lists.newArrayList();
        for (Element element : imgTags) {
            list.add(HtmlElementExt.of(element));
        }
        return list;
    }


    public static class HtmlElementExt {
        private Element element;

        private HtmlElementExt(Element element) {
            this.element = element;
        }

        public static HtmlElementExt of(Element element) {
            return new HtmlElementExt(element);
        }

        public void replaceImagePath(String tnPath, String nPath) {
            if (Objects.isNull(element) || Strings.isNullOrEmpty(nPath) || Strings.isNullOrEmpty(tnPath)) {
                return;
            }
            String url = element.attr("src");
            if (Strings.isNullOrEmpty(url)) {
                return;
            }
            String newUrl = url.replace(tnPath, nPath);
            element.attr("src", newUrl);
        }

        public void convertImgPathForER(String ea) {
            String appId = RequestUtil.getAppId();
            if (Objects.isNull(element) || Strings.isNullOrEmpty(appId)) {
                return;
            }

            String url = element.attr("src");
            //String newUrl = convertImgPathER(url);
            String newUrl = convertImgPathERWithToken(ea, url);
            element.attr("src", newUrl);
        }

        private String convertImgPathERWithToken(String ea, String rawPath) {
            if (Strings.isNullOrEmpty(rawPath)) {
                return rawPath;
            }

            int index = StringUtils.indexOf(rawPath, "/i/");
            if (index < 0) {
                return rawPath;
            }

            String nPath = StringUtils.substringBetween(StringUtils.substring(rawPath, index + 2), "/");
            String shareToken = createShareToken(ea, nPath, StoneCGIConfig.getFileShareSkey());
            if (Strings.isNullOrEmpty(shareToken)) {
                return rawPath;
            }

            String newUrl = rawPath.replace("/i/", "/s/");
            return newUrl.replace(nPath, shareToken);
        }

        private String convertImgPathER(String rawPath) {
            if (Strings.isNullOrEmpty(rawPath)) {
                return rawPath;
            }

            String newUrl = rawPath.replace("/i/", "/o/");
            return String.format("%s/%s", newUrl, RequestUtil.getAppId());
        }


        private String createShareToken(String ea, String basePath, String fileShareSkey) {
            Guard guard = new Guard(fileShareSkey);
            StringBuilder raw = new StringBuilder().append(ea)
                    .append("$")
                    .append("1000")
                    .append("$")
                    .append("CRM")
                    .append("$")
                    .append(basePath)
                    .append("$")
                    .append(System.currentTimeMillis());
            try {
                return guard.encode2(raw.toString());
            } catch (Exception e) {
                log.error("createShareToken error, ea:{}, path:{}", ea, basePath, e);
            }
            return null;
        }

        private String getNPathFromToken(String token, String fileShareSkey) {
            try {
                Guard guard = new Guard(fileShareSkey);
                String decode = guard.decode(token);
                String[] array = StringUtils.split(decode, "$");
                if (Objects.nonNull(array) && array.length > 4) {
                    return array[3];
                }
                return token;
            } catch (Exception e) {
                log.error("getNPathFromToken error, token:{}, fileShareSkey:{}", token, fileShareSkey, e);
            }
            return token;
        }

        public void formatImagePath() {
            if (Objects.isNull(element)) {
                return;
            }

            String url = element.attr("src");
            if (Strings.isNullOrEmpty(url)) {
                return;
            }

            String newUrl;
            int tokenIndex = StringUtils.indexOf(url, "/s/");
            if (tokenIndex < 0) {
                newUrl = url.replace("/o/", "/i/");
                //去掉appid等无用的路径
                int index = StringUtils.lastIndexOf(newUrl, "/jpg");
                if (index >= 0) {
                    newUrl = StringUtils.substring(newUrl, 0, index + 4);
                }
            } else {
                String token = StringUtils.substringBetween(StringUtils.substring(url, tokenIndex + 2), "/");
                String nPath = getNPathFromToken(token, StoneCGIConfig.getFileShareSkey());
                if (StringUtils.isAnyEmpty(token, nPath)) {
                    return;
                }
                newUrl = url.replace("/s/", "/i/");
                newUrl = newUrl.replace(token, nPath);
            }
            if (StringUtils.isNotEmpty(newUrl)) {
                element.attr("src", newUrl);
            }
        }
    }

    public String body() {
        return doc.body().html();
    }


    public static void main(String[] args) {
        String value = "<p>1<img style=\"max-width:100%;\" type=\"l\" src=\"https://attachment.outlook.live.net/owa/MSA%3A471102375%40qq.com/service.svc/s/GetAttachmentThumbnail?id=AQMkADAwATM0MDAAMS0wNzI1LWI4Y2YtMDACLTAwCgBGAAADlmJondGRF0WFtkLqBOxGnAcAZ4TBcEsDOU2B6GlQQckNUwAAAgEJAAAAZ4TBcEsDOU2B6GlQQckNUwAFq12WaQAAAAESABAAcjErhuzLD0%2BQjvhsHTPn4g%3D%3D&thumbnailType=2&isc=1&token=*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************&X-OWA-CANARY=DIv4JGv6FUCiwU_JgQXrsEBW5ag_FtwYM_CIqtHUlvIpnbFWohtiVfaozyj6qJmGXCunsh03qe8.&owa=outlook.live.com&scriptVer=20240105004.04&clientId=F746A0AA28F74B80AC1597FF5EFCDEAF&animation=true\u200B\"/></p>";
        HtmlExt htmlExt = HtmlExt.of(value);
        htmlExt.getAllImageElement().forEach(a -> {
            a.formatImagePath();
        });
        System.out.println(htmlExt.body());

    }
}
