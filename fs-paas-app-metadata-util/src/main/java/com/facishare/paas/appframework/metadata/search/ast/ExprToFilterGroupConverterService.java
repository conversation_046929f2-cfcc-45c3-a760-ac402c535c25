package com.facishare.paas.appframework.metadata.search.ast;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 表达式转换为filterGroup的服务
 * <p>
 * 提供从字符串表达式转换为filterGroup的功能
 * <p>
 * Created by zhaooju on 2025/5/19
 */
@Slf4j
public class ExprToFilterGroupConverterService {

    private FilterGroupValidator validator = new FilterGroupValidator();
    private final ExprToFilterGroupConverter converter = new ExprToFilterGroupConverter();

    public void setValidator(FilterGroupValidator validator) {
        this.validator = validator;
    }

    /**
     * 将表达式字符串转换为filterGroup结构
     *
     * @param expression 表达式字符串，例如 "(1 AND 2) OR 3"
     * @return 转换结果
     */
    public ConversionResponse convertFromString(String expression) {
        try {
            // 词法分析
            List<Token> tokens = Lexer.tokenize(expression);
            log.info("Tokens for expression '{}': {}", expression, tokens);

            if (CollectionUtils.empty(tokens)) {
                return ConversionResponse.error(I18NExt.getOrDefault(I18NKey.EXPRESSION_ILLEGAL, "表达式非法: {0}", expression));// ignoreI18n
            }

            // 语法分析，生成AST
            Parser parser = new Parser(tokens);
            SearchQuery ast = parser.parseExpression();
            log.info("AST for expression '{}': {}", expression, ast);

            // 验证AST是否可以转换为filterGroup
            FilterGroupValidator.ValidationResult validationResult = validator.validate(ast);
            if (!validationResult.isValid()) {
                return ConversionResponse.error(I18NExt.getOrDefault(I18NKey.EXPRESSION_NOT_MATCH_FILTER_GROUP_STRUCTURE, "表达式不符合filterGroup结构: {0}", validationResult.getReason()));// ignoreI18n
            }

            // 将AST转换为filterGroup
            ExprToFilterGroupConverter.ConversionResult conversionResult = converter.convert(ast);
            log.info("Conversion result for expression '{}': {}", expression, conversionResult);

            return ConversionResponse.success(
                    conversionResult.getFilterGroupMap(),
                    conversionResult.getUngroupedFilters()
            );
        } catch (Exception e) {
            log.error("Error converting expression '{}' to filterGroup", expression, e);
            return ConversionResponse.error(I18NExt.getOrDefault(I18NKey.CONVERT_EXPRESSION_ERROR, "转换表达式时发生错误: {0}", e.getMessage()));// ignoreI18n
        }
    }

    /**
     * 验证表达式是否可以转换为filterGroup结构
     *
     * @param expression 表达式字符串，例如 "(1 AND 2) OR 3"
     * @return 验证结果
     */
    public FilterGroupValidator.ValidationResult validate(String expression) {
        try {
            List<Token> tokens = Lexer.tokenize(expression);
            Parser parser = new Parser(tokens);
            SearchQuery ast = parser.parseExpression();
            return validator.validate(ast);
        } catch (Exception e) {
            log.error("Error validating expression '{}'", expression, e);
            return FilterGroupValidator.ValidationResult.invalid(I18NExt.getOrDefault(I18NKey.VALIDATE_EXPRESSION_ERROR, "验证表达式时发生错误: {0}", e.getMessage())); // ignoreI18n
        }
    }

    /**
     * 估算表达式需要的filterGroup数量
     *
     * @param expression 表达式字符串，例如 "(1 AND 2) OR 3"
     * @return filterGroup数量
     */
    public int estimateFilterGroupCount(String expression) {
        try {
            List<Token> tokens = Lexer.tokenize(expression);
            Parser parser = new Parser(tokens);
            SearchQuery ast = parser.parseExpression();
            return validator.estimateFilterGroupCount(ast);
        } catch (Exception e) {
            log.error("Error estimating filterGroup count for expression '{}'", expression, e);
            return 0;
        }
    }

    /**
     * 转换结果
     */
    @Data
    public static class ConversionResponse {
        private boolean success;
        private String errorMessage;
        private Map<String, List<String>> filterGroupMap;
        private List<String> ungroupedFilters;

        public static ConversionResponse success(Map<String, List<String>> filterGroupMap, List<String> ungroupedFilters) {
            ConversionResponse response = new ConversionResponse();
            response.setSuccess(true);
            response.setFilterGroupMap(filterGroupMap);
            response.setUngroupedFilters(ungroupedFilters);
            return response;
        }

        public static ConversionResponse error(String errorMessage) {
            ConversionResponse response = new ConversionResponse();
            response.setSuccess(false);
            response.setErrorMessage(errorMessage);
            return response;
        }
    }
} 