package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.appframework.privilege.model.DataPrivilegeProvider;
import com.facishare.paas.appframework.privilege.model.DataPrivilegeProviderDefinition.BatchCheckBusinessPrivilege;
import com.facishare.paas.appframework.privilege.model.DataPrivilegeProviderDefinition.CheckBusinessPrivilege;
import com.facishare.paas.appframework.privilege.model.DataPrivilegeProviderManager;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
@ServiceModule("DataPrivilegeProvider")
public class ObjectDataPrivilegeProviderService {
    @Autowired
    private DataPrivilegeProviderManager providerManager;

    @ServiceMethod("batchCheckBusinessPrivilege")
    public BatchCheckBusinessPrivilege.Result batchCheckBusinessPrivilege(BatchCheckBusinessPrivilege.Arg arg, ServiceContext context) {
        DataPrivilegeProvider provider = providerManager.getLocalProvider(arg.getDescribeApiName());
        if (Objects.isNull(provider)) {
            Map<String, Map<String, Permissions>> actionDataPrivilegeMap = Maps.newHashMap();
            CollectionUtils.nullToEmpty(arg.getActionCodes()).forEach(actionCode -> actionDataPrivilegeMap.put(actionCode, arg.getDataPrivilegeMap()));
            return BatchCheckBusinessPrivilege.Result.builder().result(actionDataPrivilegeMap).build();
        }

        List<IObjectData> dataList = CollectionUtils.nullToEmpty(arg.getDataList())
                .stream()
                .map(ObjectData::new)
                .collect(Collectors.toList());
        Map<String, Map<String, Permissions>> map = provider.checkBusinessPrivilege(context.getUser(),
                arg.getDataPrivilegeMap(), dataList, Lists.newArrayList(arg.getActionCodes()));
        return BatchCheckBusinessPrivilege.Result.builder().result(map).build();
    }

    @ServiceMethod("checkBusinessPrivilege")
    public CheckBusinessPrivilege.Result checkBusinessPrivilege(CheckBusinessPrivilege.Arg arg, ServiceContext context) {
        DataPrivilegeProvider provider = providerManager.getLocalProvider(arg.getDescribeApiName());
        if (Objects.isNull(provider)) {
            return CheckBusinessPrivilege.Result.builder().result(arg.getDataPrivilegeMap()).build();
        }

        List<IObjectData> dataList = CollectionUtils.nullToEmpty(arg.getDataList())
                .stream()
                .map(ObjectData::new)
                .collect(Collectors.toList());
        Map<String, Permissions> permissionsMap = provider.checkBusinessPrivilege(context.getUser(),
                arg.getDataPrivilegeMap(), dataList, arg.getActionCode());
        return CheckBusinessPrivilege.Result.builder().result(permissionsMap).build();
    }


}
