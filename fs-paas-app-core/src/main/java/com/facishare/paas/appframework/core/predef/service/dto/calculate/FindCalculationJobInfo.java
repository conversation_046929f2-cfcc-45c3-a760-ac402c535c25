package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.service.dto.QueryJob;
import com.facishare.paas.appframework.metadata.repository.model.MtAsyncTaskMonitor;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * Created by zhouwr on 2024/10/11.
 */
public interface FindCalculationJobInfo {

    @Data
    class Arg {
        private String objectApiName;
        private String fieldApiName;
        private String jobId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Result {
        private CalculationJobInfo jobInfo;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class CalculationJobInfo {
        private String jobStatus;
        private Long totalDataNum;
        private Long completeDataNum;
        //预计执行时间
        private Long estimatedStartTime;
        //执行时间
        private Long startTime;
        //预计完成时间
        private Long estimatedCompleteTime;
        //完成时间
        private Long completeTime;
        private String jobId;
        private String jobStatusCode;
        private String objectApiName;
        private List<String> fieldApiNames;

        public static CalculationJobInfo of(QueryJob.JobInfo jobInfo) {
            return CalculationJobInfo.builder()
                    .jobStatus(jobInfo.jobStatusText())
                    .totalDataNum((long) jobInfo.getPredictDataNum())
                    .completeDataNum((long) jobInfo.getCompleteDataNum())
                    .estimatedStartTime(jobInfo.estimatedStartTime())
                    .startTime(jobInfo.startTime())
                    .estimatedCompleteTime(jobInfo.estimatedCompleteTime())
                    .completeTime(jobInfo.completeTime())
                    .jobId(jobInfo.getId())
                    .jobStatusCode(String.valueOf(jobInfo.getStatus()))
                    .objectApiName(jobInfo.getObjectDescribeApiName())
                    .fieldApiNames(JSON.parseArray(jobInfo.getJobParam(), String.class))
                    .build();
        }

        public static CalculationJobInfo of(MtAsyncTaskMonitor jobInfo) {
            return CalculationJobInfo.builder()
                    .jobStatus(jobInfo.jobStatusText())
                    .totalDataNum(jobInfo.getTaskTotalNum())
                    .completeDataNum(jobInfo.getCompletedNum())
                    .estimatedStartTime(jobInfo.estimatedStartTime())
                    .startTime(jobInfo.startTime())
                    .estimatedCompleteTime(jobInfo.estimatedCompleteTime())
                    .completeTime(jobInfo.getEndTime())
                    .jobId(jobInfo.getId())
                    .jobStatusCode(jobInfo.getTaskStatus())
                    .objectApiName(jobInfo.getObjectDescribeApiName())
                    .fieldApiNames(Lists.newArrayList(StringUtils.split(jobInfo.getBizApiName(), ",")))
                    .build();
        }
    }

}
