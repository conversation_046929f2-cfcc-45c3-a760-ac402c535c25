package com.facishare.paas.appframework.privilege.dto;

import lombok.*;
import lombok.experimental.Delegate;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by yusb on 2017/4/24.
 */
public class QueryEntityShareModel {

    //1. entityId ,scope, permission 参数具体说明请参考“ 数据结构设计-》对象级权限”
    // entityId	对象实体Id
    // scope	对象权限范围
    // permission	操作权限
    //2. entityOpenness, page 参数都可以为空，page如果为空取默认值(pageSize = 10000, currentPage = 1)

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Arg extends BasePrivilegeArg {
        private List<String> entitys;
        private List<String> sharesOrReceives;
        private List<String> sources;
        private List<String> receives;
        private List<Integer> sourceTypes;
        private List<Integer> receiveTypes;
        private Integer permission;
        private Integer status;
        private Integer scope;
        private BasePageInfoDataPrivilege page;
        private Map<Integer, Set<String>> sharesId;
        private Map<Integer, Set<String>> receivesId;
        private Map<Integer, Set<String>> sharesOrReceivesId;
        private Boolean outReceive;
        private Integer basedType;

        /**
         * 缺省时不用id查询
         */
        private List<String> ids;

        /**
         * 0来源方上游 1来源方下游
         *
         * NULL 查询全部
         */
        private Integer entityShareType;

        private Set<String> groupIds;

        public static Arg createByIds(List<String> sharedRuleIds, AuthContext buildAuthContext) {
            Arg arg = new Arg();
            arg.setIds(sharedRuleIds);
            arg.setContext(buildAuthContext);
            return arg;
        }

        public static Arg createByGroupIds(Set<String> groupIds, AuthContext buildAuthContext) {
            Arg arg = new Arg();
            arg.setGroupIds(groupIds);
            arg.setContext(buildAuthContext);
            return arg;
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Result extends BasePrivilegeResult {
        @Delegate
        private BaseResultDataContent result;

        @Data
        public static class BaseResultDataContent {
            private List<EntitySharePojo> content;
            private BasePageInfoDataPrivilege page;
        }
    }


}
