package com.facishare.paas.appframework.metadata.data.validator;

import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * FileValidator 单元测试
 */
@ExtendWith(MockitoExtension.class)
class FileValidatorTest {

  private FileValidator validator;

  @BeforeEach
  void setUp() {
    validator = new FileValidator();
  }

  @Test
  void testSupportFieldTypes() {
    Set<String> supportedTypes = validator.supportFieldTypes();
    
    assertEquals(3, supportedTypes.size());
    assertTrue(supportedTypes.contains(IFieldType.IMAGE));
    assertTrue(supportedTypes.contains(IFieldType.FILE_ATTACHMENT));
    assertTrue(supportedTypes.contains(IFieldType.SIGNATURE));
  }

  @Test
  void testValidateDataType_WithImageType_ShouldCallObjectDataExt() {
    // Given
    String fieldApiName = "test_field";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.IMAGE);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, "image_file_id");
    IObjectData objectData = new ObjectData(dataMap);
    
    ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);

    try (MockedStatic<ObjectDataExt> mockedStatic = mockStatic(ObjectDataExt.class)) {
      mockedStatic.when(() -> ObjectDataExt.of(objectData)).thenReturn(mockObjectDataExt);

      // When
      validator.validateDataType(fieldApiName, objectData, describe);

      // Then
      verify(mockObjectDataExt).validateFileFieldDataType(any(IFieldDescribe.class));
    }
  }

  @Test
  void testValidateDataType_WithFileAttachmentType_ShouldCallObjectDataExt() {
    // Given
    String fieldApiName = "test_field";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.FILE_ATTACHMENT);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, Arrays.asList("file_id_1", "file_id_2"));
    IObjectData objectData = new ObjectData(dataMap);
    
    ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);

    try (MockedStatic<ObjectDataExt> mockedStatic = mockStatic(ObjectDataExt.class)) {
      mockedStatic.when(() -> ObjectDataExt.of(objectData)).thenReturn(mockObjectDataExt);

      // When
      validator.validateDataType(fieldApiName, objectData, describe);

      // Then
      verify(mockObjectDataExt).validateFileFieldDataType(any(IFieldDescribe.class));
    }
  }

  @Test
  void testValidateDataType_WithSignatureType_ShouldCallObjectDataExt() {
    // Given
    String fieldApiName = "test_field";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.SIGNATURE);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, "signature_file_id");
    IObjectData objectData = new ObjectData(dataMap);
    
    ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);

    try (MockedStatic<ObjectDataExt> mockedStatic = mockStatic(ObjectDataExt.class)) {
      mockedStatic.when(() -> ObjectDataExt.of(objectData)).thenReturn(mockObjectDataExt);

      // When
      validator.validateDataType(fieldApiName, objectData, describe);

      // Then
      verify(mockObjectDataExt).validateFileFieldDataType(any(IFieldDescribe.class));
    }
  }

  @Test
  void testValidateDataType_WithValidationException_ShouldThrowException() {
    // Given
    String fieldApiName = "test_field";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.IMAGE);
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldApiName, "invalid_file_id");
    IObjectData objectData = new ObjectData(dataMap);

    ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);
    doThrow(new RuntimeException("Invalid file data")).when(mockObjectDataExt).validateFileFieldDataType(any(IFieldDescribe.class));

    try (MockedStatic<ObjectDataExt> mockedStatic = mockStatic(ObjectDataExt.class)) {
      mockedStatic.when(() -> ObjectDataExt.of(objectData)).thenReturn(mockObjectDataExt);

      // When & Then
      RuntimeException exception = assertThrows(RuntimeException.class,
          () -> validator.validateDataType(fieldApiName, objectData, describe));

      assertEquals("Invalid file data", exception.getMessage());
    }
  }

  @Test
  void testValidateDataType_WithNullFieldApiName_ShouldReturn() {
    // Given
    String fieldApiName = null;
    IObjectDescribe describe = createObjectDescribe("test_field", IFieldType.IMAGE);
    IObjectData objectData = new ObjectData(Collections.emptyMap());

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithEmptyFieldApiName_ShouldReturn() {
    // Given
    String fieldApiName = "";
    IObjectDescribe describe = createObjectDescribe("test_field", IFieldType.IMAGE);
    IObjectData objectData = new ObjectData(Collections.emptyMap());

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, describe));
  }

  @Test
  void testValidateDataType_WithNullData_ShouldReturn() {
    // Given
    String fieldApiName = "test_field";
    IObjectDescribe describe = createObjectDescribe(fieldApiName, IFieldType.IMAGE);

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, null, describe));
  }

  @Test
  void testValidateDataType_WithNullDescribe_ShouldReturn() {
    // Given
    String fieldApiName = "test_field";
    IObjectData objectData = new ObjectData(Collections.emptyMap());

    // When & Then
    assertDoesNotThrow(() -> validator.validateDataType(fieldApiName, objectData, null));
  }

  /**
   * 创建测试用的 IObjectDescribe 对象
   */
  private IObjectDescribe createObjectDescribe(String fieldApiName, String fieldType) {
    IObjectDescribe describe = new ObjectDescribe();
    describe.setApiName("TestObj");
    
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", fieldApiName);
    fieldMap.put("type", fieldType);
    fieldMap.put("label", "测试字段");
    
    IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
    
    describe.setFieldDescribes(Arrays.asList(fieldDescribe));
    return describe;
  }
}
