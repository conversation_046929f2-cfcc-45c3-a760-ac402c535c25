package com.facishare.paas.appframework.core.util

import spock.lang.Specification

/**
 * Created by zhouwr on 2019/11/14
 */
class TypesTest extends Specification {

    def "test detectFirstGenericArgType"() {
        given:
        def clazz = Types.detectFirstGenericArgType(GenericClass.P2.class)
        def clazz1 = Types.detectFirstGenericArgType(GenericClass.P3.class)
        when:
        println(clazz)
        println(clazz1)
        then:
        noExceptionThrown()
    }
}
