package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.data.*;
import com.facishare.paas.appframework.flow.ExtraDataKeys;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.model.RequestContext.Biz.ApprovalFlow;

/**
 * Created by zhouwr on 2018/1/26
 */
@Slf4j
@Component
@ServiceModule("data")
public class ObjectDataModifyService {

    @Autowired
    private MetaDataService metaDataService;

    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private DataSnapshotLogicService dataSnapshotLogicService;

    @Autowired
    private MetaDataMiscService metaDataMiscService;

    @ServiceMethod("batchUpdateByFields")
    public BatchUpdateByFields.Result batchUpdateByFields(BatchUpdateByFields.Arg arg, ServiceContext context) {
        if (CollectionUtils.empty(arg.getDataList()) || CollectionUtils.empty(arg.getUpdateFieldList())) {
            return BatchUpdateByFields.Result.builder().success(true).build();
        }

        log.debug("batchUpdateByFields,context:{},arg:{}", context, arg);

        List<IObjectData> dataList = ObjectDataDocument.ofDataList(arg.getDataList());
        dataList.forEach(x -> {
            x.set(IObjectData.TENANT_ID, context.getTenantId());
            x.set(IObjectData.DESCRIBE_API_NAME, arg.getObjectApiName());
        });
        metaDataService.batchUpdateByFields(context.getUser(), dataList, arg.getUpdateFieldList());

        return BatchUpdateByFields.Result.builder().success(true).build();
    }

    @ServiceMethod("findDataById")
    public FindDataById.Result findDataById(FindDataById.Arg arg, ServiceContext context) {
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());

        IObjectData objectData = metaDataService.findObjectData(context.getUser(), arg.getDataId(), describe);
        try {
            metaDataService.doDataPrivilegeCheck(context.getUser(), Lists.newArrayList(objectData), describe,
                    ObjectAction.VIEW_DETAIL.getActionCode());
        } catch (ValidateException e) {
            //无数据权限
            return FindDataById.Result.builder().build();
        }

        return FindDataById.Result.builder()
                .objectData(ObjectDataDocument.of(objectData))
                .build();
    }

    @ServiceMethod("findIdByName")
    public FindIdByName.Result findIdByName(FindIdByName.Arg arg, ServiceContext context) {
        log.debug("findIdByName,context:{},arg:{}", context, arg);
        if (CollectionUtils.empty(arg.getNameList()) ||
                Strings.isNullOrEmpty(arg.getObjectApiName())) {
            return FindIdByName.Result.builder().nameIdMap(Maps.newHashMap()).build();
        }

        Map<String, String> map = metaDataService.findObjectIdByName(context.getUser(), arg.getObjectApiName(), arg.getNameList());
        return FindIdByName.Result.builder().nameIdMap(map).build();
    }

    @ServiceMethod("approvalTriggerSuccessCallback")
    public ApprovalTriggerSuccessCallback.Result approvalTriggerSuccessCallback(ApprovalTriggerSuccessCallback.Arg arg, ServiceContext context) {
        if (Strings.isNullOrEmpty(arg.getDataId())
                || Strings.isNullOrEmpty(arg.getDescribeApiName())
                || Strings.isNullOrEmpty(arg.getOtherBizId())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        ObjectDataSnapshot dbSnapshot = dataSnapshotLogicService.findAndMergeSnapshot(context.getTenantId(), arg.getDescribeApiName(),
                arg.getDataId(), ApprovalFlow.getCode(), arg.getOtherBizId());
        if (dbSnapshot != null && CollectionUtils.notEmpty(dbSnapshot.getMasterSnapshot())) {
            log.warn("snapshot is already exist,tenantId:{},describeApiName:{},dataId:{},otherBizId:{}", context.getTenantId(),
                    arg.getDescribeApiName(), arg.getDataId(), arg.getOtherBizId());
            return new ApprovalTriggerSuccessCallback.Result();
        }

        Map<String, Object> extraData = arg.getExtraData();
        Map<String, Map<String, Object>> detailChange = null;
        if (CollectionUtils.notEmpty(extraData)) {
            detailChange = (Map<String, Map<String, Object>>) extraData.get(ExtraDataKeys.DETAIL_CHANGE);
        }

        ObjectDataSnapshot snapshot = ObjectDataSnapshot.builder()
                .masterSnapshot(arg.getCallbackData())
                .detailSnapshot(detailChange)
                .biz(ApprovalFlow.getCode())
                .bizId(arg.getOtherBizId())
                .build();
        dataSnapshotLogicService.createSnapshot(context.getUser(), arg.getDescribeApiName(), arg.getDataId(), snapshot);
        return new ApprovalTriggerSuccessCallback.Result();
    }

    @ServiceMethod("findAndMergeSnapshot")
    public FindAndMergeSnapshot.Result findAndMergeSnapshot(FindAndMergeSnapshot.Arg arg, ServiceContext context) {
        ObjectDataSnapshot snapshot = dataSnapshotLogicService.findAndMergeSnapshot(context.getTenantId(), arg.getDescribeApiName(), arg.getDataId(),
                arg.getBiz(), arg.getBizId());
        if (BooleanUtils.isTrue(arg.getFillExtendField())) {
            fillFieldInfo(context.getUser(), arg.getDescribeApiName(), snapshot.getMasterSnapshot(), snapshot.getDetailSnapshot());
        }
        return FindAndMergeSnapshot.Result.builder().snapshot(snapshot).build();
    }

    private void fillFieldInfo(User user, String describeApiName, Map<String, Object> masterSnapshot,
                               Map<String, Map<String, Object>> detailSnapshot) {
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), describeApiName);
        metaDataMiscService.fillExtendFieldInfo(describe, Lists.newArrayList(ObjectDataExt.of(masterSnapshot)), user);
        if (CollectionUtils.empty(detailSnapshot)) {
            return;
        }
        Map<String, IObjectDescribe> detailDescribeMap = describeLogicService.findDetailDescribes(user.getTenantId(), describeApiName).stream()
                .collect(Collectors.toMap(IObjectDescribe::getApiName, Function.identity()));
        detailSnapshot.forEach((detailApiName, changes) -> {
            IObjectDescribe objectDescribe = detailDescribeMap.get(detailApiName);
            if (Objects.isNull(objectDescribe) || CollectionUtils.empty(changes)) {
                return;
            }
            metaDataMiscService.fillExtendFieldInfo(objectDescribe, getDataList(changes), user);
        });
    }

    private List<IObjectData> getDataList(Map<String, Object> changes) {
        List<IObjectData> result = Lists.newArrayList();
        List<Map<String, Object>> addList = (List<Map<String, Object>>) changes.getOrDefault(ObjectAction.CREATE.getActionCode(), Lists.newArrayList());
        if (CollectionUtils.notEmpty(addList)) {
            addList.forEach(it -> result.add(ObjectDataExt.of(it)));
        }
        Map<String, Map<String, Object>> updateList = (Map<String, Map<String, Object>>) changes.getOrDefault(ObjectAction.UPDATE.getActionCode(), Maps.newHashMap());
        if (CollectionUtils.notEmpty(updateList)) {
            updateList.values().forEach(it -> result.add(ObjectDataExt.of(it)));
        }
        return result;
    }

}

