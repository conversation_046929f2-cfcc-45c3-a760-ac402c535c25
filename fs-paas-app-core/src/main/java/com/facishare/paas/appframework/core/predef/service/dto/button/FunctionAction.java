package com.facishare.paas.appframework.core.predef.service.dto.button;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.function.FunctionInfo;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.impl.UdefAction;
import lombok.Builder;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 函数操作
 * <p>
 * Created by liyiguang on 2018/2/3.
 */
@Data
@Builder
public class FunctionAction {
    private ServiceContext context;
    private String bindingObjectDescribeAPName;
    private ActionPojo actionPojo;
    private FunctionInfo functionInfo;

    public void validate() {
        if (Objects.isNull(actionPojo)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        ActionParameter actionParameter = ActionParameter.of(actionPojo.getAction_paramter());
        if (Objects.isNull(actionParameter)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        List<FunctionArg> functionArgs = actionParameter.getFunctionArgs();
        List<FunctionInfo.Parameter> functionParameters = functionInfo.getParameters();
        if (functionArgs.size() != functionParameters.size()) {
            throw new ValidateException(I18N.text(I18NKey.OPERATION_PARAM_AND_FUNCTION_PARAM_IS_DIFFERENT));
        }

        for (FunctionInfo.Parameter parameter : functionParameters) {
            FunctionArg arg = functionArgs.get(0);
            //参数类型和名称
            if (!parameter.getName().equals(arg.getName()) || !parameter.getType().equals(arg.getType())) {
                throw new ValidateException(I18N.text(I18NKey.OPERATION_PARAM_AND_FUNCTION_PARAM_IS_DIFFERENT_FUNCTION_PARAM, parameter, arg));
            }
        }
    }

    public IUdefAction getAction() {
        IUdefAction action = actionPojo.toUDefAction();
        action.setTenantId(context.getTenantId());
        action.setDescribeApiName(bindingObjectDescribeAPName);

        return action;
    }

    public IUdefFunction getFunction() {
        IUdefFunction function = functionInfo.toUDefFunction();
        function.setBindingObjectApiName(bindingObjectDescribeAPName);
        function.setTenantId(context.getTenantId());
        return function;
    }

    @Data
    public static class ActionParameter {
        @JSONField(name = "func_api_name")
        String functionApiName;
        @JSONField(name = "func_args")
        List<FunctionArg> functionArgs;
        @JSONField(name = "ui_event_id")
        String uiEventId;

        public static ActionParameter createByFuncApiName(String funcApiName) {
            FunctionAction.ActionParameter actionParameter = new FunctionAction.ActionParameter();
            actionParameter.setFunctionApiName(funcApiName);
            actionParameter.setFunctionArgs(Collections.emptyList());
            return actionParameter;
        }

        public List<FunctionArg> getFunctionArgs() {
            if (functionArgs == null) {
                return Collections.emptyList();
            }
            return functionArgs;
        }

        public static ActionParameter of(String json) {
            if (json == null) {
                return null;
            }
            ActionParameter actionParameter = JSON.parseObject(json, ActionParameter.class);
            return actionParameter;
        }

        public String toJson() {
            return JSON.toJSONString(this);
        }

        public IUdefAction toIUdefAction() {
            IUdefAction action = new UdefAction();
            action.setActionParamter(this.toJson());
            return action;
        }
    }

    @Data
    public static class FunctionArg {
        String name;
        String type;
        String value;
    }
}
