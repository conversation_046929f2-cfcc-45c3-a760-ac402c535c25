package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class SuspendedComponentInfoTest {

  private SuspendedComponentInfo suspendedComponentInfo;
  
  @Mock
  private IFieldDescribe fieldDescribe;

  @BeforeEach
  void setUp() {
    suspendedComponentInfo = new SuspendedComponentInfo();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试SuspendedComponentInfo的无参构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试无参构造函数")
  void testDefaultConstructor() {
    SuspendedComponentInfo info = new SuspendedComponentInfo();
    assertNotNull(info, "无参构造函数创建的对象不应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试SuspendedComponentInfo的Map构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = Maps.newHashMap();
    map.put(ISuspendedComponentInfo.TYPE, "floating_action");
    
    // 创建操作列表
    Map<String, Object> actionMap = Maps.newHashMap();
    actionMap.put(SuspendedActionInfo.TYPE, "create");
    actionMap.put(SuspendedActionInfo.URL, "/create");
    List<Map> actions = Lists.newArrayList(actionMap);
    map.put(ISuspendedComponentInfo.ACTIONS, actions);
    
    // 创建国际化属性
    Map<String, Object> i18nProps = Maps.newHashMap();
    i18nProps.put("title", "悬浮按钮");
    map.put(ISuspendedComponentInfo.I18N_PROPS, i18nProps);
    
    SuspendedComponentInfo info = new SuspendedComponentInfo(map);
    assertNotNull(info, "Map构造函数创建的对象不应为空");
    assertEquals("floating_action", info.getType(), "应正确设置类型");
    assertEquals(1, info.getActions().size(), "应正确设置操作列表");
    assertEquals(1, info.getI18NProps().size(), "应正确设置国际化属性");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getType和setType方法
   */
  @Test
  @DisplayName("正常场景 - 测试Type属性")
  void testType() {
    // 初始状态应为null
    assertNull(suspendedComponentInfo.getType(), "初始类型应为null");
    
    // 设置类型
    suspendedComponentInfo.setType("suspended_button");
    assertEquals("suspended_button", suspendedComponentInfo.getType(), "应正确设置类型");
    
    // 设置为null
    suspendedComponentInfo.setType(null);
    assertNull(suspendedComponentInfo.getType(), "应支持设置为null");
    
    // 设置为空字符串
    suspendedComponentInfo.setType("");
    assertEquals("", suspendedComponentInfo.getType(), "应支持设置为空字符串");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getActions和setActions方法 - 正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试Actions属性")
  void testActions_NormalScenario() {
    // 初始状态应为空列表
    List<SuspendedActionInfo> initialActions = suspendedComponentInfo.getActions();
    assertNotNull(initialActions, "初始操作列表不应为空");
    assertTrue(initialActions.isEmpty(), "初始操作列表应为空");
    
    // 创建操作列表
    SuspendedActionInfo action1 = new SuspendedActionInfo();
    action1.setType("create");
    action1.setUrl("/create");
    
    SuspendedActionInfo action2 = new SuspendedActionInfo();
    action2.setType("edit");
    action2.setUrl("/edit");
    
    List<SuspendedActionInfo> actions = Lists.newArrayList(action1, action2);
    
    // 设置操作列表
    suspendedComponentInfo.setActions(actions);
    
    // 获取操作列表
    List<SuspendedActionInfo> retrievedActions = suspendedComponentInfo.getActions();
    assertNotNull(retrievedActions, "获取的操作列表不应为空");
    assertEquals(2, retrievedActions.size(), "操作列表大小应为2");
    assertEquals("create", retrievedActions.get(0).getType(), "第一个操作类型应为create");
    assertEquals("edit", retrievedActions.get(1).getType(), "第二个操作类型应为edit");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setActions方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试setActions方法处理null值")
  void testSetActions_NullValue() {
    // 设置为null
    suspendedComponentInfo.setActions(null);
    
    // 获取操作列表
    List<SuspendedActionInfo> actions = suspendedComponentInfo.getActions();
    assertNotNull(actions, "null值设置后获取的操作列表不应为空");
    assertTrue(actions.isEmpty(), "null值设置后操作列表应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setActions方法 - 空列表场景
   */
  @Test
  @DisplayName("边界场景 - 测试setActions方法处理空列表")
  void testSetActions_EmptyList() {
    // 设置为空列表
    suspendedComponentInfo.setActions(Lists.newArrayList());
    
    // 获取操作列表
    List<SuspendedActionInfo> actions = suspendedComponentInfo.getActions();
    assertNotNull(actions, "空列表设置后获取的操作列表不应为空");
    assertTrue(actions.isEmpty(), "空列表设置后操作列表应为空");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getI18NProps方法 - 正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试getI18NProps方法")
  void testGetI18NProps_NormalScenario() {
    // 初始状态应为空Map
    Map<String, Object> initialProps = suspendedComponentInfo.getI18NProps();
    assertNotNull(initialProps, "初始国际化属性不应为空");
    assertTrue(initialProps.isEmpty(), "初始国际化属性应为空");
    
    // 通过Map构造函数设置国际化属性
    Map<String, Object> map = Maps.newHashMap();
    Map<String, Object> i18nProps = Maps.newHashMap();
    i18nProps.put("title", "悬浮操作");
    i18nProps.put("description", "快速操作按钮");
    map.put(ISuspendedComponentInfo.I18N_PROPS, i18nProps);
    
    SuspendedComponentInfo info = new SuspendedComponentInfo(map);
    Map<String, Object> retrievedProps = info.getI18NProps();
    
    assertNotNull(retrievedProps, "获取的国际化属性不应为空");
    assertEquals(2, retrievedProps.size(), "国际化属性大小应为2");
    assertEquals("悬浮操作", retrievedProps.get("title"), "标题应正确");
    assertEquals("快速操作按钮", retrievedProps.get("description"), "描述应正确");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getI18NProps方法 - null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试getI18NProps方法处理null值")
  void testGetI18NProps_NullValue() {
    // 通过Map构造函数设置null国际化属性
    Map<String, Object> map = Maps.newHashMap();
    map.put(ISuspendedComponentInfo.I18N_PROPS, null);
    
    SuspendedComponentInfo info = new SuspendedComponentInfo(map);
    Map<String, Object> props = info.getI18NProps();
    
    assertNotNull(props, "null值时获取的国际化属性不应为空");
    assertTrue(props.isEmpty(), "null值时国际化属性应为空Map");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试onFieldDelete方法
   */
  @Test
  @DisplayName("正常场景 - 测试onFieldDelete方法")
  void testOnFieldDelete() {
    // onFieldDelete方法是空实现，应该不抛出异常
    assertDoesNotThrow(() -> {
      suspendedComponentInfo.onFieldDelete(fieldDescribe);
    }, "onFieldDelete方法不应抛出异常");
    
    // 测试null参数
    assertDoesNotThrow(() -> {
      suspendedComponentInfo.onFieldDelete(null);
    }, "onFieldDelete方法处理null参数不应抛出异常");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试onFieldUpdate方法
   */
  @Test
  @DisplayName("正常场景 - 测试onFieldUpdate方法")
  void testOnFieldUpdate() {
    // onFieldUpdate方法是空实现，应该不抛出异常
    Map<String, Object> fieldMap = Maps.newHashMap();
    fieldMap.put("field_name", "test_field");
    
    assertDoesNotThrow(() -> {
      suspendedComponentInfo.onFieldUpdate("test_field", fieldMap);
    }, "onFieldUpdate方法不应抛出异常");
    
    // 测试null参数
    assertDoesNotThrow(() -> {
      suspendedComponentInfo.onFieldUpdate(null, null);
    }, "onFieldUpdate方法处理null参数不应抛出异常");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试接口实现
   */
  @Test
  @DisplayName("正常场景 - 测试接口实现")
  void testInterfaceImplementation() {
    // 验证实现了ISuspendedComponentInfo接口
    assertTrue(suspendedComponentInfo instanceof ISuspendedComponentInfo, 
               "应实现ISuspendedComponentInfo接口");
    
    // 验证继承自AbstractComponent
    assertTrue(suspendedComponentInfo instanceof com.facishare.paas.metadata.impl.ui.layout.component.AbstractComponent, 
               "应继承自AbstractComponent");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试继承的AbstractComponent功能
   */
  @Test
  @DisplayName("正常场景 - 测试继承功能")
  void testInheritedFunctionality() {
    // 通过Map构造函数测试继承的功能
    Map<String, Object> map = Maps.newHashMap();
    map.put("custom_field", "custom_value");
    map.put(ISuspendedComponentInfo.TYPE, "custom_suspended");
    
    SuspendedComponentInfo info = new SuspendedComponentInfo(map);
    assertEquals("custom_suspended", info.getType(), "应正确处理类型");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试复杂的悬浮组件配置
   */
  @Test
  @DisplayName("正常场景 - 测试复杂悬浮组件配置")
  void testComplexSuspendedComponentConfiguration() {
    // 创建复杂的悬浮组件配置
    SuspendedActionInfo createAction = new SuspendedActionInfo();
    createAction.setType("new_object");
    createAction.setUrl("/app/create/Account");
    
    SuspendedActionInfo editAction = new SuspendedActionInfo();
    editAction.setType("edit");
    editAction.setUrl("/app/edit/Account");
    
    SuspendedActionInfo deleteAction = new SuspendedActionInfo();
    deleteAction.setType("delete");
    deleteAction.setUrl("/app/delete/Account");
    
    List<SuspendedActionInfo> actions = Lists.newArrayList(createAction, editAction, deleteAction);
    
    // 设置完整配置
    suspendedComponentInfo.setType("floating_action_button");
    suspendedComponentInfo.setActions(actions);
    
    // 验证复杂配置
    assertEquals("floating_action_button", suspendedComponentInfo.getType(), "类型应正确");
    assertEquals(3, suspendedComponentInfo.getActions().size(), "应有3个操作");
    
    List<SuspendedActionInfo> retrievedActions = suspendedComponentInfo.getActions();
    assertEquals("new_object", retrievedActions.get(0).getType(), "第一个操作应为new_object");
    assertEquals("edit", retrievedActions.get(1).getType(), "第二个操作应为edit");
    assertEquals("delete", retrievedActions.get(2).getType(), "第三个操作应为delete");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试多种组件类型
   */
  @Test
  @DisplayName("正常场景 - 测试多种组件类型")
  void testVariousComponentTypes() {
    String[] componentTypes = {
        "floating_action_button", "suspended_menu", "quick_action", 
        "context_menu", "toolbar", "sidebar_action"
    };
    
    for (String componentType : componentTypes) {
      suspendedComponentInfo.setType(componentType);
      assertEquals(componentType, suspendedComponentInfo.getType(), 
                   "应正确设置组件类型: " + componentType);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试国际化属性的多种配置
   */
  @Test
  @DisplayName("正常场景 - 测试国际化属性多种配置")
  void testVariousI18NConfigurations() {
    // 创建包含多种国际化属性的配置
    Map<String, Object> map = Maps.newHashMap();
    Map<String, Object> i18nProps = Maps.newHashMap();
    i18nProps.put("title", "悬浮操作");
    i18nProps.put("description", "快速操作按钮");
    i18nProps.put("tooltip", "点击查看更多操作");
    i18nProps.put("confirm_message", "确认执行此操作？");
    map.put(ISuspendedComponentInfo.I18N_PROPS, i18nProps);
    
    SuspendedComponentInfo info = new SuspendedComponentInfo(map);
    Map<String, Object> retrievedProps = info.getI18NProps();
    
    assertEquals(4, retrievedProps.size(), "应有4个国际化属性");
    assertEquals("悬浮操作", retrievedProps.get("title"), "标题应正确");
    assertEquals("快速操作按钮", retrievedProps.get("description"), "描述应正确");
    assertEquals("点击查看更多操作", retrievedProps.get("tooltip"), "提示应正确");
    assertEquals("确认执行此操作？", retrievedProps.get("confirm_message"), "确认消息应正确");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试特殊字符处理
   */
  @Test
  @DisplayName("边界场景 - 测试特殊字符处理")
  void testSpecialCharacters() {
    // 测试包含特殊字符的类型
    String specialType = "suspended_action_@#$%_component";
    suspendedComponentInfo.setType(specialType);
    assertEquals(specialType, suspendedComponentInfo.getType(), "应正确处理特殊字符类型");
    
    // 测试包含特殊字符的国际化属性
    Map<String, Object> map = Maps.newHashMap();
    Map<String, Object> i18nProps = Maps.newHashMap();
    i18nProps.put("title", "标题<>&\"'测试");
    i18nProps.put("message", "消息包含特殊字符：@#$%^&*()");
    map.put(ISuspendedComponentInfo.I18N_PROPS, i18nProps);
    
    SuspendedComponentInfo info = new SuspendedComponentInfo(map);
    Map<String, Object> retrievedProps = info.getI18NProps();
    
    assertEquals("标题<>&\"'测试", retrievedProps.get("title"), "应正确处理特殊字符标题");
    assertEquals("消息包含特殊字符：@#$%^&*()", retrievedProps.get("message"), "应正确处理特殊字符消息");
  }
}
