package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.annotation.JSONField;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.timezone.annotation.ObjectDataField;
import com.facishare.paas.appframework.metadata.restdriver.BaseObjectDataConverter;
import com.facishare.paas.appframework.metadata.restdriver.ObjectDataConverter;
import com.facishare.paas.appframework.metadata.restdriver.ObjectDataConverterManager;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Created by luxin on 2018/4/8.
 */
public class StandardSimpleDetailController extends PreDefineController<StandardSimpleDetailController.Arg, StandardSimpleDetailController.Result> {

    protected IObjectDescribe describe;
    protected IObjectData data;

    @Override
    protected void before(StandardSimpleDetailController.Arg arg) {
        super.before(arg);
        //获取对象描述
        describe = findObject(arg);
        //查询object data
        data = findObjectData(arg);
        //检查数据权限
        doDataPrivilegeCheck(arg);
        filterDataObjectByFieldPrivilege();
    }


    @Override
    protected Result doService(Arg arg) {
        return new Result(ObjectDataDocument.of(data));
    }


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardController.DescribeLayout.getFuncPrivilegeCodes();
    }


    protected IObjectDescribe findObject(StandardSimpleDetailController.Arg arg) {
        return serviceFacade.findObject(controllerContext.getTenantId(), arg.getObjectDescribeApiName());
    }


    protected IObjectData findObjectData(StandardSimpleDetailController.Arg arg) {
        if (arg.isFromRecycleBin()) {
            //回收站查询已作废数据的详情
            return serviceFacade.findObjectDataIncludeDeleted(controllerContext.getUser(), arg.getObjectDataId(),
                    arg.getObjectDescribeApiName());
        }
        return serviceFacade.findObjectData(controllerContext.getUser(), arg.getObjectDataId(), describe);
    }


    protected void doDataPrivilegeCheck(StandardSimpleDetailController.Arg arg) {
        serviceFacade.doDataPrivilegeCheck(controllerContext.getUser(),
                Lists.newArrayList(data),
                describe,ObjectAction.VIEW_DETAIL.getActionCode());
    }


    /**
     * data过滤字段权限
     */
    protected void filterDataObjectByFieldPrivilege() {
        Map<String, Integer> fieldPrivilege = serviceFacade.getUserFieldPrivilege(controllerContext.getUser(), describe.getApiName());

        if (CollectionUtils.notEmpty(fieldPrivilege)) {
            fieldPrivilege.forEach((fieldName, status) -> {
                if (data.get(fieldName) != null && status == 0) {
                    data.set(fieldName, null);
                }
            });
        }
    }


    @Data
    public static class Arg {
        @JSONField(name = "M1")
        private String objectDataId;

        //兼容数据格式
        @JSONField(name = "M2")
        private String objectDescribeApiName;

        //是否回收站的详情页请求
        private boolean isFromRecycleBin;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        @JSONField(name = "M1")
        private ObjectDataDocument data;
    }
}
