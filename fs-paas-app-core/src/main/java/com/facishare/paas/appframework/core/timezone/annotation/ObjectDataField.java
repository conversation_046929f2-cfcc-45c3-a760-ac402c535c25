package com.facishare.paas.appframework.core.timezone.annotation;

import java.lang.annotation.*;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/06/15
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ObjectDataField {
    Type value() default Type.SINGLE;

    enum Type {
        SINGLE,
        LIST,
        LIST_MAP,
        MAP_MAP,
        ;
    }
}
