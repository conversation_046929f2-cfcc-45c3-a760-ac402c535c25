package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.facishare.crm.openapi.Utils.OUTBOUND_DELIVER_NOTE_PRODUCT_API_NAME;

/**
 * 出库单产品
 * create by z<PERSON><PERSON> on 2019/03/27
 */
@Component
public class OutboundDeliveryNoteProductObjectImportProvider extends DefaultObjectImportProvider {
    @Override
    public String getObjectCode() {
        return OUTBOUND_DELIVER_NOTE_PRODUCT_API_NAME;
    }

    @Override
    public Optional<ImportObject> getImportObject(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return Optional.empty();
    }
}
