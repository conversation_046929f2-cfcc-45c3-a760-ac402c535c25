package com.facishare.paas.appframework.metadata.publicobject;

import com.facishare.paas.appframework.common.service.OuterOrganizationService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.OrderByExt;
import com.facishare.paas.appframework.metadata.publicobject.module.*;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.MtPublicObjectJob;
import com.facishare.paas.appframework.metadata.repository.model.MtPublicObjectJobDetail;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IDistributedLockService;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.uc.api.model.enterprise.arg.GetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.google.common.base.Converter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2023/12/15
 */
@Slf4j
@Service
public class PublicObjectJobServiceImpl implements PublicObjectJobService {
    @Autowired
    private IRepository<MtPublicObjectJob> publicObjectJobRepository;
    @Autowired
    private IRepository<MtPublicObjectJobDetail> publicObjectJobDetailRepository;
    @Autowired
    private IDistributedLockService distributedLockService;
    @Autowired
    private OuterOrganizationService outerOrganizationService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;

    @Override
    public String createPublicObjectJob(User user, PublicObjectJobInfo publicObjectJobInfo) {
        MtPublicObjectJob publicObjectJob = PublicObjectJobConverter.getInstance().convert(publicObjectJobInfo);
        MtPublicObjectJob job = publicObjectJobRepository.create(user, publicObjectJob);
        return job.getId();
    }

    @Override
    public PublicObjectJobInfo queryPublicObjectJobById(User user, String objectApiName, String jobId) {
        Query query = buildPublicObjectJobSearchQuery(user, objectApiName, jobId, null);
        return findFirstPublicObjectJob(user, query).orElse(null);
    }

    @Override
    public Optional<PublicObjectJobInfo> queryPublicObjectJobByType(User user, String objectApiName, Collection<PublicObjectJobType> jobTypes) {
        Query query = buildPublicObjectJobSearchQuery(user, objectApiName, null, jobTypes);
        return findFirstPublicObjectJob(user, query);
    }

    @Override
    @Transactional
    public String updatePublicObjectJobStatus(User user, String objectApiName, String jobId, PublicObjectJobStatus jobStatus) {
        //加锁
        lockByObjectApiName(user, objectApiName);
        PublicObjectJobInfo jobInfo = queryPublicObjectJobById(user, objectApiName, jobId);
        jobInfo.setJobStatus(jobStatus);
        MtPublicObjectJob mtPublicObjectJob = PublicObjectJobConverter.getInstance().convert(jobInfo);
        publicObjectJobRepository.update(user, mtPublicObjectJob);
        return mtPublicObjectJob.getId();
    }

    @Override
    @Transactional
    public void updatePublicObjectJobDisplayStatus(User user, String objectApiName, String jobId, String displayStatus) {
        lockByObjectApiName(user, objectApiName);
        PublicObjectJobInfo jobInfo = queryPublicObjectJobById(user, objectApiName, jobId);
        if (Objects.isNull(jobInfo)
                || (jobInfo.getJobType() != PublicObjectJobType.OPEN_JOB && jobInfo.getJobType() != PublicObjectJobType.VERIFY_JOB)
                || !jobInfo.getJobStatus().isFinalState()) {
            return;
        }
        jobInfo.getJobResult().setDisplayStatus(displayStatus);
        MtPublicObjectJob mtPublicObjectJob = PublicObjectJobConverter.getInstance().convert(jobInfo);
        publicObjectJobRepository.update(user, mtPublicObjectJob);
    }

    private void lockByObjectApiName(User user, String objectApiName) {
        String lockKey = String.format("public_object_job_%s", objectApiName);
        distributedLockService.advisoryTransactionalLock(user.getTenantId(), lockKey);
    }

    private Query buildPublicObjectJobSearchQuery(User user, String objectApiName, String jobId, Collection<PublicObjectJobType> jobTypes) {
        List<IFilter> filters = Lists.newArrayList();
        filters.add(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter());
        filters.add(FilterExt.of(Operator.EQ, MtPublicObjectJob.OBJECT_API_NAME, objectApiName).getFilter());
        if (!Strings.isNullOrEmpty(jobId)) {
            filters.add(FilterExt.of(Operator.EQ, IObjectData.ID, jobId).getFilter());
        }
        if (CollectionUtils.notEmpty(jobTypes)) {
            List<String> values = jobTypes.stream().map(PublicObjectJobType::getType).collect(Collectors.toList());
            filters.add(FilterExt.of(Operator.IN, MtPublicObjectJob.JOB_TYPE, values).getFilter());
        }
        SearchQuery searchQuery = SearchQueryImpl.filters(filters);
        return Query.builder()
                .searchQuery(searchQuery)
                .build();
    }

    private Optional<PublicObjectJobInfo> findFirstPublicObjectJob(User user, Query query) {
        query.setLimit(1);
        query.setOffset(0);
        query.resetOrders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, false)));
        query.setNeedReturnCountNum(false);
        List<MtPublicObjectJob> mtPublicObjectJobList = publicObjectJobRepository.findBy(user, query, MtPublicObjectJob.class);
        if (CollectionUtils.empty(mtPublicObjectJobList)) {
            return Optional.empty();
        }
        PublicObjectJobInfo jobInfo = PublicObjectJobConverter.getInstance().reverse()
                .convert(mtPublicObjectJobList.get(0));
        return Optional.ofNullable(jobInfo);
    }

    @Override
    public List<PublicObjectJobDetailInfo> queryPublicObjectJobDetail(User user, String jobId, PublicObjectJobStatus jobStatus) {
        Query query = buildQuery(user, jobId, jobStatus);

        List<MtPublicObjectJobDetail> publicObjectJobDetailList = Lists.newArrayList();
        publicObjectJobDetailRepository.queryDataAndHandle(user, query, 100, 100, MtPublicObjectJobDetail.class, publicObjectJobDetailList::addAll);
        if (CollectionUtils.empty(publicObjectJobDetailList)) {
            return Lists.newArrayList();
        }
        Converter<MtPublicObjectJobDetail, PublicObjectJobDetailInfo> instance = PublicObjectJobDetailConverter.getInstance().reverse();
        return publicObjectJobDetailList.stream()
                .map(instance::convert)
                .collect(Collectors.toList());
    }

    private Query buildQuery(User user, String jobId, PublicObjectJobStatus jobStatus) {
        List<IFilter> filters = Lists.newArrayList();
        filters.add(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter());
        filters.add(FilterExt.of(Operator.EQ, MtPublicObjectJobDetail.JOB_ID, jobId).getFilter());
        if (Objects.nonNull(jobStatus) && PublicObjectJobStatus.UNKNOWN != jobStatus) {
            filters.add(FilterExt.of(Operator.EQ, MtPublicObjectJobDetail.JOB_STATUS_API_NAME, jobStatus.getStatus()).getFilter());
        }
        SearchQuery searchQuery = SearchQueryImpl.filters(filters);
        return Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, true)))
                .needReturnCountNum(false)
                .build();
    }

    @Override
    public Integer queryPublicObjectJobDetailCount(User user, String jobId, PublicObjectJobStatus jobStatus) {
        Query query = buildQuery(user, jobId, jobStatus);
        return publicObjectJobDetailRepository.findCountOnly(user, query, MtPublicObjectJobDetail.class);
    }

    @Override
    @Transactional
    public void initPublicObjectJobDetail(User user, String jobId, String objectApiName, PublicObjectJobStatus jobStatus, String downstreamTenantId) {
        // 加锁
        lockByObjectApiName(user, objectApiName);
        PublicObjectJobInfo jobInfo = queryPublicObjectJobById(user, objectApiName, jobId);
        if (Objects.isNull(jobInfo)) {
            return;
        }
        Map<String, String> ei2OuterTenantIdMap = outerOrganizationService.batchGetOuterTenantIdByEI(user, Lists.newArrayList(downstreamTenantId));
        String outerTenantId = ei2OuterTenantIdMap.get(downstreamTenantId);
        if (Strings.isNullOrEmpty(outerTenantId)) {
            return;
        }
        MtPublicObjectJobDetail jobDetail = findPublicObjectJobDetail(user, objectApiName, outerTenantId)
                .orElseGet(() -> {
                    MtPublicObjectJobDetail objectJobDetail = new MtPublicObjectJobDetail();
                    objectJobDetail.setTenantId(user.getTenantId());
                    objectJobDetail.setObjectApiName(objectApiName);
                    objectJobDetail.setEnterpriseRelationId(outerTenantId);
                    objectJobDetail.setDownstreamTenantId(downstreamTenantId);
                    return objectJobDetail;
                });
        verifyBeforeInit(user, jobId, objectApiName, downstreamTenantId, jobDetail);

        jobDetail.setJobId(jobId);
        jobDetail.setType(jobInfo.getType());
        jobDetail.setStatus(jobStatus.getStatus());
        jobDetail.setResult(null);
        publicObjectJobDetailRepository.bulkUpsert(user, Lists.newArrayList(jobDetail));
    }

    private void verifyBeforeInit(User user, String jobId, String objectApiName, String downstreamTenantId, MtPublicObjectJobDetail jobDetail) {
        if (Strings.isNullOrEmpty(jobDetail.getId())) {
            return;
        }
        PublicObjectJobStatus jobStatus = PublicObjectJobStatus.fromStatus(jobDetail.getStatus());
        if (jobStatus.isFinalState()) {
            return;
        }
        PublicObjectJobType jobType = PublicObjectJobType.fromType(jobDetail.getType());
        log.warn("initPublicObjectJobDetail fail! ei:{}, dTenantId:{}, jobId:{}, objectApiName:{}, jobType:{}, jobStatus:{}",
                user.getTenantId(), downstreamTenantId, jobId, objectApiName, jobType.getType(), jobStatus.getStatus());
        String enterpriseName = getEnterpriseName(downstreamTenantId);
        throw new ValidateException(I18NExt.text(I18NKey.HAVE_UNFINISHED_JOB, enterpriseName));
    }

    private String getEnterpriseName(String tenantId) {
        GetSimpleEnterpriseDataArg arg = new GetSimpleEnterpriseDataArg();
        arg.setEnterpriseId(Integer.parseInt(tenantId));
        GetSimpleEnterpriseDataResult result = enterpriseEditionService.getSimpleEnterpriseData(arg);
        SimpleEnterpriseData simpleEnterpriseData = result.getEnterpriseData();
        return simpleEnterpriseData.getEnterpriseName();
    }

    @Override
    @Transactional
    public void updatePublicObjectDetailJobStatus(User user, String objectApiName, String jobId, PublicObjectJobStatus jobStatus, User downstreamUser) {
        // 加锁
        lockByObjectApiName(user, objectApiName);

        Query query = buildQueryDetail(user, objectApiName, jobId, downstreamUser.getTenantId());

        List<MtPublicObjectJobDetail> jobDetailList = publicObjectJobDetailRepository.findBy(user, query, MtPublicObjectJobDetail.class);
        if (CollectionUtils.empty(jobDetailList)) {
            return;
        }
        MtPublicObjectJobDetail jobDetail = jobDetailList.get(0);
        jobDetail.setStatus(jobStatus.getStatus());
        User updateUser = new User(user.getTenantId(), downstreamUser.getUserId());
        publicObjectJobDetailRepository.bulkUpdate(updateUser, Lists.newArrayList(jobDetail));
    }

    private Query buildQueryDetail(User user, String objectApiName, String jobId, String downstreamTenantId) {
        List<IFilter> filters = Lists.newArrayList();
        filters.add(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter());
        filters.add(FilterExt.of(Operator.EQ, MtPublicObjectJobDetail.JOB_ID, jobId).getFilter());
        filters.add(FilterExt.of(Operator.EQ, MtPublicObjectJobDetail.OBJECT_API_NAME, objectApiName).getFilter());
        filters.add(FilterExt.of(Operator.EQ, MtPublicObjectJobDetail.DOWNSTREAM_TENANT_ID, downstreamTenantId).getFilter());
        SearchQuery searchQuery = SearchQueryImpl.filters(filters);
        return Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, true)))
                .needReturnCountNum(false)
                .limit(1)
                .build();
    }

    @Override
    public Optional<PublicObjectJobDetailInfo> queryPublicObjectJobDetailByDownstreamTenantId(User user, String objectApiName,
                                                                                              String jobId, String downstreamTenantId) {
        Query query = buildQueryDetail(user, objectApiName, jobId, downstreamTenantId);
        List<MtPublicObjectJobDetail> jobDetailList = publicObjectJobDetailRepository.findBy(user, query, MtPublicObjectJobDetail.class);
        Converter<MtPublicObjectJobDetail, PublicObjectJobDetailInfo> instance = PublicObjectJobDetailConverter.getInstance().reverse();
        return jobDetailList.stream()
                .map(instance::convert)
                .findFirst();
    }

    private Optional<MtPublicObjectJobDetail> findPublicObjectJobDetail(User user, String objectApiName, String outerTenantId) {
        Query query = buildQuery(user, objectApiName, Sets.newHashSet(outerTenantId), 1);
        List<MtPublicObjectJobDetail> jobDetailList = publicObjectJobDetailRepository.findBy(user, query, MtPublicObjectJobDetail.class);
        if (CollectionUtils.empty(jobDetailList)) {
            return Optional.empty();
        }
        return Optional.of(jobDetailList.get(0));
    }

    private Query buildQuery(User user, String objectApiName, Set<String> outerTenantIds, int limit) {
        List<IFilter> filters = Lists.newArrayList();
        filters.add(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter());
        filters.add(FilterExt.of(Operator.EQ, MtPublicObjectJobDetail.OBJECT_API_NAME, objectApiName).getFilter());
        filters.add(FilterExt.of(Operator.IN, MtPublicObjectJobDetail.ENTERPRISE_RELATION_ID_API_NAME, Lists.newArrayList(outerTenantIds)).getFilter());
        SearchQuery searchQuery = SearchQueryImpl.filters(filters);
        return Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, true)))
                .needReturnCountNum(false)
                .limit(limit)
                .build();
    }

}
