package com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.enums;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> create by liy on 2024/6/13
 */
@Getter
@AllArgsConstructor
public enum PackagePluginDefineType {
    SYSTEM("system", "预制"),
    SYSTEM_PERSONAL("system_personal", "预制个人"),
    CUSTOM("custom", "自定义"),
    ;

    private final String type;
    private final String name;
    private final static Map<String, PackagePluginDefineType> TYPE_MAP = Maps.newHashMap();

    static {
        for (PackagePluginDefineType item : PackagePluginDefineType.values()) {
            TYPE_MAP.put(item.getType(), item);
        }
    }

    /**
     * 获取枚举
     */
    public static PackagePluginDefineType of(String type) {
        return TYPE_MAP.get(type);
    }

    /**
     * 检查是否在枚举范围内
     */
    public static boolean in(String type) {
        return Objects.nonNull(TYPE_MAP.get(type));
    }
}
