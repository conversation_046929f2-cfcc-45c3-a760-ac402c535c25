package com.facishare.paas.appframework.metadata.onlinedoc;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.repository.model.PackagePluginEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> create by liy on 2024/6/5
 */
public interface PackagePluginLogicService {

    //创建插件
    PackagePluginEntity createPlugin(User user, PackagePluginEntity entity);

    //更新插件
    PackagePluginEntity updatePlugin(User user, PackagePluginEntity entity, List<String> fields);

    //删除插件
    PackagePluginEntity deletePlugin(User user, PackagePluginEntity entity);

    //查找插件
    PackagePluginEntity findPluginByApiName(User user, String pluginApiName);

    //分页获取所有插件
    List<PackagePluginEntity> getPluginList(User user, String appType, int pageNumber, int pageSize);

    //获取可使用的插件
    List<PackagePluginEntity> getUsingPluginList(User user, String appType, int pageNumber, int pageSize);

    //获取插件
    List<PackagePluginEntity> getPluginsByApiNames(User user, List<String> apiNames);

    //获取插件
    List<PackagePluginEntity> getPluginsByAppTypes(User user, List<String> appTypes);

    //清理个人授权记录
    void notifyRemovePersonalAuth(User user, String appType, String pluginApiName);

    //尝试获取企业认证+runtimeData；比如wps团队信息
    @Deprecated
    Map<String, Object> tryEnterpriseAuth(User user, String pluginApiName, String functionApiName, Map<String, String> devInfo);
}
