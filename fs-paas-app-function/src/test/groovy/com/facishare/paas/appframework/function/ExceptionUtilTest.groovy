package com.facishare.paas.appframework.function

import spock.lang.Specification
import spock.lang.Unroll

/**
 * ExceptionUtil单元测试类
 */
class ExceptionUtilTest extends Specification {

    /**
     * GenerateByAI
     * 测试内容描述：测试格式化错误信息方法，正常场景
     */
    def "formatErrorTest"() {
        given: "准备多行错误信息"
        def errorMessage = "第一行错误\n第二行错误\n第三行错误\n第四行错误"
        
        when: "格式化错误信息，限制行数为2"
        def result = ExceptionUtil.formatError(errorMessage, 2)
        
        then: "验证结果只包含前两行"
        compareLines(result, "第一行错误\n第二行错误")
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试格式化错误信息方法，错误输入场景
     */
    def "formatErrorTestWithNullInput"() {
        when: "输入null作为错误信息"
        def result = ExceptionUtil.formatError(null, 5)
        
        then: "返回null"
        result == null
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试格式化错误信息方法，行数限制边界值测试
     */
    @Unroll
    def "formatErrorTestWithLineNumber_#scenario"() {
        given: "准备错误信息"
        def errorMessage = "第一行错误\n第二行错误\n第三行错误"
        
        when: "使用不同行数限制格式化错误信息"
        def result = ExceptionUtil.formatError(errorMessage, lineNumber)
        
        then: "验证结果，按行比较"
        compareLines(result, expected)
        
        where:
        scenario      | lineNumber | expected
        "零行限制"     | 0          | ""
        "一行限制"     | 1          | "第一行错误"
        "足够行限制"   | 3          | "第一行错误\n第二行错误\n第三行错误"
        "超出行限制"   | 5          | "第一行错误\n第二行错误\n第三行错误"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试格式化错误信息方法，空字符串输入
     */
    def "formatErrorTestWithEmptyString"() {
        given: "准备空字符串"
        def errorMessage = ""
        
        when: "格式化空字符串"
        def result = ExceptionUtil.formatError(errorMessage, 3)
        
        then: "返回空字符串"
        result == ""
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试格式化错误信息方法，单行输入
     */
    def "formatErrorTestWithSingleLine"() {
        given: "准备单行字符串"
        def errorMessage = "只有一行错误信息"
        
        when: "格式化单行字符串，限制多行"
        def result = ExceptionUtil.formatError(errorMessage, 3)
        
        then: "返回原字符串，仅比较内容"
        result.trim() == "只有一行错误信息"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试格式化错误信息方法，包含特殊字符
     */
    def "formatErrorTestWithSpecialCharacters"() {
        given: "准备包含特殊字符的错误信息"
        def errorMessage = "带有特殊字符\t的错误\n带有换行符\n的错误\n第三行错误"
        
        when: "格式化包含特殊字符的错误信息"
        def result = ExceptionUtil.formatError(errorMessage, 2)
        
        then: "正确处理特殊字符，使用按行比较方式"
        def lines = result.split("\n|\r\n|\r")
        lines.size() == 2
        lines[0].trim() == "带有特殊字符\t的错误"
        lines[1].trim() == "带有换行符"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试格式化错误信息方法，负数行限制
     */
    def "formatErrorTestWithNegativeLineNumber"() {
        given: "准备错误信息"
        def errorMessage = "第一行错误\n第二行错误\n第三行错误"
        
        when: "使用负数行限制"
        def result = ExceptionUtil.formatError(errorMessage, -1)
        
        then: "返回空字符串"
        result == ""
    }
    
    /**
     * 按行比较两个字符串，忽略换行符差异
     */
    private boolean compareLines(String actual, String expected) {
        if (actual == null && expected == null) return true
        if (actual == null || expected == null) return false
        
        def actualLines = actual.split("\n|\r\n|\r").collect { it.trim() }
        def expectedLines = expected.split("\n|\r\n|\r").collect { it.trim() }
        
        if (actualLines.size() != expectedLines.size()) return false
        
        for (int i = 0; i < actualLines.size(); i++) {
            if (actualLines[i] != expectedLines[i]) return false
        }
        
        return true
    }
} 