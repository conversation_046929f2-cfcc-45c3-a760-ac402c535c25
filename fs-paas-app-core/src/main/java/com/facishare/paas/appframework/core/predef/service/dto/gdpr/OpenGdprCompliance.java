package com.facishare.paas.appframework.core.predef.service.dto.gdpr;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface OpenGdprCompliance {

    @Data
    class Arg {
        private List<String> apiNames;
        private List<String> unusableOperation;
        private String forbidExport;
        private String forbidOpenApi;
        private Integer period;
        private String periodType;
    }

    @Data
    @Builder
    class Result {
        //private boolean success;
    }
}
