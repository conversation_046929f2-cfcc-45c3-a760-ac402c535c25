package com.facishare.paas.appframework.flow;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.flow.dto.*;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * FlowCommonServiceImpl单元测试类
 */
@ExtendWith(MockitoExtension.class)
class FlowCommonServiceImplTest {

  @Mock
  private FlowCommonProxy flowCommonProxy;

  @InjectMocks
  private FlowCommonServiceImpl flowCommonService;

  private User testUser;
  private Map<String, String> testHeaders;

  @BeforeEach
  void setUp() {
    testUser = User.builder()
        .tenantId("74255")
        .userId("1000")
        .build();
    testUser.setUserName("testUser");

    testHeaders = new HashMap<>();
    testHeaders.put("x-tenant-id", "74255");
    testHeaders.put("x-user-id", "1000");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试existAvailableDef方法正常场景，返回成功结果
   */
  @Test
  @DisplayName("正常场景 - existAvailableDef返回成功结果")
  void testExistAvailableDef_Success() {
    // 准备测试数据
    String describeApiName = "TestObj";
    String dataId = "test123";
    
    ExistAvailableDef.ExistAvailable expectedData = ExistAvailableDef.ExistAvailable.builder()
        .existAvailableBpmDef(true)
        .existAvailableStageDef(true)
        .build();
    
    ExistAvailableDef.Result mockResult = ExistAvailableDef.Result.builder()
        .code(0)
        .data(expectedData)
        .build();

    // 配置Mock行为
    try (MockedStatic<RestUtils> mockedRestUtils = mockStatic(RestUtils.class)) {
      mockedRestUtils.when(() -> RestUtils.buildHeaders(testUser)).thenReturn(new HashMap<>());
      when(flowCommonProxy.existAvailableDef(any(ExistAvailableDef.Arg.class), anyMap()))
          .thenReturn(mockResult);

      // 执行被测试方法
      ExistAvailableDef.ExistAvailable result = flowCommonService.existAvailableDef(testUser, describeApiName, dataId);

      // 验证结果
      assertNotNull(result);
      assertTrue(result.getExistAvailableBpmDef());
      assertTrue(result.getExistAvailableStageDef());

      // 验证Mock交互
      verify(flowCommonProxy).existAvailableDef(any(ExistAvailableDef.Arg.class), anyMap());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试existAvailableDef方法异常场景，代理调用失败
   */
  @Test
  @DisplayName("异常场景 - existAvailableDef代理调用失败")
  void testExistAvailableDef_ProxyFailure() {
    // 准备测试数据
    String describeApiName = "TestObj";
    String dataId = "test123";
    
    ExistAvailableDef.Result mockResult = ExistAvailableDef.Result.builder()
        .code(1)
        .message("调用失败")
        .build();

    // 配置Mock行为
    try (MockedStatic<RestUtils> mockedRestUtils = mockStatic(RestUtils.class)) {
      mockedRestUtils.when(() -> RestUtils.buildHeaders(testUser)).thenReturn(new HashMap<>());
      when(flowCommonProxy.existAvailableDef(any(ExistAvailableDef.Arg.class), anyMap()))
          .thenReturn(mockResult);

      // 执行被测试方法
      ExistAvailableDef.ExistAvailable result = flowCommonService.existAvailableDef(testUser, describeApiName, dataId);

      // 验证结果
      assertNotNull(result);
      assertFalse(result.getExistAvailableBpmDef());
      assertFalse(result.getExistAvailableStageDef());

      // 验证Mock交互
      verify(flowCommonProxy).existAvailableDef(any(ExistAvailableDef.Arg.class), anyMap());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试existAvailableDef方法异常场景，抛出运行时异常
   */
  @Test
  @DisplayName("异常场景 - existAvailableDef抛出运行时异常")
  void testExistAvailableDefThrowsRuntimeException() {
    // 准备测试数据
    String describeApiName = "TestObj";
    String dataId = "test123";

    // 配置Mock行为
    try (MockedStatic<RestUtils> mockedRestUtils = mockStatic(RestUtils.class)) {
      mockedRestUtils.when(() -> RestUtils.buildHeaders(testUser)).thenReturn(new HashMap<>());
      when(flowCommonProxy.existAvailableDef(any(ExistAvailableDef.Arg.class), anyMap()))
          .thenThrow(new RuntimeException("网络异常"));

      // 执行被测试方法
      ExistAvailableDef.ExistAvailable result = flowCommonService.existAvailableDef(testUser, describeApiName, dataId);

      // 验证结果
      assertNotNull(result);
      assertFalse(result.getExistAvailableBpmDef());
      assertFalse(result.getExistAvailableStageDef());

      // 验证Mock交互
      verify(flowCommonProxy).existAvailableDef(any(ExistAvailableDef.Arg.class), anyMap());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试executeFlow方法正常场景，同步执行成功
   */
  @Test
  @DisplayName("正常场景 - executeFlow同步执行成功")
  void testExecuteFlow_SyncSuccess() {
    // 准备测试数据
    OneFlowExecuteRequest request = new OneFlowExecuteRequest();
    request.setFlowApiName("testFlow");
    Map<String, Object> flowArgs = new HashMap<>();
    flowArgs.put("param1", "value1");
    request.setFlowArgs(flowArgs);
    request.setAsync(false);
    request.setTenantId(74255L);
    request.setUserId(1000L);

    TriggerFlow.FlowData flowData = new TriggerFlow.FlowData();
    flowData.setWorkflowInstanceId("instance123");
    Map<String, Object> currentTask = new HashMap<>();
    currentTask.put("state", "completed");
    flowData.setCurrentTask(currentTask);

    TriggerFlow.Result mockResult = new TriggerFlow.Result();
    mockResult.setCode(0);
    mockResult.setData(flowData);

    // 配置Mock行为
    try (MockedStatic<RestUtils> mockedRestUtils = mockStatic(RestUtils.class)) {
      mockedRestUtils.when(() -> RestUtils.buildHeaders(testUser)).thenReturn(new HashMap<>());
      when(flowCommonProxy.triggerFlow(any(TriggerFlow.Arg.class), anyMap()))
          .thenReturn(mockResult);

      // 执行被测试方法
      OneFlowExecuteResponse response = flowCommonService.executeFlow(testUser, request);

      // 验证结果
      assertNotNull(response);
      assertEquals("instance123", response.getWorkflowInstanceId());

      // 验证Mock交互
      verify(flowCommonProxy).triggerFlow(any(TriggerFlow.Arg.class), anyMap());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试executeFlow方法异常场景，流程执行失败
   */
  @Test
  @DisplayName("异常场景 - executeFlow流程执行失败")
  void testExecuteFlowThrowsValidateException_FlowError() {
    // 准备测试数据
    OneFlowExecuteRequest request = new OneFlowExecuteRequest();
    request.setFlowApiName("testFlow");
    Map<String, Object> flowArgs2 = new HashMap<>();
    flowArgs2.put("param1", "value1");
    request.setFlowArgs(flowArgs2);
    request.setAsync(false);
    request.setTenantId(74255L);
    request.setUserId(1000L);

    TriggerFlow.FlowData flowData = new TriggerFlow.FlowData();
    flowData.setWorkflowInstanceId("instance123");
    Map<String, Object> currentTask2 = new HashMap<>();
    currentTask2.put("state", "error");
    flowData.setCurrentTask(currentTask2);

    TriggerFlow.Result mockResult = new TriggerFlow.Result();
    mockResult.setCode(0);
    mockResult.setData(flowData);

    // 配置Mock行为
    try (MockedStatic<RestUtils> mockedRestUtils = mockStatic(RestUtils.class);
         MockedStatic<I18NExt> mockedI18NExt = mockStatic(I18NExt.class);
         MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class)) {
      
      mockedRestUtils.when(() -> RestUtils.buildHeaders(testUser)).thenReturn(new HashMap<>());
      mockedI18NExt.when(() -> I18NExt.text(I18NKey.ONE_FLOW_FAIL)).thenReturn("OneFlow执行失败");
      Map<String, Object> errorTask = new HashMap<>();
      errorTask.put("state", "error");
      mockedCollectionUtils.when(() -> CollectionUtils.nullToEmpty(any(Map.class))).thenReturn(errorTask);
      
      when(flowCommonProxy.triggerFlow(any(TriggerFlow.Arg.class), anyMap()))
          .thenReturn(mockResult);

      // 执行并验证异常
      ValidateException exception = assertThrows(ValidateException.class, () -> {
        flowCommonService.executeFlow(testUser, request);
      });

      // 验证异常信息
      assertTrue(exception.getMessage().contains("OneFlow执行失败"));

      // 验证Mock交互
      verify(flowCommonProxy).triggerFlow(any(TriggerFlow.Arg.class), anyMap());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试executeFlow方法异常场景，代理返回错误码
   */
  @Test
  @DisplayName("异常场景 - executeFlow代理返回错误码")
  void testExecuteFlowThrowsValidateException_ProxyError() {
    // 准备测试数据
    OneFlowExecuteRequest request = new OneFlowExecuteRequest();
    request.setFlowApiName("testFlow");
    Map<String, Object> flowArgs3 = new HashMap<>();
    flowArgs3.put("param1", "value1");
    request.setFlowArgs(flowArgs3);
    request.setAsync(false);
    request.setTenantId(74255L);
    request.setUserId(1000L);

    TriggerFlow.Result mockResult = new TriggerFlow.Result();
    mockResult.setCode(1);
    mockResult.setMessage("代理调用失败");

    // 配置Mock行为
    try (MockedStatic<RestUtils> mockedRestUtils = mockStatic(RestUtils.class)) {
      mockedRestUtils.when(() -> RestUtils.buildHeaders(testUser)).thenReturn(new HashMap<>());
      when(flowCommonProxy.triggerFlow(any(TriggerFlow.Arg.class), anyMap()))
          .thenReturn(mockResult);

      // 执行并验证异常
      ValidateException exception = assertThrows(ValidateException.class, () -> {
        flowCommonService.executeFlow(testUser, request);
      });

      // 验证异常信息
      assertTrue(exception.getMessage().contains("代理调用失败"));

      // 验证Mock交互
      verify(flowCommonProxy).triggerFlow(any(TriggerFlow.Arg.class), anyMap());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试queryOneFlowDefinitionSimpleList方法正常场景
   */
  @Test
  @DisplayName("正常场景 - queryOneFlowDefinitionSimpleList返回成功结果")
  void testQueryOneFlowDefinitionSimpleList_Success() {
    // 准备测试数据
    List<String> sourceWorkflowIds = Arrays.asList("flow1", "flow2");
    
    QueryOneFlowDefinition.SimpleDefinitionData expectedData = QueryOneFlowDefinition.SimpleDefinitionData.builder()
        .simpleDefinitions(Arrays.asList(
            QueryOneFlowDefinition.OneFlowDefinition.builder().sourceWorkflowId("flow1").build(),
            QueryOneFlowDefinition.OneFlowDefinition.builder().sourceWorkflowId("flow2").build()
        ))
        .build();
    
    QueryOneFlowDefinition.Result mockResult = QueryOneFlowDefinition.Result.builder()
        .code(0)
        .data(expectedData)
        .build();

    // 配置Mock行为
    try (MockedStatic<RestUtils> mockedRestUtils = mockStatic(RestUtils.class);
         MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class)) {
      
      mockedRestUtils.when(() -> RestUtils.buildHeaders(testUser)).thenReturn(new HashMap<>());
      mockedCollectionUtils.when(() -> CollectionUtils.notEmpty(sourceWorkflowIds)).thenReturn(true);
      
      when(flowCommonProxy.queryOneFlowDefinitionSimpleList(any(QueryOneFlowDefinition.Arg.class), anyMap()))
          .thenReturn(mockResult);

      // 执行被测试方法
      QueryOneFlowDefinition.SimpleDefinitionData result = flowCommonService.queryOneFlowDefinitionSimpleList(testUser, sourceWorkflowIds);

      // 验证结果
      assertNotNull(result);
      assertEquals(2, result.getSimpleDefinitions().size());

      // 验证Mock交互
      verify(flowCommonProxy).queryOneFlowDefinitionSimpleList(any(QueryOneFlowDefinition.Arg.class), anyMap());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试queryOneFlowDefinitionSimpleList方法空列表场景
   */
  @Test
  @DisplayName("边界场景 - queryOneFlowDefinitionSimpleList空列表输入")
  void testQueryOneFlowDefinitionSimpleList_EmptyList() {
    // 准备测试数据
    List<String> sourceWorkflowIds = Collections.emptyList();

    // 配置Mock行为
    try (MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class)) {
      mockedCollectionUtils.when(() -> CollectionUtils.notEmpty(sourceWorkflowIds)).thenReturn(false);

      // 执行被测试方法
      QueryOneFlowDefinition.SimpleDefinitionData result = flowCommonService.queryOneFlowDefinitionSimpleList(testUser, sourceWorkflowIds);

      // 验证结果
      assertNotNull(result);
      assertTrue(result.getSimpleDefinitions().isEmpty());

      // 验证Mock交互
      verify(flowCommonProxy, never()).queryOneFlowDefinitionSimpleList(any(), any());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试queryOneFlowDefinitionSimpleList方法异常场景
   */
  @Test
  @DisplayName("异常场景 - queryOneFlowDefinitionSimpleList抛出异常")
  void testQueryOneFlowDefinitionSimpleList_Exception() {
    // 准备测试数据
    List<String> sourceWorkflowIds = Arrays.asList("flow1", "flow2");

    // 配置Mock行为
    try (MockedStatic<RestUtils> mockedRestUtils = mockStatic(RestUtils.class);
         MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class)) {
      
      mockedRestUtils.when(() -> RestUtils.buildHeaders(testUser)).thenReturn(new HashMap<>());
      mockedCollectionUtils.when(() -> CollectionUtils.notEmpty(sourceWorkflowIds)).thenReturn(true);
      
      when(flowCommonProxy.queryOneFlowDefinitionSimpleList(any(QueryOneFlowDefinition.Arg.class), anyMap()))
          .thenThrow(new RuntimeException("网络异常"));

      // 执行被测试方法
      QueryOneFlowDefinition.SimpleDefinitionData result = flowCommonService.queryOneFlowDefinitionSimpleList(testUser, sourceWorkflowIds);

      // 验证结果
      assertNotNull(result);
      assertTrue(result.getSimpleDefinitions().isEmpty());

      // 验证Mock交互
      verify(flowCommonProxy).queryOneFlowDefinitionSimpleList(any(QueryOneFlowDefinition.Arg.class), anyMap());
    }
  }
}
