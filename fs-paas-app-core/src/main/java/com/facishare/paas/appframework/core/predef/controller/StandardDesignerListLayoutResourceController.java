package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.metadata.ButtonUsePageType;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.facishare.paas.metadata.ui.layout.IButton;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.SceneLogicService.IS_OUTER;

/**
 * create by z<PERSON><PERSON> on 2021/09/28
 */
public class StandardDesignerListLayoutResourceController extends AbstractDesignerListLayoutResourceController<StandardDesignerListLayoutResourceController.Arg> {

    @Override
    protected List<IButton> getSingleButtons() {
        List<IButton> buttons = findButtonsByUsePage(ButtonUsePageType.DataList);
        return buttons;
    }

    @Override
    protected List<IButton> getNormalButtons() {
        List<IButton> buttons;
        if (arg.isForSite()) {
            buttons = findButtonsByUsePage(ButtonUsePageType.ListComponent);
        } else {
            buttons = findButtonsByUsePage(ButtonUsePageType.ListNormal);
        }
        return buttons;
    }

    @Override
    protected List<IScene> findScenes() {
        if (arg.isForSite()) {
            controllerContext.setAttribute(IS_OUTER, true);
        } else {
            controllerContext.setAttribute(IS_OUTER, false);
        }
        List<IScene> scenes = infraServiceFacade.findScenes(controllerContext.getObjectApiName(), controllerContext.getUser(), null);
        controllerContext.setAttribute(IS_OUTER, false);
        return scenes.stream().filter(IScene::isActive).collect(Collectors.toList());
    }

    @Data
    public static class Arg {
        private String describeApiName;
        private String bizScene;

        public boolean isForSite() {
            return "site".equals(this.bizScene);
        }
    }
}
