package com.facishare.paas.appframework.log;

import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import org.springframework.stereotype.Component;

/**
 * @Desc
 * <AUTHOR>
 * @CreateTime 2019-08-14 15:01
 */
@Component("importCreateModifyRecordProvider")
public class ImportCreateModifyRecordProvider extends CreateModifyRecordProvider {
    @Override
    public String getApiName() {
        return ActionType.Import.getId();
    }

    @Override
    public ModifyRecord getModifyRecord(LogInfo logInfo) {
        return super.getModifyRecord(logInfo);
    }
}
