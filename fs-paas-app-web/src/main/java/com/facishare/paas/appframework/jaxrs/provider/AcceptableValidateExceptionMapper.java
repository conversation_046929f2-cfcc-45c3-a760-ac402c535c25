package com.facishare.paas.appframework.jaxrs.provider;

import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.jaxrs.model.InnerAPIResult;
import com.facishare.paas.appframework.jaxrs.model.RestAPIResult;
import com.facishare.rest.core.util.JsonUtil;
import org.springframework.stereotype.Component;

import javax.ws.rs.core.Response;
import javax.ws.rs.ext.Provider;

/**
 * create by z<PERSON><PERSON> on 2020/03/24
 */
@Provider
@Component
public class AcceptableValidateExceptionMapper extends AbstractExceptionMapper<AcceptableValidateException> {
    @Override
    protected void logError(AcceptableValidateException exception, RequestContext context) {
        log.warn("acceptableValidateException, ei:{}, result:{}", context.getTenantId(), JsonUtil.toJson(exception.getValidateResult()), exception);
    }

    @Override
    protected Response toCepResponse(AcceptableValidateException exception, RequestContext context) {
        return Response.status(exception.getStatus())
                .entity(exception.getValidateResult())
                .build();
    }

    @Override
    protected Response toInnerResponse(AcceptableValidateException exception, RequestContext context) {
        return Response.status(exception.getStatus())
                .entity(InnerAPIResult.success(exception.getValidateResult()))
                .build();
    }

    @Override
    protected Response toRestResponse(AcceptableValidateException exception, RequestContext context) {
        return Response.status(exception.getStatus())
                .entity(RestAPIResult.success(exception.getValidateResult()))
                .build();
    }

}
