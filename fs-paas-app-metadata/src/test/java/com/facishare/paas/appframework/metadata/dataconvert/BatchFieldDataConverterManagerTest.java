package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.metadata.api.describe.IFieldType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试BatchFieldDataConverterManager类的批量转换器管理功能
 */
@ExtendWith(MockitoExtension.class)
class BatchFieldDataConverterManagerTest {

  @Mock
  private ApplicationContext applicationContext;

  @Mock
  private BatchFieldDataConverterAdapter batchFieldDataConverterAdapter;

  @InjectMocks
  private BatchFieldDataConverterManager manager;

  private TestBatchFieldDataConverter testBatchConverter1;
  private TestBatchFieldDataConverter testBatchConverter2;

  @BeforeEach
  void setUp() {
    testBatchConverter1 = new TestBatchFieldDataConverter(Arrays.asList(IFieldType.EMPLOYEE, IFieldType.EMPLOYEE_MANY));
    testBatchConverter2 = new TestBatchFieldDataConverter(Arrays.asList(IFieldType.DEPARTMENT, IFieldType.DEPARTMENT_MANY));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setApplicationContext方法正常初始化批量转换器映射
   */
  @Test
  @DisplayName("测试setApplicationContext - 正常初始化批量转换器映射")
  void testSetApplicationContext_NormalInitialization() {
    // 准备测试数据
    Map<String, BatchFieldDataConverter> springMap = new HashMap<>();
    springMap.put("batchConverter1", testBatchConverter1);
    springMap.put("batchConverter2", testBatchConverter2);

    when(applicationContext.getBeansOfType(BatchFieldDataConverter.class)).thenReturn(springMap);

    // 执行被测试方法
    manager.setApplicationContext(applicationContext);

    // 验证结果
    assertEquals(testBatchConverter1, manager.getBatchFieldDataConverter(IFieldType.EMPLOYEE));
    assertEquals(testBatchConverter1, manager.getBatchFieldDataConverter(IFieldType.EMPLOYEE_MANY));
    assertEquals(testBatchConverter2, manager.getBatchFieldDataConverter(IFieldType.DEPARTMENT));
    assertEquals(testBatchConverter2, manager.getBatchFieldDataConverter(IFieldType.DEPARTMENT_MANY));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setApplicationContext方法处理空的转换器映射
   */
  @Test
  @DisplayName("测试setApplicationContext - 空的转换器映射")
  void testSetApplicationContext_EmptyConverterMap() {
    // 准备测试数据
    Map<String, BatchFieldDataConverter> emptyMap = new HashMap<>();
    when(applicationContext.getBeansOfType(BatchFieldDataConverter.class)).thenReturn(emptyMap);

    // 执行被测试方法
    manager.setApplicationContext(applicationContext);

    // 验证结果 - 应该返回默认的适配器
    assertEquals(batchFieldDataConverterAdapter, manager.getBatchFieldDataConverter(IFieldType.EMPLOYEE));
    assertEquals(batchFieldDataConverterAdapter, manager.getBatchFieldDataConverter(IFieldType.DEPARTMENT));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setApplicationContext方法处理null的转换器映射
   */
  @Test
  @DisplayName("测试setApplicationContext - null转换器映射")
  void testSetApplicationContext_NullConverterMap() {
    // 准备测试数据
    when(applicationContext.getBeansOfType(BatchFieldDataConverter.class)).thenReturn(null);

    // 执行被测试方法
    manager.setApplicationContext(applicationContext);

    // 验证结果 - 应该返回默认的适配器
    assertEquals(batchFieldDataConverterAdapter, manager.getBatchFieldDataConverter(IFieldType.EMPLOYEE));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setApplicationContext方法处理BeansException异常
   */
  @Test
  @DisplayName("测试setApplicationContext - BeansException异常处理")
  void testSetApplicationContext_BeansException() {
    // 准备测试数据 - 抛出BeansException而不是RuntimeException
    when(applicationContext.getBeansOfType(BatchFieldDataConverter.class))
        .thenThrow(new org.springframework.beans.BeansException("Bean creation failed") {});

    // 执行被测试方法 - 不应该抛出异常
    assertDoesNotThrow(() -> manager.setApplicationContext(applicationContext));

    // 验证结果 - 应该返回默认的适配器
    assertEquals(batchFieldDataConverterAdapter, manager.getBatchFieldDataConverter(IFieldType.EMPLOYEE));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getBatchFieldDataConverter方法返回已注册的转换器
   */
  @Test
  @DisplayName("测试getBatchFieldDataConverter - 返回已注册的转换器")
  void testGetBatchFieldDataConverter_RegisteredConverter() {
    // 准备测试数据
    Map<String, BatchFieldDataConverter> springMap = new HashMap<>();
    springMap.put("batchConverter1", testBatchConverter1);
    when(applicationContext.getBeansOfType(BatchFieldDataConverter.class)).thenReturn(springMap);

    // 初始化管理器
    manager.setApplicationContext(applicationContext);

    // 执行被测试方法
    BatchFieldDataConverter result = manager.getBatchFieldDataConverter(IFieldType.EMPLOYEE);

    // 验证结果
    assertEquals(testBatchConverter1, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getBatchFieldDataConverter方法返回默认适配器
   */
  @Test
  @DisplayName("测试getBatchFieldDataConverter - 返回默认适配器")
  void testGetBatchFieldDataConverter_DefaultAdapter() {
    // 准备测试数据 - 不初始化任何转换器

    // 执行被测试方法
    BatchFieldDataConverter result = manager.getBatchFieldDataConverter("unknown_field_type");

    // 验证结果
    assertEquals(batchFieldDataConverterAdapter, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试批量转换器支持多个字段类型的映射
   */
  @Test
  @DisplayName("测试批量转换器支持多个字段类型的映射")
  void testMultipleFieldTypesMapping() {
    // 准备测试数据
    Map<String, BatchFieldDataConverter> springMap = new HashMap<>();
    springMap.put("multiBatchConverter", testBatchConverter1);
    when(applicationContext.getBeansOfType(BatchFieldDataConverter.class)).thenReturn(springMap);

    // 初始化管理器
    manager.setApplicationContext(applicationContext);

    // 验证结果 - 同一个转换器应该支持多个字段类型
    assertEquals(testBatchConverter1, manager.getBatchFieldDataConverter(IFieldType.EMPLOYEE));
    assertEquals(testBatchConverter1, manager.getBatchFieldDataConverter(IFieldType.EMPLOYEE_MANY));
    assertSame(manager.getBatchFieldDataConverter(IFieldType.EMPLOYEE), 
               manager.getBatchFieldDataConverter(IFieldType.EMPLOYEE_MANY));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试后注册的批量转换器覆盖先注册的转换器
   */
  @Test
  @DisplayName("测试后注册的批量转换器覆盖先注册的转换器")
  void testBatchConverterOverride() {
    // 准备测试数据 - 两个转换器都支持EMPLOYEE类型
    TestBatchFieldDataConverter overrideBatchConverter = new TestBatchFieldDataConverter(Arrays.asList(IFieldType.EMPLOYEE));
    
    Map<String, BatchFieldDataConverter> springMap = new HashMap<>();
    springMap.put("batchConverter1", testBatchConverter1); // 支持EMPLOYEE
    springMap.put("overrideBatchConverter", overrideBatchConverter); // 也支持EMPLOYEE
    
    when(applicationContext.getBeansOfType(BatchFieldDataConverter.class)).thenReturn(springMap);

    // 初始化管理器
    manager.setApplicationContext(applicationContext);

    // 验证结果 - 应该是后注册的转换器（具体哪个取决于Map的迭代顺序）
    BatchFieldDataConverter result = manager.getBatchFieldDataConverter(IFieldType.EMPLOYEE);
    assertTrue(result == testBatchConverter1 || result == overrideBatchConverter);
  }

  /**
   * 测试用的BatchFieldDataConverter实现类
   */
  private static class TestBatchFieldDataConverter implements BatchFieldDataConverter {
    private final List<String> supportedTypes;

    public TestBatchFieldDataConverter(List<String> supportedTypes) {
      this.supportedTypes = supportedTypes;
    }

    @Override
    public List<String> getSupportedFieldTypes() {
      return supportedTypes;
    }

    @Override
    public void convertFieldData(List<com.facishare.paas.metadata.api.IObjectData> dataList, 
                                 com.facishare.paas.metadata.api.describe.IFieldDescribe fieldDescribe, 
                                 DataConvertContext context) {
      // 测试实现，不做实际转换
    }
  }
}
