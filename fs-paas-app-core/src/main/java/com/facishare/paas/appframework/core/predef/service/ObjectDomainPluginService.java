package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.domain.*;
import com.facishare.paas.appframework.core.predef.service.dto.domain.FindAvailablePluginList.SimpleDomainPluginDocument;
import com.facishare.paas.appframework.core.predef.service.dto.domain.FindPluginDefinition.DomainPluginDefinitionDocument;
import com.facishare.paas.appframework.core.predef.service.dto.domain.FindPluginInstanceList.DomainPluginInstanceDocument;
import com.facishare.paas.appframework.core.predef.service.dto.domain.FindReferenceForField.PluginData;
import com.facishare.paas.appframework.core.util.ObjectUtils;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.domain.*;
import com.facishare.paas.appframework.metadata.relation.SourceTypes;
import com.facishare.paas.appframework.metadata.relation.TargetTypes;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginInstance;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.google.common.base.Strings;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by zhouwr on 2021/11/26.
 */
@Service
@ServiceModule("domain_plugin")
public class ObjectDomainPluginService {

    @Autowired
    private DomainPluginLogicService domainPluginLogicService;

    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private LayoutLogicService layoutLogicService;

    @Autowired
    private ReferenceLogicService referenceLogicService;

    @ServiceMethod("start_plugin_instance")
    public StartPluginInstance.Result startPluginInstance(StartPluginInstance.Arg arg, ServiceContext context) {
        ObjectUtils.requireNotEmpty(arg, I18NExt.text(I18NKey.PARAM_EMPTY));
        ObjectUtils.requireNotEmpty(arg.getObjectApiName(), I18NExt.text(I18NKey.PARAM_ERROR));
        ObjectUtils.requireNotEmpty(arg.getPluginApiName(), I18NExt.text(I18NKey.PARAM_ERROR));

        DomainPluginInstance instance = DomainPluginInstance.builder()
                .pluginApiName(arg.getPluginApiName())
                .refObjectApiName(arg.getObjectApiName())
                .recordTypeList(arg.getRecordTypeList())
                .pluginParam(arg.getPluginParam())
                .build();
        domainPluginLogicService.createPluginInstance(context.getUser(), instance);
        return new StartPluginInstance.Result();
    }

    @Transactional
    @ServiceMethod("create_plugin_instance")
    public CreateOrUpdatePluginInstance.Result createPluginInstance(CreateOrUpdatePluginInstance.Arg arg, ServiceContext context) {
        ObjectUtils.requireNotEmpty(arg, I18NExt.text(I18NKey.PARAM_EMPTY));
        DomainPluginInstance pluginInstance = arg.getPluginInstance();
        ObjectUtils.requireNotEmpty(pluginInstance, I18NExt.text(I18NKey.PARAM_ERROR));
        ObjectUtils.requireNotEmpty(pluginInstance.getRefObjectApiName(), I18NExt.text(I18NKey.PARAM_ERROR));
        ObjectUtils.requireNotEmpty(pluginInstance.getPluginApiName(), I18NExt.text(I18NKey.PARAM_ERROR));

        DomainPluginType pluginType = DomainPluginDefinitionHolder.getPluginType(pluginInstance.getPluginApiName());
        if (DomainPluginType.Field == pluginType) {
            List<String> fieldNames = pluginInstance.masterFieldApiNames();
            if (Strings.isNullOrEmpty(pluginInstance.getFieldApiName()) && CollectionUtils.notEmpty(fieldNames)) {
                pluginInstance.setFieldApiName(fieldNames.get(0));
            }
            ObjectUtils.requireNotEmpty(pluginInstance.getFieldApiName(), I18NExt.text(I18NKey.PARAM_ERROR));
        }

        if (pluginInstance.getPredefined() == null || !pluginInstance.getPredefined()) {
            DomainPluginInstanceProcessor.ProcessResult processResult = DomainPluginInstanceProcessor.builder()
                    .describeLogicService(describeLogicService)
                    .tenantId(context.getTenantId())
                    .pluginInstance(pluginInstance)
                    .addFields(arg.getAddFields())
                    .build()
                    .doProcess();

            if (CollectionUtils.notEmpty(processResult.getChangedDescribes())) {
                processResult.getChangedDescribes().forEach((apiName, describe) -> describeLogicService.updateDescribe(context.getUser(),
                        describe, null, false));
            }

            //将插件字段添加到布局中
            if (CollectionUtils.notEmpty(arg.getLayoutApiNames())) {
                arg.getLayoutApiNames().forEach((describeApiName, layoutApiNameList) -> {
                    IObjectDescribe describe = processResult.getDescribeMap().get(describeApiName);
                    Map<String, Layout> layouts = layoutLogicService.findLayoutByApiNames(context.getTenantId(),
                            layoutApiNameList, describeApiName);
                    List<String> fieldApiNames;
                    if (Objects.equals(describeApiName, arg.getPluginInstance().getRefObjectApiName())) {
                        fieldApiNames = arg.getPluginInstance().masterFieldApiNames();
                    } else {
                        fieldApiNames = arg.getPluginInstance().detailFieldApiNames(describeApiName);
                    }
                    List<IFieldDescribe> fieldDescribes = ObjectDescribeExt.of(describe).getFieldByApiNames(fieldApiNames);
                    if (CollectionUtils.empty(fieldDescribes)) {
                        return;
                    }
                    layouts.forEach((layoutApiName, layout) -> {
                        LayoutExt.of(layout).addFields(fieldDescribes);
                        layoutLogicService.updateLayout(context.getUser(), layout);
                    });
                });
            }
        }

        pluginInstance.setDeleted(false);
        domainPluginLogicService.createPluginInstance(context.getUser(), pluginInstance);
        return CreateOrUpdatePluginInstance.Result.builder().id(pluginInstance.getId()).build();
    }

    @Transactional
    @ServiceMethod("update_plugin_instance")
    public CreateOrUpdatePluginInstance.Result updatePluginInstance(CreateOrUpdatePluginInstance.Arg arg, ServiceContext context) {
        ObjectUtils.requireNotEmpty(arg, I18NExt.text(I18NKey.PARAM_EMPTY));
        DomainPluginInstance pluginInstance = arg.getPluginInstance();
        ObjectUtils.requireNotEmpty(pluginInstance, I18NExt.text(I18NKey.PARAM_ERROR));

        DomainPluginType pluginType = DomainPluginDefinitionHolder.getPluginType(pluginInstance.getPluginApiName());
        if (DomainPluginType.Field == pluginType) {
            List<String> fieldNames = pluginInstance.masterFieldApiNames();
            if (Strings.isNullOrEmpty(pluginInstance.getFieldApiName()) && CollectionUtils.notEmpty(fieldNames)) {
                pluginInstance.setFieldApiName(fieldNames.get(0));
            }
            ObjectUtils.requireNotEmpty(pluginInstance.getFieldApiName(), I18NExt.text(I18NKey.PARAM_ERROR));
        }

        if (Strings.isNullOrEmpty(pluginInstance.getId())) {
            ObjectUtils.requireNotEmpty(pluginInstance.getRefObjectApiName(), I18NExt.text(I18NKey.PARAM_ERROR));
            ObjectUtils.requireNotEmpty(pluginInstance.getPluginApiName(), I18NExt.text(I18NKey.PARAM_ERROR));
            DomainPluginInstance oldInstance = domainPluginLogicService.findPluginInstanceByApiName(context.getTenantId(),
                    pluginInstance.getRefObjectApiName(), pluginInstance.getPluginApiName(),
                    pluginInstance.getFieldApiName(), true);
            ObjectUtils.requireNotEmpty(oldInstance, I18NExt.text(I18NKey.PARAM_ERROR));
            //预置且没有实例
            if (Strings.isNullOrEmpty(oldInstance.getId())) {
                pluginInstance.setActive(true);
                return createPluginInstance(arg, context);
            }
            pluginInstance.setId(oldInstance.getId());
        }

        if (pluginInstance.getPredefined() == null || !pluginInstance.getPredefined()) {
            DomainPluginInstanceProcessor.ProcessResult processResult = DomainPluginInstanceProcessor.builder()
                    .describeLogicService(describeLogicService)
                    .tenantId(context.getTenantId())
                    .pluginInstance(pluginInstance)
                    .addFields(arg.getAddFields())
                    .build()
                    .doProcess();

            if (CollectionUtils.notEmpty(processResult.getChangedDescribes())) {
                processResult.getChangedDescribes().forEach((apiName, describe) -> describeLogicService.updateDescribe(context.getUser(),
                        describe, null, false));
            }
        }

        pluginInstance.setDeleted(false);
        domainPluginLogicService.updatePluginInstance(context.getUser(), pluginInstance);

        return new CreateOrUpdatePluginInstance.Result();
    }

    @ServiceMethod("enable_plugin_instance")
    public EnableOrDisablePluginInstance.Result enablePluginInstance(EnableOrDisablePluginInstance.Arg arg, ServiceContext context) {
        ObjectUtils.requireNotEmpty(arg, I18NExt.text(I18NKey.PARAM_EMPTY));
        if (Strings.isNullOrEmpty(arg.getId())) {
            ObjectUtils.requireNotEmpty(arg.getObjectApiName(), I18NExt.text(I18NKey.PARAM_ERROR));
            ObjectUtils.requireNotEmpty(arg.getPluginApiName(), I18NExt.text(I18NKey.PARAM_ERROR));
            domainPluginLogicService.updatePluginInstanceStatus(context.getUser(), arg.getObjectApiName(), arg.getPluginApiName(),
                    arg.getFieldApiName(), true);
        } else {
            domainPluginLogicService.updatePluginInstanceStatusById(context.getUser(), arg.getId(), true);
        }
        return new EnableOrDisablePluginInstance.Result();
    }

    @ServiceMethod("disable_plugin_instance")
    public EnableOrDisablePluginInstance.Result disablePluginInstance(EnableOrDisablePluginInstance.Arg arg, ServiceContext context) {
        ObjectUtils.requireNotEmpty(arg, I18NExt.text(I18NKey.PARAM_EMPTY));

        String pluginApiName = arg.getPluginApiName();
        String refObjectApiName = arg.getObjectApiName();


        if (Strings.isNullOrEmpty(arg.getId())) {
            ObjectUtils.requireNotEmpty(arg.getObjectApiName(), I18NExt.text(I18NKey.PARAM_ERROR));
            ObjectUtils.requireNotEmpty(arg.getPluginApiName(), I18NExt.text(I18NKey.PARAM_ERROR));

            // 配置中心预置插件不能禁用
            if (DomainPluginDefinitionHolder.checkIfPredefined(context.getTenantId(), pluginApiName, refObjectApiName, arg.getFieldApiName())) {
                throw new ValidateException(I18NExt.text(I18NKey.PRESETED_PLUGIN_NOT_DISABLED));
            }

            domainPluginLogicService.updatePluginInstanceStatus(context.getUser(), arg.getObjectApiName(), arg.getPluginApiName(),
                    arg.getFieldApiName(), false);
        } else {
            // 配置中心预置插件不能禁用
            DomainPluginInstance instance = domainPluginLogicService.findPluginInstanceById(context.getTenantId(), arg.getId(), true);
            if (instance != null && DomainPluginDefinitionHolder.checkIfPredefined(context.getTenantId(), instance)) {
                throw new ValidateException(I18NExt.text(I18NKey.PRESETED_PLUGIN_NOT_DISABLED));
            }

            domainPluginLogicService.updatePluginInstanceStatusById(context.getUser(), arg.getId(), false);
        }
        return new EnableOrDisablePluginInstance.Result();
    }

    @ServiceMethod("delete_plugin_instance")
    public EnableOrDisablePluginInstance.Result deletePluginInstance(EnableOrDisablePluginInstance.Arg arg, ServiceContext context) {
        ObjectUtils.requireNotEmpty(arg, I18NExt.text(I18NKey.PARAM_EMPTY));
        if (Strings.isNullOrEmpty(arg.getId())) {
            ObjectUtils.requireNotEmpty(arg.getObjectApiName(), I18NExt.text(I18NKey.PARAM_ERROR));
            ObjectUtils.requireNotEmpty(arg.getPluginApiName(), I18NExt.text(I18NKey.PARAM_ERROR));
            domainPluginLogicService.invalidPluginInstance(context.getUser(), arg.getObjectApiName(), arg.getPluginApiName(),
                    arg.getFieldApiName());
        } else {
            domainPluginLogicService.invalidPluginInstanceById(context.getUser(), arg.getId());
        }
        return new EnableOrDisablePluginInstance.Result();
    }

    @ServiceMethod("find_plugin_instance_list_management")
    // 查询全部插件实例
    public FindPluginInstanceList.Result findPluginInstanceListManagement(ServiceContext context) {
        String tenantId = context.getTenantId();
        // 查询配置中心插件实例信息
        List<DomainPluginInstance> predefinePluginInstances = DomainPluginDefinitionHolder.getPluginInstancesManagementByTenantId(tenantId);
        // 查询数据库插件实例
        Query query = Query.builder().limit(AppFrameworkConfig.DEFAULT_MAX_QUERY_LIMIT).build();
        List<DomainPluginInstance> pluginListFromDB = findPluginListManagementFromDBByQuery(context.getUser(), query);
        // 合并
        List<DomainPluginInstance> instances = mergePluginInstance(pluginListFromDB, predefinePluginInstances);
        //按照插件定义过滤插件实例
        instances = domainPluginLogicService.filterPluginInstancesByDefinitions(tenantId, instances);
        //转成前端格式
        List<FindPluginInstanceList.DomainPluginInstanceDocument> documents = buildPluginInstanceDocument(tenantId, instances);
        // 不需要total, 置空响应会自动删除
        return FindPluginInstanceList.Result.builder()
                .pluginInstanceList(documents)
                .build();
    }

    private List<DomainPluginInstance> findPluginListManagementFromDBByQuery(User user, Query query) {
        QueryResult<DomainPluginInstance> queryResult = domainPluginLogicService.findPluginInstanceByQuery(user, query);
        List<DomainPluginInstance> result = new ArrayList<>(queryResult.getData());
        return result.stream().filter(x -> DomainPluginDefinitionHolder.supportManagement(x.getPluginApiName())).collect(Collectors.toList());
    }

    private List<DomainPluginInstanceDocument> buildPluginInstanceDocument(String tenantId, List<DomainPluginInstance> pluginInstances) {
        // 获取所有插件apiName
        List<String> pluginApiNames = pluginInstances.stream().map(DomainPluginInstance::getPluginApiName).distinct().collect(Collectors.toList());
        // Label:插件名字
        Map<String, DomainPluginDefinition> definitionMap = domainPluginLogicService.findPluginDefinitionByApiNames(pluginApiNames, false);
        // refObject:相关对象
        List<String> objectApiNames = pluginInstances.stream().map(DomainPluginInstance::getRefObjectApiName).distinct().collect(Collectors.toList());
        Map<String, String> objectDisplayNameMap = describeLogicService.findDisplayNameByApiNames(tenantId, objectApiNames);
        // RecordType业务类型
        List<String> objectApiNamesWithRecordType = pluginInstances.stream().filter(x -> CollectionUtils.notEmpty(x.getRecordTypeList()))
                .map(DomainPluginInstance::getRefObjectApiName)
                .distinct()
                .collect(Collectors.toList());
        Map<String, IObjectDescribe> describeMapWithRecordType = describeLogicService.findObjects(tenantId, objectApiNamesWithRecordType);

        return pluginInstances.stream()
                .map(x -> {
                    DomainPluginInstanceDocument document = DomainPluginInstanceDocument.of(x);
                    DomainPluginDefinition definition = definitionMap.get(x.getPluginApiName());
                    if (Objects.nonNull(definition)) {
                        String label = definition.i18nLabel();
                        label = Strings.isNullOrEmpty(label) ? x.getPluginApiName() : label;
                        document.setPluginLabel(label);
                        document.setPluginDescription(definition.i18nDescription());
                    } else {
                        document.setPluginLabel(x.getPluginApiName());
                    }
                    document.setRefObjectDisplayName(objectDisplayNameMap.getOrDefault(x.getRefObjectApiName(), x.getRefObjectApiName()));
                    if (CollectionUtils.notEmpty(x.getRecordTypeList()) && describeMapWithRecordType.containsKey(x.getRefObjectApiName())) {
                        IObjectDescribe describe = describeMapWithRecordType.get(x.getRefObjectApiName());
                        ObjectDescribeExt.of(describe).getRecordTypeField().ifPresent(recordTypeField -> {
                            List<String> recordTypeLabels = x.getRecordTypeList().stream()
                                    .filter(recordTypeField::isOptionExist)
                                    .map(recordType -> recordTypeField.getRecordTypeOption(recordType).getLabel())
                                    .collect(Collectors.toList());
                            // 提示黑名单
                            document.setRecordTypeLabel((x.getExcludeRecordType() ? I18NExt.text(I18NKey.BLIACK_LIST) : "")
                                    + StringUtils.join(recordTypeLabels, ","));
                        });
                    }
                    return document;
                }).collect(Collectors.toList());
    }

    private List<DomainPluginInstance> mergePluginInstance(List<DomainPluginInstance> db, List<DomainPluginInstance> configCenter) {
        Stream<DomainPluginInstance> result = Stream.concat(db.stream(),
                configCenter.stream().filter(x -> db.stream()
                        .noneMatch(y -> y.keyEquals(x))
                ).collect(Collectors.toList()).stream());

        return result.collect(Collectors.toList());
    }

    @ServiceMethod("find_plugin_instance_list")
    public FindPluginInstanceList.Result findPluginInstanceList(FindPluginInstanceList.Arg arg, ServiceContext context) {
        ObjectUtils.requireNotEmpty(arg, I18NExt.text(I18NKey.PARAM_EMPTY));
        ObjectUtils.requireNotEmpty(arg.getSearchQueryInfo(), I18NExt.text(I18NKey.PARAM_ERROR));

        QueryResult<DomainPluginInstance> queryResult = domainPluginLogicService.findPluginInstanceByQuery(context.getUser(), arg.toQuery());
        List<String> pluginApiNames = queryResult.getData().stream().map(x -> x.getPluginApiName()).distinct().collect(Collectors.toList());
        Map<String, String> pluginLabelMap = domainPluginLogicService.findPluginLabelByApiNames(pluginApiNames);
        List<String> objectApiNames = queryResult.getData().stream().map(x -> x.getRefObjectApiName()).distinct().collect(Collectors.toList());
        Map<String, String> objectDisplayNameMap = describeLogicService.findDisplayNameByApiNames(context.getTenantId(), objectApiNames);
        List<String> objectApiNamesWithRecordType = queryResult.getData().stream()
                .filter(x -> CollectionUtils.notEmpty(x.getRecordTypeList()))
                .map(x -> x.getRefObjectApiName())
                .distinct()
                .collect(Collectors.toList());
        Map<String, IObjectDescribe> describeMapWithRecordType = describeLogicService.findObjects(context.getTenantId(), objectApiNamesWithRecordType);
        List<DomainPluginInstanceDocument> documents = queryResult.getData().stream()
                .map(x -> {
                    DomainPluginInstanceDocument document = DomainPluginInstanceDocument.of(x);
                    document.setPluginLabel(pluginLabelMap.get(x.getPluginApiName()));
                    document.setRefObjectDisplayName(objectDisplayNameMap.get(x.getRefObjectApiName()));
                    if (CollectionUtils.notEmpty(x.getRecordTypeList()) && describeMapWithRecordType.containsKey(x.getRefObjectApiName())) {
                        IObjectDescribe describe = describeMapWithRecordType.get(x.getRefObjectApiName());
                        ObjectDescribeExt.of(describe).getRecordTypeField().ifPresent(recordTypeField -> {
                            List<String> recordTypeLabels = x.getRecordTypeList().stream()
                                    .filter(recordType -> recordTypeField.isOptionExist(recordType))
                                    .map(recordType -> recordTypeField.getRecordTypeOption(recordType).getLabel())
                                    .collect(Collectors.toList());
                            document.setRecordTypeLabel(StringUtils.join(recordTypeLabels, ","));
                        });
                    }
                    return document;
                })
                .collect(Collectors.toList());
        return FindPluginInstanceList.Result.builder()
                .totalNumber(queryResult.getTotalNumber())
                .pluginInstanceList(documents)
                .build();
    }

    @ServiceMethod("find_available_plugin_list")
    public FindAvailablePluginList.Result findAvailablePluginList(FindAvailablePluginList.Arg arg, ServiceContext context) {
        ObjectUtils.requireNotEmpty(arg, I18NExt.text(I18NKey.PARAM_EMPTY));
        ObjectUtils.requireNotEmpty(arg.getDescribeApiName(), I18NExt.text(I18NKey.PARAM_ERROR));
        List<DomainPluginDefinition> pluginDefinitions = domainPluginLogicService.findAvailablePluginDefinitionsForManagement(context.getTenantId(),
                arg.getDescribeApiName());
        List<SimpleDomainPluginDocument> pluginDocuments = pluginDefinitions.stream().map(SimpleDomainPluginDocument::of).collect(Collectors.toList());
        return FindAvailablePluginList.Result.builder().pluginList(pluginDocuments).build();
    }

    @ServiceMethod("find_plugin_definition")
    public FindPluginDefinition.Result findPluginDefinition(FindPluginDefinition.Arg arg, ServiceContext context) {
        ObjectUtils.requireNotEmpty(arg, I18NExt.text(I18NKey.PARAM_EMPTY));
        ObjectUtils.requireNotEmpty(arg.getPluginApiName(), I18NExt.text(I18NKey.PARAM_ERROR));
        DomainPluginDefinition pluginDefinition = DomainPluginDefinitionHolder.getPluginDefinition(arg.getPluginApiName());
        if (Objects.nonNull(pluginDefinition)) {
            pluginDefinition.processI18NProps();
        }
        DomainPluginDefinitionDocument document = Objects.isNull(pluginDefinition) ? null : DomainPluginDefinitionDocument.of(pluginDefinition);
        return FindPluginDefinition.Result.builder().pluginDefinition(document).build();
    }

    @ServiceMethod("find_plugin_instance_detail")
    public FindPluginInstanceDetail.Result findPluginInstanceDetail(FindPluginInstanceDetail.Arg arg, ServiceContext context) {
        ObjectUtils.requireNotEmpty(arg, I18NExt.text(I18NKey.PARAM_EMPTY));
        //如果有id直接查询
        if (arg.getId() != null) {
            DomainPluginInstance result = domainPluginLogicService.findPluginInstanceById(context.getTenantId(), arg.getId(), true);
            if (result != null) {
                return FindPluginInstanceDetail.Result.builder().pluginInstance(result).build();
            }
        }

        DomainPluginInstance pluginInstance = domainPluginLogicService.findPluginInstanceByApiName(context.getTenantId(),
                arg.getObjectApiName(), arg.getPluginApiName(), arg.getFiledApiName(), true);
        if (Objects.nonNull(pluginInstance)) {
            pluginInstance.setGrayRuleMap(null);
            pluginInstance.setGrayRules(null);
            pluginInstance.setManagementGrayRule(null);
            pluginInstance.setManagementGrayRuleObj(null);
        }
        return FindPluginInstanceDetail.Result.builder().pluginInstance(pluginInstance).build();
    }

    @ServiceMethod("find_reference_for_field")
    public FindReferenceForField.Result findReferenceForField(FindReferenceForField.Arg arg, ServiceContext context) {
        ObjectUtils.requireNotEmpty(arg, I18NExt.text(I18NKey.PARAM_EMPTY));
        ObjectUtils.requireNotEmpty(arg.getDescribeApiName(), I18NExt.text(I18NKey.PARAM_ERROR));
        ObjectUtils.requireNotEmpty(arg.getFieldApiName(), I18NExt.text(I18NKey.PARAM_ERROR));
        String targetValue = arg.getDescribeApiName() + "." + arg.getFieldApiName();
        List<ReferenceData> referenceDataList = referenceLogicService.findReferenceByTarget(context.getTenantId(),
                TargetTypes.DESCRIBE_FIELD, targetValue);
        List<PluginData> pluginList = referenceDataList.stream()
                .filter(x -> SourceTypes.DOMAIN_PLUGIN.equals(x.getSourceType()))
                .map(x -> {
                    String pluginApiName = StringUtils.split(x.getSourceValue(), ".")[0];
                    return PluginData.builder()
                            .apiName(pluginApiName)
                            .label(DomainPluginDefinitionHolder.getPluginLabel(pluginApiName))
                            .build();
                }).collect(Collectors.toList());
        return FindReferenceForField.Result.builder().pluginList(pluginList).build();
    }

    @ServiceMethod("find_plugin_status")
    public FindPluginStatus.Result findPluginStatus(FindPluginStatus.Arg arg, ServiceContext context) {
        ObjectUtils.requireNotEmpty(arg, I18NExt.text(I18NKey.PARAM_EMPTY));
        ObjectUtils.requireNotEmpty(arg.getObjectApiName(), I18NExt.text(I18NKey.PARAM_ERROR));
        ObjectUtils.requireNotEmpty(arg.getPluginApiNames(), I18NExt.text(I18NKey.PARAM_ERROR));
        Map<String, Boolean> pluginStatusMap = domainPluginLogicService.findPluginStatus(context.getTenantId(),
                arg.getObjectApiName(), arg.getPluginApiNames());
        return FindPluginStatus.Result.builder().pluginStatusMap(pluginStatusMap).build();
    }

    @ServiceMethod("find_all_enabled_plugin")
    public FindAllEnabledPlugin.Result findAllEnabledPlugin(FindAllEnabledPlugin.Arg arg, ServiceContext context) {
        ObjectUtils.requireNotEmpty(arg, I18NExt.text(I18NKey.PARAM_EMPTY));
        ObjectUtils.requireNotEmpty(arg.getObjectApiName(), I18NExt.text(I18NKey.PARAM_ERROR));
        List<DomainPluginInstance> pluginInstances = domainPluginLogicService.findPluginInstances(context.getTenantId(),
                arg.getObjectApiName(), null, null);
        List<String> pluginApiNames = pluginInstances.stream().map(DomainPluginInstance::getPluginApiName).distinct().collect(Collectors.toList());
        return FindAllEnabledPlugin.Result.builder().pluginApiNames(pluginApiNames).build();
    }

}
