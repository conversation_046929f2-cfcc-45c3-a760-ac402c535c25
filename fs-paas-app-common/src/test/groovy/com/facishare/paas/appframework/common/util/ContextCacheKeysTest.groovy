package com.facishare.paas.appframework.common.util

import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

/**
 * GenerateByAI
 * 测试内容描述：ContextCacheKeys接口的单元测试
 */
class ContextCacheKeysTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ContextCacheKeys常量定义
     */
    def "测试ContextCacheKeys常量定义"() {
        expect: "常量值应符合预期"
        ContextCacheKeys.UNIQUE_RULE_LOCK == "uniqueRuleLock"
        ContextCacheKeys.DUPLICATE_SEARCH_LOCK == "duplicateSearchLock"
        ContextCacheKeys.PARAMS_IDEMPOTENT_LOCK == "paramsIdempotentLock"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ContextCacheKeys与CacheContext结合使用
     */
    def "测试ContextCacheKeys与CacheContext结合使用"() {
        given: "准备CacheContext实例"
        def context = new CacheContext()
        
        when: "使用常量作为缓存键"
        context.setCache(ContextCacheKeys.UNIQUE_RULE_LOCK, "test-value-1")
        context.setCache(ContextCacheKeys.DUPLICATE_SEARCH_LOCK, "test-value-2")
        context.setCache(ContextCacheKeys.PARAMS_IDEMPOTENT_LOCK, "test-value-3")
        
        then: "应能正确存取"
        context.getCache(ContextCacheKeys.UNIQUE_RULE_LOCK) == "test-value-1"
        context.getCache(ContextCacheKeys.DUPLICATE_SEARCH_LOCK) == "test-value-2"
        context.getCache(ContextCacheKeys.PARAMS_IDEMPOTENT_LOCK) == "test-value-3"
        
        context.containsCache(ContextCacheKeys.UNIQUE_RULE_LOCK)
        context.containsCache(ContextCacheKeys.DUPLICATE_SEARCH_LOCK)
        context.containsCache(ContextCacheKeys.PARAMS_IDEMPOTENT_LOCK)
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试ContextCacheKeys与ContextCacheUtil结合使用
     */
    def "测试ContextCacheKeys与ContextCacheUtil结合使用"() {
        given: "模拟环境"
        def context = new CacheContext()
        CacheContext.setInstance(context)
        
        when: "通过ContextCacheUtil使用常量作为缓存键"
        ContextCacheUtil.set(ContextCacheKeys.UNIQUE_RULE_LOCK, "lock-value")
        
        then: "应能通过ContextCacheUtil正确获取"
        ContextCacheUtil.get(ContextCacheKeys.UNIQUE_RULE_LOCK) == "lock-value"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ContextCacheKeys常量的唯一性
     */
    def "测试ContextCacheKeys常量的唯一性"() {
        expect: "所有常量值应该互不相同"
        ContextCacheKeys.UNIQUE_RULE_LOCK != ContextCacheKeys.DUPLICATE_SEARCH_LOCK
        ContextCacheKeys.UNIQUE_RULE_LOCK != ContextCacheKeys.PARAMS_IDEMPOTENT_LOCK
        ContextCacheKeys.DUPLICATE_SEARCH_LOCK != ContextCacheKeys.PARAMS_IDEMPOTENT_LOCK
    }
} 