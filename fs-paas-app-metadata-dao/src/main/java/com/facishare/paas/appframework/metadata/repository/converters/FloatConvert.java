package com.facishare.paas.appframework.metadata.repository.converters;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/04/20
 */
public class FloatConvert implements Converter<Float> {
    private static final FloatConvert FLOAT_CONVERT = new FloatConvert();

    public static ConverterFactory getConverterFactory() {
        return Converters.newFactory(Float.class, getConvert());
    }

    private static FloatConvert getConvert() {
        return FLOAT_CONVERT;
    }

    private FloatConvert() {
    }

    @Override
    public Float convert(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Float) {
            return ((Float) value);
        }
        if (value instanceof Number) {
            return ((Number) value).floatValue();
        }
        return Float.parseFloat(value.toString());
    }
}
