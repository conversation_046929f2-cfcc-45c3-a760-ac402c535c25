package com.facishare.paas.appframework.metadata.restdriver;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;
import java.util.Map;

/**
 * SFA老对象，元数据对象字段转换
 * <p>
 * Created by <PERSON>iyiguang on 2017/9/19.
 */
public interface ObjectDataConverter {

    IObjectData toObjectData(Map<String, Object> srcData, IObjectDescribe describe);

    List<IObjectData> toDataObjects(List<Map<String, Object>> srcDataList, IObjectDescribe describe);

    IObjectData toOldObjectData(Map<String,Object> srcData,IObjectDescribe describe);

    String toNewFieldName(String oldFieldName);

    String toOldFieldName(String newFieldName);

    Map<String, String> toNewFieldNames(List<String> oldFieldNames);

    Map<String, String> toOldFieldNames(List<String> newFieldNames);
}
