package com.facishare.paas.appframework.common.util;

import com.facishare.paas.appframework.common.service.model.AIFormulaConfigDTO;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2025/3/18
 * @Description : 读取 fs-udobj-config.ini
 */
@Slf4j
public class UdobjSectionConfig {
	private static final String CONFIG_FILE = "fs-udobj-config.ini";
	private static final Splitter SPLITTER = Splitter.on(",").trimResults().omitEmptyStrings();

	@Getter
	private static AIFormulaConfigDTO aiFormulaConfigDTO = AIFormulaConfigDTO.DEFAULT;

	@Getter
	private static Set<String> showModuleName = Sets.newHashSet(
			"Export", "ExportExcelTemplate", "List", "ListHeader", "RelatedList", "SearchList", "WebDetail", "WhatList"
	);

	static {
		loadSection("ai_formula", UdobjSectionConfig::loadAiFormula,
				() -> aiFormulaConfigDTO,
				newValue -> aiFormulaConfigDTO = newValue);

		loadSection("object_extension",
				config -> loadSet(config.get("show_module_name")),
				() -> showModuleName,
				newValue -> showModuleName = newValue);
	}

	/**
	 * 通用的section加载方法，统一处理异常和赋值逻辑
	 *
	 * @param sectionName          section名称
	 * @param configLoader         配置加载逻辑
	 * @param currentValueSupplier 获取当前值的函数
	 * @param valueUpdater         值更新函数
	 */
	private static <T> void loadSection(
			String sectionName,
			Function<IConfig, T> configLoader,
			Supplier<T> currentValueSupplier,
			Consumer<T> valueUpdater) {

		try {
			ConfigFactory.getConfig(CONFIG_FILE, sectionName, config -> {
				try {
					// 加载新配置
					T newValue = configLoader.apply(config);
					// 更新值
					valueUpdater.accept(newValue);
					log.info("load {} success, new: {}", sectionName, newValue);
				} catch (Exception e) {
					log.error("load {} failed, old: {}, config: {}", sectionName, currentValueSupplier.get(), config, e);
				}
			});
		} catch (Exception e) {
			log.error("register {} config failed", sectionName, e);
		}
	}

	private static AIFormulaConfigDTO loadAiFormula(IConfig config) {
		Integer fieldN = config.getInt("fieldN", 50);
		Integer optionN = config.getInt("optionN", 5);
		Double temperature = config.getDouble("temperature", 0.0);
		Integer maxTokens = config.getInt("maxTokens", 2048);
		String model = config.get("model", "qwen-plus");

		String fieldTypeExplainS = config.get("fieldTypeExplain");
		String noSupportApisS = config.get("noSupportApis");
		String noSupportTypesS = config.get("noSupportTypes");
		String noSupportGlobsVarsS = config.get("noSupportGlobsVars");

		if (StringUtils.isBlank(noSupportApisS) || StringUtils.isBlank(noSupportTypesS)) {
			throw new IllegalArgumentException("getAIFormulaConfig failed because of blank required fields");
		}

		Map<String, String> fieldTypeExplain = loadMap(fieldTypeExplainS);
		Set<String> noSupportApis = loadSet(noSupportApisS);
		Set<String> noSupportTypes = loadSet(noSupportTypesS);
		Set<String> noSupportGlobsVars = loadSet(noSupportGlobsVarsS);
		Map<String, Set<String>> noSupportFields = loadMap(config, ".noSupportFields");

		AIFormulaConfigDTO newConfig = AIFormulaConfigDTO.builder()
				.fieldN(fieldN)
				.optionN(optionN)
				.temperature(temperature)
				.maxTokens(maxTokens)
				.model(model)
				.fieldTypeExplain(fieldTypeExplain)
				.noSupportApis(noSupportApis)
				.noSupportTypes(noSupportTypes)
				.noSupportGlobsVars(noSupportGlobsVars)
				.noSupportFields(noSupportFields)
				.build();

		return newConfig;
	}

	private static Map<String, String> loadMap(String valueS) {
		if (StringUtils.isEmpty(valueS)) {
			return Maps.newHashMap();
		}
		return SPLITTER.splitToList(valueS).stream()
				.map(entry -> entry.split(":", 2))    // 防止和配置k-v读取冲突
				.collect(Collectors.toMap(
						entry -> entry[0],
						entry -> entry[1]
				));
	}

	private static Map<String, Set<String>> loadMap(IConfig config, String keyWords) {
		Map<String, Set<String>> mapConfig = Maps.newHashMap();
		Map<String, String> allConfig = config.getAll();
		allConfig.keySet().forEach(key -> {
			if (StringUtils.endsWith(key, keyWords)) {
				mapConfig.put(StringUtils.substringBefore(key, keyWords), loadSet(config.get(key)));
			}
		});
		return mapConfig;
	}

	private static Set<String> loadSet(String valueS) {
		if (StringUtils.isEmpty(valueS)) {
			return Sets.newHashSet();
		}
		return Sets.newHashSet(SPLITTER.split(valueS));
	}
}
