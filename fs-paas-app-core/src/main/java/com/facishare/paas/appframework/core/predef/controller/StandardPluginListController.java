package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.bizfield.BizFieldDefinitionHolder;
import com.facishare.paas.appframework.metadata.bizfield.BizFieldLogicService;
import com.facishare.paas.appframework.metadata.domain.DomainPluginDefinition;
import com.facishare.paas.appframework.metadata.domain.DomainPluginDefinition.Resource;
import com.facishare.paas.appframework.metadata.domain.DomainPluginDefinitionHolder;
import com.facishare.paas.appframework.metadata.domain.DomainPluginType;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginInstance;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2021/11/9.
 */
public class StandardPluginListController extends PreDefineController<StandardPluginListController.Arg, StandardPluginListController.Result> {

    private BizFieldLogicService bizFieldLogicService;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected void before(Arg arg) {
        bizFieldLogicService = serviceFacade.getBean(BizFieldLogicService.class);
        super.before(arg);
    }

    @Override
    protected Result doService(Arg arg) {
        List<IObjectDescribe> describeList = Lists.newArrayList();
        IObjectDescribe describe = serviceFacade.findObjectWithoutCopy(controllerContext.getTenantId(), controllerContext.getObjectApiName());
        stopWatch.lap("findDescribe");
        describeList.add(describe);
        //新建编辑页需要把从对象的插件也查出来
        if (isFormPage()) {
            List<IObjectDescribe> detailDescribes = serviceFacade.findDetailDescribesCreateWithMaster(controllerContext.getTenantId(),
                    controllerContext.getObjectApiName());
            stopWatch.lap("findDetailDescribes");
            describeList.addAll(detailDescribes);
        }
        //指定的关联对象的插件也需要查出来
        if (CollectionUtils.notEmpty(arg.getReferenceObjectApiNames())) {
            Set<String> apiNames = describeList.stream().map(IObjectDescribe::getApiName).collect(Collectors.toSet());
            Map<String, IObjectDescribe> referenceDescribes = serviceFacade.findObjectsWithoutCopy(controllerContext.getTenantId(),
                    arg.getReferenceObjectApiNames());
            describeList.addAll(CollectionUtils.nullToEmpty(referenceDescribes).values().stream()
                    .filter(x -> !apiNames.contains(x.getApiName()))
                    .collect(Collectors.toList()));
        }
        List<String> objectApiNames = describeList.stream().map(IObjectDescribe::getApiName).collect(Collectors.toList());
        List<DomainPluginInstance> pluginInstances = infraServiceFacade.findPluginInstancesByRunTimeStatus(controllerContext.getTenantId(),
                objectApiNames, arg.agentType(), null);
        stopWatch.lap("findPluginInstance");

        Map<String, List<IFieldDescribe>> bizFields = getBizFields(describeList);
        stopWatch.lap("getBizFields");

        Result result = buildResult(describeList, pluginInstances, bizFields);
        stopWatch.lap("buildResult");

        return result;
    }

    private Map<String, List<IFieldDescribe>> getBizFields(List<IObjectDescribe> describeList) {
        Map<String, List<IFieldDescribe>> bizFieldMap = Maps.newHashMap();
        describeList.forEach(describe -> {
            List<IFieldDescribe> bizFields = bizFieldLogicService.getAllBizFields(controllerContext.getTenantId(), describe);
            if (CollectionUtils.notEmpty(bizFields)) {
                bizFieldMap.put(describe.getApiName(), bizFields);
            }
        });
        return bizFieldMap;
    }

    private Result buildResult(List<IObjectDescribe> describeList, List<DomainPluginInstance> pluginInstances, Map<String, List<IFieldDescribe>> bizFields) {
        Result result = new Result();
        result.setDomains(Lists.newArrayList());
        result.setFields(Lists.newArrayList());

        describeList.forEach(describe -> {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
            Optional<RecordTypeFieldDescribe> recordTypeFieldDescribe = ObjectDescribeExt.of(describeExt).getRecordTypeField();
            if (!recordTypeFieldDescribe.isPresent()) {
                return;
            }
            List<String> allRecordTypes = recordTypeFieldDescribe.get().getRecordTypeOptions()
                    .stream().map(IRecordTypeOption::getApiName).collect(Collectors.toList());
            //领域插件
            pluginInstances.stream()
                    .filter(x -> describe.getApiName().equals(x.getRefObjectApiName()))
                    .forEach(instance -> {
                        List<Resource> resources = DomainPluginDefinitionHolder.getResources(instance.getPluginApiName(),
                                arg.getActionCode(), arg.agentType());
                        if (CollectionUtils.empty(resources)) {
                            return;
                        }
                        DomainPluginDefinition definition = DomainPluginDefinitionHolder.getPluginDefinition(instance.getPluginApiName());
                        if (Objects.isNull(definition)) {
                            return;
                        }
                        if (instance.getExcludeRecordType()) {
                            instance.reverseRecordTypes(allRecordTypes);
                            if (CollectionUtils.empty(instance.getRecordTypeList())) {
                                return;
                            }
                        }
                        resources.forEach(resource -> {
                            PluginInfo pluginInfo = PluginInfo.from(resource, instance, definition);
                            result.addPlugin(pluginInfo, DomainPluginType.of(definition.type()));
                        });
                    });
            //业务字段
            List<IFieldDescribe> bizFieldDescribes = bizFields.getOrDefault(describe.getApiName(), Collections.emptyList());
            bizFieldDescribes.forEach(field -> {
                List<Resource> resources = BizFieldDefinitionHolder.getResources(FieldDescribeExt.of(field).getBizFieldApiName(),
                        arg.getActionCode(), arg.agentType());
                if (CollectionUtils.empty(resources)) {
                    return;
                }
                resources.forEach(resource -> {
                    PluginInfo pluginInfo = PluginInfo.from(resource, field, describe);
                    result.addPlugin(pluginInfo, DomainPluginType.Field);
                });
            });
        });
        Map<String, String> customBizConfig = queryCustomBizConfigValue(describeList, pluginInstances);
        result.setCustomBizConfig(customBizConfig);
        return result;
    }

    private Map<String, String> queryCustomBizConfigValue(List<IObjectDescribe> describeList, List<DomainPluginInstance> pluginInstances) {
        Set<String> objectApiNames = describeList.stream()
                .map(IObjectDescribe::getApiName)
                .collect(Collectors.toSet());

        Map<String, String> customBizConfigMap = pluginInstances.stream()
                .filter(instance -> objectApiNames.contains(instance.getRefObjectApiName()))
                .filter(instance -> CollectionUtils.notEmpty(DomainPluginDefinitionHolder.getResources(instance.getPluginApiName(),
                        arg.getActionCode(), arg.agentType())))
                .map(instance -> DomainPluginDefinitionHolder.getPluginDefinition(instance.getPluginApiName()))
                .filter(Objects::nonNull)
                .map(DomainPluginDefinition::getCustomBizConfigList)
                .filter(CollectionUtils::notEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(DomainPluginDefinition.CustomBizConfig::getKey, DomainPluginDefinition.CustomBizConfig::getDefaultValue, (x, y) -> x));
        if (CollectionUtils.empty(customBizConfigMap)) {
            return Collections.emptyMap();
        }
        Map<String, String> resultMap = serviceFacade.queryTenantConfigs(controllerContext.getUser(), Lists.newArrayList(customBizConfigMap.keySet()));
        customBizConfigMap.forEach(resultMap::putIfAbsent);
        return resultMap;
    }

    private boolean isFormPage() {
        return StandardAction.Add.name().equalsIgnoreCase(arg.getActionCode()) || StandardAction.Edit.name().equalsIgnoreCase(arg.getActionCode());
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg {
        private String actionCode;
        //客户端类型(web-PC端；mobile-移动端)
        private String agentType;
        //需要查询插件的关联对象
        private List<String> referenceObjectApiNames;

        public String agentType() {
            return Strings.isNullOrEmpty(agentType) ? DomainPluginInstance.AgentTypes.WEB : agentType;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private List<PluginInfo> domains;
        private List<PluginInfo> fields;

        private Map<String, String> customBizConfig;

        public void addPlugin(PluginInfo pluginInfo, DomainPluginType pluginType) {
            if (Objects.isNull(pluginType)) {
                return;
            }
            switch (pluginType) {
                case Domain:
                    domains.add(pluginInfo);
                    break;
                case Field:
                    fields.add(pluginInfo);
                    break;
                default:
                    break;
            }
        }
    }

    @Data
    public static class PluginInfo {
        private String pluginApiName;
        private String objectApiName;
        private String fieldApiName;
        private List<String> recordTypeList;
        private String resource;
        private DomainPluginParam params;
        private String groupFieldApiName;
        private String type;
        private Boolean optional;
        private Map<String, String> customBizConfig;

        public static PluginInfo from(Resource resource, DomainPluginInstance instance, DomainPluginDefinition definition) {
            PluginInfo pluginInfo = new PluginInfo();
            pluginInfo.setType(definition.type());
            pluginInfo.setObjectApiName(instance.getRefObjectApiName());
            pluginInfo.setPluginApiName(instance.getPluginApiName());
            if (!instance.fieldEmpty()) {
                pluginInfo.setFieldApiName(instance.getFieldApiName());
            }
            pluginInfo.setRecordTypeList(instance.getRecordTypeList());
            pluginInfo.setResource(resource.getResourceUrl());
            pluginInfo.setParams(instance.getPluginParam());
            if (DomainPluginType.Field.getCode().equals(definition.type())) {
                pluginInfo.setOptional(true);
            } else {
                pluginInfo.setOptional(Boolean.TRUE.equals(definition.getOptional()));
            }
            return pluginInfo;
        }

        public static PluginInfo from(Resource resource, IFieldDescribe field, IObjectDescribe describe) {
            PluginInfo pluginInfo = new PluginInfo();
            pluginInfo.setOptional(true);
            pluginInfo.setType("biz_field");
            pluginInfo.setObjectApiName(describe.getApiName());
            pluginInfo.setPluginApiName(FieldDescribeExt.of(field).getBizFieldApiName());
            if (FieldDescribeExt.of(field).isGroupField()) {
                IFieldDescribe bizField = FieldDescribeExt.of(field).getBizField(describe);
                if (Objects.nonNull(bizField)) {
                    pluginInfo.setFieldApiName(bizField.getApiName());
                } else {
                    pluginInfo.setFieldApiName(field.getApiName());
                }
                pluginInfo.setGroupFieldApiName(field.getApiName());
            } else {
                pluginInfo.setFieldApiName(field.getApiName());
            }
            pluginInfo.setResource(resource.getResourceUrl());

            return pluginInfo;
        }
    }

}
