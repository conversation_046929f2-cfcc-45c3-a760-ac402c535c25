package com.facishare.crm.privilege.rest.proxy.privilege.api.model;


import com.facishare.crm.privilege.rest.proxy.privilege.api.pojo.EntityOpennessPojo;
import com.facishare.crm.privilege.rest.proxy.privilege.model.BasePageInfoDataPrivilege;
import com.facishare.crm.privilege.rest.proxy.privilege.model.BaseArgDataPrivilege;
import com.facishare.crm.privilege.rest.proxy.privilege.model.BaseResultDataPrivilege;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by yusb on 2017/4/24.
 */
public interface GetBaseDataPrivilegeRulesModel {

    //1. entityId ,scope, permission 参数具体说明请参考“ 数据结构设计-》对象级权限”
    // entityId	对象实体Id
    // scope	对象权限范围
    // permission	操作权限
    //2. entityOpenness, page 参数都可以为空，page如果为空取默认值(pageSize = 10000, currentPage = 1)

    @EqualsAndHashCode(callSuper = true)
    @Data
    class Arg extends BaseArgDataPrivilege {
        private List<String> entitys;
        private Integer permission;
        private Integer scope;
        private BasePageInfoDataPrivilege page;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    class Result extends BaseResultDataPrivilege {
        private BaseResultDataContent result;

    }

    @Data
    class BaseResultDataContent {
        private List<EntityOpennessPojo> content;
        private BasePageInfoDataPrivilege page;
    }
}
