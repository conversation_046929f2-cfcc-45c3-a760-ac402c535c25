package com.facishare.paas.appframework.function.plugin

import com.facishare.function.biz.api.exception.FunctionExecuteException
import com.facishare.function.biz.api.model.FuncExecuteContext
import com.facishare.function.biz.api.service.FunctionService
import com.facishare.paas.appframework.core.exception.AppBusinessException
import com.facishare.paas.appframework.core.exception.FunctionException
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.User
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

import java.util.function.Function
import java.util.function.Supplier

class FunctionPluginServiceImplTest extends Specification {

    FunctionPluginServiceImpl functionPluginService
    FunctionService functionService = Mock(FunctionService)
    RequestContext requestContext
    User user
    String tenantId = "12345"

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
    }

    def setup() {
        functionPluginService = new FunctionPluginServiceImpl(functionService: functionService)
        user = User.systemUser(tenantId)
        user.setUpstreamOwnerId("upstreamOwnerId")
        
        // 按照规范重构 RequestContext 构造方式
        requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(user)
                .appId("testApp")
                .thirdAppId("thirdAppId")
                .thirdType("thirdType")
                .thirdUserId("thirdUserId")
                .clientInfo("clientInfo")
                .build()
        requestContext.setAttribute(RequestContext.FS_DEVICE_TYPE, "deviceType")
        RequestContextManager.setContext(requestContext)
    }

    def cleanup() {
        RequestContextManager.removeContext()
    }

    def "测试executeFuncMethod正常执行"() {
        given: "准备测试上下文"
        Supplier<List<String>> argProcess = { -> return ["arg1", "arg2"] }
        Function<String, String> resultProcess = { it -> return it }
        FunctionPluginService.FunctionPluginContext<String> context = 
            FunctionPluginService.FunctionPluginContext.<String>builder()
                .user(user)
                .argProcess(argProcess)
                .resultProcess(resultProcess)
                .build()
        
        and: "模拟functionService.executeFuncMethod返回结果"
        functionService.executeFuncMethod(_ as FuncExecuteContext, "testFunction", "testMethod", _ as List<String>, 0) >> "success"
        
        when: "调用executeFuncMethod方法"
        def result = functionPluginService.executeFuncMethod(context, "testFunction", "testMethod")
        
        then: "验证结果"
        result == "success"
        1 * functionService.executeFuncMethod({ FuncExecuteContext ctx ->
            assert ctx.user.tenantId == user.getTenantId()
            assert ctx.user.userId == user.getUserId()
            assert ctx.user.outUserId == user.getOutUserId()
            assert ctx.user.outTenantId == user.getOutTenantId()
            assert ctx.user.upstreamOwnerId == user.getUpstreamOwnerId()
            assert ctx.appId == "testApp"
            assert ctx.thirdAppId == "thirdAppId"
            assert ctx.thirdType == "thirdType"
            assert ctx.thirdUserId == "thirdUserId"
            assert ctx.deviceType == "deviceType"
            assert ctx.clientInfo == "clientInfo"
            return true
        }, "testFunction", "testMethod", ["arg1", "arg2"], 0) >> "success"
    }
    
    def "测试executeFuncMethod为空functionApiName时直接返回"() {
        given: "准备测试上下文"
        Supplier<List<String>> argProcess = { -> return ["arg1", "arg2"] }
        Function<String, String> resultProcess = { it -> return it }
        FunctionPluginService.FunctionPluginContext<String> context = 
            FunctionPluginService.FunctionPluginContext.<String>builder()
                .user(user)
                .argProcess(argProcess)
                .resultProcess(resultProcess)
                .build()
        
        when: "调用executeFuncMethod方法，传入空functionApiName"
        def result = functionPluginService.executeFuncMethod(context, "", "testMethod")
        
        then: "验证结果直接返回空字符串"
        result == ""
        0 * functionService.executeFuncMethod(_, _, _, _, _)
    }
    
    def "测试executeFuncMethod抛出AppBusinessException异常"() {
        given: "准备测试上下文"
        Supplier<List<String>> argProcess = { -> return ["arg1", "arg2"] }
        Function<String, String> resultProcess = { it -> return it }
        FunctionPluginService.FunctionPluginContext<String> context = 
            FunctionPluginService.FunctionPluginContext.<String>builder()
                .user(user)
                .argProcess(argProcess)
                .resultProcess(resultProcess)
                .build()
        
        and: "模拟functionService.executeFuncMethod抛出AppBusinessException"
        def exception = new ValidateException("业务异常")
        functionService.executeFuncMethod(_ as FuncExecuteContext, "testFunction", "testMethod", _ as List<String>, 0) >> { throw exception }
        
        when: "调用executeFuncMethod方法"
        functionPluginService.executeFuncMethod(context, "testFunction", "testMethod")
        
        then: "验证抛出的异常"
        def e = thrown(AppBusinessException)
        e.message == "业务异常"
    }
    
    def "测试executeFuncMethod抛出FunctionExecuteException异常"() {
        given: "准备测试上下文"
        Supplier<List<String>> argProcess = { -> return ["arg1", "arg2"] }
        Function<String, String> resultProcess = { it -> return it }
        FunctionPluginService.FunctionPluginContext<String> context = 
            FunctionPluginService.FunctionPluginContext.<String>builder()
                .user(user)
                .argProcess(argProcess)
                .resultProcess(resultProcess)
                .build()
        
        and: "模拟functionService.executeFuncMethod抛出FunctionExecuteException"
        def exception = new FunctionExecuteException("函数执行异常", user.getTenantId(), "testFunction", "testMethod", ["arg1", "arg2"])
        functionService.executeFuncMethod(_ as FuncExecuteContext, "testFunction", "testMethod", _ as List<String>, 0) >> { throw exception }
        
        when: "调用executeFuncMethod方法"
        functionPluginService.executeFuncMethod(context, "testFunction", "testMethod")
        
        then: "验证抛出的异常"
        def e = thrown(FunctionException)
    }
    
    def "测试executeFuncMethod抛出其他异常"() {
        given: "准备测试上下文"
        Supplier<List<String>> argProcess = { -> return ["arg1", "arg2"] }
        Function<String, String> resultProcess = { it -> return it }
        FunctionPluginService.FunctionPluginContext<String> context = 
            FunctionPluginService.FunctionPluginContext.<String>builder()
                .user(user)
                .argProcess(argProcess)
                .resultProcess(resultProcess)
                .build()
        
        and: "模拟functionService.executeFuncMethod抛出RuntimeException"
        def exception = new RuntimeException("运行时异常")
        functionService.executeFuncMethod(_ as FuncExecuteContext, "testFunction", "testMethod", _ as List<String>, 0) >> { throw exception }
        
        when: "调用executeFuncMethod方法"
        functionPluginService.executeFuncMethod(context, "testFunction", "testMethod")
        
        then: "验证抛出的异常"
        def e = thrown(FunctionException)
    }
    
    def "测试buildFuncExecuteContext在RequestContext为null时的情况"() {
        given: "准备测试上下文并清除RequestContext"
        RequestContextManager.removeContext()
        Supplier<List<String>> argProcess = { -> return ["arg1", "arg2"] }
        Function<String, String> resultProcess = { it -> return it }
        FunctionPluginService.FunctionPluginContext<String> context = 
            FunctionPluginService.FunctionPluginContext.<String>builder()
                .user(user)
                .argProcess(argProcess)
                .resultProcess(resultProcess)
                .build()
        
        and: "模拟functionService.executeFuncMethod返回结果"
        functionService.executeFuncMethod(_ as FuncExecuteContext, "testFunction", "testMethod", _ as List<String>, 0) >> "success"
        
        when: "调用executeFuncMethod方法"
        def result = functionPluginService.executeFuncMethod(context, "testFunction", "testMethod")
        
        then: "验证结果"
        result == "success"
        1 * functionService.executeFuncMethod({ FuncExecuteContext ctx ->
            assert ctx.user.tenantId == user.getTenantId()
            assert ctx.user.userId == user.getUserId()
            assert ctx.appId == null
            return true
        }, "testFunction", "testMethod", ["arg1", "arg2"], 0) >> "success"
    }
} 