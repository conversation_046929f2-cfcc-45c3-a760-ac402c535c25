package com.facishare.paas.appframework.metadata.i18nKeyPlatform;

import com.fxiaoke.i18n.client.api.Localization;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface OnTimeTranslate {

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    class Arg {
        String key;
        String tenantId;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        String errorCode;
        String msg;
        Map<String, TranslateInfo> result ;
    }

    @Data
    class TranslateInfo {
        String id;
        String key;
        Integer tenantId;
        String en;
        String ja_JP;
        String zh_CN;
        String zh_TW;
        Long version;
        Map<Byte, String> data;
        List<String> tags;

        public Localization toLocalization() {
            return Localization.builder()
                    .id(Long.parseLong(id))
                    .key(key)
                    .tenantId(tenantId)
                    .en(en)
                    .jaJP(ja_JP)
                    .zhCN(zh_CN)
                    .zhTW(zh_TW)
                    .tags(tags)
                    .data(data)
                    .build();
        }
    }
}