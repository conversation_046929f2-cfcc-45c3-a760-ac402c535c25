package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.MappingRuleDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.convertRule.*;
import com.facishare.paas.appframework.metadata.ObjectConvertRuleService;
import com.facishare.paas.metadata.api.IObjectMappingRuleDetailInfo;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.impl.ObjectMappingRuleInfo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@ServiceModule("convert_rule")
@Service
public class ObjectValueConvertRuleService {
    @Autowired
    private ObjectConvertRuleService objectConvertRuleService;

    @ServiceMethod("create")
    public CreateConvertRule.Result create(CreateConvertRule.Arg arg, ServiceContext context) {
        //1 创建转换规则
        List<IObjectMappingRuleInfo> ruleList = convertObjectMappingRuleInfo(arg.getRuleList(), context);
        objectConvertRuleService.create(context.getUser(), ruleList);
        return new CreateConvertRule.Result();
    }

    @ServiceMethod("update")
    public UpdateConvertRule.Result update(UpdateConvertRule.Arg arg, ServiceContext context) {
        List<IObjectMappingRuleInfo> ruleList = convertObjectMappingRuleInfo(arg.getRuleList(), context);
        validateFieldMapping(ruleList);
        objectConvertRuleService.update(context.getUser(), ruleList);
        return new UpdateConvertRule.Result();
    }

    private static void validateFieldMapping(List<IObjectMappingRuleInfo> ruleList) {
        ruleList.forEach(rule -> {
            List<IObjectMappingRuleDetailInfo> fieldMapping = rule.getFieldMapping();
            Set<String> fieldApiNames = fieldMapping.stream().map(IObjectMappingRuleDetailInfo::getSourceFieldName).collect(Collectors.toSet());
            if (fieldMapping.size() != fieldApiNames.size()) {
                throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
            }
        });
    }

    private List<IObjectMappingRuleInfo> convertObjectMappingRuleInfo(List<MappingRuleDocument> arg, ServiceContext context) {
        //1 创建转换规则
        List<IObjectMappingRuleInfo> ruleList = arg.stream()
                .map(ObjectMappingRuleInfo::new).collect(Collectors.toList());
        ruleList.forEach(rule -> {
            rule.setTenantId(context.getTenantId());
            if (StringUtils.isEmpty(rule.getId())) {
                rule.setCreatedBy(context.getUser().getUserId());
            }
            rule.setLastModifiedBy(context.getUser().getUserId());
        });
        return ruleList;
    }

    @ServiceMethod("isActive")
    public EnableOrDisableConvertRule.Result isActive(EnableOrDisableConvertRule.Arg arg, ServiceContext serviceContext) {
        objectConvertRuleService.updateStatus(serviceContext.getUser(), arg.getRuleApiName(), arg.getStatus());
        return new EnableOrDisableConvertRule.Result();
    }

    @ServiceMethod("delete")
    public DeleteConvertRule.Result delete(DeleteConvertRule.Arg arg, ServiceContext context) {
        objectConvertRuleService.delete(context.getUser(), arg.getRuleApiName());
        return new DeleteConvertRule.Result();
    }

    @ServiceMethod("findRuleList")
    public FindConvertRuleList.Result findRuleList(FindConvertRuleList.Arg arg, ServiceContext context) {
        List<IObjectMappingRuleInfo> rules = objectConvertRuleService.findRuleList(context.getUser(), arg.getRuleName(), arg.getSearchQueryInfo());
        return FindConvertRuleList.Result.builder()
                .ruleList(MappingRuleDocument.fromList(rules))
                .build();
    }

    @ServiceMethod("findByApiName")
    public FindByApiName.Result findByApiName(FindByApiName.Arg arg, ServiceContext context) {
        List<IObjectMappingRuleInfo> rules = objectConvertRuleService.findConvertRuleByApiName(context.getUser(), arg.getRuleApiName());
        return FindByApiName.Result.builder()
                .ruleList(MappingRuleDocument.fromList(rules))
                .build();
    }

    @Deprecated
    @ServiceMethod("findByDescribeApiName")
    public FindByDescribeApiName.Result findByDescribeApiName(FindByDescribeApiName.Arg arg, ServiceContext context) {
        List<IObjectMappingRuleInfo> rules = objectConvertRuleService.findConvertRuleByDescribeApiName(context.getUser(), arg.getSourceApiName(), arg.getTargetApiName(), arg.getSourceId());
        return FindByDescribeApiName.Result.builder()
                .ruleList(MappingRuleDocument.fromList(rules))
                .build();
    }

    @ServiceMethod("findOrderPushRule")
    public FindOrderPushRule.Result findOrderPushRule(FindOrderPushRule.Arg arg, ServiceContext context) {
        if (CollectionUtils.empty(arg.getSourceIds())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        List<IObjectMappingRuleInfo> rules = objectConvertRuleService.findConvertRuleByDescribeApiName(context.getUser(), arg.getObjectApiName(), null, arg.getSourceIds());
        return FindOrderPushRule.Result.builder()
                .ruleList(MappingRuleDocument.fromList(rules))
                .build();
    }

    @ServiceMethod("findOrderPullRule")
    public FindOrderPullRule.Result findOrderPullRule(FindOrderPullRule.Arg arg, ServiceContext context) {
        List<IObjectMappingRuleInfo> rules = objectConvertRuleService.findConvertRuleByDescribeApiName(context.getUser(), null, arg.getObjectApiName(), Lists.newArrayList(), arg.getRecordType(), arg.isDoublePull());
        return FindOrderPullRule.Result.builder()
                .ruleList(MappingRuleDocument.fromList(rules))
                .build();
    }

    @ServiceMethod("isSupportConvertRule")
    public SupportConvertRule.Result isSupportConvertRule(ServiceContext context) {
        return SupportConvertRule.Result.builder()
                .success(objectConvertRuleService.supportConvertRule(context.getTenantId()))
                .build();
    }
}
