package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.MessagePollingService;
import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.common.service.model.PollingMsgEndType;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.brushData.*;
import com.facishare.paas.appframework.core.predef.service.dto.calculate.SubmitCalculateJob;
import com.facishare.paas.appframework.core.predef.service.dto.objectMapping.SetupTeamInterconnectedDepartmentsByIds;
import com.facishare.paas.appframework.core.predef.service.dto.objectMapping.UpdateBizType;
import com.facishare.paas.appframework.core.predef.service.dto.switchcache.UpdateSwitchCache;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.flow.ApprovalFlowService;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.flow.ExtraDataKeys;
import com.facishare.paas.appframework.flow.WorkFlowService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.gdpr.GdprService;
import com.facishare.paas.appframework.metadata.layout.LayoutStructure;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.appframework.metadata.layout.component.ISummaryComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.SummaryComponentInfo;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.metadata.options.OptionSetLogicService;
import com.facishare.paas.appframework.metadata.relation.*;
import com.facishare.paas.appframework.metadata.repository.model.GdprCompliance;
import com.facishare.paas.appframework.metadata.repository.model.MtOptionSet;
import com.facishare.paas.appframework.metadata.switchcache.SwitchCacheService;
import com.facishare.paas.appframework.metadata.treeview.TreeViewService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.CreateFunctionPrivilege;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.api.service.IUdefButtonService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.DataSnapshot;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.TabSection;
import com.facishare.paas.metadata.support.GDSHandler;
import com.facishare.paas.metadata.ui.layout.*;
import com.facishare.paas.timezone.TimeZoneContextHolder;
import com.facishare.uc.api.model.enterprise.EnterpriseInfoDto;
import com.facishare.uc.api.service.EnterpriseRemoteService;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.common.concurrent.DynamicExecutors;
import com.fxiaoke.limit.GuavaLimiter;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.eventbus.Subscribe;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.LongAdder;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by liwei on 2019/5/22
 */
@ServiceModule("brush_data")
@Component
@Slf4j
public class BrushDataService {
    @Autowired
    private MetaDataService metaDataService;

    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private ReferenceLogicService referenceLogicService;

    @Autowired
    private FieldRelationCalculateService fieldRelationCalculateService;

    @Autowired
    private ExpressionCalculateLogicService expressionCalculateLogicService;

    @Autowired
    private JobScheduleService jobScheduleService;

    @Autowired
    private LayoutLogicService layoutLogicService;

    @Autowired
    private SwitchCacheService switchCacheService;

    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;

    @Autowired
    private GDSHandler gdsHandler;

    @Autowired
    private WorkFlowService workFlowService;

    @Autowired
    private TreeViewService treeViewService;

    @Autowired
    private ApprovalFlowService approvalFlowService;

    @Autowired
    private OptionSetLogicService optionSetLogicService;

    @Autowired
    private MaskFieldLogicService maskFieldLogicService;

    @Autowired
    private AutoNumberLogicService autoNumberLogicService;

    @Autowired
    private DataSnapshotLogicService dataSnapshotLogicService;

    @Autowired
    private QuoteValueService quoteValueService;

    @Autowired
    private IUdefButtonService buttonService;

    @Autowired
    private MetaDataComputeService metaDataComputeService;

    @Autowired
    private FieldRelationGraphService fieldRelationGraphService;

    @Autowired
    private EnterpriseRemoteService enterpriseRemoteService;

    @Autowired
    private ApplicationLayeredLogicService applicationLayeredLogicService;

    @Autowired
    private CustomButtonServiceImpl customButtonService;

    @Autowired
    private MessagePollingService messagePollingService;

    @Autowired
    private ObjectMappingService objectMappingService;

    @Autowired
    private GdprService gdprService;

    public static final List<String> noNormalizedApiNames = Lists.newArrayList("SaleActionStageObj",
            "SaleActionObj", "VisitingObj", "CRMFeedObj", "OpportunityProductRelationObj", "OpportunityContactRelationObj",
            "SalesOrderContractRelationObj", "SaleEventObj");

    public static Map<String, String> oldSfaStatusMap;
    private static final int PAGE_SIZE = 1000;

    private static final ThreadFactory backgroundWorkerFactory = new ThreadFactoryBuilder().setNameFormat("brush-data-%d").setDaemon(true).build();
    private static final ThreadPoolExecutor executor = DynamicExecutors.newThreadPool(5, 50, 10000L, 1000, backgroundWorkerFactory);

    static {
        oldSfaStatusMap = Maps.newHashMap();
        oldSfaStatusMap.put("AccountObj", "account_status");
        oldSfaStatusMap.put("SalesOrderObj", "order_status");
        oldSfaStatusMap.put("OpportunityObj", "status");
        oldSfaStatusMap.put("ReturnedGoodsInvoiceObj", "status");
        oldSfaStatusMap.put("InvoiceApplicationObj", "status");
        oldSfaStatusMap.put("ContactObj", "contact_status");
        oldSfaStatusMap.put("ContractObj", "status");
        oldSfaStatusMap.put("MarketingEventObj", "status");
        oldSfaStatusMap.put("RefundObj", "status");
        oldSfaStatusMap.put("LeadsObj", "leads_status");
    }

    @ServiceMethod("submit_formula_reference_relation")
    public SubmitFormulaReferenceRelation.Result submitFormulaReferenceRelation(ServiceContext context, SubmitFormulaReferenceRelation.Arg arg) {
        if (StringUtils.isEmpty(context.getTenantId())) {
            return new SubmitFormulaReferenceRelation.Result(false);
        }
        boolean submitAll = false;
        List<IObjectDescribe> describeList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(arg.getObjectApiName())) {
            IObjectDescribe objectDescribe = describeLogicService.findObject(context.getTenantId(), arg.getObjectApiName());
            describeList.add(objectDescribe);
        } else {
            submitAll = true;
            describeList = describeLogicService.findDescribeListWithFields(context.getUser());
        }
        int itemNum = 0;
        for (IObjectDescribe describe : describeList) {
            List<IFieldDescribe> formulaFields = Lists.newArrayList();
            try {
                if (submitAll) {
                    describe = describeLogicService.findObject(context.getTenantId(), describe.getApiName());
                    formulaFields = ObjectDescribeExt.of(describe).getFormulaFields();
                } else {
                    if (StringUtils.isNotEmpty(arg.getFieldApiName())) {
                        IFieldDescribe fieldDescribe = describe.getFieldDescribe(arg.getFieldApiName());
                        formulaFields.add(fieldDescribe);
                    } else {
                        formulaFields = ObjectDescribeExt.of(describe).getFormulaFields();
                    }
                }
                List<ReferenceData> referenceData = fieldRelationCalculateService.checkReferenceOfFormulaField(describe,
                        formulaFields, false);
                List<String> personnelFormulas = referenceData.stream()
                        .filter(x -> TargetTypes.RELATED_DESCRIBE_FIELD.equals(x.getTargetType()))
                        .map(x -> StringUtils.split(x.getSourceValue(), ".")[1])
                        .collect(Collectors.toList());
                if (CollectionUtils.notEmpty(personnelFormulas)) {
                    referenceLogicService.deleteAndCreateReference(context.getTenantId(), referenceData);
                    List<IFieldDescribe> fieldsToUpdate = ObjectDescribeExt.of(describe).getFieldByApiNames(personnelFormulas);
                    fieldsToUpdate = fieldsToUpdate.stream().filter(x -> !x.isIndex()).peek(x -> x.setIndex(true)).collect(Collectors.toList());
                    describeLogicService.updateFieldDescribe(describe, fieldsToUpdate);
                    jobScheduleService.submitCalculateJob(context.getUser(), personnelFormulas, describe.getApiName(), true);
                    log.info("submitFormulaReferenceRelation success,tenantId:{},objectApiName:{},itemNum:{}", describe.getTenantId(),
                            describe.getApiName(), referenceData.size());
                    itemNum += referenceData.size();
                }
            } catch (Exception e) {
                log.error("submitFormulaReferenceRelation error,tenantId:{},objectApiName:{}", describe.getTenantId(), describe.getApiName(), e);
            }
        }
        log.info("submitFormulaReferenceRelation done,tenantId:{},itemNum:{}", context.getTenantId(), itemNum);
        return new SubmitFormulaReferenceRelation.Result(true);
    }

    @ServiceMethod("check_stored_fields")
    public CheckStoredFormulaFields.Result checkStoredFormulaFields(ServiceContext context, CheckStoredFormulaFields.Arg arg) {
        if (StringUtils.isEmpty(context.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        List<IObjectDescribe> describeList;
        if (StringUtils.isNotEmpty(arg.getObjectApiName())) {
            IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getObjectApiName());
            describeList = Lists.newArrayList(describe);
        } else {
            describeList = describeLogicService.findDescribeListWithFields(context.getUser());
        }
        //直接、间接使用全局变量和人员类型的计算字段
        describeList.forEach(x -> {
            if (StringUtils.isNotEmpty(arg.getExcludeObjectApiName()) && StringUtils.equals(x.getApiName(), arg.getExcludeObjectApiName())) {
                return;
            } else if (StringUtils.isEmpty(arg.getObjectApiName())) {
                x = describeLogicService.findObject(context.getTenantId(), x.getApiName());
            }
            List<IFieldDescribe> formulaFields = x.getFieldDescribes().stream()
                    .filter(y -> FieldDescribeExt.of(y).isFormula())
                    .filter(y -> !y.isIndex())
                    .collect(Collectors.toList());
            List<IFieldDescribe> ungroundedFormulaFields = fieldRelationCalculateService.findUnStoredFormulaFields(x, formulaFields, arg.getIncludeType());

            if (CollectionUtils.notEmpty(formulaFields)) {
                formulaFields = formulaFields.stream().filter(y -> !ungroundedFormulaFields.contains(y)).collect(Collectors.toList());
                log.info("CheckStoredFormulaFields tenantId:{},describeApiName:{},formulaFields:{}", context.getTenantId(),
                        x.getApiName(), formulaFields.stream().map(z -> z.getApiName()).collect(Collectors.toList()));
                formulaFields.stream().forEach(y -> y.setIndex(true));
                describeLogicService.updateFieldDescribe(x, formulaFields);
            }
        });
        return new CheckStoredFormulaFields.Result(true);
    }

    @ServiceMethod("check_unStored_fields")
    public CheckUnStoredFormulaFields.Result checkUnStoredFormulaFields(ServiceContext context, CheckUnStoredFormulaFields.Arg arg) {
        if (StringUtils.isEmpty(context.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        List<IObjectDescribe> describeList;
        if (StringUtils.isNotEmpty(arg.getObjectApiName())) {
            IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getObjectApiName());
            describeList = Lists.newArrayList(describe);
        } else {
            describeList = describeLogicService.findDescribeListWithFields(context.getUser());
        }
        //直接、间接使用全局变量和人员类型的计算字段
        describeList.forEach(x -> {
            if (StringUtils.isNotEmpty(arg.getExcludeObjectApiName()) && StringUtils.equals(x.getApiName(), arg.getExcludeObjectApiName())) {
                return;
            } else if (StringUtils.isEmpty(arg.getObjectApiName())) {
                x = describeLogicService.findObject(context.getTenantId(), x.getApiName());
            }
            List<IFieldDescribe> ungroundedFormulaFields = fieldRelationCalculateService.findUnStoredFormulaFields(x, null, arg.getIncludeType());
            if (CollectionUtils.notEmpty(ungroundedFormulaFields)) {
                log.info("CheckUnStoredFormulaFields tenantId:{},describeApiName:{},ungroundedFormulaFields:{}", context.getTenantId(),
                        x.getApiName(), ungroundedFormulaFields.stream().map(z -> z.getApiName()).collect(Collectors.toList()));
                ungroundedFormulaFields.stream().forEach(y -> y.setIndex(false));
                describeLogicService.updateFieldDescribe(x, ungroundedFormulaFields);
            }
        });
        return new CheckUnStoredFormulaFields.Result(true);
    }

    @ServiceMethod("update_relevant_team")
    public UpdateRelevantTeam.Result updateRelevantTeam(ServiceContext context, UpdateRelevantTeam.Arg arg) {
        UpdateRelevantTeam.Result result = new UpdateRelevantTeam.Result(true);
        List<IObjectData> objectDataListToUpdate = metaDataService.findObjectDataByIds(context.getTenantId(),
                arg.getIdList(), arg.getObjectApiName());
        if (CollectionUtils.empty(objectDataListToUpdate)) {
            result.setSuccess(false);
            return result;
        }
        boolean teamRoleGray = TeamMember.isTeamRoleGray(context.getTenantId());
        for (IObjectData objectData : objectDataListToUpdate) {
            List<TeamMemberInfoPoJo> relevantTeamFromObjectData = ObjectDataExt.of(objectData).getRelevantTeamFromObjectData();
            for (TeamMemberInfoPoJo memberInfoPoJo : relevantTeamFromObjectData) {
                String teamMemberRole = memberInfoPoJo.getTeamMemberRole();
                if (teamMemberRole.contains(",")) {
                    Set<String> roles = Sets.newHashSet(teamMemberRole.split(","));
                    List<TeamMember> teamMemberList = Lists.newArrayList();
                    for (String roleType : roles) {
                        memberInfoPoJo.getTeamMemberEmployee().forEach(x -> {
                            TeamMember teamMember;
                            if (teamRoleGray) {
                                teamMember = new TeamMember(x, roleType,
                                        TeamMember.Permission.of(memberInfoPoJo.getTeamMemberPermissionType()), null);
                            } else {
                                teamMember = new TeamMember(x, TeamMember.Role.of(roleType),
                                        TeamMember.Permission.of(memberInfoPoJo.getTeamMemberPermissionType()));
                            }
                            teamMemberList.add(teamMember);
                        });
                    }
                    ObjectDataExt.of(objectData).addTeamMembers(teamMemberList);
                }
            }
        }
        metaDataService.batchUpdateRelevantTeam(context.getUser(), objectDataListToUpdate, false);
        return result;
    }

    private QueryResult<IObjectData> findData(User user, IObjectDescribe describe, String headerId) {
        int pageSize = 100;
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        queryExt.setOrders(Lists.newArrayList(new OrderBy[]{new OrderBy("_id", true)}));
        queryExt.setLimit(pageSize);
        IFilter filter = new Filter();
        IFilter timeFilter = new Filter();
        Wheres wheres = new Wheres();

        filter.setFieldName(IObjectData.IS_DELETED);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList("1"));

        timeFilter.setFieldName(ObjectLifeStatus.LIFE_STATUS_API_NAME);
        timeFilter.setOperator(Operator.IN);
        timeFilter.setFieldValues(Lists.newArrayList(ObjectLifeStatus.INVALID.getCode()));

        List<IFilter> filters = Lists.newArrayList(filter, timeFilter);
        wheres.setFilters(filters);
        queryExt.getWheres().add(wheres);

        if (StringUtils.isNotEmpty(headerId)) {
            IFilter idFilter = new Filter();
            idFilter.setFieldName(IObjectData.ID);
            idFilter.setOperator(Operator.GT);
            idFilter.setFieldValues(Lists.newArrayList(headerId));
            queryExt.addFilters(Lists.newArrayList(idFilter));
        }

        IActionContext context = ActionContextExt.of(user).getContext();
        context.setDoCalculate(false);
        return metaDataService.findBySearchQueryWithDeleted(context, describe, queryExt.toSearchTemplateQuery());
    }

    @ServiceMethod("submit_calculate_job")
    public SubmitCalculateJob.Result submitCalculateJob(SubmitCalculateJob.Arg arg, ServiceContext context) {
        if (Objects.isNull(arg)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_EMPTY));
        }
        if (CollectionUtils.empty(arg.getCalculateJobList())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        arg.getCalculateJobList().forEach(x -> {
            if (Strings.isNullOrEmpty(x.getObjectApiName()) || CollectionUtils.empty(x.getFieldApiNameList())) {
                throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
            }
        });
        List<String> objectApiNameList = arg.getCalculateJobList().stream().map(x -> x.getObjectApiName()).distinct().collect(Collectors.toList());
        List<IObjectDescribe> describeList = Lists.newArrayList(describeLogicService.findObjects(context.getTenantId(), objectApiNameList).values());
        Map<String, List<String>> calculateFieldMap = Maps.newHashMap();
        CollectionUtils.nullToEmpty(arg.getCalculateJobList()).forEach(x -> calculateFieldMap.put(x.getObjectApiName(), x.getFieldApiNameList()));

        for (IObjectDescribe describe : describeList) {
            List<String> fieldNameList = null;
            try {
                if (CollectionUtils.notEmpty(calculateFieldMap.get(describe.getApiName()))) {
                    fieldNameList = calculateFieldMap.get(describe.getApiName());
                    //只允许提交统计字段、计算字段、特殊状态字段的计算任务
                    Set<String> allCalculateFields = ObjectDescribeExt.of(describe).getActiveFieldDescribes().stream()
                            .filter(x -> FieldDescribeExt.of(x).isCalculateFieldsNeedStoreInDB())
                            .map(x -> x.getApiName())
                            .collect(Collectors.toSet());
                    fieldNameList.removeIf(x -> !allCalculateFields.contains(x));
                }
                calculateFieldMap.put(describe.getApiName(), fieldNameList);
                if (CollectionUtils.notEmpty(fieldNameList)) {
                    jobScheduleService.submitCalculateJob(context.getUser(), fieldNameList, describe.getApiName(), arg.isManual());
                    log.warn("submit calculate job success,tenantId:{},objectApiName:{},fieldNameList:{},manual:{}",
                            context.getTenantId(), describe.getApiName(), fieldNameList, arg.isManual());
                } else {
                    log.warn("no field need calculate");
                }
            } catch (Exception e) {
                log.error("submit calculate job failed,tenantId:{},objectApiName:{},fieldNameList:{},errMsg:{}",
                        context.getTenantId(), describe.getApiName(), fieldNameList, e.getMessage(), e);
            }
        }
        return new SubmitCalculateJob.Result(calculateFieldMap);
    }

    @ServiceMethod("calculate_order_status")
    public CalculateOrderStatus.Result calculateOrderStatus(ServiceContext context) {
        String describeApiName = "SalesOrderObj";
        String orderStatusApiName = "order_status";
        CalculateOrderStatus.Result result = new CalculateOrderStatus.Result(true);
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), describeApiName);
        if (describe != null && describe.containsField(orderStatusApiName)) {
            IFieldDescribe fieldDescribe = describe.getFieldDescribe(orderStatusApiName);
            String headerId = "";
            List<IObjectData> data = processCalculateData(context, orderStatusApiName, describe, fieldDescribe, headerId);

            while (CollectionUtils.notEmpty(data) && data.size() >= 100) {
                headerId = data.get(data.size() - 1).getId();
                data = processCalculateData(context, orderStatusApiName, describe, fieldDescribe, headerId);
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    log.info("InterruptedException error.");
                    Thread.currentThread().interrupt();
                }
            }
        }
        return result;
    }

    @ServiceMethod("bulk_invalid_data")
    public BulkInvalidData.Result bulkInvalidData(BulkInvalidData.Arg arg, ServiceContext context) {
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getMasterObjectApiName());
        if (ObjectDescribeExt.of(describe).isSlaveObject()) {
            return BulkInvalidData.Result.buildError("object is exist");
        }

        IObjectData objectData = metaDataService.findObjectDataIgnoreStatus(context.getUser(), arg.getMasterDataId(), arg.getMasterObjectApiName());
        if (objectData == null || !ObjectDataExt.of(objectData).isInvalid()) {
            return BulkInvalidData.Result.buildError("data not exist or not invalid");
        }

        List<IObjectDescribe> detailDescribes = describeLogicService.findDetailDescribes(context.getTenantId(), arg.getMasterObjectApiName());
        if (CollectionUtils.empty(detailDescribes)) {
            return BulkInvalidData.Result.buildError("slave not exist");
        }

        detailDescribes.forEach(detailDescribe -> dealOneDetail(detailDescribe, Lists.newArrayList(objectData), arg.getMasterObjectApiName(), context.getUser()));
        return BulkInvalidData.Result.buildSuccess();
    }

    private void dealOneDetail(IObjectDescribe detailDescribe, List<IObjectData> masterDataList,
                               String masterObjectApiName, User user) {
        List<String> masterDataIds = masterDataList.stream().map(x -> x.getId()).collect(Collectors.toList());
        try {
            QueryResult<IObjectData> detailDataResult = metaDataService.findDetailObjectDataBatchWithPageIncludeInvalid(user,
                    masterObjectApiName, masterDataIds, detailDescribe, 1, PAGE_SIZE, null);

            int totalPage = SearchTemplateQueryExt.calculateTotalPage(detailDataResult.getTotalNumber(), PAGE_SIZE);
            for (int pageNum = 1; pageNum <= totalPage; pageNum++) {
                if (pageNum > 1) {
                    detailDataResult = metaDataService.findDetailObjectDataBatchWithPageIncludeInvalid(user,
                            masterObjectApiName, masterDataIds, detailDescribe, pageNum, PAGE_SIZE, null);
                }
                // 作废时 从对象处理 作废状态 之外的所有状态的数据
                List<IObjectData> dataList = CollectionUtils.nullToEmpty(detailDataResult.getData()).stream()
                        .filter(data -> !ObjectDataExt.of(data).isInvalid()).collect(Collectors.toList());
                if (CollectionUtils.empty(dataList)) {
                    continue;
                }
                invalidObjects(dataList, user);
            }
        } catch (MetaDataBusinessException e) {
            log.warn("dealWithDetail error,user:{},masterApiName:{},masterDataId:{},detailApiName:{}",
                    user, masterObjectApiName, masterDataIds, detailDescribe.getApiName(), e);
        } catch (Exception e) {
            log.error("dealWithDetail error,user:{},masterApiName:{},masterDataId:{},detailApiName:{}",
                    user, masterObjectApiName, masterDataIds, detailDescribe.getApiName(), e);
        }
    }

    private void invalidObjects(List<IObjectData> objectList, User user) {
        if (CollectionUtils.empty(objectList)) {
            return;
        }
        Map<String, List<IObjectData>> dataGroups = objectList.stream().collect(Collectors.groupingBy(IObjectData::getDescribeApiName));
        dataGroups.forEach((x, y) -> bulkInvalidObjects(y, user));

    }

    private void bulkInvalidObjects(List<IObjectData> objectList, User user) {
        List<IObjectData> invalidedList = metaDataService.bulkInvalid(objectList, user);
        sendActionMq(invalidedList, ObjectAction.INVALID, user);
        triggerWorkFlow(invalidedList, user);
    }

    private void triggerWorkFlow(List<IObjectData> invalidedList, User user) {
        for (IObjectData objectData : invalidedList) {
            workFlowService.startWorkFlow(objectData.getId(), objectData.getDescribeApiName(), ApprovalFlowTriggerType.INVALID.getId(), user, Maps.newHashMap(), null);
        }
    }

    private void sendActionMq(List<IObjectData> objectDataList, ObjectAction objectAction, User user) {
        metaDataService.sendActionMq(user, objectDataList, objectAction);
    }
/*
    private void bulkInvalidObjects(String detailApiName, List<IObjectData> objectList, User user) {
        RequestContext context = RequestContextManager.getContext();
        ActionContext actionContext = new ActionContext(context, detailApiName, StandardAction.BulkInvalid.toString());

        StandardBulkInvalidAction.Arg arg = new StandardBulkInvalidAction.Arg();
        List<StandardBulkInvalidAction.ArgHelper> helpers = objectList.stream().map(data -> StandardBulkInvalidAction.ArgHelper.builder()
                .id(data.getId())
                .objectDescribeApiName(data.getName())
                .build()).collect(Collectors.toList());
        arg.setDataList(helpers);
        serviceFacade.triggerAction(actionContext, arg);
    }
*/

    private List<IObjectData> processCalculateData(ServiceContext context, String orderStatusApiName, IObjectDescribe describe, IFieldDescribe fieldDescribe, String headerId) {
        QueryResult<IObjectData> queryResult = findData(context.getUser(), describe, headerId);
        List<IObjectData> data = queryResult.getData();
        calculateAndUpdateFormulaFields(context.getUser(), describe, data, Lists.newArrayList(orderStatusApiName), Lists.newArrayList(fieldDescribe));
        return data;
    }

    public void calculateAndUpdateFormulaFields(User user, IObjectDescribe describe, List<IObjectData> objectDataList,
                                                List<String> formulaFieldNames, List<IFieldDescribe> formulaFieldList) {
        ActionContextExt actionContext = ActionContextExt.of(user, null).setBatch(true);
        expressionCalculateLogicService.bulkCalculateWithDependentData(describe, objectDataList, formulaFieldList, null, true);
        metaDataService.batchUpdateWithFieldsForCalculateToPG(actionContext.getContext(), objectDataList, formulaFieldNames);
    }

    @ServiceMethod("convert_V2Layout_to_V1")
    public Map convertV2LayoutToV1(Map arg, ServiceContext context) {
        String layoutApiName = (String) arg.get("layoutApiName");
        String objectApiName = (String) arg.get("objectApiName");
        if (Strings.isNullOrEmpty(layoutApiName) || Strings.isNullOrEmpty(objectApiName)) {
            throw new ValidateException("param cannot be empty");
        }
        ILayout layout = layoutLogicService.findLayoutByApiName(context.getUser(), layoutApiName, objectApiName);
        if (layout == null) {
            throw new ValidateException("layout not exists");
        }
        LayoutStructure.restoreLayout(LayoutExt.of(layout), PageType.Designer);
        layoutLogicService.updateLayout(context.getUser(), layout);
        return ImmutableMap.of(ILayout.NAME, layout.getName(), ILayout.DISPLAY_NAME, layout.getDisplayName(), ILayout.VERSION, layout.getVersion());
    }

    @ServiceMethod("hide_components_in_layout")
    public Map hideComponentsInLayout(Map arg, ServiceContext context) {
        String layoutApiName = (String) arg.get("layoutApiName");
        String objectApiName = (String) arg.get("objectApiName");
        List<String> hiddenComponentList = (List<String>) arg.get("hiddenComponentList");
        if (Strings.isNullOrEmpty(layoutApiName)
                || Strings.isNullOrEmpty(objectApiName)
                || CollectionUtils.empty(hiddenComponentList)) {
            throw new ValidateException("param cannot be empty");
        }
        ILayout layout = layoutLogicService.findLayoutByApiName(context.getUser(), layoutApiName, objectApiName);
        if (layout == null) {
            throw new ValidateException("layout not exists");
        }
        List<String> hiddenComponents = CollectionUtils.nullToEmpty(layout.getHiddenComponents());
        hiddenComponentList.removeIf(x -> hiddenComponents.contains(x));
        if (CollectionUtils.notEmpty(hiddenComponentList)) {
            hiddenComponents.addAll(Sets.newHashSet(hiddenComponentList));
            layout.setHiddenComponents(hiddenComponents);
            layoutLogicService.updateLayout(context.getUser(), layout);
        }
        return ImmutableMap.of(ILayout.NAME, layout.getName(), ILayout.DISPLAY_NAME, layout.getDisplayName(), "added_hidden_components", hiddenComponentList);
    }

    @ServiceMethod("remove_components_in_layout")
    public Map removeComponentsInLayout(Map arg, ServiceContext context) {
        String layoutApiName = (String) arg.get("layoutApiName");
        String objectApiName = (String) arg.get("objectApiName");
        List<String> removeComponentList = (List<String>) arg.get("removeComponentList");
        if (Strings.isNullOrEmpty(layoutApiName)
                || Strings.isNullOrEmpty(objectApiName)
                || CollectionUtils.empty(removeComponentList)) {
            throw new ValidateException("param cannot be empty");
        }
        ILayout layout = layoutLogicService.findLayoutByApiName(context.getUser(), layoutApiName, objectApiName);
        if (layout == null) {
            throw new ValidateException("layout not exists");
        }
        Set<String> components = LayoutExt.of(layout).getComponentsSilently().stream().map(x -> x.getName()).collect(Collectors.toSet());
        removeComponentList.removeIf(x -> !components.contains(x));
        if (CollectionUtils.notEmpty(removeComponentList)) {
            WebDetailLayout.of(layout).removeComponents(removeComponentList);
            layoutLogicService.updateLayout(context.getUser(), layout);
        }
        return ImmutableMap.of(ILayout.NAME, layout.getName(), ILayout.DISPLAY_NAME, layout.getDisplayName(), "remove_components", removeComponentList);
    }

    @ServiceMethod("remove_duplicate_components_in_layout")
    public Map removeDuplicateComponentsInLayout(Map arg, ServiceContext context) {
        String layoutApiName = (String) arg.get("layoutApiName");
        String objectApiName = (String) arg.get("objectApiName");
        if (Strings.isNullOrEmpty(layoutApiName) || Strings.isNullOrEmpty(objectApiName)) {
            throw new ValidateException("param cannot be empty");
        }
        ILayout layout = layoutLogicService.findLayoutByApiName(context.getUser(), layoutApiName, objectApiName);
        if (layout == null) {
            throw new ValidateException("layout not exists");
        }
        Set<String> componentNames = Sets.newHashSet();
        Set<String> removedComponents = Sets.newHashSet();
        List<IComponent> components = LayoutExt.of(layout).getComponentsSilently();
        Iterator<IComponent> iterator = components.iterator();
        while (iterator.hasNext()) {
            IComponent component = iterator.next();
            if (!componentNames.add(component.getName())) {
                removedComponents.add(component.getName());
                iterator.remove();
            }
        }
        if (!removedComponents.isEmpty()) {
            layout.setComponents(components);
            layoutLogicService.updateLayout(context.getUser(), layout);
        }
        return ImmutableMap.of(ILayout.NAME, layout.getName(), ILayout.DISPLAY_NAME, layout.getDisplayName(), "removedComponents", removedComponents);
    }

    @ServiceMethod("remove_duplicate_fields_in_layout")
    public Map removeDuplicateFieldsInLayout(Map arg, ServiceContext context) {
        String objectApiName = (String) arg.get("objectApiName");
        if (Strings.isNullOrEmpty(objectApiName)) {
            throw new ValidateException("objectApiName cannot be empty");
        }
        String layoutApiName = (String) arg.get("layoutApiName");
        String appId = (String) arg.get("appId");
        List<ILayout> layouts = Lists.newArrayList();
        if (!Strings.isNullOrEmpty(layoutApiName)) {
            ILayout layout = layoutLogicService.findLayoutByApiName(context.getUser(), layoutApiName, objectApiName);
            if (layout == null) {
                throw new ValidateException("layout not exists");
            }
            if (!LayoutExt.of(layout).isDetailLayout() && !LayoutExt.of(layout).isEditLayout()) {
                throw new ValidateException("only support detail or edit layout");
            }
            layouts.add(layout);
        } else {
            LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), appId);
            layouts = layoutLogicService.findByTypesIncludeFlowLayout(layoutContext, objectApiName, Lists.newArrayList(LayoutTypes.DETAIL, LayoutTypes.EDIT));
        }

        Map<String, Map> result = Maps.newHashMap();
        layouts.forEach(layout -> {
            Set<String> fieldNames = Sets.newHashSet();
            Set<String> removedFields = Sets.newHashSet();
            LayoutExt.of(layout).getFormComponents().forEach(formComponent -> {
                List<IFieldSection> fieldSections = formComponent.getFieldSections();
                fieldSections.forEach(fieldSection -> {
                    List<IFormField> formFields = fieldSection.getFields();
                    Iterator<IFormField> iterator = formFields.iterator();
                    while (iterator.hasNext()) {
                        IFormField formField = iterator.next();
                        if (!fieldNames.add(formField.getFieldName())) {
                            iterator.remove();
                            removedFields.add(formField.getFieldName());
                        }
                    }
                    fieldSection.setFields(formFields);
                });
                formComponent.setFieldSections(fieldSections);
            });
            if (!removedFields.isEmpty()) {
                layoutLogicService.updateLayout(context.getUser(), layout);
            }
            result.put(layout.getName(), ImmutableMap.of(ILayout.DISPLAY_NAME, layout.getDisplayName(), "removedFields", removedFields));
        });

        return result;
    }

    @ServiceMethod("remove_empty_tab_in_layout")
    public Map removeEmptyTabInLayout(Map arg, ServiceContext context) {
        String layoutApiName = (String) arg.get("layoutApiName");
        String objectApiName = (String) arg.get("objectApiName");
        if (Strings.isNullOrEmpty(layoutApiName) || Strings.isNullOrEmpty(objectApiName)) {
            throw new ValidateException("param cannot be empty");
        }
        ILayout layout = layoutLogicService.findLayoutByApiName(context.getUser(), layoutApiName, objectApiName);
        if (layout == null) {
            throw new ValidateException("layout not exists");
        }
        List<ITabsComponent> tabsComponents = LayoutExt.of(layout).getTabsComponents();
        if (CollectionUtils.empty(tabsComponents)) {
            throw new ValidateException("tabs not exists");
        }
        boolean changed = false;
        for (ITabsComponent tabsComponent : tabsComponents) {
            List<TabSection> tabSectionList = tabsComponent.getTabs();
            List<List<String>> componentsList = tabsComponent.getComponents();
            if (CollectionUtils.empty(tabSectionList) || CollectionUtils.empty(componentsList)) {
                continue;
            }
            int oldTabSectionSize = tabSectionList.size();
            int oldComponentsSize = componentsList.size();
            tabSectionList.removeIf(x -> Strings.isNullOrEmpty(x.getApiName()));
            tabsComponent.setTabs(tabSectionList);
            componentsList.removeIf(x -> CollectionUtils.empty(x) || x.contains(null) || x.contains("null"));
            tabsComponent.setComponents(componentsList);
            changed = tabSectionList.size() != oldTabSectionSize || componentsList.size() != oldComponentsSize;
        }
        if (changed) {
            layoutLogicService.updateLayout(context.getUser(), layout);
        }
        return ImmutableMap.of(ILayout.NAME, layout.getName(), ILayout.DISPLAY_NAME, layout.getDisplayName(), "changed", changed);
    }

    @ServiceMethod("sync_owner_to_relevant_team")
    public UpdateRelevantTeam.Result syncOwnerToRelevantTeam(ServiceContext context, UpdateRelevantTeam.Arg arg) {
        UpdateRelevantTeam.Result result = new UpdateRelevantTeam.Result(true);
        List<IObjectData> dataList = metaDataService.findObjectDataByIds(context.getTenantId(),
                arg.getIdList(), arg.getObjectApiName());
        if (CollectionUtils.empty(dataList)) {
            result.setSuccess(false);
            return result;
        }
        List<IObjectData> toBeUpdateList = Lists.newArrayList();
        for (IObjectData objectData : dataList) {
            ObjectDataExt dataExt = ObjectDataExt.of(objectData);
            dataExt.getOwnerId().ifPresent(x -> {
                TeamMember ownerMember = new TeamMember(x, TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE);
                dataExt.addTeamMembers(Lists.newArrayList(ownerMember));
            });
            toBeUpdateList.add(objectData);
        }
        if (CollectionUtils.notEmpty(toBeUpdateList)) {
            metaDataService.batchUpdateRelevantTeam(context.getUser(), toBeUpdateList, false);
        }
        return result;
    }

    @ServiceMethod("update_field_describe")
    public Map updateFieldDescribe(Map arg, ServiceContext context) {
        if (CollectionUtils.empty(arg)) {
            throw new ValidateException("Arg can not be empty!");
        }
        String objectApiName = (String) arg.get("objectApiName");
        String fieldApiName = (String) arg.get("fieldApiName");
        Map<String, Object> fieldProps = (Map<String, Object>) arg.get("fieldProps");
        if (Strings.isNullOrEmpty(objectApiName) || Strings.isNullOrEmpty(fieldApiName) || CollectionUtils.empty(fieldProps)) {
            throw new ValidateException("Param can not be empty!");
        }
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), objectApiName);
        Optional<IFieldDescribe> fieldDescribeSilently = ObjectDescribeExt.of(describe).getFieldDescribeSilently(fieldApiName);
        if (!fieldDescribeSilently.isPresent()) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.FIELD_ALREADY_DELETED));
        }
        IFieldDescribe fieldDescribe = fieldDescribeSilently.get();
        boolean changed = false;
        Map fieldMap = FieldDescribeExt.of(fieldDescribe).toMap();
        Set<String> systemProps = Sets.newHashSet(IFieldDescribe.ID, IFieldDescribe.API_NAME, IFieldDescribe.DESCRIBE_API_NAME,
                IFieldDescribe.FIELD_NUM, IFieldDescribe.DEFINE_TYPE, IFieldDescribe.TYPE, IFieldDescribe.IS_ABSTRACT,
                IFieldDescribe.IS_ACTIVE, IFieldDescribe.INDEX_NAME);
        for (Map.Entry<String, Object> entry : fieldProps.entrySet()) {
            if (systemProps.contains(entry.getKey())) {
                continue;
            }
            if (ObjectDataExt.isValueEqual(fieldMap.get(entry.getKey()), entry.getValue(), IFieldType.TEXT)) {
                continue;
            }
            fieldDescribe.set(entry.getKey(), entry.getValue());
            changed = true;
        }
        if (changed) {
            describeLogicService.updateCustomFieldDescribe(context.getUser(), objectApiName, fieldDescribe.toJsonString(),
                    Collections.emptyList(), Collections.emptyList());
        }
        return ImmutableMap.of("changed", changed);
    }

    @ServiceMethod("update_switch_cache")
    public UpdateSwitchCache.Result updateSwitchCache(UpdateSwitchCache.Arg arg, ServiceContext context) {
        Set<String> objectApiNames = getObjectApiNames(context.getTenantId(), arg.getObjectApiNames());

        objectApiNames.forEach(objectApiName -> switchCacheService.syncSwitchStatus(context.getTenantId(), arg.getSwitchType(), objectApiName, Collections.emptyList()));
        return UpdateSwitchCache.Result.builder().success(true).build();
    }

    private Set<String> getObjectApiNames(String tenantId, Set<String> objectApiNames) {
        if (CollectionUtils.notEmpty(objectApiNames)) {
            return objectApiNames;
        }
        List<IObjectDescribe> describes = describeLogicService.findObjectsByTenantId(tenantId, false, false, false, false);
        return describes.stream().map(IObjectDescribe::getApiName).collect(Collectors.toSet());
    }

    @ServiceMethod("update_summary_info_in_layout")
    public Map updateSummaryInfoInLayout(Map arg, ServiceContext context) {
        String objectApiName = (String) arg.get("objectApiName");
        if (Strings.isNullOrEmpty(objectApiName)) {
            throw new ValidateException("objectApiName can not be empty");
        }
        List<String> layoutApiNames = (List<String>) arg.get("layoutApiNames");
        if (CollectionUtils.empty(layoutApiNames)) {
            throw new ValidateException("layoutApiNames can not be empty");
        }
        List<String> fieldApiNames = (List<String>) arg.get("fieldApiNames");
        if (fieldApiNames == null) {
            throw new ValidateException("fieldApiNames can not be null");
        }
        String appId = (String) arg.get("appId");
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), objectApiName);
        fieldApiNames.forEach(x -> {
            if (!ObjectDescribeExt.of(describe).isFieldActive(x)) {
                throw new ValidateException("field:" + x + " is inactive or deleted");
            }
            if (!FieldDescribeExt.of(describe.getFieldDescribe(x)).isNumberTypeField()) {
                throw new ValidateException("field:" + x + " is not number type");
            }
        });

        List<ISummaryComponentInfo> allPageSummaryComponentInfoList = fieldApiNames.stream().distinct()
                .map(x -> {
                    SummaryComponentInfo info = new SummaryComponentInfo(Maps.newHashMap());
                    info.setType(Count.TYPE_SUM);
                    info.setFieldName(x);
                    info.setApiName(AggregateRule.buildAggFieldName(Count.TYPE_SUM, x));
                    return info;
                }).collect(Collectors.toList());

        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), appId);
        List<ILayout> layouts = layoutLogicService.findListLayout(layoutContext, describe);
        layouts.removeIf(x -> !layoutApiNames.contains(x.getName()));
        layoutApiNames.forEach(x -> {
            if (layouts.stream().noneMatch(y -> x.equals(y.getName()))) {
                throw new ValidateException("can not find layout:" + x);
            }
        });
        List<String> updatedLayouts = Lists.newArrayList();
        layouts.forEach(layout -> {
            try {
                ListLayoutExt.of(layout).getFirstListComponent()
                        .ifPresent(it -> it.setAllPageSummaryComponentInfo(allPageSummaryComponentInfoList));
                layoutLogicService.updateListLayout(context.getUser(), layout);
                updatedLayouts.add(layout.getName());
                log.warn("update allPageSummaryInfo in layout success,tenantId:{},objectApiName:{},layoutApiName:{},fieldApiNames:{}",
                        context.getTenantId(), objectApiName, layout.getName(), fieldApiNames);
            } catch (Exception e) {
                log.error("update allPageSummaryInfo in layout failed,tenantId:{},objectApiName:{},layoutApiName:{},fieldApiNames:{}",
                        context.getTenantId(), objectApiName, layout.getName(), fieldApiNames, e);
            }
        });

        return ImmutableMap.of("updatedLayouts", updatedLayouts);
    }

    @ServiceMethod("find_timezone")
    public Map<String, Object> findTenantTimezone(Map arg, ServiceContext context) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("tenant_timezone", TimeZoneContextHolder.getTenantTimeZone().toString());
        result.put("user_timezone", TimeZoneContextHolder.getUserTimeZone().toString());
        return result;
    }

    @ServiceMethod("updateFunctionPrivilege")
    public UpdateFunctionPrivilege.Result updateFunctionPrivilege(UpdateFunctionPrivilege.Arg arg, ServiceContext context) {
        String tenantId = getTenantId(arg, context);
        if (CollectionUtils.empty(arg.getDescribeApiNames())) {
            return new UpdateFunctionPrivilege.Result();
        }
        Map<String, IObjectDescribe> objects = describeLogicService.findObjects(tenantId, arg.getDescribeApiNames());
        for (IObjectDescribe describe : objects.values()) {
            updateFunctionPrivilege(User.systemUser(tenantId), describe, arg.getActionCode());
        }
        return new UpdateFunctionPrivilege.Result();
    }

    private String getTenantId(UpdateFunctionPrivilege.Arg arg, ServiceContext context) {
        String ea = arg.getEa();
        if (Strings.isNullOrEmpty(ea)) {
            return context.getTenantId();
        }
        return gdsHandler.getEIByEA(ea);
    }

    private void updateFunctionPrivilege(User user, IObjectDescribe describe, String actionCode) {
        ObjectAction objectAction = ObjectAction.of(actionCode);
        if (ObjectAction.UNKNOWN_ACTION == objectAction) {
            return;
        }
        List<String> actionCodes = Lists.newArrayList(objectAction.getActionCode());
        functionPrivilegeService.batchCreateFunc(user, describe.getApiName(), actionCodes);
        functionPrivilegeService.updateUserDefinedFuncAccess(user, PrivilegeConstants.ADMIN_ROLE_CODE,
                describe.getApiName(), actionCodes, Lists.newArrayList());
    }

    private void validateArg(UpdateFunctionPrivilege.MultiCodeArg arg) {
        if (Objects.isNull(arg)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        Set<String> actionCodes = CollectionUtils.nullToEmpty(arg.getActionCodes());
        if (CollectionUtils.empty(arg.getDescribeApiNames())
                || Strings.isNullOrEmpty(arg.getEi())
                || CollectionUtils.empty(actionCodes)) {
            log.warn("illegal param: {}", arg);
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
    }

    @ServiceMethod("batchCheckFunctionPrivilege")
    public UpdateFunctionPrivilege.ResultItem batchCheckFunctionPrivilege(
            UpdateFunctionPrivilege.MultiCodeArg arg,
            ServiceContext context) {
        validateArg(arg);
        return processFunctionPrivilege(arg, ctx -> {
            List<String> funCodes = ctx.getFunctions().stream()
                    .map(CreateFunctionPrivilege.FunctionPojo::getFuncCode)
                    .distinct()
                    .collect(Collectors.toList());

            Map<String, List<String>> haveFuncCodesPrivilegeRoles =
                    functionPrivilegeService.getHaveFuncCodesPrivilegeRoles(ctx.getUser(), funCodes);
            boolean success = CollectionUtils.nullToEmpty(haveFuncCodesPrivilegeRoles)
                    .values().stream()
                    .allMatch(x -> CollectionUtils.nullToEmpty(x).contains(PrivilegeConstants.ADMIN_ROLE_CODE));
            return UpdateFunctionPrivilege.ResultItem.builder()
                    .ei(ctx.getUser().getTenantId())
                    .objApis(ctx.getObjApis())
                    .success(success)
                    .build();
        });
    }

    @ServiceMethod("batchUpdateFunctionPrivilege")
    public UpdateFunctionPrivilege.ResultItem batchUpdateFunctionPrivilege(
            UpdateFunctionPrivilege.MultiCodeArg arg,
            ServiceContext context) {
        validateArg(arg);
        return processFunctionPrivilege(arg, ctx -> {
            Set<String> addFunCodes = ctx.getFunctions().stream()
                    .map(CreateFunctionPrivilege.FunctionPojo::getFuncCode)
                    .collect(Collectors.toSet());

            functionPrivilegeService.batchCreateFunc(ctx.getUser(), ctx.getFunctions());
            functionPrivilegeService.updateUserDefinedFuncAccess(
                    ctx.getUser(),
                    PrivilegeConstants.ADMIN_ROLE_CODE,
                    Lists.newArrayList(addFunCodes),
                    Lists.newArrayList()
            );

            return UpdateFunctionPrivilege.ResultItem.builder()
                    .ei(ctx.getUser().getTenantId())
                    .objApis(ctx.getObjApis())
                    .success(true)
                    .build();
        });
    }

    private UpdateFunctionPrivilege.ResultItem processFunctionPrivilege(
            UpdateFunctionPrivilege.MultiCodeArg arg,
            Function<BatchProcessContext, UpdateFunctionPrivilege.ResultItem> processor) {
        String ei = arg.getEi();
        User user = User.systemUser(ei);
        Set<String> objApis = arg.getDescribeApiNames();

        try {
            Map<String, IObjectDescribe> objects = describeLogicService.findObjectsWithoutCopy(ei, objApis);
            List<CreateFunctionPrivilege.FunctionPojo> functions = buildFunctionPojos(user, arg, objects);

            if (CollectionUtils.empty(functions)) {
                return UpdateFunctionPrivilege.ResultItem.builder()
                        .ei(ei)
                        .objApis(objApis)
                        .success(true)
                        .build();
            }

            return processor.apply(new BatchProcessContext(user, functions, objApis));

        } catch (PermissionError permissionError) {
            log.warn("PermissionError, ei: {}, arg: {}", ei, arg);
            return buildErrorResult(ei, objApis, permissionError.getMessage());
        } catch (Exception exception) {
            log.warn("UnKnown Exception, ei: {}, arg: {}", ei, arg);
            return buildErrorResult(ei, objApis, exception.getMessage());
        }
    }

    @lombok.Value
    private static class BatchProcessContext {
        User user;
        List<CreateFunctionPrivilege.FunctionPojo> functions;
        Set<String> objApis;
    }

    private List<CreateFunctionPrivilege.FunctionPojo> buildFunctionPojos(
            User user,
            UpdateFunctionPrivilege.MultiCodeArg arg,
            Map<String, IObjectDescribe> objects) {

        List<CreateFunctionPrivilege.FunctionPojo> functions = Lists.newArrayList();

        for (String actionCode : arg.getActionCodes()) {
            ObjectAction objectAction = ObjectAction.of(actionCode);
            if (ObjectAction.UNKNOWN_ACTION == objectAction) {
                continue;
            }
            functions.addAll(arg.getDescribeApiNames().stream()
                    .filter(x -> Objects.nonNull(objects.getOrDefault(x, null)))
                    .map(describeApiName -> CreateFunctionPrivilege.FunctionPojo.buildFunctionPojo(
                            user.getTenantId(),
                            describeApiName,
                            objectAction.getActionCode())
                    ).collect(Collectors.toList()));
        }

        return functions;
    }

    /**
     * 构建错误结果
     */
    private UpdateFunctionPrivilege.ResultItem buildErrorResult(String ei, Set<String> objApis, String message) {
        return UpdateFunctionPrivilege.ResultItem.builder()
                .ei(ei)
                .objApis(objApis)
                .success(false)
                .failMessage(message)
                .build();
    }

    @ServiceMethod("removeMobileListLayoutSceneInfo")
    public RemoveMobileListLayoutSceneInfo.Result removeMobileListLayoutSceneInfo(RemoveMobileListLayoutSceneInfo.Arg arg, ServiceContext context) {
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());

        for (String layoutApiName : arg.getLayoutApiNames()) {
            dealMobileListLayout(context, describe, layoutApiName);
        }
        return new RemoveMobileListLayoutSceneInfo.Result();
    }

    private void dealMobileListLayout(ServiceContext context, IObjectDescribe describe, String layoutApiName) {
        ILayout layout = layoutLogicService.findListLayoutByApiName(context.getUser(), layoutApiName, describe.getApiName());
        LayoutExt layoutExt = LayoutExt.of(layout);
        if (!layoutExt.isListLayout()) {
            return;
        }
        if (CollectionUtils.empty(layoutExt.getMobileLayout())) {
            return;
        }

        ListLayoutExt mobileLayout = ListLayoutExt.of(layoutExt.getMobileLayout());
        mobileLayout.getFirstListComponent()
                .filter(listComponentExt -> CollectionUtils.notEmpty(listComponentExt.getSceneInfo()))
                .ifPresent(listComponentExt -> {
                    listComponentExt.resetSceneInfos(Lists.newArrayList());
                    layoutExt.setMobileLayout(mobileLayout.toMap());
                    layoutLogicService.updateListLayout(context.getUser(), layoutExt.getLayout());
                });
    }

    @ServiceMethod("batch_update_data")
    public Map batchUpdateObjectData(Map arg, ServiceContext context) {
        if (CollectionUtils.empty(arg)) {
            throw new ValidateException("arg is empty");
        }
        List<String> dataIdList = (List<String>) arg.get("dataIdList");
        if (CollectionUtils.empty(dataIdList)) {
            throw new ValidateException("dataIdList is empty");
        }
        Map<String, Object> fieldMap = (Map<String, Object>) arg.get("fieldMap");
        if (CollectionUtils.empty(fieldMap)) {
            throw new ValidateException("fieldMap is empty");
        }
        String objectApiName = (String) arg.get("objectApiName");
        if (Strings.isNullOrEmpty(objectApiName)) {
            throw new ValidateException("objectApiName is empty");
        }
        List<IObjectData> objectDataList = dataIdList.stream()
                .map(x -> {
                    IObjectData data = new ObjectData();
                    data.setTenantId(context.getTenantId());
                    data.setId(x);
                    data.setDescribeApiName(objectApiName);
                    ObjectDataExt.of(data).putAll(fieldMap);
                    return data;
                })
                .collect(Collectors.toList());
        IActionContext actionContext = ActionContextExt.of(context.getUser()).getContext();
        actionContext.setAllowUpdateInvalid(true);
        List<IObjectData> result = metaDataService.batchUpdateByFields(actionContext, objectDataList, Lists.newArrayList(fieldMap.keySet()));
        return ImmutableMap.of("updateNum", result.size());
    }

    @ServiceMethod("touchDescribe")
    public Map touchDescribe(Map arg, ServiceContext context) {
        String objectApiName = (String) arg.get("objectApiName");
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), objectApiName);
        describeLogicService.touchDescribe(describe);
        return Collections.emptyMap();
    }

    @ServiceMethod("maintainTreePath")
    public Map maintainTreePath(Map arg, ServiceContext context) {
        String objectApiName = (String) arg.get("objectApiName");
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), objectApiName);
        treeViewService.recursiveProcessTreePath(context.getTenantId(), describe);
        return Collections.emptyMap();
    }

    @ServiceMethod("syncCalculateData")
    public SyncCalculateData.Result syncCalculateData(SyncCalculateData.Arg arg, ServiceContext context) {
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        //只允许提交统计字段、计算字段、特殊状态字段的计算任务
        Set<String> fieldNames = arg.getFieldNames();
        if (CollectionUtils.empty(fieldNames)) {
            throw new ValidateException(I18N.text(I18NKey.NO_COUNT_FIELD_NEED_DEAL));
        }
        Set<String> allCalculateFields = ObjectDescribeExt.of(describe).getActiveFieldDescribes().stream()
                .filter(x -> FieldDescribeExt.of(x).isCalculateFieldsNeedStoreInDB())
                .map(x -> x.getApiName())
                .collect(Collectors.toSet());
        fieldNames.removeIf(it -> !allCalculateFields.contains(it));
        if (CollectionUtils.empty(fieldNames)) {
            throw new ValidateException(I18N.text(I18NKey.NO_COUNT_FIELD_NEED_DEAL));
        }

        ParallelUtils.ParallelTask task = ParallelUtils.createBackgroundTask();
        task.submit(() -> {
            SyncCalculateData.CalculateInfo calculateInfo = SyncCalculateData.CalculateInfo.of(describe, fieldNames, context.getUser());
            syncCalculateData(calculateInfo);
        }).run();
//        EventBus eventBus = new AsyncEventBus(executor);
//        eventBus.register(calculateService);
//        SyncCalculateData.CalculateInfo calculateInfo = SyncCalculateData.CalculateInfo.of(describe, fieldNames, context.getUser());
//        eventBus.post(calculateInfo);
        return new SyncCalculateData.Result();
    }

    @Subscribe
    public void syncCalculateData(SyncCalculateData.CalculateInfo calculateInfo) {
        log.warn("syncCalculateData, ei:{}, describeApiName:{}, fields:{}", calculateInfo.getUser().getTenantId(), calculateInfo.getDescribe().getApiName(), calculateInfo.getFieldNames());
        IObjectDescribe describe = calculateInfo.getDescribe();
        User user = calculateInfo.getUser();
        Set<IFieldDescribe> calculateFields = calculateInfo.getCalculateFields();
        Set<Count> countFields = calculateInfo.getCountFields();
        Set<Quote> quoteFields = calculateInfo.getQuoteFields();
        if (CollectionUtils.empty(calculateFields) && CollectionUtils.empty(countFields) && CollectionUtils.empty(quoteFields)) {
            throw new ValidateException(I18N.text(I18NKey.NO_COUNT_FIELD_NEED_DEAL));
        }

        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        LongAdder counter = new LongAdder();
        metaDataService.queryDataAndHandle(user, queryExt.toSearchTemplateQuery(), describe, AppFrameworkConfig.getSyncCalculateDataDataBatchSize(), AppFrameworkConfig.getSyncCalculateDataMaxQueryCount(), true, queryResult -> {
            counter.increment();
            List<IObjectData> dataList = queryResult.getData();
            GuavaLimiter.acquire("limit-sync-calculate-data-rate", user.getTenantId(), dataList.size());
            calculateCount(user, describe, dataList, countFields);
            calculateFormula(user, describe, dataList, calculateFields);
            calculateQuote(user, describe, dataList, quoteFields);
            log.info("queryDataAndHandleCalculateData, ei:{}, describeApiName:{},counter:{}", user.getTenantId(), describe.getApiName(), counter);
        });
        log.warn("syncCalculateData end, ei:{}, describeApiName:{}, fields:{}", calculateInfo.getUser().getTenantId(), calculateInfo.getDescribe().getApiName(), calculateInfo.getFieldNames());
    }

    private void calculateQuote(User user, IObjectDescribe describe, List<IObjectData> dataList, Set<Quote> quoteFields) {
        quoteValueService.fillQuoteFieldValue(user, dataList, describe,
                null, true, Lists.newArrayList(quoteFields), null, true);
        ActionContextExt actionContext = ActionContextExt.of(user).setBatch(true);
        List<String> fields = quoteFields.stream()
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toList());
        metaDataService.batchUpdateWithFieldsForCalculateToPG(actionContext.getContext(), dataList, fields);
    }

    private void calculateFormula(User user, IObjectDescribe describe, List<IObjectData> dataList, Set<IFieldDescribe> calculateFields) {
        if (CollectionUtils.empty(calculateFields) || CollectionUtils.empty(dataList)) {
            return;
        }
        expressionCalculateLogicService.bulkCalculateWithDependentData(describe, dataList, Lists.newArrayList(calculateFields), null, true);
        ActionContextExt actionContext = ActionContextExt.of(user).setBatch(true);
        List<String> fields = calculateFields.stream()
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toList());
        metaDataService.batchUpdateWithFieldsForCalculateToPG(actionContext.getContext(), dataList, fields);
    }

    private void calculateCount(User user, IObjectDescribe describe, List<IObjectData> dataList, Set<Count> countFields) {
        if (CollectionUtils.empty(countFields) || CollectionUtils.empty(dataList)) {
            return;
        }
        List<String> dataIds = dataList.stream()
                .map(IObjectData::getId)
                .distinct()
                .collect(Collectors.toList());
        metaDataService.calculateAndUpdateCountFields(user, dataIds, describe, Lists.newArrayList(countFields));
    }

    @ServiceMethod("asyncStartApproval")
    public AsyncStartApproval.Result asyncStartApproval(AsyncStartApproval.Arg arg, ServiceContext context) {
        Set<String> dataIds = arg.getDataIds();
        String describeApiName = arg.getDescribeApiName();
        if (CollectionUtils.empty(dataIds) || Strings.isNullOrEmpty(describeApiName)) {
            return new AsyncStartApproval.Result();
        }
        boolean isWorkFlowEnabled = arg.getTriggerWorkflow();
        Map<String, Map<String, Object>> callbackDataMap = Maps.newHashMap();
        List<Tuple<String, String>> objetcs = Lists.newArrayList();
        for (String dataId : dataIds) {
            objetcs.add(Tuple.of(dataId, describeApiName));
            Map<String, Object> map = ImmutableMap.of(ExtraDataKeys.TRIGGER_WORK_FLOW, isWorkFlowEnabled);
            callbackDataMap.put(dataId, map);
        }
        // 触发审批流程
        approvalFlowService.batchStartApprovalAsynchronous(context.getUser(), objetcs, ApprovalFlowTriggerType.CREATE, Maps.newHashMap(), callbackDataMap);
        return new AsyncStartApproval.Result();
    }

    @ServiceMethod("updateFieldOptionSet")
    public UpdateFieldOptionSet.Result updateFieldOptionSet(UpdateFieldOptionSet.Arg arg, ServiceContext context) {
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());

        Map<String, String> fieldOptionMap = arg.getFieldOptionSetPairs().stream()
                .collect(Collectors.toMap(UpdateFieldOptionSet.FieldOptionSetPair::getFieldName, UpdateFieldOptionSet.FieldOptionSetPair::getOptionApiName,
                        (x, y) -> x));
        Map<String, IFieldDescribe> fieldDescribeMap = ObjectDescribeExt.of(describe)
                .getFieldByApiNames(Lists.newArrayList(fieldOptionMap.keySet()))
                .stream()
                .collect(Collectors.toMap(IFieldDescribe::getApiName, Function.identity()));

        Map<String, MtOptionSet> optionSetMap = optionSetLogicService.findByApiNames(context.getUser(), fieldOptionMap.values());

        Map<String, UpdateFieldOptionSet.ErrorInfo> errorInfoMap = Maps.newHashMap();
        List<IFieldDescribe> toUpdateFields = Lists.newArrayList();
        for (UpdateFieldOptionSet.FieldOptionSetPair pair : arg.getFieldOptionSetPairs()) {
            String fieldName = pair.getFieldName();
            String optionApiName = pair.getOptionApiName();
            IFieldDescribe fieldDescribe = fieldDescribeMap.get(fieldName);
            MtOptionSet mtOptionSet = optionSetMap.get(optionApiName);
            checkFieldAndOption(errorInfoMap, fieldName, optionApiName, fieldDescribe, mtOptionSet);
            // 修改选项集依赖
            if (!errorInfoMap.containsKey(fieldName)) {
                SelectOne selectOne = FieldDescribeExt.of(Maps.newHashMap(FieldDescribeExt.of(fieldDescribe).toMap())).getFieldDescribe();
                SelectOneExt.of(selectOne).setOptionApiName(optionApiName);
                toUpdateFields.add(selectOne);
            }
        }
        // 更新描述
        if (CollectionUtils.empty(errorInfoMap)) {
            describeLogicService.updateFieldDescribe(describe, Lists.newArrayList(toUpdateFields));
        }
        return UpdateFieldOptionSet.Result.of(errorInfoMap.values());
    }

    private void checkFieldAndOption(Map<String, UpdateFieldOptionSet.ErrorInfo> errorInfoMap, String fieldName, String optionApiName,
                                     IFieldDescribe fieldDescribe, MtOptionSet mtOptionSet) {
        // 字段不存在
        if (Objects.isNull(fieldDescribe)) {
            errorInfoMap.computeIfAbsent(fieldName, key -> new UpdateFieldOptionSet.ErrorInfo(fieldName))
                    .addMessage(String.format("field :%s, not found", fieldName));
        } else {
            // 字段不是单选\多选
            FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
            if (!(fieldDescribeExt.isSelectOne() || fieldDescribeExt.isSelectMany())) {
                errorInfoMap.computeIfAbsent(fieldName, key -> new UpdateFieldOptionSet.ErrorInfo(fieldName))
                        .addMessage(String.format("field :%s, not is select field", fieldName));
            }
        }
        // 选项集不存在
        if (Objects.isNull(mtOptionSet)) {
            errorInfoMap.computeIfAbsent(fieldName, key -> new UpdateFieldOptionSet.ErrorInfo(fieldName))
                    .addMessage(String.format("optionSet :%s, not found", optionApiName));
        }
    }

    @ServiceMethod("decodeMaskFieldEncryptValue")
    public MaskFieldEncrypt.Result decodeMaskFieldEncryptValue(MaskFieldEncrypt.Arg arg, ServiceContext context) {
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        IObjectData objectData = arg.getObjectData().toObjectData();
        maskFieldLogicService.decodeMaskFieldEncryptValue(context.getUser(), Lists.newArrayList(objectData), describe);
        return MaskFieldEncrypt.Result.of(ObjectDataDocument.of(objectData));
    }

    @ServiceMethod("resetAutoNumber")
    public ResetAutoNumber.Result resetAutoNumber(ResetAutoNumber.Arg arg, ServiceContext context) {
        autoNumberLogicService.resetAutoNumber(context.getUser(), arg.getDescribeApiName(), arg.getFieldApiName(),
                arg.getCondition(), arg.getCounter(), arg.getStartNumber());
        return new ResetAutoNumber.Result();
    }

    @ServiceMethod("correctLayoutComponents")
    public CorrectLayoutComponents.Result correctLayoutComponents(CorrectLayoutComponents.Arg arg, ServiceContext context) {
        ILayout layout = layoutLogicService.findListLayoutByApiName(context.getUser(), arg.getLayoutApiName(), arg.getDescribeApiName());
        if (Objects.isNull(layout)) {
            return new CorrectLayoutComponents.Result();
        }
        LayoutExt layoutExt = LayoutExt.of(layout);
        // 暂时只处理详情页V3布局的文本框组件
        if (!layoutExt.isDetailLayout() || !layoutExt.isV3Layout()) {
            return new CorrectLayoutComponents.Result();
        }
        Set<String> textComponentNames = layoutExt.getComponentsSilently().stream()
                .filter(it -> ComponentExt.of(it).isTextComponent())
                .map(IComponent::getName)
                .collect(Collectors.toSet());
        if (CollectionUtils.empty(textComponentNames)) {
            return new CorrectLayoutComponents.Result();
        }
        List<Map> rows = (List<Map>) layoutExt.getLayoutStructure().get(LayoutStructure.LAYOUT);
        Set<String> components = Sets.newHashSet();
        for (Map row : rows) {
            List<List<String>> columns = (List<List<String>>) row.get(LayoutStructure.COMPONENTS);
            columns.forEach(column -> components.addAll(findComponentsByContainer(layoutExt, column)));
        }
        textComponentNames.removeAll(components);
        layoutExt.removeComponents(Lists.newArrayList(textComponentNames));
        if (arg.isUpdateLayout()) {
            layoutLogicService.updateLayout(context.getUser(), layout);
        }
        return CorrectLayoutComponents.Result.builder()
                .layout(LayoutDocument.of(layout))
                .build();
    }

    private Set<String> findComponentsByContainer(LayoutExt layoutExt, List<String> components) {
        Set<String> result = Sets.newHashSet();
        if (CollectionUtils.empty(components)) {
            return Sets.newHashSet();
        }
        result.addAll(components);
        for (IComponent component : layoutExt.getComponentByApiNames(components)) {
            ComponentExt componentExt = ComponentExt.of(component);
            if (componentExt.isContainerComponent()) {
                List<String> childComponents = componentExt.getChildComponents();
                Set<String> resultComponents = findComponentsByContainer(layoutExt, childComponents);
                result.addAll(resultComponents);
            }
        }
        return result;
    }

    @ServiceMethod("delete_data_snapshot")
    public Map deleteDataSnapshot(Map arg, ServiceContext context) {
        if (CollectionUtils.empty(arg)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_EMPTY));
        }
        String describeApiName = (String) arg.get("describeApiName");
        if (Strings.isNullOrEmpty(describeApiName)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        String biz = (String) arg.get("biz");
        if (Strings.isNullOrEmpty(biz)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        List<Map> paras = (List<Map>) arg.get("paras");
        if (CollectionUtils.empty(paras)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        paras.forEach(para -> {
            String dataId = (String) para.get("dataId");
            String bizId = (String) para.get("bizId");
            if (StringUtils.isAnyEmpty(dataId, bizId)) {
                throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
            }
            dataSnapshotLogicService.deleteSnapshot(context.getUser(), describeApiName, dataId, biz, bizId);
        });
        return Collections.emptyMap();
    }

    @ServiceMethod("update_data_snapshot")
    public Map updateDataSnapshot(DataSnapshot arg, ServiceContext context) {
        dataSnapshotLogicService.updateSnapshot(context.getUser(), arg);
        return Collections.emptyMap();
    }

    @ServiceMethod("correctLayoutButtons")
    public Map<String, Object> correctLayoutButtons(ServiceContext context, Map<String, Object> arg) {
        String describeApiName = (String) arg.get("describe_api_name");
        String layoutName = (String) arg.get("layout_name");
        ILayout layout = layoutLogicService.findLayoutByApiName(context.getUser(), layoutName, describeApiName);
        if (layout == null) {
            throw new ValidateException("layout not exists");
        }
        LayoutExt layoutExt = LayoutExt.of(layout);
        if (!layoutExt.isDetailLayout()) {
            return Maps.newHashMap();
        }
        if (!layoutExt.isEnableMobileLayout()) {
            return Maps.newHashMap();
        }
        LayoutExt mobileLayout = LayoutExt.of(layoutExt.getMobileLayout());
        List<IButton> buttonOrder = mobileLayout.getButtonOrder();
        if (CollectionUtils.empty(buttonOrder)) {
            return Maps.newHashMap();
        }
        List<IButton> buttons = buttonOrder.stream()
                .filter(button -> !Strings.isNullOrEmpty(button.getName()))
                .collect(Collectors.toList());
        mobileLayout.setButtonOrder(buttons);
        layoutLogicService.updateLayout(context.getUser(), layout);
        return ImmutableMap.of("success", "true");
    }

    @ServiceMethod("delete_layout")
    @Transactional
    public Map<String, Object> deleteLayout(ServiceContext context, Map<String, Object> arg) {
        String describeApiName = (String) arg.get("describeApiName");
        String layoutApiName = (String) arg.get("layoutApiName");
        if (Strings.isNullOrEmpty(describeApiName) || Strings.isNullOrEmpty(layoutApiName)) {
            throw new ValidateException("describeApiName or layoutApiName is empty");
        }
        log.warn("delete layout, describeApiName: {}, layoutApiName: {}", describeApiName, layoutApiName);
        ILayout layout = layoutLogicService.findLayoutByApiName(context.getUser(), layoutApiName, describeApiName);
        if (layout == null) {
            throw new ValidateException("layout not exists");
        }
        if (Boolean.TRUE.equals(layout.isDefault())) {
            layout.setIsDefault(false);
            layoutLogicService.updateLayout(context.getUser(), layout, false);
        }
        layoutLogicService.deleteLayout(context.getUser(), layout);
        log.warn("delete layout success, describeApiName: {}, layoutApiName: {}", describeApiName, layoutApiName);
        return ImmutableMap.of("success", "true", "layout", layout.getName() + "-" + layout.getDisplayName());
    }

    @ServiceMethod("createSystemButton")
    public Map<String, Object> createSystemButton(ServiceContext serviceContext, Map<String, Object> arg) {
        User user = serviceContext.getUser();
        ButtonExt buttonExt = ButtonExt.of(arg);
        buttonExt.setId(IdGenerator.get());
        buttonExt.setTenantId(user.getTenantId());
        buttonExt.setCreatedBy(user.getUserId());
        buttonExt.setLastModifiedBy(user.getUserId());

        try {
            buttonService.create(buttonExt.getButton());
        } catch (MetadataServiceException e) {
            log.warn("create system button failed, button: {}", buttonExt);
            throw new MetaDataBusinessException(e.getMessage());
        }
        return ImmutableMap.of("success", "true", "buttonExt", buttonExt.getLabel() + "-" + buttonExt.getApiName());
    }

    @ServiceMethod("validateDefaultValue")
    public ValidateDefaultValue.Result validateDefaultValue(ServiceContext serviceContext, ValidateDefaultValue.Arg arg) {
        IObjectData objectData = arg.getObjectData().toObjectData();
        IObjectDescribe objectDescribe = describeLogicService.findObject(serviceContext.getTenantId(), objectData.getDescribeApiName());

        Map<String, IObjectDescribe> detailDescribeMap = describeLogicService.findDetailDescribes(serviceContext.getTenantId(), objectData.getDescribeApiName()).stream()
                .collect(Collectors.toMap(IObjectDescribe::getApiName, Function.identity()));

        Map<String, List<IObjectData>> detailObjectData = initDetailObjectData(serviceContext, arg);

        IObjectData oldMasterData = findDbMasterData(serviceContext, objectData, objectDescribe);
        List<IObjectDescribe> detailDescribes = Lists.newArrayList(detailDescribeMap.values()).stream()
                .filter(x -> detailObjectData.containsKey(x.getApiName())).collect(Collectors.toList());
        Map<String, List<IObjectData>> dbDetailDataMap = findDbDetailData(serviceContext, oldMasterData, detailDescribes);

        // 编辑场景需要合并数据
        if (Objects.nonNull(oldMasterData)) {
            ObjectDataExt.mergeWithDbData(Lists.newArrayList(objectData), Lists.newArrayList(oldMasterData));
            ObjectDataExt.mergeWithDbData(detailObjectData, dbDetailDataMap);
        }

        // 构造参数
        EditCalculateParam calculateParam = buildEditCalculateParam(objectDescribe, objectData, oldMasterData,
                detailDescribeMap, detailObjectData, dbDetailDataMap, arg.getMasterCalculateFields(), arg.getDetailCalculateFields());
        // 计算
        metaDataComputeService.calculateForEditData(serviceContext.getUser(), calculateParam);

        // diff
        Map<String, Object> masterDataDiff = calculateParam.diffMasterWithDefaultValue(objectData);
        Map<String, List<Map<String, Object>>> detailsDataDiff = calculateParam.diffDetailsWithDefaultValue(detailObjectData);

        return ValidateDefaultValue.Result.builder()
                .masterDataDiff(masterDataDiff)
                .detailsDataDiff(detailsDataDiff)
                .build();
    }

    private Map<String, List<IObjectData>> findDbDetailData(ServiceContext serviceContext, IObjectData objectData, List<IObjectDescribe> detailDescribes) {
        if (Objects.isNull(objectData) || Strings.isNullOrEmpty(objectData.getId())) {
            return Maps.newHashMap();
        }
        return metaDataService.findDetailObjectDataList(detailDescribes, objectData, serviceContext.getUser());
    }

    private IObjectData findDbMasterData(ServiceContext serviceContext, IObjectData objectData, IObjectDescribe objectDescribe) {
        String dataId = objectData.getId();
        if (Strings.isNullOrEmpty(dataId)) {
            return null;
        }
        return metaDataService.findObjectData(serviceContext.getUser(), dataId, objectDescribe);
    }

    private EditCalculateParam buildEditCalculateParam(IObjectDescribe objectDescribe, IObjectData objectData, IObjectData oldMasterData,
                                                       Map<String, IObjectDescribe> detailDescribeMap,
                                                       Map<String, List<IObjectData>> detailObjectData,
                                                       Map<String, List<IObjectData>> dbDetailDataMap,
                                                       Set<String> masterCalculateFields, Map<String, Map<String, Set<String>>> detailCalculateFields) {
        IObjectData masterData = ObjectDataExt.of(objectData).copy();
        Map<String, List<IObjectData>> detailDataMap = ObjectDataExt.copyMap(detailObjectData);
        Map<String, List<IObjectData>> oldDetailDataMap = ObjectDataExt.copyMap(dbDetailDataMap);
        ObjectDataExt.fillDataIndex(detailDataMap, oldDetailDataMap);

        FieldRelationGraph graph = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(objectDescribe, Lists.newArrayList(detailDescribeMap.values()),
                false, true, true);
        return EditCalculateParam.builder()
                .masterData(masterData)
                .detailDataMap(detailDataMap)
                .oldMasterData(oldMasterData)
                .oldDetailDataMap(oldDetailDataMap)
                .masterDescribe(objectDescribe)
                .detailDescribeMap(detailDescribeMap)
                .excludeDefaultValue(false)
                .includeQuoteField(true)
                .excludeLookupRelateField(false)
                .filterDefaultValueByCalculateFields(true)
                .masterCalculateFields(masterCalculateFields)
                .detailCalculateFields(detailCalculateFields)
                .getDataIndex(data -> ObjectDataExt.of(data).getDataIndex())
                .graph(graph)
                .build()
                .initModifyData(fieldRelationCalculateService, ObjectDataExt::setDataIndex);
    }

    private Map<String, List<IObjectData>> initDetailObjectData(ServiceContext serviceContext, ValidateDefaultValue.Arg arg) {
        Map<String, List<IObjectData>> detailObjectData = Maps.newHashMap();
        arg.getDetails()
                .forEach((key, value) -> detailObjectData.put(key, value.stream()
                        .map(ObjectDataDocument::toObjectData)
                        .peek(x -> {
                            x.setTenantId(serviceContext.getTenantId());
                            x.setDescribeApiName(key);
                        })
                        .collect(Collectors.toList())));
        return detailObjectData;
    }

    @ServiceMethod("getByI18nKey")
    public Map<String, String> getByI18nKey(Map<String, String> arg, ServiceContext context) {
        String key = arg.get("key");
        String text = I18NExt.text(key);
        return ImmutableMap.of("key", key, "text", text);
    }

    @ServiceMethod("enterprise_edition")
    public Map<String, Object> getSimpleEnterpriseData(ServiceContext context) {
        EnterpriseInfoDto.FindEIRequest arg = new EnterpriseInfoDto.FindEIRequest();
        arg.setEnterpriseId(Integer.parseInt(context.getTenantId()));
        EnterpriseInfoDto.FindEIResult enterprise = enterpriseRemoteService.findEnterpriseByEI(arg);
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("enterprise", enterprise);
        return resultMap;
    }

    @ServiceMethod("delete_directly_describe_custom_fields")
    public Map<String, String> deleteDirectlyDescribeCustomFields(Map<String, String> arg, ServiceContext serviceContext) {
        User user = serviceContext.getUser();
        String describeApiName = arg.get("describeApiName");
        IObjectDescribe objectDescribe = describeLogicService.findObject(user.getTenantId(), describeApiName);
        List<String> fieldNames = ObjectDescribeExt.of(objectDescribe).stream().
                filter(it -> FieldDescribeExt.of(it).isCustomField())
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toList());
        describeLogicService.deleteFieldDirect(user, objectDescribe, fieldNames);
        return ImmutableMap.of("success", "true");
    }

    @ServiceMethod("batch_update_button_url")
    public BatchUpdateButtonUrl.Result batchUpdateButtonUrl(BatchUpdateButtonUrl.Arg arg, ServiceContext serviceContext) {
        if (Objects.isNull(arg)) {
            throw new ValidateException("Arg can not be null");
        }

        if (Objects.isNull(arg.getButtonInfo()) || CollectionUtils.empty(arg.getTenants())) {
            throw new ValidateException("Param not enough");
        }

        List<String> failTenants = Lists.newArrayList();
        int count = 0;
        for (String ei : arg.getTenants()) {
            User user = User.systemUser(ei);
            try {
                customButtonService.updateButtonUrl(user, arg.getButtonInfo().getObjectDescribeApiName(),
                        arg.getButtonInfo().getButtonURL(), arg.getButtonInfo().getButtonApiNames());
                messagePollingService.sendPollingMessage(user, "paas_ui_button", PollingMsgEndType.ALL, false);
            } catch (Exception e) {
                failTenants.add(ei);
                log.warn("batch_update_button_url fail for ei:{}", ei, e);
            } finally {
                count++;
                if (count % 10 == 0) {
                    log.warn("current:{}", count);
                }
            }
        }

        return BatchUpdateButtonUrl.Result.builder().failTenants(failTenants).build();
    }

    @ServiceMethod("openApplicationLayer")
    public OpenApplicationLayer.Result openApplicationLayer(OpenApplicationLayer.Arg arg, ServiceContext context) {
        applicationLayeredLogicService.enableApplicationLayer(context.getUser(), arg.getAppId(), arg.getDescribeApiName(), arg.isHandleLayout());
        return new OpenApplicationLayer.Result();
    }


    @ServiceMethod("closeApplicationLayer")
    public OpenApplicationLayer.Result closeApplicationLayer(OpenApplicationLayer.Arg arg, ServiceContext context) {
        applicationLayeredLogicService.closeApplicationLayer(context.getUser(), arg.getAppId(), arg.getDescribeApiName(), arg.isHandleLayout());
        return new OpenApplicationLayer.Result();
    }

    @ServiceMethod("updateBizTypeByRuleApiName")
    public UpdateBizType.Result updateBizTypeByRuleApiName(UpdateBizType.Arg arg, ServiceContext context) {
        // 验证参数
        if (Strings.isNullOrEmpty(arg.getRuleApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        // 查找规则
        List<IObjectMappingRuleInfo> rules = objectMappingService.findByApiName(context.getUser(), arg.getRuleApiName());
        if (CollectionUtils.empty(rules)) {
            throw new ValidateException(I18N.text(I18NKey.RULE_NOT_FOUND));
        }
        // 更新规则的业务类型
        rules.forEach(rule -> rule.setBizType(arg.getBizType()));
        objectMappingService.updateRule(context.getUser(), rules);
        return UpdateBizType.Result.builder().success(true).build();
    }

    @ServiceMethod("setupTeamInterconnectedDepartmentsByIds")
    public SetupTeamInterconnectedDepartmentsByIds.Result setupTeamInterconnectedDepartmentsByIds(SetupTeamInterconnectedDepartmentsByIds.Arg arg,
                                                                                                  ServiceContext context) {
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopy(context.getTenantId(), arg.getDescribeApiName());
        List<IObjectData> dataList = metaDataService.setupTeamInterconnectedDepartmentsByIds(context.getUser(), objectDescribe, arg.getDataIds());
        if (arg.needUpdateTeam()) {
            metaDataService.batchUpdateRelevantTeam(context.getUser(), dataList, false);
        }
        return SetupTeamInterconnectedDepartmentsByIds.Result.builder()
                .objectDataList(ObjectDataDocument.ofList(dataList))
                .build();
    }

    /**
     * 将GDPR合规性设置从GdprCompliance表迁移到字段扩展属性中
     *
     * @param context 服务上下文
     * @return 迁移结果
     */
    @ServiceMethod("migrate_field_compliance_setting")
    public MultiTenantMigrationResult migrateFieldComplianceSetting(MigrateFieldComplianceSettingArg arg, ServiceContext context) {
        MultiTenantMigrationResult multiResult = new MultiTenantMigrationResult();

        // 如果没有提供企业ID列表或为空，则只处理当前企业
        List<String> tenantIds = (arg != null && arg.getTenantIds() != null && !arg.getTenantIds().isEmpty())
                ? arg.getTenantIds()
                : Lists.newArrayList(context.getTenantId());

        log.info("Start migrating field compliance settings, total enterprises: {}", tenantIds.size());

        // 遍历所有企业进行迁移
        for (String tenantId : tenantIds) {
            try {
                // 为每个企业创建系统用户
                User user = User.systemUser(tenantId);

                // 执行单个企业的迁移
                MigrationResult tenantResult = migrateForSingleTenant(user);

                // 添加到多企业结果中
                multiResult.addTenantResult(tenantId, tenantResult);

                log.info("Enterprise {} field compliance settings migration completed, result: total objects-{}, success-{}, failed-{}",
                        tenantId, tenantResult.getTotalObjects(), tenantResult.getSuccessCount(),
                        tenantResult.getFailedObjects().size());
            } catch (Exception e) {
                // 处理企业级异常
                log.error("Enterprise {} field compliance settings migration failed", tenantId, e);
                MigrationResult errorResult = new MigrationResult();
                errorResult.setErrorMessage("Migration process exception: " + e.getMessage());
                multiResult.addTenantResult(tenantId, errorResult);
            }
        }

        log.info("Field compliance settings migration task completed, total enterprises: {}, success: {}, failed: {}",
                multiResult.getTotalTenants(), multiResult.getSuccessTenants(), multiResult.getFailedTenants());

        return multiResult;
    }

    /**
     * 为单个企业执行字段合规性设置迁移
     *
     * @param user 用户，代表企业
     * @return 单个企业的迁移结果
     */
    private MigrationResult migrateForSingleTenant(User user) {
        try {
            String tenantId = user.getTenantId();

            // 灰度判断，确保只有在灰度开启的情况下才能执行迁移
            if (!UdobjGrayConfig.isAllow(UdobjGrayKey.FIELD_COMPLIANCE_SETTING_GRAY, tenantId)) {
                MigrationResult result = new MigrationResult();
                result.setErrorMessage("Field compliance settings feature is not enabled, cannot migrate");
                return result;
            }

            // 结果统计
            MigrationResult result = new MigrationResult();

            // 查询租户级GDPR开启状态
            Optional<GdprCompliance> compliance = gdprService.findGdprComplianceData(user);
            if (compliance.isPresent()) {
                GdprCompliance complianceData = compliance.get();
                List<String> apiNames = complianceData.getApiNames();

                // 1. 查询所有GdprCompliance记录
                List<GdprCompliance> complianceList = gdprService.findGdprComplianceListByApiNames(user, apiNames);
                result.setTotalObjects(complianceList.size());

                // 2. 对每个对象进行迁移
                for (GdprCompliance gdprCompliance : complianceList) {
                    try {
                        gdprService.migrateObjectFieldsToExtra(user, gdprCompliance);
                        result.incrementSuccessCount();
                    } catch (Exception e) {
                        // 记录失败信息
                        result.addFailedObject(gdprCompliance.getApiName(), e.getMessage());
                        log.error("Failed to migrate object field security level: {}, Enterprise ID: {}", gdprCompliance.getApiName(), tenantId, e);
                    }
                }

                // 3. 记录迁移日志
                log.info("Enterprise {} field compliance settings migration completed: total objects-{}, success-{}, failed-{}",
                        tenantId, result.getTotalObjects(), result.getSuccessCount(), result.getFailedObjects().size());
            } else {
                result.setErrorMessage("Enterprise has not enabled field compliance settings feature");
            }
            return result;
        } catch (Exception e) {
            log.error("Field compliance settings migration failed", e);
            MigrationResult result = new MigrationResult();
            result.setErrorMessage("Migration process exception: " + e.getMessage());
            return result;
        }
    }

    @ServiceMethod("calculateForAddAction")
    public CalculateForAdd.Result calculateForAddAction(CalculateForAdd.Arg arg, ServiceContext context) {
        StopWatch stopWatch = StopWatch.create(getClass().getName() + "#calculateForAddAction");
        IObjectData objectData = arg.getObjectData().toObjectData();
        Map<String, List<IObjectData>> detailObjectData = ObjectDataDocument.ofDataMap(arg.getDetails());

        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopy(context.getTenantId(), arg.getDescribeApiName());
        List<IObjectDescribe> objectDescribes = describeLogicService.findDetailDescribes(context.getTenantId(), arg.getDescribeApiName());
        List<IObjectDescribe> detailDescribes = objectDescribes.stream()
                .filter(x -> detailObjectData.containsKey(x.getApiName()))
                .collect(Collectors.toList());
        stopWatch.lap("findDescribe");
        //计算主对象和从对象的计算字段和主对象统计从对象的统计字段
        CalculateFields calculateFields = fieldRelationCalculateService.computeCalculateFieldsForAddAction(objectDescribe, detailDescribes, true);
        stopWatch.lap("computeCalculateFieldsForAddAction");
        metaDataComputeService.batchCalculateBySortFields(context.getUser(), objectData, detailObjectData, calculateFields);
        stopWatch.lap("batchCalculateBySortFields");
        stopWatch.logSlow(300);
        return CalculateForAdd.Result.builder()
                .objectData(ObjectDataDocument.of(objectData))
                .details(ObjectDataDocument.ofMap(detailObjectData))
                .build();
    }
}
