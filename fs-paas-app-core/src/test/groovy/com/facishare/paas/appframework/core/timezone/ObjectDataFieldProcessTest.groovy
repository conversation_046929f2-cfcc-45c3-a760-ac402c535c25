package com.facishare.paas.appframework.core.timezone

import com.facishare.paas.appframework.core.timezone.annotation.ObjectDataField
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import org.aspectj.weaver.bcel.UnwovenClassFile
import spock.lang.Specification

import java.util.function.Function

/**
 * GenerateByAI
 * 测试内容描述：ObjectDataFieldProcess对象数据字段处理器的单元测试
 */
class ObjectDataFieldProcessTest extends Specification {

    def mockFunction = Mock(Function)
    def mockDescribe = Mock(IObjectDescribe)

    def setup() {
        mockFunction.apply(_) >> mockDescribe
    }

    // 测试用的类
    static class TestObject {
        @ObjectDataField(ObjectDataField.Type.SINGLE)
        private Map<String, Object> singleField

        @ObjectDataField(ObjectDataField.Type.LIST)
        private List<Map<String, Object>> listField

        @ObjectDataField(ObjectDataField.Type.LIST_MAP)
        private Map<String, List<Map<String, Object>>> listMapField

        @ObjectDataField(ObjectDataField.Type.MAP_MAP)
        private Map<String, Map<String, Object>> mapMapField

        private String noAnnotationField

        // 构造函数和getter/setter
        TestObject() {
            singleField = ["id": "1", "describeApiName": "TestApi"]
            listField = [["id": "1"], ["id": "2"]]
            listMapField = ["api1": [["id": "1"], ["id": "2"]]]
            mapMapField = ["api1": ["key1": ["id": "1"]]]
            noAnnotationField = "test"
        }

        Map<String, Object> getSingleField() { return singleField }
        void setSingleField(Map<String, Object> value) { this.singleField = value }

        List<Map<String, Object>> getListField() { return listField }
        void setListField(List<Map<String, Object>> value) { this.listField = value }

        Map<String, List<Map<String, Object>>> getListMapField() { return listMapField }
        void setListMapField(Map<String, List<Map<String, Object>>> value) { this.listMapField = value }

        Map<String, Map<String, Object>> getMapMapField() { return mapMapField }
        void setMapMapField(Map<String, Map<String, Object>> value) { this.mapMapField = value }

        String getNoAnnotationField() { return noAnnotationField }
        void setNoAnnotationField(String value) { this.noAnnotationField = value }
    }

    static class NestedTestObject {
        @ObjectDataField(ObjectDataField.Type.SINGLE)
        private Map<String, Object> field

        private TestObject nestedObject

        NestedTestObject() {
            field = ["id": "nested", "describeApiName": "NestedApi"]
            nestedObject = new TestObject()
        }

        Map<String, Object> getField() { return field }
        void setField(Map<String, Object> value) { this.field = value }

        TestObject getNestedObject() { return nestedObject }
        void setNestedObject(TestObject value) { this.nestedObject = value }
    }

    static class IterableTestObject {
        @ObjectDataField(ObjectDataField.Type.SINGLE)
        private Map<String, Object> field

        private List<TestObject> objectList

        IterableTestObject() {
            field = ["id": "iterable", "describeApiName": "IterableApi"]
            objectList = [new TestObject(), new TestObject()]
        }

        Map<String, Object> getField() { return field }
        void setField(Map<String, Object> value) { this.field = value }

        List<TestObject> getObjectList() { return objectList }
        void setObjectList(List<TestObject> value) { this.objectList = value }
    }

    def "test convert2SystemZone with null value"() {
        when: "转换null值到系统时区"
        ObjectDataFieldProcess.convert2SystemZone(null, mockFunction)

        then: "应该正常执行不抛出异常"
        noExceptionThrown()
        0 * mockFunction.apply(_)
    }

    def "test convert2SystemZone with null function throws exception"() {
        given: "准备测试对象"
        def testObject = new TestObject()

        when: "使用null函数转换"
        ObjectDataFieldProcess.convert2SystemZone(testObject, null)

        then: "应该抛出NullPointerException"
        thrown(NullPointerException)
    }

    def "test convert2SystemZone with valid object"() {
        given: "准备测试对象"
        def testObject = new TestObject()

        when: "转换对象到系统时区"
        ObjectDataFieldProcess.convert2SystemZone(testObject, mockFunction)

        then: "应该正常执行"
        noExceptionThrown()
        // 验证函数被调用
        mockFunction.apply(_) >> mockDescribe
    }

    def "test convert2CustomZone with null value"() {
        when: "转换null值到自定义时区"
        ObjectDataFieldProcess.convert2CustomZone(null, mockFunction)

        then: "应该正常执行不抛出异常"
        noExceptionThrown()
        0 * mockFunction.apply(_)
    }

    def "test convert2CustomZone with null function throws exception"() {
        given: "准备测试对象"
        def testObject = new TestObject()

        when: "使用null函数转换"
        ObjectDataFieldProcess.convert2CustomZone(testObject, null)

        then: "应该抛出NullPointerException"
        thrown(NullPointerException)
    }

    def "test convert2CustomZone with valid object"() {
        given: "准备测试对象"
        def testObject = new TestObject()

        when: "转换对象到自定义时区"
        ObjectDataFieldProcess.convert2CustomZone(testObject, mockFunction)

        then: "应该正常执行"
        noExceptionThrown()
        // 验证函数被调用
        mockFunction.apply(_) >> mockDescribe
    }

    def "test convert with nested objects"() {
        given: "准备嵌套测试对象"
        def nestedObject = new NestedTestObject()

        when: "转换嵌套对象"
        ObjectDataFieldProcess.convert2SystemZone(nestedObject, mockFunction)

        then: "应该正常执行"
        noExceptionThrown()
        mockFunction.apply(_) >> mockDescribe
    }

    def "test convert with iterable objects"() {
        given: "准备包含可迭代对象的测试对象"
        def iterableObject = new IterableTestObject()

        when: "转换包含可迭代对象的对象"
        ObjectDataFieldProcess.convert2SystemZone(iterableObject, mockFunction)

        then: "应该正常执行"
        noExceptionThrown()
        mockFunction.apply(_) >> mockDescribe
    }

    def "test convert with maximum recursion depth"() {
        given: "准备深度嵌套的对象"
        def level1 = new NestedTestObject()
        def level2 = new NestedTestObject()
        def level3 = new NestedTestObject()
        def level4 = new NestedTestObject()
        
        level1.nestedObject = level2
        level2.nestedObject = level3
        level3.nestedObject = level4

        when: "转换深度嵌套对象"
        ObjectDataFieldProcess.convert2SystemZone(level1, mockFunction)

        then: "应该正常执行（受递归深度限制）"
        noExceptionThrown()
        mockFunction.apply(_) >> mockDescribe
    }

    def "test convert processes all annotation types"() {
        given: "准备包含所有注解类型的对象"
        def testObject = new TestObject()

        when: "转换对象"
        ObjectDataFieldProcess.convert2SystemZone(testObject, mockFunction)

        then: "应该处理所有注解字段"
        noExceptionThrown()
        // 验证所有转换器都被使用
        mockFunction.apply(_) >> mockDescribe
    }

    def "test convert ignores non-annotated fields"() {
        given: "准备测试对象"
        def testObject = new TestObject()
        def originalNoAnnotationField = testObject.noAnnotationField

        when: "转换对象"
        ObjectDataFieldProcess.convert2SystemZone(testObject, mockFunction)

        then: "非注解字段应该保持不变"
        testObject.noAnnotationField == originalNoAnnotationField
        mockFunction.apply(_) >> mockDescribe
    }

    def "test convert handles field access exceptions gracefully"() {
        given: "准备会导致字段访问异常的对象"
        def testObject = new Object() {
            @ObjectDataField(ObjectDataField.Type.SINGLE)
            private final Map<String, Object> readOnlyField = ["id": "1", "describeApiName": "TestApi"]
        }

        when: "转换对象"
        ObjectDataFieldProcess.convert2SystemZone(testObject, mockFunction)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test static converter map initialization"() {
        expect: "静态转换器映射应该包含所有类型"
        // 通过反射验证静态字段
        def field = ObjectDataFieldProcess.getDeclaredField("objectDataFieldConvert")
        field.setAccessible(true)
        def converterMap = field.get(null)
        
        converterMap != null
        converterMap.size() == 4
        converterMap.containsKey(ObjectDataField.Type.SINGLE)
        converterMap.containsKey(ObjectDataField.Type.LIST)
        converterMap.containsKey(ObjectDataField.Type.LIST_MAP)
        converterMap.containsKey(ObjectDataField.Type.MAP_MAP)
    }

    def "test convert with inheritance"() {
        given: "准备继承类"




        when: "转换继承对象"
        ObjectDataFieldProcess.convert2SystemZone(null, mockFunction)

        then: "应该处理父类和子类的字段"
        noExceptionThrown()
        mockFunction.apply(_) >> mockDescribe
    }

    def "test convert performance with large object"() {
        given: "准备大对象"
        def largeObject = new TestObject()
        // 设置大数据
        largeObject.listField = (1..1000).collect { ["id": it.toString()] }
        largeObject.listMapField = ["api1": (1..500).collect { ["id": it.toString()] }]

        when: "转换大对象"
        def startTime = System.currentTimeMillis()
        ObjectDataFieldProcess.convert2SystemZone(largeObject, mockFunction)
        def endTime = System.currentTimeMillis()

        then: "性能应该可接受"
        (endTime - startTime) < 5000 // 5秒内完成
        mockFunction.apply(_) >> mockDescribe
    }

    def "test convert thread safety"() {
        given: "准备测试对象"
        def testObjects = (1..10).collect { new TestObject() }
        def results = []

        when: "并发转换对象"
        def threads = testObjects.collect { obj ->
            Thread.start {
                try {
                    ObjectDataFieldProcess.convert2SystemZone(obj, mockFunction)
                    synchronized (results) {
                        results << "success"
                    }
                } catch (Exception e) {
                    synchronized (results) {
                        results << "error"
                    }
                }
            }
        }
        threads.each { it.join() }

        then: "所有转换都应该成功"
        results.size() == 10
        results.every { it == "success" }
        mockFunction.apply(_) >> mockDescribe
    }
}
