package com.facishare.paas.appframework.metadata;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.ObjectData;
import lombok.Getter;
import lombok.experimental.Delegate;

import java.util.Arrays;

public class AggregateValue extends DocumentBasedBean {

    public static final String AGGREGATE_RULE_ID = "aggregate_rule_id";
    public static final String AGGREGATE_DATE = "aggregate_date";
    public static final String AGGREGATE_DIMENSION = "aggregate_dimension";
    public static final String AGGREGATE_VALUE = "aggregate_value";
    public static final String AGGREGATE_COUNT_VALUE = "aggregate_count_value";

    @Getter
    @Delegate
    private IObjectData data;

    private AggregateValue(IObjectData data) {
        this.data = data;
    }

    public static AggregateValue of(IObjectData data) {
        return new AggregateValue(data);
    }

    public static AggregateValue from(long aggregateDate, AggregateRule aggregateRule, IObjectData aggregateData) {
        AggregateValue aggregateValue = of(new ObjectData());
        aggregateValue.setDeleted(false);
        aggregateValue.setDescribeApiName(Utils.AGGREGATE_VALUE_OBJ_API_NAME);
        aggregateValue.setTenantId(aggregateRule.getTenantId());
        aggregateValue.setAggregateRuleId(aggregateRule.getId());
        aggregateValue.setAggregateDate(aggregateDate);
        aggregateValue.setAggregateDimension((String) aggregateData.get(aggregateRule.getDimension()));
        Object value = aggregateData.get(aggregateRule.buildAggFieldName());
        if (!aggregateRule.isMaxOrMinWay()) {
            value = ObjectDataExt.isValueEmpty(value) ? 0 : value;
        }
        aggregateValue.setAggregateValue(value);
        Object countValue = aggregateData.get(AggregateRule.COUNT_VALUE_KEY);
        countValue = ObjectDataExt.isValueEmpty(countValue) ? 0 : countValue;
        aggregateValue.setAggregateCountValue(countValue);
        return aggregateValue;
    }

    public String getAggregateRuleId() {
        return (String) get(AGGREGATE_RULE_ID);
    }

    public void setAggregateRuleId(String aggregateRuleId) {
        set(AGGREGATE_RULE_ID, aggregateRuleId);
    }

    public Long getAggregateDate() {
        return (Long) get(AGGREGATE_DATE);
    }

    public void setAggregateDate(long aggregateDate) {
        set(AGGREGATE_DATE, aggregateDate);
    }

    public String getAggregateDimension() {
        return (String) get(AGGREGATE_DIMENSION);
    }

    public void setAggregateDimension(String aggregateDimension) {
        set(AGGREGATE_DIMENSION, aggregateDimension);
    }

    public void setAggregateValue(Object aggregateValue) {
        set(AGGREGATE_VALUE, aggregateValue);
    }

    public void setAggregateCountValue(Object aggregateCountValue) {
        set(AGGREGATE_COUNT_VALUE, aggregateCountValue);
    }

    public boolean keyEqual(AggregateValue value) {
        if (!getTenantId().equals(value.getTenantId())) {
            return false;
        }
        if (!getAggregateRuleId().equals(value.getAggregateRuleId())) {
            return false;
        }
        if (!getAggregateDimension().equals(value.getAggregateDimension())) {
            return false;
        }
        return getAggregateDate().equals(value.getAggregateDate());
    }

}
