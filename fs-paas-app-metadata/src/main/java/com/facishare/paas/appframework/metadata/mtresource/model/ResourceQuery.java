package com.facishare.paas.appframework.metadata.mtresource.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.config.ControlLevelResourceType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;

import java.util.*;
import java.util.function.BiFunction;

/**
 * Created by zhaooju on 2023/8/4
 */
@Data
public
class ResourceQuery {
    private Set<String> keys;
    private List<Map<String, String>> searchData;

    public static <T> ResourceQuery build(Collection<String> uniqueKeys, List<T> dataList, BiFunction<String, T, Object> function) {
        if (CollectionUtils.empty(uniqueKeys) || CollectionUtils.empty(dataList)) {
            return null;
        }
        Set<String> keys = Sets.newHashSet(uniqueKeys);
        List<Map<String, String>> searchData = Lists.newArrayList();
        for (T data : dataList) {
            Map<String, String> map = Maps.newHashMap();
            for (String key : keys) {
                Object value = function.apply(key, data);
                if (Objects.isNull(value)) {
                    map.put(key, null);
                } else {
                    map.put(key, value.toString());
                }
            }
            searchData.add(map);
        }
        ResourceQuery resourceQuery = new ResourceQuery();
        resourceQuery.setKeys(keys);
        resourceQuery.setSearchData(searchData);
        return resourceQuery;
    }

    public static Set<String> getSearchKeyByResourceType(String resourceType) {
        return ControlLevelResourceType.getByResourceType(resourceType)
                .map(ControlLevelResourceType::getUniqueFieldNames)
                .orElse(Collections.emptySet());
    }

    public Map<String, List<ConfigurationPackageResource.ResourceValue>> mackResourceValues() {
        if (CollectionUtils.empty(keys) || CollectionUtils.empty(searchData)) {
            return Maps.newHashMap();
        }
        Map<String, List<ConfigurationPackageResource.ResourceValue>> result = Maps.newHashMap();
        for (Map<String, String> searchDatum : searchData) {
            List<ConfigurationPackageResource.ResourceValue> resourceValue = Lists.newArrayList();
            for (String key : keys) {
                String value = searchDatum.get(key);
                resourceValue.add(ConfigurationPackageResource.ResourceValue.of(key, value));
            }
            String join = ConfigurationPackageResource.join(resourceValue);
            result.put(join, resourceValue);
        }
        return result;
    }
}
