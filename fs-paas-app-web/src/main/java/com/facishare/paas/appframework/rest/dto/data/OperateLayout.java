package com.facishare.paas.appframework.rest.dto.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by Yuanxl on 2018/6/11
 */
public interface OperateLayout {

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class FindDefaultArg {
        String descApiName;
        String layoutType;
    }

    @Data
    class Result {

        @JSONField(name = "ui_layout")
        @JsonProperty(value = "ui_layout")
        @SerializedName("ui_layout")
        LayoutDocument layoutDocument;
    }

    @Data
    class ResultList {
        @JSONField(name = "queryResult")
        @JsonProperty(value = "queryResult")
        @SerializedName("queryResult")
        List<LayoutDocument> layoutDocuments;
    }

}
