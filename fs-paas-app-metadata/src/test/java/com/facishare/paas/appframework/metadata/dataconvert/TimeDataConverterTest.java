package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试TimeDataConverter类的时间字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class TimeDataConverterTest {

  @Mock
  private IObjectDataProxyService dataService;

  @Mock
  private SessionContext sessionContext;

  private TimeDataConverter converter;
  private IFieldDescribe fieldDescribe;
  private IObjectData objectData;

  @BeforeEach
  void setUp() {
    converter = new TimeDataConverter();
    fieldDescribe = createTimeFieldDescribe("time_field");
    // 使用一个表示时间的时间戳（只关心时分秒部分）
    objectData = createObjectData("time_field", new Date(0, 0, 0, 14, 30, 0)); // 14:30:00
    
    // 设置转换器的属性
    converter.setFieldDescribe(fieldDescribe);
    converter.setObjectData(objectData);
    converter.setDataService(dataService);
    
    // 设置SessionContext的默认行为
    lenient().when(sessionContext.getRegion()).thenReturn("");
    lenient().when(sessionContext.getEId()).thenReturn(123456L);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法正常转换Date时间对象
   */
  @Test
  @DisplayName("测试convertFieldData - Date时间对象转换")
  void testConvertFieldData_DateTimeConversion() throws Exception {
    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 应该返回格式化的时间字符串
    assertNotNull(result);
    assertFalse(result.isEmpty());
    // 结果应该是HH:mm或HH:mm:ss格式
    assertTrue(result.matches("\\d{2}:\\d{2}(:\\d{2})?"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理null值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null值")
  void testConvertFieldData_HandlesNullValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("time_field", null);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理空字符串
   */
  @Test
  @DisplayName("测试convertFieldData - 处理空字符串")
  void testConvertFieldData_HandlesEmptyString() throws Exception {
    // 准备测试数据
    objectData = createObjectData("time_field", "");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理"null"字符串
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null字符串")
  void testConvertFieldData_HandlesNullString() throws Exception {
    // 准备测试数据
    objectData = createObjectData("time_field", "null");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理已经是字符串格式的时间
   */
  @Test
  @DisplayName("测试convertFieldData - 处理字符串格式时间")
  void testConvertFieldData_HandlesStringTime() throws Exception {
    // 准备测试数据
    String timeString = "14:30:00";
    objectData = createObjectData("time_field", timeString);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 如果输入已经是字符串，应该直接返回
    assertEquals(timeString, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理Long时间戳
   */
  @Test
  @DisplayName("测试convertFieldData - 处理Long时间戳")
  void testConvertFieldData_HandlesLongTimestamp() throws Exception {
    // 准备测试数据 - 使用时间戳
    long timestamp = System.currentTimeMillis();
    objectData = createObjectData("time_field", timestamp);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertNotNull(result);
    assertFalse(result.isEmpty());
    // 结果应该是HH:mm或HH:mm:ss格式
    assertTrue(result.matches("\\d{2}:\\d{2}(:\\d{2})?"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理多区域格式化
   */
  @Test
  @DisplayName("测试convertFieldData - 多区域格式化")
  void testConvertFieldData_MultiRegionFormatting() throws Exception {
    // 准备测试数据
    when(sessionContext.getRegion()).thenReturn("US");
    objectData = createObjectData("time_field", new Date());
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 多区域格式化可能返回null或格式化后的字符串
    assertTrue(result == null || result.length() >= 0);
  }

  /**
   * 提供不同类型的时间值测试数据
   */
  private static Stream<Arguments> provideTimeValues() {
    return Stream.of(
        Arguments.of(new Date(), true), // Date对象
        Arguments.of(System.currentTimeMillis(), true), // Long时间戳
        Arguments.of("14:30:00", true), // 字符串格式
        Arguments.of("invalid-time", true), // 无效字符串（会被直接返回）
        Arguments.of(123456, true) // 整数时间戳
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理不同类型的时间值
   */
  @ParameterizedTest
  @MethodSource("provideTimeValues")
  @DisplayName("测试convertFieldData - 不同类型的时间值")
  void testConvertFieldData_DifferentTimeValues(Object inputValue, boolean shouldSucceed) throws Exception {
    // 准备测试数据
    objectData = createObjectData("time_field", inputValue);
    converter.setObjectData(objectData);

    // 执行被测试方法
    if (shouldSucceed) {
      String result = converter.convertFieldData(sessionContext);
      assertNotNull(result);
    } else {
      assertDoesNotThrow(() -> {
        converter.convertFieldData(sessionContext);
      });
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理字段不存在的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 字段不存在")
  void testConvertFieldData_FieldNotExists() throws Exception {
    // 准备测试数据 - 创建不包含目标字段的数据
    objectData = createObjectData("other_field", new Date());
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 字段不存在时应该返回空字符串
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理异常情况
   */
  @Test
  @DisplayName("测试convertFieldData - 异常处理")
  void testConvertFieldData_ExceptionHandling() throws Exception {
    // 准备测试数据 - 使用可能导致转换异常的值
    objectData = createObjectData("time_field", Double.NaN);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - Double.NaN会被转换为Date对象，然后格式化为时间字符串
    assertNotNull(result);
    // NaN转换为Date后会是一个有效的时间，所以不会是空字符串
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试转换器的基本属性设置
   */
  @Test
  @DisplayName("测试转换器属性设置")
  void testConverterProperties() {
    // 验证属性设置
    assertEquals(fieldDescribe, converter.getFieldDescribe());
    assertEquals(objectData, converter.getObjectData());
    assertEquals(dataService, converter.getDataService());
  }

  /**
   * 创建测试用的时间字段描述
   */
  private IFieldDescribe createTimeFieldDescribe(String apiName) {
    Map<String, Object> fieldMap = new HashMap<>();
    fieldMap.put("api_name", apiName);
    fieldMap.put("label", "测试时间字段");
    fieldMap.put("type", IFieldType.TIME);
    return FieldDescribeFactory.newInstance(fieldMap);
  }

  /**
   * 创建测试用的对象数据
   */
  private IObjectData createObjectData(String fieldName, Object value) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldName, value);
    return new ObjectData(dataMap);
  }
}
