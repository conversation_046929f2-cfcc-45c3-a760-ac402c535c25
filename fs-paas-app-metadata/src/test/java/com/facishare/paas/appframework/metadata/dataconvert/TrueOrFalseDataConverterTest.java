package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.TrueOrFalse;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * GenerateByAI
 * 测试TrueOrFalseDataConverter类的布尔字段转换功能
 */
@ExtendWith(MockitoExtension.class)
class TrueOrFalseDataConverterTest {

  @Mock
  private IObjectDataProxyService dataService;

  @Mock
  private SessionContext sessionContext;

  @Mock
  private TrueOrFalse trueOrFalseFieldDescribe;

  @Mock
  private ISelectOption selectOption;

  private TrueOrFalseDataConverter converter;
  private IObjectData objectData;

  @BeforeEach
  void setUp() {
    converter = new TrueOrFalseDataConverter();
    objectData = createObjectData("boolean_field", true);

    // 设置转换器的属性
    converter.setFieldDescribe(trueOrFalseFieldDescribe);
    converter.setObjectData(objectData);
    converter.setDataService(dataService);

    // 设置字段描述的基本属性
    lenient().when(trueOrFalseFieldDescribe.getApiName()).thenReturn("boolean_field");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法正常转换true值
   */
  @Test
  @DisplayName("测试convertFieldData - true值转换")
  void testConvertFieldData_TrueValueConversion() throws Exception {
    // 准备测试数据
    when(trueOrFalseFieldDescribe.getOption(true)).thenReturn(Optional.empty());
    
    try (MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {
      i18nMock.when(() -> I18N.text(I18NKey.YES)).thenReturn("是");
      
      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);
      
      // 验证结果
      assertEquals("是", result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法正常转换false值
   */
  @Test
  @DisplayName("测试convertFieldData - false值转换")
  void testConvertFieldData_FalseValueConversion() throws Exception {
    // 准备测试数据
    objectData = createObjectData("boolean_field", false);
    converter.setObjectData(objectData);
    when(trueOrFalseFieldDescribe.getOption(false)).thenReturn(Optional.empty());
    
    try (MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {
      i18nMock.when(() -> I18N.text(I18NKey.NO)).thenReturn("否");
      
      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);
      
      // 验证结果
      assertEquals("否", result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理null值
   */
  @Test
  @DisplayName("测试convertFieldData - 处理null值")
  void testConvertFieldData_HandlesNullValue() throws Exception {
    // 准备测试数据
    objectData = createObjectData("boolean_field", null);
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法使用自定义选项标签
   */
  @Test
  @DisplayName("测试convertFieldData - 自定义选项标签")
  void testConvertFieldData_CustomOptionLabels() throws Exception {
    // 准备测试数据
    when(selectOption.getLabel()).thenReturn("是");
    when(trueOrFalseFieldDescribe.getOption(true)).thenReturn(Optional.of(selectOption));
    
    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);
    
    // 验证结果
    assertEquals("是", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法在没有自定义选项时使用默认标签
   */
  @Test
  @DisplayName("测试convertFieldData - 无自定义选项")
  void testConvertFieldData_NoCustomOptions() throws Exception {
    // 准备测试数据
    when(trueOrFalseFieldDescribe.getOption(true)).thenReturn(Optional.empty());
    
    try (MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {
      i18nMock.when(() -> I18N.text(I18NKey.YES)).thenReturn("是");
      
      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);
      
      // 验证结果
      assertEquals("是", result);
      
      // 验证I18N调用
      i18nMock.verify(() -> I18N.text(I18NKey.YES));
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理字段不存在的情况
   */
  @Test
  @DisplayName("测试convertFieldData - 字段不存在")
  void testConvertFieldData_FieldNotExists() throws Exception {
    // 准备测试数据 - 创建不包含目标字段的数据
    objectData = createObjectData("other_field", "other_value");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 字段不存在时应该返回空字符串
    assertEquals("", result);
  }

  /**
   * 提供不同类型的布尔值测试数据
   */
  private static Stream<Arguments> provideBooleanValues() {
    return Stream.of(
        Arguments.of(true, true),
        Arguments.of(false, false),
        Arguments.of("true", true),
        Arguments.of("false", false)
        // 注意：数字1和0的转换行为取决于ObjectData的具体实现
        // 在实际使用中，布尔字段通常存储为Boolean类型而不是数字
    );
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理不同类型的布尔值
   */
  @ParameterizedTest
  @MethodSource("provideBooleanValues")
  @DisplayName("测试convertFieldData - 不同类型的布尔值")
  void testConvertFieldData_DifferentBooleanValues(Object inputValue, boolean expectedBoolean) throws Exception {
    // 准备测试数据
    objectData = createObjectData("boolean_field", inputValue);
    converter.setObjectData(objectData);

    // 为 true 和 false 都设置 stubbing
    lenient().when(trueOrFalseFieldDescribe.getOption(true)).thenReturn(Optional.empty());
    lenient().when(trueOrFalseFieldDescribe.getOption(false)).thenReturn(Optional.empty());

    try (MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {
      i18nMock.when(() -> I18N.text(I18NKey.YES)).thenReturn("是");
      i18nMock.when(() -> I18N.text(I18NKey.NO)).thenReturn("否");

      // 执行被测试方法
      String result = converter.convertFieldData(sessionContext);

      // 验证结果
      String expectedResult = expectedBoolean ? "是" : "否";
      assertEquals(expectedResult, result);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertFieldData方法处理类型转换异常
   */
  @Test
  @DisplayName("测试convertFieldData - 类型转换异常")
  void testConvertFieldData_TypeConversionException() throws Exception {
    // 准备测试数据 - 使用无法转换为Boolean的值
    objectData = createObjectData("boolean_field", "invalid_boolean");
    converter.setObjectData(objectData);

    // 执行被测试方法
    String result = converter.convertFieldData(sessionContext);

    // 验证结果 - 转换异常时应该返回空字符串
    assertEquals("", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试转换器的基本属性设置
   */
  @Test
  @DisplayName("测试转换器属性设置")
  void testConverterProperties() {
    // 验证属性设置
    assertEquals(trueOrFalseFieldDescribe, converter.getFieldDescribe());
    assertEquals(objectData, converter.getObjectData());
    assertEquals(dataService, converter.getDataService());
  }

  /**
   * 创建测试用的对象数据
   */
  private IObjectData createObjectData(String fieldName, Object value) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put(fieldName, value);
    return new ObjectData(dataMap);
  }
}
