package com.facishare.paas.appframework.core.predef.action.uievent;

import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.action.StandardTriggerEventAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-08-12 14:19
 */
public interface ActionContainer {
    ServiceFacade getServiceFacade();
    InfraServiceFacade getInfraServiceFacade();
    RequestContext getContext();
    IObjectData getMasterData();
    Map<String, List<IObjectData>> getDetailData();
    IObjectDescribe getObjectDescribe();
    Map<String, IObjectDescribe> getDetailDescribe();


    void customHandleDiffedMaster(IObjectData masterWithOnlyChangedFields);
    void customHandleDiffedDetail(Map<String, List<IObjectData>> detailWithOnlyChangedFields);
}
