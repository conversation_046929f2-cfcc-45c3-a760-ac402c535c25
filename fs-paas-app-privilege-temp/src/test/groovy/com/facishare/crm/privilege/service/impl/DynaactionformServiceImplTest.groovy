package com.facishare.crm.privilege.service.impl

import com.facishare.crm.privilege.service.FunctionPrivilegeOperateService
import com.google.common.collect.Lists
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * Created by luxin on 2017/5/26.
 */
//@ContextConfiguration(locations = "classpath:dynaactionform-application.xml")
class DynaactionformServiceImplTest extends Specification {

  @Autowired
  FunctionPrivilegeOperateService dynaactionformService


  def "addDefaultRole"() {

    when:
    dynaactionformService.addRole("56609", "00000000000000000000000000000022", "智能表单角色", "智能表单角色对智能表单进行管理")
    then:
    1 == 1

  }


  def "addFunc2DynaactionformRole"() {

    when:
    dynaactionformService.addFunc2IntelliFormRole("2", Lists.newArrayList("object_2b9__c"))
    then:
    1 == 1


  }


  def "addFunc2UserDefinedRole"() {

    when:
    dynaactionformService.addUserDefinedFunc2UserDefinedRole("2", "591928e23db71d5c1b9f8019", Lists.newArrayList("object_2b9__c"))

    then:
    1 == 1

  }


  def "addUser2DynaactionformRole"() {
    when:
    dynaactionformService.addUser2IntelliFormRole("2", Lists.newArrayList("1000"))

    then:
    1 == 1


  }


  def "deleteUserFromDynaactionformRole"() {
    when:
    dynaactionformService.deleteUserFromIntelliFormRole("2", Lists.newArrayList("1000"))

    then:
    1 == 1


  }


  def "addUser2UserDefinedRole"() {


    when:
    dynaactionformService.addUser2Role("2", "2", "591928e23db71d5c1b9f8019", Lists.newArrayList("5163"))

    then:
    1 == 1


  }


  def "createUserDefinedRole"() {

    when:
    def result = dynaactionformService.createUserDefinedRole("56609", "智能表单角色", "智能表单角色负责对表单的管理")
    println result

    then:
    1 == 1
  }


}
