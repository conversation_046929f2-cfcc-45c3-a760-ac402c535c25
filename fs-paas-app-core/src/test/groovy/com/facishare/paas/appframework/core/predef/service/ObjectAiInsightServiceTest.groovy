package com.facishare.paas.appframework.core.predef.service


import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.i18n.I18NExt
import com.facishare.paas.appframework.core.i18n.I18NKey
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.action.StandardAddAction
import com.facishare.paas.appframework.core.predef.action.StandardEditAction
import com.facishare.paas.appframework.core.predef.service.dto.ai.FindInsightResult
import com.facishare.paas.appframework.core.predef.service.dto.ai.SaveInsightResult
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.appframework.metadata.ObjectDescribeExt
import com.facishare.paas.appframework.metadata.ai.AiInsightLogicService
import com.facishare.paas.appframework.metadata.repository.model.AiInsightEntity
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification

import java.lang.reflect.Field

/**
 * Unit tests for ObjectAiInsightService
 */
class ObjectAiInsightServiceTest extends Specification {

    ObjectAiInsightService objectAiInsightService
    AiInsightLogicService aiInsightLogicService
    ServiceFacade serviceFacade
    FunctionPrivilegeService functionPrivilegeService
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }


    def setup() {
        // Create mock services
        aiInsightLogicService = Mock(AiInsightLogicService)
        serviceFacade = Mock(ServiceFacade)
        functionPrivilegeService = Mock(FunctionPrivilegeService)
        
        // Create test instance
        objectAiInsightService = new ObjectAiInsightService()
        objectAiInsightService.aiInsightLogicService = aiInsightLogicService
        objectAiInsightService.serviceFacade = serviceFacade
        objectAiInsightService.functionPrivilegeService = functionPrivilegeService

        // Mock I18NExt for error messages
        GroovyMock(I18NExt, global: true)
        I18NExt.text(I18NKey.OBJECT_INVALID, _) >> "对象无效"
        I18NExt.text(I18NKey.HAS_NO_FIELD_WRITE_PERMISSION_WITH_FIELD_INFO, _, _) >> "没有字段写权限"
        
        // Mock ObjectDescribeExt
        GroovyMock(ObjectDescribeExt, global: true)
    }

    /**
     * Test finding AI insight result when result is empty
     */
    def "findAiInsightResult should return empty result when no entity found"() {
        given:
        def arg = new FindInsightResult.Arg(
                objectApiName: "TestObj",
                dataId: "data123",
                componentApiName: "TestComponent"
        )
        def context = createMockServiceContext()

        when:
        def result = objectAiInsightService.findAiInsightResult(arg, context)

        then:
        1 * aiInsightLogicService.findByUniqKey(context.getUser(), "TestObj", "data123", "TestComponent") >> null
        result.insightResult == null
        result.generateTime == null
    }

    /**
     * Test finding AI insight result with success response
     */
    def "findAiInsightResult should return populated result when entity exists"() {
        given:
        def arg = new FindInsightResult.Arg(
                objectApiName: "TestObj",
                dataId: "data123",
                componentApiName: "TestComponent"
        )
        def context = createMockServiceContext()
        def insightResult = '{"key1":"value1","key2":"value2"}'
        def generateTime = 1624512345678L
        def entity = AiInsightEntity.builder()
                .relateObjectApiName("TestObj")
                .relateDataId("data123")
                .componentApiName("TestComponent")
                .insightResult(insightResult)
                .generateTime(generateTime)
                .build()

        when:
        def result = objectAiInsightService.findAiInsightResult(arg, context)

        then:
        1 * aiInsightLogicService.findByUniqKey(context.getUser(), "TestObj", "data123", "TestComponent") >> entity
        result != null
        result.generateTime == generateTime
        result.insightResult instanceof Map
    }

    /**
     * Test saving AI insight result when result is empty
     */
    def "saveInsightResult should return empty result when insight result is null"() {
        given:
        def arg = new SaveInsightResult.Arg(
                objectApiName: "TestObj",
                dataId: "data123",
                componentApiName: "TestComponent",
                insightResult: null,
                generateTime: 1624512345678L
        )
        def context = createMockServiceContext()

        when:
        def result = objectAiInsightService.saveInsightResult(arg, context)

        then:
        0 * aiInsightLogicService.save(_, _)
        result != null
    }

    /**
     * Test saving AI insight result when result is empty map
     */
    def "saveInsightResult should return empty result when insight result is empty map"() {
        given:
        def arg = new SaveInsightResult.Arg(
                objectApiName: "TestObj",
                dataId: "data123",
                componentApiName: "TestComponent",
                insightResult: [:],
                generateTime: 1624512345678L
        )
        def context = createMockServiceContext()

        when:
        def result = objectAiInsightService.saveInsightResult(arg, context)

        then:
        0 * aiInsightLogicService.save(_, _)
        result != null
    }

    /**
     * Test saving AI insight result without field mapping
     */
    def "saveInsightResult should only save AI entity when field mapping is null"() {
        given:
        def insightResult = ["key1": "value1", "key2": "value2"]
        def arg = new SaveInsightResult.Arg(
                objectApiName: "TestObj",
                dataId: "data123",
                componentApiName: "TestComponent",
                insightResult: insightResult,
                generateTime: 1624512345678L,
                fieldMapping: null
        )
        def context = createMockServiceContext()

        when:
        def result = objectAiInsightService.saveInsightResult(arg, context)

        then:
        1 * aiInsightLogicService.save(context.getUser(), _) >> { User user, AiInsightEntity entity ->
            assert entity.relateObjectApiName == "TestObj"
            assert entity.relateDataId == "data123"
            assert entity.componentApiName == "TestComponent"
            assert entity.generateTime == 1624512345678L
        }
        0 * serviceFacade.findObject(_, _)
        result != null
    }

    /**
     * Test saving AI insight result without jsonResult field
     */
    def "saveInsightResult should only save AI entity when jsonResult field is missing"() {
        given:
        def insightResult = ["someOtherKey": "value"]
        def fieldMapping = ["field1": "name", "field2": "description"]
        def arg = new SaveInsightResult.Arg(
                objectApiName: "TestObj",
                dataId: "data123",
                componentApiName: "TestComponent",
                insightResult: insightResult,
                generateTime: 1624512345678L,
                fieldMapping: fieldMapping,
                mappingObjectApiName: "MappingObj"
        )
        def context = createMockServiceContext()

        when:
        def result = objectAiInsightService.saveInsightResult(arg, context)

        then:
        1 * aiInsightLogicService.save(context.getUser(), _)
        0 * serviceFacade.findObject(_, _)
        result != null
    }
    
    /**
     * Test saving AI insight result with inactive object
     */
    def "saveInsightResult should throw ValidateException when object is inactive"() {
        given:
        def jsonResult = ["jsonResult": ["field1": "value1", "field2": "value2"]]
        def fieldMapping = ["field1": "name", "field2": "description"]
        def arg = new SaveInsightResult.Arg(
                objectApiName: "TestObj",
                dataId: "data123",
                componentApiName: "TestComponent",
                insightResult: jsonResult,
                generateTime: 1624512345678L,
                fieldMapping: fieldMapping,
                mappingObjectApiName: "MappingObj"
        )
        def context = createMockServiceContext()
        def objectDescribe = Mock(IObjectDescribe)
        objectDescribe.isActive() >> false
        objectDescribe.getApiName() >> "MappingObj"
        objectDescribe.getDisplayName() >> "映射对象"

        when:
        def result = objectAiInsightService.saveInsightResult(arg, context)

        then:
        1 * aiInsightLogicService.save(context.getUser(), _)
        1 * serviceFacade.findObject(context.getTenantId(), "MappingObj") >> objectDescribe
        result.success == false
        result.message != null
    }

    /**
     * Create a mock ServiceContext
     */
    private ServiceContext createMockServiceContext() {
        def user = Mock(User)
        user.getUserId() >> "userId123"
        user.getTenantId() >> "tenant123"

        def context = Mock(ServiceContext)
        context.getUser() >> user
        context.getTenantId() >> "tenant123"
        
        return context
    }

    /**
     * GenerateByAI
     * 测试内容描述：当字段映射为空时，saveObjectData方法应该直接返回
     */
    def "saveObjectDataTest_WithEmptyFieldMapping"() {
        given:
        def arg = new SaveInsightResult.Arg(
                objectApiName: "TestObj",
                dataId: "data123",
                componentApiName: "TestComponent",
                insightResult: ["jsonResult": ["field1": "value1"]],
                generateTime: 1624512345678L,
                mappingObjectApiName: "MappingObj",
                fieldMapping: null
        )
        def context = createMockServiceContext()

        when:
        objectAiInsightService.saveObjectData(arg, context)

        then:
        0 * serviceFacade.findObject(_, _)
        0 * serviceFacade.triggerRemoteAction(_, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：当jsonResult为空时，saveObjectData方法应该直接返回
     */
    def "saveObjectDataTest_WithEmptyJsonResult"() {
        given:
        def arg = new SaveInsightResult.Arg(
                objectApiName: "TestObj",
                dataId: "data123",
                componentApiName: "TestComponent",
                insightResult: [:],
                generateTime: 1624512345678L,
                mappingObjectApiName: "MappingObj",
                fieldMapping: ["field1": "name", "field2": "description"]
        )
        def context = createMockServiceContext()

        when:
        objectAiInsightService.saveObjectData(arg, context)

        then:
        0 * serviceFacade.findObject(_, _)
        0 * serviceFacade.triggerRemoteAction(_, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：当关联对象与洞察对象相同时，应该触发更新操作
     * 
     * 注意：使用直接构造实体的方式替代Mock，这样更接近实际场景
     */
    def "saveObjectDataTest_SameObjectUpdate"() {
        given:
        def jsonResult = ["variable1": "value1", "variable2": "value2"]
        def fieldMapping = ["variable1": "field1", "variable2": "field2", "_data_id": "recordId", "_component_id": "componentId"]
        def arg = new SaveInsightResult.Arg(
                objectApiName: "TestObj",
                dataId: "data123",
                componentApiName: "TestComponent",
                insightResult: ["jsonResult": jsonResult],
                generateTime: 1624512345678L,
                mappingObjectApiName: "TestObj",
                fieldMapping: fieldMapping
        )
        def context = createMockServiceContext()
        
        // 创建实际的对象描述
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("TestObj")
        objectDescribe.setDisplayName("测试对象")
        objectDescribe.setIsActive(true)
        
        // 创建字段描述
        def field1 = new TextFieldDescribe()
        field1.setApiName("field1")
        field1.setLabel("Field1")
        
        def field2 = new TextFieldDescribe()
        field2.setApiName("field2")
        field2.setLabel("Field2")
        
        def recordIdField = new TextFieldDescribe()
        recordIdField.setApiName("recordId")
        recordIdField.setLabel("记录ID")
        
        def componentIdField = new TextFieldDescribe()
        componentIdField.setApiName("componentId")
        componentIdField.setLabel("组件ID")
        
        // 设置字段到对象描述
        objectDescribe.setFieldDescribes([field1, field2, recordIdField, componentIdField])
        
        // Mock ContextManager.buildActionContext
        GroovyMock(ContextManager, global: true)
        def actionContext = Mock(ActionContext)
        ContextManager.buildActionContext("TestObj", "Edit") >> actionContext
        
        // 模拟只读字段检查
        functionPrivilegeService.getReadonlyFields(context.getUser(), "TestObj") >> []

        when:
        objectAiInsightService.saveObjectData(arg, context)

        then:
        1 * serviceFacade.findObject(context.getTenantId(), "TestObj") >> objectDescribe
        1 * serviceFacade.triggerRemoteAction(_, _, StandardEditAction.Result.class) >> { act, editArg, resultClass ->
            assert editArg.objectData.get("field1") == "value1"
            assert editArg.objectData.get("field2") == "value2"
            assert editArg.objectData.get("recordId") == "data123"
            assert editArg.objectData.get("componentId") == "TestComponent"
            
            // 验证ObjectDataExt.setDescribeApiName是否被正确调用
            def data = editArg.objectData
            assert ObjectDataExt.of(data).getDescribeApiName() == "TestObj"
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：当关联对象与洞察对象不同时，应该触发创建操作
     * 
     * 注意：使用直接构造实体的方式替代Mock，这样更接近实际场景
     */
    def "saveObjectDataTest_DifferentObjectCreate"() {
        given:
        def jsonResult = ["variable1": "value1", "variable2": "value2"]
        def fieldMapping = ["variable1": "field1", "variable2": "field2", "_data_id": "recordId", "_component_id": "componentId"]
        def arg = new SaveInsightResult.Arg(
                objectApiName: "TestObj",
                dataId: "data123",
                componentApiName: "TestComponent",
                insightResult: ["jsonResult": jsonResult],
                generateTime: 1624512345678L,
                mappingObjectApiName: "MappingObj",
                fieldMapping: fieldMapping
        )
        def context = createMockServiceContext()
        
        // 创建实际的对象描述
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("MappingObj")
        objectDescribe.setDisplayName("映射对象")
        objectDescribe.setIsActive(true)
        
        // 创建字段描述
        def field1 = new TextFieldDescribe()
        field1.setApiName("field1")
        field1.setLabel("Field1")
        
        def field2 = new TextFieldDescribe()
        field2.setApiName("field2")
        field2.setLabel("Field2")
        
        def recordIdField = new TextFieldDescribe()
        recordIdField.setApiName("recordId")
        recordIdField.setLabel("记录ID")
        
        def componentIdField = new TextFieldDescribe()
        componentIdField.setApiName("componentId")
        componentIdField.setLabel("组件ID")
        
        // 设置字段到对象描述
        objectDescribe.setFieldDescribes([field1, field2, recordIdField, componentIdField])
        
        // Mock ContextManager.buildActionContext
        GroovyMock(ContextManager, global: true)
        def actionContext = Mock(ActionContext)
        ContextManager.buildActionContext("MappingObj", "Add") >> actionContext
        
        // 模拟只读字段检查
        functionPrivilegeService.getReadonlyFields(context.getUser(), "MappingObj") >> []

        when:
        objectAiInsightService.saveObjectData(arg, context)

        then:
        1 * serviceFacade.findObject(context.getTenantId(), "MappingObj") >> objectDescribe
        1 * serviceFacade.triggerRemoteAction(_, _, StandardAddAction.Result.class) >> { act, addArg, resultClass ->
            assert addArg.objectData.get("field1") == "value1"
            assert addArg.objectData.get("field2") == "value2"
            assert addArg.objectData.get("recordId") == "data123"
            assert addArg.objectData.get("componentId") == "TestComponent"
            
            // 验证ObjectDataExt.setDescribeApiName是否被正确调用
            def data = addArg.objectData
            assert ObjectDataExt.of(data).getDescribeApiName() == "MappingObj"
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：当对象无效时，应该抛出ValidateException异常
     * 
     * 注意：使用直接构造实体的方式替代Mock，这样更接近实际场景
     */
    def "saveObjectDataError_WithInvalidObject"() {
        given:
        def jsonResult = ["variable1": "value1", "variable2": "value2"]
        def fieldMapping = ["variable1": "field1", "variable2": "field2"]
        def arg = new SaveInsightResult.Arg(
                objectApiName: "TestObj",
                dataId: "data123",
                componentApiName: "TestComponent",
                insightResult: ["jsonResult": jsonResult],
                generateTime: 1624512345678L,
                mappingObjectApiName: "MappingObj",
                fieldMapping: fieldMapping
        )
        def context = createMockServiceContext()
        
        // 创建实际的对象描述，设置为非活动状态
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("MappingObj")
        objectDescribe.setDisplayName("映射对象")
        objectDescribe.setIsActive(false)
        
        // 注意：在测试环境中，I18NExt.text实际不会被调用，
        // 异常消息直接使用key值"paas.udobj.object_invalid"
        GroovyMock(I18NExt, global: true)
        I18NExt.text(I18NKey.OBJECT_INVALID, "MappingObj") >> "对象无效"

        when:
        objectAiInsightService.saveObjectData(arg, context)

        then:
        1 * serviceFacade.findObject(context.getTenantId(), "MappingObj") >> objectDescribe
        0 * serviceFacade.triggerRemoteAction(_, _, _)
        def exception = thrown(ValidateException)
        // 测试中使用key值而不是本地化文本
        exception.message == "paas.udobj.object_invalid"
    }

    /**
     * GenerateByAI
     * 测试内容描述：当没有字段写权限时，应该抛出ValidateException异常
     * 
     * 注意：使用直接构造实体的方式替代Mock，这样更接近实际场景
     */
    def "saveObjectDataError_WithNoFieldWritePermission"() {
        given:
        def jsonResult = ["variable1": "value1", "variable2": "value2"]
        def fieldMapping = ["variable1": "field1", "variable2": "field2"]
        def arg = new SaveInsightResult.Arg(
                objectApiName: "TestObj",
                dataId: "data123",
                componentApiName: "TestComponent",
                insightResult: ["jsonResult": jsonResult],
                generateTime: 1624512345678L,
                mappingObjectApiName: "MappingObj",
                fieldMapping: fieldMapping
        )
        def context = createMockServiceContext()
        
        // 创建实际的对象描述
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("MappingObj")
        objectDescribe.setDisplayName("映射对象")
        objectDescribe.setIsActive(true)
        
        // 创建字段描述
        def field1 = new TextFieldDescribe()
        field1.setApiName("field1")
        field1.setLabel("Field1")
        
        def field2 = new TextFieldDescribe()
        field2.setApiName("field2")
        field2.setLabel("Field2")
        
        // 设置字段到对象描述
        objectDescribe.setFieldDescribes([field1, field2])
        
        // 模拟I18NExt，注意在实际测试中国际化文本可能不会被调用
        GroovyMock(I18NExt, global: true)
        I18NExt.text(I18NKey.HAS_NO_FIELD_WRITE_PERMISSION_WITH_FIELD_INFO, "映射对象", "Field1|Field2") >> "没有字段写权限"

        when:
        objectAiInsightService.saveObjectData(arg, context)

        then:
        1 * serviceFacade.findObject(context.getTenantId(), "MappingObj") >> objectDescribe
        1 * functionPrivilegeService.getReadonlyFields(context.getUser(), "MappingObj") >> ["field1", "field2"]
        0 * serviceFacade.triggerRemoteAction(_, _, _)
        def exception = thrown(ValidateException)
        // 这里不检查具体的异常消息内容，因为它依赖于国际化文本处理
        true
    }
} 