package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.publicobject.*;
import com.facishare.paas.appframework.metadata.publicobject.PublicObjectLogicService;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicObjectJobResultDTO;
import com.facishare.paas.appframework.metadata.publicobject.module.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/11
 */
@Service
@ServiceModule("public_object")
public class ObjectPublicObjectService {

    @Autowired
    private PublicObjectLogicService publicObjectLogicService;

    @ServiceMethod("query_status")
    public QueryPublicObjectStatus.Result queryStatus(QueryPublicObjectStatus.Arg arg, ServiceContext context) {
        PublicObjectStatusResult publicObjectStatusResult = publicObjectLogicService.queryStatus(context.getUser(), arg.getObjectApiName());
        return QueryPublicObjectStatus.Result.of(publicObjectStatusResult);
    }

    @ServiceMethod("find_designer_resource")
    public FindDesignerResource.Result findDesignerResource(FindDesignerResource.Arg arg, ServiceContext context) {
        DesignerResourceResult designerResourceResult = publicObjectLogicService.findDesignerResource(context.getUser(), arg.getObjectApiName());
        return FindDesignerResource.Result.fromDesignerResourceResult(designerResourceResult);
    }

    @ServiceMethod("open_job")
    public CreatePublicObjectJob.Result createOpenJob(CreatePublicObjectJob.Arg arg, ServiceContext context) {
        String jobId = publicObjectLogicService.createJob(context.getUser(), arg.toCreateJobParam(PublicObjectJobType.OPEN_JOB));
        return CreatePublicObjectJob.Result.of(jobId);
    }

    @ServiceMethod("verify_job")
    public CreatePublicObjectJob.Result createVerifyJob(CreatePublicObjectJob.Arg arg, ServiceContext context) {
        String jobId = publicObjectLogicService.createJob(context.getUser(), arg.toCreateJobParam(PublicObjectJobType.VERIFY_JOB));
        return CreatePublicObjectJob.Result.of(jobId);
    }

    @ServiceMethod("query_job")
    public QueryPublicObjectJob.Result queryJob(QueryPublicObjectJob.Arg arg, ServiceContext context) {
        PublicObjectJobResult publicObjectResult = publicObjectLogicService.queryJob(context.getUser(), arg.getObjectApiName(), arg.getJobId());
        return QueryPublicObjectJob.Result.from(publicObjectResult);
    }

    @ServiceMethod("update_display_status")
    public UpdatePublicObjectJobDisplayStatus.Result updateDisplayStatus(UpdatePublicObjectJobDisplayStatus.Arg arg, ServiceContext context) {
        publicObjectLogicService.updateDisplayStatus(context.getUser(), arg.getObjectApiName(), arg.getJobId(), PublicObjectJobResultDTO.NO_DISPLAY);
        return new UpdatePublicObjectJobDisplayStatus.Result();
    }

    @ServiceMethod("invitation_job")
    public CreatePublicObjectJob.Result createInvitationJob(CreatePublicObjectJob.Arg arg, ServiceContext context) {
        String jobId = publicObjectLogicService.createJob(context.getUser(), arg.toCreateJobParam(PublicObjectJobType.INVITATION_JOB));
        return CreatePublicObjectJob.Result.of(jobId);
    }

    @ServiceMethod("enable_job")
    public CreatePublicObjectJob.Result createEnableJob(CreatePublicObjectJob.Arg arg, ServiceContext context) {
        String jobId = publicObjectLogicService.createJob(context.getUser(), arg.toCreateJobParam(PublicObjectJobType.ENABLE_JOB));
        return CreatePublicObjectJob.Result.of(jobId);
    }

    @ServiceMethod("disable_job")
    public CreatePublicObjectJob.Result createDisableJob(CreatePublicObjectJob.Arg arg, ServiceContext context) {
        String jobId = publicObjectLogicService.createJob(context.getUser(), arg.toCreateJobParam(PublicObjectJobType.DISABLE_JOB));
        return CreatePublicObjectJob.Result.of(jobId);
    }

    @ServiceMethod("verify")
    public VerifyJob.Result verifyJob(VerifyJob.Arg arg, ServiceContext context) {
        PublicObjectJobVerifyResult verifyResult = publicObjectLogicService.verifyJob(context.getUser(),
                arg.getObjectApiName(), arg.toPublicObjectJobParamVerifyInfo());
        return VerifyJob.Result.fromVerifyResult(verifyResult);
    }

    @ServiceMethod(("append_public_fields"))
    public AppendPublicFields.Result appendPublicFields(AppendPublicFields.Arg arg, ServiceContext context) {
        publicObjectLogicService.appendPublicFields(context.getUser(), arg.getObjectApiName(), arg.getFields());
        return new AppendPublicFields.Result();
    }

    @ServiceMethod("find_invite_info")
    public FindInviteInfo.Result findInviteInfo(FindInviteInfo.Arg arg, ServiceContext context) {
        PublicObjectJobInvitationInfo invitationInfo = publicObjectLogicService.findInvitationInfo(context.getUser(), arg.getObjectApiName(), arg.getToken());
        return FindInviteInfo.Result.fromPublicObjectJobInvitationInfo(invitationInfo);
    }

    @ServiceMethod("complete_invite")
    public CompleteInvite.Result completeInvite(CompleteInvite.Arg arg, ServiceContext context) {
        CompleteInvite.Result result = new CompleteInvite.Result();
        if (arg.agree()) {
            publicObjectLogicService.agreeInvitation(context.getUser(), arg.getObjectApiName(), arg.getToken());
            return result;
        }
        if (arg.reject()) {
            publicObjectLogicService.rejectInvitation(context.getUser(), arg.getObjectApiName(), arg.getToken());
            return result;
        }
        throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
    }

    @ServiceMethod("find_enterprise_relation")
    public FindEnterpriseRelation.Result findEnterpriseRelation(FindEnterpriseRelation.Arg arg, ServiceContext context) {
        if (Objects.equals("enable", arg.getType())) {
            EnterpriseRelationResult result = publicObjectLogicService.findConnectedEnterpriseRelation(context.getUser(), arg.getObjectApiName(), arg.getQueryParam());
            return FindEnterpriseRelation.Result.from(result);
        }
        if (Objects.equals("disable", arg.getType())) {
            EnterpriseRelationResult result = publicObjectLogicService.findUnconnectedEnterpriseRelation(context.getUser(), arg.getObjectApiName(), arg.getQueryParam());
            return FindEnterpriseRelation.Result.from(result);
        }
        throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
    }

    // 回调记操作记录，并发消息
    @ServiceMethod("completed_callback")
    public CompletedCallback.Result completedCallback(CompletedCallback.Arg arg, ServiceContext context) {
        publicObjectLogicService.completedCallback(context.getUser(), arg.getObjectApiName(), arg.getJobId(), arg.getUpstreamTenantId());
        return new CompletedCallback.Result();
    }

    @ServiceMethod("find_up_stream_enterprise_info")
    public FindUpStreamEnterpriseInfo.Result findUpStreamEnterpriseInfo(FindUpStreamEnterpriseInfo.Arg arg, ServiceContext context) {
        Optional<SimpleEnterpriseInfo> enterpriseSimpleInfo = publicObjectLogicService.findEnterpriseSimpleInfo(context.getUser(), arg.getDescribeApiName());
        return FindUpStreamEnterpriseInfo.Result.of(enterpriseSimpleInfo.orElse(null));
    }

}
