package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.SendTextMessage;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(
        value = "MessagePlatform",
        desc = "发送消息平台消息", // ignoreI18n
        contentType = "application/json"
)
public interface MessagePlatformProxy {

    @POST(value = "/sendTextMessage", desc = "发送文本消息")
    SendTextMessage.Result sendTextMessage(@HeaderMap Map<String, String> header, @Body SendTextMessage.Arg arg);

    @POST(value = "/sendTextLinkMessage", desc = "发送带链接的消息")
    SendTextMessage.Result sendTextLinkMessage(@HeaderMap Map<String, String> header, @Body SendTextMessage.Arg arg);
}
