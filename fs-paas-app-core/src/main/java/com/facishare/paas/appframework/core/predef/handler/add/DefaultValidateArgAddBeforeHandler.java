package com.facishare.paas.appframework.core.predef.handler.add;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.handler.HandlerAttributes;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.facade.ChangeOrderHandlerLogicService;
import com.facishare.paas.appframework.core.predef.handler.AbstractValidateArgSaveBeforeHandler;
import com.facishare.paas.appframework.metadata.ConvertRuleLogicService;
import com.facishare.paas.appframework.metadata.MtCurrency;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.ConvertRuleDataContainer;
import com.facishare.paas.appframework.metadata.repository.model.MtChangeOrderRule;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by zhouwr on 2023/1/11.
 */
@Slf4j
@Component
@HandlerProvider(name = "defaultValidateArgAddBeforeHandler")
public class DefaultValidateArgAddBeforeHandler extends AbstractValidateArgSaveBeforeHandler<AddActionHandler.Arg, AddActionHandler.Result> {
    @Autowired
    private ChangeOrderHandlerLogicService changeOrderHandlerLogicService;
    @Autowired
    private ConvertRuleLogicService convertRuleLogicService;

    @Override
    protected AddActionHandler.Result buildResult(HandlerContext context, AddActionHandler.Arg arg) {
        return new AddActionHandler.Result();
    }

    @Override
    protected void validateConvertRulesExcessCheck(HandlerContext context, AddActionHandler.Arg arg) {
        ConvertRuleDataContainer convertRuleDataContainer = arg.getConvertRuleDataContainer();
        if (Objects.isNull(convertRuleDataContainer)) {
            return;
        }
        IObjectDescribe objectDescribe = arg.getObjectDescribe();
        IObjectData objectData = ObjectDataExt.of(arg.getObjectData()).getObjectData();
        Map<String, IObjectDescribe> detailDescribeMap = arg.getDetailDescribeMap();
        Map<String, List<ObjectDataDocument>> detailObjectData = arg.getDetailObjectData();
        convertRuleLogicService.doConvertRulesExcessCheck(objectDescribe, objectData, detailDescribeMap, ObjectDataDocument.ofDataMap(detailObjectData), convertRuleDataContainer.getConvertRuleList());
    }

    @Override
    protected void validateChangeRuleWithChangeData(HandlerContext context, AddActionHandler.Arg arg) {
        User user = context.getUser();
        ObjectDataDocument objectData = arg.getObjectData();
        String changeRuleName = (String) objectData.get(ObjectDataExt.CHANGE_ORDER_RULE);
        IObjectDescribe objectDescribe = arg.getObjectDescribe();
        if (Strings.isNullOrEmpty(changeRuleName)) {
            return;
        }
        changeOrderHandlerLogicService.validateChangeRuleWithChangeData(user, objectDescribe,
                arg.objectData(), MtChangeOrderRule.CalibrationType.SUBMIT);
    }

    @Override
    protected void checkDetailCurrency(HandlerContext context, Arg arg, Map<String, List<IObjectData>> detailObjectData, IObjectData objectData) {
        //先判断数据参数中是否有币种，后面校验lookup字段过滤条件会用到
        boolean isCurrencyEmpty = ObjectDataExt.isValueEmpty(ObjectDataExt.of(objectData).getCurrency());
        context.setAttribute(HandlerAttributes.IS_CURRENCY_EMPTY, isCurrencyEmpty);

        //补充币种、汇率等字段
        //没有传币种的从对象的币种和主对象保持一致
        IObjectDescribe objectDescribe = arg.getObjectDescribe();
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            ObjectDescribeExt.of(objectDescribe).getMasterDetailFieldDescribe().ifPresent(masterDetail -> {
                String masterDataId = String.valueOf(objectData.get(masterDetail.getApiName()));
                IObjectData masterData = serviceFacade.findObjectDataIgnoreAll(context.getUser(), masterDataId, masterDetail.getTargetApiName());
                ObjectDataExt.of(objectData).checkDetailCurrency(masterData);
            });
        } else {
            List<MtCurrency> currencyList = serviceFacade.getMultiCurrencyLogicService().findCurrencyList(context.getUser());
            String personCurrencyCode = serviceFacade.getMultiCurrencyLogicService().findPersonCurrencyCode(context.getUser());
            ObjectDataExt.of(objectData).fillMultiCurrencyFields(currencyList, personCurrencyCode);
            CollectionUtils.nullToEmpty(detailObjectData).forEach((apiName, dataList) ->
                    dataList.forEach(data -> ObjectDataExt.of(data).checkDetailCurrency(objectData)));
        }
    }

    @Override
    protected void validateLookupData(HandlerContext context, AddActionHandler.Arg arg, IObjectData objectData, IObjectDescribe describe) {
        IObjectData cpData = objectData;
        //如果数据参数中没有币种，则先清空币种，再校验lookup字段的过滤条件，防止本位币校验通不过
        boolean isCurrencyEmpty = Boolean.TRUE.equals(context.getAttribute(HandlerAttributes.IS_CURRENCY_EMPTY));
        if (isCurrencyEmpty) {
            cpData = ObjectDataExt.of(objectData).copy();
            ObjectDataExt.of(cpData).setCurrency(null);
        }
        super.validateLookupData(context, arg, cpData, describe);
    }
}
