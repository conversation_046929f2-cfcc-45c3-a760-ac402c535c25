/**
 * <AUTHOR>
 * @codeName OnlineDocFeishu
 * @description 新建
 * @createTime 2025-01-07
 */
class OnlineDocLarksute implements OnlineDocPersonalPlugin {

    private static String CONTENT_TYPE = "application/json; charset=utf-8"

    //
    private static String HOST = "https://open.larksuite.com"
    //用户授权url
    private static String AUTH_CODE_URL = "/open-apis/authen/v1/authorize"
    //获取app token url
    private static String QUERY_APP_ACCESS_TOKEN_URL = "/open-apis/auth/v3/app_access_token/internal"
    //获取user token url
    private static String QUERY_USER_ACCESS_TOKEN_URL = "/open-apis/authen/v1/oidc/access_token"
    //刷新user token url
    private static String REFRESH_USER_ACCESS_TOKEN_URL = "/open-apis/authen/v1/oidc/refresh_access_token"
    //获取文件列表url
    private static String QUERY_FILE_LIST_URL = "/open-apis/drive/v1/files?direction=DESC&order_by=CreatedTime"
    //获取文件夹元数据url
    private static String QUERY_FILE_METADATA_URL = "/open-apis/drive/explorer/v2/root_folder/meta"
    //授权回调地址后缀
    private static String CALLBACK_URL_SUFFIX = "/larksuite/authRedirect"

    //保存token信息到本地缓存,默认2小时
    private void saveAccessToken2Cache(String tenantId, String userId, String pluginApiName, Map<String, Object> runtimeData, int expiresSecond) {
        Map tokenMap = [:]
        tokenMap.putAll(runtimeData)
        tokenMap.put("token_expires_millis", DateTime.now().toTimestamp() + (expiresSecond - 30) * 1000)

        String key = "OnlineDocFeishuAccess_token_" + userId + "_" + pluginApiName
        Cache cache = Fx.cache.getDefaultCache()
        String value = Fx.json.toJson(tokenMap)
        cache.put(key, value, expiresSecond)
        log.info("saveAccessToken2Cache key:" + key)
    }

    //获取token信息从本地缓存中
    private String getAccessTokenFromCache(String tenantId, String userId, String pluginApiName) {
        String key = "OnlineDocFeishuAccess_token_" + userId + "_" + pluginApiName
        Cache cache = Fx.cache.getDefaultCache()
        String value = cache.get(key) as String
        if (value == null) {
            log.info("getAccessTokenFromCache key:" + key + " value:null")
            return null
        }
        Map tokenMap = Fx.json.parse(value)
        String token = tokenMap["access_token"] as String
        Long expired = tokenMap["token_expires_millis"] as Long
        if (expired <= DateTime.now().toTimestamp()) {
            log.info("getAccessTokenFromCache key:" + key + " value:expired")
            return null
        }
        log.info("getAccessTokenFromCache key:" + key)
        return token
    }

    //获取runtimeData
    private Map getOrRefreshAccessToken(String pluginApiName, Map devInfo, Map authRuntimeData, FunctionContext context) {
        String accessTokenInCache = getAccessTokenFromCache(context.getTenantId(), context.getUserId(), pluginApiName)
        if (accessTokenInCache != null) {
            Map<String, Object> refreshMap = [:]
            refreshMap.put("errorCode", 0)
            refreshMap.put("accessToken", accessTokenInCache)
            return refreshMap
        }
        //获取app token
        String appId = devInfo.get("appId") as String
        String appKey = devInfo.get("appKey") as String
        Map appAccessTokenResultMap = queryAppAccessToken(pluginApiName, appId, appKey, context)
        String appAccessToken = appAccessTokenResultMap["accessTokenMap"]["app_access_token"] as String
        //刷新accessToken
        Map<String, Object> refreshMap = refreshAccessToken(pluginApiName, devInfo, authRuntimeData, appAccessToken, context)
        return refreshMap
    }

    //构建header
    private Map buildHeader(String accessToken) {
        Map header = [:]
        header["Authorization"] = "Bearer " + accessToken
        //header["Content-Type"] = CONTENT_TYPE
        return header
    }

    //企业认证 暂时未用到
    public EnterpriseAuth.Result enterpriseAuth(EnterpriseAuth.Arg arg, FunctionContext context) {
        log.info("enterpriseAuth arg:" + arg)
        String appId = arg.getDevInfo().get("appId") as String
        String appKey = arg.getDevInfo().get("appKey") as String
        String pluginApiName = arg.getPluginApiName()

        EnterpriseAuth.Result result = new EnterpriseAuth.Result()
        //result.setErrorCode(0)
        //result.setErrorMessage(errorMessage)
        log.info("enterpriseAuth result:" + result)
        return result
    }

    //刷新access token
    private Map<String, Object> refreshAccessToken(String pluginApiName,
                                                   Map<String, String> devInfo,
                                                   Map<String, Object> authRuntimeData,
                                                   String appAccessToken,
                                                   FunctionContext context) {
        log.info("refreshAccessToken arg:" + pluginApiName)
        Map authMap = authRuntimeData
        if (authMap.isEmpty()) {
            //从未授权，模拟金山错误码，下发授权url
            Map<String, Object> resultMap = [:]
            resultMap.put("errorCode", 99991668)
            resultMap.put("errorMessage", "never to auth")
            log.info("refreshAccessToken failed resultMap:" + resultMap)
            return resultMap
        }
        String appId = devInfo.get("appId")
        String appKey = devInfo.get("appKey")
        String refreshToken = authMap.get("refresh_token")
        String bizUrl = HOST + REFRESH_USER_ACCESS_TOKEN_URL
        //
        Map headers = [:]
        headers["Content-Type"] = CONTENT_TYPE
        headers["Authorization"] = "Bearer " + appAccessToken
        //
        Map requestBody = [:]
        requestBody["grant_type"] = "refresh_token"
        requestBody["refresh_token"] = refreshToken
        //
        log.info("refreshAccessToken request begin:" + bizUrl)
        def (Boolean error, HttpResult httpResult, String errorMessage) = Fx.http.post(bizUrl, headers, requestBody, 10000, false, 0)
        log.info("refreshAccessToken request finish:" + httpResult)
        //检查请求错误
        if (error || httpResult == null) {
            log.info("refreshAccessToken request null error :" + errorMessage)
            Map<String, Object> resultMap = [:]
            resultMap.put("errorCode", -1)
            resultMap.put("errorMessage", "request error:" + errorMessage)
            return resultMap
        }
        Map contentMap = null
        if (httpResult.statusCode == 200) {
            contentMap = httpResult.content as Map
        } else {
            contentMap = Fx.json.parse(httpResult.content as String)
        }
        Integer bizErrorCode = contentMap.get("code") as Integer
        String bizErrorMessage = contentMap.get("message") as String
        //检查请求业务错误
        if (bizErrorCode != 0) {
            Map<String, Object> resultMap = [:]
            resultMap.put("errorCode", bizErrorCode)
            resultMap.put("errorMessage", bizErrorMessage)
            log.info("refreshAccessToken request biz failed error:" + contentMap)
            return resultMap
        }
        //补充过期时间戳
        Map tokenMap = contentMap
        String accessToken = tokenMap["access_token"] as String
        Integer expiresSecond = tokenMap["expires_in"] as Integer
        Integer refreshTokenExpiresSecond = tokenMap["refresh_token_expires_in"] as Integer
        //保存到本地缓存
        saveAccessToken2Cache(context.getTenantId(), context.getUserId(), pluginApiName, tokenMap, expiresSecond)
        //返回结果
        Map<String, Object> resultMap = [:]
        resultMap.put("errorCode", 0)
        resultMap.put("accessToken", accessToken)
        resultMap.put("accessTokenMap", tokenMap)
        resultMap.put("expiredTime", DateTime.now().toTimestamp() + refreshTokenExpiresSecond * 1000L)
        log.info("refreshAccessToken finish success:" + resultMap.size())
        return resultMap
    }

    //请求access token
    private Map<String, Object> queryAppAccessToken(String pluginApiName,
                                                    String appId,
                                                    String appKey,
                                                    FunctionContext context) {
        String bizUrl = HOST + QUERY_APP_ACCESS_TOKEN_URL
        Map headers = ["Content-Type": CONTENT_TYPE]
        //
        Map requestBody = [:]
        requestBody["app_id"] = appId
        requestBody["app_secret"] = appKey
        //
        log.info("queryAppAccessToken request begin:" + bizUrl)
        def (Boolean error, HttpResult httpResult, String errorMessage) = Fx.http.post(bizUrl, headers, requestBody, 10000, false, 0)
        log.info("queryAppAccessToken request finish:" + httpResult)
        //检查请求错误
        if (error || httpResult == null) {
            Map resultMap = [:]
            resultMap["errorCode"] = -1
            resultMap["errorMessage"] = "request error:" + errorMessage
            log.info("queryAppAccessToken request null error :" + errorMessage)
            return resultMap
        }
        Map contentMap = null
        if (httpResult.statusCode == 200) {
            contentMap = httpResult.content as Map
        } else {
            contentMap = Fx.json.parse(httpResult.content as String)
        }
        Integer bizErrorCode = contentMap.get("code") as Integer
        String bizErrorMessage = contentMap.get("msg") as String
        //检查请求业务错误
        if (bizErrorCode != 0) {
            Map resultMap = [:]
            resultMap["errorCode"] = bizErrorCode
            resultMap["errorMessage"] = bizErrorMessage
            log.info("queryAppAccessToken request biz failed error:" + contentMap)
            return resultMap
        }
        //补充过期时间戳
        Map tokenMap = contentMap
        Integer accessTokenExpiresSecond = tokenMap["expire"] as Integer
        //返回结果
        Map resultMap = [:]
        resultMap["errorCode"] = 0
        resultMap["accessTokenMap"] = tokenMap
        resultMap["expiredTime"] = DateTime.now().toTimestamp() + accessTokenExpiresSecond * 1000L
        return resultMap
    }

    //请求access token
    private Map<String, Object> queryAccessToken(String pluginApiName,
                                                 String appAccessToken,
                                                 String code,
                                                 FunctionContext context) {
        String bizUrl = HOST + QUERY_USER_ACCESS_TOKEN_URL
        Map headers = [:]
        headers["Content-Type"] = CONTENT_TYPE
        headers["Authorization"] = "Bearer " + appAccessToken
        //
        Map requestBody = [:]
        requestBody["grant_type"] = "authorization_code"
        requestBody["code"] = code
        //
        log.info("queryAccessToken request begin:" + bizUrl)
        def (Boolean error, HttpResult httpResult, String errorMessage) = Fx.http.post(bizUrl, headers, requestBody, 10000, false, 0)
        log.info("queryAccessToken request finish:" + httpResult)
        //检查请求错误
        if (error || httpResult == null) {
            Map resultMap = [:]
            resultMap["errorCode"] = -1
            resultMap["errorMessage"] = "request error:" + errorMessage
            log.info("queryAccessToken request null error :" + resultMap)
            return resultMap
        }
        Map contentMap = null
        if (httpResult.statusCode == 200) {
            contentMap = httpResult.content as Map
        } else {
            contentMap = Fx.json.parse(httpResult.content as String)
        }
        Integer bizErrorCode = contentMap.get("code") as Integer
        String bizErrorMessage = contentMap.get("message") as String
        //检查请求业务错误
        if (bizErrorCode != 0) {
            Map resultMap = [:]
            resultMap["errorCode"] = bizErrorCode
            resultMap["errorMessage"] = bizErrorMessage
            log.info("queryAccessToken request biz failed error:" + contentMap)
            return resultMap
        }
        //补充过期时间戳
        Map tokenMap = contentMap["data"] as Map
        tokenMap["appAccessToken"] = appAccessToken
        Integer accessTokenExpiresSecond = tokenMap["expires_in"] as Integer
        Integer refreshTokenExpiresSecond = tokenMap["refresh_expires_in"] as Integer
        //保存到本地缓存
        saveAccessToken2Cache(context.getTenantId(), context.getUserId(), pluginApiName, tokenMap, accessTokenExpiresSecond)
        //返回结果
        Map resultMap = [:]
        resultMap["errorCode"] = 0
        resultMap["accessTokenMap"] = tokenMap
        resultMap["expiredTime"] = DateTime.now().toTimestamp() + refreshTokenExpiresSecond * 1000L
        return resultMap
    }

    //获取个人授权url
    public GetPersonalAuthUrl.Result getPersonalAuthUrl(GetPersonalAuthUrl.Arg arg, FunctionContext context) {
        //标准日志，请勿删除
        log.info("getPersonalAuthUrl arg:" + arg)

        /*此处开始加业务*/
        String appId = arg.getDevInfo().get("appId")
        String callbackUrl = arg.getDevInfo().get("callbackUrl") + CALLBACK_URL_SUFFIX

        //拼装纷享业务参数
        Map<String, String> authMap = [:]
        authMap.put("pluginApiName", arg.getPluginApiName())
        authMap.put("enterpriseId", context.getTenantId())
        authMap.put("employeeId", context.getUserId())
        //拼接如state中
        String state = Fx.crypto.base64.encode(Fx.json.toJson(authMap) as byte[])
        String bizUrl = HOST + AUTH_CODE_URL
        bizUrl += "?app_id=" + appId
        bizUrl += "&redirect_uri=" + Fx.crypto.URL.encode(callbackUrl).result()
        bizUrl += "&scope=drive:drive offline_access"
        bizUrl += "&state=" + state
        GetPersonalAuthUrl.Result result = new GetPersonalAuthUrl.Result()
        result.setErrorCode(0)
        result.setAuthUrl(bizUrl)
        //标准日志，请勿删除
        log.info("getPersonalAuthUrl result:" + result)
        return result
    }

    //处理callback回来的数据
    public ActionCallback.Result actionCallback(ActionCallback.Arg arg, FunctionContext context) {
        //标准日志，请勿删除
        log.info("actionCallback arg:" + arg)

        /*此处开始加业务*/
        String pluginApiName = arg.getPluginApiName()
        String code = arg.getCode()
        String appId = arg.getDevInfo().get("appId")
        String appKey = arg.getDevInfo().get("appKey")
        String callbackUrl = arg.getDevInfo().get("callbackUrl") + CALLBACK_URL_SUFFIX
        Integer errorCode = null
        String errorMessage = null
        //获取app token
        Map appAccessTokenResultMap = queryAppAccessToken(pluginApiName, appId, appKey, context)
        Map appAccessTokenMap = appAccessTokenResultMap["accessTokenMap"] as Map
        String appAccessToken = appAccessTokenResultMap["accessTokenMap"]["app_access_token"] as String
        errorCode = appAccessTokenResultMap["errorCode"] as Integer
        errorMessage = appAccessTokenResultMap["errorMessage"] as String
        if (errorCode != 0) {
            ActionCallback.Result result = new ActionCallback.Result()
            result.setErrorCode(errorCode)
            result.setErrorMessage(errorMessage)
            //标准日志，请勿删除
            log.info("actionCallback queryAppAccessToken error result:" + result)
            return result
        }
        //获取用户授权token
        Map accessTokenResultMap = queryAccessToken(pluginApiName, appAccessToken, code, context)
        errorCode = accessTokenResultMap["errorCode"] as Integer
        errorMessage = accessTokenResultMap["errorMessage"] as String
        if (errorCode != 0) {
            ActionCallback.Result result = new ActionCallback.Result()
            result.setErrorCode(errorCode)
            result.setErrorMessage(errorMessage)
            //标准日志，请勿删除
            log.info("actionCallback queryAccessToken error result:" + result)
            return result
        }
        ActionCallback.Result result = new ActionCallback.Result()
        result.setErrorCode(errorCode)
        result.setRuntimeData(accessTokenResultMap["accessTokenMap"] as Map)
        result.setExpiredTime(accessTokenResultMap["expiredTime"] as Long)
        //标准日志，请勿删除
        log.info("actionCallback result:" + result)
        return result
    }

    //请求文件列表
    public QueryFileList.Result queryFileList(QueryFileList.Arg arg, FunctionContext context) {
        //标准日志，请勿删除
        log.info("queryFileList arg:" + arg)
        String pluginApiName = arg.getPluginApiName()
        String appId = arg.getDevInfo().get("appId") as String
        String appKey = arg.getDevInfo().get("appKey") as String
        Map authRuntimeData = arg.getAuthRuntimeData()
        Map pluginRuntimeData = arg.getPluginRuntimeData()
        //获取or更新accessToken
        Map accessTokenMap = getOrRefreshAccessToken(pluginApiName, arg.getDevInfo(), authRuntimeData, context)
        Integer errorCode = accessTokenMap["errorCode"] as Integer
        String errorMessage = accessTokenMap["errorMessage"] as String
        String accessToken = accessTokenMap["accessToken"] as String
        Map newAccessTokenMap = accessTokenMap["accessTokenMap"] as Map
        Long expiredTime = accessTokenMap["expiredTime"] as Long
        if (errorCode != 0) {
            //返回失败结果
            return buildQueryFileListFailedResult(arg.getPluginApiName(), errorCode, errorMessage, arg.getDevInfo(), context)
        }
        //查询团队列表
        Map<String, Object> extraInfo = (arg.getExtraInfo() == null) ? [:] : arg.getExtraInfo()
        //查询文件
        String folderToken = null
        String nextPageToken = extraInfo.get("next_page_token")
        //进入选择的文件夹
        if (arg.getFolder() != null) {
            folderToken = arg.getFolder().getFileId()
        }
        //先查询元数据信息
        if (folderToken == null) {
            Map metadataTokenMap = queryFileMetadataToken(accessToken)
            errorCode = metadataTokenMap.get("errorCode") as Integer
            errorMessage = metadataTokenMap.get("errorMessage") as String
            folderToken = metadataTokenMap.get("metadataToken") as String
            if (errorCode != 0) {
                //返回失败结果
                return buildQueryFileListFailedResult(arg.getPluginApiName(), errorCode, errorMessage, arg.getDevInfo(), context)
            }
        }
        //请求文件列表清单
        Map fileListMap = queryFiles(arg.getPluginApiName(), accessToken, folderToken, nextPageToken)
        errorCode = fileListMap.get("errorCode") as Integer
        errorMessage = fileListMap.get("errorMessage") as String
        List<Map> fileList = fileListMap.get("fileList") as List
        boolean hasMore = fileListMap["hasMore"] as boolean
        extraInfo["next_page_token"] = fileListMap["next_page_token"] as String
        if (errorCode != 0) {
            //返回失败结果
            return buildQueryFileListFailedResult(arg.getPluginApiName(), errorCode, errorMessage, arg.getDevInfo(), context)
        }
        //返回结果
        QueryFileList.Result result = new QueryFileList.Result()
        result.setErrorCode(errorCode)
        result.setErrorMessage(errorMessage)
        result.setFileList(fileList)
        result.setHasMore(hasMore)
        result.setExtraInfo(extraInfo)
        result.setPluginRuntimeData(newAccessTokenMap)
        result.setExpiredTime(expiredTime == null ? 0 : expiredTime)
        //标准日志，请勿删除
        log.info("queryFileList result:" + result)
        return result
    }

    private QueryFileList.Result buildQueryFileListFailedResult(String pluginApiName,
                                                                int errorCode,
                                                                String errorMessage,
                                                                Map devInfo,
                                                                FunctionContext context) {
        //以下错误需要用户重新授权
        //{"result":4001,"msg":"Invalid token, please refresh"}
        //{"result":20037,"msg":"The refresh token passed has expired. Please generate a new one"}
        //{"result":99991677,"msg":"token expire"}
        //{"result":99991668,"msg":"Invalid access token for authorization. Please make a request with token attached."}
        //{"result":99991669,"msg":"invalid user refresh token"}
        if (errorCode == 4001
                || errorCode == 20037
                || errorCode == 99991677
                || errorCode == 99991668
                || errorCode == 99991669) {
            //检查是否需要授权
            GetPersonalAuthUrl.Arg authUrlArg = new GetPersonalAuthUrl.Arg()
            authUrlArg.setPluginApiName(pluginApiName)
            authUrlArg.setDevInfo(devInfo)
            GetPersonalAuthUrl.Result authUrlResult = getPersonalAuthUrl(authUrlArg, context)
            //
            QueryFileList.Result result = new QueryFileList.Result()
            result.setErrorCode(errorCode)
            result.setErrorMessage(errorMessage)
            result.setAuthUrl(authUrlResult.getAuthUrl())
            return result
        }
        Fx.message.throwException(errorMessage)
    }

    //获取预览url
    public GetPreviewUrl.Result getPreviewUrl(GetPreviewUrl.Arg arg, FunctionContext context) {
        //标准日志，请勿删除
        log.info("getPreviewUrl arg:" + arg)
        //返回结果
        GetPreviewUrl.Result result = new GetPreviewUrl.Result()
        result.setErrorCode(0)
        result.setFileUrl(arg.getFile().getFileUrl())
        //标准日志，请勿删除
        log.info("getPreviewUrl result:" + result)
        return result
    }

    //获取文件元数据
    private Map<String, Object> queryFileMetadataToken(String accessToken) {
        String bizUrl = HOST + QUERY_FILE_METADATA_URL
        Map headers = buildHeader(accessToken)
        log.info("queryFileMetadata request begin:" + bizUrl)
        def (Boolean error, HttpResult httpResult, String errorMessage) = Fx.http.get(bizUrl, headers, 10000, false, 0)
        log.info("queryFileMetadata request finish:" + httpResult)
        //检查请求错误
        if (error || httpResult == null) {
            log.info("queryFileMetadata request null error :" + errorMessage)
            Map<String, Object> resultMap = [:]
            resultMap.put("errorCode", -1)
            resultMap.put("errorMessage", "request error:" + errorMessage)
            return resultMap
        }
        Map contentMap = null
        if (httpResult.statusCode == 200) {
            contentMap = httpResult.content as Map
        } else {
            contentMap = Fx.json.parse(httpResult.content as String)
        }
        Integer bizErrorCode = contentMap.get("code") as Integer
        String bizErrorMessage = contentMap.get("msg") as String
        //检查请求业务错误
        if (bizErrorCode != 0) {
            log.info("queryFileMetadata request biz failed error:" + contentMap)
            Map<String, Object> resultMap = [:]
            resultMap.put("errorCode", bizErrorCode)
            resultMap.put("errorMessage", bizErrorMessage)
            return resultMap
        }
        Map<String, Object> resultMap = [:]
        resultMap.put("errorCode", 0)
        resultMap.put("metadataToken", contentMap["data"]["token"] as String)
        return resultMap
    }

    //查询文件列表 pageNumber从0开始
    private Map<String, Object> queryFiles(String pluginApiName, String accessToken, String folderToken, String pageToken) {
        int pageSize = 20
        String bizUrl = HOST + QUERY_FILE_LIST_URL + "&folder_token=" + folderToken + "&page_size=" + pageSize
        if (pageToken != null) {
            bizUrl += "&page_token=" + pageToken
        }
        Map headers = buildHeader(accessToken)
        log.info("queryFiles request begin:" + bizUrl)
        def (Boolean error, HttpResult httpResult, String errorMessage) = Fx.http.get(bizUrl, headers, 10000, false, 0)
        log.info("queryFiles request finish:" + httpResult)
        //检查请求错误
        if (error || httpResult == null) {
            log.info("queryFiles request null error :" + errorMessage)
            Map<String, Object> resultMap = [:]
            resultMap.put("errorCode", -1)
            resultMap.put("errorMessage", "request error:" + errorMessage)
            return resultMap
        }
        Map contentMap = null
        if (httpResult.statusCode == 200) {
            contentMap = httpResult.content as Map
        } else {
            contentMap = Fx.json.parse(httpResult.content as String)
        }
        Integer bizErrorCode = contentMap.get("code") as Integer
        String bizErrorMessage = contentMap.get("msg") as String
        //检查请求业务错误
        if (bizErrorCode != 0) {
            log.info("queryFiles request biz failed error:" + contentMap)
            Map<String, Object> resultMap = [:]
            resultMap.put("errorCode", bizErrorCode)
            resultMap.put("errorMessage", bizErrorMessage)
            return resultMap
        }
        List<Map> fileList = contentMap["data"]["files"] as List
        String nextPageToken = contentMap["data"]["next_page_token"] as String
        boolean hasMore = contentMap["data"]["has_more"] as Boolean

        Map<String, Object> resultMap = [:]
        resultMap.put("errorCode", 0)
        resultMap.put("fileList", convert2FileInfo(pluginApiName, fileList))
        resultMap.put("hasMore", hasMore)
        resultMap.put("next_page_token", nextPageToken)
        return resultMap
    }

    //转换成规范结构
    private List<Map> convert2FileInfo(String pluginApiName, List<Map> fileList) {
        List<Map> fileInfoList = []
        fileList.each() { item ->
            String name = item.getOrDefault("name", "未命名") as String
            name = name.length() <= 0 ? "未命名" : name
            Map fileInfo = [:]
            fileInfo.put("fileId", item.get("token"))
            fileInfo.put("fileUrl", item.get("url"))
            fileInfo.put("fileName", name)
            fileInfo.put("fileExt", findFileExtension(name, item.get("type") as String))
            fileInfo.put("fileType", convertFileType(item.get("type") as String))
            fileInfo.put("fileSize", -1)
            fileInfo.put("createTime", 1000 * (item.get("created_time") as long))
            fileInfo.put("updateTime", 1000 * (item.get("modified_time") as long))
            fileInfo.put("pluginApiName", pluginApiName)
            fileInfoList.add(fileInfo)
        }
        return fileInfoList
    }

    //解析文件后缀名
    private String findFileExtension(String fileName, String fileType) {
        def parts = fileName.split("\\.")
        if (parts.size() > 1) {
            return parts[parts.size() - 1]
        }
        //文件名不带后缀，尝试返回文件类型值
        switch (fileType) {
            case "doc": //旧版文档
            case "sheet": //表格
            case "mindnote": //思维导图
            case "bitable": //多维表格
            case "file": //文件
            case "docx": //新版文档
            case "shortcut": //快捷方式
                return fileType
            default:
                return null
        }
    }

    //检查文件类型，只支持文件夹、文件、未知
    private String convertFileType(String fileType) {
        switch (fileType) {
            case "doc": //旧版文档
            case "sheet": //表格
            case "mindnote": //思维导图
            case "bitable": //多维表格
            case "file": //文件
            case "docx": //新版文档
                return "file"
            case "folder":
                return "folder"
            case "shortcut": //快捷方式
            default:
                return "unknown"
        }
    }

    static void main(String[] args) {

    }
}