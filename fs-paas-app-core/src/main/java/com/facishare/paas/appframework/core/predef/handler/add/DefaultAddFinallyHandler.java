package com.facishare.paas.appframework.core.predef.handler.add;

import com.facishare.paas.appframework.common.util.CacheContext;
import com.facishare.paas.appframework.common.util.ContextCacheKeys;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.handler.AbstractSaveFinallyHandler;
import com.facishare.paas.appframework.core.predef.service.ParamsIdempotentService;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by zhouwr on 2023/3/16.
 */
@Component
@HandlerProvider(name = "defaultAddFinallyHandler")
public class DefaultAddFinallyHandler extends AbstractSaveFinallyHandler {
    @Autowired
    private ParamsIdempotentService paramsIdempotentService;

    @Override
    public Result handle(HandlerContext context, Arg arg) {
        RLock paramsIdempotentLock = CacheContext.getContext().removeCache(ContextCacheKeys.PARAMS_IDEMPOTENT_LOCK);
        if (!arg.isDoActComplete()) {
            paramsIdempotentService.unlock(paramsIdempotentLock);
        }
        return super.handle(context, arg);
    }
}
