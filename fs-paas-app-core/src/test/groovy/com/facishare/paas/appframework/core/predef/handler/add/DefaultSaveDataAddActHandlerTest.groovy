package com.facishare.paas.appframework.core.predef.handler.add

import com.google.common.base.Charsets
import com.google.common.hash.Hashing
import spock.lang.Specification

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/12/31
 */
class DefaultSaveDataAddActHandlerTest extends Specification {

    def "test_buildIdempotentKey"() {
        given:
        def postId = "720ca9dfe9fa4cbe80c87cff04a033a7O-E.quantum.1017-90617182"
        def tenantId = "708381"
        def describeApiName = "object_1e3aR__c"
        def code = "Add"
        when:
        def ret = Hashing.sha256().newHasher()
                .putString("saveMasterAndDetailData", Charsets.UTF_8)
                .putString(postId, Charsets.UTF_8)
                .putString(tenantId, Charsets.UTF_8)
                .putString(describeApiName + "/" + code, Charsets.UTF_8)
                .hash().toString()
        println(ret)
        then:
        expect == ret
        where:
        expect << ["8c2f674f49fd10887b216f24f6129fa22811f9760fa59e32fa4f22a1ad70828d"]
    }
}
