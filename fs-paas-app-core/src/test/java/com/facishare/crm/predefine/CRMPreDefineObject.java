package com.facishare.crm.predefine;

import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObject;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;

/**
 * for test
 * <p>
 * Created by liyiguang on 2017/7/9.
 */
public enum CRMPreDefineObject implements PreDefineObject {

    Account("AccountObj");

    CRMPreDefineObject(String apiName) {
        this.apiName = apiName;
    }

    @Override
    public String getApiName() {
        return apiName;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String actionCode) {
        String className = PACKAGE_NAME + ".action." + this + actionCode + "Action";
        return new ActionClassInfo(className);
    }

    public ControllerClassInfo getControllerClassInfo(String methodName) {
        String className = PACKAGE_NAME + ".controller." + this + methodName + "Controller";
        return new ControllerClassInfo(className);
    }

    @Override
    public String getPackageName() {
        return PACKAGE_NAME;
    }

    private String apiName;
    private static String PACKAGE_NAME = "com.facishare.crm.predefine";



    public static void init(){
        for (CRMPreDefineObject object : CRMPreDefineObject.values()) {
            PreDefineObjectRegistry.register(object);
        }
    }

}


