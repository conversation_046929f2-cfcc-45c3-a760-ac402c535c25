package com.facishare.paas.appframework.core

import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.User
import groovy.util.logging.Slf4j
import spock.lang.Specification

/**
 * create by <PERSON><PERSON><PERSON> on 2018/12/10
 */
@Slf4j

class BaseTest extends Specification {

    protected String tenantId = "74255"
    protected User user
    protected RequestContext requestContext

    def setup() {
        user = initUser()
        requestContext = buildRequestContext(user)
    }

    protected User initUser() {
        buildUser(tenantId, "1000")
    }

    def buildRequestContext(User user, String appId = null) {
        def requestContext = RequestContext.builder()
                .tenantId(user.getTenantId())
                .contentType(RequestContext.ContentType.FULL_JSON)
                .user(user)
                .appId(appId)
                .build()
        RequestContextManager.setContext(requestContext)
        this.requestContext = requestContext
    }

    def buildUser(String tenantId, String userId, String outTenantId = null, String outUserId = null) {
        new User(tenantId, userId, outUserId, outTenantId)
    }
}
