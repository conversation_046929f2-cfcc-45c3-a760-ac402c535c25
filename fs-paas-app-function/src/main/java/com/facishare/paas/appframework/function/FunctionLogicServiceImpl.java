package com.facishare.paas.appframework.function;

import com.facishare.function.Function;
import com.facishare.function.exception.FunctionCompileException;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.PhoneNumberService;
import com.facishare.paas.appframework.common.service.ReferenceServiceProxy;
import com.facishare.paas.appframework.common.service.dto.BatchDeleteReference;
import com.facishare.paas.appframework.common.service.dto.DeleteAndCreateReference;
import com.facishare.paas.appframework.common.service.dto.FindReferenceByTarget;
import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.exception.FunctionTimeoutException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.rest.InnerHeaders;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.function.dto.*;
import com.facishare.paas.appframework.function.util.FunctionQueryTemplateUtils;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.quota.TenantLicenseInfo;
import com.facishare.paas.appframework.license.util.ModulePara;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.log.dto.FunctionValidationLog;
import com.facishare.paas.appframework.log.dto.InsertFunctionVerificationLog;
import com.facishare.paas.appframework.log.dto.QueryFunctionVerificationLog;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.relation.TargetTypes;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.UdefFunctionService;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.bitbucket.cowwoc.diffmatchpatch.DiffMatchPatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.SocketTimeoutException;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.model.RequestContext.FS_DEVICE_TYPE;
import static com.facishare.paas.appframework.function.FunctionRestProxy.X_FS_CLIENT_INFO;

/**
 * 函数业务流逻辑
 * <p>
 * Created by liyiguang on 2018/2/3.
 */
@Slf4j
@Service("functionLogicService")
public class FunctionLogicServiceImpl implements FunctionLogicService {
    /**
     * 函数元数据服务
     */
    @Autowired
    private UdefFunctionService udefFunctionService;

    @Autowired
    private LicenseService licenseService;

    @Autowired
    private LogService logService;
    @Autowired
    private FunctionRestProxy functionRestProxy;
    @Autowired
    private ConfigService configService;
    @Autowired
    private SecurityChecker securityChecker;
    @Autowired
    private PhoneNumberService phoneNumberService;
    @Autowired
    private ReferenceServiceProxy referenceServiceProxy;

    private static final String FUNCTION_DEVELOP_VERIFICATION_KEY = "function.develop.verification";
    private static final String ENABLE_DEVELOP_VERIFICATION_VALUE = "enable";
    private static final String DISABLE_DEVELOP_VERIFICATION_VALUE = "disable";

    @Override
    public IUdefFunction createUDefFunction(User user, IUdefFunction function) {
        function.setTenantId(user.getTenantId());
        function.setCreatedBy(user.getUserId());

        //校验body size
        UdefFunctionExt.of(function).validateBodySize();

        Map<String, Object> map = Maps.newHashMap();
        map.put("api_name", function.getApiName());
        map.put("limit", 10);
        map.put("offset", 0);
        QueryResult result = queryUDefFunction(user, map);
        if (Objects.nonNull(result) && CollectionUtils.notEmpty(result.getData())) {
            throw new ValidateException(I18NExt.text(I18NKey.FUNCTION_API_NAME_ALREADY_EXIST));
        }

        //编译检查
        try {
            compile(user, function);
        } catch (FunctionCompileException e) {
            throw new MetaDataBusinessException(e.getMessage());
        }

        try {
            setFunctionDevelopInfo(user, function);
            IUdefFunction udefFunction = udefFunctionService.create(function);
            logService.logUdefFunction(user, EventType.ADD, ActionType.CREATE_FUNCTION, function.getBindingObjectApiName(), function.getApiName(), udefFunction);
            return udefFunction;
        } catch (MetadataServiceException e) {
            log.warn("createUDefFunction error,user:{},function:{}", user, function, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public IUdefFunction updateUDefFunction(User user, IUdefFunction function) {
        function.setLastModifiedBy(user.getUserId());

        //校验body size
        UdefFunctionExt.of(function).validateBodySize();

        //编译检查
        try {
            compile(user, function);
        } catch (FunctionCompileException e) {
            throw new MetaDataBusinessException(e.getMessage());
        }

        try {
            setFunctionDevelopInfo(user, function);
            IUdefFunction udefFunction = udefFunctionService.update(function);
            logService.logUdefFunction(user, EventType.MODIFY, ActionType.UPDATE_FUNCTION, function.getBindingObjectApiName(), function.getApiName(), udefFunction);
            return udefFunction;
        } catch (MetadataServiceException e) {
            log.warn("updateUDefFunction error,user:{}，function:{}", user, function, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public IUdefFunction createFunctionByRest(User user, IUdefFunction function) {
        CreateFunction.Arg arg = CreateFunction.Arg.builder()
                .function(FunctionInfo.of(function))
                .build();
        CreateFunction.Result createResult;
        try {
            createResult = functionRestProxy.create(getHeaders(user), arg);
        } catch (ValidateException e) {
            throw e;
        } catch (Exception e) {
            log.error("functionRestProxy.create exception ", e);
            throw new FunctionException(I18N.text(I18NKey.FUNC_FAIL));
        }
        return createResult.getFunction().toUDefFunction();
    }

    @Override
    public IUdefFunction updateFunctionByRest(User user, IUdefFunction function) {
        UpdateFunction.Arg arg = UpdateFunction.Arg.builder()
                .function(FunctionInfo.of(function))
                .build();
        UpdateFunction.Result updateResult;
        try {
            updateResult = functionRestProxy.update(getHeaders(user), arg);
        } catch (ValidateException e) {
            throw e;
        } catch (Exception e) {
            log.error("functionRestProxy.update exception ", e);
            throw new FunctionException(I18N.text(I18NKey.FUNC_FAIL));
        }
        return updateResult.getFunction().toUDefFunction();
    }

    /**
     * 运行函数
     *
     * @param user
     * @param function
     * @param parameters
     * @param data
     * @param details
     * @return
     */
    @Override
    public RunResult executeUDefFunction(
            User user, IUdefFunction function, Map<String, Object> parameters, IObjectData data, Map<String, List<IObjectData>> details) {

        return executeUDefFunction(user, function, parameters, data, details, null);
    }

    @Override
    public RunResult executeUDefFunction(User user, IUdefFunction function, Map<String, Object> parameters, IObjectData data, Map<String,
            List<IObjectData>> details, Map<String, Object> actionParam) {
        return executeUDefFunction(user, function, parameters, data, details, actionParam, null);
    }

    @Override
    public RunResult executeUDefFunction(User user, IUdefFunction function, Map<String, Object> parameters, IObjectData data, Map<String,
            List<IObjectData>> details, Map<String, Object> actionParam, String actionStage) {
        return executeUDefFunction(user, function, parameters, data, details, null, actionParam, actionStage);
    }

    @Override
    public RunResult executeUDefFunction(User user, IUdefFunction function, Map<String, Object> parameters, IObjectData data,
                                         Map<String, List<IObjectData>> details, Map<String, List<IObjectData>> relatedDataList,
                                         Map<String, Object> actionParam, String actionStage) {
        FuncBizExtendParam.Arg arg = FuncBizExtendParam.Arg.builder().actionStage(actionStage).build();
        return executeUDefFunction(user, function, parameters, data, details, relatedDataList, actionParam, arg);
    }

    @Override
    public RunResult executeUDefFunction(User user, IUdefFunction function, Map<String, Object> parameters, IObjectData data,
                                         Map<String, List<IObjectData>> details, Map<String, List<IObjectData>> relatedDataList, Map<String, Object> actionParam,
                                         FuncBizExtendParam.Arg funcBizExtendParamArg) {
        Map<String, List<Map<String, Object>>> detailMap = null;
        Map<String, List<Map<String, Object>>> relatedMap = null;
        if (CollectionUtils.notEmpty(details)) {
            detailMap = details.entrySet().stream().collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> entry.getValue().stream().map(objectData -> ObjectDataExt.of(objectData).toMap()).collect(Collectors.toList())
            ));
        }
        if (CollectionUtils.notEmpty(relatedDataList)) {
            relatedMap = relatedDataList.values().stream()
                    .flatMap(Collection::stream)
                    .collect(Collectors.groupingBy(IObjectData::getDescribeApiName,
                            Collectors.mapping(objectData -> ObjectDataExt.of(objectData).toMap(), Collectors.toList())));
        }

        FunctionInvoker invoker = FunctionInvoker.builder().inputData(parameters).udefFunction(function).build();
        ExecuteFunction.Arg arg = ExecuteFunction.Arg
                .builder()
                .objectData(dataToMap(data, function.getBindingObjectApiName(), user.getTenantId()))
                .objectDetails(detailMap)
                .objectRelatedData(relatedMap)
                .function(invoker.getFunction())
                .arg(actionParam)
                .actionStage(funcBizExtendParamArg.getActionStage())
                .apiName(funcBizExtendParamArg.getApiName())
                .actionPage(funcBizExtendParamArg.getActionPage())
                .searchQuery(funcBizExtendParamArg.getSearchQuery())
                .build();
        return functionRestProxy.execute(getHeaders(user), arg);
    }

    private Map<String, String> getHeaders(User user) {
        Map<String, String> headers = RestUtils.buildHeaders(user);
        headers.put(X_FS_CLIENT_INFO, getClientInfo());
        RequestContext context = RequestContextManager.getContext();
        if (Objects.isNull(context)) {
            return headers;
        }
        headers.put(FS_DEVICE_TYPE, context.getAttribute(FS_DEVICE_TYPE));
        // 调用命名空间为button的函数支持幂等，解决FlowCompleted接口重试超时问题。
        if (RequestUtil.isFunctionIdempotent() && !Strings.isNullOrEmpty(context.getPostId())) {
            headers.put(InnerHeaders.POST_ID, context.getPostId());
        }
        return headers;
    }

    private String getAppId() {
        return RequestContextManager.getContext() == null ? null : RequestContextManager.getContext().getAppId();
    }

    private Map<String, Object> dataToMap(IObjectData data, String bindingObjectApiName, String tenantId) {
        if (data == null) {
            data = new ObjectData();
            data.setTenantId(tenantId);
            data.setDescribeApiName(bindingObjectApiName);
        }
        return ObjectDataExt.of(data).toMap();
    }

    @Override
    public RunResult executeUDefIdempotent(RequestContext context, String apiName, String bindingObjectAPIName, List<?> parameters, String bindingObjectDataId, List<String> objectIds) {


        AsyncRun.Arg arg = AsyncRun.Arg.builder()
                .apiName(apiName)
                .bindingObjectAPIName(bindingObjectAPIName)
                .bindingObjectDataId(bindingObjectDataId)
                .parameters(parameters)
                .build();
        try {
            return functionRestProxy.run(getHeaders(context.getUser()), arg);
        } catch (Exception e) {
            //幂等失效，超时
            Throwable rootCause = ExceptionUtils.getRootCause(e);
            if (rootCause instanceof SocketTimeoutException || rootCause instanceof FunctionTimeoutException) {
                //返回空，让调用端再次尝试取回结果
                return null;
            }
            throw e;
        }
    }

    @Override
    public RunResult executeFunctionController(RequestContext context, String apiName, List<?> parameters) {
        ControllerFunction.Arg arg = ControllerFunction.Arg.builder()
                .apiName(apiName)
                .parameters(parameters)
                .build();

        try {
            return functionRestProxy.controller(getHeaders(context.getUser()), arg);
        } catch (Exception e) {
            return RunResult.builder().success(false).errorInfo(I18NExt.text(I18NKey.FUNC_FAIL)).build();
        }
    }

    /**
     * 调试函数
     *
     * @param user
     * @param function
     * @param parameters
     * @param dataSource
     * @return
     */
    @Override
    public DebugFunction.Result debugRunUDefFunction(User user, IUdefFunction function, Map<String, Object> parameters, String dataSource, String requestId) {

        FunctionInvoker invoker = FunctionInvoker.builder().inputData(parameters).udefFunction(function).build();
        DebugFunction.Arg arg = DebugFunction.Arg.builder()
                .function(invoker.getFunction())
                .objectAPIName(function.getBindingObjectApiName())
                .objectId(dataSource)
                .requestId(requestId)
                .build();

        try {
            DebugFunction.Result result = functionRestProxy.asyncDebug(getHeaders(user), arg);
            //正在运行的函数返回为空
            if (result.getStatus() == DebugFunction.Result.RUNNING) {
                return null;
            }
            return result;
        } catch (Exception e) {
            Throwable rootCause = ExceptionUtils.getRootCause(e);
            if (rootCause instanceof SocketTimeoutException || rootCause instanceof FunctionTimeoutException) {
                //rest接口超时,返回空，让调用端再次尝试取回结果
                return null;
            }
            throw e;
        }
    }

    @Override
    public BatchDataExecuteFunction.Result batchDataExecuteFunction(User user, IUdefFunction function, List<String> objectIds, Map<String, Object> parameters) {

        FunctionInvoker invoker = FunctionInvoker.builder().inputData(parameters).udefFunction(function).build();
        BatchDataExecuteFunction.Arg arg = BatchDataExecuteFunction.Arg.builder()
                .function(invoker.getFunction())
                .objectAPIName(function.getBindingObjectApiName())
                .objectIds(objectIds)
                .build();

        return functionRestProxy.batchDataExecuteFunction(getHeaders(user), arg);
    }

    @Override
    public batchDataExecuteTask.Result importPreProcessingFunction(FunctionServiceContext functionServiceContext, List<IObjectData> objectDataList) {
        IUdefFunction function = functionServiceContext.getFunction();
        User user = functionServiceContext.getUser();

        List<Map> objectList = objectDataList.stream()
                .map(ObjectDataExt::of)
                .map(dataExt -> dataExt.toMap())
                .collect(Collectors.toList());

        FunctionInvoker invoker = FunctionInvoker.builder().udefFunction(function).build();
        batchDataExecuteTask.Task task = batchDataExecuteTask.Task.of(functionServiceContext.getTaskId(),
                functionServiceContext.getFinalBatch(), functionServiceContext.getUnionImport(), functionServiceContext.getFileCode());
        batchDataExecuteTask.Arg arg = batchDataExecuteTask.Arg.builder()
                .function(invoker.getFunction())
                .objectAPIName(function.getBindingObjectApiName())
                .dataList(objectList)
                .task(task)
                .build();

        return functionRestProxy.batchDataExecuteTask(getHeaders(user), arg);
    }

    @Override
    public IUdefFunction findUDefFunction(User user, String apiName, String bindingObjectAPIName) {
        if (StringUtils.isBlank(bindingObjectAPIName)) {
            //不绑定对象,底层sql要求判空会会按照NONE查询,所以只能传null或NONE,前端传入"",需要兼容
            bindingObjectAPIName = "NONE";
        }
        return udefFunctionService.findFunctionByApiName(user.getTenantId(), apiName, bindingObjectAPIName);
    }

    @Override
    public IUdefFunction findFunctionByVersion(User user, String apiName, String bindingObjectAPIName, Integer version) {
        List<Integer> versions = Lists.newArrayList(version);
        List<IUdefFunction> functionList = udefFunctionService.findVersions(user.getTenantId(), apiName,
                bindingObjectAPIName, versions, null, null, null, 1);
        if (CollectionUtils.empty(functionList)) {
            return null;
        }
        return functionList.get(0);
    }

    @Override
    public List<IUdefFunction> queryFunctionVersions(
            User user, String apiName, String bindingObjectAPIName, List<Integer> versions, Integer updateBy, Long beginTime, Long endTime, Integer limit) {
        return uniqueByVersion(user.getTenantId(), apiName, bindingObjectAPIName, versions, updateBy, beginTime, endTime, limit);
    }

    @Override
    public String diffFunction(User user, String apiName, String bindingObjectAPIName, List<Integer> versions) {
        String html = "";
        List<IUdefFunction> functionList = uniqueByVersion(user.getTenantId(), apiName, bindingObjectAPIName, versions, null, null, null, 100);
        if (CollectionUtils.notEmpty(functionList) && functionList.size() == 2) {
            DiffMatchPatch diffMatchPatch = new DiffMatchPatch();
            LinkedList<DiffMatchPatch.Diff> list = diffMatchPatch.diffMain(functionList.get(1).getBody(), functionList.get(0).getBody());
            diffMatchPatch.diffCleanupSemantic(list);
            html = "<pre>" + diffMatchPatch.diffPrettyHtml(list).replace("&para;", "") + "</pre>";
        }
        return html;
    }

    //version 可能重复，加上根据version去重
    private List<IUdefFunction> uniqueByVersion(
            String tenantId, String apiName, String bindingObjectAPIName, List<Integer> versions, Integer updateBy, Long beginTime, Long endTime, Integer limit) {
        String updateUserId = updateBy == null ? null : String.valueOf(updateBy);
        if (limit == null || limit > 100) {
            limit = 100;
        }
        List<IUdefFunction> functionList = udefFunctionService.findVersions(tenantId, apiName, bindingObjectAPIName, versions, updateUserId, beginTime, endTime, limit);
        Set<Integer> versionSet = Sets.newHashSet();
        List<IUdefFunction> uniqueVersions = Lists.newArrayList();
        functionList.stream().forEach(x -> {
            if (!versionSet.contains(x.getVersion())) {
                versionSet.add(x.getVersion());
                uniqueVersions.add(x);
            }
        });
        return uniqueVersions;
    }

    /**
     * 编译检查
     *
     * @param user
     * @param function
     */
    @Override
    public void compile(User user, IUdefFunction function) {
        validateFunctionBody(function.getApiName(), function.getBody());
        Function func = Function.builder()
                .apiName(function.getApiName())
                .functionName(function.getFunctionName())
                .parameters(function.getParameters().stream().map(Function.Parameter::of).collect(Collectors.toList()))
                .returnType(function.getReturnType())
                .body(function.getBody())
                .type(function.getType())
                .lang(function.getLang())
                .tenantId(user.getTenantId()).build();


        Compile.Result result = functionRestProxy.compile(user.getTenantId(), user.getUserId(), new Compile.Arg(func));
        if (!result.isSuccess()) {
            throw new MetaDataBusinessException(result.getErrorMessage());
        }
    }

    @Override
    public QueryResult queryUDefFunction(User user, Map<String, Object> map) {
        return udefFunctionService.findFunctionByExample(user.getTenantId(), map);
    }

    public List<ReferenceData> findReferenceByTarget(String tenantId, String functionApiName) {
        HashMap<Object, Object> pathMap = Maps.newHashMap();
        pathMap.put("tenantId", tenantId);
        FindReferenceByTarget.Result result = referenceServiceProxy.findByTarget(pathMap, TargetTypes.FUNCTION, functionApiName, "false", "5");
        return result.getValues();
    }

    @Override
    public Boolean setIsActive(User user, String bindingObjectAPIName, String apiName, Boolean isActive) {
        IActionContext actionContext = new ActionContext();
        actionContext.setUserId(user.getUserId());
        if (!isActive) {
            List<ReferenceData> data = findReferenceByTarget(user.getTenantId(), apiName);
            if (!org.springframework.util.CollectionUtils.isEmpty(data)) {
                String references = Joiner.on(",").join(data.stream().map(t -> t.getSourceLabel()).collect(Collectors.toList()));
                throw new ValidateException(references + "use the" + apiName);
            }
        }
        boolean result = udefFunctionService.changeUdefFunctionIsActive(apiName, bindingObjectAPIName, user.getTenantId(), isActive, actionContext);
        if (result) {
            if (isActive) {
                logService.logUdefFunction(user, EventType.ENABLE, ActionType.ENABLE_FUNCTION, bindingObjectAPIName, apiName, null);
            } else {
                logService.logUdefFunction(user, EventType.DISABLE, ActionType.DISABLE_FUNCTION, bindingObjectAPIName, apiName, null);
            }
        }
        return result;
    }

    @Override
    public Boolean funcIsExist(User user, List<String> nameSpace, List<String> returnType, String bindingObjectAPIName) {
        return udefFunctionService.checkFunctionExistByDescribeApiName(bindingObjectAPIName,
                nameSpace, returnType, user.getTenantId());
    }

    @Override
    public Boolean deleteUDefFunction(User user, String bindingObjectAPIName, String apiName) {
        IActionContext actionContext = new ActionContext();
        actionContext.setUserId(user.getUserId());
        IUdefFunction function = udefFunctionService.findFunctionByApiName(user.getTenantId(), apiName, bindingObjectAPIName);
        boolean result = udefFunctionService.deleteFunction(user.getTenantId(), apiName, bindingObjectAPIName, actionContext);
        if (result) {
            logService.logUdefFunction(user, EventType.DELETE, ActionType.DELETE_FUNCTION, bindingObjectAPIName, apiName, function);
        }
        return result;
    }

    @Override
    public void checkFunctionCountLimit(User user) {
        //少一次RPC调用
        TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo.builder()
                .licenseService(licenseService)
                .user(user)
                .build()
                .init(Sets.newHashSet(ModulePara.ModuleBiz.CUSTOM_FUNCTION.getBizCode()));
//        if (!tenantLicenseInfo.isAllowFunction()) {
//            throw new ValidateException(I18N.text(I18NKey.FUNCTION_NOT_ALLOW));
//        }

        Map<String, Object> map = Maps.newHashMap();
        map.put("limit", 1);
        map.put("offset", 0);
        QueryResult queryResult = udefFunctionService.findFunctionByExample(user.getTenantId(), map);

        tenantLicenseInfo.checkFunctionCountLimit(queryResult.getTotalNumber());
    }

    @Override
    public void setFunctionUsedAction(User user, String apiName, String describeApiName, String status, Map<String, Object> usedInfo) {
        IActionContext actionContext = new ActionContext();
        actionContext.setUserId(user.getUserId());
        udefFunctionService.updateUdefFunctionStatus(user.getTenantId(), apiName, describeApiName, status, usedInfo, actionContext);
    }

    @Override
    public List<IUdefFunction> findFunctionByExample(User user, Map<String, Object> map) {
        QueryResult<IUdefFunction> result = udefFunctionService.findFunctionByExample(user.getTenantId(), map);
        if (result != null) {
            return result.getData();
        }
        return Lists.newArrayList();
    }

    @Override
    public List<IUdefFunction> findFunctionByApiNames(User user, List<String> apiNames, String bindingObjectAPIName) {
        List<IUdefFunction> result = udefFunctionService.findFunctionByApiNames(
                user.getTenantId(), apiNames, bindingObjectAPIName);
        if (CollectionUtils.empty(result)) {
            return Lists.newArrayList();
        }
        return result;
    }

    @Override
    public Map<String, IncrementNumberBatchExecute.IncrementNumber> incrementNumberBatchExecute(User user, String describeApiName, String functionApiName,
                                                                                                List<IncrementNumberBatchExecute.ObjectInfo> objectInfos) {
        if (CollectionUtils.empty(objectInfos)) {
            return Collections.emptyMap();
        }
        List<List<IncrementNumberBatchExecute.ObjectInfo>> partition = Lists.partition(objectInfos, 200);
        Map<String, IncrementNumberBatchExecute.IncrementNumber> resultMap = partition.stream()
                .map(it -> incrementNumberBatchExecuteSlicing(user, describeApiName, functionApiName, it))
                .reduce(Maps.newHashMap(), (a, b) -> {
                    a.putAll(b);
                    return a;
                });
        return resultMap;
    }

    @Override
    public DeveloperVerificationResult checkDeveloperVerification(User user, String sessionId) {
        DeveloperVerificationResult verification = new DeveloperVerificationResult();
        verification.setNeedVerification(true);
        verification.setPhone("");
        try {
            QueryFunctionVerificationLog.Result result = logService.queryFunctionValidationLog(user, sessionId);
            if (!result.isSuccess()) {
                log.error("queryFunctionValidationLog error , tenantId :{}  sessionId :{}  error :{}", user.getTenantId(), sessionId, result.getErrorMessage());
                return verification;
            }

            if (ObjectUtils.isEmpty(result.getLogs())) {
                return verification;
            }

            FunctionValidationLog functionValidationLog = result.getLogs().get(0);
            verification.setNeedVerification(!securityChecker.isExpire(functionValidationLog.getStartTime()));
            verification.setPhone(functionValidationLog.getPhone());
            return verification;
        } catch (Exception e) {
            log.error("checkDeveloperVerification tenantId :{} sessionId :{}", user.getTenantId(), sessionId, e);
        }
        return verification;
    }

    @Override
    public void enableDeveloperVerification(User user, String tenantId) {
        //目前开启企业验证的接口，仅提供给fs调用
        User openTenantUser = User.systemUser(tenantId);
        configService.upsertTenantConfig(openTenantUser, FUNCTION_DEVELOP_VERIFICATION_KEY, ENABLE_DEVELOP_VERIFICATION_VALUE, ConfigValueType.STRING);
    }

    @Override
    public void disableDeveloperVerification(User user, String closeTenantId) {
        User closeTenantUser = User.systemUser(closeTenantId);
        configService.upsertTenantConfig(closeTenantUser, FUNCTION_DEVELOP_VERIFICATION_KEY, DISABLE_DEVELOP_VERIFICATION_VALUE, ConfigValueType.STRING);
    }

    @Override
    public String checkFunctionDevelopVerificationCode(User user, String areaCode, String phone, String smsCode) {
        String result = phoneNumberService.verifySmsCode(user, areaCode, phone, smsCode);
        if (!Objects.equals(result, "SUCCESS")) {
            return result;
        }

        RequestContext context = RequestContextManager.getContext();
        if (Objects.isNull(context) || StringUtils.isEmpty(context.getSessionId())) {
            log.warn("request context or session id isEmpty ");
            return result;
        }

        FunctionValidationLog validationLog = FunctionValidationLog.builder().sessionId(context.getSessionId())
                .phone(phone)
                .startTime(System.currentTimeMillis())
                .tenantId(user.getTenantId())
                .build();

        try {
            InsertFunctionVerificationLog.Result insertResult = logService.insertFunctionValidationLog(user, Arrays.asList(validationLog));
            if (!insertResult.isSuccess()) {
                log.error("insertFunctionValidationLog error :{} ", insertResult.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("insertFunctionValidationLog error tenantId :{} , phone :{}", user.getTenantId(), phone, e);
        }

        return result;
    }

    @Override
    public boolean isEnableFunctionDevelopVerification(User user) {
        try {
            String tenantConfig = configService.findTenantConfig(user, FUNCTION_DEVELOP_VERIFICATION_KEY);
            return Objects.equals(tenantConfig, ENABLE_DEVELOP_VERIFICATION_VALUE);
        } catch (Exception e) {
            log.error("find function.develop.verification config error , tenantId :{}", user.getTenantId(), e);
        }

        //这个API 目前用于前端是否展示查看按钮，如果查询出错，保守的展示查看按钮
        return true;
    }

    @Override
    public void saveRelation(User user, List<ReferenceData> list) {
        if (CollectionUtils.notEmpty(list)) {
            HashMap<Object, Object> pathMap = Maps.newHashMap();
            pathMap.put("tenantId", user.getTenantId());
            DeleteAndCreateReference.Arg arg = DeleteAndCreateReference.Arg.builder().items(list).build();
            Map<String, String> queryParams = Maps.newHashMap();
            queryParams.put("deleteByTargetValue", Boolean.TRUE.toString());
            referenceServiceProxy.deleteAndCreate(pathMap, queryParams, arg);
        }
    }

    @Override
    public void deleteAndCreateRelation(User user, List<ReferenceData> list) {
        if (CollectionUtils.notEmpty(list)) {
            HashMap<Object, Object> pathMap = Maps.newHashMap();
            pathMap.put("tenantId", user.getTenantId());
            DeleteAndCreateReference.Arg arg = DeleteAndCreateReference.Arg.builder().items(list).build();
            referenceServiceProxy.deleteAndCreate(pathMap, arg);
        }
    }

    @Override
    public void deleteRelation(User user, String type, String value, String funcApiName) {
        referenceServiceProxy.deleteReference(user.getTenantId(), type, value, funcApiName, TargetTypes.FUNCTION);
    }

    @Override
    public void batchDeleteRelation(User user, List<ReferenceData> list) {
        if (CollectionUtils.notEmpty(list)) {
            List<BatchDeleteReference.ReferenceData> batchDeleteReferenceList = Lists.newArrayList();
            list.stream().forEach(referenceData -> {
                BatchDeleteReference.ReferenceData batchDeleteReference = BatchDeleteReference.ReferenceData.builder()
                        .sourceType(referenceData.getSourceType())
                        .sourceValue(referenceData.getSourceValue())
                        .targetValue(referenceData.getTargetValue())
                        .build();
                batchDeleteReferenceList.add(batchDeleteReference);
            });
            if (CollectionUtils.notEmpty(batchDeleteReferenceList)) {
                BatchDeleteReference.Arg arg = BatchDeleteReference.Arg.builder().items(batchDeleteReferenceList).build();
                referenceServiceProxy.batchDeleteReference(user.getTenantId(), arg);
            }
        }
    }

    @Override
    public List<ReferenceData> findRelationBySource(User user, String sourceType, String sourceValue) {
        FindReferenceByTarget.Result result = referenceServiceProxy.findRelationsBySource(user.getTenantId(), sourceType, sourceValue);
        return CollectionUtils.nullToEmpty(result.getValues());
    }

    @Override
    public List<FunctionVSCodeExt> downloadFunctions(User user, String apiName, String bindingObjectApiName,
                                                     String type, int pageNumber, int pageSize) {
        // apiName为空表达拉所有函数
        List<IUdefFunction> functions = Lists.newArrayList();
        if (StringUtils.isAllBlank(apiName)) {
            Map map = Maps.newHashMap();
            map.put("limit", pageSize);
            map.put("offset", (pageNumber - 1) * pageSize);
            map.put("type", type);
            QueryResult result = udefFunctionService.findFunctionByExample(user.getTenantId(), map);
            functions = result.getData();
        } else {
            IUdefFunction function = udefFunctionService.findFunctionByApiName(user.getTenantId(), apiName, bindingObjectApiName);
            if (Objects.isNull(function)) {
                throw new ValidateException(I18N.text(I18NKey.NOT_FIND_FUNCTION, apiName));
            }
            functions.add(function);
        }
        List<FunctionVSCodeExt> vsCodeExts = functions.stream().map(FunctionVSCodeExt::of).collect(Collectors.toList());
        return vsCodeExts;
    }

    @Override
    public long uploadFunction(User user, String apiName, String name, String content, String metaXml, String type, long updateTime,
                               String nameSpace, String returnType, String bindingObject, String describe, String commit) {

        IUdefFunction function = buildUdefFunction(user, metaXml, apiName, name,
                content, type, updateTime, nameSpace, returnType, bindingObject, describe, commit);
        //校验body size
        UdefFunctionExt.of(function).validateBodySize();
        //编译检查
        try {
            compile(user, function);
        } catch (FunctionCompileException e) {
            throw new MetaDataBusinessException(e.getMessage());
        }
        setFunctionDevelopInfo(user, function);
        try {
            if (updateTime == 0) { // updateTime == 0 为新建函数
                IUdefFunction udefFunction = udefFunctionService.create(function);
                logService.logUdefFunction(user, EventType.ADD, ActionType.CREATE_FUNCTION,
                        function.getBindingObjectApiName(), function.getApiName(), udefFunction);
                return udefFunction.getCreateTime();
            } else {
                IUdefFunction udefFunction = udefFunctionService.update(function);
                logService.logUdefFunction(user, EventType.MODIFY, ActionType.UPDATE_FUNCTION,
                        function.getBindingObjectApiName(), function.getApiName(), udefFunction);
                return udefFunction.getCreateTime();
            }
        } catch (MetadataServiceException e) {
            log.warn("uploadFunction error,user:{},function:{}", user, function, e);
            throw new MetaDataBusinessException(e);
        }
    }


    @Override
    public String getDetailForVSCode(User user, String apiName, String bindingObjectApiName) {
        IUdefFunction function = udefFunctionService.findFunctionByApiName(user.getTenantId(), apiName, bindingObjectApiName);
        if (Objects.isNull(function)) {
            throw new ValidateException(I18N.text(I18NKey.NOT_FIND_FUNCTION, apiName));
        }
        StringBuilder sBuilder = new StringBuilder();
        // TODO: 2024/9/20 I18n
        sBuilder.append("函数名称：").append(function.getFunctionName()).append("\n") // ignoreI18n
                .append("函数APIName：").append(function.getApiName()).append("\n") // ignoreI18n
                .append("命名空间：").append(function.getNameSpace()).append("\n") // ignoreI18n
                .append("返回类型：").append(function.getReturnType()).append("\n") // ignoreI18n
                .append("函数描述：").append(function.getRemark()).append("\n"); // ignoreI18n
        return sBuilder.toString();
    }

    @Override
    public Optional<IUdefFunction> findFunctionByFuncRelationKey(User user, String funcRelationKeyType,
                                                                 String funcRelationKeyValue, String bindingObjectAPIName) {
        FindRelationBySource.Arg arg = FindRelationBySource.Arg.of(funcRelationKeyType, funcRelationKeyValue);
        FindRelationBySource.Result result = functionRestProxy.findRelationBySource(user.getTenantId(), user.getUserId(), getClientInfo(), arg);
        return Optional.ofNullable(result)
                .map(FindRelationBySource.Result::getItems)
                .filter(CollectionUtils::notEmpty)
                .map(it -> it.get(0))
                .map(it -> findUDefFunction(user, it.getFuncApiName(), bindingObjectAPIName));
    }

    @Override
    public Analyze.Result analyze(User user, IUdefFunction function) {
        Function func = Function.builder()
                .apiName(function.getApiName())
                .functionName(function.getFunctionName())
                .parameters(function.getParameters().stream().map(Function.Parameter::of).collect(Collectors.toList()))
                .returnType(function.getReturnType())
                .body(function.getBody())
                .tenantId(user.getTenantId()).build();

        Analyze.Result result = functionRestProxy.analyze(user.getTenantId(), user.getUserId(), new Analyze.Arg(func));
        return result;
    }

    private IUdefFunction buildUdefFunction(User user, String metaXml, String apiName, String name, String content,
                                            String type, long updateTime, String nameSpace, String returnType,
                                            String bindingObject, String describe, String commit) {

        IUdefFunction function = VSCodeExtUtils.parseXML(user, metaXml);
        function.setTenantId(user.getTenantId());
        function.setApiName(apiName);
        function.setBody(content);
        function.setFunctionName(name);
        function.setNameSpace(nameSpace);
        function.setReturnType(returnType);
        function.setBindingObjectApiName(bindingObject);
        function.setType(type);
        if (updateTime == 0) { // VSCode插件新建
            function.setRemark(describe); //VSCode 插件只有新建的时候会有describe
            return function;
        }
        IUdefFunction oldFunction = udefFunctionService.findFunctionByApiName(user.getTenantId(), apiName, bindingObject);
        if (Objects.isNull(oldFunction)) {
            throw new ValidateException(I18N.text(I18NKey.NOT_FIND_FUNCTION, name));
        }
        if (updateTime < oldFunction.getCreateTime()) {
            throw new ValidateException(I18NExt.text(I18NKey.CURRENT_CODE_HAS_NEWER_VERSION_ONLINE_PLEASE_PULL_BEFORE_UPLOADING));
        }
        function.setRemark(oldFunction.getRemark());
        function.setVersion(oldFunction.getVersion());
        function.setIsActive(oldFunction.isActive());
        function.setStatus(oldFunction.getStatus());
        function.setUsedInfo(oldFunction.getUsedInfo());
        function.setCommitLog(commit);
        return function;
    }

    private Map<String, IncrementNumberBatchExecute.IncrementNumber> incrementNumberBatchExecuteSlicing(User user, String describeApiName, String functionApiName,
                                                                                                        List<IncrementNumberBatchExecute.ObjectInfo> objectInfos) {
        IncrementNumberBatchExecute.Arg arg = IncrementNumberBatchExecute.Arg.builder()
                .bindingObjectAPIName(describeApiName)
                .functionAPIName(functionApiName)
                .objectInfos(objectInfos)
                .build();
        IncrementNumberBatchExecute.Result result = functionRestProxy.incrementNumberBatchExecute(getHeaders(user), arg);
        if (!result.isSuccess()) {
            log.warn("incrementNumberBatchExecute fail, tenantId:{}, objectApiName:{}, funcApiName:{}, message:{}",
                    user.getTenantId(), describeApiName, functionApiName, result.getErrorMessage());
            throw new MetaDataBusinessException(I18N.text(I18NKey.FUNC_INCREMENT_NUMBER_FAIL));
        }
        return result.getResult();
    }


    private String getClientInfo() {
        String clientInfo = RequestUtil.getClientInfo();
        return StringUtils.isEmpty(clientInfo) ? "" : clientInfo;
    }

    private void setFunctionDevelopInfo(User user, IUdefFunction function) {
        try {
            String sessionId = getSessionId();
            if (StringUtils.isEmpty(sessionId)) {
                log.warn("Session id is empty , tenantId :{} , function :{}", user.getTenantId(), function.getApiName());
                return;
            }

            String tenantConfig = configService.findTenantConfig(user, FUNCTION_DEVELOP_VERIFICATION_KEY);
            if (!Objects.equals(tenantConfig, ENABLE_DEVELOP_VERIFICATION_VALUE)) {
                return;
            }

            QueryFunctionVerificationLog.Result result = logService.queryFunctionValidationLog(user, sessionId);
            if (!result.isSuccess()) {
                log.error("queryFunctionValidationLog error , tenantId :{}  sessionId :{}  error :{}", user.getTenantId(), sessionId, result.getErrorMessage());
                return;
            }
            if (ObjectUtils.isEmpty(result.getLogs())) {
                log.warn("queryFunctionValidationLog result is Empty tenantId :{}  sessionId :{}", user.getTenantId(), sessionId);
                return;
            }

            FunctionValidationLog functionValidationLog = result.getLogs().get(0);
            String phone = functionValidationLog.getPhone();
            function.setDeveloperInfo(phone);
        } catch (Exception e) {
            log.error("setFunctionDevelopInfo error , tenantId :{} ", user.getTenantId(), e);
        }
    }


    private String getSessionId() {
        RequestContext context = RequestContextManager.getContext();
        if (ObjectUtils.isEmpty(context)) {
            return "";
        }

        return context.getSessionId();
    }

    private void validateFunctionBody(String functionAPIName, String body) {
        if (StringUtils.isBlank(body)) {
            return;
        }
        boolean allow = securityChecker.validateHttpURL(functionAPIName, body);
        if (!allow) {
            throw new ValidateException(I18N.text(I18NKey.VALIDATE_FUNCTION));
        }
    }

    @Override
    public void clearUdefFunctionUsedInfo(User user, IUdefFunction function, String describeApiName, String usedApiName, String status) {
        if (Objects.nonNull(function)) {
            Map usedInfo = function.getUsedInfo();
            IActionContext actionContext = new ActionContext();
            actionContext.setUserId(user.getUserId());
            if (CollectionUtils.notEmpty(usedInfo) && usedInfo.get("api_name") != null
                    && usedApiName.equals(usedInfo.get("api_name").toString())) {
                udefFunctionService.updateUdefFunctionStatus(user.getTenantId(), function.getApiName(),
                        describeApiName, status, Maps.newHashMap(), actionContext);
            }
        }
    }

    @Override
    public void processFunctionReference(IFieldDescribe fieldDescribe, String sourceType, String functionApiName, String bindingObjectApiName, boolean isClearFuncReference) {
        RequestContext context = RequestContextManager.getContext();
        User user = context.getUser();
        if (isClearFuncReference) {
            deleteRelation(user, sourceType, fieldDescribe.getApiName(), functionApiName);
        } else {
            IUdefFunction function = udefFunctionService.findFunctionByApiName(context.getTenantId(), functionApiName, bindingObjectApiName);
            if (Objects.isNull(function)) {
                throw new ValidateException(I18N.text(I18NKey.NOT_FIND_FUNCTION, functionApiName));
            }
            ReferenceData referenceData = buildReferenceData(fieldDescribe, function, sourceType);
            saveRelation(user, Lists.newArrayList(referenceData));
        }
        logService.logUdefFunction(user, EventType.MODIFY, ActionType.UPDATE_FUNCTION_REFERENCE, fieldDescribe.getDescribeApiName(), functionApiName, null);
    }

    private ReferenceData buildReferenceData(IFieldDescribe fieldDescribe, IUdefFunction function, String sourceType) {
        return ReferenceData.builder()
                .sourceType(sourceType)
                .sourceLabel(fieldDescribe.getLabel())
                .sourceValue(fieldDescribe.getApiName())
                .targetType(TargetTypes.FUNCTION)
                .targetValue(function.getApiName())
                .build();
    }

    @Override
    public Map<String, Object> handleFiltersByValueType(User user, String functionBindingObjectApiName, SearchTemplateQuery query, Supplier<Tuple<IObjectData, Map<String, List<IObjectData>>>> supplier) {
        return handleFiltersByValueType(user, functionBindingObjectApiName, query, supplier, FuncBizExtendParam.Arg.builder().build());
    }

    @Override
    public Map<String, Object> handleFiltersByValueType(User user, String functionBindingObjectApiName, SearchTemplateQuery query, Supplier<Tuple<IObjectData, Map<String, List<IObjectData>>>> supplier, FuncBizExtendParam.Arg funcBizExtendParam) {
        Tuple<Wheres, Integer> functionFilterInfo = FunctionQueryTemplateUtils.getFunctionFilter(query.getWheres());
        if (org.springframework.util.ObjectUtils.isEmpty(functionFilterInfo)) {
            return Maps.newHashMap();
        }

        Tuple<String, String> objectAndFunctionAPIName = getObjectAndFunctionAPIName(functionBindingObjectApiName, functionFilterInfo.getKey().getFilters().get(functionFilterInfo.getValue()));
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(objectAndFunctionAPIName)) {
            return Maps.newHashMap();
        }

        IUdefFunction function = findUDefFunction(user, objectAndFunctionAPIName.getValue(), objectAndFunctionAPIName.getKey());
        if (org.springframework.util.ObjectUtils.isEmpty(function)) {
            throw new ValidateException(I18N.text(I18NKey.NOT_FIND_FUNCTION, objectAndFunctionAPIName.getValue()));
        }
        Tuple<IObjectData, Map<String, List<IObjectData>>> result = supplier.get();
        RunResult runResult = executeUDefFunction(user, function, null, result.getKey(), result.getValue(), null, null, funcBizExtendParam);
        if (!runResult.isSuccess()) {
            throw new FunctionException(runResult.getErrorInfo());
        }
        return FunctionQueryTemplateUtils.handleFunctionResult(user.getTenantId(), functionFilterInfo.getKey(), functionFilterInfo.getValue(), runResult);
    }

    @Override
    public RunResult findAndExecuteFunction(User user, Supplier<Tuple<IObjectData, Map<String, List<IObjectData>>>> supplier, String bindingObjectAPIName, String functionAPIName) {
        IUdefFunction function = findUDefFunction(user, functionAPIName, bindingObjectAPIName);
        if (org.springframework.util.ObjectUtils.isEmpty(function)) {
            throw new ValidateException(I18N.text(I18NKey.NOT_FIND_FUNCTION, functionAPIName));
        }
        IObjectData objectData = supplier.get().getKey();
        Map<String, List<IObjectData>> details = supplier.get().getValue();
        RunResult runResult = executeUDefFunction(user, function, null, objectData, details);
        if (!runResult.isSuccess()) {
            throw new FunctionException(runResult.getErrorInfo());
        }
        return runResult;
    }

    private Tuple<String, String> getObjectAndFunctionAPIName(String functionBindingObjectApiName, IFilter filter) {
        String functionAPIName = filter.getFieldValues().get(0);
        if (org.apache.commons.lang.StringUtils.isEmpty(functionBindingObjectApiName) || org.apache.commons.lang.StringUtils.isEmpty(functionAPIName)) {
            return null;
        }

        return Tuple.of(functionBindingObjectApiName, functionAPIName);
    }
}
