package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.common.util.Pair;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.layout.EditLayout;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.layout.rule.FieldBranchImpl;
import com.facishare.paas.appframework.metadata.layout.rule.IFieldBranch;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.LayoutRuleInfo;
import com.facishare.paas.metadata.impl.search.Operator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Delegate;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public class LayoutRuleExt {
    private static final Set<String> UN_SUPPORT_LAYOUT_RULE_FIELDS = ImmutableSet.of(IObjectData.CREATED_BY,
            IObjectData.OWNER, IObjectData.DATA_OWN_DEPARTMENT);
    @Getter
    @Delegate
    private LayoutRuleInfo ruleInfo;

    private LayoutRuleExt(LayoutRuleInfo rule) {
        this.ruleInfo = rule;
    }

    public PageBranch getPageBranch() {
        return JSON.parseObject(JSON.toJSONString(ruleInfo.getPageBranches()), PageBranch.class);
    }

    public static LayoutRuleExt of(LayoutRuleInfo rule) {
        return new LayoutRuleExt(rule);
    }

    public static LayoutRuleExt of(Map map) {
        return of(new LayoutRuleInfo(map));
    }

    public String getDefaultEditPageApiName() {
        return LayoutTypes.EDIT + "_" + getApiName();
    }

    public String getDefaultEditPageLabel() {
        return getLabel() + I18NExt.getOrDefault(I18NKey.EDIT_PAGE_LABEL_SUFFIX, EditLayout.EDIT_PAGE_LABEL);
    }

    public void fillSystemInfo(User user) {
        ruleInfo.setCreatedBy(user.getUserId());
        ruleInfo.setLastModifiedBy(user.getUserId());
    }

    public void enableRule() {
        ruleInfo.setStatus(0);
    }

    public void diableRule() {
        ruleInfo.setStatus(1);
    }

    public LayoutRuleInfo getRule() {
        return ruleInfo;
    }

    public boolean isPageTypeRule() {
        return "page".equals(getRule().getType());
    }

    public boolean isEnabled() {
        return Objects.equals(ruleInfo.getStatus(), 0);
    }

    public static boolean existCycle(List<LayoutRuleInfo> ruleInfoList) {
        List<FieldBranch> allBranches = Lists.newArrayList();

        ruleInfoList.forEach(rule -> {
            if ("page".equals(rule.getType())) {
                return;
            }
            List mainFieldBranches = rule.getMainFieldBranches();
            List<FieldBranch> mainBranches = JSON.parseArray(JSON.toJSONString(mainFieldBranches), FieldBranch.class);
            allBranches.addAll(CollectionUtils.nullToEmpty(mainBranches));
        });


        List<Pair<String, String>> list = Lists.newArrayList();
        allBranches.forEach(a -> {
            String fieldName = a.getMainFieldFilter().getFieldName();
            List<Branch> branches = a.getBranches();
            branches.forEach(b -> {
                Set<String> left = Sets.newHashSet();
                left.add(fieldName);
                List<Filter> conditions = b.getConditions();
                conditions.forEach(c -> left.add(c.getFieldName()));

                List<ShowFieldResult> showFieldList = b.getResult().getShowFieldList();
                showFieldList.forEach(e -> left.forEach(l -> list.add(Pair.of(l, e.getFieldApiName()))));
            });
        });

        if (CollectionUtils.empty(list)) {
            return false;
        }

        return checkExistCycle(Sets.newHashSet(list.get(0).getKey()), list, new Stack<>());
    }

    private static boolean checkExistCycle(Set<String> leftList, List<Pair<String, String>> list, Stack<String> existed) {
        for (String left : leftList) {
            if (existed.contains(left)) {
                return true;
            }

            existed.push(left);
            Set<String> rightList = getAllRight(left, list);
            if (CollectionUtils.empty(rightList)) {
                existed.pop();
            } else {
                return checkExistCycle(rightList, list, existed);
            }
        }
        return false;
    }

    private static Set<String> getAllRight(String left, List<Pair<String, String>> list) {
        Set<String> result = Sets.newHashSet();
        list.forEach(a -> {
            if (Objects.equals(a.getKey(), left)) {
                result.add(a.getValue());
            }
        });
        return result;
    }

    public List<FieldBranch> getMainBranches() {
        return JSON.parseArray(JSON.toJSONString(ruleInfo.getMainFieldBranches()), FieldBranch.class);
    }

    public void resetMainBranches(List<FieldBranch> mainBranches) {
        List mainFieldBranches = JSONObject.parseObject(JSON.toJSONString(mainBranches), List.class);
        ruleInfo.setMainFieldBranches(mainFieldBranches);
    }

    public List<IFieldBranch> getFieldBranches() {
        List<Map> fieldBranches = (List<Map>) ruleInfo.getMainFieldBranches();
        if (CollectionUtils.empty(fieldBranches)) {
            return Collections.emptyList();
        }
        return fieldBranches.stream()
                .map(FieldBranchImpl::new)
                .collect(Collectors.toList());
    }

    public void validatePageTypeRuleByLayout(List<LayoutRuleInfo> layoutRuleList) {
        if (!"page".equals(getRule().getType())) {
            return;
        }
        boolean anyMatch = layoutRuleList.stream().anyMatch(x -> StringUtils.equals(getRule().getPageTriggerMode(), x.getPageTriggerMode())
                && StringUtils.equals(getRule().getLayoutApiName(), x.getLayoutApiName()));
        if (anyMatch) {
            throw new ValidateException(I18N.text(I18NKey.LAYOUT_RULE_UNIQUE_VALIDATE));
        }
    }

    public boolean isFieldInRule(String fieldName) {
        if (fieldName.equals(ruleInfo.getMainField())) {
            return true;
        }
        List<FieldBranch> branches = getMainBranches();
        return CollectionUtils.nullToEmpty(branches).stream().anyMatch(x -> isFieldInMainBranch(fieldName, x));
    }

    private boolean isFieldInMainBranch(String fieldName, FieldBranch branch) {
        if (fieldName.equals(branch.getMainFieldFilter().getFieldName())) {
            return true;
        }
        return CollectionUtils.nullToEmpty(branch.getBranches()).stream().anyMatch(x -> isFieldInBranch(fieldName, x));
    }

    private boolean isFieldInBranch(String fieldName, Branch branch) {
        if (CollectionUtils.nullToEmpty(branch.getResult().getRequiredFieldList()).stream()
                .anyMatch(x -> fieldName.equals(x.getFieldApiName()))) {
            return true;
        }
        if (CollectionUtils.nullToEmpty(branch.getResult().getShowFieldList()).stream()
                .anyMatch(x -> fieldName.equals(x.getFieldApiName()))) {
            return true;
        }
        if (CollectionUtils.nullToEmpty(branch.getResult().getReadonlyFieldList()).stream()
                .anyMatch(x -> fieldName.equals(x.getFieldApiName()))) {
            return true;
        }
        return CollectionUtils.nullToEmpty(branch.getConditions()).stream()
                .anyMatch(x -> fieldName.equals(x.getFieldName()));
    }

    @Data
    public static class FieldBranch {
        @JsonProperty("main_field_filter")
        @JSONField(name = "main_field_filter")
        private Filter mainFieldFilter;
        @JsonProperty("branches")
        @JSONField(name = "branches")
        private List<Branch> branches;
    }

    @Data
    public static class PageBranch {
        @JsonProperty("type")
        @JSONField(name = "type")
        private String type;
        @JsonProperty("hide_field")
        @JSONField(name = "hide_field")
        private List<String> hideField;
        @JsonProperty("readonly_field")
        @JSONField(name = "readonly_field")
        private List<String> readonlyField;
    }

    @Data
    public static class Branch {
        @JsonProperty("conditions")
        @JSONField(name = "conditions")
        private List<Filter> conditions;
        @JsonProperty("result")
        @JSONField(name = "result")
        private RuleResult result;
    }

    @Data
    public static class RuleResult {
        @JsonProperty("show_field")
        @JSONField(name = "show_field")
        private List<ShowFieldResult> showFieldList;
        @JsonProperty("required_field")
        @JSONField(name = "required_field")
        private List<RequiredFieldResult> requiredFieldList;
        @JsonProperty("readonly_field")
        @JSONField(name = "readonly_field")
        private List<ReadonlyFieldResult> readonlyFieldList;
    }

    @Data
    public static class ShowFieldResult {
        @JsonProperty("field_api_name")
        @JSONField(name = "field_api_name")
        private String fieldApiName;
    }

    @Data
    public static class RequiredFieldResult {
        @JsonProperty("field_api_name")
        @JSONField(name = "field_api_name")
        private String fieldApiName;
    }

    @Data
    public static class ReadonlyFieldResult {
        @JsonProperty("field_api_name")
        @JSONField(name = "field_api_name")
        private String fieldApiName;
    }

    @Data
    public static class Filter {
        @JsonProperty("field_name")
        @JSONField(name = "field_name")
        private String fieldName;
        @JsonProperty("operator")
        @JSONField(name = "operator")
        private String operator;
        @JsonProperty("field_values")
        @JSONField(name = "field_values")
        private List<String> fieldValues;
        @JsonProperty("value_type")
        @JSONField(name = "value_type")
        private int valueType;

        public static Filter fromIFilter(IFilter filter) {
            Filter result = new Filter();
            result.setFieldName(filter.getFieldName());
            result.setOperator(filter.getOperator().toString());
            result.setValueType(filter.getValueType());
            result.setFieldValues(filter.getFieldValues());
            return result;
        }

        public IFilter toIFilter() {
            FilterExt filterExt = FilterExt.of(Operator.valueOf(operator), fieldName, fieldValues);
            filterExt.setValueType(valueType);
            return filterExt.getFilter();
        }
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    public static class FieldConfig {
        private Tuple<String, Object> tuple;
        private Number width;

        public static FieldConfig fromMap(Map<String, Object> map) {
            Tuple<String, Object> tuple = null;
            Number width = null;
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                if (entry.getValue() instanceof Boolean) {
                    tuple = Tuple.of(entry.getKey(), entry.getValue());
                }
                if (entry.getValue() instanceof Number) {
                    width = (Number) entry.getValue();
                }
            }
            return of(tuple, width);
        }

        public DocumentBaseEntity fieldConfigToMap() {
            Map<String, Object> fieldConfig = Maps.newHashMap();
            fieldConfig.put(getTuple().getKey(), getTuple().getValue());
            fieldConfig.put("width", getWidth());
            return new DocumentBaseEntity(fieldConfig);
        }
    }
}
