
package com.facishare.paas.appframework.fcp;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.fcp.model.BaseFcpServiceResult;
import com.facishare.fcp.model.FcpServiceResult;
import com.facishare.paas.appframework.core.model.ContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.GlobalDataService;
import com.facishare.paas.appframework.core.predef.service.dto.globaldata.GetCountryAreaFieldOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by yusb on 2017/11/4
 */
@Component
@FcpService("global")
public class GlobalDataController {

    @Autowired
    private GlobalDataService globalDataService;

    @FcpMethod("country_area_field_options")
    public FcpServiceResult getCountryAreaFieldOptions(GetCountryAreaFieldOptions.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("global_data", "country_area_field_options");
        String countryAreaFieldOptions = globalDataService.getCountryAreaFieldOptions(arg, context);
        BaseFcpServiceResult baseFcpServiceResult = new BaseFcpServiceResult();
        baseFcpServiceResult.setResult(countryAreaFieldOptions);
        return baseFcpServiceResult;
    }

    @FcpMethod("country_area_field_options_include_deleted")
    public FcpServiceResult getCountryAreaFieldOptionsIncludedDeleted(GetCountryAreaFieldOptions.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("global_data", "country_area_field_options_include_deleted");
        String countryAreaFieldOptions = globalDataService.getCountryAreaFieldOptionsIncludedDeleted(arg, context);
        BaseFcpServiceResult baseFcpServiceResult = new BaseFcpServiceResult();
        baseFcpServiceResult.setResult(countryAreaFieldOptions);
        return baseFcpServiceResult;
    }

}