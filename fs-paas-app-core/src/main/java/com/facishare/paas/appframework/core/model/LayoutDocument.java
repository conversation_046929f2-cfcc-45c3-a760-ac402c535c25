package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2017/10/11
 */
public class LayoutDocument extends DocumentBaseEntity {

    public static final String LAYOUT_RULE = "layout_rule";

    public LayoutDocument() {
    }

    public LayoutDocument(Map<String, Object> data) {
        super(data);
    }

    public ILayout toLayout() {
        return new Layout(data);
    }

    public static LayoutDocument of(Map<String, Object> data) {
        if (data == null) {
            return null;
        }
        return new LayoutDocument(data);
    }

    public static LayoutDocument of(ILayout layout) {
        if (layout == null) {
            return null;
        }
        return new LayoutDocument(LayoutExt.of(layout).toMap());
    }

    public static LayoutDocument of(LayoutExt layout) {
        if (layout == null) {
            return null;
        }
        return new LayoutDocument(layout.toMap());
    }

    public static List<LayoutDocument> ofList(List<ILayout> layouts) {
        if (CollectionUtils.isEmpty(layouts)) {
            return Lists.newArrayList();
        }
        return layouts.stream().map(layout -> LayoutDocument.of(layout)).collect(Collectors.toList());
    }

    public LayoutDocument configLayoutRules(List<LayoutRuleDocument> rules) {
        data.put(LAYOUT_RULE, rules);
        return this;
    }

    public List<LayoutRuleDocument> getLayoutRule() {
        return (List<LayoutRuleDocument>) data.get(LAYOUT_RULE);
    }
}
