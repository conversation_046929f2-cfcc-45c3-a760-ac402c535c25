package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.AuthContext;
import com.facishare.paas.appframework.common.service.dto.QueryRoleInfoByCodes;
import com.facishare.paas.appframework.common.service.dto.QueryUserRoleInfo;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("userRoleInformationService")
public class UserRoleInformationServiceImpl implements UserRoleInformationService {
    @Autowired
    private UserRoleInformationProxy userRoleInformationProxy;

    @Override
    public List<QueryUserRoleInfo.RoleInfo> queryRoleInfoByRoleCode(User user, List<String> roleIds) {
        if (CollectionUtils.empty(roleIds)) {
            return Lists.newArrayList();
        }
        QueryRoleInfoByCodes.Arg arg = QueryRoleInfoByCodes.Arg.builder()
                .authContext(AuthContext.buildByUser(user))
                .roleCodes(roleIds)
                .build();
        QueryRoleInfoByCodes.Result result = userRoleInformationProxy.queryRoleInfoWithCodes(arg, RestUtils.buildHeaders(user));
        if (!result.isSuccess()) {
            throw new PermissionError(result.getErrMessage());
        }
        return CollectionUtils.nullToEmpty(result.getRoles());
    }

}
