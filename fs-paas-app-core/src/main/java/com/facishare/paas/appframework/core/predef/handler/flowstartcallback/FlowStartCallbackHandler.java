package com.facishare.paas.appframework.core.predef.handler.flowstartcallback;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.paas.appframework.core.predef.action.StandardFlowStartCallbackAction;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by zhouwr on 2023/10/10.
 */
public interface FlowStartCallbackHandler extends Handler<FlowStartCallbackHandler.Arg, FlowStartCallbackHandler.Result> {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @ToString(callSuper = true, exclude = {"describeMap", "triggerActionHandlerDescribes"})
    @EqualsAndHashCode(callSuper = true)
    class Arg extends Handler.Arg<StandardFlowStartCallbackAction.Arg> {
        //对象描述集合
        @JsonIgnore
        private Map<String, IObjectDescribe> describeMap;
        //加工过的数据
        private ObjectDataDocument data;
        //从数据库查到的数据
        private ObjectDataDocument dbData;
        //加工过的从对象数据
        private Map<String, List<ObjectDataDocument>> detailDataMap;
        //审批流回调数据
        private Map<String, Object> callbackData;
        //是否跳过工作流
        private boolean skipWorkFlow;
        //触发审批的Action绑定的Handler列表
        @JsonIgnore
        private List<SimpleHandlerDescribe> triggerActionHandlerDescribes;
        //主对象需要更新的字段
        private Map<String, Object> updateFieldMap;

        public IObjectData data() {
            if (Objects.isNull(data)) {
                return null;
            }
            return data.toObjectData();
        }

        public IObjectData dbData() {
            if (Objects.isNull(dbData)) {
                return null;
            }
            return dbData.toObjectData();
        }

        public Map<String, List<IObjectData>> detailDataMap() {
            return ObjectDataDocument.ofDataMap(detailDataMap);
        }

        public Map<String, IObjectDescribe> detailDescribeMap() {
            Map<String, IObjectDescribe> detailDescribeMap = new HashMap<>();
            describeMap.forEach((apiName, describe) -> {
                if (apiName.equals(getObjectApiName())) {
                    return;
                }
                if (!ObjectDescribeExt.of(describe).isSlaveObject()) {
                    return;
                }
                detailDescribeMap.put(apiName, describe);
            });
            return detailDescribeMap;
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Result extends Handler.Result<StandardFlowStartCallbackAction.Result> {
        //加工过的数据
        private ObjectDataDocument data;
        //加工过的从对象数据
        private Map<String, List<ObjectDataDocument>> detailDataMap;
    }
}
