//package com.facishare.paas.appframework.core.predef.service
//
//import com.facishare.paas.appframework.core.model.ContextManager
//import com.facishare.paas.appframework.core.model.RequestContext
//import com.facishare.paas.appframework.core.model.RequestContextManager
//import com.facishare.paas.appframework.core.model.User
//import com.facishare.paas.appframework.core.predef.service.dto.gdpr.*
//import com.facishare.paas.appframework.metadata.gdpr.GdprService
//import com.facishare.paas.appframework.metadata.gdpr.GdprServiceImpl
//import com.facishare.paas.appframework.metadata.repository.model.GdprLegalBase
//import com.facishare.paas.appframework.metadata.repository.model.GdprProjectRequest
//import com.google.common.collect.Lists
//import spock.lang.Specification
//
//class ObjectGdprServiceTest extends Specification {
//
//    static {
//        System.setProperty("spring.profiles.active", "fstest")
//    }
//
//    GdprService gdprService = new GdprServiceImpl()
//    ObjectGdprService objectGdprService = new ObjectGdprService(gdprService: gdprService)
//
//
//    def tenant_id = '78057'
//    def context
//
//    def setup() {
//        RequestContext requestContext = RequestContext.builder().tenantId(tenant_id).user(Optional.of(User.systemUser(tenant_id))).build()
//        RequestContextManager.setContext(requestContext)
//        context = ContextManager.buildServiceContext("gdpr", "")
//    }
//
//    def "test create_legal_base_link"() {
//        given:
//        def context = ContextManager.buildServiceContext("gdpr", "")
//        def arg = CreateLegalBaseLink.Arg.newInstance()
//        arg.setApiName("object_0q605__c")
//        arg.setDataId("6316ee636d51690001f046fb")
//        when:
//        def result = objectGdprService.createLegalBaseLink(arg, context)
//
//        then:
//        println("openCompliance" + result)
//    }
//
//
//    def "test openCompliance"() {
//        given:
//        def context = ContextManager.buildServiceContext("gdpr", "")
//        def arg = OpenGdprCompliance.Arg.newInstance()
//        arg.setApiNames(Lists.newArrayList("object_Uh1x4__c"))
//        arg.setUnusableOperation(Lists.newArrayList("Edit", "OpenApi", "Export"))
//        arg.setForbidExport("all")
//        arg.setForbidOpenApi("sensitive")
//
//        when:
//        def result = objectGdprService.openGdprCompliance(arg, context)
//
//        then:
//        println("openCompliance" + result)
//    }
//
//    def "test closeCompliance"() {
//        given:
//
//        def arg = CloseGdprCompliance.Arg.newInstance()
//        when:
//        def result = objectGdprService.closeGdprCompliance(arg, context)
//        then:
//        println("closeCompliance" + result)
//    }
//
//
//    def "test findGdprComplianceStatus"() {
//        given:
//        def arg = FindGdprComplianceStatus.Arg.newInstance()
//        arg.setApiName("")
//        when:
//        def reslut = objectGdprService.findGdprComplianceStatus(arg, context)
//        then:
//        println("findGdprComplianceStatus" + reslut)
//    }
//
//    def "test findGdprComplianceData"() {
//        given:
//        def arg = FindGdprComplianceData.Arg.newInstance()
//        when:
//        def result = objectGdprService.findGdprComplianceData(arg, context)
//        then:
//        println("findGdprComplianceData" + result)
//    }
//
//    def "test updateGdprPersonalField"() {
//        given:
//        def arg = UpdateGdprPersonalField.Arg.newInstance()
//        arg.setApiName("object_Uh1x4__c")
//        arg.setOrdinaryFields(Lists.newArrayList("name"))
//        arg.setSensitiveFields(Lists.newArrayList("record_type"))
//        when:
//        def result = objectGdprService.updateGdprPersonalField(arg, context)
//        then:
//        println("updateGdprPersonalField" + result)
//    }
//
//    def "test findGdprPersonalField"() {
//        given:
//        def arg = FindGdprPersonalField.Arg.newInstance()
//        arg.setApiName("object_Uh1x4__c")
//        when:
//        def result = objectGdprService.findGdprPersonalField(arg, context)
//        then:
//        println("findGdprPersonalField" + result)
//    }
//
//    def "test findGdprLegalBaseView"() {
//        given:
//        def arg = FindGdprLegalBaseView.Arg.newInstance()
//        arg.setApiName("object_0q605__c")
//        when:
//        def result = objectGdprService.findGdprLegalBaseView(arg, context)
//        then:
//        println("findGdprLegalBaseView" + result)
//
//    }
//
//    def "test queryGdprLegalBaseData"() {
//        given:
//        def arg = QueryGdprLegalBaseData.Arg.newInstance()
//        arg.setApiName("object_0q605__c")
//        arg.setLegalBase(GdprLegalBase.AGREE)
//        arg.setPageNumber(1)
//        arg.setPageSize(10)
//        when:
//        def result = objectGdprService.queryGdprLegalBaseData(arg, context)
//        then:
//        println("queryGdprLegalBaseData:" + result)
//    }
//
//    def "test openGdprLegalBase"() {
//        given:
//        def arg = OpenGdprLegalBase.Arg.newInstance()
//        arg.setApiName("object_0q605__c")
//        arg.setDataId("60adff3c20df720001db4253")
//
//        def findArg = FindGdprLegalBaseDetail.Arg.newInstance()
//        findArg.setApiName("object_0q605__c")
//        findArg.setDataId("60adff3c20df720001db4253")
//        when:
//        objectGdprService.openGdprLegalBase(arg, context)
//        then:
//        println("openGdprLegalBase:" + objectGdprService.findGdprLegalBaseDetail(findArg, context))
//    }
//
//
//    def "test closeGdprLegalBase"() {
//        given:
//        def arg = CloseGdprLegalBase.Arg.newInstance()
//        arg.setApiName("object_Uh1x4__c")
//        arg.setDataId("60c808e3ad8185000182c8ea")
//
//        def findArg = FindGdprLegalBaseDetail.Arg.newInstance()
//        findArg.setApiName("object_Uh1x4__c")
//        findArg.setDataId("60c808e3ad8185000182c8ea")
//        when:
//        objectGdprService.closeGdprLegalBase(arg, context)
//        then:
//        println("closeGdprLegalBase:" + objectGdprService.findGdprLegalBaseDetail(findArg, context))
//    }
//
//    def "test updateGdprLegalBase"() {
//        given:
//        def arg = UpdateGdprLegalBase.Arg.newInstance()
//        arg.setApiName("object_0q605__c")
//        arg.setDataId("60d30c9cb4b70a00015a0534")
//        arg.setLegalBase("agree")
//
//        def findArg = FindGdprLegalBaseDetail.Arg.newInstance()
//        findArg.setApiName("object_0q605__c")
//        findArg.setDataId("60d30c9cb4b70a00015a0534")
//        when:
//        objectGdprService.updateGdprLegalBase(arg, context)
//        then:
//        println("closeGdprLegalBase:" + objectGdprService.findGdprLegalBaseDetail(findArg, context))
//    }
//
//    def "test findGdprLegalBaseDetail"() {
//        given:
//        def arg = FindGdprLegalBaseDetail.Arg.newInstance()
//        arg.setApiName("object_Uh1x4__c")
//        arg.setDataId("60c808e3ad8185000182c8ea")
//        when:
//        def result = objectGdprService.findGdprLegalBaseDetail(arg, context)
//        then:
//        println("findGdprLegalBaseDetail:" + result)
//    }
//
//    def "test addGdprLegalBaseDetail"() {
//        given:
//        def arg = AddGdprLegalBaseDetail.Arg.newInstance()
//        arg.setLegalBase(GdprLegalBase.AGREE)
//        arg.setApiName("object_0q605__c")
//        arg.setDataId("60d30c9cb4b70a00015a0534")
//        arg.setAgreeDate(System.currentTimeMillis())
//        arg.setRemark("ceshi")
//        arg.setContactWay("phone")
//        when:
//        def result = objectGdprService.addGdprLegalBaseDetail(arg, context)
//        then:
//        println("addGdprLegalBaseDetail" + result)
//    }
//
//    def "test updateGdprLegalBaseDetail"() {
//        given:
//        def arg = UpdateGdprLegalBaseDetail.Arg.newInstance()
//        arg.setLegalBase(GdprLegalBase.AGREE)
//        arg.setApiName("object_0q605__c")
//        arg.setDataId("60d30c9cb4b70a00015a0534")
//        arg.setAgreeDate(System.currentTimeMillis())
//        arg.setRemark("sdasdasdasd")
//        arg.setContactWay("phone")
//        arg.setId("60ecf3a2d46786000108f118")
//        when:
//        def result = objectGdprService.updateGdprLegalBaseDetail(arg, context)
//        then:
//        println("updateGdprLegalBaseDetail" + result)
//    }
//
//    def "test deleteGdprLegalBaseDetail"() {
//        given:
//        def arg = DeleteGdprLegalBaseDetail.Arg.newInstance()
//        arg.setApiName("object_Uh1x4__c")
//        arg.setDataId("60c808e3ad8185000182c8ea")
//        arg.setId("60cb01aa0f7cab0001f4947a")
//        when:
//        def result = objectGdprService.deleteGdprLegalBaseDetail(arg, context)
//        then:
//        println("deleteGdprLegalBaseDetail" + result)
//    }
//
//
//    def "test findGdprProjectRequestData"() {
//        given:
//        def arg = FindGdprProjectRequestData.Arg.newInstance()
//        arg.setApiName("object_Uh1x4__c")
//        arg.setDataId("60c808e3ad8185000182c8ea")
//        when:
//        def result = objectGdprService.findGdprProjectRequestData(arg, context)
//        then:
//        println("findGdprLegalBaseDetail:" + result)
//    }
//
//    def "test batchUpdateGdprLegalBase"() {
//        given:
//        def arg = BatchUpdateGdprLegalBase.Arg.newInstance()
//        arg.setApiName("object_Uh1x4__c")
//        //arg.setLegalBase(GdprLegalBase.LEGAL_BASIS)
//        arg.setDataIds(Lists.newArrayList("60c33525ad818500018232ee", "60c808e3ad8185000182c8ea"))
//        when:
//        def result = objectGdprService.batchUpdateGdprLegalBase(arg, context)
//        then:
//        println("batchUpdateGdprLegalBase" + result)
//    }
//
//
//    def "test findGdprProjectRequestView"() {
//        given:
//        def arg = FindGdprProjectRequestView.Arg.newInstance()
//        arg.setApiName("object_0q605__c")
//        when:
//        def result = objectGdprService.findGdprProjectRequestView(arg, context)
//        then:
//        println("addGdprProjectRequestData" + result)
//    }
//
//
//    def "test addGdprProjectRequestData"() {
//        given:
//        def arg = AddGdprProjectRequestData.Arg.newInstance()
//        arg.setApiName("object_Uh1x4__c")
//        arg.setDataId("60c33525ad818500018232ee")
//        arg.setType(GdprProjectRequest.GDPR_PROJECT_REQUEST_TYPE_QUERY)
//        when:
//        def result = objectGdprService.addGdprProjectRequestData(arg, context)
//        then:
//        println("addGdprProjectRequestData" + result)
//    }
//
//
//    def "test updateGdprProjectRequestData"() {
//        given:
//        def arg = UpdateGdprProjectRequestData.Arg.newInstance()
//        arg.setId("60c807d9ce9a3b0ef0415268")
//        when:
//        def result = objectGdprService.updateGdprProjectRequestData(arg, context)
//        then:
//        println("updateGdprProjectRequestData" + result)
//    }
//
//    def "test deleteGdprProjectRequestData"() {
//        given:
//        def arg = DeleteGdprProjectRequestData.Arg.newInstance()
//        arg.setId("60c807d9ce9a3b0ef0415268")
//        when:
//        def result = objectGdprService.deleteGdprProjectRequestData(arg, context)
//        then:
//        println("deleteGdprProjectRequestData" + result)
//    }
//}
