/**
 * Facishare PAAS Auth
 * This is PAAS auth service project
 *
 * OpenAPI spec version: v1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


package com.facishare.crm.privilege.swagger.model;

import java.util.Objects;
import com.facishare.crm.privilege.swagger.model.AuthContext;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * CreateRoleArg
 */
@javax.annotation.Generated(value = "class io.swagger.codegen.languages.JavaClientCodegen", date = "2017-05-16T15:01:04.279+08:00")
public class CreateRoleArg   {
  @SerializedName("authContext")
  private AuthContext authContext = null;

  @SerializedName("roleCode")
  private String roleCode = null;

  @SerializedName("roleName")
  private String roleName = null;

  @SerializedName("description")
  private String description = null;

  @SerializedName("roleType")
  private Integer roleType = null;

  public CreateRoleArg authContext(AuthContext authContext) {
    this.authContext = authContext;
    return this;
  }

   /**
   * Get authContext
   * @return authContext
  **/
  @ApiModelProperty(example = "null", value = "")
  public AuthContext getAuthContext() {
    return authContext;
  }

  public void setAuthContext(AuthContext authContext) {
    this.authContext = authContext;
  }

  public CreateRoleArg roleCode(String roleCode) {
    this.roleCode = roleCode;
    return this;
  }

   /**
   * Get roleCode
   * @return roleCode
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getRoleCode() {
    return roleCode;
  }

  public void setRoleCode(String roleCode) {
    this.roleCode = roleCode;
  }

  public CreateRoleArg roleName(String roleName) {
    this.roleName = roleName;
    return this;
  }

   /**
   * Get roleName
   * @return roleName
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getRoleName() {
    return roleName;
  }

  public void setRoleName(String roleName) {
    this.roleName = roleName;
  }

  public CreateRoleArg description(String description) {
    this.description = description;
    return this;
  }

   /**
   * Get description
   * @return description
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public CreateRoleArg roleType(Integer roleType) {
    this.roleType = roleType;
    return this;
  }

   /**
   * Get roleType
   * @return roleType
  **/
  @ApiModelProperty(example = "null", value = "")
  public Integer getRoleType() {
    return roleType;
  }

  public void setRoleType(Integer roleType) {
    this.roleType = roleType;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CreateRoleArg createRoleArg = (CreateRoleArg) o;
    return Objects.equals(this.authContext, createRoleArg.authContext) &&
        Objects.equals(this.roleCode, createRoleArg.roleCode) &&
        Objects.equals(this.roleName, createRoleArg.roleName) &&
        Objects.equals(this.description, createRoleArg.description) &&
        Objects.equals(this.roleType, createRoleArg.roleType);
  }

  @Override
  public int hashCode() {
    return Objects.hash(authContext, roleCode, roleName, description, roleType);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CreateRoleArg {\n");
    
    sb.append("    authContext: ").append(toIndentedString(authContext)).append("\n");
    sb.append("    roleCode: ").append(toIndentedString(roleCode)).append("\n");
    sb.append("    roleName: ").append(toIndentedString(roleName)).append("\n");
    sb.append("    description: ").append(toIndentedString(description)).append("\n");
    sb.append("    roleType: ").append(toIndentedString(roleType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

